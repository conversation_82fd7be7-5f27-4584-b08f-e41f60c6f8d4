// File: Api/V2/Controllers/SparePartTransactionController.cs
// Description: 备品备件出入库记录管理API控制器

using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using ItAssetsSystem.Application.Features.SpareParts.Services;
using ItAssetsSystem.Application.Features.SpareParts.Dtos;
using ItAssetsSystem.Application.Common.Dtos;
using Microsoft.AspNetCore.Authorization;
using System;

namespace ItAssetsSystem.Api.V2.Controllers
{
    /// <summary>
    /// 备品备件出入库记录管理API控制器
    /// </summary>
    [ApiController]
    [Route("api/v2/spare-part-transactions")]
    [Authorize]
    public class SparePartTransactionController : ControllerBase
    {
        private readonly ISparePartTransactionService _transactionService;
        
        /// <summary>
        /// 构造函数
        /// </summary>
        public SparePartTransactionController(ISparePartTransactionService transactionService)
        {
            _transactionService = transactionService;
        }
        
        /// <summary>
        /// 获取出入库记录列表
        /// </summary>
        /// <param name="query">查询参数</param>
        /// <returns>记录列表</returns>
        [HttpGet]
        public async Task<IActionResult> GetTransactions([FromQuery] SparePartTransactionQuery query)
        {
            var result = await _transactionService.GetTransactionsAsync(query);
            return Ok(new ApiResponse<PaginatedResult<SparePartTransactionDto>>
            {
                Success = true,
                Data = result
            });
        }
        
        /// <summary>
        /// 获取特定备件的出入库记录
        /// </summary>
        /// <param name="partId">备件ID</param>
        /// <param name="query">分页参数</param>
        /// <returns>记录列表</returns>
        [HttpGet("part/{partId}")]
        public async Task<IActionResult> GetTransactionsByPartId(long partId, [FromQuery] PaginationQuery query)
        {
            var result = await _transactionService.GetTransactionsByPartIdAsync(partId, query);
            return Ok(new ApiResponse<PaginatedResult<SparePartTransactionDto>>
            {
                Success = true,
                Data = result
            });
        }
        
        /// <summary>
        /// 获取出入库记录详情
        /// </summary>
        /// <param name="id">记录ID</param>
        /// <returns>记录详情</returns>
        [HttpGet("{id}")]
        public async Task<IActionResult> GetTransactionById(long id)
        {
            var result = await _transactionService.GetTransactionByIdAsync(id);
            if (result == null)
            {
                return NotFound(new ApiResponse<object>
                {
                    Success = false,
                    Message = $"出入库记录ID {id} 不存在"
                });
            }
            
            return Ok(new ApiResponse<SparePartTransactionDto>
            {
                Success = true,
                Data = result
            });
        }
    }
} 