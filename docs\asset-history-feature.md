# 资产历史记录功能文档

## 概述

资产历史记录功能允许用户跟踪和查看系统中所有资产发生的变更历史，包括属性更新、位置变更、状态变更等。这些历史记录对于资产管理非常重要，能够提供完整的资产生命周期追踪。

## 数据结构

### 资产历史表(assethistories)

资产历史记录表主要存储所有资产的变更历史记录：

```sql
CREATE TABLE `assethistories` (
  `Id` int NOT NULL AUTO_INCREMENT,
  `AssetId` int NOT NULL COMMENT '资产ID',
  `OperationType` int NOT NULL COMMENT '操作类型：1创建，2修改，3删除，4位置变更，5状态变更',
  `OperatorId` int NOT NULL COMMENT '操作人ID',
  `OperationTime` datetime NOT NULL COMMENT '操作时间',
  `Description` text COMMENT '描述（JSON格式，记录变更前后的属性值）',
  `CreatedAt` datetime NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`),
  KEY `IX_AssetHistories_AssetId` (`AssetId`),
  KEY `IX_AssetHistories_OperatorId` (`OperatorId`),
  CONSTRAINT `FK_AssetHistories_Assets_AssetId` FOREIGN KEY (`AssetId`) REFERENCES `assets` (`Id`),
  CONSTRAINT `FK_AssetHistories_Users_OperatorId` FOREIGN KEY (`OperatorId`) REFERENCES `users` (`Id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='资产历史表'
```

### 位置历史表(locationhistories)

位置历史表已存在，专门用于记录资产位置变更的历史记录。

## 对象模型

1. **AssetHistory** - 资产历史记录实体
   - Id: 主键
   - AssetId: 关联的资产ID
   - OperationType: 操作类型 (1:创建, 2:修改, 3:删除, 4:位置变更, 5:状态变更)
   - OperatorId: 操作人ID
   - OperationTime: 操作时间
   - Description: 变更描述，JSON格式存储变更详情
   - CreatedAt/UpdatedAt: 审计字段

## API接口

### 获取资产历史记录

```
GET /api/Asset/{id}/history
```

请求参数：
- id: 资产ID（路径参数）
- type: 历史记录类型（查询参数，0:全部，1:位置变更，2:状态变更）
- startTime: 开始时间（查询参数，可选）
- endTime: 结束时间（查询参数，可选）

响应格式：
```json
{
  "success": true,
  "data": {
    "asset": {
      "id": 1,
      "assetCode": "IPC2023001",
      "name": "工控机"
    },
    "history": [
      {
        "id": 5,
        "type": "update",
        "operationType": 2,
        "operationTypeName": "修改",
        "operatorId": 1,
        "operatorName": "管理员",
        "operationTime": "2025-03-26T10:30:00",
        "description": "品牌: 联想 → 华硕",
        "changes": [
          {
            "field": "品牌",
            "oldValue": "联想",
            "newValue": "华硕"
          }
        ],
        "icon": "edit",
        "color": "#faad14"
      },
      {
        "id": 2,
        "type": "location",
        "changeType": 0,
        "changeTypeName": "转移",
        "oldLocationId": 1,
        "oldLocationName": "一号车间",
        "newLocationId": 3,
        "newLocationName": "二号车间",
        "operatorId": 1,
        "operatorName": "管理员",
        "reason": "",
        "notes": "设备转移",
        "changeTime": "2025-03-25T15:20:00",
        "icon": "map-pin",
        "color": "#1890ff"
      }
    ]
  }
}
```

## 视觉设计

### 时间线设计

资产历史记录使用时间线的方式展示：
- 每个历史条目显示在时间线上，按时间倒序排列
- 不同类型的历史记录使用不同的图标和颜色
- 位置变更记录使用流程图样式
- 属性变更记录使用表格样式展示具体变更内容

### 颜色编码

- 创建操作: 绿色 (#52c41a)
- 修改操作: 黄色 (#faad14)
- 删除操作: 红色 (#f5222d)
- 位置变更: 蓝色 (#1890ff)
- 状态变更: 紫色 (#722ed1)

### 互动效果

- 鼠标悬停时卡片有阴影变化效果
- 时间线节点有轻微的放大效果
- 可展开/折叠详细信息
- 变更记录支持分页

## 自动记录机制

当资产发生以下操作时，将自动创建历史记录：

1. 创建资产
2. 修改资产属性
3. 删除资产
4. 资产位置变更
5. 资产状态变更

## 存储格式

对于属性变更，在Description字段中存储JSON格式的变更信息，格式如下：

```json
[
  {
    "field": "品牌",
    "oldValue": "联想",
    "newValue": "华硕"
  },
  {
    "field": "型号",
    "oldValue": "ThinkCentre",
    "newValue": "ROG"
  }
]
```

## 技术实现

1. 前端使用Element Plus的Timeline组件实现时间线展示
2. 后端使用原生SQL查询避免导航属性问题
3. 使用图标和颜色区分不同类型的操作
4. 针对不同字段类型使用适当的格式化和样式

## 未来优化方向

1. 支持更多的历史记录类型
2. 添加高级筛选功能
3. 导出历史记录报表
4. 增加更多可视化图表展示资产变更模式
5. 实现批量资产历史记录比较 