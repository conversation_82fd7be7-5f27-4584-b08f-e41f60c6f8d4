<!--
  本文件已废弃（deprecated），请使用 ModernKanbanView.vue 或 EnhancedTaskListView.vue 替代。
  如需恢复请联系前端负责人。
-->
<template>
  <div class="kanban-view page-container">
    <h2 class="page-title">任务看板</h2>

    <!-- 过滤器区域 -->
    <el-card shadow="never" class="filter-card mb-4">
       <div class="filters">
         <el-input v-model="searchQuery" placeholder="搜索任务标题..." clearable style="width: 200px;" @input="debouncedFetchData">
           <template #prepend><el-icon><Search /></el-icon></template>
         </el-input>
         <!-- More filters can be added here based on the example -->
         <!-- <el-select v-model="filters.status" placeholder="按状态筛选"...> -->
         <!-- <el-select v-model="filters.assigneeId" placeholder="按负责人筛选"...> -->
         <el-button @click="fetchKanbanData" :icon="Refresh" :loading="loading">刷新</el-button>
       </div>
    </el-card>

    <!-- 看板区域 -->
    <div v-if="loading" class="loading-skeleton">
      <el-skeleton :rows="10" animated />
    </div>

    <div class="kanban-board" v-show="!loading">
      <div
        v-for="column in taskColumns"
        :key="column.id"
        class="task-column"
        :class="{ 'drag-over': dragOverStatus === column.id }"
        @dragover.prevent="onDragOver($event, column.id)"
        @dragleave.prevent="onDragLeave"
        @drop="onDrop($event, column.id)"
      >
        <div class="column-header">
          <h4>{{ column.name }}</h4>
          <span class="status-count">{{ column.tasks.length }}</span>
        </div>
        <draggable
          :list="column.tasks"
          item-key="id"
          group="tasks"
          class="task-list"
          :animation="200"
          ghost-class="ghost-card"
          chosen-class="chosen-card"
          drag-class="drag-card"
          @start="onDragStart"
          @end="onDragEnd"
        >
          <template #item="{ element: task }">
            <div
              class="task-card"
              :draggable="true"
              @dragstart="setDraggedTask($event, task)"
              @click="viewTaskDetail(task.id || task.taskId)"
              :data-id="task.id || task.taskId"
              :class="[
                `priority-${(task.priority || 'medium').toLowerCase()}`,
                { 'is-dragging': draggedTask && (draggedTask.id || draggedTask.taskId) === (task.id || task.taskId) },
                { 'overdue-task': task.isOverdue && task.kanbanStatus !== 'completed' },
                { 'paused-task': task.isPaused && task.kanbanStatus !== 'completed' },
                { 'review-task': task.isAwaitingReview && task.kanbanStatus !== 'completed' }
              ]"
            >
              <div class="task-title">{{ task.name }}</div>
              <div class="task-meta">
                <span class="assignee" v-if="task.assigneeUserId">
                  <img v-if="getUserAvatar(task.assigneeUserId)" :src="getUserAvatar(task.assigneeUserId)" alt="Avatar" class="small-avatar">
                  <span v-else-if="getUserName(task.assigneeUserId) !== '未分配' && getUserName(task.assigneeUserId) !== '未知用户'" class="small-avatar">{{ getUserName(task.assigneeUserId).substring(0, 1) }}</span>
                  <span>{{ getUserName(task.assigneeUserId) }}</span>
                </span>
                <span v-else class="assignee">
                   <span class="small-avatar">?</span>
                  <span>未分配</span>
                </span>
                <span
                  v-if="task.planEndDate"
                  class="due-date"
                  :class="{ 'overdue': task.isOverdue && task.kanbanStatus !== 'completed' }"
                >
                  <el-icon><Calendar /></el-icon>
                  <span>{{ formatDate(task.planEndDate, false) }}</span>
                </span>
              </div>
              <div class="task-meta" v-if="task.priority">
                <span :class="['priority-tag', `priority-${(task.priority || 'medium').toLowerCase()}`]">
                  {{ task.priority }}
                </span>
                 <span v-if="task.isPaused && task.kanbanStatus !== 'completed'" class="status-indicator-tag paused-tag">
                  <el-icon><VideoPause /></el-icon> 已暂停
                </span>
                <span v-if="task.isAwaitingReview && task.kanbanStatus !== 'completed'" class="status-indicator-tag review-tag">
                  <el-icon><View /></el-icon> 评审中
                </span>
              </div>
            </div>
          </template>
        </draggable>
        <div v-if="!column.tasks.length && !loading" class="empty-column-placeholder">
          <el-icon><InfoFilled /></el-icon>
          <span>暂无任务</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { Clock, Search, Refresh, InfoFilled, Calendar, VideoPause, View } from '@element-plus/icons-vue'
import draggable from 'vuedraggable'
import { taskApi } from '../../api/task.js'
import userApi from '../../api/user.js' // <--- 引入用户API
import { ElMessage, ElSkeleton } from 'element-plus'
import { useUserStore } from '@/stores/modules/user' // 引入用户 Store

const router = useRouter()
const userStore = useUserStore() // 使用 Store
const loading = ref(false)
const searchQuery = ref('')
const selectedAssigneeId = ref(null)
const selectedPriority = ref(null)
const dateRange = ref([]) // For filtering by date range
const teamMembersList = ref([]) // To store fetched team members
const draggedTask = ref(null) // Store the task being dragged
const dragOverStatus = ref(null) // For visual feedback on column hover

// Define Kanban columns - simplified, overdue is now a flag
// OnHold and PendingReview will be shown in 'in-progress' with visual cues
const definedStatuses = ref([
  { id: 'todo', name: '待处理', tasks: [] },
  { id: 'in-progress', name: '进行中', tasks: [] },
  { id: 'completed', name: '已完成', tasks: [] }
])

const taskColumns = computed(() => {
  return definedStatuses.value.map(status => ({
    ...status,
    tasks: tasks.value.filter(task => task.kanbanStatus === status.id)
  }))
})

const tasks = ref([]) // Flat list of all tasks for easier management

const kanbanStatusMap = {
  todo: '待处理',
  'in-progress': '进行中',
  completed: '已完成',
  // No 'overdue' here as a column status
}

// Helper to get user name by ID
function getUserName(userId) {
  if (!userId || !teamMembersList.value || teamMembersList.value.length === 0) return '未分配';
  const user = teamMembersList.value.find(member => member.id === userId || member.userId === userId);
  return user ? user.name || user.userName || user.nickName : '未知用户';
}

// Helper to get user avatar by ID (simplified)
function getUserAvatar(userId) {
  if (!userId || !teamMembersList.value || teamMembersList.value.length === 0) return null; // Or a default avatar path
  const user = teamMembersList.value.find(member => member.id === userId || member.userId === userId);
  return user ? user.avatar : null; // Or a default avatar
}

// Debounced fetch function
const debouncedFetchKanbanData = debounce(fetchKanbanData, 300)

watch([searchQuery, selectedAssigneeId, selectedPriority, dateRange], debouncedFetchKanbanData, { deep: true })

async function fetchKanbanData() {
  loading.value = true
  try {
    const params = {
      page: 1, // Reset to page 1 for new fetches/filters
      pageSize: 1000, // Fetch all tasks for client-side bucketing
      keyword: searchQuery.value || undefined,
      assigneeId: selectedAssigneeId.value || undefined,
      priority: selectedPriority.value || undefined,
      startDate: dateRange.value && dateRange.value[0] ? formatDate(dateRange.value[0], false) : undefined,
      endDate: dateRange.value && dateRange.value[1] ? formatDate(dateRange.value[1], false) : undefined,
      // Sort by creation date montagnes or a meaningful default
      orderBy: 'creationTimestamp',
      isAsc: false
    }

    // Fetch tasks using V2 API
    const response = await taskApi.getTaskListV2(params)
    let rawTasks = []
    if (response && response.data && Array.isArray(response.data.list)) {
        rawTasks = response.data.list
    } else if (response && Array.isArray(response.list)) { // Handle direct array if API structure varies
      rawTasks = response.list
    } else if (Array.isArray(response)) {
        rawTasks = response
    } else {
        console.warn('Unexpected response structure for tasks:', response)
    }

    const processedTasks = []
    rawTasks.forEach(rawTask => {
      // Backend status strings: "NotStarted", "InProgress", "OnHold", "PendingReview", "Completed", "Cancelled", "Overdue"
      // Frontend Kanban statuses: "todo", "in-progress", "completed"

      if (rawTask.status && rawTask.status.toLowerCase() === 'cancelled') {
        return; // Skip cancelled tasks
      }

      const task = { ...rawTask } // Create a mutable copy

      // 1. Determine overdue status (prioritize API if it sends 'Overdue' status or an isOverdue flag)
      task.isOverdue = false
      if (rawTask.status && rawTask.status.toLowerCase() === 'overdue') {
        task.isOverdue = true
        // If API says "Overdue", we need a base status for column placement.
        // This might require an additional field from API like 'originalStatusBeforeOverdue'
        // For now, let's assume it was 'InProgress' or 'NotStarted'
        // A more robust solution would involve the API providing the "logical" status alongside "Overdue"
        // For simplicity, if status is "Overdue", we might place it in "in-progress" or "todo" based on other fields
        // Or, if an 'originalStatus' field exists:
        // task.underlyingApiStatus = rawTask.originalStatus || 'NotStarted'; 
        // For now, we let mapApiToKanbanStatus handle this if API sends "Overdue"
      } else if (rawTask.status && rawTask.status.toLowerCase() !== 'completed') {
        // Calculate overdue if not completed and not already marked overdue by API
        task.isOverdue = checkOverdue(rawTask)
      }

      // 2. Determine other flags (isPaused, isAwaitingReview)
      task.isPaused = rawTask.status && rawTask.status.toLowerCase() === 'onhold'
      task.isAwaitingReview = rawTask.status && rawTask.status.toLowerCase() === 'pendingreview'

      // 3. Get Kanban status for column placement
      task.kanbanStatus = mapApiToKanbanStatus(rawTask.status, task.isOverdue)
      
      // Store original API status for updates, if not already the same as mapped kanban status's API equivalent
      task.originalApiStatus = rawTask.status

      if (task.kanbanStatus) { // Only add if it has a valid kanban column status
        processedTasks.push(task)
      }
    })
    tasks.value = processedTasks

  } catch (error) {
    console.error("Error fetching Kanban data:", error)
    ElMessage.error("获取看板数据失败")
    tasks.value = [] // Clear tasks on error
  } finally {
    loading.value = false
  }
}

// --- Task Card Actions ---
function checkOverdue(task) {
  // API might directly provide an `isOverdue` flag or status="Overdue", prefer that if available (handled in fetchKanbanData)
  // This function serves as a fallback or primary calculator if API doesn't specify overdue status directly
  if (typeof task.isOverdue === 'boolean' && task.status && task.status.toLowerCase() === 'overdue') {
      return task.isOverdue // Already determined by API status
  }
  if (task.status && task.status.toLowerCase() === 'completed') return false
  return task.planEndDate && new Date(task.planEndDate) < new Date()
}

function viewTaskDetail(taskId) {
  if (!taskId) return
  router.push(`/main/tasks/detail/${taskId}`)
}

// --- Drag and Drop Handlers ---
function setDraggedTask(event, task) {
  // Standard HTML dragstart event needed to set data
  event.dataTransfer.setData('text/plain', task.id)
  event.dataTransfer.effectAllowed = 'move'
  draggedTask.value = task // Keep track for styling
}

function onDragStart() {
  // vuedraggable start event, draggedTask should already be set by HTML dragstart
}

function onDragEnd() {
  // Called by vuedraggable when drag ends (dropped or cancelled)
  // Also called by HTML dragend
  if (draggedTask.value) {
      const cardElement = document.querySelector(`.task-card[draggable="true"][data-id="${draggedTask.value.id || draggedTask.value.taskId}"]`) // Ensure we have the right element
      if(cardElement) cardElement.classList.remove('is-dragging')
  }
  draggedTask.value = null
  dragOverStatus.value = null // Reset background highlight
}

function onDragOver(event, targetStatus) {
  event.preventDefault() // Necessary to allow dropping
  // Allow drop if the task exists and the target status is different from current kanban status
  if (draggedTask.value && draggedTask.value.kanbanStatus !== targetStatus) { 
    dragOverStatus.value = targetStatus // Set for styling the column background
  } else {
    dragOverStatus.value = null
  }
}

function onDragEnter(event, targetStatus) {
  // Add visual cue if needed, but dragOverStatus handles background
}

function onDragLeave(event) {
  // Only remove highlight if leaving the column *entirely*
  // This logic can be tricky; often relying on onDragOver is sufficient
  if (event.target.classList.contains('task-column') && !event.currentTarget.contains(event.relatedTarget)) {
     dragOverStatus.value = null
  }
}

async function onDrop(event, newKanbanStatus) {
  dragOverStatus.value = null // Clear visual feedback
  if (!draggedTask.value) return // No task was being dragged

  const taskToMove = { ...draggedTask.value } // Work with a copy
  const originalKanbanStatus = taskToMove.kanbanStatus

  if (newKanbanStatus === originalKanbanStatus) {
    console.log('Dropped in the same column, no status change needed.')
    // draggedTask.value = null; // Clearing this here might be too early if onDragEnd hasn't fired for vuedraggable
    return
  }

  const targetApiStatus = mapKanbanToApiStatus(newKanbanStatus)

  if (!targetApiStatus) {
      ElMessage.warning(`无法将任务移动到 '${kanbanStatusMap[newKanbanStatus] || newKanbanStatus}' 状态，无效的API目标状态。`)
      // No visual rollback needed here as optimistic update hasn't happened yet for this case
      // draggedTask.value = null; 
      return
  }
  
  console.log(`Task ${taskToMove.id || taskToMove.taskId} dropped. Kanban: ${originalKanbanStatus} -> ${newKanbanStatus}. API: ${taskToMove.originalApiStatus} -> ${targetApiStatus}`)

  // --- Optimistic UI Update ---
  // Find the task in the main tasks array and update its kanbanStatus
  const taskIndex = tasks.value.findIndex(t => (t.id || t.taskId) === (taskToMove.id || taskToMove.taskId))
  if (taskIndex !== -1) {
    // Update the status and any related flags for the task in the flat list
    tasks.value[taskIndex].kanbanStatus = newKanbanStatus
    // Recalculate/update flags based on new status if necessary (e.g., moving out of 'Paused')
    tasks.value[taskIndex].isPaused = (targetApiStatus.toLowerCase() === 'onhold')
    tasks.value[taskIndex].isAwaitingReview = (targetApiStatus.toLowerCase() === 'pendingreview')
    // Overdue status should ideally not change just by column drag, but by date or explicit action
    // task.isOverdue is primarily determined at fetch or by specific "mark overdue" actions.
    // If a task is moved to "completed", it's no longer overdue.
    if (newKanbanStatus === 'completed') {
      tasks.value[taskIndex].isOverdue = false
    }
     // taskColumns computed property will automatically reflect this change.
  } else {
      console.error(`Optimistic update failed: Could not find task ${taskToMove.id || taskToMove.taskId} in tasks list.`)
      ElMessage.error('看板更新失败，请刷新重试。')
      // draggedTask.value = null; // Clear on failure before API call is not ideal
      return
  }

  // --- API Call --- 
  try {
    // Call API using the mapped API status
    // Ensure your API call uses the correct task ID property (e.g., taskId or id)
    await taskApi.updateTaskStatus((taskToMove.id || taskToMove.taskId), { status: targetApiStatus })
    ElMessage.success(`任务状态已更新为: ${kanbanStatusMap[newKanbanStatus] || newKanbanStatus}`)
    // After successful API update, refresh data to ensure consistency, or update task.originalApiStatus
    tasks.value[taskIndex].originalApiStatus = targetApiStatus
    // Optionally re-fetch data, though optimistic update should suffice for status
    // fetchKanbanData(); 
  } catch (error) {
    ElMessage.error('更新任务状态失败')
    console.error('Error updating task status:', error)
    // --- Rollback optimistic UI update on failure ---
    if (taskIndex !== -1) {
      tasks.value[taskIndex].kanbanStatus = originalKanbanStatus // Revert to original kanban column
      // Revert any other flags if needed
      tasks.value[taskIndex].isPaused = (tasks.value[taskIndex].originalApiStatus.toLowerCase() === 'onhold')
      tasks.value[taskIndex].isAwaitingReview = (tasks.value[taskIndex].originalApiStatus.toLowerCase() === 'pendingreview')
      if (originalKanbanStatus !== 'completed' && tasks.value[taskIndex].originalApiStatus.toLowerCase() !== 'completed') {
        tasks.value[taskIndex].isOverdue = checkOverdue(tasks.value[taskIndex]) // Re-check overdue based on original state
      }
    }
  } finally {
    // This will be cleared by onDragEnd naturally
    // draggedTask.value = null; 
  }
}

// Helper function to map Kanban status to API status (for updates)
// Backend status strings: "NotStarted", "InProgress", "OnHold", "PendingReview", "Completed", "Cancelled", "Overdue"
function mapKanbanToApiStatus(kanbanStatus) {
    switch (kanbanStatus) {
        case 'todo': return 'NotStarted'; // Or "Todo" if your API expects that for unstarted tasks
        case 'in-progress': return 'InProgress';
        case 'completed': return 'Completed';
        // 'overdue' is not a kanbanStatus tasks can be dragged *to* for API update
        // 'onhold' and 'pendingreview' are not distinct columns here, handled by API or specific actions
        default: 
          console.warn(`Cannot map Kanban status '${kanbanStatus}' to an API status for update.`)
          return null; 
    }
}

// Helper function to map API status to Kanban status (used in fetchKanbanData)
// API status (rawTask.status): "NotStarted", "InProgress", "OnHold", "PendingReview", "Completed", "Cancelled", "Overdue"
// Kanban status (task.kanbanStatus): "todo", "in-progress", "completed"
function mapApiToKanbanStatus(apiStatusString, isTaskOverdue) {
     const statusLower = apiStatusString ? apiStatusString.toLowerCase() : 'notstarted'

     // Cancelled tasks are filtered out before this function is heavily used for column assignment
     if (statusLower === 'cancelled') return null; // Should be filtered earlier

     if (statusLower === 'completed') return 'completed';

     // If API explicitly says "Overdue", or if calculated as overdue AND not completed,
     // it still needs a base column (todo or in-progress). The `isTaskOverdue` flag on the task object handles the visual.
     // Tasks that are 'OnHold' or 'PendingReview' are placed in 'in-progress'
     if (statusLower === 'inprogress' || statusLower === 'onhold' || statusLower === 'pendingreview' || statusLower === 'overdue') {
         return 'in-progress';
     }
     
     // Default to 'todo' for "NotStarted" or any other unhandled non-completed/non-cancelled status.
     // This includes if API said "Overdue" but didn't have an underlying "InProgress" or "NotStarted" state; 
     // such tasks (e.g. an overdue "NotStarted" task) would land in 'todo' but be flagged as overdue.
     if (statusLower === 'notstarted') {
        return 'todo';
     }
     
     // Fallback for any unknown status that wasn't 'completed' or 'cancelled'
     // If it's an active task, and it's overdue, it can be in 'in-progress' or 'todo' column with overdue styling.
     // This logic prioritizes the active workflow columns.
     if (isTaskOverdue) {
        // If a task is overdue, it's either 'todo' or 'in-progress' (unless completed).
        // Default to 'in-progress' for overdue tasks that aren't explicitly 'NotStarted'.
        // This could be refined if API provides 'originalStatus' for 'Overdue' tasks.
        return (statusLower === 'notstarted') ? 'todo' : 'in-progress';
     } 
     // Default to 'todo' for any other case not explicitly handled (e.g. new API statuses not yet mapped)
     console.warn(`Unmapped API status '${apiStatusString}' encountered, defaulting to 'todo'. Task isOverdue: ${isTaskOverdue}`)
     return 'todo'; 
 }

// --- Debounce for Search ---
let debounceTimer;
function debouncedFetchData() {
    clearTimeout(debounceTimer);
    debounceTimer = setTimeout(() => {
        fetchKanbanData();
    }, 500); // 500ms delay
}

// Lifecycle Hook
onMounted(async () => { // <--- 改为 async
  // Fetch team members first (or in parallel)
  try {
    const userResponse = await userApi.getUserList({ pageSize: 1000 }); // <--- 获取用户列表 (无分页)
    if (userResponse && Array.isArray(userResponse.list)) {
        teamMembersList.value = userResponse.list;
        console.log('Fetched team members:', teamMembersList.value);
    } else if (userResponse && Array.isArray(userResponse)) { // Handle direct array response
         teamMembersList.value = userResponse;
         console.log('Fetched team members (direct array):', teamMembersList.value);
    } else {
        console.warn('Could not fetch team members or invalid format', userResponse);
        teamMembersList.value = []; // Ensure it's an array
    }
  } catch (error) {
      console.error('Error fetching team members:', error);
      ElMessage.error('获取负责人列表失败');
      teamMembersList.value = []; // Ensure it's an array on error
  }
  
  // Then fetch Kanban data (which uses getUserName/Avatar helpers)
  await fetchKanbanData(); // <--- 等待看板数据获取完成
  
  // setInterval(fetchKanbanData, 60000); // Optional: Auto-refresh
});

</script>

<style scoped>
/* Using CSS variables defined in src/styles/variables.scss */
.kanban-view {
  padding: 20px;
  background-color: var(--bg-color);
  height: calc(100vh - 60px); /* Adjust based on actual header height */
  display: flex;
  flex-direction: column;
}

.page-title {
  font-size: 1.8rem;
  font-weight: 600;
  color: var(--header-bg); /* Using header-bg for title color */
  margin-bottom: 15px;
}

.filter-card {
  margin-bottom: 20px;
  background-color: var(--card-bg);
  border: none;
  border-radius: 8px;
}

.filters {
  display: flex;
  gap: 15px;
  align-items: center;
  flex-wrap: wrap;
}

.loading-skeleton {
  padding: 20px;
}

.kanban-board {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px; /* Reduced gap slightly */
  flex-grow: 1;
  overflow-x: auto; /* Allow horizontal scroll if columns exceed width */
  padding-bottom: 10px; /* Space for scrollbar */
}

.task-column {
  background-color: var(--column-bg);
  border-radius: 8px;
  padding: 15px;
  display: flex;
  flex-direction: column;
  border: 1px solid var(--border-color-light);
  max-height: calc(100vh - 230px); /* Adjust based on header/filter height */
  min-height: 200px;
  transition: background-color 0.3s ease;
}

.task-column.drag-over {
  background-color: var(--drag-over-bg); /* Highlight column when dragging over */
  border: 1px dashed var(--primary-color);
}

.column-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 12px;
  margin-bottom: 15px;
  border-bottom: 1px solid var(--border-color-light);
}

.column-header h4 {
  font-size: 1.1rem; /* Slightly smaller than example */
  font-weight: 600;
  color: var(--header-bg);
  margin: 0;
}

.status-count {
  background-color: #e9ecef; /* Consider using a CSS var */
  color: var(--text-secondary-color);
  border-radius: 12px;
  padding: 4px 10px;
  font-size: 0.75rem;
  font-weight: 600;
}

.task-list {
  flex-grow: 1;
  overflow-y: auto;
  min-height: 100px; /* Ensure drop zone exists even when empty */
  padding-right: 5px; /* Space for scrollbar */
}

/* Custom Scrollbar */
.task-list::-webkit-scrollbar { width: 6px; }
.task-list::-webkit-scrollbar-track { background: transparent; }
.task-list::-webkit-scrollbar-thumb { background-color: #ccc; border-radius: 3px; }
.task-list::-webkit-scrollbar-thumb:hover { background-color: #aaa; }

.task-card {
  background-color: var(--card-bg);
  border-radius: 6px;
  padding: 12px 15px; /* Slightly adjusted padding */
  margin-bottom: 12px; /* Slightly adjusted margin */
  box-shadow: var(--shadow-light);
  transition: var(--transition);
  cursor: grab;
  border-left: 5px solid var(--info-color); /* Default border color */
  position: relative; /* Needed for absolute positioning of elements if any */
}

.task-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

/* --- Priority Borders --- */
.task-card.priority-high { border-left-color: var(--danger-color); }
.task-card.priority-medium { border-left-color: var(--warning-color); }
.task-card.priority-low { border-left-color: var(--success-color); }

/* --- Dragging States --- */
.task-card.is-dragging {
  opacity: 0.7;
  transform: rotate(2deg);
  cursor: grabbing;
}
.task-card.ghost-card { /* Placeholder style */
  opacity: 0.4;
  background: var(--hover-bg-color);
  border: 2px dashed var(--border-color);
}
.task-card.chosen-card { /* Style of the card being dragged */
  /* Already handled by is-dragging */
}
.task-card.drag-card { /* Might be redundant with is-dragging */
    opacity: 0.7;
    transform: rotate(2deg);
}

.task-title {
  font-weight: 600;
  font-size: 0.95rem; /* Slightly smaller */
  color: var(--header-bg);
  line-height: 1.4;
  margin-bottom: 8px;
  word-break: break-word;
}

.task-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.8rem; /* Smaller meta text */
  color: var(--text-secondary-color);
  margin-bottom: 6px;
  flex-wrap: wrap;
  gap: 8px;
}
.task-meta:last-child {
    margin-bottom: 0;
}

.assignee {
  display: flex;
  align-items: center;
  gap: 6px; /* Gap between avatar and name */
}

.small-avatar {
  width: 22px;
  height: 22px;
  border-radius: 50%;
  object-fit: cover; /* Added for better image handling */
  background-color: #eee; /* Background for initials */
  display: inline-flex; /* Center initials */
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  font-weight: bold;
  color: #555;
}

/* --- Priority Tags --- */
.priority-tag {
  padding: 2px 6px; /* Smaller tags */
  border-radius: 4px;
  font-size: 0.7rem;
  font-weight: 600;
  text-transform: capitalize;
}
.priority-tag.priority-low { background: #e1fcef; color: #1a7431; }
.priority-tag.priority-medium { background: #fff8e1; color: #b8860b; }
.priority-tag.priority-high { background: #ffebee; color: #b71c1c; }

.due-date {
  display: inline-flex; /* Align icon and text */
  align-items: center;
  gap: 4px;
}
.due-date .el-icon {
    font-size: 0.9em; /* Slightly smaller icon */
}
.due-date.overdue {
  color: var(--danger-color);
  font-weight: bold;
}

.empty-column-placeholder {
  text-align: center;
  color: var(--text-placeholder-color);
  margin-top: 20px;
  font-size: 0.9em;
  padding: 20px;
  border: 2px dashed var(--border-color-light);
  border-radius: 6px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
}
.empty-column-placeholder .el-icon {
    font-size: 1.5rem;
}

/* Ensure cards take full width within the list */
.task-list > div {
    width: 100%;
}

/* Dark theme adjustments if needed */
.dark-theme .status-count {
    background-color: var(--dark-border-color);
    color: var(--dark-text-color);
}
.dark-theme .priority-tag.priority-low { background: #2c5d3b; color: #c8e6c9; }
.dark-theme .priority-tag.priority-medium { background: #6f4f14; color: #ffecb3; }
.dark-theme .priority-tag.priority-high { background: #7f1d1d; color: #ffcdd2; }
.dark-theme .small-avatar { background-color: #4a5568; color: #e2e8f0;}

.task-card.overdue-task {
  border-left-color: var(--danger-color) !important; /* Emphasize overdue with border */
  background-color: #fff0f0; /* Light red background for overdue */
}
.dark-theme .task-card.overdue-task {
    background-color: #5e3030;
}

.task-card.paused-task {
  opacity: 0.8;
  /* Add a specific style for paused tasks, e.g., a slight desaturation or an icon */
   border-left-style: dashed;
}

.task-card.review-task {
  /* Add a specific style for tasks awaiting review */
  border-left-style: dotted;
  border-left-color: var(--el-color-primary-light-3);
}

.status-indicator-tag {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 0.7rem;
  font-weight: 600;
}
.paused-tag {
  background-color: #e0e0e0;
  color: #5f5f5f;
}
.dark-theme .paused-tag {
    background-color: #424242;
    color: #bdbdbd;
}
.review-tag {
  background-color: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
}
.dark-theme .review-tag {
    background-color: var(--el-color-primary-dark-2);
    color: var(--el-color-primary-light-7);
}
</style> 