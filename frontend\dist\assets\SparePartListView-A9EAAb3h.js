import{_ as e,r as a,ad as l,c as t,j as s,a as u,k as d,o as r,w as o,d as n,e as i,b as c,F as m,h as p,n as v,t as f,p as y,i as b,E as h,z as g,f as _,ao as w,S as k,bT as V,bJ as I,$ as C,a9 as x,l as U,m as N,af as S}from"./index-CG5lHOPO.js";import{u as q}from"./spareparts-CUOQzjwG.js";import{getSparePartStockSummary as j,getSparePartInventories as T,getSparePartStatusTypes as z,getSparePartLocations as Q,adjustSparePartStatus as D,batchUpdateSparePartStatus as A,createRepairOrder as E,getSpareParts as R,updateSparePart as B,stockAdjustment as O,createSparePart as F,sparePartInbound as P,sparePartOutbound as $,getSparePartTransactions as M}from"./spareparts-DKUrs8IX.js";const L={class:"status-adjustment-container"},G={class:"status-item"},J={class:"status-quantity"},K={class:"adjustment-grid"},H={class:"status-header"},W={class:"current-qty"},Y={class:"adjustment-controls"},X={class:"adjustment-buttons"},Z={class:"result-quantity"},ee={class:"summary-content"},ae={class:"summary-item"},le={class:"summary-item"},te={class:"dialog-footer"},se=e({__name:"StatusAdjustmentDialog",props:{visible:{type:Boolean,default:!1},partId:{type:Number,required:!0},currentStatusBreakdown:{type:Array,default:()=>[]},statusTypes:{type:Array,default:()=>[]},unit:{type:String,default:"个"}},emits:["update:visible","submit"],setup(e,{emit:g}){const _=e,w=g,k=a(),V=l({adjustments:{},reason:""}),I=()=>{const e={};_.statusTypes.forEach((a=>{e[a.id]=q(a.code)})),V.adjustments=e},C=t((()=>_.currentStatusBreakdown.reduce(((e,a)=>e+a.quantity),0))),x=t((()=>Object.values(V.adjustments).reduce(((e,a)=>e+(a||0)),0))),U=t((()=>x.value-C.value)),N=t((()=>""!==V.reason.trim()&&S.value)),S=t((()=>_.statusTypes.some((e=>q(e.code)!==(V.adjustments[e.id]||0))))),q=e=>{const a=_.currentStatusBreakdown.find((a=>a.statusCode===e));return a?a.quantity:0},j=e=>{const a=_.statusTypes.find((a=>a.code===e));return a&&V.adjustments[a.id]||0},T=(e,a)=>{const l=V.adjustments[e]||0,t=Math.max(0,l+a);V.adjustments[e]=t},z=async()=>{if(!V.reason.trim())return void h.warning("请输入调整原因");if(!S.value)return void h.warning("没有检测到状态数量变化");const e=[];_.statusTypes.forEach((a=>{const l=q(a.code),t=V.adjustments[a.id]||0;l!==t&&e.push({statusId:a.id,statusCode:a.code,statusName:a.name,fromQuantity:l,toQuantity:t,changeQuantity:t-l})}));const a={partId:_.partId,adjustments:e,reason:V.reason,totalChange:U.value};w("submit",a)},Q=()=>{w("update:visible",!1),V.reason="",I()};return s((()=>_.visible),(e=>{e&&I()})),s((()=>_.statusTypes),(()=>{_.visible&&I()}),{deep:!0}),(a,l)=>{const t=u("el-col"),s=u("el-row"),h=u("el-card"),g=u("el-input-number"),_=u("el-form-item"),w=u("el-button"),I=u("el-button-group"),S=u("el-input"),D=u("el-form"),A=u("el-dialog");return r(),d(A,{"model-value":e.visible,"onUpdate:modelValue":l[1]||(l[1]=e=>a.$emit("update:visible",e)),title:"库存状态调整",width:"800px",onClose:Q},{footer:o((()=>[n("div",te,[i(w,{onClick:Q},{default:o((()=>l[8]||(l[8]=[y("取消")]))),_:1}),i(w,{type:"primary",onClick:z,disabled:!N.value},{default:o((()=>l[9]||(l[9]=[y("确认调整")]))),_:1},8,["disabled"])])])),default:o((()=>[n("div",L,[i(h,{class:"current-status-card",style:{"margin-bottom":"16px"}},{header:o((()=>l[2]||(l[2]=[n("span",null,"当前库存状态",-1)]))),default:o((()=>[i(s,{gutter:16},{default:o((()=>[(r(!0),c(m,null,p(e.currentStatusBreakdown,(a=>(r(),d(t,{key:a.statusCode,span:6},{default:o((()=>[n("div",G,[n("div",{class:"status-name",style:v({color:a.color})},f(a.statusName),5),n("div",J,f(a.quantity)+" "+f(e.unit),1)])])),_:2},1024)))),128))])),_:1})])),_:1}),i(D,{ref_key:"adjustmentFormRef",ref:k,model:V,"label-width":"120px"},{default:o((()=>[n("div",K,[(r(!0),c(m,null,p(e.statusTypes,(a=>(r(),c("div",{key:a.id,class:"adjustment-item"},[i(h,{class:"status-adjustment-card"},{header:o((()=>[n("div",H,[n("span",{style:v({color:a.color})},f(a.name),5),n("span",W,"(当前: "+f(q(a.code))+")",1)])])),default:o((()=>[n("div",Y,[i(_,{label:"调整数量",style:{"margin-bottom":"8px"}},{default:o((()=>[i(g,{modelValue:V.adjustments[a.id],"onUpdate:modelValue":e=>V.adjustments[a.id]=e,min:(a.code,0),max:(a.code,9999),step:1,size:"small",style:{width:"100%"},onChange:e=>{return l=a.id,t=e,void(V.adjustments[l]=t||0);var l,t}},null,8,["modelValue","onUpdate:modelValue","min","max","onChange"])])),_:2},1024),n("div",X,[i(I,{size:"small"},{default:o((()=>[i(w,{onClick:e=>T(a.id,1)},{default:o((()=>l[3]||(l[3]=[y("+1")]))),_:2},1032,["onClick"]),i(w,{onClick:e=>T(a.id,5)},{default:o((()=>l[4]||(l[4]=[y("+5")]))),_:2},1032,["onClick"]),i(w,{onClick:e=>T(a.id,-1)},{default:o((()=>l[5]||(l[5]=[y("-1")]))),_:2},1032,["onClick"]),i(w,{onClick:e=>T(a.id,-5)},{default:o((()=>l[6]||(l[6]=[y("-5")]))),_:2},1032,["onClick"])])),_:2},1024)]),n("div",Z," 调整后: "+f(j(a.code))+" "+f(e.unit),1)])])),_:2},1024)])))),128))]),i(_,{label:"调整原因",prop:"reason",style:{"margin-top":"16px"}},{default:o((()=>[i(S,{modelValue:V.reason,"onUpdate:modelValue":l[0]||(l[0]=e=>V.reason=e),type:"textarea",rows:3,placeholder:"请输入状态调整原因（必填）"},null,8,["modelValue"])])),_:1}),i(h,{class:"adjustment-summary",style:{"margin-top":"16px"}},{header:o((()=>l[7]||(l[7]=[n("span",null,"调整汇总",-1)]))),default:o((()=>[n("div",ee,[n("div",ae,[n("span",null,"调整前总数: "+f(C.value)+" "+f(e.unit),1)]),n("div",le,[n("span",null,"调整后总数: "+f(x.value)+" "+f(e.unit),1)]),n("div",{class:b(["summary-item",{"quantity-change":0!==U.value}])},[n("span",null,"数量变化: "+f(U.value>0?"+":"")+f(U.value)+" "+f(e.unit),1)],2)])])),_:1})])),_:1},8,["model"])])])),_:1},8,["model-value"])}}},[["__scopeId","data-v-687a2d4a"]]),ue={class:"spare-part-status-manager"},de={class:"status-summary-cards"},re={class:"status-info"},oe={class:"status-icon"},ne={class:"status-details"},ie={class:"status-count"},ce={class:"status-breakdown"},me={class:"breakdown-item"},pe={class:"breakdown-item"},ve={class:"breakdown-item"},fe={class:"status-info"},ye={class:"status-icon"},be={class:"status-details"},he={class:"status-count"},ge={class:"status-breakdown"},_e={class:"breakdown-item"},we={class:"breakdown-item"},ke={class:"breakdown-item"},Ve={class:"status-info"},Ie={class:"status-icon"},Ce={class:"status-details"},xe={class:"status-count"},Ue={class:"status-breakdown"},Ne={class:"breakdown-item"},Se={class:"breakdown-item"},qe={class:"breakdown-item"},je={class:"status-info"},Te={class:"status-icon"},ze={class:"status-details"},Qe={class:"status-count"},De={class:"status-breakdown"},Ae={class:"breakdown-item"},Ee={class:"breakdown-item"},Re={class:"card-header"},Be={class:"header-actions"},Oe={class:"quantity-display"},Fe={key:0,class:"serial-numbers"},Pe={key:0,class:"more-serials"},$e={key:1,class:"no-serial"},Me={key:1},Le={class:"selected-items"},Ge={class:"dialog-footer"},Je={class:"repair-items"},Ke={class:"item-info"},He={class:"item-name"},We={class:"item-location"},Ye={class:"item-status"},Xe={class:"item-quantity"},Ze={class:"item-details"},ea={class:"dialog-footer"},aa=e({__name:"SparePartStatusManager",props:{partId:{type:Number,required:!0}},setup(e){const U=e,N=a({partId:0,partCode:"",partName:"",unit:"",totalQuantity:0,availableQuantity:0,unavailableQuantity:0,inTransitQuantity:0,reservedQuantity:0,statusBreakdown:[]}),S=a([]),q=a([]),R=a([]),B=a([]),O=a([]),F=a([]),P=a(null),$=a(null),M=a([]),L=a(!1),G=a(!1),J=a(!1),K=l({newStatusId:null,reason:"",repairOrderId:null}),H=l({type:2,title:"",description:"",priority:3,faultId:null,supplierId:null,estimatedCost:null,estimatedDays:null,notes:""}),W={newStatusId:[{required:!0,message:"请选择新状态",trigger:"change"}],reason:[{required:!0,message:"请输入变更原因",trigger:"blur"}]},Y={type:[{required:!0,message:"请选择维修类型",trigger:"change"}],title:[{required:!0,message:"请输入维修标题",trigger:"blur"}],description:[{required:!0,message:"请输入维修描述",trigger:"blur"}],priority:[{required:!0,message:"请选择优先级",trigger:"change"}]},X=t((()=>{let e=S.value;return P.value&&(e=e.filter((e=>e.statusId===P.value))),$.value&&(e=e.filter((e=>e.locationId===$.value))),e})),Z=e=>{const a=N.value.statusBreakdown.find((a=>a.statusCode===e));return a?a.quantity:0},ee=e=>new Date(e).toLocaleDateString("zh-CN"),ae=e=>{const a=new Date,l=new Date(e),t=Math.ceil((l-a)/864e5);return t<0?"warranty-expired":t<30?"warranty-warning":"warranty-normal"},le=e=>{M.value=e},te=()=>{L.value=!0},aa=()=>{0!==M.value.length?G.value=!0:h.warning("请先选择要变更状态的库存项目")},la=()=>{if(0===M.value.length)return void h.warning("请先选择要返厂维修的库存项目");M.value.some((e=>"Unavailable"===e.statusCategory&&(e.statusName.includes("故障")||e.statusName.includes("损坏"))))?J.value=!0:x.confirm("通常返厂维修的是故障或损坏的备件，您选择的项目中没有此类状态。是否继续？","确认返厂",{confirmButtonText:"继续",cancelButtonText:"取消",type:"warning"}).then((()=>{J.value=!0})).catch((()=>{}))},ta=async e=>{try{const a=await D(e);a.success?(h.success("状态调整成功"),L.value=!1,await da(),await ra()):h.error("状态调整失败: "+a.message)}catch(a){h.error("状态调整失败")}},sa=async()=>{try{const e=M.value.map((e=>({inventoryId:e.id,newStatusId:K.newStatusId,reason:K.reason}))),a=await A({updates:e});a.success?(h.success(`状态变更成功，共更新 ${a.data.successCount} 项`),G.value=!1,await da(),await ra(),M.value=[],K.newStatusId=null,K.reason="",K.repairOrderId=null):h.error("状态变更失败: "+a.message)}catch(e){h.error("状态变更失败")}},ua=async()=>{try{const e={title:H.title,priority:H.priority,supplierId:H.supplierId,faultId:H.faultId,inventoryIds:M.value.map((e=>e.id)),faultDescription:H.description,notes:H.notes},a=await E(e);a.success?(h.success(`返厂维修单创建成功，单号: ${a.data.orderNumber}`),J.value=!1,await da(),await ra(),M.value=[],Object.assign(H,{type:2,title:"",description:"",priority:3,faultId:null,supplierId:null,estimatedCost:null,estimatedDays:null,notes:""})):h.error("创建返厂维修单失败: "+a.message)}catch(e){h.error("创建返厂维修单失败")}};g((async()=>{await Promise.all([oa(),na()]),await da(),await ra()}));const da=async()=>{try{const e=await j(U.partId);if(e.success){if(N.value=e.data,q.value.length>0){const e=N.value.statusBreakdown.map((e=>e.statusCode));q.value.forEach((a=>{e.includes(a.code)||N.value.statusBreakdown.push({statusCode:a.code,statusName:a.name,category:a.category,quantity:0,color:a.color})})),N.value.statusBreakdown.sort(((e,a)=>q.value.findIndex((a=>a.code===e.statusCode))-q.value.findIndex((e=>e.code===a.statusCode))))}}else h.error("加载库存汇总失败: "+e.message)}catch(e){h.error("加载库存汇总失败")}},ra=async()=>{try{const e=await T(U.partId,{pageIndex:1,pageSize:1e3});e.success?S.value=e.data.items||[]:h.error("加载库存明细失败: "+e.message)}catch(e){h.error("加载库存明细失败")}},oa=async()=>{try{const e=await z();e.success?q.value=e.data||[]:h.error("加载状态类型失败: "+e.message)}catch(e){h.error("加载状态类型失败")}},na=async()=>{try{const e=await Q();e.success?R.value=e.data||[]:h.error("加载库位数据失败: "+e.message)}catch(e){h.error("加载库位数据失败")}};return s((()=>U.partId),(async e=>{e&&(await da(),await ra())})),(e,a)=>{const l=u("el-icon"),t=u("el-card"),s=u("el-col"),g=u("el-row"),x=u("el-option"),S=u("el-select"),j=u("el-button"),T=u("el-table-column"),z=u("el-tag"),Q=u("el-table"),D=u("el-form-item"),A=u("el-input"),E=u("el-form"),da=u("el-dialog"),ra=u("el-input-number");return r(),c("div",ue,[n("div",de,[i(g,{gutter:16},{default:o((()=>[i(s,{span:6},{default:o((()=>[i(t,{class:"status-card available"},{default:o((()=>[n("div",re,[n("div",oe,[i(l,null,{default:o((()=>[i(_(w))])),_:1})]),n("div",ne,[n("div",ie,f(N.value.availableQuantity),1),a[19]||(a[19]=n("div",{class:"status-label"},"可用库存",-1)),n("div",ce,[n("span",me,"新品: "+f(Z("NEW")),1),n("span",pe,"良好: "+f(Z("GOOD")),1),n("span",ve,"翻新: "+f(Z("REFURBISHED")),1)])])])])),_:1})])),_:1}),i(s,{span:6},{default:o((()=>[i(t,{class:"status-card unavailable"},{default:o((()=>[n("div",fe,[n("div",ye,[i(l,null,{default:o((()=>[i(_(k))])),_:1})]),n("div",be,[n("div",he,f(N.value.unavailableQuantity),1),a[20]||(a[20]=n("div",{class:"status-label"},"不可用库存",-1)),n("div",ge,[n("span",_e,"故障: "+f(Z("FAULTY")),1),n("span",we,"损坏: "+f(Z("DAMAGED")),1),n("span",ke,"报废: "+f(Z("SCRAPPED")),1)])])])])),_:1})])),_:1}),i(s,{span:6},{default:o((()=>[i(t,{class:"status-card in-transit"},{default:o((()=>[n("div",Ve,[n("div",Ie,[i(l,null,{default:o((()=>[i(_(V))])),_:1})]),n("div",Ce,[n("div",xe,f(N.value.inTransitQuantity),1),a[21]||(a[21]=n("div",{class:"status-label"},"在途库存",-1)),n("div",Ue,[n("span",Ne,"返厂中: "+f(Z("UNDER_REPAIR")),1),n("span",Se,"维修中: "+f(Z("REPAIRING")),1),n("span",qe,"待检验: "+f(Z("PENDING_INSPECTION")),1)])])])])),_:1})])),_:1}),i(s,{span:6},{default:o((()=>[i(t,{class:"status-card reserved"},{default:o((()=>[n("div",je,[n("div",Te,[i(l,null,{default:o((()=>[i(_(I))])),_:1})]),n("div",ze,[n("div",Qe,f(N.value.reservedQuantity),1),a[22]||(a[22]=n("div",{class:"status-label"},"预留库存",-1)),n("div",De,[n("span",Ae,"已分配: "+f(Z("ALLOCATED")),1),n("span",Ee,"预留: "+f(Z("RESERVED")),1)])])])])),_:1})])),_:1})])),_:1})]),i(t,{class:"inventory-table-card",style:{"margin-top":"16px"}},{header:o((()=>[n("div",Re,[a[26]||(a[26]=n("span",{class:"card-title"},"📦 库存明细",-1)),n("div",Be,[i(S,{modelValue:P.value,"onUpdate:modelValue":a[0]||(a[0]=e=>P.value=e),placeholder:"筛选状态",clearable:"",size:"small",style:{width:"150px","margin-right":"8px"}},{default:o((()=>[(r(!0),c(m,null,p(q.value,(e=>(r(),d(x,{key:e.id,label:e.name,value:e.id},{default:o((()=>[n("span",{style:v({color:e.color})},f(e.name),5)])),_:2},1032,["label","value"])))),128))])),_:1},8,["modelValue"]),i(S,{modelValue:$.value,"onUpdate:modelValue":a[1]||(a[1]=e=>$.value=e),placeholder:"筛选库位",clearable:"",size:"small",style:{width:"150px","margin-right":"8px"}},{default:o((()=>[(r(!0),c(m,null,p(R.value,(e=>(r(),d(x,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"]),i(j,{type:"primary",size:"small",onClick:te},{default:o((()=>a[23]||(a[23]=[y("状态调整")]))),_:1}),i(j,{type:"success",size:"small",onClick:aa},{default:o((()=>a[24]||(a[24]=[y("批量变更")]))),_:1}),i(j,{type:"warning",size:"small",onClick:la},{default:o((()=>a[25]||(a[25]=[y("返厂维修")]))),_:1})])])])),default:o((()=>[i(Q,{ref:"inventoryTable",data:X.value,border:"",style:{width:"100%"},onSelectionChange:le},{default:o((()=>[i(T,{type:"selection",width:"55"}),i(T,{prop:"locationName",label:"库位",width:"120"}),i(T,{prop:"statusName",label:"状态",width:"100"},{default:o((({row:e})=>[i(z,{color:e.statusColor,effect:"dark",size:"small"},{default:o((()=>[y(f(e.statusName),1)])),_:2},1032,["color"])])),_:1}),i(T,{prop:"quantity",label:"数量",width:"80",align:"center"},{default:o((({row:e})=>[n("span",Oe,f(e.quantity)+" "+f(N.value.unit),1)])),_:1}),i(T,{prop:"batchNumber",label:"批次号",width:"120"}),i(T,{prop:"serialNumbers",label:"序列号","min-width":"150"},{default:o((({row:e})=>[e.serialNumbers&&e.serialNumbers.length>0?(r(),c("div",Fe,[(r(!0),c(m,null,p(e.serialNumbers.slice(0,2),((e,a)=>(r(),d(z,{key:a,size:"small",style:{"margin-right":"4px"}},{default:o((()=>[y(f(e),1)])),_:2},1024)))),128)),e.serialNumbers.length>2?(r(),c("span",Pe," +"+f(e.serialNumbers.length-2)+"个 ",1)):C("",!0)])):(r(),c("span",$e,"-"))])),_:1}),i(T,{prop:"purchaseDate",label:"采购日期",width:"100"},{default:o((({row:e})=>[y(f(e.purchaseDate?ee(e.purchaseDate):"-"),1)])),_:1}),i(T,{prop:"warrantyExpireDate",label:"保修到期",width:"100"},{default:o((({row:e})=>[e.warrantyExpireDate?(r(),c("span",{key:0,class:b(ae(e.warrantyExpireDate))},f(ee(e.warrantyExpireDate)),3)):(r(),c("span",Me,"-"))])),_:1}),i(T,{prop:"unitCost",label:"单价",width:"100",align:"right"},{default:o((({row:e})=>[y(f(e.unitCost?`¥${e.unitCost.toFixed(2)}`:"-"),1)])),_:1}),i(T,{prop:"notes",label:"备注","min-width":"120"}),i(T,{label:"操作",width:"120",fixed:"right"},{default:o((({row:e})=>[i(j,{type:"text",size:"small",onClick:e=>{h.info("查看状态变更历史")}},{default:o((()=>a[27]||(a[27]=[y("历史")]))),_:2},1032,["onClick"]),i(j,{type:"text",size:"small",onClick:e=>{h.info("编辑库存明细")}},{default:o((()=>a[28]||(a[28]=[y("编辑")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"])])),_:1}),i(se,{visible:L.value,"onUpdate:visible":a[2]||(a[2]=e=>L.value=e),"part-id":U.partId,"current-status-breakdown":N.value.statusBreakdown,"status-types":q.value,unit:N.value.unit||"个",onSubmit:ta},null,8,["visible","part-id","current-status-breakdown","status-types","unit"]),i(da,{modelValue:G.value,"onUpdate:modelValue":a[7]||(a[7]=e=>G.value=e),title:"批量状态变更",width:"600px"},{footer:o((()=>[n("div",Ge,[i(j,{onClick:a[6]||(a[6]=e=>G.value=!1)},{default:o((()=>a[29]||(a[29]=[y("取消")]))),_:1}),i(j,{type:"primary",onClick:sa},{default:o((()=>a[30]||(a[30]=[y("确定")]))),_:1})])])),default:o((()=>[i(E,{ref:"statusChangeFormRef",model:K,rules:W,"label-width":"100px"},{default:o((()=>[i(D,{label:"选中项目"},{default:o((()=>[n("div",Le,[(r(!0),c(m,null,p(M.value,(e=>(r(),c("div",{key:e.id,class:"selected-item"},[n("span",null,f(e.locationName)+" - "+f(e.statusName)+" - "+f(e.quantity)+f(N.value.unit),1)])))),128))])])),_:1}),i(D,{label:"新状态",prop:"newStatusId"},{default:o((()=>[i(S,{modelValue:K.newStatusId,"onUpdate:modelValue":a[3]||(a[3]=e=>K.newStatusId=e),placeholder:"请选择新状态",style:{width:"100%"}},{default:o((()=>[(r(!0),c(m,null,p(q.value,(e=>(r(),d(x,{key:e.id,label:e.name,value:e.id},{default:o((()=>[n("span",{style:v({color:e.color})},f(e.name),5)])),_:2},1032,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),i(D,{label:"变更原因",prop:"reason"},{default:o((()=>[i(A,{modelValue:K.reason,"onUpdate:modelValue":a[4]||(a[4]=e=>K.reason=e),type:"textarea",rows:3,placeholder:"请输入状态变更原因"},null,8,["modelValue"])])),_:1}),i(D,{label:"关联返厂单"},{default:o((()=>[i(S,{modelValue:K.repairOrderId,"onUpdate:modelValue":a[5]||(a[5]=e=>K.repairOrderId=e),placeholder:"选择关联返厂单（可选）",clearable:"",style:{width:"100%"}},{default:o((()=>[(r(!0),c(m,null,p(B.value,(e=>(r(),d(x,{key:e.id,label:e.orderNumber,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue"]),i(da,{modelValue:J.value,"onUpdate:modelValue":a[18]||(a[18]=e=>J.value=e),title:"创建返厂维修单",width:"800px"},{footer:o((()=>[n("div",ea,[i(j,{onClick:a[17]||(a[17]=e=>J.value=!1)},{default:o((()=>a[31]||(a[31]=[y("取消")]))),_:1}),i(j,{type:"primary",onClick:ua},{default:o((()=>a[32]||(a[32]=[y("创建返厂单")]))),_:1})])])),default:o((()=>[i(E,{ref:"repairOrderFormRef",model:H,rules:Y,"label-width":"100px"},{default:o((()=>[i(g,{gutter:16},{default:o((()=>[i(s,{span:12},{default:o((()=>[i(D,{label:"维修类型",prop:"type"},{default:o((()=>[i(S,{modelValue:H.type,"onUpdate:modelValue":a[8]||(a[8]=e=>H.type=e),placeholder:"请选择维修类型",style:{width:"100%"}},{default:o((()=>[i(x,{label:"故障维修",value:1}),i(x,{label:"备件维修",value:2}),i(x,{label:"预防性维修",value:3})])),_:1},8,["modelValue"])])),_:1})])),_:1}),i(s,{span:12},{default:o((()=>[i(D,{label:"优先级",prop:"priority"},{default:o((()=>[i(S,{modelValue:H.priority,"onUpdate:modelValue":a[9]||(a[9]=e=>H.priority=e),placeholder:"请选择优先级",style:{width:"100%"}},{default:o((()=>[i(x,{label:"紧急",value:1}),i(x,{label:"高",value:2}),i(x,{label:"中",value:3}),i(x,{label:"低",value:4})])),_:1},8,["modelValue"])])),_:1})])),_:1})])),_:1}),i(D,{label:"维修标题",prop:"title"},{default:o((()=>[i(A,{modelValue:H.title,"onUpdate:modelValue":a[10]||(a[10]=e=>H.title=e),placeholder:"请输入维修标题"},null,8,["modelValue"])])),_:1}),i(D,{label:"维修描述",prop:"description"},{default:o((()=>[i(A,{modelValue:H.description,"onUpdate:modelValue":a[11]||(a[11]=e=>H.description=e),type:"textarea",rows:3,placeholder:"请输入维修描述"},null,8,["modelValue"])])),_:1}),i(D,{label:"关联故障"},{default:o((()=>[i(S,{modelValue:H.faultId,"onUpdate:modelValue":a[12]||(a[12]=e=>H.faultId=e),placeholder:"选择关联故障（可选）",clearable:"",style:{width:"100%"}},{default:o((()=>[(r(!0),c(m,null,p(O.value,(e=>(r(),d(x,{key:e.id,label:e.title,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),i(D,{label:"维修供应商"},{default:o((()=>[i(S,{modelValue:H.supplierId,"onUpdate:modelValue":a[13]||(a[13]=e=>H.supplierId=e),placeholder:"选择维修供应商",style:{width:"100%"}},{default:o((()=>[(r(!0),c(m,null,p(F.value,(e=>(r(),d(x,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),i(g,{gutter:16},{default:o((()=>[i(s,{span:12},{default:o((()=>[i(D,{label:"预估费用"},{default:o((()=>[i(ra,{modelValue:H.estimatedCost,"onUpdate:modelValue":a[14]||(a[14]=e=>H.estimatedCost=e),min:0,precision:2,placeholder:"预估费用",style:{width:"100%"}},null,8,["modelValue"])])),_:1})])),_:1}),i(s,{span:12},{default:o((()=>[i(D,{label:"预估天数"},{default:o((()=>[i(ra,{modelValue:H.estimatedDays,"onUpdate:modelValue":a[15]||(a[15]=e=>H.estimatedDays=e),min:1,placeholder:"预估维修天数",style:{width:"100%"}},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),i(D,{label:"备注"},{default:o((()=>[i(A,{modelValue:H.notes,"onUpdate:modelValue":a[16]||(a[16]=e=>H.notes=e),type:"textarea",rows:2,placeholder:"请输入备注"},null,8,["modelValue"])])),_:1}),i(D,{label:"维修物品"},{default:o((()=>[n("div",Je,[(r(!0),c(m,null,p(M.value,(e=>(r(),c("div",{key:e.id,class:"repair-item"},[n("div",Ke,[n("span",He,f(N.value.partName),1),n("span",We,f(e.locationName),1),n("span",Ye,f(e.statusName),1),n("span",Xe,f(e.quantity)+f(N.value.unit),1)]),n("div",Ze,[i(A,{modelValue:e.faultDescription,"onUpdate:modelValue":a=>e.faultDescription=a,placeholder:"故障描述",size:"small",style:{"margin-bottom":"4px"}},null,8,["modelValue","onUpdate:modelValue"]),i(A,{modelValue:e.serialNumber,"onUpdate:modelValue":a=>e.serialNumber=a,placeholder:"序列号（可选）",size:"small"},null,8,["modelValue","onUpdate:modelValue"])])])))),128))])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-490aa10f"]]),la={class:"spare-part-list-view"},ta={class:"page-header"},sa={class:"filter-container"},ua={class:"pagination-container"},da={class:"stock-display"},ra={class:"stock-value"},oa={key:0,class:"stock-adjustment"},na={class:"dialog-footer"},ia={class:"dialog-footer"},ca={class:"dialog-footer"},ma={key:0,class:"detail-content"},pa={class:"price"},va={class:"card-header"},fa={class:"threshold-number"},ya={class:"threshold-number"},ba={class:"stock-chart"},ha={class:"chart-bar"},ga={class:"bar-container"},_a={class:"bar-markers"},wa={class:"chart-labels"},ka={class:"card-header"},Va={class:"dialog-footer"},Ia={class:"dialog-footer"},Ca=e({__name:"SparePartListView",setup(e){const s=a(!1),_=a([]),w=a(0),k=a(!1),V=a("add"),I=a(null),x=a(!1),j=a(!1),T=a(null),z=a(null),Q=a({}),D=a(!1),A=a(!1),E=a([]),L=a(!1),G=a(!1),J=a(!0),K=q(),H=l({pageIndex:1,pageSize:10,name:"",code:"",typeId:"",stockStatus:"",sortBy:"",sortOrder:""}),W=l({id:null,code:"",materialNumber:"",name:"",typeId:"",specification:"",brand:"",unit:"",initialStock:0,stockQuantity:0,stockAdjustment:0,adjustmentReason:"",warningThreshold:10,minStock:5,locationId:"",price:null,remarks:""}),Y=l({partId:null,quantity:1,locationId:"",reasonType:1,referenceNumber:"",remarks:""}),X=l({partId:null,quantity:1,locationId:"",reasonType:3,referenceNumber:"",relatedAssetId:null,relatedFaultId:null,remarks:""}),Z={code:[{required:!0,message:"请输入备件编号",trigger:"blur"},{max:50,message:"长度不能超过50个字符",trigger:"blur"}],materialNumber:[{max:50,message:"长度不能超过50个字符",trigger:"blur"}],name:[{required:!0,message:"请输入备件名称",trigger:"blur"},{max:100,message:"长度不能超过100个字符",trigger:"blur"}],typeId:[{required:!0,message:"请选择备件类型",trigger:"change"}],unit:[{required:!0,message:"请输入单位",trigger:"blur"},{max:10,message:"长度不能超过10个字符",trigger:"blur"}],locationId:[{required:!0,message:"请选择库位",trigger:"change"}]},ee={quantity:[{required:!0,message:"请输入入库数量",trigger:"blur"},{type:"number",min:1,message:"数量必须大于0",trigger:"blur"}],locationId:[{required:!0,message:"请选择库位",trigger:"change"}],reasonType:[{required:!0,message:"请选择入库类型",trigger:"change"}]},ae={quantity:[{required:!0,message:"请输入出库数量",trigger:"blur"},{type:"number",min:1,message:"数量必须大于0",trigger:"blur"}],locationId:[{required:!0,message:"请选择库位",trigger:"change"}],reasonType:[{required:!0,message:"请选择出库类型",trigger:"change"}]},le=t((()=>K.typesTree)),te=t((()=>K.locations));g((async()=>{await Promise.all([K.fetchTypesTree(),K.fetchLocations()]),se()}));const se=async()=>{s.value=!0;try{const e=await R({pageIndex:H.pageIndex,pageSize:H.pageSize,name:H.name||void 0,code:H.code||void 0,typeId:H.typeId||void 0,stockStatus:H.stockStatus||void 0,sortBy:H.sortBy||void 0,sortOrder:H.sortOrder||void 0});e.success?(_.value=e.data.items,w.value=e.data.totalCount):h.error(e.message||"获取备件列表失败")}catch(e){h.error("获取备件列表失败")}finally{s.value=!1}},ue=()=>{H.pageIndex=1,se()},de=()=>{H.name="",H.code="",H.typeId="",H.stockStatus="",H.sortBy="",H.sortOrder="",ue()},re=e=>{e.prop&&e.order?(H.sortBy=e.prop,H.sortOrder="ascending"===e.order?"asc":"desc"):(H.sortBy="",H.sortOrder=""),se()},oe=e=>{H.pageSize=e,se()},ne=e=>{H.pageIndex=e,se()},ie=e=>e.stockQuantity<=e.minStock?"stock-danger":e.stockQuantity<=e.warningThreshold?"stock-warning":"stock-normal",ce=()=>{V.value="add",_e(),k.value=!0},me=e=>{V.value="edit",Object.keys(W).forEach((a=>{W[a]=e[a]})),k.value=!0},pe=e=>{Q.value=e,Y.partId=e.id,Y.locationId=e.locationId,Y.quantity=1,x.value=!0},ve=e=>{Q.value=e,X.partId=e.id,X.locationId=e.locationId,X.quantity=1,X.relatedAssetId=null,X.relatedFaultId=null,j.value=!0},fe=async()=>{if(Q.value.id){L.value=!0;try{const e=await M({sparePartId:Q.value.id,pageIndex:1,pageSize:10});e.success?E.value=e.data.items||[]:E.value=[]}catch(e){E.value=[]}finally{L.value=!1}}},ye=e=>e.stockQuantity<=e.minStock?"库存不足":e.stockQuantity<=e.warningThreshold?"库存预警":"库存正常",be=e=>{const a=Math.max(e.stockQuantity,2*e.warningThreshold);return Math.min(e.stockQuantity/a*100,100)},he=e=>{const a=Math.max(e.stockQuantity,2*e.warningThreshold);return e.minStock/a*100},ge=e=>{const a=Math.max(e.stockQuantity,2*e.warningThreshold);return e.warningThreshold/a*100},_e=()=>{G.value=!1,"add"===V.value&&Object.keys(W).forEach((e=>{W[e]="warningThreshold"===e?10:"minStock"===e?5:"initialStock"===e||"stockAdjustment"===e?0:""})),I.value&&I.value.resetFields()},we=async()=>{I.value&&await I.value.validate((async e=>{if(!e)return!1;try{let e;if("edit"===V.value&&W.stockAdjustment&&0!==W.stockAdjustment){if(!W.adjustmentReason||""===W.adjustmentReason.trim())return void h.error("请输入库存调整原因");if(e=await B(W.id,W),!e.success)return void h.error(e.message||"更新备件信息失败");const a=await O({partId:W.id,adjustmentQuantity:W.stockAdjustment,locationId:W.locationId,reason:W.adjustmentReason,referenceNumber:`ADJ-${Date.now()}`});a.success?h.success("备件信息更新成功，库存调整完成"):h.warning(`备件信息更新成功，但库存调整失败: ${a.message}`)}else{if(e="add"===V.value?await F(W):await B(W.id,W),!e.success)return void h.error(e.message||("add"===V.value?"新增失败":"更新失败"));h.success("add"===V.value?"新增成功":"更新成功")}k.value=!1,se()}catch(a){h.error("add"===V.value?"新增失败":"更新失败")}}))},ke=async()=>{T.value&&await T.value.validate((async e=>{if(!e)return!1;try{const e=await P(Y);e.success?(h.success("入库成功"),x.value=!1,se()):h.error(e.message||"入库失败")}catch(a){h.error("入库失败")}}))},Ve=async()=>{z.value&&await z.value.validate((async e=>{if(!e)return!1;try{const e=await $(X);e.success?(h.success("出库成功"),j.value=!1,se()):h.error(e.message||"出库失败")}catch(a){h.error("出库失败")}}))};return(e,a)=>{const l=u("el-button"),t=u("el-input"),g=u("el-form-item"),q=u("el-option"),R=u("el-select"),B=u("el-form"),O=u("el-table-column"),F=u("el-table"),P=u("el-pagination"),$=u("el-input-number"),M=u("el-alert"),K=u("el-dialog"),se=u("el-tag"),Ie=u("el-descriptions-item"),Ce=u("el-descriptions"),xe=u("el-card"),Ue=N("loading");return r(),c("div",la,[n("div",ta,[a[51]||(a[51]=n("h2",null,"备件台账管理",-1)),i(l,{type:"primary",onClick:ce},{default:o((()=>a[50]||(a[50]=[y("新增备件")]))),_:1})]),n("div",sa,[i(B,{inline:!0,model:H,class:"demo-form-inline"},{default:o((()=>[i(g,{label:"备件名称"},{default:o((()=>[i(t,{modelValue:H.name,"onUpdate:modelValue":a[0]||(a[0]=e=>H.name=e),placeholder:"备件名称",clearable:"",onKeyup:S(ue,["enter"])},null,8,["modelValue"])])),_:1}),i(g,{label:"备件编号"},{default:o((()=>[i(t,{modelValue:H.code,"onUpdate:modelValue":a[1]||(a[1]=e=>H.code=e),placeholder:"备件编号",clearable:"",onKeyup:S(ue,["enter"])},null,8,["modelValue"])])),_:1}),i(g,{label:"备件类型"},{default:o((()=>[i(R,{modelValue:H.typeId,"onUpdate:modelValue":a[2]||(a[2]=e=>H.typeId=e),placeholder:"备件类型",clearable:""},{default:o((()=>[(r(!0),c(m,null,p(le.value,(e=>(r(),d(q,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),i(g,{label:"库存状态"},{default:o((()=>[i(R,{modelValue:H.stockStatus,"onUpdate:modelValue":a[3]||(a[3]=e=>H.stockStatus=e),placeholder:"库存状态",clearable:""},{default:o((()=>[i(q,{label:"正常",value:"normal"}),i(q,{label:"预警",value:"warning"}),i(q,{label:"不足",value:"danger"})])),_:1},8,["modelValue"])])),_:1}),i(g,null,{default:o((()=>[i(l,{type:"primary",onClick:ue},{default:o((()=>a[52]||(a[52]=[y("查询")]))),_:1}),i(l,{onClick:de},{default:o((()=>a[53]||(a[53]=[y("重置")]))),_:1})])),_:1})])),_:1},8,["model"])]),U((r(),d(F,{data:_.value,border:"",style:{width:"100%"},onSortChange:re},{default:o((()=>[i(O,{prop:"code",label:"备件编号",width:"120",sortable:"custom"}),i(O,{prop:"materialNumber",label:"物料编号",width:"120",sortable:"custom"}),i(O,{prop:"name",label:"备件名称",width:"150",sortable:"custom"}),i(O,{prop:"typeName",label:"备件类型",width:"120"}),i(O,{prop:"specification",label:"规格型号",width:"120"}),i(O,{prop:"brand",label:"品牌",width:"100"}),i(O,{prop:"stockQuantity",label:"库存数量",width:"100",sortable:"custom"},{default:o((e=>[n("span",{class:b(ie(e.row))},f(e.row.stockQuantity)+" "+f(e.row.unit),3)])),_:1}),i(O,{prop:"minStock",label:"最小库存",width:"100"}),i(O,{prop:"locationName",label:"库位",width:"120"}),i(O,{prop:"price",label:"单价(元)",width:"100"},{default:o((e=>[y(f(e.row.price?e.row.price.toFixed(2):"-"),1)])),_:1}),i(O,{label:"操作",width:"280",fixed:"right"},{default:o((e=>[i(l,{type:"info",size:"small",onClick:a=>{return l=e.row,Q.value=l,D.value=!0,void fe();var l}},{default:o((()=>a[54]||(a[54]=[y("详情")]))),_:2},1032,["onClick"]),i(l,{type:"primary",size:"small",onClick:a=>me(e.row)},{default:o((()=>a[55]||(a[55]=[y("编辑")]))),_:2},1032,["onClick"]),i(l,{type:"warning",size:"small",onClick:a=>{return l=e.row,void h.info(`申请返厂：${l.name}`);var l}},{default:o((()=>a[56]||(a[56]=[y("返厂")]))),_:2},1032,["onClick"]),i(l,{type:"success",size:"small",onClick:a=>pe(e.row)},{default:o((()=>a[57]||(a[57]=[y("入库")]))),_:2},1032,["onClick"]),i(l,{type:"danger",size:"small",onClick:a=>ve(e.row)},{default:o((()=>a[58]||(a[58]=[y("出库")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"])),[[Ue,s.value]]),n("div",ua,[i(P,{currentPage:H.pageIndex,"onUpdate:currentPage":a[4]||(a[4]=e=>H.pageIndex=e),"page-size":H.pageSize,"onUpdate:pageSize":a[5]||(a[5]=e=>H.pageSize=e),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:w.value,onSizeChange:oe,onCurrentChange:ne},null,8,["currentPage","page-size","total"])]),i(K,{modelValue:k.value,"onUpdate:modelValue":a[23]||(a[23]=e=>k.value=e),title:"add"===V.value?"新增备件":"编辑备件",width:"650px",onClose:_e},{footer:o((()=>[n("div",na,[i(l,{onClick:a[22]||(a[22]=e=>k.value=!1)},{default:o((()=>a[59]||(a[59]=[y("取消")]))),_:1}),i(l,{type:"primary",onClick:we},{default:o((()=>a[60]||(a[60]=[y("确定")]))),_:1})])])),default:o((()=>[i(B,{ref_key:"formRef",ref:I,model:W,rules:Z,"label-width":"100px",style:{"max-height":"500px","overflow-y":"auto"}},{default:o((()=>[i(g,{label:"备件编号",prop:"code"},{default:o((()=>[i(t,{modelValue:W.code,"onUpdate:modelValue":a[6]||(a[6]=e=>W.code=e),placeholder:"请输入备件编号"},null,8,["modelValue"])])),_:1}),i(g,{label:"物料编号",prop:"materialNumber"},{default:o((()=>[i(t,{modelValue:W.materialNumber,"onUpdate:modelValue":a[7]||(a[7]=e=>W.materialNumber=e),placeholder:"请输入物料编号"},null,8,["modelValue"])])),_:1}),i(g,{label:"备件名称",prop:"name"},{default:o((()=>[i(t,{modelValue:W.name,"onUpdate:modelValue":a[8]||(a[8]=e=>W.name=e),placeholder:"请输入备件名称"},null,8,["modelValue"])])),_:1}),i(g,{label:"备件类型",prop:"typeId"},{default:o((()=>[i(R,{modelValue:W.typeId,"onUpdate:modelValue":a[9]||(a[9]=e=>W.typeId=e),placeholder:"请选择备件类型",style:{width:"100%"}},{default:o((()=>[(r(!0),c(m,null,p(le.value,(e=>(r(),d(q,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),i(g,{label:"规格型号",prop:"specification"},{default:o((()=>[i(t,{modelValue:W.specification,"onUpdate:modelValue":a[10]||(a[10]=e=>W.specification=e),placeholder:"请输入规格型号"},null,8,["modelValue"])])),_:1}),i(g,{label:"品牌",prop:"brand"},{default:o((()=>[i(t,{modelValue:W.brand,"onUpdate:modelValue":a[11]||(a[11]=e=>W.brand=e),placeholder:"请输入品牌"},null,8,["modelValue"])])),_:1}),i(g,{label:"单位",prop:"unit"},{default:o((()=>[i(t,{modelValue:W.unit,"onUpdate:modelValue":a[12]||(a[12]=e=>W.unit=e),placeholder:"请输入单位"},null,8,["modelValue"])])),_:1}),"add"===V.value?(r(),d(g,{key:0,label:"初始库存",prop:"initialStock"},{default:o((()=>[i($,{modelValue:W.initialStock,"onUpdate:modelValue":a[13]||(a[13]=e=>W.initialStock=e),min:0,placeholder:"请输入初始库存"},null,8,["modelValue"])])),_:1})):C("",!0),"edit"===V.value?(r(),d(g,{key:1,label:"当前库存"},{default:o((()=>[n("div",da,[n("span",ra,f(W.stockQuantity||0)+" "+f(W.unit),1),J.value?(r(),d(l,{key:0,type:"text",size:"small",onClick:a[14]||(a[14]=e=>G.value=!G.value)},{default:o((()=>[y(f(G.value?"取消调整":"库存调整"),1)])),_:1})):C("",!0)]),G.value&&J.value?(r(),c("div",oa,[i(M,{title:"库存调整",type:"warning",description:"库存调整将生成调整记录，请谨慎操作。建议通过入库/出库操作来管理库存。",closable:!1,style:{margin:"10px 0"}}),i(g,{label:"调整数量",prop:"stockAdjustment"},{default:o((()=>[i($,{modelValue:W.stockAdjustment,"onUpdate:modelValue":a[15]||(a[15]=e=>W.stockAdjustment=e),placeholder:"正数为增加，负数为减少",precision:0},null,8,["modelValue"])])),_:1}),i(g,{label:"调整原因",prop:"adjustmentReason"},{default:o((()=>[i(t,{modelValue:W.adjustmentReason,"onUpdate:modelValue":a[16]||(a[16]=e=>W.adjustmentReason=e),type:"textarea",placeholder:"请输入调整原因",rows:2},null,8,["modelValue"])])),_:1})])):C("",!0)])),_:1})):C("",!0),i(g,{label:"预警阈值",prop:"warningThreshold"},{default:o((()=>[i($,{modelValue:W.warningThreshold,"onUpdate:modelValue":a[17]||(a[17]=e=>W.warningThreshold=e),min:0,placeholder:"请输入预警阈值"},null,8,["modelValue"])])),_:1}),i(g,{label:"最小库存",prop:"minStock"},{default:o((()=>[i($,{modelValue:W.minStock,"onUpdate:modelValue":a[18]||(a[18]=e=>W.minStock=e),min:0,placeholder:"请输入最小库存"},null,8,["modelValue"])])),_:1}),i(g,{label:"库位",prop:"locationId"},{default:o((()=>[i(R,{modelValue:W.locationId,"onUpdate:modelValue":a[19]||(a[19]=e=>W.locationId=e),placeholder:"请选择库位",style:{width:"100%"}},{default:o((()=>[(r(!0),c(m,null,p(te.value,(e=>(r(),d(q,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),i(g,{label:"价格(元)",prop:"price"},{default:o((()=>[i($,{modelValue:W.price,"onUpdate:modelValue":a[20]||(a[20]=e=>W.price=e),min:0,precision:2,placeholder:"请输入价格"},null,8,["modelValue"])])),_:1}),i(g,{label:"备注",prop:"remarks"},{default:o((()=>[i(t,{modelValue:W.remarks,"onUpdate:modelValue":a[21]||(a[21]=e=>W.remarks=e),type:"textarea",placeholder:"请输入备注"},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue","title"]),i(K,{modelValue:x.value,"onUpdate:modelValue":a[31]||(a[31]=e=>x.value=e),title:"备件入库",width:"550px"},{footer:o((()=>[n("div",ia,[i(l,{onClick:a[30]||(a[30]=e=>x.value=!1)},{default:o((()=>a[61]||(a[61]=[y("取消")]))),_:1}),i(l,{type:"primary",onClick:ke},{default:o((()=>a[62]||(a[62]=[y("确定")]))),_:1})])])),default:o((()=>[i(B,{ref_key:"inboundFormRef",ref:T,model:Y,rules:ee,"label-width":"100px"},{default:o((()=>[i(g,{label:"备件名称"},{default:o((()=>[i(t,{modelValue:Q.value.name,"onUpdate:modelValue":a[24]||(a[24]=e=>Q.value.name=e),disabled:""},null,8,["modelValue"])])),_:1}),i(g,{label:"当前库存"},{default:o((()=>[i(t,{value:`${Q.value.stockQuantity} ${Q.value.unit||""}`,disabled:""},null,8,["value"])])),_:1}),i(g,{label:"入库数量",prop:"quantity"},{default:o((()=>[i($,{modelValue:Y.quantity,"onUpdate:modelValue":a[25]||(a[25]=e=>Y.quantity=e),min:1},null,8,["modelValue"])])),_:1}),i(g,{label:"库位",prop:"locationId"},{default:o((()=>[i(R,{modelValue:Y.locationId,"onUpdate:modelValue":a[26]||(a[26]=e=>Y.locationId=e),placeholder:"请选择库位"},{default:o((()=>[(r(!0),c(m,null,p(te.value,(e=>(r(),d(q,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),i(g,{label:"入库类型",prop:"reasonType"},{default:o((()=>[i(R,{modelValue:Y.reasonType,"onUpdate:modelValue":a[27]||(a[27]=e=>Y.reasonType=e),placeholder:"请选择入库类型"},{default:o((()=>[i(q,{label:"采购入库",value:1}),i(q,{label:"退回入库",value:2}),i(q,{label:"盘点调整",value:5})])),_:1},8,["modelValue"])])),_:1}),i(g,{label:"关联单号",prop:"referenceNumber"},{default:o((()=>[i(t,{modelValue:Y.referenceNumber,"onUpdate:modelValue":a[28]||(a[28]=e=>Y.referenceNumber=e),placeholder:"请输入关联单号"},null,8,["modelValue"])])),_:1}),i(g,{label:"备注",prop:"remarks"},{default:o((()=>[i(t,{modelValue:Y.remarks,"onUpdate:modelValue":a[29]||(a[29]=e=>Y.remarks=e),type:"textarea",placeholder:"请输入备注"},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue"]),i(K,{modelValue:j.value,"onUpdate:modelValue":a[41]||(a[41]=e=>j.value=e),title:"备件出库",width:"550px"},{footer:o((()=>[n("div",ca,[i(l,{onClick:a[40]||(a[40]=e=>j.value=!1)},{default:o((()=>a[63]||(a[63]=[y("取消")]))),_:1}),i(l,{type:"primary",onClick:Ve},{default:o((()=>a[64]||(a[64]=[y("确定")]))),_:1})])])),default:o((()=>[i(B,{ref_key:"outboundFormRef",ref:z,model:X,rules:ae,"label-width":"100px"},{default:o((()=>[i(g,{label:"备件名称"},{default:o((()=>[i(t,{modelValue:Q.value.name,"onUpdate:modelValue":a[32]||(a[32]=e=>Q.value.name=e),disabled:""},null,8,["modelValue"])])),_:1}),i(g,{label:"当前库存"},{default:o((()=>[i(t,{value:`${Q.value.stockQuantity} ${Q.value.unit||""}`,disabled:""},null,8,["value"])])),_:1}),i(g,{label:"出库数量",prop:"quantity"},{default:o((()=>[i($,{modelValue:X.quantity,"onUpdate:modelValue":a[33]||(a[33]=e=>X.quantity=e),min:1,max:Q.value.stockQuantity},null,8,["modelValue","max"])])),_:1}),i(g,{label:"库位",prop:"locationId"},{default:o((()=>[i(R,{modelValue:X.locationId,"onUpdate:modelValue":a[34]||(a[34]=e=>X.locationId=e),placeholder:"请选择库位"},{default:o((()=>[(r(!0),c(m,null,p(te.value,(e=>(r(),d(q,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),i(g,{label:"出库类型",prop:"reasonType"},{default:o((()=>[i(R,{modelValue:X.reasonType,"onUpdate:modelValue":a[35]||(a[35]=e=>X.reasonType=e),placeholder:"请选择出库类型"},{default:o((()=>[i(q,{label:"领用出库",value:3}),i(q,{label:"报废出库",value:4}),i(q,{label:"盘点调整",value:5})])),_:1},8,["modelValue"])])),_:1}),i(g,{label:"关联单号",prop:"referenceNumber"},{default:o((()=>[i(t,{modelValue:X.referenceNumber,"onUpdate:modelValue":a[36]||(a[36]=e=>X.referenceNumber=e),placeholder:"请输入关联单号"},null,8,["modelValue"])])),_:1}),i(g,{label:"关联资产",prop:"relatedAssetId"},{default:o((()=>[i(t,{modelValue:X.relatedAssetId,"onUpdate:modelValue":a[37]||(a[37]=e=>X.relatedAssetId=e),placeholder:"请输入关联资产ID"},null,8,["modelValue"])])),_:1}),i(g,{label:"关联故障",prop:"relatedFaultId"},{default:o((()=>[i(t,{modelValue:X.relatedFaultId,"onUpdate:modelValue":a[38]||(a[38]=e=>X.relatedFaultId=e),placeholder:"请输入关联故障ID"},null,8,["modelValue"])])),_:1}),i(g,{label:"备注",prop:"remarks"},{default:o((()=>[i(t,{modelValue:X.remarks,"onUpdate:modelValue":a[39]||(a[39]=e=>X.remarks=e),type:"textarea",placeholder:"请输入备注"},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue"]),i(K,{modelValue:D.value,"onUpdate:modelValue":a[47]||(a[47]=e=>D.value=e),title:"备件详情",width:"800px"},{footer:o((()=>[n("div",Va,[i(l,{onClick:a[42]||(a[42]=e=>D.value=!1)},{default:o((()=>a[71]||(a[71]=[y("关闭")]))),_:1}),i(l,{type:"primary",onClick:a[43]||(a[43]=e=>me(Q.value))},{default:o((()=>a[72]||(a[72]=[y("编辑")]))),_:1}),i(l,{type:"success",onClick:a[44]||(a[44]=e=>pe(Q.value))},{default:o((()=>a[73]||(a[73]=[y("入库")]))),_:1}),i(l,{type:"warning",onClick:a[45]||(a[45]=e=>ve(Q.value))},{default:o((()=>a[74]||(a[74]=[y("出库")]))),_:1}),i(l,{type:"info",onClick:a[46]||(a[46]=e=>{return a=Q.value,Q.value=a,void(A.value=!0);var a})},{default:o((()=>a[75]||(a[75]=[y("状态管理")]))),_:1})])])),default:o((()=>[Q.value?(r(),c("div",ma,[i(xe,{class:"detail-card",shadow:"never"},{header:o((()=>a[65]||(a[65]=[n("div",{class:"card-header"},[n("span",{class:"card-title"},"📋 基本信息")],-1)]))),default:o((()=>[i(Ce,{column:2,border:""},{default:o((()=>[i(Ie,{label:"备件编号"},{default:o((()=>[i(se,{type:"primary"},{default:o((()=>[y(f(Q.value.code),1)])),_:1})])),_:1}),i(Ie,{label:"物料编号"},{default:o((()=>[y(f(Q.value.materialNumber||"-"),1)])),_:1}),i(Ie,{label:"备件名称"},{default:o((()=>[n("strong",null,f(Q.value.name),1)])),_:1}),i(Ie,{label:"备件类型"},{default:o((()=>[y(f(Q.value.typeName||"-"),1)])),_:1}),i(Ie,{label:"规格型号"},{default:o((()=>[y(f(Q.value.specification||"-"),1)])),_:1}),i(Ie,{label:"品牌"},{default:o((()=>[y(f(Q.value.brand||"-"),1)])),_:1}),i(Ie,{label:"单位"},{default:o((()=>[y(f(Q.value.unit||"-"),1)])),_:1}),i(Ie,{label:"库位"},{default:o((()=>[y(f(Q.value.locationName||"-"),1)])),_:1}),i(Ie,{label:"单价"},{default:o((()=>[n("span",pa,"¥"+f(Q.value.price?Q.value.price.toFixed(2):"0.00"),1)])),_:1}),i(Ie,{label:"备注"},{default:o((()=>[y(f(Q.value.remarks||"-"),1)])),_:1})])),_:1})])),_:1}),i(xe,{class:"detail-card",shadow:"never"},{header:o((()=>{return[n("div",va,[a[66]||(a[66]=n("span",{class:"card-title"},"📦 库存信息",-1)),i(se,{type:(e=Q.value,e.stockQuantity<=e.minStock?"danger":e.stockQuantity<=e.warningThreshold?"warning":"success"),size:"small"},{default:o((()=>[y(f(ye(Q.value)),1)])),_:1},8,["type"])])];var e})),default:o((()=>{return[i(Ce,{column:3,border:""},{default:o((()=>[i(Ie,{label:"当前库存"},{default:o((()=>[n("span",{class:b([ie(Q.value),"stock-number"])},f(Q.value.stockQuantity)+" "+f(Q.value.unit),3)])),_:1}),i(Ie,{label:"最小库存"},{default:o((()=>[n("span",fa,f(Q.value.minStock)+" "+f(Q.value.unit),1)])),_:1}),i(Ie,{label:"预警阈值"},{default:o((()=>[n("span",ya,f(Q.value.warningThreshold)+" "+f(Q.value.unit),1)])),_:1})])),_:1}),n("div",ba,[a[68]||(a[68]=n("div",{class:"chart-title"},"库存状态",-1)),n("div",ha,[n("div",ga,[n("div",{class:"bar-fill",style:v({width:be(Q.value)+"%",backgroundColor:(e=Q.value,e.stockQuantity<=e.minStock?"#f56c6c":e.stockQuantity<=e.warningThreshold?"#e6a23c":"#67c23a")})},null,4),n("div",_a,[n("div",{class:"marker min-marker",style:v({left:he(Q.value)+"%"}),title:"最小库存"},null,4),n("div",{class:"marker warning-marker",style:v({left:ge(Q.value)+"%"}),title:"预警阈值"},null,4)])]),n("div",wa,[a[67]||(a[67]=n("span",null,"0",-1)),n("span",null,f(Math.max(Q.value.stockQuantity,2*Q.value.warningThreshold)),1)])])])];var e})),_:1}),i(xe,{class:"detail-card",shadow:"never"},{header:o((()=>[n("div",ka,[a[70]||(a[70]=n("span",{class:"card-title"},"📝 最近操作记录",-1)),i(l,{size:"small",onClick:fe},{default:o((()=>a[69]||(a[69]=[y("刷新")]))),_:1})])])),default:o((()=>[U((r(),d(F,{data:E.value,size:"small","max-height":"300"},{default:o((()=>[i(O,{prop:"operationTime",label:"操作时间",width:"150"},{default:o((({row:e})=>{return[y(f((a=e.operationTime,a?new Date(a).toLocaleString("zh-CN"):"-")),1)];var a})),_:1}),i(O,{prop:"typeName",label:"操作类型",width:"100"},{default:o((({row:e})=>{return[i(se,{type:(a=e.type,1===a?"success":2===a?"danger":"info"),size:"small"},{default:o((()=>[y(f(e.typeName),1)])),_:2},1032,["type"])];var a})),_:1}),i(O,{prop:"quantity",label:"数量",width:"80",align:"center"},{default:o((({row:e})=>[n("span",{class:b(1===e.type?"text-success":"text-danger")},f(1===e.type?"+":"-")+f(Math.abs(e.quantity)),3)])),_:1}),i(O,{prop:"referenceNumber",label:"关联单号",width:"120"}),i(O,{prop:"operatorName",label:"操作人",width:"100"}),i(O,{prop:"remarks",label:"备注","min-width":"150"})])),_:1},8,["data"])),[[Ue,L.value]])])),_:1})])):C("",!0)])),_:1},8,["modelValue"]),i(K,{modelValue:A.value,"onUpdate:modelValue":a[49]||(a[49]=e=>A.value=e),title:"备件状态管理",width:"1200px",top:"5vh"},{footer:o((()=>[n("div",Ia,[i(l,{onClick:a[48]||(a[48]=e=>A.value=!1)},{default:o((()=>a[76]||(a[76]=[y("关闭")]))),_:1})])])),default:o((()=>[A.value&&Q.value.id?(r(),d(aa,{key:0,"part-id":Q.value.id},null,8,["part-id"])):C("",!0)])),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-f7574ce2"]]);export{Ca as default};
