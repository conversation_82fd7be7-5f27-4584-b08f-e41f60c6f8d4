# 位置分类查询API文档

## 基本信息

- **基础URL**: `/api/v2/locationdepartmentinheritance`
- **认证方式**: JWT Bearer <PERSON>ken
- **内容类型**: `application/json`
- **API版本**: V2

## 接口列表

### 1. 获取所有位置的部门继承信息

**接口地址**: `GET /api/v2/locationdepartmentinheritance`

**请求示例**:
```http
GET /api/v2/locationdepartmentinheritance
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**响应示例**:
```json
{
  "success": true,
  "data": [
    {
      "locationId": 1,
      "locationName": "生产车间A",
      "locationPath": "工厂 > 生产区 > 生产车间A",
      "directDepartmentId": 5,
      "directDepartmentName": "生产部",
      "inheritedDepartmentId": 5,
      "inheritedDepartmentName": "生产部",
      "effectiveDepartmentId": 5,
      "effectiveDepartmentName": "生产部",
      "assetCount": 15,
      "assets": [
        {
          "assetId": 101,
          "assetName": "生产设备A",
          "assetCode": "DEV001",
          "assetTypeName": "生产设备"
        }
      ]
    }
  ],
  "message": "成功获取 50 个位置的部门继承信息"
}
```

---

### 2. 获取指定位置的部门继承信息

**接口地址**: `GET /api/v2/locationdepartmentinheritance/{id}`

**路径参数**:
- `id` (integer, required): 位置ID

**请求示例**:
```http
GET /api/v2/locationdepartmentinheritance/1
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "locationId": 1,
    "locationName": "生产车间A",
    "locationPath": "工厂 > 生产区 > 生产车间A",
    "directDepartmentId": 5,
    "directDepartmentName": "生产部",
    "inheritedDepartmentId": 5,
    "inheritedDepartmentName": "生产部",
    "effectiveDepartmentId": 5,
    "effectiveDepartmentName": "生产部",
    "assetCount": 15,
    "assets": [...]
  },
  "message": "成功获取位置部门继承信息"
}
```

**错误响应**:
```json
{
  "success": false,
  "message": "未找到位置 999 的部门继承信息"
}
```

---

### 3. 获取部门位置统计信息

**接口地址**: `GET /api/v2/locationdepartmentinheritance/department-stats`

**请求示例**:
```http
GET /api/v2/locationdepartmentinheritance/department-stats
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**响应示例**:
```json
{
  "success": true,
  "data": [
    {
      "departmentId": 5,
      "departmentName": "生产部",
      "directLocationCount": 3,
      "inheritedLocationCount": 7,
      "totalLocationCount": 10,
      "directAssetCount": 25,
      "inheritedAssetCount": 40,
      "totalAssetCount": 65
    },
    {
      "departmentId": 6,
      "departmentName": "质检部",
      "directLocationCount": 2,
      "inheritedLocationCount": 5,
      "totalLocationCount": 7,
      "directAssetCount": 15,
      "inheritedAssetCount": 20,
      "totalAssetCount": 35
    }
  ],
  "message": "成功获取 2 个部门的位置统计信息"
}
```

---

### 4. 获取部门管理的所有位置

**接口地址**: `GET /api/v2/locationdepartmentinheritance/by-department/{departmentId}`

**路径参数**:
- `departmentId` (integer, required): 部门ID

**请求示例**:
```http
GET /api/v2/locationdepartmentinheritance/by-department/5
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**响应示例**:
```json
{
  "success": true,
  "data": [
    {
      "locationId": 1,
      "locationName": "生产车间A",
      "locationPath": "工厂 > 生产区 > 生产车间A",
      "effectiveDepartmentId": 5,
      "effectiveDepartmentName": "生产部",
      "assetCount": 15,
      "assets": [...]
    }
  ],
  "message": "成功获取部门 5 管理的 10 个位置信息"
}
```

---

### 5. 分类高级搜索 (POST)

**接口地址**: `POST /api/v2/locationdepartmentinheritance/search`

**请求体参数**:
```json
{
  "departmentId": 5,                    // 可选，部门ID筛选
  "locationType": 2,                    // 可选，位置类型（1=工厂,2=车间,3=工序,4=工位）
  "parentLocationId": 10,               // 可选，父级位置ID
  "assetTypeId": 3,                     // 可选，资产类型ID
  "assetStatus": "正常",                 // 可选，资产状态
  "includeChildren": true,              // 可选，是否包含子位置（默认true）
  "onlyWithAssets": true,               // 可选，是否只显示有资产的位置（默认false）
  "onlyDirectDepartment": false,        // 可选，是否只显示直接分配部门的位置（默认false）
  "keyword": "生产",                     // 可选，关键词搜索
  "sortBy": "assetCount",               // 可选，排序字段（默认locationPath）
  "sortDirection": "desc",              // 可选，排序方向（asc/desc，默认asc）
  "pageNumber": 1,                      // 可选，页码（默认1）
  "pageSize": 20                        // 可选，每页大小（默认20）
}
```

**请求示例**:
```http
POST /api/v2/locationdepartmentinheritance/search
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

{
  "departmentId": 5,
  "locationType": 2,
  "onlyWithAssets": true,
  "sortBy": "assetCount",
  "sortDirection": "desc",
  "pageNumber": 1,
  "pageSize": 20
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "locations": [
      {
        "locationId": 1,
        "locationName": "生产车间A",
        "locationPath": "工厂 > 生产区 > 生产车间A",
        "directDepartmentId": 5,
        "directDepartmentName": "生产部",
        "inheritedDepartmentId": 5,
        "inheritedDepartmentName": "生产部",
        "effectiveDepartmentId": 5,
        "effectiveDepartmentName": "生产部",
        "assetCount": 15,
        "assets": [...]
      }
    ],
    "totalCount": 150,
    "stats": {
      "byDepartment": [...],
      "byLocationType": [...],
      "byAssetType": [...],
      "byLocationLevel": [...]
    }
  },
  "message": "成功查询到 150 个位置，当前页 20 个"
}
```

---

### 6. 分类快速搜索 (GET)

**接口地址**: `GET /api/v2/locationdepartmentinheritance/search`

**查询参数**:
- `departmentId` (integer, optional): 部门ID
- `locationType` (integer, optional): 位置类型
- `parentLocationId` (integer, optional): 父位置ID
- `assetTypeId` (integer, optional): 资产类型ID
- `assetStatus` (string, optional): 资产状态
- `includeChildren` (boolean, optional): 是否包含子位置 (默认true)
- `onlyWithAssets` (boolean, optional): 是否只显示有资产的位置 (默认false)
- `onlyDirectDepartment` (boolean, optional): 是否只显示直接分配部门的位置 (默认false)
- `keyword` (string, optional): 关键词搜索
- `sortBy` (string, optional): 排序字段 (默认locationPath)
- `sortDirection` (string, optional): 排序方向 (默认asc)
- `pageNumber` (integer, optional): 页码 (默认1)
- `pageSize` (integer, optional): 每页大小 (默认20)

**请求示例**:
```http
GET /api/v2/locationdepartmentinheritance/search?departmentId=5&locationType=2&onlyWithAssets=true&keyword=生产&pageNumber=1&pageSize=20
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**响应格式**: 与POST搜索相同

---

### 7. 获取分类统计信息

**接口地址**: `GET /api/v2/locationdepartmentinheritance/classification-stats`

**请求示例**:
```http
GET /api/v2/locationdepartmentinheritance/classification-stats
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "byDepartment": [
      {
        "departmentId": 5,
        "departmentName": "生产部",
        "directLocationCount": 3,
        "inheritedLocationCount": 7,
        "totalLocationCount": 10,
        "directAssetCount": 25,
        "inheritedAssetCount": 40,
        "totalAssetCount": 65
      }
    ],
    "byLocationType": [
      {
        "locationType": 1,
        "locationTypeName": "工厂",
        "locationCount": 1,
        "assetCount": 0,
        "locationsWithDepartment": 0
      },
      {
        "locationType": 2,
        "locationTypeName": "车间",
        "locationCount": 15,
        "assetCount": 150,
        "locationsWithDepartment": 12
      }
    ],
    "byAssetType": [
      {
        "assetTypeId": 1,
        "assetTypeName": "生产设备",
        "assetCount": 80,
        "locationCount": 25,
        "departmentCount": 5
      },
      {
        "assetTypeId": null,
        "assetTypeName": "未分类",
        "assetCount": 20,
        "locationCount": 10,
        "departmentCount": 3
      }
    ],
    "byLocationLevel": [
      {
        "level": 0,
        "locationCount": 1,
        "assetCount": 0,
        "locationsWithDepartment": 0
      },
      {
        "level": 1,
        "locationCount": 5,
        "assetCount": 50,
        "locationsWithDepartment": 4
      }
    ]
  },
  "message": "成功获取位置分类统计信息"
}
```

## 数据模型

### LocationDepartmentInheritanceDto (位置部门继承信息)
```typescript
interface LocationDepartmentInheritanceDto {
  locationId: number;                    // 位置ID
  locationName: string;                  // 位置名称
  locationPath: string;                  // 位置路径
  directDepartmentId?: number;           // 直接分配的部门ID
  directDepartmentName?: string;         // 直接分配的部门名称
  inheritedDepartmentId?: number;        // 继承的部门ID
  inheritedDepartmentName?: string;      // 继承的部门名称
  effectiveDepartmentId?: number;        // 有效部门ID
  effectiveDepartmentName?: string;      // 有效部门名称
  assetCount: number;                    // 资产数量
  assets: AssetSummaryDto[];            // 资产摘要列表
}
```

### AssetSummaryDto (资产摘要)
```typescript
interface AssetSummaryDto {
  assetId: number;        // 资产ID
  assetName: string;      // 资产名称
  assetCode: string;      // 资产编码
  assetTypeName?: string; // 资产类型名称
}
```

### LocationClassificationQueryDto (查询参数)
```typescript
interface LocationClassificationQueryDto {
  departmentId?: number;           // 部门ID筛选
  locationType?: number;           // 位置类型筛选
  parentLocationId?: number;       // 父级位置ID筛选
  assetTypeId?: number;            // 资产类型ID筛选
  assetStatus?: string;            // 资产状态筛选
  includeChildren?: boolean;       // 是否包含子位置
  onlyWithAssets?: boolean;        // 是否只显示有资产的位置
  onlyDirectDepartment?: boolean;  // 是否只显示有直接部门分配的位置
  keyword?: string;                // 关键词搜索
  sortBy?: string;                 // 排序字段
  sortDirection?: string;          // 排序方向
  pageNumber?: number;             // 页码
  pageSize?: number;               // 每页大小
}
```

### LocationClassificationResultDto (查询结果)
```typescript
interface LocationClassificationResultDto {
  locations: LocationDepartmentInheritanceDto[];  // 位置列表
  totalCount: number;                             // 总记录数
  stats: LocationClassificationStatsDto;         // 分类统计信息
}
```

### 统计模型

#### LocationDepartmentStatsDto (部门统计)
```typescript
interface LocationDepartmentStatsDto {
  departmentId: number;           // 部门ID
  departmentName: string;         // 部门名称
  directLocationCount: number;    // 直接分配的位置数量
  inheritedLocationCount: number; // 继承的位置数量
  totalLocationCount: number;     // 总位置数量
  directAssetCount: number;       // 直接位置的资产数量
  inheritedAssetCount: number;    // 继承位置的资产数量
  totalAssetCount: number;        // 总资产数量
}
```

#### LocationTypeStatsDto (位置类型统计)
```typescript
interface LocationTypeStatsDto {
  locationType: number;             // 位置类型
  locationTypeName: string;         // 位置类型名称
  locationCount: number;            // 位置数量
  assetCount: number;               // 资产数量
  locationsWithDepartment: number;  // 有部门分配的位置数量
}
```

#### AssetTypeStatsDto (资产类型统计)
```typescript
interface AssetTypeStatsDto {
  assetTypeId?: number;    // 资产类型ID (null表示未分类)
  assetTypeName: string;   // 资产类型名称
  assetCount: number;      // 资产数量
  locationCount: number;   // 分布的位置数量
  departmentCount: number; // 涉及的部门数量
}
```

#### LocationLevelStatsDto (位置层级统计)
```typescript
interface LocationLevelStatsDto {
  level: number;                     // 层级深度
  locationCount: number;             // 该层级的位置数量
  assetCount: number;                // 该层级的资产数量
  locationsWithDepartment: number;   // 该层级有部门分配的位置数量
}
```

## 错误响应

### 标准错误格式
```json
{
  "success": false,
  "error": "错误类型",
  "message": "详细错误信息"
}
```

### 常见错误代码

| HTTP状态码 | 错误类型 | 说明 |
|-----------|----------|------|
| 400 | Bad Request | 请求参数错误 |
| 401 | Unauthorized | 未授权，需要登录 |
| 403 | Forbidden | 权限不足 |
| 404 | Not Found | 资源不存在 |
| 500 | Internal Server Error | 服务器内部错误 |

### 错误示例

#### 401 未授权
```json
{
  "success": false,
  "error": "Unauthorized",
  "message": "JWT token is missing or invalid"
}
```

#### 404 资源不存在
```json
{
  "success": false,
  "error": "Not Found",
  "message": "未找到位置 999 的部门继承信息"
}
```

#### 400 参数错误
```json
{
  "success": false,
  "error": "Bad Request",
  "message": "pageSize must be between 1 and 100"
}
```

#### 500 服务器错误
```json
{
  "success": false,
  "error": "Internal Server Error",
  "message": "数据库连接失败"
}
```

## 使用示例

### JavaScript/TypeScript
```javascript
// 基础查询
const getAllLocations = async () => {
  const response = await fetch('/api/v2/locationdepartmentinheritance', {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  });
  return response.json();
};

// 高级搜索
const searchLocations = async (query) => {
  const response = await fetch('/api/v2/locationdepartmentinheritance/search', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(query)
  });
  return response.json();
};

// 获取统计信息
const getStats = async () => {
  const response = await fetch('/api/v2/locationdepartmentinheritance/classification-stats', {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  });
  return response.json();
};
```

### cURL
```bash
# 获取所有位置信息
curl -X GET "http://localhost:5001/api/v2/locationdepartmentinheritance" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json"

# 高级搜索
curl -X POST "http://localhost:5001/api/v2/locationdepartmentinheritance/search" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "departmentId": 5,
    "locationType": 2,
    "onlyWithAssets": true,
    "pageSize": 20
  }'

# 快速搜索
curl -X GET "http://localhost:5001/api/v2/locationdepartmentinheritance/search?departmentId=5&onlyWithAssets=true" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 最佳实践

### 1. 分页查询
- 建议使用默认页面大小(20)，避免一次性查询大量数据
- 最大页面大小限制为100
- 使用totalCount进行分页导航

### 2. 条件组合
- 多个筛选条件使用AND逻辑组合
- includeChildren=true时会递归查询所有子位置
- onlyWithAssets=true可以过滤空位置，提高查询效率

### 3. 排序优化
- 默认按locationPath排序，保持层级结构
- 按assetCount排序可以快速找到资产集中的位置
- 按departmentName排序便于按部门分组

### 4. 缓存策略
- classification-stats接口结果可以缓存30分钟
- department-stats结果可以缓存15分钟
- 具体位置信息建议实时查询

### 5. 错误处理
- 始终检查response.success字段
- 根据HTTP状态码处理不同类型的错误
- 对于401错误，应引导用户重新登录