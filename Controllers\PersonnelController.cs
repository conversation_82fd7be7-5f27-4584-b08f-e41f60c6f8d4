// IT资产管理系统 - 人员控制器
// 文件路径: /Controllers/PersonnelController.cs
// 功能: 提供人员相关API

using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using ItAssetsSystem.Models.Entities;
using ItAssetsSystem.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using System.Threading.Tasks;

namespace ItAssetsSystem.Controllers
{
    /// <summary>
    /// 人员控制器
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class PersonnelController : ControllerBase
    {
        private readonly ILogger<PersonnelController> _logger;
        private readonly AppDbContext _context;

        public PersonnelController(ILogger<PersonnelController> logger, AppDbContext context)
        {
            _logger = logger;
            _context = context;
        }

        /// <summary>
        /// 获取人员列表
        /// </summary>
        /// <param name="departmentId">部门ID</param>
        /// <param name="keyword">关键词</param>
        /// <returns>人员列表</returns>
        [HttpGet]
        public async Task<IActionResult> GetAll(
            [FromQuery] int? departmentId = null,
            [FromQuery] string keyword = null)
        {
            _logger.LogInformation($"获取人员列表 - 参数: departmentId={departmentId}, keyword={keyword}");

            try
            {
                var query = _context.Personnel.AsQueryable();

                // 应用筛选条件
                if (departmentId.HasValue)
                {
                    query = query.Where(p => p.DepartmentId == departmentId.Value);
                }

                if (!string.IsNullOrEmpty(keyword))
                {
                    query = query.Where(p => p.Name.Contains(keyword) || 
                                           p.EmployeeCode.Contains(keyword) || 
                                           p.Contact.Contains(keyword));
                }

                // 加载部门信息
                query = query.Include(p => p.Department);

                // 执行查询
                var personnel = await query
                    .OrderBy(p => p.Name)
                    .Select(p => new
                    {
                        id = p.Id,
                        name = p.Name,
                        position = p.Position,
                        contact = p.Contact,
                        departmentId = p.DepartmentId,
                        departmentName = p.Department != null ? p.Department.Name : null,
                        employeeCode = p.EmployeeCode,
                        createdAt = p.CreatedAt,
                        updatedAt = p.UpdatedAt
                    })
                    .ToListAsync();

                _logger.LogInformation($"查询到 {personnel.Count} 个人员记录");

                return Ok(new { success = true, data = personnel });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取人员列表出错");
                return StatusCode(500, new { success = false, message = "获取人员列表出错: " + ex.Message });
            }
        }

        /// <summary>
        /// 获取人员详情
        /// </summary>
        /// <param name="id">人员ID</param>
        /// <returns>人员详情</returns>
        [HttpGet("{id}")]
        public async Task<IActionResult> GetById(int id)
        {
            _logger.LogInformation($"获取人员详情，ID: {id}");

            try
            {
                var personnel = await _context.Personnel
                    .Include(p => p.Department)
                    .FirstOrDefaultAsync(p => p.Id == id);

                if (personnel == null)
                {
                    return NotFound(new { success = false, message = $"未找到ID为{id}的人员" });
                }

                var result = new
                {
                    id = personnel.Id,
                    name = personnel.Name,
                    position = personnel.Position,
                    contact = personnel.Contact,
                    departmentId = personnel.DepartmentId,
                    departmentName = personnel.Department?.Name,
                    employeeCode = personnel.EmployeeCode,
                    createdAt = personnel.CreatedAt,
                    updatedAt = personnel.UpdatedAt
                };

                return Ok(new { success = true, data = result });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取人员详情出错，ID: {id}");
                return StatusCode(500, new { success = false, message = $"获取人员详情出错: " + ex.Message });
            }
        }

        /// <summary>
        /// 创建人员
        /// </summary>
        /// <param name="request">创建请求</param>
        /// <returns>创建结果</returns>
        [HttpPost]
        public async Task<IActionResult> Create([FromBody] PersonnelRequest request)
        {
            if (request == null)
            {
                return BadRequest(new { success = false, message = "请求数据不能为空" });
            }

            _logger.LogInformation($"创建人员: {request.Name}, 工号: {request.EmployeeCode}");

            try
            {
                // 检查部门是否存在
                if (request.DepartmentId.HasValue)
                {
                    var departmentExists = await _context.Departments.AnyAsync(d => d.Id == request.DepartmentId.Value);
                    if (!departmentExists)
                    {
                        return BadRequest(new { success = false, message = $"部门ID {request.DepartmentId.Value} 不存在" });
                    }
                }

                // 检查工号是否唯一
                if (!string.IsNullOrEmpty(request.EmployeeCode))
                {
                    var exists = await _context.Personnel.AnyAsync(p => p.EmployeeCode == request.EmployeeCode);
                    if (exists)
                    {
                        return BadRequest(new { success = false, message = $"工号 {request.EmployeeCode} 已存在" });
                    }
                }

                // 创建人员实体
                var personnel = new Personnel
                {
                    Name = request.Name,
                    Position = request.Position,
                    Contact = request.Contact,
                    DepartmentId = request.DepartmentId,
                    EmployeeCode = request.EmployeeCode,
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now
                };

                _context.Personnel.Add(personnel);
                await _context.SaveChangesAsync();

                _logger.LogInformation($"人员创建成功，ID: {personnel.Id}");

                return Ok(new { success = true, data = personnel.Id, message = "创建成功" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建人员出错");
                return StatusCode(500, new { success = false, message = "创建人员出错: " + ex.Message });
            }
        }

        /// <summary>
        /// 更新人员
        /// </summary>
        /// <param name="id">人员ID</param>
        /// <param name="request">更新请求</param>
        /// <returns>更新结果</returns>
        [HttpPut("{id}")]
        public async Task<IActionResult> Update(int id, [FromBody] PersonnelRequest request)
        {
            if (request == null)
            {
                return BadRequest(new { success = false, message = "请求数据不能为空" });
            }

            _logger.LogInformation($"更新人员，ID: {id}");

            try
            {
                var personnel = await _context.Personnel.FindAsync(id);
                if (personnel == null)
                {
                    return NotFound(new { success = false, message = $"未找到ID为{id}的人员" });
                }

                // 检查部门是否存在
                if (request.DepartmentId.HasValue)
                {
                    var departmentExists = await _context.Departments.AnyAsync(d => d.Id == request.DepartmentId.Value);
                    if (!departmentExists)
                    {
                        return BadRequest(new { success = false, message = $"部门ID {request.DepartmentId.Value} 不存在" });
                    }
                }

                // 检查工号是否唯一（排除自身）
                if (!string.IsNullOrEmpty(request.EmployeeCode) && request.EmployeeCode != personnel.EmployeeCode)
                {
                    var exists = await _context.Personnel.AnyAsync(p => p.EmployeeCode == request.EmployeeCode && p.Id != id);
                    if (exists)
                    {
                        return BadRequest(new { success = false, message = $"工号 {request.EmployeeCode} 已存在" });
                    }
                }

                // 更新人员信息
                personnel.Name = request.Name;
                personnel.Position = request.Position;
                personnel.Contact = request.Contact;
                personnel.DepartmentId = request.DepartmentId;
                personnel.EmployeeCode = request.EmployeeCode;
                personnel.UpdatedAt = DateTime.Now;
                
                await _context.SaveChangesAsync();
                
                _logger.LogInformation($"人员更新成功，ID: {id}");
                
                return Ok(new { success = true, message = "更新成功" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"更新人员出错，ID: {id}");
                return StatusCode(500, new { success = false, message = "更新人员出错: " + ex.Message });
            }
        }

        /// <summary>
        /// 删除人员
        /// </summary>
        /// <param name="id">人员ID</param>
        /// <returns>删除结果</returns>
        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(int id)
        {
            _logger.LogInformation($"删除人员，ID: {id}");

            try
            {
                var personnel = await _context.Personnel.FindAsync(id);
                if (personnel == null)
                {
                    return NotFound(new { success = false, message = $"未找到ID为{id}的人员" });
                }

                // 检查是否有关联位置
                var hasLocationAssoc = await _context.LocationUsers.AnyAsync(lu => lu.PersonnelId == id);
                if (hasLocationAssoc)
                {
                    return BadRequest(new { success = false, message = "该人员已关联位置，无法删除" });
                }

                _context.Personnel.Remove(personnel);
                await _context.SaveChangesAsync();

                _logger.LogInformation($"人员删除成功，ID: {id}");

                return Ok(new { success = true, message = "删除成功" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"删除人员出错，ID: {id}");
                return StatusCode(500, new { success = false, message = "删除人员出错: " + ex.Message });
            }
        }

        /// <summary>
        /// 获取部门负责人列表
        /// </summary>
        /// <returns>部门负责人列表</returns>
        [HttpGet("department-managers")]
        public async Task<IActionResult> GetDepartmentManagers()
        {
            _logger.LogInformation("获取部门负责人列表");

            try
            {
                var managers = await _context.Personnel
                    .Where(p => p.IsDepartmentManager)
                    .Include(p => p.Department)
                    .OrderBy(p => p.Department.Name)
                    .ThenBy(p => p.Name)
                    .Select(p => new
                    {
                        id = p.Id,
                        name = p.Name,
                        departmentId = p.DepartmentId,
                        departmentName = p.Department != null ? p.Department.Name : null,
                        position = p.Position,
                        contact = p.Contact
                    })
                    .ToListAsync();

                _logger.LogInformation($"查询到 {managers.Count} 个部门负责人");

                return Ok(new { success = true, data = managers });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取部门负责人列表出错");
                return StatusCode(500, new { success = false, message = "获取部门负责人列表出错: " + ex.Message });
            }
        }
    }

    /// <summary>
    /// 人员请求模型
    /// </summary>
    public class PersonnelRequest
    {
        /// <summary>
        /// 姓名
        /// </summary>
        public string Name { get; set; }
        
        /// <summary>
        /// 职位
        /// </summary>
        public string Position { get; set; }
        
        /// <summary>
        /// 联系方式
        /// </summary>
        public string Contact { get; set; }
        
        /// <summary>
        /// 部门ID
        /// </summary>
        public int? DepartmentId { get; set; }
        
        /// <summary>
        /// 工号
        /// </summary>
        public string EmployeeCode { get; set; }
    }
} 