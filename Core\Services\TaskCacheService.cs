// File: Core/Services/TaskCacheService.cs
// Description: 任务查询缓存服务

#nullable enable
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using ItAssetsSystem.Application.Features.Tasks.Dtos;
using ItAssetsSystem.Application.Common.Dtos;

namespace ItAssetsSystem.Core.Services
{
    /// <summary>
    /// 任务查询缓存服务接口
    /// </summary>
    public interface ITaskCacheService
    {
        Task<ApiResponse<PaginatedResult<TaskDto>>?> GetCachedTasksAsync(string cacheKey);
        Task SetCachedTasksAsync(string cacheKey, ApiResponse<PaginatedResult<TaskDto>> tasks, TimeSpan? expiration = null);
        Task InvalidateTaskCacheAsync(long? taskId = null);
        string GenerateCacheKey(TaskQueryParametersDto parameters);
    }

    /// <summary>
    /// 任务查询缓存服务实现
    /// </summary>
    public class TaskCacheService : ITaskCacheService
    {
        private readonly IMemoryCache _memoryCache;
        private readonly ILogger<TaskCacheService> _logger;
        
        // 缓存键前缀
        private const string TASK_LIST_CACHE_PREFIX = "tasks_list:";
        private const string TASK_DETAIL_CACHE_PREFIX = "task_detail:";
        
        // 默认缓存时间
        private static readonly TimeSpan DefaultCacheExpiration = TimeSpan.FromMinutes(5);
        private static readonly TimeSpan DetailCacheExpiration = TimeSpan.FromMinutes(10);
        
        // 存储所有缓存键，便于批量清除
        private static readonly HashSet<string> _cacheKeys = new HashSet<string>();
        private static readonly object _cacheKeysLock = new object();

        public TaskCacheService(IMemoryCache memoryCache, ILogger<TaskCacheService> logger)
        {
            _memoryCache = memoryCache ?? throw new ArgumentNullException(nameof(memoryCache));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// 获取缓存的任务列表
        /// </summary>
        public Task<ApiResponse<PaginatedResult<TaskDto>>?> GetCachedTasksAsync(string cacheKey)
        {
            try
            {
                if (_memoryCache.TryGetValue(cacheKey, out ApiResponse<PaginatedResult<TaskDto>>? cachedResult))
                {
                    _logger.LogDebug("命中任务列表缓存: {CacheKey}", cacheKey);
                    return Task.FromResult(cachedResult);
                }
                
                _logger.LogDebug("未命中任务列表缓存: {CacheKey}", cacheKey);
                return Task.FromResult<ApiResponse<PaginatedResult<TaskDto>>?>(null);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "获取缓存任务列表失败: {CacheKey}", cacheKey);
                return Task.FromResult<ApiResponse<PaginatedResult<TaskDto>>?>(null);
            }
        }

        /// <summary>
        /// 设置任务列表缓存
        /// </summary>
        public Task SetCachedTasksAsync(string cacheKey, ApiResponse<PaginatedResult<TaskDto>> tasks, TimeSpan? expiration = null)
        {
            try
            {
                var options = new MemoryCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = expiration ?? DefaultCacheExpiration,
                    SlidingExpiration = TimeSpan.FromMinutes(2), // 2分钟滑动过期
                    Priority = CacheItemPriority.Normal,
                    PostEvictionCallbacks = 
                    {
                        new PostEvictionCallbackRegistration
                        {
                            EvictionCallback = (key, value, reason, state) =>
                            {
                                // 缓存被移除时，从追踪列表中删除
                                lock (_cacheKeysLock)
                                {
                                    _cacheKeys.Remove(key.ToString() ?? "");
                                }
                            }
                        }
                    }
                };

                _memoryCache.Set(cacheKey, tasks, options);
                
                // 追踪缓存键
                lock (_cacheKeysLock)
                {
                    _cacheKeys.Add(cacheKey);
                }
                
                _logger.LogDebug("设置任务列表缓存: {CacheKey}, 过期时间: {Expiration}", 
                    cacheKey, expiration ?? DefaultCacheExpiration);
                return Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "设置任务列表缓存失败: {CacheKey}", cacheKey);
                return Task.CompletedTask;
            }
        }

        /// <summary>
        /// 生成缓存键
        /// </summary>
        public string GenerateCacheKey(TaskQueryParametersDto parameters)
        {
            try
            {
                // 基于查询参数生成唯一缓存键
                var keyParts = new List<string>
                {
                    $"page:{parameters.PageNumber}",
                    $"size:{parameters.PageSize}",
                    $"status:{parameters.Status ?? "all"}",
                    $"priority:{parameters.Priority ?? "all"}",
                    $"type:{parameters.TaskType ?? "all"}",
                    $"assignee:{parameters.AssigneeUserId?.ToString() ?? "all"}",
                    $"creator:{parameters.CreatorUserId?.ToString() ?? "all"}",
                    $"asset:{parameters.AssetId?.ToString() ?? "all"}",
                    $"location:{parameters.LocationId?.ToString() ?? "all"}",
                    $"parent:{parameters.ParentTaskId?.ToString() ?? "all"}",
                    $"project:{parameters.ProjectId?.ToString() ?? "all"}",
                    $"search:{parameters.SearchTerm ?? "none"}",
                    $"sort:{parameters.SortBy ?? "default"}:{parameters.SortDirection ?? "desc"}",
                    $"from:{parameters.FromDate?.ToString("yyyyMMdd") ?? "none"}",
                    $"to:{parameters.ToDate?.ToString("yyyyMMdd") ?? "none"}",
                    $"deleted:{parameters.IncludeDeleted}"
                };

                var cacheKey = TASK_LIST_CACHE_PREFIX + string.Join("_", keyParts);
                return cacheKey;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "生成缓存键失败，使用默认键");
                return TASK_LIST_CACHE_PREFIX + "default_" + DateTime.Now.Ticks;
            }
        }

        /// <summary>
        /// 清除任务相关缓存
        /// </summary>
        public Task InvalidateTaskCacheAsync(long? taskId = null)
        {
            try
            {
                if (taskId.HasValue)
                {
                    // 清除特定任务的详情缓存
                    var detailCacheKey = TASK_DETAIL_CACHE_PREFIX + taskId.Value;
                    _memoryCache.Remove(detailCacheKey);
                    _logger.LogDebug("清除任务详情缓存: {TaskId}", taskId);
                }

                // 使用追踪的缓存键进行精确清除
                List<string> keysToRemove;
                lock (_cacheKeysLock)
                {
                    keysToRemove = _cacheKeys.Where(key => key.StartsWith(TASK_LIST_CACHE_PREFIX)).ToList();
                }

                foreach (var key in keysToRemove)
                {
                    _memoryCache.Remove(key);
                    _logger.LogDebug("清除任务列表缓存: {CacheKey}", key);
                }

                // 更新追踪列表
                lock (_cacheKeysLock)
                {
                    foreach (var key in keysToRemove)
                    {
                        _cacheKeys.Remove(key);
                    }
                }
                
                _logger.LogInformation("任务缓存清除完成: 清除{Count}个任务列表缓存项, TaskId={TaskId}", 
                    keysToRemove.Count, taskId);
                
                return Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "清除任务缓存失败: TaskId={TaskId}", taskId);
                return Task.CompletedTask;
            }
        }
    }
}