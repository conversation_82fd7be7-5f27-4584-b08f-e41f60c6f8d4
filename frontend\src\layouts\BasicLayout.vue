/**
 * 简单基础布局组件
 * 文件路径: src/layouts/BasicLayout.vue
 * 功能描述: 一个简单的布局组件，只包含基本的头部和内容区
 */

<template>
  <div class="basic-layout">
    <!-- 简单顶部导航 -->
    <header class="basic-header">
      <h1 class="site-title">{{ systemConfig.name }}</h1>
      <div class="user-menu">
        <el-button type="primary" size="small" @click="logout">退出登录</el-button>
      </div>
    </header>
    
    <!-- 内容区域 -->
    <main class="main-content">
      <router-view></router-view>
    </main>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/modules/user'
import { ElMessageBox } from 'element-plus'
import systemConfig from '@/config/system'

const router = useRouter()
const userStore = useUserStore()

// 退出登录
const logout = () => {
  ElMessageBox.confirm('确定要退出登录吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await userStore.logout()
      router.push('/login')
    } catch (error) {
      console.error('登出失败', error)
    }
  }).catch(() => {
    // 取消退出
  })
}
</script>

<style lang="scss" scoped>
.basic-layout {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.basic-header {
  height: 60px;
  background-color: #f0f2f5;
  display: flex;
  align-items: center;
  padding: 0 20px;
  justify-content: space-between;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  
  .site-title {
    font-size: 18px;
    margin: 0;
    color: #303133;
  }
}

.main-content {
  flex: 1;
  padding: 20px;
  background-color: #f5f7fa;
}
</style> 