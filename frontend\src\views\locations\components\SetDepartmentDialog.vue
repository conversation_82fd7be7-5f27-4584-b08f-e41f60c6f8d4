/**
 * 航空航天级IT资产管理系统 - 设置使用部门对话框
 * 文件路径: src/views/locations/components/SetDepartmentDialog.vue
 * 功能描述: 为位置设置关联部门的对话框组件
 */

<template>
  <el-dialog
    v-model="dialogVisible"
    title="设置使用部门"
    width="500px"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    @closed="handleClosed"
  >
    <el-form :model="formData" label-width="100px" label-position="right">
      <el-form-item label="当前位置">
        <span class="location-name">{{ locationName }}</span>
      </el-form-item>
      
      <el-form-item label="选择部门" required>
        <el-select
          v-model="formData.departmentId"
          placeholder="请选择部门"
          style="width: 100%"
          filterable
          :loading="loading"
        >
          <el-option
            v-for="dept in departmentOptions"
            :key="dept.id"
            :label="dept.name"
            :value="dept.id"
          >
            <span class="department-option">{{ dept.name }}</span>
            <span class="department-code">{{ dept.code }}</span>
          </el-option>
        </el-select>
      </el-form-item>
      
      <el-form-item label="备注">
        <el-input
          v-model="formData.remarks"
          type="textarea"
          :rows="3"
          placeholder="请输入备注信息"
        />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, defineProps, defineEmits } from 'vue'
import { ElMessage } from 'element-plus'
import departmentApi from '@/api/department'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  locationId: {
    type: [Number, String],
    default: ''
  },
  locationName: {
    type: String,
    default: ''
  },
  currentDepartmentId: {
    type: [Number, String],
    default: null
  }
})

const emit = defineEmits(['update:visible', 'success', 'close'])

// 对话框可见状态
const dialogVisible = ref(false)

// 表单数据
const formData = reactive({
  departmentId: null,
  remarks: ''
})

// 状态控制
const loading = ref(false)
const submitting = ref(false)

// 部门选项
const departmentOptions = ref([])

// 监听visible变化
watch(
  () => props.visible,
  (val) => {
    dialogVisible.value = val
    if (val) {
      // 初始化表单
      initForm()
      // 加载部门列表
      fetchDepartments()
    }
  }
)

// 监听dialogVisible变化
watch(
  () => dialogVisible.value,
  (val) => {
    emit('update:visible', val)
  }
)

// 初始化表单数据
const initForm = () => {
  formData.departmentId = props.currentDepartmentId || null
  formData.remarks = ''
}

// 获取部门列表
const fetchDepartments = async () => {
  loading.value = true
  try {
    const res = await departmentApi.getDepartmentList()
    if (res.data && res.data.success) {
      departmentOptions.value = res.data.data || []
    } else {
      ElMessage.error('获取部门列表失败')
    }
  } catch (error) {
    console.error('获取部门列表失败:', error)
    ElMessage.error('获取部门列表失败')
  } finally {
    loading.value = false
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formData.departmentId) {
    ElMessage.warning('请选择部门')
    return
  }
  
  if (!props.locationId) {
    ElMessage.warning('位置ID无效')
    return
  }
  
  submitting.value = true
  try {
    // 调用API设置部门
    const res = await departmentApi.updateLocationDepartment(props.locationId, {
      departmentId: formData.departmentId,
      remarks: formData.remarks
    })
    
    if (res.data && res.data.success) {
      ElMessage.success('设置部门成功')
      dialogVisible.value = false
      emit('success')
    } else {
      ElMessage.error('设置部门失败: ' + ((res.data && res.data.message) || '未知错误'))
    }
  } catch (error) {
    console.error('设置部门失败:', error)
    ElMessage.error('设置部门失败: ' + (error.message || '未知错误'))
  } finally {
    submitting.value = false
  }
}

// 关闭处理
const handleClosed = () => {
  emit('close')
}
</script>

<style lang="scss" scoped>
.location-name {
  font-weight: bold;
  color: #409EFF;
}

.department-option {
  float: left;
}

.department-code {
  float: right;
  color: #909399;
  font-size: 13px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style> 