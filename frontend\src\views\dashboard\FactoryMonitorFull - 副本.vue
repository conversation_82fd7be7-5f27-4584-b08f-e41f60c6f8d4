<template>
  <div class="factory-monitor-full">
    <!-- 顶部状态栏 -->
    <div class="top-status-bar">
      <div class="status-info">
        <!-- 状态信息已移除 -->
      </div>
      
      <div class="zoom-controls">
        <el-button-group size="small">
          <el-button @click="zoomOut" :disabled="zoomLevel <= 0.5">
            <el-icon><ZoomOut /></el-icon>
          </el-button>
          <el-button @click="resetZoom">
            <el-icon><Refresh /></el-icon>
          </el-button>
          <el-button @click="zoomIn" :disabled="zoomLevel >= 2">
            <el-icon><ZoomIn /></el-icon>
          </el-button>
        </el-button-group>
      </div>
    </div>

    <!-- 主要工厂布局区域 -->
    <div class="factory-layout-container" :style="containerStyle">
      <div class="factory-canvas" :style="canvasStyle">
        <!-- 区域容器 -->
        <div
          v-for="zone in layoutConfig?.zones || []"
          :key="zone.id"
          class="zone-container"
          :style="getZoneStyle(zone)"
        >
          <!-- 区域标签已移除 -->
          
          <!-- 工位网格 -->
          <div class="workstation-grid" :style="getGridStyle(zone)">
            <div
              v-for="workstation in getZoneWorkstations(zone)"
              :key="workstation.locationId"
              class="workstation-cell"
              :class="getWorkstationClass(workstation)"
              @click="selectWorkstation(workstation)"
            >
              <!-- 状态图标 -->
              <div class="status-icon">
                <el-icon v-if="workstation.status === 'operational'">
                  <Check />
                </el-icon>
                <el-icon v-else-if="workstation.status === 'warning'">
                  <Warning />
                </el-icon>
                <el-icon v-else-if="workstation.status === 'error'">
                  <Close />
                </el-icon>
                <el-icon v-else>
                  <Minus />
                </el-icon>
              </div>
              
              <!-- 工位编号和效率指示器已移除 -->
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧状态图例 -->
    <div class="status-legend">
      <div class="legend-items">
        <div class="legend-item">
          <div class="legend-dot operational"></div>
        </div>
        <div class="legend-item">
          <div class="legend-dot warning"></div>
        </div>
        <div class="legend-item">
          <div class="legend-dot error"></div>
        </div>
        <div class="legend-item">
          <div class="legend-dot idle"></div>
        </div>
      </div>
    </div>

    <!-- 悬浮提示框已移除 -->
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Check, Warning, Close, Minus, ZoomIn, ZoomOut, Refresh
} from '@element-plus/icons-vue'

// 响应式数据
const layoutConfig = ref(null)
const workstations = ref([])
const zoomLevel = ref(1)
const selectedWorkstation = ref(null)

// 提示框相关
const tooltipVisible = ref(false)
const tooltipX = ref(0)
const tooltipY = ref(0)
const tooltipWorkstation = ref(null)

// 计算属性
const totalWorkstations = computed(() => workstations.value.length)
const totalZones = computed(() => layoutConfig.value?.zones?.length || 0)

const containerStyle = computed(() => ({
  transform: `scale(${zoomLevel.value})`,
  transformOrigin: 'top left'
}))

const canvasStyle = computed(() => {
  if (!layoutConfig.value?.canvas) return {}
  return {
    width: layoutConfig.value.canvas.width + 'px',
    height: layoutConfig.value.canvas.height + 'px',
    backgroundColor: layoutConfig.value.canvas.backgroundColor || '#1e293b'
  }
})

// 方法
const loadLayoutConfig = async () => {
  try {
    const response = await fetch('/analyresport/factory-layout-1748964326771.json')
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }
    
    const config = await response.json()
    layoutConfig.value = adaptConfig(config)
    workstations.value = generateWorkstationData()
    
    console.log('布局配置加载成功:', layoutConfig.value)
  } catch (error) {
    console.error('加载布局配置失败:', error)
    ElMessage.error(`加载布局配置失败: ${error.message}`)
    // 使用默认配置
    layoutConfig.value = getDefaultConfig()
    workstations.value = generateWorkstationData()
  }
}

const adaptConfig = (config) => {
  // 计算画布尺寸
  let maxX = 0, maxY = 0
  config.zones.forEach(zone => {
    maxX = Math.max(maxX, zone.x + zone.width)
    maxY = Math.max(maxY, zone.y + zone.height)
  })

  return {
    name: "工厂布局",
    canvas: {
      width: maxX + 100,
      height: maxY + 100,
      backgroundColor: "#1e293b"
    },
    zones: config.zones.map(zone => ({
      id: zone.id,
      name: zone.name,
      color: zone.color,
      position: {
        x: zone.x,
        y: zone.y,
        width: zone.width,
        height: zone.height
      },
      layout: {
        rows: zone.rows,
        cols: zone.cols
      },
      startWorkstation: zone.startWorkstation
    }))
  }
}

const generateWorkstationData = () => {
  if (!layoutConfig.value) return []
  
  const data = []
  const statuses = ['operational', 'warning', 'error', 'idle']
  const weights = [0.7, 0.15, 0.1, 0.05]
  
  layoutConfig.value.zones.forEach(zone => {
    const { rows, cols } = zone.layout
    const startId = zone.startWorkstation
    
    for (let row = 0; row < rows; row++) {
      for (let col = 0; col < cols; col++) {
        const workstationId = startId + row * cols + col
        
        // 随机状态
        const random = Math.random()
        let status = 'operational'
        let cumulative = 0
        
        for (let i = 0; i < weights.length; i++) {
          cumulative += weights[i]
          if (random < cumulative) {
            status = statuses[i]
            break
          }
        }
        
        data.push({
          locationId: workstationId,
          locationName: `${zone.name}-工位${workstationId.toString().padStart(3, '0')}`,
          locationCode: `WS${workstationId.toString().padStart(3, '0')}`,
          zoneId: zone.id,
          zoneName: zone.name,
          status: status,
          efficiency: Math.floor(Math.random() * 31) + 70, // 70-100%
          assetCount: Math.floor(Math.random() * 6) + 2, // 2-7
          taskCount: Math.floor(Math.random() * 9) + 1, // 1-9
          row: row,
          col: col
        })
      }
    }
  })
  
  return data
}

const getDefaultConfig = () => ({
  name: "默认工厂布局",
  canvas: { width: 1000, height: 600, backgroundColor: "#1e293b" },
  zones: [{
    id: 1,
    name: "默认区域",
    color: "#3b82f6",
    position: { x: 100, y: 100, width: 400, height: 300 },
    layout: { rows: 3, cols: 4 },
    startWorkstation: 1
  }]
})

const getZoneStyle = (zone) => ({
  position: 'absolute',
  left: zone.position.x + 'px',
  top: zone.position.y + 'px',
  width: zone.position.width + 'px',
  height: zone.position.height + 'px',
  border: `2px solid ${zone.color}`,
  borderRadius: '8px',
  backgroundColor: `${zone.color}10`
})

const getGridStyle = (zone) => ({
  display: 'grid',
  gridTemplateColumns: `repeat(${zone.layout.cols}, 1fr)`,
  gridTemplateRows: `repeat(${zone.layout.rows}, 1fr)`,
  gap: '4px',
  padding: '8px',
  height: 'calc(100% - 30px)'
})

const getZoneWorkstations = (zone) => {
  return workstations.value
    .filter(w => w.zoneId === zone.id)
    .sort((a, b) => a.row * zone.layout.cols + a.col - (b.row * zone.layout.cols + b.col))
}

const getWorkstationClass = (workstation) => {
  return [`status-${workstation.status}`]
}

const getStatusText = (status) => {
  const statusMap = {
    operational: '正常运行',
    warning: '警告状态', 
    error: '故障状态',
    idle: '空闲状态'
  }
  return statusMap[status] || '未知状态'
}

// 缩放控制
const zoomIn = () => {
  if (zoomLevel.value < 2) {
    zoomLevel.value = Math.min(2, zoomLevel.value + 0.1)
  }
}

const zoomOut = () => {
  if (zoomLevel.value > 0.5) {
    zoomLevel.value = Math.max(0.5, zoomLevel.value - 0.1)
  }
}

const resetZoom = () => {
  zoomLevel.value = 1
}

// 工位选择
const selectWorkstation = (workstation) => {
  selectedWorkstation.value = workstation
  console.log('选中工位:', workstation)
}

// 提示框
const showTooltip = (workstation, event) => {
  tooltipWorkstation.value = workstation
  tooltipX.value = event.clientX + 10
  tooltipY.value = event.clientY + 10
  tooltipVisible.value = true
}

const hideTooltip = () => {
  tooltipVisible.value = false
}

// 生命周期
onMounted(() => {
  loadLayoutConfig()
})
</script>

<style scoped>
.factory-monitor-full {
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  overflow: hidden;
  position: relative;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* 顶部状态栏 */
.top-status-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 60px;
  background: rgba(15, 23, 42, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  z-index: 1000;
}

.status-info {
  display: flex;
  align-items: center;
  gap: 16px;
  color: #e2e8f0;
  font-size: 14px;
  font-weight: 500;
}

.total-info {
  color: #22c55e;
}

.zone-info, .layout-name {
  color: #94a3b8;
}

/* 主要布局容器 */
.factory-layout-container {
  position: absolute;
  top: 60px;
  left: 0;
  right: 200px;
  bottom: 0;
  overflow: auto;
  padding: 20px;
}

.factory-canvas {
  position: relative;
  margin: 0 auto;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

/* 区域容器 */
.zone-container {
  border-radius: 8px;
  transition: all 0.3s ease;
}

.zone-container:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.zone-label {
  position: absolute;
  top: -25px;
  left: 8px;
  font-size: 14px;
  font-weight: 600;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

/* 工位网格 */
.workstation-grid {
  width: 100%;
  height: 100%;
}

.workstation-cell {
  background: rgba(30, 41, 59, 0.8);
  border-radius: 6px;
  border: 2px solid transparent;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  min-height: 60px;
  backdrop-filter: blur(5px);
}

.workstation-cell:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
}

/* 状态样式 */
.workstation-cell.status-operational {
  background: linear-gradient(135deg, #22c55e, #16a34a);
  border-color: #22c55e;
  box-shadow: 0 0 20px rgba(34, 197, 94, 0.3);
}

.workstation-cell.status-warning {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  border-color: #f59e0b;
  box-shadow: 0 0 20px rgba(245, 158, 11, 0.3);
}

.workstation-cell.status-error {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  border-color: #ef4444;
  box-shadow: 0 0 20px rgba(239, 68, 68, 0.3);
  animation: pulse-error 2s infinite;
}

.workstation-cell.status-idle {
  background: linear-gradient(135deg, #64748b, #475569);
  border-color: #64748b;
  box-shadow: 0 0 20px rgba(100, 116, 139, 0.2);
}

@keyframes pulse-error {
  0%, 100% { box-shadow: 0 0 20px rgba(239, 68, 68, 0.3); }
  50% { box-shadow: 0 0 30px rgba(239, 68, 68, 0.6); }
}

/* 状态图标 */
.status-icon {
  font-size: 16px;
  color: white;
  margin-bottom: 4px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* 工位编号和效率指示器样式已移除 */

/* 右侧状态图例 */
.status-legend {
  position: fixed;
  top: 60px;
  right: 0;
  width: 200px;
  height: calc(100vh - 60px);
  background: rgba(15, 23, 42, 0.95);
  backdrop-filter: blur(10px);
  border-left: 1px solid rgba(148, 163, 184, 0.1);
  padding: 24px;
}

.legend-title {
  font-size: 16px;
  font-weight: 600;
  color: #e2e8f0;
  margin-bottom: 20px;
  text-align: center;
}

.legend-items {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 12px;
  color: #cbd5e1;
  font-size: 14px;
}

.legend-dot {
  width: 16px;
  height: 16px;
  border-radius: 4px;
  flex-shrink: 0;
}

.legend-dot.operational {
  background: linear-gradient(135deg, #22c55e, #16a34a);
  box-shadow: 0 0 10px rgba(34, 197, 94, 0.3);
}

.legend-dot.warning {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  box-shadow: 0 0 10px rgba(245, 158, 11, 0.3);
}

.legend-dot.error {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  box-shadow: 0 0 10px rgba(239, 68, 68, 0.3);
}

.legend-dot.idle {
  background: linear-gradient(135deg, #64748b, #475569);
  box-shadow: 0 0 10px rgba(100, 116, 139, 0.2);
}

/* 提示框样式已移除 */
</style>
