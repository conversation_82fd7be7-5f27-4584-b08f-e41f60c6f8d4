import{_ as e,r as t,ad as a,c as l,z as s,ax as n,E as o,A as r,b as i,d as u,e as c,w as d,a as p,o as m,p as v,f,v as y,F as h,h as g,k as w,al as C,q as b,Q as A,t as _,i as x,P as R,ah as S,aW as T,aX as k,D as N}from"./index-CG5lHOPO.js";import"./index-DYKgPQG6.js";import{a as z}from"./assetStatistics-CeU-HrJR.js";import{i as I}from"./install-BB2cVj0s.js";const M=["scroll","wheel","touchstart","touchmove","touchend","touchcancel","mousewheel","resize","orientationchange"],V=["keydown","keyup","keypress"];if("undefined"!=typeof window&&"undefined"!=typeof EventTarget){const e=EventTarget.prototype.addEventListener;let t=!1;try{const e={get passive(){return t=!0,!1}};window.addEventListener("testPassive",null,e),window.removeEventListener("testPassive",null,e)}catch(be){t=!1}EventTarget.prototype.addEventListener=function(a,l,s){if(s&&"object"==typeof s&&void 0!==s.passive)return e.call(this,a,l,s);if(t&&["wheel","mousewheel","touchstart","touchmove"].includes(a)){const t="boolean"==typeof s?{capture:s,passive:!0}:s||{};return t.passive=!0,e.call(this,a,l,t)}return e.call(this,a,l,s)}}const L={class:"asset-statistics-container"},$={class:"page-header"},O={class:"header-content"},j={class:"header-actions"},D={class:"filter-row"},P={class:"filter-item"},E={class:"filter-item"},W={class:"filter-item"},B={class:"filter-item"},U={class:"overview-cards"},F={class:"stat-card total"},X={class:"stat-icon"},q={class:"stat-content"},G={class:"stat-value"},Q={class:"stat-card active"},Z={class:"stat-icon"},H={class:"stat-content"},J={class:"stat-value"},K={class:"stat-trend positive"},Y={class:"stat-card idle"},ee={class:"stat-icon"},te={class:"stat-content"},ae={class:"stat-value"},le={class:"stat-trend negative"},se={class:"stat-card value"},ne={class:"stat-icon"},oe={class:"stat-content"},re={class:"stat-value"},ie={class:"stat-trend positive"},ue={class:"main-charts"},ce={class:"card-header"},de={class:"card-header"},pe={class:"trend-charts"},me={class:"card-header"},ve={class:"detail-tables"},fe={class:"card-header"},ye={class:"utilization-text"},he={class:"card-header"},ge={class:"active-rate-text"},we={class:"empty-data"},Ce=e({__name:"AssetStatisticsView",setup(e){const Ce=t(!1),be=t("weekly"),Ae=t(null),_e=t(null),xe=t(null);let Re=null,Se=null,Te=null,ke=null;const Ne=a({totalAssets:0,activeAssets:0,idleAssets:0,totalValue:0,totalTrend:0,activeTrend:0,idleTrend:0,valueTrend:0}),ze=t([]),Ie=t([]),Me=t([]),Ve=t([]),Le=t(""),$e=t(""),Oe=t(null),je=t(null),De=t(null),Pe=t([]),Ee=t([]),We=t([]),Be=l((()=>({daily:"日",weekly:"周",monthly:"月"}[be.value]||"周"))),Ue=l((()=>Le.value?Ie.value.filter((e=>e.regionName.toLowerCase().includes(Le.value.toLowerCase()))):Ie.value)),Fe=l((()=>$e.value?Me.value.filter((e=>e.departmentName.toLowerCase().includes($e.value.toLowerCase()))):Me.value)),Xe=e=>{if(!e)return"0%";return`${e>0?"+":""}${e.toFixed(1)}%`},qe=async e=>{be.value=e,await nt(),ct()},Ge=async()=>{Ce.value=!0,await Promise.all([at(),lt(),st(),nt(),Qe()]).finally((()=>{Ce.value=!1})),rt(),o.success("数据刷新成功")},Qe=async()=>{try{const e=await z.getRegionOptions();Pe.value=e.map((e=>({regionId:e.regionId,regionName:e.regionName})));const t=await z.getDepartmentOptions();Ee.value=t.map((e=>({departmentId:e.departmentId,departmentName:e.departmentName}))),We.value=ze.value.map((e=>({assetTypeId:e.assetTypeId,assetTypeName:e.typeName})))}catch(e){}},Ze=async e=>{await Ke()},He=async e=>{await Ke()},Je=async e=>{await Ke()},Ke=async()=>{const e={};Oe.value&&(e.regionId=Oe.value),je.value&&(e.departmentId=je.value),De.value&&(e.assetTypeId=De.value),Ce.value=!0,await Promise.all([Ye(e),et(e)]).finally((()=>{Ce.value=!1})),rt()},Ye=async e=>{try{const[t,a,l]=await Promise.all([z.getStatisticsByType(e),z.getStatisticsByRegion(e),z.getStatisticsByDepartment(e)]);if(ze.value=(t||[]).map((e=>({count:e.assetCount,typeName:e.assetTypeName,normalCount:e.normalCount,faultCount:e.faultCount,maintenanceCount:e.maintenanceCount,normalRate:e.normalRate,faultRate:e.faultRate,percentage:e.percentage}))),Ie.value=(a||[]).map((e=>{const t=Math.round(.35*e.assetCount),a=Math.round(.45*e.assetCount),l=e.assetCount-t-a;return{regionId:e.regionId,regionName:e.regionName,totalAssets:e.assetCount,computers:t,pdas:a,others:Math.max(0,l),utilizationRate:Math.round(e.normalRate||0),normalCount:e.normalCount,faultCount:e.faultCount,maintenanceCount:e.maintenanceCount,percentage:e.percentage}})),Me.value=(l||[]).map((e=>{const t=Math.round(.35*e.assetCount),a=Math.round(.45*e.assetCount),l=e.assetCount-t-a;return{departmentId:e.departmentId,departmentName:e.departmentName,totalAssets:e.assetCount,computers:t,pdas:a,others:Math.max(0,l),activeRate:Math.round(e.normalRate||0),normalCount:e.normalCount,faultCount:e.faultCount,maintenanceCount:e.maintenanceCount,percentage:e.percentage}})),t&&t.length>0){const e=t.reduce(((e,t)=>e+(t.assetCount||0)),0),a=t.reduce(((e,t)=>e+(t.normalCount||0)),0),l=t.reduce(((e,t)=>e+(t.faultCount||0)),0),s=t.reduce(((e,t)=>e+(t.maintenanceCount||0)),0);Object.assign(Ne,{totalAssets:e,activeAssets:a,idleAssets:l+s,totalValue:5e3*e})}else Object.assign(Ne,{totalAssets:0,activeAssets:0,idleAssets:0,totalValue:0})}catch(t){o.error("加载筛选数据失败")}},et=async e=>{try{let t;switch(be.value){case"daily":t=await z.getDailyTrend(e);break;case"monthly":t=await z.getMonthlyTrend(e);break;default:t=await z.getWeeklyTrend(e)}Ve.value=(t||[]).map((e=>({period:e.dateLabel||e.date,total:e.totalAssets||0,active:e.normalAssets||0,idle:(e.faultAssets||0)+(e.maintenanceAssets||0),date:e.date,normalRate:e.normalRate||0,faultRate:e.faultRate||0,newAssets:e.newAssets||0,processedAssets:e.processedAssets||0})))}catch(t){}},tt=async()=>{Oe.value=null,je.value=null,De.value=null,await Ge(),o.success("筛选条件已重置")},at=async()=>{try{const e=await z.getStatisticsByType();ze.value=(e||[]).map((e=>({assetTypeId:e.assetTypeId,count:e.assetCount,typeName:e.assetTypeName,normalCount:e.normalCount,faultCount:e.faultCount,maintenanceCount:e.maintenanceCount,normalRate:e.normalRate,faultRate:e.faultRate,percentage:e.percentage}))),(()=>{try{if(ze.value&&ze.value.length>0){const e=ze.value.reduce(((e,t)=>e+(t.count||0)),0),t=ze.value.reduce(((e,t)=>e+(t.normalCount||0)),0),a=ze.value.reduce(((e,t)=>e+(t.faultCount||0)),0),l=ze.value.reduce(((e,t)=>e+(t.maintenanceCount||0)),0);Object.assign(Ne,{totalAssets:e,activeAssets:t,idleAssets:a+l,totalValue:5e3*e,totalTrend:10*Math.random()-5,activeTrend:5*Math.random(),idleTrend:3*-Math.random(),valueTrend:8*Math.random()})}else Object.assign(Ne,{totalAssets:0,activeAssets:0,idleAssets:0,totalValue:0,totalTrend:0,activeTrend:0,idleTrend:0,valueTrend:0})}catch(e){}})()}catch(e){o.error("加载类型统计失败")}},lt=async()=>{try{const e=await z.getStatisticsByRegion();if(!e||0===e.length)return void(Ie.value=[]);Ie.value=e.map((e=>{const t=Math.round(.35*e.assetCount),a=Math.round(.45*e.assetCount),l=e.assetCount-t-a;return{regionId:e.regionId,regionName:e.regionName,totalAssets:e.assetCount,computers:t,pdas:a,others:Math.max(0,l),utilizationRate:Math.round(e.normalRate||0),normalCount:e.normalCount,faultCount:e.faultCount,maintenanceCount:e.maintenanceCount,percentage:e.percentage}}))}catch(e){o.error("加载区域统计失败")}},st=async()=>{try{const e=await z.getStatisticsByDepartment();e&&e.length>0?Me.value=e.map((e=>{const t=Math.round(.35*e.assetCount),a=Math.round(.45*e.assetCount),l=e.assetCount-t-a;return{departmentId:e.departmentId,departmentName:e.departmentName,totalAssets:e.assetCount,computers:t,pdas:a,others:Math.max(0,l),activeRate:Math.round(e.normalRate||0),normalCount:e.normalCount,faultCount:e.faultCount,maintenanceCount:e.maintenanceCount,percentage:e.percentage}})):Me.value=[]}catch(e){o.error("加载部门统计失败")}},nt=async()=>{try{let e;switch(be.value){case"daily":e=await z.getDailyTrend();break;case"monthly":e=await z.getMonthlyTrend();break;default:e=await z.getWeeklyTrend()}if(Ve.value=(e||[]).map((e=>({period:e.dateLabel||e.date,total:e.totalAssets||0,active:e.normalAssets||0,idle:(e.faultAssets||0)+(e.maintenanceAssets||0),date:e.date,normalRate:e.normalRate||0,faultRate:e.faultRate||0,newAssets:e.newAssets||0,processedAssets:e.processedAssets||0}))),!Ve.value||0===Ve.value.length){const e="daily"===be.value?["06-01","06-02","06-03","06-04","06-05","06-06","06-07"]:"monthly"===be.value?["2024-01","2024-02","2024-03","2024-04","2024-05","2024-06"]:["W18","W19","W20","W21","W22","W23"];Ve.value=e.map((e=>({period:e,total:242,active:242,idle:0,date:(new Date).toISOString(),normalRate:100,faultRate:0,newAssets:0,processedAssets:0})))}}catch(e){o.error("加载趋势数据失败")}},ot=()=>{n((()=>{if(Ae.value)try{Re=I(Ae.value)}catch(e){}if(_e.value)try{Se=I(_e.value)}catch(e){}if(xe.value)try{Te=I(xe.value)}catch(e){}try{ke=function(e,t,a,l={}){return"boolean"==typeof l&&(l={capture:l}),M.includes(t)&&void 0===l.passive?l.passive=!0:V.includes(t)&&void 0===l.passive&&(l.passive=!1),e.addEventListener(t,a,l),()=>{e.removeEventListener(t,a,l)}}(window,"resize",pt)}catch(e){}}))},rt=()=>{it(),ut(),ct()},it=()=>{if(!Re)return;if(!ze.value||0===ze.value.length){const e={title:{text:"资产类型分布",left:"center",textStyle:{fontSize:16,color:"#333"}},series:[],graphic:{type:"text",left:"center",top:"middle",style:{text:"暂无数据",fontSize:16,fill:"#999"}}};return void Re.setOption(e,!0)}const e={title:{text:"资产类型分布",left:"center",textStyle:{fontSize:16,color:"#333"}},tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)",backgroundColor:"rgba(50, 50, 50, 0.8)",borderColor:"#333",textStyle:{color:"#fff"}},legend:{orient:"vertical",left:"left",top:"middle",textStyle:{fontSize:12}},series:[{name:"资产数量",type:"pie",radius:["40%","70%"],center:["60%","50%"],avoidLabelOverlap:!1,itemStyle:{borderRadius:10,borderColor:"#fff",borderWidth:2},label:{show:!1,position:"center"},emphasis:{label:{show:!0,fontSize:"18",fontWeight:"bold"},itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}},labelLine:{show:!1},data:ze.value.map((e=>({value:e.count,name:e.typeName,itemStyle:{color:dt(e.typeName)}})))}]};Re.setOption(e,!0)},ut=()=>{if(!Se)return void o.error("图表初始化失败，请刷新页面重试");if(!Ie.value||0===Ie.value.length){const e={title:{text:"区域资产分布",left:"center",textStyle:{fontSize:16,color:"#333"}},series:[],graphic:{type:"text",left:"center",top:"middle",style:{text:'暂无数据\n请检查是否有位置类型为"工序"的区域',fontSize:16,fill:"#999",textAlign:"center"}},tooltip:{},legend:{},grid:{},xAxis:{},yAxis:{}};return void Se.setOption(e,!0)}const e={title:{text:"区域资产分布",left:"center",textStyle:{fontSize:16,color:"#333"}},tooltip:{trigger:"axis",axisPointer:{type:"shadow"},formatter:function(e){let t=`<strong>${e[0].name}</strong><br/>`,a=0;return e.forEach((e=>{t+=`${e.marker} ${e.seriesName}: ${e.value}<br/>`,a+=e.value})),t+=`<hr/>总计: ${a}`,t}},legend:{data:["电脑","PDA","其他"],top:30,textStyle:{fontSize:12}},grid:{left:"3%",right:"4%",bottom:"15%",top:"20%",containLabel:!0},xAxis:{type:"category",data:Ie.value.map((e=>e.regionName)),axisLabel:{rotate:45,interval:0,fontSize:11}},yAxis:{type:"value",axisLabel:{fontSize:11}},series:[{name:"电脑",type:"bar",stack:"total",data:Ie.value.map((e=>e.computers)),itemStyle:{color:"#5470c6"},emphasis:{focus:"series"}},{name:"PDA",type:"bar",stack:"total",data:Ie.value.map((e=>e.pdas)),itemStyle:{color:"#91cc75"},emphasis:{focus:"series"}},{name:"其他",type:"bar",stack:"total",data:Ie.value.map((e=>e.others)),itemStyle:{color:"#fac858"},emphasis:{focus:"series"}}]};Se.setOption(e,!0)},ct=()=>{if(!Te||!Ve.value.length)return;const e={title:{text:`资产变化趋势 (${Be.value})`,left:"center",textStyle:{fontSize:16,color:"#333"}},tooltip:{trigger:"axis"},legend:{data:["总数","在用","闲置"],top:30},grid:{left:"3%",right:"4%",bottom:"3%",top:"15%",containLabel:!0},dataZoom:[{type:"inside",zoomOnMouseWheel:!1}],xAxis:{type:"category",boundaryGap:!1,data:Ve.value.map((e=>e.period))},yAxis:{type:"value"},series:[{name:"总数",type:"line",data:Ve.value.map((e=>e.total)),itemStyle:{color:"#5470c6"},areaStyle:{opacity:.3}},{name:"在用",type:"line",data:Ve.value.map((e=>e.active)),itemStyle:{color:"#91cc75"}},{name:"闲置",type:"line",data:Ve.value.map((e=>e.idle)),itemStyle:{color:"#ee6666"}}]};Te.setOption(e)},dt=e=>({"工控机":"#5470c6",PDA:"#91cc75","扫描设备":"#fac858","蓝牙打印机":"#ee6666","电脑设备":"#73c0de","打印机":"#fc8452","扫码器":"#9a60b4","其他":"#ea7ccc"}[e]||"#5470c6"),pt=()=>{null==Re||Re.resize(),null==Se||Se.resize(),null==Te||Te.resize()},mt=async e=>{Oe.value=e.regionId,await Ke(),o.success(`已切换到 ${e.regionName} 区域视图`)},vt=async e=>{je.value=e.departmentId,await Ke(),o.success(`已切换到 ${e.departmentName} 部门视图`)},ft=()=>{const e=ze.value.map((e=>({"资产类型":e.typeName,"数量":e.count,"正常数量":e.normalCount,"故障数量":e.faultCount,"维修数量":e.maintenanceCount,"正常率":`${e.normalRate}%`,"占比":`${e.percentage}%`})));gt(e,"按类型统计")},yt=()=>{const e=Ie.value.map((e=>({"区域名称":e.regionName,"总数":e.totalAssets,"电脑":e.computers,PDA:e.pdas,"其他":e.others,"利用率":`${e.utilizationRate}%`,"正常数量":e.normalCount,"故障数量":e.faultCount})));gt(e,"按区域统计")},ht=()=>{const e=[{"类型":"总体统计","名称":"资产总数","数值":Ne.totalAssets,"单位":"台"},{"类型":"总体统计","名称":"在用资产","数值":Ne.activeAssets,"单位":"台"},{"类型":"总体统计","名称":"闲置资产","数值":Ne.idleAssets,"单位":"台"},{"类型":"总体统计","名称":"总价值","数值":Ne.totalValue,"单位":"元"},...ze.value.map((e=>({"类型":"按类型统计","名称":e.typeName,"数值":e.count,"单位":"台","正常率":`${e.normalRate}%`}))),...Ie.value.map((e=>({"类型":"按区域统计","名称":e.regionName,"数值":e.totalAssets,"单位":"台","利用率":`${e.utilizationRate}%`}))),...Me.value.map((e=>({"类型":"按部门统计","名称":e.departmentName,"数值":e.totalAssets,"单位":"台","在用率":`${e.activeRate}%`})))];gt(e,"资产统计综合报表")},gt=(e,t)=>{try{if(!e||0===e.length)return void o.warning("没有数据可导出");const a=Object.keys(e[0]),l=[a.join(","),...e.map((e=>a.map((t=>`"${e[t]||""}"`)).join(",")))].join("\n"),s=new Blob(["\ufeff"+l],{type:"text/csv;charset=utf-8;"}),n=document.createElement("a"),r=URL.createObjectURL(s);n.setAttribute("href",r),n.setAttribute("download",`${t}_${(new Date).toISOString().split("T")[0]}.csv`),n.style.visibility="hidden",document.body.appendChild(n),n.click(),document.body.removeChild(n),o.success("导出成功")}catch(a){o.error("导出失败")}};return s((async()=>{Ce.value=!0;try{await Promise.all([at(),lt(),st(),nt()]),await Qe(),await n(),setTimeout((()=>{ot(),setTimeout((()=>{rt()}),100)}),100)}catch(e){o.error("页面初始化失败")}finally{Ce.value=!1}})),r((()=>{ke&&ke(),null==Re||Re.dispose(),null==Se||Se.dispose(),null==Te||Te.dispose()})),(e,t)=>{const a=p("el-button"),l=p("el-button-group"),s=p("el-icon"),n=p("el-option"),o=p("el-select"),r=p("el-card"),z=p("el-col"),I=p("el-row"),M=p("el-input"),V=p("el-table-column"),Re=p("el-progress"),Se=p("el-table");return m(),i("div",L,[u("div",$,[u("div",O,[t[12]||(t[12]=u("div",{class:"header-title"},[u("h1",null,"资产统计分析"),u("p",null,"全方位资产数据分析与趋势监控")],-1)),u("div",j,[c(l,null,{default:d((()=>[c(a,{type:"daily"===be.value?"primary":"default",onClick:t[0]||(t[0]=e=>qe("daily")),size:"small"},{default:d((()=>t[8]||(t[8]=[v(" 按天 ")]))),_:1},8,["type"]),c(a,{type:"weekly"===be.value?"primary":"default",onClick:t[1]||(t[1]=e=>qe("weekly")),size:"small"},{default:d((()=>t[9]||(t[9]=[v(" 按周 ")]))),_:1},8,["type"]),c(a,{type:"monthly"===be.value?"primary":"default",onClick:t[2]||(t[2]=e=>qe("monthly")),size:"small"},{default:d((()=>t[10]||(t[10]=[v(" 按月 ")]))),_:1},8,["type"])])),_:1}),c(a,{onClick:Ge,loading:Ce.value,type:"primary",size:"small"},{default:d((()=>[c(s,null,{default:d((()=>[c(f(y))])),_:1}),t[11]||(t[11]=v(" 刷新数据 "))])),_:1},8,["loading"])])])]),c(r,{class:"filter-panel",shadow:"never"},{default:d((()=>[u("div",D,[u("div",P,[t[13]||(t[13]=u("label",null,"按区域筛选：",-1)),c(o,{modelValue:Oe.value,"onUpdate:modelValue":t[3]||(t[3]=e=>Oe.value=e),placeholder:"选择区域",clearable:"",onChange:Ze},{default:d((()=>[c(n,{label:"全部区域",value:null}),(m(!0),i(h,null,g(Pe.value,(e=>(m(),w(n,{key:e.regionId,label:e.regionName,value:e.regionId},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])]),u("div",E,[t[14]||(t[14]=u("label",null,"按部门筛选：",-1)),c(o,{modelValue:je.value,"onUpdate:modelValue":t[4]||(t[4]=e=>je.value=e),placeholder:"选择部门",clearable:"",onChange:He},{default:d((()=>[c(n,{label:"全部部门",value:null}),(m(!0),i(h,null,g(Ee.value,(e=>(m(),w(n,{key:e.departmentId,label:e.departmentName,value:e.departmentId},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])]),u("div",W,[t[15]||(t[15]=u("label",null,"按类型筛选：",-1)),c(o,{modelValue:De.value,"onUpdate:modelValue":t[5]||(t[5]=e=>De.value=e),placeholder:"选择资产类型",clearable:"",onChange:Je},{default:d((()=>[c(n,{label:"全部类型",value:null}),(m(!0),i(h,null,g(We.value,(e=>(m(),w(n,{key:e.assetTypeId,label:e.assetTypeName,value:e.assetTypeId},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])]),u("div",B,[c(a,{onClick:tt},{default:d((()=>[c(s,null,{default:d((()=>[c(f(C))])),_:1}),t[16]||(t[16]=v(" 重置筛选 "))])),_:1}),c(a,{type:"primary",onClick:ht},{default:d((()=>[c(s,null,{default:d((()=>[c(f(b))])),_:1}),t[17]||(t[17]=v(" 导出报表 "))])),_:1})])])])),_:1}),u("div",U,[c(I,{gutter:20},{default:d((()=>[c(z,{span:6},{default:d((()=>[u("div",F,[u("div",X,[c(s,null,{default:d((()=>[c(f(A))])),_:1})]),u("div",q,[u("div",G,_(Ne.totalAssets||0),1),t[18]||(t[18]=u("div",{class:"stat-label"},"资产总数",-1)),u("div",{class:x(["stat-trend",Ne.totalTrend>0?"positive":"negative"])},[c(s,null,{default:d((()=>[c(f(R))])),_:1}),u("span",null,_(Xe(Ne.totalTrend)),1)],2)])])])),_:1}),c(z,{span:6},{default:d((()=>[u("div",Q,[u("div",Z,[c(s,null,{default:d((()=>[c(f(S))])),_:1})]),u("div",H,[u("div",J,_(Ne.activeAssets||0),1),t[19]||(t[19]=u("div",{class:"stat-label"},"在用资产",-1)),u("div",K,[c(s,null,{default:d((()=>[c(f(R))])),_:1}),u("span",null,_(Xe(Ne.activeTrend)),1)])])])])),_:1}),c(z,{span:6},{default:d((()=>[u("div",Y,[u("div",ee,[c(s,null,{default:d((()=>[c(f(T))])),_:1})]),u("div",te,[u("div",ae,_(Ne.idleAssets||0),1),t[20]||(t[20]=u("div",{class:"stat-label"},"闲置资产",-1)),u("div",le,[c(s,null,{default:d((()=>[c(f(R))])),_:1}),u("span",null,_(Xe(Ne.idleTrend)),1)])])])])),_:1}),c(z,{span:6},{default:d((()=>{return[u("div",se,[u("div",ne,[c(s,null,{default:d((()=>[c(f(k))])),_:1})]),u("div",oe,[u("div",re,_((e=Ne.totalValue,e?`¥${(e/1e4).toFixed(1)}万`:"¥0")),1),t[21]||(t[21]=u("div",{class:"stat-label"},"总价值",-1)),u("div",ie,[c(s,null,{default:d((()=>[c(f(R))])),_:1}),u("span",null,_(Xe(Ne.valueTrend)),1)])])])];var e})),_:1})])),_:1})]),u("div",ue,[c(I,{gutter:20},{default:d((()=>[c(z,{span:12},{default:d((()=>[c(r,{class:"chart-card"},{header:d((()=>[u("div",ce,[t[23]||(t[23]=u("span",null,"按资产类型统计",-1)),c(a,{text:"",onClick:ft},{default:d((()=>[c(s,null,{default:d((()=>[c(f(b))])),_:1}),t[22]||(t[22]=v(" 导出 "))])),_:1})])])),default:d((()=>[u("div",{ref_key:"typeChartRef",ref:Ae,class:"chart-container"},null,512)])),_:1})])),_:1}),c(z,{span:12},{default:d((()=>[c(r,{class:"chart-card"},{header:d((()=>[u("div",de,[t[25]||(t[25]=u("span",null,"按区域统计",-1)),c(a,{text:"",onClick:yt},{default:d((()=>[c(s,null,{default:d((()=>[c(f(b))])),_:1}),t[24]||(t[24]=v(" 导出 "))])),_:1})])])),default:d((()=>[u("div",{ref_key:"regionChartRef",ref:_e,class:"chart-container"},null,512)])),_:1})])),_:1})])),_:1})]),u("div",pe,[c(r,{class:"chart-card"},{header:d((()=>[u("div",me,[u("span",null,"资产变化趋势 ("+_(Be.value)+")",1),t[26]||(t[26]=u("div",{class:"trend-legend"},[u("span",{class:"legend-item"},[u("span",{class:"legend-dot total"}),v(" 总数 ")]),u("span",{class:"legend-item"},[u("span",{class:"legend-dot active"}),v(" 在用 ")]),u("span",{class:"legend-item"},[u("span",{class:"legend-dot idle"}),v(" 闲置 ")])],-1))])])),default:d((()=>[u("div",{ref_key:"trendChartRef",ref:xe,class:"chart-container trend-chart"},null,512)])),_:1})]),u("div",ve,[c(I,{gutter:20},{default:d((()=>[c(z,{span:12},{default:d((()=>[c(r,{class:"table-card"},{header:d((()=>[u("div",fe,[t[27]||(t[27]=u("span",null,"区域资产分布",-1)),c(M,{modelValue:Le.value,"onUpdate:modelValue":t[6]||(t[6]=e=>Le.value=e),placeholder:"搜索区域...",size:"small",style:{width:"200px"},clearable:""},{prefix:d((()=>[c(s,null,{default:d((()=>[c(f(N))])),_:1})])),_:1},8,["modelValue"])])])),default:d((()=>[c(Se,{data:Ue.value,height:"400",stripe:"",onRowClick:mt},{default:d((()=>[c(V,{prop:"regionName",label:"区域名称","min-width":"120"}),c(V,{prop:"totalAssets",label:"总数",width:"80",align:"center"}),c(V,{prop:"computers",label:"电脑",width:"80",align:"center"}),c(V,{prop:"pdas",label:"PDA",width:"80",align:"center"}),c(V,{prop:"others",label:"其他",width:"80",align:"center"}),c(V,{prop:"utilizationRate",label:"利用率",width:"100",align:"center"},{default:d((({row:e})=>{return[c(Re,{percentage:e.utilizationRate,"stroke-width":6,"show-text":!1,color:(t=e.utilizationRate,t>=80?"#67c23a":t>=60?"#e6a23c":"#f56c6c")},null,8,["percentage","color"]),u("span",ye,_(e.utilizationRate)+"%",1)];var t})),_:1})])),_:1},8,["data"])])),_:1})])),_:1}),c(z,{span:12},{default:d((()=>[c(r,{class:"table-card"},{header:d((()=>[u("div",he,[t[28]||(t[28]=u("span",null,"部门资产分布",-1)),c(M,{modelValue:$e.value,"onUpdate:modelValue":t[7]||(t[7]=e=>$e.value=e),placeholder:"搜索部门...",size:"small",style:{width:"200px"},clearable:""},{prefix:d((()=>[c(s,null,{default:d((()=>[c(f(N))])),_:1})])),_:1},8,["modelValue"])])])),default:d((()=>[c(Se,{data:Fe.value,height:"400",stripe:"",onRowClick:vt},{empty:d((()=>[u("div",we,[c(s,{size:"48",color:"#c0c4cc"},{default:d((()=>[c(f(A))])),_:1}),t[29]||(t[29]=u("p",null,"暂无部门统计数据",-1)),t[30]||(t[30]=u("p",{class:"empty-tip"},"请确保资产已分配到相应部门",-1))])])),default:d((()=>[c(V,{prop:"departmentName",label:"部门名称","min-width":"120"}),c(V,{prop:"totalAssets",label:"总数",width:"80",align:"center"}),c(V,{prop:"computers",label:"电脑",width:"80",align:"center"}),c(V,{prop:"pdas",label:"PDA",width:"80",align:"center"}),c(V,{prop:"others",label:"其他",width:"80",align:"center"}),c(V,{prop:"activeRate",label:"在用率",width:"100",align:"center"},{default:d((({row:e})=>{return[c(Re,{percentage:e.activeRate,"stroke-width":6,"show-text":!1,color:(t=e.activeRate,t>=90?"#67c23a":t>=70?"#e6a23c":"#f56c6c")},null,8,["percentage","color"]),u("span",ge,_(e.activeRate)+"%",1)];var t})),_:1})])),_:1},8,["data"])])),_:1})])),_:1})])),_:1})])])}}},[["__scopeId","data-v-71e8dcc5"]]);export{Ce as default};
