// File: Core/Abstractions/ISparePartTypeRepository.cs
// Description: 备品备件类型仓储接口，定义数据访问方法

using System.Collections.Generic;
using System.Threading.Tasks;
using ItAssetsSystem.Domain.Entities;

namespace ItAssetsSystem.Core.Abstractions
{
    /// <summary>
    /// 备品备件类型仓储接口
    /// </summary>
    public interface ISparePartTypeRepository
    {
        /// <summary>
        /// 获取所有类型列表
        /// </summary>
        /// <returns>类型列表</returns>
        Task<List<SparePartType>> GetAllAsync();
        
        /// <summary>
        /// 根据ID获取类型
        /// </summary>
        /// <param name="id">类型ID</param>
        /// <returns>类型实体</returns>
        Task<SparePartType> GetByIdAsync(long id);
        
        /// <summary>
        /// 创建类型
        /// </summary>
        /// <param name="entity">类型实体</param>
        /// <returns>创建的类型实体</returns>
        Task<SparePartType> CreateAsync(SparePartType entity);
        
        /// <summary>
        /// 更新类型
        /// </summary>
        /// <param name="entity">类型实体</param>
        /// <returns>更新后的类型实体</returns>
        Task<SparePartType> UpdateAsync(SparePartType entity);
        
        /// <summary>
        /// 删除类型
        /// </summary>
        /// <param name="id">类型ID</param>
        /// <returns>是否成功</returns>
        Task<bool> DeleteAsync(long id);
        
        /// <summary>
        /// 检查编码是否已存在
        /// </summary>
        /// <param name="code">编码</param>
        /// <param name="excludeId">排除的ID</param>
        /// <returns>是否存在</returns>
        Task<bool> IsCodeExistsAsync(string code, long? excludeId = null);
        
        /// <summary>
        /// 获取类型的所有子类型ID
        /// </summary>
        /// <param name="parentId">父类型ID</param>
        /// <returns>子类型ID列表</returns>
        Task<List<long>> GetChildTypeIdsAsync(long parentId);
    }
} 