-- =====================================================
-- 清理多余的数据库触发器
-- 
-- 说明：当前系统已完全采用应用层服务处理业务逻辑，
-- 数据库触发器已经变得多余且可能引起冲突
-- =====================================================

-- 1. 首先查看当前所有触发器
SELECT '=== 当前数据库中的所有触发器 ===' AS Info;

SELECT 
    TRIGGER_NAME as '触发器名称',
    EVENT_MANIPULATION as '事件类型',
    EVENT_OBJECT_TABLE as '表名',
    ACTION_TIMING as '触发时机',
    CREATED as '创建时间'
FROM INFORMATION_SCHEMA.TRIGGERS 
WHERE TRIGGER_SCHEMA = DATABASE()
ORDER BY EVENT_OBJECT_TABLE, TRIGGER_NAME;

-- 2. 删除任务相关的多余触发器
SELECT '=== 删除任务相关触发器 ===' AS Info;

-- 任务表触发器 (现在通过 TaskService 处理)
DROP TRIGGER IF EXISTS `tr_tasks_status_update`;
DROP TRIGGER IF EXISTS `tr_tasks_status_update_backup`;
DROP TRIGGER IF EXISTS `tr_tasks_insert`;
DROP TRIGGER IF EXISTS `tr_tasks_update`;
DROP TRIGGER IF EXISTS `tr_tasks_after_update`;
DROP TRIGGER IF EXISTS `tr_tasks_before_update`;

-- 任务领取表触发器 (现在通过 TaskClaimService 处理)
DROP TRIGGER IF EXISTS `tr_task_claims_insert`;
DROP TRIGGER IF EXISTS `tr_task_claims_update`;
DROP TRIGGER IF EXISTS `tr_task_claims_update_backup`;
DROP TRIGGER IF EXISTS `tr_task_claims_after_insert`;
DROP TRIGGER IF EXISTS `tr_task_claims_after_update`;

SELECT '任务相关触发器删除完成' AS Status;

-- 3. 删除游戏化相关的多余触发器
SELECT '=== 删除游戏化相关触发器 ===' AS Info;

-- 游戏化日志触发器 (现在通过 GamificationService 处理)
DROP TRIGGER IF EXISTS `tr_gamification_log_insert`;
DROP TRIGGER IF EXISTS `tr_gamification_log_update`;
DROP TRIGGER IF EXISTS `tr_gamification_log_after_insert`;

-- 用户积分/等级触发器 (现在通过 UniversalGamificationService 处理)
DROP TRIGGER IF EXISTS `tr_user_points_update`;
DROP TRIGGER IF EXISTS `tr_user_level_update`;
DROP TRIGGER IF EXISTS `tr_user_stats_update`;
DROP TRIGGER IF EXISTS `tr_user_gamification_update`;

SELECT '游戏化相关触发器删除完成' AS Status;

-- 4. 删除通知相关的多余触发器
SELECT '=== 删除通知相关触发器 ===' AS Info;

-- 通知触发器 (现在通过 NotificationService 处理)
DROP TRIGGER IF EXISTS `tr_notifications_insert`;
DROP TRIGGER IF EXISTS `tr_notifications_update`;
DROP TRIGGER IF EXISTS `tr_notifications_after_insert`;

SELECT '通知相关触发器删除完成' AS Status;

-- 5. 删除资产相关的多余触发器 (如果通过应用层处理)
SELECT '=== 删除资产相关触发器 ===' AS Info;

DROP TRIGGER IF EXISTS `tr_assets_status_update`;
DROP TRIGGER IF EXISTS `tr_assets_location_update`;
DROP TRIGGER IF EXISTS `tr_assets_after_update`;
DROP TRIGGER IF EXISTS `tr_assets_before_update`;

SELECT '资产相关触发器删除完成' AS Status;

-- 6. 删除审计日志相关的多余触发器
SELECT '=== 删除审计日志相关触发器 ===' AS Info;

DROP TRIGGER IF EXISTS `tr_audit_log_insert`;
DROP TRIGGER IF EXISTS `tr_audit_log_update`;
DROP TRIGGER IF EXISTS `tr_audit_after_insert`;

SELECT '审计日志相关触发器删除完成' AS Status;

-- 7. 删除其他可能的多余触发器
SELECT '=== 删除其他多余触发器 ===' AS Info;

-- 用户相关触发器
DROP TRIGGER IF EXISTS `tr_users_update`;
DROP TRIGGER IF EXISTS `tr_users_after_update`;

-- 部门相关触发器
DROP TRIGGER IF EXISTS `tr_departments_update`;
DROP TRIGGER IF EXISTS `tr_departments_after_update`;

-- 位置相关触发器
DROP TRIGGER IF EXISTS `tr_locations_update`;
DROP TRIGGER IF EXISTS `tr_locations_after_update`;

-- 故障相关触发器
DROP TRIGGER IF EXISTS `tr_faults_update`;
DROP TRIGGER IF EXISTS `tr_faults_after_update`;

-- 维修相关触发器
DROP TRIGGER IF EXISTS `tr_repairs_update`;
DROP TRIGGER IF EXISTS `tr_repairs_after_update`;

-- 备件相关触发器
DROP TRIGGER IF EXISTS `tr_spare_parts_update`;
DROP TRIGGER IF EXISTS `tr_spare_parts_after_update`;

SELECT '其他多余触发器删除完成' AS Status;

-- 8. 最终检查剩余的触发器
SELECT '=== 清理后剩余的触发器 ===' AS Info;

SELECT 
    TRIGGER_NAME as '触发器名称',
    EVENT_MANIPULATION as '事件类型',
    EVENT_OBJECT_TABLE as '表名',
    ACTION_TIMING as '触发时机'
FROM INFORMATION_SCHEMA.TRIGGERS 
WHERE TRIGGER_SCHEMA = DATABASE()
ORDER BY EVENT_OBJECT_TABLE, TRIGGER_NAME;

-- 9. 统计信息
SELECT 
    CASE 
        WHEN COUNT(*) = 0 THEN '✅ 所有多余触发器已清理完成'
        WHEN COUNT(*) <= 2 THEN CONCAT('⚠️ 还剩余 ', COUNT(*), ' 个触发器，请检查是否需要保留')
        ELSE CONCAT('❌ 还剩余 ', COUNT(*), ' 个触发器，可能需要进一步清理')
    END AS '清理结果',
    COUNT(*) as '剩余触发器数量'
FROM INFORMATION_SCHEMA.TRIGGERS 
WHERE TRIGGER_SCHEMA = DATABASE();

-- =====================================================
-- 清理说明
-- =====================================================
/*
删除这些触发器的原因：

1. 任务管理：现在完全通过 TaskService 和事件驱动架构处理
   - 任务创建、更新、完成都有专门的服务方法
   - 使用 MediatR 发布领域事件
   - 有缓存服务优化性能

2. 游戏化系统：现在通过多个专门服务处理
   - GamificationService：核心游戏化逻辑
   - UniversalGamificationService：通用奖励系统
   - 事件驱动的奖励分发机制

3. 通知系统：现在通过 NotificationService 处理
   - 支持实时推送
   - 统一的通知创建和管理

4. 数据一致性：应用层事务管理更可靠
   - Entity Framework 的事务管理
   - 更好的错误处理和回滚机制

5. 性能优化：避免触发器冲突和递归调用
   - 减少数据库层面的复杂性
   - 更好的调试和监控能力

保留的触发器（如果有）应该是：
- 纯数据库层面的约束检查
- 审计日志（如果不通过应用层处理）
- 数据同步（如果有外部系统集成需求）
*/
