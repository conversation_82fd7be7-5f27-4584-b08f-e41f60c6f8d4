/**
 * API请求管理器 - 统一管理API请求，防止重复调用
 * 文件路径: src/utils/apiRequestManager.js
 * 功能描述: 请求去重、缓存管理、频率控制
 */

class ApiRequestManager {
  constructor() {
    this.pendingRequests = new Map()
    this.cache = new Map()
    this.cacheTimeout = 5 * 60 * 1000 // 5分钟缓存
    this.requestCounts = new Map() // 请求计数
    this.lastCleanup = Date.now()
    this.cleanupInterval = 10 * 60 * 1000 // 10分钟清理一次
  }

  // 生成请求唯一键
  generateKey(url, params = {}, method = 'GET') {
    const sortedParams = Object.keys(params)
      .sort()
      .reduce((result, key) => {
        result[key] = params[key]
        return result
      }, {})
    
    return `${method}:${url}:${JSON.stringify(sortedParams)}`
  }

  // 请求去重和缓存处理
  async request(key, requestFn, useCache = true, cacheTime = null) {
    try {
      // 定期清理过期缓存
      this.cleanupExpiredCache()

      // 检查缓存
      if (useCache && this.cache.has(key)) {
        const cached = this.cache.get(key)
        const timeout = cacheTime || this.cacheTimeout
        if (Date.now() - cached.timestamp < timeout) {
          console.log(`🔄 使用缓存: ${key}`)
          this.incrementRequestCount(key, 'cache')
          return cached.data
        }
      }

      // 检查是否有相同请求正在进行
      if (this.pendingRequests.has(key)) {
        console.log(`⏳ 等待进行中的请求: ${key}`)
        this.incrementRequestCount(key, 'pending')
        return await this.pendingRequests.get(key)
      }

      // 创建新请求
      console.log(`📡 发起新请求: ${key}`)
      const requestPromise = this._executeRequest(key, requestFn, useCache)
      this.pendingRequests.set(key, requestPromise)

      try {
        const result = await requestPromise
        this.incrementRequestCount(key, 'success')
        return result
      } finally {
        this.pendingRequests.delete(key)
      }
    } catch (error) {
      this.incrementRequestCount(key, 'error')
      console.error(`❌ 请求失败: ${key}`, error)
      throw error
    }
  }

  async _executeRequest(key, requestFn, useCache) {
    try {
      const data = await requestFn()
      
      if (useCache) {
        this.cache.set(key, {
          data,
          timestamp: Date.now()
        })
      }
      
      return data
    } catch (error) {
      console.error(`❌ 执行请求失败: ${key}`, error)
      throw error
    }
  }

  // 清除缓存
  clearCache(pattern) {
    if (pattern) {
      const keysToDelete = []
      for (const [key] of this.cache) {
        if (key.includes(pattern)) {
          keysToDelete.push(key)
        }
      }
      keysToDelete.forEach(key => this.cache.delete(key))
      console.log(`🧹 清除匹配缓存: ${pattern}, 清除${keysToDelete.length}个`)
    } else {
      this.cache.clear()
      console.log('🧹 清除所有缓存')
    }
  }

  // 取消进行中的请求
  cancelRequest(key) {
    if (this.pendingRequests.has(key)) {
      this.pendingRequests.delete(key)
      console.log(`🚫 取消请求: ${key}`)
    }
  }

  // 清理过期缓存
  cleanupExpiredCache() {
    const now = Date.now()
    if (now - this.lastCleanup < this.cleanupInterval) {
      return
    }

    const expiredKeys = []
    for (const [key, cached] of this.cache) {
      if (now - cached.timestamp > this.cacheTimeout) {
        expiredKeys.push(key)
      }
    }

    expiredKeys.forEach(key => this.cache.delete(key))
    this.lastCleanup = now

    if (expiredKeys.length > 0) {
      console.log(`🧹 清理过期缓存: ${expiredKeys.length}个`)
    }
  }

  // 请求计数统计
  incrementRequestCount(key, type) {
    if (!this.requestCounts.has(key)) {
      this.requestCounts.set(key, {
        cache: 0,
        pending: 0,
        success: 0,
        error: 0,
        total: 0
      })
    }
    
    const counts = this.requestCounts.get(key)
    counts[type]++
    counts.total++
  }

  // 获取请求统计
  getRequestStats(key) {
    return this.requestCounts.get(key) || {
      cache: 0,
      pending: 0,
      success: 0,
      error: 0,
      total: 0
    }
  }

  // 获取所有统计信息
  getAllStats() {
    const stats = {
      totalRequests: 0,
      cacheHits: 0,
      pendingHits: 0,
      successRequests: 0,
      errorRequests: 0,
      cacheSize: this.cache.size,
      pendingSize: this.pendingRequests.size
    }

    for (const [key, counts] of this.requestCounts) {
      stats.totalRequests += counts.total
      stats.cacheHits += counts.cache
      stats.pendingHits += counts.pending
      stats.successRequests += counts.success
      stats.errorRequests += counts.error
    }

    return stats
  }

  // 打印统计信息
  printStats() {
    const stats = this.getAllStats()
    console.log('📊 API请求统计:', {
      ...stats,
      cacheHitRate: stats.totalRequests > 0 ? 
        ((stats.cacheHits + stats.pendingHits) / stats.totalRequests * 100).toFixed(2) + '%' : '0%'
    })
  }
}

// 创建全局实例
export const apiRequestManager = new ApiRequestManager()

// 便捷方法
export const cachedRequest = (key, requestFn, useCache = true, cacheTime = null) => {
  return apiRequestManager.request(key, requestFn, useCache, cacheTime)
}

export const clearApiCache = (pattern) => {
  apiRequestManager.clearCache(pattern)
}

export const getApiStats = () => {
  return apiRequestManager.getAllStats()
}

export const printApiStats = () => {
  apiRequestManager.printStats()
}

export default apiRequestManager
