/**
 * 航空航天级IT资产管理系统 - 默认布局组件
 * 文件路径: src/layouts/DefaultLayout.vue
 * 功能描述: 系统的主布局结构，包含顶部导航、侧边栏菜单和内容区域
 */

<template>
  <div class="app-container">
    <!-- 顶部导航栏 -->
    <el-header class="header">
      <div class="header-left">
        <div class="logo">{{ systemConfig.appName || 'IT资产管理系统' }}</div>
      </div>
      <div class="header-right">
        <el-input v-model="searchQuery" placeholder="全局搜索..." style="width: 250px; margin-right: 15px;" clearable :prefix-icon="Search" />

        <!-- 新增：创建随手记按钮 -->
        <el-tooltip content="新建随手记" placement="bottom">
          <el-button :icon="EditPen" circle @click="openQuickMemoDrawer" style="margin-right: 10px;"></el-button>
        </el-tooltip>

        <!-- 新增：工作汇总快速访问按钮 -->
        <el-tooltip content="工作汇总报告" placement="bottom">
          <el-button :icon="DataAnalysis" circle @click="showWorkSummaryDialog" style="margin-right: 10px;" type="primary"></el-button>
        </el-tooltip>

        <!-- 新增/修改：通知按钮 -->
        <el-tooltip content="查看通知" placement="bottom">
          <el-badge :value="unreadNotificationCount" :hidden="unreadNotificationCount === 0" class="item notification-badge-header" :max="99">
            <el-button :icon="Bell" circle @click="showNotificationsPanel"></el-button>
          </el-badge>
        </el-tooltip>

        <el-dropdown trigger="click" @command="handleCommand">
          <span class="user-avatar-name">
            <el-avatar :size="32" :src="userStore.computedAvatarUrl" class="avatar" />
             <span class="username">{{ userStore.userInfo?.name || '用户名' }}</span>
             <el-icon class="el-icon--right"><arrow-down /></el-icon>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="profile">
                <el-icon><User /></el-icon>个人中心
              </el-dropdown-item>
              <el-dropdown-item command="password">
                <el-icon><Setting /></el-icon>修改密码
              </el-dropdown-item>
              <el-dropdown-item divided command="logout">
                <el-icon><SwitchButton /></el-icon>退出登录
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </el-header>

    <el-container>
      <!-- 侧边栏 -->
      <el-aside class="sidebar" :width="isCollapse ? '64px' : '210px'">
         <el-scrollbar>
          <!-- 重建菜单 -->
          <el-menu 
            :default-active="activeMenu" 
            :collapse="isCollapse" 
            class="el-menu-vertical"
            router  
            :unique-opened="true" 
          >
             <!-- 仪表盘 -->
             <el-menu-item index="/main/dashboard">
                <el-icon><Odometer /></el-icon>
                <template #title>仪表盘</template>
             </el-menu-item>

             <!-- 资产管理 -->
             <el-sub-menu index="/main/asset">
                <template #title>
                  <el-icon><Monitor /></el-icon>
                  <span>资产管理</span>
                </template>
                <el-menu-item index="/main/asset/list">
                  <el-icon><List /></el-icon>
                  <span>资产列表</span>
                </el-menu-item>
                <el-menu-item index="/main/asset/type">
                  <el-icon><Setting /></el-icon>
                  <span>资产类型</span>
                </el-menu-item>
                <el-menu-item index="/main/asset/statistics">
                  <el-icon><DataBoard /></el-icon>
                  <span>统计分析</span>
                </el-menu-item>
                <el-menu-item index="/main/asset/analytics-workbench">
                  <el-icon><TrendCharts /></el-icon>
                  <span>智能分析工作台</span>
                </el-menu-item>
             </el-sub-menu>

             <!-- 位置管理 -->
             <el-sub-menu index="/main/locations">
                <template #title>
                  <el-icon><LocationInformation /></el-icon>
                  <span>位置管理</span>
                </template>
                <el-menu-item index="/main/locations/structure">
                   <!-- <el-icon><Tree /></el-icon>  需要 Tree 图标 -->
                   <span>位置结构</span>
                 </el-menu-item>
                 <el-menu-item index="/main/locations/relations">
                   <!-- <el-icon><Connection /></el-icon> 需要 Connection 图标 -->
                   <span>位置关联</span>
                 </el-menu-item>
             </el-sub-menu>

             <!-- 故障管理 -->
              <el-sub-menu index="/main/faults">
                <template #title>
                  <el-icon><Warning /></el-icon>
                  <span>故障管理</span>
                </template>
                <el-menu-item index="/main/faults/list">
                   <span>故障列表</span>
                 </el-menu-item>
                 <el-menu-item index="/main/faults/maintenance">
                   <span>返厂/维修</span>
                 </el-menu-item>
             </el-sub-menu>
             
             <!-- 采购管理 -->
              <el-sub-menu index="/main/purchases">
                <template #title>
                  <el-icon><ShoppingCart /></el-icon>
                  <span>采购管理</span>
                </template>
                <el-menu-item index="/main/purchases/list">
                   <span>采购列表</span>
                 </el-menu-item>
             </el-sub-menu>

             <!-- 备品备件管理 (新添加) -->
             <el-sub-menu index="/main/spareparts">
                <template #title>
                  <el-icon><Box /></el-icon> <!-- 使用Box图标 -->
                  <span>备品备件管理</span>
                </template>
                <el-menu-item index="/main/spareparts/list">
                  <el-icon><Tickets /></el-icon> <!-- 使用Tickets或List图标 -->
                  <span>备件台账</span>
                </el-menu-item>
                <el-menu-item index="/main/spareparts/types">
                  <el-icon><CollectionTag /></el-icon> <!-- 使用CollectionTag或Setting图标 -->
                  <span>备件类型管理</span>
                </el-menu-item>
                <el-menu-item index="/main/spareparts/locations">
                  <el-icon><MapLocation /></el-icon> <!-- 使用MapLocation图标 -->
                  <span>备件库位管理</span>
                </el-menu-item>
                <el-menu-item index="/main/spareparts/transactions">
                  <el-icon><Switch /></el-icon> <!-- 使用Switch或Sort图标 -->
                  <span>出入库记录</span>
                </el-menu-item>
                <el-menu-item index="/main/spareparts/suppliers">
                  <el-icon><OfficeBuilding /></el-icon>
                  <span>供应商管理</span>
                </el-menu-item>
             </el-sub-menu>

            <!-- 任务中心 (新) -->
             <el-sub-menu index="/main/tasks"> 
                <template #title>
                  <el-icon><Tickets /></el-icon> 
                  <span>任务中心</span> 
                </template>
                <el-menu-item index="/main/tasks/list">
                  <el-icon><List /></el-icon>
                  <span>任务列表</span>
                </el-menu-item>
                <el-menu-item index="/main/tasks/pdca-tracker">
                  <el-icon><Checked /></el-icon>
                  <span>PDCA跟踪</span>
                </el-menu-item>
                <el-menu-item index="/main/tasks/periodic">
                  <el-icon><Refresh /></el-icon>
                  <span>周期性任务</span>
                </el-menu-item>
                <el-menu-item index="/main/tasks/kanban">
                  <el-icon><DataBoard /></el-icon>
                  <span>任务看板</span>
                </el-menu-item>
                <el-menu-item index="/main/tasks/shift-management" v-if="hasPermission(['admin', 'manager'])">
                  <el-icon><SwitchButton /></el-icon>
                  <span>班次管理</span>
                </el-menu-item>
                <el-menu-item index="/main/tasks/work-summary">
                  <el-icon><DataAnalysis /></el-icon>
                  <span>工作汇总报告</span>
                </el-menu-item>
                 <!-- 根据需要添加任务详情路由入口 -->
                 <!-- <el-menu-item index="/main/tasks/detail/:id" v-if="false">任务详情</el-menu-item> -->
             </el-sub-menu>
             
             <!-- 随手记 (新添加) -->
             <el-menu-item index="/main/quickmemo">
                <el-icon><Notebook /></el-icon>
                <template #title>随手记</template>
             </el-menu-item>

             <!-- 随手记列表 (新添加) -->
             <el-menu-item index="/main/memo-list">
                <el-icon><Tickets /></el-icon>
                <template #title>随手记列表</template>
             </el-menu-item>

             <!-- 游戏化系统 (新) -->
             <el-sub-menu index="/main/gamification">
                <template #title>
                  <el-icon><Trophy /></el-icon>
                  <span>游戏化系统</span>
                </template>
                <el-menu-item index="/main/gamification/overview">
                  <el-icon><DataBoard /></el-icon>
                  <span>系统管理</span>
                </el-menu-item>
                <el-menu-item index="/main/gamification/achievements">
                  <el-icon><Star /></el-icon>
                  <span>成就管理</span>
                </el-menu-item>
                <el-menu-item index="/main/gamification/leaderboard">
                  <el-icon><Trophy /></el-icon>
                  <span>排行榜</span>
                </el-menu-item>
                <el-menu-item index="/standardized-gamification">
                  <el-icon><Medal /></el-icon>
                  <span>标准化游戏化</span>
                </el-menu-item>
             </el-sub-menu>

             <!-- 个人中心 -->
              <el-menu-item index="/main/user/profile">
                 <el-icon><User /></el-icon>
                 <template #title>个人中心</template>
              </el-menu-item>

            <!-- 系统管理 -->
             <el-sub-menu index="/main/system">
                <template #title>
                  <el-icon><Setting /></el-icon>
                  <span>系统管理</span>
                </template>
                <el-menu-item index="/main/system/users">用户管理</el-menu-item>
                <el-menu-item index="/main/system/roles">角色管理</el-menu-item>
                <el-menu-item index="/main/system/menus">菜单管理</el-menu-item>
                <el-menu-item index="/main/system/departments">部门管理</el-menu-item>
                <el-menu-item index="/main/system/personnel">人员管理</el-menu-item>
                <el-menu-item index="/main/system/logs">审计日志</el-menu-item>
             </el-sub-menu>

             <!-- 功能测试 (如果需要保留) -->
             <el-sub-menu index="/test">
               <template #title>
                 <el-icon><Operation /></el-icon>
                 <span>功能测试</span>
               </template>
               <el-menu-item index="/test/export">
                 <el-icon><Download /></el-icon>
                 <span>导出测试</span>
               </el-menu-item>
             </el-sub-menu>
             
          </el-menu>
         </el-scrollbar>
      </el-aside>

      <!-- 主内容区域 -->
      <el-main class="main-content">
        <ActivityTicker />
        <router-view v-slot="{ Component }">
          <transition name="fade-transform" mode="out-in">
            <component :is="Component" />
          </transition>
        </router-view>
      </el-main>
    </el-container>
    
    <!-- 浮动收缩按钮 -->
    <div class="collapse-btn" :class="{ 'collapsed': isCollapse }" @click="isCollapse = !isCollapse">
      <el-icon>
        <Expand v-if="isCollapse" />
        <Fold v-else />
      </el-icon>
    </div>
    
    <!-- 退出登录确认对话框 -->
     <el-dialog
       title="退出确认"
       v-model="userStore.isLogoutDialogVisible" 
       width="380px"
     >
       <span>确定要退出登录吗？</span>
       <template #footer>
         <el-button @click="userStore.setLogoutDialogVisible(false)">取消</el-button>
         <el-button type="primary" @click="confirmLogout">确定</el-button>
       </template>
     </el-dialog>

    <!-- 通知中心 -->
    <NotificationCenter
      v-model:visible="showNotificationDrawer"
      mode="drawer"
      @view-task="handleViewTask"
    />

    <!-- 工作汇总弹窗 -->
    <WorkSummaryDialog v-model="showWorkSummaryDialogVisible" />

    <!-- 实时排行榜弹窗 -->
    <RealtimeLeaderboardDialog
      v-model:visible="showLeaderboardDialog"
      @close="handleLeaderboardDialogClose"
    />

  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useUserStore } from '@/stores/modules/user'
import { useGamificationStore } from '@/stores/modules/gamification'
import { useQuickMemoStore } from '@/stores/modules/quickMemo'
import { useNotificationStore } from '@/stores/modules/notification'
import { notificationService } from '@/utils/notification-service'
import { ElMessageBox, ElMessage } from 'element-plus'
import ActivityTicker from '@/components/ActivityTicker.vue'
import Breadcrumb from '@/components/Breadcrumb.vue'
import NotificationCenter from '@/views/tasks/components/NotificationCenter.vue'
import WorkSummaryDialog from '@/components/WorkSummaryDialog.vue'
import RealtimeLeaderboardDialog from '@/components/Leaderboard/RealtimeLeaderboardDialog.vue'
import systemConfig from '@/config/system'
import {
  Odometer,
  Monitor,
  LocationInformation,
  Warning,
  ShoppingCart,
  List,
  Setting,
  Expand,
  Fold,
  ArrowDown,
  Operation,
  Download,
  Ship,
  Star,
  User,
  SwitchButton,
  Tickets,
  DataBoard,
  Trophy,
  ChatDotRound,
  Search,
  EditPen,
  Bell,
  Notebook,
  Checked,
  Refresh,
  DataAnalysis,
  Box,
  CollectionTag,
  OfficeBuilding,
  MapLocation,
  Switch,
  TrendCharts,
  Medal
} from '@element-plus/icons-vue'

// 路由和状态
const router = useRouter()
const route = useRoute()
const userStore = useUserStore()
const gamificationStore = useGamificationStore()
const quickMemoStore = useQuickMemoStore()
const notificationStore = useNotificationStore()

// 侧边栏折叠状态
const isCollapse = ref(localStorage.getItem('sidebarCollapse') === 'true')
const searchQuery = ref('')

// 工作汇总弹窗状态
const showWorkSummaryDialogVisible = ref(false)

// 实时排行榜弹窗状态
const showLeaderboardDialog = ref(false)

// 缓存的视图组件
const cachedViews = ref(['Dashboard', 'AssetList', 'LocationTree', 'FaultList', 'TaskList'])

// 当前激活的菜单项
const activeMenu = computed(() => {
  const { meta, path } = route
  if (meta.activeMenu) {
    return meta.activeMenu
  }
  return path
})

// 使用固定颜色或从其他地方获取
const sidebarBgColor = ref('var(--sidebar-bg)')
const sidebarTextColor = ref('var(--sidebar-text-color)')
const sidebarActiveTextColor = ref('var(--sidebar-active-text-color)')

// 检查是否具有指定权限
const hasPermission = (requiredRoles) => {
  if (!requiredRoles || requiredRoles.length === 0) {
    return true
  }
  
  const userRoles = userStore.roles || []
  
  if (import.meta.env.DEV && userRoles.length === 0) {
    return true
  }
  
  return requiredRoles.some(role => userRoles.includes(role))
}

// 下拉菜单命令处理
const handleCommand = (command) => {
  switch (command) {
    case 'profile':
      router.push('/main/user/profile')
      break
    case 'password':
      ElMessageBox.alert('修改密码功能待实现', '提示', { type: 'info' })
      break
    case 'logout':
      userStore.setLogoutDialogVisible(true)
      break
    default:
      break
  }
}

// 确认退出登录
const confirmLogout = async () => {
  try {
    await userStore.logout()
    router.push(`/login?redirect=${route.fullPath}`)
    userStore.setLogoutDialogVisible(false)
  } catch (error) {
    ElMessageBox.alert('退出登录失败', '错误', { type: 'error' })
    console.error('Logout failed:', error)
    userStore.setLogoutDialogVisible(false)
  }
}

// 组件挂载时的处理
onMounted(async () => {
  if (!userStore.userInfo?.id) {
    try {
      await userStore.getUserInfo()
    } catch (error) {
      console.error("Failed to fetch user info on mount:", error)
    }
  }

  gamificationStore.initializeStore()

  // 只有在用户已登录时才初始化通知系统
  if (userStore.isLogin && userStore.userInfo?.id) {
    try {
      console.log('用户已登录，初始化通知系统...')
      await notificationStore.fetchUnreadCount()
      notificationStore.startPolling()

      // 初始化SignalR实时通知连接
      await notificationService.initConnection(userStore.userInfo.id)
    } catch (error) {
      console.error('初始化通知系统失败:', error)
    }
  } else {
    console.log('用户未登录，跳过通知系统初始化')
  }

  isCollapse.value = localStorage.getItem('sidebarCollapse') === 'true'

  // 检查是否为每日首次进入，如果是则显示排行榜弹窗
  checkDailyFirstVisit()
})

// 监听折叠状态变化并保存
watch(isCollapse, (newValue) => {
  localStorage.setItem('sidebarCollapse', newValue)
})

// 新增随手记和通知相关逻辑
const openQuickMemoDrawer = () => {
  quickMemoStore.openMemoDrawer('create')
}

// 通知相关状态
const showNotificationDrawer = ref(false)
const unreadNotificationCount = computed(() => notificationStore.unreadCount)

const showNotificationsPanel = () => {
  showNotificationDrawer.value = true
}

// 显示工作汇总弹窗
const showWorkSummaryDialog = () => {
  showWorkSummaryDialogVisible.value = true
}

const handleViewTask = (taskId) => {
  // 跳转到任务详情页面
  router.push(`/main/tasks/detail/${taskId}`)
  showNotificationDrawer.value = false
}

// 检查每日首次进入
const checkDailyFirstVisit = () => {
  const today = new Date().toDateString()
  const lastVisitDate = localStorage.getItem('last-visit-date')
  const leaderboardHiddenDate = localStorage.getItem('leaderboard-dialog-hidden')

  // 如果今天是首次访问，或者用户没有选择今日不再显示
  if (lastVisitDate !== today && leaderboardHiddenDate !== today) {
    // 延迟2秒显示，让页面完全加载
    setTimeout(() => {
      showLeaderboardDialog.value = true
    }, 2000)
  }

  // 更新最后访问日期
  localStorage.setItem('last-visit-date', today)
}

// 处理排行榜弹窗关闭
const handleLeaderboardDialogClose = () => {
  showLeaderboardDialog.value = false
}

// 组件销毁时清理
onUnmounted(() => {
  console.log('DefaultLayout 组件销毁，清理通知系统...')
  // 断开SignalR连接
  notificationService.disconnect()

  // 停止通知轮询
  notificationStore.stopPolling()
})
</script>

<style scoped lang="scss">
.app-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: var(--bg-color); /* 使用页面背景色 */
}

/* 顶部导航 */
.header {
  height: var(--header-height);
  background-color: var(--header-bg);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  box-shadow: var(--shadow-light);
  color: var(--text-color-on-primary); /* 确保头部文本颜色 */
  flex-shrink: 0;

  .header-left {
    display: flex;
    align-items: center;
    .logo {
      font-size: 20px;
      font-weight: bold;
      color: var(--text-color-on-primary); /* Logo 文本颜色 */
      margin-right: 20px;
    }
  }

  .header-right {
    display: flex;
    align-items: center;

    :deep(.el-input__inner) { /* 修复搜索框文字颜色 */
        color: var(--text-color);
    }

    .user-avatar-name {
      display: flex;
      align-items: center;
      cursor: pointer;
      color: var(--text-color-on-primary); /* 用户名颜色 */

      .avatar {
        margin-right: 8px;
      }
      .username {
        font-size: 14px;
      }
       .el-icon--right {
         margin-left: 4px;
       }
    }

    .notification-badge-header {
      margin-right: 10px; // 调整间距
      .el-button { // 调整按钮样式以适应头部
        background-color: transparent;
        // color: var(--text-color-on-primary); // 根据你的主题变量调整
        color: var(--header-text-color, #fff); // 假设头部文字颜色变量
        border: none;
        font-size: 18px; // 图标大小
        &:hover {
          // background-color: rgba(255,255,255,0.1); // 悬浮效果
          background-color: var(--header-hover-bg-color, rgba(255,255,255,0.1)); // 假设的悬浮背景色变量
        }
      }
      :deep(.el-badge__content.is-fixed) { // 调整角标位置
        top: 8px;
        right: calc(10px + var(--el-badge-size) / 2 - 2px); // 微调以适应圆形按钮
      }
    }
  }
}

/* 下拉菜单样式修正 */
:deep(.el-dropdown-menu__item) {
  color: var(--text-color) !important; // 强制设置文字颜色
  &:hover {
    background-color: var(--hover-bg-color) !important; // 强制设置悬停背景色
    color: var(--primary-color) !important; // 强制设置悬停文字颜色
  }
  .el-icon {
      color: var(--text-color-secondary); // 图标颜色
      margin-right: 8px;
  }
}
:deep(.el-dropdown-menu__item.is-disabled) { // 禁用项颜色
   color: var(--text-color-disabled) !important;
   &:hover {
     background-color: transparent !important; // 禁用项悬停无背景
   }
}
:deep(.el-dropdown-menu__item--divided) { // 分割线
    border-top-color: var(--border-color-light) !important;
    margin-top: 4px !important;
    padding-top: 4px !important;
}


.el-container {
  flex: 1; /* 占据剩余空间 */
  overflow: hidden; /* 防止内容溢出导致滚动条 */
}

/* 侧边栏 */
.sidebar {
  background-color: var(--sidebar-bg);
  transition: width var(--transition-duration) var(--transition-timing-function);
  box-shadow: var(--shadow-light);
  display: flex;
  flex-direction: column;
  overflow: hidden; /* 防止折叠动画时内容溢出 */

  .el-scrollbar {
    flex: 1; /* 占据侧边栏所有可用空间 */
  }

  .el-menu-vertical:not(.el-menu--collapse) {
    width: 210px;
    min-height: 100%; /* 确保菜单至少填满滚动区域 */
  }
   .el-menu-vertical {
     border-right: none;
     background-color: var(--sidebar-bg) !important;

     .el-menu-item,
     :deep(.el-sub-menu__title) {
       color: var(--sidebar-text-color, var(--text-color-secondary)) !important;
       &:hover {
         background-color: var(--sidebar-hover-bg, var(--hover-bg-color)) !important;
         color: var(--sidebar-active-text-color, var(--accent-color)) !important;
         .el-icon {
           color: var(--sidebar-active-text-color, var(--accent-color)) !important;
         }
       }
       .el-icon {
         color: var(--sidebar-text-color, var(--text-color-secondary));
       }
     }
     
     .el-menu-item.is-active {
       background-color: var(--sidebar-active-bg, transparent) !important;
       color: var(--sidebar-active-text-color, var(--accent-color)) !important;
       border-left: 3px solid var(--sidebar-active-text-color, var(--accent-color));
       .el-icon {
         color: var(--sidebar-active-text-color, var(--accent-color)) !important;
       }
     }
     
     :deep(.el-sub-menu.is-active > .el-sub-menu__title) {
       color: var(--sidebar-active-text-color, var(--accent-color)) !important;
       .el-icon {
         color: var(--sidebar-active-text-color, var(--accent-color)) !important;
       }
     }
   }
}


/* 主内容区 */
.main-content {
  padding: var(--content-padding);
  background-color: var(--bg-color);
  overflow-y: auto; /* 允许内容区垂直滚动 */
}

/* 折叠按钮 */
.collapse-btn {
  position: fixed;
  bottom: 20px;
  left: calc(var(--sidebar-width) + 10px); /* 初始位置 */
  width: 40px;
  height: 40px;
  background-color: var(--primary-color);
  color: var(--text-color-on-primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: var(--shadow-medium);
  transition: left var(--transition-duration) var(--transition-timing-function);
  z-index: var(--z-index-sticky);

  &.collapsed {
    left: calc(var(--sidebar-collapsed-width) + 10px); /* 折叠后位置 */
  }

  .el-icon {
    font-size: 20px;
  }
   &:hover {
      background-color: var(--primary-color-dark);
   }
}

/* 页面过渡动画 */
.fade-transform-leave-active,
.fade-transform-enter-active {
  transition: all 0.5s;
}
.fade-transform-enter-from {
  opacity: 0;
  transform: translateX(-30px);
}
.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(30px);
}

/* 移除旧的或不再使用的样式 */
/* .sidebar::-webkit-scrollbar { display: none; } */
/* .header .logo img { height: 30px; } */
/* .header .user-info { display: flex; align-items: center; } */
/* ... 其他可能需要移除的旧样式 */
</style> 