<template>
  <div class="file-upload">
    <el-upload
      ref="uploadRef"
      :action="uploadUrl"
      :headers="uploadHeaders"
      :data="uploadData"
      :multiple="multiple"
      :accept="accept"
      :limit="limit"
      :file-list="fileList"
      :before-upload="beforeUpload"
      :on-success="onSuccess"
      :on-error="onError"
      :on-remove="onRemove"
      :on-exceed="onExceed"
      :auto-upload="autoUpload"
      :show-file-list="showFileList"
      :drag="drag"
      class="upload-component"
    >
      <template v-if="drag">
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">
          将文件拖拽到此处，或<em>点击上传</em>
        </div>
      </template>
      <template v-else>
        <el-button type="primary" :loading="uploading">
          <el-icon><plus /></el-icon>
          {{ buttonText }}
        </el-button>
      </template>
      
      <template #tip v-if="showTip">
        <div class="el-upload__tip">
          <slot name="tip">
            {{ tipText }}
          </slot>
        </div>
      </template>
    </el-upload>

    <!-- 文件预览 -->
    <div v-if="previewFiles.length > 0" class="file-preview">
      <div 
        v-for="file in previewFiles" 
        :key="file.uid || file.id"
        class="file-item"
      >
        <div class="file-info">
          <el-icon class="file-icon"><document /></el-icon>
          <span class="file-name">{{ file.name }}</span>
          <span class="file-size">{{ formatFileSize(file.size) }}</span>
        </div>
        <div class="file-actions">
          <el-button 
            v-if="file.url" 
            type="text" 
            size="small"
            @click="previewFile(file)"
          >
            预览
          </el-button>
          <el-button 
            v-if="file.url" 
            type="text" 
            size="small"
            @click="downloadFile(file)"
          >
            下载
          </el-button>
          <el-button 
            type="text" 
            size="small"
            @click="removeFile(file)"
          >
            删除
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, UploadFilled, Document } from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/modules/user'

export default {
  name: 'FileUpload',
  components: {
    Plus,
    UploadFilled,
    Document
  },
  props: {
    modelValue: {
      type: Array,
      default: () => []
    },
    multiple: {
      type: Boolean,
      default: true
    },
    accept: {
      type: String,
      default: '*'
    },
    limit: {
      type: Number,
      default: 10
    },
    maxSize: {
      type: Number,
      default: 10 * 1024 * 1024 // 10MB
    },
    autoUpload: {
      type: Boolean,
      default: true
    },
    showFileList: {
      type: Boolean,
      default: false
    },
    drag: {
      type: Boolean,
      default: false
    },
    buttonText: {
      type: String,
      default: '选择文件'
    },
    tipText: {
      type: String,
      default: ''
    },
    uploadPath: {
      type: String,
      default: 'tasks'
    }
  },
  emits: ['update:modelValue', 'change', 'upload-success', 'upload-error'],
  setup(props, { emit }) {
    const userStore = useUserStore()
    const uploadRef = ref()
    const uploading = ref(false)
    const fileList = ref([])

    const uploadUrl = computed(() => `/api/files/upload`)
    const uploadHeaders = computed(() => ({
      'Authorization': `Bearer ${userStore.token}`
    }))
    const uploadData = computed(() => ({
      path: props.uploadPath
    }))

    const showTip = computed(() => !!props.tipText)
    const previewFiles = computed(() => props.modelValue || [])

    const beforeUpload = (file) => {
      if (file.size > props.maxSize) {
        ElMessage.error(`文件大小不能超过 ${formatFileSize(props.maxSize)}`)
        return false
      }
      uploading.value = true
      return true
    }

    const onSuccess = (response, file) => {
      uploading.value = false
      const newFile = {
        id: response.data?.id || Date.now(),
        name: file.name,
        size: file.size,
        url: response.data?.url,
        type: file.type,
        uid: file.uid
      }
      
      const updatedFiles = [...(props.modelValue || []), newFile]
      emit('update:modelValue', updatedFiles)
      emit('change', updatedFiles)
      emit('upload-success', newFile, response)
      
      ElMessage.success('文件上传成功')
    }

    const onError = (error, file) => {
      uploading.value = false
      console.error('文件上传失败:', error)
      emit('upload-error', error, file)
      ElMessage.error('文件上传失败')
    }

    const onRemove = (file) => {
      const updatedFiles = (props.modelValue || []).filter(f => f.uid !== file.uid)
      emit('update:modelValue', updatedFiles)
      emit('change', updatedFiles)
    }

    const onExceed = () => {
      ElMessage.warning(`最多只能上传 ${props.limit} 个文件`)
    }

    const removeFile = (file) => {
      const updatedFiles = (props.modelValue || []).filter(f => 
        (f.id && f.id !== file.id) || (f.uid && f.uid !== file.uid)
      )
      emit('update:modelValue', updatedFiles)
      emit('change', updatedFiles)
    }

    const previewFile = (file) => {
      if (file.url) {
        window.open(file.url, '_blank')
      }
    }

    const downloadFile = (file) => {
      if (file.url) {
        const link = document.createElement('a')
        link.href = file.url
        link.download = file.name
        link.click()
      }
    }

    const formatFileSize = (size) => {
      if (!size) return '0 B'
      const units = ['B', 'KB', 'MB', 'GB']
      let index = 0
      while (size >= 1024 && index < units.length - 1) {
        size /= 1024
        index++
      }
      return `${size.toFixed(1)} ${units[index]}`
    }

    watch(() => props.modelValue, (newValue) => {
      // 同步外部文件列表到上传组件
      fileList.value = (newValue || []).map(file => ({
        name: file.name,
        url: file.url,
        uid: file.uid || file.id
      }))
    }, { immediate: true })

    return {
      uploadRef,
      uploading,
      fileList,
      uploadUrl,
      uploadHeaders,
      uploadData,
      showTip,
      previewFiles,
      beforeUpload,
      onSuccess,
      onError,
      onRemove,
      onExceed,
      removeFile,
      previewFile,
      downloadFile,
      formatFileSize
    }
  }
}
</script>

<style scoped>
.file-upload {
  width: 100%;
}

.upload-component {
  width: 100%;
}

.file-preview {
  margin-top: 16px;
}

.file-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  margin-bottom: 8px;
  background-color: #fafafa;
}

.file-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.file-icon {
  margin-right: 8px;
  color: #606266;
}

.file-name {
  font-weight: 500;
  margin-right: 12px;
}

.file-size {
  color: #909399;
  font-size: 12px;
}

.file-actions {
  display: flex;
  gap: 8px;
}
</style>