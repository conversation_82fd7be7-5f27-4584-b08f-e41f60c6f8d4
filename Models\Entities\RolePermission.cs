// File: Models/Entities/RolePermission.cs
// Description: 角色权限关联实体类

#nullable enable

namespace ItAssetsSystem.Models.Entities
{
    /// <summary>
    /// 角色权限关联实体类
    /// </summary>
    public class RolePermission
    {
        /// <summary>
        /// 角色ID
        /// </summary>
        public int RoleId { get; set; }

        /// <summary>
        /// 权限ID
        /// </summary>
        public int PermissionId { get; set; }

        /// <summary>
        /// 角色导航属性
        /// </summary>
        public virtual Role Role { get; set; } = null!;

        /// <summary>
        /// 权限导航属性
        /// </summary>
        public virtual Permission Permission { get; set; } = null!;
    }
} 