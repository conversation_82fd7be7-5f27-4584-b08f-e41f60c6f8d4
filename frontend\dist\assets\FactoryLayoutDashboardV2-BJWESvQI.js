import{_ as a,r as e,c as l,z as s,A as t,b as n,d as o,e as i,$ as u,p as d,w as r,t as c,n as v,F as p,h as m,i as h,E as g,a as f,o as y,f as w,Q as b,v as k,k as x,al as z,S as M}from"./index-CG5lHOPO.js";const V={class:"factory-dashboard-v2"},_={class:"dashboard-header"},$={class:"header-left"},C={class:"dashboard-title"},I={class:"header-center"},D={class:"status-overview"},U={class:"status-item operational"},E={class:"count"},N={class:"status-item warning"},R={class:"count"},T={class:"status-item error"},j={class:"count"},A={class:"status-item idle"},L={class:"count"},S={class:"header-right"},F={class:"time-display"},W={class:"current-time"},B={class:"last-update"},P={class:"dashboard-main"},Q={class:"control-panel"},X={class:"panel-section"},Y={class:"zone-filters"},q={class:"panel-section"},G={class:"status-filters"},H={class:"panel-section"},J={class:"display-settings"},K={class:"factory-layout-container"},O={class:"layout-header"},Z={class:"layout-stats"},aa=["onMouseenter"],ea=["onClick","onMouseenter"],la={class:"cell-content"},sa={key:2,class:"workstation-number"},ta={class:"detail-panel"},na={class:"panel-section"},oa={class:"monitoring-cards"},ia={class:"monitor-card"},ua={class:"card-value"},da={class:"monitor-card"},ra={class:"card-value"},ca={class:"monitor-card"},va={class:"card-value"},pa={key:0,class:"panel-section"},ma={class:"workstation-details"},ha={class:"detail-item"},ga={class:"value"},fa={class:"detail-item"},ya={class:"value"},wa={class:"detail-item"},ba={class:"detail-item"},ka={class:"value"},xa={class:"detail-item"},za={class:"value"},Ma={class:"detail-item"},Va={class:"value"},_a={class:"panel-section"},$a={class:"alert-list"},Ca={class:"alert-time"},Ia={class:"alert-message"},Da={key:0,class:"workstation-dialog-content"},Ua={class:"tooltip-content"},Ea={class:"tooltip-title"},Na={class:"tooltip-info"},Ra=a({__name:"FactoryLayoutDashboardV2",setup(a){const Ra=e(!0),Ta=e(!1),ja=e(""),Aa=e(new Date),La=e(null),Sa=e(null),Fa=e([]),Wa=e(null),Ba=e(null),Pa=e(!0),Qa=e([]),Xa=e(["operational","warning","error","idle"]),Ya=e(!0),qa=e(!0),Ga=e(!0),Ha=e(!1),Ja=e(!1),Ka=e(null),Oa=e({}),Za=e([]),ae=()=>{const a=[];Sa.value.zones.forEach((e=>{const l=e.rows*e.cols;for(let s=0;s<l;s++){const l=e.startWorkstation+s;a.push({id:l,zoneId:e.id,zoneName:e.name,zoneColor:e.color,status:le(),efficiency:Math.floor(30*Math.random())+70,uptime:Math.floor(20*Math.random())+80,taskCount:Math.floor(8*Math.random())+1,assetCount:Math.floor(5*Math.random())+2,lastUpdate:new Date})}})),Fa.value=a,Qa.value=Sa.value.zones.map((a=>a.id))},ee=()=>{Sa.value={zones:[{id:1,name:"默认区域",x:100,y:100,width:200,height:150,rows:3,cols:3,color:"#4A90E2",startWorkstation:1}]},ae()},le=()=>{const a=["operational","warning","error","idle"],e=[.7,.15,.1,.05],l=Math.random();let s=0;for(let t=0;t<e.length;t++)if(s+=e[t],l<=s)return a[t];return"operational"},se=l((()=>{var a;return(null==(a=Sa.value)?void 0:a.zones)||[]})),te=l((()=>Sa.value?Sa.value.zones.filter((a=>Pa.value||Qa.value.includes(a.id))):[])),ne=l((()=>Fa.value.filter((a=>{const e=Pa.value||Qa.value.includes(a.zoneId),l=Xa.value.includes(a.status);return e&&l})))),oe=l((()=>{const a={operational:0,warning:0,error:0,idle:0};return ne.value.forEach((e=>{a[e.status]++})),a})),ie=l((()=>ne.value.length)),ue=l((()=>te.value.length)),de=l((()=>{const a=ne.value.length,e=ne.value.filter((a=>"idle"!==a.status)).length;return a>0?Math.round(e/a*100):0})),re=l((()=>{const a=ne.value.length;if(0===a)return 0;const e=ne.value.reduce(((a,e)=>a+e.efficiency),0);return Math.round(e/a)})),ce=l((()=>{const a=ne.value.length;if(0===a)return 0;const e=ne.value.reduce(((a,e)=>a+e.uptime),0);return Math.round(e/a)})),ve=l((()=>Math.round(20*Math.random()+75))),pe=l((()=>{var a;if(!(null==(a=Sa.value)?void 0:a.zones))return{};let e=0,l=0;return Sa.value.zones.forEach((a=>{e=Math.max(e,a.x+a.width),l=Math.max(l,a.y+a.height)})),{width:`${e+50}px`,height:`${l+50}px`,position:"relative",background:"linear-gradient(135deg, #0f172a 0%, #1e293b 100%)",border:"2px solid #334155",borderRadius:"8px"}})),me=a=>({position:"absolute",left:`${a.x}px`,top:`${a.y}px`,width:`${a.width}px`,height:`${a.height}px`,border:`2px solid ${a.color}`,borderRadius:"6px",backgroundColor:`${a.color}15`,transition:Ga.value?"all 0.3s ease":"none"}),he=a=>({display:"grid",gridTemplateRows:`repeat(${a.rows}, 1fr)`,gridTemplateColumns:`repeat(${a.cols}, 1fr)`,gap:`${a.gapY||2}px ${a.gapX||2}px`,width:"100%",height:"100%",padding:"4px"}),ge=a=>Fa.value.filter((e=>e.zoneId===a.id)),fe=a=>{var e;return["workstation",`status-${a.status}`,{selected:(null==(e=Wa.value)?void 0:e.id)===a.id},{animated:Ga.value}]},ye=a=>({operational:"正常运行",warning:"警告",error:"故障",idle:"空闲"}[a]||"未知"),we=async()=>{Ta.value=!0;try{await new Promise((a=>setTimeout(a,1e3))),ae(),Aa.value=new Date,g.success("数据刷新成功")}catch(a){g.error("数据刷新失败")}finally{Ta.value=!1}},be=a=>{var e;Qa.value=a&&(null==(e=Sa.value)?void 0:e.zones.map((a=>a.id)))||[]},ke=a=>{var e;Pa.value=a.length===((null==(e=Sa.value)?void 0:e.zones.length)||0)},xe=()=>{Ba.value=null},ze=()=>{Ja.value=!1,Ka.value=null},Me=a=>a.toLocaleTimeString("zh-CN",{hour:"2-digit",minute:"2-digit",second:"2-digit"}),Ve=()=>{ja.value=(new Date).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"})};return s((async()=>{await(async()=>{try{Ra.value=!0;const a=await fetch("/analyresport/factory-layout-1748945213262.json");if(!a.ok)throw new Error("配置文件加载失败");const e=await a.json();Sa.value=e,ae(),g.success("布局配置加载成功")}catch(a){g.error("加载配置失败，使用默认数据"),ee()}finally{Ra.value=!1}})(),(()=>{const a=[{level:"error",message:"工位异常停机"},{level:"warning",message:"设备温度过高"},{level:"warning",message:"生产效率下降"},{level:"error",message:"传感器通信故障"}];Za.value=Array.from({length:5},((e,l)=>{const s=a[Math.floor(Math.random()*a.length)];return{id:l+1,level:s.level,message:s.message,time:new Date(Date.now()-36e5*Math.random())}})).sort(((a,e)=>e.time-a.time))})(),Ve();const a=setInterval(Ve,1e3),e=setInterval((()=>{Aa.value=new Date}),3e4);t((()=>{clearInterval(a),clearInterval(e)}))})),(a,e)=>{var l,s,t,g,Ra;const Sa=f("el-icon"),Fa=f("el-button"),ae=f("el-checkbox"),ee=f("el-checkbox-group"),le=f("el-switch"),ne=f("el-dialog");return y(),n("div",V,[o("div",_,[o("div",$,[o("h1",C,[i(Sa,{class:"title-icon"},{default:r((()=>[i(w(b))])),_:1}),e[7]||(e[7]=d(" 智能制造监控系统 V2.0 "))]),e[8]||(e[8]=o("div",{class:"subtitle"},"基于版本 ef7aee39-e800-4bb8-8878-3d453576c3fc",-1))]),o("div",I,[o("div",D,[o("div",U,[o("span",E,c(oe.value.operational),1),e[9]||(e[9]=o("span",{class:"label"},"正常运行",-1))]),o("div",N,[o("span",R,c(oe.value.warning),1),e[10]||(e[10]=o("span",{class:"label"},"警告",-1))]),o("div",T,[o("span",j,c(oe.value.error),1),e[11]||(e[11]=o("span",{class:"label"},"故障",-1))]),o("div",A,[o("span",L,c(oe.value.idle),1),e[12]||(e[12]=o("span",{class:"label"},"空闲",-1))])])]),o("div",S,[o("div",F,[o("div",W,c(ja.value),1),o("div",B,"更新: "+c(Me(Aa.value)),1)]),i(Fa,{onClick:we,loading:Ta.value,type:"primary",size:"small"},{default:r((()=>[i(Sa,null,{default:r((()=>[i(w(k))])),_:1}),e[13]||(e[13]=d(" 刷新数据 "))])),_:1},8,["loading"])])]),o("div",P,[o("div",Q,[o("div",X,[e[15]||(e[15]=o("h3",null,"区域筛选",-1)),o("div",Y,[i(ae,{modelValue:Pa.value,"onUpdate:modelValue":e[0]||(e[0]=a=>Pa.value=a),onChange:be},{default:r((()=>e[14]||(e[14]=[d("全部区域")]))),_:1},8,["modelValue"]),i(ee,{modelValue:Qa.value,"onUpdate:modelValue":e[1]||(e[1]=a=>Qa.value=a),onChange:ke},{default:r((()=>[(y(!0),n(p,null,m(se.value,(a=>(y(),x(ae,{key:a.id,label:a.id,style:v({color:a.color})},{default:r((()=>[d(c(a.name),1)])),_:2},1032,["label","style"])))),128))])),_:1},8,["modelValue"])])]),o("div",q,[e[20]||(e[20]=o("h3",null,"状态筛选",-1)),o("div",G,[i(ee,{modelValue:Xa.value,"onUpdate:modelValue":e[2]||(e[2]=a=>Xa.value=a)},{default:r((()=>[i(ae,{label:"operational"},{default:r((()=>e[16]||(e[16]=[d("正常运行")]))),_:1}),i(ae,{label:"warning"},{default:r((()=>e[17]||(e[17]=[d("警告")]))),_:1}),i(ae,{label:"error"},{default:r((()=>e[18]||(e[18]=[d("故障")]))),_:1}),i(ae,{label:"idle"},{default:r((()=>e[19]||(e[19]=[d("空闲")]))),_:1})])),_:1},8,["modelValue"])])]),o("div",H,[e[21]||(e[21]=o("h3",null,"显示设置",-1)),o("div",J,[i(le,{modelValue:Ya.value,"onUpdate:modelValue":e[3]||(e[3]=a=>Ya.value=a),"active-text":"显示工位号"},null,8,["modelValue"]),i(le,{modelValue:qa.value,"onUpdate:modelValue":e[4]||(e[4]=a=>qa.value=a),"active-text":"显示区域标签"},null,8,["modelValue"]),i(le,{modelValue:Ga.value,"onUpdate:modelValue":e[5]||(e[5]=a=>Ga.value=a),"active-text":"启用动画"},null,8,["modelValue"])])])]),o("div",K,[o("div",O,[e[22]||(e[22]=o("h2",null,"工厂布局监控",-1)),o("div",Z,[o("span",null,"总工位: "+c(ie.value),1),o("span",null,"活跃区域: "+c(ue.value),1),o("span",null,"在线率: "+c(de.value)+"%",1)])]),o("div",{class:"factory-canvas",ref_key:"canvasRef",ref:La},[o("div",{class:"canvas-container",style:v(pe.value)},[(y(!0),n(p,null,m(te.value,(a=>(y(),n("div",{key:a.id,class:h(["zone-container",[`zone-${a.id}`,{"zone-highlighted":Ba.value===a.id}]]),style:v(me(a)),onMouseenter:e=>{return l=a.id,void(Ba.value=l);var l},onMouseleave:xe},[qa.value?(y(),n("div",{key:0,class:"zone-label",style:v({color:a.color})},c(a.name),5)):u("",!0),o("div",{class:"workstation-grid",style:v(he(a))},[(y(!0),n(p,null,m(ge(a),(e=>(y(),n("div",{key:e.id,class:h(["workstation-cell",fe(e)]),style:v({backgroundColor:a.color}),onClick:a=>(a=>{Wa.value=a,Ha.value=!0})(e),onMouseenter:a=>((a,e)=>{Ka.value=a,Ja.value=!0;const l=e.target.getBoundingClientRect();Oa.value={position:"fixed",left:`${l.right+10}px`,top:`${l.top}px`,zIndex:9999}})(e,a),onMouseleave:ze},[o("div",la,["error"===e.status?(y(),x(Sa,{key:0,class:"status-icon error"},{default:r((()=>[i(w(z))])),_:1})):"warning"===e.status?(y(),x(Sa,{key:1,class:"status-icon warning"},{default:r((()=>[i(w(M))])),_:1})):u("",!0),Ya.value?(y(),n("span",sa,c(e.id),1)):u("",!0)])],46,ea)))),128))],4)],46,aa)))),128))],4)],512)]),o("div",ta,[o("div",na,[e[29]||(e[29]=o("h3",null,"实时监控",-1)),o("div",oa,[o("div",ia,[e[23]||(e[23]=o("div",{class:"card-title"},"设备效率",-1)),o("div",ua,c(re.value)+"%",1),e[24]||(e[24]=o("div",{class:"card-trend up"},"↑ 2.3%",-1))]),o("div",da,[e[25]||(e[25]=o("div",{class:"card-title"},"运行时间",-1)),o("div",ra,c(ce.value)+"%",1),e[26]||(e[26]=o("div",{class:"card-trend down"},"↓ 0.8%",-1))]),o("div",ca,[e[27]||(e[27]=o("div",{class:"card-title"},"产能利用率",-1)),o("div",va,c(ve.value)+"%",1),e[28]||(e[28]=o("div",{class:"card-trend up"},"↑ 1.5%",-1))])])]),Wa.value?(y(),n("div",pa,[e[36]||(e[36]=o("h3",null,"工位详情",-1)),o("div",ma,[o("div",ha,[e[30]||(e[30]=o("span",{class:"label"},"工位编号:",-1)),o("span",ga,c(Wa.value.id),1)]),o("div",fa,[e[31]||(e[31]=o("span",{class:"label"},"所属区域:",-1)),o("span",ya,c(Wa.value.zoneName),1)]),o("div",wa,[e[32]||(e[32]=o("span",{class:"label"},"运行状态:",-1)),o("span",{class:h(["value",Wa.value.status])},c(ye(Wa.value.status)),3)]),o("div",ba,[e[33]||(e[33]=o("span",{class:"label"},"设备效率:",-1)),o("span",ka,c(Wa.value.efficiency)+"%",1)]),o("div",xa,[e[34]||(e[34]=o("span",{class:"label"},"运行时间:",-1)),o("span",za,c(Wa.value.uptime)+"%",1)]),o("div",Ma,[e[35]||(e[35]=o("span",{class:"label"},"任务数量:",-1)),o("span",Va,c(Wa.value.taskCount),1)])])])):u("",!0),o("div",_a,[e[37]||(e[37]=o("h3",null,"告警信息",-1)),o("div",$a,[(y(!0),n(p,null,m(Za.value,(a=>(y(),n("div",{key:a.id,class:h(["alert-item",a.level])},[o("div",Ca,c(Me(a.time)),1),o("div",Ia,c(a.message),1)],2)))),128))])])])]),i(ne,{modelValue:Ha.value,"onUpdate:modelValue":e[6]||(e[6]=a=>Ha.value=a),title:`工位 ${null==(l=Wa.value)?void 0:l.id} 详细信息`,width:"600px","close-on-click-modal":!1},{default:r((()=>[Wa.value?(y(),n("div",Da,e[38]||(e[38]=[o("p",null,"工位详细信息对话框内容...",-1)]))):u("",!0)])),_:1},8,["modelValue","title"]),Ja.value?(y(),n("div",{key:0,class:"workstation-tooltip",style:v(Oa.value)},[o("div",Ua,[o("div",Ea,"工位 "+c(null==(s=Ka.value)?void 0:s.id),1),o("div",Na,[o("div",null,"区域: "+c(null==(t=Ka.value)?void 0:t.zoneName),1),o("div",null,"状态: "+c(ye(null==(g=Ka.value)?void 0:g.status)),1),o("div",null,"效率: "+c(null==(Ra=Ka.value)?void 0:Ra.efficiency)+"%",1)])])],4)):u("",!0)])}}},[["__scopeId","data-v-5f68eeeb"]]);export{Ra as default};
