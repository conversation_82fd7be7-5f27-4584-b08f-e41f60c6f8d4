@echo off
chcp 65001 >nul
echo ========================================
echo Simple Notification Client Build Script
echo ========================================

:: Check if CMake exists
cmake --version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ERROR: CMake not found, please install CMake first
    echo.
    echo Download CMake from: https://cmake.org/download/
    pause
    exit /b 1
)

:: Check if Visual Studio Build Tools exist
where cl >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ERROR: Visual Studio Build Tools not found
    echo.
    echo Please install one of the following:
    echo - Visual Studio 2019 or later
    echo - Visual Studio Build Tools 2019 or later
    echo - Run this from Developer Command Prompt
    echo.
    pause
    exit /b 1
)

echo CMake and build tools found successfully

:: Create build directory
if not exist build_simple (
    mkdir build_simple
    echo Created build directory: build_simple
)

cd build_simple

:: Configure project with simple CMakeLists
echo Configuring project...
cmake .. -f ../CMakeLists_simple.txt -G "Visual Studio 16 2019" -A x64
if %ERRORLEVEL% neq 0 (
    echo Trying with different generator...
    cmake .. -f ../CMakeLists_simple.txt -G "Visual Studio 17 2022" -A x64
    if %ERRORLEVEL% neq 0 (
        echo Trying with NMake...
        cmake .. -f ../CMakeLists_simple.txt -G "NMake Makefiles"
        if %ERRORLEVEL% neq 0 (
            echo ERROR: All CMake configuration attempts failed
            echo.
            echo Available generators:
            cmake --help
            pause
            exit /b 1
        )
    )
)

:: Build project
echo Building project...
cmake --build . --config Release
if %ERRORLEVEL% neq 0 (
    echo ERROR: Build failed
    echo.
    echo Build log should be above. Common issues:
    echo 1. Missing Windows SDK
    echo 2. Incompatible Visual Studio version
    echo 3. Missing dependencies
    pause
    exit /b 1
)

:: Create necessary directories
if not exist bin\Release\logs (
    mkdir bin\Release\logs
    echo Created logs directory
)

echo.
echo ========================================
echo Build completed successfully!
echo.
echo Executable locations:
if exist bin\Release\SimpleNotificationDemo.exe (
    echo - Demo client: build_simple\bin\Release\SimpleNotificationDemo.exe
)
if exist bin\NotificationClientMinimal.exe (
    echo - Minimal client: build_simple\bin\NotificationClientMinimal.exe
)
echo.
echo Usage:
echo   cd build_simple\bin\Release
echo   SimpleNotificationDemo.exe --help
echo.
echo ========================================

:: Create run script
if exist bin\Release (
    cd bin\Release
    echo @echo off > run_demo.bat
    echo echo Starting Simple Notification Client Demo... >> run_demo.bat
    echo SimpleNotificationDemo.exe >> run_demo.bat
    echo pause >> run_demo.bat
    echo Created run_demo.bat
    cd ..\..
)

pause