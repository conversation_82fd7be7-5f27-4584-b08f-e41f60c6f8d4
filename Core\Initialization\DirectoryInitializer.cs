using System;
using System.IO;
using ItAssetsSystem.Core.Helpers;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace ItAssetsSystem.Core.Initialization
{
    /// <summary>
    /// 目录初始化服务
    /// </summary>
    public class DirectoryInitializer
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<DirectoryInitializer> _logger;
        
        public DirectoryInitializer(IConfiguration configuration, ILogger<DirectoryInitializer> logger)
        {
            _configuration = configuration;
            _logger = logger;
        }
        
        /// <summary>
        /// 初始化所有系统必要目录
        /// </summary>
        public void Initialize()
        {
            try
            {
                _logger.LogInformation("开始初始化系统目录...");
                
                // 创建数据目录
                CreateDataDirectory();
                
                // 创建离线队列目录
                CreateOfflineQueueDirectory();
                
                // 创建备份目录
                CreateBackupDirectory();
                
                // 创建导入导出目录
                CreateImportExportDirectories();
                
                // 创建日志目录
                CreateLogsDirectory();
                
                // 创建临时文件目录
                CreateTempDirectory();
                
                _logger.LogInformation("系统目录初始化完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "初始化系统目录时发生错误");
                throw;
            }
        }
        
        private void CreateDataDirectory()
        {
            string path = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data");
            FileHelper.EnsureDirectoryExists(path, _logger);
        }
        
        private void CreateOfflineQueueDirectory()
        {
            string path = _configuration.GetValue<string>("OfflineQueue:StoragePath");
            if (string.IsNullOrEmpty(path))
            {
                path = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data", "OfflineOperations");
            }
            
            FileHelper.EnsureDirectoryExists(path, _logger);
        }
        
        private void CreateBackupDirectory()
        {
            string path = _configuration.GetValue<string>("Backup:BackupStoragePath");
            if (string.IsNullOrEmpty(path))
            {
                path = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data", "Backups");
            }
            
            FileHelper.EnsureDirectoryExists(path, _logger);
        }
        
        private void CreateImportExportDirectories()
        {
            // 导入目录
            string importPath = _configuration.GetValue<string>("Import:ImportFilesPath");
            if (string.IsNullOrEmpty(importPath))
            {
                importPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data", "Import");
            }
            FileHelper.EnsureDirectoryExists(importPath, _logger);
            
            // 模板目录
            string templatesPath = _configuration.GetValue<string>("Import:TemplatesPath");
            if (string.IsNullOrEmpty(templatesPath))
            {
                templatesPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data", "Templates");
            }
            FileHelper.EnsureDirectoryExists(templatesPath, _logger);
            
            // 导出目录
            string exportPath = _configuration.GetValue<string>("Export:ExportFilesPath");
            if (string.IsNullOrEmpty(exportPath))
            {
                exportPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data", "Export");
            }
            FileHelper.EnsureDirectoryExists(exportPath, _logger);
        }
        
        private void CreateLogsDirectory()
        {
            string path = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Logs");
            FileHelper.EnsureDirectoryExists(path, _logger);
        }
        
        private void CreateTempDirectory()
        {
            string path = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Temp");
            FileHelper.EnsureDirectoryExists(path, _logger);
        }
    }
} 