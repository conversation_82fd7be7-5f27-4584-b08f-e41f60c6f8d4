/**
 * IT资产管理系统 - 全局样式文件
 * 文件路径: src/styles/global.scss
 * 功能描述: 全系统通用样式定义，包括重置样式和公共样式类
 */

/* 引入变量定义 */
@use "./variables.scss" as *;

/* 重置基础样式 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html, body {
  height: 100%;
  width: 100%;
  font-family: var(--font-family-base);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: var(--font-size-base);
  color: var(--el-text-color-primary);
  background-color: var(--el-bg-color-page);
  line-height: var(--line-height-base);
}

#app {
  height: 100%; /* 确保根元素高度 */
}

/* 清除浮动 */
.clearfix:after {
  content: "";
  display: table;
  clear: both;
}

/* 通用布局类 */
.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.justify-between {
  justify-content: space-between;
}

.justify-center {
  justify-content: center;
}

.align-center {
  align-items: center;
}

.align-start {
  align-items: flex-start;
}

.flex-1 {
  flex: 1;
}

/* 通用边距类 */
.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 4px; }
.mt-2 { margin-top: 8px; }
.mt-3 { margin-top: 12px; }
.mt-4 { margin-top: 16px; }
.mt-5 { margin-top: 20px; }

.mr-0 { margin-right: 0; }
.mr-1 { margin-right: 4px; }
.mr-2 { margin-right: 8px; }
.mr-3 { margin-right: 12px; }
.mr-4 { margin-right: 16px; }
.mr-5 { margin-right: 20px; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 4px; }
.mb-2 { margin-bottom: 8px; }
.mb-3 { margin-bottom: 12px; }
.mb-4 { margin-bottom: 16px; }
.mb-5 { margin-bottom: 20px; }

.ml-0 { margin-left: 0; }
.ml-1 { margin-left: 4px; }
.ml-2 { margin-left: 8px; }
.ml-3 { margin-left: 12px; }
.ml-4 { margin-left: 16px; }
.ml-5 { margin-left: 20px; }

.pa-0 { padding: 0; }
.pa-1 { padding: 4px; }
.pa-2 { padding: 8px; }
.pa-3 { padding: 12px; }
.pa-4 { padding: 16px; }
.pa-5 { padding: 20px; }

/* 文本样式类 */
.text-primary { color: var(--el-color-primary); }
.text-success { color: var(--el-color-success); }
.text-warning { color: var(--el-color-warning); }
.text-danger { color: var(--el-color-danger); }
.text-info { color: var(--el-color-info); }

.text-disabled { color: var(--el-text-color-disabled); }
.text-regular { color: var(--el-text-color-regular); }
.text-secondary { color: var(--el-text-color-secondary); }

.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }

.text-bold { font-weight: bold; }
.text-normal { font-weight: normal; }

.text-xs { font-size: 12px; }
.text-sm { font-size: 13px; }
.text-md { font-size: 14px; }
.text-lg { font-size: 16px; }
.text-xl { font-size: 18px; }

/* 边框样式类 */
.border {
  border: 1px solid var(--el-border-color);
}

.border-top {
  border-top: 1px solid var(--el-border-color);
}

.border-right {
  border-right: 1px solid var(--el-border-color);
}

.border-bottom {
  border-bottom: 1px solid var(--el-border-color);
}

.border-left {
  border-left: 1px solid var(--el-border-color);
}

.rounded {
  border-radius: 4px;
}

.rounded-circle {
  border-radius: 50%;
}

/* 阴影类 */
.shadow-sm {
  box-shadow: var(--el-box-shadow-lighter);
}

.shadow {
  box-shadow: var(--el-box-shadow-light);
}

.shadow-lg {
  box-shadow: var(--el-box-shadow);
}

/* 页面容器 */
.page-container {
  padding: 20px;
  height: 100%;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--el-bg-color-page);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 表格样式 */
.el-table {
  .cell {
    padding: 8px;
  }
  
  th {
    background-color: var(--el-fill-color-light);
    color: var(--el-text-color-primary);
    font-weight: 600;
  }
  
  .el-button--small {
    padding: 6px 8px;
  }
}

a {
  text-decoration: none;
  color: var(--el-color-primary);
  &:hover {
    color: var(--el-color-primary-light-1);
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-thumb {
  background-color: rgba(144, 147, 153, 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-track {
  background-color: transparent;
}

/* 通用工具类 */
.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.text-overflow {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 间距类 */
.mt-xs { margin-top: 4px; }
.mt-sm { margin-top: 8px; }
.mt { margin-top: 16px; }
.mt-md { margin-top: 16px; }
.mt-lg { margin-top: 24px; }
.mt-xl { margin-top: 32px; }

.mb-xs { margin-bottom: 4px; }
.mb-sm { margin-bottom: 8px; }
.mb { margin-bottom: 16px; }
.mb-md { margin-bottom: 16px; }
.mb-lg { margin-bottom: 24px; }
.mb-xl { margin-bottom: 32px; }

.ml-xs { margin-left: 4px; }
.ml-sm { margin-left: 8px; }
.ml { margin-left: 16px; }
.ml-md { margin-left: 16px; }
.ml-lg { margin-left: 24px; }
.ml-xl { margin-left: 32px; }

.mr-xs { margin-right: 4px; }
.mr-sm { margin-right: 8px; }
.mr { margin-right: 16px; }
.mr-md { margin-right: 16px; }
.mr-lg { margin-right: 24px; }
.mr-xl { margin-right: 32px; }

/* 页面内容容器 */
.card-container {
  background-color: var(--el-bg-color);
  border-radius: 4px;
  box-shadow: var(--el-box-shadow-light);
  padding: 16px;
}

/* 页面标题 */
.page-title {
  font-size: 20px;
  font-weight: bold;
  color: var(--el-text-color-primary);
  margin-bottom: 16px;
  border-bottom: 1px solid var(--el-border-color-light);
  padding-bottom: 8px;
}

/* 工具栏 */
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

/* 数据表格 */
.data-table {
  margin-top: 16px;
}

/* 分页 */
.pagination-container {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}

/* 状态标签 */
.status-tag {
  padding: 2px 8px;
  border-radius: 2px;
  font-size: 13px;
  
  &.success {
    background-color: rgba(103, 194, 58, 0.1);
    color: var(--el-color-success);
  }
  
  &.warning {
    background-color: rgba(230, 162, 60, 0.1);
    color: var(--el-color-warning);
  }
  
  &.danger {
    background-color: rgba(245, 108, 108, 0.1);
    color: var(--el-color-danger);
  }
  
  &.info {
    background-color: rgba(144, 147, 153, 0.1);
    color: var(--el-color-info);
  }
}

/* 表单容器 */
.form-container {
  max-width: 800px;
  margin: 0 auto;
}

/* 搜索表单 */
.search-form {
  padding: 16px;
  margin-bottom: 16px;
  background-color: var(--el-bg-color);
  border-radius: 4px;
}

/* 加载遮罩层 */
.app-loading {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  
  .loading-text {
    margin-top: 16px;
    color: var(--el-color-primary);
  }
}

/* 主题相关样式 */
.theme-switch-enter-active,
.theme-switch-leave-active {
  transition: opacity 0.5s ease;
}

.theme-switch-enter-from,
.theme-switch-leave-to {
  opacity: 0;
}

// 提示主题切换的遮罩
.theme-switch-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s, visibility 0.3s;
  
  &.active {
    opacity: 1;
    visibility: visible;
  }
  
  .theme-message {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    max-width: 90%;
    
    h3 {
      margin-top: 0;
      margin-bottom: 16px;
      font-size: 18px;
    }
    
    p {
      margin: 0;
      color: var(--el-text-color-secondary);
    }
  }
}

.hide {
  display: none;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 间距工具类 */
.mt-10 { margin-top: 10px; }
.mt-15 { margin-top: 15px; }
.mt-20 { margin-top: 20px; }

.mb-10 { margin-bottom: 10px; }
.mb-15 { margin-bottom: 15px; }
.mb-20 { margin-bottom: 20px; }

.ml-10 { margin-left: 10px; }
.ml-15 { margin-left: 15px; }
.ml-20 { margin-left: 20px; }

.mr-10 { margin-right: 10px; }
.mr-15 { margin-right: 15px; }
.mr-20 { margin-right: 20px; }

.p-10 { padding: 10px; }
.p-15 { padding: 15px; }
.p-20 { padding: 20px; }

/* 可滚动容器 */
.scrollable {
  overflow: auto;
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  &::-webkit-scrollbar-thumb {
    background-color: rgba(144, 147, 153, 0.3);
    border-radius: 3px;
  }
  &::-webkit-scrollbar-track {
    background-color: transparent;
  }
}

/* ========================================================================== */
/* == BEGIN: Element Plus 全局样式覆盖 (从系统 B App.vue 或类似文件复制) == */
/* ========================================================================== */

/*
  在这里粘贴从系统 B App.vue 复制的全局 Element Plus 样式覆盖代码。
  这些代码通常使用 :deep() 或直接针对 Element Plus 的类名进行样式调整，
  并应确保使用 variables.scss 中定义的新 CSS 变量。

  例如：
  :deep(.el-button--primary) {
    background-color: var(--el-color-primary);
    border-color: var(--el-color-primary);
    &:hover {
      background-color: var(--el-color-primary-light-1);
      border-color: var(--el-color-primary-light-1);
    }
  }

  .el-card {
    border-radius: var(--border-radius-large);
    box-shadow: var(--box-shadow-light);
  }
*/

/* Element Plus Overrides to match Cek-smart style */
:root { /* Ensure these are applied globally */
  --el-border-radius-base: 6px; // Cek-smart uses slightly rounded corners
  --el-box-shadow-light: 0 2px 12px 0 rgba(0, 0, 0, 0.06); // Softer shadows
}

.el-card {
  border-radius: var(--el-border-radius-large, 10px); // Larger radius for cards
  box-shadow: var(--shadow-light, 0 1px 3px rgba(0,0,0,0.08), 0 1px 2px rgba(0,0,0,0.05));
  border: 1px solid var(--border-color-light, #ECEFF1); // Light border for cards
  background-color: var(--card-bg, #FFFFFF);
}

.el-button--primary {
  background-color: var(--accent-color); // Use accent for primary buttons
  border-color: var(--accent-color);
  &:hover, &:focus {
    background-color: var(--accent-color-light); // A lighter shade for hover
    border-color: var(--accent-color-light);
  }
}
// Add more overrides for .el-input, .el-select, .el-menu-item etc. as needed
// to match Cek-smart's look & feel (e.g., input field styles, sidebar item styles).

// Category specific styles (as suggested in markdown for later use)
// .category-work { background-color: var(--el-color-primary-light-5); color: var(--el-color-primary); border: 1px solid var(--el-color-primary-light-3); }
// .category-personal { background-color: var(--el-color-success-light-5); color: var(--el-color-success); border: 1px solid var(--el-color-success-light-3); }
// .category-default { background-color: var(--el-color-info-light-5); color: var(--el-color-info); border: 1px solid var(--el-color-info-light-3); }

/* ======================================================================== */
/* == END: Element Plus 全局样式覆盖                                      == */
/* ======================================================================== */


/* ========================================================================== */
/* == BEGIN: 游戏化相关全局样式 (从系统 B App.vue 或类似文件复制)         == */
/* ========================================================================== */

/*
  在这里粘贴从系统 B App.vue 或相关样式文件复制的游戏化 UI 组件样式。
  例如：.score-with-icon, .coin-icon, .coin-rain, .level-badge, .xp-bar 等。
  确保这些样式使用了 variables.scss 中定义的 --color-*, --font-* 等变量。

  示例：
  .score-with-icon {
    display: inline-flex;
    align-items: center;
    color: var(--color-coin);
    font-weight: bold;
    .coin-icon {
      width: 1.2em;
      height: 1.2em;
      margin-right: 4px;
      vertical-align: middle; // 可选
    }
  }

  .xp-bar {
    height: 8px;
    background-color: var(--el-border-color-lighter);
    border-radius: 4px;
    overflow: hidden;
    .xp-bar-fill {
      height: 100%;
      background-color: var(--color-xp);
      transition: width var(--transition-duration) var(--transition-timing-function);
    }
  }
*/

/* ======================================================================== */
/* == END: 游戏化相关全局样式                                             == */
/* ======================================================================== */


/* ========================================================================== */
/* == BEGIN: 游戏化相关全局样式 (从系统 B App.vue 或类似文件复制)         == */
/* ========================================================================== */

/*
  在这里粘贴从系统 B App.vue 或相关样式文件复制的游戏化 UI 组件样式。
  例如：.score-with-icon, .coin-icon, .coin-rain, .level-badge, .xp-bar 等。
  确保这些样式使用了 variables.scss 中定义的 --color-*, --font-* 等变量。

  示例：
  .score-with-icon {
    display: inline-flex;
    align-items: center;
    color: var(--color-coin);
    font-weight: bold;
    .coin-icon {
      width: 1.2em;
      height: 1.2em;
      margin-right: 4px;
      vertical-align: middle; // 可选
    }
  }

  .xp-bar {
    height: 8px;
    background-color: var(--el-border-color-lighter);
    border-radius: 4px;
    overflow: hidden;
    .xp-bar-fill {
      height: 100%;
      background-color: var(--color-xp);
      transition: width var(--transition-duration) var(--transition-timing-function);
    }
  }
*/

/* ======================================================================== */
/* == END: 游戏化相关全局样式                                             == */
/* ======================================================================== */

/* ======================================================================== */
/* == END: 游戏化相关全局样式                                             == */
/* ======================================================================== */

// 全局修复 el-select 下拉菜单背景色（无论明暗主题都为纯白色）
:root, .dark-theme {
  --el-bg-color-overlay: #fff !important;
}

:deep(.el-select-dropdown), .el-select-dropdown {
  background: #fff !important;
  background-color: #fff !important;
  opacity: 1 !important;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1) !important;
  filter: none !important;
}

:deep(.el-select-dropdown__item), .el-select-dropdown__item {
  background: #fff !important;
  color: #222 !important;
  opacity: 1 !important;
} 