// IT资产管理系统 - 基础操作类
// 文件路径: /Core/Resilience/Operation.cs
// 功能: 实现IOperation接口，提供基本的操作功能

using System;
using System.Text.Json;
using System.Threading.Tasks;

namespace ItAssetsSystem.Core.Resilience
{
    /// <summary>
    /// 基础操作类，实现IOperation接口
    /// </summary>
    public abstract class Operation : IOperation
    {
        /// <summary>
        /// 操作ID
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// 操作类型
        /// </summary>
        public abstract OperationType Type { get; }

        /// <summary>
        /// 目标实体类型
        /// </summary>
        public abstract string EntityType { get; }

        /// <summary>
        /// 目标实体ID
        /// </summary>
        public abstract string EntityId { get; }

        /// <summary>
        /// 操作数据
        /// </summary>
        public abstract string Data { get; }

        /// <summary>
        /// 操作状态
        /// </summary>
        public OperationStatus Status { get; set; } = OperationStatus.Pending;

        /// <summary>
        /// 优先级
        /// </summary>
        public OperationPriority Priority { get; set; } = OperationPriority.Normal;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; private set; }

        /// <summary>
        /// 上次尝试时间
        /// </summary>
        public DateTime? LastAttemptTime { get; set; }

        /// <summary>
        /// 重试次数
        /// </summary>
        public int RetryCount { get; set; }

        /// <summary>
        /// 最大重试次数
        /// </summary>
        public virtual int MaxRetries => 3;

        /// <summary>
        /// 后台执行标志
        /// </summary>
        public virtual bool IsBackgroundOperation => false;

        /// <summary>
        /// 构造函数
        /// </summary>
        protected Operation()
        {
            Id = Guid.NewGuid().ToString();
            CreatedAt = DateTime.UtcNow;
            Status = OperationStatus.Pending;
            RetryCount = 0;
        }

        /// <summary>
        /// 执行操作
        /// </summary>
        /// <returns>操作结果</returns>
        public abstract Task<OperationResult> ExecuteAsync();

        /// <summary>
        /// 检查操作是否可重试
        /// </summary>
        /// <returns>是否可重试</returns>
        public virtual bool CanRetry()
        {
            return Status == OperationStatus.Failed && RetryCount < MaxRetries;
        }

        /// <summary>
        /// 获取冲突解决策略
        /// </summary>
        /// <returns>冲突解决策略</returns>
        public virtual ConflictResolutionStrategy GetConflictResolutionStrategy()
        {
            return ConflictResolutionStrategy.PreferServer;
        }

        /// <summary>
        /// 序列化为JSON
        /// </summary>
        /// <returns>JSON字符串</returns>
        public virtual string ToJson()
        {
            var operationInfo = new
            {
                Id,
                Type,
                EntityType,
                EntityId,
                Data,
                Status,
                Priority,
                CreatedAt,
                LastAttemptTime,
                RetryCount,
                MaxRetries,
                IsBackgroundOperation,
                TypeName = GetType().AssemblyQualifiedName
            };

            return JsonSerializer.Serialize(operationInfo);
        }

        /// <summary>
        /// 从JSON反序列化
        /// </summary>
        /// <param name="json">JSON字符串</param>
        public virtual void FromJson(string json)
        {
            var options = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            };

            var operationInfo = JsonSerializer.Deserialize<OperationInfo>(json, options);

            Id = operationInfo.Id;
            Status = operationInfo.Status;
            Priority = operationInfo.Priority;
            CreatedAt = operationInfo.CreatedAt;
            LastAttemptTime = operationInfo.LastAttemptTime;
            RetryCount = operationInfo.RetryCount;
        }

        /// <summary>
        /// 操作序列化辅助类
        /// </summary>
        protected class OperationInfo
        {
            /// <summary>
            /// 操作ID
            /// </summary>
            public string Id { get; set; }

            /// <summary>
            /// 操作类型
            /// </summary>
            public OperationType Type { get; set; }

            /// <summary>
            /// 实体类型
            /// </summary>
            public string EntityType { get; set; }

            /// <summary>
            /// 实体ID
            /// </summary>
            public string EntityId { get; set; }

            /// <summary>
            /// 操作数据
            /// </summary>
            public string Data { get; set; }

            /// <summary>
            /// 操作状态
            /// </summary>
            public OperationStatus Status { get; set; }

            /// <summary>
            /// 优先级
            /// </summary>
            public OperationPriority Priority { get; set; }

            /// <summary>
            /// 创建时间
            /// </summary>
            public DateTime CreatedAt { get; set; }

            /// <summary>
            /// 上次尝试时间
            /// </summary>
            public DateTime? LastAttemptTime { get; set; }

            /// <summary>
            /// 重试次数
            /// </summary>
            public int RetryCount { get; set; }

            /// <summary>
            /// 最大重试次数
            /// </summary>
            public int MaxRetries { get; set; }

            /// <summary>
            /// 后台执行标志
            /// </summary>
            public bool IsBackgroundOperation { get; set; }

            /// <summary>
            /// 类型名称
            /// </summary>
            public string TypeName { get; set; }
        }
    }

    /// <summary>
    /// 创建操作抽象类
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    public abstract class CreateOperation<T> : Operation
    {
        /// <summary>
        /// 要创建的实体
        /// </summary>
        protected T Entity { get; set; }

        /// <summary>
        /// 操作类型
        /// </summary>
        public override OperationType Type => OperationType.Create;

        /// <summary>
        /// 实体类型
        /// </summary>
        public override string EntityType => typeof(T).Name;

        /// <summary>
        /// 实体ID（创建操作不适用）
        /// </summary>
        public override string EntityId => "new";

        /// <summary>
        /// 操作数据
        /// </summary>
        public override string Data => JsonSerializer.Serialize(Entity);

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="entity">要创建的实体</param>
        protected CreateOperation(T entity)
        {
            Entity = entity ?? throw new ArgumentNullException(nameof(entity));
        }
    }

    /// <summary>
    /// 更新操作抽象类
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    public abstract class UpdateOperation<T> : Operation
    {
        /// <summary>
        /// 要更新的实体ID
        /// </summary>
        protected new string Id { get; set; }

        /// <summary>
        /// 更新后的实体
        /// </summary>
        protected T Entity { get; set; }

        /// <summary>
        /// 操作类型
        /// </summary>
        public override OperationType Type => OperationType.Update;

        /// <summary>
        /// 实体类型
        /// </summary>
        public override string EntityType => typeof(T).Name;

        /// <summary>
        /// 实体ID
        /// </summary>
        public override string EntityId => Id;

        /// <summary>
        /// 操作数据
        /// </summary>
        public override string Data => JsonSerializer.Serialize(Entity);

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="id">实体ID</param>
        /// <param name="entity">更新后的实体</param>
        protected UpdateOperation(string id, T entity)
        {
            Id = id ?? throw new ArgumentNullException(nameof(id));
            Entity = entity ?? throw new ArgumentNullException(nameof(entity));
        }
    }

    /// <summary>
    /// 删除操作抽象类
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    public abstract class DeleteOperation<T> : Operation
    {
        /// <summary>
        /// 要删除的实体ID
        /// </summary>
        protected new string Id { get; set; }

        /// <summary>
        /// 操作类型
        /// </summary>
        public override OperationType Type => OperationType.Delete;

        /// <summary>
        /// 实体类型
        /// </summary>
        public override string EntityType => typeof(T).Name;

        /// <summary>
        /// 实体ID
        /// </summary>
        public override string EntityId => Id;

        /// <summary>
        /// 操作数据
        /// </summary>
        public override string Data => Id;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="id">实体ID</param>
        protected DeleteOperation(string id)
        {
            Id = id ?? throw new ArgumentNullException(nameof(id));
        }
    }
} 