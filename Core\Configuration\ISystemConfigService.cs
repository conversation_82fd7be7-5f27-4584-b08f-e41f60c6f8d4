using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ItAssetsSystem.Core.Configuration
{
    /// <summary>
    /// 系统配置服务接口
    /// </summary>
    public interface ISystemConfigService
    {
        /// <summary>
        /// 获取系统所有配置项
        /// </summary>
        /// <returns>所有配置项的键值对</returns>
        Task<Dictionary<string, object>> GetAllConfigurationsAsync();
        
        /// <summary>
        /// 根据键获取配置值
        /// </summary>
        /// <param name="key">配置键</param>
        /// <typeparam name="T">配置值类型</typeparam>
        /// <returns>配置值</returns>
        Task<T> GetConfigurationValueAsync<T>(string key);
        
        /// <summary>
        /// 更新配置值
        /// </summary>
        /// <param name="key">配置键</param>
        /// <param name="value">配置值</param>
        /// <returns>更新是否成功</returns>
        Task<bool> UpdateConfigurationValueAsync(string key, object value);
        
        /// <summary>
        /// 批量更新配置
        /// </summary>
        /// <param name="configurations">配置键值对</param>
        /// <returns>更新是否成功</returns>
        Task<bool> BulkUpdateConfigurationsAsync(Dictionary<string, object> configurations);
        
        /// <summary>
        /// 重置所有配置为默认值
        /// </summary>
        /// <returns>操作是否成功</returns>
        Task<bool> ResetToDefaultsAsync();
        
        /// <summary>
        /// 导出系统配置
        /// </summary>
        /// <param name="filePath">导出文件路径</param>
        /// <returns>操作是否成功</returns>
        Task<bool> ExportConfigurationsAsync(string filePath);
        
        /// <summary>
        /// 导入系统配置
        /// </summary>
        /// <param name="filePath">导入文件路径</param>
        /// <returns>操作是否成功</returns>
        Task<bool> ImportConfigurationsAsync(string filePath);
    }
} 