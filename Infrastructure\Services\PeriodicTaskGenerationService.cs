// File: Infrastructure/Services/PeriodicTaskGenerationService.cs
// Description: 周期性任务生成服务，负责定期检查和生成新的任务实例

using System;
using System.Threading;
using System.Threading.Tasks;
using System.Collections.Generic;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Cronos;
using ItAssetsSystem.Core.Abstractions;
using ItAssetsSystem.Domain.Entities.Tasks;
using System.Linq;
using TaskEntity = ItAssetsSystem.Domain.Entities.Tasks.Task;
using ItAssetsSystem.Application.Features.Tasks.Dtos;
using ItAssetsSystem.Core.Events;
using ItAssetsSystem.Core.Events.Tasks;
using ItAssetsSystem.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;

namespace ItAssetsSystem.Infrastructure.Services
{
    /// <summary>
    /// 周期性任务生成服务
    /// </summary>
    public class PeriodicTaskGenerationService : BackgroundService
    {
        private readonly ILogger<PeriodicTaskGenerationService> _logger;
        private readonly IServiceProvider _serviceProvider;
        private readonly IEventBus _eventBus;
        private readonly TimeSpan _checkInterval = TimeSpan.FromMinutes(1); // 每分钟检查一次
        private readonly HashSet<long> _processingSchedules = new HashSet<long>(); // 正在处理的计划ID
        private readonly object _lockObject = new object(); // 锁对象

        public PeriodicTaskGenerationService(
            ILogger<PeriodicTaskGenerationService> logger,
            IServiceProvider serviceProvider,
            IEventBus eventBus)
        {
            _logger = logger;
            _serviceProvider = serviceProvider;
            _eventBus = eventBus;
        }

        protected override async System.Threading.Tasks.Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("周期性任务生成服务已启动");

            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    await ProcessPeriodicTasksAsync(stoppingToken);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "处理周期性任务的顶层循环中发生错误");
                }

                try
                {
                    await System.Threading.Tasks.Task.Delay(_checkInterval, stoppingToken);
                }
                catch (TaskCanceledException)
                {
                    // 正常的取消操作，不需要记录错误
                    break;
                }
            }
            _logger.LogInformation("周期性任务生成服务已停止");
        }

        private async System.Threading.Tasks.Task ProcessPeriodicTasksAsync(CancellationToken stoppingToken)
        {
            using var scope = _serviceProvider.CreateScope();
            var taskRepository = scope.ServiceProvider.GetRequiredService<ITaskRepository>();
            var now = DateTime.Now; // 统一使用本地时间进行比较

            var allEnabledSchedules = await taskRepository.GetAllPeriodicSchedulesAsync(false);

            _logger.LogInformation("检查 {Count} 个启用的周期性任务计划。当前本地时间: {CurrentTime} (Kind: {Kind})", 
                allEnabledSchedules.Count, now.ToString("yyyy-MM-dd HH:mm:ss"), now.Kind);

            // 添加详细的调试日志
            foreach (var schedule in allEnabledSchedules)
            {
                var nextGenTime = schedule.NextGenerationTime.ToString("yyyy-MM-dd HH:mm:ss");
                var shouldGenerate = schedule.NextGenerationTime <= now;
                
                // 🔍 详细调试：检查NextGenerationTime的Kind和UTC时间问题
                _logger.LogInformation("🔍 时间调试 - 计划 {ScheduleId} ({ScheduleName}): Cron='{CronExpression}', 存储的下次生成时间={NextGenTime} (Kind={Kind}), 当前时间={CurrentTime}, 应该生成={ShouldGenerate}",
                    schedule.PeriodicTaskScheduleId, schedule.Name, schedule.CronExpression, 
                    nextGenTime, schedule.NextGenerationTime.Kind, now.ToString("yyyy-MM-dd HH:mm:ss"), shouldGenerate);
                
                // 🔍 如果是UTC时间存储，转换后再比较
                if (schedule.NextGenerationTime.Kind == DateTimeKind.Utc)
                {
                    var localNextGenTime = schedule.NextGenerationTime.ToLocalTime();
                    var shouldGenerateLocal = localNextGenTime <= now;
                    _logger.LogInformation("⚠️ 发现UTC时间！计划 {ScheduleId}: UTC时间={UtcTime}, 转换为本地时间={LocalTime}, 重新判断应该生成={ShouldGenerateLocal}",
                        schedule.PeriodicTaskScheduleId, nextGenTime, localNextGenTime.ToString("yyyy-MM-dd HH:mm:ss"), shouldGenerateLocal);
                }
            }

            // 修复：正确处理NextGenerationTime的时区问题
            var schedulesToProcess = allEnabledSchedules.Where(s => 
            {
                if (s.NextGenerationTime.Kind == DateTimeKind.Utc)
                {
                    // 如果存储的是UTC时间，转换为本地时间再比较
                    return s.NextGenerationTime.ToLocalTime() <= now;
                }
                else
                {
                    // 如果存储的是本地时间，直接比较
                    return s.NextGenerationTime <= now;
                }
            });
            
            foreach (var schedule in schedulesToProcess)
            {
                if (stoppingToken.IsCancellationRequested)
                {
                    _logger.LogInformation("周期性任务处理被中断。");
                    break;
                }

                // 检查是否已经在处理中，避免重复生成
                lock (_lockObject)
                {
                    if (_processingSchedules.Contains(schedule.PeriodicTaskScheduleId))
                    {
                        _logger.LogInformation("周期性任务计划 {ScheduleId} 正在处理中，跳过", schedule.PeriodicTaskScheduleId);
                        continue;
                    }
                    _processingSchedules.Add(schedule.PeriodicTaskScheduleId);
                }

                try
                {
                    _logger.LogInformation("处理周期性任务计划 ID: {ScheduleId}, 名称: {ScheduleName}", schedule.PeriodicTaskScheduleId, schedule.Name);

                    // 🔍 检查结束条件
                    if (await ShouldStopGeneratingAsync(schedule, now))
                    {
                        _logger.LogInformation("周期性任务计划 {ScheduleId} ({ScheduleName}) 已达到结束条件，跳过生成",
                            schedule.PeriodicTaskScheduleId, schedule.Name);
                        continue;
                    }

                    // 检查是否在时间窗口内已生成过任务，避免重复生成
                    // 修复：对于按天任务，应该检查今天是否已经生成过任务
                    DateTime checkTime;
                    int windowMinutes;

                    // 根据任务类型调整检查策略
                    if (schedule.RecurrenceType == "Daily")
                    {
                        // 对于按天任务，检查今天是否已经生成过任务
                        // 使用今天的计划执行时间作为检查基准
                        var dailyCronExpression = CronExpression.Parse(schedule.CronExpression, CronFormat.Standard);
                        var todayStart = now.Date; // 今天00:00:00
                        var todayStartUtc = todayStart.ToUniversalTime();
                        var todayScheduledTime = dailyCronExpression.GetNextOccurrence(todayStartUtc.AddSeconds(-1), TimeZoneInfo.Local);

                        if (todayScheduledTime.HasValue)
                        {
                            checkTime = todayScheduledTime.Value.Kind == DateTimeKind.Utc ?
                                todayScheduledTime.Value.ToLocalTime() : todayScheduledTime.Value;
                            windowMinutes = 60; // 对于按天任务，使用1小时的窗口

                            _logger.LogInformation("🔍 按天任务检查 - 计划 {ScheduleId}: 今天计划时间={TodayScheduledTime}, 检查窗口=±{WindowMinutes}分钟",
                                schedule.PeriodicTaskScheduleId, checkTime.ToString("yyyy-MM-dd HH:mm:ss"), windowMinutes);
                        }
                        else
                        {
                            // 如果无法计算今天的计划时间，使用当前时间
                            checkTime = now;
                            windowMinutes = 60;
                            _logger.LogWarning("⚠️ 无法计算今天的计划时间，使用当前时间检查 - 计划 {ScheduleId}", schedule.PeriodicTaskScheduleId);
                        }
                    }
                    else
                    {
                        // 对于其他类型的任务，使用原有逻辑
                        var nextGenTimeLocal = schedule.NextGenerationTime.Kind == DateTimeKind.Utc ?
                            schedule.NextGenerationTime.ToLocalTime() : schedule.NextGenerationTime;
                        checkTime = nextGenTimeLocal;
                        windowMinutes = 5;
                    }

                    var existingTask = await taskRepository.GetTaskByScheduleAndTimeWindowAsync(
                        schedule.PeriodicTaskScheduleId,
                        checkTime,
                        windowMinutes);

                    if (existingTask != null)
                    {
                        _logger.LogInformation("⚠️ 周期性任务计划 {ScheduleId} (名称: {ScheduleName}) 在时间窗口内已存在任务 {ExistingTaskId}，跳过生成。已存在任务创建时间: {ExistingTaskTime}, 检查时间: {CheckTime}±{WindowMinutes}分钟",
                            schedule.PeriodicTaskScheduleId, schedule.Name, existingTask.TaskId, existingTask.CreationTimestamp.ToString("yyyy-MM-dd HH:mm:ss"), checkTime.ToString("yyyy-MM-dd HH:mm:ss"), windowMinutes);
                        continue;
                    }
                    
                    var templateTask = await taskRepository.GetTaskByIdAsync(schedule.TemplateTaskId);
                    if (templateTask == null)
                    {
                        _logger.LogError("找不到周期性任务计划 {ScheduleId} (名称: {ScheduleName}) 的模板任务 {TemplateTaskId}",
                            schedule.PeriodicTaskScheduleId, schedule.Name, schedule.TemplateTaskId);
                        continue;
                    }

                    // 获取负责人列表，从新的负责人关联表中获取
                    var assigneeUserIds = new List<int>();

                    // 从 periodic_task_schedule_assignees 表中获取负责人
                    var assignees = await taskRepository.GetPeriodicScheduleAssigneesAsync(schedule.PeriodicTaskScheduleId);
                    if (assignees != null && assignees.Any())
                    {
                        assigneeUserIds.AddRange(assignees.Select(a => a.UserId));
                        _logger.LogInformation("从数据库获取到 {Count} 个负责人: {Ids}", assignees.Count(), string.Join(",", assigneeUserIds));
                    }

                    // 如果没有从数据库中获取到负责人，使用模板任务的负责人
                    if (assigneeUserIds.Count == 0 && templateTask.AssigneeUserId.HasValue)
                    {
                        assigneeUserIds.Add(templateTask.AssigneeUserId.Value);
                        _logger.LogInformation("使用模板任务的负责人: {UserId}", templateTask.AssigneeUserId.Value);
                    }

                    // 如果仍然没有负责人，使用创建者作为默认负责人
                    if (assigneeUserIds.Count == 0)
                    {
                        assigneeUserIds.Add(schedule.CreatorUserId);
                        _logger.LogInformation("使用创建者作为默认负责人: {UserId}", schedule.CreatorUserId);
                    }

                    // 创建一个任务，但有多个负责人
                    var newTask = new TaskEntity
                    {
                        Name = templateTask.Name,
                        Description = templateTask.Description,
                        TaskType = Models.Enums.TaskType.Periodic.ToString(),
                        Priority = templateTask.Priority, // 使用模板任务的优先级
                        Status = "Todo",
                        CreatorUserId = schedule.CreatorUserId,
                        AssigneeUserId = assigneeUserIds.Count > 0 ? assigneeUserIds[0] : schedule.CreatorUserId, // 主负责人设为第一个
                        LocationId = templateTask.LocationId, // 使用模板任务的位置
                        AssetId = templateTask.AssetId, // 使用模板任务的资产
                        PlanStartDate = now,
                        PlanEndDate = CalculateTaskDueDate(now, templateTask.PlanEndDate, templateTask.PlanStartDate),
                        CreationTimestamp = now,
                        LastUpdatedTimestamp = now,
                        PeriodicTaskScheduleId = schedule.PeriodicTaskScheduleId,
                        Points = templateTask.Points,
                        ProjectId = templateTask.ProjectId
                    };

                    var createdTask = await taskRepository.AddTaskAsync(newTask);
                    _logger.LogInformation("为计划 {ScheduleId} 创建了新任务 ID: {NewTaskId}，共有 {AssigneeCount} 个负责人",
                        schedule.PeriodicTaskScheduleId, createdTask.TaskId, assigneeUserIds.Count);

                    // 为所有负责人在 taskassignees 表中创建记录（与普通任务创建逻辑保持一致）
                    for (int i = 0; i < assigneeUserIds.Count; i++)
                    {
                        var assigneeUserId = assigneeUserIds[i];

                        try
                        {
                            await taskRepository.AddAssigneeAsync(new Domain.Entities.Tasks.TaskAssignee
                            {
                                TaskId = createdTask.TaskId,
                                UserId = assigneeUserId,
                                AssignedByUserId = schedule.CreatorUserId,
                                AssignmentType = i == 0 ? "Assignee" : "Participant", // 第一个为主负责人，其他为协作者
                                AssignmentTimestamp = DateTime.Now
                            });
                            _logger.LogInformation("已添加{Role} {UserId} 到任务 {TaskId} 的 taskassignees 表",
                                i == 0 ? "主负责人" : "协作者", assigneeUserId, createdTask.TaskId);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "添加{Role} {UserId} 到任务 {TaskId} 的 taskassignees 表失败",
                                i == 0 ? "主负责人" : "协作者", assigneeUserId, createdTask.TaskId);
                        }
                    }

                    // 发布任务创建事件
                    var taskCreatedEvent = new TaskCreatedEvent(
                        createdTask.TaskId,
                        createdTask.Name,
                        createdTask.CreatorUserId,
                        createdTask.AssigneeUserId,
                        createdTask.TaskType,
                        createdTask.PlanEndDate,
                        createdTask.Points,
                        createdTask.CreationTimestamp
                    );
                    _eventBus.Publish(taskCreatedEvent);

                    var createdTasks = new List<TaskEntity> { createdTask };

                    // 解析Cron表达式并计算下次生成时间
                    var cronExpression = CronExpression.Parse(schedule.CronExpression, CronFormat.Standard);

                    // 将本地时间转换为UTC，然后使用本地时区进行计算
                    var utcNow = now.ToUniversalTime();
                    var nextGenerationTimeRaw = cronExpression.GetNextOccurrence(utcNow, TimeZoneInfo.Local);
                    
                    // 详细日志：检查Cronos返回值的Kind
                    if (nextGenerationTimeRaw.HasValue)
                    {
                        _logger.LogInformation("🔍 Cronos返回值调试 - 计划 {ScheduleId}: 原始值={RawValue}, Kind={Kind}, 是否UTC={IsUtc}",
                            schedule.PeriodicTaskScheduleId,
                            nextGenerationTimeRaw.Value.ToString("yyyy-MM-dd HH:mm:ss"),
                            nextGenerationTimeRaw.Value.Kind,
                            nextGenerationTimeRaw.Value.Kind == DateTimeKind.Utc);
                    }

                    // 正确处理时区转换：如果Cronos返回UTC时间，需要转换为本地时间
                    DateTime? nextGenerationTime = null;
                    if (nextGenerationTimeRaw.HasValue)
                    {
                        if (nextGenerationTimeRaw.Value.Kind == DateTimeKind.Utc)
                        {
                            // 如果是UTC时间，转换为本地时间
                            nextGenerationTime = nextGenerationTimeRaw.Value.ToLocalTime();
                            _logger.LogInformation("🔄 时区转换 - 计划 {ScheduleId}: UTC时间={UtcTime} -> 本地时间={LocalTime}",
                                schedule.PeriodicTaskScheduleId,
                                nextGenerationTimeRaw.Value.ToString("yyyy-MM-dd HH:mm:ss"),
                                nextGenerationTime.Value.ToString("yyyy-MM-dd HH:mm:ss"));
                        }
                        else
                        {
                            // 如果已经是本地时间或未指定，确保标记为本地时间
                            nextGenerationTime = DateTime.SpecifyKind(nextGenerationTimeRaw.Value, DateTimeKind.Local);
                        }
                    }

                    // 添加详细的调试日志
                    _logger.LogInformation("计划 {ScheduleId}: Cron表达式='{CronExpression}', 当前时间={CurrentTime}, 最终下次生成时间={NextGenTime}, Kind={Kind}",
                        schedule.PeriodicTaskScheduleId, schedule.CronExpression, now.ToString("yyyy-MM-dd HH:mm:ss"),
                        nextGenerationTime?.ToString("yyyy-MM-dd HH:mm:ss") ?? "null",
                        nextGenerationTime?.Kind.ToString() ?? "null");

                    // 更新生成统计信息
                    await taskRepository.UpdateGenerationStatisticsAsync(
                        schedule.PeriodicTaskScheduleId,
                        createdTasks.Count,
                        nextGenerationTime);

                    var taskIds = string.Join(", ", createdTasks.Select(t => t.TaskId));
                    if (nextGenerationTime.HasValue)
                    {
                        _logger.LogInformation(
                            "✅ 成功为周期性任务计划 {ScheduleId} (名称: {ScheduleName}) 生成 {TaskCount} 个新任务实例，ID: {TaskIds}。已生成总数: {TotalGenerated}，下次生成时间：{NextGenerationTime}",
                            schedule.PeriodicTaskScheduleId, schedule.Name, createdTasks.Count, taskIds, schedule.OccurrencesGenerated + createdTasks.Count, nextGenerationTime.Value.ToString("yyyy-MM-dd HH:mm:ss"));

                        // 验证下次生成时间是否合理（应该在未来）
                        var timeUntilNext = nextGenerationTime.Value - now;
                        if (timeUntilNext.TotalMinutes < 1)
                        {
                            _logger.LogWarning("⚠️ 下次生成时间过于接近当前时间！计划 {ScheduleId}, 时间差: {TimeDiff}分钟",
                                schedule.PeriodicTaskScheduleId, timeUntilNext.TotalMinutes);
                        }
                        else
                        {
                            _logger.LogInformation("⏰ 下次生成将在 {TimeDiff} 后执行", FormatTimeSpan(timeUntilNext));
                        }
                    }
                    else
                    {
                        _logger.LogError(
                            "❌ 为周期性任务计划 {ScheduleId} (名称: {ScheduleName}) 生成了 {TaskCount} 个任务实例，ID: {TaskIds}，但无法计算下次生成时间！",
                            schedule.PeriodicTaskScheduleId, schedule.Name, createdTasks.Count, taskIds);
                        _logger.LogError("🔍 Cron表达式解析失败：'{CronExpression}'。请检查表达式格式是否正确。",
                            schedule.CronExpression);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex,
                        "处理周期性任务计划 {ScheduleId} (名称: {ScheduleName}) 时发生错误",
                        schedule.PeriodicTaskScheduleId, schedule.Name);
                }
                finally
                {
                    // 处理完成后移除锁
                    lock (_lockObject)
                    {
                        _processingSchedules.Remove(schedule.PeriodicTaskScheduleId);
                    }
                }
            }
        }

        private DateTime? CalculateTaskDueDate(DateTime now, DateTime? templateDueDate, DateTime? templateStartDate)
        {
            if (!templateDueDate.HasValue || !templateStartDate.HasValue || templateStartDate.Value >= templateDueDate.Value)
            {
                return now.AddDays(7);
            }

            var duration = templateDueDate.Value - templateStartDate.Value;
            return now.Add(duration);
        }

        /// <summary>
        /// 格式化时间差为易读的字符串
        /// </summary>
        private string FormatTimeSpan(TimeSpan timeSpan)
        {
            if (timeSpan.TotalDays >= 1)
            {
                return $"{timeSpan.Days}天{timeSpan.Hours}小时{timeSpan.Minutes}分钟";
            }
            else if (timeSpan.TotalHours >= 1)
            {
                return $"{timeSpan.Hours}小时{timeSpan.Minutes}分钟";
            }
            else
            {
                return $"{timeSpan.Minutes}分钟";
            }
        }

        /// <summary>
        /// 检查周期性任务计划是否应该停止生成任务
        /// </summary>
        /// <param name="schedule">周期性任务计划</param>
        /// <param name="currentTime">当前时间</param>
        /// <returns>是否应该停止生成</returns>
        private async System.Threading.Tasks.Task<bool> ShouldStopGeneratingAsync(PeriodicTaskSchedule schedule, DateTime currentTime)
        {
            try
            {
                // 检查计划状态
                if (schedule.Status != "Active")
                {
                    _logger.LogInformation("周期性任务计划 {ScheduleId} 状态为 {Status}，停止生成",
                        schedule.PeriodicTaskScheduleId, schedule.Status);

                    // 如果状态不是Active，更新状态为Completed或Expired
                    if (schedule.Status == "Paused")
                    {
                        return true; // 暂停状态，跳过生成但不更改状态
                    }

                    return true;
                }

                // 检查结束条件
                switch (schedule.EndConditionType?.ToLower())
                {
                    case "enddate":
                        if (schedule.EndDate.HasValue)
                        {
                            var endDate = schedule.EndDate.Value;
                            // 确保时区一致性
                            if (endDate.Kind == DateTimeKind.Utc)
                            {
                                endDate = endDate.ToLocalTime();
                            }

                            if (currentTime.Date > endDate.Date)
                            {
                                _logger.LogInformation("周期性任务计划 {ScheduleId} 已超过结束日期 {EndDate}，停止生成",
                                    schedule.PeriodicTaskScheduleId, endDate.ToString("yyyy-MM-dd"));

                                // 更新状态为Expired
                                await UpdateScheduleStatusAsync((int)schedule.PeriodicTaskScheduleId, "Expired");
                                return true;
                            }
                        }
                        break;

                    case "occurrences":
                        if (schedule.TotalOccurrences.HasValue &&
                            schedule.OccurrencesGenerated >= schedule.TotalOccurrences.Value)
                        {
                            _logger.LogInformation("周期性任务计划 {ScheduleId} 已达到最大生成次数 {TotalOccurrences}，停止生成",
                                schedule.PeriodicTaskScheduleId, schedule.TotalOccurrences.Value);

                            // 更新状态为Completed
                            await UpdateScheduleStatusAsync((int)schedule.PeriodicTaskScheduleId, "Completed");
                            return true;
                        }
                        break;

                    case "never":
                    default:
                        // 永不结束，继续生成
                        break;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查周期性任务计划 {ScheduleId} 结束条件时发生错误", schedule.PeriodicTaskScheduleId);
                return false; // 出错时继续生成，避免影响正常任务
            }
        }

        /// <summary>
        /// 更新周期性任务计划状态
        /// </summary>
        /// <param name="scheduleId">计划ID</param>
        /// <param name="newStatus">新状态</param>
        private async System.Threading.Tasks.Task UpdateScheduleStatusAsync(int scheduleId, string newStatus)
        {
            try
            {
                using var scope = _serviceProvider.CreateScope();
                var dbContext = scope.ServiceProvider.GetRequiredService<AppDbContext>();

                var schedule = await dbContext.PeriodicTaskSchedules
                    .FirstOrDefaultAsync(s => s.PeriodicTaskScheduleId == scheduleId);

                if (schedule != null)
                {
                    schedule.Status = newStatus;
                    schedule.LastUpdatedTimestamp = DateTime.Now;
                    await dbContext.SaveChangesAsync();

                    _logger.LogInformation("已更新周期性任务计划 {ScheduleId} 状态为 {Status}", scheduleId, newStatus);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新周期性任务计划 {ScheduleId} 状态时发生错误", scheduleId);
            }
        }

    }
}