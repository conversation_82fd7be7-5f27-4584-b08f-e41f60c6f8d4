// File: Application/Features/Notes/Services/QuickMemoService.cs
// Description: Implementation of IQuickMemoService for QuickMemo business logic.
#nullable enable
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using ItAssetsSystem.Application.Common.Dtos;
using ItAssetsSystem.Application.Features.Notes.Dtos;
using ItAssetsSystem.Core.Abstractions; // For IQuickMemoRepository, IQuickMemoCategoryRepository
using ItAssetsSystem.Domain.Entities.Notes; // For QuickMemo, QuickMemoCategory
using Microsoft.Extensions.Logging;

namespace ItAssetsSystem.Application.Features.Notes.Services
{
    public class QuickMemoService : IQuickMemoService
    {
        private readonly IQuickMemoRepository _memoRepository;
        private readonly IQuickMemoCategoryRepository _categoryRepository;
        private readonly ILogger<QuickMemoService> _logger;

        public QuickMemoService(
            IQuickMemoRepository memoRepository,
            IQuickMemoCategoryRepository categoryRepository,
            ILogger<QuickMemoService> logger)
        {
            _memoRepository = memoRepository;
            _categoryRepository = categoryRepository;
            _logger = logger;
        }

        // --- QuickMemo Methods ---

        public async Task<ApiResponse<List<QuickMemoDto>>> GetMemosAsync(int userId, CancellationToken cancellationToken = default)
        {
            try
            {
                var memos = await _memoRepository.GetByUserIdAsync(userId, cancellationToken);
                var memoDtos = memos.Select(m => new QuickMemoDto
                {
                    Id = m.Id,
                    Title = m.Title,
                    Content = m.Content,
                    CategoryId = m.CategoryId,
                    CategoryName = m.Category?.Name, // Populate from included Category
                    CategoryColor = m.Category?.Color, // Populate from included Category
                    IsPinned = m.IsPinned,
                    Color = m.Color,
                    CreatedAt = m.CreatedAt,
                    UpdatedAt = m.UpdatedAt,
                    PositionX = m.PositionXApp,
                    PositionY = m.PositionYApp,
                    SizeWidth = m.SizeWidth,
                    SizeHeight = m.SizeHeight,
                    ZIndex = m.ZIndex,
                    UserId = m.UserId,
                    UserName = m.User?.Name
                }).ToList();
                return ApiResponse<List<QuickMemoDto>>.CreateSuccess(memoDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching memos for user {UserId}", userId);
                return ApiResponse<List<QuickMemoDto>>.CreateFail("获取随手记列表时发生错误。");
            }
        }

        public async Task<ApiResponse<QuickMemoDto>> GetMemoByIdAsync(string memoId, int userId, CancellationToken cancellationToken = default)
        {
            try
            {
                var memo = await _memoRepository.GetByIdAsync(memoId, cancellationToken);
                if (memo == null || memo.UserId != userId)
                {
                    return ApiResponse<QuickMemoDto>.CreateFail("随手记未找到或无权访问。");
                }
                var memoDto = new QuickMemoDto
                {
                    Id = memo.Id,
                    Title = memo.Title,
                    Content = memo.Content,
                    CategoryId = memo.CategoryId,
                    CategoryName = memo.Category?.Name,
                    CategoryColor = memo.Category?.Color,
                    IsPinned = memo.IsPinned,
                    Color = memo.Color,
                    CreatedAt = memo.CreatedAt,
                    UpdatedAt = memo.UpdatedAt,
                    PositionX = memo.PositionXApp,
                    PositionY = memo.PositionYApp,
                    SizeWidth = memo.SizeWidth,
                    SizeHeight = memo.SizeHeight,
                    ZIndex = memo.ZIndex,
                    UserId = memo.UserId,
                    UserName = memo.User?.Name
                };
                return ApiResponse<QuickMemoDto>.CreateSuccess(memoDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching memo {MemoId} for user {UserId}", memoId, userId);
                return ApiResponse<QuickMemoDto>.CreateFail("获取随手记详情时发生错误。");
            }
        }

        public async Task<ApiResponse<QuickMemoDto>> CreateMemoAsync(CreateQuickMemoRequestDto request, int userId, CancellationToken cancellationToken = default)
        {
            try
            {
                if (request.CategoryId != null)
                {
                    var categoryExists = await _categoryRepository.GetByIdAndUserIdAsync(request.CategoryId, userId, cancellationToken);
                    if (categoryExists == null)
                    {
                        return ApiResponse<QuickMemoDto>.CreateFail("指定的分类不存在或不属于您。");
                    }
                }

                var memo = new QuickMemo
                {
                    Title = request.Title,
                    Content = request.Content,
                    UserId = userId,
                    CategoryId = request.CategoryId,
                    IsPinned = request.IsPinned,
                    Color = request.Color,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow,
                    PositionXApp = request.PositionX ?? 0,
                    PositionYApp = request.PositionY ?? 0,
                    SizeWidth = request.SizeWidth ?? 200,
                    SizeHeight = request.SizeHeight ?? 180,
                    ZIndex = request.ZIndex ?? 0
                };
                // Id is generated by entity constructor

                await _memoRepository.AddAsync(memo, cancellationToken);
                await _memoRepository.SaveChangesAsync(cancellationToken);

                // Fetch category details for DTO response
                QuickMemoCategory? category = null;
                if (memo.CategoryId != null)
                {
                    category = await _categoryRepository.GetByIdAsync(memo.CategoryId, cancellationToken);
                }

                var memoDto = new QuickMemoDto
                {
                    Id = memo.Id,
                    Title = memo.Title,
                    Content = memo.Content,
                    CategoryId = memo.CategoryId,
                    CategoryName = category?.Name,
                    CategoryColor = category?.Color,
                    IsPinned = memo.IsPinned,
                    Color = memo.Color,
                    CreatedAt = memo.CreatedAt,
                    UpdatedAt = memo.UpdatedAt,
                    PositionX = memo.PositionXApp,
                    PositionY = memo.PositionYApp,
                    SizeWidth = memo.SizeWidth,
                    SizeHeight = memo.SizeHeight,
                    ZIndex = memo.ZIndex,
                    UserId = memo.UserId,
                    UserName = memo.User?.Name
                };
                return ApiResponse<QuickMemoDto>.CreateSuccess(memoDto, "随手记创建成功。");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating memo for user {UserId}", userId);
                return ApiResponse<QuickMemoDto>.CreateFail("创建随手记时发生错误。");
            }
        }

        public async Task<ApiResponse<QuickMemoDto>> UpdateMemoAsync(string memoId, UpdateQuickMemoRequestDto request, int userId, CancellationToken cancellationToken = default)
        {
            try
            {
                var memo = await _memoRepository.GetByIdAsync(memoId, cancellationToken);
                if (memo == null || memo.UserId != userId)
                {
                    return ApiResponse<QuickMemoDto>.CreateFail("随手记未找到或无权访问。");
                }

                if (request.CategoryId != null)
                {
                    var categoryExists = await _categoryRepository.GetByIdAndUserIdAsync(request.CategoryId, userId, cancellationToken);
                    if (categoryExists == null)
                    {
                         // Allow unsetting category by passing null or empty string for CategoryId
                        if (!string.IsNullOrEmpty(request.CategoryId))
                        {
                            return ApiResponse<QuickMemoDto>.CreateFail("指定的分类不存在或不属于您。");
                        }
                         memo.CategoryId = null; // Explicitly set to null if request.CategoryId is empty string intending to unset.
                    }
                    else
                    {
                        memo.CategoryId = request.CategoryId;
                    }
                }
                else if (request.CategoryId == null && memo.CategoryId != null) 
                {
                    // If CategoryId is not in the request body, do not change it.
                    // This logic branch assumes request.CategoryId being null means "do not update the categoryId field".
                    // If the DTO is structured such that a non-provided field becomes null, this is correct.
                    // If an empty string means "clear category", that's handled by the DTO and `!string.IsNullOrEmpty` above.
                }

                memo.Title = request.Title ?? memo.Title;
                if (request.Content != null) 
                {
                    memo.Content = request.Content; 
                }
                memo.Color = request.Color;
                memo.IsPinned = request.IsPinned ?? memo.IsPinned;
                memo.UpdatedAt = DateTime.UtcNow;

                // Update new fields if provided
                if (request.PositionX.HasValue)
                    memo.PositionXApp = request.PositionX.Value;
                if (request.PositionY.HasValue)
                    memo.PositionYApp = request.PositionY.Value;
                memo.SizeWidth = request.SizeWidth ?? memo.SizeWidth;
                memo.SizeHeight = request.SizeHeight ?? memo.SizeHeight;
                memo.ZIndex = request.ZIndex ?? memo.ZIndex;

                _memoRepository.Update(memo);
                await _memoRepository.SaveChangesAsync(cancellationToken);

                QuickMemoCategory? updatedCategory = null;
                if (memo.CategoryId != null)
                {
                    updatedCategory = await _categoryRepository.GetByIdAsync(memo.CategoryId, cancellationToken);
                }
                
                var memoDto = new QuickMemoDto
                {
                    Id = memo.Id,
                    Title = memo.Title,
                    Content = memo.Content,
                    CategoryId = memo.CategoryId,
                    CategoryName = updatedCategory?.Name,
                    CategoryColor = updatedCategory?.Color,
                    IsPinned = memo.IsPinned,
                    Color = memo.Color,
                    CreatedAt = memo.CreatedAt,
                    UpdatedAt = memo.UpdatedAt,
                    PositionX = memo.PositionXApp,
                    PositionY = memo.PositionYApp,
                    SizeWidth = memo.SizeWidth,
                    SizeHeight = memo.SizeHeight,
                    ZIndex = memo.ZIndex,
                    UserId = memo.UserId,
                    UserName = memo.User?.Name
                };
                return ApiResponse<QuickMemoDto>.CreateSuccess(memoDto, "随手记更新成功。");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating memo {MemoId} for user {UserId}", memoId, userId);
                return ApiResponse<QuickMemoDto>.CreateFail("更新随手记时发生错误。");
            }
        }

        public async Task<ApiResponse<bool>> DeleteMemoAsync(string memoId, int userId, CancellationToken cancellationToken = default)
        {
            try
            {
                var memo = await _memoRepository.GetByIdAsync(memoId, cancellationToken);
                if (memo == null || memo.UserId != userId)
                {
                    return ApiResponse<bool>.CreateFail("随手记未找到或无权访问。");
                }

                _memoRepository.Delete(memo);
                var result = await _memoRepository.SaveChangesAsync(cancellationToken);
                return ApiResponse<bool>.CreateSuccess(result > 0, result > 0 ? "随手记删除成功。" : "删除操作未影响任何记录。");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting memo {MemoId} for user {UserId}", memoId, userId);
                return ApiResponse<bool>.CreateFail("删除随手记时发生错误。");
            }
        }

        // --- QuickMemoCategory Methods ---

        public async Task<ApiResponse<List<QuickMemoCategoryDto>>> GetCategoriesAsync(int userId, CancellationToken cancellationToken = default)
        {
            try
            {
                var categories = await _categoryRepository.GetByUserIdAsync(userId, cancellationToken);
                var categoryDtos = categories.Select(c => new QuickMemoCategoryDto
                {
                    Id = c.Id,
                    Name = c.Name,
                    Color = c.Color
                }).ToList();
                return ApiResponse<List<QuickMemoCategoryDto>>.CreateSuccess(categoryDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching categories for user {UserId}", userId);
                return ApiResponse<List<QuickMemoCategoryDto>>.CreateFail("获取分类列表时发生错误。");
            }
        }
         public async Task<ApiResponse<QuickMemoCategoryDto>> GetCategoryByIdAsync(string categoryId, int userId, CancellationToken cancellationToken = default)
        {
            try
            {
                var category = await _categoryRepository.GetByIdAndUserIdAsync(categoryId, userId, cancellationToken);
                if (category == null)
                {
                    return ApiResponse<QuickMemoCategoryDto>.CreateFail("分类未找到或无权访问。");
                }
                var dto = new QuickMemoCategoryDto
                {
                    Id = category.Id,
                    Name = category.Name,
                    Color = category.Color
                };
                return ApiResponse<QuickMemoCategoryDto>.CreateSuccess(dto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching category {CategoryId} for user {UserId}", categoryId, userId);
                return ApiResponse<QuickMemoCategoryDto>.CreateFail("获取分类详情时发生错误。");
            }
        }


        public async Task<ApiResponse<QuickMemoCategoryDto>> CreateCategoryAsync(CreateQuickMemoCategoryRequestDto request, int userId, CancellationToken cancellationToken = default)
        {
            try
            {
                var existingCategory = await _categoryRepository.FindByNameAsync(userId, request.Name, cancellationToken);
                if (existingCategory != null)
                {
                    return ApiResponse<QuickMemoCategoryDto>.CreateFail($"分类名称 '{request.Name}' 已存在。");
                }

                var category = new QuickMemoCategory
                {
                    Name = request.Name,
                    Color = request.Color,
                    UserId = userId,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };
                // Id is generated by entity constructor

                await _categoryRepository.AddAsync(category, cancellationToken);
                await _categoryRepository.SaveChangesAsync(cancellationToken);

                var categoryDto = new QuickMemoCategoryDto
                {
                    Id = category.Id,
                    Name = category.Name,
                    Color = category.Color
                };
                return ApiResponse<QuickMemoCategoryDto>.CreateSuccess(categoryDto, "分类创建成功。");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating category for user {UserId} with name {CategoryName}", userId, request.Name);
                return ApiResponse<QuickMemoCategoryDto>.CreateFail("创建分类时发生错误。");
            }
        }

        public async Task<ApiResponse<QuickMemoCategoryDto>> UpdateCategoryAsync(string categoryId, CreateQuickMemoCategoryRequestDto request, int userId, CancellationToken cancellationToken = default)
        {
            try
            {
                var category = await _categoryRepository.GetByIdAndUserIdAsync(categoryId, userId, cancellationToken);
                if (category == null)
                {
                    return ApiResponse<QuickMemoCategoryDto>.CreateFail("分类未找到或无权访问。");
                }

                // Check if new name conflicts with another existing category for the same user
                if (category.Name != request.Name)
                {
                    var existingCategoryWithNewName = await _categoryRepository.FindByNameAsync(userId, request.Name, cancellationToken);
                    if (existingCategoryWithNewName != null && existingCategoryWithNewName.Id != categoryId)
                    {
                        return ApiResponse<QuickMemoCategoryDto>.CreateFail($"分类名称 '{request.Name}' 已被其他分类使用。");
                    }
                }

                category.Name = request.Name;
                category.Color = request.Color;
                category.UpdatedAt = DateTime.UtcNow;

                _categoryRepository.Update(category);
                await _categoryRepository.SaveChangesAsync(cancellationToken);

                var categoryDto = new QuickMemoCategoryDto
                {
                    Id = category.Id,
                    Name = category.Name,
                    Color = category.Color
                };
                return ApiResponse<QuickMemoCategoryDto>.CreateSuccess(categoryDto, "分类更新成功。");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating category {CategoryId} for user {UserId}", categoryId, userId);
                return ApiResponse<QuickMemoCategoryDto>.CreateFail("更新分类时发生错误。");
            }
        }

        public async Task<ApiResponse<bool>> DeleteCategoryAsync(string categoryId, int userId, CancellationToken cancellationToken = default)
        {
            try
            {
                var category = await _categoryRepository.GetByIdAndUserIdAsync(categoryId, userId, cancellationToken);
                if (category == null)
                {
                    return ApiResponse<bool>.CreateFail("分类未找到或无权访问。");
                }

                // Optional: Check if category is in use by any memos.
                // If so, decide on a strategy: prevent deletion, unassign memos, or cascade delete memos.
                // Current DB schema for QuickMemo.CategoryId is OnDelete(DeleteBehavior.SetNull).
                // So deleting a category will set CategoryId to null for associated memos.

                _categoryRepository.Delete(category);
                var result = await _categoryRepository.SaveChangesAsync(cancellationToken);
                return ApiResponse<bool>.CreateSuccess(result > 0, result > 0 ? "分类删除成功。" : "删除操作未影响任何记录。");
            }
            catch (Exception ex)
            {
                 // Check for specific DbUpdateException if foreign key constraints prevent deletion
                _logger.LogError(ex, "Error deleting category {CategoryId} for user {UserId}", categoryId, userId);
                return ApiResponse<bool>.CreateFail("删除分类时发生错误。可能仍有随手记使用此分类。");
            }
        }
    }
}