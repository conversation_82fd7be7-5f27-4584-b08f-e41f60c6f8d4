// IT资产管理系统 - 用户实体
// 文件路径: /Models/Entities/User.cs
// 功能: 定义用户实体

using System;
using System.Collections.Generic;
using ItAssetsSystem.Models;
using ItAssetsSystem.Models.Enums;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ItAssetsSystem.Models.Entities
{
    /// <summary>
    /// 用户实体
    /// </summary>
    [Table("users")]
    public class User : IAuditableEntity
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// 用户名/登录名
        /// </summary>
        [Required]
        [MaxLength(50)]
        [Column("Username")]
        public string Username { get; set; } = string.Empty;

        /// <summary>
        /// 密码存储值
        /// 注意：实际数据库中为PasswordHash字段，不是Password
        /// </summary>
        [NotMapped] // 不映射到数据库
        public string Password { get; set; }
        
        /// <summary>
        /// 密码哈希（数据库中存储的字段）
        /// </summary>
        [Required]
        [Column("PasswordHash")]
        public string PasswordHash { get; set; }
        
        /// <summary>
        /// 安全戳
        /// </summary>
        [Column("SecurityStamp")]
        public string SecurityStamp { get; set; }
        
        /// <summary>
        /// 密码盐值（不在数据库中）
        /// </summary>
        [NotMapped]
        public string PasswordSalt { get; set; }

        /// <summary>
        /// 姓名
        /// </summary>
        [Required]
        [MaxLength(50)]
        [Column("Name")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 员工编号（数据库中不存在此字段）
        /// </summary>
        [NotMapped]
        public string EmployeeNumber { get; set; }

        /// <summary>
        /// 职位
        /// </summary>
        [Column("Position")]
        public string Position { get; set; }

        /// <summary>
        /// 部门ID
        /// </summary>
        [Column("DepartmentId")]
        public int? DepartmentId { get; set; }

        /// <summary>
        /// 默认角色ID
        /// </summary>
        [Column("DefaultRoleId")]
        public int? DefaultRoleId { get; set; }

        /// <summary>
        /// 电子邮件
        /// </summary>
        [Column("Email")]
        public string Email { get; set; }

        /// <summary>
        /// 手机号码
        /// </summary>
        [Column("Mobile")]
        public string Mobile { get; set; }

        /// <summary>
        /// 头像URL
        /// </summary>
        [Column("Avatar")]
        public string Avatar { get; set; }

        /// <summary>
        /// 性别
        /// </summary>
        [Column("Gender")]
        public Gender Gender { get; set; }

        /// <summary>
        /// 是否激活（启用）
        /// </summary>
        [Column("IsActive")]
        public bool IsActive { get; set; }

        /// <summary>
        /// 最后登录时间
        /// </summary>
        [Column("LastLoginAt")]
        public DateTime? LastLoginAt { get; set; }

        /// <summary>
        /// 密码重置时间（数据库中不存在此字段）
        /// </summary>
        [NotMapped]
        public DateTime? PasswordResetAt { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [Column("CreatedAt")]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新时间
        /// </summary>
        [Column("UpdatedAt")]
        public DateTime UpdatedAt { get; set; }

        /// <summary>
        /// 部门
        /// </summary>
        public virtual Department Department { get; set; }

        /// <summary>
        /// 默认角色
        /// </summary>
        public virtual Role DefaultRole { get; set; }

        /// <summary>
        /// 用户角色关联
        /// </summary>
        public virtual ICollection<UserRole> UserRoles { get; set; } = new List<UserRole>();

        /// <summary>
        /// 用户位置关联
        /// </summary>
        public virtual ICollection<LocationUser> LocationUsers { get; set; }
    }
}

// 计划行数: 50
// 实际行数: 50 