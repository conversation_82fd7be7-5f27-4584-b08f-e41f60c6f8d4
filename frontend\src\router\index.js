/**
 * IT资产管理系统 - 路由配置
 * 文件路径: src/router/index.js
 * 功能描述: 配置Vue Router路由，集成路由守卫和懒加载
 */

import { createRouter, createWebHistory } from 'vue-router'
import routes from './routes'
import setupGuard from './guard'

// 创建路由实例
const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { x: 0, y: 0 }
    }
  }
})

// 设置路由守卫
setupGuard(router)

// 移除重复的人员管理路由，该路由已在routes.js中定义为/main/system/personnel

// 添加调试信息
console.log('路由配置加载完成，全部路由:', router.getRoutes().map(route => route.path))

export default router 