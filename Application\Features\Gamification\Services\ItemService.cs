using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using ItAssetsSystem.Infrastructure.Data;
using ItAssetsSystem.Models.Gamification;
using ItAssetsSystem.Models.Entities.Gamification;
using ItAssetsSystem.Services.Interfaces;

namespace ItAssetsSystem.Application.Features.Gamification.Services
{
    public interface IItemService
    {
        Task<ItemDropResult> GrantRandomItemAsync(int userId, string source);
        Task<List<UserItemInfo>> GetUserItemsAsync(int userId);
        Task<bool> UseItemAsync(int userId, long itemId);
        Task<List<GamificationItem>> GetAllItemsAsync();
        Task<bool> CanObtainItemAsync(int userId);
    }

    public class ItemService : IItemService
    {
        private readonly AppDbContext _context;
        private readonly ILogger<ItemService> _logger;
        private readonly IUniversalGamificationService _gamificationService;

        public ItemService(
            AppDbContext context, 
            ILogger<ItemService> logger,
            IUniversalGamificationService gamificationService)
        {
            _context = context;
            _logger = logger;
            _gamificationService = gamificationService;
        }

        public async Task<ItemDropResult> GrantRandomItemAsync(int userId, string source)
        {
            try
            {
                // 检查每日道具获取限制
                if (!await CanObtainItemAsync(userId))
                {
                    return new ItemDropResult { Success = false, Message = "今日道具获取已达上限" };
                }

                var rarity = DetermineRarity(source);
                var availableItems = await _context.GamificationItems
                    .Where(gi => gi.Rarity == rarity && gi.IsActive)
                    .ToListAsync();

                if (!availableItems.Any())
                {
                    _logger.LogWarning($"没有找到 {rarity} 稀有度的可用道具");
                    return new ItemDropResult { Success = false, Message = "没有可用道具" };
                }

                // 基于掉落率的加权随机选择
                var selectedItem = SelectItemByWeight(availableItems);

                // 发放道具给用户 - 使用游戏化道具实体
                var userItem = new ItAssetsSystem.Models.Entities.Gamification.GamificationUserItem
                {
                    UserId = userId,
                    ItemId = selectedItem.ItemId,
                    Quantity = 1,
                    AcquiredTimestamp = DateTime.Now,
                    ObtainedSource = source
                };

                _context.GamificationUserItems.Add(userItem);

                // 更新用户统计
                var userStats = await _context.GamificationUserStats
                    .FirstOrDefaultAsync(us => us.CoreUserId == userId);

                if (userStats != null)
                {
                    // 注意：GamificationUserStats没有这些字段，需要在数据库中添加或使用其他方式
                    // userStats.TotalItemsObtained = (userStats.TotalItemsObtained ?? 0) + 1;

                    // switch (rarity)
                    // {
                    //     case "Rare":
                    //         userStats.RareItemsCount = (userStats.RareItemsCount ?? 0) + 1;
                    //         break;
                    //     case "Epic":
                    //         userStats.EpicItemsCount = (userStats.EpicItemsCount ?? 0) + 1;
                    //         break;
                    //     case "Legendary":
                    //         userStats.LegendaryItemsCount = (userStats.LegendaryItemsCount ?? 0) + 1;
                    //         break;
                    // }
                }

                // 更新每日限制
                await UpdateDailyItemLimitAsync(userId);

                // 记录获得道具日志
                await _gamificationService.TriggerBehaviorRewardAsync(
                    userId,
                    "ITEM_OBTAINED",
                    selectedItem.ItemId,
                    context: new { itemId = selectedItem.ItemId, rarity, source },
                    description: $"获得{GetRarityDisplayName(rarity)}道具: {selectedItem.Name}"
                );

                await _context.SaveChangesAsync();

                return new ItemDropResult
                {
                    Success = true,
                    Item = selectedItem,
                    Rarity = rarity,
                    Message = $"恭喜获得{GetRarityDisplayName(rarity)}道具: {selectedItem.Name}!"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"为用户 {userId} 发放随机道具时发生错误");
                return new ItemDropResult { Success = false, Message = "道具发放失败" };
            }
        }

        public async Task<List<UserItemInfo>> GetUserItemsAsync(int userId)
        {
            var userItems = await _context.GamificationUserItems
                .Include(ui => ui.Item)
                .Where(ui => ui.UserId == userId)
                .GroupBy(ui => ui.ItemId)
                .Select(g => new UserItemInfo
                {
                    ItemId = g.Key,
                    ItemName = g.First().Item.Name,
                    ItemCode = g.First().Item.Code,
                    ItemType = g.First().Item.Type,
                    Rarity = g.First().Item.Rarity,
                    Effect = g.First().Item.Effect,
                    EffectDuration = g.First().Item.EffectDuration,
                    IconUrl = g.First().Item.IconUrl,
                    Quantity = g.Sum(ui => ui.Quantity),
                    FirstObtainedAt = g.Min(ui => ui.AcquiredTimestamp)
                })
                .OrderBy(ui => GetRarityOrder(ui.Rarity))
                .ThenBy(ui => ui.ItemName)
                .ToListAsync();

            return userItems;
        }

        public async Task<bool> UseItemAsync(int userId, long itemId)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var userItem = await _context.GamificationUserItems
                    .Include(ui => ui.Item)
                    .FirstOrDefaultAsync(ui => ui.UserId == userId && ui.ItemId == itemId);

                if (userItem == null || userItem.Quantity <= 0)
                {
                    return false;
                }

                var item = userItem.Item;

                // 应用道具效果
                await ApplyItemEffectAsync(userId, item);

                // 减少道具数量
                userItem.Quantity -= 1;
                userItem.LastUsedTimestamp = DateTime.Now;
                if (userItem.Quantity <= 0)
                {
                    _context.GamificationUserItems.Remove(userItem);
                }

                // 记录使用日志
                await _gamificationService.TriggerBehaviorRewardAsync(
                    userId,
                    "ITEM_USED",
                    itemId,
                    context: new { itemId, itemCode = item.Code, itemType = item.Type },
                    description: $"使用道具: {item.Name}"
                );

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();

                return true;
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, $"用户 {userId} 使用道具 {itemId} 时发生错误");
                return false;
            }
        }

        public async Task<List<GamificationItem>> GetAllItemsAsync()
        {
            return await _context.GamificationItems
                .Where(gi => gi.IsActive)
                .OrderBy(gi => GetRarityOrder(gi.Rarity))
                .ThenBy(gi => gi.Name)
                .ToListAsync();
        }

        public async Task<bool> CanObtainItemAsync(int userId)
        {
            var today = DateTime.Today;
            var dailyLimit = await _context.DailyLimits
                .FirstOrDefaultAsync(dl => dl.UserId == userId && dl.LimitDate == today);

            if (dailyLimit == null)
            {
                // 创建今日限制记录
                dailyLimit = new DailyLimit
                {
                    UserId = userId,
                    LimitDate = today,
                    ItemsObtained = 0,
                    MaxItems = 5 // 默认每日最多5个道具
                };
                _context.DailyLimits.Add(dailyLimit);
                await _context.SaveChangesAsync();
                return true;
            }

            return dailyLimit.ItemsObtained < dailyLimit.MaxItems;
        }

        private string DetermineRarity(string source)
        {
            var random = new Random().NextDouble();

            return source switch
            {
                "TASK_COMPLETE" => random switch
                {
                    < 0.05 => "Legendary",
                    < 0.15 => "Epic", 
                    < 0.40 => "Rare",
                    _ => "Common"
                },
                "LEVEL_UP" => random switch
                {
                    < 0.10 => "Legendary",
                    < 0.30 => "Epic",
                    < 0.60 => "Rare", 
                    _ => "Common"
                },
                "ACHIEVEMENT" => random switch
                {
                    < 0.15 => "Legendary",
                    < 0.35 => "Epic",
                    < 0.65 => "Rare",
                    _ => "Common"
                },
                _ => "Common"
            };
        }

        private GamificationItem SelectItemByWeight(List<GamificationItem> items)
        {
            var totalWeight = items.Sum(i => i.DropRate);
            var randomValue = new Random().NextDouble() * (double)totalWeight;
            
            double currentWeight = 0;
            foreach (var item in items)
            {
                currentWeight += (double)item.DropRate;
                if (randomValue <= currentWeight)
                {
                    return item;
                }
            }

            return items.Last(); // 备选方案
        }

        private async Task ApplyItemEffectAsync(int userId, GamificationItem item)
        {
            switch (item.Code)
            {
                case "DOUBLE_POINTS":
                case "TRIPLE_XP":
                case "EFFICIENCY_BOOST":
                case "POINT_BONUS":
                    // 创建临时效果
                    var effect = new ActiveItemEffect
                    {
                        UserId = userId,
                        ItemId = item.ItemId,
                        EffectType = item.Code,
                        Multiplier = GetEffectMultiplier(item.Code),
                        ExpiresAt = DateTime.Now.AddMinutes(item.EffectDuration ?? 60),
                        CreatedAt = DateTime.Now
                    };
                    _context.ActiveItemEffects.Add(effect);
                    break;

                case "XP_POTION":
                    // 立即给予经验值
                    var userStats = await _context.GamificationUserStats
                        .FirstOrDefaultAsync(us => us.CoreUserId == userId);
                    if (userStats != null)
                    {
                        userStats.CurrentXP = userStats.CurrentXP + 50;
                    }
                    break;

                case "COIN_CHEST":
                    // 立即给予金币
                    var coinAmount = new Random().Next(100, 301); // 100-300金币
                    var userStatsForCoins = await _context.GamificationUserStats
                        .FirstOrDefaultAsync(us => us.CoreUserId == userId);
                    if (userStatsForCoins != null)
                    {
                        userStatsForCoins.CoinsBalance = userStatsForCoins.CoinsBalance + coinAmount;
                    }
                    break;

                case "DIAMOND_VAULT":
                    // 立即给予钻石
                    var diamondAmount = new Random().Next(50, 101); // 50-100钻石
                    var userStatsForDiamonds = await _context.GamificationUserStats
                        .FirstOrDefaultAsync(us => us.CoreUserId == userId);
                    if (userStatsForDiamonds != null)
                    {
                        userStatsForDiamonds.DiamondsBalance = userStatsForDiamonds.DiamondsBalance + diamondAmount;
                    }
                    break;

                case "TIME_WARP":
                    // 立即完成一个进行中的任务
                    var taskToComplete = await _context.Tasks
                        .Where(t => t.AssigneeUserId == userId && t.Status == "InProgress")
                        .OrderBy(t => t.PlanEndDate)
                        .FirstOrDefaultAsync();
                    
                    if (taskToComplete != null)
                    {
                        taskToComplete.Status = "Done";
                        taskToComplete.ActualEndDate = DateTime.Now;
                        taskToComplete.CompletedByUserId = userId;
                        taskToComplete.CompletedAt = DateTime.Now;
                    }
                    break;
            }
        }

        private decimal GetEffectMultiplier(string effectCode)
        {
            return effectCode switch
            {
                "DOUBLE_POINTS" => 2.0m,
                "TRIPLE_XP" => 3.0m,
                "EFFICIENCY_BOOST" => 1.1m,
                "POINT_BONUS" => 1.2m,
                _ => 1.0m
            };
        }

        private async Task UpdateDailyItemLimitAsync(int userId)
        {
            var today = DateTime.Today;
            var dailyLimit = await _context.DailyLimits
                .FirstOrDefaultAsync(dl => dl.UserId == userId && dl.LimitDate == today);

            if (dailyLimit != null)
            {
                dailyLimit.ItemsObtained += 1;
            }
        }

        private string GetRarityDisplayName(string rarity)
        {
            return rarity switch
            {
                "Common" => "普通",
                "Rare" => "稀有",
                "Epic" => "史诗",
                "Legendary" => "传说",
                _ => "未知"
            };
        }

        private int GetRarityOrder(string rarity)
        {
            return rarity switch
            {
                "Legendary" => 1,
                "Epic" => 2,
                "Rare" => 3,
                "Common" => 4,
                _ => 5
            };
        }
    }

    // 数据传输对象
    public class ItemDropResult
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public GamificationItem Item { get; set; }
        public string Rarity { get; set; }
    }

    public class UserItemInfo
    {
        public long ItemId { get; set; }
        public string ItemName { get; set; }
        public string ItemCode { get; set; }
        public string ItemType { get; set; }
        public string Rarity { get; set; }
        public string Effect { get; set; }
        public int? EffectDuration { get; set; }
        public string IconUrl { get; set; }
        public int Quantity { get; set; }
        public DateTime FirstObtainedAt { get; set; }
    }
}
