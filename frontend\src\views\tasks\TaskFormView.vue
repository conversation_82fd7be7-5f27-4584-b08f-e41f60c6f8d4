<template>
  <div class="task-form-container">
    <el-card class="form-card">
      <template #header>
        <div class="card-header">
          <h2>{{ isEdit ? '编辑任务' : '创建任务' }}</h2>
          <div class="header-actions">
            <el-button @click="goBack">
              <el-icon><Back /></el-icon>
              返回
            </el-button>
          </div>
        </div>
      </template>
      
      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="10" animated />
      </div>
      
      <el-form
        v-else
        ref="taskFormRef"
        :model="taskForm"
        :rules="rules"
        label-width="100px"
        class="task-form"
      >
        <el-form-item label="任务标题" prop="title">
          <el-input v-model="taskForm.title" placeholder="请输入任务标题" maxlength="100" show-word-limit />
        </el-form-item>
        
        <el-form-item label="任务描述" prop="description">
          <el-input
            v-model="taskForm.description"
            type="textarea"
            :rows="4"
            placeholder="请输入任务描述"
            maxlength="2000"
            show-word-limit
          />
        </el-form-item>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="优先级" prop="priority">
              <el-select v-model="taskForm.priority" placeholder="请选择优先级" style="width: 100%">
                <el-option label="低" value="low" />
                <el-option label="中" value="medium" />
                <el-option label="高" value="high" />
                <el-option label="紧急" value="urgent" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-select v-model="taskForm.status" placeholder="请选择状态" style="width: 100%">
                <el-option label="待处理" value="pending" />
                <el-option label="进行中" value="in_progress" />
                <el-option label="已完成" value="completed" />
                <el-option label="已取消" value="cancelled" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="任务分类">
              <el-select
                v-model="taskForm.categoryId"
                placeholder="请选择任务分类"
                style="width: 100%"
                clearable
                filterable
              >
                <el-option
                  v-for="category in taskCategories"
                  :key="category.categoryId"
                  :label="category.name"
                  :value="category.categoryId"
                >
                  <div class="category-option">
                    <el-icon v-if="category.icon" :style="{ color: category.color || '#409EFF' }">
                      <component :is="category.icon" />
                    </el-icon>
                    <span>{{ category.name }}</span>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="任务类型" prop="taskType">
              <el-select v-model="taskForm.taskType" placeholder="请选择任务类型" style="width: 100%">
                <el-option label="普通任务" value="Normal" />
                <el-option label="周期任务" value="Periodic" />
                <el-option label="PDCA任务" value="PDCA" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="开始日期">
              <el-date-picker
                v-model="taskForm.startDate"
                type="date"
                placeholder="选择开始日期"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="截止日期" prop="dueDate">
              <el-date-picker
                v-model="taskForm.dueDate"
                type="date"
                placeholder="选择截止日期"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="进度">
          <el-slider v-model="taskForm.progress" :format-tooltip="formatProgressTooltip" />
        </el-form-item>
        
        <el-form-item label="负责人">
          <div class="assignee-section">
            <!-- 主要负责人 -->
            <div class="main-assignee">
              <label class="sub-label">主要负责人</label>
              <el-select
                v-model="taskForm.assigneeUserId"
                placeholder="选择主要负责人"
                filterable
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="user in users"
                  :key="user.id"
                  :label="user.name"
                  :value="user.id"
                >
                  <div class="user-option">
                    <span>{{ user.name }}</span>
                    <span class="user-dept">{{ user.department }}</span>
                  </div>
                </el-option>
              </el-select>
            </div>
            
            <!-- 协作人员 -->
            <div class="collaborators" style="margin-top: 10px;">
              <label class="sub-label">协作人员（可选）</label>
              <el-select
                v-model="taskForm.collaboratorUserIds"
                placeholder="选择协作人员"
                multiple
                filterable
                collapse-tags
                collapse-tags-tooltip
                style="width: 100%"
              >
                <el-option
                  v-for="user in availableCollaborators"
                  :key="user.id"
                  :label="user.name"
                  :value="user.id"
                >
                  <div class="user-option">
                    <span>{{ user.name }}</span>
                    <span class="user-dept">{{ user.department }}</span>
                  </div>
                </el-option>
              </el-select>
            </div>
            
            <!-- 快速操作按钮 -->
            <div class="quick-actions" style="margin-top: 8px;">
              <el-button size="small" @click="addCurrentUserAsCollaborator" v-if="!isCurrentUserInvolved">
                <el-icon><Plus /></el-icon>
                加入协作
              </el-button>
              <el-button size="small" @click="clearAllAssignees" v-if="hasAnyAssignee">
                <el-icon><Close /></el-icon>
                清空所有
              </el-button>
            </div>
          </div>
        </el-form-item>
        
        <el-form-item label="标签">
          <el-select
            v-model="taskForm.tags"
            multiple
            filterable
            allow-create
            default-first-option
            placeholder="请选择或创建标签"
            style="width: 100%"
          >
            <el-option
              v-for="tag in availableTags"
              :key="tag"
              :label="tag"
              :value="tag"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="submitForm" :loading="submitting">
            {{ isEdit ? '更新' : '创建' }}
          </el-button>
          <el-button @click="resetForm">重置</el-button>
          <el-button @click="goBack">取消</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Close, Back } from '@element-plus/icons-vue'
import { formatDate } from '@/utils/date'
import { taskApi } from '@/api/task'
import userApi from '@/api/user'
import { taskCategoryApi } from '@/api/taskCategory'

export default {
  name: 'TaskFormView',
  
  components: {
    Plus,
    Close,
    Back
  },
  
  props: {
    taskId: {
      type: Number,
      default: null
    },
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  
  setup(props) {
    const router = useRouter()
    const route = useRoute()
    const taskFormRef = ref(null)
    const loading = ref(false)
    const submitting = ref(false)
    const users = ref([])
    const taskCategories = ref([])
    const availableTags = ref(['bug', '功能', '文档', '优化', '紧急', '待讨论'])
    
    // 表单数据
    const taskForm = reactive({
      title: '',
      description: '',
      priority: 'medium',
      status: 'pending',
      startDate: null,
      dueDate: null,
      progress: 0,
      assigneeUserId: null,
      collaboratorUserIds: [], // 新增：协作人员IDs
      taskType: 'Normal', // 新增：任务类型
      categoryId: null, // 新增：任务分类ID
      tags: []
    })
    
    // 当前用户信息
    const currentUser = ref(null)
    
    // 表单验证规则
    const rules = {
      title: [
        { required: true, message: '请输入任务标题', trigger: 'blur' },
        { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }
      ],
      description: [
        { required: true, message: '请输入任务描述', trigger: 'blur' }
      ],
      priority: [
        { required: true, message: '请选择优先级', trigger: 'change' }
      ],
      status: [
        { required: true, message: '请选择状态', trigger: 'change' }
      ],
      dueDate: [
        { 
          validator: (rule, value, callback) => {
            if (taskForm.startDate && value && new Date(value) < new Date(taskForm.startDate)) {
              callback(new Error('截止日期不能早于开始日期'))
            } else {
              callback()
            }
          }, 
          trigger: 'change' 
        }
      ]
    }
    
    // Computed 属性
    const availableCollaborators = computed(() => {
      // 过滤掉已经是主要负责人的用户
      return users.value.filter(user => user.id !== taskForm.assigneeUserId)
    })
    
    const isCurrentUserInvolved = computed(() => {
      if (!currentUser.value) return false
      return taskForm.assigneeUserId === currentUser.value.id || 
             taskForm.collaboratorUserIds.includes(currentUser.value.id)
    })
    
    const hasAnyAssignee = computed(() => {
      return taskForm.assigneeUserId || taskForm.collaboratorUserIds.length > 0
    })
    
    // 多负责人相关方法
    const addCurrentUserAsCollaborator = () => {
      if (currentUser.value && !taskForm.collaboratorUserIds.includes(currentUser.value.id)) {
        taskForm.collaboratorUserIds.push(currentUser.value.id)
        ElMessage.success('已将您加入协作人员')
      }
    }
    
    const clearAllAssignees = () => {
      taskForm.assigneeUserId = null
      taskForm.collaboratorUserIds = []
      ElMessage.success('已清空所有负责人')
    }
    
    // 获取当前用户信息
    const getCurrentUser = async () => {
      try {
        const response = await userApi.getCurrentUser()
        if (response.success) {
          currentUser.value = response.data
        }
      } catch (error) {
        console.error('获取当前用户信息失败:', error)
      }
    }
    
    // 加载用户列表
    const loadUsers = async () => {
      try {
        const response = await userApi.getUsers()
        if (response.success) {
          users.value = response.data
        } else {
          ElMessage.warning(response.message || '加载用户列表失败')
        }
      } catch (error) {
        console.error('加载用户列表出错:', error)
        ElMessage.warning('加载用户列表时发生错误')
      }
    }

    // 加载任务分类列表
    const loadTaskCategories = async () => {
      try {
        const response = await taskCategoryApi.getActiveCategories()
        if (response.success) {
          taskCategories.value = response.data
        } else {
          console.warn('加载任务分类失败:', response.message)
          // 不显示错误消息，因为分类是可选的
        }
      } catch (error) {
        console.error('加载任务分类出错:', error)
        // 不显示错误消息，因为分类是可选的
      }
    }
    
    // 加载任务详情（编辑模式）
    const loadTaskDetail = async () => {
      if (!props.taskId) return
      
      loading.value = true
      try {
        const response = await taskApi.getTaskById(props.taskId)
        if (response.success) {
          const task = response.data
          
          // 填充表单数据，从后端TaskDto映射到前端表单
          taskForm.title = task.name || task.title // name -> title
          taskForm.description = task.description
          taskForm.priority = task.priority
          taskForm.status = task.status
          taskForm.startDate = task.planStartDate ? new Date(task.planStartDate) : null // planStartDate -> startDate
          taskForm.dueDate = task.planEndDate ? new Date(task.planEndDate) : null // planEndDate -> dueDate
          taskForm.progress = task.progress || 0
          taskForm.assigneeUserId = task.assigneeUserId
          taskForm.taskType = task.taskType || 'Normal'
          taskForm.categoryId = task.categoryId || null // 任务分类ID
          
          // 加载协作人员 - 从assignees数组中提取协作者
          if (task.assignees && Array.isArray(task.assignees)) {
            taskForm.collaboratorUserIds = task.assignees
              .filter(assignee => assignee.assignmentType === 'Participant')
              .map(assignee => assignee.userId)
          } else {
            taskForm.collaboratorUserIds = task.collaboratorUserIds || []
          }
          
          taskForm.tags = task.tagList || task.tags || []
        } else {
          ElMessage.error(response.message || '加载任务详情失败')
          goBack()
        }
      } catch (error) {
        console.error('加载任务详情出错:', error)
        ElMessage.error('加载任务详情时发生错误')
        goBack()
      } finally {
        loading.value = false
      }
    }
    
    // 格式化进度条提示
    const formatProgressTooltip = (val) => {
      return `${val}%`
    }
    
    // 提交表单
    const submitForm = async () => {
      if (!taskFormRef.value) return
      
      await taskFormRef.value.validate(async (valid, fields) => {
        if (!valid) {
          console.log('表单验证失败:', fields)
          return
        }
        
        submitting.value = true
        try {
          // 准备提交数据，映射字段名到后端API期望的格式
          const formData = {
            name: taskForm.title, // title -> name
            description: taskForm.description,
            priority: taskForm.priority,
            status: taskForm.status,
            planStartDate: taskForm.startDate ? formatDate(taskForm.startDate, 'yyyy-MM-dd') : null, // startDate -> planStartDate
            planEndDate: taskForm.dueDate ? formatDate(taskForm.dueDate, 'yyyy-MM-dd') : null, // dueDate -> planEndDate
            progress: taskForm.progress,
            assigneeUserId: taskForm.assigneeUserId,
            collaboratorUserIds: taskForm.collaboratorUserIds, // 包含协作人员
            taskType: taskForm.taskType || 'Normal', // 添加默认任务类型
            categoryId: taskForm.categoryId, // 任务分类ID
            tags: taskForm.tags
          }
          
          let response
          if (props.isEdit) {
            // 更新任务
            response = await taskApi.updateTask(props.taskId, formData)
          } else {
            // 创建任务
            response = await taskApi.createTask(formData)
          }
          
          if (response.success) {
            ElMessage.success(props.isEdit ? '任务更新成功' : '任务创建成功')
            
            // 根据操作类型决定跳转
            if (props.isEdit) {
              // 编辑后返回详情页
              router.push({
                name: 'TaskDetail',
                params: { id: props.taskId }
              })
            } else {
              // 创建后返回列表或进入详情页
              const newTaskId = response.data.taskId
              router.push({
                name: 'TaskDetail',
                params: { id: newTaskId }
              })
            }
          } else {
            ElMessage.error(response.message || (props.isEdit ? '更新任务失败' : '创建任务失败'))
          }
        } catch (error) {
          console.error(props.isEdit ? '更新任务出错:' : '创建任务出错:', error)
          ElMessage.error(props.isEdit ? '更新任务时发生错误' : '创建任务时发生错误')
        } finally {
          submitting.value = false
        }
      })
    }
    
    // 重置表单
    const resetForm = () => {
      if (props.isEdit) {
        // 编辑模式下重新加载任务数据
        loadTaskDetail()
      } else {
        // 创建模式下重置为默认值
        taskFormRef.value?.resetFields()
      }
    }
    
    // 返回上一页
    const goBack = () => {
      router.back()
    }
    
    onMounted(() => {
      getCurrentUser() // 获取当前用户信息
      loadUsers()
      loadTaskCategories() // 加载任务分类
      if (props.isEdit) {
        loadTaskDetail()
      }
    })
    
    return {
      taskFormRef,
      taskForm,
      rules,
      loading,
      submitting,
      users,
      availableTags,
      currentUser,
      // Computed
      availableCollaborators,
      isCurrentUserInvolved,
      hasAnyAssignee,
      // Methods
      formatProgressTooltip,
      submitForm,
      resetForm,
      goBack,
      addCurrentUserAsCollaborator,
      clearAllAssignees
    }
  }
}
</script>

<style scoped>
.task-form-container {
  padding: 20px;
}

.form-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.loading-container {
  padding: 20px;
}

.assignee-section {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 15px;
  background: #fafafa;
}

.sub-label {
  display: inline-block;
  font-size: 12px;
  color: #666;
  margin-bottom: 5px;
  font-weight: 500;
}

.user-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.user-dept {
  font-size: 12px;
  color: #999;
}

.quick-actions {
  display: flex;
  gap: 8px;
}

.quick-actions .el-button {
  font-size: 12px;
  padding: 4px 8px;
}

.task-form {
  max-width: 800px;
  margin: 0 auto;
}

.category-option {
  display: flex;
  align-items: center;
  gap: 8px;
}

.category-option .el-icon {
  font-size: 16px;
}
</style>