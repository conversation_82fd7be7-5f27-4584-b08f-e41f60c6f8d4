import{_ as e,r as a,ad as t,j as l,z as s,a as i,k as d,o,w as r,d as n,e as u,b as p,$ as c,af as m,F as v,h as f,p as b,i as g,t as y,ax as h,E as _,c as w,aV as k}from"./index-CG5lHOPO.js";import{getSpareParts as x,getSparePartTypes as I,getMaintenanceSuppliers as V,createRepairOrder as D,updateRepairOrder as C}from"./spareparts-DKUrs8IX.js";const U={class:"filter-section"},z={class:"pagination-container"},S={key:0,class:"selected-section"},q={class:"selected-items"},N={class:"dialog-footer"},Q=e({__name:"SparePartSelector",props:{visible:{type:Boolean,default:!1},selectedParts:{type:Array,default:()=>[]}},emits:["update:visible","confirm"],setup(e,{emit:w}){const k=e,V=w,D=a(),C=a([]),Q=a([]),j=a(0),O=a([]),R=t({pageIndex:1,pageSize:10,name:"",code:"",typeId:null,stockStatus:"available"}),P=async()=>{try{const e=await x(R);e.success&&(C.value=e.data.items||[],j.value=e.data.totalCount||0,h((()=>{C.value.forEach((e=>{O.value.some((a=>a.id===e.id))&&D.value.toggleRowSelection(e,!0)}))})))}catch(e){_.error("加载备件列表失败")}},$=e=>{O.value=e},F=()=>{Object.assign(R,{pageIndex:1,pageSize:10,name:"",code:"",typeId:null,stockStatus:"available"}),P()},T=()=>{V("confirm",O.value),A()},A=()=>{V("update:visible",!1),O.value=[]},B=e=>e.stockQuantity<=0?"stock-empty":e.stockQuantity<=e.minStock?"stock-danger":e.stockQuantity<=e.warningThreshold?"stock-warning":"stock-normal",E=e=>e.stockQuantity<=0||e.stockQuantity<=e.minStock?"danger":e.stockQuantity<=e.warningThreshold?"warning":"success",H=e=>e.stockQuantity<=0?"无库存":e.stockQuantity<=e.minStock?"库存不足":e.stockQuantity<=e.warningThreshold?"库存预警":"库存正常";return l((()=>k.selectedParts),(e=>{O.value=[...e]}),{deep:!0,immediate:!0}),s((()=>{P(),(async()=>{try{const e=await I();e.success&&(Q.value=e.data||[])}catch(e){}})()})),(e,a)=>{const t=i("el-input"),l=i("el-form-item"),s=i("el-option"),h=i("el-select"),_=i("el-button"),w=i("el-form"),x=i("el-table-column"),I=i("el-tag"),K=i("el-table"),L=i("el-pagination"),M=i("el-divider"),Y=i("el-dialog");return o(),d(Y,{"model-value":k.visible,"onUpdate:modelValue":a[5]||(a[5]=e=>V("update:visible",e)),title:"选择备件",width:"800px",onClose:A},{footer:r((()=>[n("div",N,[u(_,{onClick:A},{default:r((()=>a[8]||(a[8]=[b("取消")]))),_:1}),u(_,{type:"primary",onClick:T,disabled:0===O.value.length},{default:r((()=>[b(" 确认选择 ("+y(O.value.length)+") ",1)])),_:1},8,["disabled"])])])),default:r((()=>[n("div",U,[u(w,{inline:!0,model:R,class:"filter-form"},{default:r((()=>[u(l,{label:"备件名称"},{default:r((()=>[u(t,{modelValue:R.name,"onUpdate:modelValue":a[0]||(a[0]=e=>R.name=e),placeholder:"备件名称",clearable:"",onKeyup:m(P,["enter"])},null,8,["modelValue"])])),_:1}),u(l,{label:"备件编号"},{default:r((()=>[u(t,{modelValue:R.code,"onUpdate:modelValue":a[1]||(a[1]=e=>R.code=e),placeholder:"备件编号",clearable:"",onKeyup:m(P,["enter"])},null,8,["modelValue"])])),_:1}),u(l,{label:"备件类型"},{default:r((()=>[u(h,{modelValue:R.typeId,"onUpdate:modelValue":a[2]||(a[2]=e=>R.typeId=e),placeholder:"备件类型",clearable:""},{default:r((()=>[(o(!0),p(v,null,f(Q.value,(e=>(o(),d(s,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),u(l,null,{default:r((()=>[u(_,{type:"primary",onClick:P},{default:r((()=>a[6]||(a[6]=[b("查询")]))),_:1}),u(_,{onClick:F},{default:r((()=>a[7]||(a[7]=[b("重置")]))),_:1})])),_:1})])),_:1},8,["model"])]),u(K,{ref_key:"tableRef",ref:D,data:C.value,border:"",style:{width:"100%"},onSelectionChange:$},{default:r((()=>[u(x,{type:"selection",width:"55"}),u(x,{prop:"code",label:"备件编号",width:"120"}),u(x,{prop:"name",label:"备件名称","min-width":"150"}),u(x,{prop:"typeName",label:"备件类型",width:"120"}),u(x,{prop:"specification",label:"规格型号",width:"120"}),u(x,{prop:"stockQuantity",label:"库存数量",width:"100",align:"center"},{default:r((({row:e})=>[n("span",{class:g(B(e))},y(e.stockQuantity)+" "+y(e.unit),3)])),_:1}),u(x,{prop:"locationName",label:"库位",width:"120"}),u(x,{label:"状态",width:"100"},{default:r((({row:e})=>[u(I,{type:E(e),size:"small"},{default:r((()=>[b(y(H(e)),1)])),_:2},1032,["type"])])),_:1})])),_:1},8,["data"]),n("div",z,[u(L,{"current-page":R.pageIndex,"onUpdate:currentPage":a[3]||(a[3]=e=>R.pageIndex=e),"page-size":R.pageSize,"onUpdate:pageSize":a[4]||(a[4]=e=>R.pageSize=e),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:j.value,onSizeChange:P,onCurrentChange:P},null,8,["current-page","page-size","total"])]),O.value.length>0?(o(),p("div",S,[u(M,{"content-position":"left"},{default:r((()=>[b("已选择的备件 ("+y(O.value.length)+")",1)])),_:1}),n("div",q,[(o(!0),p(v,null,f(O.value,(e=>(o(),d(I,{key:e.id,closable:"",onClose:a=>(e=>{const a=O.value.findIndex((a=>a.id===e.id));a>-1&&(O.value.splice(a,1),D.value.toggleRowSelection(e,!1))})(e),style:{"margin-right":"8px","margin-bottom":"8px"}},{default:r((()=>[b(y(e.name)+" ("+y(e.code)+") ",1)])),_:2},1032,["onClose"])))),128))])])):c("",!0)])),_:1},8,["model-value"])}}},[["__scopeId","data-v-d776e6b1"]]),j={class:"document-header"},O={class:"company-info"},R={class:"header-info"},P={class:"info-row"},$={class:"value"},F={class:"info-row"},T={class:"value"},A={class:"info-row"},B={class:"value"},E={class:"section"},H={class:"info-grid"},K={class:"info-item"},L={class:"value"},M={class:"info-item"},Y={class:"value"},G={class:"info-item"},J={class:"value"},W={class:"info-item"},X={class:"value"},Z={class:"info-item full-width"},ee={class:"value"},ae={class:"section"},te={class:"items-table"},le={class:"text-center"},se={class:"text-center"},ie={class:"section"},de={class:"summary-stats"},oe={class:"stat-item"},re={class:"stat-value"},ne={class:"stat-item"},ue={class:"stat-value"},pe={class:"stat-item"},ce={class:"stat-value"},me={class:"stat-item"},ve={class:"stat-value"},fe={key:0,class:"section"},be={class:"notes-content"},ge={class:"dialog-footer"},ye=e({__name:"RepairOrderSummary",props:{visible:{type:Boolean,default:!1},orderData:{type:Object,required:!0}},emits:["update:visible"],setup(e,{emit:t}){const l=e,s=a(),m=w((()=>{var e;return(null==(e=l.orderData.repairItems)?void 0:e.reduce(((e,a)=>e+(a.quantity||0)),0))||0})),h=w((()=>{if(!l.orderData.estimatedDays)return"-";const e=new Date;return e.setDate(e.getDate()+l.orderData.estimatedDays),k(e)})),k=e=>{if(!e)return"-";return new Date(e).toLocaleDateString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit"})},x=e=>e?`¥${e.toFixed(2)}`:"-",I=e=>({1:"紧急",2:"高",3:"中",4:"低"}[e]||"中"),V=()=>{const e=s.value.innerHTML,a=window.open("","_blank");a.document.write(`\n    <html>\n      <head>\n        <title>返厂维修汇总单</title>\n        <style>\n          body { font-family: 'Microsoft YaHei', sans-serif; margin: 20px; }\n          .summary-document { max-width: 800px; margin: 0 auto; }\n          .document-header { border-bottom: 2px solid #333; padding-bottom: 20px; margin-bottom: 20px; }\n          .section { margin-bottom: 20px; }\n          .section-title { font-size: 16px; font-weight: bold; margin-bottom: 10px; border-bottom: 1px solid #ddd; padding-bottom: 5px; }\n          .items-table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }\n          .items-table th, .items-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }\n          .items-table th { background-color: #f5f5f5; font-weight: bold; }\n          .text-center { text-align: center; }\n          .signature-section { display: flex; justify-content: space-between; margin-top: 40px; }\n          .signature-item { text-align: center; }\n          .signature-line { width: 120px; height: 1px; border-bottom: 1px solid #333; margin-bottom: 5px; }\n          @media print { body { margin: 0; } }\n        </style>\n      </head>\n      <body>${e}</body>\n    </html>\n  `),a.document.close(),a.print()},D=()=>{_.info("PDF导出功能开发中...")};return(a,t)=>{const l=i("el-button"),_=i("el-dialog");return o(),d(_,{"model-value":e.visible,"onUpdate:modelValue":t[1]||(t[1]=e=>a.$emit("update:visible",e)),title:"返厂维修汇总单",width:"800px","close-on-click-modal":!1,class:"repair-summary-dialog"},{footer:r((()=>[n("div",ge,[u(l,{onClick:t[0]||(t[0]=e=>a.$emit("update:visible",!1))},{default:r((()=>t[22]||(t[22]=[b("关闭")]))),_:1}),u(l,{type:"primary",onClick:V},{default:r((()=>t[23]||(t[23]=[b("打印汇总单")]))),_:1}),u(l,{type:"success",onClick:D},{default:r((()=>t[24]||(t[24]=[b("导出PDF")]))),_:1})])])),default:r((()=>{return[n("div",{class:"summary-document",ref_key:"summaryRef",ref:s},[n("div",j,[n("div",O,[t[5]||(t[5]=n("h2",null,"设备返厂维修汇总单",-1)),n("div",R,[n("div",P,[t[2]||(t[2]=n("span",{class:"label"},"单据编号：",-1)),n("span",$,y(e.orderData.orderCode),1)]),n("div",F,[t[3]||(t[3]=n("span",{class:"label"},"创建日期：",-1)),n("span",T,y(k(e.orderData.createdAt)),1)]),n("div",A,[t[4]||(t[4]=n("span",{class:"label"},"维修单号：",-1)),n("span",B,y(e.orderData.orderCode),1)])])]),n("div",{class:g(["priority-badge",(a=e.orderData.priority,{1:"urgent",2:"high",3:"medium",4:"low"}[a]||"medium")])},y(I(e.orderData.priority)),3)]),n("div",E,[t[11]||(t[11]=n("h3",{class:"section-title"},"基本信息",-1)),n("div",H,[n("div",K,[t[6]||(t[6]=n("span",{class:"label"},"维修标题：",-1)),n("span",L,y(e.orderData.title),1)]),n("div",M,[t[7]||(t[7]=n("span",{class:"label"},"维修供应商：",-1)),n("span",Y,y(e.orderData.supplierName),1)]),n("div",G,[t[8]||(t[8]=n("span",{class:"label"},"预估费用：",-1)),n("span",J,y(x(e.orderData.estimatedCost)),1)]),n("div",W,[t[9]||(t[9]=n("span",{class:"label"},"预估工期：",-1)),n("span",X,y(e.orderData.estimatedDays)+"天",1)]),n("div",Z,[t[10]||(t[10]=n("span",{class:"label"},"维修描述：",-1)),n("span",ee,y(e.orderData.description),1)])])]),n("div",ae,[t[13]||(t[13]=n("h3",{class:"section-title"},"返厂物品明细",-1)),n("table",te,[t[12]||(t[12]=n("thead",null,[n("tr",null,[n("th",{width:"60"},"序号"),n("th",{width:"120"},"备件编号"),n("th",null,"备件名称"),n("th",{width:"80"},"数量"),n("th",{width:"120"},"序列号"),n("th",null,"故障描述")])],-1)),n("tbody",null,[(o(!0),p(v,null,f(e.orderData.repairItems,((e,a)=>(o(),p("tr",{key:a},[n("td",le,y(a+1),1),n("td",null,y(e.partCode),1),n("td",null,y(e.partName),1),n("td",se,y(e.quantity),1),n("td",null,y(e.serialNumber||"-"),1),n("td",null,y(e.faultDescription||"-"),1)])))),128))])])]),n("div",ie,[t[18]||(t[18]=n("h3",{class:"section-title"},"汇总统计",-1)),n("div",de,[n("div",oe,[t[14]||(t[14]=n("span",{class:"stat-label"},"返厂物品种类：",-1)),n("span",re,y(e.orderData.repairItems.length)+"种",1)]),n("div",ne,[t[15]||(t[15]=n("span",{class:"stat-label"},"返厂物品总数：",-1)),n("span",ue,y(m.value)+"件",1)]),n("div",pe,[t[16]||(t[16]=n("span",{class:"stat-label"},"预估维修费用：",-1)),n("span",ce,y(x(e.orderData.estimatedCost)),1)]),n("div",me,[t[17]||(t[17]=n("span",{class:"stat-label"},"预估完成时间：",-1)),n("span",ve,y(h.value),1)])])]),e.orderData.notes?(o(),p("div",fe,[t[19]||(t[19]=n("h3",{class:"section-title"},"备注信息",-1)),n("div",be,y(e.orderData.notes),1)])):c("",!0),t[20]||(t[20]=n("div",{class:"signature-section"},[n("div",{class:"signature-item"},[n("div",{class:"signature-line"}),n("div",{class:"signature-label"},"申请人签名"),n("div",{class:"signature-date"},"日期：_______")]),n("div",{class:"signature-item"},[n("div",{class:"signature-line"}),n("div",{class:"signature-label"},"部门主管签名"),n("div",{class:"signature-date"},"日期：_______")]),n("div",{class:"signature-item"},[n("div",{class:"signature-line"}),n("div",{class:"signature-label"},"设备管理员签名"),n("div",{class:"signature-date"},"日期：_______")])],-1)),t[21]||(t[21]=n("div",{class:"document-footer"},[n("div",{class:"footer-info"},[n("span",null,"本单据一式三份，申请部门、设备管理部门、财务部门各执一份")]),n("div",{class:"page-number"}," 第 1 页 共 1 页 ")],-1))],512)];var a})),_:1},8,["model-value"])}}},[["__scopeId","data-v-cfc5085a"]]);const he={key:0,class:"empty-state"},_e={key:1,class:"quick-summary"},we={class:"dialog-footer"},ke=e({__name:"RepairOrderDialog",props:{visible:{type:Boolean,default:!1},orderData:{type:Object,default:()=>({})},mode:{type:String,default:"create"}},emits:["update:visible","submit"],setup(e,{emit:m}){const g=e,h=m,x=a(),I=a(!1),U=a(!1),z=a(!1),S=a([]),q=a([]),N=a({}),j=t({id:null,title:"",description:"",priority:3,supplierId:null,faultId:null,estimatedCost:null,estimatedDays:null,notes:"",repairItems:[]}),O={title:[{required:!0,message:"请输入维修标题",trigger:"blur"}],description:[{required:!0,message:"请输入维修描述",trigger:"blur"}],priority:[{required:!0,message:"请选择优先级",trigger:"change"}],supplierId:[{required:!0,message:"请选择维修供应商",trigger:"change"}]},R=w((()=>j.repairItems.reduce(((e,a)=>e+(a.quantity||0)),0))),P=w((()=>j.title&&j.description&&j.supplierId&&j.repairItems.length>0)),$=async()=>{try{const a=await(e={status:0},k({url:"/v2/faults",method:"get",params:e}));a.success&&(q.value=a.data.items||[])}catch(a){}var e},F=()=>{I.value=!0},T=e=>{e.forEach((e=>{j.repairItems.find((a=>a.partId===e.id))||j.repairItems.push({partId:e.id,partCode:e.code,partName:e.name,availableQuantity:e.stockQuantity,quantity:1,faultDescription:"",serialNumber:""})})),I.value=!1},A=e=>{if(!e)return"未选择";const a=S.value.find((a=>a.id===e));return a?a.name:"未知供应商"},B=async()=>{try{if(await x.value.validate(),0===j.repairItems.length)return void _.warning("请至少选择一个维修物品");z.value=!0;const e={...j,repairItems:j.repairItems.map((e=>({partId:e.partId,quantity:e.quantity,faultDescription:e.faultDescription,serialNumber:e.serialNumber})))};let a;a="create"===g.mode?await D(e):await C(j.id,e),a.success?(_.success("create"===g.mode?"维修单创建成功":"维修单更新成功"),"create"===g.mode&&(N.value={...a.data,supplierName:A(j.supplierId),repairItems:j.repairItems,createdAt:new Date},h("update:visible",!1),H(),setTimeout((()=>{U.value=!0}),300)),h("submit",a.data)):_.error("操作失败: "+a.message)}catch(e){_.error("提交失败")}finally{z.value=!1}},E=()=>{h("update:visible",!1),H()},H=()=>{Object.assign(j,{id:null,title:"",description:"",priority:3,supplierId:null,faultId:null,estimatedCost:null,estimatedDays:null,notes:"",repairItems:[]})};return l((()=>g.visible),(e=>{}),{immediate:!0}),l((()=>g.orderData),(e=>{e&&Object.keys(e).length>0&&Object.assign(j,{...e,repairItems:e.repairItems||[]})}),{deep:!0,immediate:!0}),s((()=>{(async()=>{try{const e=await V();e.success&&(S.value=e.data||[])}catch(e){}})(),$()})),(a,t)=>{const l=i("el-input"),s=i("el-form-item"),m=i("el-col"),_=i("el-option"),w=i("el-select"),k=i("el-row"),V=i("el-input-number"),D=i("el-button"),C=i("el-divider"),$=i("el-table-column"),A=i("el-table"),H=i("el-empty"),K=i("el-tag"),L=i("el-form"),M=i("el-dialog");return o(),d(M,{"model-value":g.visible,"onUpdate:modelValue":t[10]||(t[10]=e=>h("update:visible",e)),title:"create"===e.mode?"创建返厂维修单":"编辑返厂维修单",width:"1200px",onClose:E,class:"repair-order-dialog"},{footer:r((()=>[n("div",we,[u(D,{onClick:E},{default:r((()=>t[14]||(t[14]=[b("取消")]))),_:1}),u(D,{type:"primary",onClick:B,disabled:!P.value,loading:z.value},{default:r((()=>[b(y("create"===e.mode?"创建维修单":"保存修改"),1)])),_:1},8,["disabled","loading"])])])),default:r((()=>[u(L,{ref_key:"formRef",ref:x,model:j,rules:O,"label-width":"100px",size:"small"},{default:r((()=>[u(k,{gutter:16},{default:r((()=>[u(m,{span:12},{default:r((()=>[u(s,{label:"维修标题",prop:"title"},{default:r((()=>[u(l,{modelValue:j.title,"onUpdate:modelValue":t[0]||(t[0]=e=>j.title=e),placeholder:"请输入维修标题"},null,8,["modelValue"])])),_:1})])),_:1}),u(m,{span:12},{default:r((()=>[u(s,{label:"优先级",prop:"priority"},{default:r((()=>[u(w,{modelValue:j.priority,"onUpdate:modelValue":t[1]||(t[1]=e=>j.priority=e),placeholder:"中",style:{width:"100%"}},{default:r((()=>[u(_,{label:"紧急",value:1}),u(_,{label:"高",value:2}),u(_,{label:"中",value:3}),u(_,{label:"低",value:4})])),_:1},8,["modelValue"])])),_:1})])),_:1})])),_:1}),u(s,{label:"维修描述",prop:"description"},{default:r((()=>[u(l,{modelValue:j.description,"onUpdate:modelValue":t[2]||(t[2]=e=>j.description=e),type:"textarea",rows:2,placeholder:"请输入维修描述"},null,8,["modelValue"])])),_:1}),u(k,{gutter:16},{default:r((()=>[u(m,{span:12},{default:r((()=>[u(s,{label:"维修供应商",prop:"supplierId"},{default:r((()=>[u(w,{modelValue:j.supplierId,"onUpdate:modelValue":t[3]||(t[3]=e=>j.supplierId=e),placeholder:"请选择维修供应商",style:{width:"100%"}},{default:r((()=>[(o(!0),p(v,null,f(S.value,(e=>(o(),d(_,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1})])),_:1}),u(m,{span:12},{default:r((()=>[u(s,{label:"关联故障"},{default:r((()=>[u(w,{modelValue:j.faultId,"onUpdate:modelValue":t[4]||(t[4]=e=>j.faultId=e),placeholder:"选择关联故障（可选）",clearable:"",style:{width:"100%"}},{default:r((()=>[(o(!0),p(v,null,f(q.value,(e=>(o(),d(_,{key:e.id,label:e.title,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1})])),_:1})])),_:1}),u(k,{gutter:16},{default:r((()=>[u(m,{span:8},{default:r((()=>[u(s,{label:"预估费用"},{default:r((()=>[u(V,{modelValue:j.estimatedCost,"onUpdate:modelValue":t[5]||(t[5]=e=>j.estimatedCost=e),min:0,precision:2,"controls-position":"right",style:{width:"100%"}},null,8,["modelValue"])])),_:1})])),_:1}),u(m,{span:8},{default:r((()=>[u(s,{label:"预估天数"},{default:r((()=>[u(V,{modelValue:j.estimatedDays,"onUpdate:modelValue":t[6]||(t[6]=e=>j.estimatedDays=e),min:1,"controls-position":"right",style:{width:"100%"}},null,8,["modelValue"])])),_:1})])),_:1}),u(m,{span:8},{default:r((()=>[u(s,{label:"备注"},{default:r((()=>[u(l,{modelValue:j.notes,"onUpdate:modelValue":t[7]||(t[7]=e=>j.notes=e),placeholder:"请输入备注"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),u(C,{"content-position":"left"},{default:r((()=>[t[12]||(t[12]=n("span",{style:{"font-weight":"bold"}},"维修物品",-1)),u(D,{type:"primary",size:"small",onClick:F,style:{"margin-left":"10px"}},{default:r((()=>t[11]||(t[11]=[b("选择备件")]))),_:1})])),_:1}),u(A,{data:j.repairItems,border:"",size:"small",class:"items-table","max-height":"300"},{default:r((()=>[u($,{prop:"partCode",label:"备件编号",width:"120"}),u($,{prop:"partName",label:"备件名称","min-width":"150"}),u($,{label:"数量",width:"100"},{default:r((({row:e})=>[u(V,{modelValue:e.quantity,"onUpdate:modelValue":a=>e.quantity=a,min:1,max:e.availableQuantity,size:"small","controls-position":"right"},null,8,["modelValue","onUpdate:modelValue","max"])])),_:1}),u($,{label:"故障描述","min-width":"150"},{default:r((({row:e})=>[u(l,{modelValue:e.faultDescription,"onUpdate:modelValue":a=>e.faultDescription=a,placeholder:"故障描述",size:"small"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),u($,{label:"序列号",width:"120"},{default:r((({row:e})=>[u(l,{modelValue:e.serialNumber,"onUpdate:modelValue":a=>e.serialNumber=a,placeholder:"序列号",size:"small"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),u($,{label:"操作",width:"80",fixed:"right"},{default:r((({$index:e})=>[u(D,{type:"danger",size:"small",link:"",onClick:a=>{return t=e,void j.repairItems.splice(t,1);var t}},{default:r((()=>t[13]||(t[13]=[b("删除")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"]),0===j.repairItems.length?(o(),p("div",he,[u(H,{description:"暂无维修物品，请点击上方选择备件添加","image-size":80})])):c("",!0),j.repairItems.length>0?(o(),p("div",_e,[u(K,{type:"info"},{default:r((()=>[b("物品种类："+y(j.repairItems.length)+"种",1)])),_:1}),u(K,{type:"success"},{default:r((()=>[b("总数量："+y(R.value)+"件",1)])),_:1}),j.estimatedCost?(o(),d(K,{key:0,type:"warning"},{default:r((()=>[b("预估费用：¥"+y(j.estimatedCost.toFixed(2)),1)])),_:1})):c("",!0),j.estimatedDays?(o(),d(K,{key:1,type:"primary"},{default:r((()=>[b("预估工期："+y(j.estimatedDays)+"天",1)])),_:1})):c("",!0)])):c("",!0)])),_:1},8,["model"]),u(Q,{visible:I.value,"onUpdate:visible":t[8]||(t[8]=e=>I.value=e),"selected-parts":j.repairItems,onConfirm:T},null,8,["visible","selected-parts"]),u(ye,{visible:U.value,"onUpdate:visible":t[9]||(t[9]=e=>U.value=e),"order-data":N.value},null,8,["visible","order-data"])])),_:1},8,["model-value","title"])}}},[["__scopeId","data-v-3203f48f"]]);export{ke as R};
