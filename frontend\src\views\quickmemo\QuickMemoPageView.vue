// File: frontend/src/views/quickmemo/QuickMemoPageView.vue
// Description: 独立页面，用于展示随手记看板。

<template>
  <div class="memo-page-container" ref="pageContainer">
    <!-- 顶部输入区域 -->
    <div class="top-input-bar">
      <el-input
        v-model="newMemoTitle"
        placeholder="写点什么..."
        clearable
        class="input-new-memo"
        @keyup.enter="handleAddNewMemo"
        size="large"
      />
      <el-button type="primary" @click="handleAddNewMemo" class="add-memo-button" size="large">添加便签</el-button>
    </div>

    <!-- 主要内容区域 -->
    <div class="content-area">
      <!-- 便利贴墙视图 -->
      <div class="content-panel">
        <quick-memo-board />
      </div>
    </div>
    
    <!-- 新建/编辑随手记抽屉 (保留，以防需要更详细的编辑) -->
    <memo-form-drawer />
    
    <!-- 悬浮按钮 (移除) -->
    <!-- 
    <div class="floating-action-button" @click="handleAddMemo">
      <el-icon><Plus /></el-icon>
    </div> 
    -->
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
// import { Plus } from '@element-plus/icons-vue'; // Plus 图标不再直接使用
import QuickMemoBoard from '@/components/QuickMemo/QuickMemoBoard.vue';
import MemoFormDrawer from '@/components/QuickMemo/MemoFormDrawer.vue';
import { useQuickMemoStore } from '@/stores/modules/quickMemo';
import { ElMessage } from 'element-plus';

const quickMemoStore = useQuickMemoStore();
const pageContainer = ref(null);
const newMemoTitle = ref('');

// const handleAddMemo = () => { // 旧的FAB点击事件，现由顶部按钮处理
//   quickMemoStore.openMemoDrawer('create');
// };

const handleAddNewMemo = async () => {
  if (!newMemoTitle.value.trim()) {
    ElMessage.warning('便签内容不能为空');
    return;
  }
  try {
    const randomPositionX = Math.floor(Math.random() * 300) + 50; // 示例随机位置
    const randomPositionY = Math.floor(Math.random() * 200) + 50;

    await quickMemoStore.createQuickMemo({ 
      title: newMemoTitle.value,
      content: newMemoTitle.value, // 默认为标题内容
      positionX: randomPositionX,
      positionY: randomPositionY,
      sizeWidth: 240, // 与新卡片样式宽度一致
      sizeHeight: 150, // 示例高度
      isPinned: false,
      categoryId: null, // 默认无分类
    });
    newMemoTitle.value = ''; 
    ElMessage.success('便签已添加');
    // createQuickMemo 内部应已处理列表刷新
  } catch (error) {
    ElMessage.error('添加便签失败');
    console.error('Failed to add new memo:', error);
  }
};

onMounted(() => {
  // 预加载随手记数据
  quickMemoStore.fetchQuickMemos();
});

</script>

<style scoped>
.memo-page-container {
  display: flex;
  flex-direction: column; /* 改为纵向排列 */
  height: 100%;
  position: relative;
  overflow: hidden;
  background-color: #e9eef3; /* 调整为类似截图的浅灰色背景 */
  padding: 24px; /* 页面整体内边距 */
  box-sizing: border-box;
}

.top-input-bar {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
  padding: 24px;
  background-color: #ffffff; /* 白色背景 */
  border-radius: 8px; /* 圆角 */
  box-shadow: 0 4px 12px rgba(0,0,0,0.08); /* 细致阴影 */
}

.input-new-memo {
  flex-grow: 1;
  margin-right: 16px;
}

.input-new-memo :deep(.el-input__wrapper) {
  box-shadow: none !important; /* 移除输入框聚焦时的默认阴影，如果需要 */
}
.input-new-memo :deep(.el-input__inner) {
  font-size: 16px; /* 增大字体 */
}

.add-memo-button {
  font-size: 16px;
  min-width: 100px;
}

.content-area {
  flex: 1;
  position: relative;
  overflow: hidden; /* 重要：确保内容区域本身不产生滚动条，由content-panel处理 */
  background-color: transparent; 
}

.content-panel {
  width: 100%;
  height: 100%;
  overflow-y: auto; /* 只允许垂直滚动 */
  overflow-x: hidden; /* 禁止水平滚动 */
  position: relative;
  padding-right: 8px; /* 为滚动条留出一些空间，防止内容遮挡 */
}

/* 移除旧的悬浮按钮样式 */
/*
.floating-action-button {
  position: fixed;
  right: 24px;
  bottom: 24px;
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background-color: var(--el-color-primary);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 3px 5px -1px rgba(0, 0, 0, 0.2), 
              0 6px 10px 0 rgba(0, 0, 0, 0.14), 
              0 1px 18px 0 rgba(0, 0, 0, 0.12);
  cursor: pointer;
  transition: all 0.3s;
  z-index: 100;
}

.floating-action-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 8px -1px rgba(0, 0, 0, 0.2), 
              0 8px 14px 0 rgba(0, 0, 0, 0.14), 
              0 2px 20px 0 rgba(0, 0, 0, 0.12);
}

.floating-action-button .el-icon {
  font-size: 24px;
}
*/
</style> 