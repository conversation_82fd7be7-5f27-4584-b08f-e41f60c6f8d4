{"ConnectionStrings": {"DefaultConnection": "server=localhost;port=3306;database=itassets;user=aerospace_itam;password=*********;"}, "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "System": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "Logs/itassets-server-.log", "rollingInterval": "Day", "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}URL: {AbsoluteUrl}{NewLine}远程IP: {RemoteIpAddress}{NewLine}Referer: {Refer<PERSON>}{NewLine}User-Agent: {UserAgent}{NewLine}{Exception}{NewLine}--- 详细属性: {Properties:j}{NewLine}"}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"], "Properties": {"Application": "ItAssetsSystem"}}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "AllowedHosts": "*", "Database": {"EnableSeedData": false}, "NetworkMonitor": {"CheckIntervalMs": 10000, "TimeoutMs": 5000, "RetryCount": 3, "CheckUrl": "https://www.baidu.com", "AutoStartMonitoring": true}, "OfflineQueue": {"StoragePath": "Data/OfflineOperations", "AutoSaveIntervalMs": 60000, "MaxRetryCount": 5, "RetryDelayBaseMs": 1000, "DefaultOperationTimeoutMs": 30000, "ConcurrentProcessingCount": 3, "AutoProcessPendingOnReconnect": true}, "Backup": {"EnableAutoBackup": true, "BackupFrequencyHours": 24, "MaxBackupsToKeep": 7, "BackupStoragePath": "Data/Backups"}, "Import": {"ImportFilesPath": "Data/Import", "TemplatesPath": "Data/Templates", "MaxFileSize": 10485760, "AllowedFormats": ["csv", "xlsx", "json"]}, "Export": {"ExportFilesPath": "Data/Export", "MaxExportRecords": 50000, "EnableEncryption": false, "DefaultPageSize": 1000}, "FileStorageSettings": {"BasePhysicalPathForFilesVirtualDir": "PLEASE_UPDATE_THIS_PATH_TO_IIS_VIRTUAL_DIR_PHYSICAL_PATH"}}