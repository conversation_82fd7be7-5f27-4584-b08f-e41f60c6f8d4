// File: Api/V2/TasksController.cs
// Description: 任务管理API控制器

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using ItAssetsSystem.Application.Common.Dtos;
using ItAssetsSystem.Application.Features.Tasks.Dtos;
using ItAssetsSystem.Application.Features.Tasks.Services;
using ItAssetsSystem.Application.Features.Tasks.Queries;
using ItAssetsSystem.Application.Features.Tasks.Commands;
using ItAssetsSystem.Core.Services;
using PagedResultType = ItAssetsSystem.Core.Interfaces.Services.PagedResult<ItAssetsSystem.Application.Features.Tasks.Dtos.CommentDto>;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using MediatR;
using ItAssetsSystem.Services.Interfaces;
using ItAssetsSystem.Models.Entities.Gamification;
using ItAssetsSystem.Core.Abstractions;

namespace ItAssetsSystem.Api.V2
{
    /// <summary>
    /// 任务管理API控制器
    /// </summary>
    [ApiController]
    [Route("api/v2/tasks")]
    // [Authorize] // 暂时禁用认证以便测试
    public class TasksController : ControllerBase
    {
        private readonly ITaskService _taskService;
        private readonly ILogger<TasksController> _logger;
        private readonly TaskClaimService _taskClaimService;
        private readonly IMediator _mediator;
        private readonly IUniversalGamificationService _universalGamificationService;
        private readonly ITaskRepository _taskRepository;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="taskService">任务服务</param>
        /// <param name="logger">日志记录器</param>
        /// <param name="taskClaimService">任务领取服务</param>
        /// <param name="mediator">MediatR中介者</param>
        /// <param name="universalGamificationService">通用游戏化服务</param>
        /// <param name="taskRepository">任务仓储</param>
        public TasksController(ITaskService taskService, ILogger<TasksController> logger, TaskClaimService taskClaimService, IMediator mediator, IUniversalGamificationService universalGamificationService, ITaskRepository taskRepository)
        {
            _taskService = taskService ?? throw new ArgumentNullException(nameof(taskService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _taskClaimService = taskClaimService ?? throw new ArgumentNullException(nameof(taskClaimService));
            _mediator = mediator ?? throw new ArgumentNullException(nameof(mediator));
            _universalGamificationService = universalGamificationService ?? throw new ArgumentNullException(nameof(universalGamificationService));
            _taskRepository = taskRepository ?? throw new ArgumentNullException(nameof(taskRepository));
        }

        /// <summary>
        /// 获取当前用户ID
        /// </summary>
        /// <returns>当前用户ID</returns>
        private int GetCurrentUserId()
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
            if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out int userId))
            {
                throw new UnauthorizedAccessException("未能获取有效的用户ID");
            }
            return userId;
        }

        /// <summary>
        /// 获取任务列表（分页）- 优化版本
        /// </summary>
        /// <param name="page">页码</param>
        /// <param name="size">每页大小</param>
        /// <param name="keyword">搜索关键词</param>
        /// <param name="status">任务状态</param>
        /// <param name="priority">优先级</param>
        /// <param name="taskType">任务类型</param>
        /// <param name="assigneeId">负责人ID</param>
        /// <param name="createdBy">创建人ID</param>
        /// <param name="assetId">资产ID</param>
        /// <param name="locationId">位置ID</param>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <param name="parentTaskId">父任务ID</param>
        /// <param name="projectId">项目ID</param>
        /// <param name="sortBy">排序字段</param>
        /// <param name="sortDirection">排序方向</param>
        /// <param name="includeDeleted">是否包含已删除</param>
        /// <returns>任务分页列表</returns>
        [HttpGet("paged")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<PaginatedResult<TaskDto>>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<PaginatedResult<TaskDto>>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ApiResponse<PaginatedResult<TaskDto>>))]
        public async Task<IActionResult> GetTasksPaged(
            [FromQuery] int page = 1,
            [FromQuery] int size = 20,
            [FromQuery] string? keyword = null,
            [FromQuery] string? status = null,
            [FromQuery] string? priority = null,
            [FromQuery] string? taskType = null,
            [FromQuery] int? assigneeId = null,
            [FromQuery] int? createdBy = null,
            [FromQuery] int? assetId = null,
            [FromQuery] int? locationId = null,
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null,
            [FromQuery] long? parentTaskId = null,
            [FromQuery] long? projectId = null,
            [FromQuery] string? sortBy = "CreationTimestamp",
            [FromQuery] string? sortDirection = "desc",
            [FromQuery] bool includeDeleted = false)
        {
            try
            {
                var query = new GetTasksPagedQuery
                {
                    Page = page,
                    Size = size,
                    Keyword = keyword,
                    Status = status,
                    Priority = priority,
                    TaskType = taskType,
                    AssigneeId = assigneeId,
                    CreatedBy = createdBy,
                    AssetId = assetId,
                    LocationId = locationId,
                    StartDate = startDate,
                    EndDate = endDate,
                    ParentTaskId = parentTaskId,
                    ProjectId = projectId,
                    SortBy = sortBy,
                    SortDirection = sortDirection,
                    IncludeDeleted = includeDeleted
                };

                var result = await _mediator.Send(query);
                return Ok(ApiResponse<PaginatedResult<TaskDto>>.CreateSuccess(result, "获取任务列表成功"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取任务分页列表时发生错误");
                return StatusCode(StatusCodes.Status500InternalServerError,
                    ApiResponse<PaginatedResult<TaskDto>>.CreateFail("获取任务分页列表时发生服务器错误: " + ex.Message));
            }
        }

        /// <summary>
        /// 获取任务列表（分页）- 优化版本，查询真实数据库数据
        /// </summary>
        /// <param name="pageNumber">页码</param>
        /// <param name="pageSize">每页大小</param>
        /// <param name="status">状态筛选</param>
        /// <param name="priority">优先级筛选</param>
        /// <param name="assigneeUserId">负责人筛选</param>
        /// <param name="searchTerm">搜索关键词</param>
        /// <param name="forceRefresh">强制刷新</param>
        /// <returns>任务列表</returns>
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetTasks(
            [FromQuery] int pageNumber = 1,
            [FromQuery] int pageSize = 10,
            [FromQuery] string? status = null,
            [FromQuery] string? priority = null,
            [FromQuery] int? assigneeUserId = null,
            [FromQuery] string? searchTerm = null,
            [FromQuery] bool forceRefresh = false)
        {
            _logger.LogInformation("V2 获取任务列表: pageNumber={PageNumber}, pageSize={PageSize}", pageNumber, pageSize);

            try
            {
                // 使用TaskService获取真实数据，但需要获取分页信息
                var queryParameters = new TaskQueryParametersDto
                {
                    PageNumber = pageNumber,
                    PageSize = pageSize,
                    Status = status,
                    Priority = priority,
                    AssigneeUserId = assigneeUserId,
                    SearchTerm = searchTerm ?? string.Empty,
                    ForceRefresh = forceRefresh
                };

                // 获取当前用户ID用于优化多负责人领取状态显示
                var currentUserId = GetCurrentUserId();

                // 使用TaskService获取分页数据（避免重复分页）
                var tasksList = await _taskService.GetTasksAsync(queryParameters, currentUserId);
                if (!tasksList.Success)
                {
                    return StatusCode(500, new { success = false, message = tasksList.Error });
                }

                // TaskService已经处理了分页，直接使用结果
                var tasks = tasksList.Data;

                var totalCount = tasksList.Data.Count;

                var response = new
                {
                    success = true,
                    data = tasks,
                    pagination = new
                    {
                        totalCount = totalCount,
                        pageNumber = pageNumber,
                        pageSize = pageSize,
                        totalPages = (int)Math.Ceiling((double)totalCount / pageSize)
                    },
                    message = "获取任务列表成功"
                };

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "V2 获取任务列表异常");
                return StatusCode(500, new { success = false, message = "获取任务列表失败: " + ex.Message });
            }
        }

        /// <summary>
        /// 获取状态显示名称
        /// </summary>
        private string GetStatusDisplayName(string status)
        {
            return status switch
            {
                "Todo" => "待办",
                "InProgress" => "进行中",
                "Completed" => "已完成",
                "Cancelled" => "已取消",
                "Paused" => "暂停",
                _ => status ?? "未知"
            };
        }

        /// <summary>
        /// 获取优先级显示名称
        /// </summary>
        private string GetPriorityDisplayName(string priority)
        {
            return priority switch
            {
                "Low" => "低",
                "Medium" => "中",
                "High" => "高",
                "Critical" => "紧急",
                _ => priority ?? "中"
            };
        }

        /// <summary>
        /// 获取任务详情
        /// </summary>
        /// <param name="id">任务ID</param>
        /// <param name="includeComments">是否包含评论</param>
        /// <param name="includeAttachments">是否包含附件</param>
        /// <param name="includeHistory">是否包含历史记录</param>
        /// <returns>任务详情</returns>
        [HttpGet("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<TaskDto>))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponse<TaskDto>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ApiResponse<TaskDto>))]
        public async Task<IActionResult> GetTask(long id,
            [FromQuery] bool includeComments = true,
            [FromQuery] bool includeAttachments = true,
            [FromQuery] bool includeHistory = true)
        {
            try
            {
                _logger.LogInformation("获取任务详情: TaskId={TaskId}, 包含评论={IncludeComments}, 包含附件={IncludeAttachments}, 包含历史={IncludeHistory}",
                    id, includeComments, includeAttachments, includeHistory);

                // 新增：预加载关联数据
                var result = await _taskService.GetTaskByIdAsync(id, includeComments, includeAttachments, includeHistory);
                if (!result.Success)
                {
                    _logger.LogWarning("任务详情获取失败: TaskId={TaskId}, Error={Error}", id, result.Error);
                    return NotFound(result);
                }

                _logger.LogInformation("成功获取任务详情: TaskId={TaskId}", id);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取任务详情时发生错误: ID={TaskId}", id);
                return StatusCode(StatusCodes.Status500InternalServerError,
                    ApiResponseFactory.CreateFail<TaskDto>("获取任务详情时发生服务器错误: " + ex.Message));
            }
        }

        /// <summary>
        /// 创建任务
        /// </summary>
        /// <param name="request">创建任务请求</param>
        /// <returns>创建的任务</returns>
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status201Created, Type = typeof(ApiResponse<TaskDto>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<TaskDto>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ApiResponse<TaskDto>))]
        public async Task<IActionResult> CreateTask([FromBody] CreateTaskRequestDto request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ApiResponseFactory.CreateFail<TaskDto>("请求数据无效"));
                }

                var currentUserId = GetCurrentUserId();
                var result = await _taskService.CreateTaskAsync(request, currentUserId);

                if (!result.Success)
                {
                    return BadRequest(result);
                }

                // 触发任务创建奖励
                try
                {
                    await _universalGamificationService.TriggerBehaviorRewardAsync(
                        currentUserId,
                        BehaviorCodes.TASK_CREATED,
                        result.Data.TaskId,
                        description: $"创建任务: {result.Data.Name}"
                    );
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "触发任务创建奖励失败: TaskId={TaskId}", result.Data.TaskId);
                }

                return CreatedAtAction(nameof(GetTask), new { id = result.Data.TaskId }, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建任务时发生错误");
                return StatusCode(StatusCodes.Status500InternalServerError, 
                    ApiResponseFactory.CreateFail<TaskDto>("创建任务时发生服务器错误: " + ex.Message));
            }
        }

        /// <summary>
        /// 更新任务
        /// </summary>
        /// <param name="id">任务ID</param>
        /// <param name="request">更新任务请求</param>
        /// <returns>更新后的任务</returns>
        [HttpPut("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<TaskDto>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<TaskDto>))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponse<TaskDto>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ApiResponse<TaskDto>))]
        public async Task<IActionResult> UpdateTask(long id, [FromBody] UpdateTaskRequestDto request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ApiResponseFactory.CreateFail<TaskDto>("请求数据无效"));
                }

                var currentUserId = GetCurrentUserId();
                var result = await _taskService.UpdateTaskAsync(id, request, currentUserId);
                
                if (!result.Success)
                {
                    return result.Error.Contains("未找到") ? NotFound(result) : BadRequest(result);
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新任务时发生错误: ID={TaskId}", id);
                return StatusCode(StatusCodes.Status500InternalServerError, 
                    ApiResponseFactory.CreateFail<TaskDto>("更新任务时发生服务器错误: " + ex.Message));
            }
        }

        /// <summary>
        /// 删除任务
        /// </summary>
        /// <param name="id">任务ID</param>
        /// <returns>操作结果</returns>
        [HttpDelete("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<bool>))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponse<bool>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ApiResponse<bool>))]
        public async Task<IActionResult> DeleteTask(long id)
        {
            try
            {
                var currentUserId = GetCurrentUserId();

                // 获取任务信息用于回退奖励
                var taskResult = await _taskService.GetTaskByIdAsync(id);

                var result = await _taskService.DeleteTaskAsync(id, currentUserId);

                if (!result.Success)
                {
                    return result.Error.Contains("未找到") ? NotFound(result) : BadRequest(result);
                }

                // 回退任务创建奖励
                if (taskResult.Success && taskResult.Data != null)
                {
                    try
                    {
                        await _universalGamificationService.RevertBehaviorRewardAsync(
                            taskResult.Data.CreatorUserId,
                            BehaviorCodes.TASK_CREATED,
                            id
                        );
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "回退任务创建奖励失败: TaskId={TaskId}", id);
                    }
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除任务时发生错误: ID={TaskId}", id);
                return StatusCode(StatusCodes.Status500InternalServerError, 
                    ApiResponseFactory.CreateFail<bool>("删除任务时发生服务器错误: " + ex.Message));
            }
        }

        /// <summary>
        /// 更新任务状态
        /// </summary>
        /// <param name="id">任务ID</param>
        /// <param name="request">更新状态请求</param>
        /// <returns>操作结果</returns>
        [HttpPatch("{id}/status")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<TaskDto>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<TaskDto>))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponse<TaskDto>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ApiResponse<TaskDto>))]
        public async Task<IActionResult> UpdateTaskStatus(long id, [FromBody] UpdateTaskStatusRequestDto request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ApiResponseFactory.CreateFail<TaskDto>("请求数据无效"));
                }

                var currentUserId = GetCurrentUserId();
                var result = await _taskService.UpdateTaskStatusAsync(id, request.Status, request.Remarks, currentUserId);
                
                if (!result.Success)
                {
                    return result.Error.Contains("未找到") ? NotFound(result) : BadRequest(result);
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新任务状态时发生错误: ID={TaskId}", id);
                return StatusCode(StatusCodes.Status500InternalServerError, 
                    ApiResponseFactory.CreateFail<TaskDto>("更新任务状态时发生服务器错误: " + ex.Message));
            }
        }

        /// <summary>
        /// 更新任务进度
        /// </summary>
        /// <param name="id">任务ID</param>
        /// <param name="request">更新进度请求</param>
        /// <returns>操作结果</returns>
        [HttpPatch("{id}/progress")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<TaskDto>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<TaskDto>))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponse<TaskDto>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ApiResponse<TaskDto>))]
        public async Task<IActionResult> UpdateTaskProgress(long id, [FromBody] UpdateTaskProgressRequestDto request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ApiResponseFactory.CreateFail<TaskDto>("请求数据无效"));
                }

                var currentUserId = GetCurrentUserId();
                var result = await _taskService.UpdateTaskProgressAsync(id, request.Progress, request.Remarks, currentUserId);
                
                if (!result.Success)
                {
                    return result.Error.Contains("未找到") ? NotFound(result) : BadRequest(result);
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新任务进度时发生错误: ID={TaskId}", id);
                return StatusCode(StatusCodes.Status500InternalServerError, 
                    ApiResponseFactory.CreateFail<TaskDto>("更新任务进度时发生服务器错误: " + ex.Message));
            }
        }

        /// <summary>
        /// 分配任务
        /// </summary>
        /// <param name="id">任务ID</param>
        /// <param name="request">分配任务请求</param>
        /// <returns>操作结果</returns>
        [HttpPatch("{id}/assign")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<TaskDto>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<TaskDto>))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponse<TaskDto>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ApiResponse<TaskDto>))]
        public async Task<IActionResult> AssignTask(long id, [FromBody] AssignTaskRequestDto request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ApiResponseFactory.CreateFail<TaskDto>("请求数据无效"));
                }

                var currentUserId = GetCurrentUserId();
                var result = await _taskService.AssignTaskAsync(id, request.AssigneeUserId, request.Remarks, currentUserId);
                
                if (!result.Success)
                {
                    return result.Error.Contains("未找到") ? NotFound(result) : BadRequest(result);
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "分配任务时发生错误: ID={TaskId}", id);
                return StatusCode(StatusCodes.Status500InternalServerError, 
                    ApiResponseFactory.CreateFail<TaskDto>("分配任务时发生服务器错误: " + ex.Message));
            }
        }

        /// <summary>
        /// 完成任务
        /// </summary>
        /// <param name="id">任务ID</param>
        /// <param name="request">完成任务请求</param>
        /// <returns>操作结果</returns>
        [HttpPatch("{id}/complete")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<TaskDto>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<TaskDto>))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponse<TaskDto>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ApiResponse<TaskDto>))]
        public async Task<IActionResult> CompleteTask(long id, [FromBody] CompleteTaskRequestDto request = null)
        {
            try
            {
                var currentUserId = GetCurrentUserId();
                var remarks = request?.Remarks ?? string.Empty;

                // 获取任务信息用于计算奖励
                var taskResult = await _taskService.GetTaskByIdAsync(id);

                var result = await _taskService.CompleteTaskAsync(id, remarks, currentUserId);

                if (!result.Success)
                {
                    return result.Error.Contains("未找到") ? NotFound(result) : BadRequest(result);
                }

                // 触发任务完成奖励
                if (taskResult.Success && taskResult.Data != null)
                {
                    try
                    {
                        // 检查是否按时完成
                        var isOnTime = taskResult.Data.PlanEndDate == null || DateTime.Now <= taskResult.Data.PlanEndDate;

                        await _universalGamificationService.TriggerBehaviorRewardAsync(
                            currentUserId,
                            BehaviorCodes.TASK_COMPLETED,
                            id,
                            context: new { isOnTime = isOnTime },
                            description: $"完成任务: {taskResult.Data.Name}"
                        );
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "触发任务完成奖励失败: TaskId={TaskId}", id);
                    }
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "完成任务时发生错误: ID={TaskId}", id);
                return StatusCode(StatusCodes.Status500InternalServerError, 
                    ApiResponseFactory.CreateFail<TaskDto>("完成任务时发生服务器错误: " + ex.Message));
            }
        }

        /// <summary>
        /// 获取任务评论列表（分页）
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <param name="pageNumber">页码（从1开始）</param>
        /// <param name="pageSize">每页大小</param>
        /// <param name="topLevelOnly">是否只返回顶层评论</param>
        /// <param name="sortBy">排序字段</param>
        /// <param name="sortDirection">排序方向（asc或desc）</param>
        /// <returns>分页评论列表</returns>
        [HttpGet("{taskId}/comments")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<PagedResultType>))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponse<PagedResultType>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ApiResponse<PagedResultType>))]
        public async Task<IActionResult> GetTaskComments(
            long taskId,
            [FromQuery] int pageNumber = 1,
            [FromQuery] int pageSize = 20,
            [FromQuery] bool? topLevelOnly = null,
            [FromQuery] string sortBy = "CreationTimestamp",
            [FromQuery] string sortDirection = "asc",
            [FromQuery] bool? isEdited = null,
            [FromQuery] bool includeDeleted = false,
            [FromQuery] int? minContentLength = null,
            [FromQuery] int? maxContentLength = null,
            [FromQuery] bool? hasMentions = null)
        {
            try
            {
                var currentUserId = GetCurrentUserId();
                if (currentUserId == 0)
                {
                    return Unauthorized(ApiResponse<PagedResultType>.CreateFail("用户未认证"));
                }

                var query = new SearchCommentsQuery
                {
                    TaskId = taskId,
                    SearchKeyword = string.Empty,
                    UserId = null,
                    StartDate = null,
                    EndDate = null,
                    IsPinned = null,
                    PageNumber = pageNumber,
                    PageSize = pageSize,
                    SortBy = sortBy,
                    SortDirection = sortDirection,
                    CurrentUserId = currentUserId,
                    TopLevelOnly = topLevelOnly,
                    IsEdited = isEdited,
                    IncludeDeleted = includeDeleted,
                    MinContentLength = minContentLength,
                    MaxContentLength = maxContentLength,
                    HasMentions = hasMentions
                };

                var result = await _mediator.Send(query);

                if (result.Success)
                {
                    return Ok(result);
                }
                else
                {
                    return BadRequest(result);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取任务评论列表时发生错误: TaskId={TaskId}", taskId);
                return StatusCode(StatusCodes.Status500InternalServerError,
                    ApiResponse<PagedResultType>.CreateFail("获取任务评论列表时发生服务器错误: " + ex.Message));
            }
        }

        /// <summary>
        /// 获取任务评论列表（简单版本，不分页）
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <returns>评论列表</returns>
        [HttpGet("{taskId}/comments/simple")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<List<CommentDto>>))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponse<List<CommentDto>>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ApiResponse<List<CommentDto>>))]
        public async Task<IActionResult> GetTaskCommentsSimple(long taskId)
        {
            try
            {
                var result = await _taskService.GetTaskCommentsAsync(taskId);
                if (!result.Success)
                {
                    return NotFound(result);
                }
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取任务评论列表时发生错误: TaskId={TaskId}", taskId);
                return StatusCode(StatusCodes.Status500InternalServerError,
                    ApiResponseFactory.CreateFail<List<CommentDto>>("获取任务评论列表时发生服务器错误: " + ex.Message));
            }
        }

        /// <summary>
        /// 获取任务评论树结构（包含回复层次）
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <param name="maxDepth">最大深度（默认3层）</param>
        /// <param name="sortBy">排序字段</param>
        /// <param name="sortDirection">排序方向</param>
        /// <returns>评论树结构</returns>
        [HttpGet("{taskId}/comments/tree")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<List<CommentDto>>))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponse<List<CommentDto>>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ApiResponse<List<CommentDto>>))]
        public async Task<IActionResult> GetTaskCommentsTree(
            long taskId,
            [FromQuery] int maxDepth = 3,
            [FromQuery] string sortBy = "CreationTimestamp",
            [FromQuery] string sortDirection = "asc")
        {
            try
            {
                var currentUserId = GetCurrentUserId();
                if (currentUserId == 0)
                {
                    return Unauthorized(ApiResponse<List<CommentDto>>.CreateFail("用户未认证"));
                }

                var comments = await _taskService.GetCommentTreeAsync(taskId, maxDepth, sortBy, sortDirection);

                if (comments.Success)
                {
                    return Ok(comments);
                }
                else
                {
                    return BadRequest(comments);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取任务评论树结构时发生错误: TaskId={TaskId}", taskId);
                return StatusCode(StatusCodes.Status500InternalServerError,
                    ApiResponse<List<CommentDto>>.CreateFail("获取任务评论树结构时发生服务器错误: " + ex.Message));
            }
        }

        /// <summary>
        /// 添加任务评论
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <param name="request">评论请求</param>
        /// <returns>添加的评论</returns>
        [HttpPost("{taskId}/comments")]
        [ProducesResponseType(StatusCodes.Status201Created, Type = typeof(ApiResponse<CommentDto>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<CommentDto>))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponse<CommentDto>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ApiResponse<CommentDto>))]
        public async Task<IActionResult> AddTaskComment(long taskId, [FromBody] AddCommentRequestDto request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ApiResponseFactory.CreateFail<CommentDto>("请求数据无效"));
                }

                var currentUserId = GetCurrentUserId();
                var result = await _taskService.AddCommentAsync(taskId, request, currentUserId);
                
                if (!result.Success)
                {
                    return result.Error.Contains("未找到") ? NotFound(result) : BadRequest(result);
                }

                return CreatedAtAction(nameof(GetTaskComments), new { taskId }, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "添加任务评论时发生错误: TaskId={TaskId}", taskId);
                return StatusCode(StatusCodes.Status500InternalServerError, 
                    ApiResponseFactory.CreateFail<CommentDto>("添加任务评论时发生服务器错误: " + ex.Message));
            }
        }

        /// <summary>
        /// 更新任务评论
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <param name="commentId">评论ID</param>
        /// <param name="request">更新评论请求</param>
        /// <returns>更新后的评论</returns>
        [HttpPut("{taskId}/comments/{commentId}")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<CommentDto>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<CommentDto>))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponse<CommentDto>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ApiResponse<CommentDto>))]
        public async Task<IActionResult> UpdateTaskComment(long taskId, long commentId, [FromBody] UpdateCommentRequestDto request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ApiResponseFactory.CreateFail<CommentDto>("请求数据无效"));
                }

                var currentUserId = GetCurrentUserId();
                var command = new UpdateCommentCommand(commentId, taskId, request.Content ?? string.Empty, currentUserId,
                    request.MentionedUserIds, request.IsPinned);

                var result = await _mediator.Send(command);

                if (!result.Success)
                {
                    return result.Error.Contains("不存在") ? NotFound(result) : BadRequest(result);
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新任务评论时发生错误: TaskId={TaskId}, CommentId={CommentId}", taskId, commentId);
                return StatusCode(StatusCodes.Status500InternalServerError,
                    ApiResponseFactory.CreateFail<CommentDto>("更新任务评论时发生服务器错误: " + ex.Message));
            }
        }

        /// <summary>
        /// 置顶/取消置顶任务评论
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <param name="commentId">评论ID</param>
        /// <param name="request">置顶请求</param>
        /// <returns>更新后的评论</returns>
        [HttpPatch("{taskId}/comments/{commentId}/pin")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<CommentDto>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<CommentDto>))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponse<CommentDto>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ApiResponse<CommentDto>))]
        public async Task<IActionResult> PinTaskComment(long taskId, long commentId, [FromBody] PinCommentRequestDto request)
        {
            try
            {
                var currentUserId = GetCurrentUserId();
                if (currentUserId == 0)
                {
                    return Unauthorized(ApiResponse<CommentDto>.CreateFail("用户未认证"));
                }

                var command = new PinCommentCommand(commentId, taskId, request.IsPinned, currentUserId);
                var result = await _mediator.Send(command);

                if (result.Success)
                {
                    return Ok(result);
                }
                else
                {
                    return BadRequest(result);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "置顶任务评论时发生错误，任务ID: {TaskId}, 评论ID: {CommentId}", taskId, commentId);
                return StatusCode(StatusCodes.Status500InternalServerError,
                    ApiResponse<CommentDto>.CreateFail("置顶评论时发生内部错误"));
            }
        }

        /// <summary>
        /// 删除任务评论
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <param name="commentId">评论ID</param>
        /// <returns>删除结果</returns>
        [HttpDelete("{taskId}/comments/{commentId}")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<bool>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<bool>))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponse<bool>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ApiResponse<bool>))]
        public async Task<IActionResult> DeleteTaskComment(long taskId, long commentId)
        {
            try
            {
                var currentUserId = GetCurrentUserId();
                if (currentUserId == 0)
                {
                    return Unauthorized(ApiResponse<bool>.CreateFail("用户未认证"));
                }

                var command = new DeleteCommentCommand(commentId, taskId, currentUserId);
                var result = await _mediator.Send(command);

                if (result.Success)
                {
                    return Ok(result);
                }
                else
                {
                    return BadRequest(result);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除任务评论时发生错误，任务ID: {TaskId}, 评论ID: {CommentId}", taskId, commentId);
                return StatusCode(StatusCodes.Status500InternalServerError,
                    ApiResponse<bool>.CreateFail("删除评论时发生内部错误"));
            }
        }

        /// <summary>
        /// 回复任务评论
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <param name="commentId">父评论ID</param>
        /// <param name="request">回复评论请求</param>
        /// <returns>回复的评论</returns>
        [HttpPost("{taskId}/comments/{commentId}/reply")]
        [ProducesResponseType(StatusCodes.Status201Created, Type = typeof(ApiResponse<CommentDto>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<CommentDto>))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponse<CommentDto>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ApiResponse<CommentDto>))]
        public async Task<IActionResult> ReplyTaskComment(long taskId, long commentId, [FromBody] ReplyCommentRequestDto request)
        {
            try
            {
                var currentUserId = GetCurrentUserId();
                if (currentUserId == 0)
                {
                    return Unauthorized(ApiResponse<CommentDto>.CreateFail("用户未认证"));
                }

                var command = new ReplyCommentCommand(taskId, commentId, request.Content, request.MentionedUserIds, currentUserId);
                var result = await _mediator.Send(command);

                if (result.Success)
                {
                    return CreatedAtAction(nameof(GetTaskComments), new { taskId }, result);
                }
                else
                {
                    return result.Error.Contains("不存在") ? NotFound(result) : BadRequest(result);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "回复任务评论时发生错误，任务ID: {TaskId}, 父评论ID: {CommentId}", taskId, commentId);
                return StatusCode(StatusCodes.Status500InternalServerError,
                    ApiResponse<CommentDto>.CreateFail("回复评论时发生内部错误"));
            }
        }

        /// <summary>
        /// 搜索评论
        /// </summary>
        /// <param name="request">搜索评论请求</param>
        /// <returns>分页评论结果</returns>
        [HttpPost("comments/search")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<PagedResultType>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<PagedResultType>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ApiResponse<PagedResultType>))]
        public async Task<IActionResult> SearchComments([FromBody] SearchCommentsRequestDto request)
        {
            try
            {
                var currentUserId = GetCurrentUserId();
                if (currentUserId == 0)
                {
                    return Unauthorized(ApiResponse<PagedResultType>.CreateFail("用户未认证"));
                }

                var query = new SearchCommentsQuery
                {
                    TaskId = request.TaskId,
                    SearchKeyword = request.SearchKeyword,
                    UserId = request.UserId,
                    StartDate = request.StartDate,
                    EndDate = request.EndDate,
                    IsPinned = request.IsPinned,
                    PageNumber = request.PageNumber,
                    PageSize = request.PageSize,
                    SortBy = request.SortBy,
                    SortDirection = request.SortDirection,
                    CurrentUserId = currentUserId,
                    TopLevelOnly = request.TopLevelOnly,
                    IsEdited = request.IsEdited,
                    IncludeDeleted = request.IncludeDeleted,
                    MinContentLength = request.MinContentLength,
                    MaxContentLength = request.MaxContentLength,
                    HasMentions = request.HasMentions
                };

                var result = await _mediator.Send(query);

                if (result.Success)
                {
                    return Ok(result);
                }
                else
                {
                    return BadRequest(result);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "搜索评论时发生错误");
                return StatusCode(StatusCodes.Status500InternalServerError,
                    ApiResponse<PagedResultType>.CreateFail("搜索评论时发生内部错误"));
            }
        }

        /// <summary>
        /// 获取任务附件列表
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <returns>附件列表</returns>
        [HttpGet("{taskId}/attachments")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<List<AttachmentDto>>))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponse<List<AttachmentDto>>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ApiResponse<List<AttachmentDto>>))]
        public async Task<IActionResult> GetTaskAttachments(long taskId)
        {
            try
            {
                var result = await _taskService.GetTaskAttachmentsAsync(taskId);
                if (!result.Success)
                {
                    return NotFound(result);
                }
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取任务附件列表时发生错误: TaskId={TaskId}", taskId);
                return StatusCode(StatusCodes.Status500InternalServerError, 
                    ApiResponseFactory.CreateFail<List<AttachmentDto>>("获取任务附件列表时发生服务器错误: " + ex.Message));
            }
        }

        /// <summary>
        /// 上传任务附件
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <param name="file">文件</param>
        /// <param name="description">描述</param>
        /// <returns>上传的附件</returns>
        [HttpPost("{taskId}/attachments")]
        [ProducesResponseType(StatusCodes.Status201Created, Type = typeof(ApiResponse<AttachmentDto>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<AttachmentDto>))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponse<AttachmentDto>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ApiResponse<AttachmentDto>))]
        public async Task<IActionResult> UploadTaskAttachment(long taskId, IFormFile file, [FromQuery] string description = null)
        {
            try
            {
                if (file == null || file.Length == 0)
                {
                    return BadRequest(ApiResponseFactory.CreateFail<AttachmentDto>("未提供有效的文件"));
                }

                using var stream = new MemoryStream();
                await file.CopyToAsync(stream);

                var currentUserId = GetCurrentUserId();
                var result = await _taskService.AddAttachmentAsync(taskId, file.FileName, stream.ToArray(), file.ContentType, description, currentUserId);
                
                if (!result.Success)
                {
                    return result.Error.Contains("未找到") ? NotFound(result) : BadRequest(result);
                }

                return CreatedAtAction(nameof(GetTaskAttachments), new { taskId }, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "上传任务附件时发生错误: TaskId={TaskId}", taskId);
                return StatusCode(StatusCodes.Status500InternalServerError, 
                    ApiResponseFactory.CreateFail<AttachmentDto>("上传任务附件时发生服务器错误: " + ex.Message));
            }
        }

        /// <summary>
        /// 获取任务历史记录
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <returns>历史记录列表</returns>
        [HttpGet("{taskId:long}/history")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<List<TaskHistoryDto>>))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponse<List<TaskHistoryDto>>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ApiResponse<List<TaskHistoryDto>>))]
        public async Task<IActionResult> GetTaskHistory(long taskId)
        {
            try
            {
                _logger.LogInformation("获取任务 {TaskId} 的历史记录", taskId);
                var result = await _taskService.GetTaskHistoryAsync(taskId);
                if (!result.Success)
                {
                    _logger.LogWarning("任务 {TaskId} 的历史记录获取失败: {Error}", taskId, result.Error);
                    return NotFound(result);
                }
                _logger.LogInformation("成功获取任务 {TaskId} 的 {Count} 条历史记录", taskId, result.Data?.Count ?? 0);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取任务历史记录时发生错误: TaskId={TaskId}", taskId);
                return StatusCode(StatusCodes.Status500InternalServerError, 
                    ApiResponseFactory.CreateFail<List<TaskHistoryDto>>("获取任务历史记录时发生服务器错误: " + ex.Message));
            }
        }

        /// <summary>
        /// 记录任务查看
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <returns>记录结果</returns>
        [HttpPost("{taskId:long}/view")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<object>))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponse<object>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ApiResponse<object>))]
        public async Task<IActionResult> RecordTaskView(long taskId)
        {
            try
            {
                // 获取当前用户ID
                var currentUserId = GetCurrentUserId();
                var userId = (long)currentUserId;

                // 检查任务是否存在
                var taskExists = await _taskService.TaskExistsAsync(taskId);
                if (!taskExists)
                {
                    _logger.LogWarning("任务 {TaskId} 不存在", taskId);
                    return NotFound(ApiResponse<object>.CreateFail("任务不存在"));
                }

                // TODO: 实现真实的任务查看记录功能
                // 目前返回成功响应
                _logger.LogInformation("用户 {UserId} 查看了任务 {TaskId}", userId, taskId);
                return Ok(ApiResponse<object>.CreateSuccess(new { viewed = true }, "记录任务查看成功"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "记录任务查看时发生错误: TaskId={TaskId}", taskId);
                return Ok(ApiResponse<object>.CreateFail("记录任务查看失败"));
            }
        }

        /// <summary>
        /// 领取任务 (游戏化功能)
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <param name="claimData">领取数据</param>
        /// <returns>领取结果</returns>
        [HttpPost("{taskId:long}/claim")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<object>))]
        [ProducesResponseType(StatusCodes.Status404NotFound, Type = typeof(ApiResponse<object>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<object>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ApiResponse<object>))]
        public async Task<IActionResult> ClaimTask(long taskId, [FromBody] ClaimTaskRequestDto claimData)
        {
            try
            {
                // 获取当前用户ID
                var currentUserId = GetCurrentUserId();

                // 使用TaskClaimService进行任务领取
                var claimRequest = new CreateTaskClaimDto
                {
                    TaskId = taskId,
                    Notes = claimData?.Notes ?? $"用户主动领取任务"
                };

                var claimResult = await _taskClaimService.ClaimTaskAsync(claimRequest, currentUserId);

                if (!claimResult.Success)
                {
                    _logger.LogWarning("用户 {UserId} 领取任务 {TaskId} 失败: {Message}", currentUserId, taskId, claimResult.Message);
                    return Ok(ApiResponse<object>.CreateFail(claimResult.Message));
                }

                // 记录任务历史
                await _taskService.AddTaskHistoryAsync(taskId, "任务领取",
                    claimData?.Notes ?? $"用户主动领取任务", currentUserId);

                // 触发任务领取奖励
                try
                {
                    await _universalGamificationService.TriggerBehaviorRewardAsync(
                        currentUserId,
                        BehaviorCodes.TASK_CLAIMED,
                        taskId,
                        description: "领取任务"
                    );
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "触发任务领取奖励失败: TaskId={TaskId}", taskId);
                }

                _logger.LogInformation("用户 {UserId} 成功领取任务 {TaskId}", currentUserId, taskId);
                return Ok(ApiResponse<object>.CreateSuccess(new {
                    claimed = true,
                    claimId = claimResult.Data.ClaimId,
                    assigneeUserId = currentUserId,
                    status = "InProgress"
                }, "任务领取成功"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "领取任务时发生错误: TaskId={TaskId}", taskId);
                return Ok(ApiResponse<object>.CreateFail("领取任务失败"));
            }
        }

        /// <summary>
        /// 获取今日已查看任务数量
        /// </summary>
        /// <returns>今日已查看任务数量</returns>
        [HttpGet("viewed-today")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<object>))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ApiResponse<object>))]
        public async Task<IActionResult> GetTodayViewedCount()
        {
            try
            {
                // 获取当前用户ID
                var currentUserId = GetCurrentUserId();
                var userId = (long)currentUserId;

                // TODO: 实现真实的今日查看任务数量统计
                // 目前返回模拟数据
                var count = 0;

                _logger.LogInformation("获取用户 {UserId} 今日已查看任务数量: {Count}", userId, count);
                return Ok(ApiResponse<object>.CreateSuccess(new { count = count }));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取今日已查看任务数量时发生错误");
                return Ok(ApiResponse<object>.CreateFail("获取今日已查看任务数量失败"));
            }
        }
    }
}