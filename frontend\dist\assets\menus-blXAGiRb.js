import{_ as e,r as t,z as s,b as i,d as n,e as a,w as o,f as l,aG as d,aE as r,aD as p,a as c,m,o as u,p as y,l as h,k as f,ae as w,bM as _,b4 as b,t as k,a6 as v,$ as g,b1 as x,aJ as C,E as z,a9 as T}from"./index-CG5lHOPO.js";const $={class:"menu-management-container"},M={class:"page-header"},U={class:"page-actions"},A={key:0},B={key:1},E={key:2},R={key:1},S=e({__name:"menus",setup(e){const S=t(!1),j=t(null),D=t([]);s((()=>{F()}));const F=()=>{S.value=!0,setTimeout((()=>{D.value=[{id:1,title:"仪表盘",icon:"Odometer",path:"/main/dashboard",component:"dashboard/index",type:"menu",permission:"dashboard:view",sort:1,hidden:!1},{id:2,title:"资产管理",icon:"Monitor",path:"/main/assets",component:"assets/index",type:"directory",permission:"assets",sort:2,hidden:!1,children:[{id:21,title:"资产列表",icon:"List",path:"/main/assets/list",component:"assets/list",type:"menu",permission:"assets:list",sort:1,hidden:!1,children:[{id:211,title:"查看",icon:"",path:"",component:"",type:"button",permission:"assets:list:view",sort:1,hidden:!1},{id:212,title:"新增",icon:"",path:"",component:"",type:"button",permission:"assets:list:add",sort:2,hidden:!1},{id:213,title:"编辑",icon:"",path:"",component:"",type:"button",permission:"assets:list:edit",sort:3,hidden:!1},{id:214,title:"删除",icon:"",path:"",component:"",type:"button",permission:"assets:list:delete",sort:4,hidden:!1}]},{id:22,title:"资产类型",icon:"SetUp",path:"/main/assets/types",component:"assets/types",type:"menu",permission:"assets:types",sort:2,hidden:!1}]},{id:3,title:"系统管理",icon:"Setting",path:"/main/system",component:"system/index",type:"directory",permission:"system",sort:10,hidden:!1,children:[{id:31,title:"用户管理",icon:"User",path:"/main/system/users",component:"system/users",type:"menu",permission:"system:users",sort:1,hidden:!1},{id:32,title:"角色管理",icon:"UserFilled",path:"/main/system/roles",component:"system/roles",type:"menu",permission:"system:roles",sort:2,hidden:!1},{id:33,title:"菜单管理",icon:"Menu",path:"/main/system/menus",component:"system/menus",type:"menu",permission:"system:menus",sort:3,hidden:!1}]}],S.value=!1}),500)},G=()=>{j.value.expandAllRows()},I=()=>{j.value.collapseAllRows()},J=()=>{z.info("打开新建菜单对话框")};return(e,t)=>{const s=c("el-button"),L=c("el-icon"),O=c("el-table-column"),q=c("el-tag"),H=c("el-table"),K=c("el-card"),N=m("loading");return u(),i("div",$,[n("div",M,[t[3]||(t[3]=n("h2",{class:"page-title"},"菜单管理",-1)),n("div",U,[a(s,{type:"primary",onClick:J,icon:l(d)},{default:o((()=>t[0]||(t[0]=[y(" 新建菜单 ")]))),_:1},8,["icon"]),a(s,{type:"success",onClick:G,icon:l(r)},{default:o((()=>t[1]||(t[1]=[y(" 展开所有 ")]))),_:1},8,["icon"]),a(s,{type:"info",onClick:I,icon:l(p)},{default:o((()=>t[2]||(t[2]=[y(" 折叠所有 ")]))),_:1},8,["icon"])])]),a(K,{class:"data-card"},{default:o((()=>[h((u(),f(H,{ref_key:"menuTable",ref:j,data:D.value,"row-key":"id",border:"","default-expand-all":"","tree-props":{children:"children",hasChildren:"hasChildren"}},{default:o((()=>[a(O,{prop:"title",label:"菜单名称","min-width":"180"},{default:o((e=>["menu"===e.row.type?(u(),i("span",A,[a(L,null,{default:o((()=>[a(l(w))])),_:1})])):"directory"===e.row.type?(u(),i("span",B,[a(L,null,{default:o((()=>[a(l(_))])),_:1})])):(u(),i("span",E,[a(L,null,{default:o((()=>[a(l(b))])),_:1})])),y(" "+k(e.row.title),1)])),_:1}),a(O,{prop:"icon",label:"图标",width:"100"},{default:o((e=>[e.row.icon?(u(),f(L,{key:0},{default:o((()=>[(u(),f(v(e.row.icon)))])),_:2},1024)):(u(),i("span",R,"-"))])),_:1}),a(O,{prop:"path",label:"路由路径","min-width":"150"}),a(O,{prop:"type",label:"类型",width:"100"},{default:o((e=>["directory"===e.row.type?(u(),f(q,{key:0,type:"warning"},{default:o((()=>t[4]||(t[4]=[y("目录")]))),_:1})):"menu"===e.row.type?(u(),f(q,{key:1,type:"success"},{default:o((()=>t[5]||(t[5]=[y("菜单")]))),_:1})):"button"===e.row.type?(u(),f(q,{key:2,type:"info"},{default:o((()=>t[6]||(t[6]=[y("按钮")]))),_:1})):g("",!0)])),_:1}),a(O,{prop:"permission",label:"权限标识","min-width":"150"}),a(O,{prop:"sort",label:"排序",width:"80",align:"center"}),a(O,{prop:"hidden",label:"可见",width:"80",align:"center"},{default:o((e=>[e.row.hidden?(u(),f(q,{key:1,type:"info"},{default:o((()=>t[8]||(t[8]=[y("隐藏")]))),_:1})):(u(),f(q,{key:0,type:"success"},{default:o((()=>t[7]||(t[7]=[y("显示")]))),_:1}))])),_:1}),a(O,{label:"操作",width:"230",fixed:"right"},{default:o((e=>["button"!==e.row.type?(u(),f(s,{key:0,type:"text",size:"small",onClick:t=>{return s=e.row,void z.info(`为菜单"${s.title}"添加子菜单`);var s},icon:l(d)},{default:o((()=>t[9]||(t[9]=[y(" 添加 ")]))),_:2},1032,["onClick","icon"])):g("",!0),a(s,{type:"text",size:"small",onClick:t=>{return s=e.row,void z.info(`编辑菜单：${s.title}`);var s},icon:l(x)},{default:o((()=>t[10]||(t[10]=[y(" 编辑 ")]))),_:2},1032,["onClick","icon"]),a(s,{type:"text",size:"small",onClick:t=>{var s;(s=e.row).children&&s.children.length>0?z.warning("该菜单下有子菜单，请先删除子菜单"):T.confirm(`确定要删除菜单"${s.title}"吗？`,"删除菜单",{confirmButtonText:"确定",cancelButtonText:"取消",type:"error"}).then((()=>{z.success("菜单已删除"),F()})).catch((()=>{}))},icon:l(C)},{default:o((()=>t[11]||(t[11]=[y(" 删除 ")]))),_:2},1032,["onClick","icon"])])),_:1})])),_:1},8,["data"])),[[N,S.value]])])),_:1})])}}},[["__scopeId","data-v-7a4d8442"]]);export{S as default};
