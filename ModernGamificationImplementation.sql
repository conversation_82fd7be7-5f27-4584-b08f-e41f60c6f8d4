-- =====================================================
-- 现代化游戏化系统数据库升级脚本
-- 版本: v2.0 (混合架构)
-- 设计理念: 保留游戏化体验 + 增加企业级统计分析
-- =====================================================

-- 1. 保留并扩展现有等级系统
CREATE TABLE IF NOT EXISTS user_levels (
    level INT PRIMARY KEY COMMENT '等级',
    name VARCHAR(50) NOT NULL COMMENT '等级名称',
    required_xp INT NOT NULL COMMENT '所需经验值',
    reward_coins INT NOT NULL DEFAULT 0 COMMENT '金币奖励',
    reward_diamonds INT NOT NULL DEFAULT 0 COMMENT '钻石奖励',
    unlock_features JSON COMMENT '解锁特权',
    icon_url VARCHAR(255) COMMENT '等级图标',
    color VARCHAR(7) COMMENT '等级颜色',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB COMMENT='用户等级配置表';

-- 2. 保留并扩展道具系统
CREATE TABLE IF NOT EXISTS gamification_items (
    item_id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL COMMENT '道具名称',
    code VARCHAR(50) UNIQUE NOT NULL COMMENT '道具代码',
    type VARCHAR(50) NOT NULL COMMENT '道具类型',
    rarity VARCHAR(20) DEFAULT 'Common' COMMENT '稀有度：Common, Rare, Epic, Legendary',
    effect TEXT COMMENT '道具效果描述',
    effect_duration INT COMMENT '效果持续时间(分钟)',
    drop_rate DECIMAL(5,4) DEFAULT 0.01 COMMENT '掉落概率',
    icon_url VARCHAR(255) COMMENT '道具图标',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_rarity (rarity),
    INDEX idx_type (type)
) ENGINE=InnoDB COMMENT='道具配置表';

-- 3. 用户道具背包表
CREATE TABLE IF NOT EXISTS user_items (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL COMMENT '用户ID',
    item_id BIGINT NOT NULL COMMENT '道具ID',
    quantity INT DEFAULT 1 COMMENT '数量',
    obtained_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '获得时间',
    obtained_source VARCHAR(50) COMMENT '获得来源',
    INDEX idx_user_id (user_id),
    INDEX idx_item_id (item_id),
    FOREIGN KEY (item_id) REFERENCES gamification_items(item_id)
) ENGINE=InnoDB COMMENT='用户道具背包表';

-- 4. 新增：工作汇总统计表（企业级统计分析）
CREATE TABLE IF NOT EXISTS user_work_summary (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    user_name VARCHAR(50) NOT NULL,
    department_name VARCHAR(50),
    period_type ENUM('daily', 'weekly', 'monthly') NOT NULL,
    period_date DATE NOT NULL COMMENT '周期起始日期',
    
    -- 📋 任务模块统计
    tasks_created INT DEFAULT 0 COMMENT '新建任务数',
    tasks_claimed INT DEFAULT 0 COMMENT '领取任务数',
    tasks_completed INT DEFAULT 0 COMMENT '完成任务数',
    tasks_commented INT DEFAULT 0 COMMENT '评论任务数',
    
    -- 🏢 资产模块统计
    assets_created INT DEFAULT 0 COMMENT '新建资产数',
    assets_updated INT DEFAULT 0 COMMENT '更新资产数',
    assets_deleted INT DEFAULT 0 COMMENT '删除资产数',
    
    -- 🔧 故障模块统计
    faults_reported INT DEFAULT 0 COMMENT '登记故障数',
    faults_repaired INT DEFAULT 0 COMMENT '维修故障数',
    
    -- 🛒 采购模块统计
    procurements_created INT DEFAULT 0 COMMENT '新建采购单数',
    procurements_updated INT DEFAULT 0 COMMENT '更新采购进度数',
    
    -- 📦 备件模块统计
    parts_in INT DEFAULT 0 COMMENT '备件入库数',
    parts_out INT DEFAULT 0 COMMENT '备件出库数',
    parts_added INT DEFAULT 0 COMMENT '新增备件数',
    
    -- 🎮 游戏化数据汇总
    total_points_earned INT DEFAULT 0 COMMENT '总积分',
    total_coins_earned INT DEFAULT 0 COMMENT '总金币',
    total_diamonds_earned INT DEFAULT 0 COMMENT '总钻石',
    total_xp_earned INT DEFAULT 0 COMMENT '总经验',
    
    -- 📈 排名信息
    points_rank INT DEFAULT 0 COMMENT '积分排名',
    coins_rank INT DEFAULT 0 COMMENT '金币排名',
    diamonds_rank INT DEFAULT 0 COMMENT '钻石排名',
    productivity_rank INT DEFAULT 0 COMMENT '生产力排名',
    
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_user_period (user_id, period_type, period_date),
    INDEX idx_period (period_type, period_date),
    INDEX idx_user_period (user_id, period_type),
    INDEX idx_points_rank (points_rank),
    INDEX idx_productivity_rank (productivity_rank)
) ENGINE=InnoDB COMMENT='用户工作汇总统计表';

-- 5. 游戏化规则配置表（灵活配置）
CREATE TABLE IF NOT EXISTS gamification_behavior_rules (
    behavior_code VARCHAR(50) PRIMARY KEY,
    behavior_name VARCHAR(100) NOT NULL,
    module_name VARCHAR(50) NOT NULL,
    points INT DEFAULT 0 COMMENT '基础积分',
    coins INT DEFAULT 0 COMMENT '基础金币',
    xp INT DEFAULT 0 COMMENT '基础经验',
    diamonds INT DEFAULT 0 COMMENT '基础钻石',
    item_drop_chance DECIMAL(5,4) DEFAULT 0.0000 COMMENT '道具掉落概率',
    is_active BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB COMMENT='游戏化行为规则配置表';

-- 6. 扩展现有gamification_userstats表
ALTER TABLE gamification_userstats 
ADD COLUMN IF NOT EXISTS level_progress DECIMAL(5,2) DEFAULT 0.00 COMMENT '等级进度百分比',
ADD COLUMN IF NOT EXISTS total_items_obtained INT DEFAULT 0 COMMENT '总获得道具数',
ADD COLUMN IF NOT EXISTS rare_items_count INT DEFAULT 0 COMMENT '稀有道具数量',
ADD COLUMN IF NOT EXISTS epic_items_count INT DEFAULT 0 COMMENT '史诗道具数量',
ADD COLUMN IF NOT EXISTS legendary_items_count INT DEFAULT 0 COMMENT '传说道具数量',
ADD COLUMN IF NOT EXISTS last_activity_date DATE COMMENT '最后活跃日期',
ADD COLUMN IF NOT EXISTS consecutive_days INT DEFAULT 0 COMMENT '连续活跃天数';

-- 7. 每日限制表（防刷机制）
CREATE TABLE IF NOT EXISTS daily_limits (
    user_id INT NOT NULL COMMENT '用户ID',
    limit_date DATE NOT NULL COMMENT '限制日期',
    points_earned INT DEFAULT 0 COMMENT '已获得积分',
    max_points INT DEFAULT 200 COMMENT '最大积分',
    items_obtained INT DEFAULT 0 COMMENT '已获得道具数',
    max_items INT DEFAULT 5 COMMENT '最大道具数',
    tasks_completed INT DEFAULT 0 COMMENT '已完成任务数',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (user_id, limit_date)
) ENGINE=InnoDB COMMENT='每日限制表';

-- =====================================================
-- 初始化配置数据
-- =====================================================

-- 等级配置数据
INSERT INTO user_levels (level, name, required_xp, reward_coins, reward_diamonds, unlock_features, color) VALUES
(1, '新手上路', 0, 50, 0, '["basic_dashboard"]', '#8BC34A'),
(2, '初级用户', 100, 75, 1, '["task_templates"]', '#4CAF50'),
(3, '熟练用户', 250, 100, 2, '["advanced_search"]', '#2196F3'),
(4, '经验用户', 500, 150, 3, '["bulk_operations"]', '#03A9F4'),
(5, '资产专家', 1000, 200, 5, '["advanced_reports", "custom_views"]', '#00BCD4'),
(6, '维修能手', 1800, 300, 8, '["priority_support"]', '#009688'),
(7, '系统达人', 3000, 400, 12, '["system_config"]', '#FF9800'),
(8, '管理大师', 5000, 600, 20, '["admin_panel"]', '#FF5722'),
(9, '资深专家', 8000, 800, 30, '["data_export"]', '#9C27B0'),
(10, '传奇用户', 12000, 1200, 50, '["golden_badge", "vip_support"]', '#E91E63'),
(11, '超级管理员', 18000, 1500, 75, '["full_access"]', '#F44336'),
(12, '系统之神', 25000, 2000, 100, '["god_mode"]', '#FFD700')
ON DUPLICATE KEY UPDATE
name = VALUES(name),
required_xp = VALUES(required_xp),
reward_coins = VALUES(reward_coins),
reward_diamonds = VALUES(reward_diamonds),
unlock_features = VALUES(unlock_features),
color = VALUES(color);

-- 道具配置数据
INSERT INTO gamification_items (name, code, type, rarity, effect, effect_duration, drop_rate, icon_url) VALUES
-- 普通道具 (60%概率)
('效率提升卡', 'EFFICIENCY_BOOST', 'buff', 'Common', '任务完成速度提升10%', 60, 0.30, '/icons/efficiency.png'),
('积分加成卡', 'POINT_BONUS', 'buff', 'Common', '获得积分增加20%', 120, 0.20, '/icons/points.png'),
('经验药水', 'XP_POTION', 'consumable', 'Common', '立即获得50经验值', 0, 0.10, '/icons/xp_potion.png'),

-- 稀有道具 (25%概率)
('双倍积分卡', 'DOUBLE_POINTS', 'buff', 'Rare', '2小时内获得双倍积分', 120, 0.15, '/icons/double_points.png'),
('任务透视镜', 'TASK_VISION', 'tool', 'Rare', '查看所有任务详细信息', 180, 0.08, '/icons/task_vision.png'),
('金币宝箱', 'COIN_CHEST', 'consumable', 'Rare', '立即获得100-300金币', 0, 0.02, '/icons/coin_chest.png'),

-- 史诗道具 (10%概率)
('时空加速器', 'TIME_WARP', 'tool', 'Epic', '立即完成一个进行中任务', 0, 0.05, '/icons/time_warp.png'),
('资产透视镜', 'ASSET_VISION', 'skill', 'Epic', '查看资产完整历史记录', 1440, 0.03, '/icons/asset_vision.png'),
('三倍经验卡', 'TRIPLE_XP', 'buff', 'Epic', '3小时内获得三倍经验', 180, 0.02, '/icons/triple_xp.png'),

-- 传说道具 (5%概率)
('万能钥匙', 'MASTER_KEY', 'tool', 'Legendary', '解锁所有高级功能24小时', 1440, 0.003, '/icons/master_key.png'),
('时间回溯器', 'TIME_REWIND', 'tool', 'Legendary', '撤销最近一次操作', 0, 0.001, '/icons/time_rewind.png'),
('钻石宝库', 'DIAMOND_VAULT', 'consumable', 'Legendary', '立即获得50-100钻石', 0, 0.001, '/icons/diamond_vault.png')
ON DUPLICATE KEY UPDATE
name = VALUES(name),
type = VALUES(type),
rarity = VALUES(rarity),
effect = VALUES(effect),
effect_duration = VALUES(effect_duration),
drop_rate = VALUES(drop_rate),
icon_url = VALUES(icon_url);

-- 行为规则配置数据
INSERT INTO gamification_behavior_rules (behavior_code, behavior_name, module_name, points, coins, xp, diamonds, item_drop_chance) VALUES
('TASK_CREATE', '新建任务', 'Task', 10, 5, 15, 0, 0.0100),
('TASK_CLAIM', '领取任务', 'Task', 5, 2, 8, 0, 0.0050),
('TASK_COMPLETE', '完成任务', 'Task', 20, 10, 30, 1, 0.0500),
('TASK_COMMENT', '评论任务', 'Task', 3, 1, 5, 0, 0.0010),
('ASSET_CREATE', '新建资产', 'Asset', 15, 8, 20, 0, 0.0200),
('ASSET_UPDATE', '更新资产', 'Asset', 8, 3, 12, 0, 0.0100),
('ASSET_DELETE', '删除资产', 'Asset', 5, 2, 8, 0, 0.0050),
('FAULT_REPORT', '登记故障', 'Fault', 12, 6, 18, 0, 0.0150),
('FAULT_REPAIR', '返厂维修', 'Fault', 10, 5, 15, 0, 0.0100),
('PROCUREMENT_CREATE', '新建采购单', 'Procurement', 18, 10, 25, 1, 0.0300),
('PROCUREMENT_UPDATE', '更新采购进度', 'Procurement', 8, 4, 12, 0, 0.0100),
('INVENTORY_IN', '备件入库', 'Inventory', 10, 5, 15, 0, 0.0150),
('INVENTORY_OUT', '备件出库', 'Inventory', 8, 3, 12, 0, 0.0100),
('INVENTORY_ADD', '新增备件', 'Inventory', 12, 6, 18, 0, 0.0200),
('LEVEL_UP', '等级提升', 'System', 0, 0, 0, 5, 0.1000),
('DAILY_LOGIN', '每日登录', 'System', 5, 2, 10, 0, 0.0050)
ON DUPLICATE KEY UPDATE
behavior_name = VALUES(behavior_name),
module_name = VALUES(module_name),
points = VALUES(points),
coins = VALUES(coins),
xp = VALUES(xp),
diamonds = VALUES(diamonds),
item_drop_chance = VALUES(item_drop_chance);

-- =====================================================
-- 创建现代化视图（替代复杂存储过程）
-- =====================================================

-- 实时排行榜视图
CREATE OR REPLACE VIEW v_realtime_leaderboard AS
SELECT 
    ROW_NUMBER() OVER (ORDER BY gus.PointsBalance DESC) as rank_no,
    u.id as user_id,
    u.name as user_name,
    d.name as department_name,
    gus.PointsBalance as total_points,
    gus.CoinsBalance as total_coins,
    gus.DiamondsBalance as total_diamonds,
    gus.CurrentLevel as current_level,
    ul.name as level_name,
    ul.color as level_color,
    gus.CompletedTasksCount as tasks_completed,
    gus.total_items_obtained,
    (gus.rare_items_count + gus.epic_items_count + gus.legendary_items_count) as special_items_count,
    gus.consecutive_days,
    gus.LastActivityTimestamp as last_activity
FROM gamification_userstats gus
JOIN users u ON gus.CoreUserId = u.id
LEFT JOIN user_levels ul ON gus.CurrentLevel = ul.level
LEFT JOIN departments d ON u.department_id = d.id
WHERE u.IsDeleted = 0
ORDER BY gus.PointsBalance DESC;

-- 用户成长轨迹视图
CREATE OR REPLACE VIEW v_user_growth_timeline AS
SELECT 
    gl.UserId as user_id,
    u.name as user_name,
    DATE(gl.CreatedAt) as activity_date,
    gl.ActionType as action_type,
    COUNT(*) as action_count,
    SUM(gl.PointsGained) as points_earned,
    SUM(gl.XpGained) as xp_earned,
    SUM(gl.CoinsGained) as coins_earned,
    SUM(gl.DiamondsGained) as diamonds_earned
FROM gamification_log gl
JOIN users u ON gl.UserId = u.id
WHERE gl.CreatedAt >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
    AND u.IsDeleted = 0
GROUP BY gl.UserId, DATE(gl.CreatedAt), gl.ActionType
ORDER BY activity_date DESC, user_id;

-- 部门统计视图
CREATE OR REPLACE VIEW v_department_statistics AS
SELECT 
    d.id as department_id,
    d.name as department_name,
    COUNT(DISTINCT u.id) as user_count,
    AVG(gus.PointsBalance) as avg_points,
    SUM(gus.PointsBalance) as total_points,
    AVG(gus.CurrentLevel) as avg_level,
    SUM(gus.CompletedTasksCount) as total_tasks_completed,
    SUM(gus.total_items_obtained) as total_items_obtained
FROM departments d
LEFT JOIN users u ON d.id = u.department_id AND u.IsDeleted = 0
LEFT JOIN gamification_userstats gus ON u.id = gus.CoreUserId
GROUP BY d.id, d.name
ORDER BY total_points DESC;

-- =====================================================
-- 更新现有用户数据
-- =====================================================

-- 更新现有用户的等级信息
UPDATE gamification_userstats gus
JOIN (
    SELECT 
        gus.CoreUserId,
        COALESCE(MAX(ul.level), 1) as calculated_level
    FROM gamification_userstats gus
    LEFT JOIN user_levels ul ON gus.CurrentXP >= ul.required_xp
    GROUP BY gus.CoreUserId
) calc ON gus.CoreUserId = calc.CoreUserId
SET gus.CurrentLevel = calc.calculated_level
WHERE gus.CurrentLevel IS NULL OR gus.CurrentLevel = 0;

-- 初始化每日限制数据
INSERT IGNORE INTO daily_limits (user_id, limit_date, points_earned, items_obtained, tasks_completed)
SELECT 
    gus.CoreUserId,
    CURDATE(),
    0,
    0,
    0
FROM gamification_userstats gus
JOIN users u ON gus.CoreUserId = u.id
WHERE u.IsDeleted = 0;

-- =====================================================
-- 创建索引优化查询性能
-- =====================================================

-- 游戏化日志表索引
CREATE INDEX IF NOT EXISTS idx_gamification_log_user_date ON gamification_log(UserId, CreatedAt);
CREATE INDEX IF NOT EXISTS idx_gamification_log_action_date ON gamification_log(ActionType, CreatedAt);

-- 用户统计表索引
CREATE INDEX IF NOT EXISTS idx_gamification_userstats_points ON gamification_userstats(PointsBalance DESC);
CREATE INDEX IF NOT EXISTS idx_gamification_userstats_level ON gamification_userstats(CurrentLevel DESC);
CREATE INDEX IF NOT EXISTS idx_gamification_userstats_activity ON gamification_userstats(LastActivityTimestamp);

-- 工作汇总表索引
CREATE INDEX IF NOT EXISTS idx_user_work_summary_composite ON user_work_summary(period_type, period_date, points_rank);

-- =====================================================
-- 验证脚本执行结果
-- =====================================================

-- 检查表创建情况
SELECT 
    'Tables Created' as status,
    COUNT(*) as count
FROM information_schema.tables 
WHERE table_schema = DATABASE() 
    AND table_name IN ('user_levels', 'gamification_items', 'user_items', 'user_work_summary', 'gamification_behavior_rules', 'daily_limits');

-- 检查数据初始化情况
SELECT 'Levels' as type, COUNT(*) as count FROM user_levels
UNION ALL
SELECT 'Items' as type, COUNT(*) as count FROM gamification_items
UNION ALL
SELECT 'Behavior Rules' as type, COUNT(*) as count FROM gamification_behavior_rules;

-- 检查视图创建情况
SELECT 
    'Views Created' as status,
    COUNT(*) as count
FROM information_schema.views 
WHERE table_schema = DATABASE() 
    AND table_name LIKE 'v_%';

-- =====================================================
-- 清理和优化
-- =====================================================

-- 清理可能的重复数据
DELETE t1 FROM user_work_summary t1
INNER JOIN user_work_summary t2
WHERE t1.id > t2.id
    AND t1.user_id = t2.user_id
    AND t1.period_type = t2.period_type
    AND t1.period_date = t2.period_date;

-- 优化表结构
OPTIMIZE TABLE gamification_userstats;
OPTIMIZE TABLE gamification_log;
OPTIMIZE TABLE user_work_summary;

SELECT '✅ 现代化游戏化系统升级完成！' as result;
