import{aV as e}from"./index-CG5lHOPO.js";const t="/v2/statistics",s={executeQuery:s=>e({url:`${t}/query`,method:"post",data:s}),getDimensions:()=>e({url:`${t}/dimensions`,method:"get"}),getMetrics:()=>e({url:`${t}/metrics`,method:"get"}),exportData:(s,a="excel")=>e({url:`${t}/export`,method:"post",data:{...s,format:a},responseType:"blob"}),getAnalysisTemplates:()=>e({url:`${t}/templates`,method:"get"}),saveAnalysisTemplate:s=>e({url:`${t}/templates`,method:"post",data:s}),getAssetSnapshots:t=>e({url:"/v2/assetsnapshots",method:"get",params:t}),getWorkSummary:(t={})=>e({url:"/v2/work-summary",method:"get",params:{periodType:t.periodType||"weekly",periodDate:t.periodDate,limit:t.limit||50}}),updateWorkSummary:(t={})=>e({url:"/v2/work-summary/update",method:"post",data:t})};export{s};
