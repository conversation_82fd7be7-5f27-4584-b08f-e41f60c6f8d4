// File: frontend/src/views/notifications/NotificationsListView.vue
// Description: 通知列表页面，用于显示所有通知

<template>
  <div class="notifications-page">
    <el-card class="notifications-card">
      <template #header>
        <div class="card-header">
          <h2>通知中心</h2>
          <div class="header-actions">
            <el-button 
              v-if="hasUnread" 
              type="primary" 
              @click="markAllAsRead"
            >
              全部标为已读
            </el-button>
          </div>
        </div>
      </template>

      <el-tabs v-model="activeTab" @tab-click="handleTabClick">
        <el-tab-pane label="全部" name="all">
          <div class="notification-list">
            <el-empty v-if="notifications.length === 0" description="暂无通知" />
            <div 
              v-for="notification in notifications" 
              :key="notification.id" 
              class="notification-item"
              :class="{ 'unread': !notification.read }"
              @click="handleNotificationClick(notification)"
            >
              <div class="notification-icon">
                <el-icon :size="20" :color="getIconColor(notification.type)">
                  <component :is="getNotificationIcon(notification.type)" />
                </el-icon>
              </div>
              <div class="notification-content">
                <div class="notification-title">{{ notification.title }}</div>
                <div class="notification-message">{{ notification.content || notification.message }}</div>
                <div class="notification-time">{{ formatTime(notification.timestamp || notification.creationTimestamp) }}</div>
              </div>
              <div class="notification-actions">
                <el-button 
                  type="text" 
                  size="small" 
                  @click.stop="markAsRead(notification.id)"
                  v-if="!notification.read"
                >
                  标为已读
                </el-button>
                <el-button 
                  type="text" 
                  size="small" 
                  @click.stop="deleteNotification(notification.id)"
                >
                  删除
                </el-button>
              </div>
            </div>
          </div>
          
          <div class="pagination-wrapper" v-if="notifications.length > 0">
            <el-pagination
              v-model:current-page="pagination.currentPage"
              v-model:page-size="pagination.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              :small="false"
              :disabled="loading"
              :background="true"
              layout="total, sizes, prev, pager, next, jumper"
              :total="total"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="任务" name="task">
          <div class="notification-list">
            <el-empty v-if="taskNotifications.length === 0" description="暂无任务通知" />
            <div 
              v-for="notification in taskNotifications" 
              :key="notification.id" 
              class="notification-item"
              :class="{ 'unread': !notification.read }"
              @click="handleNotificationClick(notification)"
            >
              <div class="notification-icon">
                <el-icon :size="20" :color="getIconColor(notification.type)">
                  <component :is="getNotificationIcon(notification.type)" />
                </el-icon>
              </div>
              <div class="notification-content">
                <div class="notification-title">{{ notification.title }}</div>
                <div class="notification-message">{{ notification.content || notification.message }}</div>
                <div class="notification-time">{{ formatTime(notification.timestamp || notification.creationTimestamp) }}</div>
              </div>
              <div class="notification-actions">
                <el-button 
                  type="text" 
                  size="small" 
                  @click.stop="markAsRead(notification.id)"
                  v-if="!notification.read"
                >
                  标为已读
                </el-button>
                <el-button 
                  type="text" 
                  size="small" 
                  @click.stop="deleteNotification(notification.id)"
                >
                  删除
                </el-button>
              </div>
            </div>
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="系统" name="system">
          <div class="notification-list">
            <el-empty v-if="systemNotifications.length === 0" description="暂无系统通知" />
            <div 
              v-for="notification in systemNotifications" 
              :key="notification.id" 
              class="notification-item"
              :class="{ 'unread': !notification.read }"
              @click="handleNotificationClick(notification)"
            >
              <div class="notification-icon">
                <el-icon :size="20" :color="getIconColor(notification.type)">
                  <component :is="getNotificationIcon(notification.type)" />
                </el-icon>
              </div>
              <div class="notification-content">
                <div class="notification-title">{{ notification.title }}</div>
                <div class="notification-message">{{ notification.content || notification.message }}</div>
                <div class="notification-time">{{ formatTime(notification.timestamp || notification.creationTimestamp) }}</div>
              </div>
              <div class="notification-actions">
                <el-button 
                  type="text" 
                  size="small" 
                  @click.stop="markAsRead(notification.id)"
                  v-if="!notification.read"
                >
                  标为已读
                </el-button>
                <el-button 
                  type="text" 
                  size="small" 
                  @click.stop="deleteNotification(notification.id)"
                >
                  删除
                </el-button>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useNotificationStore } from '@/stores/modules/notification'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Bell, Message, Warning, CircleCheck, Document, Select
} from '@element-plus/icons-vue'

const router = useRouter()
const notificationStore = useNotificationStore()
const activeTab = ref('all')
const loading = ref(false)
const total = ref(0)

// 分页
const pagination = ref({
  currentPage: 1,
  pageSize: 20
})

// 计算属性
const notifications = computed(() => notificationStore.notifications)
const hasUnread = computed(() => notificationStore.hasUnread)

// 过滤出任务相关通知
const taskNotifications = computed(() => {
  return notifications.value.filter(notification => {
    const taskTypes = ['TaskAssigned', 'TaskStatusChanged', 'TaskComment', 'TaskAttachmentAdded', 'TaskMention', 'TaskOverdue', 'TaskContentChanged']
    return taskTypes.includes(notification.type)
  })
})

// 过滤出系统通知
const systemNotifications = computed(() => {
  return notifications.value.filter(notification => {
    const taskTypes = ['TaskAssigned', 'TaskStatusChanged', 'TaskComment', 'TaskAttachmentAdded', 'TaskMention', 'TaskOverdue', 'TaskContentChanged']
    return !taskTypes.includes(notification.type)
  })
})

// 加载通知
const loadNotifications = async () => {
  console.log('loadNotifications 开始加载...')
  loading.value = true
  try {
    await notificationStore.fetchNotifications({
      page: pagination.value.currentPage,
      pageSize: pagination.value.pageSize,
      force: true
    })
    total.value = notificationStore.total
    console.log('通知加载完成，数量:', notificationStore.notifications.length)
    console.log('总数:', total.value)
  } catch (error) {
    console.error('加载通知失败', error)
    ElMessage.error('加载通知失败')
  } finally {
    loading.value = false
  }
}

// 处理标签页切换
const handleTabClick = () => {
  // 切换标签页时可以做一些额外操作
}

// 标记已读
const markAsRead = async (notificationId) => {
  try {
    await notificationStore.markAsRead(notificationId)
    ElMessage.success('已标记为已读')
  } catch (error) {
    console.error('标记已读失败', error)
    ElMessage.error('标记已读失败')
  }
}

// 全部标记已读
const markAllAsRead = async () => {
  try {
    await notificationStore.markAllAsRead()
    ElMessage.success('已全部标记为已读')
  } catch (error) {
    console.error('全部标记已读失败', error)
    ElMessage.error('全部标记已读失败')
  }
}

// 删除通知
const deleteNotification = async (notificationId) => {
  try {
    await ElMessageBox.confirm('确定要删除此通知吗？', '删除通知', {
      type: 'warning'
    })
    await notificationStore.deleteNotification(notificationId)
    ElMessage.success('删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除通知失败', error)
      ElMessage.error('删除通知失败')
    }
  }
}

// 通知点击处理
const handleNotificationClick = (notification) => {
  markAsRead(notification.id)
  
  // 根据通知类型跳转到不同页面
  if (notification.referenceType === 'Task' && notification.referenceId) {
    router.push(`/main/tasks/detail/${notification.referenceId}`)
  } else if (notification.resourceType === 'Task' && notification.resourceId) {
    router.push(`/main/tasks/detail/${notification.resourceId}`)
  } else if (notification.taskId) {
    router.push(`/main/tasks/detail/${notification.taskId}`)
  }
}

// 分页处理
const handleSizeChange = (size) => {
  pagination.value.pageSize = size
  pagination.value.currentPage = 1
  loadNotifications()
}

const handleCurrentChange = (page) => {
  pagination.value.currentPage = page
  loadNotifications()
}

// 格式化时间
const formatTime = (timestamp) => {
  if (!timestamp) return ''
  
  const date = new Date(timestamp)
  const now = new Date()
  const diff = now - date
  
  // 一天内显示"x小时前"或"x分钟前"
  if (diff < 24 * 60 * 60 * 1000) {
    if (diff < 60 * 60 * 1000) {
      const minutes = Math.floor(diff / (60 * 1000))
      return `${minutes} 分钟前`
    } else {
      const hours = Math.floor(diff / (60 * 60 * 1000))
      return `${hours} 小时前`
    }
  }
  
  // 一周内显示星期几
  if (diff < 7 * 24 * 60 * 60 * 1000) {
    const days = ['日', '一', '二', '三', '四', '五', '六']
    return `星期${days[date.getDay()]} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
  }
  
  // 其他情况显示完整日期
  return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
}

// 获取通知图标
const getNotificationIcon = (type) => {
  switch (type) {
    case 'TaskAssigned':
      return Document
    case 'TaskStatusChanged':
      return CircleCheck
    case 'TaskComment':
      return Message
    case 'TaskAttachmentAdded':
      return Document
    case 'TaskMention':
      return Bell
    case 'TaskOverdue':
      return Warning
    case 'TaskContentChanged':
      return Document
    case 'Test':
      return Bell
    default:
      return Bell
  }
}

// 获取图标颜色
const getIconColor = (type) => {
  switch (type) {
    case 'TaskAssigned':
    case 'TaskMention':
      return '#67c23a'
    case 'TaskOverdue':
      return '#f56c6c'
    case 'TaskStatusChanged':
    case 'TaskContentChanged':
      return '#e6a23c'
    case 'TaskComment':
    case 'TaskAttachmentAdded':
      return '#409eff'
    default:
      return '#909399'
  }
}

// 生命周期钩子
onMounted(() => {
  loadNotifications()
})
</script>

<style scoped>
.notifications-page {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
}

.notifications-card {
  border-radius: 8px;
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h2 {
  margin: 0;
  font-size: 18px;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.notification-list {
  min-height: 300px;
}

.notification-item {
  display: flex;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.3s;
}

.notification-item:hover {
  background-color: #f5f7fa;
}

.notification-item.unread {
  background-color: #ecf5ff;
}

.notification-icon {
  display: flex;
  align-items: flex-start;
  padding-top: 4px;
  margin-right: 16px;
}

.notification-content {
  flex: 1;
  min-width: 0;
}

.notification-title {
  font-weight: 600;
  margin-bottom: 4px;
  color: #303133;
}

.notification-message {
  color: #606266;
  margin-bottom: 4px;
  word-break: break-word;
}

.notification-time {
  font-size: 12px;
  color: #909399;
}

.notification-actions {
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: flex-end;
  justify-content: flex-start;
  padding-top: 4px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #e4e7ed;
}

.empty-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 0;
  color: #909399;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  color: #dcdfe6;
}
</style> 