#nullable enable

// File: Core/Abstractions/IFileStorageService.cs
// Description: Defines the contract for file storage operations.

using Microsoft.AspNetCore.Http;
using System.Threading.Tasks;

namespace ItAssetsSystem.Core.Abstractions
{
    /// <summary>
    /// Interface for handling file storage operations.
    /// </summary>
    public interface IFileStorageService
    {
        /// <summary>
        /// Saves an avatar image file for a specific user.
        /// </summary>
        /// <param name="file">The uploaded avatar file.</param>
        /// <param name="userId">The ID of the user whose avatar is being saved.</param>
        /// <returns>The relative URL path to the saved avatar image, or null if saving failed.</returns>
        Task<string?> SaveAvatarAsync(IFormFile file, string userId);

        /// <summary>
        /// Saves a generic file from a byte array to a specified sub-path.
        /// </summary>
        /// <param name="fileContent">The byte array content of the file.</param>
        /// <param name="fileName">The desired file name (with extension).</param>
        /// <param name="subPath">The sub-path within the storage root where the file should be saved (e.g., "tasks/attachments").</param>
        /// <returns>The relative URL path to the saved file, or null if saving failed.</returns>
        Task<string?> SaveFileAsync(byte[] fileContent, string fileName, string subPath);

        /// <summary>
        /// Deletes a file based on its relative path.
        /// </summary>
        /// <param name="relativePath">The relative path of the file to delete.</param>
        /// <returns>True if deletion was successful or file didn't exist, false otherwise.</returns>
        Task<bool> DeleteFileAsync(string? relativePath);
    }
} 