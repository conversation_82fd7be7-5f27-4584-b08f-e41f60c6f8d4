// IT资产管理系统 - 位置层级配置服务
// 文件路径: /Core/Location/LocationHierarchyService.cs
// 功能: 通过JSON配置文件定义和管理位置层级结构

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using ItAssetsSystem.Core.Helpers;
using ItAssetsSystem.Infrastructure.Data;
using ItAssetsSystem.Models.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using LocationEntity = ItAssetsSystem.Models.Entities.Location;

namespace ItAssetsSystem.Core.Location
{
    /// <summary>
    /// 位置层级结构配置类
    /// </summary>
    public class LocationHierarchyConfig
    {
        /// <summary>
        /// 位置层级列表
        /// </summary>
        public List<LocationLevel> Levels { get; set; } = new List<LocationLevel>();
        
        /// <summary>
        /// 根节点列表
        /// </summary>
        public List<LocationNode> RootNodes { get; set; } = new List<LocationNode>();
    }
    
    /// <summary>
    /// 位置层级定义
    /// </summary>
    public class LocationLevel
    {
        /// <summary>
        /// 层级代码
        /// </summary>
        public string Code { get; set; }
        
        /// <summary>
        /// 层级名称
        /// </summary>
        public string Name { get; set; }
        
        /// <summary>
        /// 层级描述
        /// </summary>
        public string Description { get; set; }
        
        /// <summary>
        /// 图标
        /// </summary>
        public string Icon { get; set; }
        
        /// <summary>
        /// 颜色
        /// </summary>
        public string Color { get; set; }
        
        /// <summary>
        /// 是否可以包含资产
        /// </summary>
        public bool CanContainAssets { get; set; } = true;
    }
    
    /// <summary>
    /// 位置节点定义
    /// </summary>
    public class LocationNode
    {
        /// <summary>
        /// 节点ID
        /// </summary>
        public string Id { get; set; }
        
        /// <summary>
        /// 节点代码
        /// </summary>
        public string Code { get; set; }
        
        /// <summary>
        /// 节点名称
        /// </summary>
        public string Name { get; set; }
        
        /// <summary>
        /// 层级代码
        /// </summary>
        public string LevelCode { get; set; }
        
        /// <summary>
        /// 描述
        /// </summary>
        public string Description { get; set; }
        
        /// <summary>
        /// 子节点
        /// </summary>
        public List<LocationNode> Children { get; set; } = new List<LocationNode>();
    }
    
    /// <summary>
    /// 位置层级服务
    /// </summary>
    public class LocationHierarchyService : ILocationHierarchyService
    {
        private readonly ILogger<LocationHierarchyService> _logger;
        private readonly IConfiguration _configuration;
        private readonly AppDbContext _dbContext;
        private readonly string _configFilePath;
        private readonly JsonSerializerOptions _jsonOptions;
        
        /// <summary>
        /// 构造函数
        /// </summary>
        public LocationHierarchyService(
            ILogger<LocationHierarchyService> logger,
            IConfiguration configuration,
            AppDbContext dbContext)
        {
            _logger = logger;
            _configuration = configuration;
            _dbContext = dbContext;
            
            // 获取位置层级配置文件路径
            string configPath = _configuration.GetValue<string>("Location:ConfigPath");
            if (string.IsNullOrEmpty(configPath))
            {
                configPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data", "Location");
            }
            
            // 确保目录存在
            FileHelper.EnsureDirectoryExists(configPath, _logger);
            
            // 配置文件路径
            _configFilePath = Path.Combine(configPath, "hierarchy.json");
            
            // 初始化JSON序列化选项
            _jsonOptions = new JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            };
        }
        
        /// <summary>
        /// 获取位置层级配置
        /// </summary>
        public async Task<LocationHierarchyConfig> GetHierarchyConfigAsync()
        {
            _logger.LogInformation("获取位置层级配置");
            
            try
            {
                if (File.Exists(_configFilePath))
                {
                    string json = await File.ReadAllTextAsync(_configFilePath);
                    return JsonSerializer.Deserialize<LocationHierarchyConfig>(json, _jsonOptions);
                }
                
                // 返回默认配置
                return CreateDefaultConfig();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取位置层级配置失败");
                return CreateDefaultConfig();
            }
        }
        
        /// <summary>
        /// 保存位置层级配置
        /// </summary>
        public async Task<bool> SaveHierarchyConfigAsync(LocationHierarchyConfig config)
        {
            if (config == null)
            {
                throw new ArgumentNullException(nameof(config));
            }
            
            _logger.LogInformation("保存位置层级配置");
            
            try
            {
                string json = JsonSerializer.Serialize(config, _jsonOptions);
                await File.WriteAllTextAsync(_configFilePath, json);
                
                _logger.LogInformation("位置层级配置已保存");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存位置层级配置失败");
                return false;
            }
        }
        
        /// <summary>
        /// 同步位置层级结构到数据库
        /// </summary>
        public async System.Threading.Tasks.Task<bool> SyncHierarchyToDbAsync()
        {
            _logger.LogInformation("同步位置层级结构到数据库");
            
            try
            {
                // 获取配置
                var config = await GetHierarchyConfigAsync();
                
                // 获取所有现有位置
                var existingLocations = await _dbContext.Locations.ToListAsync();
                
                // 创建位置类型映射（Code -> Name）
                var locationTypeMapping = config.Levels.ToDictionary(l => l.Code, l => l.Name);
                
                // 递归处理根节点
                foreach (var rootNode in config.RootNodes)
                {
                    await ProcessLocationNodeAsync(rootNode, null, existingLocations, locationTypeMapping);
                }
                
                // 保存更改
                await _dbContext.SaveChangesAsync();
                
                _logger.LogInformation("位置层级结构已同步到数据库");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "同步位置层级结构到数据库失败");
                return false;
            }
        }
        
        /// <summary>
        /// 从数据库重建层级结构
        /// </summary>
        public async Task<bool> RebuildHierarchyFromDbAsync()
        {
            _logger.LogInformation("从数据库重建位置层级结构");
            
            try
            {
                // 获取所有位置，按路径排序
                var allLocations = await _dbContext.Locations
                    .OrderBy(l => l.Id)
                    .ToListAsync();
                
                // 创建新配置
                var config = new LocationHierarchyConfig();
                
                // 提取唯一的层级类型（这里使用一些启发式方法来猜测类型）
                // 例如，根据位置代码的模式或路径深度等
                var levelTypes = new List<(string Code, string Name)>
                {
                    ("building", "建筑"),
                    ("floor", "楼层"),
                    ("room", "房间")
                };
                
                // 创建层级列表
                foreach (var level in levelTypes)
                {
                    config.Levels.Add(new LocationLevel
                    {
                        Code = level.Code,
                        Name = level.Name,
                        Description = $"{level.Name}层级",
                        CanContainAssets = true
                    });
                }
                
                // 获取根节点
                var rootLocations = allLocations
                    .Where(l => l.ParentId == null)
                    .ToList();
                
                // 递归构建节点树
                foreach (var rootLocation in rootLocations)
                {
                    config.RootNodes.Add(BuildLocationNodeFromEntity(rootLocation, allLocations));
                }
                
                // 保存配置
                await SaveHierarchyConfigAsync(config);
                
                _logger.LogInformation("位置层级结构已从数据库重建");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "从数据库重建位置层级结构失败");
                return false;
            }
        }
        
        /// <summary>
        /// 处理位置节点
        /// </summary>
        private async System.Threading.Tasks.Task ProcessLocationNodeAsync(
            LocationNode node, 
            LocationEntity parentEntity,
            List<LocationEntity> existingLocations,
            Dictionary<string, string> locationTypeMapping)
        {
            // 检查是否已存在
            var existingLocation = existingLocations
                .FirstOrDefault(l => l.Code == node.Code);
            
            LocationEntity locationEntity;
            
            if (existingLocation != null)
            {
                // 更新现有位置
                locationEntity = existingLocation;
                locationEntity.Name = node.Name;
                // 使用位置类型代码作为额外属性或备注，因为实体没有LocationTypeCode字段
                locationEntity.Description = node.Description;
                locationEntity.ParentId = parentEntity?.Id;
                
                // 更新位置实体
                _dbContext.Locations.Update(locationEntity);
            }
            else
            {
                // 创建新位置
                locationEntity = new LocationEntity
                {
                    Code = node.Code,
                    Name = node.Name,
                    Description = node.Description,
                    ParentId = parentEntity?.Id,
                    IsActive = true,
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now
                };
                
                await _dbContext.Locations.AddAsync(locationEntity);
            }
            
            // 递归处理子节点
            foreach (var childNode in node.Children)
            {
                await ProcessLocationNodeAsync(childNode, locationEntity, existingLocations, locationTypeMapping);
            }
        }
        
        /// <summary>
        /// 从实体构建位置节点
        /// </summary>
        private LocationNode BuildLocationNodeFromEntity(
            LocationEntity location,
            List<LocationEntity> allLocations)
        {
            // 根据位置深度推测层级类型
            string levelCode = "room"; // 默认为房间
            if (location.ParentId == null)
            {
                levelCode = "building"; // 根节点为建筑
            }
            else
            {
                var parent = allLocations.FirstOrDefault(l => l.Id == location.ParentId);
                if (parent != null && parent.ParentId == null)
                {
                    levelCode = "floor"; // 二级节点为楼层
                }
            }
            
            var node = new LocationNode
            {
                Id = location.Id.ToString(),
                Code = location.Code,
                Name = location.Name,
                LevelCode = levelCode,
                Description = location.Description
            };
            
            // 获取子节点
            var children = allLocations
                .Where(l => l.ParentId == location.Id)
                .ToList();
            
            // 递归处理子节点
            foreach (var child in children)
            {
                node.Children.Add(BuildLocationNodeFromEntity(child, allLocations));
            }
            
            return node;
        }
        
        /// <summary>
        /// 创建默认配置
        /// </summary>
        private LocationHierarchyConfig CreateDefaultConfig()
        {
            return new LocationHierarchyConfig
            {
                Levels = new List<LocationLevel>
                {
                    new LocationLevel
                    {
                        Code = "building",
                        Name = "建筑",
                        Description = "建筑物",
                        Icon = "building",
                        Color = "#1890ff",
                        CanContainAssets = false
                    },
                    new LocationLevel
                    {
                        Code = "floor",
                        Name = "楼层",
                        Description = "建筑物的楼层",
                        Icon = "apartment",
                        Color = "#52c41a",
                        CanContainAssets = false
                    },
                    new LocationLevel
                    {
                        Code = "room",
                        Name = "房间",
                        Description = "具体房间",
                        Icon = "home",
                        Color = "#722ed1",
                        CanContainAssets = true
                    }
                },
                RootNodes = new List<LocationNode>
                {
                    new LocationNode
                    {
                        Id = Guid.NewGuid().ToString("N"),
                        Code = "HQ",
                        Name = "总部大楼",
                        LevelCode = "building",
                        Description = "公司总部大楼",
                        Children = new List<LocationNode>
                        {
                            new LocationNode
                            {
                                Id = Guid.NewGuid().ToString("N"),
                                Code = "HQ-F1",
                                Name = "1楼",
                                LevelCode = "floor",
                                Description = "总部大楼1楼",
                                Children = new List<LocationNode>
                                {
                                    new LocationNode
                                    {
                                        Id = Guid.NewGuid().ToString("N"),
                                        Code = "HQ-F1-R101",
                                        Name = "101会议室",
                                        LevelCode = "room",
                                        Description = "1楼101会议室"
                                    },
                                    new LocationNode
                                    {
                                        Id = Guid.NewGuid().ToString("N"),
                                        Code = "HQ-F1-R102",
                                        Name = "102办公室",
                                        LevelCode = "room",
                                        Description = "1楼102办公室"
                                    }
                                }
                            },
                            new LocationNode
                            {
                                Id = Guid.NewGuid().ToString("N"),
                                Code = "HQ-F2",
                                Name = "2楼",
                                LevelCode = "floor",
                                Description = "总部大楼2楼",
                                Children = new List<LocationNode>
                                {
                                    new LocationNode
                                    {
                                        Id = Guid.NewGuid().ToString("N"),
                                        Code = "HQ-F2-R201",
                                        Name = "201办公室",
                                        LevelCode = "room",
                                        Description = "2楼201办公室"
                                    },
                                    new LocationNode
                                    {
                                        Id = Guid.NewGuid().ToString("N"),
                                        Code = "HQ-F2-R202",
                                        Name = "202会议室",
                                        LevelCode = "room",
                                        Description = "2楼202会议室"
                                    }
                                }
                            }
                        }
                    }
                }
            };
        }
    }
} 