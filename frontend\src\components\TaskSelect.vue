<template>
  <el-select
    v-model="selectedValue"
    :placeholder="placeholder"
    :multiple="multiple"
    :filterable="true"
    :remote="true"
    :remote-method="searchTasks"
    :loading="loading"
    :clearable="true"
    @change="handleChange"
  >
    <el-option
      v-for="task in tasks"
      :key="task.id"
      :label="task.title"
      :value="task.id"
    >
      <div class="task-option">
        <div class="task-info">
          <span class="task-title">{{ task.title }}</span>
          <el-tag :type="getPriorityType(task.priority)" size="small">
            {{ getPriorityLabel(task.priority) }}
          </el-tag>
          <el-tag :type="getStatusType(task.status)" size="small">
            {{ getStatusLabel(task.status) }}
          </el-tag>
        </div>
        <div class="task-meta">
          <span class="task-assignee" v-if="task.assigneeName">
            {{ task.assigneeName }}
          </span>
          <span class="task-date" v-if="task.dueDate">
            {{ formatDate(task.dueDate) }}
          </span>
        </div>
      </div>
    </el-option>
  </el-select>
</template>

<script>
import { ref, watch } from 'vue'
import { taskApi } from '@/api/tasks'

export default {
  name: 'TaskSelect',
  props: {
    modelValue: {
      type: [String, Number, Array],
      default: null
    },
    multiple: {
      type: Boolean,
      default: false
    },
    placeholder: {
      type: String,
      default: '请选择任务'
    },
    excludeCompleted: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:modelValue', 'change'],
  setup(props, { emit }) {
    const selectedValue = ref(props.modelValue)
    const tasks = ref([])
    const loading = ref(false)

    const searchTasks = async (query) => {
      if (!query) {
        tasks.value = []
        return
      }
      
      loading.value = true
      try {
        const params = {
          search: query,
          pageSize: 20
        }
        if (props.excludeCompleted) {
          params.excludeStatus = 'Completed'
        }
        
        const response = await taskApi.getTasks(params)
        tasks.value = response.data?.items || []
      } catch (error) {
        console.error('搜索任务失败:', error)
        tasks.value = []
      } finally {
        loading.value = false
      }
    }

    const handleChange = (value) => {
      emit('update:modelValue', value)
      emit('change', value)
    }

    const getPriorityType = (priority) => {
      const priorityMap = {
        'High': 'danger',
        'Medium': 'warning',
        'Low': 'success'
      }
      return priorityMap[priority] || 'info'
    }

    const getPriorityLabel = (priority) => {
      const priorityMap = {
        'High': '高',
        'Medium': '中',
        'Low': '低'
      }
      return priorityMap[priority] || priority
    }

    const getStatusType = (status) => {
      const statusMap = {
        'Pending': 'info',
        'InProgress': 'warning',
        'Completed': 'success',
        'Cancelled': 'danger'
      }
      return statusMap[status] || 'info'
    }

    const getStatusLabel = (status) => {
      const statusMap = {
        'Pending': '待处理',
        'InProgress': '进行中',
        'Completed': '已完成',
        'Cancelled': '已取消'
      }
      return statusMap[status] || status
    }

    const formatDate = (date) => {
      if (!date) return ''
      return new Date(date).toLocaleDateString('zh-CN')
    }

    watch(() => props.modelValue, (newValue) => {
      selectedValue.value = newValue
    })

    return {
      selectedValue,
      tasks,
      loading,
      searchTasks,
      handleChange,
      getPriorityType,
      getPriorityLabel,
      getStatusType,
      getStatusLabel,
      formatDate
    }
  }
}
</script>

<style scoped>
.task-option {
  width: 100%;
}

.task-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.task-title {
  font-weight: 500;
  flex: 1;
}

.task-meta {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #909399;
}
</style>