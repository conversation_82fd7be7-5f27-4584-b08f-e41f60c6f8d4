// IT资产管理系统 - 故障控制器
// 文件路径: /Controllers/FaultController.cs
// 功能: 提供故障管理相关API

#nullable enable
using ItAssetsSystem.Core.Events;
using ItAssetsSystem.Core.Plugins;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ItAssetsSystem.Controllers
{
    /// <summary>
    /// 故障控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class FaultController : ControllerBase
    {
        private readonly ILogger<FaultController> _logger;
        private readonly ItAssetsSystem.Core.Events.IEventBus _eventBus;
        private readonly IFaultReportService? _faultReportService;
        private readonly IFaultProcessService? _faultProcessService;

        /// <summary>
        /// 构造函数
        /// </summary>
        public FaultController(
            ILogger<FaultController> logger,
            ItAssetsSystem.Core.Events.IEventBus eventBus,
            IFaultReportService? faultReportService = null,
            IFaultProcessService? faultProcessService = null)
        {
            _logger = logger;
            _eventBus = eventBus;
            _faultReportService = faultReportService;
            _faultProcessService = faultProcessService;
        }

        /// <summary>
        /// 获取所有故障记录
        /// </summary>
        /// <returns>故障记录列表</returns>
        [HttpGet]
        public IActionResult GetAll()
        {
            _logger.LogInformation("获取所有故障记录");
            try
            {
                // 返回模拟数据
                var faults = new List<object>
                {
                    new { id = 1, assetId = 1, assetName = "笔记本电脑", faultType = "硬件故障", title = "屏幕闪烁", status = 1, reportTime = DateTime.Now.AddDays(-3) },
                    new { id = 2, assetId = 2, assetName = "打印机", faultType = "硬件故障", title = "无法打印", status = 2, reportTime = DateTime.Now.AddDays(-5) },
                    new { id = 3, assetId = 3, assetName = "投影仪", faultType = "软件故障", title = "无法连接", status = 0, reportTime = DateTime.Now.AddDays(-1) }
                };
                
                return Ok(new { success = true, data = faults });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取故障记录列表出错");
                return StatusCode(500, new { success = false, message = "获取故障记录列表出错" });
            }
        }

        /// <summary>
        /// 获取故障记录详情
        /// </summary>
        /// <param name="id">故障ID</param>
        /// <returns>故障详情</returns>
        [HttpGet("{id}")]
        public IActionResult GetById(int id)
        {
            _logger.LogInformation($"获取故障记录ID: {id}");
            try
            {
                // 返回模拟数据
                var fault = new
                {
                    id = id,
                    assetId = 1,
                    assetName = "笔记本电脑",
                    faultTypeId = 1,
                    faultTypeName = "硬件故障",
                    title = "屏幕闪烁",
                    description = "使用过程中屏幕频繁闪烁，影响正常使用",
                    reporterId = 1,
                    reporterName = "张三",
                    reportTime = DateTime.Now.AddDays(-3),
                    status = 1, // 0:待处理, 1:处理中, 2:已修复, 3:无法修复
                    statusName = "处理中",
                    assigneeId = 2,
                    assigneeName = "李四",
                    assignTime = DateTime.Now.AddDays(-2),
                    severity = 2, // 1:低, 2:中, 3:高, 4:紧急
                    severityName = "中",
                    resolution = "",
                    rootCause = "",
                    notes = "已联系维修人员"
                };
                
                return Ok(new { success = true, data = fault });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取故障记录ID {id} 出错");
                return StatusCode(500, new { success = false, message = $"获取故障记录ID {id} 出错" });
            }
        }

        /// <summary>
        /// 报告故障
        /// </summary>
        /// <param name="model">故障报告模型</param>
        /// <returns>创建结果</returns>
        [HttpPost]
        public IActionResult Report([FromBody] ReportFaultModel model)
        {
            _logger.LogInformation($"报告故障: {model.Title}, 资产ID: {model.AssetId}");
            try
            {
                // 调用故障报告服务（如果可用）
                int faultId;
                if (_faultReportService != null)
                {
                    faultId = _faultReportService.ReportFault(
                        model.AssetId ?? 0, // 如果没有资产ID，传递0
                        model.FaultTypeId,
                        model.Title,
                        model.Description,
                        model.ReporterId
                    );
                }
                else
                {
                    // 模拟故障ID
                    faultId = new Random().Next(1000, 9999);
                }

                return Ok(new
                {
                    success = true,
                    data = new { id = faultId },
                    message = "故障报告提交成功"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "报告故障出错");
                return StatusCode(500, new { success = false, message = "报告故障出错" });
            }
        }

        /// <summary>
        /// 修复故障
        /// </summary>
        /// <param name="id">故障ID</param>
        /// <param name="model">故障修复模型</param>
        /// <returns>修复结果</returns>
        [HttpPut("{id}/fix")]
        public IActionResult Fix(int id, [FromBody] FixFaultModel model)
        {
            _logger.LogInformation($"修复故障: {id}");
            try
            {
                // 调用故障处理服务（如果可用）
                if (_faultProcessService != null)
                {
                    _faultProcessService.FixFault(id, model.Resolution, model.RootCause);
                }

                // 发布故障修复事件
                _eventBus.Publish(new FaultFixedEvent
                {
                    FaultId = id,
                    FaultTypeId = 1, // 实际实现中应该从数据库获取
                    AssigneeId = model.AssigneeId,
                    Resolution = model.Resolution
                });

                return Ok(new
                {
                    success = true,
                    message = "故障修复成功"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"修复故障 {id} 出错");
                return StatusCode(500, new { success = false, message = $"修复故障 {id} 出错" });
            }
        }

        /// <summary>
        /// 故障维修使用备件
        /// </summary>
        /// <param name="id">故障ID</param>
        /// <param name="model">备件使用模型</param>
        /// <returns>使用结果</returns>
        [HttpPost("{id}/use-spare-parts")]
        public IActionResult UseSparePartsForRepair(int id, [FromBody] UseSparePartsModel model)
        {
            _logger.LogInformation($"故障维修使用备件: 故障ID {id}");
            try
            {
                // 模拟备件使用逻辑
                var usedParts = new List<object>();
                foreach (var part in model.SpareParts)
                {
                    usedParts.Add(new
                    {
                        sparePartId = part.SparePartId,
                        sparePartName = $"备件-{part.SparePartId}",
                        usedQuantity = part.Quantity,
                        remainingStock = 50 - part.Quantity // 模拟剩余库存
                    });
                }

                return Ok(new
                {
                    success = true,
                    data = new
                    {
                        faultId = id,
                        usedSpareParts = usedParts
                    },
                    message = "备件使用记录成功"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"故障维修使用备件失败: 故障ID {id}");
                return StatusCode(500, new { success = false, message = "备件使用记录失败" });
            }
        }

        /// <summary>
        /// 创建返厂记录
        /// </summary>
        /// <param name="id">故障ID</param>
        /// <param name="model">返厂模型</param>
        /// <returns>创建结果</returns>
        [HttpPost("{id}/return-to-factory")]
        public IActionResult CreateReturnToFactory(int id, [FromBody] ReturnToFactoryModel model)
        {
            _logger.LogInformation($"创建返厂记录: 故障ID {id}");
            try
            {
                // 模拟创建返厂记录
                var returnId = new Random().Next(1000, 9999);
                var returnCode = $"RTF-{DateTime.Now:yyyyMMdd}-{returnId:D4}";

                return Ok(new
                {
                    success = true,
                    data = new
                    {
                        id = returnId,
                        code = returnCode,
                        faultId = id,
                        assetId = model.AssetId,
                        supplierId = model.SupplierId,
                        reason = model.Reason,
                        status = 0, // 待送出
                        statusName = "待送出",
                        estimatedReturnTime = model.EstimatedReturnTime
                    },
                    message = "返厂记录创建成功"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"创建返厂记录失败: 故障ID {id}");
                return StatusCode(500, new { success = false, message = "创建返厂记录失败" });
            }
        }
    }

    /// <summary>
    /// 故障报告模型
    /// </summary>
    public class ReportFaultModel
    {
        /// <summary>
        /// 资产ID（可选，对于电池等无固定资产编号的故障可为空）
        /// </summary>
        public int? AssetId { get; set; }

        /// <summary>
        /// 故障类型ID
        /// </summary>
        public int FaultTypeId { get; set; }

        /// <summary>
        /// 故障标题
        /// </summary>
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// 故障描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 严重程度
        /// </summary>
        public int Severity { get; set; }

        /// <summary>
        /// 报告人ID
        /// </summary>
        public int ReporterId { get; set; }

        /// <summary>
        /// 故障发生时间
        /// </summary>
        public DateTime? HappenTime { get; set; }

        /// <summary>
        /// 优先级
        /// </summary>
        public string Priority { get; set; } = "medium";

        /// <summary>
        /// 是否需要自动生成备件入库记录
        /// </summary>
        public bool AutoGenerateSparePartRecord { get; set; } = true;

        /// <summary>
        /// 备件信息（如果需要自动生成备件记录）
        /// </summary>
        public SparePartInfo? SparePartInfo { get; set; }
    }

    /// <summary>
    /// 备件信息模型
    /// </summary>
    public class SparePartInfo
    {
        /// <summary>
        /// 备件名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 备件类型ID
        /// </summary>
        public int? TypeId { get; set; }

        /// <summary>
        /// 规格型号
        /// </summary>
        public string Specification { get; set; } = string.Empty;

        /// <summary>
        /// 品牌
        /// </summary>
        public string? Brand { get; set; }

        /// <summary>
        /// 入库数量
        /// </summary>
        public int Quantity { get; set; } = 1;

        /// <summary>
        /// 单价
        /// </summary>
        public decimal? Price { get; set; }

        /// <summary>
        /// 库位ID
        /// </summary>
        public int? LocationId { get; set; }
    }

    /// <summary>
    /// 故障修复模型
    /// </summary>
    public class FixFaultModel
    {
        /// <summary>
        /// 处理人ID
        /// </summary>
        public int AssigneeId { get; set; }

        /// <summary>
        /// 解决方案
        /// </summary>
        public string? Resolution { get; set; }

        /// <summary>
        /// 根本原因
        /// </summary>
        public string? RootCause { get; set; }
    }

    /// <summary>
    /// 备件使用模型
    /// </summary>
    public class UseSparePartsModel
    {
        /// <summary>
        /// 使用的备件列表
        /// </summary>
        public List<SparePartUsage> SpareParts { get; set; } = new List<SparePartUsage>();

        /// <summary>
        /// 备注
        /// </summary>
        public string? Notes { get; set; }
    }

    /// <summary>
    /// 备件使用信息
    /// </summary>
    public class SparePartUsage
    {
        /// <summary>
        /// 备件ID
        /// </summary>
        public long SparePartId { get; set; }

        /// <summary>
        /// 使用数量
        /// </summary>
        public int Quantity { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Notes { get; set; }
    }

    /// <summary>
    /// 返厂模型
    /// </summary>
    public class ReturnToFactoryModel
    {
        /// <summary>
        /// 资产ID
        /// </summary>
        public int AssetId { get; set; }

        /// <summary>
        /// 供应商ID
        /// </summary>
        public int SupplierId { get; set; }

        /// <summary>
        /// 返厂原因
        /// </summary>
        public string? Reason { get; set; }

        /// <summary>
        /// 预计返回时间
        /// </summary>
        public DateTime? EstimatedReturnTime { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Notes { get; set; }
    }
}

// 计划行数: 150
// 实际行数: 150 