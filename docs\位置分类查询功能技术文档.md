# 位置分类查询功能技术文档

## 📋 目录
- [功能概述](#功能概述)
- [技术架构](#技术架构)
- [核心功能](#核心功能)
- [API接口文档](#api接口文档)
- [数据模型](#数据模型)
- [实现原理](#实现原理)
- [使用指南](#使用指南)
- [性能优化](#性能优化)
- [故障排除](#故障排除)

## 功能概述

### 背景
在IT资产管理系统中，位置和部门的关系往往存在复杂的层级继承关系。例如：
- 一个工位可能没有直接分配部门，但继承自其所在车间的部门
- 需要快速查找某个部门管理的所有位置和资产
- 需要按多种维度（部门、位置类型、资产类型等）进行复合查询

### 解决方案
位置分类查询功能通过CTE（Common Table Expression）递归查询技术，实现了：
1. **位置部门继承关系的自动计算**
2. **多维度分类查询**
3. **统计分析功能**
4. **高性能的大数据量查询**

### 核心价值
- 🎯 **精确定位**: 快速找到符合条件的位置和资产
- 📊 **数据洞察**: 提供多维度统计分析
- ⚡ **高性能**: 单次查询处理复杂层级关系
- 🔄 **易集成**: 标准V2 API接口，易于前端集成

## 技术架构

### 架构层次
```
┌─────────────────────────────────────────┐
│              前端层 (Vue 3)              │
├─────────────────────────────────────────┤
│           API控制器层 (V2)               │
├─────────────────────────────────────────┤
│          应用服务层 (Services)           │
├─────────────────────────────────────────┤
│          数据访问层 (EF Core)            │
├─────────────────────────────────────────┤
│           数据库层 (MySQL)               │
└─────────────────────────────────────────┘
```

### 关键组件
- **LocationDepartmentInheritanceController**: V2 API控制器
- **ILocationDepartmentInheritanceService**: 服务接口
- **LocationDepartmentInheritanceService**: 核心业务逻辑
- **LocationClassificationQueryDto**: 查询参数模型
- **LocationClassificationResultDto**: 查询结果模型

### 技术栈
- **后端**: .NET 6, Entity Framework Core 6, MySQL
- **前端**: Vue 3, Element Plus, Axios
- **查询引擎**: MySQL CTE递归查询
- **架构模式**: Clean Architecture, CQRS

## 核心功能

### 1. 位置部门继承
```sql
-- 核心CTE递归查询逻辑
WITH RECURSIVE location_hierarchy AS (
    -- 基础情况：顶级位置
    SELECT 
        l.id as location_id,
        l.department_id as inherited_department_id
    FROM locations l
    WHERE l.parent_id IS NULL
    
    UNION ALL
    
    -- 递归情况：子位置继承父位置部门
    SELECT 
        l.id as location_id,
        COALESCE(l.department_id, lh.inherited_department_id) as inherited_department_id
    FROM locations l
    INNER JOIN location_hierarchy lh ON l.parent_id = lh.location_id
)
```

### 2. 多维度分类查询
支持以下查询维度：
- **部门维度**: `departmentId`
- **位置类型**: `locationType` (1=工厂, 2=车间, 3=工序, 4=工位)
- **层级关系**: `parentLocationId` + `includeChildren`
- **资产维度**: `assetTypeId`, `assetStatus`
- **筛选条件**: `onlyWithAssets`, `onlyDirectDepartment`
- **关键词搜索**: `keyword`
- **排序分页**: `sortBy`, `sortDirection`, `pageNumber`, `pageSize`

### 3. 统计分析
提供四个维度的统计：
- **按部门统计**: 直接/继承的位置数和资产数
- **按位置类型统计**: 各类型的位置数和资产数
- **按资产类型统计**: 资产分布情况
- **按层级统计**: 各层级的统计信息

## API接口文档

### 基础路径
```
/api/v2/locationdepartmentinheritance
```

### 接口列表

#### 1. 获取所有位置的部门继承信息
```http
GET /api/v2/locationdepartmentinheritance
Authorization: Bearer {token}
```

**响应示例:**
```json
{
  "success": true,
  "data": [
    {
      "locationId": 1,
      "locationName": "生产车间A",
      "locationPath": "工厂 > 生产区 > 生产车间A",
      "directDepartmentId": 5,
      "directDepartmentName": "生产部",
      "inheritedDepartmentId": 5,
      "inheritedDepartmentName": "生产部",
      "effectiveDepartmentId": 5,
      "effectiveDepartmentName": "生产部",
      "assetCount": 15,
      "assets": [...]
    }
  ],
  "message": "成功获取 50 个位置的部门继承信息"
}
```

#### 2. 高级分类查询 (POST)
```http
POST /api/v2/locationdepartmentinheritance/search
Content-Type: application/json
Authorization: Bearer {token}
```

**请求体:**
```json
{
  "departmentId": 5,
  "locationType": 2,
  "parentLocationId": 10,
  "assetTypeId": 3,
  "assetStatus": "正常",
  "includeChildren": true,
  "onlyWithAssets": true,
  "onlyDirectDepartment": false,
  "keyword": "生产",
  "sortBy": "assetCount",
  "sortDirection": "desc",
  "pageNumber": 1,
  "pageSize": 20
}
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "locations": [...],
    "totalCount": 150,
    "stats": {
      "byDepartment": [...],
      "byLocationType": [...],
      "byAssetType": [...],
      "byLocationLevel": [...]
    }
  },
  "message": "成功查询到 150 个位置，当前页 20 个"
}
```

#### 3. 快速查询 (GET)
```http
GET /api/v2/locationdepartmentinheritance/search?departmentId=5&locationType=2&onlyWithAssets=true&pageNumber=1&pageSize=20
Authorization: Bearer {token}
```

#### 4. 获取分类统计信息
```http
GET /api/v2/locationdepartmentinheritance/classification-stats
Authorization: Bearer {token}
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "byDepartment": [
      {
        "departmentId": 5,
        "departmentName": "生产部",
        "directLocationCount": 3,
        "inheritedLocationCount": 7,
        "totalLocationCount": 10,
        "directAssetCount": 25,
        "inheritedAssetCount": 40,
        "totalAssetCount": 65
      }
    ],
    "byLocationType": [
      {
        "locationType": 2,
        "locationTypeName": "车间",
        "locationCount": 15,
        "assetCount": 150,
        "locationsWithDepartment": 12
      }
    ],
    "byAssetType": [...],
    "byLocationLevel": [...]
  }
}
```

## 数据模型

### 请求模型

#### LocationClassificationQueryDto
```csharp
public class LocationClassificationQueryDto
{
    public int? DepartmentId { get; set; }           // 部门ID筛选
    public int? LocationType { get; set; }           // 位置类型筛选
    public int? ParentLocationId { get; set; }       // 父级位置ID筛选
    public int? AssetTypeId { get; set; }            // 资产类型ID筛选
    public string? AssetStatus { get; set; }         // 资产状态筛选
    public bool IncludeChildren { get; set; } = true;        // 是否包含子位置
    public bool OnlyWithAssets { get; set; } = false;        // 是否只显示有资产的位置
    public bool OnlyDirectDepartment { get; set; } = false;  // 是否只显示有直接部门的位置
    public string? Keyword { get; set; }             // 关键词搜索
    public string? SortBy { get; set; } = "locationPath";    // 排序字段
    public string? SortDirection { get; set; } = "asc";      // 排序方向
    public int PageNumber { get; set; } = 1;         // 页码
    public int PageSize { get; set; } = 20;          // 每页大小
}
```

### 响应模型

#### LocationDepartmentInheritanceDto
```csharp
public class LocationDepartmentInheritanceDto
{
    public int LocationId { get; set; }
    public string LocationName { get; set; } = string.Empty;
    public string LocationPath { get; set; } = string.Empty;
    public int? DirectDepartmentId { get; set; }
    public string? DirectDepartmentName { get; set; }
    public int? InheritedDepartmentId { get; set; }
    public string? InheritedDepartmentName { get; set; }
    public int? EffectiveDepartmentId { get; set; }
    public string? EffectiveDepartmentName { get; set; }
    public int AssetCount { get; set; }
    public List<AssetSummaryDto> Assets { get; set; } = new();
}
```

#### LocationClassificationResultDto
```csharp
public class LocationClassificationResultDto
{
    public List<LocationDepartmentInheritanceDto> Locations { get; set; } = new();
    public int TotalCount { get; set; }
    public LocationClassificationStatsDto Stats { get; set; } = new();
}
```

## 实现原理

### 1. CTE递归查询原理
```sql
WITH RECURSIVE location_hierarchy AS (
    -- 锚点查询：查找所有顶级位置（没有父位置的位置）
    SELECT 
        l.id as location_id,
        l.name as location_name,
        l.parent_id,
        l.department_id as direct_department_id,
        d.name as direct_department_name,
        l.department_id as inherited_department_id,  -- 顶级位置的继承部门就是自己的部门
        d.name as inherited_department_name,
        CAST(l.name AS CHAR(1000)) as location_path, -- 位置路径
        0 as level  -- 层级深度
    FROM locations l
    LEFT JOIN departments d ON l.department_id = d.id
    WHERE l.parent_id IS NULL  -- 顶级位置条件
    
    UNION ALL
    
    -- 递归查询：查找子位置
    SELECT 
        l.id as location_id,
        l.name as location_name,
        l.parent_id,
        l.department_id as direct_department_id,
        d.name as direct_department_name,
        -- 继承逻辑：如果当前位置有部门就用自己的，否则继承父位置的部门
        COALESCE(l.department_id, lh.inherited_department_id) as inherited_department_id,
        COALESCE(d.name, lh.inherited_department_name) as inherited_department_name,
        -- 构建层级路径
        CONCAT(lh.location_path, ' > ', l.name) as location_path,
        lh.level + 1 as level  -- 层级深度递增
    FROM locations l
    INNER JOIN location_hierarchy lh ON l.parent_id = lh.location_id  -- 递归连接
    LEFT JOIN departments d ON l.department_id = d.id
)
```

### 2. 资产数据聚合
```sql
location_with_assets AS (
    SELECT 
        lh.*,
        COUNT(a.id) as asset_count,
        -- 使用GROUP_CONCAT聚合资产信息，避免多次查询
        GROUP_CONCAT(
            CONCAT(a.id, '|', a.name, '|', a.asset_code, '|', COALESCE(at.name, ''))
            ORDER BY a.id
            SEPARATOR ';'
        ) as assets_data
    FROM location_hierarchy lh
    LEFT JOIN assets a ON lh.location_id = a.location_id
    LEFT JOIN asset_types at ON a.asset_type_id = at.id
    GROUP BY lh.location_id
)
```

### 3. 动态条件构建
```csharp
private string BuildWhereConditions(LocationClassificationQueryDto query)
{
    var conditions = new List<string>();

    if (query.DepartmentId.HasValue)
        conditions.Add($"lwa.inherited_department_id = {query.DepartmentId}");

    if (query.LocationType.HasValue)
        conditions.Add($"lwa.location_type = {query.LocationType}");

    if (query.ParentLocationId.HasValue)
    {
        if (query.IncludeChildren)
        {
            // 递归查询子位置：查找路径包含指定位置的所有位置
            conditions.Add($"(lwa.location_id = {query.ParentLocationId} OR lwa.location_path LIKE CONCAT('%', (SELECT name FROM locations WHERE id = {query.ParentLocationId}), '%'))");
        }
        else
        {
            // 只查询直接子位置
            conditions.Add($"lwa.parent_id = {query.ParentLocationId}");
        }
    }

    if (query.OnlyWithAssets)
        conditions.Add("lwa.asset_count > 0");

    if (query.OnlyDirectDepartment)
        conditions.Add("lwa.direct_department_id IS NOT NULL");

    if (!string.IsNullOrEmpty(query.Keyword))
    {
        var keyword = query.Keyword.Replace("'", "''"); // SQL注入防护
        conditions.Add($"(lwa.location_name LIKE '%{keyword}%' OR lwa.inherited_department_name LIKE '%{keyword}%' OR lwa.assets_data LIKE '%{keyword}%')");
    }

    return conditions.Count > 0 ? "WHERE " + string.Join(" AND ", conditions) : "";
}
```

### 4. 性能优化策略
1. **索引优化**: 在关键字段上建立索引
2. **查询缓存**: 缓存不频繁变更的数据
3. **分页处理**: 使用LIMIT/OFFSET进行分页
4. **数据聚合**: 一次查询获取所有相关数据

## 使用指南

### 1. 前端集成

#### API调用示例
```javascript
import locationDepartmentInheritanceApi from '@/api/locationDepartmentInheritance'

// 查询生产部的所有车间
const searchProductionWorkshops = async () => {
  const query = {
    departmentId: 5,        // 生产部ID
    locationType: 2,        // 车间类型
    includeChildren: true,  // 包含子位置
    sortBy: 'assetCount',   // 按资产数量排序
    sortDirection: 'desc'   // 降序排列
  }
  
  const response = await locationDepartmentInheritanceApi.searchLocationsByClassification(query)
  return response.data
}

// 获取维修设备列表
const getMaintenanceAssets = async () => {
  const query = {
    assetStatus: '维修中',
    onlyWithAssets: true,
    sortBy: 'departmentName'
  }
  
  const response = await locationDepartmentInheritanceApi.searchLocationsByClassification(query)
  return response.data
}
```

#### 组件使用
```vue
<template>
  <div>
    <!-- 高级搜索组件 -->
    <LocationClassificationSearch 
      @search-complete="handleSearchComplete"
    />
    
    <!-- 或者使用部门继承面板 -->
    <DepartmentInheritancePanel 
      :location-id="selectedLocationId"
      @asset-click="handleAssetClick"
    />
  </div>
</template>

<script setup>
import LocationClassificationSearch from './components/LocationClassificationSearch.vue'
import DepartmentInheritancePanel from './components/DepartmentInheritancePanel.vue'

const handleSearchComplete = (results) => {
  console.log('搜索结果:', results)
}

const handleAssetClick = (assetId) => {
  // 跳转到资产详情页
  router.push({ name: 'AssetDetail', params: { id: assetId } })
}
</script>
```

### 2. 后端扩展

#### 添加新的查询条件
```csharp
// 在LocationClassificationQueryDto中添加新字段
public DateTime? CreatedAfter { get; set; }
public DateTime? CreatedBefore { get; set; }

// 在BuildWhereConditions方法中添加相应逻辑
if (query.CreatedAfter.HasValue)
    conditions.Add($"lwa.created_at >= '{query.CreatedAfter:yyyy-MM-dd}'");

if (query.CreatedBefore.HasValue)
    conditions.Add($"lwa.created_at <= '{query.CreatedBefore:yyyy-MM-dd}'");
```

#### 添加新的统计维度
```csharp
public async Task<List<LocationStatusStatsDto>> GetLocationStatusStatsAsync()
{
    var sql = @"
        SELECT 
            status,
            COUNT(*) as location_count,
            SUM(asset_count) as total_assets
        FROM location_with_assets
        GROUP BY status
        ORDER BY location_count DESC";
        
    // 执行查询逻辑...
}
```

### 3. 业务场景示例

#### 场景1: 设备巡检计划
```javascript
// 生成巡检计划：按部门获取所有需要巡检的设备位置
const generateInspectionPlan = async (departmentId) => {
  const query = {
    departmentId: departmentId,
    assetStatus: '正常',
    onlyWithAssets: true,
    sortBy: 'locationPath'
  }
  
  const response = await locationDepartmentInheritanceApi.searchLocationsByClassification(query)
  
  // 按位置类型分组
  const plan = response.data.locations.reduce((acc, location) => {
    const type = getLocationTypeName(location.locationType)
    if (!acc[type]) acc[type] = []
    acc[type].push({
      locationId: location.locationId,
      locationName: location.locationName,
      locationPath: location.locationPath,
      assetCount: location.assetCount,
      assets: location.assets
    })
    return acc
  }, {})
  
  return plan
}
```

#### 场景2: 资产盘点报表
```javascript
// 生成部门资产盘点报表
const generateInventoryReport = async () => {
  const stats = await locationDepartmentInheritanceApi.getLocationClassificationStats()
  
  const report = stats.data.byDepartment.map(dept => ({
    部门名称: dept.departmentName,
    管理位置总数: dept.totalLocationCount,
    直接管理位置: dept.directLocationCount,
    继承管理位置: dept.inheritedLocationCount,
    资产总数: dept.totalAssetCount,
    直接管理资产: dept.directAssetCount,
    继承管理资产: dept.inheritedAssetCount,
    位置利用率: Math.round(dept.locationsWithAssets / dept.totalLocationCount * 100) + '%'
  }))
  
  return report
}
```

#### 场景3: 故障分析
```javascript
// 分析故障设备的位置分布
const analyzeFaultDistribution = async () => {
  const faultQuery = {
    assetStatus: '故障',
    onlyWithAssets: true,
    sortBy: 'departmentName'
  }
  
  const response = await locationDepartmentInheritanceApi.searchLocationsByClassification(faultQuery)
  
  // 按部门统计故障设备
  const faultStats = response.data.locations.reduce((stats, location) => {
    const dept = location.effectiveDepartmentName || '未分配'
    if (!stats[dept]) {
      stats[dept] = { locationCount: 0, faultAssetCount: 0 }
    }
    stats[dept].locationCount++
    stats[dept].faultAssetCount += location.assets.filter(asset => asset.status === '故障').length
    return stats
  }, {})
  
  return faultStats
}
```

## 性能优化

### 1. 数据库优化

#### 必要索引
```sql
-- 位置表索引
CREATE INDEX idx_locations_department_id ON locations(department_id);
CREATE INDEX idx_locations_parent_id ON locations(parent_id);
CREATE INDEX idx_locations_type ON locations(type);
CREATE INDEX idx_locations_name ON locations(name);

-- 资产表索引
CREATE INDEX idx_assets_location_id ON assets(location_id);
CREATE INDEX idx_assets_asset_type_id ON assets(asset_type_id);
CREATE INDEX idx_assets_status ON assets(status);
CREATE INDEX idx_assets_name ON assets(name);

-- 部门表索引
CREATE INDEX idx_departments_name ON departments(name);

-- 复合索引（根据查询模式）
CREATE INDEX idx_locations_type_dept ON locations(type, department_id);
CREATE INDEX idx_assets_location_status ON assets(location_id, status);
```

#### 查询优化
```sql
-- 使用分析器查看执行计划
EXPLAIN SELECT * FROM location_hierarchy WHERE inherited_department_id = 5;

-- 考虑分区（针对大表）
ALTER TABLE locations PARTITION BY RANGE(type) (
    PARTITION p_factory VALUES LESS THAN (2),
    PARTITION p_workshop VALUES LESS THAN (3),
    PARTITION p_process VALUES LESS THAN (4),
    PARTITION p_workstation VALUES LESS THAN (5)
);
```

### 2. 应用层优化

#### 缓存策略
```csharp
public class CachedLocationDepartmentInheritanceService : ILocationDepartmentInheritanceService
{
    private readonly ILocationDepartmentInheritanceService _innerService;
    private readonly IMemoryCache _cache;
    private readonly TimeSpan _cacheExpiry = TimeSpan.FromMinutes(30);

    public async Task<LocationClassificationStatsDto> GetLocationClassificationStatsAsync()
    {
        const string cacheKey = "location_classification_stats";
        
        if (_cache.TryGetValue(cacheKey, out LocationClassificationStatsDto cached))
        {
            return cached;
        }

        var result = await _innerService.GetLocationClassificationStatsAsync();
        _cache.Set(cacheKey, result, _cacheExpiry);
        
        return result;
    }
}
```

#### 异步处理
```csharp
// 对于大数据量查询，使用异步处理
public async Task<LocationClassificationResultDto> GetLocationsByClassificationAsync(
    LocationClassificationQueryDto query)
{
    // 并行执行主查询和统计查询
    var mainTask = ExecuteLocationQuery(mainSql);
    var countTask = ExecuteCountQuery(countSql);
    var statsTask = GetLocationClassificationStatsAsync();

    await Task.WhenAll(mainTask, countTask, statsTask);

    return new LocationClassificationResultDto
    {
        Locations = await mainTask,
        TotalCount = await countTask,
        Stats = await statsTask
    };
}
```

### 3. 前端优化

#### 虚拟滚动
```vue
<template>
  <el-table-v2
    :columns="columns"
    :data="searchResults"
    :width="800"
    :height="600"
    :row-height="60"
  />
</template>
```

#### 防抖搜索
```javascript
import { debounce } from 'lodash-es'

const debouncedSearch = debounce(async (query) => {
  await performSearch(query)
}, 300)

watch(() => searchForm.keyword, (newKeyword) => {
  if (newKeyword) {
    debouncedSearch({ ...searchForm, keyword: newKeyword })
  }
})
```

## 故障排除

### 常见问题

#### 1. 查询超时
**症状**: 查询执行时间过长，返回超时错误
**原因**: 
- 缺少必要索引
- 查询条件过于宽泛
- 数据量过大

**解决方案**:
```sql
-- 检查执行计划
EXPLAIN EXTENDED SELECT * FROM location_hierarchy WHERE inherited_department_id = 5;

-- 添加缺失索引
CREATE INDEX idx_missing ON locations(field_name);

-- 优化查询条件
-- 避免使用 SELECT *，指定具体字段
-- 增加更精确的筛选条件
```

#### 2. 内存不足
**症状**: 应用程序内存使用过高
**原因**: 
- 一次性加载过多数据
- 缺少分页限制

**解决方案**:
```csharp
// 限制最大页面大小
if (query.PageSize > 100)
{
    query.PageSize = 100;
}

// 使用流式处理大结果集
public async IAsyncEnumerable<LocationDepartmentInheritanceDto> GetLocationsStreamAsync(
    LocationClassificationQueryDto query)
{
    using var reader = await command.ExecuteReaderAsync();
    while (await reader.ReadAsync())
    {
        yield return MapFromReader(reader);
    }
}
```

#### 3. 递归深度过深
**症状**: MySQL报错"递归深度超限"
**原因**: 位置层级过深或存在循环引用

**解决方案**:
```sql
-- 设置递归深度限制
SET cte_max_recursion_depth = 100;

-- 检查循环引用
SELECT l1.id, l1.name, l1.parent_id 
FROM locations l1
JOIN locations l2 ON l1.parent_id = l2.id
WHERE l2.parent_id = l1.id;
```

#### 4. 权限问题
**症状**: 401未授权或403禁止访问
**原因**: JWT令牌过期或权限不足

**解决方案**:
```javascript
// 检查令牌有效性
const checkToken = () => {
  const token = localStorage.getItem('token')
  if (!token) {
    router.push('/login')
    return false
  }
  
  // 检查令牌是否过期
  const payload = JSON.parse(atob(token.split('.')[1]))
  if (payload.exp * 1000 < Date.now()) {
    localStorage.removeItem('token')
    router.push('/login')
    return false
  }
  
  return true
}
```

### 调试工具

#### 1. SQL查询日志
```csharp
// 在开发环境启用SQL日志
protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
{
    if (Environment.IsDevelopment())
    {
        optionsBuilder
            .EnableSensitiveDataLogging()
            .LogTo(Console.WriteLine, LogLevel.Information);
    }
}
```

#### 2. 性能监控
```csharp
public async Task<LocationClassificationResultDto> GetLocationsByClassificationAsync(
    LocationClassificationQueryDto query)
{
    using var activity = ActivitySource.StartActivity("LocationClassificationQuery");
    activity?.SetTag("query.departmentId", query.DepartmentId?.ToString());
    activity?.SetTag("query.pageSize", query.PageSize.ToString());
    
    var stopwatch = Stopwatch.StartNew();
    
    try
    {
        var result = await ExecuteQuery(query);
        
        stopwatch.Stop();
        _logger.LogInformation("查询完成，耗时: {ElapsedMs}ms, 结果数: {Count}", 
            stopwatch.ElapsedMilliseconds, result.Locations.Count);
            
        return result;
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "查询失败，耗时: {ElapsedMs}ms", stopwatch.ElapsedMilliseconds);
        throw;
    }
}
```

#### 3. 前端调试
```javascript
// API调用拦截器
request.interceptors.request.use(config => {
  console.log('API请求:', config.method?.toUpperCase(), config.url, config.data || config.params)
  return config
})

request.interceptors.response.use(
  response => {
    console.log('API响应:', response.config.url, '耗时:', Date.now() - response.config.metadata?.startTime, 'ms')
    return response
  },
  error => {
    console.error('API错误:', error.config?.url, error.message)
    return Promise.reject(error)
  }
)
```

---

## 总结

位置分类查询功能通过CTE递归查询技术，实现了复杂的位置部门继承关系计算和多维度分类查询。该功能具有以下特点：

✅ **高性能**: 单次查询处理复杂层级关系  
✅ **灵活性**: 支持多种查询条件组合  
✅ **可扩展**: 易于添加新的查询维度和统计指标  
✅ **易集成**: 标准化的API接口和前端组件  
✅ **安全性**: 完整的权限控制和数据验证  

通过合理的索引优化和缓存策略，该功能能够在大数据量情况下保持良好的性能表现，为IT资产管理系统提供强大的数据查询和分析能力。