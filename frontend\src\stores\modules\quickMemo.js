// File: frontend/src/stores/modules/quickMemo.js
// Description: Pinia store for Quick Memos (随手记).

import { defineStore } from 'pinia'
import {
  getQuickMemos,
  addQuickMemo,
  updateQuickMemo,
  deleteQuickMemo,
  getMemoCategories,
  addMemoCategory
} from '@/api/quickMemo'
import { ElMessage } from 'element-plus'; // For user feedback

export const useQuickMemoStore = defineStore('quickMemo', {
  state: () => ({
    quickMemos: [], // Will store { id, title, categoryId, x, y, width, height, zIndex, createdAt, updatedAt, etc. }
    categories: [], 
    isLoadingQuickMemos: false,
    isLoadingCategories: false,
    isMemoDrawerOpen: false, // Kept for potential future use or alternative views
    memoDrawerMode: 'create', 
    editingMemo: null 
  }),

  actions: {
    async fetchQuickMemos(params) {
      this.isLoadingQuickMemos = true
      try {
        const response = await getQuickMemos(params)
        if (response.success && Array.isArray(response.data)) {
          this.quickMemos = response.data.map(memo => ({
            ...memo,
            x: memo.positionX || memo.x || 0, 
            y: memo.positionY || memo.y || 0,
            width: memo.sizeWidth || memo.width || 200,
            height: memo.sizeHeight || memo.height || 180,
            zIndex: memo.zIndex || 1,
          }));
          this.quickMemos.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt)); // Ensure sort order
        } else {
          this.quickMemos = []
          console.error('Failed to fetch quick memos:', response.message);
          // ElMessage.error('获取随手记列表失败');
        }
      } catch (error) {
        console.error('Error fetching quick memos:', error)
        ElMessage.error('获取随手记列表失败，请检查网络或联系管理员');
        this.quickMemos = []
      }
      this.isLoadingQuickMemos = false
    },

    addMemoPlaceholder(memoData) {
      // Adds a temporary memo to the list for immediate UI update
      // This memo should have a temporary ID and an 'isNew' flag
      this.quickMemos.push(memoData);
    },

    removeMemoPlaceholder(tempId) {
      this.quickMemos = this.quickMemos.filter(m => m.id !== tempId);
    },
    
    updateLocalMemo(updatedMemo) {
      // Updates a memo in the local list without an API call (e.g., for zIndex)
      const index = this.quickMemos.findIndex(m => m.id === updatedMemo.id);
      if (index !== -1) {
        this.quickMemos.splice(index, 1, { ...this.quickMemos[index], ...updatedMemo });
      }
    },

    async createQuickMemo(memoData) {
      // memoData comes from StickyNote, includes x, y, width, height, zIndex
      const tempId = memoData.id; // if it was a placeholder, it might have a temp-id
      try {
        // 检查categoryId - 如果不存在或不是有效的分类ID (检查categories列表)，则设为null
        const validCategoryIds = this.categories.map(cat => cat.id);
        
        const payload = {
          title: memoData.title,
          content: memoData.content,
          // 只有当categoryId存在且在有效分类列表中时，才使用它
          categoryId: memoData.categoryId && validCategoryIds.includes(memoData.categoryId) 
                      ? memoData.categoryId 
                      : null,
          positionX: memoData.x,
          positionY: memoData.y,
          sizeWidth: memoData.width,
          sizeHeight: memoData.height,
          zIndex: memoData.zIndex,
        };
        // If it's a truly new memo (not an update of a temp one with a temp-id passed in)
        // the actual memoData object from the board might not have an ID or one starting with 'temp-'
        if (memoData.id && memoData.id.toString().startsWith('temp-')) {
             // This case is handled by replacing tempId below
        } else if (memoData.id) {
            // This implies it was an existing memo, should go to editQuickMemo
            // However, createQuickMemo is specifically for new ones.
            // This path might not be hit if board logic is correct (temp-id for new)
        }


        const response = await addQuickMemo(payload); // API call
        if (response.success && response.data) {
          const newMemoFromServer = {
            ...response.data,
            x: response.data.positionX || 0, 
            y: response.data.positionY || 0,
            width: response.data.sizeWidth || 200,
            height: response.data.sizeHeight || 180,
            zIndex: response.data.zIndex || 1
          };

          // Replace placeholder if it existed
          if (tempId && tempId.toString().startsWith('temp-')) {
            const index = this.quickMemos.findIndex(m => m.id === tempId);
            if (index !== -1) {
              this.quickMemos.splice(index, 1, newMemoFromServer);
            } else { // Fallback if placeholder not found (should not happen)
              this.quickMemos.unshift(newMemoFromServer); 
            }
          } else { // If it wasn't a placeholder, just add
             this.quickMemos.unshift(newMemoFromServer);
          }
          ElMessage.success('随手记已添加！');
          this.closeMemoDrawer(); // Close if it was open via old drawer
          return newMemoFromServer;
        } else {
            ElMessage.error(response.message || response.error || '添加随手记失败');
            throw new Error(response.message || response.error || '添加随手记失败');
        }
      } catch (error) {
        console.error('Error adding quick memo:', error);
        // 尝试从错误响应中提取具体错误信息
        const errorMessage = error.response?.data?.error || error.response?.data?.message || '添加随手记操作失败';
        ElMessage.error(errorMessage);
        throw error; 
      }
    },

    async editQuickMemo(memoData) {
      if (!memoData || !memoData.id || memoData.id.toString().startsWith('temp-')) {
        console.error('Error updating quick memo: Valid ID is missing');
        ElMessage.error('更新随手记失败：ID无效');
        return;
      }
      try {
        // 检查categoryId - 使用相同的验证逻辑
        const validCategoryIds = this.categories.map(cat => cat.id);
        
        const payload = {
          title: memoData.title,
          content: memoData.content,
          // 只有当categoryId存在且在有效分类列表中时，才使用它
          categoryId: memoData.categoryId && validCategoryIds.includes(memoData.categoryId) 
                      ? memoData.categoryId 
                      : null,
          positionX: memoData.x,
          positionY: memoData.y,
          sizeWidth: memoData.width,
          sizeHeight: memoData.height,
          zIndex: memoData.zIndex,
        };
        const response = await updateQuickMemo(memoData.id, payload);
        if (response.success && response.data) {
          const updatedMemoFromServer = {
             ...response.data,
             x: response.data.positionX || memoData.x || 0,
             y: response.data.positionY || memoData.y || 0,
             width: response.data.sizeWidth || memoData.width || 200,
             height: response.data.sizeHeight || memoData.height || 180,
             zIndex: response.data.zIndex || memoData.zIndex || 1
          };
          const index = this.quickMemos.findIndex(m => m.id === memoData.id);
          if (index !== -1) {
            this.quickMemos.splice(index, 1, updatedMemoFromServer);
          }
          ElMessage.success('随手记已更新！');
          this.closeMemoDrawer();
          return updatedMemoFromServer;
        } else {
            ElMessage.error(response.message || response.error || '更新随手记失败');
            throw new Error(response.message || response.error || '更新随手记失败');
        }
      } catch (error) {
        console.error('Error updating quick memo:', error);
        // 尝试从错误响应中提取具体错误信息
        const errorMessage = error.response?.data?.error || error.response?.data?.message || '更新随手记操作失败';
        ElMessage.error(errorMessage);
        throw error;
      }
    },

    async removeQuickMemo(memoId) {
      if (memoId.toString().startsWith('temp-')) { // Should be handled by removeMemoPlaceholder
        this.removeMemoPlaceholder(memoId);
        return;
      }
      try {
        const response = await deleteQuickMemo(memoId);
        if (response.success) {
            this.quickMemos = this.quickMemos.filter(m => m.id !== memoId);
            ElMessage.success('随手记已删除');
        } else {
            ElMessage.error(response.message || '删除随手记失败');
        }
      } catch (error) {
        console.error('Error deleting quick memo:', error);
        ElMessage.error('删除随手记操作失败');
      }
    },

    async fetchCategories() {
      this.isLoadingCategories = true;
      try {
        const response = await getMemoCategories();
        if (response.success && response.data) {
            this.categories = response.data;
            if (this.categories.length === 0) { // If API returns success but empty, add defaults
                 this.setDefaultCategories();
            }
        } else {
            // ElMessage.error('获取分类失败，使用默认分类');
            this.setDefaultCategories();
        }
      } catch (error) {
        console.error('Error fetching memo categories:', error);
        ElMessage.error('获取分类失败，将使用默认分类');
        this.setDefaultCategories();
      }
      this.isLoadingCategories = false;
    },
    
    setDefaultCategories() {
        // 由于后端需要分类ID是实际存在的，我们不再设置默认分类
        // 如果用户需要分类，应该先创建分类
        this.categories = [];
        
        // 提示用户创建分类
        ElMessage.info('没有可用的随手记分类，您可以点击"添加分类"按钮创建新分类');
    },

    async createCategory(categoryData) {
        // categoryData expects { name: string, color?: string }
        try {
            const payload = { ...categoryData };
            if (!payload.color) { // Assign a default color if not provided
                const defaultColors = ['#409EFF', '#E6A23C', '#67C23A', '#F56C6C', '#909399', '#b3e19d', '#fbc475'];
                payload.color = defaultColors[this.categories.length % defaultColors.length];
            }

            const response = await addMemoCategory(payload);
            if (response.success && response.data) {
                this.categories.push(response.data);
                ElMessage.success(`分类 "${response.data.name}" 已创建`);
                return response.data; 
            } else {
                ElMessage.error(response.message || '创建分类失败');
                return null;
            }
        } catch (error) {
            console.error('Error creating memo category:', error);
            ElMessage.error('创建分类操作失败');
            throw error;
        }
    },

    openMemoDrawer(mode = 'create', memo = null) {
      this.memoDrawerMode = mode
      this.editingMemo = mode === 'edit' && memo ? { ...memo } : null
      this.isMemoDrawerOpen = true
    },

    closeMemoDrawer() {
      this.isMemoDrawerOpen = false
      this.editingMemo = null
    }
  },

  getters: {
    getCategoryById: (state) => (categoryId) => {
      if (!categoryId) return state.categories.find(cat => cat.id === 'default') || state.categories[0] || null;
      return state.categories.find(cat => cat.id === categoryId) || state.categories.find(cat => cat.id === 'default');
    },
    // getCategoryClass is no longer needed if StickyNote directly uses color for styling
    getCategoryName: (state) => (categoryId) => {
        const cat = state.getCategoryById(categoryId); // Use the getter for consistency
        return cat ? cat.name : '未分类';
    }
  }
}) 