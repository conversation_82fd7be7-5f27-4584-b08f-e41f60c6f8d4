/**
 * API路径帮助工具
 * 文件路径: src/utils/api-path-helper.js
 * 功能描述: 帮助开发人员正确格式化API路径，避免重复的前缀问题
 */

import systemConfig from '@/config/system'

/**
 * 获取正确的API路径
 * @param {string} path 相对API路径（不应包含/api前缀）
 * @returns {string} 格式化后的正确API路径
 * 
 * 使用示例:
 * import { getApiPath } from '@/utils/api-path-helper'
 * 
 * // 正确: getApiPath('/Asset/1') -> '/api/Asset/1'
 * // 错误: getApiPath('/api/Asset/1') -> '/api/api/Asset/1'（会导致重复前缀）
 */
export function getApiPath(path) {
  // 确保path以'/'开头
  let formattedPath = path;
  if (!formattedPath.startsWith('/')) {
    formattedPath = '/' + formattedPath;
  }
  
  // 检查是否有重复的'/api'前缀
  if (formattedPath.startsWith('/api/')) {
    console.warn(`检测到API路径包含重复的'/api'前缀: ${formattedPath}`);
    // 移除重复的'/api'前缀
    formattedPath = formattedPath.replace('/api', '');
    console.warn(`已修正为: ${formattedPath}`);
  }
  
  return formattedPath;
}

/**
 * 获取完整的API URL
 * @param {string} path 相对API路径（不应包含/api前缀）
 * @returns {string} 完整的API URL
 */
export function getFullApiUrl(path) {
  const formattedPath = getApiPath(path);
  return `${systemConfig.apiBaseUrl}${formattedPath}`;
}

/**
 * API路径使用规则
 */
export const API_PATH_RULES = {
  // 正确的API路径格式
  CORRECT_FORMAT: "路径格式应为'/EntityName/action'，不要包含'/api'前缀",
  
  // 示例
  EXAMPLES: {
    CORRECT: [
      "/Asset/1",
      "/AssetType/export",
      "/Import/data"
    ],
    INCORRECT: [
      "/api/Asset/1", // 包含重复前缀
      "Asset/1"       // 缺少前导斜杠
    ]
  },
  
  // 路径检查
  checkPath(path) {
    if (!path) return false;
    
    // 检查是否以斜杠开头
    if (!path.startsWith('/')) {
      return {
        valid: false,
        message: "API路径应以'/'开头"
      };
    }
    
    // 检查是否包含'/api'前缀
    if (path.startsWith('/api/')) {
      return {
        valid: false,
        message: "API路径不应包含'/api'前缀，因为systemConfig.apiBaseUrl已经包含此前缀"
      };
    }
    
    return {
      valid: true,
      message: "API路径格式正确"
    };
  }
};

export default {
  getApiPath,
  getFullApiUrl,
  API_PATH_RULES
} 