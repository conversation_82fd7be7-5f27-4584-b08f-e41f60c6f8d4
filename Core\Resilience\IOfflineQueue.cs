// IT资产管理系统 - 离线队列接口
// 文件路径: /Core/Resilience/IOfflineQueue.cs
// 功能: 定义离线队列接口，用于管理离线操作的存储和执行

using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace ItAssetsSystem.Core.Resilience
{
    /// <summary>
    /// 离线操作接口
    /// </summary>
    public interface IOfflineOperation
    {
        /// <summary>
        /// 操作ID
        /// </summary>
        string Id { get; }
        
        /// <summary>
        /// 操作类型
        /// </summary>
        string OperationType { get; }
        
        /// <summary>
        /// 操作数据
        /// </summary>
        string OperationData { get; }
        
        /// <summary>
        /// 操作状态
        /// </summary>
        OperationStatus Status { get; }
        
        /// <summary>
        /// 创建时间
        /// </summary>
        DateTime CreatedAt { get; }
        
        /// <summary>
        /// 最后更新时间
        /// </summary>
        DateTime UpdatedAt { get; }
        
        /// <summary>
        /// 错误信息
        /// </summary>
        string ErrorMessage { get; }
        
        /// <summary>
        /// 重试次数
        /// </summary>
        int RetryCount { get; }
        
        /// <summary>
        /// 优先级（值越小优先级越高）
        /// </summary>
        int Priority { get; }
        
        /// <summary>
        /// 超时（毫秒），0表示永不超时
        /// </summary>
        int TimeoutMs { get; }
        
        /// <summary>
        /// 执行操作
        /// </summary>
        /// <param name="cancellationToken">取消标记</param>
        /// <returns>操作结果</returns>
        Task<bool> ExecuteAsync(CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 更新状态
        /// </summary>
        /// <param name="status">新状态</param>
        /// <param name="errorMessage">错误信息</param>
        void UpdateStatus(OperationStatus status, string errorMessage = null);
        
        /// <summary>
        /// 增加重试次数
        /// </summary>
        void IncrementRetryCount();
    }
    
    /// <summary>
    /// 离线队列选项
    /// </summary>
    public class OfflineQueueOptions
    {
        /// <summary>
        /// 存储路径
        /// </summary>
        public string StoragePath { get; set; } = "offline_operations";
        
        /// <summary>
        /// 自动保存间隔（毫秒）
        /// </summary>
        public int AutoSaveIntervalMs { get; set; } = 60000;
        
        /// <summary>
        /// 最大重试次数
        /// </summary>
        public int MaxRetryCount { get; set; } = 5;
        
        /// <summary>
        /// 重试延迟基准（毫秒）
        /// </summary>
        public int RetryDelayBaseMs { get; set; } = 1000;
        
        /// <summary>
        /// 默认操作超时（毫秒）
        /// </summary>
        public int DefaultOperationTimeoutMs { get; set; } = 30000;
        
        /// <summary>
        /// 并发处理数
        /// </summary>
        public int ConcurrentProcessingCount { get; set; } = 3;
        
        /// <summary>
        /// 网络重连后自动处理待处理操作
        /// </summary>
        public bool AutoProcessPendingOnReconnect { get; set; } = true;
    }
    
    /// <summary>
    /// 离线队列事件参数
    /// </summary>
    public class OfflineQueueEventArgs : EventArgs
    {
        /// <summary>
        /// 操作
        /// </summary>
        public IOfflineOperation Operation { get; }
        
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="operation">操作</param>
        public OfflineQueueEventArgs(IOfflineOperation operation)
        {
            Operation = operation ?? throw new ArgumentNullException(nameof(operation));
        }
    }
    
    /// <summary>
    /// 离线队列接口
    /// </summary>
    public interface IOfflineQueue
    {
        /// <summary>
        /// 是否启用
        /// </summary>
        bool IsEnabled { get; }
        
        /// <summary>
        /// 队列中等待处理的操作数
        /// </summary>
        int PendingCount { get; }
        
        /// <summary>
        /// 操作入队事件
        /// </summary>
        event EventHandler<OfflineQueueEventArgs> OperationEnqueued;
        
        /// <summary>
        /// 操作取消事件
        /// </summary>
        event EventHandler<OfflineQueueEventArgs> OperationCancelled;
        
        /// <summary>
        /// 操作成功事件
        /// </summary>
        event EventHandler<OfflineQueueEventArgs> OperationSucceeded;
        
        /// <summary>
        /// 操作失败事件
        /// </summary>
        event EventHandler<OfflineQueueEventArgs> OperationFailed;
        
        /// <summary>
        /// 启用队列
        /// </summary>
        void Enable();
        
        /// <summary>
        /// 禁用队列
        /// </summary>
        void Disable();
        
        /// <summary>
        /// 添加操作到队列
        /// </summary>
        /// <param name="operation">离线操作</param>
        /// <returns>操作ID</returns>
        Task<string> EnqueueAsync(IOfflineOperation operation);
        
        /// <summary>
        /// 取消操作
        /// </summary>
        /// <param name="operationId">操作ID</param>
        /// <returns>是否成功取消</returns>
        Task<bool> CancelOperationAsync(string operationId);
        
        /// <summary>
        /// 执行操作
        /// </summary>
        /// <param name="operationId">操作ID</param>
        /// <returns>是否执行成功</returns>
        Task<bool> ExecuteOperationAsync(string operationId);
        
        /// <summary>
        /// 获取待处理操作列表
        /// </summary>
        /// <returns>待处理操作列表</returns>
        Task<IEnumerable<IOfflineOperation>> GetPendingOperationsAsync();
        
        /// <summary>
        /// 获取失败操作列表
        /// </summary>
        /// <returns>失败操作列表</returns>
        Task<IEnumerable<IOfflineOperation>> GetFailedOperationsAsync();
        
        /// <summary>
        /// 重试失败操作
        /// </summary>
        /// <param name="operationId">操作ID，如果为null则重试所有失败操作</param>
        /// <returns>重试成功的操作数</returns>
        Task<int> RetryFailedOperationsAsync(string operationId = null);
        
        /// <summary>
        /// 清空队列
        /// </summary>
        /// <returns>是否成功清空</returns>
        Task<bool> ClearQueueAsync();
        
        /// <summary>
        /// 保存队列状态
        /// </summary>
        /// <returns>是否保存成功</returns>
        Task<bool> SaveQueueStateAsync();
        
        /// <summary>
        /// 加载队列状态
        /// </summary>
        /// <returns>是否加载成功</returns>
        Task<bool> LoadQueueStateAsync();
    }
} 