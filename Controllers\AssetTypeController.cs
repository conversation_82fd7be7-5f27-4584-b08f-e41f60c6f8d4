using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ItAssetsSystem.Models.Entities;
using ItAssetsSystem.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using System.Text;
using System.IO;
using OfficeOpenXml;
using OfficeOpenXml.Style;
using System.Drawing;

namespace ItAssetsSystem.Controllers
{
    /// <summary>
    /// 资产类型控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class AssetTypeController : ControllerBase
    {
        private readonly ILogger<AssetTypeController> _logger;
        private readonly AppDbContext _context;

        public AssetTypeController(ILogger<AssetTypeController> logger, AppDbContext context)
        {
            _logger = logger;
            _context = context;
        }

        /// <summary>
        /// 获取所有资产类型
        /// </summary>
        /// <returns>资产类型列表</returns>
        [HttpGet]
        public async Task<IActionResult> GetAll(
            [FromQuery] int? parentId = null, 
            [FromQuery] bool? isActive = null,
            [FromQuery] string keyword = null,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 10)
        {
            // 处理请求参数，兼容前端传递的params[page]格式
            if (Request.Query.ContainsKey("params[page]"))
            {
                if (int.TryParse(Request.Query["params[page]"], out int parsedPage))
                {
                    page = parsedPage;
                }
            }
            
            if (Request.Query.ContainsKey("params[pageSize]"))
            {
                if (int.TryParse(Request.Query["params[pageSize]"], out int parsedPageSize))
                {
                    pageSize = parsedPageSize;
                }
            }
            
            if (Request.Query.ContainsKey("params[keyword]"))
            {
                keyword = Request.Query["params[keyword]"].ToString();
            }
            
            _logger.LogInformation("获取资产类型列表，参数：" + 
                $"parentId={parentId}, isActive={isActive}, keyword={keyword}, page={page}, pageSize={pageSize}");
            
            try
            {
                // 使用简单查询获取基本资产类型列表
                var query = _context.AssetTypes.AsQueryable();

                // 应用筛选条件
                if (parentId.HasValue)
                {
                    query = query.Where(t => t.ParentId == parentId.Value);
                    _logger.LogDebug($"根据父类型ID筛选: {parentId.Value}");
                }
                
                // 注意：只有当isActive有值时才进行筛选
                if (isActive.HasValue)
                {
                    query = query.Where(t => t.IsActive == isActive.Value);
                    _logger.LogDebug($"根据激活状态筛选: {isActive.Value}");
                }
                else
                {
                    _logger.LogDebug("不筛选激活状态，返回所有资产类型");
                }
                
                if (!string.IsNullOrEmpty(keyword))
                {
                    keyword = keyword.Trim().ToLower();
                    query = query.Where(t => 
                        t.Name.ToLower().Contains(keyword) || 
                        t.Code.ToLower().Contains(keyword) ||
                        t.Description.ToLower().Contains(keyword)
                    );
                    _logger.LogDebug($"根据关键词筛选: {keyword}");
                }

                // 获取总记录数
                var totalCount = await query.CountAsync();
                
                _logger.LogInformation($"查询到资产类型总数：{totalCount}");
                
                // 分页查询获取ID列表
                var assetTypeIds = await query
                    .OrderBy(t => t.Id)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .Select(t => t.Id)
                    .ToListAsync();
                
                // 使用ID列表获取资产类型详情，不包含导航属性以避免循环引用
                var assetTypes = await _context.AssetTypes
                    .Where(t => assetTypeIds.Contains(t.Id))
                    .OrderBy(t => t.Id)
                    .Select(t => new
                    {
                        t.Id,
                        t.Name,
                        t.Code,
                        t.Description,
                        t.ParentId,
                        t.IsActive,
                        t.CreatedAt,
                        t.UpdatedAt
                    })
                    .ToListAsync();
                
                _logger.LogInformation($"返回当页资产类型数量：{assetTypes.Count}，资产类型ID列表：{string.Join(",", assetTypeIds)}");

                return Ok(new { 
                    success = true, 
                    data = new {
                        items = assetTypes,
                        total = totalCount,
                        page = page,
                        pageSize = pageSize,
                        pages = (int)Math.Ceiling((double)totalCount / pageSize)
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取资产类型列表出错");
                return StatusCode(500, new { success = false, message = "获取资产类型列表出错: " + ex.Message });
            }
        }

        /// <summary>
        /// 获取资产类型详情
        /// </summary>
        /// <param name="id">资产类型ID</param>
        /// <returns>资产类型详情</returns>
        [HttpGet("{id}")]
        public async Task<IActionResult> GetById(int id)
        {
            _logger.LogInformation($"获取资产类型详情，ID: {id}");
            try
            {
                var assetType = await _context.AssetTypes
                    .Include(t => t.Parent)
                    .FirstOrDefaultAsync(t => t.Id == id);

                if (assetType == null)
                {
                    return NotFound(new { success = false, message = $"未找到ID为{id}的资产类型" });
                }

                return Ok(new { success = true, data = assetType });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取资产类型ID {id} 出错");
                return StatusCode(500, new { success = false, message = $"获取资产类型ID {id} 出错: " + ex.Message });
            }
        }

        /// <summary>
        /// 创建资产类型
        /// </summary>
        /// <param name="assetType">资产类型信息</param>
        /// <returns>创建结果</returns>
        [HttpPost]
        public async Task<IActionResult> Create([FromBody] AssetType assetType)
        {
            _logger.LogInformation("创建资产类型");
            try
            {
                // 验证必填字段
                if (string.IsNullOrEmpty(assetType.Name))
                {
                    return BadRequest(new { success = false, message = "资产类型名称不能为空" });
                }

                // 如果指定了父类型，检查父类型是否存在
                if (assetType.ParentId.HasValue)
                {
                    var parentType = await _context.AssetTypes.FindAsync(assetType.ParentId.Value);
                    if (parentType == null)
                    {
                        return BadRequest(new { success = false, message = "父类型不存在" });
                    }
                }

                // 设置默认值
                assetType.IsActive = true;
                assetType.CreatedAt = DateTime.Now;
                assetType.UpdatedAt = DateTime.Now;

                // 保存资产类型
                _context.AssetTypes.Add(assetType);
                await _context.SaveChangesAsync();

                return Ok(new { success = true, data = assetType, message = "资产类型创建成功" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建资产类型出错");
                return StatusCode(500, new { success = false, message = "创建资产类型出错: " + ex.Message });
            }
        }

        /// <summary>
        /// 更新资产类型
        /// </summary>
        /// <param name="id">资产类型ID</param>
        /// <param name="assetType">资产类型信息</param>
        /// <returns>更新结果</returns>
        [HttpPut("{id}")]
        public async Task<IActionResult> Update(int id, [FromBody] AssetType assetType)
        {
            _logger.LogInformation($"更新资产类型，ID: {id}");
            try
            {
                // 验证必填字段
                if (string.IsNullOrEmpty(assetType.Name))
                {
                    return BadRequest(new { success = false, message = "资产类型名称不能为空" });
                }

                // 检查资产类型是否存在
                var existingType = await _context.AssetTypes.FindAsync(id);
                if (existingType == null)
                {
                    return NotFound(new { success = false, message = $"未找到ID为{id}的资产类型" });
                }

                // 如果指定了父类型，检查父类型是否存在
                if (assetType.ParentId.HasValue)
                {
                    var parentType = await _context.AssetTypes.FindAsync(assetType.ParentId.Value);
                    if (parentType == null)
                    {
                        return BadRequest(new { success = false, message = "父类型不存在" });
                    }
                }

                // 更新资产类型信息
                existingType.Name = assetType.Name;
                existingType.Code = assetType.Code;
                existingType.ParentId = assetType.ParentId;
                existingType.Description = assetType.Description;
                existingType.IsActive = assetType.IsActive;
                existingType.UpdatedAt = DateTime.Now;

                // 保存更改
                await _context.SaveChangesAsync();

                return Ok(new { success = true, data = existingType, message = "资产类型更新成功" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"更新资产类型ID {id} 出错");
                return StatusCode(500, new { success = false, message = $"更新资产类型ID {id} 出错: " + ex.Message });
            }
        }

        /// <summary>
        /// 删除资产类型
        /// </summary>
        /// <param name="id">资产类型ID</param>
        /// <returns>删除结果</returns>
        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(int id)
        {
            _logger.LogInformation($"删除资产类型，ID: {id}");
            try
            {
                // 检查资产类型是否存在
                var assetType = await _context.AssetTypes
                    .Include(t => t.Children)
                    .FirstOrDefaultAsync(t => t.Id == id);

                if (assetType == null)
                {
                    return NotFound(new { success = false, message = $"未找到ID为{id}的资产类型" });
                }

                // 检查是否有子类型
                if (assetType.Children?.Any() == true)
                {
                    return BadRequest(new { success = false, message = "该资产类型下有子类型，不能删除" });
                }

                // 检查是否有关联的资产 - 修改查询方式避免错误
                bool hasAssets = await _context.Assets.AnyAsync(a => a.AssetTypeId == id);
                if (hasAssets)
                {
                    return BadRequest(new { success = false, message = "该资产类型下有关联的资产，不能删除" });
                }

                // 删除资产类型
                _context.AssetTypes.Remove(assetType);
                await _context.SaveChangesAsync();

                return Ok(new { success = true, message = "资产类型删除成功" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"删除资产类型ID {id} 出错");
                return StatusCode(500, new { success = false, message = $"删除资产类型ID {id} 出错: " + ex.Message });
            }
        }

        /// <summary>
        /// 导出资产类型数据
        /// </summary>
        /// <returns>Excel文件</returns>
        [HttpGet("export")]
        public async Task<IActionResult> Export()
        {
            _logger.LogInformation("导出资产类型数据");
            try
            {
                // 查询所有资产类型
                var assetTypes = await _context.AssetTypes
                    .OrderBy(t => t.Id)
                    .ToListAsync();

                // 设置Excel包的许可证信息
                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
                
                using (var package = new ExcelPackage())
                {
                    // 创建工作表
                    var worksheet = package.Workbook.Worksheets.Add("资产类型列表");
                    
                    // 设置表头样式
                    var headerStyle = worksheet.Cells["A1:H1"].Style;
                    headerStyle.Font.Bold = true;
                    headerStyle.Font.Size = 12;
                    headerStyle.Fill.PatternType = ExcelFillStyle.Solid;
                    headerStyle.Fill.BackgroundColor.SetColor(Color.FromArgb(48, 84, 150));
                    headerStyle.Font.Color.SetColor(Color.White);
                    headerStyle.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                    headerStyle.VerticalAlignment = ExcelVerticalAlignment.Center;
                    headerStyle.Border.Top.Style = ExcelBorderStyle.Thin;
                    headerStyle.Border.Bottom.Style = ExcelBorderStyle.Thin;
                    headerStyle.Border.Left.Style = ExcelBorderStyle.Thin;
                    headerStyle.Border.Right.Style = ExcelBorderStyle.Thin;
                    
                    // 添加表头
                    worksheet.Cells[1, 1].Value = "类型ID";
                    worksheet.Cells[1, 2].Value = "类型编码";
                    worksheet.Cells[1, 3].Value = "类型名称";
                    worksheet.Cells[1, 4].Value = "上级类型ID";
                    worksheet.Cells[1, 5].Value = "描述";
                    worksheet.Cells[1, 6].Value = "是否激活";
                    worksheet.Cells[1, 7].Value = "创建时间";
                    worksheet.Cells[1, 8].Value = "更新时间";
                    
                    // 添加数据
                    int row = 2;
                    foreach (var type in assetTypes)
                    {
                        worksheet.Cells[row, 1].Value = type.Id;
                        worksheet.Cells[row, 2].Value = type.Code;
                        worksheet.Cells[row, 3].Value = type.Name;
                        worksheet.Cells[row, 4].Value = type.ParentId;
                        worksheet.Cells[row, 5].Value = type.Description;
                        worksheet.Cells[row, 6].Value = type.IsActive ? "是" : "否";
                        worksheet.Cells[row, 7].Value = type.CreatedAt;
                        worksheet.Cells[row, 8].Value = type.UpdatedAt;
                        
                        // 设置日期格式
                        worksheet.Cells[row, 7].Style.Numberformat.Format = "yyyy-mm-dd hh:mm:ss";
                        worksheet.Cells[row, 8].Style.Numberformat.Format = "yyyy-mm-dd hh:mm:ss";
                        
                        // 设置单元格样式
                        var cellStyle = worksheet.Cells[row, 1, row, 8].Style;
                        cellStyle.Border.Top.Style = ExcelBorderStyle.Thin;
                        cellStyle.Border.Bottom.Style = ExcelBorderStyle.Thin;
                        cellStyle.Border.Left.Style = ExcelBorderStyle.Thin;
                        cellStyle.Border.Right.Style = ExcelBorderStyle.Thin;
                        
                        // 设置交替行颜色
                        if (row % 2 == 0)
                        {
                            var rowStyle = worksheet.Cells[row, 1, row, 8].Style;
                            rowStyle.Fill.PatternType = ExcelFillStyle.Solid;
                            rowStyle.Fill.BackgroundColor.SetColor(Color.FromArgb(235, 241, 252));
                        }
                        
                        row++;
                    }
                    
                    // 调整列宽
                    worksheet.Column(1).Width = 10;  // ID
                    worksheet.Column(2).Width = 15;  // 编码
                    worksheet.Column(3).Width = 20;  // 名称
                    worksheet.Column(4).Width = 12;  // 上级ID
                    worksheet.Column(5).Width = 30;  // 描述
                    worksheet.Column(6).Width = 10;  // 是否激活
                    worksheet.Column(7).Width = 20;  // 创建时间
                    worksheet.Column(8).Width = 20;  // 更新时间
                    
                    // 冻结首行
                    worksheet.View.FreezePanes(2, 1);
                    
                    // 转换为字节数组
                    var excelData = package.GetAsByteArray();
                    
                    // 返回Excel文件
                    return File(excelData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", $"资产类型导出_{DateTime.Now:yyyyMMdd}.xlsx");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "导出资产类型数据出错");
                return StatusCode(500, new { success = false, message = $"导出资产类型数据出错: {ex.Message}" });
            }
        }

        /// <summary>
        /// 切换资产类型激活状态
        /// </summary>
        /// <param name="id">资产类型ID</param>
        /// <returns>操作结果</returns>
        [HttpPut("{id}/toggle-active")]
        public async Task<IActionResult> ToggleActive(int id)
        {
            _logger.LogInformation($"切换资产类型激活状态，ID: {id}");
            try
            {
                // 检查资产类型是否存在
                var assetType = await _context.AssetTypes.FindAsync(id);
                if (assetType == null)
                {
                    return NotFound(new { success = false, message = $"未找到ID为{id}的资产类型" });
                }

                // 切换激活状态
                assetType.IsActive = !assetType.IsActive;
                assetType.UpdatedAt = DateTime.Now;

                // 保存更改
                await _context.SaveChangesAsync();

                return Ok(new { 
                    success = true, 
                    data = assetType, 
                    message = $"资产类型状态已{(assetType.IsActive ? "激活" : "停用")}" 
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"切换资产类型ID {id} 激活状态出错");
                return StatusCode(500, new { success = false, message = $"切换资产类型ID {id} 激活状态出错: " + ex.Message });
            }
        }
    }
} 