// File: frontend/src/views/test/NotificationTestPage.vue
// Description: 通知系统测试页面

<template>
  <div class="notification-test-page">
    <el-card class="test-card">
      <template #header>
        <div class="card-header">
          <h2>通知系统测试面板</h2>
          <el-tag type="success" v-if="connectionStatus">连接状态: 正常</el-tag>
          <el-tag type="danger" v-else>连接状态: 断开</el-tag>
        </div>
      </template>

      <div class="status-section">
        <h3>连接信息</h3>
        <div class="status-info">
          <p><strong>SignalR状态:</strong> {{ connectionStatus ? '已连接' : '未连接' }}</p>
          <p><strong>服务器时间:</strong> {{ serverInfo.serverTime || '未知' }}</p>
          <p><strong>Hub路径:</strong> {{ serverInfo.hubPath || '/hubs/notification' }}</p>
          <p><strong>消息:</strong> {{ serverInfo.message || '未知' }}</p>
          <p><strong>用户ID:</strong> {{ currentUserId }}</p>
        </div>
      </div>

      <el-divider />

      <div class="test-section">
        <h3>测试发送通知</h3>
        <div class="test-controls">
          <el-button type="primary" @click="sendTestNotification" :loading="sending">
            发送测试通知
          </el-button>
          <el-button type="warning" @click="sendTestEvent" :loading="sending">
            发送测试事件
          </el-button>
          <el-button type="info" @click="checkConnectionStatus" :loading="checking">
            检查连接状态
          </el-button>
        </div>
      </div>

      <el-divider />

      <div class="log-section">
        <div class="log-header">
          <h3>事件日志</h3>
          <el-button type="default" size="small" @click="clearLogs">清空日志</el-button>
        </div>
        <div class="log-content">
          <div v-for="(log, index) in logs" :key="index" class="log-item" :class="log.type">
            <span class="log-time">{{ formatTime(log.time) }}</span>
            <span class="log-type">[{{ log.type.toUpperCase() }}]</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
          <div v-if="logs.length === 0" class="empty-log">
            暂无日志记录
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { notificationApi } from '@/api/notification'
import { notificationService } from '@/utils/notification-service'
import { useUserStore } from '@/stores/modules/user'
import { ElMessage } from 'element-plus'

// 状态变量
const connectionStatus = ref(false)
const serverInfo = ref({})
const logs = ref([])
const sending = ref(false)
const checking = ref(false)
const userStore = useUserStore()
const currentUserId = ref(0)

// 方法
const sendTestNotification = async () => {
  try {
    sending.value = true
    addLog('info', '发送测试通知请求...')
    const response = await notificationApi.sendTestNotification()
    addLog('success', `测试通知发送成功: ${JSON.stringify(response)}`)
    ElMessage.success('测试通知已发送')
  } catch (error) {
    addLog('error', `测试通知发送失败: ${error.message || '未知错误'}`)
    ElMessage.error('发送测试通知失败')
  } finally {
    sending.value = false
  }
}

const sendTestEvent = async () => {
  try {
    sending.value = true
    addLog('info', '发送测试事件请求...')
    const response = await notificationApi.sendTestEvent()
    addLog('success', `测试事件发送成功: ${JSON.stringify(response)}`)
    ElMessage.success('测试事件已发送')
  } catch (error) {
    addLog('error', `测试事件发送失败: ${error.message || '未知错误'}`)
    ElMessage.error('发送测试事件失败')
  } finally {
    sending.value = false
  }
}

const checkConnectionStatus = async () => {
  try {
    checking.value = true
    addLog('info', '检查连接状态...')
    const response = await notificationApi.getConnectionStatus()
    
    if (response && response.success) {
      serverInfo.value = response.data
      addLog('success', `连接状态检查成功: ${JSON.stringify(response.data)}`)
      
      // 使用notificationService手动测试连接
      try {
        await notificationService.sendTestNotification(currentUserId.value)
        addLog('info', '前端通知服务测试成功')
      } catch (error) {
        addLog('warning', `前端通知服务测试失败: ${error.message || '未知错误'}`)
      }
    } else {
      addLog('error', `连接状态检查失败: ${response?.message || '未知错误'}`)
    }
  } catch (error) {
    serverInfo.value = {}
    addLog('error', `连接状态检查出错: ${error.message || '未知错误'}`)
    ElMessage.error('获取连接状态失败')
  } finally {
    checking.value = false
  }
}

const reconnectSignalR = async () => {
  try {
    addLog('info', '尝试重新连接SignalR...')
    await notificationService.disconnect()
    await notificationService.initConnection(currentUserId.value)
    connectionStatus.value = true
    addLog('success', 'SignalR重新连接成功')
  } catch (error) {
    connectionStatus.value = false
    addLog('error', `SignalR重新连接失败: ${error.message || '未知错误'}`)
  }
}

const addLog = (type, message) => {
  logs.value.unshift({
    type,
    message,
    time: new Date()
  })
  
  // 最多保留100条日志
  if (logs.value.length > 100) {
    logs.value = logs.value.slice(0, 100)
  }
}

const clearLogs = () => {
  logs.value = []
  addLog('info', '日志已清空')
}

const formatTime = (time) => {
  return time.toLocaleTimeString('zh-CN', { hour12: false, hour: '2-digit', minute: '2-digit', second: '2-digit', fractionalSecondDigits: 3 })
}

// 定期检查SignalR连接状态
const startConnectionCheck = () => {
  const interval = setInterval(() => {
    // 使用服务的isConnected属性检查连接状态
    connectionStatus.value = notificationService.isConnected || false
    
    // 如果前面的检查出现问题，确保有一个合理的默认值
    if (connectionStatus.value === undefined) {
      connectionStatus.value = false
    }
  }, 5000)
  
  return interval
}

// 生命周期钩子
onMounted(async () => {
  addLog('info', '通知测试页面已加载')
  
  // 获取当前用户ID
  currentUserId.value = userStore.userId
  addLog('info', `当前用户ID: ${currentUserId.value}`)
  
  // 检查SignalR连接状态
  connectionStatus.value = notificationService.isConnected || false
  addLog('info', `当前SignalR连接状态: ${connectionStatus.value ? '已连接' : '未连接'}`)
  
  // 获取服务器连接信息
  await checkConnectionStatus()
  
  // 如果没有连接，尝试重新连接
  if (!connectionStatus.value && currentUserId.value) {
    await reconnectSignalR()
  }
  
  // 定期检查连接状态
  const checkInterval = startConnectionCheck()
  
  // 在组件卸载时清除定时器
  onUnmounted(() => {
    clearInterval(checkInterval)
  })
})
</script>

<style scoped>
.notification-test-page {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
}

.test-card {
  margin-bottom: 20px;
  border-radius: 8px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h2 {
  margin: 0;
  font-size: 18px;
}

.status-section,
.test-section,
.log-section {
  margin-bottom: 20px;
}

.status-info {
  background-color: #f5f7fa;
  padding: 10px 15px;
  border-radius: 4px;
  font-family: monospace;
}

.test-controls {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.log-content {
  background-color: #1e1e1e;
  color: #ddd;
  padding: 10px;
  border-radius: 4px;
  height: 300px;
  overflow-y: auto;
  font-family: monospace;
  font-size: 13px;
}

.log-item {
  padding: 4px 0;
  line-height: 1.4;
}

.log-time {
  color: #888;
  margin-right: 8px;
}

.log-type {
  margin-right: 8px;
  font-weight: bold;
}

.log-item.info .log-type {
  color: #409eff;
}

.log-item.success .log-type {
  color: #67c23a;
}

.log-item.warning .log-type {
  color: #e6a23c;
}

.log-item.error .log-type {
  color: #f56c6c;
}

.empty-log {
  text-align: center;
  color: #666;
  padding: 20px;
}
</style> 