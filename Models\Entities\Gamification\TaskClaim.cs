using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using ItAssetsSystem.Domain.Entities.Tasks;
using ItAssetsSystem.Domain.Entities;

namespace ItAssetsSystem.Models.Entities.Gamification
{
    /// <summary>
    /// 任务领取记录实体
    /// </summary>
    [Table("task_claims")]
    public class TaskClaim
    {
        /// <summary>
        /// 领取记录ID
        /// </summary>
        [Key]
        [Column("claim_id")]
        public long ClaimId { get; set; }

        /// <summary>
        /// 任务ID
        /// </summary>
        [Column("task_id")]
        public long TaskId { get; set; }

        /// <summary>
        /// 领取用户ID
        /// </summary>
        [Column("claimed_by")]
        public int ClaimedBy { get; set; }

        /// <summary>
        /// 班次ID
        /// </summary>
        [Column("shift_id")]
        public long ShiftId { get; set; }

        /// <summary>
        /// 领取时间
        /// </summary>
        [Column("claimed_at")]
        public DateTime ClaimedAt { get; set; }

        /// <summary>
        /// 领取日期
        /// </summary>
        [Column("claim_date")]
        public DateTime ClaimDate { get; set; }

        /// <summary>
        /// 领取状态
        /// </summary>
        [Column("claim_status")]
        [MaxLength(20)]
        public string ClaimStatus { get; set; } = "Claimed";

        /// <summary>
        /// 开始时间
        /// </summary>
        [Column("started_at")]
        public DateTime? StartedAt { get; set; }

        /// <summary>
        /// 完成时间
        /// </summary>
        [Column("completed_at")]
        public DateTime? CompletedAt { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [Column("notes")]
        [MaxLength(1000)]
        public string? Notes { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [Column("created_at")]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新时间
        /// </summary>
        [Column("updated_at")]
        public DateTime UpdatedAt { get; set; } = DateTime.Now;

        // 导航属性
        /// <summary>
        /// 关联的任务
        /// </summary>
        [ForeignKey("TaskId")]
        public virtual ItAssetsSystem.Domain.Entities.Tasks.Task Task { get; set; } = null!;

        /// <summary>
        /// 领取用户
        /// </summary>
        [ForeignKey("ClaimedBy")]
        public virtual User ClaimedByUser { get; set; } = null!;
    }

    /// <summary>
    /// 任务领取状态枚举
    /// </summary>
    public enum TaskClaimStatus
    {
        /// <summary>
        /// 已领取
        /// </summary>
        Claimed,
        
        /// <summary>
        /// 已开始
        /// </summary>
        Started,
        
        /// <summary>
        /// 已完成
        /// </summary>
        Completed,
        
        /// <summary>
        /// 已取消
        /// </summary>
        Cancelled
    }
}
