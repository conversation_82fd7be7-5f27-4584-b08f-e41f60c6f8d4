using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.SignalR;
using System;
using System.Threading;
using System.Threading.Tasks;
using ItAssetsSystem.Application.Features.Statistics.Services;
using ItAssetsSystem.Core.Hubs;

namespace ItAssetsSystem.Infrastructure.Services
{
    /// <summary>
    /// 工作汇总更新后台服务 (基于现有系统架构)
    /// </summary>
    public class WorkSummaryUpdateService : BackgroundService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<WorkSummaryUpdateService> _logger;

        public WorkSummaryUpdateService(
            IServiceProvider serviceProvider,
            ILogger<WorkSummaryUpdateService> logger)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("工作汇总更新服务启动");

            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    var now = DateTime.Now;

                    // 每日凌晨2点更新昨日统计
                    if (now.Hour == 2 && now.Minute < 5)
                    {
                        await UpdateDailyStatistics();
                        await UpdateTaskClaimStatistics(); // 同时更新任务领取统计
                    }

                    // 每周一凌晨3点更新上周统计
                    if (now.DayOfWeek == DayOfWeek.Monday && now.Hour == 3 && now.Minute < 5)
                    {
                        await UpdateWeeklyStatistics();
                    }

                    // 每月1日凌晨4点更新上月统计
                    if (now.Day == 1 && now.Hour == 4 && now.Minute < 5)
                    {
                        await UpdateMonthlyStatistics();
                    }

                    // 每30分钟推送实时更新 (利用现有NotificationHub)
                    if (now.Minute % 30 == 0 && now.Second < 30)
                    {
                        await PushRealtimeUpdates();
                    }

                    // 每15分钟更新任务领取统计 (提高实时性)
                    if (now.Minute % 15 == 0 && now.Second < 30)
                    {
                        await UpdateTaskClaimStatistics();
                    }

                    // 每分钟检查一次
                    await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "工作汇总更新服务执行失败");
                    await Task.Delay(TimeSpan.FromMinutes(5), stoppingToken);
                }
            }
        }

        private async Task UpdateDailyStatistics()
        {
            using var scope = _serviceProvider.CreateScope();
            var workSummaryService = scope.ServiceProvider.GetRequiredService<IWorkSummaryService>();

            try
            {
                var yesterday = DateTime.Today.AddDays(-1);
                await workSummaryService.UpdateWorkSummaryAsync("daily", yesterday);
                _logger.LogInformation("每日统计更新完成: {Date}", yesterday);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "每日统计更新失败");
            }
        }

        private async Task UpdateWeeklyStatistics()
        {
            using var scope = _serviceProvider.CreateScope();
            var workSummaryService = scope.ServiceProvider.GetRequiredService<IWorkSummaryService>();

            try
            {
                var lastWeek = DateTime.Today.AddDays(-7);
                await workSummaryService.UpdateWorkSummaryAsync("weekly", lastWeek);
                _logger.LogInformation("每周统计更新完成: {Date}", lastWeek);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "每周统计更新失败");
            }
        }

        private async Task UpdateMonthlyStatistics()
        {
            using var scope = _serviceProvider.CreateScope();
            var workSummaryService = scope.ServiceProvider.GetRequiredService<IWorkSummaryService>();

            try
            {
                var lastMonth = DateTime.Today.AddMonths(-1);
                await workSummaryService.UpdateWorkSummaryAsync("monthly", lastMonth);
                _logger.LogInformation("每月统计更新完成: {Date}", lastMonth);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "每月统计更新失败");
            }
        }

        private async Task PushRealtimeUpdates()
        {
            using var scope = _serviceProvider.CreateScope();

            try
            {
                var workSummaryService = scope.ServiceProvider.GetRequiredService<IWorkSummaryService>();
                var hubContext = scope.ServiceProvider.GetRequiredService<IHubContext<NotificationHub>>();

                // 获取最新排行榜数据
                var leaderboard = await workSummaryService.GetEnhancedLeaderboardAsync(10);

                // 推送到所有连接的客户端 (利用现有NotificationHub)
                await hubContext.Clients.All.SendAsync("WorkSummaryUpdated", new
                {
                    Type = "leaderboard_update",
                    Data = leaderboard,
                    UpdatedAt = DateTime.Now,
                    Source = "scheduled_update"
                });

                _logger.LogDebug("实时排行榜更新推送完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "推送实时更新失败");
            }
        }

        /// <summary>
        /// 更新任务领取统计 - 新增功能
        /// </summary>
        private async Task UpdateTaskClaimStatistics()
        {
            using var scope = _serviceProvider.CreateScope();
            var workSummaryService = scope.ServiceProvider.GetRequiredService<IWorkSummaryService>();

            try
            {
                await workSummaryService.UpdateTaskClaimStatisticsAsync();
                _logger.LogInformation("任务领取统计更新完成");

                // 推送任务领取统计更新通知
                var hubContext = scope.ServiceProvider.GetRequiredService<IHubContext<NotificationHub>>();
                await hubContext.Clients.All.SendAsync("TaskClaimStatisticsUpdated", new
                {
                    Type = "task_claim_update",
                    UpdatedAt = DateTime.Now,
                    Message = "任务领取统计已更新"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "任务领取统计更新失败");
            }
        }
    }
}
