<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    title="返厂维修汇总单"
    width="800px"
    :close-on-click-modal="false"
    class="repair-summary-dialog"
  >
    <div class="summary-document" ref="summaryRef">
      <!-- 文档头部 -->
      <div class="document-header">
        <div class="company-info">
          <h2>设备返厂维修汇总单</h2>
          <div class="header-info">
            <div class="info-row">
              <span class="label">单据编号：</span>
              <span class="value">{{ orderData.orderCode }}</span>
            </div>
            <div class="info-row">
              <span class="label">创建日期：</span>
              <span class="value">{{ formatDate(orderData.createdAt) }}</span>
            </div>
            <div class="info-row">
              <span class="label">维修单号：</span>
              <span class="value">{{ orderData.orderCode }}</span>
            </div>
          </div>
        </div>
        <div class="priority-badge" :class="getPriorityClass(orderData.priority)">
          {{ getPriorityText(orderData.priority) }}
        </div>
      </div>

      <!-- 基本信息 -->
      <div class="section">
        <h3 class="section-title">基本信息</h3>
        <div class="info-grid">
          <div class="info-item">
            <span class="label">维修标题：</span>
            <span class="value">{{ orderData.title }}</span>
          </div>
          <div class="info-item">
            <span class="label">维修供应商：</span>
            <span class="value">{{ orderData.supplierName }}</span>
          </div>
          <div class="info-item">
            <span class="label">预估费用：</span>
            <span class="value">{{ formatCurrency(orderData.estimatedCost) }}</span>
          </div>
          <div class="info-item">
            <span class="label">预估工期：</span>
            <span class="value">{{ orderData.estimatedDays }}天</span>
          </div>
          <div class="info-item full-width">
            <span class="label">维修描述：</span>
            <span class="value">{{ orderData.description }}</span>
          </div>
        </div>
      </div>

      <!-- 返厂物品明细 -->
      <div class="section">
        <h3 class="section-title">返厂物品明细</h3>
        <table class="items-table">
          <thead>
            <tr>
              <th width="60">序号</th>
              <th width="120">备件编号</th>
              <th>备件名称</th>
              <th width="80">数量</th>
              <th width="120">序列号</th>
              <th>故障描述</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(item, index) in orderData.repairItems" :key="index">
              <td class="text-center">{{ index + 1 }}</td>
              <td>{{ item.partCode }}</td>
              <td>{{ item.partName }}</td>
              <td class="text-center">{{ item.quantity }}</td>
              <td>{{ item.serialNumber || '-' }}</td>
              <td>{{ item.faultDescription || '-' }}</td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 汇总统计 -->
      <div class="section">
        <h3 class="section-title">汇总统计</h3>
        <div class="summary-stats">
          <div class="stat-item">
            <span class="stat-label">返厂物品种类：</span>
            <span class="stat-value">{{ orderData.repairItems.length }}种</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">返厂物品总数：</span>
            <span class="stat-value">{{ totalQuantity }}件</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">预估维修费用：</span>
            <span class="stat-value">{{ formatCurrency(orderData.estimatedCost) }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">预估完成时间：</span>
            <span class="stat-value">{{ estimatedCompletionDate }}</span>
          </div>
        </div>
      </div>

      <!-- 备注信息 -->
      <div v-if="orderData.notes" class="section">
        <h3 class="section-title">备注信息</h3>
        <div class="notes-content">
          {{ orderData.notes }}
        </div>
      </div>

      <!-- 签名区域 -->
      <div class="signature-section">
        <div class="signature-item">
          <div class="signature-line"></div>
          <div class="signature-label">申请人签名</div>
          <div class="signature-date">日期：_______</div>
        </div>
        <div class="signature-item">
          <div class="signature-line"></div>
          <div class="signature-label">部门主管签名</div>
          <div class="signature-date">日期：_______</div>
        </div>
        <div class="signature-item">
          <div class="signature-line"></div>
          <div class="signature-label">设备管理员签名</div>
          <div class="signature-date">日期：_______</div>
        </div>
      </div>

      <!-- 页脚 -->
      <div class="document-footer">
        <div class="footer-info">
          <span>本单据一式三份，申请部门、设备管理部门、财务部门各执一份</span>
        </div>
        <div class="page-number">
          第 1 页 共 1 页
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="$emit('update:visible', false)">关闭</el-button>
        <el-button type="primary" @click="printSummary">打印汇总单</el-button>
        <el-button type="success" @click="exportPDF">导出PDF</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed, ref } from 'vue'
import { ElMessage } from 'element-plus'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  orderData: {
    type: Object,
    required: true
  }
})

// Emits
const emit = defineEmits(['update:visible'])

// Refs
const summaryRef = ref()

// 计算属性
const totalQuantity = computed(() => {
  return props.orderData.repairItems?.reduce((total, item) => total + (item.quantity || 0), 0) || 0
})

const estimatedCompletionDate = computed(() => {
  if (!props.orderData.estimatedDays) return '-'
  const date = new Date()
  date.setDate(date.getDate() + props.orderData.estimatedDays)
  return formatDate(date)
})

// 方法
const formatDate = (date) => {
  if (!date) return '-'
  const d = new Date(date)
  return d.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })
}

const formatCurrency = (amount) => {
  if (!amount) return '-'
  return `¥${amount.toFixed(2)}`
}

const getPriorityText = (priority) => {
  const priorityMap = {
    1: '紧急',
    2: '高',
    3: '中',
    4: '低'
  }
  return priorityMap[priority] || '中'
}

const getPriorityClass = (priority) => {
  const classMap = {
    1: 'urgent',
    2: 'high',
    3: 'medium',
    4: 'low'
  }
  return classMap[priority] || 'medium'
}

const printSummary = () => {
  const printContent = summaryRef.value.innerHTML
  const printWindow = window.open('', '_blank')
  printWindow.document.write(`
    <html>
      <head>
        <title>返厂维修汇总单</title>
        <style>
          body { font-family: 'Microsoft YaHei', sans-serif; margin: 20px; }
          .summary-document { max-width: 800px; margin: 0 auto; }
          .document-header { border-bottom: 2px solid #333; padding-bottom: 20px; margin-bottom: 20px; }
          .section { margin-bottom: 20px; }
          .section-title { font-size: 16px; font-weight: bold; margin-bottom: 10px; border-bottom: 1px solid #ddd; padding-bottom: 5px; }
          .items-table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
          .items-table th, .items-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
          .items-table th { background-color: #f5f5f5; font-weight: bold; }
          .text-center { text-align: center; }
          .signature-section { display: flex; justify-content: space-between; margin-top: 40px; }
          .signature-item { text-align: center; }
          .signature-line { width: 120px; height: 1px; border-bottom: 1px solid #333; margin-bottom: 5px; }
          @media print { body { margin: 0; } }
        </style>
      </head>
      <body>${printContent}</body>
    </html>
  `)
  printWindow.document.close()
  printWindow.print()
}

const exportPDF = () => {
  ElMessage.info('PDF导出功能开发中...')
  // TODO: 实现PDF导出功能
}
</script>

<style scoped>
.repair-summary-dialog :deep(.el-dialog__body) {
  padding: 20px;
}

.summary-document {
  font-family: 'Microsoft YaHei', sans-serif;
  line-height: 1.6;
  color: #333;
}

.document-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  border-bottom: 2px solid #333;
  padding-bottom: 20px;
  margin-bottom: 20px;
}

.company-info h2 {
  margin: 0 0 15px 0;
  font-size: 24px;
  font-weight: bold;
  text-align: center;
}

.header-info {
  display: flex;
  gap: 30px;
}

.info-row {
  display: flex;
  align-items: center;
}

.priority-badge {
  padding: 8px 16px;
  border-radius: 4px;
  font-weight: bold;
  font-size: 14px;
}

.priority-badge.urgent {
  background-color: #ff4d4f;
  color: white;
}

.priority-badge.high {
  background-color: #ff7a45;
  color: white;
}

.priority-badge.medium {
  background-color: #1890ff;
  color: white;
}

.priority-badge.low {
  background-color: #52c41a;
  color: white;
}

.section {
  margin-bottom: 25px;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 15px;
  padding-bottom: 8px;
  border-bottom: 1px solid #ddd;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.info-item {
  display: flex;
  align-items: center;
}

.info-item.full-width {
  grid-column: 1 / -1;
  align-items: flex-start;
}

.info-item .label {
  font-weight: bold;
  min-width: 100px;
  color: #666;
}

.info-item .value {
  color: #333;
}

.items-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 20px;
}

.items-table th,
.items-table td {
  border: 1px solid #ddd;
  padding: 12px 8px;
  text-align: left;
}

.items-table th {
  background-color: #f5f5f5;
  font-weight: bold;
  text-align: center;
}

.text-center {
  text-align: center;
}

.summary-stats {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
  background-color: #f9f9f9;
  padding: 20px;
  border-radius: 4px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-label {
  font-weight: bold;
  color: #666;
}

.stat-value {
  font-weight: bold;
  color: #1890ff;
  font-size: 16px;
}

.notes-content {
  background-color: #f9f9f9;
  padding: 15px;
  border-radius: 4px;
  border-left: 4px solid #1890ff;
}

.signature-section {
  display: flex;
  justify-content: space-between;
  margin-top: 40px;
  margin-bottom: 30px;
}

.signature-item {
  text-align: center;
  flex: 1;
}

.signature-line {
  width: 120px;
  height: 1px;
  border-bottom: 1px solid #333;
  margin: 0 auto 8px auto;
}

.signature-label {
  font-weight: bold;
  margin-bottom: 5px;
}

.signature-date {
  font-size: 12px;
  color: #666;
}

.document-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1px solid #ddd;
  padding-top: 15px;
  font-size: 12px;
  color: #666;
}

.dialog-footer {
  text-align: right;
}
</style>
