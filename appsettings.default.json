{"ConnectionStrings": {"DefaultConnection": "server=localhost;port=3306;database=itassets;user=root;password=password;"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "AllowedHosts": "*", "Database": {"EnableSeedData": true}, "NetworkMonitor": {"CheckIntervalMs": 30000, "TimeoutMs": 5000, "RetryCount": 3, "CheckUrl": "https://www.baidu.com", "AutoStartMonitoring": true}, "OfflineQueue": {"StoragePath": "Data/OfflineOperations", "AutoSaveIntervalMs": 60000, "MaxRetryCount": 3, "RetryDelayBaseMs": 2000, "DefaultOperationTimeoutMs": 30000, "ConcurrentProcessingCount": 2, "AutoProcessPendingOnReconnect": true}, "Backup": {"EnableAutoBackup": true, "BackupFrequencyHours": 24, "MaxBackupsToKeep": 5, "BackupStoragePath": "Data/Backups"}, "Import": {"ImportFilesPath": "Data/Import", "TemplatesPath": "Data/Templates", "MaxFileSize": 5242880, "AllowedFormats": ["csv", "xlsx", "json"]}, "Export": {"ExportFilesPath": "Data/Export", "MaxExportRecords": 10000, "EnableEncryption": false, "DefaultPageSize": 500}, "UI": {"Theme": "light", "Language": "zh-CN", "PageSize": 20, "EnableAnimations": true, "ShowHelpTips": true, "DefaultDashboard": "assets"}, "Security": {"TokenExpirationMinutes": 60, "RequireHttps": true, "PasswordMinLength": 8, "PasswordRequireDigit": true, "PasswordRequireLowercase": true, "PasswordRequireUppercase": true, "PasswordRequireNonAlphanumeric": true, "MaxFailedLoginAttempts": 5, "LockoutDurationMinutes": 15}}