/**
 * 航空航天级IT资产管理系统 - 页面标题组件
 * 文件路径: src/components/PageHeader.vue
 * 功能描述: 页面顶部标题区域，包含标题、描述和操作按钮
 */

<template>
  <div class="page-header">
    <div class="header-left">
      <h2 class="title">{{ title }}</h2>
      <p v-if="description" class="description">{{ description }}</p>
    </div>
    <div class="header-right">
      <slot name="actions"></slot>
    </div>
  </div>
</template>

<script setup>
defineProps({
  // 页面标题
  title: {
    type: String,
    required: true
  },
  // 页面描述（可选）
  description: {
    type: String,
    default: ''
  }
})
</script>

<style lang="scss" scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--el-border-color-light);
  
  .header-left {
    flex: 1;
    
    .title {
      margin: 0;
      font-size: 24px;
      font-weight: 500;
      color: var(--el-text-color-primary);
      line-height: 32px;
    }
    
    .description {
      margin: 8px 0 0;
      font-size: 14px;
      color: var(--el-text-color-secondary);
      line-height: 1.5;
    }
  }
  
  .header-right {
    display: flex;
    align-items: center;
    gap: 8px;
  }
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    
    .header-left {
      margin-bottom: 16px;
    }
    
    .header-right {
      width: 100%;
      justify-content: flex-start;
    }
  }
}
</style> 