using System;
using System.Collections.Generic;
using System.Reflection;
using ItAssetsSystem.Core.Plugins;

namespace ItAssetsSystem.Core.Abstractions
{
    /// <summary>
    /// 插件管理器接口
    /// </summary>
    public interface IPluginManager
    {
        /// <summary>
        /// 初始化插件管理器
        /// </summary>
        void Initialize();

        /// <summary>
        /// 加载所有插件
        /// </summary>
        void LoadAllPlugins();

        /// <summary>
        /// 加载指定插件
        /// </summary>
        /// <param name="pluginPath">插件路径</param>
        /// <returns>插件信息</returns>
        PluginInfo LoadPlugin(string pluginPath);

        /// <summary>
        /// 卸载指定插件
        /// </summary>
        /// <param name="pluginId">插件ID</param>
        /// <returns>是否成功</returns>
        bool UnloadPlugin(string pluginId);

        /// <summary>
        /// 启动所有插件
        /// </summary>
        void StartAllPlugins();

        /// <summary>
        /// 启动指定插件
        /// </summary>
        /// <param name="pluginId">插件ID</param>
        /// <returns>是否成功</returns>
        bool StartPlugin(string pluginId);

        /// <summary>
        /// 停止所有插件
        /// </summary>
        void StopAllPlugins();

        /// <summary>
        /// 停止指定插件
        /// </summary>
        /// <param name="pluginId">插件ID</param>
        /// <returns>是否成功</returns>
        bool StopPlugin(string pluginId);

        /// <summary>
        /// 获取所有插件
        /// </summary>
        /// <returns>插件列表</returns>
        List<PluginInfo> GetAllPlugins();

        /// <summary>
        /// 获取指定插件
        /// </summary>
        /// <param name="pluginId">插件ID</param>
        /// <returns>插件信息</returns>
        PluginInfo GetPlugin(string pluginId);
    }
} 