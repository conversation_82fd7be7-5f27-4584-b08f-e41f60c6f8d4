// File: Api/V2/Controllers/SparePartTypesController.cs
// Description: 备品备件类型API控制器

using System.Threading.Tasks;
using ItAssetsSystem.Application.Common.Dtos;
using ItAssetsSystem.Application.Features.SpareParts.Dtos;
using ItAssetsSystem.Application.Features.SpareParts.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace ItAssetsSystem.Api.V2.Controllers
{
    /// <summary>
    /// 备品备件类型控制器
    /// </summary>
    [ApiController]
    // [Route("api/v2/spare-part-types")] // Old route
    [Route("api/v2/spareparttype")] // New route to match frontend request
    // [Authorize]  // 临时注释掉授权要求，方便测试
    public class SparePartTypesController : ControllerBase
    {
        private readonly ISparePartTypeService _typeService;
        private readonly ILogger<SparePartTypesController> _logger;

        /// <summary>
        /// 构造函数
        /// </summary>
        public SparePartTypesController(ISparePartTypeService typeService, ILogger<SparePartTypesController> logger)
        {
            _typeService = typeService;
            _logger = logger;
        }
        
        /// <summary>
        /// 获取所有备品备件类型（平铺列表）
        /// </summary>
        /// <returns>类型列表</returns>
        [HttpGet]
        public async Task<IActionResult> GetAllTypes()
        {
            try
            {
                _logger.LogInformation("开始获取备品备件类型列表");
                var result = await _typeService.GetAllTypesAsync();
                _logger.LogInformation("成功获取备品备件类型列表，数量: {Count}", result?.Count ?? 0);
                return Ok(ApiResponse<object>.CreateSuccess(result));
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "获取备品备件类型列表时发生错误");
                return Ok(ApiResponse<object>.CreateFail("获取备品备件类型列表失败"));
            }
        }
        
        /// <summary>
        /// 获取类型树
        /// </summary>
        /// <returns>树形结构的类型列表</returns>
        [HttpGet("tree")]
        public async Task<IActionResult> GetTypeTree()
        {
            try
            {
                _logger.LogInformation("开始获取备品备件类型树");
                var result = await _typeService.GetTypeTreeAsync();
                _logger.LogInformation("成功获取备品备件类型树，数量: {Count}", result?.Count ?? 0);
                return Ok(ApiResponse<object>.CreateSuccess(result));
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "获取备品备件类型树时发生错误");
                return Ok(ApiResponse<object>.CreateFail("获取备品备件类型树失败"));
            }
        }
        
        /// <summary>
        /// 根据ID获取类型详情
        /// </summary>
        /// <param name="id">类型ID</param>
        /// <returns>类型DTO</returns>
        [HttpGet("{id}")]
        public async Task<IActionResult> GetTypeById(long id)
        {
            try
            {
                _logger.LogInformation("开始获取备品备件类型详情，ID: {Id}", id);
                var result = await _typeService.GetTypeByIdAsync(id);
                if (result == null)
                {
                    _logger.LogWarning("未找到ID为 {Id} 的备品备件类型", id);
                    return Ok(ApiResponse<object>.CreateFail("未找到指定的备品备件类型"));
                }
                _logger.LogInformation("成功获取备品备件类型详情，ID: {Id}", id);
                return Ok(ApiResponse<object>.CreateSuccess(result));
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "获取备品备件类型详情时发生错误，ID: {Id}", id);
                return Ok(ApiResponse<object>.CreateFail("获取备品备件类型详情失败"));
            }
        }
        
        /// <summary>
        /// 创建类型
        /// </summary>
        /// <param name="request">创建请求</param>
        /// <returns>创建的类型DTO</returns>
        [HttpPost]
        public async Task<IActionResult> CreateType([FromBody] CreateSparePartTypeRequest request)
        {
            var result = await _typeService.CreateTypeAsync(request);
            return Ok(ApiResponse<object>.CreateSuccess(result, "类型创建成功"));
        }
        
        /// <summary>
        /// 更新类型
        /// </summary>
        /// <param name="id">类型ID</param>
        /// <param name="request">更新请求</param>
        /// <returns>更新后的类型DTO</returns>
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateType(long id, [FromBody] CreateSparePartTypeRequest request)
        {
            var result = await _typeService.UpdateTypeAsync(id, request);
            if (result == null)
            {
                return Ok(ApiResponse<object>.CreateFail($"未找到ID为{id}的类型"));
            }
            return Ok(ApiResponse<object>.CreateSuccess(result, "类型更新成功"));
        }
        
        /// <summary>
        /// 删除类型
        /// </summary>
        /// <param name="id">类型ID</param>
        /// <returns>成功消息</returns>
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteType(long id)
        {
            var result = await _typeService.DeleteTypeAsync(id);
            if (!result)
            {
                return Ok(ApiResponse<object>.CreateFail($"未找到ID为{id}的类型或删除失败"));
            }
            return Ok(ApiResponse<object>.CreateSuccess(null, "类型删除成功"));
        }
    }
} 