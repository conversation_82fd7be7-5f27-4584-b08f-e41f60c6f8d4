using Microsoft.AspNetCore.SignalR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Logging;
using System.Threading.Tasks;
using ItAssetsSystem.Application.Features.Gamification.Services;

namespace ItAssetsSystem.Application.Features.Gamification.Hubs
{
    [Authorize]
    public class GamificationHub : Hub
    {
        private readonly ILogger<GamificationHub> _logger;
        private readonly IModernGamificationService _gamificationService;

        public GamificationHub(
            ILogger<GamificationHub> logger,
            IModernGamificationService gamificationService)
        {
            _logger = logger;
            _gamificationService = gamificationService;
        }

        public async Task JoinStatisticsGroup()
        {
            await Groups.AddToGroupAsync(Context.ConnectionId, "Statistics");
            _logger.LogDebug("用户加入统计组: {ConnectionId}", Context.ConnectionId);
        }

        public async Task JoinUserGroup(int userId)
        {
            await Groups.AddToGroupAsync(Context.ConnectionId, $"User_{userId}");
            _logger.LogDebug("用户加入个人组: {UserId}, {ConnectionId}", userId, Context.ConnectionId);
        }

        public async Task RequestLeaderboardUpdate(string type)
        {
            try
            {
                var leaderboard = await _gamificationService.GetLeaderboardAsync(type, 20);
                await Clients.Caller.SendAsync("LeaderboardData", new
                {
                    Type = type,
                    Data = leaderboard,
                    RequestedAt = System.DateTime.Now
                });
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "请求排行榜数据失败: Type={Type}", type);
                await Clients.Caller.SendAsync("Error", "获取排行榜数据失败");
            }
        }

        public async Task RequestWorkSummary(string periodType)
        {
            try
            {
                var summary = await _gamificationService.GetWorkSummaryAsync(periodType, System.DateTime.Today, 50);
                await Clients.Caller.SendAsync("WorkSummaryData", new
                {
                    PeriodType = periodType,
                    Data = summary,
                    RequestedAt = System.DateTime.Now
                });
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "请求工作汇总失败: PeriodType={PeriodType}", periodType);
                await Clients.Caller.SendAsync("Error", "获取工作汇总失败");
            }
        }

        public override async Task OnConnectedAsync()
        {
            _logger.LogDebug("用户连接: {ConnectionId}", Context.ConnectionId);
            await base.OnConnectedAsync();
        }

        public override async Task OnDisconnectedAsync(System.Exception exception)
        {
            _logger.LogDebug("用户断开连接: {ConnectionId}, 异常: {Exception}", 
                Context.ConnectionId, exception?.Message);
            await base.OnDisconnectedAsync(exception);
        }
    }
}
