/**
 * 航空航天级IT资产管理系统 - 仪表盘API
 * 文件路径: src/api/dashboard.js
 * 功能描述: 提供仪表盘数据相关的API服务
 */

import request from '@/utils/request'

// 仪表盘API基础路径
const baseUrl = '/dashboard'

export default {
  /**
   * 获取资产概览数据
   * @returns {Promise}
   */
  getAssetOverview() {
    return request.get(`${baseUrl}/asset-overview`)
  },
  
  /**
   * 获取资产分类统计
   * @param {Object} params - 查询参数
   * @returns {Promise}
   */
  getAssetCategoryStats(params) {
    return request.get(`${baseUrl}/asset-category-stats`, params)
  },
  
  /**
   * 获取资产状态统计
   * @param {Object} params - 查询参数
   * @returns {Promise}
   */
  getAssetStatusStats(params) {
    return request.get(`${baseUrl}/asset-status-stats`, params)
  },
  
  /**
   * 获取资产位置分布
   * @param {Object} params - 查询参数
   * @returns {Promise}
   */
  getAssetLocationDistribution(params) {
    return request.get(`${baseUrl}/asset-location-distribution`, params)
  },
  
  /**
   * 获取故障趋势数据
   * @param {Object} params - 查询参数，包含时间范围
   * @returns {Promise}
   */
  getFaultTrend(params) {
    return request.get(`${baseUrl}/fault-trend`, params)
  },
  
  /**
   * 获取故障类型统计
   * @param {Object} params - 查询参数
   * @returns {Promise}
   */
  getFaultTypeStats(params) {
    return request.get(`${baseUrl}/fault-type-stats`, params)
  },
  
  /**
   * 获取故障处理时间统计
   * @param {Object} params - 查询参数
   * @returns {Promise}
   */
  getFaultProcessingTimeStats(params) {
    return request.get(`${baseUrl}/fault-processing-time-stats`, params)
  },
  
  /**
   * 获取任务完成率统计
   * @param {Object} params - 查询参数，包含时间范围
   * @returns {Promise}
   */
  getTaskCompletionRate(params) {
    return request.get(`${baseUrl}/task-completion-rate`, params)
  },
  
  /**
   * 获取任务类型统计
   * @param {Object} params - 查询参数
   * @returns {Promise}
   */
  getTaskTypeStats(params) {
    return request.get(`${baseUrl}/task-type-stats`, params)
  },
  
  /**
   * 获取采购趋势数据
   * @param {Object} params - 查询参数，包含时间范围
   * @returns {Promise}
   */
  getPurchaseTrend(params) {
    return request.get(`${baseUrl}/purchase-trend`, params)
  },
  
  /**
   * 获取采购金额统计
   * @param {Object} params - 查询参数，包含时间范围
   * @returns {Promise}
   */
  getPurchaseAmountStats(params) {
    return request.get(`${baseUrl}/purchase-amount-stats`, params)
  },
  
  /**
   * 获取采购品类统计
   * @param {Object} params - 查询参数
   * @returns {Promise}
   */
  getPurchaseCategoryStats(params) {
    return request.get(`${baseUrl}/purchase-category-stats`, params)
  },
  
  /**
   * 获取资产价值统计
   * @param {Object} params - 查询参数
   * @returns {Promise}
   */
  getAssetValueStats(params) {
    return request.get(`${baseUrl}/asset-value-stats`, params)
  },
  
  /**
   * 获取资产折旧趋势
   * @param {Object} params - 查询参数，包含时间范围
   * @returns {Promise}
   */
  getAssetDepreciationTrend(params) {
    return request.get(`${baseUrl}/asset-depreciation-trend`, params)
  },
  
  /**
   * 获取待处理事项统计
   * @returns {Promise}
   */
  getPendingItems() {
    return request.get(`${baseUrl}/pending-items`)
  },
  
  /**
   * 获取近期活动数据
   * @param {Object} params - 查询参数，包含数量限制
   * @returns {Promise}
   */
  getRecentActivities(params) {
    return request.get(`${baseUrl}/recent-activities`, params)
  },
  
  /**
   * 获取系统性能指标
   * @returns {Promise}
   */
  getSystemPerformance() {
    return request.get(`${baseUrl}/system-performance`)
  },
  
  /**
   * 获取用户活跃度统计
   * @param {Object} params - 查询参数，包含时间范围
   * @returns {Promise}
   */
  getUserActivityStats(params) {
    return request.get(`${baseUrl}/user-activity-stats`, params)
  },
  
  /**
   * 获取自定义仪表盘配置
   * @returns {Promise}
   */
  getDashboardConfig() {
    return request.get(`${baseUrl}/config`)
  },
  
  /**
   * 保存自定义仪表盘配置
   * @param {Object} data - 仪表盘配置数据
   * @returns {Promise}
   */
  saveDashboardConfig(data) {
    return request.post(`${baseUrl}/config`, data)
  },
  
  /**
   * 重置仪表盘配置为默认
   * @returns {Promise}
   */
  resetDashboardConfig() {
    return request.put(`${baseUrl}/config/reset`)
  }
} 