using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using ItAssetsSystem.Infrastructure.Data;
using ItAssetsSystem.Models.Gamification;
using ItAssetsSystem.Models.Entities.Gamification;
using ItAssetsSystem.Services.Interfaces;

namespace ItAssetsSystem.Application.Features.Gamification.Services
{
    public interface ILevelService
    {
        Task<bool> CheckAndProcessLevelUpAsync(int userId);
        Task<UserLevelInfo> GetUserLevelInfoAsync(int userId);
        Task<List<UserLevel>> GetAllLevelsAsync();
        Task<LevelUpResult> ProcessLevelUpAsync(int userId, int newLevel);
    }

    public class LevelService : ILevelService
    {
        private readonly AppDbContext _context;
        private readonly ILogger<LevelService> _logger;
        private readonly IUniversalGamificationService _gamificationService;

        public LevelService(
            AppDbContext context, 
            ILogger<LevelService> logger,
            IUniversalGamificationService gamificationService)
        {
            _context = context;
            _logger = logger;
            _gamificationService = gamificationService;
        }

        public async Task<bool> CheckAndProcessLevelUpAsync(int userId)
        {
            try
            {
                _logger.LogInformation($"检查用户 {userId} 是否可以升级");

                var userStats = await _context.GamificationUserStats
                    .FirstOrDefaultAsync(us => us.CoreUserId == userId);

                if (userStats == null)
                {
                    _logger.LogWarning($"用户 {userId} 的统计数据不存在");
                    return false;
                }

                var currentLevel = userStats.CurrentLevel;
                var currentXp = userStats.CurrentXP;
                var leveledUp = false;

                // 检查是否可以连续升级
                while (true)
                {
                    var nextLevel = currentLevel + 1;
                    var nextLevelConfig = await _context.UserLevels
                        .FirstOrDefaultAsync(ul => ul.Level == nextLevel);

                    if (nextLevelConfig == null)
                    {
                        _logger.LogInformation($"用户 {userId} 已达到最高等级 {currentLevel}");
                        break;
                    }

                    if (currentXp >= nextLevelConfig.RequiredXp)
                    {
                        // 可以升级
                        var levelUpResult = await ProcessLevelUpAsync(userId, nextLevel);
                        if (levelUpResult.Success)
                        {
                            currentLevel = nextLevel;
                            leveledUp = true;
                            _logger.LogInformation($"用户 {userId} 升级到等级 {nextLevel}");

                            // 升级时有概率获得道具
                            if (new Random().NextDouble() < 0.3) // 30%概率
                            {
                                await GrantRandomItemAsync(userId, "LEVEL_UP");
                            }
                        }
                        else
                        {
                            break;
                        }
                    }
                    else
                    {
                        // 更新等级进度
                        await UpdateLevelProgressAsync(userId, currentLevel, currentXp);
                        break;
                    }
                }

                return leveledUp;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"检查用户 {userId} 升级时发生错误");
                return false;
            }
        }

        public async Task<LevelUpResult> ProcessLevelUpAsync(int userId, int newLevel)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var userStats = await _context.GamificationUserStats
                    .FirstOrDefaultAsync(us => us.CoreUserId == userId);

                var levelConfig = await _context.UserLevels
                    .FirstOrDefaultAsync(ul => ul.Level == newLevel);

                if (userStats == null || levelConfig == null)
                {
                    return new LevelUpResult { Success = false, Message = "用户或等级配置不存在" };
                }

                var oldLevel = userStats.CurrentLevel;

                // 更新用户等级和奖励
                userStats.CurrentLevel = newLevel;
                userStats.CoinsBalance = userStats.CoinsBalance + levelConfig.RewardCoins;
                userStats.DiamondsBalance = userStats.DiamondsBalance + levelConfig.RewardDiamonds;

                // 记录升级历史
                var levelHistory = new UserLevelHistory
                {
                    UserId = userId,
                    OldLevel = oldLevel,
                    NewLevel = newLevel,
                    LevelUpTime = DateTime.Now,
                    XpAtLevelup = userStats.CurrentXP,
                    RewardsGranted = System.Text.Json.JsonSerializer.Serialize(new
                    {
                        coins = levelConfig.RewardCoins,
                        diamonds = levelConfig.RewardDiamonds,
                        features = levelConfig.UnlockFeatures
                    })
                };

                _context.UserLevelHistory.Add(levelHistory);

                // 记录游戏化日志
                await _gamificationService.TriggerBehaviorRewardAsync(
                    userId, 
                    "LEVEL_UP", 
                    newLevel,
                    context: new { oldLevel, newLevel, rewards = new { coins = levelConfig.RewardCoins, diamonds = levelConfig.RewardDiamonds } },
                    description: $"升级到 {levelConfig.Name} (等级 {newLevel})"
                );

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();

                return new LevelUpResult
                {
                    Success = true,
                    OldLevel = oldLevel,
                    NewLevel = newLevel,
                    LevelName = levelConfig.Name,
                    RewardCoins = levelConfig.RewardCoins,
                    RewardDiamonds = levelConfig.RewardDiamonds,
                    UnlockedFeatures = levelConfig.UnlockFeatures
                };
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, $"处理用户 {userId} 升级到等级 {newLevel} 时发生错误");
                return new LevelUpResult { Success = false, Message = ex.Message };
            }
        }

        public async Task<UserLevelInfo> GetUserLevelInfoAsync(int userId)
        {
            var userStats = await _context.GamificationUserStats
                .FirstOrDefaultAsync(us => us.CoreUserId == userId);

            if (userStats == null)
            {
                return null;
            }

            var currentLevel = userStats.CurrentLevel;
            var currentLevelConfig = await _context.UserLevels
                .FirstOrDefaultAsync(ul => ul.Level == currentLevel);

            var nextLevelConfig = await _context.UserLevels
                .FirstOrDefaultAsync(ul => ul.Level == currentLevel + 1);

            var currentXp = userStats.CurrentXP;
            var currentLevelXp = currentLevelConfig?.RequiredXp ?? 0;
            var nextLevelXp = nextLevelConfig?.RequiredXp ?? currentXp;

            var progress = nextLevelConfig != null 
                ? Math.Round(((decimal)(currentXp - currentLevelXp) / (nextLevelXp - currentLevelXp)) * 100, 2)
                : 100m;

            return new UserLevelInfo
            {
                UserId = userId,
                CurrentLevel = currentLevel,
                CurrentLevelName = currentLevelConfig?.Name ?? "未知等级",
                CurrentLevelColor = currentLevelConfig?.Color ?? "#666666",
                CurrentXp = currentXp,
                CurrentLevelXp = currentLevelXp,
                NextLevelXp = nextLevelXp,
                Progress = Math.Max(0, Math.Min(100, progress)),
                NextLevelName = nextLevelConfig?.Name,
                UnlockedFeatures = currentLevelConfig?.UnlockFeatures,
                IsMaxLevel = nextLevelConfig == null
            };
        }

        public async Task<List<UserLevel>> GetAllLevelsAsync()
        {
            return await _context.UserLevels
                .OrderBy(ul => ul.Level)
                .ToListAsync();
        }

        private async Task UpdateLevelProgressAsync(int userId, int currentLevel, int currentXp)
        {
            var currentLevelConfig = await _context.UserLevels
                .FirstOrDefaultAsync(ul => ul.Level == currentLevel);

            var nextLevelConfig = await _context.UserLevels
                .FirstOrDefaultAsync(ul => ul.Level == currentLevel + 1);

            if (currentLevelConfig != null && nextLevelConfig != null)
            {
                var currentLevelXp = currentLevelConfig.RequiredXp;
                var nextLevelXp = nextLevelConfig.RequiredXp;
                var progress = Math.Round(((decimal)(currentXp - currentLevelXp) / (nextLevelXp - currentLevelXp)) * 100, 2);

                var userStats = await _context.GamificationUserStats
                    .FirstOrDefaultAsync(us => us.CoreUserId == userId);

                if (userStats != null)
                {
                    // 注意：GamificationUserStats没有LevelProgress字段，这里可能需要添加或使用其他方式
                    // userStats.LevelProgress = Math.Max(0, Math.Min(100, progress));
                    await _context.SaveChangesAsync();
                }
            }
        }

        private async Task GrantRandomItemAsync(int userId, string source)
        {
            try
            {
                await _context.Database.ExecuteSqlRawAsync(
                    "CALL GrantRandomItem({0}, {1})", userId, source);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"为用户 {userId} 发放随机道具时发生错误");
            }
        }
    }

    // 数据传输对象
    public class UserLevelInfo
    {
        public int UserId { get; set; }
        public int CurrentLevel { get; set; }
        public string CurrentLevelName { get; set; }
        public string CurrentLevelColor { get; set; }
        public int CurrentXp { get; set; }
        public int CurrentLevelXp { get; set; }
        public int NextLevelXp { get; set; }
        public decimal Progress { get; set; }
        public string NextLevelName { get; set; }
        public string UnlockedFeatures { get; set; }
        public bool IsMaxLevel { get; set; }
    }

    public class LevelUpResult
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public int OldLevel { get; set; }
        public int NewLevel { get; set; }
        public string LevelName { get; set; }
        public int RewardCoins { get; set; }
        public int RewardDiamonds { get; set; }
        public string UnlockedFeatures { get; set; }
    }
}
