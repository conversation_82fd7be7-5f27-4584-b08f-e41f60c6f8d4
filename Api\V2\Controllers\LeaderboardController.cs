// File: Api/V2/Controllers/LeaderboardController.cs
// Description: 排行榜API控制器

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Logging;
using ItAssetsSystem.Services.Interfaces;
using ItAssetsSystem.Application.Common.Dtos;

namespace ItAssetsSystem.Api.V2.Controllers
{
    /// <summary>
    /// 排行榜API控制器
    /// </summary>
    [ApiController]
    [Route("api/v2/leaderboard")]
    [Authorize]
    public class LeaderboardController : ControllerBase
    {
        private readonly ILeaderboardService _leaderboardService;
        private readonly ILogger<LeaderboardController> _logger;

        public LeaderboardController(
            ILeaderboardService leaderboardService,
            ILogger<LeaderboardController> logger)
        {
            _leaderboardService = leaderboardService;
            _logger = logger;
        }

        /// <summary>
        /// 获取排行榜
        /// </summary>
        /// <param name="metric">排行指标</param>
        /// <param name="period">统计周期</param>
        /// <param name="limit">返回数量</param>
        /// <returns>排行榜数据</returns>
        [HttpGet]
        public async Task<IActionResult> GetLeaderboard(
            [FromQuery] string metric = LeaderboardMetrics.POINTS,
            [FromQuery] string period = StatisticsPeriods.ALLTIME,
            [FromQuery] int limit = 50)
        {
            try
            {
                var leaderboard = await _leaderboardService.GetLeaderboardAsync(metric, period, limit);
                
                return Ok(ApiResponse<List<LeaderboardEntry>>.CreateSuccess(leaderboard, "获取排行榜成功"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取排行榜失败: metric={Metric}, period={Period}", metric, period);
                return StatusCode(500, ApiResponse<object>.CreateFail("获取排行榜失败"));
            }
        }

        /// <summary>
        /// 获取用户排名
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="metric">排行指标</param>
        /// <param name="period">统计周期</param>
        /// <returns>用户排名信息</returns>
        [HttpGet("user/{userId:int}")]
        public async Task<IActionResult> GetUserRank(
            int userId,
            [FromQuery] string metric = LeaderboardMetrics.POINTS,
            [FromQuery] string period = StatisticsPeriods.ALLTIME)
        {
            try
            {
                var userRank = await _leaderboardService.GetUserRankAsync(userId, metric, period);
                
                if (userRank == null)
                {
                    return NotFound(ApiResponse<object>.CreateFail("用户排名信息不存在"));
                }

                return Ok(ApiResponse<UserRankInfo>.CreateSuccess(userRank, "获取用户排名成功"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户排名失败: userId={UserId}, metric={Metric}", userId, metric);
                return StatusCode(500, ApiResponse<object>.CreateFail("获取用户排名失败"));
            }
        }

        /// <summary>
        /// 获取排行榜指标列表
        /// </summary>
        /// <returns>可用的排行榜指标</returns>
        [HttpGet("metrics")]
        public IActionResult GetMetrics()
        {
            var metrics = new[]
            {
                new { Code = LeaderboardMetrics.POINTS, Name = "积分排行", Description = "根据总积分排名" },
                new { Code = LeaderboardMetrics.COINS, Name = "金币排行", Description = "根据金币数量排名" },
                new { Code = LeaderboardMetrics.DIAMONDS, Name = "钻石排行", Description = "根据钻石数量排名" },
                new { Code = LeaderboardMetrics.TASKS_CREATED, Name = "任务创建排行", Description = "根据创建任务数量排名" },
                new { Code = LeaderboardMetrics.TASKS_COMPLETED, Name = "任务完成排行", Description = "根据完成任务数量排名" },
                new { Code = LeaderboardMetrics.TASKS_CLAIMED, Name = "任务领取排行", Description = "根据领取任务数量排名" },
                new { Code = LeaderboardMetrics.FAULTS_RECORDED, Name = "故障登记排行", Description = "根据登记故障数量排名" },
                new { Code = LeaderboardMetrics.MAINTENANCE_CREATED, Name = "维修单创建排行", Description = "根据创建维修单数量排名" },
                new { Code = LeaderboardMetrics.ASSETS_UPDATED, Name = "资产更新排行", Description = "根据更新资产数量排名" }
            };

            return Ok(ApiResponse<object>.CreateSuccess(metrics, "获取排行榜指标成功"));
        }

        /// <summary>
        /// 获取统计周期列表
        /// </summary>
        /// <returns>可用的统计周期</returns>
        [HttpGet("periods")]
        public IActionResult GetPeriods()
        {
            var periods = new[]
            {
                new { Code = StatisticsPeriods.DAILY, Name = "今日", Description = "今日统计" },
                new { Code = StatisticsPeriods.WEEKLY, Name = "本周", Description = "本周统计" },
                new { Code = StatisticsPeriods.MONTHLY, Name = "本月", Description = "本月统计" },
                new { Code = StatisticsPeriods.QUARTERLY, Name = "本季度", Description = "本季度统计" },
                new { Code = StatisticsPeriods.YEARLY, Name = "本年", Description = "本年统计" },
                new { Code = StatisticsPeriods.ALLTIME, Name = "总榜", Description = "历史总榜" }
            };

            return Ok(ApiResponse<object>.CreateSuccess(periods, "获取统计周期成功"));
        }

        /// <summary>
        /// 刷新排行榜缓存
        /// </summary>
        /// <returns>刷新结果</returns>
        [HttpPost("refresh")]
        public async Task<IActionResult> RefreshCache()
        {
            try
            {
                await _leaderboardService.RefreshLeaderboardCacheAsync();
                return Ok(ApiResponse<object>.CreateSuccess(null, "排行榜缓存刷新成功"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "刷新排行榜缓存失败");
                return StatusCode(500, ApiResponse<object>.CreateFail("刷新排行榜缓存失败"));
            }
        }

        /// <summary>
        /// 获取多维度排行榜概览
        /// </summary>
        /// <param name="period">统计周期</param>
        /// <param name="limit">每个排行榜返回数量</param>
        /// <returns>多维度排行榜数据</returns>
        [HttpGet("overview")]
        public async Task<IActionResult> GetLeaderboardOverview(
            [FromQuery] string period = StatisticsPeriods.ALLTIME,
            [FromQuery] int limit = 10)
        {
            try
            {
                var overview = new
                {
                    PointsLeaderboard = await _leaderboardService.GetLeaderboardAsync(LeaderboardMetrics.POINTS, period, limit),
                    TasksCompletedLeaderboard = await _leaderboardService.GetLeaderboardAsync(LeaderboardMetrics.TASKS_COMPLETED, period, limit),
                    TasksCreatedLeaderboard = await _leaderboardService.GetLeaderboardAsync(LeaderboardMetrics.TASKS_CREATED, period, limit),
                    FaultsRecordedLeaderboard = await _leaderboardService.GetLeaderboardAsync(LeaderboardMetrics.FAULTS_RECORDED, period, limit),
                    MaintenanceCreatedLeaderboard = await _leaderboardService.GetLeaderboardAsync(LeaderboardMetrics.MAINTENANCE_CREATED, period, limit)
                };

                return Ok(ApiResponse<object>.CreateSuccess(overview, "获取排行榜概览成功"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取排行榜概览失败: period={Period}", period);
                return StatusCode(500, ApiResponse<object>.CreateFail("获取排行榜概览失败"));
            }
        }
    }
}
