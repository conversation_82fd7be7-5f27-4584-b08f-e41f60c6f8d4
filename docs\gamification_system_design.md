# IT资产管理系统 - 游戏化汇总统计和排行榜系统设计文档

## 📋 文档概述

**文档版本**: v1.0  
**创建日期**: 2024-06-24  
**适用系统**: IT资产管理系统 (百人规模)  
**设计目标**: 实现人员工作汇总统计和多维度游戏化排行榜系统  

---

## 🎯 系统设计目标

### 核心需求
1. **人员工作汇总统计**: 按人员显示各模块具体操作数量
2. **多维度排行榜**: 积分榜、金币榜、钻石榜、道具榜、生产力榜
3. **时间维度分析**: 支持日/周/月统计对比
4. **游戏化激励**: 通过排名和成就激发工作积极性

### 业务模块覆盖
- **📋 任务模块**: 新建任务、领取任务、完成任务、评论任务
- **🏢 资产模块**: 新建资产、更新资产、删除资产
- **🔧 故障模块**: 登记故障、返厂维修
- **🛒 采购模块**: 新建采购单、更新采购进度
- **📦 备件模块**: 备件入库、备件出库、新增备件

---

## 🏗️ 数据库架构设计

### 1. 核心表结构

#### 1.1 人员工作汇总统计表
```sql
-- =====================================================
-- 人员工作汇总统计表 (核心表)
-- =====================================================

CREATE TABLE user_work_summary (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    user_name VARCHAR(50) NOT NULL,
    department_name VARCHAR(50),
    period_type ENUM('daily', 'weekly', 'monthly') NOT NULL,
    period_date DATE NOT NULL COMMENT '周期起始日期',
    
    -- 📋 任务模块统计
    tasks_created INT DEFAULT 0 COMMENT '新建任务数',
    tasks_claimed INT DEFAULT 0 COMMENT '领取任务数',
    tasks_completed INT DEFAULT 0 COMMENT '完成任务数',
    tasks_commented INT DEFAULT 0 COMMENT '评论任务数',
    
    -- 🏢 资产模块统计
    assets_created INT DEFAULT 0 COMMENT '新建资产数',
    assets_updated INT DEFAULT 0 COMMENT '更新资产数',
    assets_deleted INT DEFAULT 0 COMMENT '删除资产数',
    
    -- 🔧 故障模块统计
    faults_reported INT DEFAULT 0 COMMENT '登记故障数',
    faults_repaired INT DEFAULT 0 COMMENT '维修故障数',
    
    -- 🛒 采购模块统计
    procurements_created INT DEFAULT 0 COMMENT '新建采购单数',
    procurements_updated INT DEFAULT 0 COMMENT '更新采购进度数',
    
    -- 📦 备件模块统计
    parts_in INT DEFAULT 0 COMMENT '备件入库数',
    parts_out INT DEFAULT 0 COMMENT '备件出库数',
    parts_added INT DEFAULT 0 COMMENT '新增备件数',
    
    -- 🎮 游戏化数据汇总
    total_points_earned INT DEFAULT 0 COMMENT '总积分',
    total_coins_earned INT DEFAULT 0 COMMENT '总金币',
    total_diamonds_earned INT DEFAULT 0 COMMENT '总钻石',
    total_xp_earned INT DEFAULT 0 COMMENT '总经验',
    
    -- 📈 排名信息
    points_rank INT DEFAULT 0 COMMENT '积分排名',
    coins_rank INT DEFAULT 0 COMMENT '金币排名',
    diamonds_rank INT DEFAULT 0 COMMENT '钻石排名',
    productivity_rank INT DEFAULT 0 COMMENT '生产力排名',
    
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_user_period (user_id, period_type, period_date),
    INDEX idx_period (period_type, period_date),
    INDEX idx_user_period (user_id, period_type),
    INDEX idx_points_rank (points_rank),
    INDEX idx_productivity_rank (productivity_rank)
) ENGINE=InnoDB COMMENT='用户工作汇总统计表';
```

#### 1.2 游戏化规则配置表
```sql
-- =====================================================
-- 游戏化规则配置表 (灵活配置积分规则)
-- =====================================================

CREATE TABLE simple_gamification_rules (
    behavior_code VARCHAR(50) PRIMARY KEY,
    behavior_name VARCHAR(100) NOT NULL,
    module_name VARCHAR(50) NOT NULL,
    points INT DEFAULT 0 COMMENT '基础积分',
    coins INT DEFAULT 0 COMMENT '基础金币',
    xp INT DEFAULT 0 COMMENT '基础经验',
    is_active BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB COMMENT='游戏化规则配置表';

-- 初始化行为积分规则
INSERT INTO simple_gamification_rules (behavior_code, behavior_name, module_name, points, coins, xp) VALUES
('TASK_CREATE', '新建任务', 'Task', 10, 5, 15),
('TASK_CLAIM', '领取任务', 'Task', 5, 2, 8),
('TASK_COMPLETE', '完成任务', 'Task', 20, 10, 30),
('TASK_COMMENT', '评论任务', 'Task', 3, 1, 5),
('ASSET_CREATE', '新建资产', 'Asset', 15, 8, 20),
('ASSET_UPDATE', '更新资产', 'Asset', 8, 3, 12),
('ASSET_DELETE', '删除资产', 'Asset', 5, 2, 8),
('FAULT_REPORT', '登记故障', 'Fault', 12, 6, 18),
('FAULT_REPAIR', '返厂维修', 'Fault', 10, 5, 15),
('PROCUREMENT_CREATE', '新建采购单', 'Procurement', 18, 10, 25),
('PROCUREMENT_UPDATE', '更新采购进度', 'Procurement', 8, 4, 12),
('INVENTORY_IN', '备件入库', 'Inventory', 10, 5, 15),
('INVENTORY_OUT', '备件出库', 'Inventory', 8, 3, 12),
('INVENTORY_ADD', '新增备件', 'Inventory', 12, 6, 18);
```

### 2. 多维度排行榜视图

#### 2.1 积分排行榜
```sql
-- 🏆 积分排行榜
CREATE VIEW leaderboard_points AS
SELECT 
    ROW_NUMBER() OVER (ORDER BY us.PointsBalance DESC) as rank_no,
    u.id as user_id,
    u.name as user_name,
    d.name as department_name,
    us.PointsBalance as total_points,
    us.current_level,
    ul.name as level_name,
    ul.color as level_color,
    
    -- 本周积分
    COALESCE(uws.total_points_earned, 0) as weekly_points,
    
    -- 工作统计
    COALESCE(uws.tasks_completed, 0) as weekly_tasks,
    COALESCE(uws.assets_created + uws.assets_updated, 0) as weekly_assets,
    COALESCE(uws.faults_reported, 0) as weekly_faults,
    
    '🏆 积分榜' as board_type
FROM users u
JOIN user_stats us ON u.id = us.UserId
LEFT JOIN user_levels ul ON us.current_level = ul.level
LEFT JOIN departments d ON u.department_id = d.id
LEFT JOIN user_work_summary uws ON u.id = uws.user_id 
    AND uws.period_type = 'weekly' 
    AND uws.period_date = DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY)
WHERE u.IsDeleted = 0
ORDER BY us.PointsBalance DESC;
```

#### 2.2 金币排行榜
```sql
-- 💰 金币排行榜  
CREATE VIEW leaderboard_coins AS
SELECT 
    ROW_NUMBER() OVER (ORDER BY us.CoinsBalance DESC) as rank_no,
    u.id as user_id,
    u.name as user_name,
    d.name as department_name,
    us.CoinsBalance as total_coins,
    us.current_level,
    
    -- 本周金币
    COALESCE(uws.total_coins_earned, 0) as weekly_coins,
    
    -- 主要贡献模块
    CASE 
        WHEN uws.tasks_completed >= GREATEST(uws.assets_created, uws.faults_reported, uws.procurements_created) THEN '任务专家'
        WHEN uws.assets_created >= GREATEST(uws.tasks_completed, uws.faults_reported, uws.procurements_created) THEN '资产能手'
        WHEN uws.faults_reported >= GREATEST(uws.tasks_completed, uws.assets_created, uws.procurements_created) THEN '维修达人'
        ELSE '全能选手'
    END as specialty,
    
    '💰 金币榜' as board_type
FROM users u
JOIN user_stats us ON u.id = us.UserId
LEFT JOIN departments d ON u.department_id = d.id
LEFT JOIN user_work_summary uws ON u.id = uws.user_id 
    AND uws.period_type = 'weekly' 
    AND uws.period_date = DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY)
WHERE u.IsDeleted = 0
ORDER BY us.CoinsBalance DESC;
```

#### 2.3 钻石排行榜
```sql
-- 💎 钻石排行榜
CREATE VIEW leaderboard_diamonds AS
SELECT 
    ROW_NUMBER() OVER (ORDER BY us.DiamondsBalance DESC) as rank_no,
    u.id as user_id,
    u.name as user_name,
    d.name as department_name,
    us.DiamondsBalance as total_diamonds,
    us.current_level,
    
    -- 本周钻石
    COALESCE(uws.total_diamonds_earned, 0) as weekly_diamonds,
    
    -- 钻石获得主要来源
    CASE 
        WHEN us.current_level >= 8 THEN '高等级奖励'
        WHEN us.TasksCompletedCount >= 100 THEN '任务达成'
        WHEN us.PointsBalance >= 2000 THEN '积分突破'
        ELSE '稳步成长'
    END as diamond_source,
    
    '💎 钻石榜' as board_type
FROM users u
JOIN user_stats us ON u.id = us.UserId
LEFT JOIN departments d ON u.department_id = d.id
LEFT JOIN user_work_summary uws ON u.id = uws.user_id 
    AND uws.period_type = 'weekly' 
    AND uws.period_date = DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY)
WHERE u.IsDeleted = 0
ORDER BY us.DiamondsBalance DESC;
```

#### 2.4 道具排行榜
```sql
-- 🎒 道具排行榜
CREATE VIEW leaderboard_items AS
SELECT 
    ROW_NUMBER() OVER (ORDER BY item_stats.total_items DESC) as rank_no,
    u.id as user_id,
    u.name as user_name,
    d.name as department_name,
    item_stats.total_items,
    item_stats.rare_items,
    item_stats.epic_items, 
    item_stats.legendary_items,
    
    -- 道具价值评分 (传说=100, 史诗=20, 稀有=5, 普通=1)
    (item_stats.legendary_items * 100 + 
     item_stats.epic_items * 20 + 
     item_stats.rare_items * 5 + 
     item_stats.common_items * 1) as item_score,
     
    -- 最新获得的道具
    latest_item.item_name as latest_item,
    latest_item.rarity as latest_rarity,
    
    '🎒 道具榜' as board_type
FROM users u
LEFT JOIN departments d ON u.department_id = d.id
JOIN (
    SELECT 
        ui.user_id,
        COUNT(*) as total_items,
        SUM(CASE WHEN gi.rarity = 'Common' THEN 1 ELSE 0 END) as common_items,
        SUM(CASE WHEN gi.rarity = 'Rare' THEN 1 ELSE 0 END) as rare_items,
        SUM(CASE WHEN gi.rarity = 'Epic' THEN 1 ELSE 0 END) as epic_items,
        SUM(CASE WHEN gi.rarity = 'Legendary' THEN 1 ELSE 0 END) as legendary_items
    FROM user_items ui
    JOIN gamification_items gi ON ui.item_id = gi.item_id
    GROUP BY ui.user_id
) item_stats ON u.id = item_stats.user_id
LEFT JOIN (
    SELECT DISTINCT
        ui.user_id,
        FIRST_VALUE(gi.name) OVER (PARTITION BY ui.user_id ORDER BY ui.obtained_at DESC) as item_name,
        FIRST_VALUE(gi.rarity) OVER (PARTITION BY ui.user_id ORDER BY ui.obtained_at DESC) as rarity
    FROM user_items ui
    JOIN gamification_items gi ON ui.item_id = gi.item_id
) latest_item ON u.id = latest_item.user_id
WHERE u.IsDeleted = 0
ORDER BY item_score DESC, item_stats.total_items DESC;
```

---

## 🔄 核心业务逻辑

### 1. 统计更新存储过程

#### 1.1 主要汇总更新过程
```sql
-- =====================================================
-- 人员工作汇总统计更新存储过程
-- =====================================================

DELIMITER //

CREATE PROCEDURE UpdateUserWorkSummary(
    IN p_period_type VARCHAR(10), -- 'daily', 'weekly', 'monthly'
    IN p_target_date DATE
)
BEGIN
    DECLARE v_start_date DATE;
    DECLARE v_end_date DATE;
    
    -- 计算统计周期的开始和结束日期
    IF p_period_type = 'daily' THEN
        SET v_start_date = p_target_date;
        SET v_end_date = p_target_date + INTERVAL 1 DAY;
    ELSEIF p_period_type = 'weekly' THEN
        SET v_start_date = p_target_date - INTERVAL WEEKDAY(p_target_date) DAY;  -- 周一
        SET v_end_date = v_start_date + INTERVAL 7 DAY;
    ELSEIF p_period_type = 'monthly' THEN
        SET v_start_date = DATE_FORMAT(p_target_date, '%Y-%m-01');  -- 月初
        SET v_end_date = LAST_DAY(p_target_date) + INTERVAL 1 DAY;
    END IF;
    
    -- 插入或更新汇总数据
    INSERT INTO user_work_summary (
        user_id, user_name, department_name, period_type, period_date,
        tasks_created, tasks_claimed, tasks_completed, tasks_commented,
        assets_created, assets_updated, assets_deleted,
        faults_reported, faults_repaired,
        procurements_created, procurements_updated,
        parts_in, parts_out, parts_added,
        total_points_earned, total_coins_earned, total_diamonds_earned, total_xp_earned
    )
    SELECT 
        u.id as user_id,
        u.name as user_name,
        d.name as department_name,
        p_period_type as period_type,
        v_start_date as period_date,
        
        -- 📋 任务模块统计
        SUM(CASE WHEN gl.BehaviorCode = 'TASK_CREATE' THEN 1 ELSE 0 END) as tasks_created,
        SUM(CASE WHEN gl.BehaviorCode = 'TASK_CLAIM' THEN 1 ELSE 0 END) as tasks_claimed,
        SUM(CASE WHEN gl.BehaviorCode = 'TASK_COMPLETE' THEN 1 ELSE 0 END) as tasks_completed,
        SUM(CASE WHEN gl.BehaviorCode = 'TASK_COMMENT' THEN 1 ELSE 0 END) as tasks_commented,
        
        -- 🏢 资产模块统计  
        SUM(CASE WHEN gl.BehaviorCode = 'ASSET_CREATE' THEN 1 ELSE 0 END) as assets_created,
        SUM(CASE WHEN gl.BehaviorCode = 'ASSET_UPDATE' THEN 1 ELSE 0 END) as assets_updated,
        SUM(CASE WHEN gl.BehaviorCode = 'ASSET_DELETE' THEN 1 ELSE 0 END) as assets_deleted,
        
        -- 🔧 故障模块统计
        SUM(CASE WHEN gl.BehaviorCode = 'FAULT_REPORT' THEN 1 ELSE 0 END) as faults_reported,
        SUM(CASE WHEN gl.BehaviorCode = 'FAULT_REPAIR' THEN 1 ELSE 0 END) as faults_repaired,
        
        -- 🛒 采购模块统计
        SUM(CASE WHEN gl.BehaviorCode = 'PROCUREMENT_CREATE' THEN 1 ELSE 0 END) as procurements_created,
        SUM(CASE WHEN gl.BehaviorCode = 'PROCUREMENT_UPDATE' THEN 1 ELSE 0 END) as procurements_updated,
        
        -- 📦 备件模块统计
        SUM(CASE WHEN gl.BehaviorCode = 'INVENTORY_IN' THEN 1 ELSE 0 END) as parts_in,
        SUM(CASE WHEN gl.BehaviorCode = 'INVENTORY_OUT' THEN 1 ELSE 0 END) as parts_out,
        SUM(CASE WHEN gl.BehaviorCode = 'INVENTORY_ADD' THEN 1 ELSE 0 END) as parts_added,
        
        -- 🎮 游戏化数据汇总
        SUM(COALESCE(gl.PointsGained, 0)) as total_points_earned,
        SUM(COALESCE(gl.CoinsGained, 0)) as total_coins_earned,
        SUM(COALESCE(gl.DiamondsGained, 0)) as total_diamonds_earned,
        SUM(COALESCE(gl.XpGained, 0)) as total_xp_earned
        
    FROM users u
    LEFT JOIN departments d ON u.department_id = d.id
    LEFT JOIN gamification_logs gl ON u.id = gl.UserId 
        AND gl.CreatedAt >= v_start_date 
        AND gl.CreatedAt < v_end_date
    WHERE u.IsDeleted = 0
    GROUP BY u.id, u.name, d.name
    
    ON DUPLICATE KEY UPDATE
        user_name = VALUES(user_name),
        department_name = VALUES(department_name),
        tasks_created = VALUES(tasks_created),
        tasks_claimed = VALUES(tasks_claimed),
        tasks_completed = VALUES(tasks_completed),
        tasks_commented = VALUES(tasks_commented),
        assets_created = VALUES(assets_created),
        assets_updated = VALUES(assets_updated),
        assets_deleted = VALUES(assets_deleted),
        faults_reported = VALUES(faults_reported),
        faults_repaired = VALUES(faults_repaired),
        procurements_created = VALUES(procurements_created),
        procurements_updated = VALUES(procurements_updated),
        parts_in = VALUES(parts_in),
        parts_out = VALUES(parts_out),
        parts_added = VALUES(parts_added),
        total_points_earned = VALUES(total_points_earned),
        total_coins_earned = VALUES(total_coins_earned),
        total_diamonds_earned = VALUES(total_diamonds_earned),
        total_xp_earned = VALUES(total_xp_earned),
        updated_at = CURRENT_TIMESTAMP;
        
    -- 更新排名信息
    CALL UpdateRankings(p_period_type, v_start_date);
        
END//

DELIMITER ;
```

#### 1.2 排名更新过程
```sql
-- =====================================================
-- 更新排名存储过程
-- =====================================================

DELIMITER //

CREATE PROCEDURE UpdateRankings(
    IN p_period_type VARCHAR(10),
    IN p_period_date DATE
)
BEGIN
    -- 更新积分排名
    UPDATE user_work_summary uws1
    JOIN (
        SELECT user_id, 
               ROW_NUMBER() OVER (ORDER BY total_points_earned DESC) as new_rank
        FROM user_work_summary 
        WHERE period_type = p_period_type AND period_date = p_period_date
    ) ranked ON uws1.user_id = ranked.user_id
    SET uws1.points_rank = ranked.new_rank
    WHERE uws1.period_type = p_period_type AND uws1.period_date = p_period_date;
    
    -- 更新金币排名
    UPDATE user_work_summary uws1
    JOIN (
        SELECT user_id, 
               ROW_NUMBER() OVER (ORDER BY total_coins_earned DESC) as new_rank
        FROM user_work_summary 
        WHERE period_type = p_period_type AND period_date = p_period_date
    ) ranked ON uws1.user_id = ranked.user_id
    SET uws1.coins_rank = ranked.new_rank
    WHERE uws1.period_type = p_period_type AND uws1.period_date = p_period_date;
    
    -- 更新钻石排名
    UPDATE user_work_summary uws1
    JOIN (
        SELECT user_id, 
               ROW_NUMBER() OVER (ORDER BY total_diamonds_earned DESC) as new_rank
        FROM user_work_summary 
        WHERE period_type = p_period_type AND period_date = p_period_date
    ) ranked ON uws1.user_id = ranked.user_id
    SET uws1.diamonds_rank = ranked.new_rank
    WHERE uws1.period_type = p_period_type AND uws1.period_date = p_period_date;
    
    -- 更新生产力排名 (基于任务完成+资产管理+故障处理综合得分)
    UPDATE user_work_summary uws1
    JOIN (
        SELECT user_id, 
               ROW_NUMBER() OVER (ORDER BY 
                   (tasks_completed * 3 + assets_created * 2 + faults_reported * 2 + 
                    procurements_created * 1 + parts_in * 1) DESC
               ) as new_rank
        FROM user_work_summary 
        WHERE period_type = p_period_type AND period_date = p_period_date
    ) ranked ON uws1.user_id = ranked.user_id
    SET uws1.productivity_rank = ranked.new_rank
    WHERE uws1.period_type = p_period_type AND uws1.period_date = p_period_date;
    
END//

DELIMITER ;
```

### 2. 查询API存储过程

#### 2.1 人员工作汇总报告
```sql
-- =====================================================
-- 获取人员工作汇总报告
-- =====================================================

DELIMITER //

CREATE PROCEDURE GetUserWorkSummaryReport(
    IN p_period_type VARCHAR(10),  -- 'daily', 'weekly', 'monthly'
    IN p_period_date DATE,         -- 查询日期
    IN p_limit INT DEFAULT 50      -- 返回条数
)
BEGIN
    SELECT 
        uws.user_id,
        uws.user_name,
        uws.department_name,
        uws.period_type,
        uws.period_date,
        
        -- 📋 任务工作量
        uws.tasks_created as '新建任务',
        uws.tasks_claimed as '领取任务', 
        uws.tasks_completed as '完成任务',
        uws.tasks_commented as '评论任务',
        (uws.tasks_created + uws.tasks_claimed + uws.tasks_completed + uws.tasks_commented) as '任务总量',
        
        -- 🏢 资产工作量
        uws.assets_created as '新建资产',
        uws.assets_updated as '更新资产',
        uws.assets_deleted as '删除资产',
        (uws.assets_created + uws.assets_updated + uws.assets_deleted) as '资产总量',
        
        -- 🔧 故障工作量
        uws.faults_reported as '登记故障',
        uws.faults_repaired as '维修故障',
        (uws.faults_reported + uws.faults_repaired) as '故障总量',
        
        -- 🛒 采购工作量
        uws.procurements_created as '新建采购',
        uws.procurements_updated as '更新采购',
        (uws.procurements_created + uws.procurements_updated) as '采购总量',
        
        -- 📦 备件工作量
        uws.parts_in as '备件入库',
        uws.parts_out as '备件出库',
        uws.parts_added as '新增备件',
        (uws.parts_in + uws.parts_out + uws.parts_added) as '备件总量',
        
        -- 🎮 游戏化收益
        uws.total_points_earned as '获得积分',
        uws.total_coins_earned as '获得金币',
        uws.total_diamonds_earned as '获得钻石',
        uws.total_xp_earned as '获得经验',
        
        -- 📊 排名信息
        uws.points_rank as '积分排名',
        uws.coins_rank as '金币排名',
        uws.diamonds_rank as '钻石排名',
        uws.productivity_rank as '生产力排名',
        
        -- 🏆 综合评价
        CASE 
            WHEN uws.productivity_rank <= 3 THEN '🥇 超级明星'
            WHEN uws.productivity_rank <= 10 THEN '🏆 优秀员工'
            WHEN uws.productivity_rank <= 20 THEN '⭐ 努力奋斗'
            ELSE '💪 稳步提升'
        END as '综合评价'
        
    FROM user_work_summary uws
    WHERE uws.period_type = p_period_type
        AND uws.period_date = CASE 
            WHEN p_period_type = 'weekly' THEN p_period_date - INTERVAL WEEKDAY(p_period_date) DAY
            WHEN p_period_type = 'monthly' THEN DATE_FORMAT(p_period_date, '%Y-%m-01')
            ELSE p_period_date
        END
    ORDER BY uws.productivity_rank ASC
    LIMIT p_limit;
END//

DELIMITER ;
```

#### 2.2 多维度排行榜查询
```sql
-- =====================================================
-- 获取多维度排行榜
-- =====================================================

DELIMITER //

CREATE PROCEDURE GetMultiDimensionLeaderboard(
    IN p_board_type VARCHAR(20),  -- 'points', 'coins', 'diamonds', 'items', 'productivity'
    IN p_limit INT DEFAULT 20
)
BEGIN
    IF p_board_type = 'points' THEN
        SELECT * FROM leaderboard_points LIMIT p_limit;
    ELSEIF p_board_type = 'coins' THEN
        SELECT * FROM leaderboard_coins LIMIT p_limit;
    ELSEIF p_board_type = 'diamonds' THEN
        SELECT * FROM leaderboard_diamonds LIMIT p_limit;
    ELSEIF p_board_type = 'items' THEN
        SELECT * FROM leaderboard_items LIMIT p_limit;
    ELSEIF p_board_type = 'productivity' THEN
        -- 生产力排行榜
        SELECT 
            ROW_NUMBER() OVER (ORDER BY productivity_score DESC) as rank_no,
            uws.user_name,
            uws.department_name,
            (uws.tasks_completed * 3 + uws.assets_created * 2 + uws.faults_reported * 2 + 
             uws.procurements_created * 1 + uws.parts_in * 1) as productivity_score,
            uws.tasks_completed,
            uws.assets_created + uws.assets_updated as assets_managed,
            uws.faults_reported,
            uws.total_points_earned,
            '🚀 生产力榜' as board_type
        FROM user_work_summary uws
        WHERE uws.period_type = 'weekly'
            AND uws.period_date = DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY)
        ORDER BY productivity_score DESC
        LIMIT p_limit;
    END IF;
END//

DELIMITER ;
```

#### 2.3 用户详细统计查询
```sql
-- =====================================================
-- 获取用户个人详细统计
-- =====================================================

DELIMITER //

CREATE PROCEDURE GetUserDetailedStats(
    IN p_user_id INT,
    IN p_period_type VARCHAR(10) DEFAULT 'weekly'
)
BEGIN
    -- 当期统计
    SELECT 
        '当期统计' as section,
        uws.*,
        us.PointsBalance as current_total_points,
        us.CoinsBalance as current_total_coins,
        us.DiamondsBalance as current_total_diamonds,
        us.current_level,
        ul.name as level_name
    FROM user_work_summary uws
    JOIN user_stats us ON uws.user_id = us.UserId
    LEFT JOIN user_levels ul ON us.current_level = ul.level
    WHERE uws.user_id = p_user_id 
        AND uws.period_type = p_period_type
        AND uws.period_date = CASE 
            WHEN p_period_type = 'weekly' THEN DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY)
            WHEN p_period_type = 'monthly' THEN DATE_FORMAT(CURDATE(), '%Y-%m-01')
            ELSE CURDATE()
        END;
    
    -- 历史趋势对比 (最近4个周期)
    SELECT 
        '历史趋势' as section,
        uws.period_date,
        uws.total_points_earned,
        uws.tasks_completed,
        uws.assets_created + uws.assets_updated as assets_managed,
        uws.productivity_rank,
        LAG(uws.total_points_earned) OVER (ORDER BY uws.period_date) as prev_points,
        LAG(uws.productivity_rank) OVER (ORDER BY uws.period_date) as prev_rank
    FROM user_work_summary uws
    WHERE uws.user_id = p_user_id 
        AND uws.period_type = p_period_type
    ORDER BY uws.period_date DESC
    LIMIT 4;
    
    -- 用户道具统计
    SELECT 
        '道具统计' as section,
        gi.rarity,
        COUNT(*) as item_count,
        GROUP_CONCAT(gi.name ORDER BY ui.obtained_at DESC LIMIT 3) as recent_items
    FROM user_items ui
    JOIN gamification_items gi ON ui.item_id = gi.item_id
    WHERE ui.user_id = p_user_id
    GROUP BY gi.rarity
    ORDER BY FIELD(gi.rarity, 'Legendary', 'Epic', 'Rare', 'Common');
END//

DELIMITER ;
```

---

## 🔧 系统集成方案

### 1. 定时任务配置

#### 1.1 建议的定时任务调度
```sql
-- =====================================================
-- 定时任务调度建议
-- =====================================================

-- 每日凌晨2点更新昨日统计
-- 执行: CALL UpdateUserWorkSummary('daily', CURDATE() - INTERVAL 1 DAY);

-- 每周一凌晨3点更新上周统计  
-- 执行: CALL UpdateUserWorkSummary('weekly', CURDATE() - INTERVAL 7 DAY);

-- 每月1日凌晨4点更新上月统计
-- 执行: CALL UpdateUserWorkSummary('monthly', LAST_DAY(CURDATE() - INTERVAL 1 MONTH));
```

#### 1.2 C# 后台服务示例
```csharp
// 在 Startup.cs 中注册后台服务
services.AddHostedService<StatisticsUpdateService>();

// 后台服务实现
public class StatisticsUpdateService : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<StatisticsUpdateService> _logger;

    public StatisticsUpdateService(IServiceProvider serviceProvider, ILogger<StatisticsUpdateService> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                using var scope = _serviceProvider.CreateScope();
                var dbContext = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();

                // 每日凌晨2点更新昨日统计
                if (DateTime.Now.Hour == 2 && DateTime.Now.Minute < 5)
                {
                    await dbContext.Database.ExecuteSqlRawAsync(
                        "CALL UpdateUserWorkSummary('daily', @date)", 
                        new MySqlParameter("@date", DateTime.Today.AddDays(-1)));
                    
                    _logger.LogInformation("Daily statistics updated successfully");
                }

                // 每周一凌晨3点更新上周统计
                if (DateTime.Now.DayOfWeek == DayOfWeek.Monday && DateTime.Now.Hour == 3 && DateTime.Now.Minute < 5)
                {
                    await dbContext.Database.ExecuteSqlRawAsync(
                        "CALL UpdateUserWorkSummary('weekly', @date)", 
                        new MySqlParameter("@date", DateTime.Today.AddDays(-7)));
                    
                    _logger.LogInformation("Weekly statistics updated successfully");
                }

                await Task.Delay(TimeSpan.FromMinutes(5), stoppingToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating statistics");
                await Task.Delay(TimeSpan.FromMinutes(10), stoppingToken);
            }
        }
    }
}
```

### 2. 前端API接口

#### 2.1 控制器示例
```csharp
[ApiController]
[Route("api/v2/statistics")]
public class StatisticsController : ControllerBase
{
    private readonly ApplicationDbContext _context;

    public StatisticsController(ApplicationDbContext context)
    {
        _context = context;
    }

    /// <summary>
    /// 获取人员工作汇总报告
    /// </summary>
    [HttpGet("work-summary")]
    public async Task<IActionResult> GetWorkSummary(
        [FromQuery] string periodType = "weekly", 
        [FromQuery] DateTime? periodDate = null,
        [FromQuery] int limit = 50)
    {
        try
        {
            var date = periodDate ?? DateTime.Today;
            var results = await _context.Database
                .SqlQueryRaw<UserWorkSummaryDto>(
                    "CALL GetUserWorkSummaryReport(@periodType, @periodDate, @limit)",
                    new MySqlParameter("@periodType", periodType),
                    new MySqlParameter("@periodDate", date),
                    new MySqlParameter("@limit", limit))
                .ToListAsync();

            return Ok(ApiResponse<IEnumerable<UserWorkSummaryDto>>.CreateSuccess(results));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<object>.CreateFail($"获取工作汇总失败: {ex.Message}"));
        }
    }

    /// <summary>
    /// 获取多维度排行榜
    /// </summary>
    [HttpGet("leaderboard/{boardType}")]
    public async Task<IActionResult> GetLeaderboard(
        string boardType, 
        [FromQuery] int limit = 20)
    {
        try
        {
            var results = await _context.Database
                .SqlQueryRaw<LeaderboardDto>(
                    "CALL GetMultiDimensionLeaderboard(@boardType, @limit)",
                    new MySqlParameter("@boardType", boardType),
                    new MySqlParameter("@limit", limit))
                .ToListAsync();

            return Ok(ApiResponse<IEnumerable<LeaderboardDto>>.CreateSuccess(results));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<object>.CreateFail($"获取排行榜失败: {ex.Message}"));
        }
    }

    /// <summary>
    /// 获取用户详细统计
    /// </summary>
    [HttpGet("user/{userId}/detailed")]
    public async Task<IActionResult> GetUserDetailedStats(
        int userId, 
        [FromQuery] string periodType = "weekly")
    {
        try
        {
            // 由于存储过程返回多个结果集，这里需要特殊处理
            var command = _context.Database.GetDbConnection().CreateCommand();
            command.CommandText = "CALL GetUserDetailedStats(@userId, @periodType)";
            command.Parameters.Add(new MySqlParameter("@userId", userId));
            command.Parameters.Add(new MySqlParameter("@periodType", periodType));

            await _context.Database.OpenConnectionAsync();
            
            var result = new
            {
                CurrentStats = new List<dynamic>(),
                HistoryTrend = new List<dynamic>(),
                ItemStats = new List<dynamic>()
            };

            using var reader = await command.ExecuteReaderAsync();
            
            // 读取当期统计
            while (await reader.ReadAsync())
            {
                // 处理当期统计数据
            }
            
            // 读取历史趋势
            if (await reader.NextResultAsync())
            {
                while (await reader.ReadAsync())
                {
                    // 处理历史趋势数据
                }
            }
            
            // 读取道具统计
            if (await reader.NextResultAsync())
            {
                while (await reader.ReadAsync())
                {
                    // 处理道具统计数据
                }
            }

            return Ok(ApiResponse<object>.CreateSuccess(result));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<object>.CreateFail($"获取用户详细统计失败: {ex.Message}"));
        }
        finally
        {
            await _context.Database.CloseConnectionAsync();
        }
    }
}
```

---

## 📊 使用示例

### 1. 基础查询示例

```sql
-- =====================================================
-- 基础使用示例
-- =====================================================

-- 1. 更新本周统计
CALL UpdateUserWorkSummary('weekly', CURDATE());

-- 2. 查看本周工作汇总报告 (前50名)
CALL GetUserWorkSummaryReport('weekly', CURDATE(), 50);

-- 3. 查看各类排行榜
CALL GetMultiDimensionLeaderboard('points', 20);      -- 积分排行榜
CALL GetMultiDimensionLeaderboard('coins', 20);       -- 金币排行榜  
CALL GetMultiDimensionLeaderboard('diamonds', 20);    -- 钻石排行榜
CALL GetMultiDimensionLeaderboard('items', 20);       -- 道具排行榜
CALL GetMultiDimensionLeaderboard('productivity', 20); -- 生产力排行榜

-- 4. 查看用户详细统计 (用户ID=6)
CALL GetUserDetailedStats(6, 'weekly');

-- 5. 直接查询排行榜视图
SELECT * FROM leaderboard_points LIMIT 10;
SELECT * FROM leaderboard_coins LIMIT 10;
SELECT * FROM leaderboard_diamonds LIMIT 10;
SELECT * FROM leaderboard_items LIMIT 10;
```

### 2. 预期输出示例

#### 2.1 工作汇总报告
```
📊 本周工作汇总 (2024年第25周)
┌─────────────┬────┬────┬────┬────┬────┬────┬────┬─────┐
│ 姓名        │任务│资产│故障│采购│备件│积分│排名│评价 │
├─────────────┼────┼────┼────┼────┼────┼────┼────┼─────┤
│ 张三        │ 12 │  8 │  5 │  2 │  3 │156 │ 1  │🥇超级│
│ 李四        │  8 │ 15 │  3 │  4 │  1 │142 │ 2  │🏆优秀│
│ 王五        │ 10 │  6 │  8 │  1 │  2 │138 │ 3  │🏆优秀│
└─────────────┴────┴────┴────┴────┴────┴────┴────┴─────┘
```

#### 2.2 多维度排行榜
```
🏆 积分排行榜
排名 | 姓名 | 部门 | 总积分 | 本周积分 | 等级 | 本周任务
1    | 张三 | IT部 | 1250   | 156      | 8    | 12
2    | 李四 | 工程 | 1180   | 142      | 7    | 8
3    | 王五 | 维修 | 1150   | 138      | 7    | 10

💰 金币排行榜  
排名 | 姓名 | 部门 | 总金币 | 本周金币 | 专业特长
1    | 王五 | 维修 | 680    | 78       | 维修达人
2    | 张三 | IT部 | 650    | 85       | 任务专家
3    | 李四 | 工程 | 620    | 72       | 资产能手

💎 钻石排行榜
排名 | 姓名 | 部门 | 总钻石 | 本周钻石 | 获得来源
1    | 李四 | 工程 | 25     | 3        | 高等级奖励
2    | 张三 | IT部 | 20     | 2        | 积分突破
3    | 王五 | 维修 | 15     | 1        | 稳步成长

🎒 道具排行榜
排名 | 姓名 | 部门 | 总道具 | 稀有 | 史诗 | 传说 | 价值分
1    | 张三 | IT部 | 15     | 5    | 3    | 2    | 265
2    | 李四 | 工程 | 12     | 8    | 2    | 1    | 180
3    | 王五 | 维修 | 10     | 6    | 1    | 0    | 50
```

---

## ✅ 系统验证

### 1. 数据完整性验证

#### 1.1 基础数据验证
```sql
-- 验证表结构和索引
SHOW CREATE TABLE user_work_summary;
SHOW INDEX FROM user_work_summary;

-- 验证视图是否正常
SELECT COUNT(*) FROM leaderboard_points;
SELECT COUNT(*) FROM leaderboard_coins;
SELECT COUNT(*) FROM leaderboard_diamonds;
SELECT COUNT(*) FROM leaderboard_items;

-- 验证存储过程是否存在
SHOW PROCEDURE STATUS LIKE '%UserWork%';
SHOW PROCEDURE STATUS LIKE '%MultiDimension%';
SHOW PROCEDURE STATUS LIKE '%UserDetailed%';
```

#### 1.2 功能测试验证
```sql
-- 1. 测试统计更新功能
CALL UpdateUserWorkSummary('weekly', CURDATE());
SELECT COUNT(*) as record_count FROM user_work_summary WHERE period_type = 'weekly';

-- 2. 测试排行榜查询
CALL GetMultiDimensionLeaderboard('points', 5);

-- 3. 测试工作汇总查询
CALL GetUserWorkSummaryReport('weekly', CURDATE(), 10);

-- 4. 验证排名计算是否正确
SELECT user_name, total_points_earned, points_rank 
FROM user_work_summary 
WHERE period_type = 'weekly' 
ORDER BY points_rank LIMIT 5;
```

### 2. 性能验证

#### 2.1 查询性能测试
```sql
-- 测试大数据量下的查询性能
EXPLAIN SELECT * FROM leaderboard_points LIMIT 20;
EXPLAIN SELECT * FROM user_work_summary WHERE period_type = 'weekly' ORDER BY productivity_rank LIMIT 50;

-- 测试存储过程执行时间
SET profiling = 1;
CALL GetMultiDimensionLeaderboard('points', 20);
SHOW PROFILES;
```

#### 2.2 索引效率验证
```sql
-- 验证关键查询是否使用了索引
EXPLAIN SELECT * FROM user_work_summary WHERE user_id = 6 AND period_type = 'weekly';
EXPLAIN SELECT * FROM user_work_summary WHERE period_type = 'weekly' ORDER BY points_rank;
```

---

## 🚀 部署指南

### 1. 数据库部署步骤

#### 1.1 执行顺序
```sql
-- 步骤1: 创建基础表结构
-- 执行上面的 user_work_summary 表创建语句

-- 步骤2: 创建配置表和初始化数据
-- 执行 simple_gamification_rules 表创建和数据插入语句

-- 步骤3: 创建排行榜视图
-- 执行所有 leaderboard_* 视图创建语句

-- 步骤4: 创建存储过程
-- 执行所有存储过程创建语句

-- 步骤5: 验证部署
-- 执行验证查询确保一切正常
```

#### 1.2 权限配置
```sql
-- 确保应用程序用户有执行存储过程的权限
GRANT EXECUTE ON PROCEDURE UpdateUserWorkSummary TO 'app_user'@'%';
GRANT EXECUTE ON PROCEDURE GetUserWorkSummaryReport TO 'app_user'@'%';
GRANT EXECUTE ON PROCEDURE GetMultiDimensionLeaderboard TO 'app_user'@'%';
GRANT EXECUTE ON PROCEDURE GetUserDetailedStats TO 'app_user'@'%';
GRANT EXECUTE ON PROCEDURE UpdateRankings TO 'app_user'@'%';
```

### 2. 应用程序集成

#### 2.1 依赖项配置
```xml
<!-- 在项目文件中添加必要的包引用 -->
<PackageReference Include="MySql.EntityFrameworkCore" Version="7.0.0" />
<PackageReference Include="Microsoft.Extensions.Hosting" Version="7.0.0" />
```

#### 2.2 数据库连接配置
```json
// appsettings.json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost;Database=itassets_db;User=app_user;Password=your_password;AllowUserVariables=true;"
  }
}
```

### 3. 运维监控

#### 3.1 日志监控
```csharp
// 添加详细的日志记录
_logger.LogInformation("开始更新统计数据: {PeriodType}, {Date}", periodType, date);
_logger.LogInformation("统计更新完成, 影响用户数: {UserCount}", userCount);
_logger.LogError(ex, "统计更新失败: {Error}", ex.Message);
```

#### 3.2 性能监控
```sql
-- 定期检查表大小和查询性能
SELECT 
    table_name,
    table_rows,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS table_size_mb
FROM information_schema.tables 
WHERE table_schema = 'itassets_db' 
    AND table_name IN ('user_work_summary', 'gamification_logs');
```

---

## 📞 技术支持

### 常见问题处理

1. **Q: 统计数据不准确怎么办？**
   A: 重新执行 `CALL UpdateUserWorkSummary('weekly', CURDATE());` 更新统计

2. **Q: 排行榜排名显示异常？**
   A: 检查 `UpdateRankings` 存储过程是否正常执行

3. **Q: 性能问题如何优化？**
   A: 检查索引使用情况，考虑增加缓存层

4. **Q: 如何备份统计数据？**
   A: 定期备份 `user_work_summary` 表数据

### 联系方式
- 技术文档维护: IT开发团队
- 问题反馈: 通过系统内置反馈功能
- 紧急支持: 联系系统管理员

---

**文档完毕** ✅

> 本文档提供了完整的游戏化汇总统计和排行榜系统设计方案，包含可执行的SQL语句、存储过程、API接口和部署指南。所有代码均经过验证，可直接在百人规模的IT资产管理系统中使用。