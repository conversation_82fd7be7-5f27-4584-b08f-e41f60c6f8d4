// File: Application/Features/SpareParts/Dtos/SparePartTransactionRequest.cs
// Description: 备品备件出入库操作的请求DTO基类

using System.ComponentModel.DataAnnotations;

namespace ItAssetsSystem.Application.Features.SpareParts.Dtos
{
    /// <summary>
    /// 备品备件出入库操作的请求DTO基类
    /// </summary>
    public class SparePartTransactionRequest
    {
        /// <summary>
        /// 备件ID
        /// </summary>
        [Required(ErrorMessage = "备件ID不能为空")]
        public long PartId { get; set; }
        
        /// <summary>
        /// 数量
        /// </summary>
        [Required(ErrorMessage = "数量不能为空")]
        [Range(1, int.MaxValue, ErrorMessage = "数量必须大于0")]
        public int Quantity { get; set; }
        
        /// <summary>
        /// 库位ID
        /// </summary>
        [Required(ErrorMessage = "库位不能为空")]
        public long LocationId { get; set; }
        
        /// <summary>
        /// 关联单号（采购单、维修单等）
        /// </summary>
        [StringLength(100, ErrorMessage = "关联单号长度不能超过100个字符")]
        public string Reference { get; set; }
        
        /// <summary>
        /// 原因类型: 1采购, 2退回, 3领用, 4报废, 5盘点调整等
        /// </summary>
        public byte? ReasonType { get; set; }
        
        /// <summary>
        /// 原因/用途详细描述
        /// </summary>
        [StringLength(200, ErrorMessage = "原因/用途详细描述长度不能超过200个字符")]
        public string Reason { get; set; }
        
        /// <summary>
        /// 关联资产ID
        /// </summary>
        public int? RelatedAssetId { get; set; }
        
        /// <summary>
        /// 关联故障ID
        /// </summary>
        public int? RelatedFaultId { get; set; }
        
        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(500, ErrorMessage = "备注长度不能超过500个字符")]
        public string Notes { get; set; }
    }
} 