import{_ as a,x as s,b as t,d as e,t as n,f as c,C as o,e as i,w as r,a as l,u,o as m,p as d,a9 as p}from"./index-CG5lHOPO.js";const h={class:"basic-layout"},y={class:"basic-header"},f={class:"site-title"},_={class:"user-menu"},x={class:"main-content"},b=a({__name:"BasicLayout",setup(a){const b=u(),v=s(),w=()=>{p.confirm("确定要退出登录吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{try{await v.logout(),b.push("/login")}catch(a){}})).catch((()=>{}))};return(a,s)=>{const u=l("el-button"),p=l("router-view");return m(),t("div",h,[e("header",y,[e("h1",f,n(c(o).name),1),e("div",_,[i(u,{type:"primary",size:"small",onClick:w},{default:r((()=>s[0]||(s[0]=[d("退出登录")]))),_:1})])]),e("main",x,[i(p)])])}}},[["__scopeId","data-v-341124a4"]]);export{b as default};
