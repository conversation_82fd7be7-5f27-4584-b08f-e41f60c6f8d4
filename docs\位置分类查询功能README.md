# 位置分类查询功能

> 基于CTE递归查询的智能位置部门继承和多维度分类查询系统

## 🚀 功能概述

位置分类查询功能是IT资产管理系统的核心模块之一，通过MySQL CTE（Common Table Expression）递归查询技术，解决了复杂的位置层级关系和部门继承问题，提供了强大的多维度查询和统计分析能力。

### 🎯 核心价值
- **智能继承**: 自动计算位置的部门继承关系
- **多维查询**: 支持部门、位置类型、资产类型等多种维度筛选
- **统计分析**: 提供详细的分类统计信息
- **高性能**: 单次查询处理复杂层级关系
- **易集成**: 标准V2 API接口，完整前端组件

### 🔧 解决的问题
1. **位置部门归属**: "某个位置的资产属于哪个部门？"
2. **层级继承**: 子位置自动继承父位置的部门分配
3. **复合查询**: 按多种条件组合查询位置和资产
4. **数据分析**: 多维度统计分析和报表生成

## 📊 核心功能

### 1. 位置部门继承
- **直接分配**: 位置直接设置的部门
- **继承分配**: 从父级位置继承的部门
- **有效部门**: 优先使用直接分配，否则使用继承部门

### 2. 多维度分类查询
| 查询维度 | 参数名 | 说明 | 示例 |
|---------|--------|------|------|
| 部门筛选 | `departmentId` | 按部门ID筛选 | 查找生产部的所有位置 |
| 位置类型 | `locationType` | 1=工厂,2=车间,3=工序,4=工位 | 查找所有车间 |
| 层级关系 | `parentLocationId` | 父位置+是否递归 | 查找某车间的所有工位 |
| 资产维度 | `assetTypeId`, `assetStatus` | 按资产类型/状态筛选 | 查找有故障设备的位置 |
| 条件筛选 | `onlyWithAssets` | 只显示有资产的位置 | 过滤空位置 |
| 关键词搜索 | `keyword` | 模糊搜索 | 搜索"生产"相关位置 |

### 3. 统计分析
- **按部门统计**: 各部门管理的位置数和资产数
- **按位置类型统计**: 工厂/车间/工序/工位的分布情况
- **按资产类型统计**: 各类型资产的分布和位置情况
- **按层级统计**: 各层级的位置和资产分布

## 🛠️ 技术架构

### 后端架构
```
┌─────────────────────────────────────────┐
│    API控制器层 (V2 Controllers)          │
│  LocationDepartmentInheritanceController │
├─────────────────────────────────────────┤
│         应用服务层 (Services)            │
│  LocationDepartmentInheritanceService    │
├─────────────────────────────────────────┤
│          数据访问层 (EF Core)            │
│           CTE递归查询                    │
├─────────────────────────────────────────┤
│           数据库层 (MySQL)               │
│      locations, assets, departments      │
└─────────────────────────────────────────┘
```

### 前端组件
```
┌─────────────────────────────────────────┐
│       LocationClassificationSearch       │
│         (高级搜索组件)                    │
├─────────────────────────────────────────┤
│      DepartmentInheritancePanel          │
│       (部门继承信息面板)                  │
├─────────────────────────────────────────┤
│       locationDepartmentInheritance      │
│           (API调用封装)                   │
└─────────────────────────────────────────┘
```

## 📡 API接口

### 基础URL
```
/api/v2/locationdepartmentinheritance
```

### 核心接口
| 方法 | 路径 | 功能 |
|------|------|------|
| `GET` | `/` | 获取所有位置的部门继承信息 |
| `GET` | `/{id}` | 获取指定位置的部门继承信息 |
| `GET` | `/department-stats` | 获取部门位置统计信息 |
| `GET` | `/by-department/{departmentId}` | 获取部门管理的所有位置 |
| `POST` | `/search` | 高级分类搜索（推荐） |
| `GET` | `/search` | 快速分类搜索 |
| `GET` | `/classification-stats` | 获取分类统计信息 |

### 快速示例
```bash
# 获取生产部的所有车间
curl -X GET "/api/v2/locationdepartmentinheritance/search?departmentId=5&locationType=2" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 查找有故障设备的位置
curl -X GET "/api/v2/locationdepartmentinheritance/search?assetStatus=故障&onlyWithAssets=true" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 💻 使用示例

### 前端集成
```vue
<template>
  <div>
    <!-- 高级搜索组件 -->
    <LocationClassificationSearch 
      @search-complete="handleSearchComplete"
    />
    
    <!-- 部门继承信息面板 -->
    <DepartmentInheritancePanel 
      :location-id="selectedLocationId"
    />
  </div>
</template>

<script setup>
import LocationClassificationSearch from '@/components/LocationClassificationSearch.vue'
import DepartmentInheritancePanel from '@/components/DepartmentInheritancePanel.vue'
</script>
```

### JavaScript调用
```javascript
import locationDepartmentInheritanceApi from '@/api/locationDepartmentInheritance'

// 查询生产部的所有工位设备
const getProductionWorkstations = async () => {
  const query = {
    departmentId: 5,          // 生产部
    locationType: 4,          // 工位
    onlyWithAssets: true,     // 有设备的位置
    sortBy: 'assetCount',     // 按设备数量排序
    sortDirection: 'desc'
  }
  
  const response = await locationDepartmentInheritanceApi.searchLocationsByClassification(query)
  return response.data
}

// 生成部门资产盘点清单
const generateInventoryReport = async (departmentId) => {
  const response = await locationDepartmentInheritanceApi.getLocationsByDepartment(departmentId)
  
  const report = response.data.map(location => ({
    位置路径: location.locationPath,
    位置名称: location.locationName,
    资产数量: location.assetCount,
    有效部门: location.effectiveDepartmentName
  }))
  
  return report
}
```

## 📋 实际应用场景

### 1. 设备巡检计划
```javascript
// 按部门生成设备巡检计划
const generateInspectionPlan = async (departmentId) => {
  const query = {
    departmentId: departmentId,
    assetStatus: '正常',
    onlyWithAssets: true,
    sortBy: 'locationPath'
  }
  
  const response = await locationDepartmentInheritanceApi.searchLocationsByClassification(query)
  return response.data.locations
}
```

### 2. 故障设备统计
```javascript
// 统计各部门的故障设备分布
const analyzeFaultDistribution = async () => {
  const query = {
    assetStatus: '故障',
    onlyWithAssets: true,
    sortBy: 'departmentName'
  }
  
  const response = await locationDepartmentInheritanceApi.searchLocationsByClassification(query)
  
  // 按部门分组统计
  const faultStats = response.data.locations.reduce((stats, location) => {
    const dept = location.effectiveDepartmentName || '未分配'
    if (!stats[dept]) stats[dept] = { locationCount: 0, assetCount: 0 }
    stats[dept].locationCount++
    stats[dept].assetCount += location.assetCount
    return stats
  }, {})
  
  return faultStats
}
```

### 3. 空置位置管理
```javascript
// 查找有部门分配但无设备的位置
const findEmptyLocations = async () => {
  const query = {
    onlyDirectDepartment: true,  // 有直接部门分配
    onlyWithAssets: false,       // 允许无资产
    sortBy: 'departmentName'
  }
  
  const response = await locationDepartmentInheritanceApi.searchLocationsByClassification(query)
  return response.data.locations.filter(loc => loc.assetCount === 0)
}
```

### 4. 资产密度分析
```javascript
// 分析各位置类型的资产密度
const analyzeAssetDensity = async () => {
  const stats = await locationDepartmentInheritanceApi.getLocationClassificationStats()
  
  const densityReport = stats.data.byLocationType.map(type => ({
    位置类型: type.locationTypeName,
    位置数量: type.locationCount,
    资产数量: type.assetCount,
    平均资产密度: Math.round(type.assetCount / type.locationCount * 100) / 100,
    部门覆盖率: Math.round(type.locationsWithDepartment / type.locationCount * 100) + '%'
  }))
  
  return densityReport
}
```

## 🔧 配置和部署

### 后端配置
服务已在`Startup.cs`中注册：
```csharp
services.AddScoped<Application.Features.Location.Services.ILocationDepartmentInheritanceService, 
                   Application.Features.Location.Services.LocationDepartmentInheritanceService>();
```

### 数据库索引优化
```sql
-- 必要索引
CREATE INDEX idx_locations_department_id ON locations(department_id);
CREATE INDEX idx_locations_parent_id ON locations(parent_id);
CREATE INDEX idx_locations_type ON locations(type);
CREATE INDEX idx_assets_location_id ON assets(location_id);
CREATE INDEX idx_assets_status ON assets(status);

-- 复合索引（提升查询性能）
CREATE INDEX idx_locations_type_dept ON locations(type, department_id);
CREATE INDEX idx_assets_location_status ON assets(location_id, status);
```

### 权限要求
- 所有接口需要JWT认证
- 建议为不同角色配置不同的查询权限

## 📈 性能优化

### 1. 查询优化
- 使用CTE递归查询，单次完成层级计算
- 合理使用分页，避免大结果集
- 适当的索引策略

### 2. 缓存策略
- 统计信息缓存30分钟
- 部门信息缓存15分钟
- 具体位置信息实时查询

### 3. 前端优化
- 虚拟滚动处理大列表
- 防抖搜索减少请求
- 合理的组件懒加载

## 📚 文档索引

### 技术文档
- [位置分类查询功能技术文档](./位置分类查询功能技术文档.md) - 完整的技术实现细节
- [位置分类查询API文档](./api/位置分类查询API文档.md) - 详细的API接口文档

### 使用指南
- [使用示例和集成指南](../使用示例和集成指南.md) - 基础使用示例
- [位置分类查询功能说明](../位置分类查询功能说明.md) - 详细功能说明和实战案例

### 组件文档
- [LocationClassificationSearch.vue](../frontend/src/views/locations/components/LocationClassificationSearch.vue) - 高级搜索组件
- [DepartmentInheritancePanel.vue](../frontend/src/views/locations/components/DepartmentInheritancePanel.vue) - 部门继承面板
- [locationDepartmentInheritance.js](../frontend/src/api/locationDepartmentInheritance.js) - API封装

## 🐛 故障排除

### 常见问题
1. **查询超时**: 检查索引，优化查询条件
2. **内存不足**: 使用分页，限制结果集大小
3. **权限错误**: 确认JWT令牌有效性
4. **递归深度**: 检查位置层级关系，避免循环引用

### 调试工具
- SQL查询日志：开发环境自动启用
- 性能监控：使用Stopwatch记录执行时间
- 前端调试：API请求/响应拦截器

## 🎯 总结

位置分类查询功能通过先进的CTE递归查询技术，为IT资产管理系统提供了强大的数据查询和分析能力：

✅ **智能继承**: 自动处理复杂的位置部门继承关系  
✅ **多维查询**: 支持7个维度的灵活组合查询  
✅ **高性能**: 单次查询处理复杂层级关系  
✅ **易扩展**: 标准V2架构，易于添加新的查询维度  
✅ **完整集成**: 提供完整的前后端解决方案  

该功能完全遵循项目的双轨架构原则，不影响现有核心模块，可以安全部署和使用。通过合理的索引优化和缓存策略，能够在大数据量情况下保持良好的性能表现。

---

**开发团队**: IT资产管理系统开发组  
**文档版本**: v1.0  
**最后更新**: 2025-06-02