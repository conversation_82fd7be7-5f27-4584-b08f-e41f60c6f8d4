using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using ItAssetsSystem.Models.Gamification;

namespace ItAssetsSystem.Models.Entities.Gamification
{
    /// <summary>
    /// 游戏化用户道具实体 - 对应 gamification_useritems 表
    /// </summary>
    [Table("gamification_useritems")]
    public class GamificationUserItem
    {
        /// <summary>
        /// 用户物品ID (BIGINT PK)
        /// </summary>
        [Key]
        [Column("UserItemId")]
        public long UserItemId { get; set; }

        /// <summary>
        /// 用户ID (关联 users.Id - INT)
        /// </summary>
        [Column("UserId")]
        public int UserId { get; set; }

        /// <summary>
        /// 物品ID (关联 gamification_items.ItemId - BIGINT)
        /// </summary>
        [Column("ItemId")]
        public long ItemId { get; set; }

        /// <summary>
        /// 物品数量
        /// </summary>
        [Column("Quantity")]
        public int Quantity { get; set; } = 1;

        /// <summary>
        /// 获得时间
        /// </summary>
        [Column("AcquiredTimestamp")]
        public DateTime AcquiredTimestamp { get; set; } = DateTime.Now;

        /// <summary>
        /// 上次使用时间
        /// </summary>
        [Column("LastUsedTimestamp")]
        public DateTime? LastUsedTimestamp { get; set; }

        /// <summary>
        /// 过期时间(如果有)
        /// </summary>
        [Column("ExpiryTimestamp")]
        public DateTime? ExpiryTimestamp { get; set; }

        /// <summary>
        /// 获得来源
        /// </summary>
        [Column("ObtainedSource")]
        [StringLength(50)]
        public string? ObtainedSource { get; set; }

        // 导航属性
        /// <summary>
        /// 关联的用户
        /// </summary>
        [ForeignKey("UserId")]
        public virtual User User { get; set; } = null!;

        /// <summary>
        /// 关联的道具
        /// </summary>
        [ForeignKey("ItemId")]
        public virtual GamificationItem Item { get; set; } = null!;
    }
}
