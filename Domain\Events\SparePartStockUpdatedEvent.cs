// File: Domain/Events/SparePartStockUpdatedEvent.cs
// Description: 备品备件库存更新领域事件

using System;

namespace ItAssetsSystem.Domain.Events
{
    /// <summary>
    /// 备品备件库存更新事件
    /// </summary>
    public class SparePartStockUpdatedEvent
    {
        /// <summary>
        /// 备件ID
        /// </summary>
        public long PartId { get; set; }

        /// <summary>
        /// 变更数量（正数为增加，负数为减少）
        /// </summary>
        public int ChangeQuantity { get; set; }

        /// <summary>
        /// 变更后的库存数量
        /// </summary>
        public int NewStockQuantity { get; set; }

        /// <summary>
        /// 变更原因
        /// </summary>
        public string Reason { get; set; }

        /// <summary>
        /// 操作用户ID
        /// </summary>
        public int OperatorUserId { get; set; }

        /// <summary>
        /// 操作时间
        /// </summary>
        public DateTime OperationTime { get; set; }

        /// <summary>
        /// 关联的事务记录ID
        /// </summary>
        public long? TransactionId { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public SparePartStockUpdatedEvent(long partId, int changeQuantity, int newStockQuantity, string reason, int operatorUserId, long? transactionId = null)
        {
            PartId = partId;
            ChangeQuantity = changeQuantity;
            NewStockQuantity = newStockQuantity;
            Reason = reason;
            OperatorUserId = operatorUserId;
            OperationTime = DateTime.Now;
            TransactionId = transactionId;
        }
    }
}
