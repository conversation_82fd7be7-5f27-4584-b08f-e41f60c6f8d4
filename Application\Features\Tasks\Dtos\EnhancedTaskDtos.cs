// File: Application/Features/Tasks/Dtos/EnhancedTaskDtos.cs
// Description: 增强的任务管理DTO，支持现代化功能

using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace ItAssetsSystem.Application.Features.Tasks.Dtos
{
    /// <summary>
    /// 任务查询参数增强版
    /// </summary>
    public class EnhancedTaskQueryParametersDto : TaskQueryParametersDto
    {
        /// <summary>
        /// 标签过滤
        /// </summary>
        public List<string> Tags { get; set; }

        /// <summary>
        /// 是否包含子任务
        /// </summary>
        public bool IncludeSubTasks { get; set; } = false;

        /// <summary>
        /// 是否只显示我负责的任务
        /// </summary>
        public bool OnlyMyTasks { get; set; } = false;

        /// <summary>
        /// 是否只显示我参与的任务
        /// </summary>
        public bool OnlyParticipating { get; set; } = false;

        /// <summary>
        /// 是否只显示逾期任务
        /// </summary>
        public bool OnlyOverdue { get; set; } = false;

        /// <summary>
        /// 预计工作量范围（小时）
        /// </summary>
        public int? MinWorkload { get; set; }
        public int? MaxWorkload { get; set; }

        /// <summary>
        /// 完成度范围
        /// </summary>
        public int? MinProgress { get; set; }
        public int? MaxProgress { get; set; }

        /// <summary>
        /// 任务积分范围
        /// </summary>
        public int? MinPoints { get; set; }
        public int? MaxPoints { get; set; }

        /// <summary>
        /// 高级排序选项
        /// </summary>
        public string SortBy2 { get; set; } // 二级排序
        public string SortDirection2 { get; set; } // 二级排序方向

        /// <summary>
        /// 自定义字段过滤
        /// </summary>
        public Dictionary<string, object> CustomFilters { get; set; }
    }

    /// <summary>
    /// 批量操作基础请求
    /// </summary>
    public class BatchOperationRequestDto
    {
        [Required]
        public List<long> TaskIds { get; set; } = new();

        /// <summary>
        /// 操作原因/备注
        /// </summary>
        public string Reason { get; set; }

        /// <summary>
        /// 是否发送通知
        /// </summary>
        public bool SendNotification { get; set; } = true;
    }

    /// <summary>
    /// 智能分配请求
    /// </summary>
    public class SmartAssignRequestDto : BatchOperationRequestDto
    {
        /// <summary>
        /// 分配模式
        /// </summary>
        [Required]
        public string AssignMode { get; set; } = "single"; // single, distribute, auto

        /// <summary>
        /// 目标用户ID列表
        /// </summary>
        public List<int> UserIds { get; set; } = new();

        /// <summary>
        /// 是否保留原有参与者
        /// </summary>
        public bool KeepParticipants { get; set; } = true;

        /// <summary>
        /// 分配策略
        /// </summary>
        public string Strategy { get; set; } // workload_balance, skill_match, random

        /// <summary>
        /// 技能权重配置
        /// </summary>
        public Dictionary<string, double> SkillWeights { get; set; }
    }

    /// <summary>
    /// 任务模板DTO
    /// </summary>
    public class TaskTemplateDto
    {
        public long TemplateId { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; }
        public string Priority { get; set; } = "Medium";
        public string TaskType { get; set; } = "Normal";
        public int EstimatedHours { get; set; }
        public int DefaultPoints { get; set; }
        public List<string> DefaultTags { get; set; } = new();
        public List<ChecklistItemDto> ChecklistTemplate { get; set; } = new();
        public string CreatorUserName { get; set; }
        public DateTime CreatedAt { get; set; }
        public int UsageCount { get; set; }
    }

    /// <summary>
    /// 检查列表项DTO
    /// </summary>
    public class ChecklistItemDto
    {
        public long ChecklistItemId { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; }
        public bool IsCompleted { get; set; }
        public int SortOrder { get; set; }
        public DateTime? CompletedAt { get; set; }
        public int? CompletedByUserId { get; set; }
        public string CompletedByUserName { get; set; }
    }

    /// <summary>
    /// 任务时间跟踪DTO
    /// </summary>
    public class TaskTimeTrackingDto
    {
        public long TimeEntryId { get; set; }
        public long TaskId { get; set; }
        public int UserId { get; set; }
        public string UserName { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public int DurationMinutes { get; set; }
        public string Description { get; set; }
        public bool IsBillable { get; set; }
        public DateTime CreatedAt { get; set; }
    }

    /// <summary>
    /// 任务依赖关系DTO
    /// </summary>
    public class TaskDependencyDto
    {
        public long DependencyId { get; set; }
        public long TaskId { get; set; }
        public long DependsOnTaskId { get; set; }
        public string DependsOnTaskName { get; set; } = string.Empty;
        public string DependencyType { get; set; } = "finish_to_start"; // finish_to_start, start_to_start, finish_to_finish, start_to_finish
        public int? LagDays { get; set; }
        public bool IsBlocking { get; set; }
        public DateTime CreatedAt { get; set; }
    }

    /// <summary>
    /// 任务统计增强版
    /// </summary>
    public class EnhancedTaskStatisticsDto : TaskStatisticsDto
    {
        /// <summary>
        /// 平均完成时间（天）
        /// </summary>
        public double AverageCompletionDays { get; set; }

        /// <summary>
        /// 逾期率
        /// </summary>
        public double OverdueRate { get; set; }

        /// <summary>
        /// 按创建者统计
        /// </summary>
        public Dictionary<string, int> TasksByCreator { get; set; } = new();

        /// <summary>
        /// 按负责人统计
        /// </summary>
        public Dictionary<string, TaskUserStatDto> TasksByAssignee { get; set; } = new();

        /// <summary>
        /// 按标签统计
        /// </summary>
        public Dictionary<string, int> TasksByTag { get; set; } = new();

        /// <summary>
        /// 按月份统计
        /// </summary>
        public List<MonthlyTaskStatDto> MonthlyStats { get; set; } = new();

        /// <summary>
        /// 绩效指标
        /// </summary>
        public TaskPerformanceDto Performance { get; set; } = new();

        /// <summary>
        /// 工作量分布
        /// </summary>
        public WorkloadDistributionDto WorkloadDistribution { get; set; } = new();
    }

    /// <summary>
    /// 用户任务统计DTO
    /// </summary>
    public class TaskUserStatDto
    {
        public int UserId { get; set; }
        public string UserName { get; set; } = string.Empty;
        public string AvatarUrl { get; set; }
        public int TotalTasks { get; set; }
        public int CompletedTasks { get; set; }
        public int InProgressTasks { get; set; }
        public int OverdueTasks { get; set; }
        public double CompletionRate { get; set; }
        public double AverageCompletionDays { get; set; }
        public int TotalPoints { get; set; }
        public int CurrentWorkload { get; set; } // 当前进行中的任务数
    }

    /// <summary>
    /// 月度任务统计DTO
    /// </summary>
    public class MonthlyTaskStatDto
    {
        public int Year { get; set; }
        public int Month { get; set; }
        public string MonthName { get; set; } = string.Empty;
        public int CreatedTasks { get; set; }
        public int CompletedTasks { get; set; }
        public int OverdueTasks { get; set; }
        public double CompletionRate { get; set; }
        public int TotalPoints { get; set; }
    }

    /// <summary>
    /// 任务绩效DTO
    /// </summary>
    public class TaskPerformanceDto
    {
        /// <summary>
        /// 团队效率指数
        /// </summary>
        public double EfficiencyIndex { get; set; }

        /// <summary>
        /// 质量评分
        /// </summary>
        public double QualityScore { get; set; }

        /// <summary>
        /// 准时完成率
        /// </summary>
        public double OnTimeCompletionRate { get; set; }

        /// <summary>
        /// 返工率
        /// </summary>
        public double ReworkRate { get; set; }

        /// <summary>
        /// 客户满意度
        /// </summary>
        public double CustomerSatisfaction { get; set; }

        /// <summary>
        /// 趋势分析
        /// </summary>
        public PerformanceTrendDto Trend { get; set; } = new();
    }

    /// <summary>
    /// 绩效趋势DTO
    /// </summary>
    public class PerformanceTrendDto
    {
        public double EfficiencyTrend { get; set; } // 正数表示上升，负数表示下降
        public double QualityTrend { get; set; }
        public double OnTimeTrend { get; set; }
        public string TrendPeriod { get; set; } = "last_30_days";
    }

    /// <summary>
    /// 工作量分布DTO
    /// </summary>
    public class WorkloadDistributionDto
    {
        /// <summary>
        /// 按用户分布
        /// </summary>
        public List<UserWorkloadDto> UserWorkloads { get; set; } = new();

        /// <summary>
        /// 按部门分布
        /// </summary>
        public List<DepartmentWorkloadDto> DepartmentWorkloads { get; set; } = new();

        /// <summary>
        /// 工作量平衡指数
        /// </summary>
        public double BalanceIndex { get; set; }

        /// <summary>
        /// 建议重新分配的任务
        /// </summary>
        public List<TaskDto> SuggestedReassignments { get; set; } = new();
    }

    /// <summary>
    /// 用户工作量DTO
    /// </summary>
    public class UserWorkloadDto
    {
        public int UserId { get; set; }
        public string UserName { get; set; } = string.Empty;
        public int ActiveTasks { get; set; }
        public int TotalEstimatedHours { get; set; }
        public double WorkloadPercentage { get; set; }
        public string WorkloadLevel { get; set; } = "normal"; // low, normal, high, overloaded
        public List<TaskDto> HighPriorityTasks { get; set; } = new();
    }

    /// <summary>
    /// 部门工作量DTO
    /// </summary>
    public class DepartmentWorkloadDto
    {
        public int DepartmentId { get; set; }
        public string DepartmentName { get; set; } = string.Empty;
        public int TotalTasks { get; set; }
        public int TotalMembers { get; set; }
        public double AverageTasksPerMember { get; set; }
        public double AverageCompletionRate { get; set; }
        public List<UserWorkloadDto> MemberWorkloads { get; set; } = new();
    }

    /// <summary>
    /// 任务智能建议DTO
    /// </summary>
    public class TaskSmartSuggestionDto
    {
        public string SuggestionType { get; set; } = string.Empty; // priority_adjustment, reassignment, deadline_extension, etc.
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public double Confidence { get; set; } // 0-1之间的置信度
        public string Reason { get; set; } = string.Empty;
        public List<long> AffectedTaskIds { get; set; } = new();
        public Dictionary<string, object> SuggestionData { get; set; } = new();
        public DateTime GeneratedAt { get; set; }
    }

    /// <summary>
    /// 任务看板配置DTO
    /// </summary>
    public class TaskKanbanConfigDto
    {
        public long ConfigId { get; set; }
        public int UserId { get; set; }
        public string ConfigName { get; set; } = string.Empty;
        public List<KanbanColumnDto> Columns { get; set; } = new();
        public Dictionary<string, object> ViewSettings { get; set; } = new();
        public bool IsDefault { get; set; }
        public bool IsShared { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }

    /// <summary>
    /// 看板列配置DTO
    /// </summary>
    public class KanbanColumnDto
    {
        public string Status { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Color { get; set; } = string.Empty;
        public string Icon { get; set; } = string.Empty;
        public int SortOrder { get; set; }
        public int? TaskLimit { get; set; }
        public bool IsCollapsed { get; set; }
        public List<string> AllowedTaskTypes { get; set; } = new();
    }

    /// <summary>
    /// 任务活动流DTO
    /// </summary>
    public class TaskActivityDto
    {
        public long ActivityId { get; set; }
        public long TaskId { get; set; }
        public string TaskName { get; set; } = string.Empty;
        public int? UserId { get; set; }
        public string UserName { get; set; }
        public string UserAvatarUrl { get; set; }
        public string ActivityType { get; set; } = string.Empty;
        public string ActivityDescription { get; set; } = string.Empty;
        public Dictionary<string, object> ActivityData { get; set; }
        public DateTime Timestamp { get; set; }
        public string IpAddress { get; set; }
        public string UserAgent { get; set; }
    }

    /// <summary>
    /// 任务快速创建DTO
    /// </summary>
    public class QuickCreateTaskDto
    {
        [Required]
        [StringLength(200)]
        public string Name { get; set; } = string.Empty;

        public string Description { get; set; }
        
        public string Priority { get; set; } = "Medium";
        
        public int? AssigneeUserId { get; set; }
        
        public DateTime? DueDate { get; set; }
        
        public List<string> Tags { get; set; } = new();
        
        public long? TemplateId { get; set; }
        
        public long? ParentTaskId { get; set; }
        
        public int? AssetId { get; set; }
        
        public int? LocationId { get; set; }

        /// <summary>
        /// 快速检查列表项
        /// </summary>
        public List<string> QuickChecklistItems { get; set; } = new();
    }
}