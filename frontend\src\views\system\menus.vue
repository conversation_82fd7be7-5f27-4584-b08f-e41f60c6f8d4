/**
 * 航空航天级IT资产管理系统 - 菜单管理页面
 * 文件路径: src/views/system/menus.vue
 * 功能描述: 系统菜单的增删改查管理功能
 */

<template>
  <div class="menu-management-container">
    <!-- 页面标题和操作按钮 -->
    <div class="page-header">
      <h2 class="page-title">菜单管理</h2>
      <div class="page-actions">
        <el-button type="primary" @click="handleCreateMenu" :icon="Plus">
          新建菜单
        </el-button>
        <el-button type="success" @click="handleExpandAll" :icon="ZoomIn">
          展开所有
        </el-button>
        <el-button type="info" @click="handleCollapseAll" :icon="ZoomOut">
          折叠所有
        </el-button>
      </div>
    </div>

    <!-- 菜单树表格 -->
    <el-card class="data-card">
      <el-table
        ref="menuTable"
        v-loading="loading"
        :data="menuList"
        row-key="id"
        border
        default-expand-all
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      >
        <el-table-column prop="title" label="菜单名称" min-width="180">
          <template #default="scope">
            <span v-if="scope.row.type === 'menu'">
              <el-icon><Menu /></el-icon>
            </span>
            <span v-else-if="scope.row.type === 'directory'">
              <el-icon><Folder /></el-icon>
            </span>
            <span v-else>
              <el-icon><Link /></el-icon>
            </span>
            {{ scope.row.title }}
          </template>
        </el-table-column>
        <el-table-column prop="icon" label="图标" width="100">
          <template #default="scope">
            <el-icon v-if="scope.row.icon">
              <component :is="scope.row.icon" />
            </el-icon>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="path" label="路由路径" min-width="150" />
        <el-table-column prop="type" label="类型" width="100">
          <template #default="scope">
            <el-tag v-if="scope.row.type === 'directory'" type="warning">目录</el-tag>
            <el-tag v-else-if="scope.row.type === 'menu'" type="success">菜单</el-tag>
            <el-tag v-else-if="scope.row.type === 'button'" type="info">按钮</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="permission" label="权限标识" min-width="150" />
        <el-table-column prop="sort" label="排序" width="80" align="center" />
        <el-table-column prop="hidden" label="可见" width="80" align="center">
          <template #default="scope">
            <el-tag v-if="!scope.row.hidden" type="success">显示</el-tag>
            <el-tag v-else type="info">隐藏</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="230" fixed="right">
          <template #default="scope">
            <el-button 
              v-if="scope.row.type !== 'button'"
              type="text" 
              size="small" 
              @click="handleAddChild(scope.row)"
              :icon="Plus"
            >
              添加
            </el-button>
            <el-button 
              type="text" 
              size="small" 
              @click="handleEdit(scope.row)"
              :icon="Edit"
            >
              编辑
            </el-button>
            <el-button 
              type="text" 
              size="small" 
              @click="handleDelete(scope.row)"
              :icon="Delete"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Plus, Edit, Delete, ZoomIn, ZoomOut,
  Menu, Folder, Link
} from '@element-plus/icons-vue'

// 数据加载状态
const loading = ref(false)

// 菜单表格引用
const menuTable = ref(null)

// 菜单列表数据
const menuList = ref([])

// 生命周期钩子
onMounted(() => {
  fetchMenuList()
})

// 获取菜单列表
const fetchMenuList = () => {
  loading.value = true
  
  // 模拟API调用
  // 实际项目中应该调用API: const res = await getMenuList()
  setTimeout(() => {
    // 模拟数据
    menuList.value = [
      {
        id: 1,
        title: '仪表盘',
        icon: 'Odometer',
        path: '/main/dashboard',
        component: 'dashboard/index',
        type: 'menu',
        permission: 'dashboard:view',
        sort: 1,
        hidden: false
      },
      {
        id: 2,
        title: '资产管理',
        icon: 'Monitor',
        path: '/main/assets',
        component: 'assets/index',
        type: 'directory',
        permission: 'assets',
        sort: 2,
        hidden: false,
        children: [
          {
            id: 21,
            title: '资产列表',
            icon: 'List',
            path: '/main/assets/list',
            component: 'assets/list',
            type: 'menu',
            permission: 'assets:list',
            sort: 1,
            hidden: false,
            children: [
              {
                id: 211,
                title: '查看',
                icon: '',
                path: '',
                component: '',
                type: 'button',
                permission: 'assets:list:view',
                sort: 1,
                hidden: false
              },
              {
                id: 212,
                title: '新增',
                icon: '',
                path: '',
                component: '',
                type: 'button',
                permission: 'assets:list:add',
                sort: 2,
                hidden: false
              },
              {
                id: 213,
                title: '编辑',
                icon: '',
                path: '',
                component: '',
                type: 'button',
                permission: 'assets:list:edit',
                sort: 3,
                hidden: false
              },
              {
                id: 214,
                title: '删除',
                icon: '',
                path: '',
                component: '',
                type: 'button',
                permission: 'assets:list:delete',
                sort: 4,
                hidden: false
              }
            ]
          },
          {
            id: 22,
            title: '资产类型',
            icon: 'SetUp',
            path: '/main/assets/types',
            component: 'assets/types',
            type: 'menu',
            permission: 'assets:types',
            sort: 2,
            hidden: false
          }
        ]
      },
      {
        id: 3,
        title: '系统管理',
        icon: 'Setting',
        path: '/main/system',
        component: 'system/index',
        type: 'directory',
        permission: 'system',
        sort: 10,
        hidden: false,
        children: [
          {
            id: 31,
            title: '用户管理',
            icon: 'User',
            path: '/main/system/users',
            component: 'system/users',
            type: 'menu',
            permission: 'system:users',
            sort: 1,
            hidden: false
          },
          {
            id: 32,
            title: '角色管理',
            icon: 'UserFilled',
            path: '/main/system/roles',
            component: 'system/roles',
            type: 'menu',
            permission: 'system:roles',
            sort: 2,
            hidden: false
          },
          {
            id: 33,
            title: '菜单管理',
            icon: 'Menu',
            path: '/main/system/menus',
            component: 'system/menus',
            type: 'menu',
            permission: 'system:menus',
            sort: 3,
            hidden: false
          }
        ]
      }
    ]
    
    loading.value = false
  }, 500)
}

// 展开所有节点
const handleExpandAll = () => {
  menuTable.value.expandAllRows()
}

// 折叠所有节点
const handleCollapseAll = () => {
  menuTable.value.collapseAllRows()
}

// 添加子菜单
const handleAddChild = (row) => {
  ElMessage.info(`为菜单"${row.title}"添加子菜单`)
  // 实际项目中可以打开对话框添加子菜单
}

// 编辑菜单
const handleEdit = (row) => {
  ElMessage.info(`编辑菜单：${row.title}`)
  // 实际项目中可以打开编辑对话框
}

// 删除菜单
const handleDelete = (row) => {
  // 检查是否有子菜单
  if (row.children && row.children.length > 0) {
    ElMessage.warning('该菜单下有子菜单，请先删除子菜单')
    return
  }
  
  ElMessageBox.confirm(`确定要删除菜单"${row.title}"吗？`, '删除菜单', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'error'
  }).then(() => {
    // 模拟API调用
    // 实际项目中应该调用API: await deleteMenu(row.id)
    
    ElMessage.success('菜单已删除')
    
    // 刷新列表
    fetchMenuList()
  }).catch(() => {
    // 取消操作
  })
}

// 创建菜单
const handleCreateMenu = () => {
  ElMessage.info('打开新建菜单对话框')
  // 实际项目中可以打开对话框创建菜单
}
</script>

<style lang="scss" scoped>
@use "@/styles/variables.scss" as *;

.menu-management-container {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    
    .page-title {
      font-size: 20px;
      font-weight: 600;
      color: #303133;
      margin: 0;
    }
    
    .page-actions {
      display: flex;
      gap: 8px;
    }
  }
  
  .data-card {
    margin-bottom: 16px;
  }
}
</style> 