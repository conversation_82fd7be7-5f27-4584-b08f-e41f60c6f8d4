<template>
  <div class="location-relations-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2 class="page-title">位置关联</h2>
      <div class="page-actions">
        <el-button type="primary" @click="handleRelateAssets" :icon="Link">关联资产</el-button>
        <el-button type="success" @click="handleRelateDepartment" :icon="OfficeBuilding">关联部门</el-button>
        <el-button type="danger" @click="handleBatchUnrelate" :icon="Remove" :disabled="selectedAssets.length === 0">
          解除关联
        </el-button>
        <!-- 添加工位管理人员和使用人按钮，适用于工序(type=3)和工位(type=4)时显示 -->
        <el-button 
          v-if="currentLocationDetail && (currentLocationDetail.type === 3 || currentLocationDetail.type === 4)" 
          type="warning" 
          @click="handleSetManager" 
          :icon="UserFilled">
          设置管理员
        </el-button>
        <el-button 
          v-if="currentLocationDetail && (currentLocationDetail.type === 3 || currentLocationDetail.type === 4)" 
          type="info" 
          @click="handleSetUsers" 
          :icon="User">
          设置使用人
        </el-button>
      </div>
    </div>
    
    <!-- 主体内容 -->
    <div class="relation-content">
      <!-- 筛选区域 -->
      <el-card class="filter-card">
        <div class="filters">
          <el-row :gutter="16">
            <el-col :span="24">
              <div class="location-view-switch">
                <el-radio-group v-model="viewMode" size="small">
                  <el-radio-button value="tree">树形层级视图</el-radio-button>
                  <el-radio-button value="flat">扁平列表视图</el-radio-button>
                </el-radio-group>
              </div>
            </el-col>
          </el-row>
          
          <el-row :gutter="16" v-if="viewMode === 'flat'">
            <el-col :span="8">
              <el-form-item label="位置选择">
                <el-select
                  v-model="selectedLocation"
                  filterable
                  placeholder="请选择位置"
                  style="width: 100%"
                  @change="handleLocationChange"
                >
                  <el-option
                    v-for="location in locationOptions"
                    :key="location.id"
                    :label="location.fullName"
                    :value="location.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="部门">
                <el-select
                  v-model="departmentFilter"
                  placeholder="全部部门"
                  clearable
                  style="width: 100%"
                  @change="fetchAssetsForLocation"
                >
                  <el-option 
                    v-for="dept in departments" 
                    :key="dept.id" 
                    :label="dept.name" 
                    :value="dept.id" 
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="资产状态">
                <el-select
                  v-model="assetStatusFilter"
                  placeholder="全部状态"
                  clearable
                  style="width: 100%"
                  @change="fetchAssetsForLocation"
                >
                  <el-option 
                    v-for="status in assetStatuses" 
                    :key="status.value" 
                    :label="status.label" 
                    :value="status.value" 
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="16" v-if="viewMode === 'flat'">
            <el-col :span="8">
              <el-form-item label="关键字">
                <el-input
                  v-model="keywordFilter"
                  placeholder="搜索资产名称/编号/SN"
                  clearable
                  @keyup.enter="fetchAssetsForLocation"
                >
                  <template #append>
                    <el-button :icon="Search" @click="fetchAssetsForLocation" />
                  </template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="最后更新时间">
                <el-date-picker
                  v-model="dateRangeFilter"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  value-format="YYYY-MM-DD"
                  @change="fetchAssetsForLocation"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8" class="text-right">
              <el-button type="primary" @click="fetchAssetsForLocation" :icon="Search">搜索</el-button>
              <el-button @click="resetFilters" :icon="RefreshRight">重置</el-button>
            </el-col>
          </el-row>
          
          <el-row :gutter="16" v-if="viewMode === 'tree'">
            <el-col :span="24">
              <el-form-item label="位置层级查询">
                <el-input
                  v-model="treeSearchKeyword"
                  placeholder="搜索位置名称"
                  clearable
                  @keyup.enter="searchLocationTree"
                >
                  <template #append>
                    <el-button :icon="Search" @click="searchLocationTree" />
                  </template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-card>

      <!-- 数据表格/树形结构 -->
      <el-card class="data-card">
        <template #header>
          <div class="card-header">
            <div class="header-info">
              <span v-if="viewMode === 'flat'">位置列表</span>
              <span v-else>位置层级结构</span>
              <el-tag v-if="selectedDepartment && viewMode === 'flat'" type="success" class="department-tag">
                {{ selectedDepartment.name }}
              </el-tag>
            </div>
            <span class="location-path" v-if="viewMode === 'flat'">当前位置：{{ currentLocationPath }}</span>
          </div>
        </template>
        
        <!-- 树形层级视图 -->
        <div v-if="viewMode === 'tree'" class="tree-view-container">
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="tree-container">
                <div class="tree-title">位置层级结构</div>
                <el-scrollbar height="calc(100vh - 350px)" style="border: 1px solid #EBEEF5;">
                  <el-skeleton :loading="loading" animated :rows="10" v-if="loading">
                  </el-skeleton>
                  <el-tree
                    v-else
                    ref="locationTree"
                    :data="locationTreeData"
                    :props="locationTreeProps"
                    node-key="id"
                    highlight-current
                    :default-expand-all="false"
                    :expand-on-click-node="false"
                    @node-click="handleLocationNodeClick"
                    :filter-node-method="filterLocationNode"
                    v-loading="loading"
                  >
                    <template #default="{ node, data }">
                      <div class="location-tree-node">
                        <div class="node-content">
                          <span class="location-name">{{ data.name }}</span>
                          <span class="location-type">
                            <el-tag size="small" :type="getLocationTypeTag(data.type)">
                              {{ getLocationTypeName(data.type) }}
                            </el-tag>
                          </span>
                        </div>
                      </div>
                    </template>
                  </el-tree>
                </el-scrollbar>
              </div>
            </el-col>
            
            <el-col :span="16">
              <div class="location-detail-panel" v-if="selectedLocationForTree">
                <div class="detail-header">
                  <h3>{{ selectedLocationForTree.name }}</h3>
                  <div class="location-path">{{ currentLocationPath }}</div>
                  <div class="location-actions" v-if="selectedLocationForTree.type === 3 || selectedLocationForTree.type === 4">
                    <el-button type="primary" size="small" @click="handleSetManager(selectedLocationForTree)">
                      设置管理员
                    </el-button>
                    <el-button type="success" size="small" @click="handleSetUsers(selectedLocationForTree)">
                      设置使用人
                    </el-button>
                  </div>
                </div>
                
                <el-tabs v-model="activeDetailTab">
                  <el-tab-pane label="位置信息" name="info">
                    <el-descriptions :column="2" border>
                      <el-descriptions-item label="位置名称">{{ selectedLocationForTree.name }}</el-descriptions-item>
                      <el-descriptions-item label="位置类型">
                        <el-tag :type="getLocationTypeTag(selectedLocationForTree.type)">
                          {{ getLocationTypeName(selectedLocationForTree.type) }}
                        </el-tag>
                      </el-descriptions-item>
                      <el-descriptions-item label="上级位置">{{ selectedLocationForTree.parentName || '-' }}</el-descriptions-item>
                      <el-descriptions-item label="使用部门">{{ selectedLocationForTree.departmentName || '-' }}</el-descriptions-item>
                      <el-descriptions-item label="管理人员" :span="2">
                        <div v-if="locationUsersForTree.length > 0">
                          <el-tag 
                            v-for="manager in locationUsersForTree.filter(u => u.userType === 1)" 
                            :key="manager.id"
                            type="warning"
                            style="margin-right: 5px; margin-bottom: 5px"
                          >
                            {{ manager.name || manager.username }}
                          </el-tag>
                          <span v-if="!locationUsersForTree.some(u => u.userType === 1)">未设置</span>
                        </div>
                        <span v-else>未设置</span>
                      </el-descriptions-item>
                      <el-descriptions-item label="使用人员" :span="2">
                        <div v-if="locationUsersForTree.length > 0">
                          <el-tag 
                            v-for="user in locationUsersForTree.filter(u => u.userType === 0)" 
                            :key="user.id"
                            type="info"
                            style="margin-right: 5px; margin-bottom: 5px"
                          >
                            {{ user.name || user.username }}
                          </el-tag>
                          <span v-if="!locationUsersForTree.some(u => u.userType === 0)">未设置</span>
                        </div>
                        <span v-else>未设置</span>
                      </el-descriptions-item>
                      <el-descriptions-item label="备注信息" :span="2">
                        {{ selectedLocationForTree.remark || '-' }}
                      </el-descriptions-item>
                    </el-descriptions>
                    
                    <div class="personnel-info" v-if="selectedLocationForTree.type === 3 || selectedLocationForTree.type === 4">
                      <el-alert
                        title="位置人员管理说明"
                        type="info"
                        :closable="false"
                        style="margin: 20px 0"
                      >
                        <div class="info-content">
                          <p>1. 工序和工位可以设置管理员和使用人员</p>
                          <p>2. 管理员负责此位置的资产和人员管理</p>
                          <p>3. 使用人员是日常使用此位置资产的人员</p>
                          <p>4. 人员信息来自系统用户管理，请确保先添加相关人员</p>
                        </div>
                      </el-alert>
                    </div>
                  </el-tab-pane>
                  
                  <el-tab-pane label="关联资产" name="assets">
                    <div v-loading="treeAssetLoading">
                      <el-empty v-if="locationAssetsForTree.length === 0" description="暂无关联资产"></el-empty>
                      <el-table
                        v-else
                        :data="locationAssetsForTree"
                        style="width: 100%"
                        border
                        stripe
                        size="small"
                      >
                        <el-table-column type="index" label="序号" width="60" align="center"></el-table-column>
                        <el-table-column prop="assetCode" label="资产编号" width="120"></el-table-column>
                        <el-table-column prop="name" label="资产名称" width="150"></el-table-column>
                        <el-table-column prop="assetTypeName" label="资产类型" width="120"></el-table-column>
                        <el-table-column prop="model" label="规格型号" width="120"></el-table-column>
                        <el-table-column label="状态" width="80">
                          <template #default="{ row }">
                            <el-tag :type="getAssetStatusType(row.status)">
                              {{ getAssetStatusText(row.status) }}
                            </el-tag>
                          </template>
                        </el-table-column>
                        <el-table-column label="操作" width="150" fixed="right">
                          <template #default="{ row }">
                            <el-button link @click="viewAssetDetail(row)">查看</el-button>
                            <el-button link class="danger-text" @click="handleUnrelateAsset(row)">解除关联</el-button>
                          </template>
                        </el-table-column>
                      </el-table>
                    </div>
                  </el-tab-pane>
                </el-tabs>
              </div>
              <el-empty 
                v-else 
                description="请选择左侧位置"
                style="margin-top: 100px"
              ></el-empty>
            </el-col>
          </el-row>
        </div>
        
        <!-- 扁平资产列表视图 -->
        <div v-if="viewMode === 'flat'" class="flat-view">
          <!-- 筛选条件 -->
          <el-form :inline="true" class="location-filters">
            <el-form-item label="位置类型">
              <el-select v-model="typeFilter" clearable placeholder="全部类型" style="width: 150px;">
                <el-option label="全部" value=""></el-option>
                <el-option label="工厂" :value="1"></el-option>
                <el-option label="产线" :value="2"></el-option>
                <el-option label="工序" :value="3"></el-option>
                <el-option label="工位" :value="4"></el-option>
              </el-select>
            </el-form-item>
            
            <el-form-item label="上级位置">
              <el-select v-model="parentFilter" filterable clearable placeholder="请选择" style="width: 200px;">
                <el-option 
                  v-for="item in parentOptions" 
                  :key="item.id" 
                  :label="item.name" 
                  :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
            
            <el-form-item label="关键字">
              <el-input v-model="keywordFilter" placeholder="位置名称/编码" style="width: 200px;" />
            </el-form-item>
            
            <el-form-item>
              <el-button type="primary" @click="fetchLocationList">搜索</el-button>
              <el-button @click="resetLocationFilters">重置</el-button>
            </el-form-item>
          </el-form>
          
          <!-- 位置列表表格 -->
          <el-table
            ref="locationTable"
            v-loading="loading"
            :data="locationList"
            style="width: 100%"
            @row-click="handleLocationClick"
            :height="tableHeight"
            border
          >
            <el-table-column type="selection" width="50" />
            <el-table-column prop="code" label="位置编码" width="120" />
            <el-table-column prop="name" label="位置名称" min-width="150" />
            <el-table-column prop="type" label="位置类型" width="100">
              <template #default="scope">
                <el-tag size="small" :type="getLocationTypeTag(scope.row.type)">
                  {{ getLocationTypeName(scope.row.type) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="parentName" label="上级位置" min-width="150" />
            <el-table-column prop="departmentName" label="使用部门" min-width="120">
              <template #default="scope">
                <span v-if="scope.row.departmentName">{{ scope.row.departmentName }}</span>
                <el-button v-else type="primary" link @click.stop="handleRelateDepartment(scope.row)">设置部门</el-button>
              </template>
            </el-table-column>
            <el-table-column prop="managerName" label="负责人" width="120">
              <template #default="scope">
                <span v-if="scope.row.managerName">{{ scope.row.managerName }}</span>
                <el-button v-else-if="scope.row.type === 3 || scope.row.type === 4" type="warning" link @click.stop="handleSetManager(scope.row)">设置负责人</el-button>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column label="使用人" width="100">
              <template #default="scope">
                <el-button v-if="scope.row.type === 3 || scope.row.type === 4" type="info" link @click.stop="viewLocationUsers(scope.row)">
                  {{ scope.row.userCount || 0 }}人
                </el-button>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" fixed="right" width="220">
              <template #default="scope">
                <el-button size="small" type="primary" @click.stop="handleRelateAssets(scope.row)">关联资产</el-button>
                <el-dropdown @command="(cmd) => handleCommand(cmd, scope.row)" trigger="click">
                  <el-button size="small" type="primary">
                    更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="setDepartment">设置部门</el-dropdown-item>
                      <el-dropdown-item v-if="scope.row.type === 3 || scope.row.type === 4" command="setManager">设置负责人</el-dropdown-item>
                      <el-dropdown-item v-if="scope.row.type === 3 || scope.row.type === 4" command="setUsers">设置使用人</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </template>
            </el-table-column>
          </el-table>
          
          <!-- 分页 -->
          <div class="pagination-container">
            <el-pagination
              v-model:current-page="pagination.currentPage"
              v-model:page-size="pagination.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              :total="pagination.total"
              background
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>
      </el-card>
    </div>
    
    <!-- 关联资产对话框 -->
    <el-dialog
      title="关联资产"
      v-model="relateDialogVisible"
      width="900px"
      append-to-body
    >
      <div class="relate-dialog-content">
        <div class="filters">
          <el-row :gutter="16">
            <el-col :span="8">
              <el-input
                v-model="assetSearchKeyword"
                placeholder="搜索资产名称/编号/SN"
                clearable
                @keyup.enter="searchUnrelatedAssets"
              >
                <template #append>
                  <el-button :icon="Search" @click="searchUnrelatedAssets" />
                </template>
              </el-input>
            </el-col>
            <el-col :span="8">
              <el-select
                v-model="unrelatedAssetTypeFilter"
                placeholder="资产类型"
                clearable
                style="width: 100%"
                @change="searchUnrelatedAssets"
              >
                <el-option 
                  v-for="type in assetTypes" 
                  :key="type.value" 
                  :label="type.label" 
                  :value="type.value" 
                />
              </el-select>
            </el-col>
            <el-col :span="8">
              <el-select
                v-model="relationType"
                placeholder="关联类型"
                style="width: 100%"
              >
                <el-option label="主要位置" value="primary" />
                <el-option label="次要位置" value="secondary" />
                <el-option label="临时位置" value="temporary" />
              </el-select>
            </el-col>
          </el-row>
        </div>
        
        <el-table
          ref="unrelatedAssetTable"
          v-loading="unrelatedLoading"
          :data="unrelatedAssetList"
          style="width: 100%; margin-top: 16px;"
          @selection-change="handleUnrelatedSelectionChange"
          height="400px"
          border
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="code" label="资产编号" width="120" />
          <el-table-column prop="name" label="资产名称" width="150" />
          <el-table-column prop="type" label="资产类型" width="120">
            <template #default="scope">
              <el-tag size="small" :type="getAssetTypeTag(scope.row.type)">
                {{ scope.row.typeName }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="sn" label="序列号" width="150" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag size="small" :type="getStatusType(scope.row.status)">
                {{ scope.row.statusText }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="department" label="所属部门" width="150" />
          <el-table-column prop="user" label="责任人" width="120" />
          <el-table-column prop="currentLocation" label="当前位置" width="180" />
        </el-table>
        
        <div class="dialog-pagination">
          <el-pagination
            v-model:current-page="unrelatedPagination.currentPage"
            v-model:page-size="unrelatedPagination.pageSize"
            :page-sizes="[10, 20, 50]"
            :background="true"
            layout="total, sizes, prev, pager, next"
            :total="unrelatedPagination.total"
            @size-change="handleUnrelatedSizeChange"
            @current-change="handleUnrelatedCurrentChange"
          />
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="relateDialogVisible = false">取 消</el-button>
          <el-button 
            type="primary" 
            @click="confirmRelateAssets" 
            :disabled="selectedUnrelatedAssets.length === 0 || !relationType"
          >
            确认关联
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 部门关联对话框 -->
    <el-dialog
      v-model="departmentDialogVisible"
      title="关联部门"
      width="500px"
    >
      <el-form
        ref="departmentForm"
        :model="departmentForm"
        :rules="departmentRules"
        label-width="100px"
      >
        <el-form-item label="选择部门" prop="departmentId">
          <el-select
            v-model="departmentForm.departmentId"
            filterable
            placeholder="请选择部门"
            style="width: 100%"
          >
            <el-option
              v-for="dept in departmentOptions"
              :key="dept.id"
              :label="dept.name"
              :value="dept.id"
            />
          </el-select>
        </el-form-item>
        <el-alert
          v-if="departmentForm.departmentId"
          type="info"
          show-icon
          :closable="false"
        >
          <div>
            <p>设置部门后，系统将自动关联该部门的负责人作为位置管理员。</p>
            <p v-if="selectedDepartmentManager">
              当前选择部门负责人: <strong>{{ selectedDepartmentManager }}</strong>
            </p>
            <p v-else>
              当前选择部门未设置负责人
            </p>
          </div>
        </el-alert>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="departmentDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitDepartment" :loading="departmentSubmitting">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 人员关联对话框 -->
    <el-dialog
      v-model="userDialogVisible"
      :title="`${userDialogType === 'manager' ? '设置管理员' : '设置使用人'}`"
      width="600px"
    >
      <el-form
        ref="userForm"
        :model="userForm"
        label-width="100px"
      >
        <el-form-item label="选择人员">
          <el-select
            v-model="userForm.selectedUsers"
            filterable
            multiple
            placeholder="请选择人员"
            style="width: 100%"
          >
            <el-option
              v-for="person in personnelOptions"
              :key="person.id"
              :label="`${person.name}${person.employeeCode ? ' (' + person.employeeCode + ')' : ''}`"
              :value="person.id"
            >
              <div class="personnel-option">
                <div>
                  <span class="personnel-name">{{ person.name }}</span>
                  <span class="personnel-code" v-if="person.employeeCode">({{ person.employeeCode }})</span>
                </div>
                <span class="personnel-dept" v-if="person.departmentName">{{ person.departmentName }}</span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="操作模式">
          <el-radio-group v-model="userForm.replaceExisting">
            <el-radio :label="true">替换现有人员</el-radio>
            <el-radio :label="false">追加新人员</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <div v-if="locationUsers.length > 0">
          <div class="current-users-title">当前关联人员:</div>
          <el-table :data="locationUsers" size="small" border style="width: 100%">
            <el-table-column label="姓名" prop="name" />
            <el-table-column label="工号" prop="employeeCode" />
            <el-table-column label="类型">
              <template #default="scope">
                <el-tag
                  :type="scope.row.userType === 1 ? 'success' : 'info'"
                >
                  {{ scope.row.userType === 1 ? '管理员' : '使用人' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="80">
              <template #default="scope">
                <el-button
                  type="danger"
                  size="small"
                  text
                  @click="removeUser(scope.row)"
                >
                  移除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="userDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitUsers" :loading="userSubmitting">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
  
  <!-- 添加设置部门对话框组件 -->
  <set-department-dialog
    v-if="showSetDepartmentDialog"
    v-model:visible="showSetDepartmentDialog"
    :location-id="currentLocationDetail?.id"
    :location-name="currentLocationDetail?.name"
    :current-department-id="currentLocationDetail?.defaultDepartmentId"
    @success="handleSetDepartmentSuccess"
  />

  <!-- 添加设置人员对话框组件 -->
  <set-location-users-dialog
    v-model:visible="showUserDialog"
    :location-id="currentLocationDetail?.id"
    :location-name="currentLocationDetail?.name"
    :user-type="userDialogType"
    :department-info="currentLocationDetail?.defaultDepartmentName"
    @success="handleUserDialogSuccess"
  />
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick, watch, onBeforeUnmount } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Search, RefreshRight, Link, Remove, UserFilled, User, OfficeBuilding, ArrowDown
} from '@element-plus/icons-vue'
import locationApi from '@/api/location'
import assetApi from '@/api/asset'
import departmentApi from '@/api/department'
import personnelApi from '@/api/personnel'

// 引入部门和人员对话框组件
import SetDepartmentDialog from './components/SetDepartmentDialog.vue'
import SetLocationUsersDialog from './components/SetLocationUsersDialog.vue'

// 表格高度自适应
const tableHeight = ref(450)

// 视图模式切换：flat=扁平列表视图，tree=树形层级视图
const viewMode = ref('tree')

// 位置数据
const locationData = ref([])
const selectedLocation = ref(null)
const currentLocationPath = ref('未选择')
const locationOptions = ref([])
const currentLocationDetail = ref(null)

// 树形视图相关数据
const locationTreeData = ref([])
const locationTreeProps = {
  children: 'children',
  label: 'name'
}
const selectedLocationForTree = ref(null)
const locationUsersForTree = ref([])
const locationAssetsForTree = ref([])
const treeAssetLoading = ref(false)
const treeSearchKeyword = ref('')
const locationTree = ref(null)
const activeDetailTab = ref('info')

// 部门数据
const departments = ref([])
const departmentFilter = ref('')
const selectedDepartment = ref(null)

// 资产列表数据
const assetList = ref([])
const loading = ref(false)
const selectedAssets = ref([])
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 筛选条件
const assetStatusFilter = ref('')
const keywordFilter = ref('')
const dateRangeFilter = ref([])
const typeFilter = ref('')
const parentFilter = ref(null)

// 关联资产对话框
const relateDialogVisible = ref(false)
const unrelatedAssetList = ref([])
const unrelatedLoading = ref(false)
const selectedUnrelatedAssets = ref([])
const assetSearchKeyword = ref('')
const unrelatedAssetTypeFilter = ref('')
const relationType = ref('primary')
const unrelatedPagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 部门关联对话框
const departmentDialogVisible = ref(false)
const departmentSubmitting = ref(false)
const departmentForm = reactive({
  departmentId: null
})
const departmentRules = {
  departmentId: [
    { required: true, message: '请选择部门', trigger: 'change' }
  ]
}

// 人员关联对话框
const userDialogVisible = ref(false)
const userSubmitting = ref(false)
const userForm = reactive({
  selectedUsers: [],
  replaceExisting: false
})

// 资产状态选项
const assetStatuses = [
  { label: '在用', value: 'in_use' },
  { label: '闲置', value: 'idle' },
  { label: '维修中', value: 'repairing' },
  { label: '借用', value: 'borrowed' },
  { label: '报废', value: 'scrapped' }
]

// 部门选项
const departmentOptions = ref([])
// 人员选项
const personnelOptions = ref([])
// 位置关联人员
const locationUsers = ref([])
// 当前选中部门的经理名称
const selectedDepartmentManager = ref('')

// 添加人员对话框相关变量
// 使用人员对话框
const showUserDialog = ref(false)
const userDialogType = ref('user') // user 或 manager

// 部门对话框
const showSetDepartmentDialog = ref(false)

// 关联部门方法
const handleRelateDepartment = (row = null) => {
  // 确定要操作的位置，可以是传入的行或当前选择的位置
  const targetLocation = row || currentLocationDetail.value;
  
  if (!targetLocation) {
    ElMessage.warning('请先选择位置');
    return;
  }
  
  // 更新当前选择的位置
  if (row) {
    currentLocationDetail.value = row;
    selectedLocation.value = row.id;
  }
  
  // 加载部门列表
  fetchDepartments();
  
  // 设置当前选中的部门ID
  departmentForm.departmentId = currentLocationDetail.value?.defaultDepartmentId || null;

  // 如果有设置部门，更新部门经理信息
  if (departmentForm.departmentId) {
    updateSelectedDepartmentManager();
  }

  // 显示对话框
  showSetDepartmentDialog.value = true;
}

// 更新选中部门的经理信息
const updateSelectedDepartmentManager = () => {
  selectedDepartmentManager.value = '';
  
  if (!departmentForm.departmentId) return;
  
  const selectedDept = departmentOptions.value.find(d => d.id === departmentForm.departmentId);
  if (selectedDept && selectedDept.managerId) {
    const manager = personnelOptions.value.find(p => p.id === selectedDept.managerId);
    if (manager) {
      selectedDepartmentManager.value = `${manager.name}${manager.employeeCode ? ' (' + manager.employeeCode + ')' : ''}`;
    } else {
      selectedDepartmentManager.value = `ID: ${selectedDept.managerId}`;
    }
  }
}

// 处理设置部门成功回调
const handleSetDepartmentSuccess = async () => {
  // 刷新当前位置信息
  if (currentLocationDetail.value) {
    await fetchLocationDetail(currentLocationDetail.value.id)
  }
  ElMessage.success('位置部门设置成功')
}

// 处理人员对话框成功关闭
const handleUserDialogSuccess = () => {
  // 刷新当前位置信息
  if (currentLocationDetail.value) {
    fetchLocationDetail(currentLocationDetail.value.id)
  }
}

// 更新设置管理员方法，使其支持工序和工位
const handleSetManager = async (row = null) => {
  // 确定要操作的位置，可以是传入的行或当前选择的位置
  const targetLocation = row || currentLocationDetail.value;
  
  if (!targetLocation || (targetLocation.type !== 3 && targetLocation.type !== 4)) {
    ElMessage.warning('只能为工序或工位类型的位置设置管理员');
    return;
  }
  
  // 更新当前选择的位置
  if (row) {
    currentLocationDetail.value = row;
    selectedLocation.value = row.id;
  }
  
  // 打开管理员设置对话框
  userDialogType.value = 'manager';
  showUserDialog.value = true;
}

// 更新设置使用人方法，使其支持工序和工位
const handleSetUsers = async (row = null) => {
  // 确定要操作的位置，可以是传入的行或当前选择的位置
  const targetLocation = row || currentLocationDetail.value;
  
  if (!targetLocation || (targetLocation.type !== 3 && targetLocation.type !== 4)) {
    ElMessage.warning('只能为工序或工位类型的位置设置使用人');
    return;
  }
  
  // 更新当前选择的位置
  if (row) {
    currentLocationDetail.value = row;
    selectedLocation.value = row.id;
  }
  
  // 打开使用人设置对话框
  userDialogType.value = 'user';
  showUserDialog.value = true;
}

// 初始化数据加载
const fetchInitialData = async () => {
  loading.value = true;
  
  try {
    // 先加载基础数据
    try {
      await fetchDepartments();
      console.log('部门数据加载成功');
    } catch (error) {
      console.error('部门数据加载失败:', error);
      ElMessage.warning('部门数据加载失败，部分功能可能受限');
    }
    
    try {
      await fetchLocationOptions();
      console.log('位置选项加载成功');
    } catch (error) {
      console.error('位置选项加载失败:', error);
      ElMessage.warning('位置选项加载失败，部分功能可能受限');
    }
    
    // 根据当前视图模式加载相应数据
    if (viewMode.value === 'tree') {
      await fetchLocationTreeData();
    } else {
      await fetchLocationList();
    }
  } catch (error) {
    console.error('初始化数据失败:', error);
    ElMessage.error('初始化数据失败，请刷新页面重试');
  } finally {
    loading.value = false;
  }
};

// 获取树形数据的方法
const fetchLocationTree = async () => {
  try {
    const res = await locationApi.getLocationTree();
    if (res.success) {
      locationTreeData.value = res.data || [];
    } else {
      ElMessage.error(res.message || '获取位置树形结构失败');
    }
  } catch (error) {
    ElMessage.error('获取位置树形结构失败');
  }
};

// 在搜索框输入时过滤树节点
const filterLocationNode = (value, data) => {
  if (!value) return true;
  const searchValue = value.toLowerCase();
  const nameMatch = data.name && data.name.toLowerCase().includes(searchValue);
  const codeMatch = data.code && data.code.toLowerCase().includes(searchValue);
  const typeMatch = getLocationTypeName(data.type).toLowerCase().includes(searchValue);
  return nameMatch || codeMatch || typeMatch;
};

// 获取部门列表
const fetchDepartments = async () => {
  try {
    const res = await departmentApi.getDepartmentList()
    if (res && res.data && res.data.success) {
      departments.value = res.data.data || []
      console.log(`成功加载 ${departments.value.length} 个部门`);
    } else {
      console.error('获取部门列表失败:', res?.data?.message || '未知错误')
      departments.value = []
      throw new Error(res?.data?.message || '获取部门列表失败')
    }
  } catch (error) {
    console.error('获取部门列表失败:', error)
    departments.value = []
    throw error
  }
}

// 监听部门变化
watch(() => departmentForm.departmentId, (newValue) => {
  if (newValue) {
    updateSelectedDepartmentManager();
  } else {
    selectedDepartmentManager.value = '';
  }
});

// 获取位置树数据
const fetchLocationTreeData = async () => {
  try {
    loading.value = true;
    const treeRes = await locationApi.getLocationTree();
    
    if (treeRes && treeRes.success) {
      locationTreeData.value = treeRes.data || [];
      nextTick(() => {
        if (locationTree.value && locationTreeData.value.length > 0) {
          locationTreeData.value.forEach(node => {
            if (locationTree.value.store && locationTree.value.store.nodesMap && locationTree.value.store.nodesMap[node.id]) {
              locationTree.value.store.nodesMap[node.id].expanded = true;
            }
          });
        }
      });
    } else {
      ElMessage.error('获取位置树形数据失败，请刷新重试');
      locationTreeData.value = [];
    }
  } catch (error) {
    ElMessage.error('加载位置树数据失败');
    locationTreeData.value = [];
  } finally {
    loading.value = false;
  }
}

// 搜索位置树
const searchLocationTree = () => {
  locationTree.value?.filter(treeSearchKeyword.value)
}

// 处理树节点点击事件
const handleLocationNodeClick = async (data, node) => {
  selectedLocationForTree.value = data;
  currentLocationDetail.value = data;
  currentLocationPath.value = data.fullPath || data.name;
  
  if (window._locationClickTimer) {
    clearTimeout(window._locationClickTimer);
  }
  
  window._locationClickTimer = setTimeout(async () => {
    treeAssetLoading.value = true;
    
    try {
      if (data.type === 3 || data.type === 4) {
        await loadLocationUsers(data.id);
      } else {
        locationUsersForTree.value = [];
      }
      
      await fetchLocationAssets(data.id);
    } catch (error) {
      console.error('加载位置节点数据失败:', error);
    } finally {
      treeAssetLoading.value = false;
    }
  }, 300);
}

// 获取位置类型文本
const getLocationTypeName = (type) => {
  const typeMap = {
    0: "工厂",
    1: "车间", 
    2: "产线", 
    3: "工序", 
    4: "工位"
  }
  return typeMap[type] || '未知'
}

// 获取位置类型标签样式
const getLocationTypeTag = (type) => {
  const typeTagMap = {
    0: "danger",    // 工厂
    1: "warning",   // 车间
    2: "primary",   // 产线 
    3: "success",   // 工序
    4: "info"       // 工位
  }
  return typeTagMap[type] || ''
}

// 扁平列表视图 - 获取位置列表
const fetchLocationList = async () => {
  loading.value = true;
  try {
    const params = {
      pageIndex: pagination.currentPage,
      pageSize: pagination.pageSize
    };
    
    if (typeFilter.value) {
      params.type = typeFilter.value;
    }
    
    if (parentFilter.value) {
      params.parentId = parentFilter.value;
    }
    
    if (keywordFilter.value) {
      params.keyword = keywordFilter.value;
    }
    
    const res = await locationApi.searchLocations(params);
    
    if (res && res.success) {
      locationList.value = res.data?.items || [];
      pagination.total = res.data?.total || 0;
    } else {
      ElMessage.error(res?.message || '获取位置列表失败');
      locationList.value = [];
      pagination.total = 0;
    }
  } catch (error) {
    ElMessage.error('获取位置列表失败，请检查网络连接');
    locationList.value = [];
    pagination.total = 0;
  } finally {
    loading.value = false;
  }
}

// 扁平列表视图 - 重置筛选条件
const resetLocationFilters = () => {
  typeFilter.value = ''
  parentFilter.value = null
  keywordFilter.value = ''
  pagination.currentPage = 1
  fetchLocationList()
}

// 处理位置点击
const handleLocationClick = (row) => {
  selectedLocation.value = row.id
  currentLocationDetail.value = row
  currentLocationPath.value = row.fullPath || row.name
  
  if (row.type === 3 || row.type === 4) {
    loadLocationUsers(row.id)
  }
  
  fetchAssetsForLocation()
}

// 查看位置使用人
const viewLocationUsers = async (row) => {
  try {
    currentLocationDetail.value = row
    usersDialogVisible.value = true
    locationUsersLoading.value = true
    
    const res = await locationApi.getLocationUsers(row.id)
    if (res.success) {
      locationUsers.value = res.data || []
    } else {
      locationUsers.value = []
      ElMessage.error(res.message || '获取使用人列表失败')
    }
  } catch (error) {
    locationUsers.value = []
    ElMessage.error('获取使用人列表失败')
  } finally {
    locationUsersLoading.value = false
  }
}

// 多功能命令处理
const handleCommand = (command, row) => {
  switch (command) {
    case 'setDepartment':
      handleRelateDepartment(row);
      break;
    case 'setManager':
      handleSetManager(row);
      break;
    case 'setUsers':
      handleSetUsers(row);
      break;
  }
}

// 分页处理
const handleSizeChange = (size) => {
  pagination.pageSize = size
  if (viewMode.value === 'flat') {
    fetchLocationList()
  } else {
    fetchAssetsForLocation()
  }
}

const handleCurrentChange = (page) => {
  pagination.currentPage = page
  if (viewMode.value === 'flat') {
    fetchLocationList()
  } else {
    fetchAssetsForLocation()
  }
}

// 获取资产列表
const fetchAssetsForLocation = async () => {
  if (!selectedLocation.value && !selectedLocationForTree.value) {
    assetList.value = [];
    pagination.total = 0;
    return;
  }
  
  loading.value = true;
  try {
    const locationId = selectedLocation.value || 
                      (selectedLocationForTree.value ? selectedLocationForTree.value.id : null);
    
    if (!locationId) {
      assetList.value = [];
      pagination.total = 0;
      return;
    }
    
    const params = {
      locationId: locationId,
      pageSize: pagination.pageSize,
      pageIndex: pagination.currentPage,
      keyword: keywordFilter.value || '',
      status: assetStatusFilter.value || null,
      startDate: dateRangeFilter.value && dateRangeFilter.value[0] ? dateRangeFilter.value[0] : null,
      endDate: dateRangeFilter.value && dateRangeFilter.value[1] ? dateRangeFilter.value[1] : null,
      departmentId: departmentFilter.value || null
    };
    
    const res = await locationApi.getLocationAssets(params);
    
    if (res && res.success) {
      assetList.value = res.data.items || [];
      pagination.total = res.data.total || 0;
    } else {
      assetList.value = [];
      pagination.total = 0;
    }
  } catch (error) {
    ElMessage.error('获取资产列表失败');
    assetList.value = [];
    pagination.total = 0;
  } finally {
    loading.value = false;
  }
}

// 位置选择变更
const handleLocationChange = async (locationId) => {
  currentLocationDetail.value = null
  
  if (!locationId) {
    selectedLocation.value = null
    currentLocationPath.value = '未选择'
    resetFilters(false)
    return
  }
  
  selectedLocation.value = locationId
  
  try {
    loading.value = true
    const res = await locationApi.getLocationDetail(locationId)
    if (res.success) {
      currentLocationDetail.value = res.data
      currentLocationPath.value = res.data.path ? res.data.path.replace(/,/g, ' > ') : res.data.name
      
      if (res.data.defaultDepartmentId) {
        departmentFilter.value = res.data.defaultDepartmentId
        
        const dept = departments.value.find(d => d.id === res.data.defaultDepartmentId)
        if (dept) {
          selectedDepartment.value = dept
        } else {
          await fetchDepartments()
          const reloadedDept = departments.value.find(d => d.id === res.data.defaultDepartmentId)
          if (reloadedDept) {
            selectedDepartment.value = reloadedDept
          }
        }
      } else {
        departmentFilter.value = null
        selectedDepartment.value = null
      }
    }
  } catch (error) {
    ElMessage.error('获取位置详情失败')
  } finally {
    loading.value = false
  }
  
  assetStatusFilter.value = null
  keywordFilter.value = ''
  dateRangeFilter.value = null
  
  fetchAssetsForLocation()
}

// 重置筛选条件
const resetFilters = (includeLocation = true) => {
  if (includeLocation) {
    selectedLocation.value = null
  }
  departmentFilter.value = null
  assetStatusFilter.value = null
  keywordFilter.value = ''
  dateRangeFilter.value = null
  fetchAssetsForLocation()
}

// 关联资产按钮点击处理
const handleRelateAssets = () => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  relateDialogVisible.value = true
  assetSearchKeyword.value = ''
  unrelatedAssetTypeFilter.value = null
  relationType.value = 'primary'
  selectedUnrelatedAssets.value = []
  unrelatedPagination.currentPage = 1
  searchUnrelatedAssets()
}

// 单个解除关联
const handleUnrelate = (asset) => {
  ElMessageBox.confirm(`确认解除资产 ${asset.code || asset.assetCode} 与当前位置的关联关系吗?`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      loading.value = true
      const result = await assetApi.changeAssetLocation(asset.id, {
        newLocationId: 0,
        reason: '手动解除关联',
        notes: '从位置关联管理页面解除'
      })
      
      if (result && result.success) {
        ElMessage.success('解除关联成功')
        fetchAssetsForLocation()
      } else {
        ElMessage.error(result?.message || '解除关联失败')
      }
    } catch (error) {
      ElMessage.error('解除关联失败，请检查网络连接')
    } finally {
      loading.value = false
    }
  }).catch(() => {})
}

// 批量解除关联
const handleBatchUnrelate = () => {
  if (selectedAssets.value.length === 0) {
    ElMessage.warning('请选择要解除关联的资产')
    return
  }
  
  const assetNames = selectedAssets.value.map(asset => asset.name || asset.assetCode).join(', ')
  
  ElMessageBox.confirm(`确认解除资产 [${assetNames}] 与当前位置的关联关系吗?`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      loading.value = true
      const promises = selectedAssets.value.map(asset => 
        assetApi.changeAssetLocation(asset.id, {
          newLocationId: 0,
          reason: '批量解除关联',
          notes: '从位置关联管理页面批量解除'
        })
      )
      
      const results = await Promise.allSettled(promises)
      const succeeded = results.filter(r => r.status === 'fulfilled' && r.value?.success).length
      const failed = results.length - succeeded
      
      if (failed === 0) {
        ElMessage.success(`成功解除 ${succeeded} 个资产的关联`)
      } else if (succeeded === 0) {
        ElMessage.error(`解除关联失败，请重试`)
      } else {
        ElMessage.warning(`部分资产解除关联成功，${succeeded} 成功，${failed} 失败`)
      }
      
      fetchAssetsForLocation()
    } catch (error) {
      ElMessage.error('批量解除关联失败，请检查网络连接')
    } finally {
      loading.value = false
      selectedAssets.value = []
    }
  }).catch(() => {})
}

// 获取未关联资产列表
const fetchUnrelatedAssets = async () => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  unrelatedLoading.value = true
  
  try {
    const params = {
      locationId: selectedLocation.value,
      unrelated: true,
      pageIndex: unrelatedPagination.currentPage,
      pageSize: unrelatedPagination.pageSize,
      keyword: assetSearchKeyword.value,
      assetTypeId: unrelatedAssetTypeFilter.value
    }
    
    const res = await assetApi.getAssetList(params)
    
    if (res.success) {
      unrelatedAssetList.value = (res.data?.items || []).map(asset => ({
        id: asset.id,
        code: asset.assetCode,
        name: asset.name,
        type: asset.assetTypeId,
        typeName: asset.assetTypeName,
        sn: asset.serialNumber || '-',
        status: asset.status || 0,
        statusText: getStatusName(asset.status || 0)
      }))
      unrelatedPagination.total = res.data?.total || 0
    } else {
      unrelatedAssetList.value = []
      unrelatedPagination.total = 0
      ElMessage.error(res.message || '获取未关联资产失败')
    }
  } catch (error) {
    ElMessage.error('获取未关联资产列表失败')
    unrelatedAssetList.value = []
    unrelatedPagination.total = 0
  } finally {
    unrelatedLoading.value = false
  }
}

// 搜索未关联资产
const searchUnrelatedAssets = async () => {
  await fetchUnrelatedAssets()
}

// 确认关联资产
const confirmRelateAssets = async () => {
  if (selectedUnrelatedAssets.value.length === 0) {
    ElMessage.warning('请选择要关联的资产')
    return
  }
  
  if (!relationType.value) {
    ElMessage.warning('请选择关联类型')
    return
  }
  
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    await ElMessageBox.confirm(
      `确认将选中的 ${selectedUnrelatedAssets.value.length} 个资产关联到 "${currentLocationPath.value}" 吗？`,
      '确认关联',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'info'
      }
    )
    
    unrelatedLoading.value = true
    const assetIds = selectedUnrelatedAssets.value.map(asset => asset.id)
    
    const res = await locationApi.relateAssets(selectedLocation.value, {
      assetIds: assetIds,
      relationType: relationType.value,
      notes: '从位置关联管理页面关联'
    })
    
    if (res.success) {
      ElMessage.success(`成功关联 ${assetIds.length} 个资产到 ${currentLocationPath.value}`)
      relateDialogVisible.value = false
      selectedUnrelatedAssets.value = []
      fetchAssetsForLocation()
    } else {
      ElMessage.error(res.message || '关联资产失败')
    }
  } catch (error) {
    ElMessage.error('关联资产失败: ' + (error.message || '未知错误'))
  } finally {
    unrelatedLoading.value = false
  }
}

// 获取状态名称辅助函数
const getStatusName = (status) => {
  const statusMap = {
    0: '未知',
    1: '在用',
    2: '闲置',
    3: '维修中',
    4: '借用',
    5: '报废'
  }
  return statusMap[status] || '未知'
}

// 工具方法：获取状态标签类型
const getStatusType = (status) => {
  const map = {
    'in_use': 'success',
    'idle': 'info',
    'repairing': 'warning',
    'borrowed': 'primary',
    'scrapped': 'danger'
  }
  return map[status] || 'info'
}

// 工具方法：获取资产类型标签
const getAssetTypeTag = (type) => {
  const map = {
    'laptop': '',
    'desktop': 'success',
    'server': 'danger',
    'monitor': 'warning',
    'printer': 'info',
    'network': 'primary',
    'other': 'info'
  }
  return map[type] || 'info'
}

// 查看资产详情
const viewAssetDetail = (asset) => {
  ElMessageBox.alert(`资产 ${asset.name} 的详细信息`, '资产详情', {
    confirmButtonText: '确定'
  })
}

// 解除资产关联
const handleUnrelateAsset = (asset) => {
  ElMessageBox.confirm(
    `确定要解除资产 ${asset.name} 与当前位置的关联吗？`,
    '解除关联',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  )
  .then(async () => {
    try {
      const res = await locationApi.unrelateAssets(
        selectedLocationForTree.value.id, 
        [asset.id]
      )
      if (res.success) {
        ElMessage.success('解除关联成功')
        fetchLocationAssets(selectedLocationForTree.value.id)
      } else {
        ElMessage.error(res.message || '解除关联失败')
      }
    } catch (error) {
      ElMessage.error('解除关联失败')
    }
  })
  .catch(() => {})
}

// 获取位置关联的资产
const fetchLocationAssets = async (locationId) => {
  treeAssetLoading.value = true
  try {
    const params = {
      locationId: locationId,
      pageSize: 100,
      pageIndex: 1
    }
    
    const assetsRes = await locationApi.getLocationAssets(params)
    if (assetsRes.success) {
      locationAssetsForTree.value = assetsRes.data.items || []
    } else {
      locationAssetsForTree.value = []
    }
  } catch (error) {
    locationAssetsForTree.value = []
  } finally {
    treeAssetLoading.value = false
  }
}

// 获取资产状态显示样式
const getAssetStatusType = (status) => {
  const statusTypeMap = {
    0: 'info',     // 闲置
    1: 'success',  // 在用
    2: 'warning',  // 维修中
    3: 'danger'    // 报废
  }
  return statusTypeMap[status] || 'info'
}

// 获取资产状态文本
const getAssetStatusText = (status) => {
  const statusTextMap = {
    0: '闲置',
    1: '在用',
    2: '维修中',
    3: '报废'
  }
  return statusTextMap[status] || '未知'
}

// 加载位置使用人
const loadLocationUsers = async (locationId) => {
  try {
    const usersRes = await locationApi.getLocationUsers(locationId)
    if (usersRes && usersRes.data && usersRes.data.success) {
      locationUsersForTree.value = usersRes.data.data || []
    } else {
      console.error('获取位置使用人列表失败:', usersRes?.data?.message || '未知错误')
      locationUsersForTree.value = []
    }
  } catch (error) {
    console.error('获取位置使用人列表出错:', error)
    locationUsersForTree.value = []
  }
}

// 提交部门关联
const submitDepartment = async () => {
  if (!departmentForm.departmentId) {
    ElMessage.warning('请选择部门')
    return
  }
  
  departmentSubmitting.value = true
  try {
    const response = await locationApi.setLocationDepartment(
      currentLocationDetail.value.id, 
      departmentForm.departmentId
    )
    
    if (response.data && response.data.success) {
      ElMessage.success('关联部门成功')
      departmentDialogVisible.value = false
      await fetchLocationDetail(currentLocationDetail.value.id)
      await fetchLocationUsers(currentLocationDetail.value.id)
    } else {
      ElMessage.error(response.data?.message || '关联部门失败')
    }
  } catch (error) {
    ElMessage.error('关联部门失败: ' + error.message)
  } finally {
    departmentSubmitting.value = false
  }
}

// 提交人员关联
const submitUsers = async () => {
  if (userForm.selectedUsers.length === 0) {
    ElMessage.warning('请选择人员')
    return
  }
  
  userSubmitting.value = true
  try {
    const users = userForm.selectedUsers.map(id => ({
      personnelId: id,
      userType: userDialogType.value === 'manager' ? 1 : 0
    }))
    
    const response = await locationApi.setLocationUsers(
      currentLocationDetail.value.id,
      users,
      userForm.replaceExisting
    )
    
    if (response.data && response.data.success) {
      ElMessage.success(`设置${userDialogType.value === 'manager' ? '管理员' : '使用人'}成功`)
      userDialogVisible.value = false
      await fetchLocationUsers(currentLocationDetail.value.id)
    } else {
      ElMessage.error(response.data?.message || `设置${userDialogType.value === 'manager' ? '管理员' : '使用人'}失败`)
    }
  } catch (error) {
    ElMessage.error(`设置${userDialogType.value === 'manager' ? '管理员' : '使用人'}失败: ` + error.message)
  } finally {
    userSubmitting.value = false
  }
}

// 移除关联人员
const removeUser = async (user) => {
  try {
    await ElMessageBox.confirm(`确定要移除 ${user.name} 吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const response = await locationApi.removeLocationUser(currentLocationDetail.value.id, user.id)
    
    if (response.data && response.data.success) {
      ElMessage.success('移除成功')
      await fetchLocationUsers(currentLocationDetail.value.id)
    } else {
      ElMessage.error(response.data?.message || '移除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('移除关联人员失败: ' + error.message)
    }
  }
}

// 组件挂载完成后加载数据
onMounted(() => {
  fetchInitialData();
});

// 组件卸载前清理
onBeforeUnmount(() => {
  if (window._locationRelationsTimer) {
    clearTimeout(window._locationRelationsTimer)
    window._locationRelationsTimer = null
  }
})
</script>

<style lang="scss" scoped>
.location-relations-container {
  padding: 20px;
  
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    .page-title {
      margin: 0;
      font-size: 20px;
    }
    
    .page-actions {
      display: flex;
      gap: 10px;
    }
  }
  
  .filter-card {
    margin-bottom: 20px;
    
    .filters {
      .location-view-switch {
        margin-bottom: 15px;
      }
    }
  }
  
  .data-card {
    min-height: calc(100vh - 290px);
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .header-info {
        display: flex;
        align-items: center;
        
        .department-tag {
          margin-left: 10px;
        }
      }
      
      .location-path {
        color: #909399;
        font-size: 14px;
      }
    }
  }
  
  .tree-view-container {
    height: calc(100vh - 290px);
    
    .tree-container {
      .tree-title {
        font-weight: bold;
        margin-bottom: 10px;
        padding: 8px;
        background-color: #f2f6fc;
        border-radius: 4px;
      }
    }
    
    .location-tree-node {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 2px 0;
      width: 100%;
      
      .node-content {
        display: flex;
        align-items: center;
        flex: 1;
        
        .location-name {
          margin-right: 8px;
          font-weight: 500;
        }
        
        .location-type {
          margin-left: 5px;
        }
      }
      
      .node-actions {
        margin-left: 10px;
        opacity: 0;
        transition: opacity 0.3s;
      }
      
      &:hover .node-actions {
        opacity: 1;
      }
    }
    
    .location-detail-panel {
      padding: 10px;
      border: 1px solid #ebeef5;
      border-radius: 4px;
      height: 100%;
      
      .detail-header {
        padding-bottom: 15px;
        border-bottom: 1px solid #ebeef5;
        margin-bottom: 15px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        
        h3 {
          margin: 0;
          font-size: 18px;
          flex: 1;
        }
        
        .location-path {
          color: #909399;
          font-size: 13px;
          margin: 5px 0;
          width: 100%;
        }
        
        .location-actions {
          display: flex;
          gap: 10px;
        }
      }
    }
  }
  
  .personnel-info {
    margin-top: 20px;
    
    .info-content {
      line-height: 1.8;
      p {
        margin: 5px 0;
      }
    }
  }
  
  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }
  
  .danger-text {
    color: #f56c6c;
  }
}

:deep(.el-tree-node) {
  .el-tree-node__content {
    height: 36px;
    
    &:hover {
      background-color: #f5f7fa;
    }
  }
  
  &.is-current > .el-tree-node__content {
    background-color: #ecf5ff;
  }
}

:deep(.location-tags) {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  margin: 5px 0;
}

/* 人员选择样式 */
.personnel-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.personnel-name {
  font-weight: bold;
}

.personnel-code {
  margin-left: 5px;
  color: #909399;
  font-size: 0.9em;
}

.personnel-dept {
  color: #67c23a;
  font-size: 0.9em;
}

.current-users-title {
  font-weight: bold;
  margin: 15px 0 10px 0;
  color: #606266;
  font-size: 14px;
}
</style>