{"name": "@interactjs/types", "version": "1.10.27", "main": "index", "module": "index", "type": "module", "repository": {"type": "git", "url": "https://github.com/taye/interact.js.git", "directory": "packages/@interactjs/types"}, "typings": "typings.d.ts", "devDependencies": {"@interactjs/actions": "1.10.27", "@interactjs/auto-scroll": "1.10.27", "@interactjs/auto-start": "1.10.27", "@interactjs/core": "1.10.27", "@interactjs/dev-tools": "1.10.27", "@interactjs/inertia": "1.10.27", "@interactjs/interact": "1.10.27", "@interactjs/interactjs": "1.10.27", "@interactjs/modifiers": "1.10.27", "@interactjs/pointer-events": "1.10.27", "@interactjs/reflow": "1.10.27", "@interactjs/snappers": "1.10.27", "@interactjs/utils": "1.10.27"}, "publishConfig": {"access": "public"}, "sideEffects": ["**/index.js", "**/index.prod.js"], "license": "MIT", "gitHead": "3ace1cad"}