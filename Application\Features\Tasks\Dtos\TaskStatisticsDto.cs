// File: Application/Features/Tasks/Dtos/TaskStatisticsDto.cs
// Description: 任务统计数据传输对象

using System.Collections.Generic;

namespace ItAssetsSystem.Application.Features.Tasks.Dtos
{
    /// <summary>
    /// 任务统计信息
    /// </summary>
    public class TaskStatisticsDto
    {
        public int TotalTasks { get; set; }
        public int CompletedTasks { get; set; }
        public int InProgressTasks { get; set; }
        public int TodoTasks { get; set; }
        public int OverdueTasks { get; set; }
        public Dictionary<string, int> TasksByPriority { get; set; } = new();
        public Dictionary<string, int> TasksByStatus { get; set; } = new();
        public List<TaskDto> UpcomingDeadlines { get; set; } = new();
    }
} 