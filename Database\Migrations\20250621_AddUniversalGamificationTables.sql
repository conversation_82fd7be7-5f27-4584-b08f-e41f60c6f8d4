-- 通用游戏化系统数据库迁移脚本
-- 创建日期: 2025-06-21
-- 描述: 添加通用游戏化系统所需的表和字段

-- 1. 创建行为类型配置表
CREATE TABLE IF NOT EXISTS `gamification_behavior_types` (
  `id` int NOT NULL AUTO_INCREMENT,
  `code` varchar(50) NOT NULL COMMENT '行为代码（唯一标识）',
  `name` varchar(100) NOT NULL COMMENT '行为名称',
  `description` varchar(255) DEFAULT NULL COMMENT '行为描述',
  `base_points` int NOT NULL DEFAULT '0' COMMENT '基础积分',
  `base_coins` int NOT NULL DEFAULT '0' COMMENT '基础金币',
  `base_diamonds` int NOT NULL DEFAULT '0' COMMENT '基础钻石',
  `multiplier` decimal(5,2) NOT NULL DEFAULT '1.00' COMMENT '积分系数',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用',
  `category` varchar(20) NOT NULL DEFAULT '' COMMENT '行为分类',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_behavior_code` (`code`),
  KEY `idx_category` (`category`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='游戏化行为类型配置表';

-- 2. 扩展用户统计表，添加新的统计字段
ALTER TABLE `gamification_userstats` 
ADD COLUMN IF NOT EXISTS `CoinsBalance` int NOT NULL DEFAULT '0' COMMENT '金币余额' AFTER `LastUpdatedTimestamp`,
ADD COLUMN IF NOT EXISTS `DiamondsBalance` int NOT NULL DEFAULT '0' COMMENT '钻石余额' AFTER `CoinsBalance`,
ADD COLUMN IF NOT EXISTS `TasksCreatedCount` int NOT NULL DEFAULT '0' COMMENT '累计创建任务数' AFTER `DiamondsBalance`,
ADD COLUMN IF NOT EXISTS `TasksClaimedCount` int NOT NULL DEFAULT '0' COMMENT '累计领取任务数' AFTER `TasksCreatedCount`,
ADD COLUMN IF NOT EXISTS `FaultsRecordedCount` int NOT NULL DEFAULT '0' COMMENT '累计登记故障数' AFTER `TasksClaimedCount`,
ADD COLUMN IF NOT EXISTS `MaintenanceCreatedCount` int NOT NULL DEFAULT '0' COMMENT '累计创建维修单数' AFTER `FaultsRecordedCount`,
ADD COLUMN IF NOT EXISTS `AssetsUpdatedCount` int NOT NULL DEFAULT '0' COMMENT '累计更新资产数' AFTER `MaintenanceCreatedCount`,
ADD COLUMN IF NOT EXISTS `ReturnFactoryCount` int NOT NULL DEFAULT '0' COMMENT '累计返厂登记数' AFTER `AssetsUpdatedCount`;

-- 3. 扩展游戏化日志表，添加金币和钻石字段
ALTER TABLE `gamification_log` 
ADD COLUMN IF NOT EXISTS `CoinsGained` int DEFAULT '0' COMMENT '获得金币数' AFTER `PointsChange`,
ADD COLUMN IF NOT EXISTS `DiamondsGained` int DEFAULT '0' COMMENT '获得钻石数' AFTER `CoinsGained`,
ADD COLUMN IF NOT EXISTS `ActionType` varchar(50) DEFAULT NULL COMMENT '行为类型' AFTER `EventType`,
ADD COLUMN IF NOT EXISTS `Description` varchar(500) DEFAULT NULL COMMENT '描述' AFTER `Reason`,
ADD COLUMN IF NOT EXISTS `ReferenceId` varchar(50) DEFAULT NULL COMMENT '关联对象ID' AFTER `RelatedTaskId`;

-- 4. 插入默认行为类型配置
INSERT IGNORE INTO `gamification_behavior_types` (`code`, `name`, `description`, `base_points`, `base_coins`, `base_diamonds`, `category`) VALUES
-- 任务相关
('TASK_CREATED', '任务创建', '创建新任务获得奖励', 20, 10, 0, 'TASK'),
('TASK_COMPLETED', '任务完成', '完成任务获得奖励', 50, 25, 1, 'TASK'),
('TASK_CLAIMED', '任务领取', '领取任务获得奖励', 10, 5, 0, 'TASK'),
('TASK_DELETED', '任务删除', '删除任务扣除奖励（负分）', -20, -10, 0, 'TASK'),

-- 资产相关
('ASSET_CREATED', '资产创建', '创建新资产获得奖励', 30, 15, 0, 'ASSET'),
('ASSET_UPDATED', '资产更新', '更新资产信息获得奖励', 15, 8, 0, 'ASSET'),
('ASSET_TRANSFERRED', '资产转移', '资产转移操作获得奖励', 20, 10, 0, 'ASSET'),

-- 故障相关
('FAULT_RECORDED', '故障登记', '登记故障获得奖励', 25, 12, 0, 'FAULT'),
('FAULT_RESOLVED', '故障解决', '解决故障获得奖励', 40, 20, 1, 'FAULT'),

-- 维修相关
('MAINTENANCE_CREATED', '维修单创建', '创建维修单获得奖励', 35, 18, 0, 'MAINTENANCE'),
('MAINTENANCE_COMPLETED', '维修完成', '完成维修获得奖励', 60, 30, 2, 'MAINTENANCE'),

-- 返厂相关
('RETURN_FACTORY_CREATED', '返厂登记', '登记返厂获得奖励', 40, 20, 1, 'RETURN'),
('RETURN_FACTORY_COMPLETED', '返厂完成', '返厂完成获得奖励', 80, 40, 3, 'RETURN');

-- 5. 创建排行榜类型配置表（可选，用于更灵活的排行榜配置）
CREATE TABLE IF NOT EXISTS `gamification_leaderboard_types` (
  `id` int NOT NULL AUTO_INCREMENT,
  `code` varchar(50) NOT NULL COMMENT '排行榜代码',
  `name` varchar(100) NOT NULL COMMENT '排行榜名称',
  `metric` varchar(50) NOT NULL COMMENT '统计指标',
  `period` varchar(20) NOT NULL COMMENT '统计周期',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用',
  `sort_order` int NOT NULL DEFAULT '0' COMMENT '排序顺序',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_leaderboard_code` (`code`),
  KEY `idx_metric` (`metric`),
  KEY `idx_period` (`period`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='排行榜类型配置表';

-- 6. 插入默认排行榜类型
INSERT IGNORE INTO `gamification_leaderboard_types` (`code`, `name`, `metric`, `period`, `sort_order`) VALUES
('POINTS_DAILY', '每日积分排行', 'points', 'daily', 1),
('POINTS_WEEKLY', '每周积分排行', 'points', 'weekly', 2),
('POINTS_MONTHLY', '每月积分排行', 'points', 'monthly', 3),
('POINTS_ALLTIME', '总积分排行', 'points', 'alltime', 4),
('TASKS_COMPLETED_WEEKLY', '每周完成任务排行', 'tasks_completed', 'weekly', 5),
('TASKS_COMPLETED_MONTHLY', '每月完成任务排行', 'tasks_completed', 'monthly', 6),
('TASKS_CREATED_MONTHLY', '每月创建任务排行', 'tasks_created', 'monthly', 7),
('FAULTS_RECORDED_MONTHLY', '每月故障登记排行', 'faults_recorded', 'monthly', 8);

-- 7. 创建索引优化查询性能
CREATE INDEX IF NOT EXISTS `idx_gamification_userstats_points` ON `gamification_userstats` (`PointsBalance` DESC);
CREATE INDEX IF NOT EXISTS `idx_gamification_userstats_level` ON `gamification_userstats` (`CurrentLevel` DESC);
CREATE INDEX IF NOT EXISTS `idx_gamification_userstats_tasks_completed` ON `gamification_userstats` (`CompletedTasksCount` DESC);
CREATE INDEX IF NOT EXISTS `idx_gamification_userstats_tasks_created` ON `gamification_userstats` (`TasksCreatedCount` DESC);
CREATE INDEX IF NOT EXISTS `idx_gamification_userstats_last_activity` ON `gamification_userstats` (`LastActivityTimestamp` DESC);

CREATE INDEX IF NOT EXISTS `idx_gamification_log_user_action` ON `gamification_log` (`UserId`, `ActionType`, `Timestamp`);
CREATE INDEX IF NOT EXISTS `idx_gamification_log_reference` ON `gamification_log` (`ReferenceId`, `ActionType`);

-- 8. 创建视图用于快速查询排行榜数据
CREATE OR REPLACE VIEW `v_leaderboard_overview` AS
SELECT 
    u.Id as UserId,
    u.Name as UserName,
    u.Username,
    u.Department,
    u.AvatarUrl,
    gus.PointsBalance,
    gus.CoinsBalance,
    gus.DiamondsBalance,
    gus.CurrentLevel,
    gus.CurrentXP,
    gus.CompletedTasksCount,
    gus.TasksCreatedCount,
    gus.TasksClaimedCount,
    gus.FaultsRecordedCount,
    gus.MaintenanceCreatedCount,
    gus.AssetsUpdatedCount,
    gus.ReturnFactoryCount,
    gus.LastActivityTimestamp,
    -- 计算综合活跃度分数
    (gus.TasksCreatedCount * 2 + gus.TasksClaimedCount * 1 + gus.CompletedTasksCount * 3 + 
     gus.FaultsRecordedCount * 2 + gus.MaintenanceCreatedCount * 3 + gus.AssetsUpdatedCount * 1) as ActivityScore
FROM users u
LEFT JOIN gamification_userstats gus ON u.Id = gus.CoreUserId
WHERE u.IsActive = 1;

-- 完成迁移
SELECT 'Universal Gamification System migration completed successfully!' as Status;
