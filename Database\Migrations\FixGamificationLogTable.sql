-- 修复游戏化日志表结构
-- 确保所有必需的字段都存在

USE itassets;

-- 检查并添加缺失的字段
ALTER TABLE `gamification_log` 
ADD COLUMN IF NOT EXISTS `CoinsGained` int DEFAULT '0' COMMENT '获得金币数' AFTER `PointsChange`,
ADD COLUMN IF NOT EXISTS `DiamondsGained` int DEFAULT '0' COMMENT '获得钻石数' AFTER `CoinsGained`,
ADD COLUMN IF NOT EXISTS `ActionType` varchar(100) DEFAULT NULL COMMENT '行为类型' AFTER `EventType`,
ADD COLUMN IF NOT EXISTS `Description` varchar(500) DEFAULT NULL COMMENT '描述' AFTER `Reason`,
ADD COLUMN IF NOT EXISTS `ReferenceId` varchar(50) DEFAULT NULL COMMENT '关联对象ID' AFTER `RelatedTaskId`;

-- 检查表结构
DESCRIBE `gamification_log`;

-- 显示修复结果
SELECT 'Gamification log table structure fixed successfully!' as Status;
