// File: Application/Features/Notes/Dtos/CreateQuickMemoCategoryRequestDto.cs
// Description: DTO for creating a new QuickMemoCategory.
#nullable enable
using System.ComponentModel.DataAnnotations;

namespace ItAssetsSystem.Application.Features.Notes.Dtos
{
    public class CreateQuickMemoCategoryRequestDto
    {
        [Required(ErrorMessage = "Category name is required.")]
        [MaxLength(100, ErrorMessage = "Category name cannot exceed 100 characters.")]
        public string Name { get; set; } = string.Empty;

        [MaxLength(7, ErrorMessage = "Color hex code cannot exceed 7 characters.")]
        public string? Color { get; set; }
    }
} 