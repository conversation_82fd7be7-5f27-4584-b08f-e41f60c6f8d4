// File: Infrastructure/Data/Repositories/QuickMemoCategoryRepository.cs
// Description: EF Core implementation of IQuickMemoCategoryRepository.
#nullable enable
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Threading; // For CancellationToken
using ItAssetsSystem.Core.Abstractions;
using ItAssetsSystem.Domain.Entities.Notes;
using ItAssetsSystem.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;

namespace ItAssetsSystem.Infrastructure.Data.Repositories
{
    public class QuickMemoCategoryRepository : IQuickMemoCategoryRepository
    {
        private readonly AppDbContext _context;

        public QuickMemoCategoryRepository(AppDbContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<QuickMemoCategory>> GetByUserIdAsync(int userId, CancellationToken cancellationToken = default)
        {
            return await _context.QuickMemoCategories
                                 .Where(c => c.UserId == userId)
                                 .OrderBy(c => c.Name) // Default ordering
                                 .ToListAsync(cancellationToken);
        }

        public async Task<QuickMemoCategory?> GetByIdAsync(string categoryId, CancellationToken cancellationToken = default)
        {
            return await _context.QuickMemoCategories
                                 .FirstOrDefaultAsync(c => c.Id == categoryId, cancellationToken);
        }
        
        public async Task<QuickMemoCategory?> GetByIdAndUserIdAsync(string categoryId, int userId, CancellationToken cancellationToken = default)
        {
            return await _context.QuickMemoCategories
                                 .FirstOrDefaultAsync(c => c.Id == categoryId && c.UserId == userId, cancellationToken);
        }

        public async Task AddAsync(QuickMemoCategory category, CancellationToken cancellationToken = default)
        {
            await _context.QuickMemoCategories.AddAsync(category, cancellationToken);
        }

        public void Update(QuickMemoCategory category)
        {
            _context.QuickMemoCategories.Update(category);
        }

        public void Delete(QuickMemoCategory category)
        {
            _context.QuickMemoCategories.Remove(category);
        }

        public async Task<bool> UserOwnsCategoryAsync(string categoryId, int userId, CancellationToken cancellationToken = default)
        {
            return await _context.QuickMemoCategories.AnyAsync(c => c.Id == categoryId && c.UserId == userId, cancellationToken);
        }

        public async Task<QuickMemoCategory?> FindByNameAsync(int userId, string name, CancellationToken cancellationToken = default)
        {
            return await _context.QuickMemoCategories
                                 .FirstOrDefaultAsync(c => c.UserId == userId && EF.Functions.Collate(c.Name, "SQL_Latin1_General_CP1_CI_AS") == EF.Functions.Collate(name, "SQL_Latin1_General_CP1_CI_AS"), cancellationToken);
            // Using EF.Functions.Collate for case-insensitive comparison if DB default is case-sensitive.
            // Adjust collation as per your DB (e.g., "utf8mb4_general_ci" for MySQL).
            // If your DB/column is already case-insensitive, you can use: c.Name.ToLower() == name.ToLower() or c.Name == name if comparison is CI by default.
            // For simplicity if DB is case-insensitive by default for the column: 
            // return await _context.QuickMemoCategories.FirstOrDefaultAsync(c => c.UserId == userId && c.Name == name, cancellationToken);
        }

        public async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            return await _context.SaveChangesAsync(cancellationToken);
        }
    }
} 