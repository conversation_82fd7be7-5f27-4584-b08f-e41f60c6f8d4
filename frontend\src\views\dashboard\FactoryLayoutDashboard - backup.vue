<template>
  <div class="modern-factory-dashboard">
    <!-- Enhanced Header -->
    <header class="dashboard-header">
      <div class="header-container">
        <div class="brand-section">
          <div class="brand-icon">
            <el-icon size="32"><Cpu /></el-icon>
            <div class="icon-glow"></div>
          </div>
          <div class="brand-content">
            <h1 class="brand-title">智能制造监控系统</h1>
            <p class="brand-subtitle">实时工厂状态监控 • {{ stats.total }}个工位</p>
          </div>
          <div class="status-badge" :class="systemStatus">
            <div class="status-indicator"></div>
            <span>系统{{ systemStatusText }}</span>
          </div>
        </div>

        <div class="header-actions">
          <!-- Enhanced Search -->
          <div class="search-container">
            <el-input
              v-model="searchTerm"
              placeholder="搜索工位编号或设备名称..."
              class="smart-search"
              clearable
              @input="handleSearch"
            >
              <template #prefix>
                <el-icon class="search-icon"><Search /></el-icon>
              </template>
            </el-input>

            <!-- Search Results Dropdown -->
            <div v-if="searchResults.length > 0 && searchTerm" class="search-results">
              <div
                v-for="result in searchResults.slice(0, 5)"
                :key="result.locationId"
                class="search-result-item"
                @click="selectSearchResult(result)"
              >
                <div class="result-status" :class="result.status"></div>
                <div class="result-content">
                  <span class="result-name">{{ result.locationName }}</span>
                  <span class="result-info">{{ result.efficiency }}% 效率</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Action Buttons -->
          <div class="action-buttons">
            <!-- Filter with Badge -->
            <el-popover placement="bottom" trigger="click" width="350" popper-class="filter-popover">
              <template #reference>
                <el-button
                  class="action-btn filter-btn"
                  :class="{ 'active': hasActiveFilters }"
                >
                  <el-icon><Filter /></el-icon>
                  <span>筛选</span>
                  <el-badge
                    v-if="activeFilterCount > 0"
                    :value="activeFilterCount"
                    class="filter-badge"
                  />
                </el-button>
              </template>

              <div class="filter-content">
                <div class="filter-section">
                  <label>状态筛选</label>
                  <el-checkbox-group v-model="statusFilters" @change="applyFilters">
                    <el-checkbox label="operational">正常运行</el-checkbox>
                    <el-checkbox label="warning">警告状态</el-checkbox>
                    <el-checkbox label="error">故障状态</el-checkbox>
                    <el-checkbox label="idle">空闲工位</el-checkbox>
                  </el-checkbox-group>
                </div>

                <div class="filter-section">
                  <label>效率范围</label>
                  <el-slider
                    v-model="efficiencyRange"
                    range
                    :min="0"
                    :max="100"
                    :step="5"
                    @change="applyFilters"
                  />
                  <div class="range-display">{{ efficiencyRange[0] }}% - {{ efficiencyRange[1] }}%</div>
                </div>

                <div class="filter-actions">
                  <el-button size="small" @click="resetFilters">重置</el-button>
                  <el-button size="small" type="primary" @click="applyFilters">应用</el-button>
                </div>
              </div>
            </el-popover>

            <!-- View Mode Toggle -->
            <el-button-group class="view-toggle">
              <el-button
                :class="{ 'active': viewMode === 'layout' }"
                @click="setViewMode('layout')"
                class="view-btn"
                title="布局视图"
              >
                <el-icon><Grid /></el-icon>
              </el-button>
              <el-button
                :class="{ 'active': viewMode === 'list' }"
                @click="setViewMode('list')"
                class="view-btn"
                title="列表视图"
              >
                <el-icon><List /></el-icon>
              </el-button>
            </el-button-group>

            <!-- Refresh Button -->
            <el-button
              @click="refreshData"
              :loading="refreshing"
              class="action-btn refresh-btn"
              :class="{ 'refreshing': refreshing }"
              title="刷新数据"
            >
              <el-icon class="refresh-icon"><Refresh /></el-icon>
            </el-button>

            <!-- Fullscreen Button -->
            <el-button
              @click="toggleFullScreen"
              class="action-btn fullscreen-btn"
              :class="{ 'active': isFullscreen }"
              :title="isFullscreen ? '退出全屏' : '全屏'"
            >
              <el-icon>
                <FullScreen v-if="!isFullscreen" />
                <Close v-else />
              </el-icon>
            </el-button>
          </div>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="dashboard-main">
      <div class="dashboard-container">
        <!-- Enhanced Stats Panel -->
        <div class="stats-panel">
          <!-- Real-time Overview -->
          <div class="stats-card overview-card">
            <div class="card-header">
              <h3 class="card-title">实时概览</h3>
              <div class="update-indicator">
                <div class="pulse-dot"></div>
                <span class="update-time">{{ formattedTime }}</span>
              </div>
            </div>

            <div class="stats-grid">
              <div
                v-for="(status, key) in statusCards"
                :key="key"
                class="modern-stat-card"
                :class="key"
                @click="filterByStatus(key)"
              >
                <div class="stat-visual">
                  <div class="stat-icon-container" :class="key">
                    <el-icon class="stat-icon">
                      <component :is="status.icon" />
                    </el-icon>
                    <div class="icon-glow" :class="key"></div>
                  </div>
                  <div class="stat-progress">
                    <div
                      class="progress-bar"
                      :class="key"
                      :style="{ width: getStatusPercent(key) + '%' }"
                    ></div>
                  </div>
                </div>
                <div class="stat-data">
                  <div class="stat-value">{{ stats[key] }}</div>
                  <div class="stat-label">{{ status.label }}</div>
                  <div v-if="key === 'operational'" class="stat-percent">
                    {{ stats.operationalPercent }}%
                  </div>
                </div>
              </div>
            </div>

            <!-- System Metrics -->
            <div class="system-metrics">
              <div class="metric-item">
                <div class="metric-value">{{ avgEfficiency }}%</div>
                <div class="metric-label">平均效率</div>
              </div>
              <div class="metric-item">
                <div class="metric-value">{{ totalAssets }}</div>
                <div class="metric-label">总设备数</div>
              </div>
            </div>
          </div>

          <!-- Priority Workstations -->
          <div class="stats-card priority-card">
            <div class="card-header">
              <h3 class="card-title">重点监控</h3>
              <el-badge :value="priorityWorkstations.length" class="priority-badge" />
            </div>

            <div class="priority-list">
              <div
                v-for="workstation in priorityWorkstations.slice(0, 5)"
                :key="workstation.locationId"
                class="priority-item"
                @click="selectWorkstation(workstation.locationId)"
              >
                <div class="priority-status" :class="workstation.status"></div>
                <div class="priority-content">
                  <div class="priority-name">{{ workstation.locationName }}</div>
                  <div class="priority-info">
                    {{ workstation.efficiency }}% 效率
                    <span v-if="workstation.faultCount > 0" class="fault-count">
                      • {{ workstation.faultCount }} 故障
                    </span>
                  </div>
                </div>
                <el-icon class="priority-arrow"><ArrowRight /></el-icon>
              </div>
            </div>
          </div>

          <!-- Zone Statistics -->
          <div class="stats-card zone-card">
            <div class="card-header">
              <h3 class="card-title">区域统计</h3>
            </div>

            <div class="zone-list">
              <div
                v-for="(zone, zoneId) in zoneStats"
                :key="zoneId"
                class="zone-item"
              >
                <div class="zone-info">
                  <div class="zone-name">{{ getZoneName(zoneId) }}</div>
                  <div class="zone-metrics">
                    <span class="zone-total">{{ zone.total }}个工位</span>
                    <span class="zone-efficiency">{{ zone.efficiency }}%效率</span>
                  </div>
                </div>
                <div class="zone-status">
                  <div class="status-indicators">
                    <span class="status-dot operational" v-if="zone.operational > 0">{{ zone.operational }}</span>
                    <span class="status-dot warning" v-if="zone.warning > 0">{{ zone.warning }}</span>
                    <span class="status-dot error" v-if="zone.error > 0">{{ zone.error }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Factory Layout -->
        <div class="factory-layout">
          <div class="layout-header">
            <div class="layout-info-left">
              <span>显示: {{ filteredLocations.length }} / {{ locations.length }} 个工位</span>
              <span v-if="layoutConfig" class="layout-info">
                | {{ layoutConfig.zones?.length || 0 }} 个区域
                | {{ layoutConfig.name || '未命名布局' }}
              </span>
              <span v-if="importedFileName" class="current-file">
                | 当前文件: {{ importedFileName }}
              </span>
            </div>

            <div class="layout-controls">
              <!-- 缩放控制 -->
              <div class="zoom-controls">
                <el-button-group size="small">
                  <el-button @click="zoomOut" :disabled="zoomLevel <= 0.5">
                    <el-icon><ZoomOut /></el-icon>
                  </el-button>
                  <el-button @click="resetZoom">
                    {{ Math.round(zoomLevel * 100) }}%
                  </el-button>
                  <el-button @click="zoomIn" :disabled="zoomLevel >= 2">
                    <el-icon><ZoomIn /></el-icon>
                  </el-button>
                </el-button-group>
              </div>
            </div>
          </div>

          <!-- Layout View -->
          <div v-if="viewMode === 'layout'" class="factory-floor" :class="{ 'fullscreen': isFullscreen }">
            <!-- Factory Grid Background -->
            <svg class="factory-grid" :viewBox="canvasViewBox">
              <defs>
                <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
                  <path d="M 20 0 L 0 0 0 20" fill="none" stroke="rgba(107, 148, 214, 0.08)" stroke-width="1"/>
                </pattern>
                <!-- 渐变定义 -->
                <linearGradient id="operationalGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" style="stop-color:#22c55e;stop-opacity:1" />
                  <stop offset="100%" style="stop-color:#16a34a;stop-opacity:1" />
                </linearGradient>
                <linearGradient id="warningGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" style="stop-color:#eab308;stop-opacity:1" />
                  <stop offset="100%" style="stop-color:#ca8a04;stop-opacity:1" />
                </linearGradient>
                <linearGradient id="errorGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" style="stop-color:#ef4444;stop-opacity:1" />
                  <stop offset="100%" style="stop-color:#dc2626;stop-opacity:1" />
                </linearGradient>
              </defs>
              <rect width="100%" height="100%" fill="url(#grid)" />
            </svg>

            <!-- 动态区域容器布局系统 -->
            <div class="zone-containers" :style="containerStyle">
              <!-- 动态生成区域容器 -->
              <div
                v-for="zone in layoutConfig?.zones || []"
                :key="zone.id"
                class="zone-container modern-zone"
                :class="`zone-${zone.id}`"
                :data-zone="zone.name"
                :style="getZoneContainerStyle(zone)"
              >
                <!-- 增强区域标签 -->
                <div class="zone-label modern-zone-label" :style="{ color: zone.color }">
                  <div class="zone-header">
                    <span class="zone-name">{{ zone.name }}</span>
                    <span class="zone-stats">{{ getZoneWorkstationCount(zone) }}个工位</span>
                  </div>
                  <div class="zone-efficiency">
                    效率: {{ getZoneEfficiency(zone) }}%
                  </div>
                </div>

                <div class="zone-workstations modern-grid" :style="getZoneGridStyle(zone)">
                  <div
                    v-for="location in getSortedZoneWorkstations(zone)"
                    :key="location.locationId"
                    class="workstation-cell modern-workstation"
                    :class="getWorkstationClasses(location)"
                    @click="handleLocationClick(location)"
                    @mouseenter="showTooltip(location, $event)"
                    @mouseleave="hideTooltip"
                    v-show="isLocationVisible(location)"
                  >
                    <!-- 3D卡片效果背景 -->
                    <div class="cell-background"></div>
                    
                    <!-- 状态指示灯 -->
                    <div class="status-indicator" :class="location.status">
                      <div class="pulse-ring" v-if="location.status === 'error'"></div>
                    </div>

                    <div class="cell-content modern-content">
                      <!-- 工位编号和名称 -->
                      <div class="workstation-header">
                        <div class="workstation-id">{{ location.locationCode || location.locationId }}</div>
                        <div class="workstation-name">{{ getShortLocationName(location.locationName) }}</div>
                      </div>

                      <!-- 状态图标 -->
                      <div class="status-display">
                        <el-icon v-if="location.status === 'error'" class="status-icon error">
                          <Close />
                        </el-icon>
                        <el-icon v-else-if="location.status === 'warning'" class="status-icon warning">
                          <Warning />
                        </el-icon>
                        <el-icon v-else-if="location.status === 'operational'" class="status-icon operational">
                          <Check />
                        </el-icon>
                        <el-icon v-else class="status-icon idle">
                          <Setting />
                        </el-icon>
                      </div>

                      <!-- 圆形效率指示器 -->
                      <div class="efficiency-indicator" v-if="location.efficiency">
                        <div class="efficiency-circle">
                          <svg viewBox="0 0 36 36" class="circular-chart">
                            <path class="circle-bg"
                              d="M18 2.0845
                                a 15.9155 15.9155 0 0 1 0 31.831
                                a 15.9155 15.9155 0 0 1 0 -31.831"
                            />
                            <path class="circle"
                              :class="getEfficiencyLevel(location.efficiency)"
                              :stroke-dasharray="`${location.efficiency}, 100`"
                              d="M18 2.0845
                                a 15.9155 15.9155 0 0 1 0 31.831
                                a 15.9155 15.9155 0 0 1 0 -31.831"
                            />
                          </svg>
                          <div class="efficiency-text">{{ location.efficiency }}%</div>
                        </div>
                      </div>

                      <!-- 悬浮详情层 -->
                      <div class="hover-overlay">
                        <div class="quick-stats">
                          <div class="stat-item">
                            <span class="stat-label">设备</span>
                            <span class="stat-value">{{ location.assetCount || 0 }}</span>
                          </div>
                          <div class="stat-item">
                            <span class="stat-label">任务</span>
                            <span class="stat-value">{{ location.taskCount || 0 }}</span>
                          </div>
                          <div class="stat-item" v-if="location.faultCount > 0">
                            <span class="stat-label">故障</span>
                            <span class="stat-value error">{{ location.faultCount }}</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- 高亮边框 -->
                    <div v-if="selectedLocationId === location.locationId" class="selection-border modern-selection"></div>
                    
                    <!-- 故障脉冲效果 -->
                    <div v-if="location.status === 'error'" class="error-pulse"></div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 图例 -->
            <div class="map-legend">
              <div class="legend-title">状态图例</div>
              <div class="legend-items">
                <div class="legend-item">
                  <span class="legend-dot operational"></span>
                  <span>正常运行</span>
                </div>
                <div class="legend-item">
                  <span class="legend-dot warning"></span>
                  <span>警告状态</span>
                </div>
                <div class="legend-item">
                  <span class="legend-dot error"></span>
                  <span>故障状态</span>
                </div>
                <div class="legend-item">
                  <span class="legend-dot idle"></span>
                  <span>空闲状态</span>
                </div>
              </div>
            </div>
          </div>

          <!-- List View -->
          <div v-else class="factory-list">
            <el-table
              :data="filteredLocations"
              height="100%"
              @row-click="handleRowClick"
              highlight-current-row
            >
              <el-table-column prop="locationCode" label="工位编号" width="100" />
              <el-table-column prop="locationName" label="工位名称" min-width="120" />
              <el-table-column prop="departmentName" label="所属部门" min-width="120" />
              <el-table-column prop="efficiency" label="效率" width="80" align="center">
                <template #default="{ row }">
                  <span :class="getEfficiencyClass(row.efficiency)">{{ row.efficiency }}%</span>
                </template>
              </el-table-column>
              <el-table-column prop="status" label="状态" width="100" align="center">
                <template #default="{ row }">
                  <el-tag :type="getStatusTagType(row.status)" size="small">
                    {{ getStatusText(row.status) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="assetCount" label="设备数" width="80" align="center" />
              <el-table-column prop="taskCount" label="任务数" width="80" align="center" />
              <el-table-column prop="faultCount" label="故障数" width="80" align="center">
                <template #default="{ row }">
                  <span v-if="row.faultCount > 0" class="fault-count-text">{{ row.faultCount }}</span>
                  <span v-else class="no-fault">0</span>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="100" align="center">
                <template #default="{ row }">
                  <el-button size="small" @click.stop="selectWorkstation(row.locationId)">
                    详情
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </main>

    <!-- 悬浮提示框 -->
    <div
      v-show="tooltipVisible"
      class="location-tooltip"
      :style="{ left: tooltipX + 'px', top: tooltipY + 'px' }"
    >
      <div class="tooltip-header">
        <div class="tooltip-title">{{ tooltipLocation?.locationName }}</div>
        <div class="tooltip-code">{{ tooltipLocation?.locationCode }}</div>
      </div>
      <div class="tooltip-content">
        <div class="tooltip-section">
          <div class="section-title">状态信息</div>
          <div class="status-info">
            <span class="status-badge" :class="tooltipLocation?.status">
              {{ getStatusText(tooltipLocation?.status) }}
            </span>
            <span class="efficiency-text">效率: {{ tooltipLocation?.efficiency }}%</span>
          </div>
        </div>
        <div class="tooltip-section" v-if="tooltipLocation?.departmentName">
          <div class="section-title">所属区域</div>
          <div class="department-name">{{ tooltipLocation.originalDepartmentName || tooltipLocation.departmentName }}</div>
        </div>
        <div class="tooltip-section">
          <div class="section-title">设备信息</div>
          <div class="asset-info">
            <span>设备数量: {{ tooltipLocation?.assetCount || 0 }}</span>
            <span>任务数量: {{ tooltipLocation?.taskCount || 0 }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Workstation Details Drawer -->
    <el-drawer
      v-model="showDetailsDrawer"
      title="工位详情"
      size="450px"
      direction="rtl"
    >
      <LocationDetailsModal
        v-if="selectedLocation"
        :location="selectedLocation"
        @close="closeLocationDetail"
      />
    </el-drawer>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Cpu, Search, Filter, Refresh, FullScreen, Close,
  Check, Warning, Close as CloseIcon, Setting, ArrowRight,
  ZoomIn, ZoomOut, Grid, List
} from '@element-plus/icons-vue'
import LocationDetailsModal from './components/LocationDetailsModal.vue'

// 响应式数据
const locations = ref([])
const loading = ref(true)
const refreshing = ref(false)
const configType = ref('custom')
const customConfigFile = ref('factory-layout-1748964326771.json')
const layoutConfig = ref(null)
const lastUpdate = ref(new Date())
const importedFileName = ref('')
const importedFileContent = ref(null)
const uploadRef = ref(null)

// 新增的交互功能数据
const viewMode = ref('layout')
const isFullscreen = ref(false)
const zoomLevel = ref(1)
const selectedLocationId = ref(null)
const selectedLocation = ref(null)
const showDetailsDrawer = ref(false)

// 筛选功能数据
const statusFilters = ref(['operational', 'warning', 'error', 'idle'])
const searchTerm = ref('')
const efficiencyRange = ref([0, 100])
const searchResults = ref([])

// 提示框相关
const tooltipVisible = ref(false)
const tooltipX = ref(0)
const tooltipY = ref(0)
const tooltipLocation = ref(null)

// 系统状态
const systemStatus = ref('operational')
const systemStatusText = computed(() => {
  const statusMap = {
    operational: '正常运行',
    warning: '警告状态',
    offline: '离线状态'
  }
  return statusMap[systemStatus.value] || '未知状态'
})

// Status cards configuration
const statusCards = {
  operational: { icon: Check, label: '运行正常' },
  warning: { icon: Warning, label: '警告状态' },
  error: { icon: CloseIcon, label: '故障状态' },
  idle: { icon: Setting, label: '空闲工位' }
}

// 加载工厂布局配置
const loadLayoutConfig = async () => {
  try {
    loading.value = true

    let config

    // 如果是导入模式且有导入的文件内容，直接使用
    if (configType.value === 'import' && importedFileContent.value) {
      console.log('使用导入的JSON文件内容')
      config = importedFileContent.value
    } else {
      // 从服务器加载配置文件
      let configPath
      if (configType.value === 'custom') {
        configPath = `/analyresport/${customConfigFile.value}`
      } else if (configType.value === 'default') {
        configPath = '/Configurations/factory-layout.json'
      } else {
        // import模式但没有文件内容，使用默认配置
        console.log('导入模式但没有文件，使用默认配置')
        layoutConfig.value = getDefaultLayoutConfig()
        return layoutConfig.value
      }

      console.log(`加载配置文件: ${configPath}`)

      const response = await fetch(configPath)
      console.log('响应状态:', response.status, response.statusText)
      console.log('响应头:', response.headers.get('content-type'))

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const responseText = await response.text()
      console.log('响应内容前100字符:', responseText.substring(0, 100))

      try {
        config = JSON.parse(responseText)
      } catch (parseError) {
        console.error('JSON解析错误:', parseError)
        console.error('响应内容:', responseText)
        throw new Error(`JSON解析失败: ${parseError.message}`)
      }
    }

    if (config.factoryLayout) {
      layoutConfig.value = config.factoryLayout
    } else if (config.zones && Array.isArray(config.zones)) {
      layoutConfig.value = adaptCustomConfig(config)
    } else {
      throw new Error('未识别的配置文件格式')
    }

    console.log('布局配置加载成功:', layoutConfig.value)
    return layoutConfig.value
  } catch (error) {
    console.error('加载布局配置失败:', error)
    ElMessage.error(`加载布局配置失败: ${error.message}，使用默认配置`)
    layoutConfig.value = getDefaultLayoutConfig()
    return layoutConfig.value
  } finally {
    loading.value = false
  }
}

// 适配自定义配置格式
const adaptCustomConfig = (customConfig) => {
  // 计算原始画布的实际尺寸
  let maxX = 0, maxY = 0
  customConfig.zones.forEach(zone => {
    maxX = Math.max(maxX, zone.x + zone.width)
    maxY = Math.max(maxY, zone.y + zone.height)
  })

  // 使用原始尺寸，添加一些边距
  const originalCanvasWidth = maxX + 50
  const originalCanvasHeight = maxY + 50

  console.log(`原始画布尺寸: ${originalCanvasWidth} x ${originalCanvasHeight}`)

  const adapted = {
    name: "自定义工厂布局",
    description: `包含${customConfig.zones.length}个区域的工厂布局`,
    version: customConfig.version || "1.0",
    canvas: {
      width: originalCanvasWidth,
      height: originalCanvasHeight,
      backgroundColor: "#0f172a",
      gridSize: 20
    },
    zones: [],
    statusDistribution: { operational: 0.7, warning: 0.15, error: 0.1, idle: 0.05 },
    defaultMetrics: {
      efficiency: { min: 70, max: 100 },
      uptime: { min: 80, max: 100 },
      assetCount: { min: 2, max: 7 },
      taskCount: { min: 1, max: 9 }
    }
  }

  customConfig.zones.forEach(zone => {
    const workstationCount = zone.rows * zone.cols
    const positions = []

    // 计算每个工位的实际尺寸（考虑间距）
    const cellWidth = zone.width / zone.cols
    const cellHeight = zone.height / zone.rows

    for (let row = 0; row < zone.rows; row++) {
      for (let col = 0; col < zone.cols; col++) {
        const workstationId = zone.startWorkstation + row * zone.cols + col
        // 计算工位在区域内的相对位置
        const x = col * cellWidth
        const y = row * cellHeight

        positions.push({
          id: workstationId,
          x: x,
          y: y,
          name: `${zone.name}-工位${workstationId.toString().padStart(3, '0')}`
        })
      }
    }

    adapted.zones.push({
      id: `zone${zone.id}`,
      name: zone.name,
      uniqueName: `${zone.name}-${zone.id}`, // 添加唯一标识符
      description: `${zone.name}生产区域`,
      color: zone.color,
      position: { x: zone.x, y: zone.y, width: zone.width, height: zone.height },
      layout: { type: "grid", rows: zone.rows, cols: zone.cols },
      workstations: { startId: zone.startWorkstation, count: workstationCount, pattern: "grid", positions: positions }
    })
  })

  return adapted
}

// 默认布局配置
const getDefaultLayoutConfig = () => {
  return {
    name: "默认工厂布局",
    description: "包含1个区域的默认工厂布局",
    version: "1.0",
    canvas: {
      width: 1000,
      height: 600,
      backgroundColor: "#0f172a",
      gridSize: 20
    },
    zones: [{
      id: "zone1",
      name: "默认区域",
      uniqueName: "默认区域-1",
      description: "默认区域生产区域",
      color: "#4A90E2",
      position: { x: 100, y: 100, width: 300, height: 270 },
      layout: { type: "grid", rows: 3, cols: 3 },
      workstations: {
        startId: 1,
        count: 9,
        pattern: "grid",
        positions: Array.from({length: 9}, (_, i) => ({
          id: i + 1,
          x: (i % 3) * 100,
          y: Math.floor(i / 3) * 90,
          name: `默认区域-工位${(i + 1).toString().padStart(3, '0')}`
        }))
      }
    }],
    statusDistribution: { operational: 0.7, warning: 0.15, error: 0.1, idle: 0.05 },
    defaultMetrics: {
      efficiency: { min: 70, max: 100 },
      uptime: { min: 80, max: 100 },
      assetCount: { min: 2, max: 7 },
      taskCount: { min: 1, max: 9 }
    }
  }
}

// 根据JSON配置生成工位数据
const generateWorkstationDataFromLayout = () => {
  if (!layoutConfig.value) return []

  const workstations = []
  const statuses = ['operational', 'warning', 'error', 'idle']
  const distribution = layoutConfig.value.statusDistribution
  const statusWeights = [distribution.operational, distribution.warning, distribution.error, distribution.idle]

  layoutConfig.value.zones.forEach(zone => {
    // 根据区域的行列数生成对应数量的工位
    const { rows, cols } = zone.layout
    const startId = zone.workstations.startId

    for (let row = 0; row < rows; row++) {
      for (let col = 0; col < cols; col++) {
        const workstationId = startId + row * cols + col

        const random = Math.random()
        let status = 'operational'
        let cumulative = 0

        for (let j = 0; j < statusWeights.length; j++) {
          cumulative += statusWeights[j]
          if (random < cumulative) {
            status = statuses[j]
            break
          }
        }

        const metrics = layoutConfig.value.defaultMetrics || {
          efficiency: { min: 70, max: 100 },
          uptime: { min: 80, max: 100 },
          assetCount: { min: 2, max: 7 },
          taskCount: { min: 1, max: 9 }
        }

        workstations.push({
          locationId: workstationId,
          locationName: `${zone.name}-工位${workstationId.toString().padStart(3, '0')}`,
          locationCode: `WS${workstationId.toString().padStart(3, '0')}`,
          departmentName: zone.uniqueName, // 使用唯一标识符
          originalDepartmentName: zone.name, // 保留原始名称用于显示
          status: status,
          efficiency: Math.floor(Math.random() * (metrics.efficiency.max - metrics.efficiency.min + 1)) + metrics.efficiency.min,
          uptime: Math.floor(Math.random() * (metrics.uptime.max - metrics.uptime.min + 1)) + metrics.uptime.min,
          assetCount: Math.floor(Math.random() * (metrics.assetCount.max - metrics.assetCount.min + 1)) + metrics.assetCount.min,
          taskCount: Math.floor(Math.random() * (metrics.taskCount.max - metrics.taskCount.min + 1)) + metrics.taskCount.min,
          faultCount: status === 'error' ? Math.floor(Math.random() * 3) + 1 : status === 'warning' ? Math.floor(Math.random() * 2) : 0,
          lastUpdate: new Date(),
          zoneColor: zone.color,
          zoneId: zone.id,
          isHighlighted: false,
          // 添加在区域内的相对位置信息
          relativeRow: row,
          relativeCol: col
        })
      }
    }
  })

  console.log(`生成了 ${workstations.length} 个工位数据`)
  console.log('工位分布详情:')
  layoutConfig.value.zones.forEach(zone => {
    const zoneWorkstations = workstations.filter(w => w.departmentName === zone.uniqueName)
    console.log(`  ${zone.name}(${zone.uniqueName}): ${zoneWorkstations.length}个工位 (${zone.layout.rows}x${zone.layout.cols})`)
  })
  return workstations
}

// 获取指定区域的工位列表
const getZoneWorkstations = (zoneName) => {
  return locations.value.filter(location => location.departmentName === zoneName)
}

// 获取排序后的区域工位列表（按照网格位置排序）
const getSortedZoneWorkstations = (zone) => {
  // 使用唯一标识符来获取工位
  const zoneWorkstations = locations.value.filter(location => location.departmentName === zone.uniqueName)

  console.log(`获取区域 ${zone.name}(${zone.uniqueName}) 的工位: ${zoneWorkstations.length}个`)

  // 按照行列位置排序，确保工位按照网格顺序显示
  return zoneWorkstations.sort((a, b) => {
    if (a.relativeRow !== b.relativeRow) {
      return a.relativeRow - b.relativeRow
    }
    return a.relativeCol - b.relativeCol
  })
}

// 工位样式类
const getWorkstationClasses = (location) => {
  return [`status-${location.status}`, { 'highlighted': location.isHighlighted }]
}

// 获取区域容器样式
const getZoneContainerStyle = (zone) => {
  if (!zone.position) return {}

  return {
    position: 'absolute',
    left: `${zone.position.x}px`,
    top: `${zone.position.y}px`,
    width: `${zone.position.width}px`,
    height: `${zone.position.height}px`,
    border: `2px solid ${zone.color}60`,
    borderRadius: '8px',
    backgroundColor: `${zone.color}15`
  }
}

// 获取区域网格样式
const getZoneGridStyle = (zone) => {
  if (!zone.layout) return {}

  const { rows, cols } = zone.layout
  return {
    display: 'grid',
    gridTemplateColumns: `repeat(${cols}, 1fr)`,
    gridTemplateRows: `repeat(${rows}, 1fr)`,
    gap: '2px',
    height: '100%',
    width: '100%',
    padding: '5px'
  }
}

// 筛选后的位置数据
const filteredLocations = computed(() => {
  let filtered = locations.value

  // 状态筛选
  if (statusFilters.value.length < 4) {
    filtered = filtered.filter(location => statusFilters.value.includes(location.status))
  }

  // 搜索筛选
  if (searchTerm.value) {
    const term = searchTerm.value.toLowerCase()
    filtered = filtered.filter(location =>
      location.locationName.toLowerCase().includes(term) ||
      location.locationCode.toLowerCase().includes(term) ||
      location.locationId.toString().includes(term)
    )
  }

  // 效率范围筛选
  filtered = filtered.filter(location =>
    location.efficiency >= efficiencyRange.value[0] &&
    location.efficiency <= efficiencyRange.value[1]
  )

  return filtered
})

// 筛选相关计算属性
const hasActiveFilters = computed(() => {
  return statusFilters.value.length < 4 ||
         searchTerm.value.length > 0 ||
         efficiencyRange.value[0] > 0 ||
         efficiencyRange.value[1] < 100
})

const activeFilterCount = computed(() => {
  let count = 0
  if (statusFilters.value.length < 4) count++
  if (searchTerm.value.length > 0) count++
  if (efficiencyRange.value[0] > 0 || efficiencyRange.value[1] < 100) count++
  return count
})

// 重点监控工位
const priorityWorkstations = computed(() => {
  return locations.value.filter(location =>
    location.status === 'error' ||
    location.status === 'warning' ||
    location.efficiency < 70
  ).sort((a, b) => {
    // 故障优先，然后按效率排序
    if (a.status === 'error' && b.status !== 'error') return -1
    if (b.status === 'error' && a.status !== 'error') return 1
    if (a.status === 'warning' && b.status !== 'warning') return -1
    if (b.status === 'warning' && a.status !== 'warning') return 1
    return a.efficiency - b.efficiency
  })
})

// 区域统计
const zoneStats = computed(() => {
  const stats = {}

  if (layoutConfig.value?.zones) {
    layoutConfig.value.zones.forEach(zone => {
      const zoneLocations = locations.value.filter(loc =>
        loc.zoneId === zone.id || loc.zoneName === zone.name
      )

      stats[zone.id] = {
        total: zoneLocations.length,
        operational: zoneLocations.filter(l => l.status === 'operational').length,
        warning: zoneLocations.filter(l => l.status === 'warning').length,
        error: zoneLocations.filter(l => l.status === 'error').length,
        idle: zoneLocations.filter(l => l.status === 'idle').length,
        efficiency: zoneLocations.length > 0
          ? Math.round(zoneLocations.reduce((sum, l) => sum + l.efficiency, 0) / zoneLocations.length)
          : 0
      }
    })
  }

  return stats
})

// 平均效率
const avgEfficiency = computed(() => {
  if (locations.value.length === 0) return 0
  const total = locations.value.reduce((sum, loc) => sum + loc.efficiency, 0)
  return Math.round(total / locations.value.length)
})

// 总设备数
const totalAssets = computed(() => {
  return locations.value.reduce((sum, loc) => sum + (loc.assetCount || 0), 0)
})

// 计算属性
const stats = computed(() => {
  const total = locations.value.length
  const operational = locations.value.filter(l => l.status === 'operational').length
  const warning = locations.value.filter(l => l.status === 'warning').length
  const error = locations.value.filter(l => l.status === 'error').length
  const idle = locations.value.filter(l => l.status === 'idle').length

  return {
    total,
    operational,
    warning,
    error,
    idle,
    operationalPercent: total > 0 ? Math.round(operational / total * 100) : 0
  }
})

const formattedTime = computed(() => {
  return lastUpdate.value.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
})

const canvasViewBox = computed(() => {
  if (layoutConfig.value && layoutConfig.value.canvas) {
    const { width, height } = layoutConfig.value.canvas
    return `0 0 ${width} ${height}`
  }
  return '0 0 1200 500'
})

// 计算缩放比例和容器样式
const containerStyle = computed(() => {
  if (!layoutConfig.value || !layoutConfig.value.canvas) {
    return {}
  }

  const { width: canvasWidth, height: canvasHeight } = layoutConfig.value.canvas

  // 假设显示区域大小（可以根据实际情况调整）
  const displayWidth = 900  // 右侧显示区域宽度
  const displayHeight = 400 // 右侧显示区域高度

  // 计算基础缩放比例，保持宽高比
  const scaleX = displayWidth / canvasWidth
  const scaleY = displayHeight / canvasHeight
  const baseScale = Math.min(scaleX, scaleY, 1) // 不放大，只缩小

  // 应用用户缩放级别
  const finalScale = baseScale * zoomLevel.value

  console.log(`画布尺寸: ${canvasWidth}x${canvasHeight}, 显示区域: ${displayWidth}x${displayHeight}, 基础缩放: ${baseScale}, 最终缩放: ${finalScale}`)

  return {
    width: `${canvasWidth}px`,
    height: `${canvasHeight}px`,
    transform: `scale(${finalScale})`,
    transformOrigin: 'center center',
    transition: 'transform 0.3s ease'
  }
})

// 文件处理
const handleFileChange = (file) => {
  console.log('选择文件:', file.name)

  if (!file.raw) {
    ElMessage.error('文件读取失败')
    return
  }

  const reader = new FileReader()
  reader.onload = (e) => {
    try {
      const content = e.target.result
      const jsonData = JSON.parse(content)

      importedFileName.value = file.name
      importedFileContent.value = jsonData

      console.log('JSON文件解析成功:', jsonData)
      ElMessage.success(`文件 ${file.name} 导入成功`)

      // 自动加载新配置
      loadLayoutConfig().then(() => {
        locations.value = generateWorkstationDataFromLayout()
        lastUpdate.value = new Date()
      })

    } catch (error) {
      console.error('JSON文件解析失败:', error)
      ElMessage.error(`JSON文件解析失败: ${error.message}`)
      importedFileName.value = ''
      importedFileContent.value = null
    }
  }

  reader.onerror = () => {
    ElMessage.error('文件读取失败')
    importedFileName.value = ''
    importedFileContent.value = null
  }

  reader.readAsText(file.raw)
}

// 加载默认JSON文件
const loadDefaultJson = async () => {
  try {
    console.log('手动加载默认JSON文件...')
    const success = await autoLoadExistingJson()

    if (success) {
      // 重新加载配置
      await loadLayoutConfig()
      locations.value = generateWorkstationDataFromLayout()
      lastUpdate.value = new Date()
    } else {
      ElMessage.warning('默认JSON文件不存在，请选择其他文件或使用默认布局')
    }
  } catch (error) {
    console.error('加载默认JSON文件失败:', error)
    ElMessage.error('加载默认JSON文件失败')
  }
}

// 新增的交互方法
const isLocationVisible = (location) => {
  return filteredLocations.value.includes(location)
}

const getEfficiencyColor = (efficiency) => {
  if (efficiency >= 90) return '#22c55e'
  if (efficiency >= 70) return '#eab308'
  if (efficiency >= 50) return '#f97316'
  return '#ef4444'
}

const getStatusText = (status) => {
  const statusMap = {
    operational: '正常运行',
    warning: '警告状态',
    error: '故障状态',
    idle: '空闲状态'
  }
  return statusMap[status] || '未知状态'
}

const getStatusPercent = (status) => {
  const total = stats.value.total
  return total > 0 ? (stats.value[status] / total * 100) : 0
}

const getZoneName = (zoneId) => {
  const zoneNames = {
    zone1: '区域1',
    zone2: '区域2',
    zone3: '区域3',
    zone4: '区域4',
    zone5: '区域5',
    zone6: '区域6',
    zone7: '区域7'
  }
  return zoneNames[zoneId] || '未知区域'
}

// 搜索功能
const handleSearch = (value) => {
  if (!value) {
    searchResults.value = []
    return
  }

  const term = value.toLowerCase()
  searchResults.value = locations.value.filter(location =>
    location.locationName.toLowerCase().includes(term) ||
    location.locationCode.toLowerCase().includes(term) ||
    location.locationId.toString().includes(term)
  ).slice(0, 10)
}

const selectSearchResult = (result) => {
  searchTerm.value = ''
  searchResults.value = []
  selectWorkstation(result.locationId)
}

// 筛选功能
const filterByStatus = (status) => {
  statusFilters.value = [status]
  applyFilters()
}

const setViewMode = (mode) => {
  viewMode.value = mode
}

// 缩放控制
const zoomIn = () => {
  if (zoomLevel.value < 2) {
    zoomLevel.value = Math.min(zoomLevel.value + 0.2, 2)
  }
}

const zoomOut = () => {
  if (zoomLevel.value > 0.5) {
    zoomLevel.value = Math.max(zoomLevel.value - 0.2, 0.5)
  }
}

const resetZoom = () => {
  zoomLevel.value = 1
}

// 全屏控制
const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value
  if (isFullscreen.value) {
    document.documentElement.requestFullscreen?.()
  } else {
    document.exitFullscreen?.()
  }
}

// 筛选控制
const applyFilters = () => {
  // 筛选逻辑已在计算属性中处理
  console.log('应用筛选:', {
    statusFilters: statusFilters.value,
    searchTerm: searchTerm.value,
    efficiencyRange: efficiencyRange.value
  })
}

const resetFilters = () => {
  statusFilters.value = ['operational', 'warning', 'error', 'idle']
  searchTerm.value = ''
  efficiencyRange.value = [0, 100]
}

// 提示框控制
const showTooltip = (location, event) => {
  tooltipLocation.value = location
  tooltipX.value = event.clientX + 10
  tooltipY.value = event.clientY - 50
  tooltipVisible.value = true
}

const hideTooltip = () => {
  tooltipVisible.value = false
  tooltipLocation.value = null
}

// 工位选择
const selectWorkstation = (locationId) => {
  console.log('选择工位:', locationId)
  selectedLocationId.value = locationId

  const location = locations.value.find(loc => loc.locationId === locationId)
  if (location) {
    selectedLocation.value = {
      ...location,
      locationPath: `${location.originalDepartmentName || location.departmentName} / ${location.locationName}`,
      effectiveDepartmentName: location.originalDepartmentmentName || location.departmentName,
      directDepartmentName: location.departmentName,
      details: {
        temperature: Math.round(Math.random() * 20 + 20) + '°C',
        pressure: Math.round(Math.random() * 10 + 95) + 'kPa',
        uptime: Math.round(Math.random() * 20 + 80) + '%',
        lastMaintenance: '2天前',
        errorCode: location.status === 'error' ? 'E001' : null,
        warningMessage: location.status === 'warning' ? '温度偏高，请注意监控' : null
      },
      assets: [
        { assetId: 1, assetCode: 'NC001', assetName: '数控机床', assetTypeName: '加工设备' },
        { assetId: 2, assetCode: 'QI002', assetName: '质检仪', assetTypeName: '检测设备' }
      ]
    }
    showDetailsDrawer.value = true
  }
}

// 事件处理
const handleLocationClick = (location) => {
  selectWorkstation(location.locationId)
}

const handleRowClick = (row) => {
  selectWorkstation(row.locationId)
}

const closeLocationDetail = () => {
  selectedLocation.value = null
  selectedLocationId.value = null
  showDetailsDrawer.value = false
}

// 刷新数据
const refreshData = async () => {
  refreshing.value = true
  try {
    await loadLocations()
    lastUpdate.value = new Date()
    ElMessage.success('数据刷新成功')
  } catch (error) {
    ElMessage.error('数据刷新失败')
  } finally {
    refreshing.value = false
  }
}

// 全屏控制
const toggleFullScreen = () => {
  isFullscreen.value = !isFullscreen.value
  if (isFullscreen.value) {
    document.documentElement.requestFullscreen?.()
  } else {
    document.exitFullscreen?.()
  }
}

// 表格相关方法
const getEfficiencyClass = (efficiency) => {
  if (efficiency >= 80) return 'efficiency-high'
  if (efficiency >= 60) return 'efficiency-medium'
  return 'efficiency-low'
}

const getStatusTagType = (status) => {
  const typeMap = {
    operational: 'success',
    warning: 'warning',
    error: 'danger',
    idle: 'info'
  }
  return typeMap[status] || 'info'
}

const handleRefresh = async () => {
  refreshing.value = true
  try {
    await loadLayoutConfig()
    locations.value = generateWorkstationDataFromLayout()
    lastUpdate.value = new Date()
    ElMessage.success('数据已刷新')
  } catch (error) {
    console.error('刷新失败:', error)
    ElMessage.error('刷新失败')
  } finally {
    refreshing.value = false
  }
}

const handleConfigChange = async () => {
  try {
    let configName = ''
    switch (configType.value) {
      case 'default':
        configName = '默认'
        break
      case 'custom':
        configName = '自定义'
        break
      case 'import':
        configName = '导入文件'
        // 清空之前的导入数据，等待用户选择新文件
        if (!importedFileContent.value) {
          importedFileName.value = ''
          ElMessage.info('请选择JSON文件进行导入')
          return
        }
        break
    }

    console.log(`切换到${configName}布局配置`)
    await loadLayoutConfig()
    locations.value = generateWorkstationDataFromLayout()
    lastUpdate.value = new Date()
    ElMessage.success(`已切换到${configName}布局: ${locations.value.length}个工位`)
  } catch (error) {
    console.error('切换配置失败:', error)
    ElMessage.error('切换配置失败')
  }
}

// 自动加载现有JSON文件
const autoLoadExistingJson = async () => {
  try {
    console.log('尝试自动加载现有JSON文件...')
    const response = await fetch(`/analyresport/${customConfigFile.value}`)

    if (response.ok) {
      const jsonData = await response.json()
      importedFileName.value = customConfigFile.value
      importedFileContent.value = jsonData
      console.log('自动加载JSON文件成功:', customConfigFile.value)
      ElMessage.success(`自动加载布局文件: ${customConfigFile.value}`)
      return true
    }
  } catch (error) {
    console.log('自动加载JSON文件失败，将使用默认配置:', error.message)
  }
  return false
}

// 初始化
const initDashboard = async () => {
  try {
    console.log('开始初始化工厂仪表板...')

    // 如果是导入模式，尝试自动加载现有JSON文件
    if (configType.value === 'import') {
      await autoLoadExistingJson()
    }

    await loadLayoutConfig()
    locations.value = generateWorkstationDataFromLayout()
    console.log(`工厂仪表板初始化完成: ${locations.value.length}个工位`)

    // 定时更新数据
    setInterval(() => {
      lastUpdate.value = new Date()
    }, 5000)
  } catch (error) {
    console.error('初始化工厂仪表板失败:', error)
    ElMessage.error('初始化失败，请刷新页面重试')
  }
}

// 新增的辅助函数
const getZoneWorkstationCount = (zone) => {
  return getSortedZoneWorkstations(zone).length
}

const getZoneEfficiency = (zone) => {
  const workstations = getSortedZoneWorkstations(zone)
  if (workstations.length === 0) return 0
  const totalEfficiency = workstations.reduce((sum, ws) => sum + (ws.efficiency || 0), 0)
  return Math.round(totalEfficiency / workstations.length)
}

const getShortLocationName = (locationName) => {
  if (!locationName) return ''
  // 截取名称，避免过长
  return locationName.length > 10 ? locationName.substring(0, 8) + '...' : locationName
}

const getEfficiencyLevel = (efficiency) => {
  if (efficiency >= 90) return 'excellent'
  if (efficiency >= 80) return 'good'
  if (efficiency >= 70) return 'average'
  return 'poor'
}

const getEfficiencyColor = (efficiency) => {
  if (efficiency >= 90) return '#10b981'
  if (efficiency >= 80) return '#3b82f6'  
  if (efficiency >= 70) return '#f59e0b'
  return '#ef4444'
}

// 生命周期
onMounted(async () => {
  await initDashboard()
})
</script>

<style scoped>
/* Import the enhanced styles from our previous dashboard */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

:root {
  --color-industrial-900: #0c1a25;
  --color-industrial-800: #122738;
  --color-industrial-700: #1a3650;
  --color-industrial-600: #23527c;
  --color-industrial-500: #2e6da4;
  --color-industrial-400: #3d8fd1;
  --color-industrial-300: #66b0ff;
  --color-status-operational: #10b981;
  --color-status-warning: #f59e0b;
  --color-status-error: #ef4444;
  --color-status-idle: #3b82f6;
}

.modern-factory-dashboard {
  min-height: 100vh;
  background: linear-gradient(135deg, var(--color-industrial-900) 0%, var(--color-industrial-800) 100%);
  color: #e2e8f0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  position: relative;
  overflow-x: hidden;
}

/* Background pattern */
.modern-factory-dashboard::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(circle at 1px 1px, rgba(66, 153, 225, 0.05) 1px, transparent 0),
    radial-gradient(circle at 1px 1px, rgba(66, 153, 225, 0.03) 1px, transparent 0);
  background-size: 40px 40px, 20px 20px;
  background-position: 0 0, 20px 20px;
  pointer-events: none;
  z-index: -1;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: calc(100vh - 140px);
  background: rgba(30, 41, 59, 0.4);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 16px;
  padding: 2rem;
}

/* Header Styles */
.dashboard-header {
  padding: 1.25rem 1.5rem;
  background: rgba(18, 39, 56, 0.95);
  backdrop-filter: blur(16px);
  border-bottom: 1px solid rgba(59, 130, 246, 0.3);
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.header-container {
  max-width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 2rem;
}

.brand-section {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.brand-icon {
  position: relative;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--color-industrial-500), var(--color-industrial-400));
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 8px 25px rgba(45, 122, 164, 0.3);
}

.icon-glow {
  position: absolute;
  inset: -2px;
  background: linear-gradient(135deg, var(--color-industrial-400), var(--color-industrial-300));
  border-radius: 18px;
  z-index: -1;
  opacity: 0.5;
  filter: blur(4px);
}

.brand-content {
  flex: 1;
}

.brand-title {
  font-size: 1.75rem;
  font-weight: 700;
  margin: 0;
  background: linear-gradient(135deg, #f1f5f9, #66b0ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: -0.025em;
}

.brand-subtitle {
  font-size: 0.875rem;
  color: #94a3b8;
  margin: 0.25rem 0 0 0;
  font-weight: 400;
}

.status-badge {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  border-radius: 12px;
  padding: 0.75rem 1rem;
  border: 1px solid;
  background: rgba(16, 185, 129, 0.1);
  border-color: rgba(16, 185, 129, 0.3);
}

.status-badge.warning {
  background: rgba(245, 158, 11, 0.1);
  border-color: rgba(245, 158, 11, 0.3);
}

.status-badge.offline {
  background: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.3);
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--color-status-operational);
  animation: pulse 2s infinite;
}

.status-badge.warning .status-indicator {
  background: var(--color-status-warning);
}

.status-badge.offline .status-indicator {
  background: var(--color-status-error);
}

/* Header Actions */
.header-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.search-container {
  position: relative;
}

.smart-search {
  width: 280px;
  transition: all 0.3s ease;
}

.smart-search:focus-within {
  width: 320px;
}

.search-icon {
  color: var(--color-industrial-400);
}

.search-results {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: rgba(18, 39, 56, 0.95);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 12px;
  margin-top: 0.5rem;
  z-index: 50;
  max-height: 200px;
  overflow-y: auto;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.search-result-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 1px solid rgba(59, 130, 246, 0.1);
}

.search-result-item:hover {
  background: rgba(59, 130, 246, 0.1);
}

.result-status {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.result-status.operational { background: var(--color-status-operational); }
.result-status.warning { background: var(--color-status-warning); }
.result-status.error { background: var(--color-status-error); }
.result-status.idle { background: var(--color-status-idle); }

.result-content {
  flex: 1;
}

.result-name {
  font-weight: 500;
  color: #e2e8f0;
  font-size: 0.875rem;
  display: block;
}

.result-info {
  font-size: 0.75rem;
  color: #94a3b8;
  margin-top: 0.125rem;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.action-btn {
  min-width: 44px;
  height: 44px;
  border-radius: 12px;
  border: 1px solid rgba(59, 130, 246, 0.3);
  background: rgba(18, 39, 56, 0.6);
  color: #94a3b8;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0 1rem;
  font-weight: 500;
}

.action-btn:hover {
  background: rgba(59, 130, 246, 0.1);
  border-color: rgba(59, 130, 246, 0.5);
  color: #e2e8f0;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

.action-btn.active {
  background: var(--color-industrial-500);
  border-color: var(--color-industrial-400);
  color: white;
  box-shadow: 0 4px 15px rgba(45, 122, 164, 0.3);
}

.filter-btn.active {
  background: var(--color-status-warning);
  border-color: rgba(245, 158, 11, 0.5);
  color: white;
}

.view-toggle {
  display: flex;
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid rgba(59, 130, 246, 0.3);
}

.view-btn {
  border: none;
  border-radius: 0;
  background: rgba(18, 39, 56, 0.6);
  color: #94a3b8;
  min-width: 44px;
  height: 44px;
  transition: all 0.3s ease;
}

.view-btn:hover {
  background: rgba(59, 130, 246, 0.1);
  color: #e2e8f0;
}

.view-btn.active {
  background: var(--color-industrial-500);
  color: white;
}

/* Main Content */
.dashboard-main {
  flex: 1;
  padding: 1.5rem;
  max-width: 100%;
}

.dashboard-container {
  display: grid;
  grid-template-columns: 350px 1fr;
  gap: 2rem;
  height: calc(100vh - 180px);
  max-width: 100%;
}

.stats-panel {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.filter-panel {
  display: flex;
  flex-direction: column;
}

.stats-card, .filter-card {
  background: rgba(30, 41, 59, 0.6);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 16px;
}

.filter-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filter-label {
  font-size: 0.875rem;
  font-weight: 600;
  color: #f1f5f9;
  margin-bottom: 0.5rem;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
}

.update-time {
  font-size: 0.75rem;
  color: #64748b;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  background: rgba(51, 65, 85, 0.3);
  border-radius: 12px;
  border-left: 4px solid;
  transition: all 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px -8px rgba(0, 0, 0, 0.3);
}

.stat-item.operational { border-left-color: #22c55e; }
.stat-item.warning { border-left-color: #eab308; }
.stat-item.error { border-left-color: #ef4444; }
.stat-item.idle { border-left-color: #6b7280; }

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
}

.operational .stat-icon { background: rgba(34, 197, 94, 0.2); color: #22c55e; }
.warning .stat-icon { background: rgba(234, 179, 8, 0.2); color: #eab308; }
.error .stat-icon { background: rgba(239, 68, 68, 0.2); color: #ef4444; }
.idle .stat-icon { background: rgba(107, 114, 128, 0.2); color: #6b7280; }

.stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  line-height: 1;
}

.stat-label {
  font-size: 0.75rem;
  color: #94a3b8;
  margin-top: 0.25rem;
}

.stat-percent {
  font-size: 0.875rem;
  font-weight: 600;
  color: #22c55e;
}

.factory-layout {
  background: rgba(30, 41, 59, 0.4);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 16px;
  padding: 1rem;
  overflow: hidden;
}

.layout-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid rgba(59, 130, 246, 0.2);
  font-weight: 600;
}

.layout-info-left {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.layout-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.zoom-controls {
  display: flex;
  align-items: center;
}

.factory-floor {
  position: relative;
  height: calc(100% - 60px);
  background: #0f172a;
  border-radius: 12px;
  overflow: auto;
  border: 2px solid rgba(59, 130, 246, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
}

.factory-grid {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.zone-containers {
  position: relative;
  z-index: 2;
  transform-origin: center center;
  /* 尺寸和缩放通过 containerStyle 计算属性动态设置 */
}

.zone-container {
  z-index: 3;
}

.zone-workstations {
  width: 100%;
  height: 100%;
}

.zone-label {
  position: absolute;
  top: -20px;
  left: 5px;
  font-size: 0.75rem;
  font-weight: 600;
  background: rgba(0, 0, 0, 0.7);
  padding: 2px 6px;
  border-radius: 4px;
  z-index: 5;
}

.workstation-cell {
  background: rgba(59, 130, 246, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 20px;
  position: relative;
  overflow: hidden;
}

.workstation-cell:hover {
  transform: scale(1.15);
  z-index: 10;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
  border-color: rgba(255, 255, 255, 0.6);
}

.workstation-cell.status-operational {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.9), rgba(16, 185, 129, 0.9));
  border-color: rgba(34, 197, 94, 0.6);
}

.workstation-cell.status-warning {
  background: linear-gradient(135deg, rgba(234, 179, 8, 0.9), rgba(245, 158, 11, 0.9));
  border-color: rgba(234, 179, 8, 0.6);
}

.workstation-cell.status-error {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.9), rgba(220, 38, 38, 0.9));
  border-color: rgba(239, 68, 68, 0.6);
  animation: pulse-error 2s infinite;
}

.workstation-cell.status-idle {
  background: linear-gradient(135deg, rgba(107, 114, 128, 0.9), rgba(75, 85, 99, 0.9));
  border-color: rgba(107, 114, 128, 0.6);
}

.efficiency-bar {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 0 0 6px 6px;
}

.efficiency-fill {
  height: 100%;
  transition: width 0.3s ease;
  border-radius: 0 0 6px 6px;
}

.selection-border {
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border: 2px solid #fbbf24;
  border-radius: 8px;
  pointer-events: none;
  animation: selection-glow 1.5s infinite;
}

.cell-content {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  font-size: 10px;
  font-weight: 600;
  color: white;
  text-shadow: 0 1px 2px rgba(0,0,0,0.5);
}

.status-icon {
  font-size: 12px;
}

.cell-number {
  font-size: 8px;
}

/* 图例样式 */
.map-legend {
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 8px;
  padding: 12px;
  min-width: 150px;
  z-index: 100;
}

.legend-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: #f1f5f9;
  margin-bottom: 8px;
  text-align: center;
}

.legend-items {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.75rem;
  color: #e2e8f0;
}

.legend-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.legend-dot.operational {
  background: linear-gradient(135deg, #22c55e, #16a34a);
}

.legend-dot.warning {
  background: linear-gradient(135deg, #eab308, #ca8a04);
}

.legend-dot.error {
  background: linear-gradient(135deg, #ef4444, #dc2626);
}

.legend-dot.idle {
  background: linear-gradient(135deg, #6b7280, #4b5563);
}

/* 提示框样式 */
.location-tooltip {
  position: fixed;
  background: rgba(0, 0, 0, 0.95);
  color: #fff;
  border-radius: 8px;
  padding: 12px;
  font-size: 12px;
  max-width: 280px;
  z-index: 10000;
  pointer-events: none;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.tooltip-header {
  margin-bottom: 8px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding-bottom: 6px;
}

.tooltip-title {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 2px;
}

.tooltip-code {
  font-size: 11px;
  color: #94a3b8;
}

.tooltip-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.tooltip-section {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.section-title {
  font-size: 11px;
  color: #94a3b8;
  font-weight: 500;
}

.status-info, .asset-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.status-badge {
  display: inline-block;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 600;
}

.status-badge.operational {
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
}

.status-badge.warning {
  background: rgba(234, 179, 8, 0.2);
  color: #eab308;
}

.status-badge.error {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

.status-badge.idle {
  background: rgba(107, 114, 128, 0.2);
  color: #6b7280;
}

.efficiency-text {
  font-size: 11px;
  color: #60a5fa;
}

.department-name {
  font-size: 12px;
  color: #34d399;
}

.asset-info span {
  font-size: 11px;
  color: #e2e8f0;
}

/* 全屏样式 */
.factory-floor.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  background: #0f172a;
}

/* 动画效果 */
@keyframes pulse-error {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(239, 68, 68, 0);
  }
}

@keyframes selection-glow {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(251, 191, 36, 0.7);
  }
  50% {
    box-shadow: 0 0 0 4px rgba(251, 191, 36, 0);
  }
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .dashboard-main {
    grid-template-columns: 1fr;
    grid-template-rows: auto auto 1fr;
  }

  .stats-panel, .filter-panel {
    grid-column: 1;
  }

  .factory-layout {
    grid-column: 1;
  }
}

/* Table Styles */
.factory-list {
  height: calc(100% - 80px);
}

.efficiency-high {
  color: var(--color-status-operational);
  font-weight: 600;
}

.efficiency-medium {
  color: var(--color-status-warning);
  font-weight: 600;
}

.efficiency-low {
  color: var(--color-status-error);
  font-weight: 600;
}

.fault-count-text {
  color: var(--color-status-error);
  font-weight: 600;
}

.no-fault {
  color: #94a3b8;
}

/* Filter Popover */
:deep(.filter-popover) {
  background: rgba(18, 39, 56, 0.95);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 16px;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
}

.filter-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.filter-section {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filter-section label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #e2e8f0;
}

.range-display {
  font-size: 0.75rem;
  color: #94a3b8;
  text-align: center;
  margin-top: 0.5rem;
}

.filter-actions {
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
  padding-top: 1rem;
  border-top: 1px solid rgba(59, 130, 246, 0.2);
}

/* Element Plus Customization */
:deep(.el-input__wrapper) {
  background: rgba(18, 39, 56, 0.8);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 12px;
  transition: all 0.3s ease;
}

:deep(.el-input__wrapper:hover) {
  border-color: rgba(59, 130, 246, 0.5);
}

:deep(.el-input__wrapper.is-focus) {
  border-color: var(--color-industrial-400);
  box-shadow: 0 0 15px rgba(61, 143, 209, 0.3);
}

:deep(.el-input__inner) {
  color: #e2e8f0;
  background: transparent;
}

:deep(.el-input__inner::placeholder) {
  color: #94a3b8;
}

:deep(.el-button) {
  border-radius: 12px;
  font-weight: 500;
  transition: all 0.3s ease;
}

:deep(.el-button--primary) {
  background: var(--color-industrial-500);
  border-color: var(--color-industrial-400);
}

:deep(.el-button--primary:hover) {
  background: var(--color-industrial-400);
  border-color: var(--color-industrial-300);
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(45, 122, 164, 0.3);
}

:deep(.el-checkbox) {
  color: #e2e8f0;
}

:deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
  background: var(--color-industrial-500);
  border-color: var(--color-industrial-400);
}

:deep(.el-table) {
  background: transparent;
}

:deep(.el-table th) {
  background: rgba(26, 54, 80, 0.8);
  color: #e2e8f0;
  border-bottom: 1px solid rgba(59, 130, 246, 0.3);
}

:deep(.el-table td) {
  background: rgba(18, 39, 56, 0.6);
  color: #e2e8f0;
  border-bottom: 1px solid rgba(59, 130, 246, 0.2);
}

:deep(.el-table__row:hover) {
  background: rgba(59, 130, 246, 0.1);
}

:deep(.el-drawer) {
  background: rgba(18, 39, 56, 0.95);
  backdrop-filter: blur(16px);
}

:deep(.el-drawer__header) {
  background: rgba(26, 54, 80, 0.8);
  border-bottom: 1px solid rgba(59, 130, 246, 0.3);
  color: #e2e8f0;
}

/* Scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(18, 39, 56, 0.6);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--color-industrial-500);
  border-radius: 4px;
  transition: background 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-industrial-400);
}

@media (max-width: 768px) {
  .header-container {
    flex-direction: column;
    gap: 1rem;
  }

  .header-actions {
    width: 100%;
    justify-content: space-between;
  }

  .smart-search {
    width: 100%;
    max-width: 280px;
  }

  .dashboard-container {
    grid-template-columns: 1fr;
    grid-template-rows: auto 1fr;
    gap: 1rem;
  }

  .stats-panel {
    order: 2;
  }

  .factory-layout {
    order: 1;
  }
}

/* Enhanced Stats Panel Styles */
.stats-panel {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  height: 100%;
  overflow-y: auto;
}

.stats-card {
  background: rgba(18, 39, 56, 0.8);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 16px;
  backdrop-filter: blur(16px);
  padding: 1.5rem;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
}

.stats-card:hover {
  border-color: rgba(59, 130, 246, 0.5);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.4);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.card-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #e2e8f0;
  margin: 0;
}

.update-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.pulse-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--color-status-operational);
  animation: pulse 2s infinite;
}

.update-time {
  font-size: 0.75rem;
  color: #94a3b8;
  font-weight: 500;
}

/* Modern Stat Cards */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.modern-stat-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border-radius: 12px;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.modern-stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, transparent, rgba(255, 255, 255, 0.05));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.modern-stat-card:hover::before {
  opacity: 1;
}

.modern-stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.modern-stat-card.operational {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.15), rgba(16, 185, 129, 0.05));
  border: 1px solid rgba(16, 185, 129, 0.3);
}

.modern-stat-card.warning {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.15), rgba(245, 158, 11, 0.05));
  border: 1px solid rgba(245, 158, 11, 0.3);
}

.modern-stat-card.error {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.15), rgba(239, 68, 68, 0.05));
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.modern-stat-card.idle {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.15), rgba(59, 130, 246, 0.05));
  border: 1px solid rgba(59, 130, 246, 0.3);
}

.stat-visual {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.stat-icon-container {
  position: relative;
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.stat-icon-container.operational {
  background: linear-gradient(135deg, var(--color-status-operational), #059669);
}

.stat-icon-container.warning {
  background: linear-gradient(135deg, var(--color-status-warning), #d97706);
}

.stat-icon-container.error {
  background: linear-gradient(135deg, var(--color-status-error), #dc2626);
}

.stat-icon-container.idle {
  background: linear-gradient(135deg, var(--color-status-idle), #2563eb);
}

.stat-icon {
  font-size: 20px;
  z-index: 2;
}

.icon-glow {
  position: absolute;
  inset: -2px;
  border-radius: 14px;
  z-index: 1;
  opacity: 0.6;
  filter: blur(4px);
}

.icon-glow.operational {
  background: linear-gradient(135deg, var(--color-status-operational), #059669);
}

.icon-glow.warning {
  background: linear-gradient(135deg, var(--color-status-warning), #d97706);
}

.icon-glow.error {
  background: linear-gradient(135deg, var(--color-status-error), #dc2626);
}

.icon-glow.idle {
  background: linear-gradient(135deg, var(--color-status-idle), #2563eb);
}

.stat-progress {
  width: 48px;
  height: 4px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  border-radius: 2px;
  transition: width 0.8s ease;
}

.progress-bar.operational {
  background: var(--color-status-operational);
}

.progress-bar.warning {
  background: var(--color-status-warning);
}

.progress-bar.error {
  background: var(--color-status-error);
}

.progress-bar.idle {
  background: var(--color-status-idle);
}

.stat-data {
  flex: 1;
}

.stat-value {
  font-size: 1.75rem;
  font-weight: 700;
  color: #e2e8f0;
  line-height: 1;
}

.stat-label {
  font-size: 0.875rem;
  color: #94a3b8;
  margin-top: 0.25rem;
  font-weight: 500;
}

.stat-percent {
  font-size: 0.75rem;
  color: var(--color-status-operational);
  font-weight: 600;
  margin-top: 0.25rem;
}

/* System Metrics */
.system-metrics {
  display: flex;
  gap: 1rem;
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid rgba(59, 130, 246, 0.2);
}

.metric-item {
  flex: 1;
  text-align: center;
}

.metric-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--color-industrial-400);
  line-height: 1;
}

.metric-label {
  font-size: 0.75rem;
  color: #94a3b8;
  margin-top: 0.25rem;
  font-weight: 500;
}

/* Priority Workstations */
.priority-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.priority-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(59, 130, 246, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
}

.priority-item:hover {
  background: rgba(59, 130, 246, 0.1);
  border-color: rgba(59, 130, 246, 0.3);
  transform: translateX(4px);
}

.priority-status {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.priority-status.operational { background: var(--color-status-operational); }
.priority-status.warning { background: var(--color-status-warning); }
.priority-status.error { background: var(--color-status-error); }
.priority-status.idle { background: var(--color-status-idle); }

.priority-content {
  flex: 1;
}

.priority-name {
  font-weight: 500;
  color: #e2e8f0;
  font-size: 0.875rem;
}

.priority-info {
  font-size: 0.75rem;
  color: #94a3b8;
  margin-top: 0.125rem;
}

.fault-count {
  color: var(--color-status-error);
  font-weight: 600;
}

.priority-arrow {
  color: #94a3b8;
  font-size: 14px;
  transition: transform 0.3s ease;
}

.priority-item:hover .priority-arrow {
  transform: translateX(2px);
}

/* Zone Statistics */
.zone-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.zone-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(59, 130, 246, 0.1);
}

.zone-info {
  flex: 1;
}

.zone-name {
  font-weight: 500;
  color: #e2e8f0;
  font-size: 0.875rem;
}

.zone-metrics {
  display: flex;
  gap: 0.75rem;
  margin-top: 0.25rem;
}

.zone-total, .zone-efficiency {
  font-size: 0.75rem;
  color: #94a3b8;
}

.zone-efficiency {
  color: var(--color-industrial-400);
  font-weight: 600;
}

.status-indicators {
  display: flex;
  gap: 0.25rem;
}

.status-dot {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 20px;
  height: 20px;
  border-radius: 10px;
  font-size: 0.75rem;
  font-weight: 600;
  color: white;
}

.status-dot.operational {
  background: var(--color-status-operational);
}

.status-dot.warning {
  background: var(--color-status-warning);
}

.status-dot.error {
  background: var(--color-status-error);
}

/* 现代化工位样式增强 */
.modern-zone {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.modern-zone:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.3);
}

.modern-zone-label {
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.6));
  backdrop-filter: blur(8px);
  border-radius: 12px;
  padding: 12px 16px;
  margin-bottom: 16px;
  border: 1px solid rgba(59, 130, 246, 0.3);
}

.zone-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.zone-name {
  font-size: 1rem;
  font-weight: 700;
  color: #e2e8f0;
}

.zone-stats {
  font-size: 0.75rem;
  color: #94a3b8;
  background: rgba(59, 130, 246, 0.1);
  padding: 2px 8px;
  border-radius: 12px;
  border: 1px solid rgba(59, 130, 246, 0.3);
}

.zone-efficiency {
  font-size: 0.8rem;
  color: #66b0ff;
  font-weight: 600;
}

.modern-grid {
  gap: 12px !important;
}

.modern-workstation {
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  perspective: 1000px;
  min-height: 90px;
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.9), rgba(30, 41, 59, 0.7));
  border: 1px solid rgba(59, 130, 246, 0.2);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.modern-workstation::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, transparent, rgba(255, 255, 255, 0.05));
  opacity: 0.6;
  z-index: 1;
  transition: opacity 0.3s ease;
}

.modern-workstation:hover {
  transform: translateY(-8px) scale(1.05);
  z-index: 10;
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.4);
  border-color: rgba(59, 130, 246, 0.6);
}

.modern-workstation:hover::before {
  opacity: 1;
}

.modern-workstation.status-operational {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.15), rgba(16, 185, 129, 0.1));
  border-color: rgba(34, 197, 94, 0.4);
}

.modern-workstation.status-warning {
  background: linear-gradient(135deg, rgba(234, 179, 8, 0.15), rgba(245, 158, 11, 0.1));
  border-color: rgba(234, 179, 8, 0.4);
}

.modern-workstation.status-error {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.15), rgba(220, 38, 38, 0.1));
  border-color: rgba(239, 68, 68, 0.4);
  animation: pulse-error 2s infinite;
}

.modern-workstation.status-idle {
  background: linear-gradient(135deg, rgba(107, 114, 128, 0.15), rgba(75, 85, 99, 0.1));
  border-color: rgba(107, 114, 128, 0.4);
}

.cell-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at center, rgba(255, 255, 255, 0.02), transparent);
  z-index: 1;
}

.status-indicator {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  z-index: 5;
  transition: all 0.3s ease;
}

.status-indicator.operational {
  background: #10b981;
  box-shadow: 0 0 12px rgba(16, 185, 129, 0.6);
}

.status-indicator.warning {
  background: #f59e0b;
  box-shadow: 0 0 12px rgba(245, 158, 11, 0.6);
  animation: pulse 2s infinite;
}

.status-indicator.error {
  background: #ef4444;
  box-shadow: 0 0 12px rgba(239, 68, 68, 0.6);
  animation: pulse 1s infinite;
}

.status-indicator.idle {
  background: #6b7280;
  box-shadow: 0 0 8px rgba(107, 114, 128, 0.4);
}

.pulse-ring {
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  border: 2px solid #ef4444;
  border-radius: 50%;
  animation: pulse-ring 1.5s infinite;
}

.modern-content {
  position: relative;
  z-index: 2;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 12px;
}

.workstation-header {
  text-align: center;
  margin-bottom: 8px;
}

.workstation-id {
  font-size: 0.9rem;
  font-weight: 700;
  color: #e2e8f0;
  line-height: 1;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.workstation-name {
  font-size: 0.7rem;
  color: #94a3b8;
  margin-top: 2px;
  line-height: 1;
}

.status-display {
  display: flex;
  justify-content: center;
  margin: 8px 0;
}

.status-icon {
  font-size: 20px;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
  transition: all 0.3s ease;
}

.status-icon.operational {
  color: #10b981;
}

.status-icon.warning {
  color: #f59e0b;
  animation: bounce 2s infinite;
}

.status-icon.error {
  color: #ef4444;
  animation: shake 0.5s infinite;
}

.status-icon.idle {
  color: #6b7280;
}

.efficiency-indicator {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 8px;
}

.efficiency-circle {
  position: relative;
  width: 40px;
  height: 40px;
}

.circular-chart {
  width: 100%;
  height: 100%;
  transform: rotate(-90deg);
}

.circle-bg {
  fill: none;
  stroke: rgba(255, 255, 255, 0.1);
  stroke-width: 2;
}

.circle {
  fill: none;
  stroke-width: 3;
  stroke-linecap: round;
  transition: stroke-dasharray 0.8s ease;
}

.circle.excellent {
  stroke: #10b981;
}

.circle.good {
  stroke: #3b82f6;
}

.circle.average {
  stroke: #f59e0b;
}

.circle.poor {
  stroke: #ef4444;
}

.efficiency-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 0.65rem;
  font-weight: 700;
  color: #e2e8f0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.hover-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.85), rgba(0, 0, 0, 0.7));
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 10;
  border-radius: 12px;
}

.modern-workstation:hover .hover-overlay {
  opacity: 1;
}

.quick-stats {
  display: flex;
  flex-direction: column;
  gap: 6px;
  text-align: center;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.75rem;
}

.stat-label {
  color: #94a3b8;
  font-weight: 500;
}

.stat-value {
  color: #e2e8f0;
  font-weight: 700;
  margin-left: 8px;
}

.stat-value.error {
  color: #ef4444;
}

.modern-selection {
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  border: 3px solid #fbbf24;
  border-radius: 15px;
  pointer-events: none;
  animation: selection-glow 1.5s infinite;
  z-index: 15;
}

.error-pulse {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: 2px solid transparent;
  border-radius: 12px;
  animation: error-pulse-border 2s infinite;
  z-index: 3;
  pointer-events: none;
}

/* 动画定义 */
@keyframes pulse-error {
  0%, 100% { 
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2), 0 0 0 0 rgba(239, 68, 68, 0.7); 
  }
  50% { 
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3), 0 0 0 8px rgba(239, 68, 68, 0); 
  }
}

@keyframes pulse-ring {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(1.4);
    opacity: 0;
  }
}

@keyframes selection-glow {
  0%, 100% { 
    box-shadow: 0 0 0 0 rgba(251, 191, 36, 0.7); 
  }
  50% { 
    box-shadow: 0 0 0 6px rgba(251, 191, 36, 0); 
  }
}

@keyframes error-pulse-border {
  0%, 100% {
    border-color: transparent;
  }
  50% {
    border-color: rgba(239, 68, 68, 0.6);
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-4px);
  }
  60% {
    transform: translateY(-2px);
  }
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }
  20%, 40%, 60%, 80% { transform: translateX(2px); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}
</style>
