/**
 * 航空航天级IT资产管理系统 - 位置关联页面
 * 文件路径: src/views/locations/relations.vue
 * 功能描述: 管理资产与位置的关联关系
 */

<template>
  <div class="location-relations-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2 class="page-title">位置关联</h2>
      <div class="page-actions">
        <el-button type="primary" @click="handleRelateAssets" :icon="Link">关联资产</el-button>
        <el-button type="success" @click="handleRelateDepartment" :icon="OfficeBuilding">关联部门</el-button>
        <el-button type="danger" @click="handleBatchUnrelate" :icon="Remove" :disabled="selectedAssets.length === 0">
          解除关联
        </el-button>
        <!-- 添加工位管理人员和使用人按钮，适用于工序(type=3)和工位(type=4)时显示 -->
        <el-button 
          v-if="currentLocationDetail && (currentLocationDetail.type === 3 || currentLocationDetail.type === 4)" 
          type="warning" 
          @click="handleSetManager" 
          :icon="UserFilled">
          设置管理员
        </el-button>
        <el-button 
          v-if="currentLocationDetail && (currentLocationDetail.type === 3 || currentLocationDetail.type === 4)" 
          type="info" 
          @click="handleSetUsers" 
          :icon="User">
          设置使用人
        </el-button>
      </div>
    </div>
    
    <!-- 主体内容 -->
    <div class="relation-content">
      <!-- 筛选区域 -->
      <el-card class="filter-card">
        <div class="filters">
          <el-row :gutter="16">
            <el-col :span="24">
              <div class="location-view-switch">
                <el-radio-group v-model="viewMode" size="small">
                  <el-radio-button value="tree">树形层级视图</el-radio-button>
                  <el-radio-button value="flat">扁平列表视图</el-radio-button>
                </el-radio-group>
              </div>
            </el-col>
          </el-row>
          
          <el-row :gutter="16" v-if="viewMode === 'flat'">
            <el-col :span="8">
              <el-form-item label="位置选择">
                <el-select
                  v-model="selectedLocation"
                  filterable
                  placeholder="请选择位置"
                  style="width: 100%"
                  @change="handleLocationChange"
                >
                  <el-option
                    v-for="location in locationOptions"
                    :key="location.id"
                    :label="location.fullName"
                    :value="location.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="部门">
                <el-select
                  v-model="departmentFilter"
                  placeholder="全部部门"
                  clearable
                  style="width: 100%"
                  @change="fetchAssetsForLocation"
                >
                  <el-option 
                    v-for="dept in departments" 
                    :key="dept.id" 
                    :label="dept.name" 
                    :value="dept.id" 
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="资产状态">
                <el-select
                  v-model="assetStatusFilter"
                  placeholder="全部状态"
                  clearable
                  style="width: 100%"
                  @change="fetchAssetsForLocation"
                >
                  <el-option 
                    v-for="status in assetStatuses" 
                    :key="status.value" 
                    :label="status.label" 
                    :value="status.value" 
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="16" v-if="viewMode === 'flat'">
            <el-col :span="8">
              <el-form-item label="关键字">
                <el-input
                  v-model="keywordFilter"
                  placeholder="搜索资产名称/编号/SN"
                  clearable
                  @keyup.enter="fetchAssetsForLocation"
                >
                  <template #append>
                    <el-button :icon="Search" @click="fetchAssetsForLocation" />
                  </template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="最后更新时间">
                <el-date-picker
                  v-model="dateRangeFilter"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  value-format="YYYY-MM-DD"
                  @change="fetchAssetsForLocation"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8" class="text-right">
              <el-button type="primary" @click="fetchAssetsForLocation" :icon="Search">搜索</el-button>
              <el-button @click="resetFilters" :icon="RefreshRight">重置</el-button>
            </el-col>
          </el-row>
          
          <el-row :gutter="16" v-if="viewMode === 'tree'">
            <el-col :span="24">
              <el-form-item label="位置层级查询">
                <el-input
                  v-model="treeSearchKeyword"
                  placeholder="搜索位置名称"
                  clearable
                  @keyup.enter="searchLocationTree"
                >
                  <template #append>
                    <el-button :icon="Search" @click="searchLocationTree" />
                  </template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-card>

      <!-- 数据表格/树形结构 -->
      <el-card class="data-card">
        <template #header>
          <div class="card-header">
            <div class="header-info">
              <span v-if="viewMode === 'flat'">位置列表</span>
              <span v-else>位置层级结构</span>
              <el-tag v-if="selectedDepartment && viewMode === 'flat'" type="success" class="department-tag">
                {{ selectedDepartment.name }}
              </el-tag>
            </div>
            <span class="location-path" v-if="viewMode === 'flat'">当前位置：{{ currentLocationPath }}</span>
          </div>
        </template>
        
        <!-- 树形层级视图 -->
        <div v-if="viewMode === 'tree'" class="tree-view-container">
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="tree-container">
                <div class="tree-title">位置层级结构</div>
                <el-scrollbar height="calc(100vh - 350px)" style="border: 1px solid #EBEEF5;">
                  <el-skeleton :loading="loading" animated :rows="10" v-if="loading">
                  </el-skeleton>
                  <el-tree
                    v-else
                    ref="locationTree"
                    :data="locationTreeData"
                    :props="locationTreeProps"
                    node-key="id"
                    highlight-current
                    :default-expand-all="false"
                    :expand-on-click-node="false"
                    @node-click="handleLocationNodeClick"
                    :filter-node-method="filterLocationNode"
                    v-loading="loading"
                  >
                    <template #default="{ node, data }">
                      <div class="location-tree-node">
                        <div class="node-content">
                          <span class="location-name">{{ data.name }}</span>
                          <span class="location-type">
                            <el-tag size="small" :type="getLocationTypeTag(data.type)">
                              {{ getLocationTypeName(data.type) }}
                            </el-tag>
                          </span>
                        </div>
                      </div>
                    </template>
                  </el-tree>
                </el-scrollbar>
              </div>
            </el-col>
            
            <el-col :span="16">
              <div class="location-detail-panel" v-if="selectedLocationForTree">
                <div class="detail-header">
                  <h3>{{ selectedLocationForTree.name }}</h3>
                  <div class="location-path">{{ currentLocationPath }}</div>
                  <div class="location-actions" v-if="selectedLocationForTree.type === 3 || selectedLocationForTree.type === 4">
                    <el-button type="primary" size="small" @click="handleSetManager(selectedLocationForTree)">
                      设置管理员
                    </el-button>
                    <el-button type="success" size="small" @click="handleSetUsers(selectedLocationForTree)">
                      设置使用人
                    </el-button>
                  </div>
                </div>
                
                <el-tabs v-model="activeDetailTab">
                  <el-tab-pane label="位置信息" name="info">
                    <el-descriptions :column="2" border>
                      <el-descriptions-item label="位置名称">{{ selectedLocationForTree.name }}</el-descriptions-item>
                      <el-descriptions-item label="位置类型">
                        <el-tag :type="getLocationTypeTag(selectedLocationForTree.type)">
                          {{ getLocationTypeName(selectedLocationForTree.type) }}
                        </el-tag>
                      </el-descriptions-item>
                      <el-descriptions-item label="上级位置">{{ selectedLocationForTree.parentName || '-' }}</el-descriptions-item>
                      <el-descriptions-item label="使用部门">{{ selectedLocationForTree.departmentName || '-' }}</el-descriptions-item>
                      <el-descriptions-item label="管理人员" :span="2">
                        <div v-if="locationUsersForTree.length > 0">
                          <el-tag 
                            v-for="manager in locationUsersForTree.filter(u => u.userType === 1)" 
                            :key="manager.id"
                            type="warning"
                            style="margin-right: 5px; margin-bottom: 5px"
                          >
                            {{ manager.name || manager.username }}
                          </el-tag>
                          <span v-if="!locationUsersForTree.some(u => u.userType === 1)">未设置</span>
                        </div>
                        <span v-else>未设置</span>
                      </el-descriptions-item>
                      <el-descriptions-item label="使用人员" :span="2">
                        <div v-if="locationUsersForTree.length > 0">
                          <el-tag 
                            v-for="user in locationUsersForTree.filter(u => u.userType === 0)" 
                            :key="user.id"
                            type="info"
                            style="margin-right: 5px; margin-bottom: 5px"
                          >
                            {{ user.name || user.username }}
                          </el-tag>
                          <span v-if="!locationUsersForTree.some(u => u.userType === 0)">未设置</span>
                        </div>
                        <span v-else>未设置</span>
                      </el-descriptions-item>
                      <el-descriptions-item label="备注信息" :span="2">
                        {{ selectedLocationForTree.remark || '-' }}
                      </el-descriptions-item>
                    </el-descriptions>
                    
                    <div class="personnel-info" v-if="selectedLocationForTree.type === 3 || selectedLocationForTree.type === 4">
                      <el-alert
                        title="位置人员管理说明"
                        type="info"
                        :closable="false"
                        style="margin: 20px 0"
                      >
                        <div class="info-content">
                          <p>1. 工序和工位可以设置管理员和使用人员</p>
                          <p>2. 管理员负责此位置的资产和人员管理</p>
                          <p>3. 使用人员是日常使用此位置资产的人员</p>
                          <p>4. 人员信息来自系统用户管理，请确保先添加相关人员</p>
                        </div>
                      </el-alert>
                    </div>
                  </el-tab-pane>
                  
                  <el-tab-pane label="关联资产" name="assets">
                    <div v-loading="treeAssetLoading">
                      <el-empty v-if="locationAssetsForTree.length === 0" description="暂无关联资产"></el-empty>
                      <el-table
                        v-else
                        :data="locationAssetsForTree"
                        style="width: 100%"
                        border
                        stripe
                        size="small"
                      >
                        <el-table-column type="index" label="序号" width="60" align="center"></el-table-column>
                        <el-table-column prop="assetCode" label="资产编号" width="120"></el-table-column>
                        <el-table-column prop="name" label="资产名称" width="150"></el-table-column>
                        <el-table-column prop="assetTypeName" label="资产类型" width="120"></el-table-column>
                        <el-table-column prop="model" label="规格型号" width="120"></el-table-column>
                        <el-table-column label="状态" width="80">
                          <template #default="{ row }">
                            <el-tag :type="getAssetStatusType(row.status)">
                              {{ getAssetStatusText(row.status) }}
                            </el-tag>
                          </template>
                        </el-table-column>
                        <el-table-column label="操作" width="150" fixed="right">
                          <template #default="{ row }">
                            <el-button type="text" @click="viewAssetDetail(row)">查看</el-button>
                            <el-button type="text" class="danger-text" @click="handleUnrelateAsset(row)">解除关联</el-button>
                          </template>
                        </el-table-column>
                      </el-table>
                    </div>
                  </el-tab-pane>
                </el-tabs>
              </div>
              <el-empty 
                v-else 
                description="请选择左侧位置"
                style="margin-top: 100px"
              ></el-empty>
            </el-col>
          </el-row>
        </div>
        
        <!-- 扁平资产列表视图 -->
        <div v-if="viewMode === 'flat'" class="flat-view">
          <!-- 筛选条件 -->
          <el-form :inline="true" class="location-filters">
            <el-form-item label="位置类型">
              <el-select v-model="typeFilter" clearable placeholder="全部类型" style="width: 150px;">
                <el-option label="全部" value=""></el-option>
                <el-option label="工厂" :value="1"></el-option>
                <el-option label="产线" :value="2"></el-option>
                <el-option label="工序" :value="3"></el-option>
                <el-option label="工位" :value="4"></el-option>
              </el-select>
            </el-form-item>
            
            <el-form-item label="上级位置">
              <el-select v-model="parentFilter" filterable clearable placeholder="请选择" style="width: 200px;">
                <el-option 
                  v-for="item in parentOptions" 
                  :key="item.id" 
                  :label="item.name" 
                  :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
            
            <el-form-item label="关键字">
              <el-input v-model="keywordFilter" placeholder="位置名称/编码" style="width: 200px;" />
            </el-form-item>
            
            <el-form-item>
              <el-button type="primary" @click="fetchLocationList">搜索</el-button>
              <el-button @click="resetLocationFilters">重置</el-button>
            </el-form-item>
          </el-form>
          
          <!-- 位置列表表格 -->
          <el-table
            ref="locationTable"
            v-loading="loading"
            :data="locationList"
            style="width: 100%"
            @row-click="handleLocationClick"
            :height="tableHeight"
            border
          >
            <el-table-column type="selection" width="50" />
            <el-table-column prop="code" label="位置编码" width="120" />
            <el-table-column prop="name" label="位置名称" min-width="150" />
            <el-table-column prop="type" label="位置类型" width="100">
            <template #default="scope">
                <el-tag size="small" :type="getLocationTypeTag(scope.row.type)">
                  {{ getLocationTypeName(scope.row.type) }}
              </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="parentName" label="上级位置" min-width="150" />
            <el-table-column prop="departmentName" label="使用部门" min-width="120">
              <template #default="scope">
                <span v-if="scope.row.departmentName">{{ scope.row.departmentName }}</span>
                <el-button v-else type="primary" link @click.stop="handleRelateDepartment(scope.row)">设置部门</el-button>
              </template>
            </el-table-column>
            <el-table-column prop="managerName" label="负责人" width="120">
              <template #default="scope">
                <span v-if="scope.row.managerName">{{ scope.row.managerName }}</span>
                <el-button v-else-if="scope.row.type === 3 || scope.row.type === 4" type="warning" link @click.stop="handleSetManager(scope.row)">设置负责人</el-button>
              <span v-else>-</span>
            </template>
          </el-table-column>
            <el-table-column label="使用人" width="100">
            <template #default="scope">
                <el-button v-if="scope.row.type === 3 || scope.row.type === 4" type="info" link @click.stop="viewLocationUsers(scope.row)">
                  {{ scope.row.userCount || 0 }}人
              </el-button>
                <span v-else>-</span>
            </template>
          </el-table-column>
            <el-table-column label="操作" fixed="right" width="220">
              <template #default="scope">
                <el-button size="small" type="primary" @click.stop="handleRelateAssets(scope.row)">关联资产</el-button>
                <el-dropdown @command="(cmd) => handleCommand(cmd, scope.row)" trigger="click">
                  <el-button size="small" type="primary">
                    更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="setDepartment">设置部门</el-dropdown-item>
                      <el-dropdown-item v-if="scope.row.type === 3 || scope.row.type === 4" command="setManager">设置负责人</el-dropdown-item>
                      <el-dropdown-item v-if="scope.row.type === 3 || scope.row.type === 4" command="setUsers">设置使用人</el-dropdown-item>
                    </el-dropdown-menu>
          </template>
                </el-dropdown>
              </template>
            </el-table-column>
        </el-table>
        
        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="pagination.currentPage"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pagination.total"
              background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
          </div>
        </div>
      </el-card>
    </div>
    
    <!-- 关联资产对话框 -->
    <el-dialog
      title="关联资产"
      v-model="relateDialogVisible"
      width="900px"
      append-to-body
    >
      <div class="relate-dialog-content">
        <div class="filters">
          <el-row :gutter="16">
            <el-col :span="8">
              <el-input
                v-model="assetSearchKeyword"
                placeholder="搜索资产名称/编号/SN"
                clearable
                @keyup.enter="searchUnrelatedAssets"
              >
                <template #append>
                  <el-button :icon="Search" @click="searchUnrelatedAssets" />
                </template>
              </el-input>
            </el-col>
            <el-col :span="8">
              <el-select
                v-model="unrelatedAssetTypeFilter"
                placeholder="资产类型"
                clearable
                style="width: 100%"
                @change="searchUnrelatedAssets"
              >
                <el-option 
                  v-for="type in assetTypes" 
                  :key="type.value" 
                  :label="type.label" 
                  :value="type.value" 
                />
              </el-select>
            </el-col>
            <el-col :span="8">
              <el-select
                v-model="relationType"
                placeholder="关联类型"
                style="width: 100%"
              >
                <el-option label="主要位置" value="primary" />
                <el-option label="次要位置" value="secondary" />
                <el-option label="临时位置" value="temporary" />
              </el-select>
            </el-col>
          </el-row>
        </div>
        
        <el-table
          ref="unrelatedAssetTable"
          v-loading="unrelatedLoading"
          :data="unrelatedAssetList"
          style="width: 100%; margin-top: 16px;"
          @selection-change="handleUnrelatedSelectionChange"
          height="400px"
          border
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="code" label="资产编号" width="120" />
          <el-table-column prop="name" label="资产名称" width="150" />
          <el-table-column prop="type" label="资产类型" width="120">
            <template #default="scope">
              <el-tag size="small" :type="getAssetTypeTag(scope.row.type)">
                {{ scope.row.typeName }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="sn" label="序列号" width="150" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag size="small" :type="getStatusType(scope.row.status)">
                {{ scope.row.statusText }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="department" label="所属部门" width="150" />
          <el-table-column prop="user" label="责任人" width="120" />
          <el-table-column prop="currentLocation" label="当前位置" width="180" />
        </el-table>
        
        <div class="dialog-pagination">
          <el-pagination
            v-model:current-page="unrelatedPagination.currentPage"
            v-model:page-size="unrelatedPagination.pageSize"
            :page-sizes="[10, 20, 50]"
            :background="true"
            layout="total, sizes, prev, pager, next"
            :total="unrelatedPagination.total"
            @size-change="handleUnrelatedSizeChange"
            @current-change="handleUnrelatedCurrentChange"
          />
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="relateDialogVisible = false">取 消</el-button>
          <el-button 
            type="primary" 
            @click="confirmRelateAssets" 
            :disabled="selectedUnrelatedAssets.length === 0 || !relationType"
          >
            确认关联
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 关联部门对话框 -->
    <el-dialog
      title="关联部门"
      v-model="departmentDialogVisible"
      width="600px"
      append-to-body
    >
      <div class="department-dialog-content">
        <el-form
          ref="departmentFormRef"
          :model="departmentFormData"
          label-width="100px"
        >
          <el-form-item label="选择部门" prop="departmentId">
            <el-select
              v-model="departmentFormData.departmentId"
              placeholder="请选择部门"
              style="width: 100%"
            >
              <el-option
                v-for="dept in departments"
                :key="dept.id"
                :label="dept.name"
                :value="dept.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="部门负责人" prop="managerId">
            <el-select
              v-model="departmentFormData.managerId"
              placeholder="请选择部门负责人"
              style="width: 100%"
              :disabled="!departmentFormData.departmentId"
            >
              <el-option
                v-for="user in departmentUsers"
                :key="user.id"
                :label="user.name"
                :value="user.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="departmentFormData.remark"
              type="textarea"
              :rows="3"
              placeholder="请输入备注信息"
            />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="departmentDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitDepartmentForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 设置管理员对话框 -->
    <el-dialog
      title="设置位置管理员"
      v-model="managerDialogVisible"
      width="550px"
      append-to-body
    >
      <div class="manager-dialog-content">
        <el-form
          ref="managerFormRef"
          :model="managerFormData"
          label-width="100px"
        >
          <el-form-item label="管理员" prop="managerId" required>
            <el-select
              v-model="managerFormData.managerId"
              placeholder="请选择位置管理员"
              style="width: 100%"
              filterable
            >
              <el-option
                v-for="user in managerCandidates"
                :key="user.id"
                :label="user.name"
                :value="user.id"
              >
                <div class="user-option">
                  <span>{{ user.name }}</span>
                  <span class="user-detail">{{ user.departmentName || '无部门' }} | {{ user.position || '无职位' }}</span>
                </div>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="managerFormData.remark"
              type="textarea"
              :rows="3"
              placeholder="请输入备注信息"
            />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="managerDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitManagerForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 设置使用人对话框 -->
    <el-dialog
      title="设置位置使用人"
      v-model="usersDialogVisible"
      width="650px"
      append-to-body
    >
      <div class="users-dialog-content">
        <div class="dialog-description">
          <el-alert type="info" :closable="false">
            <p>当前位置: <strong>{{ currentLocationPath }}</strong></p>
            <p>可以为此工位设置多个使用人员。使用人员将可以查看和管理与此位置关联的资产。</p>
          </el-alert>
        </div>

        <div class="dialog-actions">
          <el-button type="primary" @click="handleAddUser" :icon="Plus">添加使用人</el-button>
          <el-checkbox v-model="replaceExistingUsers" label="替换现有使用人" border></el-checkbox>
        </div>

        <el-table
          v-loading="locationUsersLoading"
          :data="selectedLocationUsers"
          style="width: 100%; margin-top: 15px;"
          border
        >
          <el-table-column type="index" label="序号" width="60" />
          <el-table-column prop="name" label="姓名" width="120" />
          <el-table-column prop="departmentName" label="部门" width="150" />
          <el-table-column prop="position" label="职位" width="150" />
          <el-table-column label="操作" width="120">
            <template #default="scope">
              <el-button type="danger" size="small" @click="removeSelectedUser(scope.$index)">
                移除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="usersDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitUsersForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 添加使用人的选择对话框 -->
    <el-dialog
      v-model="selectUserDialogVisible"
      title="选择使用人"
      width="650px"
      append-to-body
    >
      <div class="select-user-dialog-content">
        <el-form inline>
          <el-form-item label="部门">
            <el-select v-model="userSearchDept" placeholder="选择部门" clearable>
              <el-option
                v-for="dept in departments"
                :key="dept.id"
                :label="dept.name"
                :value="dept.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="关键词">
            <el-input v-model="userSearchKeyword" placeholder="姓名/工号" clearable></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="searchUserCandidates">搜索</el-button>
          </el-form-item>
        </el-form>

        <el-table
          v-loading="userCandidatesLoading"
          :data="userCandidates"
          style="width: 100%"
          border
          @row-click="handleUserCandidateSelect"
          height="300px"
        >
          <el-table-column type="index" label="序号" width="60" />
          <el-table-column prop="name" label="姓名" width="120" />
          <el-table-column prop="employeeCode" label="工号" width="120" />
          <el-table-column prop="departmentName" label="部门" width="150" />
          <el-table-column prop="position" label="职位" width="150" />
        </el-table>
      </div>
    </el-dialog>

    <!-- 添加设置部门对话框组件 -->
    <set-department-dialog
      v-if="showSetDepartmentDialog"
      v-model:visible="showSetDepartmentDialog"
      :location-id="currentLocation?.id"
      :location-name="currentLocation?.name"
      :current-department-id="currentLocation?.defaultDepartmentId"
      @success="handleSetDepartmentSuccess"
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick, watch, onBeforeUnmount } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Search, Link, Remove, RefreshRight, View,
  LocationInformation, OfficeBuilding, UserFilled, User, Plus, ArrowDown
} from '@element-plus/icons-vue'
import locationApi from '@/api/location'
import assetApi from '@/api/asset'
import departmentApi from '@/api/department'
import userApi from '@/api/user'
import SetDepartmentDialog from './components/SetDepartmentDialog.vue'

// 表格高度自适应
const tableHeight = ref(450)

// 视图模式切换：flat=扁平列表视图，tree=树形层级视图
const viewMode = ref('tree')

// 位置数据
const locationData = ref([])
const selectedLocation = ref(null)
const currentLocationPath = ref('未选择')
const locationOptions = ref([]) // 位置下拉选项

// 添加当前选择的位置详情
const currentLocationDetail = ref(null)

// 树形视图相关数据
const locationTreeData = ref([])
const locationTreeProps = {
  children: 'children',
  label: 'name'
}
const selectedLocationForTree = ref(null)
const locationUsersForTree = ref([])
const locationAssetsForTree = ref([])
const treeAssetLoading = ref(false)
const treeSearchKeyword = ref('')
const locationTree = ref(null)
const activeDetailTab = ref('info')

// 部门数据
const departments = ref([])
const departmentUsers = ref([])
const selectedDepartment = ref(null)
const departmentFilter = ref('')

// 资产列表数据
const assetList = ref([])
const loading = ref(false)
const selectedAssets = ref([])
const assetTable = ref(null)
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 筛选条件
const assetTypeFilter = ref('')
const assetStatusFilter = ref('')
const keywordFilter = ref('')
const dateRangeFilter = ref([])
const typeFilter = ref('') // 添加位置类型筛选
const parentFilter = ref(null) // 添加上级位置筛选

// 关联资产对话框
const relateDialogVisible = ref(false)
const unrelatedAssetList = ref([])
const unrelatedLoading = ref(false)
const selectedUnrelatedAssets = ref([])
const assetSearchKeyword = ref('')
const unrelatedAssetTypeFilter = ref('')
const relationType = ref('primary')
const unrelatedPagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 部门关联对话框
const departmentDialogVisible = ref(false)
const departmentFormRef = ref(null)
const departmentFormData = reactive({
  departmentId: '',
  managerId: '',
  remark: ''
})

// 设置管理员对话框
const managerDialogVisible = ref(false)
const managerFormRef = ref(null)
const managerFormData = reactive({
  managerId: '',
  remark: ''
})
const managerCandidates = ref([])

// 设置使用人对话框
const usersDialogVisible = ref(false)
const locationUsersLoading = ref(false)
const selectedLocationUsers = ref([])
const replaceExistingUsers = ref(false)
const selectUserDialogVisible = ref(false)
const userSearchDept = ref(null)
const userSearchKeyword = ref('')
const userCandidates = ref([])
const userCandidatesLoading = ref(false)

// 资产类型和状态选项
const assetTypes = ref([])
const assetStatuses = [
  { label: '在用', value: 'in_use' },
  { label: '闲置', value: 'idle' },
  { label: '维修中', value: 'repairing' },
  { label: '借用', value: 'borrowed' },
  { label: '报废', value: 'scrapped' }
]

// 所有数据是否已加载完成
const dataLoaded = ref(false)

// 初始化数据加载
const fetchInitialData = async () => {
  console.log('开始初始化数据...');
  loading.value = true;
  
  try {
    // 先加载基础数据
    await Promise.all([
      fetchDepartments(),
      fetchLocationOptions()
    ]);
    
    // 根据当前视图模式加载相应数据
    if (viewMode.value === 'tree') {
      // 树形视图优先加载树结构
      await fetchLocationTreeData();
      // 不预加载扁平列表，等用户切换视图时再加载
    } else {
      // 扁平视图优先加载位置列表
      await fetchLocationList();
      // 不预加载树形数据，等用户切换视图时再加载
    }
    
    console.log('数据初始化完成');
  } catch (error) {
    console.error('初始化数据失败:', error);
    ElMessage.error('初始化数据失败，请刷新页面重试');
  } finally {
    loading.value = false;
  }
};

// 组件挂载完成后加载数据
onMounted(() => {
  console.log('位置关联页面加载');
  fetchInitialData();
});

// 修复树形视图显示方法
const handleChangeViewMode = (mode) => {
  console.log('切换视图模式:', mode);
  viewMode.value = mode;
  
  // 当切换到树形视图时，确保树形数据已加载
  if (mode === 'tree' && (!locationTreeData.value || locationTreeData.value.length === 0)) {
    fetchLocationTree();
  }
  
  // 当切换到扁平视图时，确保列表数据已加载
  if (mode === 'flat' && (!locationList.value || locationList.value.length === 0)) {
    fetchLocationList();
  }
};

// 获取树形数据的方法
const fetchLocationTree = async () => {
  try {
    console.log('获取位置树形结构');
    const res = await locationApi.getLocationTree();
    if (res.success) {
      locationTreeData.value = res.data || [];
      console.log('位置树形数据:', locationTreeData.value);
    } else {
      ElMessage.error(res.message || '获取位置树形结构失败');
    }
  } catch (error) {
    console.error('获取位置树形结构失败:', error);
    ElMessage.error('获取位置树形结构失败');
  }
};

// 在搜索框输入时过滤树节点
const filterLocationNode = (value, data) => {
  if (!value) return true;
  
  // 检查节点名称、代码或类型是否包含搜索关键词
  const searchValue = value.toLowerCase();
  const nameMatch = data.name && data.name.toLowerCase().includes(searchValue);
  const codeMatch = data.code && data.code.toLowerCase().includes(searchValue);
  const typeMatch = getLocationTypeName(data.type).toLowerCase().includes(searchValue);
  
  return nameMatch || codeMatch || typeMatch;
};

// 组件卸载前清理
onBeforeUnmount(() => {
  console.log('清理位置关联页面资源')
  // 清空数据
  selectedLocation.value = null
  locationOptions.value = []
  departments.value = []
  assetList.value = []
  
  // 移除可能的定时器或观察器
  if (window._locationRelationsTimer) {
    clearTimeout(window._locationRelationsTimer)
    window._locationRelationsTimer = null
  }
})

// 获取部门列表
const fetchDepartments = async () => {
  try {
    console.log('开始获取部门列表')
    const res = await departmentApi.getDepartmentList()
    if (res.success) {
      departments.value = res.data || []
      console.log(`加载了 ${departments.value.length} 个部门`)
    } else {
      departments.value = []
    }
  } catch (error) {
    console.error('获取部门列表失败:', error)
    departments.value = []
  }
}

// 获取部门用户列表
const fetchDepartmentUsers = async (departmentId) => {
  if (!departmentId) {
    departmentUsers.value = []
    return
  }
  try {
    const res = await departmentApi.getDepartmentUsers(departmentId)
    departmentUsers.value = res.data || []
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 监听部门变化
watch(() => departmentFormData.departmentId, (newVal) => {
  departmentFormData.managerId = ''
  fetchDepartmentUsers(newVal)
})

// 关联部门
const handleRelateDepartment = () => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  departmentDialogVisible.value = true
  departmentFormData.departmentId = ''
  departmentFormData.managerId = ''
  departmentFormData.remark = ''
}

// 提交部门关联表单
const submitDepartmentForm = async () => {
  if (!departmentFormData.departmentId) {
    ElMessage.warning('请选择部门')
    return
  }
  
  try {
    await locationApi.relateDepartment({
      locationId: selectedLocation.value,
      ...departmentFormData
    })
    
    ElMessage.success('关联部门成功')
    departmentDialogVisible.value = false
    
    // 更新部门信息
    const dept = departments.value.find(d => d.id === departmentFormData.departmentId)
    if (dept) {
      selectedDepartment.value = dept
    }
    
    // 刷新资产列表
    fetchAssetsForLocation()
  } catch (error) {
    console.error('关联部门失败:', error)
    ElMessage.error('关联部门失败')
  }
}

// 获取位置树数据 - 优化树形结构展示
const fetchLocationTreeData = async () => {
  try {
    console.log('正在加载位置树形数据...');
    loading.value = true;
    
    // 使用标准API获取位置树数据
    const treeRes = await locationApi.getLocationTree();
    
    if (treeRes && treeRes.success) {
      locationTreeData.value = treeRes.data || [];
      console.log('树形数据加载成功，节点数量:', countNodes(locationTreeData.value));
      
      // 默认展开第一级节点，不全部展开以提高性能
      nextTick(() => {
        if (locationTree.value && locationTreeData.value.length > 0) {
          locationTreeData.value.forEach(node => {
            if (locationTree.value.store && locationTree.value.store.nodesMap && locationTree.value.store.nodesMap[node.id]) {
              locationTree.value.store.nodesMap[node.id].expanded = true;
            }
          });
        }
      });
    } else {
      console.error('获取位置树形数据失败:', treeRes?.message);
      ElMessage.error('获取位置树形数据失败，请刷新重试');
      locationTreeData.value = [];
    }
  } catch (error) {
    console.error('加载位置树形数据出错:', error);
    ElMessage.error('加载位置树数据失败');
    locationTreeData.value = [];
  } finally {
    loading.value = false;
  }
}

// 计算树节点总数
const countNodes = (tree) => {
  if (!tree || !tree.length) return 0
  
  let count = tree.length
  for (const node of tree) {
    if (node.children && node.children.length) {
      count += countNodes(node.children)
    }
  }
  return count
}

// 搜索位置树
const searchLocationTree = () => {
  locationTree.value?.filter(treeSearchKeyword.value)
}

// 处理树节点点击事件
const handleLocationNodeClick = async (data, node) => {
  console.log('点击位置节点:', data.name, data);
  selectedLocationForTree.value = data;
  currentLocationDetail.value = data;
  currentLocationPath.value = data.fullPath || data.name;
  
  // 使用节流处理，防止频繁点击导致的性能问题
  if (window._locationClickTimer) {
    clearTimeout(window._locationClickTimer);
  }
  
  window._locationClickTimer = setTimeout(async () => {
    treeAssetLoading.value = true;
    
    try {
      // 只有当节点类型为工序(3)或工位(4)时才加载用户数据
      if (data.type === 3 || data.type === 4) {
        // 加载位置使用人
        await loadLocationUsers(data.id);
      } else {
        // 清空当前用户数据
        locationUsersForTree.value = [];
      }
      
      // 加载关联资产
      await fetchLocationAssets(data.id);
    } catch (error) {
      console.error('加载位置节点数据失败:', error);
    } finally {
      treeAssetLoading.value = false;
    }
  }, 300); // 300ms的延迟，防止频繁请求
}

// 在树视图中处理关联资产
const handleRelateAssetsForTree = (node = null) => {
  // 使用传入的节点或当前选中节点
  const targetNode = node || selectedLocationForTree.value;
  
  if (!targetNode || !targetNode.id) {
    ElMessage.warning('请选择位置');
    return;
  }
  
  selectedLocation.value = targetNode.id;
  currentLocationDetail.value = targetNode;
  handleRelateAssets();
}

// 在树视图中处理关联部门
const handleRelateDepartmentForTree = (node = null) => {
  // 使用传入的节点或当前选中节点
  const targetNode = node || selectedLocationForTree.value;
  
  if (!targetNode || !targetNode.id) {
    ElMessage.warning('请选择位置');
    return;
  }
  
  selectedLocation.value = targetNode.id;
  currentLocationDetail.value = targetNode;
  handleRelateDepartment();
}

// 在树视图中处理设置管理员
const handleSetManagerForTree = (node = null) => {
  // 使用传入的节点或当前选中节点
  const targetNode = node || selectedLocationForTree.value;
  
  if (!targetNode || !targetNode.id) {
    ElMessage.warning('请选择位置');
    return;
  }
  
  selectedLocation.value = targetNode.id;
  currentLocationDetail.value = targetNode;
  handleSetManager();
}

// 在树视图中处理设置使用人
const handleSetUsersForTree = (node = null) => {
  // 使用传入的节点或当前选中节点
  const targetNode = node || selectedLocationForTree.value;
  
  if (!targetNode || !targetNode.id) {
    ElMessage.warning('请选择位置');
    return;
  }
  
  selectedLocation.value = targetNode.id;
  currentLocationDetail.value = targetNode;
  handleSetUsers();
}

// 在树视图中处理解除资产关联
const handleUnrelateFromTree = (asset) => {
  if (!selectedLocationForTree.value || !selectedLocationForTree.value.id) {
    ElMessage.warning('请选择位置')
    return
  }
  
  ElMessageBox.confirm(`确认解除资产 ${asset.code || asset.assetCode} 与当前位置的关联关系吗?`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      treeAssetLoading.value = true
      // 调用API解除关联
      const result = await assetApi.changeAssetLocation(asset.id, {
        newLocationId: 0, // 使用0或null表示解除关联
        reason: '手动解除关联',
        notes: '从位置树视图解除'
      })
      
      if (result && result.success) {
    ElMessage.success('解除关联成功')
        // 重新加载资产列表
        await loadLocationAssets(selectedLocationForTree.value.id)
      } else {
        console.error('解除关联失败:', result)
        ElMessage.error(result?.message || '解除关联失败')
      }
  } catch (error) {
      console.error('解除关联异常:', error)
      ElMessage.error('解除关联失败，请检查网络连接')
    } finally {
      treeAssetLoading.value = false
    }
  }).catch(() => {
    // 用户取消操作
  })
}

// 获取位置类型文本
const getLocationTypeName = (type) => {
  const typeMap = {
    0: "工厂",
    1: "车间", 
    2: "产线", 
    3: "工序", 
    4: "工位"
  }
  return typeMap[type] || '未知'
}

// 获取位置类型标签样式
const getLocationTypeTag = (type) => {
  const typeTagMap = {
    0: "danger",    // 工厂
    1: "warning",   // 车间
    2: "primary",   // 产线 
    3: "success",   // 工序
    4: "info"       // 工位
  }
  return typeTagMap[type] || ''
}

// 监听视图模式变化
watch(viewMode, (newMode) => {
  if (newMode === 'tree' && locationTreeData.value.length === 0) {
    fetchLocationTreeData()
  } else if (newMode === 'flat') {
    // 重置树视图相关数据
    selectedLocationForTree.value = null
    locationUsersForTree.value = []
    locationAssetsForTree.value = []
    
    // 加载扁平位置列表数据
    fetchLocationList()
  }
})

// 扁平列表视图 - 获取位置列表
const fetchLocationList = async () => {
  loading.value = true;
  try {
    // 使用与后端匹配的参数格式
    const params = {
      pageIndex: pagination.currentPage,
      pageSize: pagination.pageSize
    };
    
    // 添加可选筛选条件
    if (typeFilter.value) {
      params.type = typeFilter.value;
    }
    
    if (parentFilter.value) {
      params.parentId = parentFilter.value;
    }
    
    if (keywordFilter.value) {
      params.keyword = keywordFilter.value;
    }
    
    console.log('获取位置列表 - API调用', params);
    
    // 使用GET方法调用接口
    const res = await locationApi.searchLocations(params);
    
    if (res && res.success) {
      locationList.value = res.data?.items || [];
      pagination.total = res.data?.total || 0;
      console.log(`获取到位置列表: ${locationList.value.length}条记录`);
      
      // 获取工序和工位的用户信息
      await fetchLocationUsersInfo();
    } else {
      console.error('获取位置列表失败:', res?.message);
      ElMessage.error(res?.message || '获取位置列表失败');
      locationList.value = [];
      pagination.total = 0;
    }
  } catch (error) {
    console.error('获取位置列表失败:', error);
    ElMessage.error('获取位置列表失败，请检查网络连接');
    locationList.value = [];
    pagination.total = 0;
  } finally {
    loading.value = false;
  }
}

// 获取位置用户信息 - 单独抽取方法
const fetchLocationUsersInfo = async () => {
  // 过滤工序和工位位置
  const locationIds = locationList.value
    .filter(location => location.type === 3 || location.type === 4)
    .map(location => location.id);
  
  if (!locationIds.length) return;
  
  try {
    // 尝试批量获取用户数据
    const batchRes = await locationApi.getLocationUsersBatch(locationIds).catch(() => null);
    
    if (batchRes && batchRes.success && Array.isArray(batchRes.data)) {
      // 将用户数据分配到相应位置
      locationList.value.forEach(location => {
        const userData = batchRes.data.find(d => d.locationId === location.id);
        location.userCount = userData?.users?.length || 0;
      });
      return;
    }
    
    // 如果批量API不可用，回退到单独请求
    console.warn('批量获取用户数据失败，使用逐个请求');
    await Promise.all(
      locationIds.map(async locationId => {
        try {
          const location = locationList.value.find(loc => loc.id === locationId);
          if (!location) return;
          
          const usersRes = await locationApi.getLocationUsers(locationId);
          location.userCount = usersRes?.success ? (usersRes.data?.length || 0) : 0;
        } catch (err) {
          console.warn(`获取位置${locationId}用户数据失败:`, err);
        }
      })
    );
  } catch (error) {
    console.error('获取位置用户信息失败:', error);
  }
}

// 扁平列表视图 - 重置筛选条件
const resetLocationFilters = () => {
  typeFilter.value = ''
  parentFilter.value = null
  keywordFilter.value = ''
    pagination.currentPage = 1
  fetchLocationList()
}

// 加载上级位置选项
const loadParentOptions = async () => {
  try {
    const res = await locationApi.getLocationsForDropdown({ includeAll: true })
    if (res.success) {
      parentOptions.value = res.data || []
      console.log(`加载了 ${parentOptions.value.length} 个上级位置选项`)
    }
  } catch (error) {
    console.error('加载上级位置选项失败:', error)
  }
}

// 处理位置点击
const handleLocationClick = (row) => {
  selectedLocation.value = row.id
  currentLocationDetail.value = row
  currentLocationPath.value = row.fullPath || row.name
  
  // 加载位置使用人
  if (row.type === 3 || row.type === 4) {
    loadLocationUsers(row.id)
  }
  
  // 加载关联资产
    fetchAssetsForLocation()
}

// 查看位置使用人
const viewLocationUsers = async (row) => {
  try {
    currentLocationDetail.value = row
    usersDialogVisible.value = true
    locationUsersLoading.value = true
    
    const res = await locationApi.getLocationUsers(row.id)
    if (res.success) {
      locationUsers.value = res.data || []
  } else {
      locationUsers.value = []
      ElMessage.error(res.message || '获取使用人列表失败')
    }
  } catch (error) {
    console.error('获取使用人列表失败:', error)
    locationUsers.value = []
    ElMessage.error('获取使用人列表失败')
  } finally {
    locationUsersLoading.value = false
  }
}

// 多功能命令处理
const handleCommand = (command, row) => {
  switch (command) {
    case 'setDepartment':
      handleRelateDepartment(row)
      break
    case 'setManager':
      handleSetManager(row)
      break
    case 'setUsers':
      handleSetUsers(row)
      break
  }
}

// 分页处理
const handleSizeChange = (size) => {
  pagination.pageSize = size
  if (viewMode.value === 'flat') {
    fetchLocationList()
  } else {
    fetchAssetsForLocation()
  }
}

const handleCurrentChange = (page) => {
  pagination.currentPage = page
  if (viewMode.value === 'flat') {
    fetchLocationList()
  } else {
    fetchAssetsForLocation()
  }
}

// 获取资产列表 - 优化版
const fetchAssetsForLocation = async () => {
  if (!selectedLocation.value && !selectedLocationForTree.value) {
    assetList.value = [];
    pagination.total = 0;
    return;
  }
  
  loading.value = true;
  try {
    // 确定当前选中位置ID
    const locationId = selectedLocation.value || 
                      (selectedLocationForTree.value ? selectedLocationForTree.value.id : null);
    
    if (!locationId) {
      assetList.value = [];
      pagination.total = 0;
      return;
    }
    
    // 构建查询参数，确保与后端API匹配
    const params = {
      pageIndex: pagination.currentPage,
      pageSize: pagination.pageSize,
      locationId: locationId,
      keyword: keywordFilter.value || '',
      assetType: assetTypeFilter.value || null,
      status: assetStatusFilter.value || null,
      startDate: dateRangeFilter.value && dateRangeFilter.value[0] ? dateRangeFilter.value[0] : null,
      endDate: dateRangeFilter.value && dateRangeFilter.value[1] ? dateRangeFilter.value[1] : null,
      departmentId: departmentFilter.value || null
    };
    
    console.log(`获取位置(ID:${locationId})关联资产`);
    
    const res = await locationApi.getLocationAssets(params);
    
    if (res && res.success) {
      assetList.value = res.data?.items || [];
      pagination.total = res.data?.total || 0;
      console.log(`获取到${assetList.value.length}个资产，总数:${pagination.total}`);
    } else {
      console.error('获取资产列表失败:', res?.message);
      assetList.value = [];
      pagination.total = 0;
    }
  } catch (error) {
    console.error('获取资产列表失败:', error);
    ElMessage.error('获取资产列表失败');
    assetList.value = [];
    pagination.total = 0;
  } finally {
    loading.value = false;
  }
}

// 位置选择变更
const handleLocationChange = async (locationId) => {
  console.log('位置选择变更:', locationId)
  
  // 清除当前位置详情
  currentLocationDetail.value = null
  
  if (!locationId) {
    selectedLocation.value = null
    currentLocationPath.value = '未选择'
    resetFilters(false) // 不清空位置选择
    return
  }
  
  selectedLocation.value = locationId
  
  // 获取位置详情
  try {
    loading.value = true
    const res = await locationApi.getLocationDetail(locationId)
    if (res.success) {
      currentLocationDetail.value = res.data
      console.log('获取到位置详情:', res.data)
      
      // 设置当前位置路径
      currentLocationPath.value = res.data.path ? res.data.path.replace(/,/g, ' > ') : res.data.name
      
      // 如果位置有关联部门，更新部门筛选
      if (res.data.defaultDepartmentId) {
        departmentFilter.value = res.data.defaultDepartmentId
        
        // 更新选中的部门信息
        const dept = departments.value.find(d => d.id === res.data.defaultDepartmentId)
        if (dept) {
          selectedDepartment.value = dept
          console.log('位置关联的默认部门:', dept.name)
        } else {
          // 如果找不到部门信息，可能需要重新加载部门列表
          await fetchDepartments()
          const reloadedDept = departments.value.find(d => d.id === res.data.defaultDepartmentId)
          if (reloadedDept) {
            selectedDepartment.value = reloadedDept
            console.log('重新加载后找到部门:', reloadedDept.name)
          } else {
            console.warn('找不到与位置关联的部门信息:', res.data.defaultDepartmentId)
            selectedDepartment.value = null
          }
        }
      } else {
        departmentFilter.value = null
        selectedDepartment.value = null
      }
    }
  } catch (error) {
    console.error('获取位置详情失败:', error)
    ElMessage.error('获取位置详情失败')
  } finally {
    loading.value = false
  }
  
  // 清空其他筛选条件
  assetStatusFilter.value = null
  keywordFilter.value = ''
  dateRangeFilter.value = null
  
  // 获取该位置的资产列表
  fetchAssetsForLocation()
}

// 重置筛选条件
const resetFilters = (includeLocation = true) => {
  if (includeLocation) {
    selectedLocation.value = null
  }
  departmentFilter.value = null
  assetStatusFilter.value = null
  keywordFilter.value = ''
  dateRangeFilter.value = null
  // 重置后重新获取数据
  fetchAssetsForLocation()
}

// 关联资产按钮点击处理
const handleRelateAssets = () => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  relateDialogVisible.value = true
  // 重置搜索表单
  assetSearchKeyword.value = ''
  unrelatedAssetTypeFilter.value = null
  relationType.value = 'primary'
  selectedUnrelatedAssets.value = []
  unrelatedPagination.currentPage = 1
  // 获取未关联的资产列表
  searchUnrelatedAssets()
}

// 多选变更事件
const handleSelectionChange = (selection) => {
  selectedAssets.value = selection
}

// 单个解除关联
const handleUnrelate = (asset) => {
  ElMessageBox.confirm(`确认解除资产 ${asset.code || asset.assetCode} 与当前位置的关联关系吗?`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      loading.value = true
      // 调用API解除关联
      const result = await assetApi.changeAssetLocation(asset.id, {
        newLocationId: 0, // 使用0或null表示解除关联
        reason: '手动解除关联',
        notes: '从位置关联管理页面解除'
      })
      
      if (result && result.success) {
        ElMessage.success('解除关联成功')
        // 重新获取位置下的资产列表
        fetchAssetsForLocation()
      } else {
        console.error('解除关联失败:', result)
        ElMessage.error(result?.message || '解除关联失败')
      }
    } catch (error) {
      console.error('解除关联异常:', error)
      ElMessage.error('解除关联失败，请检查网络连接')
    } finally {
      loading.value = false
    }
  }).catch(() => {
    // 用户取消操作
  })
}

// 批量解除关联
const handleBatchUnrelate = () => {
  if (selectedAssets.value.length === 0) {
    ElMessage.warning('请选择要解除关联的资产')
    return
  }
  
  const assetNames = selectedAssets.value.map(asset => asset.name || asset.assetCode).join(', ')
  
  ElMessageBox.confirm(`确认解除资产 [${assetNames}] 与当前位置的关联关系吗?`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      loading.value = true
      
      // 创建批量解除关联的请求
      const promises = selectedAssets.value.map(asset => 
        assetApi.changeAssetLocation(asset.id, {
          newLocationId: 0, // 使用0或null表示解除关联
          reason: '批量解除关联',
          notes: '从位置关联管理页面批量解除'
        })
      )
      
      // 并行处理所有请求
      const results = await Promise.allSettled(promises)
      
      // 检查结果
      const succeeded = results.filter(r => r.status === 'fulfilled' && r.value?.success).length
      const failed = results.length - succeeded
      
      if (failed === 0) {
        ElMessage.success(`成功解除 ${succeeded} 个资产的关联`)
      } else if (succeeded === 0) {
        ElMessage.error(`解除关联失败，请重试`)
      } else {
        ElMessage.warning(`部分资产解除关联成功，${succeeded} 成功，${failed} 失败`)
      }
      
      // 重新获取数据
      fetchAssetsForLocation()
    } catch (error) {
      console.error('批量解除关联异常:', error)
      ElMessage.error('批量解除关联失败，请检查网络连接')
    } finally {
      loading.value = false
      // 清空选择
      selectedAssets.value = []
    }
  }).catch(() => {
    // 用户取消操作
  })
}

// 获取未关联资产列表
const fetchUnrelatedAssets = async () => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  unrelatedLoading.value = true
  
  try {
  // 构建查询参数
  const params = {
    locationId: selectedLocation.value,
      unrelated: true,
      pageIndex: unrelatedPagination.currentPage,
    pageSize: unrelatedPagination.pageSize,
    keyword: assetSearchKeyword.value,
      assetTypeId: unrelatedAssetTypeFilter.value
    }
    
    // 调用资产API获取未关联的资产列表
    const res = await assetApi.getAssetList(params)
    
    if (res.success) {
      // 将API返回的资产数据格式化为组件需要的格式
      unrelatedAssetList.value = (res.data?.items || []).map(asset => ({
        id: asset.id,
        code: asset.assetCode,
        name: asset.name,
        type: asset.assetTypeId,
        typeName: asset.assetTypeName,
        sn: asset.serialNumber || '-',
        status: asset.status || 0,
        statusText: getStatusName(asset.status || 0)
      }))
      unrelatedPagination.total = res.data?.total || 0
    } else {
      unrelatedAssetList.value = []
      unrelatedPagination.total = 0
      ElMessage.error(res.message || '获取未关联资产失败')
    }
  } catch (error) {
    console.error('获取未关联资产出错:', error)
    ElMessage.error('获取未关联资产列表失败')
    unrelatedAssetList.value = []
    unrelatedPagination.total = 0
  } finally {
    unrelatedLoading.value = false
  }
}

// 搜索未关联资产
const searchUnrelatedAssets = async () => {
  await fetchUnrelatedAssets()
}

// 未关联资产多选变更事件
const handleUnrelatedSelectionChange = (selection) => {
  selectedUnrelatedAssets.value = selection
}

// 确认关联资产
const confirmRelateAssets = async () => {
  if (selectedUnrelatedAssets.value.length === 0) {
    ElMessage.warning('请选择要关联的资产')
    return
  }
  
  if (!relationType.value) {
    ElMessage.warning('请选择关联类型')
    return
  }
  
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  // 显示确认对话框
  try {
    await ElMessageBox.confirm(
      `确认将选中的 ${selectedUnrelatedAssets.value.length} 个资产关联到 "${currentLocationPath.value}" 吗？`,
      '确认关联',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'info'
      }
    )
    
    // 用户确认，执行关联操作
    // 显示加载状态
    unrelatedLoading.value = true
    
    // 准备关联数据
    const assetIds = selectedUnrelatedAssets.value.map(asset => asset.id)
    
    console.log('关联资产数据:', {
      位置ID: selectedLocation.value,
      资产IDs: assetIds,
      关联类型: relationType.value
    })
    
    // 调用API关联资产到位置
    try {
      const res = await locationApi.relateAssets(selectedLocation.value, {
        assetIds: assetIds,
        relationType: relationType.value,
        notes: '从位置关联管理页面关联'
      })
      
      if (res.success) {
        ElMessage.success(`成功关联 ${assetIds.length} 个资产到 ${currentLocationPath.value}`)
  
  // 关闭对话框
  relateDialogVisible.value = false
        
        // 清空选择
        selectedUnrelatedAssets.value = []
  
  // 刷新资产列表
        fetchAssetsForLocation()
      } else {
        ElMessage.error(res.message || '关联资产失败')
      }
    } catch (error) {
      console.error('关联资产失败:', error)
      ElMessage.error('关联资产失败: ' + (error.message || '未知错误'))
    } finally {
      unrelatedLoading.value = false
    }
  } catch (e) {
    // 用户取消操作
    console.log('用户取消关联操作')
  }
}

// 获取状态名称辅助函数
const getStatusName = (status) => {
  const statusMap = {
    0: '未知',
    1: '在用',
    2: '闲置',
    3: '维修中',
    4: '借用',
    5: '报废'
  }
  return statusMap[status] || '未知'
}

// 工具方法：获取状态标签类型
const getStatusType = (status) => {
  const map = {
    'in_use': 'success',
    'idle': 'info',
    'repairing': 'warning',
    'borrowed': 'primary',
    'scrapped': 'danger'
  }
  return map[status] || 'info'
}

// 工具方法：获取资产类型标签
const getAssetTypeTag = (type) => {
  const map = {
    'laptop': '',
    'desktop': 'success',
    'server': 'danger',
    'monitor': 'warning',
    'printer': 'info',
    'network': 'primary',
    'other': 'info'
  }
  return map[type] || 'info'
}

// 工具方法：获取关联类型标签
const getRelationTag = (type) => {
  const map = {
    'primary': 'success',
    'secondary': 'info',
    'temporary': 'warning'
  }
  return map[type] || 'info'
}

// 工具方法：获取关联类型文本
const getRelationTypeText = (type) => {
  const map = {
    'primary': '主要位置',
    'secondary': '次要位置',
    'temporary': '临时位置'
  }
  return map[type] || '未知'
}

// 查看资产详情
const handleAssetDetail = (asset) => {
  ElMessage.info(`查看资产 ${asset.name} (${asset.code}) 的详情`)
  // 实际项目中可以跳转到资产详情页: router.push(`/assets/detail/${asset.id}`)
}

// 添加使用人
const handleAddUser = () => {
  // 重置搜索条件
  userSearchDept.value = null
  userSearchKeyword.value = ''
  userCandidates.value = []
  
  // 搜索用户候选人
  searchUserCandidates()
  
  // 显示选择对话框
  selectUserDialogVisible.value = true
}

// 搜索用户候选人
const searchUserCandidates = async () => {
  userCandidatesLoading.value = true
  
  try {
    // 调用API搜索用户
    const res = await userApi.getUsersByDepartment({
      departmentId: userSearchDept.value || null,
      keyword: userSearchKeyword.value || '',
      userType: 0 // 0表示普通用户/使用人
    })
    
    if (res.success) {
      // 过滤掉已经选择的用户
      const selectedIds = selectedLocationUsers.value.map(u => u.id)
      userCandidates.value = (res.data || []).filter(u => !selectedIds.includes(u.id))
    } else {
      ElMessage.error('搜索用户失败: ' + (res.message || '未知错误'))
      userCandidates.value = []
    }
  } catch (error) {
    console.error('搜索用户出错:', error)
    ElMessage.error('搜索用户失败')
    userCandidates.value = []
  } finally {
    userCandidatesLoading.value = false
  }
}

// 选择用户候选人
const handleUserCandidateSelect = (row) => {
  // 添加到已选列表
  selectedLocationUsers.value.push({
    id: row.id,
    name: row.name,
    departmentName: row.departmentName,
    position: row.position
  })
  
  // 关闭选择对话框
  selectUserDialogVisible.value = false
}

// 移除已选用户
const removeSelectedUser = (index) => {
  selectedLocationUsers.value.splice(index, 1)
}

// 更新设置管理员方法，使其支持工序和工位
const handleSetManager = async (row = null) => {
  // 确定要操作的位置，可以是传入的行或当前选择的位置
  const targetLocation = row || currentLocationDetail.value;
  
  if (!targetLocation || (targetLocation.type !== 3 && targetLocation.type !== 4)) {
    ElMessage.warning('只能为工序或工位类型的位置设置管理员');
    return;
  }

  // 更新当前选择的位置
  if (row) {
    currentLocationDetail.value = row;
    selectedLocation.value = row.id;
  }

  // 重置表单
  managerFormData.managerId = '';
  managerFormData.remark = '';
  
  try {
    // 加载管理员候选人
    const res = await userApi.getUsersByRole({ userType: 1 }); // 1表示管理员类型
    if (res.success) {
      managerCandidates.value = res.data || [];
      
      // 如果当前位置已有管理员，则默认选中
      if (targetLocation.managerId) {
        managerFormData.managerId = targetLocation.managerId;
      }
      
      // 显示对话框
      managerDialogVisible.value = true;
    } else {
      ElMessage.error('获取管理员候选人失败: ' + (res.message || '未知错误'));
    }
  } catch (error) {
    console.error('获取管理员候选人出错:', error);
    ElMessage.error('获取管理员候选人失败');
  }
}

// 更新设置使用人方法，使其支持工序和工位
const handleSetUsers = async (row = null) => {
  // 确定要操作的位置，可以是传入的行或当前选择的位置
  const targetLocation = row || currentLocationDetail.value;
  
  if (!targetLocation || (targetLocation.type !== 3 && targetLocation.type !== 4)) {
    ElMessage.warning('只能为工序或工位类型的位置设置使用人');
    return;
  }
  
  // 更新当前选择的位置
  if (row) {
    currentLocationDetail.value = row;
    selectedLocation.value = row.id;
  }
  
  // 重置数据
  selectedLocationUsers.value = [];
  replaceExistingUsers.value = false;
  locationUsersLoading.value = true;
  
  try {
    // 获取当前位置的使用人
    const res = await locationApi.getLocationUsers(targetLocation.id);
    if (res.success) {
      // 筛选出使用人(类型为0)
      selectedLocationUsers.value = (res.data || [])
        .filter(user => user.userType === 0)
        .map(user => ({
          id: user.personnelId,
          name: user.name,
          departmentName: user.departmentName,
          position: user.position,
          remarks: user.remarks
        }));
      
      // 显示对话框
      usersDialogVisible.value = true;
    } else {
      ElMessage.error('获取位置使用人失败: ' + (res.message || '未知错误'));
    }
  } catch (error) {
    console.error('获取位置使用人出错:', error);
    ElMessage.error('获取位置使用人失败');
  } finally {
    locationUsersLoading.value = false;
  }
}

// 成功设置后处理方法
const afterSetManager = async () => {
  // 重新加载位置详情
  if (viewMode.value === 'flat') {
    if (selectedLocation.value) {
      await handleLocationChange(selectedLocation.value);
    } else {
      // 刷新扁平列表
      await fetchLocationList();
    }
  } else {
    if (selectedLocationForTree.value?.id) {
      await loadLocationDetails(selectedLocationForTree.value.id);
    }
  }
}

// 修改提交管理员设置方法
const submitManagerForm = async () => {
  if (!managerFormData.managerId) {
    ElMessage.warning('请选择管理员');
    return;
  }
  
  try {
    // 确保当前选择位置可用
    const locationId = currentLocationDetail.value?.id;
    
    if (!locationId) {
      ElMessage.warning('请选择位置');
      return;
    }
    
    // 调用API设置管理员
    const res = await locationApi.updateLocationUsers(locationId, {
      replaceExisting: true,
      users: [
        {
          personnelId: managerFormData.managerId,
          userType: 1, // 1表示管理员类型
          remarks: managerFormData.remark || ''
        }
      ]
    });
    
    if (res.success) {
      ElMessage.success('设置管理员成功');
      managerDialogVisible.value = false;
      
      // 重新加载位置详情
      await afterSetManager();
    } else {
      ElMessage.error('设置管理员失败: ' + (res.message || '未知错误'));
    }
  } catch (error) {
    console.error('设置管理员出错:', error);
    ElMessage.error('设置管理员失败');
  }
}

// 修改提交使用人设置方法
const submitUsersForm = async () => {
  if (selectedLocationUsers.value.length === 0) {
    ElMessage.warning('请至少添加一个使用人');
    return;
  }
  
  try {
    // 确保当前选择位置可用
    const locationId = currentLocationDetail.value?.id;
    
    if (!locationId) {
      ElMessage.warning('请选择位置');
      return;
    }
    
    // 准备请求数据
    const requestData = {
      replaceExisting: replaceExistingUsers.value,
      users: selectedLocationUsers.value.map(user => ({
        personnelId: user.id,
        userType: 0, // 0表示普通用户/使用人
        remarks: user.remarks || ''
      }))
    };
    
    // 调用API设置使用人
    const res = await locationApi.updateLocationUsers(locationId, requestData);
    
    if (res.success) {
      ElMessage.success('设置使用人成功');
      usersDialogVisible.value = false;
      
      // 重新加载位置详情
      await afterSetManager();
    } else {
      ElMessage.error('设置使用人失败: ' + (res.message || '未知错误'));
    }
  } catch (error) {
    console.error('设置使用人出错:', error);
    ElMessage.error('设置使用人失败');
  }
}

// 新增树节点命令处理函数
const handleTreeNodeCommand = (command, node) => {
  console.log('树节点操作:', command, node);
  
  switch (command) {
    case 'relate':
      handleRelateAssetsForTree(node);
      break;
    case 'department':
      handleRelateDepartmentForTree(node);
      break;
    case 'manager':
      handleSetManagerForTree(node);
      break;
    case 'users':
      handleSetUsersForTree(node);
      break;
  }
}

// 添加缺失的fetchLocationOptions函数
/**
 * 获取位置下拉选项
 */
const fetchLocationOptions = async () => {
  try {
    const res = await locationApi.getLocationsForDropdown({ includeAll: true });
    if (res.success) {
      locationOptions.value = res.data || [];
    } else {
      console.error('获取位置下拉选项失败:', res.message);
      ElMessage.error('获取位置下拉选项失败');
    }
  } catch (error) {
    console.error('获取位置下拉选项出错:', error);
    ElMessage.error('获取位置下拉选项失败');
  }
};

// 加载位置使用人
const loadLocationUsers = async (locationId) => {
  try {
    const usersRes = await locationApi.getLocationUsers(locationId)
    if (usersRes.success) {
      locationUsersForTree.value = usersRes.data || []
      console.log('加载位置使用人成功:', locationUsersForTree.value.length)
    } else {
      locationUsersForTree.value = []
      console.warn('获取位置使用人返回错误:', usersRes.message)
    }
  } catch (error) {
    console.error('获取位置使用人失败:', error)
    locationUsersForTree.value = []
  }
}

// 获取位置关联的资产
const fetchLocationAssets = async (locationId) => {
  treeAssetLoading.value = true
  try {
    const params = {
      locationId: locationId,
      pageSize: 100,
      pageIndex: 1
    }
    
    console.log('获取位置关联的资产:', locationId)
    const assetsRes = await locationApi.getLocationAssets(params)
    if (assetsRes.success) {
      locationAssetsForTree.value = assetsRes.data.items || []
      console.log('加载位置资产成功:', locationAssetsForTree.value.length)
    } else {
      locationAssetsForTree.value = []
      console.warn('获取位置资产返回错误:', assetsRes.message)
    }
  } catch (error) {
    console.error('获取位置资产失败:', error)
    locationAssetsForTree.value = []
  } finally {
    treeAssetLoading.value = false
  }
}

// 获取资产状态显示样式
const getAssetStatusType = (status) => {
  const statusTypeMap = {
    0: 'info',     // 闲置
    1: 'success',  // 在用
    2: 'warning',  // 维修中
    3: 'danger'    // 报废
  }
  return statusTypeMap[status] || 'info'
}

// 获取资产状态文本
const getAssetStatusText = (status) => {
  const statusTextMap = {
    0: '闲置',
    1: '在用',
    2: '维修中',
    3: '报废'
  }
  return statusTextMap[status] || '未知'
}

// 查看资产详情
const viewAssetDetail = (asset) => {
  console.log('查看资产详情:', asset)
  // 可以根据实际情况实现查看详情的功能
  ElMessageBox.alert(`资产 ${asset.name} 的详细信息`, '资产详情', {
    confirmButtonText: '确定'
  })
}

// 解除资产关联
const handleUnrelateAsset = (asset) => {
  console.log('解除资产关联:', asset)
  ElMessageBox.confirm(
    `确定要解除资产 ${asset.name} 与当前位置的关联吗？`,
    '解除关联',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  )
  .then(async () => {
    try {
      const res = await locationApi.unrelateAssets(
        selectedLocationForTree.value.id, 
        [asset.id]
      )
      if (res.success) {
        ElMessage.success('解除关联成功')
        // 刷新资产列表
        fetchLocationAssets(selectedLocationForTree.value.id)
      } else {
        ElMessage.error(res.message || '解除关联失败')
      }
    } catch (error) {
      console.error('解除关联失败:', error)
      ElMessage.error('解除关联失败')
    }
  })
  .catch(() => {
    // 用户取消操作
  })
}

// 状态变量
const state = reactive({
  showSetDepartmentDialog: false,
})

/**
 * 处理设置部门成功
 */
const handleSetDepartmentSuccess = async () => {
  // 刷新当前位置数据
  await fetchLocationDetail(state.currentLocation.id)
  // 刷新位置树
  await fetchLocationTree()
  ElMessage.success('位置部门设置成功')
}

// 设置当前操作的位置
const currentLocation = computed(() => ({
  id: selectedLocation.value,
  name: selectedLocation.value?.name,
  defaultDepartmentId: selectedLocation.value?.defaultDepartmentId
}))

// 设置使用部门
const handleSetDepartment = (departmentId) => {
  departmentFormData.departmentId = departmentId
  departmentFormData.managerId = ''
  departmentFormData.remark = ''
  state.showSetDepartmentDialog = true
}

// 提交设置部门表单
const submitSetDepartmentForm = async () => {
  if (!departmentFormData.departmentId) {
    ElMessage.warning('请选择部门')
    return
  }
  
  try {
    await locationApi.relateDepartment({
      locationId: selectedLocation.value,
      ...departmentFormData
    })
    
    ElMessage.success('设置部门成功')
    state.showSetDepartmentDialog = false
    
    // 更新部门信息
    const dept = departments.value.find(d => d.id === departmentFormData.departmentId)
    if (dept) {
      selectedDepartment.value = dept
    }
    
    // 刷新资产列表
    fetchAssetsForLocation()
  } catch (error) {
    console.error('设置部门失败:', error)
    ElMessage.error('设置部门失败')
  }
}

// 设置部门用户
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门负责人
const setDepartmentManager = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentManager(departmentId)
    if (res.success) {
      departmentFormData.managerId = res.data.id
      departmentFormData.remark = res.data.remark
    } else {
      ElMessage.error('获取部门负责人失败')
    }
  } catch (error) {
    console.error('获取部门负责人失败:', error)
    ElMessage.error('获取部门负责人失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  try {
    const res = await locationApi.getDepartmentUsers(departmentId)
    if (res.success) {
      departmentUsers.value = res.data || []
    } else {
      ElMessage.error('获取部门用户列表失败')
    }
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 设置部门使用人
const setDepartmentUsers = async (departmentId) => {
  if (!selectedLocation.value) {
/**
 * 航空航天级IT资产管理系统 - 位置关联页面
 * 文件路径: src/views/locations/relations.vue
 * 功能描述: 管理资产与位置的关联关系
 */

<template>
  <div class="location-relations-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2 class="page-title">位置关联</h2>
      <div class="page-actions">
        <el-button type="primary" @click="handleRelateAssets" :icon="Link">关联资产</el-button>
        <el-button type="success" @click="handleRelateDepartment" :icon="OfficeBuilding">关联部门</el-button>
        <el-button type="danger" @click="handleBatchUnrelate" :icon="Remove" :disabled="selectedAssets.length === 0">
          解除关联
        </el-button>
        <!-- 添加工位管理人员和使用人按钮，适用于工序(type=3)和工位(type=4)时显示 -->
        <el-button 
          v-if="currentLocationDetail && (currentLocationDetail.type === 3 || currentLocationDetail.type === 4)" 
          type="warning" 
          @click="handleSetManager" 
          :icon="UserFilled">
          设置管理员
        </el-button>
        <el-button 
          v-if="currentLocationDetail && (currentLocationDetail.type === 3 || currentLocationDetail.type === 4)" 
          type="info" 
          @click="handleSetUsers" 
          :icon="User">
          设置使用人
        </el-button>
      </div>
    </div>
    
    <!-- 主体内容 -->
    <div class="relation-content">
      <!-- 筛选区域 -->
      <el-card class="filter-card">
        <div class="filters">
          <el-row :gutter="16">
            <el-col :span="24">
              <div class="location-view-switch">
                <el-radio-group v-model="viewMode" size="small">
                  <el-radio-button value="tree">树形层级视图</el-radio-button>
                  <el-radio-button value="flat">扁平列表视图</el-radio-button>
                </el-radio-group>
              </div>
            </el-col>
          </el-row>
          
          <el-row :gutter="16" v-if="viewMode === 'flat'">
            <el-col :span="8">
              <el-form-item label="位置选择">
                <el-select
                  v-model="selectedLocation"
                  filterable
                  placeholder="请选择位置"
                  style="width: 100%"
                  @change="handleLocationChange"
                >
                  <el-option
                    v-for="location in locationOptions"
                    :key="location.id"
                    :label="location.fullName"
                    :value="location.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="部门">
                <el-select
                  v-model="departmentFilter"
                  placeholder="全部部门"
                  clearable
                  style="width: 100%"
                  @change="fetchAssetsForLocation"
                >
                  <el-option 
                    v-for="dept in departments" 
                    :key="dept.id" 
                    :label="dept.name" 
                    :value="dept.id" 
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="资产状态">
                <el-select
                  v-model="assetStatusFilter"
                  placeholder="全部状态"
                  clearable
                  style="width: 100%"
                  @change="fetchAssetsForLocation"
                >
                  <el-option 
                    v-for="status in assetStatuses" 
                    :key="status.value" 
                    :label="status.label" 
                    :value="status.value" 
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="16" v-if="viewMode === 'flat'">
            <el-col :span="8">
              <el-form-item label="关键字">
                <el-input
                  v-model="keywordFilter"
                  placeholder="搜索资产名称/编号/SN"
                  clearable
                  @keyup.enter="fetchAssetsForLocation"
                >
                  <template #append>
                    <el-button :icon="Search" @click="fetchAssetsForLocation" />
                  </template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="最后更新时间">
                <el-date-picker
                  v-model="dateRangeFilter"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  value-format="YYYY-MM-DD"
                  @change="fetchAssetsForLocation"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8" class="text-right">
              <el-button type="primary" @click="fetchAssetsForLocation" :icon="Search">搜索</el-button>
              <el-button @click="resetFilters" :icon="RefreshRight">重置</el-button>
            </el-col>
          </el-row>
          
          <el-row :gutter="16" v-if="viewMode === 'tree'">
            <el-col :span="24">
              <el-form-item label="位置层级查询">
                <el-input
                  v-model="treeSearchKeyword"
                  placeholder="搜索位置名称"
                  clearable
                  @keyup.enter="searchLocationTree"
                >
                  <template #append>
                    <el-button :icon="Search" @click="searchLocationTree" />
                  </template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-card>

      <!-- 数据表格/树形结构 -->
      <el-card class="data-card">
        <template #header>
          <div class="card-header">
            <div class="header-info">
              <span v-if="viewMode === 'flat'">位置列表</span>
              <span v-else>位置层级结构</span>
              <el-tag v-if="selectedDepartment && viewMode === 'flat'" type="success" class="department-tag">
                {{ selectedDepartment.name }}
              </el-tag>
            </div>
            <span class="location-path" v-if="viewMode === 'flat'">当前位置：{{ currentLocationPath }}</span>
          </div>
        </template>
        
        <!-- 树形层级视图 -->
        <div v-if="viewMode === 'tree'" class="tree-view-container">
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="tree-container">
                <div class="tree-title">位置层级结构</div>
                <el-scrollbar height="calc(100vh - 350px)" style="border: 1px solid #EBEEF5;">
                  <el-skeleton :loading="loading" animated :rows="10" v-if="loading">
                  </el-skeleton>
                  <el-tree
                    v-else
                    ref="locationTree"
                    :data="locationTreeData"
                    :props="locationTreeProps"
                    node-key="id"
                    highlight-current
                    :default-expand-all="false"
                    :expand-on-click-node="false"
                    @node-click="handleLocationNodeClick"
                    :filter-node-method="filterLocationNode"
                    v-loading="loading"
                  >
                    <template #default="{ node, data }">
                      <div class="location-tree-node">
                        <div class="node-content">
                          <span class="location-name">{{ data.name }}</span>
                          <span class="location-type">
                            <el-tag size="small" :type="getLocationTypeTag(data.type)">
                              {{ getLocationTypeName(data.type) }}
                            </el-tag>
                          </span>
                        </div>
                      </div>
                    </template>
                  </el-tree>
                </el-scrollbar>
              </div>
            </el-col>
            
            <el-col :span="16">
              <div class="location-detail-panel" v-if="selectedLocationForTree">
                <div class="detail-header">
                  <h3>{{ selectedLocationForTree.name }}</h3>
                  <div class="location-path">{{ currentLocationPath }}</div>
                  <div class="location-actions" v-if="selectedLocationForTree.type === 3 || selectedLocationForTree.type === 4">
                    <el-button type="primary" size="small" @click="handleSetManager(selectedLocationForTree)">
                      设置管理员
                    </el-button>
                    <el-button type="success" size="small" @click="handleSetUsers(selectedLocationForTree)">
                      设置使用人
                    </el-button>
                  </div>
                </div>
                
                <el-tabs v-model="activeDetailTab">
                  <el-tab-pane label="位置信息" name="info">
                    <el-descriptions :column="2" border>
                      <el-descriptions-item label="位置名称">{{ selectedLocationForTree.name }}</el-descriptions-item>
                      <el-descriptions-item label="位置类型">
                        <el-tag :type="getLocationTypeTag(selectedLocationForTree.type)">
                          {{ getLocationTypeName(selectedLocationForTree.type) }}
                        </el-tag>
                      </el-descriptions-item>
                      <el-descriptions-item label="上级位置">{{ selectedLocationForTree.parentName || '-' }}</el-descriptions-item>
                      <el-descriptions-item label="使用部门">{{ selectedLocationForTree.departmentName || '-' }}</el-descriptions-item>
                      <el-descriptions-item label="管理人员" :span="2">
                        <div v-if="locationUsersForTree.length > 0">
                          <el-tag 
                            v-for="manager in locationUsersForTree.filter(u => u.userType === 1)" 
                            :key="manager.id"
                            type="warning"
                            style="margin-right: 5px; margin-bottom: 5px"
                          >
                            {{ manager.name || manager.username }}
                          </el-tag>
                          <span v-if="!locationUsersForTree.some(u => u.userType === 1)">未设置</span>
                        </div>
                        <span v-else>未设置</span>
                      </el-descriptions-item>
                      <el-descriptions-item label="使用人员" :span="2">
                        <div v-if="locationUsersForTree.length > 0">
                          <el-tag 
                            v-for="user in locationUsersForTree.filter(u => u.userType === 0)" 
                            :key="user.id"
                            type="info"
                            style="margin-right: 5px; margin-bottom: 5px"
                          >
                            {{ user.name || user.username }}
                          </el-tag>
                          <span v-if="!locationUsersForTree.some(u => u.userType === 0)">未设置</span>
                        </div>
                        <span v-else>未设置</span>
                      </el-descriptions-item>
                      <el-descriptions-item label="备注信息" :span="2">
                        {{ selectedLocationForTree.remark || '-' }}
                      </el-descriptions-item>
                    </el-descriptions>
                    
                    <div class="personnel-info" v-if="selectedLocationForTree.type === 3 || selectedLocationForTree.type === 4">
                      <el-alert
                        title="位置人员管理说明"
                        type="info"
                        :closable="false"
                        style="margin: 20px 0"
                      >
                        <div class="info-content">
                          <p>1. 工序和工位可以设置管理员和使用人员</p>
                          <p>2. 管理员负责此位置的资产和人员管理</p>
                          <p>3. 使用人员是日常使用此位置资产的人员</p>
                          <p>4. 人员信息来自系统用户管理，请确保先添加相关人员</p>
                        </div>
                      </el-alert>
                    </div>
                  </el-tab-pane>
                  
                  <el-tab-pane label="关联资产" name="assets">
                    <div v-loading="treeAssetLoading">
                      <el-empty v-if="locationAssetsForTree.length === 0" description="暂无关联资产"></el-empty>
                      <el-table
                        v-else
                        :data="locationAssetsForTree"
                        style="width: 100%"
                        border
                        stripe
                        size="small"
                      >
                        <el-table-column type="index" label="序号" width="60" align="center"></el-table-column>
                        <el-table-column prop="assetCode" label="资产编号" width="120"></el-table-column>
                        <el-table-column prop="name" label="资产名称" width="150"></el-table-column>
                        <el-table-column prop="assetTypeName" label="资产类型" width="120"></el-table-column>
                        <el-table-column prop="model" label="规格型号" width="120"></el-table-column>
                        <el-table-column label="状态" width="80">
                          <template #default="{ row }">
                            <el-tag :type="getAssetStatusType(row.status)">
                              {{ getAssetStatusText(row.status) }}
                            </el-tag>
                          </template>
                        </el-table-column>
                        <el-table-column label="操作" width="150" fixed="right">
                          <template #default="{ row }">
                            <el-button type="text" @click="viewAssetDetail(row)">查看</el-button>
                            <el-button type="text" class="danger-text" @click="handleUnrelateAsset(row)">解除关联</el-button>
                          </template>
                        </el-table-column>
                      </el-table>
                    </div>
                  </el-tab-pane>
                </el-tabs>
              </div>
              <el-empty 
                v-else 
                description="请选择左侧位置"
                style="margin-top: 100px"
              ></el-empty>
            </el-col>
          </el-row>
        </div>
        
        <!-- 扁平资产列表视图 -->
        <div v-if="viewMode === 'flat'" class="flat-view">
          <!-- 筛选条件 -->
          <el-form :inline="true" class="location-filters">
            <el-form-item label="位置类型">
              <el-select v-model="typeFilter" clearable placeholder="全部类型" style="width: 150px;">
                <el-option label="全部" value=""></el-option>
                <el-option label="工厂" :value="1"></el-option>
                <el-option label="产线" :value="2"></el-option>
                <el-option label="工序" :value="3"></el-option>
                <el-option label="工位" :value="4"></el-option>
              </el-select>
            </el-form-item>
            
            <el-form-item label="上级位置">
              <el-select v-model="parentFilter" filterable clearable placeholder="请选择" style="width: 200px;">
                <el-option 
                  v-for="item in parentOptions" 
                  :key="item.id" 
                  :label="item.name" 
                  :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
            
            <el-form-item label="关键字">
              <el-input v-model="keywordFilter" placeholder="位置名称/编码" style="width: 200px;" />
            </el-form-item>
            
            <el-form-item>
              <el-button type="primary" @click="fetchLocationList">搜索</el-button>
              <el-button @click="resetLocationFilters">重置</el-button>
            </el-form-item>
          </el-form>
          
          <!-- 位置列表表格 -->
          <el-table
            ref="locationTable"
            v-loading="loading"
            :data="locationList"
            style="width: 100%"
            @row-click="handleLocationClick"
            :height="tableHeight"
            border
          >
            <el-table-column type="selection" width="50" />
            <el-table-column prop="code" label="位置编码" width="120" />
            <el-table-column prop="name" label="位置名称" min-width="150" />
            <el-table-column prop="type" label="位置类型" width="100">
            <template #default="scope">
                <el-tag size="small" :type="getLocationTypeTag(scope.row.type)">
                  {{ getLocationTypeName(scope.row.type) }}
              </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="parentName" label="上级位置" min-width="150" />
            <el-table-column prop="departmentName" label="使用部门" min-width="120">
              <template #default="scope">
                <span v-if="scope.row.departmentName">{{ scope.row.departmentName }}</span>
                <el-button v-else type="primary" link @click.stop="handleRelateDepartment(scope.row)">设置部门</el-button>
              </template>
            </el-table-column>
            <el-table-column prop="managerName" label="负责人" width="120">
              <template #default="scope">
                <span v-if="scope.row.managerName">{{ scope.row.managerName }}</span>
                <el-button v-else-if="scope.row.type === 3 || scope.row.type === 4" type="warning" link @click.stop="handleSetManager(scope.row)">设置负责人</el-button>
              <span v-else>-</span>
            </template>
          </el-table-column>
            <el-table-column label="使用人" width="100">
            <template #default="scope">
                <el-button v-if="scope.row.type === 3 || scope.row.type === 4" type="info" link @click.stop="viewLocationUsers(scope.row)">
                  {{ scope.row.userCount || 0 }}人
              </el-button>
                <span v-else>-</span>
            </template>
          </el-table-column>
            <el-table-column label="操作" fixed="right" width="220">
              <template #default="scope">
                <el-button size="small" type="primary" @click.stop="handleRelateAssets(scope.row)">关联资产</el-button>
                <el-dropdown @command="(cmd) => handleCommand(cmd, scope.row)" trigger="click">
                  <el-button size="small" type="primary">
                    更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="setDepartment">设置部门</el-dropdown-item>
                      <el-dropdown-item v-if="scope.row.type === 3 || scope.row.type === 4" command="setManager">设置负责人</el-dropdown-item>
                      <el-dropdown-item v-if="scope.row.type === 3 || scope.row.type === 4" command="setUsers">设置使用人</el-dropdown-item>
                    </el-dropdown-menu>
          </template>
                </el-dropdown>
              </template>
            </el-table-column>
        </el-table>
        
        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="pagination.currentPage"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pagination.total"
              background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
          </div>
        </div>
      </el-card>
    </div>
    
    <!-- 关联资产对话框 -->
    <el-dialog
      title="关联资产"
      v-model="relateDialogVisible"
      width="900px"
      append-to-body
    >
      <div class="relate-dialog-content">
        <div class="filters">
          <el-row :gutter="16">
            <el-col :span="8">
              <el-input
                v-model="assetSearchKeyword"
                placeholder="搜索资产名称/编号/SN"
                clearable
                @keyup.enter="searchUnrelatedAssets"
              >
                <template #append>
                  <el-button :icon="Search" @click="searchUnrelatedAssets" />
                </template>
              </el-input>
            </el-col>
            <el-col :span="8">
              <el-select
                v-model="unrelatedAssetTypeFilter"
                placeholder="资产类型"
                clearable
                style="width: 100%"
                @change="searchUnrelatedAssets"
              >
                <el-option 
                  v-for="type in assetTypes" 
                  :key="type.value" 
                  :label="type.label" 
                  :value="type.value" 
                />
              </el-select>
            </el-col>
            <el-col :span="8">
              <el-select
                v-model="relationType"
                placeholder="关联类型"
                style="width: 100%"
              >
                <el-option label="主要位置" value="primary" />
                <el-option label="次要位置" value="secondary" />
                <el-option label="临时位置" value="temporary" />
              </el-select>
            </el-col>
          </el-row>
        </div>
        
        <el-table
          ref="unrelatedAssetTable"
          v-loading="unrelatedLoading"
          :data="unrelatedAssetList"
          style="width: 100%; margin-top: 16px;"
          @selection-change="handleUnrelatedSelectionChange"
          height="400px"
          border
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="code" label="资产编号" width="120" />
          <el-table-column prop="name" label="资产名称" width="150" />
          <el-table-column prop="type" label="资产类型" width="120">
            <template #default="scope">
              <el-tag size="small" :type="getAssetTypeTag(scope.row.type)">
                {{ scope.row.typeName }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="sn" label="序列号" width="150" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag size="small" :type="getStatusType(scope.row.status)">
                {{ scope.row.statusText }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="department" label="所属部门" width="150" />
          <el-table-column prop="user" label="责任人" width="120" />
          <el-table-column prop="currentLocation" label="当前位置" width="180" />
        </el-table>
        
        <div class="dialog-pagination">
          <el-pagination
            v-model:current-page="unrelatedPagination.currentPage"
            v-model:page-size="unrelatedPagination.pageSize"
            :page-sizes="[10, 20, 50]"
            :background="true"
            layout="total, sizes, prev, pager, next"
            :total="unrelatedPagination.total"
            @size-change="handleUnrelatedSizeChange"
            @current-change="handleUnrelatedCurrentChange"
          />
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="relateDialogVisible = false">取 消</el-button>
          <el-button 
            type="primary" 
            @click="confirmRelateAssets" 
            :disabled="selectedUnrelatedAssets.length === 0 || !relationType"
          >
            确认关联
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 关联部门对话框 -->
    <el-dialog
      title="关联部门"
      v-model="departmentDialogVisible"
      width="600px"
      append-to-body
    >
      <div class="department-dialog-content">
        <el-form
          ref="departmentFormRef"
          :model="departmentFormData"
          label-width="100px"
        >
          <el-form-item label="选择部门" prop="departmentId">
            <el-select
              v-model="departmentFormData.departmentId"
              placeholder="请选择部门"
              style="width: 100%"
            >
              <el-option
                v-for="dept in departments"
                :key="dept.id"
                :label="dept.name"
                :value="dept.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="部门负责人" prop="managerId">
            <el-select
              v-model="departmentFormData.managerId"
              placeholder="请选择部门负责人"
              style="width: 100%"
              :disabled="!departmentFormData.departmentId"
            >
              <el-option
                v-for="user in departmentUsers"
                :key="user.id"
                :label="user.name"
                :value="user.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="departmentFormData.remark"
              type="textarea"
              :rows="3"
              placeholder="请输入备注信息"
            />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="departmentDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitDepartmentForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 设置管理员对话框 -->
    <el-dialog
      title="设置位置管理员"
      v-model="managerDialogVisible"
      width="550px"
      append-to-body
    >
      <div class="manager-dialog-content">
        <el-form
          ref="managerFormRef"
          :model="managerFormData"
          label-width="100px"
        >
          <el-form-item label="管理员" prop="managerId" required>
            <el-select
              v-model="managerFormData.managerId"
              placeholder="请选择位置管理员"
              style="width: 100%"
              filterable
            >
              <el-option
                v-for="user in managerCandidates"
                :key="user.id"
                :label="user.name"
                :value="user.id"
              >
                <div class="user-option">
                  <span>{{ user.name }}</span>
                  <span class="user-detail">{{ user.departmentName || '无部门' }} | {{ user.position || '无职位' }}</span>
                </div>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="managerFormData.remark"
              type="textarea"
              :rows="3"
              placeholder="请输入备注信息"
            />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="managerDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitManagerForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 设置使用人对话框 -->
    <el-dialog
      title="设置位置使用人"
      v-model="usersDialogVisible"
      width="650px"
      append-to-body
    >
      <div class="users-dialog-content">
        <div class="dialog-description">
          <el-alert type="info" :closable="false">
            <p>当前位置: <strong>{{ currentLocationPath }}</strong></p>
            <p>可以为此工位设置多个使用人员。使用人员将可以查看和管理与此位置关联的资产。</p>
          </el-alert>
        </div>

        <div class="dialog-actions">
          <el-button type="primary" @click="handleAddUser" :icon="Plus">添加使用人</el-button>
          <el-checkbox v-model="replaceExistingUsers" label="替换现有使用人" border></el-checkbox>
        </div>

        <el-table
          v-loading="locationUsersLoading"
          :data="selectedLocationUsers"
          style="width: 100%; margin-top: 15px;"
          border
        >
          <el-table-column type="index" label="序号" width="60" />
          <el-table-column prop="name" label="姓名" width="120" />
          <el-table-column prop="departmentName" label="部门" width="150" />
          <el-table-column prop="position" label="职位" width="150" />
          <el-table-column label="操作" width="120">
            <template #default="scope">
              <el-button type="danger" size="small" @click="removeSelectedUser(scope.$index)">
                移除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="usersDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitUsersForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 添加使用人的选择对话框 -->
    <el-dialog
      v-model="selectUserDialogVisible"
      title="选择使用人"
      width="650px"
      append-to-body
    >
      <div class="select-user-dialog-content">
        <el-form inline>
          <el-form-item label="部门">
            <el-select v-model="userSearchDept" placeholder="选择部门" clearable>
              <el-option
                v-for="dept in departments"
                :key="dept.id"
                :label="dept.name"
                :value="dept.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="关键词">
            <el-input v-model="userSearchKeyword" placeholder="姓名/工号" clearable></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="searchUserCandidates">搜索</el-button>
          </el-form-item>
        </el-form>

        <el-table
          v-loading="userCandidatesLoading"
          :data="userCandidates"
          style="width: 100%"
          border
          @row-click="handleUserCandidateSelect"
          height="300px"
        >
          <el-table-column type="index" label="序号" width="60" />
          <el-table-column prop="name" label="姓名" width="120" />
          <el-table-column prop="employeeCode" label="工号" width="120" />
          <el-table-column prop="departmentName" label="部门" width="150" />
          <el-table-column prop="position" label="职位" width="150" />
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick, watch, onBeforeUnmount } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Search, Link, Remove, RefreshRight, View,
  LocationInformation, OfficeBuilding, UserFilled, User, Plus, ArrowDown
} from '@element-plus/icons-vue'
import locationApi from '@/api/location'
import assetApi from '@/api/asset'
import departmentApi from '@/api/department'
import userApi from '@/api/user'

// 表格高度自适应
const tableHeight = ref(450)

// 视图模式切换：flat=扁平列表视图，tree=树形层级视图
const viewMode = ref('tree')

// 位置数据
const locationData = ref([])
const selectedLocation = ref(null)
const currentLocationPath = ref('未选择')
const locationOptions = ref([]) // 位置下拉选项

// 添加当前选择的位置详情
const currentLocationDetail = ref(null)

// 树形视图相关数据
const locationTreeData = ref([])
const locationTreeProps = {
  children: 'children',
  label: 'name'
}
const selectedLocationForTree = ref(null)
const locationUsersForTree = ref([])
const locationAssetsForTree = ref([])
const treeAssetLoading = ref(false)
const treeSearchKeyword = ref('')
const locationTree = ref(null)
const activeDetailTab = ref('info')

// 部门数据
const departments = ref([])
const departmentUsers = ref([])
const selectedDepartment = ref(null)
const departmentFilter = ref('')

// 资产列表数据
const assetList = ref([])
const loading = ref(false)
const selectedAssets = ref([])
const assetTable = ref(null)
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 筛选条件
const assetTypeFilter = ref('')
const assetStatusFilter = ref('')
const keywordFilter = ref('')
const dateRangeFilter = ref([])
const typeFilter = ref('') // 添加位置类型筛选
const parentFilter = ref(null) // 添加上级位置筛选

// 关联资产对话框
const relateDialogVisible = ref(false)
const unrelatedAssetList = ref([])
const unrelatedLoading = ref(false)
const selectedUnrelatedAssets = ref([])
const assetSearchKeyword = ref('')
const unrelatedAssetTypeFilter = ref('')
const relationType = ref('primary')
const unrelatedPagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 部门关联对话框
const departmentDialogVisible = ref(false)
const departmentFormRef = ref(null)
const departmentFormData = reactive({
  departmentId: '',
  managerId: '',
  remark: ''
})

// 设置管理员对话框
const managerDialogVisible = ref(false)
const managerFormRef = ref(null)
const managerFormData = reactive({
  managerId: '',
  remark: ''
})
const managerCandidates = ref([])

// 设置使用人对话框
const usersDialogVisible = ref(false)
const locationUsersLoading = ref(false)
const selectedLocationUsers = ref([])
const replaceExistingUsers = ref(false)
const selectUserDialogVisible = ref(false)
const userSearchDept = ref(null)
const userSearchKeyword = ref('')
const userCandidates = ref([])
const userCandidatesLoading = ref(false)

// 资产类型和状态选项
const assetTypes = ref([])
const assetStatuses = [
  { label: '在用', value: 'in_use' },
  { label: '闲置', value: 'idle' },
  { label: '维修中', value: 'repairing' },
  { label: '借用', value: 'borrowed' },
  { label: '报废', value: 'scrapped' }
]

// 所有数据是否已加载完成
const dataLoaded = ref(false)

// 初始化数据加载
const fetchInitialData = async () => {
  console.log('开始初始化数据...');
  loading.value = true;
  
  try {
    // 先加载基础数据
    await Promise.all([
      fetchDepartments(),
      fetchLocationOptions()
    ]);
    
    // 根据当前视图模式加载相应数据
    if (viewMode.value === 'tree') {
      // 树形视图优先加载树结构
      await fetchLocationTreeData();
      // 不预加载扁平列表，等用户切换视图时再加载
    } else {
      // 扁平视图优先加载位置列表
      await fetchLocationList();
      // 不预加载树形数据，等用户切换视图时再加载
    }
    
    console.log('数据初始化完成');
  } catch (error) {
    console.error('初始化数据失败:', error);
    ElMessage.error('初始化数据失败，请刷新页面重试');
  } finally {
    loading.value = false;
  }
};

// 组件挂载完成后加载数据
onMounted(() => {
  console.log('位置关联页面加载');
  fetchInitialData();
});

// 修复树形视图显示方法
const handleChangeViewMode = (mode) => {
  console.log('切换视图模式:', mode);
  viewMode.value = mode;
  
  // 当切换到树形视图时，确保树形数据已加载
  if (mode === 'tree' && (!locationTreeData.value || locationTreeData.value.length === 0)) {
    fetchLocationTree();
  }
  
  // 当切换到扁平视图时，确保列表数据已加载
  if (mode === 'flat' && (!locationList.value || locationList.value.length === 0)) {
    fetchLocationList();
  }
};

// 获取树形数据的方法
const fetchLocationTree = async () => {
  try {
    console.log('获取位置树形结构');
    const res = await locationApi.getLocationTree();
    if (res.success) {
      locationTreeData.value = res.data || [];
      console.log('位置树形数据:', locationTreeData.value);
    } else {
      ElMessage.error(res.message || '获取位置树形结构失败');
    }
  } catch (error) {
    console.error('获取位置树形结构失败:', error);
    ElMessage.error('获取位置树形结构失败');
  }
};

// 在搜索框输入时过滤树节点
const filterLocationNode = (value, data) => {
  if (!value) return true;
  
  // 检查节点名称、代码或类型是否包含搜索关键词
  const searchValue = value.toLowerCase();
  const nameMatch = data.name && data.name.toLowerCase().includes(searchValue);
  const codeMatch = data.code && data.code.toLowerCase().includes(searchValue);
  const typeMatch = getLocationTypeName(data.type).toLowerCase().includes(searchValue);
  
  return nameMatch || codeMatch || typeMatch;
};

// 组件卸载前清理
onBeforeUnmount(() => {
  console.log('清理位置关联页面资源')
  // 清空数据
  selectedLocation.value = null
  locationOptions.value = []
  departments.value = []
  assetList.value = []
  
  // 移除可能的定时器或观察器
  if (window._locationRelationsTimer) {
    clearTimeout(window._locationRelationsTimer)
    window._locationRelationsTimer = null
  }
})

// 获取部门列表
const fetchDepartments = async () => {
  try {
    console.log('开始获取部门列表')
    const res = await departmentApi.getDepartmentList()
    if (res.success) {
      departments.value = res.data || []
      console.log(`加载了 ${departments.value.length} 个部门`)
    } else {
      departments.value = []
    }
  } catch (error) {
    console.error('获取部门列表失败:', error)
    departments.value = []
  }
}

// 获取部门用户列表
const fetchDepartmentUsers = async (departmentId) => {
  if (!departmentId) {
    departmentUsers.value = []
    return
  }
  try {
    const res = await departmentApi.getDepartmentUsers(departmentId)
    departmentUsers.value = res.data || []
  } catch (error) {
    console.error('获取部门用户列表失败:', error)
    ElMessage.error('获取部门用户列表失败')
  }
}

// 监听部门变化
watch(() => departmentFormData.departmentId, (newVal) => {
  departmentFormData.managerId = ''
  fetchDepartmentUsers(newVal)
})

// 关联部门
const handleRelateDepartment = () => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  departmentDialogVisible.value = true
  departmentFormData.departmentId = ''
  departmentFormData.managerId = ''
  departmentFormData.remark = ''
}

// 提交部门关联表单
const submitDepartmentForm = async () => {
  if (!departmentFormData.departmentId) {
    ElMessage.warning('请选择部门')
    return
  }
  
  try {
    await locationApi.relateDepartment({
      locationId: selectedLocation.value,
      ...departmentFormData
    })
    
    ElMessage.success('关联部门成功')
    departmentDialogVisible.value = false
    
    // 更新部门信息
    const dept = departments.value.find(d => d.id === departmentFormData.departmentId)
    if (dept) {
      selectedDepartment.value = dept
    }
    
    // 刷新资产列表
    fetchAssetsForLocation()
  } catch (error) {
    console.error('关联部门失败:', error)
    ElMessage.error('关联部门失败')
  }
}

// 获取位置树数据 - 优化树形结构展示
const fetchLocationTreeData = async () => {
  try {
    console.log('正在加载位置树形数据...');
    loading.value = true;
    
    // 使用标准API获取位置树数据
    const treeRes = await locationApi.getLocationTree();
    
    if (treeRes && treeRes.success) {
      locationTreeData.value = treeRes.data || [];
      console.log('树形数据加载成功，节点数量:', countNodes(locationTreeData.value));
      
      // 默认展开第一级节点，不全部展开以提高性能
      nextTick(() => {
        if (locationTree.value && locationTreeData.value.length > 0) {
          locationTreeData.value.forEach(node => {
            if (locationTree.value.store && locationTree.value.store.nodesMap && locationTree.value.store.nodesMap[node.id]) {
              locationTree.value.store.nodesMap[node.id].expanded = true;
            }
          });
        }
      });
    } else {
      console.error('获取位置树形数据失败:', treeRes?.message);
      ElMessage.error('获取位置树形数据失败，请刷新重试');
      locationTreeData.value = [];
    }
  } catch (error) {
    console.error('加载位置树形数据出错:', error);
    ElMessage.error('加载位置树数据失败');
    locationTreeData.value = [];
  } finally {
    loading.value = false;
  }
}

// 计算树节点总数
const countNodes = (tree) => {
  if (!tree || !tree.length) return 0
  
  let count = tree.length
  for (const node of tree) {
    if (node.children && node.children.length) {
      count += countNodes(node.children)
    }
  }
  return count
}

// 搜索位置树
const searchLocationTree = () => {
  locationTree.value?.filter(treeSearchKeyword.value)
}

// 处理树节点点击事件
const handleLocationNodeClick = async (data, node) => {
  console.log('点击位置节点:', data.name, data);
  selectedLocationForTree.value = data;
  currentLocationDetail.value = data;
  currentLocationPath.value = data.fullPath || data.name;
  
  // 使用节流处理，防止频繁点击导致的性能问题
  if (window._locationClickTimer) {
    clearTimeout(window._locationClickTimer);
  }
  
  window._locationClickTimer = setTimeout(async () => {
    treeAssetLoading.value = true;
    
    try {
      // 只有当节点类型为工序(3)或工位(4)时才加载用户数据
      if (data.type === 3 || data.type === 4) {
        // 加载位置使用人
        await loadLocationUsers(data.id);
      } else {
        // 清空当前用户数据
        locationUsersForTree.value = [];
      }
      
      // 加载关联资产
      await fetchLocationAssets(data.id);
    } catch (error) {
      console.error('加载位置节点数据失败:', error);
    } finally {
      treeAssetLoading.value = false;
    }
  }, 300); // 300ms的延迟，防止频繁请求
}

// 在树视图中处理关联资产
const handleRelateAssetsForTree = (node = null) => {
  // 使用传入的节点或当前选中节点
  const targetNode = node || selectedLocationForTree.value;
  
  if (!targetNode || !targetNode.id) {
    ElMessage.warning('请选择位置');
    return;
  }
  
  selectedLocation.value = targetNode.id;
  currentLocationDetail.value = targetNode;
  handleRelateAssets();
}

// 在树视图中处理关联部门
const handleRelateDepartmentForTree = (node = null) => {
  // 使用传入的节点或当前选中节点
  const targetNode = node || selectedLocationForTree.value;
  
  if (!targetNode || !targetNode.id) {
    ElMessage.warning('请选择位置');
    return;
  }
  
  selectedLocation.value = targetNode.id;
  currentLocationDetail.value = targetNode;
  handleRelateDepartment();
}

// 在树视图中处理设置管理员
const handleSetManagerForTree = (node = null) => {
  // 使用传入的节点或当前选中节点
  const targetNode = node || selectedLocationForTree.value;
  
  if (!targetNode || !targetNode.id) {
    ElMessage.warning('请选择位置');
    return;
  }
  
  selectedLocation.value = targetNode.id;
  currentLocationDetail.value = targetNode;
  handleSetManager();
}

// 在树视图中处理设置使用人
const handleSetUsersForTree = (node = null) => {
  // 使用传入的节点或当前选中节点
  const targetNode = node || selectedLocationForTree.value;
  
  if (!targetNode || !targetNode.id) {
    ElMessage.warning('请选择位置');
    return;
  }
  
  selectedLocation.value = targetNode.id;
  currentLocationDetail.value = targetNode;
  handleSetUsers();
}

// 在树视图中处理解除资产关联
const handleUnrelateFromTree = (asset) => {
  if (!selectedLocationForTree.value || !selectedLocationForTree.value.id) {
    ElMessage.warning('请选择位置')
    return
  }
  
  ElMessageBox.confirm(`确认解除资产 ${asset.code || asset.assetCode} 与当前位置的关联关系吗?`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      treeAssetLoading.value = true
      // 调用API解除关联
      const result = await assetApi.changeAssetLocation(asset.id, {
        newLocationId: 0, // 使用0或null表示解除关联
        reason: '手动解除关联',
        notes: '从位置树视图解除'
      })
      
      if (result && result.success) {
    ElMessage.success('解除关联成功')
        // 重新加载资产列表
        await loadLocationAssets(selectedLocationForTree.value.id)
      } else {
        console.error('解除关联失败:', result)
        ElMessage.error(result?.message || '解除关联失败')
      }
  } catch (error) {
      console.error('解除关联异常:', error)
      ElMessage.error('解除关联失败，请检查网络连接')
    } finally {
      treeAssetLoading.value = false
    }
  }).catch(() => {
    // 用户取消操作
  })
}

// 获取位置类型文本
const getLocationTypeName = (type) => {
  const typeMap = {
    0: "工厂",
    1: "车间", 
    2: "产线", 
    3: "工序", 
    4: "工位"
  }
  return typeMap[type] || '未知'
}

// 获取位置类型标签样式
const getLocationTypeTag = (type) => {
  const typeTagMap = {
    0: "danger",    // 工厂
    1: "warning",   // 车间
    2: "primary",   // 产线 
    3: "success",   // 工序
    4: "info"       // 工位
  }
  return typeTagMap[type] || ''
}

// 监听视图模式变化
watch(viewMode, (newMode) => {
  if (newMode === 'tree' && locationTreeData.value.length === 0) {
    fetchLocationTreeData()
  } else if (newMode === 'flat') {
    // 重置树视图相关数据
    selectedLocationForTree.value = null
    locationUsersForTree.value = []
    locationAssetsForTree.value = []
    
    // 加载扁平位置列表数据
    fetchLocationList()
  }
})

// 扁平列表视图 - 获取位置列表
const fetchLocationList = async () => {
  loading.value = true;
  try {
    // 使用与后端匹配的参数格式
    const params = {
      pageIndex: pagination.currentPage,
      pageSize: pagination.pageSize
    };
    
    // 添加可选筛选条件
    if (typeFilter.value) {
      params.type = typeFilter.value;
    }
    
    if (parentFilter.value) {
      params.parentId = parentFilter.value;
    }
    
    if (keywordFilter.value) {
      params.keyword = keywordFilter.value;
    }
    
    console.log('获取位置列表 - API调用', params);
    
    // 使用GET方法调用接口
    const res = await locationApi.searchLocations(params);
    
    if (res && res.success) {
      locationList.value = res.data?.items || [];
      pagination.total = res.data?.total || 0;
      console.log(`获取到位置列表: ${locationList.value.length}条记录`);
      
      // 获取工序和工位的用户信息
      await fetchLocationUsersInfo();
    } else {
      console.error('获取位置列表失败:', res?.message);
      ElMessage.error(res?.message || '获取位置列表失败');
      locationList.value = [];
      pagination.total = 0;
    }
  } catch (error) {
    console.error('获取位置列表失败:', error);
    ElMessage.error('获取位置列表失败，请检查网络连接');
    locationList.value = [];
    pagination.total = 0;
  } finally {
    loading.value = false;
  }
}

// 获取位置用户信息 - 单独抽取方法
const fetchLocationUsersInfo = async () => {
  // 过滤工序和工位位置
  const locationIds = locationList.value
    .filter(location => location.type === 3 || location.type === 4)
    .map(location => location.id);
  
  if (!locationIds.length) return;
  
  try {
    // 尝试批量获取用户数据
    const batchRes = await locationApi.getLocationUsersBatch(locationIds).catch(() => null);
    
    if (batchRes && batchRes.success && Array.isArray(batchRes.data)) {
      // 将用户数据分配到相应位置
      locationList.value.forEach(location => {
        const userData = batchRes.data.find(d => d.locationId === location.id);
        location.userCount = userData?.users?.length || 0;
      });
      return;
    }
    
    // 如果批量API不可用，回退到单独请求
    console.warn('批量获取用户数据失败，使用逐个请求');
    await Promise.all(
      locationIds.map(async locationId => {
        try {
          const location = locationList.value.find(loc => loc.id === locationId);
          if (!location) return;
          
          const usersRes = await locationApi.getLocationUsers(locationId);
          location.userCount = usersRes?.success ? (usersRes.data?.length || 0) : 0;
        } catch (err) {
          console.warn(`获取位置${locationId}用户数据失败:`, err);
        }
      })
    );
  } catch (error) {
    console.error('获取位置用户信息失败:', error);
  }
}

// 扁平列表视图 - 重置筛选条件
const resetLocationFilters = () => {
  typeFilter.value = ''
  parentFilter.value = null
  keywordFilter.value = ''
    pagination.currentPage = 1
  fetchLocationList()
}

// 加载上级位置选项
const loadParentOptions = async () => {
  try {
    const res = await locationApi.getLocationsForDropdown({ includeAll: true })
    if (res.success) {
      parentOptions.value = res.data || []
      console.log(`加载了 ${parentOptions.value.length} 个上级位置选项`)
    }
  } catch (error) {
    console.error('加载上级位置选项失败:', error)
  }
}

// 处理位置点击
const handleLocationClick = (row) => {
  selectedLocation.value = row.id
  currentLocationDetail.value = row
  currentLocationPath.value = row.fullPath || row.name
  
  // 加载位置使用人
  if (row.type === 3 || row.type === 4) {
    loadLocationUsers(row.id)
  }
  
  // 加载关联资产
    fetchAssetsForLocation()
}

// 查看位置使用人
const viewLocationUsers = async (row) => {
  try {
    currentLocationDetail.value = row
    usersDialogVisible.value = true
    locationUsersLoading.value = true
    
    const res = await locationApi.getLocationUsers(row.id)
    if (res.success) {
      locationUsers.value = res.data || []
  } else {
      locationUsers.value = []
      ElMessage.error(res.message || '获取使用人列表失败')
    }
  } catch (error) {
    console.error('获取使用人列表失败:', error)
    locationUsers.value = []
    ElMessage.error('获取使用人列表失败')
  } finally {
    locationUsersLoading.value = false
  }
}

// 多功能命令处理
const handleCommand = (command, row) => {
  switch (command) {
    case 'setDepartment':
      handleRelateDepartment(row)
      break
    case 'setManager':
      handleSetManager(row)
      break
    case 'setUsers':
      handleSetUsers(row)
      break
  }
}

// 分页处理
const handleSizeChange = (size) => {
  pagination.pageSize = size
  if (viewMode.value === 'flat') {
    fetchLocationList()
  } else {
    fetchAssetsForLocation()
  }
}

const handleCurrentChange = (page) => {
  pagination.currentPage = page
  if (viewMode.value === 'flat') {
    fetchLocationList()
  } else {
    fetchAssetsForLocation()
  }
}

// 获取资产列表 - 优化版
const fetchAssetsForLocation = async () => {
  if (!selectedLocation.value && !selectedLocationForTree.value) {
    assetList.value = [];
    pagination.total = 0;
    return;
  }
  
  loading.value = true;
  try {
    // 确定当前选中位置ID
    const locationId = selectedLocation.value || 
                      (selectedLocationForTree.value ? selectedLocationForTree.value.id : null);
    
    if (!locationId) {
      assetList.value = [];
      pagination.total = 0;
      return;
    }
    
    // 构建查询参数，确保与后端API匹配
    const params = {
      pageIndex: pagination.currentPage,
      pageSize: pagination.pageSize,
      locationId: locationId,
      keyword: keywordFilter.value || '',
      assetType: assetTypeFilter.value || null,
      status: assetStatusFilter.value || null,
      startDate: dateRangeFilter.value && dateRangeFilter.value[0] ? dateRangeFilter.value[0] : null,
      endDate: dateRangeFilter.value && dateRangeFilter.value[1] ? dateRangeFilter.value[1] : null,
      departmentId: departmentFilter.value || null
    };
    
    console.log(`获取位置(ID:${locationId})关联资产`);
    
    const res = await locationApi.getLocationAssets(params);
    
    if (res && res.success) {
      assetList.value = res.data?.items || [];
      pagination.total = res.data?.total || 0;
      console.log(`获取到${assetList.value.length}个资产，总数:${pagination.total}`);
    } else {
      console.error('获取资产列表失败:', res?.message);
      assetList.value = [];
      pagination.total = 0;
    }
  } catch (error) {
    console.error('获取资产列表失败:', error);
    ElMessage.error('获取资产列表失败');
    assetList.value = [];
    pagination.total = 0;
  } finally {
    loading.value = false;
  }
}

// 位置选择变更
const handleLocationChange = async (locationId) => {
  console.log('位置选择变更:', locationId)
  
  // 清除当前位置详情
  currentLocationDetail.value = null
  
  if (!locationId) {
    selectedLocation.value = null
    currentLocationPath.value = '未选择'
    resetFilters(false) // 不清空位置选择
    return
  }
  
  selectedLocation.value = locationId
  
  // 获取位置详情
  try {
    loading.value = true
    const res = await locationApi.getLocationDetail(locationId)
    if (res.success) {
      currentLocationDetail.value = res.data
      console.log('获取到位置详情:', res.data)
      
      // 设置当前位置路径
      currentLocationPath.value = res.data.path ? res.data.path.replace(/,/g, ' > ') : res.data.name
      
      // 如果位置有关联部门，更新部门筛选
      if (res.data.defaultDepartmentId) {
        departmentFilter.value = res.data.defaultDepartmentId
        
        // 更新选中的部门信息
        const dept = departments.value.find(d => d.id === res.data.defaultDepartmentId)
        if (dept) {
          selectedDepartment.value = dept
          console.log('位置关联的默认部门:', dept.name)
        } else {
          // 如果找不到部门信息，可能需要重新加载部门列表
          await fetchDepartments()
          const reloadedDept = departments.value.find(d => d.id === res.data.defaultDepartmentId)
          if (reloadedDept) {
            selectedDepartment.value = reloadedDept
            console.log('重新加载后找到部门:', reloadedDept.name)
          } else {
            console.warn('找不到与位置关联的部门信息:', res.data.defaultDepartmentId)
            selectedDepartment.value = null
          }
        }
      } else {
        departmentFilter.value = null
        selectedDepartment.value = null
      }
    }
  } catch (error) {
    console.error('获取位置详情失败:', error)
    ElMessage.error('获取位置详情失败')
  } finally {
    loading.value = false
  }
  
  // 清空其他筛选条件
  assetStatusFilter.value = null
  keywordFilter.value = ''
  dateRangeFilter.value = null
  
  // 获取该位置的资产列表
  fetchAssetsForLocation()
}

// 重置筛选条件
const resetFilters = (includeLocation = true) => {
  if (includeLocation) {
    selectedLocation.value = null
  }
  departmentFilter.value = null
  assetStatusFilter.value = null
  keywordFilter.value = ''
  dateRangeFilter.value = null
  // 重置后重新获取数据
  fetchAssetsForLocation()
}

// 关联资产按钮点击处理
const handleRelateAssets = () => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  relateDialogVisible.value = true
  // 重置搜索表单
  assetSearchKeyword.value = ''
  unrelatedAssetTypeFilter.value = null
  relationType.value = 'primary'
  selectedUnrelatedAssets.value = []
  unrelatedPagination.currentPage = 1
  // 获取未关联的资产列表
  searchUnrelatedAssets()
}

// 多选变更事件
const handleSelectionChange = (selection) => {
  selectedAssets.value = selection
}

// 单个解除关联
const handleUnrelate = (asset) => {
  ElMessageBox.confirm(`确认解除资产 ${asset.code || asset.assetCode} 与当前位置的关联关系吗?`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      loading.value = true
      // 调用API解除关联
      const result = await assetApi.changeAssetLocation(asset.id, {
        newLocationId: 0, // 使用0或null表示解除关联
        reason: '手动解除关联',
        notes: '从位置关联管理页面解除'
      })
      
      if (result && result.success) {
        ElMessage.success('解除关联成功')
        // 重新获取位置下的资产列表
        fetchAssetsForLocation()
      } else {
        console.error('解除关联失败:', result)
        ElMessage.error(result?.message || '解除关联失败')
      }
    } catch (error) {
      console.error('解除关联异常:', error)
      ElMessage.error('解除关联失败，请检查网络连接')
    } finally {
      loading.value = false
    }
  }).catch(() => {
    // 用户取消操作
  })
}

// 批量解除关联
const handleBatchUnrelate = () => {
  if (selectedAssets.value.length === 0) {
    ElMessage.warning('请选择要解除关联的资产')
    return
  }
  
  const assetNames = selectedAssets.value.map(asset => asset.name || asset.assetCode).join(', ')
  
  ElMessageBox.confirm(`确认解除资产 [${assetNames}] 与当前位置的关联关系吗?`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      loading.value = true
      
      // 创建批量解除关联的请求
      const promises = selectedAssets.value.map(asset => 
        assetApi.changeAssetLocation(asset.id, {
          newLocationId: 0, // 使用0或null表示解除关联
          reason: '批量解除关联',
          notes: '从位置关联管理页面批量解除'
        })
      )
      
      // 并行处理所有请求
      const results = await Promise.allSettled(promises)
      
      // 检查结果
      const succeeded = results.filter(r => r.status === 'fulfilled' && r.value?.success).length
      const failed = results.length - succeeded
      
      if (failed === 0) {
        ElMessage.success(`成功解除 ${succeeded} 个资产的关联`)
      } else if (succeeded === 0) {
        ElMessage.error(`解除关联失败，请重试`)
      } else {
        ElMessage.warning(`部分资产解除关联成功，${succeeded} 成功，${failed} 失败`)
      }
      
      // 重新获取数据
      fetchAssetsForLocation()
    } catch (error) {
      console.error('批量解除关联异常:', error)
      ElMessage.error('批量解除关联失败，请检查网络连接')
    } finally {
      loading.value = false
      // 清空选择
      selectedAssets.value = []
    }
  }).catch(() => {
    // 用户取消操作
  })
}

// 获取未关联资产列表
const fetchUnrelatedAssets = async () => {
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  unrelatedLoading.value = true
  
  try {
  // 构建查询参数
  const params = {
    locationId: selectedLocation.value,
      unrelated: true,
      pageIndex: unrelatedPagination.currentPage,
    pageSize: unrelatedPagination.pageSize,
    keyword: assetSearchKeyword.value,
      assetTypeId: unrelatedAssetTypeFilter.value
    }
    
    // 调用资产API获取未关联的资产列表
    const res = await assetApi.getAssetList(params)
    
    if (res.success) {
      // 将API返回的资产数据格式化为组件需要的格式
      unrelatedAssetList.value = (res.data?.items || []).map(asset => ({
        id: asset.id,
        code: asset.assetCode,
        name: asset.name,
        type: asset.assetTypeId,
        typeName: asset.assetTypeName,
        sn: asset.serialNumber || '-',
        status: asset.status || 0,
        statusText: getStatusName(asset.status || 0)
      }))
      unrelatedPagination.total = res.data?.total || 0
    } else {
      unrelatedAssetList.value = []
      unrelatedPagination.total = 0
      ElMessage.error(res.message || '获取未关联资产失败')
    }
  } catch (error) {
    console.error('获取未关联资产出错:', error)
    ElMessage.error('获取未关联资产列表失败')
    unrelatedAssetList.value = []
    unrelatedPagination.total = 0
  } finally {
    unrelatedLoading.value = false
  }
}

// 搜索未关联资产
const searchUnrelatedAssets = async () => {
  await fetchUnrelatedAssets()
}

// 未关联资产多选变更事件
const handleUnrelatedSelectionChange = (selection) => {
  selectedUnrelatedAssets.value = selection
}

// 确认关联资产
const confirmRelateAssets = async () => {
  if (selectedUnrelatedAssets.value.length === 0) {
    ElMessage.warning('请选择要关联的资产')
    return
  }
  
  if (!relationType.value) {
    ElMessage.warning('请选择关联类型')
    return
  }
  
  if (!selectedLocation.value) {
    ElMessage.warning('请先选择位置')
    return
  }
  
  // 显示确认对话框
  try {
    await ElMessageBox.confirm(
      `确认将选中的 ${selectedUnrelatedAssets.value.length} 个资产关联到 "${currentLocationPath.value}" 吗？`,
      '确认关联',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'info'
      }
    )
    
    // 用户确认，执行关联操作
    // 显示加载状态
    unrelatedLoading.value = true
    
    // 准备关联数据
    const assetIds = selectedUnrelatedAssets.value.map(asset => asset.id)
    
    console.log('关联资产数据:', {
      位置ID: selectedLocation.value,
      资产IDs: assetIds,
      关联类型: relationType.value
    })
    
    // 调用API关联资产到位置
    try {
      const res = await locationApi.relateAssets(selectedLocation.value, {
        assetIds: assetIds,
        relationType: relationType.value,
        notes: '从位置关联管理页面关联'
      })
      
      if (res.success) {
        ElMessage.success(`成功关联 ${assetIds.length} 个资产到 ${currentLocationPath.value}`)
  
  // 关闭对话框
  relateDialogVisible.value = false
        
        // 清空选择
        selectedUnrelatedAssets.value = []
  
  // 刷新资产列表
        fetchAssetsForLocation()
      } else {
        ElMessage.error(res.message || '关联资产失败')
      }
    } catch (error) {
      console.error('关联资产失败:', error)
      ElMessage.error('关联资产失败: ' + (error.message || '未知错误'))
    } finally {
      unrelatedLoading.value = false
    }
  } catch (e) {
    // 用户取消操作
    console.log('用户取消关联操作')
  }
}

// 获取状态名称辅助函数
const getStatusName = (status) => {
  const statusMap = {
    0: '未知',
    1: '在用',
    2: '闲置',
    3: '维修中',
    4: '借用',
    5: '报废'
  }
  return statusMap[status] || '未知'
}

// 工具方法：获取状态标签类型
const getStatusType = (status) => {
  const map = {
    'in_use': 'success',
    'idle': 'info',
    'repairing': 'warning',
    'borrowed': 'primary',
    'scrapped': 'danger'
  }
  return map[status] || 'info'
}

// 工具方法：获取资产类型标签
const getAssetTypeTag = (type) => {
  const map = {
    'laptop': '',
    'desktop': 'success',
    'server': 'danger',
    'monitor': 'warning',
    'printer': 'info',
    'network': 'primary',
    'other': 'info'
  }
  return map[type] || 'info'
}

// 工具方法：获取关联类型标签
const getRelationTag = (type) => {
  const map = {
    'primary': 'success',
    'secondary': 'info',
    'temporary': 'warning'
  }
  return map[type] || 'info'
}

// 工具方法：获取关联类型文本
const getRelationTypeText = (type) => {
  const map = {
    'primary': '主要位置',
    'secondary': '次要位置',
    'temporary': '临时位置'
  }
  return map[type] || '未知'
}

// 查看资产详情
const handleAssetDetail = (asset) => {
  ElMessage.info(`查看资产 ${asset.name} (${asset.code}) 的详情`)
  // 实际项目中可以跳转到资产详情页: router.push(`/assets/detail/${asset.id}`)
}

// 添加使用人
const handleAddUser = () => {
  // 重置搜索条件
  userSearchDept.value = null
  userSearchKeyword.value = ''
  userCandidates.value = []
  
  // 搜索用户候选人
  searchUserCandidates()
  
  // 显示选择对话框
  selectUserDialogVisible.value = true
}

// 搜索用户候选人
const searchUserCandidates = async () => {
  userCandidatesLoading.value = true
  
  try {
    // 调用API搜索用户
    const res = await userApi.getUsersByDepartment({
      departmentId: userSearchDept.value || null,
      keyword: userSearchKeyword.value || '',
      userType: 0 // 0表示普通用户/使用人
    })
    
    if (res.success) {
      // 过滤掉已经选择的用户
      const selectedIds = selectedLocationUsers.value.map(u => u.id)
      userCandidates.value = (res.data || []).filter(u => !selectedIds.includes(u.id))
    } else {
      ElMessage.error('搜索用户失败: ' + (res.message || '未知错误'))
      userCandidates.value = []
    }
  } catch (error) {
    console.error('搜索用户出错:', error)
    ElMessage.error('搜索用户失败')
    userCandidates.value = []
  } finally {
    userCandidatesLoading.value = false
  }
}

// 选择用户候选人
const handleUserCandidateSelect = (row) => {
  // 添加到已选列表
  selectedLocationUsers.value.push({
    id: row.id,
    name: row.name,
    departmentName: row.departmentName,
    position: row.position
  })
  
  // 关闭选择对话框
  selectUserDialogVisible.value = false
}

// 移除已选用户
const removeSelectedUser = (index) => {
  selectedLocationUsers.value.splice(index, 1)
}

// 更新设置管理员方法，使其支持工序和工位
const handleSetManager = async (row = null) => {
  // 确定要操作的位置，可以是传入的行或当前选择的位置
  const targetLocation = row || currentLocationDetail.value;
  
  if (!targetLocation || (targetLocation.type !== 3 && targetLocation.type !== 4)) {
    ElMessage.warning('只能为工序或工位类型的位置设置管理员');
    return;
  }

  // 更新当前选择的位置
  if (row) {
    currentLocationDetail.value = row;
    selectedLocation.value = row.id;
  }

  // 重置表单
  managerFormData.managerId = '';
  managerFormData.remark = '';
  
  try {
    // 加载管理员候选人
    const res = await userApi.getUsersByRole({ userType: 1 }); // 1表示管理员类型
    if (res.success) {
      managerCandidates.value = res.data || [];
      
      // 如果当前位置已有管理员，则默认选中
      if (targetLocation.managerId) {
        managerFormData.managerId = targetLocation.managerId;
      }
      
      // 显示对话框
      managerDialogVisible.value = true;
    } else {
      ElMessage.error('获取管理员候选人失败: ' + (res.message || '未知错误'));
    }
  } catch (error) {
    console.error('获取管理员候选人出错:', error);
    ElMessage.error('获取管理员候选人失败');
  }
}

// 更新设置使用人方法，使其支持工序和工位
const handleSetUsers = async (row = null) => {
  // 确定要操作的位置，可以是传入的行或当前选择的位置
  const targetLocation = row || currentLocationDetail.value;
  
  if (!targetLocation || (targetLocation.type !== 3 && targetLocation.type !== 4)) {
    ElMessage.warning('只能为工序或工位类型的位置设置使用人');
    return;
  }
  
  // 更新当前选择的位置
  if (row) {
    currentLocationDetail.value = row;
    selectedLocation.value = row.id;
  }
  
  // 重置数据
  selectedLocationUsers.value = [];
  replaceExistingUsers.value = false;
  locationUsersLoading.value = true;
  
  try {
    // 获取当前位置的使用人
    const res = await locationApi.getLocationUsers(targetLocation.id);
    if (res.success) {
      // 筛选出使用人(类型为0)
      selectedLocationUsers.value = (res.data || [])
        .filter(user => user.userType === 0)
        .map(user => ({
          id: user.personnelId,
          name: user.name,
          departmentName: user.departmentName,
          position: user.position,
          remarks: user.remarks
        }));
      
      // 显示对话框
      usersDialogVisible.value = true;
    } else {
      ElMessage.error('获取位置使用人失败: ' + (res.message || '未知错误'));
    }
  } catch (error) {
    console.error('获取位置使用人出错:', error);
    ElMessage.error('获取位置使用人失败');
  } finally {
    locationUsersLoading.value = false;
  }
}

// 成功设置后处理方法
const afterSetManager = async () => {
  // 重新加载位置详情
  if (viewMode.value === 'flat') {
    if (selectedLocation.value) {
      await handleLocationChange(selectedLocation.value);
    } else {
      // 刷新扁平列表
      await fetchLocationList();
    }
  } else {
    if (selectedLocationForTree.value?.id) {
      await loadLocationDetails(selectedLocationForTree.value.id);
    }
  }
}

// 修改提交管理员设置方法
const submitManagerForm = async () => {
  if (!managerFormData.managerId) {
    ElMessage.warning('请选择管理员');
    return;
  }
  
  try {
    // 确保当前选择位置可用
    const locationId = currentLocationDetail.value?.id;
    
    if (!locationId) {
      ElMessage.warning('请选择位置');
      return;
    }
    
    // 调用API设置管理员
    const res = await locationApi.updateLocationUsers(locationId, {
      replaceExisting: true,
      users: [
        {
          personnelId: managerFormData.managerId,
          userType: 1, // 1表示管理员类型
          remarks: managerFormData.remark || ''
        }
      ]
    });
    
    if (res.success) {
      ElMessage.success('设置管理员成功');
      managerDialogVisible.value = false;
      
      // 重新加载位置详情
      await afterSetManager();
    } else {
      ElMessage.error('设置管理员失败: ' + (res.message || '未知错误'));
    }
  } catch (error) {
    console.error('设置管理员出错:', error);
    ElMessage.error('设置管理员失败');
  }
}

// 修改提交使用人设置方法
const submitUsersForm = async () => {
  if (selectedLocationUsers.value.length === 0) {
    ElMessage.warning('请至少添加一个使用人');
    return;
  }
  
  try {
    // 确保当前选择位置可用
    const locationId = currentLocationDetail.value?.id;
    
    if (!locationId) {
      ElMessage.warning('请选择位置');
      return;
    }
    
    // 准备请求数据
    const requestData = {
      replaceExisting: replaceExistingUsers.value,
      users: selectedLocationUsers.value.map(user => ({
        personnelId: user.id,
        userType: 0, // 0表示普通用户/使用人
        remarks: user.remarks || ''
      }))
    };
    
    // 调用API设置使用人
    const res = await locationApi.updateLocationUsers(locationId, requestData);
    
    if (res.success) {
      ElMessage.success('设置使用人成功');
      usersDialogVisible.value = false;
      
      // 重新加载位置详情
      await afterSetManager();
    } else {
      ElMessage.error('设置使用人失败: ' + (res.message || '未知错误'));
    }
  } catch (error) {
    console.error('设置使用人出错:', error);
    ElMessage.error('设置使用人失败');
  }
}

// 新增树节点命令处理函数
const handleTreeNodeCommand = (command, node) => {
  console.log('树节点操作:', command, node);
  
  switch (command) {
    case 'relate':
      handleRelateAssetsForTree(node);
      break;
    case 'department':
      handleRelateDepartmentForTree(node);
      break;
    case 'manager':
      handleSetManagerForTree(node);
      break;
    case 'users':
      handleSetUsersForTree(node);
      break;
  }
}

// 添加缺失的fetchLocationOptions函数
/**
 * 获取位置下拉选项
 */
const fetchLocationOptions = async () => {
  try {
    const res = await locationApi.getLocationsForDropdown({ includeAll: true });
    if (res.success) {
      locationOptions.value = res.data || [];
    } else {
      console.error('获取位置下拉选项失败:', res.message);
      ElMessage.error('获取位置下拉选项失败');
    }
  } catch (error) {
    console.error('获取位置下拉选项出错:', error);
    ElMessage.error('获取位置下拉选项失败');
  }
};

// 加载位置使用人
const loadLocationUsers = async (locationId) => {
  try {
    const usersRes = await locationApi.getLocationUsers(locationId)
    if (usersRes.success) {
      locationUsersForTree.value = usersRes.data || []
      console.log('加载位置使用人成功:', locationUsersForTree.value.length)
    } else {
      locationUsersForTree.value = []
      console.warn('获取位置使用人返回错误:', usersRes.message)
    }
  } catch (error) {
    console.error('获取位置使用人失败:', error)
    locationUsersForTree.value = []
  }
}

// 获取位置关联的资产
const fetchLocationAssets = async (locationId) => {
  treeAssetLoading.value = true
  try {
    const params = {
      locationId: locationId,
      pageSize: 100,
      pageIndex: 1
    }
    
    console.log('获取位置关联的资产:', locationId)
    const assetsRes = await locationApi.getLocationAssets(params)
    if (assetsRes.success) {
      locationAssetsForTree.value = assetsRes.data.items || []
      console.log('加载位置资产成功:', locationAssetsForTree.value.length)
    } else {
      locationAssetsForTree.value = []
      console.warn('获取位置资产返回错误:', assetsRes.message)
    }
  } catch (error) {
    console.error('获取位置资产失败:', error)
    locationAssetsForTree.value = []
  } finally {
    treeAssetLoading.value = false
  }
}

// 获取资产状态显示样式
const getAssetStatusType = (status) => {
  const statusTypeMap = {
    0: 'info',     // 闲置
    1: 'success',  // 在用
    2: 'warning',  // 维修中
    3: 'danger'    // 报废
  }
  return statusTypeMap[status] || 'info'
}

// 获取资产状态文本
const getAssetStatusText = (status) => {
  const statusTextMap = {
    0: '闲置',
    1: '在用',
    2: '维修中',
    3: '报废'
  }
  return statusTextMap[status] || '未知'
}

// 查看资产详情
const viewAssetDetail = (asset) => {
  console.log('查看资产详情:', asset)
  // 可以根据实际情况实现查看详情的功能
  ElMessageBox.alert(`资产 ${asset.name} 的详细信息`, '资产详情', {
    confirmButtonText: '确定'
  })
}

// 解除资产关联
const handleUnrelateAsset = (asset) => {
  console.log('解除资产关联:', asset)
  ElMessageBox.confirm(
    `确定要解除资产 ${asset.name} 与当前位置的关联吗？`,
    '解除关联',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  )
  .then(async () => {
    try {
      const res = await locationApi.unrelateAssets(
        selectedLocationForTree.value.id, 
        [asset.id]
      )
      if (res.success) {
        ElMessage.success('解除关联成功')
        // 刷新资产列表
        fetchLocationAssets(selectedLocationForTree.value.id)
      } else {
        ElMessage.error(res.message || '解除关联失败')
      }
    } catch (error) {
      console.error('解除关联失败:', error)
      ElMessage.error('解除关联失败')
    }
  })
  .catch(() => {
    // 用户取消操作
  })
}
</script>

<style lang="scss" scoped>
@use "@/styles/variables.scss" as *;

.location-relations-container {
  padding: 20px;
  
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    .page-title {
      margin: 0;
      font-size: 20px;
    }
    
    .page-actions {
      display: flex;
      gap: 10px;
    }
  }
  
  .filter-card {
    margin-bottom: 20px;
    
    .filters {
      .location-view-switch {
        margin-bottom: 15px;
      }
    }
  }
  
  .data-card {
    min-height: calc(100vh - 290px);
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .header-info {
        display: flex;
        align-items: center;
        
        .department-tag {
          margin-left: 10px;
        }
      }
      
      .location-path {
        color: #909399;
        font-size: 14px;
      }
    }
  }
  
  .tree-view-container {
    height: calc(100vh - 290px);
    
    .tree-container {
      .tree-title {
        font-weight: bold;
        margin-bottom: 10px;
        padding: 8px;
        background-color: #f2f6fc;
        border-radius: 4px;
      }
    }
    
    .location-tree-node {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 2px 0;
      width: 100%;
      
      .node-content {
        display: flex;
        align-items: center;
        flex: 1;
        
        .location-name {
          margin-right: 8px;
          font-weight: 500;
        }
        
        .location-type {
          margin-left: 5px;
        }
        
        .location-code {
          margin-left: 5px;
          color: #909399;
          font-size: 12px;
        }
      }
      
      .node-actions {
        margin-left: 10px;
        opacity: 0;
        transition: opacity 0.3s;
      }
      
      &:hover .node-actions {
        opacity: 1;
      }
    }
    
    .location-detail-panel {
      padding: 10px;
      border: 1px solid #ebeef5;
      border-radius: 4px;
      height: 100%;
      
      .detail-header {
        padding-bottom: 15px;
        border-bottom: 1px solid #ebeef5;
        margin-bottom: 15px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        
        h3 {
          margin: 0;
          font-size: 18px;
          flex: 1;
        }
        
        .location-path {
          color: #909399;
          font-size: 13px;
          margin: 5px 0;
          width: 100%;
        }
        
        .location-actions {
          display: flex;
          gap: 10px;
        }
      }
    }
  }
  
  .personnel-info {
    margin-top: 20px;
    
    .info-content {
      line-height: 1.8;
      p {
        margin: 5px 0;
      }
    }
  }
  
  .table-container {
    margin-top: 20px;
    
    .table-toolbar {
      display: flex;
      justify-content: space-between;
      margin-bottom: 15px;
      
      .table-actions {
        display: flex;
        gap: 10px;
      }
    }
  }
  
  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }
  
  .flat-list-table {
    min-height: 400px;
  }
  
  .danger-text {
    color: #f56c6c;
  }
  
  .empty-block {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 300px;
    color: #909399;
    
    .el-icon {
      font-size: 40px;
      margin-bottom: 15px;
    }
  }
}

// 优化树形视图加载性能
:deep(.el-tree-node) {
  .el-tree-node__content {
    height: 36px;
    
    &:hover {
      background-color: #f5f7fa;
    }
  }
  
  &.is-current > .el-tree-node__content {
    background-color: #ecf5ff;
  }
}

// 使用CPU渲染加速
:deep(.el-tree) {
  will-change: transform;
  transform: translateZ(0);
}

:deep(.location-tags) {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  margin: 5px 0;
}
</style> 