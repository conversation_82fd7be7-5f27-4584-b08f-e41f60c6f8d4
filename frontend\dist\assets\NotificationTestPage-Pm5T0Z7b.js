import{_ as a,r as e,x as s,z as l,A as t,b as n,e as i,w as o,E as c,a as u,o as r,d as v,p as d,t as g,$ as f,F as y,h as p,i as m,k as h}from"./index-CG5lHOPO.js";import{n as w}from"./notification-Cv7sgGe-.js";import{n as _}from"./notification-service-w2_wgXl6.js";const S={class:"notification-test-page"},$={class:"card-header"},C={class:"status-section"},k={class:"status-info"},N={class:"test-section"},b={class:"test-controls"},I={class:"log-section"},T={class:"log-header"},R={class:"log-content"},D={class:"log-time"},j={class:"log-type"},x={class:"log-message"},z={key:0,class:"empty-log"},J=a({__name:"NotificationTestPage",setup(a){const J=e(!1),O=e({}),E=e([]),P=e(!1),A=e(!1),F=s(),H=e(0),L=async()=>{try{P.value=!0,B("info","发送测试通知请求...");const a=await w.sendTestNotification();B("success",`测试通知发送成功: ${JSON.stringify(a)}`),c.success("测试通知已发送")}catch(a){B("error",`测试通知发送失败: ${a.message||"未知错误"}`),c.error("发送测试通知失败")}finally{P.value=!1}},U=async()=>{try{P.value=!0,B("info","发送测试事件请求...");const a=await w.sendTestEvent();B("success",`测试事件发送成功: ${JSON.stringify(a)}`),c.success("测试事件已发送")}catch(a){B("error",`测试事件发送失败: ${a.message||"未知错误"}`),c.error("发送测试事件失败")}finally{P.value=!1}},q=async()=>{try{A.value=!0,B("info","检查连接状态...");const e=await w.getConnectionStatus();if(e&&e.success){O.value=e.data,B("success",`连接状态检查成功: ${JSON.stringify(e.data)}`);try{await _.sendTestNotification(H.value),B("info","前端通知服务测试成功")}catch(a){B("warning",`前端通知服务测试失败: ${a.message||"未知错误"}`)}}else B("error",`连接状态检查失败: ${(null==e?void 0:e.message)||"未知错误"}`)}catch(a){O.value={},B("error",`连接状态检查出错: ${a.message||"未知错误"}`),c.error("获取连接状态失败")}finally{A.value=!1}},B=(a,e)=>{E.value.unshift({type:a,message:e,time:new Date}),E.value.length>100&&(E.value=E.value.slice(0,100))},G=()=>{E.value=[],B("info","日志已清空")};return l((async()=>{B("info","通知测试页面已加载"),H.value=F.userId,B("info",`当前用户ID: ${H.value}`),J.value=_.isConnected||!1,B("info","当前SignalR连接状态: "+(J.value?"已连接":"未连接")),await q(),!J.value&&H.value&&await(async()=>{try{B("info","尝试重新连接SignalR..."),await _.disconnect(),await _.initConnection(H.value),J.value=!0,B("success","SignalR重新连接成功")}catch(a){J.value=!1,B("error",`SignalR重新连接失败: ${a.message||"未知错误"}`)}})();const a=setInterval((()=>{J.value=_.isConnected||!1,void 0===J.value&&(J.value=!1)}),5e3);t((()=>{clearInterval(a)}))})),(a,e)=>{const s=u("el-tag"),l=u("el-divider"),t=u("el-button"),c=u("el-card");return r(),n("div",S,[i(c,{class:"test-card"},{header:o((()=>[v("div",$,[e[2]||(e[2]=v("h2",null,"通知系统测试面板",-1)),J.value?(r(),h(s,{key:0,type:"success"},{default:o((()=>e[0]||(e[0]=[d("连接状态: 正常")]))),_:1})):(r(),h(s,{key:1,type:"danger"},{default:o((()=>e[1]||(e[1]=[d("连接状态: 断开")]))),_:1}))])])),default:o((()=>[v("div",C,[e[8]||(e[8]=v("h3",null,"连接信息",-1)),v("div",k,[v("p",null,[e[3]||(e[3]=v("strong",null,"SignalR状态:",-1)),d(" "+g(J.value?"已连接":"未连接"),1)]),v("p",null,[e[4]||(e[4]=v("strong",null,"服务器时间:",-1)),d(" "+g(O.value.serverTime||"未知"),1)]),v("p",null,[e[5]||(e[5]=v("strong",null,"Hub路径:",-1)),d(" "+g(O.value.hubPath||"/hubs/notification"),1)]),v("p",null,[e[6]||(e[6]=v("strong",null,"消息:",-1)),d(" "+g(O.value.message||"未知"),1)]),v("p",null,[e[7]||(e[7]=v("strong",null,"用户ID:",-1)),d(" "+g(H.value),1)])])]),i(l),v("div",N,[e[12]||(e[12]=v("h3",null,"测试发送通知",-1)),v("div",b,[i(t,{type:"primary",onClick:L,loading:P.value},{default:o((()=>e[9]||(e[9]=[d(" 发送测试通知 ")]))),_:1},8,["loading"]),i(t,{type:"warning",onClick:U,loading:P.value},{default:o((()=>e[10]||(e[10]=[d(" 发送测试事件 ")]))),_:1},8,["loading"]),i(t,{type:"info",onClick:q,loading:A.value},{default:o((()=>e[11]||(e[11]=[d(" 检查连接状态 ")]))),_:1},8,["loading"])])]),i(l),v("div",I,[v("div",T,[e[14]||(e[14]=v("h3",null,"事件日志",-1)),i(t,{type:"default",size:"small",onClick:G},{default:o((()=>e[13]||(e[13]=[d("清空日志")]))),_:1})]),v("div",R,[(r(!0),n(y,null,p(E.value,((a,e)=>{return r(),n("div",{key:e,class:m(["log-item",a.type])},[v("span",D,g((s=a.time,s.toLocaleTimeString("zh-CN",{hour12:!1,hour:"2-digit",minute:"2-digit",second:"2-digit",fractionalSecondDigits:3}))),1),v("span",j,"["+g(a.type.toUpperCase())+"]",1),v("span",x,g(a.message),1)],2);var s})),128)),0===E.value.length?(r(),n("div",z," 暂无日志记录 ")):f("",!0)])])])),_:1})])}}},[["__scopeId","data-v-466c2adf"]]);export{J as default};
