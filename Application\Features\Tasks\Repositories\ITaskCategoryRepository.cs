using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using ItAssetsSystem.Domain.Entities.Tasks;
using ItAssetsSystem.Application.Features.Tasks.Dtos;

namespace ItAssetsSystem.Application.Features.Tasks.Repositories
{
    /// <summary>
    /// 任务分类仓储接口
    /// </summary>
    public interface ITaskCategoryRepository
    {
        /// <summary>
        /// 获取所有任务分类
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>任务分类列表</returns>
        Task<List<TaskCategory>> GetAllAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// 获取启用的任务分类
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>启用的任务分类列表</returns>
        Task<List<TaskCategory>> GetActiveAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// 根据ID获取任务分类
        /// </summary>
        /// <param name="categoryId">分类ID</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>任务分类</returns>
        Task<TaskCategory?> GetByIdAsync(int categoryId, CancellationToken cancellationToken = default);

        /// <summary>
        /// 根据名称获取任务分类
        /// </summary>
        /// <param name="name">分类名称</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>任务分类</returns>
        Task<TaskCategory?> GetByNameAsync(string name, CancellationToken cancellationToken = default);

        /// <summary>
        /// 分页查询任务分类
        /// </summary>
        /// <param name="request">查询请求</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>分页结果</returns>
        Task<(List<TaskCategory> Items, int TotalCount)> GetPagedAsync(TaskCategoryQueryRequestDto request, CancellationToken cancellationToken = default);

        /// <summary>
        /// 创建任务分类
        /// </summary>
        /// <param name="category">任务分类</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>创建的任务分类</returns>
        Task<TaskCategory> CreateAsync(TaskCategory category, CancellationToken cancellationToken = default);

        /// <summary>
        /// 更新任务分类
        /// </summary>
        /// <param name="category">任务分类</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>更新的任务分类</returns>
        Task<TaskCategory> UpdateAsync(TaskCategory category, CancellationToken cancellationToken = default);

        /// <summary>
        /// 删除任务分类
        /// </summary>
        /// <param name="categoryId">分类ID</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>是否删除成功</returns>
        Task<bool> DeleteAsync(int categoryId, CancellationToken cancellationToken = default);

        /// <summary>
        /// 检查分类是否存在
        /// </summary>
        /// <param name="categoryId">分类ID</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>是否存在</returns>
        Task<bool> ExistsAsync(int categoryId, CancellationToken cancellationToken = default);

        /// <summary>
        /// 检查分类名称是否已存在
        /// </summary>
        /// <param name="name">分类名称</param>
        /// <param name="excludeCategoryId">排除的分类ID（用于更新时检查）</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>是否已存在</returns>
        Task<bool> NameExistsAsync(string name, int? excludeCategoryId = null, CancellationToken cancellationToken = default);

        /// <summary>
        /// 获取分类下的任务数量
        /// </summary>
        /// <param name="categoryId">分类ID</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>任务数量</returns>
        Task<int> GetTaskCountAsync(int categoryId, CancellationToken cancellationToken = default);
    }
}
