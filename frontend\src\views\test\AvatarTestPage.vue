// File: frontend/src/views/test/AvatarTestPage.vue
// Description: A simple page to test displaying the current user's avatar.

<template>
  <div class="avatar-test-page">
    <h1>用户头像测试页面</h1>
    <p>此页面专门用于测试用户头像的显示功能。</p>
    
    <div v-if="userStore.pending">
      <p>正在加载用户信息...</p>
    </div>
    
    <div v-else-if="!userStore.isLogin">
      <p>用户未登录，无法显示头像。</p>
    </div>
    
    <div v-else>
      <h2>头像预览:</h2>
      <el-avatar :size="150" :src="avatarUrlToDisplay" class="test-avatar">
        <img v-if="!avatarUrlToDisplay" src="https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png" />
      </el-avatar>
      
      <div class="debug-info">
        <h3>调试信息:</h3>
        <p><code>userStore.userInfo.value?.avatar</code>: {{ userStore.userInfo.value?.avatar }}</p>
        <p><code>userStore.avatar.value</code>: {{ userStore.avatar.value }}</p>
        <p><code>userStore.computedAvatarUrl</code> (直接从 store 获取): {{ userStore.computedAvatarUrl }}</p>
        <p><code>avatarUrlToDisplay</code> (在组件中计算得到): {{ avatarUrlToDisplay }}</p>
        <p><code>VITE_STATIC_FILES_BASE_URL</code>: {{ viteStaticBaseUrl }} </p>
        <p><code>document.location.origin</code>: {{ currentOrigin }}</p>
      </div>
    </div>
    <el-button @click="forceRefreshAvatarInfo" style="margin-top: 20px;">强制刷新Store信息</el-button>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useUserStore } from '@/stores/modules/user'
import { ElAvatar, ElButton } from 'element-plus'

console.log('[AvatarTestPage] Component setup initiated.')

const userStore = useUserStore()
const avatarUrlToDisplay = ref('')
const viteStaticBaseUrl = ref(import.meta.env.VITE_STATIC_FILES_BASE_URL)
const currentOrigin = ref(document.location.origin)

const updateAvatarUrl = () => {
  const urlFromStore = userStore.computedAvatarUrl;
  console.log('[AvatarTestPage] updateAvatarUrl called. URL from store:', urlFromStore);
  avatarUrlToDisplay.value = urlFromStore;
}

// Watch for changes in the computed property from the store
// This is crucial if the store's value might change after initial load
watch(() => userStore.computedAvatarUrl, (newUrl) => {
  console.log('[AvatarTestPage] Watched userStore.computedAvatarUrl changed to:', newUrl);
  avatarUrlToDisplay.value = newUrl;
}, { immediate: true }); // immediate: true ensures it runs on component mount

onMounted(async () => {
  console.log('[AvatarTestPage] Component mounted.')
  if (!userStore.isLogin && userStore.token) {
    console.log('[AvatarTestPage] User not logged in but token exists, attempting to fetch user info.')
    try {
      await userStore.getInfo(); // Ensure user info is loaded if not already
      console.log('[AvatarTestPage] User info fetched in onMounted.');
    } catch (error) {
      console.error('[AvatarTestPage] Error fetching user info in onMounted:', error);
    }
  } else {
    console.log('[AvatarTestPage] User already logged in or no token.');
  }
  // Initial update, though the watcher with immediate:true should also cover this
  updateAvatarUrl(); 
});

const forceRefreshAvatarInfo = () => {
  console.log('[AvatarTestPage] Forcing refresh of avatar info.');
  // Re-log critical values
  console.log('[AvatarTestPage] userStore.userInfo.value?.avatar:', userStore.userInfo.value?.avatar);
  console.log('[AvatarTestPage] userStore.avatar.value:', userStore.avatar.value);
  // This will trigger the watcher if the value changes or re-evaluate the computed prop
  updateAvatarUrl();
}

console.log('[AvatarTestPage] Initial userStore.isLogin:', userStore.isLogin);
console.log('[AvatarTestPage] Initial userStore.token:', userStore.token ? 'Token exists' : 'No token');
console.log('[AvatarTestPage] Initial userStore.userInfo.value?.avatar:', userStore.userInfo.value?.avatar);
console.log('[AvatarTestPage] Initial userStore.avatar.value:', userStore.avatar.value);
console.log('[AvatarTestPage] Initial userStore.computedAvatarUrl (direct from store):', userStore.computedAvatarUrl);
</script>

<style scoped>
.avatar-test-page {
  padding: 20px;
  font-family: sans-serif;
}
.test-avatar {
  border: 2px solid #eee;
  margin-top: 10px;
  margin-bottom: 20px;
}
.debug-info {
  margin-top: 20px;
  padding: 15px;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 5px;
}
.debug-info p {
  margin: 5px 0;
  word-break: break-all;
}
code {
  background-color: #e0e0e0;
  padding: 2px 4px;
  border-radius: 3px;
}
</style> 