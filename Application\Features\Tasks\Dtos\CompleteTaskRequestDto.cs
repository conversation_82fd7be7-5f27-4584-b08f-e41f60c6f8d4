#nullable enable
// File: Application/Features/Tasks/Dtos/CompleteTaskRequestDto.cs
// Description: DTO for completing a task, allowing for remarks.


using System.ComponentModel.DataAnnotations;

namespace ItAssetsSystem.Application.Features.Tasks.Dtos
{
    public class CompleteTaskRequestDto
    {
        [StringLength(1000, ErrorMessage = "备注长度不能超过1000个字符")]
        public string? Remarks { get; set; }
    }
} 