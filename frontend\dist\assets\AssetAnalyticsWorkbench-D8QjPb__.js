import{aV as e,_ as a,r as t,ad as l,c as s,z as n,ax as r,j as i,E as u,A as o,b as c,d,e as v,w as p,F as m,h as f,$ as g,t as b,i as h,a as y,m as k,o as C,f as x,s as S,k as D,aZ as w,a_ as $,p as R,aK as z,I as A,n as _,l as M,aL as T,v as V,a9 as L}from"./index-CG5lHOPO.js";import"./index-DYKgPQG6.js";import{s as N}from"./statistics-BoWGSZxY.js";import{i as O}from"./install-BB2cVj0s.js";const F="/v2/asset-statistics",I={getAnalyticsWorkbench:()=>e({url:`${F}/analytics-workbench`,method:"get"}),getTypeStatistics:(a={})=>e({url:`${F}/type-statistics`,method:"get",params:a}),getRegionStatistics:(a={})=>e({url:`${F}/region-statistics`,method:"get",params:a}),getDepartmentStatistics:(a={})=>e({url:`${F}/department-statistics`,method:"get",params:a}),getAssetTypes:()=>e({url:`${F}/asset-types`,method:"get"}),getRegions:()=>e({url:`${F}/regions`,method:"get"}),getDepartments:()=>e({url:`${F}/departments`,method:"get"}),getWeeklyTrend:(a={})=>e({url:`${F}/weekly-trend`,method:"get",params:a}),getDailyTrend:(a={})=>e({url:`${F}/daily-trend`,method:"get",params:a}),getMonthlyTrend:(a={})=>e({url:`${F}/monthly-trend`,method:"get",params:a}),getCombinedStatistics:(a={})=>e({url:`${F}/combined-statistics`,method:"get",params:a})};function P(e,a=!0){const t=a?[{value:0,color:"rgba(0, 32, 96, 0.6)"},{value:25,color:"rgba(0, 64, 128, 0.7)"},{value:50,color:"rgba(0, 128, 192, 0.8)"},{value:75,color:"rgba(0, 192, 255, 0.9)"},{value:100,color:"rgba(64, 224, 255, 1.0)"}]:[{value:0,color:"rgba(240, 249, 255, 0.8)"},{value:25,color:"rgba(189, 229, 255, 0.8)"},{value:50,color:"rgba(107, 174, 214, 0.8)"},{value:75,color:"rgba(49, 130, 189, 0.8)"},{value:100,color:"rgba(8, 81, 156, 0.8)"}];for(let l=1;l<t.length;l++)if(e<=t[l].value){const a=t[l-1],s=t[l],n=s.value-a.value,r=e-a.value,i=0===n?0:r/n,u=a.color.match(/rgba\((\d+),\s*(\d+),\s*(\d+),\s*([\d.]+)\)/),o=s.color.match(/rgba\((\d+),\s*(\d+),\s*(\d+),\s*([\d.]+)\)/);if(!u||!o)return a.color;return`rgba(${Math.round(parseInt(u[1])+i*(parseInt(o[1])-parseInt(u[1])))}, ${Math.round(parseInt(u[2])+i*(parseInt(o[2])-parseInt(u[2])))}, ${Math.round(parseInt(u[3])+i*(parseInt(o[3])-parseInt(u[3])))}, ${(parseFloat(u[4])+i*(parseFloat(o[4])-parseFloat(u[4]))).toFixed(2)})`}return t[t.length-1].color}const U={class:"control-panel"},j={class:"panel-header"},W={class:"logo-container"},B={class:"filters-panel"},E={class:"filter-group"},J={class:"filter-group"},X={class:"filter-group"},q={class:"checkbox-group"},K={class:"filter-group"},Q={class:"filter-group"},Z={class:"main-content"},G={class:"content-header"},H={class:"dashboard-title"},Y={class:"dashboard-subtitle"},ee={class:"kpi-section"},ae={class:"kpi-container"},te={class:"kpi-content"},le={class:"kpi-main"},se={class:"kpi-label"},ne={class:"kpi-value-container"},re={key:0,class:"kpi-unit"},ie={key:0,class:"kpi-trend"},ue={class:"trend-text"},oe={key:0,class:"kpi-chart"},ce={key:0,class:"debug-section"},de={class:"debug-header"},ve={class:"debug-content"},pe={class:"grid-layout"},me={class:"left-column"},fe={class:"card-header"},ge={class:"header-actions"},be={class:"factory-map-wide factory-map-compact"},he={key:0,class:"area-info"},ye={class:"area-stats"},ke={class:"stat-row"},Ce={class:"stat-value"},xe={class:"stat-row"},Se={class:"stat-value"},De={class:"stat-row"},we={class:"stat-value"},$e={class:"stat-row"},Re={class:"stat-value"},ze={class:"right-column"},Ae={class:"asset-type-content asset-type-compact"},_e={class:"asset-type-legend"},Me={class:"legend-label"},Te={class:"legend-value"},Ve={class:"chart-header"},Le={class:"chart-title"},Ne={class:"chart-actions"},Oe={key:0,class:"drill-controls"},Fe={class:"chart-container"},Ie={class:"grid-layout second-row"},Pe={class:"chart-container"},Ue={class:"chart-container"},je={class:"table-section"},We={class:"table-header"},Be={class:"table-summary"},Ee={class:"summary-text"},Je={key:0,class:"empty-data"},Xe={class:"pagination-container"},qe=a({__name:"AssetAnalyticsWorkbench",setup(e){const a=t(null),F=t(null),qe=t(null),Ke=t(null);t(null);const Qe=t(null),Ze=t(null),Ge=t(null),He=t(null),Ye=t(null),ea=t(null),aa=t(null),ta=t(!1),la=t(!1),sa=t("bar"),na=t(!1),ra=t(!0),ia=t("all"),ua=t(null),oa=l({dimension:"department",metric:"count",filters:{},dateRange:null,pagination:{page:1,size:20}}),ca=t(["在用","闲置","维修中","报废"]),da=t([]),va=t([{key:"department",name:"部门"},{key:"location",name:"位置"},{key:"type",name:"资产类型"},{key:"status",name:"状态"}]),pa=t([{key:"count",name:"数量"},{key:"totalValue",name:"总价值"},{key:"averageValue",name:"平均价值"}]),ma=t(["在用","闲置","维修中","报废"]),fa=t(["技术部","销售部","市场部","行政部","财务部"]),ga=t(null),ba=t(null),ha=t([]),ya=t([]),ka=t([]),Ca=l({currentPage:1,pageSize:20,total:0}),xa=s((()=>oa.filters.department?`${oa.filters.department} - 资产洞察`:"资产全局洞察")),Sa=s((()=>oa.filters.department?`当前已筛选"部门"为"${oa.filters.department}"`:"当前展示所有资产的概览。您可以在左侧面板进行筛选和维度切换。")),Da=s((()=>{var e,a;return`按${(null==(e=va.value.find((e=>e.key===oa.dimension)))?void 0:e.name)||"未知维度"}分析 - ${(null==(a=pa.value.find((e=>e.key===oa.metric)))?void 0:a.name)||"未知指标"}`}));s((()=>{var e;if(!(null==(e=ba.value)?void 0:e.kpiData))return La();const a=ba.value.kpiData;return[{label:"资产总数",value:(a.totalAssets||0).toLocaleString(),valueClass:"kpi-value-default"},{label:"资产总值",value:(a.totalValue||0).toFixed(2),unit:"万",valueClass:"kpi-value-default"},{label:"在用率",value:`${(a.onlineRate||0).toFixed(1)}%`,valueClass:"kpi-value-success"},{label:"维修中",value:a.faultCount||0,valueClass:"kpi-value-warning"}]})),s((()=>{var e;if(!(null==(e=ba.value)?void 0:e.kpiData))return{onlineRate:0,idleRate:0,maintenanceRate:0,averageUtilization:0};const a=ba.value.kpiData;return{onlineRate:(a.onlineRate||0).toFixed(1),idleRate:(a.idleRate||0).toFixed(1),maintenanceRate:(a.maintenanceRate||0).toFixed(1),averageUtilization:(a.averageUtilization||0).toFixed(1)}}));const wa=s((()=>{var e;if(!(null==(e=ba.value)?void 0:e.kpiData))return za();const a=ba.value.kpiData;return[{label:"总资产数",value:(a.totalAssets||0).toLocaleString(),valueClass:"kpi-value-blue",cardClass:"kpi-card-blue",trend:a.totalTrend||0,chartData:$a("line"),chartRef:null},{label:"故障数量",value:(a.faultCount||0).toLocaleString(),valueClass:"kpi-value-green",cardClass:"kpi-card-green",trend:a.faultTrend||0,chartData:$a("area"),chartRef:null},{label:"在线率",value:(a.onlineRate||0)+"%",valueClass:"kpi-value-orange",cardClass:"kpi-card-orange",trend:a.onlineRateTrend||0,chartData:$a("bar"),chartRef:null},{label:"维修中",value:(a.maintenanceCount||0).toLocaleString(),valueClass:"kpi-value-purple",cardClass:"kpi-card-purple",trend:a.maintenanceTrend||0,chartData:$a("line"),chartRef:null}]})),$a=e=>{const a=[120,132,101,134,90,230,210];switch(e){case"line":return a.map(((e,a)=>e+20*Math.sin(a)));case"area":return a.map(((e,a)=>.8*e+15*Math.cos(a)));case"bar":return a.map(((e,a)=>1.1*e+a%2*10));case"pie":return a.map(((e,a)=>.9*e+30*Math.random()));default:return a}},Ra=s((()=>{var e;return(null==(e=ba.value)?void 0:e.typeStatistics)&&ba.value.typeStatistics.length>0?ba.value.typeStatistics.map(((e,a)=>({name:e.assetTypeName,percentage:parseFloat(e.percentage),color:["#5470c6","#91cc75","#fac858","#ee6666","#73c0de"][a%5]}))):[{name:"计算机设备",percentage:45.2,color:"#5470c6"},{name:"网络设备",percentage:25.8,color:"#91cc75"},{name:"服务器",percentage:15.3,color:"#fac858"},{name:"打印机",percentage:8.9,color:"#ee6666"},{name:"其他设备",percentage:4.8,color:"#73c0de"}]}));s((()=>{var e;return(null==(e=ba.value)?void 0:e.statusDistribution)?ba.value.statusDistribution.statusLabels.map(((e,a)=>({name:e,percentage:parseFloat((ba.value.statusDistribution.statusCounts[a]/ba.value.statusDistribution.statusCounts.reduce(((e,a)=>e+a),0)*100).toFixed(1)),color:["#5470c6","#91cc75","#fac858","#ee6666","#73c0de"][a%5]}))):[{name:"在用",percentage:78.5,color:"#5470c6"},{name:"闲置",percentage:12.3,color:"#91cc75"},{name:"维修中",percentage:6.8,color:"#fac858"},{name:"报废",percentage:2.4,color:"#ee6666"}]}));const za=()=>[{label:"总资产数",value:"0",valueClass:"kpi-value-blue",cardClass:"kpi-card-blue",trend:0,chartData:$a("line"),chartRef:null},{label:"故障数量",value:"0",valueClass:"kpi-value-green",cardClass:"kpi-card-green",trend:0,chartData:$a("area"),chartRef:null},{label:"在线率",value:"0%",valueClass:"kpi-value-orange",cardClass:"kpi-card-orange",trend:0,chartData:$a("bar"),chartRef:null},{label:"维修中",value:"0",valueClass:"kpi-value-purple",cardClass:"kpi-card-purple",trend:0,chartData:$a("line"),chartRef:null}],Aa=s((()=>{var e;if(!(null==(e=ba.value)?void 0:e.departmentStatistics))return[];return ba.value.departmentStatistics.map((e=>({value:e.departmentId,label:e.departmentName})))})),_a=()=>{var e,a,t;if("all"===ia.value){return(null==(a=null==(e=ba.value)?void 0:e.departmentStatistics)?void 0:a.reduce(((e,a)=>e+(Number(a.assetCount)||0)),0))||0}return Number(null==(t=ua.value)?void 0:t.assetCount)||0},Ma=()=>{var e,a,t;if("all"===ia.value){return(null==(a=null==(e=ba.value)?void 0:e.departmentStatistics)?void 0:a.reduce(((e,a)=>e+(Number(a.normalCount)||0)),0))||0}return Number(null==(t=ua.value)?void 0:t.normalCount)||0},Ta=()=>{const e=_a(),a=Ma();if(0===e)return"0.0";return(a/e*100).toFixed(1)},Va=()=>{var e,a,t,l;return"all"===ia.value?(null==(a=null==(e=ba.value)?void 0:e.departmentStatistics)?void 0:a.reduce(((e,a)=>e+(a.faultCount||0)),0))||0:(null==(l=null==(t=ua.value)?void 0:t.assetTypes)?void 0:l["故障设备"])||0},La=()=>[{label:"资产总数",value:"0",valueClass:"kpi-value-default"},{label:"资产总值",value:"0.00",unit:"万",valueClass:"kpi-value-default"},{label:"在用率",value:"0.0%",valueClass:"kpi-value-success"},{label:"维修中",value:"0",valueClass:"kpi-value-warning"}];n((async()=>{await(async()=>{try{const e=await N.getDimensions();e.data&&e.data.success&&(va.value=e.data.data||[])}catch(e){va.value=[{key:"department",name:"部门"},{key:"location",name:"位置"},{key:"type",name:"资产类型"},{key:"status",name:"状态"}]}})(),await(async()=>{try{const e=await N.getMetrics();e.data&&e.data.success&&(pa.value=e.data.data||[])}catch(e){pa.value=[{key:"count",name:"数量"},{key:"totalValue",name:"总价值"},{key:"averageValue",name:"平均价值"}]}})(),await r(),Fa(),it(),ut(),ct(),yt(),kt(),await Na(),await Oa(),r((()=>{var e,a,t,l;if((null==(e=ba.value)?void 0:e.departmentStatistics)&&it(),He.value&&(null==(a=ba.value)?void 0:a.valueDistribution)&&gt(ba.value.valueDistribution),Ye.value&&((null==(t=ba.value)?void 0:t.matrixData)||(null==(l=ba.value)?void 0:l.departmentAssetMatrix))){const e=ba.value.matrixData||ba.value.departmentAssetMatrix;bt(e)}})),document.body.classList.toggle("dark-theme",ra.value),document.body.classList.toggle("light-theme",!ra.value),window.addEventListener("resize",wt,{passive:!0})})),i((()=>[oa.dimension,oa.metric,ca.value]),(()=>{Oa()}),{deep:!0});const Na=async()=>{try{ta.value=!0,la.value=!0;const e=await I.getAnalyticsWorkbench();if(e.success&&e.data){const a=e.data;ba.value=a,vt(a.kpiData),pt(a.filterOptions),mt(a),xt(a.departmentStatistics),await Oa(),it(),r((()=>{yt()})),u.success("数据加载完成")}else u.error(e.message||"数据加载失败")}catch(e){u.error("数据加载失败: "+e.message)}finally{ta.value=!1,la.value=!1}},Oa=async()=>{var e,a,t;try{ta.value=!0,la.value=!0;const l={dimension:oa.dimension,metric:oa.metric,filters:{...oa.filters,statuses:ca.value},dateRange:2===da.value.length?{startDate:da.value[0],endDate:da.value[1],field:"CreatedAt"}:null,pagination:{page:Ca.currentPage,size:Ca.pageSize}},s=await N.executeQuery(l);s&&s.success?(ga.value=s.data,null==(e=ga.value)||e.detailedData,Wa(),(null==(a=ga.value)?void 0:a.pagination)&&(Ca.total=ga.value.pagination.totalCount),Ia()):(u.error("查询结果格式不正确"),(null==(t=ba.value)?void 0:t.departmentStatistics)&&xt(ba.value.departmentStatistics))}catch(l){u.error("查询执行失败")}finally{ta.value=!1,la.value=!1}},Fa=()=>{try{if(Ze.value&&(Ze.value.dispose(),Ze.value=null),!a.value)return;Ze.value=O(a.value,null,{renderer:"canvas",useDirtyRect:!0,useCoarsePointer:!0,pointerSize:20}),Ze.value.on("click",(e=>{Ka(e)}))}catch(e){Ze.value=null}},Ia=()=>{var e;try{if(!Ze.value||Ze.value.isDisposed())return void Fa();if(!(null==(e=ga.value)?void 0:e.aggregatedData))return;const a=ga.value.aggregatedData;if(!Array.isArray(a)||0===a.length)return;const t=a.map((e=>e.value||"未知")),l=a.map((e=>e.metricValue||0));let s={};switch(sa.value){case"bar":default:s=Pa(t,l);break;case"pie":s=Ua(a);break;case"line":s=ja(t,l)}if(!s||"object"!=typeof s)return;Ze.value.setOption(s,!0)}catch(a){setTimeout((()=>{var e;Fa(),(null==(e=ga.value)?void 0:e.aggregatedData)&&Ia()}),100)}},Pa=(e,a)=>{try{return Array.isArray(e)&&Array.isArray(a)?0===e.length||0===a.length?{title:{text:"暂无数据",left:"center",top:"center",textStyle:{color:"#999"}}}:{tooltip:{trigger:"axis",backgroundColor:"#fff",borderColor:"#e5e7eb",borderWidth:1,textStyle:{color:"#1f2937"},formatter:e=>{if(!e||!e[0])return"";const a=e[0];return`${a.name}<br/>${a.seriesName||"数值"}: ${a.value}<br/><span style="color: #6b7280; font-size: 12px;">点击可下钻分析</span>`}},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"category",data:e,axisLine:{lineStyle:{color:"#e5e7eb"}},axisTick:{lineStyle:{color:"#e5e7eb"}},axisLabel:{color:"#6b7280"}},yAxis:{type:"value",axisLine:{lineStyle:{color:"#e5e7eb"}},axisTick:{lineStyle:{color:"#e5e7eb"}},axisLabel:{color:"#6b7280"},splitLine:{lineStyle:{color:"#f3f4f6"}}},series:[{name:"数量",type:"bar",data:a,itemStyle:{color:"#3b82f6",borderRadius:[4,4,0,0]},emphasis:{itemStyle:{color:"#2563eb",shadowBlur:10,shadowColor:"rgba(59, 130, 246, 0.3)"}},cursor:"pointer"}]}:null}catch(t){return null}},Ua=e=>{try{if(!Array.isArray(e))return null;if(0===e.length)return{title:{text:"暂无数据",left:"center",top:"center",textStyle:{color:"#999"}}};const a=e.filter((e=>e&&"object"==typeof e&&void 0!==e.metricValue&&null!==e.metricValue&&void 0!==e.value&&null!==e.value));return 0===a.length?{title:{text:"暂无有效数据",left:"center",top:"center",textStyle:{color:"#999"}}}:{tooltip:{trigger:"item",formatter:e=>e?`${e.name}<br/>${e.seriesName||"数值"}: ${e.value} (${e.percent}%)<br/><span style="color: #6b7280; font-size: 12px;">点击可下钻分析</span>`:"",backgroundColor:"#fff",borderColor:"#e5e7eb",borderWidth:1,textStyle:{color:"#1f2937"}},legend:{orient:"vertical",left:"left",textStyle:{color:"#6b7280"}},series:[{name:Da.value||"统计",type:"pie",radius:"50%",center:["60%","50%"],data:a.map((e=>({value:Number(e.metricValue)||0,name:String(e.value)||"未知"}))),emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}},cursor:"pointer",itemStyle:{borderWidth:2,borderColor:"#fff"}}]}}catch(a){return null}},ja=(e,a)=>({tooltip:{trigger:"axis"},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"category",data:e},yAxis:{type:"value"},series:[{type:"line",data:a,smooth:!0,lineStyle:{color:"#3b82f6"},itemStyle:{color:"#3b82f6"}}]}),Wa=()=>{var e,a;(null==(e=ga.value)?void 0:e.detailedData)?(ha.value=ga.value.detailedData,(null==(a=ga.value)?void 0:a.pagination)&&(Ca.total=ga.value.pagination.totalCount||ha.value.length)):ha.value=[]},Ba=()=>{Oa()},Ea=()=>{Oa()},Ja=()=>{Oa()},Xa=()=>{Oa()},qa=()=>{oa.dateRange=da.value,Oa()},Ka=e=>{const a=e.name,t=oa.dimension;ka.value.push({dimension:t,value:a,filters:{...oa.filters}}),oa.filters[t]=a,Qa(t),Oa(),u.success(`已下钻至 ${Za(t)}: ${a}`)},Qa=e=>{const a={department:"assetType",assetType:"location",location:"status",status:"department"}[e];a&&(oa.dimension=a)},Za=e=>({department:"部门",assetType:"资产类型",location:"区域",status:"状态"}[e]||e),Ga=()=>{if(0===ka.value.length)return;const e=ka.value.pop();oa.dimension=e.dimension,oa.filters={...e.filters},Oa(),u.info("已返回上一级")},Ha=()=>{ka.value=[],oa.filters={},oa.dimension="department",Oa()},Ya=e=>{},et=e=>{sa.value=e,Ia()},at=e=>{Ca.pageSize=e,oa.pagination.size=e,Oa()},tt=e=>{Ca.currentPage=e,oa.pagination.page=e,Oa()},lt=()=>{oa.dimension="department",oa.metric="count",oa.filters={},ca.value=["在用","闲置","维修中","报废"],da.value=[],Ca.currentPage=1,Ca.pageSize=20,Oa(),u.success("筛选条件已重置，资产列表已更新")},st=()=>{L.confirm("确定要导出当前数据吗？","确认导出",{confirmButtonText:"确定",cancelButtonText:"取消",type:"info"}).then((()=>{u.success("数据导出功能开发中...")})).catch((()=>{}))},nt=()=>{ra.value=!ra.value,document.body.classList.toggle("dark-theme",ra.value),document.body.classList.toggle("light-theme",!ra.value),r((()=>{var e,a,t,l,s;Ge.value&&(Ge.value.dispose(),Ge.value=null,it()),aa.value&&(aa.value.dispose(),aa.value=null,yt()),Ze.value&&(Ze.value.dispose(),Ze.value=null,Fa(),(null==(e=ga.value)?void 0:e.aggregatedData)&&Ia()),He.value&&(He.value.dispose(),He.value=null,ut(),(null==(a=ua.value)?void 0:a.valueDistribution)?ot(ua.value.valueDistribution):(null==(t=ba.value)?void 0:t.valueDistribution)&&gt(ba.value.valueDistribution)),Ye.value&&(Ye.value.dispose(),Ye.value=null,ct(),(null==(l=ba.value)?void 0:l.departmentAssetMatrix)?bt(ba.value.departmentAssetMatrix):(null==(s=ba.value)?void 0:s.matrixData)&&bt(ba.value.matrixData))}))},rt=()=>{ua.value=null,it(),ut()},it=()=>{if(!F.value)return;Ge.value=O(F.value,ra.value?"dark":null,{renderer:"canvas",useDirtyRect:!0,useCoarsePointer:!0,pointerSize:20});const e=(()=>{var e;if(!(null==(e=ba.value)?void 0:e.departmentStatistics))return null;let a=ba.value.departmentStatistics;if(!a||0===a.length)return null;ia.value&&"all"!==ia.value&&(a=a.filter((e=>e.departmentId===ia.value)));const t=(e,a)=>{const t=Math.min(Math.ceil(Math.sqrt(a)),5),l=Math.ceil(a/t),s=Math.floor(e/t),n=e%t,r=Math.max(130,Math.min(200,530/t)),i=Math.max(100,Math.min(150,590/l)),u=15+n*(r+8),o=10+s*(i+6);return[[u,o],[u+r,o],[u+r,o+i],[u,o+i]]},l=Math.max(...a.map((e=>e.normalCount||0)));return a.map(((e,s)=>({name:e.departmentName||`部门${e.departmentId}`,coords:t(s,a.length),value:l>0?Math.ceil((e.normalCount||0)/l*100):0,assetCount:e.assetCount||0,normalCount:e.normalCount||0,onlineRate:Number((e.normalRate||0).toFixed(1)),utilizationRate:Math.max(0,Number((e.normalRate||0).toFixed(1))-10*Math.random()),assetTypes:{"部门总资产":e.assetCount||0,"在用设备":e.normalCount||0,"故障设备":e.faultCount||0,"维护设备":e.maintenanceCount||0},valueDistribution:[{range:"0-10万",count:Math.ceil(.2*(e.assetCount||0))},{range:"10-50万",count:Math.ceil(.5*(e.assetCount||0))},{range:"50-100万",count:Math.ceil(.2*(e.assetCount||0))},{range:"100万以上",count:Math.ceil(.1*(e.assetCount||0))}],maintenanceStatus:{"在用":e.normalCount||0,"故障":e.faultCount||0,"维护中":e.maintenanceCount||0}})))})()||[{name:"暂无数据",coords:[[50,50],[200,50],[200,150],[50,150]],value:0,assetCount:0,normalCount:0,onlineRate:0,utilizationRate:0,assetTypes:{"部门总资产":0,"在用设备":0,"故障设备":0,"维护设备":0},valueDistribution:[],maintenanceStatus:{"在用":0,"故障":0,"维护中":0}}],a=e.map((e=>({name:e.name,value:e.value,coords:e.coords,assetCount:e.assetCount,normalCount:e.normalCount,onlineRate:e.onlineRate,utilizationRate:e.utilizationRate,assetTypes:e.assetTypes,valueDistribution:e.valueDistribution,maintenanceStatus:e.maintenanceStatus}))),t={backgroundColor:"transparent",tooltip:{trigger:"item",formatter:e=>{var a,t;const l=e.data;return`\n          <div class="tooltip-content">\n            <div class="tooltip-title">${e.name}</div>\n            <div>总资产数: ${l.assetCount}台</div>\n            <div>在用资产: ${l.normalCount}台</div>\n            <div>故障设备: ${(null==(a=l.assetTypes)?void 0:a["故障设备"])||0}台</div>\n            <div>维护设备: ${(null==(t=l.assetTypes)?void 0:t["维护设备"])||0}台</div>\n            <div>在用率: ${l.onlineRate}%</div>\n          </div>\n        `}},series:[{type:"custom",coordinateSystem:"none",renderItem:(e,t)=>{const l=e.dataIndex,s=a[l];if(!s)return null;const n=s.coords;if(!n||!n.length)return null;return{type:"polygon",shape:{points:n.map((e=>[e[0],e[1]]))},style:{fill:P?P(s.value,ra.value):(e=>{const a=Math.max(0,Math.min(100,e||0))/100;return 0===a?"rgba(200, 200, 200, 0.3)":a<.2?`rgba(59, 130, 246, ${.3+.4*a})`:a<.4?`rgba(34, 197, 94, ${.4+.3*a})`:a<.6?`rgba(251, 191, 36, ${.5+.3*a})`:a<.8?`rgba(249, 115, 22, ${.6+.3*a})`:`rgba(239, 68, 68, ${.7+.3*a})`})(s.value),stroke:ra.value?"rgba(255, 255, 255, 0.5)":"rgba(0, 0, 0, 0.5)",lineWidth:1},emphasis:{style:{fill:ra.value?"rgba(0, 255, 255, 0.8)":"rgba(24, 144, 255, 0.8)",stroke:ra.value?"#fff":"#000",lineWidth:2}},textContent:{style:{text:s.name,fill:ra.value?"#fff":"#000",fontSize:16,fontWeight:"bold"}},textConfig:{position:"inside"}}},data:a.map(((e,a)=>({name:e.name,value:e.value,itemData:e}))),emphasis:{focus:"self"},select:{itemStyle:{shadowBlur:10,shadowColor:ra.value?"rgba(0, 255, 255, 0.5)":"rgba(24, 144, 255, 0.5)"}}}]};Ge.value.setOption(t),Ge.value.on("click",(a=>{var t;const l=(null==(t=a.data)?void 0:t.itemData)||e.find((e=>e.name===a.name));l&&(ua.value=l,ot(l.valueDistribution),dt(l.maintenanceStatus))})),"all"===ia.value?ua.value=null:e.length>0&&!ua.value&&(ua.value=e[0])},ut=()=>{if(!qe.value)return;He.value=O(qe.value,ra.value?"dark":null,{renderer:"canvas",useDirtyRect:!0,useCoarsePointer:!0,pointerSize:20});const e={backgroundColor:"transparent",title:{text:"资产价值区间分布",left:"center",textStyle:{fontSize:14,color:ra.value?"#eee":"#333"}},tooltip:{trigger:"axis",axisPointer:{type:"shadow"},formatter:function(e){var a;if(e&&e.length>0){const t=e[0];return`${t.name}<br/>资产数量: ${t.value}<br/>价值: ${(null==(a=t.data)?void 0:a.totalValue)||"未知"}万`}return""}},legend:{data:["资产数量"],bottom:"0%"},grid:{left:"3%",right:"4%",bottom:"15%",top:"15%",containLabel:!0},xAxis:{type:"category",data:[],axisLabel:{interval:0,fontSize:10}},yAxis:{type:"value",name:"资产数量",nameTextStyle:{padding:[0,0,0,30]}},series:[{name:"资产数量",type:"bar",emphasis:{focus:"series"},data:[]}]};He.value.setOption(e)},ot=e=>{if(!He.value||!e)return;const a=e.map((e=>e.count));He.value.setOption({series:[{data:a,type:"bar",name:"资产数量"}],legend:{data:["资产数量"]}})},ct=()=>{if(!Ke.value)return;Ye.value=O(Ke.value,ra.value?"dark":null,{renderer:"canvas",useDirtyRect:!0,useCoarsePointer:!0,pointerSize:20});const e={backgroundColor:"transparent",title:{text:"部门资产矩阵",left:"center",textStyle:{fontSize:14,color:ra.value?"#eee":"#333"}},tooltip:{position:"top",formatter:function(e){var a,t;if(e.value&&e.value.length>=3){const[l,s,n]=e.value,r=(null==(a=e.data)?void 0:a.xLabel)||l;return`${(null==(t=e.data)?void 0:t.yLabel)||s} - ${r}<br/>资产数量: ${n}`}return""}},grid:{left:"3%",right:"7%",bottom:"15%",top:"15%",containLabel:!0},xAxis:{type:"category",data:[],splitArea:{show:!0},axisLabel:{interval:0,fontSize:10,rotate:30}},yAxis:{type:"category",data:[],splitArea:{show:!0}},visualMap:{min:0,max:10,calculable:!0,orient:"horizontal",left:"center",bottom:"0%",inRange:{color:ra.value?["#313695","#4575b4","#74add1","#abd9e9","#e0f3f8","#ffffbf","#fee090","#fdae61","#f46d43","#d73027","#a50026"]:["#313695","#4575b4","#74add1","#abd9e9","#e0f3f8","#ffffbf","#fee090","#fdae61","#f46d43","#d73027","#a50026"].reverse()}},series:[{name:"资产数量",type:"heatmap",data:[],label:{show:!0},emphasis:{itemStyle:{shadowBlur:10,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]};Ye.value.setOption(e)},dt=e=>{if(!ea.value||!e)return;const a=Object.entries(e).map((([e,a])=>({name:e,value:a})));ea.value.setOption({series:[{data:a}],legend:{data:Object.keys(e)}})},vt=e=>{},pt=e=>{e&&e.assetTypes&&(va.value=[{key:"assetType",name:"资产类型",options:e.assetTypes},{key:"department",name:"部门",options:e.departments},{key:"region",name:"区域",options:e.regions},{key:"status",name:"状态",options:e.statuses}])},mt=e=>{e&&(e.valueDistribution&&He.value&&gt(e.valueDistribution),e.matrixData&&Ye.value?bt(e.matrixData):e.departmentAssetMatrix&&Ye.value&&bt(e.departmentAssetMatrix),e.statusDistribution&&ea.value&&ht(e.statusDistribution),e.typeStatistics&&e.typeStatistics.length>0&&ft(e.typeStatistics))},ft=e=>{if(!Ze.value||!e)return;const a=e.map((e=>e.assetTypeName)),t=e.map((e=>e.assetCount)),l=Pa(a,t);l&&Ze.value.setOption(l,!0)},gt=e=>{if(!He.value||!e)return;const a=e.assetCounts.map(((a,t)=>({value:a,totalValue:e.totalValues?e.totalValues[t]:0,itemStyle:{color:e.rangeColors&&e.rangeColors[t]?e.rangeColors[t]:["#5470c6","#91cc75","#fac858","#ee6666","#73c0de"][t%5]}}))),t={xAxis:{data:e.valueRanges||[]},series:[{name:"资产数量",type:"bar",data:a}],tooltip:{formatter:function(e){const a=e.data;return`${e.name}<br/>资产数量: ${a.value}<br/>价值: ${a.totalValue}万`}}};He.value.setOption(t)},bt=e=>{if(!Ye.value||!e)return;const a=e.matrixData.flat(),t=Math.max(...a,10),l=e.matrixData.flatMap(((a,t)=>a.map(((a,l)=>({value:[l,t,a],xLabel:e.assetTypes[l],yLabel:e.departments[t]}))))),s={xAxis:{data:e.assetTypes||[]},yAxis:{data:e.departments||[]},visualMap:{min:0,max:t,calculable:!0,orient:"horizontal",left:"center",bottom:"0%"},series:[{name:"资产数量",type:"heatmap",data:l,label:{show:!0}}],tooltip:{formatter:function(e){return e.data&&e.data.value&&e.data.value.length>=3?`${e.data.yLabel} - ${e.data.xLabel}<br/>资产数量: ${e.data.value[2]}`:""}}};Ye.value.setOption(s)},ht=e=>{if(!ea.value||!e)return;const a={series:[{data:e.statusLabels.map(((a,t)=>({name:a,value:e.statusCounts[t]})))}]};ea.value.setOption(a)},yt=()=>{if(Qe.value){aa.value=O(Qe.value,null);const e={backgroundColor:"transparent",textStyle:{color:ra.value?"#e2e8f0":"#1e293b"},series:[{type:"pie",radius:["50%","80%"],center:["50%","50%"],data:Ra.value.map((e=>({value:e.percentage,name:e.name,itemStyle:{color:e.color}}))),label:{show:!1},labelLine:{show:!1},emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:ra.value?"rgba(255, 255, 255, 0.3)":"rgba(0, 0, 0, 0.3)"}}}],legend:{show:!1}};aa.value.setOption(e)}},kt=()=>{wa.value.forEach(((e,a)=>{if(e.chartRef){const t=O(e.chartRef,ra.value?"dark":null),l={backgroundColor:"transparent",grid:{top:0,left:0,right:0,bottom:0},xAxis:{type:"category",show:!1},yAxis:{type:"value",show:!1},series:[{type:"line",data:e.chartData,smooth:!0,symbol:"none",lineStyle:{width:2,color:Ct(a)}}]};t.setOption(l)}}))},Ct=e=>{const a=["#3b82f6","#10b981","#f59e0b","#8b5cf6"];return a[e%a.length]},xt=e=>{e&&Array.isArray(e)?ha.value=e.map((e=>({id:e.departmentId,department:e.departmentName,totalAssets:e.assetCount,normalAssets:e.normalCount,faultAssets:e.faultCount,maintenanceAssets:e.maintenanceCount,normalRate:e.normalRate+"%",faultRate:e.faultRate+"%"}))):ha.value=[]},St=(e,a)=>{try{e&&"function"==typeof e.dispose&&(e.isDisposed()||e.dispose())}catch(t){}};o((()=>{try{window.removeEventListener("resize",wt,{passive:!0}),St(Ze.value),St(Ge.value),St(He.value),St(Ye.value),St(ea.value),St(aa.value),Ze.value=null,Ge.value=null,He.value=null,Ye.value=null,ea.value=null,aa.value=null}catch(e){}}));const Dt=(e,a)=>{try{e&&"function"==typeof e.resize&&(e.isDisposed()||e.resize())}catch(t){}},wt=((e,a)=>{let t;return function(...l){clearTimeout(t),t=setTimeout((()=>{clearTimeout(t),e(...l)}),a)}})((()=>{Dt(Ze.value),Dt(Ge.value),Dt(He.value),Dt(Ye.value),Dt(ea.value),Dt(aa.value)}),150);return(e,t)=>{const l=y("el-icon"),s=y("el-button"),n=y("el-option"),r=y("el-select"),i=y("el-checkbox"),u=y("el-date-picker"),o=y("el-card"),L=y("el-radio-button"),N=y("el-radio-group"),O=y("el-button-group"),I=y("el-empty"),P=y("el-table-column"),Ze=y("el-table"),Ge=y("el-pagination"),He=k("loading");return C(),c("div",{class:h(["analytics-workbench",{"dark-theme":ra.value,"light-theme":!ra.value}])},[d("aside",U,[d("div",j,[d("div",W,[v(l,{class:"logo-icon"},{default:p((()=>[v(x(S))])),_:1}),t[13]||(t[13]=d("h1",{class:"panel-title"},"资产分析工作台",-1))]),v(s,{type:"text",class:"theme-toggle",onClick:nt},{default:p((()=>[ra.value?(C(),D(l,{key:0},{default:p((()=>[v(x(w))])),_:1})):(C(),D(l,{key:1},{default:p((()=>[v(x($))])),_:1}))])),_:1})]),d("div",B,[d("div",E,[t[14]||(t[14]=d("label",{class:"filter-label"},"分析维度",-1)),v(r,{modelValue:oa.dimension,"onUpdate:modelValue":t[0]||(t[0]=e=>oa.dimension=e),placeholder:"选择分析维度",class:"filter-select",onChange:Ba},{default:p((()=>[(C(!0),c(m,null,f(va.value,(e=>(C(),D(n,{key:e.key,label:e.name,value:e.key},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])]),d("div",J,[t[15]||(t[15]=d("label",{class:"filter-label"},"度量指标",-1)),v(r,{modelValue:oa.metric,"onUpdate:modelValue":t[1]||(t[1]=e=>oa.metric=e),placeholder:"选择度量指标",class:"filter-select",onChange:Ea},{default:p((()=>[(C(!0),c(m,null,f(pa.value,(e=>(C(),D(n,{key:e.key,label:e.name,value:e.key},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])]),d("div",X,[t[16]||(t[16]=d("label",{class:"filter-label"},"资产状态",-1)),d("div",q,[(C(!0),c(m,null,f(ma.value,(e=>(C(),D(i,{key:e,modelValue:ca.value,"onUpdate:modelValue":t[2]||(t[2]=e=>ca.value=e),label:e,onChange:Ja},{default:p((()=>[R(b(e),1)])),_:2},1032,["modelValue","label"])))),128))])]),d("div",K,[t[17]||(t[17]=d("label",{class:"filter-label"},"部门",-1)),v(r,{modelValue:oa.filters.department,"onUpdate:modelValue":t[3]||(t[3]=e=>oa.filters.department=e),placeholder:"选择部门",class:"filter-select",clearable:"",onChange:Xa},{default:p((()=>[v(n,{label:"所有部门",value:""}),(C(!0),c(m,null,f(fa.value,(e=>(C(),D(n,{key:e,label:e,value:e},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])]),d("div",Q,[t[18]||(t[18]=d("label",{class:"filter-label"},"时间范围",-1)),v(u,{modelValue:da.value,"onUpdate:modelValue":t[4]||(t[4]=e=>da.value=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",class:"filter-select",onChange:qa},null,8,["modelValue"])]),v(s,{type:"info",class:"reset-btn",onClick:lt},{default:p((()=>t[19]||(t[19]=[R(" 重置筛选 ")]))),_:1})])]),d("main",Z,[d("header",G,[d("h2",H,b(xa.value),1),d("p",Y,b(Sa.value),1)]),d("section",ee,[d("div",ae,[(C(!0),c(m,null,f(wa.value,(e=>(C(),c("div",{key:e.label,class:h(["kpi-card enhanced",e.cardClass])},[d("div",te,[d("div",le,[d("p",se,b(e.label),1),d("div",ne,[d("h3",{class:h(["kpi-value",e.valueClass])},[R(b(e.value)+" ",1),e.unit?(C(),c("span",re,b(e.unit),1)):g("",!0)],2),void 0!==e.trend?(C(),c("div",ie,[v(l,{class:h(["trend-icon",e.trend>=0?"trend-up":"trend-down"])},{default:p((()=>[e.trend>=0?(C(),D(x(z),{key:0})):(C(),D(x(A),{key:1}))])),_:2},1032,["class"]),d("span",ue,b(Math.abs(e.trend).toFixed(1))+"%",1)])):g("",!0)])]),e.chartData?(C(),c("div",oe,[d("div",{ref_for:!0,ref:a=>e.chartRef=a,class:"mini-chart"},null,512)])):g("",!0)])],2)))),128))])]),na.value?(C(),c("section",ce,[v(o,{class:"debug-card"},{header:p((()=>[d("div",de,[t[21]||(t[21]=d("h3",{class:"debug-title"},"数据调试信息",-1)),v(s,{type:"warning",size:"small",onClick:t[5]||(t[5]=e=>na.value=!1)},{default:p((()=>t[20]||(t[20]=[R("关闭")]))),_:1})])])),default:p((()=>{var e,a,l;return[d("div",ve,[t[22]||(t[22]=d("h4",null,"统计结果摘要:",-1)),d("pre",null,b(JSON.stringify((null==(e=ga.value)?void 0:e.summary)||{},null,2)),1),t[23]||(t[23]=d("h4",null,"聚合数据 (前5项):",-1)),d("pre",null,b(JSON.stringify((null==(l=null==(a=ga.value)?void 0:a.aggregatedData)?void 0:l.slice(0,5))||[],null,2)),1),t[24]||(t[24]=d("h4",null,"详细数据 (前2项):",-1)),d("pre",null,b(JSON.stringify(ha.value.slice(0,2)||[],null,2)),1),t[25]||(t[25]=d("h4",null,"表格列:",-1)),d("pre",null,b(JSON.stringify(ya.value||[],null,2)),1)])]})),_:1})])):g("",!0),d("div",pe,[d("div",me,[v(o,{class:"map-card"},{header:p((()=>[d("div",fe,[t[27]||(t[27]=d("h3",null,"部门资产分布热力图",-1)),d("div",ge,[v(N,{modelValue:ia.value,"onUpdate:modelValue":t[6]||(t[6]=e=>ia.value=e),size:"small",onChange:rt},{default:p((()=>[v(L,{value:"all"},{default:p((()=>t[26]||(t[26]=[R("全部部门")]))),_:1}),(C(!0),c(m,null,f(Aa.value.slice(0,4),(e=>(C(),D(L,{key:e.value,value:e.value},{default:p((()=>[R(b(e.label),1)])),_:2},1032,["value"])))),128))])),_:1},8,["modelValue"])])])])),default:p((()=>{var e;return[d("div",be,[d("div",{ref_key:"factoryMap",ref:F,class:"factory-map factory-map-reduced"},null,512),t[32]||(t[32]=d("div",{class:"map-legend"},[d("div",{class:"legend-title"},"在用资产密度"),d("div",{class:"legend-gradient"}),d("div",{class:"legend-labels"},[d("span",null,"低"),d("span",null,"高")])],-1)),ua.value||"all"===ia.value?(C(),c("div",he,[d("h4",null,b("all"===ia.value?"全部部门统计":null==(e=ua.value)?void 0:e.name),1),d("div",ye,[d("div",ke,[t[28]||(t[28]=d("span",{class:"stat-label"},"资产总数:",-1)),d("span",Ce,b(_a())+"台",1)]),d("div",xe,[t[29]||(t[29]=d("span",{class:"stat-label"},"在用资产:",-1)),d("span",Se,b(Ma())+"台",1)]),d("div",De,[t[30]||(t[30]=d("span",{class:"stat-label"},"在用率:",-1)),d("span",we,b(Ta())+"%",1)]),d("div",$e,[t[31]||(t[31]=d("span",{class:"stat-label"},"故障设备:",-1)),d("span",Re,b(Va())+"台",1)])])])):g("",!0)])]})),_:1})]),d("div",ze,[v(o,{class:"asset-type-card"},{header:p((()=>t[33]||(t[33]=[d("div",{class:"chart-header"},[d("h3",{class:"chart-title"},"资产类型")],-1)]))),default:p((()=>[d("div",Ae,[d("div",{ref_key:"assetTypePieChart1",ref:Qe,class:"asset-type-pie-chart asset-type-reduced"},null,512),d("div",_e,[(C(!0),c(m,null,f(Ra.value,(e=>(C(),c("div",{key:e.name,class:"legend-item"},[d("span",{class:"legend-color",style:_({backgroundColor:e.color})},null,4),d("span",Me,b(e.name),1),d("span",Te,b(e.percentage)+"%",1)])))),128))])])])),_:1}),v(o,{class:"chart-card"},{header:p((()=>[d("div",Ve,[d("h3",Le,b(Da.value),1),d("div",Ne,[ka.value.length>0?(C(),c("div",Oe,[v(s,{type:"info",size:"small",onClick:Ga},{default:p((()=>[v(l,null,{default:p((()=>[v(x(T))])),_:1}),t[34]||(t[34]=R(" 返回上级 "))])),_:1}),v(s,{type:"warning",size:"small",onClick:Ha},{default:p((()=>[v(l,null,{default:p((()=>[v(x(V))])),_:1}),t[35]||(t[35]=R(" 重置下钻 "))])),_:1})])):g("",!0),v(O,null,{default:p((()=>[v(s,{type:"bar"===sa.value?"primary":"default",size:"small",onClick:t[7]||(t[7]=e=>et("bar"))},{default:p((()=>t[36]||(t[36]=[R(" 柱状图 ")]))),_:1},8,["type"]),v(s,{type:"pie"===sa.value?"primary":"default",size:"small",onClick:t[8]||(t[8]=e=>et("pie"))},{default:p((()=>t[37]||(t[37]=[R(" 饼图 ")]))),_:1},8,["type"]),v(s,{type:"line"===sa.value?"primary":"default",size:"small",onClick:t[9]||(t[9]=e=>et("line"))},{default:p((()=>t[38]||(t[38]=[R(" 折线图 ")]))),_:1},8,["type"])])),_:1})])])])),default:p((()=>[d("div",Fe,[M(d("div",{ref_key:"mainChart",ref:a,class:"chart-canvas"},null,512),[[He,ta.value]])])])),_:1})])]),d("div",Ie,[v(o,{class:"chart-card"},{header:p((()=>t[39]||(t[39]=[d("div",{class:"chart-header"},[d("h3",{class:"chart-title"},"资产价值区间分布")],-1)]))),default:p((()=>[d("div",Pe,[d("div",{ref_key:"valueDistributionChart",ref:qe,class:"chart-canvas"},null,512)])])),_:1}),v(o,{class:"chart-card"},{header:p((()=>t[40]||(t[40]=[d("div",{class:"chart-header"},[d("h3",{class:"chart-title"},"部门资产矩阵")],-1)]))),default:p((()=>[d("div",Ue,[d("div",{ref_key:"matrixChart",ref:Ke,class:"chart-canvas"},null,512)])])),_:1})]),d("section",je,[v(o,{class:"table-card"},{header:p((()=>{var e,a;return[d("div",We,[t[42]||(t[42]=d("h3",{class:"table-title"},"数据明细",-1)),d("div",Be,[d("span",Ee,"显示 "+b((null==(a=null==(e=ga.value)?void 0:e.summary)?void 0:a.totalRecords)||0)+" 条记录",1),v(s,{type:"info",size:"small",onClick:t[10]||(t[10]=e=>na.value=!na.value)},{default:p((()=>[R(b(na.value?"隐藏调试":"显示调试"),1)])),_:1}),v(s,{type:"primary",size:"small",onClick:st},{default:p((()=>t[41]||(t[41]=[R(" 导出数据 ")]))),_:1})])])]})),default:p((()=>[ha.value.length||la.value?M((C(),D(Ze,{key:1,data:ha.value,class:"data-table",stripe:"",border:"",onRowClick:Ya},{default:p((()=>[v(P,{prop:"id",label:"ID",width:"80"}),v(P,{prop:"assetCode",label:"资产编号",width:"120"}),v(P,{prop:"assetName",label:"资产名称",width:"150"}),v(P,{prop:"assetType",label:"资产类型"}),v(P,{prop:"department",label:"部门"}),v(P,{prop:"location",label:"位置"}),v(P,{prop:"status",label:"状态",width:"100"}),v(P,{prop:"price",label:"价值(万元)",width:"120",formatter:e=>e.price?`¥${Number(e.price).toFixed(2)}万`:"-"},null,8,["formatter"]),v(P,{prop:"createdAt",label:"创建时间",formatter:e=>e.createdAt?new Date(e.createdAt).toLocaleDateString():"-"},null,8,["formatter"]),v(P,{prop:"managerName",label:"负责人",width:"100"})])),_:1},8,["data"])),[[He,la.value]]):(C(),c("div",Je,[v(I,{description:"暂无数据"})])),d("div",Xe,[v(Ge,{"current-page":Ca.currentPage,"onUpdate:currentPage":t[11]||(t[11]=e=>Ca.currentPage=e),"page-size":Ca.pageSize,"onUpdate:pageSize":t[12]||(t[12]=e=>Ca.pageSize=e),"page-sizes":[10,20,50,100],total:Ca.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:at,onCurrentChange:tt},null,8,["current-page","page-size","total"])])])),_:1})])])],2)}}},[["__scopeId","data-v-9f77c3a6"]]);export{qe as default};
