// IT资产管理系统 - 网络监控接口
// 文件路径: /Core/Resilience/INetworkMonitor.cs
// 功能: 定义网络监控接口，负责检测网络连接状态

using System;
using System.Threading.Tasks;

namespace ItAssetsSystem.Core.Resilience
{
    /// <summary>
    /// 网络状态枚举
    /// </summary>
    public enum NetworkStatus
    {
        /// <summary>
        /// 连接状态
        /// </summary>
        Connected,
        
        /// <summary>
        /// 断开状态
        /// </summary>
        Disconnected,
        
        /// <summary>
        /// 未知状态
        /// </summary>
        Unknown
    }
    
    /// <summary>
    /// 网络监控器选项
    /// </summary>
    public class NetworkMonitorOptions
    {
        /// <summary>
        /// 网络检查间隔（毫秒）
        /// </summary>
        public int CheckIntervalMs { get; set; } = 10000;
        
        /// <summary>
        /// 检测超时（毫秒）
        /// </summary>
        public int TimeoutMs { get; set; } = 5000;
        
        /// <summary>
        /// 检测重试次数
        /// </summary>
        public int RetryCount { get; set; } = 3;
        
        /// <summary>
        /// 检测URL
        /// </summary>
        public string CheckUrl { get; set; } = "https://www.baidu.com";
        
        /// <summary>
        /// 是否在启动时自动开始监控
        /// </summary>
        public bool AutoStartMonitoring { get; set; } = true;
    }
    
    /// <summary>
    /// 网络监控器接口
    /// </summary>
    public interface INetworkMonitor
    {
        /// <summary>
        /// 当前网络状态
        /// </summary>
        NetworkStatus CurrentStatus { get; }
        
        /// <summary>
        /// 网络状态变化事件
        /// </summary>
        event EventHandler<NetworkStatus> NetworkStatusChanged;
        
        /// <summary>
        /// 启动网络监控
        /// </summary>
        void Start();
        
        /// <summary>
        /// 停止网络监控
        /// </summary>
        void Stop();
        
        /// <summary>
        /// 执行网络连接检查
        /// </summary>
        /// <returns>网络连接状态</returns>
        Task<NetworkStatus> CheckConnectionAsync();
        
        /// <summary>
        /// 判断网络是否已连接
        /// </summary>
        /// <returns>是否已连接</returns>
        bool IsConnected();
    }
} 