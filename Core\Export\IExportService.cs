// IT资产管理系统 - 数据导出服务接口
// 文件路径: /Core/Export/IExportService.cs
// 功能: 定义数据导出功能的接口

using System.Collections.Generic;
using System.Threading.Tasks;
using System.IO;

namespace ItAssetsSystem.Core.Export
{
    /// <summary>
    /// 数据导出格式
    /// </summary>
    public enum ExportFormat
    {
        /// <summary>
        /// CSV格式
        /// </summary>
        Csv,
        
        /// <summary>
        /// Excel格式
        /// </summary>
        Excel,
        
        /// <summary>
        /// PDF格式
        /// </summary>
        Pdf,
        
        /// <summary>
        /// JSON格式
        /// </summary>
        Json
    }
    
    /// <summary>
    /// 数据导出选项
    /// </summary>
    public class ExportOptions
    {
        /// <summary>
        /// 导出格式
        /// </summary>
        public ExportFormat Format { get; set; }
        
        /// <summary>
        /// 文件名
        /// </summary>
        public string FileName { get; set; }
        
        /// <summary>
        /// 包含表头
        /// </summary>
        public bool IncludeHeaders { get; set; } = true;
        
        /// <summary>
        /// 列定义（键为属性名，值为显示名称）
        /// </summary>
        public Dictionary<string, string> Columns { get; set; }
    }
    
    /// <summary>
    /// 数据导出服务接口
    /// </summary>
    public interface IExportService
    {
        /// <summary>
        /// 导出数据到指定格式
        /// </summary>
        /// <typeparam name="T">数据类型</typeparam>
        /// <param name="data">要导出的数据集合</param>
        /// <param name="options">导出选项</param>
        /// <returns>导出结果的内存流</returns>
        Task<MemoryStream> ExportDataAsync<T>(IEnumerable<T> data, ExportOptions options);
        
        /// <summary>
        /// 获取支持的导出格式
        /// </summary>
        /// <returns>支持的导出格式列表</returns>
        List<ExportFormat> GetSupportedFormats();
    }
}

// 计划行数: 50
// 实际行数: 50 