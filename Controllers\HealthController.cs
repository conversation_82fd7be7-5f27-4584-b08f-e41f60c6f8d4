// IT资产管理系统 - 健康检查控制器
// 文件路径: /Controllers/HealthController.cs
// 功能: 提供健康检查API

using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;
using ItAssetsSystem.Infrastructure.Data;
using ItAssetsSystem.Models;

namespace ItAssetsSystem.Controllers
{
    /// <summary>
    /// 健康检查控制器
    /// </summary>
    [ApiController]
    [Route("[controller]")]
    public class HealthController : ControllerBase
    {
        private readonly ILogger<HealthController> _logger;
        private readonly AppDbContext _context;

        public HealthController(ILogger<HealthController> logger, AppDbContext context)
        {
            _logger = logger;
            _context = context;
        }

        /// <summary>
        /// 健康检查
        /// </summary>
        /// <returns>系统健康状态</returns>
        [HttpGet]
        public async Task<IActionResult> Check()
        {
            _logger.LogInformation("执行健康检查");
            
            var health = new HealthStatus
            {
                Status = "Healthy",
                Timestamp = DateTime.UtcNow,
                Version = GetType().Assembly.GetName().Version.ToString(),
                Services = new System.Collections.Generic.Dictionary<string, string>()
            };
            
            try
            {
                // 检查数据库连接
                bool isDatabaseAvailable = await CheckDatabaseConnectionAsync();
                health.Services["Database"] = isDatabaseAvailable ? "Available" : "Unavailable";
                
                if (!isDatabaseAvailable)
                {
                    health.Status = "Degraded";
                    health.Notes = "数据库连接不可用，系统使用模拟数据";
                }
                
                // 返回健康状态
                return Ok(health);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "健康检查失败");
                health.Status = "Unhealthy";
                health.Notes = ex.Message;
                return StatusCode(500, health);
            }
        }
        
        /// <summary>
        /// 检查数据库连接是否可用
        /// </summary>
        private async Task<bool> CheckDatabaseConnectionAsync()
        {
            try
            {
                // 尝试执行一个简单的查询来测试数据库连接
                return await _context.Database.CanConnectAsync();
            }
            catch
            {
                return false;
            }
        }
    }
} 