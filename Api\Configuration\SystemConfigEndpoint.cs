using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using ItAssetsSystem.Core.Configuration;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace ItAssetsSystem.Api.Configuration
{
    [ApiController]
    [Route("api/system/config")]
    public class SystemConfigEndpoint : ControllerBase
    {
        private readonly ISystemConfigService _configService;
        private readonly ILogger<SystemConfigEndpoint> _logger;
        
        public SystemConfigEndpoint(ISystemConfigService configService, ILogger<SystemConfigEndpoint> logger)
        {
            _configService = configService;
            _logger = logger;
        }
        
        /// <summary>
        /// 获取所有系统配置
        /// </summary>
        /// <returns>配置项字典</returns>
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<Dictionary<string, object>>> GetAllConfigurationsAsync()
        {
            try
            {
                var configurations = await _configService.GetAllConfigurationsAsync();
                return Ok(configurations);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取所有系统配置时发生错误");
                return StatusCode(StatusCodes.Status500InternalServerError, new { message = "获取系统配置失败" });
            }
        }
        
        /// <summary>
        /// 获取指定配置项
        /// </summary>
        /// <param name="key">配置键</param>
        /// <returns>配置值</returns>
        [HttpGet("{key}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<object>> GetConfigurationAsync(string key)
        {
            try
            {
                var value = await _configService.GetConfigurationValueAsync<object>(key);
                
                if (value == null)
                {
                    return NotFound(new { message = $"未找到配置项: {key}" });
                }
                
                return Ok(value);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取系统配置项时发生错误: {Key}", key);
                return StatusCode(StatusCodes.Status500InternalServerError, new { message = $"获取配置项失败: {key}" });
            }
        }
        
        /// <summary>
        /// 更新配置项
        /// </summary>
        [HttpPut("{key}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult> UpdateConfigurationAsync(string key, [FromBody] object value)
        {
            try
            {
                if (string.IsNullOrEmpty(key))
                {
                    return BadRequest(new { message = "配置键不能为空" });
                }
                
                var success = await _configService.UpdateConfigurationValueAsync(key, value);
                
                if (success)
                {
                    return Ok(new { message = $"成功更新配置项: {key}" });
                }
                else
                {
                    return StatusCode(StatusCodes.Status500InternalServerError, new { message = $"更新配置项失败: {key}" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新系统配置项时发生错误: {Key}", key);
                return StatusCode(StatusCodes.Status500InternalServerError, new { message = $"更新配置项失败: {key}" });
            }
        }
        
        /// <summary>
        /// 批量更新配置项
        /// </summary>
        [HttpPut]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult> BulkUpdateConfigurationsAsync([FromBody] Dictionary<string, object> configurations)
        {
            try
            {
                if (configurations == null || configurations.Count == 0)
                {
                    return BadRequest(new { message = "配置项不能为空" });
                }
                
                var success = await _configService.BulkUpdateConfigurationsAsync(configurations);
                
                if (success)
                {
                    return Ok(new { message = $"成功批量更新配置项，共 {configurations.Count} 项" });
                }
                else
                {
                    return StatusCode(StatusCodes.Status500InternalServerError, new { message = "批量更新配置项失败" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量更新系统配置项时发生错误");
                return StatusCode(StatusCodes.Status500InternalServerError, new { message = "批量更新配置项失败" });
            }
        }
        
        /// <summary>
        /// 重置所有配置为默认值
        /// </summary>
        [HttpPost("reset")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult> ResetToDefaultsAsync()
        {
            try
            {
                var success = await _configService.ResetToDefaultsAsync();
                
                if (success)
                {
                    return Ok(new { message = "成功重置所有配置项为默认值" });
                }
                else
                {
                    return StatusCode(StatusCodes.Status500InternalServerError, new { message = "重置配置项失败" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "重置系统配置项时发生错误");
                return StatusCode(StatusCodes.Status500InternalServerError, new { message = "重置配置项失败" });
            }
        }
        
        /// <summary>
        /// 导出系统配置
        /// </summary>
        [HttpGet("export")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult> ExportConfigurationsAsync()
        {
            try
            {
                // 创建临时文件
                var tempFile = Path.Combine(Path.GetTempPath(), $"config-export-{DateTime.Now:yyyyMMddHHmmss}.json");
                
                var success = await _configService.ExportConfigurationsAsync(tempFile);
                
                if (success)
                {
                    // 读取文件内容并返回
                    var fileContents = await System.IO.File.ReadAllBytesAsync(tempFile);
                    
                    // 删除临时文件
                    try { System.IO.File.Delete(tempFile); } catch { }
                    
                    return File(fileContents, "application/json", $"system-config-{DateTime.Now:yyyyMMdd}.json");
                }
                else
                {
                    return StatusCode(StatusCodes.Status500InternalServerError, new { message = "导出配置失败" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "导出系统配置时发生错误");
                return StatusCode(StatusCodes.Status500InternalServerError, new { message = "导出配置失败" });
            }
        }
        
        /// <summary>
        /// 导入系统配置
        /// </summary>
        [HttpPost("import")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult> ImportConfigurationsAsync(IFormFile file)
        {
            try
            {
                if (file == null || file.Length == 0)
                {
                    return BadRequest(new { message = "未提供配置文件" });
                }
                
                if (!file.FileName.EndsWith(".json", StringComparison.OrdinalIgnoreCase))
                {
                    return BadRequest(new { message = "配置文件必须是JSON格式" });
                }
                
                // 创建临时文件
                var tempFile = Path.Combine(Path.GetTempPath(), $"config-import-{DateTime.Now:yyyyMMddHHmmss}.json");
                
                // 保存上传文件到临时文件
                using (var stream = new FileStream(tempFile, FileMode.Create))
                {
                    await file.CopyToAsync(stream);
                }
                
                // 导入配置
                var success = await _configService.ImportConfigurationsAsync(tempFile);
                
                // 删除临时文件
                try { System.IO.File.Delete(tempFile); } catch { }
                
                if (success)
                {
                    return Ok(new { message = "成功导入系统配置" });
                }
                else
                {
                    return StatusCode(StatusCodes.Status500InternalServerError, new { message = "导入配置失败" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "导入系统配置时发生错误");
                return StatusCode(StatusCodes.Status500InternalServerError, new { message = "导入配置失败" });
            }
        }
    }
} 