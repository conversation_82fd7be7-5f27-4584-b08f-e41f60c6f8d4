<!-- 资产类型管理页面 -->
<template>
  <div class="asset-type">
    <!-- 搜索区域 -->
    <el-card class="search-card">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="关键词">
          <el-input
            v-model="searchForm.keyword"
            placeholder="请输入类型名称/编码"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作按钮区域 -->
    <el-card class="operation-card">
      <div class="operation-buttons">
        <el-button type="primary" @click="handleAdd">新增类型</el-button>
        <el-button type="success" @click="handleExport">导出</el-button>
        <el-button type="warning" @click="handleBatchImport">批量导入</el-button>
      </div>
    </el-card>

    <!-- 资产类型列表 -->
    <el-card class="list-card">
      <el-table
        v-loading="loading"
        :data="typeList"
        border
        style="width: 100%"
      >
        <el-table-column prop="code" label="类型编码" width="120" />
        <el-table-column prop="name" label="类型名称" width="150" />
        <el-table-column prop="description" label="描述" />
        <el-table-column label="是否激活" width="80" align="center">
          <template #default="{ row }">
            <el-tag :type="row.isActive ? 'success' : 'danger'" size="small">
              {{ row.isActive ? '是' : '否' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间" width="180" />
        <el-table-column prop="updatedAt" label="更新时间" width="180" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button-group>
              <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>
              <el-button 
                :type="row.isActive ? 'warning' : 'success'" 
                link 
                @click="handleToggleActive(row)"
              >
                {{ row.isActive ? '停用' : '激活' }}
              </el-button>
              <el-button type="danger" link @click="handleDelete(row)">删除</el-button>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="50%"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="类型编码" prop="code">
          <el-input v-model="form.code" placeholder="请输入类型编码" />
        </el-form-item>
        <el-form-item label="类型名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入类型名称" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="3"
            placeholder="请输入描述信息"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 批量导入对话框 -->
    <el-dialog
      v-model="importDialogVisible"
      title="资产类型批量导入"
      width="500px"
      destroy-on-close
    >
      <div class="import-dialog-content">
        <div class="import-steps">
          <p class="step"><el-tag size="small">步骤 1</el-tag> 下载导入模板</p>
          <p class="step"><el-tag size="small">步骤 2</el-tag> 按照模板格式填写数据</p>
          <p class="step"><el-tag size="small">步骤 3</el-tag> 上传填写好的文件</p>
        </div>
        
        <div class="template-download">
          <el-button type="primary" plain @click="downloadTemplate">
            <el-icon><Download /></el-icon> 下载导入模板
          </el-button>
          <p class="template-tip">请确保按照模板格式填写数据，否则可能导致导入失败</p>
        </div>
        
        <el-divider />
        
        <el-upload
          ref="uploadRef"
          class="import-upload"
          :action="`${systemConfig.apiBaseUrl}/Import/data?entityType=AssetTypes`"
          :headers="uploadHeaders"
          :before-upload="beforeImportUpload"
          :on-success="handleImportSuccess"
          :on-error="handleImportError"
          :on-progress="handleImportProgress"
          :auto-upload="false"
          accept=".csv,.xlsx,.xls"
          :limit="1"
        >
          <template #trigger>
            <el-button type="primary">
              <el-icon><Upload /></el-icon> 选择文件
            </el-button>
          </template>
          
          <template #tip>
            <div class="el-upload__tip">
              仅支持 xlsx, xls 或 csv 格式文件，文件大小不超过10MB
            </div>
          </template>
          
          <el-button 
            class="ml-3" 
            type="success" 
            @click="submitImport"
            :disabled="uploadFile === null"
          >
            开始导入
          </el-button>
        </el-upload>
        
        <div v-if="importing" class="import-progress">
          <el-progress 
            :percentage="importProgress" 
            status="success"
          />
          <p class="progress-text">正在导入，请勿关闭窗口...</p>
        </div>
        
        <div v-if="importResult" class="import-result">
          <el-alert
            :title="importResult.success ? '导入成功' : '导入失败'"
            :type="importResult.success ? 'success' : 'error'"
            show-icon
          >
            <div class="result-info">
              <p v-if="importResult.success">
                成功导入 {{ importResult.data?.successCount || 0 }} 条记录
              </p>
              <p v-if="importResult.data?.errorCount">
                失败 {{ importResult.data?.errorCount || 0 }} 条记录
              </p>
              <div v-if="importResult.data?.errorMessages?.length > 0" class="error-messages">
                <p>错误信息:</p>
                <ul>
                  <li v-for="(error, index) in importResult.data?.errorMessages" :key="index">
                    {{ error }}
                  </li>
                </ul>
              </div>
            </div>
          </el-alert>
        </div>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="importDialogVisible = false">关闭</el-button>
          <el-button 
            v-if="importResult && importResult.success" 
            type="primary" 
            @click="finishImport"
          >
            完成并刷新
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Download, Upload } from '@element-plus/icons-vue'
import assetTypeApi from '@/api/assetType'
import systemConfig from '@/config/system'
import { getToken } from '@/utils/auth'

// 数据列表
const typeList = ref([])
const loading = ref(false)
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)

// 搜索表单
const searchForm = ref({
  keyword: ''
})

// 表单数据
const dialogVisible = ref(false)
const dialogType = ref('add')
const dialogTitle = ref('新增资产类型')
const formRef = ref(null)
const form = ref({
  code: '',
  name: '',
  description: ''
})

// 表单验证规则
const rules = {
  code: [{ required: true, message: '请输入类型编码', trigger: 'blur' }],
  name: [{ required: true, message: '请输入类型名称', trigger: 'blur' }]
}

// 导入相关
const importDialogVisible = ref(false)
const uploadRef = ref(null)
const uploadFile = ref(null)
const importing = ref(false)
const importProgress = ref(0)
const importResult = ref(null)
const uploadHeaders = {
  Authorization: `Bearer ${getToken()}`
}

// 获取资产类型列表
const fetchTypeList = async () => {
  try {
    loading.value = true
    // 确保使用正确的参数格式
    const params = {
      page: currentPage.value,
      pageSize: pageSize.value,
      isActive: null // 始终传递null，表示获取所有资产类型
    }
    
    // 只有在关键词不为空时才添加
    if (searchForm.value.keyword && searchForm.value.keyword.trim() !== '') {
      params.keyword = searchForm.value.keyword.trim()
    }
    
    console.log('请求资产类型列表，参数:', params)
    
    const res = await assetTypeApi.getAssetTypes(params)
    if (res.success === true) {
      // 使用返回的分页数据
      typeList.value = res.data.items || []
      total.value = res.data.total || 0
      
      console.log('资产类型列表数据:', {
        列表条数: typeList.value.length,
        当前页: currentPage.value,
        页大小: pageSize.value,
        总数: total.value,
        总页数: res.data.pages || Math.ceil(total.value / pageSize.value)
      })
      
      // 如果列表为空但有数据，可能是分页问题，尝试回到第一页
      if (typeList.value.length === 0 && total.value > 0) {
        currentPage.value = 1
        fetchTypeList()
      }
    } else {
      ElMessage.error(res.message || '获取资产类型列表失败')
    }
  } catch (error) {
    console.error('获取资产类型列表失败:', error)
    ElMessage.error('获取资产类型列表失败: ' + (error.message || '未知错误'))
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchTypeList()
}

// 重置
const handleReset = () => {
  searchForm.value = {
    keyword: ''
  }
  handleSearch()
}

// 新增
const handleAdd = () => {
  dialogType.value = 'add'
  dialogTitle.value = '新增资产类型'
  form.value = {
    code: '',
    name: '',
    description: ''
  }
  dialogVisible.value = true
}

// 编辑
const handleEdit = (row) => {
  dialogType.value = 'edit'
  dialogTitle.value = '编辑资产类型'
  
  // 深拷贝行数据，避免直接修改表格数据
  form.value = JSON.parse(JSON.stringify(row))
  
  // 确保ID是数字类型
  if (typeof form.value.id === 'string') {
    form.value.id = parseInt(form.value.id, 10)
    if (isNaN(form.value.id)) {
      console.error('资产类型ID无效', row.id)
      ElMessage.error('资产类型ID无效')
      return
    }
  }
  
  console.log('编辑资产类型 - 表单初始化数据:', form.value)
  
  // 打开对话框
  dialogVisible.value = true
}

// 删除
const handleDelete = (row) => {
  ElMessageBox.confirm(
    `确定要删除"${row.name}"吗？`,
    '警告',
    {
      confirmButtonText: '确定删除',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    loading.value = true
    try {
      const res = await assetTypeApi.deleteAssetType(row.id)
      if (res.success) {
        ElMessage.success('删除成功')
        fetchTypeList()
      } else {
        ElMessage.error(res.message || '删除失败')
      }
    } catch (error) {
      console.error('删除失败:', error)
      ElMessage.error('删除失败: ' + (error.message || '未知错误'))
    } finally {
      loading.value = false
    }
  }).catch(() => {
    // 用户取消删除
  })
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid, fields) => {
    if (valid) {
      loading.value = true
      try {
        const data = { ...form.value }
        let res
        
        // 确保在控制台打印完整数据以便调试
        console.log(`${dialogType.value === 'add' ? '创建' : '更新'}资产类型数据:`, data);
        
        if (dialogType.value === 'add') {
          res = await assetTypeApi.createAssetType(data)
        } else {
          const id = data.id;
          // 确保ID是数字类型
          if (typeof id !== 'number') {
            data.id = parseInt(id, 10);
            if (isNaN(data.id)) {
              throw new Error('资产类型ID无效');
            }
          }
          
          console.log(`准备更新资产类型 ID: ${data.id}, 数据:`, data);
          res = await assetTypeApi.updateAssetType(data.id, data)
        }
        
        console.log(`${dialogType.value === 'add' ? '创建' : '更新'}资产类型响应:`, res);
        
        if (res.success) {
          ElMessage.success(dialogType.value === 'add' ? '添加成功' : '更新成功')
          dialogVisible.value = false
          fetchTypeList()
        } else {
          ElMessage.error(res.message || (dialogType.value === 'add' ? '添加失败' : '更新失败'))
        }
      } catch (error) {
        console.error('操作失败:', error)
        ElMessage.error('操作失败: ' + (error.message || '未知错误'))
      } finally {
        loading.value = false
      }
    } else {
      console.error('表单验证失败:', fields)
    }
  })
}

// 导出
const handleExport = async () => {
  try {
    // 显示加载中提示
    const loadingInstance = ElMessage({
      message: '正在准备导出数据，请稍候...',
      type: 'warning',
      duration: 0
    })
    
    // 调用导出API
    const response = await assetTypeApi.exportAssetTypes({
      keyword: searchForm.value.keyword // 传递当前搜索条件
    })
    
    // 关闭加载提示
    loadingInstance.close()
    
    // 处理不同的文件类型
    let fileType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
    let fileExtension = 'xlsx';
    
    // 从响应头中获取内容类型
    const contentType = response.type || 'application/octet-stream';
    console.log('导出文件内容类型:', contentType);
    
    // 根据内容类型设置文件扩展名
    if (contentType.includes('sheet') || contentType.includes('excel')) {
      fileType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      fileExtension = 'xlsx';
    } else if (contentType.includes('csv')) {
      fileType = 'text/csv';
      fileExtension = 'csv';
    }
    
    // 创建文件下载链接
    const blob = new Blob([response], { type: fileType });
    const fileName = `资产类型导出_${new Date().toISOString().substring(0, 10)}.${fileExtension}`;
    
    if (window.navigator.msSaveOrOpenBlob) {
      // IE浏览器
      window.navigator.msSaveBlob(blob, fileName);
    } else {
      // 其他浏览器
      const link = document.createElement('a');
      link.href = URL.createObjectURL(blob);
      link.download = fileName;
      link.style.display = 'none';
      document.body.appendChild(link);
      link.click();
      URL.revokeObjectURL(link.href);
      document.body.removeChild(link);
    }
    
    ElMessage.success('导出成功');
  } catch (error) {
    console.error('导出资产类型数据失败:', error);
    ElMessage.error('导出失败，请重试');
  }
}

// 批量导入
const handleBatchImport = () => {
  importDialogVisible.value = true
  uploadFile.value = null
  importing.value = false
  importProgress.value = 0
  importResult.value = null
}

// 下载导入模板
const downloadTemplate = async () => {
  try {
    const response = await assetTypeApi.getAssetTypeImportTemplate()
    const blob = new Blob([response], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
    const fileName = `资产类型导入模板_${new Date().toISOString().substring(0, 10)}.xlsx`
    
    if (window.navigator.msSaveOrOpenBlob) {
      window.navigator.msSaveBlob(blob, fileName)
    } else {
      const link = document.createElement('a')
      link.href = URL.createObjectURL(blob)
      link.download = fileName
      link.style.display = 'none'
      document.body.appendChild(link)
      link.click()
      URL.revokeObjectURL(link.href)
      document.body.removeChild(link)
    }
    
    ElMessage.success('模板下载成功')
  } catch (error) {
    console.error('下载导入模板失败:', error)
    ElMessage.error('模板下载失败，请重试')
  }
}

// 上传前验证
const beforeImportUpload = (file) => {
  const isValidType = file.type === 'application/vnd.ms-excel' || 
                      file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                      file.type === 'text/csv' ||
                      /\.(csv|xlsx|xls)$/i.test(file.name)
  
  const isLt10M = file.size / 1024 / 1024 < 10
  
  if (!isValidType) {
    ElMessage.error('请上传Excel或CSV格式的文件!')
    return false
  }
  
  if (!isLt10M) {
    ElMessage.error('文件大小不能超过10MB!')
    return false
  }
  
  uploadFile.value = file
  return isValidType && isLt10M
}

// 提交导入
const submitImport = () => {
  if (uploadFile.value) {
    importing.value = true
    importProgress.value = 0
    importResult.value = null
    
    if (uploadRef.value) {
      uploadRef.value.submit()
    }
  } else {
    ElMessage.warning('请先选择要导入的文件')
  }
}

// 导入进度
const handleImportProgress = (event) => {
  importProgress.value = Math.round(event.percent)
}

// 导入成功
const handleImportSuccess = (response) => {
  importing.value = false
  importProgress.value = 100
  
  if (response.success) {
    importResult.value = {
      success: true,
      data: response.data || {
        successCount: response.data?.totalCount || 0,
        errorCount: 0,
        errorMessages: []
      }
    }
  } else {
    importResult.value = {
      success: false,
      data: {
        successCount: 0,
        errorCount: response.data?.errors?.length || 0,
        errorMessages: response.data?.errors || [response.message || '导入失败']
      }
    }
  }
}

// 导入错误
const handleImportError = (error) => {
  importing.value = false
  console.error('资产类型导入失败:', error)
  
  let errorMessage = '导入失败'
  try {
    if (error.response && error.response.data) {
      errorMessage = error.response.data.message || error.response.data.error || '导入失败'
    }
  } catch (e) {
    console.error('解析错误信息失败:', e)
  }
  
  importResult.value = {
    success: false,
    data: {
      successCount: 0,
      errorCount: 1,
      errorMessages: [errorMessage]
    }
  }
}

// 完成导入并刷新
const finishImport = () => {
  importDialogVisible.value = false
  fetchTypeList() // 刷新资产类型列表
}

// 分页大小改变
const handleSizeChange = (val) => {
  console.log(`改变每页显示数量: ${val}`)
  pageSize.value = val
  currentPage.value = 1 // 切换页面大小时重置为第一页
  fetchTypeList()
}

// 当前页改变
const handleCurrentChange = (val) => {
  console.log(`跳转到页面: ${val}`)
  currentPage.value = val
  fetchTypeList()
}

// 切换激活状态
const handleToggleActive = async (row) => {
  try {
    loading.value = true
    const res = await assetTypeApi.toggleAssetTypeActive(row.id)
    if (res.success) {
      ElMessage.success('激活状态切换成功')
      fetchTypeList()
    } else {
      ElMessage.error(res.message || '激活状态切换失败')
    }
  } catch (error) {
    console.error('切换激活状态失败:', error)
    ElMessage.error('切换激活状态失败')
  } finally {
    loading.value = false
  }
}

// 初始化
onMounted(() => {
  fetchTypeList()
})
</script>

<style scoped>
.asset-type {
  padding: 20px;
}

.search-card {
  margin-bottom: 20px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.operation-card {
  margin-bottom: 20px;
}

.operation-buttons {
  display: flex;
  gap: 10px;
}

.list-card {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.import-dialog-content {
  padding: 20px;
}

.import-steps {
  margin-bottom: 20px;
}

.step {
  margin-bottom: 10px;
}

.template-download {
  margin-bottom: 20px;
}

.template-tip {
  margin-top: 10px;
  margin-bottom: 10px;
}

.import-upload {
  margin-bottom: 20px;
}

.import-progress {
  margin-bottom: 20px;
}

.progress-text {
  margin-top: 10px;
  margin-bottom: 10px;
}

.import-result {
  margin-bottom: 20px;
}

.result-info {
  margin-bottom: 10px;
}

.error-messages {
  margin-top: 10px;
}
</style> 