/**
 * API索引文件
 * 文件路径: src/api/index.js
 * 功能描述: 统一导出所有API模块
 */

import request from '@/utils/request'
import authApi from './auth'
import userApi from './user'
import assetApi from './asset'
import locationApi from './location'

/**
 * 通用请求方法
 */
export function getRequest(url, params, config = {}) {
  return request.get(url, { params, ...config })
}

export function postRequest(url, data, config = {}) {
  return request.post(url, data, config)
}

export function putRequest(url, data, config = {}) {
  return request.put(url, data, config)
}

export function deleteRequest(url, config = {}) {
  return request.delete(url, config)
}

export {
  authApi,
  userApi,
  assetApi,
  locationApi
}

// 默认导出
export default {
  auth: authApi,
  user: userApi,
  asset: assetApi,
  location: locationApi,
  
  // 通用方法
  get: getRequest,
  post: postRequest,
  put: putRequest,
  delete: deleteRequest
}