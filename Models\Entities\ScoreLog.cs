// IT资产管理系统 - 积分变动日志实体
// 文件路径: /Models/Entities/ScoreLog.cs
// 功能: 定义积分变动日志数据结构

using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using ItAssetsSystem.Domain.Entities.Tasks;

namespace ItAssetsSystem.Models.Entities
{
    /// <summary>
    /// 积分行为类型枚举
    /// </summary>
    public enum ScoreActionType
    {
        /// <summary>
        /// 创建任务
        /// </summary>
        CreateTask = 1,
        
        /// <summary>
        /// 更新任务进度
        /// </summary>
        UpdateTask = 2,
        
        /// <summary>
        /// 完成任务
        /// </summary>
        CompleteTask = 3,
        
        /// <summary>
        /// 评论任务
        /// </summary>
        CommentTask = 4,
        
        /// <summary>
        /// 认领任务
        /// </summary>
        ClaimTask = 5,
        
        /// <summary>
        /// 每日签到
        /// </summary>
        DailySignIn = 6,
        
        /// <summary>
        /// 使用道具
        /// </summary>
        UseItem = 7,
        
        /// <summary>
        /// 受到攻击
        /// </summary>
        BeAttacked = 8
    }

    /// <summary>
    /// 积分变动日志实体类
    /// </summary>
    [Table("score_logs")]
    public class ScoreLog
    {
        /// <summary>
        /// 记录ID
        /// </summary>
        [Key]
        [Column("id")]
        public int Id { get; set; }
        
        /// <summary>
        /// 用户ID
        /// </summary>
        [Required]
        [Column("user_id")]
        public int UserId { get; set; }
        
        /// <summary>
        /// 行为类型
        /// </summary>
        [Column("action_type")]
        public int ActionType { get; set; }
        
        /// <summary>
        /// 积分变动值
        /// </summary>
        [Column("score_change")]
        public int ScoreChange { get; set; }
        
        /// <summary>
        /// 任务ID
        /// </summary>
        [Column("task_id")]
        public long? TaskId { get; set; }
        
        /// <summary>
        /// 道具ID
        /// </summary>
        [Column("item_id")]
        public int? ItemId { get; set; }
        
        /// <summary>
        /// 描述
        /// </summary>
        [Column("description")]
        [StringLength(200)]
        public string Description { get; set; }
        
        /// <summary>
        /// 创建时间
        /// </summary>
        [Column("created_at")]
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        
        /// <summary>
        /// 导航属性 - 用户
        /// </summary>
        [ForeignKey("UserId")]
        public virtual User User { get; set; }
        
        /// <summary>
        /// 导航属性 - 任务
        /// </summary>
        [ForeignKey("TaskId")]
        public virtual ItAssetsSystem.Domain.Entities.Tasks.Task Task { get; set; }
    }
} 