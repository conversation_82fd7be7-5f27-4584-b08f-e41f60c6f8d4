## **Cursor规则：AI 后端代码生成强制规范 V1.1 (独立版)**

**AI，你在生成任何后端代码时，必须严格遵守以下规则，无需参考其他提示词：**

1. **文件头注释 (强制):** 每个 C\# 文件顶部**必须**包含路径和文件名注释：  
   // File: src/LayerName/FeatureName/FileName.cs  
   // Description: \[文件功能简述\]

2. **文件夹结构 (强制):** 代码**必须**放置在标准的 Clean Architecture 目录内 (Domain, Application, Infrastructure, Api)，并按功能特性组织 (Features/FeatureName)。**新模块代码使用 V2 API 路径或新特性文件夹。**  
3. **层依赖 (强制):** **严格遵守** Domain \-\> Application \-\> Infrastructure / Api 依赖规则。**禁止**跨层或反向依赖。  
4. **API 规范 (强制):**  
   * **新 API (V2+):** **必须**使用 DTOs (定义在 Application/Features/.../Dtos) 进行请求/响应。**必须**返回标准 JSON 响应结构 ({success, data, message?, error?, pagination?})。**严禁**返回实体或匿名类型。使用 /api/v2/... 路由。  
   * **核心 API (V1 \- 冻结):** **禁止修改** /api/v1/... 核心模块 API 的现有路由、参数和返回结构（保持原样）。  
5. **数据访问 (强制):**  
   * **新模块:** **必须**使用 EF Core 8 \+ 仓储模式。**禁止**原生 SQL。  
   * **核心模块 (冻结):** **禁止修改**其现有的数据访问逻辑（保留混用）。  
   * **新模块访问核心表:** **仅限只读查询**（优先调用 V1 API，其次 EF Core AsNoTracking()）。**严禁**新模块直接写入核心表。  
6. **代码质量 (强制):** **必须**符合低复杂度 (圈复杂度 \< 15)、SOLID 原则、清晰注释（XML Doc \+ 内部注释）、一致风格（.editorconfig）。**放弃**代码行数限制。  
7. **可测试性 (强制):** 生成的代码（尤其 Application/Domain）**必须**高度可测试（依赖注入，无静态依赖），目标单元测试覆盖率 \>85%。  
8. **日志与错误处理 (强制):** **必须**集成 Serilog 进行结构化日志记录。**必须**使用全局异常处理（推荐 IExceptionHandler）和自定义异常，返回标准错误响应。  
9. **数据库 Schema (强制):**  
   * **新表** (如 Tasks, Comments, Gamification\_\*) **必须**使用 **BIGINT AUTO\_INCREMENT** 主键。  
   * **核心表** (如 Users, assets, locations) **保持 INT 主键**。  
   * EF Core 配置**必须**正确处理主键类型和外键关系。  
10. **安全 (强制):** **必须**实现 JWT 认证和基于角色的授权。**必须**进行输入验证。  
11. **寻求澄清 (强制):** 遇到任何指令不明确、冲突或遗漏时，**必须**提问确认，**禁止**自行猜测。

**核心记忆：核心模块 (V1 API, INT PKs, 混合数据访问) 已冻结！新模块 (V2 API, BIGINT PKs, 纯 EF Core, DTOs) 必须遵循 Clean Architecture 全新开发！**