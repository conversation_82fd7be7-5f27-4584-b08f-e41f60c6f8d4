// File: Application/Features/Tasks/Commands/ReplyCommentCommand.cs
// Description: 回复评论命令

using System.Collections.Generic;
using MediatR;
using ItAssetsSystem.Application.Common.Dtos;
using ItAssetsSystem.Application.Features.Tasks.Dtos;

namespace ItAssetsSystem.Application.Features.Tasks.Commands
{
    /// <summary>
    /// 回复评论命令
    /// </summary>
    public class ReplyCommentCommand : IRequest<ApiResponse<CommentDto>>
    {
        /// <summary>
        /// 任务ID
        /// </summary>
        public long TaskId { get; set; }

        /// <summary>
        /// 父评论ID
        /// </summary>
        public long ParentCommentId { get; set; }

        /// <summary>
        /// 评论内容
        /// </summary>
        public string Content { get; set; }

        /// <summary>
        /// 提及的用户ID列表
        /// </summary>
        public List<int> MentionedUserIds { get; set; } = new();

        /// <summary>
        /// 当前用户ID
        /// </summary>
        public int CurrentUserId { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public ReplyCommentCommand()
        {
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <param name="parentCommentId">父评论ID</param>
        /// <param name="content">评论内容</param>
        /// <param name="mentionedUserIds">提及的用户ID列表</param>
        /// <param name="currentUserId">当前用户ID</param>
        public ReplyCommentCommand(long taskId, long parentCommentId, string content, List<int> mentionedUserIds, int currentUserId)
        {
            TaskId = taskId;
            ParentCommentId = parentCommentId;
            Content = content;
            MentionedUserIds = mentionedUserIds ?? new();
            CurrentUserId = currentUserId;
        }
    }
}
