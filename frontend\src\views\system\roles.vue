/**
 * 航空航天级IT资产管理系统 - 角色管理页面
 * 文件路径: src/views/system/roles.vue
 * 功能描述: 系统角色的增删改查管理功能
 */

<template>
  <div class="role-management-container">
    <!-- 页面标题和操作按钮 -->
    <div class="page-header">
      <h2 class="page-title">角色管理</h2>
      <div class="page-actions">
        <el-button type="primary" @click="handleCreateRole" :icon="Plus">
          新建角色
        </el-button>
      </div>
    </div>

    <!-- 数据表格 -->
    <el-card class="data-card">
      <el-table
        ref="roleTable"
        v-loading="loading"
        :data="roleList"
        border
        style="width: 100%"
      >
        <el-table-column prop="name" label="角色名称" width="180" />
        <el-table-column prop="code" label="角色编码" width="180" />
        <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />
        <el-table-column prop="userCount" label="用户数" width="100" align="center" />
        <el-table-column prop="createTime" label="创建时间" width="170" sortable />
        <el-table-column label="操作" width="230" fixed="right">
          <template #default="scope">
            <el-button 
              type="text" 
              size="small" 
              @click="handleAssignPermissions(scope.row)"
              :icon="SetUp"
            >
              分配权限
            </el-button>
            <el-button 
              type="text" 
              size="small" 
              @click="handleEdit(scope.row)"
              :icon="Edit"
            >
              编辑
            </el-button>
            <el-button 
              type="text" 
              size="small" 
              @click="handleDelete(scope.row)"
              :icon="Delete"
              :disabled="scope.row.code === 'admin'"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Plus, SetUp, Edit, Delete
} from '@element-plus/icons-vue'

// 数据加载状态
const loading = ref(false)

// 角色列表数据
const roleList = ref([])
const roleTable = ref(null)

// 生命周期钩子
onMounted(() => {
  fetchRoleList()
})

// 获取角色列表
const fetchRoleList = () => {
  loading.value = true
  
  // 模拟API调用
  // 实际项目中应该调用API: const res = await getRoleList()
  setTimeout(() => {
    // 模拟数据
    roleList.value = [
      {
        id: 1,
        name: '系统管理员',
        code: 'admin',
        description: '系统管理员，拥有系统的所有权限',
        userCount: 3,
        createTime: '2023-01-01 08:00:00'
      },
      {
        id: 2,
        name: '资产管理员',
        code: 'asset_manager',
        description: '资产管理员，可以管理IT资产、位置和维护记录',
        userCount: 8,
        createTime: '2023-01-02 09:15:00'
      },
      {
        id: 3,
        name: '采购员',
        code: 'purchaser',
        description: '采购员，负责IT资产的采购和入库管理',
        userCount: 5,
        createTime: '2023-01-03 10:30:00'
      },
      {
        id: 4,
        name: '维护人员',
        code: 'maintenance',
        description: '维护人员，负责IT资产的故障维修和维护',
        userCount: 12,
        createTime: '2023-01-04 11:45:00'
      },
      {
        id: 5,
        name: '普通用户',
        code: 'user',
        description: '普通用户，可以查看资产信息和提交故障报告',
        userCount: 42,
        createTime: '2023-01-05 13:00:00'
      }
    ]
    
    loading.value = false
  }, 500)
}

// 分配权限
const handleAssignPermissions = (row) => {
  ElMessage.info(`为角色"${row.name}"分配权限`)
  // 实际项目中可以打开对话框分配权限
}

// 编辑角色
const handleEdit = (row) => {
  ElMessage.info(`编辑角色：${row.name}`)
  // 实际项目中可以打开编辑对话框
}

// 删除角色
const handleDelete = (row) => {
  if (row.code === 'admin') {
    ElMessage.warning('系统管理员角色不能删除')
    return
  }
  
  ElMessageBox.confirm(`确定要删除角色"${row.name}"吗？删除后将影响拥有该角色的用户权限。`, '删除角色', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'error'
  }).then(() => {
    // 模拟API调用
    // 实际项目中应该调用API: await deleteRole(row.id)
    
    ElMessage.success('角色已删除')
    
    // 刷新列表
    fetchRoleList()
  }).catch(() => {
    // 取消操作
  })
}

// 创建角色
const handleCreateRole = () => {
  ElMessage.info('打开新建角色对话框')
  // 实际项目中可以打开对话框创建角色
}
</script>

<style lang="scss" scoped>
@use "@/styles/variables.scss" as *;

.role-management-container {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    
    .page-title {
      font-size: 20px;
      font-weight: 600;
      color: #303133;
      margin: 0;
    }
    
    .page-actions {
      display: flex;
      gap: 8px;
    }
  }
  
  .data-card {
    margin-bottom: 16px;
  }
}
</style> 