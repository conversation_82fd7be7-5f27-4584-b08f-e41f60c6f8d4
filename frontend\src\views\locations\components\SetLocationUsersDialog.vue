/**
 * 航空航天级IT资产管理系统 - 设置位置使用人员对话框
 * 文件路径: src/views/locations/components/SetLocationUsersDialog.vue
 * 功能描述: 为位置设置关联人员的对话框组件
 */

<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="600px"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    @closed="handleClosed"
  >
    <div class="set-users-container">
      <!-- 位置信息 -->
      <div class="location-info">
        <div class="location-info-item">
          <span class="location-info-label">当前位置：</span>
          <span class="location-info-value">{{ locationName }}</span>
        </div>
        <div class="location-info-item" v-if="departmentInfo">
          <span class="location-info-label">使用部门：</span>
          <span class="location-info-value">{{ departmentInfo }}</span>
        </div>
      </div>
      
      <!-- 当前选中的用户列表 -->
      <div v-if="currentUsers.length > 0" class="current-users-section">
        <div class="current-users-title">当前{{ userTypeText }}列表</div>
        <el-table :data="currentUsers" size="small" border style="width: 100%">
          <el-table-column type="index" label="序号" width="60" />
          <el-table-column prop="name" label="姓名" width="120" />
          <el-table-column prop="employeeCode" label="工号" width="120" />
          <el-table-column prop="departmentName" label="部门" />
          <el-table-column label="操作" width="100">
            <template #default="scope">
              <el-button
                type="danger"
                size="small"
                circle
                @click.stop="removeUser(scope.row)"
                :icon="Delete"
              />
            </template>
          </el-table-column>
        </el-table>
      </div>
      
      <!-- 人员选择表单 -->
      <el-form :model="formData" label-width="100px" class="user-form">
        <el-form-item label="人员筛选">
          <div class="filter-row">
            <el-select
              v-model="formData.departmentId"
              placeholder="选择部门"
              clearable
              style="width: 220px; margin-right: 10px"
              @change="handleDepartmentChange"
            >
              <el-option
                v-for="dept in departmentOptions"
                :key="dept.id"
                :label="dept.name"
                :value="dept.id"
              />
            </el-select>
            
            <el-input
              v-model="formData.keyword"
              placeholder="姓名/工号"
              clearable
              style="width: 220px"
            >
              <template #append>
                <el-button :icon="Search" @click="searchPersonnel" />
              </template>
            </el-input>
          </div>
        </el-form-item>
        
        <el-form-item :label="userTypeText">
          <el-select
            v-model="formData.selectedUsers"
            multiple
            filterable
            placeholder="请选择人员"
            style="width: 100%"
            :loading="loading"
          >
            <el-option
              v-for="person in personnelOptions"
              :key="person.id"
              :label="`${person.name} ${person.employeeCode ? '(' + person.employeeCode + ')' : ''}`"
              :value="person.id"
            >
              <div class="personnel-option">
                <div>
                  <span class="personnel-name">{{ person.name }}</span>
                  <span class="personnel-code">{{ person.employeeCode }}</span>
                </div>
                <span class="personnel-dept">{{ person.departmentName }}</span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-checkbox v-model="formData.replaceExisting">替换当前{{ userTypeText }}</el-checkbox>
        </el-form-item>
      </el-form>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, computed, defineProps, defineEmits } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Delete } from '@element-plus/icons-vue'
import departmentApi from '@/api/department'
import personnelApi from '@/api/personnel'
import locationApi from '@/api/location'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  locationId: {
    type: [Number, String],
    default: ''
  },
  locationName: {
    type: String,
    default: ''
  },
  userType: {
    type: String,
    default: 'user' // 'user' 或 'manager'
  },
  departmentInfo: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update:visible', 'success', 'close'])

// 对话框可见状态
const dialogVisible = ref(false)

// 对话框标题
const dialogTitle = computed(() => {
  return props.userType === 'manager' ? '设置位置管理员' : '设置位置使用人'
})

// 用户类型文本
const userTypeText = computed(() => {
  return props.userType === 'manager' ? '管理员' : '使用人'
})

// 表单数据
const formData = reactive({
  departmentId: null,
  keyword: '',
  selectedUsers: [],
  replaceExisting: false
})

// 状态控制
const loading = ref(false)
const submitting = ref(false)

// 数据列表
const departmentOptions = ref([])
const personnelOptions = ref([])
const currentUsers = ref([])

// 监听visible变化
watch(
  () => props.visible,
  (val) => {
    dialogVisible.value = val
    if (val) {
      // 初始化表单
      initForm()
      // 加载部门和人员列表
      fetchDepartments()
      fetchPersonnel()
      // 加载当前位置关联的人员
      fetchLocationUsers()
    }
  }
)

// 监听dialogVisible变化
watch(
  () => dialogVisible.value,
  (val) => {
    emit('update:visible', val)
  }
)

// 初始化表单数据
const initForm = () => {
  formData.departmentId = null
  formData.keyword = ''
  formData.selectedUsers = []
  formData.replaceExisting = false
}

// 获取部门列表
const fetchDepartments = async () => {
  try {
    const res = await departmentApi.getDepartmentList()
    if (res && res.data && res.data.success) {
      departmentOptions.value = res.data.data || []
    } else {
      console.error('获取部门列表失败:', res?.data?.message || '未知错误')
      ElMessage.error('获取部门列表失败')
      departmentOptions.value = []
    }
  } catch (error) {
    console.error('获取部门列表出错:', error)
    ElMessage.error('获取部门列表失败: ' + (error.message || '服务器错误'))
    departmentOptions.value = []
  }
}

// 获取人员列表
const fetchPersonnel = async () => {
  loading.value = true
  try {
    const params = {}
    if (formData.departmentId) {
      params.departmentId = formData.departmentId
    }
    if (formData.keyword) {
      params.keyword = formData.keyword
    }
    
    const res = await personnelApi.getPersonnelList(params)
    if (res && res.data && res.data.success) {
      personnelOptions.value = res.data.data || []
    } else {
      console.error('获取人员列表失败:', res?.data?.message || '未知错误')
      ElMessage.error('获取人员列表失败')
      personnelOptions.value = []
    }
  } catch (error) {
    console.error('获取人员列表出错:', error)
    ElMessage.error('获取人员列表失败: ' + (error.message || '服务器错误'))
    personnelOptions.value = []
  } finally {
    loading.value = false
  }
}

// 获取位置关联的人员
const fetchLocationUsers = async () => {
  if (!props.locationId) return
  
  try {
    const res = await locationApi.getLocationUsers(props.locationId)
    if (res && res.data && res.data.success) {
      // 根据用户类型过滤
      const userType = props.userType === 'manager' ? 1 : 0
      currentUsers.value = (res.data.data || []).filter(u => u.userType === userType)
    } else {
      console.error('获取位置关联人员失败:', res?.data?.message || '未知错误')
      ElMessage.error('获取位置关联人员失败')
      currentUsers.value = []
    }
  } catch (error) {
    console.error('获取位置关联人员出错:', error)
    ElMessage.error('获取位置关联人员失败: ' + (error.message || '服务器错误'))
    currentUsers.value = []
  }
}

// 处理部门变化
const handleDepartmentChange = () => {
  fetchPersonnel()
}

// 搜索人员
const searchPersonnel = () => {
  fetchPersonnel()
}

// 移除用户
const removeUser = async (user) => {
  try {
    const response = await locationApi.removeLocationUser(props.locationId, user.id)
    if (response && response.data && response.data.success) {
      ElMessage.success('移除成功')
      await fetchLocationUsers()
    } else {
      ElMessage.error(response?.data?.message || '移除用户失败')
    }
  } catch (error) {
    console.error('移除用户失败:', error)
    ElMessage.error('移除用户失败: ' + (error.message || '服务器错误'))
  }
}

// 提交表单
const handleSubmit = async () => {
  if (formData.selectedUsers.length === 0) {
    ElMessage.warning(`请选择${userTypeText.value}`)
    return
  }
  
  if (!props.locationId) {
    ElMessage.warning('位置ID无效')
    return
  }
  
  submitting.value = true
  try {
    const users = formData.selectedUsers.map(id => ({
      personnelId: id,
      userType: props.userType === 'manager' ? 1 : 0 // 1:管理员，0:使用人
    }))
    
    const response = await locationApi.setLocationUsers(
      props.locationId,
      users,
      formData.replaceExisting
    )
    
    if (response && response.data && response.data.success) {
      ElMessage.success(`设置${userTypeText.value}成功`)
      dialogVisible.value = false
      emit('success')
    } else {
      ElMessage.error(response?.data?.message || `设置${userTypeText.value}失败`)
    }
  } catch (error) {
    console.error(`设置${userTypeText.value}失败:`, error)
    ElMessage.error(`设置${userTypeText.value}失败: ` + (error.message || '服务器错误'))
  } finally {
    submitting.value = false
  }
}

// 关闭处理
const handleClosed = () => {
  emit('close')
}
</script>

<style lang="scss" scoped>
.set-users-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.location-info {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 15px;
  background-color: #f9fafc;
}

.location-info-item {
  margin-bottom: 10px;
  display: flex;
  align-items: flex-start;
}

.location-info-label {
  font-weight: bold;
  color: #606266;
  width: 100px;
  text-align: right;
  padding-right: 15px;
}

.location-info-value {
  flex: 1;
  color: #303133;
}

.current-users-section {
  margin-top: 10px;
}

.current-users-title {
  font-weight: bold;
  margin-bottom: 10px;
  color: #606266;
  font-size: 14px;
}

.user-form {
  margin-top: 10px;
}

.filter-row {
  display: flex;
  align-items: center;
}

.personnel-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.personnel-name {
  font-weight: bold;
}

.personnel-code {
  margin-left: 5px;
  color: #909399;
  font-size: 0.9em;
}

.personnel-dept {
  color: #67c23a;
  font-size: 0.9em;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style> 