-- 验证脚本语法 - 简化测试版本
-- 这个脚本用于快速验证主要视图的语法正确性

-- 验证基础表是否存在
SELECT 'Table Verification' AS test_type;
SELECT 
    CASE WHEN COUNT(*) > 0 THEN 'EXISTS' ELSE 'MISSING' END AS assets_table
FROM information_schema.tables 
WHERE table_schema = DATABASE() AND table_name = 'assets';

SELECT 
    CASE WHEN COUNT(*) > 0 THEN 'EXISTS' ELSE 'MISSING' END AS locations_table
FROM information_schema.tables 
WHERE table_schema = DATABASE() AND table_name = 'locations';

SELECT 
    CASE WHEN COUNT(*) > 0 THEN 'EXISTS' ELSE 'MISSING' END AS departments_table
FROM information_schema.tables 
WHERE table_schema = DATABASE() AND table_name = 'departments';

-- 测试位置层级解析语法
SELECT 'Location Hierarchy Test' AS test_type;
SELECT 
    Id,
    Name,
    Path,
    CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(CONCAT(COALESCE(Path, '0'), ',0,0,0,0,0'), ',', 1), ',', -1) AS UNSIGNED) AS Level1LocationId,
    CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(CONCAT(COALESCE(Path, '0'), ',0,0,0,0,0'), ',', 2), ',', -1) AS UNSIGNED) AS Level2LocationId
FROM locations 
LIMIT 3;

-- 测试资产状态映射语法
SELECT 'Asset Status Test' AS test_type;
SELECT 
    Id,
    Status,
    CASE Status WHEN 1 THEN 1 ELSE 0 END AS IsInUse,
    CASE Status WHEN 0 THEN 1 ELSE 0 END AS IsIdle,
    CASE Status WHEN 2 THEN 1 ELSE 0 END AS IsMaintenance,
    CASE Status WHEN 4 THEN 1 ELSE 0 END AS IsFaulty,
    CASE Status WHEN 3 THEN 1 ELSE 0 END AS IsScrapped
FROM assets 
LIMIT 3;

-- 测试部门继承逻辑语法
SELECT 'Department Inheritance Test' AS test_type;
SELECT 
    a.Id AS AssetId,
    a.LocationId,
    l.DefaultDepartmentId,
    COALESCE(l.DefaultDepartmentId, 0) AS InheritedDepartmentId
FROM assets a
LEFT JOIN locations l ON a.LocationId = l.Id
LIMIT 3;

SELECT 'Syntax validation completed successfully' AS result;