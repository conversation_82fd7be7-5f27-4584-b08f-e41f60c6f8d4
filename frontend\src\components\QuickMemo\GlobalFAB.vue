// File: frontend/src/components/QuickMemo/GlobalFAB.vue
// Description: Global Floating Action Button for creating new Quick Memos.

<template>
  <div class="global-fab">
    <el-button
      type="primary"
      :icon="Plus"
      circle
      size="large"
      class="fab-button"
      @click="openDrawerForCreate"
    />
  </div>
</template>

<script setup>
import { Plus } from '@element-plus/icons-vue'
import { useQuickMemoStore } from '@/stores/modules/quickMemo'

const quickMemoStore = useQuickMemoStore()

const openDrawerForCreate = () => {
  quickMemoStore.openMemoDrawer('create')
}
</script>

<style scoped>
.global-fab {
  position: fixed;
  bottom: 30px;
  right: 30px;
  z-index: 1050;
}

.fab-button {
  width: 56px;
  height: 56px;
  font-size: 24px;
  box-shadow: var(--shadow-medium);
}

.fab-button:hover {
  transform: scale(1.05);
}
</style> 