// File: frontend/src/components/QuickMemo/QuickMemoBoard.vue
// Description: 随手记看板组件，用于展示和管理多个便利贴。

<template>
  <div class="memo-board-wrapper">
    <div class="memo-board" ref="memoBoardRef">
      <sticky-note
        v-for="memo in sortedMemos"
        :key="memo.id"
        :memo-data="memo"
      />
      <div v-if="quickMemos.length === 0 && !quickMemoStore.isLoading" class="empty-board-message">
        <el-empty description="还没有随手记，快写下你的灵感吧！" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import StickyNote from './StickyNote.vue';
import { useQuickMemoStore } from '@/stores/modules/quickMemo';
// import interact from 'interactjs'; // 移除 interactjs

const quickMemoStore = useQuickMemoStore();
const memoBoardRef = ref(null);

// 获取随手记列表的计算属性
const quickMemos = computed(() => quickMemoStore.quickMemos);

// 可选：按创建时间或更新时间排序便签，新的在前或旧的在前
const sortedMemos = computed(() => {
  return [...quickMemos.value].sort((a, b) => {
    // 假设有 isPinned 字段用于置顶，置顶的在前
    if (a.isPinned && !b.isPinned) return -1;
    if (!a.isPinned && b.isPinned) return 1;
    // 然后按更新时间倒序
    return new Date(b.updatedAt || b.createdAt) - new Date(a.updatedAt || a.createdAt);
  });
});


// 移除旧的与拖拽、层级管理相关的逻辑
// const highestZIndex = ref(1);
// const boardOffset = ref({ x: 0, y: 0 });

// const updateHighestZIndex = () => {
//   if (quickMemos.value.length > 0) {
//     highestZIndex.value = Math.max(...quickMemos.value.map(m => m.zIndex || 0), 1);
//   } else {
//     highestZIndex.value = 1;
//   }
// };

// const bringToFront = (memoId) => {
//   highestZIndex.value += 1;
//   const memo = quickMemos.value.find(m => m.id === memoId);
//   if (memo) {
//     quickMemoStore.updateQuickMemo(memoId, { ...memo, zIndex: highestZIndex.value });
//   }
// };

// const handleMemoUpdate = (updatedMemo) => {
//   quickMemoStore.updateQuickMemo(updatedMemo.id, updatedMemo);
// };

// const handleDelete = (memoToDelete) => {
//   quickMemoStore.removeQuickMemo(memoToDelete.id);
// };


// onMounted(() => {
//   quickMemoStore.fetchQuickMemos();
  // updateHighestZIndex();
  // if (memoBoardRef.value) {
  //   const rect = memoBoardRef.value.getBoundingClientRect();
  //   boardOffset.value = { x: rect.left, y: rect.top };
  // }
// });

// watch(quickMemos, () => {
  // updateHighestZIndex();
// }, { deep: true });

</script>

<style scoped>
.memo-board-wrapper {
  width: 100%;
  height: 100%;
  /* background-color: #f0f2f5; */ /* 背景颜色移至 PageView */
  /* padding: 10px; */ /* 内边距也由 PageView 控制或在此处微调 */
  box-sizing: border-box;
}

.memo-board {
  display: flex;
  flex-wrap: wrap; /* 允许卡片换行 */
  gap: 20px; /* 卡片之间的间距 */
  align-items: flex-start; /* 顶部对齐 */
  align-content: flex-start; /* 多行内容从顶部开始排列 */
  padding: 10px; /* 看板内部的内边距，使卡片不会紧贴边缘 */
  height: 100%; /* 确保 .memo-board 填充 .memo-board-wrapper */
  /* overflow-y: auto; */ /* 滚动条已移至 PageView 的 .content-panel */
}

.empty-board-message {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  height: calc(100% - 40px); /* 尝试使其在可视区域内居中 */
  color: #909399;
}

/* 移除与绝对定位和拖拽相关的旧样式 */
</style> 