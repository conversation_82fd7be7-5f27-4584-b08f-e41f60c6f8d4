#nullable enable

using System.Collections.Generic;
using System.Threading.Tasks;
using ItAssetsSystem.Models.Entities;

namespace ItAssetsSystem.Core.Abstractions
{
    /// <summary>
    /// 用户仓储接口
    /// </summary>
    public interface IUserRepository
    {
        /// <summary>
        /// 根据ID获取用户
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>用户实体</returns>
        Task<User?> GetByIdAsync(int userId);

        /// <summary>
        /// 更新用户头像
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="avatarUrl">头像URL</param>
        /// <returns>是否更新成功</returns>
        Task<bool> UpdateAvatarAsync(int userId, string? avatarUrl);

        /// <summary>
        /// 获取用户角色列表
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>角色列表</returns>
        Task<List<Role>> GetUserRolesAsync(int userId);

        /// <summary>
        /// 获取用户权限列表
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>权限列表</returns>
        Task<List<Permission>> GetUserPermissionsAsync(int userId);

        /// <summary>
        /// 更新用户信息
        /// </summary>
        /// <param name="user">用户实体</param>
        /// <returns>是否更新成功</returns>
        Task<bool> UpdateAsync(User user);
    }
} 