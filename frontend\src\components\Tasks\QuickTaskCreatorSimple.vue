<template>
  <div class="quick-task-creator">
    <!-- 触发按钮 -->
    <el-button 
      v-if="!isExpanded"
      type="primary" 
      @click="expandCreator"
      :class="triggerClass"
    >
      <el-icon><Plus /></el-icon>
      {{ triggerText }}
    </el-button>

    <!-- 快速创建表单 -->
    <div 
      v-show="isExpanded" 
      class="quick-form-container floating"
    >
      <el-card shadow="always" class="quick-form-card">
        <template #header>
          <div class="form-header">
            <span>⚡ 快速创建任务</span>
            <el-button 
              type="text" 
              @click="collapseCreator"
              class="close-btn"
            >
              <el-icon><Close /></el-icon>
            </el-button>
          </div>
        </template>

        <el-form 
          ref="quickFormRef"
          :model="quickForm"
          :rules="quickRules"
          size="small"
          @submit.prevent="handleQuickSubmit"
        >
          <!-- 任务标题 -->
          <el-form-item prop="title">
            <el-input
              v-model="quickForm.title"
              placeholder="任务标题（必填）"
              maxlength="100"
              clearable
              @keyup.enter="handleQuickSubmit"
              autofocus
            />
          </el-form-item>

          <!-- 任务内容 -->
          <el-form-item prop="description">
            <el-input
              type="textarea"
              v-model="quickForm.description"
              placeholder="任务内容/描述"
              :rows="3"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>

          <!-- 快速设置行 -->
          <div class="quick-settings">
            <!-- 负责人（多选） -->
            <el-select
              v-model="quickForm.assigneeUserIds"
              multiple
              collapse-tags
              placeholder="负责人"
              filterable
              clearable
              class="quick-assignee"
            >
              <el-option
                v-for="user in users"
                :key="user.id"
                :label="user.name"
                :value="user.id"
              />
            </el-select>

            <!-- 优先级 -->
            <el-select
              v-model="quickForm.priority"
              placeholder="优先级"
              class="quick-priority"
            >
              <el-option label="低" value="Low">
                <el-tag type="info" size="small">低</el-tag>
              </el-option>
              <el-option label="中" value="Medium">
                <el-tag type="warning" size="small">中</el-tag>
              </el-option>
              <el-option label="高" value="High">
                <el-tag type="danger" size="small">高</el-tag>
              </el-option>
            </el-select>

            <!-- 截止日期 -->
            <el-date-picker
              v-model="quickForm.dueDate"
              type="date"
              placeholder="截止日期"
              class="quick-date"
              size="small"
            />
          </div>

          <!-- 快捷操作按钮 -->
          <div class="quick-actions">
            <div class="left-actions">
              <span class="tip-text">💡 填写标题即可快速创建</span>
            </div>

            <div class="right-actions">
              <!-- 展开详细表单 -->
              <el-button 
                type="text" 
                @click="expandToFullForm"
                class="expand-btn"
              >
                <el-icon><More /></el-icon>
                详细设置
              </el-button>

              <!-- 提交按钮 -->
              <el-button 
                type="primary" 
                @click="handleQuickSubmit"
                :loading="submitting"
                size="small"
              >
                <el-icon><Check /></el-icon>
                创建
              </el-button>
            </div>
          </div>
        </el-form>
      </el-card>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, Close, Check, More } from '@element-plus/icons-vue'
import { taskApi } from '@/api/task'
import userApi from '@/api/user'
import { useUserStore } from '@/stores/modules/user'

export default {
  name: 'QuickTaskCreatorSimple',

  components: {
    Plus, Close, Check, More
  },

  props: {
    triggerText: {
      type: String,
      default: '快速创建任务'
    },
    triggerClass: {
      type: String,
      default: ''
    }
  },

  emits: ['created', 'expand', 'collapse', 'expandToFullForm'],

  setup(props, { emit }) {
    const quickFormRef = ref(null)
    const isExpanded = ref(false)
    const submitting = ref(false)
    const userStore = useUserStore()
    const users = ref([])
    const loading = ref(false)

    // 加载用户列表
    const loadUserList = async () => {
      loading.value = true
      try {
        const response = await userApi.getUserList()
        if (response && response.data) {
          users.value = response.data.map(user => ({
            id: user.id,
            name: user.name || user.userName,
            department: user.department || '未知部门'
          }))
          
          // 如果当前用户已登录，默认选择当前用户为第一个负责人
          if (userStore.userInfo && userStore.userInfo.id) {
            quickForm.assigneeUserIds = [userStore.userInfo.id]
          }
        }
      } catch (error) {
        console.error('获取用户列表失败:', error)
        ElMessage.error('获取用户列表失败，使用测试数据')
        // 加载失败时使用测试数据作为后备
        users.value = [
      { id: 1, name: '张三', department: '技术部' },
      { id: 2, name: '李四', department: '运维部' },
      { id: 3, name: '王五', department: '产品部' },
      { id: 4, name: '赵六', department: '设计部' }
        ]
        
        // 默认选择第一个测试用户
        if (users.value.length > 0) {
          quickForm.assigneeUserIds = [users.value[0].id]
        }
      } finally {
        loading.value = false
      }
    }

    // 快速表单数据
    const quickForm = reactive({
      title: '',
      description: '',
      assigneeUserIds: [], // 支持多选的负责人
      priority: 'Medium',
      dueDate: null
    })

    // 表单验证规则
    const quickRules = {
      title: [
        { required: true, message: '请输入任务标题', trigger: 'blur' },
        { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }
      ]
    }

    // 展开创建器
    const expandCreator = () => {
      isExpanded.value = true
      emit('expand')
    }

    // 收起创建器
    const collapseCreator = () => {
      isExpanded.value = false
      resetForm()
      emit('collapse')
    }

    // 重置表单
    const resetForm = () => {
      quickForm.title = ''
      quickForm.description = ''
      quickForm.assigneeUserIds = []
      quickForm.priority = 'Medium'
      quickForm.dueDate = null
      if (quickFormRef.value) {
        quickFormRef.value.clearValidate()
      }
    }

    // 快速提交
    const handleQuickSubmit = async () => {
      if (!quickFormRef.value) return

      const isValid = await quickFormRef.value.validate().catch(() => false)
      if (!isValid) return

      submitting.value = true
      try {
        // 从多选的负责人中取第一个作为主负责人
        const assigneeUserId = quickForm.assigneeUserIds.length > 0 ? quickForm.assigneeUserIds[0] : null
        
        // 剩余的负责人作为协作者
        const collaborators = quickForm.assigneeUserIds.slice(1)
        
        // 构建符合API格式的任务数据
        const today = new Date()
        const taskData = {
          name: quickForm.title,
          description: quickForm.description || '',
          assigneeUserId: assigneeUserId, // 主负责人
          collaboratorUserIds: collaborators, // 协作者(其余负责人)
          priority: quickForm.priority,
          planStartDate: formatDate(today), // 默认开始日期为今天
          planEndDate: quickForm.dueDate ? formatDate(quickForm.dueDate) : null,
          status: 'Todo',
          taskType: 'Normal'
        }

        // 调用实际的API
        const response = await taskApi.createTask(taskData)
        
        if (response.success) {
        ElMessage.success('🎉 任务创建成功！')
          emit('created', response.data)
        
        // 重置表单并收起
        resetForm()
        collapseCreator()
        } else {
          throw new Error(response.message || '创建失败')
        }
      } catch (error) {
        console.error('创建任务失败:', error)
        ElMessage.error('创建任务失败: ' + (error.message || '未知错误'))
      } finally {
        submitting.value = false
      }
    }

    // 展开到完整表单
    const expandToFullForm = () => {
      emit('expandToFullForm', {
        title: quickForm.title,
        description: quickForm.description,
        assigneeUserIds: quickForm.assigneeUserIds,
        participantUserIds: quickForm.participantUserIds,
        priority: quickForm.priority,
        dueDate: quickForm.dueDate
      })
      collapseCreator()
    }

    // 格式化日期
    const formatDate = (date) => {
      if (!date) return null
      return date.toISOString().split('T')[0]
    }

    // 设置默认日期
    const setDefaultDates = () => {
      const today = new Date()
      const oneMonthLater = new Date()
      oneMonthLater.setMonth(today.getMonth() + 1)
      
      // 默认设置截止日期为一个月后
      quickForm.dueDate = oneMonthLater
    }

    // 在组件挂载时加载用户列表和设置默认日期
    onMounted(() => {
      loadUserList()
      setDefaultDates()
    })

    return {
      quickFormRef,
      isExpanded,
      submitting,
      users,
      quickForm,
      quickRules,
      expandCreator,
      collapseCreator,
      resetForm,
      handleQuickSubmit,
      expandToFullForm,
      loading
    }
  }
}
</script>

<style scoped>
.quick-task-creator {
  position: relative;
}

.quick-form-container.floating {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 2000;
  width: 500px;
  max-width: 90vw;
}

.quick-form-card {
  border-radius: 12px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  font-size: 16px;
}

.close-btn {
  padding: 5px;
}

.quick-settings {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 16px;
}

.quick-assignee {
  flex: 3;
  margin-bottom: 10px;
  width: 100%;
}

.quick-priority {
  flex: 1;
  margin-bottom: 10px;
}

.quick-date {
  flex: 2;
  margin-bottom: 10px;
}

.quick-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
}

.left-actions,
.right-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.tip-text {
  font-size: 12px;
  color: #666;
}

.expand-btn {
  font-size: 12px;
  padding: 4px 8px;
}

/* 增强视觉效果 */
.quick-form-card::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #667eea, #764ba2, #f093fb, #f5576c);
  border-radius: 14px;
  z-index: -1;
  opacity: 0.8;
}

.el-form-item {
  margin-bottom: 16px;
}

.el-input, .el-select, .el-date-picker {
  border-radius: 8px;
}
</style>