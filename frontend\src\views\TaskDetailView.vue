<template>
  <div class="task-detail" v-if="task">
    <div class="page-header">
      <div class="header-left">
        <el-button @click="goBack" type="text" size="small">
          <el-icon><ArrowLeft /></el-icon> 返回
        </el-button>
        <h2>任务详情: {{ task.title }}</h2>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="editTask">编辑任务</el-button>
      </div>
    </div>

    <!-- 任务基本信息 -->
    <el-card shadow="hover" class="info-card">
      <div class="info-grid">
        <div class="info-item">
          <div class="item-label">任务编号:</div>
          <div class="item-value">{{ task.id }}</div>
        </div>
        <div class="info-item">
          <div class="item-label">任务类型:</div>
          <div class="item-value">
            <el-tag :type="getTaskTypeStyle(task.type).type">{{ getTaskTypeStyle(task.type).label }}</el-tag>
          </div>
        </div>
        <div class="info-item">
          <div class="item-label">状态:</div>
          <div class="item-value">
            <el-select v-model="task.status" size="small" @change="updateTaskStatus">
              <el-option v-for="status in taskStatusOptions" :key="status.value" :label="status.label" :value="status.value"></el-option>
            </el-select>
          </div>
        </div>
        <div class="info-item">
          <div class="item-label">优先级:</div>
          <div class="item-value">
            <el-tag :type="getPriorityType(task.priority)">{{ getPriorityLabel(task.priority) }}</el-tag>
          </div>
        </div>
        <div class="info-item">
          <div class="item-label">创建时间:</div>
          <div class="item-value">{{ task.createDate }}</div>
        </div>
        <div class="info-item">
          <div class="item-label">截止时间:</div>
          <div class="item-value" :class="{ 'text-danger': isOverdue(task.dueDate) && task.status !== 'completed' }">
            {{ task.dueDate }}
          </div>
        </div>
        <div class="info-item span-full">
          <div class="item-label">负责人:</div>
          <div class="item-value">
            <el-avatar-group>
              <el-tooltip v-for="userId in task.assignees" :key="userId" :content="getUserById(userId)?.name" placement="top">
                <el-avatar :size="32" :src="getUserById(userId)?.avatar" />
              </el-tooltip>
            </el-avatar-group>
          </div>
        </div>
      </div>

      <el-divider />
      
      <div class="description-section">
        <div class="section-header">任务描述:</div>
        <div class="section-content">{{ task.description }}</div>
      </div>
    </el-card>

    <!-- 任务详情选项卡 -->
    <el-card shadow="hover" class="tabs-card">
      <el-tabs type="border-card">
        <el-tab-pane label="评论">
          <div class="comment-form">
            <el-input
              v-model="newComment"
              type="textarea"
              :rows="3"
              placeholder="输入评论内容，使用 @ 提及同事..."
            />
            <el-button type="primary" @click="addComment" style="margin-top: 10px;">发表评论</el-button>
          </div>
          
          <div class="comments-list">
            <div v-for="comment in taskComments" :key="comment.id" class="comment-item">
              <el-avatar :size="40" :src="getUserById(comment.userId)?.avatar" />
              <div class="comment-content">
                <div class="comment-header">
                  <span class="comment-user">{{ getUserById(comment.userId)?.name }}</span>
                  <span class="comment-time">{{ formatTimeAgo(comment.time) }}</span>
                </div>
                <div class="comment-text">{{ comment.content }}</div>
              </div>
            </div>
            <el-empty v-if="taskComments.length === 0" description="暂无评论"></el-empty>
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="附件">
          <el-upload
            action="#"
            :auto-upload="false"
            multiple
            :limit="5"
            list-type="text"
          >
            <template #trigger>
              <el-button type="primary">选择文件</el-button>
            </template>
            <template #tip>
              <div class="el-upload__tip">
                单个文件不超过10MB，支持各种格式文件
              </div>
            </template>
          </el-upload>
          
          <div class="attachments-list">
            <div v-for="attachment in task.attachments" :key="attachment.id" class="attachment-item">
              <el-icon><Document /></el-icon>
              <span class="attachment-name">{{ attachment.name }}</span>
              <span class="attachment-size">{{ formatFileSize(attachment.size) }}</span>
              <div class="attachment-actions">
                <el-button type="primary" link size="small">下载</el-button>
                <el-button type="danger" link size="small" @click="deleteAttachment(attachment.id)">删除</el-button>
              </div>
            </div>
            <el-empty v-if="!task.attachments || task.attachments.length === 0" description="暂无附件"></el-empty>
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="活动日志">
          <div class="activity-logs">
            <div v-for="log in taskLogs" :key="log.id" class="activity-item">
              <el-avatar :size="28" :src="getUserById(log.userId)?.avatar" />
              <div class="activity-content">
                <span class="activity-text">
                  <span class="activity-user">{{ getUserById(log.userId)?.name }}</span>
                  {{ log.action }}
                </span>
                <span class="activity-time">{{ formatTimeAgo(log.time) }}</span>
              </div>
            </div>
            <el-empty v-if="taskLogs.length === 0" description="暂无活动记录"></el-empty>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
  <div v-else class="task-not-found">
    <el-empty description="任务不存在或已被删除">
      <template #description>
        <p>任务不存在或已被删除</p>
      </template>
      <el-button @click="goBack">返回任务列表</el-button>
    </el-empty>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ArrowLeft, Document } from '@element-plus/icons-vue'

const router = useRouter()
const route = useRoute()
const taskId = route.params.id as string

// 团队成员
const teamMembers = reactive([
  { id: 'user1', name: '张三', avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png' },
  { id: 'user2', name: '李四', avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png' },
  { id: 'user3', name: '王五', avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png' },
  { id: 'user4', name: '赵六', avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png' }
])

// 任务状态选项
const taskStatusOptions = [
  { label: '未开始', value: 'unstarted' },
  { label: '进行中', value: 'in-progress' },
  { label: '待审核', value: 'review' },
  { label: '已完成', value: 'completed' },
  { label: '已逾期', value: 'overdue' }
]

// 模拟任务数据
const allTasks = reactive([
  {
    id: 'task1',
    title: '优化用户注册流程',
    description: '重新设计注册表单，简化注册步骤，提高转化率。需要重点关注移动端适配和表单验证体验。',
    type: 'daily',
    status: 'unstarted',
    priority: 'high',
    assignees: ['user1', 'user2'],
    createDate: '2024-05-01',
    dueDate: '2024-05-10',
    mentioned: true,
    attachments: [
      { id: 'att1', name: '注册流程设计图.pdf', size: 2048000, uploadTime: new Date(2024, 4, 1) },
      { id: 'att2', name: '用户调研报告.docx', size: 1024000, uploadTime: new Date(2024, 4, 2) }
    ]
  },
  {
    id: 'task2',
    title: '修复移动端显示问题',
    description: '解决在小屏设备上页面布局错乱的问题',
    type: 'daily',
    status: 'in-progress',
    priority: 'medium',
    assignees: ['user1'],
    createDate: '2024-05-02',
    dueDate: '2024-05-12',
    mentioned: false,
    attachments: []
  }
])

// 当前任务
const task = computed(() => {
  return allTasks.find(t => t.id === taskId) || null
})

// 新评论
const newComment = ref('')

// 任务评论
const taskComments = reactive([
  { id: 'c1', userId: 'user2', content: '这个需求优先级很高，我们需要尽快完成', time: new Date(2024, 4, 2, 10, 30) },
  { id: 'c2', userId: 'user1', content: '已经开始处理了，预计明天可以完成初稿', time: new Date(2024, 4, 2, 14, 15) }
])

// 任务活动日志
const taskLogs = reactive([
  { id: 'log1', userId: 'user1', action: '创建了任务', time: new Date(2024, 4, 1, 9, 0) },
  { id: 'log2', userId: 'user2', action: '被添加为任务负责人', time: new Date(2024, 4, 1, 10, 30) },
  { id: 'log3', userId: 'user1', action: '将优先级从"中"修改为"高"', time: new Date(2024, 4, 2, 15, 0) }
])

// 方法
function goBack() {
  router.back()
}

function editTask() {
  console.log('编辑任务', taskId)
}

function updateTaskStatus(status: string) {
  console.log('更新任务状态', status)
  // 记录活动日志
  taskLogs.unshift({
    id: `log${taskLogs.length + 1}`,
    userId: 'user1', // 当前用户
    action: `将任务状态修改为"${getStatusLabel(status)}"`,
    time: new Date()
  })
}

function getStatusLabel(status: string): string {
  const option = taskStatusOptions.find(opt => opt.value === status)
  return option ? option.label : status
}

function getUserById(userId: string) {
  return teamMembers.find(user => user.id === userId)
}

function getTaskTypeStyle(type: string) {
  switch (type) {
    case 'daily':
      return { label: '日常任务', type: 'info' }
    case 'periodic':
      return { label: '周期任务', type: 'success' }
    case 'pdca':
      return { label: 'PDCA任务', type: 'warning' }
    default:
      return { label: '未知类型', type: 'info' }
  }
}

function getPriorityLabel(priority: string): string {
  switch (priority) {
    case 'high': return '高'
    case 'medium': return '中'
    case 'low': return '低'
    default: return priority
  }
}

function getPriorityType(priority: string): string {
  switch (priority) {
    case 'high': return 'danger'
    case 'medium': return 'warning'
    case 'low': return 'info'
    default: return 'info'
  }
}

function isOverdue(dueDate: string): boolean {
  const today = new Date()
  const due = new Date(dueDate)
  return due < today
}

function formatTimeAgo(date: Date): string {
  const now = new Date()
  const diffInMs = now.getTime() - date.getTime()
  const diffInMinutes = Math.floor(diffInMs / (1000 * 60))
  const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60))
  const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24))
  
  if (diffInMinutes < 60) {
    return `${diffInMinutes} 分钟前`
  } else if (diffInHours < 24) {
    return `${diffInHours} 小时前`
  } else if (diffInDays < 30) {
    return `${diffInDays} 天前`
  } else {
    return date.toLocaleDateString()
  }
}

function formatFileSize(bytes: number): string {
  if (bytes < 1024) {
    return bytes + ' B'
  } else if (bytes < 1024 * 1024) {
    return (bytes / 1024).toFixed(1) + ' KB'
  } else {
    return (bytes / (1024 * 1024)).toFixed(1) + ' MB'
  }
}

function addComment() {
  if (!newComment.value.trim()) return
  
  taskComments.unshift({
    id: `c${taskComments.length + 1}`,
    userId: 'user1', // 当前用户
    content: newComment.value,
    time: new Date()
  })
  
  // 记录活动日志
  taskLogs.unshift({
    id: `log${taskLogs.length + 1}`,
    userId: 'user1', // 当前用户
    action: '添加了评论',
    time: new Date()
  })
  
  newComment.value = ''
}

function deleteAttachment(attachmentId: string) {
  if (!task.value) return
  
  const index = task.value.attachments.findIndex(att => att.id === attachmentId)
  if (index !== -1) {
    const fileName = task.value.attachments[index].name
    task.value.attachments.splice(index, 1)
    
    // 记录活动日志
    taskLogs.unshift({
      id: `log${taskLogs.length + 1}`,
      userId: 'user1', // 当前用户
      action: `删除了附件 "${fileName}"`,
      time: new Date()
    })
  }
}
</script>

<style scoped>
.task-detail {
  padding-bottom: 40px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 10px;
}

.header-left h2 {
  margin: 0;
  font-size: 20px;
}

.info-card, .tabs-card {
  margin-bottom: 20px;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
}

.info-item {
  display: flex;
  flex-direction: column;
}

.span-full {
  grid-column: 1 / span 3;
}

.item-label {
  color: #909399;
  margin-bottom: 5px;
  font-size: 14px;
}

.item-value {
  font-weight: 500;
}

.description-section {
  margin-top: 20px;
}

.section-header {
  color: #909399;
  margin-bottom: 10px;
  font-size: 14px;
}

.section-content {
  white-space: pre-line;
  line-height: 1.6;
}

.text-danger {
  color: #F56C6C;
  font-weight: bold;
}

.comments-list, .attachments-list, .activity-logs {
  margin-top: 20px;
}

.comment-item {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #EBEEF5;
}

.comment-content {
  flex: 1;
}

.comment-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}

.comment-user {
  font-weight: 500;
}

.comment-time {
  color: #909399;
  font-size: 12px;
}

.comment-text {
  white-space: pre-line;
  line-height: 1.5;
}

.attachment-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px 0;
  border-bottom: 1px solid #EBEEF5;
}

.attachment-name {
  flex: 1;
  font-weight: 500;
}

.attachment-size {
  color: #909399;
  font-size: 12px;
  min-width: 70px;
}

.attachment-actions {
  display: flex;
  gap: 10px;
}

.activity-item {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
}

.activity-content {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 15px;
  border-bottom: 1px solid #EBEEF5;
}

.activity-user {
  font-weight: 500;
  margin-right: 5px;
}

.activity-time {
  color: #909399;
  font-size: 12px;
}

.task-not-found {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50vh;
}
</style> 