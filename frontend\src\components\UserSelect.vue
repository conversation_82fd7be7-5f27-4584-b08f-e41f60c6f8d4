<template>
  <el-select
    :model-value="modelValue"
    @update:model-value="handleChange"
    :placeholder="placeholder"
    :multiple="multiple"
    :filterable="filterable"
    :remote="remote"
    :remote-method="handleRemoteSearch"
    :loading="loading"
    :clearable="clearable"
    :size="size"
    class="user-select"
  >
    <el-option
      v-for="user in userOptions"
      :key="user.id"
      :label="user.name"
      :value="user.id"
      class="user-option"
    >
      <div class="user-option-content">
        <el-avatar
          :src="user.avatarUrl"
          :size="20"
          class="user-avatar"
        >
          {{ user.name?.charAt(0) || '?' }}
        </el-avatar>
        <div class="user-info">
          <span class="user-name">{{ user.name }}</span>
          <span v-if="user.department" class="user-department">
            {{ user.department }}
          </span>
        </div>
        <div v-if="showWorkload && user.currentTasks !== undefined" class="user-workload">
          <el-tag size="small" :type="getWorkloadTagType(user.currentTasks)">
            {{ user.currentTasks }}个任务
          </el-tag>
        </div>
      </div>
    </el-option>
  </el-select>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useUserStore } from '@/stores/modules/user'

const props = defineProps({
  modelValue: {
    type: [Number, Array],
    default: null
  },
  placeholder: {
    type: String,
    default: '选择用户'
  },
  multiple: {
    type: Boolean,
    default: false
  },
  filterable: {
    type: Boolean,
    default: true
  },
  remote: {
    type: Boolean,
    default: true
  },
  clearable: {
    type: Boolean,
    default: true
  },
  size: {
    type: String,
    default: 'default'
  },
  showWorkload: {
    type: Boolean,
    default: false
  },
  // 用户过滤条件
  userFilter: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

// Store
const userStore = useUserStore()

// 响应式状态
const userOptions = ref([])
const loading = ref(false)
const searchKeyword = ref('')

// 计算属性
const allUsers = computed(() => userStore.users || [])

// 监听器
watch(() => props.userFilter, () => {
  loadUsers()
}, { deep: true })

// 生命周期
onMounted(() => {
  loadUsers()
})

// 方法
const loadUsers = async (keyword = '') => {
  loading.value = true
  try {
    let users = []
    
    if (keyword) {
      // 远程搜索
      users = await userStore.searchUsers(keyword)
    } else {
      // 获取所有用户或应用过滤条件
      users = await userStore.getUsers(props.userFilter)
    }
    
    userOptions.value = users.map(user => ({
      id: user.id,
      name: user.name || user.username,
      avatarUrl: user.avatarUrl || user.avatar,
      department: user.department?.name || user.departmentName,
      currentTasks: user.currentTasks || 0,
      isActive: user.isActive !== false
    })).filter(user => user.isActive)
    
  } catch (error) {
    console.error('加载用户列表失败:', error)
    // 使用缓存的用户数据作为降级
    userOptions.value = allUsers.value.map(user => ({
      id: user.id,
      name: user.name || user.username,
      avatarUrl: user.avatarUrl,
      department: user.department,
      currentTasks: 0,
      isActive: true
    }))
  } finally {
    loading.value = false
  }
}

const handleRemoteSearch = (keyword) => {
  searchKeyword.value = keyword
  if (keyword) {
    loadUsers(keyword)
  } else {
    loadUsers()
  }
}

const handleChange = (value) => {
  emit('update:modelValue', value)
  emit('change', value)
}

const getWorkloadTagType = (taskCount) => {
  if (taskCount === 0) return 'success'
  if (taskCount <= 3) return ''
  if (taskCount <= 6) return 'warning'
  return 'danger'
}
</script>

<style scoped>
.user-select {
  width: 100%;
}

.user-option-content {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 0;
}

.user-avatar {
  flex-shrink: 0;
}

.user-info {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-width: 0;
}

.user-name {
  font-size: 13px;
  font-weight: 500;
  color: #303133;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.user-department {
  font-size: 11px;
  color: #909399;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.user-workload {
  flex-shrink: 0;
}

/* 暗色主题支持 */
.dark .user-name {
  color: #e0e0e0;
}

.dark .user-department {
  color: #a0a0a0;
}
</style>