import axios from 'axios';
import { formatDate } from '@/utils/format';
import systemConfig from '@/config/system';
import request from '@/utils/request';

const baseUrl = '';

const personnelApi = {
  /**
   * 获取人员列表
   * @param {Object} params 查询参数
   * @param {Number} params.departmentId 部门ID
   * @param {String} params.keyword 关键词
   * @returns {Promise<Object>} 响应结果
   */
  getPersonnelList(params = {}) {
    // 确保URL没有重复的/api前缀
    const apiPath = '/Personnel';
    
    console.log('获取人员列表 - API调用', {
      请求方法: 'GET',
      请求路径: apiPath,
      完整URL: `${systemConfig.apiBaseUrl}${apiPath}`,
      查询参数: params
    });
    
    return request.get(apiPath, { params })
      .then(response => {
        // 确保返回一致的数据结构
        console.log('人员列表API响应:', response);
        
        // 如果response已经是包装好的对象，直接返回
        if (response && typeof response === 'object' && 'success' in response) {
          return { data: response };
        }
        
        // 否则包装成统一的格式
        return { 
          data: { 
            success: true, 
            data: Array.isArray(response) ? response : (response?.data || []),
            message: '获取成功'
          } 
        };
      })
      .catch(error => {
        console.error('获取人员列表失败:', error);
        return { 
          data: { 
            success: false, 
            data: [],
            message: error.response?.data?.message || error.message || '服务器错误' 
          } 
        };
      });
  },

  /**
   * 获取人员详情
   * @param {Number} id 人员ID
   * @returns {Promise<Object>} 人员详情
   */
  getPersonnelById(id) {
    console.log('获取人员详情 - API调用', {
      请求方法: 'GET',
      请求路径: `/Personnel/${id}`,
      完整URL: `${systemConfig.apiBaseUrl}/Personnel/${id}`
    });
    return request.get(`/Personnel/${id}`)
      .then(response => {
        return { data: response };
      })
      .catch(error => {
        console.error('获取人员详情失败:', error);
        return { data: { success: false, message: error.message || '服务器错误' } };
      });
  },

  /**
   * 创建人员
   * @param {Object} data 人员数据
   */
  createPersonnel(data) {
    // 确保数据正确格式化
    const submitData = {
      name: data.name,
      position: data.position || '',
      contact: data.contact || '',
      departmentId: data.departmentId,
      employeeCode: data.employeeCode || ''
    };
    
    // 确保URL没有重复的/api前缀
    const apiPath = '/Personnel';
    
    // 记录完整的请求信息用于调试
    console.log('创建人员 - 请求详情', {
      方法: 'POST',
      请求路径: apiPath,
      完整URL: `${systemConfig.apiBaseUrl}${apiPath}`,
      数据: submitData
    });
    
    // 使用request工具而不是直接使用axios
    return request.post(apiPath, submitData)
      .then(response => {
        console.log('创建人员 - 响应成功:', response);
        return { data: response };
      })
      .catch(error => {
        console.error('创建人员 - 响应错误:', error);
        // 始终返回统一格式的响应便于前端处理
        return { 
          data: { 
            success: false, 
            message: error.response?.data?.message || error.message || '服务器错误' 
          } 
        };
      });
  },

  /**
   * 更新人员
   * @param {number} id 人员ID
   * @param {Object} data 人员数据
   */
  updatePersonnel(id, data) {
    const submitData = {
      name: data.name,
      position: data.position || '',
      contact: data.contact || '',
      departmentId: data.departmentId,
      employeeCode: data.employeeCode || ''
    };
    
    // 确保URL没有重复的/api前缀
    const apiPath = `/Personnel/${id}`;
    
    console.log('更新人员 - API调用', {
      请求方法: 'PUT',
      请求路径: apiPath,
      完整URL: `${systemConfig.apiBaseUrl}${apiPath}`,
      人员ID: id,
      请求数据: submitData
    });
    
    return request.put(apiPath, submitData)
      .then(response => {
        console.log('更新人员 - 响应成功:', response);
        return { data: response };
      })
      .catch(error => {
        console.error('更新人员失败:', error);
        return { 
          data: { 
            success: false, 
            message: error.response?.data?.message || error.message || '服务器错误' 
          } 
        };
      });
  },

  /**
   * 删除人员
   * @param {Number} id 人员ID
   * @returns {Promise<Object>} 删除结果
   */
  deletePersonnel(id) {
    // 确保URL没有重复的/api前缀
    const apiPath = `/Personnel/${id}`;
    
    console.log('删除人员 - API调用', {
      请求方法: 'DELETE',
      请求路径: apiPath,
      完整URL: `${systemConfig.apiBaseUrl}${apiPath}`,
      人员ID: id
    });
    
    return request.delete(apiPath)
      .then(response => {
        console.log('删除人员 - 响应成功:', response);
        return { data: response };
      })
      .catch(error => {
        console.error('删除人员失败:', error);
        return { 
          data: { 
            success: false, 
            message: error.response?.data?.message || error.message || '服务器错误' 
          } 
        };
      });
  }
};

export default personnelApi; 