<template>
  <div class="standardized-gamification-dashboard">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">
        <i class="fas fa-trophy"></i>
        标准化游戏化系统
      </h1>
      <p class="page-description">
        基于行业最佳实践的标准化行为追踪与游戏化激励系统
      </p>
    </div>

    <!-- 系统状态卡片 -->
    <div class="status-cards">
      <div class="status-card">
        <div class="card-icon">
          <i class="fas fa-database"></i>
        </div>
        <div class="card-content">
          <h3>行为追踪</h3>
          <p class="card-value">{{ behaviorStats.totalEvents || 0 }}</p>
          <p class="card-label">总事件数</p>
        </div>
      </div>

      <div class="status-card">
        <div class="card-icon">
          <i class="fas fa-users"></i>
        </div>
        <div class="card-content">
          <h3>活跃用户</h3>
          <p class="card-value">{{ behaviorStats.activeUsers || 0 }}</p>
          <p class="card-label">本周活跃</p>
        </div>
      </div>

      <div class="status-card">
        <div class="card-icon">
          <i class="fas fa-coins"></i>
        </div>
        <div class="card-content">
          <h3>总积分</h3>
          <p class="card-value">{{ behaviorStats.totalPoints || 0 }}</p>
          <p class="card-label">系统总积分</p>
        </div>
      </div>

      <div class="status-card">
        <div class="card-icon">
          <i class="fas fa-chart-line"></i>
        </div>
        <div class="card-content">
          <h3>系统状态</h3>
          <p class="card-value">{{ systemStatus }}</p>
          <p class="card-label">运行状态</p>
        </div>
      </div>
    </div>

    <!-- 功能区域 -->
    <div class="function-areas">
      <!-- 实时数据区域 -->
      <div class="function-card">
        <h3>
          <i class="fas fa-chart-line"></i>
          实时业务数据
        </h3>
        <div class="real-data-display">
          <div class="data-item">
            <span class="data-label">今日任务完成:</span>
            <span class="data-value">{{ realTimeData.todayTasksCompleted }}</span>
          </div>
          <div class="data-item">
            <span class="data-label">今日资产创建:</span>
            <span class="data-value">{{ realTimeData.todayAssetsCreated }}</span>
          </div>
          <div class="data-item">
            <span class="data-label">今日故障处理:</span>
            <span class="data-value">{{ realTimeData.todayFaultsHandled }}</span>
          </div>
          <div class="data-item">
            <span class="data-label">系统总积分:</span>
            <span class="data-value">{{ realTimeData.totalSystemPoints }}</span>
          </div>
        </div>
        <div class="refresh-button">
          <button @click="refreshRealTimeData" :disabled="loading" class="btn-primary">
            <i class="fas fa-sync-alt"></i>
            刷新数据
          </button>
        </div>
      </div>

      <!-- 统计区域 -->
      <div class="function-card">
        <h3>
          <i class="fas fa-chart-bar"></i>
          用户统计
        </h3>
        <div class="stats-controls">
          <div class="stats-group">
            <label>查询用户:</label>
            <input v-model="statsUserId" type="number" min="1" />
          </div>
          <div class="stats-group">
            <label>统计周期:</label>
            <select v-model="statsPeriod">
              <option value="daily">日统计</option>
              <option value="weekly">周统计</option>
              <option value="monthly">月统计</option>
            </select>
          </div>
          <button @click="getUserStats" :disabled="loading" class="btn-info">
            <i class="fas fa-search"></i>
            查询统计
          </button>
        </div>
        
        <!-- 统计结果 -->
        <div v-if="userStats" class="stats-result">
          <div class="stats-grid">
            <div class="stat-item">
              <span class="stat-label">任务创建:</span>
              <span class="stat-value">{{ userStats.TasksCreated || 0 }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">任务完成:</span>
              <span class="stat-value">{{ userStats.TasksCompleted || 0 }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">总积分:</span>
              <span class="stat-value">{{ userStats.TotalPoints || 0 }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">总金币:</span>
              <span class="stat-value">{{ userStats.TotalCoins || 0 }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 排行榜区域 -->
    <div class="leaderboard-section">
      <h3>
        <i class="fas fa-trophy"></i>
        实时排行榜
      </h3>
      <div class="leaderboard-controls">
        <div class="control-group">
          <label>排行类型:</label>
          <select v-model="leaderboardType">
            <option value="points">积分排行</option>
            <option value="coins">金币排行</option>
            <option value="tasks">任务完成排行</option>
            <option value="productivity">生产力排行</option>
          </select>
        </div>

        <div class="control-group">
          <label>显示范围:</label>
          <select v-model="leaderboardScope">
            <option value="top10">前10名</option>
            <option value="top20">前20名</option>
            <option value="department">本部门</option>
            <option value="active">活跃用户</option>
            <option value="around">我的周围</option>
          </select>
        </div>

        <div class="control-group">
          <label>时间范围:</label>
          <select v-model="leaderboardPeriod">
            <option value="weekly">本周</option>
            <option value="monthly">本月</option>
            <option value="daily">今日</option>
          </select>
        </div>

        <button @click="getLeaderboard" :disabled="loading" class="btn-success">
          <i class="fas fa-refresh"></i>
          刷新排行榜
        </button>
      </div>
      
      <!-- 当前用户排名信息 -->
      <div v-if="currentUserRank && leaderboardScope !== 'around'" class="current-user-rank">
        <div class="rank-info">
          <i class="fas fa-user"></i>
          <span>我的排名: 第{{ currentUserRank.rank }}名</span>
          <span class="score">{{ currentUserRank.totalPointsEarned || 0 }}积分</span>
        </div>
      </div>

      <div v-if="leaderboard.length > 0" class="leaderboard-table">
        <div class="table-header">
          <h4>{{ getLeaderboardTitle() }}</h4>
          <span class="result-count">显示 {{ leaderboard.length }} 条结果</span>
        </div>

        <table>
          <thead>
            <tr>
              <th>排名</th>
              <th>用户</th>
              <th>部门</th>
              <th>积分</th>
              <th>金币</th>
              <th>任务完成</th>
              <th>生产力评分</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="user in leaderboard" :key="user.UserId"
                :class="{
                  'top-three': user.Rank <= 3,
                  'current-user': user.UserId === statsUserId,
                  'highlight-user': user.UserId === statsUserId
                }">
              <td>
                <span class="rank-badge" :class="getRankClass(user.Rank)">
                  {{ user.Rank }}
                  <i v-if="user.UserId === statsUserId" class="fas fa-user-circle current-user-icon"></i>
                </span>
              </td>
              <td class="user-info">
                <strong>{{ user.UserName }}</strong>
                <span v-if="user.UserId === statsUserId" class="current-user-label">(我)</span>
              </td>
              <td>{{ user.DepartmentName }}</td>
              <td class="points">{{ user.TotalPoints }}</td>
              <td class="coins">{{ user.TotalCoins }}</td>
              <td class="tasks">{{ user.TasksCompleted }}</td>
              <td class="productivity">{{ user.ProductivityScore?.toFixed(1) || '0.0' }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- 操作日志 -->
    <div class="operation-log">
      <h3>
        <i class="fas fa-history"></i>
        操作日志
      </h3>
      <div class="log-container">
        <div v-for="(log, index) in operationLogs" :key="index" class="log-item" :class="log.type">
          <span class="log-time">{{ formatTime(log.time) }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import request from '@/utils/request'

export default {
  name: 'StandardizedGamificationDashboard',
  setup() {
    // 响应式数据
    const loading = ref(false)
    const systemStatus = ref('正常运行')

    // 实时数据
    const realTimeData = reactive({
      todayTasksCompleted: 0,
      todayAssetsCreated: 0,
      todayFaultsHandled: 0,
      totalSystemPoints: 0
    })

    // 统计相关
    const statsUserId = ref(6)
    const statsPeriod = ref('weekly')
    const userStats = ref(null)
    
    // 排行榜相关
    const leaderboardType = ref('points')
    const leaderboardScope = ref('top10')
    const leaderboardPeriod = ref('weekly')
    const leaderboard = ref([])
    const currentUserRank = ref(null) // 当前用户排名信息
    
    // 系统统计
    const behaviorStats = reactive({
      totalEvents: 0,
      activeUsers: 0,
      totalPoints: 0
    })
    
    // 操作日志
    const operationLogs = ref([])

    // 添加日志
    const addLog = (message, type = 'info') => {
      operationLogs.value.unshift({
        time: new Date(),
        message,
        type
      })
      if (operationLogs.value.length > 50) {
        operationLogs.value.pop()
      }
    }

    // 刷新实时数据
    const refreshRealTimeData = async () => {
      loading.value = true
      try {
        // 获取工作汇总数据
        const workSummaryResponse = await request.get('/work-summary', {
          params: {
            periodType: 'daily',
            limit: 100
          }
        })

        if (workSummaryResponse.success) {
          const summaries = workSummaryResponse.data

          // 计算今日统计
          realTimeData.todayTasksCompleted = summaries.reduce((sum, item) => sum + (item.TasksCompleted || 0), 0)
          realTimeData.todayAssetsCreated = summaries.reduce((sum, item) => sum + (item.AssetsCreated || 0), 0)
          realTimeData.todayFaultsHandled = summaries.reduce((sum, item) => sum + (item.FaultsReported || 0), 0)
          realTimeData.totalSystemPoints = summaries.reduce((sum, item) => sum + (item.TotalPointsEarned || 0), 0)

          addLog('实时数据刷新成功', 'success')
          ElMessage.success('实时数据刷新成功')
        }
      } catch (error) {
        ElMessage.error('刷新实时数据失败: ' + error.message)
        addLog(`刷新实时数据失败: ${error.message}`, 'error')
      } finally {
        loading.value = false
      }
    }

    // 获取用户统计
    const getUserStats = async () => {
      loading.value = true
      try {
        const response = await request.get('/work-summary', {
          params: {
            periodType: statsPeriod.value,
            limit: 1,
            userId: statsUserId.value
          }
        })

        if (response.success && response.data.length > 0) {
          userStats.value = response.data[0]
          addLog(`获取用户统计成功: 用户${statsUserId.value} (${statsPeriod.value})`, 'info')
        } else {
          userStats.value = null
          addLog(`用户${statsUserId.value}暂无统计数据`, 'info')
        }
      } catch (error) {
        ElMessage.error('获取用户统计失败: ' + error.message)
        addLog(`获取用户统计失败: ${error.message}`, 'error')
      } finally {
        loading.value = false
      }
    }

    // 获取排行榜
    const getLeaderboard = async () => {
      loading.value = true
      try {
        // 根据显示范围确定查询参数
        const queryParams = getLeaderboardQueryParams()

        try {
          // 使用工作汇总数据作为排行榜数据源
          const response = await request.get('/v2/work-summary', {
            params: queryParams
          })

          if (response.success && response.data) {
            // 处理排行榜数据
            const processedData = processLeaderboardData(response.data)
            leaderboard.value = processedData.leaderboard
            currentUserRank.value = processedData.currentUserRank

            addLog(`排行榜数据加载成功: ${leaderboard.value.length}条记录 (${leaderboardScope.value})`, 'info')
          } else {
            throw new Error('工作汇总API返回失败')
          }
        } catch (apiError) {
          // 使用模拟数据作为后备
          const mockData = generateMockLeaderboard()
          leaderboard.value = mockData.leaderboard
          currentUserRank.value = mockData.currentUserRank

          addLog(`使用模拟排行榜数据: ${leaderboardType.value}排行 (${leaderboardScope.value})`, 'info')
        }
        ElMessage.success('排行榜刷新成功')
      } catch (error) {
        ElMessage.error('获取排行榜失败: ' + error.message)
        addLog(`获取排行榜失败: ${error.message}`, 'error')
      } finally {
        loading.value = false
      }
    }

    // 获取排名样式类
    const getRankClass = (rank) => {
      if (rank === 1) return 'rank-gold'
      if (rank === 2) return 'rank-silver'
      if (rank === 3) return 'rank-bronze'
      return 'rank-normal'
    }

    // 格式化时间
    const formatTime = (time) => {
      return time.toLocaleTimeString()
    }

    // 获取排行榜查询参数
    const getLeaderboardQueryParams = () => {
      const baseParams = {
        periodType: leaderboardPeriod.value
      }

      switch (leaderboardScope.value) {
        case 'top10':
          return { ...baseParams, limit: 10 }
        case 'top20':
          return { ...baseParams, limit: 20 }
        case 'department':
          // 如果有当前用户部门信息，可以添加部门筛选
          return { ...baseParams, limit: 50 } // 暂时返回更多数据，前端筛选
        case 'active':
          // 只显示有活动记录的用户
          return { ...baseParams, limit: 30, activeOnly: true }
        case 'around':
          // 显示当前用户周围的排名
          return { ...baseParams, limit: 50, aroundUser: statsUserId.value }
        default:
          return { ...baseParams, limit: 10 }
      }
    }

    // 处理排行榜数据
    const processLeaderboardData = (rawData) => {
      // 转换工作汇总数据为排行榜格式
      let processedLeaderboard = rawData.map((item, index) => ({
        Rank: index + 1,
        UserId: item.userId,
        UserName: item.userName,
        DepartmentName: item.departmentName,
        TotalPoints: item.totalPointsEarned || 0,
        TotalCoins: item.totalCoinsEarned || 0,
        TasksCompleted: item.tasksCompleted || 0,
        ProductivityScore: item.productivityScore || 0
      }))

      // 根据显示范围进行筛选
      if (leaderboardScope.value === 'department') {
        // 假设当前用户在IT部门，实际应该从用户信息获取
        const currentUserDept = 'IT部门'
        processedLeaderboard = processedLeaderboard.filter(user =>
          user.DepartmentName === currentUserDept
        )
      } else if (leaderboardScope.value === 'active') {
        // 只显示有实际活动的用户（积分>0或任务完成>0）
        processedLeaderboard = processedLeaderboard.filter(user =>
          user.TotalPoints > 0 || user.TasksCompleted > 0
        )
      } else if (leaderboardScope.value === 'around') {
        // 显示当前用户周围的排名
        const currentUserIndex = processedLeaderboard.findIndex(user =>
          user.UserId === statsUserId.value
        )
        if (currentUserIndex >= 0) {
          const start = Math.max(0, currentUserIndex - 5)
          const end = Math.min(processedLeaderboard.length, currentUserIndex + 6)
          processedLeaderboard = processedLeaderboard.slice(start, end)
        }
      }

      // 查找当前用户排名
      const currentUserRank = rawData.find(user => user.userId === statsUserId.value)

      return {
        leaderboard: processedLeaderboard,
        currentUserRank: currentUserRank ? {
          rank: rawData.findIndex(user => user.userId === statsUserId.value) + 1,
          ...currentUserRank
        } : null
      }
    }

    // 生成模拟排行榜数据
    const generateMockLeaderboard = () => {
      const mockUsers = [
        { UserId: 6, UserName: '翟志浩', DepartmentName: 'IT部门', TotalPoints: 150, TotalCoins: 75, TasksCompleted: 8, ProductivityScore: 85.5 },
        { UserId: 1, UserName: '系统管理员', DepartmentName: '总经办', TotalPoints: 120, TotalCoins: 60, TasksCompleted: 6, ProductivityScore: 78.2 },
        { UserId: 2, UserName: 'IT管理员', DepartmentName: 'IT部门', TotalPoints: 110, TotalCoins: 55, TasksCompleted: 5, ProductivityScore: 72.3 },
        { UserId: 7, UserName: '方平', DepartmentName: '人力资源部', TotalPoints: 95, TotalCoins: 48, TasksCompleted: 4, ProductivityScore: 68.1 },
        { UserId: 8, UserName: '陈正华', DepartmentName: '质量部', TotalPoints: 88, TotalCoins: 44, TasksCompleted: 3, ProductivityScore: 65.7 }
      ]

      let filteredUsers = [...mockUsers]

      // 根据显示范围筛选
      if (leaderboardScope.value === 'department') {
        filteredUsers = mockUsers.filter(user => user.DepartmentName === 'IT部门')
      } else if (leaderboardScope.value === 'active') {
        filteredUsers = mockUsers.filter(user => user.TotalPoints > 0)
      } else if (leaderboardScope.value === 'around') {
        // 模拟当前用户周围排名
        filteredUsers = mockUsers.slice(0, 3)
      }

      const leaderboard = filteredUsers.map((user, index) => ({
        Rank: index + 1,
        ...user
      }))

      const currentUserRank = mockUsers.find(user => user.UserId === statsUserId.value)

      return {
        leaderboard,
        currentUserRank: currentUserRank ? {
          rank: mockUsers.findIndex(user => user.UserId === statsUserId.value) + 1,
          ...currentUserRank
        } : null
      }
    }

    // 获取排行榜标题
    const getLeaderboardTitle = () => {
      const typeMap = {
        'points': '积分',
        'coins': '金币',
        'tasks': '任务完成',
        'productivity': '生产力'
      }

      const scopeMap = {
        'top10': '前10名',
        'top20': '前20名',
        'department': '本部门',
        'active': '活跃用户',
        'around': '我的周围'
      }

      const periodMap = {
        'daily': '今日',
        'weekly': '本周',
        'monthly': '本月'
      }

      return `${periodMap[leaderboardPeriod.value]}${typeMap[leaderboardType.value]}排行榜 - ${scopeMap[leaderboardScope.value]}`
    }

    // 初始化
    onMounted(() => {
      addLog('标准化游戏化系统初始化完成', 'success')
      refreshRealTimeData()
      getUserStats()
      getLeaderboard()
    })

    return {
      loading,
      systemStatus,
      realTimeData,
      statsUserId,
      statsPeriod,
      userStats,
      leaderboardType,
      leaderboardScope,
      leaderboardPeriod,
      leaderboard,
      currentUserRank,
      behaviorStats,
      operationLogs,
      refreshRealTimeData,
      getUserStats,
      getLeaderboard,
      getLeaderboardTitle,
      getRankClass,
      formatTime
    }
  }
}
</script>

<style scoped>
.standardized-gamification-dashboard {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
}

.page-title {
  font-size: 2.5rem;
  color: #2c3e50;
  margin-bottom: 10px;
}

.page-title i {
  color: #f39c12;
  margin-right: 10px;
}

.page-description {
  font-size: 1.1rem;
  color: #7f8c8d;
}

.status-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.status-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.card-icon {
  font-size: 2.5rem;
  margin-right: 20px;
  opacity: 0.8;
}

.card-value {
  font-size: 2rem;
  font-weight: bold;
  margin: 5px 0;
}

.card-label {
  font-size: 0.9rem;
  opacity: 0.8;
}

.function-areas {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.function-card {
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.function-card h3 {
  color: #2c3e50;
  margin-bottom: 20px;
  font-size: 1.3rem;
}

.test-controls, .stats-controls {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.test-group, .stats-group {
  display: flex;
  align-items: center;
  gap: 10px;
}

.test-group label, .stats-group label {
  min-width: 80px;
  font-weight: 500;
}

.test-buttons {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}

.btn-primary, .btn-secondary, .btn-info, .btn-success {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s;
}

.btn-primary { background: #3498db; color: white; }
.btn-secondary { background: #95a5a6; color: white; }
.btn-info { background: #17a2b8; color: white; }
.btn-success { background: #28a745; color: white; }

.btn-primary:hover { background: #2980b9; }
.btn-secondary:hover { background: #7f8c8d; }
.btn-info:hover { background: #138496; }
.btn-success:hover { background: #218838; }

.stats-result {
  margin-top: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 10px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  padding: 8px;
  background: white;
  border-radius: 4px;
}

.stat-value {
  font-weight: bold;
  color: #2c3e50;
}

.leaderboard-section {
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  margin-bottom: 30px;
}

.leaderboard-controls {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
  align-items: center;
  flex-wrap: wrap;
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.control-group label {
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

.control-group select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  background: white;
  font-size: 14px;
  min-width: 120px;
}

.current-user-rank {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 12px 20px;
  border-radius: 8px;
  margin-bottom: 15px;
}

.rank-info {
  display: flex;
  align-items: center;
  gap: 10px;
  font-weight: 500;
}

.rank-info .score {
  margin-left: auto;
  font-weight: bold;
  font-size: 16px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  padding-bottom: 10px;
  border-bottom: 2px solid #f0f0f0;
}

.table-header h4 {
  margin: 0;
  color: #333;
  font-size: 16px;
}

.result-count {
  color: #666;
  font-size: 12px;
}

.leaderboard-table table {
  width: 100%;
  border-collapse: collapse;
}

.leaderboard-table th,
.leaderboard-table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #dee2e6;
}

.leaderboard-table th {
  background: #f8f9fa;
  font-weight: 600;
  color: #495057;
}

.top-three {
  background: linear-gradient(90deg, #fff3cd 0%, #ffffff 100%);
}

.current-user {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  border: 2px solid #2196f3;
  font-weight: bold;
}

.highlight-user {
  position: relative;
}

.current-user-icon {
  color: #2196f3;
  margin-left: 5px;
  font-size: 12px;
}

.current-user-label {
  color: #2196f3;
  font-size: 12px;
  font-weight: bold;
  margin-left: 5px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 5px;
}

.points { color: #ff6b35; font-weight: bold; }
.coins { color: #ffd700; font-weight: bold; }
.tasks { color: #4caf50; font-weight: bold; }
.productivity { color: #9c27b0; font-weight: bold; }

.rank-badge {
  display: inline-block;
  width: 30px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  border-radius: 50%;
  font-weight: bold;
  color: white;
}

.rank-gold { background: #ffd700; }
.rank-silver { background: #c0c0c0; }
.rank-bronze { background: #cd7f32; }
.rank-normal { background: #6c757d; }

.operation-log {
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.log-container {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 10px;
}

.log-item {
  padding: 8px;
  margin-bottom: 5px;
  border-radius: 4px;
  font-family: monospace;
  font-size: 0.9rem;
}

.log-item.success { background: #d4edda; color: #155724; }
.log-item.error { background: #f8d7da; color: #721c24; }
.log-item.info { background: #d1ecf1; color: #0c5460; }

.log-time {
  font-weight: bold;
  margin-right: 10px;
}

input, select {
  padding: 8px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 0.9rem;
}

input:focus, select:focus {
  outline: none;
  border-color: #80bdff;
  box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
}
</style>
