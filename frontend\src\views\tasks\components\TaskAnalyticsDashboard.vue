<template>
  <div class="task-analytics-dashboard">
    <!-- 仪表板标题 -->
    <div class="dashboard-header">
      <div class="header-content">
        <h2 class="dashboard-title">📊 任务分析仪表板</h2>
        <p class="dashboard-subtitle">数据驱动的任务管理洞察</p>
      </div>
      
      <!-- 时间范围选择器 -->
      <div class="time-range-selector">
        <el-radio-group v-model="timeRange" @change="handleTimeRangeChange">
          <el-radio-button value="7d">最近7天</el-radio-button>
          <el-radio-button value="30d">最近30天</el-radio-button>
          <el-radio-button value="90d">最近90天</el-radio-button>
        </el-radio-group>
      </div>
    </div>

    <!-- 关键指标卡片 -->
    <div class="metrics-grid">
      <el-card class="metric-card" shadow="hover">
        <div class="metric-content">
          <div class="metric-icon" style="background: #409eff">📋</div>
          <div class="metric-info">
            <div class="metric-value">{{ statistics.totalTasks }}</div>
            <div class="metric-label">任务总数</div>
          </div>
        </div>
      </el-card>
      
      <el-card class="metric-card" shadow="hover">
        <div class="metric-content">
          <div class="metric-icon" style="background: #67c23a">✅</div>
          <div class="metric-info">
            <div class="metric-value">{{ (statistics.completionRate * 100).toFixed(1) }}%</div>
            <div class="metric-label">完成率</div>
          </div>
        </div>
      </el-card>
      
      <el-card class="metric-card" shadow="hover">
        <div class="metric-content">
          <div class="metric-icon" style="background: #f56c6c">⏰</div>
          <div class="metric-info">
            <div class="metric-value">{{ statistics.overdueTasks }}</div>
            <div class="metric-label">逾期任务</div>
          </div>
        </div>
      </el-card>
      
      <el-card class="metric-card" shadow="hover">
        <div class="metric-content">
          <div class="metric-icon" style="background: #e6a23c">📈</div>
          <div class="metric-info">
            <div class="metric-value">{{ statistics.avgCompletionTime }}天</div>
            <div class="metric-label">平均完成时间</div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 图表区域 -->
    <div class="charts-grid">
      <el-card class="chart-card" shadow="hover">
        <template #header>
          <span>📊 任务状态分布</span>
        </template>
        <div class="simple-chart">
          <div class="chart-placeholder">
            <p>图表组件开发中...</p>
            <el-button type="primary" @click="refreshData">刷新数据</el-button>
          </div>
        </div>
      </el-card>
      
      <el-card class="chart-card" shadow="hover">
        <template #header>
          <span>🎯 优先级分布</span>
        </template>
        <div class="simple-chart">
          <div class="priority-stats">
            <div class="priority-item">
              <span class="priority-label high">🔴 高优先级</span>
              <span class="priority-count">{{ priorityStats.high }}</span>
            </div>
            <div class="priority-item">
              <span class="priority-label medium">🟡 中优先级</span>
              <span class="priority-count">{{ priorityStats.medium }}</span>
            </div>
            <div class="priority-item">
              <span class="priority-label low">🟢 低优先级</span>
              <span class="priority-count">{{ priorityStats.low }}</span>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 数据表格 -->
    <el-card class="table-card" shadow="hover">
      <template #header>
        <span>📋 任务详情</span>
      </template>
      <el-table :data="recentTasks" v-loading="loading">
        <el-table-column prop="name" label="任务名称" />
        <el-table-column prop="status" label="状态">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="priority" label="优先级">
          <template #default="{ row }">
            <el-tag :type="getPriorityTagType(row.priority)">
              {{ getPriorityText(row.priority) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="assigneeUserName" label="负责人" />
        <el-table-column prop="planEndDate" label="截止日期">
          <template #default="{ row }">
            <span v-if="row.planEndDate">{{ formatDate(row.planEndDate) }}</span>
            <span v-else class="text-gray">未设置</span>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useTaskEnhancedStore } from '@/stores/modules/taskEnhanced'

// Store
const analyticsStore = useTaskEnhancedStore()

// 响应式状态
const timeRange = ref('30d')
const loading = ref(false)

// 统计数据
const statistics = reactive({
  totalTasks: 0,
  completionRate: 0,
  overdueTasks: 0,
  avgCompletionTime: 0
})

const priorityStats = reactive({
  high: 0,
  medium: 0,
  low: 0
})

const recentTasks = ref([])

// 计算属性
const tasks = computed(() => analyticsStore.tasks || [])

// 方法
const refreshData = async () => {
  loading.value = true
  try {
    await analyticsStore.fetchTasks()
    calculateStatistics()
    ElMessage.success('数据刷新成功')
  } catch (error) {
    ElMessage.error('数据刷新失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

const calculateStatistics = () => {
  const allTasks = tasks.value
  
  statistics.totalTasks = allTasks.length
  statistics.completionRate = allTasks.length > 0 
    ? allTasks.filter(t => t.status === 'Done').length / allTasks.length 
    : 0
  statistics.overdueTasks = allTasks.filter(t => isOverdue(t)).length
  statistics.avgCompletionTime = calculateAvgCompletionTime(allTasks)
  
  // 优先级统计
  priorityStats.high = allTasks.filter(t => t.priority === 'High').length
  priorityStats.medium = allTasks.filter(t => t.priority === 'Medium').length
  priorityStats.low = allTasks.filter(t => t.priority === 'Low').length
  
  // 最近任务
  recentTasks.value = allTasks.slice(0, 10)
}

const handleTimeRangeChange = (value) => {
  timeRange.value = value
  refreshData()
}

const isOverdue = (task) => {
  if (!task.planEndDate || task.status === 'Done') return false
  return new Date(task.planEndDate) < new Date()
}

const calculateAvgCompletionTime = (tasks) => {
  const completedTasks = tasks.filter(t => t.status === 'Done' && t.actualEndDate && t.actualStartDate)
  if (completedTasks.length === 0) return 0
  
  const totalDays = completedTasks.reduce((sum, task) => {
    const start = new Date(task.actualStartDate)
    const end = new Date(task.actualEndDate)
    return sum + Math.ceil((end - start) / (1000 * 60 * 60 * 24))
  }, 0)
  
  return Math.round(totalDays / completedTasks.length)
}

const getStatusTagType = (status) => {
  const types = { Todo: 'info', InProgress: 'warning', Done: 'success', Cancelled: 'danger' }
  return types[status] || 'info'
}

const getStatusText = (status) => {
  const texts = { Todo: '待处理', InProgress: '进行中', Done: '已完成', Cancelled: '已取消' }
  return texts[status] || status
}

const getPriorityTagType = (priority) => {
  const types = { High: 'danger', Medium: 'warning', Low: 'info' }
  return types[priority] || 'info'
}

const getPriorityText = (priority) => {
  const texts = { High: '高', Medium: '中', Low: '低' }
  return texts[priority] || priority
}

const formatDate = (date) => {
  if (!date) return ''
  return new Date(date).toLocaleDateString()
}

// 生命周期
onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.task-analytics-dashboard {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.dashboard-title {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
}

.dashboard-subtitle {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.metric-card {
  cursor: pointer;
  transition: transform 0.3s ease;
}

.metric-card:hover {
  transform: translateY(-4px);
}

.metric-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.metric-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.metric-value {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.metric-label {
  font-size: 14px;
  color: #909399;
}

.charts-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 24px;
}

.chart-card {
  min-height: 300px;
}

.simple-chart {
  height: 250px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  text-align: center;
  color: #909399;
}

.priority-stats {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 20px;
}

.priority-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
}

.priority-count {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
}

.table-card {
  margin-bottom: 24px;
}

.text-gray {
  color: #c0c4cc;
}

@media (max-width: 768px) {
  .charts-grid {
    grid-template-columns: 1fr;
  }
  
  .dashboard-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
}
</style>