using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using ItAssetsSystem.Infrastructure.Data;
using ItAssetsSystem.Models.Entities.Gamification;
using ItAssetsSystem.Models.Entities.Statistics;
using ItAssetsSystem.Models.Enums;

namespace ItAssetsSystem.Application.Features.Gamification.Services
{
    /// <summary>
    /// 一致性行为追踪器
    /// 实现最终一致性 + Saga模式
    /// </summary>
    public interface IConsistentBehaviorTracker
    {
        /// <summary>
        /// 记录用户行为（保证最终一致性）
        /// </summary>
        Task<long> TrackBehaviorAsync(string actionType, int userId, long? referenceId = null,
                                     string? referenceTable = null, object? context = null,
                                     string? sessionId = null, string? ipAddress = null, string? userAgent = null);

        /// <summary>
        /// 重试处理失败的事件
        /// </summary>
        Task<bool> RetryFailedEventAsync(long eventId);

        /// <summary>
        /// 标记事件处理完成
        /// </summary>
        Task MarkEventCompletedAsync(long eventId);

        /// <summary>
        /// 标记事件处理失败
        /// </summary>
        Task MarkEventFailedAsync(long eventId, string failureReason);
    }

    public class ConsistentBehaviorTracker : IConsistentBehaviorTracker
    {
        private readonly AppDbContext _context;
        private readonly ILogger<ConsistentBehaviorTracker> _logger;
        private readonly Dictionary<string, BehaviorTypeConfig> _behaviorConfigs;

        public ConsistentBehaviorTracker(AppDbContext context, ILogger<ConsistentBehaviorTracker> logger)
        {
            _context = context;
            _logger = logger;
            _behaviorConfigs = new Dictionary<string, BehaviorTypeConfig>();
        }

        /// <summary>
        /// 记录用户行为（保证最终一致性）
        /// </summary>
        public async Task<long> TrackBehaviorAsync(string actionType, int userId, long? referenceId = null,
                                                  string? referenceTable = null, object? context = null,
                                                  string? sessionId = null, string? ipAddress = null, string? userAgent = null)
        {
            long eventId = 0;
            
            try
            {
                // 🔥 Phase 1: 原子写入事件表（权威数据源）
                using var transaction = await _context.Database.BeginTransactionAsync();
                
                // 1. 获取行为配置
                var config = await GetBehaviorConfigAsync(actionType);
                if (config == null || !config.IsActive)
                {
                    _logger.LogWarning("行为类型 {ActionType} 未配置或已禁用", actionType);
                    return 0;
                }

                // 2. 计算奖励
                var rewards = CalculateRewards(config);

                // 3. 创建行为事件
                var behaviorEvent = new UserBehaviorEvent
                {
                    UserId = userId,
                    ActionType = actionType,
                    ActionCategory = config.ActionCategory,
                    ReferenceId = referenceId,
                    ReferenceTable = referenceTable,
                    PointsEarned = rewards.Points,
                    CoinsEarned = rewards.Coins,
                    DiamondsEarned = rewards.Diamonds,
                    XpEarned = rewards.Xp,
                    ContextData = context != null ? System.Text.Json.JsonSerializer.Serialize(context) : null,
                    SessionId = sessionId,
                    IpAddress = ipAddress,
                    UserAgent = userAgent,
                    Timestamp = DateTime.Now,
                    CreatedAt = DateTime.Now,
                    ProcessingStatus = ProcessingStatus.Pending.ToStatusString(),
                    RetryCount = 0
                };
                
                _context.UserBehaviorEvents.Add(behaviorEvent);
                await _context.SaveChangesAsync();

                // 获取生成的ID
                eventId = behaviorEvent.Id;

                await transaction.CommitAsync();

                _logger.LogInformation("事件记录成功: EventId={EventId}, UserId={UserId}, ActionType={ActionType}",
                    eventId, userId, actionType);

                // 🚀 Phase 2: 异步更新汇总表
                _ = Task.Run(async () => await UpdateSummaryTableAsync(behaviorEvent));

                return eventId;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "记录用户行为失败: EventId={EventId}, UserId={UserId}, ActionType={ActionType}", 
                    eventId, userId, actionType);
                
                // 🔧 标记失败状态
                await MarkEventFailedAsync(eventId, ex.Message);
                throw;
            }
        }

        /// <summary>
        /// 异步更新汇总表
        /// </summary>
        private async Task UpdateSummaryTableAsync(UserBehaviorEvent behaviorEvent)
        {
            try
            {
                // 标记为处理中
                await UpdateEventStatusAsync(behaviorEvent.Id, ProcessingStatus.Processing);

                var currentWeek = GetCurrentWeekStart();
                var summary = await GetOrCreateSummaryAsync(behaviorEvent.UserId, currentWeek);
                
                // 应用事件到汇总表
                ApplyEventToSummary(summary, behaviorEvent);
                
                await _context.SaveChangesAsync();
                
                // 标记处理完成
                await MarkEventCompletedAsync(behaviorEvent.Id);
                
                _logger.LogInformation("汇总表更新成功: EventId={EventId}, UserId={UserId}", 
                    behaviorEvent.Id, behaviorEvent.UserId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "汇总表更新失败: EventId={EventId}", behaviorEvent.Id);
                await MarkEventFailedAsync(behaviorEvent.Id, ex.Message);
            }
        }

        /// <summary>
        /// 重试处理失败的事件
        /// </summary>
        public async Task<bool> RetryFailedEventAsync(long eventId)
        {
            try
            {
                var behaviorEvent = await _context.UserBehaviorEvents
                    .FirstOrDefaultAsync(e => e.Id == eventId);

                if (behaviorEvent == null)
                {
                    _logger.LogWarning("事件不存在: EventId={EventId}", eventId);
                    return false;
                }

                if (!ProcessingStatusExtensions.FromStatusString(behaviorEvent.ProcessingStatus).CanRetry())
                {
                    _logger.LogWarning("事件状态不允许重试: EventId={EventId}, Status={Status}", 
                        eventId, behaviorEvent.ProcessingStatus);
                    return false;
                }

                // 增加重试次数
                behaviorEvent.RetryCount++;
                behaviorEvent.LastProcessedAt = DateTime.Now;
                await _context.SaveChangesAsync();

                // 重新处理
                await UpdateSummaryTableAsync(behaviorEvent);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "重试事件失败: EventId={EventId}", eventId);
                return false;
            }
        }

        /// <summary>
        /// 标记事件处理完成
        /// </summary>
        public async Task MarkEventCompletedAsync(long eventId)
        {
            await UpdateEventStatusAsync(eventId, ProcessingStatus.Completed);
        }

        /// <summary>
        /// 标记事件处理失败
        /// </summary>
        public async Task MarkEventFailedAsync(long eventId, string failureReason)
        {
            await UpdateEventStatusAsync(eventId, ProcessingStatus.Failed, failureReason);
        }

        #region 私有方法

        private async Task UpdateEventStatusAsync(long eventId, ProcessingStatus status, string? failureReason = null)
        {
            try
            {
                var behaviorEvent = await _context.UserBehaviorEvents
                    .FirstOrDefaultAsync(e => e.Id == eventId);

                if (behaviorEvent != null)
                {
                    behaviorEvent.ProcessingStatus = status.ToStatusString();
                    behaviorEvent.LastProcessedAt = DateTime.Now;
                    
                    if (!string.IsNullOrEmpty(failureReason))
                    {
                        behaviorEvent.FailureReason = failureReason;
                    }

                    await _context.SaveChangesAsync();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新事件状态失败: EventId={EventId}, Status={Status}", eventId, status);
            }
        }

        private DateTime GetCurrentWeekStart()
        {
            var today = DateTime.Today;
            var daysFromMonday = (int)today.DayOfWeek - (int)DayOfWeek.Monday;
            if (daysFromMonday < 0) daysFromMonday += 7;
            return today.AddDays(-daysFromMonday);
        }

        private async Task<UserWorkSummary> GetOrCreateSummaryAsync(int userId, DateTime weekStart)
        {
            var summary = await _context.UserWorkSummaries
                .FirstOrDefaultAsync(s => s.UserId == userId && 
                                   s.PeriodType == "weekly" && 
                                   s.PeriodDate == weekStart);

            if (summary == null)
            {
                var user = await _context.Users
                    .Include(u => u.Department)
                    .FirstOrDefaultAsync(u => u.Id == userId);

                summary = new UserWorkSummary
                {
                    UserId = userId,
                    UserName = user?.Name ?? "未知用户",
                    DepartmentName = user?.Department?.Name ?? "未分配",
                    PeriodType = "weekly",
                    PeriodDate = weekStart,
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now
                };

                _context.UserWorkSummaries.Add(summary);
            }

            return summary;
        }

        private void ApplyEventToSummary(UserWorkSummary summary, UserBehaviorEvent behaviorEvent)
        {
            // 根据行为类型更新对应字段
            switch (behaviorEvent.ActionType)
            {
                case "TASK_CLAIMED":
                    summary.TasksClaimed++;
                    break;
                case "TASK_COMPLETED":
                    summary.TasksCompleted++;
                    break;
                case "TASK_CREATED":
                    summary.TasksCreated++;
                    break;
                case "TASK_COMMENTED":
                    summary.TasksCommented++;
                    break;
                case "ASSET_CREATED":
                    summary.AssetsCreated++;
                    break;
                case "ASSET_UPDATED":
                    summary.AssetsUpdated++;
                    break;
                case "ASSET_DELETED":
                    summary.AssetsDeleted++;
                    break;
                case "FAULT_RECORDED":
                    summary.FaultsReported++;
                    break;
                case "FAULT_REPAIRED":
                    summary.FaultsRepaired++;
                    break;
                case "PURCHASE_CREATED":
                    summary.ProcurementsCreated++;
                    break;
                case "PURCHASE_UPDATED":
                    summary.ProcurementsUpdated++;
                    break;
                case "SPAREPART_INBOUND":
                    summary.PartsIn++;
                    break;
                case "SPAREPART_OUTBOUND":
                    summary.PartsOut++;
                    break;
                case "SPAREPART_UPDATED":
                    summary.PartsAdded++;
                    break;
                // 其他行为类型可以在这里继续添加
            }

            // 更新游戏化收益
            summary.TotalPointsEarned += behaviorEvent.PointsEarned;
            summary.TotalCoinsEarned += behaviorEvent.CoinsEarned;
            summary.TotalDiamondsEarned += behaviorEvent.DiamondsEarned;
            summary.TotalXpEarned += behaviorEvent.XpEarned;

            summary.UpdatedAt = DateTime.Now;
            summary.Version++;
        }

        private async Task<BehaviorTypeConfig?> GetBehaviorConfigAsync(string actionType)
        {
            if (_behaviorConfigs.TryGetValue(actionType, out var cachedConfig))
                return cachedConfig;

            await LoadBehaviorConfigsAsync();
            return _behaviorConfigs.TryGetValue(actionType, out var config) ? config : null;
        }

        private async Task LoadBehaviorConfigsAsync()
        {
            try
            {
                var configs = await _context.BehaviorTypeConfigs
                    .Where(c => c.IsActive)
                    .ToListAsync();

                _behaviorConfigs.Clear();
                foreach (var config in configs)
                {
                    _behaviorConfigs[config.ActionType] = config;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加载行为配置失败");
            }
        }

        private BehaviorRewards CalculateRewards(BehaviorTypeConfig config)
        {
            return new BehaviorRewards
            {
                Points = config.BasePoints,
                Coins = config.BaseCoins,
                Diamonds = config.BaseDiamonds,
                Xp = config.BaseXp
            };
        }

        #endregion
    }


}
