<template>
  <el-dialog
    v-model="visible"
    title="采购单详情"
    width="800px"
    destroy-on-close
    @close="handleClose"
  >
    <div v-loading="loading" class="purchase-detail-content">
      <div v-if="purchaseData" class="detail-container">
        <!-- 基本信息 -->
        <el-card class="info-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span class="card-title">📋 基本信息</span>
            </div>
          </template>
          
          <el-descriptions :column="2" border>
            <el-descriptions-item label="采购单号">
              <el-tag type="primary">{{ purchaseData.orderNumber || purchaseData.orderCode }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="采购状态">
              <el-tag :type="getStatusType(purchaseData.status)">
                {{ getStatusText(purchaseData.status) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="供应商">
              {{ purchaseData.supplierName || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="申请人">
              {{ purchaseData.applicantName || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="预计交付日期">
              {{ formatDate(purchaseData.expectedDeliveryDate) }}
            </el-descriptions-item>
            <el-descriptions-item label="实际交付日期">
              {{ formatDate(purchaseData.actualDeliveryDate) }}
            </el-descriptions-item>
            <el-descriptions-item label="总金额">
              <span class="amount">¥{{ formatAmount(purchaseData.totalAmount) }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="创建时间">
              {{ formatDate(purchaseData.createdAt) }}
            </el-descriptions-item>
          </el-descriptions>
          
          <div v-if="purchaseData.notes" class="notes-section">
            <h4>备注说明</h4>
            <p class="notes-text">{{ purchaseData.notes }}</p>
          </div>
        </el-card>

        <!-- 采购物品清单 -->
        <el-card class="items-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span class="card-title">📦 采购物品清单</span>
              <span class="item-count">共 {{ purchaseData.items?.length || 0 }} 项</span>
            </div>
          </template>
          
          <el-table 
            :data="purchaseData.items || []" 
            border 
            stripe
            style="width: 100%"
          >
            <el-table-column type="index" label="序号" width="60" align="center" />
            <el-table-column prop="name" label="物品名称" min-width="150">
              <template #default="{ row }">
                <div class="item-name">
                  <strong>{{ row.name || row.itemName }}</strong>
                  <div v-if="row.itemCode" class="item-code">编号: {{ row.itemCode }}</div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="specification" label="规格型号" min-width="120">
              <template #default="{ row }">
                {{ row.specification || '-' }}
              </template>
            </el-table-column>
            <el-table-column prop="quantity" label="数量" width="80" align="center">
              <template #default="{ row }">
                <el-tag size="small">{{ row.quantity }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="unitPrice" label="单价" width="100" align="right">
              <template #default="{ row }">
                ¥{{ formatAmount(row.unitPrice) }}
              </template>
            </el-table-column>
            <el-table-column label="小计" width="120" align="right">
              <template #default="{ row }">
                <span class="subtotal">¥{{ formatAmount(row.unitPrice * row.quantity) }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="notes" label="备注" min-width="100">
              <template #default="{ row }">
                {{ row.notes || '-' }}
              </template>
            </el-table-column>
          </el-table>
          
          <!-- 总计 -->
          <div class="total-section">
            <div class="total-row">
              <span class="total-label">总计金额：</span>
              <span class="total-amount">¥{{ formatAmount(purchaseData.totalAmount) }}</span>
            </div>
          </div>
        </el-card>
      </div>
      
      <!-- 加载状态 -->
      <div v-else-if="loading" class="loading-container">
        <el-icon class="is-loading"><Loading /></el-icon>
        <p>正在加载采购单详情...</p>
      </div>
      
      <!-- 错误状态 -->
      <div v-else class="error-container">
        <el-icon><WarningFilled /></el-icon>
        <p>加载采购单详情失败</p>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button v-if="purchaseData" @click="handlePrintPreview">
          <el-icon><View /></el-icon>
          打印预览
        </el-button>
        <el-button v-if="purchaseData" type="primary" @click="handlePrint">
          <el-icon><Printer /></el-icon>
          打印
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Loading, WarningFilled, Printer, View } from '@element-plus/icons-vue'
import purchaseApi from '@/api/purchase'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  purchaseId: {
    type: [Number, String],
    default: null
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'close'])

// 响应式数据
const visible = ref(false)
const loading = ref(false)
const purchaseData = ref(null)

// 监听显示状态
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
  if (newVal && props.purchaseId) {
    loadPurchaseDetail()
  }
})

watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

// 加载采购单详情
const loadPurchaseDetail = async () => {
  if (!props.purchaseId) {
    ElMessage.error('采购单ID无效')
    return
  }

  loading.value = true
  purchaseData.value = null

  try {
    console.log('加载采购单详情，ID:', props.purchaseId)
    const response = await purchaseApi.getPurchaseByIdV2(props.purchaseId)
    console.log('采购单详情API响应:', response)

    if (response && response.success && response.data) {
      purchaseData.value = response.data
      console.log('采购单详情加载成功:', purchaseData.value)
    } else {
      ElMessage.error(response?.message || '获取采购单详情失败')
    }
  } catch (error) {
    console.error('加载采购单详情失败:', error)
    ElMessage.error('加载采购单详情失败: ' + (error.message || '网络错误'))
  } finally {
    loading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
  purchaseData.value = null
  emit('close')
}

// 打印功能
const handlePrint = () => {
  // 创建打印内容
  const printContent = generatePrintContent()

  // 创建新窗口进行打印
  const printWindow = window.open('', '_blank', 'width=800,height=600')

  if (printWindow) {
    printWindow.document.write(printContent)
    printWindow.document.close()

    // 等待内容加载完成后打印
    printWindow.onload = () => {
      printWindow.focus()
      printWindow.print()
      printWindow.close()
    }
  } else {
    // 如果无法打开新窗口，使用当前窗口打印
    const originalContent = document.body.innerHTML
    document.body.innerHTML = printContent
    window.print()
    document.body.innerHTML = originalContent
    location.reload() // 重新加载页面恢复状态
  }
}

// 生成打印内容
const generatePrintContent = () => {
  const data = purchaseData.value
  if (!data) return ''

  return `
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>采购单详情 - ${data.orderNumber || data.orderCode}</title>
      <style>
        * {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
        }

        body {
          font-family: 'Microsoft YaHei', Arial, sans-serif;
          font-size: 12px;
          line-height: 1.6;
          color: #333;
          background: white;
          padding: 20px;
        }

        .print-header {
          text-align: center;
          margin-bottom: 30px;
          border-bottom: 2px solid #333;
          padding-bottom: 15px;
        }

        .print-title {
          font-size: 24px;
          font-weight: bold;
          margin-bottom: 5px;
          color: #333;
        }

        .print-subtitle {
          font-size: 14px;
          color: #666;
          margin-bottom: 10px;
        }

        .order-number {
          font-size: 16px;
          font-weight: bold;
          color: #333;
        }

        .info-section {
          margin-bottom: 25px;
        }

        .section-title {
          font-size: 16px;
          font-weight: bold;
          margin-bottom: 15px;
          color: #333;
          border-left: 4px solid #409eff;
          padding-left: 10px;
        }

        .info-grid {
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          gap: 15px;
          margin-bottom: 15px;
        }

        .info-item {
          display: flex;
          align-items: center;
        }

        .info-label {
          font-weight: bold;
          min-width: 100px;
          color: #555;
        }

        .info-value {
          flex: 1;
          color: #333;
        }

        .status-badge {
          display: inline-block;
          padding: 2px 8px;
          border-radius: 4px;
          font-size: 11px;
          font-weight: bold;
        }

        .status-success { background: #f0f9ff; color: #1890ff; border: 1px solid #d1ecf1; }
        .status-warning { background: #fffbf0; color: #fa8c16; border: 1px solid #ffeaa7; }
        .status-info { background: #f6f8fa; color: #666; border: 1px solid #d1d5da; }

        .amount-highlight {
          font-weight: bold;
          color: #e6a23c;
          font-size: 14px;
        }

        .notes-section {
          margin-top: 15px;
          padding: 12px;
          background: #f8f9fa;
          border-radius: 4px;
          border-left: 4px solid #409eff;
        }

        .notes-title {
          font-weight: bold;
          margin-bottom: 8px;
          color: #333;
        }

        .notes-content {
          color: #666;
          line-height: 1.8;
        }

        .items-table {
          width: 100%;
          border-collapse: collapse;
          margin-top: 10px;
          font-size: 11px;
        }

        .items-table th,
        .items-table td {
          border: 1px solid #ddd;
          padding: 8px;
          text-align: left;
        }

        .items-table th {
          background: #f5f7fa;
          font-weight: bold;
          color: #333;
          text-align: center;
        }

        .items-table td {
          vertical-align: top;
        }

        .items-table .text-center {
          text-align: center;
        }

        .items-table .text-right {
          text-align: right;
        }

        .item-name {
          font-weight: bold;
          color: #333;
        }

        .item-code {
          font-size: 10px;
          color: #999;
          margin-top: 2px;
        }

        .quantity-badge {
          display: inline-block;
          background: #e6f7ff;
          color: #1890ff;
          padding: 2px 6px;
          border-radius: 3px;
          font-weight: bold;
        }

        .total-section {
          margin-top: 20px;
          padding-top: 15px;
          border-top: 2px solid #eee;
          text-align: right;
        }

        .total-row {
          font-size: 16px;
          font-weight: bold;
          color: #333;
        }

        .total-amount {
          color: #e6a23c;
          font-size: 18px;
        }

        .print-footer {
          margin-top: 40px;
          padding-top: 20px;
          border-top: 1px solid #ddd;
        }

        .footer-content {
          display: flex;
          justify-content: space-between;
          align-items: flex-end;
        }

        .print-info {
          color: #999;
          font-size: 11px;
        }

        .signature-area {
          display: flex;
          gap: 60px;
        }

        .signature-item {
          text-align: center;
        }

        .signature-line {
          width: 100px;
          height: 1px;
          border-bottom: 1px solid #333;
          margin-bottom: 5px;
        }

        .signature-label {
          font-size: 12px;
          color: #666;
        }

        @media print {
          body {
            padding: 10px;
          }

          .print-header {
            margin-bottom: 20px;
          }

          .info-section {
            margin-bottom: 20px;
            page-break-inside: avoid;
          }

          .items-table {
            page-break-inside: auto;
          }

          .items-table tr {
            page-break-inside: avoid;
            page-break-after: auto;
          }

          .total-section {
            page-break-inside: avoid;
          }

          .print-footer {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            text-align: center;
          }
        }
      </style>
    </head>
    <body>
      <div class="print-header">
        <div class="print-title">采购订单</div>
        <div class="print-subtitle">Purchase Order</div>
        <div class="order-number">订单号：${data.orderNumber || data.orderCode}</div>
      </div>

      <div class="info-section">
        <div class="section-title">基本信息</div>
        <div class="info-grid">
          <div class="info-item">
            <span class="info-label">采购单号：</span>
            <span class="info-value">${data.orderNumber || data.orderCode}</span>
          </div>
          <div class="info-item">
            <span class="info-label">采购状态：</span>
            <span class="info-value">
              <span class="status-badge ${getStatusClass(data.status)}">${getStatusText(data.status)}</span>
            </span>
          </div>
          <div class="info-item">
            <span class="info-label">供应商：</span>
            <span class="info-value">${data.supplierName || '-'}</span>
          </div>
          <div class="info-item">
            <span class="info-label">申请人：</span>
            <span class="info-value">${data.applicantName || '-'}</span>
          </div>
          <div class="info-item">
            <span class="info-label">预计交付：</span>
            <span class="info-value">${formatDate(data.expectedDeliveryDate)}</span>
          </div>
          <div class="info-item">
            <span class="info-label">实际交付：</span>
            <span class="info-value">${formatDate(data.actualDeliveryDate)}</span>
          </div>
          <div class="info-item">
            <span class="info-label">总金额：</span>
            <span class="info-value amount-highlight">¥${formatAmount(data.totalAmount)}</span>
          </div>
          <div class="info-item">
            <span class="info-label">创建时间：</span>
            <span class="info-value">${formatDate(data.createdAt)}</span>
          </div>
        </div>

        ${data.notes ? `
          <div class="notes-section">
            <div class="notes-title">备注说明</div>
            <div class="notes-content">${data.notes}</div>
          </div>
        ` : ''}
      </div>

      <div class="info-section">
        <div class="section-title">采购物品清单 (共 ${data.items?.length || 0} 项)</div>
        <table class="items-table">
          <thead>
            <tr>
              <th style="width: 40px;">序号</th>
              <th style="width: 200px;">物品名称</th>
              <th style="width: 120px;">规格型号</th>
              <th style="width: 60px;">数量</th>
              <th style="width: 80px;">单价</th>
              <th style="width: 80px;">小计</th>
              <th>备注</th>
            </tr>
          </thead>
          <tbody>
            ${(data.items || []).map((item, index) => `
              <tr>
                <td class="text-center">${index + 1}</td>
                <td>
                  <div class="item-name">${item.name || item.itemName}</div>
                  ${item.itemCode ? `<div class="item-code">编号: ${item.itemCode}</div>` : ''}
                </td>
                <td>${item.specification || '-'}</td>
                <td class="text-center">
                  <span class="quantity-badge">${item.quantity}</span>
                </td>
                <td class="text-right">¥${formatAmount(item.unitPrice)}</td>
                <td class="text-right">¥${formatAmount(item.unitPrice * item.quantity)}</td>
                <td>${item.notes || '-'}</td>
              </tr>
            `).join('')}
          </tbody>
        </table>

        <div class="total-section">
          <div class="total-row">
            总计金额：<span class="total-amount">¥${formatAmount(data.totalAmount)}</span>
          </div>
        </div>
      </div>

      <div class="print-footer">
        <div class="footer-content">
          <div class="print-info">
            <div>打印时间：${new Date().toLocaleString('zh-CN')}</div>
            <div>系统：IT资产管理系统</div>
          </div>
          <div class="signature-area">
            <div class="signature-item">
              <div class="signature-line"></div>
              <div class="signature-label">采购员签字</div>
            </div>
            <div class="signature-item">
              <div class="signature-line"></div>
              <div class="signature-label">审批人签字</div>
            </div>
            <div class="signature-item">
              <div class="signature-line"></div>
              <div class="signature-label">供应商签字</div>
            </div>
          </div>
        </div>
      </div>
    </body>
    </html>
  `
}

// 打印预览功能
const handlePrintPreview = () => {
  const printContent = generatePrintContent()
  const previewWindow = window.open('', '_blank', 'width=900,height=700,scrollbars=yes')

  if (previewWindow) {
    previewWindow.document.write(printContent)
    previewWindow.document.close()
    previewWindow.focus()
  } else {
    ElMessage.error('无法打开预览窗口，请检查浏览器弹窗设置')
  }
}

// 获取状态样式类
const getStatusClass = (status) => {
  const statusMap = {
    0: 'status-warning',  // 未到货
    1: 'status-success',  // 已到货
    'pending': 'status-info',
    'approved': 'status-success',
    'purchased': 'status-warning',
    'received': 'status-success',
    'rejected': 'status-danger',
    'cancelled': 'status-danger'
  }
  return statusMap[status] || 'status-info'
}

// 工具方法：获取状态类型
const getStatusType = (status) => {
  const statusMap = {
    0: 'warning',  // 未到货
    1: 'success',  // 已到货
    'pending': 'info',
    'approved': 'success',
    'purchased': 'warning',
    'received': 'primary',
    'rejected': 'danger',
    'cancelled': 'danger'
  }
  return statusMap[status] || 'info'
}

// 工具方法：获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    0: '未到货',
    1: '已到货',
    'pending': '待审批',
    'approved': '已审批',
    'purchased': '已采购',
    'received': '已入库',
    'rejected': '已拒绝',
    'cancelled': '已取消'
  }
  return statusMap[status] || '未知状态'
}

// 工具方法：格式化日期
const formatDate = (dateStr) => {
  if (!dateStr || dateStr === '0001-01-01T00:00:00' || dateStr.startsWith('0001-01-01')) {
    return '-'
  }
  
  try {
    const date = new Date(dateStr)
    if (isNaN(date.getTime())) return '-'
    
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    })
  } catch (error) {
    return '-'
  }
}

// 工具方法：格式化金额
const formatAmount = (amount) => {
  if (amount == null || amount === '') return '0.00'
  return Number(amount).toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
}
</script>

<style lang="scss" scoped>
.purchase-detail-content {
  min-height: 400px;
  
  .detail-container {
    .info-card, .items-card {
      margin-bottom: 20px;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .card-title {
        font-weight: 600;
        font-size: 16px;
        color: #303133;
      }
      
      .item-count {
        color: #909399;
        font-size: 14px;
      }
    }
    
    .notes-section {
      margin-top: 20px;
      padding-top: 20px;
      border-top: 1px solid #ebeef5;
      
      h4 {
        margin: 0 0 10px 0;
        color: #303133;
        font-size: 14px;
      }
      
      .notes-text {
        margin: 0;
        color: #606266;
        line-height: 1.6;
        background: #f5f7fa;
        padding: 12px;
        border-radius: 4px;
      }
    }
    
    .item-name {
      .item-code {
        font-size: 12px;
        color: #909399;
        margin-top: 4px;
      }
    }
    
    .amount {
      font-weight: 600;
      color: #e6a23c;
    }
    
    .subtotal {
      font-weight: 500;
      color: #303133;
    }
    
    .total-section {
      margin-top: 16px;
      padding-top: 16px;
      border-top: 2px solid #ebeef5;
      
      .total-row {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        
        .total-label {
          font-size: 16px;
          font-weight: 600;
          color: #303133;
          margin-right: 12px;
        }
        
        .total-amount {
          font-size: 18px;
          font-weight: 700;
          color: #e6a23c;
        }
      }
    }
  }
  
  .loading-container, .error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 300px;
    color: #909399;
    
    .el-icon {
      font-size: 48px;
      margin-bottom: 16px;
    }
    
    p {
      margin: 0;
      font-size: 14px;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 打印样式优化 */
@media print {
  .el-dialog {
    box-shadow: none !important;
    border: none !important;
  }

  .el-dialog__header,
  .el-dialog__footer {
    display: none !important;
  }

  .el-dialog__body {
    padding: 0 !important;
  }

  .purchase-detail-content {
    box-shadow: none !important;
    border: none !important;
  }

  .info-card,
  .items-card {
    box-shadow: none !important;
    border: 1px solid #ddd !important;
    margin-bottom: 15px !important;
    page-break-inside: avoid;
  }

  .card-header {
    background: #f5f7fa !important;
    padding: 10px !important;
    border-bottom: 1px solid #ddd !important;
  }

  .el-descriptions {
    font-size: 12px !important;
  }

  .el-table {
    font-size: 11px !important;
  }

  .el-table th,
  .el-table td {
    padding: 6px !important;
  }

  .total-section {
    page-break-inside: avoid;
  }
}
</style>
