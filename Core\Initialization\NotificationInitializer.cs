// File: Core/Initialization/NotificationInitializer.cs
// Description: 通知表初始化和修复工具

using ItAssetsSystem.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Hosting;
using MySqlConnector;

namespace ItAssetsSystem.Core.Initialization
{
    /// <summary>
    /// 通知表初始化器，用于检查和修复通知表结构
    /// </summary>
    public static class NotificationInitializer
    {
        /// <summary>
        /// 初始化并修复通知表结构
        /// </summary>
        public static async Task InitializeNotificationTableAsync(IServiceProvider serviceProvider)
        {
            using var scope = serviceProvider.CreateScope();
            var logger = scope.ServiceProvider.GetRequiredService<ILogger<AppDbContext>>();
            var dbContext = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            logger.LogInformation("检查通知表结构");
            
            try
            {
                // 检查是否存在UserId1列
                bool hasUserId1Column = await CheckTableHasColumnAsync(dbContext, "notifications", "UserId1");
                
                if (hasUserId1Column)
                {
                    logger.LogWarning("发现通知表存在UserId1列，将进行修复");
                    
                    // 删除列
                    await DropColumnAsync(dbContext, "notifications", "UserId1");
                    
                    logger.LogInformation("成功移除通知表的UserId1列");
                }
                else
                {
                    logger.LogInformation("通知表结构正常，无需修复");
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "检查/修复通知表结构失败");
            }
        }
        
        /// <summary>
        /// 检查表是否存在指定列
        /// </summary>
        private static async Task<bool> CheckTableHasColumnAsync(AppDbContext dbContext, string tableName, string columnName)
        {
            string sql = @"
                SELECT COUNT(*)
                FROM INFORMATION_SCHEMA.COLUMNS
                WHERE TABLE_SCHEMA = @schema
                  AND TABLE_NAME = @tableName
                  AND COLUMN_NAME = @columnName";

            // 获取数据库名称
            var connectionString = dbContext.Database.GetConnectionString();
            var builder = new MySqlConnectionStringBuilder(connectionString);
            string dbName = builder.Database;

            var parameters = new[]
            {
                new MySqlParameter("@schema", dbName),
                new MySqlParameter("@tableName", tableName),
                new MySqlParameter("@columnName", columnName)
            };

            var result = await dbContext.Database
                .ExecuteSqlRawAsync(sql, parameters);

            return result > 0;
        }
        
        /// <summary>
        /// 删除表的指定列
        /// </summary>
        private static async Task DropColumnAsync(AppDbContext dbContext, string tableName, string columnName)
        {
            string sql = $"ALTER TABLE `{tableName}` DROP COLUMN `{columnName}`";
            await dbContext.Database.ExecuteSqlRawAsync(sql);
        }
    }
} 