// IT资产管理系统 - 游戏化任务管理插件
// 文件路径: /Core/Plugins/TaskManagementPlugin.cs
// 功能: 提供游戏化任务管理和积分系统功能

using ItAssetsSystem.Core.Abstractions;
using ItAssetsSystem.Core.Events;
using ItAssetsSystem.Models.Entities;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace ItAssetsSystem.Core.Plugins
{
    /// <summary>
    /// 任务创建事件
    /// </summary>
    public class TaskCreatedEvent
    {
        /// <summary>
        /// 任务ID
        /// </summary>
        public int TaskId { get; set; }
        
        /// <summary>
        /// 任务标题
        /// </summary>
        public string TaskTitle { get; set; }
        
        /// <summary>
        /// 创建者ID
        /// </summary>
        public int UserId { get; set; }
        
        /// <summary>
        /// 任务类型
        /// </summary>
        public int TaskType { get; set; }
    }

    /// <summary>
    /// 任务更新事件
    /// </summary>
    public class TaskUpdatedEvent
    {
        /// <summary>
        /// 任务ID
        /// </summary>
        public int TaskId { get; set; }
        
        /// <summary>
        /// 任务标题
        /// </summary>
        public string TaskTitle { get; set; }
        
        /// <summary>
        /// 更新者ID
        /// </summary>
        public int UserId { get; set; }
        
        /// <summary>
        /// 进度
        /// </summary>
        public int Progress { get; set; }
    }

    /// <summary>
    /// 任务完成事件
    /// </summary>
    public class TaskCompletedEvent
    {
        /// <summary>
        /// 任务ID
        /// </summary>
        public int TaskId { get; set; }
        
        /// <summary>
        /// 任务标题
        /// </summary>
        public string TaskTitle { get; set; }
        
        /// <summary>
        /// 完成者ID
        /// </summary>
        public int UserId { get; set; }
        
        /// <summary>
        /// 是否周期任务
        /// </summary>
        public bool IsPeriodicTask { get; set; }
    }
    
    /// <summary>
    /// 任务评论事件
    /// </summary>
    public class TaskCommentedEvent
    {
        /// <summary>
        /// 任务ID
        /// </summary>
        public int TaskId { get; set; }
        
        /// <summary>
        /// 任务标题
        /// </summary>
        public string TaskTitle { get; set; }
        
        /// <summary>
        /// 评论者ID
        /// </summary>
        public int UserId { get; set; }
        
        /// <summary>
        /// 评论内容
        /// </summary>
        public string Comment { get; set; }
    }

    /// <summary>
    /// 资产创建事件
    /// </summary>
    public class AssetCreatedEvent
    {
        /// <summary>
        /// 资产ID
        /// </summary>
        public int AssetId { get; set; }
        
        /// <summary>
        /// 资产名称
        /// </summary>
        public string AssetName { get; set; }
    }

    /// <summary>
    /// 任务管理插件接口
    /// </summary>
    public interface ITaskService
    {
        /// <summary>
        /// 创建任务
        /// </summary>
        System.Threading.Tasks.Task<int> CreateTaskAsync(object taskData);
        
        /// <summary>
        /// 完成任务
        /// </summary>
        System.Threading.Tasks.Task<bool> CompleteTaskAsync(int taskId);
        
        /// <summary>
        /// 创建周期性任务
        /// </summary>
        System.Threading.Tasks.Task<int> CreatePeriodicTaskAsync(object taskData, string cronExpression);
        
        /// <summary>
        /// 创建PDCA计划任务
        /// </summary>
        System.Threading.Tasks.Task<int> CreatePdcaTaskAsync(object taskData, int pdcaPlanId);
        
        /// <summary>
        /// 添加任务评论
        /// </summary>
        System.Threading.Tasks.Task<bool> AddTaskCommentAsync(int taskId, int userId, string comment);
        
        /// <summary>
        /// 更新任务进度
        /// </summary>
        System.Threading.Tasks.Task<bool> UpdateTaskProgressAsync(int taskId, int userId, int progress);
        
        /// <summary>
        /// 获取用户积分
        /// </summary>
        System.Threading.Tasks.Task<int> GetUserPointsAsync(int userId);
        
        /// <summary>
        /// 获取排行榜
        /// </summary>
        System.Threading.Tasks.Task<object> GetLeaderboardAsync(string type = "daily");
        
        /// <summary>
        /// 获取用户道具
        /// </summary>
        System.Threading.Tasks.Task<List<object>> GetUserItemsAsync(int userId);
        
        /// <summary>
        /// 使用攻击道具
        /// </summary>
        System.Threading.Tasks.Task<bool> UseWeaponAsync(int fromUserId, int toUserId, string weaponType);
    }

    /// <summary>
    /// 游戏化任务管理插件 - 提供任务管理和游戏化功能的实现
    /// </summary>
    public class TaskManagementPlugin : PluginBase, ITaskService
    {
        private readonly ILogger<TaskManagementPlugin> _logger;
        private readonly IEventBus _eventBus;
        private Timer _periodicTaskTimer;
        private readonly IServiceProvider _serviceProvider;
        
        /// <inheritdoc/>
        public override string Name => "任务管理插件";
        
        /// <inheritdoc/>
        public override string Description => "提供游戏化任务管理功能，包括积分系统、排名和虚拟奖励";
        
        /// <inheritdoc/>
        public override Version Version => new Version(1, 0, 0);
        
        /// <inheritdoc/>
        public bool IsEnabled { get; set; } = true;
        
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志记录器</param>
        /// <param name="eventBus">事件总线</param>
        /// <param name="serviceProvider">服务提供程序</param>
        public TaskManagementPlugin(
            ILogger<TaskManagementPlugin> logger,
            IEventBus eventBus,
            IServiceProvider serviceProvider)
        {
            _logger = logger;
            _eventBus = eventBus;
            _serviceProvider = serviceProvider;
        }
        
        /// <inheritdoc/>
        protected override void OnStart()
        {
            _logger.LogInformation("启动任务管理插件...");
            
            // 订阅事件
            _eventBus.Subscribe<AssetCreatedEvent>(HandleAssetCreatedEvent);
            _eventBus.Subscribe<TaskCompletedEvent>(HandleTaskCompletedEvent);
            
            // 启动周期任务定时器
            StartPeriodicTaskScheduler();
            
            _logger.LogInformation("任务管理插件启动完成");
        }
        
        /// <inheritdoc/>
        protected override void OnStop()
        {
            _logger.LogInformation("停止任务管理插件...");
            
            // 取消订阅事件
            _eventBus.Unsubscribe<AssetCreatedEvent>(HandleAssetCreatedEvent);
            _eventBus.Unsubscribe<TaskCompletedEvent>(HandleTaskCompletedEvent);
            
            // 停止定时器
            _periodicTaskTimer?.Dispose();
            _periodicTaskTimer = null;
            
            _logger.LogInformation("任务管理插件已停止");
        }
        
        /// <inheritdoc/>
        public override void RegisterServices(IServiceCollection services)
        {
            _logger.LogInformation("注册任务管理服务...");
            services.AddSingleton<ITaskService>(this);
        }

        /// <summary>
        /// 初始化插件
        /// </summary>
        public void Initialize()
        {
            _logger.LogInformation("初始化游戏化任务管理插件...");
            
            // 注册事件处理
            _eventBus.Subscribe<TaskCreatedEvent>(OnTaskCreated);
            _eventBus.Subscribe<TaskUpdatedEvent>(OnTaskUpdated);
            _eventBus.Subscribe<TaskCompletedEvent>(OnTaskCompleted);
            _eventBus.Subscribe<TaskCommentedEvent>(OnTaskCommented);
            
            // 初始化定时器，每天定时处理周期任务
            _periodicTaskTimer = new Timer(ProcessPeriodicTasks, null, TimeSpan.FromMinutes(1), TimeSpan.FromHours(1));
            
            _logger.LogInformation("游戏化任务管理插件初始化完成");
        }

        /// <summary>
        /// 关闭插件
        /// </summary>
        public void Shutdown()
        {
            _logger.LogInformation("关闭游戏化任务管理插件...");
            
            // 取消订阅事件
            _eventBus.Unsubscribe<TaskCreatedEvent>(OnTaskCreated);
            _eventBus.Unsubscribe<TaskUpdatedEvent>(OnTaskUpdated);
            _eventBus.Unsubscribe<TaskCompletedEvent>(OnTaskCompleted);
            _eventBus.Unsubscribe<TaskCommentedEvent>(OnTaskCommented);
            
            // 释放定时器
            _periodicTaskTimer?.Dispose();
            
            _logger.LogInformation("游戏化任务管理插件已关闭");
        }
        
        /// <summary>
        /// 处理周期性任务
        /// </summary>
        private void ProcessPeriodicTasks(object state)
        {
            // 此处实现周期性任务的处理逻辑
            _logger.LogInformation("正在处理周期性任务...");
            
            try
            {
                // 在实际实现中，应该查询数据库获取需要执行的周期性任务
                // 1. 获取所有需要执行的周期性任务
                // 2. 为每个周期性任务创建新的任务实例
                // 3. 通知相关用户
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理周期性任务时出错");
            }
        }
        
        /// <summary>
        /// 处理任务完成事件
        /// </summary>
        private void HandleTaskCompletedEvent(TaskCompletedEvent evt)
        {
            _logger.LogInformation($"处理任务完成事件: {evt.TaskId}");
            
            if (evt.IsPeriodicTask)
            {
                // 对于周期任务，需要创建下一个周期的任务实例
                CreateNextPeriodicTaskInstance(evt.TaskId);
            }
        }
        
        /// <summary>
        /// 处理资产创建事件
        /// </summary>
        private void HandleAssetCreatedEvent(AssetCreatedEvent evt)
        {
            _logger.LogInformation($"处理资产创建事件，创建验收任务");
            
            // 为新资产创建验收任务
            CreateAssetAcceptanceTask(evt.AssetId, evt.AssetName);
        }
        
        /// <summary>
        /// 启动周期任务调度器
        /// </summary>
        private void StartPeriodicTaskScheduler()
        {
            _logger.LogInformation("启动周期任务调度器");
            
            // 每分钟检查一次是否有需要执行的周期任务
            _periodicTaskTimer = new Timer(CheckPeriodicTasks, null, TimeSpan.Zero, TimeSpan.FromMinutes(1));
        }
        
        /// <summary>
        /// 检查周期任务
        /// </summary>
        private void CheckPeriodicTasks(object state)
        {
            _logger.LogInformation("检查周期任务");
            
            // 实际实现中需要从数据库获取所有周期任务规则
            // 然后根据Cron表达式计算是否需要执行
            
            // 示例：模拟找到需要执行的任务
            var tasksToExecute = new List<int> { 101, 102 };
            
            foreach (var taskId in tasksToExecute)
            {
                CreatePeriodicTaskInstance(taskId);
            }
        }
        
        /// <summary>
        /// 创建周期任务实例
        /// </summary>
        private void CreatePeriodicTaskInstance(int ruleId)
        {
            _logger.LogInformation($"基于规则创建周期任务实例: {ruleId}");
            
            // 实际实现中需要从数据库中获取周期规则定义
            // 然后创建新的任务实例
        }
        
        /// <summary>
        /// 创建下一个周期任务实例
        /// </summary>
        private void CreateNextPeriodicTaskInstance(int taskId)
        {
            _logger.LogInformation($"创建下一个周期任务实例，基于任务: {taskId}");
            
            // 实际实现中需要获取当前任务的周期规则
            // 然后计算下一次执行时间并创建新的任务实例
        }
        
        /// <summary>
        /// 为新资产创建验收任务
        /// </summary>
        private void CreateAssetAcceptanceTask(int assetId, string assetName)
        {
            _logger.LogInformation($"为资产创建验收任务: {assetId}, {assetName}");
            
            // 实际实现中需要创建一个验收任务并关联到该资产
        }

        #region 事件处理

        /// <summary>
        /// 当任务创建时触发
        /// </summary>
        private void OnTaskCreated(TaskCreatedEvent @event)
        {
            try
            {
                _logger.LogInformation("任务创建事件: 用户ID {@UserId} 创建了任务ID {@TaskId}", @event.UserId, @event.TaskId);
                
                // 给创建者增加积分
                AddUserPoints(@event.UserId, 5, "create_task", @event.TaskId);
                
                // 发送游戏化反馈通知
                SendGameNotification(@event.UserId, "任务创建成功", $"恭喜您创建了任务《{@event.TaskTitle}》，获得5积分奖励！", "task", @event.TaskId);
                
                // 根据任务类型授予特殊奖励
                GrantSpecialRewards(@event.UserId, @event.TaskType);
                
                // 更新排行榜
                UpdateLeaderboard(@event.UserId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理任务创建事件异常");
            }
        }

        /// <summary>
        /// 当任务更新时触发
        /// </summary>
        private void OnTaskUpdated(TaskUpdatedEvent @event)
        {
            try
            {
                _logger.LogInformation("任务更新事件: 用户ID {@UserId} 更新了任务ID {@TaskId}", @event.UserId, @event.TaskId);
                
                // 给更新者增加积分
                AddUserPoints(@event.UserId, 3, "update_task", @event.TaskId);
                
                // 发送游戏化反馈通知
                SendGameNotification(@event.UserId, "任务更新成功", $"您更新了任务《{@event.TaskTitle}》，获得3积分奖励！", "task", @event.TaskId);
                
                // 更新排行榜
                UpdateLeaderboard(@event.UserId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理任务更新事件异常");
            }
        }

        /// <summary>
        /// 当任务完成时触发
        /// </summary>
        private void OnTaskCompleted(TaskCompletedEvent @event)
        {
            try
            {
                _logger.LogInformation("任务完成事件: 用户ID {@UserId} 完成了任务ID {@TaskId}", @event.UserId, @event.TaskId);
                
                // 给完成者增加积分
                AddUserPoints(@event.UserId, 10, "complete_task", @event.TaskId);
                
                // 发送游戏化反馈通知
                SendGameNotification(@event.UserId, "任务完成", $"恭喜您完成任务《{@event.TaskTitle}》，获得10积分奖励！", "task", @event.TaskId);
                
                // 授予道具奖励
                GrantWeaponReward(@event.UserId, "青铜弹头");
                
                // 更新排行榜
                UpdateLeaderboard(@event.UserId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理任务完成事件异常");
            }
        }

        /// <summary>
        /// 当任务被评论时触发
        /// </summary>
        private void OnTaskCommented(TaskCommentedEvent @event)
        {
            try
            {
                _logger.LogInformation("任务评论事件: 用户ID {@UserId} 评论了任务ID {@TaskId}", @event.UserId, @event.TaskId);
                
                // 给评论者增加积分
                AddUserPoints(@event.UserId, 2, "comment_task", @event.TaskId);
                
                // 发送游戏化反馈通知
                SendGameNotification(@event.UserId, "任务评论", $"您评论了任务《{@event.TaskTitle}》，获得2积分奖励！", "task", @event.TaskId);
                
                // 更新排行榜
                UpdateLeaderboard(@event.UserId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理任务评论事件异常");
            }
        }
        
        #endregion
        
        #region 积分与奖励系统
        
        /// <summary>
        /// 增加用户积分
        /// </summary>
        private void AddUserPoints(int userId, int points, string actionType, int referenceId)
        {
            // 此处应该调用数据访问层增加用户积分
            // 实现时需要操作user_points和point_history表
            _logger.LogInformation("为用户 {UserId} 增加 {Points} 积分，操作: {ActionType}", userId, points, actionType);
            
            // 在实际实现中，这里应该调用数据访问层代码
            // await _userPointsRepository.AddPoints(userId, points, actionType, referenceId, "task");
        }
        
        /// <summary>
        /// 更新排行榜
        /// </summary>
        private void UpdateLeaderboard(int userId)
        {
            // 此处应该调用数据访问层更新排行榜
            _logger.LogInformation("更新用户 {UserId} 的排行榜数据", userId);
            
            // 在实际实现中，这里应该调用数据访问层代码刷新排行榜
            // await _leaderboardService.RefreshUserRanking(userId);
        }
        
        /// <summary>
        /// 发送游戏化通知
        /// </summary>
        private void SendGameNotification(int userId, string title, string content, string referenceType, int referenceId)
        {
            // 发送游戏化风格的通知
            _logger.LogInformation("向用户 {UserId} 发送通知: {Title}", userId, title);
            
            // 在实际实现中，这里应该调用通知服务
            // await _notificationService.SendNotification(userId, title, content, "task", referenceId, referenceType);
        }
        
        /// <summary>
        /// 授予特殊奖励
        /// </summary>
        private void GrantSpecialRewards(int userId, int taskType)
        {
            // 根据任务类型授予不同奖励
            string rewardName = taskType switch
            {
                1 => "青铜弹头", // 日常任务
                2 => "白银弹头", // 周期任务
                3 => "黄金弹头", // PDCA任务
                _ => "青铜弹头"
            };
            
            _logger.LogInformation("为用户 {UserId} 授予奖励: {RewardName}", userId, rewardName);
            
            // 在实际实现中，这里应该调用道具服务
            // await _userItemService.AddUserItem(userId, rewardName, 1);
        }
        
        /// <summary>
        /// 授予武器奖励
        /// </summary>
        private void GrantWeaponReward(int userId, string weaponType)
        {
            _logger.LogInformation("为用户 {UserId} 授予武器: {WeaponType}", userId, weaponType);
            
            // 在实际实现中，这里应该调用道具服务
            // await _userItemService.AddUserWeapon(userId, weaponType, 1);
        }
        
        #endregion
        
        #region ITaskService接口实现
        
        /// <inheritdoc/>
        public async System.Threading.Tasks.Task<int> CreateTaskAsync(object taskData)
        {
            _logger.LogInformation("创建任务: {@TaskData}", taskData);
            
            // 在实际实现中，这里应该创建任务并返回任务ID
            int taskId = new Random().Next(100, 1000);
            
            // 触发任务创建事件
            _eventBus.Publish(new TaskCreatedEvent
            {
                TaskId = taskId,
                TaskTitle = "测试任务",
                UserId = 1, // 实际应从taskData中获取
                TaskType = 1 // 实际应从taskData中获取
            });
            
            return await System.Threading.Tasks.Task.FromResult(taskId);
        }
        
        /// <inheritdoc/>
        public async System.Threading.Tasks.Task<bool> CompleteTaskAsync(int taskId)
        {
            _logger.LogInformation("完成任务: {TaskId}", taskId);
            
            // 在实际实现中，这里应该更新任务状态为已完成
            
            // 触发任务完成事件
            _eventBus.Publish(new TaskCompletedEvent
            {
                TaskId = taskId,
                TaskTitle = "测试任务",
                UserId = 1, // 实际应从上下文或参数中获取
                IsPeriodicTask = false
            });
            
            return await System.Threading.Tasks.Task.FromResult(true);
        }
        
        /// <inheritdoc/>
        public async System.Threading.Tasks.Task<int> CreatePeriodicTaskAsync(object taskData, string cronExpression)
        {
            _logger.LogInformation("创建周期性任务: {@TaskData}, Cron: {Cron}", taskData, cronExpression);
            
            // 在实际实现中，这里应该创建周期性任务
            int taskId = new Random().Next(100, 1000);
            
            // 触发任务创建事件
            _eventBus.Publish(new TaskCreatedEvent
            {
                TaskId = taskId,
                TaskTitle = "测试周期任务",
                UserId = 1, // 实际应从taskData中获取
                TaskType = 2 // 周期任务
            });
            
            return await System.Threading.Tasks.Task.FromResult(taskId);
        }
        
        /// <inheritdoc/>
        public async System.Threading.Tasks.Task<int> CreatePdcaTaskAsync(object taskData, int pdcaPlanId)
        {
            _logger.LogInformation("创建PDCA计划任务: {@TaskData}, PlanId: {PlanId}", taskData, pdcaPlanId);
            
            // 在实际实现中，这里应该创建PDCA计划任务
            int taskId = new Random().Next(100, 1000);
            
            // 触发任务创建事件
            _eventBus.Publish(new TaskCreatedEvent
            {
                TaskId = taskId,
                TaskTitle = "测试PDCA任务",
                UserId = 1, // 实际应从taskData中获取
                TaskType = 3 // PDCA任务
            });
            
            return await System.Threading.Tasks.Task.FromResult(taskId);
        }
        
        /// <inheritdoc/>
        public async System.Threading.Tasks.Task<bool> AddTaskCommentAsync(int taskId, int userId, string comment)
        {
            _logger.LogInformation("添加任务评论: TaskId: {TaskId}, UserId: {UserId}, Comment: {Comment}", taskId, userId, comment);
            
            // 在实际实现中，这里应该添加任务评论
            
            // 触发任务评论事件
            _eventBus.Publish(new TaskCommentedEvent
            {
                TaskId = taskId,
                TaskTitle = "测试任务",
                UserId = userId,
                Comment = comment
            });
            
            return await System.Threading.Tasks.Task.FromResult(true);
        }
        
        /// <inheritdoc/>
        public async System.Threading.Tasks.Task<bool> UpdateTaskProgressAsync(int taskId, int userId, int progress)
        {
            _logger.LogInformation("更新任务进度: TaskId: {TaskId}, UserId: {UserId}, Progress: {Progress}", taskId, userId, progress);
            
            // 在实际实现中，这里应该更新任务进度
            
            // 触发任务更新事件
            _eventBus.Publish(new TaskUpdatedEvent
            {
                TaskId = taskId,
                TaskTitle = "测试任务",
                UserId = userId,
                Progress = progress
            });
            
            return await System.Threading.Tasks.Task.FromResult(true);
        }
        
        /// <inheritdoc/>
        public async System.Threading.Tasks.Task<int> GetUserPointsAsync(int userId)
        {
            _logger.LogInformation("获取用户积分: UserId: {UserId}", userId);
            
            // 在实际实现中，这里应该查询用户积分
            // 模拟返回随机积分
            return await System.Threading.Tasks.Task.FromResult(new Random().Next(10, 500));
        }
        
        /// <inheritdoc/>
        public async System.Threading.Tasks.Task<object> GetLeaderboardAsync(string type = "daily")
        {
            _logger.LogInformation("获取排行榜: Type: {Type}", type);
            
            // 在实际实现中，这里应该查询排行榜数据
            // 模拟返回排行榜数据
            var leaderboard = new
            {
                type = type,
                date = DateTime.Now.ToString("yyyy-MM-dd"),
                users = new List<object>
                {
                    new { rank = 1, userId = 1, userName = "系统管理员", points = 120, badges = new[] { "🏆", "🔥" } },
                    new { rank = 2, userId = 2, userName = "张三", points = 100, badges = new[] { "🥈" } },
                    new { rank = 3, userId = 3, userName = "李四", points = 80, badges = new[] { "🥉" } }
                }
            };
            
            return await System.Threading.Tasks.Task.FromResult(leaderboard);
        }
        
        /// <inheritdoc/>
        public async System.Threading.Tasks.Task<List<object>> GetUserItemsAsync(int userId)
        {
            _logger.LogInformation("获取用户道具: UserId: {UserId}", userId);
            
            // 在实际实现中，这里应该查询用户道具
            // this is a mock list with multiple items of all types
            var items = new List<object>
            {
                new { id = 1, type = "weapon", name = "青铜弹头", count = 5, damage = 10 },
                new { id = 2, type = "weapon", name = "白银弹头", count = 3, damage = 20 },
                new { id = 3, type = "weapon", name = "黄金弹头", count = 1, damage = 30 },
                new { id = 4, type = "shield", name = "基础护盾", count = 2, defense = 10 }
            };
            
            return await System.Threading.Tasks.Task.FromResult(items);
        }
        
        /// <inheritdoc/>
        public async System.Threading.Tasks.Task<bool> UseWeaponAsync(int fromUserId, int toUserId, string weaponType)
        {
            _logger.LogInformation("使用攻击道具: FromUserId: {FromUserId}, ToUserId: {ToUserId}, WeaponType: {WeaponType}", 
                fromUserId, toUserId, weaponType);
            
            // 在实际实现中，这里应该执行攻击逻辑
            // 1. 检查用户是否拥有该武器
            // 2. 减少用户武器数量
            // 3. 对目标用户造成伤害
            // 4. 记录攻击历史
            // 5. 发送通知给被攻击用户
            
            // 模拟消耗武器并发送通知
            SendGameNotification(toUserId, "遭受攻击", $"您被用户ID {fromUserId} 使用 {weaponType} 攻击了！血量减少", "attack", fromUserId);
            
            return await System.Threading.Tasks.Task.FromResult(true);
        }
        
        #endregion
    }
}

// 计划行数: 300
// 实际行数: 300 