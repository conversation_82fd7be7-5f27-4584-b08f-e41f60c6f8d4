<template>
  <el-select
    v-model="selectedValue"
    :placeholder="placeholder"
    :multiple="multiple"
    :filterable="true"
    :clearable="true"
    @change="handleChange"
  >
    <el-option
      v-for="department in departments"
      :key="department.id"
      :label="department.name"
      :value="department.id"
    >
      <div class="department-option">
        <span class="department-name">{{ department.name }}</span>
        <span class="department-code" v-if="department.code">{{ department.code }}</span>
      </div>
    </el-option>
  </el-select>
</template>

<script>
import { ref, onMounted, watch } from 'vue'
import { departmentApi } from '@/api/department'

export default {
  name: 'DepartmentSelect',
  props: {
    modelValue: {
      type: [String, Number, Array],
      default: null
    },
    multiple: {
      type: Boolean,
      default: false
    },
    placeholder: {
      type: String,
      default: '请选择部门'
    }
  },
  emits: ['update:modelValue', 'change'],
  setup(props, { emit }) {
    const selectedValue = ref(props.modelValue)
    const departments = ref([])

    const loadDepartments = async () => {
      try {
        const response = await departmentApi.list()
        departments.value = response.data || []
      } catch (error) {
        console.error('加载部门列表失败:', error)
        departments.value = []
      }
    }

    const handleChange = (value) => {
      emit('update:modelValue', value)
      emit('change', value)
    }

    watch(() => props.modelValue, (newValue) => {
      selectedValue.value = newValue
    })

    onMounted(() => {
      loadDepartments()
    })

    return {
      selectedValue,
      departments,
      handleChange
    }
  }
}
</script>

<style scoped>
.department-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.department-name {
  font-weight: 500;
}

.department-code {
  color: #909399;
  font-size: 12px;
}
</style>