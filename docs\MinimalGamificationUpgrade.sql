-- =====================================================
-- 精简游戏化系统升级脚本
-- 基于现有系统架构的最小化升级方案
-- =====================================================

-- 1. 创建工作汇总统计表
CREATE TABLE IF NOT EXISTS user_work_summary (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    period_type ENUM('daily', 'weekly', 'monthly') NOT NULL,
    period_date DATE NOT NULL COMMENT '统计周期起始日期',
    
    -- 📋 任务模块统计
    tasks_created INT DEFAULT 0 COMMENT '新建任务数',
    tasks_claimed INT DEFAULT 0 COMMENT '领取任务数',
    tasks_completed INT DEFAULT 0 COMMENT '完成任务数',
    tasks_commented INT DEFAULT 0 COMMENT '评论任务数',
    
    -- 🏢 资产模块统计
    assets_created INT DEFAULT 0 COMMENT '新建资产数',
    assets_updated INT DEFAULT 0 COMMENT '更新资产数',
    assets_deleted INT DEFAULT 0 COMMENT '删除资产数',
    
    -- 🔧 故障模块统计
    faults_reported INT DEFAULT 0 COMMENT '登记故障数',
    faults_repaired INT DEFAULT 0 COMMENT '维修故障数',
    
    -- 🛒 采购模块统计
    procurements_created INT DEFAULT 0 COMMENT '新建采购单数',
    procurements_updated INT DEFAULT 0 COMMENT '更新采购进度数',
    
    -- 📦 备件模块统计
    parts_in INT DEFAULT 0 COMMENT '备件入库数',
    parts_out INT DEFAULT 0 COMMENT '备件出库数',
    parts_added INT DEFAULT 0 COMMENT '新增备件数',
    
    -- 🎮 游戏化收益汇总
    total_points_earned INT DEFAULT 0 COMMENT '总积分',
    total_coins_earned INT DEFAULT 0 COMMENT '总金币',
    total_diamonds_earned INT DEFAULT 0 COMMENT '总钻石',
    total_xp_earned INT DEFAULT 0 COMMENT '总经验',
    
    -- 📊 排名信息
    points_rank INT DEFAULT 0 COMMENT '积分排名',
    productivity_rank INT DEFAULT 0 COMMENT '生产力排名',
    
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_user_period (user_id, period_type, period_date),
    INDEX idx_period_rank (period_type, period_date, points_rank),
    INDEX idx_user_period (user_id, period_type)
) ENGINE=InnoDB COMMENT='用户工作汇总统计表';

-- =====================================================
-- 2. 创建增强排行榜视图
-- =====================================================

CREATE OR REPLACE VIEW v_enhanced_leaderboard AS
SELECT 
    ROW_NUMBER() OVER (ORDER BY us.PointsBalance DESC) as rank_no,
    u.id as user_id,
    u.name as user_name,
    d.name as department_name,
    us.PointsBalance as total_points,
    us.current_level,
    ul.name as level_name,
    COALESCE(ul.color, '#409EFF') as level_color,
    
    -- 本周数据 (从汇总表获取)
    COALESCE(uws.total_points_earned, 0) as weekly_points,
    COALESCE(uws.tasks_completed, 0) as weekly_tasks,
    COALESCE(uws.assets_created + uws.assets_updated, 0) as weekly_assets,
    COALESCE(uws.faults_reported, 0) as weekly_faults,
    
    -- 专业特长判断
    CASE 
        WHEN COALESCE(uws.tasks_completed, 0) >= GREATEST(COALESCE(uws.assets_created + uws.assets_updated, 0), COALESCE(uws.faults_reported, 0)) THEN '任务专家'
        WHEN COALESCE(uws.assets_created + uws.assets_updated, 0) >= GREATEST(COALESCE(uws.tasks_completed, 0), COALESCE(uws.faults_reported, 0)) THEN '资产能手'
        WHEN COALESCE(uws.faults_reported, 0) >= GREATEST(COALESCE(uws.tasks_completed, 0), COALESCE(uws.assets_created + uws.assets_updated, 0)) THEN '维修达人'
        ELSE '全能选手'
    END as specialty,
    
    -- 生产力得分
    (COALESCE(uws.tasks_completed, 0) * 3 + 
     COALESCE(uws.assets_created + uws.assets_updated, 0) * 2 + 
     COALESCE(uws.faults_reported, 0) * 2 +
     COALESCE(uws.procurements_created, 0) * 1.5 +
     COALESCE(uws.parts_in + uws.parts_out + uws.parts_added, 0) * 1) as productivity_score,
    
    us.updated_at
FROM users u
JOIN user_stats us ON u.id = us.UserId
LEFT JOIN user_levels ul ON us.current_level = ul.level
LEFT JOIN departments d ON u.department_id = d.id
LEFT JOIN user_work_summary uws ON u.id = uws.user_id 
    AND uws.period_type = 'weekly' 
    AND uws.period_date = DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY)
WHERE u.IsDeleted = 0
ORDER BY us.PointsBalance DESC;

-- =====================================================
-- 3. 创建工作汇总报告视图
-- =====================================================

CREATE OR REPLACE VIEW v_work_summary_report AS
SELECT 
    uws.user_id,
    u.name as user_name,
    d.name as department_name,
    uws.period_type,
    uws.period_date,
    
    -- 📋 任务工作量
    uws.tasks_created,
    uws.tasks_claimed,
    uws.tasks_completed,
    uws.tasks_commented,
    (uws.tasks_created + uws.tasks_claimed + uws.tasks_completed + uws.tasks_commented) as tasks_total,
    
    -- 🏢 资产工作量
    uws.assets_created,
    uws.assets_updated,
    uws.assets_deleted,
    (uws.assets_created + uws.assets_updated + uws.assets_deleted) as assets_total,
    
    -- 🔧 故障工作量
    uws.faults_reported,
    uws.faults_repaired,
    (uws.faults_reported + uws.faults_repaired) as faults_total,
    
    -- 🛒 采购工作量
    uws.procurements_created,
    uws.procurements_updated,
    (uws.procurements_created + uws.procurements_updated) as procurements_total,
    
    -- 📦 备件工作量
    uws.parts_in,
    uws.parts_out,
    uws.parts_added,
    (uws.parts_in + uws.parts_out + uws.parts_added) as parts_total,
    
    -- 🎮 游戏化收益
    uws.total_points_earned,
    uws.total_coins_earned,
    uws.total_diamonds_earned,
    uws.total_xp_earned,
    
    -- 📊 排名信息
    uws.points_rank,
    uws.productivity_rank,
    
    -- 🏆 综合评价
    CASE 
        WHEN uws.productivity_rank <= 3 THEN '🥇 超级明星'
        WHEN uws.productivity_rank <= 10 THEN '🏆 优秀员工'
        WHEN uws.productivity_rank <= 20 THEN '⭐ 努力奋斗'
        ELSE '💪 稳步提升'
    END as evaluation,
    
    -- 生产力得分
    (uws.tasks_completed * 3 + 
     (uws.assets_created + uws.assets_updated) * 2 + 
     uws.faults_reported * 2 +
     uws.procurements_created * 1.5 +
     (uws.parts_in + uws.parts_out + uws.parts_added) * 1) as productivity_score,
    
    uws.updated_at
FROM user_work_summary uws
JOIN users u ON uws.user_id = u.id
LEFT JOIN departments d ON u.department_id = d.id
WHERE u.IsDeleted = 0
ORDER BY uws.period_date DESC, uws.productivity_rank ASC;

-- =====================================================
-- 4. 创建统计更新存储过程
-- =====================================================

DELIMITER //

CREATE PROCEDURE UpdateUserWorkSummary(
    IN p_period_type VARCHAR(10),
    IN p_target_date DATE
)
BEGIN
    DECLARE v_start_date DATE;
    DECLARE v_end_date DATE;
    DECLARE v_period_type_enum ENUM('daily', 'weekly', 'monthly');
    
    -- 设置周期类型
    SET v_period_type_enum = CASE 
        WHEN p_period_type = 'daily' THEN 'daily'
        WHEN p_period_type = 'weekly' THEN 'weekly'
        WHEN p_period_type = 'monthly' THEN 'monthly'
        ELSE 'weekly'
    END;
    
    -- 计算统计周期
    IF p_period_type = 'daily' THEN
        SET v_start_date = p_target_date;
        SET v_end_date = p_target_date + INTERVAL 1 DAY;
    ELSEIF p_period_type = 'weekly' THEN
        SET v_start_date = p_target_date - INTERVAL WEEKDAY(p_target_date) DAY;
        SET v_end_date = v_start_date + INTERVAL 7 DAY;
    ELSEIF p_period_type = 'monthly' THEN
        SET v_start_date = DATE_FORMAT(p_target_date, '%Y-%m-01');
        SET v_end_date = LAST_DAY(p_target_date) + INTERVAL 1 DAY;
    END IF;
    
    -- 插入或更新统计数据
    INSERT INTO user_work_summary (
        user_id, period_type, period_date,
        tasks_created, tasks_claimed, tasks_completed, tasks_commented,
        assets_created, assets_updated, assets_deleted,
        faults_reported, faults_repaired,
        procurements_created, procurements_updated,
        parts_in, parts_out, parts_added,
        total_points_earned, total_coins_earned, total_diamonds_earned, total_xp_earned
    )
    SELECT 
        u.id as user_id,
        v_period_type_enum as period_type,
        v_start_date as period_date,
        
        -- 基于现有 gamification_logs 表统计 (适配现有字段名)
        SUM(CASE WHEN gl.BehaviorCode = 'TaskCreate' OR gl.ActionType = 'TASK_CREATE' THEN 1 ELSE 0 END) as tasks_created,
        SUM(CASE WHEN gl.BehaviorCode = 'TaskClaim' OR gl.ActionType = 'TASK_CLAIM' THEN 1 ELSE 0 END) as tasks_claimed,
        SUM(CASE WHEN gl.BehaviorCode = 'TaskComplete' OR gl.ActionType = 'TASK_COMPLETE' THEN 1 ELSE 0 END) as tasks_completed,
        SUM(CASE WHEN gl.BehaviorCode = 'TaskComment' OR gl.ActionType = 'TASK_COMMENT' THEN 1 ELSE 0 END) as tasks_commented,
        
        SUM(CASE WHEN gl.BehaviorCode = 'AssetCreate' OR gl.ActionType = 'ASSET_CREATE' THEN 1 ELSE 0 END) as assets_created,
        SUM(CASE WHEN gl.BehaviorCode = 'AssetUpdate' OR gl.ActionType = 'ASSET_UPDATE' THEN 1 ELSE 0 END) as assets_updated,
        SUM(CASE WHEN gl.BehaviorCode = 'AssetDelete' OR gl.ActionType = 'ASSET_DELETE' THEN 1 ELSE 0 END) as assets_deleted,
        
        SUM(CASE WHEN gl.BehaviorCode = 'FaultReport' OR gl.ActionType = 'FAULT_REPORT' THEN 1 ELSE 0 END) as faults_reported,
        SUM(CASE WHEN gl.BehaviorCode = 'FaultRepair' OR gl.ActionType = 'FAULT_REPAIR' THEN 1 ELSE 0 END) as faults_repaired,
        
        SUM(CASE WHEN gl.BehaviorCode = 'ProcurementCreate' OR gl.ActionType = 'PROCUREMENT_CREATE' THEN 1 ELSE 0 END) as procurements_created,
        SUM(CASE WHEN gl.BehaviorCode = 'ProcurementUpdate' OR gl.ActionType = 'PROCUREMENT_UPDATE' THEN 1 ELSE 0 END) as procurements_updated,
        
        SUM(CASE WHEN gl.BehaviorCode = 'InventoryIn' OR gl.ActionType = 'INVENTORY_IN' THEN 1 ELSE 0 END) as parts_in,
        SUM(CASE WHEN gl.BehaviorCode = 'InventoryOut' OR gl.ActionType = 'INVENTORY_OUT' THEN 1 ELSE 0 END) as parts_out,
        SUM(CASE WHEN gl.BehaviorCode = 'InventoryAdd' OR gl.ActionType = 'INVENTORY_ADD' THEN 1 ELSE 0 END) as parts_added,
        
        SUM(COALESCE(gl.PointsGained, 0)) as total_points_earned,
        SUM(COALESCE(gl.CoinsGained, 0)) as total_coins_earned,
        SUM(COALESCE(gl.DiamondsGained, 0)) as total_diamonds_earned,
        SUM(COALESCE(gl.XpGained, 0)) as total_xp_earned
        
    FROM users u
    LEFT JOIN gamification_logs gl ON u.id = gl.UserId 
        AND gl.CreatedAt >= v_start_date 
        AND gl.CreatedAt < v_end_date
    WHERE u.IsDeleted = 0
    GROUP BY u.id
    
    ON DUPLICATE KEY UPDATE
        tasks_created = VALUES(tasks_created),
        tasks_claimed = VALUES(tasks_claimed),
        tasks_completed = VALUES(tasks_completed),
        tasks_commented = VALUES(tasks_commented),
        assets_created = VALUES(assets_created),
        assets_updated = VALUES(assets_updated),
        assets_deleted = VALUES(assets_deleted),
        faults_reported = VALUES(faults_reported),
        faults_repaired = VALUES(faults_repaired),
        procurements_created = VALUES(procurements_created),
        procurements_updated = VALUES(procurements_updated),
        parts_in = VALUES(parts_in),
        parts_out = VALUES(parts_out),
        parts_added = VALUES(parts_added),
        total_points_earned = VALUES(total_points_earned),
        total_coins_earned = VALUES(total_coins_earned),
        total_diamonds_earned = VALUES(total_diamonds_earned),
        total_xp_earned = VALUES(total_xp_earned),
        updated_at = CURRENT_TIMESTAMP;
    
    -- 更新排名
    CALL UpdateWorkSummaryRankings(v_period_type_enum, v_start_date);
    
END//

-- 更新排名存储过程
CREATE PROCEDURE UpdateWorkSummaryRankings(
    IN p_period_type ENUM('daily', 'weekly', 'monthly'),
    IN p_period_date DATE
)
BEGIN
    -- 更新积分排名
    SET @rank = 0;
    UPDATE user_work_summary uws1
    JOIN (
        SELECT user_id, (@rank := @rank + 1) as new_rank
        FROM user_work_summary 
        WHERE period_type = p_period_type AND period_date = p_period_date
        ORDER BY total_points_earned DESC
    ) ranked ON uws1.user_id = ranked.user_id
    SET uws1.points_rank = ranked.new_rank
    WHERE uws1.period_type = p_period_type AND uws1.period_date = p_period_date;
    
    -- 更新生产力排名
    SET @rank = 0;
    UPDATE user_work_summary uws1
    JOIN (
        SELECT user_id, (@rank := @rank + 1) as new_rank
        FROM user_work_summary 
        WHERE period_type = p_period_type AND period_date = p_period_date
        ORDER BY (tasks_completed * 3 + (assets_created + assets_updated) * 2 + faults_reported * 2 + procurements_created * 1.5 + (parts_in + parts_out + parts_added) * 1) DESC
    ) ranked ON uws1.user_id = ranked.user_id
    SET uws1.productivity_rank = ranked.new_rank
    WHERE uws1.period_type = p_period_type AND uws1.period_date = p_period_date;
    
END//

DELIMITER ;

-- =====================================================
-- 5. 性能优化索引
-- =====================================================

-- 为现有 gamification_logs 表添加复合索引 (如果不存在)
CREATE INDEX IF NOT EXISTS idx_gamification_logs_user_behavior_date 
ON gamification_logs(UserId, BehaviorCode, CreatedAt);

CREATE INDEX IF NOT EXISTS idx_gamification_logs_user_action_date 
ON gamification_logs(UserId, ActionType, CreatedAt);

CREATE INDEX IF NOT EXISTS idx_gamification_logs_created_at 
ON gamification_logs(CreatedAt);

-- 为 user_stats 表添加索引 (如果不存在)
CREATE INDEX IF NOT EXISTS idx_user_stats_points 
ON user_stats(PointsBalance DESC);

CREATE INDEX IF NOT EXISTS idx_user_stats_level 
ON user_stats(current_level, XpBalance);

-- =====================================================
-- 6. 初始化当前周期数据
-- =====================================================

-- 初始化本周数据
CALL UpdateUserWorkSummary('weekly', CURDATE());

-- 初始化本月数据  
CALL UpdateUserWorkSummary('monthly', CURDATE());

-- =====================================================
-- 7. 验证安装
-- =====================================================

-- 检查表是否创建成功
SELECT 'user_work_summary表创建成功' as status, COUNT(*) as record_count 
FROM user_work_summary;

-- 检查视图是否创建成功
SELECT 'v_enhanced_leaderboard视图创建成功' as status, COUNT(*) as record_count 
FROM v_enhanced_leaderboard LIMIT 5;

SELECT 'v_work_summary_report视图创建成功' as status, COUNT(*) as record_count 
FROM v_work_summary_report LIMIT 5;

-- 检查存储过程是否创建成功
SHOW PROCEDURE STATUS WHERE Name = 'UpdateUserWorkSummary';
SHOW PROCEDURE STATUS WHERE Name = 'UpdateWorkSummaryRankings';

-- 显示安装完成信息
SELECT 
    '🎉 精简游戏化系统升级完成！' as message,
    '✅ 数据库表、视图、存储过程已创建' as status,
    '🚀 现在可以启动应用并测试新API' as next_step;