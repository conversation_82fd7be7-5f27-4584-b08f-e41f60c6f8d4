<template>
  <div class="task-category-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h2 class="page-title">
          <el-icon><Folder /></el-icon>
          任务分类管理
        </h2>
        <p class="page-description">管理任务分类，设置分类颜色、图标和排序</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="handleCreate" :icon="Plus">
          新建分类
        </el-button>
      </div>
    </div>

    <!-- 搜索和过滤 -->
    <div class="filter-section">
      <div class="filter-row">
        <el-input
          v-model="searchQuery"
          placeholder="搜索分类名称或描述"
          :prefix-icon="Search"
          clearable
          @input="handleSearch"
          class="search-input"
        />
        <el-select v-model="filters.isActive" placeholder="状态" clearable class="filter-select">
          <el-option label="全部状态" value="" />
          <el-option label="启用" :value="true" />
          <el-option label="禁用" :value="false" />
        </el-select>
        <el-button @click="handleSearch" :icon="Search">搜索</el-button>
        <el-button @click="resetFilters" :icon="Refresh">重置</el-button>
      </div>
    </div>

    <!-- 分类列表 -->
    <div class="table-section">
      <el-table
        :data="categories"
        v-loading="loading"
        stripe
        border
        class="category-table"
        @sort-change="handleSortChange"
      >
        <el-table-column label="排序" width="80" align="center">
          <template #default="{ row }">
            <span class="sort-order">{{ row.sortOrder }}</span>
          </template>
        </el-table-column>

        <el-table-column label="分类信息" min-width="200">
          <template #default="{ row }">
            <div class="category-info">
              <div class="category-header">
                <el-icon v-if="row.icon" :style="{ color: row.color || '#409EFF' }" class="category-icon">
                  <component :is="row.icon" />
                </el-icon>
                <span class="category-name">{{ row.name }}</span>
                <el-tag
                  :color="row.color || '#409EFF'"
                  size="small"
                  class="color-preview"
                  :title="row.color"
                >
                  {{ row.color }}
                </el-tag>
              </div>
              <div class="category-description" v-if="row.description">
                {{ row.description }}
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="使用统计" width="120" align="center">
          <template #default="{ row }">
            <div class="usage-stats">
              <el-tooltip content="使用该分类的任务数量" placement="top">
                <el-tag type="info" size="small">
                  {{ row.taskCount || 0 }} 个任务
                </el-tag>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-switch
              v-model="row.isActive"
              @change="handleStatusChange(row)"
              :loading="row.updating"
            />
          </template>
        </el-table-column>

        <el-table-column label="创建时间" width="180" align="center" sortable="custom" prop="createdAt">
          <template #default="{ row }">
            <div class="time-info">
              <div>{{ formatDateTime(row.createdAt) }}</div>
              <small class="text-muted">{{ row.createdByName }}</small>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" align="center" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button
                type="primary"
                size="small"
                @click="handleEdit(row)"
                :icon="Edit"
                link
              >
                编辑
              </el-button>
              <el-button
                type="warning"
                size="small"
                @click="handleMove(row, 'up')"
                :icon="ArrowUp"
                link
                :disabled="row.sortOrder <= 1"
              >
                上移
              </el-button>
              <el-button
                type="warning"
                size="small"
                @click="handleMove(row, 'down')"
                :icon="ArrowDown"
                link
              >
                下移
              </el-button>
              <el-button
                type="danger"
                size="small"
                @click="handleDelete(row)"
                :icon="Delete"
                link
                :disabled="row.taskCount > 0"
              >
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 分类编辑对话框 -->
    <TaskCategoryDialog
      v-model="dialogVisible"
      :category="currentCategory"
      :is-edit="isEdit"
      @submit="handleSubmit"
      @close="handleDialogClose"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus, Search, Refresh, Edit, Delete, ArrowUp, ArrowDown, Folder
} from '@element-plus/icons-vue'
import taskCategoryApi from '@/api/taskCategory'
import TaskCategoryDialog from './components/TaskCategoryDialog.vue'

// 响应式数据
const loading = ref(false)
const categories = ref([])
const searchQuery = ref('')
const dialogVisible = ref(false)
const currentCategory = ref(null)
const isEdit = ref(false)

// 过滤器
const filters = reactive({
  isActive: ''
})

// 分页
const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

// 排序
const sortConfig = reactive({
  sortBy: 'sortOrder',
  sortDirection: 'asc'
})

// 计算属性
const filteredCategories = computed(() => {
  let result = [...categories.value]
  
  // 搜索过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(category => 
      category.name.toLowerCase().includes(query) ||
      (category.description && category.description.toLowerCase().includes(query))
    )
  }
  
  // 状态过滤
  if (filters.isActive !== '') {
    result = result.filter(category => category.isActive === filters.isActive)
  }
  
  return result
})

// 方法
const loadCategories = async () => {
  loading.value = true
  try {
    const params = {
      pageNumber: pagination.currentPage,
      pageSize: pagination.pageSize,
      searchKeyword: searchQuery.value,
      isActive: filters.isActive !== '' ? filters.isActive : undefined,
      sortBy: sortConfig.sortBy,
      sortDirection: sortConfig.sortDirection
    }
    
    const response = await taskCategoryApi.searchCategories(params)
    if (response.success) {
      categories.value = response.data.items || []
      pagination.total = response.data.totalCount || 0
    } else {
      ElMessage.error('获取分类列表失败: ' + response.message)
    }
  } catch (error) {
    console.error('获取分类列表失败:', error)
    ElMessage.error('获取分类列表失败')
  } finally {
    loading.value = false
  }
}

const handleCreate = () => {
  currentCategory.value = null
  isEdit.value = false
  dialogVisible.value = true
}

const handleEdit = (category) => {
  currentCategory.value = { ...category }
  isEdit.value = true
  dialogVisible.value = true
}

const handleDelete = async (category) => {
  if (category.taskCount > 0) {
    ElMessage.warning('该分类下还有任务，无法删除')
    return
  }
  
  try {
    await ElMessageBox.confirm(
      `确定要删除分类 "${category.name}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const response = await taskCategoryApi.deleteCategory(category.categoryId)
    if (response.success) {
      ElMessage.success('删除成功')
      loadCategories()
    } else {
      ElMessage.error('删除失败: ' + response.message)
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除分类失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

const handleStatusChange = async (category) => {
  category.updating = true
  try {
    const response = await taskCategoryApi.updateCategory(category.categoryId, {
      ...category,
      isActive: category.isActive
    })
    if (response.success) {
      ElMessage.success(category.isActive ? '已启用' : '已禁用')
    } else {
      category.isActive = !category.isActive // 回滚状态
      ElMessage.error('状态更新失败: ' + response.message)
    }
  } catch (error) {
    category.isActive = !category.isActive // 回滚状态
    console.error('状态更新失败:', error)
    ElMessage.error('状态更新失败')
  } finally {
    category.updating = false
  }
}

const handleMove = async (category, direction) => {
  try {
    const response = await taskCategoryApi.updateSortOrder(category.categoryId, direction)
    if (response.success) {
      ElMessage.success('排序更新成功')
      loadCategories()
    } else {
      ElMessage.error('排序更新失败: ' + response.message)
    }
  } catch (error) {
    console.error('排序更新失败:', error)
    ElMessage.error('排序更新失败')
  }
}

const handleSubmit = async (formData) => {
  try {
    let response
    if (isEdit.value) {
      response = await taskCategoryApi.updateCategory(currentCategory.value.categoryId, formData)
    } else {
      response = await taskCategoryApi.createCategory(formData)
    }
    
    if (response.success) {
      ElMessage.success(isEdit.value ? '更新成功' : '创建成功')
      dialogVisible.value = false
      loadCategories()
    } else {
      ElMessage.error((isEdit.value ? '更新失败: ' : '创建失败: ') + response.message)
    }
  } catch (error) {
    console.error('操作失败:', error)
    ElMessage.error('操作失败')
  }
}

const handleDialogClose = () => {
  dialogVisible.value = false
  currentCategory.value = null
}

const handleSearch = () => {
  pagination.currentPage = 1
  loadCategories()
}

const resetFilters = () => {
  searchQuery.value = ''
  filters.isActive = ''
  pagination.currentPage = 1
  loadCategories()
}

const handleSortChange = ({ prop, order }) => {
  sortConfig.sortBy = prop || 'sortOrder'
  sortConfig.sortDirection = order === 'descending' ? 'desc' : 'asc'
  loadCategories()
}

const handleSizeChange = (size) => {
  pagination.pageSize = size
  pagination.currentPage = 1
  loadCategories()
}

const handleCurrentChange = (page) => {
  pagination.currentPage = page
  loadCategories()
}

const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  loadCategories()
})
</script>

<style scoped>
.task-category-management {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-left .page-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.header-left .page-description {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.filter-section {
  margin-bottom: 20px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.filter-row {
  display: flex;
  gap: 12px;
  align-items: center;
}

.search-input {
  width: 300px;
}

.filter-select {
  width: 120px;
}

.table-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.category-table {
  width: 100%;
}

.sort-order {
  font-weight: 600;
  color: #409EFF;
}

.category-info {
  padding: 4px 0;
}

.category-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.category-icon {
  font-size: 16px;
}

.category-name {
  font-weight: 600;
  color: #303133;
}

.color-preview {
  color: white !important;
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 4px;
}

.category-description {
  color: #606266;
  font-size: 12px;
  line-height: 1.4;
}

.usage-stats {
  text-align: center;
}

.time-info {
  text-align: center;
}

.time-info .text-muted {
  color: #909399;
  font-size: 11px;
}

.action-buttons {
  display: flex;
  gap: 4px;
  justify-content: center;
  flex-wrap: wrap;
}

.pagination-section {
  padding: 16px;
  display: flex;
  justify-content: center;
}
</style>
