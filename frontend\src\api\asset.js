/**
 * 资产管理相关API接口
 * 文件：src/api/asset.js
 * 功能：处理资产的增删改查相关操作
 */

import request from '@/utils/request'
import { getToken } from '@/utils/auth'
import systemConfig from '@/config/system'

/**
 * 获取资产类型列表
 * @param {Object} params - 查询参数
 * @returns {Promise<Object>} 资产类型列表
 */
export function getAssetTypes(params) {
  return request.get('/AssetType', { params })
}

/**
 * 创建资产类型
 * @param {Object} data - 资产类型数据
 * @returns {Promise<Object>} 创建结果
 */
export function createAssetType(data) {
  return request.post('/AssetType', data)
}

/**
 * 更新资产类型
 * @param {number|string} id - 资产类型ID
 * @param {Object} data - 资产类型数据
 * @returns {Promise<Object>} 更新结果
 */
export function updateAssetType(id, data) {
  return request.put(`/AssetType/${id}`, data)
}

/**
 * 删除资产类型
 * @param {number|string} id - 资产类型ID
 * @returns {Promise<Object>} 删除结果
 */
export function deleteAssetType(id) {
  return request.delete(`/AssetType/${id}`)
}

/**
 * 获取资产列表
 * @param {Object} params - 查询参数
 * @returns {Promise<Object>} 资产列表
 */
export function getAssets(params) {
  console.log('调用资产列表API，参数:', params);
  
  // 确保参数格式正确
  const validParams = { ...params };
  if (validParams.pageIndex !== undefined && typeof validParams.pageIndex !== 'number') {
    validParams.pageIndex = parseInt(validParams.pageIndex) || 1;
  }
  if (validParams.pageSize !== undefined && typeof validParams.pageSize !== 'number') {
    validParams.pageSize = parseInt(validParams.pageSize) || 20;
  }
  
  // 删除空字符串参数
  Object.keys(validParams).forEach(key => {
    if (validParams[key] === '') {
      delete validParams[key];
    }
  });
  
  console.log('处理后的API参数:', validParams);
  return request.get('/Asset', { params: validParams })
    .then(response => {
      console.log('资产列表API原始响应:', response);
      return response;
    })
    .catch(error => {
      console.error('资产列表API错误:', error);
      throw error;
    });
}

/**
 * 获取资产详情
 * @param {number|string} id - 资产ID
 * @returns {Promise<Object>} 资产详情
 */
export function getAssetById(id) {
  console.log(`获取资产详情，ID: ${id}`);
  
  // 确保ID不为空
  if (!id) {
    console.error('获取资产详情失败: ID为空');
    return Promise.reject(new Error('资产ID不能为空'));
  }
  
  return request.get(`/Asset/${id}`)
    .then(response => {
      console.log('获取资产详情成功:', response);
      return response;
    })
    .catch(error => {
      console.error('获取资产详情API错误:', error);
      throw error;
    });
}

/**
 * 创建资产
 * @param {Object} data - 资产数据
 * @returns {Promise<Object>} 创建结果
 */
export function createAsset(data) {
  return request.post('/Asset', data)
}

/**
 * 更新资产
 * @param {number|string} id - 资产ID
 * @param {Object} data - 资产数据
 * @returns {Promise<Object>} 更新结果
 */
export function updateAsset(id, data) {
  return request.put(`/Asset/${id}`, data)
}

/**
 * 删除资产
 * @param {number|string} id - 资产ID
 * @returns {Promise<Object>} 删除结果
 */
export function deleteAsset(id) {
  return request.delete(`/Asset/${id}`)
}

/**
 * 变更资产位置
 * @param {number} id 资产ID
 * @param {object} data 位置变更数据
 * @param {number} data.newLocationId 新位置ID
 * @param {string} data.reason 变更原因
 * @param {string} data.notes 备注说明(可选)
 * @returns {Promise} 返回位置变更结果
 */
export function changeAssetLocation(id, data) {
  // 确保必填字段已设置
  const requestData = {
    ...data,
    reason: data.reason || '系统自动变更',
    notes: data.notes || ''
  };
  
  return request.post(`/Asset/${id}/change-location`, requestData)
}

/**
 * 获取资产历史记录
 * @param {number} id 资产ID
 * @param {Object} params 查询参数
 * @returns {Promise} 返回资产历史记录
 */
export function getAssetHistory(id, params) {
  return request.get(`/Asset/${id}/history`, { params })
}

/**
 * 获取位置资产历史记录
 * @param {number} locationId 位置ID
 * @param {Object} params 查询参数
 * @returns {Promise} 返回位置资产历史记录
 */
export function getLocationAssetHistory(locationId, params) {
  return request.get(`/Asset/location/${locationId}/history`, { params })
}

/**
 * 导出资产
 * @param {Object} query - 查询参数
 * @returns {Promise<Object>}
 */
export function exportAssets(query = {}) {
  console.log('导出资产，参数:', query);
  const token = getToken();
  const baseUrl = systemConfig.apiBaseUrl;
  
  // 构建URL参数
  const queryParams = new URLSearchParams();
  Object.keys(query).forEach(key => {
    if (query[key] !== undefined && query[key] !== '') {
      queryParams.append(key, query[key]);
    }
  });
  
  // 添加导出所有数据的参数
  queryParams.append('exportAll', 'true');
  
  const url = `${baseUrl}/Asset/export?${queryParams.toString()}`;
  console.log('导出URL:', url);
  
  // 创建下载链接
  const link = document.createElement('a');
  link.href = url;
  
  // 设置请求头
  if (token) {
    // 这里不能直接设置头部，而是通过cookie或query参数传递token
    // 添加token到URL参数
    link.href = `${url}&access_token=${encodeURIComponent(token)}`;
  }
  
  // 使用当前时间生成文件名
  const now = new Date();
  const fileName = `资产导出_${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')}.xlsx`;
  link.download = fileName;
  
  // 添加到页面并触发点击
  document.body.appendChild(link);
  link.click();
  
  // 移除元素
  document.body.removeChild(link);
  
  return Promise.resolve({
    success: true,
    message: '导出请求已发送'
  });
}

/**
 * 获取资产导入模板
 * @returns {Promise} 返回导入模板文件
 */
export function getAssetImportTemplate() {
  return request.get('/import/template', { 
    params: { entityType: 'Assets', format: 'excel' }, 
    responseType: 'blob' 
  })
}

/**
 * 导入资产数据
 * @param {FormData} formData 包含导入文件的表单数据
 * @returns {Promise} 返回导入结果
 */
export function importAssets(formData) {
  // 确保formData中包含entityType参数
  if (!formData.has('entityType')) {
    formData.append('entityType', 'Assets')
  }
  
  return request.post('/import/data', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 资产标签打印
 * @param {Array} ids - 资产ID数组
 * @returns {Promise}
 */
export function printAssetLabels(ids) {
  return request.post('/asset/print-labels', { ids })
}

/**
 * 获取资产统计数据
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getAssetStatistics(params) {
  return request.get('/asset/statistics', { params })
}

// 统一导出 assetApi 对象，兼容组件导入
export const assetApi = {
  getAssetTypes,
  createAssetType,
  updateAssetType,
  deleteAssetType,
  getAssets,
  getAssetById,
  createAsset,
  updateAsset,
  deleteAsset,
  changeAssetLocation,
  getAssetHistory,
  getLocationAssetHistory,
  exportAssets,
  getAssetImportTemplate,
  importAssets,
  printAssetLabels,
  getAssetStatistics
}

// 默认导出，兼容默认导入
export default assetApi