// IT资产管理系统 - 采购订单DTO
// 文件路径: /Application/Features/Purchase/Dtos/PurchaseOrderDto.cs
// 功能: 采购订单数据传输对象

using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace ItAssetsSystem.Application.Features.Purchase.Dtos
{
    /// <summary>
    /// 采购订单DTO
    /// </summary>
    public class PurchaseOrderDto
    {
        /// <summary>
        /// 订单ID
        /// </summary>
        public int Id { get; set; }
        
        /// <summary>
        /// 订单编号
        /// </summary>
        public string OrderCode { get; set; }
        
        /// <summary>
        /// 标题
        /// </summary>
        public string Title { get; set; }
        
        /// <summary>
        /// 描述
        /// </summary>
        public string Description { get; set; }
        
        /// <summary>
        /// 供应商ID
        /// </summary>
        public int SupplierId { get; set; }
        
        /// <summary>
        /// 供应商名称
        /// </summary>
        public string SupplierName { get; set; }
        
        /// <summary>
        /// 状态
        /// </summary>
        public int Status { get; set; }
        
        /// <summary>
        /// 状态名称
        /// </summary>
        public string StatusName { get; set; }
        
        /// <summary>
        /// 预计交付日期
        /// </summary>
        public DateTime? EstimatedDeliveryDate { get; set; }
        
        /// <summary>
        /// 实际交付日期
        /// </summary>
        public DateTime? ActualDeliveryDate { get; set; }
        
        /// <summary>
        /// 申请人ID
        /// </summary>
        public int ApplicantId { get; set; }
        
        /// <summary>
        /// 申请人姓名
        /// </summary>
        public string ApplicantName { get; set; }
        
        /// <summary>
        /// 申请时间
        /// </summary>
        public DateTime ApplicationTime { get; set; }
        
        /// <summary>
        /// 审批人ID
        /// </summary>
        public int? ApproverId { get; set; }
        
        /// <summary>
        /// 审批人姓名
        /// </summary>
        public string ApproverName { get; set; }
        
        /// <summary>
        /// 审批时间
        /// </summary>
        public DateTime? ApprovalTime { get; set; }
        
        /// <summary>
        /// 总金额
        /// </summary>
        public decimal TotalAmount { get; set; }
        
        /// <summary>
        /// 备注
        /// </summary>
        public string Notes { get; set; }
        
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }
        
        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; }

        /// <summary>
        /// 主要物料编号（第一个物品的编号）
        /// </summary>
        public string PrimaryItemCode { get; set; }

        /// <summary>
        /// 主要物料名称（第一个物品的名称）
        /// </summary>
        public string PrimaryItemName { get; set; }

        /// <summary>
        /// 物品总数量
        /// </summary>
        public int TotalQuantity { get; set; }

        /// <summary>
        /// 物品种类数
        /// </summary>
        public int ItemCount { get; set; }
    }
    
    /// <summary>
    /// 采购订单详情DTO
    /// </summary>
    public class PurchaseOrderDetailDto : PurchaseOrderDto
    {
        /// <summary>
        /// 采购物品列表
        /// </summary>
        public List<PurchaseItemDto> Items { get; set; } = new List<PurchaseItemDto>();
    }
    
    /// <summary>
    /// 采购物品DTO
    /// </summary>
    public class PurchaseItemDto
    {
        /// <summary>
        /// 物品ID
        /// </summary>
        public int Id { get; set; }
        
        /// <summary>
        /// 采购订单ID
        /// </summary>
        public int PurchaseOrderId { get; set; }
        
        /// <summary>
        /// 物品名称
        /// </summary>
        public string ItemName { get; set; }
        
        /// <summary>
        /// 物品编码
        /// </summary>
        public string ItemCode { get; set; }
        
        /// <summary>
        /// 规格
        /// </summary>
        public string Specification { get; set; }
        
        /// <summary>
        /// 资产类型ID
        /// </summary>
        public int? AssetTypeId { get; set; }
        
        /// <summary>
        /// 资产类型名称
        /// </summary>
        public string AssetTypeName { get; set; }
        
        /// <summary>
        /// 单价
        /// </summary>
        public decimal UnitPrice { get; set; }
        
        /// <summary>
        /// 数量
        /// </summary>
        public int Quantity { get; set; }
        
        /// <summary>
        /// 总价
        /// </summary>
        public decimal TotalPrice { get; set; }
        
        /// <summary>
        /// 备注
        /// </summary>
        public string Notes { get; set; }
        
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }
        
        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; }
    }
    
    /// <summary>
    /// 采购订单查询参数
    /// </summary>
    public class PurchaseOrderQuery
    {
        /// <summary>
        /// 页码
        /// </summary>
        public int PageIndex { get; set; } = 1;
        
        /// <summary>
        /// 页大小
        /// </summary>
        public int PageSize { get; set; } = 10;
        
        /// <summary>
        /// 关键字搜索
        /// </summary>
        public string Keyword { get; set; }
        
        /// <summary>
        /// 供应商ID
        /// </summary>
        public int? SupplierId { get; set; }
        
        /// <summary>
        /// 状态
        /// </summary>
        public int? Status { get; set; }
        
        /// <summary>
        /// 开始日期
        /// </summary>
        public DateTime? StartDate { get; set; }
        
        /// <summary>
        /// 结束日期
        /// </summary>
        public DateTime? EndDate { get; set; }
        
        /// <summary>
        /// 排序字段
        /// </summary>
        public string SortBy { get; set; } = "CreatedAt";
        
        /// <summary>
        /// 排序方向
        /// </summary>
        public string SortDirection { get; set; } = "desc";
    }
    
    /// <summary>
    /// 分页结果
    /// </summary>
    public class PagedResult<T>
    {
        /// <summary>
        /// 数据列表
        /// </summary>
        public List<T> Items { get; set; } = new List<T>();
        
        /// <summary>
        /// 总记录数
        /// </summary>
        public int TotalCount { get; set; }
        
        /// <summary>
        /// 页码
        /// </summary>
        public int PageIndex { get; set; }
        
        /// <summary>
        /// 页大小
        /// </summary>
        public int PageSize { get; set; }
        
        /// <summary>
        /// 总页数
        /// </summary>
        public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
    }

    /// <summary>
    /// 创建采购订单请求
    /// </summary>
    public class CreatePurchaseOrderRequest
    {
        /// <summary>
        /// 供应商ID
        /// </summary>
        [Required]
        public int SupplierId { get; set; }

        /// <summary>
        /// 申请人ID
        /// </summary>
        [Required]
        public int RequesterId { get; set; }

        /// <summary>
        /// 预计交付日期
        /// </summary>
        public DateTime? ExpectedDeliveryDate { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Notes { get; set; }

        /// <summary>
        /// 采购物品列表
        /// </summary>
        [Required]
        public List<CreatePurchaseItemRequest> Items { get; set; } = new List<CreatePurchaseItemRequest>();
    }

    /// <summary>
    /// 创建采购物品请求
    /// </summary>
    public class CreatePurchaseItemRequest
    {
        /// <summary>
        /// 物品名称
        /// </summary>
        [Required]
        public string ItemName { get; set; }

        /// <summary>
        /// 物料编号
        /// </summary>
        public string ItemCode { get; set; }

        /// <summary>
        /// 规格
        /// </summary>
        public string Specification { get; set; }

        /// <summary>
        /// 资产类型ID
        /// </summary>
        [Required]
        public int AssetTypeId { get; set; }

        /// <summary>
        /// 单价
        /// </summary>
        [Required]
        public decimal UnitPrice { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        [Required]
        public int Quantity { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Notes { get; set; }
    }

    /// <summary>
    /// 更新采购订单请求
    /// </summary>
    public class UpdatePurchaseOrderRequest
    {
        /// <summary>
        /// 供应商ID
        /// </summary>
        [Required]
        public int SupplierId { get; set; }

        /// <summary>
        /// 预计交付日期
        /// </summary>
        public DateTime? ExpectedDeliveryDate { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Notes { get; set; }
    }

    /// <summary>
    /// 到货确认请求
    /// </summary>
    public class ConfirmDeliveryRequest
    {
        /// <summary>
        /// 实际到货日期
        /// </summary>
        public DateTime? ActualDeliveryDate { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Notes { get; set; }
    }

    /// <summary>
    /// 到货处理结果
    /// </summary>
    public class PurchaseDeliveryResultDto
    {
        /// <summary>
        /// 订单ID
        /// </summary>
        public int OrderId { get; set; }

        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 消息
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// 到货日期
        /// </summary>
        public DateTime DeliveryDate { get; set; }
    }

    /// <summary>
    /// 处理到货物品请求
    /// </summary>
    public class ProcessDeliveredItemRequest
    {
        /// <summary>
        /// 采购物品ID
        /// </summary>
        [Required]
        public int PurchaseItemId { get; set; }

        /// <summary>
        /// 转为备件的数量
        /// </summary>
        public int ToSparePartQuantity { get; set; }

        /// <summary>
        /// 备件库位ID
        /// </summary>
        public long? SparePartLocationId { get; set; }

        /// <summary>
        /// 转为资产的数量
        /// </summary>
        public int ToAssetQuantity { get; set; }

        /// <summary>
        /// 资产位置ID
        /// </summary>
        public int? AssetLocationId { get; set; }
    }

    /// <summary>
    /// 采购处理结果
    /// </summary>
    public class PurchaseProcessResultDto
    {
        /// <summary>
        /// 订单ID
        /// </summary>
        public int OrderId { get; set; }

        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 消息
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// 处理的物品结果列表
        /// </summary>
        public List<ProcessedItemResult> ProcessedItems { get; set; } = new List<ProcessedItemResult>();
    }

    /// <summary>
    /// 处理物品结果
    /// </summary>
    public class ProcessedItemResult
    {
        /// <summary>
        /// 采购物品ID
        /// </summary>
        public int PurchaseItemId { get; set; }

        /// <summary>
        /// 物品名称
        /// </summary>
        public string ItemName { get; set; }

        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 消息
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// 创建的备件ID列表
        /// </summary>
        public List<long> CreatedSparePartIds { get; set; } = new List<long>();

        /// <summary>
        /// 备件数量
        /// </summary>
        public int SparePartQuantity { get; set; }

        /// <summary>
        /// 创建的资产ID列表
        /// </summary>
        public List<int> CreatedAssetIds { get; set; } = new List<int>();

        /// <summary>
        /// 资产数量
        /// </summary>
        public int AssetQuantity { get; set; }
    }

    /// <summary>
    /// 供应商DTO
    /// </summary>
    public class SupplierDto
    {
        /// <summary>
        /// 供应商ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 供应商名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 联系人
        /// </summary>
        public string ContactPerson { get; set; }

        /// <summary>
        /// 电话
        /// </summary>
        public string Phone { get; set; }

        /// <summary>
        /// 邮箱
        /// </summary>
        public string Email { get; set; }

        /// <summary>
        /// 地址
        /// </summary>
        public string Address { get; set; }
    }

    /// <summary>
    /// 资产类型DTO
    /// </summary>
    public class AssetTypeDto
    {
        /// <summary>
        /// 类型ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 类型名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 类型编码
        /// </summary>
        public string Code { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        public string Description { get; set; }
    }
}
