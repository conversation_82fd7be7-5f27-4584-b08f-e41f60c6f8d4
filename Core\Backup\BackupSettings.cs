// IT资产管理系统 - 备份设置
// 文件路径: /Core/Backup/BackupSettings.cs
// 功能: 定义备份设置

using System;

namespace ItAssetsSystem.Core.Backup
{
    /// <summary>
    /// 备份设置
    /// </summary>
    public class BackupSettings
    {
        /// <summary>
        /// 是否启用自动备份
        /// </summary>
        public bool EnableAutoBackup { get; set; } = true;
        
        /// <summary>
        /// 自动备份频率（小时）
        /// </summary>
        public int AutoBackupFrequencyHours { get; set; } = 24;
        
        /// <summary>
        /// 备份保留天数
        /// </summary>
        public int RetentionDays { get; set; } = 30;
        
        /// <summary>
        /// 最大备份数量
        /// </summary>
        public int MaxBackupCount { get; set; } = 10;
        
        /// <summary>
        /// 自动备份时间（24小时制，格式: HH:mm）
        /// </summary>
        public string AutoBackupTime { get; set; } = "02:00";
        
        /// <summary>
        /// 最近一次自动备份时间
        /// </summary>
        public DateTime? LastAutoBackupTime { get; set; }
        
        /// <summary>
        /// 最近一次手动备份时间
        /// </summary>
        public DateTime? LastManualBackupTime { get; set; }
    }
} 