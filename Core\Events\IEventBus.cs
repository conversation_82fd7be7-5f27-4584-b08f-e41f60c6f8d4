// IT资产管理系统 - 事件总线接口
// 文件路径: /Core/Events/IEventBus.cs
// 功能: 定义事件总线基本接口，包括事件的发布和订阅功能

using System;
using System.Threading.Tasks;

namespace ItAssetsSystem.Core.Events
{
    /// <summary>
    /// 事件总线接口
    /// </summary>
    public interface IEventBus
    {
        /// <summary>
        /// 发布事件
        /// </summary>
        /// <typeparam name="TEvent">事件类型</typeparam>
        /// <param name="event">事件实例</param>
        void Publish<TEvent>(TEvent @event) where TEvent : class;

        /// <summary>
        /// 异步发布事件
        /// </summary>
        /// <typeparam name="TEvent">事件类型</typeparam>
        /// <param name="event">事件实例</param>
        /// <returns>任务</returns>
        Task PublishAsync<TEvent>(TEvent @event) where TEvent : class;

        /// <summary>
        /// 订阅事件
        /// </summary>
        /// <typeparam name="TEvent">事件类型</typeparam>
        /// <param name="handler">事件处理程序</param>
        void Subscribe<TEvent>(Action<TEvent> handler) where TEvent : class;

        /// <summary>
        /// 异步订阅事件
        /// </summary>
        /// <typeparam name="TEvent">事件类型</typeparam>
        /// <param name="handler">异步事件处理程序</param>
        void SubscribeAsync<TEvent>(Func<TEvent, Task> handler) where TEvent : class;

        /// <summary>
        /// 取消订阅事件
        /// </summary>
        /// <typeparam name="TEvent">事件类型</typeparam>
        /// <param name="handler">事件处理程序</param>
        void Unsubscribe<TEvent>(Action<TEvent> handler) where TEvent : class;

        /// <summary>
        /// 取消异步订阅事件
        /// </summary>
        /// <typeparam name="TEvent">事件类型</typeparam>
        /// <param name="handler">异步事件处理程序</param>
        void UnsubscribeAsync<TEvent>(Func<TEvent, Task> handler) where TEvent : class;
    }
}

// 计划行数: 25
// 实际行数: 25 