using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ItAssetsSystem.Application.Features.Statistics.Services;
using ItAssetsSystem.Application.Common.Dtos;
using ItAssetsSystem.Models;
using ItAssetsSystem.Core.Services;
using ApiResponse = ItAssetsSystem.Models.ApiResponse;

namespace ItAssetsSystem.Api.V2.Controllers
{
    [ApiController]
    [Route("api/v2/work-summary")]
    // [Authorize] // 临时移除授权要求用于测试
    public class WorkSummaryController : ControllerBase
    {
        private readonly IWorkSummaryService _workSummaryService;
        private readonly ILogger<WorkSummaryController> _logger;

        public WorkSummaryController(
            IWorkSummaryService workSummaryService,
            ILogger<WorkSummaryController> logger)
        {
            _workSummaryService = workSummaryService;
            _logger = logger;
        }

        /// <summary>
        /// 获取工作汇总报告
        /// </summary>
        [HttpGet]
        public async Task<ActionResult<ItAssetsSystem.Models.ApiResponse<System.Collections.Generic.List<WorkSummaryDto>>>> GetWorkSummary(
            [FromQuery] string periodType = "weekly",
            [FromQuery] DateTime? periodDate = null,
            [FromQuery] int limit = 50)
        {
            try
            {
                var results = await _workSummaryService.GetWorkSummaryAsync(periodType, periodDate, limit);

                // 直接返回ApiResponse，不要再用Ok()包装
                if (results.Success)
                {
                    return Ok(ItAssetsSystem.Models.ApiResponse<System.Collections.Generic.List<WorkSummaryDto>>.CreateSuccess(results.Data, results.Message));
                }
                else
                {
                    return Ok(ItAssetsSystem.Models.ApiResponse<System.Collections.Generic.List<WorkSummaryDto>>.CreateFail(results.Message));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取工作汇总失败: PeriodType={PeriodType}, Date={Date}", periodType, periodDate);
                return Ok(ItAssetsSystem.Models.ApiResponse<System.Collections.Generic.List<WorkSummaryDto>>.CreateFail("获取工作汇总失败"));
            }
        }

        /// <summary>
        /// 获取增强排行榜
        /// </summary>
        [HttpGet("leaderboard")]
        public async Task<ActionResult<ItAssetsSystem.Models.ApiResponse<System.Collections.Generic.List<EnhancedLeaderboardDto>>>> GetEnhancedLeaderboard(
            [FromQuery] int limit = 20)
        {
            try
            {
                var results = await _workSummaryService.GetEnhancedLeaderboardAsync(limit);
                return ApiResponseFactory.CreateSuccess(results, "获取排行榜成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取排行榜失败");
                return ApiResponseFactory.CreateFail<System.Collections.Generic.List<EnhancedLeaderboardDto>>("获取排行榜失败");
            }
        }

        /// <summary>
        /// 获取任务领取统计
        /// </summary>
        [HttpGet("task-claims")]
        public async Task<ActionResult<ItAssetsSystem.Models.ApiResponse<System.Collections.Generic.List<TaskClaimStatisticsDto>>>> GetTaskClaimStatistics(
            [FromQuery] int limit = 50)
        {
            try
            {
                var results = await _workSummaryService.GetTaskClaimStatisticsAsync();
                return Ok(results);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取任务领取统计失败");
                return Ok(ItAssetsSystem.Models.ApiResponse<System.Collections.Generic.List<TaskClaimStatisticsDto>>.CreateFail("获取任务领取统计失败"));
            }
        }

        /// <summary>
        /// 手动更新工作汇总 (管理员功能)
        /// </summary>
        [HttpPost("update")]
        [AllowAnonymous] // 临时移除授权用于测试
        public async Task<ActionResult<ItAssetsSystem.Models.ApiResponse<bool>>> UpdateWorkSummary(
            [FromQuery] string periodType = "weekly",
            [FromQuery] DateTime? targetDate = null)
        {
            try
            {
                var success = await _workSummaryService.UpdateWorkSummaryAsync(periodType, targetDate);

                if (success.Success)
                {
                    return ApiResponseFactory.CreateSuccess(true, "工作汇总更新成功");
                }
                else
                {
                    return ApiResponseFactory.CreateFail<bool>("工作汇总更新失败");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新工作汇总失败: PeriodType={PeriodType}, Date={Date}", periodType, targetDate);
                return ApiResponseFactory.CreateFail<bool>("更新工作汇总失败");
            }
        }

        /// <summary>
        /// 清除缓存并刷新数据
        /// </summary>
        [HttpPost("refresh")]
        [AllowAnonymous] // 临时移除授权用于测试
        public async Task<ActionResult<ItAssetsSystem.Models.ApiResponse<object>>> RefreshWorkSummary(
            [FromQuery] string periodType = "weekly")
        {
            try
            {
                _logger.LogInformation("开始刷新工作汇总数据: {PeriodType}", periodType);

                // 先更新数据
                var updateResult = await _workSummaryService.UpdateWorkSummaryAsync(periodType);

                if (!updateResult.Success)
                {
                    _logger.LogWarning("更新工作汇总数据失败: {Message}", updateResult.Message);
                }

                // 返回最新数据
                var result = await _workSummaryService.GetWorkSummaryAsync(periodType, DateTime.Today, 100);

                _logger.LogInformation("工作汇总数据刷新完成: {Count}条记录", result.Data?.Count ?? 0);

                return ApiResponseFactory.CreateSuccess<object>(new {
                    message = "数据已刷新",
                    count = result.Data?.Count ?? 0,
                    data = result.Data,
                    periodType = periodType,
                    refreshTime = DateTime.Now
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "刷新工作汇总数据失败: PeriodType={PeriodType}", periodType);
                return ApiResponseFactory.CreateFail<object>("刷新失败: " + ex.Message);
            }
        }

        /// <summary>
        /// 手动更新任务领取统计 (管理员功能)
        /// </summary>
        [HttpPost("update-task-claims")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<ItAssetsSystem.Models.ApiResponse<bool>>> UpdateTaskClaimStatistics()
        {
            try
            {
                var success = await _workSummaryService.UpdateTaskClaimStatisticsAsync();

                if (success.Success)
                {
                    return ApiResponseFactory.CreateSuccess(true, "任务领取统计更新成功");
                }
                else
                {
                    return ApiResponseFactory.CreateFail<bool>("任务领取统计更新失败");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新任务领取统计失败");
                return ApiResponseFactory.CreateFail<bool>("更新任务领取统计失败");
            }
        }

        /// <summary>
        /// 获取统计概览
        /// </summary>
        [HttpGet("overview")]
        public async Task<ActionResult<ItAssetsSystem.Models.ApiResponse<WorkSummaryOverviewDto>>> GetOverview()
        {
            try
            {
                // 获取本周汇总
                var weeklyDataResponse = await _workSummaryService.GetWorkSummaryAsync("weekly", DateTime.Today, 10);
                var weeklyData = weeklyDataResponse.Success ? weeklyDataResponse.Data : new List<WorkSummaryDto>();

                // 获取排行榜
                var leaderboard = await _workSummaryService.GetEnhancedLeaderboardAsync(5);

                // 转换 WorkSummaryDto 到 UserWorkSummaryDto
                var weeklyTopPerformers = weeklyData?.Select(w => new UserWorkSummaryDto
                {
                    UserId = w.UserId,
                    UserName = w.UserName,
                    DepartmentName = w.DepartmentName,
                    PeriodType = w.PeriodType,
                    PeriodDate = w.PeriodDate,
                    TasksCreated = w.TasksCreated,
                    TasksClaimed = w.TasksClaimed,
                    TasksCompleted = w.TasksCompleted,
                    TasksCommented = w.TasksCommented,
                    TasksTotal = w.TasksTotal,
                    AssetsCreated = w.AssetsCreated,
                    AssetsUpdated = w.AssetsUpdated,
                    AssetsDeleted = w.AssetsDeleted,
                    AssetsTotal = w.AssetsTotal,
                    FaultsReported = w.FaultsReported,
                    FaultsRepaired = w.FaultsRepaired,
                    FaultsTotal = w.FaultsTotal,
                    ProcurementsCreated = w.ProcurementsCreated,
                    ProcurementsUpdated = w.ProcurementsUpdated,
                    ProcurementsTotal = w.ProcurementsTotal,
                    PartsIn = w.PartsIn,
                    PartsOut = w.PartsOut,
                    PartsAdded = w.PartsAdded,
                    PartsTotal = w.PartsTotal,
                    TotalPointsEarned = w.TotalPointsEarned,
                    TotalCoinsEarned = w.TotalCoinsEarned,
                    TotalDiamondsEarned = w.TotalDiamondsEarned,
                    TotalXpEarned = w.TotalXpEarned,
                    PointsRank = w.PointsRank,
                    ProductivityRank = w.ProductivityRank,
                    ProductivityScore = w.ProductivityScore,
                    Evaluation = w.Evaluation
                }).ToList() ?? new List<UserWorkSummaryDto>();

                var overview = new WorkSummaryOverviewDto
                {
                    WeeklyTopPerformers = weeklyTopPerformers,
                    CurrentLeaderboard = leaderboard,
                    LastUpdated = DateTime.Now,
                    TotalActiveUsers = weeklyTopPerformers.Count
                };

                return ApiResponseFactory.CreateSuccess(overview, "获取统计概览成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取统计概览失败");
                return ApiResponseFactory.CreateFail<WorkSummaryOverviewDto>("获取统计概览失败");
            }
        }
    }

    /// <summary>
    /// 工作汇总概览DTO
    /// </summary>
    public class WorkSummaryOverviewDto
    {
        public System.Collections.Generic.List<UserWorkSummaryDto> WeeklyTopPerformers { get; set; }
        public System.Collections.Generic.List<EnhancedLeaderboardDto> CurrentLeaderboard { get; set; }
        public DateTime LastUpdated { get; set; }
        public int TotalActiveUsers { get; set; }
    }
}
