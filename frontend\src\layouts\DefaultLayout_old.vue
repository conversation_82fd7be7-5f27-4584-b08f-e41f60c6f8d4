/**
 * 航空航天级IT资产管理系统 - 默认布局组件 (旧备份)
 * 文件路径: src/layouts/DefaultLayout_old.vue
 * 功能描述: 系统的主布局结构，包含顶部导航、侧边栏菜单和内容区域
 */

<template>
  <div class="app-container">
    <!-- 顶部导航栏 -->
    <header class="app-header">
      <div class="logo-container">
        <el-icon :size="24" color="#409EFF"><Monitor /></el-icon>
        <h1 class="title">{{ systemConfig.name }}</h1>
      </div>
      <div class="header-right">
        <!-- 主题切换下拉菜单 -->
        <el-dropdown @command="handleThemeChange" class="theme-dropdown">
          <span class="theme-selector">
            <el-icon v-if="themeStore.currentTheme.icon === 'Setting'"><Setting /></el-icon>
            <el-icon v-else-if="themeStore.currentTheme.icon === 'Ship'"><Ship /></el-icon>
            <el-icon v-else-if="themeStore.currentTheme.icon === 'Star'"><Star /></el-icon>
            <span class="theme-name">{{ themeStore.currentTheme.name }}</span>
            <el-icon><arrow-down /></el-icon>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item v-for="theme in themeStore.availableThemes" 
                                :key="theme.id" 
                                :command="theme.id"
                                :class="{ 'active-theme': themeStore.currentTheme.id === theme.id }">
                <el-icon v-if="theme.icon === 'Setting'"><Setting /></el-icon>
                <el-icon v-else-if="theme.icon === 'Ship'"><Ship /></el-icon>
                <el-icon v-else-if="theme.icon === 'Star'"><Star /></el-icon>
                {{ theme.name }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>

        <el-dropdown @command="handleCommand">
          <span class="user-info">
            <el-avatar :size="32" :src="userStore.computedAvatarUrl" />
            <span class="username">{{ userStore.userInfo?.name || '用户' }}</span>
            <el-icon><arrow-down /></el-icon>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="profile">个人信息</el-dropdown-item>
              <el-dropdown-item command="password">修改密码</el-dropdown-item>
              <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </header>
    
    <!-- 主内容区 -->
    <div class="app-main">
      <!-- 侧边菜单 -->
      <aside class="app-sidebar" :class="{ collapsed: isCollapsed }">
        <el-scrollbar>
          <el-menu
            :default-active="activeMenu"
            :collapse="isCollapsed"
            :unique-opened="true"
            background-color="#304156"
            text-color="#bfcbd9"
            active-text-color="#409EFF"
            router
          >
            <!-- 仪表盘菜单项（所有用户可见） -->
            <el-menu-item index="/main/dashboard">
              <el-icon><Odometer /></el-icon>
              <template #title>仪表盘</template>
            </el-menu-item>
            
            <!-- 资产管理菜单项 -->
            <el-sub-menu index="/main/asset">
              <template #title>
                <el-icon><Monitor /></el-icon>
                <span>资产管理</span>
              </template>
              <el-menu-item index="/main/asset/list">
                <el-icon><List /></el-icon>
                <span>资产列表</span>
              </el-menu-item>
              <el-menu-item index="/main/asset/type">
                <el-icon><Setting /></el-icon>
                <span>资产类型</span>
              </el-menu-item>
            </el-sub-menu>
            
            <!-- 新增功能测试菜单 -->
            <el-sub-menu index="/test">
              <template #title>
                <el-icon><Operation /></el-icon>
                <span>功能测试</span>
              </template>
              <el-menu-item index="/test/export">
                <el-icon><Download /></el-icon>
                <span>导出测试</span>
              </el-menu-item>
            </el-sub-menu>
            
            <!-- 位置管理菜单组 -->
            <el-sub-menu index="/main/locations">
              <template #title>
                <el-icon><LocationInformation /></el-icon>
                <span>位置管理</span>
              </template>
              <el-menu-item index="/main/locations/structure">位置结构</el-menu-item>
              <el-menu-item index="/main/locations/relations">位置关联</el-menu-item>
            </el-sub-menu>
            
            <!-- 故障管理菜单组 -->
            <el-sub-menu index="/main/faults">
              <template #title>
                <el-icon><Warning /></el-icon>
                <span>故障管理</span>
              </template>
              <el-menu-item index="/main/faults/list">故障列表</el-menu-item>
              <el-menu-item index="/main/faults/maintenance">返厂维修</el-menu-item>
            </el-sub-menu>
            
            <!-- 采购管理菜单组 -->
            <el-sub-menu index="/main/purchases">
              <template #title>
                <el-icon><ShoppingCart /></el-icon>
                <span>采购管理</span>
              </template>
              <el-menu-item index="/main/purchases/list">采购列表</el-menu-item>
            </el-sub-menu>
            
            <!-- 任务管理菜单组 -->
            <el-sub-menu index="/main/tasks">
              <template #title>
                <el-icon><List /></el-icon>
                <span>任务管理</span>
              </template>
              <el-menu-item index="/main/tasks/list">任务列表</el-menu-item>
              <el-menu-item index="/main/tasks/board">任务看板</el-menu-item>
              <el-menu-item index="/main/tasks/periodic">周期任务</el-menu-item>
            </el-sub-menu>
            
            <!-- 系统管理菜单组 -->
            <el-sub-menu index="/main/system">
              <template #title>
                <el-icon><Setting /></el-icon>
                <span>系统管理</span>
              </template>
              <el-menu-item index="/main/system/users">用户管理</el-menu-item>
              <el-menu-item index="/main/system/roles">角色管理</el-menu-item>
              <el-menu-item index="/main/system/menus">菜单管理</el-menu-item>
              <el-menu-item index="/main/system/departments">部门管理</el-menu-item>
              <el-menu-item index="/main/system/personnel">人员管理</el-menu-item>
              <el-menu-item index="/main/system/logs">审计日志</el-menu-item>
            </el-sub-menu>
          </el-menu>
        </el-scrollbar>
        
        <!-- 侧边栏收缩按钮 -->
        <div class="sidebar-toggle" @click="toggleSidebar">
          <el-icon v-if="isCollapsed"><Expand /></el-icon>
          <el-icon v-else><Fold /></el-icon>
        </div>
      </aside>
      
      <!-- 内容区域 -->
      <main class="app-content">
        <!-- 页面标题 -->
        <div class="content-header">
          <breadcrumb />
        </div>
        
        <!-- 路由视图 -->
        <div class="content-body">
          <router-view v-slot="{ Component }">
            <transition name="fade-transform" mode="out-in">
              <keep-alive :include="cachedViews">
                <component :is="Component" />
              </keep-alive>
            </transition>
          </router-view>
        </div>
      </main>
    </div>
    
    <!-- 退出登录确认对话框 -->
    <el-dialog
      title="退出确认"
      v-model="userStore.isLogoutDialogVisible"
      width="380px"
    >
      <span>确定要退出登录吗？</span>
      <template #footer>
        <el-button @click="userStore.setLogoutDialogVisible(false)">取消</el-button>
        <el-button type="primary" @click="confirmLogout">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useUserStore } from '@/stores/modules/user'
import { useThemeStore } from '@/stores/modules/theme'
import { ElMessageBox } from 'element-plus'
import Breadcrumb from '@/components/Breadcrumb.vue'
import systemConfig from '@/config/system'
import { 
  Odometer, 
  Monitor, 
  LocationInformation, 
  Warning, 
  ShoppingCart, 
  List, 
  Setting, 
  Expand, 
  Fold, 
  ArrowDown, 
  Operation, 
  Download,
  Ship,
  Star
} from '@element-plus/icons-vue'

// 路由和状态
const router = useRouter()
const route = useRoute()
const userStore = useUserStore()
const themeStore = useThemeStore()

// 侧边栏折叠状态
const isCollapsed = ref(false)

// 缓存的视图组件
const cachedViews = ref(['Dashboard', 'AssetList', 'LocationTree', 'FaultList', 'TaskList'])

// 当前激活的菜单项
const activeMenu = computed(() => {
  // 获取当前路由路径或meta中的activeMenu
  return route.meta.activeMenu || route.path
})

// 检查是否具有指定权限
const hasPermission = (requiredRoles) => {
  // 如果没有设置所需角色，则默认所有人可见
  if (!requiredRoles || requiredRoles.length === 0) {
    return true
  }
  
  // 检查用户角色是否包含所需角色之一
  const userRoles = userStore.roles || []
  return userRoles.some(role => requiredRoles.includes(role))
}

// 切换侧边栏折叠状态
const toggleSidebar = () => {
  isCollapsed.value = !isCollapsed.value
}

// 用户下拉菜单命令处理
const handleCommand = (command) => {
  if (command === 'logout') {
    userStore.setLogoutDialogVisible(true)
  } else if (command === 'profile') {
    router.push('/main/user/profile')
  } else if (command === 'password') {
    // TODO: 实现修改密码逻辑
    console.log('跳转到修改密码页')
  }
}

// 主题切换处理
const handleThemeChange = (themeId) => {
  themeStore.setTheme(themeId)
}

// 确认退出登录
const confirmLogout = async () => {
  try {
    await userStore.logout()
    userStore.setLogoutDialogVisible(false)
    router.push('/login')
  } catch (error) {
    ElMessageBox.alert(`退出登录失败: ${error.message}`, '错误', { type: 'error' })
  }
}

// 组件挂载后
onMounted(() => {
  // 初始化时，如果用户信息不存在，尝试获取
  if (!userStore.userInfo?.id) {
    userStore.fetchUserInfo()
  }
  
  // 加载用户选择的主题
  themeStore.loadTheme()
})
</script>

<style lang="scss" scoped>
@use "@/styles/variables.scss" as *;

.app-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
}

.app-header {
  height: $header-height;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 $content-padding;
  background-color: #fff; // 可以考虑使用 CSS 变量
  border-bottom: 1px solid var(--el-border-color-light);
  flex-shrink: 0;

  .logo-container {
    display: flex;
    align-items: center;
    .title {
      font-size: 1.4rem;
      font-weight: 600;
      margin-left: 10px;
      color: $text-color-primary; // 使用 CSS 变量
    }
  }

  .header-right {
    display: flex;
    align-items: center;

    .theme-dropdown {
      margin-right: 20px;
      cursor: pointer;
      .theme-selector {
        display: flex;
        align-items: center;
        color: $text-color-regular; // 使用 CSS 变量
        .theme-name {
          margin: 0 5px;
        }
        .el-icon {
          font-size: 16px;
        }
      }
    }
    
    .user-info {
      display: flex;
      align-items: center;
      cursor: pointer;

      .username {
        margin-left: 8px;
        color: $text-color-regular; // 使用 CSS 变量
      }
      .el-icon {
        margin-left: 5px;
        color: $text-color-secondary; // 使用 CSS 变量
      }
    }
  }
}

// 主题下拉菜单激活项样式
.el-dropdown-menu__item.active-theme {
  color: var(--el-color-primary);
  font-weight: bold;
}

.app-main {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.app-sidebar {
  width: $sidebar-width;
  background-color: #304156; // 侧边栏背景色，可改为 CSS 变量
  transition: width $transition-duration ease-in-out;
  position: relative;
  flex-shrink: 0;

  &.collapsed {
    width: $sidebar-collapsed-width;
    .el-menu--collapse {
      width: 100%;
    }
    .sidebar-toggle {
      justify-content: center;
    }
  }

  .el-scrollbar {
    height: calc(100% - 40px); // 减去底部 toggle 的高度
  }

  .el-menu {
    border-right: none;
    &:not(.el-menu--collapse) {
      width: $sidebar-width;
    }
  }

  .el-menu-item, .el-sub-menu__title {
    height: 50px;
    line-height: 50px;
  }

  .sidebar-toggle {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 40px;
    background-color: #263445; // 可改为 CSS 变量
    color: #bfcbd9;
    display: flex;
    align-items: center;
    justify-content: flex-end; // 默认靠右
    padding: 0 20px; // 默认内边距
    cursor: pointer;
    transition: background-color $transition-duration ease-in-out;

    &:hover {
      background-color: #1e2a38; // 可改为 CSS 变量
    }
    
    .el-icon {
      font-size: 20px;
    }
  }
}

.app-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: $background-color-base; // 页面背景色

  .content-header {
    padding: 10px $content-padding;
    background-color: #fff; // 面包屑背景色
    border-bottom: 1px solid var(--el-border-color-light);
    flex-shrink: 0;
  }

  .content-body {
    flex: 1;
    padding: $content-padding;
    overflow-y: auto;
    overflow-x: hidden;
  }
}
</style>