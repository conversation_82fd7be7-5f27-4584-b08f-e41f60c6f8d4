/**
 * 403错误页面
 * 文件路径: src/views/error/403.vue
 * 功能描述: 权限不足403页面
 */

<template>
  <div class="error-page">
    <div class="error-container">
      <div class="error-image">
        <el-icon size="160" color="#F56C6C"><WarningFilled /></el-icon>
      </div>
      <h1 class="error-title">403</h1>
      <p class="error-message">抱歉，您没有权限访问此页面</p>
      <div class="error-actions">
        <el-button type="primary" @click="goBack">返回上一页</el-button>
        <el-button @click="goHome">返回首页</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { WarningFilled } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'

const router = useRouter()

const goBack = () => {
  router.go(-1)
}

const goHome = () => {
  router.push('/')
}
</script>

<style lang="scss" scoped>
.error-page {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #f5f7fa;
  
  .error-container {
    text-align: center;
    padding: 40px;
    max-width: 500px;
    
    .error-image {
      margin-bottom: 30px;
    }
    
    .error-title {
      font-size: 72px;
      color: #F56C6C;
      margin: 0 0 20px;
      line-height: 1;
    }
    
    .error-message {
      font-size: 20px;
      color: #606266;
      margin-bottom: 30px;
    }
    
    .error-actions {
      display: flex;
      justify-content: center;
      gap: 16px;
    }
  }
}

@media (max-width: 576px) {
  .error-page {
    .error-container {
      padding: 20px;
      
      .error-title {
        font-size: 60px;
      }
      
      .error-message {
        font-size: 16px;
      }
    }
  }
}
</style> 