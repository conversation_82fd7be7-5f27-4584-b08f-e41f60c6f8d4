/**
 * 航空航天级IT资产管理系统 - 认证工具
 * 文件路径: src/utils/auth.js
 * 功能描述: 处理用户认证相关的功能，包括令牌管理和权限校验
 */

// 令牌相关常量
const TOKEN_KEY = 'it_asset_token'
const REFRESH_TOKEN_KEY = 'refreshToken'
const TOKEN_EXPIRATION_KEY = 'tokenExpiration'
const USER_INFO_KEY = 'userInfo'

/**
 * 存储认证令牌
 * @param {string} token - JWT token
 * @param {string} refreshToken - 刷新token，可选
 * @param {number} expiresIn - token过期时间（秒），可选
 */
export function setToken(token, refreshToken = '', expiresIn = null) {
  console.log('【Auth】存储token:', token ? token.substring(0, 10) + '...' : 'null')
  
  if (!token) {
    console.warn('【Auth】警告: 尝试存储空token')
    return false
  }
  
  try {
    localStorage.setItem(TOKEN_KEY, token)
    
    // 如果提供了刷新token，也存储它
    if (refreshToken) {
      localStorage.setItem(REFRESH_TOKEN_KEY, refreshToken)
    }
    
    // 如果提供了过期时间，计算并存储过期时间戳
    if (expiresIn) {
      const expirationTime = Date.now() + expiresIn * 1000
      localStorage.setItem(TOKEN_EXPIRATION_KEY, expirationTime.toString())
    }
    
    console.log('【Auth】token存储成功')
    return true
  } catch (error) {
    console.error('【Auth】存储token失败:', error)
    return false
  }
}

/**
 * 获取访问令牌
 * @returns {string} token
 */
export function getToken() {
  return localStorage.getItem(TOKEN_KEY)
}

/**
 * 获取刷新令牌
 * @returns {string} refreshToken
 */
export function getRefreshToken() {
  return localStorage.getItem(REFRESH_TOKEN_KEY)
}

/**
 * 检查令牌是否已过期
 * @returns {boolean} 是否已过期
 */
export function isTokenExpired() {
  const token = getToken()
  if (!token) return true
  
  const decoded = parseToken(token)
  if (!decoded || !decoded.exp) return true
  
  // exp是Unix时间戳（秒）
  const expireTime = decoded.exp * 1000
  return Date.now() >= expireTime
}

/**
 * 清除所有认证信息
 */
export function clearToken() {
  try {
    localStorage.removeItem(TOKEN_KEY)
    localStorage.removeItem(REFRESH_TOKEN_KEY)
    localStorage.removeItem(TOKEN_EXPIRATION_KEY)
    localStorage.removeItem(USER_INFO_KEY)
    console.log('【Auth】已清除所有认证数据')
    return true
  } catch (error) {
    console.error('【Auth】清除认证数据失败:', error)
    return false
  }
}

/**
 * 保存用户信息到本地存储
 * @param {Object} userInfo - 用户信息对象
 */
export function setUserInfo(userInfo) {
  if (!userInfo) {
    console.warn('【Auth】警告: 尝试存储空用户信息')
    return false
  }
  
  try {
    localStorage.setItem(USER_INFO_KEY, JSON.stringify(userInfo))
    console.log('【Auth】用户信息存储成功')
    return true
  } catch (error) {
    console.error('【Auth】存储用户信息失败:', error)
    return false
  }
}

/**
 * 获取用户信息
 * @returns {Object|null} 用户信息对象
 */
export function getUserInfo() {
  try {
    const userInfoStr = localStorage.getItem(USER_INFO_KEY)
    if (!userInfoStr) return null
    
    return JSON.parse(userInfoStr)
  } catch (error) {
    console.error('【Auth】获取用户信息失败:', error)
    return null
  }
}

/**
 * 检查用户是否有特定权限
 * @param {string|string[]} permission - 权限标识或权限标识数组
 * @returns {boolean} 是否有权限
 */
export function hasPermission(permission) {
  // 如果没有指定权限，则默认允许
  if (!permission) {
    return true
  }
  
  try {
    const userInfo = getUserInfo()
    
    // 如果用户信息不存在，则无权限
    if (!userInfo) {
      return false
    }
    
    // 如果用户是管理员，则拥有所有权限
    if (userInfo.roles && userInfo.roles.includes('admin')) {
      return true
    }
    
    // 检查用户是否有指定权限
    const permissions = userInfo.permissions || []
    const hasAccess = permissions.includes(permission)
    
    console.log('权限检查', { 
      permission, 
      userPermissions: permissions,
      hasAccess
    })
    
    return hasAccess
  } catch (error) {
    console.error('检查权限失败', error)
    return false
  }
}

/**
 * 检查用户是否有特定角色
 * @param {string|string[]} role - 角色标识或角色标识数组
 * @returns {boolean} 是否有角色
 */
export function hasRole(role) {
  // 如果没有指定角色，则默认允许
  if (!role) {
    return true
  }
  
  try {
    const userInfo = getUserInfo()
    
    // 如果用户信息不存在，则无权限
    if (!userInfo) {
      return false
    }
    
    // 检查用户是否有指定角色
    const roles = userInfo.roles || []
    const hasRole = roles.includes(role)
    
    console.log('角色检查', { 
      role, 
      userRoles: roles,
      hasRole
    })
    
    return hasRole
  } catch (error) {
    console.error('检查角色失败', error)
    return false
  }
}

/**
 * 解析JWT Token
 * @param {string} token JWT Token
 * @returns {Object|null} 解析后的Token内容
 */
export function parseToken(token) {
  if (!token) return null
  
  try {
    const base64Url = token.split('.')[1]
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/')
    const jsonPayload = decodeURIComponent(
      atob(base64)
        .split('')
        .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
        .join('')
    )
    
    return JSON.parse(jsonPayload)
  } catch (error) {
    console.error('解析Token失败:', error)
    return null
  }
}

/**
 * 移除Token
 */
export function removeToken() {
  return localStorage.removeItem(TOKEN_KEY)
} 