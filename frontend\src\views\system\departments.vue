/**
 * 航空航天级IT资产管理系统 - 部门管理页面
 * 文件路径: src/views/system/departments.vue
 * 功能描述: 系统部门的增删改查管理功能
 */

<template>
  <div class="department-management-container">
    <!-- 页面标题和操作按钮 -->
    <div class="page-header">
      <h2 class="page-title">部门管理</h2>
      <div class="page-actions">
        <el-button type="primary" @click="handleCreateDepartment" :icon="Plus">
          新建部门
        </el-button>
        <el-button type="success" @click="handleExpandAll" :icon="ZoomIn">
          展开所有
        </el-button>
        <el-button type="info" @click="handleCollapseAll" :icon="ZoomOut">
          折叠所有
        </el-button>
      </div>
    </div>

    <!-- 部门树表格 -->
    <el-card class="data-card">
      <el-table
        ref="departmentTable"
        v-loading="loading"
        :data="departmentList"
        row-key="id"
        border
        default-expand-all
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      >
        <el-table-column prop="name" label="部门名称" min-width="180" />
        <el-table-column prop="code" label="部门编码" width="150" />
        <el-table-column label="部门经理" width="120">
          <template #default="scope">
            <span>{{ getPersonnelName(scope.row.managerId) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="部门主任" width="120">
          <template #default="scope">
            <span>{{ getPersonnelName(scope.row.deputyManagerId) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="phone" label="联系电话" width="150" />
        <el-table-column prop="email" label="邮箱" min-width="200" show-overflow-tooltip />
        <el-table-column prop="userCount" label="用户数" width="100" align="center" />
        <el-table-column prop="sort" label="排序" width="80" align="center" />
        <el-table-column prop="status" label="状态" width="100" align="center">
          <template #default="scope">
            <el-tag v-if="scope.row.status === 'active'" type="success">正常</el-tag>
            <el-tag v-else-if="scope.row.status === 'disabled'" type="info">禁用</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="250" fixed="right">
          <template #default="scope">
            <el-button 
              type="text" 
              size="small" 
              @click="handleAddChild(scope.row)"
              :icon="Plus"
            >
              添加下级
            </el-button>
            <el-button 
              type="text" 
              size="small" 
              @click="handleEdit(scope.row)"
              :icon="Edit"
            >
              编辑
            </el-button>
            <el-button 
              type="text" 
              size="small" 
              @click="handleDelete(scope.row)"
              :icon="Delete"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 部门对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="50%"
      :before-close="handleCloseDialog"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
      >
        <el-form-item label="部门名称" prop="name">
          <el-input v-model="formData.name" />
        </el-form-item>
        <el-form-item label="部门编码" prop="code">
          <el-input v-model="formData.code" />
        </el-form-item>
        <el-form-item label="部门经理" prop="managerId">
          <el-select 
            v-model="formData.managerId" 
            filterable 
            clearable 
            placeholder="请选择部门经理"
            :loading="personnelLoading"
          >
            <el-option 
              v-for="item in personnelOptions" 
              :key="item.id" 
              :label="item.name" 
              :value="item.id"
            >
              <div class="personnel-option">
                <span>{{ item.name }}</span>
                <span class="personnel-code">({{ item.employeeCode }})</span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="部门主任" prop="deputyManagerId">
          <el-select 
            v-model="formData.deputyManagerId" 
            filterable 
            clearable 
            placeholder="请选择部门主任"
            :loading="personnelLoading"
          >
            <el-option 
              v-for="item in personnelOptions" 
              :key="item.id" 
              :label="item.name" 
              :value="item.id"
            >
              <div class="personnel-option">
                <span>{{ item.name }}</span>
                <span class="personnel-code">({{ item.employeeCode }})</span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="联系电话" prop="phone">
          <el-input v-model="formData.phone" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="formData.email" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="formData.description" type="textarea" />
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input-number v-model="formData.sort" :min="0" :max="100" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="formData.status" placeholder="请选择状态">
            <el-option label="正常" value="active" />
            <el-option label="禁用" value="disabled" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="handleCloseDialog">取消</el-button>
        <el-button type="primary" :loading="formLoading" @click="submitForm">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Plus, Edit, Delete, ZoomIn, ZoomOut
} from '@element-plus/icons-vue'
import departmentApi from '@/api/department'
import personnelApi from '@/api/personnel'

// 数据加载状态
const loading = ref(false)
const personnelLoading = ref(false)

// 部门表格引用
const departmentTable = ref(null)

// 部门列表数据
const departmentList = ref([])
// 人员选项列表
const personnelOptions = ref([])

// 部门对话框相关
const dialogVisible = ref(false)
const dialogTitle = ref('')
const formLoading = ref(false)
const formData = ref({
  id: undefined,
  name: '',
  code: '',
  parentId: null,
  managerId: null,
  deputyManagerId: null,
  phone: '',
  email: '',
  description: '',
  sort: 0,
  status: 'active'
})

const formRules = ref({
  name: [{ required: true, message: '部门名称不能为空', trigger: 'blur' }],
  code: [{ required: true, message: '部门编码不能为空', trigger: 'blur' }]
})

const formRef = ref(null)

// 生命周期钩子
onMounted(() => {
  fetchDepartmentList()
  fetchPersonnelOptions()
})

// 获取部门列表
const fetchDepartmentList = async () => {
  loading.value = true
  try {
    const response = await departmentApi.getDepartmentList()
    if (response.data && response.data.success) {
      // 转换扁平数据为树形结构
      departmentList.value = convertToTree(response.data.data)
      console.log('获取部门数据成功:', departmentList.value)
    } else {
      console.error('获取部门数据失败:', response.data?.message || '未知错误')
      ElMessage.error('获取部门数据失败')
    }
  } catch (error) {
    console.error('获取部门列表出错:', error)
    ElMessage.error('获取部门列表失败: ' + (error.message || '服务器错误'))
  } finally {
    loading.value = false
  }
}

// 获取人员列表作为下拉选项
const fetchPersonnelOptions = async () => {
  personnelLoading.value = true
  try {
    // 确保不传入departmentId参数，这样可以获取所有部门的人员
    const response = await personnelApi.getPersonnelList({
      // 不添加任何过滤条件，获取所有人员
    })
    
    if (response.data && response.data.success) {
      personnelOptions.value = response.data.data
      console.log('获取人员数据成功，共获取到', personnelOptions.value.length, '条记录')
      console.log('当前可选人员列表:', personnelOptions.value.map(p => ({id: p.id, name: p.name, dept: p.departmentName})))
    } else {
      console.error('获取人员数据失败:', response.data?.message || '未知错误')
      ElMessage.error('获取人员数据失败')
    }
  } catch (error) {
    console.error('获取人员列表出错:', error)
    ElMessage.error('获取人员数据失败: ' + (error.message || '服务器错误'))
  } finally {
    personnelLoading.value = false
  }
  // 返回promise以支持链式调用
  return Promise.resolve()
}

// 将扁平部门列表转换为树形结构
const convertToTree = (items) => {
  if (!items || !Array.isArray(items)) {
    console.error('部门数据格式错误:', items)
    return []
  }
  
  const result = []
  const itemMap = {}
  
  // 创建一个以id为键的映射
  items.forEach(item => {
    // 复制原始对象，并添加children数组
    itemMap[item.id] = {
      ...item,
      // 确保ID字段是正确的类型
      id: Number(item.id),
      parentId: item.parentId ? Number(item.parentId) : null,
      managerId: item.managerId ? Number(item.managerId) : null,
      // 处理后端可能没有返回deputyManagerId字段的情况
      deputyManagerId: item.deputyManagerId !== undefined ? Number(item.deputyManagerId) : null,
      // 如果后端没有提供manager,phone,email,userCount等字段，添加默认值
      manager: item.manager || '',
      phone: item.phone || '',
      email: item.email || '',
      userCount: item.userCount || 0,
      sort: item.sort || 0,
      status: item.isActive ? 'active' : 'disabled',
      children: []
    }
  })
  
  // 建立父子关系
  items.forEach(item => {
    if (item.parentId) {
      const parent = itemMap[item.parentId]
      if (parent) {
        parent.children.push(itemMap[item.id])
      } else {
        // 如果找不到父节点，作为顶级节点
        result.push(itemMap[item.id])
      }
    } else {
      // 顶级部门
      result.push(itemMap[item.id])
    }
  })
  
  return result
}

// 展开所有节点
const handleExpandAll = () => {
  departmentTable.value.expandAllRows()
}

// 折叠所有节点
const handleCollapseAll = () => {
  departmentTable.value.collapseAllRows()
}

// 删除部门
const handleDelete = (row) => {
  // 检查是否有子部门
  if (row.children && row.children.length > 0) {
    ElMessage.warning('该部门下有子部门，请先删除子部门')
    return
  }
  
  // 检查部门是否有用户
  if (row.userCount > 0) {
    ElMessage.warning('该部门下有用户，不能直接删除')
    return
  }
  
  ElMessageBox.confirm(`确定要删除部门"${row.name}"吗？`, '删除部门', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'error'
  }).then(async () => {
    try {
      loading.value = true
      const response = await departmentApi.deleteDepartment(row.id)
      
      if (response.data && response.data.success) {
    ElMessage.success('部门已删除')
    // 刷新列表
    fetchDepartmentList()
      } else {
        ElMessage.error(response.data?.message || '删除部门失败')
      }
    } catch (error) {
      console.error('删除部门出错:', error)
      ElMessage.error('删除部门失败: ' + (error.message || '服务器错误'))
    } finally {
      loading.value = false
    }
  }).catch(() => {
    // 取消操作
  })
}

// 创建部门
const handleCreateDepartment = () => {
  dialogTitle.value = '新建部门'
  
  // 先设置初始数据并打开对话框
  formData.value = {
    id: undefined,
    name: '',
    code: '',
    parentId: null,
    managerId: null,
    deputyManagerId: null,
    phone: '',
    email: '',
    description: '',
    sort: 0,
    status: 'active'
  }
  
  dialogVisible.value = true
  
  // 再加载人员列表
  personnelLoading.value = true
  personnelApi.getPersonnelList().then(response => {
    if (response.data && response.data.success) {
      personnelOptions.value = response.data.data
      console.log('获取人员数据成功，共获取到', personnelOptions.value.length, '条记录')
    } else {
      ElMessage.error('获取人员数据失败')
    }
    personnelLoading.value = false
  }).catch(error => {
    console.error('获取人员列表失败:', error)
    ElMessage.error('获取人员数据失败: ' + (error.message || '服务器错误'))
    personnelLoading.value = false
  })
  
  // 清除表单验证
  nextTick(() => {
    formRef.value && formRef.value.clearValidate()
  })
}

// 添加子部门
const handleAddChild = (row) => {
  dialogTitle.value = `添加"${row.name}"的子部门`
  
  // 先设置初始数据并打开对话框
  formData.value = {
    id: undefined,
    name: '',
    code: '',
    parentId: row.id,
    managerId: null,
    deputyManagerId: null,
    phone: '',
    email: '',
    description: '',
    sort: 0,
    status: 'active'
  }
  
  dialogVisible.value = true
  
  // 再加载人员列表
  personnelLoading.value = true
  personnelApi.getPersonnelList().then(response => {
    if (response.data && response.data.success) {
      personnelOptions.value = response.data.data
      console.log('获取人员数据成功，共获取到', personnelOptions.value.length, '条记录')
    } else {
      ElMessage.error('获取人员数据失败')
    }
    personnelLoading.value = false
  }).catch(error => {
    console.error('获取人员列表失败:', error)
    ElMessage.error('获取人员数据失败: ' + (error.message || '服务器错误'))
    personnelLoading.value = false
  })
  
  // 清除表单验证
  nextTick(() => {
    formRef.value && formRef.value.clearValidate()
  })
}

// 编辑部门
const handleEdit = (row) => {
  dialogTitle.value = `编辑部门"${row.name}"`
  
  // 打开对话框
  dialogVisible.value = true
  
  // 处理后端可能没有返回deputyManagerId字段的情况
  const processedRow = {
    ...row,
    deputyManagerId: row.deputyManagerId !== undefined ? row.deputyManagerId : null
  };
  
  console.log('处理前的行数据:', row);
  console.log('处理后的行数据:', processedRow);
  
  // 先加载表单基础数据
  formData.value = { 
    ...processedRow,
    managerId: null, // 先清空，避免选择器显示错误
    deputyManagerId: null
  }
  
  // 获取最新人员列表
  personnelLoading.value = true
  personnelApi.getPersonnelList().then(response => {
    if (response.data && response.data.success) {
      personnelOptions.value = response.data.data
      console.log('获取人员数据成功，共获取到', personnelOptions.value.length, '条记录')
      
      // 人员列表加载完成后，再设置选中的经理和主任
      nextTick(() => {
        formData.value.managerId = processedRow.managerId ? Number(processedRow.managerId) : null
        formData.value.deputyManagerId = processedRow.deputyManagerId ? Number(processedRow.deputyManagerId) : null
        
        console.log('已设置选中的部门负责人:', {
          部门经理ID: formData.value.managerId,
          部门经理姓名: getPersonnelName(formData.value.managerId),
          部门主任ID: formData.value.deputyManagerId, 
          部门主任姓名: getPersonnelName(formData.value.deputyManagerId)
        })
      })
    } else {
      ElMessage.error('获取人员数据失败')
    }
    personnelLoading.value = false
  }).catch(error => {
    console.error('获取人员列表失败:', error)
    ElMessage.error('获取人员数据失败: ' + (error.message || '服务器错误'))
    personnelLoading.value = false
  })
  
  // 清除表单验证
  nextTick(() => {
    formRef.value && formRef.value.clearValidate()
  })
}

// 提交部门表单
const submitForm = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    formLoading.value = true
    
    // 准备提交的数据，转换为后端需要的格式
    const submitData = {
      name: formData.value.name,
      code: formData.value.code,
      description: formData.value.description || '',
      parentId: formData.value.parentId,
      managerId: formData.value.managerId,
      deputyManagerId: formData.value.deputyManagerId,
      status: formData.value.status
    }
    
    let response
    
    if (formData.value.id) {
      // 更新部门
      response = await departmentApi.updateDepartment(formData.value.id, submitData)
    } else {
      // 创建部门
      response = await departmentApi.createDepartment(submitData)
    }
    
    if (response.data && response.data.success) {
      ElMessage.success(formData.value.id ? '部门更新成功' : '部门创建成功')
      dialogVisible.value = false
      fetchDepartmentList()
    } else {
      ElMessage.error(response.data?.message || (formData.value.id ? '部门更新失败' : '部门创建失败'))
    }
  } catch (error) {
    console.error('提交部门表单出错:', error)
    ElMessage.error('操作失败: ' + (error.message || '服务器错误'))
  } finally {
    formLoading.value = false
  }
}

// 关闭对话框
const handleCloseDialog = () => {
  dialogVisible.value = false
}

// 获取人员名称
const getPersonnelName = (id) => {
  if (!id) return ''
  
  // 确保ID是数字类型
  const numericId = Number(id)
  
  // 检查人员列表是否加载
  if (!personnelOptions.value || personnelOptions.value.length === 0) {
    console.warn('人员列表为空，无法获取人员姓名, ID:', numericId)
    return `ID: ${numericId}`
  }
  
  console.log(`尝试查找ID: ${numericId} 的人员信息，当前列表长度:`, personnelOptions.value.length)
  
  // 打印一下ID的类型，帮助调试
  console.log('ID类型:', typeof numericId, '值:', numericId)
  
  // 先用严格相等查找
  let personnel = personnelOptions.value.find(item => item.id === numericId)
  
  // 如果找不到，尝试用字符串比较
  if (!personnel) {
    console.log('使用严格相等未找到人员，尝试字符串比较')
    personnel = personnelOptions.value.find(item => String(item.id) === String(numericId))
  }
  
  if (personnel) {
    console.log(`找到ID为${numericId}的人员:`, personnel.name)
    return personnel.name
  } else {
    console.warn(`未找到ID为${numericId}的人员信息，人员列表长度:`, personnelOptions.value.length)
    // 输出所有人员ID列表帮助调试
    console.log('可用人员ID列表:', personnelOptions.value.map(p => ({id: p.id, type: typeof p.id})))
    return `未知(ID:${numericId})`
  }
}
</script>

<style lang="scss" scoped>
.department-management-container {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    .page-title {
      margin: 0;
      font-size: 20px;
    }
    
    .page-actions {
      display: flex;
      gap: 10px;
    }
  }
  
  .data-card {
    margin-bottom: 20px;
  }
}

.personnel-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  .personnel-code {
    color: var(--el-text-color-secondary);
    font-size: 13px;
  }
}
</style> 