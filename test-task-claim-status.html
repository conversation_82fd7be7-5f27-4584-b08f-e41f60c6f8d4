<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>任务领取状态测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .task-item {
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #eee;
            border-radius: 3px;
            background: #f9f9f9;
        }
        .claim-status {
            font-weight: bold;
            color: #007bff;
            transition: all 0.3s ease;
        }
        .claimed {
            color: #28a745;
            background: #d4edda;
            padding: 2px 8px;
            border-radius: 4px;
            border: 1px solid #c3e6cb;
        }
        .claiming {
            color: #ffc107;
            background: #fff3cd;
            padding: 2px 8px;
            border-radius: 4px;
            border: 1px solid #ffeaa7;
            animation: pulse 1.5s infinite;
        }
        .not-claimed {
            color: #6c757d;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        button {
            margin: 5px;
            padding: 8px 16px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        .btn-primary {
            background: #007bff;
            color: white;
        }
        .btn-success {
            background: #28a745;
            color: white;
        }
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>任务领取状态测试</h1>
    
    <div class="test-section">
        <h2>测试步骤</h2>
        <ol>
            <li>点击"获取任务列表"加载任务数据</li>
            <li>选择一个未领取的任务，点击"领取任务"</li>
            <li>再次点击"获取任务列表"查看领取状态是否正确更新</li>
            <li>检查任务是否显示为"已领取"状态</li>
        </ol>
    </div>

    <div class="test-section">
        <h2>操作面板</h2>
        <button class="btn-primary" onclick="loadTasks()">获取任务列表</button>
        <button class="btn-secondary" onclick="clearLog()">清空日志</button>
    </div>

    <div class="test-section">
        <h2>任务列表</h2>
        <div id="taskList">点击"获取任务列表"加载数据...</div>
    </div>

    <div class="test-section">
        <h2>操作日志</h2>
        <div id="log" class="log"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5001/api';
        
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        async function loadTasks() {
            try {
                log('开始获取任务列表...');
                
                const response = await fetch(`${API_BASE}/v2/tasks?pageNumber=1&pageSize=10`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                log(`API响应: ${JSON.stringify(data, null, 2)}`);

                if (data.success && data.data) {
                    displayTasks(data.data);
                    log(`成功加载 ${data.data.length} 个任务`);
                } else {
                    log(`API返回错误: ${data.message || '未知错误'}`);
                }
            } catch (error) {
                log(`获取任务列表失败: ${error.message}`);
                console.error('Error loading tasks:', error);
            }
        }

        function displayTasks(tasks) {
            const taskListDiv = document.getElementById('taskList');
            
            if (!tasks || tasks.length === 0) {
                taskListDiv.innerHTML = '<p>没有找到任务数据</p>';
                return;
            }

            let html = '';
            tasks.forEach(task => {
                const claimedStatus = task.claimedByUserId ? 
                    `<span class="claim-status claimed">已领取 (用户ID: ${task.claimedByUserId})</span>` :
                    `<span class="claim-status not-claimed">未领取</span>`;
                
                const claimButton = task.claimedByUserId ? 
                    `<button class="btn-secondary" disabled>已领取</button>` :
                    `<button class="btn-success" onclick="claimTask(${task.taskId})">领取任务</button>`;

                html += `
                    <div class="task-item">
                        <h4>任务 #${task.taskId}: ${task.name}</h4>
                        <p><strong>状态:</strong> ${task.status}</p>
                        <p><strong>优先级:</strong> ${task.priority}</p>
                        <p><strong>领取状态:</strong> ${claimedStatus}</p>
                        <p><strong>创建时间:</strong> ${new Date(task.creationTimestamp).toLocaleString()}</p>
                        ${claimButton}
                        <button class="btn-primary" onclick="getTaskDetail(${task.taskId})">查看详情</button>
                    </div>
                `;
            });

            taskListDiv.innerHTML = html;
        }

        async function claimTask(taskId) {
            try {
                log(`开始领取任务 #${taskId}...`);

                // 立即更新UI显示为"领取中"状态
                updateTaskClaimStatus(taskId, 'claiming');

                const response = await fetch(`${API_BASE}/v2/tasks/${taskId}/claim`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        notes: `测试领取任务 #${taskId}`
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                log(`领取任务API响应: ${JSON.stringify(data, null, 2)}`);

                if (data.success) {
                    log(`✅ 任务 #${taskId} 领取成功！`);

                    // 立即更新UI显示为"已领取"状态（乐观更新）
                    updateTaskClaimStatus(taskId, 'claimed');

                    // 延迟验证服务器状态，测试是否会出现闪烁
                    setTimeout(() => {
                        log('🔍 验证服务器状态（测试是否闪烁）...');
                        getTaskDetail(taskId);
                    }, 2000);

                    // 延迟刷新整个列表，模拟实际应用场景
                    setTimeout(() => {
                        log('🔄 刷新任务列表（模拟实际应用）...');
                        loadTasks();
                    }, 3000);
                } else {
                    log(`❌ 任务领取失败: ${data.message || '未知错误'}`);
                    updateTaskClaimStatus(taskId, 'failed');
                }
            } catch (error) {
                log(`❌ 领取任务失败: ${error.message}`);
                updateTaskClaimStatus(taskId, 'failed');
                console.error('Error claiming task:', error);
            }
        }

        function updateTaskClaimStatus(taskId, status) {
            const taskElements = document.querySelectorAll('.task-item');
            taskElements.forEach(element => {
                const titleElement = element.querySelector('.task-title');
                if (titleElement && titleElement.textContent.includes(`#${taskId}`)) {
                    const statusElement = element.querySelector('.claim-status');
                    const buttonElement = element.querySelector('button[onclick*="claimTask"]');

                    if (status === 'claiming') {
                        if (statusElement) statusElement.innerHTML = '<span class="claim-status claiming">领取中...</span>';
                        if (buttonElement) {
                            buttonElement.disabled = true;
                            buttonElement.textContent = '领取中...';
                        }
                    } else if (status === 'claimed') {
                        if (statusElement) statusElement.innerHTML = '<span class="claim-status claimed">✅ 已领取 (测试用户)</span>';
                        if (buttonElement) {
                            buttonElement.disabled = true;
                            buttonElement.textContent = '已领取';
                            buttonElement.className = 'btn-secondary';
                        }
                    } else if (status === 'failed') {
                        if (statusElement) statusElement.innerHTML = '<span class="claim-status not-claimed">❌ 领取失败</span>';
                        if (buttonElement) {
                            buttonElement.disabled = false;
                            buttonElement.textContent = '重新领取';
                        }
                    }
                }
            });
        }

        async function getTaskDetail(taskId) {
            try {
                log(`获取任务 #${taskId} 详情...`);
                
                const response = await fetch(`${API_BASE}/v2/tasks/${taskId}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                log(`任务详情API响应: ${JSON.stringify(data, null, 2)}`);

                if (data.success && data.data) {
                    const task = data.data;
                    const claimedInfo = task.claimedByUserId ? 
                        `已被用户 ${task.claimedByUserId} (${task.claimedByUserName || '未知'}) 领取` : 
                        '未领取';
                    log(`📋 任务 #${taskId} 详情 - 领取状态: ${claimedInfo}`);
                } else {
                    log(`获取任务详情失败: ${data.message || '未知错误'}`);
                }
            } catch (error) {
                log(`获取任务详情失败: ${error.message}`);
                console.error('Error getting task detail:', error);
            }
        }

        // 页面加载完成后自动加载任务列表
        window.onload = function() {
            log('页面加载完成，准备测试任务领取状态功能');
            log('请点击"获取任务列表"开始测试');
        };
    </script>
</body>
</html>
