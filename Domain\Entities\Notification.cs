using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using ItAssetsSystem.Models.Entities;

namespace ItAssetsSystem.Domain.Entities
{
    /// <summary>
    /// 通知实体
    /// </summary>
    [Table("notifications")]
    public class Notification
    {
        /// <summary>
        /// 通知ID (BIGINT)
        /// </summary>
        [Key]
        public long NotificationId { get; set; }

        /// <summary>
        /// 接收用户ID (关联 users.Id - INT)
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// 标题
        /// </summary>
        [Required]
        [MaxLength(255)]
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// 内容
        /// </summary>
        [Required]
        public string Content { get; set; } = string.Empty;

        /// <summary>
        /// 通知类型：task/system/alert/gamification
        /// </summary>
        [Required]
        [MaxLength(50)]
        public string Type { get; set; } = string.Empty;

        /// <summary>
        /// 关联对象ID (可能是 TaskId, BadgeId etc - BIGINT)
        /// </summary>
        public long? ReferenceId { get; set; }

        /// <summary>
        /// 关联对象类型 (Task, Badge, etc)
        /// </summary>
        [MaxLength(50)]
        public string ReferenceType { get; set; }

        /// <summary>
        /// 是否已读
        /// </summary>
        public bool IsRead { get; set; } = false;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreationTimestamp { get; set; } = DateTime.Now;

        /// <summary>
        /// 阅读时间
        /// </summary>
        public DateTime? ReadTimestamp { get; set; }

        /// <summary>
        /// 优先级 (Normal, High, Low) - 此字段不在数据库中
        /// </summary>
        [NotMapped]
        [MaxLength(20)]
        public string Priority { get; set; } = "Normal";

        /// <summary>
        /// 触发用户ID (谁触发了这个通知) - 此字段不在数据库中
        /// </summary>
        [NotMapped]
        public int? TriggeredByUserId { get; set; }

        /// <summary>
        /// 额外数据 (JSON格式) - 此字段不在数据库中
        /// </summary>
        [NotMapped]
        public string ExtraData { get; set; }

        /// <summary>
        /// 创建时间 (API兼容)
        /// </summary>
        [NotMapped]
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// 阅读时间 (API兼容)
        /// </summary>
        [NotMapped]
        public DateTime? ReadAt { get; set; }

        /// <summary>
        /// 资源类型 (API兼容)
        /// </summary>
        [NotMapped]
        public string ResourceType { get; set; }

        /// <summary>
        /// 资源ID (API兼容)
        /// </summary>
        [NotMapped]
        public long? ResourceId { get; set; }
    }
}