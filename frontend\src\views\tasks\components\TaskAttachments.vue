<template>
  <div class="task-attachments">
    <h3>任务附件</h3>
    <FileUpload
      v-model="attachments"
      :uploadPath="`tasks/${taskId}/attachments`"
      :show-file-list="true"
      :multiple="true"
      button-text="上传附件"
      tip-text="支持多文件上传，单个文件不超过10MB"
    />
    <div v-if="attachments.length === 0" class="empty">暂无附件</div>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'
import FileUpload from '@/components/FileUpload.vue'
const props = defineProps({
  taskId: {
    type: [String, Number],
    required: true
  }
})
const attachments = ref([])
// 可根据 taskId 加载历史附件，后续可扩展为API请求
watch(() => props.taskId, (newId) => {
  attachments.value = [] // TODO: 加载历史附件
})
</script>

<style scoped>
.task-attachments {
  border-top: 1px solid #eee;
  margin-top: 16px;
  padding-top: 12px;
}
.empty {
  color: #bbb;
  font-size: 13px;
  margin: 8px 0;
}
</style> 