{"version": 3, "file": "index.js", "sources": ["index.ts"], "sourcesContent": ["// eslint-disable-next-line import/no-extraneous-dependencies\nimport interact from '@interactjs/interactjs'\n\nexport default interact\n\nif (typeof module === 'object' && !!module) {\n  try {\n    module.exports = interact\n  } catch {}\n}\n\n;(interact as any).default = interact\n"], "names": ["module", "exports", "interact", "_unused", "default"], "mappings": ";;;;;;;;;;;AAAA;AAKA,IAAI,OAAOA,MAAM,KAAK,QAAQ,IAAI,CAAC,CAACA,MAAM,EAAE;EAC1C,IAAI;IACFA,MAAM,CAACC,OAAO,GAAGC,QAAQ,CAAA;AAC3B,GAAC,CAAC,OAAAC,OAAA,EAAM,EAAC;AACX,CAAA;AAEED,QAAQ,CAASE,OAAO,GAAGF,QAAQ"}