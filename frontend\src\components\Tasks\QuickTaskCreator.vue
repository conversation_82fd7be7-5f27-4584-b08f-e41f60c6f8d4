<template>
  <div class="quick-task-creator">
    <div class="title">
      <slot name="title">快速创建任务</slot>
          </div>
    <el-form :model="form" :rules="rules" ref="formRef" label-width="80px" size="small">
      <el-form-item label="任务名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入任务名称" />
          </el-form-item>
      <el-form-item label="负责人" prop="assigneeUserIds">
            <el-select
          v-model="form.assigneeUserIds" 
          multiple 
          collapse-tags 
          collapse-tags-tooltip
              filterable
          placeholder="请选择负责人(多选)"
          :loading="loading"
          style="width: 100%"
            >
              <el-option
                v-for="user in users"
                :key="user.id"
                :label="user.name"
                :value="user.id"
              >
                <div class="user-option">
              <el-avatar :size="24" :src="user.avatarUrl" v-if="user.avatarUrl">{{ user.name.substring(0, 1) }}</el-avatar>
                  <span>{{ user.name }}</span>
                  <span class="user-dept">{{ user.department }}</span>
                </div>
              </el-option>
            </el-select>
      </el-form-item>
      <el-form-item label="优先级" prop="priority">
        <el-select v-model="form.priority" placeholder="请选择优先级">
          <el-option label="高" value="High" />
          <el-option label="中" value="Medium" />
          <el-option label="低" value="Low" />
            </el-select>
      </el-form-item>
      <el-form-item label="截止日期" prop="planEndDate">
        <el-date-picker v-model="form.planEndDate" type="date" placeholder="选择日期" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="submitForm" :loading="submitting">创建</el-button>
        <el-button @click="resetForm">重置</el-button>
      </el-form-item>
        </el-form>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Plus, Close, Check, More, Star, DocumentCopy 
} from '@element-plus/icons-vue'
import { taskApi } from '@/api/task'
import userApi from '@/api/user'

export default {
  name: 'QuickTaskCreator',

  components: {
    Plus, Close, Check, More, Star, DocumentCopy
  },

  props: {
    // 是否始终展开（嵌入式使用）
    alwaysExpanded: {
      type: Boolean,
      default: false
    },
    // 触发按钮文本
    triggerText: {
      type: String,
      default: '快速创建任务'
    },
    // 触发按钮样式类
    triggerClass: {
      type: String,
      default: ''
    },
    // 默认负责人ID
    defaultAssigneeId: {
      type: Number,
      default: null
    },
    // 创建成功后的回调
    onCreated: {
      type: Function,
      default: null
    }
  },

  emits: ['created', 'expand', 'collapse', 'expand-to-full-form'],

  setup(props, { emit }) {
    const formRef = ref(null)
    const isExpanded = ref(props.alwaysExpanded)
    const submitting = ref(false)
    const users = ref([])
    const templates = ref([])
    const smartSuggestions = ref([])
    const showSuggestions = ref(false)

    // 表单数据
    const form = reactive({
      name: '',
      assigneeUserIds: [], // 使用数组支持多负责人
      priority: 'Medium',
      planEndDate: null
    })

    // 表单验证规则
    const rules = {
      name: [
        { required: true, message: '请输入任务名称', trigger: 'blur' }
      ],
      assigneeUserIds: [
        { type: 'array', message: '请选择负责人', trigger: 'change' }
      ],
      priority: [
        { required: true, message: '请选择优先级', trigger: 'change' }
      ]
    }

    // 可选协作人员（排除主要负责人）
    const availableCollaborators = computed(() => {
      return users.value.filter(user => !form.assigneeUserIds.includes(user.id))
    })

    // 展开创建器
    const expandCreator = () => {
      isExpanded.value = true
      emit('expand')
    }

    // 收起创建器
    const collapseCreator = () => {
      isExpanded.value = false
      resetForm()
      emit('collapse')
    }

    // 重置表单
    const resetForm = () => {
      form.name = ''
      form.assigneeUserIds = []
      form.priority = 'Medium'
      form.planEndDate = null
      showSuggestions.value = false
      if (formRef.value) {
        formRef.value.clearValidate()
      }
    }

    // 提交表单
    const submitForm = async () => {
      if (!formRef.value) return
      
      await formRef.value.validate(async (valid, fields) => {
        if (valid) {
          submitting.value = true
          try {
            // 获取主负责人和协作者
            let assigneeUserId = null;
            let collaboratorUserIds = [];
            
            if (form.assigneeUserIds.length > 0) {
              assigneeUserId = form.assigneeUserIds[0];
              collaboratorUserIds = form.assigneeUserIds.slice(1);
            }
            
            // 构建任务数据
        const taskData = {
              name: form.name,
              priority: form.priority,
          status: 'Todo',
              assigneeUserId: assigneeUserId,
              assigneeUserIds: form.assigneeUserIds, // 保留原始数组，让API处理转换
              collaboratorUserIds: collaboratorUserIds, // 显式设置协作者
              planEndDate: form.planEndDate ? new Date(form.planEndDate).toISOString() : null
            }
            
            console.log('创建任务数据:', taskData);
            
            // 调用API创建任务
            const result = await taskApi.createTask(taskData)
            
            // 创建成功后重置表单
          resetForm()
            
            // 发出创建成功事件
            emit('created', result.data)
            
            // 显示成功消息
            ElMessage.success('任务创建成功')
      } catch (error) {
        console.error('创建任务失败:', error)
            ElMessage.error(`创建任务失败: ${error.message || '未知错误'}`)
      } finally {
        submitting.value = false
      }
        } else {
          console.log('表单验证失败:', fields)
        }
      })
    }

    // 展开到完整表单
    const expandToFullForm = () => {
      emit('expand-to-full-form', {
        title: form.name,
        assigneeUserIds: form.assigneeUserIds,
        priority: form.priority,
        dueDate: form.planEndDate
      })
    }

    // 应用模板
    const applyTemplate = (template) => {
      form.name = template.title
      form.assigneeUserIds = template.defaultAssigneeIds
      form.priority = template.priority
      if (template.dueInDays) {
        const dueDate = new Date()
        dueDate.setDate(dueDate.getDate() + template.dueInDays)
        form.planEndDate = dueDate
      }
      ElMessage.success(`已应用模板：${template.name}`)
    }

    // 应用建议
    const applySuggestion = (suggestion) => {
      if (suggestion.assigneeUserIds) {
        form.assigneeUserIds = suggestion.assigneeUserIds
      }
      if (suggestion.priority) {
        form.priority = suggestion.priority
      }
      if (suggestion.collaborators) {
        form.collaboratorUserIds = suggestion.collaborators
      }
      showSuggestions.value = false
      ElMessage.success('已应用建议')
    }

    // 生成智能建议
    const generateSmartSuggestions = () => {
      // 这里可以基于历史数据、用户习惯等生成智能建议
      smartSuggestions.value = []
      
      if (form.name.includes('bug') || form.name.includes('修复')) {
        smartSuggestions.value.push({
          id: 'bug-fix',
          title: 'Bug修复任务',
          description: '建议设置为高优先级，分配给技术团队',
          confidence: 85,
          priority: 'High',
          assigneeUserIds: users.value.filter(u => u.role === 'developer').map(u => u.id)
        })
      }
      
      if (form.name.includes('紧急') || form.name.includes('urgent')) {
        smartSuggestions.value.push({
          id: 'urgent-task',
          title: '紧急任务处理',
          description: '建议立即分配并设置今日截止',
          confidence: 90,
          priority: 'High',
          dueDate: new Date()
        })
      }
    }

    // 加载用户列表
    const loadUsers = async () => {
      try {
        const response = await userApi.getUserList()
        if (response.success) {
          users.value = response.data
        } else if (response.data) {
          // 如果没有success字段但有data，直接使用data
          users.value = response.data
        }
      } catch (error) {
        console.error('加载用户列表失败:', error)
      }
    }

    // 加载任务模板
    const loadTemplates = async () => {
      // 这里可以从API加载常用模板
      templates.value = [
        { id: 1, name: '日常检查', title: '日常设备检查', priority: 'Medium', dueInDays: 1 },
        { id: 2, name: '紧急维修', title: '设备紧急维修', priority: 'High', dueInDays: 0 },
        { id: 3, name: '月度报告', title: '月度工作报告', priority: 'Low', dueInDays: 7 }
      ]
    }

    // 格式化日期
    const formatDate = (date) => {
      if (!date) return null
      return date.toISOString().split('T')[0]
    }

    // 监听标题变化，生成建议
    watch(() => form.name, (newName) => {
      if (newName.length > 2) {
        generateSmartSuggestions()
      }
    })

    onMounted(() => {
      loadUsers()
      loadTemplates()
    })

    return {
      formRef,
      isExpanded,
      submitting,
      users,
      templates,
      smartSuggestions,
      showSuggestions,
      form,
      rules,
      availableCollaborators,
      expandCreator,
      collapseCreator,
      resetForm,
      submitForm,
      expandToFullForm,
      applyTemplate,
      applySuggestion,
      generateSmartSuggestions
    }
  }
}
</script>

<style scoped>
.quick-task-creator {
  position: relative;
}

.trigger-btn {
  font-size: 14px;
}

.quick-form-container.floating {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 2000;
  width: 500px;
  max-width: 90vw;
}

.quick-form-card {
  border-radius: 8px;
}

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 500;
}

.close-btn {
  padding: 5px;
}

.quick-settings {
  display: flex;
  gap: 10px;
  margin-bottom: 12px;
}

.quick-assignee {
  flex: 2;
}

.quick-priority {
  flex: 1;
}

.quick-date {
  flex: 1.5;
}

.collaborators-section {
  margin-bottom: 12px;
}

.user-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.user-dept {
  font-size: 12px;
  color: #999;
}

.quick-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 15px;
}

.left-actions,
.right-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.suggestion-btn,
.template-btn,
.expand-btn {
  font-size: 12px;
  padding: 4px 8px;
}

.suggestions-panel {
  margin-top: 15px;
  border-top: 1px solid #e4e7ed;
  padding-top: 15px;
}

.suggestions-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  font-weight: 500;
  font-size: 13px;
}

.suggestions-list {
  max-height: 200px;
  overflow-y: auto;
}

.suggestion-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 8px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.2s;
}

.suggestion-item:hover {
  border-color: #409eff;
  background: #f0f9ff;
}

.suggestion-content {
  flex: 1;
}

.suggestion-content strong {
  font-size: 13px;
}

.suggestion-content p {
  font-size: 12px;
  color: #666;
  margin: 2px 0 0 0;
}

.suggestion-meta {
  margin-left: 10px;
}
</style>