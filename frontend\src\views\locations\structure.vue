/**
 * 航空航天级IT资产管理系统 - 位置结构页面
 * 文件路径: src/views/locations/structure.vue
 * 功能描述: 管理公司组织的位置层级结构
 */

<template>
  <div class="location-structure-container">
    <!-- 页面标题和操作按钮 -->
    <div class="page-header">
      <h2 class="page-title">位置结构</h2>
      <div class="page-actions">
        <el-button type="primary" @click="handleAddLocation" :icon="Plus">添加位置</el-button>
        <el-button type="success" @click="handleExpandAll" :icon="Expand">展开全部</el-button>
        <el-button @click="handleCollapseAll" :icon="Fold">折叠全部</el-button>
      </div>
    </div>
    
    <!-- 位置树形结构 -->
    <el-card class="tree-card">
      <div class="tree-container">
        <el-tree
          ref="locationTree"
          :data="locationData"
          :props="defaultProps"
          node-key="id"
          :expand-on-click-node="false"
          :expanded-keys="expandedKeys"
          v-loading="loading"
          class="location-tree"
        >
          <template #default="{ node, data }">
            <div class="tree-node">
              <span class="label">{{ data.name }}</span>
              <span class="code">[{{ data.code }}]</span>
              <span v-if="data.assetCount > 0" class="count">资产: {{ data.assetCount }}</span>
              <div class="actions">
                <el-button
                  size="small"
                  type="primary"
                  @click="handleEdit(data)"
                  :icon="Edit"
                  circle
                  title="编辑位置"
                />
                <el-button
                  size="small"
                  type="success"
                  @click="handleAddChild(data)"
                  :icon="Plus"
                  circle
                  title="添加子位置"
                />
                <el-button
                  size="small"
                  type="danger"
                  @click="handleDelete(data)"
                  :icon="Delete"
                  circle
                  title="删除位置"
                />
              </div>
            </div>
          </template>
        </el-tree>
        
        <el-empty
          v-if="locationData.length === 0 && !loading"
          description="暂无位置数据"
        />
      </div>
    </el-card>
    
    <!-- 位置表单对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="600px"
      append-to-body
    >
      <el-form
        ref="locationForm"
        :model="locationFormData"
        :rules="locationRules"
        label-width="100px"
      >
        <el-form-item label="位置名称" prop="name">
          <el-input v-model="locationFormData.name" placeholder="请输入位置名称" />
        </el-form-item>
        <el-form-item label="位置编码" prop="code">
          <el-input v-model="locationFormData.code" placeholder="请输入位置编码" />
        </el-form-item>
        <el-form-item label="位置类型" prop="type">
          <el-select v-model="locationFormData.type" placeholder="请选择位置类型" style="width: 100%">
            <el-option
              v-for="type in LOCATION_TYPES"
              :key="type.value"
              :label="type.label"
              :value="type.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="上级位置" prop="parentId">
          <el-select
            v-model="locationFormData.parentId"
            :disabled="locationFormData.type === 0"
            clearable
            filterable
            placeholder="请选择上级位置"
            style="width: 100%"
          >
            <el-option
              v-for="item in filteredLocationData"
              :key="item.id"
              :label="`${item.name} [${item.code}]`"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="详细地址" prop="address">
          <el-input v-model="locationFormData.address" placeholder="请输入详细地址" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="locationFormData.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>
        
        <!-- 新增默认部门选择 -->
        <el-form-item label="默认部门" prop="defaultDepartmentId">
          <el-select
            v-model="locationFormData.defaultDepartmentId"
            clearable
            filterable
            placeholder="请选择默认部门"
            style="width: 100%"
          >
            <el-option
              v-for="dept in departmentOptions"
              :key="dept.id"
              :label="dept.name"
              :value="dept.id"
            />
          </el-select>
        </el-form-item>
        
        <!-- 新增位置负责人选择 -->
        <el-form-item label="位置负责人" prop="managerId">
          <el-select
            v-model="locationFormData.managerId"
            disabled
            placeholder="位置负责人由部门决定"
            style="width: 100%"
          >
            <el-option
              v-for="user in managerOptions"
              :key="user.value"
              :label="user.label"
              :value="user.value"
            />
          </el-select>
          <div class="form-tip" v-if="locationFormData.defaultDepartmentId">
            <el-icon><InfoFilled /></el-icon> 位置负责人由部门自动决定
          </div>
        </el-form-item>
        
        <!-- 新增使用人多选 -->
        <el-form-item label="使用人" prop="usageUserIds">
          <el-select
            v-model="locationFormData.usageUserIds"
            multiple
            filterable
            placeholder="请选择使用人"
            style="width: 100%"
            @visible-change="visibleChange"
            value-key="value"
            :loading="usersLoading"
          >
            <template #prefix v-if="locationFormDetailLoading">
              <el-icon class="is-loading"><svg class="circular" viewBox="25 25 50 50"><circle cx="50" cy="50" r="20" fill="none"/></svg></el-icon>
              <span>加载中...</span>
            </template>
            <el-option
              v-for="user in usageUserOptions"
              :key="user.value"
              :label="user.label"
              :value="user.value"
            />
          </el-select>
          <div v-if="locationFormData.usersDisplay" class="form-tip">
            <el-icon><InfoFilled /></el-icon> 已关联用户: {{ locationFormData.usersDisplay }}
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>
    
    <!-- 部门设置对话框 -->
    <el-dialog 
      v-model="departmentDialogVisible" 
      title="设置使用部门" 
      width="500px"
    >
      <el-form :model="departmentForm" label-width="100px">
        <el-form-item label="选择部门">
          <el-select 
            v-model="departmentForm.departmentId" 
            placeholder="请选择部门" 
            clearable
            filterable
            style="width: 100%"
          >
            <el-option 
              v-for="dept in departmentOptions" 
              :key="dept.id" 
              :label="dept.name" 
              :value="dept.id" 
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="departmentDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveDepartment">确认</el-button>
        </div>
      </template>
    </el-dialog>
    
    <!-- 人员设置对话框 -->
    <el-dialog 
      v-model="usersDialogVisible" 
      title="设置使用人" 
      width="700px"
    >
      <el-transfer
        v-model="selectedUsers"
        :data="allUsers"
        :titles="['可选人员', '已选人员']"
        :props="{
          key: 'id',
          label: 'name'
        }"
        filterable
      />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="usersDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveUsers">确认</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch, nextTick, onBeforeUnmount } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Plus, Edit, Delete, 
  Expand, Fold, 
  LocationInformation,
  OfficeBuilding,
  User,
  InfoFilled
} from '@element-plus/icons-vue'
import locationApi from '@/api/location'
import departmentApi from '@/api/department'
import personnelApi from '@/api/personnel'

// 位置结构数据
const locationData = ref([])
const loading = ref(false)
const expandedKeys = ref([])
const locationTree = ref(null)

// 对话框控制
const dialogVisible = ref(false)
const dialogType = ref('add') // 'add' | 'edit' | 'addChild'
const dialogTitle = computed(() => {
  if (dialogType.value === 'add') return '添加位置'
  if (dialogType.value === 'edit') return '编辑位置'
  if (dialogType.value === 'addChild') return '添加子位置'
  return '位置信息'
})
const locationForm = ref(null)
const editNodeIsRoot = ref(false)
const locationFormDetailLoading = ref(false)

// 部门设置对话框
const departmentDialogVisible = ref(false)
const departmentForm = reactive({
  departmentId: null,
  locationId: null
})

// 人员设置对话框
const usersDialogVisible = ref(false)
const selectedLocation = ref(null)
const allUsers = ref([])
const selectedUsers = ref([])
const usersLoading = ref(false)

// 树形控件配置
const defaultProps = {
  children: 'children',
  label: 'name'
}

// 部门和用户数据
const departmentOptions = ref([])
const userOptions = ref([])
const managerOptions = ref([])
const usageUserOptions = ref([])
const isLoadingOptions = ref(false)

// 表单数据
const locationFormData = reactive({
  id: undefined,
  name: '',
  code: '',
  type: undefined,
  parentId: null,
  description: '',
  address: '',
  remark: '',
  defaultDepartmentId: null,
  managerId: null,
  usageUserIds: [],
  isActive: true
})

// 表单校验规则
const locationRules = {
  name: [
    { required: true, message: '请输入位置名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入位置编码', trigger: 'blur' },
    { pattern: /^[A-Z0-9_]{2,20}$/, message: '编码只能包含大写字母、数字和下划线', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择位置类型', trigger: 'change' }
  ]
}

// 位置类型常量
const LOCATION_TYPES = [
  { value: 0, label: '工厂' },
  { value: 1, label: '产线' },
  { value: 2, label: '工序' },
  { value: 3, label: '工位' },
  { value: 4, label: '设备位置' }
]

// 获取可选的上级位置（平铺列表）
const availableParents = computed(() => {
  if (locationFormData.type === undefined) return []
  if (locationFormData.type === 0) return [] // 工厂没有上级

  const parentType = locationFormData.type - 1
  const flatten = (nodes) => {
    return nodes.reduce((acc, node) => {
      if (node.type === parentType) {
        acc.push({
          id: node.id,
          name: `${node.name} [${node.code}]`,
          type: node.type
        })
      }
      if (node.children) {
        acc.push(...flatten(node.children))
      }
      return acc
    }, [])
  }
  
  return flatten(locationData.value)
})

// 过滤后的位置树数据
const filteredLocationData = computed(() => {
  if (locationFormData.type === 0) return []; // 工厂没有上级
  
  const parentType = locationFormData.type - 1;
  
  // 平铺符合条件的上级节点（不再是树形）
  const flattenParents = (nodes) => {
    return nodes.reduce((acc, node) => {
      if (node.type === parentType) {
        acc.push({
          ...node,
          children: undefined // 移除children属性
        });
      }
      if (node.children) {
        acc.push(...flattenParents(node.children));
      }
      return acc;
    }, []);
  };
  
  return flattenParents(locationData.value);
});

// 获取动态placeholder
const getParentPlaceholder = () => {
  if (locationFormData.type === 0) return '工厂没有上级位置'
  if (!locationFormData.type) return '请先选择位置类型'
  return `请选择${LOCATION_TYPES[locationFormData.type - 1]?.label || '上级'}位置`
}

// 监听位置类型变化
watch(() => locationFormData.type, (newType, oldType) => {
  // 类型变化时清空上级选择（除非是从无类型初始化）
  if (oldType !== undefined) {
    locationFormData.parentId = null
  }
  
  // 特殊处理工厂类型
  if (newType === 0) {
    locationFormData.parentId = null
  }
}, { immediate: true })

// 更新管理员和使用人选项
const updatePersonnelOptions = async () => {
  try {
    const response = await personnelApi.getPersonnelList()
    if (response.data && response.data.success) {
      const personnel = response.data.data || []
      
      // 设置负责人选项
      managerOptions.value = personnel.map(person => ({
        value: person.id,
        label: `${person.name || '未命名'} ${person.employeeCode ? `(${person.employeeCode})` : ''} - ${person.departmentName || '无部门'}`
      }))
      
      // 设置使用人选项
      usageUserOptions.value = personnel.map(person => ({
        value: person.id,
        label: `${person.name || '未命名'} ${person.employeeCode ? `(${person.employeeCode})` : ''} - ${person.departmentName || '无部门'}`
      }))
    }
  } catch (error) {
    console.error('获取人员列表失败:', error)
    ElMessage.error('获取人员列表失败')
  }
}

// 监听defaultDepartmentId变化，自动设置部门负责人
watch(() => locationFormData.defaultDepartmentId, async (newDeptId) => {
  if (newDeptId) {
    // 查找部门信息
    const dept = departmentOptions.value.find(d => d.id === newDeptId)
    if (dept && dept.managerId) {
      // 如果部门有负责人，自动设置为位置负责人
      locationFormData.managerId = dept.managerId
    } else {
      locationFormData.managerId = null
    }
  } else {
    locationFormData.managerId = null
  }
})

// 监听上级位置变化
watch(() => locationFormData.parentId, (newParentId) => {
  if (newParentId) {
    // 确保父级位置在过滤后的数据中
    const parentExists = filteredLocationData.value.some(node => node.id === newParentId)
    if (!parentExists) {
      locationFormData.parentId = null
    }
  }
})

// 获取位置结构数据
const fetchLocationStructure = async () => {
  loading.value = true
  try {
    const res = await locationApi.getLocationTree()
    console.log('获取位置树结构:', res)
    if (res.success) {
      locationData.value = res.data || []
      // 默认展开第一级节点
      const firstLevelKeys = locationData.value.map(item => item.id)
      expandedKeys.value = firstLevelKeys
    } else {
      ElMessage.error(res.message || '获取位置树结构失败')
      locationData.value = []
    }
  } catch (error) {
    console.error('获取位置树结构失败:', error)
    ElMessage.error('获取位置树结构失败')
    locationData.value = []
  } finally {
    loading.value = false
  }
}

// 展开全部
const handleExpandAll = () => {
  console.log('展开全部')
  const allKeys = []
  const getKeys = (nodes) => {
    nodes.forEach(node => {
      allKeys.push(node.id)
      if (node.children && node.children.length > 0) {
        getKeys(node.children)
      }
    })
  }
  getKeys(locationData.value)
  expandedKeys.value = allKeys
}

// 折叠全部
const handleCollapseAll = () => {
  console.log('折叠全部')
  expandedKeys.value = []
}

// 添加位置
const handleAddLocation = () => {
  console.log('添加位置')
  dialogType.value = 'add'
  resetForm()
  // 默认选择工厂类型
  locationFormData.type = 0
  dialogVisible.value = true
}

// 添加子位置
const handleAddChild = (data) => {
  console.log('添加子位置:', data)
  dialogType.value = 'addChild'
  resetForm()
  
  // 自动设置父级和类型
  locationFormData.parentId = data.id
  locationFormData.type = data.type + 1
  
  // 自动生成建议编码
  if (data.children) {
    const childCount = data.children.length
    locationFormData.code = `${data.code}_${String(childCount + 1).padStart(2, '0')}`
  } else {
    locationFormData.code = `${data.code}_01`
  }
  
  // 继承父级的使用部门和使用人
  if (data.defaultDepartmentId) {
    locationFormData.defaultDepartmentId = data.defaultDepartmentId
    // 负责人会由watch自动设置
  }
  
  dialogVisible.value = true
}

// 编辑位置
const handleEdit = async (data) => {
  dialogType.value = 'edit'
  editNodeIsRoot.value = !data.parentId
  
  // 重置表单
  resetForm()
  loading.value = true
  locationFormDetailLoading.value = true
  
  try {
    // 先加载人员选项
    await updatePersonnelOptions()
    
    // 获取位置详情（包含关联信息）
    const detailData = await fetchLocationDetail(data.id)
    
    if (detailData) {
      // 确保响应式更新顺序
      nextTick(() => {
        // 先设置类型，因为类型会影响父级选择
        locationFormData.type = detailData.type
        nextTick(() => {
          // 然后设置其他数据
          Object.assign(locationFormData, {
            id: detailData.id,
            name: detailData.name,
            code: detailData.code,
            type: detailData.type,
            parentId: detailData.parentId,
            address: detailData.address,
            remark: detailData.description,
            defaultDepartmentId: detailData.defaultDepartmentId,
            managerId: detailData.managerId,
            usersDisplay: detailData.usersDisplay || null
          })
          
          // 单独设置使用人，以确保正确触发响应式更新
          if (detailData.usageUserIds && Array.isArray(detailData.usageUserIds)) {
            console.log('设置使用人IDs:', detailData.usageUserIds)
            locationFormData.usageUserIds = [...detailData.usageUserIds]
          } else {
            locationFormData.usageUserIds = []
          }
          
          // 如果使用部门为空且有父级，尝试继承父级的使用部门和人员
          if (!detailData.defaultDepartmentId && detailData.parentId && detailData.type > 1) {
            inheritParentSettings(detailData.parentId)
          }
          
          // 最后显示对话框
          dialogVisible.value = true
        })
      })
    }
  } catch (error) {
    console.error('加载位置详情失败:', error)
    ElMessage.error('加载位置详情失败')
  } finally {
    loading.value = false
    locationFormDetailLoading.value = false
  }
}

// 删除位置
const handleDelete = async (data) => {
  // 检查资产关联
  if (data.assetCount > 0) {
    ElMessage.warning(`该位置已关联 ${data.assetCount} 个资产，请先解除关联`)
    return
  }
  
  // 添加确认对话框，询问是否要递归删除子位置
  let confirmMessage = '确定要删除该位置吗？删除后不可恢复！'
  let isRecursive = false
  
  if (data.children && data.children.length > 0) {
    isRecursive = await ElMessageBox.confirm(
      `该位置包含 ${data.children.length} 个子位置，是否一并删除？`,
      '警告',
      {
        confirmButtonText: '是，一并删除',
        cancelButtonText: '否，仅删除该位置',
        type: 'warning',
        distinguishCancelAndClose: true
      }
    ).then(() => true).catch(action => {
      if (action === 'cancel') {
        return false
      } else {
        throw new Error('cancel-all')
      }
    }).catch(error => {
      if (error.message === 'cancel-all') {
        return null // 用户取消整个操作
      }
      return false
    })
    
    if (isRecursive === null) {
      return // 用户取消整个操作
    }
  }
  
  try {
    await ElMessageBox.confirm(
      isRecursive 
        ? `将删除此位置及其所有子位置（共 ${countAllChildren(data) + 1} 个），确定继续吗？` 
        : confirmMessage,
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const res = await locationApi.deleteLocation(data.id, { recursive: isRecursive })
    if (res.success) {
    ElMessage.success('删除成功')
      // 刷新数据
    fetchLocationStructure()
    } else {
      ElMessage.error(res.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除位置失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 计算节点及其所有子节点的总数
const countAllChildren = (node) => {
  if (!node.children || node.children.length === 0) {
    return 0
  }
  
  let count = node.children.length
  for (const child of node.children) {
    count += countAllChildren(child)
  }
  
  return count
}

// 获取部门数据
const fetchDepartments = async () => {
  try {
    isLoadingOptions.value = true
    const res = await departmentApi.getDepartments()
    if (res.data && res.data.success) {
      departmentOptions.value = res.data.data || []
    } else {
      ElMessage.error((res.data && res.data.message) || '获取部门列表失败')
    }
  } catch (error) {
    console.error('获取部门列表失败:', error)
    ElMessage.error('获取部门列表失败')
  } finally {
    isLoadingOptions.value = false
  }
}

// 获取人员列表
const fetchPersonnel = async () => {
  loading.value = true
  try {
    const response = await personnelApi.getPersonnelList()
    if (response.data && response.data.success) {
      const personnel = response.data.data || []
      allUsers.value = personnel.map(user => ({
        id: user.id,
        name: user.name || user.employeeCode,
        disabled: false
      }))
      console.log(`获取到 ${allUsers.value.length} 个人员`)
    } else {
      ElMessage.warning('获取人员列表失败')
      allUsers.value = []
    }
  } catch (error) {
    console.error('获取人员列表失败:', error)
    ElMessage.error('获取人员列表失败')
    allUsers.value = []
  } finally {
    loading.value = false
  }
}

// 获取位置关联的人员
const fetchLocationUsers = async (locationId) => {
  try {
    usersLoading.value = true;
    const response = await locationApi.getLocationUsers(locationId);
    console.log('获取位置关联人员返回数据:', response);
    
    if (response && Array.isArray(response)) {
      // 获取管理员
      const managers = response.filter(u => u.type === 1 || u.userType === 1);
      // 获取使用人
      const users = response.filter(u => u.type === 0 || u.userType === 0);
      
      selectedUsers.value = users.map(u => u.personnelId || u.id);
      
      console.log('筛选后的管理员:', managers);
      console.log('筛选后的使用人:', users);
    } else {
      selectedUsers.value = [];
      ElMessage.warning('未获取到位置关联人员数据');
    }
  } catch (error) {
    console.error('获取位置关联人员失败:', error);
    ElMessage.error('获取位置关联人员失败');
    selectedUsers.value = [];
  } finally {
    usersLoading.value = false;
  }
};

// 设置部门按钮点击
const handleSetDepartment = async (data) => {
  selectedLocation.value = data.id
  departmentForm.locationId = data.id
  
  try {
    // 从位置树数据中直接获取位置详情，避免额外的API调用
    const locationDetail = data
    departmentForm.departmentId = locationDetail.defaultDepartmentId || null
    departmentDialogVisible.value = true
  } catch (error) {
    console.error('获取位置信息失败:', error)
    ElMessage.error('获取位置信息失败')
    departmentForm.departmentId = null
    departmentDialogVisible.value = true
  }
}

// 保存部门关联
const saveDepartment = async () => {
  if (!departmentForm.locationId) {
    ElMessage.warning('未选择位置')
    return
  }
  
  try {
    const response = await locationApi.updateLocationDepartment(
      departmentForm.locationId, 
      { departmentId: departmentForm.departmentId }
    )
    
    if (response.success) {
      ElMessage.success('部门关联设置成功')
      departmentDialogVisible.value = false
      // 刷新位置结构
      fetchLocationStructure()
    } else {
      ElMessage.error(response.message || '设置部门关联失败')
    }
  } catch (error) {
    console.error('保存部门关联失败:', error)
    ElMessage.error('保存部门关联失败')
  }
}

// 设置人员按钮点击
const handleSetUsers = async (data) => {
  selectedLocation.value = data.id
  
  try {
    // 先获取所有人员
    await fetchPersonnel()
    // 再获取当前已关联的人员
    await fetchLocationUsers(data.id)
    
    usersDialogVisible.value = true
  } catch (error) {
    console.error('初始化人员数据失败:', error)
    ElMessage.error('初始化人员数据失败')
  }
}

// 保存人员关联
const saveUsers = async () => {
  if (!selectedLocation.value) {
    ElMessage.warning('未选择位置')
    return
  }
  
  try {
    // 构建用户关联数据
    const userData = {
      replaceExisting: true, // 替换现有关联
      users: selectedUsers.value.map(userId => ({
        personnelId: userId, // 确保使用正确的字段名PersonnelId
        userType: 0 // 使用人员
      }))
    }
    
    console.log('发送用户关联数据:', userData)
    const response = await locationApi.updateLocationUsers(selectedLocation.value, userData)
    
    if (response.success) {
      ElMessage.success('人员关联设置成功')
      usersDialogVisible.value = false
      // 刷新位置结构
      fetchLocationStructure()
    } else {
      ElMessage.error(response.message || '设置人员关联失败')
    }
  } catch (error) {
    console.error('保存人员关联失败:', error)
    ElMessage.error('保存人员关联失败')
  }
}

// 提交表单
const submitForm = async () => {
  if (!locationForm.value) return
  
  try {
    await locationForm.value.validate()
    
    loading.value = true
    const formData = {
      id: locationFormData.id,
      name: locationFormData.name,
      code: locationFormData.code,
      type: locationFormData.type,
      parentId: locationFormData.parentId,
      description: locationFormData.remark, // 注意这里字段名不一致
      address: locationFormData.address,
      defaultDepartmentId: locationFormData.defaultDepartmentId,
      isActive: true
    }
    
    console.log('提交位置表单:', formData)
    
    let result
    if (dialogType.value === 'edit') {
      // 编辑模式
      result = await locationApi.updateLocation(locationFormData.id, formData)
    } else {
      // 添加模式
      result = await locationApi.createLocation(formData)
    }
    
    if (result.success) {
      ElMessage.success(dialogType.value === 'edit' ? '更新位置成功' : '添加位置成功')
      
      // 保存成功后关联人员
      if ((locationFormData.usageUserIds && locationFormData.usageUserIds.length > 0)) {
        const locationId = dialogType.value === 'edit' ? locationFormData.id : result.data.id
        
        // 构建用户关联数据
        const userData = {
          replaceExisting: true, // 替换现有关联
          users: []
        }
        
        // 添加使用人员
        if (locationFormData.usageUserIds && locationFormData.usageUserIds.length > 0) {
          locationFormData.usageUserIds.forEach(userId => {
            userData.users.push({
              personnelId: userId,
              userType: 0 // 使用人员
            })
          })
        }
        
        if (userData.users.length > 0) {
          try {
            console.log('更新位置关联人员:', userData)
            await locationApi.updateLocationUsers(locationId, userData)
            console.log('位置关联人员更新成功')
          } catch (error) {
            console.error('更新位置关联人员失败:', error)
            ElMessage.warning('位置创建成功，但关联人员失败')
          }
        }
      }
      
      dialogVisible.value = false
      fetchLocationStructure() // 刷新数据
    } else {
      ElMessage.error(result.message || '操作失败')
    }
  } catch (error) {
    console.error('提交表单失败:', error)
    ElMessage.error('表单验证失败，请检查输入')
  } finally {
    loading.value = false
  }
}

// 重置表单
const resetForm = () => {
  console.log('重置表单')
  if (locationForm.value) {
    locationForm.value.resetFields()
  }
  Object.assign(locationFormData, {
    id: undefined,
    name: '',
    code: '',
    type: undefined,
    parentId: null,
    description: '',
    address: '',
    remark: '',
    defaultDepartmentId: null,
    managerId: null,
    usageUserIds: [],
    isActive: true
  })
}

// 获取位置详情
const fetchLocationDetail = async (locationId) => {
  locationFormDetailLoading.value = true
  try {
    const response = await locationApi.getLocationDetail(locationId)
    if (response.success && response.data) {
      // 新API直接返回用户信息，不需要单独获取
      console.log('获取位置详情:', response)
      
      if (response.usageUsers && Array.isArray(response.usageUsers) && response.usageUsers.length > 0) {
        // 设置使用人IDs
        const usageUserIds = response.usageUsers.map(user => user.id || user.personnelId)
        response.data.usageUserIds = usageUserIds
        
        // 为表单展示设置使用人名称文本
        const userNames = response.usageUsers.map(user => {
          return `${user.name || ''}${user.employeeCode ? ` (${user.employeeCode})` : ''}${user.departmentName ? ` - ${user.departmentName}` : ''}`
        })
        
        if (userNames.length > 0) {
          response.data.usersDisplay = userNames.join(', ')
        }
        
        console.log('处理后的使用人信息:', { 
          ids: response.data.usageUserIds, 
          display: response.data.usersDisplay 
        })
      } else {
        // 没有关联用户时设置为空
        response.data.usageUserIds = []
        response.data.usersDisplay = null
      }
      
      // 如果有管理员，设置管理员ID
      if (response.managers && Array.isArray(response.managers) && response.managers.length > 0) {
        response.data.managerId = response.managers[0].id || response.managers[0].personnelId
      }
      
      // 确保有人员选项
      await updatePersonnelOptions()
      
      return response.data
    } else {
      console.error('获取位置详情失败:', response?.message || '未知错误')
      return null
    }
  } catch (error) {
    console.error('获取位置详情出错:', error)
    return null
  } finally {
    locationFormDetailLoading.value = false
  }
}

// 生命周期钩子
onMounted(() => {
  console.log('位置结构页面加载')
  
  // 加载所有需要的数据
  Promise.all([
    fetchLocationStructure(),
    fetchDepartments(),
    fetchPersonnel(),
    updatePersonnelOptions()
  ]).catch(error => {
    console.error('初始化数据加载失败:', error)
  })
})

// 清理资源
const cleanupResources = () => {
  console.log('清理位置结构页面资源')
  // 清空数据
  locationData.value = []
  expandedKeys.value = []
}

// 组件卸载前的清理工作
onBeforeUnmount(() => {
  cleanupResources()
})

// 继承父级位置的使用部门和人员设置
const inheritParentSettings = async (parentId) => {
  try {
    const parentData = await fetchLocationDetail(parentId)
    
    if (parentData) {
      // 如果父级有使用部门，则继承
      if (parentData.defaultDepartmentId) {
        locationFormData.defaultDepartmentId = parentData.defaultDepartmentId
        // 负责人会由watch自动设置
      } else if (parentData.parentId && parentData.type > 1) {
        // 如果父级也没有设置，且父级不是顶级，则继续向上查找
        inheritParentSettings(parentData.parentId)
      }
      
      // 如果父级有使用人，则继承
      if (parentData.usageUserIds && parentData.usageUserIds.length > 0) {
        locationFormData.usageUserIds = [...parentData.usageUserIds]
      }
    }
  } catch (error) {
    console.error('继承父级设置失败:', error)
  }
}

// 监听select下拉框显示事件
const visibleChange = (visible) => {
  if (visible) {
    // 当下拉框打开时，确保已经加载了人员选项
    if (usageUserOptions.value.length === 0) {
      updatePersonnelOptions()
    }
    
    // 确保当前选中的人员在下拉框中可见
    if (locationFormData.usageUserIds && locationFormData.usageUserIds.length > 0) {
      console.log('当前已选择的使用人:', locationFormData.usageUserIds)
    }
  }
}
</script>

<style lang="scss" scoped>
.location-structure-container {
  padding: 20px;
  min-height: 100%;
  background-color: #f5f7fa;
  
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    .page-title {
      margin: 0;
      font-size: 20px;
      font-weight: 600;
      color: #303133;
    }
    
    .page-actions {
      .el-button {
        margin-left: 10px;
      }
    }
  }
  
  .tree-card {
    background-color: #fff;
    border-radius: 4px;
    
    .tree-container {
      padding: 20px;
      
      .location-tree {
        width: 100%;
      }
    }
  }
      
      .tree-node {
        flex: 1;
        display: flex;
        align-items: center;
        padding: 8px 0;
    font-size: 14px;
    width: 100%;
        
        .label {
      font-weight: bold;
      margin-right: 8px;
      color: #303133;
        }
        
        .code {
          color: #909399;
      margin-right: 8px;
        }
        
        .count {
      color: #409EFF;
          font-size: 12px;
      background-color: #ecf5ff;
          padding: 2px 6px;
          border-radius: 10px;
        }
        
        .actions {
          margin-left: auto;
      opacity: 0;
      transition: opacity 0.3s;
      display: flex;
      gap: 4px;
      
      .el-button {
        padding: 4px;
      }
        }
        
        &:hover .actions {
      opacity: 1;
    }
  }
}

// 对话框样式
:deep(.el-dialog) {
  .el-dialog__body {
    padding: 20px;
  }

  .dialog-footer {
    text-align: right;
    margin-top: 20px;
  }
}

:deep(.el-tree-node__content) {
  height: auto;
  padding: 4px 0;
}

:deep(.el-tree) {
  background: none;
}

// 图标按钮样式
.tree-node {
  .actions {
    gap: 5px;
    
    .el-button {
      padding: 6px;
    }
  }
}

// 穿梭框样式
:deep(.el-transfer) {
  display: flex;
  justify-content: center;
  margin: 20px 0;
}
</style> 