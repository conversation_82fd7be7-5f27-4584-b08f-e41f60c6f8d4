# 航空航天级IT资产管理系统 - 实体关系与依赖映射详解

版本: 1.0
最后更新: 2025-03-22

## 目录
1. [文档目的](#1-文档目的)
2. [实体关系模型(ER图)](#2-实体关系模型er图)
3. [类图与接口关系](#3-类图与接口关系)
4. [核心组件依赖图](#4-核心组件依赖图)
5. [插件与核心引擎交互模型](#5-插件与核心引擎交互模型)
6. [服务注册与依赖注入配置](#6-服务注册与依赖注入配置)
7. [实体映射与数据转换](#7-实体映射与数据转换)
8. [基础设施依赖关系](#8-基础设施依赖关系)

## 1. 文档目的

本文档旨在提供航空航天级IT资产管理系统的详细实体关系和依赖映射，作为蓝图文档和API文档的补充，确保系统实施过程中的一致性和完整性。通过详细的图表和说明，帮助开发团队清晰理解系统各组件之间的关系，避免实施过程中出现依赖缺失或结构混乱的问题。

## 2. 实体关系模型(ER图)

### 2.1 核心实体关系图

```
+---------------+       +---------------+       +----------------+
| Asset         |<----->| Location      |<----->| Department     |
+---------------+       +---------------+       +----------------+
| Id            |       | Id            |       | Id             |
| AssetCode     |       | Code          |       | Code           |
| Name          |       | Name          |       | Name           |
| AssetTypeId   |       | Type          |       | ParentId       |
| SerialNumber  |       | ParentId      |       | ManagerId      |
| Model         |       | Path          |       | Description    |
| ...           |       | ...           |       | ...            |
+---------------+       +---------------+       +----------------+
       |                       |                        |
       |                       |                        |
       v                       v                        v
+---------------+       +---------------+       +----------------+
| AssetType     |       | LocationUser  |       | User           |
+---------------+       +---------------+       +----------------+
| Id            |       | Id            |       | Id             |
| Name          |       | LocationId    |       | Username       |
| Code          |       | UserId        |       | PasswordHash   |
| Description   |       | UserType      |       | Name           |
| ...           |       | ...           |       | ...            |
+---------------+       +---------------+       +----------------+
                                                       |
                                                       |
+---------------+       +---------------+              |
| Task          |<----->| PdcaPlan      |              |
+---------------+       +---------------+              |
| Id            |       | Id            |              |
| Title         |       | Title         |              |
| Description   |       | Type          |<-------------+
| TaskType      |       | PlanStartDate |
| ...           |       | ...           |
+---------------+       +---------------+
       |
       |
+---------------+       +---------------+       +----------------+
| PeriodicRule  |       | FaultRecord   |<----->| FaultType      |
+---------------+       +---------------+       +----------------+
| Id            |       | Id            |       | Id             |
| Name          |       | FaultNumber   |       | Name           |
| Frequency     |       | AssetId       |       | Code           |
| ...           |       | ...           |       | ...            |
+---------------+       +---------------+       +----------------+
                               |
                               |
                        +---------------+
                        | ReturnToFactory|
                        +---------------+
                        | Id            |
                        | ReturnNumber  |
                        | FaultRecordId |
                        | ...           |
                        +---------------+
```

### 2.2 详细关系说明

#### Asset与AssetType关系
- 关系类型: 多对一 (Many-to-One)
- 外键: Asset.AssetTypeId → AssetType.Id
- 依赖约束: 级联更新，限制删除
- 每个Asset必须属于一个AssetType
- AssetType可以有多个关联的Asset

#### Asset与Location关系
- 关系类型: 多对一 (Many-to-One)
- 外键: Asset.LocationId → Location.Id
- 依赖约束: 级联更新，限制删除
- 一个Asset可以位于一个Location或无位置
- 一个Location可以包含多个Asset

#### Location与LocationUser关系
- 关系类型: 一对多 (One-to-Many)
- 外键: LocationUser.LocationId → Location.Id
- 依赖约束: 级联更新，级联删除
- 一个Location可以有多个关联用户
- LocationUser表示不同类型的关联(使用人/负责人)

#### Location与Department关系
- 关系类型: 多对一 (Many-to-One)
- 外键: 
  - Location.DepartmentId → Department.Id (使用部门)
  - Location.DefaultDepartmentId → Department.Id (默认使用部门)
- 依赖约束: 级联更新，限制删除
- 一个Location可以关联到一个部门

#### Location层级结构
- 关系类型: 自引用 (Self-Reference)
- 外键: Location.ParentId → Location.Id
- 一个Location可以有一个父级Location和多个子级Location
- Location.Path记录完整层级路径(如"1-3-5")

#### User与Department关系
- 关系类型: 多对一 (Many-to-One)
- 外键: User.DepartmentId → Department.Id
- 依赖约束: 级联更新，限制删除
- 一个User属于一个Department

#### User与Role关系(通过UserRole)
- 关系类型: 多对多 (Many-to-Many)
- 关联表: UserRoles
- 外键: 
  - UserRole.UserId → User.Id
  - UserRole.RoleId → Role.Id
- 依赖约束: 级联更新，级联删除
- 一个User可以有多个Role
- 一个Role可以分配给多个User

#### FaultRecord与Asset关系
- 关系类型: 多对一 (Many-to-One)
- 外键: FaultRecord.AssetId → Asset.Id
- 依赖约束: 级联更新，级联删除
- 一个Asset可以有多个FaultRecord
- 每个FaultRecord必须关联一个Asset

#### ReturnToFactory与FaultRecord关系
- 关系类型: 多对一 (Many-to-One)
- 外键: ReturnToFactory.FaultRecordId → FaultRecord.Id
- 依赖约束: 级联更新，限制删除
- 一个FaultRecord可以有多个ReturnToFactory记录

#### Task与User关系
- 关系类型: 多对一 (Many-to-One)
- 外键: 
  - Task.AssignedToId → User.Id (任务分配人)
  - Task.CreatedById → User.Id (任务创建人)
- 依赖约束: 级联更新，限制删除
- 一个Task可以分配给一个User
- 一个User可以创建多个Task

#### Task与PeriodicRule关系
- 关系类型: 多对一 (Many-to-One)
- 外键: Task.PeriodicRuleId → PeriodicRule.Id
- 依赖约束: 级联更新，限制删除
- 一个PeriodicRule可以生成多个Task

#### Task与PdcaPlan关系
- 关系类型: 多对一 (Many-to-One)
- 外键: Task.PdcaPlanId → PdcaPlan.Id
- 依赖约束: 级联更新，限制删除
- 一个PdcaPlan可以包含多个Task

### 2.3 实体约束和验证规则

#### Asset实体约束
- AssetCode: 唯一、非空、最大长度50
- Name: 非空、最大长度100
- AssetTypeId: 非空、大于0
- SerialNumber: 最大长度100
- LocationId: 可为空

#### Location实体约束
- Code: 唯一、非空、最大长度50
- Name: 非空、最大长度100
- Type: 非空，范围(0-3)
- Path: 非空、最大长度300

#### User实体约束
- Username: 唯一、非空、最大长度50
- PasswordHash: 非空、最大长度100
- Name: 非空、最大长度50
- Email: 非空、最大长度100、有效Email格式

#### Task实体约束
- Title: 非空、最大长度200
- TaskType: 非空，范围(0-2)
- Status: 非空，范围(0-3)
- CreatedById: 非空、大于0
- AssignedToId: 可为空

## 3. 类图与接口关系

### 3.1 核心引擎类图

```
+------------------+     +------------------+     +------------------+
| ICoreEngine      |<----|  CoreEngine      |---->| IPluginManager   |
+------------------+     +------------------+     +------------------+
| Initialize()     |     | Initialize()     |     | LoadPlugins()    |
| Start()          |     | Start()          |     | GetPlugin<T>()   |
| Stop()           |     | Stop()           |     | ...              |
| ...              |     | ...              |     |                  |
+------------------+     +------------------+     +------------------+
        ^                       |                        ^
        |                       |                        |
        |                       v                        |
+------------------+     +------------------+     +------------------+
| IEventManager    |<----| EventManager     |---->| PluginManager    |
+------------------+     +------------------+     +------------------+
| Subscribe()      |     | Subscribe()      |     | LoadPlugins()    |
| Publish()        |     | Publish()        |     | GetPlugin<T>()   |
| ...              |     | ...              |     | ...              |
+------------------+     +------------------+     +------------------+
                                |
                                v
                         +------------------+
                         | ResilienceManager |
                         +------------------+
                         | QueueOperation() |
                         | ProcessQueue()   |
                         | ...              |
                         +------------------+
```

### 3.2 插件接口层次结构

```
+------------------+
| IPlugin          |
+------------------+
| Initialize()     |
| Start()          |
| Stop()           |
| GetMetadata()    |
+------------------+
        ^
        |
+-------+-------+
|               |
+------------------+     +------------------+
| IAssetPlugin     |     | ILocationPlugin  |
+------------------+     +------------------+
| GetAssetById()   |     | GetLocations()   |
| CreateAsset()    |     | CreateLocation() |
| ...              |     | ...              |
+------------------+     +------------------+
        ^                        ^
        |                        |
+------------------+     +------------------+
| AssetMgmtPlugin  |     | LocationMgmtPlugin|
+------------------+     +------------------+
| GetAssetById()   |     | GetLocations()   |
| CreateAsset()    |     | CreateLocation() |
| ...              |     | ...              |
+------------------+     +------------------+
```

### 3.3 服务层接口关系

```
+------------------+     +------------------+     +------------------+
| IAssetService    |     | ILocationService |     | IPurchaseService |
+------------------+     +------------------+     +------------------+
| GetAssetById()   |     | GetLocations()   |     | CreatePurchase() |
| CreateAsset()    |     | CreateLocation() |     | ConfirmDelivery()|
| ...              |     | ...              |     | ...              |
+------------------+     +------------------+     +------------------+
        ^                        ^                        ^
        |                        |                        |
+------------------+     +------------------+     +------------------+
| AssetService     |     | LocationService  |     | PurchaseService  |
+------------------+     +------------------+     +------------------+
| GetAssetById()   |     | GetLocations()   |     | CreatePurchase() |
| CreateAsset()    |     | CreateLocation() |     | ConfirmDelivery()|
| ...              |     | ...              |     | ...              |
+------------------+     +------------------+     +------------------+
        |                        |                        |
        v                        v                        v
+------------------+     +------------------+     +------------------+
| IAssetRepository |     | ILocationRepo    |     | IPurchaseRepo    |
+------------------+     +------------------+     +------------------+
| GetById()        |     | GetById()        |     | GetById()        |
| Create()         |     | Create()         |     | Create()         |
| ...              |     | ...              |     | ...              |
+------------------+     +------------------+     +------------------+
        ^                        ^                        ^
        |                        |                        |
+------------------+     +------------------+     +------------------+
| AssetRepository  |     | LocationRepo     |     | PurchaseRepo     |
+------------------+     +------------------+     +------------------+
| GetById()        |     | GetById()        |     | GetById()        |
| Create()         |     | Create()         |     | ...              |
| ...              |     | ...              |     |                  |
+------------------+     +------------------+     +------------------+
```

### 3.4 数据访问层关系

```
+------------------+
| AppDbContext     |
+------------------+
| DbSet<Asset>     |
| DbSet<Location>  |
| DbSet<User>      |
| ...              |
| SaveChanges()    |
+------------------+
        ^
        |
        |
+-------+--------+--------+--------+
|       |        |        |        |
v       v        v        v        v
+-----+ +-----+ +-----+ +-----+ +-----+
|Asset| |Locat| |User | |Task | |Fault|
|Repo | |Repo | |Repo | |Repo | |Repo |
+-----+ +-----+ +-----+ +-----+ +-----+
```

## 4. 核心组件依赖图

```
+---------------------+
|   Program.cs        |
+---------------------+
| - Configure Services|
| - Configure App     |
| - Run Application   |
+---------------------+
          |
          v
+---------------------+       +---------------------+
|   CoreEngine        |<----->|   PluginManager     |
+---------------------+       +---------------------+
| - Initialize()      |       | - LoadPlugins()     |
| - Start()           |       | - GetPlugin<T>()    |
| - Stop()            |       | - ...               |
+---------------------+       +---------------------+
          |                             |
          v                             v
+---------------------+       +---------------------+
|   EventManager      |<----->|   Plugin Instances  |
+---------------------+       +---------------------+
| - Subscribe()       |       | - AssetMgmtPlugin   |
| - Publish()         |       | - LocationMgmtPlugin|
| - ...               |       | - ...               |
+---------------------+       +---------------------+
          |                             |
          v                             v
+---------------------+       +---------------------+
|   Service Layer     |<----->|   Repository Layer  |
+---------------------+       +---------------------+
| - AssetService      |       | - AssetRepository   |
| - LocationService   |       | - LocationRepository|
| - ...               |       | - ...               |
+---------------------+       +---------------------+
                                        |
                                        v
                              +---------------------+
                              |   DbContext         |
                              +---------------------+
                              | - Connection Mgmt   |
                              | - Entity Tracking   |
                              | - ...               |
                              +---------------------+
```

## 5. 插件与核心引擎交互模型

### 5.1 插件加载流程

```
Program.cs
    |
    v
CoreEngine.Initialize()
    |
    v
PluginManager.LoadPlugins()
    |
    v
foreach plugin in pluginDirectory:
    |
    v
    Load plugin assembly
        |
        v
        Find types implementing IPlugin
            |
            v
            Create plugin instance
                |
                v
                Register plugin instance
                    |
                    v
                    Plugin.Initialize()
```

### 5.2 事件流通机制

```
Plugin A                  EventManager                    Plugin B
    |                           |                             |
    |-- Publish Event "X" ----->|                             |
    |                           |-- Route Event -------------->|
    |                           |                             |
    |                           |<-- Return Result (async) ---|
    |<-- Aggregated Results ----|                             |
    |                           |                             |
```

### 5.3 典型插件依赖关系

#### AssetManagementPlugin依赖
- CoreEngine
- EventManager
- IAssetRepository
- ILocationRepository
- IAssetTypeRepository
- IUserRepository

#### LocationManagementPlugin依赖
- CoreEngine
- EventManager
- ILocationRepository
- IDepartmentRepository
- IUserRepository

#### TaskManagementPlugin依赖
- CoreEngine
- EventManager
- ITaskRepository
- IPeriodicRuleRepository
- IPdcaPlanRepository
- IUserRepository

## 6. 服务注册与依赖注入配置

### 6.1 核心服务注册

以下是Program.cs中的服务注册代码:

```csharp
// 注册核心引擎和基础设施
services.AddSingleton<ICoreEngine, CoreEngine>();
services.AddSingleton<IPluginManager, PluginManager>();
services.AddSingleton<IEventManager, EventManager>();
services.AddSingleton<IResilienceManager, ResilienceManager>();

// 注册数据访问层
services.AddDbContext<AppDbContext>(options =>
    options.UseMySql(
        Configuration.GetConnectionString("DefaultConnection"),
        ServerVersion.AutoDetect(Configuration.GetConnectionString("DefaultConnection")),
        b => b.MigrationsAssembly("AerospaceITAM.Infrastructure")));

// 注册仓储
services.AddScoped<IAssetRepository, AssetRepository>();
services.AddScoped<ILocationRepository, LocationRepository>();
services.AddScoped<IUserRepository, UserRepository>();
services.AddScoped<ITaskRepository, TaskRepository>();
services.AddScoped<IPurchaseRepository, PurchaseRepository>();
services.AddScoped<IFaultRepository, FaultRepository>();
services.AddScoped<IEventRepository, EventRepository>();

// 注册核心服务
services.AddScoped<IAssetService, AssetService>();
services.AddScoped<ILocationService, LocationService>();
services.AddScoped<IUserService, UserService>();
services.AddScoped<ITaskService, TaskService>();
services.AddScoped<IPurchaseService, PurchaseService>();
services.AddScoped<IFaultService, FaultService>();

// 注册配置服务
services.AddSingleton<ILocationHierarchyLoader, LocationHierarchyLoader>();
services.AddSingleton<IConfigurationManager, ConfigurationManager>();

// 注册安全服务
services.AddScoped<IPasswordHasher, PasswordHasher>();
services.AddScoped<IPermissionManager, PermissionManager>();
services.AddScoped<IAuthService, AuthService>();

// 注册跨域策略
services.AddCors(options =>
{
    options.AddPolicy("CorsPolicy", builder =>
        builder.AllowAnyOrigin()
               .AllowAnyMethod()
               .AllowAnyHeader());
});

// 注册身份验证
services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddJwtBearer(options =>
    {
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuer = true,
            ValidateAudience = true,
            ValidateLifetime = true,
            ValidateIssuerSigningKey = true,
            ValidIssuer = Configuration["JwtSettings:Issuer"],
            ValidAudience = Configuration["JwtSettings:Audience"],
            IssuerSigningKey = new SymmetricSecurityKey(
                Encoding.UTF8.GetBytes(Configuration["JwtSettings:SecretKey"]))
        };
    });

// 注册授权策略
services.AddAuthorization(options =>
{
    // 资产管理权限
    options.AddPolicy("asset:view", policy => policy.RequireClaim("permission", "asset:view"));
    options.AddPolicy("asset:create", policy => policy.RequireClaim("permission", "asset:create"));
    options.AddPolicy("asset:update", policy => policy.RequireClaim("permission", "asset:update"));
    options.AddPolicy("asset:delete", policy => policy.RequireClaim("permission", "asset:delete"));
    
    // 位置管理权限
    options.AddPolicy("location:view", policy => policy.RequireClaim("permission", "location:view"));
    options.AddPolicy("location:create", policy => policy.RequireClaim("permission", "location:create"));
    options.AddPolicy("location:update", policy => policy.RequireClaim("permission", "location:update"));
    options.AddPolicy("location:delete", policy => policy.RequireClaim("permission", "location:delete"));
    
    // 其他权限...
});

// 注册MVC和API控制器
services.AddControllers()
    .AddJsonOptions(options =>
    {
        options.JsonSerializerOptions.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
        options.JsonSerializerOptions.WriteIndented = true;
    });

// 注册Swagger
services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new OpenApiInfo { Title = "Aerospace ITAM API", Version = "v1" });
    c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
    {
        Description = "JWT Authorization header using the Bearer scheme",
        Type = SecuritySchemeType.Http,
        Scheme = "bearer"
    });
    c.AddSecurityRequirement(new OpenApiSecurityRequirement
    {
        {
            new OpenApiSecurityScheme
            {
                Reference = new OpenApiReference { Type = ReferenceType.SecurityScheme, Id = "Bearer" }
            },
            Array.Empty<string>()
        }
    });
});
```

### 6.2 服务依赖关系和生命周期

| 服务类型 | 实现类 | 生命周期 | 主要依赖项 |
|---------|--------|---------|-----------|
| ICoreEngine | CoreEngine | Singleton | IPluginManager, IEventManager, IConfigurationManager |
| IPluginManager | PluginManager | Singleton | ICoreEngine, IEventManager |
| IEventManager | EventManager | Singleton | - |
| IResilienceManager | ResilienceManager | Singleton | IEventManager |
| IAssetService | AssetService | Scoped | IAssetRepository, IEventManager |
| ILocationService | LocationService | Scoped | ILocationRepository, IEventManager |
| ITaskService | TaskService | Scoped | ITaskRepository, IEventManager |
| IPurchaseService | PurchaseService | Scoped | IPurchaseRepository, IEventManager |
| IFaultService | FaultService | Scoped | IFaultRepository, IEventManager |
| IUserService | UserService | Scoped | IUserRepository, IPasswordHasher |
| IAssetRepository | AssetRepository | Scoped | AppDbContext |
| ILocationRepository | LocationRepository | Scoped | AppDbContext |
| ITaskRepository | TaskRepository | Scoped | AppDbContext |
| IPurchaseRepository | PurchaseRepository | Scoped | AppDbContext |
| IFaultRepository | FaultRepository | Scoped | AppDbContext |
| IUserRepository | UserRepository | Scoped | AppDbContext |
| IEventRepository | EventRepository | Scoped | EventStoreDbContext |
| IAuthService | AuthService | Scoped | IUserRepository, IPasswordHasher, TokenConfiguration |

## 7. 实体映射与数据转换

### 7.1 实体与DTO映射关系

```csharp
// 资产映射示例
public class AssetProfile : Profile
{
    public AssetProfile()
    {
        // Entity -> DTO
        CreateMap<Asset, AssetDto>()
            .ForMember(dest => dest.AssetType, opt => opt.MapFrom(src => src.AssetType.Name))
            .ForMember(dest => dest.Location, opt => opt.MapFrom(src => src.Location.Name))
            .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.Status.ToString()));
        
        // DTO -> Entity
        CreateMap<AssetDto, Asset>()
            .ForMember(dest => dest.AssetType, opt => opt.Ignore())
            .ForMember(dest => dest.Location, opt => opt.Ignore());
        
        // Create DTO -> Entity
        CreateMap<CreateAssetDto, Asset>()
            .ForMember(dest => dest.Id, opt => opt.Ignore())
            .ForMember(dest => dest.AssetType, opt => opt.Ignore())
            .ForMember(dest => dest.Location, opt => opt.Ignore())
            .ForMember(dest => dest.CreatedAt, opt => opt.MapFrom(_ => DateTime.UtcNow))
            .ForMember(dest => dest.UpdatedAt, opt => opt.MapFrom(_ => DateTime.UtcNow));
        
        // Update DTO -> Entity
        CreateMap<UpdateAssetDto, Asset>()
            .ForMember(dest => dest.Id, opt => opt.Ignore())
            .ForMember(dest => dest.AssetCode, opt => opt.Ignore())
            .ForMember(dest => dest.AssetType, opt => opt.Ignore())
            .ForMember(dest => dest.Location, opt => opt.Ignore())
            .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.UpdatedAt, opt => opt.MapFrom(_ => DateTime.UtcNow));
    }
}
```

### 7.2 AutoMapper配置与注册

```csharp
// Program.cs
// 注册AutoMapper
services.AddAutoMapper(cfg =>
{
    cfg.AddProfile<AssetProfile>();
    cfg.AddProfile<LocationProfile>();
    cfg.AddProfile<TaskProfile>();
    cfg.AddProfile<PurchaseProfile>();
    cfg.AddProfile<FaultProfile>();
    cfg.AddProfile<UserProfile>();
});
```

### 7.3 常用映射操作

#### 实体到DTO
```csharp
public async Task<AssetDto> GetAssetByIdAsync(int id)
{
    var asset = await _assetRepository.GetByIdAsync(id);
    if (asset == null)
    {
        return null;
    }
    
    return _mapper.Map<AssetDto>(asset);
}
```

#### DTO到实体
```csharp
public async Task<AssetDto> CreateAssetAsync(CreateAssetDto createDto)
{
    var asset = _mapper.Map<Asset>(createDto);
    
    // 生成资产编码
    asset.AssetCode = await _assetCodeGenerator.GenerateAssetCodeAsync(createDto.AssetTypeId);
    
    // 保存到数据库
    await _assetRepository.AddAsync(asset);
    await _assetRepository.SaveChangesAsync();
    
    // 返回完整DTO
    return _mapper.Map<AssetDto>(asset);
}
```

#### 更新映射
```csharp
public async Task<AssetDto> UpdateAssetAsync(int id, UpdateAssetDto updateDto)
{
    var asset = await _assetRepository.GetByIdAsync(id);
    if (asset == null)
    {
        return null;
    }
    
    // 更新实体属性
    _mapper.Map(updateDto, asset);
    
    // 保存变更
    await _assetRepository.UpdateAsync(asset);
    await _assetRepository.SaveChangesAsync();
    
    // 返回更新后的DTO
    return _mapper.Map<AssetDto>(asset);
}
```

## 8. 基础设施依赖关系

### 8.1 数据库上下文配置

```csharp
public class AppDbContext : DbContext
{
    public AppDbContext(DbContextOptions<AppDbContext> options) : base(options)
    {
    }
    
    public DbSet<Asset> Assets { get; set; }
    public DbSet<AssetType> AssetTypes { get; set; }
    public DbSet<Location> Locations { get; set; }
    public DbSet<Department> Departments { get; set; }
    public DbSet<User> Users { get; set; }
    public DbSet<Role> Roles { get; set; }
    public DbSet<UserRole> UserRoles { get; set; }
    public DbSet<Task> Tasks { get; set; }
    public DbSet<PeriodicRule> PeriodicRules { get; set; }
    public DbSet<PdcaPlan> PdcaPlans { get; set; }
    public DbSet<FaultRecord> FaultRecords { get; set; }
    public DbSet<FaultType> FaultTypes { get; set; }
    public DbSet<PurchaseOrder> PurchaseOrders { get; set; }
    public DbSet<PurchaseItem> PurchaseItems { get; set; }
    public DbSet<Supplier> Suppliers { get; set; }
    public DbSet<ReturnToFactory> ReturnToFactories { get; set; }
    public DbSet<AuditLog> AuditLogs { get; set; }
    
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);
        
        // 应用实体配置
        modelBuilder.ApplyConfiguration(new AssetConfiguration());
        modelBuilder.ApplyConfiguration(new AssetTypeConfiguration());
        modelBuilder.ApplyConfiguration(new LocationConfiguration());
        modelBuilder.ApplyConfiguration(new DepartmentConfiguration());
        modelBuilder.ApplyConfiguration(new UserConfiguration());
        modelBuilder.ApplyConfiguration(new RoleConfiguration());
        modelBuilder.ApplyConfiguration(new UserRoleConfiguration());
        modelBuilder.ApplyConfiguration(new TaskConfiguration());
        modelBuilder.ApplyConfiguration(new PeriodicRuleConfiguration());
        modelBuilder.ApplyConfiguration(new PdcaPlanConfiguration());
        modelBuilder.ApplyConfiguration(new FaultRecordConfiguration());
        modelBuilder.ApplyConfiguration(new FaultTypeConfiguration());
        modelBuilder.ApplyConfiguration(new PurchaseOrderConfiguration());
        modelBuilder.ApplyConfiguration(new PurchaseItemConfiguration());
        modelBuilder.ApplyConfiguration(new SupplierConfiguration());
        modelBuilder.ApplyConfiguration(new ReturnToFactoryConfiguration());
        modelBuilder.ApplyConfiguration(new AuditLogConfiguration());
    }
}
```

### 8.2 事件存储配置

```csharp
public class EventStoreDbContext : DbContext
{
    public EventStoreDbContext(DbContextOptions<EventStoreDbContext> options) : base(options)
    {
    }
    
    public DbSet<EventRecord> Events { get; set; }
    
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);
        
        modelBuilder.Entity<EventRecord>(entity =>
        {
            entity.ToTable("events");
            entity.HasKey(e => e.Id);
            entity.Property(e => e.EntityId).IsRequired();
            entity.Property(e => e.EntityType).IsRequired().HasMaxLength(100);
            entity.Property(e => e.EventType).IsRequired().HasMaxLength(100);
            entity.Property(e => e.Data).IsRequired();
            entity.Property(e => e.Timestamp).IsRequired();
            entity.Property(e => e.UserId);
            
            entity.HasIndex(e => new { e.EntityType, e.EntityId });
            entity.HasIndex(e => e.Timestamp);
            entity.HasIndex(e => e.EventType);
        });
    }
}
```

### 8.3 实体配置示例

```csharp
public class AssetConfiguration : IEntityTypeConfiguration<Asset>
{
    public void Configure(EntityTypeBuilder<Asset> builder)
    {
        builder.ToTable("assets");
        
        builder.HasKey(e => e.Id);
        
        builder.Property(e => e.AssetCode)
            .IsRequired()
            .HasMaxLength(50);
        
        builder.HasIndex(e => e.AssetCode)
            .IsUnique();
        
        builder.Property(e => e.Name)
            .IsRequired()
            .HasMaxLength(100);
        
        builder.Property(e => e.SerialNumber)
            .HasMaxLength(100);
        
        builder.HasIndex(e => e.SerialNumber);
        
        builder.Property(e => e.Model)
            .HasMaxLength(100);
        
        builder.Property(e => e.Brand)
            .HasMaxLength(100);
        
        builder.Property(e => e.Status)
            .IsRequired();
        
        builder.HasIndex(e => e.Status);
        
        builder.Property(e => e.Notes)
            .HasMaxLength(500);
        
        builder.Property(e => e.CreatedAt)
            .IsRequired();
        
        builder.Property(e => e.UpdatedAt)
            .IsRequired();
        
        // 关系配置
        builder.HasOne(e => e.AssetType)
            .WithMany(e => e.Assets)
            .HasForeignKey(e => e.AssetTypeId)
            .OnDelete(DeleteBehavior.Restrict);
        
        builder.HasOne(e => e.Location)
            .WithMany(e => e.Assets)
            .HasForeignKey(e => e.LocationId)
            .OnDelete(DeleteBehavior.Restrict);
    }
}
```

通过本文档提供的详细实体关系和依赖映射，开发团队可以清晰理解系统各组件之间的关系，避免实施过程中出现依赖缺失或结构混乱的问题。
