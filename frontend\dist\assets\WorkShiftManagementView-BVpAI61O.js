import{_ as e,r as a,ad as l,z as t,b as i,d as s,e as d,w as o,E as r,a as u,m as n,o as m,p as c,f,aG as p,l as v,k as h,t as g,$ as y,b7 as w,F as _,h as b,a9 as T}from"./index-CG5lHOPO.js";import{w as V}from"./workShift-Ce3ThpoM.js";const C={class:"shift-management-container"},k={class:"page-header"},D={class:"header-right"},U={class:"card-header"},N={class:"dialog-footer"},x={key:0},A={class:"assignment-header"},H={class:"shift-info"},z={class:"add-user-section"},O={class:"assigned-users-section"},S={class:"dialog-footer"},I=e({__name:"WorkShiftManagementView",setup(e){const I=a(!1),q=a([]),L=a(!1),j=a(!1),E=a("create"),$=a(null),P=a(!1),F=a(),G=a([]),M=a([]),R=a(null),W=a(!1),B=a(!1),J=a(!1),K=l({effectiveDate:new Date,notes:""}),Q=l({shiftName:"",shiftCode:"",shiftType:"Day",startTime:"",endTime:"",taskClaimTime:"",isOvernight:!1,description:"",isActive:!0}),X={shiftName:[{required:!0,message:"请输入班次名称",trigger:"blur"}],shiftCode:[{required:!0,message:"请输入班次代码",trigger:"blur"}],shiftType:[{required:!0,message:"请选择班次类型",trigger:"change"}],startTime:[{required:!0,message:"请选择开始时间",trigger:"change"}],endTime:[{required:!0,message:"请选择结束时间",trigger:"change"}],taskClaimTime:[{required:!0,message:"请选择任务领取时间",trigger:"change"}]},Y=async()=>{var e;I.value=!0;try{const a=await V.getAllShifts();a.success?q.value=(null==(e=a.data)?void 0:e.map((e=>({...e,statusLoading:!1}))))||[]:r.error("获取班次列表失败")}catch(a){r.error("获取班次列表失败")}finally{I.value=!1}},Z=()=>{E.value="create",ie(),L.value=!0},ee=async()=>{var e,a;if($.value){W.value=!0,B.value=!0;try{const l=await V.getShiftAssignments($.value.shiftId);l.success&&(G.value=(null==(e=l.data)?void 0:e.map((e=>({...e,removing:!1}))))||[]);const t=await V.getAllUsers();if(t.success){const e=(null==(a=t.data)?void 0:a.items)||t.data||[],l=G.value.map((e=>e.userId));M.value=e.filter((e=>!l.includes(e.id)))}}catch(l){r.error("加载用户分配数据失败")}finally{W.value=!1,B.value=!1}}},ae=async()=>{if(R.value&&$.value){J.value=!0;try{const e={userId:R.value,shiftId:$.value.shiftId,effectiveDate:K.effectiveDate,notes:K.notes||""},a=await V.assignUserToShift(e);a.success?(r.success("用户分配成功"),R.value=null,K.notes="",K.effectiveDate=new Date,await ee()):r.error(a.message||"用户分配失败")}catch(e){r.error("用户分配失败")}finally{J.value=!1}}},le=async()=>{if(F.value)try{if(await F.value.validate(),P.value=!0,"create"===E.value){const e=await V.createShift(Q);e.success?(r.success("班次创建成功"),L.value=!1,Y()):r.error(e.message||"班次创建失败")}else r.success("班次更新成功"),L.value=!1,Y()}catch(e){}finally{P.value=!1}},te=()=>{L.value=!1,ie()},ie=()=>{Object.assign(Q,{shiftName:"",shiftCode:"",shiftType:"Day",startTime:"",endTime:"",taskClaimTime:"",isOvernight:!1,description:"",isActive:!0}),$.value=null,F.value&&F.value.clearValidate()},se=e=>({Day:"白班",Night:"夜班",Evening:"中班"}[e]||e),de=e=>{if(!e)return"";return new Date(e).toLocaleDateString("zh-CN")},oe=e=>({Permanent:"永久",Temporary:"临时",Substitute:"替代"}[e]||e);return t((()=>{Y()})),(e,a)=>{const l=u("el-icon"),t=u("el-button"),ie=u("el-table-column"),re=u("el-tag"),ue=u("el-switch"),ne=u("el-table"),me=u("el-card"),ce=u("el-input"),fe=u("el-form-item"),pe=u("el-option"),ve=u("el-select"),he=u("el-time-picker"),ge=u("el-form"),ye=u("el-dialog"),we=u("el-col"),_e=u("el-date-picker"),be=u("el-row"),Te=n("loading");return m(),i("div",C,[s("div",k,[a[15]||(a[15]=s("div",{class:"header-left"},[s("h1",null,"班次管理"),s("p",{class:"subtitle"},"管理工作班次设置和用户分配")],-1)),s("div",D,[d(t,{type:"primary",onClick:Z},{default:o((()=>[d(l,null,{default:o((()=>[d(f(p))])),_:1}),a[14]||(a[14]=c(" 新增班次 "))])),_:1})])]),d(me,{class:"shift-list-card"},{header:o((()=>[s("div",U,[a[17]||(a[17]=s("span",null,"班次列表",-1)),d(t,{text:"",onClick:Y},{default:o((()=>[d(l,null,{default:o((()=>[d(f(w))])),_:1}),a[16]||(a[16]=c(" 刷新 "))])),_:1})])])),default:o((()=>[v((m(),h(ne,{data:q.value,stripe:"",style:{width:"100%"}},{default:o((()=>[d(ie,{prop:"shiftName",label:"班次名称",width:"150"}),d(ie,{prop:"shiftCode",label:"班次代码",width:"120"}),d(ie,{prop:"shiftType",label:"班次类型",width:"120"},{default:o((({row:e})=>{return[d(re,{type:(a=e.shiftType,{Day:"success",Night:"info",Evening:"warning"}[a]||"primary")},{default:o((()=>[c(g(se(e.shiftType)),1)])),_:2},1032,["type"])];var a})),_:1}),d(ie,{label:"工作时间",width:"200"},{default:o((({row:e})=>[s("span",null,g(e.startTime)+" - "+g(e.endTime),1),e.isOvernight?(m(),h(re,{key:0,size:"small",type:"warning",style:{"margin-left":"8px"}},{default:o((()=>a[18]||(a[18]=[c(" 跨夜 ")]))),_:1})):y("",!0)])),_:1}),d(ie,{prop:"taskClaimTime",label:"任务领取时间",width:"150"}),d(ie,{prop:"description",label:"描述","show-overflow-tooltip":""}),d(ie,{label:"状态",width:"100"},{default:o((({row:e})=>[d(ue,{modelValue:e.isActive,"onUpdate:modelValue":a=>e.isActive=a,onChange:a=>(async e=>{e.statusLoading=!0;try{r.success("班次已"+(e.isActive?"启用":"禁用"))}catch(a){e.isActive=!e.isActive,r.error("状态更新失败")}finally{e.statusLoading=!1}})(e),loading:e.statusLoading},null,8,["modelValue","onUpdate:modelValue","onChange","loading"])])),_:1}),d(ie,{label:"操作",width:"200",fixed:"right"},{default:o((({row:e})=>[d(t,{size:"small",onClick:a=>{return l=e,E.value="edit",$.value=l,Object.assign(Q,{shiftName:l.shiftName,shiftCode:l.shiftCode,shiftType:l.shiftType,startTime:l.startTime,endTime:l.endTime,taskClaimTime:l.taskClaimTime,isOvernight:l.isOvernight,description:l.description,isActive:l.isActive}),void(L.value=!0);var l}},{default:o((()=>a[19]||(a[19]=[c("编辑")]))),_:2},1032,["onClick"]),d(t,{size:"small",onClick:a=>{return l=e,$.value=l,void(j.value=!0);var l}},{default:o((()=>a[20]||(a[20]=[c("用户分配")]))),_:2},1032,["onClick"]),d(t,{size:"small",type:"danger",onClick:a=>(async e=>{try{await T.confirm(`确定要删除班次"${e.shiftName}"吗？`,"确认删除",{type:"warning"}),r.success("删除成功"),Y()}catch(a){}})(e),disabled:e.isActive},{default:o((()=>a[21]||(a[21]=[c(" 删除 ")]))),_:2},1032,["onClick","disabled"])])),_:1})])),_:1},8,["data"])),[[Te,I.value]])])),_:1}),d(ye,{modelValue:L.value,"onUpdate:modelValue":a[8]||(a[8]=e=>L.value=e),title:"create"===E.value?"新增班次":"编辑班次",width:"600px",onClose:te},{footer:o((()=>[s("div",N,[d(t,{onClick:te},{default:o((()=>a[23]||(a[23]=[c("取消")]))),_:1}),d(t,{type:"primary",onClick:le,loading:P.value},{default:o((()=>a[24]||(a[24]=[c(" 确定 ")]))),_:1},8,["loading"])])])),default:o((()=>[d(ge,{ref_key:"formRef",ref:F,model:Q,rules:X,"label-width":"120px"},{default:o((()=>[d(fe,{label:"班次名称",prop:"shiftName"},{default:o((()=>[d(ce,{modelValue:Q.shiftName,"onUpdate:modelValue":a[0]||(a[0]=e=>Q.shiftName=e),placeholder:"请输入班次名称"},null,8,["modelValue"])])),_:1}),d(fe,{label:"班次代码",prop:"shiftCode"},{default:o((()=>[d(ce,{modelValue:Q.shiftCode,"onUpdate:modelValue":a[1]||(a[1]=e=>Q.shiftCode=e),placeholder:"请输入班次代码"},null,8,["modelValue"])])),_:1}),d(fe,{label:"班次类型",prop:"shiftType"},{default:o((()=>[d(ve,{modelValue:Q.shiftType,"onUpdate:modelValue":a[2]||(a[2]=e=>Q.shiftType=e),placeholder:"请选择班次类型",style:{width:"100%"}},{default:o((()=>[d(pe,{label:"白班",value:"Day"}),d(pe,{label:"夜班",value:"Night"}),d(pe,{label:"中班",value:"Evening"})])),_:1},8,["modelValue"])])),_:1}),d(fe,{label:"开始时间",prop:"startTime"},{default:o((()=>[d(he,{modelValue:Q.startTime,"onUpdate:modelValue":a[3]||(a[3]=e=>Q.startTime=e),format:"HH:mm:ss","value-format":"HH:mm:ss",placeholder:"选择开始时间",style:{width:"100%"}},null,8,["modelValue"])])),_:1}),d(fe,{label:"结束时间",prop:"endTime"},{default:o((()=>[d(he,{modelValue:Q.endTime,"onUpdate:modelValue":a[4]||(a[4]=e=>Q.endTime=e),format:"HH:mm:ss","value-format":"HH:mm:ss",placeholder:"选择结束时间",style:{width:"100%"}},null,8,["modelValue"])])),_:1}),d(fe,{label:"任务领取时间",prop:"taskClaimTime"},{default:o((()=>[d(he,{modelValue:Q.taskClaimTime,"onUpdate:modelValue":a[5]||(a[5]=e=>Q.taskClaimTime=e),format:"HH:mm:ss","value-format":"HH:mm:ss",placeholder:"选择任务领取时间",style:{width:"100%"}},null,8,["modelValue"])])),_:1}),d(fe,{label:"跨夜班次"},{default:o((()=>[d(ue,{modelValue:Q.isOvernight,"onUpdate:modelValue":a[6]||(a[6]=e=>Q.isOvernight=e)},null,8,["modelValue"]),a[22]||(a[22]=s("span",{style:{"margin-left":"10px",color:"#909399","font-size":"12px"}}," 如果班次跨越午夜，请开启此选项 ",-1))])),_:1}),d(fe,{label:"描述"},{default:o((()=>[d(ce,{modelValue:Q.description,"onUpdate:modelValue":a[7]||(a[7]=e=>Q.description=e),type:"textarea",rows:3,placeholder:"请输入班次描述"},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue","title"]),d(ye,{modelValue:j.value,"onUpdate:modelValue":a[13]||(a[13]=e=>j.value=e),title:"用户分配",width:"900px",onOpen:ee},{footer:o((()=>[s("div",S,[d(t,{onClick:a[12]||(a[12]=e=>j.value=!1)},{default:o((()=>a[29]||(a[29]=[c("关闭")]))),_:1})])])),default:o((()=>[$.value?(m(),i("div",x,[s("div",A,[s("h4",null,g($.value.shiftName)+" - 用户分配",1),s("p",H,"班次时间: "+g($.value.startTime)+" - "+g($.value.endTime),1)]),s("div",z,[a[26]||(a[26]=s("h5",null,"添加用户到班次",-1)),d(be,{gutter:16},{default:o((()=>[d(we,{span:12},{default:o((()=>[d(ve,{modelValue:R.value,"onUpdate:modelValue":a[9]||(a[9]=e=>R.value=e),placeholder:"选择用户",filterable:"",clearable:"",style:{width:"100%"},loading:B.value},{default:o((()=>[(m(!0),i(_,null,b(M.value,(e=>(m(),h(pe,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue","loading"])])),_:1}),d(we,{span:6},{default:o((()=>[d(_e,{modelValue:K.effectiveDate,"onUpdate:modelValue":a[10]||(a[10]=e=>K.effectiveDate=e),type:"date",placeholder:"生效日期",style:{width:"100%"}},null,8,["modelValue"])])),_:1}),d(we,{span:6},{default:o((()=>[d(t,{type:"primary",onClick:ae,loading:J.value,disabled:!R.value},{default:o((()=>a[25]||(a[25]=[c(" 添加 ")]))),_:1},8,["loading","disabled"])])),_:1})])),_:1}),d(be,{style:{"margin-top":"8px"}},{default:o((()=>[d(we,{span:24},{default:o((()=>[d(ce,{modelValue:K.notes,"onUpdate:modelValue":a[11]||(a[11]=e=>K.notes=e),placeholder:"备注（可选）",type:"textarea",rows:2},null,8,["modelValue"])])),_:1})])),_:1})]),s("div",O,[a[28]||(a[28]=s("h5",null,"已分配用户",-1)),v((m(),h(ne,{data:G.value,style:{width:"100%"},size:"small"},{default:o((()=>[d(ie,{prop:"userName",label:"用户姓名",width:"120"}),d(ie,{prop:"effectiveDate",label:"生效日期",width:"120"},{default:o((({row:e})=>[c(g(de(e.effectiveDate)),1)])),_:1}),d(ie,{prop:"expiryDate",label:"失效日期",width:"120"},{default:o((({row:e})=>[c(g(e.expiryDate?de(e.expiryDate):"永久有效"),1)])),_:1}),d(ie,{prop:"assignmentType",label:"分配类型",width:"100"},{default:o((({row:e})=>{return[d(re,{size:"small",type:(a=e.assignmentType,{Permanent:"success",Temporary:"warning",Substitute:"info"}[a]||"primary")},{default:o((()=>[c(g(oe(e.assignmentType)),1)])),_:2},1032,["type"])];var a})),_:1}),d(ie,{prop:"notes",label:"备注","show-overflow-tooltip":""}),d(ie,{label:"操作",width:"80"},{default:o((({row:e})=>[d(t,{size:"small",type:"danger",onClick:a=>(async e=>{try{await T.confirm(`确定要移除用户"${e.userName}"的班次分配吗？`,"确认移除",{type:"warning"}),e.removing=!0;const a=await V.removeUserShiftAssignment(e.assignmentId);a.success?(r.success("移除成功"),await ee()):r.error(a.message||"移除失败")}catch(a){"cancel"!==a&&r.error("移除用户分配失败")}finally{e.removing=!1}})(e),loading:e.removing},{default:o((()=>a[27]||(a[27]=[c(" 移除 ")]))),_:2},1032,["onClick","loading"])])),_:1})])),_:1},8,["data"])),[[Te,W.value]])])])):y("",!0)])),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-18cc46f5"]]);export{I as default};
