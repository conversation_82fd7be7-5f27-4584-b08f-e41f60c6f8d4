using System;
using System.ComponentModel.DataAnnotations;

namespace ItAssetsSystem.Application.Features.AssetStatistics.Dtos
{
    /// <summary>
    /// 资产统计查询参数DTO
    /// </summary>
    public class AssetStatisticsQueryDto
    {
        /// <summary>
        /// 开始日期
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// 结束日期
        /// </summary>
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// 资产类型ID
        /// </summary>
        public int? AssetTypeId { get; set; }

        /// <summary>
        /// 位置ID
        /// </summary>
        public int? LocationId { get; set; }

        /// <summary>
        /// 位置类型 (0=工厂, 1=产线, 2=工序, 3=工位, 4=设备位置)
        /// </summary>
        public int? LocationType { get; set; }

        /// <summary>
        /// 部门ID
        /// </summary>
        public int? DepartmentId { get; set; }

        /// <summary>
        /// 资产状态
        /// </summary>
        public string Status { get; set; }

        /// <summary>
        /// 关键词搜索
        /// </summary>
        public string Keyword { get; set; }
    }
}