好的，根据您提供的后端代码文件，我将分析并整理出可用的 API 接口，特别关注任务模块 (V2) 和全局游戏化相关的接口。

**注意:**

*   此文档基于对控制器 (`[ApiController]`) 和路由 (`[Route]`, `[HttpGet]`, `[HttpPost]` 等) 的静态分析生成。
*   标记为 `(V1)` 或 `(旧)` 的接口可能与较新的 `Api/` 或 `Api/V2/` 下的接口功能重叠或已被取代，需要进一步确认是否应弃用。
*   标记为 `(Mock)` 的接口目前返回的是模拟数据，并未连接真实服务或数据库。
*   标记为 `(测试)` 的接口主要用于调试或测试，可能不适合生产环境。
*   `LocationController.cs` 和 `TaskController.cs` (V1) 中的接口与 `Api/` 下的接口存在较多重叠，已优先列出 `Api/` 和 `Api/V2/` 下的接口。

---

## IT 资产管理系统 - 后端 API 接口文档

### 一、 任务管理 (V2 - 推荐使用)

**控制器:** `Api/V2/TasksController.cs`

*   **`GET /api/v2/tasks`**: 获取任务列表 (分页, 支持多种筛选条件，如状态、优先级、类型、负责人、创建人、资产、位置、日期范围等)。
    *   查询参数: `TaskQueryParametersDto`
*   **`GET /api/v2/tasks/{id}`**: 获取指定 ID 的任务详情。
*   **`POST /api/v2/tasks`**: 创建新任务。
    *   请求体: `CreateTaskRequestDto`
*   **`PUT /api/v2/tasks/{id}`**: 更新指定 ID 的任务信息。
    *   请求体: `UpdateTaskRequestDto`
*   **`DELETE /api/v2/tasks/{id}`**: 删除指定 ID 的任务 (通常是软删除)。
*   **`PATCH /api/v2/tasks/{id}/status`**: 更新任务状态。
    *   请求体: `UpdateTaskStatusRequestDto`
*   **`PATCH /api/v2/tasks/{id}/progress`**: 更新任务进度。
    *   请求体: `UpdateTaskProgressRequestDto`
*   **`PATCH /api/v2/tasks/{id}/assign`**: 分配任务给指定负责人。
    *   请求体: `AssignTaskRequestDto`
*   **`PATCH /api/v2/tasks/{id}/complete`**: 完成任务。
    *   查询参数: `remarks` (可选备注)
*   **`GET /api/v2/tasks/{taskId}/comments`**: 获取任务的评论列表。
*   **`POST /api/v2/tasks/{taskId}/comments`**: 为任务添加评论。
    *   请求体: `AddCommentRequestDto`
*   **`GET /api/v2/tasks/{taskId}/attachments`**: 获取任务的附件列表。
*   **`POST /api/v2/tasks/{taskId}/attachments`**: 为任务上传附件。
    *   请求体: Form Data (`file`), 查询参数: `description` (可选)
*   **`GET /api/v2/tasks/{taskId}/history`**: 获取任务的历史记录。

### 二、 游戏化相关接口 (来源: TaskController.cs - V1)

**控制器:** `Controllers/TaskController.cs`
**注意:** 这些接口存在于 V1 的 `TaskController` 中，可能需要评估是否迁移到 V2 或单独模块。

*   **`GET /api/Task/leaderboard`**: 获取任务排行榜。
    *   查询参数: `type` (如 "daily", "weekly", 默认为 "daily")
*   **`GET /api/Task/user-items`**: 获取指定用户的道具列表 (武器、护盾等)。
    *   查询参数: `userId` (可选，默认为 1)
*   **`POST /api/Task/sign-in`**: 用户每日签到，获取积分和道具奖励。
*   **`POST /api/Task/{id}/claim`**: 用户认领任务 (具体逻辑待定)。
*   **`POST /api/Task/{id}/progress`**: 更新任务进度 (V1 版本)。
    *   请求体: `UpdateProgressModel`

### 三、 资产管理

**控制器:** `Controllers/AssetController.cs`

*   **`GET /api/Asset`**: 获取资产列表 (分页, 支持多种筛选条件)。
*   **`GET /api/Asset/{id}`**: 获取指定 ID 的资产详情。
*   **`POST /api/Asset`**: 创建新资产。
    *   请求体: `Asset`
*   **`PUT /api/Asset/{id}`**: 更新指定 ID 的资产信息。
    *   请求体: `AssetUpdateModel` (包含资产数据和变更详情)
*   **`DELETE /api/Asset/{id}`**: 删除指定 ID 的资产。
*   **`GET /api/Asset/{id}/history`**: 获取指定资产的历史记录 (包括位置变更)。
*   **`POST /api/Asset/{id}/change-location`**: 变更资产的位置。
    *   请求体: `AssetLocationChangeModel`
*   **`GET /api/Asset/export`**: 导出资产数据到 Excel 文件 (支持筛选)。

### 四、 资产类型管理

**控制器:** `Controllers/AssetTypeController.cs`

*   **`GET /api/AssetType`**: 获取资产类型列表 (分页, 支持筛选)。
*   **`GET /api/AssetType/{id}`**: 获取指定 ID 的资产类型详情。
*   **`POST /api/AssetType`**: 创建新资产类型。
    *   请求体: `AssetType`
*   **`PUT /api/AssetType/{id}`**: 更新指定 ID 的资产类型信息。
    *   请求体: `AssetType`
*   **`DELETE /api/AssetType/{id}`**: 删除指定 ID 的资产类型。
*   **`GET /api/AssetType/export`**: 导出资产类型数据到 Excel 文件。
*   **`PUT /api/AssetType/{id}/toggle-active`**: 切换资产类型的激活状态。

### 五、 位置管理 (推荐使用 Api/Location/ 下的接口)

**控制器:** `Api/Location/` 下的多个 Endpoint

*   **`GET /api/locations/{id}`**: 获取指定 ID 的位置详情。
*   **`POST /api/locations`**: 创建新位置。
    *   请求体: `LocationUpsertRequest`
*   **`PUT /api/locations/{id}`**: 更新指定 ID 的位置信息。
    *   请求体: `LocationUpsertRequest`
*   **`DELETE /api/locations/{id}`**: 删除指定 ID 的位置。
*   **`GET /api/location-tree`**: 获取完整的位置树状结构。
*   **`GET /api/location-tree/{locationId}/children`**: 获取指定位置节点的直接子节点。
*   **`GET /api/location-search`**: 搜索位置 (支持关键词、激活状态、部门等筛选)。
    *   查询参数: `LocationSearchRequest`
*   **`GET /api/location-search/{id}`**: 按 ID 获取位置详情 (与 `/api/locations/{id}` 功能重叠)。
*   **`GET /api/locations/workstations` (来自 LocationController)**: 获取工位层级(Type=4)的位置列表。
*   **`GET /api/locations/dropdown` (来自 LocationController)**: 获取用于下拉选择的位置列表 (支持按类型筛选)。
*   **`GET /api/locations/{locationId}/assets` (来自 LocationController)**: 获取指定位置关联的资产列表 (分页)。
*   **`POST /api/locations/{locationId}/assets` (来自 LocationController)**: 关联一个或多个资产到指定位置。
    *   请求体: `LocationAssetRelateModel`
*   **`POST /api/locations/{locationId}/assets/unrelate` (来自 LocationController)**: 解除一个或多个资产与指定位置的关联。
    *   请求体: `LocationAssetUnrelateModel`
*   **`GET /api/locations/unrelated-assets` (来自 LocationController)**: 获取未关联到任何位置的资产列表 (分页, 支持筛选)。
*   **`GET /api/locations/{locationId}/users` (来自 LocationController)**: 获取与位置关联的用户列表。
*   **`POST /api/locations/{locationId}/users` (来自 LocationController)**: 更新（添加/替换）与位置关联的用户。
    *   请求体: `UpdateLocationUsersRequest`
*   **`DELETE /api/locations/{locationId}/users/{personnelId}` (来自 LocationController)**: 移除指定位置与人员的关联。
*   **`POST /api/locations/{locationId}/department` (来自 LocationController)**: 设置位置关联的部门，并可能自动关联部门负责人为位置管理员。
    *   请求体: `SetLocationDepartmentRequest`
*   **`GET /api/location-hierarchy`**: 获取位置层级配置 (来自 JSON 文件)。
*   **`POST /api/location-hierarchy`**: 保存位置层级配置。
    *   请求体: `LocationHierarchyConfig`
*   **`POST /api/location-hierarchy/sync`**: 将 JSON 配置中的层级结构同步到数据库。
*   **`POST /api/location-hierarchy/rebuild`**: 从数据库中的位置数据重建层级 JSON 配置文件。

### 六、 用户和认证管理

**控制器:** `Controllers/UserController.cs`

*   **`POST /api/User/login`**: 用户登录 (匿名访问)。
    *   请求体: `LoginModel` (包含用户名和密码)
*   **`GET /api/User`**: 获取所有用户列表。
*   **`GET /api/User/{id}`**: 获取指定 ID 的用户详情。
*   **`POST /api/User`**: 添加新用户 (需要认证)。
    *   请求体: `UserCreateModel`
*   **`GET /api/User/profile`**: 获取当前登录用户的个人信息。
*   **`PUT /api/User/profile`**: 更新当前登录用户的个人信息。
    *   请求体: `UserProfileUpdateModel`
*   **`POST /api/User/avatar`**: 上传当前登录用户的头像。
    *   请求体: Form Data (`file`)
*   **`PUT /api/User/change-password`**: 修改当前登录用户的密码。
    *   请求体: `ChangePasswordModel`
*   **`POST /api/User/reset-password`**: 重置指定用户的密码为默认值 (需要权限)。
    *   请求体: `userId` (int)
*   **`POST /api/User/reset-all-passwords`**: 重置所有用户的密码为默认值 (需要极高权限)。
*   **`POST /api/User/batch`**: 根据用户 ID 列表批量获取用户信息。
    *   请求体: `BatchUserRequest`
*   **`GET /api/User/location-users`**: 获取可用于关联到位置的用户候选列表。
    *   查询参数: `userType` (0=使用人, 1=管理员), `departmentId` (可选)
*   **`GET /api/User/info`**: 获取当前登录用户的详细信息 (包含角色和权限)。

### 七、 部门管理

**控制器:** `Controllers/DepartmentController.cs`

*   **`GET /api/Department`**: 获取所有部门列表。
*   **`GET /api/Department/{id}`**: 获取指定 ID 的部门详情。
*   **`GET /api/Department/{departmentId}/users`**: 获取指定部门下的用户列表。
*   **`POST /api/Department`**: 创建新部门。
    *   请求体: `DepartmentRequest`
*   **`PUT /api/Department/{id}`**: 更新指定 ID 的部门信息。
    *   请求体: `DepartmentRequest`
*   **`DELETE /api/Department/{id}`**: 删除指定 ID 的部门。

### 八、 人员管理 (与用户关联，但独立管理)

**控制器:** `Controllers/PersonnelController.cs`

*   **`GET /api/Personnel`**: 获取人员列表 (支持按部门和关键词筛选)。
*   **`GET /api/Personnel/{id}`**: 获取指定 ID 的人员详情。
*   **`POST /api/Personnel`**: 创建新人员记录。
    *   请求体: `PersonnelRequest`
*   **`PUT /api/Personnel/{id}`**: 更新指定 ID 的人员信息。
    *   请求体: `PersonnelRequest`
*   **`DELETE /api/Personnel/{id}`**: 删除指定 ID 的人员记录。
*   **`GET /api/Personnel/department-managers`**: 获取被标记为部门负责人的人员列表。

### 九、 数据备份与恢复

**控制器:** `Api/Backup/` 下的多个 Endpoint

*   **`GET /api/backup/configure`**: 获取自动备份配置。
*   **`POST /api/backup/configure`**: 配置自动备份参数。
    *   请求体: `BackupConfigurationRequest`
*   **`GET /api/backup/history`**: 获取备份历史记录列表。
*   **`POST /api/backup/create`**: 创建一个手动备份。
    *   请求体: `CreateBackupRequest`
*   **`POST /api/backup/restore`**: 从指定备份恢复数据。
    *   请求体: `RestoreBackupRequest`
*   **`DELETE /api/backup/{backupId}`**: 删除指定的备份文件和记录。

### 十、 数据导入

**控制器:** `Api/Import/ImportDataEndpoint.cs`

*   **`GET /api/import/formats`**: 获取支持的导入文件格式 (CSV, Excel, JSON)。
*   **`GET /api/import/template/{entityType}/{formatStr}`**: 下载指定实体类型的导入模板文件。
*   **`POST /api/import/process`**: 上传并处理数据导入文件。
    *   请求体: Form Data (`file`, `entityType`)
*   **`POST /api/import/data`**: (兼容旧接口) 上传并处理数据导入文件。
    *   请求体: Form Data (`file`, `entityType`)
*   **`POST /api/import/upload/{entityType}`**: (兼容旧接口) 上传文件用于导入。
    *   请求体: Form Data (`file`)

### 十一、 数据导出

**控制器:** `Api/Export/ExportDataEndpoint.cs`

*   **`GET /api/export/formats`**: 获取支持的导出文件格式 (CSV, Excel, PDF, JSON)。
*   **`GET /api/export/entities`**: 获取支持导出的实体类型列表。
*   **`POST /api/export`**: 根据请求导出指定实体的数据。
    *   请求体: `ExportRequest` (包含实体类型、格式、筛选条件等)

### 十二、 系统配置管理

**控制器:** `Api/Configuration/SystemConfigEndpoint.cs`

*   **`GET /api/system/config`**: 获取所有系统配置项。
*   **`GET /api/system/config/{key}`**: 获取指定键的配置项值。
*   **`PUT /api/system/config/{key}`**: 更新指定键的配置项值。
    *   请求体: `object` (配置值)
*   **`PUT /api/system/config`**: 批量更新配置项。
    *   请求体: `Dictionary<string, object>`
*   **`POST /api/system/config/reset`**: 将所有配置项重置为默认值。
*   **`GET /api/system/config/export`**: 导出系统配置到 JSON 文件。
*   **`POST /api/system/config/import`**: 从 JSON 文件导入系统配置。
    *   请求体: Form Data (`file`)

### 十三、 插件管理

**控制器:** `Controllers/PluginController.cs`

*   **`GET /api/plugin`**: 获取所有已加载插件的信息。
*   **`GET /api/plugin/{id}`**: 获取指定 ID 的插件信息。
*   **`POST /api/plugin/{id}/start`**: 启动指定插件。
*   **`POST /api/plugin/{id}/stop`**: 停止指定插件。
*   **`DELETE /api/plugin/{id}`**: 卸载指定插件 (非内置)。
*   **`POST /api/plugin/upload`**: 上传并安装新插件 (DLL 文件)。
*   **`POST /api/plugin/startAll`**: 启动所有已加载的插件。
*   **`POST /api/plugin/stopAll`**: 停止所有正在运行的插件。

### 十四、 自然语言处理

**控制器:** `Api/NaturalLanguage/NaturalLanguageEndpoint.cs`

*   **`POST /api/natural-language/process`**: 处理用户输入的自然语言命令。
    *   请求体: `CommandRequest`
*   **`GET /api/natural-language/examples`**: 获取常用命令示例。
*   **`GET /api/natural-language/history`**: 获取当前用户的命令历史记录。
*   **`POST /api/natural-language/suggestions`**: 根据用户部分输入提供命令建议。
    *   请求体: `SuggestionRequest`

### 十五、 UI 定义管理

**控制器:** `Api/UI/UiDefinitionEndpoint.cs`

*   **`GET /api/ui`**: 获取所有 UI 定义列表 (系统内置 + 自定义)。
*   **`GET /api/ui/{id}`**: 获取指定 ID 的 UI 定义。
*   **`POST /api/ui`**: 保存 UI 定义 (创建或更新)。
    *   请求体: `UiDefinition`
*   **`DELETE /api/ui/{id}`**: 删除指定 ID 的 UI 定义 (不能删除系统内置)。
*   **`POST /api/ui/reset-system`**: 重新加载或重置系统内置的 UI 定义。

### 十六、 故障管理 (Mock)

**控制器:** `Controllers/FaultController.cs`
**注意:** 当前实现返回模拟数据。

*   **`GET /api/Fault`**: 获取所有故障记录 (Mock)。
*   **`GET /api/Fault/{id}`**: 获取指定 ID 的故障详情 (Mock)。
*   **`POST /api/Fault`**: 报告新故障。
    *   请求体: `ReportFaultModel`
*   **`PUT /api/Fault/{id}/fix`**: 标记故障已修复。
    *   请求体: `FixFaultModel`

### 十七、 采购管理 (Mock)

**控制器:** `Controllers/PurchaseController.cs`
**注意:** 当前实现返回模拟数据。

*   **`GET /api/Purchase`**: 获取所有采购订单 (Mock)。
*   **`GET /api/Purchase/{id}`**: 获取指定 ID 的采购订单详情 (Mock)。
*   **`POST /api/Purchase`**: 创建采购订单。
    *   请求体: `CreateOrderModel`
*   **`PUT /api/Purchase/{id}/delivery`**: 更新采购订单的到货状态。
    *   请求体: `DeliveryModel`

### 十八、 其他

*   **`GET /Health` (来自 `HealthController.cs`)**: 提供详细的系统健康状态检查 (包括数据库连接)。

---

**总结:**

*   **任务管理:** 强烈推荐使用 `/api/v2/tasks` 下的接口，功能全面且基于新架构。V1 的 `TaskController` 可能需要被移除或重构。
*   **游戏化:** 相关接口目前存在于 V1 的 `TaskController` 中，应考虑是否需要保留这些功能，如果需要，最好迁移到独立的 V2 模块或服务中。
*   **位置管理:** `Api/Location/` 下的接口是推荐使用的，`Controllers/LocationController.cs` 中的逻辑应逐步迁移或确认是否冗余后删除。
*   **故障和采购:** 当前控制器返回的是模拟数据，需要进一步开发或连接真实的数据服务。
*   **测试/模板接口:** `WeatherForecastController`, `ExportTestController`, `OfflineOperationController` 应在生产环境中移除。

这份文档应该为您提供了一个清晰的后端可用接口概览。