{"permissions": {"allow": ["Bash(grep:*)", "Bash(find:*)", "Bash(dotnet build:*)", "<PERSON><PERSON>(sed:*)", "Bash(rg:*)", "Bash(npm run dev:*)", "Bash(ls:*)", "Bash(rm:*)", "Bash(npm install:*)", "Bash(timeout 10s npm run dev)", "Bash(grep -r \"MapControllers\\|UseRouting\\|MapControllerRoute\" /mnt/e/itassetssystem/singleit20250406/Startup.cs)", "Bash(grep -n \"UseRouting\\|MapControllers\\|endpoints\\.Map\" /mnt/e/itassetssystem/singleit20250406/Startup.cs)", "Bash(find /mnt/e/itassetssystem/singleit20250406 -name \"*TaskService*\" -type f)", "Bash(grep -n \"HttpGet\\|HttpPost\\|HttpPut\\|HttpDelete\\|HttpPatch\" /mnt/e/itassetssystem/singleit20250406/Api/V2/Controllers/TasksController.cs)", "Bash(grep -n \"HttpGet\\|HttpPost\\|HttpPut\\|HttpDelete\\|HttpPatch\" /mnt/e/itassetssystem/singleit20250406/Api/V2/Controllers/TasksControllerEnhanced.cs)", "Bash(grep -n \"HttpGet\" /mnt/e/itassetssystem/singleit20250406/Api/V2/Controllers/TasksControllerEnhanced.cs)", "Bash(grep -n \"request\\.get\\|request\\.post\\|request\\.put\\|request\\.delete\\|request\\.patch\" /mnt/e/itassetssystem/singleit20250406/frontend/src/api/task.js)", "Bash(grep -n \"request\\.get.*tasks\" /mnt/e/itassetssystem/singleit20250406/frontend/src/api/task.js)", "Bash(diff /mnt/e/itassetssystem/singleit20250406/Api/V2/TasksController.cs /mnt/e/itassetssystem/singleit20250406/Api/V2/Controllers/TasksController.cs)", "Bash(grep -n \"HttpGet\\|HttpPost\\|HttpPut\\|HttpDelete\\|HttpPatch\" /mnt/e/itassetssystem/singleit20250406/Api/V2/TasksController.cs)", "Bash(grep -n \"HttpGet\" /mnt/e/itassetssystem/singleit20250406/Api/V2/TasksController.cs)", "Bash(grep -n \"Route.*api/v2\" /mnt/e/itassetssystem/singleit20250406/Api/V2/*.cs /mnt/e/itassetssystem/singleit20250406/Api/V2/Controllers/*.cs)", "Bash(mkdir -p /mnt/e/itassetssystem/singleit20250406/Api/V2/backup)", "Bash(cp /mnt/e/itassetssystem/singleit20250406/Api/V2/Controllers/TasksController.cs /mnt/e/itassetssystem/singleit20250406/Api/V2/backup/TasksController_51lines.cs)", "Bash(cp /mnt/e/itassetssystem/singleit20250406/Api/V2/Controllers/TasksControllerEnhanced.cs /mnt/e/itassetssystem/singleit20250406/Api/V2/backup/TasksControllerEnhanced.cs)", "Bash(mv /mnt/e/itassetssystem/singleit20250406/Api/V2/PeriodicTaskSchedulesController.cs /mnt/e/itassetssystem/singleit20250406/Api/V2/Controllers/)", "Bash(mv /mnt/e/itassetssystem/singleit20250406/Api/V2/QuickMemosController.cs /mnt/e/itassetssystem/singleit20250406/Api/V2/Controllers/)", "Bash(mv /mnt/e/itassetssystem/singleit20250406/Api/V2/TasksController.cs /mnt/e/itassetssystem/singleit20250406/Api/V2/Controllers/TasksController_Full.cs)", "Bash(mv /mnt/e/itassetssystem/singleit20250406/Api/V2/Controllers/TasksController_Full.cs /mnt/e/itassetssystem/singleit20250406/Api/V2/Controllers/TasksController.cs)", "Bash(grep -n \"AuditLogs\\|AuditLog\" /mnt/e/itassetssystem/singleit20250406/Migrations/20250528120919_InitClean.cs)", "Bash(grep -n \"getFullAvatarUrl\" /mnt/e/itassetssystem/singleit20250406/frontend/src/views/tasks/components/TaskDetailDialog.vue)", "Bash(grep -n \"getFullAvatarUrl\" /mnt/e/itassetssystem/singleit20250406/frontend/src/stores/modules/user.js)", "Bash(grep -n \"getFullAvatarUrl(taskData.creatorUserId)\" /mnt/e/itassetssystem/singleit20250406/frontend/src/views/tasks/components/TaskDetailDialog.vue)", "Bash(grep -n \"getFullAvatarUrl(participant.id)\" /mnt/e/itassetssystem/singleit20250406/frontend/src/views/tasks/components/TaskDetailDialog.vue)", "Bash(grep -n -A 5 -B 5 \"participants\\|Participants\" /mnt/e/itassetssystem/singleit20250406/Application/Features/Tasks/Dtos/TaskDto.cs)", "Bash(grep -n -A 5 -B 5 \"TaskAssignees\\|Assignees\\|collaborator\" /mnt/e/itassetssystem/singleit20250406/Application/Features/Tasks/Dtos/TaskDto.cs)", "Bash(grep -n \"TaskAssignees\\|Assignees\" /mnt/e/itassetssystem/singleit20250406/Application/Features/Tasks/Dtos/TaskDto.cs)", "Bash(find /mnt/e/itassetssystem/singleit20250406 -name \"*.cs\" -exec grep -l \"UserBasicDto\" {} \\;)", "Bash(grep -n -A 10 \"UserBasicDto\\|CoreUserDto\" /mnt/e/itassetssystem/singleit20250406/Core/Abstractions/ICoreDataQueryService.cs)", "Bash(grep -n -A 5 -B 5 \"UserBasicDto\" /mnt/e/itassetssystem/singleit20250406/Application/Features/Tasks/Services/TaskService.cs)", "Bash(grep -n -A 10 \"class UserBasicDto\" /mnt/e/itassetssystem/singleit20250406/Application/Features/Tasks/Dtos/TaskDto.cs)", "Bash(grep -n -A 20 \"CREATE TABLE.*tasks\" /mnt/e/itassetssystem/singleit20250406/analyresport/20250528itassets数据库.sql)", "Bash(grep -n -A 30 \"CREATE TABLE.*taskassignees\" /mnt/e/itassetssystem/singleit20250406/analyresport/20250528itassets数据库.sql)", "Bash(grep -n -A 10 -B 5 \"Map TaskAssignees to Participants DTO list\" /mnt/e/itassetssystem/singleit20250406/Application/Features/Tasks/Services/TaskService.cs)", "Bash(grep -n \"import.*UserAvatar\" /mnt/e/itassetssystem/singleit20250406/frontend/src/views/tasks/components/TaskDetailDialog.vue)", "Bash(grep -n -A 5 -B 5 \"const.*=\" /mnt/e/itassetssystem/singleit20250406/frontend/src/views/tasks/components/TaskDetailDialog.vue)", "Bash(grep -n -A 10 -B 5 \"负责人\\|assignee\" /mnt/e/itassetssystem/singleit20250406/frontend/src/components/Tasks/EnhancedTaskCard.vue)", "Bash(grep -n -A 10 -B 5 \"assignee\\|负责\" /mnt/e/itassetssystem/singleit20250406/frontend/src/components/Tasks/EnhancedTaskCard.vue)", "Bash(find /mnt/e/itassetssystem/singleit20250406/frontend/src/views/tasks -name \"*.vue\")", "Bash(grep -n -A 5 -B 5 \"assignee\\|负责\" /mnt/e/itassetssystem/singleit20250406/frontend/src/views/tasks/components/TaskCard.vue)", "Bash(grep -n \"getAllAssignees\" /mnt/e/itassetssystem/singleit20250406/frontend/src/views/tasks/components/TaskCard.vue)", "Bash(find . -name \"*.sql\" -type f)", "Bash(grep -n \"CREATE TABLE.*tasks\" analyresport/20250528itassets数据库.sql)", "Bash(grep -n \"CREATE TABLE.*taskassignees\" analyresport/20250528itassets数据库.sql)", "Bash(grep -A 20 \"INSERT INTO.*tasks\" analyresport/20250528itassets数据库.sql)", "Bash(grep -n \"INSERT INTO\" analyresport/20250528itassets数据库.sql)", "Bash(find . -name \"TaskService.cs\" -type f)", "Bash(grep -n \"UtcNow\" Application/Features/Tasks/Services/TaskService.cs)", "Bash(sed -i 's/DateTime\\.UtcNow/DateTime.Now/g' Application/Features/Tasks/Services/TaskService.cs)", "Bash(sed -i 's/use UtcNow/use local time/g' Application/Features/Tasks/Services/TaskService.cs)", "Bash(grep -r \"UtcNow\" Application/Features/Tasks/)", "Bash(grep -r \"ToUniversalTime\" Application/Features/Tasks/)", "Bash(sed -i 's/\\.ToUniversalTime()/\\/\\/ .ToUniversalTime() removed - using local time/g' Application/Features/Tasks/Services/TaskService.cs)", "Bash(grep -n \"负责人\\|assignee\\|UserAvatar\" frontend/src/views/tasks/components/TaskCard.vue)", "Bash(dotnet run)", "<PERSON><PERSON>(python3:*)", "Bash(npm run build:*)", "Bash(dotnet ef migrations add:*)", "<PERSON><PERSON>(touch:*)", "Bash(/home/<USER>/.npm-global/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-linux/rg \"CREATE TABLE.*task|CREATE TABLE.*spare\" /mnt/e/itassetssystem/singleit20250406/analyresport/20250601itassets数据库.sql)", "<PERSON><PERSON>(mysql:*)", "<PERSON><PERSON>(timeout 15 npm run dev)", "Bash(/home/<USER>/.npm-global/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-linux/rg -n \"new Task\" /mnt/e/itassetssystem/singleit20250406/Application/Features/Tasks/Services/TaskService.cs)", "Bash(/home/<USER>/.npm-global/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-linux/rg -n \"Task\\.\" /mnt/e/itassetssystem/singleit20250406/Application/Features/Tasks/Services/TaskService.cs)", "Bash(/home/<USER>/.npm-global/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-linux/rg -n \"fetchTasks\" /mnt/e/itassetssystem/singleit20250406/frontend/src/api/taskEnhanced.js)", "Bash(/home/<USER>/.npm-global/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-linux/rg -A 10 -B 5 \"fetchTasks\" /mnt/e/itassetssystem/singleit20250406/frontend/src/stores/modules/taskEnhanced.js)", "Bash(/home/<USER>/.npm-global/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-linux/rg -A 10 \"getTaskList\" /mnt/e/itassetssystem/singleit20250406/frontend/src/stores/modules/taskEnhanced.js)", "Bash(/home/<USER>/.npm-global/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-linux/rg -A 20 \"public.*GetTasks\" /mnt/e/itassetssystem/singleit20250406/Api/V2/Controllers/TasksController.cs)", "Bash(/home/<USER>/.npm-global/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-linux/rg -A 10 \"GetUsersAsync\" /mnt/e/itassetssystem/singleit20250406/Infrastructure/Services/CoreDataQueryService.cs)", "Bash(/home/<USER>/.npm-global/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-linux/rg -A 5 \"GetAssetsAsync\" /mnt/e/itassetssystem/singleit20250406/Infrastructure/Services/CoreDataQueryService.cs)", "Bash(/home/<USER>/.npm-global/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-linux/rg -A 5 \"GetLocationsAsync\" /mnt/e/itassetssystem/singleit20250406/Infrastructure/Services/CoreDataQueryService.cs)", "<PERSON><PERSON>(mkdir:*)", "Bash(/home/<USER>/.npm-global/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-linux/rg -n \"MaxValue|MinValue\" CreateOptimizedAssetViews.sql)", "Bash(/home/<USER>/.npm-global/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-linux/rg -n -A 5 -B 5 \"v_asset_value_distribution_enhanced\" CreateOptimizedAssetViews.sql)", "Bash(/home/<USER>/.npm-global/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-linux/rg -n \"FROM v_\" CreateOptimizedAssetViews.sql)", "Bash(/home/<USER>/.npm-global/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-linux/rg -n -A 5 -B 5 \"AS MaxValue\" CreateOptimizedAssetViews.sql)", "Bash(/home/<USER>/.npm-global/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-linux/rg -n \"analytics-workbench\" .)", "Bash(/home/<USER>/.npm-global/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-linux/rg -n -A 10 -B 5 \"updateKpiData\" views/asset/AssetAnalyticsWorkbench.vue)", "Bash(/home/<USER>/.npm-global/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-linux/rg -n \"GetAssetTypesAsync\\|GetRegionOptionsAsync\\|GetDepartmentOptionsAsync\" Application/Features/AssetStatistics/Services/ViewBasedAssetStatisticsService.cs)", "<PERSON><PERSON>(mv:*)", "Bash(cp:*)", "WebFetch(domain:github.com)", "WebFetch(domain:app.augmentcode.com)", "<PERSON><PERSON>(curl:*)", "Bash(npm run:*)"], "deny": []}, "enableAllProjectMcpServers": false}