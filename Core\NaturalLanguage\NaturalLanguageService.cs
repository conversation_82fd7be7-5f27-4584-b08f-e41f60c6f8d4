// IT资产管理系统 - 自然语言处理服务实现
// 文件路径: /Core/NaturalLanguage/NaturalLanguageService.cs
// 功能: 提供自然语言处理服务，用于非技术人员的领域特定语言接口

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using ItAssetsSystem.Core.Helpers;
using ItAssetsSystem.Infrastructure.Data;
using ItAssetsSystem.Models.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Text.Json.Serialization;

// 使用全限定名称来避免Task类型的歧义
using Task = System.Threading.Tasks.Task;

namespace ItAssetsSystem.Core.NaturalLanguage
{
    // 添加数据传输对象类，用于替代匿名类型
    public class AssetDto
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string AssetType { get; set; }
        public string SerialNumber { get; set; }
        public string Location { get; set; }
        public int Status { get; set; }
        public DateTime? PurchaseDate { get; set; }
        public decimal? Price { get; set; }
        public string Notes { get; set; }
    }
    
    public class FaultDto
    {
        public int Id { get; set; }
        public string Title { get; set; }
        public string Asset { get; set; }
        public string FaultType { get; set; }
        public int Status { get; set; }
        public int Severity { get; set; }
        public DateTime ReportedAt { get; set; }
    }
    
    public class StatDto
    {
        public string Name { get; set; }
        public int Count { get; set; }
    }
    
    /// <summary>
    /// 命令历史记录
    /// </summary>
    public class CommandHistoryEntry
    {
        /// <summary>
        /// ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();
        
        /// <summary>
        /// 用户ID
        /// </summary>
        public string UserId { get; set; }
        
        /// <summary>
        /// 命令文本
        /// </summary>
        public string CommandText { get; set; }
        
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }
        
        /// <summary>
        /// 结果消息
        /// </summary>
        public string ResultMessage { get; set; }
    }
    
    /// <summary>
    /// 命令模式定义
    /// </summary>
    public class CommandPattern
    {
        /// <summary>
        /// 命令类型
        /// </summary>
        public string CommandType { get; set; }
        
        /// <summary>
        /// 正则表达式模式
        /// </summary>
        public string Pattern { get; set; }
        
        /// <summary>
        /// 示例
        /// </summary>
        public List<string> Examples { get; set; } = new List<string>();
        
        /// <summary>
        /// 参数名称映射
        /// </summary>
        public Dictionary<string, string> ParameterMapping { get; set; } = new Dictionary<string, string>();
    }
    
    /// <summary>
    /// 自然语言处理服务
    /// </summary>
    public class NaturalLanguageService : INaturalLanguageService
    {
        private readonly ILogger<NaturalLanguageService> _logger;
        private readonly IConfiguration _configuration;
        private readonly AppDbContext _dbContext;
        private readonly string _commandHistoryPath;
        private readonly string _commandPatternsPath;
        private readonly List<CommandPattern> _commandPatterns;
        private readonly JsonSerializerOptions _jsonOptions;
        
        /// <summary>
        /// 构造函数
        /// </summary>
        public NaturalLanguageService(
            ILogger<NaturalLanguageService> logger,
            IConfiguration configuration,
            AppDbContext dbContext)
        {
            _logger = logger;
            _configuration = configuration;
            _dbContext = dbContext;
            
            // 获取命令历史存储路径
            string dataPath = _configuration.GetValue<string>("NaturalLanguage:DataPath");
            if (string.IsNullOrEmpty(dataPath))
            {
                dataPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data", "NaturalLanguage");
            }
            
            // 确保目录存在
            FileHelper.EnsureDirectoryExists(dataPath, _logger);
            
            // 命令历史文件路径
            _commandHistoryPath = Path.Combine(dataPath, "command_history.json");
            
            // 命令模式文件路径
            _commandPatternsPath = Path.Combine(dataPath, "command_patterns.json");
            
            // 初始化JSON序列化选项
            _jsonOptions = new JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            };
            
            // 加载命令模式
            _commandPatterns = LoadCommandPatterns();
            
            // 如果命令模式为空，创建默认模式
            if (_commandPatterns.Count == 0)
            {
                _commandPatterns = CreateDefaultCommandPatterns();
                SaveCommandPatterns(_commandPatterns);
            }
        }
        
        /// <summary>
        /// 处理自然语言命令
        /// </summary>
        public async Task<CommandResult> ProcessCommandAsync(string input, string userId)
        {
            if (string.IsNullOrWhiteSpace(input))
            {
                return new CommandResult
                {
                    Success = false,
                    Message = "请输入命令",
                    Command = input,
                    Suggestions = await GetCommandExamplesAsync()
                };
            }
            
            _logger.LogInformation("处理自然语言命令: {Input}", input);
            
            try
            {
                // 匹配命令
                var match = MatchCommand(input);
                
                if (match == null)
                {
                    var result = new CommandResult
                    {
                        Success = false,
                        Message = "无法识别此命令，请尝试使用其他表述",
                        Command = input,
                        Suggestions = await GetCommandSuggestionsAsync(input, userId)
                    };
                    
                    // 记录命令历史
                    await SaveCommandHistoryAsync(userId, input, false, result.Message);
                    
                    return result;
                }
                
                // 根据命令类型执行操作
                var commandResult = await ExecuteCommandAsync(match, userId);
                
                // 记录命令历史
                await SaveCommandHistoryAsync(userId, input, commandResult.Success, commandResult.Message);
                
                return commandResult;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理自然语言命令失败: {Input}", input);
                
                var result = new CommandResult
                {
                    Success = false,
                    Message = "处理命令时发生错误: " + ex.Message,
                    Command = input
                };
                
                // 记录命令历史
                await SaveCommandHistoryAsync(userId, input, false, result.Message);
                
                return result;
            }
        }
        
        /// <summary>
        /// 获取命令示例
        /// </summary>
        public async Task<List<string>> GetCommandExamplesAsync()
        {
            _logger.LogInformation("获取命令示例");
            
            var examples = _commandPatterns
                .SelectMany(p => p.Examples)
                .ToList();
            
            return await System.Threading.Tasks.Task.FromResult(examples);
        }
        
        /// <summary>
        /// 获取命令历史
        /// </summary>
        public async Task<List<string>> GetCommandHistoryAsync(string userId, int count = 10)
        {
            _logger.LogInformation("获取命令历史: 用户{UserId}, 数量{Count}", userId, count);
            
            try
            {
                var history = await LoadCommandHistoryAsync();
                
                return history
                    .Where(h => h.UserId == userId)
                    .OrderByDescending(h => h.CreatedAt)
                    .Take(count)
                    .Select(h => h.CommandText)
                    .ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取命令历史失败");
                return new List<string>();
            }
        }
        
        /// <summary>
        /// 获取命令建议
        /// </summary>
        public async Task<List<string>> GetCommandSuggestionsAsync(string partialInput, string userId)
        {
            _logger.LogInformation("获取命令建议: {PartialInput}", partialInput);
            
            // 添加await Task.Yield()确保异步方法不会触发警告
            await Task.Yield();
            
            var suggestions = new List<string>();
            
            try
            {
                // 从命令模式中获取示例
                var examples = _commandPatterns
                    .SelectMany(p => p.Examples)
                    .ToList();
                
                // 过滤与部分输入匹配的示例
                if (!string.IsNullOrWhiteSpace(partialInput))
                {
                    suggestions.AddRange(examples
                        .Where(e => e.Contains(partialInput, StringComparison.OrdinalIgnoreCase))
                        .Take(5));
                }
                else
                {
                    suggestions.AddRange(examples.Take(5));
                }
                
                // 如果建议过少，添加一些通用建议
                if (suggestions.Count < 5)
                {
                    var commonSuggestions = new List<string>
                    {
                        "查询所有资产",
                        "查询正在维修的资产",
                        "查询闲置资产",
                        "新增资产",
                        "查询位于1楼的资产",
                        "查询IT部门的资产",
                        "统计各部门资产",
                        "统计资产状态",
                        "查询最近故障"
                    };
                    
                    suggestions.AddRange(commonSuggestions
                        .Where(s => !suggestions.Contains(s))
                        .Take(5 - suggestions.Count));
                }
                
                return suggestions;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取命令建议失败");
                return suggestions;
            }
        }
        
        /// <summary>
        /// 匹配命令
        /// </summary>
        private CommandMatch MatchCommand(string input)
        {
            try
            {
                foreach (var pattern in _commandPatterns)
                {
                    var regex = new Regex(pattern.Pattern, RegexOptions.IgnoreCase);
                    var match = regex.Match(input);
                    
                    if (match.Success)
                    {
                        var parameters = new Dictionary<string, string>();
                        
                        // 提取参数
                        foreach (var param in pattern.ParameterMapping)
                        {
                            if (match.Groups[param.Key].Success)
                            {
                                parameters[param.Value] = match.Groups[param.Key].Value;
                            }
                        }
                        
                        return new CommandMatch
                        {
                            CommandType = pattern.CommandType,
                            Confidence = 0.9, // 简化实现，使用固定值
                            Parameters = parameters
                        };
                    }
                }
                
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "匹配命令失败: {Input}", input);
                return null;
            }
        }
        
        /// <summary>
        /// 执行命令
        /// </summary>
        private async Task<CommandResult> ExecuteCommandAsync(CommandMatch match, string userId)
        {
            _logger.LogInformation("执行命令: {CommandType}", match.CommandType);
            
            try
            {
                switch (match.CommandType)
                {
                    case "QueryAssets":
                        return await QueryAssetsAsync(match.Parameters);
                    
                    case "QueryAssetById":
                        return await QueryAssetByIdAsync(match.Parameters);
                    
                    case "QueryAssetsByStatus":
                        return await QueryAssetsByStatusAsync(match.Parameters);
                    
                    case "QueryAssetsByLocation":
                        return await QueryAssetsByLocationAsync(match.Parameters);
                    
                    case "QueryAssetsByDepartment":
                        return await QueryAssetsByDepartmentAsync(match.Parameters);
                    
                    case "StatAssetsByDepartment":
                        return await StatAssetsByDepartmentAsync();
                    
                    case "StatAssetsByStatus":
                        return await StatAssetsByStatusAsync();
                    
                    case "QueryRecentFaults":
                        return await QueryRecentFaultsAsync(match.Parameters);
                    
                    default:
                        return new CommandResult
                        {
                            Success = false,
                            Message = "不支持的命令类型: " + match.CommandType
                        };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "执行命令失败: {CommandType}", match.CommandType);
                
                return new CommandResult
                {
                    Success = false,
                    Message = "执行命令时发生错误: " + ex.Message
                };
            }
        }
        
        #region 命令实现
        
        /// <summary>
        /// 查询资产
        /// </summary>
        private async Task<CommandResult> QueryAssetsAsync(Dictionary<string, string> parameters)
        {
            int limit = 10;
            if (parameters.ContainsKey("limit") && int.TryParse(parameters["limit"], out int parsedLimit))
            {
                limit = parsedLimit;
            }
            
            var assets = await _dbContext.Assets
                .Include(a => a.AssetType)
                .Include(a => a.Location)
                .OrderByDescending(a => a.CreatedAt)
                .Take(limit)
                .ToListAsync();
            
            if (assets.Any() == false)
            {
                return new CommandResult
                {
                    Success = true,
                    Message = "未找到任何资产",
                    Data = new List<object>()
                };
            }
            
            var result = assets.Select(a => new AssetDto
            {
                Id = a.Id,
                Name = a.Name,
                AssetType = a.AssetType?.Name,
                SerialNumber = a.SerialNumber,
                Location = a.Location?.Name,
                Status = a.Status,
                PurchaseDate = a.PurchaseDate
            }).ToList();
            
            return new CommandResult
            {
                Success = true,
                Message = $"找到 {assets.Count()} 个资产",
                Data = result,
                Suggestions = new List<string>
                {
                    "查询库存中的资产",
                    "查询IT部门的资产",
                    "统计各部门资产"
                }
            };
        }
        
        /// <summary>
        /// 查询指定资产
        /// </summary>
        private async Task<CommandResult> QueryAssetByIdAsync(Dictionary<string, string> parameters)
        {
            if (!parameters.ContainsKey("id") && !parameters.ContainsKey("serialNumber"))
            {
                return new CommandResult
                {
                    Success = false,
                    Message = "未提供资产ID或序列号"
                };
            }
            
            Asset asset = null;
            
            if (parameters.ContainsKey("id") && int.TryParse(parameters["id"], out int assetId))
            {
                asset = await _dbContext.Assets
                    .Include(a => a.AssetType)
                    .Include(a => a.Location)
                    .FirstOrDefaultAsync(a => a.Id == assetId);
            }
            else if (parameters.ContainsKey("serialNumber"))
            {
                asset = await _dbContext.Assets
                    .Include(a => a.AssetType)
                    .Include(a => a.Location)
                    .FirstOrDefaultAsync(a => a.SerialNumber == parameters["serialNumber"]);
            }
            
            if (asset == null)
            {
                return new CommandResult
                {
                    Success = false,
                    Message = "未找到指定资产",
                    Suggestions = new List<string>
                    {
                        "查询所有资产",
                        "使用其他序列号查询资产"
                    }
                };
            }
            
            var result = new AssetDto
            {
                Id = asset.Id,
                Name = asset.Name,
                AssetType = asset.AssetType?.Name,
                SerialNumber = asset.SerialNumber,
                Location = asset.Location?.Name,
                Status = asset.Status,
                PurchaseDate = asset.PurchaseDate,
                Price = asset.Price,
                Notes = asset.Notes
            };
            
            return new CommandResult
            {
                Success = true,
                Message = $"找到资产: {asset.Name}",
                Data = result,
                Suggestions = new List<string>
                {
                    $"查询与{asset.Name}相关的故障",
                    "查询类似资产"
                }
            };
        }
        
        /// <summary>
        /// 按状态查询资产
        /// </summary>
        private async Task<CommandResult> QueryAssetsByStatusAsync(Dictionary<string, string> parameters)
        {
            if (!parameters.ContainsKey("status"))
            {
                return new CommandResult
                {
                    Success = false,
                    Message = "未提供资产状态"
                };
            }
            
            // 将状态文本映射为数值
            string statusText = parameters["status"];
            int statusValue = 0;
            
            if (int.TryParse(MapStatusValue(statusText), out int parsedStatus))
            {
                statusValue = parsedStatus;
            }
            
            var assets = await _dbContext.Assets
                .Include(a => a.AssetType)
                .Include(a => a.Location)
                .Where(a => a.Status == statusValue)
                .OrderByDescending(a => a.CreatedAt)
                .Take(20)
                .ToListAsync();
            
            if (assets.Any() == false)
            {
                return new CommandResult
                {
                    Success = true,
                    Message = $"未找到状态为 {parameters["status"]} 的资产",
                    Data = new List<object>()
                };
            }
            
            var result = assets.Select(a => new AssetDto
            {
                Id = a.Id,
                Name = a.Name,
                AssetType = a.AssetType?.Name,
                SerialNumber = a.SerialNumber,
                Location = a.Location?.Name,
                Status = a.Status,
                PurchaseDate = a.PurchaseDate
            }).ToList();
            
            return new CommandResult
            {
                Success = true,
                Message = $"找到 {assets.Count()} 个状态为 {parameters["status"]} 的资产",
                Data = result,
                Suggestions = new List<string>
                {
                    "统计各状态资产数量",
                    "查询所有资产"
                }
            };
        }
        
        /// <summary>
        /// 按位置查询资产
        /// </summary>
        private async Task<CommandResult> QueryAssetsByLocationAsync(Dictionary<string, string> parameters)
        {
            if (!parameters.ContainsKey("location"))
            {
                return new CommandResult
                {
                    Success = false,
                    Message = "缺少位置参数",
                    Suggestions = new List<string>
                    {
                        "查询总部的资产",
                        "查询研发中心的资产",
                        "查询所有资产"
                    }
                };
            }

            string locationName = parameters["location"];
            
            // 查找匹配的位置
            var location = await _dbContext.Locations
                .FirstOrDefaultAsync(l => l.Name.Contains(locationName) || l.Code.Contains(locationName));
            
            if (location == null)
            {
                return new CommandResult
                {
                    Success = false,
                    Message = $"未找到名称包含\"{locationName}\"的位置",
                    Suggestions = new List<string>
                    {
                        "查询所有位置",
                        "查询所有资产"
                    }
                };
            }
            
            // 查询资产
            var assets = await _dbContext.Assets
                .Include(a => a.AssetType)
                .Include(a => a.Location)
                .Where(a => a.LocationId == location.Id)
                .OrderByDescending(a => a.CreatedAt)
                .Take(20)
                .ToListAsync();
            
            if (assets.Any() == false)
            {
                return new CommandResult
                {
                    Success = true,
                    Message = $"未找到位于 {locationName} 的资产",
                    Data = new List<object>()
                };
            }
            
            var result = assets.Select(a => new AssetDto
            {
                Id = a.Id,
                Name = a.Name,
                AssetType = a.AssetType?.Name,
                SerialNumber = a.SerialNumber,
                Location = a.Location?.Name,
                Status = a.Status,
                PurchaseDate = a.PurchaseDate
            }).ToList();
            
            return new CommandResult
            {
                Success = true,
                Message = $"找到 {assets.Count()} 个位于 {locationName} 的资产",
                Data = result,
                Suggestions = new List<string>
                {
                    "统计各位置资产数量",
                    "查询所有资产"
                }
            };
        }
        
        /// <summary>
        /// 按部门查询资产
        /// </summary>
        private async Task<CommandResult> QueryAssetsByDepartmentAsync(Dictionary<string, string> parameters)
        {
            if (!parameters.ContainsKey("department"))
            {
                return new CommandResult
                {
                    Success = false,
                    Message = "缺少部门参数",
                    Suggestions = new List<string>
                    {
                        "查询研发部的资产",
                        "查询市场部的资产",
                        "查询所有资产"
                    }
                };
            }

            string departmentName = parameters["department"];
            
            // 查找部门
            var department = await _dbContext.Departments
                .FirstOrDefaultAsync(d => d.Name.Contains(departmentName));
            
            if (department == null)
            {
                return new CommandResult
                {
                    Success = false,
                    Message = $"未找到名称包含\"{departmentName}\"的部门",
                    Suggestions = new List<string>
                    {
                        "查询所有部门",
                        "查询所有资产"
                    }
                };
            }
            
            // 通过位置关联查询资产
            var assets = await _dbContext.Assets
                .Include(a => a.Location)
                .Include(a => a.AssetType)
                .Where(a => a.Location.DefaultDepartmentId == department.Id)
                .ToListAsync();
            
            if (assets.Any() == false)
            {
                return new CommandResult
                {
                    Success = true,
                    Message = $"未找到属于 {departmentName} 的资产",
                    Data = new List<object>()
                };
            }
            
            var result = assets.Select(a => new AssetDto
            {
                Id = a.Id,
                Name = a.Name,
                AssetType = a.AssetType?.Name,
                SerialNumber = a.SerialNumber,
                Location = a.Location?.Name,
                Status = a.Status,
                PurchaseDate = a.PurchaseDate
            }).ToList();
            
            return new CommandResult
            {
                Success = true,
                Message = $"找到 {assets.Count()} 个属于 {departmentName} 的资产",
                Data = result,
                Suggestions = new List<string>
                {
                    "统计各部门资产数量",
                    "查询所有资产"
                }
            };
        }
        
        /// <summary>
        /// 按部门统计资产
        /// </summary>
        private async Task<CommandResult> StatAssetsByDepartmentAsync()
        {
            // 查询各部门资产数量
            var departmentAssets = await _dbContext.Locations
                .Where(l => l.DefaultDepartmentId != null)
                .Join(_dbContext.Assets,
                    loc => loc.Id,
                    asset => asset.LocationId,
                    (loc, asset) => new { DefaultDepartmentId = loc.DefaultDepartmentId.Value, Asset = asset })
                .GroupBy(x => x.DefaultDepartmentId)
                .Select(g => new
                {
                    DefaultDepartmentId = g.Key,
                    Count = g.Count()
                })
                .ToListAsync();
            
            // 获取部门名称
            var departments = await _dbContext.Departments.ToListAsync();
            
            var result = departmentAssets
                .Join(departments,
                    da => da.DefaultDepartmentId,
                    dept => dept.Id,
                    (da, dept) => new StatDto
                    {
                        Name = dept.Name,
                        Count = da.Count
                    })
                .OrderByDescending(x => x.Count)
                .ToList();
            
            return new CommandResult
            {
                Success = true,
                Message = "各部门资产统计",
                Data = result,
                Suggestions = new List<string>
                {
                    "统计各状态资产数量",
                    "查询IT部门的资产"
                }
            };
        }
        
        /// <summary>
        /// 按状态统计资产
        /// </summary>
        private async Task<CommandResult> StatAssetsByStatusAsync()
        {
            var stats = await _dbContext.Assets
                .GroupBy(a => a.Status)
                .Select(g => new
                {
                    Status = g.Key,
                    Count = g.Count()
                })
                .ToListAsync();
            
            var result = stats.Select(s => new StatDto
            {
                Name = MapStatusDisplayName(s.Status.ToString()),
                Count = s.Count
            }).OrderByDescending(s => s.Count).ToList();
            
            return new CommandResult
            {
                Success = true,
                Message = "各状态资产统计",
                Data = result,
                Suggestions = new List<string>
                {
                    "统计各部门资产数量",
                    "查询库存中的资产"
                }
            };
        }
        
        /// <summary>
        /// 查询最近故障
        /// </summary>
        private async Task<CommandResult> QueryRecentFaultsAsync(Dictionary<string, string> parameters)
        {
            int limit = 10;
            if (parameters.ContainsKey("limit") && int.TryParse(parameters["limit"], out int parsedLimit))
            {
                limit = parsedLimit;
            }
            
            var faults = await _dbContext.FaultRecords
                .Include(f => f.Asset)
                .Include(f => f.FaultType)
                .OrderByDescending(f => f.CreatedAt)
                .Take(limit)
                .ToListAsync();
            
            if (faults.Any() == false)
            {
                return new CommandResult
                {
                    Success = true,
                    Message = "未找到任何故障记录",
                    Data = new List<object>()
                };
            }
            
            var result = faults.Select(f => new FaultDto
            {
                Id = f.Id,
                Title = f.Title,
                Asset = f.Asset?.Name,
                FaultType = f.FaultType?.Name,
                Status = f.Status,
                Severity = f.Severity,
                ReportedAt = f.ReportTime
            }).ToList();
            
            return new CommandResult
            {
                Success = true,
                Message = $"找到 {faults.Count()} 条最近故障记录",
                Data = result,
                Suggestions = new List<string>
                {
                    "查询高优先级故障",
                    "查询正在处理的故障"
                }
            };
        }
        
        #endregion
        
        #region 辅助方法
        
        /// <summary>
        /// 加载命令模式
        /// </summary>
        private List<CommandPattern> LoadCommandPatterns()
        {
            try
            {
                if (File.Exists(_commandPatternsPath))
                {
                    string json = File.ReadAllText(_commandPatternsPath);
                    return JsonSerializer.Deserialize<List<CommandPattern>>(json, _jsonOptions);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加载命令模式失败");
            }
            
            return new List<CommandPattern>();
        }
        
        /// <summary>
        /// 保存命令模式
        /// </summary>
        private void SaveCommandPatterns(List<CommandPattern> patterns)
        {
            try
            {
                string json = JsonSerializer.Serialize(patterns, _jsonOptions);
                File.WriteAllText(_commandPatternsPath, json);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存命令模式失败");
            }
        }
        
        /// <summary>
        /// 加载命令历史
        /// </summary>
        private async Task<List<CommandHistoryEntry>> LoadCommandHistoryAsync()
        {
            try
            {
                if (File.Exists(_commandHistoryPath))
                {
                    string json = await File.ReadAllTextAsync(_commandHistoryPath);
                    return JsonSerializer.Deserialize<List<CommandHistoryEntry>>(json, _jsonOptions);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加载命令历史失败");
            }
            
            return new List<CommandHistoryEntry>();
        }
        
        /// <summary>
        /// 保存命令历史
        /// </summary>
        private async Task SaveCommandHistoryAsync(string userId, string commandText, bool success, string resultMessage)
        {
            try
            {
                var history = await LoadCommandHistoryAsync();
                
                // 添加新记录
                history.Add(new CommandHistoryEntry
                {
                    UserId = userId,
                    CommandText = commandText,
                    Success = success,
                    ResultMessage = resultMessage
                });
                
                // 保留最近1000条记录
                if (history.Count > 1000)
                {
                    history = history
                        .OrderByDescending(h => h.CreatedAt)
                        .Take(1000)
                        .ToList();
                }
                
                // 保存到文件
                string json = JsonSerializer.Serialize(history, _jsonOptions);
                await File.WriteAllTextAsync(_commandHistoryPath, json);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存命令历史失败");
            }
        }
        
        /// <summary>
        /// 创建默认命令模式
        /// </summary>
        private List<CommandPattern> CreateDefaultCommandPatterns()
        {
            return new List<CommandPattern>
            {
                new CommandPattern
                {
                    CommandType = "QueryAssets",
                    Pattern = @"(?:查(?:询|看)|显示)(?:所有)?(?:的)?资产(?:信息)?(?:(?:，|,)?最(?:多|新)(?:显示)?(?<limit>\d+)条)?",
                    Examples = new List<string>
                    {
                        "查询所有资产",
                        "显示所有资产信息",
                        "查看资产",
                        "查询资产信息，最多显示20条"
                    },
                    ParameterMapping = new Dictionary<string, string>
                    {
                        { "limit", "limit" }
                    }
                },
                new CommandPattern
                {
                    CommandType = "QueryAssetById",
                    Pattern = @"(?:查(?:询|看)|显示)(?:资产)?(?:ID|编号|序列号)为?(?<id>[a-zA-Z0-9-]+)(?:的)?(?:资产)?(?:信息)?",
                    Examples = new List<string>
                    {
                        "查询ID为ABC123的资产",
                        "查看序列号为SN2021001的资产信息",
                        "显示编号为XYZ-789的资产"
                    },
                    ParameterMapping = new Dictionary<string, string>
                    {
                        { "id", "id" }
                    }
                },
                new CommandPattern
                {
                    CommandType = "QueryAssetsByStatus",
                    Pattern = @"(?:查(?:询|看)|显示)(?:所有)?(?:状态(?:为|是))?(?<status>使用中|库存中|维护中|已处置|闲置|在用|维修|报废)(?:的)?(?:资产)?(?:信息)?",
                    Examples = new List<string>
                    {
                        "查询库存中的资产",
                        "显示维护中的资产信息",
                        "查看已处置的资产",
                        "查询闲置资产"
                    },
                    ParameterMapping = new Dictionary<string, string>
                    {
                        { "status", "status" }
                    }
                },
                new CommandPattern
                {
                    CommandType = "QueryAssetsByLocation",
                    Pattern = @"(?:查(?:询|看)|显示)(?:位于|在)?(?<location>[^的]+)(?:的)?(?:资产)?(?:信息)?",
                    Examples = new List<string>
                    {
                        "查询位于1楼的资产",
                        "显示在会议室的资产信息",
                        "查看总部大楼的资产"
                    },
                    ParameterMapping = new Dictionary<string, string>
                    {
                        { "location", "location" }
                    }
                },
                new CommandPattern
                {
                    CommandType = "QueryAssetsByDepartment",
                    Pattern = @"(?:查(?:询|看)|显示)(?<department>[^的]+)(?:部门)?(?:的)?(?:资产)?(?:信息)?",
                    Examples = new List<string>
                    {
                        "查询IT部门的资产",
                        "显示财务部的资产信息",
                        "查看人力资源部资产"
                    },
                    ParameterMapping = new Dictionary<string, string>
                    {
                        { "department", "department" }
                    }
                },
                new CommandPattern
                {
                    CommandType = "StatAssetsByDepartment",
                    Pattern = @"(?:统计|汇总)(?:各|所有)?(?:部门)?(?:的)?资产(?:数量|分布)?",
                    Examples = new List<string>
                    {
                        "统计各部门资产",
                        "汇总所有部门的资产数量",
                        "统计资产部门分布"
                    },
                    ParameterMapping = new Dictionary<string, string>()
                },
                new CommandPattern
                {
                    CommandType = "StatAssetsByStatus",
                    Pattern = @"(?:统计|汇总)(?:各|所有)?(?:状态)?(?:的)?资产(?:数量|分布)?",
                    Examples = new List<string>
                    {
                        "统计各状态资产",
                        "汇总所有状态的资产数量",
                        "统计资产状态分布"
                    },
                    ParameterMapping = new Dictionary<string, string>()
                },
                new CommandPattern
                {
                    CommandType = "QueryRecentFaults",
                    Pattern = @"(?:查(?:询|看)|显示)(?:最近|最新)?(?<limit>\d+)?(?:的)?(?:故障|问题)(?:记录)?",
                    Examples = new List<string>
                    {
                        "查询最近故障",
                        "显示最新10个故障记录",
                        "查看故障记录"
                    },
                    ParameterMapping = new Dictionary<string, string>
                    {
                        { "limit", "limit" }
                    }
                }
            };
        }
        
        /// <summary>
        /// 映射状态值
        /// </summary>
        private string MapStatusValue(string statusText)
        {
            statusText = statusText.ToLower();
            
            switch (statusText)
            {
                case "闲置":
                case "库存":
                case "未使用":
                    return "0";
                case "在用":
                case "使用中":
                case "已分配":
                    return "1";
                case "维修中":
                case "维修":
                case "故障":
                    return "2";
                case "报废":
                case "报废处理":
                case "已报废":
                    return "3";
                default:
                    return "0";
            }
        }
        
        /// <summary>
        /// 映射状态显示名称
        /// </summary>
        private string MapStatusDisplayName(string statusValue)
        {
            switch (statusValue)
            {
                case "0": return "闲置";
                case "1": return "在用";
                case "2": return "维修中";
                case "3": return "报废";
                default: return "未知";
            }
        }
        
        #endregion
    }
} 