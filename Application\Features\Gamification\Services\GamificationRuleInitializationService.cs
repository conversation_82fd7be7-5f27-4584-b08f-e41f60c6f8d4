// File: Application/Features/Gamification/Services/GamificationRuleInitializationService.cs
// Description: 游戏化规则初始化服务

using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using ItAssetsSystem.Application.Features.Gamification.Configuration;

namespace ItAssetsSystem.Application.Features.Gamification.Services
{
    /// <summary>
    /// 游戏化规则初始化服务
    /// </summary>
    public class GamificationRuleInitializationService : BackgroundService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<GamificationRuleInitializationService> _logger;
        private readonly GamificationOptions _options;

        public GamificationRuleInitializationService(
            IServiceProvider serviceProvider,
            ILogger<GamificationRuleInitializationService> logger,
            IOptions<GamificationOptions> options)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
            _options = options.Value;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            // 等待应用启动完成
            await Task.Delay(5000, stoppingToken);

            if (!_options.Enabled)
            {
                _logger.LogInformation("游戏化系统已禁用，跳过规则初始化");
                return;
            }

            try
            {
                await InitializeDefaultRulesAsync();
                _logger.LogInformation("游戏化规则初始化完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "游戏化规则初始化失败");
            }
        }

        /// <summary>
        /// 初始化默认规则
        /// </summary>
        private async Task InitializeDefaultRulesAsync()
        {
            using var scope = _serviceProvider.CreateScope();
            var ruleEngine = scope.ServiceProvider.GetRequiredService<IGamificationRuleEngine>();

            var defaultRules = CreateDefaultRules();

            foreach (var rule in defaultRules)
            {
                try
                {
                    var existingRules = await ruleEngine.GetApplicableRulesAsync(rule.EventType, rule.ClientId);
                    var ruleExists = false;

                    foreach (var existingRule in existingRules)
                    {
                        if (existingRule.Id == rule.Id)
                        {
                            ruleExists = true;
                            break;
                        }
                    }

                    if (!ruleExists)
                    {
                        var success = await ruleEngine.AddRuleAsync(rule);
                        if (success)
                        {
                            _logger.LogInformation("成功添加默认规则: {RuleId}", rule.Id);
                        }
                        else
                        {
                            _logger.LogWarning("添加默认规则失败: {RuleId}", rule.Id);
                        }
                    }
                    else
                    {
                        _logger.LogDebug("规则已存在，跳过: {RuleId}", rule.Id);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "处理默认规则时发生错误: {RuleId}", rule.Id);
                }
            }
        }

        /// <summary>
        /// 创建默认规则
        /// </summary>
        private GamificationRule[] CreateDefaultRules()
        {
            var defaultRewards = _options.DefaultRewards;
            var clientId = _options.ClientId;

            return new[]
            {
                // 任务创建规则
                new GamificationRule
                {
                    Id = "TASK_CREATED_DEFAULT",
                    Name = "任务创建默认奖励",
                    EventType = "TaskCreated",
                    Condition = "{}",
                    PointsReward = defaultRewards.TaskCreated.Points,
                    CoinsReward = defaultRewards.TaskCreated.Coins,
                    DiamondsReward = defaultRewards.TaskCreated.Diamonds,
                    ExperienceReward = defaultRewards.TaskCreated.Experience,
                    IsActive = true,
                    Priority = 100,
                    ClientId = clientId
                },

                // 任务完成规则
                new GamificationRule
                {
                    Id = "TASK_COMPLETED_DEFAULT",
                    Name = "任务完成默认奖励",
                    EventType = "TaskCompleted",
                    Condition = "{}",
                    PointsReward = defaultRewards.TaskCompleted.Points,
                    CoinsReward = defaultRewards.TaskCompleted.Coins,
                    DiamondsReward = defaultRewards.TaskCompleted.Diamonds,
                    ExperienceReward = defaultRewards.TaskCompleted.Experience,
                    IsActive = true,
                    Priority = 100,
                    ClientId = clientId
                },

                // 按时完成任务额外奖励
                new GamificationRule
                {
                    Id = "TASK_ONTIME_BONUS",
                    Name = "按时完成任务额外奖励",
                    EventType = "TaskCompleted",
                    Condition = "{\"IsOnTime\": true}",
                    PointsReward = defaultRewards.OnTimeBonus.Points,
                    CoinsReward = defaultRewards.OnTimeBonus.Coins,
                    DiamondsReward = defaultRewards.OnTimeBonus.Diamonds,
                    ExperienceReward = defaultRewards.OnTimeBonus.Experience,
                    IsActive = true,
                    Priority = 50, // 高优先级
                    ClientId = clientId
                },

                // 任务认领规则
                new GamificationRule
                {
                    Id = "TASK_CLAIMED_DEFAULT",
                    Name = "任务认领默认奖励",
                    EventType = "TaskClaimed",
                    Condition = "{}",
                    PointsReward = defaultRewards.TaskClaimed.Points,
                    CoinsReward = defaultRewards.TaskClaimed.Coins,
                    DiamondsReward = defaultRewards.TaskClaimed.Diamonds,
                    ExperienceReward = defaultRewards.TaskClaimed.Experience,
                    IsActive = true,
                    Priority = 100,
                    ClientId = clientId
                },

                // 资产更新规则
                new GamificationRule
                {
                    Id = "ASSET_UPDATED_DEFAULT",
                    Name = "资产更新默认奖励",
                    EventType = "AssetUpdated",
                    Condition = "{}",
                    PointsReward = defaultRewards.AssetUpdated.Points,
                    CoinsReward = defaultRewards.AssetUpdated.Coins,
                    DiamondsReward = defaultRewards.AssetUpdated.Diamonds,
                    ExperienceReward = defaultRewards.AssetUpdated.Experience,
                    IsActive = true,
                    Priority = 100,
                    ClientId = clientId
                },

                // 故障报告规则
                new GamificationRule
                {
                    Id = "FAULT_REPORTED_DEFAULT",
                    Name = "故障报告默认奖励",
                    EventType = "FaultReported",
                    Condition = "{}",
                    PointsReward = defaultRewards.FaultReported.Points,
                    CoinsReward = defaultRewards.FaultReported.Coins,
                    DiamondsReward = defaultRewards.FaultReported.Diamonds,
                    ExperienceReward = defaultRewards.FaultReported.Experience,
                    IsActive = true,
                    Priority = 100,
                    ClientId = clientId
                },

                // 严重故障额外奖励
                new GamificationRule
                {
                    Id = "FAULT_CRITICAL_BONUS",
                    Name = "严重故障额外奖励",
                    EventType = "FaultReported",
                    Condition = "{\"Severity\": \"Critical\"}",
                    PointsReward = 10,
                    CoinsReward = 5,
                    DiamondsReward = 1,
                    ExperienceReward = 10,
                    IsActive = true,
                    Priority = 50,
                    ClientId = clientId
                },

                // 每日登录规则
                new GamificationRule
                {
                    Id = "DAILY_LOGIN_DEFAULT",
                    Name = "每日登录默认奖励",
                    EventType = "DailyLogin",
                    Condition = "{}",
                    PointsReward = defaultRewards.DailyLogin.Points,
                    CoinsReward = defaultRewards.DailyLogin.Coins,
                    DiamondsReward = defaultRewards.DailyLogin.Diamonds,
                    ExperienceReward = defaultRewards.DailyLogin.Experience,
                    IsActive = true,
                    Priority = 100,
                    ClientId = clientId
                },

                // 连续登录奖励
                new GamificationRule
                {
                    Id = "CONSECUTIVE_LOGIN_BONUS",
                    Name = "连续登录奖励",
                    EventType = "DailyLogin",
                    Condition = "{\"ConsecutiveDays\": {\"$gte\": 7}}",
                    PointsReward = 5,
                    CoinsReward = 3,
                    DiamondsReward = 1,
                    ExperienceReward = 5,
                    IsActive = true,
                    Priority = 50,
                    ClientId = clientId
                },

                // 评论添加规则
                new GamificationRule
                {
                    Id = "COMMENT_ADDED_DEFAULT",
                    Name = "评论添加默认奖励",
                    EventType = "CommentAdded",
                    Condition = "{}",
                    PointsReward = defaultRewards.CommentAdded.Points,
                    CoinsReward = defaultRewards.CommentAdded.Coins,
                    DiamondsReward = defaultRewards.CommentAdded.Diamonds,
                    ExperienceReward = defaultRewards.CommentAdded.Experience,
                    IsActive = true,
                    Priority = 100,
                    ClientId = clientId
                },

                // 长评论额外奖励
                new GamificationRule
                {
                    Id = "LONG_COMMENT_BONUS",
                    Name = "长评论额外奖励",
                    EventType = "CommentAdded",
                    Condition = "{\"ContentLength\": {\"$gte\": 100}}",
                    PointsReward = 2,
                    CoinsReward = 1,
                    DiamondsReward = 0,
                    ExperienceReward = 2,
                    IsActive = true,
                    Priority = 50,
                    ClientId = clientId
                }
            };
        }
    }
}
