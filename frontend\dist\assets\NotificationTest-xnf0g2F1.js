import{_ as e,r as t,b as s,d as a,e as i,w as r,a as n,o as c,p as o,t as l,E as u}from"./index-CG5lHOPO.js";import{u as d,n as m}from"./notification-Cv7sgGe-.js";import{N as p}from"./NotificationCenter-R_b7DCjJ.js";import"./notification-service-w2_wgXl6.js";const f={class:"notification-test"},g={class:"test-results"},v=e({__name:"NotificationTest",setup(e){const v=t(!1),y=t({}),w=d(),S=async()=>{try{const e=await m.getNotifications();y.value.getNotifications={success:e.success,data:e.data,timestamp:(new Date).toISOString()},e.success?u.success(`获取成功，共${e.data.notifications.length}条通知`):u.error("获取失败: "+e.message)}catch(e){y.value.getNotifications={error:e.message,timestamp:(new Date).toISOString()},u.error("测试失败: "+e.message)}},_=async()=>{try{const e=await m.sendTestNotification();y.value.sendNotification={success:e.success,message:e.message,timestamp:(new Date).toISOString()},e.success?(u.success("测试通知发送成功"),setTimeout((()=>{w.fetchNotifications(!0)}),1e3)):u.error("发送失败: "+e.message)}catch(e){y.value.sendNotification={error:e.message,timestamp:(new Date).toISOString()},u.error("发送失败: "+e.message)}},N=async()=>{try{const e=await m.getUnreadCount();y.value.getUnreadCount={success:e.success,data:e.data,timestamp:(new Date).toISOString()},e.success?u.success(`未读通知数量: ${e.data}`):u.error("获取失败: "+e.message)}catch(e){y.value.getUnreadCount={error:e.message,timestamp:(new Date).toISOString()},u.error("获取失败: "+e.message)}};return(e,t)=>{const u=n("el-button"),d=n("el-space"),m=n("el-card");return c(),s("div",f,[t[9]||(t[9]=a("h2",null,"通知系统测试页面",-1)),i(m,{class:"test-card"},{header:r((()=>t[2]||(t[2]=[a("span",null,"API测试",-1)]))),default:r((()=>[i(d,{direction:"vertical",style:{width:"100%"}},{default:r((()=>[i(u,{onClick:S,type:"primary"},{default:r((()=>t[3]||(t[3]=[o(" 获取通知列表 ")]))),_:1}),i(u,{onClick:_,type:"success"},{default:r((()=>t[4]||(t[4]=[o(" 发送测试通知 ")]))),_:1}),i(u,{onClick:N,type:"info"},{default:r((()=>t[5]||(t[5]=[o(" 获取未读数量 ")]))),_:1})])),_:1})])),_:1}),i(m,{class:"test-card",style:{"margin-top":"20px"}},{header:r((()=>t[6]||(t[6]=[a("span",null,"通知中心组件测试",-1)]))),default:r((()=>[i(p,{mode:"drawer",visible:v.value,"onUpdate:visible":t[0]||(t[0]=e=>v.value=e)},null,8,["visible"]),i(u,{onClick:t[1]||(t[1]=e=>v.value=!0),type:"primary"},{default:r((()=>t[7]||(t[7]=[o(" 打开通知中心 (Drawer模式) ")]))),_:1})])),_:1}),i(m,{class:"test-card",style:{"margin-top":"20px"}},{header:r((()=>t[8]||(t[8]=[a("span",null,"测试结果",-1)]))),default:r((()=>[a("div",g,[a("pre",null,l(JSON.stringify(y.value,null,2)),1)])])),_:1})])}}},[["__scopeId","data-v-d638c4d9"]]);export{v as default};
