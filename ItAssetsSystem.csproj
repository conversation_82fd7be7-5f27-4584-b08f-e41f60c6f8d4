<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <LangVersion>11.0</LangVersion>
    <Nullable>disable</Nullable>
    <ImplicitUsings>disable</ImplicitUsings>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <NoWarn>$(NoWarn);1591</NoWarn>
    <SuppressTfmSupportBuildWarnings>true</SuppressTfmSupportBuildWarnings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="12.0.1" />
    <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.1" />
    <PackageReference Include="CronExpressionDescriptor" Version="2.41.0" />
    <PackageReference Include="CsvHelper" Version="33.0.1" />
    <PackageReference Include="EFCore.NamingConventions" Version="6.0.0" />
    <PackageReference Include="EPPlus" Version="7.7.0" />
    <PackageReference Include="itext7" Version="8.0.5" />
    <PackageReference Include="MediatR" Version="12.5.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Versioning" Version="5.1.0" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR" Version="1.2.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="6.0.7" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="6.0.7">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="6.0.7" />
    <PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="9.0.6" />
    <PackageReference Include="MySql.Data" Version="9.2.0" />
    <PackageReference Include="MySqlConnector" Version="2.4.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Pomelo.EntityFrameworkCore.MySql" Version="6.0.2" />
    <PackageReference Include="Serilog.AspNetCore" Version="6.1.0" />
    <PackageReference Include="Serilog.Sinks.Console" Version="4.1.0" />
    <PackageReference Include="Serilog.Sinks.File" Version="5.0.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.4.0" />
    <PackageReference Include="Microsoft.IdentityModel.Tokens" Version="6.32.1" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="6.32.1" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="6.0.20" />
    <PackageReference Include="Cronos" Version="0.8.3" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="logs\" />
  </ItemGroup>

  <ItemGroup>
    <Compile Remove="analyresport\**\*.cs" />
    <EmbeddedResource Remove="analyresport\**\*" />
    <None Remove="analyresport\**\*" />
    <Compile Remove="linshijiejian\**\*.cs" />
    <EmbeddedResource Remove="linshijiejian\**\*" />
    <None Remove="linshijiejian\**\*" />
  </ItemGroup>

  <ItemGroup>
    <!-- Exclude migration runner from main project compilation -->
    <Compile Remove="RunQuickMemoMigration.cs" />
  </ItemGroup>

</Project> 