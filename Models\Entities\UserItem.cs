// IT资产管理系统 - 用户道具实体
// 文件路径: /Models/Entities/UserItem.cs
// 功能: 定义用户道具系统数据结构

using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ItAssetsSystem.Models.Entities
{
    /// <summary>
    /// 道具类型枚举
    /// </summary>
    public enum ItemType
    {
        /// <summary>
        /// 武器（攻击型道具）
        /// </summary>
        Weapon = 1,
        
        /// <summary>
        /// 防具（防御型道具）
        /// </summary>
        Shield = 2,
        
        /// <summary>
        /// 回复（恢复型道具）
        /// </summary>
        Recovery = 3,
        
        /// <summary>
        /// 增益（buff型道具）
        /// </summary>
        Buff = 4,
        
        /// <summary>
        /// 特殊道具
        /// </summary>
        Special = 5
    }
    
    /// <summary>
    /// 武器等级枚举
    /// </summary>
    public enum WeaponLevel
    {
        /// <summary>
        /// 青铜级
        /// </summary>
        Bronze = 1,
        
        /// <summary>
        /// 白银级
        /// </summary>
        Silver = 2,
        
        /// <summary>
        /// 黄金级
        /// </summary>
        Gold = 3,
        
        /// <summary>
        /// 钻石级
        /// </summary>
        Diamond = 4,
        
        /// <summary>
        /// 传说级
        /// </summary>
        Legendary = 5
    }

    /// <summary>
    /// 用户道具实体类
    /// </summary>
    [Table("user_items")]
    public class UserItem
    {
        /// <summary>
        /// 记录ID
        /// </summary>
        [Key]
        [Column("id")]
        public int Id { get; set; }
        
        /// <summary>
        /// 用户ID
        /// </summary>
        [Required]
        [Column("user_id")]
        public int UserId { get; set; }
        
        /// <summary>
        /// 道具名称
        /// </summary>
        [Required]
        [Column("name")]
        [StringLength(50)]
        public string Name { get; set; }
        
        /// <summary>
        /// 道具类型
        /// </summary>
        [Column("type")]
        public ItemType Type { get; set; }
        
        /// <summary>
        /// 道具等级（对于武器）
        /// </summary>
        [Column("level")]
        public WeaponLevel? Level { get; set; }
        
        /// <summary>
        /// 道具数量
        /// </summary>
        [Column("count")]
        public int Count { get; set; } = 1;
        
        /// <summary>
        /// 道具属性值（攻击力/防御力/回复量）
        /// </summary>
        [Column("value")]
        public int Value { get; set; }
        
        /// <summary>
        /// 是否可用（有些道具可能会被禁用）
        /// </summary>
        [Column("is_enabled")]
        public bool IsEnabled { get; set; } = true;
        
        /// <summary>
        /// 过期时间（有些道具可能有时效性）
        /// </summary>
        [Column("expire_time")]
        public DateTime? ExpireTime { get; set; }
        
        /// <summary>
        /// 道具描述
        /// </summary>
        [Column("description")]
        [StringLength(200)]
        public string Description { get; set; }
        
        /// <summary>
        /// 获取时间
        /// </summary>
        [Column("created_at")]
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        
        /// <summary>
        /// 更新时间
        /// </summary>
        [Column("updated_at")]
        public DateTime UpdatedAt { get; set; } = DateTime.Now;
        
        /// <summary>
        /// 导航属性 - 用户
        /// </summary>
        public virtual User User { get; set; }
    }
} 