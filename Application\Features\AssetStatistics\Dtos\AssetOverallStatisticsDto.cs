namespace ItAssetsSystem.Application.Features.AssetStatistics.Dtos
{
    /// <summary>
    /// 资产总体统计DTO
    /// </summary>
    public class AssetOverallStatisticsDto
    {
        /// <summary>
        /// 资产总数
        /// </summary>
        public int TotalAssets { get; set; }

        /// <summary>
        /// 正常资产数量
        /// </summary>
        public int NormalAssets { get; set; }

        /// <summary>
        /// 故障资产数量
        /// </summary>
        public int FaultAssets { get; set; }

        /// <summary>
        /// 维修中资产数量
        /// </summary>
        public int MaintenanceAssets { get; set; }

        /// <summary>
        /// 闲置资产数量
        /// </summary>
        public int IdleAssets { get; set; }

        /// <summary>
        /// 报废资产数量
        /// </summary>
        public int ScrapAssets { get; set; }

        /// <summary>
        /// 正常率
        /// </summary>
        public decimal NormalRate { get; set; }

        /// <summary>
        /// 故障率
        /// </summary>
        public decimal FaultRate { get; set; }

        /// <summary>
        /// 资产类型数量
        /// </summary>
        public int AssetTypeCount { get; set; }

        /// <summary>
        /// 位置数量
        /// </summary>
        public int LocationCount { get; set; }

        /// <summary>
        /// 部门数量
        /// </summary>
        public int DepartmentCount { get; set; }
    }
}