// File: Services/Interfaces/IAchievementService.cs
// Description: 成就服务接口

using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ItAssetsSystem.Services.Interfaces
{
    /// <summary>
    /// 成就服务接口
    /// </summary>
    public interface IAchievementService
    {
        /// <summary>
        /// 检查用户成就进度
        /// </summary>
        /// <param name="userId">用户ID</param>
        Task CheckUserAchievementsAsync(int userId);

        /// <summary>
        /// 获取用户成就列表
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>用户成就列表</returns>
        Task<List<UserAchievementDto>> GetUserAchievementsAsync(int userId);

        /// <summary>
        /// 获取成就统计
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>成就统计信息</returns>
        Task<AchievementStatsDto> GetAchievementStatsAsync(int userId);

        /// <summary>
        /// 初始化用户成就进度
        /// </summary>
        /// <param name="userId">用户ID</param>
        Task InitializeUserAchievementsAsync(int userId);
    }

    /// <summary>
    /// 用户成就DTO
    /// </summary>
    public class UserAchievementDto
    {
        public long Id { get; set; }
        public int AchievementId { get; set; }
        public string Code { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string? Description { get; set; }
        public string? Icon { get; set; }
        public int Level { get; set; }
        public int RequiredValue { get; set; }
        public int CurrentValue { get; set; }
        public bool IsCompleted { get; set; }
        public DateTime AchievedAt { get; set; }
        public int Progress { get; set; } // 进度百分比
        public int RewardPoints { get; set; }
        public int RewardCoins { get; set; }
        public int RewardDiamonds { get; set; }
    }

    /// <summary>
    /// 成就统计DTO
    /// </summary>
    public class AchievementStatsDto
    {
        public int TotalAchievements { get; set; }
        public int CompletedAchievements { get; set; }
        public int InProgressAchievements { get; set; }
        public int CompletionRate { get; set; } // 完成率百分比
    }
}
