-- =====================================================
-- 修复采购事件同步脚本
-- 功能：重新处理失败的采购相关事件，确保同步到user_work_summary表
-- 创建时间：2025-06-27
-- =====================================================

START TRANSACTION;

-- 1. 重置失败的采购事件状态为Pending，以便重新处理
UPDATE user_behavior_events 
SET processing_status = 'Pending',
    failure_reason = NULL,
    retry_count = 0,
    last_processed_at = NULL
WHERE action_type IN ('PURCHASE_CREATED', 'PURCHASE_UPDATED') 
  AND processing_status = 'Failed';

-- 2. 重置处理中的采购事件状态为Pending（防止卡住）
UPDATE user_behavior_events 
SET processing_status = 'Pending',
    last_processed_at = NULL
WHERE action_type IN ('PURCHASE_CREATED', 'PURCHASE_UPDATED') 
  AND processing_status = 'Processing'
  AND last_processed_at < DATE_SUB(NOW(), INTERVAL 1 HOUR);

-- 3. 手动同步采购事件到user_work_summary表
-- 首先获取需要处理的事件
SELECT 
    ube.id as event_id,
    ube.user_id,
    ube.action_type,
    ube.points_earned,
    ube.coins_earned,
    ube.diamonds_earned,
    ube.xp_earned,
    ube.timestamp,
    YEARWEEK(ube.timestamp, 1) as week_key
FROM user_behavior_events ube
WHERE ube.action_type IN ('PURCHASE_CREATED', 'PURCHASE_UPDATED')
  AND ube.processing_status = 'Pending'
ORDER BY ube.user_id, ube.timestamp;

-- 4. 为每个用户创建或更新工作汇总记录
-- 注意：这里使用存储过程会更好，但为了简单起见，我们先查看数据

-- 查看当前user_work_summary中的采购统计
SELECT 
    user_id,
    user_name,
    period_date,
    procurements_created,
    procurements_updated,
    total_points_earned,
    total_coins_earned
FROM user_work_summary 
WHERE user_id IN (
    SELECT DISTINCT user_id 
    FROM user_behavior_events 
    WHERE action_type IN ('PURCHASE_CREATED', 'PURCHASE_UPDATED')
)
ORDER BY user_id, period_date;

-- 5. 检查是否有遗漏的采购事件
SELECT 
    ube.user_id,
    ube.action_type,
    COUNT(*) as event_count,
    SUM(ube.points_earned) as total_points,
    SUM(ube.coins_earned) as total_coins
FROM user_behavior_events ube
WHERE ube.action_type IN ('PURCHASE_CREATED', 'PURCHASE_UPDATED')
GROUP BY ube.user_id, ube.action_type
ORDER BY ube.user_id, ube.action_type;

-- 6. 验证同步状态
SELECT 
    'user_behavior_events' as source_table,
    action_type,
    COUNT(*) as count
FROM user_behavior_events 
WHERE action_type IN ('PURCHASE_CREATED', 'PURCHASE_UPDATED')
GROUP BY action_type

UNION ALL

SELECT 
    'user_work_summary' as source_table,
    'PURCHASE_CREATED' as action_type,
    SUM(procurements_created) as count
FROM user_work_summary

UNION ALL

SELECT 
    'user_work_summary' as source_table,
    'PURCHASE_UPDATED' as action_type,
    SUM(procurements_updated) as count
FROM user_work_summary;

COMMIT;

-- 执行完成提示
SELECT 'Purchase events synchronization fix completed!' as status;
