// File: Application/Features/SpareParts/Dtos/SparePartLocationDto.cs
// Description: 备品备件库位DTO

using System;

namespace ItAssetsSystem.Application.Features.SpareParts.Dtos
{
    /// <summary>
    /// 备品备件库位DTO
    /// </summary>
    public class SparePartLocationDto
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        public long Id { get; set; }
        
        /// <summary>
        /// 库位编号，如"A-1-1-1"
        /// </summary>
        public string Code { get; set; }
        
        /// <summary>
        /// 库位名称，如"A区-1号货架-1层-1格"
        /// </summary>
        public string Name { get; set; }
        
        /// <summary>
        /// 区域标识，如"A区", "B区"
        /// </summary>
        public string Area { get; set; }
        
        /// <summary>
        /// 库位描述
        /// </summary>
        public string Description { get; set; }
        
        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsActive { get; set; }
        
        /// <summary>
        /// 存放的备件数量
        /// </summary>
        public int PartsCount { get; set; }
        
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }
        
        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; }
    }
} 