<template>
  <div class="modern-factory-dashboard">
    <!-- Enhanced Header -->
    <header class="dashboard-header">
      <div class="header-container">
        <div class="brand-section">
          <div class="brand-icon">
            <el-icon size="32"><Cpu /></el-icon>
            <div class="icon-glow"></div>
          </div>
          <div class="brand-content">
            <h1 class="brand-title">智能制造监控系统</h1>
            <p class="brand-subtitle">实时工厂状态监控 • {{ stats.total }}个工位</p>
          </div>
          <div class="status-badge" :class="systemStatus">
            <div class="status-indicator"></div>
            <span>系统{{ systemStatusText }}</span>
          </div>
          <!-- 调试信息已移除 -->
        </div>

        <div class="header-actions">
          <!-- Enhanced Search -->
          <div class="search-container">
            <el-input
              v-model="searchTerm"
              placeholder="搜索工位编号或设备名称..."
              class="smart-search"
              clearable
              @input="handleSearch"
            >
              <template #prefix>
                <el-icon class="search-icon"><Search /></el-icon>
              </template>
            </el-input>

            <!-- Search Results Dropdown -->
            <div v-if="searchResults.length > 0 && searchTerm" class="search-results">
              <div
                v-for="result in searchResults.slice(0, 5)"
                :key="result.locationId"
                class="search-result-item"
                @click="selectSearchResult(result)"
              >
                <div class="result-status" :class="result.status"></div>
                <div class="result-content">
                  <span class="result-name">{{ result.locationName }}</span>
                  <span class="result-info">{{ result.efficiency }}% 效率</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Action Buttons -->
          <div class="action-buttons">
            <!-- Filter with Badge -->
            <el-popover placement="bottom" trigger="click" width="350" popper-class="filter-popover">
              <template #reference>
                <el-button
                  class="action-btn filter-btn"
                  :class="{ 'active': hasActiveFilters }"
                >
                  <el-icon><Filter /></el-icon>
                  <span>筛选</span>
                  <el-badge
                    v-if="activeFilterCount > 0"
                    :value="activeFilterCount"
                    class="filter-badge"
                  />
                </el-button>
              </template>

              <div class="filter-content">
                <div class="filter-section">
                  <label>状态筛选</label>
                  <el-checkbox-group v-model="statusFilters" @change="applyFilters">
                    <el-checkbox label="operational">正常运行</el-checkbox>
                    <el-checkbox label="warning">警告状态</el-checkbox>
                    <el-checkbox label="error">故障状态</el-checkbox>
                    <el-checkbox label="idle">空闲工位</el-checkbox>
                  </el-checkbox-group>
                </div>

                <div class="filter-section">
                  <label>效率范围</label>
                  <el-slider
                    v-model="efficiencyRange"
                    range
                    :min="0"
                    :max="100"
                    :step="5"
                    @change="applyFilters"
                  />
                  <div class="range-display">{{ efficiencyRange[0] }}% - {{ efficiencyRange[1] }}%</div>
                </div>

                <div class="filter-actions">
                  <el-button size="small" @click="resetFilters">重置</el-button>
                  <el-button size="small" type="primary" @click="applyFilters">应用</el-button>
                </div>
              </div>
            </el-popover>

            <!-- View Mode Toggle -->
            <el-button-group class="view-toggle">
              <el-button
                :class="{ 'active': viewMode === 'layout' }"
                @click="setViewMode('layout')"
                class="view-btn"
                title="布局视图"
              >
                <el-icon><Grid /></el-icon>
              </el-button>
              <el-button
                :class="{ 'active': viewMode === 'list' }"
                @click="setViewMode('list')"
                class="view-btn"
                title="列表视图"
              >
                <el-icon><List /></el-icon>
              </el-button>
            </el-button-group>

            <!-- Refresh Button -->
            <el-button
              @click="refreshData"
              :loading="refreshing"
              class="action-btn refresh-btn"
              :class="{ 'refreshing': refreshing }"
              title="刷新数据"
            >
              <el-icon class="refresh-icon"><Refresh /></el-icon>
            </el-button>

            <!-- Fullscreen Button -->
            <el-button
              @click="toggleFullScreen"
              class="action-btn fullscreen-btn"
              :class="{ 'active': isFullscreen }"
              :title="isFullscreen ? '退出全屏' : '全屏'"
            >
              <el-icon>
                <FullScreen v-if="!isFullscreen" />
                <Close v-else />
              </el-icon>
            </el-button>
          </div>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="dashboard-main">
      <!-- 加载状态 -->
      <div v-if="loading && !dataInitialized" class="loading-container">
        <el-icon class="loading-icon" size="48">
          <Cpu />
        </el-icon>
        <h3>正在加载工厂布局...</h3>
        <p>请稍候，系统正在初始化数据</p>
        <el-progress
          :percentage="50"
          :show-text="false"
          :stroke-width="4"
          color="#3d8fd1"
          style="width: 200px; margin-top: 20px;"
        />
      </div>

      <!-- 错误状态 -->
      <div v-else-if="initializationError" class="error-container">
        <el-icon class="error-icon" size="48">
          <Warning />
        </el-icon>
        <h3>加载失败</h3>
        <p>{{ initializationError }}</p>
        <el-button type="primary" @click="initDashboard">重新加载</el-button>
      </div>

      <!-- 主要内容 -->
      <transition name="fade-in" appear>
        <div v-show="dataInitialized" class="dashboard-container">
        <!-- Enhanced Stats Panel -->
        <div class="stats-panel">
          <!-- Real-time Overview -->
          <div class="stats-card overview-card">
            <div class="card-header">
              <h3 class="card-title">实时概览</h3>
              <div class="update-indicator">
                <div class="pulse-dot"></div>
                <span class="update-time">{{ formattedTime }}</span>
              </div>
            </div>

            <div class="stats-grid">
              <div
                v-for="(status, key) in statusCards"
                :key="key"
                class="modern-stat-card"
                :class="key"
                @click="filterByStatus(key)"
              >
                <div class="stat-visual">
                  <div class="stat-icon-container" :class="key">
                    <el-icon class="stat-icon">
                      <component :is="status.icon" />
                    </el-icon>
                    <div class="icon-glow" :class="key"></div>
                  </div>
                  <div class="stat-progress">
                    <div
                      class="progress-bar"
                      :class="key"
                      :style="{ width: getStatusPercent(key) + '%' }"
                    ></div>
                  </div>
                </div>
                <div class="stat-data">
                  <div class="stat-value">{{ stats[key] }}</div>
                  <div class="stat-label">{{ status.label }}</div>
                  <div v-if="key === 'operational'" class="stat-percent">
                    {{ stats.operationalPercent }}%
                  </div>
                </div>
              </div>
            </div>

            <!-- System Metrics -->
            <div class="system-metrics">
              <div class="metric-item">
                <div class="metric-value">{{ avgEfficiency }}%</div>
                <div class="metric-label">平均效率</div>
              </div>
              <div class="metric-item">
                <div class="metric-value">{{ totalAssets }}</div>
                <div class="metric-label">总设备数</div>
              </div>
            </div>
          </div>

          <!-- 重点监控已移除 -->

          <!-- Zone Statistics -->
          <div class="stats-card zone-card">
            <div class="card-header">
              <h3 class="card-title">区域统计</h3>
            </div>

            <div class="zone-list">
              <div
                v-for="(zone, zoneId) in zoneStats"
                :key="zoneId"
                class="zone-item"
              >
                <div class="zone-info">
                  <div class="zone-name">{{ getZoneName(zoneId) }}</div>
                  <div class="zone-metrics">
                    <span class="zone-total">{{ zone.total }}个工位</span>
                    <span class="zone-efficiency">{{ zone.efficiency }}%效率</span>
                  </div>
                </div>
                <div class="zone-status">
                  <div class="status-indicators">
                    <span class="status-dot operational" v-if="zone.operational > 0">{{ zone.operational }}</span>
                    <span class="status-dot warning" v-if="zone.warning > 0">{{ zone.warning }}</span>
                    <span class="status-dot error" v-if="zone.error > 0">{{ zone.error }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Factory Layout -->
        <div class="factory-layout">

          <!-- Layout View -->
          <div v-if="viewMode === 'layout'" class="factory-floor" :class="{ 'fullscreen': isFullscreen }">
            <!-- Factory Grid Background -->
            <svg class="factory-grid" :viewBox="canvasViewBox">
              <defs>
                <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
                  <path d="M 20 0 L 0 0 0 20" fill="none" stroke="rgba(107, 148, 214, 0.08)" stroke-width="1"/>
                </pattern>
                <!-- 渐变定义 -->
                <linearGradient id="operationalGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" style="stop-color:#22c55e;stop-opacity:1" />
                  <stop offset="100%" style="stop-color:#16a34a;stop-opacity:1" />
                </linearGradient>
                <linearGradient id="warningGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" style="stop-color:#eab308;stop-opacity:1" />
                  <stop offset="100%" style="stop-color:#ca8a04;stop-opacity:1" />
                </linearGradient>
                <linearGradient id="errorGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" style="stop-color:#ef4444;stop-opacity:1" />
                  <stop offset="100%" style="stop-color:#dc2626;stop-opacity:1" />
                </linearGradient>
              </defs>
              <rect width="100%" height="100%" fill="url(#grid)" />
            </svg>

            <!-- 动态区域容器布局系统 -->
            <div class="zone-containers" :style="containerStyle">
              <!-- 动态生成区域容器 -->
              <div
                v-for="zone in layoutConfig?.zones || []"
                :key="zone.id"
                class="zone-container modern-zone"
                :class="`zone-${zone.id}`"
                :data-zone="zone.name"
                :style="getZoneContainerStyle(zone)"
              >
                <!-- 区域信息标签 -->
                <div class="zone-info-label">
                  <div class="zone-title">{{ zone.name }}</div>
                  <div class="zone-debug" v-if="showDebugInfo">
                    {{ getZoneLayout(zone).rows }}x{{ getZoneLayout(zone).cols }}
                    位置:({{ getZonePosition(zone).x }},{{ getZonePosition(zone).y }})
                    尺寸:{{ getZonePosition(zone).width }}x{{ getZonePosition(zone).height }}
                    间距:{{ zone.gapX || 2 }}x{{ zone.gapY || 2 }}
                  </div>
                </div>

                <div class="zone-workstations modern-grid" :style="getZoneGridStyle(zone)">
                  <div
                    v-for="location in getSortedZoneWorkstations(zone)"
                    :key="location.locationId"
                    class="workstation-cell modern-workstation"
                    :class="getWorkstationClasses(location)"
                    :style="getWorkstationStyle(location, zone)"
                    @click="handleLocationClick(location)"
                    @mouseenter="handleHoverStart(location, $event)"
                    @mouseleave="handleHoverEnd"
                    v-show="isLocationVisible(location)"
                  >
                    <div class="cell-content">
                      <!-- 纯色块设计，工位背景色即为状态色 -->
                    </div>

                    <!-- 选中状态 -->
                    <div v-if="selectedLocationId === location.locationId" class="selected-border"></div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 状态图例已移除 -->
          </div>

          <!-- List View -->
          <div v-else class="factory-list">
            <el-table
              :data="filteredLocations"
              height="100%"
              @row-click="handleRowClick"
              highlight-current-row
            >
              <el-table-column prop="locationCode" label="工位编号" width="100" />
              <el-table-column prop="locationName" label="工位名称" min-width="120" />
              <el-table-column prop="departmentName" label="所属部门" min-width="120" />
              <el-table-column prop="efficiency" label="效率" width="80" align="center">
                <template #default="{ row }">
                  <span :class="getEfficiencyClass(row.efficiency)">{{ row.efficiency }}%</span>
                </template>
              </el-table-column>
              <el-table-column prop="status" label="状态" width="100" align="center">
                <template #default="{ row }">
                  <el-tag :type="getStatusTagType(row.status)" size="small">
                    {{ getStatusText(row.status) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="assetCount" label="设备数" width="80" align="center" />
              <el-table-column prop="taskCount" label="任务数" width="80" align="center" />
              <el-table-column prop="faultCount" label="故障数" width="80" align="center">
                <template #default="{ row }">
                  <span v-if="row.faultCount > 0" class="fault-count-text">{{ row.faultCount }}</span>
                  <span v-else class="no-fault">0</span>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="100" align="center">
                <template #default="{ row }">
                  <el-button size="small" @click.stop="selectWorkstation(row.locationId)">
                    详情
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
        </div>
      </transition>
    </main>

    <!-- 悬浮提示框 -->
    <div
      v-show="tooltipVisible"
      class="location-tooltip"
      :style="{ left: tooltipX + 'px', top: tooltipY + 'px' }"
    >
      <div class="tooltip-header">
        <div class="tooltip-title">{{ tooltipLocation?.locationName }}</div>
        <div class="tooltip-code">{{ tooltipLocation?.locationCode }}</div>
      </div>
      <div class="tooltip-content">
        <div class="tooltip-section">
          <div class="section-title">状态信息</div>
          <div class="status-info">
            <span class="status-badge" :class="tooltipLocation?.status">
              {{ getStatusText(tooltipLocation?.status) }}
            </span>
            <span class="efficiency-text">效率: {{ tooltipLocation?.efficiency }}%</span>
          </div>
        </div>
        <div class="tooltip-section" v-if="tooltipLocation?.departmentName">
          <div class="section-title">所属区域</div>
          <div class="department-name">{{ tooltipLocation.originalDepartmentName || tooltipLocation.departmentName }}</div>
        </div>
        <div class="tooltip-section">
          <div class="section-title">设备信息</div>
          <div class="asset-info">
            <span>设备数量: {{ tooltipLocation?.assetCount || 0 }}</span>
            <span>任务数量: {{ tooltipLocation?.taskCount || 0 }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Workstation Details Drawer -->
    <el-drawer
      v-model="showDetailsDrawer"
      title="工位详情"
      size="450px"
      direction="rtl"
    >
      <LocationDetailsModal
        v-if="selectedLocation"
        :location="selectedLocation"
        @close="closeLocationDetail"
      />
    </el-drawer>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick, shallowRef, defineComponent } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Cpu, Search, Filter, Refresh, FullScreen, Close,
  Check, Warning, Close as CloseIcon, Setting, ArrowRight,
  ZoomIn, ZoomOut, Grid, List, Upload
} from '@element-plus/icons-vue'

// 优化的性能监控模块
const performanceMonitor = {
  frameCount: 0,
  lastTime: performance.now(),
  fps: 0,
  raf: null,
  lastDegradation: 0,
  updateCounter: 0,
  backgroundLoadCount: 0,
  workstationRenderCount: 0,
  componentRerenderCount: 0,

  start() {
    this.frameCount = 0
    this.lastTime = performance.now()
    this.raf = requestAnimationFrame(this.tick.bind(this))
    // 监控背景加载
    this.monitorBackgroundLoading()
  },

  tick() {
    this.frameCount++
    this.updateCounter++
    const now = performance.now()
    const elapsed = now - this.lastTime

    // 减少计算频率，每2秒计算一次
    if (elapsed >= 2000) {
      this.fps = Math.round((this.frameCount * 1000) / elapsed)
      this.frameCount = 0
      this.lastTime = now

      // 减少UI更新频率，每4次计算更新一次
      if (this.updateCounter % 4 === 0) {
        this.updateUI()
      }

      // 减少性能检测频率
      if (this.updateCounter % 8 === 0 && this.fps < 50) {
        this.handlePerformanceDegradation()
      }
    }

    this.raf = requestAnimationFrame(this.tick.bind(this))
  },

  updateUI() {
    const fpsElement = document.getElementById('fps-counter')
    if (fpsElement) {
      fpsElement.textContent = `FPS: ${this.fps}`
      fpsElement.className = 'fps-counter ' + (this.fps > 55 ? 'fps-good' :
                            this.fps > 45 ? 'fps-medium' :
                            'fps-poor')
    }

    // 更新调试信息
    this.updateDebugInfo()
  },

  updateDebugInfo() {
    const bgLoadElement = document.getElementById('bg-load-count')
    const workstationElement = document.getElementById('workstation-render-count')
    const rerenderElement = document.getElementById('component-rerender-count')

    if (bgLoadElement) bgLoadElement.textContent = this.backgroundLoadCount
    if (workstationElement) workstationElement.textContent = this.workstationRenderCount
    if (rerenderElement) rerenderElement.textContent = this.componentRerenderCount
  },

  monitorBackgroundLoading() {
    // 监控CSS背景图片加载
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes' &&
            (mutation.attributeName === 'style' || mutation.attributeName === 'class')) {
          this.backgroundLoadCount++
        }
      })
    })

    // 监控主要容器的背景变化
    const containers = [
      '.modern-factory-dashboard',
      '.factory-layout',
      '.factory-floor',
      '.cell-background'
    ]

    containers.forEach(selector => {
      const elements = document.querySelectorAll(selector)
      elements.forEach(element => {
        observer.observe(element, {
          attributes: true,
          attributeFilter: ['style', 'class']
        })
      })
    })
  },

  incrementBackgroundLoad() {
    this.backgroundLoadCount++
  },

  incrementWorkstationRender() {
    this.workstationRenderCount++
  },

  incrementComponentRerender() {
    this.componentRerenderCount++
  },

  handlePerformanceDegradation() {
    if (performance.now() - this.lastDegradation > 10000) { // 增加到10秒
      document.documentElement.classList.add('low-fps-mode')
      this.lastDegradation = performance.now()
      // 移除console.warn以减少性能消耗
    }
  },

  stop() {
    if (this.raf) {
      cancelAnimationFrame(this.raf)
      this.raf = null
    }
  }
}
import LocationDetailsModal from './components/LocationDetailsModal.vue'

// 防抖工具函数
const debounce = (fn, delay) => {
  let timeoutId
  const debounced = (...args) => {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => fn.apply(this, args), delay)
  }
  debounced.cancel = () => clearTimeout(timeoutId)
  return debounced
}

// 调试函数 - 追踪渲染问题
const trackBackgroundLoad = (source = 'unknown') => {
  debugCounters.backgroundLoadCount++
  debugCounters.lastBackgroundLoad = new Date().toLocaleTimeString()
  // 减少console输出频率
  if (debugCounters.backgroundLoadCount % 5 === 1) {
    console.warn(`🔴 背景加载 #${debugCounters.backgroundLoadCount} - 来源: ${source}`, debugCounters.lastBackgroundLoad)
  }
  updateDebugDisplay()
}

const trackWorkstationRender = (source = 'unknown', count = 1) => {
  debugCounters.workstationRenderCount += count
  debugCounters.lastWorkstationRender = new Date().toLocaleTimeString()
  // 减少console输出频率
  if (debugCounters.workstationRenderCount % 10 === 1) {
    console.warn(`🟡 工位渲染 #${debugCounters.workstationRenderCount} - 来源: ${source}, 数量: ${count}`, debugCounters.lastWorkstationRender)
  }
  updateDebugDisplay()
}

const trackComponentRerender = (source = 'unknown') => {
  debugCounters.componentRerenderCount++
  debugCounters.lastComponentRerender = new Date().toLocaleTimeString()
  // 减少console输出频率
  if (debugCounters.componentRerenderCount % 5 === 1) {
    console.warn(`🟠 组件重渲染 #${debugCounters.componentRerenderCount} - 来源: ${source}`, debugCounters.lastComponentRerender)
  }
  updateDebugDisplay()
}

// 优化的调试显示更新（减少DOM操作频率）
let updateDebugDisplayCounter = 0
const updateDebugDisplay = () => {
  updateDebugDisplayCounter++
  // 减少DOM更新频率，每5次调用更新一次
  if (updateDebugDisplayCounter % 5 === 1) {
    nextTick(() => {
      const bgElement = document.getElementById('bg-load-count')
      const wsElement = document.getElementById('workstation-render-count')
      const crElement = document.getElementById('component-rerender-count')

      if (bgElement) bgElement.textContent = debugCounters.backgroundLoadCount
      if (wsElement) wsElement.textContent = debugCounters.workstationRenderCount
      if (crElement) crElement.textContent = debugCounters.componentRerenderCount
    })
  }
}

// 响应式数据 - 使用shallowRef减少深度监听
const locations = shallowRef([])
const loading = ref(false) // 改为false，避免初始闪烁
const refreshing = ref(false)
const configType = ref('custom')
const customConfigFile = ref('factory-layout.json') // 固定文件名
const layoutConfig = ref(null)
const lastUpdate = ref(new Date())
const importedFileName = ref('')
const importedFileContent = ref(null)
const uploadRef = ref(null)

// 新增的交互功能数据
const viewMode = ref('layout')
const isFullscreen = ref(false)
const zoomLevel = ref(1) // 默认100%缩放，配合调整后的基础缩放计算
const selectedLocationId = ref(null)
const selectedLocation = ref(null)
const showDetailsDrawer = ref(false)

// 筛选功能数据
const statusFilters = ref(['operational', 'warning', 'error', 'idle'])
const searchTerm = ref('')
const efficiencyRange = ref([0, 100])
const searchResults = ref([])

// 提示框相关
const tooltipVisible = ref(false)
const tooltipX = ref(0)
const tooltipY = ref(0)
const tooltipLocation = ref(null)

// 数据初始化状态
const dataInitialized = ref(false)
const initializationError = ref(null)

// 调试信息显示控制
const showDebugInfo = ref(false)

// 性能优化相关
const visibleWorkstations = ref(new Set())
const intersectionObserver = ref(null)

// 调试计数器 - 使用普通对象减少响应式开销
const debugCounters = {
  backgroundLoadCount: 0,
  workstationRenderCount: 0,
  componentRerenderCount: 0,
  lastBackgroundLoad: null,
  lastWorkstationRender: null,
  lastComponentRerender: null
}

// 系统状态
const systemStatus = ref('operational')
const systemStatusText = computed(() => {
  const statusMap = {
    operational: '正常运行',
    warning: '警告状态',
    offline: '离线状态'
  }
  return statusMap[systemStatus.value] || '未知状态'
})

// Status cards configuration
const statusCards = {
  operational: { icon: Check, label: '运行正常' },
  warning: { icon: Warning, label: '警告状态' },
  error: { icon: CloseIcon, label: '故障状态' },
  idle: { icon: Setting, label: '空闲工位' }
}

// 加载工厂布局配置
const loadLayoutConfig = async () => {
  try {
    console.log('🚀 开始加载工厂布局配置...')

    loading.value = true
    initializationError.value = null

    // 直接从固定位置加载JSON文件
    const configPath = `/analyresport/factory-layout.json`

    console.log(`🔍 加载配置文件: ${configPath}`)

    const response = await fetch(configPath)
    console.log('📡 响应状态:', response.status, response.statusText)
    console.log('📡 响应头Content-Type:', response.headers.get('content-type'))

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const responseText = await response.text()
    console.log('📄 响应内容前200字符:', responseText.substring(0, 200))
    console.log('📄 响应内容类型检查:', responseText.startsWith('{') ? 'JSON格式' : '非JSON格式')

    let config
    try {
      config = JSON.parse(responseText)
      console.log('✅ JSON解析成功')
    } catch (parseError) {
      console.error('❌ JSON解析错误:', parseError)
      console.error('响应内容:', responseText)
      throw new Error(`JSON解析失败: ${parseError.message}`)
    }

    if (config.factoryLayout) {
      layoutConfig.value = config.factoryLayout
    } else if (config.zones && Array.isArray(config.zones)) {
      layoutConfig.value = adaptCustomConfig(config)
    } else {
      throw new Error('未识别的配置文件格式')
    }

    console.log('布局配置加载成功:', layoutConfig.value)
    dataInitialized.value = true
    return layoutConfig.value
  } catch (error) {
    console.error('加载布局配置失败:', error)
    initializationError.value = error.message
    ElMessage.error(`加载布局配置失败: ${error.message}，使用默认配置`)
    layoutConfig.value = getDefaultLayoutConfig()
    dataInitialized.value = true
    return layoutConfig.value
  } finally {
    loading.value = false
  }
}

// 适配自定义配置格式
const adaptCustomConfig = (customConfig) => {
  // 首先转换区域格式
  const convertedZones = customConfig.zones.map(zone => ({
    ...zone,
    position: {
      x: zone.x,
      y: zone.y,
      width: zone.width,
      height: zone.height
    },
    layout: {
      rows: zone.rows,
      cols: zone.cols
    }
  }))

  // 应用智能重新布局算法
  const relayoutedZones = recalculateZoneLayout(convertedZones)

  // 严格按照JSON配置计算画布尺寸，保持等比例关系
  let maxX = 0, maxY = 0
  customConfig.zones.forEach(zone => {
    // 使用JSON配置中的原始坐标和尺寸
    const rightEdge = zone.x + zone.width
    const bottomEdge = zone.y + zone.height

    maxX = Math.max(maxX, rightEdge)
    maxY = Math.max(maxY, bottomEdge)
  })

  // 画布尺寸：严格按照JSON配置的边界，只添加少量边距保持比例
  const margin = 50 // 减少边距，保持原始比例关系
  const canvasWidth = maxX + margin
  const canvasHeight = maxY + margin

  console.log(`JSON配置边界: ${maxX} x ${maxY}`)
  console.log(`画布尺寸: ${canvasWidth} x ${canvasHeight}`)
  console.log(`保持JSON配置的等比例关系`)

  console.log(`重新布局后画布尺寸: ${canvasWidth} x ${canvasHeight}`)

  const adapted = {
    name: "自定义工厂布局",
    description: `包含${customConfig.zones.length}个区域的工厂布局`,
    version: customConfig.version || "1.0",
    canvas: {
      width: canvasWidth,
      height: canvasHeight,
      backgroundColor: "#0f172a",
      gridSize: 20
    },
    zones: [],
    statusDistribution: { operational: 0.7, warning: 0.15, error: 0.1, idle: 0.05 },
    defaultMetrics: {
      efficiency: { min: 70, max: 100 },
      uptime: { min: 80, max: 100 },
      assetCount: { min: 2, max: 7 },
      taskCount: { min: 1, max: 9 }
    }
  }

  // 使用重新布局后的区域信息
  relayoutedZones.forEach(zone => {
    const layout = getZoneLayout(zone)
    const position = getZonePosition(zone)
    const workstationCount = layout.rows * layout.cols
    const positions = []

    // 严格按照JSON配置的区域尺寸计算工位位置，保持等比例关系
    const zoneWidth = position.width
    const zoneHeight = position.height

    // 使用JSON配置中的gapX和gapY，如果没有则使用默认值
    const gapX = zone.gapX || 2
    const gapY = zone.gapY || 2

    // 计算每个工位的实际尺寸（考虑间距）
    // 这样计算确保工位在区域内均匀分布，与JSON配置的比例关系一致
    const cellWidth = (zoneWidth - (layout.cols - 1) * gapX) / layout.cols
    const cellHeight = (zoneHeight - (layout.rows - 1) * gapY) / layout.rows

    for (let row = 0; row < layout.rows; row++) {
      for (let col = 0; col < layout.cols; col++) {
        const workstationId = zone.startWorkstation + row * layout.cols + col

        // 计算工位在区域内的精确位置（考虑间距）
        const x = col * (cellWidth + gapX)
        const y = row * (cellHeight + gapY)

        positions.push({
          id: workstationId,
          x: x,
          y: y,
          width: cellWidth,
          height: cellHeight,
          name: `${zone.name}-工位${workstationId.toString().padStart(3, '0')}`
        })
      }
    }

    // 保持原始位置，只使用调整后的尺寸
    const finalX = position.x  // 保持原始X位置
    const finalY = position.y  // 保持原始Y位置
    const finalWidth = zone.adjustedWidth || position.width
    const finalHeight = zone.adjustedHeight || position.height

    adapted.zones.push({
      id: `zone${zone.id}`,
      name: zone.name,
      uniqueName: `${zone.name}-${zone.id}`, // 添加唯一标识符
      description: `${zone.name}生产区域`,
      color: zone.color,
      position: { x: finalX, y: finalY, width: finalWidth, height: finalHeight },
      layout: { type: "grid", rows: layout.rows, cols: layout.cols },
      workstations: { startId: zone.startWorkstation, count: workstationCount, pattern: "grid", positions: positions },
      // 保存调整信息，但不改变位置
      adjustedWidth: zone.adjustedWidth,
      adjustedHeight: zone.adjustedHeight
    })
  })

  console.log('区域重新布局完成:', adapted.zones.map(z => `${z.name}: (${z.position.x}, ${z.position.y}) ${z.position.width}x${z.position.height}`))
  console.log(`最终画布尺寸: ${canvasWidth} x ${canvasHeight}`)

  return adapted
}

// 默认布局配置
const getDefaultLayoutConfig = () => {
  return {
    name: "默认工厂布局",
    description: "包含1个区域的默认工厂布局",
    version: "1.0",
    canvas: {
      width: 1000,
      height: 600,
      backgroundColor: "#0f172a",
      gridSize: 20
    },
    zones: [{
      id: "zone1",
      name: "默认区域",
      uniqueName: "默认区域-1",
      description: "默认区域生产区域",
      color: "#4A90E2",
      position: { x: 100, y: 100, width: 300, height: 270 },
      layout: { type: "grid", rows: 3, cols: 3 },
      workstations: {
        startId: 1,
        count: 9,
        pattern: "grid",
        positions: Array.from({length: 9}, (_, i) => ({
          id: i + 1,
          x: (i % 3) * 100,
          y: Math.floor(i / 3) * 90,
          name: `默认区域-工位${(i + 1).toString().padStart(3, '0')}`
        }))
      }
    }],
    statusDistribution: { operational: 0.7, warning: 0.15, error: 0.1, idle: 0.05 },
    defaultMetrics: {
      efficiency: { min: 70, max: 100 },
      uptime: { min: 80, max: 100 },
      assetCount: { min: 2, max: 7 },
      taskCount: { min: 1, max: 9 }
    }
  }
}

// 根据JSON配置生成工位数据
const generateWorkstationDataFromLayout = () => {
  if (!layoutConfig.value) {
    trackWorkstationRender('配置为空，返回空数组', 0)
    return []
  }

  const workstations = []
  const statuses = ['operational', 'warning', 'error', 'idle']
  const distribution = layoutConfig.value.statusDistribution
  const statusWeights = [distribution.operational, distribution.warning, distribution.error, distribution.idle]

  layoutConfig.value.zones.forEach(zone => {
    // 根据区域的行列数生成对应数量的工位
    const layout = getZoneLayout(zone)
    const { rows, cols } = layout
    // 兼容新旧格式的startWorkstation
    const startId = zone.workstations?.startId || zone.startWorkstation || 1

    for (let row = 0; row < rows; row++) {
      for (let col = 0; col < cols; col++) {
        const workstationId = startId + row * cols + col

        const random = Math.random()
        let status = 'operational'
        let cumulative = 0

        for (let j = 0; j < statusWeights.length; j++) {
          cumulative += statusWeights[j]
          if (random < cumulative) {
            status = statuses[j]
            break
          }
        }

        const metrics = layoutConfig.value.defaultMetrics || {
          efficiency: { min: 70, max: 100 },
          uptime: { min: 80, max: 100 },
          assetCount: { min: 2, max: 7 },
          taskCount: { min: 1, max: 9 }
        }

        const uniqueName = `${zone.name}-${zone.id}`

        workstations.push({
          locationId: workstationId,
          locationName: `${zone.name}-工位${workstationId.toString().padStart(3, '0')}`,
          locationCode: `WS${workstationId.toString().padStart(3, '0')}`,
          departmentName: uniqueName, // 使用唯一标识符
          originalDepartmentName: zone.name, // 保留原始名称用于显示
          status: status,
          efficiency: Math.floor(Math.random() * (metrics.efficiency.max - metrics.efficiency.min + 1)) + metrics.efficiency.min,
          uptime: Math.floor(Math.random() * (metrics.uptime.max - metrics.uptime.min + 1)) + metrics.uptime.min,
          assetCount: Math.floor(Math.random() * (metrics.assetCount.max - metrics.assetCount.min + 1)) + metrics.assetCount.min,
          taskCount: Math.floor(Math.random() * (metrics.taskCount.max - metrics.taskCount.min + 1)) + metrics.taskCount.min,
          faultCount: status === 'error' ? Math.floor(Math.random() * 3) + 1 : status === 'warning' ? Math.floor(Math.random() * 2) : 0,
          lastUpdate: new Date(),
          zoneColor: zone.color,
          zoneId: zone.id,
          isHighlighted: false,
          // 添加在区域内的相对位置信息（确保逻辑正确）
          relativeRow: row, // 行索引（从0开始）
          relativeCol: col, // 列索引（从0开始）
          // 添加网格位置信息用于调试
          gridPosition: `${row + 1}-${col + 1}`, // 显示位置（从1开始）
          totalCols: cols, // 总列数
          totalRows: rows  // 总行数
        })
      }
    }
  })

  console.log(`生成了 ${workstations.length} 个工位数据`)
  console.log('工位分布详情:')
  layoutConfig.value.zones.forEach(zone => {
    const layout = getZoneLayout(zone)
    const startId = zone.workstations?.startId || zone.startWorkstation || 1
    const uniqueName = `${zone.name}-${zone.id}`

    const zoneWorkstations = workstations.filter(w => w.departmentName === uniqueName)
    console.log(`  ${zone.name}(${uniqueName}): ${zoneWorkstations.length}个工位 (${layout.rows}x${layout.cols})`)

    // 验证工位编号的连续性
    const workstationIds = zoneWorkstations.map(w => w.locationId).sort((a, b) => a - b)
    const expectedStart = startId
    const expectedEnd = expectedStart + layout.rows * layout.cols - 1
    console.log(`    工位编号范围: ${workstationIds[0]} - ${workstationIds[workstationIds.length - 1]} (期望: ${expectedStart} - ${expectedEnd})`)

    // 验证行列分布
    const rowCounts = {}
    const colCounts = {}
    zoneWorkstations.forEach(w => {
      rowCounts[w.relativeRow] = (rowCounts[w.relativeRow] || 0) + 1
      colCounts[w.relativeCol] = (colCounts[w.relativeCol] || 0) + 1
    })
    console.log(`    行分布:`, rowCounts, `列分布:`, colCounts)
  })

  // 减少追踪调用以提升性能

  return workstations
}

// 获取指定区域的工位列表
const getZoneWorkstations = (zoneName) => {
  return locations.value.filter(location => location.departmentName === zoneName)
}

// 使用缓存的工位数据映射，避免频繁重新计算
let cachedZoneWorkstationsMap = new Map()
let lastLocationsLength = 0
let lastLayoutConfigVersion = 0

// 计算属性：按区域分组的工位数据（使用缓存优化）
const zoneWorkstationsMap = computed(() => {
  const currentLocationsLength = locations.value.length
  const currentLayoutVersion = layoutConfig.value?.version || 0

  // 只有在数据真正变化时才重新计算
  if (currentLocationsLength === lastLocationsLength &&
      currentLayoutVersion === lastLayoutConfigVersion &&
      cachedZoneWorkstationsMap.size > 0) {
    return cachedZoneWorkstationsMap
  }

  const map = new Map()

  if (!layoutConfig.value?.zones) {
    cachedZoneWorkstationsMap = map
    return map
  }

  layoutConfig.value.zones.forEach(zone => {
    const uniqueName = `${zone.name}-${zone.id}`
    // 获取该区域的工位
    const zoneWorkstations = locations.value.filter(location => location.departmentName === uniqueName)

    // 按照行列位置排序
    const sortedWorkstations = zoneWorkstations.sort((a, b) => {
      if (a.relativeRow !== b.relativeRow) {
        return a.relativeRow - b.relativeRow
      }
      return a.relativeCol - b.relativeCol
    })

    map.set(uniqueName, sortedWorkstations)
  })

  // 更新缓存
  cachedZoneWorkstationsMap = map
  lastLocationsLength = currentLocationsLength
  lastLayoutConfigVersion = currentLayoutVersion

  // 只在真正重新计算时追踪（减少日志输出）
  console.log(`🔄 重新计算工位映射，共 ${locations.value.length} 个工位`)

  return map
})

// 获取排序后的区域工位列表（从计算属性中获取，避免重复计算）
const getSortedZoneWorkstations = (zone) => {
  const uniqueName = `${zone.name}-${zone.id}`
  return zoneWorkstationsMap.value.get(uniqueName) || []
}

// 工位样式类
const getWorkstationClasses = (location) => {
  return [`status-${location.status}`, { 'highlighted': location.isHighlighted }]
}

// 严格按照JSON配置保持等比例布局 - 不调整区域尺寸，保持原始比例关系
const recalculateZoneLayout = (zones) => {
  if (!zones || zones.length === 0) return zones

  const adjustedZones = zones.map(zone => {
    // 严格保持JSON配置中的原始尺寸和位置，不进行任何调整
    // 这样可以确保布局与JSON配置完全一致

    const position = getZonePosition(zone)
    const layout = getZoneLayout(zone)

    console.log(`区域 ${zone.name}:
      - 位置: (${position.x}, ${position.y})
      - 尺寸: ${position.width}x${position.height}
      - 布局: ${layout.rows}行 x ${layout.cols}列
      - 保持原始比例关系`)

    return {
      ...zone,
      // 完全保持原始尺寸和位置
      adjustedWidth: position.width,
      adjustedHeight: position.height,
      newX: position.x,
      newY: position.y,
      originalX: position.x,
      originalY: position.y
    }
  })

  return adjustedZones
}

// 检查两个矩形是否重叠
const checkRectangleOverlap = (x1, y1, w1, h1, x2, y2, w2, h2) => {
  return !(x1 + w1 <= x2 || x2 + w2 <= x1 || y1 + h1 <= y2 || y2 + h2 <= y1)
}

// 兼容性函数：获取区域位置信息（支持新旧两种JSON格式）
const getZonePosition = (zone) => {
  // 新格式：使用position对象
  if (zone.position) {
    return {
      x: zone.position.x,
      y: zone.position.y,
      width: zone.position.width,
      height: zone.position.height
    }
  }
  // 旧格式：直接在zone对象中
  return {
    x: zone.x || 0,
    y: zone.y || 0,
    width: zone.width || 100,
    height: zone.height || 100
  }
}

// 兼容性函数：获取区域布局信息（支持新旧两种JSON格式）
const getZoneLayout = (zone) => {
  // 新格式：使用layout对象
  if (zone.layout) {
    return {
      rows: zone.layout.rows || 1,
      cols: zone.layout.cols || 1
    }
  }
  // 旧格式：直接在zone对象中
  return {
    rows: zone.rows || 1,
    cols: zone.cols || 1
  }
}

// 获取区域容器样式 - 严格按照JSON配置的位置和尺寸
const getZoneContainerStyle = (zone) => {
  const position = getZonePosition(zone)

  // 严格使用JSON配置中的原始位置和尺寸，保持等比例关系
  const x = position.x
  const y = position.y
  const width = position.width
  const height = position.height

  return {
    position: 'absolute',
    left: `${x}px`,
    top: `${y}px`,
    width: `${width}px`,
    height: `${height}px`,
    border: `2px dashed ${zone.color}`,
    borderRadius: '8px',
    backgroundColor: `${zone.color}15`
  }
}

// 获取区域网格样式 - 严格按照JSON配置的间距设置
const getZoneGridStyle = (zone) => {
  const layout = getZoneLayout(zone)

  if (!layout || (layout.rows === 1 && layout.cols === 1)) {
    return {
      position: 'relative',
      height: '100%',
      width: '100%',
      padding: '0',
      boxSizing: 'border-box'
    }
  }

  const { rows, cols } = layout
  // 使用JSON配置中的间距值
  const gapX = zone.gapX || 2
  const gapY = zone.gapY || 2

  // 根据JSON配置计算每个工位的实际尺寸
  const position = getZonePosition(zone)
  const zoneWidth = position.width
  const zoneHeight = position.height

  // 计算每个工位的尺寸（减去间距）
  const cellWidth = (zoneWidth - (cols - 1) * gapX) / cols
  const cellHeight = (zoneHeight - (rows - 1) * gapY) / rows

  // 使用具体的像素值而不是1fr，确保工位大小与JSON配置一致
  const columnSizes = Array(cols).fill(`${cellWidth}px`).join(' ')
  const rowSizes = Array(rows).fill(`${cellHeight}px`).join(' ')

  return {
    display: 'grid',
    gridTemplateRows: rowSizes, // 使用计算出的实际尺寸
    gridTemplateColumns: columnSizes, // 使用计算出的实际尺寸
    gap: `${gapY}px ${gapX}px`, // 使用JSON配置的间距值
    height: '100%',
    width: '100%',
    padding: '0', // 不使用padding，严格按照JSON配置
    boxSizing: 'border-box',
    justifyContent: 'stretch', // 拉伸填满
    alignContent: 'stretch', // 拉伸填满
    // 确保网格项目不会溢出
    overflow: 'hidden'
  }
}

// 获取工位样式 - 确保在网格中正确显示，保持比例
const getWorkstationStyle = (location, zone) => {
  return {
    // 网格布局会自动处理位置和尺寸
    width: '100%',
    height: '100%',
    minWidth: '24px', // 减小最小尺寸，确保小区域也能显示
    minHeight: '24px',
    aspectRatio: '1', // 保持正方形比例
    // 确保工位内容居中
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    // 防止内容溢出
    overflow: 'hidden',
    // 确保在网格中的位置
    gridArea: 'auto',
    // 添加边框以便调试
    boxSizing: 'border-box'
  }
}

// 筛选后的位置数据 - 使用memoization优化
let lastFilteredResult = []
let lastFilterParams = { term: '', statuses: [], minEff: 0, maxEff: 100, locationsLength: 0 }

const filteredLocations = computed(() => {
  // 缓存计算所需值，减少重复访问
  const term = searchTerm.value.toLowerCase()
  const statuses = statusFilters.value
  const [minEff, maxEff] = efficiencyRange.value
  const locationsData = locations.value

  // 检查参数是否真的变化了
  const currentParams = {
    term,
    statuses: JSON.stringify(statuses),
    minEff,
    maxEff,
    locationsLength: locationsData.length
  }

  const paramsChanged = JSON.stringify(currentParams) !== JSON.stringify(lastFilterParams)

  if (!paramsChanged && lastFilteredResult.length > 0) {
    return lastFilteredResult
  }

  const result = locationsData.filter(location => {
    // 状态筛选
    if (statuses.length < 4 && !statuses.includes(location.status)) return false

    // 效率范围筛选
    if (location.efficiency < minEff || location.efficiency > maxEff) return false

    // 搜索筛选
    if (term &&
        !location.locationName.toLowerCase().includes(term) &&
        !location.locationCode.toLowerCase().includes(term) &&
        !location.locationId.toString().includes(term)) {
      return false
    }

    return true
  })

  // 更新缓存
  lastFilteredResult = result
  lastFilterParams = currentParams

  // 移除追踪以提升性能

  return result
})

// 筛选相关计算属性
const hasActiveFilters = computed(() => {
  return statusFilters.value.length < 4 ||
         searchTerm.value.length > 0 ||
         efficiencyRange.value[0] > 0 ||
         efficiencyRange.value[1] < 100
})

const activeFilterCount = computed(() => {
  let count = 0
  if (statusFilters.value.length < 4) count++
  if (searchTerm.value.length > 0) count++
  if (efficiencyRange.value[0] > 0 || efficiencyRange.value[1] < 100) count++
  return count
})

// 重点监控工位
const priorityWorkstations = computed(() => {
  return locations.value.filter(location =>
    location.status === 'error' ||
    location.status === 'warning' ||
    location.efficiency < 70
  ).sort((a, b) => {
    // 故障优先，然后按效率排序
    if (a.status === 'error' && b.status !== 'error') return -1
    if (b.status === 'error' && a.status !== 'error') return 1
    if (a.status === 'warning' && b.status !== 'warning') return -1
    if (b.status === 'warning' && a.status !== 'warning') return 1
    return a.efficiency - b.efficiency
  })
})

// 区域统计
const zoneStats = computed(() => {
  const stats = {}

  if (layoutConfig.value?.zones) {
    layoutConfig.value.zones.forEach(zone => {
      const zoneLocations = locations.value.filter(loc =>
        loc.zoneId === zone.id || loc.zoneName === zone.name
      )

      stats[zone.id] = {
        total: zoneLocations.length,
        operational: zoneLocations.filter(l => l.status === 'operational').length,
        warning: zoneLocations.filter(l => l.status === 'warning').length,
        error: zoneLocations.filter(l => l.status === 'error').length,
        idle: zoneLocations.filter(l => l.status === 'idle').length,
        efficiency: zoneLocations.length > 0
          ? Math.round(zoneLocations.reduce((sum, l) => sum + l.efficiency, 0) / zoneLocations.length)
          : 0
      }
    })
  }

  return stats
})

// 平均效率
const avgEfficiency = computed(() => {
  if (locations.value.length === 0) return 0
  const total = locations.value.reduce((sum, loc) => sum + loc.efficiency, 0)
  return Math.round(total / locations.value.length)
})

// 总设备数
const totalAssets = computed(() => {
  return locations.value.reduce((sum, loc) => sum + (loc.assetCount || 0), 0)
})

// 计算属性
const stats = computed(() => {
  const total = locations.value.length
  const operational = locations.value.filter(l => l.status === 'operational').length
  const warning = locations.value.filter(l => l.status === 'warning').length
  const error = locations.value.filter(l => l.status === 'error').length
  const idle = locations.value.filter(l => l.status === 'idle').length

  const result = {
    total,
    operational,
    warning,
    error,
    idle,
    operationalPercent: total > 0 ? Math.round(operational / total * 100) : 0
  }

  // 移除追踪以提升性能

  return result
})

// 总工位数
const totalWorkstations = computed(() => locations.value.length)

// 整体效率
const overallEfficiency = computed(() => {
  if (locations.value.length === 0) return 0
  const totalEfficiency = locations.value.reduce((sum, location) => sum + location.efficiency, 0)
  return totalEfficiency / locations.value.length
})

// 获取状态数量
const getStatusCount = (status) => {
  return locations.value.filter(l => l.status === status).length
}

const formattedTime = computed(() => {
  return lastUpdate.value.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
})

const canvasViewBox = computed(() => {
  if (layoutConfig.value && layoutConfig.value.canvas) {
    const { width, height } = layoutConfig.value.canvas
    // 确保viewBox足够大以容纳所有内容
    const viewWidth = Math.max(width, 1200)
    const viewHeight = Math.max(height, 600)
    return `0 0 ${viewWidth} ${viewHeight}`
  }
  return '0 0 1200 600'
})

// 计算缩放比例和容器样式 - 修复显示和居中问题
const containerStyle = computed(() => {
  if (!layoutConfig.value || !layoutConfig.value.canvas) {
    return {}
  }

  let { width: canvasWidth, height: canvasHeight } = layoutConfig.value.canvas

  // 严格按照JSON配置计算画布尺寸，保持等比例关系
  if (layoutConfig.value.zones) {
    let maxX = 0
    let maxY = 0

    layoutConfig.value.zones.forEach(zone => {
      const position = getZonePosition(zone)
      // 严格使用JSON配置中的原始尺寸
      const rightEdge = position.x + position.width
      const bottomEdge = position.y + position.height

      maxX = Math.max(maxX, rightEdge)
      maxY = Math.max(maxY, bottomEdge)
    })

    // 保持JSON配置的比例关系，只添加少量边距
    const margin = 50
    canvasWidth = Math.max(canvasWidth, maxX + margin)
    canvasHeight = Math.max(canvasHeight, maxY + margin)
  }

  // 计算合适的显示区域大小（动态获取容器尺寸）
  const displayWidth = 1200  // 增加显示区域宽度，适应100%缩放
  const displayHeight = 600  // 增加显示区域高度，适应100%缩放

  // 计算基础缩放比例，确保内容适合容器
  const scaleX = displayWidth / canvasWidth
  const scaleY = displayHeight / canvasHeight
  const baseScale = Math.min(scaleX, scaleY, 0.9) // 提高最大缩放为0.9，确保在100%时有合适大小

  // 应用用户缩放级别
  const finalScale = baseScale * zoomLevel.value

  console.log(`原始画布: ${layoutConfig.value.canvas.width}x${layoutConfig.value.canvas.height}, 调整后画布: ${canvasWidth}x${canvasHeight}, 基础缩放: ${baseScale}, 最终缩放: ${finalScale}`)

  return {
    width: `${canvasWidth}px`,
    height: `${canvasHeight}px`,
    transform: `scale(${finalScale})`,
    transformOrigin: 'center center', // 中心对齐，实现居中效果
    transition: 'transform 0.3s ease'
  }
})

// 文件选择处理
const handleFileSelection = (fileName) => {
  console.log('选择文件:', fileName)

  if (fileName === 'default') {
    configType.value = 'default'
    customConfigFile.value = ''
  } else {
    configType.value = 'custom'
    customConfigFile.value = fileName
  }

  // 清除导入的文件内容，使用服务器文件
  importedFileName.value = ''
  importedFileContent.value = null

  ElMessage.info(`已选择文件: ${fileName === 'default' ? '默认布局' : fileName}`)
}

// 重新加载布局
const reloadLayout = async () => {
  try {
    loading.value = true
    dataInitialized.value = false

    console.log('重新加载布局配置...')

    // 重新加载配置
    await loadLayoutConfig()

    // 重新生成工位数据
    if (layoutConfig.value) {
      locations.value = generateWorkstationDataFromLayout()
      lastUpdate.value = new Date()

      ElMessage.success('布局重新加载成功')
      console.log('布局重新加载完成')
    }
  } catch (error) {
    console.error('重新加载失败:', error)
    ElMessage.error(`重新加载失败: ${error.message}`)
  } finally {
    loading.value = false
  }
}

// 文件上传前的验证
const beforeUpload = (file) => {
  const isJSON = file.type === 'application/json' || file.name.endsWith('.json')
  if (!isJSON) {
    ElMessage.error('只能上传JSON格式的文件!')
    return false
  }

  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    ElMessage.error('文件大小不能超过10MB!')
    return false
  }

  return true
}

// 处理文件上传
const handleFileUpload = (file) => {
  console.log('上传文件:', file.name)

  if (!file.raw) {
    ElMessage.error('文件读取失败')
    return
  }

  const reader = new FileReader()
  reader.onload = (e) => {
    try {
      const content = e.target.result
      const jsonData = JSON.parse(content)

      // 验证JSON结构
      if (!jsonData.zones || !Array.isArray(jsonData.zones)) {
        throw new Error('JSON文件格式不正确，缺少zones数组')
      }

      importedFileName.value = file.name
      importedFileContent.value = jsonData
      configType.value = 'import'

      console.log('JSON文件解析成功:', jsonData)
      ElMessage.success(`文件 ${file.name} 上传成功`)

      // 自动加载新配置
      loadLayoutConfig().then(() => {
        locations.value = generateWorkstationDataFromLayout()
        lastUpdate.value = new Date()

        // 清空上传组件
        if (uploadRef.value) {
          uploadRef.value.clearFiles()
        }
      })

    } catch (error) {
      console.error('JSON文件解析失败:', error)
      ElMessage.error(`JSON文件解析失败: ${error.message}`)
      importedFileName.value = ''
      importedFileContent.value = null

      // 清空上传组件
      if (uploadRef.value) {
        uploadRef.value.clearFiles()
      }
    }
  }

  reader.onerror = () => {
    ElMessage.error('文件读取失败')
    importedFileName.value = ''
    importedFileContent.value = null

    // 清空上传组件
    if (uploadRef.value) {
      uploadRef.value.clearFiles()
    }
  }

  reader.readAsText(file.raw)
}

// 兼容旧的文件处理函数
const handleFileChange = handleFileUpload

// 加载默认JSON文件
const loadDefaultJson = async () => {
  try {
    console.log('手动加载默认JSON文件...')
    const success = await autoLoadExistingJson()

    if (success) {
      // 重新加载配置
      await loadLayoutConfig()
      locations.value = generateWorkstationDataFromLayout()
      lastUpdate.value = new Date()
    } else {
      ElMessage.warning('默认JSON文件不存在，请选择其他文件或使用默认布局')
    }
  } catch (error) {
    console.error('加载默认JSON文件失败:', error)
    ElMessage.error('加载默认JSON文件失败')
  }
}

// 新增的交互方法
const isLocationVisible = (location) => {
  return filteredLocations.value.includes(location)
}

// 检查工位是否在视口中（用于性能优化）
const isWorkstationInViewport = (locationId) => {
  return visibleWorkstations.value.has(locationId.toString())
}

const getEfficiencyColor = (efficiency) => {
  if (efficiency >= 90) return '#10b981'  // operational green
  if (efficiency >= 80) return '#3b82f6'  // good blue  
  if (efficiency >= 70) return '#f59e0b'  // warning yellow
  return '#ef4444'  // error red
}

const getStatusText = (status) => {
  const statusMap = {
    operational: '正常运行',
    warning: '警告状态',
    error: '故障状态',
    idle: '空闲状态'
  }
  return statusMap[status] || '未知状态'
}

const getStatusPercent = (status) => {
  const total = stats.value.total
  return total > 0 ? (stats.value[status] / total * 100) : 0
}

const getZoneName = (zoneId) => {
  const zoneNames = {
    zone1: '区域1',
    zone2: '区域2',
    zone3: '区域3',
    zone4: '区域4',
    zone5: '区域5',
    zone6: '区域6',
    zone7: '区域7'
  }
  return zoneNames[zoneId] || '未知区域'
}

// 搜索功能
const handleSearch = (value) => {
  if (!value) {
    searchResults.value = []
    return
  }

  const term = value.toLowerCase()
  searchResults.value = locations.value.filter(location =>
    location.locationName.toLowerCase().includes(term) ||
    location.locationCode.toLowerCase().includes(term) ||
    location.locationId.toString().includes(term)
  ).slice(0, 10)
}

const selectSearchResult = (result) => {
  searchTerm.value = ''
  searchResults.value = []
  selectWorkstation(result.locationId)
}

// 筛选功能
const filterByStatus = (status) => {
  statusFilters.value = [status]
  applyFilters()
}

const setViewMode = (mode) => {
  viewMode.value = mode
}

// 缩放控制
const zoomIn = () => {
  if (zoomLevel.value < 2) {
    zoomLevel.value = Math.min(zoomLevel.value + 0.2, 2)
  }
}

const zoomOut = () => {
  if (zoomLevel.value > 0.5) {
    zoomLevel.value = Math.max(zoomLevel.value - 0.2, 0.5)
  }
}

const resetZoom = () => {
  zoomLevel.value = 1
}

// 全屏控制
const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value
  if (isFullscreen.value) {
    document.documentElement.requestFullscreen?.()
  } else {
    document.exitFullscreen?.()
  }
}

// 筛选控制
const applyFilters = () => {
  // 筛选逻辑已在计算属性中处理
  console.log('应用筛选:', {
    statusFilters: statusFilters.value,
    searchTerm: searchTerm.value,
    efficiencyRange: efficiencyRange.value
  })
}

const resetFilters = () => {
  statusFilters.value = ['operational', 'warning', 'error', 'idle']
  searchTerm.value = ''
  efficiencyRange.value = [0, 100]
}

// 提示框控制 - 使用防抖优化
const showTooltip = (location, event) => {
  tooltipLocation.value = location
  tooltipX.value = event.clientX + 10
  tooltipY.value = event.clientY - 50
  tooltipVisible.value = true
}

const hideTooltip = () => {
  tooltipVisible.value = false
  tooltipLocation.value = null
}

// 防抖的hover处理
const debouncedShowTooltip = debounce((location, event) => {
  showTooltip(location, event)
}, 150)

const handleHoverStart = (location, event) => {
  debouncedShowTooltip(location, event)
}

const handleHoverEnd = () => {
  // 取消等待中的提示显示
  debouncedShowTooltip.cancel?.()
  hideTooltip()
}

// 工位选择
const selectWorkstation = (locationId) => {
  console.log('选择工位:', locationId)
  selectedLocationId.value = locationId

  const location = locations.value.find(loc => loc.locationId === locationId)
  if (location) {
    selectedLocation.value = {
      ...location,
      locationPath: `${location.originalDepartmentName || location.departmentName} / ${location.locationName}`,
      effectiveDepartmentName: location.originalDepartmentmentName || location.departmentName,
      directDepartmentName: location.departmentName,
      details: {
        temperature: Math.round(Math.random() * 20 + 20) + '°C',
        pressure: Math.round(Math.random() * 10 + 95) + 'kPa',
        uptime: Math.round(Math.random() * 20 + 80) + '%',
        lastMaintenance: '2天前',
        errorCode: location.status === 'error' ? 'E001' : null,
        warningMessage: location.status === 'warning' ? '温度偏高，请注意监控' : null
      },
      assets: [
        { assetId: 1, assetCode: 'NC001', assetName: '数控机床', assetTypeName: '加工设备' },
        { assetId: 2, assetCode: 'QI002', assetName: '质检仪', assetTypeName: '检测设备' }
      ]
    }
    showDetailsDrawer.value = true
  }
}

// 事件处理
const handleLocationClick = (location) => {
  selectWorkstation(location.locationId)
}

const handleRowClick = (row) => {
  selectWorkstation(row.locationId)
}

const closeLocationDetail = () => {
  selectedLocation.value = null
  selectedLocationId.value = null
  showDetailsDrawer.value = false
}

// 防抖的数据刷新
const debouncedRefresh = debounce(async () => {
  refreshing.value = true
  try {
    await loadLayoutConfig()
    locations.value = generateWorkstationDataFromLayout()
    lastUpdate.value = new Date()
    ElMessage.success('数据已刷新')
  } catch (error) {
    console.error('刷新失败:', error)
    ElMessage.error('刷新失败')
  } finally {
    refreshing.value = false
  }
}, 500)

// 刷新数据
const refreshData = () => {
  debouncedRefresh()
}

// 全屏控制
const toggleFullScreen = () => {
  isFullscreen.value = !isFullscreen.value
  if (isFullscreen.value) {
    document.documentElement.requestFullscreen?.()
  } else {
    document.exitFullscreen?.()
  }
}

// 表格相关方法
const getEfficiencyClass = (efficiency) => {
  if (efficiency >= 80) return 'efficiency-high'
  if (efficiency >= 60) return 'efficiency-medium'
  return 'efficiency-low'
}

const getStatusTagType = (status) => {
  const typeMap = {
    operational: 'success',
    warning: 'warning',
    error: 'danger',
    idle: 'info'
  }
  return typeMap[status] || 'info'
}

// 获取状态样式类
const getStatusClass = (status) => {
  return status || 'operational'
}

const handleRefresh = async () => {
  refreshing.value = true
  try {
    await loadLayoutConfig()
    locations.value = generateWorkstationDataFromLayout()
    lastUpdate.value = new Date()
    ElMessage.success('数据已刷新')
  } catch (error) {
    console.error('刷新失败:', error)
    ElMessage.error('刷新失败')
  } finally {
    refreshing.value = false
  }
}

const handleConfigChange = async () => {
  try {
    let configName = ''
    switch (configType.value) {
      case 'default':
        configName = '默认'
        break
      case 'custom':
        configName = '自定义'
        break
      case 'import':
        configName = '导入文件'
        // 清空之前的导入数据，等待用户选择新文件
        if (!importedFileContent.value) {
          importedFileName.value = ''
          ElMessage.info('请选择JSON文件进行导入')
          return
        }
        break
    }

    console.log(`切换到${configName}布局配置`)
    await loadLayoutConfig()
    locations.value = generateWorkstationDataFromLayout()
    lastUpdate.value = new Date()
    ElMessage.success(`已切换到${configName}布局: ${locations.value.length}个工位`)
  } catch (error) {
    console.error('切换配置失败:', error)
    ElMessage.error('切换配置失败')
  }
}

// 自动加载现有JSON文件
const autoLoadExistingJson = async () => {
  try {
    console.log('尝试自动加载现有JSON文件...')
    const response = await fetch(`/analyresport/${customConfigFile.value}`)

    if (response.ok) {
      const jsonData = await response.json()
      importedFileName.value = customConfigFile.value
      importedFileContent.value = jsonData
      console.log('自动加载JSON文件成功:', customConfigFile.value)
      ElMessage.success(`自动加载布局文件: ${customConfigFile.value}`)
      return true
    }
  } catch (error) {
    console.log('自动加载JSON文件失败，将使用默认配置:', error.message)
  }
  return false
}

// 初始化
const initDashboard = async () => {
  try {
    console.log('开始初始化工厂仪表板...')

    // 避免重复初始化
    if (dataInitialized.value) {
      console.log('数据已初始化，跳过重复初始化')
      return
    }

    loading.value = true
    initializationError.value = null

    // 如果是导入模式，尝试自动加载现有JSON文件
    if (configType.value === 'import') {
      await autoLoadExistingJson()
    }

    // 加载布局配置
    await loadLayoutConfig()

    // 生成工位数据
    if (layoutConfig.value) {
      locations.value = generateWorkstationDataFromLayout()
      console.log(`工厂仪表板初始化完成: ${locations.value.length}个工位`)

      // 标记初始化完成
      dataInitialized.value = true

      // 定时更新数据（只设置一次）
      if (!window.factoryDashboardTimer) {
        window.factoryDashboardTimer = setInterval(() => {
          lastUpdate.value = new Date()
          // 可以在这里添加数据刷新逻辑
        }, 5000)
      }
    } else {
      throw new Error('布局配置加载失败')
    }
  } catch (error) {
    console.error('初始化工厂仪表板失败:', error)
    initializationError.value = error.message
    ElMessage.error('初始化失败，请刷新页面重试')
  } finally {
    loading.value = false
  }
}

// 计算属性：区域统计信息（避免模板中重复计算）
const zoneStatsMap = computed(() => {
  const statsMap = new Map()

  if (!layoutConfig.value?.zones) return statsMap

  layoutConfig.value.zones.forEach(zone => {
    const workstations = zoneWorkstationsMap.value.get(zone.uniqueName) || []
    const count = workstations.length

    let efficiency = 0
    if (count > 0) {
      const totalEfficiency = workstations.reduce((sum, ws) => sum + (ws.efficiency || 0), 0)
      efficiency = Math.round(totalEfficiency / count)
    }

    statsMap.set(zone.uniqueName, {
      count,
      efficiency
    })
  })

  return statsMap
})

// 辅助函数（从计算属性获取，不再重复计算）
const getZoneWorkstationCount = (zone) => {
  return zoneStatsMap.value.get(zone.uniqueName)?.count || 0
}

const getZoneEfficiency = (zone) => {
  return zoneStatsMap.value.get(zone.uniqueName)?.efficiency || 0
}

const getShortLocationName = (locationName) => {
  if (!locationName) return ''
  // 截取名称，避免过长
  return locationName.length > 10 ? locationName.substring(0, 8) + '...' : locationName
}

const getEfficiencyLevel = (efficiency) => {
  if (efficiency >= 90) return 'excellent'
  if (efficiency >= 80) return 'good'
  if (efficiency >= 70) return 'average'
  return 'poor'
}


// IntersectionObserver初始化
const initIntersectionObserver = () => {
  if (intersectionObserver.value) {
    intersectionObserver.value.disconnect()
  }

  intersectionObserver.value = new IntersectionObserver(
    (entries) => {
      entries.forEach(entry => {
        const workstationId = entry.target.dataset.workstationId
        if (entry.isIntersecting) {
          visibleWorkstations.value.add(workstationId)
        } else {
          visibleWorkstations.value.delete(workstationId)
        }
      })
    },
    {
      rootMargin: '50px',
      threshold: 0.1
    }
  )
}

// 观察工位元素
const observeWorkstation = (element, workstationId) => {
  if (intersectionObserver.value && element) {
    element.dataset.workstationId = workstationId
    intersectionObserver.value.observe(element)
  }
}

// 数据预加载和缓存
const preloadData = () => {
  // 预设默认数据，避免闪烁
  if (!dataInitialized.value) {
    layoutConfig.value = getDefaultLayoutConfig()
    locations.value = generateWorkstationDataFromLayout()
  }
}

// 移除重复的FPS监控（已由performanceMonitor处理）

// 生命周期
onMounted(async () => {
  // 启动性能监控
  performanceMonitor.start()

  // 初始化IntersectionObserver
  initIntersectionObserver()

  // 立即预加载默认数据
  preloadData()

  // 然后异步加载真实数据
  await initDashboard()
})

// 组件卸载时清理定时器
onUnmounted(() => {
  // 停止性能监控
  performanceMonitor.stop()

  // 清理IntersectionObserver
  if (intersectionObserver.value) {
    intersectionObserver.value.disconnect()
    intersectionObserver.value = null
  }

  if (window.factoryDashboardTimer) {
    clearInterval(window.factoryDashboardTimer)
    window.factoryDashboardTimer = null
  }
})
</script>

<style scoped>
/* Import the enhanced styles from our previous dashboard */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

:root {
  /* Industrial base colors */
  --color-industrial-900: #0c1a25;
  --color-industrial-800: #122738;
  --color-industrial-700: #1a3650;
  --color-industrial-600: #23527c;
  --color-industrial-500: #2e6da4;
  --color-industrial-400: #3d8fd1;
  --color-industrial-300: #66b0ff;
  
  /* Enhanced status colors with sophisticated variations */
  --color-status-operational: #10b981;
  --color-status-operational-dark: #059669;
  --color-status-operational-light: #34d399;
  
  --color-status-warning: #f59e0b;
  --color-status-warning-dark: #d97706;
  --color-status-warning-light: #fbbf24;
  
  --color-status-error: #ef4444;
  --color-status-error-dark: #dc2626;
  --color-status-error-light: #f87171;
  
  --color-status-idle: #3b82f6;
  --color-status-idle-dark: #2563eb;
  --color-status-idle-light: #60a5fa;
  
  /* Advanced gradient combinations */
  --gradient-operational: linear-gradient(135deg, var(--color-status-operational), var(--color-status-operational-dark));
  --gradient-warning: linear-gradient(135deg, var(--color-status-warning), var(--color-status-warning-dark));
  --gradient-error: linear-gradient(135deg, var(--color-status-error), var(--color-status-error-dark));
  --gradient-idle: linear-gradient(135deg, var(--color-status-idle), var(--color-status-idle-dark));
  
  /* Glow effects for modern aesthetics */
  --glow-operational: rgba(16, 185, 129, 0.6);
  --glow-warning: rgba(245, 158, 11, 0.6);
  --glow-error: rgba(239, 68, 68, 0.6);
  --glow-idle: rgba(59, 130, 246, 0.6);
  
  /* Background variations with transparency */
  --bg-operational: linear-gradient(135deg, rgba(34, 197, 94, 0.15), rgba(16, 185, 129, 0.05));
  --bg-warning: linear-gradient(135deg, rgba(245, 158, 11, 0.15), rgba(217, 119, 6, 0.05));
  --bg-error: linear-gradient(135deg, rgba(239, 68, 68, 0.15), rgba(220, 38, 38, 0.05));
  --bg-idle: linear-gradient(135deg, rgba(59, 130, 246, 0.15), rgba(37, 99, 235, 0.05));
  
  /* Efficiency level colors */
  --color-efficiency-excellent: #10b981;
  --color-efficiency-good: #3b82f6;
  --color-efficiency-average: #f59e0b;
  --color-efficiency-poor: #ef4444;
}

.modern-factory-dashboard {
  min-height: 100vh;
  background: linear-gradient(135deg, #1e293b 0%, #334155 30%, #475569 70%, #64748b 100%);
  color: #e2e8f0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  position: relative;
  overflow-x: hidden;
}

/* Background pattern - 优化背景加载 */
.modern-factory-dashboard::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(circle at 1px 1px, rgba(66, 153, 225, 0.03) 1px, transparent 0),
    radial-gradient(circle at 1px 1px, rgba(66, 153, 225, 0.015) 1px, transparent 0);
  background-size: 60px 60px, 30px 30px;
  background-position: 0 0, 30px 30px;
  background-attachment: fixed;
  background-repeat: repeat;
  pointer-events: none;
  z-index: 0;
  /* 优化渲染性能 */
  will-change: auto;
  transform: translate3d(0, 0, 0);
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: calc(100vh - 140px);
  background: rgba(30, 41, 59, 0.4);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 16px;
  padding: 2rem;
}

.loading-container h3 {
  margin: 1rem 0 0.5rem 0;
  color: #e2e8f0;
  font-size: 1.5rem;
  font-weight: 600;
}

.loading-container p {
  margin: 0;
  color: #94a3b8;
  font-size: 1rem;
}

.loading-icon {
  color: var(--color-industrial-400);
  animation: pulse 2s infinite;
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: calc(100vh - 140px);
  background: rgba(30, 41, 59, 0.4);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: 16px;
  padding: 2rem;
}

.error-container h3 {
  margin: 1rem 0 0.5rem 0;
  color: #ef4444;
  font-size: 1.5rem;
  font-weight: 600;
}

.error-container p {
  margin: 0 0 1.5rem 0;
  color: #94a3b8;
  font-size: 1rem;
  text-align: center;
  max-width: 400px;
}

.error-icon {
  color: #ef4444;
}

/* 过渡动画 */
.fade-in-enter-active, .fade-in-leave-active {
  transition: opacity 0.5s ease, transform 0.5s ease;
}

.fade-in-enter-from, .fade-in-leave-to {
  opacity: 0;
  transform: translateY(20px);
}

.fade-in-enter-to, .fade-in-leave-from {
  opacity: 1;
  transform: translateY(0);
}

/* Header Styles */
.dashboard-header {
  padding: 1.25rem 1.5rem;
  background: rgba(18, 39, 56, 0.95);
  backdrop-filter: blur(16px);
  border-bottom: 1px solid rgba(59, 130, 246, 0.3);
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.header-container {
  max-width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 2rem;
}

.brand-section {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.brand-icon {
  position: relative;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--color-industrial-500), var(--color-industrial-400));
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 8px 25px rgba(45, 122, 164, 0.3);
}

.icon-glow {
  position: absolute;
  inset: -2px;
  background: linear-gradient(135deg, var(--color-industrial-400), var(--color-industrial-300));
  border-radius: 18px;
  z-index: -1;
  opacity: 0.5;
  filter: blur(4px);
}

.brand-content {
  flex: 1;
}

.brand-title {
  font-size: 1.75rem;
  font-weight: 700;
  margin: 0;
  background: linear-gradient(135deg, #f1f5f9, #66b0ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: -0.025em;
}

.brand-subtitle {
  font-size: 0.875rem;
  color: #94a3b8;
  margin: 0.25rem 0 0 0;
  font-weight: 400;
}

.status-badge {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  border-radius: 12px;
  padding: 0.75rem 1rem;
  border: 1px solid;
  background: rgba(16, 185, 129, 0.1);
  border-color: rgba(16, 185, 129, 0.3);
}

.status-badge.warning {
  background: rgba(245, 158, 11, 0.1);
  border-color: rgba(245, 158, 11, 0.3);
}

.status-badge.offline {
  background: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.3);
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--color-status-operational);
  animation: pulse 2s infinite;
}

.status-badge.warning .status-indicator {
  background: var(--color-status-warning);
}

.status-badge.offline .status-indicator {
  background: var(--color-status-error);
}

/* FPS计数器样式已移除 */

/* 调试面板样式已移除 */

/* Header Actions */
.header-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.search-container {
  position: relative;
}

.smart-search {
  width: 280px;
  transition: all 0.3s ease;
}

.smart-search:focus-within {
  width: 320px;
}

.search-icon {
  color: var(--color-industrial-400);
}

.search-results {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: rgba(18, 39, 56, 0.95);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 12px;
  margin-top: 0.5rem;
  z-index: 50;
  max-height: 200px;
  overflow-y: auto;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.search-result-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 1px solid rgba(59, 130, 246, 0.1);
}

.search-result-item:hover {
  background: rgba(59, 130, 246, 0.1);
}

.result-status {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.result-status.operational { background: var(--color-status-operational); }
.result-status.warning { background: var(--color-status-warning); }
.result-status.error { background: var(--color-status-error); }
.result-status.idle { background: var(--color-status-idle); }

.result-content {
  flex: 1;
}

.result-name {
  font-weight: 500;
  color: #e2e8f0;
  font-size: 0.875rem;
  display: block;
}

.result-info {
  font-size: 0.75rem;
  color: #94a3b8;
  margin-top: 0.125rem;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.action-btn {
  min-width: 44px;
  height: 44px;
  border-radius: 12px;
  border: 1px solid rgba(59, 130, 246, 0.3);
  background: rgba(18, 39, 56, 0.6);
  color: #94a3b8;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0 1rem;
  font-weight: 500;
}

.action-btn:hover {
  background: rgba(59, 130, 246, 0.1);
  border-color: rgba(59, 130, 246, 0.5);
  color: #e2e8f0;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

.action-btn.active {
  background: var(--color-industrial-500);
  border-color: var(--color-industrial-400);
  color: white;
  box-shadow: 0 4px 15px rgba(45, 122, 164, 0.3);
}

.filter-btn.active {
  background: var(--color-status-warning);
  border-color: rgba(245, 158, 11, 0.5);
  color: white;
}

.view-toggle {
  display: flex;
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid rgba(59, 130, 246, 0.3);
}

.view-btn {
  border: none;
  border-radius: 0;
  background: rgba(18, 39, 56, 0.6);
  color: #94a3b8;
  min-width: 44px;
  height: 44px;
  transition: all 0.3s ease;
}

.view-btn:hover {
  background: rgba(59, 130, 246, 0.1);
  color: #e2e8f0;
}

.view-btn.active {
  background: var(--color-industrial-500);
  color: white;
}

/* Main Content */
.dashboard-main {
  flex: 1;
  padding: 1rem; /* 减少主内容区域的内边距 */
  max-width: 100%;
}

.dashboard-container {
  display: grid;
  grid-template-columns: 240px 1fr; /* 进一步减少左侧面板宽度到240px */
  gap: 0.75rem; /* 进一步减少间距 */
  height: calc(100vh - 180px);
  max-width: 100%;
}

.stats-panel {
  display: flex;
  flex-direction: column;
  gap: 0.75rem; /* 减少面板间距 */
}

.filter-panel {
  display: flex;
  flex-direction: column;
}

.stats-card, .filter-card {
  background: rgba(30, 41, 59, 0.6);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 12px; /* 减少圆角 */
  padding: 1rem; /* 减少内边距 */
}

.filter-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filter-label {
  font-size: 0.875rem;
  font-weight: 600;
  color: #f1f5f9;
  margin-bottom: 0.5rem;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
}

.update-time {
  font-size: 0.75rem;
  color: #64748b;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.75rem; /* 减少间距 */
  margin-bottom: 1rem; /* 减少底部边距 */
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.5rem; /* 减少间距 */
  padding: 0.75rem; /* 减少内边距 */
  background: rgba(51, 65, 85, 0.3);
  border-radius: 8px; /* 减少圆角 */
  border-left: 3px solid; /* 减少边框宽度 */
  transition: all 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px -8px rgba(0, 0, 0, 0.3);
}

.stat-item.operational { border-left-color: #22c55e; }
.stat-item.warning { border-left-color: #eab308; }
.stat-item.error { border-left-color: #ef4444; }
.stat-item.idle { border-left-color: #475569; }

.stat-icon {
  width: 32px; /* 减少图标容器大小 */
  height: 32px;
  border-radius: 8px; /* 减少圆角 */
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem; /* 减少图标大小 */
}

.operational .stat-icon { background: rgba(34, 197, 94, 0.2); color: #22c55e; }
.warning .stat-icon { background: rgba(234, 179, 8, 0.2); color: #eab308; }
.error .stat-icon { background: rgba(239, 68, 68, 0.2); color: #ef4444; }
.idle .stat-icon { background: rgba(71, 85, 105, 0.3); color: #94a3b8; }

.stat-value {
  font-size: 1.25rem; /* 减少字体大小 */
  font-weight: 700;
  line-height: 1;
}

.stat-label {
  font-size: 0.75rem;
  color: #94a3b8;
  margin-top: 0.25rem;
}

.stat-percent {
  font-size: 0.875rem;
  font-weight: 600;
  color: #22c55e;
}

.factory-layout {
  background: rgba(30, 41, 59, 0.6);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 16px;
  padding: 1rem;
  overflow: hidden;
  /* 优化背景渲染 */
  background-attachment: local;
  background-repeat: no-repeat;
  background-size: cover;
  /* 性能优化 */
  will-change: auto;
  contain: layout style paint;
}

/* 移除了layout-header相关样式 */

.factory-floor {
  position: relative;
  height: 100%; /* 移除header后，占用全部高度 */
  background: #2d3748;
  border-radius: 12px;
  overflow: auto;
  border: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center; /* 垂直居中 */
  justify-content: center; /* 水平居中 */
  /* 添加立体感边框 */
  box-shadow:
    inset 0 1px 0 rgba(255, 255, 255, 0.1),
    inset 0 -1px 0 rgba(0, 0, 0, 0.2),
    0 2px 8px rgba(0, 0, 0, 0.3);
  /* 性能优化 */
  will-change: transform;
  contain: strict;
}

.factory-grid {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.zone-containers {
  position: relative;
  z-index: 10;
  transform-origin: center center; /* 改为中心对齐，配合居中布局 */
  /* 尺寸和缩放通过 containerStyle 计算属性动态设置 */
  /* 布局稳定性增强 */
  contain: layout style;
  /* 移除最小尺寸限制，让内容自然居中 */
}

.zone-container {
  z-index: 20;
  position: relative;
}

.zone-workstations {
  width: 100%;
  height: 100%;
}

.zone-label {
  position: absolute;
  top: -25px;
  left: 8px;
  font-size: 0.8rem;
  font-weight: 700;
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.95), rgba(30, 41, 59, 0.95));
  backdrop-filter: blur(10px);
  color: #e2e8f0;
  padding: 4px 12px;
  border-radius: 6px;
  border: 1px solid rgba(59, 130, 246, 0.3);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
  z-index: 50;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.workstation-cell {
  /* 基础样式 - 有生命力的色块设计 */
  border-radius: 6px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  min-height: 36px;
  min-width: 40px;
  position: relative;
  font-size: 10px;
  font-weight: 600;
  overflow: visible;
  color: #ffffff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  margin: 2px;
  /* 添加呼吸动画 */
  animation: workstation-breathe 4s ease-in-out infinite;
}

/* 正常运行状态 - 简洁绿色 */
.workstation-cell.status-operational {
  background: rgba(34, 197, 94, 0.8);
  border: 2px solid #22c55e;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.workstation-cell.status-operational:hover {
  background: rgba(34, 197, 94, 0.9);
  border-color: #16a34a;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  transform: translateY(-1px) scale(1.02);
}

/* 警告状态 - 简洁橙色 */
.workstation-cell.status-warning {
  background: rgba(245, 158, 11, 0.8);
  border: 2px solid #f59e0b;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.workstation-cell.status-warning:hover {
  background: rgba(245, 158, 11, 0.9);
  border-color: #d97706;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  transform: translateY(-1px) scale(1.02);
}

/* 故障状态 - 简洁红色 */
.workstation-cell.status-error {
  background: rgba(239, 68, 68, 0.8);
  border: 2px solid #ef4444;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.workstation-cell.status-error:hover {
  background: rgba(239, 68, 68, 0.9);
  border-color: #dc2626;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  transform: translateY(-1px) scale(1.02);
}

@keyframes pulse-error {
  0%, 100% {
    box-shadow:
      inset 0 1px 0 rgba(255, 255, 255, 0.2),
      inset 0 -1px 0 rgba(0, 0, 0, 0.2),
      0 1px 3px rgba(0, 0, 0, 0.3);
  }
  50% {
    box-shadow:
      inset 0 1px 0 rgba(255, 255, 255, 0.3),
      inset 0 -1px 0 rgba(0, 0, 0, 0.3),
      0 0 15px rgba(239, 68, 68, 0.6);
  }
}

/* 空闲状态 - 简洁蓝灰色 */
.workstation-cell.status-idle {
  background: rgba(100, 116, 139, 0.7);
  border: 2px solid #64748b;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  color: #cbd5e1;
}

.workstation-cell.status-idle:hover {
  background: rgba(100, 116, 139, 0.8);
  border-color: #475569;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  transform: translateY(-1px) scale(1.02);
}

.efficiency-bar {
  position: absolute;
  bottom: 2px;
  left: 2px;
  right: 2px;
  height: 4px;
  background: rgba(0, 0, 0, 0.4);
  border-radius: 2px;
  overflow: hidden;
}

.efficiency-fill {
  height: 100%;
  transition: width 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 2px;
  background: linear-gradient(90deg,
    rgba(255, 255, 255, 0.8) 0%,
    rgba(255, 255, 255, 0.9) 50%,
    rgba(255, 255, 255, 0.8) 100%);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
  box-shadow: 0 0 8px rgba(255, 255, 255, 0.4);
}

.selection-border {
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border: 2px solid #fbbf24;
  border-radius: 8px;
  pointer-events: none;
  animation: selection-glow 1.5s infinite;
}

.cell-content {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 2;
  box-sizing: border-box;
}

/* 光泽效果已移除，保持简洁的色块设计 */

/* 区域信息标签 */
.zone-info-label {
  position: absolute;
  top: 5px;
  left: 5px;
  background: rgba(0, 0, 0, 0.85);
  backdrop-filter: blur(8px);
  border-radius: 6px;
  padding: 6px 12px;
  border: 1px solid rgba(59, 130, 246, 0.6);
  z-index: 30;
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.4);
  pointer-events: none; /* 避免阻挡工位点击 */
}

.zone-title {
  font-size: 13px;
  font-weight: 700;
  color: #ffffff;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.9);
  white-space: nowrap;
  letter-spacing: 0.5px;
}

/* 区域容器样式增强 */
.zone-container {
  position: relative;
  transition: all 0.3s ease;
}

.zone-container:hover {
  transform: scale(1.02);
  z-index: 5;
}

.zone-container:hover .zone-info-label {
  background: rgba(0, 0, 0, 0.95);
  border-color: rgba(59, 130, 246, 0.8);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
}

/* 状态指示器圆点 */
.status-indicator-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  box-shadow: 0 0 4px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
}

/* 状态颜色 */
.status-indicator-dot.operational {
  background: linear-gradient(135deg, #22c55e, #16a34a);
  box-shadow: 0 0 8px rgba(34, 197, 94, 0.4);
}

.status-indicator-dot.warning {
  background: linear-gradient(135deg, #eab308, #ca8a04);
  box-shadow: 0 0 8px rgba(234, 179, 8, 0.4);
}

.status-indicator-dot.error {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  box-shadow: 0 0 8px rgba(239, 68, 68, 0.4);
}

.status-indicator-dot.idle {
  background: linear-gradient(135deg, #64748b, #475569);
  box-shadow: 0 0 8px rgba(100, 116, 139, 0.4);
}

/* 移除状态点，使用边框颜色区分状态 */



.hover-info {
  position: absolute;
  bottom: 2px;
  left: 2px;
  font-size: 8px;
  color: #ef4444;
  background: rgba(239, 68, 68, 0.1);
  padding: 1px 3px;
  border-radius: 2px;
}

.selected-border {
  position: absolute;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  border: 2px solid #fbbf24;
  border-radius: 8px;
  pointer-events: none;
}

.status-display {
  margin-bottom: 4px;
}

.status-icon {
  font-size: 14px;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.3));
}

.efficiency-indicator {
  position: relative;
  width: 24px;
  height: 24px;
}

.efficiency-circle {
  position: relative;
  width: 100%;
  height: 100%;
}

.circular-chart {
  width: 100%;
  height: 100%;
  transform: rotate(-90deg);
}

.circle-bg {
  fill: none;
  stroke: rgba(255, 255, 255, 0.2);
  stroke-width: 2;
}

.circle {
  fill: none;
  stroke-width: 2;
  stroke-linecap: round;
  transition: stroke-dasharray 0.3s ease;
}

.circle.high {
  stroke: rgba(255, 255, 255, 0.9);
}

.circle.medium {
  stroke: rgba(255, 255, 255, 0.7);
}

.circle.low {
  stroke: rgba(255, 255, 255, 0.5);
}

.efficiency-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 7px;
  font-weight: 600;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* 图例样式已移除 */

/* 提示框样式 */
.location-tooltip {
  position: fixed;
  background: rgba(0, 0, 0, 0.95);
  color: #fff;
  border-radius: 8px;
  padding: 12px;
  font-size: 12px;
  max-width: 280px;
  z-index: 10000;
  pointer-events: none;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.tooltip-header {
  margin-bottom: 8px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding-bottom: 6px;
}

.tooltip-title {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 2px;
}

.tooltip-code {
  font-size: 11px;
  color: #94a3b8;
}

.tooltip-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.tooltip-section {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.section-title {
  font-size: 11px;
  color: #94a3b8;
  font-weight: 500;
}

.status-info, .asset-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.status-badge {
  display: inline-block;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 600;
}

.status-badge.operational {
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
}

.status-badge.warning {
  background: rgba(234, 179, 8, 0.2);
  color: #eab308;
}

.status-badge.error {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

.status-badge.idle {
  background: rgba(71, 85, 105, 0.3);
  color: #94a3b8;
}

.efficiency-text {
  font-size: 11px;
  color: #60a5fa;
}

.department-name {
  font-size: 12px;
  color: #34d399;
}

.asset-info span {
  font-size: 11px;
  color: #e2e8f0;
}

/* 全屏样式 */
.factory-floor.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  background: #0f172a;
}

/* ===== 简化的动画系统 ===== */
/* 移除复杂的发光动画，保持性能 */

@keyframes selection-glow {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(251, 191, 36, 0.7);
  }
  50% {
    box-shadow: 0 0 0 4px rgba(251, 191, 36, 0);
  }
}

@keyframes subtle-pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.03); }
}

@keyframes efficiency-glow {
  0% { box-shadow: 0 0 0 0 rgba(96, 165, 250, 0.4); }
  70% { box-shadow: 0 0 0 6px rgba(96, 165, 250, 0); }
  100% { box-shadow: 0 0 0 0 rgba(96, 165, 250, 0); }
}

/* 2. 优化动画应用 */
.pulse-ring {
  animation: pulse-ring 1.5s cubic-bezier(0.4, 0, 0.6, 1) 3; /* 有限循环3次 */
}

.status-indicator.operational {
  animation: status-breathe 6s ease-in-out infinite; /* 延长动画周期 */
}

.status-indicator.warning {
  animation: pulse-warning 4s ease-in-out infinite; /* 降低频率 */
}

.error-pulse {
  display: none; /* 移除故障脉冲动画 */
}

/* ===== 性能降级样式 ===== */
.low-fps-mode {
  --animation-quality: low;
  --shadow-quality: low;
  --blur-quality: none;
  --transition-quality: none;
  --gpu-acceleration: false;
}

.low-fps-mode .modern-workstation {
  transition: none !important;
  animation: none !important;
  will-change: auto !important;
  transform: none !important;
}

.low-fps-mode .workstation-cell:hover {
  transform: none !important;
  animation: none !important;
}

.low-fps-mode .status-indicator {
  animation: none !important;
}

.low-fps-mode .pulse-ring,
.low-fps-mode .error-pulse {
  display: none !important;
}

/* 低性能模式背景优化 */
.low-fps-mode .modern-factory-dashboard::before {
  opacity: 0.3 !important;
  background-size: 120px 120px, 60px 60px !important;
}

.low-fps-mode .factory-layout {
  backdrop-filter: none !important;
  background: rgba(30, 41, 59, 0.8) !important;
}

.low-fps-mode .factory-floor {
  background: #0f172a !important;
  box-shadow: none !important;
}

.low-fps-mode .cell-background {
  background: rgba(255, 255, 255, 0.02) !important;
}

/* ===== 渐进增强与优雅降级 ===== */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* ===== 滚动条优化 ===== */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(59, 130, 246, 0.3);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(59, 130, 246, 0.5);
}

/* ===== 响应式调整 ===== */
@media (max-width: 768px) {
  .dashboard-container {
    display: flex;
    flex-direction: column;
  }

  .stats-panel {
    order: 2;
    max-height: 40vh;
    overflow-y: auto;
  }

  .factory-layout {
    order: 1;
    height: 50vh;
  }

  /* 移动端性能优化 */
  .workstation-cell {
    transition: none;
  }

  .workstation-cell:hover {
    transform: none;
  }
}

/* ===== 表格渲染优化 ===== */
:deep(.el-table__body-wrapper) {
  will-change: transform;
}

/* ===== 过渡效果优化 ===== */
.fade-in-enter-active, .fade-in-leave-active {
  transition: opacity 0.5s ease;
}

.fade-in-enter-from, .fade-in-leave-to {
  opacity: 0;
}

.hover-overlay {
  transition: opacity 0.3s ease;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes glow-operational {
  0%, 100% {
    box-shadow: 0 4px 20px rgba(34, 197, 94, 0.4), 0 0 30px rgba(34, 197, 94, 0.2);
  }
  50% {
    box-shadow: 0 4px 25px rgba(34, 197, 94, 0.5), 0 0 40px rgba(34, 197, 94, 0.3);
  }
}

@keyframes glow-warning {
  0%, 100% {
    box-shadow: 0 4px 20px rgba(245, 158, 11, 0.4), 0 0 30px rgba(245, 158, 11, 0.2);
  }
  50% {
    box-shadow: 0 4px 25px rgba(245, 158, 11, 0.5), 0 0 40px rgba(245, 158, 11, 0.3);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-2px);
  }
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .dashboard-main {
    grid-template-columns: 1fr;
    grid-template-rows: auto auto 1fr;
  }

  .stats-panel, .filter-panel {
    grid-column: 1;
  }

  .factory-layout {
    grid-column: 1;
  }
}

/* Table Styles */
.factory-list {
  height: calc(100% - 80px);
}

.efficiency-high {
  color: var(--color-status-operational);
  font-weight: 600;
}

.efficiency-medium {
  color: var(--color-status-warning);
  font-weight: 600;
}

.efficiency-low {
  color: var(--color-status-error);
  font-weight: 600;
}

.fault-count-text {
  color: var(--color-status-error);
  font-weight: 600;
}

.no-fault {
  color: #94a3b8;
}

/* Filter Popover */
:deep(.filter-popover) {
  background: rgba(18, 39, 56, 0.95);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 16px;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
}

.filter-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.filter-section {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filter-section label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #e2e8f0;
}

.range-display {
  font-size: 0.75rem;
  color: #94a3b8;
  text-align: center;
  margin-top: 0.5rem;
}

.filter-actions {
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
  padding-top: 1rem;
  border-top: 1px solid rgba(59, 130, 246, 0.2);
}

/* Element Plus Customization */
:deep(.el-input__wrapper) {
  background: rgba(18, 39, 56, 0.8);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 12px;
  transition: all 0.3s ease;
}

:deep(.el-input__wrapper:hover) {
  border-color: rgba(59, 130, 246, 0.5);
}

:deep(.el-input__wrapper.is-focus) {
  border-color: var(--color-industrial-400);
  box-shadow: 0 0 15px rgba(61, 143, 209, 0.3);
}

:deep(.el-input__inner) {
  color: #e2e8f0;
  background: transparent;
}

:deep(.el-input__inner::placeholder) {
  color: #94a3b8;
}

:deep(.el-button) {
  border-radius: 12px;
  font-weight: 500;
  transition: all 0.3s ease;
}

:deep(.el-button--primary) {
  background: var(--color-industrial-500);
  border-color: var(--color-industrial-400);
}

:deep(.el-button--primary:hover) {
  background: var(--color-industrial-400);
  border-color: var(--color-industrial-300);
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(45, 122, 164, 0.3);
}

:deep(.el-checkbox) {
  color: #e2e8f0;
}

:deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
  background: var(--color-industrial-500);
  border-color: var(--color-industrial-400);
}

:deep(.el-table) {
  background: transparent;
}

:deep(.el-table th) {
  background: rgba(26, 54, 80, 0.8);
  color: #e2e8f0;
  border-bottom: 1px solid rgba(59, 130, 246, 0.3);
}

:deep(.el-table td) {
  background: rgba(18, 39, 56, 0.6);
  color: #e2e8f0;
  border-bottom: 1px solid rgba(59, 130, 246, 0.2);
}

:deep(.el-table__row:hover) {
  background: rgba(59, 130, 246, 0.1);
}

:deep(.el-drawer) {
  background: rgba(18, 39, 56, 0.95);
  backdrop-filter: blur(16px);
}

:deep(.el-drawer__header) {
  background: rgba(26, 54, 80, 0.8);
  border-bottom: 1px solid rgba(59, 130, 246, 0.3);
  color: #e2e8f0;
}

/* Scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(18, 39, 56, 0.6);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--color-industrial-500);
  border-radius: 4px;
  transition: background 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-industrial-400);
}

@media (max-width: 768px) {
  .header-container {
    flex-direction: column;
    gap: 1rem;
  }

  .header-actions {
    width: 100%;
    justify-content: space-between;
  }

  .smart-search {
    width: 100%;
    max-width: 280px;
  }

  .dashboard-container {
    grid-template-columns: 1fr;
    grid-template-rows: auto 1fr;
    gap: 1rem;
  }

  .stats-panel {
    order: 2;
  }

  .factory-layout {
    order: 1;
  }
}

/* Enhanced Stats Panel Styles */
.stats-panel {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  height: 100%;
  overflow-y: auto;
}

.stats-card {
  background: rgba(18, 39, 56, 0.8);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 16px;
  backdrop-filter: blur(16px);
  padding: 1.5rem;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
}

.stats-card:hover {
  border-color: rgba(59, 130, 246, 0.5);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.4);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.card-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #e2e8f0;
  margin: 0;
}

.update-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.pulse-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--color-status-operational);
  animation: pulse 2s infinite;
}

.update-time {
  font-size: 0.75rem;
  color: #94a3b8;
  font-weight: 500;
}

/* Modern Stat Cards */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.modern-stat-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border-radius: 12px;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.modern-stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, transparent, rgba(255, 255, 255, 0.05));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.modern-stat-card:hover::before {
  opacity: 1;
}

.modern-stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.modern-stat-card.operational {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.15), rgba(16, 185, 129, 0.05));
  border: 1px solid rgba(16, 185, 129, 0.3);
}

.modern-stat-card.warning {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.15), rgba(245, 158, 11, 0.05));
  border: 1px solid rgba(245, 158, 11, 0.3);
}

.modern-stat-card.error {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.15), rgba(239, 68, 68, 0.05));
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.modern-stat-card.idle {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.15), rgba(59, 130, 246, 0.05));
  border: 1px solid rgba(59, 130, 246, 0.3);
}

.stat-visual {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.stat-icon-container {
  position: relative;
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.stat-icon-container.operational {
  background: linear-gradient(135deg, var(--color-status-operational), #059669);
}

.stat-icon-container.warning {
  background: linear-gradient(135deg, var(--color-status-warning), #d97706);
}

.stat-icon-container.error {
  background: linear-gradient(135deg, var(--color-status-error), #dc2626);
}

.stat-icon-container.idle {
  background: linear-gradient(135deg, var(--color-status-idle), #2563eb);
}

.stat-icon {
  font-size: 20px;
  z-index: 2;
}

.icon-glow {
  position: absolute;
  inset: -2px;
  border-radius: 14px;
  z-index: 1;
  opacity: 0.6;
  filter: blur(4px);
}

.icon-glow.operational {
  background: linear-gradient(135deg, var(--color-status-operational), #059669);
}

.icon-glow.warning {
  background: linear-gradient(135deg, var(--color-status-warning), #d97706);
}

.icon-glow.error {
  background: linear-gradient(135deg, var(--color-status-error), #dc2626);
}

.icon-glow.idle {
  background: linear-gradient(135deg, var(--color-status-idle), #2563eb);
}

.stat-progress {
  width: 48px;
  height: 4px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  border-radius: 2px;
  transition: width 0.8s ease;
}

.progress-bar.operational {
  background: var(--color-status-operational);
}

.progress-bar.warning {
  background: var(--color-status-warning);
}

.progress-bar.error {
  background: var(--color-status-error);
}

.progress-bar.idle {
  background: var(--color-status-idle);
}

.stat-data {
  flex: 1;
}

.stat-value {
  font-size: 1.75rem;
  font-weight: 700;
  color: #e2e8f0;
  line-height: 1;
}

.stat-label {
  font-size: 0.875rem;
  color: #94a3b8;
  margin-top: 0.25rem;
  font-weight: 500;
}

.stat-percent {
  font-size: 0.75rem;
  color: var(--color-status-operational);
  font-weight: 600;
  margin-top: 0.25rem;
}

/* System Metrics */
.system-metrics {
  display: flex;
  gap: 1rem;
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid rgba(59, 130, 246, 0.2);
}

.metric-item {
  flex: 1;
  text-align: center;
}

.metric-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--color-industrial-400);
  line-height: 1;
}

.metric-label {
  font-size: 0.75rem;
  color: #94a3b8;
  margin-top: 0.25rem;
  font-weight: 500;
}

/* Priority Workstations */
.priority-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.priority-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(59, 130, 246, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
}

.priority-item:hover {
  background: rgba(59, 130, 246, 0.1);
  border-color: rgba(59, 130, 246, 0.3);
  transform: translateX(4px);
}

.priority-status {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.priority-status.operational { background: var(--color-status-operational); }
.priority-status.warning { background: var(--color-status-warning); }
.priority-status.error { background: var(--color-status-error); }
.priority-status.idle { background: var(--color-status-idle); }

.priority-content {
  flex: 1;
}

.priority-name {
  font-weight: 500;
  color: #e2e8f0;
  font-size: 0.875rem;
}

.priority-info {
  font-size: 0.75rem;
  color: #94a3b8;
  margin-top: 0.125rem;
}

.fault-count {
  color: var(--color-status-error);
  font-weight: 600;
}

.priority-arrow {
  color: #94a3b8;
  font-size: 14px;
  transition: transform 0.3s ease;
}

.priority-item:hover .priority-arrow {
  transform: translateX(2px);
}

/* Zone Statistics */
.zone-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.zone-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(59, 130, 246, 0.1);
}

.zone-info {
  flex: 1;
}

.zone-name {
  font-weight: 500;
  color: #e2e8f0;
  font-size: 0.875rem;
}

.zone-metrics {
  display: flex;
  gap: 0.75rem;
  margin-top: 0.25rem;
}

.zone-total, .zone-efficiency {
  font-size: 0.75rem;
  color: #94a3b8;
}

.zone-efficiency {
  color: var(--color-industrial-400);
  font-weight: 600;
}

.status-indicators {
  display: flex;
  gap: 0.25rem;
}

.status-dot {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 20px;
  height: 20px;
  border-radius: 10px;
  font-size: 0.75rem;
  font-weight: 600;
  color: white;
}

.status-dot.operational {
  background: var(--color-status-operational);
}

.status-dot.warning {
  background: var(--color-status-warning);
}

.status-dot.error {
  background: var(--color-status-error);
}

/* 现代化工位样式增强 */
.modern-zone {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.modern-zone:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.3);
}

.modern-zone-label {
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.6));
  backdrop-filter: blur(8px);
  border-radius: 12px;
  padding: 12px 16px;
  margin-bottom: 16px;
  border: 1px solid rgba(59, 130, 246, 0.3);
}

.zone-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.zone-name {
  font-size: 1rem;
  font-weight: 700;
  color: #e2e8f0;
}

.zone-stats {
  font-size: 0.75rem;
  color: #94a3b8;
  background: rgba(59, 130, 246, 0.1);
  padding: 2px 8px;
  border-radius: 12px;
  border: 1px solid rgba(59, 130, 246, 0.3);
}

.zone-efficiency {
  font-size: 0.8rem;
  color: #66b0ff;
  font-weight: 600;
}

.modern-grid {
  gap: 4px !important;
  /* 确保网格项目正确对齐 */
  align-items: start;
  justify-items: start;
  align-content: start;
  justify-content: start;
  /* 防止溢出 */
  overflow: hidden;
}

.modern-workstation {
  position: relative;
  border-radius: 6px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.2s ease;
  /* 网格布局中的尺寸由父容器控制 */
  width: 100%;
  height: 100%;
  min-width: 32px;
  min-height: 32px;
  max-width: 60px;
  max-height: 60px;
  /* 移除margin，由grid gap控制间距 */
  margin: 0;
  /* 默认背景将被状态样式覆盖 */
  background: rgba(74, 85, 104, 0.6);
  border: 2px solid #64748b;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  /* 确保内容居中 */
  display: flex;
  align-items: center;
  justify-content: center;
  /* 保持正方形 */
  aspect-ratio: 1;
}

.modern-workstation::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.05),
    rgba(255, 255, 255, 0.1),
    rgba(255, 255, 255, 0.05),
    transparent
  );
  background-size: 200% 100%;
  opacity: 0.6;
  z-index: 1;
  transition: opacity 0.3s ease;
  animation: shimmer 4s infinite;
}

.modern-workstation:hover {
  transform: translateY(-8px) scale(1.05) rotateX(2deg);
  z-index: 10;
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.4), 0 0 30px rgba(59, 130, 246, 0.3);
  border-color: rgba(59, 130, 246, 0.8);
}

.modern-workstation:hover::before {
  opacity: 1;
}

/* modern-workstation 状态样式 - 简洁透明设计 */
.modern-workstation.status-operational {
  background: rgba(34, 197, 94, 0.8) !important;
  border: 2px solid #22c55e !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
}

.modern-workstation.status-warning {
  background: rgba(245, 158, 11, 0.8) !important;
  border: 2px solid #f59e0b !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
}

.modern-workstation.status-error {
  background: rgba(239, 68, 68, 0.8) !important;
  border: 2px solid #ef4444 !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
}

.modern-workstation.status-idle {
  background: rgba(100, 116, 139, 0.7) !important;
  border: 2px solid #64748b !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
}

/* 移除复杂的cell-background，简化工位显示 */

.status-indicator {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  z-index: 5;
  transition: all 0.3s ease;
}

.status-indicator.operational {
  background: var(--gradient-operational);
  box-shadow: 0 0 12px var(--glow-operational), 0 0 6px var(--color-status-operational);
  animation: status-breathe 4s infinite;
}

.status-indicator.warning {
  background: var(--gradient-warning);
  box-shadow: 0 0 12px var(--glow-warning), 0 0 6px var(--color-status-warning);
  animation: pulse-warning 2.5s infinite;
}

.status-indicator.error {
  background: var(--gradient-error);
  box-shadow: 0 0 15px var(--glow-error), 0 0 8px var(--color-status-error);
  animation: pulse-error 1.5s infinite;
}

.status-indicator.idle {
  background: var(--gradient-idle);
  box-shadow: 0 0 10px var(--glow-idle), 0 0 5px var(--color-status-idle);
}

.pulse-ring {
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  border: 2px solid #ef4444;
  border-radius: 50%;
  animation: pulse-ring 1.5s infinite;
}

.modern-content {
  position: relative;
  z-index: 2;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 12px;
}

.workstation-header {
  text-align: center;
  margin-bottom: 8px;
}

.workstation-id {
  font-size: 0.9rem;
  font-weight: 700;
  color: #e2e8f0;
  line-height: 1;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.workstation-name {
  font-size: 0.7rem;
  color: #94a3b8;
  margin-top: 2px;
  line-height: 1;
}

.status-display {
  display: flex;
  justify-content: center;
  margin: 8px 0;
}

.status-icon {
  font-size: 20px;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
  transition: all 0.3s ease;
}

.status-icon.operational {
  color: #10b981;
}

.status-icon.warning {
  color: #f59e0b;
  animation: bounce 2s infinite;
}

.status-icon.error {
  color: #ef4444;
  animation: shake 0.5s infinite;
}

.status-icon.idle {
  color: #94a3b8;
  filter: drop-shadow(0 0 4px rgba(148, 163, 184, 0.3));
}

.efficiency-indicator {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 8px;
}

.efficiency-circle {
  position: relative;
  width: 40px;
  height: 40px;
}

.circular-chart {
  width: 100%;
  height: 100%;
  transform: rotate(-90deg);
}

.circle-bg {
  fill: none;
  stroke: rgba(255, 255, 255, 0.1);
  stroke-width: 2;
}

.circle {
  fill: none;
  stroke-width: 3;
  stroke-linecap: round;
  transition: stroke-dasharray 0.8s ease;
}

.circle.excellent {
  stroke: var(--color-efficiency-excellent);
  filter: drop-shadow(0 0 4px var(--glow-operational));
  animation: efficiency-glow 3s infinite;
}

.circle.good {
  stroke: var(--color-efficiency-good);
  filter: drop-shadow(0 0 4px var(--glow-idle));
  animation: efficiency-glow 3.5s infinite;
}

.circle.average {
  stroke: var(--color-efficiency-average);
  filter: drop-shadow(0 0 4px var(--glow-warning));
  animation: efficiency-glow 3s infinite;
}

.circle.poor {
  stroke: var(--color-efficiency-poor);
  filter: drop-shadow(0 0 4px var(--glow-error));
  animation: efficiency-glow 2s infinite;
}

.efficiency-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 0.65rem;
  font-weight: 700;
  color: #e2e8f0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.hover-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.85), rgba(0, 0, 0, 0.7));
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 10;
  border-radius: 12px;
}

.modern-workstation:hover .hover-overlay {
  opacity: 1;
}

.quick-stats {
  display: flex;
  flex-direction: column;
  gap: 6px;
  text-align: center;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.75rem;
}

.stat-label {
  color: #94a3b8;
  font-weight: 500;
}

.stat-value {
  color: #e2e8f0;
  font-weight: 700;
  margin-left: 8px;
}

.stat-value.error {
  color: #ef4444;
}

.modern-selection {
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  border: 3px solid #fbbf24;
  border-radius: 15px;
  pointer-events: none;
  animation: selection-glow 1.5s infinite;
  z-index: 15;
}

.error-pulse {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: 2px solid transparent;
  border-radius: 12px;
  animation: error-pulse-border 2s infinite;
  z-index: 3;
  pointer-events: none;
}

/* 动画定义 */
@keyframes pulse-error {
  0%, 100% { 
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2), 0 0 0 0 rgba(239, 68, 68, 0.7); 
  }
  50% { 
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3), 0 0 0 8px rgba(239, 68, 68, 0); 
  }
}

@keyframes pulse-ring {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(1.4);
    opacity: 0;
  }
}

@keyframes selection-glow {
  0%, 100% { 
    box-shadow: 0 0 0 0 rgba(251, 191, 36, 0.7); 
  }
  50% { 
    box-shadow: 0 0 0 6px rgba(251, 191, 36, 0); 
  }
}

@keyframes error-pulse-border {
  0%, 100% {
    border-color: transparent;
  }
  50% {
    border-color: rgba(239, 68, 68, 0.6);
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-4px);
  }
  60% {
    transform: translateY(-2px);
  }
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }
  20%, 40%, 60%, 80% { transform: translateX(2px); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* Enhanced animation effects from reference file */
@keyframes pulse-warning {
  0%, 100% { 
    transform: scale(1);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2), 0 0 20px var(--glow-warning);
  }
  50% { 
    transform: scale(1.02);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3), 0 0 30px var(--glow-warning);
  }
}

@keyframes glow-pulse {
  0%, 100% { 
    box-shadow: 0 0 5px currentColor;
  }
  50% { 
    box-shadow: 0 0 20px currentColor, 0 0 30px currentColor;
  }
}

@keyframes efficiency-glow {
  0%, 100% {
    filter: drop-shadow(0 0 2px currentColor);
  }
  50% {
    filter: drop-shadow(0 0 8px currentColor) drop-shadow(0 0 12px currentColor);
  }
}

@keyframes status-breathe {
  0%, 100% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.1);
    opacity: 1;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* ===== 背景加载优化 ===== */
.modern-factory-dashboard,
.factory-layout,
.factory-floor,
.cell-background {
  /* 确保背景正确渲染 */
  background-origin: padding-box;
  background-clip: padding-box;
  /* 优化重绘性能 */
  will-change: auto;
  /* 防止背景闪烁 */
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
}

/* 背景加载状态指示 */
.background-loading {
  position: relative;
}

.background-loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(59, 130, 246, 0.1) 50%,
    transparent 100%);
  animation: background-shimmer 2s infinite;
  pointer-events: none;
  z-index: 1;
}

@keyframes background-shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* 背景渲染优化 */
.modern-factory-dashboard::before {
  /* 强制GPU加速 */
  transform: translate3d(0, 0, 0);
  /* 优化背景渲染 */
  image-rendering: optimizeSpeed;
  image-rendering: -webkit-optimize-contrast;
  image-rendering: optimize-contrast;
}

.factory-floor {
  /* 优化渐变渲染 - 更明亮的背景 */
  background-image: linear-gradient(135deg, #334155 0%, #475569 50%, #334155 100%);
  /* 强制GPU加速 */
  transform: translate3d(0, 0, 0);
}

.cell-background {
  /* 优化径向渐变 */
  background-image: radial-gradient(circle at center, rgba(255, 255, 255, 0.04), rgba(255, 255, 255, 0.01));
  /* 强制GPU加速 */
  transform: translate3d(0, 0, 0);
}
</style>
