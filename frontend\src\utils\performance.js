/**
 * 性能优化工具函数
 */

/**
 * 防抖函数
 * @param {Function} func 要防抖的函数
 * @param {number} wait 等待时间（毫秒）
 * @param {boolean} immediate 是否立即执行
 * @returns {Function} 防抖后的函数
 */
export function debounce(func, wait, immediate = false) {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      timeout = null
      if (!immediate) func.apply(this, args)
    }
    const callNow = immediate && !timeout
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
    if (callNow) func.apply(this, args)
  }
}

/**
 * 节流函数
 * @param {Function} func 要节流的函数
 * @param {number} limit 时间间隔（毫秒）
 * @returns {Function} 节流后的函数
 */
export function throttle(func, limit) {
  let inThrottle
  return function executedFunction(...args) {
    if (!inThrottle) {
      func.apply(this, args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

/**
 * 延迟执行函数
 * @param {number} ms 延迟时间（毫秒）
 * @returns {Promise} Promise对象
 */
export function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms))
}

/**
 * 批量更新函数
 * @param {Function} updateFn 更新函数
 * @returns {Function} 批量更新函数
 */
export function batchUpdate(updateFn) {
  let pending = false
  let updates = []
  
  return function(update) {
    updates.push(update)
    
    if (!pending) {
      pending = true
      Promise.resolve().then(() => {
        const allUpdates = updates.splice(0)
        pending = false
        updateFn(allUpdates)
      })
    }
  }
}

/**
 * 优化输入框性能的配置
 */
export const inputOptimization = {
  // 基础输入框配置
  basic: {
    clearable: true,
    maxlength: 100,
  },
  
  // 文本域配置
  textarea: {
    clearable: true,
    maxlength: 1000,
    showWordLimit: true,
    resize: 'none'
  },
  
  // 搜索框配置
  search: {
    clearable: true,
    maxlength: 50,
  },
  
  // 名称输入框配置
  name: {
    clearable: true,
    maxlength: 50,
    showWordLimit: true
  }
}

/**
 * 优化选择器性能的配置
 */
export const selectOptimization = {
  // 基础选择器配置
  basic: {
    clearable: true,
    filterable: false,
  },
  
  // 可搜索选择器配置
  searchable: {
    clearable: true,
    filterable: true,
    remote: false
  },
  
  // 远程搜索选择器配置
  remote: {
    clearable: true,
    filterable: true,
    remote: true,
    reserveKeyword: false,
    defaultFirstOption: false
  }
}

/**
 * 表单验证优化配置
 */
export const validationOptimization = {
  // 只在失焦时验证
  onBlur: {
    trigger: 'blur'
  },
  
  // 只在提交时验证
  onSubmit: {
    trigger: 'submit'
  },
  
  // 实时验证（谨慎使用）
  realTime: {
    trigger: ['blur', 'change']
  }
}

/**
 * 创建优化的响应式对象
 * @param {Object} data 初始数据
 * @param {boolean} shallow 是否使用浅层响应式
 * @returns {Object} 响应式对象
 */
export function createOptimizedReactive(data, shallow = false) {
  if (shallow) {
    // 对于大型对象或频繁更新的对象使用浅层响应式
    return shallowReactive(data)
  }
  return reactive(data)
}

/**
 * 优化列表渲染的key生成器
 * @param {string} prefix 前缀
 * @returns {Function} key生成函数
 */
export function createKeyGenerator(prefix = 'item') {
  let counter = 0
  return (item, index) => {
    if (item.id) return `${prefix}-${item.id}`
    if (item.key) return `${prefix}-${item.key}`
    return `${prefix}-${index}-${counter++}`
  }
}

/**
 * 内存优化：清理未使用的引用
 * @param {Object} refs 引用对象
 */
export function cleanupRefs(refs) {
  Object.keys(refs).forEach(key => {
    if (refs[key] && typeof refs[key].value !== 'undefined') {
      refs[key].value = null
    }
  })
}

/**
 * 检查是否为移动设备
 * @returns {boolean} 是否为移动设备
 */
export function isMobile() {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
}

/**
 * 获取设备性能等级
 * @returns {string} 性能等级：'high', 'medium', 'low'
 */
export function getDevicePerformance() {
  const memory = navigator.deviceMemory || 4 // 默认4GB
  const cores = navigator.hardwareConcurrency || 4 // 默认4核
  
  if (memory >= 8 && cores >= 8) return 'high'
  if (memory >= 4 && cores >= 4) return 'medium'
  return 'low'
}

/**
 * 根据设备性能调整配置
 * @param {Object} config 配置对象
 * @returns {Object} 调整后的配置
 */
export function adaptConfigForDevice(config) {
  const performance = getDevicePerformance()
  const mobile = isMobile()
  
  const adaptedConfig = { ...config }
  
  if (performance === 'low' || mobile) {
    // 低性能设备优化
    adaptedConfig.debounceTime = Math.max(config.debounceTime || 300, 500)
    adaptedConfig.pageSize = Math.min(config.pageSize || 20, 10)
    adaptedConfig.enableAnimation = false
  } else if (performance === 'high') {
    // 高性能设备优化
    adaptedConfig.debounceTime = Math.min(config.debounceTime || 300, 150)
    adaptedConfig.pageSize = Math.max(config.pageSize || 20, 50)
    adaptedConfig.enableAnimation = true
  }
  
  return adaptedConfig
}
