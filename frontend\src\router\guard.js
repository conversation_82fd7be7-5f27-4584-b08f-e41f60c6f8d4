/**
 * 路由守卫配置
 * 文件路径: src/router/guard.js
 * 功能描述: 处理路由权限、导航进度条
 */

import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import { getToken } from '@/utils/auth'

// 配置NProgress
NProgress.configure({
  easing: 'ease',
  speed: 500,
  showSpinner: false,
  trickleSpeed: 200,
  minimum: 0.3
})

// 白名单路由 - 不需要登录也可以访问
const whiteList = ['/login', '/auth-redirect', '/register', '/reset-password']

/**
 * 设置路由守卫
 * @param {import('vue-router').Router} router 路由实例
 */
export default function setupGuard(router) {
  // 路由前置守卫
  router.beforeEach(async (to, from, next) => {
    // 开始进度条
    NProgress.start()

    // 设置页面标题
    document.title = to.meta.title ? `${to.meta.title} - IT资产管理系统` : 'IT资产管理系统'

    // 获取用户Token
    const hasToken = getToken()

    if (hasToken) {
      if (to.path === '/login') {
        // 已登录，重定向到首页
        next({ path: '/' })
        NProgress.done()
      } else {
        try {
          // 检查用户是否已获取权限信息
          // 这里可以根据实际情况添加权限检查
          next()
        } catch (error) {
          console.error('路由守卫错误:', error)
          next(`/login?redirect=${to.path}`)
          NProgress.done()
        }
      }
    } else {
      // 未登录
      if (whiteList.indexOf(to.path) !== -1) {
        // 白名单路由，直接访问
        next()
      } else {
        // 没有权限，重定向到登录页
        next(`/login?redirect=${to.path}`)
        NProgress.done()
      }
    }
  })

  // 路由后置守卫
  router.afterEach(() => {
    // 结束进度条
    NProgress.done()
  })
} 