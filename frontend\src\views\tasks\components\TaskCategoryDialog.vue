<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑分类' : '新建分类'"
    width="600px"
    @close="handleClose"
    :close-on-click-modal="false"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="100px"
      @submit.prevent="handleSubmit"
    >
      <el-form-item label="分类名称" prop="name">
        <el-input
          v-model="formData.name"
          placeholder="请输入分类名称"
          maxlength="100"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="分类描述" prop="description">
        <el-input
          type="textarea"
          v-model="formData.description"
          placeholder="请输入分类描述（可选）"
          :rows="3"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="分类颜色" prop="color">
        <div class="color-picker-section">
          <el-color-picker
            v-model="formData.color"
            :predefine="predefinedColors"
            show-alpha
          />
          <el-input
            v-model="formData.color"
            placeholder="#409EFF"
            style="width: 120px; margin-left: 12px;"
          />
          <div class="color-preview" :style="{ backgroundColor: formData.color }">
            预览
          </div>
        </div>
      </el-form-item>

      <el-form-item label="分类图标" prop="icon">
        <div class="icon-picker-section">
          <el-select
            v-model="formData.icon"
            placeholder="选择图标"
            filterable
            style="width: 200px;"
          >
            <el-option
              v-for="icon in availableIcons"
              :key="icon.name"
              :label="icon.label"
              :value="icon.name"
            >
              <div class="icon-option">
                <el-icon :style="{ color: formData.color || '#409EFF' }">
                  <component :is="icon.name" />
                </el-icon>
                <span>{{ icon.label }}</span>
              </div>
            </el-option>
          </el-select>
          <div class="icon-preview" v-if="formData.icon">
            <el-icon :style="{ color: formData.color || '#409EFF', fontSize: '24px' }">
              <component :is="formData.icon" />
            </el-icon>
            <span>{{ formData.icon }}</span>
          </div>
        </div>
      </el-form-item>

      <el-form-item label="排序顺序" prop="sortOrder">
        <el-input-number
          v-model="formData.sortOrder"
          :min="0"
          :max="9999"
          controls-position="right"
          placeholder="排序顺序"
        />
        <small class="form-hint">数字越小排序越靠前</small>
      </el-form-item>

      <el-form-item label="状态" prop="isActive">
        <el-switch
          v-model="formData.isActive"
          active-text="启用"
          inactive-text="禁用"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  category: {
    type: Object,
    default: null
  },
  isEdit: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'submit', 'close'])

// 响应式数据
const formRef = ref()
const submitting = ref(false)

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 表单数据
const formData = reactive({
  name: '',
  description: '',
  color: '#409EFF',
  icon: 'Folder',
  sortOrder: 0,
  isActive: true
})

// 预定义颜色
const predefinedColors = [
  '#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399',
  '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
  '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
]

// 可用图标
const availableIcons = [
  { name: 'Folder', label: '文件夹' },
  { name: 'Document', label: '文档' },
  { name: 'Setting', label: '设置' },
  { name: 'Tools', label: '工具' },
  { name: 'Monitor', label: '监控' },
  { name: 'DataAnalysis', label: '分析' },
  { name: 'Warning', label: '警告' },
  { name: 'Success', label: '成功' },
  { name: 'Info', label: '信息' },
  { name: 'Question', label: '问题' },
  { name: 'Star', label: '星标' },
  { name: 'Flag', label: '标记' },
  { name: 'Bell', label: '通知' },
  { name: 'Clock', label: '时间' },
  { name: 'Calendar', label: '日历' },
  { name: 'User', label: '用户' },
  { name: 'UserFilled', label: '用户(填充)' },
  { name: 'House', label: '房屋' },
  { name: 'Office', label: '办公' },
  { name: 'Cpu', label: 'CPU' },
  { name: 'HardDisk', label: '硬盘' },
  { name: 'Printer', label: '打印机' },
  { name: 'Phone', label: '电话' },
  { name: 'Laptop', label: '笔记本' },
  { name: 'Mouse', label: '鼠标' },
  { name: 'Headset', label: '耳机' },
  { name: 'Camera', label: '摄像头' },
  { name: 'VideoCamera', label: '摄像机' },
  { name: 'Microphone', label: '麦克风' },
  { name: 'Connection', label: '连接' },
  { name: 'Link', label: '链接' },
  { name: 'Share', label: '分享' },
  { name: 'Download', label: '下载' },
  { name: 'Upload', label: '上传' },
  { name: 'Refresh', label: '刷新' },
  { name: 'Search', label: '搜索' },
  { name: 'Filter', label: '过滤' },
  { name: 'Sort', label: '排序' },
  { name: 'Grid', label: '网格' },
  { name: 'List', label: '列表' },
  { name: 'View', label: '查看' },
  { name: 'Edit', label: '编辑' },
  { name: 'Delete', label: '删除' },
  { name: 'Plus', label: '添加' },
  { name: 'Minus', label: '减少' },
  { name: 'Close', label: '关闭' },
  { name: 'Check', label: '检查' },
  { name: 'CircleCheck', label: '圆形检查' },
  { name: 'CircleClose', label: '圆形关闭' },
  { name: 'ArrowUp', label: '向上箭头' },
  { name: 'ArrowDown', label: '向下箭头' },
  { name: 'ArrowLeft', label: '向左箭头' },
  { name: 'ArrowRight', label: '向右箭头' },
  { name: 'Back', label: '返回' },
  { name: 'Right', label: '右' },
  { name: 'Top', label: '顶部' },
  { name: 'Bottom', label: '底部' }
]

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入分类名称', trigger: 'blur' },
    { min: 1, max: 100, message: '分类名称长度在 1 到 100 个字符', trigger: 'blur' }
  ],
  description: [
    { max: 500, message: '描述长度不能超过 500 个字符', trigger: 'blur' }
  ],
  color: [
    { required: true, message: '请选择分类颜色', trigger: 'change' }
  ],
  icon: [
    { required: true, message: '请选择分类图标', trigger: 'change' }
  ],
  sortOrder: [
    { required: true, message: '请输入排序顺序', trigger: 'blur' },
    { type: 'number', min: 0, max: 9999, message: '排序顺序必须在 0 到 9999 之间', trigger: 'blur' }
  ]
}

// 方法
const initForm = () => {
  if (props.isEdit && props.category) {
    Object.assign(formData, {
      name: props.category.name || '',
      description: props.category.description || '',
      color: props.category.color || '#409EFF',
      icon: props.category.icon || 'Folder',
      sortOrder: props.category.sortOrder || 0,
      isActive: props.category.isActive !== false
    })
  } else {
    Object.assign(formData, {
      name: '',
      description: '',
      color: '#409EFF',
      icon: 'Folder',
      sortOrder: 0,
      isActive: true
    })
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    submitting.value = true
    
    const submitData = {
      name: formData.name.trim(),
      description: formData.description?.trim() || '',
      color: formData.color,
      icon: formData.icon,
      sortOrder: formData.sortOrder,
      isActive: formData.isActive
    }
    
    emit('submit', submitData)
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    submitting.value = false
  }
}

const handleClose = () => {
  emit('close')
  emit('update:modelValue', false)
}

// 监听器
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    nextTick(() => {
      initForm()
    })
  }
})

watch(() => props.category, () => {
  if (props.modelValue) {
    initForm()
  }
})
</script>

<style scoped>
.color-picker-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.color-preview {
  width: 60px;
  height: 32px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
  font-weight: 500;
  border: 1px solid #dcdfe6;
}

.icon-picker-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.icon-option {
  display: flex;
  align-items: center;
  gap: 8px;
}

.icon-preview {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #f5f7fa;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.icon-preview span {
  font-size: 12px;
  color: #606266;
}

.form-hint {
  color: #909399;
  font-size: 12px;
  margin-left: 8px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-color-picker__trigger) {
  width: 40px;
  height: 32px;
}

:deep(.el-input-number) {
  width: 150px;
}

:deep(.el-form-item__content) {
  align-items: flex-start;
}
</style>
