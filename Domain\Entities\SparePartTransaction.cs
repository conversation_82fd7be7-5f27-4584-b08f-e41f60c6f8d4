// File: Domain/Entities/SparePartTransaction.cs
// Description: 备品备件出入库记录实体类

using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ItAssetsSystem.Domain.Entities
{
    /// <summary>
    /// 备品备件出入库记录实体
    /// </summary>
    [Table("spare_part_transactions")]
    public class SparePartTransaction
    {
        /// <summary>
        /// 记录ID
        /// </summary>
        [Key]
        [Column("id")]
        public long Id { get; set; }
        
        /// <summary>
        /// 备件ID
        /// </summary>
        [Column("part_id")]
        public long PartId { get; set; }
        
        /// <summary>
        /// 备件导航属性
        /// </summary>
        [ForeignKey("PartId")]
        public virtual SparePart Part { get; set; }
        
        /// <summary>
        /// 库位ID
        /// </summary>
        [Column("location_id")]
        public long LocationId { get; set; }
        
        /// <summary>
        /// 库位导航属性
        /// </summary>
        [ForeignKey("LocationId")]
        public virtual SparePartLocation Location { get; set; }
        
        /// <summary>
        /// 操作类型: 1=入库, 2=出库
        /// </summary>
        [Column("type")]
        public byte Type { get; set; }
        
        /// <summary>
        /// 操作数量
        /// </summary>
        [Column("quantity")]
        public int Quantity { get; set; }
        
        /// <summary>
        /// 操作后库存
        /// </summary>
        [Column("stock_after")]
        public int StockAfter { get; set; }
        
        /// <summary>
        /// 原因类型: 1=采购入库, 2=退回入库, 3=领用出库, 4=报废出库, 5=盘点调整
        /// </summary>
        [Column("reason_type")]
        public byte ReasonType { get; set; }
        
        /// <summary>
        /// 关联单号
        /// </summary>
        [Column("reference")]
        [StringLength(100)]
        public string ReferenceNumber { get; set; }
        
        /// <summary>
        /// 操作人ID
        /// </summary>
        [Column("user_id")]
        public int OperatorUserId { get; set; }
        
        /// <summary>
        /// 操作时间
        /// </summary>
        [Column("transaction_time")]
        public DateTime OperationTime { get; set; }
        
        /// <summary>
        /// 原因/用途详细描述 (此字段用于存储备注信息)
        /// </summary>
        [Column("reason")]
        [StringLength(500)]
        public string Reason { get; set; }
        
        /// <summary>
        /// 关联资产ID (若出库用于特定资产)
        /// </summary>
        [Column("related_asset_id")]
        public int? RelatedAssetId { get; set; }
        
        /// <summary>
        /// 关联故障ID (若出库用于特定维修)
        /// </summary>
        [Column("related_fault_id")]
        public int? RelatedFaultId { get; set; }
        
        /// <summary>
        /// 记录批次号，用于将相同批次的多个记录关联
        /// </summary>
        [Column("batch_number")]
        [StringLength(50)]
        public string BatchNumber { get; set; }
        
        /// <summary>
        /// 是否由系统自动生成
        /// </summary>
        [Column("is_system_generated")]
        public bool IsSystemGenerated { get; set; } = false;

        /// <summary>
        /// 变更前状态ID
        /// </summary>
        [Column("from_status_id")]
        public int? FromStatusId { get; set; }

        /// <summary>
        /// 变更前状态导航属性
        /// </summary>
        [ForeignKey("FromStatusId")]
        public virtual SparePartStatusType FromStatus { get; set; }

        /// <summary>
        /// 变更后状态ID
        /// </summary>
        [Column("to_status_id")]
        public int? ToStatusId { get; set; }

        /// <summary>
        /// 变更后状态导航属性
        /// </summary>
        [ForeignKey("ToStatusId")]
        public virtual SparePartStatusType ToStatus { get; set; }

        /// <summary>
        /// 关联库存明细ID
        /// </summary>
        [Column("inventory_id")]
        public long? InventoryId { get; set; }

        /// <summary>
        /// 关联库存明细导航属性
        /// </summary>
        [ForeignKey("InventoryId")]
        public virtual SparePartInventory Inventory { get; set; }

        /// <summary>
        /// 关联返厂单ID
        /// </summary>
        [Column("repair_order_id")]
        public int? RepairOrderId { get; set; }

        // 注释掉导航属性，避免Entity Framework错误
        // /// <summary>
        // /// 关联返厂单导航属性
        // /// </summary>
        // [ForeignKey("RepairOrderId")]
        // public virtual RepairOrder RepairOrder { get; set; }
    }
}