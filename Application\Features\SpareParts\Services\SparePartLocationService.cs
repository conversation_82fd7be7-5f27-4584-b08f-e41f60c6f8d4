// File: Application/Features/SpareParts/Services/SparePartLocationService.cs
// Description: 备品备件库位服务实现类

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ItAssetsSystem.Application.Features.SpareParts.Dtos;
using ItAssetsSystem.Core.Abstractions;
using ItAssetsSystem.Domain.Entities;

namespace ItAssetsSystem.Application.Features.SpareParts.Services
{
    /// <summary>
    /// 备品备件库位服务实现类
    /// </summary>
    public class SparePartLocationService : ISparePartLocationService
    {
        private readonly ISparePartLocationRepository _locationRepository;
        private readonly ICurrentUserService _currentUserService;
        
        /// <summary>
        /// 构造函数
        /// </summary>
        public SparePartLocationService(
            ISparePartLocationRepository locationRepository,
            ICurrentUserService currentUserService)
        {
            _locationRepository = locationRepository;
            _currentUserService = currentUserService;
        }
        
        /// <summary>
        /// 获取所有库位列表
        /// </summary>
        /// <returns>库位列表</returns>
        public async Task<List<SparePartLocationDto>> GetAllLocationsAsync()
        {
            var locations = await _locationRepository.GetAllAsync();
            return locations.Select(MapToDto).ToList();
        }
        
        /// <summary>
        /// 根据区域获取库位列表
        /// </summary>
        /// <param name="area">区域标识</param>
        /// <returns>库位列表</returns>
        public async Task<List<SparePartLocationDto>> GetLocationsByAreaAsync(string area)
        {
            var locations = await _locationRepository.GetByAreaAsync(area);
            return locations.Select(MapToDto).ToList();
        }
        
        /// <summary>
        /// 获取库位详情
        /// </summary>
        /// <param name="id">库位ID</param>
        /// <returns>库位DTO</returns>
        public async Task<SparePartLocationDto> GetLocationByIdAsync(long id)
        {
            var location = await _locationRepository.GetByIdAsync(id);
            if (location == null)
            {
                return null;
            }
            
            return MapToDto(location);
        }
        
        /// <summary>
        /// 创建库位
        /// </summary>
        /// <param name="request">创建请求</param>
        /// <returns>创建的库位DTO</returns>
        public async Task<SparePartLocationDto> CreateLocationAsync(CreateSparePartLocationRequest request)
        {
            // 验证编码是否已存在
            var codeExists = await _locationRepository.IsCodeExistsAsync(request.Code);
            if (codeExists)
            {
                throw new ArgumentException($"编码 {request.Code} 已存在");
            }
            
            // 创建库位实体
            var location = new SparePartLocation
            {
                Code = request.Code,
                Name = request.Name,
                Area = request.Area,
                Description = request.Description,
                CreatorUserId = _currentUserService.UserId,
                CreatedAt = DateTime.Now,
                UpdatedAt = DateTime.Now,
                IsActive = true
            };
            
            // 保存库位
            var createdLocation = await _locationRepository.CreateAsync(location);
            
            return MapToDto(createdLocation);
        }
        
        /// <summary>
        /// 更新库位
        /// </summary>
        /// <param name="id">库位ID</param>
        /// <param name="request">更新请求</param>
        /// <returns>更新后的库位DTO</returns>
        public async Task<SparePartLocationDto> UpdateLocationAsync(long id, CreateSparePartLocationRequest request)
        {
            // 验证库位是否存在
            var existingLocation = await _locationRepository.GetByIdAsync(id);
            if (existingLocation == null)
            {
                return null;
            }
            
            // 验证编码是否已存在
            var codeExists = await _locationRepository.IsCodeExistsAsync(request.Code, id);
            if (codeExists)
            {
                throw new ArgumentException($"编码 {request.Code} 已存在");
            }
            
            // 更新库位属性
            existingLocation.Code = request.Code;
            existingLocation.Name = request.Name;
            existingLocation.Area = request.Area;
            existingLocation.Description = request.Description;
            existingLocation.UpdatedAt = DateTime.Now;
            
            // 保存库位
            var updatedLocation = await _locationRepository.UpdateAsync(existingLocation);
            
            return MapToDto(updatedLocation);
        }
        
        /// <summary>
        /// 删除库位
        /// </summary>
        /// <param name="id">库位ID</param>
        /// <returns>是否成功</returns>
        public async Task<bool> DeleteLocationAsync(long id)
        {
            return await _locationRepository.DeleteAsync(id);
        }
        
        /// <summary>
        /// 获取所有区域列表
        /// </summary>
        /// <returns>区域列表</returns>
        public async Task<List<string>> GetAllAreasAsync()
        {
            return await _locationRepository.GetAllAreasAsync();
        }
        
        /// <summary>
        /// 将实体映射为DTO
        /// </summary>
        /// <param name="entity">库位实体</param>
        /// <returns>库位DTO</returns>
        private SparePartLocationDto MapToDto(SparePartLocation entity)
        {
            if (entity == null)
            {
                return null;
            }
            
            return new SparePartLocationDto
            {
                Id = entity.Id,
                Code = entity.Code,
                Name = entity.Name,
                Area = entity.Area,
                Description = entity.Description,
                CreatedAt = entity.CreatedAt,
                UpdatedAt = entity.UpdatedAt,
                IsActive = entity.IsActive,
                PartsCount = entity.SpareParts?.Count ?? 0
            };
        }
    }
} 