// IT资产管理系统 - 工位实体
// 文件路径: /Models/Entities/Workstation.cs
// 功能: 定义工位实体，对应workstations表

using System;
using System.Collections.Generic;

namespace ItAssetsSystem.Models.Entities
{
    /// <summary>
    /// 工位实体
    /// </summary>
    public class Workstation : IAuditableEntity
    {
        /// <summary>
        /// 工位ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 工位编码
        /// </summary>
        public string Code { get; set; }

        /// <summary>
        /// 工位名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 工序ID
        /// </summary>
        public int ProcessId { get; set; }

        /// <summary>
        /// 序号（在工序中的顺序）
        /// </summary>
        public int OrderNumber { get; set; }

        /// <summary>
        /// 状态（0:停用, 1:启用）
        /// </summary>
        public int Status { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; }

        /// <summary>
        /// 所属工序
        /// </summary>
        public virtual Process Process { get; set; }

        /// <summary>
        /// 关联的资产
        /// </summary>
        public virtual ICollection<Asset> Assets { get; set; }
    }
} 