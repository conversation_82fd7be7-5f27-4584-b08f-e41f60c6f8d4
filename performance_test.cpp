// 性能测试工具 - 测试WebSocket vs UDP延迟
#include <windows.h>
#include <chrono>
#include <iostream>
#include <vector>
#include <thread>
#include "websocket_client.h"

class PerformanceTest {
private:
    std::vector<double> websocket_latencies;
    std::vector<double> udp_latencies;
    
public:
    // 测试WebSocket延迟
    void testWebSocketLatency() {
        WebSocketClient client;
        client.connect("ws://localhost:8080/hubs/notification");
        
        for (int i = 0; i < 1000; i++) {
            auto start = std::chrono::high_resolution_clock::now();
            
            // 发送ping消息
            client.send("{\"type\":\"ping\",\"timestamp\":" + 
                       std::to_string(std::chrono::duration_cast<std::chrono::microseconds>(start.time_since_epoch()).count()) + "}");
            
            // 等待pong响应
            std::string response = client.waitForMessage(1000); // 1秒超时
            
            auto end = std::chrono::high_resolution_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
            
            websocket_latencies.push_back(duration.count() / 1000.0); // 转换为毫秒
            
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
        }
    }
    
    // 测试UDP延迟
    void testUDPLatency() {
        SOCKET udp_socket = socket(AF_INET, SOCK_DGRAM, IPPROTO_UDP);
        
        sockaddr_in server_addr = {};
        server_addr.sin_family = AF_INET;
        server_addr.sin_port = htons(8081);
        inet_pton(AF_INET, "127.0.0.1", &server_addr.sin_addr);
        
        for (int i = 0; i < 1000; i++) {
            auto start = std::chrono::high_resolution_clock::now();
            
            // 发送UDP ping
            char ping_data[64];
            auto timestamp = std::chrono::duration_cast<std::chrono::microseconds>(start.time_since_epoch()).count();
            sprintf_s(ping_data, "ping:%lld", timestamp);
            
            sendto(udp_socket, ping_data, strlen(ping_data), 0, 
                   (sockaddr*)&server_addr, sizeof(server_addr));
            
            // 接收pong响应
            char response[64];
            int addr_len = sizeof(server_addr);
            recvfrom(udp_socket, response, sizeof(response), 0, 
                     (sockaddr*)&server_addr, &addr_len);
            
            auto end = std::chrono::high_resolution_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
            
            udp_latencies.push_back(duration.count() / 1000.0);
            
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
        }
        
        closesocket(udp_socket);
    }
    
    // 输出统计结果
    void printResults() {
        auto calcStats = [](const std::vector<double>& data) {
            double sum = 0, min_val = data[0], max_val = data[0];
            for (double val : data) {
                sum += val;
                min_val = std::min(min_val, val);
                max_val = std::max(max_val, val);
            }
            double avg = sum / data.size();
            
            std::cout << "  平均: " << avg << "ms" << std::endl;
            std::cout << "  最小: " << min_val << "ms" << std::endl;
            std::cout << "  最大: " << max_val << "ms" << std::endl;
        };
        
        std::cout << "\n=== 性能测试结果 ===" << std::endl;
        std::cout << "WebSocket延迟统计:" << std::endl;
        calcStats(websocket_latencies);
        
        std::cout << "\nUDP延迟统计:" << std::endl;
        calcStats(udp_latencies);
        
        // 计算性能提升
        double ws_avg = 0, udp_avg = 0;
        for (double val : websocket_latencies) ws_avg += val;
        for (double val : udp_latencies) udp_avg += val;
        ws_avg /= websocket_latencies.size();
        udp_avg /= udp_latencies.size();
        
        std::cout << "\n性能对比:" << std::endl;
        std::cout << "UDP比WebSocket快 " << ((ws_avg - udp_avg) / ws_avg * 100) << "%" << std::endl;
    }
};

int main() {
    std::cout << "开始性能测试..." << std::endl;
    
    PerformanceTest test;
    
    std::cout << "测试WebSocket延迟..." << std::endl;
    test.testWebSocketLatency();
    
    std::cout << "测试UDP延迟..." << std::endl;
    test.testUDPLatency();
    
    test.printResults();
    
    return 0;
}