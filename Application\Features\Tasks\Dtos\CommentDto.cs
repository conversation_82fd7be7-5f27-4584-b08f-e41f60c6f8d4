// File: Application/Features/Tasks/Dtos/CommentDto.cs
// Description: 评论数据传输对象

using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text.Json.Serialization;

namespace ItAssetsSystem.Application.Features.Tasks.Dtos
{
    /// <summary>
    /// 评论数据传输对象
    /// </summary>
    public class CommentDto
    {
        /// <summary>
        /// 评论ID
        /// </summary>
        [JsonPropertyName("commentId")]
        public long CommentId { get; set; }

        /// <summary>
        /// 任务ID
        /// </summary>
        [JsonPropertyName("taskId")]
        public long TaskId { get; set; }

        /// <summary>
        /// 评论内容
        /// </summary>
        [JsonPropertyName("content")]
        public string Content { get; set; } = string.Empty;

        /// <summary>
        /// 创建者用户ID
        /// </summary>
        [JsonPropertyName("userId")]
        public int UserId { get; set; }

        /// <summary>
        /// 创建者用户名称
        /// </summary>
        [JsonPropertyName("userName")]
        public string UserName { get; set; } = string.Empty;

        /// <summary>
        /// 创建者头像URL
        /// </summary>
        [JsonPropertyName("userAvatarUrl")]
        public string UserAvatarUrl { get; set; } = string.Empty;

        /// <summary>
        /// 创建时间
        /// </summary>
        [JsonPropertyName("creationTimestamp")]
        public DateTime CreationTimestamp { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        [JsonPropertyName("lastUpdatedTimestamp")]
        public DateTime? LastUpdatedTimestamp { get; set; }

        /// <summary>
        /// 父评论ID
        /// </summary>
        [JsonPropertyName("parentCommentId")]
        public long? ParentCommentId { get; set; }

        /// <summary>
        /// 是否置顶
        /// </summary>
        [JsonPropertyName("isPinned")]
        public bool IsPinned { get; set; }

        /// <summary>
        /// 是否已编辑
        /// </summary>
        [JsonPropertyName("isEdited")]
        public bool IsEdited { get; set; }

        /// <summary>
        /// 提及的用户ID（JSON字符串或直接列表）。
        /// V2 Comment entity stores this as a string. DTO could be string or List&lt;int&gt;.
        /// For simplicity with JSON, often a string is fine if the client handles parsing,
        /// or a List&lt;int&gt; if server-side deserialization from a JSON array is expected.
        /// </summary>
        [JsonPropertyName("mentionedUserIds")]
        public string MentionedUserIds { get; set; } = string.Empty;

        /// <summary>
        /// 附件列表
        /// </summary>
        [JsonPropertyName("attachments")]
        public List<AttachmentDto> Attachments { get; set; } = new();

        /// <summary>
        /// 回复列表（用于树形结构）
        /// </summary>
        [JsonPropertyName("replies")]
        public List<CommentDto> Replies { get; set; } = new();
    }

    /// <summary>
    /// 更新评论请求DTO
    /// </summary>
    public class UpdateCommentRequestDto
    {
        /// <summary>
        /// 评论内容
        /// </summary>
        [Required(ErrorMessage = "评论内容不能为空")]
        [StringLength(2000, ErrorMessage = "评论内容不能超过2000个字符")]
        public string Content { get; set; } = string.Empty;

        /// <summary>
        /// 提及的用户ID列表
        /// </summary>
        public List<int> MentionedUserIds { get; set; } = new();

        /// <summary>
        /// 是否置顶（仅管理员可设置）
        /// </summary>
        public bool? IsPinned { get; set; }
    }

    /// <summary>
    /// 置顶评论请求DTO
    /// </summary>
    public class PinCommentRequestDto
    {
        /// <summary>
        /// 是否置顶
        /// </summary>
        [Required(ErrorMessage = "置顶状态不能为空")]
        public bool IsPinned { get; set; }
    }

    /// <summary>
    /// 回复评论请求DTO
    /// </summary>
    public class ReplyCommentRequestDto
    {
        /// <summary>
        /// 评论内容
        /// </summary>
        [Required(ErrorMessage = "评论内容不能为空")]
        [MaxLength(2000, ErrorMessage = "评论内容不能超过2000个字符")]
        public string Content { get; set; }

        /// <summary>
        /// 提及的用户ID列表
        /// </summary>
        public List<int> MentionedUserIds { get; set; } = new();
    }

    /// <summary>
    /// 评论查询参数DTO
    /// </summary>
    public class CommentQueryParametersDto
    {
        /// <summary>
        /// 任务ID
        /// </summary>
        [Required(ErrorMessage = "任务ID不能为空")]
        public long TaskId { get; set; }

        /// <summary>
        /// 是否只返回顶层评论
        /// </summary>
        public bool TopLevelOnly { get; set; } = true;

        /// <summary>
        /// 页码（从1开始）
        /// </summary>
        public int PageNumber { get; set; } = 1;

        /// <summary>
        /// 每页大小
        /// </summary>
        public int PageSize { get; set; } = 20;

        /// <summary>
        /// 排序字段
        /// </summary>
        public string SortBy { get; set; } = "CreationTimestamp";

        /// <summary>
        /// 排序方向（asc或desc）
        /// </summary>
        public string SortDirection { get; set; } = "asc";
    }

    /// <summary>
    /// 搜索评论请求DTO
    /// </summary>
    public class SearchCommentsRequestDto
    {
        /// <summary>
        /// 任务ID（可选，如果指定则只搜索该任务的评论）
        /// </summary>
        public long? TaskId { get; set; }

        /// <summary>
        /// 搜索关键词
        /// </summary>
        public string SearchKeyword { get; set; } = string.Empty;

        /// <summary>
        /// 用户ID（可选，如果指定则只搜索该用户的评论）
        /// </summary>
        public int? UserId { get; set; }

        /// <summary>
        /// 开始日期（可选）
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// 结束日期（可选）
        /// </summary>
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// 是否只搜索置顶评论
        /// </summary>
        public bool? IsPinned { get; set; }

        /// <summary>
        /// 页码（从1开始）
        /// </summary>
        public int PageNumber { get; set; } = 1;

        /// <summary>
        /// 每页大小
        /// </summary>
        public int PageSize { get; set; } = 20;

        /// <summary>
        /// 排序字段（CreationTimestamp、LastUpdatedTimestamp、UserId、Content、ContentLength、IsPinned、IsEdited、ParentCommentId、MentionedUsers、Relevance等）
        /// </summary>
        public string SortBy { get; set; } = "CreationTimestamp";

        /// <summary>
        /// 排序方向（asc或desc）
        /// </summary>
        public string SortDirection { get; set; } = "desc";

        /// <summary>
        /// 是否只返回顶层评论（非回复）
        /// </summary>
        public bool? TopLevelOnly { get; set; }

        /// <summary>
        /// 是否只返回已编辑的评论
        /// </summary>
        public bool? IsEdited { get; set; }

        /// <summary>
        /// 是否包含已删除的评论
        /// </summary>
        public bool IncludeDeleted { get; set; } = false;

        /// <summary>
        /// 评论内容最小长度
        /// </summary>
        public int? MinContentLength { get; set; }

        /// <summary>
        /// 评论内容最大长度
        /// </summary>
        public int? MaxContentLength { get; set; }

        /// <summary>
        /// 是否有提及用户
        /// </summary>
        public bool? HasMentions { get; set; }
    }
} 