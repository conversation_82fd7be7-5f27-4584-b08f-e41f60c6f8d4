// File: Core/Configuration/FileStorageSettings.cs
// Description: Configuration settings for file storage.

namespace ItAssetsSystem.Core.Configuration
{
    public class FileStorageSettings
    {
        public const string SectionName = "FileStorageSettings";

        /// <summary>
        /// The base physical path on the server where the IIS virtual directory (e.g., "/files") is mapped.
        /// Example: "D:\\StaticContent\\SiteFiles"
        /// </summary>
        public string BasePhysicalPathForFilesVirtualDir { get; set; }
    }
} 