/**
 * 性能监控工具
 * 文件路径: src/utils/performanceMonitor.js
 * 功能描述: 监控API请求性能、连接数、错误率等
 */

class PerformanceMonitor {
  constructor() {
    this.metrics = {
      apiCalls: new Map(),
      errors: [],
      connectionCount: 0,
      startTime: Date.now()
    }
    this.thresholds = {
      maxApiCallsPerMinute: 60,
      maxErrorRate: 0.1, // 10%
      maxResponseTime: 5000, // 5秒
      maxConcurrentConnections: 10
    }
    this.alerts = []
    this.isMonitoring = false
  }

  // 开始监控
  startMonitoring() {
    if (this.isMonitoring) return
    
    this.isMonitoring = true
    console.log('🔍 性能监控已启动')
    
    // 每分钟检查一次性能指标
    this.monitorInterval = setInterval(() => {
      this.checkPerformanceMetrics()
    }, 60000)
    
    // 每10秒检查连接数
    this.connectionInterval = setInterval(() => {
      this.checkConnectionCount()
    }, 10000)
  }

  // 停止监控
  stopMonitoring() {
    if (!this.isMonitoring) return
    
    this.isMonitoring = false
    clearInterval(this.monitorInterval)
    clearInterval(this.connectionInterval)
    console.log('🔍 性能监控已停止')
  }

  // 记录API调用
  recordApiCall(url, method, startTime, endTime, success = true, error = null) {
    const duration = endTime - startTime
    const key = `${method}:${url}`
    
    if (!this.metrics.apiCalls.has(key)) {
      this.metrics.apiCalls.set(key, {
        count: 0,
        totalDuration: 0,
        errors: 0,
        lastCall: 0,
        callsPerMinute: []
      })
    }
    
    const metric = this.metrics.apiCalls.get(key)
    metric.count++
    metric.totalDuration += duration
    metric.lastCall = endTime
    
    if (!success) {
      metric.errors++
      this.metrics.errors.push({
        url,
        method,
        error: error?.message || 'Unknown error',
        timestamp: endTime
      })
    }
    
    // 记录每分钟调用次数
    const minute = Math.floor(endTime / 60000)
    metric.callsPerMinute[minute] = (metric.callsPerMinute[minute] || 0) + 1
    
    // 检查是否超过阈值
    this.checkApiCallThresholds(key, metric, duration)
  }

  // 检查API调用阈值
  checkApiCallThresholds(key, metric, duration) {
    const now = Date.now()
    const currentMinute = Math.floor(now / 60000)
    const callsThisMinute = metric.callsPerMinute[currentMinute] || 0
    
    // 检查调用频率
    if (callsThisMinute > this.thresholds.maxApiCallsPerMinute) {
      this.addAlert('high_frequency', `API调用频率过高: ${key} (${callsThisMinute}/分钟)`)
    }
    
    // 检查响应时间
    if (duration > this.thresholds.maxResponseTime) {
      this.addAlert('slow_response', `API响应时间过长: ${key} (${duration}ms)`)
    }
    
    // 检查错误率
    const errorRate = metric.errors / metric.count
    if (errorRate > this.thresholds.maxErrorRate && metric.count > 10) {
      this.addAlert('high_error_rate', `API错误率过高: ${key} (${(errorRate * 100).toFixed(1)}%)`)
    }
  }

  // 检查连接数
  checkConnectionCount() {
    // 这里可以通过浏览器API或其他方式检查连接数
    // 暂时使用估算方法
    const activeRequests = this.getActiveRequestsCount()
    
    if (activeRequests > this.thresholds.maxConcurrentConnections) {
      this.addAlert('high_connections', `并发连接数过高: ${activeRequests}`)
    }
  }

  // 获取活跃请求数量（估算）
  getActiveRequestsCount() {
    // 简单估算：基于最近1秒内的API调用
    const now = Date.now()
    let activeCount = 0
    
    for (const [key, metric] of this.metrics.apiCalls) {
      if (now - metric.lastCall < 1000) {
        activeCount++
      }
    }
    
    return activeCount
  }

  // 添加告警
  addAlert(type, message) {
    const alert = {
      type,
      message,
      timestamp: Date.now(),
      id: Date.now() + Math.random()
    }
    
    this.alerts.push(alert)
    console.warn(`⚠️ 性能告警: ${message}`)
    
    // 保持最近100个告警
    if (this.alerts.length > 100) {
      this.alerts = this.alerts.slice(-100)
    }
    
    // 触发告警事件
    this.triggerAlert(alert)
  }

  // 触发告警事件
  triggerAlert(alert) {
    // 可以在这里添加告警处理逻辑
    // 例如：发送通知、记录日志等
    
    if (alert.type === 'high_connections') {
      // 连接数过高时的处理
      console.error('🚨 连接数过高，可能导致后端压力过大')
    }
  }

  // 检查性能指标
  checkPerformanceMetrics() {
    const stats = this.getPerformanceStats()
    console.log('📊 性能监控报告:', stats)
    
    // 清理过期数据
    this.cleanupOldData()
  }

  // 清理过期数据
  cleanupOldData() {
    const now = Date.now()
    const oneHourAgo = now - 60 * 60 * 1000
    
    // 清理过期错误记录
    this.metrics.errors = this.metrics.errors.filter(
      error => error.timestamp > oneHourAgo
    )
    
    // 清理过期告警
    this.alerts = this.alerts.filter(
      alert => alert.timestamp > oneHourAgo
    )
  }

  // 获取性能统计
  getPerformanceStats() {
    const now = Date.now()
    const uptime = now - this.metrics.startTime
    
    let totalCalls = 0
    let totalErrors = 0
    let totalDuration = 0
    
    for (const [key, metric] of this.metrics.apiCalls) {
      totalCalls += metric.count
      totalErrors += metric.errors
      totalDuration += metric.totalDuration
    }
    
    return {
      uptime: Math.floor(uptime / 1000), // 秒
      totalApiCalls: totalCalls,
      totalErrors: totalErrors,
      errorRate: totalCalls > 0 ? (totalErrors / totalCalls * 100).toFixed(2) + '%' : '0%',
      averageResponseTime: totalCalls > 0 ? Math.round(totalDuration / totalCalls) + 'ms' : '0ms',
      activeAlerts: this.alerts.length,
      apiEndpoints: this.metrics.apiCalls.size
    }
  }

  // 获取详细报告
  getDetailedReport() {
    const stats = this.getPerformanceStats()
    const apiDetails = []
    
    for (const [key, metric] of this.metrics.apiCalls) {
      apiDetails.push({
        endpoint: key,
        calls: metric.count,
        errors: metric.errors,
        errorRate: metric.count > 0 ? (metric.errors / metric.count * 100).toFixed(2) + '%' : '0%',
        avgResponseTime: metric.count > 0 ? Math.round(metric.totalDuration / metric.count) + 'ms' : '0ms',
        lastCall: new Date(metric.lastCall).toLocaleTimeString()
      })
    }
    
    return {
      summary: stats,
      apiDetails: apiDetails.sort((a, b) => b.calls - a.calls),
      recentAlerts: this.alerts.slice(-10),
      recentErrors: this.metrics.errors.slice(-10)
    }
  }

  // 重置统计
  reset() {
    this.metrics = {
      apiCalls: new Map(),
      errors: [],
      connectionCount: 0,
      startTime: Date.now()
    }
    this.alerts = []
    console.log('🔄 性能监控数据已重置')
  }
}

// 创建全局实例
export const performanceMonitor = new PerformanceMonitor()

// 便捷方法
export const startPerformanceMonitoring = () => {
  performanceMonitor.startMonitoring()
}

export const stopPerformanceMonitoring = () => {
  performanceMonitor.stopMonitoring()
}

export const getPerformanceReport = () => {
  return performanceMonitor.getDetailedReport()
}

export const recordApiPerformance = (url, method, startTime, endTime, success, error) => {
  performanceMonitor.recordApiCall(url, method, startTime, endTime, success, error)
}

export default performanceMonitor
