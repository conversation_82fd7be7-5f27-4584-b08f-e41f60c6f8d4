# 后端功能实现指南：积分、钻石、金币、刀具

本文档旨在指导后端开发人员在实现与积分、钻石、金币和刀具相关的功能时需要考虑的数据类型、数据库设计和API接口。

## 1. 数据模型设计

在数据库中，我们需要为用户存储这些虚拟物品或货币的数量。可以考虑以下两种方式：

**方式一：统一资源表**

创建一个通用的用户资源表，用于存储所有类型的虚拟物品。

```sql
CREATE TABLE UserResources (
    UserId INT NOT NULL,
    ResourceType VARCHAR(50) NOT NULL, -- 'Points', 'Diamonds', 'GoldCoins', 'Knife_TypeA', 'Knife_TypeB' 等
    Quantity BIGINT NOT NULL DEFAULT 0,
    LastUpdated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (UserId, ResourceType),
    FOREIGN KEY (UserId) REFERENCES Users(Id) -- 假设存在一个 Users 表
);
```

*   **优点**: 扩展性好，未来增加新的虚拟物品类型时，只需增加新的 `ResourceType` 即可，无需修改表结构。
*   **缺点**: 查询特定用户的多种资源时可能需要多次查询或复杂查询。`Quantity` 需要使用足够大的整数类型（如 `BIGINT`）以适应不同物品的数量级。

**方式二：独立字段或表**

将常用的货币（积分、钻石、金币）作为用户表的独立字段，而将道具（如刀具）存储在单独的表中。

```sql
-- 在 Users 表中增加字段
ALTER TABLE Users
ADD COLUMN Points BIGINT NOT NULL DEFAULT 0,
ADD COLUMN Diamonds BIGINT NOT NULL DEFAULT 0,
ADD COLUMN GoldCoins BIGINT NOT NULL DEFAULT 0;

-- 创建用户道具表
CREATE TABLE UserItems (
    UserItemId INT PRIMARY KEY AUTO_INCREMENT,
    UserId INT NOT NULL,
    ItemType VARCHAR(50) NOT NULL, -- 'Knife_TypeA', 'Knife_TypeB' 等
    ItemId VARCHAR(100) NULL, -- 如果道具本身有唯一ID或特殊标识
    Quantity INT NOT NULL DEFAULT 1, -- 对于非堆叠物品通常是1，可堆叠则>1
    AcquiredDate TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ExpiryDate TIMESTAMP NULL, -- 如果道具有时效性
    Status VARCHAR(20) DEFAULT 'Active', -- 'Active', 'Used', 'Expired'
    FOREIGN KEY (UserId) REFERENCES Users(Id)
);
```

*   **优点**: 查询常用货币非常直接快速。道具表可以存储更丰富的道具信息（如有效期、状态等）。
*   **缺点**: 增加新的全局货币类型需要修改 `Users` 表结构。

**推荐**:

*   如果虚拟物品种类繁多且未来可能频繁增加，**方式一**更灵活。
*   如果主要关注积分、钻石、金币这几种核心货币，且道具种类相对固定或需要存储更多属性，**方式二**更直观高效。

**数据类型选择**:

*   **积分 (Points)**: 通常是整数，根据业务需求决定是否允许负数。使用 `BIGINT` 以支持大量积分。
*   **钻石 (Diamonds)**: 通常是充值货币，需要高精度和安全性。使用 `BIGINT`。涉及交易时需要考虑事务和日志记录。
*   **金币 (GoldCoins)**: 游戏内货币，使用 `BIGINT`。
*   **刀具 (Knives)**:
    *   如果是可堆叠的消耗品，可以用 `INT` 或 `BIGINT` 记录数量（类似方式一或方式二的 `Quantity`）。
    *   如果是独特的装备或具有不同属性的道具，每个刀具实例可能需要作为一条记录存储在 `UserItems` 表中（方式二），包含类型、属性、状态等信息。`ItemType` 可以是字符串枚举值（如 'BasicKnife', 'FireKnife'）。

## 2. 后端 API 设计

需要设计清晰的 API 来处理这些资源的增减和查询。

**核心 API 示例**:

*   **获取用户资源**:
    *   `GET /api/users/{userId}/resources`：获取指定用户的所有（或指定类型）资源信息。
    *   请求参数: `?types=Points,Diamonds` (可选，用于指定查询类型)
    *   响应: `{ "Points": 1000, "Diamonds": 50, "GoldCoins": 50000, "Items": [ { "ItemType": "Knife_TypeA", "Quantity": 1 }, ... ] }`
*   **增加资源**:
    *   `POST /api/users/{userId}/resources/add`
    *   请求体: `{ "ResourceType": "GoldCoins", "Amount": 100, "Source": "DailyLoginBonus" }` 或 `{ "ItemType": "Knife_TypeA", "Quantity": 1, "Source": "Purchase" }`
    *   需要进行严格的权限和逻辑校验（例如，钻石增加通常来自支付回调）。
    *   记录详细的交易日志。
*   **消耗/减少资源**:
    *   `POST /api/users/{userId}/resources/spend`
    *   请求体: `{ "ResourceType": "Diamonds", "Amount": 10, "Reason": "BuyItem_Knife_TypeB" }` 或 `{ "ItemType": "Knife_TypeA", "Quantity": 1, "Reason": "UsedInGame" }`
    *   需要检查用户余额是否足够。
    *   确保操作的原子性，特别是在涉及多种资源交换的场景下（例如，用钻石买金币）。
    *   记录详细的交易日志。

**事务管理**:

所有涉及资源变更的操作（特别是增加和减少）都应该在数据库事务中执行，以确保数据的一致性。例如，购买道具时，需要同时扣除货币并增加道具，这两个操作必须要么都成功，要么都失败。

**安全性**:

*   所有资源变更的 API 都需要严格的身份验证和授权。
*   防止客户端直接修改资源数量，所有变更必须通过后端 API 进行，并进行逻辑校验。
*   记录详细的操作日志（谁、什么时间、做了什么、原因、变更前后的数量），便于审计和问题排查。

## 3. 注意事项

*   **并发控制**: 当多个请求同时尝试修改同一用户的同一资源时（例如，快速点击购买），需要使用乐观锁或悲观锁来防止数据不一致。
*   **日志记录**: 详细记录每一次资源的变动，包括来源、原因、数量变化等，对于追踪问题和反作弊至关重要。
*   **精度问题**: 虽然这里都用了 `BIGINT`，但如果涉及到需要更高精度的计算（虽然在虚拟货币中少见），可能需要考虑使用 `DECIMAL` 类型。
*   **可扩展性**: 设计时考虑未来可能增加的新货币或道具类型。

选择合适的数据模型和设计健壮的 API 是后端实现的关键。请根据具体的业务场景和性能需求来决定最终方案。 