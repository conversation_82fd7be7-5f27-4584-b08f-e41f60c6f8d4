#nullable enable
// File: Application/Features/Tasks/Dtos/UpdateTaskRequestDto.cs
// Description: 更新任务请求的数据传输对象

using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace ItAssetsSystem.Application.Features.Tasks.Dtos
{
    /// <summary>
    /// 更新任务请求 DTO
    /// </summary>
    public class UpdateTaskRequestDto
    {
        [StringLength(200, ErrorMessage = "任务名称长度不能超过200个字符")]
        public string? Name { get; set; }

        [StringLength(2000, ErrorMessage = "任务描述长度不能超过2000个字符")]
        public string? Description { get; set; }

        [StringLength(50, ErrorMessage = "状态长度不能超过50个字符")]
        public string? Status { get; set; }

        [StringLength(50, ErrorMessage = "优先级长度不能超过50个字符")]
        public string? Priority { get; set; }
        
        [StringLength(50, ErrorMessage = "任务类型长度不能超过50个字符")]
        public string? TaskType { get; set; }

        public DateTime? PlanStartDate { get; set; }

        public DateTime? PlanEndDate { get; set; }

        public DateTime? ActualStartDate { get; set; }

        public DateTime? ActualEndDate { get; set; }

        public int? AssigneeUserId { get; set; }
        
        /// <summary>
        /// 协作者用户ID列表，包括所有除主负责人外的协作者
        /// </summary>
        [System.Text.Json.Serialization.JsonPropertyName("collaboratorUserIds")]
        public List<int>? CollaboratorUserIds { get; set; }

        public long? ParentTaskId { get; set; }

        [StringLength(50, ErrorMessage = "PDCA阶段长度不能超过50个字符")]
        public string? PDCAStage { get; set; }

        public int? Points { get; set; }

        [Range(0, 100, ErrorMessage = "进度必须在0到100之间")]
        public int? Progress { get; set; }

        public long? ProjectId { get; set; } // 假设项目ID是long类型

        public int? AssetId { get; set; } // 核心资产ID是int

        public int? LocationId { get; set; } // 核心位置ID是int

        // 注意: ParticipantUserIdsList 通常不由 UpdateTaskAsync 直接处理，
        // 而是通过单独的接口如 AddAssigneeAsync, RemoveAssigneeAsync 管理。
        // 如果需要在此 DTO 中批量更新参与者，TaskService 中的 UpdateTaskAsync 逻辑需要相应调整。
        // public List<int>? ParticipantUserIdsList { get; set; }

        public bool? IsOverdueAcknowledged { get; set; }

        /// <summary>
        /// 完成用户ID (用于任务完成水印)
        /// </summary>
        public int? CompletedByUserId { get; set; }

        /// <summary>
        /// 完成用户水印颜色 (十六进制颜色代码)
        /// </summary>
        [StringLength(7, ErrorMessage = "颜色代码长度不能超过7个字符")]
        public string? CompletionWatermarkColor { get; set; }
    }
}