{"version": 3, "file": "mention-dropdown.js", "sources": ["../../../../../../packages/components/mention/src/mention-dropdown.ts"], "sourcesContent": ["import { buildProps, definePropType, isString } from '@element-plus/utils'\n\nimport type { MentionOption } from './types'\n\nexport const mentionDropdownProps = buildProps({\n  options: {\n    type: definePropType<MentionOption[]>(Array),\n    default: () => [],\n  },\n  loading: Boolean,\n  disabled: Boolean,\n  contentId: String,\n  ariaLabel: String,\n})\n\nexport const mentionDropdownEmits = {\n  select: (option: MentionOption) => isString(option.value),\n}\n"], "names": ["buildProps", "definePropType", "isString"], "mappings": ";;;;;;;AACY,MAAC,oBAAoB,GAAGA,kBAAU,CAAC;AAC/C,EAAE,OAAO,EAAE;AACX,IAAI,IAAI,EAAEC,sBAAc,CAAC,KAAK,CAAC;AAC/B,IAAI,OAAO,EAAE,MAAM,EAAE;AACrB,GAAG;AACH,EAAE,OAAO,EAAE,OAAO;AAClB,EAAE,QAAQ,EAAE,OAAO;AACnB,EAAE,SAAS,EAAE,MAAM;AACnB,EAAE,SAAS,EAAE,MAAM;AACnB,CAAC,EAAE;AACS,MAAC,oBAAoB,GAAG;AACpC,EAAE,MAAM,EAAE,CAAC,MAAM,KAAKC,eAAQ,CAAC,MAAM,CAAC,KAAK,CAAC;AAC5C;;;;;"}