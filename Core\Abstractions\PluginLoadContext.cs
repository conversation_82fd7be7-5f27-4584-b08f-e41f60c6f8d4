using System;
using System.Reflection;
using System.Runtime.Loader;

namespace ItAssetsSystem.Core.Abstractions
{
    /// <summary>
    /// 插件加载上下文
    /// </summary>
    public class PluginLoadContext : AssemblyLoadContext
    {
        private readonly string _pluginPath;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="pluginPath">插件路径</param>
        public PluginLoadContext(string pluginPath)
            : base(isCollectible: true)
        {
            _pluginPath = pluginPath ?? throw new ArgumentNullException(nameof(pluginPath));
        }

        /// <inheritdoc/>
        protected override Assembly Load(AssemblyName assemblyName)
        {
            // 尝试从插件目录加载程序集
            var assemblyPath = System.IO.Path.Combine(
                System.IO.Path.GetDirectoryName(_pluginPath),
                assemblyName.Name + ".dll");

            if (System.IO.File.Exists(assemblyPath))
            {
                return LoadFromAssemblyPath(assemblyPath);
            }

            // 如果找不到，则从默认加载上下文加载
            return Assembly.Load(assemblyName);
        }
    }
} 