/**
 * 航空航天级IT资产管理系统 - 返厂管理页面
 * 文件路径: src/views/return-to-factory/list.vue
 * 功能描述: 管理设备返厂维修记录
 */

<template>
  <div class="return-to-factory-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>返厂管理</h2>
      <p>管理设备返厂维修记录，跟踪维修状态和进度</p>
    </div>

    <!-- 筛选条件 -->
    <el-card class="filter-card" shadow="never">
      <el-form :model="filterForm" inline>
        <el-form-item label="返厂编号">
          <el-input 
            v-model="filterForm.code" 
            placeholder="请输入返厂编号" 
            clearable 
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="资产名称">
          <el-input 
            v-model="filterForm.assetName" 
            placeholder="请输入资产名称" 
            clearable 
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select 
            v-model="filterForm.status" 
            placeholder="请选择状态" 
            clearable 
            style="width: 150px"
          >
            <el-option 
              v-for="item in statusOptions" 
              :key="item.value" 
              :label="item.label" 
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="filterForm.timeRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 240px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :icon="Search" @click="handleSearch">搜索</el-button>
          <el-button :icon="RefreshRight" @click="resetFilter">重置</el-button>
          <el-button type="success" :icon="Download" @click="handleExportData">导出</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card" shadow="never">
      <el-table 
        :data="returnList" 
        v-loading="loading" 
        stripe 
        border
        style="width: 100%"
      >
        <el-table-column prop="code" label="返厂编号" width="120" />
        <el-table-column prop="assetName" label="资产名称" width="150" />
        <el-table-column prop="assetCode" label="资产编号" width="120" />
        <el-table-column prop="supplierName" label="供应商" width="120" />
        <el-table-column prop="reason" label="返厂原因" width="200" show-overflow-tooltip />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="sendTime" label="送出时间" width="120" />
        <el-table-column prop="estimatedReturnTime" label="预计返回" width="120" />
        <el-table-column prop="actualReturnTime" label="实际返回" width="120" />
        <el-table-column prop="cost" label="维修费用" width="100">
          <template #default="{ row }">
            <span v-if="row.cost">¥{{ row.cost }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" :icon="View" @click="handleViewDetail(row)">
              详情
            </el-button>
            <el-button 
              v-if="row.status !== 'returned'" 
              type="warning" 
              size="small" 
              :icon="Edit" 
              @click="handleUpdateStatus(row)"
            >
              更新状态
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 状态更新对话框 -->
    <el-dialog 
      v-model="statusDialogVisible" 
      title="更新返厂状态" 
      width="500px"
    >
      <el-form :model="statusForm" label-width="100px">
        <el-form-item label="当前状态">
          <el-tag :type="getStatusType(currentReturn?.status)">
            {{ getStatusText(currentReturn?.status) }}
          </el-tag>
        </el-form-item>
        <el-form-item label="新状态" required>
          <el-select v-model="statusForm.status" placeholder="请选择新状态">
            <el-option 
              v-for="item in getAvailableStatuses(currentReturn?.status)" 
              :key="item.value" 
              :label="item.label" 
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="维修费用" v-if="statusForm.status === 'returned'">
          <el-input-number 
            v-model="statusForm.cost" 
            :min="0" 
            :precision="2" 
            placeholder="请输入维修费用"
          />
        </el-form-item>
        <el-form-item label="备注">
          <el-input 
            v-model="statusForm.notes" 
            type="textarea" 
            :rows="3" 
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="statusDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmUpdateStatus">确认</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import returnToFactoryApi from '@/api/returnToFactory'
import { Search, Download, View, Edit, RefreshRight } from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const returnList = ref([])
const statusDialogVisible = ref(false)
const currentReturn = ref(null)

// 筛选表单
const filterForm = reactive({
  code: '',
  assetName: '',
  status: '',
  timeRange: []
})

// 状态更新表单
const statusForm = reactive({
  status: '',
  cost: null,
  notes: ''
})

// 分页配置
const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

// 状态选项
const statusOptions = [
  { label: '待送出', value: 'pending' },
  { label: '已送出', value: 'sent' },
  { label: '维修中', value: 'repairing' },
  { label: '已返回', value: 'returned' }
]

// 生命周期钩子
onMounted(() => {
  fetchReturnList()
})

// 获取返厂列表
const fetchReturnList = async () => {
  loading.value = true
  
  try {
    const params = {
      pageIndex: pagination.currentPage,
      pageSize: pagination.pageSize,
      code: filterForm.code,
      assetName: filterForm.assetName,
      status: filterForm.status,
      startTime: filterForm.timeRange?.[0],
      endTime: filterForm.timeRange?.[1]
    }
    
    const response = await returnToFactoryApi.getReturnToFactoryList(params)

    if (response.success) {
      returnList.value = response.data || []
      pagination.total = response.data?.length || 0
    } else {
      ElMessage.error(response.message || '获取返厂列表失败')
    }
    
  } catch (error) {
    console.error('Error fetching return list:', error)
    ElMessage.error('获取返厂列表失败')

    // 清空数据
    returnList.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

// 获取状态类型
const getStatusType = (status) => {
  const types = {
    pending: 'info',
    sent: 'warning',
    repairing: 'primary',
    returned: 'success'
  }
  return types[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const texts = {
    pending: '待送出',
    sent: '已送出',
    repairing: '维修中',
    returned: '已返回'
  }
  return texts[status] || '未知'
}

// 获取可用状态
const getAvailableStatuses = (currentStatus) => {
  const statusFlow = {
    pending: [{ label: '已送出', value: 'sent' }],
    sent: [{ label: '维修中', value: 'repairing' }],
    repairing: [{ label: '已返回', value: 'returned' }],
    returned: []
  }
  return statusFlow[currentStatus] || []
}

// 搜索
const handleSearch = () => {
  pagination.currentPage = 1
  fetchReturnList()
}

// 重置筛选条件
const resetFilter = () => {
  Object.keys(filterForm).forEach(key => {
    filterForm[key] = key === 'timeRange' ? [] : ''
  })
  pagination.currentPage = 1
  fetchReturnList()
}

// 分页事件
const handleSizeChange = (size) => {
  pagination.pageSize = size
  fetchReturnList()
}

const handleCurrentChange = (page) => {
  pagination.currentPage = page
  fetchReturnList()
}

// 查看详情
const handleViewDetail = (row) => {
  ElMessage.info(`查看返厂详情：${row.code}`)
}

// 更新状态
const handleUpdateStatus = (row) => {
  currentReturn.value = row
  statusForm.status = ''
  statusForm.cost = null
  statusForm.notes = ''
  statusDialogVisible.value = true
}

// 确认更新状态
const confirmUpdateStatus = async () => {
  try {
    if (!statusForm.status) {
      ElMessage.warning('请选择新状态')
      return
    }
    
    const response = await returnToFactoryApi.updateReturnStatus(currentReturn.value.id, statusForm)
    
    if (response.success) {
      ElMessage.success('状态更新成功')
      statusDialogVisible.value = false
      fetchReturnList()
    } else {
      ElMessage.error(response.message || '状态更新失败')
    }
  } catch (error) {
    console.error('更新状态失败:', error)
    ElMessage.error('状态更新失败：' + error.message)
  }
}

// 导出数据
const handleExportData = () => {
  ElMessage.success('开始导出数据，请稍候...')
}
</script>

<style scoped>
.return-to-factory-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  color: #303133;
  margin: 0 0 8px 0;
}

.page-header p {
  color: #909399;
  margin: 0;
}

.filter-card, .table-card {
  margin-bottom: 20px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
