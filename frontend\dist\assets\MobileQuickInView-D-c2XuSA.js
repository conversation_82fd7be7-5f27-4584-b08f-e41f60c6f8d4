import{_ as e,r as a,ad as l,c as s,z as t,E as o,b as u,d as r,$ as n,e as d,w as i,t as c,f as m,al as p,u as v,a as f,o as b,aL as y,bW as k,D as V,p as _,F as h,h as w,k as I,aM as g}from"./index-CG5lHOPO.js";import{getSparePartLocations as C,getSpareParts as U}from"./spareparts-DKUrs8IX.js";const q={class:"quick-inbound-view"},x={class:"mobile-header"},N={class:"scan-search-area"},T={key:0,class:"part-info-card"},z={class:"part-header"},L={class:"part-name"},S={class:"part-code"},j={class:"part-details"},M={class:"info-item"},D={class:"value"},E={class:"info-item"},F={class:"value"},Q={class:"info-item"},W={class:"value"},$={key:1,class:"part-selector"},A={key:2,class:"inbound-form"},B={key:3,class:"submit-area"},G={class:"part-search"},H={class:"part-list"},J=["onClick"],K={class:"part-main-info"},O={class:"part-item-name"},P={class:"part-item-code"},R={class:"part-extra-info"},X={class:"stock"},Y=e({__name:"MobileQuickInView",setup(e){const Y=v(),Z=a(""),ee=a(null),ae=a(!1),le=a(""),se=a(!1),te=a([]),oe=a([]),ue=l({partId:null,quantity:1,reasonType:1,locationId:null,referenceNumber:"",remarks:""}),re=s((()=>{if(!le.value)return oe.value;const e=le.value.toLowerCase();return oe.value.filter((a=>a.name.toLowerCase().includes(e)||a.code.toLowerCase().includes(e)))}));t((()=>{ne(),de()}));const ne=async()=>{try{const e=await C();e.success?(te.value=e.data,te.value.length>0&&(ue.locationId=te.value[0].id)):o.error(e.message||"获取库位失败")}catch(e){o.error("获取库位失败，请稍后重试")}},de=async()=>{try{const e=await U({pageIndex:1,pageSize:100});e.success?oe.value=e.data.items:o.error(e.message||"获取备件列表失败")}catch(e){o.error("获取备件列表失败，请稍后重试")}},ie=()=>{Y.back()},ce=()=>{o.info("扫码功能待实现")},me=()=>{ae.value=!0},pe=()=>{ee.value=null,ue.partId=null},ve=async()=>{if(ee.value)if(ue.locationId){se.value=!0;try{const e={partId:ue.partId,quantity:ue.quantity,type:1,reasonType:ue.reasonType,locationId:ue.locationId,referenceNumber:ue.referenceNumber,remarks:ue.remarks},a=await(void 0)(e);a.success?(o.success("入库成功"),pe(),ue.quantity=1,ue.referenceNumber="",ue.remarks="",Z.value=""):o.error(a.message||"入库失败")}catch(e){o.error("入库失败，请稍后重试")}finally{se.value=!1}}else o.warning("请选择库位");else o.warning("请先选择备件")};return(e,a)=>{const l=f("el-icon"),s=f("el-button"),t=f("el-input"),o=f("el-input-number"),v=f("el-form-item"),C=f("el-option"),U=f("el-select"),Y=f("el-form"),oe=f("el-drawer");return b(),u("div",q,[r("div",x,[r("div",{class:"back-button",onClick:ie},[d(l,null,{default:i((()=>[d(m(y))])),_:1})]),a[8]||(a[8]=r("div",{class:"title"},"快速入库",-1)),a[9]||(a[9]=r("div",null,null,-1))]),r("div",N,[d(t,{modelValue:Z.value,"onUpdate:modelValue":a[0]||(a[0]=e=>Z.value=e),placeholder:"扫描或输入备件编号/条码",clearable:""},{prefix:i((()=>[d(l,null,{default:i((()=>[d(m(V))])),_:1})])),append:i((()=>[d(s,{icon:m(k),onClick:ce},null,8,["icon"])])),_:1},8,["modelValue"])]),ee.value?(b(),u("div",T,[r("div",z,[r("div",null,[r("div",L,c(ee.value.name),1),r("div",S,c(ee.value.code),1)]),d(s,{type:"danger",size:"small",icon:m(p),circle:"",onClick:pe},null,8,["icon"])]),r("div",j,[r("div",M,[a[10]||(a[10]=r("span",{class:"label"},"类型:",-1)),r("span",D,c(ee.value.typeName),1)]),r("div",E,[a[11]||(a[11]=r("span",{class:"label"},"规格:",-1)),r("span",F,c(ee.value.specification),1)]),r("div",Q,[a[12]||(a[12]=r("span",{class:"label"},"库存:",-1)),r("span",W,c(ee.value.currentStock),1)])])])):n("",!0),ee.value?n("",!0):(b(),u("div",$,[d(s,{type:"primary",block:"",onClick:me},{default:i((()=>a[13]||(a[13]=[_("选择备件")]))),_:1})])),ee.value?(b(),u("div",A,[d(Y,{model:ue,"label-position":"top"},{default:i((()=>[d(v,{label:"入库数量"},{default:i((()=>[d(o,{modelValue:ue.quantity,"onUpdate:modelValue":a[1]||(a[1]=e=>ue.quantity=e),min:1,"controls-position":"right",style:{width:"100%"}},null,8,["modelValue"])])),_:1}),d(v,{label:"入库原因"},{default:i((()=>[d(U,{modelValue:ue.reasonType,"onUpdate:modelValue":a[2]||(a[2]=e=>ue.reasonType=e),placeholder:"选择入库原因",style:{width:"100%"}},{default:i((()=>[d(C,{label:"采购入库",value:1}),d(C,{label:"退回入库",value:2}),d(C,{label:"盘点调整",value:5})])),_:1},8,["modelValue"])])),_:1}),d(v,{label:"库位"},{default:i((()=>[d(U,{modelValue:ue.locationId,"onUpdate:modelValue":a[3]||(a[3]=e=>ue.locationId=e),filterable:"",placeholder:"选择库位",style:{width:"100%"}},{default:i((()=>[(b(!0),u(h,null,w(te.value,(e=>(b(),I(C,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),d(v,{label:"关联单号"},{default:i((()=>[d(t,{modelValue:ue.referenceNumber,"onUpdate:modelValue":a[4]||(a[4]=e=>ue.referenceNumber=e),placeholder:"选填，相关采购单号"},null,8,["modelValue"])])),_:1}),d(v,{label:"备注"},{default:i((()=>[d(t,{modelValue:ue.remarks,"onUpdate:modelValue":a[5]||(a[5]=e=>ue.remarks=e),type:"textarea",placeholder:"选填，备注信息",rows:3},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])):n("",!0),ee.value?(b(),u("div",B,[d(s,{type:"primary",block:"",onClick:ve,loading:se.value},{default:i((()=>a[14]||(a[14]=[_(" 确认入库 ")]))),_:1},8,["loading"])])):n("",!0),d(oe,{modelValue:ae.value,"onUpdate:modelValue":a[7]||(a[7]=e=>ae.value=e),title:"选择备件",direction:"bottom",size:"90%"},{default:i((()=>[r("div",G,[d(t,{modelValue:le.value,"onUpdate:modelValue":a[6]||(a[6]=e=>le.value=e),placeholder:"搜索备件",clearable:""},{prefix:i((()=>[d(l,null,{default:i((()=>[d(m(V))])),_:1})])),_:1},8,["modelValue"])]),r("div",H,[(b(!0),u(h,null,w(re.value,((e,a)=>(b(),u("div",{key:a,class:"part-item",onClick:a=>(e=>{ee.value=e,ue.partId=e.id,ae.value=!1})(e)},[r("div",K,[r("div",O,c(e.name),1),r("div",P,c(e.code),1)]),r("div",R,[r("span",X,"库存: "+c(e.currentStock),1),d(l,null,{default:i((()=>[d(m(g))])),_:1})])],8,J)))),128))])])),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-58afb322"]]);export{Y as default};
