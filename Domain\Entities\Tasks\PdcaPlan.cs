// File: Domain/Entities/Tasks/PdcaPlan.cs
// Description: PDCA 计划实体
#nullable enable
using System;
using System.Collections.Generic;
using ItAssetsSystem.Models.Entities; // Corrected: Assuming User is here

namespace ItAssetsSystem.Domain.Entities.Tasks
{
    /// <summary>
    /// PDCA计划信息表 (V2 - BIGINT PK)
    /// </summary>
    public class PdcaPlan
    {
        /// <summary>
        /// PDCA计划主键ID (BIGINT)
        /// </summary>
        public long PdcaPlanId { get; set; }

        /// <summary>
        /// 关联的任务ID (BIGINT, 指向 TaskType='PDCA' 的任务)
        /// </summary>
        public long TaskId { get; set; }
        /// <summary>
        /// 关联的任务实体
        /// </summary>
        public virtual Task Task { get; set; } = null!;

        /// <summary>
        /// 创建用户ID (INT, 关联 users.Id)
        /// </summary>
        public int CreatorUserId { get; set; }
        /// <summary>
        /// 关联的创建者用户实体
        /// </summary>
        public virtual User CreatorUser { get; set; } = null!; // Adjust User namespace if needed

        /// <summary>
        /// 负责人用户ID (INT, 关联 users.Id)
        /// </summary>
        public int ResponsiblePersonId { get; set; }
        /// <summary>
        /// 关联的负责人用户实体
        /// </summary>
        public virtual User ResponsiblePerson { get; set; } = null!; // Adjust User namespace if needed

        /// <summary>
        /// 计划标题
        /// </summary>
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// 计划阶段 (Plan, Do, Check, Act)
        /// </summary>
        public string Stage { get; set; } = "Plan"; // Matches Task.PDCAStage

        /// <summary>
        /// 目标/标准
        /// </summary>
        public string? Goal { get; set; }

        /// <summary>
        /// Plan阶段内容
        /// </summary>
        public string? PlanContent { get; set; }

        /// <summary>
        /// Do阶段执行记录
        /// </summary>
        public string? DoRecord { get; set; }

        /// <summary>
        /// Check阶段检查结果
        /// </summary>
        public string? CheckResult { get; set; }

        /// <summary>
        /// Act阶段处理措施
        /// </summary>
        public string? ActAction { get; set; }

        /// <summary>
        /// PDCA状态 (0:进行中, 1:已完成, 2:已关闭)
        /// </summary>
        public int Status { get; set; }

        /// <summary>
        /// 计划完成率 (0.00 - 100.00)
        /// </summary>
        public decimal CompletionRate { get; set; }

        /// <summary>
        /// 额外备注
        /// </summary>
        public string? Notes { get; set; }

        /// <summary>
        /// 记录创建时间
        /// </summary>
        public DateTime CreationTimestamp { get; set; }

        /// <summary>
        /// 记录最后更新时间
        /// </summary>
        public DateTime LastUpdatedTimestamp { get; set; }
    }
} 