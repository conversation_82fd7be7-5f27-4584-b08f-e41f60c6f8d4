// File: Models/Entities/Gamification/BehaviorType.cs
// Description: 游戏化行为类型定义

using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ItAssetsSystem.Models.Entities.Gamification
{
    /// <summary>
    /// 游戏化行为类型配置表
    /// </summary>
    [Table("gamification_behavior_types")]
    public class BehaviorType
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Key]
        [Column("id")]
        public int Id { get; set; }

        /// <summary>
        /// 行为代码（唯一标识）
        /// </summary>
        [Required]
        [MaxLength(50)]
        [Column("code")]
        public string Code { get; set; } = string.Empty;

        /// <summary>
        /// 行为名称
        /// </summary>
        [Required]
        [MaxLength(100)]
        [Column("name")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 行为描述
        /// </summary>
        [MaxLength(255)]
        [Column("description")]
        public string? Description { get; set; }

        /// <summary>
        /// 基础积分
        /// </summary>
        [Column("base_points")]
        public int BasePoints { get; set; }

        /// <summary>
        /// 积分奖励（别名）
        /// </summary>
        [NotMapped]
        public int PointsReward => BasePoints;

        /// <summary>
        /// 基础金币
        /// </summary>
        [Column("base_coins")]
        public int BaseCoins { get; set; }

        /// <summary>
        /// 金币奖励（别名）
        /// </summary>
        [NotMapped]
        public int CoinsReward => BaseCoins;

        /// <summary>
        /// 基础钻石
        /// </summary>
        [Column("base_diamonds")]
        public int BaseDiamonds { get; set; }

        /// <summary>
        /// 钻石奖励（别名）
        /// </summary>
        [NotMapped]
        public int DiamondsReward => BaseDiamonds;

        /// <summary>
        /// 积分系数（可配置）
        /// </summary>
        [Column("multiplier")]
        public decimal Multiplier { get; set; } = 1.0m;

        /// <summary>
        /// 是否启用
        /// </summary>
        [Column("is_active")]
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// 行为分类（TASK/ASSET/FAULT/MAINTENANCE）
        /// </summary>
        [MaxLength(20)]
        [Column("category")]
        public string Category { get; set; } = string.Empty;

        /// <summary>
        /// 创建时间
        /// </summary>
        [Column("created_at")]
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        [Column("updated_at")]
        public DateTime UpdatedAt { get; set; }
    }

    /// <summary>
    /// 行为类型枚举
    /// </summary>
    public static class BehaviorCodes
    {
        // 任务相关
        public const string TASK_CREATED = "TASK_CREATED";
        public const string TASK_COMPLETED = "TASK_COMPLETED";
        public const string TASK_CLAIMED = "TASK_CLAIMED";
        public const string TASK_DELETED = "TASK_DELETED"; // 负分行为

        // 资产相关
        public const string ASSET_CREATED = "ASSET_CREATED";
        public const string ASSET_UPDATED = "ASSET_UPDATED";
        public const string ASSET_TRANSFERRED = "ASSET_TRANSFERRED";

        // 故障相关
        public const string FAULT_RECORDED = "FAULT_RECORDED";
        public const string FAULT_RESOLVED = "FAULT_RESOLVED";

        // 维修相关
        public const string MAINTENANCE_CREATED = "MAINTENANCE_CREATED";
        public const string MAINTENANCE_COMPLETED = "MAINTENANCE_COMPLETED";

        // 备件相关
        public const string SPAREPART_INBOUND = "SPAREPART_INBOUND";
        public const string SPAREPART_OUTBOUND = "SPAREPART_OUTBOUND";
        public const string SPAREPART_UPDATED = "SPAREPART_UPDATED";

        // 采购相关
        public const string PURCHASE_CREATED = "PURCHASE_CREATED";
        public const string PURCHASE_UPDATED = "PURCHASE_UPDATED";

        // 返厂相关
        public const string RETURN_FACTORY_CREATED = "RETURN_FACTORY_CREATED";
        public const string RETURN_FACTORY_COMPLETED = "RETURN_FACTORY_COMPLETED";
    }
}
