import{_ as e,u as a,c as l,r as t,a as s,b as n,o as i,d as u,e as d,w as o,f as r,g as c,n as p,F as m,h as f,i as _,t as v,j as g,k as y,l as h,m as k,p as b,q as x,s as w,v as C,E as V,x as I,y as D,z as L,A as S,B as U,C as P,D as R,G as j,H as z,I as N,J as M,K as T,L as E,M as $,N as A,O as q,P as F,Q as W,R as B,S as G,T as H,U as J,V as K,W as O,X as Q,Y as X,Z as Y,$ as Z,a0 as ee,a1 as ae,a2 as le,a3 as te,a4 as se,a5 as ne,a6 as ie,a7 as ue,a8 as de,a9 as oe}from"./index-CG5lHOPO.js";import{u as re}from"./gamification-Dm7mCEPf.js";import{u as ce}from"./notification-Cv7sgGe-.js";import{n as pe}from"./notification-service-w2_wgXl6.js";import{f as me,z as fe}from"./zh-CN-B1csyosV.js";import{N as _e}from"./NotificationCenter-R_b7DCjJ.js";import{s as ve}from"./statistics-BoWGSZxY.js";import"./gamification-2FBMrBgR.js";import"./en-US-BvtvdVHO.js";const ge={class:"activity-ticker-container"},ye={class:"ticker-icon"},he={class:"ticker-wrap"},ke=["onClick"],be={class:"item-content"},xe={class:"item-time"},we=["onClick"],Ce={class:"item-content"},Ve={class:"item-time"},Ie=e({__name:"ActivityTicker",setup(e){const g=a(),y=re(),h=l((()=>y.recentActivities.map((e=>({id:e.id,type:e.type||"notification",content:e.text||"无内容",timestamp:e.timestamp,relatedId:e.relatedId}))))),k=t(null),b=l((()=>{const e=h.value.length;return`${Math.max(5,3*e)}s`})),x=e=>{switch(e){case"task_assigned":return"新任务";case"task_completed":return"任务完成";case"reward":return"奖励";case"mention":return"＠提及";case"system":return"系统";case"achievement":return"成就达成";case"comment":return"新评论";default:return"通知"}},w=e=>{if(!e)return"";try{const a=new Date(e);return isNaN(a.getTime())?"无效日期":me(a,{addSuffix:!0,locale:fe})}catch(a){return"无效日期"}},C=e=>{"task_assigned"!==e.type&&"mention"!==e.type&&"task_completed"!==e.type||!e.relatedId?"achievement"===e.type&&e.relatedId:g.push({name:"TaskDetail",params:{id:e.relatedId}}).catch((e=>{}))},V=e=>{const a=e.target.closest(".ticker-list");a&&(a.style.animationPlayState="paused")},I=e=>{const a=e.target.closest(".ticker-list");a&&(a.style.animationPlayState="running")};return(e,a)=>{const l=s("el-icon");return i(),n("div",ge,[u("div",ye,[d(l,null,{default:o((()=>[d(r(c))])),_:1})]),u("div",he,[u("ul",{class:"ticker-list",style:p({animationDuration:b.value}),ref_key:"tickerListRef",ref:k},[(i(!0),n(m,null,f(h.value,(e=>(i(),n("li",{key:e.id,class:"ticker-item",onClick:a=>C(e),onMouseover:V,onMouseout:I},[u("span",{class:_(["item-type",`type-${e.type}`])},"["+v(x(e.type))+"]",3),u("span",be,v(e.content),1),u("span",xe,v(w(e.timestamp)),1)],40,ke)))),128)),(i(!0),n(m,null,f(h.value,(e=>(i(),n("li",{key:`dup-${e.id}`,class:"ticker-item",onClick:a=>C(e),onMouseover:V,onMouseout:I,"aria-hidden":"true"},[u("span",{class:_(["item-type",`type-${e.type}`])},"["+v(x(e.type))+"]",3),u("span",Ce,v(e.content),1),u("span",Ve,v(w(e.timestamp)),1)],40,we)))),128))],4)])])}}},[["__scopeId","data-v-7bb0690c"]]),De={class:"dialog-header"},Le={class:"header-left"},Se={class:"header-right"},Ue={class:"dialog-content"},Pe={class:"summary-stats"},Re={class:"stat-card"},je={class:"stat-value"},ze={class:"stat-card"},Ne={class:"stat-value"},Me={class:"stat-card"},Te={class:"stat-value"},Ee={class:"stat-card"},$e={class:"stat-value"},Ae={class:"points-value"},qe={class:"dialog-footer"},Fe=e({__name:"WorkSummaryDialog",props:{modelValue:{type:Boolean,default:!1}},emits:["update:modelValue"],setup(e,{emit:a}){const n=e,c=a,p=l({get:()=>n.modelValue,set:e=>c("update:modelValue",e)}),m=t(!1),f=t("weekly"),_=t([]),I=l((()=>_.value.reduce(((e,a)=>e+(a.tasksCompleted||0)+(a.tasksCreated||0)),0))),D=l((()=>_.value.reduce(((e,a)=>e+(a.totalPointsEarned||0)),0))),L=l((()=>{if(0===_.value.length)return 0;return _.value.reduce(((e,a)=>e+(a.productivityScore||0)),0)/_.value.length})),S=async()=>{m.value=!0;try{const e=await ve.getWorkSummary({periodType:f.value,limit:100});if(!e.data.success)throw new Error(e.data.message||"获取工作汇总失败");_.value=e.data.data||[]}catch(e){V.error("获取工作汇总失败"),_.value=U()}finally{m.value=!1}},U=()=>[{userName:"张三",tasksCompleted:12,tasksCreated:8,assetsCreated:5,assetsUpdated:2,faultsReported:3,faultsRepaired:1,totalPointsEarned:156,pointsRank:1,evaluation:"超级",productivityScore:85.5},{userName:"李四",tasksCompleted:9,tasksCreated:15,assetsCreated:3,assetsUpdated:4,faultsReported:1,faultsRepaired:2,totalPointsEarned:142,pointsRank:2,evaluation:"优秀",productivityScore:78.2}],P=e=>{switch(e){case"超级":return"danger";case"优秀":return"success";case"良好":return"warning";case"一般":return"info";default:return""}},R=()=>{V.info("导出功能开发中...")},j=()=>{p.value=!1};return g(p,(e=>{e&&S()})),(e,a)=>{const l=s("el-icon"),t=s("el-tag"),n=s("el-option"),c=s("el-select"),g=s("el-button"),V=s("el-col"),U=s("el-row"),z=s("el-table-column"),N=s("el-table"),M=s("el-dialog"),T=k("loading");return i(),y(M,{modelValue:p.value,"onUpdate:modelValue":a[1]||(a[1]=e=>p.value=e),title:"工作汇总报告",width:"90%","before-close":j,class:"work-summary-dialog"},{header:o((()=>[u("div",De,[u("div",Le,[d(l,{class:"header-icon"},{default:o((()=>[d(r(w))])),_:1}),a[2]||(a[2]=u("span",{class:"dialog-title"},"工作汇总报告",-1)),d(t,{type:"info",size:"small",class:"period-tag"},{default:o((()=>{return[b(v((e=f.value,{weekly:"本周",monthly:"本月",quarterly:"本季度",yearly:"本年"}[e]||"未知")),1)];var e})),_:1})]),u("div",Se,[d(c,{modelValue:f.value,"onUpdate:modelValue":a[0]||(a[0]=e=>f.value=e),onChange:S,size:"small",class:"period-select"},{default:o((()=>[d(n,{label:"本周",value:"weekly"}),d(n,{label:"本月",value:"monthly"}),d(n,{label:"本季度",value:"quarterly"}),d(n,{label:"本年",value:"yearly"})])),_:1},8,["modelValue"]),d(g,{size:"small",type:"primary",icon:r(C),onClick:S,loading:m.value},{default:o((()=>a[3]||(a[3]=[b(" 刷新 ")]))),_:1},8,["icon","loading"])])])])),footer:o((()=>[u("div",qe,[d(g,{onClick:j},{default:o((()=>a[8]||(a[8]=[b("关闭")]))),_:1}),d(g,{type:"primary",onClick:R},{default:o((()=>[d(l,null,{default:o((()=>[d(r(x))])),_:1}),a[9]||(a[9]=b(" 导出数据 "))])),_:1})])])),default:o((()=>[u("div",Ue,[u("div",Pe,[d(U,{gutter:16},{default:o((()=>[d(V,{span:6},{default:o((()=>[u("div",Re,[u("div",je,v(_.value.length),1),a[4]||(a[4]=u("div",{class:"stat-label"},"参与人数",-1))])])),_:1}),d(V,{span:6},{default:o((()=>[u("div",ze,[u("div",Ne,v(I.value),1),a[5]||(a[5]=u("div",{class:"stat-label"},"总任务数",-1))])])),_:1}),d(V,{span:6},{default:o((()=>[u("div",Me,[u("div",Te,v(D.value),1),a[6]||(a[6]=u("div",{class:"stat-label"},"总积分",-1))])])),_:1}),d(V,{span:6},{default:o((()=>[u("div",Ee,[u("div",$e,v(L.value.toFixed(1)),1),a[7]||(a[7]=u("div",{class:"stat-label"},"平均生产力",-1))])])),_:1})])),_:1})]),h((i(),y(N,{data:_.value,stripe:"",border:"",height:"400","default-sort":{prop:"totalPointsEarned",order:"descending"},class:"summary-table"},{default:o((()=>[d(z,{type:"index",label:"#",width:"50",align:"center",fixed:"left"}),d(z,{prop:"userName",label:"姓名",width:"100",fixed:"left"}),d(z,{label:"📋 任务",align:"center"},{default:o((()=>[d(z,{prop:"tasksCompleted",label:"完成",width:"60",align:"center"}),d(z,{prop:"tasksCreated",label:"新建",width:"60",align:"center"}),d(z,{label:"总计",width:"60",align:"center"},{default:o((({row:e})=>[b(v((e.tasksCompleted||0)+(e.tasksCreated||0)),1)])),_:1})])),_:1}),d(z,{label:"🏢 资产",align:"center"},{default:o((()=>[d(z,{prop:"assetsCreated",label:"新建",width:"60",align:"center"}),d(z,{prop:"assetsUpdated",label:"更新",width:"60",align:"center"})])),_:1}),d(z,{label:"🔧 故障",align:"center"},{default:o((()=>[d(z,{prop:"faultsReported",label:"登记",width:"60",align:"center"}),d(z,{prop:"faultsRepaired",label:"维修",width:"60",align:"center"})])),_:1}),d(z,{label:"🏆 积分",align:"center"},{default:o((()=>[d(z,{prop:"totalPointsEarned",label:"积分",width:"80",align:"center"},{default:o((({row:e})=>[u("span",Ae,v(e.totalPointsEarned||0),1)])),_:1})])),_:1}),d(z,{prop:"pointsRank",label:"排名",width:"60",align:"center"},{default:o((({row:e})=>{return[d(t,{type:(a=e.pointsRank,1===a?"danger":2===a?"warning":3===a?"success":"info"),size:"small",round:""},{default:o((()=>[b(v(e.pointsRank||"-"),1)])),_:2},1032,["type"])];var a})),_:1}),d(z,{prop:"evaluation",label:"评价",width:"100",align:"center"},{default:o((({row:e})=>[d(t,{type:P(e.evaluation),size:"small",effect:"light"},{default:o((()=>[b(v(e.evaluation||"待评价"),1)])),_:2},1032,["type"])])),_:1})])),_:1},8,["data"])),[[T,m.value]])])])),_:1},8,["modelValue"])}}},[["__scopeId","data-v-88ce9999"]]),We={class:"app-container"},Be={class:"header-left"},Ge={class:"logo"},He={class:"header-right"},Je={class:"user-avatar-name"},Ke={class:"username"},Oe=e({__name:"DefaultLayout",setup(e){const c=a(),p=U(),m=I(),f=re(),h=D(),k=ce(),V=t("true"===localStorage.getItem("sidebarCollapse")),me=t(""),fe=t(!1);t(["Dashboard","AssetList","LocationTree","FaultList","TaskList"]);const ve=l((()=>{const{meta:e,path:a}=p;return e.activeMenu?e.activeMenu:a}));t("var(--sidebar-bg)"),t("var(--sidebar-text-color)"),t("var(--sidebar-active-text-color)");const ge=e=>{if(!e||0===e.length)return!0;const a=m.roles||[];return e.some((e=>a.includes(e)))},ye=e=>{switch(e){case"profile":c.push("/main/user/profile");break;case"password":oe.alert("修改密码功能待实现","提示",{type:"info"});break;case"logout":m.setLogoutDialogVisible(!0)}},he=async()=>{try{await m.logout(),c.push(`/login?redirect=${p.fullPath}`),m.setLogoutDialogVisible(!1)}catch(e){oe.alert("退出登录失败","错误",{type:"error"}),m.setLogoutDialogVisible(!1)}};L((async()=>{var e,a;if(!(null==(e=m.userInfo)?void 0:e.id))try{await m.getUserInfo()}catch(l){}if(f.initializeStore(),m.isLogin&&(null==(a=m.userInfo)?void 0:a.id))try{await k.fetchUnreadCount(),k.startPolling(),await pe.initConnection(m.userInfo.id)}catch(l){}V.value="true"===localStorage.getItem("sidebarCollapse")})),g(V,(e=>{localStorage.setItem("sidebarCollapse",e)}));const ke=()=>{h.openMemoDrawer("create")},be=t(!1),xe=l((()=>k.unreadCount)),we=()=>{be.value=!0},Ce=()=>{fe.value=!0},Ve=e=>{c.push(`/main/tasks/detail/${e}`),be.value=!1};return S((()=>{pe.disconnect(),k.stopPolling()})),(e,a)=>{const l=s("el-input"),t=s("el-button"),c=s("el-tooltip"),p=s("el-badge"),f=s("el-avatar"),g=s("el-icon"),h=s("el-dropdown-item"),k=s("el-dropdown-menu"),I=s("el-dropdown"),D=s("el-header"),L=s("el-menu-item"),S=s("el-sub-menu"),U=s("el-menu"),oe=s("el-scrollbar"),re=s("el-aside"),ce=s("router-view"),pe=s("el-main"),De=s("el-container"),Le=s("el-dialog");return i(),n("div",We,[d(D,{class:"header"},{default:o((()=>[u("div",Be,[u("div",Ge,v(r(P).appName||"IT资产管理系统"),1)]),u("div",He,[d(l,{modelValue:me.value,"onUpdate:modelValue":a[0]||(a[0]=e=>me.value=e),placeholder:"全局搜索...",style:{width:"250px","margin-right":"15px"},clearable:"","prefix-icon":r(R)},null,8,["modelValue","prefix-icon"]),d(c,{content:"新建随手记",placement:"bottom"},{default:o((()=>[d(t,{icon:r(j),circle:"",onClick:ke,style:{"margin-right":"10px"}},null,8,["icon"])])),_:1}),d(c,{content:"工作汇总报告",placement:"bottom"},{default:o((()=>[d(t,{icon:r(w),circle:"",onClick:Ce,style:{"margin-right":"10px"},type:"primary"},null,8,["icon"])])),_:1}),d(c,{content:"查看通知",placement:"bottom"},{default:o((()=>[d(p,{value:xe.value,hidden:0===xe.value,class:"item notification-badge-header",max:99},{default:o((()=>[d(t,{icon:r(z),circle:"",onClick:we},null,8,["icon"])])),_:1},8,["value","hidden"])])),_:1}),d(I,{trigger:"click",onCommand:ye},{dropdown:o((()=>[d(k,null,{default:o((()=>[d(h,{command:"profile"},{default:o((()=>[d(g,null,{default:o((()=>[d(r(M))])),_:1}),a[6]||(a[6]=b("个人中心 "))])),_:1}),d(h,{command:"password"},{default:o((()=>[d(g,null,{default:o((()=>[d(r(T))])),_:1}),a[7]||(a[7]=b("修改密码 "))])),_:1}),d(h,{divided:"",command:"logout"},{default:o((()=>[d(g,null,{default:o((()=>[d(r(E))])),_:1}),a[8]||(a[8]=b("退出登录 "))])),_:1})])),_:1})])),default:o((()=>{var e;return[u("span",Je,[d(f,{size:32,src:r(m).computedAvatarUrl,class:"avatar"},null,8,["src"]),u("span",Ke,v((null==(e=r(m).userInfo)?void 0:e.name)||"用户名"),1),d(g,{class:"el-icon--right"},{default:o((()=>[d(r(N))])),_:1})])]})),_:1})])])),_:1}),d(De,null,{default:o((()=>[d(re,{class:"sidebar",width:V.value?"64px":"210px"},{default:o((()=>[d(oe,null,{default:o((()=>[d(U,{"default-active":ve.value,collapse:V.value,class:"el-menu-vertical",router:"","unique-opened":!0},{default:o((()=>[d(L,{index:"/main/dashboard"},{title:o((()=>a[9]||(a[9]=[b("仪表盘")]))),default:o((()=>[d(g,null,{default:o((()=>[d(r($))])),_:1})])),_:1}),d(S,{index:"/main/asset"},{title:o((()=>[d(g,null,{default:o((()=>[d(r(W))])),_:1}),a[10]||(a[10]=u("span",null,"资产管理",-1))])),default:o((()=>[d(L,{index:"/main/asset/list"},{default:o((()=>[d(g,null,{default:o((()=>[d(r(A))])),_:1}),a[11]||(a[11]=u("span",null,"资产列表",-1))])),_:1}),d(L,{index:"/main/asset/type"},{default:o((()=>[d(g,null,{default:o((()=>[d(r(T))])),_:1}),a[12]||(a[12]=u("span",null,"资产类型",-1))])),_:1}),d(L,{index:"/main/asset/statistics"},{default:o((()=>[d(g,null,{default:o((()=>[d(r(q))])),_:1}),a[13]||(a[13]=u("span",null,"统计分析",-1))])),_:1}),d(L,{index:"/main/asset/analytics-workbench"},{default:o((()=>[d(g,null,{default:o((()=>[d(r(F))])),_:1}),a[14]||(a[14]=u("span",null,"智能分析工作台",-1))])),_:1})])),_:1}),d(S,{index:"/main/locations"},{title:o((()=>[d(g,null,{default:o((()=>[d(r(B))])),_:1}),a[15]||(a[15]=u("span",null,"位置管理",-1))])),default:o((()=>[d(L,{index:"/main/locations/structure"},{default:o((()=>a[16]||(a[16]=[u("span",null,"位置结构",-1)]))),_:1}),d(L,{index:"/main/locations/relations"},{default:o((()=>a[17]||(a[17]=[u("span",null,"位置关联",-1)]))),_:1})])),_:1}),d(S,{index:"/main/faults"},{title:o((()=>[d(g,null,{default:o((()=>[d(r(G))])),_:1}),a[18]||(a[18]=u("span",null,"故障管理",-1))])),default:o((()=>[d(L,{index:"/main/faults/list"},{default:o((()=>a[19]||(a[19]=[u("span",null,"故障列表",-1)]))),_:1}),d(L,{index:"/main/faults/maintenance"},{default:o((()=>a[20]||(a[20]=[u("span",null,"返厂/维修",-1)]))),_:1})])),_:1}),d(S,{index:"/main/purchases"},{title:o((()=>[d(g,null,{default:o((()=>[d(r(H))])),_:1}),a[21]||(a[21]=u("span",null,"采购管理",-1))])),default:o((()=>[d(L,{index:"/main/purchases/list"},{default:o((()=>a[22]||(a[22]=[u("span",null,"采购列表",-1)]))),_:1})])),_:1}),d(S,{index:"/main/spareparts"},{title:o((()=>[d(g,null,{default:o((()=>[d(r(Y))])),_:1}),a[23]||(a[23]=u("span",null,"备品备件管理",-1))])),default:o((()=>[d(L,{index:"/main/spareparts/list"},{default:o((()=>[d(g,null,{default:o((()=>[d(r(J))])),_:1}),a[24]||(a[24]=u("span",null,"备件台账",-1))])),_:1}),d(L,{index:"/main/spareparts/types"},{default:o((()=>[d(g,null,{default:o((()=>[d(r(K))])),_:1}),a[25]||(a[25]=u("span",null,"备件类型管理",-1))])),_:1}),d(L,{index:"/main/spareparts/locations"},{default:o((()=>[d(g,null,{default:o((()=>[d(r(O))])),_:1}),a[26]||(a[26]=u("span",null,"备件库位管理",-1))])),_:1}),d(L,{index:"/main/spareparts/transactions"},{default:o((()=>[d(g,null,{default:o((()=>[d(r(Q))])),_:1}),a[27]||(a[27]=u("span",null,"出入库记录",-1))])),_:1}),d(L,{index:"/main/spareparts/suppliers"},{default:o((()=>[d(g,null,{default:o((()=>[d(r(X))])),_:1}),a[28]||(a[28]=u("span",null,"供应商管理",-1))])),_:1})])),_:1}),d(S,{index:"/main/tasks"},{title:o((()=>[d(g,null,{default:o((()=>[d(r(J))])),_:1}),a[29]||(a[29]=u("span",null,"任务中心",-1))])),default:o((()=>[d(L,{index:"/main/tasks/simple-list"},{default:o((()=>[d(g,null,{default:o((()=>[d(r(A))])),_:1}),a[30]||(a[30]=u("span",null,"任务列表",-1))])),_:1}),d(L,{index:"/main/tasks/list"},{default:o((()=>[d(g,null,{default:o((()=>[d(r(J))])),_:1}),a[31]||(a[31]=u("span",null,"增强任务列表",-1))])),_:1}),d(L,{index:"/main/tasks/pdca-tracker"},{default:o((()=>[d(g,null,{default:o((()=>[d(r(ee))])),_:1}),a[32]||(a[32]=u("span",null,"PDCA跟踪",-1))])),_:1}),d(L,{index:"/main/tasks/periodic"},{default:o((()=>[d(g,null,{default:o((()=>[d(r(C))])),_:1}),a[33]||(a[33]=u("span",null,"周期性任务",-1))])),_:1}),d(L,{index:"/main/tasks/kanban"},{default:o((()=>[d(g,null,{default:o((()=>[d(r(q))])),_:1}),a[34]||(a[34]=u("span",null,"任务看板",-1))])),_:1}),ge(["admin","manager"])?(i(),y(L,{key:0,index:"/main/tasks/shift-management"},{default:o((()=>[d(g,null,{default:o((()=>[d(r(E))])),_:1}),a[35]||(a[35]=u("span",null,"班次管理",-1))])),_:1})):Z("",!0),d(L,{index:"/main/tasks/work-summary"},{default:o((()=>[d(g,null,{default:o((()=>[d(r(w))])),_:1}),a[36]||(a[36]=u("span",null,"工作汇总报告",-1))])),_:1})])),_:1}),d(L,{index:"/main/quickmemo"},{title:o((()=>a[37]||(a[37]=[b("随手记")]))),default:o((()=>[d(g,null,{default:o((()=>[d(r(ae))])),_:1})])),_:1}),d(L,{index:"/main/memo-list"},{title:o((()=>a[38]||(a[38]=[b("随手记列表")]))),default:o((()=>[d(g,null,{default:o((()=>[d(r(J))])),_:1})])),_:1}),d(S,{index:"/main/gamification"},{title:o((()=>[d(g,null,{default:o((()=>[d(r(te))])),_:1}),a[39]||(a[39]=u("span",null,"游戏化系统",-1))])),default:o((()=>[d(L,{index:"/main/gamification/overview"},{default:o((()=>[d(g,null,{default:o((()=>[d(r(q))])),_:1}),a[40]||(a[40]=u("span",null,"系统管理",-1))])),_:1}),d(L,{index:"/main/gamification/achievements"},{default:o((()=>[d(g,null,{default:o((()=>[d(r(le))])),_:1}),a[41]||(a[41]=u("span",null,"成就管理",-1))])),_:1}),d(L,{index:"/main/gamification/leaderboard"},{default:o((()=>[d(g,null,{default:o((()=>[d(r(te))])),_:1}),a[42]||(a[42]=u("span",null,"排行榜",-1))])),_:1})])),_:1}),d(L,{index:"/main/user/profile"},{title:o((()=>a[43]||(a[43]=[b("个人中心")]))),default:o((()=>[d(g,null,{default:o((()=>[d(r(M))])),_:1})])),_:1}),d(S,{index:"/main/system"},{title:o((()=>[d(g,null,{default:o((()=>[d(r(T))])),_:1}),a[44]||(a[44]=u("span",null,"系统管理",-1))])),default:o((()=>[d(L,{index:"/main/system/users"},{default:o((()=>a[45]||(a[45]=[b("用户管理")]))),_:1}),d(L,{index:"/main/system/roles"},{default:o((()=>a[46]||(a[46]=[b("角色管理")]))),_:1}),d(L,{index:"/main/system/menus"},{default:o((()=>a[47]||(a[47]=[b("菜单管理")]))),_:1}),d(L,{index:"/main/system/departments"},{default:o((()=>a[48]||(a[48]=[b("部门管理")]))),_:1}),d(L,{index:"/main/system/personnel"},{default:o((()=>a[49]||(a[49]=[b("人员管理")]))),_:1}),d(L,{index:"/main/system/logs"},{default:o((()=>a[50]||(a[50]=[b("审计日志")]))),_:1})])),_:1}),d(S,{index:"/test"},{title:o((()=>[d(g,null,{default:o((()=>[d(r(se))])),_:1}),a[51]||(a[51]=u("span",null,"功能测试",-1))])),default:o((()=>[d(L,{index:"/test/export"},{default:o((()=>[d(g,null,{default:o((()=>[d(r(x))])),_:1}),a[52]||(a[52]=u("span",null,"导出测试",-1))])),_:1})])),_:1})])),_:1},8,["default-active","collapse"])])),_:1})])),_:1},8,["width"]),d(pe,{class:"main-content"},{default:o((()=>[d(Ie),d(ce,null,{default:o((({Component:e})=>[d(ne,{name:"fade-transform",mode:"out-in"},{default:o((()=>[(i(),y(ie(e)))])),_:2},1024)])),_:1})])),_:1})])),_:1}),u("div",{class:_(["collapse-btn",{collapsed:V.value}]),onClick:a[1]||(a[1]=e=>V.value=!V.value)},[d(g,null,{default:o((()=>[V.value?(i(),y(r(ue),{key:0})):(i(),y(r(de),{key:1}))])),_:1})],2),d(Le,{title:"退出确认",modelValue:r(m).isLogoutDialogVisible,"onUpdate:modelValue":a[3]||(a[3]=e=>r(m).isLogoutDialogVisible=e),width:"380px"},{footer:o((()=>[d(t,{onClick:a[2]||(a[2]=e=>r(m).setLogoutDialogVisible(!1))},{default:o((()=>a[53]||(a[53]=[b("取消")]))),_:1}),d(t,{type:"primary",onClick:he},{default:o((()=>a[54]||(a[54]=[b("确定")]))),_:1})])),default:o((()=>[a[55]||(a[55]=u("span",null,"确定要退出登录吗？",-1))])),_:1},8,["modelValue"]),d(_e,{visible:be.value,"onUpdate:visible":a[4]||(a[4]=e=>be.value=e),mode:"drawer",onViewTask:Ve},null,8,["visible"]),d(Fe,{modelValue:fe.value,"onUpdate:modelValue":a[5]||(a[5]=e=>fe.value=e)},null,8,["modelValue"])])}}},[["__scopeId","data-v-9b9b73bd"]]);export{Oe as default};
