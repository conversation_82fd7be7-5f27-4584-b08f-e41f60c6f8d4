// File: Application/Features/SpareParts/Dtos/CreateSparePartLocationRequest.cs
// Description: 备品备件库位创建请求数据传输对象

using System.ComponentModel.DataAnnotations;

namespace ItAssetsSystem.Application.Features.SpareParts.Dtos
{
    /// <summary>
    /// 备品备件库位创建请求数据传输对象
    /// </summary>
    public class CreateSparePartLocationRequest
    {
        /// <summary>
        /// 库位编号
        /// </summary>
        [Required(ErrorMessage = "库位编号不能为空")]
        [StringLength(50, ErrorMessage = "库位编号长度不能超过50个字符")]
        public string Code { get; set; }
        
        /// <summary>
        /// 库位名称
        /// </summary>
        [Required(ErrorMessage = "库位名称不能为空")]
        [StringLength(100, ErrorMessage = "库位名称长度不能超过100个字符")]
        public string Name { get; set; }
        
        /// <summary>
        /// 区域标识
        /// </summary>
        [Required(ErrorMessage = "区域标识不能为空")]
        [StringLength(20, ErrorMessage = "区域标识长度不能超过20个字符")]
        public string Area { get; set; }
        
        /// <summary>
        /// 库位描述
        /// </summary>
        [StringLength(200, ErrorMessage = "库位描述长度不能超过200个字符")]
        public string Description { get; set; }
        
        /// <summary>
        /// 排序号
        /// </summary>
        public int SortOrder { get; set; }
    }
} 