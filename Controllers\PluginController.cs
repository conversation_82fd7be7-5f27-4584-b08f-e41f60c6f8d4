// IT资产管理系统 - 插件管理控制器
// 文件路径: /Controllers/PluginController.cs
// 功能: 提供插件管理的RESTful API接口

using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace ItAssetsSystem.Controllers
{
    /// <summary>
    /// 插件控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class PluginController : ControllerBase
    {
        private readonly ILogger<PluginController> _logger;
        private readonly ItAssetsSystem.Core.Abstractions.IPluginManager _pluginManager;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志记录器</param>
        /// <param name="pluginManager">插件管理器</param>
        public PluginController(ILogger<PluginController> logger, ItAssetsSystem.Core.Abstractions.IPluginManager pluginManager)
        {
            _logger = logger;
            _pluginManager = pluginManager;
        }

        /// <summary>
        /// 获取所有插件信息
        /// </summary>
        /// <returns>插件信息列表</returns>
        [HttpGet]
        public IActionResult GetAllPlugins()
        {
            try
            {
                var plugins = _pluginManager.GetAllPlugins();
                var result = plugins.Select(p => new
                {
                    id = p.Id,
                    name = p.Instance.Name,
                    description = p.Instance.Description,
                    version = p.Instance.Version.ToString(),
                    isRunning = p.Instance.IsRunning,
                    isBuiltIn = p.IsBuiltIn,
                    path = p.Path,
                    loadTime = p.LoadTime
                });

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取插件列表时发生异常");
                return StatusCode(500, "获取插件列表失败");
            }
        }

        /// <summary>
        /// 获取指定插件信息
        /// </summary>
        /// <param name="id">插件ID</param>
        /// <returns>插件信息</returns>
        [HttpGet("{id}")]
        public IActionResult GetPlugin(string id)
        {
            try
            {
                var plugin = _pluginManager.GetPlugin(id);
                if (plugin == null)
                {
                    return NotFound($"插件 {id} 不存在");
                }

                var result = new
                {
                    id = plugin.Id,
                    name = plugin.Instance.Name,
                    description = plugin.Instance.Description,
                    version = plugin.Instance.Version.ToString(),
                    isRunning = plugin.Instance.IsRunning,
                    isBuiltIn = plugin.IsBuiltIn,
                    path = plugin.Path,
                    loadTime = plugin.LoadTime
                };

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取插件 {id} 信息时发生异常");
                return StatusCode(500, $"获取插件 {id} 信息失败");
            }
        }

        /// <summary>
        /// 启动插件
        /// </summary>
        /// <param name="id">插件ID</param>
        /// <returns>操作结果</returns>
        [HttpPost("{id}/start")]
        public IActionResult StartPlugin(string id)
        {
            try
            {
                var plugin = _pluginManager.GetPlugin(id);
                if (plugin == null)
                {
                    return NotFound($"插件 {id} 不存在");
                }

                if (plugin.Instance.IsRunning)
                {
                    return Ok(new { success = true, message = $"插件 {id} 已经在运行中" });
                }

                bool success = _pluginManager.StartPlugin(id);
                if (success)
                {
                    return Ok(new { success = true, message = $"插件 {id} 启动成功" });
                }
                else
                {
                    return BadRequest(new { success = false, message = $"插件 {id} 启动失败" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"启动插件 {id} 时发生异常");
                return StatusCode(500, new { success = false, message = $"启动插件 {id} 失败: {ex.Message}" });
            }
        }

        /// <summary>
        /// 停止插件
        /// </summary>
        /// <param name="id">插件ID</param>
        /// <returns>操作结果</returns>
        [HttpPost("{id}/stop")]
        public IActionResult StopPlugin(string id)
        {
            try
            {
                var plugin = _pluginManager.GetPlugin(id);
                if (plugin == null)
                {
                    return NotFound($"插件 {id} 不存在");
                }

                if (!plugin.Instance.IsRunning)
                {
                    return Ok(new { success = true, message = $"插件 {id} 已经停止" });
                }

                bool success = _pluginManager.StopPlugin(id);
                if (success)
                {
                    return Ok(new { success = true, message = $"插件 {id} 停止成功" });
                }
                else
                {
                    return BadRequest(new { success = false, message = $"插件 {id} 停止失败" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"停止插件 {id} 时发生异常");
                return StatusCode(500, new { success = false, message = $"停止插件 {id} 失败: {ex.Message}" });
            }
        }

        /// <summary>
        /// 卸载插件
        /// </summary>
        /// <param name="id">插件ID</param>
        /// <returns>操作结果</returns>
        [HttpDelete("{id}")]
        public IActionResult UnloadPlugin(string id)
        {
            try
            {
                var plugin = _pluginManager.GetPlugin(id);
                if (plugin == null)
                {
                    return NotFound($"插件 {id} 不存在");
                }

                if (plugin.IsBuiltIn)
                {
                    return BadRequest(new { success = false, message = "内置插件不能卸载" });
                }

                bool success = _pluginManager.UnloadPlugin(id);
                if (success)
                {
                    return Ok(new { success = true, message = $"插件 {id} 卸载成功" });
                }
                else
                {
                    return BadRequest(new { success = false, message = $"插件 {id} 卸载失败" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"卸载插件 {id} 时发生异常");
                return StatusCode(500, new { success = false, message = $"卸载插件 {id} 失败: {ex.Message}" });
            }
        }

        /// <summary>
        /// 上传并安装插件
        /// </summary>
        /// <returns>操作结果</returns>
        [HttpPost("upload")]
        public async Task<IActionResult> UploadPlugin()
        {
            try
            {
                if (Request.Form.Files.Count == 0)
                {
                    return BadRequest(new { success = false, message = "未提供插件文件" });
                }

                var file = Request.Form.Files[0];
                if (file.Length == 0)
                {
                    return BadRequest(new { success = false, message = "文件为空" });
                }

                if (!Path.GetExtension(file.FileName).Equals(".dll", StringComparison.OrdinalIgnoreCase))
                {
                    return BadRequest(new { success = false, message = "只能上传DLL文件" });
                }

                // 创建保存插件的目录
                string pluginsDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Plugins");
                if (!Directory.Exists(pluginsDirectory))
                {
                    Directory.CreateDirectory(pluginsDirectory);
                }

                // 保存插件文件
                string filePath = Path.Combine(pluginsDirectory, file.FileName);
                using (var stream = new FileStream(filePath, FileMode.Create))
                {
                    await file.CopyToAsync(stream);
                }

                // 加载插件
                var pluginInfo = _pluginManager.LoadPlugin(filePath);
                if (pluginInfo == null)
                {
                    System.IO.File.Delete(filePath);
                    return BadRequest(new { success = false, message = "插件加载失败" });
                }

                return Ok(new
                {
                    success = true,
                    message = $"插件 {pluginInfo.Instance.Name} v{pluginInfo.Instance.Version} 安装成功",
                    plugin = new
                    {
                        id = pluginInfo.Id,
                        name = pluginInfo.Instance.Name,
                        description = pluginInfo.Instance.Description,
                        version = pluginInfo.Instance.Version.ToString()
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "上传插件时发生异常");
                return StatusCode(500, new { success = false, message = $"上传插件失败: {ex.Message}" });
            }
        }

        /// <summary>
        /// 启动所有插件
        /// </summary>
        /// <returns>操作结果</returns>
        [HttpPost("startAll")]
        public IActionResult StartAllPlugins()
        {
            try
            {
                _pluginManager.StartAllPlugins();
                return Ok(new { success = true, message = "所有插件启动成功" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "启动所有插件时发生异常");
                return StatusCode(500, new { success = false, message = $"启动所有插件失败: {ex.Message}" });
            }
        }

        /// <summary>
        /// 停止所有插件
        /// </summary>
        /// <returns>操作结果</returns>
        [HttpPost("stopAll")]
        public IActionResult StopAllPlugins()
        {
            try
            {
                _pluginManager.StopAllPlugins();
                return Ok(new { success = true, message = "所有插件停止成功" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "停止所有插件时发生异常");
                return StatusCode(500, new { success = false, message = $"停止所有插件失败: {ex.Message}" });
            }
        }
    }
}

// 计划行数: 256
// 实际行数: 256 