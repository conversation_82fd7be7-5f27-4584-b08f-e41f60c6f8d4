using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;

namespace ItAssetsSystem.Infrastructure.Data
{
    /// <summary>
    /// 游戏化表初始化器
    /// </summary>
    public class GamificationTablesInitializer
    {
        private readonly AppDbContext _context;
        private readonly ILogger<GamificationTablesInitializer> _logger;

        public GamificationTablesInitializer(AppDbContext context, ILogger<GamificationTablesInitializer> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// 初始化游戏化相关表
        /// </summary>
        public async Task InitializeAsync()
        {
            try
            {
                _logger.LogInformation("开始初始化游戏化相关表...");

                // 创建游戏化行为类型表
                await CreateGamificationBehaviorTypesTableAsync();

                // 创建其他游戏化表
                await CreateOtherGamificationTablesAsync();

                // 插入默认数据
                await InsertDefaultDataAsync();

                _logger.LogInformation("游戏化相关表初始化完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "初始化游戏化相关表时发生错误");
                throw;
            }
        }

        /// <summary>
        /// 创建游戏化行为类型表
        /// </summary>
        private async Task CreateGamificationBehaviorTypesTableAsync()
        {
            var sql = @"
                CREATE TABLE IF NOT EXISTS `gamification_behavior_types` (
                  `id` int NOT NULL AUTO_INCREMENT,
                  `code` varchar(50) NOT NULL COMMENT '行为代码（唯一标识）',
                  `name` varchar(100) NOT NULL COMMENT '行为名称',
                  `description` varchar(255) DEFAULT NULL COMMENT '行为描述',
                  `base_points` int NOT NULL DEFAULT '0' COMMENT '基础积分',
                  `base_coins` int NOT NULL DEFAULT '0' COMMENT '基础金币',
                  `base_diamonds` int NOT NULL DEFAULT '0' COMMENT '基础钻石',
                  `multiplier` decimal(5,2) NOT NULL DEFAULT '1.00' COMMENT '积分系数',
                  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用',
                  `category` varchar(20) NOT NULL DEFAULT '' COMMENT '行为分类',
                  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                  PRIMARY KEY (`id`),
                  UNIQUE KEY `uk_gamification_behavior_types_code` (`code`),
                  KEY `idx_gamification_behavior_types_category` (`category`),
                  KEY `idx_gamification_behavior_types_active` (`is_active`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='游戏化行为类型配置表';
            ";

            await _context.Database.ExecuteSqlRawAsync(sql);
            _logger.LogInformation("gamification_behavior_types表创建完成");
        }

        /// <summary>
        /// 创建其他游戏化表
        /// </summary>
        private async Task CreateOtherGamificationTablesAsync()
        {
            // 创建游戏化排行榜类型表
            var leaderboardTypesSql = @"
                CREATE TABLE IF NOT EXISTS `gamification_leaderboard_types` (
                  `id` int NOT NULL AUTO_INCREMENT,
                  `code` varchar(50) NOT NULL COMMENT '排行榜代码',
                  `name` varchar(100) NOT NULL COMMENT '排行榜名称',
                  `description` varchar(255) DEFAULT NULL COMMENT '排行榜描述',
                  `metric` varchar(50) NOT NULL COMMENT '统计指标',
                  `period` varchar(20) NOT NULL COMMENT '统计周期',
                  `sort_order` int NOT NULL DEFAULT '0' COMMENT '排序顺序',
                  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用',
                  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                  PRIMARY KEY (`id`),
                  UNIQUE KEY `uk_gamification_leaderboard_types_code` (`code`),
                  KEY `idx_gamification_leaderboard_types_metric` (`metric`),
                  KEY `idx_gamification_leaderboard_types_period` (`period`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='排行榜类型配置表';
            ";

            await _context.Database.ExecuteSqlRawAsync(leaderboardTypesSql);
            _logger.LogInformation("gamification_leaderboard_types表创建完成");

            // 先删除现有的user_work_summary表（如果存在）
            await _context.Database.ExecuteSqlRawAsync("DROP TABLE IF EXISTS `user_work_summary`");
            _logger.LogInformation("已删除现有的user_work_summary表");

            // 创建用户工作汇总表（与Entity类完全匹配）
            var userWorkSummarySql = @"
                CREATE TABLE `user_work_summary` (
                  `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
                  `user_id` INT NOT NULL COMMENT '用户ID',
                  `user_name` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '用户姓名',
                  `department_name` VARCHAR(50) COMMENT '部门名称',
                  `period_type` VARCHAR(10) NOT NULL COMMENT '统计周期类型(daily/weekly/monthly)',
                  `period_date` DATE NOT NULL COMMENT '统计周期起始日期',

                  -- 任务模块统计
                  `tasks_created` INT NOT NULL DEFAULT 0 COMMENT '新建任务数',
                  `tasks_claimed` INT NOT NULL DEFAULT 0 COMMENT '领取任务数',
                  `tasks_completed` INT NOT NULL DEFAULT 0 COMMENT '完成任务数',
                  `tasks_commented` INT NOT NULL DEFAULT 0 COMMENT '评论任务数',

                  -- 资产模块统计
                  `assets_created` INT NOT NULL DEFAULT 0 COMMENT '新建资产数',
                  `assets_updated` INT NOT NULL DEFAULT 0 COMMENT '更新资产数',
                  `assets_deleted` INT NOT NULL DEFAULT 0 COMMENT '删除资产数',

                  -- 故障模块统计
                  `faults_reported` INT NOT NULL DEFAULT 0 COMMENT '登记故障数',
                  `faults_repaired` INT NOT NULL DEFAULT 0 COMMENT '维修故障数',

                  -- 采购模块统计
                  `procurements_created` INT NOT NULL DEFAULT 0 COMMENT '新建采购数',
                  `procurements_updated` INT NOT NULL DEFAULT 0 COMMENT '更新采购数',

                  -- 备件模块统计
                  `parts_in` INT NOT NULL DEFAULT 0 COMMENT '备件入库数',
                  `parts_out` INT NOT NULL DEFAULT 0 COMMENT '备件出库数',
                  `parts_added` INT NOT NULL DEFAULT 0 COMMENT '新增备件数',

                  -- 游戏化收益统计
                  `total_points_earned` INT NOT NULL DEFAULT 0 COMMENT '获得积分总数',
                  `total_coins_earned` INT NOT NULL DEFAULT 0 COMMENT '获得金币总数',
                  `total_diamonds_earned` INT NOT NULL DEFAULT 0 COMMENT '获得钻石总数',
                  `total_xp_earned` INT NOT NULL DEFAULT 0 COMMENT '获得经验值总数',

                  -- 排名信息
                  `points_rank` INT NOT NULL DEFAULT 0 COMMENT '积分排名',
                  `productivity_rank` INT NOT NULL DEFAULT 0 COMMENT '生产力排名',
                  `productivity_score` DOUBLE NOT NULL DEFAULT 0.0 COMMENT '生产力得分',
                  `evaluation` VARCHAR(20) NOT NULL DEFAULT '' COMMENT '评价等级',

                  -- 时间戳
                  `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                  `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

                  -- 数据一致性字段
                  `last_reconciled_at` DATETIME NULL COMMENT '最后对账时间',
                  `version` INT NOT NULL DEFAULT 1 COMMENT '数据版本号（用于乐观锁）',

                  -- 索引
                  INDEX `ix_user_work_summary_user_id` (`user_id`),
                  INDEX `ix_user_work_summary_period` (`period_type`, `period_date`),
                  INDEX `ix_user_work_summary_productivity_rank` (`productivity_rank`),
                  INDEX `ix_user_work_summary_points_rank` (`points_rank`),
                  UNIQUE INDEX `ix_user_work_summary_unique` (`user_id`, `period_type`, `period_date`),

                  -- 外键约束
                  FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户工作汇总统计表';
            ";

            await _context.Database.ExecuteSqlRawAsync(userWorkSummarySql);
            _logger.LogInformation("user_work_summary表创建完成");
        }

        /// <summary>
        /// 插入默认数据
        /// </summary>
        private async Task InsertDefaultDataAsync()
        {
            // 插入默认行为类型配置
            var behaviorTypesSql = @"
                INSERT IGNORE INTO `gamification_behavior_types` (`code`, `name`, `description`, `base_points`, `base_coins`, `base_diamonds`, `category`) VALUES
                -- 任务相关
                ('TASK_CREATED', '任务创建', '创建新任务获得奖励', 20, 10, 0, 'TASK'),
                ('TASK_COMPLETED', '任务完成', '完成任务获得奖励', 50, 25, 1, 'TASK'),
                ('TASK_CLAIMED', '任务领取', '领取任务获得奖励', 10, 5, 0, 'TASK'),
                ('TASK_DELETED', '任务删除', '删除任务扣除奖励（负分）', -20, -10, 0, 'TASK'),
                
                -- 资产相关
                ('ASSET_CREATED', '资产创建', '创建新资产获得奖励', 30, 15, 0, 'ASSET'),
                ('ASSET_UPDATED', '资产更新', '更新资产信息获得奖励', 15, 8, 0, 'ASSET'),
                ('ASSET_TRANSFERRED', '资产转移', '资产转移操作获得奖励', 20, 10, 0, 'ASSET'),
                
                -- 故障相关
                ('FAULT_RECORDED', '故障登记', '登记故障获得奖励', 25, 12, 0, 'FAULT'),
                ('FAULT_RESOLVED', '故障解决', '解决故障获得奖励', 40, 20, 1, 'FAULT'),
                
                -- 维修相关
                ('MAINTENANCE_CREATED', '维修创建', '创建维修工单获得奖励', 20, 10, 0, 'MAINTENANCE'),
                ('MAINTENANCE_COMPLETED', '维修完成', '完成维修工单获得奖励', 35, 18, 1, 'MAINTENANCE'),

                -- 备件相关
                ('SPAREPART_INBOUND', '备件入库', '备件入库操作获得奖励', 15, 8, 0, 'SPAREPART'),
                ('SPAREPART_OUTBOUND', '备件出库', '备件出库操作获得奖励', 12, 6, 0, 'SPAREPART'),
                ('SPAREPART_UPDATED', '备件更新', '更新备件信息获得奖励', 10, 5, 0, 'SPAREPART'),

                -- 采购相关
                ('PURCHASE_CREATED', '采购创建', '创建采购订单获得奖励', 25, 12, 0, 'PURCHASE'),
                ('PURCHASE_UPDATED', '采购更新', '更新采购订单获得奖励', 15, 8, 0, 'PURCHASE'),

                -- 评论相关
                ('COMMENT_ADDED', '添加评论', '添加评论获得奖励', 5, 2, 0, 'SOCIAL'),
                ('COMMENT_LIKED', '评论点赞', '评论被点赞获得奖励', 2, 1, 0, 'SOCIAL'),
                
                -- 登录相关
                ('DAILY_LOGIN', '每日登录', '每日首次登录获得奖励', 5, 2, 0, 'ACTIVITY'),
                ('WEEKLY_LOGIN', '每周登录', '每周连续登录获得奖励', 20, 10, 0, 'ACTIVITY');
            ";

            await _context.Database.ExecuteSqlRawAsync(behaviorTypesSql);
            _logger.LogInformation("默认行为类型数据插入完成");

            // 插入默认排行榜类型
            var leaderboardTypesSql = @"
                INSERT IGNORE INTO `gamification_leaderboard_types` (`code`, `name`, `metric`, `period`, `sort_order`) VALUES
                ('POINTS_DAILY', '每日积分排行', 'points', 'daily', 1),
                ('POINTS_WEEKLY', '每周积分排行', 'points', 'weekly', 2),
                ('POINTS_MONTHLY', '每月积分排行', 'points', 'monthly', 3),
                ('POINTS_ALLTIME', '总积分排行', 'points', 'alltime', 4),
                ('TASKS_COMPLETED_WEEKLY', '每周完成任务排行', 'tasks_completed', 'weekly', 5),
                ('TASKS_COMPLETED_MONTHLY', '每月完成任务排行', 'tasks_completed', 'monthly', 6),
                ('TASKS_CREATED_MONTHLY', '每月创建任务排行', 'tasks_created', 'monthly', 7),
                ('FAULTS_RECORDED_MONTHLY', '每月故障登记排行', 'faults_recorded', 'monthly', 8);
            ";

            await _context.Database.ExecuteSqlRawAsync(leaderboardTypesSql);
            _logger.LogInformation("默认排行榜类型数据插入完成");

            // 插入用户工作汇总测试数据（使用本周周一的日期）
            var userWorkSummaryTestDataSql = @"
                INSERT IGNORE INTO `user_work_summary`
                (`user_id`, `user_name`, `department_name`, `period_type`, `period_date`,
                 `tasks_created`, `tasks_completed`, `tasks_claimed`, `assets_created`, `faults_reported`,
                 `total_points_earned`, `total_coins_earned`, `total_diamonds_earned`,
                 `productivity_score`, `productivity_rank`, `evaluation`)
                VALUES
                (6, 'TestUser6', 'IT部门', 'weekly', DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY), 5, 8, 10, 3, 2, 150, 75, 3, 85.50, 1, '优秀'),
                (1, 'TestUser1', 'IT部门', 'weekly', DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY), 3, 6, 8, 2, 1, 120, 60, 2, 78.20, 2, '良好'),
                (2, 'TestUser2', '运维部门', 'weekly', DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY), 4, 5, 7, 1, 3, 110, 55, 1, 72.30, 3, '良好'),
                (3, 'TestUser3', '运维部门', 'weekly', DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY), 2, 4, 6, 2, 1, 95, 48, 1, 65.80, 4, '一般'),
                (4, 'TestUser4', '维修部门', 'weekly', DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY), 1, 3, 5, 1, 2, 80, 40, 0, 58.90, 5, '一般');
            ";

            await _context.Database.ExecuteSqlRawAsync(userWorkSummaryTestDataSql);
            _logger.LogInformation("用户工作汇总测试数据插入完成");
        }
    }
}
