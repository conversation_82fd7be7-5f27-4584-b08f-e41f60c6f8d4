// IT资产管理系统 - 插件基类
// 文件路径: /Core/Abstractions/PluginBase.cs
// 功能: 为插件提供通用实现，简化插件开发

using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System;

namespace ItAssetsSystem.Core.Abstractions
{
    /// <summary>
    /// 插件基类，提供通用实现
    /// </summary>
    public abstract class PluginBase : IPlugin
    {
        protected ILogger Logger { get; private set; }
        protected IServiceProvider ServiceProvider { get; private set; }

        /// <inheritdoc/>
        public abstract string Name { get; }

        /// <inheritdoc/>
        public abstract string Description { get; }

        /// <inheritdoc/>
        public abstract Version Version { get; }

        /// <inheritdoc/>
        public bool IsRunning { get; private set; }

        /// <inheritdoc/>
        public abstract void RegisterServices(IServiceCollection services);

        /// <inheritdoc/>
        public virtual void Start(IServiceProvider serviceProvider)
        {
            if (IsRunning)
                return;

            ServiceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
            
            // 从服务提供程序获取日志服务
            var loggerFactory = ServiceProvider.GetRequiredService<ILoggerFactory>();
            Logger = loggerFactory.CreateLogger(GetType());

            OnStart();
            IsRunning = true;
            Logger.LogInformation($"插件 {Name} v{Version} 已启动");
        }

        /// <inheritdoc/>
        public virtual void Stop()
        {
            if (!IsRunning)
                return;

            OnStop();
            IsRunning = false;
            Logger?.LogInformation($"插件 {Name} 已停止");
        }

        /// <summary>
        /// 在插件启动时执行的操作
        /// </summary>
        protected virtual void OnStart() { }

        /// <summary>
        /// 在插件停止时执行的操作
        /// </summary>
        protected virtual void OnStop() { }
    }
}

// 计划行数: 25
// 实际行数: 25 