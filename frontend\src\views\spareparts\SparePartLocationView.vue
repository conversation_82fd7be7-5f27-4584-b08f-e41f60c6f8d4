<template>
  <div class="spare-part-location-view">
    <el-card shadow="hover">
      <template #header>
        <div class="card-header">
          <span>备件库位管理</span>
          <el-button type="primary" @click="handleAddLocation">
            新增库位
          </el-button>
        </div>
      </template>

      <!-- 库位列表 -->
      <el-table 
        v-loading="loading" 
        :data="locations" 
        border 
        style="width: 100%"
        row-key="id"
      >
        <el-table-column prop="code" label="库位编码" width="150"></el-table-column>
        <el-table-column prop="name" label="库位名称" width="200"></el-table-column>
        <el-table-column prop="area" label="区域" width="150"></el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.status === 'normal' ? 'success' : 'warning'">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="描述"></el-table-column>
        <el-table-column label="操作" width="200" align="center">
          <template #default="scope">
            <el-button 
              size="small" 
              type="primary" 
              @click="handleEdit(scope.row)"
              :icon="Edit"
              circle
            ></el-button>
            <el-button 
              size="small" 
              type="danger" 
              @click="handleDelete(scope.row)" 
              :icon="Delete"
              circle
            ></el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    
    <!-- 新增/编辑库位对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑库位' : '新增库位'"
      width="500px"
      :close-on-click-modal="false"
      @closed="resetForm"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
        label-position="right"
        status-icon
      >
        <el-form-item label="库位编码" prop="code">
          <el-input v-model="form.code" placeholder="请输入库位编码"></el-input>
        </el-form-item>
        
        <el-form-item label="库位名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入库位名称"></el-input>
        </el-form-item>
        
        <el-form-item label="所属区域" prop="area">
          <el-input v-model="form.area" placeholder="请输入所属区域"></el-input>
        </el-form-item>
        
        <el-form-item label="状态" prop="status">
          <el-select v-model="form.status" placeholder="请选择库位状态" style="width: 100%">
            <el-option label="正常" value="normal"></el-option>
            <el-option label="维护中" value="maintenance"></el-option>
            <el-option label="已停用" value="disabled"></el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="form.description"
            placeholder="请输入描述信息"
            type="textarea"
            :rows="3"
          ></el-input>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm" :loading="loading">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { Edit, Delete } from '@element-plus/icons-vue';
import { useSparePartsStore } from '@/stores/modules/spareparts';

// 状态管理
const sparePartsStore = useSparePartsStore();

// 状态变量
const loading = ref(false);
const dialogVisible = ref(false);
const isEdit = ref(false);
const locations = ref([]);
const formRef = ref(null);

// 表单数据
const form = ref({
  code: '',
  name: '',
  area: '',
  status: 'normal',
  description: ''
});

// 表单验证规则
const rules = {
  code: [
    { required: true, message: '请输入库位编码', trigger: 'blur' },
    { min: 1, max: 20, message: '长度在 1 到 20 个字符', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入库位名称', trigger: 'blur' },
    { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
  ],
  area: [
    { required: true, message: '请输入所属区域', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择库位状态', trigger: 'change' }
  ]
};

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    normal: '正常',
    maintenance: '维护中',
    disabled: '已停用'
  };
  return statusMap[status] || status;
};

// 加载库位数据
const loadLocations = async () => {
  try {
    loading.value = true;
    locations.value = await sparePartsStore.fetchLocations() || [];
  } catch (error) {
    console.error('获取库位数据失败:', error);
    ElMessage.error('获取库位数据失败');
  } finally {
    loading.value = false;
  }
};

// 新增库位
const handleAddLocation = () => {
  isEdit.value = false;
  dialogVisible.value = true;
};

// 编辑库位
const handleEdit = (row) => {
  isEdit.value = true;
  form.value = { ...row };
  dialogVisible.value = true;
};

// 删除库位
const handleDelete = (row) => {
  ElMessageBox.confirm(
    `确定要删除库位"${row.name}"吗？`,
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      loading.value = true;
      const result = await sparePartsStore.deleteLocation(row.id);
      if (result) {
        ElMessage.success('删除成功');
        await loadLocations();
      }
    } catch (error) {
      console.error('删除库位失败:', error);
      ElMessage.error('删除库位失败');
    } finally {
      loading.value = false;
    }
  }).catch(() => {
    // 用户取消删除
  });
};

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return;
  
  try {
    await formRef.value.validate();
    loading.value = true;
    
    if (isEdit.value) {
      // 编辑库位
      const { id, ...updateData } = form.value;
      const result = await sparePartsStore.updateLocation(id, updateData);
      if (result) {
        ElMessage.success('更新成功');
        dialogVisible.value = false;
        await loadLocations();
      }
    } else {
      // 新增库位
      const result = await sparePartsStore.createLocation(form.value);
      if (result) {
        ElMessage.success('创建成功');
        dialogVisible.value = false;
        await loadLocations();
      }
    }
  } catch (error) {
    console.error('表单验证失败或提交失败:', error);
    if (error.message) {
      ElMessage.error(error.message);
    }
  } finally {
    loading.value = false;
  }
};

// 重置表单
const resetForm = () => {
  form.value = {
    code: '',
    name: '',
    area: '',
    status: 'normal',
    description: ''
  };
  
  formRef.value && formRef.value.resetFields();
};

// 组件挂载时加载数据
onMounted(async () => {
  await loadLocations();
});
</script>

<style scoped>
.spare-part-location-view {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style> 