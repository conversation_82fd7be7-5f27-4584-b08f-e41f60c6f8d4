<template>
  <div class="dashboard">
    <div class="page-header">欢迎回来, {{ user.name }}! <el-tag type="warning" effect="light">今天也要元气满满！</el-tag></div>

    <!-- 任务统计卡片 -->
    <div class="card-container">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card shadow="hover" class="stat-card">
            <div class="stat-value">{{ taskStats.pending }}</div>
            <div class="stat-label">待办任务</div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover" class="stat-card">
            <div class="stat-value">{{ taskStats.inProgress }}</div>
            <div class="stat-label">进行中</div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover" class="stat-card">
            <div class="stat-value">{{ taskStats.review }}</div>
            <div class="stat-label">待审核</div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover" class="stat-card">
            <div class="stat-value">{{ taskStats.completed }}</div>
            <div class="stat-label">已完成</div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 主要内容区域 -->
    <el-row :gutter="20">
      <el-col :span="16">
        <!-- 快速访问 -->
        <el-card shadow="hover" class="dashboard-card">
          <template #header>
            <div class="card-header">
              <span>快速访问</span>
            </div>
          </template>
          <div class="quick-actions">
            <el-button type="primary" @click="createTask">创建新任务</el-button>
            <el-button @click="viewTasks">查看我的任务</el-button>
            <el-button @click="viewKanban">打开任务看板</el-button>
          </div>
        </el-card>

        <!-- 即将到期的任务 -->
        <el-card shadow="hover" class="dashboard-card">
          <template #header>
            <div class="card-header">
              <span>即将到期的任务</span>
            </div>
          </template>
          <el-table :data="upcomingTasks" style="width: 100%">
            <el-table-column prop="title" label="任务名称" />
            <el-table-column prop="dueDate" label="截止时间" width="180" />
            <el-table-column label="操作" width="120">
              <template #default="scope">
                <el-button type="primary" link @click="viewTask(scope.row.id)">查看</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>

      <el-col :span="8">
        <!-- 用户状态 -->
        <el-card shadow="hover" class="dashboard-card">
          <template #header>
            <div class="card-header">
              <span>我的状态</span>
            </div>
          </template>
          <div class="user-status">
            <div class="score-display">积分: <span class="score">{{ user.score }}</span></div>
            <div class="level-progress">
              <span>等级: {{ userLevel.name }}</span>
              <el-progress :percentage="userLevel.progress" :stroke-width="10" status="success">
                <span>{{ user.score }} / {{ userLevel.nextLevelScore }}</span>
              </el-progress>
            </div>
            <el-button type="warning" size="small" :disabled="user.checkedInToday" @click="dailyCheckIn">
              {{ user.checkedInToday ? '今日已签到' : '每日签到 (+2积分)' }}
            </el-button>
          </div>
        </el-card>

        <!-- 最新成就 -->
        <el-card shadow="hover" class="dashboard-card">
          <template #header>
            <div class="card-header">
              <span>最新成就</span>
            </div>
          </template>
          <div v-if="latestAchievement" class="achievement">
            <el-avatar :size="60" :src="latestAchievement.icon" />
            <div class="achievement-info">
              <div class="achievement-name">{{ latestAchievement.name }}</div>
              <div class="achievement-time">{{ formatTimeAgo(latestAchievement.unlockTime) }} 解锁</div>
            </div>
          </div>
          <el-empty v-else description="继续努力，解锁新成就！"></el-empty>
        </el-card>
      </el-col>
    </el-row>

    <!-- 新增：思维球和随手记列表区域 -->
    <el-row :gutter="20" class="dashboard-row">
      <el-col :span="24">
        <el-card shadow="hover" class="dashboard-card quickmemo-board-card">
          <template #header>
            <div class="card-header">
              <span><el-icon style="margin-right: 8px;"><CollectionTag /></el-icon>随手记面板</span>
              <el-button @click="clearAllMemos" type="danger" link :icon="Delete">清空面板</el-button>
            </div>
          </template>
          <QuickMemoBoard />
        </el-card>
      </el-col>
    </el-row>

  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { useRouter } from 'vue-router'
import { Opportunity, Tickets, CollectionTag, Delete } from '@element-plus/icons-vue'
import { useQuickMemoStore } from '@/stores/modules/quickMemo'
import { ElMessageBox, ElMessage } from 'element-plus'

// 新增：导入随手记相关组件
// import ThinkingSphereCSS3D from '@/components/QuickMemo/ThinkingSphereCSS3D.vue'
// import QuickMemoList from '@/components/QuickMemo/QuickMemoList.vue'
import QuickMemoBoard from '@/components/QuickMemo/QuickMemoBoard.vue'

const router = useRouter()
const quickMemoStore = useQuickMemoStore()

// 用户信息
const user = reactive({
  id: 'user1',
  name: '张三',
  avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
  department: '研发部',
  position: '前端开发工程师',
  level: 5,
  score: 320,
  checkedInToday: false
})

// 用户等级
const userLevel = computed(() => {
  return {
    name: 'Level ' + user.level,
    progress: 50, // 这里应该根据用户积分计算当前等级的进度
    nextLevelScore: user.score + 100
  }
})

// 任务统计
const taskStats = reactive({
  pending: 8,
  inProgress: 15,
  review: 3,
  completed: 42
})

// 即将到期的任务
const upcomingTasks = reactive([
  { id: 't1', title: '优化用户注册流程', dueDate: '2024-05-10' },
  { id: 't2', title: '修复移动端显示问题', dueDate: '2024-05-12' },
  { id: 't3', title: '完成系统架构文档', dueDate: '2024-05-15' }
])

// 最新成就
const latestAchievement = ref({
  id: 'ach1',
  name: '高效能',
  description: '连续7天完成任务',
  icon: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
  unlockTime: new Date(2024, 5, 1)
})

// 方法
function createTask() {
  console.log('打开创建任务对话框')
}

function viewTasks() {
  router.push({ name: 'tasks' })
}

function viewKanban() {
  router.push({ name: 'kanban' })
}

function viewTask(taskId: string) {
  router.push({ name: 'taskDetail', params: { id: taskId } })
}

function dailyCheckIn() {
  user.checkedInToday = true
  user.score += 2
  alert('签到成功，+2积分！')
}

function formatTimeAgo(date: Date): string {
  const now = new Date()
  const diffInDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24))
  
  if (diffInDays === 0) {
    return '今天'
  } else if (diffInDays === 1) {
    return '昨天'
  } else if (diffInDays < 7) {
    return `${diffInDays}天前`
  } else {
    return date.toLocaleDateString()
  }
}

// Method to clear all memos
const clearAllMemos = () => {
  ElMessageBox.confirm(
    '确定要清空面板上所有的随手记吗？此操作不可恢复。',
    '警告',
    {
      confirmButtonText: '全部删除',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(async () => {
      // Iterate over a copy for safe deletion from the reactive array
      for (const memo of [...quickMemoStore.quickMemos]) { 
        if (!memo.id.toString().startsWith('temp-')) { // Only delete saved memos via API
            await quickMemoStore.removeQuickMemo(memo.id);
        } else {
            quickMemoStore.removeMemoPlaceholder(memo.id); // Remove local placeholders
        }
      }
      // If any temp memos were missed, this ensures the array is clean
      quickMemoStore.quickMemos = quickMemoStore.quickMemos.filter(m => m.id.toString().startsWith('temp-') && false); 
      ElMessage.success('所有随手记已清空');
    }).catch(() => {
      ElMessage.info('操作已取消');
    });
};

</script>

<style scoped>
.page-header {
  font-size: 20px;
  font-weight: 500;
  margin-bottom: 20px;
}

.card-container {
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
  transition: transform 0.3s;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: var(--app-primary-color);
}

.stat-label {
  color: rgba(26, 86, 219, 0.65);
  margin-top: 5px;
}

.dashboard-card {
  margin-bottom: 20px;
  border-radius: var(--el-border-radius-large, 10px);
  border: 1px solid var(--border-color-light, #ECEFF1);
  background-color: var(--card-bg, #FFFFFF);
  box-shadow: var(--shadow-light);
  transition: box-shadow 0.3s ease;
}

.dashboard-card:hover {
  box-shadow: var(--shadow-medium);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: var(--text-color);
  span {
    display: flex;
    align-items: center;
  }
}

.quick-actions {
  display: flex;
  gap: 10px;
}

.user-status {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.score-display {
  font-size: 16px;
}

.score {
  color: var(--game-points);
  font-size: 20px;
  font-weight: bold;
}

.level-progress {
  margin: 10px 0;
}

.level-progress :deep(.el-progress-bar__inner) {
  background-color: var(--game-points) !important;
}

.achievement {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 10px;
  background-color: rgba(214, 158, 46, 0.1);
  border-radius: 8px;
}

.achievement-info {
  flex: 1;
}

.achievement-name {
  font-weight: 500;
  margin-bottom: 5px;
  color: var(--app-accent-color);
}

.achievement-time {
  font-size: 12px;
  color: rgba(63, 131, 248, 0.8);
}

:deep(.el-button--primary:hover) {
  background-color: var(--app-secondary-color);
  border-color: var(--app-secondary-color);
}

:deep(.el-button--primary.is-link) {
  color: var(--app-primary-color);
}

:deep(.el-button--primary.is-link:hover) {
  color: var(--app-secondary-color);
}

.dashboard-row {
    margin-top: 20px;
}

.quickmemo-board-card :deep(.el-card__body) {
  padding: 0;
  height: calc(100vh - 300px);
  min-height: 400px;
}
.dashboard-row .quickmemo-board-card {
  min-height: 500px;
}
</style> 