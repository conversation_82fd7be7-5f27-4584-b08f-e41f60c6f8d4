{"server": {"ip": "127.0.0.1", "http_port": 5001, "signalr_hub": "/hubs/notification", "api_base_url": "http://localhost:5001/api", "api_version": "v2", "enable_udp_server": false, "enable_websocket_server": false}, "auth": {"username": "", "password": "", "auto_login": true, "token_refresh_threshold": 300, "support_anonymous": true}, "network": {"enable_udp": false, "enable_websocket": false, "enable_signalr": true, "signalr_timeout_ms": 5000, "http_timeout_ms": 10000, "max_retry_count": 3, "retry_interval_ms": 1000, "heartbeat_interval_ms": 30000}, "performance": {"buffer_size": 65536, "max_queue_size": 1000, "thread_pool_size": 4, "enable_zero_copy": true, "enable_memory_pool": true, "batch_send_threshold": 10, "batch_send_timeout_ms": 100}, "ui": {"enable_tray": true, "enable_balloon_tips": true, "enable_sound": false, "auto_hide_timeout_ms": 5000, "max_notifications": 100, "notification_fade_time_ms": 300}, "logging": {"level": "INFO", "enable_file_logging": true, "log_file_path": "logs/client.log", "max_log_file_size": 10485760, "max_log_files": 5, "enable_console_logging": true}, "gamification": {"enable_reward_notifications": true, "enable_leaderboard_updates": true, "enable_achievement_notifications": true, "reward_display_duration_ms": 3000, "achievement_display_duration_ms": 5000}, "task_management": {"enable_task_notifications": true, "enable_claim_notifications": true, "enable_completion_notifications": true, "enable_reminder_notifications": true, "reminder_advance_minutes": 15}, "filters": {"priority_levels": ["LOW", "NORMAL", "HIGH", "CRITICAL"], "message_types": ["GAMIFICATION_REWARD", "TASK_UPDATE", "SYSTEM_ALERT", "LEADERBOARD_UPDATE"], "blocked_users": [], "blocked_departments": []}, "advanced": {"enable_debug_mode": false, "enable_performance_monitoring": true, "enable_network_diagnostics": false, "enable_message_encryption": false, "compression_threshold": 1024, "enable_statistics": true, "statistics_interval_ms": 60000}}