/**
 * 航空航天级IT资产管理系统 - 路由配置定义
 * 文件路径: src/router/routes.js
 * 功能描述: 定义系统路由结构，包括懒加载和路由元数据
 */

// 布局组件
const DefaultLayout = () => import('@/layouts/DefaultLayout.vue')
const BasicLayout = () => import('@/layouts/BasicLayout.vue')
import MainLayout from '@/layouts/DefaultLayout.vue';
import RouterViewLayout from '@/views/layout/RouterViewLayout.vue'; // 假设已存在或创建此布局组件

// 路由配置
const routes = [
  // 根路径重定向到主页
  {
    path: '/',
    redirect: '/main/dashboard'
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/auth/Login.vue'),
    meta: {
      title: '登录',
      hidden: true,
      noAuth: true
    }
  },
  // 直接访问的仪表盘路由（不使用任何布局）
  {
    path: '/dashboard-simple',
    name: 'DashboardSimple',
    component: () => import('@/views/dashboard/index.vue'),
    meta: {
      title: '简易仪表盘',
      noAuth: true
    }
  },
  // 独立工厂监控看板
  {
    path: '/factory-monitor',
    name: 'FactoryMonitor',
    component: () => import('@/views/dashboard/FactoryLayoutDashboardSimple.vue'),
    meta: {
      title: '智能制造监控系统',
      noAuth: false
    }
  },
  // 标准化游戏化系统
  {
    path: '/standardized-gamification',
    name: 'StandardizedGamification',
    component: () => import('@/views/gamification/StandardizedGamificationDashboard.vue'),
    meta: {
      title: '标准化游戏化系统',
      noAuth: false
    }
  },
  // 完整版工厂监控看板
  {
    path: '/factory-monitor-full',
    name: 'FactoryMonitorFull',
    component: () => import('@/views/dashboard/FactoryLayoutDashboard.vue'),
    meta: {
      title: '智能制造监控系统(完整版)',
      noAuth: false
    }
  },
  // 测试版工厂监控看板
  {
    path: '/factory-monitor-test',
    name: 'FactoryMonitorTest',
    component: () => import('@/views/dashboard/FactoryLayoutDashboardTest.vue'),
    meta: {
      title: '智能制造监控系统(测试版)',
      noAuth: true
    }
  },
  // V2版本工厂监控看板 - 基于ef7aee39版本
  {
    path: '/factory-monitor-v2',
    name: 'FactoryMonitorV2',
    component: () => import('@/views/dashboard/FactoryLayoutDashboardV2.vue'),
    meta: {
      title: '智能制造监控系统 V2.0',
      noAuth: false
    }
  },
  // 全屏工厂监控看板 - 简洁方块式布局
  {
    path: '/factory-monitor-full-simple',
    name: 'FactoryMonitorFullSimple',
    component: () => import('@/views/dashboard/FactoryMonitorFull.vue'),
    meta: {
      title: '工厂监控全屏版',
      noAuth: false
    }
  },
  // 工厂布局设计器
  {
    path: '/layout-designer',
    name: 'LayoutDesigner',
    component: () => import('@/views/LayoutDesigner.vue'),
    meta: {
      title: '工厂布局设计器',
      noAuth: false
    }
  },
  // 使用简单布局的仪表盘
  {
    path: '/dashboard-basic',
    component: BasicLayout,
    children: [
      {
        path: '',
        name: 'DashboardBasic',
        component: () => import('@/views/dashboard/index.vue'),
        meta: {
          title: '基础布局仪表盘',
          noAuth: true
        }
      }
    ]
  },
  // 主应用布局
  {
    path: '/main',
    component: DefaultLayout,
    redirect: '/main/dashboard/overview',
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: RouterViewLayout,
        meta: {
          title: '仪表盘',
          icon: 'Odometer',
          affix: true,
          keepAlive: true
        },
        redirect: '/main/dashboard/overview',
        children: [
          {
            path: 'overview',
            name: 'DashboardOverview',
            component: () => import('@/views/dashboard/index.vue'),
            meta: {
              title: '概览',
              icon: 'DataLine',
              keepAlive: true
            }
          },
          {
            path: 'factory',
            name: 'FactoryDashboard',
            component: () => import('@/views/dashboard/FactoryLayoutDashboard.vue'),
            meta: {
              title: '工厂监控',
              icon: 'Monitor',
              keepAlive: true
            }
          },
          {
            path: 'layout-designer',
            name: 'LayoutDesignerMain',
            component: () => import('@/views/LayoutDesigner.vue'),
            meta: {
              title: '布局设计器',
              icon: 'Grid',
              keepAlive: true
            }
          }
        ]
      },
      // 资产管理路由
      {
        path: 'asset',
        name: 'Asset',
        component: () => import('@/views/asset/index.vue'),
        meta: {
          title: '资产管理',
          icon: 'Monitor',
          permission: 'asset:view'
        },
        redirect: '/main/asset/list',
        children: [
          {
            path: 'list',
            name: 'AssetList',
            component: () => import('@/views/asset/list.vue'),
            meta: {
              title: '资产列表',
              icon: 'List',
              keepAlive: true,
              permission: 'asset:list'
            }
          },
          {
            path: 'type',
            name: 'AssetType',
            component: () => import('@/views/asset/type.vue'),
            meta: {
              title: '资产类型',
              icon: 'SetUp',
              keepAlive: true,
              permission: 'asset:type'
            }
          },
          {
            path: 'statistics',
            name: 'AssetStatistics',
            component: () => import('@/views/asset/AssetStatisticsView.vue'),
            meta: {
              title: '统计分析',
              icon: 'DataAnalysis',
              keepAlive: true,
              permission: 'asset:statistics'
            }
          },
          {
            path: 'statistics-debug',
            name: 'AssetStatisticsDebug',
            component: () => import('@/views/asset/AssetStatisticsDebug.vue'),
            meta: {
              title: '统计调试',
              icon: 'Bug',
              keepAlive: true,
              hidden: true // 隐藏在菜单中
            }
          },
          {
            path: 'analytics-workbench',
            name: 'AssetAnalyticsWorkbench',
            component: () => import('@/views/asset/AssetAnalyticsWorkbench.vue'),
            meta: {
              title: '智能分析工作台',
              icon: 'TrendCharts',
              keepAlive: true,
              permission: 'asset:analytics'
            }
          }
        ]
      },
      // 位置管理路由
      {
        path: 'locations',
        name: 'Locations',
        component: () => import('@/views/locations/index.vue'),
        meta: {
          title: '位置管理',
          icon: 'LocationInformation'
        },
        redirect: '/main/locations/structure',
        children: [
          {
            path: 'structure',
            name: 'LocationStructure',
            component: () => import('@/views/locations/structure.vue'),
            meta: {
              title: '位置结构',
              icon: 'Tree',
              keepAlive: true
            }
          },
          {
            path: 'relations',
            name: 'LocationRelations',
            component: () => import('@/views/locations/relations.vue'),
            meta: {
              title: '位置关联',
              icon: 'Connection',
              keepAlive: true
            }
          }
        ]
      },
      // 故障管理路由
      {
        path: 'faults',
        name: 'Faults',
        component: () => import('@/views/faults/index.vue'),
        meta: {
          title: '故障管理',
          icon: 'Warning'
        },
        redirect: '/main/faults/list',
        children: [
          {
            path: 'list',
            name: 'FaultList',
            component: () => import('@/views/faults/list.vue'),
            meta: {
              title: '故障列表',
              keepAlive: true
            }
          },
          {
            path: 'maintenance',
            name: 'Maintenance',
            component: () => import('@/views/faults/maintenance.vue'),
            meta: {
              title: '返厂/维修',
              keepAlive: true
            }
          }
        ]
      },
      // 采购管理路由
      {
        path: 'purchases',
        name: 'Purchases',
        component: () => import('@/views/purchases/index.vue'),
        meta: {
          title: '采购管理',
          icon: 'ShoppingCart'
        },
        redirect: '/main/purchases/list',
        children: [
          {
            path: 'list',
            name: 'PurchaseList',
            component: () => import('@/views/purchases/list.vue'),
            meta: {
              title: '采购列表',
              keepAlive: true
            }
          }
        ]
      },
      // 现代化任务管理路由
      {
        path: 'tasks',
        name: 'Tasks',
        component: RouterViewLayout,
        meta: {
          title: '任务中心',
          icon: 'Tickets',
          requiresAuth: true,
          roles: ['admin', 'user', 'manager']
        },
        redirect: '/main/tasks/kanban',
        children: [
          // 现代化看板视图 - 默认主页
          {
            path: 'kanban',
            name: 'ModernTaskKanban',
            component: () => import('@/views/tasks/ModernKanbanView.vue'),
            meta: {
              title: '任务看板',
              icon: 'Grid',
              keepAlive: true,
              breadcrumb: [
                { title: '首页', to: '/main/dashboard' },
                { title: '任务中心', to: '/main/tasks' },
                { title: '任务看板' }
              ]
            }
          },
          // 任务列表视图（统一的增强列表）
          {
            path: 'list',
            name: 'TaskList',
            component: () => import('@/views/tasks/EnhancedTaskListView.vue'),
            meta: {
              title: '任务列表',
              icon: 'Document',
              keepAlive: true,
              breadcrumb: [
                { title: '首页', to: '/main/dashboard' },
                { title: '任务中心', to: '/main/tasks' },
                { title: '任务列表' }
              ]
            }
          },
          // 甘特图视图（待开发）
          {
            path: 'gantt',
            name: 'TaskGantt',
            component: () => import('@/views/tasks/TaskGanttView.vue'),
            meta: {
              title: '甘特图',
              icon: 'TrendCharts',
              keepAlive: true,
              breadcrumb: [
                { title: '首页', to: '/main/dashboard' },
                { title: '任务中心', to: '/main/tasks' },
                { title: '甘特图' }
              ]
            }
          },
          // 我的任务
          {
            path: 'my',
            name: 'MyTasks',
            component: () => import('@/views/tasks/MyTasksView.vue'),
            meta: {
              title: '我的任务',
              icon: 'UserFilled',
              keepAlive: true,
              breadcrumb: [
                { title: '首页', to: '/main/dashboard' },
                { title: '任务中心', to: '/main/tasks' },
                { title: '我的任务' }
              ]
            }
          },
          // 团队任务
          {
            path: 'team',
            name: 'TeamTasks',
            component: () => import('@/views/tasks/TeamTasksView.vue'),
            meta: {
              title: '团队任务',
              icon: 'Postcard',
              roles: ['admin', 'manager', 'team_lead'],
              breadcrumb: [
                { title: '首页', to: '/main/dashboard' },
                { title: '任务中心', to: '/main/tasks' },
                { title: '团队任务' }
              ]
            }
          },
          // 周期性任务
          {
            path: 'periodic',
            name: 'PeriodicTaskView',
            component: () => import('@/views/tasks/PeriodicTaskView.vue'),
            meta: {
              title: '周期性任务',
              icon: 'RefreshLeft',
              keepAlive: true,
              breadcrumb: [
                { title: '首页', to: '/main/dashboard' },
                { title: '任务中心', to: '/main/tasks' },
                { title: '周期性任务' }
              ]
            }
          },
          // PDCA跟踪（保持兼容）
          {
            path: 'pdca-tracker',
            name: 'PDCATrackerView',
            component: () => import('@/views/tasks/EnhancedTaskListView.vue'),
            meta: {
              title: 'PDCA跟踪',
              icon: 'DataComparison',
              keepAlive: true,
              breadcrumb: [
                { title: '首页', to: '/main/dashboard' },
                { title: '任务中心', to: '/main/tasks' },
                { title: 'PDCA跟踪' }
              ]
            }
          },
          // 数据分析
          {
            path: 'analytics',
            name: 'TaskAnalytics',
            component: () => import('@/views/tasks/TaskAnalyticsView.vue'),
            meta: {
              title: '数据分析',
              icon: 'DataAnalysis',
              roles: ['admin', 'manager'],
              breadcrumb: [
                { title: '首页', to: '/main/dashboard' },
                { title: '任务中心', to: '/main/tasks' },
                { title: '数据分析' }
              ]
            }
          },
          // 班次管理
          {
            path: 'shift-management',
            name: 'WorkShiftManagement',
            component: () => import('@/views/tasks/WorkShiftManagementView.vue'),
            meta: {
              title: '班次管理',
              icon: 'Clock',
              roles: ['admin', 'manager'],
              keepAlive: true,
              breadcrumb: [
                { title: '首页', to: '/main/dashboard' },
                { title: '任务中心', to: '/main/tasks' },
                { title: '班次管理' }
              ]
            }
          },
          // 班次统计
          {
            path: 'shift-statistics',
            name: 'ShiftStatistics',
            component: () => import('@/views/tasks/ShiftStatisticsView.vue'),
            meta: {
              title: '班次统计',
              icon: 'DataBoard',
              keepAlive: true,
              breadcrumb: [
                { title: '首页', to: '/main/dashboard' },
                { title: '任务中心', to: '/main/tasks' },
                { title: '班次统计' }
              ]
            }
          },
          // 工作汇总报告
          {
            path: 'work-summary',
            name: 'WorkSummary',
            component: () => import('@/views/statistics/WorkSummaryView.vue'),
            meta: {
              title: '工作汇总报告',
              icon: 'DataAnalysis',
              keepAlive: true,
              breadcrumb: [
                { title: '首页', to: '/main/dashboard' },
                { title: '任务中心', to: '/main/tasks' },
                { title: '工作汇总报告' }
              ]
            }
          },
          // 班次管理测试页面 (开发环境)
          {
            path: 'shift-test',
            name: 'ShiftManagementTest',
            component: () => import('@/views/tasks/ShiftManagementTestView.vue'),
            meta: {
              title: '班次测试',
              icon: 'Tools',
              hidden: true, // 隐藏在菜单中
              breadcrumb: [
                { title: '首页', to: '/main/dashboard' },
                { title: '任务中心', to: '/main/tasks' },
                { title: '班次测试' }
              ]
            }
          },
          // 任务详情
          {
            path: 'detail/:id(\\d+)',
            name: 'TaskDetail',
            component: () => import('@/views/tasks/TaskDetailView.vue'),
            meta: {
              title: '任务详情',
              hidden: true,
              activeMenu: '/main/tasks/kanban',
              breadcrumb: [
                { title: '首页', to: '/main/dashboard' },
                { title: '任务中心', to: '/main/tasks' },
                { title: '任务详情' }
              ]
            },
            props: (route) => ({
              taskId: parseInt(route.params.id),
              tab: route.query.tab || 'details'
            })
          },
          // 任务创建/编辑
          {
            path: 'create',
            name: 'TaskCreate',
            component: () => import('@/views/tasks/TaskFormView.vue'),
            meta: {
              title: '创建任务',
              hidden: true,
              breadcrumb: [
                { title: '首页', to: '/main/dashboard' },
                { title: '任务中心', to: '/main/tasks' },
                { title: '创建任务' }
              ]
            }
          },
          {
            path: 'edit/:id(\\d+)',
            name: 'TaskEdit',
            component: () => import('@/views/tasks/TaskFormView.vue'),
            meta: {
              title: '编辑任务',
              hidden: true,
              breadcrumb: [
                { title: '首页', to: '/main/dashboard' },
                { title: '任务中心', to: '/main/tasks' },
                { title: '编辑任务' }
              ]
            },
            props: (route) => ({
              taskId: parseInt(route.params.id),
              isEdit: true
            })
          }
        ]
      },
      // 随手记路由 
      {
        path: 'quickmemo',
        name: 'QuickMemoModule',
        component: () => import('@/views/quickmemo/QuickMemoPageView.vue'),
        meta: {
          title: '随手记',
          icon: 'Notebook',
          keepAlive: true
        }
      },
      // 随手记列表页（作为独立菜单项）
      {
        path: 'memo-list',
        name: 'QuickMemoList',
        component: () => import('@/views/quickmemo/QuickMemoListView.vue'),
        meta: {
          title: '随手记列表',
          icon: 'Tickets',
          keepAlive: true
        }
      },
      // 随手记词云页面（作为独立菜单项）
      {
        path: 'memo-wordcloud',
        name: 'MemoWordCloud',
        component: () => import('@/views/quickmemo/MemoWordCloudView.vue'),
        meta: {
          title: '随手记词云',
          icon: 'PieChart',
          keepAlive: true
        }
      },
      // 排行榜路由
      {
        path: 'leaderboard',
        name: 'Leaderboard',
        component: () => import('@/views/tasks/LeaderboardView.vue'),
        meta: {
          title: '排行榜',
          icon: 'Trophy'
        }
      },
      // 通知系统测试路由
      {
        path: 'notification-test',
        name: 'NotificationTest',
        component: () => import('@/views/test/NotificationTest.vue'),
        meta: {
          title: '通知测试',
          icon: 'Bell',
          hidden: false // 在开发环境中显示
        }
      },
      // 游戏化系统路由
      {
        path: 'gamification',
        name: 'Gamification',
        component: RouterViewLayout,
        meta: {
          title: '游戏化系统',
          icon: 'Trophy'
        },
        redirect: '/main/gamification/overview',
        children: [
          {
            path: 'overview',
            name: 'GamificationOverview',
            component: () => import('@/views/gamification/index.vue'),
            meta: {
              title: '系统管理',
              icon: 'DataLine',
              keepAlive: true
            }
          },
          {
            path: 'achievements',
            name: 'GamificationAchievements',
            component: () => import('@/views/gamification/achievements.vue'),
            meta: {
              title: '成就管理',
              icon: 'Medal',
              keepAlive: true
            }
          },
          {
            path: 'leaderboard',
            name: 'GamificationLeaderboard',
            component: () => import('@/views/tasks/LeaderboardView.vue'),
            meta: {
              title: '排行榜',
              icon: 'TrendCharts',
              keepAlive: true
            }
          },
          {
            path: 'standardized-dashboard',
            name: 'StandardizedGamificationDashboard',
            component: () => import('@/views/gamification/StandardizedGamificationDashboard.vue'),
            meta: {
              title: '标准化游戏化仪表板',
              icon: 'DataBoard',
              keepAlive: true
            }
          }
        ]
      },
      // 系统管理路由
      {
        path: 'system',
        name: 'System',
        component: () => import('@/views/system/index.vue'),
        meta: {
          title: '系统管理',
          icon: 'Setting'
        },
        redirect: '/main/system/users',
        children: [
          {
            path: 'users',
            name: 'Users',
            component: () => import('@/views/system/users.vue'),
            meta: {
              title: '用户管理',
              keepAlive: true
            }
          },
          {
            path: 'roles',
            name: 'Roles',
            component: () => import('@/views/system/roles.vue'),
            meta: {
              title: '角色管理',
              keepAlive: true
            }
          },
          {
            path: 'menus',
            name: 'Menus',
            component: () => import('@/views/system/menus.vue'),
            meta: {
              title: '菜单管理',
              keepAlive: true
            }
          },
          {
            path: 'departments',
            name: 'Departments',
            component: () => import('@/views/system/departments.vue'),
            meta: {
              title: '部门管理',
              keepAlive: true
            }
          },
          {
            path: 'personnel',
            name: 'SystemPersonnel',
            component: () => import('@/views/personnel/index.vue'),
            meta: {
              title: '人员管理',
              keepAlive: true
            }
          },
          {
            path: 'logs',
            name: 'Logs',
            component: () => import('@/views/system/logs.vue'),
            meta: {
              title: '审计日志',
              keepAlive: true
            }
          }
        ]
      },
      // 用户中心路由 (修改后)
      {
        path: 'user',
        name: 'User',
        component: () => import('@/views/user/ProfileView.vue'),
        meta: {
          title: '个人中心',
          icon: 'UserFilled'
        },
        redirect: '/main/user/profile',
        children: [
          {
            path: 'profile',
            name: 'UserProfile',
            component: () => import('@/views/user/Profile.vue'),
            meta: {
              title: '个人资料',
              keepAlive: true
            }
          },
          // {
          //   path: 'settings',
          //   name: 'UserSettings',
          //   component: () => import('@/views/user/Settings.vue'),
          //   meta: {
          //     title: '账户设置'
          //   }
          // }
        ]
      },
      // 新增的头像测试页面路由
      {
        path: 'test-avatar',
        name: 'AvatarTestPage',
        component: () => import('@/views/test/AvatarTestPage.vue'),
        meta: {
          title: '头像测试页',
          icon: 'PictureFilled', // 您可以选一个合适的图标
          requiresAuth: true
        }
      },
      // 通知中心路由
      {
        path: 'notifications',
        name: 'Notifications',
        component: () => import('@/views/notifications/NotificationsListView.vue'),
        meta: {
          title: '通知中心',
          icon: 'Bell',
          requiresAuth: true,
          keepAlive: true,
          breadcrumb: [
            { title: '首页', to: '/main/dashboard' },
            { title: '通知中心' }
          ]
        }
      },
      // 备品备件管理路由
      {
        path: 'spareparts',
        component: RouterViewLayout,
        redirect: '/main/spareparts/list',
        meta: { title: '备品备件管理', icon: 'Box' },
        children: [
          // 备件台账
          {
            path: 'list',
            name: 'SparePartList',
            component: () => import('@/views/spareparts/SparePartListView.vue'),
            meta: {
              title: '备件台账',
              icon: 'Tickets',
              breadcrumb: [{ name: '备品备件管理' }, { name: '备件台账' }]
            }
          },
          // 备件类型管理
          {
            path: 'types',
            name: 'SparePartTypes',
            component: () => import('@/views/spareparts/SparePartTypeView.vue'),
            meta: {
              title: '备件类型管理',
              icon: 'CollectionTag',
              breadcrumb: [{ name: '备品备件管理' }, { name: '备件类型管理' }]
            }
          },
          // 备件库位管理
          {
            path: 'locations',
            name: 'SparePartLocations',
            component: () => import('@/views/spareparts/SparePartLocationView.vue'),
            meta: {
              title: '备件库位管理',
              icon: 'MapLocation',
              breadcrumb: [{ name: '备品备件管理' }, { name: '备件库位管理' }]
            }
          },
          {
            path: 'transactions',
            name: 'SparePartTransactions',
            component: () => import('@/views/spareparts/SparePartTransactionView.vue'),
            meta: {
              title: '出入库记录',
              auth: true,
              breadcrumb: [{ name: '备品备件管理' }, { name: '出入库记录' }]
            }
          },
          {
            path: 'repair-orders',
            name: 'RepairOrders',
            component: () => import('@/views/spareparts/RepairOrderView.vue'),
            meta: {
              title: '返厂维修管理',
              icon: 'Tools',
              auth: true,
              breadcrumb: [{ name: '备品备件管理' }, { name: '返厂维修管理' }]
            }
          },
          {
            path: 'quick-ops',
            name: 'SparePartQuickOps',
            component: () => import('@/views/spareparts/SparePartQuickOpsView.vue'),
            meta: {
              title: '快速出入库',
              auth: true,
              isMobileOptimized: true, // 特殊标记，用于可能的布局调整
              hideInMenu: true, // 通常快速操作页面不直接在主菜单显示
              breadcrumb: [{ name: '备品备件管理' }, { name: '快速出入库' }]
            }
          },
          // 移动端视图
          {
            path: 'mobile',
            name: 'SparePartMobile',
            component: () => import('@/views/spareparts/MobileSparePartView.vue'),
            meta: {
              title: '移动端视图',
              auth: true,
              isMobile: true,
              hideInMenu: true,
              breadcrumb: [{ name: '备品备件管理' }, { name: '移动端视图' }]
            }
          },
          // 移动端快速入库
          {
            path: 'mobile/quick-in',
            name: 'SparePartMobileQuickIn',
            component: () => import('@/views/spareparts/MobileQuickInView.vue'),
            meta: {
              title: '快速入库',
              auth: true,
              isMobile: true,
              hideInMenu: true,
              breadcrumb: [{ name: '备品备件管理' }, { name: '快速入库' }]
            }
          },
          // 移动端快速出库
          {
            path: 'mobile/quick-out',
            name: 'SparePartMobileQuickOut',
            component: () => import('@/views/spareparts/MobileQuickOutView.vue'),
            meta: {
              title: '快速出库',
              auth: true,
              isMobile: true,
              hideInMenu: true,
              breadcrumb: [{ name: '备品备件管理' }, { name: '快速出库' }]
            }
          },
          // 供应商管理
          {
            path: 'suppliers',
            name: 'SupplierList',
            component: () => import('@/views/suppliers/SupplierListView.vue'),
            meta: {
              title: '供应商管理',
              icon: 'OfficeBuilding',
              auth: true,
              breadcrumb: [{ name: '备品备件管理' }, { name: '供应商管理' }]
            }
          }
          // 可根据需要添加备件仪表盘路由
          // {
          //   path: 'dashboard',
          //   name: 'SparePartDashboard',
          //   component: () => import('@/views/spareparts/SparePartDashboardView.vue'),
          //   meta: {
          //     title: '备件仪表盘',
          //     auth: true,
          //     breadcrumb: [{ name: '备品备件管理' }, { name: '备件仪表盘' }]
          //   }
          // }
        ]
      }
    ]
  },
  // 个人信息和修改密码路由 (这些是独立于 /main 的，需要确认是否仍需要)
  {
    path: '/profile', // 这个路径可能与 /main/user/profile 冲突或重复
    component: DefaultLayout,
    meta: {
      hidden: true // 不在菜单显示
    },
    children: [
      {
        path: '',
        // name: 'StandaloneUserProfile', // 确保 Name 唯一
        component: () => import('@/views/user/Profile.vue'), // 注意：这是 Profile.vue 而不是 ProfileView.vue
        meta: {
          title: '个人信息',
          icon: 'User'
        }
      }
    ]
  },
  {
    path: '/change-password',
    component: DefaultLayout,
    meta: {
      hidden: true
    },
    children: [
      {
        path: '',
        name: 'ChangePassword',
        component: () => import('@/views/user/ChangePassword.vue'),
        meta: {
          title: '修改密码',
          icon: 'Lock'
        }
      }
    ]
  },
  // 错误页面路由
  {
    path: '/error',
    component: DefaultLayout,
    redirect: '/error/404',
    meta: {
      title: '错误页面',
      icon: 'Warning',
      hidden: true
    },
    children: [
      {
        path: '403',
        name: 'Error403',
        component: () => import('@/views/error/403.vue'),
        meta: {
          title: '403',
          hidden: true
        }
      },
      {
        path: '404',
        name: 'Error404',
        component: () => import('@/views/error/404.vue'),
        meta: {
          title: '404',
          hidden: true
        }
      }
    ]
  },
  // 添加测试导出页面路由
  {
    path: '/test',
    component: DefaultLayout,
    meta: {
      title: '功能测试',
      icon: 'Operation'
    },
    children: [
      {
        path: 'export',
        name: 'TestExport',
        component: () => import('@/views/test/export.vue'),
        meta: {
          title: '导出测试',
          icon: 'Download'
        }
      },
      {
        path: 'notification',
        name: 'NotificationTest',
        component: () => import('@/views/test/NotificationTestPage.vue'),
        meta: {
          title: '通知测试',
          icon: 'Bell',
          keepAlive: true,
          breadcrumb: [
            { title: '首页', to: '/main/dashboard' },
            { title: '功能测试', to: '/test' },
            { title: '通知测试' }
          ]
        }
      }
    ]
  },
  // 独立扫码录入页面（免登录）
  {
    path: '/input-csv',
    name: 'InputCsvPage',
    component: () => import('@/views/InputCsvPage.vue'),
    meta: {
      title: '扫码录入',
      public: true
    }
  },
  // 处理所有未匹配的路由，导向404页面
  {
    path: '/:pathMatch(.*)*',
    redirect: '/error/404',
    hidden: true
  }
]

export default routes 