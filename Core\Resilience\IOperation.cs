// IT资产管理系统 - 操作接口
// 文件路径: /Core/Resilience/IOperation.cs
// 功能: 定义操作接口，表示系统中可以离线执行和重试的操作

using System;
using System.Threading.Tasks;

namespace ItAssetsSystem.Core.Resilience
{
    /// <summary>
    /// 操作类型枚举
    /// </summary>
    public enum OperationType
    {
        /// <summary>
        /// 创建操作
        /// </summary>
        Create = 1,

        /// <summary>
        /// 更新操作
        /// </summary>
        Update = 2,

        /// <summary>
        /// 删除操作
        /// </summary>
        Delete = 3,

        /// <summary>
        /// 查询操作
        /// </summary>
        Query = 4,

        /// <summary>
        /// 自定义操作
        /// </summary>
        Custom = 99
    }

    /// <summary>
    /// 操作优先级枚举
    /// </summary>
    public enum OperationPriority
    {
        /// <summary>
        /// 低优先级
        /// </summary>
        Low = 0,

        /// <summary>
        /// 标准优先级
        /// </summary>
        Normal = 1,

        /// <summary>
        /// 高优先级
        /// </summary>
        High = 2,

        /// <summary>
        /// 紧急优先级
        /// </summary>
        Critical = 3
    }

    /// <summary>
    /// 操作状态枚举
    /// </summary>
    public enum OperationStatus
    {
        /// <summary>
        /// 待处理
        /// </summary>
        Pending = 0,

        /// <summary>
        /// 执行中
        /// </summary>
        InProgress = 1,

        /// <summary>
        /// 已完成
        /// </summary>
        Completed = 2,

        /// <summary>
        /// 失败
        /// </summary>
        Failed = 3,

        /// <summary>
        /// 已取消
        /// </summary>
        Cancelled = 4
    }

    /// <summary>
    /// 操作结果
    /// </summary>
    public class OperationResult
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 结果数据
        /// </summary>
        public object Data { get; set; }

        /// <summary>
        /// 创建成功结果
        /// </summary>
        /// <param name="data">结果数据</param>
        /// <returns>操作结果</returns>
        public static OperationResult Success(object data = null)
        {
            return new OperationResult { IsSuccess = true, Data = data };
        }

        /// <summary>
        /// 创建失败结果
        /// </summary>
        /// <param name="errorMessage">错误消息</param>
        /// <returns>操作结果</returns>
        public static OperationResult Failure(string errorMessage)
        {
            return new OperationResult { IsSuccess = false, ErrorMessage = errorMessage };
        }
    }

    /// <summary>
    /// 操作接口
    /// </summary>
    public interface IOperation
    {
        /// <summary>
        /// 操作ID
        /// </summary>
        string Id { get; set; }

        /// <summary>
        /// 操作类型
        /// </summary>
        OperationType Type { get; }

        /// <summary>
        /// 目标实体类型
        /// </summary>
        string EntityType { get; }

        /// <summary>
        /// 目标实体ID
        /// </summary>
        string EntityId { get; }

        /// <summary>
        /// 操作数据
        /// </summary>
        string Data { get; }

        /// <summary>
        /// 操作状态
        /// </summary>
        OperationStatus Status { get; set; }

        /// <summary>
        /// 优先级
        /// </summary>
        OperationPriority Priority { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        DateTime CreatedAt { get; }

        /// <summary>
        /// 上次尝试时间
        /// </summary>
        DateTime? LastAttemptTime { get; set; }

        /// <summary>
        /// 重试次数
        /// </summary>
        int RetryCount { get; set; }

        /// <summary>
        /// 最大重试次数
        /// </summary>
        int MaxRetries { get; }

        /// <summary>
        /// 后台执行标志
        /// </summary>
        bool IsBackgroundOperation { get; }

        /// <summary>
        /// 执行操作
        /// </summary>
        /// <returns>操作结果</returns>
        Task<OperationResult> ExecuteAsync();

        /// <summary>
        /// 检查操作是否可重试
        /// </summary>
        /// <returns>是否可重试</returns>
        bool CanRetry();

        /// <summary>
        /// 获取冲突解决策略
        /// </summary>
        /// <returns>冲突解决策略</returns>
        ConflictResolutionStrategy GetConflictResolutionStrategy();

        /// <summary>
        /// 序列化为JSON
        /// </summary>
        /// <returns>JSON字符串</returns>
        string ToJson();

        /// <summary>
        /// 从JSON反序列化
        /// </summary>
        /// <param name="json">JSON字符串</param>
        void FromJson(string json);
    }

    /// <summary>
    /// 冲突解决策略枚举
    /// </summary>
    public enum ConflictResolutionStrategy
    {
        /// <summary>
        /// 以本地为准
        /// </summary>
        PreferLocal = 0,

        /// <summary>
        /// 以服务器为准
        /// </summary>
        PreferServer = 1,

        /// <summary>
        /// 合并
        /// </summary>
        Merge = 2,

        /// <summary>
        /// 提示用户
        /// </summary>
        PromptUser = 3
    }
} 