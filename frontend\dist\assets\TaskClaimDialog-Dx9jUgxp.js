import{_ as a,c as e,r as l,j as s,k as t,o as i,w as o,d,b as r,$ as u,e as n,h as c,a as v,t as p,p as m,F as f,E as k}from"./index-CG5lHOPO.js";import{w as y}from"./workShift-Ce3ThpoM.js";import{u as w}from"./gamification-Dm7mCEPf.js";const g={class:"task-claim-content"},b={class:"form-section"},h={class:"task-option"},V={class:"task-info"},_={class:"task-name"},D={class:"task-meta"},C={class:"task-type"},E={key:0,class:"task-deadline"},I={key:0,class:"task-preview"},x={class:"task-details"},T={class:"detail-row"},j={class:"value"},z={class:"detail-row"},L={class:"value"},N={class:"detail-row"},S={key:0,class:"detail-row"},U={class:"value"},$={class:"form-section"},A={class:"dialog-footer"},B=a({__name:"TaskClaimDialog",props:{modelValue:{type:Boolean,default:!1}},emits:["update:modelValue","success"],setup(a,{emit:B}){const F=a,H=B,M=e({get:()=>F.modelValue,set:a=>H("update:modelValue",a)}),q=l(!1),G=l(!1),J=l([]),K=l(null),O=l(""),P=w(),Q=e((()=>J.value.find((a=>a.taskId===K.value)))),R=async()=>{var a;if(K.value){G.value=!0;try{const e=await y.claimTask({taskId:K.value,notes:O.value});e.success?(k.success("任务领取成功"),P.recordEvent("task_claimed",{taskId:K.value,taskName:null==(a=Q.value)?void 0:a.name}),H("success"),W()):k.error(e.message||"任务领取失败")}catch(e){k.error("任务领取失败")}finally{G.value=!1}}else k.warning("请选择要领取的任务")},W=()=>{K.value=null,O.value="",M.value=!1},X=a=>({Critical:"danger",High:"warning",Medium:"primary",Low:"info"}[a]||"primary");return s(M,(a=>{a&&(async()=>{var a;q.value=!0;try{const e=await y.getAvailableTasks();e.success?J.value=(null==(a=e.data)?void 0:a.items)||[]:k.error("获取可领取任务失败")}catch(e){k.error("获取可领取任务失败")}finally{q.value=!1}})()})),(a,e)=>{const l=v("el-tag"),s=v("el-option"),k=v("el-select"),y=v("el-input"),w=v("el-button"),B=v("el-dialog");return i(),t(B,{modelValue:M.value,"onUpdate:modelValue":e[2]||(e[2]=a=>M.value=a),title:"任务领取",width:"600px","before-close":W},{footer:o((()=>[d("div",A,[n(w,{onClick:W},{default:o((()=>e[10]||(e[10]=[m("取消")]))),_:1}),n(w,{type:"primary",onClick:R,loading:G.value,disabled:!K.value},{default:o((()=>e[11]||(e[11]=[m(" 确认领取 ")]))),_:1},8,["loading","disabled"])])])),default:o((()=>{return[d("div",g,[d("div",b,[e[3]||(e[3]=d("label",{class:"form-label"},"选择要领取的任务:",-1)),n(k,{modelValue:K.value,"onUpdate:modelValue":e[0]||(e[0]=a=>K.value=a),placeholder:"请选择任务",filterable:"",clearable:"",style:{width:"100%"},loading:q.value},{default:o((()=>[(i(!0),r(f,null,c(J.value,(a=>(i(),t(s,{key:a.taskId,label:`${a.name} (${a.priority})`,value:a.taskId},{default:o((()=>{return[d("div",h,[d("div",V,[d("span",_,p(a.name),1),n(l,{type:X(a.priority),size:"small",class:"task-priority"},{default:o((()=>[m(p(a.priority),1)])),_:2},1032,["type"])]),d("div",D,[d("span",C,p(a.taskType),1),a.planEndDate?(i(),r("span",E," 截止: "+p((e=a.planEndDate,e?new Date(e).toLocaleDateString("zh-CN"):"")),1)):u("",!0)])])];var e})),_:2},1032,["label","value"])))),128))])),_:1},8,["modelValue","loading"])]),Q.value?(i(),r("div",I,[e[8]||(e[8]=d("h4",null,"任务详情",-1)),d("div",x,[d("div",T,[e[4]||(e[4]=d("span",{class:"label"},"任务名称:",-1)),d("span",j,p(Q.value.name),1)]),d("div",z,[e[5]||(e[5]=d("span",{class:"label"},"任务描述:",-1)),d("span",L,p(Q.value.description||"无描述"),1)]),d("div",N,[e[6]||(e[6]=d("span",{class:"label"},"优先级:",-1)),n(l,{type:X(Q.value.priority),size:"small"},{default:o((()=>[m(p(Q.value.priority),1)])),_:1},8,["type"])]),Q.value.planEndDate?(i(),r("div",S,[e[7]||(e[7]=d("span",{class:"label"},"计划完成时间:",-1)),d("span",U,p((a=Q.value.planEndDate,a?new Date(a).toLocaleString("zh-CN"):"")),1)])):u("",!0)])])):u("",!0),d("div",$,[e[9]||(e[9]=d("label",{class:"form-label"},"备注 (可选):",-1)),n(y,{modelValue:O.value,"onUpdate:modelValue":e[1]||(e[1]=a=>O.value=a),type:"textarea",rows:3,placeholder:"请输入领取任务的备注信息...",maxlength:"500","show-word-limit":""},null,8,["modelValue"])])])];var a})),_:1},8,["modelValue"])}}},[["__scopeId","data-v-f51cf643"]]);export{B as T};
