{"version": 3, "file": "index.js", "sources": ["../../../../../packages/components/steps/index.ts"], "sourcesContent": ["import { withInstall, withNoopInstall } from '@element-plus/utils'\n\nimport Steps from './src/steps.vue'\nimport Step from './src/item.vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElSteps: SFCWithInstall<typeof Steps> & {\n  Step: typeof Step\n} = withInstall(Steps, {\n  Step,\n})\nexport default ElSteps\nexport const ElStep: SFCWithInstall<typeof Step> = withNoopInstall(Step)\n\nexport * from './src/item'\nexport * from './src/steps'\n"], "names": ["withInstall", "Steps", "Step", "withNoopInstall"], "mappings": ";;;;;;;;;;AAGY,MAAC,OAAO,GAAGA,mBAAW,CAACC,kBAAK,EAAE;AAC1C,QAAEC,iBAAI;AACN,CAAC,EAAE;AAES,MAAC,MAAM,GAAGC,uBAAe,CAACD,iBAAI;;;;;;;;;"}