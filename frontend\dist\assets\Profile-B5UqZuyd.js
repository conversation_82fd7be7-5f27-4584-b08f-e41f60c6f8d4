import{_ as e,x as a,r as l,C as r,c as s,ad as t,z as o,b as u,e as n,w as d,a as i,o as m,d as p,f as c,bc as f,p as v,t as _,E as g}from"./index-CG5lHOPO.js";import{P as h}from"./PageHeader-o27NV7QI.js";const b={class:"user-profile"},V={class:"card-header"},y={class:"profile-content"},w={class:"avatar-container"},U={class:"avatar-uploader"},x={class:"avatar-uploader-icon"},k={class:"info-container"},j=e({__name:"Profile",setup(e){const j=a(),z=l(!1),C=l(null);r.defaultAvatar;const P=`${r.apiBaseUrl}/v2/profile/avatar`,A=s((()=>({Authorization:`Bearer ${j.token}`}))),B=s((()=>j.userInfo||{})),L=t({name:"",email:"",phone:""}),$={name:[{required:!0,message:"请输入姓名",trigger:"blur"},{min:2,max:20,message:"长度在 2 到 20 个字符",trigger:"blur"}],email:[{required:!0,message:"请输入邮箱地址",trigger:"blur"},{type:"email",message:"请输入正确的邮箱地址",trigger:"blur"}],phone:[{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码",trigger:"blur"}]},q=()=>{L.name=B.value.name||"",L.email=B.value.email||"",L.phone=B.value.phone||"",z.value=!0},I=e=>{if(e.success){const a=e.data.avatarUrl;a?(j.setAvatar(a),g.success("头像上传成功")):g.warning("头像上传成功，但未获取到URL")}else g.error(e.message||"头像上传失败")},R=e=>{const a=e.type.startsWith("image/"),l=e.size/1024/1024<5;return a?!!l||(g.error("头像大小不能超过 5MB!"),!1):(g.error("头像必须是图片格式!"),!1)},D=e=>{g.error("头像上传失败，请重试")},E=async()=>{C.value&&await C.value.validate((async e=>{if(e)try{const e=await j.updateProfile({name:L.name,email:L.email,phone:L.phone});e.success?(g.success("个人信息更新成功"),z.value=!1):g.error(e.message||"更新个人信息失败")}catch(a){g.error("更新个人信息失败: "+(a.message||"未知错误"))}}))};return o((()=>{})),(e,a)=>{const l=i("el-button"),r=i("el-avatar"),s=i("el-icon"),t=i("el-upload"),o=i("el-descriptions-item"),g=i("el-descriptions"),j=i("el-card"),F=i("el-input"),H=i("el-form-item"),M=i("el-form"),S=i("el-dialog");return m(),u("div",b,[n(h,{title:"个人信息",description:"查看和修改您的个人账户信息"}),n(j,{shadow:"hover",class:"profile-card"},{header:d((()=>[p("div",V,[a[6]||(a[6]=p("h3",null,"个人信息",-1)),n(l,{type:"primary",onClick:q},{default:d((()=>a[5]||(a[5]=[v("编辑")]))),_:1})])])),default:d((()=>[p("div",y,[p("div",w,[p("div",U,[n(t,{class:"avatar-uploader",action:P,headers:A.value,"show-file-list":!1,"on-success":I,"before-upload":R,"on-error":D},{default:d((()=>[n(r,{size:100,src:"http://localhost/files/uploads/avatars/user1_1747191994946.jpg",class:"profile-avatar"}),p("div",x,[n(s,null,{default:d((()=>[n(c(f))])),_:1}),a[7]||(a[7]=p("span",null,"点击更换",-1))])])),_:1},8,["headers"])])]),p("div",k,[n(g,{column:2,border:""},{default:d((()=>[n(o,{label:"用户名"},{default:d((()=>[v(_(B.value.username),1)])),_:1}),n(o,{label:"姓名"},{default:d((()=>[v(_(B.value.name),1)])),_:1}),n(o,{label:"邮箱"},{default:d((()=>[v(_(B.value.email),1)])),_:1}),n(o,{label:"电话"},{default:d((()=>[v(_(B.value.phone),1)])),_:1}),n(o,{label:"部门"},{default:d((()=>[v(_(B.value.department),1)])),_:1}),n(o,{label:"职位"},{default:d((()=>[v(_(B.value.position),1)])),_:1}),n(o,{label:"角色"},{default:d((()=>{var e;return[v(_((null==(e=B.value.roles)?void 0:e.join(", "))||"无"),1)]})),_:1}),n(o,{label:"最后登录"},{default:d((()=>{return[v(_((e=B.value.lastLogin,e?new Date(e).toLocaleString():"未登录")),1)];var e})),_:1})])),_:1})])])])),_:1}),n(S,{modelValue:z.value,"onUpdate:modelValue":a[4]||(a[4]=e=>z.value=e),title:"编辑个人信息",width:"500px","destroy-on-close":""},{footer:d((()=>[n(l,{onClick:a[3]||(a[3]=e=>z.value=!1)},{default:d((()=>a[8]||(a[8]=[v("取消")]))),_:1}),n(l,{type:"primary",onClick:E},{default:d((()=>a[9]||(a[9]=[v("保存")]))),_:1})])),default:d((()=>[n(M,{ref_key:"editFormRef",ref:C,model:L,rules:$,"label-width":"100px"},{default:d((()=>[n(H,{label:"姓名",prop:"name"},{default:d((()=>[n(F,{modelValue:L.name,"onUpdate:modelValue":a[0]||(a[0]=e=>L.name=e)},null,8,["modelValue"])])),_:1}),n(H,{label:"邮箱",prop:"email"},{default:d((()=>[n(F,{modelValue:L.email,"onUpdate:modelValue":a[1]||(a[1]=e=>L.email=e)},null,8,["modelValue"])])),_:1}),n(H,{label:"电话",prop:"phone"},{default:d((()=>[n(F,{modelValue:L.phone,"onUpdate:modelValue":a[2]||(a[2]=e=>L.phone=e)},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-85e50b91"]]);export{j as default};
