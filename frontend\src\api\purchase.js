/**
 * 航空航天级IT资产管理系统 - 采购管理API
 * 文件路径: src/api/purchase.js
 * 功能描述: 提供采购管理相关的API服务
 */

import request from '@/utils/request'

// 采购API基础路径 - 使用V2版本
const baseUrl = '/v2/purchase'

export default {
  /**
   * 获取采购单列表
   * @param {Object} params - 查询参数
   * @returns {Promise}
   */
  getPurchaseList(params) {
    return request.get(baseUrl, params)
  },
  
  /**
   * 获取采购单详情
   * @param {number|string} id - 采购单ID
   * @returns {Promise}
   */
  getPurchaseById(id) {
    return request.get(`${baseUrl}/${id}`)
  },

  /**
   * 获取采购单详情 (V2)
   * @param {number|string} id - 采购单ID
   * @returns {Promise}
   */
  getPurchaseByIdV2(id) {
    return request.get(`/v2/Purchase/${id}`)
  },
  
  /**
   * 创建采购单
   * @param {Object} data - 采购单数据
   * @returns {Promise}
   */
  createPurchase(data) {
    return request.post(baseUrl, data)
  },

  /**
   * 创建采购订单 (V2)
   * @param {Object} data - 采购订单数据
   * @returns {Promise}
   */
  createPurchaseOrder(data) {
    return request.post(baseUrl, data)
  },
  
  /**
   * 更新采购单
   * @param {number|string} id - 采购单ID
   * @param {Object} data - 采购单数据
   * @returns {Promise}
   */
  updatePurchase(id, data) {
    return request.put(`${baseUrl}/${id}`, data)
  },
  
  /**
   * 删除采购单
   * @param {number|string} id - 采购单ID
   * @returns {Promise}
   */
  deletePurchase(id) {
    return request.delete(`${baseUrl}/${id}`)
  },
  
  /**
   * 获取供应商列表
   * @param {Object} params - 查询参数
   * @returns {Promise}
   */
  getSupplierList(params) {
    return request.get('/suppliers', params)
  },
  
  /**
   * 获取供应商详情
   * @param {number|string} id - 供应商ID
   * @returns {Promise}
   */
  getSupplierById(id) {
    return request.get(`/suppliers/${id}`)
  },
  
  /**
   * 创建供应商
   * @param {Object} data - 供应商数据
   * @returns {Promise}
   */
  createSupplier(data) {
    return request.post('/suppliers', data)
  },
  
  /**
   * 更新供应商
   * @param {number|string} id - 供应商ID
   * @param {Object} data - 供应商数据
   * @returns {Promise}
   */
  updateSupplier(id, data) {
    return request.put(`/suppliers/${id}`, data)
  },
  
  /**
   * 删除供应商
   * @param {number|string} id - 供应商ID
   * @returns {Promise}
   */
  deleteSupplier(id) {
    return request.delete(`/suppliers/${id}`)
  },
  
  /**
   * 提交采购单审批
   * @param {number|string} id - 采购单ID
   * @returns {Promise}
   */
  submitPurchaseForApproval(id) {
    return request.put(`${baseUrl}/${id}/submit`)
  },
  
  /**
   * 批准采购单
   * @param {number|string} id - 采购单ID
   * @param {Object} data - 批准相关数据
   * @returns {Promise}
   */
  approvePurchase(id, data) {
    return request.put(`${baseUrl}/${id}/approve`, data)
  },
  
  /**
   * 拒绝采购单
   * @param {number|string} id - 采购单ID
   * @param {Object} data - 拒绝原因等数据
   * @returns {Promise}
   */
  rejectPurchase(id, data) {
    return request.put(`${baseUrl}/${id}/reject`, data)
  },
  
  /**
   * 确认采购下单
   * @param {number|string} id - 采购单ID
   * @param {Object} data - 下单确认数据
   * @returns {Promise}
   */
  confirmPurchaseOrder(id, data) {
    return request.put(`${baseUrl}/${id}/order`, data)
  },
  
  /**
   * 确认采购收货
   * @param {number|string} id - 采购单ID
   * @param {Object} data - 收货确认数据
   * @returns {Promise}
   */
  confirmPurchaseReceived(id, data) {
    return request.put(`${baseUrl}/${id}/receive`, data)
  },
  
  /**
   * 确认采购入库
   * @param {number|string} id - 采购单ID
   * @param {Object} data - 入库确认数据
   * @returns {Promise}
   */
  confirmPurchaseStorage(id, data) {
    return request.put(`${baseUrl}/${id}/storage`, data)
  },
  
  /**
   * 确认采购完成
   * @param {number|string} id - 采购单ID
   * @returns {Promise}
   */
  completePurchase(id) {
    return request.put(`${baseUrl}/${id}/complete`)
  },
  
  /**
   * 取消采购单
   * @param {number|string} id - 采购单ID
   * @param {Object} data - 取消原因等数据
   * @returns {Promise}
   */
  cancelPurchase(id, data) {
    return request.put(`${baseUrl}/${id}/cancel`, data)
  },
  
  /**
   * 获取采购单审批历史
   * @param {number|string} id - 采购单ID
   * @param {Object} params - 查询参数
   * @returns {Promise}
   */
  getPurchaseApprovalHistory(id, params) {
    return request.get(`${baseUrl}/${id}/approval-history`, params)
  },
  
  /**
   * 为采购单上传附件
   * @param {number|string} id - 采购单ID
   * @param {FormData} formData - 包含文件的表单数据
   * @returns {Promise}
   */
  uploadPurchaseAttachment(id, formData) {
    return request.upload(`${baseUrl}/${id}/attachments`, formData)
  },
  
  /**
   * 获取采购单附件列表
   * @param {number|string} id - 采购单ID
   * @param {Object} params - 查询参数
   * @returns {Promise}
   */
  getPurchaseAttachments(id, params) {
    return request.get(`${baseUrl}/${id}/attachments`, params)
  },
  
  /**
   * 删除采购单附件
   * @param {number|string} purchaseId - 采购单ID
   * @param {number|string} attachmentId - 附件ID
   * @returns {Promise}
   */
  deletePurchaseAttachment(purchaseId, attachmentId) {
    return request.delete(`${baseUrl}/${purchaseId}/attachments/${attachmentId}`)
  },
  
  /**
   * 导出采购数据
   * @param {Object} params - 查询参数
   * @returns {Promise}
   */
  exportPurchases(params) {
    return request.download(`${baseUrl}/export`, params, 'purchases.xlsx')
  },
  
  /**
   * 获取采购统计数据
   * @param {Object} params - 查询参数
   * @returns {Promise}
   */
  getPurchaseStatistics(params) {
    return request.get(`${baseUrl}/statistics`, params)
  },

  // ===== V2 新增功能 =====

  /**
   * 获取供应商列表 (V2)
   * @returns {Promise}
   */
  getSuppliersV2() {
    return request.get(`${baseUrl}/suppliers`)
  },

  /**
   * 获取可采购的资产类型列表 (V2)
   * @returns {Promise}
   */
  getPurchasableAssetTypes() {
    return request.get(`${baseUrl}/asset-types`)
  },

  /**
   * 处理采购物品转化 (V2)
   * @param {number} orderId - 采购订单ID
   * @param {Array} items - 转化物品列表
   * @returns {Promise}
   */
  processDeliveredItems(orderId, items) {
    return request.post(`${baseUrl}/${orderId}/process-items`, items)
  },

  /**
   * 审批采购申请
   * @param {number} id - 采购申请ID
   * @returns {Promise}
   */
  approvePurchase(id) {
    return request.put(`${baseUrl}/${id}/approve`)
  },

  /**
   * 拒绝采购申请
   * @param {number} id - 采购申请ID
   * @param {Object} data - 拒绝原因
   * @returns {Promise}
   */
  rejectPurchase(id, data) {
    return request.put(`${baseUrl}/${id}/reject`, data)
  },

  /**
   * 完成采购
   * @param {number} id - 采购申请ID
   * @param {Object} data - 完成信息
   * @returns {Promise}
   */
  completePurchase(id, data) {
    return request.put(`${baseUrl}/${id}/complete`, data)
  }
}