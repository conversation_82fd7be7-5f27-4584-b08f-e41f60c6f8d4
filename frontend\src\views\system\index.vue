/**
 * 航空航天级IT资产管理系统 - 系统管理页面
 * 文件路径: src/views/system/index.vue
 * 功能描述: 系统管理模块的布局容器
 */

<template>
  <div class="system-container">
    <router-view v-slot="{ Component }">
      <keep-alive>
        <component :is="Component" />
      </keep-alive>
    </router-view>
  </div>
</template>

<script setup>
// 该组件仅作为系统管理子页面的容器
</script>

<style lang="scss" scoped>
@use "@/styles/variables.scss" as *;

.system-container {
  padding: 16px;
  height: 100%;
}
</style> 