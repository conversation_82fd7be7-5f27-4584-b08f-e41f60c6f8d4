<template>
  <div class="spare-part-quick-ops-view">
    <div class="ops-header">
      <h2>快速出入库</h2>
      <p>专为移动端设计，简化操作流程。</p>
    </div>

    <div class="ops-type-selector">
      <el-radio-group v-model="operationType" size="large">
        <el-radio-button label="inbound">入库</el-radio-button>
        <el-radio-button label="outbound">出库</el-radio-button>
      </el-radio-group>
    </div>

    <div class="ops-form">
      <!-- 备件选择 (考虑扫码或搜索) -->
      <div class="form-item">
        <label>备件选择:</label>
        <!-- TODO: 实现备件选择器，支持搜索和扫码输入 -->
        <p>交互: 点击后弹出搜索框，或提供扫码按钮。</p>
      </div>

      <!-- 数量 -->
      <div class="form-item">
        <label>数量:</label>
        <!-- TODO: 实现数量输入 -->
        <p>交互: 数字输入框，可带加减按钮。</p>
      </div>

      <!-- 仓库/库位选择 (根据出入库类型调整) -->
      <div class="form-item">
        <label>{{ operationType === 'inbound' ? '入库到仓库/库位:' : '从仓库/库位出库:' }}</label>
        <!-- TODO: 实现仓库/库位选择器 -->
        <p>交互: 简洁的选择器，可能需要分步选择仓库再选库位。</p>
      </div>

      <!-- 领用人/关联任务 (出库时可能需要) -->
      <div v-if="operationType === 'outbound'" class="form-item">
        <label>领用人/关联信息:</label>
        <!-- TODO: 实现领用人或其他关联信息的输入 -->
        <p>交互: 文本输入或选择器。</p>
      </div>

      <div class="form-actions">
        <!-- TODO: 实现提交按钮和逻辑 -->
        <el-button type="primary" size="large" @click="handleSubmit">确认{{ operationType === 'inbound' ? '入库' : '出库' }}</el-button>
      </div>
    </div>

    <div class="recent-ops">
      <h3>最近操作记录 (仅示意)</h3>
      <!-- TODO: 显示最近的几条快速操作记录 -->
      <ul>
        <li>入库：CPU * 5 到 主仓库-A01 (张三)</li>
        <li>出库：内存条 * 2 从 主仓库-B02 (李四，用于维修单号 XXX)</li>
      </ul>
    </div>

  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';

const operationType = ref('inbound'); // 'inbound' or 'outbound'

const handleSubmit = () => {
  // TODO: 实现提交逻辑
  console.log(`Submitting ${operationType.value} operation.`);
};

onMounted(() => {
  console.log('SparePartQuickOpsView component mounted');
  // 可以在这里针对移动端做一些初始化设置
});
</script>

<style scoped>
.spare-part-quick-ops-view {
  padding: 15px;
  max-width: 600px; /* 在PC端预览时限制最大宽度，方便查看移动端效果 */
  margin: auto; /* 居中显示 */
}

.ops-header {
  text-align: center;
  margin-bottom: 20px;
}

.ops-type-selector {
  display: flex;
  justify-content: center;
  margin-bottom: 25px;
}

.form-item {
  margin-bottom: 15px;
}

.form-item label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

.form-actions {
  margin-top: 25px;
  text-align: center;
}

.recent-ops {
  margin-top: 30px;
  border-top: 1px solid #eee;
  padding-top: 15px;
}

.recent-ops h3 {
  margin-bottom: 10px;
}

/* 可以添加更多针对移动端的优化样式 */
@media (max-width: 768px) {
  .spare-part-quick-ops-view {
    padding: 10px;
  }
  /* 可以在这里调整字体大小、按钮大小等 */
}
</style> 