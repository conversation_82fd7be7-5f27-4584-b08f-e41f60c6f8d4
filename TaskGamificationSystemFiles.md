# 任务完成奖励系统 - 完整文件清单

## 📱 **前端界面文件**

### 1. 主要任务管理界面
- **`frontend/src/views/tasks/EnhancedTaskListView.vue`** - 主任务列表界面
  - 包含任务领取按钮 (第323-334行)
  - 任务完成状态更改 (第277行 `handleStatusChange`)
  - 完成人水印显示 (第219-227行)
  - 游戏化奖励触发 (第838行, 1060行)

### 2. 任务相关组件
- **`frontend/src/components/Tasks/TaskClaimDialog.vue`** - 任务领取对话框
- **`frontend/src/components/Tasks/TaskRewardPopup.vue`** - 奖励弹窗组件
- **`frontend/src/components/Tasks/RewardPanel.vue`** - 奖励面板组件
- **`frontend/src/components/Tasks/EnhancedTaskCard.vue`** - 任务卡片组件
- **`frontend/src/components/Tasks/LeaderboardPanel.vue`** - 排行榜面板

### 3. 统计排行榜界面
- **`frontend/src/views/tasks/LeaderboardView.vue`** - 排行榜主界面
- **`frontend/src/views/tasks/ShiftStatisticsView.vue`** - 班次统计界面
- **`frontend/src/views/gamification/`** - 游戏化相关界面目录

### 4. API调用文件
- **`frontend/src/api/gamification.js`** - 游戏化API (包含claimTask方法)
- **`frontend/src/api/task.js`** - 任务API
- **`frontend/src/api/tasks.js`** - 任务增强API

## 🔧 **后端API文件**

### 1. 控制器层
- **`Api/V2/Controllers/TasksController.cs`** - 任务控制器
  - `CompleteTask` 方法 (第564-610行) - 任务完成API
  - `ClaimTask` 方法 (第1179-1228行) - 任务领取API
  - 游戏化奖励触发 (第596行)

- **`Api/V2/Controllers/GamificationV2Controller.cs`** - 游戏化控制器
  - 统计数据API
  - 排行榜API
  - 奖励触发API

### 2. 服务层
- **`Application/Features/Tasks/Services/TaskService.cs`** - 任务服务
  - `CompleteTaskAsync` 方法 (第1160-1242行) - 任务完成逻辑
  - `UpdateTaskStatusAsync` 方法 (第980-990行) - 状态更新逻辑
  - 完成人信息设置 (第1183行)
  - 完成时间设置 (第1181行)

- **`Services/UniversalGamificationService.cs`** - 通用游戏化服务
  - `TriggerBehaviorRewardAsync` 方法 (第48行) - 奖励触发
  - 任务完成奖励计算 (第200行)

- **`Application/Features/Tasks/Services/TaskClaimService.cs`** - 任务领取服务
- **`Application/Features/Tasks/Services/TaskWatermarkService.cs`** - 任务水印服务

### 3. 事件处理
- **`Domain/Events/TaskCompletedEvent.cs`** - 任务完成事件
- **`Application/Features/Gamification/Events/GamificationEvents.cs`** - 游戏化事件

## 🗄️ **数据库表结构**

### 1. 核心任务表
```sql
-- tasks 表 (主要任务表)
CREATE TABLE tasks (
    TaskId BIGINT PRIMARY KEY AUTO_INCREMENT,
    Name VARCHAR(255) NOT NULL,
    Status VARCHAR(50) DEFAULT 'Todo',
    CompletedByUserId INT NULL,           -- 完成人ID
    CompletedAt DATETIME NULL,            -- 完成时间
    CompletionWatermarkColor VARCHAR(7),  -- 完成水印颜色
    ActualEndDate DATETIME NULL,          -- 实际结束时间
    Progress INT DEFAULT 0,               -- 进度百分比
    Points INT DEFAULT 0,                 -- 任务积分
    CreatorUserId INT,
    AssigneeUserId INT,
    CreationTimestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    LastUpdatedTimestamp DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    IsDeleted BOOLEAN DEFAULT FALSE
);
```

### 2. 任务领取表
```sql
-- task_claims 表 (任务领取记录)
CREATE TABLE task_claims (
    ClaimId BIGINT PRIMARY KEY AUTO_INCREMENT,
    TaskId BIGINT NOT NULL,
    UserId INT NOT NULL,
    ClaimTimestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    ClaimStatus VARCHAR(50) DEFAULT 'Claimed',
    Notes TEXT,
    IsDeleted BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (TaskId) REFERENCES tasks(TaskId),
    FOREIGN KEY (UserId) REFERENCES users(id)
);
```

### 3. 游戏化统计表
```sql
-- user_stats 表 (用户统计)
CREATE TABLE user_stats (
    UserId INT PRIMARY KEY,
    PointsBalance INT DEFAULT 0,          -- 积分余额
    XpBalance INT DEFAULT 0,              -- 经验值余额
    CoinsBalance INT DEFAULT 0,           -- 金币余额
    DiamondsBalance INT DEFAULT 0,        -- 钻石余额
    TasksCompletedCount INT DEFAULT 0,    -- 完成任务数
    TasksClaimedCount INT DEFAULT 0,      -- 领取任务数
    TasksCreatedCount INT DEFAULT 0,      -- 创建任务数
    FaultsReportedCount INT DEFAULT 0,    -- 故障报告数
    AssetsUpdatedCount INT DEFAULT 0,     -- 资产更新数
    LoginStreakCount INT DEFAULT 0,       -- 连续登录天数
    LastLoginDate DATE,
    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
    UpdatedAt DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 4. 排行榜表
```sql
-- leaderboards 表 (排行榜)
CREATE TABLE leaderboards (
    LeaderboardId BIGINT PRIMARY KEY AUTO_INCREMENT,
    UserId INT NOT NULL,
    LeaderboardType VARCHAR(50) NOT NULL, -- 'points', 'weekly_tasks', 'monthly_tasks'
    Period VARCHAR(50) NOT NULL,          -- 'current', 'weekly', 'monthly'
    RankScore DECIMAL(10,2) NOT NULL,     -- 排行分数
    Rank INT NOT NULL,                    -- 排名
    LastUpdated DATETIME DEFAULT CURRENT_TIMESTAMP,
    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### 5. 游戏化行为表
```sql
-- behavior_types 表 (行为类型定义)
CREATE TABLE behavior_types (
    BehaviorTypeId INT PRIMARY KEY AUTO_INCREMENT,
    Code VARCHAR(50) UNIQUE NOT NULL,     -- 'TASK_COMPLETED', 'TASK_CLAIMED'
    Name VARCHAR(100) NOT NULL,
    Description TEXT,
    DefaultPoints INT DEFAULT 0,
    DefaultXp INT DEFAULT 0,
    DefaultCoins INT DEFAULT 0,
    DefaultDiamonds INT DEFAULT 0,
    IsActive BOOLEAN DEFAULT TRUE
);

-- gamification_logs 表 (游戏化日志)
CREATE TABLE gamification_logs (
    LogId BIGINT PRIMARY KEY AUTO_INCREMENT,
    UserId INT NOT NULL,
    BehaviorCode VARCHAR(50) NOT NULL,
    ReferenceId BIGINT NULL,              -- 关联对象ID (如TaskId)
    PointsGained INT DEFAULT 0,
    XpGained INT DEFAULT 0,
    CoinsGained INT DEFAULT 0,
    DiamondsGained INT DEFAULT 0,
    Description TEXT,
    Context JSON,                         -- 上下文数据
    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

## 🔄 **关键业务流程**

### 1. 任务完成流程
1. **前端**: 用户在任务列表中更改状态为"已完成"
2. **API**: 调用 `PUT /api/v2/tasks/{id}/status` 或 `PATCH /api/v2/tasks/{id}/complete`
3. **服务层**: `TaskService.CompleteTaskAsync()` 执行:
   - 设置 `CompletedByUserId = currentUserId`
   - 设置 `CompletedAt = DateTime.Now`
   - 设置 `Status = "Done"`
   - 调用水印服务设置完成水印
4. **事件**: 发布 `TaskCompletedEvent`
5. **游戏化**: 触发 `TriggerBehaviorRewardAsync(userId, "TASK_COMPLETED", taskId)`
6. **奖励计算**: 根据任务类型、是否按时完成等计算奖励
7. **前端**: 显示奖励弹窗，更新用户统计

### 2. 任务领取流程
1. **前端**: 用户点击"领取"按钮
2. **API**: 调用 `POST /api/v2/tasks/{id}/claim`
3. **服务层**: `TaskClaimService.ClaimTaskAsync()` 执行:
   - 创建 `task_claims` 记录
   - 更新任务分配信息
4. **游戏化**: 触发 `TriggerBehaviorRewardAsync(userId, "TASK_CLAIMED", taskId)`
5. **前端**: 显示"已领取"状态，显示奖励信息

### 3. 统计更新流程
1. **实时更新**: 每次行为触发后立即更新 `user_stats`
2. **批量更新**: 定时任务重新计算统计数据
3. **排行榜**: 定期生成各类排行榜数据
4. **前端展示**: 实时显示个人统计和团队排行

## 🎯 **奖励规则配置**

### 默认奖励标准
- **任务领取**: 3积分 + 2经验 + 0金币 + 0钻石
- **任务完成**: 20积分 + 10经验 + 2金币 + 0钻石
- **按时完成**: 额外1.5倍奖励
- **任务创建**: 5积分 + 3经验 + 1金币 + 0钻石
- **故障报告**: 15积分 + 8经验 + 1金币 + 0钻石

### 统计维度
- **按日统计**: 每日完成、领取、创建任务数
- **按周统计**: 每周完成、领取、创建任务数
- **按月统计**: 每月完成、领取、创建任务数
- **其他模块**: 故障登记、返厂申请、采购申请统计

这个系统已经具备完整的任务完成奖励功能，包括前端界面、后端API、数据库表和业务逻辑！
