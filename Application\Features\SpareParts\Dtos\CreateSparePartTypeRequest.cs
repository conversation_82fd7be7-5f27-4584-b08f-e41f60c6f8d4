// File: Application/Features/SpareParts/Dtos/CreateSparePartTypeRequest.cs
// Description: 备品备件类型创建请求数据传输对象

using System.ComponentModel.DataAnnotations;

namespace ItAssetsSystem.Application.Features.SpareParts.Dtos
{
    /// <summary>
    /// 备品备件类型创建请求数据传输对象
    /// </summary>
    public class CreateSparePartTypeRequest
    {
        /// <summary>
        /// 类型名称
        /// </summary>
        [Required(ErrorMessage = "类型名称不能为空")]
        [StringLength(50, ErrorMessage = "类型名称长度不能超过50个字符")]
        public string Name { get; set; }
        
        /// <summary>
        /// 类型编码
        /// </summary>
        [Required(ErrorMessage = "类型编码不能为空")]
        [StringLength(20, ErrorMessage = "类型编码长度不能超过20个字符")]
        public string Code { get; set; }
        
        /// <summary>
        /// 父类型ID
        /// </summary>
        public long? ParentId { get; set; }
        
        /// <summary>
        /// 描述
        /// </summary>
        [StringLength(200, ErrorMessage = "描述长度不能超过200个字符")]
        public string Description { get; set; }
        
        /// <summary>
        /// 排序号
        /// </summary>
        public int SortOrder { get; set; }
    }
} 