// File: Core/Events/Tasks/TaskCompletedEvent.cs
// Description: 任务完成时发布的领域事件

using MediatR;
using System;
using ItAssetsSystem.Models.Enums; // For CoreTaskTypeEnum

namespace ItAssetsSystem.Core.Events.Tasks
{
    /// <summary>
    /// 表示一个任务已被完成的事件。
    /// </summary>
    public class TaskCompletedEvent : INotification
    {
        /// <summary>
        /// 任务ID (V2 TaskId, BIGINT)
        /// </summary>
        public long TaskId { get; }

        /// <summary>
        /// 任务名称
        /// </summary>
        public string TaskName { get; }

        /// <summary>
        /// 创建者用户ID (Core User Id, INT)
        /// </summary>
        public int CreatorUserId { get; }

        /// <summary>
        /// 负责人用户ID (Core User Id, INT), 可能为空
        /// </summary>
        public int? AssigneeUserId { get; }

        /// <summary>
        /// 完成者用户ID (Core User Id, INT)
        /// </summary>
        public int CompleterUserId { get; }

        /// <summary>
        /// 任务完成时间戳 (来自 V2 Task.ActualEndDate, DateTime)
        /// </summary>
        public DateTime CompletedTimestamp { get; }

        /// <summary>
        /// 任务类型 (来自 Core ItAssetsSystem.Models.Enums.TaskType)
        /// </summary>
        public TaskType CoreTaskType { get; } // Using the enum directly

        /// <summary>
        /// 任务积分 (来自 V2 Task.Points, int)
        /// </summary>
        public int Points { get; }

        public TaskCompletedEvent(
            long taskId,
            string taskName,
            int creatorUserId,
            int? assigneeUserId,
            int completerUserId,
            DateTime completedTimestamp,
            TaskType coreTaskType, // Expecting the enum type directly
            int points)
        {
            TaskId = taskId;
            TaskName = taskName ?? throw new ArgumentNullException(nameof(taskName));
            CreatorUserId = creatorUserId;
            AssigneeUserId = assigneeUserId;
            CompleterUserId = completerUserId;
            CompletedTimestamp = completedTimestamp;
            CoreTaskType = coreTaskType;
            Points = points;
        }
    }
} 