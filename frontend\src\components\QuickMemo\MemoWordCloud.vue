// File: frontend/src/components/QuickMemo/MemoWordCloud.vue
// Description: 随手记词云组件，展示随手记内容的关键词分布。

<template>
  <div class="memo-word-cloud-container" v-loading="isProcessing">
    <div v-if="isProcessing" class="loading-placeholder">
      <el-icon class="is-loading"><Loading /></el-icon>
      <span>生成词云中...</span>
    </div>
    <div v-else-if="words.length === 0" class="empty-placeholder">
      <el-icon><Document /></el-icon>
      <span>暂无词云数据</span>
      <span>随手记多一些内容试试</span>
    </div>
    <div v-else ref="cloudContainerRef" class="word-cloud-wrapper" 
      @mousedown="startDrag" 
      @mouseup="stopDrag" 
      @mouseleave="stopDrag"
      @mousemove="onDrag">
      <canvas ref="canvasRef" class="cloud-canvas"></canvas>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { useQuickMemoStore } from '@/stores/modules/quickMemo'
import { Document, Loading } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import * as THREE from 'three'
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls'

const props = defineProps({
  maxWords: {
    type: Number,
    default: 50
  },
  minFontSize: {
    type: Number,
    default: 14
  },
  maxFontSize: {
    type: Number,
    default: 36
  },
  rotationSpeed: {
    type: Number,
    default: 0.005 // 球体自动旋转速度
  }
})

const quickMemoStore = useQuickMemoStore()
const cloudContainerRef = ref(null)
const canvasRef = ref(null)
const isProcessing = ref(false)
const words = ref([])
const hoveredWord = ref(null)
let scene, camera, renderer, cloudSphere, controls
let isDragging = ref(false)
let animationFrameId = null
let autoRotate = true
let mousePosition = { x: 0, y: 0 }
let raycaster = new THREE.Raycaster()
let mouse = new THREE.Vector2()
let wordObjects = [] // 存储词对象，用于射线检测

// 过滤常用的停用词
const stopWords = [
  '的', '了', '和', '是', '在', '我', '有', '个', '与', '这', '那', '你', '我们', '他们',
  '就', '都', '而', '及', '上', '下', '也', '很', '但', '着', '为', '所', '以', '于',
  '把', '等', '对', '能', '会', '还', '只', '如', '到', '要', '被', '可', '没', '来', '去',
  'a', 'an', 'the', 'and', 'or', 'but', 'if', 'of', 'in', 'on', 'at', 'to', 'for', 'with',
  'by', 'from', 'as', 'it', 'is', 'be', 'was', 'were', 'been', 'this', 'that', 'these', 'those'
]

// 初始化3D场景
const initScene = () => {
  if (!cloudContainerRef.value || !canvasRef.value) return
  
  const container = cloudContainerRef.value
  const width = container.clientWidth
  const height = container.clientHeight
  
  // 创建场景
  scene = new THREE.Scene()
  
  // 创建相机
  camera = new THREE.PerspectiveCamera(60, width / height, 0.1, 1000)
  camera.position.z = 500
  
  // 创建渲染器
  renderer = new THREE.WebGLRenderer({ 
    canvas: canvasRef.value,
    alpha: true, 
    antialias: true 
  })
  renderer.setSize(width, height)
  renderer.setClearColor(0x000000, 0) // 透明背景
  
  // 添加轨道控制
  controls = new OrbitControls(camera, renderer.domElement)
  controls.enableDamping = true // 启用阻尼效果
  controls.dampingFactor = 0.05
  controls.rotateSpeed = 0.5
  controls.enableZoom = true
  controls.enablePan = true
  controls.autoRotate = autoRotate
  controls.autoRotateSpeed = 0.5
  
  // 添加灯光
  const ambientLight = new THREE.AmbientLight(0xffffff, 0.6)
  scene.add(ambientLight)
  
  const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8)
  directionalLight.position.set(0, 1, 1)
  scene.add(directionalLight)
  
  // 创建射线检测器
  raycaster = new THREE.Raycaster()
  mouse = new THREE.Vector2()
  
  // 创建词云球体
  createWordSphere()
  
  // 启动动画循环
  startAnimation()
  
  // 监听窗口大小变化
  window.addEventListener('resize', handleResize)
}

// 从随手记中提取关键词
const extractKeywords = () => {
  isProcessing.value = true
  
  try {
    const memos = quickMemoStore.quickMemos
    
    if (!memos || memos.length === 0) {
      words.value = []
      isProcessing.value = false
      return
    }
    
    // 合并所有随手记文本
    const allText = memos.map(memo => {
      let text = memo.title || ''
      if (memo.content) {
        text += ' ' + memo.content
      }
      return text
    }).join(' ')
    
    // 简单分词：按空格和中文字符分词
    // 注意：实际应用中可能需要更复杂的中文分词算法
    const segments = allText
      .replace(/[^\u4e00-\u9fa5a-zA-Z0-9]/g, ' ') // 只保留中文、英文和数字
      .split(/\s+/)
      .filter(word => word.length > 1)  // 过滤掉单字符
      .filter(word => !stopWords.includes(word.toLowerCase())) // 过滤停用词
    
    // 统计词频
    const wordCount = {}
    segments.forEach(word => {
      wordCount[word] = (wordCount[word] || 0) + 1
    })
    
    // 转换为词云数据格式
    const wordData = Object.keys(wordCount).map(text => ({
      text,
      weight: wordCount[text],
      category: Math.floor(Math.random() * 5), // 随机分类，用于颜色区分
      memoIds: memos.filter(memo => 
        (memo.title && memo.title.includes(text)) || 
        (memo.content && memo.content.includes(text))
      ).map(memo => memo.id),
      highlight: false
    }))
    
    // 按权重排序并限制数量
    wordData.sort((a, b) => b.weight - a.weight)
    words.value = wordData.slice(0, props.maxWords)
    
    // 更新场景
    nextTick(() => {
      if (scene) {
        // 如果场景已存在，则更新球体
        updateWordSphere()
      } else {
        // 否则初始化场景
        initScene()
      }
    })
  } catch (error) {
    console.error('生成词云时出错:', error)
    ElMessage.error('生成词云失败')
  } finally {
    isProcessing.value = false
  }
}

// 创建词云球体
const createWordSphere = () => {
  if (!scene || words.value.length === 0) return
  
  // 清空现有的词对象
  wordObjects = []
  if (cloudSphere) {
    scene.remove(cloudSphere)
  }
  
  // 创建词云球体组
  cloudSphere = new THREE.Group()
  
  // 找到最大和最小权重
  const maxWeight = Math.max(...words.value.map(word => word.weight))
  const minWeight = Math.min(...words.value.map(word => word.weight))
  
  // 球体半径
  const radius = 200
  
  // 使用黄金比创建均匀分布在球面上的点
  const phi = Math.PI * (3 - Math.sqrt(5)) // 黄金角
  
  words.value.forEach((word, i) => {
    // 根据权重计算字体大小
    const fontSizeRange = props.maxFontSize - props.minFontSize
    const weightRatio = (word.weight - minWeight) / (maxWeight - minWeight || 1)
    const fontSize = props.minFontSize + weightRatio * fontSizeRange
    
    // 使用黄金螺旋算法计算球面坐标
    const y = 1 - (i / (words.value.length - 1)) * 2 // -1到1
    const radius_at_y = Math.sqrt(1 - y * y) // 在特定y的圆半径
    const theta = phi * i // 黄金角旋转
    
    const x = Math.cos(theta) * radius_at_y
    const z = Math.sin(theta) * radius_at_y
    
    // 创建文本画布
    const canvas = document.createElement('canvas')
    const context = canvas.getContext('2d')
    
    // 设置画布大小
    const canvasSize = Math.max(fontSize * word.text.length, 100)
    canvas.width = canvasSize
    canvas.height = canvasSize
    
    // 设置字体
    context.font = `${fontSize}px Arial, "Microsoft YaHei", sans-serif`
    context.textAlign = 'center'
    context.textBaseline = 'middle'
    
    // 获取颜色
    const color = getCategoryColor(word.category)
    context.fillStyle = color
    
    // 在画布中心绘制文本
    context.fillText(word.text, canvas.width / 2, canvas.height / 2)
    
    // 创建纹理
    const texture = new THREE.CanvasTexture(canvas)
    texture.needsUpdate = true
    
    // 创建材质
    const material = new THREE.SpriteMaterial({
      map: texture,
      transparent: true
    })
    
    // 创建精灵
    const sprite = new THREE.Sprite(material)
    
    // 设置精灵大小，与权重成正比
    const scale = 0.5 + weightRatio * 0.5
    sprite.scale.set(scale * canvasSize, scale * canvasSize, 1)
    
    // 设置位置
    sprite.position.set(x * radius, y * radius, z * radius)
    
    // 添加用户数据，存储词信息用于交互
    sprite.userData = {
      wordData: word,
      originalPosition: new THREE.Vector3(x * radius, y * radius, z * radius),
      originalScale: new THREE.Vector3().copy(sprite.scale)
    }
    
    // 添加到组和词对象数组
    cloudSphere.add(sprite)
    wordObjects.push(sprite)
    
    // 添加随机微动动画
    animateWord(sprite)
  })
  
  // 添加球体到场景
  scene.add(cloudSphere)
}

// 更新词云球体
const updateWordSphere = () => {
  if (scene) {
    createWordSphere()
  }
}

// 为词添加轻微的随机动画
const animateWord = (wordSprite) => {
  // 为每个词创建一个独特的微动
  const originalPos = wordSprite.userData.originalPosition.clone()
  const amplitude = 0.2 + Math.random() * 0.3 // 随机振幅
  const speed = 0.0005 + Math.random() * 0.001 // 随机速度
  const phase = Math.random() * Math.PI * 2 // 随机相位
  
  // 将动画功能存储在精灵的userData中
  wordSprite.userData.animate = (time) => {
    const x = originalPos.x + Math.sin(time * speed + phase) * amplitude
    const y = originalPos.y + Math.cos(time * speed + phase * 1.3) * amplitude
    const z = originalPos.z + Math.sin(time * speed * 1.2 + phase * 0.7) * amplitude
    
    wordSprite.position.set(x, y, z)
  }
}

// 启动动画循环
const startAnimation = () => {
  let time = 0
  const animate = () => {
    time += 1
    
    // 更新每个词的位置
    wordObjects.forEach(wordSprite => {
      if (wordSprite.userData.animate) {
        wordSprite.userData.animate(time)
      }
    })
    
    // 更新轨道控制
    if (controls) {
      controls.update()
    }
    
    // 渲染场景
    renderer.render(scene, camera)
    
    // 继续动画循环
    animationFrameId = requestAnimationFrame(animate)
  }
  
  // 清理之前的动画
  if (animationFrameId) {
    cancelAnimationFrame(animationFrameId)
  }
  
  // 开始新的动画循环
  animationFrameId = requestAnimationFrame(animate)
}

// 处理窗口大小变化
const handleResize = () => {
  if (!cloudContainerRef.value || !renderer || !camera) return
  
  const width = cloudContainerRef.value.clientWidth
  const height = cloudContainerRef.value.clientHeight
  
  camera.aspect = width / height
  camera.updateProjectionMatrix()
  
  renderer.setSize(width, height)
}

// 开始拖动
const startDrag = (event) => {
  if (!cloudContainerRef.value || !scene || !camera) return
  
  isDragging.value = true
  
  // 获取鼠标位置并进行射线检测
  updateMousePosition(event)
}

// 停止拖动
const stopDrag = () => {
  isDragging.value = false
}

// 拖动处理
const onDrag = (event) => {
  if (!isDragging.value || !scene || !camera || !controls) return
  
  // 用轨道控制器处理拖动旋转
  // (已由OrbitControls自动处理)
  
  // 获取鼠标位置并进行射线检测
  updateMousePosition(event)
}

// 更新鼠标位置并处理射线检测
const updateMousePosition = (event) => {
  if (!cloudContainerRef.value || !camera || !scene) return
  
  const rect = cloudContainerRef.value.getBoundingClientRect()
  mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1
  mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1
  
  // 射线检测
  raycaster.setFromCamera(mouse, camera)
  const intersects = raycaster.intersectObjects(wordObjects)
  
  // 处理鼠标悬停效果
  if (intersects.length > 0) {
    const intersectedObject = intersects[0].object
    const wordData = intersectedObject.userData.wordData
    
    // 设置当前悬停词
    hoveredWord.value = wordData
    
    // 高亮效果
    wordObjects.forEach(obj => {
      if (obj === intersectedObject) {
        // 高亮当前词
        obj.scale.copy(obj.userData.originalScale).multiplyScalar(1.3)
        obj.material.opacity = 1
      } else {
        // 恢复其他词
        obj.scale.copy(obj.userData.originalScale)
        obj.material.opacity = 0.8
      }
    })
    
    // 更改鼠标样式
    cloudContainerRef.value.style.cursor = 'pointer'
  } else {
    // 没有悬停词
    hoveredWord.value = null
    
    // 恢复所有词的效果
    wordObjects.forEach(obj => {
      obj.scale.copy(obj.userData.originalScale)
      obj.material.opacity = 0.8
    })
    
    // 恢复鼠标样式
    cloudContainerRef.value.style.cursor = 'move'
  }
}

// 根据分类获取颜色
const getCategoryColor = (category) => {
  const colors = [
    '#409EFF', // 主题蓝
    '#67C23A', // 成功绿
    '#E6A23C', // 警告黄
    '#F56C6C', // 危险红
    '#909399'  // 信息灰
  ]
  return colors[category % colors.length]
}

// 处理词点击事件
const handleWordClick = (wordData) => {
  if (!wordData) return
  
  // 查找包含该关键词的随手记
  const relatedMemos = quickMemoStore.quickMemos.filter(memo => 
    wordData.memoIds.includes(memo.id)
  )
  
  if (relatedMemos.length > 0) {
    // 打开第一个相关随手记
    quickMemoStore.openMemoDrawer('edit', relatedMemos[0])
    ElMessage.success(`找到${relatedMemos.length}条包含"${wordData.text}"的随手记`)
  } else {
    ElMessage.info(`没有找到包含"${wordData.text}"的随手记`)
  }
}

// 设置轨道控制器自动旋转
const setAutoRotate = (value) => {
  autoRotate = value
  if (controls) {
    controls.autoRotate = value
  }
}

// 鼠标悬停时暂停动画
const handleMouseEnter = () => {
  // 保持手动控制，不停止动画
}

// 鼠标离开时恢复动画
const handleMouseLeave = () => {
  // 恢复自动旋转
  setAutoRotate(true)
}

// 添加点击事件监听
const addClickListener = () => {
  if (!cloudContainerRef.value) return
  
  cloudContainerRef.value.addEventListener('click', (event) => {
    if (hoveredWord.value) {
      handleWordClick(hoveredWord.value)
    }
  })
}

// 监听随手记数据变化
watch(() => quickMemoStore.quickMemos, () => {
  extractKeywords()
}, { deep: true })

// 组件挂载时初始化
onMounted(() => {
  if (quickMemoStore.quickMemos.length === 0) {
    quickMemoStore.fetchQuickMemos().then(() => {
      extractKeywords()
    })
  } else {
    extractKeywords()
  }
  
  // 添加点击事件监听
  nextTick(() => {
    addClickListener()
  })
})

// 组件卸载时清理
onUnmounted(() => {
  // 清理动画
  if (animationFrameId) {
    cancelAnimationFrame(animationFrameId)
  }
  
  // 移除事件监听
  window.removeEventListener('resize', handleResize)
  
  // 释放资源
  if (renderer) {
    renderer.dispose()
  }
  
  if (controls) {
    controls.dispose()
  }
  
  // 清理场景中的对象
  if (scene) {
    scene.traverse((object) => {
      if (object.geometry) {
        object.geometry.dispose()
      }
      
      if (object.material) {
        if (Array.isArray(object.material)) {
          object.material.forEach(material => material.dispose())
        } else {
          object.material.dispose()
        }
      }
    })
  }
})
</script>

<style scoped>
.memo-word-cloud-container {
  width: 100%;
  height: 100%;
  position: relative;
  perspective: 1000px; /* 3D透视效果 */
  overflow: hidden;
}

.word-cloud-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
  transform-style: preserve-3d;
  cursor: move;
  overflow: hidden;
  border-radius: var(--el-border-radius-base);
  background: transparent;
}

.cloud-canvas {
  width: 100%;
  height: 100%;
  display: block;
}

.loading-placeholder,
.empty-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #909399;
  font-size: 14px;
  height: 100%;
}

.loading-placeholder .el-icon,
.empty-placeholder .el-icon {
  font-size: 32px;
  margin-bottom: 10px;
}

.empty-placeholder span + span {
  margin-top: 5px;
  font-size: 12px;
}
</style> 