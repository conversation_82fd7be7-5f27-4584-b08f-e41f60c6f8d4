<template>
  <div class="activity-ticker-container">
    <div class="ticker-icon">
      <el-icon><DataLine /></el-icon>
    </div>
    <div class="ticker-wrap">
      <ul class="ticker-list" :style="{ animationDuration: animationDuration }" ref="tickerListRef">
        <li 
          v-for="item in items" 
          :key="item.id" 
          class="ticker-item"
          @click="handleItemClick(item)"
          @mouseover="pauseAnimation"
          @mouseout="resumeAnimation"
        >
          <span class="item-type" :class="`type-${item.type}`">[{{ itemTypeLabel(item.type) }}]</span>
          <span class="item-content">{{ item.content }}</span>
          <span class="item-time">{{ formatTimeAgo(item.timestamp) }}</span>
        </li>
        <!-- Duplicate list for seamless scroll -->
        <li 
          v-for="item in items" 
          :key="`dup-${item.id}`" 
          class="ticker-item"
          @click="handleItemClick(item)"
          @mouseover="pauseAnimation"
          @mouseout="resumeAnimation"
          aria-hidden="true" 
        >
           <span class="item-type" :class="`type-${item.type}`">[{{ itemTypeLabel(item.type) }}]</span>
          <span class="item-content">{{ item.content }}</span>
          <span class="item-time">{{ formatTimeAgo(item.timestamp) }}</span>
        </li>
      </ul>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { DataLine } from '@element-plus/icons-vue'
import { formatDistanceToNow } from 'date-fns'; // Import date-fns function
import { zhCN } from 'date-fns/locale'; // Import Chinese locale
import { useGamificationStore } from '@/stores/modules/gamification'; // <-- 导入游戏化 Store

const router = useRouter()
const gamificationStore = useGamificationStore() // <-- 使用游戏化 Store

// 使用 Store 中的 recentActivities 替换模拟数据
const items = computed(() => gamificationStore.recentActivities.map(activity => ({
    // Map store activity structure to the structure expected by the template
    id: activity.id,
    type: activity.type || 'notification', // Provide default type
    content: activity.text || '无内容', // Ensure content exists
    timestamp: activity.timestamp,
    relatedId: activity.relatedId // Assuming store provides relatedId if needed
})));

const tickerListRef = ref(null);

const animationDuration = computed(() => {
  // Adjust speed based on number of items
  const itemCount = items.value.length;
  return `${Math.max(5, itemCount * 3)}s`; // Ensure minimum duration, use .value
});

const itemTypeLabel = (type) => {
  // Map activity types from the store to labels
  switch (type) {
    case 'task_assigned': return '新任务';
    case 'task_completed': return '任务完成';
    case 'reward': return '奖励';
    case 'mention': return '＠提及';
    case 'system': return '系统';
    case 'achievement': return '成就达成';
    case 'comment': return '新评论';
    default: return '通知';
  }
};

const formatTimeAgo = (timestamp) => {
    if (!timestamp) return '';
    try {
        const date = new Date(timestamp);
        if (isNaN(date.getTime())) return '无效日期';
        return formatDistanceToNow(date, { addSuffix: true, locale: zhCN });
    } catch (e) {
        return '无效日期';
    }
};

const handleItemClick = (item) => {
  console.log('Clicked item:', item);
  // Update logic based on mapped activity types
  if ((item.type === 'task_assigned' || item.type === 'mention' || item.type === 'task_completed') && item.relatedId) {
       // Assuming task detail route name is 'TaskDetail' (check routes.js)
       // Ensure relatedId is correctly passed from the store activity
       router.push({ name: 'TaskDetail', params: { id: item.relatedId } }).catch(err => {
         console.warn('Navigation failed:', err);
         // Maybe use ElMessage for user feedback
         // ElMessage.error(`无法导航到任务详情，任务ID: ${item.relatedId}`);
       });
  } else if (item.type === 'achievement' && item.relatedId) {
      // Navigate to achievement page or profile section?
      // router.push({ name: 'Achievements' }); 
      console.log('Navigate to achievement details (not implemented)');
  } else {
    // Generic handler or specific handlers for other types
     console.log(`Unhandled ticker item type: ${item.type}`);
    // ElMessage.info(`通知: ${item.content}`); // Use ElMessage instead of alert
  }
};

const pauseAnimation = (event) => {
  const list = event.target.closest('.ticker-list');
  if (list) list.style.animationPlayState = 'paused';
}

const resumeAnimation = (event) => {
   const list = event.target.closest('.ticker-list');
   if (list) list.style.animationPlayState = 'running';
}

// Fetch initial activities when component mounts (if store doesn't handle initial load)
// onMounted(() => {
//   if (gamificationStore.recentActivities.length === 0) {
//       gamificationStore.fetchRecentActivities(); 
//   }
// });

</script>

<style scoped>
/* Use CSS variables for theme consistency */
.activity-ticker-container {
  display: flex;
  align-items: center;
  /* Use a subtle background, maybe derived from primary or info color */
  background-color: var(--primary-color-bg-light, #E0F2F1);
  border-radius: var(--border-radius-base, 6px);
  padding: 6px 12px; /* Slightly reduced padding */
  margin-bottom: var(--content-padding, 20px); /* Consistent margin */
  overflow: hidden;
  border: 1px solid var(--primary-color-light, #B2DFDB);
  box-shadow: var(--shadow-light);
}

.ticker-icon {
  color: var(--primary-color, #00796B);
  margin-right: 10px;
  display: flex;
  align-items: center;
  font-size: 16px; /* Adjusted size */
}

.ticker-wrap {
  flex: 1;
  height: 22px; /* Adjusted height */
  overflow: hidden;
  position: relative;
}

.ticker-list {
  list-style: none;
  padding: 0;
  margin: 0;
  position: absolute;
  width: 100%;
  animation: ticker-scroll linear infinite;
  animation-play-state: running; /* Start running */
}

.ticker-item {
  height: 22px; /* Match the wrap height */
  line-height: 22px;
  font-size: var(--font-size-base, 14px);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
  color: var(--text-color, #263238);
  transition: background-color 0.2s ease;
}

.ticker-item:hover {
   background-color: var(--hover-bg-color, #E0F2F1);
   color: var(--primary-color-dark, #004D40);
}

.item-type {
  font-weight: 600; /* Bold type */
  margin-right: 6px;
}
/* Use theme status colors */
.type-reward { color: var(--accent-color, #F57C00); }
.type-mention { color: var(--danger-color, #E53935); }
.type-task_assigned { color: var(--primary-color, #00796B); }
.type-task_completed { color: var(--success-color, #4CAF50); }
.type-system { color: var(--info-color, #546E7A); }

.item-content {
  margin-right: 15px;
}

.item-time {
  font-size: 12px;
  color: var(--text-color-secondary, #546E7A);
  float: right; /* Align time to the right */
  margin-left: 5px; /* Add some space */
}

@keyframes ticker-scroll {
  0% { transform: translateY(0%); }
  100% { transform: translateY(-50%); } /* Scroll up by half the total height (original + duplicate) */
}

/* Dark theme adjustments */
.dark-theme .activity-ticker-container {
    background-color: var(--sidebar-bg, #37474F);
    border-color: var(--border-color, #455A64);
}
.dark-theme .ticker-icon,
.dark-theme .item-type.type-task_assigned {
    color: var(--primary-color, #4DB6AC); /* Use lighter primary for dark */
}
.dark-theme .ticker-item {
    color: var(--text-color, #ECEFF1);
}
.dark-theme .ticker-item:hover {
   background-color: var(--hover-bg-color, #455A64);
   color: var(--text-color, #ECEFF1);
}
.dark-theme .item-time {
    color: var(--text-color-secondary, #B0BEC5);
}
.dark-theme .type-reward { color: var(--accent-color, #FFB74D); }
.dark-theme .type-mention { color: var(--danger-color, #E57373); }
.dark-theme .type-task_completed { color: var(--success-color, #81C784); }
.dark-theme .type-system { color: var(--info-color, #90A4AE); }

</style> 