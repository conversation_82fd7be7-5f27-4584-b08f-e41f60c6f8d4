// IT资产管理系统 - 服务集合扩展
// 文件路径: /Core/Extensions/ServiceCollectionExtensions.cs
// 功能: 提供服务集合扩展方法用于注册核心服务

using Microsoft.Extensions.DependencyInjection;
using ItAssetsSystem.Core.Events;
using ItAssetsSystem.Core.Abstractions;
using ItAssetsSystem.Core.Plugins;
using Microsoft.Extensions.Configuration;
using System;
using System.Linq;
using System.Reflection;
using Microsoft.Extensions.Logging;

namespace ItAssetsSystem.Core.Extensions
{
    /// <summary>
    /// 服务集合扩展方法
    /// </summary>
    public static class ServiceCollectionExtensions
    {
        /// <summary>
        /// 添加核心服务
        /// </summary>
        /// <param name="services">服务集合</param>
        /// <returns>服务集合</returns>
        public static IServiceCollection AddCoreServices(this IServiceCollection services)
        {
            // 添加事件总线
            services.AddEventBus();

            // 添加插件管理器
            services.AddPluginManager();

            return services;
        }

        /// <summary>
        /// 添加事件总线服务
        /// </summary>
        /// <param name="services">服务集合</param>
        /// <returns>服务集合</returns>
        public static IServiceCollection AddEventBus(this IServiceCollection services)
        {
            services.AddSingleton<IEventBus, EventBus>();
            return services;
        }

        /// <summary>
        /// 添加插件管理器服务
        /// </summary>
        /// <param name="services">服务集合</param>
        /// <returns>服务集合</returns>
        public static IServiceCollection AddPluginManager(this IServiceCollection services)
        {
            // 注册插件管理器为单例服务
            services.AddSingleton<ItAssetsSystem.Core.Abstractions.IPluginManager>(sp =>
            {
                var logger = sp.GetRequiredService<ILogger<ItAssetsSystem.Core.Plugins.PluginManager>>();
                var serviceProvider = sp.GetRequiredService<IServiceProvider>();
                return new ItAssetsSystem.Core.Plugins.PluginManager(logger, serviceProvider, services);
            });
            return services;
        }

        /// <summary>
        /// 添加插件支持
        /// </summary>
        /// <param name="services">服务集合</param>
        /// <param name="configuration">配置</param>
        /// <returns>服务集合</returns>
        public static IServiceCollection AddPlugins(this IServiceCollection services, IConfiguration configuration)
        {
            // 注册所有内置插件
            var assembly = Assembly.GetExecutingAssembly();
            var pluginTypes = assembly.GetTypes()
                .Where(t => typeof(IPlugin).IsAssignableFrom(t) && !t.IsAbstract && t != typeof(PluginBase))
                .ToArray();

            foreach (var pluginType in pluginTypes)
            {
                services.AddTransient(pluginType);
            }
            
            return services;
        }
    }
}

// 计划行数: 50
// 实际行数: 50 