import request from '@/utils/request'

// 周期性任务计划API
export const periodicTaskApi = {
  // 获取周期性任务计划列表
  getPeriodicSchedules(params = {}) {
    return request({
      url: '/v2/periodic-schedules',
      method: 'get',
      params
    })
  },

  // 获取指定周期性任务计划详情
  getPeriodicSchedule(id) {
    return request({
      url: `/v2/periodic-schedules/${id}`,
      method: 'get'
    })
  },

  // 创建周期性任务计划
  createPeriodicSchedule(data) {
    return request({
      url: '/v2/periodic-schedules',
      method: 'post',
      data
    })
  },

  // 更新周期性任务计划
  updatePeriodicSchedule(id, data) {
    return request({
      url: `/v2/periodic-schedules/${id}`,
      method: 'put',
      data
    })
  },

  // 删除周期性任务计划
  deletePeriodicSchedule(id) {
    return request({
      url: `/v2/periodic-schedules/${id}`,
      method: 'delete'
    })
  },

  // 启用/禁用周期性任务计划
  enablePeriodicSchedule(id, isEnabled) {
    return request({
      url: `/v2/periodic-schedules/${id}/enable`,
      method: 'patch',
      params: {
        isEnabled
      }
    })
  }
}

export default periodicTaskApi