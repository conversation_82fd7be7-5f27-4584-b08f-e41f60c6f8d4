// File: Infrastructure/Data/Repositories/SparePartLocationRepository.cs
// Description: 备品备件库位仓储实现类

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ItAssetsSystem.Core.Abstractions;
using ItAssetsSystem.Domain.Entities;
using ItAssetsSystem.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;

namespace ItAssetsSystem.Infrastructure.Data.Repositories
{
    /// <summary>
    /// 备品备件库位仓储实现类
    /// </summary>
    public class SparePartLocationRepository : ISparePartLocationRepository
    {
        private readonly AppDbContext _dbContext;
        
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="dbContext">数据库上下文</param>
        public SparePartLocationRepository(AppDbContext dbContext)
        {
            _dbContext = dbContext;
        }
        
        /// <summary>
        /// 获取所有库位
        /// </summary>
        /// <returns>库位列表</returns>
        public async Task<List<SparePartLocation>> GetAllAsync()
        {
            return await _dbContext.SparePartLocations
                .Where(l => l.IsActive)
                .OrderBy(l => l.Area)
                .ThenBy(l => l.Name)
                .ToListAsync();
        }
        
        /// <summary>
        /// 根据区域获取库位
        /// </summary>
        /// <param name="area">区域标识</param>
        /// <returns>库位列表</returns>
        public async Task<List<SparePartLocation>> GetByAreaAsync(string area)
        {
            return await _dbContext.SparePartLocations
                .Where(l => l.IsActive && l.Area == area)
                .OrderBy(l => l.Name)
                .ToListAsync();
        }
        
        /// <summary>
        /// 根据ID获取库位
        /// </summary>
        /// <param name="id">库位ID</param>
        /// <returns>库位实体</returns>
        public async Task<SparePartLocation> GetByIdAsync(long id)
        {
            return await _dbContext.SparePartLocations
                .FirstOrDefaultAsync(l => l.Id == id);
        }
        
        /// <summary>
        /// 创建库位
        /// </summary>
        /// <param name="entity">库位实体</param>
        /// <returns>创建的库位实体</returns>
        public async Task<SparePartLocation> CreateAsync(SparePartLocation entity)
        {
            entity.CreatedAt = DateTime.Now;
            entity.UpdatedAt = DateTime.Now;
            
            await _dbContext.SparePartLocations.AddAsync(entity);
            await _dbContext.SaveChangesAsync();
            
            return entity;
        }
        
        /// <summary>
        /// 更新库位
        /// </summary>
        /// <param name="entity">库位实体</param>
        /// <returns>更新后的库位实体</returns>
        public async Task<SparePartLocation> UpdateAsync(SparePartLocation entity)
        {
            entity.UpdatedAt = DateTime.Now;
            
            var existingEntity = await _dbContext.SparePartLocations.FindAsync(entity.Id);
            if (existingEntity == null)
            {
                return null;
            }
            
            _dbContext.Entry(existingEntity).CurrentValues.SetValues(entity);
            
            await _dbContext.SaveChangesAsync();
            
            return existingEntity;
        }
        
        /// <summary>
        /// 删除库位
        /// </summary>
        /// <param name="id">库位ID</param>
        /// <returns>是否成功</returns>
        public async Task<bool> DeleteAsync(long id)
        {
            using var transaction = await _dbContext.Database.BeginTransactionAsync();
            
            try
            {
                // 检查是否有关联的备件
                var hasRelatedParts = await _dbContext.SpareParts
                    .AnyAsync(p => p.LocationId == id);
                
                if (hasRelatedParts)
                {
                    throw new InvalidOperationException("不能删除有关联备件的库位");
                }
                
                // 检查是否有关联的出入库记录
                var hasTransactions = await _dbContext.SparePartTransactions
                    .AnyAsync(t => t.LocationId == id);
                
                if (hasTransactions)
                {
                    throw new InvalidOperationException("不能删除有关联出入库记录的库位");
                }
                
                var entity = await _dbContext.SparePartLocations.FindAsync(id);
                if (entity == null)
                {
                    return false;
                }
                
                _dbContext.SparePartLocations.Remove(entity);
                await _dbContext.SaveChangesAsync();
                
                await transaction.CommitAsync();
                
                return true;
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }
        }
        
        /// <summary>
        /// 检查库位编码是否已存在
        /// </summary>
        /// <param name="code">编码</param>
        /// <param name="excludeId">排除的ID</param>
        /// <returns>是否存在</returns>
        public async Task<bool> IsCodeExistsAsync(string code, long? excludeId = null)
        {
            var query = _dbContext.SparePartLocations.Where(l => l.Code == code);
            
            if (excludeId.HasValue)
            {
                query = query.Where(l => l.Id != excludeId.Value);
            }
            
            return await query.AnyAsync();
        }
        
        /// <summary>
        /// 获取所有区域列表
        /// </summary>
        /// <returns>区域标识列表</returns>
        public async Task<List<string>> GetAllAreasAsync()
        {
            return await _dbContext.SparePartLocations
                .Where(l => l.IsActive)
                .Select(l => l.Area)
                .Distinct()
                .OrderBy(a => a)
                .ToListAsync();
        }
    }
} 