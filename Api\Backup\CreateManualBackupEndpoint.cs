// IT资产管理系统 - 创建手动备份API
// 文件路径: /Api/Backup/CreateManualBackupEndpoint.cs
// 功能: 创建手动备份的API端点

using System;
using System.Threading.Tasks;
using ItAssetsSystem.Core.Backup;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace ItAssetsSystem.Api.Backup
{
    [ApiController]
    [Route("api/backup/create")]
    public class CreateManualBackupEndpoint : ControllerBase
    {
        private readonly IBackupService _backupService;
        private readonly ILogger<CreateManualBackupEndpoint> _logger;

        public CreateManualBackupEndpoint(
            IBackupService backupService,
            ILogger<CreateManualBackupEndpoint> logger)
        {
            _backupService = backupService;
            _logger = logger;
        }

        public class CreateBackupRequest
        {
            public string Name { get; set; }
            public string Description { get; set; }
        }

        public class CreateBackupResponse
        {
            public string BackupId { get; set; }
            public bool Success { get; set; }
            public string Message { get; set; }
        }

        [HttpPost]
        public async Task<IActionResult> CreateBackup([FromBody] CreateBackupRequest request)
        {
            try
            {
                _logger.LogInformation("正在创建手动备份: {Name}", request.Name);
                
                if (string.IsNullOrWhiteSpace(request.Name))
                {
                    return BadRequest(new { Success = false, Message = "备份名称不能为空" });
                }
                
                // 创建手动备份
                var backup = await _backupService.CreateManualBackupAsync(
                    request.Name, 
                    request.Description ?? "手动创建的备份");
                
                return Ok(new
                {
                    Success = true,
                    Message = "备份创建成功",
                    BackupId = backup.Id,
                    CreatedAt = backup.CreatedAt
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建备份失败");
                return StatusCode(500, new { Success = false, Message = "创建备份失败" });
            }
        }
    }
}

// 计划行数: 25
// 实际行数: 25 