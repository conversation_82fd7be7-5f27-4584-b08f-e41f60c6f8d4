import{_ as a,r as l,c as e,z as s,b as t,d as i,e as o,w as n,t as c,$ as d,F as u,h as r,a as v,o as f,f as p,ak as m,K as y,p as h,D as b,v as _,k,al as w,aA as g,ah as x,S as z,n as C,i as V}from"./index-CG5lHOPO.js";const I={class:"factory-dashboard"},N={class:"dashboard-header"},S={class:"max-w-7xl mx-auto flex flex-wrap items-center justify-between gap-2"},U={class:"flex items-center"},j={class:"header-icon"},M={class:"header-title"},O={class:"update-badge"},D={class:"font-medium"},F={class:"header-controls"},J={key:0,class:"file-import-section"},L={key:0,class:"imported-file-name"},B={class:"max-w-7xl mx-auto p-3 flex-grow"},P={key:0,class:"loading-container"},T={key:1,class:"dashboard-main"},$={class:"stats-panel"},A={class:"card-header"},K={class:"update-time"},R={class:"stats-grid"},q={class:"stat-item operational"},E={class:"stat-icon"},G={class:"stat-content"},H={class:"stat-value"},Q={class:"stat-percent"},W={class:"stat-item warning"},X={class:"stat-icon"},Y={class:"stat-content"},Z={class:"stat-value"},aa={class:"stat-item error"},la={class:"stat-icon"},ea={class:"stat-content"},sa={class:"stat-value"},ta={class:"stat-item idle"},ia={class:"stat-icon"},oa={class:"stat-content"},na={class:"stat-value"},ca={class:"efficiency-summary"},da={class:"efficiency-item"},ua={class:"efficiency-value"},ra={class:"efficiency-item"},va={class:"efficiency-value"},fa={class:"factory-layout"},pa={class:"layout-header"},ma={class:"header-controls"},ya={key:0,class:"factory-floor",ref:"factoryFloor"},ha=["viewBox"],ba={class:"zone-containers"},_a=["data-zone"],ka=["onClick","onMouseenter"],wa={class:"cell-content"},ga={key:3,class:"cell-number"},xa={key:0,class:"fault-badge"},za={key:1,class:"hover-tooltip"},Ca={key:1,class:"factory-list"},Va=a({__name:"FactoryLayoutDashboardTest",setup(a){const Va=l([]);l(null);const Ia=l(null),Na=l(""),Sa=l(!1),Ua=l(new Date),ja=l(!1),Ma=l("layout"),Oa=l(null),Da=l(!0),Fa=l("import"),Ja=l("");l(null);const La=l(null),Ba=()=>{},Pa=()=>{},Ta=()=>{},$a=()=>{},Aa=()=>{},Ka=()=>{},Ra=e((()=>Va.value)),qa=e((()=>({total:0,operational:0,warning:0,error:0,idle:0,operationalPercent:0}))),Ea=e((()=>Ua.value.toLocaleTimeString("zh-CN"))),Ga=e((()=>0)),Ha=e((()=>0)),Qa=e((()=>"0 0 1200 500"));return s((()=>{Da.value=!1})),(a,l)=>{var e;const s=v("el-icon"),Va=v("el-option"),Ua=v("el-select"),Wa=v("el-button"),Xa=v("el-upload"),Ya=v("el-input"),Za=v("el-loading-spinner"),al=v("el-card"),ll=v("el-table-column"),el=v("el-tag"),sl=v("el-table");return f(),t("div",I,[i("header",N,[i("div",S,[i("div",U,[i("div",j,[o(s,{size:"32"},{default:n((()=>[o(p(m))])),_:1})]),i("div",M,[l[3]||(l[3]=i("h1",null,"智能制造监控系统",-1)),i("p",null,"实时工厂状态监控 • "+c(qa.value.total)+"个工位",1)]),i("div",O,[l[4]||(l[4]=i("span",{class:"text-gray-400"},"最后更新: ",-1)),i("span",D,c(Ea.value),1)])]),i("div",F,[o(Ua,{modelValue:Fa.value,"onUpdate:modelValue":l[0]||(l[0]=a=>Fa.value=a),onChange:Ba,placeholder:"选择布局配置",class:"config-selector",size:"small"},{default:n((()=>[o(Va,{label:"默认布局",value:"default"}),o(Va,{label:"自定义布局",value:"custom"}),o(Va,{label:"导入JSON文件",value:"import"})])),_:1},8,["modelValue"]),"import"===Fa.value?(f(),t("div",J,[o(Xa,{ref_key:"uploadRef",ref:La,"auto-upload":!1,"show-file-list":!1,accept:".json","on-change":Pa,class:"json-uploader"},{default:n((()=>[o(Wa,{type:"info",size:"small",icon:p(y)},{default:n((()=>l[5]||(l[5]=[h("选择JSON")]))),_:1},8,["icon"])])),_:1},512),o(Wa,{type:"success",size:"small",onClick:Ta,title:"加载默认JSON文件"},{default:n((()=>l[6]||(l[6]=[h(" 加载默认 ")]))),_:1}),Ja.value?(f(),t("span",L,c(Ja.value),1)):d("",!0)])):d("",!0),o(Ya,{modelValue:Na.value,"onUpdate:modelValue":l[1]||(l[1]=a=>Na.value=a),placeholder:"搜索工位...",class:"search-input",clearable:""},{prefix:n((()=>[o(s,null,{default:n((()=>[o(p(b))])),_:1})])),_:1},8,["modelValue"]),o(Wa,{onClick:$a,loading:ja.value,size:"small",circle:"",title:"刷新数据"},{default:n((()=>[o(s,null,{default:n((()=>[o(p(_))])),_:1})])),_:1},8,["loading"]),o(Wa,{onClick:Aa,size:"small",circle:"",title:Sa.value?"退出全屏":"全屏"},{default:n((()=>[Sa.value?(f(),k(s,{key:0},{default:n((()=>[o(p(w))])),_:1})):(f(),k(s,{key:1},{default:n((()=>[o(p(g))])),_:1}))])),_:1},8,["title"])])])]),i("main",B,[Da.value?(f(),t("div",P,[o(Za,{text:"正在加载工厂布局配置..."})])):(f(),t("div",T,[i("div",$,[o(al,{class:"stats-card"},{header:n((()=>[i("div",A,[l[7]||(l[7]=i("span",null,"实时概览",-1)),i("span",K,c(Ea.value),1)])])),default:n((()=>[i("div",R,[i("div",q,[i("div",E,[o(s,null,{default:n((()=>[o(p(x))])),_:1})]),i("div",G,[i("div",H,c(qa.value.operational),1),l[8]||(l[8]=i("div",{class:"stat-label"},"运行正常",-1)),i("div",Q,c(qa.value.operationalPercent)+"%",1)])]),i("div",W,[i("div",X,[o(s,null,{default:n((()=>[o(p(z))])),_:1})]),i("div",Y,[i("div",Z,c(qa.value.warning),1),l[9]||(l[9]=i("div",{class:"stat-label"},"警告状态",-1))])]),i("div",aa,[i("div",la,[o(s,null,{default:n((()=>[o(p(w))])),_:1})]),i("div",ea,[i("div",sa,c(qa.value.error),1),l[10]||(l[10]=i("div",{class:"stat-label"},"错误状态",-1))])]),i("div",ta,[i("div",ia,[o(s,null,{default:n((()=>[o(p(y))])),_:1})]),i("div",oa,[i("div",na,c(qa.value.idle),1),l[11]||(l[11]=i("div",{class:"stat-label"},"空闲工位",-1))])])]),i("div",ca,[i("div",da,[i("div",ua,c(Ga.value)+"%",1),l[12]||(l[12]=i("div",{class:"efficiency-label"},"平均效率",-1))]),i("div",ra,[i("div",va,c(Ha.value),1),l[13]||(l[13]=i("div",{class:"efficiency-label"},"总设备数",-1))])])])),_:1})]),i("div",fa,[i("div",pa,[i("span",null,"显示: "+c(Ra.value.length)+" / "+c(qa.value.total),1),i("div",ma,[o(Ua,{modelValue:Ma.value,"onUpdate:modelValue":l[2]||(l[2]=a=>Ma.value=a),class:"view-selector"},{default:n((()=>[o(Va,{label:"布局视图",value:"layout"}),o(Va,{label:"列表视图",value:"list"})])),_:1},8,["modelValue"])])]),"layout"===Ma.value?(f(),t("div",ya,[(f(),t("svg",{class:"factory-grid",viewBox:Qa.value},l[14]||(l[14]=[i("defs",null,[i("pattern",{id:"grid",width:"20",height:"20",patternUnits:"userSpaceOnUse"},[i("path",{d:"M 20 0 L 0 0 0 20",fill:"none",stroke:"rgba(107, 148, 214, 0.08)","stroke-width":"1"})])],-1),i("rect",{width:"100%",height:"100%",fill:"url(#grid)"},null,-1)]),8,ha)),i("div",ba,[(f(!0),t(u,null,r((null==(e=Oa.value)?void 0:e.zones)||[],(a=>(f(),t("div",{key:a.id,class:V(["zone-container",`zone-${a.id}`]),"data-zone":a.name,style:C({})},[i("div",{class:"zone-workstations",style:C({})},[(f(!0),t(u,null,r((a.name,[]),(a=>(f(),t("div",{key:a.locationId,class:V(["workstation-cell",[]]),onClick:l=>{a.locationId},onMouseenter:l=>{a.locationId},onMouseleave:Ka},[i("div",wa,["error"===a.status?(f(),k(s,{key:0,class:"status-icon error"},{default:n((()=>[o(p(w))])),_:1})):"warning"===a.status?(f(),k(s,{key:1,class:"status-icon warning"},{default:n((()=>[o(p(z))])),_:1})):"operational"===a.status?(f(),k(s,{key:2,class:"status-icon operational"},{default:n((()=>[o(p(x))])),_:1})):(f(),t("span",ga,c(a.locationId),1))]),a.faultCount>0?(f(),t("div",xa,c(a.faultCount),1)):d("",!0),Ia.value===a.locationId?(f(),t("div",za,c(a.locationName)+" - "+c(a.efficiency)+"% ",1)):d("",!0)],42,ka)))),128))],4)],14,_a)))),128))])],512)):(f(),t("div",Ca,[o(sl,{data:Ra.value,height:"100%"},{default:n((()=>[o(ll,{prop:"locationCode",label:"工位编号",width:"100"}),o(ll,{prop:"locationName",label:"工位名称"}),o(ll,{prop:"departmentName",label:"所属部门"}),o(ll,{prop:"efficiency",label:"效率",width:"80"},{default:n((({row:a})=>[i("span",null,c(a.efficiency)+"%",1)])),_:1}),o(ll,{prop:"status",label:"状态",width:"100"},{default:n((({row:a})=>[o(el,{type:(a.status,"info")},{default:n((()=>[h(c((a.status,"正常")),1)])),_:2},1032,["type"])])),_:1}),o(ll,{prop:"assetCount",label:"设备数",width:"80"}),o(ll,{prop:"taskCount",label:"任务数",width:"80"}),o(ll,{label:"操作",width:"100"},{default:n((({row:a})=>[o(Wa,{size:"small",onClick:l=>{a.locationId}},{default:n((()=>l[15]||(l[15]=[h(" 详情 ")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"])]))])]))])])}}},[["__scopeId","data-v-d09b92e2"]]);export{Va as default};
