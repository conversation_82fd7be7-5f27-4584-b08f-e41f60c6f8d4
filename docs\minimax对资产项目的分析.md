

# IT资产管理系统技术文档

## 一、项目概述
**项目名称**: 航空航天级IT资产管理系统  
**当前版本**: 1.0.0  
**技术栈**:  
- 后端: .NET Core 6.0 + Entity Framework Core  
- 前端: Vue 3 + Element Plus  
- 数据库: MySQL 8.0+  
- 实时通信: SignalR  
- 插件架构: .NET Core插件系统  

## 二、系统架构图
```text
                         .................
:                                                :
:  [前端] --> [API Gateway] --> [后端服务层]    :
:    │              │               │           :
:    ├─任务管理    ├─认证鉴权      ├─资产服务    :
:    ├─资产统计    ├─版本控制      ├─任务服务    :
:    ├─位置管理    └─性能监控      └─位置服务    :
:    └─通知中心                                :
:                                                :
:  [插件系统] <--> [事件总线] <--> [数据库]      :
:                                                :
'................................' 
```

## 三、核心模块详解

### 3.1 任务管理系统 (V2)

#### 3.1.1 功能特性
- ✅ 多负责人任务分配（主负责人+协作者）
- ✅ 智能过滤器（状态/优先级/负责人/日期/关键词）
- ✅ 批量操作（状态修改/分配/删除）
- ✅ 实时统计卡片（总任务/进行中/已完成/已逾期）
- ✅ 任务进度追踪（0-100%）
- ✅ 周期任务支持（Cron表达式）
- ✅ 游戏化任务（积分/徽章/排行榜）

#### 3.1.2 关键表结构
```sql
CREATE TABLE tasks (
  TaskId BIGINT PRIMARY KEY AUTO_INCREMENT,
  Name VARCHAR(255) NOT NULL,
  AssigneeUserId INT,        -- 主负责人（INT）
  TaskType VARCHAR(50),      -- Normal/Periodic/PDCA
  Status VARCHAR(50),        -- Todo/InProgress/Done
  Progress INT DEFAULT 0,
  PlanEndDate DATETIME,
  IsOverdueAcknowledged TINYINT(1)
);

CREATE TABLE taskassignees (
  TaskAssigneeId BIGINT PRIMARY KEY AUTO_INCREMENT,
  TaskId BIGINT,             -- 关联tasks.TaskId
  UserId INT,                -- 协作者ID (INT)
  AssignmentType VARCHAR(50) -- Responsible/Participant
);
```

#### 3.1.3 关键API
```csharp
// TasksController.cs (V2)
[HttpGet]
public async Task<IActionResult> GetTasks([FromQuery] TaskQueryParametersDto parameters) 
{ /* ... */ }

[HttpPost]
public async Task<IActionResult> CreateTask([FromBody] CreateTaskRequestDto request) 
{ /* ... */ }

[HttpPost("{id}/assign")]
public async Task<IActionResult> AssignTask(long id, [FromBody] AssignTaskRequest request) 
{ /* ... */ }
```

### 3.2 资产统计系统 (V2)

#### 3.2.1 功能特性
- ✅ 多维度统计（类型/部门/区域/状态）
- ✅ 趋势分析（周/日/月）
- ✅ 资产价值分布
- ✅ 设备状态热力图
- ✅ 实时数据同步

#### 3.2.2 关键视图
```sql
CREATE VIEW v_asset_statistics_fast AS
SELECT 
  'type' AS DimensionType,
  cast(AssetTypeId as CHAR) AS DimensionKey,
  AssetTypeName AS DimensionName,
  COUNT(*) AS TotalCount,
  SUM(IsInUse) AS InUseCount
FROM v_assets_enhanced
GROUP BY AssetTypeId, AssetTypeName;
```

#### 3.2.3 关键API
```csharp
// AssetStatisticsController.cs
[HttpGet("analytics-workbench")]
public async Task<IActionResult> GetAnalyticsWorkbenchData()
{
  // 组合KPI、类型统计、区域统计、部门统计
  return Ok(new {
    KpiData = await GetKpiData(),
    TypeStatistics = await GetTypeStats(),
    RegionStatistics = await GetRegionStats()
  });
}
```

### 3.3 位置管理系统

#### 3.3.1 功能特性
- ✅ 树形位置结构（工厂→产线→工序→工位）
- ✅ 位置属性配置（行/列/间距/颜色）
- ✅ 关联资产/部门/人员
- ✅ 批量导入/导出布局

#### 3.3.2 关键表结构
```sql
CREATE TABLE locations (
  Id INT PRIMARY KEY AUTO_INCREMENT,
  Code VARCHAR(50) UNIQUE,
  Name VARCHAR(100) NOT NULL,
  Type INT DEFAULT 0,      -- 0=厂区,1=产线,2=工序,3=工位
  Path VARCHAR(200),       -- 层级路径: 0,1,5,12
  DefaultDepartmentId INT  -- 关联departments.Id
);
```

#### 3.3.3 关键API
```csharp
// LocationController.cs
[HttpGet("tree")]
public async Task<IActionResult> GetTree() 
{ /* 返回树形结构 */ }

[HttpPost("{locationId}/assets")]
public async Task<IActionResult> RelateAssets(int locationId, [FromBody] LocationAssetRelateModel model) 
{ /* 关联资产 */ }
```

### 3.4 资产管理系统

#### 3.4.1 功能特性
- ✅ 资产全生命周期管理
- ✅ 多条件查询（类型/状态/位置/部门）
- ✅ 批量导入/导出（Excel/CSV/JSON）
- ✅ 资产位置变更历史
- ✅ 资产标签打印

#### 3.4.2 关键表结构
```sql
CREATE TABLE assets (
  Id INT PRIMARY KEY AUTO_INCREMENT,
  assetCode VARCHAR(30) UNIQUE,  -- IT-***********-001
  AssetTypeId INT NOT NULL,      -- 关联assettypes
  LocationId INT,                -- 关联locations
  Status INT DEFAULT 0           -- 0=闲置,1=在用,2=维修,3=报废
);

CREATE TABLE locationhistories (
  Id INT PRIMARY KEY AUTO_INCREMENT,
  AssetId INT NOT NULL,          -- 关联assets.Id
  OldLocationId INT,
  NewLocationId INT NOT NULL,    -- 关联locations.Id
  ChangeTime DATETIME
);
```

#### 3.4.3 关键API
```csharp
// AssetController.cs
[HttpGet]
public async Task<IActionResult> GetAssets([FromQuery] AssetQueryDto query) 
{ /* ... */ }

[HttpPost("export")]
public IActionResult Export() 
{ /* 生成Excel */ }
```

## 四、冗余代码分析

### 4.1 API版本冗余
| 冗余模块        | 问题描述                               | 处理建议                  |
|----------------|---------------------------------------|-------------------------|
| Api/V1_1/AssetController.cs | 与V1版本功能重叠，未标注弃用           | 标记[Obsolete]，仅保留V2 |
| Api/V1_1/TaskController.cs  | 与V1版本功能重叠，未标注弃用           | 标记[Obsolete]，仅保留V2 |
| Api/V1_1/UserController.cs  | 与V1版本功能重叠，未标注弃用           | 标记[Obsolete]，仅保留V2 |

### 4.2 视图冗余
| 冗余视图               | 问题描述                               | 处理建议              |
|-----------------------|---------------------------------------|---------------------|
| v_asset_kpi_enhanced  | 计算逻辑重复，未被实际使用              | 删除                 |
| view_performance_stats | 性能统计表，无实际使用场景               | 删除                 |
| user_actions          | 积分相关表，但功能未完全实现             | 注释或删除           |

### 4.3 前端冗余
| 冗余文件                | 问题描述                               | 处理建议              |
|------------------------|---------------------------------------|---------------------|
| src/api/taskEnhanced.js | 与src/api/task.js 功能重复              | 合并或删除           |
| src/components/ABTestManager.vue | 功能未完整实现，代码不完整             | 完成或移除           |
| src/components/FactoryLayoutDesignerbeifen.vue | 重复设计器文件        | 保留最新版，删除备份 |

### 4.4 插件冗余
| 冗余插件                 | 问题描述                               | 处理建议              |
|------------------------|---------------------------------------|---------------------|
| Core/Plugins/AuditLogPlugin.cs | 未注册事件监听，无实际作用             | 完善或移除           |
| Core/Plugins/PurchaseManagementPlugin.cs | 仅实现部分功能，未完成   | 完成或移除           |

## 五、无效功能清单

### 5.1 任务系统
- **任务评论@提及功能**: 代码存在但未实际触发通知
- **任务附件预览**: AttachmentFileDto缺少预览URL字段
- **任务统计图表**: TaskAnalyticsDashboard.vue未正确绑定数据

### 5.2 资产系统
- **资产折旧计算**: 界面存在但后端无对应API
- **资产预测模型**: 仅前端占位，无后端逻辑
- **资产二维码生成**: QRCodeGenerator未实现

### 5.3 位置系统
- **位置热力图**: factoryAreas数据未更新最新配置
- **位置权限控制**: 仅界面控制，无后端验证

## 六、上手操作指南

### 6.1 开发环境搭建
```bash
# 后端
dotnet restore
dotnet run --project ItAssetsSystem.Api

# 前端
cd frontend
npm install
npm run dev
```

### 6.2 关键配置
```json
// appsettings.json
{
  "ConnectionStrings": {
    "DefaultConnection": "server=localhost;port=3306;database=itassets;user=root;password=root;charset=utf8mb4"
  },
  "SystemOptions": {
    "SystemName": "IT资产管理系统",
    "LocationTemplateFile": "Configurations/location-templates.json"
  }
}
```

### 6.3 数据库初始化
```sql
-- 执行初始化脚本
mysql -u root -p itassets < 20250620itassets数据库.txt
```

## 七、优化建议

### 7.1 代码规范
- 统一API版本为v2，移除v1/v1.1冗余代码
- 使用DTO自动映射（如AutoMapper）替代手动映射
- 统一异常处理（全局ExceptionFilter）

### 7.2 架构优化
- 拆分大型Controller（如AssetStatisticsController > 5k行）
- 使用CQRS模式分离查询与命令
- 引入Hangfire实现后台任务调度

### 7.3 功能补全
- 完成游戏化任务系统积分计算
- 实现资产折旧算法
- 完善位置权限控制

## 八、核心流程图

### 8.1 任务创建流程
```text
用户提交 → [TaskController.CreateTask()] 
→ 验证数据 → 生成TaskId → 记录TaskCreatedEvent 
→ [TaskService.CreateAsync()] → 插入DB 
→ 触发实时通知 → 返回结果
```

### 8.2 资产位置变更流程
```text
用户提交 → [LocationController.SetLocationDepartment()] 
→ 验证位置存在 → 更新Location.DefaultDepartmentId 
→ 创建LocationHistory记录 
→ 触发AssetAssignedEvent → 更新资产关联
```

## 九、典型问题排查

### 9.1 任务无法分配
```csharp
// 检查TaskAssignees表是否关联正确
var assignees = await _context.TaskAssignees
    .Where(ta => ta.TaskId == taskId)
    .ToListAsync();
```

### 9.2 资产统计不更新
```sql
-- 检查统计视图是否最新
SELECT * FROM v_asset_statistics_fast LIMIT 10;
```

### 9.3 位置树加载缓慢
```csharp
// 检查递归查询性能
var locations = await _context.Locations
    .OrderBy(l => l.Id)
    .ToListAsync();
```

## 十、扩展阅读
- [Element Plus组件文档](https://element-plus.gitee.io/zh-CN/)
- [EF Core性能优化](https://learn.microsoft.com/zh-cn/ef/core/performance/)
- [SignalR实时通信](https://learn.microsoft.com/zh-cn/aspnet/core/signalr)

---
**文档版本**: 1.0  
**最后更新**: 2025-06-21  
**维护人员**: 技术架构组