<template>
  <div class="leaderboard-view page-container">
    <h2 class="page-title">游戏化中心 - 多维度排行榜</h2>

    <el-row :gutter="20">
      <!-- Left Panel: Multi-Dimension Leaderboard -->
      <el-col :xs="24" :md="16">
        <el-card shadow="never" class="leaderboard-card" v-loading="loadingLeaderboard">
          <template #header>
            <div class="card-header">
              <span><el-icon><Medal /></el-icon> {{ currentLeaderboardTitle }}</span>
              <div class="header-controls">
                <!-- 排行榜类型切换 -->
                <el-select v-model="currentMetric" size="small" @change="handleMetricChange" style="width: 140px; margin-right: 10px;">
                  <el-option
                    v-for="metric in availableMetrics"
                    :key="metric.code"
                    :label="metric.name"
                    :value="metric.code">
                    <span>{{ metric.icon }} {{ metric.name }}</span>
                  </el-option>
                </el-select>
                <!-- 时间周期切换 -->
                <el-radio-group v-model="currentPeriod" size="small" @change="fetchCurrentLeaderboard">
                  <el-radio-button label="weekly">本周</el-radio-button>
                  <el-radio-button label="monthly">本月</el-radio-button>
                  <el-radio-button label="yearly">本年</el-radio-button>
                  <el-radio-button label="alltime">总榜</el-radio-button>
                </el-radio-group>
              </div>
            </div>
          </template>

          <!-- Top 3 Users -->
          <div class="top-users" v-if="leaderboardList.length >= 3">
            <!-- Second Place -->
            <div class="top-user second-place">
              <div class="rank-badge">2</div>
              <el-avatar :size="70" :src="getFullAvatarUrl(leaderboardList[1].avatarUrl) || defaultAvatar" class="user-avatar" @error="onAvatarError">{{ getInitials(leaderboardList[1].userName) }}</el-avatar>
              <div class="user-info">
                <div class="user-name">{{ leaderboardList[1].userName }}</div>
                <div class="user-score">{{ getScoreDisplay(leaderboardList[1]) }}</div>
                <div class="user-department">{{ leaderboardList[1].department || '未分配' }}</div>
              </div>
            </div>
            <!-- First Place -->
            <div class="top-user first-place">
              <div class="crown"><el-icon><Trophy /></el-icon></div>
              <div class="rank-badge">1</div>
              <el-avatar :size="90" :src="getFullAvatarUrl(leaderboardList[0].avatarUrl) || defaultAvatar" class="user-avatar" @error="onAvatarError">{{ getInitials(leaderboardList[0].userName) }}</el-avatar>
              <div class="user-info">
                <div class="user-name">{{ leaderboardList[0].userName }}</div>
                <div class="user-score">{{ getScoreDisplay(leaderboardList[0]) }}</div>
                <div class="user-department">{{ leaderboardList[0].department || '未分配' }}</div>
              </div>
            </div>
            <!-- Third Place -->
            <div class="top-user third-place">
              <div class="rank-badge">3</div>
              <el-avatar :size="70" :src="getFullAvatarUrl(leaderboardList[2].avatarUrl) || defaultAvatar" class="user-avatar" @error="onAvatarError">{{ getInitials(leaderboardList[2].userName) }}</el-avatar>
              <div class="user-info">
                <div class="user-name">{{ leaderboardList[2].userName }}</div>
                <div class="user-score">{{ getScoreDisplay(leaderboardList[2]) }}</div>
                <div class="user-department">{{ leaderboardList[2].department || '未分配' }}</div>
              </div>
            </div>
          </div>
          <el-empty v-else-if="!loadingLeaderboard && leaderboardList.length < 3" description="暂无足够数据生成Top 3排行"></el-empty>

          <!-- Rest of the Leaderboard Table -->
          <el-table :data="leaderboardList.slice(3)" stripe style="width: 100%" v-if="leaderboardList.length > 3" class="leaderboard-table">
            <el-table-column type="index" :index="indexMethod" width="70" label="排名" align="center" />
            <el-table-column label="用户" min-width="150">
              <template #default="{ row }">
                <div class="user-cell">
                  <el-avatar :size="30" :src="getFullAvatarUrl(row.avatarUrl) || defaultAvatar" class="table-avatar" @error="onAvatarError">{{ getInitials(row.userName) }}</el-avatar>
                  <span>{{ row.userName }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="department" label="部门" min-width="100" />
            <el-table-column :label="currentScoreLabel" sortable min-width="120" align="right">
                <template #default="{ row }">
                    <span style="font-weight: 600;">{{ getScoreDisplay(row) }}</span>
                </template>
            </el-table-column>
            <!-- 动态显示额外列 -->
            <el-table-column v-if="showTasksColumn" label="完成任务" sortable min-width="100" align="center">
              <template #default="{ row }">
                <span>{{ row.tasksCompleted || 0 }}</span>
              </template>
            </el-table-column>
            <el-table-column v-if="showCoinsColumn" label="金币" sortable min-width="100" align="center">
              <template #default="{ row }">
                <div class="score-with-icon">
                  <span>💰 {{ row.coins || 0 }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column v-if="showDiamondsColumn" label="钻石" sortable min-width="100" align="center">
              <template #default="{ row }">
                <div class="score-with-icon">
                  <span>💎 {{ row.diamonds || 0 }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="100" align="center">
              <template #default="{ row }">
                <el-button link type="primary" size="small" @click="viewProfile(row.userId)">查看</el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-empty v-if="!loadingLeaderboard && leaderboardList.length === 0" description="暂无排行数据"></el-empty>
        </el-card>
      </el-col>

      <!-- Right Panel: Achievements & Star -->
      <el-col :xs="24" :md="8">
        <el-card shadow="never" class="achievements-card mb-4" v-loading="loadingAchievements">
          <template #header>
            <div class="card-header">
              <span><el-icon><MagicStick /></el-icon> 最新成就</span>
            </div>
          </template>
          <div v-if="recentAchievements.length > 0" class="achievement-list thin-scrollbar">
            <div v-for="(achievement, index) in recentAchievements" :key="index" class="achievement-item">
              <el-avatar :size="36" :src="getFullAvatarUrl(achievement.user?.avatar) || defaultAvatar" class="achievement-avatar" @error="onAvatarError">{{ getInitials(achievement.user?.name) }}</el-avatar>
              <div class="achievement-info">
                 <span class="achievement-user">{{ achievement.user?.name || '未知用户' }}</span>
                 <span class="achievement-title">获得了成就: <strong>{{ achievement.title }}</strong></span>
                <div class="achievement-time">{{ formatTimeAgo(achievement.time) }}</div>
                 <!-- Add reward display if needed -->
              </div>
            </div>
          </div>
          <el-empty v-else description="暂无最新成就记录"></el-empty>
          <div class="card-footer" v-if="allAchievements.length > 0">
             <el-button type="primary" plain size="small" @click="achievementDialogVisible = true">
               查看所有成就
             </el-button>
           </div>
        </el-card>

        <el-card shadow="never" class="star-card" v-loading="loadingStar">
          <template #header>
            <div class="card-header">
              <span><el-icon><StarFilled /></el-icon> 本月之星</span>
            </div>
          </template>
          <div class="star-of-month" v-if="starOfMonthData">
            <el-avatar :size="80" :src="getFullAvatarUrl(starOfMonthData.avatar) || defaultAvatar" class="star-avatar" @error="onAvatarError">{{ getInitials(starOfMonthData.name) }}</el-avatar>
            <div class="star-info">
              <h3>{{ starOfMonthData.name }}</h3>
              <p>{{ starOfMonthData.department || '-' }}</p>
              <div class="star-stats">
                <div class="stat-item">
                  <span class="stat-value">{{ starOfMonthData.taskCount || 0 }}</span>
                  <span class="stat-label">完成任务</span>
                </div>
                <div class="stat-item">
                  <span class="stat-value">{{ starOfMonthData.monthlyRankScore || 0 }}</span>
                  <span class="stat-label">本月积分</span>
                </div>
                <div class="stat-item">
                  <span class="stat-value">{{ starOfMonthData.achievementCount || 0 }}</span>
                  <span class="stat-label">获得成就</span>
                </div>
              </div>
            </div>
          </div>
          <el-empty v-else description="暂无本月之星数据"></el-empty>
        </el-card>
      </el-col>
    </el-row>

    <!-- All Achievements Dialog -->
    <el-dialog title="所有成就记录" v-model="achievementDialogVisible" width="clamp(500px, 60%, 800px)" top="8vh" append-to-body draggable>
        <el-table :data="allAchievements" style="width: 100%" height="450px" v-loading="loadingAchievements">
            <el-table-column prop="user.name" label="用户" width="120" />
            <el-table-column prop="title" label="成就名称" min-width="150" />
            <!-- <el-table-column prop="description" label="描述" /> -->
            <el-table-column prop="time" label="获得时间" width="160">
              <template #default="{ row }">{{ formatTimeAgo(row.time) }}</template>
            </el-table-column>
             <!-- Add reward column if needed -->
        </el-table>
         <template #footer>
            <el-button @click="achievementDialogVisible = false">关闭</el-button>
         </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { Medal, Trophy, MagicStick, StarFilled } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { useGamificationStore } from '@/stores/modules/gamification'
import { formatDistanceToNow } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import { getFullAvatarUrl } from '@/stores/modules/user'
import { getMultiDimensionLeaderboard, getLeaderboardMetrics, LeaderboardMetrics, StatisticsPeriods } from '@/api/gamification'

const router = useRouter()
const gamificationStore = useGamificationStore()

const loadingLeaderboard = ref(false)
const loadingAchievements = ref(false)
const loadingStar = ref(false)

// 多维度排行榜状态
const currentMetric = ref(LeaderboardMetrics.POINTS) // 当前排行榜指标
const currentPeriod = ref(StatisticsPeriods.WEEKLY) // 当前时间周期
const leaderboardList = ref([]) // 排行榜数据
const availableMetrics = ref([]) // 可用的排行榜指标

// 成就和明星数据
const recentAchievements = computed(() => gamificationStore.achievements.slice(0, 5))
const allAchievements = computed(() => gamificationStore.achievements)
const starOfMonthData = ref(null)
const achievementDialogVisible = ref(false)

const defaultAvatar = 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png'

// --- Computed Properties ---

// 当前排行榜标题
const currentLeaderboardTitle = computed(() => {
  const metric = availableMetrics.value.find(m => m.code === currentMetric.value)
  return metric ? metric.name : '排行榜'
})

// 当前分数标签
const currentScoreLabel = computed(() => {
  const labels = {
    [LeaderboardMetrics.POINTS]: '积分',
    [LeaderboardMetrics.COINS]: '金币',
    [LeaderboardMetrics.DIAMONDS]: '钻石',
    [LeaderboardMetrics.TASKS_CREATED]: '创建任务',
    [LeaderboardMetrics.TASKS_COMPLETED]: '完成任务',
    [LeaderboardMetrics.TASKS_CLAIMED]: '领取任务',
    [LeaderboardMetrics.FAULTS_RECORDED]: '故障登记',
    [LeaderboardMetrics.MAINTENANCE_CREATED]: '维修单',
    [LeaderboardMetrics.ASSETS_UPDATED]: '资产更新'
  }
  return labels[currentMetric.value] || '分数'
})

// 是否显示任务列
const showTasksColumn = computed(() => {
  return currentMetric.value === LeaderboardMetrics.POINTS
})

// 是否显示金币列
const showCoinsColumn = computed(() => {
  return currentMetric.value === LeaderboardMetrics.POINTS || currentMetric.value === LeaderboardMetrics.COINS
})

// 是否显示钻石列
const showDiamondsColumn = computed(() => {
  return currentMetric.value === LeaderboardMetrics.POINTS || currentMetric.value === LeaderboardMetrics.DIAMONDS
})

// --- Helper Functions ---

// 获取分数显示
const getScoreDisplay = (user) => {
  const icons = {
    [LeaderboardMetrics.POINTS]: '🏆',
    [LeaderboardMetrics.COINS]: '💰',
    [LeaderboardMetrics.DIAMONDS]: '💎',
    [LeaderboardMetrics.TASKS_CREATED]: '📝',
    [LeaderboardMetrics.TASKS_COMPLETED]: '✅',
    [LeaderboardMetrics.TASKS_CLAIMED]: '👋',
    [LeaderboardMetrics.FAULTS_RECORDED]: '🔧',
    [LeaderboardMetrics.MAINTENANCE_CREATED]: '🛠️',
    [LeaderboardMetrics.ASSETS_UPDATED]: '📦'
  }

  const values = {
    [LeaderboardMetrics.POINTS]: user.points || 0,
    [LeaderboardMetrics.COINS]: user.coins || 0,
    [LeaderboardMetrics.DIAMONDS]: user.diamonds || 0,
    [LeaderboardMetrics.TASKS_CREATED]: user.tasksCreated || 0,
    [LeaderboardMetrics.TASKS_COMPLETED]: user.tasksCompleted || 0,
    [LeaderboardMetrics.TASKS_CLAIMED]: user.tasksClaimed || 0,
    [LeaderboardMetrics.FAULTS_RECORDED]: user.faultsRecorded || 0,
    [LeaderboardMetrics.MAINTENANCE_CREATED]: user.maintenanceCreated || 0,
    [LeaderboardMetrics.ASSETS_UPDATED]: user.assetsUpdated || 0
  }

  const icon = icons[currentMetric.value] || '📊'
  const value = values[currentMetric.value] || 0
  return `${icon} ${value}`
}

// --- API Call Functions ---

// 初始化排行榜指标
async function initializeMetrics() {
  try {
    // 设置默认的排行榜指标
    availableMetrics.value = [
      { code: LeaderboardMetrics.POINTS, name: '积分排行', icon: '🏆', description: '根据总积分排名' },
      { code: LeaderboardMetrics.COINS, name: '金币排行', icon: '💰', description: '根据金币数量排名' },
      { code: LeaderboardMetrics.DIAMONDS, name: '钻石排行', icon: '💎', description: '根据钻石数量排名' },
      { code: LeaderboardMetrics.TASKS_COMPLETED, name: '任务完成排行', icon: '✅', description: '根据完成任务数量排名' },
      { code: LeaderboardMetrics.TASKS_CREATED, name: '任务创建排行', icon: '📝', description: '根据创建任务数量排名' },
      { code: LeaderboardMetrics.FAULTS_RECORDED, name: '故障登记排行', icon: '🔧', description: '根据登记故障数量排名' },
      { code: LeaderboardMetrics.MAINTENANCE_CREATED, name: '维修单排行', icon: '🛠️', description: '根据创建维修单数量排名' },
      { code: LeaderboardMetrics.ASSETS_UPDATED, name: '资产更新排行', icon: '📦', description: '根据更新资产数量排名' }
    ]
  } catch (error) {
    console.error('初始化排行榜指标失败:', error)
  }
}

// 获取当前排行榜数据
async function fetchCurrentLeaderboard() {
  loadingLeaderboard.value = true
  try {
    const response = await getMultiDimensionLeaderboard(currentMetric.value, currentPeriod.value, 20)

    if (response.success && response.data) {
      leaderboardList.value = response.data.map((item, index) => ({
        userId: item.userId,
        userName: item.userName,
        avatarUrl: item.avatarUrl,
        department: item.department,
        points: item.points || 0,
        coins: item.coins || 0,
        diamonds: item.diamonds || 0,
        tasksCreated: item.tasksCreated || 0,
        tasksCompleted: item.tasksCompleted || 0,
        tasksClaimed: item.tasksClaimed || 0,
        faultsRecorded: item.faultsRecorded || 0,
        maintenanceCreated: item.maintenanceCreated || 0,
        assetsUpdated: item.assetsUpdated || 0,
        rank: index + 1
      }))
    } else {
      leaderboardList.value = []
      ElMessage.warning('暂无排行榜数据')
    }
  } catch (error) {
    console.error('获取排行榜数据失败:', error)
    ElMessage.error('获取排行榜数据失败')
    leaderboardList.value = []
  } finally {
    loadingLeaderboard.value = false
  }
}

// 处理排行榜指标变化
const handleMetricChange = () => {
  fetchCurrentLeaderboard()
}

async function fetchAchievements() {
  loadingAchievements.value = true;
  try {
      // Call the action from the store
      await gamificationStore.fetchAchievements();
      // Note: The store's fetchAchievements currently gets ALL achievements.
      // The computed properties `recentAchievements` and `allAchievements` handle slicing/display.
  } catch (error) {
    ElMessage.error('获取成就数据失败');
    console.error('Fetch achievements error:', error);
  } finally {
    loadingAchievements.value = false;
  }
}

async function fetchStarOfMonth() {
    loadingStar.value = true;
    try {
        // TODO: Implement dedicated API call or store logic for Star of the Month
        // Example: const response = await gamificationApi.getStarOfMonth();
        // Mock data for now:
        await new Promise(resolve => setTimeout(resolve, 400)); // Simulate delay
        starOfMonthData.value = {
            id: 'user1', name: '张三', avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
            department: '研发部', taskCount: 15, monthlyRankScore: 890, achievementCount: 3
        };
    } catch (error) {
        ElMessage.error('获取本月之星数据失败');
        console.error('Fetch star error:', error);
        starOfMonthData.value = null;
    } finally {
        loadingStar.value = false;
    }
}

// --- Helper Functions ---
const indexMethod = (index) => {
  // Calculate rank based on the sliced data (index + 4 because top 3 are separate)
  return index + 4;
};

const formatTimeAgo = (timestamp) => {
    if (!timestamp) return '';
    try {
        const date = new Date(timestamp);
        if (isNaN(date.getTime())) return '无效日期';
        return formatDistanceToNow(date, { addSuffix: true, locale: zhCN });
    } catch (e) { return '无效日期'; }
};

const getInitials = (name) => {
    if (!name || typeof name !== 'string') return '?';
    const names = name.split(' ');
    let initials = names[0].substring(0, 1).toUpperCase();
    if (names.length > 1) {
        initials += names[names.length - 1].substring(0, 1).toUpperCase();
    }
    return initials;
};

const onAvatarError = (event) => {
  event.target.style.display = 'none'; // Hide broken img, let initials show
};

const viewProfile = (userId) => {
  if (!userId) return;
  // Navigate to the user profile page (assuming route name is 'UserProfile')
  router.push({ name: 'UserProfile', params: { id: userId } }).catch(err => {
      console.warn('Profile navigation failed:', err);
      // Maybe user profile page doesn't exist yet?
  });
};

// --- Lifecycle Hook ---
onMounted(async () => {
  // 初始化多维度排行榜
  await initializeMetrics();
  await fetchCurrentLeaderboard();
  await fetchAchievements();
  await fetchStarOfMonth();
});

</script>

<style scoped>
/* Using CSS variables */
.leaderboard-view {
  padding: 20px;
  background-color: var(--bg-color);
}

.page-title {
  font-size: 1.8rem;
  font-weight: 600;
  color: var(--header-bg);
  margin-bottom: 20px;
}

.mb-4 { margin-bottom: 20px; }

.leaderboard-card,
.achievements-card,
.star-card {
  border: 1px solid var(--border-color-light);
  background-color: var(--card-bg);
  border-radius: 8px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: var(--header-bg);
  font-size: 1.1rem;
  flex-wrap: wrap;
  gap: 10px;
}
.card-header > span {
    display: flex;
    align-items: center;
    gap: 8px;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 10px;
  flex-wrap: wrap;
}

@media (max-width: 768px) {
  .card-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .header-controls {
    width: 100%;
    justify-content: flex-start;
  }
}

.top-users {
  display: flex;
  justify-content: space-around;
  align-items: flex-end; /* Align bottoms */
  text-align: center;
  margin-bottom: 30px;
  padding: 20px 0;
  border-bottom: 1px solid var(--border-color-light);
}

.top-user {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.rank-badge {
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
  background-color: var(--text-secondary-color);
  color: white;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  font-size: 0.8rem;
  font-weight: bold;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
  z-index: 2;
}

.user-avatar {
  border: 4px solid transparent;
  margin-bottom: 10px;
  background-color: #eee; /* Fallback background */
}

.user-info {
    margin-top: 5px;
}

.user-name {
  font-weight: 600;
  color: var(--text-color);
  font-size: 1rem;
}

.user-score {
  font-size: 0.9rem;
  color: var(--primary-color);
  font-weight: bold;
  margin-top: 4px;
}

.user-department {
  font-size: 0.8rem;
  color: var(--text-color-secondary);
  margin-top: 2px;
}

.score-with-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

/* Specific styles for top 3 */
.first-place .user-avatar { border-color: #ffd700; /* Gold */ }
.second-place .user-avatar { border-color: #c0c0c0; /* Silver */ }
.third-place .user-avatar { border-color: #cd7f32; /* Bronze */ }

.first-place .rank-badge { background-color: #ffd700; color: #4d3a00; }
.second-place .rank-badge { background-color: #c0c0c0; color: #333; }
.third-place .rank-badge { background-color: #cd7f32; color: white; }

.crown {
  position: absolute;
  top: -25px; /* Adjust based on avatar size */
  left: 50%;
  transform: translateX(-50%);
  color: #ffd700;
  font-size: 24px;
  z-index: 3;
}

/* Leaderboard Table */
.leaderboard-table .user-cell {
    display: flex;
    align-items: center;
    gap: 10px;
}
.leaderboard-table .table-avatar {
    background-color: #eee;
}

/* Achievements */
.achievement-list {
    max-height: 300px; /* Limit height and make scrollable */
    overflow-y: auto;
    padding-right: 5px;
}

.achievement-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 15px;
  padding-bottom: 15px;
  border-bottom: 1px solid var(--border-color-light);
}
.achievement-item:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.achievement-avatar {
    flex-shrink: 0;
    background-color: #eee;
}

.achievement-info {
    flex-grow: 1;
}
.achievement-user {
    font-weight: 600;
    margin-right: 5px;
}
.achievement-title {
    font-size: 0.9rem;
    color: var(--text-color);
    margin-bottom: 3px;
}
.achievement-title strong {
    color: var(--primary-color);
}
.achievement-time {
    font-size: 0.75rem;
    color: var(--text-secondary-color);
}

.card-footer {
    text-align: center;
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid var(--border-color-light);
}

/* Star of the Month */
.star-card {
    /* Optional: Add specific background or border */
}
.star-of-month {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 10px 0;
}

.star-avatar {
    border: 4px solid var(--primary-color); /* Highlight border */
    margin-bottom: 15px;
    background-color: #eee;
}

.star-info h3 {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--header-bg);
  margin: 0 0 5px 0;
}

.star-info p {
  font-size: 0.9rem;
  color: var(--text-secondary-color);
  margin-bottom: 15px;
}

.star-stats {
  display: flex;
  justify-content: space-around;
  width: 100%;
  gap: 10px;
  padding: 10px;
  background-color: var(--column-bg); /* Light background for stats */
  border-radius: 6px;
}

.stat-item {
  display: flex;
  flex-direction: column;
}

.stat-value {
  font-size: 1.1rem;
  font-weight: bold;
  color: var(--primary-color);
}

.stat-label {
  font-size: 0.75rem;
  color: var(--text-secondary-color);
}

/* Common Scrollbar Style */
.thin-scrollbar::-webkit-scrollbar { width: 5px; }
.thin-scrollbar::-webkit-scrollbar-track { background: transparent; }
.thin-scrollbar::-webkit-scrollbar-thumb { background-color: #ccc; border-radius: 3px; }
.thin-scrollbar::-webkit-scrollbar-thumb:hover { background-color: #aaa; }

/* Dark theme adjustments */
.dark-theme .top-users { border-bottom-color: var(--dark-border-color); }
.dark-theme .user-avatar, .dark-theme .table-avatar, .dark-theme .achievement-avatar, .dark-theme .star-avatar {
    background-color: #4a5568;
}
.dark-theme .achievement-item { border-bottom-color: var(--dark-border-color); }
.dark-theme .card-footer { border-top-color: var(--dark-border-color); }
.dark-theme .star-stats { background-color: var(--dark-bg); }

</style> 