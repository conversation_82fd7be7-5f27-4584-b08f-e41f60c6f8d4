{"version": 3, "file": "ru.min.js", "sources": ["../../../../packages/locale/lang/ru.ts"], "sourcesContent": ["export default {\n  name: 'ru',\n  el: {\n    breadcrumb: {\n      label: 'Хлебные крошки',\n    },\n    colorpicker: {\n      confirm: 'подтверждать',\n      clear: 'Очистить',\n    },\n    datepicker: {\n      now: 'Сейчас',\n      today: 'Сегодня',\n      cancel: 'Отме<PERSON>',\n      clear: 'Очистить',\n      confirm: 'подтверждать',\n      selectDate: 'Выбрать дату',\n      selectTime: 'Выбрать время',\n      startDate: 'Дата начала',\n      startTime: 'Время начала',\n      endDate: 'Дата окончания',\n      endTime: 'Время окончания',\n      prevYear: 'Предыдущий год',\n      nextYear: 'Следующий год',\n      prevMonth: 'Предыдущий месяц',\n      nextMonth: 'Следующий месяц',\n      year: '',\n      month1: 'Январь',\n      month2: 'Февраль',\n      month3: 'Март',\n      month4: 'Апрель',\n      month5: 'Май',\n      month6: 'Июнь',\n      month7: 'Июль',\n      month8: 'Август',\n      month9: 'Сентябрь',\n      month10: 'Октябрь',\n      month11: 'Ноябрь',\n      month12: 'Декабрь',\n      week: 'неделя',\n      weeks: {\n        sun: 'Вс',\n        mon: 'Пн',\n        tue: 'Вт',\n        wed: 'Ср',\n        thu: 'Чт',\n        fri: 'Пт',\n        sat: 'Сб',\n      },\n      months: {\n        jan: 'Янв',\n        feb: 'Фев',\n        mar: 'Мар',\n        apr: 'Апр',\n        may: 'Май',\n        jun: 'Июн',\n        jul: 'Июл',\n        aug: 'Авг',\n        sep: 'Сен',\n        oct: 'Окт',\n        nov: 'Ноя',\n        dec: 'Дек',\n      },\n    },\n    select: {\n      loading: 'Загрузка',\n      noMatch: 'Совпадений не найдено',\n      noData: 'Нет данных',\n      placeholder: 'Выбрать',\n    },\n    mention: {\n      loading: 'Загрузка',\n    },\n    cascader: {\n      noMatch: 'Совпадений не найдено',\n      loading: 'Загрузка',\n      placeholder: 'Выбрать',\n      noData: 'Нет данных',\n    },\n    pagination: {\n      goto: 'Перейти',\n      pagesize: ' на странице',\n      total: 'Всего {total}',\n      pageClassifier: '',\n      page: 'Страница',\n      prev: 'Перейти на предыдущую страницу',\n      next: 'Перейти на следующую страницу',\n      currentPage: 'страница {pager}',\n      prevPages: 'Предыдущие {pager} страниц',\n      nextPages: 'Следующие {pager} страниц',\n    },\n    messagebox: {\n      title: 'Сообщение',\n      confirm: 'подтверждать',\n      cancel: 'Отмена',\n      error: 'Недопустимый ввод данных',\n    },\n    upload: {\n      deleteTip: 'Нажмите [Удалить] для удаления',\n      delete: 'Удалить',\n      preview: 'Превью',\n      continue: 'Продолжить',\n    },\n    table: {\n      emptyText: 'Нет данных',\n      confirmFilter: 'Подтвердить',\n      resetFilter: 'Сбросить',\n      clearFilter: 'Все',\n      sumText: 'Сумма',\n    },\n    tour: {\n      next: 'Далее',\n      previous: 'Назад',\n      finish: 'Завершить',\n    },\n    tree: {\n      emptyText: 'Нет данных',\n    },\n    transfer: {\n      noMatch: 'Совпадений не найдено',\n      noData: 'Нет данных',\n      titles: ['Список 1', 'Список 2'],\n      filterPlaceholder: 'Введите ключевое слово',\n      noCheckedFormat: '{total} пунктов',\n      hasCheckedFormat: '{checked}/{total} выбрано',\n    },\n    image: {\n      error: 'ОШИБКА',\n    },\n    pageHeader: {\n      title: 'Назад',\n    },\n    popconfirm: {\n      confirmButtonText: 'подтверждать',\n      cancelButtonText: 'Отмена',\n    },\n    carousel: {\n      leftArrow: 'Слайдер стрелка влево',\n      rightArrow: 'Слайдер стрелка вправо',\n      indicator: 'Слайдер перейти на страницу под номером {index}',\n    },\n  },\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA,WAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,iFAAiF,CAAC,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,0EAA0E,CAAC,KAAK,CAAC,kDAAkD,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,sCAAsC,CAAC,KAAK,CAAC,4CAA4C,CAAC,MAAM,CAAC,sCAAsC,CAAC,KAAK,CAAC,kDAAkD,CAAC,OAAO,CAAC,0EAA0E,CAAC,UAAU,CAAC,qEAAqE,CAAC,UAAU,CAAC,2EAA2E,CAAC,SAAS,CAAC,+DAA+D,CAAC,SAAS,CAAC,qEAAqE,CAAC,OAAO,CAAC,iFAAiF,CAAC,OAAO,CAAC,uFAAuF,CAAC,QAAQ,CAAC,iFAAiF,CAAC,QAAQ,CAAC,2EAA2E,CAAC,SAAS,CAAC,6FAA6F,CAAC,SAAS,CAAC,uFAAuF,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,sCAAsC,CAAC,MAAM,CAAC,4CAA4C,CAAC,MAAM,CAAC,0BAA0B,CAAC,MAAM,CAAC,sCAAsC,CAAC,MAAM,CAAC,oBAAoB,CAAC,MAAM,CAAC,0BAA0B,CAAC,MAAM,CAAC,0BAA0B,CAAC,MAAM,CAAC,sCAAsC,CAAC,MAAM,CAAC,kDAAkD,CAAC,OAAO,CAAC,4CAA4C,CAAC,OAAO,CAAC,sCAAsC,CAAC,OAAO,CAAC,4CAA4C,CAAC,IAAI,CAAC,sCAAsC,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,kDAAkD,CAAC,OAAO,CAAC,sHAAsH,CAAC,MAAM,CAAC,yDAAyD,CAAC,WAAW,CAAC,4CAA4C,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,kDAAkD,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,sHAAsH,CAAC,OAAO,CAAC,kDAAkD,CAAC,WAAW,CAAC,4CAA4C,CAAC,MAAM,CAAC,yDAAyD,CAAC,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,4CAA4C,CAAC,QAAQ,CAAC,gEAAgE,CAAC,KAAK,CAAC,wCAAwC,CAAC,cAAc,CAAC,EAAE,CAAC,IAAI,CAAC,kDAAkD,CAAC,IAAI,CAAC,uKAAuK,CAAC,IAAI,CAAC,iKAAiK,CAAC,WAAW,CAAC,0DAA0D,CAAC,SAAS,CAAC,iHAAiH,CAAC,SAAS,CAAC,2GAA2G,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,wDAAwD,CAAC,OAAO,CAAC,0EAA0E,CAAC,MAAM,CAAC,sCAAsC,CAAC,KAAK,CAAC,wIAAwI,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,6JAA6J,CAAC,MAAM,CAAC,4CAA4C,CAAC,OAAO,CAAC,sCAAsC,CAAC,QAAQ,CAAC,8DAA8D,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,yDAAyD,CAAC,aAAa,CAAC,oEAAoE,CAAC,WAAW,CAAC,kDAAkD,CAAC,WAAW,CAAC,oBAAoB,CAAC,OAAO,CAAC,gCAAgC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,gCAAgC,CAAC,QAAQ,CAAC,gCAAgC,CAAC,MAAM,CAAC,wDAAwD,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,yDAAyD,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,sHAAsH,CAAC,MAAM,CAAC,yDAAyD,CAAC,MAAM,CAAC,CAAC,wCAAwC,CAAC,wCAAwC,CAAC,CAAC,iBAAiB,CAAC,4HAA4H,CAAC,eAAe,CAAC,oDAAoD,CAAC,gBAAgB,CAAC,8DAA8D,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,sCAAsC,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,gCAAgC,CAAC,CAAC,UAAU,CAAC,CAAC,iBAAiB,CAAC,0EAA0E,CAAC,gBAAgB,CAAC,sCAAsC,CAAC,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,sHAAsH,CAAC,UAAU,CAAC,4HAA4H,CAAC,SAAS,CAAC,2NAA2N,CAAC,CAAC,CAAC;;;;;;;;"}