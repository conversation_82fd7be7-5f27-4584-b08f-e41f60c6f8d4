// File: Application/Features/Inputs/Commands/DeleteInputCommandHandler.cs
// Description: 处理删除输入内容命令
using MediatR;
using ItAssetsSystem.Application.Common.Dtos;
using System.Globalization;
using System.Text;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using System;
using System.Collections.Generic;

namespace ItAssetsSystem.Application.Features.Inputs.Commands
{
    public class DeleteInputCommandHandler : IRequestHandler<DeleteInputCommand, ApiResponse<bool>>
    {
        private readonly string _csvPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "inputs.csv");

        public async Task<ApiResponse<bool>> Handle(DeleteInputCommand request, CancellationToken cancellationToken)
        {
            if (!File.Exists(_csvPath))
                return ApiResponse<bool>.CreateFail("数据文件不存在");

            var lines = await File.ReadAllLinesAsync(_csvPath, Encoding.UTF8, cancellationToken);
            if (lines.Length <= 1)
                return ApiResponse<bool>.CreateFail("没有可删除的数据");

            var header = lines[0];
            var newLines = new List<string> { header };
            bool deleted = false;
            for (int i = 1; i < lines.Length; i++)
            {
                var parts = ParseCsvLine(lines[i]);
                if (parts.Length >= 2 && parts[1] == request.Time && !deleted)
                {
                    deleted = true;
                    continue; // 跳过该行
                }
                newLines.Add(lines[i]);
            }
            if (!deleted)
                return ApiResponse<bool>.CreateFail("未找到指定记录");

            await File.WriteAllLinesAsync(_csvPath, newLines, Encoding.UTF8, cancellationToken);
            return ApiResponse<bool>.CreateSuccess(true, "删除成功");
        }

        private string[] ParseCsvLine(string line)
        {
            var parts = new List<string>();
            int i = 0;
            while (i < line.Length)
            {
                if (line[i] == '"')
                {
                    int j = i + 1;
                    var sb = new StringBuilder();
                    while (j < line.Length)
                    {
                        if (line[j] == '"')
                        {
                            if (j + 1 < line.Length && line[j + 1] == '"')
                            {
                                sb.Append('"');
                                j += 2;
                            }
                            else
                            {
                                break;
                            }
                        }
                        else
                        {
                            sb.Append(line[j]);
                            j++;
                        }
                    }
                    parts.Add(sb.ToString());
                    i = j + 2;
                }
                else
                {
                    i++;
                }
            }
            return parts.ToArray();
        }
    }
} 