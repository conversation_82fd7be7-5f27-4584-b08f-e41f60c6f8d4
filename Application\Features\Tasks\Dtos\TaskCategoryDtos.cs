using System;
using System.ComponentModel.DataAnnotations;

namespace ItAssetsSystem.Application.Features.Tasks.Dtos
{
    /// <summary>
    /// 任务分类DTO
    /// </summary>
    public class TaskCategoryDto
    {
        public int CategoryId { get; set; }
        public string Name { get; set; } = string.Empty;
        public string? Description { get; set; }
        public string? Color { get; set; }
        public string? Icon { get; set; }
        public int SortOrder { get; set; }
        public bool IsActive { get; set; }
        public int CreatedBy { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
        
        /// <summary>
        /// 该分类下的任务数量
        /// </summary>
        public int TaskCount { get; set; }
    }

    /// <summary>
    /// 创建任务分类请求DTO
    /// </summary>
    public class CreateTaskCategoryRequestDto
    {
        [Required(ErrorMessage = "分类名称不能为空")]
        [StringLength(100, ErrorMessage = "分类名称长度不能超过100个字符")]
        public string Name { get; set; } = string.Empty;

        [StringLength(500, ErrorMessage = "分类描述长度不能超过500个字符")]
        public string? Description { get; set; }

        [StringLength(7, ErrorMessage = "颜色代码长度不能超过7个字符")]
        [RegularExpression(@"^#[0-9A-Fa-f]{6}$", ErrorMessage = "颜色代码格式不正确，应为#RRGGBB格式")]
        public string? Color { get; set; }

        [StringLength(50, ErrorMessage = "图标名称长度不能超过50个字符")]
        public string? Icon { get; set; }

        public int SortOrder { get; set; } = 0;

        public bool IsActive { get; set; } = true;
    }

    /// <summary>
    /// 更新任务分类请求DTO
    /// </summary>
    public class UpdateTaskCategoryRequestDto
    {
        [Required(ErrorMessage = "分类名称不能为空")]
        [StringLength(100, ErrorMessage = "分类名称长度不能超过100个字符")]
        public string Name { get; set; } = string.Empty;

        [StringLength(500, ErrorMessage = "分类描述长度不能超过500个字符")]
        public string? Description { get; set; }

        [StringLength(7, ErrorMessage = "颜色代码长度不能超过7个字符")]
        [RegularExpression(@"^#[0-9A-Fa-f]{6}$", ErrorMessage = "颜色代码格式不正确，应为#RRGGBB格式")]
        public string? Color { get; set; }

        [StringLength(50, ErrorMessage = "图标名称长度不能超过50个字符")]
        public string? Icon { get; set; }

        public int SortOrder { get; set; } = 0;

        public bool IsActive { get; set; } = true;
    }

    /// <summary>
    /// 任务分类查询请求DTO
    /// </summary>
    public class TaskCategoryQueryRequestDto
    {
        /// <summary>
        /// 搜索关键词
        /// </summary>
        public string? SearchKeyword { get; set; }

        /// <summary>
        /// 是否只查询启用的分类
        /// </summary>
        public bool? IsActive { get; set; }

        /// <summary>
        /// 页码
        /// </summary>
        public int PageNumber { get; set; } = 1;

        /// <summary>
        /// 页大小
        /// </summary>
        public int PageSize { get; set; } = 20;

        /// <summary>
        /// 排序字段
        /// </summary>
        public string? SortBy { get; set; } = "SortOrder";

        /// <summary>
        /// 排序方向 (asc/desc)
        /// </summary>
        public string? SortDirection { get; set; } = "asc";
    }

    /// <summary>
    /// 任务分类简化DTO (用于下拉选择)
    /// </summary>
    public class TaskCategorySimpleDto
    {
        public int CategoryId { get; set; }
        public string Name { get; set; } = string.Empty;
        public string? Color { get; set; }
        public string? Icon { get; set; }
        public bool IsActive { get; set; }
    }
}
