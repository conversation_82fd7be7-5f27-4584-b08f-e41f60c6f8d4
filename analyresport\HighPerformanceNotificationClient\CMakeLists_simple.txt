cmake_minimum_required(VERSION 3.16)
project(HighPerformanceNotificationClient)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Set output directory
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)

# Platform check
if(NOT WIN32)
    message(FATAL_ERROR "This project is designed for Windows only")
endif()

# Compiler options
if(MSVC)
    add_compile_options(/W4 /WX-)
    add_compile_definitions(_WIN32_WINNT=0x0A00)  # Windows 10
    add_compile_definitions(NOMINMAX)
else()
    add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# Check what source files exist
message(STATUS "Checking for source files...")
if(EXISTS ${CMAKE_SOURCE_DIR}/src/main.cpp)
    message(STATUS "Found: src/main.cpp")
else()
    message(WARNING "Missing: src/main.cpp")
endif()

if(EXISTS ${CMAKE_SOURCE_DIR}/src/demo_main.cpp)
    message(STATUS "Found: src/demo_main.cpp")
else()
    message(WARNING "Missing: src/demo_main.cpp")
endif()

# Create demo executable that actually works
add_executable(SimpleNotificationDemo
    src/demo_main.cpp
)

target_link_libraries(SimpleNotificationDemo
    ws2_32
    user32
)

message(STATUS "Build configuration completed")