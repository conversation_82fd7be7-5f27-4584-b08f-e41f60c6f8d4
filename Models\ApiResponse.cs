// File: Models/ApiResponse.cs
// Description: API响应模型，用于标准化API返回格式

using System.Text.Json.Serialization;

namespace ItAssetsSystem.Models
{
    /// <summary>
    /// API响应模型
    /// </summary>
    /// <typeparam name="T">响应数据类型</typeparam>
    public class ApiResponse<T>
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        [JsonPropertyName("success")]
        public bool Success { get; set; }

        /// <summary>
        /// 响应数据
        /// </summary>
        public T Data { get; set; }

        /// <summary>
        /// 响应消息
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        [JsonPropertyName("error")]
        public string Error { get; set; }

        /// <summary>
        /// 分页信息
        /// </summary>
        public PaginationInfo Pagination { get; set; }

        /// <summary>
        /// 创建成功响应
        /// </summary>
        /// <param name="data">响应数据</param>
        /// <param name="message">响应消息</param>
        /// <returns>成功的API响应</returns>
        public static ApiResponse<T> CreateSuccess(T data, string message = "操作成功")
        {
            return new ApiResponse<T>
            {
                Success = true,
                Data = data,
                Message = message
            };
        }

        /// <summary>
        /// 创建失败响应
        /// </summary>
        /// <param name="message">错误消息</param>
        /// <param name="error">详细错误信息</param>
        /// <returns>失败的API响应</returns>
        public static ApiResponse<T> CreateFail(string message, string error = null)
        {
            return new ApiResponse<T>
            {
                Success = false,
                Message = message,
                Error = error ?? message
            };
        }


    }

    /// <summary>
    /// 非泛型API响应模型
    /// </summary>
    public class ApiResponse
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        [JsonPropertyName("success")]
        public bool Success { get; set; }

        /// <summary>
        /// 响应消息
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        [JsonPropertyName("error")]
        public string Error { get; set; }

        /// <summary>
        /// 创建成功响应
        /// </summary>
        /// <param name="message">响应消息</param>
        /// <returns>成功的API响应</returns>
        public static ApiResponse Ok(string message = "操作成功")
        {
            return new ApiResponse
            {
                Success = true,
                Message = message
            };
        }

        /// <summary>
        /// 创建失败响应
        /// </summary>
        /// <param name="message">错误消息</param>
        /// <param name="errorDetails">详细错误信息</param>
        /// <returns>失败的API响应</returns>
        public static ApiResponse Fail(string message, string errorDetails = null)
        {
            return new ApiResponse
            {
                Success = false,
                Message = message,
                Error = errorDetails ?? message
            };
        }
    }

    /// <summary>
    /// 分页信息
    /// </summary>
    public class PaginationInfo
    {
        /// <summary>
        /// 当前页码
        /// </summary>
        public int PageIndex { get; set; }

        /// <summary>
        /// 每页大小
        /// </summary>
        public int PageSize { get; set; }

        /// <summary>
        /// 总记录数
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// 总页数
        /// </summary>
        public int TotalPages { get; set; }
    }
} 