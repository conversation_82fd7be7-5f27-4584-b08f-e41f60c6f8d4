// File: Infrastructure/Data/Extensions/HostExtensions.cs
// Description: 主机扩展方法，用于初始化数据库和修复通知表结构

using ItAssetsSystem.Core.Initialization;
using ItAssetsSystem.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;

namespace ItAssetsSystem.Infrastructure.Data.Extensions
{
    /// <summary>
    /// 主机扩展类
    /// </summary>
    public static class HostExtensions
    {
        /// <summary>
        /// 修复通知表结构
        /// </summary>
        public static async Task<IHost> FixNotificationTableAsync(this IHost host)
        {
            using (var scope = host.Services.CreateScope())
            {
                var services = scope.ServiceProvider;
                try
                {
                    var context = services.GetRequiredService<AppDbContext>();
                    var logger = services.GetRequiredService<ILogger<AppDbContext>>();

                    logger.LogInformation("开始修复通知表结构");

                    // 初始化并修复通知表
                    await NotificationInitializer.InitializeNotificationTableAsync(services);

                    logger.LogInformation("通知表结构修复完成");
                }
                catch (Exception ex)
                {
                    var logger = services.GetRequiredService<ILogger<AppDbContext>>();
                    logger.LogError(ex, "修复通知表结构失败");
                }
            }

            return host;
        }

        /// <summary>
        /// 修复故障记录表结构
        /// </summary>
        public static async Task<IHost> FixFaultRecordTableAsync(this IHost host)
        {
            using (var scope = host.Services.CreateScope())
            {
                var services = scope.ServiceProvider;
                try
                {
                    var context = services.GetRequiredService<AppDbContext>();
                    var logger = services.GetRequiredService<ILogger<AppDbContext>>();

                    logger.LogInformation("开始修复故障记录表结构");

                    // 初始化并修复故障记录表
                    await FaultRecordInitializer.InitializeFaultRecordTableAsync(services);

                    logger.LogInformation("故障记录表结构修复完成");
                }
                catch (Exception ex)
                {
                    var logger = services.GetRequiredService<ILogger<AppDbContext>>();
                    logger.LogError(ex, "修复故障记录表结构失败");
                }
            }

            return host;
        }

        /// <summary>
        /// 修复周期性任务计划表结构
        /// </summary>
        public static async Task<IHost> FixPeriodicTaskScheduleTableAsync(this IHost host)
        {
            using (var scope = host.Services.CreateScope())
            {
                var services = scope.ServiceProvider;
                try
                {
                    var context = services.GetRequiredService<AppDbContext>();
                    var logger = services.GetRequiredService<ILogger<AppDbContext>>();

                    logger.LogInformation("开始修复周期性任务计划表结构");

                    // 初始化并修复周期性任务计划表
                    await PeriodicTaskScheduleInitializer.InitializePeriodicTaskScheduleTableAsync(services);

                    logger.LogInformation("周期性任务计划表结构修复完成");
                }
                catch (Exception ex)
                {
                    var logger = services.GetRequiredService<ILogger<AppDbContext>>();
                    logger.LogError(ex, "修复周期性任务计划表结构失败");
                }
            }

            return host;
        }

        /// <summary>
        /// 修复任务历史记录表结构
        /// </summary>
        public static async Task<IHost> FixTaskHistoryTableAsync(this IHost host)
        {
            using (var scope = host.Services.CreateScope())
            {
                var services = scope.ServiceProvider;
                try
                {
                    var context = services.GetRequiredService<AppDbContext>();
                    var logger = services.GetRequiredService<ILogger<AppDbContext>>();

                    logger.LogInformation("开始修复任务历史记录表结构");

                    // 初始化并修复任务历史记录表
                    await TaskHistoryInitializer.InitializeTaskHistoryTableAsync(services);

                    logger.LogInformation("任务历史记录表结构修复完成");
                }
                catch (Exception ex)
                {
                    var logger = services.GetRequiredService<ILogger<AppDbContext>>();
                    logger.LogError(ex, "修复任务历史记录表结构失败");
                }
            }

            return host;
        }

        /// <summary>
        /// 初始化班次管理表
        /// </summary>
        public static async Task<IHost> InitializeShiftManagementTablesAsync(this IHost host)
        {
            using (var scope = host.Services.CreateScope())
            {
                var services = scope.ServiceProvider;
                try
                {
                    var context = services.GetRequiredService<AppDbContext>();
                    var logger = services.GetRequiredService<ILogger<AppDbContext>>();

                    logger.LogInformation("开始初始化班次管理表...");

                    // 创建班次表
                    var createShiftTableSql = @"
                        CREATE TABLE IF NOT EXISTS `work_shifts` (
                            `shift_id` BIGINT AUTO_INCREMENT PRIMARY KEY,
                            `shift_name` VARCHAR(50) NOT NULL COMMENT '班次名称',
                            `shift_code` VARCHAR(20) NOT NULL UNIQUE COMMENT '班次代码',
                            `shift_type` VARCHAR(20) NOT NULL DEFAULT 'Day' COMMENT '班次类型',
                            `start_time` TIME NOT NULL COMMENT '开始时间',
                            `end_time` TIME NOT NULL COMMENT '结束时间',
                            `task_claim_time` TIME NOT NULL COMMENT '任务领取时间',
                            `is_overnight` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否跨天',
                            `is_active` BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否启用',
                            `description` VARCHAR(500) COMMENT '描述',
                            `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                            `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                            `created_by` INT NOT NULL COMMENT '创建用户ID',
                            `updated_by` INT COMMENT '更新用户ID'
                        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
                    ";

                    await context.Database.ExecuteSqlRawAsync(createShiftTableSql);

                    // 为tasks表添加完成用户水印字段（如果不存在）
                    var checkAndAddWatermarkColumnsSql = @"
                        -- 检查并添加CompletedByUserId字段
                        SET @sql = (SELECT IF(
                            (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
                             WHERE TABLE_SCHEMA = DATABASE()
                             AND TABLE_NAME = 'tasks'
                             AND COLUMN_NAME = 'CompletedByUserId') = 0,
                            'ALTER TABLE `tasks` ADD COLUMN `CompletedByUserId` INT COMMENT ''完成用户ID''',
                            'SELECT ''CompletedByUserId column already exists'' as message'
                        ));
                        PREPARE stmt FROM @sql;
                        EXECUTE stmt;
                        DEALLOCATE PREPARE stmt;

                        -- 检查并添加CompletionWatermarkColor字段
                        SET @sql = (SELECT IF(
                            (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
                             WHERE TABLE_SCHEMA = DATABASE()
                             AND TABLE_NAME = 'tasks'
                             AND COLUMN_NAME = 'CompletionWatermarkColor') = 0,
                            'ALTER TABLE `tasks` ADD COLUMN `CompletionWatermarkColor` VARCHAR(7) COMMENT ''完成用户水印颜色''',
                            'SELECT ''CompletionWatermarkColor column already exists'' as message'
                        ));
                        PREPARE stmt FROM @sql;
                        EXECUTE stmt;
                        DEALLOCATE PREPARE stmt;
                    ";

                    await context.Database.ExecuteSqlRawAsync(checkAndAddWatermarkColumnsSql);

                    logger.LogInformation("班次管理表初始化完成");
                }
                catch (Exception ex)
                {
                    var logger = services.GetRequiredService<ILogger<AppDbContext>>();
                    logger.LogError(ex, "初始化班次管理表时发生错误");
                    // 不抛出异常，允许应用继续启动
                }
            }

            return host;
        }

        /// <summary>
        /// 初始化任务相关表
        /// </summary>
        public static async Task<IHost> InitializeTaskTablesAsync(this IHost host)
        {
            using (var scope = host.Services.CreateScope())
            {
                var services = scope.ServiceProvider;
                try
                {
                    var context = services.GetRequiredService<AppDbContext>();
                    var logger = services.GetRequiredService<ILogger<AppDbContext>>();

                    logger.LogInformation("开始初始化任务相关表...");

                    var initializer = new Core.Initialization.TaskTablesInitializer(context,
                        services.GetRequiredService<ILogger<Core.Initialization.TaskTablesInitializer>>());
                    await initializer.InitializeAsync();

                    logger.LogInformation("任务相关表初始化完成");
                }
                catch (Exception ex)
                {
                    var logger = services.GetRequiredService<ILogger<AppDbContext>>();
                    logger.LogError(ex, "初始化任务相关表时发生错误");
                    // 不抛出异常，允许应用继续启动
                }
            }

            return host;
        }

        /// <summary>
        /// 初始化游戏化相关表
        /// </summary>
        public static async Task<IHost> InitializeGamificationTablesAsync(this IHost host)
        {
            using (var scope = host.Services.CreateScope())
            {
                var services = scope.ServiceProvider;
                try
                {
                    var context = services.GetRequiredService<AppDbContext>();
                    var logger = services.GetRequiredService<ILogger<AppDbContext>>();

                    logger.LogInformation("开始初始化游戏化相关表...");

                    var initializer = new GamificationTablesInitializer(context,
                        services.GetRequiredService<ILogger<GamificationTablesInitializer>>());
                    await initializer.InitializeAsync();

                    logger.LogInformation("游戏化相关表初始化完成");
                }
                catch (Exception ex)
                {
                    var logger = services.GetRequiredService<ILogger<AppDbContext>>();
                    logger.LogError(ex, "初始化游戏化相关表时发生错误");
                    // 不抛出异常，允许应用继续启动
                }
            }

            return host;
        }
    }
}