# 🗄️ 数据库设计文档

## 📊 数据库概览

- **数据库引擎**: MySQL 8.0
- **字符集**: utf8mb4 (完整Unicode支持)
- **表数量**: 69张表
- **设计模式**: 双重主键策略 + Clean Architecture

## 🏗️ 核心设计原则

### 双重主键策略
```sql
-- 传统核心模块 (INT主键) - 保持稳定
users: Id INT AUTO_INCREMENT
assets: Id INT AUTO_INCREMENT  
locations: Id INT AUTO_INCREMENT

-- V2新模块 (BIGINT主键) - 支持海量数据
tasks: TaskId BIGINT AUTO_INCREMENT
spare_parts: id BIGINT AUTO_INCREMENT
gamification_userstats: UserId BIGINT
```

### 命名规范
- **表名**: snake_case (users, asset_types)
- **字段名**: snake_case 或 PascalCase (根据模块)
- **外键**: 表名_id 或 引用表Id
- **时间字段**: created_at, updated_at 或 CreatedAt, UpdatedAt

## 📋 核心数据表

### 1. 👥 用户权限模块

#### users (用户表) - 核心表
```sql
CREATE TABLE users (
  Id INT AUTO_INCREMENT PRIMARY KEY,
  Username VARCHAR(50) UNIQUE NOT NULL,
  PasswordHash VARCHAR(200) NOT NULL,
  Name VARCHAR(50) NOT NULL,
  Email VARCHAR(100) UNIQUE,
  Mobile VARCHAR(20),
  DepartmentId INT,
  Position VARCHAR(50),
  IsActive BOOLEAN DEFAULT TRUE,
  CreatedAt DATETIME NOT NULL,
  UpdatedAt DATETIME NOT NULL,
  Avatar VARCHAR(200),
  
  FOREIGN KEY (DepartmentId) REFERENCES departments(Id)
);
```

#### departments (部门表)
```sql
CREATE TABLE departments (
  Id INT AUTO_INCREMENT PRIMARY KEY,
  Name VARCHAR(50) UNIQUE NOT NULL,
  Code VARCHAR(20) UNIQUE NOT NULL,
  Description VARCHAR(200),
  ParentId INT,
  ManagerId INT,
  IsActive BOOLEAN DEFAULT TRUE,
  CreatedAt DATETIME NOT NULL,
  UpdatedAt DATETIME NOT NULL,
  
  FOREIGN KEY (ParentId) REFERENCES departments(Id),
  FOREIGN KEY (ManagerId) REFERENCES users(Id)
);
```

### 2. 📦 资产管理模块

#### assets (资产表) - 核心业务表
```sql
CREATE TABLE assets (
  Id INT AUTO_INCREMENT PRIMARY KEY,
  assetCode VARCHAR(30) UNIQUE NOT NULL COMMENT '资产编号(IT-类型-年月日-序号)',
  FinancialCode VARCHAR(50) COMMENT '财务编号',
  Name VARCHAR(100) NOT NULL COMMENT '资产名称',
  AssetTypeId INT NOT NULL,
  SerialNumber VARCHAR(100) COMMENT '序列号',
  Model VARCHAR(100) COMMENT '型号',
  Brand VARCHAR(100) COMMENT '品牌',
  PurchaseDate DATETIME COMMENT '购买日期',
  WarrantyExpireDate DATETIME COMMENT '保修到期日',
  Price DECIMAL(18,2) COMMENT '价格',
  LocationId INT COMMENT '位置ID',
  DepartmentId INT,
  Status INT DEFAULT 0 COMMENT '状态：0闲置，1在用，2维修中，3报废',
  Notes VARCHAR(500) COMMENT '备注',
  CreatedAt DATETIME NOT NULL,
  UpdatedAt DATETIME NOT NULL,
  
  FOREIGN KEY (AssetTypeId) REFERENCES assettypes(Id),
  FOREIGN KEY (LocationId) REFERENCES locations(Id),
  FOREIGN KEY (DepartmentId) REFERENCES departments(Id),
  
  INDEX idx_assetcode (assetCode),
  INDEX idx_status_location (Status, LocationId),
  INDEX idx_type_status (AssetTypeId, Status)
);
```

#### assettypes (资产类型表) - 支持树形结构
```sql
CREATE TABLE assettypes (
  Id INT AUTO_INCREMENT PRIMARY KEY,
  Name VARCHAR(50) UNIQUE NOT NULL,
  Code VARCHAR(50) UNIQUE NOT NULL,
  Description VARCHAR(200),
  ParentId INT COMMENT '父类型ID',
  RequireSerialNumber BOOLEAN DEFAULT TRUE,
  SortOrder INT DEFAULT 0,
  IsActive BOOLEAN DEFAULT TRUE,
  CreatedAt DATETIME NOT NULL,
  UpdatedAt DATETIME NOT NULL,
  
  FOREIGN KEY (ParentId) REFERENCES assettypes(Id)
);
```

### 3. 📍 位置管理模块

#### locations (位置表) - 5级层次结构
```sql
CREATE TABLE locations (
  Id INT AUTO_INCREMENT PRIMARY KEY,
  Code VARCHAR(50) UNIQUE NOT NULL,
  Name VARCHAR(100) NOT NULL,
  Type INT DEFAULT 0 COMMENT '位置类型：0厂区，1产线，2工序，3工位，4设备位置',
  ParentId INT COMMENT '父位置ID',
  Path VARCHAR(200) COMMENT '层级路径',
  Description VARCHAR(200),
  DefaultDepartmentId INT COMMENT '默认部门ID',
  DefaultResponsiblePersonId INT COMMENT '默认负责人ID',
  IsActive BOOLEAN DEFAULT TRUE,
  CreatedAt DATETIME NOT NULL,
  UpdatedAt DATETIME NOT NULL,
  level TINYINT DEFAULT 3 COMMENT '位置级别(1-5)',
  
  FOREIGN KEY (ParentId) REFERENCES locations(Id),
  FOREIGN KEY (DefaultDepartmentId) REFERENCES departments(Id),
  FOREIGN KEY (DefaultResponsiblePersonId) REFERENCES users(Id),
  
  INDEX idx_type_parent (Type, ParentId),
  INDEX idx_level (level)
);
```

### 4. ⚡ 任务管理模块 (V2 - BIGINT主键)

#### tasks (任务表) - V2核心表
```sql
CREATE TABLE tasks (
  TaskId BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '任务主键ID',
  ProjectId BIGINT COMMENT '所属项目ID',
  ParentTaskId BIGINT COMMENT '父任务ID',
  CreatorUserId INT NOT NULL COMMENT '创建者用户ID (关联 users.Id)',
  AssigneeUserId INT COMMENT '负责人用户ID (关联 users.Id)',
  AssetId INT COMMENT '关联资产ID (关联 assets.Id)',
  LocationId INT COMMENT '关联位置ID (关联 locations.Id)',
  Name VARCHAR(255) NOT NULL COMMENT '任务名称',
  Description TEXT COMMENT '任务描述',
  Status VARCHAR(50) DEFAULT 'Todo' COMMENT '任务状态',
  Priority VARCHAR(50) DEFAULT 'Medium' COMMENT '优先级',
  TaskType VARCHAR(50) DEFAULT 'Normal' COMMENT '任务类型',
  PlanStartDate DATETIME COMMENT '计划开始时间',
  PlanEndDate DATETIME COMMENT '计划结束时间',
  ActualStartDate DATETIME COMMENT '实际开始时间',
  ActualEndDate DATETIME COMMENT '实际完成时间',
  CreationTimestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
  LastUpdatedTimestamp DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  Progress INT DEFAULT 0 COMMENT '任务进度百分比(0-100)',
  Points INT DEFAULT 0 COMMENT '完成任务可获得的积分',
  CompletedByUserId INT COMMENT '完成用户ID',
  IsDeleted BOOLEAN DEFAULT FALSE,
  
  FOREIGN KEY (CreatorUserId) REFERENCES users(Id),
  FOREIGN KEY (AssigneeUserId) REFERENCES users(Id),
  FOREIGN KEY (AssetId) REFERENCES assets(Id),
  FOREIGN KEY (LocationId) REFERENCES locations(Id),
  
  INDEX idx_status (Status),
  INDEX idx_assignee (AssigneeUserId),
  INDEX idx_creator (CreatorUserId),
  INDEX idx_asset (AssetId),
  INDEX idx_created_time (CreationTimestamp)
);
```

#### comments (任务评论表)
```sql
CREATE TABLE comments (
  CommentId BIGINT AUTO_INCREMENT PRIMARY KEY,
  TaskId BIGINT NOT NULL,
  AuthorUserId INT NOT NULL COMMENT '评论者用户ID',
  Content TEXT NOT NULL,
  CreationTimestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
  LastUpdatedTimestamp DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  IsDeleted BOOLEAN DEFAULT FALSE,
  
  FOREIGN KEY (TaskId) REFERENCES tasks(TaskId),
  FOREIGN KEY (AuthorUserId) REFERENCES users(Id),
  
  INDEX idx_task_created (TaskId, CreationTimestamp)
);
```

### 5. 🔧 备件管理模块

#### spare_parts (备件表)
```sql
CREATE TABLE spare_parts (
  id BIGINT AUTO_INCREMENT PRIMARY KEY,
  code VARCHAR(50) UNIQUE NOT NULL COMMENT '备件编号',
  material_number VARCHAR(50) COMMENT '物料编号',
  name VARCHAR(100) NOT NULL COMMENT '备件名称',
  type_id INT NOT NULL COMMENT '备件类型ID',
  spec VARCHAR(200) COMMENT '规格型号',
  brand VARCHAR(100) COMMENT '品牌',
  quantity INT DEFAULT 0 COMMENT '当前库存量',
  unit VARCHAR(20) NOT NULL COMMENT '单位',
  min_threshold INT DEFAULT 5 COMMENT '最小安全库存',
  warning_threshold INT DEFAULT 10 COMMENT '预警库存',
  location_id INT NOT NULL COMMENT '库位ID',
  purchase_price DECIMAL(10,2) COMMENT '采购价格',
  supplier_id INT COMMENT '默认供应商',
  notes VARCHAR(500) COMMENT '备注',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_code (code),
  INDEX idx_type (type_id),
  INDEX idx_location (location_id),
  INDEX idx_low_stock (quantity, min_threshold)
);
```

#### spare_part_transactions (备件事务表)
```sql
CREATE TABLE spare_part_transactions (
  id BIGINT AUTO_INCREMENT PRIMARY KEY,
  part_id BIGINT NOT NULL,
  transaction_type VARCHAR(20) NOT NULL COMMENT 'INBOUND, OUTBOUND, ADJUSTMENT',
  quantity_change INT NOT NULL COMMENT '数量变化(正数入库,负数出库)',
  quantity_before INT NOT NULL COMMENT '操作前库存',
  quantity_after INT NOT NULL COMMENT '操作后库存',
  operator_user_id INT NOT NULL,
  related_task_id BIGINT COMMENT '关联任务ID',
  notes VARCHAR(500),
  transaction_time DATETIME DEFAULT CURRENT_TIMESTAMP,
  
  FOREIGN KEY (part_id) REFERENCES spare_parts(id),
  FOREIGN KEY (operator_user_id) REFERENCES users(Id),
  FOREIGN KEY (related_task_id) REFERENCES tasks(TaskId),
  
  INDEX idx_part_time (part_id, transaction_time),
  INDEX idx_type_time (transaction_type, transaction_time)
);
```

### 6. 🎮 游戏化系统

#### gamification_userstats (用户游戏化统计)
```sql
CREATE TABLE gamification_userstats (
  UserId BIGINT PRIMARY KEY COMMENT '用户统计ID (BIGINT)',
  CoreUserId INT UNIQUE NOT NULL COMMENT '对应的核心用户ID',
  CurrentXP INT DEFAULT 0 COMMENT '当前经验值',
  CurrentLevel INT DEFAULT 1 COMMENT '当前等级',
  PointsBalance INT DEFAULT 0 COMMENT '当前可用积分',
  CompletedTasksCount INT DEFAULT 0 COMMENT '累计完成任务数',
  OnTimeTasksCount INT DEFAULT 0 COMMENT '累计按时完成任务数',
  StreakCount INT DEFAULT 0 COMMENT '当前连续活动天数',
  LastActivityTimestamp DATETIME COMMENT '最后活跃时间',
  LastUpdatedTimestamp DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  FOREIGN KEY (CoreUserId) REFERENCES users(Id),
  
  INDEX idx_level_xp (CurrentLevel, CurrentXP),
  INDEX idx_points (PointsBalance)
);
```

## 🔗 关键关系设计

### 跨模块关联策略
```sql
-- V2模块关联V1核心数据的设计模式
-- 任务表关联资产和用户
tasks.AssigneeUserId (BIGINT) → users.Id (INT)  -- 类型转换关联
tasks.AssetId (BIGINT) → assets.Id (INT)        -- 类型转换关联

-- 游戏化统计的双重关联设计
gamification_userstats.UserId (BIGINT PK)       -- 逻辑主键
gamification_userstats.CoreUserId (INT FK)      -- 物理外键到users.Id
```

### 索引设计策略
```sql
-- 复合索引优化查询性能
CREATE INDEX idx_tasks_status_assignee ON tasks(Status, AssigneeUserId);
CREATE INDEX idx_assets_type_status ON assets(AssetTypeId, Status);
CREATE INDEX idx_locations_type_parent ON locations(Type, ParentId);

-- 覆盖索引提升查询效率
CREATE INDEX idx_tasks_list_covering ON tasks(Status, AssigneeUserId, CreationTimestamp)
INCLUDE (TaskId, Name, Priority);
```

## 📊 性能优化设计

### 物化视图
```sql
-- 资产KPI统计视图
CREATE VIEW mv_asset_kpi AS
SELECT 
  COUNT(*) as total_assets,
  COUNT(CASE WHEN Status = 1 THEN 1 END) as in_use_assets,
  COUNT(CASE WHEN Status = 0 THEN 1 END) as idle_assets,
  SUM(Price) as total_value,
  AVG(DATEDIFF(NOW(), PurchaseDate)) as avg_age_days
FROM assets 
WHERE IsDeleted = FALSE;

-- 资产统计汇总视图
CREATE VIEW mv_asset_statistics AS
SELECT 
  at.Name as asset_type_name,
  COUNT(a.Id) as asset_count,
  SUM(a.Price) as total_value,
  AVG(a.Price) as avg_price
FROM assets a
JOIN assettypes at ON a.AssetTypeId = at.Id
WHERE a.IsDeleted = FALSE
GROUP BY at.Id, at.Name;
```

### 分区策略 (大表优化)
```sql
-- 历史数据表按时间分区
CREATE TABLE asset_histories (
  Id BIGINT AUTO_INCREMENT,
  AssetId INT NOT NULL,
  OperationType INT NOT NULL,
  OperationTime DATETIME NOT NULL,
  Description TEXT,
  PRIMARY KEY (Id, OperationTime)
) PARTITION BY RANGE (YEAR(OperationTime)) (
  PARTITION p2023 VALUES LESS THAN (2024),
  PARTITION p2024 VALUES LESS THAN (2025),
  PARTITION p2025 VALUES LESS THAN (2026),
  PARTITION pmax VALUES LESS THAN MAXVALUE
);
```

## 🚨 数据约束与验证

### 业务规则约束
```sql
-- 资产编码格式约束
ALTER TABLE assets ADD CONSTRAINT chk_asset_code 
CHECK (assetCode REGEXP '^IT-[A-Z]+-[0-9]{8}-[0-9]{3}$');

-- 任务进度范围约束
ALTER TABLE tasks ADD CONSTRAINT chk_progress 
CHECK (Progress BETWEEN 0 AND 100);

-- 库存数量约束
ALTER TABLE spare_parts ADD CONSTRAINT chk_quantity 
CHECK (quantity >= 0);
```

### 触发器保证数据一致性
```sql
-- 资产状态变更时自动记录历史
DELIMITER $$
CREATE TRIGGER tr_asset_status_change 
AFTER UPDATE ON assets
FOR EACH ROW
BEGIN
  IF OLD.Status != NEW.Status THEN
    INSERT INTO assethistories (AssetId, OperationType, OperationTime, Description)
    VALUES (NEW.Id, 5, NOW(), 
      CONCAT('状态从 ', OLD.Status, ' 变更为 ', NEW.Status));
  END IF;
END$$
DELIMITER ;
```

## 📋 数据迁移与版本控制

### EF Core迁移文件
```
Migrations/
├── 20250528120919_InitClean.cs              # 初始化迁移
├── 20250601000000_AddPeriodicTaskDefaultFields.cs
├── 20250611000000_FixPeriodicTaskScheduleMapping.cs
├── 20250612073747_FixSnakeCaseNaming.cs     # 命名规范统一
└── AppDbContextModelSnapshot.cs             # 当前模型快照
```

### 数据库版本控制策略
```sql
-- 脚本执行日志表
CREATE TABLE _script_execution_log (
  id INT AUTO_INCREMENT PRIMARY KEY,
  step VARCHAR(100) NOT NULL,
  status VARCHAR(20) NOT NULL,
  message TEXT,
  executed_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

---

**数据库设计版本**: v2.0  
**最后更新**: 2025年6月21日  
**维护者**: 数据库架构师