// File: Application/Features/Tasks/Dtos/PeriodicTaskScheduleQueryParametersDto.cs
// Description: DTO for periodic task schedule query parameters.
#nullable enable
using System;

namespace ItAssetsSystem.Application.Features.Tasks.Dtos
{
    public class PeriodicTaskScheduleQueryParametersDto
    {
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 10;
        public string? SearchTerm { get; set; } // To search in Name or Description
        public string? Status { get; set; } // e.g., "Active", "Paused", "Completed"
        public long? TemplateTaskId { get; set; }
        public int? CreatorUserId { get; set; }
        public string? SortBy { get; set; } // e.g., "Name", "NextGenerationTime", "CreationTimestamp"
        public string? SortDirection { get; set; } // "asc" or "desc"
        public string? RecurrenceType { get; set; } // e.g., "Daily", "Weekly"
    }
} 