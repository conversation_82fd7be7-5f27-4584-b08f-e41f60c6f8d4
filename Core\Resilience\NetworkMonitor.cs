// IT资产管理系统 - 网络监控器
// 文件路径: /Core/Resilience/NetworkMonitor.cs
// 功能: 提供网络连接监控功能，实现INetworkMonitor接口

using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Net.Http;
using System.Net.NetworkInformation;
using System.Threading;
using System.Threading.Tasks;

namespace ItAssetsSystem.Core.Resilience
{
    /// <summary>
    /// 网络监控器实现类
    /// </summary>
    public class NetworkMonitor : INetworkMonitor, IDisposable
    {
        private readonly ILogger<NetworkMonitor> _logger;
        private readonly NetworkMonitorOptions _options;
        private readonly HttpClient _httpClient;
        private Timer _checkTimer;
        private bool _isMonitoring;
        private NetworkStatus _currentStatus = NetworkStatus.Unknown;
        private readonly object _statusLock = new object();

        /// <summary>
        /// 当前网络状态
        /// </summary>
        public NetworkStatus CurrentStatus
        {
            get
            {
                lock (_statusLock)
                {
                    return _currentStatus;
                }
            }
            private set
            {
                bool hasChanged = false;
                NetworkStatus oldStatus;

                lock (_statusLock)
                {
                    oldStatus = _currentStatus;
                    
                    if (_currentStatus != value)
                    {
                        _currentStatus = value;
                        hasChanged = true;
                    }
                }

                if (hasChanged)
                {
                    _logger.LogInformation($"网络状态变化: {oldStatus} -> {value}");
                    NetworkStatusChanged?.Invoke(this, value);
                }
            }
        }

        /// <summary>
        /// 网络状态变化事件
        /// </summary>
        public event EventHandler<NetworkStatus> NetworkStatusChanged;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志记录器</param>
        /// <param name="options">网络监控选项</param>
        public NetworkMonitor(ILogger<NetworkMonitor> logger, IOptions<NetworkMonitorOptions> options)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
            
            _httpClient = new HttpClient
            {
                Timeout = TimeSpan.FromMilliseconds(_options.TimeoutMs)
            };

            if (_options.AutoStartMonitoring)
            {
                Start();
            }
        }

        /// <summary>
        /// 启动网络监控
        /// </summary>
        public void Start()
        {
            if (_isMonitoring)
            {
                return;
            }

            _logger.LogInformation("启动网络监控");
            _isMonitoring = true;

            // 立即执行一次检查
            _ = CheckNetworkStatusAsync();

            // 定时执行网络检查
            _checkTimer = new Timer(async _ => await CheckNetworkStatusAsync(), null, 
                _options.CheckIntervalMs, _options.CheckIntervalMs);
        }

        /// <summary>
        /// 停止网络监控
        /// </summary>
        public void Stop()
        {
            if (!_isMonitoring)
            {
                return;
            }

            _logger.LogInformation("停止网络监控");
            _isMonitoring = false;

            _checkTimer?.Change(Timeout.Infinite, Timeout.Infinite);
            _checkTimer?.Dispose();
            _checkTimer = null;
        }

        /// <summary>
        /// 检查网络状态
        /// </summary>
        private async Task CheckNetworkStatusAsync()
        {
            try
            {
                NetworkStatus status = await CheckConnectionAsync();
                CurrentStatus = status;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查网络状态时发生错误");
                CurrentStatus = NetworkStatus.Unknown;
            }
        }

        /// <summary>
        /// 检查网络连接
        /// </summary>
        public async Task<NetworkStatus> CheckConnectionAsync()
        {
            _logger.LogDebug("正在检查网络连接...");
            
            // 重试策略
            for (int i = 0; i < _options.RetryCount; i++)
            {
                try
                {
                    string url = _options.CheckUrl;
                    
                    // 确保URL是有效的
                    if (!url.StartsWith("http://") && !url.StartsWith("https://"))
                    {
                        url = "https://" + url;
                    }
                    
                    // 发送HEAD请求以减少数据传输
                    var request = new HttpRequestMessage(HttpMethod.Head, url);
                    
                    var response = await _httpClient.SendAsync(request);
                    response.EnsureSuccessStatusCode();
                    
                    _logger.LogDebug("网络连接成功");
                    return NetworkStatus.Connected;
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, $"第 {i+1}/{_options.RetryCount} 次网络检查失败");
                    
                    if (i < _options.RetryCount - 1)
                    {
                        await Task.Delay(500); // 短暂延迟后重试
                    }
                }
            }
            
            _logger.LogWarning("网络连接不可用");
            return NetworkStatus.Disconnected;
        }

        /// <summary>
        /// 判断网络是否已连接
        /// </summary>
        public bool IsConnected()
        {
            return CurrentStatus == NetworkStatus.Connected;
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Stop();
            _httpClient?.Dispose();
        }
    }
} 