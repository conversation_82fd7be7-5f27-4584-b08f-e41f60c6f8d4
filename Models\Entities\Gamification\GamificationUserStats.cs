using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ItAssetsSystem.Models.Entities.Gamification
{
    /// <summary>
    /// 游戏化用户统计实体
    /// </summary>
    [Table("gamification_userstats")]
    public class GamificationUserStats
    {
        /// <summary>
        /// 用户统计关联ID (BIGINT PK)
        /// </summary>
        [Key]
        [Column("UserId")]
        public long UserId { get; set; }

        /// <summary>
        /// 对应的核心用户ID (关联 users.Id - INT)
        /// </summary>
        [Column("CoreUserId")]
        public int CoreUserId { get; set; }

        /// <summary>
        /// 当前经验值
        /// </summary>
        [Column("CurrentXP")]
        public int CurrentXP { get; set; } = 0;

        /// <summary>
        /// 当前等级
        /// </summary>
        [Column("CurrentLevel")]
        public int CurrentLevel { get; set; } = 1;

        /// <summary>
        /// 当前可用积分
        /// </summary>
        [Column("PointsBalance")]
        public int PointsBalance { get; set; } = 0;

        /// <summary>
        /// 累计完成任务数
        /// </summary>
        [Column("CompletedTasksCount")]
        public int CompletedTasksCount { get; set; } = 0;

        /// <summary>
        /// 累计按时完成任务数
        /// </summary>
        [Column("OnTimeTasksCount")]
        public int OnTimeTasksCount { get; set; } = 0;

        /// <summary>
        /// 当前连续活动/完成任务天数
        /// </summary>
        [Column("StreakCount")]
        public int StreakCount { get; set; } = 0;

        /// <summary>
        /// 最后活跃时间戳
        /// </summary>
        [Column("LastActivityTimestamp")]
        public DateTime? LastActivityTimestamp { get; set; }

        /// <summary>
        /// 上次增加连续记录的日期
        /// </summary>
        [Column("LastStreakTimestamp")]
        public DateTime? LastStreakTimestamp { get; set; }

        /// <summary>
        /// 最后更新时间戳
        /// </summary>
        [Column("LastUpdatedTimestamp")]
        public DateTime LastUpdatedTimestamp { get; set; } = DateTime.Now;

        /// <summary>
        /// 金币余额
        /// </summary>
        [Column("CoinsBalance")]
        public int CoinsBalance { get; set; } = 0;

        /// <summary>
        /// 钻石余额
        /// </summary>
        [Column("DiamondsBalance")]
        public int DiamondsBalance { get; set; } = 0;

        /// <summary>
        /// 累计创建任务数
        /// </summary>
        [Column("CreatedTasksCount")]
        public int CreatedTasksCount { get; set; } = 0;

        /// <summary>
        /// 累计领取任务数
        /// </summary>
        [Column("ClaimedTasksCount")]
        public int ClaimedTasksCount { get; set; } = 0;



        // 导航属性
        /// <summary>
        /// 关联的用户
        /// </summary>
        [ForeignKey("CoreUserId")]
        public virtual User User { get; set; } = null!;
    }
}
