// IT资产管理系统 - SQL查询日志拦截器
// 文件路径: /Core/Middleware/SqlQueryLoggingInterceptor.cs
// 功能: 拦截并记录数据库SQL查询

using Microsoft.EntityFrameworkCore.Diagnostics;
using Microsoft.Extensions.Logging;
using System;
using System.Data.Common;
using System.Threading;
using System.Threading.Tasks;
using System.Diagnostics;

namespace ItAssetsSystem.Core.Middleware
{
    /// <summary>
    /// SQL查询日志拦截器
    /// </summary>
    public class SqlQueryLoggingInterceptor : DbCommandInterceptor
    {
        private readonly ILogger<SqlQueryLoggingInterceptor> _logger;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志记录器</param>
        public SqlQueryLoggingInterceptor(ILogger<SqlQueryLoggingInterceptor> logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// 命令执行前
        /// </summary>
        public override InterceptionResult<DbDataReader> ReaderExecuting(
            DbCommand command,
            CommandEventData eventData,
            InterceptionResult<DbDataReader> result)
        {
            LogCommand(command, eventData.CommandSource);
            return result;
        }

        /// <summary>
        /// 异步命令执行前
        /// </summary>
        public override ValueTask<InterceptionResult<DbDataReader>> ReaderExecutingAsync(
            DbCommand command,
            CommandEventData eventData,
            InterceptionResult<DbDataReader> result,
            CancellationToken cancellationToken = default)
        {
            LogCommand(command, eventData.CommandSource);
            return new ValueTask<InterceptionResult<DbDataReader>>(result);
        }

        /// <summary>
        /// 命令执行后
        /// </summary>
        public override DbDataReader ReaderExecuted(
            DbCommand command,
            CommandExecutedEventData eventData,
            DbDataReader result)
        {
            LogExecutedCommand(command, eventData);
            return result;
        }

        /// <summary>
        /// 异步命令执行后
        /// </summary>
        public override ValueTask<DbDataReader> ReaderExecutedAsync(
            DbCommand command,
            CommandExecutedEventData eventData,
            DbDataReader result,
            CancellationToken cancellationToken = default)
        {
            LogExecutedCommand(command, eventData);
            return new ValueTask<DbDataReader>(result);
        }

        /// <summary>
        /// 非查询命令执行前
        /// </summary>
        public override InterceptionResult<int> NonQueryExecuting(
            DbCommand command,
            CommandEventData eventData,
            InterceptionResult<int> result)
        {
            LogCommand(command, eventData.CommandSource);
            return result;
        }

        /// <summary>
        /// 异步非查询命令执行前
        /// </summary>
        public override ValueTask<InterceptionResult<int>> NonQueryExecutingAsync(
            DbCommand command,
            CommandEventData eventData,
            InterceptionResult<int> result,
            CancellationToken cancellationToken = default)
        {
            LogCommand(command, eventData.CommandSource);
            return new ValueTask<InterceptionResult<int>>(result);
        }

        /// <summary>
        /// 非查询命令执行后
        /// </summary>
        public override int NonQueryExecuted(
            DbCommand command,
            CommandExecutedEventData eventData,
            int result)
        {
            LogExecutedCommand(command, eventData);
            return result;
        }

        /// <summary>
        /// 异步非查询命令执行后
        /// </summary>
        public override ValueTask<int> NonQueryExecutedAsync(
            DbCommand command,
            CommandExecutedEventData eventData,
            int result,
            CancellationToken cancellationToken = default)
        {
            LogExecutedCommand(command, eventData);
            return new ValueTask<int>(result);
        }

        /// <summary>
        /// 标量查询执行前
        /// </summary>
        public override InterceptionResult<object> ScalarExecuting(
            DbCommand command,
            CommandEventData eventData,
            InterceptionResult<object> result)
        {
            LogCommand(command, eventData.CommandSource);
            return result;
        }

        /// <summary>
        /// 异步标量查询执行前
        /// </summary>
        public override ValueTask<InterceptionResult<object>> ScalarExecutingAsync(
            DbCommand command,
            CommandEventData eventData,
            InterceptionResult<object> result,
            CancellationToken cancellationToken = default)
        {
            LogCommand(command, eventData.CommandSource);
            return new ValueTask<InterceptionResult<object>>(result);
        }

        /// <summary>
        /// 标量查询执行后
        /// </summary>
        public override object ScalarExecuted(
            DbCommand command,
            CommandExecutedEventData eventData,
            object result)
        {
            LogExecutedCommand(command, eventData);
            return result;
        }

        /// <summary>
        /// 异步标量查询执行后
        /// </summary>
        public override ValueTask<object> ScalarExecutedAsync(
            DbCommand command,
            CommandExecutedEventData eventData,
            object result,
            CancellationToken cancellationToken = default)
        {
            LogExecutedCommand(command, eventData);
            return new ValueTask<object>(result);
        }

        /// <summary>
        /// 记录命令信息
        /// </summary>
        private void LogCommand(DbCommand command, CommandSource commandSource)
        {
            // 尝试获取调用堆栈信息
            var stackTrace = new StackTrace(true);
            var frames = stackTrace.GetFrames();
            
            // 尝试找到非EF Core和非此拦截器的第一个调用帧
            string callerInfo = "未知";
            if (frames != null)
            {
                foreach (var frame in frames)
                {
                    var method = frame.GetMethod();
                    if (method == null) continue;
                    
                    var declaringType = method.DeclaringType;
                    if (declaringType == null) continue;
                    
                    var ns = declaringType.Namespace ?? string.Empty;
                    
                    // 排除EF Core和此拦截器的命名空间
                    if (!ns.StartsWith("Microsoft.EntityFrameworkCore") && 
                        !ns.StartsWith("System.Data") &&
                        !ns.StartsWith("System.Threading") &&
                        !declaringType.FullName.Contains("SqlQueryLoggingInterceptor"))
                    {
                        var fileName = frame.GetFileName() ?? "未知文件";
                        var lineNumber = frame.GetFileLineNumber();
                        callerInfo = $"{declaringType.FullName}.{method.Name} ({fileName}:行{lineNumber})";
                        break;
                    }
                }
            }
            
            var commandText = command.CommandText;
            var parameters = string.Empty;
            
            // 记录参数
            foreach (DbParameter parameter in command.Parameters)
            {
                parameters += $"\n    {parameter.ParameterName}: {parameter.Value ?? "NULL"} ({parameter.DbType})";
            }
            
            // 记录SQL和参数
            _logger.LogInformation(
                "执行SQL查询 (来源: {CommandSource}, 调用者: {CallerInfo}):\n" +
                "SQL: {CommandText}\n" +
                "参数: {Parameters}",
                commandSource.ToString(), callerInfo, commandText, 
                string.IsNullOrEmpty(parameters) ? "(无)" : parameters);
        }

        /// <summary>
        /// 记录命令执行结果
        /// </summary>
        private void LogExecutedCommand(DbCommand command, CommandExecutedEventData eventData)
        {
            var commandText = command.CommandText?.Substring(0, Math.Min(command.CommandText.Length, 100)) + "...";
            var elapsed = eventData.Duration.TotalMilliseconds;
            
            // 记录SQL执行结果
            _logger.LogInformation(
                "SQL查询执行完成: 耗时 {ElapsedMilliseconds}ms\n" +
                "SQL: {CommandText}",
                elapsed, commandText);
        }
    }
} 