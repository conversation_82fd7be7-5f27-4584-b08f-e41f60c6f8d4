<template>
  <div class="modern-task-management" :class="{ dark: isDarkTheme }">
    <!-- 顶部操作栏 -->
    <div class="task-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">任务管理</h1>
          <el-badge :value="unreadNotificationsCount" class="notification-badge" v-if="unreadNotificationsCount > 0">
            <el-button
              circle
              @click="showNotificationCenter = !showNotificationCenter"
              class="notification-btn"
            >
              <el-icon><Bell /></el-icon>
            </el-button>
          </el-badge>
        </div>

        <div class="header-right">
          <!-- 搜索框 -->
          <el-input
            v-model="searchQuery"
            placeholder="搜索任务..."
            prefix-icon="Search"
            class="search-input"
            clearable
          />

          <!-- 筛选器 -->
          <el-select v-model="filter.status" placeholder="状态" clearable class="filter-select">
            <el-option label="全部" value="" />
            <el-option v-for="status in taskStatusOptions" :key="status" :label="getStatusText(status)" :value="status" />
          </el-select>

          <el-select v-model="filter.priority" placeholder="优先级" clearable class="filter-select">
            <el-option label="全部" value="" />
            <el-option label="高" value="High" />
            <el-option label="中" value="Medium" />
            <el-option label="低" value="Low" />
          </el-select>

          <!-- 视图切换 -->
          <el-button-group class="view-toggle">
            <el-button
              :type="activeView === 'kanban' ? 'primary' : ''"
              @click="activeView = 'kanban'"
            >
              <el-icon><Grid /></el-icon>
            </el-button>
            <el-button
              :type="activeView === 'list' ? 'primary' : ''"
              @click="activeView = 'list'"
            >
              <el-icon><List /></el-icon>
            </el-button>
          </el-button-group>

          <!-- 快速创建任务 -->
          <QuickTaskCreator
            trigger-text="快速创建"
            trigger-class="quick-create-btn"
            @created="onTaskCreated"
            @expand-to-full-form="handleExpandToFullForm"
          />

          <!-- 详细创建任务 -->
          <el-button @click="showCreateDialog = true" class="detail-create-btn">
            <el-icon><Setting /></el-icon>
            详细创建
          </el-button>
        </div>
      </div>
    </div>

    <!-- 批量操作栏 -->
    <div 
      v-if="selectedTaskIds.length > 0" 
      class="batch-actions-bar"
      :class="{ visible: selectedTaskIds.length > 0 }"
    >
      <div class="flex items-center gap-2">
        <span class="text-sm text-gray-600 dark:text-gray-300">
          已选择 {{ selectedTaskIds.length }} 个任务
        </span>
        <el-button size="small" @click="clearSelection">取消选择</el-button>
      </div>
      <div class="flex items-center gap-2">
        <el-button size="small" @click="showBatchAssignDialog = true">
          <el-icon><User /></el-icon>
          批量分配
        </el-button>
        <el-button size="small" @click="showBatchStatusDialog = true">
          <el-icon><Edit /></el-icon>
          批量修改状态
        </el-button>
        <el-button size="small" type="danger" @click="batchDeleteTasks">
          <el-icon><Delete /></el-icon>
          批量删除
        </el-button>
      </div>
    </div>

    <!-- 看板视图 -->
    <div v-if="activeView === 'kanban'" class="kanban-board">
      <div 
        v-for="column in taskColumns" 
        :key="column.status" 
        class="task-column"
      >
        <!-- 列头 -->
        <div class="task-column-header">
          <div class="column-title-wrapper">
            <el-icon :class="`text-${getStatusColor(column.status)}`">
              <component :is="column.icon" />
            </el-icon>
            <span class="task-column-title">{{ column.title }}</span>
            <span class="task-count">{{ column.tasks.length }}</span>
          </div>
          <el-button
            size="small"
            text
            @click="addTaskToColumn(column.status)"
            v-if="column.status !== '已逾期'"
          >
            <el-icon><Plus /></el-icon>
          </el-button>
        </div>

        <!-- 任务卡片容器 -->
        <div class="task-cards-container">
          <VueDraggable
            v-model="column.tasks"
            :animation="250"
            group="tasks"
            item-key="taskId"
            class="task-list"
            ghost-class="ghost-card"
            drag-class="drag-card"
            @start="onDragStart"
            @end="onDragEnd($event, column.status)"
          >
            <EnhancedTaskCard
              v-for="task in column.tasks"
              :key="task.taskId"
              :task="task"
              :selected="selectedTaskIds.includes(task.taskId)"
              :class="['in-kanban']"
              @select="toggleTaskSelection"
              @click="showTaskDetail"
              @quick-action="handleTaskQuickAction"
              @status-change="handleQuickStatusChange"
            />
          </VueDraggable>
        </div>
      </div>
    </div>

    <!-- 列表视图 -->
    <div v-else-if="activeView === 'list'" class="list-view">
      <el-table
        :data="filteredTasks"
        @selection-change="handleSelectionChange"
        stripe
        class="modern-table"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="name" label="任务名称" min-width="200">
          <template #default="{ row }">
            <div class="flex items-center gap-2">
              <el-tag
                :type="getPriorityTagType(row.priority)"
                size="small"
                round
              >
                {{ row.priority }}
              </el-tag>
              <span 
                class="font-medium cursor-pointer hover:text-blue-600"
                @click="showTaskDetail(row)"
              >
                {{ row.name }}
              </span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)" size="small">
              {{ row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="assigneeUserName" label="负责人" width="120" />
        <el-table-column prop="planEndDate" label="截止时间" width="120">
          <template #default="{ row }">
            <span :class="{ 'text-red-500': row.isOverdue }">
              {{ formatDate(row.planEndDate) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="progress" label="进度" width="100">
          <template #default="{ row }">
            <el-progress :percentage="row.progress" :stroke-width="6" />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button size="small" text @click="showTaskDetail(row)">
              查看
            </el-button>
            <el-button size="small" text @click="editTask(row)">
              编辑
            </el-button>
            <el-button size="small" text type="danger" @click="deleteTask(row.taskId)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 任务详情对话框 -->
    <TaskDetailDialog
      v-model="showDetailDialog"
      :task="selectedTask"
      @save="saveTask"
      @delete="deleteTask"
      @close="showDetailDialog = false"
    />

    <!-- 任务表单对话框 -->
    <TaskFormDialog
      v-model="showCreateDialog"
      :task="taskForm"
      :edit-mode="editMode"
      @save="saveTask"
      @close="closeTaskDialog"
    />

    <!-- 批量分配对话框 -->
    <BatchAssignDialog
      v-model="showBatchAssignDialog"
      :team-members="teamMembers"
      @assign="batchAssignTasks"
      @close="showBatchAssignDialog = false"
    />

    <!-- 批量状态修改对话框 -->
    <BatchStatusDialog
      v-model:visible="showBatchStatusDialog"
      :task-ids="selectedTaskIds"
    />

    <!-- 任务预览弹窗 -->
    <TaskPreviewPopup
      v-if="previewTask"
      :task="previewTask"
      :top="previewPosition.top"
      :left="previewPosition.left"
    />

    <!-- 通知中心 -->
    <NotificationCenter
      v-model:visible="showNotificationCenter"
      @view-task="showTaskDetail"
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Setting, Bell, Grid, List, User, Edit, Delete, Plus, Search
} from '@element-plus/icons-vue'
import { VueDraggable } from 'vue-draggable-plus'
import EnhancedTaskCard from '@/components/Tasks/EnhancedTaskCard.vue'
import TaskDetailDialog from './components/TaskDetailDialog.vue'
import TaskFormDialog from './components/TaskFormDialog.vue'
import BatchAssignDialog from './components/BatchAssignDialog.vue'
import BatchStatusDialog from './components/BatchStatusDialog.vue'
import TaskPreviewPopup from './components/TaskPreviewPopup.vue'
import NotificationCenter from './components/NotificationCenter.vue'
import QuickTaskCreator from '@/components/Tasks/QuickTaskCreator.vue'
import { useTaskEnhancedStore } from '@/stores/modules/taskEnhanced'
import { formatDate } from '@/utils/format'

// Store
const taskStore = useTaskEnhancedStore()

// 响应式状态
const activeView = ref('kanban')
const isDarkTheme = ref(false)
const searchQuery = ref('')
const filter = reactive({
  status: '',
  priority: '',
  assigneeId: ''
})

// 对话框状态
const showCreateDialog = ref(false)
const showDetailDialog = ref(false)
const showBatchAssignDialog = ref(false)
const showBatchStatusDialog = ref(false)
const showNotificationCenter = ref(false)

// 任务相关
const selectedTask = ref(null)
const selectedTaskIds = ref([])

// 预览相关
const previewTask = ref(null)
const previewPosition = reactive({ top: 0, left: 0 })

// 常量
const taskStatusOptions = ['Todo', 'InProgress', 'Done', 'Cancelled']
const teamMembers = ref([])
const notifications = reactive([])

// 计算属性
const filteredTasks = computed(() => {
  let tasks = taskStore.tasks
  
  // 搜索过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    tasks = tasks.filter(task => 
      task.name?.toLowerCase().includes(query) ||
      task.description?.toLowerCase().includes(query)
    )
  }
  
  // 状态过滤
  if (filter.status) {
    tasks = tasks.filter(task => task.status === filter.status)
  }
  
  // 优先级过滤
  if (filter.priority) {
    tasks = tasks.filter(task => task.priority === filter.priority)
  }
  
  return tasks
})

const taskColumns = computed(() => {
  const columns = [
    { status: 'Todo', title: '待办任务', icon: 'Memo' },
    { status: 'InProgress', title: '进行中', icon: 'Loading' },
    { status: 'Done', title: '已完成', icon: 'SuccessFilled' },
    { status: 'Overdue', title: '已逾期', icon: 'WarningFilled' }
  ]
  
  return columns.map(col => ({
    ...col,
    tasks: filteredTasks.value.filter(task => task.status === col.status)
  }))
})

const unreadNotificationsCount = computed(() => {
  return notifications.filter(n => !n.read).length
})

// 方法
const toggleTaskSelection = (taskId, event) => {
  if (event?.ctrlKey || event?.metaKey) {
    const index = selectedTaskIds.value.indexOf(taskId)
    if (index > -1) {
      selectedTaskIds.value.splice(index, 1)
    } else {
      selectedTaskIds.value.push(taskId)
    }
  } else {
    selectedTaskIds.value = selectedTaskIds.value.includes(taskId) ? [] : [taskId]
  }
}

const clearSelection = () => {
  selectedTaskIds.value = []
}

const showTaskDetail = (task) => {
  selectedTask.value = task
  showDetailDialog.value = true
}

const showTaskPreview = (task, event) => {
  previewTask.value = task
  previewPosition.top = event.clientY + 10
  previewPosition.left = event.clientX + 10
}

const hideTaskPreview = () => {
  previewTask.value = null
}

const addTaskToColumn = (status) => {
  // 实现添加任务到指定列的逻辑
}

// 状态文本转换
const getStatusText = (status) => {
  const texts = {
    'Todo': '待处理',
    'InProgress': '进行中', 
    'Done': '已完成',
    'Cancelled': '已取消'
  }
  return texts[status] || status
}

const editTask = (task) => {
  // 实现编辑任务的逻辑
}

const deleteTask = async (taskId) => {
  try {
    await ElMessageBox.confirm('确定要删除这个任务吗？', '确认删除', {
      type: 'warning'
    })
    await taskStore.deleteTask(taskId)
    ElMessage.success('任务删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '删除失败')
    }
  }
}

// 快速创建任务完成回调
const onTaskCreated = (newTask) => {
  // 刷新任务列表
  taskStore.fetchTasks()
  
  // 显示成功消息已在QuickTaskCreator组件中处理
  console.log('新任务已创建:', newTask)
  
  // 可选：自动选中新创建的任务
  if (newTask && newTask.taskId) {
    setTimeout(() => {
      showTaskDetail(newTask)
    }, 500)
  }
}

// 批量操作
const batchAssignTasks = async (assigneeIds) => {
  try {
    await taskStore.batchAssignTasks(selectedTaskIds.value, assigneeIds)
    ElMessage.success('批量分配成功')
    clearSelection()
  } catch (error) {
    ElMessage.error(error.message || '批量分配失败')
  }
}

const batchUpdateStatus = async (status) => {
  try {
    await taskStore.batchUpdateStatus(selectedTaskIds.value, status)
    ElMessage.success('批量状态修改成功')
    clearSelection()
  } catch (error) {
    ElMessage.error(error.message || '批量状态修改失败')
  }
}

const batchDeleteTasks = async () => {
  try {
    await ElMessageBox.confirm(`确定要删除选中的 ${selectedTaskIds.value.length} 个任务吗？`, '确认删除', {
      type: 'warning'
    })
    await taskStore.batchDeleteTasks(selectedTaskIds.value)
    ElMessage.success('批量删除成功')
    clearSelection()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '批量删除失败')
    }
  }
}

// 拖拽操作
const onDragStart = () => {
  // 拖拽开始
}

const onDragEnd = async (event, targetStatus) => {
  const taskId = event.item.dataset.taskId
  if (taskId && targetStatus !== '已逾期') {
    try {
      await taskStore.updateTaskStatus(taskId, targetStatus)
      ElMessage.success('任务状态更新成功')
    } catch (error) {
      ElMessage.error(error.message || '状态更新失败')
      // 刷新数据以恢复原状态
      await taskStore.fetchTasks()
    }
  }
}

// 任务卡片快速操作处理
const handleTaskQuickAction = async (payload) => {
  const { action, task } = payload
  
  try {
    switch (action) {
      case 'edit':
        editTask(task)
        break
      case 'assign':
        // 打开分配对话框
        selectedTaskIds.value = [task.taskId]
        showBatchAssignDialog.value = true
        break
      case 'clone':
        await cloneTask(task)
        break
      case 'delete':
        await deleteTask(task.taskId)
        break
      default:
        console.warn('未知的快速操作:', action)
    }
  } catch (error) {
    ElMessage.error(error.message || '操作失败')
  }
}

// 快速状态变更处理
const handleQuickStatusChange = async (payload) => {
  const { taskId, newStatus, oldStatus } = payload
  
  try {
    await taskStore.updateTaskStatus(taskId, newStatus)
    ElMessage.success(`任务状态已更新`)
  } catch (error) {
    ElMessage.error(error.message || '状态更新失败')
  }
}

// 克隆任务
const cloneTask = async (task) => {
  try {
    const clonedTask = {
      ...task,
      name: `${task.name} (副本)`,
      status: 'Todo',
      progress: 0,
      createTime: null,
      updateTime: null
    }
    delete clonedTask.taskId
    
    await taskStore.createTask(clonedTask)
    ElMessage.success('任务克隆成功')
  } catch (error) {
    ElMessage.error(error.message || '任务克隆失败')
  }
}

// 处理展开到完整表单
const handleExpandToFullForm = (formData) => {
  // 预填表单数据
  // 实现展开到完整表单的逻辑
}

// 列表视图选择处理
const handleSelectionChange = (selection) => {
  selectedTaskIds.value = selection.map(item => item.taskId)
}

// 辅助方法
const getStatusColor = (status) => {
  const colors = {
    'Todo': 'blue',
    'InProgress': 'orange',
    'Done': 'green',
    'Overdue': 'red'
  }
  return colors[status] || 'gray'
}

const getStatusTagType = (status) => {
  const types = {
    'Todo': '',
    'InProgress': 'warning',
    'Done': 'success',
    'Overdue': 'danger'
  }
  return types[status] || ''
}

const getPriorityTagType = (priority) => {
  const types = {
    'High': 'danger',
    'Medium': 'warning',
    'Low': 'success'
  }
  return types[priority] || ''
}

// 生命周期
onMounted(async () => {
  await taskStore.fetchTasks()
  // 加载团队成员等其他数据
})
</script>

<style scoped>
.modern-task-management {
  min-height: 100vh;
  background: #f5f7fa;
  transition: all 0.3s ease;
  padding-bottom: 0;
  position: relative;
}

.modern-task-management:has(.batch-actions-bar.visible) {
  padding-bottom: 80px;
}

.modern-task-management.dark {
  background: #1a1a1a;
}

.task-header {
  padding: 1.5rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 1.5rem;
}

.dark .task-header {
  background: #2d2d2d;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.page-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #303133;
  margin: 0;
}

.dark .page-title {
  color: #e0e0e0;
}

.notification-badge {
  margin-left: 0.5rem;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.search-input {
  width: 280px;
  min-width: 200px;
}

.filter-select {
  width: 120px;
  min-width: 100px;
}

.view-toggle {
  margin: 0 0.5rem;
}

.detail-create-btn {
  white-space: nowrap;
}

.kanban-board {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1.5rem;
  padding: 0 1.5rem;
  overflow-x: auto;
  min-width: fit-content;
  align-items: start;
}

@media (max-width: 1400px) {
  .kanban-board {
    grid-template-columns: repeat(3, minmax(320px, 1fr));
  }
}

@media (max-width: 1024px) {
  .kanban-board {
    grid-template-columns: repeat(2, minmax(320px, 1fr));
  }
}

@media (max-width: 768px) {
  .kanban-board {
    grid-template-columns: 1fr;
    padding: 0 1rem;
    gap: 1rem;
  }

  .header-content {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .header-right {
    flex-direction: column;
    align-items: stretch;
    gap: 0.5rem;
  }

  .search-input,
  .filter-select {
    width: 100%;
  }

  .task-column {
    min-height: 300px;
    max-height: 60vh;
  }

  .task-header {
    padding: 1rem;
  }
}

.task-column {
  background: white;
  border-radius: 12px;
  padding: 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  min-height: 500px;
  max-height: calc(100vh - 200px);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.dark .task-column {
  background: #2d2d2d;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.task-column-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.25rem;
  border-bottom: 2px solid #f0f0f0;
  background: #fafafa;
  flex-shrink: 0;
}

.dark .task-column-header {
  border-bottom-color: #404040;
  background: #3a3a3a;
}

.column-title-wrapper {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.task-column-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
}

.dark .task-column-title {
  color: #e0e0e0;
}

.task-count {
  background: #e6f7ff;
  color: #1890ff;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  min-width: 1.5rem;
  text-align: center;
}

.dark .task-count {
  background: #1f3a8a;
  color: #93c5fd;
}

.task-cards-container {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  min-height: 0;
}

.task-list {
  min-height: 200px;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  padding: 0;
}

.task-list .enhanced-task-card {
  margin-bottom: 0;
}

.ghost-card {
  opacity: 0.5;
}

.drag-card {
  transform: rotate(5deg);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

/* 修复任务卡片在看板中的布局 */
.task-cards-container .enhanced-task-card {
  position: relative;
  z-index: 1;
}

/* 批量操作栏样式 */
.batch-actions-bar {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.batch-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  font-size: 0.9rem;
  color: #495057;
}

.batch-actions {
  display: flex;
  gap: 0.5rem;
}

.list-view {
  padding: 0 1.5rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.dark .list-view {
  background: #2d2d2d;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.modern-table {
  border-radius: 8px;
  overflow: hidden;
}

.notification-btn {
  position: relative;
}

/* 快速创建按钮样式 */
.quick-create-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  font-weight: 600;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
  transition: all 0.3s ease;
}

.quick-create-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
}

/* 暗色主题调整 */
.dark .enhanced-task-card {
  background: #2d2d2d;
  border-color: #404040;
  color: #e0e0e0;
}

.dark .enhanced-task-card:hover {
  border-color: #667eea;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}
</style>