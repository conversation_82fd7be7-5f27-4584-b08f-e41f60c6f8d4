import request from '@/utils/request'

/**
 * 任务分类API服务
 */
export const taskCategoryApi = {
  /**
   * 获取所有任务分类
   */
  getAllCategories() {
    return request({
      url: '/v1.1/task-categories',
      method: 'get'
    })
  },

  /**
   * 获取启用的任务分类（用于下拉选择）
   */
  getActiveCategories() {
    return request({
      url: '/v1.1/task-categories/active',
      method: 'get'
    })
  },

  /**
   * 根据ID获取任务分类详情
   * @param {number} categoryId - 分类ID
   */
  getCategoryById(categoryId) {
    return request({
      url: `/v1.1/task-categories/${categoryId}`,
      method: 'get'
    })
  },

  /**
   * 分页查询任务分类
   * @param {Object} params - 查询参数
   * @param {number} params.pageNumber - 页码
   * @param {number} params.pageSize - 页大小
   * @param {string} params.searchKeyword - 搜索关键词
   * @param {boolean} params.isActive - 是否启用
   * @param {string} params.sortBy - 排序字段
   * @param {string} params.sortDirection - 排序方向
   */
  searchCategories(params) {
    return request({
      url: '/v1.1/task-categories/search',
      method: 'post',
      data: {
        pageNumber: params.pageNumber || 1,
        pageSize: params.pageSize || 10,
        searchKeyword: params.searchKeyword || '',
        isActive: params.isActive,
        sortBy: params.sortBy || 'sortOrder',
        sortDirection: params.sortDirection || 'asc'
      }
    })
  },

  /**
   * 创建任务分类
   * @param {Object} data - 分类数据
   * @param {string} data.name - 分类名称
   * @param {string} data.description - 分类描述
   * @param {string} data.color - 分类颜色
   * @param {string} data.icon - 分类图标
   * @param {number} data.sortOrder - 排序顺序
   * @param {boolean} data.isActive - 是否启用
   */
  createCategory(data) {
    return request({
      url: '/v1.1/task-categories',
      method: 'post',
      data: {
        name: data.name,
        description: data.description || '',
        color: data.color || '#409EFF',
        icon: data.icon || 'Folder',
        sortOrder: data.sortOrder || 0,
        isActive: data.isActive !== false
      }
    })
  },

  /**
   * 更新任务分类
   * @param {number} categoryId - 分类ID
   * @param {Object} data - 更新数据
   */
  updateCategory(categoryId, data) {
    return request({
      url: `/v1.1/task-categories/${categoryId}`,
      method: 'put',
      data: {
        name: data.name,
        description: data.description || '',
        color: data.color || '#409EFF',
        icon: data.icon || 'Folder',
        sortOrder: data.sortOrder || 0,
        isActive: data.isActive !== false
      }
    })
  },

  /**
   * 删除任务分类
   * @param {number} categoryId - 分类ID
   */
  deleteCategory(categoryId) {
    return request({
      url: `/v1.1/task-categories/${categoryId}`,
      method: 'delete'
    })
  },

  /**
   * 更新分类排序
   * @param {number} categoryId - 分类ID
   * @param {string} direction - 移动方向 ('up' | 'down')
   */
  updateSortOrder(categoryId, direction) {
    return request({
      url: `/v1.1/task-categories/${categoryId}/sort`,
      method: 'patch',
      data: { direction }
    })
  }
}

// 默认导出
export default taskCategoryApi

// 便捷方法导出
export const {
  getAllCategories,
  getActiveCategories,
  getCategoryById,
  searchCategories,
  createCategory,
  updateCategory,
  deleteCategory,
  updateSortOrder
} = taskCategoryApi
