import{aV as e,_ as a,r as l,j as t,z as s,A as u,b as o,o as n,e as d,w as r,d as i,a as c,f as p,b9 as v,ba as m,p as f,E as g,ad as y,c as b,k as w,$ as h,aG as _,q as V,m as k,F as C,h as $,D as P,b7 as N,t as T,l as S,aH as x,b1 as I,Z as U,bb as z,bc as D,aJ as R,af as M,aO as F}from"./index-CG5lHOPO.js";import{getMaintenanceSuppliers as K,getSpareParts as L,sparePartInbound as Y}from"./spareparts-DKUrs8IX.js";const B="/v2/faults",Q={getFaultList:a=>e.get(B,{params:a}),getFaultById:a=>e.get(`${B}/${a}`),createFault:a=>e.post("/v2/faults",{faultMode:a.faultMode||"asset",assetId:a.assetId||null,assetKeyword:a.assetKeyword||null,deviceName:a.deviceName||null,faultType:a.faultType,title:a.title,description:a.description,priority:a.priority,happenTime:a.happenTime,autoGenerateSparePartRecord:a.autoGenerateSparePartRecord||!1,sparePartInfo:a.sparePartInfo||null}),updateFault:(a,l)=>e.put(`${B}/${a}`,l),deleteFault:a=>e.delete(`${B}/${a}`),getFaultTypeList:a=>e.get("/fault-types",a),createFaultType:a=>e.post("/fault-types",a),updateFaultType:(a,l)=>e.put(`/fault-types/${a}`,l),deleteFaultType:a=>e.delete(`/fault-types/${a}`),assignFault:(a,l)=>e.post(`${B}/${a}/assign`,l),startFaultProcessing:a=>e.put(`${B}/${a}/start-processing`),pauseFaultProcessing:(a,l)=>e.put(`${B}/${a}/pause-processing`,l),completeFault:(a,l)=>e.put(`${B}/${a}/complete`,l),closeFault:(a,l)=>e.put(`${B}/${a}/close`,l),reopenFault:(a,l)=>e.put(`${B}/${a}/reopen`,l),addFaultRecord:(a,l)=>e.post(`${B}/${a}/records`,l),getFaultRecords:(a,l)=>e.get(`${B}/${a}/records`,l),exportFaults:a=>e.download(`${B}/export`,a,"faults.xlsx"),getFaultStatistics:a=>e.get(`${B}/statistics`,a),useSpareParts:(a,l)=>e.post(`${B}/${a}/use-spare-parts`,l),createReturnToFactory:(a,l)=>e.post(`${B}/${a}/return-to-factory`,l),searchAssets:a=>e.get("/assets/search",a)},O={class:"qr-scanner"},j={class:"scanner-container"},G={key:0,class:"no-camera"},q={key:1,class:"loading"},E={key:2,class:"camera-view"},H={class:"dialog-footer"},A=a({__name:"QrCodeScanner",props:{modelValue:{type:Boolean,default:!1},scanType:{type:String,default:"asset",validator:e=>["asset","faultType","deviceType"].includes(e)}},emits:["update:modelValue","scan-success","manual-input"],setup(e,{emit:a}){const y=e,b=a,w=l(!1),h=l(!1),_=l(!0),V=l(null),k=l(null),C=l(null),$=l(null);t((()=>y.modelValue),(e=>{w.value=e,e?P():N()}));const P=async()=>{h.value=!0;try{if(!navigator.mediaDevices||!navigator.mediaDevices.getUserMedia)return _.value=!1,void(h.value=!1);const e={video:{width:{ideal:400},height:{ideal:300},facingMode:"environment"}};C.value=await navigator.mediaDevices.getUserMedia(e),V.value&&(V.value.srcObject=C.value,V.value.onloadedmetadata=()=>{h.value=!1,T()})}catch(e){_.value=!1,h.value=!1,g.error("无法访问摄像头，请检查权限设置")}},N=()=>{C.value&&(C.value.getTracks().forEach((e=>e.stop())),C.value=null),$.value&&(clearInterval($.value),$.value=null)},T=()=>{V.value&&k.value&&($.value=setInterval((()=>{S()}),500))},S=()=>{if(!V.value||!k.value)return;const e=V.value,a=k.value,l=a.getContext("2d");if(a.width=e.videoWidth,a.height=e.videoHeight,l.drawImage(e,0,0,a.width,a.height),l.getImageData(0,0,a.width,a.height),Math.random()<.1){const e=x();I(e)}},x=()=>{switch(y.scanType){case"asset":return{type:"asset",code:"PC-2024-001",name:"联想台式机",id:1001};case"faultType":return{type:"faultType",code:"FT-HW-001",name:"硬件故障",id:1};case"deviceType":return{type:"deviceType",code:"DT-PC-001",name:"台式电脑",id:1};default:return{type:"unknown",code:"UNKNOWN-001",name:"未知类型",id:0}}},I=e=>{g.success(`扫描成功：${e.name} (${e.code})`),b("scan-success",e),U()},U=()=>{N(),b("update:modelValue",!1)},z=()=>{b("manual-input",y.scanType),U()};return s((()=>{y.modelValue&&(w.value=!0,P())})),u((()=>{N()})),(e,a)=>{const l=c("el-icon"),t=c("el-button"),s=c("el-dialog");return n(),o("div",O,[d(s,{modelValue:w.value,"onUpdate:modelValue":a[0]||(a[0]=e=>w.value=e),title:"扫描二维码",width:"500px","before-close":U},{footer:r((()=>[i("div",H,[d(t,{onClick:U},{default:r((()=>a[4]||(a[4]=[f("取消")]))),_:1}),d(t,{type:"primary",onClick:z},{default:r((()=>a[5]||(a[5]=[f("手动输入")]))),_:1})])])),default:r((()=>[i("div",j,[_.value?h.value?(n(),o("div",q,[d(l,{size:"48",class:"is-loading"},{default:r((()=>[d(p(m))])),_:1}),a[2]||(a[2]=i("p",null,"正在启动摄像头...",-1))])):(n(),o("div",E,[i("video",{ref_key:"videoRef",ref:V,autoplay:"",playsinline:""},null,512),i("canvas",{ref_key:"canvasRef",ref:k,style:{display:"none"}},null,512),a[3]||(a[3]=i("div",{class:"scan-frame"},[i("div",{class:"corner top-left"}),i("div",{class:"corner top-right"}),i("div",{class:"corner bottom-left"}),i("div",{class:"corner bottom-right"})],-1))])):(n(),o("div",G,[d(l,{size:"48"},{default:r((()=>[d(p(v))])),_:1}),a[1]||(a[1]=i("p",null,"未检测到摄像头",-1))]))]),a[6]||(a[6]=i("div",{class:"scanner-tips"},[i("p",null,"请将二维码对准扫描框"),i("p",null,"支持资产码、故障类型码等")],-1))])),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-b5621b23"]]),W={class:"fault-list-container"},X={class:"page-header"},J={class:"page-actions"},Z={class:"filter-container"},ee={class:"batch-info"},ae={class:"batch-buttons"},le={key:0,class:"asset-info"},te={class:"asset-name"},se={class:"asset-code text-secondary"},ue={class:"pagination-container"},oe={class:"asset-suggestion-item"},ne={class:"asset-main"},de={class:"asset-name"},re={class:"asset-code"},ie={class:"asset-detail"},ce={class:"asset-sn"},pe={class:"asset-location"},ve={key:0,class:"selected-asset"},me={class:"asset-info"},fe={class:"asset-name"},ge={class:"asset-code"},ye={key:0,class:"asset-sn"},be={class:"form-tip"},we={class:"spare-part-suggestion-item"},he={class:"spare-part-main"},_e={class:"spare-part-name"},Ve={class:"spare-part-stock"},ke={class:"spare-part-details"},Ce={key:0,class:"spare-part-spec"},$e={key:1,class:"spare-part-brand"},Pe={class:"form-tip"},Ne={class:"fault-type-input-group"},Te={class:"form-tip"},Se={class:"form-tip"},xe={class:"offline-device-info"},Ie={key:4,class:"spare-part-section"},Ue={class:"dialog-footer"},ze={class:"search-bar"},De={class:"asset-pagination"},Re={class:"dialog-footer"},Me={class:"spare-parts-dialog-content"},Fe={class:"fault-info"},Ke={class:"spare-parts-selection"},Le={class:"section-header"},Ye={key:0,class:"stock-info"},Be={class:"dialog-footer"},Qe={class:"return-to-factory-dialog-content"},Oe={class:"fault-info"},je={key:0},Ge={key:1},qe={class:"dialog-footer"},Ee={key:0,class:"fault-detail-content"},He={class:"detail-item"},Ae={class:"detail-item"},We={class:"detail-item"},Xe={class:"detail-item"},Je={class:"detail-item"},Ze={key:0,class:"asset-code"},ea={class:"detail-item"},aa={class:"detail-item"},la={class:"detail-item"},ta={class:"detail-item"},sa={class:"detail-item"},ua={class:"detail-item"},oa={class:"detail-item"},na={class:"detail-item full-width"},da={class:"description-content"},ra={class:"dialog-footer"},ia={class:"dialog-footer"},ca=a({__name:"list",setup(e){const a=l(!1),t=l([]),u=l(null),v=y({currentPage:1,pageSize:10,total:0}),m=y({code:"",assetKeyword:"",faultType:"",status:"",timeRange:[]}),B=[{label:"硬件故障",value:1},{label:"软件故障",value:2},{label:"网络故障",value:3},{label:"外设故障",value:4},{label:"其他故障",value:5}],O=[{label:"低",value:"low"},{label:"中",value:"medium"},{label:"高",value:"high"},{label:"紧急",value:"urgent"}],j=[{label:"待处理",value:"pending"},{label:"处理中",value:"processing"},{label:"已修复",value:"resolved"},{label:"已关闭",value:"closed"},{label:"待返修",value:"repair_pending"},{label:"返修中",value:"repairing"}],G=[{text:"最近一周",value:()=>{const e=new Date,a=new Date;return a.setTime(a.getTime()-6048e5),[a,e]}},{text:"最近一个月",value:()=>{const e=new Date,a=new Date;return a.setTime(a.getTime()-2592e6),[a,e]}},{text:"最近三个月",value:()=>{const e=new Date,a=new Date;return a.setTime(a.getTime()-7776e6),[a,e]}}],q=l(!1),E=l({faultMode:"asset",assetId:"",assetName:"",assetCode:"",assetSn:"",assetKeyword:"",deviceName:"",title:"",faultType:1,priority:"medium",happenTime:(new Date).toISOString().slice(0,16),description:"",attachments:[],autoGenerateSparePartRecord:!1,sparePartName:"",sparePartSpecification:"",sparePartBrand:"",sparePartQuantity:1,sparePartPrice:null}),H=l(null),ca=l([]),pa=l([]),va=l(!1),ma={title:[{required:!0,message:"请输入故障标题",trigger:"blur"},{min:2,max:100,message:"长度在 2 到 100 个字符",trigger:"blur"}],description:[{max:1e3,message:"长度不能超过 1000 个字符",trigger:"blur"}],sparePartName:[{validator:(e,a,l)=>{E.value.autoGenerateSparePartRecord&&!a?l(new Error("启用自动生成备件记录时，备件名称不能为空")):l()},trigger:"blur"}]},fa=l([]),ga=l(!1),ya=l(""),ba=l(!1),wa=l([]),ha=y({currentPage:1,pageSize:20,total:0});s((()=>{_a(),(async()=>{try{const e=await K();e.success&&(Cl.value=e.data||[])}catch(e){}})()}));const _a=async()=>{var e,l;a.value=!0;const s={page:v.currentPage,pageSize:v.pageSize,code:m.code,assetKeyword:m.assetKeyword,faultType:m.faultType,status:m.status,startTime:null==(e=m.timeRange)?void 0:e[0],endTime:null==(l=m.timeRange)?void 0:l[1]};try{const e=await Q.getFaultList(s);e.success?(t.value=e.data.items||e.data||[],v.total=e.data.total||e.data.length||0):(g.error(e.message||"获取故障列表失败"),t.value=[],v.total=0)}catch(u){g.error("获取故障列表失败"),t.value=[],v.total=0}finally{a.value=!1}},Va=()=>{v.currentPage=1,_a()},ka=()=>{m.code="",m.assetKeyword="",m.faultType="",m.status="",m.timeRange=[],v.currentPage=1,_a()},Ca=e=>{v.pageSize=e,_a()},$a=e=>{v.currentPage=e,_a()},Pa=l(!1),Na=l(null),Ta=l([]),Sa=b((()=>Ta.value.length>0&&Ta.value.every((e=>pl(e))))),xa=l(!1),Ia=l({id:null,code:"",title:"",processType:"repair",assigneeId:null,notes:"",expectedReturnDate:null,spareParts:[]}),Ua=e=>{const a=new Date;a.setDate(a.getDate()+7),Ia.value={id:e.id,code:e.code,title:e.title,processType:"repair",assigneeId:null,notes:"",expectedReturnDate:a,spareParts:[]},xa.value=!0},za=async()=>{try{Ia.value.id,Ia.value.processType,Ia.value.assigneeId,Ia.value.notes,"return"===Ia.value.processType&&Ia.value.expectedReturnDate;g.success("return"===Ia.value.processType?"返厂申请提交成功":"故障处理开始"),xa.value=!1,await getFaultList()}catch(e){g.error("提交失败，请重试")}},Da=e=>{Ta.value=e},Ra=()=>{Ta.value=[],u.value&&u.value.clearSelection()},Ma=()=>{if(0===Ta.value.length)return void g.warning("请先选择要返厂的故障记录");const e=new Date;e.setDate(e.getDate()+7),kl.value={faultIds:Ta.value.map((e=>e.id)),faultCodes:Ta.value.map((e=>e.code)),expectedReturnDate:e,reason:"",notes:"",isBatch:!0},Vl.value=!0},Fa=()=>{g.success("开始导出数据，请稍候...")},Ka=()=>{La(),q.value=!0},La=()=>{Object.keys(E.value).forEach((e=>{E.value[e]="priority"===e?"medium":"faultType"===e?1:"faultMode"===e?"asset":"happenTime"===e?(new Date).toISOString().slice(0,16):"sparePartQuantity"===e?1:"autoGenerateSparePartRecord"!==e&&("attachments"===e?[]:"sparePartPrice"===e?null:"")})),fa.value=[],ca.value=[],pa.value=[]},Ya=async()=>{var e;try{await(null==(e=H.value)?void 0:e.validate())}catch(a){return}if(!E.value.title||E.value.title.trim().length<2)g.warning("请输入至少2个字符的故障标题");else{if(E.value.autoGenerateSparePartRecord){if(!E.value.sparePartName)return void g.warning("启用自动生成备件记录时，备件名称不能为空");if(E.value.sparePartQuantity<=0)return void g.warning("备件数量必须大于0")}try{const e={faultMode:E.value.faultMode,assetId:"asset"===E.value.faultMode&&E.value.assetId||null,assetKeyword:"asset"===E.value.faultMode?E.value.assetKeyword:null,deviceName:"offline"===E.value.faultMode?E.value.deviceName:null,faultType:E.value.faultType,title:E.value.title,description:E.value.description,priority:E.value.priority,happenTime:E.value.happenTime,autoGenerateSparePartRecord:E.value.autoGenerateSparePartRecord,sparePartInfo:E.value.autoGenerateSparePartRecord?{name:E.value.sparePartName,specification:E.value.sparePartSpecification,brand:E.value.sparePartBrand,quantity:E.value.sparePartQuantity,price:E.value.sparePartPrice}:null},a=await Q.createFault(e);if(a.success){let e=`故障登记成功！故障编号：${`FIX-${(new Date).toISOString().slice(0,10).replace(/-/g,"")}-${String(a.data.id).padStart(3,"0")}`}`;if("offline"===E.value.faultMode&&E.value.deviceName)try{const l=await Xa(a.data.id);e+="\n✅ 备件入库记录已自动创建",e+=`\n📦 匹配的备件：${l.matchedSparePart.name}`}catch(l){e+=`\n⚠️ 备件入库失败：${l.message}`,g.warning(`备件入库失败：${l.message}`)}else E.value.autoGenerateSparePartRecord&&(e+="\n✅ 备件记录已自动创建");g.success(e),q.value=!1,_a()}else g.error(a.message||"故障登记失败")}catch(a){g.error("故障登记失败")}}},Ba=()=>{ga.value=!0,ya.value=E.value.assetKeyword,Qa()},Qa=async()=>{ba.value=!0;const e={keyword:ya.value,page:ha.currentPage,pageSize:ha.pageSize};try{const a=await F({keyword:ya.value,pageSize:e.pageSize,pageIndex:e.page});if(a.success){const e=a.data.items||a.data||[];wa.value=e.map((e=>({id:e.id,assetCode:e.assetCode||e.code,code:e.assetCode||e.code,name:e.name,assetTypeName:e.assetTypeName,serialNumber:e.serialNumber,sn:e.serialNumber,brand:e.brand,model:e.model,status:e.status,statusName:e.statusName,locationName:e.locationName,locationId:e.locationId,departmentName:e.departmentName,departmentId:e.departmentId}))),ha.total=a.data.total||e.length||0}else g.error(a.message||"搜索资产失败"),wa.value=[],ha.total=0}catch(a){g.error("搜索资产失败"),wa.value=[],ha.total=0}finally{ba.value=!1}},Oa=e=>{E.value.assetId=e.id,E.value.assetName=e.name,E.value.assetCode=e.assetCode||e.code,E.value.assetSn=e.serialNumber||e.sn,E.value.assetKeyword=`${e.assetCode||e.code} - ${e.name} - ${e.locationName||"未分配"} - ${e.departmentName||"未分配"}`;let a=`【资产信息】编号: ${e.assetCode||e.code}, 名称: ${e.name}, 位置: ${e.locationName||"未分配"}, 部门: ${e.departmentName||"未分配"}`;if((e.serialNumber||e.sn)&&(a+=`, SN: ${e.serialNumber||e.sn}`),e.brand||e.model){const l=[e.brand,e.model].filter(Boolean).join(" ");l&&(a+=`, 品牌型号: ${l}`)}E.value.description&&""!==E.value.description.trim()?E.value.description.includes("【资产信息】")||(E.value.description=a+"\n\n"+E.value.description):E.value.description=a,ga.value=!1,g.success(`已选择资产：${e.assetCode||e.code} - ${e.name}`)},ja=()=>{E.value.assetId="",E.value.assetName="",E.value.assetCode="",E.value.assetSn="",E.value.assetKeyword=""},Ga=e=>{"asset"===e?E.value.deviceName="":ja()},qa=async(e,a)=>{if(!e||e.length<1)a([]);else try{const l=await F({keyword:e,pageSize:20,pageIndex:1});if(l.success){const t=(l.data.items||l.data||[]).map((e=>{const a=e.assetCode||e.code||"无编号",l=e.name||"未命名",t=e.locationName||"未分配位置",s=e.departmentName||"未分配部门",u=e.serialNumber||e.sn||"",o=e.brand||"",n=e.model||"";let d=`${a} - ${l} - ${t} - ${s}`;if(u&&(d+=` | SN: ${u}`),o||n){const e=[o,n].filter(Boolean).join(" ");e&&(d+=` | ${e}`)}return{id:e.id,name:l,code:a,assetCode:a,sn:u,serialNumber:u,brand:o,model:n,locationName:t,locationId:e.locationId,departmentName:s,departmentId:e.departmentId,value:d,searchText:`${a} ${l} ${t} ${s} ${u} ${o} ${n}`.toLowerCase()}}));a(t.sort(((a,l)=>{const t=e.toLowerCase(),s=a.searchText.indexOf(t),u=l.searchText.indexOf(t);return 0===s&&0!==u?-1:0===u&&0!==s?1:s-u})))}else a([])}catch(l){a([])}},Ea=e=>{E.value.assetId=e.id,E.value.assetName=e.name,E.value.assetCode=e.assetCode||e.code,E.value.assetSn=e.serialNumber||e.sn,E.value.assetKeyword=e.value;let a=`【资产信息】编号: ${e.assetCode||e.code}, 名称: ${e.name}, 位置: ${e.locationName||"未分配"}, 部门: ${e.departmentName||"未分配"}`;if((e.serialNumber||e.sn)&&(a+=`, SN: ${e.serialNumber||e.sn}`),e.brand||e.model){const l=[e.brand,e.model].filter(Boolean).join(" ");l&&(a+=`, 品牌型号: ${l}`)}E.value.description&&""!==E.value.description.trim()?E.value.description.includes("【资产信息】")||(E.value.description=a+"\n\n"+E.value.description):E.value.description=a,g.success(`已选择资产：${e.assetCode||e.code} - ${e.name}`)},Ha=async(e,a)=>{if(!e||e.length<1){if(pa.value.length>0){a(pa.value.map((e=>({...e,value:e.name}))).slice(0,10))}else a([]);return}0===pa.value.length&&await Wa();const l=e.toLowerCase();a(pa.value.filter((e=>{const a=e.name.toLowerCase(),t=(e.specification||"").toLowerCase(),s=(e.brand||"").toLowerCase();return a.includes(l)||t.includes(l)||s.includes(l)||l.includes(a)})).sort(((e,a)=>{const t=e.name.toLowerCase(),s=a.name.toLowerCase(),u=t.indexOf(l),o=s.indexOf(l);return 0===u&&0!==o?-1:0===o&&0!==u?1:u-o})).map((e=>({...e,value:e.name}))).slice(0,10))},Aa=e=>{E.value.deviceName=e.name;let a=`【线下设备】设备名称: ${e.name}`;e.specification&&(a+=`\n规格: ${e.specification}`),e.brand&&(a+=`\n品牌: ${e.brand}`),E.value.description&&""!==E.value.description.trim()?E.value.description.includes("【线下设备】")||(E.value.description=a+"\n\n"+E.value.description):E.value.description=a,g.success(`已选择设备：${e.name}`)},Wa=async()=>{if(!(pa.value.length>0)){va.value=!0;try{const e=await L({pageSize:100,onlyNames:!0});if(e.success){const a={};(e.data.items||e.data||[]).forEach((e=>{a[e.name]?a[e.name].stockQuantity+=e.stockQuantity||0:a[e.name]={id:e.id,name:e.name,specification:e.specification,brand:e.brand,stockQuantity:e.stockQuantity||0}})),pa.value=Object.values(a)}else g.error("获取备件台账失败"),pa.value=[]}catch(e){g.error("获取备件台账失败"),pa.value=[]}finally{va.value=!1}}},Xa=async e=>{0===pa.value.length&&await Wa();const a=E.value.deviceName.toLowerCase();let l=null;if(l=pa.value.find((e=>e.name.toLowerCase()===a)),l||(l=pa.value.find((e=>e.name.toLowerCase().includes(a)||a.includes(e.name.toLowerCase())))),!l){const e=a.split(/[\s\-_]+/);l=pa.value.find((a=>{const l=a.name.toLowerCase();return e.some((e=>e.length>1&&l.includes(e)))}))}if(!l)throw new Error(`未找到与设备名称 "${E.value.deviceName}" 匹配的备件记录。请检查设备名称是否与备件库中的资产名称一致。`);const t={partId:l.id,quantity:1,reasonType:4,reason:`故障登记自动入库 - 故障编号: FIX-${(new Date).toISOString().slice(0,10).replace(/-/g,"")}-${String(e).padStart(3,"0")}`,referenceNumber:`FAULT-${e}`,remarks:`线下设备故障登记自动生成的入库记录\n设备名称: ${E.value.deviceName}\n匹配的备件: ${l.name}\n故障标题: ${E.value.title}`,locationId:1},s=await Y(t);if(!s.success)throw new Error(s.message||"备件入库失败");return{...s.data,matchedSparePart:l}},Ja=()=>{bl.value="asset",yl.value=!0},Za=()=>{bl.value="faultType",yl.value=!0},el=e=>{switch(e.type){case"asset":E.value.assetId=e.id,E.value.assetName=e.name,E.value.assetCode=e.code,E.value.assetKeyword=`${e.name} (${e.code})`,g.success(`已关联资产：${e.name}`);break;case"faultType":E.value.faultType=e.id,g.success(`已选择故障类型：${e.name}`);break;case"deviceType":e.name.includes("电脑")||e.name.includes("PC")?E.value.faultType=1:(e.name.includes("打印机")||e.name.includes("扫码器"))&&(E.value.faultType=4),g.success(`已识别设备类型：${e.name}`)}},al=e=>{switch(e){case"asset":Ba();break;case"faultType":g.info("请从下拉列表中选择故障类型");break;case"deviceType":g.info("请手动选择对应的故障类型")}},ll=e=>{ha.pageSize=e,Qa()},tl=e=>{ha.currentPage=e,Qa()},sl=e=>{if(e.size>10485760){g.warning("文件大小不能超过10MB");const a=fa.value.indexOf(e);return void(-1!==a&&fa.value.splice(a,1))}const a=e.name.split(".").pop().toLowerCase();if(["jpg","jpeg","png","pdf"].includes(a));else{g.warning("只支持jpg、png、pdf格式文件");const a=fa.value.indexOf(e);-1!==a&&fa.value.splice(a,1)}},ul=e=>{const a=fa.value.indexOf(e);-1!==a&&fa.value.splice(a,1)},ol=e=>({0:"闲置",1:"在用",2:"维修中",3:"报废"}[e]||"未知状态"),nl=e=>({1:"硬件故障",2:"软件故障",3:"网络故障",4:"外设故障",5:"其他故障"}[e]||"未知类型"),dl=e=>({1:"低",2:"中",3:"高",4:"紧急"}[e]||"未知"),rl=e=>({pending:"待处理",processing:"处理中",resolved:"已修复",closed:"已关闭",repair_pending:"待返修",repairing:"返修中"}[e]||"未知"),il=e=>({low:"低",medium:"中",high:"高"}[e]||e),cl=e=>[0,1].includes(e.status),pl=e=>[0,1].includes(e.status),vl=l(!1),ml=l(null),fl=y({spareParts:[],notes:""}),gl=l([]),yl=l(!1),bl=l("asset"),wl=e=>{ml.value=e,fl.spareParts=[],fl.notes="",vl.value=!0,(async()=>{va.value=!0;try{const e=await L({pageSize:100});e.success?gl.value=e.data.items||e.data||[]:(g.error("获取备件列表失败"),gl.value=[])}catch(e){g.error("获取备件列表失败"),gl.value=[]}finally{va.value=!1}})()},hl=()=>{fl.spareParts.push({sparePartId:"",sparePartName:"",quantity:1,notes:""})},_l=async()=>{try{if(0===fl.spareParts.length)return void g.warning("请至少添加一个备件");for(const a of fl.spareParts){if(!a.sparePartId)return void g.warning("请选择备件");if(a.quantity<=0)return void g.warning("备件数量必须大于0")}const e=await Q.useSpareParts(ml.value.id,{spareParts:fl.spareParts,notes:fl.notes});if(!e.success)return void g.error(e.message||"备件使用记录失败");g.success("备件使用记录成功"),vl.value=!1,_a()}catch(e){g.error("备件使用记录失败："+e.message)}},Vl=l(!1),kl=l({faultIds:[],faultCodes:[],supplierId:"",reason:"",expectedReturnDate:"",notes:"",isBatch:!1}),Cl=l([]),$l=e=>{ml.value=e;const a=new Date;a.setDate(a.getDate()+7),kl.value={faultIds:[e.id],faultCodes:[e.code],supplierId:"",reason:"",expectedReturnDate:a,notes:"",isBatch:!1},Vl.value=!0},Pl=async()=>{try{if(!kl.value.supplierId)return void g.warning("请选择供应商");if(!kl.value.reason)return void g.warning("请输入返厂原因");kl.value.isBatch?(g.success(`批量返厂申请提交成功，共 ${kl.value.faultIds.length} 条故障记录`),Ra()):g.success("返厂记录创建成功"),Vl.value=!1,_a()}catch(e){g.error("创建返厂记录失败："+e.message)}},Nl=e=>{const a=gl.value.find((a=>a.id===e));return a&&a.stockQuantity||0};return(e,l)=>{const s=c("el-button"),y=c("el-input"),b=c("el-form-item"),F=c("el-option"),K=c("el-select"),L=c("el-date-picker"),Y=c("el-form"),ca=c("el-card"),pa=c("el-table-column"),_a=c("el-tag"),La=c("el-table"),Xa=c("el-pagination"),Tl=c("el-radio"),Sl=c("el-radio-group"),xl=c("el-button-group"),Il=c("el-autocomplete"),Ul=c("el-text"),zl=c("el-checkbox"),Dl=c("el-alert"),Rl=c("el-divider"),Ml=c("el-col"),Fl=c("el-row"),Kl=c("el-input-number"),Ll=c("el-icon"),Yl=c("el-upload"),Bl=c("el-dialog"),Ql=k("loading");return n(),o("div",W,[i("div",X,[l[51]||(l[51]=i("h2",{class:"page-title"},"故障列表",-1)),i("div",J,[d(s,{type:"primary",onClick:Ka,icon:p(_)},{default:r((()=>l[49]||(l[49]=[f(" 登记故障 ")]))),_:1},8,["icon"]),d(s,{type:"primary",onClick:Fa,icon:p(V)},{default:r((()=>l[50]||(l[50]=[f(" 导出数据 ")]))),_:1},8,["icon"])])]),d(ca,{class:"filter-card"},{default:r((()=>[i("div",Z,[m?(n(),w(Y,{key:0,inline:!0,model:m,class:"filter-form"},{default:r((()=>[d(b,{label:"故障编号"},{default:r((()=>[d(y,{modelValue:m.code,"onUpdate:modelValue":l[0]||(l[0]=e=>m.code=e),placeholder:"故障编号",clearable:""},null,8,["modelValue"])])),_:1}),d(b,{label:"资产信息"},{default:r((()=>[d(y,{modelValue:m.assetKeyword,"onUpdate:modelValue":l[1]||(l[1]=e=>m.assetKeyword=e),placeholder:"资产名称/编号/SN",clearable:""},null,8,["modelValue"])])),_:1}),d(b,{label:"故障类型"},{default:r((()=>[d(K,{modelValue:m.faultType,"onUpdate:modelValue":l[2]||(l[2]=e=>m.faultType=e),placeholder:"全部类型",clearable:""},{default:r((()=>[(n(),o(C,null,$(B,(e=>d(F,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),64))])),_:1},8,["modelValue"])])),_:1}),d(b,{label:"处理状态"},{default:r((()=>[d(K,{modelValue:m.status,"onUpdate:modelValue":l[3]||(l[3]=e=>m.status=e),placeholder:"全部状态",clearable:""},{default:r((()=>[(n(),o(C,null,$(j,(e=>d(F,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),64))])),_:1},8,["modelValue"])])),_:1}),d(b,{label:"发生时间"},{default:r((()=>[d(L,{modelValue:m.timeRange,"onUpdate:modelValue":l[4]||(l[4]=e=>m.timeRange=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"YYYY-MM-DD",shortcuts:G},null,8,["modelValue"])])),_:1}),d(b,null,{default:r((()=>[d(s,{type:"primary",onClick:Va,icon:p(P)},{default:r((()=>l[52]||(l[52]=[f(" 搜索 ")]))),_:1},8,["icon"]),d(s,{onClick:ka,icon:p(N)},{default:r((()=>l[53]||(l[53]=[f(" 重置 ")]))),_:1},8,["icon"])])),_:1})])),_:1},8,["model"])):h("",!0)])])),_:1}),Ta.value.length>0?(n(),w(ca,{key:0,class:"batch-operations"},{default:r((()=>[i("div",ee,[i("span",null,"已选择 "+T(Ta.value.length)+" 条故障记录",1),i("div",ae,[d(s,{type:"warning",onClick:Ma,disabled:!Sa.value},{default:r((()=>l[54]||(l[54]=[f(" 批量返厂 ")]))),_:1},8,["disabled"]),d(s,{onClick:Ra},{default:r((()=>l[55]||(l[55]=[f("取消选择")]))),_:1})])])])),_:1})):h("",!0),d(ca,{class:"data-card"},{default:r((()=>[S((n(),w(La,{ref_key:"faultTable",ref:u,data:t.value||[],border:"",style:{width:"100%"},onSelectionChange:Da},{default:r((()=>[d(pa,{type:"selection",width:"55"}),d(pa,{prop:"code",label:"故障编号",width:"120",sortable:""}),d(pa,{prop:"title",label:"故障标题","min-width":"180","show-overflow-tooltip":""}),d(pa,{prop:"assetInfo",label:"故障资产",width:"200","show-overflow-tooltip":""},{default:r((e=>[e.row?(n(),o("div",le,[i("div",te,T(e.row.assetName||"未关联资产"),1),i("div",se,T(e.row.assetCode||""),1)])):h("",!0)])),_:1}),d(pa,{prop:"faultType",label:"故障类型",width:"120"},{default:r((e=>{return[e.row?(n(),w(_a,{key:0,type:(a=e.row.faultType,{1:"danger",2:"warning",3:"info",4:"success",5:""}[a]||""),size:"small"},{default:r((()=>[f(T(nl(e.row.faultType)),1)])),_:2},1032,["type"])):h("",!0)];var a})),_:1}),d(pa,{prop:"priority",label:"优先级",width:"100"},{default:r((e=>{return[e.row?(n(),w(_a,{key:0,type:(a=e.row.priority,{1:"info",2:"",3:"warning",4:"danger"}[a]||""),size:"small"},{default:r((()=>[f(T(dl(e.row.priority)),1)])),_:2},1032,["type"])):h("",!0)];var a})),_:1}),d(pa,{prop:"status",label:"处理状态",width:"120"},{default:r((e=>{return[e.row?(n(),w(_a,{key:0,type:(a=e.row.status,{pending:"info",processing:"warning",resolved:"success",closed:"",repair_pending:"warning",repairing:"danger"}[a]||""),size:"small"},{default:r((()=>[f(T(rl(e.row.status)),1)])),_:2},1032,["type"])):h("",!0)];var a})),_:1}),d(pa,{prop:"reportUser",label:"报告人",width:"120"}),d(pa,{prop:"reportTime",label:"报告时间",width:"180",sortable:""}),d(pa,{prop:"handler",label:"处理人",width:"120"}),d(pa,{prop:"updateTime",label:"更新时间",width:"180",sortable:""}),d(pa,{label:"操作",width:"280",fixed:"right"},{default:r((e=>{return[e.row?(n(),o(C,{key:0},[d(s,{type:"primary",text:"",size:"small",onClick:a=>(async e=>{try{const a=await Q.getFaultById(e.id);a.success?(Na.value=a.data,Pa.value=!0):g.error(a.message||"获取故障详情失败")}catch(a){g.error("获取故障详情失败")}})(e.row),icon:p(x)},{default:r((()=>l[56]||(l[56]=[f(" 详情 ")]))),_:2},1032,["onClick","icon"]),d(s,{type:"success",text:"",size:"small",onClick:a=>Ua(e.row),icon:p(I),disabled:!cl(e.row)},{default:r((()=>l[57]||(l[57]=[f(" 处理 ")]))),_:2},1032,["onClick","icon","disabled"]),d(s,{type:"info",text:"",size:"small",onClick:a=>wl(e.row),icon:p(U),disabled:(a=e.row,!(1===a.status))},{default:r((()=>l[58]||(l[58]=[f(" 用料 ")]))),_:2},1032,["onClick","icon","disabled"]),d(s,{type:"warning",text:"",size:"small",onClick:a=>$l(e.row),icon:p(z),disabled:!pl(e.row)},{default:r((()=>l[59]||(l[59]=[f(" 返厂 ")]))),_:2},1032,["onClick","icon","disabled"])],64)):h("",!0)];var a})),_:1})])),_:1},8,["data"])),[[Ql,a.value]]),i("div",ue,[d(Xa,{"current-page":v.currentPage,"onUpdate:currentPage":l[5]||(l[5]=e=>v.currentPage=e),"page-size":v.pageSize,"onUpdate:pageSize":l[6]||(l[6]=e=>v.pageSize=e),"page-sizes":[10,20,50,100],background:!0,layout:"total, sizes, prev, pager, next, jumper",total:v.total,onSizeChange:Ca,onCurrentChange:$a},null,8,["current-page","page-size","total"])])])),_:1}),d(Bl,{title:"登记故障",modelValue:q.value,"onUpdate:modelValue":l[22]||(l[22]=e=>q.value=e),width:"700px","append-to-body":""},{footer:r((()=>[i("div",Ue,[d(s,{onClick:l[21]||(l[21]=e=>q.value=!1)},{default:r((()=>l[74]||(l[74]=[f("取 消")]))),_:1}),d(s,{type:"primary",onClick:Ya},{default:r((()=>l[75]||(l[75]=[f("确 定")]))),_:1})])])),default:r((()=>[d(Y,{ref_key:"faultFormRef",ref:H,model:E.value,rules:ma,"label-width":"100px"},{default:r((()=>[d(b,{label:"故障模式",prop:"faultMode"},{default:r((()=>[d(Sl,{modelValue:E.value.faultMode,"onUpdate:modelValue":l[7]||(l[7]=e=>E.value.faultMode=e),onChange:Ga},{default:r((()=>[d(Tl,{value:"asset"},{default:r((()=>l[60]||(l[60]=[f("有资产编号设备")]))),_:1}),d(Tl,{value:"offline"},{default:r((()=>l[61]||(l[61]=[f("线下设备（备件台账）")]))),_:1})])),_:1},8,["modelValue"])])),_:1}),"asset"===E.value.faultMode?(n(),w(b,{key:0,label:"资产",prop:"assetId"},{default:r((()=>[d(Il,{modelValue:E.value.assetKeyword,"onUpdate:modelValue":l[8]||(l[8]=e=>E.value.assetKeyword=e),"fetch-suggestions":qa,placeholder:"请输入资产编号/名称/位置/SN进行智能搜索",clearable:"",maxlength:100,style:{width:"100%"},"trigger-on-focus":!1,onSelect:Ea,onClear:ja},{default:r((({item:e})=>[i("div",oe,[i("div",ne,[i("span",de,T(e.name),1),i("span",re,T(e.code),1)]),i("div",ie,[i("span",ce,"SN: "+T(e.sn||"无"),1),i("span",pe,T(e.locationName||"未分配"),1)])])])),append:r((()=>[d(xl,null,{default:r((()=>[d(s,{icon:p(D),onClick:Ja},{default:r((()=>l[62]||(l[62]=[f("扫码")]))),_:1},8,["icon"]),d(s,{icon:p(P),onClick:Ba},{default:r((()=>l[63]||(l[63]=[f("高级搜索")]))),_:1},8,["icon"])])),_:1})])),_:1},8,["modelValue"]),E.value.assetId?(n(),o("div",ve,[i("div",me,[i("span",fe,T(E.value.assetName),1),i("span",ge,T(E.value.assetCode),1),E.value.assetSn?(n(),o("span",ye,"SN: "+T(E.value.assetSn),1)):h("",!0)]),d(s,{link:"",type:"danger",onClick:ja,icon:p(R),size:"small"},{default:r((()=>l[64]||(l[64]=[f(" 清除 ")]))),_:1},8,["icon"])])):h("",!0),i("div",be,[d(Ul,{type:"info",size:"small"},{default:r((()=>l[65]||(l[65]=[f(" 输入时会自动搜索匹配的资产，选择后保存资产信息，否则保存输入的内容 ")]))),_:1})])])),_:1})):h("",!0),"offline"===E.value.faultMode?(n(),w(b,{key:1,label:"设备名称",prop:"deviceName"},{default:r((()=>[d(Il,{modelValue:E.value.deviceName,"onUpdate:modelValue":l[9]||(l[9]=e=>E.value.deviceName=e),"fetch-suggestions":Ha,placeholder:"请输入设备名称（支持从备件库智能匹配）",clearable:"",maxlength:100,style:{width:"100%"},"trigger-on-focus":!0,onSelect:Aa,onFocus:Wa},{default:r((({item:e})=>[i("div",we,[i("div",he,[i("span",_e,T(e.name),1),i("span",Ve,"库存: "+T(e.stockQuantity||0),1)]),i("div",ke,[e.specification?(n(),o("span",Ce,T(e.specification),1)):h("",!0),e.brand?(n(),o("span",$e,T(e.brand),1)):h("",!0)])])])),_:1},8,["modelValue"]),i("div",Pe,[d(Ul,{type:"info",size:"small"},{default:r((()=>l[66]||(l[66]=[f(" 线下设备模式：输入设备名称智能匹配备件库，适用于电池、线缆等无固定资产编号的设备 ")]))),_:1})])])),_:1})):h("",!0),d(b,{label:"故障标题",prop:"title"},{default:r((()=>[d(y,{modelValue:E.value.title,"onUpdate:modelValue":l[10]||(l[10]=e=>E.value.title=e),placeholder:"请输入故障标题",maxlength:100,"show-word-limit":"",clearable:""},null,8,["modelValue"])])),_:1}),d(b,{label:"故障类型",prop:"faultType"},{default:r((()=>[i("div",Ne,[d(K,{modelValue:E.value.faultType,"onUpdate:modelValue":l[11]||(l[11]=e=>E.value.faultType=e),placeholder:"请选择故障类型",style:{width:"calc(100% - 80px)"},clearable:"",filterable:""},{default:r((()=>[(n(),o(C,null,$(B,(e=>d(F,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),64))])),_:1},8,["modelValue"]),d(s,{icon:p(D),onClick:Za,style:{"margin-left":"8px"}},{default:r((()=>l[67]||(l[67]=[f("扫码")]))),_:1},8,["icon"])]),i("div",Te,[d(Ul,{type:"info",size:"small"},{default:r((()=>l[68]||(l[68]=[f(" 支持扫描故障类型二维码自动选择，默认为硬件故障 ")]))),_:1})])])),_:1}),d(b,{label:"优先级",prop:"priority"},{default:r((()=>[d(K,{modelValue:E.value.priority,"onUpdate:modelValue":l[12]||(l[12]=e=>E.value.priority=e),placeholder:"请选择优先级",style:{width:"100%"},clearable:""},{default:r((()=>[(n(),o(C,null,$(O,(e=>d(F,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),64))])),_:1},8,["modelValue"])])),_:1}),d(b,{label:"发生时间",prop:"happenTime"},{default:r((()=>[d(L,{modelValue:E.value.happenTime,"onUpdate:modelValue":l[13]||(l[13]=e=>E.value.happenTime=e),type:"datetime-local",placeholder:"请选择故障发生时间",style:{width:"100%"},"value-format":"YYYY-MM-DDTHH:mm"},null,8,["modelValue"])])),_:1}),d(b,{label:"故障描述",prop:"description"},{default:r((()=>[d(y,{modelValue:E.value.description,"onUpdate:modelValue":l[14]||(l[14]=e=>E.value.description=e),type:"textarea",rows:4,placeholder:"请详细描述故障现象、影响范围等信息",maxlength:1e3,"show-word-limit":"",resize:"none"},null,8,["modelValue"])])),_:1}),"asset"===E.value.faultMode?(n(),w(b,{key:2,label:"备件管理"},{default:r((()=>[d(zl,{modelValue:E.value.autoGenerateSparePartRecord,"onUpdate:modelValue":l[15]||(l[15]=e=>E.value.autoGenerateSparePartRecord=e)},{default:r((()=>l[69]||(l[69]=[f(" 自动生成备件入库记录 ")]))),_:1},8,["modelValue"]),i("div",Se,[d(Ul,{type:"info",size:"small"},{default:r((()=>l[70]||(l[70]=[f(" 勾选后将自动创建一条备件入库记录，适用于电池等消耗性故障 ")]))),_:1})])])),_:1})):h("",!0),"offline"===E.value.faultMode?(n(),w(b,{key:3,label:"备件管理"},{default:r((()=>[i("div",xe,[d(Dl,{title:"线下设备模式",type:"info",closable:!1,"show-icon":""},{default:r((()=>l[71]||(l[71]=[f(" 选择线下设备后，系统将自动生成对应的备件入库记录，无需手动填写备件信息。 ")]))),_:1})])])),_:1})):h("",!0),"asset"===E.value.faultMode&&E.value.autoGenerateSparePartRecord?(n(),o("div",Ie,[d(Rl,{"content-position":"left"},{default:r((()=>l[72]||(l[72]=[f("备件信息")]))),_:1}),d(Fl,{gutter:16},{default:r((()=>[d(Ml,{span:12},{default:r((()=>[d(b,{label:"备件名称",prop:"sparePartName"},{default:r((()=>[d(y,{modelValue:E.value.sparePartName,"onUpdate:modelValue":l[16]||(l[16]=e=>E.value.sparePartName=e),placeholder:"请输入备件名称",maxlength:50,clearable:""},null,8,["modelValue"])])),_:1})])),_:1}),d(Ml,{span:12},{default:r((()=>[d(b,{label:"规格型号"},{default:r((()=>[d(y,{modelValue:E.value.sparePartSpecification,"onUpdate:modelValue":l[17]||(l[17]=e=>E.value.sparePartSpecification=e),placeholder:"请输入规格型号",maxlength:100,clearable:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),d(Fl,{gutter:16},{default:r((()=>[d(Ml,{span:8},{default:r((()=>[d(b,{label:"品牌"},{default:r((()=>[d(y,{modelValue:E.value.sparePartBrand,"onUpdate:modelValue":l[18]||(l[18]=e=>E.value.sparePartBrand=e),placeholder:"请输入品牌",maxlength:30,clearable:""},null,8,["modelValue"])])),_:1})])),_:1}),d(Ml,{span:8},{default:r((()=>[d(b,{label:"数量"},{default:r((()=>[d(Kl,{modelValue:E.value.sparePartQuantity,"onUpdate:modelValue":l[19]||(l[19]=e=>E.value.sparePartQuantity=e),min:1,max:999},null,8,["modelValue"])])),_:1})])),_:1}),d(Ml,{span:8},{default:r((()=>[d(b,{label:"单价"},{default:r((()=>[d(Kl,{modelValue:E.value.sparePartPrice,"onUpdate:modelValue":l[20]||(l[20]=e=>E.value.sparePartPrice=e),precision:2,min:0},null,8,["modelValue"])])),_:1})])),_:1})])),_:1})])):h("",!0),d(b,{label:"附件"},{default:r((()=>[d(Yl,{action:"#","list-type":"picture-card","auto-upload":!1,limit:5,"on-change":sl,"on-remove":ul,"file-list":fa.value},{default:r((()=>[d(Ll,null,{default:r((()=>[d(p(_))])),_:1})])),_:1},8,["file-list"]),l[73]||(l[73]=i("div",{class:"upload-tip text-secondary"}," 支持jpg、png、pdf格式，最多5个文件，每个不超过10MB ",-1))])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue"]),d(Bl,{title:"选择资产",modelValue:ga.value,"onUpdate:modelValue":l[27]||(l[27]=e=>ga.value=e),width:"800px","append-to-body":""},{footer:r((()=>[i("div",Re,[d(s,{onClick:l[26]||(l[26]=e=>ga.value=!1)},{default:r((()=>l[76]||(l[76]=[f("取 消")]))),_:1})])])),default:r((()=>[i("div",ze,[d(y,{modelValue:ya.value,"onUpdate:modelValue":l[23]||(l[23]=e=>ya.value=e),placeholder:"请输入资产编号/名称/位置/SN进行智能搜索",onKeyup:M(p(Qa),["enter"])},{append:r((()=>[d(s,{icon:p(P),onClick:p(Qa)},null,8,["icon","onClick"])])),_:1},8,["modelValue","onKeyup"])]),S((n(),w(La,{ref:"assetTable",data:wa.value,style:{width:"100%"},height:"400px",border:"",onRowClick:Oa},{default:r((()=>[d(pa,{type:"index",label:"序号",width:"60",align:"center"},{default:r((e=>[i("span",null,T((ha.currentPage-1)*ha.pageSize+e.$index+1),1)])),_:1}),d(pa,{prop:"assetCode",label:"资产编号",width:"120"},{default:r((e=>[i("span",null,T(e.row.assetCode||e.row.code||"无编号"),1)])),_:1}),d(pa,{prop:"name",label:"资产名称","min-width":"150"}),d(pa,{prop:"assetTypeName",label:"资产类型",width:"120"},{default:r((e=>[e.row?(n(),w(_a,{key:0,size:"small"},{default:r((()=>[f(T(e.row.assetTypeName||"未知"),1)])),_:2},1024)):h("",!0)])),_:1}),d(pa,{prop:"serialNumber",label:"序列号",width:"150"},{default:r((e=>[i("span",null,T(e.row.serialNumber||e.row.sn||"-"),1)])),_:1}),d(pa,{prop:"status",label:"状态",width:"100"},{default:r((e=>{return[e.row?(n(),w(_a,{key:0,size:"small",type:(a=e.row.status,{0:"info",1:"success",2:"warning",3:"danger"}[a]||"info")},{default:r((()=>[f(T(e.row.statusName||ol(e.row.status)||"未知"),1)])),_:2},1032,["type"])):h("",!0)];var a})),_:1}),d(pa,{prop:"departmentName",label:"所属部门",width:"120"},{default:r((e=>[i("span",null,T(e.row.departmentName||"未分配"),1)])),_:1}),d(pa,{prop:"locationName",label:"位置",width:"150"},{default:r((e=>[i("span",null,T(e.row.locationName||"未分配"),1)])),_:1})])),_:1},8,["data"])),[[Ql,ba.value]]),i("div",De,[d(Xa,{"current-page":ha.currentPage,"onUpdate:currentPage":l[24]||(l[24]=e=>ha.currentPage=e),"page-size":ha.pageSize,"onUpdate:pageSize":l[25]||(l[25]=e=>ha.pageSize=e),"page-sizes":[20,50,100],background:!0,layout:"total, sizes, prev, pager, next",total:ha.total,onSizeChange:ll,onCurrentChange:tl},null,8,["current-page","page-size","total"])])])),_:1},8,["modelValue"]),d(Bl,{modelValue:vl.value,"onUpdate:modelValue":l[29]||(l[29]=e=>vl.value=e),title:"故障维修使用备件",width:"70%","close-on-click-modal":!1},{footer:r((()=>[i("span",Be,[d(s,{onClick:l[28]||(l[28]=e=>vl.value=!1)},{default:r((()=>l[83]||(l[83]=[f("取消")]))),_:1}),d(s,{type:"primary",onClick:_l},{default:r((()=>l[84]||(l[84]=[f("确认使用")]))),_:1})])])),default:r((()=>{var e,a,t,u;return[i("div",Me,[i("div",Fe,[l[80]||(l[80]=i("h4",null,"故障信息",-1)),i("p",null,[l[77]||(l[77]=i("strong",null,"故障编号：",-1)),f(T(null==(e=ml.value)?void 0:e.code),1)]),i("p",null,[l[78]||(l[78]=i("strong",null,"故障标题：",-1)),f(T(null==(a=ml.value)?void 0:a.title),1)]),i("p",null,[l[79]||(l[79]=i("strong",null,"故障资产：",-1)),f(T(null==(t=ml.value)?void 0:t.assetName)+" ("+T(null==(u=ml.value)?void 0:u.assetCode)+")",1)])]),i("div",Ke,[i("div",Le,[l[82]||(l[82]=i("h4",null,"备件使用",-1)),d(s,{type:"primary",size:"small",onClick:hl},{default:r((()=>l[81]||(l[81]=[f("添加备件")]))),_:1})]),d(La,{data:fl.spareParts,border:"",style:{width:"100%"}},{default:r((()=>[d(pa,{label:"备件",width:"200"},{default:r((e=>[d(K,{modelValue:e.row.sparePartId,"onUpdate:modelValue":a=>e.row.sparePartId=a,placeholder:"选择备件",filterable:"",onChange:a=>((e,a)=>{const l=gl.value.find((a=>a.id===e));l&&(fl.spareParts[a].sparePartName=l.name,fl.spareParts[a].maxQuantity=l.stockQuantity||0)})(a,e.$index),loading:va.value},{default:r((()=>[(n(!0),o(C,null,$(gl.value,(e=>(n(),w(F,{key:e.id,label:`${e.name} (库存: ${e.stockQuantity||0})`,value:e.id,disabled:(e.stockQuantity||0)<=0},null,8,["label","value","disabled"])))),128))])),_:2},1032,["modelValue","onUpdate:modelValue","onChange","loading"])])),_:1}),d(pa,{label:"库存",width:"80",align:"center"},{default:r((e=>[e.row?(n(),o("span",Ye,T(Nl(e.row.sparePartId)),1)):h("",!0)])),_:1}),d(pa,{label:"使用数量",width:"120",align:"center"},{default:r((e=>[e.row?(n(),w(Kl,{key:0,modelValue:e.row.quantity,"onUpdate:modelValue":a=>e.row.quantity=a,min:1,max:Nl(e.row.sparePartId),size:"small"},null,8,["modelValue","onUpdate:modelValue","max"])):h("",!0)])),_:1}),d(pa,{label:"备注","min-width":"150"},{default:r((e=>[e.row?(n(),w(y,{key:0,modelValue:e.row.notes,"onUpdate:modelValue":a=>e.row.notes=a,placeholder:"备注信息",size:"small"},null,8,["modelValue","onUpdate:modelValue"])):h("",!0)])),_:1}),d(pa,{label:"操作",width:"80",align:"center"},{default:r((e=>[e.row?(n(),w(s,{key:0,type:"danger",text:"",size:"small",onClick:a=>{return l=e.$index,void fl.spareParts.splice(l,1);var l},icon:p(R)},null,8,["onClick","icon"])):h("",!0)])),_:1})])),_:1},8,["data"])])])]})),_:1},8,["modelValue"]),d(Bl,{modelValue:Vl.value,"onUpdate:modelValue":l[35]||(l[35]=e=>Vl.value=e),title:kl.value.isBatch?"批量创建返厂记录":"创建返厂记录",width:"60%","close-on-click-modal":!1},{footer:r((()=>[i("span",qe,[d(s,{onClick:l[34]||(l[34]=e=>Vl.value=!1)},{default:r((()=>l[90]||(l[90]=[f("取消")]))),_:1}),d(s,{type:"primary",onClick:Pl},{default:r((()=>l[91]||(l[91]=[f("确认返厂")]))),_:1})])])),default:r((()=>{var e,a,t,s;return[i("div",Qe,[i("div",Oe,[i("h4",null,T(kl.value.isBatch?"批量故障信息":"故障信息"),1),kl.value.isBatch?(n(),o("div",je,[i("p",null,[l[85]||(l[85]=i("strong",null,"选择故障数量：",-1)),f(T(kl.value.faultCodes.length)+" 条",1)]),i("p",null,[l[86]||(l[86]=i("strong",null,"故障编号：",-1)),f(T(kl.value.faultCodes.join(", ")),1)])])):(n(),o("div",Ge,[i("p",null,[l[87]||(l[87]=i("strong",null,"故障编号：",-1)),f(T(null==(e=ml.value)?void 0:e.code),1)]),i("p",null,[l[88]||(l[88]=i("strong",null,"故障标题：",-1)),f(T(null==(a=ml.value)?void 0:a.title),1)]),i("p",null,[l[89]||(l[89]=i("strong",null,"故障资产：",-1)),f(T(null==(t=ml.value)?void 0:t.assetName)+" ("+T(null==(s=ml.value)?void 0:s.assetCode)+")",1)])]))]),d(Y,{model:kl.value,"label-width":"100px"},{default:r((()=>[d(b,{label:"供应商",required:""},{default:r((()=>[d(K,{modelValue:kl.value.supplierId,"onUpdate:modelValue":l[30]||(l[30]=e=>kl.value.supplierId=e),placeholder:"请选择维修供应商",style:{width:"100%"}},{default:r((()=>[(n(!0),o(C,null,$(Cl.value,(e=>(n(),w(F,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),d(b,{label:"返厂原因",required:""},{default:r((()=>[d(y,{modelValue:kl.value.reason,"onUpdate:modelValue":l[31]||(l[31]=e=>kl.value.reason=e),type:"textarea",rows:3,placeholder:"请详细描述返厂原因"},null,8,["modelValue"])])),_:1}),d(b,{label:"预计返回时间"},{default:r((()=>[d(L,{modelValue:kl.value.expectedReturnDate,"onUpdate:modelValue":l[32]||(l[32]=e=>kl.value.expectedReturnDate=e),type:"date",placeholder:"选择预计返回时间",style:{width:"100%"},"value-format":"YYYY-MM-DD"},null,8,["modelValue"])])),_:1}),d(b,{label:"备注"},{default:r((()=>[d(y,{modelValue:kl.value.notes,"onUpdate:modelValue":l[33]||(l[33]=e=>kl.value.notes=e),type:"textarea",rows:2,placeholder:"其他备注信息"},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])]})),_:1},8,["modelValue","title"]),d(Bl,{modelValue:Pa.value,"onUpdate:modelValue":l[39]||(l[39]=e=>Pa.value=e),title:"故障详情",width:"70%","close-on-click-modal":!1},{footer:r((()=>[i("span",ra,[d(s,{onClick:l[36]||(l[36]=e=>Pa.value=!1)},{default:r((()=>l[105]||(l[105]=[f("关闭")]))),_:1}),d(s,{type:"success",onClick:l[37]||(l[37]=e=>Ua(Na.value)),disabled:!cl(Na.value)},{default:r((()=>l[106]||(l[106]=[f(" 处理故障 ")]))),_:1},8,["disabled"]),d(s,{type:"warning",onClick:l[38]||(l[38]=e=>$l(Na.value)),disabled:!pl(Na.value)},{default:r((()=>l[107]||(l[107]=[f(" 申请返厂 ")]))),_:1},8,["disabled"])])])),default:r((()=>[Na.value?(n(),o("div",Ee,[d(Fl,{gutter:20},{default:r((()=>[d(Ml,{span:12},{default:r((()=>[i("div",He,[l[92]||(l[92]=i("label",null,"故障编号：",-1)),i("span",null,T(Na.value.code),1)])])),_:1}),d(Ml,{span:12},{default:r((()=>{return[i("div",Ae,[l[93]||(l[93]=i("label",null,"故障状态：",-1)),d(_a,{type:(e=Na.value.status,{0:"warning",1:"primary",2:"success",3:"info"}[e]||"info")},{default:r((()=>[f(T(Na.value.statusName),1)])),_:1},8,["type"])])];var e})),_:1})])),_:1}),d(Fl,{gutter:20},{default:r((()=>[d(Ml,{span:12},{default:r((()=>[i("div",We,[l[94]||(l[94]=i("label",null,"故障标题：",-1)),i("span",null,T(Na.value.title),1)])])),_:1}),d(Ml,{span:12},{default:r((()=>[i("div",Xe,[l[95]||(l[95]=i("label",null,"故障类型：",-1)),i("span",null,T(Na.value.faultTypeName),1)])])),_:1})])),_:1}),d(Fl,{gutter:20},{default:r((()=>[d(Ml,{span:12},{default:r((()=>[i("div",Je,[l[96]||(l[96]=i("label",null,"关联资产：",-1)),i("span",null,T(Na.value.assetName),1),Na.value.assetCode?(n(),o("span",Ze,"("+T(Na.value.assetCode)+")",1)):h("",!0)])])),_:1}),d(Ml,{span:12},{default:r((()=>{return[i("div",ea,[l[97]||(l[97]=i("label",null,"优先级：",-1)),d(_a,{type:(e=Na.value.priority,{low:"info",medium:"warning",high:"danger"}[e]||"info")},{default:r((()=>[f(T(il(Na.value.priority)),1)])),_:1},8,["type"])])];var e})),_:1})])),_:1}),d(Fl,{gutter:20},{default:r((()=>[d(Ml,{span:12},{default:r((()=>[i("div",aa,[l[98]||(l[98]=i("label",null,"报告人：",-1)),i("span",null,T(Na.value.reportUser),1)])])),_:1}),d(Ml,{span:12},{default:r((()=>[i("div",la,[l[99]||(l[99]=i("label",null,"处理人：",-1)),i("span",null,T(Na.value.handler||"未分配"),1)])])),_:1})])),_:1}),d(Fl,{gutter:20},{default:r((()=>[d(Ml,{span:12},{default:r((()=>[i("div",ta,[l[100]||(l[100]=i("label",null,"报告时间：",-1)),i("span",null,T(Na.value.reportTime),1)])])),_:1}),d(Ml,{span:12},{default:r((()=>[i("div",sa,[l[101]||(l[101]=i("label",null,"更新时间：",-1)),i("span",null,T(Na.value.updateTime),1)])])),_:1})])),_:1}),Na.value.happenTime?(n(),w(Fl,{key:0,gutter:20},{default:r((()=>[d(Ml,{span:12},{default:r((()=>[i("div",ua,[l[102]||(l[102]=i("label",null,"发生时间：",-1)),i("span",null,T(Na.value.happenTime),1)])])),_:1}),d(Ml,{span:12},{default:r((()=>[i("div",oa,[l[103]||(l[103]=i("label",null,"位置：",-1)),i("span",null,T(Na.value.locationName||"未指定"),1)])])),_:1})])),_:1})):h("",!0),i("div",na,[l[104]||(l[104]=i("label",null,"故障描述：",-1)),i("div",da,T(Na.value.description),1)])])):h("",!0)])),_:1},8,["modelValue"]),d(Bl,{modelValue:xa.value,"onUpdate:modelValue":l[47]||(l[47]=e=>xa.value=e),title:"故障处理",width:"60%","close-on-click-modal":!1},{footer:r((()=>[i("span",ia,[d(s,{onClick:l[46]||(l[46]=e=>xa.value=!1)},{default:r((()=>l[110]||(l[110]=[f("取消")]))),_:1}),d(s,{type:"primary",onClick:za},{default:r((()=>[f(T("return"===Ia.value.processType?"申请返厂":"开始处理"),1)])),_:1})])])),default:r((()=>[d(Y,{model:Ia.value,"label-width":"120px"},{default:r((()=>[d(Fl,{gutter:20},{default:r((()=>[d(Ml,{span:12},{default:r((()=>[d(b,{label:"故障编号："},{default:r((()=>[d(y,{modelValue:Ia.value.code,"onUpdate:modelValue":l[40]||(l[40]=e=>Ia.value.code=e),disabled:""},null,8,["modelValue"])])),_:1})])),_:1}),d(Ml,{span:12},{default:r((()=>[d(b,{label:"故障标题："},{default:r((()=>[d(y,{modelValue:Ia.value.title,"onUpdate:modelValue":l[41]||(l[41]=e=>Ia.value.title=e),disabled:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),d(b,{label:"处理方式："},{default:r((()=>[d(Sl,{modelValue:Ia.value.processType,"onUpdate:modelValue":l[42]||(l[42]=e=>Ia.value.processType=e)},{default:r((()=>[d(Tl,{value:"repair"},{default:r((()=>l[108]||(l[108]=[f("现场维修")]))),_:1}),d(Tl,{value:"return"},{default:r((()=>l[109]||(l[109]=[f("申请返厂")]))),_:1})])),_:1},8,["modelValue"])])),_:1}),d(b,{label:"处理人员："},{default:r((()=>[d(K,{modelValue:Ia.value.assigneeId,"onUpdate:modelValue":l[43]||(l[43]=e=>Ia.value.assigneeId=e),placeholder:"请选择处理人员",clearable:""},{default:r((()=>[(n(!0),o(C,null,$(e.userList,(e=>(n(),w(F,{key:e.id,label:e.username,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),"return"===Ia.value.processType?(n(),w(b,{key:0,label:"预计返回时间："},{default:r((()=>[d(L,{modelValue:Ia.value.expectedReturnDate,"onUpdate:modelValue":l[44]||(l[44]=e=>Ia.value.expectedReturnDate=e),type:"date",placeholder:"选择预计返回时间",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD"},null,8,["modelValue"])])),_:1})):h("",!0),d(b,{label:"处理备注："},{default:r((()=>[d(y,{modelValue:Ia.value.notes,"onUpdate:modelValue":l[45]||(l[45]=e=>Ia.value.notes=e),type:"textarea",rows:4,placeholder:"请输入处理备注..."},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue"]),d(A,{modelValue:yl.value,"onUpdate:modelValue":l[48]||(l[48]=e=>yl.value=e),"scan-type":bl.value,onScanSuccess:el,onManualInput:al},null,8,["modelValue","scan-type"])])}}},[["__scopeId","data-v-2ba27cc8"]]);export{ca as default};
