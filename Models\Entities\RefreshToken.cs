// IT资产管理系统 - 刷新令牌实体
// 文件路径: /Models/Entities/RefreshToken.cs
// 功能: 定义用户刷新令牌实体

using System;
using System.ComponentModel.DataAnnotations;

namespace ItAssetsSystem.Models.Entities
{
    /// <summary>
    /// 刷新令牌
    /// </summary>
    public class RefreshToken
    {
        /// <summary>
        /// 令牌ID
        /// </summary>
        [Key]
        public int Id { get; set; }
        
        /// <summary>
        /// 用户ID
        /// </summary>
        public int UserId { get; set; }
        
        /// <summary>
        /// 用户
        /// </summary>
        public User User { get; set; }
        
        /// <summary>
        /// 令牌
        /// </summary>
        [Required]
        [MaxLength(128)]
        public string Token { get; set; }
        
        /// <summary>
        /// JWT ID
        /// </summary>
        [Required]
        [MaxLength(128)]
        public string JwtId { get; set; }
        
        /// <summary>
        /// 是否已使用
        /// </summary>
        public bool IsUsed { get; set; }
        
        /// <summary>
        /// 是否已撤销
        /// </summary>
        public bool IsRevoked { get; set; }
        
        /// <summary>
        /// 添加日期
        /// </summary>
        public DateTime AddedDate { get; set; }
        
        /// <summary>
        /// 过期日期
        /// </summary>
        public DateTime ExpiryDate { get; set; }
    }
} 