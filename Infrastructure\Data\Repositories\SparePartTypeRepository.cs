// File: Infrastructure/Data/Repositories/SparePartTypeRepository.cs
// Description: 备品备件类型仓储实现类

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ItAssetsSystem.Core.Abstractions;
using ItAssetsSystem.Domain.Entities;
using ItAssetsSystem.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;

namespace ItAssetsSystem.Infrastructure.Data.Repositories
{
    /// <summary>
    /// 备品备件类型仓储实现类
    /// </summary>
    public class SparePartTypeRepository : ISparePartTypeRepository
    {
        private readonly AppDbContext _dbContext;
        
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="dbContext">数据库上下文</param>
        public SparePartTypeRepository(AppDbContext dbContext)
        {
            _dbContext = dbContext;
        }
        
        /// <summary>
        /// 获取所有类型列表
        /// </summary>
        /// <returns>类型列表</returns>
        public async Task<List<SparePartType>> GetAllAsync()
        {
            return await _dbContext.SparePartTypes
                .OrderBy(t => t.Name)
                .ToListAsync();
        }
        
        /// <summary>
        /// 根据ID获取类型
        /// </summary>
        /// <param name="id">类型ID</param>
        /// <returns>类型实体</returns>
        public async Task<SparePartType> GetByIdAsync(long id)
        {
            return await _dbContext.SparePartTypes
                .Include(t => t.Parent)
                .FirstOrDefaultAsync(t => t.Id == id);
        }
        
        /// <summary>
        /// 创建类型
        /// </summary>
        /// <param name="entity">类型实体</param>
        /// <returns>创建的类型实体</returns>
        public async Task<SparePartType> CreateAsync(SparePartType entity)
        {
            entity.CreatedAt = DateTime.Now;
            entity.UpdatedAt = DateTime.Now;
            
            // 设置层级和路径
            if (entity.ParentId.HasValue)
            {
                var parent = await _dbContext.SparePartTypes.FindAsync(entity.ParentId);
                if (parent != null)
                {
                    entity.Level = parent.Level + 1;
                    entity.Path = $"{parent.Path}{parent.Id},";
                }
                else
                {
                    entity.Level = 0;
                    entity.Path = ",";
                }
            }
            else
            {
                entity.Level = 0;
                entity.Path = ",";
            }
            
            await _dbContext.SparePartTypes.AddAsync(entity);
            await _dbContext.SaveChangesAsync();
            
            return entity;
        }
        
        /// <summary>
        /// 更新类型
        /// </summary>
        /// <param name="entity">类型实体</param>
        /// <returns>更新后的类型实体</returns>
        public async Task<SparePartType> UpdateAsync(SparePartType entity)
        {
            entity.UpdatedAt = DateTime.Now;
            
            var existingEntity = await _dbContext.SparePartTypes.FindAsync(entity.Id);
            if (existingEntity == null)
            {
                return null;
            }
            
            // 检查是否修改了父级
            if (existingEntity.ParentId != entity.ParentId)
            {
                // 不允许将父级设置为自身或自身的子级
                if (entity.ParentId == entity.Id || 
                    (entity.ParentId.HasValue && await IsChildTypeAsync(entity.Id, entity.ParentId.Value)))
                {
                    throw new InvalidOperationException("不能将父级设置为自身或其子级");
                }
                
                // 更新层级和路径
                if (entity.ParentId.HasValue)
                {
                    var parent = await _dbContext.SparePartTypes.FindAsync(entity.ParentId);
                    if (parent != null)
                    {
                        entity.Level = parent.Level + 1;
                        entity.Path = $"{parent.Path}{parent.Id},";
                    }
                    else
                    {
                        entity.Level = 0;
                        entity.Path = ",";
                    }
                }
                else
                {
                    entity.Level = 0;
                    entity.Path = ",";
                }
                
                // 更新所有子级的层级和路径
                var children = await GetAllChildrenAsync(entity.Id);
                foreach (var child in children)
                {
                    await UpdateChildPathAndLevelAsync(child, entity.Path, entity.Level);
                }
            }
            
            // 更新实体属性
            _dbContext.Entry(existingEntity).CurrentValues.SetValues(entity);
            
            await _dbContext.SaveChangesAsync();
            
            return existingEntity;
        }
        
        /// <summary>
        /// 删除类型
        /// </summary>
        /// <param name="id">类型ID</param>
        /// <returns>是否成功</returns>
        public async Task<bool> DeleteAsync(long id)
        {
            using var transaction = await _dbContext.Database.BeginTransactionAsync();
            
            try
            {
                // 检查是否有子类型
                var hasChildren = await _dbContext.SparePartTypes
                    .AnyAsync(t => t.ParentId == id);
                
                if (hasChildren)
                {
                    throw new InvalidOperationException("不能删除有子类型的类型");
                }
                
                // 检查是否有关联的备件
                var hasRelatedParts = await _dbContext.SpareParts
                    .AnyAsync(p => p.TypeId == id);
                
                if (hasRelatedParts)
                {
                    throw new InvalidOperationException("不能删除有关联备件的类型");
                }
                
                var entity = await _dbContext.SparePartTypes.FindAsync(id);
                if (entity == null)
                {
                    return false;
                }
                
                _dbContext.SparePartTypes.Remove(entity);
                await _dbContext.SaveChangesAsync();
                
                await transaction.CommitAsync();
                
                return true;
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }
        }
        
        /// <summary>
        /// 检查编码是否已存在
        /// </summary>
        /// <param name="code">编码</param>
        /// <param name="excludeId">排除的ID</param>
        /// <returns>是否存在</returns>
        public async Task<bool> IsCodeExistsAsync(string code, long? excludeId = null)
        {
            var query = _dbContext.SparePartTypes.Where(t => t.Code == code);
            
            if (excludeId.HasValue)
            {
                query = query.Where(t => t.Id != excludeId.Value);
            }
            
            return await query.AnyAsync();
        }
        
        /// <summary>
        /// 获取类型的所有子类型ID
        /// </summary>
        /// <param name="parentId">父类型ID</param>
        /// <returns>子类型ID列表</returns>
        public async Task<List<long>> GetChildTypeIdsAsync(long parentId)
        {
            var parent = await _dbContext.SparePartTypes.FindAsync(parentId);
            if (parent == null)
            {
                return new List<long>();
            }
            
            return await _dbContext.SparePartTypes
                .Where(t => t.Path.Contains($",{parentId},"))
                .Select(t => t.Id)
                .ToListAsync();
        }
        
        /// <summary>
        /// 检查是否为子类型
        /// </summary>
        /// <param name="parentId">父类型ID</param>
        /// <param name="childId">子类型ID</param>
        /// <returns>是否为子类型</returns>
        private async Task<bool> IsChildTypeAsync(long parentId, long childId)
        {
            var child = await _dbContext.SparePartTypes.FindAsync(childId);
            if (child == null)
            {
                return false;
            }
            
            return child.Path.Contains($",{parentId},");
        }
        
        /// <summary>
        /// 获取所有子类型
        /// </summary>
        /// <param name="parentId">父类型ID</param>
        /// <returns>子类型列表</returns>
        private async Task<List<SparePartType>> GetAllChildrenAsync(long parentId)
        {
            var parent = await _dbContext.SparePartTypes.FindAsync(parentId);
            if (parent == null)
            {
                return new List<SparePartType>();
            }
            
            return await _dbContext.SparePartTypes
                .Where(t => t.Path.Contains($",{parentId},"))
                .ToListAsync();
        }
        
        /// <summary>
        /// 更新子类型的路径和层级
        /// </summary>
        /// <param name="child">子类型</param>
        /// <param name="parentPath">父级路径</param>
        /// <param name="parentLevel">父级层级</param>
        private async Task UpdateChildPathAndLevelAsync(SparePartType child, string parentPath, int parentLevel)
        {
            child.Path = $"{parentPath}{child.Id},";
            child.Level = parentLevel + 1;
            
            var children = await _dbContext.SparePartTypes
                .Where(t => t.ParentId == child.Id)
                .ToListAsync();
            
            foreach (var subChild in children)
            {
                await UpdateChildPathAndLevelAsync(subChild, child.Path, child.Level);
            }
        }
    }
} 