/*
 Navicat Premium Data Transfer

 Source Server         : IT资产
 Source Server Type    : MySQL
 Source Server Version : 80029
 Source Host           : localhost:3306
 Source Schema         : itassets

 Target Server Type    : MySQL
 Target Server Version : 80029
 File Encoding         : 65001

 Date: 06/04/2025 16:50:26
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for __efmigrationshistory
-- ----------------------------
DROP TABLE IF EXISTS `__efmigrationshistory`;
CREATE TABLE `__efmigrationshistory`  (
  `MigrationId` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `ProductVersion` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  PRIMARY KEY (`MigrationId`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'EF迁移历史表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for _script_execution_log
-- ----------------------------
DROP TABLE IF EXISTS `_script_execution_log`;
CREATE TABLE `_script_execution_log`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `step` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
  `executed_at` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 29 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for assethistories
-- ----------------------------
DROP TABLE IF EXISTS `assethistories`;
CREATE TABLE `assethistories`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `AssetId` int NOT NULL COMMENT '资产ID',
  `OperationType` int NOT NULL COMMENT '操作类型：1创建，2修改，3删除，4位置变更，5状态变更',
  `OperatorId` int NOT NULL COMMENT '操作人ID',
  `OperationTime` datetime(0) NOT NULL COMMENT '操作时间',
  `Description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '描述（JSON格式，记录变更前后的属性值）',
  `CreatedAt` datetime(0) NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime(0) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`) USING BTREE,
  INDEX `IX_AssetHistories_AssetId`(`AssetId`) USING BTREE,
  INDEX `IX_AssetHistories_OperatorId`(`OperatorId`) USING BTREE,
  CONSTRAINT `FK_AssetHistories_Assets_AssetId` FOREIGN KEY (`AssetId`) REFERENCES `assets` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_AssetHistories_Users_OperatorId` FOREIGN KEY (`OperatorId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 65 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '资产历史表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for assetreceives
-- ----------------------------
DROP TABLE IF EXISTS `assetreceives`;
CREATE TABLE `assetreceives`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `ReceiveCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '入库单号',
  `PurchaseOrderId` int NULL DEFAULT NULL COMMENT '采购订单ID',
  `Title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '标题',
  `Description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '描述',
  `ReceiverId` int NOT NULL COMMENT '接收人ID',
  `ReceiveTime` datetime(0) NOT NULL COMMENT '接收时间',
  `Status` int NOT NULL DEFAULT 0 COMMENT '状态：0草稿，1已提交，2已确认',
  `AssetTypeId` int NULL DEFAULT NULL COMMENT '资产类型ID',
  `Quantity` int NOT NULL DEFAULT 1 COMMENT '数量',
  `TotalAmount` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '总金额',
  `InitialLocationId` int NULL DEFAULT NULL COMMENT '初始位置ID',
  `Notes` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `CreatedAt` datetime(0) NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime(0) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE INDEX `IX_AssetReceives_ReceiveCode`(`ReceiveCode`) USING BTREE,
  INDEX `IX_AssetReceives_PurchaseOrderId`(`PurchaseOrderId`) USING BTREE,
  INDEX `IX_AssetReceives_ReceiverId`(`ReceiverId`) USING BTREE,
  INDEX `IX_AssetReceives_AssetTypeId`(`AssetTypeId`) USING BTREE,
  INDEX `IX_AssetReceives_InitialLocationId`(`InitialLocationId`) USING BTREE,
  CONSTRAINT `FK_AssetReceives_AssetTypes_AssetTypeId` FOREIGN KEY (`AssetTypeId`) REFERENCES `assettypes` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_AssetReceives_Locations_InitialLocationId` FOREIGN KEY (`InitialLocationId`) REFERENCES `locations` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_AssetReceives_PurchaseOrders_PurchaseOrderId` FOREIGN KEY (`PurchaseOrderId`) REFERENCES `purchaseorders` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_AssetReceives_Users_ReceiverId` FOREIGN KEY (`ReceiverId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '资产入库表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for assets
-- ----------------------------
DROP TABLE IF EXISTS `assets`;
CREATE TABLE `assets`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `assetCode` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '资产编号(IT-类型-年月日-序号)',
  `FinancialCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '财务编号',
  `Name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '资产名称',
  `AssetTypeId` int NOT NULL COMMENT '资产类型ID',
  `SerialNumber` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '序列号',
  `Model` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '型号',
  `Brand` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '品牌',
  `PurchaseDate` datetime(0) NULL DEFAULT NULL COMMENT '购买日期',
  `WarrantyExpireDate` datetime(0) NULL DEFAULT NULL COMMENT '保修到期日',
  `Price` decimal(18, 2) NULL DEFAULT NULL COMMENT '价格',
  `LocationId` int NULL DEFAULT NULL COMMENT '位置ID',
  `DepartmentId` int NULL DEFAULT NULL,
  `Status` int NOT NULL DEFAULT 0 COMMENT '状态：0闲置，1在用，2维修中，3报废',
  `Notes` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `CreatedAt` datetime(0) NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime(0) NOT NULL COMMENT '更新时间',
  `asset_code_prefix` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'IT' COMMENT '资产编号前缀',
  `inventory_id` int NULL DEFAULT NULL COMMENT '来源库存ID',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE INDEX `IX_Assets_AssetCode`(`assetCode`) USING BTREE,
  INDEX `IX_Assets_AssetTypeId`(`AssetTypeId`) USING BTREE,
  INDEX `IX_Assets_LocationId`(`LocationId`) USING BTREE,
  INDEX `IX_Assets_FinancialCode`(`FinancialCode`) USING BTREE,
  INDEX `IX_Assets_DepartmentId`(`DepartmentId`) USING BTREE,
  CONSTRAINT `FK_Assets_AssetTypes_AssetTypeId` FOREIGN KEY (`AssetTypeId`) REFERENCES `assettypes` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_Assets_Departments_DepartmentId` FOREIGN KEY (`DepartmentId`) REFERENCES `departments` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_Assets_Locations_LocationId` FOREIGN KEY (`LocationId`) REFERENCES `locations` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 914 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '资产表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for assettypes
-- ----------------------------
DROP TABLE IF EXISTS `assettypes`;
CREATE TABLE `assettypes`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `Name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '类型名称',
  `Code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '类型编码',
  `Description` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '类型描述',
  `ParentId` int NULL DEFAULT NULL COMMENT '父类型ID',
  `RequireSerialNumber` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否需要序列号',
  `SortOrder` int NOT NULL DEFAULT 0 COMMENT '排序',
  `IsActive` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否激活',
  `CreatedAt` datetime(0) NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime(0) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE INDEX `IX_AssetTypes_Code`(`Code`) USING BTREE,
  UNIQUE INDEX `IX_AssetTypes_Name`(`Name`) USING BTREE,
  INDEX `IX_AssetTypes_ParentId`(`ParentId`) USING BTREE,
  CONSTRAINT `FK_AssetTypes_AssetTypes_ParentId` FOREIGN KEY (`ParentId`) REFERENCES `assettypes` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 31 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '资产类型表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for auditlogs
-- ----------------------------
DROP TABLE IF EXISTS `auditlogs`;
CREATE TABLE `auditlogs`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `UserId` int NULL DEFAULT NULL COMMENT '用户ID',
  `UserName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '用户名',
  `Type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '日志类型',
  `TableName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '表名',
  `DateTime` datetime(0) NOT NULL COMMENT '日期时间',
  `PrimaryKey` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '主键',
  `OldValues` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '旧值',
  `NewValues` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '新值',
  `Action` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '操作',
  `ClientIP` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '客户端IP',
  PRIMARY KEY (`Id`) USING BTREE,
  INDEX `IX_AuditLogs_UserId`(`UserId`) USING BTREE,
  CONSTRAINT `FK_AuditLogs_Users_UserId` FOREIGN KEY (`UserId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '审计日志表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for daily_reports
-- ----------------------------
DROP TABLE IF EXISTS `daily_reports`;
CREATE TABLE `daily_reports`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `report_date` date NOT NULL,
  `report_type` enum('daily','weekly','monthly') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'daily',
  `file_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '报表文件路径',
  `snapshot_data` json NULL COMMENT '数据快照',
  `created_at` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_report_date_type`(`report_date`, `report_type`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for dashboard_config
-- ----------------------------
DROP TABLE IF EXISTS `dashboard_config`;
CREATE TABLE `dashboard_config`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `theme` enum('light','dark') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'dark',
  `layout` json NULL COMMENT '面板布局配置',
  `auto_refresh` int NULL DEFAULT 60 COMMENT '刷新间隔(秒)',
  `updated_at` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_dashboard_user`(`user_id`) USING BTREE,
  CONSTRAINT `fk_dashboard_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`Id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for db_structure_result
-- ----------------------------
DROP TABLE IF EXISTS `db_structure_result`;
CREATE TABLE `db_structure_result`  (
  `section` varchar(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '',
  `表名` varchar(64) CHARACTER SET utf8 COLLATE utf8_tolower_ci NULL DEFAULT NULL,
  `列名` varchar(64) CHARACTER SET utf8 COLLATE utf8_tolower_ci NULL DEFAULT NULL,
  `数据类型` mediumtext CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `可为空` varchar(3) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
  `默认值` text CHARACTER SET utf8 COLLATE utf8_bin NULL,
  `键` enum('','PRI','UNI','MUL') CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `额外信息` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `注释` text CHARACTER SET utf8 COLLATE utf8_bin NOT NULL
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for departments
-- ----------------------------
DROP TABLE IF EXISTS `departments`;
CREATE TABLE `departments`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `Name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '部门名称',
  `Code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '部门编码',
  `ParentId` int NULL DEFAULT NULL COMMENT '父部门ID',
  `ManagerId` int NULL DEFAULT NULL COMMENT '部门经理ID',
  `DeputyManagerId` int NULL DEFAULT NULL COMMENT '部门主任ID（二级负责人）',
  `Path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '层级路径',
  `Description` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '部门描述',
  `IsActive` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否激活',
  `CreatedAt` datetime(0) NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime(0) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE INDEX `IX_Departments_Code`(`Code`) USING BTREE,
  UNIQUE INDEX `IX_Departments_Name`(`Name`) USING BTREE,
  INDEX `IX_Departments_ParentId`(`ParentId`) USING BTREE,
  INDEX `IX_Departments_ManagerId`(`ManagerId`) USING BTREE,
  INDEX `IX_Departments_DeputyManagerId`(`DeputyManagerId`) USING BTREE,
  CONSTRAINT `FK_Departments_Departments_ParentId` FOREIGN KEY (`ParentId`) REFERENCES `departments` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_Departments_Personnel_DeputyManagerId` FOREIGN KEY (`DeputyManagerId`) REFERENCES `personnel` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 28 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '部门表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for faultrecords
-- ----------------------------
DROP TABLE IF EXISTS `faultrecords`;
CREATE TABLE `faultrecords`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `Title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '标题',
  `Description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '描述',
  `AssetId` int NOT NULL COMMENT '资产ID',
  `FaultTypeId` int NOT NULL COMMENT '故障类型ID',
  `LocationId` int NULL DEFAULT NULL COMMENT '位置ID',
  `ReporterId` int NOT NULL COMMENT '报告人ID',
  `ReportTime` datetime(0) NOT NULL COMMENT '报告时间',
  `Status` int NOT NULL DEFAULT 0 COMMENT '状态：0待处理，1处理中，2已解决，3已关闭',
  `Severity` int NOT NULL DEFAULT 0 COMMENT '严重程度：0一般，1严重，2紧急',
  `AssigneeId` int NULL DEFAULT NULL COMMENT '处理人ID',
  `AssignTime` datetime(0) NULL DEFAULT NULL COMMENT '分配时间',
  `ResponseTime` datetime(0) NULL DEFAULT NULL COMMENT '响应时间',
  `ResolutionTime` datetime(0) NULL DEFAULT NULL COMMENT '解决时间',
  `Resolution` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '解决方案',
  `RootCause` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '根本原因',
  `Notes` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `IsReturned` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否返厂',
  `CreatedAt` datetime(0) NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime(0) NOT NULL COMMENT '更新时间',
  `faultNumber` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '故障单号(FIX-年月日-序号)',
  `expected_return_date` datetime(0) NULL DEFAULT NULL COMMENT '预期返厂日期',
  `warning_flag` tinyint(1) NULL DEFAULT 0 COMMENT '超期预警标记',
  PRIMARY KEY (`Id`) USING BTREE,
  INDEX `IX_FaultRecords_AssetId`(`AssetId`) USING BTREE,
  INDEX `IX_FaultRecords_FaultTypeId`(`FaultTypeId`) USING BTREE,
  INDEX `IX_FaultRecords_LocationId`(`LocationId`) USING BTREE,
  INDEX `IX_FaultRecords_ReporterId`(`ReporterId`) USING BTREE,
  INDEX `IX_FaultRecords_AssigneeId`(`AssigneeId`) USING BTREE,
  INDEX `idx_faultrecords_faultNumber`(`faultNumber`) USING BTREE,
  CONSTRAINT `FK_FaultRecords_Assets_AssetId` FOREIGN KEY (`AssetId`) REFERENCES `assets` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_FaultRecords_FaultTypes_FaultTypeId` FOREIGN KEY (`FaultTypeId`) REFERENCES `faulttypes` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_FaultRecords_Locations_LocationId` FOREIGN KEY (`LocationId`) REFERENCES `locations` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_FaultRecords_Users_AssigneeId` FOREIGN KEY (`AssigneeId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_FaultRecords_Users_ReporterId` FOREIGN KEY (`ReporterId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '故障记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for faulttypes
-- ----------------------------
DROP TABLE IF EXISTS `faulttypes`;
CREATE TABLE `faulttypes`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `Name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '类型名称',
  `Code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '类型编码',
  `Description` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '类型描述',
  `Severity` int NOT NULL DEFAULT 0 COMMENT '严重程度：0一般，1严重，2紧急',
  `SuggestedResponseTime` int NULL DEFAULT NULL COMMENT '建议响应时间（小时）',
  `SuggestedResolutionTime` int NULL DEFAULT NULL COMMENT '建议解决时间（小时）',
  `SortOrder` int NOT NULL DEFAULT 0 COMMENT '排序',
  `IsActive` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否激活',
  `CreatedAt` datetime(0) NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime(0) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE INDEX `IX_FaultTypes_Code`(`Code`) USING BTREE,
  UNIQUE INDEX `IX_FaultTypes_Name`(`Name`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 16 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '故障类型表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for inventory_thresholds
-- ----------------------------
DROP TABLE IF EXISTS `inventory_thresholds`;
CREATE TABLE `inventory_thresholds`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `asset_type_id` int NOT NULL,
  `min_threshold` int NOT NULL DEFAULT 5 COMMENT '最小安全库存',
  `warning_threshold` int NOT NULL DEFAULT 10 COMMENT '预警库存',
  `updated_at` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_inventory_asset_type`(`asset_type_id`) USING BTREE,
  CONSTRAINT `fk_inventory_asset_type` FOREIGN KEY (`asset_type_id`) REFERENCES `assettypes` (`Id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for locationhistories
-- ----------------------------
DROP TABLE IF EXISTS `locationhistories`;
CREATE TABLE `locationhistories`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `AssetId` int NOT NULL COMMENT '资产ID',
  `OldLocationId` int NULL DEFAULT NULL COMMENT '旧位置ID',
  `NewLocationId` int NOT NULL COMMENT '新位置ID',
  `OperatorId` int NOT NULL COMMENT '操作人ID',
  `ChangeType` int NOT NULL DEFAULT 0 COMMENT '变更类型：0转移，1领用，2归还',
  `ChangeTime` datetime(0) NOT NULL COMMENT '变更时间',
  `Notes` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `CreatedAt` datetime(0) NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`Id`) USING BTREE,
  INDEX `IX_LocationHistories_AssetId`(`AssetId`) USING BTREE,
  INDEX `IX_LocationHistories_OldLocationId`(`OldLocationId`) USING BTREE,
  INDEX `IX_LocationHistories_NewLocationId`(`NewLocationId`) USING BTREE,
  INDEX `IX_LocationHistories_OperatorId`(`OperatorId`) USING BTREE,
  CONSTRAINT `FK_LocationHistories_Assets_AssetId` FOREIGN KEY (`AssetId`) REFERENCES `assets` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_LocationHistories_Locations_NewLocationId` FOREIGN KEY (`NewLocationId`) REFERENCES `locations` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_LocationHistories_Locations_OldLocationId` FOREIGN KEY (`OldLocationId`) REFERENCES `locations` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_LocationHistories_Users_OperatorId` FOREIGN KEY (`OperatorId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 13 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '位置历史表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for locations
-- ----------------------------
DROP TABLE IF EXISTS `locations`;
CREATE TABLE `locations`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `Code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '位置编码',
  `Name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '位置名称',
  `Type` int NOT NULL DEFAULT 0 COMMENT '位置类型：0厂区，1产线，2工序，3工位，4设备位置',
  `ParentId` int NULL DEFAULT NULL COMMENT '父位置ID',
  `Path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '层级路径',
  `Description` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '位置描述',
  `DefaultDepartmentId` int NULL DEFAULT NULL COMMENT '默认部门ID',
  `DefaultResponsiblePersonId` int NULL DEFAULT NULL COMMENT '默认负责人ID',
  `IsActive` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否激活',
  `CreatedAt` datetime(0) NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime(0) NOT NULL COMMENT '更新时间',
  `level` tinyint NOT NULL DEFAULT 3 COMMENT '位置级别(1-5)',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE INDEX `IX_Locations_Code`(`Code`) USING BTREE,
  INDEX `IX_Locations_ParentId`(`ParentId`) USING BTREE,
  INDEX `IX_Locations_DefaultDepartmentId`(`DefaultDepartmentId`) USING BTREE,
  INDEX `IX_Locations_DefaultResponsiblePersonId`(`DefaultResponsiblePersonId`) USING BTREE,
  INDEX `idx_locations_level`(`level`) USING BTREE,
  CONSTRAINT `FK_Locations_Departments_DefaultDepartmentId` FOREIGN KEY (`DefaultDepartmentId`) REFERENCES `departments` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_Locations_Locations_ParentId` FOREIGN KEY (`ParentId`) REFERENCES `locations` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_Locations_Users_DefaultResponsiblePersonId` FOREIGN KEY (`DefaultResponsiblePersonId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 287 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '位置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for locationusers
-- ----------------------------
DROP TABLE IF EXISTS `locationusers`;
CREATE TABLE `locationusers`  (
  `location_id` int NOT NULL,
  `personnel_id` int NOT NULL,
  `user_type` tinyint NOT NULL COMMENT '0-使用人 1-管理员',
  `created_at` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`location_id`, `personnel_id`, `user_type`) USING BTREE,
  INDEX `personnel_id`(`personnel_id`) USING BTREE,
  CONSTRAINT `locationusers_ibfk_1` FOREIGN KEY (`location_id`) REFERENCES `locations` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `locationusers_ibfk_2` FOREIGN KEY (`personnel_id`) REFERENCES `personnel` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for maintenanceorders
-- ----------------------------
DROP TABLE IF EXISTS `maintenanceorders`;
CREATE TABLE `maintenanceorders`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `OrderCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '维护单号',
  `Title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '标题',
  `Description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '描述',
  `AssetId` int NOT NULL COMMENT '资产ID',
  `FaultRecordId` int NULL DEFAULT NULL COMMENT '故障记录ID',
  `MaintenanceType` int NOT NULL DEFAULT 0 COMMENT '维护类型：0常规维护，1故障维修，2返厂维修跟踪',
  `Status` int NOT NULL DEFAULT 0 COMMENT '状态：0待处理，1处理中，2已完成，3已取消',
  `CreatorId` int NOT NULL COMMENT '创建人ID',
  `CreateTime` datetime(0) NOT NULL COMMENT '创建时间',
  `AssigneeId` int NULL DEFAULT NULL COMMENT '处理人ID',
  `AssignTime` datetime(0) NULL DEFAULT NULL COMMENT '分配时间',
  `PlanStartTime` datetime(0) NULL DEFAULT NULL COMMENT '计划开始时间',
  `PlanEndTime` datetime(0) NULL DEFAULT NULL COMMENT '计划结束时间',
  `ActualStartTime` datetime(0) NULL DEFAULT NULL COMMENT '实际开始时间',
  `ActualEndTime` datetime(0) NULL DEFAULT NULL COMMENT '实际结束时间',
  `Solution` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '解决方案',
  `Notes` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `CreatedAt` datetime(0) NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime(0) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE INDEX `IX_MaintenanceOrders_OrderCode`(`OrderCode`) USING BTREE,
  INDEX `IX_MaintenanceOrders_AssetId`(`AssetId`) USING BTREE,
  INDEX `IX_MaintenanceOrders_FaultRecordId`(`FaultRecordId`) USING BTREE,
  INDEX `IX_MaintenanceOrders_CreatorId`(`CreatorId`) USING BTREE,
  INDEX `IX_MaintenanceOrders_AssigneeId`(`AssigneeId`) USING BTREE,
  CONSTRAINT `FK_MaintenanceOrders_Assets_AssetId` FOREIGN KEY (`AssetId`) REFERENCES `assets` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_MaintenanceOrders_FaultRecords_FaultRecordId` FOREIGN KEY (`FaultRecordId`) REFERENCES `faultrecords` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_MaintenanceOrders_Users_AssigneeId` FOREIGN KEY (`AssigneeId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_MaintenanceOrders_Users_CreatorId` FOREIGN KEY (`CreatorId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '维护订单表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for menus
-- ----------------------------
DROP TABLE IF EXISTS `menus`;
CREATE TABLE `menus`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `Name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '菜单名称',
  `Code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '菜单编码',
  `ParentId` int NULL DEFAULT NULL COMMENT '父菜单ID',
  `Type` int NOT NULL DEFAULT 0 COMMENT '菜单类型：0菜单，1按钮',
  `Icon` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '图标',
  `Path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '路由路径',
  `Component` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '组件路径',
  `Permission` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '权限标识',
  `Description` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '描述',
  `SortOrder` int NOT NULL DEFAULT 0 COMMENT '排序',
  `IsExternal` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否外链',
  `KeepAlive` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否缓存',
  `IsVisible` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否可见',
  `IsActive` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否激活',
  `CreatedAt` datetime(0) NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime(0) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE INDEX `IX_Menus_Code`(`Code`) USING BTREE,
  INDEX `IX_Menus_ParentId`(`ParentId`) USING BTREE,
  CONSTRAINT `FK_Menus_Menus_ParentId` FOREIGN KEY (`ParentId`) REFERENCES `menus` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 27 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '菜单表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for notifications
-- ----------------------------
DROP TABLE IF EXISTS `notifications`;
CREATE TABLE `notifications`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '通知类型：task/system/alert',
  `reference_id` int NULL DEFAULT NULL COMMENT '关联ID',
  `reference_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '关联类型',
  `is_read` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
  `read_at` datetime(0) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_notifications_user_id`(`user_id`) USING BTREE,
  INDEX `idx_notifications_created_at`(`created_at`) USING BTREE,
  CONSTRAINT `fk_notifications_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`Id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for pdcaplans
-- ----------------------------
DROP TABLE IF EXISTS `pdcaplans`;
CREATE TABLE `pdcaplans`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `Title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '计划标题',
  `Type` int NOT NULL DEFAULT 0 COMMENT '类型：0硬件，1软件，2流程',
  `PlanStartDate` datetime(0) NOT NULL COMMENT '计划开始日期',
  `PlanEndDate` datetime(0) NOT NULL COMMENT '计划结束日期',
  `ActualEndDate` datetime(0) NULL DEFAULT NULL COMMENT '实际结束日期',
  `Status` int NOT NULL DEFAULT 0 COMMENT '状态：0计划，1进行中，2已完成，3已取消',
  `CompletionRate` decimal(5, 2) NOT NULL DEFAULT 0.00 COMMENT '完成率',
  `ResponsiblePersonId` int NOT NULL COMMENT '负责人ID',
  `Notes` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `CreatedAt` datetime(0) NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime(0) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`) USING BTREE,
  INDEX `IX_PdcaPlans_ResponsiblePersonId`(`ResponsiblePersonId`) USING BTREE,
  CONSTRAINT `FK_PdcaPlans_Users_ResponsiblePersonId` FOREIGN KEY (`ResponsiblePersonId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'PDCA计划表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for periodicrules
-- ----------------------------
DROP TABLE IF EXISTS `periodicrules`;
CREATE TABLE `periodicrules`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `Name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '规则名称',
  `Frequency` int NOT NULL DEFAULT 0 COMMENT '频率：0每天，1每周，2每月，3每季度，4每年',
  `DayOfWeek` int NULL DEFAULT NULL COMMENT '每周几（0-6）',
  `DayOfMonth` int NULL DEFAULT NULL COMMENT '每月几号（1-31）',
  `IsActive` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否激活',
  `CreatedAt` datetime(0) NOT NULL COMMENT '创建时间',
  `LastGeneratedAt` datetime(0) NULL DEFAULT NULL COMMENT '上次生成时间',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE INDEX `IX_PeriodicRules_Name`(`Name`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '周期规则表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for personnel
-- ----------------------------
DROP TABLE IF EXISTS `personnel`;
CREATE TABLE `personnel`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `position` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `contact` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `department_id` int NULL DEFAULT NULL,
  `employee_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `created_at` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0),
  `updated_at` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `department_id`(`department_id`) USING BTREE,
  CONSTRAINT `personnel_ibfk_1` FOREIGN KEY (`department_id`) REFERENCES `departments` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for point_history
-- ----------------------------
DROP TABLE IF EXISTS `point_history`;
CREATE TABLE `point_history`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `action_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '如：新建任务/更新进度/完成任务',
  `points` int NOT NULL COMMENT '获得或扣除的积分',
  `reference_id` int NULL DEFAULT NULL COMMENT '关联ID(如任务ID)',
  `reference_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '关联类型(如task)',
  `created_at` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_point_history_user_id`(`user_id`) USING BTREE,
  INDEX `idx_point_history_created_at`(`created_at`) USING BTREE,
  CONSTRAINT `fk_point_history_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`Id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for point_leaderboard
-- ----------------------------
DROP TABLE IF EXISTS `point_leaderboard`;
CREATE TABLE `point_leaderboard`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `points` int NOT NULL DEFAULT 0,
  `ranking` int NOT NULL,
  `leaderboard_date` date NOT NULL,
  `leaderboard_type` enum('daily','weekly','monthly','total') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `created_at` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_leaderboard_unique`(`user_id`, `leaderboard_date`, `leaderboard_type`) USING BTREE,
  INDEX `idx_leaderboard_date_type`(`leaderboard_date`, `leaderboard_type`) USING BTREE,
  CONSTRAINT `fk_point_leaderboard_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`Id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for purchaseitems
-- ----------------------------
DROP TABLE IF EXISTS `purchaseitems`;
CREATE TABLE `purchaseitems`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `PurchaseOrderId` int NOT NULL COMMENT '采购订单ID',
  `ItemName` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '项目名称',
  `ItemCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '项目编码',
  `Specification` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '规格',
  `AssetTypeId` int NULL DEFAULT NULL COMMENT '资产类型ID',
  `UnitPrice` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '单价',
  `Quantity` int NOT NULL DEFAULT 1 COMMENT '数量',
  `TotalPrice` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '总价',
  `Notes` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `CreatedAt` datetime(0) NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime(0) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`) USING BTREE,
  INDEX `IX_PurchaseItems_PurchaseOrderId`(`PurchaseOrderId`) USING BTREE,
  INDEX `IX_PurchaseItems_AssetTypeId`(`AssetTypeId`) USING BTREE,
  CONSTRAINT `FK_PurchaseItems_AssetTypes_AssetTypeId` FOREIGN KEY (`AssetTypeId`) REFERENCES `assettypes` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_PurchaseItems_PurchaseOrders_PurchaseOrderId` FOREIGN KEY (`PurchaseOrderId`) REFERENCES `purchaseorders` (`Id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '采购项目表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for purchaseorders
-- ----------------------------
DROP TABLE IF EXISTS `purchaseorders`;
CREATE TABLE `purchaseorders`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `OrderCode` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '采购单号(PO-年月日-时分秒)',
  `Title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '标题',
  `Description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '描述',
  `SupplierId` int NOT NULL COMMENT '供应商ID',
  `Status` int NOT NULL DEFAULT 0 COMMENT '状态：0草稿，1待审批，2已审批，3已发出，4已收货，5已完成，6已取消',
  `EstimatedDeliveryDate` datetime(0) NULL DEFAULT NULL COMMENT '预计交付日期',
  `ActualDeliveryDate` datetime(0) NULL DEFAULT NULL COMMENT '实际交付日期',
  `ApplicantId` int NOT NULL COMMENT '申请人ID',
  `ApplicationTime` datetime(0) NOT NULL COMMENT '申请时间',
  `ApproverId` int NULL DEFAULT NULL COMMENT '审批人ID',
  `ApprovalTime` datetime(0) NULL DEFAULT NULL COMMENT '审批时间',
  `TotalAmount` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '总金额',
  `Notes` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `CreatedAt` datetime(0) NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime(0) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE INDEX `IX_PurchaseOrders_OrderCode`(`OrderCode`) USING BTREE,
  INDEX `IX_PurchaseOrders_SupplierId`(`SupplierId`) USING BTREE,
  INDEX `IX_PurchaseOrders_ApplicantId`(`ApplicantId`) USING BTREE,
  INDEX `IX_PurchaseOrders_ApproverId`(`ApproverId`) USING BTREE,
  CONSTRAINT `FK_PurchaseOrders_Suppliers_SupplierId` FOREIGN KEY (`SupplierId`) REFERENCES `suppliers` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_PurchaseOrders_Users_ApplicantId` FOREIGN KEY (`ApplicantId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_PurchaseOrders_Users_ApproverId` FOREIGN KEY (`ApproverId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '采购订单表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for refreshtokens
-- ----------------------------
DROP TABLE IF EXISTS `refreshtokens`;
CREATE TABLE `refreshtokens`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `UserId` int NOT NULL COMMENT '用户ID',
  `Token` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '令牌',
  `JwtId` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'JWT ID',
  `IsUsed` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已使用',
  `IsRevoked` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已撤销',
  `AddedDate` datetime(0) NOT NULL COMMENT '添加日期',
  `ExpiryDate` datetime(0) NOT NULL COMMENT '过期日期',
  PRIMARY KEY (`Id`) USING BTREE,
  INDEX `IX_RefreshTokens_UserId`(`UserId`) USING BTREE,
  CONSTRAINT `FK_RefreshTokens_Users_UserId` FOREIGN KEY (`UserId`) REFERENCES `users` (`Id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '刷新令牌表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for returntofactories
-- ----------------------------
DROP TABLE IF EXISTS `returntofactories`;
CREATE TABLE `returntofactories`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `Code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '返厂单号',
  `AssetId` int NOT NULL COMMENT '资产ID',
  `FaultRecordId` int NOT NULL COMMENT '故障记录ID',
  `SupplierId` int NOT NULL COMMENT '供应商ID',
  `Status` int NOT NULL DEFAULT 0 COMMENT '状态：0待送出，1已送出，2维修中，3已返回，4维修失败',
  `SenderId` int NOT NULL COMMENT '送出人ID',
  `SendTime` datetime(0) NULL DEFAULT NULL COMMENT '送出时间',
  `EstimatedReturnTime` datetime(0) NULL DEFAULT NULL COMMENT '预计返回时间',
  `ActualReturnTime` datetime(0) NULL DEFAULT NULL COMMENT '实际返回时间',
  `RepairResult` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '维修结果',
  `RepairCost` decimal(18, 2) NULL DEFAULT NULL COMMENT '维修费用',
  `InWarranty` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否在保修期内',
  `Notes` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `CreatedAt` datetime(0) NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime(0) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE INDEX `IX_ReturnToFactories_Code`(`Code`) USING BTREE,
  INDEX `IX_ReturnToFactories_AssetId`(`AssetId`) USING BTREE,
  INDEX `IX_ReturnToFactories_FaultRecordId`(`FaultRecordId`) USING BTREE,
  INDEX `IX_ReturnToFactories_SupplierId`(`SupplierId`) USING BTREE,
  INDEX `IX_ReturnToFactories_SenderId`(`SenderId`) USING BTREE,
  CONSTRAINT `FK_ReturnToFactories_Assets_AssetId` FOREIGN KEY (`AssetId`) REFERENCES `assets` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_ReturnToFactories_FaultRecords_FaultRecordId` FOREIGN KEY (`FaultRecordId`) REFERENCES `faultrecords` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_ReturnToFactories_Suppliers_SupplierId` FOREIGN KEY (`SupplierId`) REFERENCES `suppliers` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_ReturnToFactories_Users_SenderId` FOREIGN KEY (`SenderId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '返厂维修表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rolemenus
-- ----------------------------
DROP TABLE IF EXISTS `rolemenus`;
CREATE TABLE `rolemenus`  (
  `RoleId` int NOT NULL COMMENT '角色ID',
  `MenuId` int NOT NULL COMMENT '菜单ID',
  `CreatedAt` datetime(0) NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`RoleId`, `MenuId`) USING BTREE,
  INDEX `IX_RoleMenus_MenuId`(`MenuId`) USING BTREE,
  CONSTRAINT `FK_RoleMenus_Menus_MenuId` FOREIGN KEY (`MenuId`) REFERENCES `menus` (`Id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `FK_RoleMenus_Roles_RoleId` FOREIGN KEY (`RoleId`) REFERENCES `roles` (`Id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '角色菜单关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for roles
-- ----------------------------
DROP TABLE IF EXISTS `roles`;
CREATE TABLE `roles`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `Name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '角色名称',
  `Code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '角色编码',
  `Description` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '角色描述',
  `IsActive` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否激活',
  `CreatedAt` datetime(0) NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime(0) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE INDEX `IX_Roles_Code`(`Code`) USING BTREE,
  UNIQUE INDEX `IX_Roles_Name`(`Name`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '角色表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for suppliers
-- ----------------------------
DROP TABLE IF EXISTS `suppliers`;
CREATE TABLE `suppliers`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `Name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '供应商名称',
  `Code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '供应商编码',
  `ContactPerson` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '联系人',
  `ContactPhone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '联系电话',
  `ContactEmail` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '联系邮箱',
  `Address` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '地址',
  `Type` int NOT NULL DEFAULT 0 COMMENT '类型：0硬件，1软件，2服务',
  `Notes` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `IsActive` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否激活',
  `CreatedAt` datetime(0) NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime(0) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE INDEX `IX_Suppliers_Code`(`Code`) USING BTREE,
  UNIQUE INDEX `IX_Suppliers_Name`(`Name`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '供应商表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for task_history
-- ----------------------------
DROP TABLE IF EXISTS `task_history`;
CREATE TABLE `task_history`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `task_id` int NOT NULL,
  `user_id` int NOT NULL,
  `action` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '记录完整操作语义',
  `snapshot` json NULL COMMENT '操作时的任务数据快照',
  `created_at` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_task_history_task_id`(`task_id`) USING BTREE,
  INDEX `idx_task_history_user_id`(`user_id`) USING BTREE,
  CONSTRAINT `fk_task_history_task` FOREIGN KEY (`task_id`) REFERENCES `tasks` (`Id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_task_history_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tasks
-- ----------------------------
DROP TABLE IF EXISTS `tasks`;
CREATE TABLE `tasks`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `Title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '任务标题',
  `Description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '任务描述',
  `TaskType` int NOT NULL DEFAULT 0 COMMENT '任务类型：0普通任务，1周期任务，2计划任务',
  `Status` int NOT NULL DEFAULT 0 COMMENT '状态：0待处理，1处理中，2已完成，3已取消',
  `AssignedToId` int NULL DEFAULT NULL COMMENT '分配给谁',
  `CreatedById` int NOT NULL COMMENT '创建人ID',
  `CreatedAt` datetime(0) NOT NULL COMMENT '创建时间',
  `DueDate` datetime(0) NULL DEFAULT NULL COMMENT '截止日期',
  `CompletedAt` datetime(0) NULL DEFAULT NULL COMMENT '完成时间',
  `RelatedAssetId` int NULL DEFAULT NULL COMMENT '相关资产ID',
  `RelatedLocationId` int NULL DEFAULT NULL COMMENT '相关位置ID',
  `PeriodicRuleId` int NULL DEFAULT NULL COMMENT '周期规则ID',
  `PdcaPlanId` int NULL DEFAULT NULL COMMENT 'PDCA计划ID',
  `Notes` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `UpdatedAt` datetime(0) NOT NULL COMMENT '更新时间',
  `progress` int NULL DEFAULT 0 COMMENT '进度百分比(0-100)',
  `points` int NULL DEFAULT 5 COMMENT '任务积分值',
  `claimed_by_id` int NULL DEFAULT NULL COMMENT '认领人ID',
  PRIMARY KEY (`Id`) USING BTREE,
  INDEX `IX_Tasks_AssignedToId`(`AssignedToId`) USING BTREE,
  INDEX `IX_Tasks_CreatedById`(`CreatedById`) USING BTREE,
  INDEX `IX_Tasks_RelatedAssetId`(`RelatedAssetId`) USING BTREE,
  INDEX `IX_Tasks_RelatedLocationId`(`RelatedLocationId`) USING BTREE,
  INDEX `IX_Tasks_PeriodicRuleId`(`PeriodicRuleId`) USING BTREE,
  INDEX `IX_Tasks_PdcaPlanId`(`PdcaPlanId`) USING BTREE,
  INDEX `fk_task_claimed_by`(`claimed_by_id`) USING BTREE,
  CONSTRAINT `fk_task_claimed_by` FOREIGN KEY (`claimed_by_id`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_Tasks_Assets_RelatedAssetId` FOREIGN KEY (`RelatedAssetId`) REFERENCES `assets` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_Tasks_Locations_RelatedLocationId` FOREIGN KEY (`RelatedLocationId`) REFERENCES `locations` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_Tasks_PdcaPlans_PdcaPlanId` FOREIGN KEY (`PdcaPlanId`) REFERENCES `pdcaplans` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_Tasks_PeriodicRules_PeriodicRuleId` FOREIGN KEY (`PeriodicRuleId`) REFERENCES `periodicrules` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_Tasks_Users_AssignedToId` FOREIGN KEY (`AssignedToId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_Tasks_Users_CreatedById` FOREIGN KEY (`CreatedById`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '任务表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for user_actions
-- ----------------------------
DROP TABLE IF EXISTS `user_actions`;
CREATE TABLE `user_actions`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `action` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '行为类型',
  `reference_id` int NULL DEFAULT NULL COMMENT '关联ID',
  `reference_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '关联类型',
  `points` int NULL DEFAULT 0 COMMENT '获得积分',
  `action_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_actions_user`(`user_id`) USING BTREE,
  INDEX `idx_user_actions_time`(`action_time`) USING BTREE,
  CONSTRAINT `fk_user_actions_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`Id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for user_points
-- ----------------------------
DROP TABLE IF EXISTS `user_points`;
CREATE TABLE `user_points`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `points` int NOT NULL DEFAULT 0,
  `daily_points` int NOT NULL DEFAULT 0 COMMENT '当日获得积分',
  `weekly_points` int NOT NULL DEFAULT 0 COMMENT '本周获得积分',
  `monthly_points` int NOT NULL DEFAULT 0 COMMENT '本月获得积分',
  `updated_at` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_user_points_user_id`(`user_id`) USING BTREE,
  CONSTRAINT `fk_user_points_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`Id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for userroles
-- ----------------------------
DROP TABLE IF EXISTS `userroles`;
CREATE TABLE `userroles`  (
  `UserId` int NOT NULL COMMENT '用户ID',
  `RoleId` int NOT NULL COMMENT '角色ID',
  `CreatedAt` datetime(0) NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`UserId`, `RoleId`) USING BTREE,
  INDEX `IX_UserRoles_RoleId`(`RoleId`) USING BTREE,
  CONSTRAINT `FK_UserRoles_Roles_RoleId` FOREIGN KEY (`RoleId`) REFERENCES `roles` (`Id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `FK_UserRoles_Users_UserId` FOREIGN KEY (`UserId`) REFERENCES `users` (`Id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户角色关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for users
-- ----------------------------
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `Username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户名',
  `PasswordHash` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '密码哈希',
  `SecurityStamp` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '安全戳',
  `Name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '姓名',
  `Email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '邮箱',
  `Mobile` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '手机号',
  `DepartmentId` int NULL DEFAULT NULL COMMENT '部门ID',
  `Position` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '职位',
  `Gender` int NOT NULL DEFAULT 0 COMMENT '性别：0未知，1男，2女',
  `DefaultRoleId` int NULL DEFAULT NULL COMMENT '默认角色ID',
  `IsActive` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否激活',
  `CreatedAt` datetime(0) NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime(0) NOT NULL COMMENT '更新时间',
  `LastLoginAt` datetime(0) NULL DEFAULT NULL COMMENT '最后登录时间',
  `Avatar` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '头像',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE INDEX `IX_Users_Username`(`Username`) USING BTREE,
  UNIQUE INDEX `IX_Users_Email`(`Email`) USING BTREE,
  INDEX `IX_Users_DepartmentId`(`DepartmentId`) USING BTREE,
  INDEX `IX_Users_DefaultRoleId`(`DefaultRoleId`) USING BTREE,
  CONSTRAINT `FK_Users_Departments_DepartmentId` FOREIGN KEY (`DepartmentId`) REFERENCES `departments` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_Users_Roles_DefaultRoleId` FOREIGN KEY (`DefaultRoleId`) REFERENCES `roles` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 22 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Procedure structure for calculate_points
-- ----------------------------
DROP PROCEDURE IF EXISTS `calculate_points`;
delimiter ;;
CREATE PROCEDURE `calculate_points`(IN user_id INT,
    IN action_type VARCHAR(50),
    IN reference_id INT,
    IN reference_type VARCHAR(50))
BEGIN
    DECLARE points_awarded INT;
    
    -- 根据动作类型确定积分
    CASE action_type
        WHEN 'create_task' THEN SET points_awarded = 5;
        WHEN 'update_progress' THEN SET points_awarded = 3;
        WHEN 'complete_task' THEN SET points_awarded = 10;
        ELSE SET points_awarded = 1;
    END CASE;
    
    -- 记录积分历史
    INSERT INTO point_history(
        user_id,
        action_type,
        points,
        reference_id,
        reference_type,
        created_at
    ) VALUES (
        user_id,
        action_type,
        points_awarded,
        reference_id,
        reference_type,
        NOW()
    );
    
    -- 更新用户积分
    INSERT INTO user_points (user_id, points, daily_points, weekly_points, monthly_points)
    VALUES (user_id, points_awarded, points_awarded, points_awarded, points_awarded)
    ON DUPLICATE KEY UPDATE
        points = points + points_awarded,
        daily_points = daily_points + points_awarded,
        weekly_points = weekly_points + points_awarded,
        monthly_points = monthly_points + points_awarded;
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for generate_asset_code
-- ----------------------------
DROP PROCEDURE IF EXISTS `generate_asset_code`;
delimiter ;;
CREATE PROCEDURE `generate_asset_code`(IN asset_type_code VARCHAR(10),
    OUT asset_code VARCHAR(30))
BEGIN
    DECLARE date_part VARCHAR(10);
    DECLARE seq_num INT;
    
    -- 当前日期部分 (年月日)
    SET date_part = DATE_FORMAT(NOW(), '%Y%m%d');
    
    -- 获取当天最大序号并+1
    SELECT IFNULL(MAX(SUBSTRING_INDEX(assetCode, '-', -1)), 0) + 1 
    INTO seq_num
    FROM assets 
    WHERE assetCode LIKE CONCAT('IT-', asset_type_code, '-', date_part, '-%');
    
    -- 格式化资产编号
    SET asset_code = CONCAT('IT-', asset_type_code, '-', date_part, '-', LPAD(seq_num, 3, '0'));
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for generate_daily_leaderboard
-- ----------------------------
DROP PROCEDURE IF EXISTS `generate_daily_leaderboard`;
delimiter ;;
CREATE PROCEDURE `generate_daily_leaderboard`()
BEGIN
    -- 清除今日排行榜数据
    DELETE FROM point_leaderboard 
    WHERE leaderboard_date = CURDATE() AND leaderboard_type = 'daily';
    
    -- 插入最新排行榜，使用ranking而非rank关键字
    INSERT INTO point_leaderboard (
        user_id,
        points,
        ranking,
        leaderboard_date,
        leaderboard_type,
        created_at
    )
    SELECT 
        user_id,
        daily_points as points,
        RANK() OVER (ORDER BY daily_points DESC) as ranking,
        CURDATE() as leaderboard_date,
        'daily' as leaderboard_type,
        NOW() as created_at
    FROM user_points
    WHERE daily_points > 0
    ORDER BY daily_points DESC;
    
    -- 重置每日积分
    UPDATE user_points SET daily_points = 0;
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for inventory_to_asset
-- ----------------------------
DROP PROCEDURE IF EXISTS `inventory_to_asset`;
delimiter ;;
CREATE PROCEDURE `inventory_to_asset`(IN inventory_id INT,
    IN location_id INT,
    IN user_id INT,
    OUT asset_id INT)
BEGIN
    DECLARE asset_type_code VARCHAR(10);
    DECLARE asset_code VARCHAR(30);
    
    -- 事务开始
    START TRANSACTION;
    
    -- 获取物料类型代码
    SELECT code INTO asset_type_code 
    FROM assettypes at 
    JOIN purchaseitems pi ON at.id = pi.assetTypeId
    WHERE pi.id = inventory_id;
    
    -- 生成资产编号
    CALL generate_asset_code(asset_type_code, asset_code);
    
    -- 创建资产记录
    INSERT INTO assets(
        assetCode,
        name,
        assetTypeId,
        model,
        brand,
        purchaseDate,
        locationId,
        status,
        inventoryId,
        createdAt,
        updatedAt
    )
    SELECT 
        asset_code,
        pi.name,
        pi.assetTypeId,
        pi.model,
        pi.brand,
        po.orderDate,
        location_id,
        1, -- 使用中状态
        inventory_id,
        NOW(),
        NOW()
    FROM purchaseitems pi
    JOIN purchaseorders po ON pi.purchaseOrderId = po.id
    WHERE pi.id = inventory_id;
    
    -- 更新库存状态
    UPDATE purchaseitems 
    SET status = 2 -- 已出库
    WHERE id = inventory_id;
    
    -- 获取新创建的资产ID
    SELECT LAST_INSERT_ID() INTO asset_id;
    
    -- 提交事务
    COMMIT;
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for _cleanup_procedures
-- ----------------------------
DROP PROCEDURE IF EXISTS `_cleanup_procedures`;
delimiter ;;
CREATE PROCEDURE `_cleanup_procedures`()
BEGIN
    -- 不再尝试删除其他存储过程
    SELECT '请手动清理存储过程' AS 'Cleanup Instruction';
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for _execute_main_script
-- ----------------------------
DROP PROCEDURE IF EXISTS `_execute_main_script`;
delimiter ;;
CREATE PROCEDURE `_execute_main_script`()
BEGIN
    -- 使用错误处理器捕获SQL异常
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        SET @execution_success = 0;
        GET DIAGNOSTICS CONDITION 1 @error_message = MESSAGE_TEXT;
        
        CALL `_log_execution`('错误捕获', '失败', @error_message);
        CALL `_rollback_changes`();
    END;
    
    -- ===================================================
    -- 开始创建表
    -- ===================================================
    CALL `_log_execution`('创建表', '开始', '开始创建新表');
    
    -- 1. 任务历史表 (task_history)
    CREATE TABLE IF NOT EXISTS `task_history` (
      `id` int NOT NULL AUTO_INCREMENT,
      `task_id` int NOT NULL,
      `user_id` int NOT NULL,
      `action` varchar(255) NOT NULL COMMENT '记录完整操作语义',
      `snapshot` json DEFAULT NULL COMMENT '操作时的任务数据快照',
      `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
      PRIMARY KEY (`id`),
      KEY `idx_task_history_task_id` (`task_id`),
      KEY `idx_task_history_user_id` (`user_id`),
      CONSTRAINT `fk_task_history_task` FOREIGN KEY (`task_id`) REFERENCES `tasks` (`id`) ON DELETE CASCADE,
      CONSTRAINT `fk_task_history_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
    CALL `_log_execution`('创建表', '成功', 'task_history');
    
    -- 2. 用户积分表 (user_points)
    CREATE TABLE IF NOT EXISTS `user_points` (
      `id` int NOT NULL AUTO_INCREMENT,
      `user_id` int NOT NULL,
      `points` int NOT NULL DEFAULT '0',
      `daily_points` int NOT NULL DEFAULT '0' COMMENT '当日获得积分',
      `weekly_points` int NOT NULL DEFAULT '0' COMMENT '本周获得积分',
      `monthly_points` int NOT NULL DEFAULT '0' COMMENT '本月获得积分',
      `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      PRIMARY KEY (`id`),
      UNIQUE KEY `idx_user_points_user_id` (`user_id`),
      CONSTRAINT `fk_user_points_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
    CALL `_log_execution`('创建表', '成功', 'user_points');
    
    -- 3. 积分历史表 (point_history)
    CREATE TABLE IF NOT EXISTS `point_history` (
      `id` int NOT NULL AUTO_INCREMENT,
      `user_id` int NOT NULL,
      `action_type` varchar(50) NOT NULL COMMENT '如：新建任务/更新进度/完成任务',
      `points` int NOT NULL COMMENT '获得或扣除的积分',
      `reference_id` int DEFAULT NULL COMMENT '关联ID(如任务ID)',
      `reference_type` varchar(50) DEFAULT NULL COMMENT '关联类型(如task)',
      `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
      PRIMARY KEY (`id`),
      KEY `idx_point_history_user_id` (`user_id`),
      KEY `idx_point_history_created_at` (`created_at`),
      CONSTRAINT `fk_point_history_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
    CALL `_log_execution`('创建表', '成功', 'point_history');
    
    -- 4. 积分排行榜表 (point_leaderboard)
    CREATE TABLE IF NOT EXISTS `point_leaderboard` (
      `id` int NOT NULL AUTO_INCREMENT,
      `user_id` int NOT NULL,
      `points` int NOT NULL DEFAULT '0',
      `rank` int NOT NULL,
      `leaderboard_date` date NOT NULL,
      `leaderboard_type` enum('daily','weekly','monthly','total') NOT NULL,
      `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
      PRIMARY KEY (`id`),
      UNIQUE KEY `idx_leaderboard_unique` (`user_id`,`leaderboard_date`,`leaderboard_type`),
      KEY `idx_leaderboard_date_type` (`leaderboard_date`,`leaderboard_type`),
      CONSTRAINT `fk_point_leaderboard_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
    CALL `_log_execution`('创建表', '成功', 'point_leaderboard');
    
    -- 5. 系统通知表 (notifications)
    CREATE TABLE IF NOT EXISTS `notifications` (
      `id` int NOT NULL AUTO_INCREMENT,
      `user_id` int NOT NULL,
      `title` varchar(255) NOT NULL,
      `content` text NOT NULL,
      `type` varchar(50) NOT NULL COMMENT '通知类型：task/system/alert',
      `reference_id` int DEFAULT NULL COMMENT '关联ID',
      `reference_type` varchar(50) DEFAULT NULL COMMENT '关联类型',
      `is_read` tinyint(1) NOT NULL DEFAULT '0',
      `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
      `read_at` datetime DEFAULT NULL,
      PRIMARY KEY (`id`),
      KEY `idx_notifications_user_id` (`user_id`),
      KEY `idx_notifications_created_at` (`created_at`),
      CONSTRAINT `fk_notifications_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
    CALL `_log_execution`('创建表', '成功', 'notifications');
    
    -- 6. 中控台配置表 (dashboard_config)
    CREATE TABLE IF NOT EXISTS `dashboard_config` (
      `id` int NOT NULL AUTO_INCREMENT,
      `user_id` int NOT NULL,
      `theme` enum('light','dark') NOT NULL DEFAULT 'dark',
      `layout` json DEFAULT NULL COMMENT '面板布局配置',
      `auto_refresh` int DEFAULT '60' COMMENT '刷新间隔(秒)',
      `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      PRIMARY KEY (`id`),
      KEY `idx_dashboard_user` (`user_id`),
      CONSTRAINT `fk_dashboard_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
    CALL `_log_execution`('创建表', '成功', 'dashboard_config');
    
    -- 7. 晨间战报表 (daily_reports)
    CREATE TABLE IF NOT EXISTS `daily_reports` (
      `id` int NOT NULL AUTO_INCREMENT,
      `report_date` date NOT NULL,
      `report_type` enum('daily','weekly','monthly') NOT NULL DEFAULT 'daily',
      `file_path` varchar(255) DEFAULT NULL COMMENT '报表文件路径',
      `snapshot_data` json DEFAULT NULL COMMENT '数据快照',
      `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
      PRIMARY KEY (`id`),
      UNIQUE KEY `idx_report_date_type` (`report_date`,`report_type`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
    CALL `_log_execution`('创建表', '成功', 'daily_reports');
    
    -- 8. 用户行为日志表 (user_actions)
    CREATE TABLE IF NOT EXISTS `user_actions` (
      `id` int NOT NULL AUTO_INCREMENT,
      `user_id` int NOT NULL,
      `action` varchar(50) NOT NULL COMMENT '行为类型',
      `reference_id` int DEFAULT NULL COMMENT '关联ID',
      `reference_type` varchar(50) DEFAULT NULL COMMENT '关联类型',
      `points` int DEFAULT '0' COMMENT '获得积分',
      `action_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
      PRIMARY KEY (`id`),
      KEY `idx_user_actions_user` (`user_id`),
      KEY `idx_user_actions_time` (`action_time`),
      CONSTRAINT `fk_user_actions_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
    CALL `_log_execution`('创建表', '成功', 'user_actions');
    
    -- 9. 库存安全线配置表 (inventory_thresholds)
    CREATE TABLE IF NOT EXISTS `inventory_thresholds` (
      `id` int NOT NULL AUTO_INCREMENT,
      `asset_type_id` int NOT NULL,
      `min_threshold` int NOT NULL DEFAULT '5' COMMENT '最小安全库存',
      `warning_threshold` int NOT NULL DEFAULT '10' COMMENT '预警库存',
      `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      PRIMARY KEY (`id`),
      UNIQUE KEY `idx_inventory_asset_type` (`asset_type_id`),
      CONSTRAINT `fk_inventory_asset_type` FOREIGN KEY (`asset_type_id`) REFERENCES `assettypes` (`id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
    CALL `_log_execution`('创建表', '成功', 'inventory_thresholds');
    
    CALL `_log_execution`('创建表', '完成', '所有新表创建完成');
    
    -- ===================================================
    -- 修改现有表
    -- ===================================================
    CALL `_log_execution`('修改表', '开始', '开始修改现有表结构');
    
    -- 1. 资产表 (assets) 增强
    SET @column_exists = (
      SELECT COUNT(1) FROM information_schema.columns 
      WHERE table_schema = DATABASE() 
      AND table_name = 'assets' 
      AND column_name = 'asset_code_prefix'
    );

    SET @alter_sql = IF(@column_exists = 0, 
      'ALTER TABLE `assets` ADD COLUMN `asset_code_prefix` varchar(20) DEFAULT "IT" COMMENT "资产编号前缀"', 
      'SELECT "Column already exists"'
    );

    PREPARE stmt FROM @alter_sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    
    -- 添加inventory_id列
    SET @column_exists = (
      SELECT COUNT(1) FROM information_schema.columns 
      WHERE table_schema = DATABASE() 
      AND table_name = 'assets' 
      AND column_name = 'inventory_id'
    );

    SET @alter_sql = IF(@column_exists = 0, 
      'ALTER TABLE `assets` ADD COLUMN `inventory_id` int DEFAULT NULL COMMENT "来源库存ID"', 
      'SELECT "Column already exists"'
    );

    PREPARE stmt FROM @alter_sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    
    -- 修改assetCode列
    SET @alter_assets = CONCAT('ALTER TABLE `assets` MODIFY COLUMN `assetCode` varchar(30) NOT NULL COMMENT ', 
                             "'资产编号(IT-类型-年月日-序号)'");
    PREPARE stmt FROM @alter_assets;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    
    CALL `_log_execution`('修改表', '成功', 'assets');
    
    -- 2. 位置表 (locations) 增强
    SET @column_exists = (
      SELECT COUNT(1) FROM information_schema.columns 
      WHERE table_schema = DATABASE() 
      AND table_name = 'locations' 
      AND column_name = 'level'
    );

    SET @alter_sql = IF(@column_exists = 0, 
      'ALTER TABLE `locations` ADD COLUMN `level` tinyint NOT NULL DEFAULT 3 COMMENT "位置级别(1-5)"', 
      'SELECT "Column already exists"'
    );

    PREPARE stmt FROM @alter_sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    
    -- 添加位置表级别索引(如果不存在)
    SET @exist_idx = (
      SELECT COUNT(1) FROM information_schema.statistics 
      WHERE table_schema = DATABASE() 
      AND table_name = 'locations' 
      AND index_name = 'idx_locations_level'
    );
    
    SET @sql_add_idx = IF(@exist_idx = 0, 
      'ALTER TABLE `locations` ADD INDEX `idx_locations_level` (`level`)', 
      'SELECT "Index already exists"'
    );
    
    PREPARE stmt FROM @sql_add_idx;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    
    CALL `_log_execution`('修改表', '成功', 'locations');
    
    -- 3. 采购订单表 (purchaseorders) 增强
    SET @alter_po = CONCAT('ALTER TABLE `purchaseorders` MODIFY COLUMN `orderNumber` varchar(30) NOT NULL COMMENT ', 
                           "'采购单号(PO-年月日-时分秒)'");
    PREPARE stmt FROM @alter_po;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    
    CALL `_log_execution`('修改表', '成功', 'purchaseorders');
    
    -- 4. 故障记录表 (faultrecords) 增强
    SET @column_exists = (
      SELECT COUNT(1) FROM information_schema.columns 
      WHERE table_schema = DATABASE() 
      AND table_name = 'faultrecords' 
      AND column_name = 'fault_no'
    );

    SET @alter_sql = IF(@column_exists = 0, 
      'ALTER TABLE `faultrecords` ADD COLUMN `fault_no` varchar(30) NOT NULL COMMENT "故障单号(FIX-年月日-序号)"', 
      'SELECT "Column already exists"'
    );

    PREPARE stmt FROM @alter_sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    
    SET @column_exists = (
      SELECT COUNT(1) FROM information_schema.columns 
      WHERE table_schema = DATABASE() 
      AND table_name = 'faultrecords' 
      AND column_name = 'expected_return_date'
    );

    SET @alter_sql = IF(@column_exists = 0, 
      'ALTER TABLE `faultrecords` ADD COLUMN `expected_return_date` datetime DEFAULT NULL COMMENT "预期返厂日期"', 
      'SELECT "Column already exists"'
    );

    PREPARE stmt FROM @alter_sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    
    SET @column_exists = (
      SELECT COUNT(1) FROM information_schema.columns 
      WHERE table_schema = DATABASE() 
      AND table_name = 'faultrecords' 
      AND column_name = 'warning_flag'
    );

    SET @alter_sql = IF(@column_exists = 0, 
      'ALTER TABLE `faultrecords` ADD COLUMN `warning_flag` tinyint(1) DEFAULT 0 COMMENT "超期预警标记"', 
      'SELECT "Column already exists"'
    );

    PREPARE stmt FROM @alter_sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    
    -- 添加故障单号索引(如果不存在)
    SET @exist_idx = (
      SELECT COUNT(1) FROM information_schema.statistics 
      WHERE table_schema = DATABASE() 
      AND table_name = 'faultrecords' 
      AND index_name = 'idx_faultrecords_fault_no'
    );
    
    SET @sql_add_idx = IF(@exist_idx = 0, 
      'ALTER TABLE `faultrecords` ADD INDEX `idx_faultrecords_fault_no` (`fault_no`)', 
      'SELECT "Index already exists"'
    );
    
    PREPARE stmt FROM @sql_add_idx;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    
    CALL `_log_execution`('修改表', '成功', 'faultrecords');
    
    -- 5. 任务表 (tasks) 增强
    SET @column_exists = (
      SELECT COUNT(1) FROM information_schema.columns 
      WHERE table_schema = DATABASE() 
      AND table_name = 'tasks' 
      AND column_name = 'progress'
    );

    SET @alter_sql = IF(@column_exists = 0, 
      'ALTER TABLE `tasks` ADD COLUMN `progress` int DEFAULT 0 COMMENT "进度百分比(0-100)"', 
      'SELECT "Column already exists"'
    );

    PREPARE stmt FROM @alter_sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    
    SET @column_exists = (
      SELECT COUNT(1) FROM information_schema.columns 
      WHERE table_schema = DATABASE() 
      AND table_name = 'tasks' 
      AND column_name = 'points'
    );

    SET @alter_sql = IF(@column_exists = 0, 
      'ALTER TABLE `tasks` ADD COLUMN `points` int DEFAULT 5 COMMENT "任务积分值"', 
      'SELECT "Column already exists"'
    );

    PREPARE stmt FROM @alter_sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    
    SET @column_exists = (
      SELECT COUNT(1) FROM information_schema.columns 
      WHERE table_schema = DATABASE() 
      AND table_name = 'tasks' 
      AND column_name = 'claimed_by_id'
    );

    SET @alter_sql = IF(@column_exists = 0, 
      'ALTER TABLE `tasks` ADD COLUMN `claimed_by_id` int DEFAULT NULL COMMENT "认领人ID"', 
      'SELECT "Column already exists"'
    );

    PREPARE stmt FROM @alter_sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    
    -- 添加任务认领者外键(如果不存在)
    SET @exist_fk = (
      SELECT COUNT(1) FROM information_schema.TABLE_CONSTRAINTS
      WHERE table_schema = DATABASE()
      AND table_name = 'tasks'
      AND constraint_name = 'fk_task_claimed_by'
    );
    
    SET @sql_add_fk = IF(@exist_fk = 0, 
      'ALTER TABLE `tasks` ADD CONSTRAINT `fk_task_claimed_by` FOREIGN KEY (`claimed_by_id`) REFERENCES `users` (`id`)', 
      'SELECT "Foreign key already exists"'
    );
    
    PREPARE stmt FROM @sql_add_fk;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    
    CALL `_log_execution`('修改表', '成功', 'tasks');
    CALL `_log_execution`('修改表', '完成', '所有表修改完成');
    
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for _log_execution
-- ----------------------------
DROP PROCEDURE IF EXISTS `_log_execution`;
delimiter ;;
CREATE PROCEDURE `_log_execution`(IN step_name VARCHAR(100),
    IN step_status VARCHAR(20),
    IN step_message TEXT)
BEGIN
    INSERT INTO `_script_execution_log` (step, status, message)
    VALUES (step_name, step_status, step_message);
    
    -- 同时输出到控制台
    SELECT CONCAT(step_name, ': ', step_status, 
           IF(step_message IS NULL, '', CONCAT(' - ', step_message))) AS 'Log';
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for _rollback_changes
-- ----------------------------
DROP PROCEDURE IF EXISTS `_rollback_changes`;
delimiter ;;
CREATE PROCEDURE `_rollback_changes`()
BEGIN
    -- 记录开始回滚
    CALL `_log_execution`('回滚操作', '开始', '执行失败,开始回滚创建的对象');
    
    -- 删除事件
    DROP EVENT IF EXISTS `check_inventory_warning`;
    CALL `_log_execution`('删除事件', '完成', 'check_inventory_warning');
    
    -- 删除触发器(按依赖顺序)
    DROP TRIGGER IF EXISTS `record_task_history`;
    DROP TRIGGER IF EXISTS `format_purchase_order_number`;
    DROP TRIGGER IF EXISTS `generate_fault_number`;
    -- DROP TRIGGER IF EXISTS `check_asset_location_level`;
    CALL `_log_execution`('删除触发器', '完成', 'All triggers');
    
    -- 删除表(按依赖关系倒序)
    DROP TABLE IF EXISTS `inventory_thresholds`;
    DROP TABLE IF EXISTS `user_actions`;
    DROP TABLE IF EXISTS `daily_reports`;
    DROP TABLE IF EXISTS `dashboard_config`;
    DROP TABLE IF EXISTS `notifications`;
    DROP TABLE IF EXISTS `point_leaderboard`;
    DROP TABLE IF EXISTS `point_history`;
    DROP TABLE IF EXISTS `user_points`;
    DROP TABLE IF EXISTS `task_history`;
    CALL `_log_execution`('删除表', '完成', 'All new tables');
    
    CALL `_log_execution`('回滚操作', '完成', '所有对象已回滚(存储过程需手动删除)');
END
;;
delimiter ;
-- 触发器限制：

-- DROP TRIGGER IF EXISTS check_asset_location_level;
-- ----------------------------
-- Triggers structure for table assets
-- ----------------------------
-- DROP TRIGGER IF EXISTS `check_asset_location_level`;
-- delimiter ;;
-- CREATE TRIGGER `check_asset_location_level` BEFORE INSERT ON `assets` FOR EACH ROW BEGIN
--     DECLARE loc_level INT;
    
--     -- 获取位置级别
--     SELECT level INTO loc_level FROM locations WHERE id = NEW.locationId;
    
--     -- 确保资产只能关联到第5级位置
--     IF loc_level != 5 THEN
--         SIGNAL SQLSTATE '45000' 
--         SET MESSAGE_TEXT = 'Assets must be associated with level 5 locations only';
--     END IF;
-- END
-- ;;
-- delimiter ;

-- SET FOREIGN_KEY_CHECKS = 1;
-- 任务责任人关联表
CREATE TABLE `task_assignees` (
  `id` int NOT NULL AUTO_INCREMENT,
  `task_id` int NOT NULL,
  `user_id` int NOT NULL,
  `role_type` tinyint NOT NULL DEFAULT 0 COMMENT '角色类型：0-责任人 1-协助人',
  `assigned_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `assigned_by` int NOT NULL COMMENT '分配人ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_task_user` (`task_id`, `user_id`),
  KEY `idx_task_assignees_task` (`task_id`),
  KEY `idx_task_assignees_user` (`user_id`),
  CONSTRAINT `fk_task_assignees_task` FOREIGN KEY (`task_id`) REFERENCES `tasks` (`Id`) ON DELETE CASCADE,
  CONSTRAINT `fk_task_assignees_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`Id`),
  CONSTRAINT `fk_task_assignees_assigner` FOREIGN KEY (`assigned_by`) REFERENCES `users` (`Id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='任务责任人关联表';

-- 修改tasks表，删除AssignedToId字段
ALTER TABLE `tasks` DROP FOREIGN KEY `FK_Tasks_Users_AssignedToId`;
ALTER TABLE `tasks` DROP COLUMN `AssignedToId`;


-- 任务依赖关系表
CREATE TABLE `task_dependencies` (
  `id` int NOT NULL AUTO_INCREMENT,
  `task_id` int NOT NULL COMMENT '任务ID',
  `dependent_task_id` int NOT NULL COMMENT '依赖的任务ID',
  `dependency_type` tinyint NOT NULL DEFAULT 0 COMMENT '依赖类型：0-必须完成 1-可选完成',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_task_dependency` (`task_id`, `dependent_task_id`),
  CONSTRAINT `fk_task_dependencies_task` FOREIGN KEY (`task_id`) REFERENCES `tasks` (`Id`) ON DELETE CASCADE,
  CONSTRAINT `fk_task_dependencies_dependent` FOREIGN KEY (`dependent_task_id`) REFERENCES `tasks` (`Id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='任务依赖关系表';

-- 任务标签表
CREATE TABLE `task_tags` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL COMMENT '标签名称',
  `color` varchar(20) NULL COMMENT '标签颜色',
  `description` varchar(200) NULL COMMENT '标签描述',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` int NOT NULL COMMENT '创建人ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_tag_name` (`name`),
  CONSTRAINT `fk_task_tags_creator` FOREIGN KEY (`created_by`) REFERENCES `users` (`Id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='任务标签表';

-- 任务-标签关联表
CREATE TABLE `task_tag_relations` (
  `task_id` int NOT NULL,
  `tag_id` int NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`task_id`, `tag_id`),
  CONSTRAINT `fk_task_tag_relations_task` FOREIGN KEY (`task_id`) REFERENCES `tasks` (`Id`) ON DELETE CASCADE,
  CONSTRAINT `fk_task_tag_relations_tag` FOREIGN KEY (`tag_id`) REFERENCES `task_tags` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='任务-标签关联表';