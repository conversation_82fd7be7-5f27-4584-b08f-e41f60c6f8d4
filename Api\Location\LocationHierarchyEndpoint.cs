// IT资产管理系统 - 位置层级API端点
// 文件路径: /Api/Location/LocationHierarchyEndpoint.cs
// 功能: 提供位置层级结构管理的API端点

using System;
using System.Threading.Tasks;
using ItAssetsSystem.Core.Location;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace ItAssetsSystem.Api.Location
{
    /// <summary>
    /// 位置层级API端点
    /// </summary>
    [ApiController]
    [Route("api/location-hierarchy")]
    public class LocationHierarchyEndpoint
    {
        private readonly ILocationHierarchyService _locationHierarchyService;
        private readonly ILogger<LocationHierarchyEndpoint> _logger;

        /// <summary>
        /// 构造函数
        /// </summary>
        public LocationHierarchyEndpoint(
            ILocationHierarchyService locationHierarchyService,
            ILogger<LocationHierarchyEndpoint> logger)
        {
            _locationHierarchyService = locationHierarchyService;
            _logger = logger;
        }

        /// <summary>
        /// 获取位置层级配置
        /// </summary>
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> GetHierarchyConfigAsync()
        {
            _logger.LogInformation("获取位置层级配置");
            
            try
            {
                var config = await _locationHierarchyService.GetHierarchyConfigAsync();
                return new OkObjectResult(config);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取位置层级配置失败");
                return new StatusCodeResult(StatusCodes.Status500InternalServerError);
            }
        }

        /// <summary>
        /// 保存位置层级配置
        /// </summary>
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<IActionResult> SaveHierarchyConfigAsync([FromBody] LocationHierarchyConfig config)
        {
            if (config == null)
            {
                return new BadRequestResult();
            }
            
            _logger.LogInformation("保存位置层级配置");
            
            try
            {
                bool success = await _locationHierarchyService.SaveHierarchyConfigAsync(config);
                
                if (success)
                {
                    return new OkResult();
                }
                else
                {
                    return new StatusCodeResult(StatusCodes.Status500InternalServerError);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存位置层级配置失败");
                return new StatusCodeResult(StatusCodes.Status500InternalServerError);
            }
        }

        /// <summary>
        /// 同步位置层级结构到数据库
        /// </summary>
        [HttpPost("sync")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> SyncHierarchyToDbAsync()
        {
            _logger.LogInformation("同步位置层级结构到数据库");
            
            try
            {
                bool success = await _locationHierarchyService.SyncHierarchyToDbAsync();
                
                if (success)
                {
                    return new OkResult();
                }
                else
                {
                    return new StatusCodeResult(StatusCodes.Status500InternalServerError);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "同步位置层级结构到数据库失败");
                return new StatusCodeResult(StatusCodes.Status500InternalServerError);
            }
        }

        /// <summary>
        /// 从数据库重建位置层级结构
        /// </summary>
        [HttpPost("rebuild")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> RebuildHierarchyFromDbAsync()
        {
            _logger.LogInformation("从数据库重建位置层级结构");
            
            try
            {
                bool success = await _locationHierarchyService.RebuildHierarchyFromDbAsync();
                
                if (success)
                {
                    return new OkResult();
                }
                else
                {
                    return new StatusCodeResult(StatusCodes.Status500InternalServerError);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "从数据库重建位置层级结构失败");
                return new StatusCodeResult(StatusCodes.Status500InternalServerError);
            }
        }
    }
} 