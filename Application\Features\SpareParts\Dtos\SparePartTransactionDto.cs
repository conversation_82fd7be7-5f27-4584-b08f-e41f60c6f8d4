// File: Application/Features/SpareParts/Dtos/SparePartTransactionDto.cs
// Description: 备品备件出入库记录数据传输对象

using System;

namespace ItAssetsSystem.Application.Features.SpareParts.Dtos
{
    /// <summary>
    /// 备品备件出入库记录数据传输对象
    /// </summary>
    public class SparePartTransactionDto
    {
        /// <summary>
        /// 记录ID
        /// </summary>
        public long Id { get; set; }
        
        /// <summary>
        /// 备件ID
        /// </summary>
        public long PartId { get; set; }
        
        /// <summary>
        /// 备件编号
        /// </summary>
        public string PartCode { get; set; }
        
        /// <summary>
        /// 备件名称
        /// </summary>
        public string PartName { get; set; }
        
        /// <summary>
        /// 库位ID
        /// </summary>
        public long LocationId { get; set; }
        
        /// <summary>
        /// 库位名称
        /// </summary>
        public string LocationName { get; set; }
        
        /// <summary>
        /// 库位区域
        /// </summary>
        public string LocationArea { get; set; }
        
        /// <summary>
        /// 操作类型: 1=入库, 2=出库
        /// </summary>
        public byte Type { get; set; }
        
        /// <summary>
        /// 操作类型名称
        /// </summary>
        public string TypeName { get; set; }
        
        /// <summary>
        /// 操作数量
        /// </summary>
        public int Quantity { get; set; }
        
        /// <summary>
        /// 操作后库存
        /// </summary>
        public int StockAfter { get; set; }
        
        /// <summary>
        /// 原因类型: 1=采购入库, 2=退回入库, 3=领用出库, 4=报废出库, 5=盘点调整
        /// </summary>
        public byte ReasonType { get; set; }
        
        /// <summary>
        /// 原因类型名称
        /// </summary>
        public string ReasonTypeName { get; set; }
        
        /// <summary>
        /// 关联单号
        /// </summary>
        public string ReferenceNumber { get; set; }
        
        /// <summary>
        /// 操作人ID
        /// </summary>
        public int OperatorUserId { get; set; }
        
        /// <summary>
        /// 操作人姓名
        /// </summary>
        public string OperatorName { get; set; }
        
        /// <summary>
        /// 操作时间
        /// </summary>
        public DateTime OperationTime { get; set; }
        
        /// <summary>
        /// 备注
        /// </summary>
        public string Remarks { get; set; }
        
        /// <summary>
        /// 记录批次号
        /// </summary>
        public string BatchNumber { get; set; }
        
        /// <summary>
        /// 是否由系统自动生成
        /// </summary>
        public bool IsSystemGenerated { get; set; }
    }
} 