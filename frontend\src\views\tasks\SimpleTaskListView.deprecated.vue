<!--
  本文件已废弃（deprecated），请使用 EnhancedTaskListView.vue 替代。
  如需恢复请联系前端负责人。
-->
// File: frontend/src/views/tasks/SimpleTaskListView.vue
// Description: 简化的任务列表视图，提供核心任务信息展示和弹窗编辑功能。

<template>
  <div class="simple-task-list page-container">
    <h2 class="page-title">任务列表</h2>

    <!-- 过滤器区域 -->
    <el-card shadow="never" class="filter-card mb-4">
      <div class="filters">
        <el-select v-model="filters.status" placeholder="状态" clearable style="width: 120px;" class="mr-2">
          <el-option label="未开始" value="Todo"></el-option>
          <el-option label="进行中" value="InProgress"></el-option>
          <el-option label="已完成" value="Completed"></el-option>
          <el-option label="已逾期" value="Overdue"></el-option>
        </el-select>
        <el-select v-model="filters.priority" placeholder="优先级" clearable style="width: 100px;" class="mr-2">
          <el-option label="高" value="High"></el-option>
          <el-option label="中" value="Medium"></el-option>
          <el-option label="低" value="Low"></el-option>
        </el-select>
        <el-select
          v-model="filters.assigneeId"
          placeholder="负责人"
          clearable
          filterable
          remote
          :remote-method="searchUsers"
          :loading="userSearchLoading"
          style="width: 130px;"
          class="mr-2"
        >
          <el-option label="全部" value=""></el-option> <!-- 保留全部选项 -->
          <el-option
            v-for="user in userOptions"
            :key="user.id"
            :label="user.name"
            :value="user.id"
          ></el-option>
        </el-select>
        <el-input
          v-model="filters.title" 
          placeholder="搜索标题..."
          clearable
          style="width: 180px;"
          class="mr-2"
          @keyup.enter="handleSearch" 
        />
        <el-button type="primary" :icon="Search" @click="handleSearch" :loading="loading">搜索</el-button> 
        <el-button :icon="Refresh" @click="resetFilters">重置</el-button>
      </div>
      <div class="actions mt-4">
         <el-button type="primary" :icon="Plus" @click="openTaskForm()">新增任务</el-button> 
      </div>
    </el-card>

    <!-- 任务表格 - 应用新样式和列 -->
    <el-card shadow="never" class="task-table-card">
       <el-table
        :data="pagedTasks" 
        style="width: 100%"
        v-loading="loading"
        row-key="taskId"
        :header-cell-style="{ background: '#f8f9fa', color: '#303133' }" 
      >
        <el-table-column label="序号" width="60" align="center">
          <template #default="scope">
            {{ (pagination.currentPage - 1) * pagination.pageSize + scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="process" label="工序" width="120">
          <template #default="scope">
            <!-- 根据任务类型映射工序 -->
            {{ getProcessByType(scope.row.taskType) }}
          </template>
        </el-table-column>
         <el-table-column prop="projectPhase" label="项目阶段" width="120">
          <template #default="scope">
            <!-- 根据关联位置或资产信息映射项目阶段 -->
            {{ getProjectPhase(scope.row) }}
          </template>
        </el-table-column>
        <el-table-column prop="name" label="问题事项" min-width="200" show-overflow-tooltip>
           <template #default="scope">
             <span class="task-title-link" @click="openTaskForm(scope.row)">{{ scope.row.name }}</span>
           </template>
        </el-table-column>
        <el-table-column label="负责人" width="120">
          <template #default="scope">
             <div v-if="scope.row.assigneeUserId" class="user-cell">
              <el-avatar 
                :size="24" 
                :src="getUserAvatarUrl(scope.row.assigneeUserId, scope.row.assigneeUserName, scope.row.assigneeAvatarUrl)" 
                class="table-avatar" 
              />
              <span>{{ scope.row.assigneeUserName || '未知用户' }}</span>
            </div>
             <span v-else>未分配</span>
          </template>
        </el-table-column>
        <el-table-column prop="planStartDate" label="开始时间" width="140" sortable>
          <template #default="scope">
            {{ formatDateTime(scope.row.planStartDate || scope.row.creationTimestamp) }}
          </template>
        </el-table-column>
        <el-table-column prop="planEndDate" label="结束时间" width="140" sortable>
           <template #default="scope">
            <span v-if="scope.row.planEndDate">{{ formatDateTime(scope.row.planEndDate) }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="进度" width="100" align="center">
          <template #default="scope">
            <el-tag 
              :type="getStatusTagType(scope.row.status)" 
              size="small" 
              effect="light" 
              round 
              :class="[getStatusClass(scope.row.status), {'is-overdue': scope.row.isOverdue}]"
            >
              {{ formatStatus(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
         <el-table-column prop="description" label="明细" width="150" show-overflow-tooltip>
           <template #default="scope">
             {{ scope.row.description || '无' }}
           </template>
         </el-table-column>
        <el-table-column label="操作" width="150" align="center" fixed="right">
          <template #default="scope">
            <el-tooltip content="编辑" placement="top">
              <el-button link type="primary" size="small" :icon="Edit" @click="openTaskForm(scope.row)"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="danger" size="small" :icon="Delete" @click="handleDelete(scope.row.taskId)"></el-button> 
            </el-tooltip>
             <el-tooltip content="查看详情" placement="top">
               <el-button link type="info" size="small" :icon="View" @click="viewTaskDetails(scope.row)"></el-button> 
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-if="pagination.total > 0"
        class="mt-4 pagination-right"
        :current-page="pagination.currentPage"
        :page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>

    <!-- 任务详情弹窗 -->
    <TaskDetailsDialog ref="taskDetailsDialogRef" /> 

  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch } from 'vue'
// 移除 useRouter, 因为详情跳转使用弹窗
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh, Plus, Edit, Delete, View, Clock } from '@element-plus/icons-vue' // 保留 Clock
// 引入外部弹窗组件
import TaskDetailsDialog from './components/TaskDetailsDialog.vue'
// 更新 API 导入路径和函数名 (假设)
import { taskApi } from '@/api/task'
import userApi from '@/api/user'
import { useUserStore, getFullAvatarUrl } from '@/stores/modules/user'

const userStore = useUserStore()

// --- 数据状态 (整合) ---
const tasks = ref([])
const loading = ref(false)
const taskDetailsDialogRef = ref(null)
const defaultAvatar = 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png'

// --- 筛选 (整合) ---
const filters = reactive({
  status: '',      // 使用小写状态 'todo', 'doing', 'done'
  priority: null,   // 使用数字 1, 2, 3
  assigneeId: null,
  title: '',       // 统一字段名
})
const userOptions = ref([])       // 用于远程搜索负责人
const userSearchLoading = ref(false)
const teamMembers = computed(() => userStore.usersList || []); // 保留 teamMembers 用于负责人下拉列表初始加载（如果需要）

// --- 分页 (整合) ---
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0,
})

// 计算属性，沿用之前的 pagedTasks (如果后端分页则直接返回 tasks.value)
const pagedTasks = computed(() => {
  return tasks.value
})

// --- 生命周期钩子 (整合) ---
onMounted(() => {
  // 可选：如果需要加载所有团队成员到筛选下拉框
  if (!userStore.usersList || userStore.usersList.length === 0) {
     userStore.getUsers(); // 异步获取
  }
  fetchTasks()
})

// --- 方法 (整合与重构) ---

// 获取任务列表 (整合)
const fetchTasks = async () => {
  loading.value = true
  try {
    const params = {
      pageIndex: pagination.currentPage,
      pageSize: pagination.pageSize,
      status: filters.status || undefined,
      priority: filters.priority || undefined,
      assigneeId: filters.assigneeId || undefined,
      search: filters.title || undefined, // 修改为后端接受的search参数名
    }
    
    // 使用taskApi获取任务列表
    const response = await taskApi.getTaskList(params)
    console.log('Task list response:', response)

    if (response && response.success) {
      // 处理分页数据
      tasks.value = response.data || []
      if (response.pagination) {
        pagination.total = response.pagination.totalCount || 0
      }
    } else {
      ElMessage.error('获取任务列表失败: ' + (response?.message || '未知错误'))
      tasks.value = []
      pagination.total = 0
    }
  } catch (error) {
    console.error('获取任务列表时发生错误:', error)
    ElMessage.error('获取任务列表失败，请检查网络连接')
    tasks.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

// 根据任务类型获取工序
const getProcessByType = (taskType) => {
  const typeMap = {
    'Normal': '来料入库',
    'Periodic': '生产加工',
    'PDCA': '质量检测',
  }
  return typeMap[taskType] || '软件配置'
}

// 根据任务关联信息获取项目阶段
const getProjectPhase = (task) => {
  if (task.assetId) {
    return '硬件组装'
  } else if (task.locationId) {
    return '现场支持'
  } else {
    return '软件配置'
  }
}

// 获取用户头像URL
const getUserAvatarUrl = (userId, userName, avatarUrl) => {
  return getFullAvatarUrl(avatarUrl)
}

// 格式化状态显示
const formatStatus = (status) => {
  const statusMap = {
    'Todo': '未开始',
    'InProgress': '进行中',
    'Completed': '已完成',
    'Overdue': '已逾期'
  }
  return statusMap[status] || status
}

// 获取状态标签类型
const getStatusTagType = (status) => {
  const typeMap = {
    'Todo': 'info',
    'InProgress': 'primary',
    'Completed': 'success',
    'Overdue': 'danger'
  }
  return typeMap[status] || 'info'
}

// 获取状态类名
const getStatusClass = (status) => {
  return `status-${status?.toLowerCase()}`
}

// 格式化日期时间
const formatDateTime = (dateStr) => {
  if (!dateStr) return '-'
  try {
    const date = new Date(dateStr)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      hour12: false // 使用24小时制
    })
  } catch (e) {
    console.error('格式化日期时间出错:', e)
    return dateStr
  }
}

// 搜索用户
const searchUsers = async (query) => {
  if (!query) {
    userOptions.value = teamMembers.value
    return
  }
  
  userSearchLoading.value = true
  try {
    // 先尝试从本地用户列表中过滤
    let matchedUsers = teamMembers.value.filter(user => 
      user.name?.includes(query) || user.username?.includes(query))
    
    // 如果本地没有足够的匹配，再请求API
    if (matchedUsers.length < 5) {
      const response = await userApi.searchUsers(query)
      if (response && response.success) {
        matchedUsers = response.data || []
      }
    }
    
    userOptions.value = matchedUsers
  } catch (error) {
    console.error('搜索用户出错:', error)
    userOptions.value = []
  } finally {
    userSearchLoading.value = false
  }
}

// 重置筛选条件
const resetFilters = () => {
  filters.status = ''
  filters.priority = null
  filters.assigneeId = null
  filters.title = ''
  
  // 重置后自动刷新列表
  fetchTasks()
}

// 搜索
const handleSearch = () => {
  pagination.currentPage = 1 // 重置到第一页
  fetchTasks()
}

// 处理分页大小变化
const handleSizeChange = (size) => {
  pagination.pageSize = size
  fetchTasks()
}

// 处理分页变化
const handleCurrentChange = (page) => {
  pagination.currentPage = page
  fetchTasks()
}

// 打开任务表单
const openTaskForm = (task = null) => {
  // 调用子组件的方法，传入要编辑的任务数据
  // taskFormDialogRef.value.open(task)
}

// 查看任务详情
const viewTaskDetails = (task) => {
  taskDetailsDialogRef.value.open(task.taskId)
}

// 删除任务
const handleDelete = (taskId) => {
  ElMessageBox.confirm('确定要删除此任务吗？此操作不可恢复。', '删除确认', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const response = await taskApi.deleteTask(taskId)
      
      if (response && response.success) {
        ElMessage.success('任务删除成功')
        fetchTasks() // 刷新任务列表
      } else {
        ElMessage.error(response?.message || '删除任务失败')
      }
    } catch (error) {
      console.error('删除任务出错:', error)
      ElMessage.error('删除任务失败，请稍后重试')
    }
  }).catch(() => {
    // 用户取消删除
  })
}
</script>

<style scoped>
.page-container {
  padding: 20px;
}

.page-title {
  font-size: 20px;
  margin-bottom: 20px;
  font-weight: 600;
  color: #303133;
}

.filter-card {
  border-radius: 4px;
  margin-bottom: 20px;
}

.filters {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.mr-2 {
  margin-right: 8px;
}

.mb-4 {
  margin-bottom: 16px;
}

.mt-4 {
  margin-top: 16px;
}

.task-table-card {
  border-radius: 4px;
}

.pagination-right {
  text-align: right;
  margin-top: 20px;
}

.task-title-link {
  color: #409eff;
  cursor: pointer;
  transition: color 0.2s;
}

.task-title-link:hover {
  color: #66b1ff;
  text-decoration: underline;
}

.user-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.table-avatar {
  flex-shrink: 0;
}

.actions {
  margin-top: 12px;
}

.status-todo {
  background-color: #f4f4f5 !important;
  color: #909399 !important;
}

.status-inprogress {
  background-color: #e6f1fc !important;
  color: #409eff !important;
}

.status-completed {
  background-color: #e7f6e7 !important;
  color: #67c23a !important;
}

.status-overdue {
  background-color: #fef0f0 !important;
  color: #f56c6c !important;
}

.is-overdue {
  background-color: #fef0f0 !important;
  color: #f56c6c !important;
  border-color: #f56c6c !important;
}
</style> 