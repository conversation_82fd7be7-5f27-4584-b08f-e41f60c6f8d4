-- =====================================================
-- 游戏化系统完整升级脚本 - 基于现有完整基础设施分析
-- 发现：系统已具备完整的游戏化基础设施，仅需极少量补充
-- =====================================================

-- 重要发现：数据库已包含完整的游戏化系统
-- - gamification_userstats: 用户统计表 (已存在)
-- - gamification_log: 事件日志表 (已存在)  
-- - task_claims: 任务领取表 (已存在)
-- - user_work_summary: 工作汇总表 (已存在)
-- - 多个排行榜视图和存储过程 (已存在)

-- 1. 优化现有索引（提升查询性能）
CREATE INDEX IF NOT EXISTS idx_task_claims_claim_date_status ON task_claims(claim_date, claim_status);
CREATE INDEX IF NOT EXISTS idx_gamification_log_timestamp_event ON gamification_log(Timestamp, EventType);
CREATE INDEX IF NOT EXISTS idx_user_work_summary_period_user ON user_work_summary(period_type, period_date, user_id);
CREATE INDEX IF NOT EXISTS idx_tasks_completed_by_user ON tasks(CompletedByUserId, Status, CompletedAt);

-- 2. 增强任务领取统计视图（基于现有task_claims表）
CREATE OR REPLACE VIEW v_task_claim_statistics AS
SELECT 
    u.Id as UserId,
    u.Name as UserName,
    COALESCE(d.Name, '未分配') as DepartmentName,
    COUNT(tc.claim_id) as TotalClaims,
    COUNT(CASE WHEN tc.claim_status = 'Completed' THEN 1 END) as CompletedClaims,
    COUNT(CASE WHEN DATE(tc.claimed_at) = CURDATE() THEN 1 END) as TodayClaims,
    COUNT(CASE WHEN tc.claimed_at >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) THEN 1 END) as WeeklyClaims,
    COUNT(CASE WHEN tc.claimed_at >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) THEN 1 END) as MonthlyClaims,
    ROUND(AVG(CASE WHEN tc.completed_at IS NOT NULL AND tc.claimed_at IS NOT NULL 
              THEN TIMESTAMPDIFF(HOUR, tc.claimed_at, tc.completed_at) END), 2) as AvgCompletionHours,
    MAX(tc.claimed_at) as LastClaimTime
FROM users u
LEFT JOIN task_claims tc ON u.Id = tc.claimed_by
LEFT JOIN departments d ON u.DepartmentId = d.Id
WHERE u.IsActive = 1
GROUP BY u.Id, u.Name, d.Name;

-- 3. 增强工作汇总统计视图（基于现有user_work_summary表）
CREATE OR REPLACE VIEW v_work_summary_enhanced AS
SELECT 
    uws.user_id,
    uws.user_name,
    uws.department_name,
    uws.period_type,
    uws.period_date,
    uws.tasks_created,
    uws.tasks_claimed,
    uws.tasks_completed,
    uws.total_points_earned,
    uws.points_rank,
    uws.productivity_rank,
    CASE 
        WHEN uws.tasks_claimed > 0 THEN ROUND((uws.tasks_completed * 100.0) / uws.tasks_claimed, 2)
        ELSE 0 
    END as completion_rate,
    CASE 
        WHEN uws.period_type = 'daily' THEN uws.total_points_earned
        WHEN uws.period_type = 'weekly' THEN ROUND(uws.total_points_earned / 7, 2)
        WHEN uws.period_type = 'monthly' THEN ROUND(uws.total_points_earned / 30, 2)
        ELSE 0
    END as daily_avg_points,
    ROW_NUMBER() OVER (PARTITION BY uws.period_type, uws.period_date ORDER BY uws.total_points_earned DESC) as overall_rank,
    CASE 
        WHEN uws.productivity_rank <= 3 THEN '🥇 超级明星'
        WHEN uws.productivity_rank <= 10 THEN '🏆 优秀员工'
        WHEN uws.productivity_rank <= 20 THEN '⭐ 努力奋斗'
        ELSE '💪 稳步提升'
    END as performance_level
FROM user_work_summary uws
WHERE uws.period_date >= DATE_SUB(CURDATE(), INTERVAL 90 DAY);

-- 4. 优化现有存储过程sp_UpdateUserGamificationStats（增加任务领取统计）
DELIMITER ;;
CREATE OR REPLACE PROCEDURE sp_UpdateTaskClaimStatistics()
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;

    START TRANSACTION;
    
    -- 更新任务领取统计（基于现有gamification_userstats表）
    UPDATE gamification_userstats gus
    INNER JOIN users u ON gus.CoreUserId = u.Id
    SET 
        gus.ClaimedTasksCount = (
            SELECT COUNT(*)
            FROM task_claims tc
            WHERE tc.claimed_by = u.Id
        ),
        gus.CompletedTasksCount = (
            SELECT COUNT(*)
            FROM tasks t
            WHERE t.CompletedByUserId = u.Id AND t.Status = 'Done'
        ),
        gus.LastUpdatedTimestamp = NOW()
    WHERE u.IsActive = 1;
    
    -- 记录到现有gamification_log表
    INSERT INTO gamification_log (UserId, EventType, Description, Timestamp)
    SELECT 
        gus.UserId,
        'CLAIM_STATS_UPDATE',
        '任务领取统计更新',
        NOW()
    FROM gamification_userstats gus
    INNER JOIN users u ON gus.CoreUserId = u.Id
    WHERE u.IsActive = 1
    LIMIT 1;
    
    COMMIT;
END;;
DELIMITER ;

-- 5. 增强现有工作汇总功能（基于现有user_work_summary表）
DELIMITER ;;
CREATE OR REPLACE PROCEDURE sp_RefreshWorkSummary(
    IN p_period_type ENUM('daily', 'weekly', 'monthly'),
    IN p_period_date DATE
)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;

    START TRANSACTION;
    
    -- 基于现有表结构更新工作汇总
    INSERT INTO user_work_summary (
        user_id, user_name, department_name, period_type, period_date,
        tasks_created, tasks_claimed, tasks_completed, tasks_commented,
        total_points_earned, total_coins_earned, total_diamonds_earned,
        created_at, updated_at
    )
    SELECT 
        u.Id,
        u.Name,
        COALESCE(d.Name, '未分配'),
        p_period_type,
        p_period_date,
        COALESCE(task_stats.created_count, 0),
        COALESCE(claim_stats.claimed_count, 0),
        COALESCE(task_stats.completed_count, 0),
        COALESCE(comment_stats.comment_count, 0),
        COALESCE(gamif_stats.points_earned, 0),
        COALESCE(gamif_stats.coins_earned, 0),
        COALESCE(gamif_stats.diamonds_earned, 0),
        NOW(),
        NOW()
    FROM users u
    LEFT JOIN departments d ON u.DepartmentId = d.Id
    LEFT JOIN (
        SELECT 
            t.CreatorUserId as user_id,
            COUNT(CASE WHEN DATE(t.CreationTimestamp) = p_period_date THEN 1 END) as created_count,
            COUNT(CASE WHEN DATE(t.CompletedAt) = p_period_date AND t.Status = 'Done' THEN 1 END) as completed_count
        FROM tasks t
        GROUP BY t.CreatorUserId
    ) task_stats ON u.Id = task_stats.user_id
    LEFT JOIN (
        SELECT 
            tc.claimed_by as user_id,
            COUNT(*) as claimed_count
        FROM task_claims tc
        WHERE tc.claim_date = p_period_date
        GROUP BY tc.claimed_by
    ) claim_stats ON u.Id = claim_stats.user_id
    LEFT JOIN (
        SELECT 
            c.UserId as user_id,
            COUNT(*) as comment_count
        FROM comments c
        WHERE DATE(c.CreationTimestamp) = p_period_date
        GROUP BY c.UserId
    ) comment_stats ON u.Id = comment_stats.user_id
    LEFT JOIN (
        SELECT 
            gus.CoreUserId as user_id,
            COALESCE(SUM(gl.PointsGained), 0) as points_earned,
            COALESCE(SUM(gl.CoinsGained), 0) as coins_earned,
            COALESCE(SUM(gl.DiamondsGained), 0) as diamonds_earned
        FROM gamification_userstats gus
        INNER JOIN gamification_log gl ON gus.UserId = gl.UserId
        WHERE DATE(gl.Timestamp) = p_period_date
        GROUP BY gus.CoreUserId
    ) gamif_stats ON u.Id = gamif_stats.user_id
    WHERE u.IsActive = 1
    ON DUPLICATE KEY UPDATE
        tasks_created = VALUES(tasks_created),
        tasks_claimed = VALUES(tasks_claimed),
        tasks_completed = VALUES(tasks_completed),
        tasks_commented = VALUES(tasks_commented),
        total_points_earned = VALUES(total_points_earned),
        total_coins_earned = VALUES(total_coins_earned),
        total_diamonds_earned = VALUES(total_diamonds_earned),
        updated_at = NOW();
    
    COMMIT;
END;;
DELIMITER ;

-- 6. 确保基础配置完整（基于现有gamification_config表）
INSERT IGNORE INTO gamification_config (ConfigKey, ConfigValue, Description, Category) VALUES
('TASK_CLAIM_DAILY_LIMIT', '50', '每日任务领取上限', 'Limits'),
('WORK_SUMMARY_AUTO_UPDATE', '1', '工作汇总自动更新开关', 'System'),
('CLAIM_STATISTICS_ENABLED', '1', '任务领取统计功能开关', 'Features'),
('PERFORMANCE_TRACKING_ENABLED', '1', '绩效跟踪功能开关', 'Features');

-- 7. 确保所有活跃用户都有完整的游戏化记录
INSERT IGNORE INTO gamification_userstats (UserId, CoreUserId, LastUpdatedTimestamp)
SELECT u.Id, u.Id, NOW()
FROM users u 
WHERE u.IsActive = 1 
AND NOT EXISTS (
    SELECT 1 FROM gamification_userstats gus WHERE gus.CoreUserId = u.Id
);

-- 8. 初始化今日工作汇总数据
CALL sp_RefreshWorkSummary('daily', CURDATE());

-- 9. 更新任务领取统计
CALL sp_UpdateTaskClaimStatistics();

-- 升级完成提示
SELECT '游戏化系统升级完成！基于现有完整基础设施进行了最小化增强。' as upgrade_status;
