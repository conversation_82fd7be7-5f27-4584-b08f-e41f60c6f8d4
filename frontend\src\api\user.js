/**
 * 用户API接口
 * 文件路径: src/api/user.js
 * 功能描述: 用户登录、获取信息等接口
 */

import request from '@/utils/request'
import { cachedRequest } from '@/utils/apiRequestManager'

/**
 * 用户登录
 * @param {string} username 用户名
 * @param {string} password 密码
 * @returns {Promise} 登录结果
 */
export function login(username, password) {
  return request.post('/User/login', {
    username,
    password
  })
}

/**
 * 获取用户信息
 * @returns {Promise} 用户信息
 */
export function getUserInfo() {
  return request.get('/User/info')
}

/**
 * 退出登录
 * @returns {Promise} 退出结果
 */
export function logout() {
  return request.post('/User/logout')
}

/**
 * 获取用户列表 (带缓存优化)
 * @param {Object} params 查询参数
 * @returns {Promise} 用户列表
 */
export function getUsers(params) {
  const cacheKey = `users-list-${JSON.stringify(params || {})}`
  return cachedRequest(
    cacheKey,
    () => request.get('/User', { params }),
    true, // 使用缓存
    5 * 60 * 1000 // 5分钟缓存
  )
}

/**
 * 获取用户详情
 * @param {number} id 用户ID
 * @returns {Promise} 用户详情
 */
export function getUserById(id) {
  return request.get(`/User/${id}`)
}

/**
 * 创建用户
 * @param {Object} data 用户数据
 * @returns {Promise} 创建结果
 */
export function createUser(data) {
  return request.post('/User', data)
}

/**
 * 更新用户
 * @param {number} id 用户ID
 * @param {Object} data 用户数据
 * @returns {Promise} 更新结果
 */
export function updateUser(id, data) {
  return request.put(`/User/${id}`, data)
}

/**
 * 删除用户
 * @param {number} id 用户ID
 * @returns {Promise} 删除结果
 */
export function deleteUser(id) {
  return request.delete(`/User/${id}`)
}

/**
 * 修改密码
 * @param {Object} data 密码数据
 * @returns {Promise} 修改结果
 */
export function changePassword(data) {
  return request.put('/User/change-password', data)
}

/**
 * 重置密码
 * @param {number} id 用户ID
 * @returns {Promise} 重置结果
 */
export function resetPassword(id) {
  return request.post(`/User/${id}/reset-password`)
}

/**
 * 航空航天级IT资产管理系统 - 用户管理API
 * 文件路径: src/api/user.js
 * 功能描述: 提供用户管理相关的API服务
 */

// 用户API对象
const userApi = {
  /**
   * 获取用户列表
   * @param {Object} params - 查询参数
   * @returns {Promise}
   */
  getUserList(params) {
    return request.get('/User', { params })
  },
  
  /**
   * 获取用户详情
   * @param {number|string} id - 用户ID
   * @returns {Promise}
   */
  getUserById(id) {
    return request.get(`/User/${id}`)
  },
  
  /**
   * 创建用户
   * @param {Object} data - 用户数据
   * @returns {Promise}
   */
  createUser(data) {
    return request.post('/User', data)
  },
  
  /**
   * 更新用户
   * @param {number|string} id - 用户ID
   * @param {Object} data - 用户数据
   * @returns {Promise}
   */
  updateUser(id, data) {
    return request.put(`/User/${id}`, data)
  },
  
  /**
   * 更新当前用户个人信息
   * @param {Object} data - 用户个人信息数据
   * @returns {Promise}
   */
  updateProfile(data) {
    return request.put('/User/profile', data)
  },
  
  /**
   * 上传用户头像
   * @param {FormData} formData - 包含图片文件的表单数据
   * @returns {Promise}
   */
  uploadAvatar(file) {
    const formData = new FormData()
    formData.append('file', file)
    return request.post('/v2/profile/avatar', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },
  
  /**
   * 删除用户
   * @param {number|string} id - 用户ID
   * @returns {Promise}
   */
  deleteUser(id) {
    return request.delete(`/User/${id}`)
  },
  
  /**
   * 重置用户密码
   * @param {number|string} id - 用户ID
   * @returns {Promise}
   */
  resetUserPassword(id) {
    return request.post(`/User/${id}/reset-password`)
  },
  
  /**
   * 修改用户密码
   * @param {number|string} id - 用户ID
   * @param {Object} data - 密码数据
   * @returns {Promise}
   */
  changeUserPassword(id, data) {
    return request.put(`/User/${id}/password`, data)
  },
  
  /**
   * 获取用户权限
   * @param {number|string} id - 用户ID
   * @returns {Promise}
   */
  getUserPermissions(id) {
    return request.get(`/User/${id}/permissions`)
  },
  
  /**
   * 更新用户权限
   * @param {number|string} id - 用户ID
   * @param {Object} data - 权限数据
   * @returns {Promise}
   */
  updateUserPermissions(id, data) {
    return request.put(`/User/${id}/permissions`, data)
  },
  
  /**
   * 获取用户角色
   * @param {number|string} id - 用户ID
   * @returns {Promise}
   */
  getUserRoles(id) {
    return request.get(`/User/${id}/roles`)
  },
  
  /**
   * 更新用户角色
   * @param {number|string} id - 用户ID
   * @param {Object} data - 角色数据
   * @returns {Promise}
   */
  updateUserRoles(id, data) {
    return request.put(`/User/${id}/roles`, data)
  },
  
  /**
   * 导出用户数据
   * @param {Object} params - 查询参数
   * @returns {Promise}
   */
  exportUsers(params) {
    return request.post('/User/export', params, {
      responseType: 'blob'
    })
  },
  
  /**
   * 批量导入用户数据
   * @param {FormData} formData - 包含Excel文件的表单数据
   * @returns {Promise}
   */
  importUsers(formData) {
    return request.post('/User/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },
  
  /**
   * 根据用户ID获取用户详情
   * @param {number} userId - 用户ID
   * @returns {Promise<object>} - 返回API响应对象
   */
  getUserDetail(userId) {
    return request.get(`/User/${userId}`)
  },
  
  /**
   * 根据多个用户ID获取用户详情
   * @param {number[]} userIds - 用户ID数组
   * @returns {Promise<object>} - 返回API响应对象
   */
  getUsersByIds(userIds) {
    return request.post(`/User/batch`, {
      userIds
    })
  },
  
  /**
   * 获取位置用户候选人
   * @param {number} userType - 用户类型 0-使用人 1-管理员
   * @param {number} [departmentId] - 可选的部门ID
   * @returns {Promise<object>} - 返回API响应对象
   */
  getLocationUserCandidates(userType, departmentId) {
    let url = `/User/location-users?userType=${userType}`
    
    if (departmentId) {
      url += `&departmentId=${departmentId}`
    }
    
    return request.get(url)
  },
  
  /**
   * 修改用户状态
   * @param {number|string} id - 用户ID
   * @param {string} status - 用户状态
   * @returns {Promise}
   */
  changeUserStatus(id, status) {
    return request.put(`/User/${id}/status`, { status })
  },
  
  /**
   * 按角色类型获取用户候选人列表
   * @param {Object} params - 查询参数
   * @param {number} params.userType - 用户类型，0-普通用户/使用人，1-管理员
   * @returns {Promise} - 用户候选人列表
   */
  getUsersByRole(params) {
    return request.get('/User/location-users', { params })
  },
  
  /**
   * 按部门获取用户列表
   * @param {Object} params - 查询参数
   * @param {number} params.departmentId - 部门ID
   * @param {string} params.keyword - 搜索关键词
   * @param {number} params.userType - 用户类型，0-普通用户/使用人，1-管理员
   * @returns {Promise} - 符合条件的用户列表
   */
  getUsersByDepartment(params) {
    return request.get('/User/department-users', { params })
  },
  
  /**
   * 修改用户状态
   * @param {number|string} id - 用户ID
   * @param {string} status - 用户状态
   * @returns {Promise}
   */
  toggleUserStatus(id, status) {
    return request.put(`/User/${id}/status`, { status })
  },
  
  /**
   * 搜索可提及的用户列表，用于任务评论中的@功能
   * @param {Object} params 搜索参数
   * @param {string} params.search 搜索关键词
   * @param {number} params.limit 返回数量限制
   * @returns {Promise} 用户列表
   */
  searchMentionUsers(params) {
    return request.get('/User/search', { params })
  },

  /**
   * 获取当前登录用户信息
   * @returns {Promise} 当前用户信息
   */
  getCurrentUser() {
    return request.get('/User/info')
  },

  /**
   * 获取当前用户详细资料
   * @returns {Promise} 当前用户详细资料
   */
  getCurrentUserProfile() {
    return request.get('/User/profile')
  }
}

export default userApi