// File: frontend/src/views/tasks/components/BatchStatusDialog.vue
// Description: 批量任务状态更新对话框组件

<template>
  <el-dialog
    v-model="dialogVisible"
    title="批量更新任务状态"
    width="500px"
    destroy-on-close
  >
    <div class="batch-status-dialog">
      <p class="dialog-info">
        您选择了 <strong>{{ taskIds.length }}</strong> 个任务，请选择要更新的状态：
      </p>
      
      <el-form :model="form" label-width="80px">
        <el-form-item label="状态">
          <el-select v-model="form.status" placeholder="选择任务状态" style="width: 100%">
            <el-option
              v-for="option in statusOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            >
              <div class="status-option">
                <el-tag :type="option.tagType" size="small" effect="light">{{ option.label }}</el-tag>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="备注">
          <el-input
            v-model="form.remarks"
            type="textarea"
            :rows="3"
            placeholder="请输入更新备注（可选）"
          />
        </el-form-item>
      </el-form>
      
      <div class="affected-tasks">
        <p>受影响的任务：</p>
        <el-scrollbar height="120px">
          <div class="task-list">
            <div v-for="id in taskIds" :key="id" class="task-item">
              <el-tag size="small" effect="plain">{{ getTaskName(id) || `任务ID: ${id}` }}</el-tag>
            </div>
          </div>
        </el-scrollbar>
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
          确认更新
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { taskApi } from '@/api/task'
import { ElMessage } from 'element-plus'
import { useTaskEnhancedStore } from '@/stores/modules/taskEnhanced'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  taskIds: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:visible', 'success'])

// 状态
const dialogVisible = ref(false)
const loading = ref(false)
const taskStore = useTaskEnhancedStore()

// 表单数据
const form = reactive({
  status: '',
  remarks: ''
})

// 状态选项
const statusOptions = [
  { value: 'Todo', label: '待办', tagType: 'info' },
  { value: 'InProgress', label: '进行中', tagType: 'warning' },
  { value: 'Done', label: '已完成', tagType: 'success' },
  { value: 'Cancelled', label: '已取消', tagType: 'danger' }
]

// 监听visible属性变化
watch(() => props.visible, (val) => {
  dialogVisible.value = val
  if (val) {
    resetForm()
  }
})

// 监听dialogVisible变化
watch(dialogVisible, (val) => {
  emit('update:visible', val)
})

// 重置表单
const resetForm = () => {
  form.status = ''
  form.remarks = ''
}

// 获取任务名称（如果有）
const getTaskName = (taskId) => {
  const task = taskStore.getTaskById(taskId)
  return task ? task.name || task.title : null
}

// 提交表单
const handleSubmit = async () => {
  if (!form.status) {
    ElMessage.warning('请选择要更新的状态')
    return
  }

  loading.value = true
  try {
    // 调用批量更新API
    const result = await taskApi.batchUpdateStatus(props.taskIds, form.status, form.remarks)
    
    if (result.success) {
      ElMessage.success('批量更新状态成功')
      emit('success')
      dialogVisible.value = false
      
      // 刷新任务列表
      await taskStore.fetchTasks()
    } else {
      ElMessage.error(result.message || '操作失败')
    }
  } catch (error) {
    console.error('批量更新状态失败', error)
    ElMessage.error(error.message || '更新状态失败，请稍后重试')
  } finally {
    loading.value = false
  }
}
</script>

<style lang="scss" scoped>
.batch-status-dialog {
  .dialog-info {
    margin-bottom: 20px;
    color: var(--el-text-color-regular);
  }
  
  .status-option {
    display: flex;
    align-items: center;
  }
  
  .affected-tasks {
    margin-top: 15px;
    border-top: 1px solid var(--el-border-color-lighter);
    padding-top: 15px;
    
    p {
      margin-bottom: 8px;
      font-size: 14px;
      color: var(--el-text-color-secondary);
    }
    
    .task-list {
      padding: 5px;
      
      .task-item {
        margin: 5px 0;
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style> 