# 智能制造监控系统 - 工厂监控看板文档

## 📊 **系统概述**

### **项目信息**
- **系统名称**：智能制造监控系统 (Factory Monitoring Dashboard)
- **版本号**：V2.0
- **开发框架**：Vue 3 + Element Plus + Vite
- **访问地址**：`http://localhost:5173/factory-monitor`
- **更新时间**：2025年06月02日

### **功能定位**
工厂监控看板是一个独立的实时态势感知系统，专门用于监控工厂145个工位的运行状态，提供全方位的生产监控和异常预警功能。

---

## 🏭 **工厂布局设计**

### **总体布局**
- **工位总数**：145个
- **布局模式**：梯形流水线布局
- **区域划分**：5个生产区域
- **画布尺寸**：1400 x 700 像素

### **区域分布**

| 区域 | 名称 | 行数 | 工位数 | 颜色标识 | 功能定位 |
|------|------|------|--------|----------|----------|
| A区 | 生产线1 | 2行 | 36个 | 蓝色 #3b82f6 | 主要生产工序 |
| B区 | 生产线2 | 2行 | 34个 | 绿色 #10b981 | 辅助生产工序 |
| C区 | 装配线 | 2行 | 32个 | 橙色 #f59e0b | 产品装配 |
| D区 | 质检线 | 1行 | 15个 | 红色 #ef4444 | 质量检测 |
| E区 | 包装线 | 2行 | 28个 | 紫色 #8b5cf6 | 成品包装 |

### **详细布局配置**

```javascript
// 工位布局配置
const layoutConfig = {
  rows: [
    { count: 18, startX: 50, y: 50, spacing: 65 },   // A区第1行
    { count: 18, startX: 50, y: 120, spacing: 65 },  // A区第2行
    { count: 17, startX: 80, y: 190, spacing: 65 },  // B区第1行
    { count: 17, startX: 80, y: 260, spacing: 65 },  // B区第2行
    { count: 16, startX: 110, y: 330, spacing: 65 }, // C区第1行
    { count: 16, startX: 110, y: 400, spacing: 65 }, // C区第2行
    { count: 15, startX: 140, y: 470, spacing: 65 }, // D区第1行
    { count: 14, startX: 170, y: 540, spacing: 65 }, // E区第1行
    { count: 14, startX: 170, y: 610, spacing: 65 }  // E区第2行
  ]
}
```

---

## 🎛️ **界面功能模块**

### **1. 顶部导航栏**
- **系统标题**：智能制造监控系统
- **实时统计**：显示总工位数 (145个)
- **更新时间**：实时显示最后更新时间
- **搜索功能**：支持工位编号、名称搜索
- **筛选功能**：
  - 部门筛选
  - 位置类型筛选
  - 仅显示有设备的位置
- **控制按钮**：
  - 刷新按钮：手动刷新数据
  - 全屏按钮：切换全屏显示

### **2. 左侧统计面板**

#### **实时概览**
```
┌─────────────────┐
│   实时概览      │
├─────────────────┤
│ ✓ 运行正常: 101 │
│ ⚠ 警告状态: 22  │
│ ✗ 错误状态: 15  │
│ ○ 空闲工位: 7   │
├─────────────────┤
│ 平均效率: 85%   │
│ 总设备数: 580   │
└─────────────────┘
```

#### **状态筛选**
- 运行正常 (绿色)
- 警告状态 (黄色)
- 错误状态 (红色)
- 空闲工位 (灰色)

#### **重点监控**
显示需要关注的故障或警告工位，最多显示5个优先级最高的工位。

### **3. 主布局视图**

#### **布局视图模式**
- **网格背景**：30x30像素细网格
- **区域标识**：虚线框标识不同生产区域
- **工位单元**：55x45像素工位卡片
- **状态颜色**：
  - 绿色：运行正常
  - 黄色：警告状态
  - 红色：错误状态 (带闪烁动画)
  - 灰色：空闲状态

#### **列表视图模式**
表格形式显示所有工位信息：
- 工位编号
- 工位名称
- 所属部门
- 运行效率
- 状态标签
- 设备数量
- 任务数量
- 操作按钮

### **4. 工位详情抽屉**

点击任意工位后，右侧弹出详情抽屉，包含三个标签页：

#### **设备资产**
```
┌──────────────────────────────┐
│ 设备名称    | 资产编号 | 状态 │
├──────────────────────────────┤
│ 数控机床-001| NC001   | 运行中│
│ 质检仪-002  | QI002   | 正常  │
│ 传送带-003  | CB003   | 维护中│
└──────────────────────────────┘
```

#### **活跃任务**
```
┌──────────────────────────────┐
│ 任务标题    | 优先级  | 负责人│
├──────────────────────────────┤
│ 日常巡检    | 高     | 张三  │
│ 设备维护    | 中     | 李四  │
│ 质量检查    | 低     | 王五  │
└──────────────────────────────┘
```

#### **故障报告**
```
┌──────────────────────────────┐
│ 故障描述    | 严重等级| 状态  │
├──────────────────────────────┤
│ 温度异常    | 警告级别| 处理中│
│ 振动超标    | 严重级别| 已修复│
└──────────────────────────────┘
```

---

## 🔄 **实时数据流**

### **数据更新机制**
- **自动刷新**：每5秒自动更新工位状态
- **手动刷新**：点击刷新按钮立即更新
- **状态变化**：工位状态随机波动模拟真实工厂环境

### **状态权重配置**
```javascript
const statusWeights = [0.7, 0.15, 0.1, 0.05]
// 70% 正常运行
// 15% 警告状态  
// 10% 故障状态
// 5%  空闲状态
```

### **模拟数据生成**
每个工位包含以下实时数据：
- **基础信息**：编号、名称、所属区域
- **运行指标**：效率(70-100%)、开机率(80-100%)
- **设备统计**：设备数量(2-6台)、任务数量(1-8个)
- **故障统计**：故障数量(0-3个)
- **更新时间**：最后更新时间戳

---

## 🎨 **视觉设计规范**

### **色彩系统**

#### **主色调**
- **深色背景**：`#0f172a` → `#1e293b` → `#334155`
- **强调色**：`#3b82f6` (蓝色)
- **文字色**：`#f1f5f9` (浅色)

#### **状态色彩**
```css
/* 运行正常 */
.status-operational {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.8), rgba(21, 128, 61, 0.9));
  border-color: rgba(34, 197, 94, 0.5);
}

/* 警告状态 */
.status-warning {
  background: linear-gradient(135deg, rgba(234, 179, 8, 0.8), rgba(161, 98, 7, 0.9));
  border-color: rgba(234, 179, 8, 0.5);
}

/* 错误状态 */
.status-error {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.8), rgba(185, 28, 28, 0.9));
  border-color: rgba(239, 68, 68, 0.5);
  animation: errorBlink 2s infinite;
}

/* 空闲状态 */
.status-idle {
  background: linear-gradient(135deg, rgba(107, 114, 128, 0.6), rgba(75, 85, 99, 0.8));
  border-color: rgba(107, 114, 128, 0.3);
}
```

#### **动画效果**
- **悬停放大**：`transform: scale(1.1)`
- **故障闪烁**：`animation: errorBlink 2s infinite`
- **平滑过渡**：`transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1)`

### **图标系统**
使用Element Plus官方图标库：
- `Check`：正常状态
- `Warning`：警告状态
- `CloseIcon`：错误状态
- `Setting`：空闲状态
- `Cpu`：系统图标

---

## 📱 **响应式设计**

### **桌面端 (≥1200px)**
- 左侧面板：300px宽度
- 主视图：自适应剩余空间
- 工位尺寸：55x45px

### **平板端 (768px-1200px)**
- 左侧面板：250px宽度
- 工位尺寸：50x40px
- 字体适当缩小

### **移动端 (<768px)**
- 垂直布局：统计面板在上，工厂布局在下
- 工位网格：4列显示
- 触摸优化：增大点击区域

---

## 🔧 **技术架构**

### **前端技术栈**
```json
{
  "framework": "Vue 3.3+",
  "ui": "Element Plus 2.4+",
  "build": "Vite 5.0+",
  "css": "SCSS + CSS3",
  "icons": "@element-plus/icons-vue",
  "router": "Vue Router 4.0+"
}
```

### **核心组件结构**
```
FactoryLayoutDashboard.vue
├── 顶部导航栏 (dashboard-header)
├── 主要内容区 (dashboard-main)
│   ├── 统计面板 (stats-panel)
│   │   ├── 实时概览卡片
│   │   ├── 筛选面板
│   │   └── 重点监控面板
│   └── 工厂布局 (factory-layout)
│       ├── 布局视图 (factory-floor)
│       └── 列表视图 (factory-list)
├── 底部状态栏 (footer)
└── 工位详情抽屉 (workstation-detail)
```

### **数据流管理**
```javascript
// 响应式数据
const state = reactive({
  workstations: [],        // 工位数据
  loading: false,          // 加载状态
  selectedWorkstation: null, // 选中工位
  drawerVisible: false,    // 抽屉显示
  realTimeData: {},        // 实时统计
  workstationDetails: {}   // 工位详情
})
```

---

## 🚀 **部署与访问**

### **开发环境**
```bash
# 启动开发服务器
npm run dev

# 访问地址
http://localhost:5173/factory-monitor
```

### **生产环境**
```bash
# 构建生产版本
npm run build

# 部署到静态服务器
npm run preview
```

### **路由配置**
```javascript
{
  path: '/factory-monitor',
  name: 'FactoryMonitor',
  component: () => import('@/views/dashboard/FactoryLayoutDashboard.vue'),
  meta: {
    title: '智能制造监控系统',
    noAuth: false
  }
}
```

---

## 🔍 **功能特性详解**

### **1. 态势感知能力**
- ✅ **宏观掌控**：一屏显示145个工位整体状态
- ✅ **异常识别**：故障工位红色闪烁，立即可见
- ✅ **效率监控**：实时效率百分比和趋势分析
- ✅ **快速定位**：点击工位查看详细信息
- ✅ **智能筛选**：按状态、部门快速过滤

### **2. 交互体验**
- **悬停效果**：鼠标悬停放大并显示工位信息
- **点击详情**：点击工位弹出详细信息抽屉
- **键盘导航**：支持Tab键在工位间导航
- **全屏模式**：F11或点击按钮进入全屏显示

### **3. 数据可视化**
- **实时图表**：工位状态分布饼图
- **趋势分析**：效率变化曲线图
- **热力图**：工位活跃度热力分布
- **告警统计**：故障类型分类统计

---

## ⚙️ **系统配置**

### **工位配置参数**
```javascript
// 可调整的配置参数
const CONFIG = {
  // 布局参数
  WORKSTATION_WIDTH: 55,      // 工位宽度(px)
  WORKSTATION_HEIGHT: 45,     // 工位高度(px)
  WORKSTATION_SPACING: 65,    // 工位间距(px)
  
  // 刷新间隔
  AUTO_REFRESH_INTERVAL: 5000, // 自动刷新间隔(ms)
  
  // 状态权重
  STATUS_WEIGHTS: [0.7, 0.15, 0.1, 0.05],
  
  // 效率范围
  EFFICIENCY_MIN: 70,         // 最低效率
  EFFICIENCY_MAX: 100,        // 最高效率
  
  // 设备数量范围
  ASSET_COUNT_MIN: 2,         // 最少设备数
  ASSET_COUNT_MAX: 6          // 最多设备数
}
```

### **主题配置**
```css
/* CSS变量定义 */
:root {
  --primary-color: #3b82f6;
  --success-color: #22c55e;
  --warning-color: #eab308;
  --error-color: #ef4444;
  --info-color: #6b7280;
  
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --bg-tertiary: #334155;
  
  --text-primary: #f1f5f9;
  --text-secondary: #94a3b8;
  --text-tertiary: #64748b;
}
```

---

## 📊 **性能指标**

### **加载性能**
- **首屏加载**：< 2秒
- **数据刷新**：< 500ms
- **动画流畅度**：60FPS
- **内存占用**：< 100MB

### **兼容性**
- **浏览器支持**：Chrome 88+, Firefox 85+, Safari 14+, Edge 88+
- **分辨率适配**：1024x768 - 3840x2160
- **移动设备**：iOS 14+, Android 10+

### **数据容量**
- **工位数据**：145个工位 × 20个字段 = 2.9KB
- **历史数据**：最多保存24小时数据
- **缓存策略**：5分钟本地缓存

---

## 🛠️ **维护与扩展**

### **日常维护**
1. **数据备份**：建议每日备份工位配置和历史数据
2. **性能监控**：监控页面加载时间和内存使用
3. **日志检查**：定期检查浏览器控制台错误
4. **功能测试**：每周进行功能回归测试

### **扩展方向**
1. **数据源集成**：对接真实的工厂MES系统
2. **AI预警**：集成机器学习异常检测
3. **移动端APP**：开发专用移动应用
4. **VR/AR支持**：3D工厂漫游功能

### **API集成准备**
```javascript
// 预留的API接口结构
const apiEndpoints = {
  getWorkstations: '/api/v2/factory-monitoring/workstations',
  getWorkstationDetails: '/api/v2/factory-monitoring/workstations/:id',
  getAssetsByLocation: '/api/v2/assets/by-location/:locationId',
  getTasksByLocation: '/api/v2/tasks/by-location/:locationId',
  getFaultsByLocation: '/api/v2/faults/by-location/:locationId'
}
```

---

## 📞 **技术支持**

### **联系方式**
- **技术支持**：技术部门
- **系统管理员**：IT部门
- **用户培训**：人力资源部

### **问题反馈**
如遇到技术问题，请提供以下信息：
1. 浏览器版本和操作系统
2. 问题重现步骤
3. 错误截图或控制台日志
4. 期望的功能表现

---

## 📝 **更新日志**

### **V2.0 (2025-06-02)**
- ✅ 新增145工位实时监控
- ✅ 新增梯形流水线布局
- ✅ 新增区域标识和色彩编码
- ✅ 新增工位详情抽屉
- ✅ 新增双视图模式(布局/列表)
- ✅ 新增实时数据更新机制
- ✅ 新增响应式设计
- ✅ 新增状态筛选功能

### **V1.0 (规划中)**
- 🔄 对接真实后端API
- 🔄 集成位置部门继承逻辑
- 🔄 增加历史数据分析
- 🔄 增加报表导出功能

---

**📋 文档版本**：V2.0  
**📅 最后更新**：2025年06月02日  
**👨‍💻 维护团队**：IT资产管理系统开发团队