-- 修复任务更新触发器冲突问题
-- 解决 "Can't update table 'tasks' in stored function/trigger" 错误

USE itassets;

-- =====================================================
-- 1. 检查当前存在的触发器
-- =====================================================

SELECT '=== 当前存在的任务相关触发器 ===' AS Info;

SELECT 
    TRIGGER_NAME,
    EVENT_MANIPULATION,
    EVENT_OBJECT_TABLE,
    ACTION_TIMING,
    DEFINER
FROM INFORMATION_SCHEMA.TRIGGERS 
WHERE TRIGGER_SCHEMA = 'itassets' 
  AND EVENT_OBJECT_TABLE IN ('tasks', 'task_claims')
ORDER BY EVENT_OBJECT_TABLE, TRIGGER_NAME;

-- =====================================================
-- 2. 删除所有有问题的触发器
-- =====================================================

SELECT '=== 删除有问题的触发器 ===' AS Info;

-- 删除任务表的触发器
DROP TRIGGER IF EXISTS `tr_tasks_status_update`;
DROP TRIGGER IF EXISTS `tr_tasks_status_update_backup`;
DROP TRIGGER IF EXISTS `tr_tasks_insert`;
DROP TRIGGER IF EXISTS `tr_tasks_update`;

-- 删除任务认领表的触发器
DROP TRIGGER IF EXISTS `tr_task_claims_insert`;
DROP TRIGGER IF EXISTS `tr_task_claims_update`;
DROP TRIGGER IF EXISTS `tr_task_claims_update_backup`;

-- 删除游戏化日志触发器
DROP TRIGGER IF EXISTS `tr_gamification_log_insert`;

SELECT '触发器删除完成' AS Status;

-- =====================================================
-- 3. 验证触发器已删除
-- =====================================================

SELECT '=== 验证触发器删除结果 ===' AS Info;

SELECT 
    TRIGGER_NAME,
    EVENT_MANIPULATION,
    EVENT_OBJECT_TABLE,
    ACTION_TIMING
FROM INFORMATION_SCHEMA.TRIGGERS 
WHERE TRIGGER_SCHEMA = 'itassets' 
  AND EVENT_OBJECT_TABLE IN ('tasks', 'task_claims')
ORDER BY EVENT_OBJECT_TABLE, TRIGGER_NAME;

-- 如果没有结果，说明触发器已全部删除

-- =====================================================
-- 4. 测试任务更新是否正常
-- =====================================================

SELECT '=== 测试任务更新功能 ===' AS Info;

-- 查找一个测试任务
SELECT TaskId, Status, LastUpdatedTimestamp 
FROM tasks 
WHERE TaskId = 524 
LIMIT 1;

-- 显示修复完成信息
SELECT 'Task update trigger conflicts have been resolved!' AS Result;
SELECT 'You can now update task status without trigger conflicts.' AS Instructions;
