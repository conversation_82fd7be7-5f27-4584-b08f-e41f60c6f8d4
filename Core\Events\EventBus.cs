// IT资产管理系统 - 事件总线
// 文件路径: /Core/Events/EventBus.cs
// 功能: 提供系统事件发布、订阅和处理功能

using Microsoft.Extensions.Logging;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace ItAssetsSystem.Core.Events
{
    /// <summary>
    /// 事件总线实现
    /// </summary>
    public class EventBus : IEventBus
    {
        private readonly ILogger<EventBus> _logger;
        private readonly ConcurrentDictionary<Type, List<Delegate>> _handlers = new ConcurrentDictionary<Type, List<Delegate>>();

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志记录器</param>
        public EventBus(ILogger<EventBus> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <inheritdoc/>
        public void Subscribe<TEvent>(Action<TEvent> handler) where TEvent : class
        {
            var eventType = typeof(TEvent);
            _logger.LogDebug($"订阅事件: {eventType.Name}");

            _handlers.AddOrUpdate(
                eventType,
                new List<Delegate> { handler },
                (_, list) =>
                {
                    lock (list)
                    {
                        list.Add(handler);
                        return list;
                    }
                });
        }

        /// <inheritdoc/>
        public void Unsubscribe<TEvent>(Action<TEvent> handler) where TEvent : class
        {
            var eventType = typeof(TEvent);
            _logger.LogDebug($"取消订阅事件: {eventType.Name}");

            if (_handlers.TryGetValue(eventType, out var handlers))
            {
                lock (handlers)
                {
                    handlers.Remove(handler);
                    if (handlers.Count == 0)
                    {
                        _handlers.TryRemove(eventType, out _);
                    }
                }
            }
        }

        /// <inheritdoc/>
        public void Publish<TEvent>(TEvent @event) where TEvent : class
        {
            var eventType = typeof(TEvent);
            _logger.LogDebug($"发布事件: {eventType.Name}");

            if (_handlers.TryGetValue(eventType, out var handlers))
            {
                List<Delegate> handlersCopy;
                lock (handlers)
                {
                    handlersCopy = handlers.ToList();
                }

                foreach (var handler in handlersCopy)
                {
                    try
                    {
                        ((Action<TEvent>)handler)(@event);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"处理事件时发生错误: {eventType.Name}");
                    }
                }
            }
        }

        /// <summary>
        /// 异步发布事件
        /// </summary>
        public Task PublishAsync<TEvent>(TEvent eventData) where TEvent : class
        {
            return Task.Run(() => Publish(eventData));
        }

        /// <summary>
        /// 异步订阅事件
        /// </summary>
        public void SubscribeAsync<TEvent>(Func<TEvent, Task> handler) where TEvent : class
        {
            // 使用适配器将异步处理程序转换为同步处理程序
            Action<TEvent> adapter = async (eventData) => 
            {
                try 
                {
                    await handler(eventData);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"处理异步事件 {typeof(TEvent).Name} 时出错");
                }
            };

            Subscribe(adapter);
        }

        /// <summary>
        /// 取消异步订阅事件
        /// </summary>
        public void UnsubscribeAsync<TEvent>(Func<TEvent, Task> handler) where TEvent : class
        {
            // 由于我们无法直接查找异步处理程序，这里简化为清除该事件的所有处理程序
            // 实际应用中应该维护异步处理程序的字典
            var eventType = typeof(TEvent);
            _logger.LogDebug($"取消所有异步订阅事件 {eventType.Name}");

            _handlers.TryRemove(eventType, out _);
        }
    }

    /// <summary>
    /// 系统通用事件定义
    /// </summary>
    public static class SystemEvents
    {
        /// <summary>
        /// 任务完成事件
        /// </summary>
        public class TaskCompletedEvent
        {
            /// <summary>
            /// 任务ID
            /// </summary>
            public int TaskId { get; set; }
            
            /// <summary>
            /// 是否周期任务
            /// </summary>
            public bool IsPeriodicTask { get; set; }
        }

        /// <summary>
        /// 任务创建事件
        /// </summary>
        public class TaskCreatedEvent
        {
            /// <summary>
            /// 任务ID
            /// </summary>
            public int TaskId { get; set; }
            
            /// <summary>
            /// 任务类型
            /// </summary>
            public int TaskType { get; set; }
        }

        /// <summary>
        /// 资产创建事件
        /// </summary>
        public class AssetCreatedEvent
        {
            /// <summary>
            /// 资产ID
            /// </summary>
            public int AssetId { get; set; }
            
            /// <summary>
            /// 资产名称
            /// </summary>
            public string AssetName { get; set; }
        }

        /// <summary>
        /// 资产分配事件
        /// </summary>
        public class AssetAssignedEvent
        {
            /// <summary>
            /// 资产ID
            /// </summary>
            public int AssetId { get; set; }
            
            /// <summary>
            /// 用户ID
            /// </summary>
            public int UserId { get; set; }
            
            /// <summary>
            /// 位置ID
            /// </summary>
            public int LocationId { get; set; }
        }

        /// <summary>
        /// 故障报告事件
        /// </summary>
        public class FaultReportedEvent
        {
            /// <summary>
            /// 故障ID
            /// </summary>
            public int FaultId { get; set; }
            
            /// <summary>
            /// 资产ID
            /// </summary>
            public int AssetId { get; set; }
            
            /// <summary>
            /// 报告用户ID
            /// </summary>
            public int ReportedByUserId { get; set; }
            
            /// <summary>
            /// 故障等级
            /// </summary>
            public int Severity { get; set; }
        }

        /// <summary>
        /// 故障修复事件
        /// </summary>
        public class FaultFixedEvent
        {
            /// <summary>
            /// 故障ID
            /// </summary>
            public int FaultId { get; set; }
            
            /// <summary>
            /// 修复用户ID
            /// </summary>
            public int FixedByUserId { get; set; }
            
            /// <summary>
            /// 解决方案
            /// </summary>
            public string Solution { get; set; }
        }
    }
}

// 计划行数: 150
// 实际行数: 150 