/**
 * 航空航天级IT资产管理系统 - 故障列表页面
 * 文件路径: src/views/faults/list.vue
 * 功能描述: 展示和管理系统中记录的所有IT设备故障
 */

<template>
  <div class="fault-list-container">
    <!-- 页面标题和操作按钮 -->
    <div class="page-header">
      <h2 class="page-title">故障列表</h2>
      <div class="page-actions">
        <el-button type="primary" @click="handleAddFault" :icon="Plus">
          登记故障
        </el-button>
        <el-button type="primary" @click="handleExportData" :icon="Download">
          导出数据
        </el-button>
      </div>
    </div>

    <!-- 筛选条件 -->
    <el-card class="filter-card">
      <div class="filter-container">
        <el-form v-if="filterForm" :inline="true" :model="filterForm" class="filter-form">
          <el-form-item label="故障编号">
            <el-input v-model="filterForm.code" placeholder="故障编号" clearable />
          </el-form-item>
          <el-form-item label="资产信息">
            <el-input 
              v-model="filterForm.assetKeyword" 
              placeholder="资产名称/编号/SN" 
              clearable 
            />
          </el-form-item>
          <el-form-item label="故障类型">
            <el-select 
              v-model="filterForm.faultType" 
              placeholder="全部类型" 
              clearable
            >
              <el-option 
                v-for="item in faultTypes" 
                :key="item.value" 
                :label="item.label" 
                :value="item.value" 
              />
            </el-select>
          </el-form-item>
          <el-form-item label="处理状态">
            <el-select 
              v-model="filterForm.status" 
              placeholder="全部状态" 
              clearable
            >
              <el-option 
                v-for="item in statusOptions" 
                :key="item.value" 
                :label="item.label" 
                :value="item.value" 
              />
            </el-select>
          </el-form-item>
          <el-form-item label="发生时间">
            <el-date-picker
              v-model="filterForm.timeRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DD"
              :shortcuts="dateShortcuts"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch" :icon="Search">
              搜索
            </el-button>
            <el-button @click="resetFilter" :icon="RefreshRight">
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <!-- 批量操作栏 -->
    <el-card class="batch-operations" v-if="selectedFaults.length > 0">
      <div class="batch-info">
        <span>已选择 {{ selectedFaults.length }} 条故障记录</span>
        <div class="batch-buttons">
          <el-button
            type="warning"
            @click="handleBatchReturnToFactory"
            :disabled="!canBatchReturnToFactory"
          >
            批量返厂
          </el-button>
          <el-button @click="clearSelection">取消选择</el-button>
        </div>
      </div>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="data-card">
      <el-table
        ref="faultTable"
        v-loading="loading"
        :data="faultList || []"
        border
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="code" label="故障编号" width="120" sortable />
        <el-table-column prop="title" label="故障标题" min-width="180" show-overflow-tooltip />
        <el-table-column prop="assetInfo" label="故障资产" width="200" show-overflow-tooltip>
          <template #default="scope">
            <div class="asset-info" v-if="scope.row">
              <div class="asset-name">{{ scope.row.assetName || '未关联资产' }}</div>
              <div class="asset-code text-secondary">{{ scope.row.assetCode || '' }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="faultType" label="故障类型" width="120">
          <template #default="scope">
            <el-tag v-if="scope.row" :type="getFaultTypeTag(scope.row.faultType)" size="small">
              {{ getFaultTypeLabel(scope.row.faultType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="priority" label="优先级" width="100">
          <template #default="scope">
            <el-tag v-if="scope.row" :type="getPriorityTag(scope.row.priority)" size="small">
              {{ getPriorityLabel(scope.row.priority) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="处理状态" width="120">
          <template #default="scope">
            <el-tag v-if="scope.row" :type="getStatusTag(scope.row.status)" size="small">
              {{ getStatusLabel(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="reportUser" label="报告人" width="120" />
        <el-table-column prop="reportTime" label="报告时间" width="180" sortable />
        <el-table-column prop="handler" label="处理人" width="120" />
        <el-table-column prop="updateTime" label="更新时间" width="180" sortable />
        <el-table-column label="操作" width="280" fixed="right">
          <template #default="scope">
            <template v-if="scope.row">
              <el-button
                type="primary"
                text
                size="small"
                @click="handleViewFault(scope.row)"
                :icon="View"
              >
                详情
              </el-button>
              <el-button
                type="success"
                text
                size="small"
                @click="handleProcessFault(scope.row)"
                :icon="Edit"
                :disabled="!canProcess(scope.row)"
              >
                处理
              </el-button>
              <el-button
                type="info"
                text
                size="small"
                @click="handleUseSpareparts(scope.row)"
                :icon="Box"
                :disabled="!canUseSpareParts(scope.row)"
              >
                用料
              </el-button>
              <el-button
                type="warning"
                text
                size="small"
                @click="handleReturnToFactory(scope.row)"
                :icon="Service"
                :disabled="!canReturnToFactory(scope.row)"
              >
                返厂
              </el-button>
            </template>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :background="true"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 故障登记对话框 -->
    <el-dialog
      title="登记故障"
      v-model="faultDialogVisible"
      width="700px"
      append-to-body
    >
      <el-form
        ref="faultFormRef"
        :model="faultForm"
        :rules="faultRules"
        label-width="100px"
      >
        <el-form-item label="故障模式" prop="faultMode">
          <el-radio-group v-model="faultForm.faultMode" @change="handleFaultModeChange">
            <el-radio value="asset">有资产编号设备</el-radio>
            <el-radio value="offline">线下设备（备件台账）</el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- 有资产模式 -->
        <el-form-item v-if="faultForm.faultMode === 'asset'" label="资产" prop="assetId">
          <el-autocomplete
            v-model="faultForm.assetKeyword"
            :fetch-suggestions="queryAssetSearch"
            placeholder="请输入资产编号/名称/位置/SN进行智能搜索"
            clearable
            :maxlength="100"
            style="width: 100%"
            :trigger-on-focus="false"
            @select="handleAssetSelect"
            @clear="clearSelectedAsset"
          >
            <template #default="{ item }">
              <div class="asset-suggestion-item">
                <div class="asset-main">
                  <span class="asset-name">{{ item.name }}</span>
                  <span class="asset-code">{{ item.code }}</span>
                </div>
                <div class="asset-detail">
                  <span class="asset-sn">SN: {{ item.sn || '无' }}</span>
                  <span class="asset-location">{{ item.locationName || '未分配' }}</span>
                </div>
              </div>
            </template>
            <template #append>
              <el-button-group>
                <el-button :icon="Camera" @click="handleScanAsset">扫码</el-button>
                <el-button :icon="Search" @click="handleSearchAsset">高级搜索</el-button>
              </el-button-group>
            </template>
          </el-autocomplete>
          
          <div class="selected-asset" v-if="faultForm.assetId">
            <div class="asset-info">
              <span class="asset-name">{{ faultForm.assetName }}</span>
              <span class="asset-code">{{ faultForm.assetCode }}</span>
              <span class="asset-sn" v-if="faultForm.assetSn">SN: {{ faultForm.assetSn }}</span>
            </div>
            <el-button
              link
              type="danger"
              @click="clearSelectedAsset"
              :icon="Delete"
              size="small"
            >
              清除
            </el-button>
          </div>
          
          <div class="form-tip">
            <el-text type="info" size="small">
              输入时会自动搜索匹配的资产，选择后保存资产信息，否则保存输入的内容
            </el-text>
          </div>
        </el-form-item>

        <!-- 无资产模式（备件台账） -->
        <el-form-item v-if="faultForm.faultMode === 'offline'" label="设备名称" prop="deviceName">
          <el-autocomplete
            v-model="faultForm.deviceName"
            :fetch-suggestions="querySparePartNames"
            placeholder="请输入设备名称（支持从备件库智能匹配）"
            clearable
            :maxlength="100"
            style="width: 100%"
            :trigger-on-focus="true"
            @select="handleDeviceNameSelect"
            @focus="fetchSparePartsForSelection"
          >
            <template #default="{ item }">
              <div class="spare-part-suggestion-item">
                <div class="spare-part-main">
                  <span class="spare-part-name">{{ item.name }}</span>
                  <span class="spare-part-stock">库存: {{ item.stockQuantity || 0 }}</span>
                </div>
                <div class="spare-part-details">
                  <span v-if="item.specification" class="spare-part-spec">{{ item.specification }}</span>
                  <span v-if="item.brand" class="spare-part-brand">{{ item.brand }}</span>
                </div>
              </div>
            </template>
          </el-autocomplete>

          <div class="form-tip">
            <el-text type="info" size="small">
              线下设备模式：输入设备名称智能匹配备件库，适用于电池、线缆等无固定资产编号的设备
            </el-text>
          </div>
        </el-form-item>
        <el-form-item label="故障标题" prop="title">
          <el-input
            v-model="faultForm.title"
            placeholder="请输入故障标题"
            :maxlength="100"
            show-word-limit
            clearable
          />
        </el-form-item>
        <el-form-item label="故障类型" prop="faultType">
          <div class="fault-type-input-group">
            <el-select
              v-model="faultForm.faultType"
              placeholder="请选择故障类型"
              style="width: calc(100% - 80px)"
              clearable
              filterable
            >
              <el-option
                v-for="item in faultTypes"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <el-button :icon="Camera" @click="handleScanFaultType" style="margin-left: 8px;">扫码</el-button>
          </div>
          <div class="form-tip">
            <el-text type="info" size="small">
              支持扫描故障类型二维码自动选择，默认为硬件故障
            </el-text>
          </div>
        </el-form-item>
        <el-form-item label="优先级" prop="priority">
          <el-select
            v-model="faultForm.priority"
            placeholder="请选择优先级"
            style="width: 100%"
            clearable
          >
            <el-option
              v-for="item in priorityOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="发生时间" prop="happenTime">
          <el-date-picker
            v-model="faultForm.happenTime"
            type="datetime-local"
            placeholder="请选择故障发生时间"
            style="width: 100%"
            value-format="YYYY-MM-DDTHH:mm"
          />
        </el-form-item>
        <el-form-item label="故障描述" prop="description">
          <el-input
            v-model="faultForm.description"
            type="textarea"
            :rows="4"
            placeholder="请详细描述故障现象、影响范围等信息"
            :maxlength="1000"
            show-word-limit
            resize="none"
          />
        </el-form-item>

        <!-- 自动生成备件记录选项（仅在有资产模式下显示） -->
        <el-form-item v-if="faultForm.faultMode === 'asset'" label="备件管理">
          <el-checkbox v-model="faultForm.autoGenerateSparePartRecord">
            自动生成备件入库记录
          </el-checkbox>
          <div class="form-tip">
            <el-text type="info" size="small">
              勾选后将自动创建一条备件入库记录，适用于电池等消耗性故障
            </el-text>
          </div>
        </el-form-item>

        <!-- 线下设备模式提示 -->
        <el-form-item v-if="faultForm.faultMode === 'offline'" label="备件管理">
          <div class="offline-device-info">
            <el-alert
              title="线下设备模式"
              type="info"
              :closable="false"
              show-icon
            >
              <template #default>
                选择线下设备后，系统将自动生成对应的备件入库记录，无需手动填写备件信息。
              </template>
            </el-alert>
          </div>
        </el-form-item>

        <!-- 备件信息（当勾选自动生成且为有资产模式时显示） -->
        <div v-if="faultForm.faultMode === 'asset' && faultForm.autoGenerateSparePartRecord" class="spare-part-section">
          <el-divider content-position="left">备件信息</el-divider>
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="备件名称" prop="sparePartName">
                <el-input
                  v-model="faultForm.sparePartName"
                  placeholder="请输入备件名称"
                  :maxlength="50"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="规格型号">
                <el-input
                  v-model="faultForm.sparePartSpecification"
                  placeholder="请输入规格型号"
                  :maxlength="100"
                  clearable
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="16">
            <el-col :span="8">
              <el-form-item label="品牌">
                <el-input
                  v-model="faultForm.sparePartBrand"
                  placeholder="请输入品牌"
                  :maxlength="30"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="数量">
                <el-input-number v-model="faultForm.sparePartQuantity" :min="1" :max="999" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="单价">
                <el-input-number v-model="faultForm.sparePartPrice" :precision="2" :min="0" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <el-form-item label="附件">
          <el-upload
            action="#"
            list-type="picture-card"
            :auto-upload="false"
            :limit="5"
            :on-change="handleFileChange"
            :on-remove="handleFileRemove"
            :file-list="fileList"
          >
            <el-icon><Plus /></el-icon>
          </el-upload>
          <div class="upload-tip text-secondary">
            支持jpg、png、pdf格式，最多5个文件，每个不超过10MB
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="faultDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitFaultForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 资产选择对话框 -->
    <el-dialog
      title="选择资产"
      v-model="assetSelectDialogVisible"
      width="800px"
      append-to-body
    >
      <div class="search-bar">
        <el-input
          v-model="assetSearchKeyword"
          placeholder="请输入资产编号/名称/位置/SN进行智能搜索"
          @keyup.enter="searchAssets"
        >
          <template #append>
            <el-button :icon="Search" @click="searchAssets"/>
          </template>
        </el-input>
      </div>
      <el-table
        ref="assetTable"
        v-loading="assetLoading"
        :data="assetList"
        style="width: 100%"
        height="400px"
        border
        @row-click="handleSelectAsset"
      >
        <el-table-column type="index" label="序号" width="60" align="center">
          <template #default="scope">
            <span>{{ (assetPagination.currentPage - 1) * assetPagination.pageSize + scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="assetCode" label="资产编号" width="120">
          <template #default="scope">
            <span>{{ scope.row.assetCode || scope.row.code || '无编号' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="资产名称" min-width="150" />
        <el-table-column prop="assetTypeName" label="资产类型" width="120">
          <template #default="scope">
            <el-tag v-if="scope.row" size="small">{{ scope.row.assetTypeName || '未知' }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="serialNumber" label="序列号" width="150">
          <template #default="scope">
            <span>{{ scope.row.serialNumber || scope.row.sn || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag v-if="scope.row" size="small" :type="getAssetStatusTag(scope.row.status)">
              {{ scope.row.statusName || getAssetStatusText(scope.row.status) || '未知' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="departmentName" label="所属部门" width="120">
          <template #default="scope">
            <span>{{ scope.row.departmentName || '未分配' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="locationName" label="位置" width="150">
          <template #default="scope">
            <span>{{ scope.row.locationName || '未分配' }}</span>
          </template>
        </el-table-column>
      </el-table>
      <div class="asset-pagination">
        <el-pagination
          v-model:current-page="assetPagination.currentPage"
          v-model:page-size="assetPagination.pageSize"
          :page-sizes="[20, 50, 100]"
          :background="true"
          layout="total, sizes, prev, pager, next"
          :total="assetPagination.total"
          @size-change="handleAssetSizeChange"
          @current-change="handleAssetCurrentChange"
        />
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="assetSelectDialogVisible = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 备件使用对话框 -->
    <el-dialog
      v-model="sparePartsDialogVisible"
      title="故障维修使用备件"
      width="70%"
      :close-on-click-modal="false"
    >
      <div class="spare-parts-dialog-content">
        <div class="fault-info">
          <h4>故障信息</h4>
          <p><strong>故障编号：</strong>{{ currentFault?.code }}</p>
          <p><strong>故障标题：</strong>{{ currentFault?.title }}</p>
          <p><strong>故障资产：</strong>{{ currentFault?.assetName }} ({{ currentFault?.assetCode }})</p>
        </div>

        <div class="spare-parts-selection">
          <div class="section-header">
            <h4>备件使用</h4>
            <el-button type="primary" size="small" @click="addSparePartItem">添加备件</el-button>
          </div>

          <el-table :data="sparePartsForm.spareParts" border style="width: 100%">
            <el-table-column label="备件" width="200">
              <template #default="scope">
                <el-select
                  v-model="scope.row.sparePartId"
                  placeholder="选择备件"
                  filterable
                  @change="(val) => handleSparePartChange(val, scope.$index)"
                  :loading="sparePartsLoading"
                >
                  <el-option
                    v-for="part in availableSpareParts"
                    :key="part.id"
                    :label="`${part.name} (库存: ${part.stockQuantity || 0})`"
                    :value="part.id"
                    :disabled="(part.stockQuantity || 0) <= 0"
                  />
                </el-select>
              </template>
            </el-table-column>
            <el-table-column label="库存" width="80" align="center">
              <template #default="scope">
                <span v-if="scope.row" class="stock-info">{{ getSparePartStock(scope.row.sparePartId) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="使用数量" width="120" align="center">
              <template #default="scope">
                <el-input-number
                  v-if="scope.row"
                  v-model="scope.row.quantity"
                  :min="1"
                  :max="getSparePartStock(scope.row.sparePartId)"
                  size="small"
                />
              </template>
            </el-table-column>
            <el-table-column label="备注" min-width="150">
              <template #default="scope">
                <el-input
                  v-if="scope.row"
                  v-model="scope.row.notes"
                  placeholder="备注信息"
                  size="small"
                />
              </template>
            </el-table-column>
            <el-table-column label="操作" width="80" align="center">
              <template #default="scope">
                <el-button
                  v-if="scope.row"
                  type="danger"
                  text
                  size="small"
                  @click="removeSparePartItem(scope.$index)"
                  :icon="Delete"
                />
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="sparePartsDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmUseSpareParts">确认使用</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 返厂对话框 -->
    <el-dialog
      v-model="returnToFactoryDialogVisible"
      :title="returnToFactoryForm.isBatch ? '批量创建返厂记录' : '创建返厂记录'"
      width="60%"
      :close-on-click-modal="false"
    >
      <div class="return-to-factory-dialog-content">
        <div class="fault-info">
          <h4>{{ returnToFactoryForm.isBatch ? '批量故障信息' : '故障信息' }}</h4>
          <div v-if="returnToFactoryForm.isBatch">
            <p><strong>选择故障数量：</strong>{{ returnToFactoryForm.faultCodes.length }} 条</p>
            <p><strong>故障编号：</strong>{{ returnToFactoryForm.faultCodes.join(', ') }}</p>
          </div>
          <div v-else>
            <p><strong>故障编号：</strong>{{ currentFault?.code }}</p>
            <p><strong>故障标题：</strong>{{ currentFault?.title }}</p>
            <p><strong>故障资产：</strong>{{ currentFault?.assetName }} ({{ currentFault?.assetCode }})</p>
          </div>
        </div>

        <el-form :model="returnToFactoryForm" label-width="100px">
          <el-form-item label="供应商" required>
            <el-select v-model="returnToFactoryForm.supplierId" placeholder="请选择维修供应商" style="width: 100%">
              <el-option
                v-for="supplier in suppliers"
                :key="supplier.id"
                :label="supplier.name"
                :value="supplier.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="返厂原因" required>
            <el-input
              v-model="returnToFactoryForm.reason"
              type="textarea"
              :rows="3"
              placeholder="请详细描述返厂原因"
            />
          </el-form-item>
          <el-form-item label="预计返回时间">
            <el-date-picker
              v-model="returnToFactoryForm.expectedReturnDate"
              type="date"
              placeholder="选择预计返回时间"
              style="width: 100%"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
          <el-form-item label="备注">
            <el-input
              v-model="returnToFactoryForm.notes"
              type="textarea"
              :rows="2"
              placeholder="其他备注信息"
            />
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="returnToFactoryDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmReturnToFactory">确认返厂</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 故障详情对话框 -->
    <el-dialog
      v-model="faultDetailDialogVisible"
      title="故障详情"
      width="70%"
      :close-on-click-modal="false"
    >
      <div v-if="faultDetail" class="fault-detail-content">
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <label>故障编号：</label>
              <span>{{ faultDetail.code }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label>故障状态：</label>
              <el-tag :type="getStatusTagType(faultDetail.status)">
                {{ faultDetail.statusName }}
              </el-tag>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <label>故障标题：</label>
              <span>{{ faultDetail.title }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label>故障类型：</label>
              <span>{{ faultDetail.faultTypeName }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <label>关联资产：</label>
              <span>{{ faultDetail.assetName }}</span>
              <span v-if="faultDetail.assetCode" class="asset-code">({{ faultDetail.assetCode }})</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label>优先级：</label>
              <el-tag :type="getPriorityTagType(faultDetail.priority)">
                {{ getPriorityText(faultDetail.priority) }}
              </el-tag>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <label>报告人：</label>
              <span>{{ faultDetail.reportUser }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label>处理人：</label>
              <span>{{ faultDetail.handler || '未分配' }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <label>报告时间：</label>
              <span>{{ faultDetail.reportTime }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label>更新时间：</label>
              <span>{{ faultDetail.updateTime }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row v-if="faultDetail.happenTime" :gutter="20">
          <el-col :span="12">
            <div class="detail-item">
              <label>发生时间：</label>
              <span>{{ faultDetail.happenTime }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="detail-item">
              <label>位置：</label>
              <span>{{ faultDetail.locationName || '未指定' }}</span>
            </div>
          </el-col>
        </el-row>

        <div class="detail-item full-width">
          <label>故障描述：</label>
          <div class="description-content">
            {{ faultDetail.description }}
          </div>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="faultDetailDialogVisible = false">关闭</el-button>
          <el-button
            type="success"
            @click="handleProcessFault(faultDetail)"
            :disabled="!canProcess(faultDetail)"
          >
            处理故障
          </el-button>
          <el-button
            type="warning"
            @click="handleReturnToFactory(faultDetail)"
            :disabled="!canReturnToFactory(faultDetail)"
          >
            申请返厂
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 故障处理对话框 -->
    <el-dialog
      v-model="processDialogVisible"
      title="故障处理"
      width="60%"
      :close-on-click-modal="false"
    >
      <el-form :model="processForm" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="故障编号：">
              <el-input v-model="processForm.code" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="故障标题：">
              <el-input v-model="processForm.title" disabled />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="处理方式：">
          <el-radio-group v-model="processForm.processType">
            <el-radio value="repair">现场维修</el-radio>
            <el-radio value="return">申请返厂</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="处理人员：">
          <el-select v-model="processForm.assigneeId" placeholder="请选择处理人员" clearable>
            <el-option
              v-for="user in userList"
              :key="user.id"
              :label="user.username"
              :value="user.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item
          v-if="processForm.processType === 'return'"
          label="预计返回时间："
        >
          <el-date-picker
            v-model="processForm.expectedReturnDate"
            type="date"
            placeholder="选择预计返回时间"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>

        <el-form-item label="处理备注：">
          <el-input
            v-model="processForm.notes"
            type="textarea"
            :rows="4"
            placeholder="请输入处理备注..."
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="processDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitProcessFault">
            {{ processForm.processType === 'return' ? '申请返厂' : '开始处理' }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 扫码组件 -->
    <QrCodeScanner
      v-model="scannerVisible"
      :scan-type="scanType"
      @scan-success="handleScanSuccess"
      @manual-input="handleManualInput"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick, shallowReactive, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import faultApi from '@/api/fault'
import returnToFactoryApi from '@/api/returnToFactory'
import { getSpareParts, sparePartInbound, getMaintenanceSuppliers } from '@/api/spareparts'
import { getAssets } from '@/api/asset'
import QrCodeScanner from '@/components/QrCodeScanner.vue'
// 移除可能干扰输入的性能优化导入
// import { debounce, inputOptimization, selectOptimization, adaptConfigForDevice } from '@/utils/performance'
import {
  Search, Plus, Download, View, Edit, Delete, Service,
  Document, RefreshRight, Box, Camera
} from '@element-plus/icons-vue'

// 简化性能配置，避免输入干扰
const performanceConfig = {
  debounceTime: 300,
  pageSize: 20,
  enableAnimation: true
}

// 简单的防抖函数
const debounce = (func, wait) => {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

// 数据加载状态
const loading = ref(false)

// 故障列表数据
const faultList = ref([])
const faultTable = ref(null)

// 分页配置
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 筛选表单
const filterForm = reactive({
  code: '',
  assetKeyword: '',
  faultType: '',
  status: '',
  timeRange: []
})

// 故障类型选项（使用数字ID对应后端枚举）
const faultTypes = [
  { label: '硬件故障', value: 1 },
  { label: '软件故障', value: 2 },
  { label: '网络故障', value: 3 },
  { label: '外设故障', value: 4 },
  { label: '其他故障', value: 5 }
]

// 优先级选项（使用字符串对应后端API）
const priorityOptions = [
  { label: '低', value: 'low' },
  { label: '中', value: 'medium' },
  { label: '高', value: 'high' },
  { label: '紧急', value: 'urgent' }
]

// 状态选项
const statusOptions = [
  { label: '待处理', value: 'pending' },
  { label: '处理中', value: 'processing' },
  { label: '已修复', value: 'resolved' },
  { label: '已关闭', value: 'closed' },
  { label: '待返修', value: 'repair_pending' },
  { label: '返修中', value: 'repairing' }
]

// 日期快捷选项
const dateShortcuts = [
  {
    text: '最近一周',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
      return [start, end]
    }
  },
  {
    text: '最近一个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
      return [start, end]
    }
  },
  {
    text: '最近三个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
      return [start, end]
    }
  }
]

// 故障表单对话框
const faultDialogVisible = ref(false)
const faultForm = ref({
  faultMode: 'asset', // 故障模式：asset(有资产) / offline(线下设备)
  assetId: '',
  assetName: '',
  assetCode: '',
  assetSn: '',
  assetKeyword: '',
  deviceName: '', // 线下设备名称
  title: '',
  faultType: 1, // 默认硬件故障（数字ID）
  priority: 'medium', // 默认中等优先级（字符串）
  happenTime: new Date().toISOString().slice(0, 16), // 默认当前时间
  description: '',
  attachments: [],
  autoGenerateSparePartRecord: false,
  sparePartName: '',
  sparePartSpecification: '',
  sparePartBrand: '',
  sparePartQuantity: 1,
  sparePartPrice: null
})
const faultFormRef = ref(null)

// 资产搜索相关
const assetSearchSuggestions = ref([])
const sparePartsForSelection = ref([])
const sparePartsLoading = ref(false)
const faultRules = {
  // 只有故障标题为必填项，其他都有默认值
  title: [
    { required: true, message: '请输入故障标题', trigger: 'blur' },
    { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  // 故障描述改为可选，只在失焦时验证
  description: [
    { max: 1000, message: '长度不能超过 1000 个字符', trigger: 'blur' }
  ],
  // 备件名称验证优化，减少频繁触发
  sparePartName: [
    {
      validator: (rule, value, callback) => {
        if (faultForm.value.autoGenerateSparePartRecord && !value) {
          callback(new Error('启用自动生成备件记录时，备件名称不能为空'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}
const fileList = ref([])

// 资产选择对话框
const assetSelectDialogVisible = ref(false)
const assetSearchKeyword = ref('')
const assetLoading = ref(false)
const assetList = ref([])
const assetPagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

// 加载供应商列表
const loadSuppliers = async () => {
  try {
    const response = await getMaintenanceSuppliers()
    if (response.success) {
      suppliers.value = response.data || []
    }
  } catch (error) {
    console.error('加载维修供应商失败:', error)
  }
}

// 生命周期钩子
onMounted(() => {
  fetchFaultList()
  loadSuppliers()
})

// 获取故障列表
const fetchFaultList = async () => {
  loading.value = true
  
  // 构建查询参数
  const params = {
    page: pagination.currentPage,
    pageSize: pagination.pageSize,
    code: filterForm.code,
    assetKeyword: filterForm.assetKeyword,
    faultType: filterForm.faultType,
    status: filterForm.status,
    startTime: filterForm.timeRange?.[0],
    endTime: filterForm.timeRange?.[1]
  }
  
  try {
    // 调用真实API获取故障列表
    const response = await faultApi.getFaultList(params)

    if (response.success) {
      faultList.value = response.data.items || response.data || []
      pagination.total = response.data.total || response.data.length || 0
    } else {
      ElMessage.error(response.message || '获取故障列表失败')
      faultList.value = []
      pagination.total = 0
    }
  } catch (error) {
    console.error('获取故障列表失败:', error)
    ElMessage.error('获取故障列表失败')
    faultList.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

// 搜索（移除防抖，避免输入干扰）
const handleSearch = () => {
  pagination.currentPage = 1
  fetchFaultList()
}

// 重置筛选条件
const resetFilter = () => {
  filterForm.code = ''
  filterForm.assetKeyword = ''
  filterForm.faultType = ''
  filterForm.status = ''
  filterForm.timeRange = []
  
  pagination.currentPage = 1
  fetchFaultList()
}

// 分页事件
const handleSizeChange = (size) => {
  pagination.pageSize = size
  fetchFaultList()
}

const handleCurrentChange = (page) => {
  pagination.currentPage = page
  fetchFaultList()
}

// 故障详情对话框
const faultDetailDialogVisible = ref(false)
const faultDetail = ref(null)

// 批量选择相关
const selectedFaults = ref([])
const canBatchReturnToFactory = computed(() => {
  return selectedFaults.value.length > 0 &&
         selectedFaults.value.every(fault => canReturnToFactory(fault))
})

// 查看故障详情
const handleViewFault = async (fault) => {
  try {
    const response = await faultApi.getFaultById(fault.id)
    if (response.success) {
      faultDetail.value = response.data
      faultDetailDialogVisible.value = true
    } else {
      ElMessage.error(response.message || '获取故障详情失败')
    }
  } catch (error) {
    console.error('获取故障详情失败:', error)
    ElMessage.error('获取故障详情失败')
  }
}

// 故障处理对话框
const processDialogVisible = ref(false)
const processForm = ref({
  id: null,
  code: '',
  title: '',
  processType: 'repair', // repair: 维修, return: 返厂
  assigneeId: null,
  notes: '',
  expectedReturnDate: null,
  spareParts: []
})

// 处理故障
const handleProcessFault = (fault) => {
  // 设置默认返厂时间为一周后
  const nextWeek = new Date()
  nextWeek.setDate(nextWeek.getDate() + 7)

  processForm.value = {
    id: fault.id,
    code: fault.code,
    title: fault.title,
    processType: 'repair',
    assigneeId: null,
    notes: '',
    expectedReturnDate: nextWeek,
    spareParts: []
  }
  processDialogVisible.value = true
}

// 提交故障处理
const submitProcessFault = async () => {
  try {
    const requestData = {
      id: processForm.value.id,
      processType: processForm.value.processType,
      assigneeId: processForm.value.assigneeId,
      notes: processForm.value.notes,
      expectedReturnDate: processForm.value.processType === 'return' ? processForm.value.expectedReturnDate : null
    }

    // 这里应该调用后端API
    // const response = await faultApi.processFault(requestData)

    ElMessage.success(
      processForm.value.processType === 'return'
        ? '返厂申请提交成功'
        : '故障处理开始'
    )

    processDialogVisible.value = false

    // 刷新故障列表
    await getFaultList()

  } catch (error) {
    console.error('提交故障处理失败:', error)
    ElMessage.error('提交失败，请重试')
  }
}

// 批量选择处理
const handleSelectionChange = (selection) => {
  selectedFaults.value = selection
}

// 清除选择
const clearSelection = () => {
  selectedFaults.value = []
  if (faultTable.value) {
    faultTable.value.clearSelection()
  }
}

// 批量返厂
const handleBatchReturnToFactory = () => {
  if (selectedFaults.value.length === 0) {
    ElMessage.warning('请先选择要返厂的故障记录')
    return
  }

  // 设置批量返厂表单
  const nextWeek = new Date()
  nextWeek.setDate(nextWeek.getDate() + 7)

  returnToFactoryForm.value = {
    faultIds: selectedFaults.value.map(f => f.id),
    faultCodes: selectedFaults.value.map(f => f.code),
    expectedReturnDate: nextWeek,
    reason: '',
    notes: '',
    isBatch: true
  }

  returnToFactoryDialogVisible.value = true
}

// 申请返修
const handleRepairApply = (fault) => {
  ElMessage.info(`申请返修：${fault.code}`)
  // 实际项目中可以跳转到申请返修页面或打开返修对话框
  // router.push(`/faults/repair/${fault.id}`)
}

// 导出数据
const handleExportData = () => {
  ElMessage.success('开始导出数据，请稍候...')
  // 实际项目中调用导出API
  // exportFaultList(filterForm).then(() => {
  //   ElMessage.success('导出成功')
  // })
}

// 登记故障
const handleAddFault = () => {
  resetFaultForm()
  faultDialogVisible.value = true
}

// 重置故障表单
const resetFaultForm = () => {
  Object.keys(faultForm.value).forEach(key => {
    if (key === 'priority') {
      faultForm.value[key] = 'medium' // 默认中等优先级（字符串）
    } else if (key === 'faultType') {
      faultForm.value[key] = 1 // 默认硬件故障（数字ID）
    } else if (key === 'faultMode') {
      faultForm.value[key] = 'asset' // 默认有资产模式
    } else if (key === 'happenTime') {
      faultForm.value[key] = new Date().toISOString().slice(0, 16) // 默认当前时间
    } else if (key === 'sparePartQuantity') {
      faultForm.value[key] = 1
    } else if (key === 'autoGenerateSparePartRecord') {
      faultForm.value[key] = false
    } else if (key === 'attachments') {
      faultForm.value[key] = []
    } else {
      faultForm.value[key] = key === 'sparePartPrice' ? null : ''
    }
  })
  fileList.value = []
  assetSearchSuggestions.value = []
  sparePartsForSelection.value = []
}

// 提交故障表单
const submitFaultForm = async () => {
  // 表单验证
  try {
    await faultFormRef.value?.validate()
  } catch (error) {
    // 验证失败时，Element Plus 会自动显示错误消息
    return
  }
  
  // 简化验证：只验证故障标题为必填项
  if (!faultForm.value.title || faultForm.value.title.trim().length < 2) {
    ElMessage.warning('请输入至少2个字符的故障标题')
    return
  }

  // 如果启用自动生成备件记录，验证备件信息
  if (faultForm.value.autoGenerateSparePartRecord) {
    if (!faultForm.value.sparePartName) {
      ElMessage.warning('启用自动生成备件记录时，备件名称不能为空')
      return
    }
    if (faultForm.value.sparePartQuantity <= 0) {
      ElMessage.warning('备件数量必须大于0')
      return
    }
  }

  try {
    // 构建提交数据
    const submitData = {
      faultMode: faultForm.value.faultMode,
      assetId: faultForm.value.faultMode === 'asset' ? faultForm.value.assetId || null : null,
      assetKeyword: faultForm.value.faultMode === 'asset' ? faultForm.value.assetKeyword : null,
      deviceName: faultForm.value.faultMode === 'offline' ? faultForm.value.deviceName : null,
      faultType: faultForm.value.faultType,
      title: faultForm.value.title,
      description: faultForm.value.description,
      priority: faultForm.value.priority,
      happenTime: faultForm.value.happenTime,
      autoGenerateSparePartRecord: faultForm.value.autoGenerateSparePartRecord,
      sparePartInfo: faultForm.value.autoGenerateSparePartRecord ? {
        name: faultForm.value.sparePartName,
        specification: faultForm.value.sparePartSpecification,
        brand: faultForm.value.sparePartBrand,
        quantity: faultForm.value.sparePartQuantity,
        price: faultForm.value.sparePartPrice
      } : null
    }

    // 调用真实API提交故障
    const response = await faultApi.createFault(submitData)

    if (response.success) {
      const faultCode = `FIX-${new Date().toISOString().slice(0, 10).replace(/-/g, '')}-${String(response.data.id).padStart(3, '0')}`
      let successMessage = `故障登记成功！故障编号：${faultCode}`

      // 如果是线下设备模式，自动生成备件入库记录
      if (faultForm.value.faultMode === 'offline' && faultForm.value.deviceName) {
        try {
          const inboundResult = await handleOfflineDeviceInbound(response.data.id)
          successMessage += `\n✅ 备件入库记录已自动创建`
          successMessage += `\n📦 匹配的备件：${inboundResult.matchedSparePart.name}`
        } catch (inboundError) {
          console.error('备件入库失败:', inboundError)
          successMessage += `\n⚠️ 备件入库失败：${inboundError.message}`
          ElMessage.warning(`备件入库失败：${inboundError.message}`)
        }
      } else if (faultForm.value.autoGenerateSparePartRecord) {
        successMessage += '\n✅ 备件记录已自动创建'
      }

      ElMessage.success(successMessage)
      faultDialogVisible.value = false
      fetchFaultList()
    } else {
      ElMessage.error(response.message || '故障登记失败')
    }
  } catch (error) {
    console.error('故障登记失败:', error)
    ElMessage.error('故障登记失败')
  }
}

// 搜索资产
const handleSearchAsset = () => {
  assetSelectDialogVisible.value = true
  assetSearchKeyword.value = faultForm.value.assetKeyword
  searchAssets()
}

// 搜索资产列表（内部实现）
const _searchAssets = async () => {
  assetLoading.value = true

  // 构建查询参数
  const params = {
    keyword: assetSearchKeyword.value,
    page: assetPagination.currentPage,
    pageSize: assetPagination.pageSize
  }

  try {
    console.log('高级搜索资产，参数:', params)

    // 调用正确的资产API搜索资产
    const response = await getAssets({
      keyword: assetSearchKeyword.value,
      pageSize: params.pageSize,
      pageIndex: params.page
    })

    console.log('高级搜索资产API响应:', response)

    if (response.success) {
      const assets = response.data.items || response.data || []
      console.log('高级搜索找到的资产数据:', assets)

      // 确保数据格式正确
      assetList.value = assets.map(asset => ({
        id: asset.id,
        assetCode: asset.assetCode || asset.code,
        code: asset.assetCode || asset.code, // 兼容性
        name: asset.name,
        assetTypeName: asset.assetTypeName,
        serialNumber: asset.serialNumber,
        sn: asset.serialNumber, // 兼容性
        brand: asset.brand,
        model: asset.model,
        status: asset.status,
        statusName: asset.statusName,
        locationName: asset.locationName,
        locationId: asset.locationId,
        departmentName: asset.departmentName,
        departmentId: asset.departmentId
      }))

      assetPagination.total = response.data.total || assets.length || 0
      console.log('高级搜索处理后的资产列表:', assetList.value)
      console.log('高级搜索分页信息 - 总数:', assetPagination.total, '当前页:', assetPagination.currentPage, '每页大小:', assetPagination.pageSize)
    } else {
      ElMessage.error(response.message || '搜索资产失败')
      assetList.value = []
      assetPagination.total = 0
    }
  } catch (error) {
    console.error('搜索资产失败:', error)
    ElMessage.error('搜索资产失败')
    assetList.value = []
    assetPagination.total = 0
  } finally {
    assetLoading.value = false
  }
}

// 搜索资产列表（移除防抖优化）
const searchAssets = _searchAssets

// 选择资产
const handleSelectAsset = (row) => {
  console.log('高级搜索选择的资产:', row)

  faultForm.value.assetId = row.id
  faultForm.value.assetName = row.name
  faultForm.value.assetCode = row.assetCode || row.code
  faultForm.value.assetSn = row.serialNumber || row.sn
  faultForm.value.assetKeyword = `${row.assetCode || row.code} - ${row.name} - ${row.locationName || '未分配'} - ${row.departmentName || '未分配'}`

  // 自动设置故障描述中的资产信息
  let assetInfo = `【资产信息】编号: ${row.assetCode || row.code}, 名称: ${row.name}, 位置: ${row.locationName || '未分配'}, 部门: ${row.departmentName || '未分配'}`

  if (row.serialNumber || row.sn) {
    assetInfo += `, SN: ${row.serialNumber || row.sn}`
  }

  if (row.brand || row.model) {
    const brandModel = [row.brand, row.model].filter(Boolean).join(' ')
    if (brandModel) {
      assetInfo += `, 品牌型号: ${brandModel}`
    }
  }

  if (!faultForm.value.description || faultForm.value.description.trim() === '') {
    faultForm.value.description = assetInfo
  } else if (!faultForm.value.description.includes('【资产信息】')) {
    faultForm.value.description = assetInfo + '\n\n' + faultForm.value.description
  }

  assetSelectDialogVisible.value = false
  ElMessage.success(`已选择资产：${row.assetCode || row.code} - ${row.name}`)
}

// 清除已选资产
const clearSelectedAsset = () => {
  faultForm.value.assetId = ''
  faultForm.value.assetName = ''
  faultForm.value.assetCode = ''
  faultForm.value.assetSn = ''
  faultForm.value.assetKeyword = ''
}

// 故障模式切换处理
const handleFaultModeChange = (mode) => {
  // 切换模式时清空相关字段
  if (mode === 'asset') {
    faultForm.value.deviceName = ''
  } else {
    clearSelectedAsset()
  }
}

// 资产自动完成搜索 - 支持智能匹配位置、名称、编号
const queryAssetSearch = async (queryString, callback) => {
  if (!queryString || queryString.length < 1) {
    callback([])
    return
  }

  try {
    console.log('开始资产搜索，关键词:', queryString)

    // 调用资产API进行智能搜索
    const response = await getAssets({
      keyword: queryString,
      pageSize: 20, // 增加搜索结果数量
      pageIndex: 1
    })

    console.log('资产搜索API响应:', response)

    if (response.success) {
      const assets = response.data.items || response.data || []
      console.log('找到的资产数据:', assets)

      const suggestions = assets.map(asset => {
        // 构建完整的显示信息：资产编号 - 名称 - 位置 - 部门
        const assetCode = asset.assetCode || asset.code || '无编号'
        const assetName = asset.name || '未命名'
        const locationName = asset.locationName || '未分配位置'
        const departmentName = asset.departmentName || '未分配部门'
        const serialNumber = asset.serialNumber || asset.sn || ''
        const brand = asset.brand || ''
        const model = asset.model || ''

        // 显示格式：资产编号 - 名称 - 位置 - 部门
        let displayValue = `${assetCode} - ${assetName} - ${locationName} - ${departmentName}`

        // 如果有序列号，添加到显示中
        if (serialNumber) {
          displayValue += ` | SN: ${serialNumber}`
        }

        // 如果有品牌型号，添加到显示中
        if (brand || model) {
          const brandModel = [brand, model].filter(Boolean).join(' ')
          if (brandModel) {
            displayValue += ` | ${brandModel}`
          }
        }

        return {
          id: asset.id,
          name: assetName,
          code: assetCode,
          assetCode: assetCode,
          sn: serialNumber,
          serialNumber: serialNumber,
          brand: brand,
          model: model,
          locationName: locationName,
          locationId: asset.locationId,
          departmentName: departmentName,
          departmentId: asset.departmentId,
          value: displayValue,
          // 用于匹配的关键词
          searchText: `${assetCode} ${assetName} ${locationName} ${departmentName} ${serialNumber} ${brand} ${model}`.toLowerCase()
        }
      })

      console.log('处理后的搜索建议:', suggestions)

      // 按匹配度排序
      const sortedSuggestions = suggestions.sort((a, b) => {
        const query = queryString.toLowerCase()
        const aMatch = a.searchText.indexOf(query)
        const bMatch = b.searchText.indexOf(query)

        // 优先显示完全匹配的结果
        if (aMatch === 0 && bMatch !== 0) return -1
        if (bMatch === 0 && aMatch !== 0) return 1

        // 其次按匹配位置排序
        return aMatch - bMatch
      })

      callback(sortedSuggestions)
    } else {
      console.error('资产搜索API失败:', response.message)
      callback([])
    }
  } catch (error) {
    console.error('资产搜索失败:', error)
    callback([])
  }
}

// 选择资产（从自动完成）
const handleAssetSelect = (item) => {
  console.log('选择的资产:', item)

  faultForm.value.assetId = item.id
  faultForm.value.assetName = item.name
  faultForm.value.assetCode = item.assetCode || item.code
  faultForm.value.assetSn = item.serialNumber || item.sn
  faultForm.value.assetKeyword = item.value

  // 自动设置故障描述中的资产信息
  let assetInfo = `【资产信息】编号: ${item.assetCode || item.code}, 名称: ${item.name}, 位置: ${item.locationName || '未分配'}, 部门: ${item.departmentName || '未分配'}`

  if (item.serialNumber || item.sn) {
    assetInfo += `, SN: ${item.serialNumber || item.sn}`
  }

  if (item.brand || item.model) {
    const brandModel = [item.brand, item.model].filter(Boolean).join(' ')
    if (brandModel) {
      assetInfo += `, 品牌型号: ${brandModel}`
    }
  }

  if (!faultForm.value.description || faultForm.value.description.trim() === '') {
    faultForm.value.description = assetInfo
  } else if (!faultForm.value.description.includes('【资产信息】')) {
    faultForm.value.description = assetInfo + '\n\n' + faultForm.value.description
  }

  ElMessage.success(`已选择资产：${item.assetCode || item.code} - ${item.name}`)
}

// 备件名称智能搜索（用于线下设备名称输入）
const querySparePartNames = async (queryString, callback) => {
  if (!queryString || queryString.length < 1) {
    // 如果没有输入，显示所有备件
    if (sparePartsForSelection.value.length > 0) {
      const suggestions = sparePartsForSelection.value.map(part => ({
        ...part,
        value: part.name
      }))
      callback(suggestions.slice(0, 10)) // 限制显示数量
    } else {
      callback([])
    }
    return
  }

  // 确保备件列表已加载
  if (sparePartsForSelection.value.length === 0) {
    await fetchSparePartsForSelection()
  }

  // 智能匹配备件名称
  const query = queryString.toLowerCase()
  const matchedParts = sparePartsForSelection.value.filter(part => {
    const name = part.name.toLowerCase()
    const spec = (part.specification || '').toLowerCase()
    const brand = (part.brand || '').toLowerCase()

    return name.includes(query) ||
           spec.includes(query) ||
           brand.includes(query) ||
           query.includes(name)
  })

  // 按匹配度排序
  const sortedParts = matchedParts.sort((a, b) => {
    const aName = a.name.toLowerCase()
    const bName = b.name.toLowerCase()
    const aMatch = aName.indexOf(query)
    const bMatch = bName.indexOf(query)

    // 优先显示名称开头匹配的
    if (aMatch === 0 && bMatch !== 0) return -1
    if (bMatch === 0 && aMatch !== 0) return 1

    // 其次按匹配位置排序
    return aMatch - bMatch
  })

  const suggestions = sortedParts.map(part => ({
    ...part,
    value: part.name
  }))

  callback(suggestions.slice(0, 10)) // 限制显示数量
}

// 处理设备名称选择
const handleDeviceNameSelect = (item) => {
  faultForm.value.deviceName = item.name

  // 自动设置故障描述中的设备信息
  let deviceInfo = `【线下设备】设备名称: ${item.name}`
  if (item.specification) {
    deviceInfo += `\n规格: ${item.specification}`
  }
  if (item.brand) {
    deviceInfo += `\n品牌: ${item.brand}`
  }

  if (!faultForm.value.description || faultForm.value.description.trim() === '') {
    faultForm.value.description = deviceInfo
  } else if (!faultForm.value.description.includes('【线下设备】')) {
    faultForm.value.description = deviceInfo + '\n\n' + faultForm.value.description
  }

  ElMessage.success(`已选择设备：${item.name}`)
}

// 获取备件台账列表（用于线下设备选择）
const fetchSparePartsForSelection = async () => {
  if (sparePartsForSelection.value.length > 0) {
    return // 已经加载过了
  }

  sparePartsLoading.value = true
  try {
    const response = await getSpareParts({
      pageSize: 100,
      onlyNames: true // 只获取名称，用于选择
    })

    if (response.success) {
      // 去重处理，按名称分组，并包含库存信息
      const uniqueParts = {}
      const items = response.data.items || response.data || []

      items.forEach(part => {
        if (!uniqueParts[part.name]) {
          uniqueParts[part.name] = {
            id: part.id,
            name: part.name,
            specification: part.specification,
            brand: part.brand,
            stockQuantity: part.stockQuantity || 0
          }
        } else {
          // 累加库存数量
          uniqueParts[part.name].stockQuantity += (part.stockQuantity || 0)
        }
      })

      sparePartsForSelection.value = Object.values(uniqueParts)
    } else {
      ElMessage.error('获取备件台账失败')
      sparePartsForSelection.value = []
    }
  } catch (error) {
    console.error('获取备件台账失败:', error)
    ElMessage.error('获取备件台账失败')
    sparePartsForSelection.value = []
  } finally {
    sparePartsLoading.value = false
  }
}

// 处理线下设备自动入库 - 支持智能匹配备件库资产
const handleOfflineDeviceInbound = async (faultId) => {
  // 确保备件列表已加载
  if (sparePartsForSelection.value.length === 0) {
    await fetchSparePartsForSelection()
  }

  // 智能匹配备件：支持模糊匹配设备名称
  const deviceName = faultForm.value.deviceName.toLowerCase()
  let selectedSparePart = null

  // 1. 精确匹配
  selectedSparePart = sparePartsForSelection.value.find(
    part => part.name.toLowerCase() === deviceName
  )

  // 2. 如果精确匹配失败，尝试包含匹配
  if (!selectedSparePart) {
    selectedSparePart = sparePartsForSelection.value.find(
      part => part.name.toLowerCase().includes(deviceName) ||
              deviceName.includes(part.name.toLowerCase())
    )
  }

  // 3. 如果还是没找到，尝试关键词匹配
  if (!selectedSparePart) {
    const keywords = deviceName.split(/[\s\-_]+/) // 按空格、横线、下划线分割
    selectedSparePart = sparePartsForSelection.value.find(part => {
      const partName = part.name.toLowerCase()
      return keywords.some(keyword =>
        keyword.length > 1 && partName.includes(keyword)
      )
    })
  }

  if (!selectedSparePart) {
    throw new Error(`未找到与设备名称 "${faultForm.value.deviceName}" 匹配的备件记录。请检查设备名称是否与备件库中的资产名称一致。`)
  }

  // 构建入库数据
  const inboundData = {
    partId: selectedSparePart.id,
    quantity: 1, // 默认数量为1
    reasonType: 4, // 4=故障入库（新增故障入库类型）
    reason: `故障登记自动入库 - 故障编号: FIX-${new Date().toISOString().slice(0, 10).replace(/-/g, '')}-${String(faultId).padStart(3, '0')}`,
    referenceNumber: `FAULT-${faultId}`,
    remarks: `线下设备故障登记自动生成的入库记录\n设备名称: ${faultForm.value.deviceName}\n匹配的备件: ${selectedSparePart.name}\n故障标题: ${faultForm.value.title}`,
    locationId: 1 // 默认库位，实际应该从配置获取
  }

  // 调用备件入库API
  const response = await sparePartInbound(inboundData)

  if (!response.success) {
    throw new Error(response.message || '备件入库失败')
  }

  return {
    ...response.data,
    matchedSparePart: selectedSparePart
  }
}

// 扫描资产二维码
const handleScanAsset = () => {
  scanType.value = 'asset'
  scannerVisible.value = true
}

// 扫描故障类型二维码
const handleScanFaultType = () => {
  scanType.value = 'faultType'
  scannerVisible.value = true
}

// 处理扫码成功
const handleScanSuccess = (data) => {
  switch (data.type) {
    case 'asset':
      faultForm.value.assetId = data.id
      faultForm.value.assetName = data.name
      faultForm.value.assetCode = data.code
      faultForm.value.assetKeyword = `${data.name} (${data.code})`
      ElMessage.success(`已关联资产：${data.name}`)
      break
    case 'faultType':
      faultForm.value.faultType = data.id
      ElMessage.success(`已选择故障类型：${data.name}`)
      break
    case 'deviceType':
      // 根据设备类型自动设置故障类型
      if (data.name.includes('电脑') || data.name.includes('PC')) {
        faultForm.value.faultType = 1 // 硬件故障
      } else if (data.name.includes('打印机')) {
        faultForm.value.faultType = 4 // 外设故障
      } else if (data.name.includes('扫码器')) {
        faultForm.value.faultType = 4 // 外设故障
      }
      ElMessage.success(`已识别设备类型：${data.name}`)
      break
  }
}

// 处理手动输入
const handleManualInput = (type) => {
  switch (type) {
    case 'asset':
      handleSearchAsset()
      break
    case 'faultType':
      ElMessage.info('请从下拉列表中选择故障类型')
      break
    case 'deviceType':
      ElMessage.info('请手动选择对应的故障类型')
      break
  }
}

// 资产分页事件
const handleAssetSizeChange = (size) => {
  assetPagination.pageSize = size
  searchAssets()
}

const handleAssetCurrentChange = (page) => {
  assetPagination.currentPage = page
  searchAssets()
}

// 文件上传相关
const handleFileChange = (file) => {
  // 文件大小限制检查
  if (file.size > 10 * 1024 * 1024) {
    ElMessage.warning('文件大小不能超过10MB')
    const index = fileList.value.indexOf(file)
    if (index !== -1) {
      fileList.value.splice(index, 1)
    }
    return
  }
  
  // 后缀检查
  const ext = file.name.split('.').pop().toLowerCase()
  if (!['jpg', 'jpeg', 'png', 'pdf'].includes(ext)) {
    ElMessage.warning('只支持jpg、png、pdf格式文件')
    const index = fileList.value.indexOf(file)
    if (index !== -1) {
      fileList.value.splice(index, 1)
    }
    return
  }
  
  // 实际项目中，这里可以处理文件预览、获取文件URL等操作
}

const handleFileRemove = (file) => {
  const index = fileList.value.indexOf(file)
  if (index !== -1) {
    fileList.value.splice(index, 1)
  }
}

// 工具方法：获取故障类型标签样式
const getFaultTypeTag = (type) => {
  const map = {
    1: 'danger',    // 硬件故障
    2: 'warning',   // 软件故障
    3: 'info',      // 网络故障
    4: 'success',   // 外设故障
    5: ''           // 其他故障
  }
  return map[type] || ''
}

// 工具方法：获取资产状态标签样式
const getAssetStatusTag = (status) => {
  const map = {
    0: 'info',      // 闲置
    1: 'success',   // 在用
    2: 'warning',   // 维修中
    3: 'danger'     // 报废
  }
  return map[status] || 'info'
}

// 工具方法：获取资产状态文本
const getAssetStatusText = (status) => {
  const map = {
    0: '闲置',
    1: '在用',
    2: '维修中',
    3: '报废'
  }
  return map[status] || '未知状态'
}

// 工具方法：获取故障类型文本
const getFaultTypeLabel = (type) => {
  const map = {
    1: '硬件故障',
    2: '软件故障',
    3: '网络故障',
    4: '外设故障',
    5: '其他故障'
  }
  return map[type] || '未知类型'
}

// 工具方法：获取优先级标签样式
const getPriorityTag = (priority) => {
  const map = {
    1: 'info',      // 低
    2: '',          // 中
    3: 'warning',   // 高
    4: 'danger'     // 紧急
  }
  return map[priority] || ''
}

// 工具方法：获取优先级文本
const getPriorityLabel = (priority) => {
  const map = {
    1: '低',
    2: '中',
    3: '高',
    4: '紧急'
  }
  return map[priority] || '未知'
}

// 工具方法：获取状态标签样式
const getStatusTag = (status) => {
  const map = {
    'pending': 'info',
    'processing': 'warning',
    'resolved': 'success',
    'closed': '',
    'repair_pending': 'warning',
    'repairing': 'danger'
  }
  return map[status] || ''
}

// 工具方法：获取状态文本
const getStatusLabel = (status) => {
  const map = {
    'pending': '待处理',
    'processing': '处理中',
    'resolved': '已修复',
    'closed': '已关闭',
    'repair_pending': '待返修',
    'repairing': '返修中'
  }
  return map[status] || '未知'
}

// 获取状态标签类型
const getStatusTagType = (status) => {
  const map = {
    0: 'warning',  // 待处理
    1: 'primary',  // 处理中
    2: 'success',  // 已解决
    3: 'info'      // 已关闭
  }
  return map[status] || 'info'
}

// 获取优先级标签类型
const getPriorityTagType = (priority) => {
  const map = {
    'low': 'info',
    'medium': 'warning',
    'high': 'danger'
  }
  return map[priority] || 'info'
}

// 获取优先级文本
const getPriorityText = (priority) => {
  const map = {
    'low': '低',
    'medium': '中',
    'high': '高'
  }
  return map[priority] || priority
}



// 工具方法：判断是否可以处理
const canProcess = (fault) => {
  // 状态：0-待处理, 1-处理中, 2-已解决, 3-已关闭
  return [0, 1].includes(fault.status)
}

// 工具方法：判断是否可以申请返修
const canApplyRepair = (fault) => {
  // 状态：0-待处理, 1-处理中
  return fault.status === 0 || fault.status === 1
}

// 工具方法：判断是否可以使用备件
const canUseSpareParts = (fault) => {
  // 只有处理中状态才能使用备件
  return fault.status === 1
}

// 工具方法：判断是否可以返厂
const canReturnToFactory = (fault) => {
  // 状态：0-待处理, 1-处理中
  return [0, 1].includes(fault.status)
}

// 备件使用对话框
const sparePartsDialogVisible = ref(false)
const currentFault = ref(null)
const sparePartsForm = reactive({
  spareParts: [],
  notes: ''
})
const availableSpareParts = ref([])
// sparePartsLoading 已在上面声明，不需要重复

// 扫码相关
const scannerVisible = ref(false)
const scanType = ref('asset') // asset, faultType, deviceType

// 获取可用备件列表
const fetchAvailableSpareParts = async () => {
  sparePartsLoading.value = true
  try {
    const response = await getSpareParts({ pageSize: 100 })
    if (response.success) {
      availableSpareParts.value = response.data.items || response.data || []
    } else {
      ElMessage.error('获取备件列表失败')
      availableSpareParts.value = []
    }
  } catch (error) {
    console.error('获取备件列表失败:', error)
    ElMessage.error('获取备件列表失败')
    availableSpareParts.value = []
  } finally {
    sparePartsLoading.value = false
  }
}

// 处理备件使用
const handleUseSpareparts = (fault) => {
  currentFault.value = fault
  sparePartsForm.spareParts = []
  sparePartsForm.notes = ''
  sparePartsDialogVisible.value = true
  fetchAvailableSpareParts()
}

// 添加备件使用项
const addSparePartItem = () => {
  sparePartsForm.spareParts.push({
    sparePartId: '',
    sparePartName: '',
    quantity: 1,
    notes: ''
  })
}

// 删除备件使用项
const removeSparePartItem = (index) => {
  sparePartsForm.spareParts.splice(index, 1)
}

// 确认使用备件
const confirmUseSpareParts = async () => {
  try {
    if (sparePartsForm.spareParts.length === 0) {
      ElMessage.warning('请至少添加一个备件')
      return
    }

    // 验证数据
    for (const item of sparePartsForm.spareParts) {
      if (!item.sparePartId) {
        ElMessage.warning('请选择备件')
        return
      }
      if (item.quantity <= 0) {
        ElMessage.warning('备件数量必须大于0')
        return
      }
    }

    // 调用API
    const response = await faultApi.useSpareParts(currentFault.value.id, {
      spareParts: sparePartsForm.spareParts,
      notes: sparePartsForm.notes
    })

    if (!response.success) {
      ElMessage.error(response.message || '备件使用记录失败')
      return
    }

    ElMessage.success('备件使用记录成功')
    sparePartsDialogVisible.value = false
    fetchFaultList()
  } catch (error) {
    ElMessage.error('备件使用记录失败：' + error.message)
  }
}

// 返厂对话框
const returnToFactoryDialogVisible = ref(false)
const returnToFactoryForm = ref({
  faultIds: [],
  faultCodes: [],
  supplierId: '',
  reason: '',
  expectedReturnDate: '',
  notes: '',
  isBatch: false
})

// 供应商数据
const suppliers = ref([])

// 处理返厂
const handleReturnToFactory = (fault) => {
  currentFault.value = fault

  // 设置默认返厂时间为一周后
  const nextWeek = new Date()
  nextWeek.setDate(nextWeek.getDate() + 7)

  returnToFactoryForm.value = {
    faultIds: [fault.id],
    faultCodes: [fault.code],
    supplierId: '',
    reason: '',
    expectedReturnDate: nextWeek,
    notes: '',
    isBatch: false
  }
  returnToFactoryDialogVisible.value = true
}

// 确认返厂
const confirmReturnToFactory = async () => {
  try {
    if (!returnToFactoryForm.value.supplierId) {
      ElMessage.warning('请选择供应商')
      return
    }
    if (!returnToFactoryForm.value.reason) {
      ElMessage.warning('请输入返厂原因')
      return
    }

    if (returnToFactoryForm.value.isBatch) {
      // 批量返厂
      ElMessage.success(`批量返厂申请提交成功，共 ${returnToFactoryForm.value.faultIds.length} 条故障记录`)

      // 清除选择
      clearSelection()
    } else {
      // 单个返厂
      // 调用API
      // const response = await faultApi.createReturnToFactory(currentFault.value.id, {
      //   assetId: currentFault.value.assetId,
      //   supplierId: returnToFactoryForm.value.supplierId,
      //   reason: returnToFactoryForm.value.reason,
      //   expectedReturnDate: returnToFactoryForm.value.expectedReturnDate,
      //   notes: returnToFactoryForm.value.notes
      // })

      ElMessage.success('返厂记录创建成功')
    }

    returnToFactoryDialogVisible.value = false
    fetchFaultList()
  } catch (error) {
    ElMessage.error('创建返厂记录失败：' + error.message)
  }
}

// 备件选择变化处理
const handleSparePartChange = (sparePartId, index) => {
  const selectedPart = availableSpareParts.value.find(part => part.id === sparePartId)
  if (selectedPart) {
    sparePartsForm.spareParts[index].sparePartName = selectedPart.name
    sparePartsForm.spareParts[index].maxQuantity = selectedPart.stockQuantity || 0
  }
}

// 获取备件库存
const getSparePartStock = (sparePartId) => {
  const part = availableSpareParts.value.find(p => p.id === sparePartId)
  return part ? (part.stockQuantity || 0) : 0
}
</script>

<style lang="scss" scoped>
@use "@/styles/variables.scss" as *;

.fault-list-container {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    
    .page-title {
      font-size: 20px;
      font-weight: 600;
      color: #303133;
      margin: 0;
    }
    
    .page-actions {
      display: flex;
      gap: 8px;
    }
  }
  
  .filter-card {
    margin-bottom: 16px;
    
    .filter-container {
      .filter-form {
        display: flex;
        flex-wrap: wrap;
      }
    }
  }
  
  .data-card {
    .pagination-container {
      margin-top: 16px;
      display: flex;
      justify-content: flex-end;
    }
    
    .asset-info {
      .asset-name {
        font-weight: 500;
      }
      
      .asset-code {
        font-size: 12px;
        color: #909399;
      }
    }
  }
  
  .asset-select-input {
    width: 100%;
  }

  .selected-asset {
    margin-top: 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    border-radius: 4px;
    background-color: #f5f7fa;
    
    .asset-info {
      .asset-name {
        font-weight: 500;
        margin-right: 8px;
      }
      
      .asset-code {
        color: #909399;
        font-size: 12px;
      }
    }
  }
  
  .upload-tip {
    font-size: 12px;
    margin-top: 4px;
  }
  
  .text-secondary {
    color: #909399;
  }
  
  .search-bar {
    margin-bottom: 16px;
  }
  
  .asset-pagination {
    margin-top: 16px;
    display: flex;
    justify-content: flex-end;
  }
}

/* 备件使用和返厂对话框样式 */
.spare-parts-dialog-content,
.return-to-factory-dialog-content {
  .fault-info {
    background: #f5f7fa;
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 20px;

    h4 {
      margin: 0 0 10px 0;
      color: #303133;
    }

    p {
      margin: 5px 0;
      color: #606266;
    }
  }
}

// 故障详情对话框样式
.fault-detail-content {
  .detail-item {
    margin-bottom: 15px;
    display: flex;
    align-items: flex-start;

    label {
      font-weight: 600;
      color: #303133;
      min-width: 100px;
      margin-right: 10px;
    }

    span {
      color: #606266;
      flex: 1;
    }

    .asset-code {
      color: #909399;
      margin-left: 5px;
    }

    &.full-width {
      flex-direction: column;

      label {
        margin-bottom: 8px;
      }

      .description-content {
        background: #f5f7fa;
        padding: 12px;
        border-radius: 4px;
        color: #606266;
        line-height: 1.6;
        white-space: pre-wrap;
        word-break: break-word;
      }
    }
  }

  .el-row {
    margin-bottom: 10px;
  }
}

.spare-parts-selection {
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;

    h4 {
      margin: 0;
      color: #303133;
    }
  }

  .stock-info {
    color: #409eff;
    font-weight: bold;
  }
}

/* 新增样式 */
.form-tip {
  margin-top: 4px;
}

.spare-part-section {
  background: #fafafa;
  padding: 16px;
  border-radius: 6px;
  margin-top: 8px;
  border: 1px solid #e4e7ed;
}

.spare-part-section .el-divider {
  margin: 0 0 16px 0;
}

/* 故障类型输入组样式 */
.fault-type-input-group {
  display: flex;
  align-items: center;
  width: 100%;
}

/* 资产输入组样式 */
.asset-input-group {
  width: 100%;
}

.asset-input-group .el-button-group {
  display: flex;
}

.asset-input-group .el-button-group .el-button {
  border-radius: 0;
}

.asset-input-group .el-button-group .el-button:first-child {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.asset-input-group .el-button-group .el-button:last-child {
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}

/* 资产自动完成建议样式 */
.asset-suggestion-item {
  padding: 8px 0;
  
  .asset-main {
    display: flex;
    align-items: center;
    margin-bottom: 4px;
    
    .asset-name {
      font-weight: 500;
      margin-right: 8px;
    }
    
    .asset-code {
      color: #409eff;
      font-size: 12px;
      background: #ecf5ff;
      padding: 2px 6px;
      border-radius: 3px;
    }
  }
  
  .asset-detail {
    display: flex;
    align-items: center;
    font-size: 12px;
    color: #909399;
    
    .asset-sn {
      margin-right: 12px;
    }
    
    .asset-location {
      color: #67c23a;
    }
  }
}

.selected-asset {
  .asset-sn {
    font-size: 12px;
    color: #909399;
    margin-left: 8px;
  }
}

/* 备件选择样式 */
.spare-part-option {
  display: flex;
  flex-direction: column;
  padding: 4px 0;
  
  .spare-part-name {
    font-weight: 500;
    color: #303133;
    margin-bottom: 2px;
  }
  
  .spare-part-spec, .spare-part-brand {
    font-size: 12px;
    color: #909399;
    margin-right: 8px;
  }
}

/* 备件建议项样式 */
.spare-part-suggestion-item {
  display: flex;
  flex-direction: column;
  padding: 8px 0;

  .spare-part-main {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;

    .spare-part-name {
      font-weight: 500;
      color: #303133;
    }

    .spare-part-stock {
      font-size: 12px;
      color: #67C23A;
      background: #f0f9ff;
      padding: 2px 6px;
      border-radius: 4px;
    }
  }

  .spare-part-details {
    display: flex;
    gap: 12px;

    .spare-part-spec, .spare-part-brand {
      font-size: 12px;
      color: #909399;
    }
  }
}

/* 批量操作样式 */
.batch-operations {
  margin-bottom: 16px;

  .batch-info {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .batch-buttons {
      display: flex;
      gap: 8px;
    }
  }
}
</style>