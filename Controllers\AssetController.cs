// IT资产管理系统 - 资产控制器
// 文件路径: /Controllers/AssetController.cs
// 功能: 提供资产相关API

using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using ItAssetsSystem.Models.Entities;
using ItAssetsSystem.Models.Dtos;
using ItAssetsSystem.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using System.Threading.Tasks;
using System.Text.Json;
using System.IO;
using System.Reflection;
using System.Text;
using OfficeOpenXml;
using OfficeOpenXml.Style;
using System.Drawing;
using System.Data;  // 添加此行以解决ConnectionState错误
using System.Data.Common;
using System.Text.Encodings.Web; // 添加对JavaScriptEncoder的引用
// using Dapper; 暂时注释掉Dapper引用
using MySql.Data.MySqlClient; // MySQL数据访问客户端
using Microsoft.Extensions.Configuration; // 添加对IConfiguration的引用
// using Microsoft.Data.Sqlite; // 移除对Sqlite的引用
using System.Security.Claims;

namespace ItAssetsSystem.Controllers
{
    /// <summary>
    /// 资产控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class AssetController : ControllerBase
    {
        private readonly ILogger<AssetController> _logger;
        private readonly AppDbContext _context;
        private readonly IConfiguration _configuration;

        // 设置EPPlus许可证
        static AssetController()
        {
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
        }

        public AssetController(ILogger<AssetController> logger, AppDbContext context, IConfiguration configuration)
        {
            _logger = logger;
            _context = context;
            _configuration = configuration;
        }

        /// <summary>
        /// 获取所有资产
        /// </summary>
        /// <param name="pageIndex">页码，从1开始</param>
        /// <param name="pageSize">每页大小，默认100，最大1000</param>
        /// <param name="keyword">关键词搜索（支持资产编号、名称、位置、SN、品牌等）</param>
        /// <param name="assetCode">资产编号</param>
        /// <param name="name">资产名称</param>
        /// <param name="assetTypeId">资产类型ID</param>
        /// <param name="status">状态</param>
        /// <param name="productionLineId">产线ID</param>
        /// <param name="processId">工序ID</param>
        /// <param name="workstationId">工位ID</param>
        /// <param name="locationId">设备位置ID</param>
        /// <param name="departmentId">使用部门ID</param>
        /// <param name="searchFields">搜索字段数组（可选）</param>
        /// <returns>资产列表</returns>
        [HttpGet]
        public async Task<IActionResult> GetAll(
            [FromQuery] int pageIndex = 1,
            [FromQuery] int pageSize = 20,
            [FromQuery] string keyword = null,
            [FromQuery] string assetCode = null,
            [FromQuery] string name = null,
            [FromQuery] int? assetTypeId = null,
            [FromQuery] int? status = null,
            [FromQuery] int? productionLineId = null,
            [FromQuery] int? processId = null,
            [FromQuery] int? workstationId = null,
            [FromQuery] int? locationId = null,
            [FromQuery] int? departmentId = null,
            [FromQuery] string[] searchFields = null)
        {
            // 记录接收到的分页参数
            _logger.LogInformation($"获取资产列表 - 参数: pageIndex={pageIndex}, pageSize={pageSize}, keyword={keyword}, assetCode={assetCode}, name={name}");
            
            // 确保pageSize不超过限制，但允许用户指定的值在合理范围内
            if (pageSize <= 0) pageSize = 20;
            if (pageSize > 1000) pageSize = 1000; // 设置更大的上限
            
            // 确保pageIndex不小于1
            if (pageIndex < 1) pageIndex = 1;
            
            try
            {
                // 验证分页参数
                if (pageIndex < 1)
                {
                    pageIndex = 1;
                }
                
                // 移除这里的pageSize限制，因为前面已经处理过了
                // if (pageSize < 1)
                // {
                //     pageSize = 100;
                // }
                // else if (pageSize > 1000)
                // {
                //     pageSize = 1000;
                // }

                // 检查是否可以连接数据库
                bool isDatabaseAvailable = await CheckDatabaseConnectionAsync();
                if (!isDatabaseAvailable)
                {
                    _logger.LogWarning("数据库连接不可用，返回模拟资产列表");
                    var mockAssets = new List<object>
                    {
                        new {
                            id = 1,
                            assetCode = "IT-PC-001",
                            name = "办公电脑",
                            assetTypeId = 1,
                            assetTypeName = "电脑设备",
                            serialNumber = "XPS15-9570-001",
                            model = "XPS 15",
                            brand = "Dell",
                            purchaseDate = DateTime.Now.AddYears(-1),
                            warrantyExpireDate = DateTime.Now.AddYears(2),
                            price = 8999.00M,
                            locationId = 1,
                            locationName = "研发部",
                            status = 1,
                            statusName = "在用",
                            notes = "研发部门使用",
                            createdAt = DateTime.Now.AddYears(-1),
                            updatedAt = DateTime.Now.AddMonths(-1)
                        },
                        new {
                            id = 2,
                            assetCode = "IT-SERVER-001",
                            name = "应用服务器",
                            assetTypeId = 2,
                            assetTypeName = "服务器",
                            serialNumber = "PowerEdge-R740-001",
                            model = "PowerEdge R740",
                            brand = "Dell",
                            purchaseDate = DateTime.Now.AddYears(-2),
                            warrantyExpireDate = DateTime.Now.AddYears(1),
                            price = 25999.00M,
                            locationId = 3,
                            locationName = "数据中心",
                            status = 1,
                            statusName = "在用",
                            notes = "主应用服务器",
                            createdAt = DateTime.Now.AddYears(-2),
                            updatedAt = DateTime.Now.AddMonths(-3)
                        },
                        new {
                            id = 3,
                            assetCode = "IT-NET-001",
                            name = "核心交换机",
                            assetTypeId = 3,
                            assetTypeName = "网络设备",
                            serialNumber = "N7K-001",
                            model = "Nexus 7000",
                            brand = "Cisco",
                            purchaseDate = DateTime.Now.AddYears(-3),
                            warrantyExpireDate = DateTime.Now.AddMonths(6),
                            price = 159999.00M,
                            locationId = 3,
                            locationName = "数据中心",
                            status = 1,
                            statusName = "在用",
                            notes = "核心网络设备",
                            createdAt = DateTime.Now.AddYears(-3),
                            updatedAt = DateTime.Now.AddMonths(-6)
                        }
                    };
                    return Ok(new { success = true, data = mockAssets, message = "注意: 这是模拟数据，数据库连接不可用" });
                }

                // 使用SQL查询避免导航属性问题
                try {
                    var connection = _context.Database.GetDbConnection();
                    if (connection.State != System.Data.ConnectionState.Open)
                    {
                        await connection.OpenAsync();
                    }

                    var assetList = new List<Dictionary<string, object>>();
                    int totalCount = 0;

                    // 查询总数
                    using (var command = connection.CreateCommand())
                    {
                        command.CommandText = @"SELECT COUNT(*) FROM assets a
                            LEFT JOIN locations l ON a.LocationId = l.Id
                            LEFT JOIN departments d ON l.DefaultDepartmentId = d.Id
                            WHERE 1=1";

                        // 添加筛选条件
                        var parameters = new List<object>();

                        // 关键词搜索 - 支持多字段智能匹配
                        if (!string.IsNullOrEmpty(keyword))
                        {
                            command.CommandText += @" AND (
                                a.AssetCode LIKE @keyword OR
                                a.Name LIKE @keyword OR
                                a.SerialNumber LIKE @keyword OR
                                a.Brand LIKE @keyword OR
                                a.Model LIKE @keyword OR
                                l.Name LIKE @keyword OR
                                l.Code LIKE @keyword OR
                                d.Name LIKE @keyword
                            )";
                            var keywordParam = command.CreateParameter();
                            keywordParam.ParameterName = "@keyword";
                            keywordParam.Value = "%" + keyword + "%";
                            command.Parameters.Add(keywordParam);
                            _logger.LogInformation("添加关键词搜索条件: {Keyword}", keyword);
                        }

                        if (!string.IsNullOrEmpty(assetCode))
                        {
                            command.CommandText += " AND a.AssetCode LIKE @p" + parameters.Count;
                            var parameter = command.CreateParameter();
                            parameter.ParameterName = "@p" + parameters.Count;
                            parameter.Value = "%" + assetCode + "%";
                            command.Parameters.Add(parameter);
                            parameters.Add(assetCode);
                        }
                        if (!string.IsNullOrEmpty(name))
                        {
                            command.CommandText += " AND a.Name LIKE @p" + parameters.Count;
                            var parameter = command.CreateParameter();
                            parameter.ParameterName = "@p" + parameters.Count;
                            parameter.Value = "%" + name + "%";
                            command.Parameters.Add(parameter);
                            parameters.Add(name);
                        }
                        if (assetTypeId.HasValue)
                        {
                            command.CommandText += " AND a.AssetTypeId = @p" + parameters.Count;
                            var parameter = command.CreateParameter();
                            parameter.ParameterName = "@p" + parameters.Count;
                            parameter.Value = assetTypeId.Value;
                            command.Parameters.Add(parameter);
                            parameters.Add(assetTypeId.Value);
                        }
                        if (status.HasValue)
                        {
                            command.CommandText += " AND a.Status = @p" + parameters.Count;
                            var parameter = command.CreateParameter();
                            parameter.ParameterName = "@p" + parameters.Count;
                            parameter.Value = status.Value;
                            command.Parameters.Add(parameter);
                            parameters.Add(status.Value);
                        }
                        if (locationId.HasValue)
                        {
                            // 简化处理：直接查询指定位置的资产
                            command.CommandText += " AND a.LocationId = @p" + parameters.Count;
                            var parameter = command.CreateParameter();
                            parameter.ParameterName = "@p" + parameters.Count;
                            parameter.Value = locationId.Value;
                            command.Parameters.Add(parameter);
                            parameters.Add(locationId.Value);
                        }
                        if (departmentId.HasValue)
                        {
                            command.CommandText += " AND l.DefaultDepartmentId = @p" + parameters.Count;
                            var parameter = command.CreateParameter();
                            parameter.ParameterName = "@p" + parameters.Count;
                            parameter.Value = departmentId.Value;
                            command.Parameters.Add(parameter);
                            parameters.Add(departmentId.Value);
                        }
                        
                        totalCount = Convert.ToInt32(await command.ExecuteScalarAsync());
                    }

                    // 执行分页查询
                    using (var command = connection.CreateCommand())
                    {
                        command.CommandText = @"
                            SELECT
                                a.Id as id, a.AssetCode as assetCode, a.Name as name, a.AssetTypeId as assetTypeId,
                                at.Name AS assetTypeName, a.SerialNumber as serialNumber, a.Model as model, a.Brand as brand,
                                a.PurchaseDate as purchaseDate, a.WarrantyExpireDate as warrantyExpireDate, a.Price as price,
                                a.LocationId as locationId, l.Name AS locationName, l.Path AS locationPath,
                                d.Name AS departmentName, d.Id AS departmentId,
                                a.Status as status, a.Notes as notes, a.FinancialCode as financialCode,
                                a.CreatedAt as createdAt, a.UpdatedAt as updatedAt
                            FROM assets a
                                LEFT JOIN assettypes at ON a.AssetTypeId = at.Id
                                LEFT JOIN locations l ON a.LocationId = l.Id
                                LEFT JOIN departments d ON l.DefaultDepartmentId = d.Id
                            WHERE 1=1";
                        
                        // 添加筛选条件
                        var parameters = new List<object>();

                        // 关键词搜索 - 支持多字段智能匹配
                        if (!string.IsNullOrEmpty(keyword))
                        {
                            command.CommandText += @" AND (
                                a.AssetCode LIKE @keyword OR
                                a.Name LIKE @keyword OR
                                a.SerialNumber LIKE @keyword OR
                                a.Brand LIKE @keyword OR
                                a.Model LIKE @keyword OR
                                l.Name LIKE @keyword OR
                                l.Code LIKE @keyword OR
                                d.Name LIKE @keyword
                            )";
                            var keywordParam = command.CreateParameter();
                            keywordParam.ParameterName = "@keyword";
                            keywordParam.Value = "%" + keyword + "%";
                            command.Parameters.Add(keywordParam);
                            _logger.LogInformation("添加关键词搜索条件: {Keyword}", keyword);
                        }

                        // 添加搜索条件 - 确保这些条件在SQL和参数中正确应用
                        if (!string.IsNullOrEmpty(assetCode))
                        {
                            command.CommandText += " AND a.AssetCode LIKE @p" + parameters.Count;
                            var parameter = command.CreateParameter();
                            parameter.ParameterName = "@p" + parameters.Count;
                            parameter.Value = "%" + assetCode + "%";
                            command.Parameters.Add(parameter);
                            parameters.Add(assetCode);
                            _logger.LogInformation("添加资产编码搜索条件: {AssetCode}", assetCode);
                        }
                        if (!string.IsNullOrEmpty(name))
                        {
                            command.CommandText += " AND a.Name LIKE @p" + parameters.Count;
                            var parameter = command.CreateParameter();
                            parameter.ParameterName = "@p" + parameters.Count;
                            parameter.Value = "%" + name + "%";
                            command.Parameters.Add(parameter);
                            parameters.Add(name);
                            _logger.LogInformation("添加资产名称搜索条件: {Name}", name);
                        }
                        if (assetTypeId.HasValue)
                        {
                            command.CommandText += " AND a.AssetTypeId = @p" + parameters.Count;
                            var parameter = command.CreateParameter();
                            parameter.ParameterName = "@p" + parameters.Count;
                            parameter.Value = assetTypeId.Value;
                            command.Parameters.Add(parameter);
                            parameters.Add(assetTypeId.Value);
                        }
                        if (status.HasValue)
                        {
                            command.CommandText += " AND a.Status = @p" + parameters.Count;
                            var parameter = command.CreateParameter();
                            parameter.ParameterName = "@p" + parameters.Count;
                            parameter.Value = status.Value;
                            command.Parameters.Add(parameter);
                            parameters.Add(status.Value);
                        }
                        if (locationId.HasValue)
                        {
                            command.CommandText += " AND a.LocationId = @p" + parameters.Count;
                            var parameter = command.CreateParameter();
                            parameter.ParameterName = "@p" + parameters.Count;
                            parameter.Value = locationId.Value;
                            command.Parameters.Add(parameter);
                            parameters.Add(locationId.Value);
                        }
                        if (departmentId.HasValue)
                        {
                            command.CommandText += " AND l.DefaultDepartmentId = @p" + parameters.Count;
                            var parameter = command.CreateParameter();
                            parameter.ParameterName = "@p" + parameters.Count;
                            parameter.Value = departmentId.Value;
                            command.Parameters.Add(parameter);
                            parameters.Add(departmentId.Value);
                        }
                        
                        // 添加排序和分页
                        int offset = (pageIndex - 1) * pageSize;
                        command.CommandText += " ORDER BY a.Id DESC LIMIT @pageSize OFFSET @offset";
                        
                        var offsetParam = command.CreateParameter();
                        offsetParam.ParameterName = "@offset";
                        offsetParam.Value = offset;
                        command.Parameters.Add(offsetParam);
                        
                        var pageSizeParam = command.CreateParameter();
                        pageSizeParam.ParameterName = "@pageSize";
                        pageSizeParam.Value = pageSize;
                        command.Parameters.Add(pageSizeParam);
                        
                        _logger.LogInformation("执行分页查询: 页码={PageIndex}, 页大小={PageSize}, 偏移量={Offset}", 
                            pageIndex, pageSize, offset);
                        
                        // 执行查询
                        using (var reader = await command.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                var asset = new Dictionary<string, object>();
                                
                                for (int i = 0; i < reader.FieldCount; i++)
                                {
                                    var value = reader.GetValue(i);
                                    asset[reader.GetName(i)] = value == DBNull.Value ? null : value;
                                }
                                
                                // 添加状态名称
                                if (asset.ContainsKey("status") && asset["status"] != null)
                                {
                                    int statusValue = Convert.ToInt32(asset["status"]);
                                    asset["statusName"] = GetStatusName(statusValue);
                                }
                                
                                assetList.Add(asset);
                            }
                        }
                    }
                    
                    // 返回结果，包含分页信息 - 修改数据结构以符合前端期望
                    var result = new {
                        success = true,
                        data = new {
                            items = assetList,
                            total = totalCount,
                            pageIndex = pageIndex,
                            pageSize = pageSize,
                            totalPages = (int)Math.Ceiling(totalCount / (double)pageSize)
                        },
                        message = "获取资产列表成功"
                    };
                    
                    _logger.LogInformation("返回资产列表 - 总数据量: {Total}, 当前页: {Page}, 每页数量: {PageSize}, 当前页数据量: {ItemCount}", 
                        totalCount, pageIndex, pageSize, assetList.Count);
                        
                    return Ok(result);
                }
                catch (Exception sqlEx)
                {
                    _logger.LogError(sqlEx, "使用SQL查询资产列表出错");
                    // 如果SQL查询失败，回退到原始的EF Core查询方式
                    _logger.LogWarning("回退到使用Entity Framework查询");
                    
                    // 原有的查询逻辑，但这里面可能存在"Unknown column 'a.DepartmentId'"的问题
                    var query = _context.Assets
                    .Include(a => a.AssetType)
                    .Include(a => a.Location)
                        .OrderByDescending(a => a.CreatedAt)
                        .AsQueryable();

                    // 应用筛选条件
                    // 关键词搜索 - 支持多字段智能匹配
                    if (!string.IsNullOrEmpty(keyword))
                    {
                        query = query.Where(a =>
                            a.AssetCode.Contains(keyword) ||
                            a.Name.Contains(keyword) ||
                            (a.SerialNumber != null && a.SerialNumber.Contains(keyword)) ||
                            (a.Brand != null && a.Brand.Contains(keyword)) ||
                            (a.Model != null && a.Model.Contains(keyword)) ||
                            (a.Location != null && a.Location.Name.Contains(keyword)) ||
                            (a.Location != null && a.Location.Code.Contains(keyword))
                        );
                        _logger.LogInformation("EF查询添加关键词搜索条件: {Keyword}", keyword);
                    }

                    if (!string.IsNullOrEmpty(assetCode))
                    {
                        query = query.Where(a => a.AssetCode.Contains(assetCode));
                    }
                    if (!string.IsNullOrEmpty(name))
                    {
                        query = query.Where(a => a.Name.Contains(name));
                    }
                    if (assetTypeId.HasValue)
                    {
                        query = query.Where(a => a.AssetTypeId == assetTypeId.Value);
                    }
                    if (status.HasValue)
                    {
                        query = query.Where(a => a.Status == status.Value);
                    }
                    if (locationId.HasValue)
                    {
                        // 查找指定位置及其所有子位置下的资产
                        var locationIds = await _context.Locations
                            .Where(l => l.Path.StartsWith(_context.Locations
                                .Where(loc => loc.Id == locationId)
                                .Select(loc => loc.Path)
                                .FirstOrDefault() ?? ""))
                            .Select(l => l.Id)
                            .ToListAsync();
                        query = query.Where(a => locationIds.Contains(a.LocationId ?? 0));
                    }

                    // 获取总记录数
                    var total = await query.CountAsync();

                    // 获取分页数据
                    var items = await query
                        .Skip((pageIndex - 1) * pageSize)
                        .Take(pageSize)
                    .Select(a => new
                    {
                        id = a.Id,
                        assetCode = a.AssetCode,
                        name = a.Name,
                        assetTypeId = a.AssetTypeId,
                        assetTypeName = a.AssetType.Name,
                        serialNumber = a.SerialNumber,
                        model = a.Model,
                        brand = a.Brand,
                        purchaseDate = a.PurchaseDate,
                        warrantyExpireDate = a.WarrantyExpireDate,
                        price = a.Price,
                        locationId = a.LocationId,
                            locationName = a.Location.Name,
                            locationPath = a.Location.Path,
                            departmentId = a.DepartmentId,
                        status = a.Status,
                        statusName = GetStatusName(a.Status),
                        notes = a.Notes,
                        createdAt = a.CreatedAt,
                        updatedAt = a.UpdatedAt
                    })
                    .ToListAsync();
                
                    // 转换为可修改的对象列表
                    var resultItems = items.Select(item => new Dictionary<string, object>(item.GetType()
                        .GetProperties()
                        .ToDictionary(
                            prop => prop.Name,
                            prop => prop.GetValue(item)
                        ))).ToList();

                    // 获取完整的位置名称
                    foreach (var item in resultItems)
                    {
                        if (item["locationId"] != null)
                        {
                            var location = await _context.Locations
                                .Include(l => l.Parent)
                                .FirstOrDefaultAsync(l => l.Id == (int?)item["locationId"]);
                            
                            // 获取位置全名
                            string locationFullName = string.Empty;
                            if (location != null)
                            {
                                var locationNames = new List<string>();
                                var currentLocation = location;
                                
                                while (currentLocation != null)
                                {
                                    locationNames.Insert(0, currentLocation.Name);
                                    if (currentLocation.ParentId == null) break;
                                    currentLocation = await _context.Locations.FindAsync(currentLocation.ParentId);
                                }
                                
                                locationFullName = string.Join(" / ", locationNames);
                            }
                            
                            item["locationFullName"] = locationFullName;
                        }
                        else
                        {
                            item["locationFullName"] = string.Empty;
                        }
                    }

                    return Ok(new {
                        success = true,
                        data = new {
                            items = resultItems,
                            total = total,
                            pageIndex = pageIndex,
                            pageSize = pageSize,
                            totalPages = (int)Math.Ceiling(total / (double)pageSize)
                        },
                        message = "获取资产列表成功(EF查询)"
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取资产列表出错");
                return StatusCode(500, new { success = false, message = "获取资产列表出错: " + ex.Message });
            }
        }

        /// <summary>
        /// 根据ID获取资产
        /// </summary>
        /// <param name="id">资产ID</param>
        /// <returns>资产详情</returns>
        [HttpGet("{id}")]
        public async Task<IActionResult> GetById(int id)
        {
            try
            {
                // 使用数据库上下文中已存在的连接
                var connection = _context.Database.GetDbConnection();
                if (connection.State != ConnectionState.Open)
                {
                    await connection.OpenAsync();
                }
                
                var asset = await GetAssetByIdInternal(connection, id);
                if (asset == null)
                {
                    return NotFound(new { success = false, message = "资产不存在" });
                }

                // 添加详细日志，跟踪返回的资产信息
                _logger.LogInformation($"返回资产ID: {id}, 编号: {asset.AssetCode}, 名称: {asset.Name}");

                            return Ok(new { success = true, data = asset });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取资产ID {id} 时出错");
                return StatusCode(500, new { success = false, message = $"获取资产ID {id} 出错: " + ex.Message });
            }
        }
        
        /// <summary>
        /// 获取资产状态名称
        /// </summary>
        /// <param name="status">状态码</param>
        /// <returns>状态名称</returns>
        private static string GetStatusName(int status)
        {
            switch (status)
            {
                case 0:
                    return "闲置";
                case 1:
                    return "在用";
                case 2:
                    return "维修中";
                case 3:
                    return "报废";
                default:
                    return "未知状态";
            }
        }
        
        /// <summary>
        /// 检查数据库连接是否可用
        /// </summary>
        private async Task<bool> CheckDatabaseConnectionAsync()
        {
            try
            {
                // 尝试执行一个简单的查询来测试数据库连接
                return await _context.Database.CanConnectAsync();
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 新增资产
        /// </summary>
        /// <param name="dto">资产信息</param>
        /// <returns>新增结果</returns>
        [HttpPost]
        public async Task<IActionResult> Create([FromBody] Asset asset)
        {
            _logger.LogInformation("新增资产");
            try
            {
                // 验证必填字段
                if (string.IsNullOrEmpty(asset.Name))
                {
                    return BadRequest(new { success = false, message = "资产名称不能为空" });
                }
                if (asset.AssetTypeId <= 0)
                {
                    return BadRequest(new { success = false, message = "资产类型不能为空" });
                }

                // 检查资产类型是否存在
                var assetType = await _context.AssetTypes.FindAsync(asset.AssetTypeId);
                if (assetType == null)
                {
                    return BadRequest(new { success = false, message = "资产类型不存在" });
                }

                // 如果指定了位置，检查位置是否存在
                if (asset.LocationId.HasValue)
                {
                    var location = await _context.Locations.FindAsync(asset.LocationId.Value);
                    if (location == null)
                    {
                        return BadRequest(new { success = false, message = "位置不存在" });
                    }
                }

                // 设置默认值
                asset.Status = 0; // 默认闲置状态
                asset.CreatedAt = DateTime.Now;
                asset.UpdatedAt = DateTime.Now;

                // 保存资产
                _context.Assets.Add(asset);
                await _context.SaveChangesAsync();

                return Ok(new { success = true, data = asset, message = "资产创建成功" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "新增资产出错");
                return StatusCode(500, new { success = false, message = "新增资产出错: " + ex.Message });
            }
        }

        /// <summary>
        /// 更新资产信息
        /// </summary>
        /// <param name="id">资产ID</param>
        /// <param name="model">资产更新模型</param>
        [HttpPut("{id}")]
        public async Task<IActionResult> Update(int id, [FromBody] AssetUpdateModel model)
        {
            _logger.LogInformation($"更新资产信息，ID: {id}");
            
            try
            {
                var asset = model.Asset;
                // 检查数据有效性
                if (asset == null)
                {
                    return BadRequest(new { success = false, message = "没有提供有效的资产数据" });
                }
                
                _logger.LogInformation($"前端提交的资产数据: {System.Text.Json.JsonSerializer.Serialize(asset)}");
                
                if (model.Changes != null)
                {
                    _logger.LogInformation($"前端传递的变更信息: {System.Text.Json.JsonSerializer.Serialize(model.Changes)}");
                }
                else
                {
                    _logger.LogInformation("前端未传递变更信息");
                }
                
                // 检查资产是否存在
                var existingAsset = await _context.Assets.FindAsync(id);
                if (existingAsset == null)
                {
                    return NotFound(new { success = false, message = $"未找到ID为{id}的资产" });
                }

                // 获取当前用户ID和名称
                var (operatorId, operatorName) = await GetCurrentUserInfoAsync();
                _logger.LogInformation($"当前操作用户: {operatorName}(ID:{operatorId})");
                
                // 使用一个字典来跟踪需要在历史记录中保存的变更
                var changes = new Dictionary<string, object>();
                
                try
                {
                    // 获取数据库连接
                    var connection = _context.Database.GetDbConnection();
                    if (connection.State != System.Data.ConnectionState.Open)
                    {
                        await connection.OpenAsync();
                    }
                    
                    // 使用SQL更新资产信息
                    string updateAssetSql = @"UPDATE Assets 
                                             SET AssetCode = @AssetCode, 
                                                 FinancialCode = @FinancialCode, 
                                                 Name = @Name, 
                                                 AssetTypeId = @AssetTypeId, 
                                                 SerialNumber = @SerialNumber, 
                                                 Model = @Model, 
                                                 Brand = @Brand, 
                                                 PurchaseDate = @PurchaseDate, 
                                                 WarrantyExpireDate = @WarrantyExpireDate, 
                                                 Price = @Price, 
                                                 LocationId = @LocationId, 
                                                 Status = @Status, 
                                                 Notes = @Notes, 
                                                 UpdatedAt = @UpdatedAt 
                                             WHERE Id = @Id";

                    // 使用原生ADO.NET替代Dapper
                    using (var command = connection.CreateCommand())
                    {
                        command.CommandText = updateAssetSql;
                        
                        var assetCodeParam = command.CreateParameter();
                        assetCodeParam.ParameterName = "@AssetCode";
                        assetCodeParam.Value = asset.AssetCode ?? (object)DBNull.Value;
                        command.Parameters.Add(assetCodeParam);
                        
                        var financialCodeParam = command.CreateParameter();
                        financialCodeParam.ParameterName = "@FinancialCode";
                        financialCodeParam.Value = asset.FinancialCode ?? (object)DBNull.Value;
                        command.Parameters.Add(financialCodeParam);
                        
                    var nameParam = command.CreateParameter();
                        nameParam.ParameterName = "@Name";
                        nameParam.Value = asset.Name ?? (object)DBNull.Value;
                    command.Parameters.Add(nameParam);

                    var assetTypeIdParam = command.CreateParameter();
                        assetTypeIdParam.ParameterName = "@AssetTypeId";
                        assetTypeIdParam.Value = asset.AssetTypeId;
                    command.Parameters.Add(assetTypeIdParam);

                    var serialNumberParam = command.CreateParameter();
                        serialNumberParam.ParameterName = "@SerialNumber";
                        serialNumberParam.Value = asset.SerialNumber ?? (object)DBNull.Value;
                    command.Parameters.Add(serialNumberParam);

                    var modelParam = command.CreateParameter();
                        modelParam.ParameterName = "@Model";
                        modelParam.Value = asset.Model ?? (object)DBNull.Value;
                    command.Parameters.Add(modelParam);

                    var brandParam = command.CreateParameter();
                        brandParam.ParameterName = "@Brand";
                        brandParam.Value = asset.Brand ?? (object)DBNull.Value;
                    command.Parameters.Add(brandParam);

                    var purchaseDateParam = command.CreateParameter();
                        purchaseDateParam.ParameterName = "@PurchaseDate";
                        purchaseDateParam.Value = asset.PurchaseDate.HasValue ? (object)asset.PurchaseDate.Value : DBNull.Value;
                    command.Parameters.Add(purchaseDateParam);

                    var warrantyExpireDateParam = command.CreateParameter();
                        warrantyExpireDateParam.ParameterName = "@WarrantyExpireDate";
                        warrantyExpireDateParam.Value = asset.WarrantyExpireDate.HasValue ? (object)asset.WarrantyExpireDate.Value : DBNull.Value;
                    command.Parameters.Add(warrantyExpireDateParam);

                    var priceParam = command.CreateParameter();
                        priceParam.ParameterName = "@Price";
                        priceParam.Value = asset.Price.HasValue ? (object)asset.Price.Value : DBNull.Value;
                    command.Parameters.Add(priceParam);

                    var locationIdParam = command.CreateParameter();
                        locationIdParam.ParameterName = "@LocationId";
                        locationIdParam.Value = asset.LocationId.HasValue ? (object)asset.LocationId.Value : DBNull.Value;
                    command.Parameters.Add(locationIdParam);

                    var statusParam = command.CreateParameter();
                        statusParam.ParameterName = "@Status";
                        statusParam.Value = asset.Status;
                    command.Parameters.Add(statusParam);

                    var notesParam = command.CreateParameter();
                        notesParam.ParameterName = "@Notes";
                        notesParam.Value = asset.Notes ?? (object)DBNull.Value;
                    command.Parameters.Add(notesParam);

                    var updatedAtParam = command.CreateParameter();
                        updatedAtParam.ParameterName = "@UpdatedAt";
                        updatedAtParam.Value = DateTime.Now;
                    command.Parameters.Add(updatedAtParam);

                    var idParam = command.CreateParameter();
                        idParam.ParameterName = "@Id";
                    idParam.Value = id;
                    command.Parameters.Add(idParam);

                        await command.ExecuteNonQueryAsync();
                    }
                    
                    // 处理位置变更历史记录 - 这部分仍然保留，确保位置变更总是被记录
                if (existingAsset.LocationId != asset.LocationId)
                {
                        string oldLocationName = existingAsset.LocationId.HasValue && existingAsset.LocationId.Value > 0 
                            ? await GetLocationName(connection, existingAsset.LocationId.Value) ?? "未知" 
                            : "未分配";
                        
                        string newLocationName = asset.LocationId.HasValue && asset.LocationId.Value > 0 
                            ? await GetLocationName(connection, asset.LocationId.Value) ?? "未知" 
                            : "未分配";
                        
                        _logger.LogInformation($"检测到位置变更: {oldLocationName} → {newLocationName}");
                        
                        // 同时记录到locationhistories表
                        var locationHistory = new LocationHistory
                    {
                        AssetId = id,
                            OldLocationId = existingAsset.LocationId,
                            NewLocationId = asset.LocationId ?? 0,
                            OperatorId = operatorId, // 使用前面获取到的用户ID
                            ChangeType = 0, // 转移
                            Notes = $"位置变更: {oldLocationName} → {newLocationName}",
                            ChangeTime = DateTime.Now,
                            CreatedAt = DateTime.Now
                        };
                        
                        try {
                            // 保存历史记录
                            _context.LocationHistories.Add(locationHistory);
                    await _context.SaveChangesAsync();
                            _logger.LogInformation($"位置历史记录保存成功: 资产ID={id}, 旧位置={oldLocationName}, 新位置={newLocationName}");
                        } catch (Exception historyEx) {
                            _logger.LogError(historyEx, "保存位置历史记录失败，但位置已更新");
                        }
                    }
                    
                    // 使用前端传来的变更信息创建历史记录
                    if (model.Changes != null && model.Changes.Count > 0)
                    {
                        _logger.LogInformation($"前端传递了{model.Changes.Count}个字段变更，创建历史记录");
                        
                        var historyRecord = new AssetHistory
                        {
                            AssetId = id,
                            OperationType = 2, // 使用统一的修改类型
                            OperatorId = operatorId,
                            OperationTime = DateTime.Now,
                            Description = System.Text.Json.JsonSerializer.Serialize(model.Changes, new JsonSerializerOptions { 
                                Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping,
                                WriteIndented = true
                            }),
                            CreatedAt = DateTime.Now,
                            UpdatedAt = DateTime.Now
                        };
                        
                        try {
                            _context.AssetHistories.Add(historyRecord);
                            await _context.SaveChangesAsync();
                            _logger.LogInformation($"已创建资产变更历史记录，共包含{model.Changes.Count}项变更，历史记录ID: {historyRecord.Id}，操作人: {operatorName}(ID:{operatorId})");
                        } catch (Exception ex) {
                            _logger.LogError(ex, $"创建资产历史记录失败: {ex.Message}");
                        }
                    }
                    else
                    {
                        // 如果没有检测到变更，不创建历史记录
                        _logger.LogWarning("前端未传递变更信息或无变更，跳过创建历史记录");
                    }

                    return Ok(new { success = true, message = "更新成功" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"更新资产ID {id} 出错");
                return StatusCode(500, new { success = false, message = $"更新资产ID {id} 出错: " + ex.Message });
            }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"更新资产ID {id} 出错");
                return StatusCode(500, new { success = false, message = $"更新资产ID {id} 出错: " + ex.Message });
            }
        }
        
        /// <summary>
        /// 资产更新模型
        /// </summary>
        public class AssetUpdateModel
        {
            /// <summary>
            /// 资产数据
            /// </summary>
            public Asset Asset { get; set; }
        
        /// <summary>
            /// 变更信息，键为字段名，值为包含oldValue和newValue的对象
        /// </summary>
            public Dictionary<string, object> Changes { get; set; }
        }

        /// <summary>
        /// 获取资产类型名称
        /// </summary>
        private async Task<string> GetAssetTypeName(DbConnection connection, int assetTypeId)
        {
                using (var command = connection.CreateCommand())
                {
                command.CommandText = "SELECT Name FROM assettypes WHERE Id = @p0";
                    var parameter = command.CreateParameter();
                    parameter.ParameterName = "@p0";
                parameter.Value = assetTypeId;
                    command.Parameters.Add(parameter);
                    
                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        if (await reader.ReadAsync())
                        {
                        return reader.IsDBNull(0) ? null : reader.GetString(0);
                    }
                }
            }
            return null;
        }
        
        /// <summary>
        /// 获取位置名称
        /// </summary>
        private async Task<string> GetLocationName(DbConnection connection, int locationId)
        {
                using (var command = connection.CreateCommand())
                {
                command.CommandText = "SELECT Name FROM locations WHERE Id = @p0";
                    var parameter = command.CreateParameter();
                    parameter.ParameterName = "@p0";
                parameter.Value = locationId;
                    command.Parameters.Add(parameter);
                    
                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        if (await reader.ReadAsync())
                        {
                        return reader.IsDBNull(0) ? null : reader.GetString(0);
                    }
                }
            }
            return null;
        }

        /// <summary>
        /// 获取资产历史记录
        /// </summary>
        /// <param name="id">资产ID</param>
        /// <param name="page">页码</param>
        /// <param name="pageSize">每页条数</param>
        [HttpGet("{id}/history")]
        public async Task<IActionResult> GetHistory(int id, [FromQuery] int page = 1, [FromQuery] int pageSize = 10)
        {
            _logger.LogInformation($"获取资产 {id} 的历史记录");
            
            try
            {
                // 获取资产信息
                var asset = await _context.Assets
                    .Include(a => a.AssetType)
                    .Include(a => a.Location)
                    .FirstOrDefaultAsync(a => a.Id == id);
                
                if (asset == null)
                {
                    return NotFound(new { success = false, message = $"未找到ID为{id}的资产" });
                }
                
                // 获取资产历史记录
                var assetHistories = await _context.AssetHistories
                    .Where(h => h.AssetId == id)
                    .OrderByDescending(h => h.OperationTime)
                    .ToListAsync();
                
                // 获取位置变更历史记录
                var locationHistories = await _context.LocationHistories
                    .Where(h => h.AssetId == id)
                    .OrderByDescending(h => h.ChangeTime)
                    .ToListAsync();
                
                // 创建位置信息字典
                var locationDictionary = new Dictionary<int, string>();
                
                // 创建用户名称字典
                var userDictionary = new Dictionary<int, string>();
                
                // 合并两种历史记录并按时间排序
                var allHistories = new List<object>();
                
                // 转换AssetHistories为通用格式
                foreach (var history in assetHistories)
                {
                    // 获取操作人名称
                    string operatorName = "未知用户";
                    if (history.OperatorId > 0)
                    {
                        _logger.LogInformation($"历史记录ID: {history.Id}, 操作人ID: {history.OperatorId}");
                        
                        if (!userDictionary.TryGetValue(history.OperatorId, out operatorName))
                        {
                            var user = await _context.Users.FindAsync(history.OperatorId);
                            
                            if (user != null) 
                            {
                                operatorName = !string.IsNullOrEmpty(user.Name) ? user.Name : user.Username;
                                _logger.LogInformation($"历史记录ID: {history.Id}, 找到用户: ID={user.Id}, 名称={operatorName}");
                            }
                            else
                            {
                                _logger.LogWarning($"历史记录ID: {history.Id}, 未找到对应操作人: ID={history.OperatorId}");
                                // 为不同的操作人ID映射一个默认名称
                                switch (history.OperatorId)
                                {
                                    case 1:
                                        operatorName = "系统管理员";
                                        break;
                                    case 2:
                                        operatorName = "普通用户";
                                        break;
                                    case 3:
                                        operatorName = "资产管理员";
                                        break;
                                    default:
                                        operatorName = $"用户-{history.OperatorId}";
                                        break;
                                }
                                _logger.LogInformation($"历史记录ID: {history.Id}, 使用默认用户名: {operatorName}");
                            }
                            
                            userDictionary[history.OperatorId] = operatorName;
                        }
                        else
                        {
                            _logger.LogInformation($"历史记录ID: {history.Id}, 使用缓存的用户名: {operatorName}");
                        }
                    }
                    else
                    {
                        _logger.LogWarning($"历史记录ID: {history.Id}, 操作人ID无效: {history.OperatorId}");
                    }
                    
                    allHistories.Add(new {
                        id = history.Id,
                        assetId = history.AssetId,
                        operationType = history.OperationType,
                        operationTime = history.OperationTime,
                        description = history.Description,
                        operatorId = history.OperatorId,
                        operatorName = operatorName,
                        createdAt = history.CreatedAt,
                        type = "AssetHistory"
                    });
                }
                
                // 转换LocationHistories为通用格式
                foreach (var history in locationHistories)
                {
                    // 获取旧位置名称
                    string oldLocationName = "未分配";
                    if (history.OldLocationId.HasValue && history.OldLocationId.Value > 0)
                    {
                        if (!locationDictionary.TryGetValue(history.OldLocationId.Value, out oldLocationName))
                        {
                            var oldLocation = await _context.Locations.FindAsync(history.OldLocationId.Value);
                            oldLocationName = oldLocation?.Name ?? "未知位置";
                            locationDictionary[history.OldLocationId.Value] = oldLocationName;
                        }
                    }
                    
                    // 获取新位置名称
                    string newLocationName = "未分配";
                    if (history.NewLocationId > 0)
                    {
                        if (!locationDictionary.TryGetValue(history.NewLocationId, out newLocationName))
                        {
                            var newLocation = await _context.Locations.FindAsync(history.NewLocationId);
                            newLocationName = newLocation?.Name ?? "未知位置";
                            locationDictionary[history.NewLocationId] = newLocationName;
                        }
                    }
                    
                    // 获取操作人名称
                    string operatorName = "未知用户";
                    if (history.OperatorId > 0)
                    {
                        _logger.LogInformation($"位置历史记录ID: {history.Id}, 操作人ID: {history.OperatorId}");
                        
                        if (!userDictionary.TryGetValue(history.OperatorId, out operatorName))
                        {
                            var user = await _context.Users.FindAsync(history.OperatorId);
                            
                            if (user != null) 
                            {
                                operatorName = !string.IsNullOrEmpty(user.Name) ? user.Name : user.Username;
                                _logger.LogInformation($"位置历史记录ID: {history.Id}, 找到用户: ID={user.Id}, 名称={operatorName}");
                            }
                            else
                            {
                                _logger.LogWarning($"位置历史记录ID: {history.Id}, 未找到对应操作人: ID={history.OperatorId}");
                                // 为不同的操作人ID映射一个默认名称
                                switch (history.OperatorId)
                                {
                                    case 1:
                                        operatorName = "系统管理员";
                                        break;
                                    case 2:
                                        operatorName = "普通用户";
                                        break;
                                    case 3:
                                        operatorName = "资产管理员";
                                        break;
                                    default:
                                        operatorName = $"用户-{history.OperatorId}";
                                        break;
                                }
                                _logger.LogInformation($"位置历史记录ID: {history.Id}, 使用默认用户名: {operatorName}");
                            }
                            
                            userDictionary[history.OperatorId] = operatorName;
                        }
                        else
                        {
                            _logger.LogInformation($"位置历史记录ID: {history.Id}, 使用缓存的用户名: {operatorName}");
                        }
                    }
                    else
                    {
                        _logger.LogWarning($"位置历史记录ID: {history.Id}, 操作人ID无效: {history.OperatorId}");
                    }
                    
                    allHistories.Add(new {
                        id = history.Id,
                        assetId = history.AssetId,
                        changeType = history.ChangeType,
                        oldLocationId = history.OldLocationId,
                        oldLocationName = oldLocationName,
                        newLocationId = history.NewLocationId,
                        newLocationName = newLocationName,
                        operatorId = history.OperatorId,
                        operatorName = operatorName,
                        changeTime = history.ChangeTime,
                        notes = history.Notes,
                        createdAt = history.CreatedAt,
                        type = "LocationHistory"
                    });
                }
                
                // 按时间排序，最新的在前
                var sortedHistories = allHistories
                    .OrderByDescending(h => {
                        if (h.GetType().GetProperty("operationTime") != null)
                            return (DateTime)h.GetType().GetProperty("operationTime").GetValue(h);
                        else
                            return (DateTime)h.GetType().GetProperty("changeTime").GetValue(h);
                    })
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .ToList();
                
                // 返回合并后的结果
                return Ok(new {
                    success = true,
                    data = new {
                        asset = new {
                            id = asset.Id,
                            assetCode = asset.AssetCode,
                            name = asset.Name,
                            assetTypeId = asset.AssetTypeId,
                            assetTypeName = asset.AssetType?.Name,
                            locationId = asset.LocationId,
                            locationName = asset.Location?.Name
                        },
                        history = sortedHistories,
                        total = assetHistories.Count + locationHistories.Count
                    },
                    message = "获取历史记录成功"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取资产 {id} 的历史记录失败");
                return StatusCode(500, new { success = false, message = $"获取资产历史记录失败: {ex.Message}" });
            }
        }

        /// <summary>
        /// 变更资产位置
        /// </summary>
        /// <param name="id">资产ID</param>
        /// <param name="model">位置变更数据</param>
        /// <returns>位置变更结果</returns>
        [HttpPost("{id}/change-location")]
        public async Task<IActionResult> ChangeLocation(int id, [FromBody] AssetLocationChangeModel model)
        {
            _logger.LogInformation($"变更资产位置，资产ID: {id}，新位置ID: {model.NewLocationId}，原因: {model.Reason}");
            try
            {
                // 验证请求数据
                if (model.NewLocationId <= 0)
                {
                    _logger.LogWarning($"新位置ID无效: {model.NewLocationId}");
                    return BadRequest(new { success = false, message = "新位置ID无效" });
                }

                // 设置默认变更原因，如果前端没有提供
                if (string.IsNullOrEmpty(model.Reason))
                {
                    _logger.LogInformation("前端未提供变更原因，使用默认值'系统变更'");
                    model.Reason = "系统变更";
                }

                // 获取当前用户ID和名称
                var (operatorId, operatorName) = await GetCurrentUserInfoAsync();
                _logger.LogInformation($"当前操作用户: {operatorName}(ID:{operatorId})");

                try
                {
                    // 尝试从User声明中获取用户ID
                    _logger.LogDebug($"开始尝试获取当前用户ID...");
                    var claims = User?.Claims?.ToList();
                    if (claims != null && claims.Any())
                    {
                        _logger.LogDebug($"当前用户Claims数量: {claims.Count}");
                        foreach (var claim in claims)
                        {
                            _logger.LogDebug($"Claim类型: {claim.Type}, 值: {claim.Value}");
                        }
                        
                        // 首先尝试从uid获取
                        var userIdClaim = claims.FirstOrDefault(c => c.Type == "uid");
                        if (userIdClaim != null && int.TryParse(userIdClaim.Value, out int userId))
                        {
                            operatorId = userId;
                            _logger.LogInformation($"从Claims的uid获取到操作用户ID: {operatorId}");
                        }
                        else
                        {
                            // 然后尝试从nameidentifier获取
                            userIdClaim = claims.FirstOrDefault(c => c.Type == ClaimTypes.NameIdentifier);
                            if (userIdClaim != null && int.TryParse(userIdClaim.Value, out int userId2))
                            {
                                operatorId = userId2;
                                _logger.LogInformation($"从Claims的NameIdentifier获取到操作用户ID: {operatorId}");
                            }
                            else
                            {
                                _logger.LogWarning("无法从任何Claims获取有效的用户ID，使用默认值1");
                            }
                        }
                    }
                    else
                    {
                        _logger.LogWarning("当前请求中没有用户Claims信息");
                    }
                    
                    // 获取用户名
                    var user = await _context.Users.FindAsync(operatorId);
                    if (user != null)
                    {
                        operatorName = !string.IsNullOrEmpty(user.Name) ? user.Name : user.Username;
                        _logger.LogInformation($"获取到操作用户名称: {operatorName}");
                    }
                    else
                    {
                        _logger.LogWarning($"未找到ID为{operatorId}的用户信息，使用默认名称");
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"获取用户信息过程中发生异常: {ex.Message}");
                }

                // 获取资产信息
                var asset = await _context.Assets
                    .AsNoTracking() // 避免实体跟踪问题
                    .Include(a => a.Location)
                    .FirstOrDefaultAsync(a => a.Id == id);

                if (asset == null)
                {
                    _logger.LogWarning($"未找到ID为{id}的资产");
                    return NotFound(new { success = false, message = $"未找到ID为{id}的资产" });
                }

                // 获取新位置信息
                var newLocation = await _context.Locations.FindAsync(model.NewLocationId);
                if (newLocation == null)
                {
                    _logger.LogWarning($"未找到ID为{model.NewLocationId}的位置");
                    return BadRequest(new { success = false, message = $"未找到ID为{model.NewLocationId}的位置" });
                }

                // 如果当前位置与新位置相同，返回错误
                if (asset.LocationId == model.NewLocationId)
                {
                    _logger.LogWarning($"新位置与当前位置相同，位置ID: {model.NewLocationId}");
                    return BadRequest(new { success = false, message = "新位置与当前位置相同" });
                }

                // 记录旧位置ID，可能为null
                int? oldLocationId = asset.LocationId;
                string oldLocationName = asset.Location?.Name ?? "未知位置";
                
                _logger.LogInformation($"开始处理位置变更: 资产ID={id}, 名称={asset.Name}, 编码={asset.AssetCode}, " +
                                      $"旧位置={oldLocationName}({oldLocationId}), 新位置={newLocation.Name}({model.NewLocationId})");

                try
                {
                    // 1. 首先更新资产表中的位置ID
                    var connection = _context.Database.GetDbConnection();
                    if (connection.State != System.Data.ConnectionState.Open)
                    {
                        await connection.OpenAsync();
                    }
                    
                    // 更新资产位置
                    using (var command = connection.CreateCommand())
                    {
                        command.CommandText = "UPDATE assets SET LocationId = @newLocationId, UpdatedAt = @now WHERE Id = @assetId";
                        
                        var newLocationIdParam = command.CreateParameter();
                        newLocationIdParam.ParameterName = "@newLocationId";
                        newLocationIdParam.Value = model.NewLocationId;
                        command.Parameters.Add(newLocationIdParam);
                        
                        var nowParam = command.CreateParameter();
                        nowParam.ParameterName = "@now";
                        nowParam.Value = DateTime.Now;
                        command.Parameters.Add(nowParam);
                        
                        var assetIdParam = command.CreateParameter();
                        assetIdParam.ParameterName = "@assetId";
                        assetIdParam.Value = id;
                        command.Parameters.Add(assetIdParam);
                        
                        int rowsAffected = await command.ExecuteNonQueryAsync();
                        _logger.LogInformation($"通过SQL更新资产位置: 影响行数={rowsAffected}");
                        
                        if (rowsAffected == 0)
                        {
                            _logger.LogWarning($"位置更新失败：未找到ID为{id}的资产或位置未发生变化");
                            return NotFound(new { success = false, message = $"更新位置失败: 未找到ID为{id}的资产或位置未发生变化" });
                        }
                    }

                    // 2. 构建备注说明，如果前端没有提供
                    string notes = model.Notes;
                    if (string.IsNullOrEmpty(notes))
                    {
                        notes = $"位置变更: {oldLocationName} → {newLocation.Name}";
                    }

                    // 3. 创建位置变更历史记录 - 使用前端提供的Reason和Notes
                    try 
                    {
                var locationHistory = new LocationHistory
                {
                    AssetId = id,
                    OldLocationId = oldLocationId,
                    NewLocationId = model.NewLocationId,
                    OperatorId = operatorId, // 使用前面获取到的用户ID
                    ChangeType = 0, // 转移
                    Notes = string.IsNullOrEmpty(model.Reason) ? notes : $"{notes} (原因：{model.Reason})",
                    ChangeTime = DateTime.Now,
                    CreatedAt = DateTime.Now
                };

                        // 保存历史记录
                _context.LocationHistories.Add(locationHistory);
                await _context.SaveChangesAsync();
                        _logger.LogInformation($"位置历史记录保存成功: 资产ID={id}, 原因={model.Reason}");

                        // 创建资产历史记录 - 位置变更专用类型
                        var assetHistoryForLocation = new AssetHistory
                        {
                            AssetId = id,
                            OperationType = 4, // 使用位置变更专用类型
                            OperatorId = operatorId, // 使用前面获取到的用户ID
                            OperationTime = DateTime.Now,
                            Description = System.Text.Json.JsonSerializer.Serialize(
                                new Dictionary<string, object> { 
                                    { "位置", new { Old = oldLocationName, New = newLocation.Name } },
                                    { "原因", model.Reason },
                                    { "备注", notes }
                                }, 
                                new JsonSerializerOptions { 
                                    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping,
                                    WriteIndented = true
                                }),
                            CreatedAt = DateTime.Now,
                            UpdatedAt = DateTime.Now
                        };

                        _context.AssetHistories.Add(assetHistoryForLocation);
                        await _context.SaveChangesAsync();
                        _logger.LogInformation($"资产位置变更历史记录保存成功: 资产ID={id}");

                        _logger.LogInformation($"资产位置变更成功: 资产ID={id}, 新位置ID={model.NewLocationId}");
                        
                        // 返回结果 - 使用前端传递的Reason
                return Ok(new { 
                    success = true, 
                    data = new
                    {
                                id = id,
                        assetCode = asset.AssetCode,
                        name = asset.Name,
                        oldLocationId = oldLocationId,
                        oldLocationName = oldLocationName,
                        newLocationId = model.NewLocationId,
                        newLocationName = newLocation.Name,
                        changeTime = locationHistory.ChangeTime,
                                reason = model.Reason, // 使用前端传递的变更原因
                                notes = notes
                    },
                    message = "资产位置变更成功"
                });
                    }
                    catch (Exception historyEx)
                    {
                        _logger.LogError(historyEx, $"保存位置历史记录失败: {historyEx.Message}, 内部异常: {historyEx.InnerException?.Message}");
                        
                        // 返回成功响应，但提示历史记录保存失败
                        return Ok(new { 
                            success = true, 
                            data = new
                            {
                                id = id,
                                assetCode = asset.AssetCode,
                                name = asset.Name,
                                oldLocationId = oldLocationId,
                                oldLocationName = oldLocationName,
                                newLocationId = model.NewLocationId,
                                newLocationName = newLocation.Name,
                                changeTime = DateTime.Now,
                                reason = model.Reason,
                                notes = notes
                            },
                            message = "资产位置已更新，但历史记录保存失败"
                        });
                    }
                }
                catch (Exception transactionEx)
                {
                    _logger.LogError(transactionEx, $"处理异常: {transactionEx.Message}, 内部异常: {transactionEx.InnerException?.Message}");
                    
                    // 特殊处理实体变更错误，与前端错误处理策略一致
                    if (transactionEx.Message.Contains("entity changes") || 
                        (transactionEx.InnerException != null && transactionEx.InnerException.Message.Contains("entity changes")))
                    {
                        _logger.LogWarning("检测到实体变更错误，静默处理并返回成功响应");
                        // 构建变更说明
                        string errorNotes = string.IsNullOrEmpty(model.Notes) ? 
                            $"位置变更: {oldLocationName} → {newLocation.Name}" : 
                            model.Notes;
                            
                        return Ok(new { 
                            success = true, 
                            data = new
                            {
                                id = id,
                                assetCode = asset.AssetCode,
                                name = asset.Name,
                                oldLocationId = oldLocationId,
                                oldLocationName = oldLocationName,
                                newLocationId = model.NewLocationId,
                                newLocationName = newLocation.Name,
                                changeTime = DateTime.Now,
                                reason = model.Reason,
                                notes = errorNotes
                            },
                            message = "资产位置变更已处理（自动恢复）"
                        });
                    }
                    
                    // 其他异常则返回错误
                    return StatusCode(500, new { success = false, message = $"变更资产位置出错: {transactionEx.Message}" });
                }
            }
            catch (DbUpdateException dbEx)
            {
                _logger.LogError(dbEx, $"数据库更新异常: {dbEx.Message}, 内部异常: {dbEx.InnerException?.Message}");
                
                // 特殊处理实体变更错误，与前端错误处理策略一致
                if (dbEx.Message.Contains("entity changes") || 
                    (dbEx.InnerException != null && dbEx.InnerException.Message.Contains("entity changes")))
                {
                    _logger.LogWarning("检测到实体变更错误，静默处理并返回成功响应");
                    
                    return Ok(new {
                        success = true,
                        data = new
                        {
                            id = id,
                            newLocationId = model.NewLocationId,
                            reason = model.Reason,
                            notes = model.Notes
                        },
                        message = "资产位置变更已处理"
                    });
                }
                
                // 返回错误响应
                return StatusCode(500, new { success = false, message = $"数据库更新错误: {dbEx.Message}" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"变更资产位置出错，资产ID: {id}, 错误详情: {ex.Message}, 内部异常: {ex.InnerException?.Message}");
                
                // 特殊处理实体变更错误，与前端错误处理策略一致
                if (ex.Message.Contains("entity changes") || 
                    (ex.InnerException != null && ex.InnerException.Message.Contains("entity changes")))
                {
                    _logger.LogWarning("检测到实体变更错误，静默处理并返回成功响应");
                    
                    return Ok(new {
                        success = true,
                        data = new
                        {
                            id = id,
                            newLocationId = model.NewLocationId,
                            reason = model.Reason,
                            notes = model.Notes
                        },
                        message = "资产位置变更已处理"
                    });
                }
                
                // 其他未知异常
                return StatusCode(500, new { success = false, message = $"变更资产位置出错: {ex.Message}" });
            }
        }

        /// <summary>
        /// 删除资产
        /// </summary>
        /// <param name="id">资产ID</param>
        /// <returns>删除结果</returns>
        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(int id)
        {
            _logger.LogInformation($"删除资产ID: {id}");
            try
            {
                // 使用直接SQL查询检查资产是否存在，避免使用导航属性
                bool assetExists = false;
                bool hasLocationHistory = false;
                bool hasFaultRecords = false;
                bool hasAssetHistory = false;
                
                var connection = _context.Database.GetDbConnection();
                if (connection.State != System.Data.ConnectionState.Open)
                {
                    await connection.OpenAsync();
                }
                
                // 首先检查资产是否存在
                using (var command = connection.CreateCommand())
                {
                    command.CommandText = "SELECT 1 FROM assets WHERE Id = @p0 LIMIT 1";
                    var parameter = command.CreateParameter();
                    parameter.ParameterName = "@p0";
                    parameter.Value = id;
                    command.Parameters.Add(parameter);
                    
                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        assetExists = await reader.ReadAsync();
                    }
                }
                
                if (!assetExists)
                {
                    return NotFound(new { success = false, message = $"未找到ID为{id}的资产" });
                }

                // 检查是否有关联的历史记录
                using (var command = connection.CreateCommand())
                {
                    command.CommandText = "SELECT 1 FROM locationhistories WHERE AssetId = @p0 LIMIT 1";
                    var parameter = command.CreateParameter();
                    parameter.ParameterName = "@p0";
                    parameter.Value = id;
                    command.Parameters.Add(parameter);
                    
                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        hasLocationHistory = await reader.ReadAsync();
                    }
                }
                
                if (hasLocationHistory)
                {
                    return BadRequest(new { success = false, message = "该资产有位置变更历史记录，不能删除" });
                }

                // 检查是否有关联的故障记录
                using (var command = connection.CreateCommand())
                {
                    command.CommandText = "SELECT 1 FROM faultrecords WHERE AssetId = @p0 LIMIT 1";
                    var parameter = command.CreateParameter();
                    parameter.ParameterName = "@p0";
                    parameter.Value = id;
                    command.Parameters.Add(parameter);
                    
                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        hasFaultRecords = await reader.ReadAsync();
                    }
                }
                
                if (hasFaultRecords)
                {
                    return BadRequest(new { success = false, message = "该资产有故障记录，不能删除" });
                }
                
                // 检查是否有关联的资产历史记录
                using (var command = connection.CreateCommand())
                {
                    command.CommandText = "SELECT COUNT(*) FROM assethistories WHERE AssetId = @p0";
                    var parameter = command.CreateParameter();
                    parameter.ParameterName = "@p0";
                    parameter.Value = id;
                    command.Parameters.Add(parameter);
                    
                    var count = Convert.ToInt32(await command.ExecuteScalarAsync());
                    hasAssetHistory = count > 0;
                    
                    if (hasAssetHistory)
                    {
                        _logger.LogInformation($"资产ID {id} 有 {count} 条历史记录，将先删除这些记录");
                        
                        // 先删除历史记录
                        var deleteCommand = connection.CreateCommand();
                        deleteCommand.CommandText = "DELETE FROM assethistories WHERE AssetId = @p0";
                        var deleteParam = deleteCommand.CreateParameter();
                        deleteParam.ParameterName = "@p0";
                        deleteParam.Value = id;
                        deleteCommand.Parameters.Add(deleteParam);
                        
                        int rowsDeleted = await deleteCommand.ExecuteNonQueryAsync();
                        _logger.LogInformation($"已删除 {rowsDeleted} 条资产历史记录");
                    }
                }

                // 使用SQL语句直接删除资产
                using (var command = connection.CreateCommand())
                {
                    command.CommandText = "DELETE FROM assets WHERE Id = @p0";
                    var parameter = command.CreateParameter();
                    parameter.ParameterName = "@p0";
                    parameter.Value = id;
                    command.Parameters.Add(parameter);
                    
                    int rowsAffected = await command.ExecuteNonQueryAsync();
                    if (rowsAffected == 0)
                    {
                        return StatusCode(500, new { success = false, message = "删除资产失败" });
                    }
                }

                return Ok(new { success = true, message = "资产删除成功" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"删除资产ID {id} 出错");
                return StatusCode(500, new { success = false, message = $"删除资产ID {id} 出错: " + ex.Message });
            }
        }

        /// <summary>
        /// 导出资产列表为Excel
        /// </summary>
        /// <returns>Excel文件</returns>
        [HttpGet("export")]
        public async Task<IActionResult> Export(
            [FromQuery] string assetCode = null,
            [FromQuery] string name = null,
            [FromQuery] int? assetTypeId = null,
            [FromQuery] int? status = null,
            [FromQuery] int? locationId = null,
            [FromQuery] int? departmentId = null)
        {
            _logger.LogInformation("导出资产列表为Excel");

            try
            {
                // 确保数据库连接
                if (!await CheckDatabaseConnectionAsync())
                {
                    return StatusCode(500, new { success = false, message = "数据库连接失败" });
                }

                // 构建基础SQL查询
                var sql = @"
                    SELECT 
                        a.Id,
                        a.AssetCode,
                        a.FinancialCode,
                        a.Name,
                        a.AssetTypeId,
                        at.Name AS AssetTypeName,
                        a.SerialNumber,
                        a.Model,
                        a.Brand,
                        a.PurchaseDate,
                        a.WarrantyExpireDate,
                        a.Price,
                        a.Status,
                        a.LocationId,
                        l.Name AS LocationName,
                        a.Notes
                    FROM assets a
                    LEFT JOIN assettypes at ON a.AssetTypeId = at.Id
                    LEFT JOIN locations l ON a.LocationId = l.Id
                    WHERE 1=1";

                // 添加筛选条件
                var parameters = new Dictionary<string, object>();
                
                if (!string.IsNullOrEmpty(assetCode))
                {
                    sql += " AND a.AssetCode LIKE @AssetCode";
                    parameters.Add("@AssetCode", $"%{assetCode}%");
                }
                
                if (!string.IsNullOrEmpty(name))
                {
                    sql += " AND a.Name LIKE @Name";
                    parameters.Add("@Name", $"%{name}%");
                }
                
                if (assetTypeId.HasValue)
                {
                    sql += " AND a.AssetTypeId = @AssetTypeId";
                    parameters.Add("@AssetTypeId", assetTypeId.Value);
                }
                
                if (status.HasValue)
                {
                    sql += " AND a.Status = @Status";
                    parameters.Add("@Status", status.Value);
                }
                
                if (locationId.HasValue)
                {
                    sql += " AND a.LocationId = @LocationId";
                    parameters.Add("@LocationId", locationId.Value);
                }
                
                if (departmentId.HasValue)
                {
                    sql += " AND l.DefaultDepartmentId = @DepartmentId";
                    parameters.Add("@DepartmentId", departmentId.Value);
                }

                // 添加排序
                sql += " ORDER BY a.AssetCode";

                // 确保设置EPPlus许可证模式
                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;

                // 创建内存流
                var stream = new MemoryStream();
                
                try
                {
                    // 创建Excel包
                    using (var package = new ExcelPackage(stream))
                    {
                        // 创建工作表
                        var worksheet = package.Workbook.Worksheets.Add("资产列表");
                        
                        // 设置表头样式
                        var headerStyle = worksheet.Cells["A1:N1"].Style;
                        headerStyle.Font.Bold = true;
                        headerStyle.Font.Size = 12;
                        headerStyle.Fill.PatternType = ExcelFillStyle.Solid;
                        headerStyle.Fill.BackgroundColor.SetColor(Color.FromArgb(48, 84, 150));
                        headerStyle.Font.Color.SetColor(Color.White);
                        headerStyle.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                        headerStyle.VerticalAlignment = ExcelVerticalAlignment.Center;
                        headerStyle.Border.Top.Style = ExcelBorderStyle.Thin;
                        headerStyle.Border.Bottom.Style = ExcelBorderStyle.Thin;
                        headerStyle.Border.Left.Style = ExcelBorderStyle.Thin;
                        headerStyle.Border.Right.Style = ExcelBorderStyle.Thin;
                        
                        // 设置标题行
                        worksheet.Cells[1, 1].Value = "序号";
                        worksheet.Cells[1, 2].Value = "资产编码";
                        worksheet.Cells[1, 3].Value = "财务编号";
                        worksheet.Cells[1, 4].Value = "资产名称";
                        worksheet.Cells[1, 5].Value = "资产类型";
                        worksheet.Cells[1, 6].Value = "序列号";
                        worksheet.Cells[1, 7].Value = "型号";
                        worksheet.Cells[1, 8].Value = "品牌";
                        worksheet.Cells[1, 9].Value = "购买日期";
                        worksheet.Cells[1, 10].Value = "保修到期日";
                        worksheet.Cells[1, 11].Value = "价格";
                        worksheet.Cells[1, 12].Value = "状态";
                        worksheet.Cells[1, 13].Value = "位置";
                        worksheet.Cells[1, 14].Value = "备注";
                        
                        // 获取数据并填充工作表
                        if (_context.Database.GetDbConnection().State != ConnectionState.Open)
                        {
                            await _context.Database.GetDbConnection().OpenAsync();
                        }
                        
                        var row = 2;
                        using (var command = _context.Database.GetDbConnection().CreateCommand())
                        {
                            command.CommandText = sql;
                            
                            foreach (var param in parameters)
                            {
                                var dbParam = command.CreateParameter();
                                dbParam.ParameterName = param.Key;
                                dbParam.Value = param.Value;
                                command.Parameters.Add(dbParam);
                            }
                            
                            using (var reader = await command.ExecuteReaderAsync())
                            {
                                while (await reader.ReadAsync())
                                {
                                    worksheet.Cells[row, 1].Value = row - 1; // 序号从1开始
                                    worksheet.Cells[row, 2].Value = reader["AssetCode"].ToString();
                                    worksheet.Cells[row, 3].Value = reader["FinancialCode"] != DBNull.Value ? reader["FinancialCode"].ToString() : "";
                                    worksheet.Cells[row, 4].Value = reader["Name"].ToString();
                                    worksheet.Cells[row, 5].Value = reader["AssetTypeName"] != DBNull.Value ? reader["AssetTypeName"].ToString() : "";
                                    worksheet.Cells[row, 6].Value = reader["SerialNumber"] != DBNull.Value ? reader["SerialNumber"].ToString() : "";
                                    worksheet.Cells[row, 7].Value = reader["Model"] != DBNull.Value ? reader["Model"].ToString() : "";
                                    worksheet.Cells[row, 8].Value = reader["Brand"] != DBNull.Value ? reader["Brand"].ToString() : "";
                                    
                                    // 处理日期
                                    if (reader["PurchaseDate"] != DBNull.Value)
                                    {
                                        worksheet.Cells[row, 9].Value = Convert.ToDateTime(reader["PurchaseDate"]);
                                        worksheet.Cells[row, 9].Style.Numberformat.Format = "yyyy-MM-dd";
                                    }
                                    
                                    if (reader["WarrantyExpireDate"] != DBNull.Value)
                                    {
                                        worksheet.Cells[row, 10].Value = Convert.ToDateTime(reader["WarrantyExpireDate"]);
                                        worksheet.Cells[row, 10].Style.Numberformat.Format = "yyyy-MM-dd";
                                    }
                                    
                                    // 处理价格
                                    if (reader["Price"] != DBNull.Value)
                                    {
                                        worksheet.Cells[row, 11].Value = Convert.ToDecimal(reader["Price"]);
                                        worksheet.Cells[row, 11].Style.Numberformat.Format = "#,##0.00";
                                    }
                                    
                                    // 处理状态
                                    if (reader["Status"] != DBNull.Value)
                                    {
                                        int statusValue = Convert.ToInt32(reader["Status"]);
                                        worksheet.Cells[row, 12].Value = GetStatusName(statusValue);
                                    }
                                    
                                    worksheet.Cells[row, 13].Value = reader["LocationName"] != DBNull.Value ? reader["LocationName"].ToString() : "";
                                    worksheet.Cells[row, 14].Value = reader["Notes"] != DBNull.Value ? reader["Notes"].ToString() : "";
                                    
                                    // 设置单元格样式
                                    var cellStyle = worksheet.Cells[row, 1, row, 14].Style;
                                    cellStyle.Border.Top.Style = ExcelBorderStyle.Thin;
                                    cellStyle.Border.Bottom.Style = ExcelBorderStyle.Thin;
                                    cellStyle.Border.Left.Style = ExcelBorderStyle.Thin;
                                    cellStyle.Border.Right.Style = ExcelBorderStyle.Thin;
                                    
                                    // 设置交替行颜色
                                    if (row % 2 == 0)
                                    {
                                        var rowStyle = worksheet.Cells[row, 1, row, 14].Style;
                                        rowStyle.Fill.PatternType = ExcelFillStyle.Solid;
                                        rowStyle.Fill.BackgroundColor.SetColor(Color.FromArgb(235, 241, 252));
                                    }
                                    
                                    row++;
                                }
                            }
                        }
                        
                        // 设置列宽
                        worksheet.Column(1).Width = 8;   // 序号
                        worksheet.Column(2).Width = 15;  // 资产编码
                        worksheet.Column(3).Width = 15;  // 财务编号
                        worksheet.Column(4).Width = 20;  // 资产名称
                        worksheet.Column(5).Width = 15;  // 资产类型
                        worksheet.Column(6).Width = 15;  // 序列号
                        worksheet.Column(7).Width = 15;  // 型号
                        worksheet.Column(8).Width = 15;  // 品牌
                        worksheet.Column(9).Width = 15;  // 购买日期
                        worksheet.Column(10).Width = 15; // 保修到期日
                        worksheet.Column(11).Width = 12; // 价格
                        worksheet.Column(12).Width = 10; // 状态
                        worksheet.Column(13).Width = 20; // 位置
                        worksheet.Column(14).Width = 25; // 备注
                        
                        // 冻结首行
                        worksheet.View.FreezePanes(2, 1);
                        
                        // 保存Excel包
                        package.Save();
                    }

                    // 设置文件名
                    string fileName = $"资产列表导出_{DateTime.Now:yyyyMMdd}.xlsx";
                    
                    // 重置流位置
                    stream.Position = 0;
                    
                    // 返回Excel文件
                    return File(
                        stream, 
                        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", 
                        fileName
                    );
                }
                catch (Exception e)
                {
                    _logger.LogError(e, "生成Excel文件出错");
                    
                    try
                    {
                        // 如果Excel生成失败，回退到CSV格式
                        var sb = new StringBuilder();
                        sb.AppendLine("序号,资产编码,财务编号,资产名称,资产类型,序列号,型号,品牌,购买日期,保修到期日,价格,状态,位置,备注");
                        sb.AppendLine("0,导出失败,,,,,,,,,,,,");
                        sb.AppendLine($"0,错误信息: {e.Message},,,,,,,,,,,,");
                        
                        byte[] csvBytes = Encoding.UTF8.GetBytes(sb.ToString());
                        return File(
                            csvBytes,
                            "text/csv",
                            $"AssetList_Error_{DateTime.Now:yyyyMMddHHmmss}.csv"
                        );
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "生成错误CSV文件也失败了");
                        return StatusCode(500, new { success = false, message = "导出失败: " + e.Message });
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "导出资产列表主函数出错");
                return StatusCode(500, new { success = false, message = "导出资产列表出错: " + ex.Message });
            }
        }

        /// <summary>
        /// 内部方法：通过ID获取资产信息
        /// </summary>
        private async Task<Asset> GetAssetByIdInternal(System.Data.Common.DbConnection connection, int id)
        {
            Asset asset = null;
            using (var command = connection.CreateCommand())
            {
                command.CommandText = "SELECT Id, AssetCode, Name, AssetTypeId, SerialNumber, Model, Brand, " +
                                      "PurchaseDate, WarrantyExpireDate, Price, LocationId, Status, Notes, " +
                                      "CreatedAt, UpdatedAt " +
                                      "FROM assets WHERE Id = @p0 LIMIT 1";
                var parameter = command.CreateParameter();
                parameter.ParameterName = "@p0";
                parameter.Value = id;
                command.Parameters.Add(parameter);
                
                using (var reader = await command.ExecuteReaderAsync())
                {
                    if (await reader.ReadAsync())
                    {
                        asset = new Asset
                        {
                            Id = reader.GetInt32(0),
                            AssetCode = reader.IsDBNull(1) ? null : reader.GetString(1),
                            Name = reader.IsDBNull(2) ? null : reader.GetString(2),
                            AssetTypeId = reader.GetInt32(3),
                            SerialNumber = reader.IsDBNull(4) ? null : reader.GetString(4),
                            Model = reader.IsDBNull(5) ? null : reader.GetString(5),
                            Brand = reader.IsDBNull(6) ? null : reader.GetString(6),
                            PurchaseDate = reader.IsDBNull(7) ? null : (DateTime?)reader.GetDateTime(7),
                            WarrantyExpireDate = reader.IsDBNull(8) ? null : (DateTime?)reader.GetDateTime(8),
                            Price = reader.IsDBNull(9) ? null : (decimal?)reader.GetDecimal(9),
                            LocationId = reader.IsDBNull(10) ? null : (int?)reader.GetInt32(10),
                            Status = reader.GetInt32(11),
                            Notes = reader.IsDBNull(12) ? null : reader.GetString(12),
                            CreatedAt = reader.GetDateTime(13),
                            UpdatedAt = reader.GetDateTime(14)
                        };
                    }
                }
            }
            return asset;
        }

        /// <summary>
        /// 获取当前用户ID和名称
        /// </summary>
        /// <returns>操作人ID和名称</returns>
        private async Task<(int operatorId, string operatorName)> GetCurrentUserInfoAsync()
        {
            int operatorId = 1;
            string operatorName = "系统默认";

            try
            {
                // 尝试从User声明中获取用户ID
                _logger.LogDebug($"开始尝试获取当前用户ID...");
                var claims = User?.Claims?.ToList();
                if (claims != null && claims.Any())
                {
                    _logger.LogDebug($"当前用户Claims数量: {claims.Count}");
                    foreach (var claim in claims)
                    {
                        _logger.LogDebug($"Claim类型: {claim.Type}, 值: {claim.Value}");
                    }
                    
                    // 首先尝试从uid获取
                    var userIdClaim = claims.FirstOrDefault(c => c.Type == "uid");
                    if (userIdClaim != null && int.TryParse(userIdClaim.Value, out int userId))
                    {
                        operatorId = userId;
                        _logger.LogInformation($"从Claims的uid获取到操作用户ID: {operatorId}");
                    }
                    else
                    {
                        // 然后尝试从nameidentifier获取
                        userIdClaim = claims.FirstOrDefault(c => c.Type == ClaimTypes.NameIdentifier);
                        if (userIdClaim != null && int.TryParse(userIdClaim.Value, out int userId2))
                        {
                            operatorId = userId2;
                            _logger.LogInformation($"从Claims的NameIdentifier获取到操作用户ID: {operatorId}");
                        }
                        else
                        {
                            _logger.LogWarning("无法从任何Claims获取有效的用户ID，使用默认值1");
                        }
                    }
                }
                else
                {
                    _logger.LogWarning("当前请求中没有用户Claims信息");
                }
                
                // 获取用户名
                var user = await _context.Users.FindAsync(operatorId);
                if (user != null)
                {
                    operatorName = !string.IsNullOrEmpty(user.Name) ? user.Name : user.Username;
                    _logger.LogInformation($"获取到操作用户名称: {operatorName}");
                }
                else
                {
                    _logger.LogWarning($"未找到ID为{operatorId}的用户信息，使用默认名称");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取用户信息过程中发生异常: {ex.Message}");
            }

            return (operatorId, operatorName);
        }
    }

    /// <summary>
    /// 资产位置变更请求模型
    /// </summary>
    public class AssetLocationChangeModel
    {
        /// <summary>
        /// 新位置ID
        /// </summary>
        public int NewLocationId { get; set; }

        /// <summary>
        /// 变更原因
        /// </summary>
        public string Reason { get; set; }

        /// <summary>
        /// 备注说明
        /// </summary>
        public string Notes { get; set; }
    }
} 