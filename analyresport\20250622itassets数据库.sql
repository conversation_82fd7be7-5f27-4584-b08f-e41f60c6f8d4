/*
 Navicat Premium Data Transfer

 Source Server         : IT资产
 Source Server Type    : MySQL
 Source Server Version : 80029
 Source Host           : localhost:3306
 Source Schema         : itassets

 Target Server Type    : MySQL
 Target Server Version : 80029
 File Encoding         : 65001

 Date: 22/06/2025 14:19:20
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for __efmigrationshistory
-- ----------------------------
DROP TABLE IF EXISTS `__efmigrationshistory`;
CREATE TABLE `__efmigrationshistory`  (
  `MigrationId` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `ProductVersion` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  PRIMARY KEY (`MigrationId`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'EF迁移历史表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for _script_execution_log
-- ----------------------------
DROP TABLE IF EXISTS `_script_execution_log`;
CREATE TABLE `_script_execution_log`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `step` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
  `executed_at` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 29 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for achievements
-- ----------------------------
DROP TABLE IF EXISTS `achievements`;
CREATE TABLE `achievements`  (
  `Id` bigint NOT NULL AUTO_INCREMENT COMMENT '成就ID',
  `Code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '成就代码',
  `Name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '成就名称',
  `Description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '成就描述',
  `IconUrl` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '成就图标URL',
  `Category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'General' COMMENT '成就分类',
  `Difficulty` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'Normal' COMMENT '难度等级: Easy, Normal, Hard, Epic',
  `PointsReward` int NOT NULL DEFAULT 0 COMMENT '积分奖励',
  `CoinsReward` int NOT NULL DEFAULT 0 COMMENT '金币奖励',
  `DiamondsReward` int NOT NULL DEFAULT 0 COMMENT '钻石奖励',
  `RequiredCount` int NOT NULL DEFAULT 1 COMMENT '达成所需次数',
  `IsRepeatable` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否可重复获得',
  `IsActive` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
  `SortOrder` int NOT NULL DEFAULT 0 COMMENT '排序顺序',
  `CreatedAt` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `UpdatedAt` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE INDEX `idx_achievements_code`(`Code`) USING BTREE,
  INDEX `idx_achievements_category`(`Category`) USING BTREE,
  INDEX `idx_achievements_difficulty`(`Difficulty`) USING BTREE,
  INDEX `idx_achievements_active`(`IsActive`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 41 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '游戏化成就定义表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for asset_value_ranges
-- ----------------------------
DROP TABLE IF EXISTS `asset_value_ranges`;
CREATE TABLE `asset_value_ranges`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `min_value` decimal(18, 2) NOT NULL,
  `max_value` decimal(18, 2) NOT NULL,
  `range_label` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `range_color` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '#3b82f6',
  `sort_order` int NOT NULL,
  `is_active` tinyint(1) NULL DEFAULT 1,
  `created_at` timestamp(0) NULL DEFAULT CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '资产价值区间配置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for assethistories
-- ----------------------------
DROP TABLE IF EXISTS `assethistories`;
CREATE TABLE `assethistories`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `AssetId` int NOT NULL COMMENT '资产ID',
  `OperationType` int NOT NULL COMMENT '操作类型：1创建，2修改，3删除，4位置变更，5状态变更',
  `OperatorId` int NOT NULL COMMENT '操作人ID',
  `OperationTime` datetime(0) NOT NULL COMMENT '操作时间',
  `Description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '描述（JSON格式，记录变更前后的属性值）',
  `CreatedAt` datetime(0) NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime(0) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`) USING BTREE,
  INDEX `IX_AssetHistories_AssetId`(`AssetId`) USING BTREE,
  INDEX `IX_AssetHistories_OperatorId`(`OperatorId`) USING BTREE,
  CONSTRAINT `FK_AssetHistories_Assets_AssetId` FOREIGN KEY (`AssetId`) REFERENCES `assets` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_AssetHistories_Users_OperatorId` FOREIGN KEY (`OperatorId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 154 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '资产历史表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for assetreceives
-- ----------------------------
DROP TABLE IF EXISTS `assetreceives`;
CREATE TABLE `assetreceives`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `ReceiveCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '入库单号',
  `PurchaseOrderId` int NULL DEFAULT NULL COMMENT '采购订单ID',
  `Title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '标题',
  `Description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '描述',
  `ReceiverId` int NOT NULL COMMENT '接收人ID',
  `ReceiveTime` datetime(0) NOT NULL COMMENT '接收时间',
  `Status` int NOT NULL DEFAULT 0 COMMENT '状态：0草稿，1已提交，2已确认',
  `AssetTypeId` int NULL DEFAULT NULL COMMENT '资产类型ID',
  `Quantity` int NOT NULL DEFAULT 1 COMMENT '数量',
  `TotalAmount` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '总金额',
  `InitialLocationId` int NULL DEFAULT NULL COMMENT '初始位置ID',
  `Notes` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `CreatedAt` datetime(0) NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime(0) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE INDEX `IX_AssetReceives_ReceiveCode`(`ReceiveCode`) USING BTREE,
  INDEX `IX_AssetReceives_PurchaseOrderId`(`PurchaseOrderId`) USING BTREE,
  INDEX `IX_AssetReceives_ReceiverId`(`ReceiverId`) USING BTREE,
  INDEX `IX_AssetReceives_AssetTypeId`(`AssetTypeId`) USING BTREE,
  INDEX `IX_AssetReceives_InitialLocationId`(`InitialLocationId`) USING BTREE,
  CONSTRAINT `FK_AssetReceives_AssetTypes_AssetTypeId` FOREIGN KEY (`AssetTypeId`) REFERENCES `assettypes` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_AssetReceives_Locations_InitialLocationId` FOREIGN KEY (`InitialLocationId`) REFERENCES `locations` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_AssetReceives_PurchaseOrders_PurchaseOrderId` FOREIGN KEY (`PurchaseOrderId`) REFERENCES `purchaseorders` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_AssetReceives_Users_ReceiverId` FOREIGN KEY (`ReceiverId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '资产入库表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for assets
-- ----------------------------
DROP TABLE IF EXISTS `assets`;
CREATE TABLE `assets`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `assetCode` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '资产编号(IT-类型-年月日-序号)',
  `FinancialCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '财务编号',
  `Name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '资产名称',
  `AssetTypeId` int NOT NULL COMMENT '资产类型ID',
  `SerialNumber` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '序列号',
  `Model` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '型号',
  `Brand` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '品牌',
  `PurchaseDate` datetime(0) NULL DEFAULT NULL COMMENT '购买日期',
  `WarrantyExpireDate` datetime(0) NULL DEFAULT NULL COMMENT '保修到期日',
  `Price` decimal(18, 2) NULL DEFAULT NULL COMMENT '价格',
  `LocationId` int NULL DEFAULT NULL COMMENT '位置ID',
  `DepartmentId` int NULL DEFAULT NULL,
  `Status` int NOT NULL DEFAULT 0 COMMENT '状态：0闲置，1在用，2维修中，3报废',
  `Notes` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `CreatedAt` datetime(0) NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime(0) NOT NULL COMMENT '更新时间',
  `asset_code_prefix` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'IT' COMMENT '资产编号前缀',
  `inventory_id` int NULL DEFAULT NULL COMMENT '来源库存ID',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE INDEX `IX_Assets_AssetCode`(`assetCode`) USING BTREE,
  INDEX `IX_Assets_AssetTypeId`(`AssetTypeId`) USING BTREE,
  INDEX `IX_Assets_LocationId`(`LocationId`) USING BTREE,
  INDEX `IX_Assets_FinancialCode`(`FinancialCode`) USING BTREE,
  INDEX `IX_Assets_DepartmentId`(`DepartmentId`) USING BTREE,
  CONSTRAINT `FK_Assets_AssetTypes_AssetTypeId` FOREIGN KEY (`AssetTypeId`) REFERENCES `assettypes` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_Assets_Departments_DepartmentId` FOREIGN KEY (`DepartmentId`) REFERENCES `departments` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_Assets_Locations_LocationId` FOREIGN KEY (`LocationId`) REFERENCES `locations` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1103 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '资产表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for assetsnapshots
-- ----------------------------
DROP TABLE IF EXISTS `assetsnapshots`;
CREATE TABLE `assetsnapshots`  (
  `Id` bigint NOT NULL AUTO_INCREMENT,
  `SnapshotDate` date NOT NULL COMMENT '快照日期',
  `AssetId` int NOT NULL COMMENT '资产ID',
  `AssetCode` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `FinancialCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `AssetName` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `AssetTypeId` int NOT NULL,
  `LocationId` int NULL DEFAULT NULL,
  `DepartmentId` int NULL DEFAULT NULL,
  `Status` int NOT NULL,
  `Price` decimal(18, 2) NULL DEFAULT NULL,
  `PurchaseDate` datetime(0) NULL DEFAULT NULL,
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE INDEX `UK_AssetSnapshots_Date_AssetId`(`SnapshotDate`, `AssetId`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '资产历史数据快照表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for assettypes
-- ----------------------------
DROP TABLE IF EXISTS `assettypes`;
CREATE TABLE `assettypes`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `Name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '类型名称',
  `Code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '类型编码',
  `Description` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '类型描述',
  `ParentId` int NULL DEFAULT NULL COMMENT '父类型ID',
  `RequireSerialNumber` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否需要序列号',
  `SortOrder` int NOT NULL DEFAULT 0 COMMENT '排序',
  `IsActive` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否激活',
  `CreatedAt` datetime(0) NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime(0) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE INDEX `IX_AssetTypes_Code`(`Code`) USING BTREE,
  UNIQUE INDEX `IX_AssetTypes_Name`(`Name`) USING BTREE,
  INDEX `IX_AssetTypes_ParentId`(`ParentId`) USING BTREE,
  CONSTRAINT `FK_AssetTypes_AssetTypes_ParentId` FOREIGN KEY (`ParentId`) REFERENCES `assettypes` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 32 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '资产类型表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for attachments
-- ----------------------------
DROP TABLE IF EXISTS `attachments`;
CREATE TABLE `attachments`  (
  `AttachmentId` bigint NOT NULL AUTO_INCREMENT COMMENT '附件ID (BIGINT)',
  `TaskId` bigint NULL DEFAULT NULL COMMENT '关联的任务ID',
  `CommentId` bigint NULL DEFAULT NULL COMMENT '关联的评论ID',
  `UploaderUserId` int NOT NULL COMMENT '上传用户ID (关联 users.Id - INT)',
  `FileName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '原始文件名',
  `StoredFileName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '存储文件名 (避免重复，建议UUID)',
  `FilePath` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '文件存储路径 (相对路径或包含Bucket信息)',
  `FileSize` bigint NOT NULL COMMENT '文件大小 (字节)',
  `FileType` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '文件MIME类型',
  `IsPreviewable` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否可在线预览',
  `StorageType` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'Local' COMMENT '存储类型 (Local, S3, AzureBlob)',
  `CreationTimestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`AttachmentId`) USING BTREE,
  UNIQUE INDEX `idx_attachments_storedfilename`(`StoredFileName`) USING BTREE,
  INDEX `idx_attachments_task`(`TaskId`) USING BTREE,
  INDEX `idx_attachments_comment`(`CommentId`) USING BTREE,
  INDEX `idx_attachments_uploader`(`UploaderUserId`) USING BTREE,
  CONSTRAINT `FK_Attachments_Comment` FOREIGN KEY (`CommentId`) REFERENCES `comments` (`CommentId`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `FK_Attachments_Task` FOREIGN KEY (`TaskId`) REFERENCES `tasks` (`TaskId`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `FK_Attachments_UploaderUser` FOREIGN KEY (`UploaderUserId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '任务附件表 (V2 - BIGINT PK)' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for auditlogs
-- ----------------------------
DROP TABLE IF EXISTS `auditlogs`;
CREATE TABLE `auditlogs`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `UserId` int NULL DEFAULT NULL COMMENT '用户ID',
  `UserName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '用户名',
  `Type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '日志类型',
  `TableName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '表名',
  `DateTime` datetime(0) NOT NULL COMMENT '日期时间',
  `PrimaryKey` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '主键',
  `OldValues` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '旧值',
  `NewValues` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '新值',
  `Action` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '操作',
  `ClientIP` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '客户端IP',
  PRIMARY KEY (`Id`) USING BTREE,
  INDEX `IX_AuditLogs_UserId`(`UserId`) USING BTREE,
  CONSTRAINT `FK_AuditLogs_Users_UserId` FOREIGN KEY (`UserId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '审计日志表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for behavior_types
-- ----------------------------
DROP TABLE IF EXISTS `behavior_types`;
CREATE TABLE `behavior_types`  (
  `Id` bigint NOT NULL AUTO_INCREMENT COMMENT '行为类型ID',
  `Code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '行为代码',
  `Name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '行为名称',
  `Description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '行为描述',
  `Category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'General' COMMENT '行为分类',
  `PointsReward` int NOT NULL DEFAULT 0 COMMENT '积分奖励',
  `CoinsReward` int NOT NULL DEFAULT 0 COMMENT '金币奖励',
  `DiamondsReward` int NOT NULL DEFAULT 0 COMMENT '钻石奖励',
  `IsActive` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
  `CreatedAt` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `UpdatedAt` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE INDEX `idx_behavior_types_code`(`Code`) USING BTREE,
  INDEX `idx_behavior_types_category`(`Category`) USING BTREE,
  INDEX `idx_behavior_types_active`(`IsActive`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 34 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '游戏化行为类型定义表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for comments
-- ----------------------------
DROP TABLE IF EXISTS `comments`;
CREATE TABLE `comments`  (
  `CommentId` bigint NOT NULL AUTO_INCREMENT COMMENT '评论ID (BIGINT)',
  `TaskId` bigint NOT NULL COMMENT '关联的任务ID',
  `UserId` int NOT NULL COMMENT '评论用户ID (关联 users.Id - INT)',
  `ParentCommentId` bigint NULL DEFAULT NULL COMMENT '父评论ID (用于回复)',
  `Content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '评论内容 (支持Markdown或HTML)',
  `MentionedUserIds` json NULL COMMENT '评论中@提及的用户ID列表 (INT IDs in JSON array)',
  `IsPinned` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否置顶',
  `IsEdited` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否被编辑过',
  `CreationTimestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
  `LastUpdatedTimestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`CommentId`) USING BTREE,
  INDEX `idx_comments_task_time`(`TaskId`, `CreationTimestamp`) USING BTREE,
  INDEX `idx_comments_user`(`UserId`) USING BTREE,
  INDEX `idx_comments_parent`(`ParentCommentId`) USING BTREE,
  CONSTRAINT `FK_Comments_ParentComment` FOREIGN KEY (`ParentCommentId`) REFERENCES `comments` (`CommentId`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `FK_Comments_Task` FOREIGN KEY (`TaskId`) REFERENCES `tasks` (`TaskId`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `FK_Comments_User` FOREIGN KEY (`UserId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 37 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '任务评论表 (V2 - BIGINT PK)' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for daily_reports
-- ----------------------------
DROP TABLE IF EXISTS `daily_reports`;
CREATE TABLE `daily_reports`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `report_date` date NOT NULL,
  `report_type` enum('daily','weekly','monthly') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'daily',
  `file_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '报表文件路径',
  `snapshot_data` json NULL COMMENT '数据快照',
  `created_at` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_report_date_type`(`report_date`, `report_type`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for dashboard_config
-- ----------------------------
DROP TABLE IF EXISTS `dashboard_config`;
CREATE TABLE `dashboard_config`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `theme` enum('light','dark') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'dark',
  `layout` json NULL COMMENT '面板布局配置',
  `auto_refresh` int NULL DEFAULT 60 COMMENT '刷新间隔(秒)',
  `updated_at` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_dashboard_user`(`user_id`) USING BTREE,
  CONSTRAINT `fk_dashboard_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`Id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for db_structure_result
-- ----------------------------
DROP TABLE IF EXISTS `db_structure_result`;
CREATE TABLE `db_structure_result`  (
  `section` varchar(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '',
  `表名` varchar(64) CHARACTER SET utf8 COLLATE utf8_tolower_ci NULL DEFAULT NULL,
  `列名` varchar(64) CHARACTER SET utf8 COLLATE utf8_tolower_ci NULL DEFAULT NULL,
  `数据类型` mediumtext CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `可为空` varchar(3) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
  `默认值` text CHARACTER SET utf8 COLLATE utf8_bin NULL,
  `键` enum('','PRI','UNI','MUL') CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `额外信息` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `注释` text CHARACTER SET utf8 COLLATE utf8_bin NOT NULL
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for departments
-- ----------------------------
DROP TABLE IF EXISTS `departments`;
CREATE TABLE `departments`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `Name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '部门名称',
  `Code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '部门编码',
  `ParentId` int NULL DEFAULT NULL COMMENT '父部门ID',
  `ManagerId` int NULL DEFAULT NULL COMMENT '部门经理ID',
  `DeputyManagerId` int NULL DEFAULT NULL COMMENT '部门主任ID（二级负责人）',
  `Path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '层级路径',
  `Description` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '部门描述',
  `IsActive` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否激活',
  `CreatedAt` datetime(0) NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime(0) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE INDEX `IX_Departments_Code`(`Code`) USING BTREE,
  UNIQUE INDEX `IX_Departments_Name`(`Name`) USING BTREE,
  INDEX `IX_Departments_ParentId`(`ParentId`) USING BTREE,
  INDEX `IX_Departments_ManagerId`(`ManagerId`) USING BTREE,
  INDEX `IX_Departments_DeputyManagerId`(`DeputyManagerId`) USING BTREE,
  CONSTRAINT `FK_Departments_Departments_ParentId` FOREIGN KEY (`ParentId`) REFERENCES `departments` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_Departments_Personnel_DeputyManagerId` FOREIGN KEY (`DeputyManagerId`) REFERENCES `personnel` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 31 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '部门表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for faultrecords
-- ----------------------------
DROP TABLE IF EXISTS `faultrecords`;
CREATE TABLE `faultrecords`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `Title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '标题',
  `Description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '描述',
  `AssetId` int NULL DEFAULT NULL COMMENT '资产ID（线下设备时可为空）',
  `FaultTypeId` int NOT NULL COMMENT '故障类型ID',
  `LocationId` int NULL DEFAULT NULL COMMENT '位置ID',
  `ReporterId` int NOT NULL COMMENT '报告人ID',
  `ReportTime` datetime(0) NOT NULL COMMENT '报告时间',
  `Status` int NOT NULL DEFAULT 0 COMMENT '状态：0待处理，1处理中，2已解决，3已关闭',
  `Severity` int NOT NULL DEFAULT 0 COMMENT '严重程度：0一般，1严重，2紧急',
  `AssigneeId` int NULL DEFAULT NULL COMMENT '处理人ID',
  `AssignTime` datetime(0) NULL DEFAULT NULL COMMENT '分配时间',
  `ResponseTime` datetime(0) NULL DEFAULT NULL COMMENT '响应时间',
  `ResolutionTime` datetime(0) NULL DEFAULT NULL COMMENT '解决时间',
  `Resolution` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '解决方案',
  `RootCause` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '根本原因',
  `Notes` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `IsReturned` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否返厂',
  `CreatedAt` datetime(0) NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime(0) NOT NULL COMMENT '更新时间',
  `faultNumber` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '故障单号(FIX-年月日-序号)',
  `expected_return_date` datetime(0) NULL DEFAULT NULL COMMENT '预期返厂日期',
  `warning_flag` tinyint(1) NULL DEFAULT 0 COMMENT '超期预警标记',
  PRIMARY KEY (`Id`) USING BTREE,
  INDEX `IX_FaultRecords_AssetId`(`AssetId`) USING BTREE,
  INDEX `IX_FaultRecords_FaultTypeId`(`FaultTypeId`) USING BTREE,
  INDEX `IX_FaultRecords_LocationId`(`LocationId`) USING BTREE,
  INDEX `IX_FaultRecords_ReporterId`(`ReporterId`) USING BTREE,
  INDEX `IX_FaultRecords_AssigneeId`(`AssigneeId`) USING BTREE,
  INDEX `idx_faultrecords_faultNumber`(`faultNumber`) USING BTREE,
  CONSTRAINT `FK_FaultRecords_Assets_AssetId` FOREIGN KEY (`AssetId`) REFERENCES `assets` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_FaultRecords_FaultTypes_FaultTypeId` FOREIGN KEY (`FaultTypeId`) REFERENCES `faulttypes` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_FaultRecords_Locations_LocationId` FOREIGN KEY (`LocationId`) REFERENCES `locations` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_FaultRecords_Users_AssigneeId` FOREIGN KEY (`AssigneeId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_FaultRecords_Users_ReporterId` FOREIGN KEY (`ReporterId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '故障记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for faulttypes
-- ----------------------------
DROP TABLE IF EXISTS `faulttypes`;
CREATE TABLE `faulttypes`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `Name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '类型名称',
  `Code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '类型编码',
  `Description` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '类型描述',
  `Severity` int NOT NULL DEFAULT 0 COMMENT '严重程度：0一般，1严重，2紧急',
  `SuggestedResponseTime` int NULL DEFAULT NULL COMMENT '建议响应时间（小时）',
  `SuggestedResolutionTime` int NULL DEFAULT NULL COMMENT '建议解决时间（小时）',
  `SortOrder` int NOT NULL DEFAULT 0 COMMENT '排序',
  `IsActive` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否激活',
  `CreatedAt` datetime(0) NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime(0) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE INDEX `IX_FaultTypes_Code`(`Code`) USING BTREE,
  UNIQUE INDEX `IX_FaultTypes_Name`(`Name`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 16 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '故障类型表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for gamification_badges
-- ----------------------------
DROP TABLE IF EXISTS `gamification_badges`;
CREATE TABLE `gamification_badges`  (
  `BadgeId` bigint NOT NULL AUTO_INCREMENT COMMENT '徽章ID (BIGINT)',
  `Code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '徽章唯一代码',
  `Name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '徽章名称',
  `Description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '徽章描述及获得条件',
  `IconUrl` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '徽章图标URL',
  `TriggerEvent` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '触发事件类型',
  `TriggerThreshold` int NULL DEFAULT NULL COMMENT '触发阈值',
  `RequiredLevel` int NULL DEFAULT NULL COMMENT '获得所需最低等级',
  `PointsAwarded` int NOT NULL DEFAULT 0 COMMENT '获得徽章时奖励的积分',
  `XPAwarded` int NOT NULL DEFAULT 0 COMMENT '获得徽章时奖励的XP',
  `IsRepeatable` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否可重复获得',
  `IsActive` tinyint(1) NOT NULL DEFAULT 1 COMMENT '徽章是否启用',
  `CreationTimestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`BadgeId`) USING BTREE,
  UNIQUE INDEX `idx_gamification_badges_code`(`Code`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '游戏化徽章定义表 (BIGINT PK)' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for gamification_config
-- ----------------------------
DROP TABLE IF EXISTS `gamification_config`;
CREATE TABLE `gamification_config`  (
  `ConfigKey` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '配置键',
  `ConfigValue` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '配置值',
  `Description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '配置描述',
  `Category` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'General' COMMENT '配置分类',
  `IsActive` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
  `UpdatedAt` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`ConfigKey`) USING BTREE,
  INDEX `idx_gamification_config_category`(`Category`, `IsActive`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '游戏化系统配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for gamification_items
-- ----------------------------
DROP TABLE IF EXISTS `gamification_items`;
CREATE TABLE `gamification_items`  (
  `ItemId` bigint NOT NULL AUTO_INCREMENT COMMENT '物品ID (BIGINT)',
  `Code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '物品唯一代码',
  `Name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '物品名称',
  `Description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '物品描述',
  `IconUrl` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '物品图标URL',
  `Type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '物品类型',
  `Effect` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '物品效果描述',
  `PointsCost` int NOT NULL DEFAULT 0 COMMENT '购买所需积分',
  `Cooldown` int NULL DEFAULT NULL COMMENT '使用冷却时间(分钟)',
  `IsActive` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否激活',
  `CreationTimestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`ItemId`) USING BTREE,
  UNIQUE INDEX `idx_gamification_items_code`(`Code`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '游戏化物品定义表 (BIGINT PK)' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for gamification_log
-- ----------------------------
DROP TABLE IF EXISTS `gamification_log`;
CREATE TABLE `gamification_log`  (
  `LogId` bigint NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `UserId` bigint NOT NULL COMMENT '关联用户ID',
  `Timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '事件发生时间',
  `EventType` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '事件类型 (如 TaskCompleted, BadgeEarned, LevelUp, PointsSpent, DailyLogin, CommentAdded)',
  `XPChange` int NOT NULL DEFAULT 0 COMMENT '经验值变动',
  `PointsChange` int NOT NULL DEFAULT 0 COMMENT '积分变动',
  `PointsGained` int NOT NULL DEFAULT 0 COMMENT '获得的积分 (用于通用游戏化系统)',
  `CoinsGained` int NOT NULL DEFAULT 0 COMMENT '获得的金币 (用于通用游戏化系统)',
  `DiamondsGained` int NOT NULL DEFAULT 0 COMMENT '获得的钻石 (用于通用游戏化系统)',
  `LevelBefore` int NULL DEFAULT NULL COMMENT '变动前等级',
  `LevelAfter` int NULL DEFAULT NULL COMMENT '变动后等级',
  `Reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '变动原因文字描述',
  `Description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '描述信息 (用于通用游戏化系统)',
  `ReferenceId` bigint NULL DEFAULT NULL COMMENT '关联引用ID (用于通用游戏化系统)',
  `RelatedTaskId` bigint NULL DEFAULT NULL COMMENT '关联的任务ID',
  `RelatedCommentId` bigint NULL DEFAULT NULL COMMENT '关联的评论ID',
  `RelatedBadgeId` bigint NULL DEFAULT NULL COMMENT '关联的徽章ID',
  `RelatedItemId` bigint NULL DEFAULT NULL COMMENT '关联的物品ID (如果有点数商城)',
  `Metadata` json NULL COMMENT '其他元数据 (例如完成任务时的详情)',
  PRIMARY KEY (`LogId`) USING BTREE,
  INDEX `idx_gamification_log_user_time`(`UserId`, `Timestamp`) USING BTREE,
  INDEX `idx_gamification_log_event_type`(`EventType`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '游戏化事件日志表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for gamification_userbadges
-- ----------------------------
DROP TABLE IF EXISTS `gamification_userbadges`;
CREATE TABLE `gamification_userbadges`  (
  `UserBadgeId` bigint NOT NULL AUTO_INCREMENT COMMENT '用户徽章记录ID (BIGINT)',
  `UserId` int NOT NULL COMMENT '用户ID (关联 users.Id - INT)',
  `BadgeId` bigint NOT NULL COMMENT '徽章ID',
  `EarnedTimestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
  `Count` int NOT NULL DEFAULT 1 COMMENT '获得次数',
  PRIMARY KEY (`UserBadgeId`) USING BTREE,
  INDEX `idx_userbadges_user_badge`(`UserId`, `BadgeId`) USING BTREE,
  INDEX `idx_userbadges_badge`(`BadgeId`) USING BTREE,
  CONSTRAINT `FK_UserBadges_Badge` FOREIGN KEY (`BadgeId`) REFERENCES `gamification_badges` (`BadgeId`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `FK_UserBadges_User` FOREIGN KEY (`UserId`) REFERENCES `users` (`Id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户获得的徽章记录表 (BIGINT PK)' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for gamification_useritems
-- ----------------------------
DROP TABLE IF EXISTS `gamification_useritems`;
CREATE TABLE `gamification_useritems`  (
  `UserItemId` bigint NOT NULL AUTO_INCREMENT COMMENT '用户物品ID (BIGINT)',
  `UserId` int NOT NULL COMMENT '用户ID (关联 users.Id - INT)',
  `ItemId` bigint NOT NULL COMMENT '物品ID',
  `Quantity` int NOT NULL DEFAULT 1 COMMENT '物品数量',
  `AcquiredTimestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '获得时间',
  `LastUsedTimestamp` datetime(0) NULL DEFAULT NULL COMMENT '上次使用时间',
  `ExpiryTimestamp` datetime(0) NULL DEFAULT NULL COMMENT '过期时间(如果有)',
  PRIMARY KEY (`UserItemId`) USING BTREE,
  UNIQUE INDEX `idx_useritems_user_item`(`UserId`, `ItemId`) USING BTREE,
  INDEX `idx_useritems_item`(`ItemId`) USING BTREE,
  CONSTRAINT `FK_UserItems_Item` FOREIGN KEY (`ItemId`) REFERENCES `gamification_items` (`ItemId`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `FK_UserItems_User` FOREIGN KEY (`UserId`) REFERENCES `users` (`Id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户拥有的物品表 (BIGINT PK)' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for gamification_userstats
-- ----------------------------
DROP TABLE IF EXISTS `gamification_userstats`;
CREATE TABLE `gamification_userstats`  (
  `UserId` bigint NOT NULL COMMENT '用户统计关联ID (BIGINT PK, 逻辑关联 users.Id)',
  `CoreUserId` int NOT NULL COMMENT '对应的核心用户ID (关联 users.Id - INT, 用于查询)',
  `CurrentXP` int NOT NULL DEFAULT 0 COMMENT '当前经验值',
  `CurrentLevel` int NOT NULL DEFAULT 1 COMMENT '当前等级',
  `PointsBalance` int NOT NULL DEFAULT 0 COMMENT '当前可用积分',
  `CoinsBalance` int NOT NULL DEFAULT 0 COMMENT '当前金币余额',
  `DiamondsBalance` int NOT NULL DEFAULT 0 COMMENT '当前钻石余额',
  `CompletedTasksCount` int NOT NULL DEFAULT 0 COMMENT '累计完成任务数',
  `CreatedTasksCount` int NOT NULL DEFAULT 0 COMMENT '累计创建任务数',
  `ClaimedTasksCount` int NOT NULL DEFAULT 0 COMMENT '累计认领任务数',
  `OnTimeTasksCount` int NOT NULL DEFAULT 0 COMMENT '累计按时完成任务数',
  `StreakCount` int NOT NULL DEFAULT 0 COMMENT '当前连续活动/完成任务天数',
  `LastActivityTimestamp` datetime(0) NULL DEFAULT NULL COMMENT '最后活跃时间戳',
  `LastStreakTimestamp` date NULL DEFAULT NULL COMMENT '上次增加连续记录的日期',
  `LastUpdatedTimestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`UserId`) USING BTREE,
  UNIQUE INDEX `idx_gamification_userstats_coreuserid`(`CoreUserId`) USING BTREE,
  CONSTRAINT `FK_UserStats_CoreUser` FOREIGN KEY (`CoreUserId`) REFERENCES `users` (`Id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '游戏化用户统计表 (BIGINT PK)' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for inventory_thresholds
-- ----------------------------
DROP TABLE IF EXISTS `inventory_thresholds`;
CREATE TABLE `inventory_thresholds`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `asset_type_id` int NOT NULL,
  `min_threshold` int NOT NULL DEFAULT 5 COMMENT '最小安全库存',
  `warning_threshold` int NOT NULL DEFAULT 10 COMMENT '预警库存',
  `updated_at` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_inventory_asset_type`(`asset_type_id`) USING BTREE,
  CONSTRAINT `fk_inventory_asset_type` FOREIGN KEY (`asset_type_id`) REFERENCES `assettypes` (`Id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for locationhistories
-- ----------------------------
DROP TABLE IF EXISTS `locationhistories`;
CREATE TABLE `locationhistories`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `AssetId` int NOT NULL COMMENT '资产ID',
  `OldLocationId` int NULL DEFAULT NULL COMMENT '旧位置ID',
  `NewLocationId` int NOT NULL COMMENT '新位置ID',
  `OperatorId` int NOT NULL COMMENT '操作人ID',
  `ChangeType` int NOT NULL DEFAULT 0 COMMENT '变更类型：0转移，1领用，2归还',
  `ChangeTime` datetime(0) NOT NULL COMMENT '变更时间',
  `Notes` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `CreatedAt` datetime(0) NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`Id`) USING BTREE,
  INDEX `IX_LocationHistories_AssetId`(`AssetId`) USING BTREE,
  INDEX `IX_LocationHistories_OldLocationId`(`OldLocationId`) USING BTREE,
  INDEX `IX_LocationHistories_NewLocationId`(`NewLocationId`) USING BTREE,
  INDEX `IX_LocationHistories_OperatorId`(`OperatorId`) USING BTREE,
  CONSTRAINT `FK_LocationHistories_Assets_AssetId` FOREIGN KEY (`AssetId`) REFERENCES `assets` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_LocationHistories_Locations_NewLocationId` FOREIGN KEY (`NewLocationId`) REFERENCES `locations` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_LocationHistories_Locations_OldLocationId` FOREIGN KEY (`OldLocationId`) REFERENCES `locations` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_LocationHistories_Users_OperatorId` FOREIGN KEY (`OperatorId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 13 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '位置历史表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for locations
-- ----------------------------
DROP TABLE IF EXISTS `locations`;
CREATE TABLE `locations`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `Code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '位置编码',
  `Name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '位置名称',
  `Type` int NOT NULL DEFAULT 0 COMMENT '位置类型：0厂区，1产线，2工序，3工位，4设备位置',
  `ParentId` int NULL DEFAULT NULL COMMENT '父位置ID',
  `Path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '层级路径',
  `Description` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '位置描述',
  `DefaultDepartmentId` int NULL DEFAULT NULL COMMENT '默认部门ID',
  `DefaultResponsiblePersonId` int NULL DEFAULT NULL COMMENT '默认负责人ID',
  `IsActive` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否激活',
  `CreatedAt` datetime(0) NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime(0) NOT NULL COMMENT '更新时间',
  `level` tinyint NOT NULL DEFAULT 3 COMMENT '位置级别(1-5)',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE INDEX `IX_Locations_Code`(`Code`) USING BTREE,
  INDEX `IX_Locations_ParentId`(`ParentId`) USING BTREE,
  INDEX `IX_Locations_DefaultDepartmentId`(`DefaultDepartmentId`) USING BTREE,
  INDEX `IX_Locations_DefaultResponsiblePersonId`(`DefaultResponsiblePersonId`) USING BTREE,
  INDEX `idx_locations_level`(`level`) USING BTREE,
  CONSTRAINT `FK_Locations_Departments_DefaultDepartmentId` FOREIGN KEY (`DefaultDepartmentId`) REFERENCES `departments` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_Locations_Locations_ParentId` FOREIGN KEY (`ParentId`) REFERENCES `locations` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_Locations_Users_DefaultResponsiblePersonId` FOREIGN KEY (`DefaultResponsiblePersonId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 293 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '位置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for locationusers
-- ----------------------------
DROP TABLE IF EXISTS `locationusers`;
CREATE TABLE `locationusers`  (
  `location_id` int NOT NULL,
  `personnel_id` int NOT NULL,
  `user_type` tinyint NOT NULL COMMENT '0-使用人 1-管理员',
  `created_at` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`location_id`, `personnel_id`, `user_type`) USING BTREE,
  INDEX `personnel_id`(`personnel_id`) USING BTREE,
  CONSTRAINT `locationusers_ibfk_1` FOREIGN KEY (`location_id`) REFERENCES `locations` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `locationusers_ibfk_2` FOREIGN KEY (`personnel_id`) REFERENCES `personnel` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for maintenanceorders
-- ----------------------------
DROP TABLE IF EXISTS `maintenanceorders`;
CREATE TABLE `maintenanceorders`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `OrderCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '维护单号',
  `Title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '标题',
  `Description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '描述',
  `AssetId` int NOT NULL COMMENT '资产ID',
  `FaultRecordId` int NULL DEFAULT NULL COMMENT '故障记录ID',
  `MaintenanceType` int NOT NULL DEFAULT 0 COMMENT '维护类型：0常规维护，1故障维修，2返厂维修跟踪',
  `Status` int NOT NULL DEFAULT 0 COMMENT '状态：0待处理，1处理中，2已完成，3已取消',
  `CreatorId` int NOT NULL COMMENT '创建人ID',
  `CreateTime` datetime(0) NOT NULL COMMENT '创建时间',
  `AssigneeId` int NULL DEFAULT NULL COMMENT '处理人ID',
  `AssignTime` datetime(0) NULL DEFAULT NULL COMMENT '分配时间',
  `PlanStartTime` datetime(0) NULL DEFAULT NULL COMMENT '计划开始时间',
  `PlanEndTime` datetime(0) NULL DEFAULT NULL COMMENT '计划结束时间',
  `ActualStartTime` datetime(0) NULL DEFAULT NULL COMMENT '实际开始时间',
  `ActualEndTime` datetime(0) NULL DEFAULT NULL COMMENT '实际结束时间',
  `Solution` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '解决方案',
  `Notes` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `CreatedAt` datetime(0) NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime(0) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE INDEX `IX_MaintenanceOrders_OrderCode`(`OrderCode`) USING BTREE,
  INDEX `IX_MaintenanceOrders_AssetId`(`AssetId`) USING BTREE,
  INDEX `IX_MaintenanceOrders_FaultRecordId`(`FaultRecordId`) USING BTREE,
  INDEX `IX_MaintenanceOrders_CreatorId`(`CreatorId`) USING BTREE,
  INDEX `IX_MaintenanceOrders_AssigneeId`(`AssigneeId`) USING BTREE,
  CONSTRAINT `FK_MaintenanceOrders_Assets_AssetId` FOREIGN KEY (`AssetId`) REFERENCES `assets` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_MaintenanceOrders_FaultRecords_FaultRecordId` FOREIGN KEY (`FaultRecordId`) REFERENCES `faultrecords` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_MaintenanceOrders_Users_AssigneeId` FOREIGN KEY (`AssigneeId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_MaintenanceOrders_Users_CreatorId` FOREIGN KEY (`CreatorId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '维护订单表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for menus
-- ----------------------------
DROP TABLE IF EXISTS `menus`;
CREATE TABLE `menus`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `Name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '菜单名称',
  `Code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '菜单编码',
  `ParentId` int NULL DEFAULT NULL COMMENT '父菜单ID',
  `Type` int NOT NULL DEFAULT 0 COMMENT '菜单类型：0菜单，1按钮',
  `Icon` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '图标',
  `Path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '路由路径',
  `Component` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '组件路径',
  `Permission` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '权限标识',
  `Description` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '描述',
  `SortOrder` int NOT NULL DEFAULT 0 COMMENT '排序',
  `IsExternal` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否外链',
  `KeepAlive` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否缓存',
  `IsVisible` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否可见',
  `IsActive` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否激活',
  `CreatedAt` datetime(0) NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime(0) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE INDEX `IX_Menus_Code`(`Code`) USING BTREE,
  INDEX `IX_Menus_ParentId`(`ParentId`) USING BTREE,
  CONSTRAINT `FK_Menus_Menus_ParentId` FOREIGN KEY (`ParentId`) REFERENCES `menus` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 27 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '菜单表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for mv_asset_kpi
-- ----------------------------
DROP TABLE IF EXISTS `mv_asset_kpi`;
CREATE TABLE `mv_asset_kpi`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `total_assets` int NULL DEFAULT 0,
  `total_value` decimal(18, 2) NULL DEFAULT 0.00,
  `in_use_rate` decimal(5, 2) NULL DEFAULT 0.00,
  `fault_rate` decimal(5, 2) NULL DEFAULT 0.00,
  `idle_rate` decimal(5, 2) NULL DEFAULT 0.00,
  `maintenance_rate` decimal(5, 2) NULL DEFAULT 0.00,
  `average_utilization` decimal(5, 2) NULL DEFAULT 0.00,
  `health_score` decimal(5, 2) NULL DEFAULT 0.00,
  `last_updated` timestamp(0) NULL DEFAULT CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_updated`(`last_updated`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'KPI指标快照表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for mv_asset_statistics
-- ----------------------------
DROP TABLE IF EXISTS `mv_asset_statistics`;
CREATE TABLE `mv_asset_statistics`  (
  `dimension_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `dimension_key` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `dimension_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `total_count` int NULL DEFAULT 0,
  `in_use_count` int NULL DEFAULT 0,
  `fault_count` int NULL DEFAULT 0,
  `total_value` decimal(18, 2) NULL DEFAULT 0.00,
  `in_use_rate` decimal(5, 2) NULL DEFAULT 0.00,
  `fault_rate` decimal(5, 2) NULL DEFAULT 0.00,
  `last_updated` timestamp(0) NULL DEFAULT CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`dimension_type`, `dimension_key`) USING BTREE,
  INDEX `idx_type_updated`(`dimension_type`, `last_updated`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '资产统计快照表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for notifications
-- ----------------------------
DROP TABLE IF EXISTS `notifications`;
CREATE TABLE `notifications`  (
  `NotificationId` bigint NOT NULL AUTO_INCREMENT COMMENT '通知ID (BIGINT)',
  `UserId` int NOT NULL COMMENT '接收用户ID (关联 users.Id - INT)',
  `Title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `Content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `Type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '通知类型：task/system/alert/gamification',
  `ReferenceId` bigint NULL DEFAULT NULL COMMENT '关联对象ID (可能是 TaskId, BadgeId etc - BIGINT)',
  `ReferenceType` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '关联对象类型 (Task, Badge, etc)',
  `IsRead` tinyint(1) NOT NULL DEFAULT 0,
  `CreationTimestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
  `ReadTimestamp` datetime(0) NULL DEFAULT NULL,
  PRIMARY KEY (`NotificationId`) USING BTREE,
  INDEX `idx_notifications_user_read_time`(`UserId`, `IsRead`, `CreationTimestamp`) USING BTREE,
  CONSTRAINT `FK_Notifications_User` FOREIGN KEY (`UserId`) REFERENCES `users` (`Id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 160 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '通知表 (BIGINT PK)' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for pdcaplans
-- ----------------------------
DROP TABLE IF EXISTS `pdcaplans`;
CREATE TABLE `pdcaplans`  (
  `pdca_plan_id` bigint NOT NULL AUTO_INCREMENT,
  `task_id` bigint NOT NULL,
  `creator_user_id` int NOT NULL,
  `responsible_person_id` int NOT NULL,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `stage` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `goal` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
  `plan_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
  `do_record` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
  `check_result` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
  `act_action` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
  `status` int NOT NULL,
  `completion_rate` decimal(5, 2) NOT NULL,
  `notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
  `creation_timestamp` datetime(6) NOT NULL,
  `last_updated_timestamp` datetime(6) NOT NULL,
  PRIMARY KEY (`pdca_plan_id`) USING BTREE,
  UNIQUE INDEX `IX_pdcaplans_task_id`(`task_id`) USING BTREE,
  INDEX `IX_pdcaplans_creator_user_id`(`creator_user_id`) USING BTREE,
  INDEX `IX_pdcaplans_responsible_person_id`(`responsible_person_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for periodic_task_schedule_assignees
-- ----------------------------
DROP TABLE IF EXISTS `periodic_task_schedule_assignees`;
CREATE TABLE `periodic_task_schedule_assignees`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `periodic_task_schedule_id` bigint NOT NULL,
  `user_id` int NOT NULL,
  `created_at` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_schedule_user`(`periodic_task_schedule_id`, `user_id`) USING BTREE,
  INDEX `idx_schedule_id`(`periodic_task_schedule_id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 31 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for periodictaskscheduleassignees
-- ----------------------------
DROP TABLE IF EXISTS `periodictaskscheduleassignees`;
CREATE TABLE `periodictaskscheduleassignees`  (
  `Id` bigint NOT NULL AUTO_INCREMENT,
  `PeriodicTaskScheduleId` bigint NOT NULL,
  `UserId` int NOT NULL,
  `CreatedAt` datetime(6) NOT NULL,
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE INDEX `uk_schedule_user`(`PeriodicTaskScheduleId`, `UserId`) USING BTREE,
  INDEX `idx_schedule_id`(`PeriodicTaskScheduleId`) USING BTREE,
  INDEX `idx_user_id`(`UserId`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for periodictaskschedules
-- ----------------------------
DROP TABLE IF EXISTS `periodictaskschedules`;
CREATE TABLE `periodictaskschedules`  (
  `PeriodicTaskScheduleId` bigint NOT NULL AUTO_INCREMENT,
  `TemplateTaskId` bigint NOT NULL,
  `CreatorUserId` int NOT NULL,
  `Name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `Description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
  `RecurrenceType` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'Daily',
  `RecurrenceInterval` int NOT NULL DEFAULT 1,
  `DaysOfWeek` json NULL,
  `DayOfMonth` int NULL DEFAULT NULL,
  `WeekOfMonth` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `DayOfWeekForMonth` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `MonthOfYear` int NULL DEFAULT NULL,
  `CronExpression` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `StartDate` datetime(6) NOT NULL,
  `EndConditionType` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'Never',
  `EndDate` datetime(6) NULL DEFAULT NULL,
  `TotalOccurrences` int NULL DEFAULT NULL,
  `OccurrencesGenerated` int NOT NULL DEFAULT 0,
  `NextGenerationTime` datetime(6) NOT NULL,
  `Status` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'Active',
  `LastGeneratedTimestamp` datetime(6) NULL DEFAULT NULL,
  `LastError` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
  `DefaultPoints` int NOT NULL DEFAULT 0,
  `CreationTimestamp` datetime(6) NOT NULL,
  `LastUpdatedTimestamp` datetime(6) NOT NULL,
  PRIMARY KEY (`PeriodicTaskScheduleId`) USING BTREE,
  INDEX `IX_PeriodicTaskSchedules_CreatorUserId`(`CreatorUserId`) USING BTREE,
  INDEX `IX_PeriodicTaskSchedules_TemplateTaskId`(`TemplateTaskId`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for personnel
-- ----------------------------
DROP TABLE IF EXISTS `personnel`;
CREATE TABLE `personnel`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `position` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `contact` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `department_id` int NULL DEFAULT NULL,
  `employee_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `created_at` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0),
  `updated_at` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `department_id`(`department_id`) USING BTREE,
  CONSTRAINT `personnel_ibfk_1` FOREIGN KEY (`department_id`) REFERENCES `departments` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 20 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for point_leaderboard
-- ----------------------------
DROP TABLE IF EXISTS `point_leaderboard`;
CREATE TABLE `point_leaderboard`  (
  `LeaderboardEntryId` bigint NOT NULL AUTO_INCREMENT COMMENT '排行榜条目ID (BIGINT)',
  `UserId` int NOT NULL COMMENT '用户ID (关联 users.Id - INT)',
  `LeaderboardType` int NOT NULL COMMENT '排行榜类型 (e.g., 1=Weekly, 2=Monthly, 3=AllTime)',
  `LeaderboardPeriod` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '排行榜周期标识 (e.g., 2024-W20, 2024-05, AllTime)',
  `Points` int NOT NULL DEFAULT 0,
  `Rank` int NOT NULL,
  `CreationTimestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
  `LastUpdatedTimestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`LeaderboardEntryId`) USING BTREE,
  UNIQUE INDEX `idx_leaderboard_unique`(`UserId`, `LeaderboardType`, `LeaderboardPeriod`) USING BTREE,
  INDEX `idx_leaderboard_type_period_rank`(`LeaderboardType`, `LeaderboardPeriod`, `Rank`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '积分排行榜 (BIGINT PK)' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for purchaseitems
-- ----------------------------
DROP TABLE IF EXISTS `purchaseitems`;
CREATE TABLE `purchaseitems`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `PurchaseOrderId` int NOT NULL COMMENT '采购订单ID',
  `ItemName` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '项目名称',
  `ItemCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '项目编码',
  `Specification` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '规格',
  `AssetTypeId` int NULL DEFAULT NULL COMMENT '资产类型ID',
  `UnitPrice` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '单价',
  `Quantity` int NOT NULL DEFAULT 1 COMMENT '数量',
  `TotalPrice` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '总价',
  `Notes` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `CreatedAt` datetime(0) NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime(0) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`) USING BTREE,
  INDEX `IX_PurchaseItems_PurchaseOrderId`(`PurchaseOrderId`) USING BTREE,
  INDEX `IX_PurchaseItems_AssetTypeId`(`AssetTypeId`) USING BTREE,
  CONSTRAINT `FK_PurchaseItems_AssetTypes_AssetTypeId` FOREIGN KEY (`AssetTypeId`) REFERENCES `assettypes` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_PurchaseItems_PurchaseOrders_PurchaseOrderId` FOREIGN KEY (`PurchaseOrderId`) REFERENCES `purchaseorders` (`Id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 21 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '采购项目表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for purchaseorders
-- ----------------------------
DROP TABLE IF EXISTS `purchaseorders`;
CREATE TABLE `purchaseorders`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `OrderCode` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '采购单号(PO-年月日-时分秒)',
  `Title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '标题',
  `Description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '描述',
  `SupplierId` int NOT NULL COMMENT '供应商ID',
  `Status` int NOT NULL DEFAULT 0 COMMENT '状态：0草稿，1待审批，2已审批，3已发出，4已收货，5已完成，6已取消',
  `EstimatedDeliveryDate` datetime(0) NULL DEFAULT NULL COMMENT '预计交付日期',
  `ActualDeliveryDate` datetime(0) NULL DEFAULT NULL COMMENT '实际交付日期',
  `ApplicantId` int NOT NULL COMMENT '申请人ID',
  `ApplicationTime` datetime(0) NOT NULL COMMENT '申请时间',
  `ApproverId` int NULL DEFAULT NULL COMMENT '审批人ID',
  `ApprovalTime` datetime(0) NULL DEFAULT NULL COMMENT '审批时间',
  `TotalAmount` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '总金额',
  `Notes` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `CreatedAt` datetime(0) NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime(0) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE INDEX `IX_PurchaseOrders_OrderCode`(`OrderCode`) USING BTREE,
  INDEX `IX_PurchaseOrders_SupplierId`(`SupplierId`) USING BTREE,
  INDEX `IX_PurchaseOrders_ApplicantId`(`ApplicantId`) USING BTREE,
  INDEX `IX_PurchaseOrders_ApproverId`(`ApproverId`) USING BTREE,
  CONSTRAINT `FK_PurchaseOrders_Suppliers_SupplierId` FOREIGN KEY (`SupplierId`) REFERENCES `suppliers` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_PurchaseOrders_Users_ApplicantId` FOREIGN KEY (`ApplicantId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_PurchaseOrders_Users_ApproverId` FOREIGN KEY (`ApproverId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 16 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '采购订单表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for quick_memo_categories
-- ----------------------------
DROP TABLE IF EXISTS `quick_memo_categories`;
CREATE TABLE `quick_memo_categories`  (
  `id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `color` varchar(7) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `user_id` int NOT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `ix_quick_memo_categories_user_name_unique`(`user_id`, `name`) USING BTREE,
  CONSTRAINT `FK_quick_memo_categories_users_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`Id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for quick_memos
-- ----------------------------
DROP TABLE IF EXISTS `quick_memos`;
CREATE TABLE `quick_memos`  (
  `id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
  `user_id` int NOT NULL,
  `category_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `is_pinned` tinyint(1) NOT NULL DEFAULT 0,
  `color` varchar(7) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  `PositionX` int NOT NULL DEFAULT 0,
  `PositionY` int NOT NULL DEFAULT 0,
  `SizeWidth` int NOT NULL DEFAULT 200,
  `SizeHeight` int NOT NULL DEFAULT 180,
  `ZIndex` int NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `IX_quick_memos_category_id`(`category_id`) USING BTREE,
  INDEX `ix_quick_memos_user_category`(`user_id`, `category_id`) USING BTREE,
  INDEX `ix_quick_memos_user_id`(`user_id`) USING BTREE,
  INDEX `ix_quick_memos_user_pinned_updated`(`user_id`, `is_pinned`, `updated_at`) USING BTREE,
  CONSTRAINT `FK_quick_memos_quick_memo_categories_category_id` FOREIGN KEY (`category_id`) REFERENCES `quick_memo_categories` (`id`) ON DELETE SET NULL ON UPDATE RESTRICT,
  CONSTRAINT `FK_quick_memos_users_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`Id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for refreshtokens
-- ----------------------------
DROP TABLE IF EXISTS `refreshtokens`;
CREATE TABLE `refreshtokens`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `UserId` int NOT NULL COMMENT '用户ID',
  `Token` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '令牌',
  `JwtId` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'JWT ID',
  `IsUsed` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已使用',
  `IsRevoked` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已撤销',
  `AddedDate` datetime(0) NOT NULL COMMENT '添加日期',
  `ExpiryDate` datetime(0) NOT NULL COMMENT '过期日期',
  PRIMARY KEY (`Id`) USING BTREE,
  INDEX `IX_RefreshTokens_UserId`(`UserId`) USING BTREE,
  CONSTRAINT `FK_RefreshTokens_Users_UserId` FOREIGN KEY (`UserId`) REFERENCES `users` (`Id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '刷新令牌表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for repairitems
-- ----------------------------
DROP TABLE IF EXISTS `repairitems`;
CREATE TABLE `repairitems`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `RepairOrderId` int NOT NULL COMMENT '关联返修单主表ID',
  `item_type` tinyint NOT NULL DEFAULT 1 COMMENT '物品类型: 1=备件, 2=资产组件',
  `part_id` int NULL DEFAULT NULL COMMENT '备件ID（当item_type=1时）',
  `component_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '组件名称',
  `serial_number` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '序列号',
  `quantity` int NOT NULL DEFAULT 1 COMMENT '数量',
  `before_status` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '维修前状态',
  `after_status` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '维修后状态',
  `AssetId` int NOT NULL COMMENT '关联资产ID',
  `FaultRecordId` int NULL DEFAULT NULL COMMENT '关联故障单ID',
  `Description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '故障描述补充',
  `RepairCost` decimal(18, 2) NULL DEFAULT 0.00 COMMENT '单项维修费用',
  `RepairStatus` int NOT NULL DEFAULT 0 COMMENT '维修状态: 0-待送修, 1-检测中, 2-维修中, 3-待返回, 4-已修复, 5-修复失败',
  `RepairResult` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '维修结果描述',
  PRIMARY KEY (`Id`) USING BTREE,
  INDEX `IX_RepairItems_RepairOrderId`(`RepairOrderId`) USING BTREE,
  INDEX `IX_RepairItems_AssetId`(`AssetId`) USING BTREE,
  INDEX `IX_RepairItems_FaultRecordId`(`FaultRecordId`) USING BTREE,
  INDEX `idx_repair_item_part`(`part_id`) USING BTREE,
  INDEX `idx_repair_item_type`(`item_type`) USING BTREE,
  CONSTRAINT `FK_RepairItems_Assets_AssetId` FOREIGN KEY (`AssetId`) REFERENCES `assets` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_RepairItems_FaultRecords_FaultRecordId` FOREIGN KEY (`FaultRecordId`) REFERENCES `faultrecords` (`Id`) ON DELETE SET NULL ON UPDATE RESTRICT,
  CONSTRAINT `FK_RepairItems_RepairOrders_RepairOrderId` FOREIGN KEY (`RepairOrderId`) REFERENCES `repairorders` (`Id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '返厂维修单明细表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for repairorders
-- ----------------------------
DROP TABLE IF EXISTS `repairorders`;
CREATE TABLE `repairorders`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `OrderCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '返修单号 (REP-YYYYMMDD-XXX)',
  `repair_type` tinyint NOT NULL DEFAULT 2 COMMENT '维修类型: 1=故障维修, 2=备件维修, 3=预防性维修',
  `title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '维修标题',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '维修描述',
  `priority` tinyint NOT NULL DEFAULT 3 COMMENT '优先级: 1=紧急, 2=高, 3=中, 4=低',
  `fault_id` int NULL DEFAULT NULL COMMENT '关联故障ID',
  `asset_id` int NULL DEFAULT NULL COMMENT '关联资产ID',
  `estimated_cost` decimal(10, 2) NULL DEFAULT NULL COMMENT '预估费用',
  `approver_id` int NULL DEFAULT NULL COMMENT '审核人ID',
  `SupplierId` int NOT NULL COMMENT '维修服务商ID',
  `SendDate` datetime(0) NULL DEFAULT NULL COMMENT '返厂寄出日期',
  `ExpectedReturnDate` datetime(0) NULL DEFAULT NULL COMMENT '预计返回日期',
  `ActualReturnDate` datetime(0) NULL DEFAULT NULL COMMENT '实际返回日期',
  `TotalCost` decimal(18, 2) NULL DEFAULT 0.00 COMMENT '维修总费用',
  `Status` int NOT NULL DEFAULT 0 COMMENT '返修单状态: 0-草稿, 1-待返厂, 2-维修中, 3-部分返回, 4-已完成',
  `Notes` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `CreatorId` int NOT NULL COMMENT '创建人ID',
  `CreatedAt` datetime(0) NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime(0) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE INDEX `IX_RepairOrders_OrderCode`(`OrderCode`) USING BTREE,
  INDEX `IX_RepairOrders_SupplierId`(`SupplierId`) USING BTREE,
  INDEX `IX_RepairOrders_CreatorId`(`CreatorId`) USING BTREE,
  INDEX `idx_repair_type`(`repair_type`) USING BTREE,
  INDEX `idx_repair_priority`(`priority`) USING BTREE,
  INDEX `idx_repair_fault`(`fault_id`) USING BTREE,
  INDEX `idx_repair_asset`(`asset_id`) USING BTREE,
  CONSTRAINT `FK_RepairOrders_Suppliers_SupplierId` FOREIGN KEY (`SupplierId`) REFERENCES `suppliers` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_RepairOrders_Users_CreatorId` FOREIGN KEY (`CreatorId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '返厂维修单主表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for returntofactories
-- ----------------------------
DROP TABLE IF EXISTS `returntofactories`;
CREATE TABLE `returntofactories`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `Code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '返厂单号',
  `AssetId` int NOT NULL COMMENT '资产ID',
  `FaultRecordId` int NOT NULL COMMENT '故障记录ID',
  `SupplierId` int NOT NULL COMMENT '供应商ID',
  `Status` int NOT NULL DEFAULT 0 COMMENT '状态：0待送出，1已送出，2维修中，3已返回，4维修失败',
  `SenderId` int NOT NULL COMMENT '送出人ID',
  `SendTime` datetime(0) NULL DEFAULT NULL COMMENT '送出时间',
  `EstimatedReturnTime` datetime(0) NULL DEFAULT NULL COMMENT '预计返回时间',
  `ActualReturnTime` datetime(0) NULL DEFAULT NULL COMMENT '实际返回时间',
  `RepairResult` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '维修结果',
  `RepairCost` decimal(18, 2) NULL DEFAULT NULL COMMENT '维修费用',
  `InWarranty` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否在保修期内',
  `Notes` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `CreatedAt` datetime(0) NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime(0) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE INDEX `IX_ReturnToFactories_Code`(`Code`) USING BTREE,
  INDEX `IX_ReturnToFactories_AssetId`(`AssetId`) USING BTREE,
  INDEX `IX_ReturnToFactories_FaultRecordId`(`FaultRecordId`) USING BTREE,
  INDEX `IX_ReturnToFactories_SupplierId`(`SupplierId`) USING BTREE,
  INDEX `IX_ReturnToFactories_SenderId`(`SenderId`) USING BTREE,
  CONSTRAINT `FK_ReturnToFactories_Assets_AssetId` FOREIGN KEY (`AssetId`) REFERENCES `assets` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_ReturnToFactories_FaultRecords_FaultRecordId` FOREIGN KEY (`FaultRecordId`) REFERENCES `faultrecords` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_ReturnToFactories_Suppliers_SupplierId` FOREIGN KEY (`SupplierId`) REFERENCES `suppliers` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_ReturnToFactories_Users_SenderId` FOREIGN KEY (`SenderId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '返厂维修表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rolemenus
-- ----------------------------
DROP TABLE IF EXISTS `rolemenus`;
CREATE TABLE `rolemenus`  (
  `RoleId` int NOT NULL COMMENT '角色ID',
  `MenuId` int NOT NULL COMMENT '菜单ID',
  `CreatedAt` datetime(0) NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`RoleId`, `MenuId`) USING BTREE,
  INDEX `IX_RoleMenus_MenuId`(`MenuId`) USING BTREE,
  CONSTRAINT `FK_RoleMenus_Menus_MenuId` FOREIGN KEY (`MenuId`) REFERENCES `menus` (`Id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `FK_RoleMenus_Roles_RoleId` FOREIGN KEY (`RoleId`) REFERENCES `roles` (`Id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '角色菜单关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for roles
-- ----------------------------
DROP TABLE IF EXISTS `roles`;
CREATE TABLE `roles`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `Name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '角色名称',
  `Code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '角色编码',
  `Description` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '角色描述',
  `IsActive` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否激活',
  `CreatedAt` datetime(0) NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime(0) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE INDEX `IX_Roles_Code`(`Code`) USING BTREE,
  UNIQUE INDEX `IX_Roles_Name`(`Name`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '角色表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for spare_part_inventories
-- ----------------------------
DROP TABLE IF EXISTS `spare_part_inventories`;
CREATE TABLE `spare_part_inventories`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `part_id` int NOT NULL COMMENT '备件ID',
  `location_id` int NOT NULL COMMENT '库位ID',
  `status_id` int NOT NULL COMMENT '状态ID',
  `quantity` int NOT NULL DEFAULT 0 COMMENT '数量',
  `batch_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '批次号',
  `serial_numbers` json NULL COMMENT '序列号列表（JSON数组）',
  `purchase_date` date NULL DEFAULT NULL COMMENT '采购日期',
  `warranty_expire_date` date NULL DEFAULT NULL COMMENT '保修到期日期',
  `supplier_id` int NULL DEFAULT NULL COMMENT '供应商ID',
  `unit_cost` decimal(10, 2) NULL DEFAULT NULL COMMENT '单位成本',
  `notes` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `created_at` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
  `updated_at` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_inventory_part`(`part_id`) USING BTREE,
  INDEX `idx_inventory_location`(`location_id`) USING BTREE,
  INDEX `idx_inventory_status`(`status_id`) USING BTREE,
  INDEX `idx_inventory_batch`(`batch_number`) USING BTREE,
  CONSTRAINT `spare_part_inventories_ibfk_1` FOREIGN KEY (`part_id`) REFERENCES `spare_parts` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `spare_part_inventories_ibfk_2` FOREIGN KEY (`location_id`) REFERENCES `spare_part_locations` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `spare_part_inventories_ibfk_3` FOREIGN KEY (`status_id`) REFERENCES `spare_part_status_types` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 33 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '备件库存明细表（按状态分类）' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for spare_part_locations
-- ----------------------------
DROP TABLE IF EXISTS `spare_part_locations`;
CREATE TABLE `spare_part_locations`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '库位编号 (例如 A-1-1-1)',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '库位名称 (例如 A区-1号货架-1层-1格)',
  `area` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '区域标识 (如 A区, B区)',
  `description` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '库位描述',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
  `created_at` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
  `updated_at` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_spare_part_locations_code`(`code`) USING BTREE,
  INDEX `idx_spare_part_locations_area`(`area`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '备品备件库位表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for spare_part_status_histories
-- ----------------------------
DROP TABLE IF EXISTS `spare_part_status_histories`;
CREATE TABLE `spare_part_status_histories`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `inventory_id` bigint NOT NULL COMMENT '库存明细ID',
  `from_status_id` int NULL DEFAULT NULL COMMENT '变更前状态',
  `to_status_id` int NOT NULL COMMENT '变更后状态',
  `quantity` int NOT NULL COMMENT '变更数量',
  `reason` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '变更原因',
  `operator_id` int NOT NULL COMMENT '操作人ID',
  `transaction_id` int NULL DEFAULT NULL COMMENT '关联交易记录ID',
  `repair_order_id` int NULL DEFAULT NULL COMMENT '关联返厂单ID',
  `created_at` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_status_history_inventory`(`inventory_id`) USING BTREE,
  INDEX `idx_status_history_from_status`(`from_status_id`) USING BTREE,
  INDEX `idx_status_history_to_status`(`to_status_id`) USING BTREE,
  INDEX `idx_status_history_operator`(`operator_id`) USING BTREE,
  CONSTRAINT `spare_part_status_histories_ibfk_1` FOREIGN KEY (`inventory_id`) REFERENCES `spare_part_inventories` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `spare_part_status_histories_ibfk_2` FOREIGN KEY (`from_status_id`) REFERENCES `spare_part_status_types` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `spare_part_status_histories_ibfk_3` FOREIGN KEY (`to_status_id`) REFERENCES `spare_part_status_types` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `spare_part_status_histories_ibfk_4` FOREIGN KEY (`operator_id`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '备件状态变更历史表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for spare_part_status_types
-- ----------------------------
DROP TABLE IF EXISTS `spare_part_status_types`;
CREATE TABLE `spare_part_status_types`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '状态代码',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '状态名称',
  `category` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '状态分类: Available, Unavailable, InTransit, Reserved',
  `color` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '显示颜色',
  `icon` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '图标',
  `description` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '状态描述',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
  `sort_order` int NOT NULL DEFAULT 0 COMMENT '排序',
  `created_at` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
  `updated_at` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_status_code`(`code`) USING BTREE,
  INDEX `idx_status_category`(`category`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 12 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '备件状态类型表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for spare_part_transactions
-- ----------------------------
DROP TABLE IF EXISTS `spare_part_transactions`;
CREATE TABLE `spare_part_transactions`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `part_id` int NOT NULL COMMENT '备件ID',
  `type` tinyint NOT NULL COMMENT '类型:1入库,2出库',
  `quantity` int NOT NULL COMMENT '数量',
  `user_id` int NOT NULL COMMENT '操作人ID',
  `location_id` int NOT NULL COMMENT '库位ID (关联 spare_part_locations.id)',
  `reference` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '关联单号 (采购单、维修单等)',
  `reason_type` tinyint NULL DEFAULT NULL COMMENT '原因类型:1采购,2退回,3领用,4报废,5盘点调整等',
  `reason` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '原因/用途详细描述',
  `from_status_id` int NULL DEFAULT NULL COMMENT '变更前状态ID',
  `to_status_id` int NULL DEFAULT NULL COMMENT '变更后状态ID',
  `inventory_id` bigint NULL DEFAULT NULL COMMENT '关联库存明细ID',
  `repair_order_id` int NULL DEFAULT NULL COMMENT '关联返厂单ID',
  `related_asset_id` int NULL DEFAULT NULL COMMENT '关联资产ID (若出库用于特定资产)',
  `related_fault_id` int NULL DEFAULT NULL COMMENT '关联故障ID (若出库用于特定维修)',
  `transaction_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
  `batch_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '记录批次号，用于将相同批次的多个记录关联',
  `stock_after` int NULL DEFAULT NULL COMMENT '操作后库存',
  `is_system_generated` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否由系统自动生成',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_transactions_part`(`part_id`) USING BTREE,
  INDEX `idx_transactions_user`(`user_id`) USING BTREE,
  INDEX `idx_transactions_time`(`transaction_time`) USING BTREE,
  INDEX `idx_transactions_type`(`type`) USING BTREE,
  INDEX `idx_transaction_from_status`(`from_status_id`) USING BTREE,
  INDEX `idx_transaction_to_status`(`to_status_id`) USING BTREE,
  INDEX `idx_transaction_inventory`(`inventory_id`) USING BTREE,
  INDEX `idx_transaction_repair_order`(`repair_order_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 49 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for spare_part_types
-- ----------------------------
DROP TABLE IF EXISTS `spare_part_types`;
CREATE TABLE `spare_part_types`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '类型名称',
  `code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '类型编码',
  `parent_id` int NULL DEFAULT NULL COMMENT '父类型ID',
  `path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '类型路径 (例如 1,5,15)',
  `level` int NOT NULL DEFAULT 1 COMMENT '层级',
  `description` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '描述',
  `created_at` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
  `updated_at` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_part_types_code`(`code`) USING BTREE,
  INDEX `idx_part_types_parent`(`parent_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 12 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for spare_parts
-- ----------------------------
DROP TABLE IF EXISTS `spare_parts`;
CREATE TABLE `spare_parts`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '备件编号',
  `material_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '物料编号',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '备件名称',
  `type_id` int NOT NULL COMMENT '备件类型ID',
  `spec` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '规格型号',
  `brand` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '品牌',
  `quantity` int NOT NULL DEFAULT 0 COMMENT '当前库存量',
  `unit` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '单位',
  `min_threshold` int NOT NULL DEFAULT 5 COMMENT '最小安全库存',
  `warning_threshold` int NOT NULL DEFAULT 10 COMMENT '预警库存',
  `location_id` int NOT NULL COMMENT '库位ID (关联 spare_part_locations.id)',
  `purchase_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '采购价格',
  `supplier_id` int NULL DEFAULT NULL COMMENT '默认供应商',
  `notes` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `created_at` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
  `updated_at` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_spare_parts_code`(`code`) USING BTREE,
  INDEX `idx_spare_parts_type`(`type_id`) USING BTREE,
  INDEX `idx_spare_parts_location`(`location_id`) USING BTREE,
  INDEX `idx_spare_parts_material_number`(`material_number`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 35 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for suppliers
-- ----------------------------
DROP TABLE IF EXISTS `suppliers`;
CREATE TABLE `suppliers`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `Name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '供应商名称',
  `Code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '供应商编码',
  `ContactPerson` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '联系人',
  `ContactPhone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '联系电话',
  `ContactEmail` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '联系邮箱',
  `Address` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '地址',
  `Type` int NOT NULL DEFAULT 0 COMMENT '类型：0硬件，1软件，2服务',
  `Notes` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `IsActive` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否激活',
  `CreatedAt` datetime(0) NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime(0) NOT NULL COMMENT '更新时间',
  `supplier_type` int NOT NULL DEFAULT 1 COMMENT '供应商类型：1=采购，2=维修，3=采购+维修',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE INDEX `IX_Suppliers_Code`(`Code`) USING BTREE,
  UNIQUE INDEX `IX_Suppliers_Name`(`Name`) USING BTREE,
  INDEX `idx_suppliers_supplier_type`(`supplier_type`) USING BTREE,
  INDEX `idx_suppliers_is_active`(`IsActive`) USING BTREE,
  INDEX `idx_suppliers_type_active`(`supplier_type`, `IsActive`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 17 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '供应商表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for task_claims
-- ----------------------------
DROP TABLE IF EXISTS `task_claims`;
CREATE TABLE `task_claims`  (
  `claim_id` bigint NOT NULL AUTO_INCREMENT,
  `task_id` bigint NOT NULL COMMENT '任务ID',
  `claimed_by` int NOT NULL COMMENT '领取用户ID',
  `shift_id` bigint NOT NULL COMMENT '班次ID',
  `claimed_at` datetime(0) NOT NULL COMMENT '领取时间',
  `claim_date` date NOT NULL COMMENT '领取日期',
  `claim_status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'Claimed' COMMENT '领取状态 (Claimed-已领取, Started-已开始, Completed-已完成, Cancelled-已取消)',
  `started_at` datetime(0) NULL DEFAULT NULL COMMENT '开始时间',
  `completed_at` datetime(0) NULL DEFAULT NULL COMMENT '完成时间',
  `notes` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注',
  `created_at` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `updated_at` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`claim_id`) USING BTREE,
  UNIQUE INDEX `uk_task_user_date`(`task_id`, `claimed_by`, `claim_date`) USING BTREE,
  INDEX `idx_task_id`(`task_id`) USING BTREE,
  INDEX `idx_claimed_by`(`claimed_by`) USING BTREE,
  INDEX `idx_shift_id`(`shift_id`) USING BTREE,
  INDEX `idx_claim_date`(`claim_date`) USING BTREE,
  INDEX `idx_claim_status`(`claim_status`) USING BTREE,
  CONSTRAINT `task_claims_ibfk_1` FOREIGN KEY (`task_id`) REFERENCES `tasks` (`TaskId`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `task_claims_ibfk_2` FOREIGN KEY (`claimed_by`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `task_claims_ibfk_3` FOREIGN KEY (`shift_id`) REFERENCES `work_shifts` (`shift_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 10 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '任务领取记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for task_reminders
-- ----------------------------
DROP TABLE IF EXISTS `task_reminders`;
CREATE TABLE `task_reminders`  (
  `reminder_id` bigint NOT NULL AUTO_INCREMENT,
  `task_id` bigint NOT NULL COMMENT '任务ID',
  `reminder_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'BeforeDeadline' COMMENT '提醒类型 (BeforeStart-开始前, BeforeDeadline-截止前, Overdue-逾期)',
  `offset_minutes` int NOT NULL COMMENT '提醒时间偏移（分钟）',
  `reminder_level` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'Warning' COMMENT '提醒级别 (Info-信息, Warning-警告, Critical-严重)',
  `reminder_method` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'System' COMMENT '提醒方式 (System-系统通知, Email-邮件, SMS-短信, All-全部)',
  `is_recurring` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否重复提醒',
  `recurring_interval` int NULL DEFAULT NULL COMMENT '重复间隔（分钟）',
  `max_occurrences` int NULL DEFAULT NULL COMMENT '最大重复次数',
  `sent_count` int NOT NULL DEFAULT 0 COMMENT '已发送次数',
  `next_reminder_time` datetime(0) NULL DEFAULT NULL COMMENT '下次提醒时间',
  `last_sent_at` datetime(0) NULL DEFAULT NULL COMMENT '最后发送时间',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
  `created_at` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `updated_at` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  `created_by` int NOT NULL COMMENT '创建用户ID',
  `updated_by` int NULL DEFAULT NULL COMMENT '更新用户ID',
  PRIMARY KEY (`reminder_id`) USING BTREE,
  INDEX `idx_task_id`(`task_id`) USING BTREE,
  INDEX `idx_next_reminder_time`(`next_reminder_time`) USING BTREE,
  INDEX `idx_active_reminder`(`is_active`, `next_reminder_time`) USING BTREE,
  INDEX `idx_reminder_type`(`reminder_type`) USING BTREE,
  CONSTRAINT `task_reminders_ibfk_1` FOREIGN KEY (`task_id`) REFERENCES `tasks` (`TaskId`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '任务提醒配置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for taskassignees
-- ----------------------------
DROP TABLE IF EXISTS `taskassignees`;
CREATE TABLE `taskassignees`  (
  `TaskAssigneeId` bigint NOT NULL AUTO_INCREMENT COMMENT '分配ID (BIGINT)',
  `TaskId` bigint NOT NULL COMMENT '任务ID',
  `UserId` int NOT NULL COMMENT '用户ID (关联 users.Id - INT)',
  `AssignmentType` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'Responsible' COMMENT '分配类型 (Responsible, Participant)',
  `AssignedByUserId` int NOT NULL COMMENT '分配人用户ID (关联 users.Id - INT)',
  `AssignmentTimestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '分配时间',
  PRIMARY KEY (`TaskAssigneeId`) USING BTREE,
  UNIQUE INDEX `idx_taskassignees_task_user_type`(`TaskId`, `UserId`, `AssignmentType`) USING BTREE,
  INDEX `idx_taskassignees_user`(`UserId`) USING BTREE,
  INDEX `idx_taskassignees_assignedby`(`AssignedByUserId`) USING BTREE,
  CONSTRAINT `FK_TaskAssignees_AssignedByUser` FOREIGN KEY (`AssignedByUserId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE CASCADE,
  CONSTRAINT `FK_TaskAssignees_Task` FOREIGN KEY (`TaskId`) REFERENCES `tasks` (`TaskId`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `FK_TaskAssignees_User` FOREIGN KEY (`UserId`) REFERENCES `users` (`Id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 209 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '任务多负责人/参与者表 (V2 - BIGINT PK)' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for taskhistories
-- ----------------------------
DROP TABLE IF EXISTS `taskhistories`;
CREATE TABLE `taskhistories`  (
  `task_history_id` bigint NOT NULL AUTO_INCREMENT,
  `task_id` bigint NOT NULL,
  `user_id` int NULL DEFAULT NULL,
  `timestamp` datetime(6) NOT NULL,
  `action_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `field_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `old_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
  `new_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
  `comment_id` bigint NULL DEFAULT NULL,
  `attachment_id` bigint NULL DEFAULT NULL,
  PRIMARY KEY (`task_history_id`) USING BTREE,
  INDEX `IX_taskhistories_task_id`(`task_id`) USING BTREE,
  INDEX `IX_taskhistories_user_id`(`user_id`) USING BTREE,
  INDEX `IX_taskhistories_timestamp`(`timestamp`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for taskhistory
-- ----------------------------
DROP TABLE IF EXISTS `taskhistory`;
CREATE TABLE `taskhistory`  (
  `TaskHistoryId` bigint NOT NULL AUTO_INCREMENT COMMENT '历史记录ID (BIGINT)',
  `TaskId` bigint NOT NULL COMMENT '关联的任务ID',
  `UserId` int NULL DEFAULT NULL COMMENT '操作用户ID (系统操作可为NULL, 关联 users.Id - INT)',
  `Timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '操作时间',
  `ActionType` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '操作类型',
  `FieldName` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '变更的字段名',
  `OldValue` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '旧值 (建议JSON)',
  `NewValue` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '新值 (建议JSON)',
  `CommentId` bigint NULL DEFAULT NULL COMMENT '关联的评论ID',
  `AttachmentId` bigint NULL DEFAULT NULL COMMENT '关联的附件ID',
  `Description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '操作的文字描述',
  PRIMARY KEY (`TaskHistoryId`) USING BTREE,
  INDEX `idx_taskhistory_task_time`(`TaskId`, `Timestamp`) USING BTREE,
  INDEX `idx_taskhistory_user`(`UserId`) USING BTREE,
  INDEX `idx_taskhistory_action`(`ActionType`) USING BTREE,
  INDEX `idx_taskhistory_comment`(`CommentId`) USING BTREE,
  INDEX `idx_taskhistory_attachment`(`AttachmentId`) USING BTREE,
  CONSTRAINT `FK_TaskHistory_Attachment` FOREIGN KEY (`AttachmentId`) REFERENCES `attachments` (`AttachmentId`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `FK_TaskHistory_Comment` FOREIGN KEY (`CommentId`) REFERENCES `comments` (`CommentId`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `FK_TaskHistory_Task` FOREIGN KEY (`TaskId`) REFERENCES `tasks` (`TaskId`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `FK_TaskHistory_User` FOREIGN KEY (`UserId`) REFERENCES `users` (`Id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 382 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '任务变更历史记录表 (V2 - BIGINT PK)' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for tasks
-- ----------------------------
DROP TABLE IF EXISTS `tasks`;
CREATE TABLE `tasks`  (
  `TaskId` bigint NOT NULL AUTO_INCREMENT COMMENT '任务主键ID (BIGINT)',
  `ProjectId` bigint NULL DEFAULT NULL COMMENT '所属项目ID (如果需要，也应为 BIGINT)',
  `ParentTaskId` bigint NULL DEFAULT NULL COMMENT '父任务ID (指向自身 TaskId)',
  `CreatorUserId` int NOT NULL COMMENT '创建者用户ID (关联 users.Id - INT)',
  `AssigneeUserId` int NULL DEFAULT NULL COMMENT '负责人用户ID (关联 users.Id - INT)',
  `AssetId` int NULL DEFAULT NULL COMMENT '关联资产ID (关联 assets.Id - INT)',
  `LocationId` int NULL DEFAULT NULL COMMENT '关联位置ID (关联 locations.Id - INT)',
  `Name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '任务名称',
  `Description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '任务描述 (支持Markdown或富文本)',
  `Status` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'Todo' COMMENT '任务状态 (使用字符串)',
  `Priority` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'Medium' COMMENT '优先级 (使用字符串)',
  `TaskType` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'Normal' COMMENT '任务类型 (Normal, Periodic, PDCA)',
  `PlanStartDate` datetime(0) NULL DEFAULT NULL COMMENT '计划开始时间',
  `PlanEndDate` datetime(0) NULL DEFAULT NULL COMMENT '计划结束时间 (截止时间)',
  `ActualStartDate` datetime(0) NULL DEFAULT NULL COMMENT '实际开始时间',
  `ActualEndDate` datetime(0) NULL DEFAULT NULL COMMENT '实际完成时间',
  `CreationTimestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `LastUpdatedTimestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '最后更新时间',
  `IsOverdueAcknowledged` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逾期是否已知晓/处理',
  `PDCAStage` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'PDCA阶段 (Plan, Do, Check, Act)',
  `PreviousInstanceTaskId` bigint NULL DEFAULT NULL COMMENT '周期性任务的前一个实例ID (指向自身 TaskId)',
  `PeriodicTaskScheduleId` bigint NULL DEFAULT NULL COMMENT '关联的周期性任务计划ID',
  `Progress` int NOT NULL DEFAULT 0 COMMENT '任务进度百分比 (0-100)',
  `Points` int NOT NULL DEFAULT 0 COMMENT '完成任务可获得的基础积分/XP',
  `CompletedByUserId` int NULL DEFAULT NULL COMMENT '完成用户ID (用于水印显示)',
  `CompletedAt` datetime(0) NULL DEFAULT NULL COMMENT '任务完成时间',
  `CompletionWatermarkColor` varchar(7) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '完成用户水印颜色 (十六进制颜色代码)',
  `IsDeleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '软删除标记',
  PRIMARY KEY (`TaskId`) USING BTREE,
  INDEX `idx_tasks_status`(`Status`) USING BTREE,
  INDEX `idx_tasks_assignee`(`AssigneeUserId`) USING BTREE,
  INDEX `idx_tasks_creator`(`CreatorUserId`) USING BTREE,
  INDEX `idx_tasks_parent`(`ParentTaskId`) USING BTREE,
  INDEX `idx_tasks_plan_end_date`(`PlanEndDate`) USING BTREE,
  INDEX `idx_tasks_type`(`TaskType`) USING BTREE,
  INDEX `idx_tasks_asset`(`AssetId`) USING BTREE,
  INDEX `idx_tasks_location`(`LocationId`) USING BTREE,
  INDEX `FK_Tasks_PreviousInstanceTask`(`PreviousInstanceTaskId`) USING BTREE,
  CONSTRAINT `FK_Tasks_Asset` FOREIGN KEY (`AssetId`) REFERENCES `assets` (`Id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `FK_Tasks_AssigneeUser` FOREIGN KEY (`AssigneeUserId`) REFERENCES `users` (`Id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `FK_Tasks_CreatorUser` FOREIGN KEY (`CreatorUserId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE CASCADE,
  CONSTRAINT `FK_Tasks_Location` FOREIGN KEY (`LocationId`) REFERENCES `locations` (`Id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `FK_Tasks_ParentTask` FOREIGN KEY (`ParentTaskId`) REFERENCES `tasks` (`TaskId`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `FK_Tasks_PreviousInstanceTask` FOREIGN KEY (`PreviousInstanceTaskId`) REFERENCES `tasks` (`TaskId`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 113 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '任务主表 (V2 - BIGINT PK)' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_achievements
-- ----------------------------
DROP TABLE IF EXISTS `user_achievements`;
CREATE TABLE `user_achievements`  (
  `Id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户成就记录ID',
  `UserId` int NOT NULL COMMENT '用户ID',
  `AchievementId` bigint NOT NULL COMMENT '成就ID',
  `Progress` int NOT NULL DEFAULT 0 COMMENT '当前进度',
  `IsCompleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已完成',
  `CompletedAt` datetime(0) NULL DEFAULT NULL COMMENT '完成时间',
  `CompletedCount` int NOT NULL DEFAULT 0 COMMENT '完成次数（可重复成就）',
  `LastProgressAt` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '最后进度更新时间',
  `CreatedAt` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `UpdatedAt` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE INDEX `idx_user_achievements_user_achievement`(`UserId`, `AchievementId`) USING BTREE,
  INDEX `idx_user_achievements_user`(`UserId`) USING BTREE,
  INDEX `idx_user_achievements_achievement`(`AchievementId`) USING BTREE,
  INDEX `idx_user_achievements_completed`(`IsCompleted`, `CompletedAt`) USING BTREE,
  CONSTRAINT `FK_UserAchievements_Achievement` FOREIGN KEY (`AchievementId`) REFERENCES `achievements` (`Id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `FK_UserAchievements_User` FOREIGN KEY (`UserId`) REFERENCES `users` (`Id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户成就记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_actions
-- ----------------------------
DROP TABLE IF EXISTS `user_actions`;
CREATE TABLE `user_actions`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `action` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '行为类型',
  `reference_id` int NULL DEFAULT NULL COMMENT '关联ID',
  `reference_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '关联类型',
  `points` int NULL DEFAULT 0 COMMENT '获得积分',
  `action_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_actions_user`(`user_id`) USING BTREE,
  INDEX `idx_user_actions_time`(`action_time`) USING BTREE,
  CONSTRAINT `fk_user_actions_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`Id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for user_items
-- ----------------------------
DROP TABLE IF EXISTS `user_items`;
CREATE TABLE `user_items`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `description` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `type` int NOT NULL,
  `level` int NOT NULL DEFAULT 1,
  `power` int NOT NULL DEFAULT 0,
  `quantity` int NOT NULL DEFAULT 1,
  `is_used` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
  `updated_at` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_items_user`(`user_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for user_levels
-- ----------------------------
DROP TABLE IF EXISTS `user_levels`;
CREATE TABLE `user_levels`  (
  `Level` int NOT NULL COMMENT '等级',
  `Name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '等级名称',
  `RequiredPoints` int NOT NULL COMMENT '所需积分',
  `IconUrl` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '等级图标URL',
  `Color` varchar(7) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '等级颜色',
  `Privileges` json NULL COMMENT '等级特权',
  `CreatedAt` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`Level`) USING BTREE,
  UNIQUE INDEX `idx_user_levels_name`(`Name`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户等级配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_shift_assignments
-- ----------------------------
DROP TABLE IF EXISTS `user_shift_assignments`;
CREATE TABLE `user_shift_assignments`  (
  `assignment_id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL COMMENT '用户ID',
  `shift_id` bigint NOT NULL COMMENT '班次ID',
  `effective_date` date NOT NULL COMMENT '生效日期',
  `expiry_date` date NULL DEFAULT NULL COMMENT '失效日期',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
  `assignment_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'Permanent' COMMENT '分配类型 (Permanent-固定, Temporary-临时, Rotation-轮班)',
  `notes` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注',
  `created_at` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `updated_at` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  `created_by` int NOT NULL COMMENT '创建用户ID',
  `updated_by` int NULL DEFAULT NULL COMMENT '更新用户ID',
  PRIMARY KEY (`assignment_id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_shift_id`(`shift_id`) USING BTREE,
  INDEX `idx_effective_date`(`effective_date`) USING BTREE,
  INDEX `idx_is_active`(`is_active`) USING BTREE,
  INDEX `idx_user_shift_active`(`user_id`, `shift_id`, `is_active`) USING BTREE,
  CONSTRAINT `user_shift_assignments_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `user_shift_assignments_ibfk_2` FOREIGN KEY (`shift_id`) REFERENCES `work_shifts` (`shift_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户班次分配表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for userroles
-- ----------------------------
DROP TABLE IF EXISTS `userroles`;
CREATE TABLE `userroles`  (
  `UserId` int NOT NULL COMMENT '用户ID',
  `RoleId` int NOT NULL COMMENT '角色ID',
  `CreatedAt` datetime(0) NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`UserId`, `RoleId`) USING BTREE,
  INDEX `IX_UserRoles_RoleId`(`RoleId`) USING BTREE,
  CONSTRAINT `FK_UserRoles_Roles_RoleId` FOREIGN KEY (`RoleId`) REFERENCES `roles` (`Id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `FK_UserRoles_Users_UserId` FOREIGN KEY (`UserId`) REFERENCES `users` (`Id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户角色关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for users
-- ----------------------------
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `Username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户名',
  `PasswordHash` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '密码哈希',
  `SecurityStamp` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '安全戳',
  `Name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '姓名',
  `Email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '邮箱',
  `Mobile` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '手机号',
  `DepartmentId` int NULL DEFAULT NULL COMMENT '部门ID',
  `Position` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '职位',
  `Gender` int NOT NULL DEFAULT 0 COMMENT '性别：0未知，1男，2女',
  `DefaultRoleId` int NULL DEFAULT NULL COMMENT '默认角色ID',
  `IsActive` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否激活',
  `CreatedAt` datetime(0) NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime(0) NOT NULL COMMENT '更新时间',
  `LastLoginAt` datetime(0) NULL DEFAULT NULL COMMENT '最后登录时间',
  `Avatar` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '头像',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE INDEX `IX_Users_Username`(`Username`) USING BTREE,
  UNIQUE INDEX `IX_Users_Email`(`Email`) USING BTREE,
  INDEX `IX_Users_DepartmentId`(`DepartmentId`) USING BTREE,
  INDEX `IX_Users_DefaultRoleId`(`DefaultRoleId`) USING BTREE,
  CONSTRAINT `FK_Users_Departments_DepartmentId` FOREIGN KEY (`DepartmentId`) REFERENCES `departments` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_Users_Roles_DefaultRoleId` FOREIGN KEY (`DefaultRoleId`) REFERENCES `roles` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 22 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for view_performance_stats
-- ----------------------------
DROP TABLE IF EXISTS `view_performance_stats`;
CREATE TABLE `view_performance_stats`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `view_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `query_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `execution_time_ms` decimal(10, 3) NOT NULL,
  `record_count` int NULL DEFAULT 0,
  `query_date` date NOT NULL,
  `created_at` timestamp(0) NULL DEFAULT CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_view_date`(`view_name`, `query_date`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 17 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '视图性能统计表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for work_shifts
-- ----------------------------
DROP TABLE IF EXISTS `work_shifts`;
CREATE TABLE `work_shifts`  (
  `shift_id` bigint NOT NULL AUTO_INCREMENT,
  `shift_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '班次名称',
  `shift_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '班次代码',
  `shift_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'Day' COMMENT '班次类型 (Day-白班, Night-夜班, Swing-中班)',
  `start_time` time(0) NOT NULL COMMENT '开始时间',
  `end_time` time(0) NOT NULL COMMENT '结束时间',
  `task_claim_time` time(0) NOT NULL COMMENT '任务领取时间',
  `is_overnight` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否跨天',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
  `description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '描述',
  `created_at` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `updated_at` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  `created_by` int NOT NULL COMMENT '创建用户ID',
  `updated_by` int NULL DEFAULT NULL COMMENT '更新用户ID',
  PRIMARY KEY (`shift_id`) USING BTREE,
  UNIQUE INDEX `shift_code`(`shift_code`) USING BTREE,
  INDEX `idx_shift_code`(`shift_code`) USING BTREE,
  INDEX `idx_shift_type`(`shift_type`) USING BTREE,
  INDEX `idx_is_active`(`is_active`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '班次表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- View structure for v_asset_kpi_enhanced
-- ----------------------------
DROP VIEW IF EXISTS `v_asset_kpi_enhanced`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_asset_kpi_enhanced` AS select count(0) AS `TotalAssets`,sum(`v_assets_enhanced`.`Price`) AS `TotalValue`,round((sum(`v_assets_enhanced`.`Price`) / 10000),2) AS `TotalValueWan`,sum(`v_assets_enhanced`.`IsInUse`) AS `InUseAssets`,sum(`v_assets_enhanced`.`IsIdle`) AS `IdleAssets`,sum(`v_assets_enhanced`.`IsMaintenance`) AS `MaintenanceAssets`,sum(`v_assets_enhanced`.`IsFaulty`) AS `FaultAssets`,sum(`v_assets_enhanced`.`IsScrapped`) AS `ScrappedAssets`,round(((sum(`v_assets_enhanced`.`IsInUse`) * 100.0) / count(0)),2) AS `InUseRate`,round(((sum(`v_assets_enhanced`.`IsIdle`) * 100.0) / count(0)),2) AS `IdleRate`,round(((sum(`v_assets_enhanced`.`IsMaintenance`) * 100.0) / count(0)),2) AS `MaintenanceRate`,round(((sum(`v_assets_enhanced`.`IsFaulty`) * 100.0) / count(0)),2) AS `FaultRate`,round(((sum(`v_assets_enhanced`.`IsScrapped`) * 100.0) / count(0)),2) AS `ScrappedRate`,round(((((sum(`v_assets_enhanced`.`IsInUse`) * 100.0) / count(0)) - ((sum(`v_assets_enhanced`.`IsIdle`) * 30.0) / count(0))) - ((sum(`v_assets_enhanced`.`IsMaintenance`) * 50.0) / count(0))),2) AS `AverageUtilization`,round(((100 - ((sum(`v_assets_enhanced`.`IsFaulty`) * 100.0) / count(0))) - ((sum(`v_assets_enhanced`.`IsScrapped`) * 100.0) / count(0))),2) AS `HealthScore`,count(distinct `v_assets_enhanced`.`AssetTypeId`) AS `TypeCount`,count(distinct (case when (`v_assets_enhanced`.`InheritedDepartmentId` > 0) then `v_assets_enhanced`.`InheritedDepartmentId` end)) AS `DepartmentCount`,count(distinct `v_assets_enhanced`.`RegionId`) AS `RegionCount`,min(`v_assets_enhanced`.`CreatedAt`) AS `EarliestAsset`,max(`v_assets_enhanced`.`CreatedAt`) AS `LatestAsset`,round(avg(`v_assets_enhanced`.`AssetAgeMonths`),1) AS `AverageAgeMonths`,round(avg(`v_assets_enhanced`.`Price`),2) AS `AverageValue`,max(`v_assets_enhanced`.`Price`) AS `MaxAssetValue`,min((case when (`v_assets_enhanced`.`Price` > 0) then `v_assets_enhanced`.`Price` end)) AS `MinAssetValue`,(select count(0) from `assets` `a_sub` where (`a_sub`.`CreatedAt` >= (curdate() - interval 30 day))) AS `NewAssetsLast30Days`,(select count(0) from `assets` `a_sub` where (`a_sub`.`CreatedAt` >= (curdate() - interval 7 day))) AS `NewAssetsLast7Days` from `v_assets_enhanced`;

-- ----------------------------
-- View structure for v_asset_matrix_enhanced
-- ----------------------------
DROP VIEW IF EXISTS `v_asset_matrix_enhanced`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_asset_matrix_enhanced` AS select `ae`.`InheritedDepartmentId` AS `DepartmentId`,`ae`.`DepartmentName` AS `DepartmentName`,`ae`.`DepartmentCode` AS `DepartmentCode`,`ae`.`AssetTypeId` AS `AssetTypeId`,`ae`.`AssetTypeName` AS `AssetTypeName`,`ae`.`AssetTypeCode` AS `AssetTypeCode`,count(0) AS `AssetCount`,sum(`ae`.`IsInUse`) AS `InUseCount`,sum(`ae`.`IsFaulty`) AS `FaultCount`,sum(`ae`.`IsMaintenance`) AS `MaintenanceCount`,sum(`ae`.`Price`) AS `TotalValue`,round(avg(`ae`.`Price`),2) AS `AverageValue`,round(((sum(`ae`.`IsInUse`) * 100.0) / count(0)),2) AS `InUseRate`,round(((sum(`ae`.`IsFaulty`) * 100.0) / count(0)),2) AS `FaultRate`,(row_number() OVER (ORDER BY `ae`.`DepartmentName` )  - 1) AS `MatrixRowIndex`,(row_number() OVER (PARTITION BY `ae`.`InheritedDepartmentId` ORDER BY `ae`.`AssetTypeName` )  - 1) AS `MatrixColIndex`,round(((count(0) * 100.0) / max(`dept_total`.`total_count`)),2) AS `HeatmapValue` from (`v_assets_enhanced` `ae` join (select coalesce(`l`.`DefaultDepartmentId`,`l2`.`DefaultDepartmentId`,`l3`.`DefaultDepartmentId`,`l4`.`DefaultDepartmentId`,`l5`.`DefaultDepartmentId`) AS `InheritedDepartmentId`,count(0) AS `total_count` from (((((`assets` `a` left join `locations` `l` on((`a`.`LocationId` = `l`.`Id`))) left join `locations` `l2` on((cast(substring_index(substring_index(concat(coalesce(`l`.`Path`,'0'),',0,0,0,0,0'),',',2),',',-(1)) as unsigned) = `l2`.`Id`))) left join `locations` `l3` on((cast(substring_index(substring_index(concat(coalesce(`l`.`Path`,'0'),',0,0,0,0,0'),',',3),',',-(1)) as unsigned) = `l3`.`Id`))) left join `locations` `l4` on((cast(substring_index(substring_index(concat(coalesce(`l`.`Path`,'0'),',0,0,0,0,0'),',',4),',',-(1)) as unsigned) = `l4`.`Id`))) left join `locations` `l5` on((cast(substring_index(substring_index(concat(coalesce(`l`.`Path`,'0'),',0,0,0,0,0'),',',5),',',-(1)) as unsigned) = `l5`.`Id`))) where (coalesce(`l`.`DefaultDepartmentId`,`l2`.`DefaultDepartmentId`,`l3`.`DefaultDepartmentId`,`l4`.`DefaultDepartmentId`,`l5`.`DefaultDepartmentId`) is not null) group by coalesce(`l`.`DefaultDepartmentId`,`l2`.`DefaultDepartmentId`,`l3`.`DefaultDepartmentId`,`l4`.`DefaultDepartmentId`,`l5`.`DefaultDepartmentId`)) `dept_total` on((`dept_total`.`InheritedDepartmentId` = `ae`.`InheritedDepartmentId`))) where ((`ae`.`InheritedDepartmentId` is not null) and (`ae`.`InheritedDepartmentId` > 0)) group by `ae`.`InheritedDepartmentId`,`ae`.`DepartmentName`,`ae`.`DepartmentCode`,`ae`.`AssetTypeId`,`ae`.`AssetTypeName`,`ae`.`AssetTypeCode` having (count(0) > 0) order by `ae`.`DepartmentName`,`ae`.`AssetTypeName`;

-- ----------------------------
-- View structure for v_asset_statistics_fast
-- ----------------------------
DROP VIEW IF EXISTS `v_asset_statistics_fast`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_asset_statistics_fast` AS select 'type' AS `DimensionType`,cast(`v_assets_enhanced`.`AssetTypeId` as char charset utf8mb4) AS `DimensionKey`,`v_assets_enhanced`.`AssetTypeName` AS `DimensionName`,`v_assets_enhanced`.`AssetTypeCode` AS `DimensionCode`,NULL AS `ParentKey`,count(0) AS `TotalCount`,sum(`v_assets_enhanced`.`IsInUse`) AS `InUseCount`,sum(`v_assets_enhanced`.`IsIdle`) AS `IdleCount`,sum(`v_assets_enhanced`.`IsMaintenance`) AS `MaintenanceCount`,sum(`v_assets_enhanced`.`IsFaulty`) AS `FaultCount`,sum(`v_assets_enhanced`.`IsScrapped`) AS `ScrappedCount`,sum(`v_assets_enhanced`.`Price`) AS `TotalValue`,round(avg(`v_assets_enhanced`.`Price`),2) AS `AverageValue`,round(((sum(`v_assets_enhanced`.`IsInUse`) * 100.0) / count(0)),2) AS `InUseRate`,round(((sum(`v_assets_enhanced`.`IsFaulty`) * 100.0) / count(0)),2) AS `FaultRate`,round(((count(0) * 100.0) / (select count(0) from `assets`)),2) AS `Percentage`,min(`v_assets_enhanced`.`CreatedAt`) AS `EarliestCreated`,max(`v_assets_enhanced`.`CreatedAt`) AS `LatestCreated`,count(distinct `v_assets_enhanced`.`DepartmentId`) AS `DepartmentCount`,count(distinct `v_assets_enhanced`.`RegionId`) AS `RegionCount` from `v_assets_enhanced` group by `v_assets_enhanced`.`AssetTypeId`,`v_assets_enhanced`.`AssetTypeName`,`v_assets_enhanced`.`AssetTypeCode` union all select 'department' AS `DimensionType`,cast(`v_assets_enhanced`.`InheritedDepartmentId` as char charset utf8mb4) AS `DimensionKey`,`v_assets_enhanced`.`DepartmentName` AS `DimensionName`,`v_assets_enhanced`.`DepartmentCode` AS `DimensionCode`,NULL AS `ParentKey`,count(0) AS `TotalCount`,sum(`v_assets_enhanced`.`IsInUse`) AS `InUseCount`,sum(`v_assets_enhanced`.`IsIdle`) AS `IdleCount`,sum(`v_assets_enhanced`.`IsMaintenance`) AS `MaintenanceCount`,sum(`v_assets_enhanced`.`IsFaulty`) AS `FaultCount`,sum(`v_assets_enhanced`.`IsScrapped`) AS `ScrappedCount`,sum(`v_assets_enhanced`.`Price`) AS `TotalValue`,round(avg(`v_assets_enhanced`.`Price`),2) AS `AverageValue`,round(((sum(`v_assets_enhanced`.`IsInUse`) * 100.0) / count(0)),2) AS `InUseRate`,round(((sum(`v_assets_enhanced`.`IsFaulty`) * 100.0) / count(0)),2) AS `FaultRate`,round(((count(0) * 100.0) / (select count(0) from `assets`)),2) AS `Percentage`,min(`v_assets_enhanced`.`CreatedAt`) AS `EarliestCreated`,max(`v_assets_enhanced`.`CreatedAt`) AS `LatestCreated`,count(distinct `v_assets_enhanced`.`AssetTypeId`) AS `DepartmentCount`,count(distinct `v_assets_enhanced`.`RegionId`) AS `RegionCount` from `v_assets_enhanced` where ((`v_assets_enhanced`.`InheritedDepartmentId` is not null) and (`v_assets_enhanced`.`InheritedDepartmentId` > 0)) group by `v_assets_enhanced`.`InheritedDepartmentId`,`v_assets_enhanced`.`DepartmentName`,`v_assets_enhanced`.`DepartmentCode` union all select 'region' AS `DimensionType`,cast(`v_assets_enhanced`.`RegionId` as char charset utf8mb4) AS `DimensionKey`,`v_assets_enhanced`.`RegionName` AS `DimensionName`,'' AS `DimensionCode`,NULL AS `ParentKey`,count(0) AS `TotalCount`,sum(`v_assets_enhanced`.`IsInUse`) AS `InUseCount`,sum(`v_assets_enhanced`.`IsIdle`) AS `IdleCount`,sum(`v_assets_enhanced`.`IsMaintenance`) AS `MaintenanceCount`,sum(`v_assets_enhanced`.`IsFaulty`) AS `FaultCount`,sum(`v_assets_enhanced`.`IsScrapped`) AS `ScrappedCount`,sum(`v_assets_enhanced`.`Price`) AS `TotalValue`,round(avg(`v_assets_enhanced`.`Price`),2) AS `AverageValue`,round(((sum(`v_assets_enhanced`.`IsInUse`) * 100.0) / count(0)),2) AS `InUseRate`,round(((sum(`v_assets_enhanced`.`IsFaulty`) * 100.0) / count(0)),2) AS `FaultRate`,round(((count(0) * 100.0) / (select count(0) from `assets`)),2) AS `Percentage`,min(`v_assets_enhanced`.`CreatedAt`) AS `EarliestCreated`,max(`v_assets_enhanced`.`CreatedAt`) AS `LatestCreated`,count(distinct `v_assets_enhanced`.`AssetTypeId`) AS `DepartmentCount`,count(distinct `v_assets_enhanced`.`InheritedDepartmentId`) AS `RegionCount` from `v_assets_enhanced` group by `v_assets_enhanced`.`RegionId`,`v_assets_enhanced`.`RegionName` union all select 'status' AS `DimensionType`,cast(`v_assets_enhanced`.`Status` as char charset utf8mb4) AS `DimensionKey`,`v_assets_enhanced`.`StatusText` AS `DimensionName`,`v_assets_enhanced`.`StatusCategory` AS `DimensionCode`,NULL AS `ParentKey`,count(0) AS `TotalCount`,sum(`v_assets_enhanced`.`IsInUse`) AS `InUseCount`,sum(`v_assets_enhanced`.`IsIdle`) AS `IdleCount`,sum(`v_assets_enhanced`.`IsMaintenance`) AS `MaintenanceCount`,sum(`v_assets_enhanced`.`IsFaulty`) AS `FaultCount`,sum(`v_assets_enhanced`.`IsScrapped`) AS `ScrappedCount`,sum(`v_assets_enhanced`.`Price`) AS `TotalValue`,round(avg(`v_assets_enhanced`.`Price`),2) AS `AverageValue`,round(((sum(`v_assets_enhanced`.`IsInUse`) * 100.0) / count(0)),2) AS `InUseRate`,round(((sum(`v_assets_enhanced`.`IsFaulty`) * 100.0) / count(0)),2) AS `FaultRate`,round(((count(0) * 100.0) / (select count(0) from `assets`)),2) AS `Percentage`,min(`v_assets_enhanced`.`CreatedAt`) AS `EarliestCreated`,max(`v_assets_enhanced`.`CreatedAt`) AS `LatestCreated`,count(distinct `v_assets_enhanced`.`AssetTypeId`) AS `DepartmentCount`,count(distinct `v_assets_enhanced`.`InheritedDepartmentId`) AS `RegionCount` from `v_assets_enhanced` group by `v_assets_enhanced`.`Status`,`v_assets_enhanced`.`StatusText`,`v_assets_enhanced`.`StatusCategory`;

-- ----------------------------
-- View structure for v_asset_value_distribution_enhanced
-- ----------------------------
DROP VIEW IF EXISTS `v_asset_value_distribution_enhanced`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_asset_value_distribution_enhanced` AS select `vr`.`range_label` AS `ValueRange`,`vr`.`range_color` AS `RangeColor`,`vr`.`sort_order` AS `SortOrder`,coalesce(`asset_stats`.`AssetCount`,0) AS `AssetCount`,coalesce(`asset_stats`.`TotalValue`,0) AS `TotalValue`,coalesce(`asset_stats`.`TotalValueWan`,0) AS `TotalValueWan`,coalesce(`asset_stats`.`AverageValue`,0) AS `AverageValue`,coalesce(`asset_stats`.`InUseCount`,0) AS `InUseCount`,coalesce(`asset_stats`.`FaultCount`,0) AS `FaultCount`,coalesce(`asset_stats`.`Percentage`,0) AS `Percentage` from (`asset_value_ranges` `vr` left join (select `v_assets_enhanced`.`ValueRange` AS `ValueRange`,count(0) AS `AssetCount`,sum(`v_assets_enhanced`.`Price`) AS `TotalValue`,round((sum(`v_assets_enhanced`.`Price`) / 10000),2) AS `TotalValueWan`,round(avg(`v_assets_enhanced`.`Price`),2) AS `AverageValue`,sum(`v_assets_enhanced`.`IsInUse`) AS `InUseCount`,sum(`v_assets_enhanced`.`IsFaulty`) AS `FaultCount`,round(((count(0) * 100.0) / (select count(0) from `assets`)),2) AS `Percentage` from `v_assets_enhanced` group by `v_assets_enhanced`.`ValueRange`) `asset_stats` on((`vr`.`range_label` = `asset_stats`.`ValueRange`))) where (`vr`.`is_active` = true) order by `vr`.`sort_order`;

-- ----------------------------
-- View structure for v_assets_enhanced
-- ----------------------------
DROP VIEW IF EXISTS `v_assets_enhanced`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_assets_enhanced` AS select `a`.`Id` AS `AssetId`,`a`.`assetCode` AS `AssetCode`,`a`.`FinancialCode` AS `FinancialCode`,`a`.`Name` AS `AssetName`,`a`.`Status` AS `Status`,coalesce(`a`.`Price`,0) AS `Price`,`a`.`PurchaseDate` AS `PurchaseDate`,`a`.`WarrantyExpireDate` AS `WarrantyExpireDate`,`a`.`SerialNumber` AS `SerialNumber`,`a`.`Model` AS `Model`,`a`.`Brand` AS `Brand`,`a`.`Notes` AS `Notes`,`a`.`CreatedAt` AS `CreatedAt`,`a`.`UpdatedAt` AS `UpdatedAt`,`a`.`AssetTypeId` AS `AssetTypeId`,coalesce(`at`.`Name`,'未分类') AS `AssetTypeName`,coalesce(`at`.`Code`,'UNKNOWN') AS `AssetTypeCode`,coalesce(`at`.`Description`,'') AS `AssetTypeDescription`,`a`.`LocationId` AS `CurrentLocationId`,coalesce(`l`.`Name`,'未指定位置') AS `CurrentLocationName`,coalesce(`l`.`Code`,'') AS `CurrentLocationCode`,coalesce(`l`.`Type`,-(1)) AS `LocationType`,coalesce(`l`.`Path`,cast(`a`.`LocationId` as char charset utf8mb4)) AS `LocationPath`,cast(substring_index(substring_index(concat(coalesce(`l`.`Path`,'0'),',0,0,0,0,0'),',',1),',',-(1)) as unsigned) AS `Level1LocationId`,cast(substring_index(substring_index(concat(coalesce(`l`.`Path`,'0'),',0,0,0,0,0'),',',2),',',-(1)) as unsigned) AS `Level2LocationId`,cast(substring_index(substring_index(concat(coalesce(`l`.`Path`,'0'),',0,0,0,0,0'),',',3),',',-(1)) as unsigned) AS `Level3LocationId`,cast(substring_index(substring_index(concat(coalesce(`l`.`Path`,'0'),',0,0,0,0,0'),',',4),',',-(1)) as unsigned) AS `Level4LocationId`,cast(substring_index(substring_index(concat(coalesce(`l`.`Path`,'0'),',0,0,0,0,0'),',',5),',',-(1)) as unsigned) AS `Level5LocationId`,coalesce(`l`.`DefaultDepartmentId`,`l2`.`DefaultDepartmentId`,`l3`.`DefaultDepartmentId`,`l4`.`DefaultDepartmentId`,`l5`.`DefaultDepartmentId`) AS `InheritedDepartmentId`,(case when (`l`.`DefaultDepartmentId` is not null) then 'current' when (`l2`.`DefaultDepartmentId` is not null) then 'level2' when (`l3`.`DefaultDepartmentId` is not null) then 'level3' when (`l4`.`DefaultDepartmentId` is not null) then 'level4' when (`l5`.`DefaultDepartmentId` is not null) then 'level5' else 'none' end) AS `DepartmentSource`,coalesce(`d`.`Id`,0) AS `DepartmentId`,coalesce(`d`.`Name`,'未分配部门') AS `DepartmentName`,coalesce(`d`.`Code`,'') AS `DepartmentCode`,`d`.`ManagerId` AS `DepartmentManagerId`,`d`.`DeputyManagerId` AS `DepartmentDeputyManagerId`,coalesce(`d`.`Path`,'') AS `DepartmentPath`,coalesce(`mgr`.`Username`,'') AS `DepartmentManagerName`,coalesce(`deputy`.`Username`,'') AS `DepartmentDeputyManagerName`,coalesce(`resp`.`Username`,'') AS `LocationResponsiblePersonName`,(case when ((`l`.`Path` is not null) and ((char_length(`l`.`Path`) - char_length(replace(`l`.`Path`,',',''))) >= 3)) then cast(substring_index(substring_index(`l`.`Path`,',',4),',',-(1)) as unsigned) else coalesce(`a`.`LocationId`,0) end) AS `RegionId`,coalesce(`region_loc`.`Name`,`l`.`Name`,'未知区域') AS `RegionName`,coalesce(`vr`.`range_label`,'未知') AS `ValueRange`,coalesce(`vr`.`range_color`,'#6b7280') AS `ValueRangeColor`,coalesce(`vr`.`sort_order`,999) AS `ValueRangeSortOrder`,(case `a`.`Status` when 0 then '闲置' when 1 then '在用' when 2 then '维修中' when 3 then '报废' when 4 then '故障' else '未知' end) AS `StatusText`,(case `a`.`Status` when 0 then 'idle' when 1 then 'in_use' when 2 then 'maintenance' when 3 then 'scrapped' when 4 then 'faulty' else 'unknown' end) AS `StatusCategory`,round((coalesce(`a`.`Price`,0) / 10000),2) AS `ValueInWan`,(case when (`a`.`Status` = 1) then 1 else 0 end) AS `IsInUse`,(case when (`a`.`Status` = 0) then 1 else 0 end) AS `IsIdle`,(case when (`a`.`Status` = 2) then 1 else 0 end) AS `IsMaintenance`,(case when (`a`.`Status` = 4) then 1 else 0 end) AS `IsFaulty`,(case when (`a`.`Status` = 3) then 1 else 0 end) AS `IsScrapped`,year(`a`.`CreatedAt`) AS `CreatedYear`,month(`a`.`CreatedAt`) AS `CreatedMonth`,cast(`a`.`CreatedAt` as date) AS `CreatedDate`,timestampdiff(MONTH,`a`.`CreatedAt`,now()) AS `AssetAgeMonths` from ((((((((((((`assets` `a` left join `assettypes` `at` on((`a`.`AssetTypeId` = `at`.`Id`))) left join `locations` `l` on((`a`.`LocationId` = `l`.`Id`))) left join `locations` `l2` on((`l2`.`Id` = cast(substring_index(substring_index(concat(coalesce(`l`.`Path`,'0'),',0,0,0,0,0'),',',2),',',-(1)) as unsigned)))) left join `locations` `l3` on((`l3`.`Id` = cast(substring_index(substring_index(concat(coalesce(`l`.`Path`,'0'),',0,0,0,0,0'),',',3),',',-(1)) as unsigned)))) left join `locations` `l4` on((`l4`.`Id` = cast(substring_index(substring_index(concat(coalesce(`l`.`Path`,'0'),',0,0,0,0,0'),',',4),',',-(1)) as unsigned)))) left join `locations` `l5` on((`l5`.`Id` = cast(substring_index(substring_index(concat(coalesce(`l`.`Path`,'0'),',0,0,0,0,0'),',',5),',',-(1)) as unsigned)))) left join `locations` `region_loc` on((`region_loc`.`Id` = (case when ((`l`.`Path` is not null) and ((char_length(`l`.`Path`) - char_length(replace(`l`.`Path`,',',''))) >= 3)) then cast(substring_index(substring_index(`l`.`Path`,',',4),',',-(1)) as unsigned) else `a`.`LocationId` end)))) left join `departments` `d` on((`d`.`Id` = coalesce(`l`.`DefaultDepartmentId`,`l2`.`DefaultDepartmentId`,`l3`.`DefaultDepartmentId`,`l4`.`DefaultDepartmentId`,`l5`.`DefaultDepartmentId`)))) left join `users` `mgr` on((`mgr`.`Id` = `d`.`ManagerId`))) left join `users` `deputy` on((`deputy`.`Id` = `d`.`DeputyManagerId`))) left join `users` `resp` on((`resp`.`Id` = `l`.`DefaultResponsiblePersonId`))) left join `asset_value_ranges` `vr` on(((coalesce(`a`.`Price`,0) >= `vr`.`min_value`) and (coalesce(`a`.`Price`,0) < `vr`.`max_value`) and (`vr`.`is_active` = true))));

-- ----------------------------
-- View structure for v_department_leaderboard
-- ----------------------------
DROP VIEW IF EXISTS `v_department_leaderboard`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_department_leaderboard` AS select `d`.`Id` AS `DepartmentId`,`d`.`Name` AS `DepartmentName`,count(distinct `u`.`Id`) AS `UserCount`,coalesce(sum(`gs`.`PointsBalance`),0) AS `TotalPoints`,coalesce(sum(`gs`.`CoinsBalance`),0) AS `TotalCoins`,coalesce(sum(`gs`.`DiamondsBalance`),0) AS `TotalDiamonds`,coalesce(sum(`gs`.`CompletedTasksCount`),0) AS `TotalCompletedTasks`,coalesce(sum(`gs`.`CreatedTasksCount`),0) AS `TotalCreatedTasks`,coalesce(sum(`gs`.`ClaimedTasksCount`),0) AS `TotalClaimedTasks`,coalesce(avg(`gs`.`PointsBalance`),0) AS `AvgPointsPerUser`,coalesce(avg(`gs`.`CompletedTasksCount`),0) AS `AvgTasksPerUser`,row_number() OVER (ORDER BY coalesce(sum(`gs`.`PointsBalance`),0) desc )  AS `DepartmentRank` from ((`departments` `d` left join `users` `u` on(((`d`.`Id` = `u`.`DepartmentId`) and (`u`.`IsActive` = 1)))) left join `gamification_userstats` `gs` on((`u`.`Id` = `gs`.`CoreUserId`))) where (`d`.`IsActive` = 1) group by `d`.`Id`,`d`.`Name` order by coalesce(sum(`gs`.`PointsBalance`),0) desc;

-- ----------------------------
-- View structure for v_recent_activities
-- ----------------------------
DROP VIEW IF EXISTS `v_recent_activities`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_recent_activities` AS select `gl`.`LogId` AS `LogId`,`gl`.`UserId` AS `UserId`,`gus`.`CoreUserId` AS `CoreUserId`,`u`.`Name` AS `UserName`,`u`.`Avatar` AS `UserAvatar`,`gl`.`EventType` AS `EventType`,coalesce(`gl`.`Description`,`gl`.`Reason`) AS `Description`,`gl`.`PointsGained` AS `PointsGained`,`gl`.`CoinsGained` AS `CoinsGained`,`gl`.`DiamondsGained` AS `DiamondsGained`,`gl`.`Timestamp` AS `Timestamp`,`gl`.`ReferenceId` AS `ReferenceId`,`gl`.`RelatedTaskId` AS `RelatedTaskId`,`t`.`Name` AS `TaskName`,(case `gl`.`EventType` when 'TASK_CREATE' then 'Task' when 'TASK_CLAIM' then 'Task' when 'TASK_COMPLETE' then 'Task' when 'TASK_COMPLETE_ONTIME' then 'Task' when 'TaskCompleted' then 'Task' when 'TaskCreated' then 'Task' when 'TaskClaimed' then 'Task' when 'COMMENT_ADD' then 'Comment' when 'DAILY_LOGIN' then 'Login' when 'ASSET_UPDATE' then 'Asset' when 'FAULT_REPORT' then 'Fault' when 'MAINTENANCE_COMPLETE' then 'Maintenance' else 'Other' end) AS `ActivityCategory` from (((`gamification_log` `gl` join `gamification_userstats` `gus` on((`gl`.`UserId` = `gus`.`UserId`))) join `users` `u` on((`gus`.`CoreUserId` = `u`.`Id`))) left join `tasks` `t` on((`gl`.`RelatedTaskId` = `t`.`TaskId`))) where (`u`.`IsActive` = 1) order by `gl`.`Timestamp` desc;

-- ----------------------------
-- View structure for v_spare_part_available_stock
-- ----------------------------
DROP VIEW IF EXISTS `v_spare_part_available_stock`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_spare_part_available_stock` AS select `sp`.`id` AS `part_id`,`sp`.`code` AS `part_code`,`sp`.`name` AS `part_name`,`sp`.`unit` AS `unit`,coalesce(sum((case when (`spt`.`category` = 'Available') then `spi`.`quantity` else 0 end)),0) AS `available_quantity`,coalesce(sum((case when (`spt`.`category` = 'Unavailable') then `spi`.`quantity` else 0 end)),0) AS `unavailable_quantity`,coalesce(sum((case when (`spt`.`category` = 'InTransit') then `spi`.`quantity` else 0 end)),0) AS `in_transit_quantity`,coalesce(sum((case when (`spt`.`category` = 'Reserved') then `spi`.`quantity` else 0 end)),0) AS `reserved_quantity`,coalesce(sum(`spi`.`quantity`),0) AS `total_quantity` from ((`spare_parts` `sp` left join `spare_part_inventories` `spi` on((`sp`.`id` = `spi`.`part_id`))) left join `spare_part_status_types` `spt` on(((`spi`.`status_id` = `spt`.`id`) and (`spt`.`is_active` = 1)))) group by `sp`.`id`;

-- ----------------------------
-- View structure for v_spare_part_stock_summary
-- ----------------------------
DROP VIEW IF EXISTS `v_spare_part_stock_summary`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_spare_part_stock_summary` AS select `sp`.`id` AS `part_id`,`sp`.`code` AS `part_code`,`sp`.`name` AS `part_name`,`sp`.`unit` AS `unit`,`spt`.`code` AS `status_code`,`spt`.`name` AS `status_name`,`spt`.`category` AS `status_category`,`spt`.`color` AS `status_color`,coalesce(sum(`spi`.`quantity`),0) AS `quantity`,`spl`.`id` AS `location_id`,`spl`.`name` AS `location_name` from (((`spare_parts` `sp` join `spare_part_status_types` `spt`) left join `spare_part_inventories` `spi` on(((`sp`.`id` = `spi`.`part_id`) and (`spt`.`id` = `spi`.`status_id`)))) left join `spare_part_locations` `spl` on((`spi`.`location_id` = `spl`.`id`))) where (`spt`.`is_active` = 1) group by `sp`.`id`,`spt`.`id`,`spl`.`id`;

-- ----------------------------
-- View structure for v_user_achievement_progress
-- ----------------------------
DROP VIEW IF EXISTS `v_user_achievement_progress`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_user_achievement_progress` AS select `ua`.`Id` AS `Id`,`ua`.`UserId` AS `UserId`,`u`.`Name` AS `UserName`,`ua`.`AchievementId` AS `AchievementId`,`a`.`Code` AS `AchievementCode`,`a`.`Name` AS `AchievementName`,`a`.`Description` AS `AchievementDescription`,`a`.`Category` AS `AchievementCategory`,`a`.`Difficulty` AS `Difficulty`,`ua`.`Progress` AS `Progress`,`a`.`RequiredCount` AS `RequiredCount`,`ua`.`IsCompleted` AS `IsCompleted`,`ua`.`CompletedAt` AS `CompletedAt`,`ua`.`CompletedCount` AS `CompletedCount`,`a`.`PointsReward` AS `PointsReward`,`a`.`CoinsReward` AS `CoinsReward`,`a`.`DiamondsReward` AS `DiamondsReward`,(case when (`ua`.`IsCompleted` = 1) then 100.0 else round(((`ua`.`Progress` * 100.0) / `a`.`RequiredCount`),2) end) AS `ProgressPercentage` from ((`user_achievements` `ua` join `users` `u` on((`ua`.`UserId` = `u`.`Id`))) join `achievements` `a` on((`ua`.`AchievementId` = `a`.`Id`))) where ((`u`.`IsActive` = 1) and (`a`.`IsActive` = 1)) order by `ua`.`UserId`,`a`.`Category`,`a`.`SortOrder`;

-- ----------------------------
-- View structure for v_user_leaderboard
-- ----------------------------
DROP VIEW IF EXISTS `v_user_leaderboard`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_user_leaderboard` AS select `u`.`Id` AS `UserId`,`u`.`Name` AS `UserName`,`u`.`Avatar` AS `UserAvatar`,`d`.`Name` AS `Department`,coalesce(`gs`.`PointsBalance`,0) AS `TotalPoints`,coalesce(`gs`.`CoinsBalance`,0) AS `TotalCoins`,coalesce(`gs`.`DiamondsBalance`,0) AS `TotalDiamonds`,coalesce(`gs`.`CurrentLevel`,1) AS `CurrentLevel`,coalesce(`gs`.`CompletedTasksCount`,0) AS `CompletedTasks`,coalesce(`gs`.`CreatedTasksCount`,0) AS `CreatedTasks`,coalesce(`gs`.`ClaimedTasksCount`,0) AS `ClaimedTasks`,coalesce(`gs`.`OnTimeTasksCount`,0) AS `OnTimeTasks`,coalesce(`gs`.`StreakCount`,0) AS `StreakCount`,`gs`.`LastActivityTimestamp` AS `LastActivityTimestamp`,row_number() OVER (ORDER BY coalesce(`gs`.`PointsBalance`,0) desc )  AS `PointsRank`,row_number() OVER (ORDER BY coalesce(`gs`.`CompletedTasksCount`,0) desc )  AS `TasksRank` from ((`users` `u` left join `gamification_userstats` `gs` on((`u`.`Id` = `gs`.`CoreUserId`))) left join `departments` `d` on((`u`.`DepartmentId` = `d`.`Id`))) where (`u`.`IsActive` = 1) order by coalesce(`gs`.`PointsBalance`,0) desc;

-- ----------------------------
-- Procedure structure for generate_asset_code
-- ----------------------------
DROP PROCEDURE IF EXISTS `generate_asset_code`;
delimiter ;;
CREATE PROCEDURE `generate_asset_code`(IN asset_type_code VARCHAR(10),
    OUT asset_code VARCHAR(30))
BEGIN
    DECLARE date_part VARCHAR(10);
    DECLARE seq_num INT;
    
    -- 当前日期部分 (年月日)
    SET date_part = DATE_FORMAT(NOW(), '%Y%m%d');
    
    -- 获取当天最大序号并+1
    SELECT IFNULL(MAX(SUBSTRING_INDEX(assetCode, '-', -1)), 0) + 1 
    INTO seq_num
    FROM assets 
    WHERE assetCode LIKE CONCAT('IT-', asset_type_code, '-', date_part, '-%');
    
    -- 格式化资产编号
    SET asset_code = CONCAT('IT-', asset_type_code, '-', date_part, '-', LPAD(seq_num, 3, '0'));
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for inventory_to_asset
-- ----------------------------
DROP PROCEDURE IF EXISTS `inventory_to_asset`;
delimiter ;;
CREATE PROCEDURE `inventory_to_asset`(IN inventory_id INT,
    IN location_id INT,
    IN user_id INT,
    OUT asset_id INT)
BEGIN
    DECLARE asset_type_code VARCHAR(10);
    DECLARE asset_code VARCHAR(30);
    
    -- 事务开始
    START TRANSACTION;
    
    -- 获取物料类型代码
    SELECT code INTO asset_type_code 
    FROM assettypes at 
    JOIN purchaseitems pi ON at.id = pi.assetTypeId
    WHERE pi.id = inventory_id;
    
    -- 生成资产编号
    CALL generate_asset_code(asset_type_code, asset_code);
    
    -- 创建资产记录
    INSERT INTO assets(
        assetCode,
        name,
        assetTypeId,
        model,
        brand,
        purchaseDate,
        locationId,
        status,
        inventoryId,
        createdAt,
        updatedAt
    )
    SELECT 
        asset_code,
        pi.name,
        pi.assetTypeId,
        pi.model,
        pi.brand,
        po.orderDate,
        location_id,
        1, -- 使用中状态
        inventory_id,
        NOW(),
        NOW()
    FROM purchaseitems pi
    JOIN purchaseorders po ON pi.purchaseOrderId = po.id
    WHERE pi.id = inventory_id;
    
    -- 更新库存状态
    UPDATE purchaseitems 
    SET status = 2 -- 已出库
    WHERE id = inventory_id;
    
    -- 获取新创建的资产ID
    SELECT LAST_INSERT_ID() INTO asset_id;
    
    -- 提交事务
    COMMIT;
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for POMELO_AFTER_ADD_PRIMARY_KEY
-- ----------------------------
DROP PROCEDURE IF EXISTS `POMELO_AFTER_ADD_PRIMARY_KEY`;
delimiter ;;
CREATE PROCEDURE `POMELO_AFTER_ADD_PRIMARY_KEY`(IN `SCHEMA_NAME_ARGUMENT` VARCHAR(255), IN `TABLE_NAME_ARGUMENT` VARCHAR(255), IN `COLUMN_NAME_ARGUMENT` VARCHAR(255))
BEGIN
	DECLARE HAS_AUTO_INCREMENT_ID INT(11);
	DECLARE PRIMARY_KEY_COLUMN_NAME VARCHAR(255);
	DECLARE PRIMARY_KEY_TYPE VARCHAR(255);
	DECLARE SQL_EXP VARCHAR(1000);
	SELECT COUNT(*)
		INTO HAS_AUTO_INCREMENT_ID
		FROM `information_schema`.`COLUMNS`
		WHERE `TABLE_SCHEMA` = (SELECT IFNULL(SCHEMA_NAME_ARGUMENT, SCHEMA()))
			AND `TABLE_NAME` = TABLE_NAME_ARGUMENT
			AND `COLUMN_NAME` = COLUMN_NAME_ARGUMENT
			AND `COLUMN_TYPE` LIKE '%int%'
			AND `COLUMN_KEY` = 'PRI';
	IF HAS_AUTO_INCREMENT_ID THEN
		SELECT `COLUMN_TYPE`
			INTO PRIMARY_KEY_TYPE
			FROM `information_schema`.`COLUMNS`
			WHERE `TABLE_SCHEMA` = (SELECT IFNULL(SCHEMA_NAME_ARGUMENT, SCHEMA()))
				AND `TABLE_NAME` = TABLE_NAME_ARGUMENT
				AND `COLUMN_NAME` = COLUMN_NAME_ARGUMENT
				AND `COLUMN_TYPE` LIKE '%int%'
				AND `COLUMN_KEY` = 'PRI';
		SELECT `COLUMN_NAME`
			INTO PRIMARY_KEY_COLUMN_NAME
			FROM `information_schema`.`COLUMNS`
			WHERE `TABLE_SCHEMA` = (SELECT IFNULL(SCHEMA_NAME_ARGUMENT, SCHEMA()))
				AND `TABLE_NAME` = TABLE_NAME_ARGUMENT
				AND `COLUMN_NAME` = COLUMN_NAME_ARGUMENT
				AND `COLUMN_TYPE` LIKE '%int%'
				AND `COLUMN_KEY` = 'PRI';
		SET SQL_EXP = CONCAT('ALTER TABLE `', (SELECT IFNULL(SCHEMA_NAME_ARGUMENT, SCHEMA())), '`.`', TABLE_NAME_ARGUMENT, '` MODIFY COLUMN `', PRIMARY_KEY_COLUMN_NAME, '` ', PRIMARY_KEY_TYPE, ' NOT NULL AUTO_INCREMENT;');
		SET @SQL_EXP = SQL_EXP;
		PREPARE SQL_EXP_EXECUTE FROM @SQL_EXP;
		EXECUTE SQL_EXP_EXECUTE;
		DEALLOCATE PREPARE SQL_EXP_EXECUTE;
	END IF;
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for POMELO_BEFORE_DROP_PRIMARY_KEY
-- ----------------------------
DROP PROCEDURE IF EXISTS `POMELO_BEFORE_DROP_PRIMARY_KEY`;
delimiter ;;
CREATE PROCEDURE `POMELO_BEFORE_DROP_PRIMARY_KEY`(IN `SCHEMA_NAME_ARGUMENT` VARCHAR(255), IN `TABLE_NAME_ARGUMENT` VARCHAR(255))
BEGIN
	DECLARE HAS_AUTO_INCREMENT_ID TINYINT(1);
	DECLARE PRIMARY_KEY_COLUMN_NAME VARCHAR(255);
	DECLARE PRIMARY_KEY_TYPE VARCHAR(255);
	DECLARE SQL_EXP VARCHAR(1000);
	SELECT COUNT(*)
		INTO HAS_AUTO_INCREMENT_ID
		FROM `information_schema`.`COLUMNS`
		WHERE `TABLE_SCHEMA` = (SELECT IFNULL(SCHEMA_NAME_ARGUMENT, SCHEMA()))
			AND `TABLE_NAME` = TABLE_NAME_ARGUMENT
			AND `Extra` = 'auto_increment'
			AND `COLUMN_KEY` = 'PRI'
			LIMIT 1;
	IF HAS_AUTO_INCREMENT_ID THEN
		SELECT `COLUMN_TYPE`
			INTO PRIMARY_KEY_TYPE
			FROM `information_schema`.`COLUMNS`
			WHERE `TABLE_SCHEMA` = (SELECT IFNULL(SCHEMA_NAME_ARGUMENT, SCHEMA()))
				AND `TABLE_NAME` = TABLE_NAME_ARGUMENT
				AND `COLUMN_KEY` = 'PRI'
			LIMIT 1;
		SELECT `COLUMN_NAME`
			INTO PRIMARY_KEY_COLUMN_NAME
			FROM `information_schema`.`COLUMNS`
			WHERE `TABLE_SCHEMA` = (SELECT IFNULL(SCHEMA_NAME_ARGUMENT, SCHEMA()))
				AND `TABLE_NAME` = TABLE_NAME_ARGUMENT
				AND `COLUMN_KEY` = 'PRI'
			LIMIT 1;
		SET SQL_EXP = CONCAT('ALTER TABLE `', (SELECT IFNULL(SCHEMA_NAME_ARGUMENT, SCHEMA())), '`.`', TABLE_NAME_ARGUMENT, '` MODIFY COLUMN `', PRIMARY_KEY_COLUMN_NAME, '` ', PRIMARY_KEY_TYPE, ' NOT NULL;');
		SET @SQL_EXP = SQL_EXP;
		PREPARE SQL_EXP_EXECUTE FROM @SQL_EXP;
		EXECUTE SQL_EXP_EXECUTE;
		DEALLOCATE PREPARE SQL_EXP_EXECUTE;
	END IF;
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for RefreshAssetKPI
-- ----------------------------
DROP PROCEDURE IF EXISTS `RefreshAssetKPI`;
delimiter ;;
CREATE PROCEDURE `RefreshAssetKPI`()
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;

    START TRANSACTION;
    
    DELETE FROM mv_asset_kpi;
    
    INSERT INTO mv_asset_kpi (
        total_assets, total_value, in_use_rate, fault_rate, 
        idle_rate, maintenance_rate, average_utilization, health_score
    )
    SELECT 
        TotalAssets, TotalValue, InUseRate, FaultRate,
        IdleRate, MaintenanceRate, AverageUtilization, HealthScore
    FROM v_asset_kpi_enhanced;
    
    COMMIT;
    
    SELECT CONCAT('KPI数据刷新完成') AS result;
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for RefreshAssetStatistics
-- ----------------------------
DROP PROCEDURE IF EXISTS `RefreshAssetStatistics`;
delimiter ;;
CREATE PROCEDURE `RefreshAssetStatistics`()
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;

    START TRANSACTION;
    
    DELETE FROM mv_asset_statistics;
    
    INSERT INTO mv_asset_statistics (
        dimension_type, dimension_key, dimension_name, total_count, 
        in_use_count, fault_count, total_value, in_use_rate, fault_rate
    )
    SELECT 
        DimensionType, DimensionKey, DimensionName, TotalCount,
        InUseCount, FaultCount, TotalValue, InUseRate, FaultRate
    FROM v_asset_statistics_fast;
    
    COMMIT;
    
    SELECT CONCAT('资产统计数据刷新完成，处理 ', ROW_COUNT(), ' 条记录') AS result;
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for sp_UpdateLeaderboard
-- ----------------------------
DROP PROCEDURE IF EXISTS `sp_UpdateLeaderboard`;
delimiter ;;
CREATE PROCEDURE `sp_UpdateLeaderboard`(IN p_LeaderboardType INT,  -- 1: 周排行榜, 2: 月排行榜, 3: 总排行榜
    IN p_Period VARCHAR(50))
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;

    START TRANSACTION;

    -- 更新所有活跃用户的统计数据
    UPDATE gamification_userstats gus
    INNER JOIN users u ON gus.CoreUserId = u.Id
    SET
        gus.CompletedTasksCount = (
            SELECT COUNT(*)
            FROM tasks t
            WHERE t.CompletedByUserId = gus.CoreUserId
              AND t.Status = 'Done'
        ),
        gus.CreatedTasksCount = (
            SELECT COUNT(*)
            FROM tasks t
            WHERE t.CreatorUserId = gus.CoreUserId
        ),
        gus.ClaimedTasksCount = (
            SELECT COUNT(*)
            FROM task_claims tc
            WHERE tc.claimed_by = gus.CoreUserId
        ),
        gus.OnTimeTasksCount = (
            SELECT COUNT(*)
            FROM tasks t
            WHERE t.CompletedByUserId = gus.CoreUserId
              AND t.Status = 'Done'
              AND t.CompletedAt IS NOT NULL
              AND t.PlanEndDate IS NOT NULL
              AND t.CompletedAt <= t.PlanEndDate
        ),
        gus.LastUpdatedTimestamp = NOW()
    WHERE u.IsActive = 1;

    -- 记录排行榜更新日志
    INSERT INTO gamification_log (UserId, EventType, Description, Timestamp)
    SELECT
        gus.UserId,
        'LeaderboardUpdate',
        CONCAT('排行榜更新 - 类型:', p_LeaderboardType, ', 周期:', p_Period),
        NOW()
    FROM gamification_userstats gus
    INNER JOIN users u ON gus.CoreUserId = u.Id
    WHERE u.IsActive = 1
    LIMIT 1;  -- 只记录一条系统日志

    COMMIT;
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for sp_UpdateUserGamificationStats
-- ----------------------------
DROP PROCEDURE IF EXISTS `sp_UpdateUserGamificationStats`;
delimiter ;;
CREATE PROCEDURE `sp_UpdateUserGamificationStats`(IN p_UserId INT)
BEGIN
    DECLARE v_CompletedTasks INT DEFAULT 0;
    DECLARE v_CreatedTasks INT DEFAULT 0;
    DECLARE v_ClaimedTasks INT DEFAULT 0;
    DECLARE v_OnTimeTasks INT DEFAULT 0;
    DECLARE v_TotalPoints INT DEFAULT 0;
    DECLARE v_TotalCoins INT DEFAULT 0;
    DECLARE v_TotalDiamonds INT DEFAULT 0;
    
    -- 计算完成的任务数
    SELECT COUNT(*) INTO v_CompletedTasks
    FROM tasks 
    WHERE CompletedByUserId = p_UserId AND Status = 'Done';
    
    -- 计算创建的任务数
    SELECT COUNT(*) INTO v_CreatedTasks
    FROM tasks 
    WHERE CreatorUserId = p_UserId;
    
    -- 计算认领的任务数
    SELECT COUNT(*) INTO v_ClaimedTasks
    FROM task_claims 
    WHERE claimed_by = p_UserId;
    
    -- 计算按时完成的任务数
    SELECT COUNT(*) INTO v_OnTimeTasks
    FROM tasks 
    WHERE CompletedByUserId = p_UserId 
      AND Status = 'Done' 
      AND CompletedAt IS NOT NULL 
      AND PlanEndDate IS NOT NULL 
      AND CompletedAt <= PlanEndDate;
    
    -- 计算总积分、金币、钻石
    SELECT
        COALESCE(SUM(PointsGained), 0),
        COALESCE(SUM(CoinsGained), 0),
        COALESCE(SUM(DiamondsGained), 0)
    INTO v_TotalPoints, v_TotalCoins, v_TotalDiamonds
    FROM gamification_log gl
    INNER JOIN gamification_userstats gus ON gl.UserId = gus.UserId
    WHERE gus.CoreUserId = p_UserId;
    
    -- 更新或插入用户统计
    INSERT INTO gamification_userstats (
        UserId, CoreUserId, CompletedTasksCount, CreatedTasksCount, 
        ClaimedTasksCount, OnTimeTasksCount, PointsBalance, 
        CoinsBalance, DiamondsBalance, LastUpdatedTimestamp
    ) VALUES (
        p_UserId, p_UserId, v_CompletedTasks, v_CreatedTasks,
        v_ClaimedTasks, v_OnTimeTasks, v_TotalPoints,
        v_TotalCoins, v_TotalDiamonds, NOW()
    ) ON DUPLICATE KEY UPDATE
        CompletedTasksCount = v_CompletedTasks,
        CreatedTasksCount = v_CreatedTasks,
        ClaimedTasksCount = v_ClaimedTasks,
        OnTimeTasksCount = v_OnTimeTasks,
        PointsBalance = v_TotalPoints,
        CoinsBalance = v_TotalCoins,
        DiamondsBalance = v_TotalDiamonds,
        LastUpdatedTimestamp = NOW();
        
END
;;
delimiter ;

-- ----------------------------
-- Triggers structure for table faultrecords
-- ----------------------------
DROP TRIGGER IF EXISTS `generate_fault_number`;
delimiter ;;
CREATE TRIGGER `generate_fault_number` BEFORE INSERT ON `faultrecords` FOR EACH ROW BEGIN
    DECLARE date_part VARCHAR(10);
    DECLARE seq_num INT;
    
    -- 如果没有手动指定故障单号
    IF NEW.faultNumber IS NULL OR NEW.faultNumber = '' THEN
        -- 生成日期部分
        SET date_part = DATE_FORMAT(NOW(), '%Y%m%d');
        
        -- 获取当天最大序号
        SELECT IFNULL(MAX(SUBSTRING_INDEX(faultNumber, '-', -1)), 0) + 1 
        INTO seq_num
        FROM faultrecords 
        WHERE faultNumber LIKE CONCAT('FIX-', date_part, '-%');
        
        -- 设置故障单号
        SET NEW.faultNumber = CONCAT('FIX-', date_part, '-', LPAD(seq_num, 3, '0'));
    END IF;
END
;;
delimiter ;

-- ----------------------------
-- Triggers structure for table gamification_log
-- ----------------------------
DROP TRIGGER IF EXISTS `tr_gamification_log_insert`;
delimiter ;;
CREATE TRIGGER `tr_gamification_log_insert` AFTER INSERT ON `gamification_log` FOR EACH ROW BEGIN
    -- 当有新的游戏化日志时，更新用户统计
    -- 这里可以调用存储过程来更新用户统计
    -- 暂时留空，后续可以扩展
    SET @user_rewarded = NEW.UserId;
END
;;
delimiter ;

-- ----------------------------
-- Triggers structure for table purchaseorders
-- ----------------------------
DROP TRIGGER IF EXISTS `format_purchase_order_number`;
delimiter ;;
CREATE TRIGGER `format_purchase_order_number` BEFORE INSERT ON `purchaseorders` FOR EACH ROW BEGIN
    -- 如果没有手动指定采购单号
    IF NEW.OrderCode IS NULL OR NEW.OrderCode = '' THEN
        -- 生成采购单号 (PO-年月日-时分秒)
        SET NEW.OrderCode = CONCAT('PO-', DATE_FORMAT(NOW(), '%Y%m%d-%H%i%s'));
    END IF;
END
;;
delimiter ;

-- ----------------------------
-- Triggers structure for table spare_part_inventories
-- ----------------------------
DROP TRIGGER IF EXISTS `update_spare_part_quantity_after_inventory_change`;
delimiter ;;
CREATE TRIGGER `update_spare_part_quantity_after_inventory_change` AFTER INSERT ON `spare_part_inventories` FOR EACH ROW BEGIN
    UPDATE spare_parts 
    SET quantity = (
        SELECT COALESCE(SUM(quantity), 0) 
        FROM spare_part_inventories 
        WHERE part_id = NEW.part_id
    )
    WHERE id = NEW.part_id;
END
;;
delimiter ;

-- ----------------------------
-- Triggers structure for table spare_part_inventories
-- ----------------------------
DROP TRIGGER IF EXISTS `update_spare_part_quantity_after_inventory_update`;
delimiter ;;
CREATE TRIGGER `update_spare_part_quantity_after_inventory_update` AFTER UPDATE ON `spare_part_inventories` FOR EACH ROW BEGIN
    UPDATE spare_parts 
    SET quantity = (
        SELECT COALESCE(SUM(quantity), 0) 
        FROM spare_part_inventories 
        WHERE part_id = NEW.part_id
    )
    WHERE id = NEW.part_id;
END
;;
delimiter ;

-- ----------------------------
-- Triggers structure for table spare_part_inventories
-- ----------------------------
DROP TRIGGER IF EXISTS `update_spare_part_quantity_after_inventory_delete`;
delimiter ;;
CREATE TRIGGER `update_spare_part_quantity_after_inventory_delete` AFTER DELETE ON `spare_part_inventories` FOR EACH ROW BEGIN
    UPDATE spare_parts 
    SET quantity = (
        SELECT COALESCE(SUM(quantity), 0) 
        FROM spare_part_inventories 
        WHERE part_id = OLD.part_id
    )
    WHERE id = OLD.part_id;
END
;;
delimiter ;

-- ----------------------------
-- Triggers structure for table task_claims
-- ----------------------------
DROP TRIGGER IF EXISTS `tr_task_claims_update`;
delimiter ;;
CREATE TRIGGER `tr_task_claims_update` AFTER UPDATE ON `task_claims` FOR EACH ROW BEGIN
    -- 当任务认领状态变为完成时，同步更新任务表的完成信息
    IF NEW.claim_status = 'Completed' AND OLD.claim_status != 'Completed' THEN
        UPDATE tasks 
        SET Status = 'Done',
            CompletedByUserId = NEW.claimed_by,
            CompletedAt = COALESCE(NEW.completed_at, NOW()),
            ActualEndDate = COALESCE(NEW.completed_at, NOW())
        WHERE TaskId = NEW.task_id;
    END IF;
    
    -- 当任务认领状态变为开始时，更新任务状态和开始时间
    IF NEW.claim_status = 'Started' AND OLD.claim_status = 'Claimed' THEN
        UPDATE tasks
        SET Status = 'InProgress',
            ActualStartDate = COALESCE(NEW.started_at, NOW())
        WHERE TaskId = NEW.task_id;
    END IF;
END
;;
delimiter ;

-- ----------------------------
-- Triggers structure for table task_claims
-- ----------------------------
DROP TRIGGER IF EXISTS `tr_task_claims_insert`;
delimiter ;;
CREATE TRIGGER `tr_task_claims_insert` AFTER INSERT ON `task_claims` FOR EACH ROW BEGIN
    -- 记录任务认领的游戏化奖励
    INSERT INTO gamification_log (
        UserId, EventType, PointsGained, CoinsGained, DiamondsGained,
        XPChange, Description, ReferenceId, RelatedTaskId, Timestamp
    )
    SELECT
        gus.UserId,
        'TaskClaimed',
        COALESCE(bt.PointsReward, 10),
        COALESCE(bt.CoinsReward, 5),
        COALESCE(bt.DiamondsReward, 0),
        COALESCE(bt.PointsReward, 10),
        CONCAT('认领任务: ', t.Name),
        NEW.claim_id,
        NEW.task_id,
        NOW()
    FROM gamification_userstats gus
    LEFT JOIN behavior_types bt ON bt.Code = 'TASK_CLAIMED'
    LEFT JOIN tasks t ON NEW.task_id = t.TaskId
    WHERE gus.CoreUserId = NEW.claimed_by
    LIMIT 1;
END
;;
delimiter ;

-- ----------------------------
-- Triggers structure for table tasks
-- ----------------------------
DROP TRIGGER IF EXISTS `tr_tasks_status_update`;
delimiter ;;
CREATE TRIGGER `tr_tasks_status_update` AFTER UPDATE ON `tasks` FOR EACH ROW BEGIN
    DECLARE v_claimed_by INT DEFAULT NULL;
    
    -- 当任务状态变为完成时，记录完成时间和用户
    IF NEW.Status = 'Done' AND OLD.Status != 'Done' THEN
        -- 如果没有设置完成时间，设置为当前时间
        IF NEW.CompletedAt IS NULL THEN
            UPDATE tasks 
            SET CompletedAt = NOW()
            WHERE TaskId = NEW.TaskId;
        END IF;
        
        -- 如果没有设置完成用户，查找实际的任务领取人
        IF NEW.CompletedByUserId IS NULL THEN
            -- 首先尝试从任务认领记录中获取完成用户
            SELECT claimed_by INTO v_claimed_by
            FROM task_claims 
            WHERE task_id = NEW.TaskId 
              AND claim_status IN ('Completed', 'Started', 'Claimed')
            ORDER BY 
                CASE claim_status 
                    WHEN 'Completed' THEN 1 
                    WHEN 'Started' THEN 2 
                    WHEN 'Claimed' THEN 3 
                END,
                completed_at DESC, 
                started_at DESC, 
                claimed_at DESC
            LIMIT 1;
            
            -- 如果找到了认领记录，使用认领人；否则使用负责人或创建者
            UPDATE tasks
            SET CompletedByUserId = COALESCE(v_claimed_by, NEW.AssigneeUserId, NEW.CreatorUserId)
            WHERE TaskId = NEW.TaskId;
        END IF;

        -- 记录任务完成的游戏化奖励
        INSERT INTO gamification_log (
            UserId, EventType, PointsGained, CoinsGained, DiamondsGained,
            XPChange, Description, ReferenceId, RelatedTaskId, Timestamp
        )
        SELECT
            gus.UserId,
            'TaskCompleted',
            COALESCE(bt.PointsReward, 50),
            COALESCE(bt.CoinsReward, 25),
            COALESCE(bt.DiamondsReward, 1),
            COALESCE(bt.PointsReward, 50),
            CONCAT('完成任务: ', NEW.Name),
            NEW.TaskId,
            NEW.TaskId,
            NOW()
        FROM gamification_userstats gus
        LEFT JOIN behavior_types bt ON bt.Code = 'TASK_COMPLETED'
        WHERE gus.CoreUserId = COALESCE(NEW.CompletedByUserId, v_claimed_by, NEW.AssigneeUserId, NEW.CreatorUserId)
        LIMIT 1;
    END IF;
END
;;
delimiter ;

-- ----------------------------
-- Triggers structure for table tasks
-- ----------------------------
DROP TRIGGER IF EXISTS `tr_tasks_status_update_backup`;
delimiter ;;
CREATE TRIGGER `tr_tasks_status_update_backup` BEFORE UPDATE ON `tasks` FOR EACH ROW BEGIN
    -- 这个触发器已被禁用，逻辑已迁移到事件驱动系统
    -- 原始逻辑保留在注释中以供参考
    /*
    IF NEW.Status = 'Done' AND OLD.Status != 'Done' THEN
        -- 设置完成时间和用户
        -- 记录游戏化奖励
    END IF;
    */
    
    -- 记录迁移日志
    INSERT INTO gamification_log (UserId, EventType, Description, Timestamp)
    VALUES (0, 'TRIGGER_DISABLED', 'tr_tasks_status_update已迁移到事件驱动系统', NOW());
END
;;
delimiter ;

SET FOREIGN_KEY_CHECKS = 1;
