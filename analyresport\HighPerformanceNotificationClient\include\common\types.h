// 通用类型定义
#pragma once

#include <string>
#include <vector>
#include <memory>
#include <functional>
#include <chrono>
#include <cstdint>

namespace notification {

// 基础类型别名
using String = std::string;
using ByteArray = std::vector<uint8_t>;
using TimePoint = std::chrono::steady_clock::time_point;
using Duration = std::chrono::milliseconds;

// 消息类型枚举
enum class MessageType : uint8_t {
    UNKNOWN = 0x00,
    GAMIFICATION_REWARD = 0x01,
    TASK_UPDATE = 0x02,
    TASK_CREATED = 0x03,
    TASK_CLAIMED = 0x04,
    TASK_COMPLETED = 0x05,
    SYSTEM_ALERT = 0x06,
    LEADERBOARD_UPDATE = 0x07,
    ACHIEVEMENT_UNLOCKED = 0x08,
    USER_MENTIONED = 0x09,
    HEARTBEAT = 0xFF
};

// 消息优先级
enum class Priority : uint8_t {
    LOW = 0,
    NORMAL = 1,
    HIGH = 2,
    CRITICAL = 3
};

// 连接状态
enum class ConnectionStatus {
    DISCONNECTED,
    CONNECTING,
    CONNECTED,
    RECONNECTING,
    FAILED
};

// 认证状态
enum class AuthStatus {
    NOT_AUTHENTICATED,
    AUTHENTICATING,
    AUTHENTICATED,
    TOKEN_EXPIRED,
    AUTH_FAILED
};

// 网络协议类型
enum class ProtocolType {
    UDP,
    WEBSOCKET,
    HTTP
};

// 通知显示类型
enum class NotificationType {
    TOAST,          // 气泡通知
    TRAY_ICON,      // 托盘图标变化
    SOUND,          // 声音提醒
    POPUP,          // 弹窗
    BADGE           // 徽章计数
};

// 游戏化奖励信息
struct RewardInfo {
    int points = 0;
    int coins = 0;
    int diamonds = 0;
    int experience = 0;
    String reason;
    String description;
};

// 任务信息
struct TaskInfo {
    int64_t task_id = 0;
    String task_name;
    String task_description;
    String status;
    String priority;
    String assigned_to;
    String created_by;
    TimePoint created_at;
    TimePoint due_date;
};

// 用户信息
struct UserInfo {
    int user_id = 0;
    String username;
    String display_name;
    String department;
    String email;
    String avatar_url;
};

// 通知消息结构
struct NotificationMessage {
    uint64_t id = 0;
    MessageType type = MessageType::UNKNOWN;
    Priority priority = Priority::NORMAL;
    String title;
    String content;
    String action_url;
    UserInfo sender;
    TimePoint timestamp;
    Duration display_duration = std::chrono::milliseconds(3000);
    
    // 特定类型数据
    RewardInfo reward_info;
    TaskInfo task_info;
    
    // 元数据
    String correlation_id;
    String department_filter;
    String user_filter;
    bool requires_acknowledgment = false;
};

// 统计信息
struct Statistics {
    uint64_t messages_received = 0;
    uint64_t messages_sent = 0;
    uint64_t bytes_received = 0;
    uint64_t bytes_sent = 0;
    uint64_t connections_established = 0;
    uint64_t connection_failures = 0;
    Duration total_uptime = Duration::zero();
    Duration average_latency = Duration::zero();
    double packet_loss_rate = 0.0;
};

// 性能指标
struct PerformanceMetrics {
    Duration message_processing_time = Duration::zero();
    Duration network_latency = Duration::zero();
    size_t memory_usage_bytes = 0;
    double cpu_usage_percent = 0.0;
    size_t active_connections = 0;
    size_t queued_messages = 0;
};

// 配置结构
struct ServerConfig {
    String ip = "127.0.0.1";
    uint16_t udp_port = 8081;
    uint16_t websocket_port = 8080;
    String signalr_hub = "/hubs/notification";
    String api_base_url = "http://localhost:5000/api";
    String api_version = "v2";
};

struct AuthConfig {
    String username;
    String password;
    String token;
    bool auto_login = true;
    Duration token_refresh_threshold = std::chrono::minutes(5);
    bool support_anonymous = true;
};

struct NetworkConfig {
    bool enable_udp = true;
    bool enable_websocket = true;
    bool fallback_to_websocket = true;
    Duration udp_timeout = std::chrono::seconds(1);
    Duration websocket_timeout = std::chrono::seconds(5);
    int max_retry_count = 3;
    Duration retry_interval = std::chrono::seconds(1);
    Duration heartbeat_interval = std::chrono::seconds(30);
};

struct UIConfig {
    bool enable_tray = true;
    bool enable_balloon_tips = true;
    bool enable_sound = false;
    Duration auto_hide_timeout = std::chrono::seconds(5);
    size_t max_notifications = 100;
    Duration notification_fade_time = std::chrono::milliseconds(300);
};

// 事件回调函数类型
using NotificationCallback = std::function<void(const NotificationMessage&)>;
using ConnectionStatusCallback = std::function<void(ConnectionStatus, ProtocolType)>;
using AuthStatusCallback = std::function<void(AuthStatus)>;
using ErrorCallback = std::function<void(const String& error)>;
using StatisticsCallback = std::function<void(const Statistics&)>;

// 智能指针别名
template<typename T>
using UniquePtr = std::unique_ptr<T>;

template<typename T>
using SharedPtr = std::shared_ptr<T>;

template<typename T>
using WeakPtr = std::weak_ptr<T>;

// 便利函数
inline String messageTypeToString(MessageType type) {
    switch (type) {
        case MessageType::GAMIFICATION_REWARD: return "GAMIFICATION_REWARD";
        case MessageType::TASK_UPDATE: return "TASK_UPDATE";
        case MessageType::TASK_CREATED: return "TASK_CREATED";
        case MessageType::TASK_CLAIMED: return "TASK_CLAIMED";
        case MessageType::TASK_COMPLETED: return "TASK_COMPLETED";
        case MessageType::SYSTEM_ALERT: return "SYSTEM_ALERT";
        case MessageType::LEADERBOARD_UPDATE: return "LEADERBOARD_UPDATE";
        case MessageType::ACHIEVEMENT_UNLOCKED: return "ACHIEVEMENT_UNLOCKED";
        case MessageType::USER_MENTIONED: return "USER_MENTIONED";
        case MessageType::HEARTBEAT: return "HEARTBEAT";
        default: return "UNKNOWN";
    }
}

inline String priorityToString(Priority priority) {
    switch (priority) {
        case Priority::LOW: return "LOW";
        case Priority::NORMAL: return "NORMAL";
        case Priority::HIGH: return "HIGH";
        case Priority::CRITICAL: return "CRITICAL";
        default: return "UNKNOWN";
    }
}

} // namespace notification