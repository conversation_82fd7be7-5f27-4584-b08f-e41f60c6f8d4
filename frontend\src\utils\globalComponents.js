/**
 * 航空航天级IT资产管理系统 - 全局组件注册工具
 * 文件路径: src/utils/globalComponents.js
 * 功能描述: 自动注册全局通用组件
 */

// 导入自定义组件
import Breadcrumb from '@/components/Breadcrumb.vue'
import StatusTag from '@/components/StatusTag.vue'
import PageHeader from '@/components/PageHeader.vue'
import SearchForm from '@/components/SearchForm.vue'
import EmptyData from '@/components/EmptyData.vue'
import UserSelect from '@/components/UserSelect.vue'
import AssetSelect from '@/components/AssetSelect.vue'
import LocationSelect from '@/components/LocationSelect.vue'
import TaskSelect from '@/components/TaskSelect.vue'
import DepartmentSelect from '@/components/DepartmentSelect.vue'
import FileUpload from '@/components/FileUpload.vue'

// 全局组件列表
const components = {
  Breadcrumb,
  StatusTag,
  PageHeader,
  SearchForm,
  EmptyData,
  UserSelect,
  AssetSelect,
  LocationSelect,
  TaskSelect,
  DepartmentSelect,
  FileUpload
}

/**
 * 设置全局组件
 * @param {Object} app - Vue应用实例
 */
export function setupGlobalComponents(app) {
  // 注册每个组件为全局组件
  Object.entries(components).forEach(([name, component]) => {
    app.component(name, component)
  })
} 