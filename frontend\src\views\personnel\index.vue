<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.keyword"
        placeholder="姓名/工号/联系方式"
        style="width: 200px;"
        class="filter-item"
        @keyup.enter.native="handleFilter"
      />
      <el-select
        v-model="listQuery.departmentId"
        placeholder="所属部门"
        clearable
        style="width: 200px"
        class="filter-item"
      >
        <el-option
          v-for="item in departmentOptions"
          :key="item.id"
          :label="item.name"
          :value="item.id"
        />
      </el-select>
      <el-button
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        @click="handleFilter"
      >
        搜索
      </el-button>
      <el-button
        class="filter-item"
        style="margin-left: 10px;"
        type="primary"
        icon="el-icon-plus"
        @click="handleCreate"
      >
        新增人员
      </el-button>
    </div>
    
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="加载中..."
      border
      fit
      highlight-current-row
      style="width: 100%"
    >
      <el-table-column
        label="ID"
        align="center"
        width="80"
      >
        <template #default="scope">
          <span v-if="scope && scope.row">{{ scope.row.id }}</span>
        </template>
      </el-table-column>
      
      <el-table-column
        label="工号"
        align="center"
        width="120"
      >
        <template #default="scope">
          <span v-if="scope && scope.row">{{ scope.row.employeeCode }}</span>
        </template>
      </el-table-column>
      
      <el-table-column
        label="姓名"
        align="center"
        width="120"
      >
        <template #default="scope">
          <span v-if="scope && scope.row">{{ scope.row.name }}</span>
        </template>
      </el-table-column>
      
      <el-table-column
        label="职位"
        align="center"
        width="150"
      >
        <template #default="scope">
          <span v-if="scope && scope.row">{{ scope.row.position }}</span>
        </template>
      </el-table-column>
      
      <el-table-column
        label="联系方式"
        align="center"
        width="150"
      >
        <template #default="scope">
          <span v-if="scope && scope.row">{{ scope.row.contact }}</span>
        </template>
      </el-table-column>
      
      <el-table-column
        label="所属部门"
        align="center"
      >
        <template #default="scope">
          <span v-if="scope && scope.row">{{ scope.row.departmentName }}</span>
        </template>
      </el-table-column>
      
      <el-table-column
        label="创建时间"
        align="center"
        width="180"
      >
        <template #default="scope">
          <span v-if="scope && scope.row">{{ formatDateTime(scope.row.createdAt) }}</span>
        </template>
      </el-table-column>
      
      <el-table-column
        label="操作"
        align="center"
        width="230"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <div v-if="scope && scope.row">
            <el-button
              size="mini"
              type="primary"
              @click="handleUpdate(scope.row)"
            >
              编辑
            </el-button>
            <el-button
              v-if="!hasRelations(scope.row)"
              size="mini"
              type="danger"
              @click="handleDelete(scope.row)"
            >
              删除
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />
    
    <el-dialog
      :title="dialogTitle"
      v-model="dialogFormVisible"
      width="500px"
    >
      <el-form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="left"
        label-width="100px"
      >
        <el-form-item label="姓名" prop="name">
          <el-input v-model="temp.name" placeholder="请输入姓名" />
        </el-form-item>
        
        <el-form-item label="工号" prop="employeeCode">
          <el-input v-model="temp.employeeCode" placeholder="请输入工号" />
        </el-form-item>
        
        <el-form-item label="职位" prop="position">
          <el-input v-model="temp.position" placeholder="请输入职位" />
        </el-form-item>
        
        <el-form-item label="联系方式" prop="contact">
          <el-input v-model="temp.contact" placeholder="请输入联系方式" />
        </el-form-item>
        
        <el-form-item label="所属部门" prop="departmentId">
          <el-select
            v-model="temp.departmentId"
            placeholder="请选择部门"
            clearable
            style="width: 100%"
            @change="handleDepartmentChange"
          >
            <el-option
              v-for="item in departmentOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogFormVisible = false">
            取消
          </el-button>
          <el-button type="primary" @click="submitForm" :loading="formLoading">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { formatDateTime } from '@/utils/format'
import Pagination from '@/components/Pagination'
import personnelApi from '@/api/personnel'
import departmentApi from '@/api/department'

export default {
  name: 'PersonnelManagement',
  components: {
    Pagination
  },
  data() {
    return {
      list: [],
      total: 0,
      listLoading: false,
      listQuery: {
        page: 1,
        limit: 20,
        keyword: '',
        departmentId: null
      },
      departmentOptions: [],
      dialogFormVisible: false,
      dialogTitle: '',
      formLoading: false,
      temp: {
        id: undefined,
        name: '',
        position: '',
        contact: '',
        departmentId: null,
        departmentName: '',
        employeeCode: ''
      },
      rules: {
        name: [{ required: true, message: '姓名不能为空', trigger: 'blur' }],
        employeeCode: [{ required: true, message: '工号不能为空', trigger: 'blur' }],
        contact: [{ required: true, message: '联系方式不能为空', trigger: 'blur' }],
        departmentId: [{ required: true, message: '请选择所属部门', trigger: 'change' }]
      }
    }
  },
  created() {
    this.initData()
  },
  methods: {
    formatDateTime,
    
    // 初始化数据
    async initData() {
      try {
        // 先获取部门数据，再获取人员数据
        await this.getDepartments()
        await this.getList()
      } catch (error) {
        console.error('初始化数据失败:', error)
      }
    },
    
    // 获取人员列表
    async getList() {
      this.listLoading = true
      try {
        const params = {
          keyword: this.listQuery.keyword,
          departmentId: this.listQuery.departmentId
        }
        console.log('获取人员列表 - 参数:', params)
        
        const response = await personnelApi.getPersonnelList(params)
        console.log('获取人员列表 - 响应:', response)
        
        if (response.data && response.data.success) {
          // 确保data是数组
          const personnelData = Array.isArray(response.data.data) ? response.data.data : []
          
          // 为每条记录添加departmentName属性
          this.list = personnelData.map(item => {
            if (item.departmentId && !item.departmentName) {
              // 从部门列表中查找对应部门名称
              const dept = this.departmentOptions.find(d => d.id === item.departmentId)
              item.departmentName = dept ? dept.name : '未知部门'
            }
            return item
          })
          
          this.total = this.list.length
          console.log('处理后的人员列表:', this.list)
        } else {
          console.error('获取人员列表失败:', response.data?.message || '未知错误')
          this.$message.error('获取人员列表失败: ' + (response.data?.message || '未知错误'))
          this.list = []
          this.total = 0
        }
      } catch (error) {
        console.error('获取人员列表失败:', error)
        this.$message.error('获取人员列表失败')
        this.list = []
        this.total = 0
      } finally {
        this.listLoading = false
      }
    },
    async getDepartments() {
      try {
        const response = await departmentApi.getDepartmentList()
        console.log('获取部门列表 - 响应:', response)
        
        if (response.data && response.data.success) {
          this.departmentOptions = response.data.data
        } else {
          console.error('获取部门列表失败:', response.data?.message || '未知错误')
        }
      } catch (error) {
        console.error('获取部门列表失败:', error)
        this.$message.error('获取部门列表失败')
      }
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    resetTemp() {
      this.temp = {
        id: undefined,
        name: '',
        position: '',
        contact: '',
        departmentId: null,
        departmentName: '',
        employeeCode: ''
      }
    },
    handleCreate() {
      this.resetTemp()
      this.dialogTitle = '新增人员'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        const formRef = this.$refs.dataForm
        if (formRef) {
          formRef.clearValidate()
        } else {
          console.warn('表单引用不存在，无法清除验证')
        }
      })
    },
    handleUpdate(row) {
      this.temp = JSON.parse(JSON.stringify(row))
      this.dialogTitle = '编辑人员'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        const formRef = this.$refs.dataForm
        if (formRef) {
          formRef.clearValidate()
        } else {
          console.warn('表单引用不存在，无法清除验证')
        }
      })
    },
    submitForm() {
      const formRef = this.$refs.dataForm
      if (!formRef) {
        this.$message.error('表单引用不存在，请稍后重试')
        return
      }
      
      formRef.validate(valid => {
        if (valid) {
          console.log('表单验证通过，准备提交数据:', this.temp)
          if (this.dialogTitle === '新增人员') {
            this.createData()
          } else {
            this.updateData()
          }
        } else {
          console.log('表单验证失败')
          return false
        }
      })
    },
    async handleDelete(row) {
      try {
        await this.$confirm('确定要删除该人员吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        const response = await personnelApi.deletePersonnel(row.id)
        if (response.data && response.data.success) {
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
          this.getList()
        } else {
          this.$message.error(response.data?.message || '删除失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除人员失败:', error)
          this.$message.error('删除人员失败: ' + (error.message || '未知错误'))
        }
      }
    },
    hasRelations(row) {
      // 实际应用中可能需要检查是否有关联数据
      return false
    },
    async createData() {
      if (this.formLoading) return
      this.formLoading = true
      
      try {
        const data = {
          name: this.temp.name,
          employeeCode: this.temp.employeeCode,
          position: this.temp.position || '',
          contact: this.temp.contact || '',
          departmentId: this.temp.departmentId
        }
        
        console.log('开始创建人员 - 提交数据:', data)
        
        const response = await personnelApi.createPersonnel(data)
        console.log('人员API响应:', response)
        
        if (response.data && response.data.success) {
          this.dialogFormVisible = false
          this.$message({
            type: 'success',
            message: '创建成功!'
          })
          this.getList()
        } else {
          const errorMsg = response.data?.message || '创建失败'
          console.error('创建人员失败:', errorMsg)
          this.$message.error(errorMsg)
        }
      } catch (error) {
        console.error('创建人员异常:', error)
        this.$message.error('创建人员失败: ' + (error.message || '服务器错误'))
      } finally {
        this.formLoading = false
      }
    },
    async updateData() {
      if (this.formLoading) return
      this.formLoading = true
      
      try {
        const data = {
          name: this.temp.name,
          employeeCode: this.temp.employeeCode,
          position: this.temp.position || '',
          contact: this.temp.contact || '',
          departmentId: this.temp.departmentId
        }
        
        console.log('开始更新人员 - 提交数据:', data)
        
        const response = await personnelApi.updatePersonnel(this.temp.id, data)
        console.log('更新人员响应:', response)
        
        if (response.data && response.data.success) {
          this.dialogFormVisible = false
          this.$message({
            type: 'success',
            message: '更新成功!'
          })
          this.getList()
        } else {
          const errorMsg = response.data?.message || '更新失败'
          console.error('更新人员失败:', errorMsg)
          this.$message.error(errorMsg)
        }
      } catch (error) {
        console.error('更新人员异常:', error)
        this.$message.error('更新人员失败: ' + (error.message || '服务器错误'))
      } finally {
        this.formLoading = false
      }
    },
    handleDepartmentChange(value) {
      this.temp.departmentId = value
      if (value) {
        const department = this.departmentOptions.find(dept => dept.id === value)
        if (department) {
          this.temp.departmentName = department.name
        }
      } else {
        this.temp.departmentName = ''
      }
    }
  }
}
</script>

<style scoped>
.filter-container {
  padding-bottom: 10px;
}
.filter-item {
  margin-bottom: 10px;
  margin-right: 10px;
}
</style> 