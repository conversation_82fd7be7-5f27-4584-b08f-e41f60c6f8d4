/**
 * 前端字段变更检测工具
 * 用于检测表单字段的实际变更，只发送真正修改的字段到后端
 * 
 * 最佳实践：
 * 1. 只发送实际变更的字段，避免不必要的后端处理
 * 2. 精确的时间字段比较，避免格式差异导致的误判
 * 3. 支持深度对象比较和数组比较
 * 4. 提供详细的变更日志用于调试
 */

/**
 * 比较两个值是否相等（支持日期、数组、对象）
 * @param {any} oldValue 原始值
 * @param {any} newValue 新值
 * @returns {boolean} 是否相等
 */
export function isEqual(oldValue, newValue) {
  // 处理 null/undefined 情况
  if (oldValue === newValue) return true
  if (oldValue == null && newValue == null) return true
  if (oldValue == null || newValue == null) return false
  
  // 处理日期比较
  if (oldValue instanceof Date && newValue instanceof Date) {
    return oldValue.getTime() === newValue.getTime()
  }
  
  // 处理日期字符串比较（ISO格式）
  if (typeof oldValue === 'string' && typeof newValue === 'string') {
    // 尝试解析为日期进行比较
    const oldDate = new Date(oldValue)
    const newDate = new Date(newValue)
    if (!isNaN(oldDate.getTime()) && !isNaN(newDate.getTime())) {
      return oldDate.getTime() === newDate.getTime()
    }
  }
  
  // 处理数组比较
  if (Array.isArray(oldValue) && Array.isArray(newValue)) {
    if (oldValue.length !== newValue.length) return false
    return oldValue.every((item, index) => isEqual(item, newValue[index]))
  }
  
  // 处理对象比较
  if (typeof oldValue === 'object' && typeof newValue === 'object') {
    const oldKeys = Object.keys(oldValue)
    const newKeys = Object.keys(newValue)
    if (oldKeys.length !== newKeys.length) return false
    return oldKeys.every(key => isEqual(oldValue[key], newValue[key]))
  }
  
  // 基本类型比较
  return oldValue === newValue
}

/**
 * 检测对象字段变更
 * @param {Object} originalData 原始数据
 * @param {Object} currentData 当前数据
 * @param {Array<string>} fieldsToCheck 需要检查的字段列表
 * @param {Object} fieldMappings 字段映射关系 { frontendField: 'backendField' }
 * @returns {Object} { hasChanges: boolean, changedFields: Object, changeLog: Array }
 */
export function detectChanges(originalData, currentData, fieldsToCheck, fieldMappings = {}) {
  const changedFields = {}
  const changeLog = []
  
  console.log('🔍 开始字段变更检测')
  console.log('原始数据:', originalData)
  console.log('当前数据:', currentData)
  console.log('检查字段:', fieldsToCheck)
  
  fieldsToCheck.forEach(field => {
    const backendField = fieldMappings[field] || field
    const oldValue = originalData[field]
    const newValue = currentData[field]
    
    if (!isEqual(oldValue, newValue)) {
      changedFields[backendField] = newValue
      changeLog.push({
        field: field,
        backendField: backendField,
        oldValue: oldValue,
        newValue: newValue,
        type: getValueType(newValue)
      })
      
      console.log(`✅ 检测到变更: ${field} (${backendField})`)
      console.log(`   旧值: ${formatValue(oldValue)}`)
      console.log(`   新值: ${formatValue(newValue)}`)
    } else {
      console.log(`⚪ 无变更: ${field}`)
    }
  })
  
  const hasChanges = Object.keys(changedFields).length > 0
  
  console.log('🎯 变更检测结果:')
  console.log(`   有变更: ${hasChanges}`)
  console.log(`   变更字段数: ${Object.keys(changedFields).length}`)
  console.log('   变更字段:', changedFields)
  
  return {
    hasChanges,
    changedFields,
    changeLog
  }
}

/**
 * 获取值的类型
 * @param {any} value 值
 * @returns {string} 类型字符串
 */
function getValueType(value) {
  if (value === null) return 'null'
  if (value === undefined) return 'undefined'
  if (value instanceof Date) return 'date'
  if (Array.isArray(value)) return 'array'
  return typeof value
}

/**
 * 格式化值用于显示
 * @param {any} value 值
 * @returns {string} 格式化后的字符串
 */
function formatValue(value) {
  if (value === null) return 'null'
  if (value === undefined) return 'undefined'
  if (value instanceof Date) return value.toISOString()
  if (Array.isArray(value)) return `[${value.join(', ')}]`
  if (typeof value === 'object') return JSON.stringify(value)
  return String(value)
}

/**
 * 任务更新专用的变更检测
 * @param {Object} originalTask 原始任务数据
 * @param {Object} currentForm 当前表单数据
 * @returns {Object} 变更检测结果
 */
export function detectTaskChanges(originalTask, currentForm) {
  // 定义需要检查的字段
  const fieldsToCheck = [
    'name',
    'description', 
    'priority',
    'status',
    'taskType',
    'planStartDate',
    'planEndDate',
    'actualStartDate',
    'actualEndDate',
    'assigneeUserId',
    'progress',
    'points',
    'categoryId',
    'assetId',
    'locationId',
    'projectId',
    'parentTaskId',
    'pdcaStage',
    'isOverdueAcknowledged'
  ]
  
  // 字段映射关系（前端字段名 -> 后端字段名）
  const fieldMappings = {
    'title': 'name',           // 前端可能使用title，后端使用name
    'startDate': 'planStartDate',
    'dueDate': 'planEndDate',
    'endDate': 'planEndDate'
  }
  
  return detectChanges(originalTask, currentForm, fieldsToCheck, fieldMappings)
}

/**
 * 处理时间字段的特殊格式化
 * @param {any} value 时间值
 * @returns {string|null} ISO格式的时间字符串或null
 */
export function formatDateForApi(value) {
  if (!value) return null
  
  if (value instanceof Date) {
    return value.toISOString()
  }
  
  if (typeof value === 'string') {
    const date = new Date(value)
    if (!isNaN(date.getTime())) {
      return date.toISOString()
    }
  }
  
  return null
}

/**
 * 构建只包含变更字段的API请求数据
 * @param {Object} changeResult 变更检测结果
 * @param {Object} additionalData 额外需要包含的数据
 * @returns {Object} API请求数据
 */
export function buildApiRequestData(changeResult, additionalData = {}) {
  if (!changeResult.hasChanges && Object.keys(additionalData).length === 0) {
    return null // 没有变更且没有额外数据，返回null表示无需请求
  }
  
  const apiData = { ...changeResult.changedFields, ...additionalData }
  
  // 处理时间字段的格式化
  const timeFields = ['planStartDate', 'planEndDate', 'actualStartDate', 'actualEndDate']
  timeFields.forEach(field => {
    if (apiData[field] !== undefined) {
      apiData[field] = formatDateForApi(apiData[field])
    }
  })
  
  console.log('🚀 构建API请求数据:', apiData)
  
  return apiData
}
