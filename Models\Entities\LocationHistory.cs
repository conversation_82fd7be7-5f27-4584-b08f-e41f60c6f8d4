// IT资产管理系统 - 位置历史记录实体
// 文件路径: /Models/Entities/LocationHistory.cs
// 功能: 定义位置历史记录，记录资产位置变更历史

using System;
using ItAssetsSystem.Models;

namespace ItAssetsSystem.Models.Entities
{
    /// <summary>
    /// 位置历史记录实体
    /// </summary>
    public class LocationHistory
    {
        /// <summary>
        /// 位置历史ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 资产ID
        /// </summary>
        public int AssetId { get; set; }

        /// <summary>
        /// 原位置ID
        /// </summary>
        public int? OldLocationId { get; set; }

        /// <summary>
        /// 目标位置ID
        /// </summary>
        public int NewLocationId { get; set; }

        /// <summary>
        /// 变更人ID
        /// </summary>
        public int OperatorId { get; set; }

        /// <summary>
        /// 变更类型（0:转移, 1:领用, 2:归还）
        /// </summary>
        public int ChangeType { get; set; }

        /// <summary>
        /// 变更备注
        /// </summary>
        public string Notes { get; set; }

        /// <summary>
        /// 变更时间
        /// </summary>
        public DateTime ChangeTime { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// 资产
        /// </summary>
        public virtual Asset Asset { get; set; }

        /// <summary>
        /// 源位置
        /// </summary>
        public virtual Location OldLocation { get; set; }

        /// <summary>
        /// 目标位置
        /// </summary>
        public virtual Location NewLocation { get; set; }

        /// <summary>
        /// 变更人
        /// </summary>
        public virtual User Operator { get; set; }
    }
} 