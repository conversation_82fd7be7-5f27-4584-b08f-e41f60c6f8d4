// File: Application/Features/Notes/Dtos/UpdateQuickMemoRequestDto.cs
// Description: DTO for updating an existing QuickMemo.
#nullable enable
using System.ComponentModel.DataAnnotations;

namespace ItAssetsSystem.Application.Features.Notes.Dtos
{
    public class UpdateQuickMemoRequestDto
    {
        [MaxLength(200, ErrorMessage = "Title cannot exceed 200 characters.")]
        public string? Title { get; set; } // Nullable for partial updates

        public string? Content { get; set; } // Nullable, allows clearing content

        public string? CategoryId { get; set; } // Nullable, allows changing or clearing category

        public bool? IsPinned { get; set; } // Nullable for partial updates

        [MaxLength(7, ErrorMessage = "Color hex code cannot exceed 7 characters.")]
        public string? Color { get; set; } // Nullable, allows changing or clearing color

        // New properties for panel layout (optional for partial updates)
        public double? PositionX { get; set; }
        public double? PositionY { get; set; }
        public int? SizeWidth { get; set; }
        public int? SizeHeight { get; set; }
        public int? ZIndex { get; set; }
    }
} 