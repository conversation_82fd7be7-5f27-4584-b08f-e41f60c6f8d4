{"version": 3, "file": "index.prod.js", "sources": ["index.ts"], "sourcesContent": ["// eslint-disable-next-line import/no-extraneous-dependencies\nimport interact from '@interactjs/interactjs'\n\nexport default interact\n\nif (typeof module === 'object' && !!module) {\n  try {\n    module.exports = interact\n  } catch {}\n}\n\n;(interact as any).default = interact\n"], "names": ["module", "exports", "interact", "_unused", "default"], "mappings": ";;2HAKA,GAAsB,iBAAXA,QAAyBA,OAClC,IACEA,OAAOC,QAAUC,QACnB,CAAE,MAAAC,GAAO,CAGTD,SAAiBE,QAAUF"}