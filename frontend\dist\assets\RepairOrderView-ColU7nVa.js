import{_ as e,r as a,j as l,k as t,o as s,w as u,l as r,m as n,b as i,$ as d,e as o,a as p,d as c,t as m,p as f,i as v,E as b,ad as _,z as y,f as g,aW as h,as as C,ao as w,aX as D,af as k,F as N,h as x,a9 as V}from"./index-CG5lHOPO.js";import{R as z}from"./RepairOrderDialog--HNxb8Jy.js";import"./spareparts-DKUrs8IX.js";const R={class:"repair-order-detail"},I={key:0,class:"detail-content"},S={class:"info-item"},B={class:"info-item"},U={class:"info-item"},q={class:"info-item"},$={class:"info-item"},O={class:"info-item"},j={class:"info-item"},F={class:"info-item"},T={class:"info-item"},A={class:"info-item"},L={class:"info-item"},P={class:"info-item"},E={class:"info-item"},K={class:"info-item"},W={class:"info-item"},X={key:1},G={class:"dialog-footer"},H=e({__name:"RepairOrderDetailDialog",props:{modelValue:{type:Boolean,default:!1},orderId:{type:Number,default:null}},emits:["update:modelValue","refresh"],setup(e,{emit:_}){const y=e,g=_,h=a(!1),C=a(null),w=e=>null==e||""===e?"0.00":Number(e).toLocaleString("zh-CN",{minimumFractionDigits:2,maximumFractionDigits:2}),D=e=>e?new Date(e).toLocaleString("zh-CN"):"-",k=e=>{const a=e.totalCost-e.estimatedCost;return a>0?"cost-over":a<0?"cost-under":"cost-equal"},N=()=>{g("update:modelValue",!1)},x=()=>{b.success("审核成功"),g("refresh"),N()},V=()=>{b.success("发货成功"),g("refresh"),N()},z=()=>{b.success("维修完成"),g("refresh"),N()},H=async()=>{if(y.orderId){h.value=!0;try{C.value={id:y.orderId,orderNumber:"RF20240618001",typeName:"备件维修",priority:2,priorityName:"高",status:1,statusName:"待审核",title:"变频器模块故障维修",description:"变频器模块出现过热保护，需要返厂检修",requesterName:"张三",supplierName:"ABB电气设备公司",createdAt:new Date,estimatedCost:2500,items:[{partName:"变频器模块",partCode:"SP002",quantity:1,faultDescription:"过热保护",repairResult:"",cost:null}]}}catch(e){b.error("获取返厂单详情失败")}finally{h.value=!1}}};return l((()=>y.modelValue),(e=>{e&&y.orderId&&H()})),l((()=>y.orderId),(e=>{e&&y.modelValue&&H()})),(e,a)=>{const l=p("el-col"),b=p("el-tag"),_=p("el-row"),H=p("el-card"),J=p("el-table-column"),M=p("el-table"),Q=p("el-button"),Y=p("el-dialog"),Z=n("loading");return s(),t(Y,{"model-value":y.modelValue,"onUpdate:modelValue":a[0]||(a[0]=e=>g("update:modelValue",e)),title:"返厂维修单详情",width:"80%","before-close":N},{footer:u((()=>[c("div",G,[o(Q,{onClick:N},{default:u((()=>a[20]||(a[20]=[f("关闭")]))),_:1}),C.value&&1===C.value.status?(s(),t(Q,{key:0,type:"primary",onClick:x},{default:u((()=>a[21]||(a[21]=[f(" 审核通过 ")]))),_:1})):d("",!0),C.value&&2===C.value.status?(s(),t(Q,{key:1,type:"warning",onClick:V},{default:u((()=>a[22]||(a[22]=[f(" 确认发货 ")]))),_:1})):d("",!0),C.value&&4===C.value.status?(s(),t(Q,{key:2,type:"success",onClick:z},{default:u((()=>a[23]||(a[23]=[f(" 完成维修 ")]))),_:1})):d("",!0)])])),default:u((()=>[r((s(),i("div",R,[C.value?(s(),i("div",I,[o(H,{class:"info-card",style:{"margin-bottom":"16px"}},{header:u((()=>a[1]||(a[1]=[c("span",{class:"card-title"},"📋 基本信息",-1)]))),default:u((()=>[o(_,{gutter:16},{default:u((()=>[o(l,{span:8},{default:u((()=>[c("div",S,[a[2]||(a[2]=c("label",null,"返厂单号：",-1)),c("span",null,m(C.value.orderNumber),1)])])),_:1}),o(l,{span:8},{default:u((()=>[c("div",B,[a[3]||(a[3]=c("label",null,"维修类型：",-1)),c("span",null,m(C.value.typeName),1)])])),_:1}),o(l,{span:8},{default:u((()=>{return[c("div",U,[a[4]||(a[4]=c("label",null,"优先级：",-1)),o(b,{type:(e=C.value.priority,{1:"danger",2:"warning",3:"info",4:"success"}[e]||"info"),size:"small"},{default:u((()=>[f(m(C.value.priorityName),1)])),_:1},8,["type"])])];var e})),_:1})])),_:1}),o(_,{gutter:16,style:{"margin-top":"16px"}},{default:u((()=>[o(l,{span:8},{default:u((()=>{return[c("div",q,[a[5]||(a[5]=c("label",null,"状态：",-1)),o(b,{type:(e=C.value.status,{1:"warning",2:"info",3:"primary",4:"warning",5:"success",6:"danger"}[e]||"info"),size:"small"},{default:u((()=>[f(m(C.value.statusName),1)])),_:1},8,["type"])])];var e})),_:1}),o(l,{span:8},{default:u((()=>[c("div",$,[a[6]||(a[6]=c("label",null,"申请人：",-1)),c("span",null,m(C.value.requesterName),1)])])),_:1}),o(l,{span:8},{default:u((()=>[c("div",O,[a[7]||(a[7]=c("label",null,"供应商：",-1)),c("span",null,m(C.value.supplierName),1)])])),_:1})])),_:1}),o(_,{gutter:16,style:{"margin-top":"16px"}},{default:u((()=>[o(l,{span:24},{default:u((()=>[c("div",j,[a[8]||(a[8]=c("label",null,"维修标题：",-1)),c("span",null,m(C.value.title),1)])])),_:1})])),_:1}),o(_,{gutter:16,style:{"margin-top":"16px"}},{default:u((()=>[o(l,{span:24},{default:u((()=>[c("div",F,[a[9]||(a[9]=c("label",null,"维修描述：",-1)),c("p",null,m(C.value.description||"-"),1)])])),_:1})])),_:1})])),_:1}),o(H,{class:"info-card",style:{"margin-bottom":"16px"}},{header:u((()=>a[10]||(a[10]=[c("span",{class:"card-title"},"⏰ 时间信息",-1)]))),default:u((()=>[o(_,{gutter:16},{default:u((()=>[o(l,{span:6},{default:u((()=>[c("div",T,[a[11]||(a[11]=c("label",null,"创建时间：",-1)),c("span",null,m(D(C.value.createdAt)),1)])])),_:1}),o(l,{span:6},{default:u((()=>[c("div",A,[a[12]||(a[12]=c("label",null,"发货时间：",-1)),c("span",null,m(C.value.shipDate?D(C.value.shipDate):"-"),1)])])),_:1}),o(l,{span:6},{default:u((()=>[c("div",L,[a[13]||(a[13]=c("label",null,"预计返回：",-1)),c("span",null,m(C.value.expectedReturnDate?D(C.value.expectedReturnDate):"-"),1)])])),_:1}),o(l,{span:6},{default:u((()=>[c("div",P,[a[14]||(a[14]=c("label",null,"实际返回：",-1)),c("span",null,m(C.value.actualReturnDate?D(C.value.actualReturnDate):"-"),1)])])),_:1})])),_:1})])),_:1}),o(H,{class:"info-card",style:{"margin-bottom":"16px"}},{header:u((()=>a[15]||(a[15]=[c("span",{class:"card-title"},"💰 费用信息",-1)]))),default:u((()=>[o(_,{gutter:16},{default:u((()=>[o(l,{span:8},{default:u((()=>[c("div",E,[a[16]||(a[16]=c("label",null,"预估费用：",-1)),c("span",null,m(C.value.estimatedCost?`¥${w(C.value.estimatedCost)}`:"-"),1)])])),_:1}),o(l,{span:8},{default:u((()=>[c("div",K,[a[17]||(a[17]=c("label",null,"实际费用：",-1)),c("span",null,m(C.value.totalCost?`¥${w(C.value.totalCost)}`:"-"),1)])])),_:1}),o(l,{span:8},{default:u((()=>[c("div",W,[a[18]||(a[18]=c("label",null,"费用差异：",-1)),C.value.estimatedCost&&C.value.totalCost?(s(),i("span",{key:0,class:v(k(C.value))}," ¥"+m(w(C.value.totalCost-C.value.estimatedCost)),3)):(s(),i("span",X,"-"))])])),_:1})])),_:1})])),_:1}),o(H,{class:"info-card"},{header:u((()=>a[19]||(a[19]=[c("span",{class:"card-title"},"📦 维修物品",-1)]))),default:u((()=>[o(M,{data:C.value.items||[],border:"",style:{width:"100%"}},{default:u((()=>[o(J,{prop:"partName",label:"备件名称","min-width":"150"}),o(J,{prop:"partCode",label:"备件编码",width:"120"}),o(J,{prop:"quantity",label:"数量",width:"80",align:"center"}),o(J,{prop:"faultDescription",label:"故障描述","min-width":"200"}),o(J,{prop:"repairResult",label:"维修结果","min-width":"150"}),o(J,{prop:"cost",label:"费用",width:"100",align:"right"},{default:u((({row:e})=>[f(m(e.cost?`¥${w(e.cost)}`:"-"),1)])),_:1})])),_:1},8,["data"])])),_:1})])):d("",!0)])),[[Z,h.value]])])),_:1},8,["model-value"])}}},[["__scopeId","data-v-a6d0aaa3"]]),J={class:"repair-order-view"},M={class:"page-header"},Q={class:"stats-cards"},Y={class:"stat-info"},Z={class:"stat-icon"},ee={class:"stat-details"},ae={class:"stat-count"},le={class:"stat-info"},te={class:"stat-icon"},se={class:"stat-details"},ue={class:"stat-count"},re={class:"stat-info"},ne={class:"stat-icon"},ie={class:"stat-details"},de={class:"stat-count"},oe={class:"stat-info"},pe={class:"stat-icon"},ce={class:"stat-details"},me={class:"stat-count"},fe={class:"filter-container"},ve={key:1},be={class:"pagination-container"},_e=e({__name:"RepairOrderView",setup(e){const l=a(!1),R=a([]),I=a(0),S=a([]),B=a([]),U=a([]),q=a(!1),$=a(!1),O=a(null),j=a({}),F=a("create"),T=a({pendingCount:0,inRepairCount:0,completedCount:0,totalCost:0}),A=_({pageIndex:1,pageSize:10,orderNumber:"",type:null,status:null,priority:null,requesterId:null,supplierId:null,startDate:null,endDate:null,sortBy:"",sortOrder:""}),L=e=>null==e||""===e?"0.00":Number(e).toLocaleString("zh-CN",{minimumFractionDigits:2,maximumFractionDigits:2}),P=e=>e?new Date(e).toLocaleDateString("zh-CN"):"-",E=e=>{if(!e.expectedReturnDate||!e.actualReturnDate)return"";const a=new Date(e.expectedReturnDate),l=new Date(e.actualReturnDate);return l>a?"return-late":l<=a?"return-ontime":""},K=()=>{A.pageIndex=1,De()},W=()=>{Object.keys(A).forEach((e=>{["pageIndex","pageSize"].includes(e)||(A[e]="pageIndex"===e?1:"pageSize"===e?10:e.includes("Date")?null:"")})),U.value=[],K()},X=e=>{e&&2===e.length?(A.startDate=e[0],A.endDate=e[1]):(A.startDate=null,A.endDate=null)},G=e=>{e.prop&&e.order?(A.sortBy=e.prop,A.sortOrder="ascending"===e.order?"asc":"desc"):(A.sortBy="",A.sortOrder=""),De()},_e=e=>{A.pageSize=e,De()},ye=e=>{A.pageIndex=e,De()},ge=()=>{j.value={},F.value="create",$.value=!0},he=e=>{O.value=e.id,q.value=!0},Ce=()=>{b.info("导出返厂单列表")},we=()=>{De(),ke()},De=async()=>{l.value=!0;try{R.value=[],I.value=0}catch(e){b.error("获取返厂单列表失败")}finally{l.value=!1}},ke=async()=>{try{T.value={pendingCount:5,inRepairCount:12,completedCount:48,totalCost:125600.5}}catch(e){}};return y((()=>{De(),ke(),(async()=>{try{S.value=[]}catch(e){}})(),(async()=>{try{B.value=[]}catch(e){}})()})),(e,a)=>{const _=p("el-button"),y=p("el-icon"),ke=p("el-card"),Ne=p("el-col"),xe=p("el-row"),Ve=p("el-input"),ze=p("el-form-item"),Re=p("el-option"),Ie=p("el-select"),Se=p("el-date-picker"),Be=p("el-form"),Ue=p("el-link"),qe=p("el-table-column"),$e=p("el-tag"),Oe=p("el-table"),je=p("el-pagination"),Fe=n("loading");return s(),i("div",J,[c("div",M,[a[12]||(a[12]=c("h2",null,"返厂维修管理",-1)),o(_,{type:"primary",onClick:ge},{default:u((()=>a[11]||(a[11]=[f("新建返厂单")]))),_:1})]),c("div",Q,[o(xe,{gutter:16},{default:u((()=>[o(Ne,{span:6},{default:u((()=>[o(ke,{class:"stat-card pending"},{default:u((()=>[c("div",Y,[c("div",Z,[o(y,null,{default:u((()=>[o(g(h))])),_:1})]),c("div",ee,[c("div",ae,m(T.value.pendingCount),1),a[13]||(a[13]=c("div",{class:"stat-label"},"待审核",-1))])])])),_:1})])),_:1}),o(Ne,{span:6},{default:u((()=>[o(ke,{class:"stat-card in-repair"},{default:u((()=>[c("div",le,[c("div",te,[o(y,null,{default:u((()=>[o(g(C))])),_:1})]),c("div",se,[c("div",ue,m(T.value.inRepairCount),1),a[14]||(a[14]=c("div",{class:"stat-label"},"维修中",-1))])])])),_:1})])),_:1}),o(Ne,{span:6},{default:u((()=>[o(ke,{class:"stat-card completed"},{default:u((()=>[c("div",re,[c("div",ne,[o(y,null,{default:u((()=>[o(g(w))])),_:1})]),c("div",ie,[c("div",de,m(T.value.completedCount),1),a[15]||(a[15]=c("div",{class:"stat-label"},"已完成",-1))])])])),_:1})])),_:1}),o(Ne,{span:6},{default:u((()=>[o(ke,{class:"stat-card total-cost"},{default:u((()=>[c("div",oe,[c("div",pe,[o(y,null,{default:u((()=>[o(g(D))])),_:1})]),c("div",ce,[c("div",me,"¥"+m(L(T.value.totalCost)),1),a[16]||(a[16]=c("div",{class:"stat-label"},"总费用",-1))])])])),_:1})])),_:1})])),_:1})]),c("div",fe,[o(Be,{inline:!0,model:A,class:"demo-form-inline"},{default:u((()=>[o(ze,{label:"返厂单号"},{default:u((()=>[o(Ve,{modelValue:A.orderNumber,"onUpdate:modelValue":a[0]||(a[0]=e=>A.orderNumber=e),placeholder:"返厂单号",clearable:"",onKeyup:k(K,["enter"])},null,8,["modelValue"])])),_:1}),o(ze,{label:"维修类型"},{default:u((()=>[o(Ie,{modelValue:A.type,"onUpdate:modelValue":a[1]||(a[1]=e=>A.type=e),placeholder:"维修类型",clearable:""},{default:u((()=>[o(Re,{label:"故障维修",value:1}),o(Re,{label:"备件维修",value:2}),o(Re,{label:"预防性维修",value:3})])),_:1},8,["modelValue"])])),_:1}),o(ze,{label:"状态"},{default:u((()=>[o(Ie,{modelValue:A.status,"onUpdate:modelValue":a[2]||(a[2]=e=>A.status=e),placeholder:"状态",clearable:""},{default:u((()=>[o(Re,{label:"待审核",value:1}),o(Re,{label:"已审核",value:2}),o(Re,{label:"已发货",value:3}),o(Re,{label:"维修中",value:4}),o(Re,{label:"已完成",value:5}),o(Re,{label:"已取消",value:6})])),_:1},8,["modelValue"])])),_:1}),o(ze,{label:"优先级"},{default:u((()=>[o(Ie,{modelValue:A.priority,"onUpdate:modelValue":a[3]||(a[3]=e=>A.priority=e),placeholder:"优先级",clearable:""},{default:u((()=>[o(Re,{label:"紧急",value:1}),o(Re,{label:"高",value:2}),o(Re,{label:"中",value:3}),o(Re,{label:"低",value:4})])),_:1},8,["modelValue"])])),_:1}),o(ze,{label:"申请人"},{default:u((()=>[o(Ie,{modelValue:A.requesterId,"onUpdate:modelValue":a[4]||(a[4]=e=>A.requesterId=e),placeholder:"申请人",clearable:"",filterable:""},{default:u((()=>[(s(!0),i(N,null,x(S.value,(e=>(s(),t(Re,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),o(ze,{label:"供应商"},{default:u((()=>[o(Ie,{modelValue:A.supplierId,"onUpdate:modelValue":a[5]||(a[5]=e=>A.supplierId=e),placeholder:"供应商",clearable:""},{default:u((()=>[(s(!0),i(N,null,x(B.value,(e=>(s(),t(Re,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),o(ze,{label:"创建时间"},{default:u((()=>[o(Se,{modelValue:U.value,"onUpdate:modelValue":a[6]||(a[6]=e=>U.value=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",onChange:X},null,8,["modelValue"])])),_:1}),o(ze,null,{default:u((()=>[o(_,{type:"primary",onClick:K},{default:u((()=>a[17]||(a[17]=[f("查询")]))),_:1}),o(_,{onClick:W},{default:u((()=>a[18]||(a[18]=[f("重置")]))),_:1}),o(_,{type:"success",onClick:Ce},{default:u((()=>a[19]||(a[19]=[f("导出")]))),_:1})])),_:1})])),_:1},8,["model"])]),r((s(),t(Oe,{data:R.value,border:"",style:{width:"100%"},onSortChange:G},{default:u((()=>[o(qe,{prop:"orderNumber",label:"返厂单号",width:"150",sortable:"custom"},{default:u((({row:e})=>[o(Ue,{type:"primary",onClick:a=>he(e)},{default:u((()=>[f(m(e.orderNumber),1)])),_:2},1032,["onClick"])])),_:1}),o(qe,{prop:"typeName",label:"维修类型",width:"100"}),o(qe,{prop:"title",label:"维修标题","min-width":"200","show-overflow-tooltip":""}),o(qe,{prop:"priorityName",label:"优先级",width:"80"},{default:u((({row:e})=>{return[o($e,{type:(a=e.priority,{1:"danger",2:"warning",3:"info",4:"success"}[a]||"info"),size:"small"},{default:u((()=>[f(m(e.priorityName),1)])),_:2},1032,["type"])];var a})),_:1}),o(qe,{prop:"statusName",label:"状态",width:"100"},{default:u((({row:e})=>{return[o($e,{type:(a=e.status,{1:"warning",2:"info",3:"primary",4:"warning",5:"success",6:"danger"}[a]||"info"),size:"small"},{default:u((()=>[f(m(e.statusName),1)])),_:2},1032,["type"])];var a})),_:1}),o(qe,{prop:"requesterName",label:"申请人",width:"100"}),o(qe,{prop:"supplierName",label:"供应商",width:"120"}),o(qe,{prop:"itemCount",label:"物品数量",width:"80",align:"center"},{default:u((({row:e})=>[o($e,{size:"small",type:"info"},{default:u((()=>[f(m(e.itemCount)+"项",1)])),_:2},1024)])),_:1}),o(qe,{prop:"estimatedCost",label:"预估费用",width:"100",align:"right"},{default:u((({row:e})=>[f(m(e.estimatedCost?`¥${L(e.estimatedCost)}`:"-"),1)])),_:1}),o(qe,{prop:"totalCost",label:"实际费用",width:"100",align:"right"},{default:u((({row:e})=>[f(m(e.totalCost?`¥${L(e.totalCost)}`:"-"),1)])),_:1}),o(qe,{prop:"shipDate",label:"发货日期",width:"100"},{default:u((({row:e})=>[f(m(e.shipDate?P(e.shipDate):"-"),1)])),_:1}),o(qe,{prop:"expectedReturnDate",label:"预计返回",width:"100"},{default:u((({row:e})=>[f(m(e.expectedReturnDate?P(e.expectedReturnDate):"-"),1)])),_:1}),o(qe,{prop:"actualReturnDate",label:"实际返回",width:"100"},{default:u((({row:e})=>[e.actualReturnDate?(s(),i("span",{key:0,class:v(E(e))},m(P(e.actualReturnDate)),3)):(s(),i("span",ve,"-"))])),_:1}),o(qe,{prop:"createdAt",label:"创建时间",width:"100"},{default:u((({row:e})=>[f(m(P(e.createdAt)),1)])),_:1}),o(qe,{label:"操作",width:"200",fixed:"right"},{default:u((({row:e})=>[o(_,{type:"info",size:"small",onClick:a=>he(e)},{default:u((()=>a[20]||(a[20]=[f("详情")]))),_:2},1032,["onClick"]),1===e.status?(s(),t(_,{key:0,type:"primary",size:"small",onClick:a=>(e=>{V.confirm(`确认审核返厂单 "${e.orderNumber}" 吗？`,"确认审核",{confirmButtonText:"确认",cancelButtonText:"取消",type:"warning"}).then((()=>{b.success("审核成功"),De()})).catch((()=>{}))})(e)},{default:u((()=>a[21]||(a[21]=[f(" 审核 ")]))),_:2},1032,["onClick"])):d("",!0),2===e.status?(s(),t(_,{key:1,type:"warning",size:"small",onClick:a=>(e=>{V.confirm(`确认发货返厂单 "${e.orderNumber}" 吗？`,"确认发货",{confirmButtonText:"确认",cancelButtonText:"取消",type:"warning"}).then((()=>{b.success("发货成功"),De()})).catch((()=>{}))})(e)},{default:u((()=>a[22]||(a[22]=[f(" 发货 ")]))),_:2},1032,["onClick"])):d("",!0),4===e.status?(s(),t(_,{key:2,type:"success",size:"small",onClick:a=>(e=>{O.value=e.id,b.info("打开完成维修对话框")})(e)},{default:u((()=>a[23]||(a[23]=[f(" 完成 ")]))),_:2},1032,["onClick"])):d("",!0),[1,2].includes(e.status)?(s(),t(_,{key:3,type:"danger",size:"small",onClick:a=>(e=>{V.confirm(`确认取消返厂单 "${e.orderNumber}" 吗？`,"确认取消",{confirmButtonText:"确认",cancelButtonText:"取消",type:"warning"}).then((()=>{b.success("取消成功"),De()})).catch((()=>{}))})(e)},{default:u((()=>a[24]||(a[24]=[f(" 取消 ")]))),_:2},1032,["onClick"])):d("",!0),o(_,{type:"text",size:"small",onClick:e=>{b.info("打印返厂单")}},{default:u((()=>a[25]||(a[25]=[f("打印")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"])),[[Fe,l.value]]),c("div",be,[o(je,{currentPage:A.pageIndex,"onUpdate:currentPage":a[7]||(a[7]=e=>A.pageIndex=e),"page-size":A.pageSize,"onUpdate:pageSize":a[8]||(a[8]=e=>A.pageSize=e),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:I.value,onSizeChange:_e,onCurrentChange:ye},null,8,["currentPage","page-size","total"])]),o(H,{modelValue:q.value,"onUpdate:modelValue":a[9]||(a[9]=e=>q.value=e),"order-id":O.value,onRefresh:De},null,8,["modelValue","order-id"]),o(z,{visible:$.value,"onUpdate:visible":a[10]||(a[10]=e=>$.value=e),"order-data":j.value,mode:F.value,onSubmit:we},null,8,["visible","order-data","mode"])])}}},[["__scopeId","data-v-2e78756d"]]);export{_e as default};
