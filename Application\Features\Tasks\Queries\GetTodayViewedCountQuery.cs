// File: Application/Features/Tasks/Queries/GetTodayViewedCountQuery.cs
// Description: 查询当前用户今日已查看任务数

using MediatR;
using System.Threading;
using System.Threading.Tasks;

namespace ItAssetsSystem.Application.Features.Tasks.Queries
{
    public class GetTodayViewedCountQuery : IRequest<int>
    {
        public long UserId { get; set; }
    }

    public class GetTodayViewedCountQueryHandler : IRequestHandler<GetTodayViewedCountQuery, int>
    {
        public GetTodayViewedCountQueryHandler()
        {
        }

        public async Task<int> Handle(GetTodayViewedCountQuery request, CancellationToken cancellationToken)
        {
            // TODO: 这里应接入真实的"任务查看日志"表，统计今日已查看任务数
            // 目前先返回0，后续可接入真实数据
            return await Task.FromResult(0);
        }
    }
} 