<template>
  <el-dialog
    v-model="visible"
    :title="isEdit ? '编辑任务' : '任务详情'"
    width="70%"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    class="task-detail-dialog"
    @close="handleClose"
  >
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="8" animated />
    </div>

    <!-- 任务详情内容 -->
    <div v-else-if="taskData" class="task-detail-content">
      <!-- 任务头部 -->
      <div class="task-header">
        <div class="task-title-section">
          <div class="task-meta-info">
            <el-tag 
              :type="getStatusTagType(taskData.status)" 
              size="large"
              class="status-tag"
            >
              {{ getStatusText(taskData.status) }}
            </el-tag>
            <el-tag 
              :type="getPriorityTagType(taskData.priority)" 
              size="small"
              round
              class="priority-tag"
            >
              {{ getPriorityText(taskData.priority) }}
            </el-tag>
            <span v-if="taskData.taskType === 'PDCA'" class="pdca-badge">
              PDCA-{{ taskData.pdcaStage }}
            </span>
            <span v-if="taskData.isOverdue" class="overdue-indicator">
              <el-icon><WarningFilled /></el-icon>
              已逾期
            </span>
          </div>
          
          <h2 class="task-title" v-if="!isEditingTitle" @dblclick="startEditTitle">
            {{ taskData.name }}
            <el-icon class="edit-icon"><Edit /></el-icon>
          </h2>
          
          <el-input
            v-else
            v-model="editTitle"
            size="large"
            @blur="saveTitle"
            @keyup.enter="saveTitle"
            @keyup.esc="cancelEditTitle"
            ref="titleInput"
            class="title-input"
          />
        </div>

        <!-- 快速操作按钮 -->
        <div class="quick-actions">
          <el-button-group>
            <el-button 
              v-if="taskData.status !== 'Done'"
              type="success" 
              @click="completeTask"
              :loading="completing"
            >
              <el-icon><Check /></el-icon>
              完成任务
            </el-button>
            <el-button @click="toggleEdit">
              <el-icon><Edit /></el-icon>
              {{ isEdit ? '取消编辑' : '编辑' }}
            </el-button>
            <el-dropdown @command="handleQuickAction">
              <el-button>
                更多操作<el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="duplicate">复制任务</el-dropdown-item>
                  <el-dropdown-item command="convert">转换为模板</el-dropdown-item>
                  <el-dropdown-item command="archive">归档</el-dropdown-item>
                  <el-dropdown-item command="delete" divided>
                    <span style="color: var(--el-color-danger)">删除任务</span>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </el-button-group>
        </div>
      </div>

      <!-- 主要内容区域 -->
      <div class="task-main-content">
        <!-- 左侧详情面板 -->
        <div class="task-details-panel">
          <el-tabs v-model="activeTab" class="task-tabs">
            <!-- 详情页签 -->
            <el-tab-pane label="详情" name="details">
              <div class="task-details">
                <!-- 描述区域 -->
                <div class="detail-section">
                  <h4 class="section-title">任务描述</h4>
                  <div v-if="!isEdit" class="description-display">
                    <div v-if="taskData.description" v-html="renderMarkdown(taskData.description)"></div>
                    <div v-else class="empty-description" @click="isEdit = true">
                      点击添加描述...
                    </div>
                  </div>
                  <el-input
                    v-else
                    v-model="editForm.description"
                    type="textarea"
                    :rows="6"
                    placeholder="输入任务描述，支持Markdown格式..."
                    class="description-editor"
                  />
                </div>

                <!-- 关联信息 -->
                <div class="detail-section">
                  <h4 class="section-title">关联信息</h4>
                  <div class="relation-grid">

                    <div class="relation-item">
                      <label>父任务:</label>
                      <div v-if="!isEdit">
                        <el-link 
                          v-if="taskData.parentTaskName" 
                          type="primary"
                          @click="openParentTask"
                        >
                          {{ taskData.parentTaskName }}
                        </el-link>
                        <span v-else class="empty-value">无</span>
                      </div>
                      <TaskSelect v-else v-model="editForm.parentTaskId" />
                    </div>

                    <div class="relation-item">
                      <label>子任务:</label>
                      <div>
                        <el-badge :value="taskData.subTaskCount" class="item">
                          <el-button size="small" text @click="showSubTasks">
                            查看子任务
                          </el-button>
                        </el-badge>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 时间信息 -->
                <div class="detail-section">
                  <h4 class="section-title">时间信息</h4>
                  <div class="time-grid">
                    <div class="time-item">
                      <label>计划开始:</label>
                      <div v-if="!isEdit">
                        {{ formatDateTime(taskData.planStartDate) || '未设置' }}
                      </div>
                      <el-date-picker
                        v-else
                        v-model="editForm.planStartDate"
                        type="datetime"
                        placeholder="选择开始时间"
                        format="YYYY-MM-DD HH:mm"
                        value-format="YYYY-MM-DDTHH:mm:ss"
                        size="small"
                      />
                    </div>
                    
                    <div class="time-item">
                      <label>计划结束:</label>
                      <div v-if="!isEdit" :class="{ 'overdue-date': taskData.isOverdue }">
                        {{ formatDateTime(taskData.planEndDate) || '未设置' }}
                      </div>
                      <el-date-picker
                        v-else
                        v-model="editForm.planEndDate"
                        type="datetime"
                        placeholder="选择结束时间"
                        format="YYYY-MM-DD HH:mm"
                        value-format="YYYY-MM-DDTHH:mm:ss"
                        size="small"
                      />
                    </div>

                    <div class="time-item">
                      <label>实际开始:</label>
                      <div>{{ formatDateTime(taskData.actualStartDate) || '未开始' }}</div>
                    </div>
                    
                    <div class="time-item">
                      <label>实际结束:</label>
                      <div>{{ formatDateTime(taskData.actualEndDate) || '未完成' }}</div>
                    </div>
                  </div>
                </div>

                <!-- 进度信息 -->
                <div class="detail-section">
                  <h4 class="section-title">进度跟踪</h4>
                  <div class="progress-section">
                    <div class="progress-display">
                      <span class="progress-label">完成进度:</span>
                      <div class="progress-controls">
                        <el-slider
                          v-if="isEdit || canUpdateProgress"
                          v-model="editForm.progress"
                          :disabled="!isEdit && !canUpdateProgress"
                          @change="updateProgress"
                          class="progress-slider"
                        />
                        <el-progress
                          v-else
                          :percentage="taskData.progress"
                          :stroke-width="12"
                          :color="getProgressColor(taskData.progress)"
                        />
                        <span class="progress-text">{{ taskData.progress }}%</span>
                      </div>
                    </div>
                    
                    <div class="points-display" v-if="taskData.points > 0">
                      <el-icon><Star /></el-icon>
                      <span>完成可获得 {{ taskData.points }} 积分</span>
                    </div>
                  </div>
                </div>
              </div>
            </el-tab-pane>

            <!-- 评论页签 -->
            <el-tab-pane name="comments">
              <template #label>
                评论 
                <el-badge 
                  v-if="taskData.commentCount > 0" 
                  :value="taskData.commentCount" 
                  class="item"
                />
              </template>
              <TaskComments 
                :task-id="taskData.taskId"
                :comments="comments"
                @add-comment="handleAddComment"
                @load-comments="loadComments"
              />
            </el-tab-pane>

            <!-- 附件页签 -->
            <el-tab-pane name="attachments">
              <template #label>
                附件 
                <el-badge 
                  v-if="taskData.attachmentCount > 0" 
                  :value="taskData.attachmentCount" 
                  class="item"
                />
              </template>
              <TaskAttachments 
                :task-id="taskData.taskId"
                :attachments="attachments"
                @upload-attachment="handleUploadAttachment"
                @delete-attachment="handleDeleteAttachment"
                @load-attachments="loadAttachments"
              />
            </el-tab-pane>

            <!-- 历史页签 -->
            <el-tab-pane label="历史" name="history">
              <TaskHistory 
                :task-id="taskData.taskId"
                :history="history"
                @load-history="loadHistory"
              />
            </el-tab-pane>
          </el-tabs>
        </div>

        <!-- 右侧属性面板 -->
        <div class="task-properties-panel">
          <div class="properties-card">
            <h4 class="panel-title">任务属性</h4>
            
            <!-- 所有负责人 -->
            <div class="property-item">
              <label class="property-label">负责人</label>
              <div v-if="!isEdit" class="property-value">
                <UserAvatarStack
                  v-if="getAllAssignees(taskData).length > 0"
                  :users="getAllAssignees(taskData)"
                  :is-main-user-primary="true"
                  :max-users="5"
                  :show-details="true"
                  avatar-size="24"
                  :overlap="8"
                />
                <span v-else class="empty-value">未分配</span>
              </div>
              <UserSelect v-else v-model="editForm.assigneeUserIds" :multiple="true" />
            </div>

            <!-- 状态 -->
            <div class="property-item">
              <label class="property-label">状态</label>
              <div v-if="!isEdit" class="property-value">
                <el-tag :type="getStatusTagType(taskData.status)">
                  {{ getStatusText(taskData.status) }}
                </el-tag>
              </div>
              <el-select v-else v-model="editForm.status" size="small">
                <el-option
                  v-for="status in statusOptions"
                  :key="status"
                  :label="getStatusText(status)"
                  :value="status"
                />
              </el-select>
            </div>

            <!-- 优先级 -->
            <div class="property-item">
              <label class="property-label">优先级</label>
              <div v-if="!isEdit" class="property-value">
                <el-tag :type="getPriorityTagType(taskData.priority)" size="small">
                  {{ getPriorityText(taskData.priority) }}
                </el-tag>
              </div>
              <el-select v-else v-model="editForm.priority" size="small">
                <el-option
                  v-for="priority in priorityOptions"
                  :key="priority"
                  :label="getPriorityText(priority)"
                  :value="priority"
                />
              </el-select>
            </div>

            <!-- 任务类型 -->
            <div class="property-item">
              <label class="property-label">类型</label>
              <div v-if="!isEdit" class="property-value">
                <el-tag type="info" size="small">{{ taskData.taskType }}</el-tag>
              </div>
              <el-select v-else v-model="editForm.taskType" size="small">
                <el-option
                  v-for="type in typeOptions"
                  :key="type"
                  :label="type"
                  :value="type"
                />
              </el-select>
            </div>

            <!-- 任务分类 -->
            <div class="property-item">
              <label class="property-label">分类</label>
              <div v-if="!isEdit" class="property-value">
                <el-tag
                  v-if="taskData.categoryName"
                  :color="taskData.categoryColor || '#409EFF'"
                  size="small"
                  style="color: white;"
                >
                  <el-icon v-if="taskData.categoryIcon" style="margin-right: 4px;">
                    <component :is="taskData.categoryIcon" />
                  </el-icon>
                  {{ taskData.categoryName }}
                </el-tag>
                <span v-else class="empty-value">未分类</span>
              </div>
              <el-select
                v-else
                v-model="editForm.categoryId"
                size="small"
                placeholder="请选择分类"
                clearable
                :loading="categoriesLoading"
              >
                <el-option
                  v-for="category in categories"
                  :key="category.categoryId"
                  :label="category.name"
                  :value="category.categoryId"
                >
                  <div class="category-option">
                    <el-icon v-if="category.icon" :style="{ color: category.color || '#409EFF' }">
                      <component :is="category.icon" />
                    </el-icon>
                    <span>{{ category.name }}</span>
                    <span class="category-desc" v-if="category.description">{{ category.description }}</span>
                  </div>
                </el-option>
              </el-select>
            </div>

            <!-- 标签 -->
            <div class="property-item">
              <label class="property-label">标签</label>
              <div class="tags-display">
                <el-tag
                  v-for="tag in taskData.tagList"
                  :key="tag"
                  size="small"
                  closable
                  @close="removeTag(tag)"
                  v-if="!isEdit"
                >
                  {{ tag }}
                </el-tag>
                <TagInput v-else v-model="editForm.tagList" />
              </div>
            </div>

            <!-- 创建信息 -->
            <div class="creation-info">
              <h4 class="panel-title">创建信息</h4>
              <div class="info-item">
                <span class="info-label">创建者:</span>
                <UserAvatar
                  :user-id="taskData.creatorUserId"
                  :user-name="taskData.creatorUserName"
                  :avatar-url="taskData.creatorUserAvatarUrl"
                  size="small"
                />
                <span class="creator-name">{{ taskData.creatorUserName }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">创建时间:</span>
                <span>{{ formatDateTime(taskData.creationTimestamp) }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">最后更新:</span>
                <span>{{ formatDateTime(taskData.lastUpdatedTimestamp) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 对话框底部操作 -->
    <template #footer>
      <div class="dialog-footer">
        <div class="footer-left">
          <el-button 
            v-if="taskData && taskData.taskId"
            type="info" 
            text
            @click="copyTaskLink"
          >
            <el-icon><Link /></el-icon>
            复制链接
          </el-button>
        </div>
        <div class="footer-right">
          <el-button @click="handleClose">取消</el-button>
          <el-button 
            v-if="isEdit"
            type="primary" 
            @click="saveTask"
            :loading="saving"
          >
            保存更改
          </el-button>
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useTaskEnhancedStore } from '@/stores/modules/taskEnhanced'
import { formatDateTime } from '@/utils/format'
import UserAvatar from '@/components/UserAvatar.vue'
import UserAvatarStack from '@/components/UserAvatarStack.vue'
import UserSelect from '@/components/UserSelect.vue'
import AssetSelect from '@/components/AssetSelect.vue'
import LocationSelect from '@/components/LocationSelect.vue'
import TaskSelect from '@/components/TaskSelect.vue'
import TagInput from '@/components/TagInput.vue'
import TaskComments from './TaskComments.vue'
import TaskAttachments from './TaskAttachments.vue'
import taskCategoryApi from '@/api/taskCategory'
import TaskHistory from './TaskHistory.vue'
import { taskApi } from '@/api/task'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  task: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:modelValue', 'save', 'delete', 'close', 'updated'])

// Store
const taskStore = useTaskEnhancedStore()

// 响应式状态
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const loading = ref(false)
const saving = ref(false)
const completing = ref(false)
const isEdit = ref(false)
const activeTab = ref('details')

const taskData = ref(null)
const editForm = reactive({})
const originalForm = reactive({})

// 标题编辑
const isEditingTitle = ref(false)
const editTitle = ref('')
const titleInput = ref(null)

// 子数据
const comments = ref([])
const attachments = ref([])
const history = ref([])

// 选项数据
const statusOptions = ref(['Todo', 'InProgress', 'Done', 'Cancelled'])
const priorityOptions = ref(['Low', 'Medium', 'High', 'Urgent'])
const typeOptions = ref(['Normal', 'Periodic', 'PDCA'])

// 分类相关数据
const categories = ref([])
const categoriesLoading = ref(false)

// 权限检查
const canUpdateProgress = computed(() => {
  // 检查用户是否可以更新进度（负责人或创建者）
  return taskData.value && (
    taskData.value.assigneeUserId === getCurrentUserId() ||
    taskData.value.creatorUserId === getCurrentUserId()
  )
})

// 监听器
watch(() => props.task, (newTask) => {
  if (newTask) {
    loadTaskData(newTask)
  }
}, { immediate: true })

watch(() => props.modelValue, (newValue) => {
  if (newValue && props.task) {
    loadTaskData(props.task)
    loadComments()
    loadAttachments()
    loadHistory()
    loadTaskCategories()
  }
})

// 方法
const loadTaskData = async (task) => {
  if (!task) return
  
  loading.value = true
  try {
    if (typeof task === 'object' && task.taskId) {
      // 如果传入的是完整的任务对象
      taskData.value = { ...task }
    } else {
      // 如果传入的是任务ID，需要获取详细信息
      const taskId = typeof task === 'number' ? task : task.taskId
      const detailTask = await taskStore.getTaskById(taskId)
      if (!detailTask) {
        emit('close')
        return
      }
      taskData.value = detailTask
    }
    
    // 初始化编辑表单
    initEditForm()
  } catch (error) {
    ElMessage.error('加载任务详情失败: ' + error.message)
    taskData.value = null
    emit('close')
  } finally {
    loading.value = false
  }
}

const initEditForm = () => {
  if (!taskData.value) return
  
  // 获取所有负责人
  const allAssignees = getAllAssignees(taskData.value);
  const assigneeUserIds = allAssignees.map(user => user.userId);
  
  console.log('初始化编辑表单，所有负责人:', allAssignees);
  
  const formData = {
    name: taskData.value.name,
    description: taskData.value.description,
    status: taskData.value.status,
    priority: taskData.value.priority,
    taskType: taskData.value.taskType,
    categoryId: taskData.value.categoryId,
    assigneeUserId: taskData.value.assigneeUserId,
    assigneeUserIds: assigneeUserIds.length > 0 ? assigneeUserIds : [taskData.value.assigneeUserId].filter(Boolean),
    planStartDate: taskData.value.planStartDate ? new Date(taskData.value.planStartDate) : null,
    planEndDate: taskData.value.planEndDate ? new Date(taskData.value.planEndDate) : null,
    progress: taskData.value.progress,
    parentTaskId: taskData.value.parentTaskId,
    tagList: [...(taskData.value.tagList || [])],
    pdcaStage: taskData.value.pdcaStage,
    points: taskData.value.points
  }
  
  Object.assign(editForm, formData)
  Object.assign(originalForm, formData)
}

const toggleEdit = () => {
  if (isEdit.value) {
    // 取消编辑，恢复原始数据
    Object.assign(editForm, originalForm)
  }
  isEdit.value = !isEdit.value
}

const saveTask = async () => {
  if (!taskData.value || !taskData.value.taskId) {
    ElMessage.error('保存失败: 任务ID不能为空')
    return
  }
  
  saving.value = true
  try {
    const taskId = taskData.value.taskId
    // 确保taskId是数值类型而不是字符串"0"
    if (taskId === 0 || taskId === '0') {
      throw new Error('任务ID不能为0')
    }
    
    // 处理多负责人：确保传递所有选择的负责人ID
    let assigneeUserId = null;
    let collaboratorUserIds = [];
    
    if (Array.isArray(editForm.assigneeUserIds) && editForm.assigneeUserIds.length > 0) {
      assigneeUserId = editForm.assigneeUserIds[0];
      collaboratorUserIds = editForm.assigneeUserIds.slice(1);
      // 确保包含所有选择的负责人
      console.log('处理多负责人:', {
        主负责人: assigneeUserId,
        协作者: collaboratorUserIds,
        所有负责人: editForm.assigneeUserIds
      });
    }
    
    const updateData = {
      name: editForm.name,
      description: editForm.description,
      status: editForm.status,
      priority: editForm.priority,
      taskType: editForm.taskType,
      categoryId: editForm.categoryId,
      assigneeUserId: assigneeUserId,
      collaboratorUserIds: collaboratorUserIds,
      // 🕐 修复时间字段：保持完整的日期时间格式
      planStartDate: editForm.planStartDate || null,
      planEndDate: editForm.planEndDate || null,
      progress: editForm.progress,
      parentTaskId: editForm.parentTaskId,
      pdcaStage: editForm.pdcaStage,
      points: editForm.points,
      tagList: editForm.tagList
    };
    
    console.log('提交更新数据:', updateData);
    
    const result = await taskApi.updateTask(taskId, updateData)
    if (result.success) {
      taskData.value = result.data
    initEditForm()
    isEdit.value = false
    
    ElMessage.success('任务更新成功')
      emit('save', result.data)
      emit('updated', result.data) // 触发列表刷新
    } else {
      throw new Error(result.message || '保存失败')
    }
  } catch (error) {
    ElMessage.error('保存失败: ' + error.message)
  } finally {
    saving.value = false
  }
}

const completeTask = async () => {
  if (!taskData.value) return
  
  completing.value = true
  try {
    const completedTask = await taskStore.completeTask(taskData.value.taskId)
    taskData.value = completedTask
    ElMessage.success('任务已完成')
    emit('save', completedTask)
    emit('updated', completedTask) // 触发列表刷新
  } catch (error) {
    ElMessage.error('完成任务失败: ' + error.message)
  } finally {
    completing.value = false
  }
}

const handleQuickAction = async (command) => {
  switch (command) {
    case 'duplicate':
      await duplicateTask()
      break
    case 'convert':
      await convertToTemplate()
      break
    case 'archive':
      await archiveTask()
      break
    case 'delete':
      await deleteTask()
      break
  }
}

const duplicateTask = async () => {
  try {
    const newTaskData = {
      ...editForm,
      name: taskData.value.name + ' (副本)',
      status: 'Todo',
      progress: 0,
      actualStartDate: null,
      actualEndDate: null
    }
    delete newTaskData.taskId
    
    await taskStore.createTask(newTaskData)
    ElMessage.success('任务复制成功')
    emit('updated') // 触发列表刷新
  } catch (error) {
    ElMessage.error('复制任务失败: ' + error.message)
  }
}

const convertToTemplate = async () => {
  ElMessage.info('转换为模板功能开发中...')
}

const archiveTask = async () => {
  try {
    await taskStore.updateTaskStatus(taskData.value.taskId, 'Archived')
    ElMessage.success('任务已归档')
    emit('updated') // 触发列表刷新
    handleClose()
  } catch (error) {
    ElMessage.error('归档失败: ' + error.message)
  }
}

const deleteTask = async () => {
  try {
    await ElMessageBox.confirm('确定要删除这个任务吗？此操作不可恢复。', '确认删除', {
      type: 'warning'
    })
    
    await taskStore.deleteTask(taskData.value.taskId)
    ElMessage.success('任务删除成功')
    emit('delete', taskData.value.taskId)
    emit('updated') // 触发列表刷新
    handleClose()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败: ' + error.message)
    }
  }
}

// 标题编辑
const startEditTitle = () => {
  isEditingTitle.value = true
  editTitle.value = taskData.value.name
  nextTick(() => {
    titleInput.value?.focus()
  })
}

const saveTitle = async () => {
  if (editTitle.value.trim() && editTitle.value !== taskData.value.name) {
    try {
      const taskId = taskData.value.taskId
      if (!taskId || taskId === 0 || taskId === '0') {
        throw new Error('任务ID不能为空或为0')
      }
      
      const updatedTask = await taskStore.updateTask({
        taskId: taskId, 
        name: editTitle.value.trim()
      })
      
      taskData.value.name = updatedTask.name
      ElMessage.success('标题更新成功')
      emit('updated', updatedTask) // 触发列表刷新
    } catch (error) {
      ElMessage.error('更新标题失败: ' + error.message)
    }
  }
  isEditingTitle.value = false
}

const cancelEditTitle = () => {
  isEditingTitle.value = false
  editTitle.value = taskData.value.name
}

// 进度更新
const updateProgress = async (newProgress) => {
  if (!canUpdateProgress.value) return
  
  try {
    await taskStore.updateTaskProgress(taskData.value.taskId, newProgress)
    taskData.value.progress = newProgress
  } catch (error) {
    ElMessage.error('更新进度失败: ' + error.message)
    editForm.progress = taskData.value.progress // 恢复原值
  }
}

// 子数据加载
const loadComments = async () => {
  if (!taskData.value) return
  try {
    comments.value = await taskStore.getTaskComments(taskData.value.taskId)
  } catch (error) {
    console.error('加载评论失败:', error)
  }
}

const loadAttachments = async () => {
  if (!taskData.value) return
  try {
    attachments.value = await taskStore.getTaskAttachments(taskData.value.taskId)
  } catch (error) {
    console.error('加载附件失败:', error)
  }
}

const loadHistory = async () => {
  if (!props.task?.taskId) return
  const res = await taskApi.getTaskHistory(props.task.taskId)
  history.value = res.data || []
}

// 加载任务分类
const loadTaskCategories = async () => {
  try {
    categoriesLoading.value = true
    const response = await taskCategoryApi.getActiveCategories()
    if (response.success) {
      categories.value = response.data || []
    } else {
      console.error('获取任务分类失败:', response.message)
      categories.value = []
    }
  } catch (error) {
    console.error('获取任务分类失败:', error)
    categories.value = []
  } finally {
    categoriesLoading.value = false
  }
}

// 评论和附件处理
const handleAddComment = async (commentData) => {
  try {
    const newComment = await taskStore.addComment(taskData.value.taskId, commentData.content, commentData.mentionedUserIds)
    comments.value.unshift(newComment)
    taskData.value.commentCount++
  } catch (error) {
    ElMessage.error('添加评论失败: ' + error.message)
  }
}

const handleUploadAttachment = async (file, description) => {
  try {
    const newAttachment = await taskStore.addAttachment(taskData.value.taskId, file, description)
    attachments.value.unshift(newAttachment)
    taskData.value.attachmentCount++
  } catch (error) {
    ElMessage.error('上传附件失败: ' + error.message)
  }
}

const handleDeleteAttachment = async (attachmentId) => {
  try {
    await taskStore.deleteAttachment(attachmentId)
    attachments.value = attachments.value.filter(att => att.attachmentId !== attachmentId)
    taskData.value.attachmentCount--
    ElMessage.success('附件删除成功')
  } catch (error) {
    ElMessage.error('删除附件失败: ' + error.message)
  }
}

// 其他操作
const copyTaskLink = () => {
  const link = `${window.location.origin}/tasks/${taskData.value.taskId}`
  navigator.clipboard.writeText(link).then(() => {
    ElMessage.success('链接已复制到剪贴板')
  })
}

const openParentTask = () => {
  if (taskData.value.parentTaskId) {
    // 打开父任务详情
    emit('open-task', taskData.value.parentTaskId)
  }
}

const showSubTasks = () => {
  // 显示子任务列表
  emit('show-subtasks', taskData.value.taskId)
}

const showParticipantSelector = () => {
  // 显示参与者选择器
  ElMessage.info('参与者选择功能开发中...')
}

const removeTag = async (tag) => {
  if (!isEdit.value) return
  
  const index = editForm.tagList.indexOf(tag)
  if (index > -1) {
    editForm.tagList.splice(index, 1)
  }
}

const handleClose = () => {
  isEdit.value = false
  isEditingTitle.value = false
  activeTab.value = 'details'
  emit('close')
}

// 辅助方法
const renderMarkdown = (text) => {
  // 简单的Markdown渲染，可以使用marked等库
  return text?.replace(/\n/g, '<br>') || ''
}

const getStatusTagType = (status) => {
  const types = {
    'Todo': '',
    'InProgress': 'warning',
    'Done': 'success',
    'Cancelled': 'info'
  }
  return types[status] || ''
}

const getPriorityTagType = (priority) => {
  const types = {
    'High': 'danger',
    'Medium': 'warning',
    'Low': 'success',
    'Urgent': 'danger'
  }
  return types[priority] || ''
}

const getPriorityText = (priority) => {
  const texts = {
    'High': '高',
    'Medium': '中',
    'Low': '低',
    'Urgent': '紧急'
  }
  return texts[priority] || priority
}

const getStatusText = (status) => {
  const texts = {
    'Todo': '待处理',
    'InProgress': '进行中',
    'Done': '已完成',
    'Cancelled': '已取消'
  }
  return texts[status] || status
}

const getProgressColor = (progress) => {
  if (progress >= 80) return '#67c23a'
  if (progress >= 50) return '#e6a23c'
  return '#f56c6c'
}

const getCurrentUserId = () => {
  // 获取当前用户ID
  return 1 // 临时返回，实际从用户store获取
}

// 获取所有负责人列表（与EnhancedTaskListView.vue保持一致）
const getAllAssignees = (task) => {
  if (!task) return []
  
  // 优先使用新的assignees字段（后端已优化）
  if (task.assignees && task.assignees.length > 0) {
    console.log('使用任务assignees字段获取负责人:', task.assignees);
    return task.assignees.map(assignee => ({
      id: assignee.userId,
      userId: assignee.userId,
      name: assignee.userName || '未知用户',
      userName: assignee.userName || '未知用户',
      avatarUrl: assignee.avatarUrl || '',
      role: assignee.role || (assignee.assignmentType === 'Responsible' ? 'Primary' : 'Collaborator'),
      assignmentType: assignee.assignmentType
    }))
  }
  
  // 向后兼容：使用原有逻辑
  const assignees = []
  
  // 添加主负责人
  if (task.assigneeUserId) {
    assignees.push({
      id: task.assigneeUserId,
      userId: task.assigneeUserId,
      name: task.assigneeUserName || '未知用户',
      userName: task.assigneeUserName || '未知用户',
      avatarUrl: task.assigneeAvatarUrl || '',
      role: 'Primary'
    })
  }
  
  // 添加协作者/参与者
  if (task.participants && task.participants.length > 0) {
    task.participants.forEach(participant => {
      // 防止重复添加主负责人
      if (participant && (participant.id || participant.userId) !== task.assigneeUserId) {
        assignees.push({
          id: participant.id || participant.userId,
          userId: participant.id || participant.userId,
          name: participant.name || participant.userName || '未知用户',
          userName: participant.name || participant.userName || '未知用户',
          avatarUrl: participant.avatarUrl || '',
          role: 'Collaborator'
        })
      }
    })
  }
  
  console.log('获取所有负责人结果:', assignees);
  return assignees
}
</script>

<style scoped>
.task-detail-dialog {
  :deep(.el-dialog) {
    border-radius: 12px;
    overflow: hidden;
  }
  
  :deep(.el-dialog__header) {
    padding: 20px 24px 0;
    border-bottom: none;
  }
  
  :deep(.el-dialog__body) {
    padding: 0 24px 20px;
    max-height: 80vh;
    overflow-y: auto;
  }
}

.loading-container {
  padding: 20px;
}

.task-detail-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding-bottom: 20px;
  border-bottom: 1px solid #eee;
}

.task-title-section {
  flex: 1;
}

.task-meta-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.status-tag {
  font-weight: 600;
}

.priority-tag {
  font-size: 10px;
}

.pdca-badge {
  background: #409eff;
  color: white;
  font-size: 10px;
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: bold;
}

.overdue-indicator {
  color: #f56c6c;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.task-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
}

.task-title:hover .edit-icon {
  opacity: 1;
}

.edit-icon {
  opacity: 0;
  transition: opacity 0.3s;
  font-size: 16px;
  color: #909399;
}

.title-input {
  font-size: 24px;
  font-weight: 600;
}

.quick-actions {
  display: flex;
  gap: 8px;
}

.task-main-content {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 24px;
}

.task-details-panel {
  min-height: 400px;
}

.task-tabs {
  :deep(.el-tabs__nav-scroll) {
    padding-left: 0;
  }
}

.detail-section {
  margin-bottom: 24px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 12px 0;
}

.description-display {
  min-height: 60px;
  padding: 12px;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  background: #fafafa;
}

.empty-description {
  color: #909399;
  cursor: pointer;
  font-style: italic;
}

.empty-description:hover {
  color: #409eff;
}

.description-editor {
  :deep(.el-textarea__inner) {
    font-family: inherit;
  }
}

.relation-grid,
.time-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.relation-item,
.time-item {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.relation-item label,
.time-item label {
  font-size: 12px;
  color: #909399;
  font-weight: 500;
}

.empty-value {
  color: #c0c4cc;
  font-style: italic;
}

.overdue-date {
  color: #f56c6c;
  font-weight: 600;
}

.progress-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.progress-display {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.progress-label {
  font-size: 12px;
  color: #909399;
  font-weight: 500;
}

.progress-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.progress-slider {
  flex: 1;
}

.progress-text {
  font-weight: 600;
  min-width: 40px;
  text-align: right;
}

.points-display {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #e6a23c;
  font-size: 14px;
}

.task-properties-panel {
  border-left: 1px solid #eee;
  padding-left: 24px;
}

.properties-card {
  background: #fafafa;
  border-radius: 8px;
  padding: 16px;
}

.panel-title {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 16px 0;
}

.property-item {
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin-bottom: 16px;
}

.property-label {
  font-size: 12px;
  color: #909399;
  font-weight: 500;
}

.property-value {
  display: flex;
  align-items: center;
  gap: 8px;
}

.participants-display {
  display: flex;
  align-items: center;
  gap: 6px;
  flex-wrap: wrap;
}

.participant-avatar {
  border: 2px solid white;
  border-radius: 50%;
}

.tags-display {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.category-option {
  display: flex;
  align-items: center;
  gap: 8px;
}

.category-desc {
  color: #909399;
  font-size: 12px;
  margin-left: auto;
}

.creation-info {
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #eee;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 12px;
}

.info-label {
  color: #909399;
  min-width: 60px;
}

.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.footer-left,
.footer-right {
  display: flex;
  gap: 8px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .task-main-content {
    grid-template-columns: 1fr;
  }
  
  .task-properties-panel {
    border-left: none;
    border-top: 1px solid #eee;
    padding-left: 0;
    padding-top: 24px;
  }
}

@media (max-width: 768px) {
  .task-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .relation-grid,
  .time-grid {
    grid-template-columns: 1fr;
  }
}

.creator-name {
  font-size: 12px;
  color: #303133;
  margin-left: 4px;
}
</style>