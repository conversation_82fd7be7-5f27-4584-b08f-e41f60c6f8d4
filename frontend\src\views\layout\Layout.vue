<template>
  <div class="app-wrapper">
    <!-- 侧边栏 -->
    <div class="sidebar-container">
      <div class="logo-container">
        <img src="@/assets/logo.png" alt="Logo" class="logo">
        <h1 class="title">IT资产管理系统</h1>
      </div>
      <el-scrollbar>
        <el-menu
          :default-active="activeMenu"
          :collapse="isCollapse"
          :unique-opened="false"
          :collapse-transition="false"
          mode="vertical"
          background-color="#304156"
          text-color="#bfcbd9"
          active-text-color="#409EFF"
        >
          <sidebar-item 
            v-for="route in routes" 
            :key="route.path" 
            :item="route" 
            :base-path="route.path" 
          />
        </el-menu>
      </el-scrollbar>
    </div>
    
    <!-- 主要内容区 -->
    <div class="main-container">
      <!-- 顶部导航栏 -->
      <div class="navbar">
        <hamburger 
          :is-active="!isCollapse" 
          class="hamburger-container" 
          @toggleClick="toggleSideBar" 
        />
        <breadcrumb class="breadcrumb-container" />
        <div class="right-menu">
          <el-dropdown class="avatar-container" trigger="click">
            <div class="avatar-wrapper">
              <img :src="avatar" class="user-avatar">
              <span class="user-name">{{ userName }}</span>
              <i class="el-icon-caret-bottom" />
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <router-link to="/profile">
                  <el-dropdown-item>个人中心</el-dropdown-item>
                </router-link>
                <el-dropdown-item divided @click="logout">
                  <span style="display:block;">退出登录</span>
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
      
      <!-- 应用主体 -->
      <app-main />
    </div>
  </div>
</template>

<script>
import SidebarItem from '@/components/SidebarItem'
import AppMain from '@/components/AppMain'
import Breadcrumb from '@/components/Breadcrumb'
import Hamburger from '@/components/Hamburger'
import { computed, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from '@/stores'

export default {
  name: 'Layout',
  components: {
    SidebarItem,
    AppMain,
    Breadcrumb,
    Hamburger
  },
  setup() {
    const store = useUserStore()
    const route = useRoute()
    const router = useRouter()
    
    const isCollapse = ref(false)
    
    const toggleSideBar = () => {
      isCollapse.value = !isCollapse.value
    }
    
    const activeMenu = computed(() => {
      const { meta, path } = route
      if (meta.activeMenu) {
        return meta.activeMenu
      }
      return path
    })
    
    const routes = computed(() => {
      return store.routes
    })
    
    const userName = computed(() => {
      return store.name
    })
    
    const avatar = computed(() => {
      return store.avatar || require('@/assets/avatar.jpg')
    })
    
    const logout = async () => {
      await store.logout()
      router.push(`/login?redirect=${route.fullPath}`)
    }
    
    return {
      isCollapse,
      toggleSideBar,
      activeMenu,
      routes,
      userName,
      avatar,
      logout
    }
  }
}
</script>

<style lang="scss" scoped>
.app-wrapper {
  position: relative;
  height: 100%;
  width: 100%;
  
  .sidebar-container {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 1001;
    width: 210px;
    height: 100%;
    overflow: hidden;
    background-color: #304156;
    transition: width 0.28s;
    
    .logo-container {
      height: 50px;
      line-height: 50px;
      background: #2b2f3a;
      text-align: center;
      
      .logo {
        width: 32px;
        height: 32px;
        vertical-align: middle;
        margin-right: 12px;
      }
      
      .title {
        display: inline-block;
        margin: 0;
        color: #fff;
        font-weight: 600;
        font-size: 14px;
        vertical-align: middle;
      }
    }
  }
  
  .main-container {
    min-height: 100%;
    margin-left: 210px;
    position: relative;
    transition: margin-left 0.28s;
    
    .navbar {
      height: 50px;
      overflow: hidden;
      position: relative;
      background: #fff;
      box-shadow: 0 1px 4px rgba(0,21,41,.08);
      
      .hamburger-container {
        line-height: 46px;
        height: 100%;
        float: left;
        cursor: pointer;
        transition: background .3s;
        padding: 0 15px;
        
        &:hover {
          background: rgba(0, 0, 0, .025)
        }
      }
      
      .breadcrumb-container {
        float: left;
        margin-left: 15px;
      }
      
      .right-menu {
        float: right;
        height: 100%;
        line-height: 50px;
        margin-right: 10px;
        
        .avatar-container {
          .avatar-wrapper {
            cursor: pointer;
            position: relative;
            line-height: 1;
            
            .user-avatar {
              width: 30px;
              height: 30px;
              border-radius: 15px;
              vertical-align: middle;
            }
            
            .user-name {
              margin-left: 5px;
              color: #333;
            }
            
            .el-icon-caret-bottom {
              margin-left: 5px;
              color: #666;
            }
          }
        }
      }
    }
  }
}

/* 当侧边栏折叠时的样式 */
.app-wrapper {
  .sidebar-container {
    &.is-collapse {
      width: 64px;
      
      .logo-container {
        .title {
          display: none;
        }
      }
    }
  }
  
  .main-container {
    &.is-collapse {
      margin-left: 64px;
    }
  }
}
</style> 