{"name": "interactjs", "version": "1.10.27", "main": "dist/interact.min.js", "typings": "index.d.ts", "description": "Drag and drop, resizing and multi-touch gestures with inertia and snapping for modern browsers (and also IE9+)", "homepage": "https://interactjs.io", "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://taye.me"}], "repository": {"type": "git", "url": "https://github.com/taye/interact.js.git"}, "keywords": ["interact.js", "draggable", "droppable", "drag", "drop", "drag and drop", "resize", "touch", "multi-touch", "gesture", "snap", "inertia", "grid", "autoscroll", "SVG", "interact"], "scripts": {"test": "cd ../; npm test"}, "dependencies": {"@interactjs/types": "1.10.27"}, "sideEffects": ["**/index.js", "**/index.prod.js"], "license": "MIT", "publishConfig": {"access": "public"}, "gitHead": "3ace1cad"}