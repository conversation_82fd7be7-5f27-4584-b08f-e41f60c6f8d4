// File: Application/Features/Notes/Dtos/CreateQuickMemoRequestDto.cs
// Description: DTO for creating a new QuickMemo.
#nullable enable
using System.ComponentModel.DataAnnotations;

namespace ItAssetsSystem.Application.Features.Notes.Dtos
{
    public class CreateQuickMemoRequestDto
    {
        [Required(ErrorMessage = "Title is required.")]
        [MaxLength(200, ErrorMessage = "Title cannot exceed 200 characters.")]
        public string Title { get; set; } = string.Empty;

        public string? Content { get; set; }

        public string? CategoryId { get; set; }

        public bool IsPinned { get; set; } = false;

        [MaxLength(7, ErrorMessage = "Color hex code cannot exceed 7 characters.")]
        public string? Color { get; set; }

        // New properties for panel layout (optional during creation, defaults can be applied by service)
        public double? PositionX { get; set; }
        public double? PositionY { get; set; }
        public int? SizeWidth { get; set; }
        public int? SizeHeight { get; set; }
        public int? ZIndex { get; set; }
    }
} 