// IT资产管理系统 - 审计日志实体
// 文件路径: /Models/Entities/AuditLog.cs
// 功能: 定义审计日志实体，记录系统操作日志

using System;
using ItAssetsSystem.Models;

namespace ItAssetsSystem.Models.Entities
{
    /// <summary>
    /// 审计日志实体
    /// </summary>
    public class AuditLog : IAuditableEntity
    {
        /// <summary>
        /// 日志ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        public int? UserId { get; set; }

        /// <summary>
        /// 用户名
        /// </summary>
        public string Username { get; set; }

        /// <summary>
        /// 操作类型（1:登录, 2:查询, 3:新增, 4:修改, 5:删除)
        /// </summary>
        public int ActionType { get; set; }

        /// <summary>
        /// 操作模块
        /// </summary>
        public string Module { get; set; }

        /// <summary>
        /// 操作功能
        /// </summary>
        public string Function { get; set; }

        /// <summary>
        /// 操作内容
        /// </summary>
        public string Content { get; set; }

        /// <summary>
        /// 目标对象
        /// </summary>
        public string Target { get; set; }

        /// <summary>
        /// 目标对象ID
        /// </summary>
        public string TargetId { get; set; }

        /// <summary>
        /// IP地址
        /// </summary>
        public string IPAddress { get; set; }

        /// <summary>
        /// 操作结果（0:失败, 1:成功）
        /// </summary>
        public int Result { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; }

        /// <summary>
        /// 用户
        /// </summary>
        public virtual User User { get; set; }
    }
}

// 计划行数: 50
// 实际行数: 50 