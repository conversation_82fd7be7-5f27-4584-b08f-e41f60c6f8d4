// File: Application/Features/SpareParts/Services/SparePartService.cs
// Description: 备品备件服务实现类

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ItAssetsSystem.Application.Common.Dtos;
using ItAssetsSystem.Application.Features.SpareParts.Dtos;
using ItAssetsSystem.Application.Features.SpareParts.EventHandlers;
using ItAssetsSystem.Core.Abstractions;
using ItAssetsSystem.Domain.Entities;
using ItAssetsSystem.Domain.Events;

namespace ItAssetsSystem.Application.Features.SpareParts.Services
{
    /// <summary>
    /// 备品备件服务实现类
    /// </summary>
    public class SparePartService : ISparePartService
    {
        private readonly ISparePartRepository _sparePartRepository;
        private readonly ISparePartTypeRepository _typeRepository;
        private readonly ISparePartLocationRepository _locationRepository;
        private readonly ISparePartTransactionRepository _transactionRepository;
        private readonly ICurrentUserService _currentUserService;
        private readonly SparePartStockUpdatedEventHandler _stockUpdatedEventHandler;
        
        /// <summary>
        /// 构造函数
        /// </summary>
        public SparePartService(
            ISparePartRepository sparePartRepository,
            ISparePartTypeRepository typeRepository,
            ISparePartLocationRepository locationRepository,
            ISparePartTransactionRepository transactionRepository,
            ICurrentUserService currentUserService,
            SparePartStockUpdatedEventHandler stockUpdatedEventHandler)
        {
            _sparePartRepository = sparePartRepository;
            _typeRepository = typeRepository;
            _locationRepository = locationRepository;
            _transactionRepository = transactionRepository;
            _currentUserService = currentUserService;
            _stockUpdatedEventHandler = stockUpdatedEventHandler;
        }
        
        /// <summary>
        /// 获取备品备件列表（分页）
        /// </summary>
        /// <param name="query">查询参数</param>
        /// <returns>分页结果</returns>
        public async Task<PaginatedResult<SparePartDto>> GetSparePartsAsync(SparePartQuery query)
        {
            var (spareParts, totalCount) = await _sparePartRepository.GetSparePartsPagedAsync(query);
            
            var sparePartDtos = spareParts.Select(MapToDto).ToList();
            
            return PaginatedResult<SparePartDto>.Create(
                sparePartDtos,
                totalCount,
                query.PageIndex,
                query.PageSize);
        }
        
        /// <summary>
        /// 获取单个备品备件详情
        /// </summary>
        /// <param name="id">备件ID</param>
        /// <returns>备件DTO</returns>
        public async Task<SparePartDto> GetSparePartByIdAsync(long id)
        {
            var sparePart = await _sparePartRepository.GetByIdAsync(id);
            if (sparePart == null)
            {
                return null;
            }
            
            return MapToDto(sparePart);
        }
        
        /// <summary>
        /// 创建备品备件
        /// </summary>
        /// <param name="request">创建请求</param>
        /// <returns>创建的备件DTO</returns>
        public async Task<SparePartDto> CreateSparePartAsync(CreateSparePartRequest request)
        {
            // 验证类型是否存在
            var type = await _typeRepository.GetByIdAsync(request.TypeId);
            if (type == null)
            {
                throw new ArgumentException($"类型ID {request.TypeId} 不存在");
            }
            
            // 验证库位是否存在
            SparePartLocation location = null;
            if (request.LocationId.HasValue)
            {
                location = await _locationRepository.GetByIdAsync(request.LocationId.Value);
                if (location == null)
                {
                    throw new ArgumentException($"库位ID {request.LocationId} 不存在");
                }
            }

            // 验证备件编码是否已存在
            if (await _sparePartRepository.ExistsByCodeAsync(request.Code))
            {
                throw new ArgumentException($"备件编码 '{request.Code}' 已存在，请使用不同的编码。");
            }
            
            // 创建备件实体
            var sparePart = new SparePart
            {
                Code = request.Code,
                MaterialNumber = request.MaterialNumber,
                Name = request.Name,
                TypeId = request.TypeId,
                Specification = request.Specification,
                Brand = request.Brand,
                Unit = request.Unit,
                StockQuantity = request.InitialStock,
                WarningThreshold = request.WarningThreshold,
                MinStock = request.MinStock,
                LocationId = request.LocationId,
                Price = request.Price,
                Remarks = request.Remarks,
                CreatedAt = DateTime.Now,
                UpdatedAt = DateTime.Now
            };
            
            // 保存备件
            var createdSparePart = await _sparePartRepository.CreateAsync(sparePart);
            
            // 如果有初始库存，创建一条入库记录
            if (request.InitialStock > 0)
            {
                var transaction = new SparePartTransaction
                {
                    PartId = createdSparePart.Id,
                    LocationId = request.LocationId ?? 0,
                    Type = 1, // 入库
                    Quantity = request.InitialStock,
                    StockAfter = request.InitialStock,
                    ReasonType = 1, // 采购入库
                    ReferenceNumber = $"INIT-{DateTime.Now:yyyyMMddHHmmss}-{createdSparePart.Id}",
                    OperatorUserId = _currentUserService.UserId,
                    OperationTime = DateTime.Now,
                    Reason = "初始库存",
                    IsSystemGenerated = true,
                    BatchNumber = null
                };
                
                await _transactionRepository.CreateAsync(transaction);
            }
            
            return MapToDto(createdSparePart);
        }
        
        /// <summary>
        /// 更新备品备件
        /// </summary>
        /// <param name="id">备件ID</param>
        /// <param name="request">更新请求</param>
        /// <returns>更新后的备件DTO</returns>
        public async Task<SparePartDto> UpdateSparePartAsync(long id, CreateSparePartRequest request)
        {
            // 验证备件是否存在
            var existingSparePart = await _sparePartRepository.GetByIdAsync(id);
            if (existingSparePart == null)
            {
                return null;
            }
            
            // 验证类型是否存在
            var type = await _typeRepository.GetByIdAsync(request.TypeId);
            if (type == null)
            {
                throw new ArgumentException($"类型ID {request.TypeId} 不存在");
            }
            
            // 验证库位是否存在
            if (request.LocationId.HasValue)
            {
                var location = await _locationRepository.GetByIdAsync(request.LocationId.Value);
                if (location == null)
                {
                    throw new ArgumentException($"库位ID {request.LocationId} 不存在");
                }
            }

            // 如果编码被修改，则验证新编码的唯一性
            if (existingSparePart.Code != request.Code)
            {
                if (await _sparePartRepository.ExistsByCodeAsync(request.Code))
                {
                    throw new ArgumentException($"备件编码 '{request.Code}' 已被其他备件使用，请使用不同的编码。");
                }
            }
            
            // 更新备件属性
            existingSparePart.Code = request.Code;
            existingSparePart.MaterialNumber = request.MaterialNumber;
            existingSparePart.Name = request.Name;
            existingSparePart.TypeId = request.TypeId;
            existingSparePart.Specification = request.Specification;
            existingSparePart.Brand = request.Brand;
            existingSparePart.Unit = request.Unit;
            existingSparePart.WarningThreshold = request.WarningThreshold;
            existingSparePart.MinStock = request.MinStock;
            existingSparePart.LocationId = request.LocationId;
            existingSparePart.Price = request.Price;
            existingSparePart.Remarks = request.Remarks;
            existingSparePart.UpdatedAt = DateTime.Now;
            
            await _sparePartRepository.UpdateAsync(existingSparePart);
            
            return MapToDto(existingSparePart);
        }
        
        /// <summary>
        /// 删除备品备件
        /// </summary>
        /// <param name="id">备件ID</param>
        /// <returns>是否成功</returns>
        public async Task<bool> DeleteSparePartAsync(long id)
        {
            return await _sparePartRepository.DeleteAsync(id);
        }
        
        /// <summary>
        /// 获取库存预警列表
        /// </summary>
        /// <param name="onlyLowStock">是否只获取低于最小安全库存的备件</param>
        /// <returns>预警备件列表</returns>
        public async Task<List<SparePartDto>> GetLowStockSparePartsAsync(bool onlyLowStock = false)
        {
            var spareParts = await _sparePartRepository.GetLowStockSparePartsAsync(onlyLowStock);
            return spareParts.Select(MapToDto).ToList();
        }
        
        /// <summary>
        /// 备件入库
        /// </summary>
        /// <param name="request">入库请求</param>
        /// <returns>入库记录DTO</returns>
        public async Task<SparePartTransactionDto> SparePartInboundAsync(SparePartInboundRequest request)
        {
            // 验证备件是否存在
            var sparePart = await _sparePartRepository.GetByIdAsync(request.PartId);
            if (sparePart == null)
            {
                throw new ArgumentException($"备件ID {request.PartId} 不存在");
            }
            
            // 验证库位是否存在
            var location = await _locationRepository.GetByIdAsync(request.LocationId);
            if (location == null)
            {
                throw new ArgumentException($"库位ID {request.LocationId} 不存在");
            }
            
            // 创建入库记录
            var transaction = new SparePartTransaction
            {
                PartId = request.PartId,
                LocationId = request.LocationId,
                Type = 1, // 入库
                Quantity = request.Quantity,
                ReasonType = request.ReasonType,
                ReferenceNumber = request.ReferenceNumber,
                OperatorUserId = _currentUserService.UserId,
                OperationTime = DateTime.Now,
                Reason = request.Remarks,
                IsSystemGenerated = false
            };
            
            // 更新库存
            var success = await _sparePartRepository.UpdateStockAsync(request.PartId, request.Quantity, transaction);
            if (!success)
            {
                throw new InvalidOperationException("更新库存失败");
            }
            
            // 重新获取最新的入库记录
            var createdTransaction = await _transactionRepository.GetByIdAsync(transaction.Id);
            
            return MapToTransactionDto(createdTransaction);
        }
        
        /// <summary>
        /// 备件出库
        /// </summary>
        /// <param name="request">出库请求</param>
        /// <returns>交易记录DTO</returns>
        public async Task<SparePartTransactionDto> SparePartOutboundAsync(SparePartOutboundRequest request)
        {
            if (request == null)
            {
                throw new ArgumentNullException(nameof(request));
            }
            
            // 将请求参数映射到基类
            request.MapToBaseClass();
            
            // 检查备件是否存在
            var sparePart = await _sparePartRepository.GetByIdAsync(request.PartId);
            if (sparePart == null)
            {
                throw new ApplicationException($"备件ID为{request.PartId}的备件不存在");
            }
            
            // 检查库位是否存在
            var location = await _locationRepository.GetByIdAsync(request.LocationId);
            if (location == null)
            {
                throw new ApplicationException($"库位ID为{request.LocationId}的库位不存在");
            }
            
            // 检查库存是否足够
            if (sparePart.StockQuantity < request.Quantity)
            {
                throw new ApplicationException($"库存不足，当前库存:{sparePart.StockQuantity}，请求出库:{request.Quantity}");
            }
            
            // 创建交易记录
            var transaction = new SparePartTransaction
            {
                PartId = request.PartId,
                LocationId = request.LocationId,
                Type = 2, // 出库
                Quantity = -request.Quantity, // 出库为负数
                ReasonType = request.ReasonType,
                ReferenceNumber = request.ReferenceNumber,
                OperatorUserId = _currentUserService.UserId,
                OperationTime = DateTime.Now,
                Reason = request.Remarks,
                IsSystemGenerated = false
            };
            
            // 更新库存并计算操作后库存
            sparePart.StockQuantity -= request.Quantity;
            transaction.StockAfter = sparePart.StockQuantity;
            
            // 处理关联资产ID
            if (request.RelatedAssetId.HasValue && request.RelatedAssetId.Value > 0)
            {
                transaction.RelatedAssetId = request.RelatedAssetId;
            }
            
            // 处理关联故障ID
            if (request.RelatedFaultId.HasValue && request.RelatedFaultId.Value > 0)
            {
                transaction.RelatedFaultId = request.RelatedFaultId;
            }
            
            // 更新备件记录时间
            sparePart.UpdatedAt = DateTime.Now;
            
            // 保存交易记录和更新的备件
            await _transactionRepository.CreateAsync(transaction);
            await _sparePartRepository.UpdateAsync(sparePart);
            
            return MapToTransactionDto(transaction);
        }

        /// <summary>
        /// 库存调整
        /// </summary>
        /// <param name="request">库存调整请求</param>
        /// <returns>调整记录DTO</returns>
        public async Task<SparePartTransactionDto> StockAdjustmentAsync(StockAdjustmentRequest request)
        {
            // 验证备件是否存在
            var sparePart = await _sparePartRepository.GetByIdAsync(request.PartId);
            if (sparePart == null)
            {
                throw new ArgumentException($"备件ID {request.PartId} 不存在");
            }

            // 验证库位是否存在
            var location = await _locationRepository.GetByIdAsync(request.LocationId);
            if (location == null)
            {
                throw new ArgumentException($"库位ID {request.LocationId} 不存在");
            }

            // 如果是减少库存，检查库存是否足够
            if (request.AdjustmentQuantity < 0)
            {
                var reduceQuantity = Math.Abs(request.AdjustmentQuantity);
                if (sparePart.StockQuantity < reduceQuantity)
                {
                    throw new ApplicationException($"库存不足，当前库存:{sparePart.StockQuantity}，请求减少:{reduceQuantity}");
                }
            }

            // 生成参考单号
            var referenceNumber = request.ReferenceNumber ?? $"ADJ-{DateTime.Now:yyyyMMddHHmmss}-{request.PartId}";

            // 创建调整记录
            var transaction = new SparePartTransaction
            {
                PartId = request.PartId,
                LocationId = request.LocationId,
                Type = (byte)(request.AdjustmentQuantity > 0 ? 1 : 2), // 正数为入库，负数为出库
                Quantity = request.AdjustmentQuantity,
                ReasonType = (byte)5, // 盘点调整
                ReferenceNumber = referenceNumber,
                OperatorUserId = _currentUserService.UserId,
                OperationTime = DateTime.Now,
                Reason = request.Reason,
                IsSystemGenerated = false
            };

            // 更新库存
            var success = await _sparePartRepository.UpdateStockAsync(request.PartId, request.AdjustmentQuantity, transaction);
            if (!success)
            {
                throw new InvalidOperationException("库存调整失败");
            }

            // 重新获取最新的调整记录
            var createdTransaction = await _transactionRepository.GetByIdAsync(transaction.Id);

            // 发布库存更新事件
            var updatedSparePart = await _sparePartRepository.GetByIdAsync(request.PartId);
            var stockUpdatedEvent = new SparePartStockUpdatedEvent(
                request.PartId,
                request.AdjustmentQuantity,
                updatedSparePart.StockQuantity,
                request.Reason,
                _currentUserService.UserId,
                createdTransaction.Id);

            await _stockUpdatedEventHandler.HandleAsync(stockUpdatedEvent);

            return MapToTransactionDto(createdTransaction);
        }

        /// <summary>
        /// 将实体映射为DTO
        /// </summary>
        /// <param name="entity">备件实体</param>
        /// <returns>备件DTO</returns>
        private SparePartDto MapToDto(SparePart entity)
        {
            if (entity == null)
            {
                return null;
            }
            
            return new SparePartDto
            {
                Id = entity.Id,
                Code = entity.Code,
                MaterialNumber = entity.MaterialNumber,
                Name = entity.Name,
                TypeId = entity.TypeId,
                TypeName = entity.Type?.Name,
                Specification = entity.Specification,
                Brand = entity.Brand,
                Unit = entity.Unit,
                StockQuantity = entity.StockQuantity,
                WarningThreshold = entity.WarningThreshold,
                MinStock = entity.MinStock,
                LocationId = entity.LocationId,
                LocationName = entity.Location?.Name,
                LocationArea = entity.Location?.Area,
                Price = entity.Price,
                Remarks = entity.Remarks,
                CreatedAt = entity.CreatedAt,
                UpdatedAt = entity.UpdatedAt,
                IsLowWarningStock = entity.IsLowWarningStock(),
                IsLowMinStock = entity.IsLowMinStock()
            };
        }
        
        /// <summary>
        /// 将交易记录实体映射为DTO
        /// </summary>
        /// <param name="entity">交易记录实体</param>
        /// <returns>交易记录DTO</returns>
        private SparePartTransactionDto MapToTransactionDto(SparePartTransaction entity)
        {
            if (entity == null)
            {
                return null;
            }
            
            return new SparePartTransactionDto
            {
                Id = entity.Id,
                PartId = entity.PartId,
                PartCode = entity.Part?.Code,
                PartName = entity.Part?.Name,
                LocationId = entity.LocationId,
                LocationName = entity.Location?.Name,
                LocationArea = entity.Location?.Area,
                Type = entity.Type,
                TypeName = entity.Type == 1 ? "入库" : "出库",
                Quantity = entity.Quantity,
                StockAfter = entity.StockAfter,
                ReasonType = entity.ReasonType,
                ReasonTypeName = GetReasonTypeName(entity.ReasonType),
                ReferenceNumber = entity.ReferenceNumber,
                OperatorUserId = entity.OperatorUserId,
                OperatorName = "系统管理员", // TODO: 从用户服务获取真实用户名
                OperationTime = entity.OperationTime,
                Remarks = entity.Reason,
                BatchNumber = entity.BatchNumber,
                IsSystemGenerated = entity.IsSystemGenerated
            };
        }
        
        /// <summary>
        /// 获取原因类型名称
        /// </summary>
        /// <param name="reasonType">原因类型编码</param>
        /// <returns>原因类型名称</returns>
        private string GetReasonTypeName(byte reasonType)
        {
            switch (reasonType)
            {
                case 1:
                    return "采购入库";
                case 2:
                    return "退回入库";
                case 3:
                    return "领用出库";
                case 4:
                    return "报废出库";
                case 5:
                    return "盘点调整";
                default:
                    return "未知";
            }
        }
    }
} 