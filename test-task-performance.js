// 任务查询性能测试脚本
// 测试优化后的任务列表查询性能

const testTaskPerformance = async () => {
    const baseUrl = 'http://localhost:5001';
    const endpoint = '/api/v2/tasks';
    
    console.log('开始测试任务查询性能...');
    console.log('目标：查询时间应该 < 500ms (行业标准)');
    console.log('之前问题：查询时间 6000ms');
    console.log('='.repeat(50));
    
    try {
        // 测试参数
        const testParams = {
            pageNumber: 1,
            pageSize: 20,
            status: '',
            assigneeUserId: '',
            creatorUserId: '',
            assetId: '',
            locationId: '',
            priority: '',
            taskType: '',
            searchKeyword: ''
        };
        
        const queryString = new URLSearchParams(testParams).toString();
        const fullUrl = `${baseUrl}${endpoint}?${queryString}`;
        
        console.log(`测试URL: ${fullUrl}`);
        console.log('开始计时...');
        
        const startTime = performance.now();
        
        const response = await fetch(fullUrl, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
        });
        
        const endTime = performance.now();
        const duration = endTime - startTime;
        
        console.log(`响应状态: ${response.status}`);
        console.log(`查询耗时: ${duration.toFixed(2)}ms`);
        
        if (response.ok) {
            const data = await response.json();
            console.log(`返回数据条数: ${data.data?.length || 0}`);
            console.log(`总记录数: ${data.totalCount || 0}`);
            
            // 性能评估
            if (duration < 500) {
                console.log('✅ 性能优秀: 查询时间 < 500ms');
            } else if (duration < 1000) {
                console.log('⚠️  性能一般: 查询时间 < 1000ms');
            } else if (duration < 3000) {
                console.log('❌ 性能较差: 查询时间 < 3000ms');
            } else {
                console.log('💥 性能极差: 查询时间 > 3000ms');
            }
            
            // 计算性能改进
            const originalTime = 6000;
            const improvement = ((originalTime - duration) / originalTime * 100).toFixed(1);
            console.log(`性能改进: ${improvement}% (从 ${originalTime}ms 降至 ${duration.toFixed(2)}ms)`);
            
        } else {
            console.error(`请求失败: ${response.status} ${response.statusText}`);
            const errorText = await response.text();
            console.error('错误详情:', errorText);
        }
        
    } catch (error) {
        console.error('测试过程中发生错误:', error.message);
        
        if (error.message.includes('fetch')) {
            console.log('提示: 请确保应用程序已启动在 http://localhost:5001');
        }
    }
    
    console.log('='.repeat(50));
    console.log('测试完成');
};

// 如果在Node.js环境中运行
if (typeof window === 'undefined') {
    // Node.js环境
    const { performance } = require('perf_hooks');
    global.fetch = require('node-fetch');
    
    testTaskPerformance().catch(console.error);
} else {
    // 浏览器环境
    testTaskPerformance().catch(console.error);
}
