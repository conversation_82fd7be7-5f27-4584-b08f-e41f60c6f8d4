<template>
  <el-dialog
    v-model="visible"
    :title="title"
    width="500px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form ref="formRef" :model="form" label-width="100px">
      <el-form-item label="导入格式">
        <el-select v-model="form.format" placeholder="请选择导入格式">
          <el-option
            v-for="format in formats"
            :key="format"
            :label="format"
            :value="format"
          />
        </el-select>
      </el-form-item>
      
      <el-form-item label="导入文件">
        <el-upload
          class="upload-demo"
          drag
          action="#"
          :auto-upload="false"
          :on-change="handleFileChange"
          :file-list="fileList"
          :limit="1"
        >
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">
            将文件拖到此处，或<em>点击上传</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              请上传符合模板格式的文件
            </div>
          </template>
        </el-upload>
      </el-form-item>

      <el-form-item v-if="importProgress > 0" label="导入进度">
        <el-progress :percentage="importProgress" />
      </el-form-item>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleDownloadTemplate">
          下载模板
        </el-button>
        <el-button
          type="success"
          :loading="importing"
          :disabled="!form.file"
          @click="handleImport"
        >
          开始导入
        </el-button>
      </span>
    </template>
  </el-dialog>

  <!-- 导入结果对话框 -->
  <el-dialog
    v-model="resultVisible"
    title="导入结果"
    width="600px"
    :close-on-click-modal="false"
  >
    <div v-if="importResult">
      <el-alert
        :title="importResult.success ? '导入成功' : '导入失败'"
        :type="importResult.success ? 'success' : 'error'"
        :description="importResult.message"
        show-icon
      />
      
      <div v-if="importResult.errors && importResult.errors.length > 0" class="mt-4">
        <h4>错误详情：</h4>
        <el-table :data="importResult.errors" style="width: 100%">
          <el-table-column prop="row" label="行号" width="80" />
          <el-table-column prop="field" label="字段" width="120" />
          <el-table-column prop="message" label="错误信息" />
        </el-table>
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { UploadFilled } from '@element-plus/icons-vue'
import { getSupportedFormats, getImportTemplate, uploadImportFile, getImportResult } from '@/api/import'

const props = defineProps({
  visible: Boolean,
  entityType: String,
  title: {
    type: String,
    default: '数据导入'
  }
})

const emit = defineEmits(['update:visible', 'success'])

const formRef = ref(null)
const form = ref({
  format: '',
  file: null
})
const fileList = ref([])
const formats = ref([])
const importing = ref(false)
const importProgress = ref(0)
const resultVisible = ref(false)
const importResult = ref(null)

// 获取支持的导入格式
const loadFormats = async () => {
  try {
    const res = await getSupportedFormats()
    formats.value = res.data
    if (formats.value.length > 0) {
      form.value.format = formats.value[0]
    }
  } catch (error) {
    ElMessage.error('获取导入格式失败：' + error.message)
  }
}

// 下载模板
const handleDownloadTemplate = async () => {
  try {
    await getImportTemplate(props.entityType, form.value.format)
    ElMessage.success('模板下载成功')
  } catch (error) {
    ElMessage.error('模板下载失败：' + error.message)
  }
}

// 文件选择
const handleFileChange = (file) => {
  form.value.file = file.raw
}

// 开始导入
const handleImport = async () => {
  if (!form.value.file) {
    ElMessage.warning('请选择要导入的文件')
    return
  }

  try {
    importing.value = true
    importProgress.value = 0
    
    const res = await uploadImportFile(props.entityType, form.value.file)
    
    // 轮询导入结果
    const checkResult = async () => {
      const result = await getImportResult(res.data.importId)
      importResult.value = result.data
      
      if (result.data.status === 'completed') {
        importing.value = false
        resultVisible.value = true
        if (result.data.success) {
          emit('success')
          handleClose()
        }
      } else if (result.data.status === 'failed') {
        importing.value = false
        resultVisible.value = true
      } else {
        setTimeout(checkResult, 2000)
      }
    }
    
    checkResult()
  } catch (error) {
    importing.value = false
    ElMessage.error('导入失败：' + error.message)
  }
}

// 关闭对话框
const handleClose = () => {
  form.value = {
    format: '',
    file: null
  }
  fileList.value = []
  importProgress.value = 0
  importResult.value = null
  emit('update:visible', false)
}

onMounted(() => {
  loadFormats()
})
</script>

<style scoped>
.mt-4 {
  margin-top: 1rem;
}
</style> 