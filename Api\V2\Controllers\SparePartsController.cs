// File: Api/V2/Controllers/SparePartsController.cs
// Description: 备品备件API控制器

using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ItAssetsSystem.Application.Common.Dtos;
using ItAssetsSystem.Application.Features.SpareParts.Dtos;
using ItAssetsSystem.Application.Features.SpareParts.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ItAssetsSystem.Services.Interfaces;
using ItAssetsSystem.Models.Entities.Gamification;
using ItAssetsSystem.Core.Abstractions;
using Microsoft.Extensions.Logging;

namespace ItAssetsSystem.Api.V2.Controllers
{
    /// <summary>
    /// 备品备件控制器
    /// </summary>
    [ApiController]
    [Route("api/v2/spare-parts")]
    [Authorize]
    public class SparePartsController : ControllerBase
    {
        private readonly ISparePartService _sparePartService;
        private readonly ISparePartTransactionService _transactionService;
        private readonly IUniversalGamificationService _universalGamificationService;
        private readonly ICurrentUserService _currentUserService;
        private readonly ILogger<SparePartsController> _logger;

        /// <summary>
        /// 构造函数
        /// </summary>
        public SparePartsController(
            ISparePartService sparePartService,
            ISparePartTransactionService transactionService,
            IUniversalGamificationService universalGamificationService,
            ICurrentUserService currentUserService,
            ILogger<SparePartsController> logger)
        {
            _sparePartService = sparePartService;
            _transactionService = transactionService;
            _universalGamificationService = universalGamificationService;
            _currentUserService = currentUserService;
            _logger = logger;
        }
        
        /// <summary>
        /// 获取备品备件列表（分页）
        /// </summary>
        /// <param name="query">查询参数</param>
        /// <returns>分页结果</returns>
        [HttpGet]
        public async Task<IActionResult> GetSpareParts([FromQuery] SparePartQuery query)
        {
            var result = await _sparePartService.GetSparePartsAsync(query);
            return Ok(ApiResponse<object>.CreateSuccess(result));
        }
        
        /// <summary>
        /// 获取单个备品备件详情
        /// </summary>
        /// <param name="id">备件ID</param>
        /// <returns>备件DTO</returns>
        [HttpGet("{id}")]
        public async Task<IActionResult> GetSparePart(long id)
        {
            var result = await _sparePartService.GetSparePartByIdAsync(id);
            if (result == null)
            {
                return Ok(ApiResponse<object>.CreateFail($"未找到ID为{id}的备件"));
            }
            return Ok(ApiResponse<object>.CreateSuccess(result));
        }
        
        /// <summary>
        /// 创建备品备件
        /// </summary>
        /// <param name="request">创建请求</param>
        /// <returns>创建的备件DTO</returns>
        [HttpPost]
        public async Task<IActionResult> CreateSparePart([FromBody] CreateSparePartRequest request)
        {
            var result = await _sparePartService.CreateSparePartAsync(request);
            return Ok(ApiResponse<object>.CreateSuccess(result, "备件创建成功"));
        }
        
        /// <summary>
        /// 更新备品备件
        /// </summary>
        /// <param name="id">备件ID</param>
        /// <param name="request">更新请求</param>
        /// <returns>更新后的备件DTO</returns>
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateSparePart(long id, [FromBody] CreateSparePartRequest request)
        {
            var result = await _sparePartService.UpdateSparePartAsync(id, request);
            if (result == null)
            {
                return Ok(ApiResponse<object>.CreateFail($"未找到ID为{id}的备件"));
            }

            // 触发备件更新奖励
            try
            {
                await _universalGamificationService.TriggerBehaviorRewardAsync(
                    _currentUserService.UserId,
                    BehaviorCodes.SPAREPART_UPDATED,
                    (int)id,
                    context: new {
                        PartName = request.Name,
                        PartCode = request.Code,
                        TypeId = request.TypeId
                    },
                    description: $"更新备件: {request.Name}"
                );
            }
            catch (System.Exception ex)
            {
                _logger.LogWarning(ex, "触发备件更新奖励失败: SparePartId={SparePartId}", id);
            }

            return Ok(ApiResponse<object>.CreateSuccess(result, "备件更新成功"));
        }
        
        /// <summary>
        /// 删除备品备件
        /// </summary>
        /// <param name="id">备件ID</param>
        /// <returns>成功消息</returns>
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteSparePart(long id)
        {
            var result = await _sparePartService.DeleteSparePartAsync(id);
            if (!result)
            {
                return Ok(ApiResponse<object>.CreateFail($"未找到ID为{id}的备件或删除失败"));
            }
            return Ok(ApiResponse<object>.CreateSuccess(null, "备件删除成功"));
        }
        
        /// <summary>
        /// 获取库存预警列表
        /// </summary>
        /// <param name="onlyLowStock">是否只获取低于最小安全库存的备件</param>
        /// <returns>预警备件列表</returns>
        [HttpGet("low-stock")]
        public async Task<IActionResult> GetLowStockSpareParts([FromQuery] bool onlyLowStock = false)
        {
            var result = await _sparePartService.GetLowStockSparePartsAsync(onlyLowStock);
            return Ok(ApiResponse<object>.CreateSuccess(result));
        }

        /// <summary>
        /// 获取备件简要信息列表（供采购选择使用）
        /// </summary>
        /// <param name="keyword">关键字搜索（可选）</param>
        /// <returns>备件简要信息列表</returns>
        [HttpGet("simple")]
        public async Task<IActionResult> GetSparePartsSimple([FromQuery] string keyword = null)
        {
            var query = new SparePartQuery
            {
                Keyword = keyword,
                PageSize = 100, // 返回更多结果供选择
                PageIndex = 1
            };

            var result = await _sparePartService.GetSparePartsAsync(query);

            // 转换为简要信息格式
            var simpleList = result.Items.Select(sp => new
            {
                id = sp.Id,
                code = sp.Code,
                materialNumber = sp.MaterialNumber,
                name = sp.Name,
                specification = sp.Specification,
                unit = sp.Unit,
                typeName = sp.TypeName
            }).ToList();

            return Ok(ApiResponse<object>.CreateSuccess(simpleList));
        }
        
        /// <summary>
        /// 备件入库
        /// </summary>
        /// <param name="request">入库请求</param>
        /// <returns>入库记录DTO</returns>
        [HttpPost("transactions/in")]
        public async Task<IActionResult> SparePartInbound([FromBody] SparePartInboundRequest request)
        {
            var result = await _sparePartService.SparePartInboundAsync(request);

            // 触发备件入库奖励
            try
            {
                await _universalGamificationService.TriggerBehaviorRewardAsync(
                    _currentUserService.UserId,
                    BehaviorCodes.SPAREPART_INBOUND,
                    (int)request.PartId,
                    context: new {
                        Quantity = request.Quantity,
                        LocationId = request.LocationId,
                        ReasonType = request.ReasonType
                    },
                    description: $"备件入库: 数量{request.Quantity}"
                );
            }
            catch (System.Exception ex)
            {
                _logger.LogWarning(ex, "触发备件入库奖励失败: PartId={PartId}", request.PartId);
            }

            return Ok(ApiResponse<object>.CreateSuccess(result, "入库操作成功"));
        }
        
        /// <summary>
        /// 备件出库
        /// </summary>
        /// <param name="request">出库请求</param>
        /// <returns>出库记录DTO</returns>
        [HttpPost("transactions/out")]
        public async Task<IActionResult> SparePartOutbound([FromBody] SparePartOutboundRequest request)
        {
            var result = await _sparePartService.SparePartOutboundAsync(request);

            // 触发备件出库奖励
            try
            {
                await _universalGamificationService.TriggerBehaviorRewardAsync(
                    _currentUserService.UserId,
                    BehaviorCodes.SPAREPART_OUTBOUND,
                    (int)request.PartId,
                    context: new {
                        Quantity = request.Quantity,
                        ReasonType = request.ReasonType,
                        RelatedAssetId = request.RelatedAssetId
                    },
                    description: $"备件出库: 数量{request.Quantity}"
                );
            }
            catch (System.Exception ex)
            {
                _logger.LogWarning(ex, "触发备件出库奖励失败: PartId={PartId}", request.PartId);
            }

            return Ok(ApiResponse<object>.CreateSuccess(result, "出库操作成功"));
        }

        /// <summary>
        /// 库存调整
        /// </summary>
        /// <param name="request">库存调整请求</param>
        /// <returns>调整记录DTO</returns>
        [HttpPost("transactions/adjust")]
        public async Task<IActionResult> StockAdjustment([FromBody] StockAdjustmentRequest request)
        {
            var result = await _sparePartService.StockAdjustmentAsync(request);
            return Ok(ApiResponse<object>.CreateSuccess(result, "库存调整成功"));
        }
        
        /// <summary>
        /// 获取指定备件的出入库记录（分页）
        /// </summary>
        /// <param name="partId">备件ID</param>
        /// <param name="query">分页参数</param>
        /// <returns>分页结果</returns>
        [HttpGet("{partId}/transactions")]
        public async Task<IActionResult> GetSparePartTransactions(long partId, [FromQuery] PaginationQuery query)
        {
            var result = await _transactionService.GetTransactionsByPartIdAsync(partId, query);
            return Ok(ApiResponse<object>.CreateSuccess(result));
        }
        
        /// <summary>
        /// 获取所有出入库记录（分页）
        /// </summary>
        /// <param name="query">查询参数</param>
        /// <returns>分页结果</returns>
        [HttpGet("transactions")]
        public async Task<IActionResult> GetTransactions([FromQuery] SparePartTransactionQuery query)
        {
            var result = await _transactionService.GetTransactionsAsync(query);
            return Ok(ApiResponse<object>.CreateSuccess(result));
        }
    }
} 