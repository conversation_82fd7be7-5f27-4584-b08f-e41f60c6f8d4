// File: Application/Features/SpareParts/EventHandlers/SparePartStockUpdatedEventHandler.cs
// Description: 备品备件库存更新事件处理器

using System;
using System.Threading.Tasks;
using ItAssetsSystem.Domain.Events;
using Microsoft.Extensions.Logging;

namespace ItAssetsSystem.Application.Features.SpareParts.EventHandlers
{
    /// <summary>
    /// 备品备件库存更新事件处理器
    /// </summary>
    public class SparePartStockUpdatedEventHandler
    {
        private readonly ILogger<SparePartStockUpdatedEventHandler> _logger;

        /// <summary>
        /// 构造函数
        /// </summary>
        public SparePartStockUpdatedEventHandler(
            ILogger<SparePartStockUpdatedEventHandler> logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// 处理库存更新事件
        /// </summary>
        /// <param name="event">库存更新事件</param>
        public async Task HandleAsync(SparePartStockUpdatedEvent @event)
        {
            try
            {
                _logger.LogInformation("处理备品备件库存更新事件: PartId={PartId}, ChangeQuantity={ChangeQuantity}, NewStock={NewStock}", 
                    @event.PartId, @event.ChangeQuantity, @event.NewStockQuantity);

                // 检查是否需要发送库存预警通知
                await CheckAndSendLowStockWarning(@event);

                // 记录库存变更日志
                LogStockChange(@event);

                _logger.LogInformation("备品备件库存更新事件处理完成: PartId={PartId}", @event.PartId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理备品备件库存更新事件时发生错误: PartId={PartId}", @event.PartId);
                throw;
            }
        }

        /// <summary>
        /// 检查并发送低库存预警
        /// </summary>
        /// <param name="event">库存更新事件</param>
        private async Task CheckAndSendLowStockWarning(SparePartStockUpdatedEvent @event)
        {
            // TODO: 实现低库存预警逻辑
            // 1. 获取备件的最小安全库存设置
            // 2. 如果当前库存低于安全库存，发送预警通知
            // 3. 通知相关管理人员

            if (@event.NewStockQuantity <= 5) // 简单的低库存阈值检查
            {
                _logger.LogWarning("备品备件库存不足预警: PartId={PartId}, CurrentStock={CurrentStock}", 
                    @event.PartId, @event.NewStockQuantity);

                // TODO: 这里可以发送通知给相关人员
                // 可以集成邮件、短信或系统内通知
            }
        }

        /// <summary>
        /// 记录库存变更日志
        /// </summary>
        /// <param name="event">库存更新事件</param>
        private void LogStockChange(SparePartStockUpdatedEvent @event)
        {
            var changeType = @event.ChangeQuantity > 0 ? "增加" : "减少";
            _logger.LogInformation("备品备件库存{ChangeType}: PartId={PartId}, 变更数量={ChangeQuantity}, 变更后库存={NewStock}, 原因={Reason}, 操作人={OperatorUserId}", 
                changeType, @event.PartId, Math.Abs(@event.ChangeQuantity), @event.NewStockQuantity, @event.Reason, @event.OperatorUserId);
        }
    }
}
