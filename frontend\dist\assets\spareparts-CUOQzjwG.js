import{bv as t,E as a}from"./index-CG5lHOPO.js";import{sparePartOutbound as s,sparePartInbound as r,getSparePartTransactions as e,deleteSparePart as n,updateSparePart as i,createSparePart as c,getSparePart as o,getSpareParts as l,deleteSparePartLocation as h,updateSparePartLocation as u,createSparePartLocation as g,getSparePartAreaStats as y,getSparePartLocations as d,deleteSparePartType as p,updateSparePartType as f,createSparePartType as m,getSparePartTypesTree as L,getSparePartTypes as w}from"./spareparts-DKUrs8IX.js";const T=t("spareparts",{state:()=>({loading:!1,formLoading:!1,types:[],typesTree:[],typesLoading:!1,locations:[],locationsLoading:!1,spareParts:[],sparePartsTotal:0,sparePartsLoading:!1,currentSparePart:null,transactions:[],transactionsTotal:0,transactionsLoading:!1,areas:[]}),getters:{typeOptions:t=>t.types.map((t=>({label:t.name,value:t.id}))),locationOptions:t=>t.locations.map((t=>({label:`${t.name} (${t.area||"未知区域"})`,value:t.id}))),locationOptionsByArea:t=>a=>t.locations.filter((t=>t.area===a)).map((t=>({label:t.name,value:t.id}))),areaOptions:t=>t.areas.map((t=>({label:t,value:t}))),lowStockCount:t=>t.spareParts.filter((t=>t.quantity<=t.min_threshold)).length,warningStockCount:t=>t.spareParts.filter((t=>t.quantity>t.min_threshold&&t.quantity<=t.warning_threshold)).length},actions:{async initializeStore(){try{await Promise.all([this.fetchTypes(),this.fetchLocations(),this.fetchAreas()])}catch(t){a.error("初始化备品备件模块失败，请刷新重试")}},async fetchTypes(){try{this.typesLoading=!0;const t=await w();t.success?this.types=t.data:a.warning(t.message||"获取备件类型失败")}catch(t){a.error("获取备件类型失败，请稍后重试")}finally{this.typesLoading=!1}},async fetchTypesTree(){try{this.typesLoading=!0;const t=await L();t.success?this.typesTree=t.data:a.warning(t.message||"获取备件类型树失败")}catch(t){a.error("获取备件类型树失败，请稍后重试")}finally{this.typesLoading=!1}},async createType(t){try{this.formLoading=!0;const s=await m(t);return s.success?(a.success("创建备件类型成功"),await this.fetchTypes(),this.typesTree.length>0&&await this.fetchTypesTree(),s.data):(a.warning(s.message||"创建备件类型失败"),null)}catch(s){return a.error("创建备件类型失败，请稍后重试"),null}finally{this.formLoading=!1}},async updateType(t,s){try{this.formLoading=!0;const r=await f(t,s);return r.success?(a.success("更新备件类型成功"),await this.fetchTypes(),this.typesTree.length>0&&await this.fetchTypesTree(),r.data):(a.warning(r.message||"更新备件类型失败"),null)}catch(r){return a.error("更新备件类型失败，请稍后重试"),null}finally{this.formLoading=!1}},async deleteType(t){try{this.formLoading=!0;const s=await p(t);return s.success?(a.success("删除备件类型成功"),await this.fetchTypes(),this.typesTree.length>0&&await this.fetchTypesTree(),!0):(a.warning(s.message||"删除备件类型失败"),!1)}catch(s){return a.error("删除备件类型失败，请稍后重试"),!1}finally{this.formLoading=!1}},async fetchLocations(){try{this.locationsLoading=!0;const t=await d();return t.success?(this.locations=t.data,this.locations):(a.warning(t.message||"获取库位失败"),[])}catch(t){return a.error("获取库位失败，请稍后重试"),[]}finally{this.locationsLoading=!1}},async fetchAreas(){try{const t=await y();return t.success?(this.areas=t.data||[],this.areas):(a.warning(t.message||"获取区域失败"),[])}catch(t){return a.error("获取区域失败，请稍后重试"),[]}},async createLocation(t){try{this.formLoading=!0;const s=await g(t);return s.success?(a.success("创建库位成功"),await this.fetchLocations(),s.data):(a.warning(s.message||"创建库位失败"),null)}catch(s){return a.error("创建库位失败，请稍后重试"),null}finally{this.formLoading=!1}},async updateLocation(t,s){try{this.formLoading=!0;const r=await u(t,s);return r.success?(a.success("更新库位成功"),await this.fetchLocations(),r.data):(a.warning(r.message||"更新库位失败"),null)}catch(r){return a.error("更新库位失败，请稍后重试"),null}finally{this.formLoading=!1}},async deleteLocation(t){try{this.formLoading=!0;const s=await h(t);return s.success?(a.success("删除库位成功"),await this.fetchLocations(),!0):(a.warning(s.message||"删除库位失败"),!1)}catch(s){return a.error("删除库位失败，请稍后重试"),!1}finally{this.formLoading=!1}},async fetchSpareParts(t={}){try{this.sparePartsLoading=!0;const s=await l(t);return s.success?(this.spareParts=s.data.items||s.data,this.sparePartsTotal=s.data.total||s.data.length,{items:this.spareParts,total:this.sparePartsTotal}):(a.warning(s.message||"获取备件列表失败"),{items:[],total:0})}catch(s){return a.error("获取备件列表失败，请稍后重试"),{items:[],total:0}}finally{this.sparePartsLoading=!1}},async fetchSparePart(t){try{this.loading=!0;const s=await o(t);return s.success?(this.currentSparePart=s.data,s.data):(a.warning(s.message||"获取备件详情失败"),null)}catch(s){return a.error("获取备件详情失败，请稍后重试"),null}finally{this.loading=!1}},async createSparePart(t){try{this.formLoading=!0;const s=await c(t);return s.success?(a.success("创建备件成功"),s.data):(a.warning(s.message||"创建备件失败"),null)}catch(s){return a.error("创建备件失败，请稍后重试"),null}finally{this.formLoading=!1}},async updateSparePart(t,s){try{this.formLoading=!0;const r=await i(t,s);return r.success?(a.success("更新备件成功"),this.currentSparePart&&this.currentSparePart.id===t&&(this.currentSparePart={...this.currentSparePart,...s}),r.data):(a.warning(r.message||"更新备件失败"),null)}catch(r){return a.error("更新备件失败，请稍后重试"),null}finally{this.formLoading=!1}},async deleteSparePart(t){try{this.formLoading=!0;const s=await n(t);return s.success?(a.success("删除备件成功"),this.currentSparePart&&this.currentSparePart.id===t&&(this.currentSparePart=null),!0):(a.warning(s.message||"删除备件失败"),!1)}catch(s){return a.error("删除备件失败，请稍后重试"),!1}finally{this.formLoading=!1}},async fetchTransactions(t={}){try{this.transactionsLoading=!0;const s=await e(t);return s.success?(this.transactions=s.data.items||s.data,this.transactionsTotal=s.data.total||s.data.length,{items:this.transactions,total:this.transactionsTotal}):(a.warning(s.message||"获取出入库记录失败"),{items:[],total:0})}catch(s){return a.error("获取出入库记录失败，请稍后重试"),{items:[],total:0}}finally{this.transactionsLoading=!1}},async createInbound(t){try{this.formLoading=!0;const s=await r(t);return s.success?(a.success("入库操作成功"),s.data):(a.warning(s.message||"入库操作失败"),null)}catch(s){return a.error("入库操作失败，请稍后重试"),null}finally{this.formLoading=!1}},async createOutbound(t){try{this.formLoading=!0;const r=await s(t);return r.success?(a.success("出库操作成功"),r.data):(a.warning(r.message||"出库操作失败"),null)}catch(r){return a.error("出库操作失败，请稍后重试"),null}finally{this.formLoading=!1}},resetStore(){this.loading=!1,this.formLoading=!1,this.types=[],this.typesTree=[],this.typesLoading=!1,this.locations=[],this.locationsLoading=!1,this.spareParts=[],this.sparePartsTotal=0,this.sparePartsLoading=!1,this.currentSparePart=null,this.transactions=[],this.transactionsTotal=0,this.transactionsLoading=!1,this.areas=[]}}});export{T as u};
