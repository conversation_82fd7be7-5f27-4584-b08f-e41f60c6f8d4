// IT资产管理系统 - 资产实体
// 文件路径: /Models/Entities/Asset.cs
// 功能: 定义资产实体，对应assets表

using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using ItAssetsSystem.Models;

namespace ItAssetsSystem.Models.Entities
{
    /// <summary>
    /// 资产实体
    /// </summary>
    public class Asset : IAuditableEntity
    {
        /// <summary>
        /// 资产ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 资产编码
        /// </summary>
        public string AssetCode { get; set; }

        /// <summary>
        /// 财务编号
        /// </summary>
        public string FinancialCode { get; set; }

        /// <summary>
        /// 资产名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 资产类型ID
        /// </summary>
        public int AssetTypeId { get; set; }
        
        /// <summary>
        /// 资产类型名称
        /// </summary>
        [NotMapped]
        public string AssetTypeName { get; set; }

        /// <summary>
        /// 资产类型
        /// </summary>
        [ForeignKey("AssetTypeId")]
        public virtual AssetType AssetType { get; set; }

        /// <summary>
        /// 序列号
        /// </summary>
        public string SerialNumber { get; set; }

        /// <summary>
        /// 型号
        /// </summary>
        public string Model { get; set; }

        /// <summary>
        /// 品牌
        /// </summary>
        public string Brand { get; set; }

        /// <summary>
        /// 购买日期
        /// </summary>
        public DateTime? PurchaseDate { get; set; }

        /// <summary>
        /// 保修过期日期
        /// </summary>
        public DateTime? WarrantyExpireDate { get; set; }

        /// <summary>
        /// 价格
        /// </summary>
        public decimal? Price { get; set; }

        /// <summary>
        /// 位置ID
        /// </summary>
        public int? LocationId { get; set; }

        /// <summary>
        /// 位置名称
        /// </summary>
        [NotMapped]
        public string LocationName { get; set; }

        /// <summary>
        /// 位置
        /// </summary>
        [ForeignKey("LocationId")]
        public virtual Location Location { get; set; }

        /// <summary>
        /// 部门ID
        /// </summary>
        public int? DepartmentId { get; set; }

        /// <summary>
        /// 部门名称
        /// </summary>
        [NotMapped]
        public string DepartmentName { get; set; }

        /// <summary>
        /// 部门
        /// </summary>
        [ForeignKey("DepartmentId")]
        public virtual Department Department { get; set; }

        /// <summary>
        /// 状态（0:闲置, 1:在用, 2:维修中, 3:报废）
        /// </summary>
        public int Status { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Notes { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; }

        /// <summary>
        /// 位置历史
        /// </summary>
        public virtual ICollection<LocationHistory> LocationHistories { get; set; }

        /// <summary>
        /// 资产历史
        /// </summary>
        public virtual ICollection<AssetHistory> AssetHistories { get; set; }

        /// <summary>
        /// 故障记录
        /// </summary>
        public virtual ICollection<FaultRecord> FaultRecords { get; set; }

        /// <summary>
        /// 工序ID
        /// </summary>
        [NotMapped]
        public int? ProcessId { get; set; }

        /// <summary>
        /// 产线ID
        /// </summary>
        [NotMapped]
        public int? ProductionLineId { get; set; }

        /// <summary>
        /// 工位ID
        /// </summary>
        [NotMapped]
        public int? WorkstationId { get; set; }
        
        /// <summary>
        /// 供应商（导入用，不存储到数据库）
        /// </summary>
        [NotMapped]
        public string Supplier { get; set; }
        
        /// <summary>
        /// 制造商（导入用，不存储到数据库）
        /// </summary>
        [NotMapped]
        public string Manufacturer { get; set; }
        
        /// <summary>
        /// 规格（导入用，不存储到数据库）
        /// </summary>
        [NotMapped]
        public string Specification { get; set; }
        
        /// <summary>
        /// 保修期（月）（导入用，不存储到数据库）
        /// </summary>
        [NotMapped]
        public int? WarrantyPeriod { get; set; }
        
        /// <summary>
        /// 导入行索引（导入用，不存储到数据库）
        /// </summary>
        [NotMapped]
        public int ImportRowIndex { get; set; }
    }
} 