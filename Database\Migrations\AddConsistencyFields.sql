-- ========================================
-- 数据一致性字段迁移脚本
-- 为最终一致性 + Saga模式添加必要字段
-- ========================================

USE itassets;

-- 1. 为 user_behavior_events 表添加一致性管理字段
ALTER TABLE user_behavior_events
ADD COLUMN id CHAR(36) NOT NULL FIRST,
ADD COLUMN processing_status VARCHAR(20) NOT NULL DEFAULT 'Pending' COMMENT '处理状态：Pending, Processing, Completed, Failed, Compensating',
ADD COLUMN failure_reason VARCHAR(1000) NULL COMMENT '处理失败原因',
ADD COLUMN retry_count INT NOT NULL DEFAULT 0 COMMENT '重试次数',
ADD COLUMN last_processed_at DATETIME NULL COMMENT '最后处理时间';

-- 2. 为现有记录生成UUID
UPDATE user_behavior_events SET id = UUID() WHERE id = '' OR id IS NULL;

-- 3. 添加主键（如果不存在）
ALTER TABLE user_behavior_events
ADD PRIMARY KEY (id);

-- 4. 为 user_work_summary 表添加校验字段
ALTER TABLE user_work_summary 
ADD COLUMN last_reconciled_at DATETIME NULL COMMENT '最后校验时间',
ADD COLUMN version INT NOT NULL DEFAULT 1 COMMENT '数据版本号（用于乐观锁）';

-- 5. 创建索引优化查询性能
CREATE INDEX idx_user_behavior_events_processing_status ON user_behavior_events (processing_status, created_at);
CREATE INDEX idx_user_behavior_events_user_timestamp ON user_behavior_events (user_id, timestamp, action_type);
CREATE INDEX idx_user_behavior_events_retry ON user_behavior_events (retry_count, processing_status, created_at);

CREATE INDEX idx_user_work_summary_reconciled ON user_work_summary (last_reconciled_at);
CREATE INDEX idx_user_work_summary_version ON user_work_summary (version, updated_at);

-- 6. 更新现有数据的处理状态
UPDATE user_behavior_events 
SET processing_status = 'Completed', 
    last_processed_at = created_at 
WHERE processing_status = 'Pending';

-- 6. 创建数据一致性检查视图
CREATE OR REPLACE VIEW v_data_consistency_check AS
SELECT 
    u.id as user_id,
    u.name as user_name,
    uws.period_date,
    uws.period_type,
    -- 汇总表数据
    uws.tasks_claimed as summary_tasks_claimed,
    uws.tasks_completed as summary_tasks_completed,
    uws.total_points_earned as summary_total_points,
    uws.total_coins_earned as summary_total_coins,
    -- 事件表实际数据
    COALESCE(actual.tasks_claimed, 0) as actual_tasks_claimed,
    COALESCE(actual.tasks_completed, 0) as actual_tasks_completed,
    COALESCE(actual.total_points, 0) as actual_total_points,
    COALESCE(actual.total_coins, 0) as actual_total_coins,
    -- 一致性检查
    CASE 
        WHEN uws.tasks_claimed = COALESCE(actual.tasks_claimed, 0) AND
             uws.tasks_completed = COALESCE(actual.tasks_completed, 0) AND
             uws.total_points_earned = COALESCE(actual.total_points, 0) AND
             uws.total_coins_earned = COALESCE(actual.total_coins, 0)
        THEN 'CONSISTENT'
        ELSE 'INCONSISTENT'
    END as consistency_status,
    uws.last_reconciled_at,
    uws.updated_at
FROM users u
LEFT JOIN user_work_summary uws ON u.id = uws.user_id AND uws.period_type = 'weekly'
LEFT JOIN (
    SELECT 
        user_id,
        DATE(DATE_SUB(timestamp, INTERVAL WEEKDAY(timestamp) DAY)) as week_start,
        SUM(CASE WHEN action_type = 'TASK_CLAIMED' THEN 1 ELSE 0 END) as tasks_claimed,
        SUM(CASE WHEN action_type = 'TASK_COMPLETED' THEN 1 ELSE 0 END) as tasks_completed,
        SUM(points_earned) as total_points,
        SUM(coins_earned) as total_coins
    FROM user_behavior_events 
    WHERE processing_status = 'Completed'
    GROUP BY user_id, week_start
) actual ON u.id = actual.user_id AND uws.period_date = actual.week_start
WHERE u.is_active = 1;

-- 7. 创建数据修复存储过程
DELIMITER //

CREATE PROCEDURE sp_repair_user_summary(
    IN p_user_id INT,
    IN p_week_start DATE
)
BEGIN
    DECLARE v_tasks_claimed INT DEFAULT 0;
    DECLARE v_tasks_completed INT DEFAULT 0;
    DECLARE v_tasks_created INT DEFAULT 0;
    DECLARE v_tasks_commented INT DEFAULT 0;
    DECLARE v_assets_created INT DEFAULT 0;
    DECLARE v_assets_updated INT DEFAULT 0;
    DECLARE v_assets_deleted INT DEFAULT 0;
    DECLARE v_faults_reported INT DEFAULT 0;
    DECLARE v_faults_repaired INT DEFAULT 0;
    DECLARE v_total_points INT DEFAULT 0;
    DECLARE v_total_coins INT DEFAULT 0;
    DECLARE v_total_diamonds INT DEFAULT 0;
    DECLARE v_total_xp INT DEFAULT 0;
    
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- 从事件表重新计算统计数据
    SELECT 
        SUM(CASE WHEN action_type = 'TASK_CLAIMED' THEN 1 ELSE 0 END),
        SUM(CASE WHEN action_type = 'TASK_COMPLETED' THEN 1 ELSE 0 END),
        SUM(CASE WHEN action_type = 'TASK_CREATED' THEN 1 ELSE 0 END),
        SUM(CASE WHEN action_type = 'TASK_COMMENTED' THEN 1 ELSE 0 END),
        SUM(CASE WHEN action_type = 'ASSET_CREATED' THEN 1 ELSE 0 END),
        SUM(CASE WHEN action_type = 'ASSET_UPDATED' THEN 1 ELSE 0 END),
        SUM(CASE WHEN action_type = 'ASSET_DELETED' THEN 1 ELSE 0 END),
        SUM(CASE WHEN action_type = 'FAULT_RECORDED' THEN 1 ELSE 0 END),
        SUM(CASE WHEN action_type = 'FAULT_REPAIRED' THEN 1 ELSE 0 END),
        SUM(points_earned),
        SUM(coins_earned),
        SUM(diamonds_earned),
        SUM(xp_earned)
    INTO 
        v_tasks_claimed, v_tasks_completed, v_tasks_created, v_tasks_commented,
        v_assets_created, v_assets_updated, v_assets_deleted,
        v_faults_reported, v_faults_repaired,
        v_total_points, v_total_coins, v_total_diamonds, v_total_xp
    FROM user_behavior_events 
    WHERE user_id = p_user_id 
      AND timestamp >= p_week_start 
      AND timestamp < DATE_ADD(p_week_start, INTERVAL 7 DAY)
      AND processing_status = 'Completed';
    
    -- 更新汇总表
    UPDATE user_work_summary 
    SET 
        tasks_claimed = COALESCE(v_tasks_claimed, 0),
        tasks_completed = COALESCE(v_tasks_completed, 0),
        tasks_created = COALESCE(v_tasks_created, 0),
        tasks_commented = COALESCE(v_tasks_commented, 0),
        assets_created = COALESCE(v_assets_created, 0),
        assets_updated = COALESCE(v_assets_updated, 0),
        assets_deleted = COALESCE(v_assets_deleted, 0),
        faults_reported = COALESCE(v_faults_reported, 0),
        faults_repaired = COALESCE(v_faults_repaired, 0),
        total_points_earned = COALESCE(v_total_points, 0),
        total_coins_earned = COALESCE(v_total_coins, 0),
        total_diamonds_earned = COALESCE(v_total_diamonds, 0),
        total_xp_earned = COALESCE(v_total_xp, 0),
        updated_at = NOW(),
        last_reconciled_at = NOW(),
        version = version + 1
    WHERE user_id = p_user_id 
      AND period_type = 'weekly' 
      AND period_date = p_week_start;
    
    COMMIT;
END //

DELIMITER ;

-- 8. 创建批量数据修复存储过程
DELIMITER //

CREATE PROCEDURE sp_repair_all_user_summaries()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE v_user_id INT;
    DECLARE v_week_start DATE;
    
    DECLARE repair_cursor CURSOR FOR
        SELECT DISTINCT user_id, period_date
        FROM user_work_summary 
        WHERE period_type = 'weekly'
          AND (last_reconciled_at IS NULL OR last_reconciled_at < DATE_SUB(NOW(), INTERVAL 1 DAY));
    
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    OPEN repair_cursor;
    
    repair_loop: LOOP
        FETCH repair_cursor INTO v_user_id, v_week_start;
        IF done THEN
            LEAVE repair_loop;
        END IF;
        
        CALL sp_repair_user_summary(v_user_id, v_week_start);
    END LOOP;
    
    CLOSE repair_cursor;
END //

DELIMITER ;

-- 9. 创建数据一致性检查函数
DELIMITER //

CREATE FUNCTION fn_check_user_consistency(p_user_id INT, p_week_start DATE) 
RETURNS VARCHAR(20)
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE v_is_consistent BOOLEAN DEFAULT TRUE;
    DECLARE v_summary_tasks_claimed INT DEFAULT 0;
    DECLARE v_actual_tasks_claimed INT DEFAULT 0;
    DECLARE v_summary_points INT DEFAULT 0;
    DECLARE v_actual_points INT DEFAULT 0;
    
    -- 获取汇总表数据
    SELECT tasks_claimed, total_points_earned
    INTO v_summary_tasks_claimed, v_summary_points
    FROM user_work_summary 
    WHERE user_id = p_user_id 
      AND period_type = 'weekly' 
      AND period_date = p_week_start;
    
    -- 获取事件表实际数据
    SELECT 
        SUM(CASE WHEN action_type = 'TASK_CLAIMED' THEN 1 ELSE 0 END),
        SUM(points_earned)
    INTO v_actual_tasks_claimed, v_actual_points
    FROM user_behavior_events 
    WHERE user_id = p_user_id 
      AND timestamp >= p_week_start 
      AND timestamp < DATE_ADD(p_week_start, INTERVAL 7 DAY)
      AND processing_status = 'Completed';
    
    -- 检查一致性
    IF COALESCE(v_summary_tasks_claimed, 0) != COALESCE(v_actual_tasks_claimed, 0) OR
       COALESCE(v_summary_points, 0) != COALESCE(v_actual_points, 0) THEN
        SET v_is_consistent = FALSE;
    END IF;
    
    RETURN CASE WHEN v_is_consistent THEN 'CONSISTENT' ELSE 'INCONSISTENT' END;
END //

DELIMITER ;

-- 10. 插入初始配置数据（如果不存在）
INSERT IGNORE INTO behavior_type_configs (action_type, action_category, base_points, base_coins, base_diamonds, base_xp, is_active, created_at, updated_at)
VALUES 
('TASK_CLAIMED', 'task_management', 10, 5, 0, 5, 1, NOW(), NOW()),
('TASK_COMPLETED', 'task_management', 50, 25, 1, 20, 1, NOW(), NOW()),
('TASK_CREATED', 'task_management', 20, 10, 0, 10, 1, NOW(), NOW()),
('TASK_COMMENTED', 'task_management', 5, 2, 0, 2, 1, NOW(), NOW()),
('ASSET_CREATED', 'asset_management', 30, 15, 0, 15, 1, NOW(), NOW()),
('ASSET_UPDATED', 'asset_management', 15, 7, 0, 7, 1, NOW(), NOW()),
('ASSET_DELETED', 'asset_management', 10, 5, 0, 5, 1, NOW(), NOW()),
('FAULT_RECORDED', 'fault_management', 25, 12, 0, 12, 1, NOW(), NOW()),
('FAULT_REPAIRED', 'fault_management', 40, 20, 1, 18, 1, NOW(), NOW());

-- 11. 创建监控表
CREATE TABLE IF NOT EXISTS data_consistency_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    check_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    user_id INT,
    week_start DATE,
    consistency_status VARCHAR(20),
    repair_attempted BOOLEAN DEFAULT FALSE,
    repair_successful BOOLEAN DEFAULT FALSE,
    error_message TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_consistency_log_time (check_time),
    INDEX idx_consistency_log_user (user_id, week_start),
    INDEX idx_consistency_log_status (consistency_status)
);

COMMIT;

-- 执行完成提示
SELECT 'Data consistency migration completed successfully!' as status;
