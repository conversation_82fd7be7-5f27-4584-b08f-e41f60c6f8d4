cmake_minimum_required(VERSION 3.16)
project(HighPerformanceNotificationClient)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置输出目录
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)

# 平台检查
if(NOT WIN32)
    message(FATAL_ERROR "This project is designed for Windows only")
endif()

# 编译选项
if(MSVC)
    add_compile_options(/W4 /WX- /bigobj)
    add_compile_definitions(_WIN32_WINNT=0x0A00)  # Windows 10
    add_compile_definitions(NOMINMAX)  # 避免Windows.h的min/max宏冲突
else()
    add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# 查找依赖库
find_package(nlohmann_json QUIET)
if(NOT nlohmann_json_FOUND)
    message(STATUS "nlohmann_json not found, using bundled version")
    include_directories(${CMAKE_SOURCE_DIR}/third_party/json/include)
endif()

# WebSocket库配置（可选）
find_package(websocketpp QUIET)
if(NOT websocketpp_FOUND)
    message(STATUS "websocketpp not found, using bundled version")
    include_directories(${CMAKE_SOURCE_DIR}/third_party/websocketpp)
endif()

# 源文件组织
set(CORE_SOURCES
    src/core/notification_client.cpp
    src/core/udp_client.cpp
    src/core/websocket_client.cpp
    src/core/message_handler.cpp
    src/core/config_manager.cpp
)

set(NETWORK_SOURCES
    src/network/udp_protocol.cpp
    src/network/websocket_protocol.cpp
    src/network/auth_manager.cpp
    src/network/connection_manager.cpp
)

set(UI_SOURCES
    src/ui/tray_manager.cpp
    src/ui/notification_window.cpp
    src/ui/settings_dialog.cpp
)

set(UTILS_SOURCES
    src/utils/logger.cpp
    src/utils/performance_monitor.cpp
    src/utils/json_parser.cpp
    src/utils/crypto_utils.cpp
)

set(HEADER_FILES
    include/core/notification_client.h
    include/core/udp_client.h
    include/core/websocket_client.h
    include/core/message_handler.h
    include/core/config_manager.h
    include/network/udp_protocol.h
    include/network/websocket_protocol.h
    include/network/auth_manager.h
    include/network/connection_manager.h
    include/ui/tray_manager.h
    include/ui/notification_window.h
    include/ui/settings_dialog.h
    include/utils/logger.h
    include/utils/performance_monitor.h
    include/utils/json_parser.h
    include/utils/crypto_utils.h
    include/common/types.h
    include/common/constants.h
)

# 主可执行文件
add_executable(HighPerformanceNotificationClient
    src/main.cpp
    ${CORE_SOURCES}
    ${NETWORK_SOURCES}
    ${UI_SOURCES}
    ${UTILS_SOURCES}
    ${HEADER_FILES}
)

# 性能测试工具
add_executable(performance_benchmark
    src/tools/performance_benchmark.cpp
    src/core/udp_client.cpp
    src/core/websocket_client.cpp
    src/network/udp_protocol.cpp
    src/network/websocket_protocol.cpp
    src/utils/logger.cpp
    src/utils/performance_monitor.cpp
)

# 配置工具
add_executable(config_tool
    src/tools/config_tool.cpp
    src/core/config_manager.cpp
    src/utils/logger.cpp
    src/utils/json_parser.cpp
)

# 包含目录
target_include_directories(HighPerformanceNotificationClient PRIVATE
    ${CMAKE_SOURCE_DIR}/include
    ${CMAKE_SOURCE_DIR}/src
)

target_include_directories(performance_benchmark PRIVATE
    ${CMAKE_SOURCE_DIR}/include
    ${CMAKE_SOURCE_DIR}/src
)

target_include_directories(config_tool PRIVATE
    ${CMAKE_SOURCE_DIR}/include
    ${CMAKE_SOURCE_DIR}/src
)

# Windows特定库链接
target_link_libraries(HighPerformanceNotificationClient
    ws2_32
    wininet
    shell32
    user32
    gdi32
    comctl32
    ole32
    advapi32
    crypt32
)

target_link_libraries(performance_benchmark
    ws2_32
    wininet
)

target_link_libraries(config_tool
    shell32
    user32
)

# JSON库链接
if(nlohmann_json_FOUND)
    target_link_libraries(HighPerformanceNotificationClient nlohmann_json::nlohmann_json)
    target_link_libraries(performance_benchmark nlohmann_json::nlohmann_json)
    target_link_libraries(config_tool nlohmann_json::nlohmann_json)
endif()

# 预编译头（可选，加速编译）
if(MSVC)
    target_precompile_headers(HighPerformanceNotificationClient PRIVATE src/pch.h)
endif()

# Release模式优化
if(CMAKE_BUILD_TYPE STREQUAL "Release")
    if(MSVC)
        target_compile_options(HighPerformanceNotificationClient PRIVATE /O2 /Ot /GL)
        target_link_options(HighPerformanceNotificationClient PRIVATE /LTCG)
    else()
        target_compile_options(HighPerformanceNotificationClient PRIVATE -O3 -march=native)
        target_link_options(HighPerformanceNotificationClient PRIVATE -s)
    endif()
endif()

# 安装配置
install(TARGETS HighPerformanceNotificationClient performance_benchmark config_tool
    RUNTIME DESTINATION bin
)

install(FILES config/config.json
    DESTINATION bin
)

install(DIRECTORY docs/
    DESTINATION docs
)

# CPack配置
set(CPACK_PACKAGE_NAME "HighPerformanceNotificationClient")
set(CPACK_PACKAGE_VERSION "1.0.0")
set(CPACK_PACKAGE_DESCRIPTION_SUMMARY "高性能实时通知客户端")
set(CPACK_PACKAGE_VENDOR "IT资产管理系统")
set(CPACK_GENERATOR "ZIP;NSIS")

include(CPack)