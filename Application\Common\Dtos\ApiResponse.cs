// File: Application/Common/Dtos/ApiResponse.cs
// Description: API响应模型引用，用于解决跨层引用问题

using System;
using System.Text.Json.Serialization;

namespace ItAssetsSystem.Application.Common.Dtos
{
    /// <summary>
    /// API响应模型
    /// </summary>
    /// <typeparam name="T">响应数据类型</typeparam>
    public class ApiResponse<T>
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        [JsonPropertyName("success")]
        public bool Success { get; set; }

        /// <summary>
        /// 响应数据
        /// </summary>
        public T Data { get; set; }

        /// <summary>
        /// 响应消息
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        [JsonPropertyName("error")]
        public string Error { get; set; }

        /// <summary>
        /// 分页信息
        /// </summary>
        public PaginationInfo Pagination { get; set; }

        /// <summary>
        /// 响应代码 (兼容V2 API)
        /// </summary>
        public int Code { get; set; }

        /// <summary>
        /// 是否可重试 (兼容V2 API)
        /// </summary>
        public bool Retriable { get; set; }

        /// <summary>
        /// 时间戳 (兼容V2 API)
        /// </summary>
        public long Timestamp { get; set; }

        /// <summary>
        /// 是否冲突 (兼容V2 API)
        /// </summary>
        public bool Conflict { get; set; }

        /// <summary>
        /// 创建成功响应
        /// </summary>
        /// <param name="data">响应数据</param>
        /// <param name="message">响应消息</param>
        /// <returns>成功的API响应</returns>
        public static ApiResponse<T> CreateSuccess(T data, string message = "操作成功")
        {
            return new ApiResponse<T>
            {
                Success = true,
                Data = data,
                Message = message,
                Code = 0,
                Retriable = false,
                Timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds(),
                Conflict = false
            };
        }

        /// <summary>
        /// 创建失败响应
        /// </summary>
        /// <param name="message">错误消息</param>
        /// <param name="error">详细错误信息</param>
        /// <returns>失败的API响应</returns>
        public static ApiResponse<T> CreateFail(string message, string error = null)
        {
            return new ApiResponse<T>
            {
                Success = false,
                Message = message,
                Error = error ?? message,
                Data = default,
                Code = -1,
                Retriable = false,
                Timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds(),
                Conflict = false
            };
        }


    }

    /// <summary>
    /// 非泛型API响应模型
    /// </summary>
    public class ApiResponse
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        [JsonPropertyName("success")]
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 响应消息
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 创建成功响应
        /// </summary>
        /// <param name="message">响应消息</param>
        /// <returns>成功的API响应</returns>
        public static ApiResponse Ok(string message = "操作成功")
        {
            return new ApiResponse
            {
                IsSuccess = true,
                Message = message
            };
        }

        /// <summary>
        /// 创建失败响应
        /// </summary>
        /// <param name="message">错误消息</param>
        /// <param name="errorDetails">详细错误信息</param>
        /// <returns>失败的API响应</returns>
        public static ApiResponse Error(string message, string errorDetails = null)
        {
            return new ApiResponse
            {
                IsSuccess = false,
                Message = message,
                ErrorMessage = errorDetails ?? message
            };
        }
    }

    /// <summary>
    /// 分页信息
    /// </summary>
    public class PaginationInfo
    {
        /// <summary>
        /// 当前页码
        /// </summary>
        public int PageIndex { get; set; }

        /// <summary>
        /// 每页大小
        /// </summary>
        public int PageSize { get; set; }

        /// <summary>
        /// 总记录数
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// 总页数
        /// </summary>
        public int TotalPages { get; set; }
    }
} 