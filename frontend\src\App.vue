/**
 * IT资产管理系统 - 根组件
 * 文件路径: src/App.vue
 * 功能描述: 应用程序主容器，提供全局布局结构
 */

<template>
  <el-config-provider :locale="zhCn" :size="size">
    <router-view v-slot="{ Component }">
      <transition name="fade-transform" mode="out-in">
        <component :is="Component" />
      </transition>
    </router-view>
    <MemoFormDrawer />
  </el-config-provider>
</template>

<script setup>
import { ref, provide } from 'vue'
import { ElConfigProvider } from 'element-plus'
import zhCn from 'element-plus/es/locale/lang/zh-cn'

// 新增：导入随手记组件
// import GlobalFAB from '@/components/QuickMemo/GlobalFAB.vue'
import MemoFormDrawer from '@/components/QuickMemo/MemoFormDrawer.vue'

// 全局设置Element Plus组件大小
const size = ref('default')
provide('size', size)
</script>

<style lang="scss" scoped>
/* 移除: 全局样式的 @use 导入 */
// @use "@/styles/variables.scss" as *;
// @use '@/styles/global.scss' as *;

/* 移除: #app 样式，已移至 global.scss */
// #app {
//   font-family: 'Microsoft YaHei', Arial, sans-serif;
//   -webkit-font-smoothing: antialiased;
//   -moz-osx-font-smoothing: grayscale;
//   color: var(--el-text-color-primary);
//   height: 100%;
// }

/* 保留: 页面过渡动画 */
.fade-transform-enter-active,
.fade-transform-leave-active {
  transition: all .3s;
}

.fade-transform-enter-from {
  opacity: 0;
  transform: translateX(-30px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(30px);
}
</style> 