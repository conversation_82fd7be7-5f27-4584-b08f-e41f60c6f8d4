// File: frontend/src/utils/notification-service.js
// Description: 实时通知服务，使用SignalR连接到后端Hub

import { HubConnectionBuilder, LogLevel, HttpTransportType } from '@microsoft/signalr'
import { useNotificationStore } from '@/stores/modules/notification'
import { ElNotification } from 'element-plus'
import router from '@/router'

let connection = null
let isConnected = false
let reconnectAttempts = 0
const maxReconnectAttempts = 5

/**
 * 通知服务，用于处理实时通知
 */
const notificationService = {
  /**
   * 获取连接状态
   */
  get isConnected() {
    return isConnected && connection !== null
  },

  /**
   * 初始化SignalR连接
   * @param {number} userId 当前用户ID
   */
  async initConnection(userId) {
    if (connection) {
      return
    }

    try {
      // 创建连接，通过URL传递用户ID
      // 使用相对路径，通过Vite代理连接到后端
      const hubUrl = `/hubs/notification?userId=${userId}`

      connection = new HubConnectionBuilder()
        .withUrl(hubUrl, {
          skipNegotiation: true,
          transport: HttpTransportType.WebSockets
        })
        .withAutomaticReconnect([0, 2000, 5000, 10000, 15000])
        .configureLogging(LogLevel.Information)
        .build()

      // 处理连接关闭事件
      connection.onclose(async (error) => {
        isConnected = false
        console.log('SignalR连接已关闭', error)
        await this.attemptReconnect()
      })

      // 注册实时通知处理函数
      this.registerHandlers()

      // 启动连接
      await connection.start()
      isConnected = true
      reconnectAttempts = 0
      console.log('SignalR连接已建立')

      // 测试连接
      try {
        const result = await connection.invoke('TestConnection')
        console.log('SignalR测试连接结果:', result)
      } catch (error) {
        console.error('测试SignalR连接失败:', error)
      }

      // 用户加入个人通知组
      if (userId) {
        try {
          await connection.invoke('UserOnline', userId)
          console.log(`用户${userId}加入通知组成功`)
        } catch (error) {
          console.error('用户加入通知组失败:', error)
        }
      }
    } catch (error) {
      console.error('初始化SignalR连接失败:', error)
      isConnected = false
      await this.attemptReconnect()
    }
  },

  /**
   * 尝试重新连接
   */
  async attemptReconnect() {
    if (reconnectAttempts >= maxReconnectAttempts) {
      console.log(`已达到最大重连次数 (${maxReconnectAttempts})，停止重连`)
      return
    }

    reconnectAttempts++
    console.log(`尝试重连 (${reconnectAttempts}/${maxReconnectAttempts})...`)

    try {
      await connection.start()
      isConnected = true
      reconnectAttempts = 0
      console.log('SignalR重连成功')
    } catch (error) {
      console.error('SignalR重连失败:', error)
      isConnected = false
      setTimeout(() => this.attemptReconnect(), 5000)
    }
  },

  /**
   * 注册通知处理函数
   */
  registerHandlers() {
    if (!connection) {
      return
    }

    // 接收通知
    connection.on('ReceiveNotification', (notification) => {
      console.log('🔔 收到实时通知:', notification)
      console.log('🔔 通知详细信息:', {
        id: notification.id,
        type: notification.type,
        title: notification.title,
        message: notification.message,
        taskId: notification.taskId,
        timestamp: notification.timestamp
      })

      try {
        // 将收到的通知添加到store
        const notificationStore = useNotificationStore()

        // 确保字段映射正确
        const mappedNotification = {
          id: notification.id,
          notificationId: notification.id,
          type: notification.type,
          title: notification.title,
          content: notification.message,
          message: notification.message,
          timestamp: notification.timestamp,
          createdAt: notification.timestamp,
          creationTimestamp: notification.timestamp,
          taskId: notification.taskId,
          resourceId: notification.taskId,
          resourceType: notification.taskId ? 'Task' : undefined,
          referenceId: notification.taskId,
          referenceType: notification.taskId ? 'Task' : undefined,
          read: notification.read || false,
          isRead: notification.read || false
        }

        console.log('🔔 映射后的通知:', mappedNotification)
        notificationStore.addNotification(mappedNotification)

        // 显示Element Plus通知
        this.showNotification(notification)
        console.log('🔔 通知处理完成')
      } catch (error) {
        console.error('❌ 处理通知出错:', error)
      }
    })

    // 添加测试事件处理程序
    connection.on('TestEvent', (message) => {
      console.log('收到测试事件:', message)
      ElNotification({
        title: '测试通知',
        message: message,
        type: 'info'
      })
    })
  },

  /**
   * 显示Element Plus弹出通知
   * @param {Object} notification 通知对象
   */
  showNotification(notification) {
    try {
      const title = notification.title || '系统通知'
      const message = notification.message || notification.content || ''
      const type = this.getNotificationType(notification.type)
      
      ElNotification({
        title,
        message,
        type,
        duration: 4500,
        onClick: () => {
          // 点击通知时跳转到相应页面
          this.handleNotificationClick(notification)
        }
      })
    } catch (error) {
      console.error('显示通知弹窗失败:', error)
    }
  },

  /**
   * 根据通知类型获取Element Plus通知类型
   * @param {string} notificationType 通知类型
   * @returns {string} Element Plus通知类型
   */
  getNotificationType(notificationType) {
    switch (notificationType) {
      case 'TaskAssigned':
      case 'TaskMention':
        return 'success'
      case 'TaskOverdue':
        return 'error'
      case 'TaskStatusChanged':
      case 'TaskContentChanged':
        return 'warning'
      case 'TaskComment':
      case 'TaskAttachmentAdded':
        return 'info'
      default:
        return 'info'
    }
  },

  /**
   * 处理通知点击
   * @param {Object} notification 通知对象
   */
  handleNotificationClick(notification) {
    // 标记通知为已读
    try {
      const notificationStore = useNotificationStore()
      notificationStore.markAsRead(notification.id)
      
      // 如果是任务相关通知，跳转到任务详情页
      if (notification.taskId) {
        router.push(`/main/tasks/detail/${notification.taskId}`)
      } else if (notification.referenceId && notification.referenceType === 'Task') {
        router.push(`/main/tasks/detail/${notification.referenceId}`)
      }
    } catch (error) {
      console.error('处理通知点击失败:', error)
    }
  },

  /**
   * 手动发送测试通知
   * @param {number} userId 用户ID
   */
  async sendTestNotification(userId) {
    try {
      if (!connection || !isConnected) {
        console.warn('SignalR连接未建立，无法发送测试通知')
        return
      }

      // 模拟一个通知对象
      const testNotification = {
        id: Date.now(),
        type: 'Test',
        title: '测试通知',
        message: `这是一条测试通知 (${new Date().toLocaleString()})`,
        timestamp: new Date(),
        read: false
      }

      // 直接使用处理机制显示通知
      this.showNotification(testNotification)
    } catch (error) {
      console.error('发送测试通知失败:', error)
    }
  },

  /**
   * 调试SignalR连接状态
   */
  debugConnection() {
    console.log('🔍 SignalR连接调试信息:')
    console.log('- 连接对象:', connection)
    console.log('- 连接状态:', isConnected)
    console.log('- 连接ID:', connection?.connectionId)
    console.log('- 连接状态值:', connection?.state)
    console.log('- 重连次数:', reconnectAttempts)

    if (connection) {
      console.log('- 连接URL:', connection.baseUrl)
      console.log('- 传输类型:', connection.transport?.name)
    }
  },

  /**
   * 测试通知推送功能
   */
  async testNotificationPush() {
    try {
      console.log('🧪 开始测试通知推送功能...')

      if (!connection || !isConnected) {
        console.error('❌ SignalR连接未建立，无法测试')
        return
      }

      // 测试连接
      const testResult = await connection.invoke('TestConnection')
      console.log('✅ 连接测试结果:', testResult)

      // 发送测试通知
      this.sendTestNotification()

      console.log('🧪 通知推送测试完成')
    } catch (error) {
      console.error('❌ 测试通知推送失败:', error)
    }
  },

  /**
   * 测试周期性任务通知
   */
  async testPeriodicTaskNotification() {
    try {
      console.log('🧪 开始测试周期性任务通知...')

      // 模拟周期性任务通知
      const periodicNotification = {
        id: Date.now(),
        type: 'TaskCreated',
        title: '您有新的周期性任务',
        message: '系统为您自动生成了周期性任务："每日报表确认"',
        timestamp: new Date(),
        taskId: 999,
        read: false
      }

      // 直接显示通知
      this.showNotification(periodicNotification)

      // 添加到store
      const notificationStore = useNotificationStore()
      notificationStore.addNotification(periodicNotification)

      console.log('🧪 周期性任务通知测试完成')
    } catch (error) {
      console.error('❌ 测试周期性任务通知失败:', error)
    }
  },

  /**
   * 断开连接
   */
  async disconnect() {
    if (connection && isConnected) {
      try {
        await connection.stop()
        console.log('SignalR连接已断开')
        isConnected = false
        connection = null
      } catch (error) {
        console.error('断开SignalR连接失败:', error)
      }
    }
  }
}

// 暴露调试方法到全局（仅在开发环境）
if (typeof window !== 'undefined' && import.meta.env.DEV) {
  window.debugSignalR = notificationService.debugConnection
  window.testNotificationPush = notificationService.testNotificationPush
  window.testPeriodicTaskNotification = notificationService.testPeriodicTaskNotification
  window.notificationService = notificationService
}

// 同时提供默认导出和命名导出
export default notificationService
export { notificationService }