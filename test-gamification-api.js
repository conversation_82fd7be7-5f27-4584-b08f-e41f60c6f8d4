// 游戏化API测试脚本
// 使用方法: node test-gamification-api.js

const https = require('https');
const http = require('http');

const API_BASE = 'http://localhost:5001/api';
const AUTH_TOKEN = 'Bearer your-token-here'; // 替换为实际的token

// 通用API请求函数
function apiRequest(path, method = 'GET', data = null) {
    return new Promise((resolve, reject) => {
        const url = new URL(API_BASE + path);
        
        const options = {
            hostname: url.hostname,
            port: url.port,
            path: url.pathname + url.search,
            method: method,
            headers: {
                'Content-Type': 'application/json',
                'Authorization': AUTH_TOKEN
            }
        };

        if (data && method === 'GET') {
            const params = new URLSearchParams(data);
            options.path += '?' + params.toString();
        }

        const req = http.request(options, (res) => {
            let responseData = '';
            
            res.on('data', (chunk) => {
                responseData += chunk;
            });
            
            res.on('end', () => {
                try {
                    const result = JSON.parse(responseData);
                    resolve({
                        success: res.statusCode >= 200 && res.statusCode < 300,
                        status: res.statusCode,
                        data: result
                    });
                } catch (error) {
                    resolve({
                        success: false,
                        status: res.statusCode,
                        error: 'Invalid JSON response',
                        rawData: responseData
                    });
                }
            });
        });

        req.on('error', (error) => {
            reject({
                success: false,
                error: error.message
            });
        });

        if (data && method !== 'GET') {
            req.write(JSON.stringify(data));
        }

        req.end();
    });
}

// 测试函数
async function testGamificationAPIs() {
    console.log('🎮 开始测试游戏化API...\n');

    // 1. 测试获取用户统计
    console.log('1. 测试获取用户统计');
    try {
        const result = await apiRequest('/v2/gamification-v2/stats/current');
        console.log('✅ 成功:', JSON.stringify(result.data, null, 2));
    } catch (error) {
        console.log('❌ 失败:', error);
    }
    console.log('');

    // 2. 测试任务完成奖励
    console.log('2. 测试任务完成奖励');
    try {
        const result = await apiRequest('/v2/gamification-v2/test/task-completed', 'POST', {
            taskId: 1,
            taskName: '测试任务',
            taskType: 'General',
            isOnTime: true,
            points: 20
        });
        console.log('✅ 成功:', JSON.stringify(result.data, null, 2));
    } catch (error) {
        console.log('❌ 失败:', error);
    }
    console.log('');

    // 3. 测试任务创建奖励
    console.log('3. 测试任务创建奖励');
    try {
        const result = await apiRequest('/v2/gamification-v2/test/task-created', 'POST', {
            taskId: 2,
            taskName: '测试创建任务',
            taskType: 'General',
            points: 10
        });
        console.log('✅ 成功:', JSON.stringify(result.data, null, 2));
    } catch (error) {
        console.log('❌ 失败:', error);
    }
    console.log('');

    // 4. 测试触发任务完成奖励
    console.log('4. 测试触发任务完成奖励');
    try {
        const result = await apiRequest('/v2/gamification-v2/trigger-complete-reward', 'POST', {
            taskId: 1,
            isOnTime: true
        });
        console.log('✅ 成功:', JSON.stringify(result.data, null, 2));
    } catch (error) {
        console.log('❌ 失败:', error);
    }
    console.log('');

    // 5. 测试触发任务领取奖励
    console.log('5. 测试触发任务领取奖励');
    try {
        const result = await apiRequest('/v2/gamification-v2/trigger-claim-reward', 'POST', {
            taskId: 1
        });
        console.log('✅ 成功:', JSON.stringify(result.data, null, 2));
    } catch (error) {
        console.log('❌ 失败:', error);
    }
    console.log('');

    // 6. 测试更新用户统计
    console.log('6. 测试更新用户统计');
    try {
        const result = await apiRequest('/v2/gamification-v2/stats/update', 'POST');
        console.log('✅ 成功:', JSON.stringify(result.data, null, 2));
    } catch (error) {
        console.log('❌ 失败:', error);
    }
    console.log('');

    // 7. 再次获取用户统计（验证更新）
    console.log('7. 再次获取用户统计（验证更新）');
    try {
        const result = await apiRequest('/v2/gamification-v2/stats/current');
        console.log('✅ 成功:', JSON.stringify(result.data, null, 2));
    } catch (error) {
        console.log('❌ 失败:', error);
    }

    console.log('\n🎮 游戏化API测试完成！');
}

// 运行测试
testGamificationAPIs().catch(console.error);
