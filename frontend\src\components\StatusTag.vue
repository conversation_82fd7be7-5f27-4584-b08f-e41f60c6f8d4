/**
 * 航空航天级IT资产管理系统 - 状态标签组件
 * 文件路径: src/components/StatusTag.vue
 * 功能描述: 用于显示不同状态的标签，支持多种预设状态
 */

<template>
  <el-tag
    :type="typeMap[status]"
    :effect="effect"
    :size="size"
    :class="['status-tag', `status-${status}`]"
  >
    {{ labelText }}
  </el-tag>
</template>

<script setup>
import { computed } from 'vue'

// 组件属性定义
const props = defineProps({
  // 状态值
  status: {
    type: String,
    required: true
  },
  // 自定义标签文本，不传则使用默认文本
  label: {
    type: String,
    default: ''
  },
  // 标签尺寸
  size: {
    type: String,
    default: 'default'
  },
  // 标签样式效果
  effect: {
    type: String,
    default: 'light'
  }
})

// 状态与标签类型映射关系
const typeMap = {
  success: 'success',
  processing: 'primary',
  warning: 'warning',
  error: 'danger',
  default: 'info',
  // 资产状态
  inUse: 'success',
  idle: 'info',
  maintenance: 'warning',
  scrapped: 'danger',
  reserved: 'primary',
  // 故障状态
  pending: 'info',
  processing: 'primary',
  resolved: 'success',
  closed: 'info',
  // 任务状态
  created: 'info',
  assigned: 'primary',
  inProgress: 'warning',
  completed: 'success',
  cancelled: 'danger'
}

// 状态与标签文本映射关系
const labelMap = {
  success: '成功',
  processing: '处理中',
  warning: '警告',
  error: '错误',
  default: '默认',
  // 资产状态
  inUse: '使用中',
  idle: '闲置中',
  maintenance: '维修中',
  scrapped: '已报废',
  reserved: '已预留',
  // 故障状态
  pending: '待处理',
  processing: '处理中',
  resolved: '已解决',
  closed: '已关闭',
  // 任务状态
  created: '已创建',
  assigned: '已分配',
  inProgress: '进行中',
  completed: '已完成',
  cancelled: '已取消'
}

// 计算显示的标签文本
const labelText = computed(() => {
  return props.label || labelMap[props.status] || props.status
})
</script>

<style lang="scss" scoped>
.status-tag {
  text-align: center;
  border-radius: 4px;
  
  &.status-inUse {
    // 自定义使用中状态样式
  }
  
  &.status-idle {
    // 自定义闲置状态样式
  }
  
  &.status-maintenance {
    // 自定义维修中状态样式
  }
}
</style> 