using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace ItAssetsSystem.Migrations
{
    public partial class AddPeriodicTaskDefaultFields : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "default_assignee_user_id",
                table: "periodictaskschedules",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "default_priority",
                table: "periodictaskschedules",
                type: "longtext",
                nullable: true)
                .Annotation("MySql:CharSet", "utf8mb4");

            migrationBuilder.AddColumn<int>(
                name: "default_duration_days",
                table: "periodictaskschedules",
                type: "int",
                nullable: true,
                defaultValue: 1);

            migrationBuilder.AddColumn<int>(
                name: "default_asset_id",
                table: "periodictaskschedules",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "default_location_id",
                table: "periodictaskschedules",
                type: "int",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_periodictaskschedules_default_assignee_user_id",
                table: "periodictaskschedules",
                column: "default_assignee_user_id");

            migrationBuilder.CreateIndex(
                name: "IX_periodictaskschedules_default_asset_id",
                table: "periodictaskschedules",
                column: "default_asset_id");

            migrationBuilder.CreateIndex(
                name: "IX_periodictaskschedules_default_location_id",
                table: "periodictaskschedules",
                column: "default_location_id");

            migrationBuilder.AddForeignKey(
                name: "FK_periodictaskschedules_users_default_assignee_user_id",
                table: "periodictaskschedules",
                column: "default_assignee_user_id",
                principalTable: "users",
                principalColumn: "id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_periodictaskschedules_assets_default_asset_id",
                table: "periodictaskschedules",
                column: "default_asset_id",
                principalTable: "assets",
                principalColumn: "id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_periodictaskschedules_locations_default_location_id",
                table: "periodictaskschedules",
                column: "default_location_id",
                principalTable: "locations",
                principalColumn: "id",
                onDelete: ReferentialAction.SetNull);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_periodictaskschedules_users_default_assignee_user_id",
                table: "periodictaskschedules");

            migrationBuilder.DropForeignKey(
                name: "FK_periodictaskschedules_assets_default_asset_id",
                table: "periodictaskschedules");

            migrationBuilder.DropForeignKey(
                name: "FK_periodictaskschedules_locations_default_location_id",
                table: "periodictaskschedules");

            migrationBuilder.DropIndex(
                name: "IX_periodictaskschedules_default_assignee_user_id",
                table: "periodictaskschedules");

            migrationBuilder.DropIndex(
                name: "IX_periodictaskschedules_default_asset_id",
                table: "periodictaskschedules");

            migrationBuilder.DropIndex(
                name: "IX_periodictaskschedules_default_location_id",
                table: "periodictaskschedules");

            migrationBuilder.DropColumn(
                name: "default_assignee_user_id",
                table: "periodictaskschedules");

            migrationBuilder.DropColumn(
                name: "default_priority",
                table: "periodictaskschedules");

            migrationBuilder.DropColumn(
                name: "default_duration_days",
                table: "periodictaskschedules");

            migrationBuilder.DropColumn(
                name: "default_asset_id",
                table: "periodictaskschedules");

            migrationBuilder.DropColumn(
                name: "default_location_id",
                table: "periodictaskschedules");
        }
    }
}