#nullable enable
// File: Models/Dtos/MentionUserDto.cs
// Description: 用于 @提及用户列表返回的精简 DTO

namespace ItAssetsSystem.Models.Dtos
{
    public class MentionUserDto
    {
        public int Id { get; set; } // Core User ID is INT
        public string Name { get; set; } // 或者 Username，取决于前端显示哪个
        public string? Avatar { get; set; }
        
        // 添加构造函数以确保 Name 属性被初始化
        public MentionUserDto(string name)
        {
            Name = name;
        }
        
        // 添加一个无参构造函数用于序列化/反序列化
        public MentionUserDto()
        {
            Name = string.Empty;
        }
    }
} 