# 多维度排行榜功能实现文档

## 概述

本文档描述了IT资产管理系统中多维度排行榜功能的实现，该功能允许用户在不同的排行榜类型之间切换，包括积分榜、金币榜、钻石榜、任务榜等。

## 实现的功能

### 1. 前端多维度排行榜界面

#### 主要特性
- **Tab式切换界面**: 支持在不同排行榜类型之间快速切换
- **多种排行榜类型**: 积分榜、金币榜、钻石榜、任务完成榜、任务创建榜、故障登记榜、维修单榜、资产更新榜
- **时间周期过滤**: 支持本周、本月、本年、总榜四种时间维度
- **响应式设计**: 适配移动端和桌面端显示

#### 排行榜类型说明
1. **积分排行榜** (🏆): 根据用户总积分排名
2. **金币排行榜** (💰): 根据用户金币数量排名
3. **钻石排行榜** (💎): 根据用户钻石数量排名
4. **任务完成排行榜** (✅): 根据完成任务数量排名
5. **任务创建排行榜** (📝): 根据创建任务数量排名
6. **故障登记排行榜** (🔧): 根据登记故障数量排名
7. **维修单排行榜** (🛠️): 根据创建维修单数量排名
8. **资产更新排行榜** (📦): 根据更新资产数量排名

### 2. API接口增强

#### 新增API函数
```javascript
// 获取多维度排行榜
getMultiDimensionLeaderboard(metric, period, limit)

// 获取排行榜指标列表
getLeaderboardMetrics()

// 获取统计周期列表
getLeaderboardPeriods()

// 获取多维度排行榜概览
getLeaderboardOverview(period, limit)
```

#### 支持的指标类型
- `points`: 积分
- `coins`: 金币
- `diamonds`: 钻石
- `tasks_created`: 创建任务数
- `tasks_completed`: 完成任务数
- `tasks_claimed`: 领取任务数
- `faults_recorded`: 故障登记数
- `maintenance_created`: 维修单数
- `assets_updated`: 资产更新数

#### 支持的时间周期
- `daily`: 日榜
- `weekly`: 周榜
- `monthly`: 月榜
- `quarterly`: 季榜
- `yearly`: 年榜
- `alltime`: 总榜

### 3. 状态管理增强

#### Pinia Store更新
- 新增 `fetchMultiDimensionLeaderboard` 方法
- 支持多维度数据获取和处理
- 保持向后兼容性

## 技术实现细节

### 1. 前端组件结构

#### LeaderboardView.vue 主要改进
```vue
<template>
  <!-- 排行榜类型选择器 -->
  <el-select v-model="currentMetric" @change="handleMetricChange">
    <el-option v-for="metric in availableMetrics" 
               :key="metric.code" 
               :label="metric.name" 
               :value="metric.code">
    </el-option>
  </el-select>
  
  <!-- 时间周期选择器 -->
  <el-radio-group v-model="currentPeriod" @change="fetchCurrentLeaderboard">
    <el-radio-button label="weekly">本周</el-radio-button>
    <el-radio-button label="monthly">本月</el-radio-button>
    <el-radio-button label="yearly">本年</el-radio-button>
    <el-radio-button label="alltime">总榜</el-radio-button>
  </el-radio-group>
</template>
```

#### 动态数据显示
- 根据选择的排行榜类型动态显示相应的分数和图标
- 智能显示额外列（如金币、钻石、任务数等）
- 响应式表格布局

### 2. 数据处理逻辑

#### 分数显示函数
```javascript
const getScoreDisplay = (user) => {
  const icons = {
    'points': '🏆',
    'coins': '💰',
    'diamonds': '💎',
    // ... 其他图标
  }
  
  const values = {
    'points': user.points || 0,
    'coins': user.coins || 0,
    'diamonds': user.diamonds || 0,
    // ... 其他值
  }
  
  return `${icons[currentMetric.value]} ${values[currentMetric.value]}`
}
```

### 3. 样式优化

#### 响应式设计
- 移动端优化的头部控件布局
- 灵活的卡片头部设计
- 美观的分数显示样式

## 使用说明

### 1. 访问排行榜页面
导航到游戏化中心 -> 排行榜页面

### 2. 切换排行榜类型
使用页面顶部的下拉选择器选择不同的排行榜类型

### 3. 选择时间周期
使用单选按钮组选择查看的时间范围

### 4. 查看排行榜数据
- Top 3用户以特殊的奖台形式显示
- 其余用户以表格形式显示
- 支持查看用户详细信息

## 后端API要求

### 必需的API端点
```
GET /api/v2/leaderboard?metric={metric}&period={period}&limit={limit}
GET /api/v2/leaderboard/metrics
GET /api/v2/leaderboard/periods
GET /api/v2/leaderboard/overview?period={period}&limit={limit}
```

### 数据格式要求
```json
{
  "success": true,
  "data": [
    {
      "userId": "string",
      "userName": "string",
      "avatarUrl": "string",
      "department": "string",
      "points": 0,
      "coins": 0,
      "diamonds": 0,
      "tasksCreated": 0,
      "tasksCompleted": 0,
      "tasksClaimed": 0,
      "faultsRecorded": 0,
      "maintenanceCreated": 0,
      "assetsUpdated": 0
    }
  ]
}
```

## 测试建议

### 1. 功能测试
- 验证所有排行榜类型切换正常
- 确认时间周期过滤工作正常
- 测试数据显示准确性

### 2. 性能测试
- 测试大量用户数据的加载性能
- 验证切换操作的响应速度

### 3. 兼容性测试
- 测试不同浏览器的兼容性
- 验证移动端显示效果

## 未来扩展

### 1. 可能的增强功能
- 添加更多排行榜类型
- 实现实时排行榜更新
- 添加排行榜历史趋势图
- 支持部门/团队排行榜

### 2. 性能优化
- 实现排行榜数据缓存
- 添加虚拟滚动支持大量数据
- 优化API调用频率

## 总结

多维度排行榜功能的实现大大提升了用户体验，让用户可以从多个角度查看自己和同事的表现。该功能完全基于现有的后端API基础设施，具有良好的扩展性和维护性。
