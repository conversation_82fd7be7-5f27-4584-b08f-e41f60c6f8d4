// File: Models/Enums/TaskType.cs
// Description: 任务类型枚举

using System.ComponentModel.DataAnnotations;

namespace ItAssetsSystem.Models.Enums
{
    /// <summary>
    /// 任务类型枚举
    /// </summary>
    public enum TaskType
    {
        /// <summary>
        /// 普通任务
        /// </summary>
        [Display(Name = "普通任务")]
        Regular = 0,

        /// <summary>
        /// 周期性任务
        /// </summary>
        [Display(Name = "周期性任务")]
        Periodic = 1,

        /// <summary>
        /// PDCA任务
        /// </summary>
        [Display(Name = "PDCA任务")]
        PDCA = 2
    }
} 