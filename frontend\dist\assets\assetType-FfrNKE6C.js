import{C as e,aV as t}from"./index-CG5lHOPO.js";function s(e={}){const s={};return s.page=e.page||1,s.pageSize=e.pageSize||1e3,void 0!==e.isActive&&(s.isActive=e.isActive),e.keyword&&(s.keyword=e.keyword),t.get("/AssetType",{params:s}).then((e=>{if(e&&e.data&&Array.isArray(e.data.items)){const t=[],s=new Set;return e.data.items.forEach((e=>{s.has(e.id)||(s.add(e.id),t.push(e))})),e.data.items=t,e}return e})).catch((e=>{throw e}))}const r={getAssetTypes:s,getAssetTypeById:function(e){return t.get(`/AssetType/${e}`)},createAssetType:function(e){const s={...e};if(void 0!==s.parentId&&null!==s.parentId&&"string"==typeof s.parentId){const e=parseInt(s.parentId,10);isNaN(e)?""===s.parentId.trim()&&(s.parentId=null):s.parentId=e}return t.post("/AssetType",s)},updateAssetType:function(e,s){let r=e;return"string"==typeof e&&(r=parseInt(e,10),isNaN(r))?Promise.reject(new Error("资产类型ID无效")):s&&s.id&&"string"==typeof s.id&&(s.id=parseInt(s.id,10),isNaN(s.id))?Promise.reject(new Error("资产类型data.id无效")):t.put(`/AssetType/${r}`,s)},deleteAssetType:function(e){return t.delete(`/AssetType/${e}`)},getAssetTypeSpecs:function(e){return t.get(`/AssetType/${e}/specifications`)},updateAssetTypeSpecs:function(e,s){return t.put(`/AssetType/${e}/specifications`,s)},exportAssetTypes:function(e){return t.get("/AssetType/export",{params:e,responseType:"blob",headers:{Accept:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel,text/csv"}}).then((e=>e)).catch((e=>{throw e}))},getAssetTypeImportTemplate:function(){return t.get("/Import/template",{params:{entityType:"AssetTypes",format:"excel"},responseType:"blob"})},importAssetTypes:function(e){return t.post("/Import/data",e,{params:{entityType:"AssetTypes"},headers:{"Content-Type":"multipart/form-data"}})},toggleAssetTypeActive:function(e){let s=e;return"string"==typeof e&&(s=parseInt(e,10),isNaN(s))?Promise.reject(new Error("资产类型ID无效")):t.put(`/AssetType/${s}/toggle-active`)}};export{r as a,s as g};
