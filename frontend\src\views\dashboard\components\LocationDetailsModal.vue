<template>
  <el-dialog
    v-model="visible"
    :title="`位置详情 - ${location.locationName}`"
    width="600px"
    :before-close="handleClose"
    class="location-details-modal"
    :modal-class="'custom-modal-backdrop'"
  >
    <div class="location-details-content">
      <!-- 基本信息 -->
      <div class="basic-info mb-6">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-gray-800">基本信息</h3>
          <el-tag :type="statusTagType" size="large">
            <el-icon class="mr-1">
              <component :is="statusIcon" />
            </el-icon>
            {{ statusText }}
          </el-tag>
        </div>
        
        <div class="grid grid-cols-2 gap-4">
          <div class="info-item">
            <label class="info-label">位置路径</label>
            <div class="info-value">{{ location.locationPath }}</div>
          </div>
          <div class="info-item">
            <label class="info-label">有效部门</label>
            <div class="info-value">{{ location.effectiveDepartmentName || '未分配' }}</div>
          </div>
          <div class="info-item">
            <label class="info-label">直接部门</label>
            <div class="info-value">{{ location.directDepartmentName || '继承自上级' }}</div>
          </div>
          <div class="info-item">
            <label class="info-label">设备数量</label>
            <div class="info-value">{{ location.assetCount }} 台</div>
          </div>
        </div>
      </div>

      <!-- 设备监控数据 -->
      <div class="monitoring-data mb-6" v-if="location.details">
        <h3 class="text-lg font-semibold text-gray-800 mb-4">设备监控数据</h3>
        <div class="grid grid-cols-2 lg:grid-cols-4 gap-3">
          <div class="monitoring-card">
            <div class="monitoring-icon temperature">
              <el-icon size="20"><Histogram /></el-icon>
            </div>
            <div class="monitoring-info">
              <div class="monitoring-label">温度</div>
              <div class="monitoring-value">{{ location.details.temperature }}</div>
            </div>
          </div>
          
          <div class="monitoring-card">
            <div class="monitoring-icon pressure">
              <el-icon size="20"><Connection /></el-icon>
            </div>
            <div class="monitoring-info">
              <div class="monitoring-label">压力</div>
              <div class="monitoring-value">{{ location.details.pressure }}</div>
            </div>
          </div>
          
          <div class="monitoring-card">
            <div class="monitoring-icon uptime">
              <el-icon size="20"><Timer /></el-icon>
            </div>
            <div class="monitoring-info">
              <div class="monitoring-label">运行时间</div>
              <div class="monitoring-value">{{ location.details.uptime }}</div>
            </div>
          </div>
          
          <div class="monitoring-card">
            <div class="monitoring-icon maintenance">
              <el-icon size="20"><Tools /></el-icon>
            </div>
            <div class="monitoring-info">
              <div class="monitoring-label">最后维护</div>
              <div class="monitoring-value">{{ location.details.lastMaintenance }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 告警信息 -->
      <div v-if="location.details?.errorCode || location.details?.warningMessage" class="alerts mb-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-4">告警信息</h3>
        
        <el-alert
          v-if="location.details.errorCode"
          title="错误警告"
          type="error"
          :description="`错误代码: ${location.details.errorCode} - 请立即检查设备`"
          show-icon
          class="mb-3"
        />
        
        <el-alert
          v-if="location.details.warningMessage"
          title="设备警告"
          type="warning"
          :description="location.details.warningMessage"
          show-icon
        />
      </div>

      <!-- 设备列表 -->
      <div v-if="location.assets && location.assets.length > 0" class="assets-list mb-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-4">设备列表</h3>
        <div class="max-h-48 overflow-y-auto">
          <el-table :data="location.assets" size="small" stripe>
            <el-table-column prop="assetCode" label="设备编码" width="120" />
            <el-table-column prop="assetName" label="设备名称" />
            <el-table-column prop="assetTypeName" label="设备类型" width="120" />
            <el-table-column label="操作" width="100" align="center">
              <template #default="{ row }">
                <el-button 
                  type="primary" 
                  size="small" 
                  text
                  @click="viewAssetDetail(row.assetId)"
                >
                  查看
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="actions">
        <div class="flex justify-between">
          <div class="flex gap-2">
            <el-button type="primary" @click="startDiagnostic">
              <el-icon class="mr-1"><Lightning /></el-icon>
              启动诊断
            </el-button>
            <el-button @click="viewHistory">
              <el-icon class="mr-1"><Document /></el-icon>
              查看历史
            </el-button>
          </div>
          <el-button @click="handleClose">关闭</el-button>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  CircleCheck, 
  WarningFilled, 
  CircleClose, 
  Setting,
  Histogram,
  Connection,
  Timer,
  Tools,
  Lightning,
  Document
} from '@element-plus/icons-vue'

const props = defineProps({
  location: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['close'])

const visible = ref(true)

// 状态配置
const statusConfig = {
  operational: {
    type: 'success',
    text: '正常运行',
    icon: CircleCheck
  },
  warning: {
    type: 'warning',
    text: '警告状态',
    icon: WarningFilled
  },
  error: {
    type: 'danger',
    text: '故障状态',
    icon: CircleClose
  },
  idle: {
    type: 'info',
    text: '空闲状态',
    icon: Setting
  }
}

// 计算属性
const statusTagType = computed(() => {
  return statusConfig[props.location.status]?.type || 'info'
})

const statusText = computed(() => {
  return statusConfig[props.location.status]?.text || '未知状态'
})

const statusIcon = computed(() => {
  return statusConfig[props.location.status]?.icon || Setting
})

// 方法
const handleClose = () => {
  visible.value = false
  setTimeout(() => {
    emit('close')
  }, 300)
}

const startDiagnostic = () => {
  ElMessage.success('诊断已启动，请等待结果')
}

const viewHistory = () => {
  ElMessage.info('历史记录功能开发中')
}

const viewAssetDetail = (assetId) => {
  ElMessage.info(`查看设备 ${assetId} 详情`)
  // 这里可以跳转到设备详情页面
  // this.$router.push({ name: 'AssetDetail', params: { id: assetId } })
}

// 监听位置变化
watch(() => props.location, (newLocation) => {
  if (newLocation) {
    visible.value = true
  }
}, { immediate: true })
</script>

<style scoped>
.location-details-content {
  padding: 0;
}

.info-item {
  @apply bg-gray-50 p-3 rounded-lg;
}

.info-label {
  @apply text-sm text-gray-600 font-medium block mb-1;
}

.info-value {
  @apply text-base text-gray-900 font-semibold;
}

.monitoring-card {
  @apply bg-gradient-to-r from-blue-50 to-indigo-50 p-3 rounded-lg border border-blue-100 flex items-center;
}

.monitoring-icon {
  @apply w-10 h-10 rounded-full flex items-center justify-center mr-3 text-white;
}

.monitoring-icon.temperature {
  @apply bg-gradient-to-r from-red-400 to-red-500;
}

.monitoring-icon.pressure {
  @apply bg-gradient-to-r from-blue-400 to-blue-500;
}

.monitoring-icon.uptime {
  @apply bg-gradient-to-r from-green-400 to-green-500;
}

.monitoring-icon.maintenance {
  @apply bg-gradient-to-r from-yellow-400 to-yellow-500;
}

.monitoring-info {
  @apply flex-1;
}

.monitoring-label {
  @apply text-xs text-gray-600 font-medium;
}

.monitoring-value {
  @apply text-sm font-bold text-gray-900;
}

.actions {
  @apply pt-4 border-t border-gray-200;
}

/* 自定义对话框样式 */
:deep(.el-dialog) {
  @apply rounded-xl;
}

:deep(.el-dialog__header) {
  @apply bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-t-xl;
}

:deep(.el-dialog__title) {
  @apply text-white font-semibold;
}

:deep(.el-dialog__close) {
  @apply text-white hover:text-gray-200;
}

:deep(.el-dialog__body) {
  @apply p-6;
}

/* 自定义模态背景 */
:deep(.custom-modal-backdrop) {
  @apply bg-black/50 backdrop-blur-sm;
}

/* 表格样式 */
:deep(.el-table) {
  @apply rounded-lg overflow-hidden;
}

:deep(.el-table th) {
  @apply bg-gray-50;
}

/* 响应式适配 */
@media (max-width: 768px) {
  .monitoring-card {
    @apply flex-col text-center;
  }
  
  .monitoring-icon {
    @apply mb-2 mr-0;
  }
  
  .grid-cols-2 {
    @apply grid-cols-1;
  }
  
  .lg\:grid-cols-4 {
    @apply grid-cols-2;
  }
}
</style>