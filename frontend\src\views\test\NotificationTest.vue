<template>
  <div class="notification-test">
    <h2>通知系统测试页面</h2>
    
    <el-card class="test-card">
      <template #header>
        <span>API测试</span>
      </template>
      
      <el-space direction="vertical" style="width: 100%">
        <el-button @click="testGetNotifications" type="primary">
          获取通知列表
        </el-button>
        
        <el-button @click="testSendNotification" type="success">
          发送测试通知
        </el-button>
        
        <el-button @click="testGetUnreadCount" type="info">
          获取未读数量
        </el-button>
      </el-space>
    </el-card>
    
    <el-card class="test-card" style="margin-top: 20px;">
      <template #header>
        <span>通知中心组件测试</span>
      </template>
      
      <NotificationCenter mode="drawer" v-model:visible="drawerVisible" />
      
      <el-button @click="drawerVisible = true" type="primary">
        打开通知中心 (Drawer模式)
      </el-button>
    </el-card>
    
    <el-card class="test-card" style="margin-top: 20px;">
      <template #header>
        <span>测试结果</span>
      </template>
      
      <div class="test-results">
        <pre>{{ JSON.stringify(testResults, null, 2) }}</pre>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { notificationApi } from '@/api/notification'
import { useNotificationStore } from '@/stores/modules/notification'
import NotificationCenter from '@/views/tasks/components/NotificationCenter.vue'

const drawerVisible = ref(false)
const testResults = ref({})
const notificationStore = useNotificationStore()

const testGetNotifications = async () => {
  try {
    console.log('开始测试获取通知列表...')
    const response = await notificationApi.getNotifications()
    console.log('API响应:', response)
    
    testResults.value.getNotifications = {
      success: response.success,
      data: response.data,
      timestamp: new Date().toISOString()
    }
    
    if (response.success) {
      ElMessage.success(`获取成功，共${response.data.notifications.length}条通知`)
    } else {
      ElMessage.error('获取失败: ' + response.message)
    }
  } catch (error) {
    console.error('测试失败:', error)
    testResults.value.getNotifications = {
      error: error.message,
      timestamp: new Date().toISOString()
    }
    ElMessage.error('测试失败: ' + error.message)
  }
}

const testSendNotification = async () => {
  try {
    console.log('开始测试发送通知...')
    const response = await notificationApi.sendTestNotification()
    console.log('发送响应:', response)
    
    testResults.value.sendNotification = {
      success: response.success,
      message: response.message,
      timestamp: new Date().toISOString()
    }
    
    if (response.success) {
      ElMessage.success('测试通知发送成功')
      // 刷新通知列表
      setTimeout(() => {
        notificationStore.fetchNotifications(true)
      }, 1000)
    } else {
      ElMessage.error('发送失败: ' + response.message)
    }
  } catch (error) {
    console.error('发送失败:', error)
    testResults.value.sendNotification = {
      error: error.message,
      timestamp: new Date().toISOString()
    }
    ElMessage.error('发送失败: ' + error.message)
  }
}

const testGetUnreadCount = async () => {
  try {
    console.log('开始测试获取未读数量...')
    const response = await notificationApi.getUnreadCount()
    console.log('未读数量响应:', response)
    
    testResults.value.getUnreadCount = {
      success: response.success,
      data: response.data,
      timestamp: new Date().toISOString()
    }
    
    if (response.success) {
      ElMessage.success(`未读通知数量: ${response.data}`)
    } else {
      ElMessage.error('获取失败: ' + response.message)
    }
  } catch (error) {
    console.error('获取失败:', error)
    testResults.value.getUnreadCount = {
      error: error.message,
      timestamp: new Date().toISOString()
    }
    ElMessage.error('获取失败: ' + error.message)
  }
}
</script>

<style scoped>
.notification-test {
  padding: 20px;
}

.test-card {
  margin-bottom: 20px;
}

.test-results {
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  max-height: 400px;
  overflow-y: auto;
}

.test-results pre {
  margin: 0;
  font-size: 12px;
}
</style>
