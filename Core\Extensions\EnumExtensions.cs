// File: Core/Extensions/EnumExtensions.cs
// Description: 枚举扩展方法，处理枚举与字符串转换及显示名称

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
// using ItAssetsSystem.Models.Entities; // Removed - Entities enums no longer exist
using ItAssetsSystem.Models.Enums;

namespace ItAssetsSystem.Core.Extensions
{
    /// <summary>
    /// 枚举扩展方法
    /// </summary>
    public static class EnumExtensions
    {
        /// <summary>
        /// 获取任务状态显示名称
        /// </summary>
        /// <param name="status">任务状态枚举 (Models.Enums.TaskStatus)</param>
        /// <returns>状态显示名称</returns>
        public static string GetDisplayName(this Models.Enums.TaskStatus status)
        {
            return status switch
            {
                Models.Enums.TaskStatus.NotStarted => "未开始",
                Models.Enums.TaskStatus.InProgress => "进行中",
                Models.Enums.TaskStatus.OnHold => "已暂停",
                Models.Enums.TaskStatus.Completed => "已完成",
                Models.Enums.TaskStatus.Cancelled => "已取消",
                _ => status.ToString()
            };
        }

        /// <summary>
        /// 获取任务优先级显示名称
        /// </summary>
        /// <param name="priority">任务优先级枚举 (Models.Enums.TaskPriority)</param>
        /// <returns>优先级显示名称</returns>
        public static string GetDisplayName(this Models.Enums.TaskPriority priority)
        {
            return priority switch
            {
                Models.Enums.TaskPriority.Low => "低",
                Models.Enums.TaskPriority.Medium => "中",
                Models.Enums.TaskPriority.High => "高",
                Models.Enums.TaskPriority.Urgent => "紧急",
                Models.Enums.TaskPriority.Critical => "关键",
                _ => priority.ToString()
            };
        }

        /// <summary>
        /// 获取任务类型显示名称
        /// </summary>
        /// <param name="type">任务类型枚举 (Models.Enums.TaskType)</param>
        /// <returns>类型显示名称</returns>
        public static string GetDisplayName(this Models.Enums.TaskType type)
        {
            return type switch
            {
                Models.Enums.TaskType.Regular => "普通任务",
                Models.Enums.TaskType.Periodic => "周期性任务",
                Models.Enums.TaskType.PDCA => "PDCA任务",
                _ => type.ToString()
            };
        }

        /// <summary>
        /// 将字符串转换为任务状态枚举 (Models.Enums.TaskStatus)
        /// </summary>
        /// <param name="status">状态字符串</param>
        /// <returns>任务状态枚举</returns>
        public static Models.Enums.TaskStatus ToEnumTaskStatus(this string status)
        {
            if (string.IsNullOrEmpty(status))
                return Models.Enums.TaskStatus.NotStarted;

            switch (status.Trim().ToLower())
            {
                case "todo":
                case "notstarted":
                case "not_started":
                    return Models.Enums.TaskStatus.NotStarted;
                case "inprogress":
                case "in progress":
                case "in_progress":
                    return Models.Enums.TaskStatus.InProgress;
                case "onhold":
                case "on_hold":
                case "paused":
                    return Models.Enums.TaskStatus.OnHold;
                case "pendingreview":
                case "pending_review":
                case "review":
                    return Models.Enums.TaskStatus.PendingReview;
                case "done":
                case "completed":
                    return Models.Enums.TaskStatus.Completed;
                case "canceled":
                case "cancelled":
                    return Models.Enums.TaskStatus.Cancelled;
                case "overdue":
                case "late":
                    return Models.Enums.TaskStatus.Overdue;
                default:
                    return Models.Enums.TaskStatus.NotStarted;
            }
        }

        /// <summary>
        /// 将字符串转换为任务优先级枚举 (Models.Enums.TaskPriority)
        /// </summary>
        /// <param name="priority">优先级字符串</param>
        /// <returns>任务优先级枚举</returns>
        public static Models.Enums.TaskPriority ToEnumTaskPriority(this string priority)
        {
            if (string.IsNullOrEmpty(priority))
                return Models.Enums.TaskPriority.Medium;

            switch (priority.Trim().ToLower())
            {
                case "low":
                    return Models.Enums.TaskPriority.Low;
                case "medium":
                case "normal":
                    return Models.Enums.TaskPriority.Medium;
                case "high":
                    return Models.Enums.TaskPriority.High;
                case "urgent":
                case "critical":
                    return Models.Enums.TaskPriority.Urgent;
                default:
                    return Models.Enums.TaskPriority.Medium;
            }
        }

        /// <summary>
        /// 将字符串转换为任务类型枚举 (Models.Enums.TaskType)
        /// </summary>
        /// <param name="type">类型字符串</param>
        /// <returns>任务类型枚举</returns>
        public static Models.Enums.TaskType ToEnumTaskType(this string type)
        {
            if (string.IsNullOrEmpty(type))
                return Models.Enums.TaskType.Regular;

            switch (type.Trim().ToLower())
            {
                case "normal":
                case "daily":
                case "regular":
                case "default":
                    return Models.Enums.TaskType.Regular;
                case "periodic":
                case "recurring":
                case "cyclical":
                    return Models.Enums.TaskType.Periodic;
                case "pdca":
                case "plan":
                case "pdcaplan":
                    return Models.Enums.TaskType.PDCA;
                default:
                    return Models.Enums.TaskType.Regular;
            }
        }

        /// <summary>
        /// 将字符串转换为枚举类型PDCAStage (Models.Enums.PDCAStage)
        /// </summary>
        /// <param name="stage">PDCA阶段字符串</param>
        /// <returns>PDCAStage枚举值，如果无法转换则返回null</returns>
        public static Models.Enums.PDCAStage? ToEnumPDCAStage(this string stage)
        {
            if (string.IsNullOrEmpty(stage))
                return null;

            if (Enum.TryParse<Models.Enums.PDCAStage>(stage, true, out var result))
                return result;

            // 默认映射规则
            return stage.ToLower() switch
            {
                "plan" => Models.Enums.PDCAStage.Plan,
                "do" => Models.Enums.PDCAStage.Do,
                "check" => Models.Enums.PDCAStage.Check,
                "act" => Models.Enums.PDCAStage.Act,
                "action" => Models.Enums.PDCAStage.Act,
                _ => null
            };
        }
    }
} 