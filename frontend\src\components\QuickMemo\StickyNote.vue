// File: frontend/src/components/QuickMemo/StickyNote.vue
// Description: 单个随手记便利贴组件，支持查看和内联编辑。

<template>
  <div 
    class="memo-card" 
    :style="cardStyle"
    @click="!isEditingCard ? startEditingCard() : null" 
    tabindex="0" 
  >
    <template v-if="!isEditingCard">
      <div class="card-header" @mousedown.stop> 
        <h4 class="memo-title">{{ memoData.title }}</h4>
      </div>
      <div 
        v-if="memoData.content && memoData.content !== memoData.title" 
        class="card-content" 
        v-html="renderedContent"
        @mousedown.stop 
      >
      </div>
      <div class="card-footer" @mousedown.stop>
        <div class="footer-info">
          <span v-if="memoData.user && memoData.user.name" class="memo-creator">
            创建者: {{ memoData.user.name }}
          </span>
          <span class="memo-time">创建: {{ formattedTimeAgo }}</span>
          <span v-if="memoData.updatedAt && formattedUpdatedAt !== formattedTimeAgo" class="memo-time memo-updated-time">
            更新: {{ formattedUpdatedAt }}
          </span>
        </div>
        <div class="actions">
          <el-popconfirm
            title="确定删除这个便签吗?"
            confirm-button-text="确定"
            cancel-button-text="取消"
            @confirm="handleDelete"
            :teleported="false"
            trigger="click"
          >
            <template #reference>
              <el-icon class="action-icon delete-icon" @click.stop title="删除便签">
                <Delete />
              </el-icon>
            </template>
          </el-popconfirm>
        </div>
      </div>
    </template>
    <template v-else>
      <div class="editing-container" ref="editingContainerRef" @focusout="handleEditContainerBlur">
        <el-input
          type="textarea"
          v-model="editableTitle"
          autosize
          ref="titleInputRef"
          placeholder="便签标题"
          class="editing-title-input"
          @mousedown.stop
          @keydown.esc.stop="cancelEditAndExit"
        />
        <el-input
          v-if="shouldEditContentSeparately"
          type="textarea"
          v-model="editableContent"
          :autosize="{ minRows: 2, maxRows: 6 }"
          placeholder="便签内容 (可选)"
          class="editing-content-input"
          @mousedown.stop
          @keydown.esc.stop="cancelEditAndExit"
        />
      </div>
    </template>
  </div>
</template>

<script setup>
import { ref, computed, nextTick, shallowRef } from 'vue';
import { Delete } from '@element-plus/icons-vue';
import { formatDistanceToNow, parseISO } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { useQuickMemoStore } from '@/stores/modules/quickMemo';
import { ElMessage } from 'element-plus';
import DOMPurify from 'dompurify';

const props = defineProps({
  memoData: {
    type: Object,
    required: true,
  }
});

const quickMemoStore = useQuickMemoStore();

const isEditingCard = ref(false);
const editableTitle = ref('');
const editableContent = ref('');
const titleInputRef = ref(null);
const editingContainerRef = ref(null);

const originalTitleBeforeEdit = ref('');
const originalContentBeforeEdit = ref('');

const shouldEditContentSeparately = computed(() => {
  return props.memoData.content && props.memoData.content !== props.memoData.title;
});

const renderedContent = computed(() => {
  if (props.memoData.content) {
    const breakReplaced = props.memoData.content.replace(/\\n/g, '<br>');
    return DOMPurify.sanitize(breakReplaced);
  }
  return '';
});

const defaultColors = [
  '#FFF9C4', // Light Yellow
  '#FFCDD2', // Light Red/Pink
  '#C8E6C9', // Light Green
  '#BBDEFB', // Light Blue
  '#D1C4E9', // Light Purple
  '#B2DFDB', // Light Teal
  '#FFE0B2', // Light Orange
  '#F0F4C3', // Light Lime
];
const getRandomColor = () => defaultColors[Math.floor(Math.random() * defaultColors.length)];

const cardStyle = computed(() => {
  let backgroundColor = getRandomColor();
  if (props.memoData.category && props.memoData.category.color) {
    if (/^#([0-9A-F]{3}){1,2}$/i.test(props.memoData.category.color)) {
      backgroundColor = props.memoData.category.color;
    }
  } else if (props.memoData.color && /^#([0-9A-F]{3}){1,2}$/i.test(props.memoData.color)) {
     backgroundColor = props.memoData.color;
  }
  const colorWithOpacity = `${backgroundColor}${Math.floor(0.7 * 255).toString(16).padStart(2, '0')}`;
  return {
    backgroundColor: /^#([0-9A-F]{3}){1,2}$/i.test(backgroundColor) ? colorWithOpacity : `${getRandomColor()}B3`,
  };
});

const formattedTimeAgo = computed(() => {
  if (!props.memoData.createdAt) return '未知';
  try {
    return formatDistanceToNow(parseISO(props.memoData.createdAt), { addSuffix: true, locale: zhCN });
  } catch (e) {
     console.warn(`Failed to parse date (createdAt): ${props.memoData.createdAt}`, e);
    return props.memoData.createdAt?.split('T')[0] || '未知时间';
  }
});

const formattedUpdatedAt = computed(() => {
  if (!props.memoData.updatedAt) return ''; // Return empty if no updatedAt
  try {
    // Check if updatedAt is significantly different from createdAt
    const created = parseISO(props.memoData.createdAt);
    const updated = parseISO(props.memoData.updatedAt);
    // If updated is within, say, 1 minute of created, consider it same for "ago" display
    if (Math.abs(updated.getTime() - created.getTime()) < 60000) {
        return formatDistanceToNow(created, { addSuffix: true, locale: zhCN }); // Show based on created
    }
    return formatDistanceToNow(updated, { addSuffix: true, locale: zhCN });
  } catch (e) {
    console.warn(`Failed to parse date (updatedAt): ${props.memoData.updatedAt}`, e);
    return props.memoData.updatedAt?.split('T')[0] || '未知时间';
  }
});

const startEditingCard = () => {
  if (isEditingCard.value) return;

  originalTitleBeforeEdit.value = props.memoData.title;
  originalContentBeforeEdit.value = props.memoData.content || '';

  editableTitle.value = props.memoData.title;
  editableContent.value = props.memoData.content || '';
  isEditingCard.value = true;
  
  nextTick(() => {
    titleInputRef.value?.focus();
    titleInputRef.value?.textarea?.select();
  });
};

const handleEditContainerBlur = (event) => {
  if (!editingContainerRef.value?.contains(event.relatedTarget)) {
    saveEdit();
  }
};

const cancelEditAndExit = () => {
  editableTitle.value = originalTitleBeforeEdit.value;
  editableContent.value = originalContentBeforeEdit.value;
  isEditingCard.value = false;
};

const saveEdit = async () => {
  if (!isEditingCard.value) return;

  const currentTitle = editableTitle.value.trim();
  const currentContent = shouldEditContentSeparately.value ? (editableContent.value.trim() || currentTitle) : currentTitle;
  const originalComparableContent = shouldEditContentSeparately.value ? (originalContentBeforeEdit.value.trim() || originalTitleBeforeEdit.value.trim()) : originalTitleBeforeEdit.value.trim();

  if (currentTitle === originalTitleBeforeEdit.value.trim() && currentContent === originalComparableContent) {
    isEditingCard.value = false;
    return;
  }

  if (!currentTitle) {
    ElMessage.warning('标题不能为空');
    return;
  }
  
  const payload = {
    title: currentTitle,
    content: currentContent,
    positionX: props.memoData.positionX,
    positionY: props.memoData.positionY,
    sizeWidth: props.memoData.sizeWidth,
    sizeHeight: props.memoData.sizeHeight,
    zIndex: props.memoData.zIndex,
    isPinned: props.memoData.isPinned,
    categoryId: props.memoData.categoryId
  };

  try {
    await quickMemoStore.editQuickMemo({ ...props.memoData, ...payload });
    ElMessage.success('更新成功');
  } catch (error) {
    ElMessage.error(`更新失败: ${error.message || '请重试'}`);
    console.error("Failed to update memo:", error);
    editableTitle.value = originalTitleBeforeEdit.value;
    editableContent.value = originalContentBeforeEdit.value;
  } finally {
    isEditingCard.value = false;
  }
};

const handleDelete = async () => {
  try {
    await quickMemoStore.removeQuickMemo(props.memoData.id);
    ElMessage.success('删除成功');
  } catch (error) {
    ElMessage.error(`删除失败: ${error.message || '请重试'}`);
    console.error("Failed to delete memo:", error);
  }
};

</script>

<style scoped>
.memo-card {
  width: 240px; 
  height: auto;
  padding: 12px 16px;
  border-radius: 8px;
  box-shadow: 0 2px 6px rgba(0,0,0,0.1);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  transition: box-shadow 0.2s ease-in-out, transform 0.2s ease-in-out;
  cursor: pointer;
  border: 1px solid rgba(0,0,0,0.08);
  box-sizing: border-box;
  overflow-wrap: break-word;
  word-break: break-word;
}

.memo-card:hover {
  box-shadow: 0 5px 15px rgba(0,0,0,0.15);
}

.card-header {
  margin-bottom: 8px;
}

.memo-title {
  font-size: 15px;
  font-weight: 600;
  margin: 0 0 4px 0;
  line-height: 1.4;
  color: #2c3e50;
}
.card-content {
  font-size: 13px;
  color: #555e67;
  line-height: 1.5;
  white-space: pre-wrap; 
  flex-grow: 1; 
  margin-bottom: 10px;
  max-height: 150px;
  overflow-y: auto;
}
.card-content::-webkit-scrollbar {
  width: 5px;
}
.card-content::-webkit-scrollbar-thumb {
  background-color: #c1c1c1;
  border-radius: 10px;
}
.card-content::-webkit-scrollbar-track {
  background-color: transparent;
}


.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 11px;
  color: #777c81;
  border-top: 1px solid rgba(0,0,0,0.05);
  padding-top: 8px;
  margin-top: auto; 
  width: 100%;
}

.footer-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 3px;
}

.memo-creator {
  font-weight: 500;
}

.memo-time {
  /* font-style: italic; */
}

.memo-updated-time {
  color: #a0a0a0;
}

.actions {
  display: flex;
  align-items: center;
}

.actions .el-icon {
  cursor: pointer;
  margin-left: 10px;
  font-size: 15px;
  color: #5f6368;
}
.actions .el-icon:hover {
  color: var(--el-color-primary);
}

.action-icon {
    padding: 4px;
    border-radius: 4px;
}

.action-icon:hover {
    background-color: rgba(0,0,0,0.05);
}

.delete-icon:hover {
  color: var(--el-color-danger);
  background-color: rgba(var(--el-color-danger-rgb), 0.1);
}

.editing-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}
.card-editing-content {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  margin-bottom: 0;
}
.editing-title-input {
  margin-bottom: 8px;
}
.editing-title-input :deep(textarea) {
  font-size: 15px;
  font-weight: 600;
  line-height: 1.4;
  color: #2c3e50;
  padding: 6px 8px;
  border: none;
  box-shadow: none !important;
  background-color: transparent;
}
.editing-content-input :deep(textarea) {
  font-size: 13px;
  line-height: 1.5;
  color: #555e67;
  padding: 6px 8px;
  border: none;
  box-shadow: none !important;
  background-color: transparent;
  flex-grow: 1;
}

:deep(.el-popper) {
  /* If needed */
}
</style> 