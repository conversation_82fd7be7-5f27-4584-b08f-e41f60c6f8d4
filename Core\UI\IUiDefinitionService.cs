// IT资产管理系统 - UI定义服务接口
// 文件路径: /Core/UI/IUiDefinitionService.cs
// 功能: 定义JSON驱动UI的核心服务接口

using System.Collections.Generic;
using System.Threading.Tasks;

namespace ItAssetsSystem.Core.UI
{
    /// <summary>
    /// UI元素类型
    /// </summary>
    public enum UiElementType
    {
        /// <summary>
        /// 表单
        /// </summary>
        Form,
        
        /// <summary>
        /// 表格
        /// </summary>
        Table,
        
        /// <summary>
        /// 卡片
        /// </summary>
        Card,
        
        /// <summary>
        /// 标签页
        /// </summary>
        Tab,
        
        /// <summary>
        /// 步骤
        /// </summary>
        Step,
        
        /// <summary>
        /// 树形结构
        /// </summary>
        Tree,
        
        /// <summary>
        /// 菜单
        /// </summary>
        Menu,
        
        /// <summary>
        /// 自定义
        /// </summary>
        Custom
    }
    
    /// <summary>
    /// 表单字段类型
    /// </summary>
    public enum FormFieldType
    {
        /// <summary>
        /// 文本输入
        /// </summary>
        Text,
        
        /// <summary>
        /// 数字输入
        /// </summary>
        Number,
        
        /// <summary>
        /// 日期选择
        /// </summary>
        Date,
        
        /// <summary>
        /// 时间选择
        /// </summary>
        Time,
        
        /// <summary>
        /// 日期时间选择
        /// </summary>
        DateTime,
        
        /// <summary>
        /// 选择框
        /// </summary>
        Select,
        
        /// <summary>
        /// 单选按钮组
        /// </summary>
        Radio,
        
        /// <summary>
        /// 复选框组
        /// </summary>
        Checkbox,
        
        /// <summary>
        /// 开关
        /// </summary>
        Switch,
        
        /// <summary>
        /// 滑块
        /// </summary>
        Slider,
        
        /// <summary>
        /// 上传
        /// </summary>
        Upload,
        
        /// <summary>
        /// 富文本编辑器
        /// </summary>
        RichText,
        
        /// <summary>
        /// 自定义
        /// </summary>
        Custom
    }
    
    /// <summary>
    /// UI定义服务接口
    /// </summary>
    public interface IUiDefinitionService
    {
        /// <summary>
        /// 获取UI定义
        /// </summary>
        /// <param name="definitionId">定义ID</param>
        /// <returns>UI定义对象</returns>
        Task<UiDefinition> GetUiDefinitionAsync(string definitionId);
        
        /// <summary>
        /// 保存UI定义
        /// </summary>
        /// <param name="definition">UI定义对象</param>
        /// <returns>保存是否成功</returns>
        Task<bool> SaveUiDefinitionAsync(UiDefinition definition);
        
        /// <summary>
        /// 获取所有UI定义
        /// </summary>
        /// <returns>UI定义列表</returns>
        Task<List<UiDefinition>> GetAllUiDefinitionsAsync();
        
        /// <summary>
        /// 删除UI定义
        /// </summary>
        /// <param name="definitionId">定义ID</param>
        /// <returns>删除是否成功</returns>
        Task<bool> DeleteUiDefinitionAsync(string definitionId);
        
        /// <summary>
        /// 加载系统内置UI定义
        /// </summary>
        /// <returns>加载是否成功</returns>
        Task<bool> LoadSystemUiDefinitionsAsync();
    }
    
    /// <summary>
    /// UI定义
    /// </summary>
    public class UiDefinition
    {
        /// <summary>
        /// 定义ID
        /// </summary>
        public string Id { get; set; }
        
        /// <summary>
        /// 定义名称
        /// </summary>
        public string Name { get; set; }
        
        /// <summary>
        /// 描述
        /// </summary>
        public string Description { get; set; }
        
        /// <summary>
        /// 类型
        /// </summary>
        public UiElementType Type { get; set; }
        
        /// <summary>
        /// 是否系统内置
        /// </summary>
        public bool IsSystem { get; set; }
        
        /// <summary>
        /// 最后修改时间
        /// </summary>
        public System.DateTime LastModified { get; set; }
        
        /// <summary>
        /// 定义内容（JSON格式）
        /// </summary>
        public string Content { get; set; }
        
        /// <summary>
        /// 关联实体
        /// </summary>
        public string EntityType { get; set; }
        
        /// <summary>
        /// 关联API
        /// </summary>
        public string ApiEndpoint { get; set; }
    }
} 