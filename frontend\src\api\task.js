/**
 * IT资产管理系统 - 任务管理API (V2 Refactored)
 * 文件路径: src/api/task.js
 * 功能描述: 提供任务管理相关的API服务 (基于V2后端接口)
 */

import request from '@/utils/request'
import { cachedRequest } from '@/utils/apiRequestManager'

/**
 * 任务管理相关API (V2)
 */
const taskApi = {
  /**
   * 获取任务列表 (V2) - 带缓存优化
   * @param {object} params 查询参数 (TaskQueryParametersDto)
   *   - pageIndex, pageSize, status, priority, taskType, assigneeUserId, creatorUserId,
   *   - assetId, locationId, startDate, endDate, search (关键词)
   * @returns {Promise}
   */
  getTaskList(params) {
    const apiParams = {
      pageNumber: params.pageNumber || 1,
      pageSize: params.pageSize || 10,
      status: params.status || undefined,
      priority: params.priority || undefined,
      assigneeUserId: params.assigneeUserId || undefined,
      searchTerm: params.searchTerm || undefined,
      forceRefresh: params.forceRefresh || false
    }

    // 如果是强制刷新，直接调用API
    if (params.forceRefresh) {
      return request.get('/v2/tasks', { params: apiParams })
    }

    // 使用缓存机制
    const cacheKey = `tasks-list-${JSON.stringify(apiParams)}`
    return cachedRequest(
      cacheKey,
      () => request.get('/v2/tasks', { params: apiParams }),
      true, // 使用缓存
      30 * 1000 // 30秒短期缓存，任务数据变化较频繁
    )
  },
  
  /**
   * 获取任务列表 (getTasks作为getTaskList的别名，用于兼容视图组件)
   * @param {object} params 查询参数
   * @returns {Promise}
   */
  getTasks(params) {
    return this.getTaskList(params)
  },

  /**
   * 获取任务详情 (V2)
   * @param {string | number} id 任务ID
   * @returns {Promise}
   */
  getTaskDetail(id) {
    console.log('getTaskDetail V2 ID:', id);
    return request.get(`/v2/tasks/${id}`);
  },
  
  /**
   * 获取任务详情 (getTaskById作为getTaskDetail的别名，用于兼容视图组件)
   * @param {string | number} id 任务ID
   * @returns {Promise}
   */
  getTaskById(id) {
    return this.getTaskDetail(id);
  },

  /**
   * 创建新任务 (V2)
   * @param {object} data 任务数据 (CreateTaskRequestDto)
   * @returns {Promise}
   */
  createTask(data) {
    console.log('正在创建任务:', data);
    const apiData = {
      name: data.name,
      description: data.description || '',
      priority: data.priority || 'Medium',
      status: data.status || 'Todo',
      taskType: data.taskType || 'Normal',
      planStartDate: data.planStartDate,
      planEndDate: data.planEndDate,
      assigneeUserId: data.assigneeUserId, // 主负责人ID
      parentTaskId: data.parentTaskId,
      projectId: data.projectId,
      assetId: data.assetId,
      locationId: data.locationId,
      points: data.points || 0,
      pDCAStage: data.pDCAStage || '',
    };
    
    // 处理协作者：智能识别数据格式 - 优先处理显式格式避免冲突
    if (data.assigneeUserId && Array.isArray(data.collaboratorUserIds)) {
      // 格式2：单独的assigneeUserId + collaboratorUserIds (来自TaskFormView.vue 和 QuickTaskCreator)
      apiData.collaboratorUserIds = data.collaboratorUserIds;
      console.log('处理负责人(格式2):', {
        主负责人: apiData.assigneeUserId,
        协作者: apiData.collaboratorUserIds
      });
    }
    // 如果没有明确指定，则从 assigneeUserIds 数组中提取
    else if (Array.isArray(data.assigneeUserIds) && data.assigneeUserIds.length > 0) {
      // 格式1：assigneeUserIds数组 (来自TaskFormDialog.vue)
      if (data.assigneeUserIds.length > 1) {
        // 多个负责人的情况
        apiData.collaboratorUserIds = data.assigneeUserIds.slice(1);
        if (!apiData.assigneeUserId) {
          apiData.assigneeUserId = data.assigneeUserIds[0];
        }
      } else {
        // 只有一个负责人的情况
        if (!apiData.assigneeUserId) {
          apiData.assigneeUserId = data.assigneeUserIds[0];
        }
        apiData.collaboratorUserIds = [];
      }
      
      console.log('处理多负责人(格式1):', {
        主负责人: apiData.assigneeUserId,
        协作者: apiData.collaboratorUserIds
      });
    }
    
    console.log('发送创建任务数据:', apiData);
    return request({
      url: '/v2/tasks',
      method: 'post',
      data: apiData
    });
  },

  /**
   * 更新任务 (V2)
   * @param {object} data 任务数据 (包含taskId和其他更新字段)
   * @returns {Promise}
   */
  updateTask(taskId, data) {
    if (!taskId || taskId === 0 || taskId === '0') {
      return Promise.reject(new Error('任务ID不能为空或为0'));
    }
    
    console.log('updateTask V2 ID:', taskId, 'data:', data);
    
    // 创建API请求数据对象
    const apiData = { ...data };
    
    // 处理协作者：支持两种格式 - 优先处理显式格式避免冲突
    // 格式2：单独的assigneeUserId + collaboratorUserIds (来自TaskFormView.vue)
    if (data.assigneeUserId && Array.isArray(data.collaboratorUserIds)) {
      // TaskFormView格式，直接使用现有字段
      console.log('处理负责人(格式2):', {
        主负责人: apiData.assigneeUserId,
        协作者: apiData.collaboratorUserIds
      });
    }
    // 格式1：assigneeUserIds数组 (来自TaskFormDialog.vue)
    else if (Array.isArray(data.assigneeUserIds) && data.assigneeUserIds.length > 0) {
      // 设置第一个为主负责人
      apiData.assigneeUserId = data.assigneeUserIds[0];
      
      // 除第一个(主负责人)外的其他所有人作为协作者
      apiData.collaboratorUserIds = data.assigneeUserIds.slice(1);
      
      // 删除前端使用的字段，避免后端混淆
      delete apiData.assigneeUserIds;
      
      console.log('处理多负责人(格式1):', {
        主负责人: apiData.assigneeUserId,
        协作者: apiData.collaboratorUserIds
      });
    }
    else if (data.assigneeUserId || (Array.isArray(data.collaboratorUserIds) && data.collaboratorUserIds.length > 0)) {
      // 其他情况：单独负责人或协作者
      console.log('处理负责人(其他格式):', {
        主负责人: apiData.assigneeUserId,
        协作者: apiData.collaboratorUserIds
      });
    }
    
    console.log('发送更新任务数据:', apiData);
    return request({
      url: `/v2/tasks/${taskId}`,
      method: 'put',
      data: apiData
    });
  },

  /**
   * 更新任务状态 (V2)
   * @param {string | number} id 任务ID
   * @param {{ status: string, remarks?: string }} statusData 状态数据 (UpdateTaskStatusRequestDto)
   * @returns {Promise}
   */
  updateTaskStatus(id, statusData) {
    console.log('updateTaskStatus V2 ID:', id, 'statusData:', statusData);
    return request.patch(`/v2/tasks/${id}/status`, statusData);
  },

  /**
   * 更新任务进度 (V2)
   * @param {string | number} id 任务ID
   * @param {{ progress: number, remarks?: string }} progressData 进度数据 (UpdateTaskProgressRequestDto)
   * @returns {Promise}
   */
  updateTaskProgress(id, progressData) {
     console.log('updateTaskProgress V2 ID:', id, 'progressData:', progressData);
     return request.patch(`/v2/tasks/${id}/progress`, progressData);
  },

  /**
   * 分配任务 (V2)
   * @param {string | number} id 任务ID
   * @param {{ assigneeUserId: number }} assignData 分配数据 (AssignTaskRequestDto)
   * @returns {Promise}
   */
  assignTask(id, assignData) {
     console.log('assignTask V2 ID:', id, 'assignData:', assignData);
     return request.patch(`/v2/tasks/${id}/assign`, assignData);
  },

  /**
   * 完成任务 (V2)
   * @param {string | number} id 任务ID
   * @param {{ remarks?: string }} completionData 完成备注
   * @returns {Promise}
   */
  completeTask(id, completionData = {}) {
     console.log('completeTask V2 ID:', id, 'completionData:', completionData);
     return request.patch(`/v2/tasks/${id}/complete`, completionData);
  },

  /**
   * 删除任务 (V2)
   * @param {string | number} id 任务ID
   * @returns {Promise}
   */
  deleteTask(id) {
    console.log('deleteTask V2 ID:', id);
    return request.delete(`/v2/tasks/${id}`);
  },

  // --- Comments (V2) ---
  /**
   * 添加评论 (V2)
   * @param {string | number} taskId 任务ID
   * @param {{ content: string }} commentData 评论内容 (AddCommentRequestDto)
   * @returns {Promise}
   */
  addComment(taskId, commentData) {
    console.log('addComment V2 taskId:', taskId, 'commentData:', commentData);
    return request.post(`/v2/tasks/${taskId}/comments`, commentData);
  },

  /**
   * 获取评论列表 (V2)
   * @param {string | number} taskId 任务ID
   * @param {object} params 分页等参数
   * @returns {Promise}
   */
  getComments(taskId, params) {
    console.log('getComments V2 taskId:', taskId, 'params:', params);
    return request.get(`/v2/tasks/${taskId}/comments`, { params });
  },

  // --- Attachments (V2) ---
  /**
   * 上传任务附件 (V2)
   * @param {string | number} taskId 任务ID
   * @param {FormData} formData 文件数据 (包含 file 和可选 description)
   * @returns {Promise}
   */
  uploadTaskAttachment(taskId, formData) {
    console.log('uploadTaskAttachment V2 taskId:', taskId);
    return request.post(`/v2/tasks/${taskId}/attachments`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
  },

   /**
   * 获取任务附件列表 (V2)
   * @param {string | number} taskId 任务ID
   * @returns {Promise}
   */
  getTaskAttachments(taskId) {
      console.log('getTaskAttachments V2 taskId:', taskId);
      return request.get(`/v2/tasks/${taskId}/attachments`);
  },

  /**
   * 删除任务附件 (V2)
   * @param {string | number} taskId 任务ID
   * @param {string | number} attachmentId 附件ID
   * @returns {Promise}
   */
  deleteTaskAttachment(taskId, attachmentId) {
    console.log('deleteTaskAttachment V2 taskId:', taskId, 'attachmentId:', attachmentId);
    return request.delete(`/v2/tasks/${taskId}/attachments/${attachmentId}`);
  },

  // --- History (V2) ---
  /**
   * 获取任务活动日志 (V2)
   * @param {string | number} taskId 任务ID
   * @param {object} params 分页等参数
   * @returns {Promise}
   */
  getTaskActivityLog(taskId, params) {
    console.log('getTaskActivityLog V2 taskId:', taskId, 'params:', params);
    return request.get(`/v2/tasks/${taskId}/history`, { params });
  },

  // --- Task Options (V2) ---
  /**
   * 获取任务状态选项列表 (V2)
   * @returns {Promise}
   */
  getTaskStatusOptions() {
    return request.get('/v2/tasks/options/statuses');
  },

  /**
   * 获取任务优先级选项列表 (V2)
   * @returns {Promise}
   */
  getTaskPriorityOptions() {
    return request.get('/v2/tasks/options/priorities');
  },

  /**
   * 获取任务类型选项列表 (V2)
   * @returns {Promise}
   */
  getTaskTypeOptions() {
    return request.get('/v2/tasks/options/types');
  },

  getComments(taskId) {
    return request.get(`/v2/tasks/${taskId}/comments`)
  },

  getTaskHistory(taskId) {
    return request.get(`/v2/tasks/${taskId}/history`)
  },

  recordTaskView(taskId) {
    return request.post(`/v2/tasks/${taskId}/view`)
  },

  getTodayViewedCount() {
    return request.get('/v2/tasks/viewed-today')
  },

  /**
   * 领取任务 (游戏化功能)
   * @param {string | number} taskId 任务ID
   * @param {object} claimData 领取数据 (包含notes等)
   * @returns {Promise}
   */
  claimTask(taskId, claimData = {}) {
    console.log('claimTask taskId:', taskId, 'claimData:', claimData);
    return request.post(`/v2/tasks/${taskId}/claim`, claimData);
  }
};

/**
 * 将前端使用的状态值映射为后端API格式
 * @param {string} status 前端状态值
 * @returns {string} 后端API接受的状态值
 */
function mapStatusToBackend(status) {
  // 前端使用小写字符串，后端使用PascalCase
  const statusMap = {
    'todo': 'Todo',
    'doing': 'InProgress',
    'done': 'Done',
    'canceled': 'Cancelled',
    'inprogress': 'InProgress',
    'cancelled': 'Cancelled'
  };
  
  return statusMap[status.toLowerCase()] || status;
}

// 兼容旧调用名
/**
 * 兼容旧调用：获取任务评论
 */
taskApi.getTaskComments = taskApi.getComments;
/**
 * 兼容旧调用：获取任务历史记录
 */
taskApi.getTaskHistory = taskApi.getTaskActivityLog;

export { taskApi } 