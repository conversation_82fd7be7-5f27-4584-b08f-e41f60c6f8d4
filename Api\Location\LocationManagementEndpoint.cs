// IT资产管理系统 - 位置管理API端点
// 文件路径: /Api/Location/LocationManagementEndpoint.cs
// 功能: 提供位置创建、修改和删除的API端点

using System;
using System.Threading.Tasks;
using ItAssetsSystem.Infrastructure.Data;
using ItAssetsSystem.Models.Entities;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace ItAssetsSystem.Api.Location
{
    /// <summary>
    /// 位置创建/修改请求
    /// </summary>
    public class LocationUpsertRequest
    {
        /// <summary>
        /// 位置代码
        /// </summary>
        public string Code { get; set; }
        
        /// <summary>
        /// 位置名称
        /// </summary>
        public string Name { get; set; }
        
        /// <summary>
        /// 位置类型
        /// </summary>
        public int Type { get; set; }
        
        /// <summary>
        /// 上级位置ID
        /// </summary>
        public int? ParentId { get; set; }
        
        /// <summary>
        /// 部门ID
        /// </summary>
        public int? DepartmentId { get; set; }
        
        /// <summary>
        /// 位置路径
        /// </summary>
        public string Path { get; set; }
        
        /// <summary>
        /// 位置描述
        /// </summary>
        public string Description { get; set; }
        
        /// <summary>
        /// 是否激活
        /// </summary>
        public bool IsActive { get; set; } = true;
    }
    
    /// <summary>
    /// 位置管理API端点
    /// </summary>
    [ApiController]
    [Route("api/locations")]
    public class LocationManagementEndpoint
    {
        private readonly AppDbContext _dbContext;
        private readonly ILogger<LocationManagementEndpoint> _logger;
        
        /// <summary>
        /// 构造函数
        /// </summary>
        public LocationManagementEndpoint(
            AppDbContext dbContext,
            ILogger<LocationManagementEndpoint> logger)
        {
            _dbContext = dbContext;
            _logger = logger;
        }
        
        /// <summary>
        /// 创建位置
        /// </summary>
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<IActionResult> CreateLocationAsync([FromBody] LocationUpsertRequest request)
        {
            if (request == null)
            {
                return new BadRequestResult();
            }
            
            if (string.IsNullOrWhiteSpace(request.Code) || string.IsNullOrWhiteSpace(request.Name))
            {
                return new BadRequestObjectResult("位置代码和名称不能为空");
            }
            
            _logger.LogInformation("创建位置，代码：{Code}，名称：{Name}", request.Code, request.Name);
            
            try
            {
                // 检查代码是否已存在
                bool exists = await _dbContext.Locations
                    .AnyAsync(l => l.Code == request.Code);
                
                if (exists)
                {
                    return new BadRequestObjectResult($"位置代码 '{request.Code}' 已存在");
                }
                
                // 检查上级位置是否存在
                if (request.ParentId.HasValue)
                {
                    bool parentExists = await _dbContext.Locations
                        .AnyAsync(l => l.Id == request.ParentId.Value);
                    
                    if (!parentExists)
                    {
                        return new BadRequestObjectResult($"上级位置ID {request.ParentId.Value} 不存在");
                    }
                }
                
                // 创建位置实体
                var location = new Models.Entities.Location
                {
                    Code = request.Code,
                    Name = request.Name,
                    Type = request.Type,
                    ParentId = request.ParentId,
                    Path = request.Path,
                    Description = request.Description,
                    IsActive = true,
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now
                };
                
                // 保存到数据库
                await _dbContext.Locations.AddAsync(location);
                await _dbContext.SaveChangesAsync();
                
                _logger.LogInformation("位置创建成功，ID：{Id}", location.Id);
                
                return new CreatedAtActionResult(
                    nameof(GetLocationByIdAsync), 
                    null, 
                    new { id = location.Id }, 
                    location);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建位置失败");
                return new StatusCodeResult(StatusCodes.Status500InternalServerError);
            }
        }
        
        /// <summary>
        /// 获取位置详情
        /// </summary>
        [HttpGet("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetLocationByIdAsync(int id)
        {
            _logger.LogInformation("获取位置详情，ID：{Id}", id);
            
            try
            {
                var location = await _dbContext.Locations
                    .Include(l => l.Parent)
                    .Include(l => l.Department)
                    .FirstOrDefaultAsync(l => l.Id == id);
                
                if (location == null)
                {
                    return new NotFoundResult();
                }
                
                return new OkObjectResult(location);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取位置详情失败");
                return new StatusCodeResult(StatusCodes.Status500InternalServerError);
            }
        }
        
        /// <summary>
        /// 更新位置
        /// </summary>
        [HttpPut("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> UpdateLocationAsync(int id, [FromBody] LocationUpsertRequest request)
        {
            if (request == null)
            {
                return new BadRequestResult();
            }
            
            if (string.IsNullOrWhiteSpace(request.Code) || string.IsNullOrWhiteSpace(request.Name))
            {
                return new BadRequestObjectResult("位置代码和名称不能为空");
            }
            
            _logger.LogInformation("更新位置，ID：{Id}", id);
            
            try
            {
                // 获取位置
                var location = await _dbContext.Locations.FindAsync(id);
                if (location == null)
                {
                    return new NotFoundResult();
                }
                
                // 检查代码是否已被其他位置使用
                bool codeExists = await _dbContext.Locations
                    .AnyAsync(l => l.Code == request.Code && l.Id != id);
                
                if (codeExists)
                {
                    return new BadRequestObjectResult($"位置代码 '{request.Code}' 已被其他位置使用");
                }
                
                // 检查上级位置是否存在
                if (request.ParentId.HasValue)
                {
                    // 防止循环引用
                    if (request.ParentId.Value == id)
                    {
                        return new BadRequestObjectResult("位置不能将自己设为上级位置");
                    }
                    
                    bool parentExists = await _dbContext.Locations
                        .AnyAsync(l => l.Id == request.ParentId.Value);
                    
                    if (!parentExists)
                    {
                        return new BadRequestObjectResult($"上级位置ID {request.ParentId.Value} 不存在");
                    }
                }
                
                // 更新位置信息
                location.Code = request.Code;
                location.Name = request.Name;
                location.ParentId = request.ParentId;
                location.DefaultDepartmentId = request.DepartmentId;
                location.Description = request.Description;
                location.IsActive = request.IsActive;
                location.UpdatedAt = DateTime.Now;
                
                // 保存更改
                _dbContext.Locations.Update(location);
                await _dbContext.SaveChangesAsync();
                
                _logger.LogInformation("位置更新成功，ID：{Id}", location.Id);
                
                return new OkObjectResult(location);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新位置失败");
                return new StatusCodeResult(StatusCodes.Status500InternalServerError);
            }
        }
        
        /// <summary>
        /// 删除位置
        /// </summary>
        [HttpDelete("{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> DeleteLocationAsync(int id)
        {
            _logger.LogInformation("删除位置，ID：{Id}", id);
            
            try
            {
                // 获取位置
                var location = await _dbContext.Locations.FindAsync(id);
                if (location == null)
                {
                    return new NotFoundResult();
                }
                
                // 检查是否有子位置
                bool hasChildren = await _dbContext.Locations
                    .AnyAsync(l => l.ParentId == id);
                
                if (hasChildren)
                {
                    return new BadRequestObjectResult("无法删除有子位置的位置，请先删除所有子位置");
                }
                
                // 检查是否有资产
                bool hasAssets = await _dbContext.Assets
                    .AnyAsync(a => a.LocationId == id);
                
                if (hasAssets)
                {
                    return new BadRequestObjectResult("无法删除有资产的位置，请先转移或删除所有资产");
                }
                
                // 执行删除
                _dbContext.Locations.Remove(location);
                await _dbContext.SaveChangesAsync();
                
                _logger.LogInformation("位置删除成功，ID：{Id}", id);
                
                return new NoContentResult();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除位置失败");
                return new StatusCodeResult(StatusCodes.Status500InternalServerError);
            }
        }
    }
} 