// File: Core/Abstractions/ISparePartTransactionRepository.cs
// Description: 备品备件出入库记录仓储接口，定义数据访问方法

using System.Collections.Generic;
using System.Threading.Tasks;
using ItAssetsSystem.Application.Common.Dtos;
using ItAssetsSystem.Application.Features.SpareParts.Dtos;
using ItAssetsSystem.Domain.Entities;

namespace ItAssetsSystem.Core.Abstractions
{
    /// <summary>
    /// 备品备件出入库记录仓储接口
    /// </summary>
    public interface ISparePartTransactionRepository
    {
        /// <summary>
        /// 获取出入库记录分页列表
        /// </summary>
        /// <param name="query">查询参数</param>
        /// <returns>记录列表和总数</returns>
        Task<(List<SparePartTransaction>, int)> GetTransactionsPagedAsync(SparePartTransactionQuery query);
        
        /// <summary>
        /// 获取特定备件的出入库记录
        /// </summary>
        /// <param name="partId">备件ID</param>
        /// <param name="query">分页参数</param>
        /// <returns>记录列表和总数</returns>
        Task<(List<SparePartTransaction>, int)> GetTransactionsByPartIdAsync(long partId, PaginationQuery query);
        
        /// <summary>
        /// 根据ID获取出入库记录
        /// </summary>
        /// <param name="id">记录ID</param>
        /// <returns>记录实体</returns>
        Task<SparePartTransaction> GetByIdAsync(long id);
        
        /// <summary>
        /// 创建出入库记录
        /// </summary>
        /// <param name="entity">记录实体</param>
        /// <returns>创建的记录实体</returns>
        Task<SparePartTransaction> CreateAsync(SparePartTransaction entity);
        
        /// <summary>
        /// 获取用户名称
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>用户名称</returns>
        Task<string> GetUserNameAsync(int userId);
    }
} 