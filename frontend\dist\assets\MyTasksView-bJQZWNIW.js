import{_ as e,b as a,e as s,w as t,r as l,ad as r,z as o,a as i,ag as n,E as u,u as d,m as p,o as g,d as c,p as m,l as f,k,t as y,$ as h,aB as v}from"./index-CG5lHOPO.js";import{U as w}from"./UserAvatarStack-X4c9Liov.js";const b={class:"my-tasks-container"},_={class:"card-header"},C={class:"header-actions"},T={class:"filter-container"},V={key:0,class:"loading-container"},P={key:1,class:"empty-data"},F={key:2},U={class:"pagination-container"},S={class:"dialog-footer"};const x=e({name:"MyTasksView",components:{UserAvatarStack:w},setup(){const e=d(),a=l([]),s=l(!0),t=l(!1),i=l(0),p=l(1),g=l(10),c=r({status:"",priority:""}),m=l(!1),f=r({taskId:null,progress:0,status:"",remarks:""}),k=l(!1),y=async()=>{s.value=!0;try{const e={pageIndex:p.value,pageSize:g.value,assignedToMe:!0};c.status&&(e.status=c.status),c.priority&&(e.priority=c.priority);const s=await n.getTasks(e);s.success?(a.value=s.data.items,i.value=s.data.totalCount):u.error(s.message||"加载任务数据失败")}catch(e){u.error("加载任务数据时发生错误")}finally{s.value=!1}},h=e=>({pending:"待处理",in_progress:"进行中",completed:"已完成",cancelled:"已取消"}[e]||e),v=a=>{e.push({name:"TaskDetail",params:{id:a.taskId}})};return o((()=>{y()})),{tasks:a,loading:s,tableLoading:t,total:i,currentPage:p,pageSize:g,filterForm:c,progressDialogVisible:m,progressForm:f,submitting:k,refreshData:()=>{y()},handleFilter:()=>{p.value=1,y()},resetFilter:()=>{c.status="",c.priority="",p.value=1,y()},handleSizeChange:e=>{g.value=e,y()},handleCurrentChange:e=>{p.value=e,y()},getStatusType:e=>({pending:"info",in_progress:"warning",completed:"success",cancelled:"danger"}[e]||"info"),getStatusText:h,getPriorityType:e=>({low:"info",medium:"success",high:"warning",urgent:"danger"}[e]||"info"),getPriorityText:e=>({low:"低",medium:"中",high:"高",urgent:"紧急"}[e]||e),getProgressStatus:e=>{if("completed"===e.status)return"success";if("cancelled"===e.status)return"exception";if(e.dueDate){if(new Date>new Date(e.dueDate)&&e.progress<100)return"exception"}return""},formatProgressTooltip:e=>`${e}%`,handleRowClick:e=>{v(e)},viewTaskDetail:v,createTask:()=>{e.push({name:"TaskCreate"})},updateProgress:e=>{f.taskId=e.taskId,f.progress=e.progress||0,f.status=e.status,f.remarks="",m.value=!0},submitProgressUpdate:async()=>{if(f.taskId){k.value=!0;try{const e=await n.updateTaskProgress(f.taskId,{progress:f.progress,remarks:f.remarks});e.success?(u.success("任务进度更新成功"),m.value=!1,f.status!==e.data.status&&await n.updateTaskStatus(f.taskId,{status:f.status,remarks:`状态更新为: ${h(f.status)}`}),y()):u.error(e.message||"更新任务进度失败")}catch(e){u.error("更新任务进度时发生错误")}finally{k.value=!1}}},getAllAssignees:e=>{if(!e)return[];const a=[];return e.assignees&&Array.isArray(e.assignees)&&e.assignees.length>0?e.assignees.forEach((e=>{a.push({id:e.userId,name:e.userName||"未知用户",avatarUrl:e.avatarUrl||"",role:e.role||("Assignee"===e.assignmentType?"Primary":"Collaborator"),isPrimary:"Primary"===e.role||"Assignee"===e.assignmentType})})):e.assigneeUserId&&a.push({id:e.assigneeUserId,name:e.assigneeName||"未知用户",avatarUrl:e.assigneeAvatarUrl||"",role:"Primary",isPrimary:!0}),a}}}},[["render",function(e,l,r,o,n,u){const d=i("Refresh"),w=i("el-icon"),x=i("el-button"),z=i("Plus"),D=i("el-option"),I=i("el-select"),A=i("el-form-item"),R=i("el-form"),N=i("el-skeleton"),$=i("el-empty"),j=i("el-table-column"),E=i("el-tag"),L=i("el-progress"),M=i("UserAvatarStack"),B=i("el-table"),q=i("el-pagination"),G=i("el-card"),H=i("el-slider"),J=i("el-input"),K=i("el-dialog"),O=p("loading");return g(),a("div",b,[s(G,{class:"task-card"},{header:t((()=>[c("div",_,[l[9]||(l[9]=c("h2",null,"我的任务",-1)),c("div",C,[s(x,{type:"primary",onClick:o.refreshData},{default:t((()=>[s(w,null,{default:t((()=>[s(d)])),_:1}),l[7]||(l[7]=m(" 刷新 "))])),_:1},8,["onClick"]),s(x,{type:"success",onClick:o.createTask},{default:t((()=>[s(w,null,{default:t((()=>[s(z)])),_:1}),l[8]||(l[8]=m(" 新建任务 "))])),_:1},8,["onClick"])])])])),default:t((()=>[c("div",T,[s(R,{inline:!0,model:o.filterForm,class:"filter-form"},{default:t((()=>[s(A,{label:"状态"},{default:t((()=>[s(I,{modelValue:o.filterForm.status,"onUpdate:modelValue":l[0]||(l[0]=e=>o.filterForm.status=e),placeholder:"任务状态",clearable:""},{default:t((()=>[s(D,{label:"待处理",value:"pending"}),s(D,{label:"进行中",value:"in_progress"}),s(D,{label:"已完成",value:"completed"}),s(D,{label:"已取消",value:"cancelled"})])),_:1},8,["modelValue"])])),_:1}),s(A,{label:"优先级"},{default:t((()=>[s(I,{modelValue:o.filterForm.priority,"onUpdate:modelValue":l[1]||(l[1]=e=>o.filterForm.priority=e),placeholder:"优先级",clearable:""},{default:t((()=>[s(D,{label:"低",value:"low"}),s(D,{label:"中",value:"medium"}),s(D,{label:"高",value:"high"}),s(D,{label:"紧急",value:"urgent"})])),_:1},8,["modelValue"])])),_:1}),s(A,null,{default:t((()=>[s(x,{type:"primary",onClick:o.handleFilter},{default:t((()=>l[10]||(l[10]=[m("筛选")]))),_:1},8,["onClick"]),s(x,{onClick:o.resetFilter},{default:t((()=>l[11]||(l[11]=[m("重置")]))),_:1},8,["onClick"])])),_:1})])),_:1},8,["model"])]),o.loading?(g(),a("div",V,[s(N,{rows:10,animated:""})])):0===o.tasks.length?(g(),a("div",P,[s($,{description:"暂无任务数据"})])):(g(),a("div",F,[f((g(),k(B,{data:o.tasks,style:{width:"100%"},onRowClick:o.handleRowClick},{default:t((()=>[s(j,{prop:"taskId",label:"ID",width:"70"}),s(j,{prop:"title",label:"任务名称","min-width":"200","show-overflow-tooltip":""}),s(j,{prop:"status",label:"状态",width:"100"},{default:t((e=>[s(E,{type:o.getStatusType(e.row.status)},{default:t((()=>[m(y(o.getStatusText(e.row.status)),1)])),_:2},1032,["type"])])),_:1}),s(j,{prop:"priority",label:"优先级",width:"100"},{default:t((e=>[s(E,{type:o.getPriorityType(e.row.priority),effect:"dark"},{default:t((()=>[m(y(o.getPriorityText(e.row.priority)),1)])),_:2},1032,["type"])])),_:1}),s(j,{prop:"dueDate",label:"截止日期",width:"120"}),s(j,{prop:"progress",label:"进度",width:"180"},{default:t((e=>[s(L,{percentage:e.row.progress||0,status:o.getProgressStatus(e.row)},null,8,["percentage","status"])])),_:1}),s(j,{label:"负责人",width:"160"},{default:t((e=>[s(M,{users:o.getAllAssignees(e.row),"is-main-user-primary":!0,"max-users":4,"avatar-size":"18",overlap:6,class:"small"},null,8,["users"])])),_:1}),s(j,{prop:"creatorName",label:"创建人",width:"120"}),s(j,{label:"操作",width:"180",fixed:"right"},{default:t((e=>[s(x,{size:"small",type:"primary",onClick:v((a=>o.viewTaskDetail(e.row)),["stop"])},{default:t((()=>l[12]||(l[12]=[m(" 详情 ")]))),_:2},1032,["onClick"]),"completed"!==e.row.status?(g(),k(x,{key:0,size:"small",type:"success",onClick:v((a=>o.updateProgress(e.row)),["stop"])},{default:t((()=>l[13]||(l[13]=[m(" 更新进度 ")]))),_:2},1032,["onClick"])):h("",!0)])),_:1})])),_:1},8,["data","onRowClick"])),[[O,o.tableLoading]]),c("div",U,[s(q,{background:"",layout:"total, sizes, prev, pager, next","page-sizes":[10,20,50,100],total:o.total,"page-size":o.pageSize,"current-page":o.currentPage,onSizeChange:o.handleSizeChange,onCurrentChange:o.handleCurrentChange},null,8,["total","page-size","current-page","onSizeChange","onCurrentChange"])])]))])),_:1}),s(K,{modelValue:o.progressDialogVisible,"onUpdate:modelValue":l[6]||(l[6]=e=>o.progressDialogVisible=e),title:"更新任务进度",width:"500px","close-on-click-modal":!1},{footer:t((()=>[c("span",S,[s(x,{onClick:l[5]||(l[5]=e=>o.progressDialogVisible=!1)},{default:t((()=>l[14]||(l[14]=[m("取消")]))),_:1}),s(x,{type:"primary",onClick:o.submitProgressUpdate,loading:o.submitting},{default:t((()=>l[15]||(l[15]=[m(" 确认 ")]))),_:1},8,["onClick","loading"])])])),default:t((()=>[s(R,{model:o.progressForm,"label-width":"100px"},{default:t((()=>[s(A,{label:"当前进度"},{default:t((()=>[s(H,{modelValue:o.progressForm.progress,"onUpdate:modelValue":l[2]||(l[2]=e=>o.progressForm.progress=e),"format-tooltip":o.formatProgressTooltip,step:5,"show-stops":""},null,8,["modelValue","format-tooltip"])])),_:1}),s(A,{label:"状态"},{default:t((()=>[s(I,{modelValue:o.progressForm.status,"onUpdate:modelValue":l[3]||(l[3]=e=>o.progressForm.status=e),placeholder:"选择状态"},{default:t((()=>[s(D,{label:"待处理",value:"pending"}),s(D,{label:"进行中",value:"in_progress"}),s(D,{label:"已完成",value:"completed"})])),_:1},8,["modelValue"])])),_:1}),s(A,{label:"备注"},{default:t((()=>[s(J,{modelValue:o.progressForm.remarks,"onUpdate:modelValue":l[4]||(l[4]=e=>o.progressForm.remarks=e),type:"textarea",rows:3,placeholder:"请输入进度更新备注"},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue"])])}],["__scopeId","data-v-9ee85576"]]);export{x as default};
