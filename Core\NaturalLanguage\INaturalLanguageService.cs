// IT资产管理系统 - 自然语言处理服务接口
// 文件路径: /Core/NaturalLanguage/INaturalLanguageService.cs
// 功能: 提供自然语言处理服务，用于非技术人员的领域特定语言接口

using System.Collections.Generic;
using System.Threading.Tasks;

namespace ItAssetsSystem.Core.NaturalLanguage
{
    /// <summary>
    /// 命令处理结果
    /// </summary>
    public class CommandResult
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }
        
        /// <summary>
        /// 结果消息
        /// </summary>
        public string Message { get; set; }
        
        /// <summary>
        /// 结果数据
        /// </summary>
        public object Data { get; set; }
        
        /// <summary>
        /// 执行的命令
        /// </summary>
        public string Command { get; set; }
        
        /// <summary>
        /// 建议的后续操作
        /// </summary>
        public List<string> Suggestions { get; set; } = new List<string>();
    }
    
    /// <summary>
    /// 命令匹配结果
    /// </summary>
    public class CommandMatch
    {
        /// <summary>
        /// 命令类型
        /// </summary>
        public string CommandType { get; set; }
        
        /// <summary>
        /// 置信度
        /// </summary>
        public double Confidence { get; set; }
        
        /// <summary>
        /// 提取的参数
        /// </summary>
        public Dictionary<string, string> Parameters { get; set; } = new Dictionary<string, string>();
    }
    
    /// <summary>
    /// 自然语言处理服务接口
    /// </summary>
    public interface INaturalLanguageService
    {
        /// <summary>
        /// 处理自然语言命令
        /// </summary>
        /// <param name="input">用户输入的自然语言命令</param>
        /// <param name="userId">用户ID</param>
        /// <returns>命令处理结果</returns>
        Task<CommandResult> ProcessCommandAsync(string input, string userId);
        
        /// <summary>
        /// 获取常用命令示例
        /// </summary>
        /// <returns>命令示例列表</returns>
        Task<List<string>> GetCommandExamplesAsync();
        
        /// <summary>
        /// 获取最近的命令历史
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="count">数量</param>
        /// <returns>命令历史列表</returns>
        Task<List<string>> GetCommandHistoryAsync(string userId, int count = 10);
        
        /// <summary>
        /// 根据用户输入提供命令建议
        /// </summary>
        /// <param name="partialInput">部分输入</param>
        /// <param name="userId">用户ID</param>
        /// <returns>命令建议列表</returns>
        Task<List<string>> GetCommandSuggestionsAsync(string partialInput, string userId);
    }
} 