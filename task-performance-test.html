<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>任务查询性能测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #2196f3;
        }
        .test-button {
            background: #4caf50;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
            margin-bottom: 20px;
        }
        .test-button:hover {
            background: #45a049;
        }
        .test-button:disabled {
            background: #cccccc;
            cursor: not-allowed;
        }
        .results {
            background: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
            font-family: monospace;
            white-space: pre-wrap;
            min-height: 200px;
            border: 1px solid #ddd;
        }
        .success { color: #4caf50; }
        .warning { color: #ff9800; }
        .error { color: #f44336; }
        .info { color: #2196f3; }
    </style>
</head>
<body>
    <div class="container">
        <h1>任务查询性能测试</h1>
        
        <div class="test-info">
            <h3>测试目标</h3>
            <ul>
                <li><strong>原始问题:</strong> 查询时间 6000ms (6秒)</li>
                <li><strong>优化目标:</strong> 查询时间 &lt; 500ms (行业标准)</li>
                <li><strong>测试端点:</strong> /api/v2/tasks</li>
                <li><strong>优化措施:</strong> 消除双重分页、简化DTO映射、添加数据库索引</li>
            </ul>
        </div>
        
        <button id="testButton" class="test-button" onclick="runPerformanceTest()">
            开始性能测试
        </button>
        
        <div id="results" class="results">点击上方按钮开始测试...</div>
    </div>

    <script>
        let testRunning = false;
        
        function log(message, type = 'info') {
            const results = document.getElementById('results');
            const timestamp = new Date().toLocaleTimeString();
            const className = type;
            results.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            results.scrollTop = results.scrollHeight;
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        async function runPerformanceTest() {
            if (testRunning) return;
            
            testRunning = true;
            const button = document.getElementById('testButton');
            button.disabled = true;
            button.textContent = '测试进行中...';
            
            clearResults();
            
            log('开始任务查询性能测试', 'info');
            log('='.repeat(50), 'info');
            
            try {
                const baseUrl = 'http://localhost:5001';
                const endpoint = '/api/v2/tasks';
                
                // 测试参数
                const testParams = {
                    pageNumber: 1,
                    pageSize: 20,
                    status: '',
                    assigneeUserId: '',
                    creatorUserId: '',
                    assetId: '',
                    locationId: '',
                    priority: '',
                    taskType: '',
                    searchKeyword: ''
                };
                
                const queryString = new URLSearchParams(testParams).toString();
                const fullUrl = `${baseUrl}${endpoint}?${queryString}`;
                
                log(`测试URL: ${fullUrl}`, 'info');
                log('开始计时...', 'info');
                
                const startTime = performance.now();
                
                const response = await fetch(fullUrl, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    }
                });
                
                const endTime = performance.now();
                const duration = endTime - startTime;
                
                log(`响应状态: ${response.status}`, 'info');
                log(`查询耗时: ${duration.toFixed(2)}ms`, 'info');
                
                if (response.ok) {
                    const data = await response.json();
                    log(`返回数据条数: ${data.data?.length || 0}`, 'info');
                    log(`总记录数: ${data.totalCount || 0}`, 'info');
                    
                    // 性能评估
                    if (duration < 500) {
                        log('✅ 性能优秀: 查询时间 < 500ms', 'success');
                    } else if (duration < 1000) {
                        log('⚠️  性能一般: 查询时间 < 1000ms', 'warning');
                    } else if (duration < 3000) {
                        log('❌ 性能较差: 查询时间 < 3000ms', 'error');
                    } else {
                        log('💥 性能极差: 查询时间 > 3000ms', 'error');
                    }
                    
                    // 计算性能改进
                    const originalTime = 6000;
                    const improvement = ((originalTime - duration) / originalTime * 100).toFixed(1);
                    log(`性能改进: ${improvement}% (从 ${originalTime}ms 降至 ${duration.toFixed(2)}ms)`, 'success');
                    
                    // 显示部分返回数据
                    if (data.data && data.data.length > 0) {
                        log('', 'info');
                        log('返回数据示例:', 'info');
                        const sample = data.data[0];
                        log(`- 任务ID: ${sample.taskId}`, 'info');
                        log(`- 任务名称: ${sample.name}`, 'info');
                        log(`- 状态: ${sample.status} (${sample.statusName})`, 'info');
                        log(`- 优先级: ${sample.priority} (${sample.priorityText})`, 'info');
                    }
                    
                } else {
                    log(`请求失败: ${response.status} ${response.statusText}`, 'error');
                    const errorText = await response.text();
                    log(`错误详情: ${errorText}`, 'error');
                }
                
            } catch (error) {
                log(`测试过程中发生错误: ${error.message}`, 'error');
                
                if (error.message.includes('fetch') || error.message.includes('NetworkError')) {
                    log('提示: 请确保应用程序已启动在 http://localhost:5001', 'warning');
                    log('可以运行: dotnet run --urls "http://localhost:5001"', 'warning');
                }
            }
            
            log('='.repeat(50), 'info');
            log('测试完成', 'info');
            
            testRunning = false;
            button.disabled = false;
            button.textContent = '重新测试';
        }
    </script>
</body>
</html>
