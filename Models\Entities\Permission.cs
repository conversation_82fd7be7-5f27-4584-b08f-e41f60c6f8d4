// File: Models/Entities/Permission.cs
// Description: 权限实体类

#nullable enable

using System.Collections.Generic;

namespace ItAssetsSystem.Models.Entities
{
    /// <summary>
    /// 权限实体类
    /// </summary>
    public class Permission
    {
        /// <summary>
        /// 权限ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 权限名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 权限描述
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// 权限编码
        /// </summary>
        public string Code { get; set; } = string.Empty;

        /// <summary>
        /// 权限类型
        /// </summary>
        public string Type { get; set; } = string.Empty;

        /// <summary>
        /// 是否为系统权限
        /// </summary>
        public bool IsSystem { get; set; }

        /// <summary>
        /// 角色权限关联
        /// </summary>
        public virtual ICollection<RolePermission> RolePermissions { get; set; } = new List<RolePermission>();
    }
} 