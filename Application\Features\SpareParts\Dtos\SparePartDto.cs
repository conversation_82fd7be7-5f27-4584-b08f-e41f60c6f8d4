// File: Application/Features/SpareParts/Dtos/SparePartDto.cs
// Description: 备品备件数据传输对象

using System;

namespace ItAssetsSystem.Application.Features.SpareParts.Dtos
{
    /// <summary>
    /// 备品备件数据传输对象
    /// </summary>
    public class SparePartDto
    {
        /// <summary>
        /// 备件ID
        /// </summary>
        public long Id { get; set; }
        
        /// <summary>
        /// 备件编号
        /// </summary>
        public string Code { get; set; }

        /// <summary>
        /// 物料编号
        /// </summary>
        public string MaterialNumber { get; set; }

        /// <summary>
        /// 备件名称
        /// </summary>
        public string Name { get; set; }
        
        /// <summary>
        /// 备件类型ID
        /// </summary>
        public long TypeId { get; set; }
        
        /// <summary>
        /// 备件类型名称
        /// </summary>
        public string TypeName { get; set; }
        
        /// <summary>
        /// 规格型号
        /// </summary>
        public string Specification { get; set; }
        
        /// <summary>
        /// 品牌
        /// </summary>
        public string Brand { get; set; }
        
        /// <summary>
        /// 单位
        /// </summary>
        public string Unit { get; set; }
        
        /// <summary>
        /// 库存数量
        /// </summary>
        public int StockQuantity { get; set; }
        
        /// <summary>
        /// 安全库存预警阈值
        /// </summary>
        public int WarningThreshold { get; set; }
        
        /// <summary>
        /// 最小安全库存
        /// </summary>
        public int MinStock { get; set; }
        
        /// <summary>
        /// 库位ID
        /// </summary>
        public long? LocationId { get; set; }
        
        /// <summary>
        /// 库位名称
        /// </summary>
        public string LocationName { get; set; }
        
        /// <summary>
        /// 库位区域
        /// </summary>
        public string LocationArea { get; set; }
        
        /// <summary>
        /// 价格(元)
        /// </summary>
        public decimal? Price { get; set; }
        
        /// <summary>
        /// 备注
        /// </summary>
        public string Remarks { get; set; }
        
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }
        
        /// <summary>
        /// 最后修改时间
        /// </summary>
        public DateTime? UpdatedAt { get; set; }
        
        /// <summary>
        /// 是否低于库存预警阈值
        /// </summary>
        public bool IsLowWarningStock { get; set; }
        
        /// <summary>
        /// 是否低于最小安全库存
        /// </summary>
        public bool IsLowMinStock { get; set; }
    }
} 