using System.ComponentModel.DataAnnotations;

namespace ItAssetsSystem.Models.Dtos.Gamification
{
    /// <summary>
    /// 任务完成请求
    /// </summary>
    public class TaskCompleteRequest
    {
        /// <summary>
        /// 任务ID
        /// </summary>
        [Required]
        public long TaskId { get; set; }

        /// <summary>
        /// 是否按时完成
        /// </summary>
        public bool IsOnTime { get; set; } = false;
    }
}
