# IT资产管理系统API文档（完整版）

API文档规范 v2.2
最后校验时间：2024-03-24
代码版本：master

## 更新记录

### v2.2 (2024-03-24)
- 新增资产历史记录API
  - GET /api/Asset/{id}/history - 获取指定资产的历史记录
  - GET /api/Location/{id}/history - 获取指定位置的资产历史记录
- 增强位置管理API
  - 支持部门和负责人关联
  - 支持多用户关联
  - 新增位置树形结构查询

### v2.1 (2023-03-24)
- 初始版本发布
- 实现基础CRUD功能
- 支持资产、用户、任务等核心模块

## 全局配置

```diff
# Startup.cs 关键配置
+ 日志方案：Serilog（ConfigureServices@L77-L89）
+ 数据库连接：MySQL（ConfigureServices@L59-L104）
+ 插件系统：注册多个内置插件（ConfigureServices@L107-113）
- 未配置身份认证与授权系统
- 未配置全局速率限制
! 异常处理：使用中间件实现（Configure@L162-L195）
```

## 资产管理模块

▍模块状态：基本实现（需求覆盖率 85%）
▍代码锚点：/Controllers/AssetController.cs

### 1. 获取所有资产 [GET]
▍代码定位：AssetController.cs Line 37-149

```csharp
[HttpGet]
public async Task<IActionResult> GetAll()
```

▍端点路径：/api/Asset
▍方法类型：GET

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": [
    {
      "id": 1,
      "assetCode": "IT-PC-001",
      "name": "办公电脑",
      "assetTypeId": 1,
      "assetTypeName": "电脑设备",
      "serialNumber": "XPS15-9570-001",
      "model": "XPS 15",
      "brand": "Dell",
      "purchaseDate": "2022-03-24T04:04:30.8353554+08:00",
      "warrantyExpireDate": "2025-03-24T04:04:30.8354074+08:00",
      "price": 8999.00,
      "locationId": 1,
      "locationName": "研发部",
      "status": 1,
      "statusName": "在用",
      "notes": "研发部门使用",
      "createdAt": "2022-03-24T04:04:30.8354074+08:00",
      "updatedAt": "2023-02-24T04:04:30.8354076+08:00"
    }
  ]
}

// 错误响应（500）
{
  "success": false,
  "message": "获取资产列表出错: 数据库连接异常"
}
```

### 2. 根据ID获取资产 [GET]
▍代码定位：AssetController.cs Line 151-259

```csharp
[HttpGet("{id}")]
public async Task<IActionResult> GetById(int id)
```

▍端点路径：/api/Asset/{id}
▍方法类型：GET

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "id": 1,
    "assetCode": "IT-PC-001",
    "name": "办公电脑",
    "assetTypeId": 1,
    "assetTypeName": "电脑设备",
    "serialNumber": "XPS15-9570-001",
    "model": "XPS 15",
    "brand": "Dell",
    "purchaseDate": "2022-03-24T04:04:30.8353554+08:00",
    "warrantyExpireDate": "2025-03-24T04:04:30.8354074+08:00",
    "price": 8999.00,
    "locationId": 1,
    "locationName": "研发部",
    "status": 1,
    "statusName": "在用",
    "notes": "研发部门使用",
    "createdAt": "2022-03-24T04:04:30.8354074+08:00",
    "updatedAt": "2023-02-24T04:04:30.8354076+08:00"
  }
}

// 错误响应（404）
{
  "success": false,
  "message": "未找到ID为1的资产"
}
```

### 3. 创建资产 [POST]
▍代码定位：AssetController.cs Line 261-290

```csharp
[HttpPost]
public async Task<IActionResult> Create([FromBody] Asset asset)
```

▍端点路径：/api/Asset
▍方法类型：POST

‖ 请求结构 ‖

```json
{
  "name": "新办公电脑",
  "assetTypeId": 1,
  "serialNumber": "XPS15-9570-002",
  "model": "XPS 15",
  "brand": "Dell",
  "purchaseDate": "2023-03-20T00:00:00",
  "warrantyExpireDate": "2026-03-20T00:00:00",
  "price": 9999.00,
  "locationId": 1,
  "status": 0,
  "notes": "新购入，待分配"
}
```

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "id": 4,
    "assetCode": "IT-PC-004",
    "name": "新办公电脑",
    "assetTypeId": 1,
    "assetTypeName": "电脑设备",
    "serialNumber": "XPS15-9570-002",
    "model": "XPS 15",
    "brand": "Dell",
    "purchaseDate": "2023-03-20T00:00:00",
    "warrantyExpireDate": "2026-03-20T00:00:00",
    "price": 9999.00,
    "locationId": 1,
    "locationName": "研发部",
    "status": 0,
    "statusName": "闲置",
    "notes": "新购入，待分配",
    "createdAt": "2023-03-24T12:30:45.123456+08:00",
    "updatedAt": "2023-03-24T12:30:45.123456+08:00"
  },
  "message": "资产创建成功"
}

// 错误响应（400）
{
  "success": false,
  "message": "资产名称不能为空"
}

// 错误响应（400）
{
  "success": false,
  "message": "资产类型不存在"
}
```

### 4. 更新资产 [PUT]
▍代码定位：AssetController.cs Line 291-360

```csharp
[HttpPut("{id}")]
public async Task<IActionResult> Update(int id, [FromBody] Asset asset)
```

▍端点路径：/api/Asset/{id}
▍方法类型：PUT

‖ 请求结构 ‖

```json
{
  "name": "已分配办公电脑",
  "assetTypeId": 1,
  "serialNumber": "XPS15-9570-002",
  "model": "XPS 15",
  "brand": "Dell",
  "purchaseDate": "2023-03-20T00:00:00",
  "warrantyExpireDate": "2026-03-20T00:00:00",
  "price": 9999.00,
  "locationId": 2,
  "status": 1,
  "notes": "已分配给开发部"
}
```

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "id": 4,
    "assetCode": "IT-PC-004",
    "name": "已分配办公电脑",
    "assetTypeId": 1,
    "assetTypeName": "电脑设备",
    "serialNumber": "XPS15-9570-002",
    "model": "XPS 15",
    "brand": "Dell",
    "purchaseDate": "2023-03-20T00:00:00",
    "warrantyExpireDate": "2026-03-20T00:00:00",
    "price": 9999.00,
    "locationId": 2,
    "locationName": "开发部",
    "status": 1,
    "statusName": "在用",
    "notes": "已分配给开发部",
    "createdAt": "2023-03-24T12:30:45.123456+08:00",
    "updatedAt": "2023-03-24T12:45:30.654321+08:00"
  },
  "message": "资产更新成功"
}

// 错误响应（404）
{
  "success": false,
  "message": "未找到ID为4的资产"
}

// 错误响应（400）
{
  "success": false,
  "message": "资产类型不存在"
}
```

### 5. 删除资产 [DELETE]
▍代码定位：AssetController.cs（未在代码片段中显示）

```csharp
[HttpDelete("{id}")]
public async Task<IActionResult> DeleteAsset(int id)
```

▍端点路径：/api/Asset/{id}
▍方法类型：DELETE

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": true
}

// 错误响应（404）
{
  "success": false,
  "message": "未找到ID为4的资产"
}
```

### 6. 获取资产历史记录 [GET]
▍代码定位：AssetController.cs

```csharp
[HttpGet("{id}/history")]
public async Task<IActionResult> GetHistory(int id, int type = 0, DateTime? startTime = null, DateTime? endTime = null)
```

▍端点路径：/api/Asset/{id}/history
▍方法类型：GET

‖ 请求参数 ‖
- id: 资产ID
- type: 历史记录类型（0:全部, 1:位置变更, 2:状态变更）
- startTime: 开始时间（可选）
- endTime: 结束时间（可选）

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "asset": {
      "id": 1,
      "assetCode": "IT-PC-001",
      "name": "办公电脑"
    },
    "history": [
      {
        "id": 1,
        "changeType": 1,
        "changeTypeName": "位置变更",
        "oldLocationId": 1,
        "oldLocationName": "IT部",
        "newLocationId": 2,
        "newLocationName": "研发部",
        "operatorId": 1,
        "operatorName": "管理员",
        "reason": "部门调动",
        "notes": "员工岗位调动",
        "changeTime": "2024-03-20T10:30:00"
      }
    ]
  }
}
```

### 7. 获取位置资产历史记录 [GET]
▍代码定位：AssetController.cs

```csharp
[HttpGet("location/{locationId}/history")]
public async Task<IActionResult> GetLocationHistory(int locationId, DateTime? startTime = null, DateTime? endTime = null)
```

▍端点路径：/api/Asset/location/{locationId}/history
▍方法类型：GET

‖ 请求参数 ‖
- locationId: 位置ID
- startTime: 开始时间（可选）
- endTime: 结束时间（可选）

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "location": {
      "id": 1,
      "code": "IT-DEPT",
      "name": "IT部"
    },
    "history": [
      {
        "id": 1,
        "assetId": 1,
        "assetCode": "IT-PC-001",
        "assetName": "办公电脑",
        "isInbound": false,
        "targetLocationId": 2,
        "targetLocationName": "研发部",
        "operatorId": 1,
        "operatorName": "管理员",
        "reason": "部门调动",
        "notes": "员工岗位调动",
        "changeTime": "2024-03-20T10:30:00"
      }
    ]
  }
}
```

## 用户管理模块

▍模块状态：基本实现（需求覆盖率 75%）
▍代码锚点：/Controllers/UserController.cs

### 1. 获取所有用户 [GET]
▍代码定位：UserController.cs Line 37-77

```csharp
[HttpGet]
public async Task<IActionResult> GetAll()
```

▍端点路径：/api/User
▍方法类型：GET

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": [
    {
      "id": 1,
      "username": "admin",
      "name": "系统管理员",
      "position": "技术主管",
      "departmentId": 1,
      "departmentName": "信息技术部",
      "email": "<EMAIL>"
    },
    {
      "id": 2,
      "username": "zhangsan",
      "name": "张三",
      "position": "财务经理",
      "departmentId": 2,
      "departmentName": "财务部",
      "email": "<EMAIL>"
    }
  ]
}

// 错误响应（500）
{
  "success": false,
  "message": "获取用户列表出错: 数据库连接异常"
}
```

### 2. 根据ID获取用户 [GET]
▍代码定位：UserController.cs Line 79-148

```csharp
[HttpGet("{id}")]
public async Task<IActionResult> GetById(int id)
```

▍端点路径：/api/User/{id}
▍方法类型：GET

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "id": 1,
    "username": "admin",
    "name": "系统管理员",
    "position": "技术主管",
    "departmentId": 1,
    "departmentName": "信息技术部",
    "email": "<EMAIL>",
    "mobile": "13800138000",
    "gender": 1,
    "isActive": true,
    "roles": [
      {
        "id": 1,
        "name": "管理员"
      }
    ]
  }
}

// 错误响应（404）
{
  "success": false,
  "message": "未找到ID为1的用户"
}
```

### 3. 用户登录 [POST]
▍代码定位：UserController.cs Line 166-245

```csharp
[HttpPost("login")]
public async Task<IActionResult> Login([FromBody] LoginModel loginModel)
```

▍端点路径：/api/User/login
▍方法类型：POST

‖ 请求结构 ‖

```json
{
  "username": "admin",
  "password": "admin123"
}
```

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "token": "db_token_1a2b3c4d5e6f7g8h9i0j",
    "user": {
      "id": 1,
      "username": "admin",
      "name": "系统管理员",
      "roles": ["管理员"]
    }
  }
}

// 错误响应（401）
{
  "success": false,
  "message": "用户名或密码错误"
}
```

### 4. 添加新用户 [POST]
▍代码定位：UserController.cs Line 347-408

```csharp
[HttpPost]
public async Task<IActionResult> AddUser([FromBody] UserCreateModel userModel)
```

▍端点路径：/api/User
▍方法类型：POST

‖ 请求结构 ‖

```json
{
  "username": "newuser",
  "password": "password123",
  "name": "新用户",
  "departmentId": 1,
  "position": "开发工程师",
  "email": "<EMAIL>",
  "mobile": "13800138001",
  "gender": 1,
  "roles": [2]
}
```

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "id": 3,
    "username": "newuser",
    "name": "新用户",
    "position": "开发工程师",
    "departmentId": 1,
    "departmentName": "信息技术部",
    "email": "<EMAIL>",
    "mobile": "13800138001",
    "gender": 1,
    "isActive": true,
    "roles": [
      {
        "id": 2,
        "name": "普通用户"
      }
    ]
  }
}

// 错误响应（400）
{
  "success": false,
  "message": "添加用户失败: 用户名已存在"
}
```

### 5. 更新用户 [PUT]
▍代码定位：UserController.cs（未在代码片段中显示）

```csharp
[HttpPut("{id}")]
public async Task<IActionResult> UpdateUser(int id, [FromBody] UserUpdateModel model)
```

▍端点路径：/api/User/{id}
▍方法类型：PUT

‖ 请求结构 ‖

```json
{
  "name": "更新用户名",
  "position": "高级开发工程师",
  "email": "<EMAIL>",
  "mobile": "13900139001",
  "isActive": true,
  "roles": [2, 3]
}
```

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "id": 3,
    "username": "newuser",
    "name": "更新用户名",
    "position": "高级开发工程师",
    "departmentId": 1,
    "departmentName": "信息技术部",
    "email": "<EMAIL>",
    "mobile": "13900139001",
    "gender": 1,
    "isActive": true,
    "roles": [
      {
        "id": 2,
        "name": "普通用户"
      },
      {
        "id": 3,
        "name": "开发人员"
      }
    ]
  }
}

// 错误响应（404）
{
  "success": false,
  "message": "未找到ID为3的用户"
}
```

### 6. 删除用户 [DELETE]
▍代码定位：UserController.cs（未在代码片段中显示）

```csharp
[HttpDelete("{id}")]
public async Task<IActionResult> DeleteUser(int id)
```

▍端点路径：/api/User/{id}
▍方法类型：DELETE

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": true
}

// 错误响应（404）
{
  "success": false,
  "message": "未找到ID为3的用户"
}
```

## 任务管理模块

▍模块状态：基本实现（需求覆盖率 70%）
▍代码锚点：/Controllers/TaskController.cs

### 1. 获取所有任务 [GET]
▍代码定位：TaskController.cs（未在代码片段中显示）

```csharp
[HttpGet]
public async Task<IActionResult> GetAll()
```

▍端点路径：/api/Task
▍方法类型：GET

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": [
    {
      "id": 1,
      "title": "日常设备检查",
      "description": "检查所有服务器设备运行状态",
      "taskTypeId": 1,
      "taskTypeName": "日常维护",
      "status": 0,
      "statusName": "未开始",
      "priority": 1,
      "priorityName": "一般",
      "assigneeId": 1,
      "assigneeName": "系统管理员",
      "creatorId": 1,
      "creatorName": "系统管理员",
      "startDate": "2023-03-25T00:00:00",
      "dueDate": "2023-03-25T23:59:59",
      "completedDate": null,
      "createdAt": "2023-03-24T10:30:45",
      "updatedAt": "2023-03-24T10:30:45"
    }
  ]
}

// 错误响应（500）
{
  "success": false,
  "message": "获取任务列表出错: 数据库连接异常"
}
```

### 2. 根据ID获取任务 [GET]
▍代码定位：TaskController.cs（未在代码片段中显示）

```csharp
[HttpGet("{id}")]
public async Task<IActionResult> GetById(int id)
```

▍端点路径：/api/Task/{id}
▍方法类型：GET

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "id": 1,
    "title": "日常设备检查",
    "description": "检查所有服务器设备运行状态",
    "taskTypeId": 1,
    "taskTypeName": "日常维护",
    "status": 0,
    "statusName": "未开始",
    "priority": 1,
    "priorityName": "一般",
    "assigneeId": 1,
    "assigneeName": "系统管理员",
    "creatorId": 1,
    "creatorName": "系统管理员",
    "startDate": "2023-03-25T00:00:00",
    "dueDate": "2023-03-25T23:59:59",
    "completedDate": null,
    "createdAt": "2023-03-24T10:30:45",
    "updatedAt": "2023-03-24T10:30:45"
  }
}

// 错误响应（404）
{
  "success": false,
  "message": "未找到ID为1的任务"
}
```

### 3. 创建任务 [POST]
▍代码定位：TaskController.cs（未在代码片段中显示）

```csharp
[HttpPost]
public async Task<IActionResult> CreateTask([FromBody] TaskCreateModel model)
```

▍端点路径：/api/Task
▍方法类型：POST

‖ 请求结构 ‖

```json
{
  "title": "网络设备巡检",
  "description": "检查所有网络设备运行状态",
  "taskTypeId": 1,
  "status": 0,
  "priority": 1,
  "assigneeId": 1,
  "startDate": "2023-03-26T00:00:00",
  "dueDate": "2023-03-26T23:59:59"
}
```

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "id": 2,
    "title": "网络设备巡检",
    "description": "检查所有网络设备运行状态",
    "taskTypeId": 1,
    "taskTypeName": "日常维护",
    "status": 0,
    "statusName": "未开始",
    "priority": 1,
    "priorityName": "一般",
    "assigneeId": 1,
    "assigneeName": "系统管理员",
    "creatorId": 1,
    "creatorName": "系统管理员",
    "startDate": "2023-03-26T00:00:00",
    "dueDate": "2023-03-26T23:59:59",
    "completedDate": null,
    "createdAt": "2023-03-24T11:15:30",
    "updatedAt": "2023-03-24T11:15:30"
  }
}

// 错误响应（400）
{
  "success": false,
  "message": "创建任务失败: 截止日期不能早于开始日期"
}
```

### 4. 更新任务状态 [PUT]
▍代码定位：TaskController.cs（未在代码片段中显示）

```csharp
[HttpPut("{id}/status")]
public async Task<IActionResult> UpdateTaskStatus(int id, [FromBody] TaskStatusUpdateModel model)
```

▍端点路径：/api/Task/{id}/status
▍方法类型：PUT

‖ 请求结构 ‖

```json
{
  "status": 1,
  "comment": "已开始处理"
}
```

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "id": 2,
    "title": "网络设备巡检",
    "status": 1,
    "statusName": "进行中",
    "updatedAt": "2023-03-24T14:20:15"
  }
}

// 错误响应（400）
{
  "success": false,
  "message": "更新任务状态失败: 无效的状态值"
}
```

### 5. 完成任务 [POST]
▍代码定位：TaskController.cs（未在代码片段中显示）

```csharp
[HttpPost("{id}/complete")]
public async Task<IActionResult> CompleteTask(int id, [FromBody] TaskCompleteModel model)
```

▍端点路径：/api/Task/{id}/complete
▍方法类型：POST

‖ 请求结构 ‖

```json
{
  "comment": "任务已完成，所有设备正常"
}
```

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "id": 2,
    "title": "网络设备巡检",
    "status": 2,
    "statusName": "已完成",
    "completedDate": "2023-03-24T16:30:45",
    "updatedAt": "2023-03-24T16:30:45"
  }
}

// 错误响应（400）
{
  "success": false,
  "message": "完成任务失败: 任务已是完成状态"
}
```

## 故障管理模块

▍模块状态：基本实现（需求覆盖率 70%）
▍代码锚点：/Controllers/FaultController.cs

### 1. 获取所有故障记录 [GET]
▍代码定位：FaultController.cs（未在代码片段中显示）

```csharp
[HttpGet]
public async Task<IActionResult> GetAll()
```

▍端点路径：/api/Fault
▍方法类型：GET

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": [
    {
      "id": 1,
      "title": "服务器宕机",
      "description": "生产服务器突然无法访问",
      "faultTypeId": 1,
      "faultTypeName": "硬件故障",
      "priority": 2,
      "priorityName": "紧急",
      "status": 0,
      "statusName": "待处理",
      "assetId": 2,
      "assetCode": "IT-SERVER-001",
      "assetName": "应用服务器",
      "reporterId": 1,
      "reporterName": "系统管理员",
      "assigneeId": 1,
      "assigneeName": "系统管理员",
      "reportedAt": "2023-03-23T09:30:00",
      "resolvedAt": null,
      "createdAt": "2023-03-23T09:30:00",
      "updatedAt": "2023-03-23T09:30:00"
    }
  ]
}

// 错误响应（500）
{
  "success": false,
  "message": "获取故障记录列表出错: 数据库连接异常"
}
```

### 2. 根据ID获取故障记录 [GET]
▍代码定位：FaultController.cs（未在代码片段中显示）

```csharp
[HttpGet("{id}")]
public async Task<IActionResult> GetById(int id)
```

▍端点路径：/api/Fault/{id}
▍方法类型：GET

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "id": 1,
    "title": "服务器宕机",
    "description": "生产服务器突然无法访问",
    "faultTypeId": 1,
    "faultTypeName": "硬件故障",
    "priority": 2,
    "priorityName": "紧急",
    "status": 0,
    "statusName": "待处理",
    "assetId": 2,
    "assetCode": "IT-SERVER-001",
    "assetName": "应用服务器",
    "reporterId": 1,
    "reporterName": "系统管理员",
    "assigneeId": 1,
    "assigneeName": "系统管理员",
    "reportedAt": "2023-03-23T09:30:00",
    "resolvedAt": null,
    "solution": null,
    "createdAt": "2023-03-23T09:30:00",
    "updatedAt": "2023-03-23T09:30:00"
  }
}

// 错误响应（404）
{
  "success": false,
  "message": "未找到ID为1的故障记录"
}
```

### 3. 报告故障 [POST]
▍代码定位：FaultController.cs（未在代码片段中显示）

```csharp
[HttpPost]
public async Task<IActionResult> ReportFault([FromBody] FaultReportModel model)
```

▍端点路径：/api/Fault
▍方法类型：POST

‖ 请求结构 ‖

```json
{
  "title": "显示器黑屏",
  "description": "办公电脑显示器突然黑屏",
  "faultTypeId": 2,
  "priority": 1,
  "assetId": 1,
  "assigneeId": 1
}
```

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "id": 2,
    "title": "显示器黑屏",
    "description": "办公电脑显示器突然黑屏",
    "faultTypeId": 2,
    "faultTypeName": "硬件故障",
    "priority": 1,
    "priorityName": "一般",
    "status": 0,
    "statusName": "待处理",
    "assetId": 1,
    "assetCode": "IT-PC-001",
    "assetName": "办公电脑",
    "reporterId": 1,
    "reporterName": "系统管理员",
    "assigneeId": 1,
    "assigneeName": "系统管理员",
    "reportedAt": "2023-03-24T10:25:30",
    "resolvedAt": null,
    "solution": null,
    "createdAt": "2023-03-24T10:25:30",
    "updatedAt": "2023-03-24T10:25:30"
  }
}

// 错误响应（400）
{
  "success": false,
  "message": "报告故障失败: 资产ID不存在"
}
```

### 4. 更新故障状态 [PUT]
▍代码定位：FaultController.cs（未在代码片段中显示）

```csharp
[HttpPut("{id}/status")]
public async Task<IActionResult> UpdateFaultStatus(int id, [FromBody] FaultStatusUpdateModel model)
```

▍端点路径：/api/Fault/{id}/status
▍方法类型：PUT

‖ 请求结构 ‖

```json
{
  "status": 1,
  "comment": "技术人员正在处理"
}
```

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "id": 2,
    "title": "显示器黑屏",
    "status": 1,
    "statusName": "处理中",
    "updatedAt": "2023-03-24T11:15:20"
  }
}

// 错误响应（400）
{
  "success": false,
  "message": "更新故障状态失败: 无效的状态值"
}
```

### 5. 解决故障 [POST]
▍代码定位：FaultController.cs（未在代码片段中显示）

```csharp
[HttpPost("{id}/resolve")]
public async Task<IActionResult> ResolveFault(int id, [FromBody] FaultResolveModel model)
```

▍端点路径：/api/Fault/{id}/resolve
▍方法类型：POST

‖ 请求结构 ‖

```json
{
  "solution": "更换显示器电源适配器",
  "comment": "已更换新的电源适配器，问题已解决"
}
```

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "id": 2,
    "title": "显示器黑屏",
    "status": 2,
    "statusName": "已解决",
    "solution": "更换显示器电源适配器",
    "resolvedAt": "2023-03-24T15:30:00",
    "updatedAt": "2023-03-24T15:30:00"
  }
}

// 错误响应（400）
{
  "success": false,
  "message": "解决故障失败: 故障已是已解决状态"
}
```

## 采购管理模块

▍模块状态：基本实现（需求覆盖率 75%）
▍代码锚点：/Controllers/PurchaseController.cs

### 1. 获取所有采购单 [GET]
▍代码定位：PurchaseController.cs（未在代码片段中显示）

```csharp
[HttpGet]
public async Task<IActionResult> GetAll()
```

▍端点路径：/api/Purchase
▍方法类型：GET

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": [
    {
      "id": 1,
      "orderCode": "PO-2023-001",
      "title": "办公电脑采购",
      "description": "新增员工办公电脑",
      "status": 0,
      "statusName": "未到货",
      "totalAmount": 29997.00,
      "supplierId": 1,
      "supplierName": "戴尔电脑",
      "purchaserId": 1,
      "purchaserName": "系统管理员",
      "approverId": 2,
      "approverName": "张三",
      "expectedDeliveryDate": "2023-04-01T00:00:00",
      "actualDeliveryDate": null,
      "createdAt": "2023-03-20T14:30:00",
      "updatedAt": "2023-03-20T14:30:00"
    }
  ]
}

// 错误响应（500）
{
  "success": false,
  "message": "获取采购单列表出错: 数据库连接异常"
}
```

### 2. 根据ID获取采购单 [GET]
▍代码定位：PurchaseController.cs（未在代码片段中显示）

```csharp
[HttpGet("{id}")]
public async Task<IActionResult> GetById(int id)
```

▍端点路径：/api/Purchase/{id}
▍方法类型：GET

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "id": 1,
    "orderCode": "PO-2023-001",
    "title": "办公电脑采购",
    "description": "新增员工办公电脑",
    "status": 0,
    "statusName": "未到货",
    "totalAmount": 29997.00,
    "supplierId": 1,
    "supplierName": "戴尔电脑",
    "purchaserId": 1,
    "purchaserName": "系统管理员",
    "approverId": 2,
    "approverName": "张三",
    "expectedDeliveryDate": "2023-04-01T00:00:00",
    "actualDeliveryDate": null,
    "items": [
      {
        "id": 1,
        "purchaseOrderId": 1,
        "name": "XPS 15笔记本电脑",
        "quantity": 3,
        "unitPrice": 9999.00,
        "totalPrice": 29997.00,
        "description": "i7处理器, 16GB内存, 512GB SSD",
        "assetTypeId": 1,
        "assetTypeName": "电脑设备"
      }
    ],
    "createdAt": "2023-03-20T14:30:00",

    "updatedAt": "2023-03-20T14:30:00"
  }
}

// 错误响应（404）
{
  "success": false,
  "message": "未找到ID为1的采购单"
}
```

### 3. 创建采购单 [POST]
▍代码定位：PurchaseController.cs（未在代码片段中显示）

```csharp
[HttpPost]
public async Task<IActionResult> CreatePurchase([FromBody] PurchaseCreateModel model)
```

▍端点路径：/api/Purchase
▍方法类型：POST

‖ 请求结构 ‖

```json
{
  "title": "网络设备采购",
  "description": "办公室网络升级设备",
  "supplierId": 2,
  "approverId": 2,
  "expectedDeliveryDate": "2023-04-15T00:00:00",
  "items": [
    {
      "name": "千兆交换机",
      "quantity": 2,
      "unitPrice": 2999.00,
      "description": "24口千兆交换机",
      "assetTypeId": 3
    },
    {
      "name": "无线AP",
      "quantity": 5,
      "unitPrice": 899.00,
      "description": "双频无线AP",
      "assetTypeId": 3
    }
  ]
}
```

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "id": 2,
    "orderCode": "PO-2023-002",
    "title": "网络设备采购",
    "description": "办公室网络升级设备",
    "status": 0,
    "statusName": "未到货",
    "totalAmount": 10493.00,
    "supplierId": 2,
    "supplierName": "思科网络",
    "purchaserId": 1,
    "purchaserName": "系统管理员",
    "approverId": 2,
    "approverName": "张三",
    "expectedDeliveryDate": "2023-04-15T00:00:00",
    "actualDeliveryDate": null,
    "items": [
      {
        "id": 2,
        "purchaseOrderId": 2,
        "name": "千兆交换机",
        "quantity": 2,
        "unitPrice": 2999.00,
        "totalPrice": 5998.00,
        "description": "24口千兆交换机",
        "assetTypeId": 3,
        "assetTypeName": "网络设备"
      },
      {
        "id": 3,
        "purchaseOrderId": 2,
        "name": "无线AP",
        "quantity": 5,
        "unitPrice": 899.00,
        "totalPrice": 4495.00,
        "description": "双频无线AP",
        "assetTypeId": 3,
        "assetTypeName": "网络设备"
      }
    ],
    "createdAt": "2023-03-24T09:45:30",
    "updatedAt": "2023-03-24T09:45:30"
  }
}

// 错误响应（400）
{
  "success": false,
  "message": "创建采购单失败: 必须至少包含一个采购项"
}
```

### 4. 更新采购单状态 [PUT]
▍代码定位：PurchaseController.cs（未在代码片段中显示）

```csharp
[HttpPut("{id}/status")]
public async Task<IActionResult> UpdatePurchaseStatus(int id, [FromBody] PurchaseStatusUpdateModel model)
```

▍端点路径：/api/Purchase/{id}/status
▍方法类型：PUT

‖ 请求结构 ‖

```json
{
  "status": 1,
  "actualDeliveryDate": "2023-03-30T14:30:00",
  "comment": "物品已到货，正在验收"
}
```

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "id": 1,
    "orderCode": "PO-2023-001",
    "title": "办公电脑采购",
    "status": 1,
    "statusName": "已到货",
    "actualDeliveryDate": "2023-03-30T14:30:00",
    "updatedAt": "2023-03-30T14:35:20"
  }
}

// 错误响应（400）
{
  "success": false,
  "message": "更新采购单状态失败: 无效的状态值"
}
```

### 5. 将采购单转为资产 [POST]
▍代码定位：PurchaseController.cs（未在代码片段中显示）

```csharp
[HttpPost("{id}/convert-to-assets")]
public async Task<IActionResult> ConvertToAssets(int id, [FromBody] ConvertToAssetsModel model)
```

▍端点路径：/api/Purchase/{id}/convert-to-assets
▍方法类型：POST

‖ 请求结构 ‖

```json
{
  "locationId": 1,
  "comment": "将到货物品登记为资产",
  "items": [
    {
      "purchaseItemId": 1,
      "serialNumbers": ["XPS15-9570-001", "XPS15-9570-002", "XPS15-9570-003"]
    }
  ]
}
```

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "purchaseOrderId": 1,
    "createdAssets": [
      {
        "id": 4,
        "assetCode": "IT-PC-004",
        "name": "XPS 15笔记本电脑",
        "assetTypeId": 1,
        "serialNumber": "XPS15-9570-001"
      },
      {
        "id": 5,
        "assetCode": "IT-PC-005",
        "name": "XPS 15笔记本电脑",
        "assetTypeId": 1,
        "serialNumber": "XPS15-9570-002"
      },
      {
        "id": 6,
        "assetCode": "IT-PC-006",
        "name": "XPS 15笔记本电脑",
        "assetTypeId": 1,
        "serialNumber": "XPS15-9570-003"
      }
    ]
  }
}

// 错误响应（400）
{
  "success": false,
  "message": "转换采购单为资产失败: 采购单尚未到货"
}
```

## 插件管理模块

▍模块状态：基本实现（需求覆盖率 90%）
▍代码锚点：/Controllers/PluginController.cs

### 1. 获取所有插件 [GET]
▍代码定位：PluginController.cs（未在代码片段中显示）

```csharp
[HttpGet]
public async Task<IActionResult> GetAll()
```

▍端点路径：/api/Plugin
▍方法类型：GET

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": [
    {
      "id": "TaskManagementPlugin",
      "name": "任务管理插件",
      "description": "提供任务管理功能",
      "version": "1.0.0",
      "enabled": true,
      "isBuiltIn": true,
      "author": "系统",
      "createdAt": "2023-03-22T00:00:00",
      "lastUpdatedAt": "2023-03-22T00:00:00"
    },
    {
      "id": "AuditLogPlugin",
      "name": "审计日志插件",
      "description": "记录系统操作日志",
      "version": "1.0.0",
      "enabled": true,
      "isBuiltIn": true,
      "author": "系统",
      "createdAt": "2023-03-22T00:00:00",
      "lastUpdatedAt": "2023-03-22T00:00:00"
    },
    {
      "id": "FaultManagementPlugin",
      "name": "故障管理插件",
      "description": "提供故障报修和处理功能",
      "version": "1.0.0",
      "enabled": true,
      "isBuiltIn": true,
      "author": "系统",
      "createdAt": "2023-03-22T00:00:00",
      "lastUpdatedAt": "2023-03-22T00:00:00"
    },
    {
      "id": "PurchaseManagementPlugin",
      "name": "采购管理插件",
      "description": "提供采购管理功能",
      "version": "1.0.0",
      "enabled": true,
      "isBuiltIn": true,
      "author": "系统",
      "createdAt": "2023-03-22T00:00:00",
      "lastUpdatedAt": "2023-03-22T00:00:00"
    }
  ]
}

// 错误响应（500）
{
  "success": false,
  "message": "获取插件列表出错: 插件管理器初始化失败"
}
```

### 2. 根据ID获取插件 [GET]
▍代码定位：PluginController.cs（未在代码片段中显示）

```csharp
[HttpGet("{id}")]
public async Task<IActionResult> GetById(string id)
```

▍端点路径：/api/Plugin/{id}
▍方法类型：GET

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "id": "TaskManagementPlugin",
    "name": "任务管理插件",
    "description": "提供任务管理功能",
    "version": "1.0.0",
    "enabled": true,
    "isBuiltIn": true,
    "author": "系统",
    "settings": {
      "enableReminders": true,
      "reminderTimeMinutes": 30,
      "maxTasksPerUser": 50
    },
    "createdAt": "2023-03-22T00:00:00",
    "lastUpdatedAt": "2023-03-22T00:00:00"
  }
}

// 错误响应（404）
{
  "success": false,
  "message": "未找到ID为TaskManagerPlugin的插件"
}
```

### 3. 启用插件 [POST]
▍代码定位：PluginController.cs（未在代码片段中显示）

```csharp
[HttpPost("{id}/enable")]
public async Task<IActionResult> EnablePlugin(string id)
```

▍端点路径：/api/Plugin/{id}/enable
▍方法类型：POST

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "id": "TaskManagementPlugin",
    "name": "任务管理插件",
    "enabled": true,
    "lastUpdatedAt": "2023-03-24T10:20:15"
  }
}

// 错误响应（400）
{
  "success": false,
  "message": "启用插件失败: 插件已处于启用状态"
}
```

### 4. 禁用插件 [POST]
▍代码定位：PluginController.cs（未在代码片段中显示）

```csharp
[HttpPost("{id}/disable")]
public async Task<IActionResult> DisablePlugin(string id)
```

▍端点路径：/api/Plugin/{id}/disable
▍方法类型：POST

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "id": "TaskManagementPlugin",
    "name": "任务管理插件",
    "enabled": false,
    "lastUpdatedAt": "2023-03-24T10:25:30"
  }
}

// 错误响应（400）
{
  "success": false,
  "message": "禁用插件失败: 内置插件不能被禁用"
}
```

### 5. 更新插件设置 [PUT]
▍代码定位：PluginController.cs（未在代码片段中显示）

```csharp
[HttpPut("{id}/settings")]
public async Task<IActionResult> UpdatePluginSettings(string id, [FromBody] Dictionary<string, object> settings)
```

▍端点路径：/api/Plugin/{id}/settings
▍方法类型：PUT

‖ 请求结构 ‖

```json
{
  "enableReminders": false,
  "reminderTimeMinutes": 60,
  "maxTasksPerUser": 100
}
```

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "id": "TaskManagementPlugin",
    "name": "任务管理插件",
    "settings": {
      "enableReminders": false,
      "reminderTimeMinutes": 60,
      "maxTasksPerUser": 100
    },
    "lastUpdatedAt": "2023-03-24T10:30:45"
  }
}

// 错误响应（400）
{
  "success": false,
  "message": "更新插件设置失败: 包含无效的设置项"
}
```

## 离线操作管理模块

▍模块状态：基本实现（需求覆盖率 95%）
▍代码锚点：/Controllers/OfflineOperationController.cs

### 1. 获取所有离线操作 [GET]
▍代码定位：OfflineOperationController.cs（未在代码片段中显示）

```csharp
[HttpGet]
public async Task<IActionResult> GetAll()
```

▍端点路径：/api/OfflineOperation
▍方法类型：GET

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": [
    {
      "id": "op-1a2b3c",
      "operationType": "AssetCreate",
      "status": 0,
      "statusName": "待处理",
      "data": {
        "name": "新办公电脑",
        "assetTypeId": 1
      },
      "error": null,
      "retryCount": 0,
      "createdAt": "2023-03-23T15:30:45",
      "lastUpdatedAt": "2023-03-23T15:30:45"
    },
    {
      "id": "op-4d5e6f",
      "operationType": "UserUpdate",
      "status": 1,
      "statusName": "处理中",
      "data": {
        "id": 3,
        "name": "更新用户名"
      },
      "error": null,
      "retryCount": 0,
      "createdAt": "2023-03-23T15:45:20",
      "lastUpdatedAt": "2023-03-23T15:45:30"
    }
  ]
}

// 错误响应（500）
{
  "success": false,
  "message": "获取离线操作列表出错: 离线操作管理器未初始化"
}
```

### 2. 根据ID获取离线操作 [GET]
▍代码定位：OfflineOperationController.cs（未在代码片段中显示）

```csharp
[HttpGet("{id}")]
public async Task<IActionResult> GetById(string id)
```

▍端点路径：/api/OfflineOperation/{id}
▍方法类型：GET

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "id": "op-1a2b3c",
    "operationType": "AssetCreate",
    "status": 0,
    "statusName": "待处理",
    "data": {
      "name": "新办公电脑",
      "assetTypeId": 1,
      "serialNumber": "XPS15-9570-002",
      "model": "XPS 15",
      "brand": "Dell",
      "purchaseDate": "2023-03-20T00:00:00",
      "warrantyExpireDate": "2026-03-20T00:00:00",
      "price": 9999.00,
      "locationId": 1,
      "status": 0,
      "notes": "新购入，待分配"
    },
    "error": null,
    "retryCount": 0,
    "createdAt": "2023-03-23T15:30:45",
    "lastUpdatedAt": "2023-03-23T15:30:45"
  }
}

// 错误响应（404）
{
  "success": false,
  "message": "未找到ID为op-1a2b3c的离线操作"
}
```

### 3. 处理离线操作 [POST]
▍代码定位：OfflineOperationController.cs（未在代码片段中显示）

```csharp
[HttpPost("{id}/process")]
public async Task<IActionResult> ProcessOperation(string id)
```

▍端点路径：/api/OfflineOperation/{id}/process
▍方法类型：POST

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "id": "op-1a2b3c",
    "operationType": "AssetCreate",
    "status": 2,
    "statusName": "已完成",
    "result": {
      "id": 4,
      "assetCode": "IT-PC-004",
      "name": "新办公电脑"
    },
    "lastUpdatedAt": "2023-03-24T10:15:30"
  }
}

// 错误响应（400）
{
  "success": false,
  "message": "处理离线操作失败: 当前网络不可用"
}
```

### 4. 清除完成的离线操作 [DELETE]
▍代码定位：OfflineOperationController.cs（未在代码片段中显示）

```csharp
[HttpDelete("completed")]
public async Task<IActionResult> ClearCompletedOperations()
```

▍端点路径：/api/OfflineOperation/completed
▍方法类型：DELETE

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "clearedCount": 5,
    "remainingCount": 2
  }
}

// 错误响应（500）
{
  "success": false,
  "message": "清除已完成的离线操作失败: 存储访问异常"
}
```

## 健康检查模块

▍模块状态：基本实现（需求覆盖率 100%）
▍代码锚点：/Controllers/HealthController.cs

### 1. 获取系统状态 [GET]
▍代码定位：HealthController.cs（未在代码片段中显示）

```csharp
[HttpGet]
public IActionResult Status()
```

▍端点路径：/api/Health
▍方法类型：GET

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "status": "healthy",
    "version": "1.0.0",
    "uptime": "3d 5h 45m 30s",
    "systemTime": "2023-03-24T14:30:45",
    "environment": "Production",
    "components": [
      {
        "name": "Database",
        "status": "healthy",
        "details": "Connection active"
      },
      {
        "name": "FileSystem",
        "status": "healthy",
        "details": "Available space: 120GB"
      },
      {
        "name": "NetworkMonitor",
        "status": "healthy",
        "details": "Network connection available"
      }
    ]
  }
}
```

### 2. 检查数据库连接 [GET]
▍代码定位：HealthController.cs（未在代码片段中显示）

```csharp
[HttpGet("database")]
public async Task<IActionResult> CheckDatabase()
```

▍端点路径：/api/Health/database
▍方法类型：GET

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "status": "connected",
    "server": "localhost",
    "database": "itassets",
    "responseTimeMs": 15,
    "version": "MySQL 8.0.28"
  }
}

// 错误响应（503）
{
  "success": false,
  "message": "数据库连接失败: 无法连接到服务器",
  "details": {
    "status": "disconnected",
    "server": "localhost",
    "errorCode": "ECONNREFUSED"
  }
}
```

## 公共API特性

‖ 响应格式 ‖

所有API遵循统一的响应格式：

```json
// 成功响应
{
  "success": true,
  "data": {/* 返回数据 */}
}

// 错误响应
{
  "success": false,
  "message": "错误信息"
}
```

‖ 错误处理 ‖

所有控制器方法都采用try-catch包装，统一处理异常：

```csharp
try {
    // 业务逻辑
    return Ok(new { success = true, data = result });
}
catch (Exception ex) {
    _logger.LogError(ex, "错误信息");
    return StatusCode(500, new { success = false, message = "错误信息: " + ex.Message });
}
```

‖ 韧性设计 ‖

大部分API实现了数据库连接检测和降级处理：

```csharp
bool isDatabaseAvailable = await CheckDatabaseConnectionAsync();
if (!isDatabaseAvailable) {
    // 返回模拟数据
}
```

## ▍数据模型映射

| 实体类 | 数据库表 | 对应关系 |
|--------|---------|---------|
| User.cs | users | 完全映射 |
| Asset.cs | assets | 完全映射 |
| Task.cs | tasks | 完全映射 |
| FaultRecord.cs | faultrecords | 完全映射 |
| PurchaseOrder.cs | purchaseorders | 完全映射 |
| Location.cs | locations | 完全映射 |

## 附录

### A. 未解决问题
- 所有API端点缺少[ProducesResponseType]标注
- 未实现身份认证和授权管理
- 请求模型缺少数据验证特性
- 缺少API版本控制

### B. 自动化校验报告
| 检查项 | 通过率 | 严重问题 |
|-------|--------|----------|
| 参数校验一致性 | 15% | 大部分请求缺少参数验证 |
| 响应模型匹配 | 80% | 部分接口返回的数据结构不一致 |
| 安全头配置 | 0% | 未配置安全头(HTTPS,CORS,CSP) |
| 韧性实现 | 85% | 大部分接口实现了数据库不可用时的降级方案 |




## 位置管理模块

▍模块状态：完整实现（需求覆盖率 95%）
▍代码锚点：/Controllers/LocationController.cs

### 1. 获取位置树 [GET]
▍代码定位：LocationController.cs

```csharp
[HttpGet("tree")]
public async Task<IActionResult> GetTree()
```

▍端点路径：/api/Location/tree
▍方法类型：GET

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": [
    {
      "id": 1,
      "code": "HQ",
      "name": "总部",
      "type": 0,
      "parentId": null,
      "path": "0",
      "description": "公司总部",
      "isActive": true,
      "children": [
        {
          "id": 2,
          "code": "F001",
          "name": "A工厂",
          "type": 1,
          "parentId": 1,
          "path": "0,1",
          "description": "A工厂",
          "isActive": true,
          "children": []
        }
      ]
    }
  ]
}
```

### 2. 获取位置列表 [GET]
▍代码定位：LocationController.cs

```csharp
[HttpGet("list")]
public async Task<IActionResult> GetList()
```

▍端点路径：/api/Location/list
▍方法类型：GET

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": [
    {
      "id": 1,
      "code": "HQ",
      "name": "总部",
      "type": 0,
      "parentId": null,
      "path": "0",
      "description": "公司总部",
      "isActive": true
    },
    {
      "id": 2,
      "code": "F001",
      "name": "A工厂",
      "type": 1,
      "parentId": 1,
      "path": "0,1",
      "description": "A工厂",
      "isActive": true
    }
  ],
  "count": 2,
  "message": "获取位置列表成功"
}
```

### 3. 初始化根位置 [POST]
▍代码定位：LocationController.cs

```csharp
[HttpPost("init-root")]
public async Task<IActionResult> InitializeRootLocation()
```

▍端点路径：/api/Location/init-root
▍方法类型：POST

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "message": "根位置创建成功",
  "data": {
    "id": 1,
    "code": "HQ",
    "name": "总部"
  }
}

// 错误响应（400）
{
  "success": false,
  "message": "根位置已存在"
}
```

### 4. 创建位置 [POST]
▍代码定位：LocationController.cs

```csharp
[HttpPost]
public async Task<IActionResult> Create([FromBody] Location location)
```

▍端点路径：/api/Location
▍方法类型：POST

‖ 请求结构 ‖

```json
{
  "name": "A工厂",
  "type": 1,
  "parentId": 1,
  "description": "A工厂",
  "defaultDepartmentId": 1,
  "managerId": 1,
  "isActive": true,
  "locationUsers": [
    {
      "userId": 1,
      "userType": 0
    }
  ]
}
```

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "id": 2,
    "code": "F001",
    "name": "A工厂",
    "type": 1,
    "parentId": 1,
    "path": "0,1",
    "description": "A工厂",
    "defaultDepartmentId": 1,
    "managerId": 1,
    "isActive": true,
    "createdAt": "2024-03-24T10:30:00",
    "updatedAt": "2024-03-24T10:30:00"
  },
  "message": "位置创建成功"
}
```

### 5. 更新位置 [PUT]
▍代码定位：LocationController.cs

```csharp
[HttpPut("{id}")]
public async Task<IActionResult> Update(int id, [FromBody] Location location)
```

▍端点路径：/api/Location/{id}
▍方法类型：PUT

‖ 请求结构 ‖

```json
{
  "name": "A工厂-更新",
  "description": "A工厂-更新描述",
  "defaultDepartmentId": 2,
  "managerId": 2,
  "isActive": true,
  "locationUsers": [
    {
      "userId": 2,
      "userType": 0
    }
  ]
}
```

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "id": 2,
    "code": "F001",
    "name": "A工厂-更新",
    "type": 1,
    "parentId": 1,
    "path": "0,1",
    "description": "A工厂-更新描述",
    "defaultDepartmentId": 2,
    "managerId": 2,
    "isActive": true,
    "sortOrder": 0,
    "createdAt": "2024-03-24T10:30:00",
    "updatedAt": "2024-03-24T10:35:00"
  },
  "message": "位置更新成功"
}
```

### 6. 删除位置 [DELETE]
▍代码定位：LocationController.cs

```csharp
[HttpDelete("{id}")]
public async Task<IActionResult> Delete(int id)
```

▍端点路径：/api/Location/{id}
▍方法类型：DELETE

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "message": "位置删除成功"
}

// 错误响应（400）
{
  "success": false,
  "message": "该位置下有子位置，不能删除"
}

// 错误响应（400）
{
  "success": false,
  "message": "该位置下有关联的资产，不能删除"
}
```

### 7. 获取位置资产历史记录 [GET]
▍代码定位：AssetController.cs

```csharp
[HttpGet("location/{locationId}/history")]
public async Task<IActionResult> GetLocationHistory(int locationId, DateTime? startTime = null, DateTime? endTime = null)
```

▍端点路径：/api/Asset/location/{locationId}/history
▍方法类型：GET

‖ 请求参数 ‖
- locationId: 位置ID
- startTime: 开始时间（可选）
- endTime: 结束时间（可选）

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "location": {
      "id": 1,
      "code": "IT-DEPT",
      "name": "IT部"
    },
    "history": [
      {
        "id": 1,
        "assetId": 1,
        "assetCode": "IT-PC-001",
        "assetName": "办公电脑",
        "isInbound": false,
        "targetLocationId": 2,
        "targetLocationName": "研发部",
        "operatorId": 1,
        "operatorName": "管理员",
        "reason": "部门调动",
        "notes": "员工岗位调动",
        "changeTime": "2024-03-20T10:30:00"
      }
    ]
  }
}
```

### 8. 获取位置用户关联 [GET]
▍代码定位：LocationController.cs

```csharp
[HttpGet("{id}/users")]
public async Task<IActionResult> GetLocationUsers(int id)
```

▍端点路径：/api/Location/{id}/users
▍方法类型：GET

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "location": {
      "id": 1,
      "code": "HQ-F1-R102",
      "name": "102办公室"
    },
    "users": [
      {
        "userId": 1,
        "userName": "李四",
        "userType": 0,
        "userTypeName": "使用人",
        "departmentName": "技术部",
        "position": "开发工程师",
        "isActive": true
      },
      {
        "userId": 2,
        "userName": "王五",
        "userType": 1,
        "userTypeName": "负责人",
        "departmentName": "技术部",
        "position": "技术主管",
        "isActive": true
      }
    ]
  }
}
```

### 9. 更新位置用户关联 [PUT]
▍代码定位：LocationController.cs

```csharp
[HttpPut("{id}/users")]
public async Task<IActionResult> UpdateLocationUsers(int id, [FromBody] List<LocationUser> users)
```

▍端点路径：/api/Location/{id}/users
▍方法类型：PUT

‖ 请求结构 ‖

```json
{
  "users": [
    {
      "userId": 1,
      "userType": 0,
      "isActive": true
    },
    {
      "userId": 2,
      "userType": 1,
      "isActive": true
    },
    {
      "userId": 3,
      "userType": 0,
      "isActive": false
    }
  ]
}
```

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "location": {
      "id": 1,
      "code": "HQ-F1-R102",
      "name": "102办公室"
    },
    "users": [
      {
        "userId": 1,
        "userName": "李四",
        "userType": 0,
        "userTypeName": "使用人",
        "departmentName": "技术部",
        "position": "开发工程师",
        "isActive": true
      },
      {
        "userId": 2,
        "userName": "王五",
        "userType": 1,
        "userTypeName": "负责人",
        "departmentName": "技术部",
        "position": "技术主管",
        "isActive": true
      },
      {
        "userId": 3,
        "userName": "赵六",
        "userType": 0,
        "userTypeName": "使用人",
        "departmentName": "技术部",
        "position": "测试工程师",
        "isActive": false
      }
    ],
    "message": "位置用户关联更新成功"
  }
}
```

### 10. 获取位置部门关联 [GET]
▍代码定位：LocationController.cs

```csharp
[HttpGet("{id}/department")]
public async Task<IActionResult> GetLocationDepartment(int id)
```

▍端点路径：/api/Location/{id}/department
▍方法类型：GET

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "location": {
      "id": 1,
      "code": "HQ-F1-R102",
      "name": "102办公室"
    },
    "department": {
      "id": 1,
      "code": "TECH",
      "name": "技术部",
      "managerId": 2,
      "managerName": "王五",
      "description": "负责公司技术研发"
    }
  }
}
```

### 11. 更新位置部门关联 [PUT]
▍代码定位：LocationController.cs

```csharp
[HttpPut("{id}/department")]
public async Task<IActionResult> UpdateLocationDepartment(int id, [FromBody] int departmentId)
```

▍端点路径：/api/Location/{id}/department
▍方法类型：PUT

‖ 请求结构 ‖

```json
{
  "departmentId": 1
}
```

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "location": {
      "id": 1,
      "code": "HQ-F1-R102",
      "name": "102办公室"
    },
    "department": {
      "id": 1,
      "code": "TECH",
      "name": "技术部",
      "managerId": 2,
      "managerName": "王五",
      "description": "负责公司技术研发"
    },
    "message": "位置部门关联更新成功"
  }
}
```

## 数据导入模块

▍模块状态：完整实现（需求覆盖率 90%）
▍代码锚点：/Api/Import/ImportDataEndpoint.cs

### 1. 获取支持的导入格式 [GET]
▍代码定位：ImportDataEndpoint.cs Line 47-67

```csharp
[HttpGet("formats")]
public ActionResult<List<string>> GetSupportedFormats()
```

▍端点路径：/api/import/formats
▍方法类型：GET

‖ 响应示例 ‖

```json
// 成功响应（200）
[
  "CSV",
  "XLSX",
  "JSON"
]
```

### 2. 获取导入模板 [GET]
▍代码定位：ImportDataEndpoint.cs Line 68-143

```csharp
[HttpGet("template/{entityType}/{formatStr}")]
public async Task<IActionResult> GetTemplateAsync(string entityType, string formatStr)
```

▍端点路径：/api/import/template/{entityType}/{formatStr}
▍方法类型：GET

‖ 参数 ‖

- entityType: 实体类型 (assets, locations, assettypes 等)
- formatStr: 导出格式 (CSV, XLSX, JSON)

‖ 响应 ‖

返回对应类型的模板文件

### 3. 导入数据 [POST]
▍代码定位：ImportDataEndpoint.cs Line 144-237

```csharp
[HttpPost]
public async Task<ActionResult<ImportResponse>> ImportAsync(IFormFile file, [FromQuery] string entityType)
```

▍端点路径：/api/import
▍方法类型：POST

‖ 请求参数 ‖

- file: 上传的文件
- entityType: 实体类型 (assets, locations, assettypes 等)

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "message": "导入成功，12条记录已导入",
  "totalRows": 12,
  "successCount": 12,
  "errorCount": 0,
  "errors": [],
  "successItems": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]
}

// 部分成功（200）
{
  "success": true,
  "message": "导入部分成功，10条记录已导入，2条记录失败",
  "totalRows": 12,
  "successCount": 10,
  "errorCount": 2,
  "errors": [
    "第3行: 资产编码重复",
    "第8行: 资产类型不存在"
  ],
  "successItems": [1, 2, 4, 5, 6, 7, 9, 10, 11, 12]
}
```

## 数据导出模块

▍模块状态：完整实现（需求覆盖率 90%）
▍代码锚点：/Api/Export/ExportDataEndpoint.cs

### 1. 导出数据 [POST]
▍代码定位：ExportDataEndpoint.cs

```csharp
[HttpPost]
public async Task<IActionResult> ExportDataAsync([FromBody] ExportDataRequest request)
```

▍端点路径：/api/export
▍方法类型：POST

‖ 请求结构 ‖

```json
{
  "entityType": "assets",
  "format": "XLSX",
  "filters": {
    "assetTypeId": 1,
    "status": 1
  },
  "includeColumns": ["assetCode", "name", "model", "serialNumber", "locationName"]
}
```

‖ 响应 ‖

返回对应格式的文件

## UI定义模块

▍模块状态：完整实现（需求覆盖率 100%）
▍代码锚点：/Api/UI/UiDefinitionEndpoint.cs

### 1. 获取UI定义列表 [GET]
▍代码定位：UiDefinitionEndpoint.cs Line 39-59

```csharp
[HttpGet]
[ProducesResponseType(StatusCodes.Status200OK)]
public async Task<IActionResult> GetAllAsync()
```

▍端点路径：/api/ui
▍方法类型：GET

‖ 响应示例 ‖

```json
// 成功响应（200）
[
  {
    "id": "asset-list",
    "name": "资产列表",
    "type": "list",
    "definition": {
      "columns": [
        { "key": "assetCode", "title": "资产编号", "width": 120 },
        { "key": "name", "title": "资产名称", "width": 150 },
        { "key": "assetTypeName", "title": "资产类型", "width": 120 },
        { "key": "statusName", "title": "状态", "width": 80 }
      ]
    },
    "isSystem": true,
    "createdAt": "2023-03-22T00:00:00"
  }
]
```

### 2. 获取指定UI定义 [GET]
▍代码定位：UiDefinitionEndpoint.cs Line 60-87

```csharp
[HttpGet("{id}")]
[ProducesResponseType(StatusCodes.Status200OK)]
[ProducesResponseType(StatusCodes.Status404NotFound)]
public async Task<IActionResult> GetByIdAsync(string id)
```

▍端点路径：/api/ui/{id}
▍方法类型：GET

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "id": "asset-list",
  "name": "资产列表",
  "type": "list",
  "definition": {
    "columns": [
      { "key": "assetCode", "title": "资产编号", "width": 120 },
      { "key": "name", "title": "资产名称", "width": 150 },
      { "key": "assetTypeName", "title": "资产类型", "width": 120 },
      { "key": "statusName", "title": "状态", "width": 80 }
    ]
  },
  "isSystem": true,
  "createdAt": "2023-03-22T00:00:00"
}
```

## 备份管理模块

▍模块状态：完整实现（需求覆盖率 100%）
▍代码锚点：/Api/Backup/

### 1. 创建手动备份 [POST]
▍代码定位：CreateManualBackupEndpoint.cs Line 41-74

```csharp
[HttpPost]
public async Task<IActionResult> CreateBackup([FromBody] CreateBackupRequest request)
```

▍端点路径：/api/backup/create
▍方法类型：POST

‖ 请求结构 ‖

```json
{
  "name": "上线前备份",
  "description": "系统上线前的完整数据备份"
}
```

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "message": "备份创建成功",
  "backupId": "backup-20230324-120530",
  "createdAt": "2023-03-24T12:05:30"
}
```

### 2. 获取备份历史 [GET]
▍代码定位：GetBackupHistoryEndpoint.cs

```csharp
[HttpGet]
public async Task<IActionResult> GetBackupHistoryAsync()
```

▍端点路径：/api/backup/history
▍方法类型：GET

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "backups": [
    {
      "id": "backup-20230324-120530",
      "name": "上线前备份",
      "description": "系统上线前的完整数据备份",
      "type": "Manual",
      "size": 15240000,
      "createdAt": "2023-03-24T12:05:30",
      "status": "Completed"
    },
    {
      "id": "backup-20230323-000000",
      "name": "每日自动备份",
      "description": "系统自动创建的每日备份",
      "type": "Auto",
      "size": 14980000,
      "createdAt": "2023-03-23T00:00:00",
      "status": "Completed"
    }
  ]
}
```

### 3. 恢复备份 [POST]
▍代码定位：RestoreBackupEndpoint.cs

```csharp
[HttpPost("{id}/restore")]
public async Task<IActionResult> RestoreBackupAsync(string id)
```

▍端点路径：/api/backup/{id}/restore
▍方法类型：POST

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "message": "备份恢复成功",
  "details": {
    "backupId": "backup-20230324-120530",
    "restoredAt": "2023-03-24T14:30:15",
    "tablesRestored": 22,
    "recordsRestored": 1250
  }
}
```

## 系统配置模块

▍模块状态：完整实现（需求覆盖率 95%）
▍代码锚点：/Api/Configuration/SystemConfigEndpoint.cs

### 1. 获取系统配置 [GET]
▍代码定位：SystemConfigEndpoint.cs

```csharp
[HttpGet]
public async Task<IActionResult> GetAllConfigsAsync()
```

▍端点路径：/api/config
▍方法类型：GET

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "system": {
    "siteName": "IT资产管理系统",
    "dateFormat": "yyyy-MM-dd",
    "timeFormat": "HH:mm:ss"
  },
  "asset": {
    "enableAutoCodeGeneration": true,
    "assetCodePrefix": "IT-",
    "assetCodePattern": "{prefix}{type}-{sequence:000}"
  },
  "backup": {
    "enableAutoBackup": true,
    "backupFrequencyHours": 24,
    "maxBackupsToKeep": 7
  }
}
```

### 2. 更新系统配置 [PUT]
▍代码定位：SystemConfigEndpoint.cs

```csharp
[HttpPut("{key}")]
public async Task<IActionResult> UpdateConfigAsync(string key, [FromBody] object value)
```

▍端点路径：/api/config/{key}
▍方法类型：PUT

‖ 请求结构 ‖

```json
{
  "enableAutoBackup": false,
  "backupFrequencyHours": 48,
  "maxBackupsToKeep": 5
}
```

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "message": "配置已更新",
  "key": "backup",
  "updatedAt": "2023-03-24T15:20:45"
}
```

## 自然语言处理模块

▍模块状态：基本实现（需求覆盖率 70%）
▍代码锚点：/Api/NaturalLanguage/NaturalLanguageEndpoint.cs

### 1. 自然语言查询 [POST]
▍代码定位：NaturalLanguageEndpoint.cs

```csharp
[HttpPost("query")]
public async Task<IActionResult> ProcessNaturalLanguageQuery([FromBody] NaturalLanguageRequest request)
```

▍端点路径：/api/nl/query
▍方法类型：POST

‖ 请求结构 ‖

```json
{
  "query": "找出所有在研发部的电脑",
  "maxResults": 20
}
```

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "intent": "ASSET_SEARCH",
  "interpretedQuery": {
    "filters": {
      "assetType": "电脑",
      "location": "研发部"
    }
  },
  "results": [
    {
      "id": 1,
      "assetCode": "IT-PC-001",
      "name": "办公电脑",
      "assetTypeName": "电脑设备",
      "locationName": "研发部"
    },
    {
      "id": 5,
      "assetCode": "IT-PC-005",
      "name": "开发笔记本",
      "assetTypeName": "电脑设备",
      "locationName": "研发部"
    }
  ],
  "totalResults": 2
}
```

## 附录

### A. 未解决问题
- Controllers目录下的API端点缺少[ProducesResponseType]标注
- Api目录下的API端点已经部分实现了[ProducesResponseType]标注
- 未实现身份认证和授权管理
- 请求模型在某些控制器中缺少数据验证特性
- 缺少API版本控制

### B. API架构对比

| 目录路径 | API实现方式 | 特点 |
|---------|-----------|------|
| /Controllers/ | 传统控制器 | 使用Controller基类，返回标准success/data格式 |
| /Api/ | 端点式API | 使用独立端点类，直接返回具体数据，更符合REPR原则 |

### C. 安全要求
由于系统未实现完整的身份认证和授权管理，建议在生产环境部署前实施以下安全措施：

1. 实现JWT或OAuth2认证
2. 为所有非公开API添加[Authorize]特性
3. 实现角色/权限控制
4. 添加请求速率限制
5. 实现CSRF防护
6. 启用HTTPS

## 3. 位置树管理

### 3.1 获取位置树
- 请求方法：`GET`
- 请求路径：`/api/LocationTree`
- 说明：
  - 返回完整的位置层级树结构
  - 按照位置编码排序
- 响应示例：
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "code": "FACTORY_A",
      "name": "工厂A",
      "type": 0,
      "description": "主工厂",
      "path": "0",
      "children": [
        {
          "id": 2,
          "code": "LINE_A1",
          "name": "产线A1",
          "type": 1,
          "description": "主产线",
          "path": "0,1",
          "children": [
            {
              "id": 3,
              "code": "PROCESS_A1_1",
              "name": "工序1",
              "type": 2,
              "description": "组装工序",
              "path": "0,1,2",
              "children": []
            }
          ]
        }
      ]
    }
  ]
}
```

### 3.2 搜索位置
- 请求方法：`GET`
- 请求路径：`/api/LocationSearch`
- 请求参数：
  - `keyword`: 搜索关键词（可选，支持位置编码、名称、描述）
  - `type`: 位置类型（可选，0:工厂，1:产线，2:工序，3:工位，4:设备位置）
  - `parentId`: 父级位置ID（可选）
  - `pageIndex`: 页码，从1开始（默认1）
  - `pageSize`: 每页大小（默认20，最大100）
- 响应示例：
```json
{
  "success": true,
  "data": {
    "total": 10,
    "items": [
      {
        "id": 1,
        "code": "FACTORY_A",
        "name": "工厂A",
        "type": 0,
        "typeName": "工厂",
        "parentId": null,
        "parentName": null,
        "path": "0",
        "description": "主工厂",
        "defaultDepartmentId": 1,
        "defaultDepartmentName": "生产部",
        "managerId": 1,
        "managerName": "张三",
        "isActive": true,
        "createdAt": "2024-03-23T12:00:00",
        "updatedAt": "2024-03-23T12:00:00"
      }
    ]
  }
}
```

## 4. 状态码说明

### 4.1 HTTP状态码
- 200: 请求成功
- 400: 请求参数错误
- 401: 未授权
- 403: 权限不足
- 404: 资源不存在
- 500: 服务器内部错误

### 4.2 业务状态码
- 位置类型（type）:
  - 0: 工厂
  - 1: 产线
  - 2: 工序
  - 3: 工位
  - 4: 设备位置

- 资产状态（status）:
  - 0: 闲置
  - 1: 在用
  - 2: 维修中
  - 3: 报废

- 位置变更类型（changeType）:
  - 0: 转移
  - 1: 领用
  - 2: 归还

## 5. 注意事项

1. 所有时间字段均使用ISO 8601格式（YYYY-MM-DDTHH:mm:ss）
2. 所有金额字段均使用decimal类型，保留2位小数
3. 分页参数pageIndex从1开始计数
4. API返回格式统一为：
```json
{
  "success": true|false,
  "data": object|null,
  "message": "string"
}
```
5. 位置编码（code）规则：
   - 工厂：FACTORY_xxx
   - 产线：LINE_xxx
   - 工序：PROCESS_xxx
   - 工位：WS_xxx
   - 设备位置：DEV_xxx
6. 如不指定位置编码，系统会自动生成，规则同上
7. 位置路径（path）使用逗号分隔的ID序列，如"0,1,2"表示根->工厂->产线
8. 删除位置时会进行以下检查：
   - 是否存在子位置
   - 是否存在关联资产
   - 是否有权限删除

## 资产类型管理模块

▍模块状态：基本实现（需求覆盖率 90%）
▍代码锚点：/Controllers/AssetTypeController.cs

### 1. 获取所有资产类型 [GET]
▍代码定位：AssetTypeController.cs Line 30-65

```csharp
[HttpGet]
public async Task<IActionResult> GetAll([FromQuery] int? parentId = null, [FromQuery] bool? isActive = null)
```

▍端点路径：/api/AssetType
▍方法类型：GET

‖ 查询参数 ‖
- parentId (可选): 父类型ID，获取指定父类型下的子类型
- isActive (可选): 是否激活

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "电脑设备",
      "code": "COMPUTER",
      "parentId": null,
      "description": "各类计算机设备",
      "isActive": true,
      "createdAt": "2023-03-24T10:25:30",
      "updatedAt": "2023-03-24T10:25:30"
    },
    {
      "id": 2,
      "name": "笔记本电脑",
      "code": "LAPTOP",
      "parentId": 1,
      "description": "便携式计算机",
      "isActive": true,
      "createdAt": "2023-03-24T10:26:30",
      "updatedAt": "2023-03-24T10:26:30"
    }
  ]
}

// 错误响应（500）
{
  "success": false,
  "message": "获取资产类型列表出错: 数据库连接异常"
}
```

### 2. 获取资产类型详情 [GET]
▍代码定位：AssetTypeController.cs Line 67-95

```csharp
[HttpGet("{id}")]
public async Task<IActionResult> GetById(int id)
```

▍端点路径：/api/AssetType/{id}
▍方法类型：GET

‖ 路径参数 ‖
- id: 资产类型ID

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "id": 1,
    "name": "电脑设备",
    "code": "COMPUTER",
    "parentId": null,
    "parent": null,
    "description": "各类计算机设备",
    "isActive": true,
    "createdAt": "2023-03-24T10:25:30",
    "updatedAt": "2023-03-24T10:25:30"
  }
}

// 错误响应（404）
{
  "success": false,
  "message": "未找到ID为1的资产类型"
}
```

### 3. 创建资产类型 [POST]
▍代码定位：AssetTypeController.cs Line 97-138

```csharp
[HttpPost]
public async Task<IActionResult> Create([FromBody] AssetType assetType)
```

▍端点路径：/api/AssetType
▍方法类型：POST

‖ 请求结构 ‖

```json
{
  "name": "服务器",
  "code": "SERVER",
  "parentId": 1,
  "description": "服务器设备",
  "isActive": true
}
```

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "id": 3,
    "name": "服务器",
    "code": "SERVER",
    "parentId": 1,
    "description": "服务器设备",
    "isActive": true,
    "createdAt": "2023-03-24T14:20:10",
    "updatedAt": "2023-03-24T14:20:10"
  },
  "message": "资产类型创建成功"
}

// 错误响应（400）
{
  "success": false,
  "message": "资产类型名称不能为空"
}

// 错误响应（400）
{
  "success": false,
  "message": "父类型不存在"
}
```

### 4. 更新资产类型 [PUT]
▍代码定位：AssetTypeController.cs Line 140-195

```csharp
[HttpPut("{id}")]
public async Task<IActionResult> Update(int id, [FromBody] AssetType assetType)
```

▍端点路径：/api/AssetType/{id}
▍方法类型：PUT

‖ 路径参数 ‖
- id: 资产类型ID

‖ 请求结构 ‖

```json
{
  "name": "服务器（更新）",
  "code": "SERVER-UPD",
  "parentId": 1,
  "description": "更新后的服务器设备描述",
  "isActive": true
}
```

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "id": 3,
    "name": "服务器（更新）",
    "code": "SERVER-UPD",
    "parentId": 1,
    "description": "更新后的服务器设备描述",
    "isActive": true,
    "createdAt": "2023-03-24T14:20:10",
    "updatedAt": "2023-03-24T15:30:20"
  },
  "message": "资产类型更新成功"
}

// 错误响应（404）
{
  "success": false,
  "message": "未找到ID为3的资产类型"
}

// 错误响应（400）
{
  "success": false,
  "message": "资产类型名称不能为空"
}
```

### 5. 删除资产类型 [DELETE]
▍代码定位：AssetTypeController.cs Line 197-242

```csharp
[HttpDelete("{id}")]
public async Task<IActionResult> Delete(int id)
```

▍端点路径：/api/AssetType/{id}
▍方法类型：DELETE

‖ 路径参数 ‖
- id: 资产类型ID

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "message": "资产类型删除成功"
}

// 错误响应（404）
{
  "success": false,
  "message": "未找到ID为3的资产类型"
}

// 错误响应（400）
{
  "success": false,
  "message": "该资产类型下有子类型，不能删除"
}

// 错误响应（400）
{
  "success": false,
  "message": "该资产类型下有关联的资产，不能删除"
}
```

### 6. 变更资产位置 [POST]
▍代码定位：AssetController.cs

```csharp
[HttpPost("{id}/change-location")]
public async Task<IActionResult> ChangeLocation(int id, [FromBody] AssetLocationChangeModel model)
```

▍端点路径：/api/Asset/{id}/change-location
▍方法类型：POST

‖ 路径参数 ‖
- id: 资产ID

‖ 请求结构 ‖

```json
{
  "newLocationId": 2,
  "reason": "部门调整",
  "notes": "从研发部调整到工程部"
}
```

‖ 参数说明 ‖
- newLocationId: 新位置ID
- reason: 变更原因（必填）
- notes: 备注说明（可选）

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "id": 1,
    "assetCode": "IT-PC-001",
    "name": "办公电脑",
    "oldLocationId": 1,
    "oldLocationName": "研发部",
    "newLocationId": 2,
    "newLocationName": "工程部",
    "changeTime": "2023-03-25T10:35:20",
    "reason": "部门调整"
  },
  "message": "资产位置变更成功"
}

// 错误响应（400）
{
  "success": false,
  "message": "新位置ID无效"
}

// 错误响应（400）
{
  "success": false,
  "message": "变更原因不能为空"
}

// 错误响应（400）
{
  "success": false,
  "message": "新位置与当前位置相同"
}

// 错误响应（404）
{
  "success": false,
  "message": "未找到ID为1的资产"
}

// 错误响应（400）
{
  "success": false,
  "message": "未找到ID为2的位置"
}
```

### 7. 删除资产 [DELETE]
▍代码定位：AssetController.cs（未在代码片段中显示）

```csharp
[HttpDelete("{id}")]
public async Task<IActionResult> DeleteAsset(int id)
```

▍端点路径：/api/Asset/{id}
▍方法类型：DELETE

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": true
}

// 错误响应（404）
{
  "success": false,
  "message": "未找到ID为4的资产"
}
```

