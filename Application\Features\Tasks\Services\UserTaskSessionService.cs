#nullable enable
// File: Application/Features/Tasks/Services/UserTaskSessionService.cs
// Description: 用户任务会话服务，管理用户登录时的任务状态预加载

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using ItAssetsSystem.Infrastructure.Data;
using ItAssetsSystem.Application.Features.Tasks.Dtos;
using ItAssetsSystem.Core.Abstractions;

namespace ItAssetsSystem.Application.Features.Tasks.Services
{
    /// <summary>
    /// 用户任务会话信息
    /// </summary>
    public class UserTaskSession
    {
        public int UserId { get; set; }
        public DateTime SessionDate { get; set; }
        public List<long> AssignedTaskIds { get; set; } = new();
        public Dictionary<long, TaskClaimInfo> TaskClaimStatuses { get; set; } = new();
        public DateTime LoadTime { get; set; }
        public bool IsValid => DateTime.Now - LoadTime < TimeSpan.FromMinutes(30); // 30分钟有效期
    }

    /// <summary>
    /// 用户任务会话服务接口
    /// </summary>
    public interface IUserTaskSessionService
    {
        Task<UserTaskSession> InitializeUserSessionAsync(int userId);
        Task<UserTaskSession?> GetUserSessionAsync(int userId);
        Task RefreshUserSessionAsync(int userId);
        Task InvalidateUserSessionAsync(int userId);
        Task<List<TaskDto>> GetUserTodayTasksWithClaimStatusAsync(int userId);
    }

    /// <summary>
    /// 用户任务会话服务实现
    /// </summary>
    public class UserTaskSessionService : IUserTaskSessionService
    {
        private readonly AppDbContext _context;
        private readonly ITaskClaimCacheService _taskClaimCacheService;
        private readonly ITaskRepository _taskRepository;
        private readonly ICoreDataQueryService _coreDataQueryService;
        private readonly ILogger<UserTaskSessionService> _logger;

        // 内存缓存用户会话（在实际应用中可以考虑使用Redis）
        private static readonly Dictionary<int, UserTaskSession> _userSessions = new();
        private static readonly object _lockObject = new();

        public UserTaskSessionService(
            AppDbContext context,
            ITaskClaimCacheService taskClaimCacheService,
            ITaskRepository taskRepository,
            ICoreDataQueryService coreDataQueryService,
            ILogger<UserTaskSessionService> logger)
        {
            _context = context;
            _taskClaimCacheService = taskClaimCacheService;
            _taskRepository = taskRepository;
            _coreDataQueryService = coreDataQueryService;
            _logger = logger;
        }

        /// <summary>
        /// 初始化用户会话（用户登录时调用）
        /// </summary>
        public async Task<UserTaskSession> InitializeUserSessionAsync(int userId)
        {
            try
            {
                var today = DateTime.Today;
                _logger.LogInformation("开始初始化用户 {UserId} 的任务会话，日期: {Date}", userId, today.ToString("yyyy-MM-dd"));

                // 1. 查询用户今日相关的所有任务
                var userTasks = await GetUserAssignedTasksAsync(userId, today);
                var taskIds = userTasks.Select(t => t.TaskId).ToList();

                _logger.LogDebug("用户 {UserId} 今日分配的任务数量: {TaskCount}", userId, taskIds.Count);

                // 2. 预加载任务领取状态
                var taskClaimStatuses = await _taskClaimCacheService.GetTaskClaimStatusesAsync(taskIds, userId, today);

                // 3. 创建用户会话
                var session = new UserTaskSession
                {
                    UserId = userId,
                    SessionDate = today,
                    AssignedTaskIds = taskIds,
                    TaskClaimStatuses = taskClaimStatuses,
                    LoadTime = DateTime.Now
                };

                // 4. 缓存会话信息
                lock (_lockObject)
                {
                    _userSessions[userId] = session;
                }

                _logger.LogInformation("用户 {UserId} 任务会话初始化完成，任务数: {TaskCount}, 领取记录数: {ClaimCount}", 
                    userId, taskIds.Count, taskClaimStatuses.Count);

                return session;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "初始化用户 {UserId} 任务会话时发生错误", userId);
                throw;
            }
        }

        /// <summary>
        /// 获取用户会话
        /// </summary>
        public async Task<UserTaskSession?> GetUserSessionAsync(int userId)
        {
            lock (_lockObject)
            {
                if (_userSessions.TryGetValue(userId, out var session) && session.IsValid)
                {
                    _logger.LogDebug("从缓存获取用户 {UserId} 的任务会话", userId);
                    return session;
                }
            }

            // 会话不存在或已过期，重新初始化
            _logger.LogDebug("用户 {UserId} 的任务会话不存在或已过期，重新初始化", userId);
            return await InitializeUserSessionAsync(userId);
        }

        /// <summary>
        /// 刷新用户会话
        /// </summary>
        public async Task RefreshUserSessionAsync(int userId)
        {
            _logger.LogDebug("刷新用户 {UserId} 的任务会话", userId);
            await InitializeUserSessionAsync(userId);
        }

        /// <summary>
        /// 失效用户会话
        /// </summary>
        public async Task InvalidateUserSessionAsync(int userId)
        {
            lock (_lockObject)
            {
                _userSessions.Remove(userId);
            }
            _logger.LogDebug("已失效用户 {UserId} 的任务会话", userId);
        }

        /// <summary>
        /// 获取用户今日任务及领取状态
        /// </summary>
        public async Task<List<TaskDto>> GetUserTodayTasksWithClaimStatusAsync(int userId)
        {
            try
            {
                // 获取用户会话
                var session = await GetUserSessionAsync(userId);
                if (session == null)
                {
                    _logger.LogWarning("无法获取用户 {UserId} 的任务会话", userId);
                    return new List<TaskDto>();
                }

                // 查询任务详细信息
                var tasks = await _taskRepository.GetTasksByIdsAsync(session.AssignedTaskIds);
                
                // 转换为DTO并设置领取状态
                var taskDtos = new List<TaskDto>();
                var userIds = new HashSet<int>();

                // 收集用户ID
                foreach (var task in tasks)
                {
                    if (task.AssigneeUserId.HasValue) userIds.Add(task.AssigneeUserId.Value);
                    if (task.CreatorUserId > 0) userIds.Add(task.CreatorUserId);
                    if (task.CompletedByUserId.HasValue) userIds.Add(task.CompletedByUserId.Value);
                }

                // 批量查询用户信息
                var users = userIds.Any()
                    ? (await _coreDataQueryService.GetUsersAsync(userIds.ToList())).ToDictionary(u => u.Id, u => u)
                    : new Dictionary<int, CoreUserDto>();

                // 转换任务并设置领取状态
                foreach (var task in tasks)
                {
                    var dto = MapTaskToDto(task, users);
                    
                    // 设置领取状态
                    if (session.TaskClaimStatuses.TryGetValue(task.TaskId, out var claimInfo))
                    {
                        dto.ClaimedByUserId = claimInfo.ClaimedByUserId;
                        dto.ClaimedByUserName = claimInfo.ClaimedByUserName;
                    }

                    taskDtos.Add(dto);
                }

                _logger.LogDebug("获取用户 {UserId} 今日任务完成，任务数: {TaskCount}", userId, taskDtos.Count);
                return taskDtos;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户 {UserId} 今日任务时发生错误", userId);
                return new List<TaskDto>();
            }
        }

        /// <summary>
        /// 查询用户分配的任务
        /// </summary>
        private async Task<List<Domain.Entities.Tasks.Task>> GetUserAssignedTasksAsync(int userId, DateTime date)
        {
            // 查询用户作为主负责人或多负责人的任务
            var mainAssigneeTasks = await _context.Tasks
                .Where(t => t.AssigneeUserId == userId
                         && t.Status != "Done"
                         && t.Status != "Cancelled"
                         && (t.PlanEndDate == null || t.PlanEndDate.Value.Date >= date))
                .ToListAsync();

            var multiAssigneeTaskIds = await _context.TaskAssignees
                .Where(ta => ta.UserId == userId)
                .Select(ta => ta.TaskId)
                .ToListAsync();

            var multiAssigneeTasks = await _context.Tasks
                .Where(t => multiAssigneeTaskIds.Contains(t.TaskId)
                         && t.Status != "Done"
                         && t.Status != "Cancelled"
                         && (t.PlanEndDate == null || t.PlanEndDate.Value.Date >= date))
                .ToListAsync();

            // 合并并去重
            var allTasks = mainAssigneeTasks
                .Concat(multiAssigneeTasks)
                .GroupBy(t => t.TaskId)
                .Select(g => g.First())
                .ToList();

            return allTasks;
        }

        /// <summary>
        /// 简单的任务DTO映射
        /// </summary>
        private TaskDto MapTaskToDto(Domain.Entities.Tasks.Task task, Dictionary<int, CoreUserDto> users)
        {
            var dto = new TaskDto
            {
                TaskId = task.TaskId,
                Name = task.Name,
                Description = task.Description,
                Status = task.Status,
                Priority = task.Priority,
                TaskType = task.TaskType,
                CreationTimestamp = task.CreationTimestamp,
                PlanEndDate = task.PlanEndDate,
                AssigneeUserId = task.AssigneeUserId,
                CreatorUserId = task.CreatorUserId,
                CompletedByUserId = task.CompletedByUserId,
                CompletedAt = task.CompletedAt
            };

            // 设置用户名
            if (task.AssigneeUserId.HasValue && users.TryGetValue(task.AssigneeUserId.Value, out var assignee))
            {
                dto.AssigneeUserName = assignee.Name;
            }

            if (users.TryGetValue(task.CreatorUserId, out var creator))
            {
                dto.CreatorUserName = creator.Name;
            }

            if (task.CompletedByUserId.HasValue && users.TryGetValue(task.CompletedByUserId.Value, out var completedBy))
            {
                dto.CompletedByUserName = completedBy.Name;
            }

            return dto;
        }
    }
}
