// File: Application/Features/Tasks/Commands/PinCommentCommand.cs
// Description: 置顶评论命令

using MediatR;
using ItAssetsSystem.Application.Common.Dtos;
using ItAssetsSystem.Application.Features.Tasks.Dtos;

namespace ItAssetsSystem.Application.Features.Tasks.Commands
{
    /// <summary>
    /// 置顶评论命令
    /// </summary>
    public class PinCommentCommand : IRequest<ApiResponse<CommentDto>>
    {
        /// <summary>
        /// 评论ID
        /// </summary>
        public long CommentId { get; set; }

        /// <summary>
        /// 任务ID
        /// </summary>
        public long TaskId { get; set; }

        /// <summary>
        /// 是否置顶
        /// </summary>
        public bool IsPinned { get; set; }

        /// <summary>
        /// 当前用户ID
        /// </summary>
        public int CurrentUserId { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public PinCommentCommand()
        {
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="commentId">评论ID</param>
        /// <param name="taskId">任务ID</param>
        /// <param name="isPinned">是否置顶</param>
        /// <param name="currentUserId">当前用户ID</param>
        public PinCommentCommand(long commentId, long taskId, bool isPinned, int currentUserId)
        {
            CommentId = commentId;
            TaskId = taskId;
            IsPinned = isPinned;
            CurrentUserId = currentUserId;
        }
    }
}
