// IT资产管理系统 - 采购物品实体
// 文件路径: /Models/Entities/PurchaseItem.cs
// 功能: 定义采购物品实体，对应purchaseitems表，记录采购订单中的物品信息

using System;
using ItAssetsSystem.Models;

namespace ItAssetsSystem.Models.Entities
{
    /// <summary>
    /// 采购物品实体
    /// </summary>
    public class PurchaseItem : IAuditableEntity
    {
        /// <summary>
        /// 采购物品ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 采购订单ID
        /// </summary>
        public int PurchaseOrderId { get; set; }

        /// <summary>
        /// 物品名称
        /// </summary>
        public string ItemName { get; set; }

        /// <summary>
        /// 物品编码
        /// </summary>
        public string ItemCode { get; set; }

        /// <summary>
        /// 规格/型号
        /// </summary>
        public string Specification { get; set; }

        /// <summary>
        /// 资产类型ID
        /// </summary>
        public int? AssetTypeId { get; set; }

        /// <summary>
        /// 单价
        /// </summary>
        public decimal UnitPrice { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        public int Quantity { get; set; }

        /// <summary>
        /// 总价
        /// </summary>
        public decimal TotalPrice { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Notes { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; }

        /// <summary>
        /// 采购订单
        /// </summary>
        public virtual PurchaseOrder PurchaseOrder { get; set; }

        /// <summary>
        /// 资产类型
        /// </summary>
        public virtual AssetType AssetType { get; set; }
    }
}

// 计划行数: 50
// 实际行数: 50 