/**
 * 航空航天级IT资产管理系统 - 空数据状态组件
 * 文件路径: src/components/EmptyData.vue
 * 功能描述: 显示无数据状态的占位组件，可自定义图标和提示文本
 */

<template>
  <div class="empty-data">
    <el-empty
      :image="image"
      :image-size="imageSize"
      :description="description"
    >
      <template #default v-if="$slots.default">
        <slot></slot>
      </template>
    </el-empty>
  </div>
</template>

<script setup>
defineProps({
  // 图片链接
  image: {
    type: String,
    default: undefined
  },
  // 图片大小（宽度）
  imageSize: {
    type: Number,
    default: 100
  },
  // 描述文字
  description: {
    type: String,
    default: '暂无数据'
  }
})
</script>

<style lang="scss" scoped>
.empty-data {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 32px 0;
  width: 100%;
}
</style> 