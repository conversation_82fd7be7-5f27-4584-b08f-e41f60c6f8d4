using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace ItAssetsSystem.Core.Configuration
{
    /// <summary>
    /// 系统配置服务实现
    /// </summary>
    public class SystemConfigService : ISystemConfigService
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<SystemConfigService> _logger;
        private readonly string _configFilePath;
        private readonly JsonSerializerOptions _jsonOptions;
        
        public SystemConfigService(IConfiguration configuration, ILogger<SystemConfigService> logger)
        {
            _configuration = configuration;
            _logger = logger;
            
            // 获取配置文件路径
            _configFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "appsettings.json");
            
            // 初始化JSON序列化选项
            _jsonOptions = new JsonSerializerOptions
            {
                WriteIndented = true
            };
        }

        /// <inheritdoc />
        public async Task<Dictionary<string, object>> GetAllConfigurationsAsync()
        {
            try
            {
                var result = new Dictionary<string, object>();
                
                // 将IConfiguration转换为字典
                foreach (var section in _configuration.GetChildren())
                {
                    await FlattenConfigurationSection(section, result, section.Key);
                }
                
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取所有配置项时发生错误");
                return new Dictionary<string, object>();
            }
        }

        /// <inheritdoc />
        public Task<T> GetConfigurationValueAsync<T>(string key)
        {
            try
            {
                var value = _configuration.GetSection(key).Get<T>();
                return Task.FromResult(value);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取配置值时发生错误: {Key}", key);
                return Task.FromResult<T>(default);
            }
        }

        /// <inheritdoc />
        public async Task<bool> UpdateConfigurationValueAsync(string key, object value)
        {
            try
            {
                // 读取当前配置文件
                string json = await File.ReadAllTextAsync(_configFilePath);
                var config = JObject.Parse(json);
                
                // 更新配置值
                SetValueInJObject(config, key.Split(':'), value);
                
                // 保存配置文件
                await File.WriteAllTextAsync(_configFilePath, config.ToString(Formatting.Indented));
                
                _logger.LogInformation("成功更新配置: {Key}", key);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新配置值时发生错误: {Key}", key);
                return false;
            }
        }

        /// <inheritdoc />
        public async Task<bool> BulkUpdateConfigurationsAsync(Dictionary<string, object> configurations)
        {
            try
            {
                // 读取当前配置文件
                string json = await File.ReadAllTextAsync(_configFilePath);
                var config = JObject.Parse(json);
                
                // 批量更新配置
                foreach (var kvp in configurations)
                {
                    SetValueInJObject(config, kvp.Key.Split(':'), kvp.Value);
                }
                
                // 保存配置文件
                await File.WriteAllTextAsync(_configFilePath, config.ToString(Formatting.Indented));
                
                _logger.LogInformation("成功批量更新配置，共 {Count} 项", configurations.Count);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量更新配置时发生错误");
                return false;
            }
        }

        /// <inheritdoc />
        public async Task<bool> ResetToDefaultsAsync()
        {
            try
            {
                // 读取默认配置文件
                string defaultConfigPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "appsettings.default.json");
                
                if (!File.Exists(defaultConfigPath))
                {
                    _logger.LogWarning("默认配置文件不存在: {Path}", defaultConfigPath);
                    return false;
                }
                
                // 复制默认配置到当前配置
                await File.WriteAllTextAsync(_configFilePath, await File.ReadAllTextAsync(defaultConfigPath));
                
                _logger.LogInformation("成功重置所有配置为默认值");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "重置配置为默认值时发生错误");
                return false;
            }
        }

        /// <inheritdoc />
        public async Task<bool> ExportConfigurationsAsync(string filePath)
        {
            try
            {
                // 获取所有配置
                var configurations = await GetAllConfigurationsAsync();
                
                // 序列化并保存到文件
                var json = System.Text.Json.JsonSerializer.Serialize(configurations, _jsonOptions);
                await File.WriteAllTextAsync(filePath, json);
                
                _logger.LogInformation("成功导出配置到文件: {Path}", filePath);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "导出配置时发生错误: {Path}", filePath);
                return false;
            }
        }

        /// <inheritdoc />
        public async Task<bool> ImportConfigurationsAsync(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    _logger.LogWarning("导入配置文件不存在: {Path}", filePath);
                    return false;
                }
                
                // 读取导入文件
                string json = await File.ReadAllTextAsync(filePath);
                var configurations = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(json);
                
                // 批量更新配置
                return await BulkUpdateConfigurationsAsync(configurations);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "导入配置时发生错误: {Path}", filePath);
                return false;
            }
        }
        
        #region 辅助方法
        
        /// <summary>
        /// 将配置节平铺为字典
        /// </summary>
        private async Task FlattenConfigurationSection(IConfigurationSection section, Dictionary<string, object> result, string prefix)
        {
            // 如果当前节点有子节点，则递归处理
            var children = section.GetChildren().ToList();
            if (children.Any())
            {
                foreach (var child in children)
                {
                    await FlattenConfigurationSection(child, result, $"{prefix}:{child.Key}");
                }
            }
            else
            {
                // 否则将值添加到结果中
                result.Add(prefix, section.Value);
            }
        }
        
        /// <summary>
        /// 在JObject中设置值
        /// </summary>
        private void SetValueInJObject(JObject jObject, string[] keys, object value)
        {
            JToken token = jObject;
            
            // 导航到倒数第二层
            for (int i = 0; i < keys.Length - 1; i++)
            {
                if (token[keys[i]] == null)
                {
                    token[keys[i]] = new JObject();
                }
                
                token = token[keys[i]];
            }
            
            // 设置最后一层的值
            if (value == null)
            {
                token[keys[keys.Length - 1]] = JValue.CreateNull();
            }
            else if (value is string str)
            {
                token[keys[keys.Length - 1]] = str;
            }
            else if (value is bool boolean)
            {
                token[keys[keys.Length - 1]] = boolean;
            }
            else if (value is int integer)
            {
                token[keys[keys.Length - 1]] = integer;
            }
            else if (value is long longValue)
            {
                token[keys[keys.Length - 1]] = longValue;
            }
            else if (value is double doubleValue)
            {
                token[keys[keys.Length - 1]] = doubleValue;
            }
            else
            {
                // 尝试作为复杂对象序列化
                token[keys[keys.Length - 1]] = JToken.FromObject(value);
            }
        }
        
        #endregion
    }
} 