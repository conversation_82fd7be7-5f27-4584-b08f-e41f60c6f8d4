using System;
using System.IO;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Reflection;
using System.Text.Json;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using OfficeOpenXml;
using CsvHelper;
using System.Globalization;
using Newtonsoft.Json;
using ItAssetsSystem.Core.Import;
using ItAssetsSystem.Core.Export;

namespace ItAssetsSystem.Api.Import
{
    public class ImportService : IImportService
    {
        private readonly ILogger<ImportService> _logger;
        private readonly IImportResultStore _resultStore;
        private readonly IExportService _exportService;

        public ImportService(ILogger<ImportService> logger, IImportResultStore resultStore, IExportService exportService)
        {
            _logger = logger;
            _resultStore = resultStore;
            _exportService = exportService;
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
        }

        public async Task<ImportResult<T>> ImportDataAsync<T>(Stream fileStream, ImportOptions options) where T : class, new()
        {
            _logger.LogInformation("开始导入数据，格式: {Format}", options.Format);
            
            if (fileStream == null)
            {
                throw new ArgumentNullException(nameof(fileStream));
            }
            
            if (options == null)
            {
                throw new ArgumentNullException(nameof(options));
            }

            // 合并中文映射和自定义映射
            if (options.ColumnMappings == null)
            {
                options.ColumnMappings = new Dictionary<string, string>();
            }

            // 如果是资产类型，添加中文列映射支持
            if (typeof(T).Name == "Asset")
            {
                // 合并中文列映射
                foreach (var mapping in options.ChineseColumnMappings)
                {
                    if (!options.ColumnMappings.ContainsKey(mapping.Key))
                    {
                        options.ColumnMappings.Add(mapping.Key, mapping.Value);
                    }
                }
                
                _logger.LogInformation("资产导入映射: {Mappings}", string.Join(", ", options.ColumnMappings.Select(m => $"{m.Key}=>{m.Value}")));
            }
            
            try
            {
                switch (options.Format)
                {
                    case ImportFormat.Csv:
                        return await ImportFromCsvAsync<T>(fileStream, options);
                    case ImportFormat.Excel:
                        return await ImportFromExcelAsync<T>(fileStream, options);
                    case ImportFormat.Json:
                        return await ImportFromJsonAsync<T>(fileStream, options);
                    default:
                        throw new NotSupportedException($"不支持的导入格式: {options.Format}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "导入数据失败: {Message}", ex.Message);
                throw;
            }
        }

        public List<ImportFormat> GetSupportedFormats()
        {
            return Enum.GetValues(typeof(ImportFormat))
                .Cast<ImportFormat>()
                .ToList();
        }

        public async Task<MemoryStream> GenerateTemplateAsync<T>(ImportFormat format)
        {
            _logger.LogInformation("生成导入模板，格式: {Format}", format);
            
            try
            {
                // 创建一个空的数据集
                var templateData = new List<T>();
                
                // 添加一个空行作为模板
                templateData.Add(Activator.CreateInstance<T>());
                
                // 获取类型的属性信息
                var properties = typeof(T).GetProperties();
                var columns = new Dictionary<string, string>();
                
                // 创建列映射（属性名作为显示名称）
                foreach (var prop in properties)
                {
                    columns[prop.Name] = prop.Name;
                }
                
                // 如果是资产类型，使用中文列名
                if (typeof(T).Name == "Asset")
                {
                    _logger.LogInformation("正在生成资产导入模板");
                    
                    // 使用反向的中文映射 - 显示中文名称
                    columns.Clear();
                    
                    // 使用资产的标准中文列名
                    columns["AssetCode"] = "资产编码";
                    columns["FinancialCode"] = "财务编号";
                    columns["Name"] = "资产名称";
                    columns["AssetTypeId"] = "资产类型ID";
                    columns["AssetTypeName"] = "资产类型名称";
                    columns["SerialNumber"] = "序列号";
                    columns["Model"] = "型号";
                    columns["Brand"] = "品牌";
                    columns["PurchaseDate"] = "购买日期";
                    columns["WarrantyExpireDate"] = "保修到期日期";
                    columns["Price"] = "价格";
                    columns["LocationId"] = "位置ID";
                    columns["LocationName"] = "位置名称";
                    columns["DepartmentId"] = "部门ID";
                    columns["DepartmentName"] = "部门名称";
                    columns["Status"] = "状态";
                    columns["Notes"] = "备注";
                }
                
                // 使用导出服务导出模板
                var options = new ExportOptions
                {
                    Format = format switch
                    {
                        ImportFormat.Csv => ExportFormat.Csv,
                        ImportFormat.Excel => ExportFormat.Excel,
                        ImportFormat.Json => ExportFormat.Json,
                        _ => throw new NotSupportedException($"不支持的导入格式: {format}")
                    },
                    FileName = $"{typeof(T).Name}_Template.{GetFileExtension(format)}",
                    IncludeHeaders = true,
                    Columns = columns
                };
                
                return await _exportService.ExportDataAsync(templateData, options);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "生成导入模板失败，格式: {Format}", format);
                throw;
            }
        }

        private async Task<ImportResult<T>> ImportFromCsvAsync<T>(Stream fileStream, ImportOptions options) where T : class, new()
        {
            var result = new ImportResult<T>();
            
            using var reader = new StreamReader(fileStream);
            
            // 读取表头
            string headerLine = await reader.ReadLineAsync();
            if (string.IsNullOrEmpty(headerLine))
            {
                throw new InvalidOperationException("CSV文件为空或不包含表头");
            }
            
            List<string> headers = options.HasHeaders 
                ? ParseCsvLine(headerLine) 
                : new List<string>();
            
            // 读取数据行
            int lineNumber = options.HasHeaders ? 1 : 0;
            string line;
            
            while ((line = await reader.ReadLineAsync()) != null)
            {
                lineNumber++;
                
                if (string.IsNullOrWhiteSpace(line))
                {
                    continue;
                }
                
                try
                {
                    List<string> values = ParseCsvLine(line);
                    
                    if (options.HasHeaders)
                    {
                        // 创建表头-值对应的字典
                        var rowValues = new Dictionary<string, string>();
                        for (int i = 0; i < Math.Min(headers.Count, values.Count); i++)
                        {
                            if (!string.IsNullOrEmpty(headers[i]))
                            {
                                rowValues[headers[i]] = values[i];
                            }
                        }
                        
                        // 使用表头创建对象
                        var item = CreateItemFromValues<T>(rowValues, options.ColumnMappings);
                        if (item != null)
                        {
                        result.SuccessItems.Add(item);
                        }
                        else
                        {
                            result.Errors[lineNumber] = $"处理第{lineNumber}行时创建对象失败";
                        }
                    }
                    else
                    {
                        // 没有表头，直接使用索引
                        var properties = typeof(T).GetProperties(BindingFlags.Public | BindingFlags.Instance);
                        var item = new T();
                        
                        for (int i = 0; i < Math.Min(properties.Length, values.Count); i++)
                        {
                            var property = properties[i];
                            property.SetValue(item, ConvertValueToType(values[i], property.PropertyType));
                        }
                        
                        result.SuccessItems.Add(item);
                    }
                }
                catch (Exception ex)
                {
                    result.Errors[lineNumber] = $"处理第{lineNumber}行时发生错误: {ex.Message}";
                }
            }
            
            result.TotalRows = lineNumber;
            return result;
        }

        /// <summary>
        /// 获取Excel单元格的字符串值，处理各种数据类型
        /// </summary>
        /// <param name="cell">Excel单元格</param>
        /// <returns>单元格的字符串值</returns>
        public string GetCellValueAsString(ExcelRange cell)
        {
            if (cell == null)
            {
                return string.Empty;
            }

            try
            {
                // 详细记录单元格信息，帮助诊断问题
                _logger.LogInformation("读取单元格 {Address}，数据类型: {DataType}", 
                    cell.Address, cell.Value?.GetType().Name ?? "null");
                
                // 尝试所有可能的方法获取值
                string result = string.Empty;
                
                // 1. 优先使用Value属性 - 可能包含原始未格式化的值
                if (cell.Value != null)
                {
                    // 处理特定类型
                    if (cell.Value is double doubleValue)
                    {
                        result = doubleValue.ToString(CultureInfo.InvariantCulture);
                        _logger.LogInformation("单元格 {Address} 是数值类型: {Value}", cell.Address, result);
                    }
                    else if (cell.Value is DateTime dateValue)
                    {
                        result = dateValue.ToString("yyyy-MM-dd");
                        _logger.LogInformation("单元格 {Address} 是日期类型: {Value}", cell.Address, result);
                    }
                    else if (cell.Value is bool boolValue)
                    {
                        result = boolValue ? "是" : "否";
                        _logger.LogInformation("单元格 {Address} 是布尔类型: {Value}", cell.Address, result);
                    }
                    else
                    {
                        result = cell.Value.ToString()?.Trim() ?? string.Empty;
                        _logger.LogInformation("单元格 {Address} 通过Value获取原始值: {Value}", cell.Address, result);
                    }
                    
                    if (!string.IsNullOrEmpty(result))
                    {
                        return result;
                    }
                }

                // 2. 如果Value为null或空，检查Text属性（可能包含格式化的文本）
                if (!string.IsNullOrEmpty(cell.Text))
                {
                    result = cell.Text.Trim();
                    _logger.LogInformation("单元格 {Address} 通过Text获取格式化值: {Value}", cell.Address, result);
                    return result;
                }
                
                // 3. 如果还是没有值，检查是否有公式
                if (!string.IsNullOrEmpty(cell.Formula))
                {
                    _logger.LogInformation("单元格 {Address} 包含公式: {Formula}", cell.Address, cell.Formula);
                    
                    // 公式单元格，尝试获取计算结果
                    if (cell.Value != null)
                    {
                        result = cell.Value.ToString()?.Trim() ?? string.Empty;
                        _logger.LogInformation("单元格 {Address} 公式计算结果: {Value}", cell.Address, result);
                        return result;
                    }
                }
                
                // 4. 尝试检查DisplayText属性（一些EPPlus版本支持）
                var displayTextProperty = cell.GetType().GetProperty("DisplayText");
                if (displayTextProperty != null)
                {
                    var displayText = displayTextProperty.GetValue(cell) as string;
                    if (!string.IsNullOrEmpty(displayText))
                    {
                        result = displayText.Trim();
                        _logger.LogInformation("单元格 {Address} 通过DisplayText获取: {Value}", cell.Address, result);
                        return result;
                    }
                }
                
                // 所有方法都失败了，返回空字符串
                _logger.LogWarning("单元格 {Address} 无法获取有效值，返回空字符串", cell.Address);
                return string.Empty;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取单元格 {Address} 值时发生异常", cell.Address);
                return string.Empty;
            }
        }
        
        /// <summary>
        /// 从Excel导入数据
        /// </summary>
        private async Task<ImportResult<T>> ImportFromExcelAsync<T>(Stream fileStream, ImportOptions options) where T : class, new()
        {
            var result = new ImportResult<T>();
            
            try
            {
                // 使用await Task.Yield()确保方法是真正的异步
                await Task.Yield();
                
                _logger.LogInformation("===== 开始Excel数据导入 =====");
                _logger.LogInformation("格式: {Format}, 包含表头: {HasHeaders}, 中文映射数量: {MappingCount}", 
                    options.Format, options.HasHeaders, options.ChineseColumnMappings?.Count ?? 0);
            
            using var package = new ExcelPackage(fileStream);
            
            // 获取工作表
            var worksheet = options.SheetIndex.HasValue 
                ? package.Workbook.Worksheets[options.SheetIndex.Value] 
                : (!string.IsNullOrEmpty(options.SheetName) 
                    ? package.Workbook.Worksheets[options.SheetName] 
                    : package.Workbook.Worksheets.FirstOrDefault());
            
            if (worksheet == null)
            {
                    _logger.LogError("未找到有效的Excel工作表");
                throw new InvalidOperationException("未找到有效的Excel工作表");
            }
            
                // 输出所有工作表信息
                _logger.LogInformation("Excel包含工作表: {Sheets}", 
                    string.Join(", ", package.Workbook.Worksheets.Select(ws => ws.Name)));
            
            // 获取数据范围
            int startRow = options.HasHeaders ? 2 : 1;
            int startCol = 1;
            int endRow = worksheet.Dimension?.End.Row ?? 0;
            int endCol = worksheet.Dimension?.End.Column ?? 0;
            
                _logger.LogInformation("Excel工作表信息: 表名='{SheetName}', 总行数={Rows}, 总列数={Columns}", 
                    worksheet.Name, endRow, endCol);
            
            if (endRow == 0 || endCol == 0)
            {
                    _logger.LogWarning("Excel工作表为空，无数据可导入");
                    result.Errors[0] = "Excel工作表为空，无数据可导入";
                return result; // 空工作表
            }
            
                if (endRow < 2 && options.HasHeaders)
                {
                    _logger.LogWarning("Excel表格只有标题行，没有实际数据");
                    result.Errors[0] = "Excel表格只有标题行，没有实际数据";
                    return result; // 只有表头没有数据
                }
                
                // 输出工作表维度信息
                _logger.LogInformation("Excel工作表维度: 起始行={StartRow}, 起始列={StartCol}, 结束行={EndRow}, 结束列={EndCol}", 
                    worksheet.Dimension.Start.Row, worksheet.Dimension.Start.Column, endRow, endCol);
                
                // 详细记录所有行的内容
                _logger.LogInformation("----- Excel文件详细信息 -----");
                
                // 读取表头行
            List<string> headers = new List<string>();
            
            if (options.HasHeaders)
            {
                    _logger.LogInformation("----- 读取表头信息 -----");
                for (int col = startCol; col <= endCol; col++)
                {
                        // 使用GetCellValueAsString方法读取表头
                        string header = GetCellValueAsString(worksheet.Cells[1, col]);
                        
                        _logger.LogInformation("列 {Col} 表头: '{Header}'", col, header);
                    headers.Add(header);
                }
                    
                    // 打印有效表头
                    var validHeaders = headers.Where(h => !string.IsNullOrEmpty(h)).ToList();
                    _logger.LogInformation("有效表头列({Count}个): {Headers}", 
                        validHeaders.Count, string.Join(", ", validHeaders));
                    
                    // 验证是否至少读取到一个有效表头
                    if (headers.All(string.IsNullOrEmpty))
                    {
                        _logger.LogError("表头行全部为空，无法继续导入");
                        result.Errors[0] = "无法识别表头，请确保Excel文件格式正确且第一行包含有效的列名";
                        return result;
                    }
                }
                
                // 统计实际读取的数据行数
                int successRows = 0;
                bool hasData = false;
                
                _logger.LogInformation("----- 开始读取数据行 -----");
                
                // 处理每一数据行
                _logger.LogInformation("----- 处理数据行 -----");
            for (int row = startRow; row <= endRow; row++)
            {
                    _logger.LogInformation("处理第{RowNumber}行数据", row);
                    
                    // 检查是否是空行
                    bool isEmptyRow = true;
                    
                    // 记录行的实际内容
                    var rowValues = new List<string>();
                    Dictionary<string, string> headerValueDict = new Dictionary<string, string>();
                    
                    for (int col = startCol; col <= endCol; col++)
                    {
                        // 使用GetCellValueAsString方法读取单元格值
                        string cellValue = GetCellValueAsString(worksheet.Cells[row, col]);
                        rowValues.Add(cellValue);
                        
                        // 检查是否为空行
                        if (!string.IsNullOrWhiteSpace(cellValue))
                        {
                            isEmptyRow = false;
                        }
                        
                        // 同时构建表头-值字典
                        if (col - startCol < headers.Count)
                        {
                            string header = headers[col - startCol];
                            if (!string.IsNullOrEmpty(header))
                            {
                                // 即使值为空也添加到字典，以便后续可以看到完整映射
                                headerValueDict[header] = cellValue ?? string.Empty;
                                
                                // 记录单元格内容
                                _logger.LogInformation("单元格[{Row},{Col}]: '{Header}'='{Value}'", 
                                    row, col, header, cellValue);
                                    
                                // 特别记录资产编号，方便排查
                                if (header.Contains("资产编码") || header.Contains("资产编号") || 
                                    header.Contains("AssetCode") || header.Equals("编码", StringComparison.OrdinalIgnoreCase))
                                {
                                    _logger.LogWarning("直接从表格读取到资产编号: 行={Row}, 值='{Value}'", row, cellValue);
                                }
                            }
                        }
                    }
                    
                    _logger.LogInformation("第{Row}行数据: {Values}", row, string.Join(" | ", rowValues));
                    
                    if (isEmptyRow)
                    {
                        _logger.LogInformation("跳过空行: 第{Row}行", row);
                        continue;
                    }
                    
                    hasData = true;
                    
                    // 创建实体对象
                    try
                    {
                        _logger.LogInformation("调用 CreateItemFromValues 前，字典包含资产编码键: {HasKey}, 值为: '{Value}'", 
                            headerValueDict.ContainsKey("资产编码") || headerValueDict.ContainsKey("资产编号"), 
                            headerValueDict.GetValueOrDefault("资产编码") ?? headerValueDict.GetValueOrDefault("资产编号") ?? "[未找到资产编码键]");
                            
                        var item = CreateItemFromValues<T>(headerValueDict, options.ChineseColumnMappings);
                        
                        if (item != null)
                        {
                            var assetCodeProp = typeof(T).GetProperty("AssetCode");
                            var currentCode = assetCodeProp?.GetValue(item)?.ToString();
                            _logger.LogWarning("CreateItemFromValues 返回后，item 的 AssetCode 为: '{AssetCode}' (Row: {Row})", currentCode, row);

                            result.SuccessItems.Add(item);
                            successRows++;
                        }
                        else
                        {
                            _logger.LogWarning("第{Row}行创建对象失败", row);
                            result.Errors[row] = $"无法从该行创建有效对象";
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "处理第{Row}行数据时发生异常: {Message}", row, ex.Message);
                        result.Errors[row] = $"处理第{row}行时发生错误: {ex.Message}";
                    }
                }
                
                // 检查是否有数据被处理
                if (!hasData)
                {
                    _logger.LogWarning("Excel没有任何有效数据行");
                    result.Errors[0] = "Excel没有任何有效数据行";
                }
                
                // 完成导入，汇总结果
                result.TotalRows = endRow - startRow + 1;
                _logger.LogInformation("Excel导入完成: 总行数={TotalRows}, 成功={Success}, 错误={Errors}", 
                    result.TotalRows, successRows, result.Errors.Count);
                
                // 记录第一条成功导入数据的属性值，用于调试
                if (result.SuccessItems.Any())
                {
                    _logger.LogInformation("成功导入的第一条数据属性:");
                    var firstItem = result.SuccessItems.FirstOrDefault();
                    if (firstItem != null)
                    {
                        foreach (var prop in typeof(T).GetProperties(BindingFlags.Public | BindingFlags.Instance))
                        {
                            var value = prop.GetValue(firstItem);
                            if (value != null)
                            {
                                _logger.LogInformation("  {Property} = {Value}", prop.Name, value);
                            }
                        }
                    }
                }
                
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Excel导入过程中发生错误: {Message}", ex.Message);
                result.Errors[0] = $"导入过程发生错误: {ex.Message}";
                return result;
            }
        }

        /// <summary>
        /// 设置对象属性值
        /// </summary>
        /// <typeparam name="T">实体类型</typeparam>
        /// <param name="item">实体对象</param>
        /// <param name="header">表头名称</param>
        /// <param name="value">属性值</param>
        /// <param name="columnMappings">列映射字典</param>
        /// <returns>设置是否成功</returns>
        private bool SetPropertyValue<T>(T item, string header, string value, Dictionary<string, string> columnMappings)
        {
            // Allow empty string values but not null
            if (string.IsNullOrEmpty(header) || value == null) 
            {
                _logger.LogDebug("SetPropertyValue: 表头为空或值为null，跳过. Header='{Header}', Value='{Value}'", header, value);
                return false;
            }
            
            string trimmedValue = value.Trim(); // Trim value early

            try
            {
                _logger.LogDebug("SetPropertyValue: 尝试设置属性，原始表头: '{Header}', Trimmed值: '{TrimmedValue}'", header, trimmedValue);
                
                // 1. Determine the target property name
                string propertyName = header;
                bool mappingFound = false;
                
                if (columnMappings != null && columnMappings.TryGetValue(header, out string mappedProperty))
                {
                    propertyName = mappedProperty;
                    mappingFound = true;
                    _logger.LogDebug("使用直接映射: '{Header}' -> '{PropertyName}'", header, propertyName);
                }
                else if (!mappingFound && columnMappings != null)
                {
                    string normalizedHeader = header.Replace(" ", "");
                    if (columnMappings.TryGetValue(normalizedHeader, out mappedProperty))
                    {
                        propertyName = mappedProperty;
                        mappingFound = true;
                        _logger.LogDebug("使用规范化映射: '{Header}' -> '{PropertyName}'", header, propertyName);
                    }
                }
                
                bool propertySet = false;
                
                // >> High Priority: Asset Code Handling for Asset Type <<
                if (typeof(T).Name == "Asset" && 
                   (string.Equals(propertyName, "AssetCode", StringComparison.OrdinalIgnoreCase) ||
                    header.Contains("资产编码") || header.Contains("资产编号") || header.Contains("编码")))
                {
                    _logger.LogWarning("Entered [AssetCode Handling] Block for Header='{Header}', Value='{Value}'", header, trimmedValue);

                    _logger.LogInformation("[AssetCode Handling] 检测到资产编码表头/映射: Header='{Header}', PropertyName='{PropertyName}', Value='{Value}'", 
                        header, propertyName, trimmedValue);
                    
                    var codeProp = typeof(T).GetProperty("AssetCode", BindingFlags.Public | BindingFlags.Instance | BindingFlags.IgnoreCase);
                    if (codeProp != null && codeProp.CanWrite)
                    {
                        if (!string.IsNullOrEmpty(trimmedValue))
                        {
                            _logger.LogInformation("[AssetCode Handling] 准备设置资产编码属性为: '{Value}'", trimmedValue);
                            try {
                                codeProp.SetValue(item, trimmedValue);
                                // Verify immediately after setting
                                var currentValue = codeProp.GetValue(item)?.ToString();
                                _logger.LogInformation("[AssetCode Handling] 资产编码属性设置后，当前值: '{CurrentValue}'", currentValue);
                                if (currentValue == trimmedValue) {
                                    _logger.LogInformation("[AssetCode Handling] 资产编码设置成功且验证通过.");
                                    propertySet = true; // Mark as set
                                }
                                else {
                                     _logger.LogError("[AssetCode Handling] 资产编码设置失败！预期值: '{Expected}', 实际值: '{Actual}'", trimmedValue, currentValue);
                                }
                            }
                            catch(Exception ex) {
                                _logger.LogError(ex, "[AssetCode Handling] 设置资产编码时发生异常");
                        }
                    }
                    else
                    {
                            _logger.LogWarning("[AssetCode Handling] 值为空或仅包含空格，跳过设置资产编码.");
                        }
                    }
                    else
                    {
                         _logger.LogWarning("[AssetCode Handling] 找不到AssetCode属性或属性不可写.");
                    }
                }

                // 2. Handle other Asset-specific logic (only if not already handled as AssetCode)
                if (!propertySet && typeof(T).Name == "Asset")
                {
                    // 处理资产类型名称
                    if (string.Equals(propertyName, "AssetTypeName", StringComparison.OrdinalIgnoreCase) || header.Contains("类型"))
                    {
                        var prop = typeof(T).GetProperty("AssetTypeName", BindingFlags.Public | BindingFlags.Instance | BindingFlags.IgnoreCase);
                        if (prop != null && prop.CanWrite && !string.IsNullOrEmpty(trimmedValue))
                        {
                            _logger.LogDebug("设置资产类型名称: {Value}", trimmedValue);
                            prop.SetValue(item, trimmedValue);
                            propertySet = true;
                        }
                    }
                    // 处理位置名称
                    else if (string.Equals(propertyName, "LocationName", StringComparison.OrdinalIgnoreCase) || header.Contains("位置"))
                    {
                        var prop = typeof(T).GetProperty("LocationName", BindingFlags.Public | BindingFlags.Instance | BindingFlags.IgnoreCase);
                        if (prop != null && prop.CanWrite && !string.IsNullOrEmpty(trimmedValue))
                        {
                            _logger.LogDebug("设置位置名称: {Value}", trimmedValue);
                            prop.SetValue(item, trimmedValue);
                            propertySet = true;
                        }
                    }
                    // 处理购买日期
                    else if (string.Equals(propertyName, "PurchaseDate", StringComparison.OrdinalIgnoreCase) || header.Contains("购买") || header.Contains("购入"))
                    {
                        var dateProp = typeof(T).GetProperty("PurchaseDate", BindingFlags.Public | BindingFlags.Instance | BindingFlags.IgnoreCase);
                        if (dateProp != null && dateProp.CanWrite && DateTime.TryParse(trimmedValue, out DateTime date))
                        {
                             _logger.LogDebug("成功解析购买日期: {Value} -> {Date}", trimmedValue, date.ToString("yyyy-MM-dd"));
                             dateProp.SetValue(item, date);
                             propertySet = true;
                        }
                        else if (!DateTime.TryParse(trimmedValue, out _)) {
                             _logger.LogWarning("无法解析购买日期: {Value}", trimmedValue);
                        }
                    }
                    // 处理状态
                    else if (string.Equals(propertyName, "Status", StringComparison.OrdinalIgnoreCase) || header.Contains("状态"))
                    {
                       var statusProp = typeof(T).GetProperty("Status", BindingFlags.Public | BindingFlags.Instance | BindingFlags.IgnoreCase);
                       if (statusProp != null && statusProp.CanWrite)
                       {
                            int statusValue = 1; // Default to 在用
                            bool statusSet = false;
                            if (trimmedValue.Equals("在用", StringComparison.OrdinalIgnoreCase) || trimmedValue.Equals("使用中", StringComparison.OrdinalIgnoreCase)) { statusValue = 1; statusSet = true; }
                            else if (trimmedValue.Equals("闲置", StringComparison.OrdinalIgnoreCase)) { statusValue = 0; statusSet = true; }
                            else if (trimmedValue.Equals("维修", StringComparison.OrdinalIgnoreCase) || trimmedValue.Equals("维修中", StringComparison.OrdinalIgnoreCase)) { statusValue = 2; statusSet = true; }
                            else if (trimmedValue.Equals("报废", StringComparison.OrdinalIgnoreCase)) { statusValue = 3; statusSet = true; }
                            else if (int.TryParse(trimmedValue, out int parsedStatus) && parsedStatus >= 0 && parsedStatus <= 3) { statusValue = parsedStatus; statusSet = true; }
                            
                            if(statusSet) {
                                statusProp.SetValue(item, statusValue);
                                _logger.LogDebug("设置状态: '{Value}' -> {NumValue}", trimmedValue, statusValue);
                                propertySet = true;
                            }
                            else {
                                statusProp.SetValue(item, 1); // Default to 在用
                                _logger.LogDebug("未知状态值 '{Value}'，默认设为'在用'(1)", trimmedValue);
                                propertySet = true; // Set default
                            }
                       }
                    }
                    // 处理保修到期日
                    else if (string.Equals(propertyName, "WarrantyExpireDate", StringComparison.OrdinalIgnoreCase) || header.Contains("保修"))
                    {
                         var dateProp = typeof(T).GetProperty("WarrantyExpireDate", BindingFlags.Public | BindingFlags.Instance | BindingFlags.IgnoreCase);
                         if (dateProp != null && dateProp.CanWrite && DateTime.TryParse(trimmedValue, out DateTime date))
                         {
                              _logger.LogDebug("成功解析保修到期日: {Value} -> {Date}", trimmedValue, date.ToString("yyyy-MM-dd"));
                              dateProp.SetValue(item, date);
                              propertySet = true;
                         }
                         else if (!DateTime.TryParse(trimmedValue, out _)) {
                              _logger.LogWarning("无法解析保修日期: {Value}", trimmedValue);
                         }
                    }
                    // 处理价格
                    else if (string.Equals(propertyName, "Price", StringComparison.OrdinalIgnoreCase) || header.Contains("价格"))
                    {
                         var priceProp = typeof(T).GetProperty("Price", BindingFlags.Public | BindingFlags.Instance | BindingFlags.IgnoreCase);
                         if (priceProp != null && priceProp.CanWrite)
                         {
                             string cleanValue = trimmedValue.Replace(",", "").Replace("￥", "").Replace("$", "").Trim();
                             if (decimal.TryParse(cleanValue, out decimal price))
                             {
                                 priceProp.SetValue(item, price);
                                 _logger.LogDebug("设置价格: {Value}", price);
                                 propertySet = true;
                             }
                             else {
                                 _logger.LogWarning("无法解析价格: {Value}", trimmedValue);
                             }
                         }
                    }
                    // 处理资产名称 (if not AssetCode which has higher priority)
                    else if (string.Equals(propertyName, "Name", StringComparison.OrdinalIgnoreCase) || header.Contains("资产名称"))
                    {
                        var nameProp = typeof(T).GetProperty("Name", BindingFlags.Public | BindingFlags.Instance | BindingFlags.IgnoreCase);
                        if (nameProp != null && nameProp.CanWrite && !string.IsNullOrEmpty(trimmedValue))
                        {
                            _logger.LogDebug("设置资产名称: {Value}", trimmedValue);
                            nameProp.SetValue(item, trimmedValue);
                            propertySet = true;
                        }
                    }
                }
                
                // 3. Generic Property Setting (if not handled by specific logic above)
                if (!propertySet)
                {
                    _logger.LogDebug("尝试通用属性设置: PropertyName='{PropertyName}', Value='{Value}'", propertyName, trimmedValue);
                    var property = typeof(T).GetProperty(propertyName, BindingFlags.Public | BindingFlags.Instance | BindingFlags.IgnoreCase);
                    
                    // If mapping was used but property not found by mapped name, try original header name
                    if (property == null && mappingFound)
                    {
                         _logger.LogDebug("按映射名称 '{MappedName}' 未找到属性，尝试原始表头 '{Header}'", propertyName, header);
                         property = typeof(T).GetProperty(header, BindingFlags.Public | BindingFlags.Instance | BindingFlags.IgnoreCase);
                    }
                    
                    if (property != null && property.CanWrite)
                    {
                        if (!string.IsNullOrEmpty(trimmedValue)) // Only set if value is not empty after trimming
                        {
                            try
                            {
                                object convertedValue = ConvertValueToType(trimmedValue, property.PropertyType);
                                property.SetValue(item, convertedValue);
                                _logger.LogDebug("通用设置成功: {Property}='{Value}'", property.Name, trimmedValue);
                                propertySet = true;
                }
                catch (Exception ex)
                {
                                _logger.LogWarning(ex, "通用设置失败: 无法将值 '{Value}' 转换为 {Type} 类型", trimmedValue, property.PropertyType.Name);
                            }
                        }
                        else {
                             _logger.LogDebug("通用设置跳过: 值为空. Property='{Property}'", property.Name);
                        }
                    }
                    else
                    {
                         _logger.LogDebug("通用设置失败: 未找到属性 '{PropertyName}' 或属性不可写", propertyName);
                    }
                }
                
                if (!propertySet) {
                     _logger.LogWarning("最终未能设置属性: 表头='{Header}', 值='{Value}'", header, value);
                }

                return propertySet;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "SetPropertyValue 发生异常: 表头='{Header}', 值='{Value}'", header, value);
                return false;
            }
        }

        private async Task<ImportResult<T>> ImportFromJsonAsync<T>(Stream fileStream, ImportOptions options) where T : class, new()
        {
            var result = new ImportResult<T>();
            
            using var reader = new StreamReader(fileStream);
            string json = await reader.ReadToEndAsync();
            
            try
            {
                List<T> items = JsonConvert.DeserializeObject<List<T>>(json);
                
                if (items == null || items.Count == 0)
                {
                    throw new InvalidOperationException("JSON文件不包含有效数据或格式不正确");
                }
                
                result.SuccessItems.AddRange(items);
                result.TotalRows = items.Count;
            }
            catch (Exception ex)
            {
                result.Errors[1] = $"解析JSON文件时发生错误: {ex.Message}";
                result.TotalRows = 1;
            }
            
            return result;
        }

        private List<string> ParseCsvLine(string line)
        {
            var values = new List<string>();
            
            if (string.IsNullOrEmpty(line))
            {
                return values;
            }
            
            int i = 0;
            bool inQuotes = false;
            var currentValue = new StringBuilder();
            
            while (i < line.Length)
            {
                char c = line[i];
                
                if (c == '"' && (i == 0 || line[i-1] != '\\'))
                {
                    inQuotes = !inQuotes;
                }
                else if (c == ',' && !inQuotes)
                {
                    values.Add(ParseCsvValue(currentValue.ToString()));
                    currentValue.Clear();
                }
                else
                {
                    currentValue.Append(c);
                }
                
                i++;
            }
            
            // 添加最后一个值
            values.Add(ParseCsvValue(currentValue.ToString()));
            
            return values;
        }

        private string ParseCsvValue(string value)
        {
            if (string.IsNullOrEmpty(value))
            {
                return string.Empty;
            }
            
            // 如果值被引号包围，则移除引号
            if (value.StartsWith("\"") && value.EndsWith("\""))
            {
                value = value.Substring(1, value.Length - 2);
            }
            
            // 替换转义字符
            value = value.Replace("\\\"", "\"");
            
            return value;
        }

        /// <summary>
        /// 从列值字典创建实体对象
        /// </summary>
        /// <typeparam name="T">实体类型</typeparam>
        /// <param name="values">列值字典，键为表头名称，值为单元格值</param>
        /// <param name="columnMappings">列映射字典</param>
        /// <returns>创建的实体对象</returns>
        private T CreateItemFromValues<T>(Dictionary<string, string> values, Dictionary<string, string> columnMappings = null) where T : class, new()
        {
            try
            {
                if (values == null || values.Count == 0)
                {
                    _logger.LogWarning("CreateItemFromValues: 值字典为空，无法创建对象");
                    return null;
                }
                
                _logger.LogInformation("[CreateItemFromValues] 创建实体 {Type}，有 {Count} 个值", typeof(T).Name, values.Count);
                
            var item = new T();
            
                // 首先处理Asset相关的关键字段
                bool isAssetType = typeof(T).Name == "Asset";
                
                if (isAssetType)
                {
                    _logger.LogInformation("[CreateItemFromValues] 创建的是Asset类型，开始处理关键属性");
                    
                    // 1. 优先处理资产编码（最重要的字段）
                    var assetCodeValues = values.Where(kv => 
                        kv.Key.Contains("资产编码") || kv.Key.Contains("资产编号") || 
                        kv.Key.Contains("AssetCode") || kv.Key.Equals("编码", StringComparison.OrdinalIgnoreCase))
                        .ToList();
                        
                    if (assetCodeValues.Any())
                    {
                        var codeEntry = assetCodeValues.First();
                        if (!string.IsNullOrEmpty(codeEntry.Value?.Trim()))
                        {
                            var codeProp = typeof(T).GetProperty("AssetCode", BindingFlags.Public | BindingFlags.Instance | BindingFlags.IgnoreCase);
                            if (codeProp != null && codeProp.CanWrite)
                            {
                                string trimmedCode = codeEntry.Value.Trim();
                                _logger.LogInformation("[CreateItemFromValues] 直接设置资产编码: '{Value}'", trimmedCode);
                                
                                try
                                {
                                    codeProp.SetValue(item, trimmedCode);
                                    // 立即验证设置成功
                                    var setCode = codeProp.GetValue(item)?.ToString();
                                    _logger.LogInformation("[CreateItemFromValues] 资产编码设置后的值: '{Value}'", setCode);
                                    
                                    if (string.IsNullOrEmpty(setCode))
                                    {
                                        _logger.LogWarning("[CreateItemFromValues] 资产编码设置后仍为空，再次尝试");
                                        codeProp.SetValue(item, trimmedCode);
                                    }
                                }
                                catch (Exception ex)
                                {
                                    _logger.LogError(ex, "[CreateItemFromValues] 设置资产编码时失败: {Message}", ex.Message);
                                }
                            }
                        }
                    }
                    
                    // 2. 处理资产名称
                    var nameValues = values.Where(kv => 
                        kv.Key.Contains("资产名称") || kv.Key.Contains("Name") || 
                        kv.Key.Equals("名称", StringComparison.OrdinalIgnoreCase))
                        .ToList();
                        
                    if (nameValues.Any())
                    {
                        var nameEntry = nameValues.First();
                        if (!string.IsNullOrEmpty(nameEntry.Value?.Trim()))
                        {
                            var nameProp = typeof(T).GetProperty("Name", BindingFlags.Public | BindingFlags.Instance | BindingFlags.IgnoreCase);
                            if (nameProp != null && nameProp.CanWrite)
                            {
                                string trimmedName = nameEntry.Value.Trim();
                                _logger.LogInformation("[CreateItemFromValues] 直接设置资产名称: '{Value}'", trimmedName);
                                nameProp.SetValue(item, trimmedName);
                            }
                        }
                    }
                    
                    // 3. 处理资产类型
                    var typeValues = values.Where(kv => 
                        kv.Key.Contains("资产类型") || kv.Key.Contains("AssetType") || 
                        kv.Key.Equals("类型", StringComparison.OrdinalIgnoreCase))
                        .ToList();
                        
                    if (typeValues.Any())
                    {
                        var typeEntry = typeValues.First();
                        if (!string.IsNullOrEmpty(typeEntry.Value?.Trim()))
                        {
                            var typeProp = typeof(T).GetProperty("AssetTypeName", BindingFlags.Public | BindingFlags.Instance | BindingFlags.IgnoreCase);
                            if (typeProp != null && typeProp.CanWrite)
                            {
                                string trimmedType = typeEntry.Value.Trim();
                                _logger.LogInformation("[CreateItemFromValues] 直接设置资产类型: '{Value}'", trimmedType);
                                typeProp.SetValue(item, trimmedType);
                            }
                        }
                    }
                    
                    // 4. 处理位置
                    var locationValues = values.Where(kv => 
                        kv.Key.Contains("位置") || kv.Key.Contains("Location") || 
                        kv.Key.Equals("地点", StringComparison.OrdinalIgnoreCase))
                        .ToList();
                        
                    if (locationValues.Any())
                    {
                        var locationEntry = locationValues.First();
                        if (!string.IsNullOrEmpty(locationEntry.Value?.Trim()))
                        {
                            var locationProp = typeof(T).GetProperty("LocationName", BindingFlags.Public | BindingFlags.Instance | BindingFlags.IgnoreCase);
                            if (locationProp != null && locationProp.CanWrite)
                            {
                                string trimmedLocation = locationEntry.Value.Trim();
                                _logger.LogInformation("[CreateItemFromValues] 直接设置位置: '{Value}'", trimmedLocation);
                                locationProp.SetValue(item, trimmedLocation);
                            }
                        }
                    }
                }
                
                // 然后处理其他所有属性
                int setCount = 0;
                
                // 输出所有值用于调试
                foreach (var pair in values)
                {
                    _logger.LogInformation("[CreateItemFromValues] 设置属性: 表头='{Header}', 值='{Value}'", pair.Key, pair.Value);
                    
                    // 通用属性设置 - 去除空格后再判断是否为空
                    string trimmedValue = pair.Value?.Trim();

                    if (!string.IsNullOrEmpty(trimmedValue))
                    {
                        bool wasSet = SetPropertyValue(item, pair.Key, trimmedValue, columnMappings);
                        if (wasSet)
                        {
                            setCount++;
                        }
                    }
                    else
                    {
                         _logger.LogInformation("[CreateItemFromValues] 跳过空值属性: 表头='{Header}'", pair.Key);
                    }
                }
                
                _logger.LogInformation("[CreateItemFromValues] 成功设置 {SetCount}/{TotalCount} 个属性值", setCount, values.Count);
                
                // 输出对象的所有已设置属性
                var props = typeof(T).GetProperties()
                    .Where(p => p.CanRead)
                    .Select(p => new { Name = p.Name, Value = p.GetValue(item) })
                    .Where(x => x.Value != null)
                    .ToList();
                
                if (props.Any())
                {
                    _logger.LogInformation("[CreateItemFromValues] 创建的对象属性值: {Properties}", 
                        string.Join(", ", props.Select(p => $"{p.Name}='{p.Value}'")));
                }
                else
                {
                    _logger.LogWarning("[CreateItemFromValues] 创建的对象没有任何属性被成功设置");
                }

                // 最终检查资产编码是否成功设置
                if (isAssetType)
                {
                    var finalCodeProp = typeof(T).GetProperty("AssetCode");
                    var finalCode = finalCodeProp?.GetValue(item)?.ToString();
                    _logger.LogWarning("[CreateItemFromValues] 方法返回前，item 的 AssetCode 最终为: '{FinalCode}'", finalCode);
            }
            
            return item;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[CreateItemFromValues] 创建实体对象时发生异常: {Message}", ex.Message);
                return null;
            }
        }

        private object ConvertValueToType(string value, Type targetType)
        {
            if (string.IsNullOrEmpty(value))
            {
                // 处理空值
                if (targetType.IsValueType && Nullable.GetUnderlyingType(targetType) == null)
                {
                    return Activator.CreateInstance(targetType); // 返回默认值
                }
                return null;
            }
            
            // 处理可空类型
            Type underlyingType = Nullable.GetUnderlyingType(targetType) ?? targetType;
            
            // 根据目标类型进行转换
            if (underlyingType == typeof(string))
            {
                return value;
            }
            else if (underlyingType == typeof(int))
            {
                return int.Parse(value);
            }
            else if (underlyingType == typeof(long))
            {
                return long.Parse(value);
            }
            else if (underlyingType == typeof(double))
            {
                return double.Parse(value, CultureInfo.InvariantCulture);
            }
            else if (underlyingType == typeof(decimal))
            {
                return decimal.Parse(value, CultureInfo.InvariantCulture);
            }
            else if (underlyingType == typeof(float))
            {
                return float.Parse(value, CultureInfo.InvariantCulture);
            }
            else if (underlyingType == typeof(bool))
            {
                return bool.Parse(value) || 
                       value.Equals("1") || 
                       value.Equals("yes", StringComparison.OrdinalIgnoreCase) || 
                       value.Equals("true", StringComparison.OrdinalIgnoreCase);
            }
            else if (underlyingType == typeof(DateTime))
            {
                return DateTime.Parse(value);
            }
            else if (underlyingType == typeof(TimeSpan))
            {
                return TimeSpan.Parse(value);
            }
            else if (underlyingType == typeof(Guid))
            {
                return Guid.Parse(value);
            }
            else if (underlyingType.IsEnum)
            {
                return Enum.Parse(underlyingType, value, true);
            }
            else if (underlyingType == typeof(byte[]))
            {
                return Convert.FromBase64String(value);
            }
            else
            {
                // 使用 Newtonsoft.Json 处理复杂类型
                return JsonConvert.DeserializeObject(value, targetType);
            }
        }

        private string GetFileExtension(ImportFormat format)
        {
            return format switch
            {
                ImportFormat.Csv => "csv",
                ImportFormat.Excel => "xlsx",
                ImportFormat.Json => "json",
                _ => "txt"
            };
        }
    }
} 