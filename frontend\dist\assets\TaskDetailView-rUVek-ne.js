import{_ as a,B as e,r as t,c as s,z as l,l as n,m as i,b as u,$ as c,d as r,e as o,w as d,t as v,f as m,aJ as f,E as p,bq as y,ag as g,a as h,u as b,o as _,p as w,aL as k,i as D,aW as z,F as S,au as x,b2 as A,k as $,bE as C,aI as I,h as T,by as B,a9 as V,br as M}from"./index-CG5lHOPO.js";import{f as N,z as E}from"./zh-CN-B1csyosV.js";import"./en-US-BvtvdVHO.js";const U={class:"task-detail-view page-container"},F={key:0,class:"content-wrapper"},j={class:"page-header mb-4"},q={class:"header-left"},L={class:"page-title"},O={class:"header-right"},P={class:"card-header"},G={class:"info-grid"},H={class:"info-item"},J={class:"item-value"},K={class:"info-item"},R={class:"item-value"},W={class:"info-item"},Y={class:"item-value assignee"},Q={class:"info-item"},X={class:"item-value"},Z={class:"info-item"},aa={class:"info-item"},ea={class:"item-value"},ta={class:"info-item"},sa={class:"item-value"},la={class:"info-item"},na={class:"item-value"},ia={class:"info-item"},ua={class:"item-value"},ca={class:"description-section"},ra={class:"description-content"},oa={class:"card-header"},da={key:0,class:"attachments-list"},va={class:"attachment-name"},ma={class:"attachment-size"},fa={class:"attachment-actions"},pa={class:"comment-section"},ya={class:"comments-list thin-scrollbar"},ga={key:0},ha={class:"comment-content"},ba={class:"comment-header"},_a={class:"comment-user"},wa={class:"comment-time"},ka={class:"comment-text"},Da={class:"comment-form"},za={class:"activity-logs thin-scrollbar"},Sa={key:0},xa={class:"activity-content"},Aa={class:"activity-text"},$a={class:"activity-user"},Ca={class:"activity-time"},Ia={key:1,class:"task-not-found"},Ta=a({__name:"TaskDetailView",setup(a){const Ta=b(),Ba=e().params.id,Va=t(!0),Ma=t(!1),Na=t(!1),Ea=t(null),Ua=t([]),Fa=t([]),ja=t(""),qa=t(!1),La=t(!1),Oa=t(!1),Pa=t(),Ga=t([]),Ha=t(null),Ja=t("comments"),Ka=t([]),Ra=s((()=>Ga.value.map((a=>({name:a.name,size:a.size,uid:a.uid,status:a.status})))));async function Wa(){if(!Ba)return p.error("无效的任务ID"),void(Va.value=!1);Va.value=!0;try{if(0===Ka.value.length)try{const a=await y.getUserList({pageSize:1e3});a&&Array.isArray(a.list)?Ka.value=a.list:a&&Array.isArray(a)?Ka.value=a:Ka.value=[]}catch(a){Ka.value=[]}const e=await g.getTaskDetail(Ba);e&&e.data?(Ea.value=function(a){const e=a.endDate&&"completed"!==a.status&&new Date(a.endDate)<new Date;return{...a,isOverdue:e,originalApiStatus:a.status,attachments:Array.isArray(a.attachments)?a.attachments:[]}}(e.data),Ya(),async function(){if(!Ba)return;Oa.value=!0;try{const a=await g.getTaskActivityLog(Ba);Fa.value=(null==a?void 0:a.data)||[]}catch(a){Fa.value=[]}finally{Oa.value=!1}}()):(Ea.value=null,p.error((null==e?void 0:e.message)||"获取任务详情失败"))}catch(e){p.error("加载任务详情时出错"),Ea.value=null}finally{Va.value=!1}}async function Ya(){if(Ba){La.value=!0;try{const a=await g.getComments(Ba);Ua.value=(null==a?void 0:a.data)||[]}catch(a){Ua.value=[]}finally{La.value=!1}}}async function Qa(a){if(Ea.value&&a!==Ea.value.originalApiStatus){Ma.value=!0;try{await g.updateTaskStatus(Ba,{status:a}),p.success("任务状态更新成功"),Ea.value.originalApiStatus=a,Ea.value.isOverdue=fe(Ea.value)}catch(e){p.error("更新任务状态失败"),Ea.value.status=Ea.value.originalApiStatus}finally{Ma.value=!1}}}async function Xa(){if(!ja.value.trim()||!Ba)return;if(qa.value)return void p.warning("正在提交中，请稍候...");qa.value=!0;const a=ja.value.trim();try{await g.addComment(Ba,{content:a}),await Ya(),ja.value="",p.success("评论已发表")}catch(e){p.error("发表评论失败")}finally{setTimeout((()=>{qa.value=!1}),1e3)}}function Za(){V.confirm("确定要删除此任务吗？此操作不可恢复。","确认删除任务",{confirmButtonText:"确认删除",cancelButtonText:"取消",type:"warning"}).then((async()=>{Na.value=!0;try{await g.deleteTask(Ba),p.success("任务删除成功"),Ta.push("/main/tasks/list")}catch(a){p.error("删除任务失败")}finally{Na.value=!1}})).catch((()=>p.info("删除已取消")))}function ae(a){const e=a.size/1024/1024<10;return e||p.error("附件大小不能超过 10MB!"),e}function ee(a,e){p.warning(`限制上传 5 个文件，本次选择了 ${a.length} 个`)}async function te(a){const{file:e,onSuccess:t,onError:s,onProgress:l}=a,n=new FormData;n.append("file",e);try{const a=await g.uploadTaskAttachment(Ba,n);if(!a||!a.success)throw new Error((null==a?void 0:a.message)||"上传失败");p.success(`${e.name} 上传成功`),t(a.data),Wa()}catch(i){p.error(`${e.name} 上传失败: ${i.message}`),s(i)}}function se(a,e){}function le(){Ta.go(-1)}function ne(a){return Ka.value.find((e=>e.id===a))||null}function ie(a){var e;return(null==(e=ne(a))?void 0:e.name)||"未知"}function ue(a){const e=ne(a);return M(null==e?void 0:e.avatar)}function ce(a){if(!a||"string"!=typeof a)return"?";const e=a.split(" ");let t=e[0].substring(0,1).toUpperCase();return e.length>1&&(t+=e[e.length-1].substring(0,1).toUpperCase()),t}function re(a){a.target.style.display="none"}function oe(a){return{low:"低",medium:"中",high:"高"}[a]||"中"}function de(a,e=!0){if(!a)return"";try{const t=new Date(a);if(isNaN(t.getTime()))return a;const s=t.getFullYear(),l=String(t.getMonth()+1).padStart(2,"0"),n=String(t.getDate()).padStart(2,"0");if(e){const a=String(t.getHours()).padStart(2,"0");return`${s}-${l}-${n} ${a}:${String(t.getMinutes()).padStart(2,"0")}`}return`${s}-${l}-${n}`}catch(t){return a}}function ve(a){if(!a)return"";try{const e=new Date(a);return isNaN(e.getTime())?"无效日期":N(e,{addSuffix:!0,locale:E})}catch(e){return"无效日期"}}function me(a){if(0===a)return"0 B";if(!a||isNaN(a)||a<0)return"N/A";const e=Math.floor(Math.log(a)/Math.log(1024));return parseFloat((a/Math.pow(1024,e)).toFixed(1))+" "+["B","KB","MB","GB"][e]}function fe(a){return a.endDate&&"completed"!==a.status&&new Date(a.endDate)<new Date}return l((()=>{Wa()})),(a,e)=>{const t=h("el-icon"),s=h("el-button"),l=h("el-option"),y=h("el-select"),b=h("el-tag"),M=h("el-avatar"),N=h("el-divider"),E=h("el-card"),Ta=h("el-empty"),Ga=h("el-col"),Ka=h("el-input"),Ya=h("el-tab-pane"),ne=h("el-tabs"),pe=h("el-row"),ye=i("loading");return n((_(),u("div",U,[Ea.value&&!Va.value?(_(),u("div",F,[r("div",j,[r("div",q,[o(s,{onClick:le,text:"",size:"small",class:"back-button"},{default:d((()=>[o(t,null,{default:d((()=>[o(m(k))])),_:1}),e[3]||(e[3]=w(" 返回 "))])),_:1}),r("h2",L,v(Ea.value.title),1)]),r("div",O,[o(s,{type:"danger",onClick:Za,icon:m(f),loading:Na.value},{default:d((()=>e[4]||(e[4]=[w("删除")]))),_:1},8,["icon","loading"])])]),o(pe,{gutter:20},{default:d((()=>[o(Ga,{xs:24,sm:16,md:17},{default:d((()=>[o(E,{shadow:"never",class:"details-card mb-4"},{header:d((()=>[r("div",P,[r("span",null,[o(t,null,{default:d((()=>[o(m(A))])),_:1}),e[5]||(e[5]=w(" 基本信息"))])])])),default:d((()=>{return[r("div",G,[r("div",H,[e[6]||(e[6]=r("span",{class:"item-label"},"状态:",-1)),r("span",J,[o(y,{modelValue:Ea.value.status,"onUpdate:modelValue":e[0]||(e[0]=a=>Ea.value.status=a),size:"small",onChange:Qa,loading:Ma.value},{default:d((()=>[o(l,{label:"未开始",value:"unstarted"}),o(l,{label:"进行中",value:"in-progress"}),o(l,{label:"已完成",value:"completed"})])),_:1},8,["modelValue","loading"])])]),r("div",K,[e[7]||(e[7]=r("span",{class:"item-label"},"优先级:",-1)),r("span",R,[o(b,{type:(a=Ea.value.priority,{low:"success",medium:"warning",high:"danger"}[a]||"info"),size:"small",effect:"light",round:""},{default:d((()=>[w(v(oe(Ea.value.priority)),1)])),_:1},8,["type"])])]),r("div",W,[e[8]||(e[8]=r("span",{class:"item-label"},"负责人:",-1)),r("span",Y,[o(M,{size:24,src:ue(Ea.value.assigneeId),class:"small-avatar",onError:re},{default:d((()=>[w(v(ce(ie(Ea.value.assigneeId))),1)])),_:1},8,["src"]),r("span",null,v(ie(Ea.value.assigneeId)),1)])]),r("div",Q,[e[9]||(e[9]=r("span",{class:"item-label"},"创建时间:",-1)),r("span",X,v(de(Ea.value.createDate)||"-"),1)]),r("div",Z,[e[10]||(e[10]=r("span",{class:"item-label"},"截止时间:",-1)),r("span",{class:D(["item-value",{overdue:fe(Ea.value)&&"completed"!==Ea.value.status}])},[o(t,null,{default:d((()=>[o(m(z))])),_:1}),w(" "+v(de(Ea.value.endDate,!1)||"未设置"),1)],2)]),Ea.value.isPeriodic?(_(),u(S,{key:0},[r("div",aa,[e[12]||(e[12]=r("span",{class:"item-label"},"任务类型:",-1)),r("span",ea,[o(b,{type:"success",size:"small"},{default:d((()=>e[11]||(e[11]=[w("周期性")]))),_:1})])]),r("div",ta,[e[13]||(e[13]=r("span",{class:"item-label"},"开始日期:",-1)),r("span",sa,v(de(Ea.value.startDate,!1)||"-"),1)]),r("div",la,[e[14]||(e[14]=r("span",{class:"item-label"},"结束日期:",-1)),r("span",na,v(de(Ea.value.endDate,!1)||"-"),1)]),r("div",ia,[e[15]||(e[15]=r("span",{class:"item-label"},"频率:",-1)),r("span",ua,v(Ea.value.frequency||"-"),1)])],64)):c("",!0)]),o(N),r("div",ca,[r("h4",null,[o(t,null,{default:d((()=>[o(m(x))])),_:1}),e[16]||(e[16]=w(" 任务描述"))]),r("p",ra,v(Ea.value.description||"暂无描述"),1)])];var a})),_:1}),o(E,{shadow:"never",class:"attachments-card mb-4"},{header:d((()=>[r("div",oa,[r("span",null,[o(t,null,{default:d((()=>[o(m(B))])),_:1}),e[17]||(e[17]=w(" 附件"))])])])),default:d((()=>[o(m(C),{ref_key:"uploadRef",ref:Pa,action:"#","http-request":te,"on-remove":se,"file-list":Ra.value,"before-upload":ae,"on-exceed":ee,multiple:"",limit:5,"list-type":"text",class:"upload-area"},{tip:d((()=>e[19]||(e[19]=[r("div",{class:"el-upload__tip"},"单个文件不超过10MB，最多上传5个文件",-1)]))),default:d((()=>[o(s,{type:"primary",icon:m(I)},{default:d((()=>e[18]||(e[18]=[w("点击上传")]))),_:1},8,["icon"])])),_:1},8,["file-list"]),Ea.value.attachments&&Ea.value.attachments.length>0?(_(),u("div",da,[(_(!0),u(S,null,T(Ea.value.attachments,(a=>(_(),u("div",{key:a.id||a.name,class:"attachment-item"},[o(t,null,{default:d((()=>[o(m(x))])),_:1}),r("span",va,v(a.name),1),r("span",ma,"("+v(me(a.size))+")",1),r("div",fa,[o(s,{type:"danger",link:"",size:"small",icon:m(f),onClick:e=>function(a){(null==a?void 0:a.id)&&V.confirm(`确定删除附件 "${a.name}"?`,"确认删除",{type:"warning"}).then((async()=>{Ha.value=a.id;try{await g.deleteTaskAttachment(Ba,a.id),p.success("附件删除成功"),Wa()}catch(e){p.error("删除附件失败")}finally{Ha.value=null}})).catch((()=>p.info("删除已取消")))}(a),loading:Ha.value===a.id},{default:d((()=>e[20]||(e[20]=[w("删除")]))),_:2},1032,["icon","onClick","loading"])])])))),128))])):(_(),$(Ta,{key:1,description:"暂无附件"}))])),_:1})])),_:1}),o(Ga,{xs:24,sm:8,md:7},{default:d((()=>[o(E,{shadow:"never",class:"activity-card"},{default:d((()=>[o(ne,{modelValue:Ja.value,"onUpdate:modelValue":e[2]||(e[2]=a=>Ja.value=a)},{default:d((()=>[o(Ya,{label:"评论",name:"comments",lazy:""},{default:d((()=>[r("div",pa,[n((_(),u("div",ya,[Ua.value.length>0?(_(),u("div",ga,[(_(!0),u(S,null,T(Ua.value,(a=>(_(),u("div",{key:a.id,class:"comment-item"},[o(M,{size:32,src:ue(a.userId),class:"comment-avatar",onError:re},{default:d((()=>[w(v(ce(ie(a.userId))),1)])),_:2},1032,["src"]),r("div",ha,[r("div",ba,[r("span",_a,v(ie(a.userId)),1),r("span",wa,v(ve(a.createDate||a.time)),1)]),r("div",ka,v(a.content),1)])])))),128))])):(_(),$(Ta,{key:1,description:"暂无评论"}))])),[[ye,La.value]]),r("div",Da,[o(Ka,{modelValue:ja.value,"onUpdate:modelValue":e[1]||(e[1]=a=>ja.value=a),type:"textarea",rows:3,placeholder:"输入评论...",disabled:qa.value,resize:"none"},null,8,["modelValue","disabled"]),o(s,{type:"primary",onClick:Xa,loading:qa.value,disabled:!ja.value.trim(),class:"submit-comment-btn"},{default:d((()=>e[21]||(e[21]=[w("发表")]))),_:1},8,["loading","disabled"])])])])),_:1}),o(Ya,{label:"活动日志",name:"logs",lazy:""},{default:d((()=>[n((_(),u("div",za,[Fa.value.length>0?(_(),u("div",Sa,[(_(!0),u(S,null,T(Fa.value,(a=>(_(),u("div",{key:a.id,class:"activity-item"},[o(M,{size:28,src:ue(a.userId),class:"activity-avatar",onError:re},{default:d((()=>[w(v(ce(ie(a.userId))),1)])),_:2},1032,["src"]),r("div",xa,[r("span",Aa,[r("span",$a,v(ie(a.userId)),1),w(" "+v(a.action),1)]),r("span",Ca,v(ve(a.createDate||a.time)),1)])])))),128))])):(_(),$(Ta,{key:1,description:"暂无活动记录"}))])),[[ye,Oa.value]])])),_:1})])),_:1},8,["modelValue"])])),_:1})])),_:1})])),_:1})])):Va.value?c("",!0):(_(),u("div",Ia,[o(Ta,{description:"任务不存在或加载失败"},{default:d((()=>[o(s,{onClick:le},{default:d((()=>e[22]||(e[22]=[w("返回任务列表")]))),_:1})])),_:1})]))])),[[ye,Va.value]])}}},[["__scopeId","data-v-b53e3b8e"]]);export{Ta as default};
