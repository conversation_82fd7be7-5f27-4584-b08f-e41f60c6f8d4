#nullable enable
// File: Application/Features/Tasks/Dtos/AttachmentDto.cs
// Description: 附件数据传输对象

using System;
using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Http;
using System.IO;

namespace ItAssetsSystem.Application.Features.Tasks.Dtos
{
    /// <summary>
    /// 附件数据传输对象
    /// </summary>
    public class AttachmentDto
    {
        /// <summary>
        /// 附件ID
        /// </summary>
        public long AttachmentId { get; set; }

        /// <summary>
        /// 所属任务ID
        /// </summary>
        public long? TaskId { get; set; }

        /// <summary>
        /// 所属任务标题
        /// </summary>
        public string TaskTitle { get; set; } = string.Empty;

        /// <summary>
        /// 所属任务评论ID
        /// </summary>
        public long? CommentId { get; set; }

        /// <summary>
        /// 上传者ID
        /// </summary>
        public int UploaderUserId { get; set; }

        /// <summary>
        /// 上传者名称
        /// </summary>
        public string UploaderUserName { get; set; } = string.Empty;

        /// <summary>
        /// 附件原始文件名
        /// </summary>
        public string FileName { get; set; } = string.Empty;

        /// <summary>
        /// 存储的文件名
        /// </summary>
        public string StoredFileName { get; set; } = string.Empty;

        /// <summary>
        /// 文件存储路径（相对路径或URL）
        /// </summary>
        public string FilePath { get; set; } = string.Empty;

        /// <summary>
        /// 文件大小（字节）
        /// </summary>
        public long FileSize { get; set; }

        /// <summary>
        /// 文件MIME类型
        /// </summary>
        public string FileType { get; set; } = string.Empty;

        /// <summary>
        /// 是否可预览
        /// </summary>
        public bool IsPreviewable { get; set; }

        /// <summary>
        /// 存储类型（e.g., "FileSystem", "AzureBlob"）
        /// </summary>
        public string StorageType { get; set; } = string.Empty;

        /// <summary>
        /// 上传时间
        /// </summary>
        public DateTime CreationTimestamp { get; set; }

        /// <summary>
        /// 下载URL
        /// </summary>
        public string DownloadUrl { get; set; } = string.Empty;

        /// <summary>
        /// 文件描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 文件大小（格式化显示）
        /// </summary>
        public string FormattedFileSize => FormatFileSize(FileSize);

        /// <summary>
        /// 文件扩展名
        /// </summary>
        public string Extension => Path.GetExtension(FileName).TrimStart('.');

        /// <summary>
        /// 上传者头像URL
        /// </summary>
        public string UploaderAvatarUrl { get; set; } = string.Empty;

        /// <summary>
        /// 缩略图URL（如果是图片）
        /// </summary>
        public string ThumbnailPath { get; set; } = string.Empty;

        /// <summary>
        /// 是否已删除
        /// </summary>
        public bool IsDeleted { get; set; }

        /// <summary>
        /// 格式化文件大小
        /// </summary>
        /// <param name="size">文件大小（字节）</param>
        /// <returns>格式化后的文件大小</returns>
        private string FormatFileSize(long size)
        {
            if (size < 1024)
                return $"{size} B";
            else if (size < 1024 * 1024)
                return $"{size / 1024.0:0.##} KB";
            else if (size < 1024 * 1024 * 1024)
                return $"{size / (1024.0 * 1024.0):0.##} MB";
            else
                return $"{size / (1024.0 * 1024.0 * 1024.0):0.##} GB";
        }
    }

    /// <summary>
    /// 上传附件请求DTO
    /// </summary>
    public class UploadAttachmentRequestDto
    {
        /// <summary>
        /// 所属任务ID
        /// </summary>
        [Required(ErrorMessage = "任务ID不能为空")]
        public long TaskId { get; set; }

        /// <summary>
        /// 文件（通过表单上传）
        /// </summary>
        [Required(ErrorMessage = "文件不能为空")]
        public IFormFile File { get; set; } = null!;

        /// <summary>
        /// 文件描述
        /// </summary>
        [StringLength(500, ErrorMessage = "文件描述不能超过500个字符")]
        public string? Description { get; set; }
    }

    /// <summary>
    /// 更新附件请求DTO
    /// </summary>
    public class UpdateAttachmentRequestDto
    {
        /// <summary>
        /// 文件名
        /// </summary>
        [StringLength(255, ErrorMessage = "文件名不能超过255个字符")]
        public string FileName { get; set; } = string.Empty;

        /// <summary>
        /// 文件描述
        /// </summary>
        [StringLength(500, ErrorMessage = "文件描述不能超过500个字符")]
        public string Description { get; set; } = string.Empty;
    }

    /// <summary>
    /// 附件查询参数DTO
    /// </summary>
    public class AttachmentQueryParametersDto
    {
        /// <summary>
        /// 任务ID
        /// </summary>
        [Required(ErrorMessage = "任务ID不能为空")]
        public long TaskId { get; set; }

        /// <summary>
        /// 页码（从1开始）
        /// </summary>
        public int PageNumber { get; set; } = 1;

        /// <summary>
        /// 每页大小
        /// </summary>
        public int PageSize { get; set; } = 20;

        /// <summary>
        /// 搜索关键词
        /// </summary>
        public string SearchTerm { get; set; } = string.Empty;

        /// <summary>
        /// 是否只显示图片
        /// </summary>
        public bool? OnlyImages { get; set; }

        /// <summary>
        /// 文件扩展名过滤
        /// </summary>
        public string Extension { get; set; } = string.Empty;

        /// <summary>
        /// 排序字段
        /// </summary>
        public string SortBy { get; set; } = "CreatedAt";

        /// <summary>
        /// 排序方向（asc或desc）
        /// </summary>
        public string SortDirection { get; set; } = "desc";
    }
} 