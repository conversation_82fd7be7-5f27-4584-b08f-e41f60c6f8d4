// File: Core/Services/CurrentUserService.cs
// Description: 当前用户上下文服务实现类

using System.Linq;
using System.Security.Claims;
using ItAssetsSystem.Core.Abstractions;
using Microsoft.AspNetCore.Http;

namespace ItAssetsSystem.Core.Services
{
    /// <summary>
    /// 当前用户上下文服务实现类
    /// </summary>
    public class CurrentUserService : ICurrentUserService
    {
        private readonly IHttpContextAccessor _httpContextAccessor;
        
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="httpContextAccessor">HTTP上下文访问器</param>
        public CurrentUserService(IHttpContextAccessor httpContextAccessor)
        {
            _httpContextAccessor = httpContextAccessor;
        }
        
        /// <summary>
        /// 当前登录用户ID
        /// </summary>
        public int UserId
        {
            get
            {
                // 首先尝试从uid声明获取
                var userId = _httpContextAccessor.HttpContext?.User?.FindFirstValue("uid");
                if (userId != null && int.TryParse(userId, out var id))
                {
                    return id;
                }

                // 如果uid不存在，则尝试从NameIdentifier获取
                userId = _httpContextAccessor.HttpContext?.User?.FindFirstValue(ClaimTypes.NameIdentifier);
                return userId != null && int.TryParse(userId, out id) ? id : 0;
            }
        }
        
        /// <summary>
        /// 当前登录用户名
        /// </summary>
        public string UserName => _httpContextAccessor.HttpContext?.User?.FindFirstValue(ClaimTypes.Name) ?? string.Empty;
        
        /// <summary>
        /// 当前用户是否已认证
        /// </summary>
        public bool IsAuthenticated => _httpContextAccessor.HttpContext?.User?.Identity?.IsAuthenticated ?? false;
        
        /// <summary>
        /// 获取当前用户ID
        /// </summary>
        /// <returns>当前用户ID，未登录则返回0</returns>
        public int GetCurrentUserId()
        {
            return UserId;
        }
        
        /// <summary>
        /// 当前用户是否有指定角色
        /// </summary>
        /// <param name="role">角色名称</param>
        /// <returns>是否有该角色</returns>
        public bool IsInRole(string role)
        {
            return _httpContextAccessor.HttpContext?.User?.IsInRole(role) ?? false;
        }
        
        /// <summary>
        /// 当前用户是否有指定权限
        /// </summary>
        /// <param name="permission">权限名称</param>
        /// <returns>是否有该权限</returns>
        public bool HasPermission(string permission)
        {
            // 实际项目中应从用户的权限列表中检查
            // 这里简单实现为检查是否有对应的声明
            return _httpContextAccessor.HttpContext?.User?.HasClaim(c => c.Type == "permission" && c.Value == permission) ?? false;
        }
    }
} 