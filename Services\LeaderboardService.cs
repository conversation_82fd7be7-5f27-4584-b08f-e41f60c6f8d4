// File: Services/LeaderboardService.cs
// Description: 排行榜服务实现

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using ItAssetsSystem.Infrastructure.Data;
using ItAssetsSystem.Services.Interfaces;

namespace ItAssetsSystem.Services
{
    /// <summary>
    /// 排行榜服务实现
    /// </summary>
    public class LeaderboardService : ILeaderboardService
    {
        private readonly AppDbContext _context;
        private readonly IMemoryCache _cache;
        private readonly ILogger<LeaderboardService> _logger;

        public LeaderboardService(
            AppDbContext context,
            IMemoryCache cache,
            ILogger<LeaderboardService> logger)
        {
            _context = context;
            _cache = cache;
            _logger = logger;
        }

        /// <summary>
        /// 获取排行榜
        /// </summary>
        public async Task<List<LeaderboardEntry>> GetLeaderboardAsync(string metric, string period = "alltime", int limit = 50)
        {
            var cacheKey = $"leaderboard:{metric}:{period}:{limit}";
            
            if (_cache.TryGetValue(cacheKey, out List<LeaderboardEntry>? cached))
            {
                return cached ?? new List<LeaderboardEntry>();
            }

            try
            {
                var dateFilter = GetDateFilter(period);
                
                var query = from stats in _context.GamificationUserStats
                           join user in _context.Users on stats.CoreUserId equals user.Id
                           where user.IsActive && (!dateFilter.HasValue || stats.LastUpdatedTimestamp >= dateFilter.Value)
                           group new { stats, user } by new { stats.CoreUserId, user.Username, user.Name, user.Department } into g
                           select new LeaderboardEntry
                           {
                               UserId = g.Key.CoreUserId,
                               UserName = g.Key.Name ?? g.Key.Username,
                               AvatarUrl = "", // 暂时设为空字符串，后续可以添加头像字段
                               Department = g.Key.Department != null ? g.Key.Department.Name : "",
                               Points = g.Sum(x => x.stats.PointsBalance),
                               Coins = g.Sum(x => x.stats.CoinsBalance),
                               Diamonds = g.Sum(x => x.stats.DiamondsBalance),
                               TasksCreated = g.Sum(x => x.stats.CreatedTasksCount),
                               TasksCompleted = g.Sum(x => x.stats.CompletedTasksCount),
                               TasksClaimed = g.Sum(x => x.stats.ClaimedTasksCount),
                               // 注释掉不存在的字段，这些功能将在后续版本中实现
                               // FaultsRecorded = g.Sum(x => x.stats.FaultsRecordedCount),
                               // MaintenanceCreated = g.Sum(x => x.stats.MaintenanceCreatedCount),
                               // AssetsUpdated = g.Sum(x => x.stats.AssetsUpdatedCount),
                               FaultsRecorded = 0,
                               MaintenanceCreated = 0,
                               AssetsUpdated = 0,
                               LastActiveTime = g.Max(x => x.stats.LastUpdatedTimestamp)
                           };

                // 根据指标排序
                query = metric switch
                {
                    LeaderboardMetrics.POINTS => query.OrderByDescending(e => e.Points),
                    LeaderboardMetrics.COINS => query.OrderByDescending(e => e.Coins),
                    LeaderboardMetrics.DIAMONDS => query.OrderByDescending(e => e.Diamonds),
                    LeaderboardMetrics.TASKS_CREATED => query.OrderByDescending(e => e.TasksCreated),
                    LeaderboardMetrics.TASKS_COMPLETED => query.OrderByDescending(e => e.TasksCompleted),
                    LeaderboardMetrics.TASKS_CLAIMED => query.OrderByDescending(e => e.TasksClaimed),
                    LeaderboardMetrics.FAULTS_RECORDED => query.OrderByDescending(e => e.FaultsRecorded),
                    LeaderboardMetrics.MAINTENANCE_CREATED => query.OrderByDescending(e => e.MaintenanceCreated),
                    LeaderboardMetrics.ASSETS_UPDATED => query.OrderByDescending(e => e.AssetsUpdated),
                    _ => query.OrderByDescending(e => e.Points)
                };

                var results = await query.Take(limit).ToListAsync();

                // 设置排名
                for (int i = 0; i < results.Count; i++)
                {
                    results[i].Rank = i + 1;
                }

                // 缓存5分钟
                _cache.Set(cacheKey, results, TimeSpan.FromMinutes(5));

                _logger.LogInformation("获取排行榜成功: metric={Metric}, period={Period}, count={Count}", metric, period, results.Count);
                return results;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取排行榜失败: metric={Metric}, period={Period}", metric, period);
                return new List<LeaderboardEntry>();
            }
        }

        /// <summary>
        /// 获取用户排名
        /// </summary>
        public async Task<UserRankInfo?> GetUserRankAsync(int userId, string metric, string period = "alltime")
        {
            try
            {
                var leaderboard = await GetLeaderboardAsync(metric, period, 1000); // 获取更多数据来计算排名
                var userEntry = leaderboard.FirstOrDefault(e => e.UserId == userId);
                
                if (userEntry == null)
                {
                    return null;
                }

                var totalUsers = await _context.GamificationUserStats
                    .Where(s => s.CoreUserId != userId)
                    .CountAsync();

                return new UserRankInfo
                {
                    UserId = userId,
                    UserName = userEntry.UserName,
                    Rank = userEntry.Rank,
                    Score = GetScoreByMetric(userEntry, metric),
                    TotalUsers = totalUsers + 1,
                    Percentile = Math.Round((double)(totalUsers - userEntry.Rank + 1) / (totalUsers + 1) * 100, 2)
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户排名失败: userId={UserId}, metric={Metric}", userId, metric);
                return null;
            }
        }

        /// <summary>
        /// 刷新排行榜缓存
        /// </summary>
        public async Task RefreshLeaderboardCacheAsync()
        {
            try
            {
                var metrics = new[] { 
                    LeaderboardMetrics.POINTS, 
                    LeaderboardMetrics.TASKS_COMPLETED, 
                    LeaderboardMetrics.TASKS_CREATED 
                };
                
                var periods = new[] { 
                    StatisticsPeriods.DAILY, 
                    StatisticsPeriods.WEEKLY, 
                    StatisticsPeriods.MONTHLY, 
                    StatisticsPeriods.ALLTIME 
                };

                foreach (var metric in metrics)
                {
                    foreach (var period in periods)
                    {
                        var cacheKey = $"leaderboard:{metric}:{period}:50";
                        _cache.Remove(cacheKey);
                        
                        // 预热缓存
                        await GetLeaderboardAsync(metric, period, 50);
                    }
                }

                _logger.LogInformation("排行榜缓存刷新完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "刷新排行榜缓存失败");
            }
        }

        /// <summary>
        /// 根据时间周期获取日期过滤条件
        /// </summary>
        private DateTime? GetDateFilter(string period)
        {
            var now = DateTime.Now;
            return period switch
            {
                StatisticsPeriods.DAILY => now.Date,
                StatisticsPeriods.WEEKLY => now.AddDays(-7),
                StatisticsPeriods.MONTHLY => now.AddMonths(-1),
                StatisticsPeriods.QUARTERLY => now.AddMonths(-3),
                StatisticsPeriods.YEARLY => now.AddYears(-1),
                _ => null // alltime
            };
        }

        /// <summary>
        /// 根据指标获取分数
        /// </summary>
        private int GetScoreByMetric(LeaderboardEntry entry, string metric)
        {
            return metric switch
            {
                LeaderboardMetrics.POINTS => entry.Points,
                LeaderboardMetrics.COINS => entry.Coins,
                LeaderboardMetrics.DIAMONDS => entry.Diamonds,
                LeaderboardMetrics.TASKS_CREATED => entry.TasksCreated,
                LeaderboardMetrics.TASKS_COMPLETED => entry.TasksCompleted,
                LeaderboardMetrics.TASKS_CLAIMED => entry.TasksClaimed,
                LeaderboardMetrics.FAULTS_RECORDED => entry.FaultsRecorded,
                LeaderboardMetrics.MAINTENANCE_CREATED => entry.MaintenanceCreated,
                LeaderboardMetrics.ASSETS_UPDATED => entry.AssetsUpdated,
                _ => entry.Points
            };
        }
    }
}
