using System;
using System.IO;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace ItAssetsSystem.Core.Helpers
{
    /// <summary>
    /// 文件操作帮助类
    /// </summary>
    public static class FileHelper
    {
        /// <summary>
        /// 确保目录存在，如不存在则创建
        /// </summary>
        /// <param name="directoryPath">目录路径</param>
        /// <param name="logger">日志记录器（可选）</param>
        /// <returns>目录是否存在或创建成功</returns>
        public static bool EnsureDirectoryExists(string directoryPath, ILogger logger = null)
        {
            try
            {
                if (!Directory.Exists(directoryPath))
                {
                    logger?.LogInformation("创建目录: {Path}", directoryPath);
                    Directory.CreateDirectory(directoryPath);
                }
                
                return true;
            }
            catch (Exception ex)
            {
                logger?.LogError(ex, "创建目录失败: {Path}", directoryPath);
                return false;
            }
        }
        
        /// <summary>
        /// 安全删除文件，忽略任何异常
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="logger">日志记录器（可选）</param>
        /// <returns>是否成功删除</returns>
        public static bool SafeDeleteFile(string filePath, ILogger logger = null)
        {
            try
            {
                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                    logger?.LogInformation("删除文件: {Path}", filePath);
                    return true;
                }
                
                return false;
            }
            catch (Exception ex)
            {
                logger?.LogError(ex, "删除文件失败: {Path}", filePath);
                return false;
            }
        }
        
        /// <summary>
        /// 读取文件内容为字符串
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="logger">日志记录器（可选）</param>
        /// <returns>文件内容</returns>
        public static async Task<string> ReadFileAsStringAsync(string filePath, ILogger logger = null)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    logger?.LogWarning("文件不存在: {Path}", filePath);
                    return null;
                }
                
                return await File.ReadAllTextAsync(filePath);
            }
            catch (Exception ex)
            {
                logger?.LogError(ex, "读取文件失败: {Path}", filePath);
                return null;
            }
        }
        
        /// <summary>
        /// 写入字符串到文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="content">内容</param>
        /// <param name="logger">日志记录器（可选）</param>
        /// <returns>是否写入成功</returns>
        public static async Task<bool> WriteStringToFileAsync(string filePath, string content, ILogger logger = null)
        {
            try
            {
                // 确保目录存在
                EnsureDirectoryExists(Path.GetDirectoryName(filePath), logger);
                
                await File.WriteAllTextAsync(filePath, content);
                logger?.LogInformation("写入文件: {Path}", filePath);
                return true;
            }
            catch (Exception ex)
            {
                logger?.LogError(ex, "写入文件失败: {Path}", filePath);
                return false;
            }
        }
        
        /// <summary>
        /// 复制文件
        /// </summary>
        /// <param name="sourceFile">源文件路径</param>
        /// <param name="destFile">目标文件路径</param>
        /// <param name="overwrite">是否覆盖</param>
        /// <param name="logger">日志记录器（可选）</param>
        /// <returns>是否复制成功</returns>
        public static bool CopyFile(string sourceFile, string destFile, bool overwrite = true, ILogger logger = null)
        {
            try
            {
                if (!File.Exists(sourceFile))
                {
                    logger?.LogWarning("源文件不存在: {Path}", sourceFile);
                    return false;
                }
                
                // 确保目标目录存在
                EnsureDirectoryExists(Path.GetDirectoryName(destFile), logger);
                
                File.Copy(sourceFile, destFile, overwrite);
                logger?.LogInformation("复制文件: {Source} -> {Destination}", sourceFile, destFile);
                return true;
            }
            catch (Exception ex)
            {
                logger?.LogError(ex, "复制文件失败: {Source} -> {Destination}", sourceFile, destFile);
                return false;
            }
        }
        
        /// <summary>
        /// 创建包含随机名称的临时文件
        /// </summary>
        /// <param name="extension">文件扩展名（包含点号）</param>
        /// <param name="logger">日志记录器（可选）</param>
        /// <returns>临时文件路径</returns>
        public static string CreateTempFile(string extension = ".tmp", ILogger logger = null)
        {
            try
            {
                string fileName = $"{Path.GetRandomFileName()}{extension}";
                string tempFilePath = Path.Combine(Path.GetTempPath(), fileName);
                
                using (File.Create(tempFilePath)) { }
                
                logger?.LogInformation("创建临时文件: {Path}", tempFilePath);
                return tempFilePath;
            }
            catch (Exception ex)
            {
                logger?.LogError(ex, "创建临时文件失败");
                return null;
            }
        }
    }
} 