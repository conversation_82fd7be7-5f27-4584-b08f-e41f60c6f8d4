-- =====================================================
-- 游戏化系统升级 - 数据库结构扩展
-- =====================================================

-- 1. 用户等级配置表
CREATE TABLE IF NOT EXISTS user_levels (
    level INT PRIMARY KEY COMMENT '等级',
    name VARCHAR(50) NOT NULL COMMENT '等级名称',
    required_xp INT NOT NULL COMMENT '所需经验值',
    reward_coins INT NOT NULL DEFAULT 0 COMMENT '金币奖励',
    reward_diamonds INT NOT NULL DEFAULT 0 COMMENT '钻石奖励',
    unlock_features JSON COMMENT '解锁特权',
    icon_url VARCHAR(255) COMMENT '等级图标',
    color VARCHAR(7) COMMENT '等级颜色',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB COMMENT='用户等级配置表';

-- 2. 用户等级历史表
CREATE TABLE IF NOT EXISTS user_level_history (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL COMMENT '用户ID',
    old_level INT COMMENT '原等级',
    new_level INT NOT NULL COMMENT '新等级',
    level_up_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '升级时间',
    rewards_granted JSON COMMENT '发放的奖励',
    xp_at_levelup INT COMMENT '升级时的经验值',
    INDEX idx_user_id (user_id),
    INDEX idx_level_up_time (level_up_time)
) ENGINE=InnoDB COMMENT='用户等级历史表';

-- 3. 道具配置表
CREATE TABLE IF NOT EXISTS gamification_items (
    item_id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL COMMENT '道具名称',
    code VARCHAR(50) UNIQUE NOT NULL COMMENT '道具代码',
    type VARCHAR(50) NOT NULL COMMENT '道具类型',
    rarity VARCHAR(20) DEFAULT 'Common' COMMENT '稀有度：Common, Rare, Epic, Legendary',
    effect TEXT COMMENT '道具效果描述',
    effect_duration INT COMMENT '效果持续时间(分钟)',
    drop_rate DECIMAL(5,4) DEFAULT 0.01 COMMENT '掉落概率',
    icon_url VARCHAR(255) COMMENT '道具图标',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_rarity (rarity),
    INDEX idx_type (type)
) ENGINE=InnoDB COMMENT='道具配置表';

-- 4. 用户道具背包表
CREATE TABLE IF NOT EXISTS user_items (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL COMMENT '用户ID',
    item_id BIGINT NOT NULL COMMENT '道具ID',
    quantity INT DEFAULT 1 COMMENT '数量',
    obtained_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '获得时间',
    obtained_source VARCHAR(50) COMMENT '获得来源',
    INDEX idx_user_id (user_id),
    INDEX idx_item_id (item_id),
    FOREIGN KEY (item_id) REFERENCES gamification_items(item_id)
) ENGINE=InnoDB COMMENT='用户道具背包表';

-- 5. 道具效果状态表
CREATE TABLE IF NOT EXISTS active_item_effects (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL COMMENT '用户ID',
    item_id BIGINT NOT NULL COMMENT '道具ID',
    effect_type VARCHAR(50) NOT NULL COMMENT '效果类型',
    multiplier DECIMAL(3,2) DEFAULT 1.0 COMMENT '效果倍数',
    expires_at DATETIME COMMENT '过期时间',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_expires_at (expires_at),
    FOREIGN KEY (item_id) REFERENCES gamification_items(item_id)
) ENGINE=InnoDB COMMENT='道具效果状态表';

-- 6. 每日限制表
CREATE TABLE IF NOT EXISTS daily_limits (
    user_id INT NOT NULL COMMENT '用户ID',
    limit_date DATE NOT NULL COMMENT '限制日期',
    points_earned INT DEFAULT 0 COMMENT '已获得积分',
    max_points INT DEFAULT 200 COMMENT '最大积分',
    items_obtained INT DEFAULT 0 COMMENT '已获得道具数',
    max_items INT DEFAULT 5 COMMENT '最大道具数',
    tasks_completed INT DEFAULT 0 COMMENT '已完成任务数',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (user_id, limit_date)
) ENGINE=InnoDB COMMENT='每日限制表';

-- 7. 扩展现有gamification_userstats表
ALTER TABLE gamification_userstats
ADD COLUMN IF NOT EXISTS level_progress DECIMAL(5,2) DEFAULT 0.00 COMMENT '等级进度百分比',
ADD COLUMN IF NOT EXISTS total_items_obtained INT DEFAULT 0 COMMENT '总获得道具数',
ADD COLUMN IF NOT EXISTS rare_items_count INT DEFAULT 0 COMMENT '稀有道具数量',
ADD COLUMN IF NOT EXISTS epic_items_count INT DEFAULT 0 COMMENT '史诗道具数量',
ADD COLUMN IF NOT EXISTS legendary_items_count INT DEFAULT 0 COMMENT '传说道具数量';

-- =====================================================
-- 初始化等级配置数据
-- =====================================================

INSERT INTO user_levels (level, name, required_xp, reward_coins, reward_diamonds, unlock_features, color) VALUES
(1, '新手上路', 0, 50, 0, '["basic_dashboard"]', '#8BC34A'),
(2, '初级用户', 100, 75, 1, '["task_templates"]', '#4CAF50'),
(3, '熟练用户', 250, 100, 2, '["advanced_search"]', '#2196F3'),
(4, '经验用户', 500, 150, 3, '["bulk_operations"]', '#03A9F4'),
(5, '资产专家', 1000, 200, 5, '["advanced_reports", "custom_views"]', '#00BCD4'),
(6, '维修能手', 1800, 300, 8, '["priority_support"]', '#009688'),
(7, '系统达人', 3000, 400, 12, '["system_config"]', '#FF9800'),
(8, '管理大师', 5000, 600, 20, '["admin_panel"]', '#FF5722'),
(9, '资深专家', 8000, 800, 30, '["data_export"]', '#9C27B0'),
(10, '传奇用户', 12000, 1200, 50, '["golden_badge", "vip_support"]', '#E91E63'),
(11, '超级管理员', 18000, 1500, 75, '["full_access"]', '#F44336'),
(12, '系统之神', 25000, 2000, 100, '["god_mode"]', '#FFD700');

-- =====================================================
-- 初始化道具配置数据
-- =====================================================

INSERT INTO gamification_items (name, code, type, rarity, effect, effect_duration, drop_rate, icon_url) VALUES
-- 普通道具 (60%概率)
('效率提升卡', 'EFFICIENCY_BOOST', 'buff', 'Common', '任务完成速度提升10%', 60, 0.30, '/icons/efficiency.png'),
('积分加成卡', 'POINT_BONUS', 'buff', 'Common', '获得积分增加20%', 120, 0.20, '/icons/points.png'),
('经验药水', 'XP_POTION', 'consumable', 'Common', '立即获得50经验值', 0, 0.10, '/icons/xp_potion.png'),

-- 稀有道具 (25%概率)
('双倍积分卡', 'DOUBLE_POINTS', 'buff', 'Rare', '2小时内获得双倍积分', 120, 0.15, '/icons/double_points.png'),
('任务透视镜', 'TASK_VISION', 'tool', 'Rare', '查看所有任务详细信息', 180, 0.08, '/icons/task_vision.png'),
('金币宝箱', 'COIN_CHEST', 'consumable', 'Rare', '立即获得100-300金币', 0, 0.02, '/icons/coin_chest.png'),

-- 史诗道具 (10%概率)
('时空加速器', 'TIME_WARP', 'tool', 'Epic', '立即完成一个进行中任务', 0, 0.05, '/icons/time_warp.png'),
('资产透视镜', 'ASSET_VISION', 'skill', 'Epic', '查看资产完整历史记录', 1440, 0.03, '/icons/asset_vision.png'),
('三倍经验卡', 'TRIPLE_XP', 'buff', 'Epic', '3小时内获得三倍经验', 180, 0.02, '/icons/triple_xp.png'),

-- 传说道具 (5%概率)
('万能钥匙', 'MASTER_KEY', 'tool', 'Legendary', '解锁所有高级功能24小时', 1440, 0.003, '/icons/master_key.png'),
('时间回溯器', 'TIME_REWIND', 'tool', 'Legendary', '撤销最近一次操作', 0, 0.001, '/icons/time_rewind.png'),
('钻石宝库', 'DIAMOND_VAULT', 'consumable', 'Legendary', '立即获得50-100钻石', 0, 0.001, '/icons/diamond_vault.png');

-- =====================================================
-- 创建升级检测存储过程
-- =====================================================

DELIMITER //

CREATE PROCEDURE CheckAndProcessLevelUp(IN p_user_id INT)
BEGIN
    DECLARE v_current_xp INT DEFAULT 0;
    DECLARE v_current_level INT DEFAULT 1;
    DECLARE v_next_level INT;
    DECLARE v_required_xp INT;
    DECLARE v_reward_coins INT;
    DECLARE v_reward_diamonds INT;
    DECLARE v_level_name VARCHAR(50);
    DECLARE v_unlock_features JSON;
    DECLARE v_done INT DEFAULT FALSE;
    
    -- 获取用户当前状态
    SELECT CurrentXP, CurrentLevel
    INTO v_current_xp, v_current_level
    FROM gamification_userstats
    WHERE CoreUserId = p_user_id;
    
    -- 检查是否可以升级
    level_check_loop: LOOP
        SET v_next_level = v_current_level + 1;
        
        -- 获取下一级要求
        SELECT required_xp, reward_coins, reward_diamonds, name, unlock_features
        INTO v_required_xp, v_reward_coins, v_reward_diamonds, v_level_name, v_unlock_features
        FROM user_levels 
        WHERE level = v_next_level;
        
        -- 如果没有找到下一级配置，退出循环
        IF v_required_xp IS NULL THEN
            LEAVE level_check_loop;
        END IF;
        
        -- 如果经验值足够升级
        IF v_current_xp >= v_required_xp THEN
            -- 更新用户等级和奖励
            UPDATE gamification_userstats
            SET CurrentLevel = v_next_level,
                CoinsBalance = CoinsBalance + v_reward_coins,
                DiamondsBalance = DiamondsBalance + v_reward_diamonds,
                level_progress = 0.00
            WHERE CoreUserId = p_user_id;
            
            -- 记录升级历史
            INSERT INTO user_level_history (user_id, old_level, new_level, xp_at_levelup, rewards_granted)
            VALUES (p_user_id, v_current_level, v_next_level, v_current_xp, 
                    JSON_OBJECT('coins', v_reward_coins, 'diamonds', v_reward_diamonds, 'features', v_unlock_features));
            
            -- 记录游戏化日志
            INSERT INTO gamification_log (UserId, ActionType, PointsGained, XpGained, CoinsGained, DiamondsGained, Description)
            VALUES (p_user_id, 'LEVEL_UP', 0, 0, v_reward_coins, v_reward_diamonds,
                    CONCAT('升级到 ', v_level_name, ' (等级 ', v_next_level, ')'));
            
            -- 更新当前等级，继续检查下一级
            SET v_current_level = v_next_level;
        ELSE
            -- 计算等级进度
            DECLARE v_prev_required_xp INT DEFAULT 0;
            
            SELECT COALESCE(required_xp, 0) INTO v_prev_required_xp
            FROM user_levels 
            WHERE level = v_current_level;
            
            UPDATE gamification_userstats
            SET level_progress = ROUND(((v_current_xp - v_prev_required_xp) / (v_required_xp - v_prev_required_xp)) * 100, 2)
            WHERE CoreUserId = p_user_id;
            
            LEAVE level_check_loop;
        END IF;
    END LOOP;
    
END//

DELIMITER ;

-- =====================================================
-- 创建道具掉落存储过程
-- =====================================================

DELIMITER //

CREATE PROCEDURE GrantRandomItem(IN p_user_id INT, IN p_source VARCHAR(50))
BEGIN
    DECLARE v_rarity VARCHAR(20);
    DECLARE v_item_id BIGINT;
    DECLARE v_item_name VARCHAR(100);
    DECLARE v_random_value DECIMAL(6,5);
    
    -- 生成随机数决定稀有度
    SET v_random_value = RAND();
    
    -- 根据来源和随机数确定稀有度
    SET v_rarity = CASE 
        WHEN p_source = 'TASK_COMPLETE' THEN
            CASE 
                WHEN v_random_value < 0.05 THEN 'Legendary'
                WHEN v_random_value < 0.15 THEN 'Epic'
                WHEN v_random_value < 0.40 THEN 'Rare'
                ELSE 'Common'
            END
        WHEN p_source = 'LEVEL_UP' THEN
            CASE 
                WHEN v_random_value < 0.10 THEN 'Legendary'
                WHEN v_random_value < 0.30 THEN 'Epic'
                WHEN v_random_value < 0.60 THEN 'Rare'
                ELSE 'Common'
            END
        ELSE 'Common'
    END;
    
    -- 从对应稀有度中随机选择道具
    SELECT item_id, name INTO v_item_id, v_item_name
    FROM gamification_items 
    WHERE rarity = v_rarity AND is_active = TRUE
    ORDER BY RAND() * drop_rate DESC
    LIMIT 1;
    
    -- 如果找到道具，发放给用户
    IF v_item_id IS NOT NULL THEN
        INSERT INTO user_items (user_id, item_id, obtained_source)
        VALUES (p_user_id, v_item_id, p_source);
        
        -- 更新用户统计
        UPDATE gamification_userstats
        SET total_items_obtained = total_items_obtained + 1,
            rare_items_count = rare_items_count + CASE WHEN v_rarity = 'Rare' THEN 1 ELSE 0 END,
            epic_items_count = epic_items_count + CASE WHEN v_rarity = 'Epic' THEN 1 ELSE 0 END,
            legendary_items_count = legendary_items_count + CASE WHEN v_rarity = 'Legendary' THEN 1 ELSE 0 END
        WHERE CoreUserId = p_user_id;
        
        -- 记录获得道具日志
        INSERT INTO gamification_log (UserId, ActionType, Description, Context)
        VALUES (p_user_id, 'ITEM_OBTAINED',
                CONCAT('获得', v_rarity, '道具: ', v_item_name),
                JSON_OBJECT('item_id', v_item_id, 'rarity', v_rarity, 'source', p_source));
    END IF;
    
END//

DELIMITER ;

-- =====================================================
-- 创建每日限制检查函数
-- =====================================================

DELIMITER //

CREATE FUNCTION CanEarnPoints(p_user_id INT, p_points_to_earn INT) RETURNS BOOLEAN
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE v_points_earned INT DEFAULT 0;
    DECLARE v_max_points INT DEFAULT 200;
    
    -- 获取今日已获得积分
    SELECT COALESCE(points_earned, 0), max_points 
    INTO v_points_earned, v_max_points
    FROM daily_limits 
    WHERE user_id = p_user_id AND limit_date = CURDATE();
    
    -- 检查是否超过限制
    RETURN (v_points_earned + p_points_to_earn) <= v_max_points;
END//

DELIMITER ;

-- =====================================================
-- 更新现有用户的等级信息
-- =====================================================

UPDATE gamification_userstats us
JOIN (
    SELECT
        us.CoreUserId,
        COALESCE(MAX(ul.level), 1) as calculated_level
    FROM gamification_userstats us
    LEFT JOIN user_levels ul ON us.CurrentXP >= ul.required_xp
    GROUP BY us.CoreUserId
) calc ON us.CoreUserId = calc.CoreUserId
SET us.CurrentLevel = calc.calculated_level
WHERE us.CurrentLevel IS NULL OR us.CurrentLevel = 0;

-- =====================================================
-- 创建成长时间线视图
-- =====================================================

CREATE OR REPLACE VIEW user_growth_timeline AS
SELECT
    gl.UserId as user_id,
    u.name as user_name,
    DATE(gl.CreatedAt) as timeline_date,
    gl.ActionType as event_type,
    COUNT(*) as event_count,
    SUM(gl.PointsGained) as points_earned,
    SUM(gl.XpGained) as xp_earned,
    SUM(gl.CoinsGained) as coins_earned,
    SUM(gl.DiamondsGained) as diamonds_earned,
    GROUP_CONCAT(DISTINCT gl.Description ORDER BY gl.CreatedAt DESC SEPARATOR '|') as milestones
FROM gamification_log gl
JOIN users u ON gl.UserId = u.id
WHERE gl.CreatedAt >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
GROUP BY gl.UserId, DATE(gl.CreatedAt), gl.ActionType
ORDER BY timeline_date DESC, user_id;

-- =====================================================
-- 创建排行榜视图
-- =====================================================

CREATE OR REPLACE VIEW enhanced_leaderboard AS
SELECT
    us.CoreUserId as user_id,
    u.name as user_name,
    d.name as department_name,
    us.PointsBalance as total_points,
    us.CurrentLevel as current_level,
    ul.name as level_name,
    ul.color as level_color,
    us.CompletedTasksCount as tasks_completed,
    us.total_items_obtained,
    us.rare_items_count + us.epic_items_count + us.legendary_items_count as special_items_count,
    RANK() OVER (ORDER BY us.PointsBalance DESC) as points_rank,
    RANK() OVER (ORDER BY us.CurrentLevel DESC, us.CurrentXP DESC) as level_rank,
    RANK() OVER (ORDER BY us.CompletedTasksCount DESC) as tasks_rank
FROM gamification_userstats us
JOIN users u ON us.CoreUserId = u.id
LEFT JOIN user_levels ul ON us.CurrentLevel = ul.level
LEFT JOIN departments d ON u.department_id = d.id
WHERE u.IsDeleted = 0
ORDER BY us.PointsBalance DESC;
