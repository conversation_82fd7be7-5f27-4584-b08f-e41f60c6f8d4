import{_ as e,r as a,c as t,z as i,b as s,e as n,w as l,E as o,a as c,u as r,o as d,d as u,$ as p,k as f,F as k,h as g,i as m,a6 as v,t as y,aB as C,p as h,H as T,au as S,S as z,bO as _,ao as A,a9 as $}from"./index-CG5lHOPO.js";import{u as b}from"./notification-Cv7sgGe-.js";const w={class:"notifications-page"},x={class:"card-header"},M={class:"header-actions"},I={class:"notification-list"},P=["onClick"],O={class:"notification-icon"},D={class:"notification-content"},U={class:"notification-title"},V={class:"notification-message"},j={class:"notification-time"},H={class:"notification-actions"},N={key:0,class:"pagination-wrapper"},F={class:"notification-list"},R=["onClick"],B={class:"notification-icon"},E={class:"notification-content"},L={class:"notification-title"},Y={class:"notification-message"},q={class:"notification-time"},G={class:"notification-actions"},J={class:"notification-list"},K=["onClick"],Q={class:"notification-icon"},W={class:"notification-content"},X={class:"notification-title"},Z={class:"notification-message"},ee={class:"notification-time"},ae={class:"notification-actions"},te=e({__name:"NotificationsListView",setup(e){const te=r(),ie=b(),se=a("all"),ne=a(!1),le=a(0),oe=a({currentPage:1,pageSize:20}),ce=t((()=>ie.notifications)),re=t((()=>ie.hasUnread)),de=t((()=>ce.value.filter((e=>["TaskAssigned","TaskStatusChanged","TaskComment","TaskAttachmentAdded","TaskMention","TaskOverdue","TaskContentChanged"].includes(e.type))))),ue=t((()=>ce.value.filter((e=>!["TaskAssigned","TaskStatusChanged","TaskComment","TaskAttachmentAdded","TaskMention","TaskOverdue","TaskContentChanged"].includes(e.type))))),pe=async()=>{ne.value=!0;try{await ie.fetchNotifications({page:oe.value.currentPage,pageSize:oe.value.pageSize,force:!0}),le.value=ie.total}catch(e){o.error("加载通知失败")}finally{ne.value=!1}},fe=()=>{},ke=async e=>{try{await ie.markAsRead(e),o.success("已标记为已读")}catch(a){o.error("标记已读失败")}},ge=async()=>{try{await ie.markAllAsRead(),o.success("已全部标记为已读")}catch(e){o.error("全部标记已读失败")}},me=async e=>{try{await $.confirm("确定要删除此通知吗？","删除通知",{type:"warning"}),await ie.deleteNotification(e),o.success("删除成功")}catch(a){"cancel"!==a&&o.error("删除通知失败")}},ve=e=>{ke(e.id),"Task"===e.referenceType&&e.referenceId?te.push(`/main/tasks/detail/${e.referenceId}`):"Task"===e.resourceType&&e.resourceId?te.push(`/main/tasks/detail/${e.resourceId}`):e.taskId&&te.push(`/main/tasks/detail/${e.taskId}`)},ye=e=>{oe.value.pageSize=e,oe.value.currentPage=1,pe()},Ce=e=>{oe.value.currentPage=e,pe()},he=e=>{if(!e)return"";const a=new Date(e),t=new Date-a;if(t<864e5){if(t<36e5){return`${Math.floor(t/6e4)} 分钟前`}return`${Math.floor(t/36e5)} 小时前`}if(t<6048e5){return`星期${["日","一","二","三","四","五","六"][a.getDay()]} ${a.getHours().toString().padStart(2,"0")}:${a.getMinutes().toString().padStart(2,"0")}`}return`${a.getFullYear()}-${(a.getMonth()+1).toString().padStart(2,"0")}-${a.getDate().toString().padStart(2,"0")} ${a.getHours().toString().padStart(2,"0")}:${a.getMinutes().toString().padStart(2,"0")}`},Te=e=>{switch(e){case"TaskAssigned":case"TaskAttachmentAdded":case"TaskContentChanged":return S;case"TaskStatusChanged":return A;case"TaskComment":return _;case"TaskMention":case"Test":default:return T;case"TaskOverdue":return z}},Se=e=>{switch(e){case"TaskAssigned":case"TaskMention":return"#67c23a";case"TaskOverdue":return"#f56c6c";case"TaskStatusChanged":case"TaskContentChanged":return"#e6a23c";case"TaskComment":case"TaskAttachmentAdded":return"#409eff";default:return"#909399"}};return i((()=>{pe()})),(e,a)=>{const t=c("el-button"),i=c("el-empty"),o=c("el-icon"),r=c("el-pagination"),T=c("el-tab-pane"),S=c("el-tabs"),z=c("el-card");return d(),s("div",w,[n(z,{class:"notifications-card"},{header:l((()=>[u("div",x,[a[4]||(a[4]=u("h2",null,"通知中心",-1)),u("div",M,[re.value?(d(),f(t,{key:0,type:"primary",onClick:ge},{default:l((()=>a[3]||(a[3]=[h(" 全部标为已读 ")]))),_:1})):p("",!0)])])])),default:l((()=>[n(S,{modelValue:se.value,"onUpdate:modelValue":a[2]||(a[2]=e=>se.value=e),onTabClick:fe},{default:l((()=>[n(T,{label:"全部",name:"all"},{default:l((()=>[u("div",I,[0===ce.value.length?(d(),f(i,{key:0,description:"暂无通知"})):p("",!0),(d(!0),s(k,null,g(ce.value,(e=>(d(),s("div",{key:e.id,class:m(["notification-item",{unread:!e.read}]),onClick:a=>ve(e)},[u("div",O,[n(o,{size:20,color:Se(e.type)},{default:l((()=>[(d(),f(v(Te(e.type))))])),_:2},1032,["color"])]),u("div",D,[u("div",U,y(e.title),1),u("div",V,y(e.content||e.message),1),u("div",j,y(he(e.timestamp||e.creationTimestamp)),1)]),u("div",H,[e.read?p("",!0):(d(),f(t,{key:0,type:"text",size:"small",onClick:C((a=>ke(e.id)),["stop"])},{default:l((()=>a[5]||(a[5]=[h(" 标为已读 ")]))),_:2},1032,["onClick"])),n(t,{type:"text",size:"small",onClick:C((a=>me(e.id)),["stop"])},{default:l((()=>a[6]||(a[6]=[h(" 删除 ")]))),_:2},1032,["onClick"])])],10,P)))),128))]),ce.value.length>0?(d(),s("div",N,[n(r,{"current-page":oe.value.currentPage,"onUpdate:currentPage":a[0]||(a[0]=e=>oe.value.currentPage=e),"page-size":oe.value.pageSize,"onUpdate:pageSize":a[1]||(a[1]=e=>oe.value.pageSize=e),"page-sizes":[10,20,50,100],small:!1,disabled:ne.value,background:!0,layout:"total, sizes, prev, pager, next, jumper",total:le.value,onSizeChange:ye,onCurrentChange:Ce},null,8,["current-page","page-size","disabled","total"])])):p("",!0)])),_:1}),n(T,{label:"任务",name:"task"},{default:l((()=>[u("div",F,[0===de.value.length?(d(),f(i,{key:0,description:"暂无任务通知"})):p("",!0),(d(!0),s(k,null,g(de.value,(e=>(d(),s("div",{key:e.id,class:m(["notification-item",{unread:!e.read}]),onClick:a=>ve(e)},[u("div",B,[n(o,{size:20,color:Se(e.type)},{default:l((()=>[(d(),f(v(Te(e.type))))])),_:2},1032,["color"])]),u("div",E,[u("div",L,y(e.title),1),u("div",Y,y(e.content||e.message),1),u("div",q,y(he(e.timestamp||e.creationTimestamp)),1)]),u("div",G,[e.read?p("",!0):(d(),f(t,{key:0,type:"text",size:"small",onClick:C((a=>ke(e.id)),["stop"])},{default:l((()=>a[7]||(a[7]=[h(" 标为已读 ")]))),_:2},1032,["onClick"])),n(t,{type:"text",size:"small",onClick:C((a=>me(e.id)),["stop"])},{default:l((()=>a[8]||(a[8]=[h(" 删除 ")]))),_:2},1032,["onClick"])])],10,R)))),128))])])),_:1}),n(T,{label:"系统",name:"system"},{default:l((()=>[u("div",J,[0===ue.value.length?(d(),f(i,{key:0,description:"暂无系统通知"})):p("",!0),(d(!0),s(k,null,g(ue.value,(e=>(d(),s("div",{key:e.id,class:m(["notification-item",{unread:!e.read}]),onClick:a=>ve(e)},[u("div",Q,[n(o,{size:20,color:Se(e.type)},{default:l((()=>[(d(),f(v(Te(e.type))))])),_:2},1032,["color"])]),u("div",W,[u("div",X,y(e.title),1),u("div",Z,y(e.content||e.message),1),u("div",ee,y(he(e.timestamp||e.creationTimestamp)),1)]),u("div",ae,[e.read?p("",!0):(d(),f(t,{key:0,type:"text",size:"small",onClick:C((a=>ke(e.id)),["stop"])},{default:l((()=>a[9]||(a[9]=[h(" 标为已读 ")]))),_:2},1032,["onClick"])),n(t,{type:"text",size:"small",onClick:C((a=>me(e.id)),["stop"])},{default:l((()=>a[10]||(a[10]=[h(" 删除 ")]))),_:2},1032,["onClick"])])],10,K)))),128))])])),_:1})])),_:1},8,["modelValue"])])),_:1})])}}},[["__scopeId","data-v-7b829eff"]]);export{te as default};
