@echo off
setlocal enabledelayedexpansion
chcp 65001 > nul

set "output_file=文件夹列表.txt"
set "output_path=%cd%\%output_file%"

:: 自动重命名防止覆盖已有文件
if exist "%output_path%" (
    set "output_file=文件夹列表_%random%.txt"
    set "output_path=%cd%\%output_file%"
    echo 检测到重复文件名，自动重命名为: %output_file%
)

echo 正在获取当前目录的文件夹列表...

:: 写入头部信息
echo 当前目录下的所有文件夹: > "%output_path%"
echo. >> "%output_path%"

:: 遍历并写入文件夹名
for /d %%a in (*) do (
    echo %%a >> "%output_path%"
)

echo. >> "%output_path%"
echo 文件夹列表已保存到: %output_path% >> "%output_path%"

echo.
echo 文件夹列表已保存到: %output_path%
echo 完成!
pause