// IT资产管理系统 - 系统配置选项
// 文件路径: /Core/Configuration/SystemOptions.cs
// 功能: 定义系统全局配置选项

using System.Collections.Generic;

namespace ItAssetsSystem.Core.Configuration
{
    /// <summary>
    /// 系统配置选项
    /// </summary>
    public class SystemOptions
    {
        /// <summary>
        /// 系统名称
        /// </summary>
        public string SystemName { get; set; } = "IT资产管理系统";

        /// <summary>
        /// 系统版本
        /// </summary>
        public string Version { get; set; } = "1.0.0";

        /// <summary>
        /// 是否开启断网操作队列
        /// </summary>
        public bool EnableOfflineQueue { get; set; } = true;

        /// <summary>
        /// 断网操作队列最大容量
        /// </summary>
        public int OfflineQueueMaxCapacity { get; set; } = 1000;

        /// <summary>
        /// 位置层级配置文件路径
        /// </summary>
        public string LocationTemplateFile { get; set; } = "Configurations/location-templates.json";

        /// <summary>
        /// 默认的位置层级类型
        /// </summary>
        public List<LocationLevelType> DefaultLocationLevels { get; set; } = new List<LocationLevelType>();
    }

    /// <summary>
    /// 位置层级类型
    /// </summary>
    public class LocationLevelType
    {
        /// <summary>
        /// 层级类型ID
        /// </summary>
        public int Type { get; set; }

        /// <summary>
        /// 层级名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 层级描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 层级顺序
        /// </summary>
        public int Order { get; set; }

        /// <summary>
        /// 是否允许在此层级存放资产
        /// </summary>
        public bool AllowStoreAssets { get; set; }

        /// <summary>
        /// 是否允许在此层级分配人员
        /// </summary>
        public bool AllowAssignUsers { get; set; }
    }
}

// 计划行数: 50
// 实际行数: 50 