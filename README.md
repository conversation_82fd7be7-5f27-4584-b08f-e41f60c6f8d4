# IT资产管理系统

IT资产管理系统是一个基于.NET 6和EF Core的Web API项目，用于管理企业IT资产、故障记录和位置信息。

## 功能特点

- 资产信息管理：记录IT设备基本信息、位置、状态等
- 资产分类管理：灵活配置资产类型和分类
- 位置管理：管理资产存放位置和层级关系
- 故障管理：记录和跟踪设备故障和维修情况
- 用户和权限管理：基于角色的访问控制
- API接口：完整的RESTful API支持

## 技术栈

- .NET 6
- Entity Framework Core 6
- MySQL/MariaDB
- Swagger/OpenAPI
- Serilog

## 快速开始

### 先决条件

- .NET 6 SDK
- MySQL/MariaDB 数据库

### 安装步骤

1. 克隆仓库
```bash
git clone https://github.com/yourusername/ItAssetsSystem.git
cd ItAssetsSystem
```

2. 修改数据库连接字符串
编辑`appsettings.json`和`appsettings.Development.json`中的数据库连接配置。

3. 运行应用程序
```bash
dotnet run
```

4. 访问API文档
打开浏览器，访问 `https://localhost:5001/swagger` 查看API文档。

## 项目结构

- `Controllers/`: API控制器
- `Data/`: 数据访问层和DbContext
- `Models/`: 实体模型和视图模型
- `Services/`: 业务逻辑服务
- `Configurations/`: 配置文件

## 贡献

欢迎贡献代码、报告问题或提出改进建议。

## 许可证

MIT 

## 任务管理模块实现情况

任务管理模块是基于Clean Architecture架构设计的新模块。主要包含以下功能：

### 主要功能

1. **任务类型支持**
   - 日常任务：常规的单次性任务
   - 周期性任务：可配置频率自动生成的重复任务
   - PDCA任务：支持计划(P)、执行(D)、检查(C)、行动(A)四个阶段的改进任务

2. **任务生命周期管理**
   - 任务创建、分配、更新
   - 任务状态流转：未开始→进行中→已完成（或已取消）
   - 任务进度、截止日期与逾期提醒
   - 任务附件与评论管理

3. **任务关联实体**
   - 资产关联：任务可关联到具体资产
   - 位置关联：任务可关联到特定位置
   - 人员关联：任务参与者、创建者、负责人

4. **游戏化元素**
   - 任务积分奖励：完成任务可获得积分
   - 健康值管理：任务逾期减少健康值
   - 任务成就系统：累计完成特定任务获得徽章

### 技术实现

1. **领域模型**
   - 实体：Task, Comment, Attachment, TaskHistory, PeriodicTaskSchedule, PdcaPlan
   - 枚举：TaskStatus, TaskPriority, TaskType, PDCAStage

2. **架构设计**
   - 采用Clean Architecture架构，分为Domain, Application, Infrastructure, Api四层
   - 利用CQRS模式分离读写操作
   - 事件驱动设计：任务状态变更触发领域事件

3. **数据存储**
   - 使用BIGINT类型主键，区别于核心模块的INT类型主键
   - EF Core作为ORM框架
   - 实现仓储模式封装数据访问逻辑

4. **API设计**
   - RESTful风格API
   - 使用/api/v2路由前缀
   - 标准化请求/响应格式

5. **横切关注点**
   - 日志记录：详细记录操作日志
   - 异常处理：统一的异常捕获与处理
   - 认证授权：基于JWT的身份认证

### 实现状态

- [x] 领域模型设计与实现
- [x] 仓储层实现
- [x] 服务层实现
- [x] API控制器实现
- [x] 枚举转换工具类
- [ ] 单元测试覆盖
- [ ] 前端界面集成

### 已知问题

目前主要问题是枚举类型定义冲突，系统中同时存在Models.Entities和Models.Enums下的同名枚举定义，已通过EnumExtensions提供临时解决方案，后续应重构为单一枚举定义源。

详情请参见CHANGELOG.md文件中的变更记录。 