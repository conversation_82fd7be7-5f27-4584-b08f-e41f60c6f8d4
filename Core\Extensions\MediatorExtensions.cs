// File: Core/Extensions/MediatorExtensions.cs
// Description: MediatR扩展方法

using System.Collections.Generic;
using System.Threading.Tasks;
using MediatR;
using Microsoft.Extensions.DependencyInjection;

namespace ItAssetsSystem.Core.Extensions
{
    /// <summary>
    /// MediatR扩展方法
    /// </summary>
    public static class MediatorExtensions
    {
        /// <summary>
        /// 添加MediatR服务
        /// </summary>
        public static IServiceCollection AddMediatorServices(this IServiceCollection services)
        {
            services.AddMediatR(cfg => 
            {
                cfg.RegisterServicesFromAssembly(typeof(MediatorExtensions).Assembly);
            });
            
            return services;
        }
    }
} 