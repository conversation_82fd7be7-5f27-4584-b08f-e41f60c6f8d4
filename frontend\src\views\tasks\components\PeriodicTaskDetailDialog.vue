<template>
  <el-dialog
    v-model="visible"
    title="周期性任务详情"
    width="800px"
    @close="handleClose"
  >
    <div v-if="schedule" class="schedule-detail">
      <div class="detail-grid">
        <div class="detail-item">
          <label>计划名称:</label>
          <span>{{ schedule.name }}</span>
        </div>
        <div class="detail-item">
          <label>描述:</label>
          <span>{{ schedule.description || '无' }}</span>
        </div>
        <div class="detail-item">
          <label>重复类型:</label>
          <span>{{ getRecurrenceText(schedule.recurrenceType) }}</span>
        </div>
        <div class="detail-item">
          <label>状态:</label>
          <el-tag :type="getStatusTagType(schedule.status)">
            {{ getStatusText(schedule.status) }}
          </el-tag>
        </div>
        <div class="detail-item">
          <label>启用状态:</label>
          <el-tag :type="schedule.isEnabled ? 'success' : 'info'">
            {{ schedule.isEnabled ? '已启用' : '已禁用' }}
          </el-tag>
        </div>
        <div class="detail-item">
          <label>下次执行:</label>
          <span>{{ formatDateTime(schedule.nextGenerationTime) || '未设置' }}</span>
        </div>
        <div class="detail-item">
          <label>已生成次数:</label>
          <span>{{ schedule.occurrencesGenerated }}</span>
        </div>
        <div class="detail-item">
          <label>创建时间:</label>
          <span>{{ formatDateTime(schedule.creationTimestamp) }}</span>
        </div>
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleEdit">编辑</el-button>
        <el-button type="danger" @click="handleDelete">删除</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  schedule: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:modelValue', 'close', 'edit', 'delete'])

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const handleClose = () => {
  emit('close')
}

const handleEdit = () => {
  emit('edit', props.schedule)
}

const handleDelete = () => {
  emit('delete', props.schedule)
}

const getRecurrenceText = (type) => {
  const texts = {
    'Daily': '每日',
    'Weekly': '每周',
    'Monthly': '每月',
    'Yearly': '每年',
    'CustomCron': '自定义'
  }
  return texts[type] || type
}

const getStatusTagType = (status) => {
  const types = {
    'Active': 'success',
    'Paused': 'warning',
    'Completed': 'info',
    'Expired': 'danger',
    'Error': 'danger'
  }
  return types[status] || ''
}

const getStatusText = (status) => {
  const texts = {
    'Active': '活动中',
    'Paused': '已暂停',
    'Completed': '已完成',
    'Expired': '已过期',
    'Error': '错误'
  }
  return texts[status] || status
}

const formatDateTime = (dateStr) => {
  if (!dateStr) return ''
  const date = new Date(dateStr)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    hour12: false // 使用24小时制
  })
}
</script>

<style scoped>
.schedule-detail {
  padding: 16px 0;
}

.detail-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.detail-item label {
  font-weight: 600;
  color: #606266;
  min-width: 80px;
}

.dialog-footer {
  text-align: right;
}
</style>