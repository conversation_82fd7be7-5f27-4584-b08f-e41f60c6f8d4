import request from '@/utils/request'

// 区域管理API
export const zoneApi = {
  // 获取所有区域配置
  getZoneConfigs() {
    return request({
      url: '/v2/zones/configs',
      method: 'get'
    })
  },

  // 获取指定区域的工位列表
  getZoneWorkstations(zoneId) {
    return request({
      url: `/v2/zones/${zoneId}/workstations`,
      method: 'get'
    })
  },

  // 更新区域配置
  updateZoneConfig(zoneId, config) {
    return request({
      url: `/v2/zones/${zoneId}/config`,
      method: 'put',
      data: config
    })
  },

  // 添加工位到区域
  addWorkstationToZone(zoneId, workstationData) {
    return request({
      url: `/v2/zones/${zoneId}/workstations`,
      method: 'post',
      data: workstationData
    })
  },

  // 从区域移除工位
  removeWorkstationFromZone(zoneId, workstationId) {
    return request({
      url: `/v2/zones/${zoneId}/workstations/${workstationId}`,
      method: 'delete'
    })
  },

  // 更新工位位置
  updateWorkstationPosition(workstationId, position) {
    return request({
      url: `/v2/workstations/${workstationId}/position`,
      method: 'put',
      data: position
    })
  },

  // 批量更新工位位置
  batchUpdateWorkstationPositions(updates) {
    return request({
      url: '/v2/workstations/positions/batch',
      method: 'put',
      data: { updates }
    })
  },

  // 获取工位详细信息
  getWorkstationDetail(workstationId) {
    return request({
      url: `/v2/workstations/${workstationId}`,
      method: 'get'
    })
  },

  // 更新工位状态
  updateWorkstationStatus(workstationId, status) {
    return request({
      url: `/v2/workstations/${workstationId}/status`,
      method: 'put',
      data: { status }
    })
  },

  // 获取区域统计信息
  getZoneStatistics(zoneId) {
    return request({
      url: `/v2/zones/${zoneId}/statistics`,
      method: 'get'
    })
  },

  // 获取所有区域统计信息
  getAllZoneStatistics() {
    return request({
      url: '/v2/zones/statistics',
      method: 'get'
    })
  }
}

// 工位布局配置
export const workstationLayoutConfig = {
  // 7个区域的详细配置
  zones: {
    zone1: {
      id: 1,
      name: '区域1',
      color: '#4A90E2',
      type: 'grid',
      layout: { rows: 9, cols: 1 },
      workstationIds: [1, 2, 3, 4, 5, 6, 7, 8, 9],
      description: '右侧蓝色区域 - 1列9行'
    },
    zone2: {
      id: 2,
      name: '区域2',
      color: '#F5A623',
      type: 'grid',
      layout: { rows: 3, cols: 7 },
      workstationIds: Array.from({length: 21}, (_, i) => i + 10),
      description: '中央黄色区域 - 3行7列'
    },
    zone3: {
      id: 3,
      name: '区域3',
      color: '#4A90E2',
      type: 'grid',
      layout: { rows: 3, cols: 1 },
      workstationIds: [31, 32, 33],
      description: '左下蓝色区域 - 1列3行'
    },
    zone4: {
      id: 4,
      name: '区域4',
      color: '#F8A5C2',
      type: 'complex',
      parts: [
        { type: 'grid', layout: { rows: 4, cols: 1 }, workstationIds: [34, 35, 36, 37] },
        { type: 'grid', layout: { rows: 3, cols: 3 }, workstationIds: [38, 39, 40, 41, 42, 43, 44, 45, 46] }
      ],
      workstationIds: Array.from({length: 13}, (_, i) => i + 34),
      description: '左上粉色区域 - 1列4行 + 3行3列'
    },
    zone5: {
      id: 5,
      name: '区域5',
      color: '#4A90E2',
      type: 'grid',
      layout: { rows: 5, cols: 1 },
      workstationIds: [47, 48, 49, 50, 51],
      description: '左侧蓝色区域 - 1列5行'
    },
    zone6: {
      id: 6,
      name: '区域6',
      color: '#50E3C2',
      type: 'complex',
      parts: [
        { name: '左部', count: 24, workstationIds: Array.from({length: 24}, (_, i) => i + 52) },
        { name: '中部', count: 31, workstationIds: Array.from({length: 31}, (_, i) => i + 76) },
        { name: '右部', count: 7, workstationIds: Array.from({length: 7}, (_, i) => i + 107) }
      ],
      workstationIds: Array.from({length: 62}, (_, i) => i + 52),
      description: '上方青色区域 - 左24个 + 中31个 + 右7个'
    },
    zone7: {
      id: 7,
      name: '区域7',
      color: '#4A90E2',
      type: 'grid',
      layout: { rows: 7, cols: 2 },
      workstationIds: Array.from({length: 14}, (_, i) => i + 114),
      description: '右上蓝色区域 - 14个工位'
    }
  },

  // 获取指定区域的配置
  getZoneConfig(zoneId) {
    return this.zones[`zone${zoneId}`] || null
  },

  // 获取工位所属区域
  getWorkstationZone(workstationId) {
    for (const [zoneKey, zone] of Object.entries(this.zones)) {
      if (zone.workstationIds.includes(workstationId)) {
        return zone
      }
    }
    return null
  },

  // 验证工位ID是否有效
  isValidWorkstationId(workstationId) {
    return workstationId >= 1 && workstationId <= 127
  },

  // 获取所有工位ID
  getAllWorkstationIds() {
    const allIds = []
    Object.values(this.zones).forEach(zone => {
      allIds.push(...zone.workstationIds)
    })
    return allIds.sort((a, b) => a - b)
  }
}

export default zoneApi
