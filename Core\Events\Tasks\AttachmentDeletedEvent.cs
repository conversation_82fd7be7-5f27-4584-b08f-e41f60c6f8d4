// File: Core/Events/Tasks/AttachmentDeletedEvent.cs
// Description: 任务附件删除时发布的领域事件

#nullable enable
using System;
using MediatR;

namespace ItAssetsSystem.Core.Events.Tasks
{
    /// <summary>
    /// 表示一个任务附件已被删除的事件。
    /// </summary>
    public class AttachmentDeletedEvent : INotification
    {
        /// <summary>
        /// 被删除的附件ID (BIGINT)
        /// </summary>
        public long AttachmentId { get; }

        /// <summary>
        /// 附件所属的任务ID (BIGINT)
        /// </summary>
        public long TaskId { get; }

        /// <summary>
        /// 被删除的附件文件名
        /// </summary>
        public string FileName { get; }

        /// <summary>
        /// 执行删除操作的用户ID (INT)
        /// </summary>
        public int DeletedByUserId { get; }

        /// <summary>
        /// 删除时间戳
        /// </summary>
        public DateTime DeletedTimestamp { get; }

        public AttachmentDeletedEvent(
            long attachmentId,
            long taskId,
            string fileName,
            int deletedByUserId)
        {
            AttachmentId = attachmentId;
            TaskId = taskId;
            FileName = fileName ?? throw new ArgumentNullException(nameof(fileName));
            DeletedByUserId = deletedByUserId;
            DeletedTimestamp = DateTime.Now;
        }
    }
} 