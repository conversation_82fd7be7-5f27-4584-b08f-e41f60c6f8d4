import request from '@/utils/request'

// 通知API
export const notificationApi = {
  // 获取用户通知列表
  getNotifications(params = {}) {
    return request({
      url: '/v2/notifications',
      method: 'get',
      params
    })
  },

  // 获取未读通知数量
  getUnreadCount() {
    return request({
      url: '/v2/notifications/unread-count',
      method: 'get'
    })
  },

  // 标记通知为已读
  markAsRead(id) {
    return request({
      url: `/v2/notifications/${id}/read`,
      method: 'put'
    })
  },

  // 标记所有通知为已读
  markAllAsRead() {
    return request({
      url: '/v2/notifications/read-all',
      method: 'put'
    })
  },

  // 删除通知
  deleteNotification(id) {
    return request({
      url: `/v2/notifications/${id}`,
      method: 'delete'
    })
  },

  // 发送测试通知
  sendTestNotification() {
    return request({
      url: '/v2/notificationtest/send',
      method: 'post'
    })
  },

  // 发送测试事件
  sendTestEvent() {
    return request({
      url: '/v2/notificationtest/send-event',
      method: 'post'
    })
  },

  // 获取通知连接状态
  getConnectionStatus() {
    return request({
      url: '/v2/notificationtest/connection-status',
      method: 'get'
    })
  }
}

export default notificationApi