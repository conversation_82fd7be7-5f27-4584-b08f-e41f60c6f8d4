// File: Core/Events/Tasks/TaskCreatedEvent.cs
// Description: 任务创建时发布的领域事件

using MediatR;
using System;

namespace ItAssetsSystem.Core.Events.Tasks
{
    /// <summary>
    /// 表示一个任务已被创建的事件。
    /// </summary>
    public class TaskCreatedEvent : INotification
    {
        /// <summary>
        /// 任务ID (V2 TaskId, BIGINT)
        /// </summary>
        public long TaskId { get; }

        /// <summary>
        /// 任务名称
        /// </summary>
        public string TaskName { get; }

        /// <summary>
        /// 创建者用户ID (Core User Id, INT)
        /// </summary>
        public int CreatorUserId { get; }

        /// <summary>
        /// 负责人用户ID (Core User Id, INT), 可能为空
        /// </summary>
        public int? AssigneeUserId { get; }

        /// <summary>
        /// 任务类型 (来自 V2 Task.TaskType, string)
        /// </summary>
        public string TaskType { get; }

        /// <summary>
        /// 计划完成日期, 可能为空
        /// </summary>
        public DateTime? PlanEndDate { get; }

        /// <summary>
        /// 任务积分 (来自 V2 Task.Points, int)
        /// </summary>
        public int Points { get; }

        /// <summary>
        /// 任务创建时间戳 (来自 V2 Task.CreationTimestamp, DateTime)
        /// </summary>
        public DateTime CreationTimestamp { get; }

        public TaskCreatedEvent(
            long taskId,
            string taskName,
            int creatorUserId,
            int? assigneeUserId,
            string taskType,
            DateTime? planEndDate,
            int points,
            DateTime creationTimestamp)
        {
            TaskId = taskId;
            TaskName = taskName ?? throw new ArgumentNullException(nameof(taskName));
            CreatorUserId = creatorUserId;
            AssigneeUserId = assigneeUserId;
            TaskType = taskType ?? throw new ArgumentNullException(nameof(taskType));
            PlanEndDate = planEndDate;
            Points = points;
            CreationTimestamp = creationTimestamp;
        }
    }
} 