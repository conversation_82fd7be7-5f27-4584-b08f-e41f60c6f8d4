/**
 * 航空航天级IT资产管理系统 - 审计日志页面
 * 文件路径: src/views/system/logs.vue
 * 功能描述: 系统操作日志的查询和管理
 */

<template>
  <div class="audit-logs-container">
    <!-- 页面标题和操作按钮 -->
    <div class="page-header">
      <h2 class="page-title">审计日志</h2>
      <div class="page-actions">
        <el-button type="primary" @click="handleExportData" :icon="Download">
          导出数据
        </el-button>
        <el-button type="warning" @click="handleClearLogs" :icon="Delete">
          清理日志
        </el-button>
      </div>
    </div>

    <!-- 筛选条件 -->
    <el-card class="filter-card">
      <div class="filter-container">
        <el-form :inline="true" :model="filterForm" class="filter-form">
          <el-form-item label="操作用户">
            <el-input v-model="filterForm.username" placeholder="操作用户" clearable />
          </el-form-item>
          <el-form-item label="操作类型">
            <el-select 
              v-model="filterForm.operationType" 
              placeholder="全部类型" 
              clearable
            >
              <el-option 
                v-for="item in operationTypeOptions" 
                :key="item.value" 
                :label="item.label" 
                :value="item.value" 
              />
            </el-select>
          </el-form-item>
          <el-form-item label="操作状态">
            <el-select 
              v-model="filterForm.status" 
              placeholder="全部状态" 
              clearable
            >
              <el-option 
                v-for="item in statusOptions" 
                :key="item.value" 
                :label="item.label" 
                :value="item.value" 
              />
            </el-select>
          </el-form-item>
          <el-form-item label="操作时间">
            <el-date-picker
              v-model="filterForm.operationTime"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch" :icon="Search">
              搜索
            </el-button>
            <el-button @click="resetFilter" :icon="RefreshRight">
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="data-card">
      <el-table
        ref="logTable"
        v-loading="loading"
        :data="logList"
        border
        style="width: 100%"
      >
        <el-table-column type="expand">
          <template #default="props">
            <div class="log-details">
              <div class="detail-item">
                <span class="detail-label">操作内容：</span>
                <span class="detail-value">{{ props.row.operationContent }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">请求方法：</span>
                <span class="detail-value">{{ props.row.method }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">请求参数：</span>
                <div class="code-block">
                  <pre>{{ formatJson(props.row.requestParams) }}</pre>
                </div>
              </div>
              <div class="detail-item" v-if="props.row.status === 'error'">
                <span class="detail-label">错误信息：</span>
                <div class="code-block">
                  <pre>{{ props.row.errorMessage }}</pre>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="id" label="日志编号" width="100" />
        <el-table-column prop="username" label="操作用户" width="120" />
        <el-table-column prop="moduleName" label="操作模块" width="120" />
        <el-table-column prop="operationType" label="操作类型" width="100">
          <template #default="scope">
            <el-tag :type="getOperationTypeTag(scope.row.operationType)" size="small">
              {{ getOperationTypeLabel(scope.row.operationType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="operationDescription" label="操作描述" min-width="200" show-overflow-tooltip />
        <el-table-column prop="ip" label="IP地址" width="140" />
        <el-table-column prop="operationTime" label="操作时间" width="170" sortable />
        <el-table-column prop="status" label="状态" width="100" align="center">
          <template #default="scope">
            <el-tag v-if="scope.row.status === 'success'" type="success">成功</el-tag>
            <el-tag v-else-if="scope.row.status === 'error'" type="danger">失败</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="executionTime" label="执行时长" width="100" align="center">
          <template #default="scope">
            {{ scope.row.executionTime }} ms
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :background="true"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Search, Download, Delete, RefreshRight
} from '@element-plus/icons-vue'

// 数据加载状态
const loading = ref(false)

// 日志列表数据
const logList = ref([])
const logTable = ref(null)

// 分页配置
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 筛选表单
const filterForm = reactive({
  username: '',
  operationType: '',
  status: '',
  operationTime: []
})

// 操作类型选项
const operationTypeOptions = [
  { label: '查询', value: 'query' },
  { label: '新增', value: 'insert' },
  { label: '修改', value: 'update' },
  { label: '删除', value: 'delete' },
  { label: '导出', value: 'export' },
  { label: '导入', value: 'import' },
  { label: '登录', value: 'login' },
  { label: '登出', value: 'logout' }
]

// 状态选项
const statusOptions = [
  { label: '成功', value: 'success' },
  { label: '失败', value: 'error' }
]

// 生命周期钩子
onMounted(() => {
  fetchLogList()
})

// 获取日志列表
const fetchLogList = () => {
  loading.value = true
  
  // 构建查询参数
  const params = {
    page: pagination.currentPage,
    pageSize: pagination.pageSize,
    username: filterForm.username,
    operationType: filterForm.operationType,
    status: filterForm.status,
    startTime: filterForm.operationTime?.[0],
    endTime: filterForm.operationTime?.[1]
  }
  
  // 模拟API调用
  // 实际项目中应该调用API: const res = await getLogList(params)
  setTimeout(() => {
    // 模拟数据
    const mockData = []
    const total = 124
    
    for (let i = 0; i < Math.min(pagination.pageSize, total - (pagination.currentPage - 1) * pagination.pageSize); i++) {
      const index = (pagination.currentPage - 1) * pagination.pageSize + i
      
      // 随机操作类型
      const operationType = ['query', 'insert', 'update', 'delete', 'export', 'import', 'login', 'logout'][Math.floor(Math.random() * 8)]
      
      // 随机状态
      const status = ['success', 'error'][Math.floor(Math.random() * (operationType === 'query' ? 5 : 3)) === 0 ? 1 : 0]
      
      // 随机模块
      const moduleOptions = ['用户管理', '角色管理', '菜单管理', '部门管理', '资产管理', '资产类型', '位置管理', '故障管理', '采购管理', '任务管理']
      const moduleName = moduleOptions[Math.floor(Math.random() * moduleOptions.length)]
      
      // 操作描述
      let operationDescription = ''
      if (operationType === 'query') {
        operationDescription = `查询${moduleName}列表数据`
      } else if (operationType === 'insert') {
        operationDescription = `新增${moduleName}记录`
      } else if (operationType === 'update') {
        operationDescription = `修改${moduleName}记录`
      } else if (operationType === 'delete') {
        operationDescription = `删除${moduleName}记录`
      } else if (operationType === 'export') {
        operationDescription = `导出${moduleName}数据`
      } else if (operationType === 'import') {
        operationDescription = `导入${moduleName}数据`
      } else if (operationType === 'login') {
        operationDescription = '用户登录系统'
      } else if (operationType === 'logout') {
        operationDescription = '用户退出系统'
      }
      
      // 随机操作时间
      const now = new Date()
      const operationDate = new Date()
      operationDate.setDate(now.getDate() - Math.floor(Math.random() * 30))
      operationDate.setHours(Math.floor(Math.random() * 24), Math.floor(Math.random() * 60), Math.floor(Math.random() * 60))
      const operationTime = operationDate.toISOString().replace('T', ' ').substring(0, 19)
      
      // 随机执行时长
      const executionTime = Math.floor(Math.random() * 1000) + 10
      
      // 随机请求参数
      const requestParams = {
        pageNum: 1,
        pageSize: 10,
        id: Math.floor(Math.random() * 1000) + 1000,
        name: `测试数据${Math.floor(Math.random() * 100)}`,
        status: ['active', 'disabled'][Math.floor(Math.random() * 2)]
      }
      
      mockData.push({
        id: 10000 + index,
        username: ['admin', 'operator', 'system', 'test_user', 'zhang_san', 'li_si'][Math.floor(Math.random() * 6)],
        moduleName: moduleName,
        operationType: operationType,
        operationDescription: operationDescription,
        operationContent: operationDescription + '，详细操作内容...',
        ip: `192.168.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}`,
        method: ['GET', 'POST', 'PUT', 'DELETE'][operationType === 'query' ? 0 : (operationType === 'insert' ? 1 : (operationType === 'update' ? 2 : 3))],
        requestParams: requestParams,
        operationTime: operationTime,
        status: status,
        executionTime: executionTime,
        errorMessage: status === 'error' ? '操作失败：' + ['参数错误', '权限不足', '数据不存在', '系统异常', '网络超时'][Math.floor(Math.random() * 5)] : ''
      })
    }
    
    logList.value = mockData
    pagination.total = total
    loading.value = false
  }, 500)
}

// 搜索
const handleSearch = () => {
  pagination.currentPage = 1
  fetchLogList()
}

// 重置筛选条件
const resetFilter = () => {
  filterForm.username = ''
  filterForm.operationType = ''
  filterForm.status = ''
  filterForm.operationTime = []
  
  pagination.currentPage = 1
  fetchLogList()
}

// 分页事件
const handleSizeChange = (size) => {
  pagination.pageSize = size
  fetchLogList()
}

const handleCurrentChange = (page) => {
  pagination.currentPage = page
  fetchLogList()
}

// 导出数据
const handleExportData = () => {
  ElMessage.success('开始导出数据，请稍候...')
  // 实际项目中调用导出API
  // exportLogList(filterForm).then(() => {
  //   ElMessage.success('导出成功')
  // })
}

// 清理日志
const handleClearLogs = () => {
  ElMessageBox.confirm('确定要清理30天前的日志数据吗？此操作不可恢复！', '清理日志', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // 模拟API调用
    // 实际项目中应该调用API: await clearLogs({ days: 30 })
    
    ElMessage.success('日志清理成功')
    
    // 刷新列表
    fetchLogList()
  }).catch(() => {
    // 取消操作
  })
}

// 工具方法：获取操作类型标签样式
const getOperationTypeTag = (type) => {
  const map = {
    'query': 'info',
    'insert': 'success',
    'update': 'warning',
    'delete': 'danger',
    'export': 'primary',
    'import': 'primary',
    'login': 'success',
    'logout': 'info'
  }
  return map[type] || ''
}

// 工具方法：获取操作类型文本
const getOperationTypeLabel = (type) => {
  const map = {
    'query': '查询',
    'insert': '新增',
    'update': '修改',
    'delete': '删除',
    'export': '导出',
    'import': '导入',
    'login': '登录',
    'logout': '登出'
  }
  return map[type] || '未知'
}

// 工具方法：格式化JSON
const formatJson = (jsonObj) => {
  try {
    if (typeof jsonObj === 'string') {
      return JSON.stringify(JSON.parse(jsonObj), null, 2)
    } else {
      return JSON.stringify(jsonObj, null, 2)
    }
  } catch (e) {
    return jsonObj
  }
}
</script>

<style lang="scss" scoped>
.audit-logs-container {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    
    .page-title {
      font-size: 20px;
      font-weight: 600;
      color: var(--el-color-primary);
      margin: 0;
    }
    
    .page-actions {
      display: flex;
      gap: 8px;
    }
  }
  
  .filter-card {
    margin-bottom: 16px;
    
    .filter-container {
      .filter-form {
        display: flex;
        flex-wrap: wrap;
      }
    }
  }
  
  .data-card {
    margin-bottom: 16px;
    
    .log-details {
      padding: 10px 20px;
      
      .detail-item {
        margin-bottom: 10px;
        
        .detail-label {
          font-weight: bold;
          color: #909399;
          margin-right: 5px;
        }
        
        .code-block {
          background-color: #f8f8f8;
          border: 1px solid #e0e0e0;
          border-radius: 4px;
          padding: 8px;
          margin-top: 5px;
          
          pre {
            margin: 0;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            word-break: break-all;
          }
        }
      }
    }
    
    .pagination-container {
      margin-top: 16px;
      display: flex;
      justify-content: flex-end;
    }
  }
}
</style> 