// IT资产管理系统 - 健康状态模型
// 文件路径: /Models/HealthStatus.cs
// 功能: 定义系统健康状态模型

using System;
using System.Collections.Generic;

namespace ItAssetsSystem.Models
{
    /// <summary>
    /// 系统健康状态
    /// </summary>
    public class HealthStatus
    {
        /// <summary>
        /// 状态: "Healthy", "Degraded", or "Unhealthy"
        /// </summary>
        public string Status { get; set; }
        
        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }
        
        /// <summary>
        /// 应用版本
        /// </summary>
        public string Version { get; set; }
        
        /// <summary>
        /// 服务状态字典
        /// </summary>
        public Dictionary<string, string> Services { get; set; }
        
        /// <summary>
        /// 附加说明
        /// </summary>
        public string Notes { get; set; }
    }
} 