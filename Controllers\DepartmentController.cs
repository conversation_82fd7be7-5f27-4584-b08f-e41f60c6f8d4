using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ItAssetsSystem.Infrastructure.Data;
using ItAssetsSystem.Models;
using ItAssetsSystem.Models.Entities;

namespace ItAssetsSystem.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class DepartmentController : ControllerBase
    {
        private readonly AppDbContext _context;
        private readonly ILogger<DepartmentController> _logger;

        public DepartmentController(AppDbContext context, ILogger<DepartmentController> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// 获取所有部门列表
        /// </summary>
        /// <returns>部门列表</returns>
        [HttpGet]
        public async Task<IActionResult> GetAll()
        {
            _logger.LogInformation("获取部门列表");
            try
            {
                var departments = await _context.Departments
                    .Where(d => d.IsActive)
                    .Select(d => new
                    {
                        d.Id,
                        d.<PERSON>,
                        d.Name,
                        d.Description,
                        d.<PERSON>,
                        d.Manager<PERSON>d,
                        DeputyManagerId = (int?)d.DeputyManagerId,
                        d.Path,
                        d.IsActive,
                        d.CreatedAt,
                        d.UpdatedAt
                    })
                    .ToListAsync();

                return Ok(new { success = true, data = departments, count = departments.Count });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取部门列表出错");
                return StatusCode(500, new { success = false, message = "获取部门列表出错: " + ex.Message });
            }
        }

        /// <summary>
        /// 获取部门详情
        /// </summary>
        /// <param name="id">部门ID</param>
        /// <returns>部门详情</returns>
        [HttpGet("{id}")]
        public async Task<IActionResult> GetById(int id)
        {
            _logger.LogInformation($"获取部门详情 ID: {id}");
            try
            {
                var department = await _context.Departments
                    .Where(d => d.Id == id)
                    .Select(d => new
                    {
                        d.Id,
                        d.Code,
                        d.Name,
                        d.Description,
                        d.ParentId,
                        d.ManagerId,
                        DeputyManagerId = (int?)d.DeputyManagerId,
                        d.Path,
                        d.IsActive,
                        d.CreatedAt,
                        d.UpdatedAt
                    })
                    .FirstOrDefaultAsync();

                if (department == null)
                {
                    return NotFound(new { success = false, message = $"未找到ID为{id}的部门" });
                }

                return Ok(new { success = true, data = department });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取部门详情出错 ID: {id}");
                return StatusCode(500, new { success = false, message = $"获取部门详情出错: {ex.Message}" });
            }
        }

        /// <summary>
        /// 获取部门用户列表
        /// </summary>
        /// <param name="departmentId">部门ID</param>
        /// <returns>部门用户列表</returns>
        [HttpGet("{departmentId}/users")]
        public async Task<IActionResult> GetDepartmentUsers(int departmentId)
        {
            _logger.LogInformation($"获取部门用户列表 部门ID: {departmentId}");
            try
            {
                var department = await _context.Departments.FindAsync(departmentId);
                if (department == null)
                {
                    return NotFound(new { success = false, message = $"未找到ID为{departmentId}的部门" });
                }

                var users = await _context.Users
                    .Where(u => u.DepartmentId == departmentId && u.IsActive)
                    .Select(u => new
                    {
                        u.Id,
                        u.Username,
                        u.Name,
                        u.Position,
                        u.Email,
                        u.Mobile,
                        u.IsActive
                    })
                    .ToListAsync();

                return Ok(new { success = true, data = users, count = users.Count });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取部门用户列表出错 部门ID: {departmentId}");
                return StatusCode(500, new { success = false, message = $"获取部门用户列表出错: {ex.Message}" });
            }
        }

        /// <summary>
        /// 创建部门
        /// </summary>
        /// <param name="request">部门信息</param>
        /// <returns>创建结果</returns>
        [HttpPost]
        public async Task<IActionResult> Create([FromBody] DepartmentRequest request)
        {
            _logger.LogInformation($"创建部门: {request?.Name}, ManagerId: {request?.ManagerId}, DeputyManagerId: {request?.DeputyManagerId}");
            
            if (request == null)
            {
                return BadRequest(new { success = false, message = "请求数据不能为空" });
            }
            
            try
            {
                // 检查部门编码是否已存在
                var codeExists = await _context.Departments.AnyAsync(d => d.Code == request.Code);
                if (codeExists)
                {
                    return BadRequest(new { success = false, message = $"部门编码 {request.Code} 已存在" });
                }
                
                // 构建部门实体
                var department = new Department
                {
                    Name = request.Name,
                    Code = request.Code,
                    Description = request.Description ?? string.Empty,
                    ParentId = request.ParentId,
                    ManagerId = request.ManagerId,
                    DeputyManagerId = request.DeputyManagerId,
                    IsActive = request.Status == "active",
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now
                };
                
                // 如果有父部门，设置路径
                if (request.ParentId.HasValue)
                {
                    var parent = await _context.Departments.FindAsync(request.ParentId.Value);
                    if (parent == null)
                    {
                        return BadRequest(new { success = false, message = $"父部门ID {request.ParentId.Value} 不存在" });
                    }
                    
                    department.Path = string.IsNullOrEmpty(parent.Path) 
                        ? parent.Id.ToString() 
                        : $"{parent.Path},{parent.Id}";
                }
                
                _context.Departments.Add(department);
                await _context.SaveChangesAsync();
                
                _logger.LogInformation($"部门创建成功: ID={department.Id}, ManagerId={department.ManagerId}, DeputyManagerId={department.DeputyManagerId}");
                return Ok(new { success = true, data = department.Id, message = "创建部门成功" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"创建部门出错: {request.Name}");
                return StatusCode(500, new { success = false, message = $"创建部门出错: {ex.Message}" });
            }
        }
        
        /// <summary>
        /// 更新部门
        /// </summary>
        /// <param name="id">部门ID</param>
        /// <param name="request">部门信息</param>
        /// <returns>更新结果</returns>
        [HttpPut("{id}")]
        public async Task<IActionResult> Update(int id, [FromBody] DepartmentRequest request)
        {
            _logger.LogInformation($"更新部门: ID={id}, 名称={request?.Name}, ManagerId={request?.ManagerId}, DeputyManagerId={request?.DeputyManagerId}");
            
            if (request == null)
            {
                return BadRequest(new { success = false, message = "请求数据不能为空" });
            }
            
            try
            {
                var department = await _context.Departments.FindAsync(id);
                
                if (department == null)
                {
                    return NotFound(new { success = false, message = $"未找到ID为{id}的部门" });
                }
                
                // 检查部门编码是否已被其他部门使用
                var codeExists = await _context.Departments.AnyAsync(d => d.Code == request.Code && d.Id != id);
                if (codeExists)
                {
                    return BadRequest(new { success = false, message = $"部门编码 {request.Code} 已被其他部门使用" });
                }
                
                // 更新部门信息
                department.Name = request.Name;
                department.Code = request.Code;
                department.Description = request.Description ?? string.Empty;
                department.IsActive = request.Status == "active";
                department.ManagerId = request.ManagerId;
                department.DeputyManagerId = request.DeputyManagerId;
                department.UpdatedAt = DateTime.Now;
                
                // 处理父部门变更
                if (request.ParentId != department.ParentId)
                {
                    // 不能将部门设为自己的子部门
                    if (request.ParentId == id)
                    {
                        return BadRequest(new { success = false, message = "不能将部门设为自己的子部门" });
                    }
                    
                    // 不能将部门设为其子部门的子部门（禁止循环引用）
                    if (request.ParentId.HasValue)
                    {
                        // 获取当前部门的所有子部门ID
                        var childIds = await GetAllChildDepartmentIds(id);
                        if (childIds.Contains(request.ParentId.Value))
                        {
                            return BadRequest(new { success = false, message = "不能将部门设为其子部门的子部门" });
                        }
                        
                        // 设置新的父部门
                        var parent = await _context.Departments.FindAsync(request.ParentId.Value);
                        if (parent == null)
                        {
                            return BadRequest(new { success = false, message = $"父部门ID {request.ParentId.Value} 不存在" });
                        }
                        
                        department.ParentId = request.ParentId;
                        department.Path = string.IsNullOrEmpty(parent.Path) 
                            ? parent.Id.ToString() 
                            : $"{parent.Path},{parent.Id}";
                        
                        // 更新所有子部门的路径
                        await UpdateChildDepartmentPaths(department);
                    }
                    else
                    {
                        // 移除父部门
                        department.ParentId = null;
                        department.Path = null;
                        
                        // 更新所有子部门的路径
                        await UpdateChildDepartmentPaths(department);
                    }
                }
                
                await _context.SaveChangesAsync();
                
                return Ok(new { success = true, message = "更新部门成功" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"更新部门出错: ID={id}");
                return StatusCode(500, new { success = false, message = $"更新部门出错: {ex.Message}" });
            }
        }
        
        /// <summary>
        /// 删除部门
        /// </summary>
        /// <param name="id">部门ID</param>
        /// <returns>删除结果</returns>
        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(int id)
        {
            _logger.LogInformation($"删除部门 ID: {id}");
            
            try
            {
                var department = await _context.Departments.FindAsync(id);
                
                if (department == null)
                {
                    return NotFound(new { success = false, message = $"未找到ID为{id}的部门" });
                }
                
                // 检查是否有子部门
                var hasChildren = await _context.Departments.AnyAsync(d => d.ParentId == id);
                if (hasChildren)
                {
                    return BadRequest(new { success = false, message = "无法删除有子部门的部门，请先删除子部门" });
                }
                
                // 检查是否有关联的用户
                var hasUsers = await _context.Users.AnyAsync(u => u.DepartmentId == id);
                if (hasUsers)
                {
                    return BadRequest(new { success = false, message = "无法删除有关联用户的部门，请先移除或重新分配用户" });
                }
                
                // 检查是否有关联的资产
                var hasAssets = await _context.Assets.AnyAsync(a => a.DepartmentId == id);
                if (hasAssets)
                {
                    return BadRequest(new { success = false, message = "无法删除有关联资产的部门，请先移除或重新分配资产" });
                }
                
                _context.Departments.Remove(department);
                await _context.SaveChangesAsync();
                
                return Ok(new { success = true, message = "删除部门成功" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"删除部门出错 ID: {id}");
                return StatusCode(500, new { success = false, message = $"删除部门出错: {ex.Message}" });
            }
        }

        /// <summary>
        /// 获取部门的所有子部门ID
        /// </summary>
        /// <param name="departmentId">部门ID</param>
        /// <returns>子部门ID列表</returns>
        private async System.Threading.Tasks.Task<List<int>> GetAllChildDepartmentIds(int departmentId)
        {
            var result = new List<int>();
            var directChildren = await _context.Departments
                .Where(d => d.ParentId == departmentId)
                .Select(d => d.Id)
                .ToListAsync();
                
            result.AddRange(directChildren);
            
            foreach (var childId in directChildren)
            {
                var grandchildren = await GetAllChildDepartmentIds(childId);
                result.AddRange(grandchildren);
            }
            
            return result;
        }
        
        /// <summary>
        /// 更新子部门的路径
        /// </summary>
        /// <param name="department">父部门</param>
        private async System.Threading.Tasks.Task UpdateChildDepartmentPaths(Department department)
        {
            var children = await _context.Departments
                .Where(d => d.ParentId == department.Id)
                .ToListAsync();
                
            foreach (var child in children)
            {
                // 更新子部门的路径
                child.Path = string.IsNullOrEmpty(department.Path) 
                    ? department.Id.ToString() 
                    : $"{department.Path},{department.Id}";
                    
                child.UpdatedAt = DateTime.Now;
                
                // 递归更新子部门的子部门
                await UpdateChildDepartmentPaths(child);
            }
        }
    }
    
    /// <summary>
    /// 部门请求模型
    /// </summary>
    public class DepartmentRequest
    {
        /// <summary>
        /// 部门名称
        /// </summary>
        public string Name { get; set; }
        
        /// <summary>
        /// 部门编码
        /// </summary>
        public string Code { get; set; }
        
        /// <summary>
        /// 部门描述
        /// </summary>
        public string Description { get; set; }
        
        /// <summary>
        /// 父部门ID
        /// </summary>
        public int? ParentId { get; set; }
        
        /// <summary>
        /// 部门经理ID
        /// </summary>
        public int? ManagerId { get; set; }
        
        /// <summary>
        /// 部门主任ID
        /// </summary>
        public int? DeputyManagerId { get; set; }
        
        /// <summary>
        /// 状态 (active/disabled)
        /// </summary>
        public string Status { get; set; } = "active";
    }
} 