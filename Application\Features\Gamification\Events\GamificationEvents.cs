// File: Application/Features/Gamification/Events/GamificationEvents.cs
// Description: 游戏化系统领域事件定义

using MediatR;
using System;

namespace ItAssetsSystem.Application.Features.Gamification.Events
{
    /// <summary>
    /// 游戏化奖励事件基类
    /// </summary>
    public abstract class GamificationEventBase : INotification
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public int UserId { get; }

        /// <summary>
        /// 事件发生时间
        /// </summary>
        public DateTime OccurredAt { get; }

        /// <summary>
        /// 事件来源
        /// </summary>
        public string Source { get; }

        /// <summary>
        /// 关联对象ID
        /// </summary>
        public long? ReferenceId { get; }

        protected GamificationEventBase(int userId, string source, long? referenceId = null)
        {
            UserId = userId;
            Source = source ?? throw new ArgumentNullException(nameof(source));
            ReferenceId = referenceId;
            OccurredAt = DateTime.UtcNow;
        }
    }

    /// <summary>
    /// 任务创建奖励事件
    /// </summary>
    public class TaskCreatedRewardEvent : GamificationEventBase
    {
        /// <summary>
        /// 任务ID
        /// </summary>
        public long TaskId { get; }

        /// <summary>
        /// 任务名称
        /// </summary>
        public string TaskName { get; }

        /// <summary>
        /// 任务类型
        /// </summary>
        public string TaskType { get; }

        /// <summary>
        /// 任务积分
        /// </summary>
        public int TaskPoints { get; }

        public TaskCreatedRewardEvent(int userId, long taskId, string taskName, string taskType, int taskPoints)
            : base(userId, "TaskCreated", taskId)
        {
            TaskId = taskId;
            TaskName = taskName ?? throw new ArgumentNullException(nameof(taskName));
            TaskType = taskType ?? throw new ArgumentNullException(nameof(taskType));
            TaskPoints = taskPoints;
        }
    }

    /// <summary>
    /// 任务完成奖励事件
    /// </summary>
    public class TaskCompletedRewardEvent : GamificationEventBase
    {
        /// <summary>
        /// 任务ID
        /// </summary>
        public long TaskId { get; }

        /// <summary>
        /// 任务名称
        /// </summary>
        public string TaskName { get; }

        /// <summary>
        /// 任务类型
        /// </summary>
        public string TaskType { get; }

        /// <summary>
        /// 是否按时完成
        /// </summary>
        public bool IsOnTime { get; }

        /// <summary>
        /// 任务积分
        /// </summary>
        public int TaskPoints { get; }

        /// <summary>
        /// 完成时间
        /// </summary>
        public DateTime CompletedAt { get; }

        public TaskCompletedRewardEvent(int userId, long taskId, string taskName, string taskType, 
            bool isOnTime, int taskPoints, DateTime completedAt)
            : base(userId, "TaskCompleted", taskId)
        {
            TaskId = taskId;
            TaskName = taskName ?? throw new ArgumentNullException(nameof(taskName));
            TaskType = taskType ?? throw new ArgumentNullException(nameof(taskType));
            IsOnTime = isOnTime;
            TaskPoints = taskPoints;
            CompletedAt = completedAt;
        }
    }

    /// <summary>
    /// 任务认领奖励事件
    /// </summary>
    public class TaskClaimedRewardEvent : GamificationEventBase
    {
        /// <summary>
        /// 任务ID
        /// </summary>
        public long TaskId { get; }

        /// <summary>
        /// 任务名称
        /// </summary>
        public string TaskName { get; }

        /// <summary>
        /// 认领时间
        /// </summary>
        public DateTime ClaimedAt { get; }

        /// <summary>
        /// 班次ID
        /// </summary>
        public long ShiftId { get; }

        public TaskClaimedRewardEvent(int userId, long taskId, string taskName, DateTime claimedAt, long shiftId)
            : base(userId, "TaskClaimed", taskId)
        {
            TaskId = taskId;
            TaskName = taskName ?? throw new ArgumentNullException(nameof(taskName));
            ClaimedAt = claimedAt;
            ShiftId = shiftId;
        }
    }

    /// <summary>
    /// 任务删除扣分事件
    /// </summary>
    public class TaskDeletedPenaltyEvent : GamificationEventBase
    {
        /// <summary>
        /// 任务ID
        /// </summary>
        public long TaskId { get; }

        /// <summary>
        /// 任务名称
        /// </summary>
        public string TaskName { get; }

        /// <summary>
        /// 删除原因
        /// </summary>
        public string? DeleteReason { get; }

        public TaskDeletedPenaltyEvent(int userId, long taskId, string taskName, string? deleteReason = null)
            : base(userId, "TaskDeleted", taskId)
        {
            TaskId = taskId;
            TaskName = taskName ?? throw new ArgumentNullException(nameof(taskName));
            DeleteReason = deleteReason;
        }
    }

    /// <summary>
    /// 资产更新奖励事件
    /// </summary>
    public class AssetUpdatedRewardEvent : GamificationEventBase
    {
        /// <summary>
        /// 资产ID
        /// </summary>
        public int AssetId { get; }

        /// <summary>
        /// 资产名称
        /// </summary>
        public string AssetName { get; }

        /// <summary>
        /// 更新类型
        /// </summary>
        public string UpdateType { get; }

        public AssetUpdatedRewardEvent(int userId, int assetId, string assetName, string updateType)
            : base(userId, "AssetUpdated", assetId)
        {
            AssetId = assetId;
            AssetName = assetName ?? throw new ArgumentNullException(nameof(assetName));
            UpdateType = updateType ?? throw new ArgumentNullException(nameof(updateType));
        }
    }

    /// <summary>
    /// 故障报告奖励事件
    /// </summary>
    public class FaultReportedRewardEvent : GamificationEventBase
    {
        /// <summary>
        /// 故障ID
        /// </summary>
        public long FaultId { get; }

        /// <summary>
        /// 故障描述
        /// </summary>
        public string FaultDescription { get; }

        /// <summary>
        /// 严重程度
        /// </summary>
        public string Severity { get; }

        public FaultReportedRewardEvent(int userId, long faultId, string faultDescription, string severity)
            : base(userId, "FaultReported", faultId)
        {
            FaultId = faultId;
            FaultDescription = faultDescription ?? throw new ArgumentNullException(nameof(faultDescription));
            Severity = severity ?? throw new ArgumentNullException(nameof(severity));
        }
    }

    /// <summary>
    /// 每日登录奖励事件
    /// </summary>
    public class DailyLoginRewardEvent : GamificationEventBase
    {
        /// <summary>
        /// 连续登录天数
        /// </summary>
        public int ConsecutiveDays { get; }

        /// <summary>
        /// 登录时间
        /// </summary>
        public DateTime LoginTime { get; }

        public DailyLoginRewardEvent(int userId, int consecutiveDays, DateTime loginTime)
            : base(userId, "DailyLogin")
        {
            ConsecutiveDays = consecutiveDays;
            LoginTime = loginTime;
        }
    }

    /// <summary>
    /// 评论添加奖励事件
    /// </summary>
    public class CommentAddedRewardEvent : GamificationEventBase
    {
        /// <summary>
        /// 评论ID
        /// </summary>
        public long CommentId { get; }

        /// <summary>
        /// 关联任务ID
        /// </summary>
        public long TaskId { get; }

        /// <summary>
        /// 评论内容长度
        /// </summary>
        public int ContentLength { get; }

        public CommentAddedRewardEvent(int userId, long commentId, long taskId, int contentLength)
            : base(userId, "CommentAdded", commentId)
        {
            CommentId = commentId;
            TaskId = taskId;
            ContentLength = contentLength;
        }
    }
}
