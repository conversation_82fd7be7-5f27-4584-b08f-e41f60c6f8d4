// File: Controllers/ReturnToFactoryController.cs
// Description: 返厂管理控制器 - 支持返厂流程和备件关联管理

using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using ItAssetsSystem.Application.Features.ReturnToFactory.Services;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ItAssetsSystem.Controllers
{
    /// <summary>
    /// 返厂管理控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class ReturnToFactoryController : ControllerBase
    {
        private readonly ILogger<ReturnToFactoryController> _logger;
        private readonly ReturnToFactoryService _returnToFactoryService;

        public ReturnToFactoryController(
            ILogger<ReturnToFactoryController> logger,
            ReturnToFactoryService returnToFactoryService)
        {
            _logger = logger;
            _returnToFactoryService = returnToFactoryService;
        }

        /// <summary>
        /// 获取所有返厂记录
        /// </summary>
        /// <returns>返厂记录列表</returns>
        [HttpGet]
        public async Task<IActionResult> GetAll()
        {
            _logger.LogInformation("获取所有返厂记录");
            try
            {
                var returns = await _returnToFactoryService.GetAllReturnToFactoryRecordsAsync();
                return Ok(new { success = true, data = returns });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取返厂记录列表出错");
                return StatusCode(500, new { success = false, message = "获取返厂记录列表出错" });
            }
        }

        /// <summary>
        /// 获取返厂记录详情
        /// </summary>
        /// <param name="id">返厂记录ID</param>
        /// <returns>返厂记录详情</returns>
        [HttpGet("{id}")]
        public async Task<IActionResult> GetById(int id)
        {
            _logger.LogInformation($"获取返厂记录ID: {id}");
            try
            {
                var returnRecord = await _returnToFactoryService.GetReturnToFactoryByIdAsync(id);

                if (returnRecord == null)
                {
                    return NotFound(new { success = false, message = $"未找到ID为 {id} 的返厂记录" });
                }

                return Ok(new { success = true, data = returnRecord });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取返厂记录ID {id} 出错");
                return StatusCode(500, new { success = false, message = $"获取返厂记录ID {id} 出错" });
            }
        }

        /// <summary>
        /// 更新返厂状态
        /// </summary>
        /// <param name="id">返厂记录ID</param>
        /// <param name="model">状态更新模型</param>
        /// <returns>更新结果</returns>
        [HttpPut("{id}/status")]
        public IActionResult UpdateStatus(int id, [FromBody] UpdateReturnStatusModel model)
        {
            _logger.LogInformation($"更新返厂状态: ID {id}, 新状态 {model.Status}");
            try
            {
                return Ok(new
                {
                    success = true,
                    data = new
                    {
                        id = id,
                        status = model.Status,
                        statusName = GetStatusName(model.Status),
                        updateTime = DateTime.Now,
                        notes = model.Notes
                    },
                    message = "返厂状态更新成功"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"更新返厂状态失败: ID {id}");
                return StatusCode(500, new { success = false, message = "更新返厂状态失败" });
            }
        }

        /// <summary>
        /// 确认返厂完成
        /// </summary>
        /// <param name="id">返厂记录ID</param>
        /// <param name="model">返厂完成模型</param>
        /// <returns>确认结果</returns>
        [HttpPost("{id}/complete")]
        public IActionResult CompleteReturn(int id, [FromBody] CompleteReturnModel model)
        {
            _logger.LogInformation($"确认返厂完成: ID {id}");
            try
            {
                // 模拟返厂完成处理
                var result = new
                {
                    id = id,
                    status = 3, // 已返回
                    statusName = "已返回",
                    actualReturnTime = model.ActualReturnTime ?? DateTime.Now,
                    repairResult = model.RepairResult,
                    repairCost = model.RepairCost,
                    isRepairSuccessful = model.IsRepairSuccessful,
                    notes = model.Notes
                };

                // 如果维修成功，可能需要更新相关故障状态
                if (model.IsRepairSuccessful)
                {
                    _logger.LogInformation($"返厂维修成功，故障已解决: 返厂ID {id}");
                }

                return Ok(new
                {
                    success = true,
                    data = result,
                    message = "返厂完成确认成功"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"确认返厂完成失败: ID {id}");
                return StatusCode(500, new { success = false, message = "确认返厂完成失败" });
            }
        }

        /// <summary>
        /// 返厂维修后补充备件库存
        /// </summary>
        /// <param name="id">返厂记录ID</param>
        /// <param name="model">备件补充模型</param>
        /// <returns>补充结果</returns>
        [HttpPost("{id}/replenish-spare-parts")]
        public IActionResult ReplenishSpareParts(int id, [FromBody] ReplenishSparePartsModel model)
        {
            _logger.LogInformation($"返厂维修后补充备件库存: 返厂ID {id}");
            try
            {
                // 模拟备件库存补充逻辑
                var replenishedParts = new List<object>();
                foreach (var part in model.SpareParts)
                {
                    replenishedParts.Add(new
                    {
                        sparePartId = part.SparePartId,
                        sparePartName = $"备件-{part.SparePartId}",
                        replenishedQuantity = part.Quantity,
                        newStock = 50 + part.Quantity, // 模拟新库存
                        notes = part.Notes
                    });
                }

                return Ok(new
                {
                    success = true,
                    data = new
                    {
                        returnId = id,
                        replenishedSpareParts = replenishedParts
                    },
                    message = "备件库存补充成功"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"补充备件库存失败: 返厂ID {id}");
                return StatusCode(500, new { success = false, message = "补充备件库存失败" });
            }
        }

        /// <summary>
        /// 获取状态名称
        /// </summary>
        private string GetStatusName(int status)
        {
            return status switch
            {
                0 => "待送出",
                1 => "已送出",
                2 => "维修中",
                3 => "已返回",
                4 => "维修失败",
                _ => "未知状态"
            };
        }
    }

    /// <summary>
    /// 更新返厂状态模型
    /// </summary>
    public class UpdateReturnStatusModel
    {
        /// <summary>
        /// 新状态
        /// </summary>
        public int Status { get; set; }
        
        /// <summary>
        /// 备注
        /// </summary>
        public string Notes { get; set; }
    }

    /// <summary>
    /// 返厂完成模型
    /// </summary>
    public class CompleteReturnModel
    {
        /// <summary>
        /// 实际返回时间
        /// </summary>
        public DateTime? ActualReturnTime { get; set; }
        
        /// <summary>
        /// 维修结果
        /// </summary>
        public string RepairResult { get; set; }
        
        /// <summary>
        /// 维修费用
        /// </summary>
        public decimal? RepairCost { get; set; }
        
        /// <summary>
        /// 是否维修成功
        /// </summary>
        public bool IsRepairSuccessful { get; set; }
        
        /// <summary>
        /// 备注
        /// </summary>
        public string Notes { get; set; }
    }

    /// <summary>
    /// 备件补充模型
    /// </summary>
    public class ReplenishSparePartsModel
    {
        /// <summary>
        /// 补充的备件列表
        /// </summary>
        public List<SparePartReplenishment> SpareParts { get; set; } = new List<SparePartReplenishment>();
        
        /// <summary>
        /// 备注
        /// </summary>
        public string Notes { get; set; }
    }

    /// <summary>
    /// 备件补充信息
    /// </summary>
    public class SparePartReplenishment
    {
        /// <summary>
        /// 备件ID
        /// </summary>
        public long SparePartId { get; set; }
        
        /// <summary>
        /// 补充数量
        /// </summary>
        public int Quantity { get; set; }
        
        /// <summary>
        /// 备注
        /// </summary>
        public string Notes { get; set; }
    }
}
