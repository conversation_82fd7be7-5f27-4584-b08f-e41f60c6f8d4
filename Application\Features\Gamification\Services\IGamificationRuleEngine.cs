// File: Application/Features/Gamification/Services/IGamificationRuleEngine.cs
// Description: 游戏化规则引擎接口

using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using ItAssetsSystem.Application.Features.Gamification.Events;

namespace ItAssetsSystem.Application.Features.Gamification.Services
{
    /// <summary>
    /// 游戏化奖励计算结果
    /// </summary>
    public class GamificationReward
    {
        /// <summary>
        /// 积分奖励
        /// </summary>
        public int Points { get; set; }

        /// <summary>
        /// 金币奖励
        /// </summary>
        public int Coins { get; set; }

        /// <summary>
        /// 钻石奖励
        /// </summary>
        public int Diamonds { get; set; }

        /// <summary>
        /// 经验值奖励
        /// </summary>
        public int Experience { get; set; }

        /// <summary>
        /// 奖励描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 奖励来源
        /// </summary>
        public string Source { get; set; } = string.Empty;

        /// <summary>
        /// 是否有奖励
        /// </summary>
        public bool HasReward => Points > 0 || Coins > 0 || Diamonds > 0 || Experience > 0;

        /// <summary>
        /// 创建空奖励
        /// </summary>
        public static GamificationReward Empty => new GamificationReward();

        /// <summary>
        /// 创建奖励
        /// </summary>
        public static GamificationReward Create(int points, int coins, int diamonds, int experience, string description, string source)
        {
            return new GamificationReward
            {
                Points = points,
                Coins = coins,
                Diamonds = diamonds,
                Experience = experience,
                Description = description,
                Source = source
            };
        }
    }

    /// <summary>
    /// 游戏化规则配置
    /// </summary>
    public class GamificationRule
    {
        /// <summary>
        /// 规则ID
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 规则名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 事件类型
        /// </summary>
        public string EventType { get; set; } = string.Empty;

        /// <summary>
        /// 条件表达式 (JSON格式)
        /// </summary>
        public string Condition { get; set; } = "{}";

        /// <summary>
        /// 积分奖励
        /// </summary>
        public int PointsReward { get; set; }

        /// <summary>
        /// 金币奖励
        /// </summary>
        public int CoinsReward { get; set; }

        /// <summary>
        /// 钻石奖励
        /// </summary>
        public int DiamondsReward { get; set; }

        /// <summary>
        /// 经验值奖励
        /// </summary>
        public int ExperienceReward { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// 优先级 (数字越小优先级越高)
        /// </summary>
        public int Priority { get; set; } = 100;

        /// <summary>
        /// 客户端ID (多租户支持)
        /// </summary>
        public string? ClientId { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// 游戏化规则引擎接口
    /// </summary>
    public interface IGamificationRuleEngine
    {
        /// <summary>
        /// 计算事件奖励
        /// </summary>
        /// <param name="gamificationEvent">游戏化事件</param>
        /// <param name="clientId">客户端ID</param>
        /// <returns>奖励结果</returns>
        Task<GamificationReward> CalculateRewardAsync(GamificationEventBase gamificationEvent, string? clientId = null);

        /// <summary>
        /// 获取适用的规则
        /// </summary>
        /// <param name="eventType">事件类型</param>
        /// <param name="clientId">客户端ID</param>
        /// <returns>适用的规则列表</returns>
        Task<IEnumerable<GamificationRule>> GetApplicableRulesAsync(string eventType, string? clientId = null);

        /// <summary>
        /// 添加规则
        /// </summary>
        /// <param name="rule">规则配置</param>
        /// <returns>是否成功</returns>
        Task<bool> AddRuleAsync(GamificationRule rule);

        /// <summary>
        /// 更新规则
        /// </summary>
        /// <param name="rule">规则配置</param>
        /// <returns>是否成功</returns>
        Task<bool> UpdateRuleAsync(GamificationRule rule);

        /// <summary>
        /// 删除规则
        /// </summary>
        /// <param name="ruleId">规则ID</param>
        /// <returns>是否成功</returns>
        Task<bool> DeleteRuleAsync(string ruleId);

        /// <summary>
        /// 启用/禁用规则
        /// </summary>
        /// <param name="ruleId">规则ID</param>
        /// <param name="isActive">是否启用</param>
        /// <returns>是否成功</returns>
        Task<bool> SetRuleActiveAsync(string ruleId, bool isActive);

        /// <summary>
        /// 获取所有规则
        /// </summary>
        /// <param name="clientId">客户端ID</param>
        /// <returns>规则列表</returns>
        Task<IEnumerable<GamificationRule>> GetAllRulesAsync(string? clientId = null);

        /// <summary>
        /// 验证规则条件
        /// </summary>
        /// <param name="condition">条件表达式</param>
        /// <param name="eventData">事件数据</param>
        /// <returns>是否满足条件</returns>
        Task<bool> EvaluateConditionAsync(string condition, object eventData);

        /// <summary>
        /// 重新加载规则缓存
        /// </summary>
        /// <returns>任务</returns>
        Task ReloadRulesAsync();
    }

    /// <summary>
    /// 游戏化奖励处理接口
    /// </summary>
    public interface IGamificationRewardProcessor
    {
        /// <summary>
        /// 处理奖励发放
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="reward">奖励信息</param>
        /// <param name="eventSource">事件来源</param>
        /// <param name="referenceId">关联对象ID</param>
        /// <returns>是否成功</returns>
        Task<bool> ProcessRewardAsync(int userId, GamificationReward reward, string eventSource, long? referenceId = null);

        /// <summary>
        /// 批量处理奖励
        /// </summary>
        /// <param name="rewards">奖励列表</param>
        /// <returns>处理结果</returns>
        Task<Dictionary<int, bool>> ProcessBatchRewardsAsync(IEnumerable<(int UserId, GamificationReward Reward, string EventSource, long? ReferenceId)> rewards);

        /// <summary>
        /// 撤销奖励 (用于任务删除等场景)
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="eventSource">事件来源</param>
        /// <param name="referenceId">关联对象ID</param>
        /// <returns>是否成功</returns>
        Task<bool> RevokeRewardAsync(int userId, string eventSource, long? referenceId);
    }

    /// <summary>
    /// 游戏化统计更新接口
    /// </summary>
    public interface IGamificationStatsUpdater
    {
        /// <summary>
        /// 更新用户统计
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>是否成功</returns>
        Task<bool> UpdateUserStatsAsync(int userId);

        /// <summary>
        /// 批量更新用户统计
        /// </summary>
        /// <param name="userIds">用户ID列表</param>
        /// <returns>更新结果</returns>
        Task<Dictionary<int, bool>> UpdateBatchUserStatsAsync(IEnumerable<int> userIds);

        /// <summary>
        /// 更新排行榜
        /// </summary>
        /// <param name="leaderboardType">排行榜类型</param>
        /// <param name="period">时间周期</param>
        /// <returns>是否成功</returns>
        Task<bool> UpdateLeaderboardAsync(string leaderboardType, string period);

        /// <summary>
        /// 检查并处理等级提升
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>是否升级</returns>
        Task<bool> CheckAndProcessLevelUpAsync(int userId);

        /// <summary>
        /// 检查并处理成就解锁
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="eventType">事件类型</param>
        /// <returns>解锁的成就列表</returns>
        Task<IEnumerable<string>> CheckAndProcessAchievementsAsync(int userId, string eventType);
    }
}
