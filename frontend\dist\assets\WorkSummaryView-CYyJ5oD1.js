import{_ as e,r as a,z as t,b as l,d as s,e as r,p as n,w as d,f as i,v as o,q as p,E as u,a as c,m,o as f,s as g,l as b,k as w,t as v}from"./index-CG5lHOPO.js";import{s as h}from"./statistics-BoWGSZxY.js";const k={class:"work-summary-container"},_={class:"page-header"},y={class:"header-content"},C={class:"title-section"},R={class:"page-title"},U={class:"action-section"},E={class:"card-header"},P={class:"header-stats"},I={class:"stat-item"},x={class:"points-value"},A=e({__name:"WorkSummaryView",setup(e){const A=a(!1),N=a("weekly"),O=a([]),V=async()=>{A.value=!0;try{const e=await h.getWorkSummary({periodType:N.value,limit:100});if(!e.data.success)throw new Error(e.data.message||"获取工作汇总失败");O.value=e.data.data||[],u.success(`成功加载 ${O.value.length} 条工作汇总数据`)}catch(e){u.error("获取工作汇总失败，使用模拟数据"),O.value=z()}finally{A.value=!1}},z=()=>[{userName:"张三",tasksCompleted:12,tasksCreated:8,assetsCreated:5,assetsUpdated:2,faultsReported:3,faultsRepaired:1,procurementsCreated:2,procurementsUpdated:4,partsIn:15,partsOut:6,partsAdded:3,totalPointsEarned:156,pointsRank:1,evaluation:"超级"},{userName:"李四",tasksCompleted:9,tasksCreated:15,assetsCreated:3,assetsUpdated:4,faultsReported:1,faultsRepaired:2,procurementsCreated:1,procurementsUpdated:1,partsIn:14,partsOut:2,partsAdded:2,totalPointsEarned:142,pointsRank:2,evaluation:"优秀"},{userName:"王五",tasksCompleted:10,tasksCreated:6,assetsCreated:8,assetsUpdated:1,faultsReported:2,faultsRepaired:1,procurementsCreated:2,procurementsUpdated:2,partsIn:13,partsOut:8,partsAdded:2,totalPointsEarned:138,pointsRank:3,evaluation:"优秀"}],j=e=>{switch(e){case"超级":return"danger";case"优秀":return"success";case"良好":return"warning";case"一般":return"info";default:return""}},q=()=>{u.info("导出功能开发中...")};return t((()=>{V()})),(e,a)=>{const t=c("el-icon"),u=c("el-option"),h=c("el-select"),z=c("el-button"),S=c("el-table-column"),W=c("el-tag"),T=c("el-table"),$=c("el-card"),B=m("loading");return f(),l("div",k,[s("div",_,[s("div",y,[s("div",C,[s("h1",R,[r(t,null,{default:d((()=>[r(i(g))])),_:1}),a[1]||(a[1]=n(" 工作汇总报告 "))]),a[2]||(a[2]=s("p",{class:"page-subtitle"},"本周工作汇总 (2024年第25周)",-1))]),s("div",U,[r(h,{modelValue:N.value,"onUpdate:modelValue":a[0]||(a[0]=e=>N.value=e),onChange:V,class:"period-select"},{default:d((()=>[r(u,{label:"本周",value:"weekly"}),r(u,{label:"本月",value:"monthly"}),r(u,{label:"本季度",value:"quarterly"}),r(u,{label:"本年",value:"yearly"})])),_:1},8,["modelValue"]),r(z,{type:"primary",icon:i(o),onClick:V,loading:A.value},{default:d((()=>a[3]||(a[3]=[n(" 刷新 ")]))),_:1},8,["icon","loading"]),r(z,{type:"success",icon:i(p),onClick:q},{default:d((()=>a[4]||(a[4]=[n(" 导出 ")]))),_:1},8,["icon"])])])]),r($,{class:"summary-card"},{header:d((()=>[s("div",E,[a[5]||(a[5]=s("span",null,"📊 工作汇总统计",-1)),s("div",P,[s("span",I,"共 "+v(O.value.length)+" 人",1)])])])),default:d((()=>[b((f(),w(T,{data:O.value,stripe:"",border:"","default-sort":{prop:"totalPointsEarned",order:"descending"},class:"work-summary-table"},{default:d((()=>[r(S,{type:"index",label:"#",width:"50",align:"center"}),r(S,{prop:"userName",label:"姓名",width:"100",fixed:"left"}),r(S,{label:"📋 任务",align:"center"},{default:d((()=>[r(S,{prop:"tasksCompleted",label:"完成",width:"60",align:"center"}),r(S,{prop:"tasksCreated",label:"新建",width:"60",align:"center"}),r(S,{label:"总计",width:"60",align:"center"},{default:d((({row:e})=>[n(v((e.tasksCompleted||0)+(e.tasksCreated||0)),1)])),_:1})])),_:1}),r(S,{label:"🏢 资产",align:"center"},{default:d((()=>[r(S,{prop:"assetsCreated",label:"新建",width:"60",align:"center"}),r(S,{prop:"assetsUpdated",label:"更新",width:"60",align:"center"}),r(S,{label:"总计",width:"60",align:"center"},{default:d((({row:e})=>[n(v((e.assetsCreated||0)+(e.assetsUpdated||0)),1)])),_:1})])),_:1}),r(S,{label:"🔧 故障",align:"center"},{default:d((()=>[r(S,{prop:"faultsReported",label:"登记",width:"60",align:"center"}),r(S,{prop:"faultsRepaired",label:"维修",width:"60",align:"center"})])),_:1}),r(S,{label:"🛒 采购",align:"center"},{default:d((()=>[r(S,{prop:"procurementsCreated",label:"新建",width:"60",align:"center"}),r(S,{prop:"procurementsUpdated",label:"更新",width:"60",align:"center"})])),_:1}),r(S,{label:"⚙️ 备件",align:"center"},{default:d((()=>[r(S,{prop:"partsIn",label:"入库",width:"60",align:"center"}),r(S,{prop:"partsOut",label:"出库",width:"60",align:"center"}),r(S,{prop:"partsAdded",label:"新增",width:"60",align:"center"})])),_:1}),r(S,{label:"🏆 积分",align:"center"},{default:d((()=>[r(S,{prop:"totalPointsEarned",label:"积分",width:"80",align:"center"},{default:d((({row:e})=>[s("span",x,v(e.totalPointsEarned||0),1)])),_:1})])),_:1}),r(S,{prop:"pointsRank",label:"排名",width:"60",align:"center"},{default:d((({row:e})=>{return[r(W,{type:(a=e.pointsRank,1===a?"danger":2===a?"warning":3===a?"success":"info"),size:"small",round:""},{default:d((()=>[n(v(e.pointsRank||"-"),1)])),_:2},1032,["type"])];var a})),_:1}),r(S,{prop:"evaluation",label:"评价",width:"100",align:"center"},{default:d((({row:e})=>[r(W,{type:j(e.evaluation),size:"small",effect:"light"},{default:d((()=>[n(v(e.evaluation||"待评价"),1)])),_:2},1032,["type"])])),_:1})])),_:1},8,["data"])),[[B,A.value]])])),_:1})])}}},[["__scopeId","data-v-669a166b"]]);export{A as default};
