-- IT资产管理系统数据库完整脚本
-- 整合自complete-sql1.sql、complete-sql-script-continued3.sql、complete-sql-script-part4.sql和complete-sql-script2.sql
-- 包含：建库建表、删库删表语句以及测试数据插入

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- 开始事务
START TRANSACTION;

-- ========================================
-- 第一部分：删除表（如果存在）
-- ========================================

-- 删除表的语句将在这里添加
DROP TABLE IF EXISTS `tasks`;
DROP TABLE IF EXISTS `pdcaplans`;
DROP TABLE IF EXISTS `periodicrules`;
DROP TABLE IF EXISTS `__efmigrationshistory`;
DROP TABLE IF EXISTS `refreshtokens`;
DROP TABLE IF EXISTS `auditlogs`;
DROP TABLE IF EXISTS `maintenanceorders`;
DROP TABLE IF EXISTS `assetreceives`;
DROP TABLE IF EXISTS `returntofactories`;
DROP TABLE IF EXISTS `purchaseitems`;
DROP TABLE IF EXISTS `purchaseorders`;
DROP TABLE IF EXISTS `suppliers`;
DROP TABLE IF EXISTS `faultrecords`;
DROP TABLE IF EXISTS `faulttypes`;
DROP TABLE IF EXISTS `locationhistories`;
DROP TABLE IF EXISTS `assets`;
DROP TABLE IF EXISTS `assettypes`;
DROP TABLE IF EXISTS `location_users`;
DROP TABLE IF EXISTS `locationusers`;
DROP TABLE IF EXISTS `locations`;
DROP TABLE IF EXISTS `rolemenus`;
DROP TABLE IF EXISTS `menus`;
DROP TABLE IF EXISTS `userroles`;
DROP TABLE IF EXISTS `users`;
DROP TABLE IF EXISTS `departments`;
DROP TABLE IF EXISTS `roles`;

-- ========================================
-- 第二部分：创建表
-- ========================================

-- 1. 角色表
CREATE TABLE `roles` (
  `Id` int NOT NULL AUTO_INCREMENT,
  `Name` varchar(50) NOT NULL COMMENT '角色名称',
  `Code` varchar(50) NOT NULL COMMENT '角色编码',
  `Description` varchar(200) DEFAULT NULL COMMENT '角色描述',
  `IsActive` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否激活',
  `CreatedAt` datetime NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`),
  UNIQUE KEY `IX_Roles_Code` (`Code`),
  UNIQUE KEY `IX_Roles_Name` (`Name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色表';

-- 2. 部门表
CREATE TABLE `departments` (
  `Id` int NOT NULL AUTO_INCREMENT,
  `Name` varchar(50) NOT NULL COMMENT '部门名称',
  `Code` varchar(50) NOT NULL COMMENT '部门编码',
  `ParentId` int DEFAULT NULL COMMENT '父部门ID',
  `ManagerId` int DEFAULT NULL COMMENT '部门经理ID',
  `Path` varchar(200) DEFAULT NULL COMMENT '层级路径',
  `Description` varchar(200) DEFAULT NULL COMMENT '部门描述',
  `IsActive` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否激活',
  `CreatedAt` datetime NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`),
  UNIQUE KEY `IX_Departments_Code` (`Code`),
  UNIQUE KEY `IX_Departments_Name` (`Name`),
  KEY `IX_Departments_ParentId` (`ParentId`),
  KEY `IX_Departments_ManagerId` (`ManagerId`),
  CONSTRAINT `FK_Departments_Departments_ParentId` FOREIGN KEY (`ParentId`) REFERENCES `departments` (`Id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='部门表';

-- 3. 用户表
CREATE TABLE `users` (
  `Id` int NOT NULL AUTO_INCREMENT,
  `Username` varchar(50) NOT NULL COMMENT '用户名',
  `PasswordHash` varchar(200) NOT NULL COMMENT '密码哈希',
  `SecurityStamp` varchar(50) NOT NULL COMMENT '安全戳',
  `Name` varchar(50) NOT NULL COMMENT '姓名',
  `Email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `Mobile` varchar(20) DEFAULT NULL COMMENT '手机号',
  `DepartmentId` int DEFAULT NULL COMMENT '部门ID',
  `Position` varchar(50) DEFAULT NULL COMMENT '职位',
  `Gender` int NOT NULL DEFAULT '0' COMMENT '性别：0未知，1男，2女',
  `DefaultRoleId` int DEFAULT NULL COMMENT '默认角色ID',
  `IsActive` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否激活',
  `CreatedAt` datetime NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime NOT NULL COMMENT '更新时间',
  `LastLoginAt` datetime DEFAULT NULL COMMENT '最后登录时间',
  `Avatar` varchar(200) DEFAULT NULL COMMENT '头像',
  PRIMARY KEY (`Id`),
  UNIQUE KEY `IX_Users_Username` (`Username`),
  UNIQUE KEY `IX_Users_Email` (`Email`),
  KEY `IX_Users_DepartmentId` (`DepartmentId`),
  KEY `IX_Users_DefaultRoleId` (`DefaultRoleId`),
  CONSTRAINT `FK_Users_Departments_DepartmentId` FOREIGN KEY (`DepartmentId`) REFERENCES `departments` (`Id`),
  CONSTRAINT `FK_Users_Roles_DefaultRoleId` FOREIGN KEY (`DefaultRoleId`) REFERENCES `roles` (`Id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 4. 用户角色关联表
CREATE TABLE `userroles` (
  `UserId` int NOT NULL COMMENT '用户ID',
  `RoleId` int NOT NULL COMMENT '角色ID',
  `CreatedAt` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`UserId`,`RoleId`),
  KEY `IX_UserRoles_RoleId` (`RoleId`),
  CONSTRAINT `FK_UserRoles_Roles_RoleId` FOREIGN KEY (`RoleId`) REFERENCES `roles` (`Id`) ON DELETE CASCADE,
  CONSTRAINT `FK_UserRoles_Users_UserId` FOREIGN KEY (`UserId`) REFERENCES `users` (`Id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户角色关联表';

-- 5. 菜单表
CREATE TABLE `menus` (
  `Id` int NOT NULL AUTO_INCREMENT,
  `Name` varchar(50) NOT NULL COMMENT '菜单名称',
  `Code` varchar(50) NOT NULL COMMENT '菜单编码',
  `ParentId` int DEFAULT NULL COMMENT '父菜单ID',
  `Type` int NOT NULL DEFAULT '0' COMMENT '菜单类型：0菜单，1按钮',
  `Icon` varchar(50) DEFAULT NULL COMMENT '图标',
  `Path` varchar(200) DEFAULT NULL COMMENT '路由路径',
  `Component` varchar(200) DEFAULT NULL COMMENT '组件路径',
  `Permission` varchar(100) DEFAULT NULL COMMENT '权限标识',
  `Description` varchar(200) DEFAULT NULL COMMENT '描述',
  `SortOrder` int NOT NULL DEFAULT '0' COMMENT '排序',
  `IsExternal` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否外链',
  `KeepAlive` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否缓存',
  `IsVisible` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否可见',
  `IsActive` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否激活',
  `CreatedAt` datetime NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`),
  UNIQUE KEY `IX_Menus_Code` (`Code`),
  KEY `IX_Menus_ParentId` (`ParentId`),
  CONSTRAINT `FK_Menus_Menus_ParentId` FOREIGN KEY (`ParentId`) REFERENCES `menus` (`Id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='菜单表';

-- 6. 角色菜单关联表
CREATE TABLE `rolemenus` (
  `RoleId` int NOT NULL COMMENT '角色ID',
  `MenuId` int NOT NULL COMMENT '菜单ID',
  `CreatedAt` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`RoleId`,`MenuId`),
  KEY `IX_RoleMenus_MenuId` (`MenuId`),
  CONSTRAINT `FK_RoleMenus_Menus_MenuId` FOREIGN KEY (`MenuId`) REFERENCES `menus` (`Id`) ON DELETE CASCADE,
  CONSTRAINT `FK_RoleMenus_Roles_RoleId` FOREIGN KEY (`RoleId`) REFERENCES `roles` (`Id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色菜单关联表';

-- 7. 位置表
CREATE TABLE `locations` (
  `Id` int NOT NULL AUTO_INCREMENT,
  `Code` varchar(50) NOT NULL COMMENT '位置编码',
  `Name` varchar(100) NOT NULL COMMENT '位置名称',
  `Type` int NOT NULL DEFAULT '0' COMMENT '位置类型：0工厂，1产线，2工序，3工位',
  `ParentId` int DEFAULT NULL COMMENT '父位置ID',
  `Path` varchar(200) DEFAULT NULL COMMENT '层级路径',
  `Description` varchar(200) DEFAULT NULL COMMENT '位置描述',
  `DefaultDepartmentId` int DEFAULT NULL COMMENT '默认部门ID',
  `DefaultResponsiblePersonId` int DEFAULT NULL COMMENT '默认负责人ID',
  `IsActive` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否激活',
  `CreatedAt` datetime NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`),
  UNIQUE KEY `IX_Locations_Code` (`Code`),
  KEY `IX_Locations_ParentId` (`ParentId`),
  KEY `IX_Locations_DefaultDepartmentId` (`DefaultDepartmentId`),
  KEY `IX_Locations_DefaultResponsiblePersonId` (`DefaultResponsiblePersonId`),
  CONSTRAINT `FK_Locations_Departments_DefaultDepartmentId` FOREIGN KEY (`DefaultDepartmentId`) REFERENCES `departments` (`Id`),
  CONSTRAINT `FK_Locations_Locations_ParentId` FOREIGN KEY (`ParentId`) REFERENCES `locations` (`Id`),
  CONSTRAINT `FK_Locations_Users_DefaultResponsiblePersonId` FOREIGN KEY (`DefaultResponsiblePersonId`) REFERENCES `users` (`Id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='位置表';

-- 8. 位置用户关联表
CREATE TABLE `locationusers` (
  `LocationId` int NOT NULL COMMENT '位置ID',
  `UserId` int NOT NULL COMMENT '用户ID',
  `UserType` int NOT NULL DEFAULT '0' COMMENT '用户类型：0使用人，1负责人',
  `IsActive` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否激活',
  `CreatedAt` datetime NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`LocationId`,`UserId`,`UserType`),
  KEY `IX_LocationUsers_UserId` (`UserId`),
  CONSTRAINT `FK_LocationUsers_Locations_LocationId` FOREIGN KEY (`LocationId`) REFERENCES `locations` (`Id`) ON DELETE CASCADE,
  CONSTRAINT `FK_LocationUsers_Users_UserId` FOREIGN KEY (`UserId`) REFERENCES `users` (`Id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='位置用户关联表';

-- 9. 位置用户关联表（备用表）
CREATE TABLE `location_users` (
  `LocationId` int NOT NULL COMMENT '位置ID',
  `UserId` int NOT NULL COMMENT '用户ID',
  `UserType` int NOT NULL DEFAULT '0' COMMENT '用户类型：0使用人，1负责人',
  `IsActive` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否激活',
  `CreatedAt` datetime NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`LocationId`,`UserId`,`UserType`),
  KEY `IX_Location_Users_UserId` (`UserId`),
  CONSTRAINT `FK_Location_Users_Locations_LocationId` FOREIGN KEY (`LocationId`) REFERENCES `locations` (`Id`) ON DELETE CASCADE,
  CONSTRAINT `FK_Location_Users_Users_UserId` FOREIGN KEY (`UserId`) REFERENCES `users` (`Id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='位置用户关联表（备用表）';

-- 10. 资产类型表
CREATE TABLE `assettypes` (
  `Id` int NOT NULL AUTO_INCREMENT,
  `Name` varchar(50) NOT NULL COMMENT '类型名称',
  `Code` varchar(50) NOT NULL COMMENT '类型编码',
  `Description` varchar(200) DEFAULT NULL COMMENT '类型描述',
  `ParentId` int DEFAULT NULL COMMENT '父类型ID',
  `RequireSerialNumber` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否需要序列号',
  `SortOrder` int NOT NULL DEFAULT '0' COMMENT '排序',
  `IsActive` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否激活',
  `CreatedAt` datetime NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`),
  UNIQUE KEY `IX_AssetTypes_Code` (`Code`),
  UNIQUE KEY `IX_AssetTypes_Name` (`Name`),
  KEY `IX_AssetTypes_ParentId` (`ParentId`),
  CONSTRAINT `FK_AssetTypes_AssetTypes_ParentId` FOREIGN KEY (`ParentId`) REFERENCES `assettypes` (`Id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='资产类型表';

-- 11. 资产表
CREATE TABLE `assets` (
  `Id` int NOT NULL AUTO_INCREMENT,
  `AssetCode` varchar(50) NOT NULL COMMENT '资产编码',
  `Name` varchar(100) NOT NULL COMMENT '资产名称',
  `AssetTypeId` int NOT NULL COMMENT '资产类型ID',
  `SerialNumber` varchar(100) DEFAULT NULL COMMENT '序列号',
  `Model` varchar(100) DEFAULT NULL COMMENT '型号',
  `Brand` varchar(100) DEFAULT NULL COMMENT '品牌',
  `PurchaseDate` datetime DEFAULT NULL COMMENT '购买日期',
  `WarrantyExpireDate` datetime DEFAULT NULL COMMENT '保修到期日',
  `Price` decimal(18,2) DEFAULT NULL COMMENT '价格',
  `LocationId` int DEFAULT NULL COMMENT '位置ID',
  `Status` int NOT NULL DEFAULT '0' COMMENT '状态：0闲置，1在用，2维修中，3报废',
  `Notes` varchar(500) DEFAULT NULL COMMENT '备注',
  `CreatedAt` datetime NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`),
  UNIQUE KEY `IX_Assets_AssetCode` (`AssetCode`),
  KEY `IX_Assets_AssetTypeId` (`AssetTypeId`),
  KEY `IX_Assets_LocationId` (`LocationId`),
  CONSTRAINT `FK_Assets_AssetTypes_AssetTypeId` FOREIGN KEY (`AssetTypeId`) REFERENCES `assettypes` (`Id`),
  CONSTRAINT `FK_Assets_Locations_LocationId` FOREIGN KEY (`LocationId`) REFERENCES `locations` (`Id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='资产表';

-- 12. 位置历史表
CREATE TABLE `locationhistories` (
  `Id` int NOT NULL AUTO_INCREMENT,
  `AssetId` int NOT NULL COMMENT '资产ID',
  `OldLocationId` int DEFAULT NULL COMMENT '旧位置ID',
  `NewLocationId` int NOT NULL COMMENT '新位置ID',
  `OperatorId` int NOT NULL COMMENT '操作人ID',
  `ChangeType` int NOT NULL DEFAULT '0' COMMENT '变更类型：0转移，1领用，2归还',
  `ChangeTime` datetime NOT NULL COMMENT '变更时间',
  `Notes` varchar(500) DEFAULT NULL COMMENT '备注',
  `CreatedAt` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`Id`),
  KEY `IX_LocationHistories_AssetId` (`AssetId`),
  KEY `IX_LocationHistories_OldLocationId` (`OldLocationId`),
  KEY `IX_LocationHistories_NewLocationId` (`NewLocationId`),
  KEY `IX_LocationHistories_OperatorId` (`OperatorId`),
  CONSTRAINT `FK_LocationHistories_Assets_AssetId` FOREIGN KEY (`AssetId`) REFERENCES `assets` (`Id`),
  CONSTRAINT `FK_LocationHistories_Locations_NewLocationId` FOREIGN KEY (`NewLocationId`) REFERENCES `locations` (`Id`),
  CONSTRAINT `FK_LocationHistories_Locations_OldLocationId` FOREIGN KEY (`OldLocationId`) REFERENCES `locations` (`Id`),
  CONSTRAINT `FK_LocationHistories_Users_OperatorId` FOREIGN KEY (`OperatorId`) REFERENCES `users` (`Id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='位置历史表';

-- 13. 故障类型表
CREATE TABLE `faulttypes` (
  `Id` int NOT NULL AUTO_INCREMENT,
  `Name` varchar(50) NOT NULL COMMENT '类型名称',
  `Code` varchar(50) NOT NULL COMMENT '类型编码',
  `Description` varchar(200) DEFAULT NULL COMMENT '类型描述',
  `Severity` int NOT NULL DEFAULT '0' COMMENT '严重程度：0一般，1严重，2紧急',
  `SuggestedResponseTime` int DEFAULT NULL COMMENT '建议响应时间（小时）',
  `SuggestedResolutionTime` int DEFAULT NULL COMMENT '建议解决时间（小时）',
  `SortOrder` int NOT NULL DEFAULT '0' COMMENT '排序',
  `IsActive` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否激活',
  `CreatedAt` datetime NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`),
  UNIQUE KEY `IX_FaultTypes_Code` (`Code`),
  UNIQUE KEY `IX_FaultTypes_Name` (`Name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='故障类型表';

-- 14. 故障记录表
CREATE TABLE `faultrecords` (
  `Id` int NOT NULL AUTO_INCREMENT,
  `Title` varchar(100) NOT NULL COMMENT '标题',
  `Description` varchar(500) DEFAULT NULL COMMENT '描述',
  `AssetId` int NOT NULL COMMENT '资产ID',
  `FaultTypeId` int NOT NULL COMMENT '故障类型ID',
  `LocationId` int DEFAULT NULL COMMENT '位置ID',
  `ReporterId` int NOT NULL COMMENT '报告人ID',
  `ReportTime` datetime NOT NULL COMMENT '报告时间',
  `Status` int NOT NULL DEFAULT '0' COMMENT '状态：0待处理，1处理中，2已解决，3已关闭',
  `Severity` int NOT NULL DEFAULT '0' COMMENT '严重程度：0一般，1严重，2紧急',
  `AssigneeId` int DEFAULT NULL COMMENT '处理人ID',
  `AssignTime` datetime DEFAULT NULL COMMENT '分配时间',
  `ResponseTime` datetime DEFAULT NULL COMMENT '响应时间',
  `ResolutionTime` datetime DEFAULT NULL COMMENT '解决时间',
  `Resolution` varchar(500) DEFAULT NULL COMMENT '解决方案',
  `RootCause` varchar(500) DEFAULT NULL COMMENT '根本原因',
  `Notes` varchar(500) DEFAULT NULL COMMENT '备注',
  `IsReturned` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否返厂',
  `CreatedAt` datetime NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`),
  KEY `IX_FaultRecords_AssetId` (`AssetId`),
  KEY `IX_FaultRecords_FaultTypeId` (`FaultTypeId`),
  KEY `IX_FaultRecords_LocationId` (`LocationId`),
  KEY `IX_FaultRecords_ReporterId` (`ReporterId`),
  KEY `IX_FaultRecords_AssigneeId` (`AssigneeId`),
  CONSTRAINT `FK_FaultRecords_Assets_AssetId` FOREIGN KEY (`AssetId`) REFERENCES `assets` (`Id`),
  CONSTRAINT `FK_FaultRecords_FaultTypes_FaultTypeId` FOREIGN KEY (`FaultTypeId`) REFERENCES `faulttypes` (`Id`),
  CONSTRAINT `FK_FaultRecords_Locations_LocationId` FOREIGN KEY (`LocationId`) REFERENCES `locations` (`Id`),
  CONSTRAINT `FK_FaultRecords_Users_AssigneeId` FOREIGN KEY (`AssigneeId`) REFERENCES `users` (`Id`),
  CONSTRAINT `FK_FaultRecords_Users_ReporterId` FOREIGN KEY (`ReporterId`) REFERENCES `users` (`Id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='故障记录表';

-- 15. 供应商表
CREATE TABLE `suppliers` (
  `Id` int NOT NULL AUTO_INCREMENT,
  `Name` varchar(100) NOT NULL COMMENT '供应商名称',
  `Code` varchar(50) NOT NULL COMMENT '供应商编码',
  `ContactPerson` varchar(50) DEFAULT NULL COMMENT '联系人',
  `ContactPhone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `ContactEmail` varchar(100) DEFAULT NULL COMMENT '联系邮箱',
  `Address` varchar(200) DEFAULT NULL COMMENT '地址',
  `Type` int NOT NULL DEFAULT '0' COMMENT '类型：0硬件，1软件，2服务',
  `Notes` varchar(500) DEFAULT NULL COMMENT '备注',
  `IsActive` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否激活',
  `CreatedAt` datetime NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`),
  UNIQUE KEY `IX_Suppliers_Code` (`Code`),
  UNIQUE KEY `IX_Suppliers_Name` (`Name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='供应商表';

-- 16. 采购订单表
CREATE TABLE `purchaseorders` (
  `Id` int NOT NULL AUTO_INCREMENT,
  `OrderCode` varchar(50) NOT NULL COMMENT '订单编码',
  `Title` varchar(100) NOT NULL COMMENT '标题',
  `Description` varchar(500) DEFAULT NULL COMMENT '描述',
  `SupplierId` int NOT NULL COMMENT '供应商ID',
  `Status` int NOT NULL DEFAULT '0' COMMENT '状态：0草稿，1待审批，2已审批，3已发出，4已收货，5已完成，6已取消',
  `EstimatedDeliveryDate` datetime DEFAULT NULL COMMENT '预计交付日期',
  `ActualDeliveryDate` datetime DEFAULT NULL COMMENT '实际交付日期',
  `ApplicantId` int NOT NULL COMMENT '申请人ID',
  `ApplicationTime` datetime NOT NULL COMMENT '申请时间',
  `ApproverId` int DEFAULT NULL COMMENT '审批人ID',
  `ApprovalTime` datetime DEFAULT NULL COMMENT '审批时间',
  `TotalAmount` decimal(18,2) NOT NULL DEFAULT '0.00' COMMENT '总金额',
  `Notes` varchar(500) DEFAULT NULL COMMENT '备注',
  `CreatedAt` datetime NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`),
  UNIQUE KEY `IX_PurchaseOrders_OrderCode` (`OrderCode`),
  KEY `IX_PurchaseOrders_SupplierId` (`SupplierId`),
  KEY `IX_PurchaseOrders_ApplicantId` (`ApplicantId`),
  KEY `IX_PurchaseOrders_ApproverId` (`ApproverId`),
  CONSTRAINT `FK_PurchaseOrders_Suppliers_SupplierId` FOREIGN KEY (`SupplierId`) REFERENCES `suppliers` (`Id`),
  CONSTRAINT `FK_PurchaseOrders_Users_ApplicantId` FOREIGN KEY (`ApplicantId`) REFERENCES `users` (`Id`),
  CONSTRAINT `FK_PurchaseOrders_Users_ApproverId` FOREIGN KEY (`ApproverId`) REFERENCES `users` (`Id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='采购订单表';

-- 17. 采购项目表
CREATE TABLE `purchaseitems` (
  `Id` int NOT NULL AUTO_INCREMENT,
  `PurchaseOrderId` int NOT NULL COMMENT '采购订单ID',
  `ItemName` varchar(100) NOT NULL COMMENT '项目名称',
  `ItemCode` varchar(50) DEFAULT NULL COMMENT '项目编码',
  `Specification` varchar(200) DEFAULT NULL COMMENT '规格',
  `AssetTypeId` int DEFAULT NULL COMMENT '资产类型ID',
  `UnitPrice` decimal(18,2) NOT NULL DEFAULT '0.00' COMMENT '单价',
  `Quantity` int NOT NULL DEFAULT '1' COMMENT '数量',
  `TotalPrice` decimal(18,2) NOT NULL DEFAULT '0.00' COMMENT '总价',
  `Notes` varchar(500) DEFAULT NULL COMMENT '备注',
  `CreatedAt` datetime NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`),
  KEY `IX_PurchaseItems_PurchaseOrderId` (`PurchaseOrderId`),
  KEY `IX_PurchaseItems_AssetTypeId` (`AssetTypeId`),
  CONSTRAINT `FK_PurchaseItems_AssetTypes_AssetTypeId` FOREIGN KEY (`AssetTypeId`) REFERENCES `assettypes` (`Id`),
  CONSTRAINT `FK_PurchaseItems_PurchaseOrders_PurchaseOrderId` FOREIGN KEY (`PurchaseOrderId`) REFERENCES `purchaseorders` (`Id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='采购项目表';

-- 18. 返厂维修表
CREATE TABLE `returntofactories` (
  `Id` int NOT NULL AUTO_INCREMENT,
  `Code` varchar(50) NOT NULL COMMENT '返厂单号',
  `AssetId` int NOT NULL COMMENT '资产ID',
  `FaultRecordId` int NOT NULL COMMENT '故障记录ID',
  `SupplierId` int NOT NULL COMMENT '供应商ID',
  `Status` int NOT NULL DEFAULT '0' COMMENT '状态：0待送出，1已送出，2维修中，3已返回，4维修失败',
  `SenderId` int NOT NULL COMMENT '送出人ID',
  `SendTime` datetime DEFAULT NULL COMMENT '送出时间',
  `EstimatedReturnTime` datetime DEFAULT NULL COMMENT '预计返回时间',
  `ActualReturnTime` datetime DEFAULT NULL COMMENT '实际返回时间',
  `RepairResult` varchar(500) DEFAULT NULL COMMENT '维修结果',
  `RepairCost` decimal(18,2) DEFAULT NULL COMMENT '维修费用',
  `InWarranty` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否在保修期内',
  `Notes` varchar(500) DEFAULT NULL COMMENT '备注',
  `CreatedAt` datetime NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`),
  UNIQUE KEY `IX_ReturnToFactories_Code` (`Code`),
  KEY `IX_ReturnToFactories_AssetId` (`AssetId`),
  KEY `IX_ReturnToFactories_FaultRecordId` (`FaultRecordId`),
  KEY `IX_ReturnToFactories_SupplierId` (`SupplierId`),
  KEY `IX_ReturnToFactories_SenderId` (`SenderId`),
  CONSTRAINT `FK_ReturnToFactories_Assets_AssetId` FOREIGN KEY (`AssetId`) REFERENCES `assets` (`Id`),
  CONSTRAINT `FK_ReturnToFactories_FaultRecords_FaultRecordId` FOREIGN KEY (`FaultRecordId`) REFERENCES `faultrecords` (`Id`),
  CONSTRAINT `FK_ReturnToFactories_Suppliers_SupplierId` FOREIGN KEY (`SupplierId`) REFERENCES `suppliers` (`Id`),
  CONSTRAINT `FK_ReturnToFactories_Users_SenderId` FOREIGN KEY (`SenderId`) REFERENCES `users` (`Id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='返厂维修表';

-- 19. 资产入库表
CREATE TABLE `assetreceives` (
  `Id` int NOT NULL AUTO_INCREMENT,
  `ReceiveCode` varchar(50) NOT NULL COMMENT '入库单号',
  `PurchaseOrderId` int DEFAULT NULL COMMENT '采购订单ID',
  `Title` varchar(100) NOT NULL COMMENT '标题',
  `Description` varchar(500) DEFAULT NULL COMMENT '描述',
  `ReceiverId` int NOT NULL COMMENT '接收人ID',
  `ReceiveTime` datetime NOT NULL COMMENT '接收时间',
  `Status` int NOT NULL DEFAULT '0' COMMENT '状态：0草稿，1已提交，2已确认',
  `AssetTypeId` int DEFAULT NULL COMMENT '资产类型ID',
  `Quantity` int NOT NULL DEFAULT '1' COMMENT '数量',
  `TotalAmount` decimal(18,2) NOT NULL DEFAULT '0.00' COMMENT '总金额',
  `InitialLocationId` int DEFAULT NULL COMMENT '初始位置ID',
  `Notes` varchar(500) DEFAULT NULL COMMENT '备注',
  `CreatedAt` datetime NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`),
  UNIQUE KEY `IX_AssetReceives_ReceiveCode` (`ReceiveCode`),
  KEY `IX_AssetReceives_PurchaseOrderId` (`PurchaseOrderId`),
  KEY `IX_AssetReceives_ReceiverId` (`ReceiverId`),
  KEY `IX_AssetReceives_AssetTypeId` (`AssetTypeId`),
  KEY `IX_AssetReceives_InitialLocationId` (`InitialLocationId`),
  CONSTRAINT `FK_AssetReceives_AssetTypes_AssetTypeId` FOREIGN KEY (`AssetTypeId`) REFERENCES `assettypes` (`Id`),
  CONSTRAINT `FK_AssetReceives_Locations_InitialLocationId` FOREIGN KEY (`InitialLocationId`) REFERENCES `locations` (`Id`),
  CONSTRAINT `FK_AssetReceives_PurchaseOrders_PurchaseOrderId` FOREIGN KEY (`PurchaseOrderId`) REFERENCES `purchaseorders` (`Id`),
  CONSTRAINT `FK_AssetReceives_Users_ReceiverId` FOREIGN KEY (`ReceiverId`) REFERENCES `users` (`Id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='资产入库表';

-- 20. 维护订单表
CREATE TABLE `maintenanceorders` (
  `Id` int NOT NULL AUTO_INCREMENT,
  `OrderCode` varchar(50) NOT NULL COMMENT '维护单号',
  `Title` varchar(100) NOT NULL COMMENT '标题',
  `Description` varchar(500) DEFAULT NULL COMMENT '描述',
  `AssetId` int NOT NULL COMMENT '资产ID',
  `FaultRecordId` int DEFAULT NULL COMMENT '故障记录ID',
  `MaintenanceType` int NOT NULL DEFAULT '0' COMMENT '维护类型：0常规维护，1故障维修，2返厂维修跟踪',
  `Status` int NOT NULL DEFAULT '0' COMMENT '状态：0待处理，1处理中，2已完成，3已取消',
  `CreatorId` int NOT NULL COMMENT '创建人ID',
  `CreateTime` datetime NOT NULL COMMENT '创建时间',
  `AssigneeId` int DEFAULT NULL COMMENT '处理人ID',
  `AssignTime` datetime DEFAULT NULL COMMENT '分配时间',
  `PlanStartTime` datetime DEFAULT NULL COMMENT '计划开始时间',
  `PlanEndTime` datetime DEFAULT NULL COMMENT '计划结束时间',
  `ActualStartTime` datetime DEFAULT NULL COMMENT '实际开始时间',
  `ActualEndTime` datetime DEFAULT NULL COMMENT '实际结束时间',
  `Solution` varchar(500) DEFAULT NULL COMMENT '解决方案',
  `Notes` varchar(500) DEFAULT NULL COMMENT '备注',
  `CreatedAt` datetime NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`),
  UNIQUE KEY `IX_MaintenanceOrders_OrderCode` (`OrderCode`),
  KEY `IX_MaintenanceOrders_AssetId` (`AssetId`),
  KEY `IX_MaintenanceOrders_FaultRecordId` (`FaultRecordId`),
  KEY `IX_MaintenanceOrders_CreatorId` (`CreatorId`),
  KEY `IX_MaintenanceOrders_AssigneeId` (`AssigneeId`),
  CONSTRAINT `FK_MaintenanceOrders_Assets_AssetId` FOREIGN KEY (`AssetId`) REFERENCES `assets` (`Id`),
  CONSTRAINT `FK_MaintenanceOrders_FaultRecords_FaultRecordId` FOREIGN KEY (`FaultRecordId`) REFERENCES `faultrecords` (`Id`),
  CONSTRAINT `FK_MaintenanceOrders_Users_AssigneeId` FOREIGN KEY (`AssigneeId`) REFERENCES `users` (`Id`),
  CONSTRAINT `FK_MaintenanceOrders_Users_CreatorId` FOREIGN KEY (`CreatorId`) REFERENCES `users` (`Id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='维护订单表';

-- 21. 审计日志表
CREATE TABLE `auditlogs` (
  `Id` int NOT NULL AUTO_INCREMENT,
  `UserId` int DEFAULT NULL COMMENT '用户ID',
  `UserName` varchar(50) DEFAULT NULL COMMENT '用户名',
  `Type` varchar(50) NOT NULL COMMENT '日志类型',
  `TableName` varchar(50) DEFAULT NULL COMMENT '表名',
  `DateTime` datetime NOT NULL COMMENT '日期时间',
  `PrimaryKey` varchar(50) DEFAULT NULL COMMENT '主键',
  `OldValues` longtext COMMENT '旧值',
  `NewValues` longtext COMMENT '新值',
  `Action` varchar(50) DEFAULT NULL COMMENT '操作',
  `ClientIP` varchar(50) DEFAULT NULL COMMENT '客户端IP',
  PRIMARY KEY (`Id`),
  KEY `IX_AuditLogs_UserId` (`UserId`),
  CONSTRAINT `FK_AuditLogs_Users_UserId` FOREIGN KEY (`UserId`) REFERENCES `users` (`Id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='审计日志表';

-- 22. 刷新令牌表
CREATE TABLE `refreshtokens` (
  `Id` int NOT NULL AUTO_INCREMENT,
  `UserId` int NOT NULL COMMENT '用户ID',
  `Token` varchar(200) NOT NULL COMMENT '令牌',
  `JwtId` varchar(200) NOT NULL COMMENT 'JWT ID',
  `IsUsed` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已使用',
  `IsRevoked` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已撤销',
  `AddedDate` datetime NOT NULL COMMENT '添加日期',
  `ExpiryDate` datetime NOT NULL COMMENT '过期日期',
  PRIMARY KEY (`Id`),
  KEY `IX_RefreshTokens_UserId` (`UserId`),
  CONSTRAINT `FK_RefreshTokens_Users_UserId` FOREIGN KEY (`UserId`) REFERENCES `users` (`Id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='刷新令牌表';

-- 23. EF Migrations History表
CREATE TABLE `__efmigrationshistory` (
  `MigrationId` varchar(150) NOT NULL,
  `ProductVersion` varchar(32) NOT NULL,
  PRIMARY KEY (`MigrationId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='EF迁移历史表';

-- 24. 周期规则表
CREATE TABLE `periodicrules` (
  `Id` int NOT NULL AUTO_INCREMENT,
  `Name` varchar(100) NOT NULL COMMENT '规则名称',
  `Frequency` int NOT NULL DEFAULT '0' COMMENT '频率：0每天，1每周，2每月，3每季度，4每年',
  `DayOfWeek` int DEFAULT NULL COMMENT '每周几（0-6）',
  `DayOfMonth` int DEFAULT NULL COMMENT '每月几号（1-31）',
  `IsActive` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否激活',
  `CreatedAt` datetime NOT NULL COMMENT '创建时间',
  `LastGeneratedAt` datetime DEFAULT NULL COMMENT '上次生成时间',
  PRIMARY KEY (`Id`),
  UNIQUE KEY `IX_PeriodicRules_Name` (`Name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='周期规则表';

-- 25. PDCA计划表
CREATE TABLE `pdcaplans` (
  `Id` int NOT NULL AUTO_INCREMENT,
  `Title` varchar(100) NOT NULL COMMENT '计划标题',
  `Type` int NOT NULL DEFAULT '0' COMMENT '类型：0硬件，1软件，2流程',
  `PlanStartDate` datetime NOT NULL COMMENT '计划开始日期',
  `PlanEndDate` datetime NOT NULL COMMENT '计划结束日期',
  `ActualEndDate` datetime DEFAULT NULL COMMENT '实际结束日期',
  `Status` int NOT NULL DEFAULT '0' COMMENT '状态：0计划，1进行中，2已完成，3已取消',
  `CompletionRate` decimal(5,2) NOT NULL DEFAULT '0.00' COMMENT '完成率',
  `ResponsiblePersonId` int NOT NULL COMMENT '负责人ID',
  `Notes` varchar(500) DEFAULT NULL COMMENT '备注',
  `CreatedAt` datetime NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`),
  KEY `IX_PdcaPlans_ResponsiblePersonId` (`ResponsiblePersonId`),
  CONSTRAINT `FK_PdcaPlans_Users_ResponsiblePersonId` FOREIGN KEY (`ResponsiblePersonId`) REFERENCES `users` (`Id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='PDCA计划表';

-- 26. 任务表
CREATE TABLE `tasks` (
  `Id` int NOT NULL AUTO_INCREMENT,
  `Title` varchar(100) NOT NULL COMMENT '任务标题',
  `Description` varchar(500) DEFAULT NULL COMMENT '任务描述',
  `TaskType` int NOT NULL DEFAULT '0' COMMENT '任务类型：0普通任务，1周期任务，2计划任务',
  `Status` int NOT NULL DEFAULT '0' COMMENT '状态：0待处理，1处理中，2已完成，3已取消',
  `AssignedToId` int DEFAULT NULL COMMENT '分配给谁',
  `CreatedById` int NOT NULL COMMENT '创建人ID',
  `CreatedAt` datetime NOT NULL COMMENT '创建时间',
  `DueDate` datetime DEFAULT NULL COMMENT '截止日期',
  `CompletedAt` datetime DEFAULT NULL COMMENT '完成时间',
  `RelatedAssetId` int DEFAULT NULL COMMENT '相关资产ID',
  `RelatedLocationId` int DEFAULT NULL COMMENT '相关位置ID',
  `PeriodicRuleId` int DEFAULT NULL COMMENT '周期规则ID',
  `PdcaPlanId` int DEFAULT NULL COMMENT 'PDCA计划ID',
  `Notes` varchar(500) DEFAULT NULL COMMENT '备注',
  `UpdatedAt` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`),
  KEY `IX_Tasks_AssignedToId` (`AssignedToId`),
  KEY `IX_Tasks_CreatedById` (`CreatedById`),
  KEY `IX_Tasks_RelatedAssetId` (`RelatedAssetId`),
  KEY `IX_Tasks_RelatedLocationId` (`RelatedLocationId`),
  KEY `IX_Tasks_PeriodicRuleId` (`PeriodicRuleId`),
  KEY `IX_Tasks_PdcaPlanId` (`PdcaPlanId`),
  CONSTRAINT `FK_Tasks_Assets_RelatedAssetId` FOREIGN KEY (`RelatedAssetId`) REFERENCES `assets` (`Id`),
  CONSTRAINT `FK_Tasks_Locations_RelatedLocationId` FOREIGN KEY (`RelatedLocationId`) REFERENCES `locations` (`Id`),
  CONSTRAINT `FK_Tasks_PdcaPlans_PdcaPlanId` FOREIGN KEY (`PdcaPlanId`) REFERENCES `pdcaplans` (`Id`),
  CONSTRAINT `FK_Tasks_PeriodicRules_PeriodicRuleId` FOREIGN KEY (`PeriodicRuleId`) REFERENCES `periodicrules` (`Id`),
  CONSTRAINT `FK_Tasks_Users_AssignedToId` FOREIGN KEY (`AssignedToId`) REFERENCES `users` (`Id`),
  CONSTRAINT `FK_Tasks_Users_CreatedById` FOREIGN KEY (`CreatedById`) REFERENCES `users` (`Id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务表';

-- ========================================
-- 第三部分：插入测试数据
-- ========================================

-- 1. 创建角色 (roles)
INSERT INTO roles (Name, Code, Description, IsActive, CreatedAt, UpdatedAt) VALUES
('超级管理员', 'SUPER_ADMIN', '系统超级管理员，拥有所有权限', 1, NOW(), NOW()),
('IT管理员', 'IT_ADMIN', 'IT部门管理员，管理IT资产和系统配置', 1, NOW(), NOW()),
('资产管理员', 'ASSET_ADMIN', '负责资产的录入、调拨和报废管理', 1, NOW(), NOW()),
('位置管理员', 'LOCATION_ADMIN', '负责管理位置和资产位置分配', 1, NOW(), NOW()),
('故障管理员', 'FAULT_ADMIN', '负责故障的处理和维修管理', 1, NOW(), NOW()),
('维修人员', 'MAINTENANCE_STAFF', '执行设备维修和保养任务', 1, NOW(), NOW()),
('普通用户', 'NORMAL_USER', '普通用户，可以查看和使用设备', 1, NOW(), NOW());

-- 2. 创建部门 (departments)
INSERT INTO departments (Name, Code, ParentId, Path, Description, IsActive, CreatedAt, UpdatedAt) VALUES
-- 一级部门
('总经办', 'CEO_OFFICE', NULL, '1', '公司总经理办公室', 1, NOW(), NOW()),
('IT部门', 'IT_DEPT', NULL, '2', '信息技术部门', 1, NOW(), NOW()),
('人力资源部', 'HR_DEPT', NULL, '3', '人力资源管理部门', 1, NOW(), NOW()),
('财务部', 'FINANCE_DEPT', NULL, '4', '财务管理部门', 1, NOW(), NOW()),
('生产部', 'PRODUCTION_DEPT', NULL, '5', '生产制造部门', 1, NOW(), NOW()),
('工程部', 'ENGINEERING_DEPT', NULL, '6', '工程技术部门', 1, NOW(), NOW()),
('质量部', 'QUALITY_DEPT', NULL, '7', '质量管理部门', 1, NOW(), NOW()),
('采购部', 'PURCHASE_DEPT', NULL, '8', '采购管理部门', 1, NOW(), NOW()),
('销售部', 'SALES_DEPT', NULL, '9', '销售管理部门', 1, NOW(), NOW());

-- 获取部门ID
SET @it_dept_id = 2;
SET @production_dept_id = 5;
SET @engineering_dept_id = 6;

-- 二级部门（IT部门下）
INSERT INTO departments (Name, Code, ParentId, Path, Description, IsActive, CreatedAt, UpdatedAt) VALUES
('IT基础设施', 'IT_INFRASTRUCTURE', @it_dept_id, '2-1', 'IT基础设施管理', 1, NOW(), NOW()),
('IT应用开发', 'IT_APPLICATION', @it_dept_id, '2-2', 'IT应用系统开发', 1, NOW(), NOW()),
('IT运维', 'IT_OPERATION', @it_dept_id, '2-3', 'IT系统运维管理', 1, NOW(), NOW()),
('IT安全', 'IT_SECURITY', @it_dept_id, '2-4', 'IT安全管理', 1, NOW(), NOW());

-- 二级部门（生产部下）
INSERT INTO departments (Name, Code, ParentId, Path, Description, IsActive, CreatedAt, UpdatedAt) VALUES
('生产一部', 'PRODUCTION_DEPT_1', @production_dept_id, '5-1', '生产制造一部', 1, NOW(), NOW()),
('生产二部', 'PRODUCTION_DEPT_2', @production_dept_id, '5-2', '生产制造二部', 1, NOW(), NOW()),
('生产计划', 'PRODUCTION_PLANNING', @production_dept_id, '5-3', '生产计划管理', 1, NOW(), NOW());

-- 二级部门（工程部下）
INSERT INTO departments (Name, Code, ParentId, Path, Description, IsActive, CreatedAt, UpdatedAt) VALUES
('设备工程', 'EQUIPMENT_ENGINEERING', @engineering_dept_id, '6-1', '设备工程管理', 1, NOW(), NOW()),
('工艺工程', 'PROCESS_ENGINEERING', @engineering_dept_id, '6-2', '工艺工程管理', 1, NOW(), NOW()),
('自动化工程', 'AUTOMATION_ENGINEERING', @engineering_dept_id, '6-3', '自动化工程管理', 1, NOW(), NOW());

-- 新增C工厂部门
INSERT INTO departments (Name, Code, ParentId, Path, Description, IsActive, CreatedAt, UpdatedAt) VALUES
('C工厂', 'FACTORY_C', NULL, '10', 'C工厂管理部门', 1, NOW(), NOW());

-- 获取C工厂部门ID
SET @factory_c_id = LAST_INSERT_ID();

-- C工厂下属部门
INSERT INTO departments (Name, Code, ParentId, Path, Description, IsActive, CreatedAt, UpdatedAt) VALUES
('C1产线部', 'C1_LINE_DEPT', @factory_c_id, '10-1', 'C1产线管理部门', 1, NOW(), NOW()),
('C2产线部', 'C2_LINE_DEPT', @factory_c_id, '10-2', 'C2产线管理部门', 1, NOW(), NOW()),
('C工厂设备部', 'C_EQUIPMENT_DEPT', @factory_c_id, '10-3', 'C工厂设备管理部门', 1, NOW(), NOW()),
('C工厂质检部', 'C_QC_DEPT', @factory_c_id, '10-4', 'C工厂质量检查部门', 1, NOW(), NOW()),
('C工厂物流部', 'C_LOGISTICS_DEPT', @factory_c_id, '10-5', 'C工厂物流管理部门', 1, NOW(), NOW()),
('C工厂行政部', 'C_ADMIN_DEPT', @factory_c_id, '10-6', 'C工厂行政管理部门', 1, NOW(), NOW());

-- 3. 创建用户 (users)
INSERT INTO users (Username, PasswordHash, SecurityStamp, Name, Email, Mobile, DepartmentId, Position, Gender, DefaultRoleId, IsActive, CreatedAt, UpdatedAt) VALUES
-- 管理员账号
('admin', 'AQAAAAIAAYagAAAAECkIJmNaLvk9o9JVh7RoQTI9VvL6B8U3uLQkzQvCeD2qcHKJWmjGsdzIgFg3Mg==', 'E89F4B8C79F14A798D97D57E97D45D46', '系统管理员', '<EMAIL>', '13800000000', 1, '管理员', 0, 1, 1, NOW(), NOW()),
('itadmin', 'AQAAAAIAAYagAAAAECkIJmNaLvk9o9JVh7RoQTI9VvL6B8U3uLQkzQvCeD2qcHKJWmjGsdzIgFg3Mg==', 'E89F4B8C79F14A798D97D57E97D45D46', 'IT管理员', '<EMAIL>', '13800000001', 2, '管理员', 1, 2, 1, NOW(), NOW()),
('assetmgr', 'AQAAAAIAAYagAAAAECkIJmNaLvk9o9JVh7RoQTI9VvL6B8U3uLQkzQvCeD2qcHKJWmjGsdzIgFg3Mg==', 'E89F4B8C79F14A798D97D57E97D45D46', '资产管理员', '<EMAIL>', '13800000002', 2, '经理', 1, 3, 1, NOW(), NOW()),
('locationmgr', 'AQAAAAIAAYagAAAAECkIJmNaLvk9o9JVh7RoQTI9VvL6B8U3uLQkzQvCeD2qcHKJWmjGsdzIgFg3Mg==', 'E89F4B8C79F14A798D97D57E97D45D46', '位置管理员', '<EMAIL>', '13800000003', 2, '经理', 1, 4, 1, NOW(), NOW()),
('faultmgr', 'AQAAAAIAAYagAAAAECkIJmNaLvk9o9JVh7RoQTI9VvL6B8U3uLQkzQvCeD2qcHKJWmjGsdzIgFg3Mg==', 'E89F4B8C79F14A798D97D57E97D45D46', '故障管理员', '<EMAIL>', '13800000004', 2, '经理', 1, 5, 1, NOW(), NOW()),
('maintenance', 'AQAAAAIAAYagAAAAECkIJmNaLvk9o9JVh7RoQTI9VvL6B8U3uLQkzQvCeD2qcHKJWmjGsdzIgFg3Mg==', 'E89F4B8C79F14A798D97D57E97D45D46', '维修人员', '<EMAIL>', '13800000005', 2, '技术员', 1, 6, 1, NOW(), NOW()),

-- 普通用户账号
('user1', 'AQAAAAIAAYagAAAAECkIJmNaLvk9o9JVh7RoQTI9VvL6B8U3uLQkzQvCeD2qcHKJWmjGsdzIgFg3Mg==', 'E89F4B8C79F14A798D97D57E97D45D46', '张三', '<EMAIL>', '13900000001', 3, '职员', 1, 7, 1, NOW(), NOW()),
('user2', 'AQAAAAIAAYagAAAAECkIJmNaLvk9o9JVh7RoQTI9VvL6B8U3uLQkzQvCeD2qcHKJWmjGsdzIgFg3Mg==', 'E89F4B8C79F14A798D97D57E97D45D46', '李四', '<EMAIL>', '13900000002', 4, '职员', 1, 7, 1, NOW(), NOW()),
('user3', 'AQAAAAIAAYagAAAAECkIJmNaLvk9o9JVh7RoQTI9VvL6B8U3uLQkzQvCeD2qcHKJWmjGsdzIgFg3Mg==', 'E89F4B8C79F14A798D97D57E97D45D46', '王五', '<EMAIL>', '13900000003', 5, '职员', 2, 7, 1, NOW(), NOW()),

-- 生产和工程部用户
('engineer1', 'AQAAAAIAAYagAAAAECkIJmNaLvk9o9JVh7RoQTI9VvL6B8U3uLQkzQvCeD2qcHKJWmjGsdzIgFg3Mg==', 'E89F4B8C79F14A798D97D57E97D45D46', '工程师1', '<EMAIL>', '13900000004', 6, '工程师', 1, 7, 1, NOW(), NOW()),

-- C工厂管理人员
('M01', 'AQAAAAIAAYagAAAAECkIJmNaLvk9o9JVh7RoQTI9VvL6B8U3uLQkzQvCeD2qcHKJWmjGsdzIgFg3Mg==', 'E89F4B8C79F14A798D97D57E97D45D46', '王经理', '<EMAIL>', '13900000001', 10, '经理', 0, 7, 1, NOW(), NOW()),
('M02', 'AQAAAAIAAYagAAAAECkIJmNaLvk9o9JVh7RoQTI9VvL6B8U3uLQkzQvCeD2qcHKJWmjGsdzIgFg3Mg==', 'E89F4B8C79F14A798D97D57E97D45D46', '李经理', '<EMAIL>', '13900000002', 11, '经理', 0, 7, 1, NOW(), NOW()),
('M03', 'AQAAAAIAAYagAAAAECkIJmNaLvk9o9JVh7RoQTI9VvL6B8U3uLQkzQvCeD2qcHKJWmjGsdzIgFg3Mg==', 'E89F4B8C79F14A798D97D57E97D45D46', '张经理', '<EMAIL>', '13900000003', 12, '经理', 0, 7, 1, NOW(), NOW()),
('M04', 'AQAAAAIAAYagAAAAECkIJmNaLvk9o9JVh7RoQTI9VvL6B8U3uLQkzQvCeD2qcHKJWmjGsdzIgFg3Mg==', 'E89F4B8C79F14A798D97D57E97D45D46', '刘经理', '<EMAIL>', '13900000004', 13, '经理', 0, 7, 1, NOW(), NOW()),
('M05', 'AQAAAAIAAYagAAAAECkIJmNaLvk9o9JVh7RoQTI9VvL6B8U3uLQkzQvCeD2qcHKJWmjGsdzIgFg3Mg==', 'E89F4B8C79F14A798D97D57E97D45D46', '赵经理', '<EMAIL>', '13900000005', 14, '组长', 0, 7, 1, NOW(), NOW()),
('M06', 'AQAAAAIAAYagAAAAECkIJmNaLvk9o9JVh7RoQTI9VvL6B8U3uLQkzQvCeD2qcHKJWmjGsdzIgFg3Mg==', 'E89F4B8C79F14A798D97D57E97D45D46', '钱经理', '<EMAIL>', '13900000006', 15, '组长', 0, 7, 1, NOW(), NOW()),

-- 使用人员
('U01', 'AQAAAAIAAYagAAAAECkIJmNaLvk9o9JVh7RoQTI9VvL6B8U3uLQkzQvCeD2qcHKJWmjGsdzIgFg3Mg==', 'E89F4B8C79F14A798D97D57E97D45D46', '张操作员', '<EMAIL>', '13911111101', 10, '操作员', 0, 7, 1, NOW(), NOW()),
('U02', 'AQAAAAIAAYagAAAAECkIJmNaLvk9o9JVh7RoQTI9VvL6B8U3uLQkzQvCeD2qcHKJWmjGsdzIgFg3Mg==', 'E89F4B8C79F14A798D97D57E97D45D46', '王操作员', '<EMAIL>', '13911111102', 10, '操作员', 0, 7, 1, NOW(), NOW()),
('U03', 'AQAAAAIAAYagAAAAECkIJmNaLvk9o9JVh7RoQTI9VvL6B8U3uLQkzQvCeD2qcHKJWmjGsdzIgFg3Mg==', 'E89F4B8C79F14A798D97D57E97D45D46', '李操作员', '<EMAIL>', '13911111103', 11, '操作员', 0, 7, 1, NOW(), NOW()),
('U04', 'AQAAAAIAAYagAAAAECkIJmNaLvk9o9JVh7RoQTI9VvL6B8U3uLQkzQvCeD2qcHKJWmjGsdzIgFg3Mg==', 'E89F4B8C79F14A798D97D57E97D45D46', '赵操作员', '<EMAIL>', '13911111104', 12, '操作员', 0, 7, 1, NOW(), NOW()),
('U05', 'AQAAAAIAAYagAAAAECkIJmNaLvk9o9JVh7RoQTI9VvL6B8U3uLQkzQvCeD2qcHKJWmjGsdzIgFg3Mg==', 'E89F4B8C79F14A798D97D57E97D45D46', '钱操作员', '<EMAIL>', '13911111105', 13, '操作员', 0, 7, 1, NOW(), NOW());

-- 为新创建的部门更新管理员
UPDATE departments SET ManagerId = 11 WHERE Id = 10;
UPDATE departments SET ManagerId = 12 WHERE Id = 11;
UPDATE departments SET ManagerId = 13 WHERE Id = 12;
UPDATE departments SET ManagerId = 14 WHERE Id = 13;
UPDATE departments SET ManagerId = 15 WHERE Id = 14;
UPDATE departments SET ManagerId = 16 WHERE Id = 15;

-- 4. 创建用户角色关系 (userroles)
-- 使用 INSERT IGNORE 避免主键冲突
INSERT IGNORE INTO userroles (UserId, RoleId, CreatedAt) VALUES
(1, 1, NOW()), -- admin -> 超级管理员
(2, 2, NOW()), -- itadmin -> IT管理员
(3, 3, NOW()), -- assetmgr -> 资产管理员
(4, 4, NOW()), -- locationmgr -> 位置管理员
(5, 5, NOW()), -- faultmgr -> 故障管理员
(6, 6, NOW()), -- maintenance -> 维修人员
(7, 7, NOW()), -- user1 -> 普通用户
(8, 7, NOW()), -- user2 -> 普通用户
(9, 7, NOW()); -- user3 -> 普通用户

-- 5. 创建菜单 (menus)
INSERT INTO menus (Name, Code, ParentId, Type, Icon, Path, Component, Permission, Description, SortOrder, IsExternal, KeepAlive, IsVisible, IsActive, CreatedAt, UpdatedAt) VALUES
-- 主菜单
('首页', 'DASHBOARD', NULL, 0, 'dashboard', '/dashboard', 'Dashboard', 'dashboard', '系统首页', 1, 0, 1, 1, 1, NOW(), NOW()),
('资产管理', 'ASSET', NULL, 0, 'computer', '/asset', 'Layout', 'asset', '资产管理模块', 2, 0, 1, 1, 1, NOW(), NOW()),
('故障管理', 'FAULT', NULL, 0, 'bug', '/fault', 'Layout', 'fault', '故障管理模块', 3, 0, 1, 1, 1, NOW(), NOW()),
('维修管理', 'MAINTENANCE', NULL, 0, 'tool', '/maintenance', 'Layout', 'maintenance', '维修管理模块', 4, 0, 1, 1, 1, NOW(), NOW()),
('采购管理', 'PURCHASE', NULL, 0, 'shopping-cart', '/purchase', 'Layout', 'purchase', '采购管理模块', 5, 0, 1, 1, 1, NOW(), NOW()),
('系统管理', 'SYSTEM', NULL, 0, 'setting', '/system', 'Layout', 'system', '系统管理模块', 6, 0, 1, 1, 1, NOW(), NOW()),
('任务管理', 'TASK', NULL, 0, 'calendar', '/task', 'Layout', 'task', '任务管理模块', 7, 0, 1, 1, 1, NOW(), NOW());

-- 获取主菜单ID
SET @dashboard_id = LAST_INSERT_ID();
SET @asset_id = @dashboard_id + 1;
SET @fault_id = @dashboard_id + 2;
SET @maintenance_id = @dashboard_id + 3;
SET @purchase_id = @dashboard_id + 4;
SET @system_id = @dashboard_id + 5;
SET @task_id = @dashboard_id + 6;

-- 资产管理子菜单
INSERT INTO menus (Name, Code, ParentId, Type, Icon, Path, Component, Permission, Description, SortOrder, IsExternal, KeepAlive, IsVisible, IsActive, CreatedAt, UpdatedAt) VALUES
('资产列表', 'ASSET:LIST', @asset_id, 0, 'list', '/asset/list', 'asset/List', 'asset:list', '资产列表', 1, 0, 1, 1, 1, NOW(), NOW()),
('资产类型', 'ASSET:TYPE', @asset_id, 0, 'tag', '/asset/type', 'asset/Type', 'asset:type', '资产类型管理', 2, 0, 1, 1, 1, NOW(), NOW()),
('位置管理', 'ASSET:LOCATION', @asset_id, 0, 'environment', '/asset/location', 'asset/Location', 'asset:location', '位置管理', 3, 0, 1, 1, 1, NOW(), NOW());

-- 故障管理子菜单
INSERT INTO menus (Name, Code, ParentId, Type, Icon, Path, Component, Permission, Description, SortOrder, IsExternal, KeepAlive, IsVisible, IsActive, CreatedAt, UpdatedAt) VALUES
('故障列表', 'FAULT:LIST', @fault_id, 0, 'exception', '/fault/list', 'fault/List', 'fault:list', '故障列表', 1, 0, 1, 1, 1, NOW(), NOW()),
('故障类型', 'FAULT:TYPE', @fault_id, 0, 'tag', '/fault/type', 'fault/Type', 'fault:type', '故障类型管理', 2, 0, 1, 1, 1, NOW(), NOW()),
('返厂维修', 'FAULT:RETURN', @fault_id, 0, 'rollback', '/fault/return', 'fault/Return', 'fault:return', '返厂维修管理', 3, 0, 1, 1, 1, NOW(), NOW());

-- 维修管理子菜单
INSERT INTO menus (Name, Code, ParentId, Type, Icon, Path, Component, Permission, Description, SortOrder, IsExternal, KeepAlive, IsVisible, IsActive, CreatedAt, UpdatedAt) VALUES
('维修列表', 'MAINTENANCE:LIST', @maintenance_id, 0, 'build', '/maintenance/list', 'maintenance/List', 'maintenance:list', '维修列表', 1, 0, 1, 1, 1, NOW(), NOW()),
('维修统计', 'MAINTENANCE:STATS', @maintenance_id, 0, 'bar-chart', '/maintenance/stats', 'maintenance/Stats', 'maintenance:stats', '维修统计', 2, 0, 1, 1, 1, NOW(), NOW());

-- 采购管理子菜单
INSERT INTO menus (Name, Code, ParentId, Type, Icon, Path, Component, Permission, Description, SortOrder, IsExternal, KeepAlive, IsVisible, IsActive, CreatedAt, UpdatedAt) VALUES
('采购单', 'PURCHASE:ORDER', @purchase_id, 0, 'file-text', '/purchase/order', 'purchase/Order', 'purchase:order', '采购单管理', 1, 0, 1, 1, 1, NOW(), NOW()),
('供应商', 'PURCHASE:SUPPLIER', @purchase_id, 0, 'team', '/purchase/supplier', 'purchase/Supplier', 'purchase:supplier', '供应商管理', 2, 0, 1, 1, 1, NOW(), NOW()),
('资产入库', 'PURCHASE:RECEIVE', @purchase_id, 0, 'import', '/purchase/receive', 'purchase/Receive', 'purchase:receive', '资产入库', 3, 0, 1, 1, 1, NOW(), NOW());

-- 系统管理子菜单
INSERT INTO menus (Name, Code, ParentId, Type, Icon, Path, Component, Permission, Description, SortOrder, IsExternal, KeepAlive, IsVisible, IsActive, CreatedAt, UpdatedAt) VALUES
('用户管理', 'SYSTEM:USER', @system_id, 0, 'user', '/system/user', 'system/User', 'system:user', '用户管理', 1, 0, 1, 1, 1, NOW(), NOW()),
('角色管理', 'SYSTEM:ROLE', @system_id, 0, 'team', '/system/role', 'system/Role', 'system:role', '角色管理', 2, 0, 1, 1, 1, NOW(), NOW()),
('菜单管理', 'SYSTEM:MENU', @system_id, 0, 'menu', '/system/menu', 'system/Menu', 'system:menu', '菜单管理', 3, 0, 1, 1, 1, NOW(), NOW()),
('部门管理', 'SYSTEM:DEPARTMENT', @system_id, 0, 'apartment', '/system/department', 'system/Department', 'system:department', '部门管理', 4, 0, 1, 1, 1, NOW(), NOW()),
('日志管理', 'SYSTEM:LOG', @system_id, 0, 'file-text', '/system/log', 'system/Log', 'system:log', '日志管理', 5, 0, 1, 1, 1, NOW(), NOW());

-- 任务管理子菜单
INSERT INTO menus (Name, Code, ParentId, Type, Icon, Path, Component, Permission, Description, SortOrder, IsExternal, KeepAlive, IsVisible, IsActive, CreatedAt, UpdatedAt) VALUES
('任务列表', 'TASK:LIST', @task_id, 0, 'unordered-list', '/task/list', 'task/List', 'task:list', '任务列表', 1, 0, 1, 1, 1, NOW(), NOW()),
('周期任务', 'TASK:PERIODIC', @task_id, 0, 'reload', '/task/periodic', 'task/Periodic', 'task:periodic', '周期任务管理', 2, 0, 1, 1, 1, NOW(), NOW()),
('PDCA计划', 'TASK:PDCA', @task_id, 0, 'sync', '/task/pdca', 'task/Pdca', 'task:pdca', 'PDCA计划管理', 3, 0, 1, 1, 1, NOW(), NOW());

-- 6. 分配角色菜单权限 (rolemenus)
-- 为超级管理员分配所有菜单
INSERT INTO rolemenus (RoleId, MenuId, CreatedAt)
SELECT 1, Id, NOW() FROM menus;

-- 为IT管理员分配部分菜单
INSERT INTO rolemenus (RoleId, MenuId, CreatedAt)
SELECT 2, Id, NOW() FROM menus WHERE Code IN ('DASHBOARD', 'ASSET', 'FAULT', 'MAINTENANCE', 'PURCHASE', 'TASK',
                                            'ASSET:LIST', 'ASSET:TYPE', 'ASSET:LOCATION',
                                            'FAULT:LIST', 'FAULT:TYPE', 'FAULT:RETURN',
                                            'MAINTENANCE:LIST', 'MAINTENANCE:STATS',
                                            'PURCHASE:ORDER', 'PURCHASE:SUPPLIER', 'PURCHASE:RECEIVE',
                                            'TASK:LIST', 'TASK:PERIODIC', 'TASK:PDCA');

-- 为资产管理员分配资产相关菜单
INSERT INTO rolemenus (RoleId, MenuId, CreatedAt)
SELECT 3, Id, NOW() FROM menus WHERE Code IN ('DASHBOARD', 'ASSET', 'ASSET:LIST', 'ASSET:TYPE', 'ASSET:LOCATION', 'TASK:LIST');

-- 为位置管理员分配位置相关菜单
INSERT INTO rolemenus (RoleId, MenuId, CreatedAt)
SELECT 4, Id, NOW() FROM menus WHERE Code IN ('DASHBOARD', 'ASSET', 'ASSET:LOCATION', 'TASK:LIST');

-- 为故障管理员分配故障相关菜单
INSERT INTO rolemenus (RoleId, MenuId, CreatedAt)
SELECT 5, Id, NOW() FROM menus WHERE Code IN ('DASHBOARD', 'FAULT', 'FAULT:LIST', 'FAULT:TYPE', 'FAULT:RETURN', 'TASK:LIST');

-- 为维修人员分配维修相关菜单
INSERT INTO rolemenus (RoleId, MenuId, CreatedAt)
SELECT 6, Id, NOW() FROM menus WHERE Code IN ('DASHBOARD', 'MAINTENANCE', 'MAINTENANCE:LIST', 'TASK:LIST');

-- 为普通用户分配基础菜单
INSERT INTO rolemenus (RoleId, MenuId, CreatedAt)
SELECT 7, Id, NOW() FROM menus WHERE Code IN ('DASHBOARD', 'ASSET:LIST', 'FAULT:LIST', 'TASK:LIST');

-- 7. 创建位置 (locations)
-- 工厂
INSERT INTO locations (Code, Name, Type, Path, Description, IsActive, CreatedAt, UpdatedAt) VALUES
('FACTORY_A', 'A工厂', 0, '1', 'A工厂主厂区', 1, NOW(), NOW()),
('FACTORY_B', 'B工厂', 0, '2', 'B工厂主厂区', 1, NOW(), NOW());

-- 获取工厂ID
SET @factory_a_id = LAST_INSERT_ID();
SET @factory_b_id = @factory_a_id + 1;

-- 产线（A工厂）
INSERT INTO locations (Code, Name, Type, ParentId, Path, Description, IsActive, CreatedAt, UpdatedAt) VALUES
('A_LINE_1', 'A1产线', 1, @factory_a_id, '1-1', 'A工厂1号产线', 1, NOW(), NOW()),
('A_LINE_2', 'A2产线', 1, @factory_a_id, '1-2', 'A工厂2号产线', 1, NOW(), NOW()),
('A_LINE_3', 'A3产线', 1, @factory_a_id, '1-3', 'A工厂3号产线', 1, NOW(), NOW()),
('A_WAREHOUSE', 'A工厂仓库', 3, @factory_a_id, '1-4', 'A工厂仓库区域', 1, NOW(), NOW());

-- 获取产线ID
SET @a_line_1_id = LAST_INSERT_ID();
SET @a_line_2_id = @a_line_1_id + 1;
SET @a_line_3_id = @a_line_1_id + 2;
SET @a_warehouse_id = @a_line_1_id + 3;

-- 产线（B工厂）
INSERT INTO locations (Code, Name, Type, ParentId, Path, Description, IsActive, CreatedAt, UpdatedAt) VALUES
('B_LINE_1', 'B1产线', 1, @factory_b_id, '2-1', 'B工厂1号产线', 1, NOW(), NOW()),
('B_LINE_2', 'B2产线', 1, @factory_b_id, '2-2', 'B工厂2号产线', 1, NOW(), NOW()),
('B_WAREHOUSE', 'B工厂仓库', 3, @factory_b_id, '2-3', 'B工厂仓库区域', 1, NOW(), NOW());

-- 获取B工厂产线ID
SET @b_line_1_id = LAST_INSERT_ID();
SET @b_line_2_id = @b_line_1_id + 1;
SET @b_warehouse_id = @b_line_1_id + 2;

-- 工位（A1产线）
INSERT INTO locations (Code, Name, Type, ParentId, Path, Description, IsActive, CreatedAt, UpdatedAt) VALUES
('A1_WS_1', 'A1-1工位', 2, @a_line_1_id, '1-3-1', 'A1产线1号工位', 1, NOW(), NOW()),
('A1_WS_2', 'A1-2工位', 2, @a_line_1_id, '1-3-2', 'A1产线2号工位', 1, NOW(), NOW()),
('A1_WS_3', 'A1-3工位', 2, @a_line_1_id, '1-3-3', 'A1产线3号工位', 1, NOW(), NOW());

-- 工位（A2产线）
INSERT INTO locations (Code, Name, Type, ParentId, Path, Description, IsActive, CreatedAt, UpdatedAt) VALUES
('A2_WS_1', 'A2-1工位', 2, @a_line_2_id, '1-4-1', 'A2产线1号工位', 1, NOW(), NOW()),
('A2_WS_2', 'A2-2工位', 2, @a_line_2_id, '1-4-2', 'A2产线2号工位', 1, NOW(), NOW());

-- 工位（B1产线）
INSERT INTO locations (Code, Name, Type, ParentId, Path, Description, IsActive, CreatedAt, UpdatedAt) VALUES
('B1_WS_1', 'B1-1工位', 2, @b_line_1_id, '2-7-1', 'B1产线1号工位', 1, NOW(), NOW()),
('B1_WS_2', 'B1-2工位', 2, @b_line_1_id, '2-7-2', 'B1产线2号工位', 1, NOW(), NOW());

-- 新增C工厂位置层级结构
INSERT INTO locations (Code, Name, Type, Path, Description, DefaultDepartmentId, DefaultResponsiblePersonId, IsActive, CreatedAt, UpdatedAt) VALUES
('FACTORY_C', 'C工厂', 0, '3', 'C工厂主厂区', 10, 11, 1, NOW(), NOW());

-- 获取工厂ID
SET @factory_c_id = LAST_INSERT_ID();

-- 产线层级
INSERT INTO locations (Code, Name, Type, ParentId, Path, Description, DefaultDepartmentId, DefaultResponsiblePersonId, IsActive, CreatedAt, UpdatedAt) VALUES
('C_LINE_1', 'C1产线', 1, @factory_c_id, '3-1', 'C工厂1号产线', 10, 11, 1, NOW(), NOW()),
('C_LINE_2', 'C2产线', 1, @factory_c_id, '3-2', 'C工厂2号产线', 11, 12, 1, NOW(), NOW());

-- 获取产线ID
SET @c_line_1_id = LAST_INSERT_ID();
SET @c_line_2_id = @c_line_1_id + 1;

-- 工序层级
INSERT INTO locations (Code, Name, Type, ParentId, Path, Description, DefaultDepartmentId, DefaultResponsiblePersonId, IsActive, CreatedAt, UpdatedAt) VALUES
-- C1产线工序
('C1_PROC_1', 'C1-1工序', 1, @c_line_1_id, '3-1-1', 'C1产线1号工序', 10, 11, 1, NOW(), NOW()),
('C1_PROC_2', 'C1-2工序', 1, @c_line_1_id, '3-1-2', 'C1产线2号工序', 12, 13, 1, NOW(), NOW()),
-- C2产线工序
('C2_PROC_1', 'C2-1工序', 1, @c_line_2_id, '3-2-1', 'C2产线1号工序', 13, 14, 1, NOW(), NOW());

-- 获取工序ID
SET @c1_proc_1_id = LAST_INSERT_ID();
SET @c1_proc_2_id = @c1_proc_1_id + 1;
SET @c2_proc_1_id = @c1_proc_1_id + 2;

-- 工位层级
INSERT INTO locations (Code, Name, Type, ParentId, Path, Description, DefaultDepartmentId, DefaultResponsiblePersonId, IsActive, CreatedAt, UpdatedAt) VALUES
-- C1-1工序工位
('C1_PROC1_WS1', 'C1-1-1工位', 2, @c1_proc_1_id, '3-1-1-1', 'C1产线1工序1号工位', 10, 11, 1, NOW(), NOW()),
('C1_PROC1_WS2', 'C1-1-2工位', 2, @c1_proc_1_id, '3-1-1-2', 'C1产线1工序2号工位', 10, 11, 1, NOW(), NOW()),
-- C1-2工序工位
('C1_PROC2_WS1', 'C1-2-1工位', 2, @c1_proc_2_id, '3-1-2-1', 'C1产线2工序1号工位', 12, 13, 1, NOW(), NOW()),
-- C2-1工序工位
('C2_PROC1_WS1', 'C2-1-1工位', 2, @c2_proc_1_id, '3-2-1-1', 'C2产线1工序1号工位', 13, 14, 1, NOW(), NOW());

-- 获取工位ID
SET @c1_proc1_ws1_id = LAST_INSERT_ID();
SET @c1_proc1_ws2_id = @c1_proc1_ws1_id + 1;
SET @c1_proc2_ws1_id = @c1_proc1_ws1_id + 2;
SET @c2_proc1_ws1_id = @c1_proc1_ws1_id + 3;

-- 位置层级 (实际操作/使用点)
INSERT INTO locations (Code, Name, Type, ParentId, Path, Description, DefaultDepartmentId, DefaultResponsiblePersonId, IsActive, CreatedAt, UpdatedAt) VALUES
-- C1-1-1工位位置
('C1_P1_W1_L1', 'C1-1-1-1号位置', 2, @c1_proc1_ws1_id, '3-1-1-1-1', 'C1产线1工序1工位1号位置', 10, 11, 1, NOW(), NOW()),
('C1_P1_W1_L2', 'C1-1-1-2号位置', 2, @c1_proc1_ws1_id, '3-1-1-1-2', 'C1产线1工序1工位2号位置', 10, 11, 1, NOW(), NOW()),
-- C1-1-2工位位置
('C1_P1_W2_L1', 'C1-1-2-1号位置', 2, @c1_proc1_ws2_id, '3-1-1-2-1', 'C1产线1工序2工位1号位置', 11, 12, 1, NOW(), NOW()),
-- C1-2-1工位位置
('C1_P2_W1_L1', 'C1-2-1-1号位置', 2, @c1_proc2_ws1_id, '3-1-2-1-1', 'C1产线2工序1工位1号位置', 12, 13, 1, NOW(), NOW()),
-- C2-1-1工位位置
('C2_P1_W1_L1', 'C2-1-1-1号位置', 2, @c2_proc1_ws1_id, '3-2-1-1-1', 'C2产线1工序1工位1号位置', 13, 14, 1, NOW(), NOW());

-- 获取位置ID
SET @c1_p1_w1_l1_id = LAST_INSERT_ID();
SET @c1_p1_w1_l2_id = @c1_p1_w1_l1_id + 1;
SET @c1_p1_w2_l1_id = @c1_p1_w1_l1_id + 2;
SET @c1_p2_w1_l1_id = @c1_p1_w1_l1_id + 3;
SET @c2_p1_w1_l1_id = @c1_p1_w1_l1_id + 4;

-- 8. 创建位置-用户关联关系
INSERT INTO location_users (LocationId, UserId, UserType, IsActive, CreatedAt, UpdatedAt) VALUES
-- C1-1-1-1号位置 (D1部门) - 使用人U01，负责人M01
(@c1_p1_w1_l1_id, 17, 0, 1, NOW(), NOW()),
(@c1_p1_w1_l1_id, 11, 1, 1, NOW(), NOW()),

-- C1-1-1-2号位置 (D1部门) - 使用人U02，负责人M01
(@c1_p1_w1_l2_id, 18, 0, 1, NOW(), NOW()),
(@c1_p1_w1_l2_id, 11, 1, 1, NOW(), NOW()),

-- C1-1-2-1号位置 (D2部门) - 使用人U03，负责人M02
(@c1_p1_w2_l1_id, 19, 0, 1, NOW(), NOW()),
(@c1_p1_w2_l1_id, 12, 1, 1, NOW(), NOW()),

-- C1-2-1-1号位置 (D3部门) - 使用人U04，负责人M03
(@c1_p2_w1_l1_id, 20, 0, 1, NOW(), NOW()),
(@c1_p2_w1_l1_id, 13, 1, 1, NOW(), NOW()),

-- C2-1-1-1号位置 (D4部门) - 使用人U05，负责人M04
(@c2_p1_w1_l1_id, 21, 0, 1, NOW(), NOW()),
(@c2_p1_w1_l1_id, 14, 1, 1, NOW(), NOW());

-- 同时填充locationusers表
INSERT INTO locationusers (LocationId, UserId, UserType, IsActive, CreatedAt, UpdatedAt) VALUES
-- C1-1-1-1号位置
(@c1_p1_w1_l1_id, 17, 0, 1, NOW(), NOW()),
(@c1_p1_w1_l1_id, 11, 1, 1, NOW(), NOW()),

-- C1-1-1-2号位置
(@c1_p1_w1_l2_id, 18, 0, 1, NOW(), NOW()),
(@c1_p1_w1_l2_id, 11, 1, 1, NOW(), NOW()),

-- C1-1-2-1号位置
(@c1_p1_w2_l1_id, 19, 0, 1, NOW(), NOW()),
(@c1_p1_w2_l1_id, 12, 1, 1, NOW(), NOW()),

-- C1-2-1-1号位置
(@c1_p2_w1_l1_id, 20, 0, 1, NOW(), NOW()),
(@c1_p2_w1_l1_id, 13, 1, 1, NOW(), NOW()),

-- C2-1-1-1号位置
(@c2_p1_w1_l1_id, 21, 0, 1, NOW(), NOW()),
(@c2_p1_w1_l1_id, 14, 1, 1, NOW(), NOW());

-- 9. 创建资产类型 (assettypes)
INSERT INTO assettypes (Name, Code, Description, ParentId, RequireSerialNumber, SortOrder, IsActive, CreatedAt, UpdatedAt) VALUES
-- 主设备类型
('电脑设备', 'COMPUTER', '计算机类设备', NULL, 1, 10, 1, NOW(), NOW()),
('网络设备', 'NETWORK', '网络通信设备', NULL, 1, 20, 1, NOW(), NOW()),
('打印设备', 'PRINTER', '打印类设备', NULL, 1, 30, 1, NOW(), NOW()),
('扫描设备', 'SCANNER', '扫描类设备', NULL, 1, 40, 1, NOW(), NOW()),
('移动设备', 'MOBILE', '移动终端设备', NULL, 1, 50, 1, NOW(), NOW()),
('存储设备', 'STORAGE', '数据存储设备', NULL, 1, 60, 1, NOW(), NOW());

-- 获取主类型ID
SET @computer_id = LAST_INSERT_ID();
SET @network_id = @computer_id + 1;
SET @printer_id = @computer_id + 2;
SET @scanner_id = @computer_id + 3;
SET @mobile_id = @computer_id + 4;
SET @storage_id = @computer_id + 5;

-- 电脑设备子类型
INSERT INTO assettypes (Name, Code, Description, ParentId, RequireSerialNumber, SortOrder, IsActive, CreatedAt, UpdatedAt) VALUES
('工控机', 'IPC', '工业控制计算机', @computer_id, 1, 11, 1, NOW(), NOW()),
('台式电脑', 'DESKTOP', '普通台式电脑', @computer_id, 1, 12, 1, NOW(), NOW()),
('笔记本电脑', 'LAPTOP', '笔记本电脑', @computer_id, 1, 13, 1, NOW(), NOW()),
('服务器', 'SERVER', '服务器设备', @computer_id, 1, 14, 1, NOW(), NOW()),

-- 网络设备子类型
('交换机', 'SWITCH', '网络交换机', @network_id, 1, 21, 1, NOW(), NOW()),
('路由器', 'ROUTER', '网络路由器', @network_id, 1, 22, 1, NOW(), NOW()),
('防火墙', 'FIREWALL', '网络防火墙', @network_id, 1, 23, 1, NOW(), NOW()),

-- 打印设备子类型
('激光打印机', 'LASER_PRINTER', '激光打印机', @printer_id, 1, 31, 1, NOW(), NOW()),
('标签打印机', 'LABEL_PRINTER', '标签打印机', @printer_id, 1, 32, 1, NOW(), NOW()),
('蓝牙打印机', 'BT_PRINTER', '蓝牙打印机', @printer_id, 1, 33, 1, NOW(), NOW()),

-- 扫描设备子类型
('条码扫描枪', 'BARCODE_SCANNER', '条码扫描枪', @scanner_id, 1, 41, 1, NOW(), NOW()),
('二维码扫描器', 'QR_SCANNER', '二维码扫描器', @scanner_id, 1, 42, 1, NOW(), NOW()),

-- 移动设备子类型
('PDA', 'PDA', '掌上数据终端', @mobile_id, 1, 51, 1, NOW(), NOW()),
('平板电脑', 'TABLET', '平板电脑', @mobile_id, 1, 52, 1, NOW(), NOW()),
('工业手机', 'RUGGED_PHONE', '工业防护手机', @mobile_id, 1, 53, 1, NOW(), NOW());

-- 保存资产类型ID供后续使用
SET @ipc_id = LAST_INSERT_ID();
SET @desktop_id = @ipc_id + 1;
SET @laptop_id = @ipc_id + 2;
SET @server_id = @ipc_id + 3;
SET @switch_id = @ipc_id + 4;
SET @router_id = @ipc_id + 5;
SET @firewall_id = @ipc_id + 6;
SET @laser_printer_id = @ipc_id + 7;
SET @label_printer_id = @ipc_id + 8;
SET @bt_printer_id = @ipc_id + 9;
SET @barcode_scanner_id = @ipc_id + 10;
SET @qr_scanner_id = @ipc_id + 11;
SET @pda_id = @ipc_id + 12;
SET @tablet_id = @ipc_id + 13;
SET @rugged_phone_id = @ipc_id + 14;

-- 更多测试数据将在后续更新中添加

-- 10. 创建供应商数据 (suppliers)
INSERT INTO suppliers (Name, Code, ContactPerson, ContactPhone, ContactEmail, Address, Type, Notes, IsActive, CreatedAt, UpdatedAt) VALUES
('联想集团', 'LENOVO', '王经理', '13566778899', '<EMAIL>', '北京市海淀区联想大厦', 0, '电脑设备主要供应商', 1, NOW(), NOW()),
('戴尔科技', 'DELL', '李经理', '13677889900', '<EMAIL>', '上海市浦东新区张江高科技园区', 0, '服务器主要供应商', 1, NOW(), NOW()),
('华为技术', 'HUAWEI', '张经理', '13788990011', '<EMAIL>', '深圳市龙岗区华为基地', 0, '网络设备主要供应商', 1, NOW(), NOW()),
('西门子工业', 'SIEMENS', '赵经理', '13899001122', '<EMAIL>', '北京市朝阳区西门子大厦', 0, '工控机主要供应商', 1, NOW(), NOW()),
('霍尼韦尔', 'HONEYWELL', '钱经理', '13900112233', '<EMAIL>', '上海市长宁区霍尼韦尔大厦', 0, '扫描设备主要供应商', 1, NOW(), NOW()),
('佳能', 'CANON', '孙经理', '13911223344', '<EMAIL>', '北京市朝阳区佳能大厦', 0, '打印设备主要供应商', 1, NOW(), NOW()),
('微软', 'MICROSOFT', '周经理', '13922334455', '<EMAIL>', '北京市海淀区微软大厦', 1, '软件主要供应商', 1, NOW(), NOW()),
('东方通信', 'EASTCOM', '吴经理', '13933445566', '<EMAIL>', '杭州市滨江区东方通信大厦', 0, '通信设备供应商', 1, NOW(), NOW()),
('普联技术', 'TP-LINK', '郑经理', '13944556677', '<EMAIL>', '深圳市南山区普联科技大厦', 0, '网络设备供应商', 1, NOW(), NOW()),
('科大讯飞', 'IFLYTEK', '冯经理', '13955667788', '<EMAIL>', '合肥市高新区科大讯飞大厦', 1, '语音识别技术供应商', 1, NOW(), NOW());

-- 11. 创建故障类型数据 (faulttypes)
INSERT INTO faulttypes (Name, Code, Description, Severity, SuggestedResponseTime, SuggestedResolutionTime, SortOrder, IsActive, CreatedAt, UpdatedAt) VALUES
('硬件损坏', 'HW_DAMAGE', '设备硬件部件物理损坏', 1, 4, 48, 10, 1, NOW(), NOW()),
('系统崩溃', 'SYS_CRASH', '操作系统或应用程序崩溃', 2, 2, 24, 20, 1, NOW(), NOW()),
('网络连接中断', 'NET_DISCONNECT', '网络连接无法建立或中断', 1, 2, 12, 30, 1, NOW(), NOW()),
('设备无法启动', 'BOOT_FAILURE', '设备无法正常启动或开机', 2, 2, 24, 40, 1, NOW(), NOW()),
('性能下降', 'PERF_DEGRADE', '设备性能显著降低', 0, 8, 48, 50, 1, NOW(), NOW()),
('设备过热', 'OVERHEAT', '设备温度异常升高', 1, 2, 12, 60, 1, NOW(), NOW()),
('电源故障', 'POWER_FAILURE', '电源供应故障或不稳定', 2, 2, 24, 70, 1, NOW(), NOW()),
('显示异常', 'DISPLAY_ERROR', '显示器或屏幕显示异常', 0, 4, 24, 80, 1, NOW(), NOW()),
('噪音异常', 'NOISE_ABNORMAL', '设备运行噪音异常', 0, 8, 48, 90, 1, NOW(), NOW()),
('软件异常', 'SW_ERROR', '软件运行异常或错误', 1, 4, 24, 100, 1, NOW(), NOW()),
('端口故障', 'PORT_FAILURE', '通信端口或接口故障', 1, 4, 24, 110, 1, NOW(), NOW()),
('打印质量问题', 'PRINT_QUALITY', '打印设备输出质量问题', 0, 8, 48, 120, 1, NOW(), NOW()),
('扫描故障', 'SCAN_FAILURE', '扫描设备无法正常工作', 1, 4, 24, 130, 1, NOW(), NOW()),
('电池故障', 'BATTERY_ISSUE', '电池无法充电或续航时间短', 0, 8, 48, 140, 1, NOW(), NOW()),
('存储故障', 'STORAGE_FAILURE', '存储设备读写错误或无法识别', 1, 4, 24, 150, 1, NOW(), NOW());

-- 12. 创建资产数据 (assets)
-- 工控机资产
INSERT INTO assets (AssetCode, Name, AssetTypeId, SerialNumber, Model, Brand, PurchaseDate, WarrantyExpireDate, Price, LocationId, Status, Notes, CreatedAt, UpdatedAt) VALUES
('IPC2023001', 'C1产线工控机01', @ipc_id, 'SN2023IPC001', 'IPC-610H', '研华', '2023-01-10', '2026-01-09', 12800.00, @c1_p1_w1_l1_id, 1, '用于C1产线生产控制', NOW(), NOW()),
('IPC2023002', 'C1产线工控机02', @ipc_id, 'SN2023IPC002', 'IPC-610H', '研华', '2023-01-10', '2026-01-09', 12800.00, @c1_p1_w1_l2_id, 1, '用于C1产线数据采集', NOW(), NOW()),
('IPC2023003', 'C2产线工控机01', @ipc_id, 'SN2023IPC003', 'IPC-610H', '研华', '2023-01-15', '2026-01-14', 12800.00, @c1_p1_w2_l1_id, 1, '用于C2产线生产控制', NOW(), NOW()),
('IPC2023004', 'C1工序控制机01', @ipc_id, 'SN2023IPC004', 'IPC-610H', '研华', '2023-01-20', '2026-01-19', 12800.00, @c1_p2_w1_l1_id, 1, '用于C1工序质量控制', NOW(), NOW()),
('IPC2023005', 'C2工序控制机01', @ipc_id, 'SN2023IPC005', 'IPC-610H', '研华', '2023-01-25', '2026-01-24', 12800.00, @c2_p1_w1_l1_id, 1, '用于C2工序质量控制', NOW(), NOW()),

-- 台式电脑资产
('PC2023001', 'IT部门台式机01', @desktop_id, 'SN2023PC001', 'ThinkCentre M720q', '联想', '2023-02-10', '2026-02-09', 5800.00, @a_warehouse_id, 1, 'IT部门日常办公使用', NOW(), NOW()),
('PC2023002', 'IT部门台式机02', @desktop_id, 'SN2023PC002', 'ThinkCentre M720q', '联想', '2023-02-10', '2026-02-09', 5800.00, @a_warehouse_id, 1, 'IT部门开发使用', NOW(), NOW()),
('PC2023003', '行政部台式机01', @desktop_id, 'SN2023PC003', 'OptiPlex 7080', '戴尔', '2023-02-15', '2026-02-14', 6200.00, @a_warehouse_id, 1, '行政部日常办公使用', NOW(), NOW()),
('PC2023004', '财务部台式机01', @desktop_id, 'SN2023PC004', 'OptiPlex 7080', '戴尔', '2023-02-15', '2026-02-14', 6200.00, @b_warehouse_id, 1, '财务部日常办公使用', NOW(), NOW()),
('PC2023005', '生产部台式机01', @desktop_id, 'SN2023PC005', 'ThinkCentre M720q', '联想', '2023-02-20', '2026-02-19', 5800.00, @b_warehouse_id, 1, '生产部日常办公使用', NOW(), NOW()),

-- 笔记本电脑资产
('LP2023001', 'IT主管笔记本01', @laptop_id, 'SN2023LP001', 'ThinkPad X1 Carbon', '联想', '2023-03-05', '2026-03-04', 12500.00, @a_warehouse_id, 1, 'IT主管移动办公使用', NOW(), NOW()),
('LP2023002', '销售经理笔记本01', @laptop_id, 'SN2023LP002', 'XPS 13', '戴尔', '2023-03-10', '2026-03-09', 13200.00, @a_warehouse_id, 1, '销售经理客户拜访使用', NOW(), NOW()),
('LP2023003', '总经理笔记本01', @laptop_id, 'SN2023LP003', 'ThinkPad X1 Carbon', '联想', '2023-03-15', '2026-03-14', 14500.00, @b_warehouse_id, 1, '总经理移动办公使用', NOW(), NOW()),

-- 打印设备资产
('PR2023001', '行政部激光打印机01', @laser_printer_id, 'SN2023PR001', 'LaserJet Pro M404dn', '惠普', '2023-04-10', '2026-04-09', 2800.00, @a_warehouse_id, 1, '行政部文档打印使用', NOW(), NOW()),
('PR2023002', 'C1产线标签打印机01', @label_printer_id, 'SN2023PR002', 'QL-820NWB', '兄弟', '2023-04-15', '2026-04-14', 1800.00, @c1_p1_w1_l1_id, 1, 'C1产线标签打印使用', NOW(), NOW()),
('PR2023003', 'C2产线标签打印机01', @label_printer_id, 'SN2023PR003', 'QL-820NWB', '兄弟', '2023-04-20', '2026-04-19', 1800.00, @c2_p1_w1_l1_id, 1, 'C2产线标签打印使用', NOW(), NOW()),

-- 扫描设备资产
('SC2023001', 'C1产线条码扫描枪01', @barcode_scanner_id, 'SN2023SC001', 'DS4308', '讯宝', '2023-05-05', '2026-05-04', 1200.00, @c1_p1_w1_l1_id, 1, 'C1产线物料扫描使用', NOW(), NOW()),
('SC2023002', 'C1产线条码扫描枪02', @barcode_scanner_id, 'SN2023SC002', 'DS4308', '讯宝', '2023-05-05', '2026-05-04', 1200.00, @c1_p1_w1_l2_id, 1, 'C1产线物料扫描使用', NOW(), NOW()),
('SC2023003', 'C2产线条码扫描枪01', @barcode_scanner_id, 'SN2023SC003', 'DS4308', '讯宝', '2023-05-10', '2026-05-09', 1200.00, @c2_p1_w1_l1_id, 1, 'C2产线物料扫描使用', NOW(), NOW()),
('SC2023004', '仓库二维码扫描器01', @qr_scanner_id, 'SN2023SC004', 'DS9908', '讯宝', '2023-05-15', '2026-05-14', 1800.00, @a_warehouse_id, 1, '仓库物料盘点使用', NOW(), NOW()),

-- 移动设备资产
('MB2023001', 'C1产线PDA01', @pda_id, 'SN2023MB001', 'TC520K', '讯宝', '2023-06-05', '2026-06-04', 6800.00, @c1_p1_w1_l1_id, 1, 'C1产线移动数据采集使用', NOW(), NOW()),
('MB2023002', 'C1产线PDA02', @pda_id, 'SN2023MB002', 'TC520K', '讯宝', '2023-06-05', '2026-06-04', 6800.00, @c1_p1_w1_l2_id, 1, 'C1产线移动数据采集使用', NOW(), NOW()),
('MB2023003', 'C2产线PDA01', @pda_id, 'SN2023MB003', 'TC520K', '讯宝', '2023-06-10', '2026-06-09', 6800.00, @c2_p1_w1_l1_id, 1, 'C2产线移动数据采集使用', NOW(), NOW()),
('MB2023004', '质检部平板电脑01', @tablet_id, 'SN2023MB004', 'iPad Pro', '苹果', '2023-06-15', '2026-06-14', 7200.00, @c1_p2_w1_l1_id, 1, '质检部移动办公使用', NOW(), NOW()),
('MB2023005', '物流部工业手机01', @rugged_phone_id, 'SN2023MB005', 'Cat S62 Pro', '卡特彼勒', '2023-06-20', '2026-06-19', 5200.00, @c1_p1_w2_l1_id, 1, '物流部恶劣环境使用', NOW(), NOW()),

-- 网络设备资产
('NW2023001', 'C1产线交换机01', @switch_id, 'SN2023NW001', 'S5700-28C-EI', '华为', '2023-07-05', '2026-07-04', 4800.00, @c1_p1_w1_l1_id, 1, 'C1产线网络互联使用', NOW(), NOW()),
('NW2023002', 'C2产线交换机01', @switch_id, 'SN2023NW002', 'S5700-28C-EI', '华为', '2023-07-10', '2026-07-09', 4800.00, @c2_p1_w1_l1_id, 1, 'C2产线网络互联使用', NOW(), NOW()),
('NW2023003', '办公区路由器01', @router_id, 'SN2023NW003', 'AR2220E', '华为', '2023-07-15', '2026-07-14', 8500.00, @a_warehouse_id, 1, '办公区网络路由使用', NOW(), NOW()),
('NW2023004', '工厂防火墙01', @firewall_id, 'SN2023NW004', 'USG6630E', '华为', '2023-07-20', '2026-07-19', 25000.00, @b_warehouse_id, 1, '工厂网络安全防护使用', NOW(), NOW());

-- 提交事务
COMMIT;

-- 执行完成
SELECT 'IT资产管理系统数据库初始化完成' AS 'Result'; 