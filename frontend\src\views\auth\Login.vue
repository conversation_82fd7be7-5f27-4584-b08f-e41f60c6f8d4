/**
 * 系统登录页面
 * 文件路径: src/views/auth/Login.vue
 * 功能描述: 系统登录界面，提供用户认证入口
 */

<template>
  <div class="login-container">
    <!-- 左侧展示区 -->
    <div class="login-banner">
      <div class="banner-content">
        <div class="login-logo">
          <el-icon :size="80" color="#ffffff"><Menu /></el-icon>
        </div>
        <h1 class="system-name">{{ systemConfig.name }}</h1>
        <p class="system-desc">{{ systemConfig.description }}</p>
      </div>
    </div>
    
    <!-- 右侧登录表单区 -->
    <div class="login-form-container">
      <div class="login-form-wrapper">
        <h2 class="login-title">系统登录</h2>
        
        <el-form 
          ref="loginFormRef"
          :model="loginModel"
          :rules="loginRules"
          class="login-form"
        >
          <el-form-item prop="username">
            <el-input 
              v-model="loginModel.username"
              placeholder="请输入用户名"
              prefix-icon="User"
              clearable
              @keyup.enter="handleLogin"
            />
          </el-form-item>
          
          <el-form-item prop="password">
            <el-input 
              v-model="loginModel.password"
              type="password"
              placeholder="请输入密码"
              prefix-icon="Lock" 
              show-password
              clearable
              @keyup.enter="handleLogin"
            />
          </el-form-item>
          
          <el-form-item>
            <el-checkbox v-model="loginModel.remember">记住我</el-checkbox>
            <el-link type="primary" class="forget-password" :underline="false">忘记密码？</el-link>
          </el-form-item>
          
          <el-form-item>
            <el-button 
              type="primary" 
              class="login-button" 
              :loading="loading"
              @click="handleLogin"
            >
              登录
            </el-button>
          </el-form-item>
          
          <!-- 测试按钮 -->
          <el-form-item>
            <el-button 
              type="success" 
              class="test-button" 
              @click="gotoTestPage"
            >
              测试页面跳转
            </el-button>
          </el-form-item>
        </el-form>
        
        <div class="login-footer">
          <p class="copyright">© {{ currentYear }} {{ systemConfig.name }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { User, Lock, Menu } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/stores'
import systemConfig from '@/config/system'

// 获取当前年份
const currentYear = computed(() => new Date().getFullYear())

// 路由和状态
const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

// 登录表单引用
const loginFormRef = ref(null)
const loading = ref(false)

// 登录表单数据
const loginModel = reactive({
  username: '',
  password: '',
  remember: false
})

// 表单验证规则
const loginRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度为3-20个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 30, message: '密码长度为6-30个字符', trigger: 'blur' }
  ]
}

// 处理登录请求
const handleLogin = async () => {
  if (!loginFormRef.value) return
  
  // 调试信息
  console.log('登录按钮点击 - 开始登录流程')
  
  try {
    // 表单验证
    await loginFormRef.value.validate((valid) => {
      if (!valid) {
        ElMessage.warning('请正确填写用户名和密码')
        return false
      }
    })
    
    // 显示加载状态
    loading.value = true
    console.log('准备发送登录请求，数据:', {
      username: loginModel.username,
      password: '******' // 不显示实际密码
    })
    
    // 直接发送登录请求到后端，让后端处理所有验证
    try {
      const loginData = {
        username: loginModel.username,
        password: loginModel.password
      }
      
      console.log('调用userStore.login发送登录请求')
      const response = await userStore.login(loginData)
      
      console.log('登录API响应:', {
        success: response?.success,
        message: response?.message,
        hasToken: !!response?.data?.token,
        hasUser: !!response?.data?.user
      })
      
      if (response && response.success) {
        ElMessage.success('登录成功，欢迎回来！')
        // 处理跳转
        handleLoginSuccess()
      } else {
        console.error('登录失败:', response)
        ElMessage.error(response?.message || '登录失败，请检查用户名和密码')
      }
    } catch (apiError) {
      console.error('API调用异常:', apiError)
      
      // 如果是网络错误或404错误，尝试模拟登录
      if (apiError?.message?.includes('404') || apiError?.message?.includes('network error')) {
        ElMessage.warning('后端API不可用，尝试模拟登录...')
        
        // 启用mock
        systemConfig.useMock = true
        
        try {
          const mockResponse = await userStore.login({
            username: loginModel.username,
            password: loginModel.password
          })
          
          if (mockResponse && mockResponse.success) {
            ElMessage.success('模拟登录成功，欢迎回来！')
            handleLoginSuccess()
            return
          } else {
            ElMessage.error(mockResponse?.message || '模拟登录失败')
          }
        } catch (mockError) {
          console.error('模拟登录失败:', mockError)
          ElMessage.error('登录失败: ' + (mockError?.message || '未知错误'))
        }
      } else {
        ElMessage.error('登录失败: ' + (apiError?.message || '未知错误'))
      }
    }
  } catch (error) {
    console.error('登录过程出错:', error)
    ElMessage.error('登录失败: ' + (error?.message || '未知错误'))
  } finally {
    // 关闭加载状态
    loading.value = false
    console.log('登录流程结束')
  }
}

// 跳转到测试页面
const gotoTestPage = () => {
  router.push('/test')
}

// 处理登录成功后的逻辑
const handleLoginSuccess = () => {
  // 获取重定向路径或默认导航到主应用仪表盘页面
  const redirect = route.query.redirect || '/main/dashboard'
  
  // 确保异步操作完成后再跳转
  setTimeout(() => {
    // 检查token存储情况
    const token = userStore.token
    
    if (token) {
      router.push(redirect)
        .catch(() => {
          // 如果跳转失败，尝试跳转到首页
          router.push('/')
        })
    } else {
      ElMessage.error('登录状态异常，请重新登录')
    }
  }, 100)
}
</script>

<style lang="scss" scoped>
@use "@/styles/variables.scss" as *;

.login-container {
  display: flex;
  height: 100vh;
  width: 100%;
  
  .login-banner {
    flex: 1;
    background-color: #1e88e5;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    
    .banner-content {
      text-align: center;
    }
    
    .login-logo {
      margin-bottom: 20px;
    }
    
    .system-name {
      font-size: 2.5rem;
      margin-bottom: 20px;
    }
    
    .system-desc {
      font-size: 1.2rem;
      max-width: 80%;
      margin: 0 auto;
    }
  }
  
  .login-form-container {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f5f5f5;
    
    .login-form-wrapper {
      width: 80%;
      max-width: 400px;
      padding: 40px;
      background-color: #fff;
      border-radius: 8px;
      box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
    }
    
    .login-title {
      text-align: center;
      margin-bottom: 30px;
      font-weight: 500;
      color: #303133;
    }
    
    .login-form {
      .el-form-item {
        margin-bottom: 25px;
      }
      
      .login-button {
        width: 100%;
        padding: 12px 0;
        font-size: 16px;
      }
      
      .test-button {
        width: 100%;
        margin-top: -10px;
      }
      
      .forget-password {
        float: right;
      }
    }
    
    .login-footer {
      margin-top: 30px;
      text-align: center;
      color: #909399;
      font-size: 14px;
    }
  }
}

/* 响应式布局 */
@media (max-width: 768px) {
  .login-container {
    flex-direction: column;
    
    .login-banner {
      height: 30vh;
      
      .system-name {
        font-size: 2rem;
      }
      
      .system-desc {
        font-size: 1rem;
      }
    }
    
    .login-form-container {
      height: 70vh;
      
      .login-form-wrapper {
        padding: 30px 20px;
      }
    }
  }
}
</style> 