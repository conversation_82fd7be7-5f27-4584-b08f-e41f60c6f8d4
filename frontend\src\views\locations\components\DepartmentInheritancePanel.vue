<!-- File: frontend/src/views/locations/components/DepartmentInheritancePanel.vue -->
<!-- Description: 位置部门继承信息面板组件 -->

<template>
  <div class="department-inheritance-panel">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>部门继承信息</span>
          <el-button type="text" @click="refreshData" :loading="loading">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </template>

      <!-- 当前位置的部门继承信息 -->
      <div v-if="currentLocationInfo" class="location-info">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="位置路径">
            {{ currentLocationInfo.locationPath }}
          </el-descriptions-item>
          <el-descriptions-item label="直接分配部门">
            <el-tag v-if="currentLocationInfo.directDepartmentName" type="primary">
              {{ currentLocationInfo.directDepartmentName }}
            </el-tag>
            <span v-else class="text-muted">未直接分配</span>
          </el-descriptions-item>
          <el-descriptions-item label="继承部门">
            <el-tag v-if="currentLocationInfo.inheritedDepartmentName" type="success">
              {{ currentLocationInfo.inheritedDepartmentName }}
            </el-tag>
            <span v-else class="text-muted">无继承部门</span>
          </el-descriptions-item>
          <el-descriptions-item label="有效部门">
            <el-tag v-if="currentLocationInfo.effectiveDepartmentName" type="warning">
              {{ currentLocationInfo.effectiveDepartmentName }}
            </el-tag>
            <span v-else class="text-muted">无有效部门</span>
          </el-descriptions-item>
          <el-descriptions-item label="资产数量">
            <el-statistic :value="currentLocationInfo.assetCount" />
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 资产列表 -->
      <div v-if="currentLocationInfo && currentLocationInfo.assets.length > 0" class="assets-section">
        <el-divider content-position="left">该位置的资产</el-divider>
        <el-table :data="currentLocationInfo.assets" size="small">
          <el-table-column prop="assetCode" label="资产编码" width="120" />
          <el-table-column prop="assetName" label="资产名称" />
          <el-table-column prop="assetTypeName" label="资产类型" width="120" />
          <el-table-column label="操作" width="100">
            <template #default="{ row }">
              <el-button type="text" size="small" @click="viewAssetDetail(row.assetId)">
                查看
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 部门统计信息 -->
      <div class="department-stats-section">
        <el-divider content-position="left">部门位置统计</el-divider>
        <el-table :data="departmentStats" size="small" v-loading="statsLoading">
          <el-table-column prop="departmentName" label="部门名称" />
          <el-table-column prop="directLocationCount" label="直接位置" width="100" align="center" />
          <el-table-column prop="inheritedLocationCount" label="继承位置" width="100" align="center" />
          <el-table-column prop="totalLocationCount" label="总位置" width="100" align="center" />
          <el-table-column prop="directAssetCount" label="直接资产" width="100" align="center" />
          <el-table-column prop="inheritedAssetCount" label="继承资产" width="100" align="center" />
          <el-table-column prop="totalAssetCount" label="总资产" width="100" align="center" />
          <el-table-column label="操作" width="120">
            <template #default="{ row }">
              <el-button type="text" size="small" @click="viewDepartmentLocations(row.departmentId)">
                查看位置
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh } from '@element-plus/icons-vue'
import locationDepartmentInheritanceApi from '@/api/locationDepartmentInheritance'

// Props
const props = defineProps({
  locationId: {
    type: Number,
    default: null
  }
})

// Emits
const emit = defineEmits(['assetClick', 'departmentClick'])

// 响应式数据
const loading = ref(false)
const statsLoading = ref(false)
const currentLocationInfo = ref(null)
const departmentStats = ref([])

// 监听位置ID变化
watch(() => props.locationId, (newLocationId) => {
  if (newLocationId) {
    fetchLocationInfo(newLocationId)
  } else {
    currentLocationInfo.value = null
  }
}, { immediate: true })

// 获取位置部门继承信息
const fetchLocationInfo = async (locationId) => {
  if (!locationId) return
  
  loading.value = true
  try {
    const response = await locationDepartmentInheritanceApi.getLocationDepartmentInheritanceById(locationId)
    if (response.success) {
      currentLocationInfo.value = response.data
    } else {
      ElMessage.error(response.message || '获取位置信息失败')
    }
  } catch (error) {
    console.error('获取位置信息错误:', error)
    ElMessage.error('获取位置信息失败')
  } finally {
    loading.value = false
  }
}

// 获取部门统计信息
const fetchDepartmentStats = async () => {
  statsLoading.value = true
  try {
    const response = await locationDepartmentInheritanceApi.getDepartmentLocationStats()
    if (response.success) {
      departmentStats.value = response.data
    } else {
      ElMessage.error(response.message || '获取部门统计失败')
    }
  } catch (error) {
    console.error('获取部门统计错误:', error)
    ElMessage.error('获取部门统计失败')
  } finally {
    statsLoading.value = false
  }
}

// 刷新数据
const refreshData = () => {
  if (props.locationId) {
    fetchLocationInfo(props.locationId)
  }
  fetchDepartmentStats()
}

// 查看资产详情
const viewAssetDetail = (assetId) => {
  emit('assetClick', assetId)
  // 这里可以跳转到资产详情页
  // this.$router.push({ name: 'AssetDetail', params: { id: assetId } })
}

// 查看部门位置
const viewDepartmentLocations = async (departmentId) => {
  try {
    const response = await locationDepartmentInheritanceApi.getLocationsByDepartment(departmentId)
    if (response.success) {
      emit('departmentClick', {
        departmentId,
        locations: response.data
      })
      ElMessage.success(`该部门管理 ${response.data.length} 个位置`)
    }
  } catch (error) {
    console.error('获取部门位置错误:', error)
    ElMessage.error('获取部门位置失败')
  }
}

// 组件挂载时获取部门统计
onMounted(() => {
  fetchDepartmentStats()
})
</script>

<style lang="scss" scoped>
.department-inheritance-panel {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .location-info {
    margin-bottom: 20px;
  }

  .assets-section,
  .department-stats-section {
    margin-top: 20px;
  }

  .text-muted {
    color: #909399;
  }

  :deep(.el-descriptions__body) {
    background-color: #fafafa;
  }

  :deep(.el-table) {
    .el-button--text {
      padding: 0;
    }
  }
}
</style>