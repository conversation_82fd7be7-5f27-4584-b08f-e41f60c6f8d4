import{_ as a,c as e,r as t,j as s,k as l,o as i,w as r,b as u,$ as c,d,e as n,p as o,t as m,n as p,a as v,l as f,f as C,v as h,m as g,E as y,z as k,A as S,F as b,h as w,s as _,aG as D,aW as A}from"./index-CG5lHOPO.js";import{w as F}from"./workShift-Ce3ThpoM.js";import{T}from"./TaskClaimDialog-Dx9jUgxp.js";import"./gamification-Dm7mCEPf.js";import"./gamification-2FBMrBgR.js";const V={key:0,class:"user-tasks-content"},B={class:"user-info-section"},E={class:"user-header"},I={class:"user-details"},N={class:"stats-overview"},x={class:"stat-item"},z={class:"stat-number primary"},Y={class:"stat-item"},j={class:"stat-number success"},U={class:"stat-item"},M={class:"stat-number warning"},R={class:"stat-item"},$={class:"stat-number danger"},O={class:"tasks-section"},G={class:"section-header"},L={class:"task-name-cell"},P={class:"task-name"},W={key:0,class:"task-watermark"},q={key:2,class:"status-text"},H={class:"dialog-footer"},J=a({__name:"UserTasksDialog",props:{modelValue:{type:Boolean,default:!1},userStat:{type:Object,default:null}},emits:["update:modelValue"],setup(a,{emit:k}){const S=a,b=k,w=e({get:()=>S.modelValue,set:a=>b("update:modelValue",a)}),_=t(!1),D=t([]),A=["#FF6B6B","#4ECDC4","#45B7D1","#96CEB4","#FFEAA7","#DDA0DD","#98D8C8","#F7DC6F","#BB8FCE","#85C1E9","#F8C471","#82E0AA"],T=async()=>{if(S.userStat){_.value=!0;try{const a=await F.getUserTodayClaimsById(S.userStat.userId);a.success?D.value=a.data||[]:y.error("获取用户任务失败")}catch(a){y.error("获取用户任务失败")}finally{_.value=!1}}},J=async(a,e)=>{try{const t=await F.updateClaimStatus(a.claimId,{claimStatus:e,notes:`状态更新为: ${Q(e)}`});t.success?(y.success("状态更新成功"),T()):y.error(t.message||"状态更新失败")}catch(t){y.error("状态更新失败")}},K=a=>A[a%A.length],Q=a=>({Claimed:"已领取",Started:"进行中",Completed:"已完成",Cancelled:"已取消"}[a]||a),X=a=>a?new Date(a).toLocaleString("zh-CN",{month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"}):"",Z=()=>{w.value=!1};return s(w,(a=>{a&&S.userStat&&T()})),(e,t)=>{var s;const y=v("el-avatar"),k=v("el-button"),S=v("el-table-column"),b=v("el-tag"),A=v("el-table"),F=v("el-dialog"),aa=g("loading");return i(),l(F,{modelValue:w.value,"onUpdate:modelValue":t[0]||(t[0]=a=>w.value=a),title:`${(null==(s=a.userStat)?void 0:s.userName)||"用户"} - 今日任务详情`,width:"800px","before-close":Z},{footer:r((()=>[d("div",H,[n(k,{onClick:Z},{default:r((()=>t[9]||(t[9]=[o("关闭")]))),_:1})])])),default:r((()=>[a.userStat?(i(),u("div",V,[d("div",B,[d("div",E,[n(y,{size:48,style:p({backgroundColor:K(a.userStat.userId)})},{default:r((()=>[o(m(a.userStat.userName.charAt(0)),1)])),_:1},8,["style"]),d("div",I,[d("h3",null,m(a.userStat.userName),1),d("p",null,m(a.userStat.shiftName)+" | 完成率: "+m(a.userStat.completionRate.toFixed(1))+"%",1)])]),d("div",N,[d("div",x,[d("span",z,m(a.userStat.claimedTasksCount),1),t[1]||(t[1]=d("span",{class:"stat-label"},"已领取",-1))]),d("div",Y,[d("span",j,m(a.userStat.completedTasksCount),1),t[2]||(t[2]=d("span",{class:"stat-label"},"已完成",-1))]),d("div",U,[d("span",M,m(a.userStat.startedTasksCount),1),t[3]||(t[3]=d("span",{class:"stat-label"},"进行中",-1))]),d("div",R,[d("span",$,m(a.userStat.unclaimedTasksCount),1),t[4]||(t[4]=d("span",{class:"stat-label"},"未领取",-1))])])]),d("div",O,[d("div",G,[t[6]||(t[6]=d("h4",null,"任务详情",-1)),n(k,{size:"small",icon:C(h),onClick:T,loading:_.value},{default:r((()=>t[5]||(t[5]=[o(" 刷新 ")]))),_:1},8,["icon","loading"])]),f((i(),l(A,{data:D.value,stripe:"",style:{width:"100%"},"empty-text":_.value?"加载中...":"暂无任务数据"},{default:r((()=>[n(S,{prop:"taskName",label:"任务名称","min-width":"200"},{default:r((({row:e})=>[d("div",L,[d("span",P,m(e.taskName||"未知任务"),1),"Completed"===e.claimStatus?(i(),u("div",W,[d("span",{class:"watermark-text",style:p({color:K(a.userStat.userId)})}," ✓ "+m(a.userStat.userName),5)])):c("",!0)])])),_:1}),n(S,{prop:"claimStatus",label:"状态",width:"100"},{default:r((({row:a})=>{return[n(b,{type:(e=a.claimStatus,{Claimed:"primary",Started:"warning",Completed:"success",Cancelled:"danger"}[e]||"info"),size:"small"},{default:r((()=>[o(m(Q(a.claimStatus)),1)])),_:2},1032,["type"])];var e})),_:1}),n(S,{prop:"claimedAt",label:"领取时间",width:"150"},{default:r((({row:a})=>[o(m(X(a.claimedAt)),1)])),_:1}),n(S,{prop:"startedAt",label:"开始时间",width:"150"},{default:r((({row:a})=>[o(m(a.startedAt?X(a.startedAt):"-"),1)])),_:1}),n(S,{prop:"completedAt",label:"完成时间",width:"150"},{default:r((({row:a})=>[o(m(a.completedAt?X(a.completedAt):"-"),1)])),_:1}),n(S,{label:"操作",width:"120",fixed:"right"},{default:r((({row:a})=>["Claimed"===a.claimStatus?(i(),l(k,{key:0,type:"primary",size:"small",onClick:e=>J(a,"Started")},{default:r((()=>t[7]||(t[7]=[o(" 开始 ")]))),_:2},1032,["onClick"])):"Started"===a.claimStatus?(i(),l(k,{key:1,type:"success",size:"small",onClick:e=>J(a,"Completed")},{default:r((()=>t[8]||(t[8]=[o(" 完成 ")]))),_:2},1032,["onClick"])):(i(),u("span",q,m(Q(a.claimStatus)),1))])),_:1})])),_:1},8,["data","empty-text"])),[[aa,_.value]])])])):c("",!0)])),_:1},8,["modelValue","title"])}}},[["__scopeId","data-v-c29ec5ce"]]),K={class:"shift-statistics-container"},Q={class:"page-header"},X={class:"header-content"},Z={class:"title-section"},aa={class:"page-title"},ea={class:"action-section"},ta={key:0,class:"current-shift-card"},sa={class:"card-header"},la={class:"shift-info"},ia={class:"shift-detail"},ra={class:"shift-actions"},ua={class:"statistics-grid"},ca={class:"stat-header"},da={class:"user-info"},na={class:"user-details"},oa={class:"shift-name"},ma={class:"stat-content"},pa={class:"stat-row"},va={class:"stat-item"},fa={class:"stat-number primary"},Ca={class:"stat-item"},ha={class:"stat-number success"},ga={class:"stat-row"},ya={class:"stat-item"},ka={class:"stat-number warning"},Sa={class:"stat-item"},ba={class:"stat-number danger"},wa={class:"progress-section"},_a={key:1,class:"empty-state"},Da=a({__name:"ShiftStatisticsView",setup(a){const s=t(!1),l=t((new Date).toISOString().split("T")[0]),f=t([]),g=t(null),V=t(!1),B=t(!1),E=t(null),I=["#FF6B6B","#4ECDC4","#45B7D1","#96CEB4","#FFEAA7","#DDA0DD","#98D8C8","#F7DC6F","#BB8FCE","#85C1E9","#F8C471","#82E0AA"],N=e((()=>{if(!g.value)return!1;const a=(new Date).toTimeString().slice(0,5),e=g.value.taskClaimTime;return Math.abs(z(a)-z(e))<=30})),x=async()=>{s.value=!0;try{const[a,e]=await Promise.all([F.getTodayShiftStatistics({statisticsDate:l.value}),F.getUserCurrentShift()]);a.success&&(f.value=a.data||[]),e.success&&(g.value=e.data)}catch(a){y.error("加载统计数据失败")}finally{s.value=!1}},z=a=>{const[e,t]=a.split(":").map(Number);return 60*e+t},Y=()=>{V.value=!0},j=()=>{y.success("任务领取成功"),x()};let U=null;return k((()=>{x(),U=setInterval((()=>{x()}),3e4)})),S((()=>{U&&(clearInterval(U),U=null)})),(a,e)=>{const t=v("el-icon"),y=v("el-date-picker"),k=v("el-button"),S=v("el-card"),F=v("el-avatar"),z=v("el-tag"),U=v("el-progress"),M=v("el-empty");return i(),u("div",K,[d("div",Q,[d("div",X,[d("div",Z,[d("h1",aa,[n(t,null,{default:r((()=>[n(C(_))])),_:1}),e[3]||(e[3]=o(" 班次任务统计 "))]),e[4]||(e[4]=d("p",{class:"page-subtitle"},"实时监控各班次任务完成情况",-1))]),d("div",ea,[n(y,{modelValue:l.value,"onUpdate:modelValue":e[0]||(e[0]=a=>l.value=a),type:"date",placeholder:"选择日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",onChange:x,class:"date-picker"},null,8,["modelValue"]),n(k,{type:"primary",icon:C(h),onClick:x,loading:s.value},{default:r((()=>e[5]||(e[5]=[o(" 刷新 ")]))),_:1},8,["icon","loading"])])])]),g.value?(i(),u("div",ta,[n(S,{shadow:"hover"},{header:r((()=>[d("div",sa,[n(t,null,{default:r((()=>[n(C(A))])),_:1}),e[6]||(e[6]=d("span",null,"当前班次",-1))])])),default:r((()=>[d("div",la,[d("div",ia,[d("h3",null,m(g.value.shiftName),1),d("p",null,m(g.value.shiftCode)+" | "+m(g.value.startTime)+" - "+m(g.value.endTime),1),d("p",null,"任务领取时间: "+m(g.value.taskClaimTime),1)]),d("div",ra,[n(k,{type:"success",icon:C(D),onClick:Y,disabled:!N.value},{default:r((()=>e[7]||(e[7]=[o(" 领取任务 ")]))),_:1},8,["icon","disabled"])])])])),_:1})])):c("",!0),d("div",ua,[(i(!0),u(b,null,w(f.value,(a=>(i(),u("div",{key:`${a.userId}-${a.shiftId}`,class:"stat-card"},[n(S,{shadow:"hover",onClick:e=>(a=>{E.value=a,B.value=!0})(a)},{header:r((()=>{return[d("div",ca,[d("div",da,[n(F,{size:32,style:p({backgroundColor:(t=a.userId,I[t%I.length])})},{default:r((()=>[o(m(a.userName.charAt(0)),1)])),_:2},1032,["style"]),d("div",na,[d("h4",null,m(a.userName),1),d("span",oa,m(a.shiftName),1)])]),n(z,{type:(e=a.completionRate,e>=80?"success":e>=60?"warning":"danger"),size:"small"},{default:r((()=>[o(m(a.completionRate.toFixed(1))+"% ",1)])),_:2},1032,["type"])])];var e,t})),default:r((()=>{return[d("div",ma,[d("div",pa,[d("div",va,[d("span",fa,m(a.claimedTasksCount),1),e[8]||(e[8]=d("span",{class:"stat-label"},"已领取",-1))]),d("div",Ca,[d("span",ha,m(a.completedTasksCount),1),e[9]||(e[9]=d("span",{class:"stat-label"},"已完成",-1))])]),d("div",ga,[d("div",ya,[d("span",ka,m(a.startedTasksCount),1),e[10]||(e[10]=d("span",{class:"stat-label"},"进行中",-1))]),d("div",Sa,[d("span",ba,m(a.unclaimedTasksCount),1),e[11]||(e[11]=d("span",{class:"stat-label"},"未领取",-1))])]),d("div",wa,[n(U,{percentage:a.completionRate,color:(t=a.completionRate,t>=80?"#67C23A":t>=60?"#E6A23C":"#F56C6C"),"stroke-width":8,"show-text":!1},null,8,["percentage","color"])])])];var t})),_:2},1032,["onClick"])])))),128))]),s.value||0!==f.value.length?c("",!0):(i(),u("div",_a,[n(M,{description:"暂无班次统计数据"},{default:r((()=>[n(k,{type:"primary",onClick:x},{default:r((()=>e[12]||(e[12]=[o("重新加载")]))),_:1})])),_:1})])),n(T,{modelValue:V.value,"onUpdate:modelValue":e[1]||(e[1]=a=>V.value=a),onSuccess:j},null,8,["modelValue"]),n(J,{modelValue:B.value,"onUpdate:modelValue":e[2]||(e[2]=a=>B.value=a),"user-stat":E.value},null,8,["modelValue","user-stat"])])}}},[["__scopeId","data-v-5090a9b2"]]);export{Da as default};
