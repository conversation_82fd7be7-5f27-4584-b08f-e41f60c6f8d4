import request from '@/utils/request'

/**
 * 游戏化系统API
 */

/**
 * 获取当前用户游戏化统计信息
 */
export function getUserStats() {
  return request({
    url: '/v2/gamification-v2/stats/current',
    method: 'get'
  })
}

/**
 * 获取游戏化规则列表
 */
export function getGamificationRules() {
  return request({
    url: '/gamification/rules',
    method: 'get'
  })
}

/**
 * 添加游戏化规则
 */
export function addGamificationRule(rule) {
  return request({
    url: '/gamification/rules',
    method: 'post',
    data: rule
  })
}

/**
 * 更新游戏化规则
 */
export function updateGamificationRule(rule) {
  return request({
    url: '/gamification/rules',
    method: 'put',
    data: rule
  })
}

/**
 * 删除游戏化规则
 */
export function deleteGamificationRule(ruleId) {
  return request({
    url: `/gamification/rules/${ruleId}`,
    method: 'delete'
  })
}

/**
 * 启用/禁用游戏化规则
 */
export function setRuleActive(ruleId, isActive) {
  return request({
    url: `/gamification/rules/${ruleId}/active`,
    method: 'patch',
    params: { isActive }
  })
}

/**
 * 重新加载规则缓存
 */
export function reloadRules() {
  return request({
    url: '/gamification/rules/reload',
    method: 'post'
  })
}

/**
 * 获取指定用户游戏化统计信息
 * @param {number} userId - 用户ID
 */
export function getUserStatsById(userId) {
  return request({
    url: `/v2/gamification-v2/stats/${userId}`,
    method: 'get'
  })
}

/**
 * 获取用户每日任务统计
 * @param {string} date - 日期 (可选，格式: YYYY-MM-DD)
 */
export function getDailyTaskStats(date = null) {
  const params = {}
  if (date) {
    params.date = date
  }

  return request({
    url: '/v2/gamification-v2/daily-stats',
    method: 'get',
    params
  })
}

/**
 * 获取排行榜
 * @param {number} type - 排行榜类型 (1=Weekly, 2=Monthly, 3=AllTime)
 * @param {number} topN - 前N名 (默认10)
 */
export function getLeaderboard(type = 1, topN = 10) {
  return request({
    url: '/v2/gamification/leaderboard',
    method: 'get',
    params: {
      type,
      topN
    }
  })
}

/**
 * 获取多维度排行榜
 * @param {string} metric - 排行指标 (points, coins, diamonds, tasks_created, tasks_completed, etc.)
 * @param {string} period - 统计周期 (daily, weekly, monthly, yearly, alltime)
 * @param {number} limit - 返回数量 (默认20)
 */
export function getMultiDimensionLeaderboard(metric = 'points', period = 'weekly', limit = 20) {
  return request({
    url: '/v2/leaderboard',
    method: 'get',
    params: {
      metric,
      period,
      limit
    }
  })
}

/**
 * 获取排行榜指标列表
 */
export function getLeaderboardMetrics() {
  return request({
    url: '/v2/leaderboard/metrics',
    method: 'get'
  })
}

/**
 * 获取统计周期列表
 */
export function getLeaderboardPeriods() {
  return request({
    url: '/v2/leaderboard/periods',
    method: 'get'
  })
}

/**
 * 获取多维度排行榜概览
 * @param {string} period - 统计周期
 * @param {number} limit - 每个排行榜返回数量
 */
export function getLeaderboardOverview(period = 'weekly', limit = 10) {
  return request({
    url: '/v2/leaderboard/overview',
    method: 'get',
    params: {
      period,
      limit
    }
  })
}



/**
 * 获取当前用户在排行榜中的位置
 * @param {number} type - 排行榜类型 (1=Weekly, 2=Monthly, 3=AllTime)
 */
export function getMyRank(type = 1) {
  return request({
    url: '/v2/gamification/my-rank',
    method: 'get',
    params: {
      type
    }
  })
}

/**
 * 初始化用户游戏化数据
 */
export function initializeUserStats() {
  return request({
    url: '/v2/gamification/initialize',
    method: 'post'
  })
}

/**
 * 手动触发任务创建奖励 (测试用)
 */
export function testTaskCreatedReward(taskId, taskName = '测试任务', taskType = 'General', points = 10) {
  return request({
    url: '/v2/gamification-v2/test/task-created',
    method: 'post',
    params: { taskId, taskName, taskType, points }
  })
}

/**
 * 手动触发任务完成奖励 (测试用)
 */
export function testTaskCompletedReward(taskId, taskName = '测试任务', taskType = 'General', isOnTime = true, points = 20) {
  return request({
    url: '/v2/gamification-v2/test/task-completed',
    method: 'post',
    params: { taskId, taskName, taskType, isOnTime, points }
  })
}

/**
 * 更新用户统计
 */
export function updateUserStats(userId = null) {
  return request({
    url: '/v2/gamification-v2/stats/update',
    method: 'post',
    params: userId ? { userId } : {}
  })
}

/**
 * 更新排行榜
 */
export function updateLeaderboard(leaderboardType = 'weekly', period = 'current') {
  return request({
    url: '/v2/gamification-v2/leaderboard/update',
    method: 'post',
    params: { leaderboardType, period }
  })
}

/**
 * 任务领取API (集成游戏化奖励)
 * @param {number} taskId - 任务ID
 * @param {string} notes - 备注
 */
export function claimTask(taskId, notes = '') {
  return request({
    url: `/v2/tasks/${taskId}/claim`,
    method: 'post',
    data: {
      notes
    }
  })
}

/**
 * 获取用户今日任务领取记录
 */
export function getUserTodayClaims() {
  return request({
    url: '/v2/tasks/claims/today',
    method: 'get'
  })
}

/**
 * 更新任务领取状态
 * @param {number} claimId - 领取记录ID
 * @param {string} status - 新状态 (Claimed, Started, Completed, Cancelled)
 * @param {string} notes - 备注
 */
export function updateClaimStatus(claimId, status, notes = '') {
  return request({
    url: `/v2/tasks/claims/${claimId}/status`,
    method: 'patch',
    data: {
      claimStatus: status,
      notes
    }
  })
}

/**
 * 获取今日班次任务统计
 * @param {Object} params - 查询参数
 * @param {number} params.shiftId - 班次ID (可选)
 * @param {number} params.userId - 用户ID (可选)
 * @param {string} params.statisticsDate - 统计日期 (可选)
 */
export function getTodayShiftStatistics(params = {}) {
  return request({
    url: '/v2/tasks/claims/statistics',
    method: 'get',
    params
  })
}

/**
 * 触发任务领取奖励
 * @param {number} taskId - 任务ID
 */
export function triggerClaimReward(taskId) {
  return request({
    url: '/v2/gamification-v2/trigger-claim-reward',
    method: 'post',
    data: {
      taskId
    }
  })
}

/**
 * 触发任务完成奖励
 * @param {number} taskId - 任务ID
 * @param {boolean} isOnTime - 是否按时完成
 */
export function triggerCompleteReward(taskId, isOnTime = false) {
  return request({
    url: '/v2/gamification-v2/trigger-complete-reward',
    method: 'post',
    data: {
      taskId,
      isOnTime
    }
  })
}

/**
 * 获取周统计汇总 - 所有人按周的任务创建、领取、完成数量
 * @param {number} weekOffset - 周偏移量 (0=本周, -1=上周, 1=下周)
 */
export function getWeeklyStats(weekOffset = 0) {
  return request({
    url: '/v2/gamification/weekly-stats',
    method: 'get',
    params: {
      weekOffset
    }
  })
}

/**
 * 获取用户周统计详情
 * @param {number} userId - 用户ID (可选，默认当前用户)
 * @param {number} weekOffset - 周偏移量 (0=本周, -1=上周, 1=下周)
 */
export function getUserWeeklyStats(userId = null, weekOffset = 0) {
  const params = { weekOffset }
  if (userId) {
    params.userId = userId
  }

  return request({
    url: '/v2/gamification/weekly-stats/user',
    method: 'get',
    params
  })
}

// 排行榜类型常量
export const LeaderboardType = {
  WEEKLY: 1,
  MONTHLY: 2,
  ALL_TIME: 3
}

// 排行榜指标常量
export const LeaderboardMetrics = {
  POINTS: 'points',
  COINS: 'coins',
  DIAMONDS: 'diamonds',
  TASKS_CREATED: 'tasks_created',
  TASKS_COMPLETED: 'tasks_completed',
  TASKS_CLAIMED: 'tasks_claimed',
  FAULTS_RECORDED: 'faults_recorded',
  MAINTENANCE_CREATED: 'maintenance_created',
  ASSETS_UPDATED: 'assets_updated'
}

// 统计周期常量
export const StatisticsPeriods = {
  DAILY: 'daily',
  WEEKLY: 'weekly',
  MONTHLY: 'monthly',
  QUARTERLY: 'quarterly',
  YEARLY: 'yearly',
  ALLTIME: 'alltime'
}

// 任务领取状态常量
export const TaskClaimStatus = {
  CLAIMED: 'Claimed',
  STARTED: 'Started', 
  COMPLETED: 'Completed',
  CANCELLED: 'Cancelled'
}

// 游戏化事件类型常量
export const GamificationEventType = {
  TASK_COMPLETED: 'TaskCompleted',
  TASK_CLAIMED: 'TaskClaimed',
  TASK_CREATED: 'TaskCreated',
  TASK_UPDATED: 'TaskUpdated',
  BADGE_EARNED: 'BadgeEarned',
  LEVEL_UP: 'LevelUp',
  POINTS_SPENT: 'PointsSpent',
  DAILY_LOGIN: 'DailyLogin',
  COMMENT_ADDED: 'CommentAdded',
  STREAK_MAINTAINED: 'StreakMaintained',
  ON_TIME_COMPLETION: 'OnTimeCompletion'
}

export default {
  getUserStats,
  getUserStatsById,
  getDailyTaskStats,
  getLeaderboard,
  getMultiDimensionLeaderboard,
  getLeaderboardMetrics,
  getLeaderboardPeriods,
  getLeaderboardOverview,
  getMyRank,
  initializeUserStats,
  triggerClaimReward,
  triggerCompleteReward,
  claimTask,
  getUserTodayClaims,
  updateClaimStatus,
  getTodayShiftStatistics,
  getWeeklyStats,
  getUserWeeklyStats,
  LeaderboardType,
  LeaderboardMetrics,
  StatisticsPeriods,
  TaskClaimStatus,
  GamificationEventType
}
