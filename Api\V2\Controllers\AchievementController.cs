// File: Api/V2/Controllers/AchievementController.cs
// Description: 成就系统API控制器

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Security.Claims;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Logging;
using ItAssetsSystem.Services.Interfaces;
using ItAssetsSystem.Application.Common.Dtos;

namespace ItAssetsSystem.Api.V2.Controllers
{
    /// <summary>
    /// 成就系统API控制器
    /// </summary>
    [ApiController]
    [Route("api/v2/achievements")]
    [Authorize]
    public class AchievementController : ControllerBase
    {
        private readonly IAchievementService _achievementService;
        private readonly ILogger<AchievementController> _logger;

        public AchievementController(
            IAchievementService achievementService,
            ILogger<AchievementController> logger)
        {
            _achievementService = achievementService;
            _logger = logger;
        }

        /// <summary>
        /// 获取当前用户成就列表
        /// </summary>
        /// <returns>用户成就列表</returns>
        [HttpGet("my")]
        public async Task<IActionResult> GetMyAchievements()
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == null)
                {
                    return Unauthorized(ApiResponse<object>.CreateFail("用户未登录"));
                }

                var achievements = await _achievementService.GetUserAchievementsAsync(userId.Value);
                return Ok(ApiResponse<List<UserAchievementDto>>.CreateSuccess(achievements, "获取成就列表成功"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户成就列表失败");
                return StatusCode(500, ApiResponse<object>.CreateFail("获取成就列表失败"));
            }
        }

        /// <summary>
        /// 获取指定用户成就列表
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>用户成就列表</returns>
        [HttpGet("user/{userId:int}")]
        public async Task<IActionResult> GetUserAchievements(int userId)
        {
            try
            {
                var achievements = await _achievementService.GetUserAchievementsAsync(userId);
                return Ok(ApiResponse<List<UserAchievementDto>>.CreateSuccess(achievements, "获取成就列表成功"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户 {UserId} 成就列表失败", userId);
                return StatusCode(500, ApiResponse<object>.CreateFail("获取成就列表失败"));
            }
        }

        /// <summary>
        /// 获取当前用户成就统计
        /// </summary>
        /// <returns>成就统计信息</returns>
        [HttpGet("my/stats")]
        public async Task<IActionResult> GetMyAchievementStats()
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == null)
                {
                    return Unauthorized(ApiResponse<object>.CreateFail("用户未登录"));
                }

                var stats = await _achievementService.GetAchievementStatsAsync(userId.Value);
                return Ok(ApiResponse<AchievementStatsDto>.CreateSuccess(stats, "获取成就统计成功"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户成就统计失败");
                return StatusCode(500, ApiResponse<object>.CreateFail("获取成就统计失败"));
            }
        }

        /// <summary>
        /// 获取指定用户成就统计
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>成就统计信息</returns>
        [HttpGet("user/{userId:int}/stats")]
        public async Task<IActionResult> GetUserAchievementStats(int userId)
        {
            try
            {
                var stats = await _achievementService.GetAchievementStatsAsync(userId);
                return Ok(ApiResponse<AchievementStatsDto>.CreateSuccess(stats, "获取成就统计成功"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户 {UserId} 成就统计失败", userId);
                return StatusCode(500, ApiResponse<object>.CreateFail("获取成就统计失败"));
            }
        }

        /// <summary>
        /// 手动检查当前用户成就进度
        /// </summary>
        /// <returns>检查结果</returns>
        [HttpPost("my/check")]
        public async Task<IActionResult> CheckMyAchievements()
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == null)
                {
                    return Unauthorized(ApiResponse<object>.CreateFail("用户未登录"));
                }

                await _achievementService.CheckUserAchievementsAsync(userId.Value);
                return Ok(ApiResponse<object>.CreateSuccess(null, "成就检查完成"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查用户成就进度失败");
                return StatusCode(500, ApiResponse<object>.CreateFail("检查成就进度失败"));
            }
        }

        /// <summary>
        /// 初始化当前用户成就进度
        /// </summary>
        /// <returns>初始化结果</returns>
        [HttpPost("my/initialize")]
        public async Task<IActionResult> InitializeMyAchievements()
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == null)
                {
                    return Unauthorized(ApiResponse<object>.CreateFail("用户未登录"));
                }

                await _achievementService.InitializeUserAchievementsAsync(userId.Value);
                return Ok(ApiResponse<object>.CreateSuccess(null, "成就初始化完成"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "初始化用户成就进度失败");
                return StatusCode(500, ApiResponse<object>.CreateFail("初始化成就进度失败"));
            }
        }

        /// <summary>
        /// 获取成就概览（已完成/进行中/总数）
        /// </summary>
        /// <returns>成就概览</returns>
        [HttpGet("overview")]
        public async Task<IActionResult> GetAchievementOverview()
        {
            try
            {
                var userId = GetCurrentUserId();
                if (userId == null)
                {
                    return Unauthorized(ApiResponse<object>.CreateFail("用户未登录"));
                }

                var achievements = await _achievementService.GetUserAchievementsAsync(userId.Value);
                var stats = await _achievementService.GetAchievementStatsAsync(userId.Value);

                var overview = new
                {
                    Stats = stats,
                    RecentCompleted = achievements
                        .Where(a => a.IsCompleted)
                        .OrderByDescending(a => a.AchievedAt)
                        .Take(5)
                        .ToList(),
                    InProgress = achievements
                        .Where(a => !a.IsCompleted && a.CurrentValue > 0)
                        .OrderByDescending(a => a.Progress)
                        .Take(5)
                        .ToList(),
                    NextToComplete = achievements
                        .Where(a => !a.IsCompleted)
                        .OrderByDescending(a => a.Progress)
                        .Take(3)
                        .ToList()
                };

                return Ok(ApiResponse<object>.CreateSuccess(overview, "获取成就概览成功"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取成就概览失败");
                return StatusCode(500, ApiResponse<object>.CreateFail("获取成就概览失败"));
            }
        }

        /// <summary>
        /// 获取当前用户ID
        /// </summary>
        /// <returns>用户ID</returns>
        private int? GetCurrentUserId()
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (int.TryParse(userIdClaim, out var userId))
            {
                return userId;
            }
            return null;
        }
    }
}
