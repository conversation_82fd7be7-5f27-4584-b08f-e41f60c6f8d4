import{_ as s,b as e,o as t,d as a,$ as i,t as d,bp as r}from"./index-CG5lHOPO.js";const p={class:"page-header"},c={class:"header-left"},o={class:"title"},l={key:0,class:"description"},n={class:"header-right"},g=s({__name:"PageHeader",props:{title:{type:String,required:!0},description:{type:String,default:""}},setup:s=>(g,h)=>(t(),e("div",p,[a("div",c,[a("h2",o,d(s.title),1),s.description?(t(),e("p",l,d(s.description),1)):i("",!0)]),a("div",n,[r(g.$slots,"actions",{},void 0,!0)])]))},[["__scopeId","data-v-21c6c352"]]);export{g as P};
