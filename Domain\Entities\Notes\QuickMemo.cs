// File: Domain/Entities/Notes/QuickMemo.cs
// Description: Represents a quick memo or note, including its content, category, and user association.
#nullable enable
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using ItAssetsSystem.Models.Entities; // For User entity

namespace ItAssetsSystem.Domain.Entities.Notes
{
    [Table("quick_memos")] // Table name changed from quick_notes
    public class QuickMemo // Class name changed from QuickNote
    {
        [Key]
        [Column("id")]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)] // Assuming string ID will be GUID generated by app
        public string Id { get; set; } = Guid.NewGuid().ToString();

        [Required]
        [MaxLength(200)]
        [Column("title")] // New field
        public string Title { get; set; } = string.Empty;

        [Column("content", TypeName = "TEXT")] // Ensure TEXT type for potentially long content
        public string? Content { get; set; }

        [Required]
        [Column("user_id")]
        public int UserId { get; set; }

        [Column("category_id")] // New field for foreign key
        public string? CategoryId { get; set; }

        [Column("is_pinned")]
        public bool IsPinned { get; set; } = false;

        [MaxLength(7)] // e.g., "#RRGGBB"
        [Column("color")]
        public string? Color { get; set; }

        [Column("created_at")]
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        [Column("updated_at")]
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // 修改列名映射以匹配数据库中的实际列名
        [Column("PositionX")]
        public int PositionXDb { get; set; }

        // 修改列名映射以匹配数据库中的实际列名
        [Column("PositionY")]
        public int PositionYDb { get; set; }

        // 应用程序使用的double类型属性
        [NotMapped]
        public double PositionXApp 
        { 
            get => PositionXDb / 10.0; 
            set => PositionXDb = (int)(value * 10); 
        }

        // 应用程序使用的double类型属性
        [NotMapped]
        public double PositionYApp 
        { 
            get => PositionYDb / 10.0; 
            set => PositionYDb = (int)(value * 10); 
        }

        [Column("SizeWidth")]
        public int SizeWidth { get; set; } = 200; // Default width

        [Column("SizeHeight")]
        public int SizeHeight { get; set; } = 180; // Default height

        [Column("ZIndex")]
        public int ZIndex { get; set; }

        // Navigation properties
        [ForeignKey("UserId")]
        public virtual User User { get; set; } = null!;

        [ForeignKey("CategoryId")]
        public virtual QuickMemoCategory? Category { get; set; } // Navigation to Category
    }
} 