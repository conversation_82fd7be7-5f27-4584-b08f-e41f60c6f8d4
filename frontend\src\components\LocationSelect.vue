<template>
  <el-tree-select
    v-model="selectedValue"
    :data="locationTree"
    :render-after-expand="false"
    :show-checkbox="multiple"
    :check-strictly="checkStrictly"
    :placeholder="placeholder"
    :filterable="true"
    :clearable="true"
    node-key="id"
    :props="treeProps"
    @change="handleChange"
  />
</template>

<script>
import { ref, onMounted, watch } from 'vue'
import locationApi from '@/api/location'

export default {
  name: 'LocationSelect',
  props: {
    modelValue: {
      type: [String, Number, Array],
      default: null
    },
    multiple: {
      type: Boolean,
      default: false
    },
    checkStrictly: {
      type: Boolean,
      default: false
    },
    placeholder: {
      type: String,
      default: '请选择位置'
    }
  },
  emits: ['update:modelValue', 'change'],
  setup(props, { emit }) {
    const selectedValue = ref(props.modelValue)
    const locationTree = ref([])

    const treeProps = {
      children: 'children',
      label: 'name',
      value: 'id'
    }

    const loadLocationTree = async () => {
      try {
        const response = await locationApi.getTree()
        locationTree.value = response.data || []
      } catch (error) {
        console.error('加载位置树失败:', error)
        locationTree.value = []
      }
    }

    const handleChange = (value) => {
      emit('update:modelValue', value)
      emit('change', value)
    }

    watch(() => props.modelValue, (newValue) => {
      selectedValue.value = newValue
    })

    onMounted(() => {
      loadLocationTree()
    })

    return {
      selectedValue,
      locationTree,
      treeProps,
      handleChange
    }
  }
}
</script>