/**
 * 认证相关API接口
 * 文件：src/api/auth.js
 * 功能：处理用户登录、登出和用户信息获取的API请求
 */

import request from '@/utils/request'

/**
 * 用户登录接口
 * @param {Object} data - 包含用户名和密码的对象
 * @returns {Promise<Object>} 登录结果，含token等信息
 */
export function login(data) {
  console.log('调用登录接口，使用正确路径');
  // 使用正确路径 /api/user/login
  return request.post('/user/login', data)
}

/**
 * 退出登录接口
 * @returns {Promise<Object>} 登出结果
 */
export function logout() {
  return request.post('/user/logout')
}

/**
 * 获取当前用户信息
 * @returns {Promise<Object>} 用户信息，包含权限和角色
 */
export function getUserInfo() {
  return request.get('/v2/profile')
}

/**
 * 刷新用户令牌
 * @param {String} refreshToken - 刷新令牌
 * @returns {Promise<Object>} 新的令牌信息
 */
export function refreshToken(refreshToken) {
  return request.post('/user/refresh-token', { refreshToken })
}

/**
 * 获取验证码
 * @returns {Promise} - 返回验证码图片
 */
export function getCaptcha() {
  return request.get('/user/captcha', { responseType: 'blob' })
}

/**
 * 修改密码
 * @param {Object} data - 包含oldPassword和newPassword的对象
 * @returns {Promise}
 */
export function changePassword(data) {
  return request.post('/user/change-password', data)
}

// 导出所有认证相关API
const authApi = {
  login,
  logout,
  getUserInfo,
  refreshToken,
  getCaptcha,
  changePassword
}

export default authApi 