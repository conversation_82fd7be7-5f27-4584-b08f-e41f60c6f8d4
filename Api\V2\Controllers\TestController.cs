// File: Api/V2/Controllers/TestController.cs
// Description: 测试控制器，用于诊断API问题

using System;
using System.Threading.Tasks;
using ItAssetsSystem.Application.Features.SpareParts.Services;
using ItAssetsSystem.Application.Features.Gamification.Services;
using ItAssetsSystem.Core.Abstractions;
using Microsoft.AspNetCore.Mvc;
using System.Text.Json;
using Microsoft.Extensions.Logging;
using ItAssetsSystem.Application.Common.Dtos;

namespace ItAssetsSystem.Api.V2.Controllers
{
    [ApiController]
    [Route("api/v2/test")]
    public class TestController : ControllerBase
    {
        private readonly ISparePartTypeService _typeService;
        private readonly ISparePartTypeRepository _typeRepository;
        private readonly ILogger<TestController> _logger;
        
        public TestController(
            ISparePartTypeService typeService,
            ISparePartTypeRepository typeRepository,
            ILogger<TestController> logger)
        {
            _typeService = typeService;
            _typeRepository = typeRepository;
            _logger = logger;
        }
        
        [HttpGet("spareparttypes")]
        public async Task<IActionResult> TestSparePartTypes()
        {
            try
            {
                var types = await _typeRepository.GetAllAsync();
                return Ok(new { 
                    Message = "成功从仓储获取备品备件类型",
                    Count = types.Count,
                    Types = types
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { 
                    Error = ex.Message,
                    StackTrace = ex.StackTrace,
                    InnerException = ex.InnerException?.Message
                });
            }
        }

        [HttpGet("typetree")]
        public async Task<IActionResult> TestTypeTree()
        {
            try
            {
                var typeTree = await _typeService.GetTypeTreeAsync();
                return Ok(new { 
                    Message = "成功从服务获取备品备件类型树",
                    Count = typeTree.Count,
                    TypeTree = typeTree
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { 
                    Error = ex.Message,
                    StackTrace = ex.StackTrace,
                    InnerException = ex.InnerException?.Message
                });
            }
        }
    }
} 