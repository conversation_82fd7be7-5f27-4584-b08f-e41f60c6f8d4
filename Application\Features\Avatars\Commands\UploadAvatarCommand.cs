// File: Application/Features/Avatars/Commands/UploadAvatarCommand.cs
// Description: 上传头像命令

#nullable enable

using MediatR;
using Microsoft.AspNetCore.Http;
using ItAssetsSystem.Application.Features.Avatars.Dtos;

namespace ItAssetsSystem.Application.Features.Avatars.Commands
{
    /// <summary>
    /// 上传头像命令
    /// </summary>
    public class UploadAvatarCommand : IRequest<UploadAvatarResponseDto>
    {
        /// <summary>
        /// 头像文件
        /// </summary>
        public IFormFile File { get; set; } = null!;

        /// <summary>
        /// 用户ID
        /// </summary>
        public string UserId { get; set; } = string.Empty;
    }
} 