<template>
  <div class="leaderboard-container">
    <el-row :gutter="20">
      <el-col :span="24">
        <el-card class="leaderboard-header">
          <h1>积分排行榜</h1>
          <p>查看团队成员的积分排名和成就展示</p>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="mt-20">
      <!-- 积分排行榜部分 -->
      <el-col :span="16">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>积分排行榜</span>
              <el-radio-group v-model="timeRange" size="small">
                <el-radio-button label="week">本周</el-radio-button>
                <el-radio-button label="month">本月</el-radio-button>
                <el-radio-button label="year">本年</el-radio-button>
                <el-radio-button label="all">全部</el-radio-button>
              </el-radio-group>
            </div>
          </template>
          
          <div class="top-users">
            <div class="top-user second-place">
              <div class="rank-badge">2</div>
              <el-avatar :size="80" :src="userList[1].avatar"></el-avatar>
              <div class="user-info">
                <div class="user-name">{{userList[1].name}}</div>
                <div class="user-rank-score">{{userList[1].rankScore}} 分</div>
                <div class="user-rewards">
                  <div class="score-with-icon">
                    <img src="https://cdn-icons-png.flaticon.com/512/2933/2933116.png" class="coin-icon" />
                    <span>{{userList[1].score}}</span>
                  </div>
                  <div class="score-with-icon">
                    <img src="https://cdn-icons-png.flaticon.com/512/2933/2933151.png" class="diamond-icon" />
                    <span>{{userList[1].diamonds}}</span>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="top-user first-place">
              <div class="rank-badge">1</div>
              <el-avatar :size="100" :src="userList[0].avatar"></el-avatar>
              <div class="user-info">
                <div class="user-name">{{userList[0].name}}</div>
                <div class="user-rank-score">{{userList[0].rankScore}} 分</div>
                <div class="user-rewards">
                  <div class="score-with-icon">
                    <img src="https://cdn-icons-png.flaticon.com/512/2933/2933116.png" class="coin-icon" />
                    <span>{{userList[0].score}}</span>
                  </div>
                  <div class="score-with-icon">
                    <img src="https://cdn-icons-png.flaticon.com/512/2933/2933151.png" class="diamond-icon" />
                    <span>{{userList[0].diamonds}}</span>
                  </div>
                </div>
              </div>
              <div class="crown">
                <el-icon :size="24"><Trophy /></el-icon>
              </div>
            </div>
            
            <div class="top-user third-place">
              <div class="rank-badge">3</div>
              <el-avatar :size="80" :src="userList[2].avatar"></el-avatar>
              <div class="user-info">
                <div class="user-name">{{userList[2].name}}</div>
                <div class="user-rank-score">{{userList[2].rankScore}} 分</div>
                <div class="user-rewards">
                  <div class="score-with-icon">
                    <img src="https://cdn-icons-png.flaticon.com/512/2933/2933116.png" class="coin-icon" />
                    <span>{{userList[2].score}}</span>
                  </div>
                  <div class="score-with-icon">
                    <img src="https://cdn-icons-png.flaticon.com/512/2933/2933151.png" class="diamond-icon" />
                    <span>{{userList[2].diamonds}}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <el-table :data="userList.slice(3)" stripe style="width: 100%">
            <el-table-column type="index" :index="indexMethod" width="80" label="排名" />
            <el-table-column prop="avatar" label="头像" width="80">
              <template #default="scope">
                <el-avatar :size="40" :src="scope.row.avatar"></el-avatar>
              </template>
            </el-table-column>
            <el-table-column prop="name" label="姓名" />
            <el-table-column prop="department" label="部门" />
            <el-table-column prop="rankScore" label="排行分" sortable />
            <el-table-column label="金币">
              <template #default="scope">
                <div class="score-with-icon">
                  <img src="https://cdn-icons-png.flaticon.com/512/2933/2933116.png" class="coin-icon" />
                  <span>{{scope.row.score}}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="钻石">
              <template #default="scope">
                <div class="score-with-icon">
                  <img src="https://cdn-icons-png.flaticon.com/512/2933/2933151.png" class="diamond-icon" />
                  <span>{{scope.row.diamonds}}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="completedTasks" label="完成任务数" sortable />
            <el-table-column label="操作">
              <template #default="scope">
                <el-button type="text" @click="viewProfile(scope.row.id)">查看资料</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
      
      <!-- 成就展示部分 -->
      <el-col :span="8">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>成就达人</span>
            </div>
          </template>
          
          <div v-for="(achievement, index) in topAchievements" :key="index" class="achievement-item">
            <el-avatar :size="40" :src="achievement.user.avatar"></el-avatar>
            <div class="achievement-info">
              <div class="achievement-title">
                <span>{{achievement.user.name}}</span>
                <el-tag size="small" type="success">{{achievement.title}}</el-tag>
              </div>
              <div class="achievement-desc">{{achievement.description}}</div>
              <div class="achievement-time">{{achievement.time}}</div>
              <div class="achievement-reward">
                <div class="score-with-icon" v-if="achievement.score">
                  <img src="https://cdn-icons-png.flaticon.com/512/2933/2933116.png" class="coin-icon" />
                  <span>+{{achievement.score}}</span>
                </div>
                <div class="score-with-icon" v-if="achievement.diamonds">
                  <img src="https://cdn-icons-png.flaticon.com/512/2933/2933151.png" class="diamond-icon" />
                  <span>+{{achievement.diamonds}}</span>
                </div>
              </div>
            </div>
          </div>
          
          <div class="text-center mt-20">
            <el-button type="primary" plain size="small" @click="showAllAchievements">
              查看全部成就
            </el-button>
          </div>
        </el-card>
        
        <el-card class="mt-20">
          <template #header>
            <div class="card-header">
              <span>本月之星</span>
            </div>
          </template>
          
          <div class="star-of-month">
            <el-avatar :size="80" :src="starOfMonth.avatar"></el-avatar>
            <div class="star-info">
              <h3>{{starOfMonth.name}}</h3>
              <p>{{starOfMonth.department}} - {{starOfMonth.position}}</p>
              <div class="star-data">
                <div class="star-item">
                  <div class="star-value">{{starOfMonth.taskCount}}</div>
                  <div class="star-label">完成任务</div>
                </div>
                <div class="star-item">
                  <div class="star-value">{{starOfMonth.monthlyRankScore}}</div>
                  <div class="star-label">本月排行分</div>
                </div>
                <div class="star-item">
                  <div class="star-value">{{starOfMonth.achievementCount}}</div>
                  <div class="star-label">获得成就</div>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 成就弹窗 -->
    <el-dialog title="全部成就" v-model="achievementDialogVisible" width="70%">
      <el-tabs v-model="achievementActiveTab">
        <el-tab-pane label="全部成就" name="all">
          <el-table :data="allAchievements" style="width: 100%">
            <el-table-column prop="user.name" label="用户" />
            <el-table-column prop="title" label="成就名称" />
            <el-table-column prop="description" label="描述" />
            <el-table-column prop="time" label="获得时间" />
            <el-table-column label="奖励">
              <template #default="scope">
                <div class="reward-container">
                  <div class="score-with-icon">
                    <img src="https://cdn-icons-png.flaticon.com/512/2933/2933116.png" class="coin-icon" />
                    <span>+{{scope.row.score}}</span>
                  </div>
                  <div class="score-with-icon" v-if="scope.row.diamonds">
                    <img src="https://cdn-icons-png.flaticon.com/512/2933/2933151.png" class="diamond-icon" />
                    <span>+{{scope.row.diamonds}}</span>
                  </div>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="成就分类" name="categories">
          <el-collapse accordion>
            <el-collapse-item v-for="(category, index) in achievementCategories" :key="index" :title="category.name">
              <div v-for="(achievement, i) in category.achievements" :key="i" class="achievement-category-item">
                <div class="achievement-category-icon">
                  <el-icon><component :is="achievement.icon" /></el-icon>
                </div>
                <div class="achievement-category-info">
                  <div class="achievement-category-title">{{achievement.title}}</div>
                  <div class="achievement-category-desc">{{achievement.description}}</div>
                </div>
                <div class="achievement-category-reward">
                  <div class="score-with-icon">
                    <img src="https://cdn-icons-png.flaticon.com/512/2933/2933116.png" class="coin-icon" />
                    <span>+{{achievement.score}}</span>
                  </div>
                  <div class="score-with-icon" v-if="achievement.diamonds">
                    <img src="https://cdn-icons-png.flaticon.com/512/2933/2933151.png" class="diamond-icon" />
                    <span>+{{achievement.diamonds}}</span>
                  </div>
                </div>
              </div>
            </el-collapse-item>
          </el-collapse>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { Trophy, Star, Medal, Lightning, UserFilled, User } from '@element-plus/icons-vue'
import { getLeaderboard, getUserStats, updateLeaderboard } from '@/api/gamification'
import { ElMessage } from 'element-plus'

export default {
  name: 'LeaderboardView',
  components: {
    Trophy,
    Star,
    Medal,
    Lightning,
    UserFilled,
    User
  },
  setup() {
    const router = useRouter()
    const timeRange = ref('month')
    const achievementDialogVisible = ref(false)
    const achievementActiveTab = ref('all')
    const loading = ref(false)

    // 用户排行榜数据
    const userList = ref([
      {
        id: 1,
        name: '张三',
        avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
        department: '研发部',
        position: '高级工程师',
        rankScore: 15500,
        score: 950,
        diamonds: 35,
        completedTasks: 45
      },
      { 
        id: 2, 
        name: '李四', 
        avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png', 
        department: '产品部', 
        position: '产品经理',
        rankScore: 14800,
        score: 920,
        diamonds: 28,
        completedTasks: 42 
      },
      { 
        id: 3, 
        name: '王五', 
        avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png', 
        department: '设计部', 
        position: 'UI设计师',
        rankScore: 14200,
        score: 880,
        diamonds: 20,
        completedTasks: 39 
      },
      { 
        id: 4, 
        name: '赵六', 
        avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png', 
        department: '研发部', 
        position: '前端工程师',
        rankScore: 13500,
        score: 820,
        diamonds: 18,
        completedTasks: 36 
      },
      { 
        id: 5, 
        name: '钱七', 
        avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png', 
        department: '研发部', 
        position: '后端工程师',
        rankScore: 13000,
        score: 790,
        diamonds: 15,
        completedTasks: 33 
      },
      { 
        id: 6, 
        name: '孙八', 
        avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png', 
        department: '测试部', 
        position: '测试工程师',
        rankScore: 12500,
        score: 760,
        diamonds: 12,
        completedTasks: 31 
      },
      { 
        id: 7, 
        name: '周九', 
        avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png', 
        department: '运维部', 
        position: '运维工程师',
        rankScore: 12000,
        score: 720,
        diamonds: 10,
        completedTasks: 28 
      },
      { 
        id: 8, 
        name: '吴十', 
        avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png', 
        department: '市场部', 
        position: '市场专员',
        rankScore: 11500,
        score: 680,
        diamonds: 8,
        completedTasks: 26 
      }
    ]).sort((a, b) => b.rankScore - a.rankScore)
    
    // 成就数据
    const topAchievements = reactive([
      {
        user: { name: '张三', avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png' },
        title: '效率之王',
        description: '一周内完成30个任务',
        time: '2023-05-15',
        score: 100,
        diamonds: 5
      },
      {
        user: { name: '李四', avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png' },
        title: '精益求精',
        description: '连续完成20个高质量任务',
        time: '2023-05-12',
        score: 80,
        diamonds: 3
      },
      {
        user: { name: '王五', avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png' },
        title: '合作专家',
        description: '参与10个团队协作项目',
        time: '2023-05-10',
        score: 60,
        diamonds: 2
      },
      {
        user: { name: '赵六', avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png' },
        title: '创新先锋',
        description: '提出5个创新解决方案',
        time: '2023-05-08',
        score: 50,
        diamonds: 1
      }
    ])
    
    // 本月之星
    const starOfMonth = reactive({
      name: '张三',
      avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
      department: '研发部',
      position: '高级工程师',
      taskCount: 45,
      monthlyRankScore: 3500,
      score: 950,
      achievementCount: 8
    })
    
    // 全部成就
    const allAchievements = reactive([
      ...topAchievements,
      {
        user: { name: '钱七', avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png' },
        title: '学习达人',
        description: '完成3门内部培训课程',
        time: '2023-05-05',
        score: 40,
        diamonds: 1
      },
      {
        user: { name: '孙八', avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png' },
        title: '问题终结者',
        description: '解决15个系统bug',
        time: '2023-05-03',
        score: 30
      }
    ])
    
    // 成就分类
    const achievementCategories = reactive([
      {
        name: '任务成就',
        achievements: [
          { title: '初出茅庐', description: '完成第一个任务', icon: 'Star', score: 10 },
          { title: '效率之王', description: '一周内完成30个任务', icon: 'Trophy', score: 100, diamonds: 5 },
          { title: '任务达人', description: '累计完成100个任务', icon: 'Medal', score: 200, diamonds: 10 }
        ]
      },
      {
        name: '质量成就',
        achievements: [
          { title: '精益求精', description: '连续完成20个高质量任务', icon: 'Medal', score: 80, diamonds: 3 },
          { title: '质量标杆', description: '任务质量评分达到90分以上', icon: 'Trophy', score: 150, diamonds: 8 }
        ]
      },
      {
        name: '创新成就',
        achievements: [
          { title: '创新先锋', description: '提出5个创新解决方案', icon: 'Lightning', score: 50, diamonds: 1 },
          { title: '创意大师', description: '提出的创新方案被采纳', icon: 'Lightning', score: 120, diamonds: 6 }
        ]
      },
      {
        name: '协作成就',
        achievements: [
          { title: '合作专家', description: '参与10个团队协作项目', icon: 'UserFilled', score: 60, diamonds: 2 },
          { title: '团队领袖', description: '成功领导一个团队项目', icon: 'User', score: 130, diamonds: 7 }
        ]
      }
    ])
    
    // 处理排名序号
    const indexMethod = (index) => {
      return index + 4 // 前三名已经单独展示
    }
    
    // 查看个人资料
    const viewProfile = (userId) => {
      router.push(`/profile/${userId}`)
    }
    
    // 加载排行榜数据
    const loadLeaderboard = async () => {
      try {
        loading.value = true
        const response = await getLeaderboard(timeRange.value, 20)

        if (response.success && response.data) {
          userList.value = response.data.map((user, index) => ({
            id: user.userId || user.id,
            name: user.userName || user.name || '未知用户',
            avatar: user.avatar || 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
            department: user.department || '未分配',
            position: user.position || '员工',
            rankScore: user.pointsBalance || 0,
            score: user.coinsBalance || 0,
            diamonds: user.diamondsBalance || 0,
            completedTasks: user.completedTasksCount || 0,
            rank: index + 1
          }))
        }
      } catch (error) {
        console.error('加载排行榜失败:', error)
        ElMessage.error('加载排行榜失败')
      } finally {
        loading.value = false
      }
    }

    // 刷新排行榜
    const refreshLeaderboard = async () => {
      try {
        await updateLeaderboard(timeRange.value)
        ElMessage.success('排行榜更新成功')
        await loadLeaderboard()
      } catch (error) {
        console.error('更新排行榜失败:', error)
        ElMessage.error('更新排行榜失败')
      }
    }

    // 显示全部成就
    const showAllAchievements = () => {
      achievementDialogVisible.value = true
    }

    // 监听时间范围变化
    const handleTimeRangeChange = () => {
      loadLeaderboard()
    }

    // 初始化
    onMounted(() => {
      loadLeaderboard()
    })

    return {
      timeRange,
      userList,
      topAchievements,
      starOfMonth,
      allAchievements,
      achievementCategories,
      achievementDialogVisible,
      achievementActiveTab,
      loading,
      indexMethod,
      viewProfile,
      showAllAchievements,
      loadLeaderboard,
      refreshLeaderboard,
      handleTimeRangeChange
    }
  }
}
</script>

<style scoped>
.leaderboard-container {
  padding: 20px;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

.mt-20 {
  margin-top: 20px;
}

.text-center {
  text-align: center;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.leaderboard-header {
  background: linear-gradient(135deg, var(--app-primary-color) 0%, var(--app-secondary-color) 100%);
  text-align: center;
  padding: 30px 0;
  border-radius: 8px;
  color: white;
}

.leaderboard-header h1 {
  margin: 0;
  font-size: 24px;
  color: white;
}

.leaderboard-header p {
  margin: 10px 0 0;
  opacity: 0.9;
}

/* 前三名展示样式 */
.top-users {
  display: flex;
  justify-content: center;
  align-items: flex-end;
  padding: 20px 0 40px;
  position: relative;
}

.top-user {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  padding: 0 20px;
  transition: transform 0.3s;
}

.top-user:hover {
  transform: translateY(-5px);
}

.first-place {
  margin-top: -20px;
  z-index: 2;
}

.second-place, .third-place {
  z-index: 1;
}

.rank-badge {
  position: absolute;
  top: -10px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: bold;
  color: white;
  z-index: 1;
}

.first-place .rank-badge {
  background-color: var(--game-gold);
  width: 30px;
  height: 30px;
}

.second-place .rank-badge {
  background-color: var(--game-silver);
}

.third-place .rank-badge {
  background-color: var(--game-bronze);
}

.crown {
  position: absolute;
  top: -25px;
  color: var(--game-gold);
  font-size: 24px;
}

.user-info {
  margin-top: 10px;
  text-align: center;
}

.user-name {
  font-weight: bold;
  font-size: 16px;
  color: var(--app-primary-color);
}

.user-rank-score {
  color: var(--game-points);
  font-size: 14px;
  margin: 4px 0;
}

.user-rewards {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  margin-top: 5px;
}

.score-with-icon {
  display: flex;
  align-items: center;
  color: var(--game-points);
  font-weight: bold;
}

.coin-icon, .diamond-icon {
  width: 16px;
  height: 16px;
  margin-right: 4px;
}

/* 成就展示样式 */
.achievement-item {
  display: flex;
  margin-bottom: 15px;
  padding: 15px;
  border-radius: 8px;
  background-color: rgba(214, 158, 46, 0.1);
  transition: transform 0.3s;
}

.achievement-item:hover {
  transform: translateX(5px);
}

.achievement-info {
  margin-left: 15px;
  flex: 1;
}

.achievement-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
}

.achievement-desc {
  color: rgba(26, 86, 219, 0.65);
  font-size: 14px;
  margin-bottom: 5px;
}

.achievement-time {
  color: rgba(63, 131, 248, 0.8);
  font-size: 12px;
}

/* 本月之星样式 */
.star-of-month {
  display: flex;
  padding: 20px;
  background: linear-gradient(135deg, rgba(26, 86, 219, 0.1) 0%, rgba(63, 131, 248, 0.1) 100%);
  border-radius: 8px;
}

.star-info {
  margin-left: 20px;
}

.star-info h3 {
  margin: 0 0 5px 0;
  color: var(--app-primary-color);
}

.star-info p {
  margin: 0 0 15px 0;
  color: rgba(26, 86, 219, 0.75);
}

.star-data {
  display: flex;
  gap: 20px;
}

.star-item {
  text-align: center;
}

.star-value {
  font-size: 20px;
  font-weight: bold;
  color: var(--game-points);
}

.star-label {
  font-size: 12px;
  color: rgba(63, 131, 248, 0.8);
}

/* 更新表格样式 */
:deep(.el-table) {
  --el-table-header-bg-color: rgba(26, 86, 219, 0.05);
}

:deep(.el-table__row:hover) td {
  background-color: rgba(26, 86, 219, 0.02) !important;
}

/* 更新按钮样式 */
:deep(.el-button--primary) {
  background-color: var(--app-primary-color);
  border-color: var(--app-primary-color);
}

:deep(.el-button--primary:hover) {
  background-color: var(--app-secondary-color);
  border-color: var(--app-secondary-color);
}

:deep(.el-button--text) {
  color: var(--app-primary-color);
}

:deep(.el-button--text:hover) {
  color: var(--app-secondary-color);
}
</style> 