using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using ItAssetsSystem.Application.Common.Dtos;

namespace ItAssetsSystem.Application.Features.Statistics.Services
{
    public interface IWorkSummaryService
    {
        Task<ApiResponse<List<WorkSummaryDto>>> GetWorkSummaryAsync(string periodType = "weekly", DateTime? periodDate = null, int limit = 50);
        Task<ApiResponse<List<LeaderboardItemDto>>> GetLeaderboardAsync(int limit = 20);
        Task<ApiResponse<OverviewDto>> GetOverviewAsync();
        Task<ApiResponse<object>> UpdateWorkSummaryAsync(string periodType = "weekly", DateTime? targetDate = null);

        // 增强功能方法
        Task<List<EnhancedLeaderboardDto>> GetEnhancedLeaderboardAsync(int limit = 20);
        Task<ApiResponse<List<TaskClaimStatisticsDto>>> GetTaskClaimStatisticsAsync();
        Task<ApiResponse<bool>> UpdateTaskClaimStatisticsAsync();
    }
}