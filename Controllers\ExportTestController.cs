using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using OfficeOpenXml;
using OfficeOpenXml.Style;

namespace ItAssetsSystem.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class ExportTestController : ControllerBase
    {
        private readonly ILogger<ExportTestController> _logger;

        public ExportTestController(ILogger<ExportTestController> logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// 测试导出XLSX (Excel 2007+格式)
        /// </summary>
        /// <returns>简化的Excel文件</returns>
        [HttpGet("xlsx")]
        public IActionResult ExportXlsx()
        {
            _logger.LogInformation("测试导出XLSX格式");

            try
            {
                // 创建内存流
                using var memoryStream = new MemoryStream();
                
                // 创建Excel包
                using (var package = new ExcelPackage(memoryStream))
                {
                    // 设置许可证模式
                    ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
                    
                    // 创建工作表
                    var worksheet = package.Workbook.Worksheets.Add("Test");
                    
                    // 设置标题行
                    worksheet.Cells[1, 1].Value = "ID";
                    worksheet.Cells[1, 2].Value = "名称";
                    worksheet.Cells[1, 3].Value = "日期";
                    worksheet.Cells[1, 4].Value = "数量";
                    
                    // 设置标题行样式
                    using (var range = worksheet.Cells[1, 1, 1, 4])
                    {
                        range.Style.Font.Bold = true;
                        range.Style.Fill.PatternType = ExcelFillStyle.Solid;
                        range.Style.Fill.BackgroundColor.SetColor(Color.LightGray);
                        range.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                    }
                    
                    // 添加5行数据
                    for (int i = 0; i < 5; i++)
                    {
                        int row = i + 2;
                        worksheet.Cells[row, 1].Value = i + 1;
                        worksheet.Cells[row, 2].Value = $"测试项目 {i + 1}";
                        worksheet.Cells[row, 3].Value = DateTime.Now.AddDays(i);
                        worksheet.Cells[row, 3].Style.Numberformat.Format = "yyyy-MM-dd";
                        worksheet.Cells[row, 4].Value = i * 10 + 5;
                    }
                    
                    // 自动调整列宽
                    worksheet.Cells.AutoFitColumns();
                    
                    // 保存Excel包
                    package.Save();
                }
                
                // 重置内存流位置
                memoryStream.Position = 0;
                
                // 创建内存流副本以确保在方法结束时不会被释放
                var fileBytes = memoryStream.ToArray();
                
                // 设置文件名
                string fileName = $"TestExport_xlsx_{DateTime.Now:yyyyMMddHHmmss}.xlsx";
                
                // 设置响应头，使用RFC 6266标准格式
                Response.Headers.Add("Content-Disposition", $"attachment; filename=\"{fileName}\"");
                Response.Headers.Add("X-Content-Type-Options", "nosniff");
                
                // 返回文件字节数组，而不是直接返回MemoryStream
                return File(
                    fileBytes, 
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", 
                    fileName
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "测试导出XLSX出错");
                return StatusCode(500, new { success = false, message = "导出测试出错: " + ex.Message });
            }
        }

        /// <summary>
        /// 测试导出XLS (Excel 97-2003格式)
        /// </summary>
        /// <returns>简化的旧版Excel文件</returns>
        [HttpGet("xls")]
        public IActionResult ExportXls()
        {
            _logger.LogInformation("测试导出XLS格式");

            try
            {
                // 创建内存流
                using var memoryStream = new MemoryStream();
                
                // 创建Excel包
                using (var package = new ExcelPackage(memoryStream))
                {
                    // 设置许可证模式
                    ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
                    
                    // 创建工作表
                    var worksheet = package.Workbook.Worksheets.Add("Test");
                    
                    // 设置标题行
                    worksheet.Cells[1, 1].Value = "ID";
                    worksheet.Cells[1, 2].Value = "名称";
                    worksheet.Cells[1, 3].Value = "日期";
                    worksheet.Cells[1, 4].Value = "数量";
                    
                    // 设置标题行样式
                    using (var range = worksheet.Cells[1, 1, 1, 4])
                    {
                        range.Style.Font.Bold = true;
                        range.Style.Fill.PatternType = ExcelFillStyle.Solid;
                        range.Style.Fill.BackgroundColor.SetColor(Color.LightGray);
                        range.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                    }
                    
                    // 添加5行数据
                    for (int i = 0; i < 5; i++)
                    {
                        int row = i + 2;
                        worksheet.Cells[row, 1].Value = i + 1;
                        worksheet.Cells[row, 2].Value = $"测试项目 {i + 1}";
                        worksheet.Cells[row, 3].Value = DateTime.Now.AddDays(i);
                        worksheet.Cells[row, 3].Style.Numberformat.Format = "yyyy-MM-dd";
                        worksheet.Cells[row, 4].Value = i * 10 + 5;
                    }
                    
                    // 自动调整列宽
                    worksheet.Cells.AutoFitColumns();
                    
                    // 保存Excel包
                    package.Save();
                }
                
                // 重置内存流位置
                memoryStream.Position = 0;
                
                // 创建内存流副本以确保在方法结束时不会被释放
                var fileBytes = memoryStream.ToArray();
                
                // 设置文件名
                string fileName = $"TestExport_xls_{DateTime.Now:yyyyMMddHHmmss}.xls";
                
                // 设置响应头，使用RFC 6266标准格式
                Response.Headers.Add("Content-Disposition", $"attachment; filename=\"{fileName}\"");
                Response.Headers.Add("X-Content-Type-Options", "nosniff");
                
                // 返回文件字节数组，而不是直接返回MemoryStream
                return File(
                    fileBytes, 
                    "application/vnd.ms-excel", 
                    fileName
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "测试导出XLS出错");
                return StatusCode(500, new { success = false, message = "导出测试出错: " + ex.Message });
            }
        }
    }
} 