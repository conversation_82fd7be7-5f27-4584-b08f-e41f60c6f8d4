# IT资产管理系统 - 插件开发指南

## 1. 插件架构介绍

IT资产管理系统采用微内核+插件架构，通过插件机制可以灵活扩展系统功能。系统核心仅提供基础设施和通用服务，大部分业务功能通过插件实现。

### 1.1 插件架构优势

- **模块化**: 功能以插件形式封装，便于维护和扩展
- **松耦合**: 插件之间通过事件总线通信，降低模块间依赖
- **可热插拔**: 支持在系统运行时动态加载和卸载插件
- **版本控制**: 每个插件有独立的版本号，便于升级和回滚
- **按需加载**: 可根据需要选择加载特定插件

### 1.2 插件类型

系统支持两种类型的插件：

1. **内置插件**: 与系统核心一起编译，随系统启动
2. **外部插件**: 独立编译的程序集，可动态加载和卸载

## 2. 创建插件

### 2.1 插件接口

所有插件必须实现`IPlugin`接口：

```csharp
public interface IPlugin
{
    string Name { get; }
    string Description { get; }
    Version Version { get; }
    bool IsRunning { get; }
    void RegisterServices(IServiceCollection services);
    void Start(IServiceProvider serviceProvider);
    void Stop();
}
```

为简化开发，建议继承`PluginBase`抽象类：

```csharp
public abstract class PluginBase : IPlugin
{
    protected ILogger Logger { get; private set; }
    protected IServiceProvider ServiceProvider { get; private set; }
    
    public abstract string Name { get; }
    public abstract string Description { get; }
    public abstract Version Version { get; }
    public bool IsRunning { get; private set; }
    
    public abstract void RegisterServices(IServiceCollection services);
    
    public virtual void Start(IServiceProvider serviceProvider) { ... }
    public virtual void Stop() { ... }
    
    protected virtual void OnStart() { }
    protected virtual void OnStop() { }
}
```

### 2.2 插件基本结构

一个典型的插件包含以下部分：

1. **插件类**: 实现`IPlugin`接口或继承`PluginBase`类
2. **服务接口**: 定义插件提供的服务
3. **服务实现**: 实现服务接口
4. **事件类**: 定义插件产生或处理的事件
5. **配置类**: 定义插件配置

### 2.3 插件示例

```csharp
public class SamplePlugin : PluginBase
{
    private IEventBus _eventBus;
    
    public override string Name => "Sample";
    public override string Description => "示例插件";
    public override Version Version => new Version(1, 0, 0);
    
    public override void RegisterServices(IServiceCollection services)
    {
        // 注册插件提供的服务
        services.AddScoped<ISampleService, SampleService>();
    }
    
    protected override void OnStart()
    {
        _eventBus = ServiceProvider.GetRequiredService<IEventBus>();
        
        // 订阅事件
        _eventBus.Subscribe<SampleEvent>(HandleSampleEvent);
        
        Logger.LogInformation("示例插件已启动");
    }
    
    protected override void OnStop()
    {
        // 取消订阅事件
        _eventBus.Unsubscribe<SampleEvent>(HandleSampleEvent);
        
        Logger.LogInformation("示例插件已停止");
    }
    
    private void HandleSampleEvent(SampleEvent @event)
    {
        Logger.LogInformation($"处理示例事件: {@event.Message}");
    }
}

public class SampleEvent
{
    public string Message { get; set; }
}

public interface ISampleService
{
    void DoSomething();
}

public class SampleService : ISampleService
{
    private readonly ILogger<SampleService> _logger;
    
    public SampleService(ILogger<SampleService> logger)
    {
        _logger = logger;
    }
    
    public void DoSomething()
    {
        _logger.LogInformation("示例服务执行操作");
    }
}
```

## 3. 开发外部插件

### 3.1 创建插件项目

1. 创建一个Class Library (.NET Core)项目
2. 添加对主系统的引用，通常包括：
   - ItAssetsSystem.Core.Abstractions (包含IPlugin接口)
   - ItAssetsSystem.Core.Events (包含IEventBus接口)
   - Microsoft.Extensions.DependencyInjection
   - Microsoft.Extensions.Logging

### 3.2 项目结构

推荐的项目结构：

```
MyPlugin/
├── MyPlugin.cs                 # 插件主类
├── Services/                   # 服务实现
│   ├── IMyService.cs           # 服务接口
│   └── MyService.cs            # 服务实现
├── Events/                     # 事件定义
│   └── MyEvents.cs             # 事件类
└── Models/                     # 数据模型
    └── MyModel.cs              # 模型类
```

### 3.3 插件配置

外部插件可以通过配置文件定义配置：

```json
{
  "Plugins": {
    "MyPlugin": {
      "Setting1": "Value1",
      "Setting2": 123
    }
  }
}
```

在插件中读取配置：

```csharp
var configuration = ServiceProvider.GetRequiredService<IConfiguration>();
var settings = configuration.GetSection("Plugins:MyPlugin").Get<MyPluginSettings>();
```

### 3.4 编译和部署

1. 编译插件项目，生成DLL文件
2. 将DLL文件复制到系统的Plugins目录下
3. 通过系统的插件管理接口加载插件

## 4. 事件驱动开发

### 4.1 事件总线

系统使用事件总线实现模块间通信：

```csharp
public interface IEventBus
{
    void Publish<TEvent>(TEvent eventData) where TEvent : class;
    void Subscribe<TEvent>(Action<TEvent> handler) where TEvent : class;
    void Unsubscribe<TEvent>(Action<TEvent> handler) where TEvent : class;
}
```

### 4.2 发布事件

```csharp
var eventBus = ServiceProvider.GetRequiredService<IEventBus>();
eventBus.Publish(new MyEvent { Data = "Hello" });
```

### 4.3 订阅事件

```csharp
protected override void OnStart()
{
    var eventBus = ServiceProvider.GetRequiredService<IEventBus>();
    eventBus.Subscribe<MyEvent>(HandleMyEvent);
}

private void HandleMyEvent(MyEvent @event)
{
    Logger.LogInformation($"收到事件: {@event.Data}");
}
```

### 4.4 取消订阅

```csharp
protected override void OnStop()
{
    var eventBus = ServiceProvider.GetRequiredService<IEventBus>();
    eventBus.Unsubscribe<MyEvent>(HandleMyEvent);
}
```

## 5. 插件管理API

系统提供REST API用于管理插件：

### 5.1 获取所有插件

```
GET /api/plugin
```

### 5.2 获取特定插件

```
GET /api/plugin/{id}
```

### 5.3 启动插件

```
POST /api/plugin/{id}/start
```

### 5.4 停止插件

```
POST /api/plugin/{id}/stop
```

### 5.5 卸载插件

```
DELETE /api/plugin/{id}
```

### 5.6 上传并安装插件

```
POST /api/plugin/upload
Content-Type: multipart/form-data
```

## 6. 最佳实践

### 6.1 插件设计原则

1. **单一职责**: 每个插件应专注于一个特定业务领域
2. **接口分离**: 提供清晰的服务接口，隐藏实现细节
3. **事件驱动**: 使用事件通信，避免直接依赖其他插件
4. **版本管理**: 遵循语义化版本规范
5. **异常处理**: 捕获并妥善处理异常，避免影响系统稳定性
6. **资源释放**: 在`Stop()`方法中释放所有资源

### 6.2 命名规范

1. 插件名称: 使用有意义的名称，通常为功能名称
2. 接口: 以"I"开头，如`IUserService`
3. 实现类: 直接使用接口名称去掉"I"，如`UserService`
4. 事件: 使用动词过去式加名词，如`UserCreatedEvent`

### 6.3 文档注释

为插件接口和主要类添加XML文档注释：

```csharp
/// <summary>
/// 用户服务接口
/// </summary>
public interface IUserService
{
    /// <summary>
    /// 创建用户
    /// </summary>
    /// <param name="username">用户名</param>
    /// <param name="password">密码</param>
    /// <returns>用户ID</returns>
    int CreateUser(string username, string password);
}
```

## 7. 故障排除

### 7.1 常见问题

1. **插件加载失败**: 检查DLL文件是否正确，依赖项是否齐全
2. **插件启动失败**: 检查服务注册，可能缺少必要的依赖项
3. **事件处理失败**: 检查事件订阅和处理逻辑
4. **资源释放不完全**: 确保在`Stop()`方法中释放所有资源

### 7.2 调试技巧

1. 检查日志文件，查找错误信息
2. 使用调试版本的插件，包含完整的调试信息
3. 通过API检查插件状态
4. 实现详细的日志记录，跟踪插件行为

## 8. 参考资源

1. [.NET Core依赖注入](https://docs.microsoft.com/zh-cn/aspnet/core/fundamentals/dependency-injection)
2. [.NET Core日志记录](https://docs.microsoft.com/zh-cn/aspnet/core/fundamentals/logging)
3. [程序集加载上下文](https://docs.microsoft.com/zh-cn/dotnet/api/system.runtime.loader.assemblyloadcontext)
4. [事件驱动编程](https://docs.microsoft.com/zh-cn/dotnet/standard/events/) 