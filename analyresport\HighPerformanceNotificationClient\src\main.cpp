// 主程序入口
#include "core/notification_client.h"
#include "utils/logger.h"
#include "common/constants.h"
#include <windows.h>
#include <iostream>
#include <csignal>
#include <memory>

using namespace notification;

// 全局客户端实例
std::unique_ptr<NotificationClient> g_client;

// 信号处理函数
BOOL WINAPI ConsoleHandler(DWORD dwType) {
    switch (dwType) {
        case CTRL_C_EVENT:
        case CTRL_CLOSE_EVENT:
        case CTRL_BREAK_EVENT:
        case CTRL_LOGOFF_EVENT:
        case CTRL_SHUTDOWN_EVENT:
            std::cout << "\n正在退出应用程序..." << std::endl;
            if (g_client) {
                g_client->stop();
            }
            return TRUE;
        default:
            return FALSE;
    }
}

// 异常处理
LONG WINAPI UnhandledExceptionFilter(EXCEPTION_POINTERS* exception_pointers) {
    // 记录崩溃信息
    std::cerr << "应用程序发生未处理的异常！" << std::endl;
    
    // 生成崩溃转储（简化版）
    char crash_file[MAX_PATH];
    GetModuleFileNameA(nullptr, crash_file, MAX_PATH);
    strcat_s(crash_file, ".crash.log");
    
    FILE* file = nullptr;
    if (fopen_s(&file, crash_file, "w") == 0 && file) {
        fprintf(file, "Crash at: %p\n", exception_pointers->ExceptionRecord->ExceptionAddress);
        fprintf(file, "Exception Code: 0x%08X\n", exception_pointers->ExceptionRecord->ExceptionCode);
        fclose(file);
    }
    
    return EXCEPTION_EXECUTE_HANDLER;
}

// 主函数
int main(int argc, char* argv[]) {
    // 设置控制台标题
    SetConsoleTitleA("高性能实时通知客户端");
    
    // 设置异常处理
    SetUnhandledExceptionFilter(UnhandledExceptionFilter);
    SetConsoleCtrlHandler(ConsoleHandler, TRUE);
    
    // 解析命令行参数
    bool debug_mode = false;
    String config_file = constants::CONFIG_FILE_PATH;
    
    for (int i = 1; i < argc; ++i) {
        String arg = argv[i];
        if (arg == "--debug" || arg == "-d") {
            debug_mode = true;
        } else if (arg == "--config" || arg == "-c") {
            if (i + 1 < argc) {
                config_file = argv[++i];
            } else {
                std::cerr << "错误: --config 参数需要指定配置文件路径" << std::endl;
                return 1;
            }
        } else if (arg == "--help" || arg == "-h") {
            std::cout << "高性能实时通知客户端 " << constants::strings::APP_VERSION << std::endl;
            std::cout << constants::strings::APP_DESCRIPTION << std::endl;
            std::cout << std::endl;
            std::cout << "用法: " << argv[0] << " [选项]" << std::endl;
            std::cout << "选项:" << std::endl;
            std::cout << "  -h, --help              显示此帮助信息" << std::endl;
            std::cout << "  -d, --debug             启用调试模式" << std::endl;
            std::cout << "  -c, --config <文件>     指定配置文件路径" << std::endl;
            std::cout << "  --version               显示版本信息" << std::endl;
            return 0;
        } else if (arg == "--version") {
            std::cout << constants::strings::APP_VERSION << std::endl;
            return 0;
        }
    }
    
    // 显示启动信息
    std::cout << "===================================" << std::endl;
    std::cout << constants::strings::APP_NAME << std::endl;
    std::cout << "版本: " << constants::strings::APP_VERSION << std::endl;
    std::cout << "协议: UDP + WebSocket" << std::endl;
    std::cout << "目标延迟: < 0.5ms (UDP)" << std::endl;
    if (debug_mode) {
        std::cout << "调试模式: 已启用" << std::endl;
    }
    std::cout << "配置文件: " << config_file << std::endl;
    std::cout << "===================================" << std::endl;
    
    try {
        // 创建客户端实例
        g_client = std::make_unique<NotificationClient>();
        
        // 设置回调函数
        g_client->setNotificationCallback([](const NotificationMessage& message) {
            std::cout << "[通知] " << message.title << ": " << message.content << std::endl;
        });
        
        g_client->setConnectionStatusCallback([](ConnectionStatus status, ProtocolType protocol) {
            String protocol_name = (protocol == ProtocolType::UDP) ? "UDP" : "WebSocket";
            switch (status) {
                case ConnectionStatus::CONNECTING:
                    std::cout << "[连接] 正在连接到服务器 (" << protocol_name << ")..." << std::endl;
                    break;
                case ConnectionStatus::CONNECTED:
                    std::cout << "[连接] 已连接到服务器 (" << protocol_name << ")" << std::endl;
                    break;
                case ConnectionStatus::DISCONNECTED:
                    std::cout << "[连接] 与服务器断开连接" << std::endl;
                    break;
                case ConnectionStatus::RECONNECTING:
                    std::cout << "[连接] 正在重新连接..." << std::endl;
                    break;
                case ConnectionStatus::FAILED:
                    std::cout << "[连接] 连接失败" << std::endl;
                    break;
            }
        });
        
        g_client->setAuthStatusCallback([](AuthStatus status) {
            switch (status) {
                case AuthStatus::AUTHENTICATING:
                    std::cout << "[认证] 正在进行用户认证..." << std::endl;
                    break;
                case AuthStatus::AUTHENTICATED:
                    std::cout << "[认证] 用户认证成功" << std::endl;
                    break;
                case AuthStatus::AUTH_FAILED:
                    std::cout << "[认证] 用户认证失败" << std::endl;
                    break;
                case AuthStatus::TOKEN_EXPIRED:
                    std::cout << "[认证] 令牌已过期，正在刷新..." << std::endl;
                    break;
                case AuthStatus::NOT_AUTHENTICATED:
                    std::cout << "[认证] 未认证状态" << std::endl;
                    break;
            }
        });
        
        g_client->setErrorCallback([](const String& error) {
            std::cerr << "[错误] " << error << std::endl;
        });
        
        if (debug_mode) {
            g_client->setStatisticsCallback([](const Statistics& stats) {
                std::cout << "[统计] 收到消息: " << stats.messages_received 
                          << ", 发送消息: " << stats.messages_sent
                          << ", 延迟: " << stats.average_latency.count() << "ms" << std::endl;
            });
        }
        
        // 初始化客户端
        if (!g_client->initialize(config_file)) {
            std::cerr << "客户端初始化失败" << std::endl;
            return 1;
        }
        
        // 启动客户端
        if (!g_client->start()) {
            std::cerr << "客户端启动失败" << std::endl;
            return 1;
        }
        
        std::cout << "客户端启动成功，按 Ctrl+C 退出..." << std::endl;
        
        // 主事件循环
        MSG msg;
        while (g_client->isRunning()) {
            // 处理Windows消息
            while (PeekMessage(&msg, nullptr, 0, 0, PM_REMOVE)) {
                TranslateMessage(&msg);
                DispatchMessage(&msg);
                
                if (msg.message == WM_QUIT) {
                    g_client->stop();
                    break;
                }
            }
            
            // 让出CPU时间
            Sleep(10);
        }
        
    } catch (const std::exception& e) {
        std::cerr << "应用程序异常: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << "应用程序发生未知异常" << std::endl;
        return 1;
    }
    
    // 清理资源
    if (g_client) {
        g_client->stop();
        g_client.reset();
    }
    
    std::cout << "应用程序已退出" << std::endl;
    return 0;
}