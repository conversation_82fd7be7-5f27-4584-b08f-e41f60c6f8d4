// IT资产管理系统 - 用户角色关联实体
// 文件路径: /Models/Entities/UserRole.cs
// 功能: 定义用户角色关联表实体

#nullable enable

// using System; // No longer needed if CreatedAt/UpdatedAt removed
// using ItAssetsSystem.Models; // No longer needed if IAuditableEntity removed

namespace ItAssetsSystem.Models.Entities
{
    /// <summary>
    /// 用户角色关联实体
    /// </summary>
    // public class UserRole : IAuditableEntity // Removed IAuditableEntity
    public class UserRole
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// 角色ID
        /// </summary>
        public int RoleId { get; set; }

        // Removed CreatedAt and UpdatedAt properties
        // /// <summary>
        // /// 创建时间
        // /// </summary>
        // public DateTime CreatedAt { get; set; }

        // /// <summary>
        // /// 更新时间
        // /// </summary>
        // public DateTime UpdatedAt { get; set; }

        /// <summary>
        /// 用户导航属性
        /// </summary>
        public virtual User User { get; set; } = null!;

        /// <summary>
        /// 角色导航属性
        /// </summary>
        public virtual Role Role { get; set; } = null!;
    }
} 