// File: Infrastructure/Data/Repositories/SparePartRepository.cs
// Description: 备品备件仓储实现类

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ItAssetsSystem.Application.Features.SpareParts.Dtos;
using ItAssetsSystem.Core.Abstractions;
using ItAssetsSystem.Domain.Entities;
using ItAssetsSystem.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;

namespace ItAssetsSystem.Infrastructure.Data.Repositories
{
    /// <summary>
    /// 备品备件仓储实现类
    /// </summary>
    public class SparePartRepository : ISparePartRepository
    {
        private readonly AppDbContext _dbContext;
        
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="dbContext">数据库上下文</param>
        public SparePartRepository(AppDbContext dbContext)
        {
            _dbContext = dbContext;
        }
        
        /// <summary>
        /// 获取备品备件分页列表
        /// </summary>
        /// <param name="query">查询参数</param>
        /// <returns>备件列表和总数</returns>
        public async Task<(List<SparePart>, int)> GetSparePartsPagedAsync(SparePartQuery query)
        {
            var sparePartsQuery = _dbContext.SpareParts
                .Include(p => p.Type)
                .Include(p => p.Location)
                .AsQueryable();
            
            // 应用筛选条件
            if (!string.IsNullOrEmpty(query.Name))
            {
                sparePartsQuery = sparePartsQuery.Where(p => p.Name.Contains(query.Name));
            }

            if (!string.IsNullOrEmpty(query.Code))
            {
                sparePartsQuery = sparePartsQuery.Where(p => p.Code.Contains(query.Code));
            }

            // 如果 Name 和 Code 为空，但 Keyword 有值，则使用 Keyword 进行更广泛的搜索
            if (string.IsNullOrEmpty(query.Name) && string.IsNullOrEmpty(query.Code) && !string.IsNullOrEmpty(query.Keyword))
            {
                sparePartsQuery = sparePartsQuery.Where(p => 
                    p.Name.Contains(query.Keyword) || 
                    p.Code.Contains(query.Keyword) || 
                    p.Specification.Contains(query.Keyword) ||
                    p.Brand.Contains(query.Keyword));
            }
            
            if (query.TypeId.HasValue)
            {
                sparePartsQuery = sparePartsQuery.Where(p => p.TypeId == query.TypeId);
            }
            
            if (query.LocationId.HasValue)
            {
                sparePartsQuery = sparePartsQuery.Where(p => p.LocationId == query.LocationId);
            }
            
            if (!string.IsNullOrEmpty(query.LocationArea))
            {
                sparePartsQuery = sparePartsQuery.Where(p => p.Location.Area == query.LocationArea);
            }
            
            // 使用 StockStatus 进行库存状态筛选
            if (!string.IsNullOrEmpty(query.StockStatus))
            {
                switch (query.StockStatus.ToLower())
                {
                    case "normal": // 正常: 大于预警值
                        sparePartsQuery = sparePartsQuery.Where(p => p.StockQuantity > p.WarningThreshold);
                        break;
                    case "warning": // 预警: 小于等于预警值 但 大于最小库存
                        sparePartsQuery = sparePartsQuery.Where(p => p.StockQuantity <= p.WarningThreshold && p.StockQuantity > p.MinStock);
                        break;
                    case "danger": // 不足: 小于等于最小库存
                        sparePartsQuery = sparePartsQuery.Where(p => p.StockQuantity <= p.MinStock);
                        break;
                }
            }
            // 保留 LowStock 逻辑作为备选或特定场景，但优先使用 StockStatus
            else if (query.LowStock.HasValue) 
            {
                if (query.LowStock.Value)
                {
                    // 低于预警库存
                    sparePartsQuery = sparePartsQuery.Where(p => p.StockQuantity <= p.WarningThreshold);
                }
                else
                {
                    // 低于最小安全库存
                    sparePartsQuery = sparePartsQuery.Where(p => p.StockQuantity <= p.MinStock);
                }
            }
            
            // 应用排序
            if (!string.IsNullOrEmpty(query.SortBy))
            {
                var sortByLower = query.SortBy.ToLower();
                var sortDirectionLower = query.SortDirection?.ToLower() ?? "asc";

                // 前端可能传递 stockQuantity, 后端统一按 stock 处理（内部已是StockQuantity）
                if (sortByLower == "stockquantity") sortByLower = "stock";

                switch (sortByLower)
                {
                    case "code":
                        sparePartsQuery = sortDirectionLower == "desc" 
                            ? sparePartsQuery.OrderByDescending(p => p.Code)
                            : sparePartsQuery.OrderBy(p => p.Code);
                        break;
                    case "name":
                        sparePartsQuery = sortDirectionLower == "desc" 
                            ? sparePartsQuery.OrderByDescending(p => p.Name)
                            : sparePartsQuery.OrderBy(p => p.Name);
                        break;
                    case "stock": // 对应前端的 stockQuantity
                        sparePartsQuery = sortDirectionLower == "desc" 
                            ? sparePartsQuery.OrderByDescending(p => p.StockQuantity)
                            : sparePartsQuery.OrderBy(p => p.StockQuantity);
                        break;
                    case "creationtime":
                        sparePartsQuery = sortDirectionLower == "desc" 
                            ? sparePartsQuery.OrderByDescending(p => p.CreatedAt)
                            : sparePartsQuery.OrderBy(p => p.CreatedAt);
                        break;
                    default:
                        sparePartsQuery = sparePartsQuery.OrderBy(p => p.Id);
                        break;
                }
            }
            else
            {
                // 默认排序
                sparePartsQuery = sparePartsQuery.OrderBy(p => p.Id);
            }
            
            // 计算总记录数
            var totalCount = await sparePartsQuery.CountAsync();
            
            // 应用分页
            var spareParts = await sparePartsQuery
                .Skip((query.PageIndex - 1) * query.PageSize)
                .Take(query.PageSize)
                .ToListAsync();
            
            return (spareParts, totalCount);
        }
        
        /// <summary>
        /// 根据ID获取备品备件
        /// </summary>
        /// <param name="id">备件ID</param>
        /// <returns>备件实体</returns>
        public async Task<SparePart> GetByIdAsync(long id)
        {
            return await _dbContext.SpareParts
                .Include(p => p.Type)
                .Include(p => p.Location)
                .FirstOrDefaultAsync(p => p.Id == id);
        }

        /// <summary>
        /// 根据备件编码检查备件是否存在
        /// </summary>
        /// <param name="code">备件编码</param>
        /// <returns>如果存在则返回true，否则返回false</returns>
        public async Task<bool> ExistsByCodeAsync(string code)
        {
            return await _dbContext.SpareParts.AnyAsync(p => p.Code == code);
        }

        /// <summary>
        /// 根据备件编码查找备件实体
        /// </summary>
        /// <param name="code">备件编码</param>
        /// <returns>备件实体，如果不存在则返回null</returns>
        public async Task<SparePart> FindByCodeAsync(string code)
        {
            return await _dbContext.SpareParts
                .Include(p => p.Type)
                .Include(p => p.Location)
                .FirstOrDefaultAsync(p => p.Code == code);
        }
        
        /// <summary>
        /// 创建备品备件
        /// </summary>
        /// <param name="entity">备件实体</param>
        /// <returns>创建的备件实体</returns>
        public async Task<SparePart> CreateAsync(SparePart entity)
        {
            entity.CreatedAt = DateTime.Now;
            entity.UpdatedAt = DateTime.Now;
            
            await _dbContext.SpareParts.AddAsync(entity);
            await _dbContext.SaveChangesAsync();
            
            return entity;
        }
        
        /// <summary>
        /// 更新备品备件
        /// </summary>
        /// <param name="entity">备件实体</param>
        /// <returns>更新后的备件实体</returns>
        public async Task<SparePart> UpdateAsync(SparePart entity)
        {
            entity.UpdatedAt = DateTime.Now;
            
            var existingEntity = await _dbContext.SpareParts.FindAsync(entity.Id);
            if (existingEntity == null)
            {
                return null;
            }
            
            // 只更新需要的属性，避免覆盖其他属性
            _dbContext.Entry(existingEntity).CurrentValues.SetValues(entity);
            
            await _dbContext.SaveChangesAsync();
            
            return existingEntity;
        }
        
        /// <summary>
        /// 删除备品备件
        /// </summary>
        /// <param name="id">备件ID</param>
        /// <returns>是否成功</returns>
        public async Task<bool> DeleteAsync(long id)
        {
            var entity = await _dbContext.SpareParts.FindAsync(id);
            if (entity == null)
            {
                return false;
            }
            
            // 检查是否有出入库记录
            var hasTransactions = await _dbContext.SparePartTransactions
                .AnyAsync(t => t.PartId == id);
            
            if (hasTransactions)
            {
                // 如果有出入库记录，则不允许删除，或者可以抛出异常提示用户
                throw new InvalidOperationException("无法删除有关联出入库记录的备件。");
            }
            
            // 如果没有出入库记录，则物理删除
            _dbContext.SpareParts.Remove(entity);
            await _dbContext.SaveChangesAsync();
            
            return true;
        }
        
        /// <summary>
        /// 获取库存预警列表
        /// </summary>
        /// <param name="onlyMinStock">是否只获取低于最小安全库存的备件</param>
        /// <returns>备件列表</returns>
        public async Task<List<SparePart>> GetLowStockSparePartsAsync(bool onlyMinStock)
        {
            IQueryable<SparePart> query = _dbContext.SpareParts
                .Include(p => p.Type)
                .Include(p => p.Location);
            
            if (onlyMinStock)
            {
                // 低于最小安全库存
                query = query.Where(p => p.StockQuantity <= p.MinStock);
            }
            else
            {
                // 低于预警库存
                query = query.Where(p => p.StockQuantity <= p.WarningThreshold);
            }
            
            return await query.ToListAsync();
        }
        
        /// <summary>
        /// 更新库存数量
        /// </summary>
        /// <param name="id">备件ID</param>
        /// <param name="changeQuantity">变更数量(正数增加,负数减少)</param>
        /// <param name="transaction">出入库记录</param>
        /// <returns>是否成功</returns>
        public async Task<bool> UpdateStockAsync(long id, int changeQuantity, SparePartTransaction transaction)
        {
            using var dbTransaction = await _dbContext.Database.BeginTransactionAsync();
            
            try
            {
                // 获取备件
                var sparePart = await _dbContext.SpareParts.FindAsync(id);
                if (sparePart == null)
                {
                    return false;
                }
                
                // 更新库存
                sparePart.UpdateStock(changeQuantity);
                
                // 设置交易记录中的操作后库存
                transaction.StockAfter = sparePart.StockQuantity;
                transaction.OperationTime = DateTime.Now;
                
                // 保存交易记录
                await _dbContext.SparePartTransactions.AddAsync(transaction);
                
                // 保存所有更改
                await _dbContext.SaveChangesAsync();
                
                // 提交事务
                await dbTransaction.CommitAsync();
                
                return true;
            }
            catch
            {
                // 回滚事务
                await dbTransaction.RollbackAsync();
                throw;
            }
        }
    }
} 