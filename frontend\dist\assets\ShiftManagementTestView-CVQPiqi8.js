import{_ as t,ad as s,b as i,d as a,e,$ as r,w as c,t as n,a as l,o as f,p as o,E as h}from"./index-CG5lHOPO.js";import{w as u}from"./workShift-Ce3ThpoM.js";const d={class:"test-container"},S={class:"test-section"},y={key:0},g={class:"test-section"},k={key:0},m={class:"test-section"},p={key:0},_={class:"test-section"},v={key:0},w={class:"test-section"},T={key:0},C=t({__name:"ShiftManagementTestView",setup(t){const C=s({shifts:!1,currentShift:!1,statistics:!1,tasks:!1,createShift:!1}),b=s({shifts:null,currentShift:null,statistics:null,tasks:null,createShift:null}),N=async()=>{C.shifts=!0;try{const t=await u.getAllShifts();b.shifts=t,h.success("获取班次成功")}catch(t){b.shifts={error:t.message},h.error("获取班次失败")}finally{C.shifts=!1}},O=async()=>{C.currentShift=!0;try{const t=await u.getUserCurrentShift();b.currentShift=t,h.success("获取当前班次成功")}catch(t){b.currentShift={error:t.message},h.error("获取当前班次失败")}finally{C.currentShift=!1}},J=async()=>{C.statistics=!0;try{const t=await u.getTodayShiftStatistics();b.statistics=t,h.success("获取统计成功")}catch(t){b.statistics={error:t.message},h.error("获取统计失败")}finally{C.statistics=!1}},A=async()=>{C.tasks=!0;try{const t=await u.getAvailableTasks();b.tasks=t,h.success("获取任务成功")}catch(t){b.tasks={error:t.message},h.error("获取任务失败")}finally{C.tasks=!1}},j=async()=>{C.createShift=!0;try{const t={shiftName:"测试班次",shiftCode:"TEST",shiftType:"Day",startTime:"09:00:00",endTime:"17:00:00",taskClaimTime:"09:00:00",isOvernight:!1,description:"这是一个测试班次"},s=await u.createShift(t);b.createShift=s,h.success("创建班次成功")}catch(t){b.createShift={error:t.message},h.error("创建班次失败")}finally{C.createShift=!1}};return(t,s)=>{const h=l("el-button");return f(),i("div",d,[s[10]||(s[10]=a("h2",null,"班次管理API测试页面",-1)),a("div",S,[s[1]||(s[1]=a("h3",null,"1. 获取所有班次",-1)),e(h,{onClick:N,loading:C.shifts},{default:c((()=>s[0]||(s[0]=[o("测试获取班次")]))),_:1},8,["loading"]),b.shifts?(f(),i("pre",y,n(JSON.stringify(b.shifts,null,2)),1)):r("",!0)]),a("div",g,[s[3]||(s[3]=a("h3",null,"2. 获取用户当前班次",-1)),e(h,{onClick:O,loading:C.currentShift},{default:c((()=>s[2]||(s[2]=[o("测试获取当前班次")]))),_:1},8,["loading"]),b.currentShift?(f(),i("pre",k,n(JSON.stringify(b.currentShift,null,2)),1)):r("",!0)]),a("div",m,[s[5]||(s[5]=a("h3",null,"3. 获取今日班次统计",-1)),e(h,{onClick:J,loading:C.statistics},{default:c((()=>s[4]||(s[4]=[o("测试获取统计")]))),_:1},8,["loading"]),b.statistics?(f(),i("pre",p,n(JSON.stringify(b.statistics,null,2)),1)):r("",!0)]),a("div",_,[s[7]||(s[7]=a("h3",null,"4. 获取可领取任务",-1)),e(h,{onClick:A,loading:C.tasks},{default:c((()=>s[6]||(s[6]=[o("测试获取任务")]))),_:1},8,["loading"]),b.tasks?(f(),i("pre",v,n(JSON.stringify(b.tasks,null,2)),1)):r("",!0)]),a("div",w,[s[9]||(s[9]=a("h3",null,"5. 创建测试班次",-1)),e(h,{onClick:j,loading:C.createShift},{default:c((()=>s[8]||(s[8]=[o("创建测试班次")]))),_:1},8,["loading"]),b.createShift?(f(),i("pre",T,n(JSON.stringify(b.createShift,null,2)),1)):r("",!0)])])}}},[["__scopeId","data-v-5bdbeb4f"]]);export{C as default};
