# IT资产管理系统API文档（完整版）

API文档规范 v2.4
最后校验时间：2024-03-25
代码版本：master

## 更新记录

### v2.4 (2024-03-25)
- 添加资产管理核心API
  - GET /api/Asset/Search - 高级搜索资产
  - GET /api/Asset/Status/{statusId} - 按状态筛选资产
  - GET /api/Asset/Reports/Summary - 获取资产摘要报告
  - POST /api/Asset/Batch/Move - 批量移动资产位置
  - POST /api/Asset/Batch/ChangeStatus - 批量更改资产状态
- 添加用户与人员管理API
  - GET /api/Personnel - 获取人员列表
  - GET /api/Personnel/{id} - 获取人员详情
  - POST /api/Personnel - 添加人员信息
  - PUT /api/Personnel/{id} - 更新人员信息
  - DELETE /api/Personnel/{id} - 删除人员
- 添加部门管理API
  - GET /api/Department/Tree - 获取部门树形结构
- 添加任务管理API
  - GET /api/Task/My - 获取我的任务
  - GET /api/Task/Periodic - 获取周期性任务
  - GET /api/Task/PDCA - 获取PDCA计划任务
- 添加插件管理API
  - GET /api/Plugin - 获取所有插件列表
  - GET /api/Plugin/{id} - 获取插件详情
  - POST /api/Plugin/Install - 安装插件
  - DELETE /api/Plugin/{id} - 卸载插件
  - PUT /api/Plugin/{id}/Enable - 启用插件
  - PUT /api/Plugin/{id}/Disable - 禁用插件
  - GET /api/Plugin/Settings/{pluginId} - 获取插件设置
  - PUT /api/Plugin/Settings/{pluginId} - 更新插件设置
- 添加离线操作管理API
  - GET /api/OfflineOperation - 获取离线操作队列
  - POST /api/OfflineOperation/Sync - 同步离线操作
  - DELETE /api/OfflineOperation/{id} - 删除离线操作记录
  - GET /api/OfflineOperation/Status - 获取同步状态
  - POST /api/OfflineOperation/ResolveConflict - 解决同步冲突
- 添加认证与授权API
  - POST /api/Auth/Token - 获取认证令牌
  - POST /api/Auth/Refresh - 刷新令牌
  - GET /api/Auth/Validate - 验证令牌
  - POST /api/Auth/Revoke - 撤销令牌
- 添加健康检查API
  - GET /api/Health - 获取系统健康状态
  - GET /api/Health/Database - 检查数据库连接
  - GET /api/Health/Services - 检查服务状态
- 添加资产类型管理API
  - GET /api/AssetType/Tree - 获取资产类型树形结构

### v2.3 (2024-03-24)
- 新增数据导入导出功能
  - GET /api/import/formats - 获取支持的导入格式
  - GET /api/import/template/{entityType}/{formatStr} - 获取导入模板
  - POST /api/import/process - 处理数据导入
  - GET /api/import/result/{importId} - 获取导入结果
- 增强位置管理API
  - 支持部门和负责人关联
  - 支持多用户关联
  - 新增位置树形结构查询
  - 新增位置资产关联管理
- 新增资产历史记录API
  - GET /api/Asset/{id}/history - 获取指定资产的历史记录
  - GET /api/Location/{id}/history - 获取指定位置的资产历史记录

### v2.2 (2024-03-20)
- 初始版本发布
- 实现基础CRUD功能
- 支持资产、用户、任务等核心模块

## 全局配置

```diff
# Startup.cs 关键配置
+ 日志方案：Serilog（ConfigureServices@L77-L89）
+ 数据库连接：MySQL（ConfigureServices@L59-L104）
+ 插件系统：注册多个内置插件（ConfigureServices@L107-113）
- 未配置身份认证与授权系统
- 未配置全局速率限制
! 异常处理：使用中间件实现（Configure@L162-L195）
```

## 安全问题和修复建议

经过系统审查，发现API存在以下安全隐患和改进空间：

### 1. 参数校验一致性问题（通过率仅15%）

#### 问题描述
- 大多数API接口缺少请求模型验证特性（如`[Required]`, `[MaxLength]`等）
- 控制器中验证逻辑分散且不一致，部分接口采用手动验证方式
- 验证错误信息格式不统一，影响前端异常处理

#### 修复方案
1. 为所有请求模型添加数据注解验证特性：
```csharp
public class ChangeLocationModel
{
    [Required(ErrorMessage = "新位置ID不能为空")]
    [Range(1, int.MaxValue, ErrorMessage = "新位置ID必须大于0")]
    public int NewLocationId { get; set; }
    
    [Required(ErrorMessage = "变更原因不能为空")]
    [StringLength(200, ErrorMessage = "变更原因最大长度为200个字符")]
    public string Reason { get; set; }
    
    [StringLength(500, ErrorMessage = "备注最大长度为500个字符")]
    public string Notes { get; set; }
}
```

2. 在`Startup.cs`中添加全局模型验证过滤器：
```csharp
services.AddControllers(options =>
{
    options.Filters.Add(new ModelStateValidationFilter());
})
```

3. 创建统一的验证响应格式处理器：
```csharp
public class ModelStateValidationFilter : ActionFilterAttribute
{
    public override void OnActionExecuting(ActionExecutingContext context)
    {
        if (!context.ModelState.IsValid)
        {
            var errors = context.ModelState
                .Where(e => e.Value.Errors.Count > 0)
                .Select(e => new 
                {
                    field = e.Key,
                    message = e.Value.Errors.First().ErrorMessage
                }).ToArray();
                
            context.Result = new BadRequestObjectResult(new 
            {
                success = false,
                errors = errors,
                message = "请求参数验证失败"
            });
        }
    }
}
```

### 2. 缺少身份认证和授权管理

#### 问题描述
- 未实现JWT身份验证机制
- 缺少授权特性（如`[Authorize]`）
- 缺少基于角色/权限的访问控制
- API接口暴露不受保护

#### 修复方案
1. 添加JWT身份验证服务（在`Startup.cs`的`ConfigureServices`方法中）：
```csharp
// 添加JWT配置
var jwtSettings = Configuration.GetSection("JwtSettings");
var secretKey = Encoding.UTF8.GetBytes(jwtSettings["SecretKey"]);

services.AddAuthentication(options =>
{
    options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
})
.AddJwtBearer(options =>
{
    options.TokenValidationParameters = new TokenValidationParameters
    {
        ValidateIssuer = true,
        ValidateAudience = true,
        ValidateLifetime = true,
        ValidateIssuerSigningKey = true,
        ValidIssuer = jwtSettings["Issuer"],
        ValidAudience = jwtSettings["Audience"],
        IssuerSigningKey = new SymmetricSecurityKey(secretKey)
    };
});
```

2. 在请求管道中添加身份验证中间件（在`Startup.cs`的`Configure`方法中）：
```csharp
app.UseAuthentication();
app.UseAuthorization();
```

3. 在控制器或行为方法上添加授权特性：
```csharp
[Authorize]
public class AssetController : ControllerBase
{
    [Authorize(Policy = "asset:create")]
    [HttpPost]
    public async Task<IActionResult> Create([FromBody] Asset asset)
    {
        // 实现逻辑
    }
}
```

4. 实现授权策略：
```csharp
services.AddAuthorization(options =>
{
    // 资产管理权限
    options.AddPolicy("asset:view", policy => policy.RequireClaim("permission", "asset:view"));
    options.AddPolicy("asset:create", policy => policy.RequireClaim("permission", "asset:create"));
    options.AddPolicy("asset:update", policy => policy.RequireClaim("permission", "asset:update"));
    options.AddPolicy("asset:delete", policy => policy.RequireClaim("permission", "asset:delete"));
    
    // 位置管理权限
    options.AddPolicy("location:view", policy => policy.RequireClaim("permission", "location:view"));
    // 其他权限...
});
```

### 3. 缺少安全HTTP头配置

#### 问题描述
- 未设置关键安全HTTP头
- 缺少跨域资源共享(CORS)策略
- 未实现内容安全策略(CSP)
- 缺少XSS防护

#### 修复方案
1. 添加安全HTTP头中间件（在`Startup.cs`的`Configure`方法中）：
```csharp
// 安装NWebsec.AspNetCore.Middleware包
// Install-Package NWebsec.AspNetCore.Middleware

// 添加安全头部
app.UseXContentTypeOptions();
app.UseXXssProtection(options => options.EnabledWithBlockMode());
app.UseXfo(options => options.Deny());
app.UseReferrerPolicy(opts => opts.NoReferrer());
app.UseCsp(options => options
    .DefaultSources(s => s.Self())
    .ScriptSources(s => s.Self().UnsafeInline())
    .StyleSources(s => s.Self().UnsafeInline())
    .FontSources(s => s.Self())
    .ImageSources(s => s.Self().CustomSources("data:"))
    .FrameSources(s => s.Self())
);
```

2. 添加CORS策略：
```csharp
// 在ConfigureServices中
services.AddCors(options =>
{
    options.AddPolicy("ApiCorsPolicy", builder =>
    {
        builder
            .WithOrigins("http://localhost:8080") // 允许的前端源
            .AllowAnyMethod()
            .AllowAnyHeader()
            .AllowCredentials();
    });
});

// 在Configure中
app.UseCors("ApiCorsPolicy");
```

### 4. 请求模型缺少数据验证特性

#### 问题描述
- 请求模型类（如Asset, User等）缺少数据验证特性
- 缺少业务规则验证
- 验证逻辑分散在控制器中

#### 修复方案
1. 为模型类添加验证特性：
```csharp
public class Asset
{
    public int Id { get; set; }

    [Required(ErrorMessage = "资产名称不能为空")]
    [StringLength(100, ErrorMessage = "资产名称最大长度为100个字符")]
    public string Name { get; set; }

    [Required(ErrorMessage = "资产类型ID不能为空")]
    [Range(1, int.MaxValue, ErrorMessage = "资产类型ID必须大于0")]
    public int AssetTypeId { get; set; }

    [StringLength(50, ErrorMessage = "序列号最大长度为50个字符")]
    public string SerialNumber { get; set; }

    // 其他属性验证...
}
```

2. 使用FluentValidation实现更复杂的验证规则：
```csharp
// 安装FluentValidation.AspNetCore包
// Install-Package FluentValidation.AspNetCore

// 在Startup.cs中配置
services.AddControllers()
    .AddFluentValidation(fv => {
        fv.RegisterValidatorsFromAssemblyContaining<AssetValidator>();
    });

// 创建验证器类
public class AssetValidator : AbstractValidator<Asset>
{
    public AssetValidator(AppDbContext dbContext)
    {
        RuleFor(x => x.Name).NotEmpty().WithMessage("资产名称不能为空");
        RuleFor(x => x.AssetTypeId).GreaterThan(0).WithMessage("资产类型ID必须大于0");
        
        // 资产编码唯一性验证
        RuleFor(x => x.AssetCode)
            .MustAsync(async (code, cancellation) => {
                if (string.IsNullOrEmpty(code)) return true;
                return !await dbContext.Assets.AnyAsync(a => a.AssetCode == code);
            })
            .WithMessage("资产编码已存在");
            
        // 条件验证
        When(x => x.Status == 1, () => {
            RuleFor(x => x.LocationId).NotNull().WithMessage("在用状态的资产必须指定位置");
        });
    }
}
```

### 5. 缺少API版本控制

#### 问题描述
- API未实现版本控制
- 缺少API版本策略和兼容性保证
- 无法实现平滑升级

#### 修复方案
1. 添加API版本控制（在`Startup.cs`的`ConfigureServices`方法中）：
```csharp
// 安装Microsoft.AspNetCore.Mvc.Versioning包
// Install-Package Microsoft.AspNetCore.Mvc.Versioning

services.AddApiVersioning(options =>
{
    options.DefaultApiVersion = new ApiVersion(1, 0);
    options.AssumeDefaultVersionWhenUnspecified = true;
    options.ReportApiVersions = true;
    options.ApiVersionReader = ApiVersionReader.Combine(
        new UrlSegmentApiVersionReader(),
        new HeaderApiVersionReader("X-Api-Version")
    );
});

services.AddVersionedApiExplorer(options =>
{
    options.GroupNameFormat = "'v'VVV";
    options.SubstituteApiVersionInUrl = true;
});
```

2. 在控制器上应用版本特性：
```csharp
[ApiVersion("1.0")]
[Route("api/v{version:apiVersion}/assets")]
public class AssetController : ControllerBase
{
    // 实现...
}

[ApiVersion("2.0")]
[Route("api/v{version:apiVersion}/assets")]
public class AssetV2Controller : ControllerBase
{
    // 实现...
}
```

### 综合修复建议

1. 创建安全优化分支，逐步实施以上修复建议
2. 添加API端点安全审计日志
3. 实现请求限流保护机制
4. 添加敏感数据加密存储
5. 实现API钩子机制，便于扩展安全策略
6. 考虑引入OpenID Connect实现更强的认证机制
7. 添加CSRF防护
8. 实施API访问审计记录

## 资产管理模块

▍模块状态：基本实现（需求覆盖率 85%）
▍代码锚点：/Controllers/AssetController.cs

### 1. 获取所有资产 [GET]
▍代码定位：AssetController.cs Line 37-149

```csharp
[HttpGet]
public async Task<IActionResult> GetAll(
    [FromQuery] int pageIndex = 1,
    [FromQuery] int pageSize = 100,
    [FromQuery] string assetCode = null,
    [FromQuery] string name = null,
    [FromQuery] int? assetTypeId = null,
    [FromQuery] int? status = null,
    [FromQuery] int? productionLineId = null,
    [FromQuery] int? processId = null,
    [FromQuery] int? workstationId = null,
    [FromQuery] int? locationId = null,
    [FromQuery] int? departmentId = null)
```

▍端点路径：/api/Asset
▍方法类型：GET

‖ 请求参数 ‖
- pageIndex: 页码，从1开始（默认为1）
- pageSize: 每页大小，默认100，最大1000
- assetCode: 资产编号（可选）
- name: 资产名称（可选）
- assetTypeId: 资产类型ID（可选）
- status: 状态（可选）
- locationId: 位置ID（可选）
- departmentId: 部门ID（可选）
- productionLineId: 产线ID（可选）
- processId: 工序ID（可选）
- workstationId: 工位ID（可选）

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": [
    {
      "id": 1,
      "assetCode": "IT-PC-001",
      "name": "办公电脑",
      "assetTypeId": 1,
      "assetTypeName": "电脑设备",
      "serialNumber": "XPS15-9570-001",
      "model": "XPS 15",
      "brand": "Dell",
      "purchaseDate": "2022-03-24T04:04:30.8353554+08:00",
      "warrantyExpireDate": "2025-03-24T04:04:30.8354074+08:00",
      "price": 8999.00,
      "locationId": 1,
      "locationName": "研发部",
      "locationPath": "0,1",
      "locationFullName": "总部/研发部",
      "status": 1,
      "statusName": "在用",
      "notes": "研发部门使用",
      "createdAt": "2022-03-24T04:04:30.8354074+08:00",
      "updatedAt": "2023-02-24T04:04:30.8354076+08:00"
    }
  ],
  "total": 25,
  "pageIndex": 1,
  "pageSize": 20,
  "totalPages": 2
}

// 错误响应（500）
{
  "success": false,
  "message": "获取资产列表出错: 数据库连接异常"
}
```

### 2. 根据ID获取资产 [GET]
▍代码定位：AssetController.cs Line 151-259

```csharp
[HttpGet("{id}")]
public async Task<IActionResult> GetById(int id)
```

▍端点路径：/api/Asset/{id}
▍方法类型：GET

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "id": 1,
    "assetCode": "IT-PC-001",
    "name": "办公电脑",
    "assetTypeId": 1,
    "assetTypeName": "电脑设备",
    "serialNumber": "XPS15-9570-001",
    "model": "XPS 15",
    "brand": "Dell",
    "purchaseDate": "2022-03-24T04:04:30.8353554+08:00",
    "warrantyExpireDate": "2025-03-24T04:04:30.8354074+08:00",
    "price": 8999.00,
    "locationId": 1,
    "locationName": "研发部",
    "locationPath": "0,1",
    "locationFullName": "总部/研发部",
    "status": 1,
    "statusName": "在用",
    "notes": "研发部门使用",
    "createdAt": "2022-03-24T04:04:30.8354074+08:00",
    "updatedAt": "2023-02-24T04:04:30.8354076+08:00"
  }
}

// 错误响应（404）
{
  "success": false,
  "message": "未找到ID为1的资产"
}
```

### 3. 创建资产 [POST]
▍代码定位：AssetController.cs Line 738-795

```csharp
[HttpPost]
public async Task<IActionResult> Create([FromBody] Asset asset)
```

▍端点路径：/api/Asset
▍方法类型：POST

‖ 请求结构 ‖

```json
{
  "name": "新办公电脑",
  "assetTypeId": 1,
  "serialNumber": "XPS15-9570-002",
  "model": "XPS 15",
  "brand": "Dell",
  "purchaseDate": "2023-03-20T00:00:00",
  "warrantyExpireDate": "2026-03-20T00:00:00",
  "price": 9999.00,
  "locationId": 1,
  "notes": "新购入，待分配"
}
```

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "id": 4,
    "assetCode": "IT-PC-004",
    "name": "新办公电脑",
    "assetTypeId": 1,
    "serialNumber": "XPS15-9570-002",
    "model": "XPS 15",
    "brand": "Dell",
    "purchaseDate": "2023-03-20T00:00:00",
    "warrantyExpireDate": "2026-03-20T00:00:00",
    "price": 9999.00,
    "locationId": 1,
    "status": 0,
    "notes": "新购入，待分配",
    "createdAt": "2023-03-24T12:30:45.123456+08:00",
    "updatedAt": "2023-03-24T12:30:45.123456+08:00"
  },
  "message": "资产创建成功"
}

// 错误响应（400）
{
  "success": false,
  "message": "资产名称不能为空"
}

// 错误响应（400）
{
  "success": false,
  "message": "资产类型不存在"
}
```

### 4. 更新资产 [PUT]
▍代码定位：AssetController.cs Line 795-860

```csharp
[HttpPut("{id}")]
public async Task<IActionResult> Update(int id, [FromBody] Asset asset)
```

▍端点路径：/api/Asset/{id}
▍方法类型：PUT

‖ 请求结构 ‖

```json
{
  "name": "已分配办公电脑",
  "assetTypeId": 1,
  "serialNumber": "XPS15-9570-002",
  "model": "XPS 15",
  "brand": "Dell",
  "purchaseDate": "2023-03-20T00:00:00",
  "warrantyExpireDate": "2026-03-20T00:00:00",
  "price": 9999.00,
  "locationId": 2,
  "status": 1,
  "notes": "已分配给开发部"
}
```

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "id": 4,
    "assetCode": "IT-PC-004",
    "name": "已分配办公电脑",
    "assetTypeId": 1,
    "serialNumber": "XPS15-9570-002",
    "model": "XPS 15",
    "brand": "Dell",
    "purchaseDate": "2023-03-20T00:00:00",
    "warrantyExpireDate": "2026-03-20T00:00:00",
    "price": 9999.00,
    "locationId": 2,
    "status": 1,
    "notes": "已分配给开发部",
    "createdAt": "2023-03-24T12:30:45.123456+08:00",
    "updatedAt": "2023-03-24T12:45:30.654321+08:00"
  },
  "message": "资产更新成功"
}

// 错误响应（404）
{
  "success": false,
  "message": "未找到ID为4的资产"
}

// 错误响应（400）
{
  "success": false,
  "message": "资产类型不存在"
}
```

### 5. 删除资产 [DELETE]
▍代码定位：AssetController.cs

```csharp
[HttpDelete("{id}")]
public async Task<IActionResult> Delete(int id)
```

▍端点路径：/api/Asset/{id}
▍方法类型：DELETE

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": true,
  "message": "资产删除成功"
}

// 错误响应（404）
{
  "success": false,
  "message": "未找到ID为4的资产"
}
```

### 6. 获取资产历史记录 [GET]
▍代码定位：AssetController.cs Line 1191-1504

```csharp
[HttpGet("{id}/history")]
public async Task<IActionResult> GetHistory(
    int id,
    [FromQuery] int type = 0,
    [FromQuery] DateTime? startTime = null,
    [FromQuery] DateTime? endTime = null)
```

▍端点路径：/api/Asset/{id}/history
▍方法类型：GET

‖ 请求参数 ‖
- id: 资产ID
- type: 历史记录类型（0:全部, 1:位置变更, 2:状态变更, 3:属性变更）
- startTime: 开始时间（可选）
- endTime: 结束时间（可选）

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "asset": {
      "id": 1,
      "assetCode": "IPC20230001",
      "name": "服务器"
    },
    "history": [
      {
        "id": 5,
        "type": "update",
        "operationType": 2,
        "operationTypeName": "修改",
        "operatorId": 1,
        "operatorName": "系统管理员",
        "operationTime": "2023-07-25T14:30:00",
        "description": "品牌: 联想 → 华硕",
        "changes": [
          {
            "field": "品牌",
            "oldValue": "联想",
            "newValue": "华硕"
          }
        ],
        "icon": "edit",
        "color": "#faad14"
      },
      {
        "id": 2,
        "type": "location",
        "changeType": 0,
        "changeTypeName": "转移",
        "oldLocationId": 1,
        "oldLocationName": "一号车间",
        "newLocationId": 3,
        "newLocationName": "二号车间",
        "operatorId": 1,
        "operatorName": "系统管理员",
        "reason": "",
        "notes": "设备转移",
        "changeTime": "2023-06-01T10:00:00",
        "icon": "map-pin",
        "color": "#1890ff"
      }
    ]
  }
}

// 错误响应（404）
{
  "success": false,
  "message": "未找到ID为1的资产"
}

// 错误响应（500）
{
  "success": false,
  "message": "获取资产历史记录出错: Table 'itassets.assethistories' doesn't exist"
}
```

### 7. 获取位置资产历史记录 [GET]
▍代码定位：AssetController.cs

```csharp
[HttpGet("location/{locationId}/history")]
public async Task<IActionResult> GetLocationHistory(int locationId, DateTime? startTime = null, DateTime? endTime = null)
```

▍端点路径：/api/Asset/location/{locationId}/history
▍方法类型：GET

‖ 请求参数 ‖
- locationId: 位置ID
- startTime: 开始时间（可选）
- endTime: 结束时间（可选）

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "location": {
      "id": 1,
      "code": "IT-DEPT",
      "name": "IT部"
    },
    "history": [
      {
        "id": 1,
        "assetId": 1,
        "assetCode": "IT-PC-001",
        "assetName": "办公电脑",
        "isInbound": false,
        "targetLocationId": 2,
        "targetLocationName": "研发部",
        "operatorId": 1,
        "operatorName": "管理员",
        "reason": "部门调动",
        "notes": "员工岗位调动",
        "changeTime": "2024-03-20T10:30:00"
      }
    ]
  }
}
```

### 8. 变更资产位置 [PUT]
▍代码定位：AssetController.cs Line 1547-1650

```csharp
[HttpPut("{id}/location")]
public async Task<IActionResult> ChangeLocation(int id, [FromBody] ChangeLocationModel model)
```

▍端点路径：/api/Asset/{id}/location
▍方法类型：PUT

‖ 请求参数 ‖
- id: 资产ID
- model: 变更位置请求模型

‖ 请求结构 ‖

```json
{
  "newLocationId": 2,
  "reason": "部门调动",
  "notes": "员工岗位调动"
}
```

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "id": 1,
    "assetCode": "IT-PC-001",
    "name": "办公电脑",
    "locationId": 2,
    "oldLocationId": 1,
    "oldLocationName": "IT部",
    "newLocationId": 2,
    "newLocationName": "研发部",
    "updatedAt": "2024-03-20T10:30:00"
  },
  "message": "资产位置变更成功"
}

// 错误响应（400）
{
  "success": false,
  "message": "新位置与当前位置相同"
}

// 错误响应（404）
{
  "success": false,
  "message": "未找到ID为1的资产"
}
```

## 用户管理模块

▍模块状态：基本实现（需求覆盖率 75%）
▍代码锚点：/Controllers/UserController.cs

### 1. 获取所有用户 [GET]
▍代码定位：UserController.cs Line 37-77

```csharp
[HttpGet]
public async Task<IActionResult> GetAll()
```

▍端点路径：/api/User
▍方法类型：GET

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": [
    {
      "id": 1,
      "username": "admin",
      "name": "系统管理员",
      "position": "技术主管",
      "departmentId": 1,
      "departmentName": "信息技术部",
      "email": "<EMAIL>"
    },
    {
      "id": 2,
      "username": "zhangsan",
      "name": "张三",
      "position": "财务经理",
      "departmentId": 2,
      "departmentName": "财务部",
      "email": "<EMAIL>"
    }
  ]
}

// 错误响应（500）
{
  "success": false,
  "message": "获取用户列表出错: 数据库连接异常"
}
```

### 2. 根据ID获取用户 [GET]
▍代码定位：UserController.cs Line 79-148

```csharp
[HttpGet("{id}")]
public async Task<IActionResult> GetById(int id)
```

▍端点路径：/api/User/{id}
▍方法类型：GET

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "id": 1,
    "username": "admin",
    "name": "系统管理员",
    "position": "技术主管",
    "departmentId": 1,
    "departmentName": "信息技术部",
    "email": "<EMAIL>",
    "mobile": "13800138000",
    "gender": 1,
    "isActive": true,
    "roles": [
      {
        "id": 1,
        "name": "管理员"
      }
    ]
  }
}

// 错误响应（404）
{
  "success": false,
  "message": "未找到ID为1的用户"
}
```

### 3. 用户登录 [POST]
▍代码定位：UserController.cs Line 166-245

```csharp
[HttpPost("login")]
public async Task<IActionResult> Login([FromBody] LoginModel loginModel)
```

▍端点路径：/api/User/login
▍方法类型：POST

‖ 请求结构 ‖

```json
{
  "username": "admin",
  "password": "admin123"
}
```

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "token": "db_token_1a2b3c4d5e6f7g8h9i0j",
    "user": {
      "id": 1,
      "username": "admin",
      "name": "系统管理员",
      "roles": ["管理员"]
    }
  }
}

// 错误响应（401）
{
  "success": false,
  "message": "用户名或密码错误"
}
```

### 4. 添加新用户 [POST]
▍代码定位：UserController.cs Line 347-408

```csharp
[HttpPost]
public async Task<IActionResult> AddUser([FromBody] UserCreateModel userModel)
```

▍端点路径：/api/User
▍方法类型：POST

‖ 请求结构 ‖

```json
{
  "username": "newuser",
  "password": "password123",
  "name": "新用户",
  "departmentId": 1,
  "position": "开发工程师",
  "email": "<EMAIL>",
  "mobile": "13800138001",
  "gender": 1,
  "roles": [2]
}
```

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "id": 3,
    "username": "newuser",
    "name": "新用户",
    "position": "开发工程师",
    "departmentId": 1,
    "departmentName": "信息技术部",
    "email": "<EMAIL>",
    "mobile": "13800138001",
    "gender": 1,
    "isActive": true,
    "roles": [
      {
        "id": 2,
        "name": "普通用户"
      }
    ]
  }
}

// 错误响应（400）
{
  "success": false,
  "message": "添加用户失败: 用户名已存在"
}
```

### 5. 更新用户 [PUT]
▍代码定位：UserController.cs（未在代码片段中显示）

```csharp
[HttpPut("{id}")]
public async Task<IActionResult> UpdateUser(int id, [FromBody] UserUpdateModel model)
```

▍端点路径：/api/User/{id}
▍方法类型：PUT

‖ 请求结构 ‖

```json
{
  "name": "更新用户名",
  "position": "高级开发工程师",
  "email": "<EMAIL>",
  "mobile": "13900139001",
  "isActive": true,
  "roles": [2, 3]
}
```

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "id": 3,
    "username": "newuser",
    "name": "更新用户名",
    "position": "高级开发工程师",
    "departmentId": 1,
    "departmentName": "信息技术部",
    "email": "<EMAIL>",
    "mobile": "13900139001",
    "gender": 1,
    "isActive": true,
    "roles": [
      {
        "id": 2,
        "name": "普通用户"
      },
      {
        "id": 3,
        "name": "开发人员"
      }
    ]
  }
}

// 错误响应（404）
{
  "success": false,
  "message": "未找到ID为3的用户"
}
```

### 6. 删除用户 [DELETE]
▍代码定位：UserController.cs（未在代码片段中显示）

```csharp
[HttpDelete("{id}")]
public async Task<IActionResult> DeleteUser(int id)
```

▍端点路径：/api/User/{id}
▍方法类型：DELETE

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": true
}

// 错误响应（404）
{
  "success": false,
  "message": "未找到ID为3的用户"
}
```

## 任务管理模块

▍模块状态：基本实现（需求覆盖率 70%）
▍代码锚点：/Controllers/TaskController.cs

### 1. 获取所有任务 [GET]
▍代码定位：TaskController.cs（未在代码片段中显示）

```csharp
[HttpGet]
public async Task<IActionResult> GetAll()
```

▍端点路径：/api/Task
▍方法类型：GET

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": [
    {
      "id": 1,
      "title": "日常设备检查",
      "description": "检查所有服务器设备运行状态",
      "taskTypeId": 1,
      "taskTypeName": "日常维护",
      "status": 0,
      "statusName": "未开始",
      "priority": 1,
      "priorityName": "一般",
      "assigneeId": 1,
      "assigneeName": "系统管理员",
      "creatorId": 1,
      "creatorName": "系统管理员",
      "startDate": "2023-03-25T00:00:00",
      "dueDate": "2023-03-25T23:59:59",
      "completedDate": null,
      "createdAt": "2023-03-24T10:30:45",
      "updatedAt": "2023-03-24T10:30:45"
    }
  ]
}

// 错误响应（500）
{
  "success": false,
  "message": "获取任务列表出错: 数据库连接异常"
}
```

### 2. 根据ID获取任务 [GET]
▍代码定位：TaskController.cs（未在代码片段中显示）

```csharp
[HttpGet("{id}")]
public async Task<IActionResult> GetById(int id)
```

▍端点路径：/api/Task/{id}
▍方法类型：GET

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "id": 1,
    "title": "日常设备检查",
    "description": "检查所有服务器设备运行状态",
    "taskTypeId": 1,
    "taskTypeName": "日常维护",
    "status": 0,
    "statusName": "未开始",
    "priority": 1,
    "priorityName": "一般",
    "assigneeId": 1,
    "assigneeName": "系统管理员",
    "creatorId": 1,
    "creatorName": "系统管理员",
    "startDate": "2023-03-25T00:00:00",
    "dueDate": "2023-03-25T23:59:59",
    "completedDate": null,
    "createdAt": "2023-03-24T10:30:45",
    "updatedAt": "2023-03-24T10:30:45"
  }
}

// 错误响应（404）
{
  "success": false,
  "message": "未找到ID为1的任务"
}
```

### 3. 创建任务 [POST]
▍代码定位：TaskController.cs（未在代码片段中显示）

```csharp
[HttpPost]
public async Task<IActionResult> CreateTask([FromBody] TaskCreateModel model)
```

▍端点路径：/api/Task
▍方法类型：POST

‖ 请求结构 ‖

```json
{
  "title": "网络设备巡检",
  "description": "检查所有网络设备运行状态",
  "taskTypeId": 1,
  "status": 0,
  "priority": 1,
  "assigneeId": 1,
  "startDate": "2023-03-26T00:00:00",
  "dueDate": "2023-03-26T23:59:59"
}
```

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "id": 2,
    "title": "网络设备巡检",
    "description": "检查所有网络设备运行状态",
    "taskTypeId": 1,
    "taskTypeName": "日常维护",
    "status": 0,
    "statusName": "未开始",
    "priority": 1,
    "priorityName": "一般",
    "assigneeId": 1,
    "assigneeName": "系统管理员",
    "creatorId": 1,
    "creatorName": "系统管理员",
    "startDate": "2023-03-26T00:00:00",
    "dueDate": "2023-03-26T23:59:59",
    "completedDate": null,
    "createdAt": "2023-03-24T11:15:30",
    "updatedAt": "2023-03-24T11:15:30"
  }
}

// 错误响应（400）
{
  "success": false,
  "message": "创建任务失败: 截止日期不能早于开始日期"
}
```

### 4. 更新任务状态 [PUT]
▍代码定位：TaskController.cs（未在代码片段中显示）

```csharp
[HttpPut("{id}/status")]
public async Task<IActionResult> UpdateTaskStatus(int id, [FromBody] TaskStatusUpdateModel model)
```

▍端点路径：/api/Task/{id}/status
▍方法类型：PUT

‖ 请求结构 ‖

```json
{
  "status": 1,
  "comment": "已开始处理"
}
```

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "id": 2,
    "title": "网络设备巡检",
    "status": 1,
    "statusName": "进行中",
    "updatedAt": "2023-03-24T14:20:15"
  }
}

// 错误响应（400）
{
  "success": false,
  "message": "更新任务状态失败: 无效的状态值"
}
```

### 5. 完成任务 [POST]
▍代码定位：TaskController.cs（未在代码片段中显示）

```csharp
[HttpPost("{id}/complete")]
public async Task<IActionResult> CompleteTask(int id, [FromBody] TaskCompleteModel model)
```

▍端点路径：/api/Task/{id}/complete
▍方法类型：POST

‖ 请求结构 ‖

```json
{
  "comment": "任务已完成，所有设备正常"
}
```

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "id": 2,
    "title": "网络设备巡检",
    "status": 2,
    "statusName": "已完成",
    "completedDate": "2023-03-24T16:30:45",
    "updatedAt": "2023-03-24T16:30:45"
  }
}

// 错误响应（400）
{
  "success": false,
  "message": "完成任务失败: 任务已是完成状态"
}
```

## 故障管理模块

▍模块状态：基本实现（需求覆盖率 70%）
▍代码锚点：/Controllers/FaultController.cs

### 1. 获取所有故障记录 [GET]
▍代码定位：FaultController.cs（未在代码片段中显示）

```csharp
[HttpGet]
public async Task<IActionResult> GetAll()
```

▍端点路径：/api/Fault
▍方法类型：GET

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": [
    {
      "id": 1,
      "title": "服务器宕机",
      "description": "生产服务器突然无法访问",
      "faultTypeId": 1,
      "faultTypeName": "硬件故障",
      "priority": 2,
      "priorityName": "紧急",
      "status": 0,
      "statusName": "待处理",
      "assetId": 2,
      "assetCode": "IT-SERVER-001",
      "assetName": "应用服务器",
      "reporterId": 1,
      "reporterName": "系统管理员",
      "assigneeId": 1,
      "assigneeName": "系统管理员",
      "reportedAt": "2023-03-23T09:30:00",
      "resolvedAt": null,
      "createdAt": "2023-03-23T09:30:00",
      "updatedAt": "2023-03-23T09:30:00"
    }
  ]
}

// 错误响应（500）
{
  "success": false,
  "message": "获取故障记录列表出错: 数据库连接异常"
}
```

### 2. 根据ID获取故障记录 [GET]
▍代码定位：FaultController.cs（未在代码片段中显示）

```csharp
[HttpGet("{id}")]
public async Task<IActionResult> GetById(int id)
```

▍端点路径：/api/Fault/{id}
▍方法类型：GET

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "id": 1,
    "title": "服务器宕机",
    "description": "生产服务器突然无法访问",
    "faultTypeId": 1,
    "faultTypeName": "硬件故障",
    "priority": 2,
    "priorityName": "紧急",
    "status": 0,
    "statusName": "待处理",
    "assetId": 2,
    "assetCode": "IT-SERVER-001",
    "assetName": "应用服务器",
    "reporterId": 1,
    "reporterName": "系统管理员",
    "assigneeId": 1,
    "assigneeName": "系统管理员",
    "reportedAt": "2023-03-23T09:30:00",
    "resolvedAt": null,
    "solution": null,
    "createdAt": "2023-03-23T09:30:00",
    "updatedAt": "2023-03-23T09:30:00"
  }
}

// 错误响应（404）
{
  "success": false,
  "message": "未找到ID为1的故障记录"
}
```

### 3. 报告故障 [POST]
▍代码定位：FaultController.cs（未在代码片段中显示）

```csharp
[HttpPost]
public async Task<IActionResult> ReportFault([FromBody] FaultReportModel model)
```

▍端点路径：/api/Fault
▍方法类型：POST

‖ 请求结构 ‖

```json
{
  "title": "显示器黑屏",
  "description": "办公电脑显示器突然黑屏",
  "faultTypeId": 2,
  "priority": 1,
  "assetId": 1,
  "assigneeId": 1
}
```

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "id": 2,
    "title": "显示器黑屏",
    "description": "办公电脑显示器突然黑屏",
    "faultTypeId": 2,
    "faultTypeName": "硬件故障",
    "priority": 1,
    "priorityName": "一般",
    "status": 0,
    "statusName": "待处理",
    "assetId": 1,
    "assetCode": "IT-PC-001",
    "assetName": "办公电脑",
    "reporterId": 1,
    "reporterName": "系统管理员",
    "assigneeId": 1,
    "assigneeName": "系统管理员",
    "reportedAt": "2023-03-24T10:25:30",
    "resolvedAt": null,
    "solution": null,
    "createdAt": "2023-03-24T10:25:30",
    "updatedAt": "2023-03-24T10:25:30"
  }
}

// 错误响应（400）
{
  "success": false,
  "message": "报告故障失败: 资产ID不存在"
}
```

### 4. 更新故障状态 [PUT]
▍代码定位：FaultController.cs（未在代码片段中显示）

```csharp
[HttpPut("{id}/status")]
public async Task<IActionResult> UpdateFaultStatus(int id, [FromBody] FaultStatusUpdateModel model)
```

▍端点路径：/api/Fault/{id}/status
▍方法类型：PUT

‖ 请求结构 ‖

```json
{
  "status": 1,
  "comment": "技术人员正在处理"
}
```

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "id": 2,
    "title": "显示器黑屏",
    "status": 1,
    "statusName": "处理中",
    "updatedAt": "2023-03-24T11:15:20"
  }
}

// 错误响应（400）
{
  "success": false,
  "message": "更新故障状态失败: 无效的状态值"
}
```

### 5. 解决故障 [POST]
▍代码定位：FaultController.cs（未在代码片段中显示）

```csharp
[HttpPost("{id}/resolve")]
public async Task<IActionResult> ResolveFault(int id, [FromBody] FaultResolveModel model)
```

▍端点路径：/api/Fault/{id}/resolve
▍方法类型：POST

‖ 请求结构 ‖

```json
{
  "solution": "更换显示器电源适配器",
  "comment": "已更换新的电源适配器，问题已解决"
}
```

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "id": 2,
    "title": "显示器黑屏",
    "status": 2,
    "statusName": "已解决",
    "solution": "更换显示器电源适配器",
    "resolvedAt": "2023-03-24T15:30:00",
    "updatedAt": "2023-03-24T15:30:00"
  }
}

// 错误响应（400）
{
  "success": false,
  "message": "解决故障失败: 故障已是已解决状态"
}
```

## 采购管理模块

▍模块状态：基本实现（需求覆盖率 75%）
▍代码锚点：/Controllers/PurchaseController.cs

### 1. 获取所有采购单 [GET]
▍代码定位：PurchaseController.cs（未在代码片段中显示）

```csharp
[HttpGet]
public async Task<IActionResult> GetAll()
```

▍端点路径：/api/Purchase
▍方法类型：GET

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": [
    {
      "id": 1,
      "orderCode": "PO-2023-001",
      "title": "办公电脑采购",
      "description": "新增员工办公电脑",
      "status": 0,
      "statusName": "未到货",
      "totalAmount": 29997.00,
      "supplierId": 1,
      "supplierName": "戴尔电脑",
      "purchaserId": 1,
      "purchaserName": "系统管理员",
      "approverId": 2,
      "approverName": "张三",
      "expectedDeliveryDate": "2023-04-01T00:00:00",
      "actualDeliveryDate": null,
      "createdAt": "2023-03-20T14:30:00",
      "updatedAt": "2023-03-20T14:30:00"
    }
  ]
}

// 错误响应（500）
{
  "success": false,
  "message": "获取采购单列表出错: 数据库连接异常"
}
```

### 2. 根据ID获取采购单 [GET]
▍代码定位：PurchaseController.cs（未在代码片段中显示）

```csharp
[HttpGet("{id}")]
public async Task<IActionResult> GetById(int id)
```

▍端点路径：/api/Purchase/{id}
▍方法类型：GET

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "id": 1,
    "orderCode": "PO-2023-001",
    "title": "办公电脑采购",
    "description": "新增员工办公电脑",
    "status": 0,
    "statusName": "未到货",
    "totalAmount": 29997.00,
    "supplierId": 1,
    "supplierName": "戴尔电脑",
    "purchaserId": 1,
    "purchaserName": "系统管理员",
    "approverId": 2,
    "approverName": "张三",
    "expectedDeliveryDate": "2023-04-01T00:00:00",
    "actualDeliveryDate": null,
    "items": [
      {
        "id": 1,
        "purchaseOrderId": 1,
        "name": "XPS 15笔记本电脑",
        "quantity": 3,
        "unitPrice": 9999.00,
        "totalPrice": 29997.00,
        "description": "i7处理器, 16GB内存, 512GB SSD",
        "assetTypeId": 1,
        "assetTypeName": "电脑设备"
      }
    ],
    "createdAt": "2023-03-20T14:30:00",

    "updatedAt": "2023-03-20T14:30:00"
  }
}

// 错误响应（404）
{
  "success": false,
  "message": "未找到ID为1的采购单"
}
```

### 3. 创建采购单 [POST]
▍代码定位：PurchaseController.cs（未在代码片段中显示）

```csharp
[HttpPost]
public async Task<IActionResult> CreatePurchase([FromBody] PurchaseCreateModel model)
```

▍端点路径：/api/Purchase
▍方法类型：POST

‖ 请求结构 ‖

```json
{
  "title": "网络设备采购",
  "description": "办公室网络升级设备",
  "supplierId": 2,
  "approverId": 2,
  "expectedDeliveryDate": "2023-04-15T00:00:00",
  "items": [
    {
      "name": "千兆交换机",
      "quantity": 2,
      "unitPrice": 2999.00,
      "description": "24口千兆交换机",
      "assetTypeId": 3
    },
    {
      "name": "无线AP",
      "quantity": 5,
      "unitPrice": 899.00,
      "description": "双频无线AP",
      "assetTypeId": 3
    }
  ]
}
```

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "id": 2,
    "orderCode": "PO-2023-002",
    "title": "网络设备采购",
    "description": "办公室网络升级设备",
    "status": 0,
    "statusName": "未到货",
    "totalAmount": 10493.00,
    "supplierId": 2,
    "supplierName": "思科网络",
    "purchaserId": 1,
    "purchaserName": "系统管理员",
    "approverId": 2,
    "approverName": "张三",
    "expectedDeliveryDate": "2023-04-15T00:00:00",
    "actualDeliveryDate": null,
    "items": [
      {
        "id": 2,
        "purchaseOrderId": 2,
        "name": "千兆交换机",
        "quantity": 2,
        "unitPrice": 2999.00,
        "totalPrice": 5998.00,
        "description": "24口千兆交换机",
        "assetTypeId": 3,
        "assetTypeName": "网络设备"
      },
      {
        "id": 3,
        "purchaseOrderId": 2,
        "name": "无线AP",
        "quantity": 5,
        "unitPrice": 899.00,
        "totalPrice": 4495.00,
        "description": "双频无线AP",
        "assetTypeId": 3,
        "assetTypeName": "网络设备"
      }
    ],
    "createdAt": "2023-03-24T09:45:30",
    "updatedAt": "2023-03-24T09:45:30"
  }
}

// 错误响应（400）
{
  "success": false,
  "message": "创建采购单失败: 必须至少包含一个采购项"
}
```

### 4. 更新采购单状态 [PUT]
▍代码定位：PurchaseController.cs（未在代码片段中显示）

```csharp
[HttpPut("{id}/status")]
public async Task<IActionResult> UpdatePurchaseStatus(int id, [FromBody] PurchaseStatusUpdateModel model)
```

▍端点路径：/api/Purchase/{id}/status
▍方法类型：PUT

‖ 请求结构 ‖

```json
{
  "status": 1,
  "actualDeliveryDate": "2023-03-30T14:30:00",
  "comment": "物品已到货，正在验收"
}
```

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "id": 1,
    "orderCode": "PO-2023-001",
    "title": "办公电脑采购",
    "status": 1,
    "statusName": "已到货",
    "actualDeliveryDate": "2023-03-30T14:30:00",
    "updatedAt": "2023-03-30T14:35:20"
  }
}

// 错误响应（400）
{
  "success": false,
  "message": "更新采购单状态失败: 无效的状态值"
}
```

### 5. 将采购单转为资产 [POST]
▍代码定位：PurchaseController.cs（未在代码片段中显示）

```csharp
[HttpPost("{id}/convert-to-assets")]
public async Task<IActionResult> ConvertToAssets(int id, [FromBody] ConvertToAssetsModel model)
```

▍端点路径：/api/Purchase/{id}/convert-to-assets
▍方法类型：POST

‖ 请求结构 ‖

```json
{
  "locationId": 1,
  "comment": "将到货物品登记为资产",
  "items": [
    {
      "purchaseItemId": 1,
      "serialNumbers": ["XPS15-9570-001", "XPS15-9570-002", "XPS15-9570-003"]
    }
  ]
}
```

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "purchaseOrderId": 1,
    "createdAssets": [
      {
        "id": 4,
        "assetCode": "IT-PC-004",
        "name": "XPS 15笔记本电脑",
        "assetTypeId": 1,
        "serialNumber": "XPS15-9570-001"
      },
      {
        "id": 5,
        "assetCode": "IT-PC-005",
        "name": "XPS 15笔记本电脑",
        "assetTypeId": 1,
        "serialNumber": "XPS15-9570-002"
      },
      {
        "id": 6,
        "assetCode": "IT-PC-006",
        "name": "XPS 15笔记本电脑",
        "assetTypeId": 1,
        "serialNumber": "XPS15-9570-003"
      }
    ]
  }
}

// 错误响应（400）
{
  "success": false,
  "message": "转换采购单为资产失败: 采购单尚未到货"
}
```

## 插件管理模块

▍模块状态：基本实现（需求覆盖率 90%）
▍代码锚点：/Controllers/PluginController.cs

### 1. 获取所有插件 [GET]
▍代码定位：PluginController.cs（未在代码片段中显示）

```csharp
[HttpGet]
public async Task<IActionResult> GetAll()
```

▍端点路径：/api/Plugin
▍方法类型：GET

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": [
    {
      "id": "TaskManagementPlugin",
      "name": "任务管理插件",
      "description": "提供任务管理功能",
      "version": "1.0.0",
      "enabled": true,
      "isBuiltIn": true,
      "author": "系统",
      "createdAt": "2023-03-22T00:00:00",
      "lastUpdatedAt": "2023-03-22T00:00:00"
    },
    {
      "id": "AuditLogPlugin",
      "name": "审计日志插件",
      "description": "记录系统操作日志",
      "version": "1.0.0",
      "enabled": true,
      "isBuiltIn": true,
      "author": "系统",
      "createdAt": "2023-03-22T00:00:00",
      "lastUpdatedAt": "2023-03-22T00:00:00"
    },
    {
      "id": "FaultManagementPlugin",
      "name": "故障管理插件",
      "description": "提供故障报修和处理功能",
      "version": "1.0.0",
      "enabled": true,
      "isBuiltIn": true,
      "author": "系统",
      "createdAt": "2023-03-22T00:00:00",
      "lastUpdatedAt": "2023-03-22T00:00:00"
    },
    {
      "id": "PurchaseManagementPlugin",
      "name": "采购管理插件",
      "description": "提供采购管理功能",
      "version": "1.0.0",
      "enabled": true,
      "isBuiltIn": true,
      "author": "系统",
      "createdAt": "2023-03-22T00:00:00",
      "lastUpdatedAt": "2023-03-22T00:00:00"
    }
  ]
}

// 错误响应（500）
{
  "success": false,
  "message": "获取插件列表出错: 插件管理器初始化失败"
}
```

### 2. 根据ID获取插件 [GET]
▍代码定位：PluginController.cs（未在代码片段中显示）

```csharp
[HttpGet("{id}")]
public async Task<IActionResult> GetById(string id)
```

▍端点路径：/api/Plugin/{id}
▍方法类型：GET

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "id": "TaskManagementPlugin",
    "name": "任务管理插件",
    "description": "提供任务管理功能",
    "version": "1.0.0",
    "enabled": true,
    "isBuiltIn": true,
    "author": "系统",
    "settings": {
      "enableReminders": true,
      "reminderTimeMinutes": 30,
      "maxTasksPerUser": 50
    },
    "createdAt": "2023-03-22T00:00:00",
    "lastUpdatedAt": "2023-03-22T00:00:00"
  }
}

// 错误响应（404）
{
  "success": false,
  "message": "未找到ID为TaskManagerPlugin的插件"
}
```

### 3. 启用插件 [POST]
▍代码定位：PluginController.cs（未在代码片段中显示）

```csharp
[HttpPost("{id}/enable")]
public async Task<IActionResult> EnablePlugin(string id)
```

▍端点路径：/api/Plugin/{id}/enable
▍方法类型：POST

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "id": "TaskManagementPlugin",
    "name": "任务管理插件",
    "enabled": true,
    "lastUpdatedAt": "2023-03-24T10:20:15"
  }
}

// 错误响应（400）
{
  "success": false,
  "message": "启用插件失败: 插件已处于启用状态"
}
```

### 4. 禁用插件 [POST]
▍代码定位：PluginController.cs（未在代码片段中显示）

```csharp
[HttpPost("{id}/disable")]
public async Task<IActionResult> DisablePlugin(string id)
```

▍端点路径：/api/Plugin/{id}/disable
▍方法类型：POST

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "id": "TaskManagementPlugin",
    "name": "任务管理插件",
    "enabled": false,
    "lastUpdatedAt": "2023-03-24T10:25:30"
  }
}

// 错误响应（400）
{
  "success": false,
  "message": "禁用插件失败: 内置插件不能被禁用"
}
```

### 5. 更新插件设置 [PUT]
▍代码定位：PluginController.cs（未在代码片段中显示）

```csharp
[HttpPut("{id}/settings")]
public async Task<IActionResult> UpdatePluginSettings(string id, [FromBody] Dictionary<string, object> settings)
```

▍端点路径：/api/Plugin/{id}/settings
▍方法类型：PUT

‖ 请求结构 ‖

```json
{
  "enableReminders": false,
  "reminderTimeMinutes": 60,
  "maxTasksPerUser": 100
}
```

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "id": "TaskManagementPlugin",
    "name": "任务管理插件",
    "settings": {
      "enableReminders": false,
      "reminderTimeMinutes": 60,
      "maxTasksPerUser": 100
    },
    "lastUpdatedAt": "2023-03-24T10:30:45"
  }
}

// 错误响应（400）
{
  "success": false,
  "message": "更新插件设置失败: 包含无效的设置项"
}
```

## 离线操作管理模块

▍模块状态：基本实现（需求覆盖率 95%）
▍代码锚点：/Controllers/OfflineOperationController.cs

### 1. 获取所有离线操作 [GET]
▍代码定位：OfflineOperationController.cs（未在代码片段中显示）

```csharp
[HttpGet]
public async Task<IActionResult> GetAll()
```

▍端点路径：/api/OfflineOperation
▍方法类型：GET

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": [
    {
      "id": "op-1a2b3c",
      "operationType": "AssetCreate",
      "status": 0,
      "statusName": "待处理",
      "data": {
        "name": "新办公电脑",
        "assetTypeId": 1
      },
      "error": null,
      "retryCount": 0,
      "createdAt": "2023-03-23T15:30:45",
      "lastUpdatedAt": "2023-03-23T15:30:45"
    },
    {
      "id": "op-4d5e6f",
      "operationType": "UserUpdate",
      "status": 1,
      "statusName": "处理中",
      "data": {
        "id": 3,
        "name": "更新用户名"
      },
      "error": null,
      "retryCount": 0,
      "createdAt": "2023-03-23T15:45:20",
      "lastUpdatedAt": "2023-03-23T15:45:30"
    }
  ]
}

// 错误响应（500）
{
  "success": false,
  "message": "获取离线操作列表出错: 离线操作管理器未初始化"
}
```

### 2. 根据ID获取离线操作 [GET]
▍代码定位：OfflineOperationController.cs（未在代码片段中显示）

```csharp
[HttpGet("{id}")]
public async Task<IActionResult> GetById(string id)
```

▍端点路径：/api/OfflineOperation/{id}
▍方法类型：GET

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "id": "op-1a2b3c",
    "operationType": "AssetCreate",
    "status": 0,
    "statusName": "待处理",
    "data": {
      "name": "新办公电脑",
      "assetTypeId": 1,
      "serialNumber": "XPS15-9570-002",
      "model": "XPS 15",
      "brand": "Dell",
      "purchaseDate": "2023-03-20T00:00:00",
      "warrantyExpireDate": "2026-03-20T00:00:00",
      "price": 9999.00,
      "locationId": 1,
      "status": 0,
      "notes": "新购入，待分配"
    },
    "error": null,
    "retryCount": 0,
    "createdAt": "2023-03-23T15:30:45",
    "lastUpdatedAt": "2023-03-23T15:30:45"
  }
}

// 错误响应（404）
{
  "success": false,
  "message": "未找到ID为op-1a2b3c的离线操作"
}
```

### 3. 处理离线操作 [POST]
▍代码定位：OfflineOperationController.cs（未在代码片段中显示）

```csharp
[HttpPost("{id}/process")]
public async Task<IActionResult> ProcessOperation(string id)
```

▍端点路径：/api/OfflineOperation/{id}/process
▍方法类型：POST

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "id": "op-1a2b3c",
    "operationType": "AssetCreate",
    "status": 2,
    "statusName": "已完成",
    "result": {
      "id": 4,
      "assetCode": "IT-PC-004",
      "name": "新办公电脑"
    },
    "lastUpdatedAt": "2023-03-24T10:15:30"
  }
}

// 错误响应（400）
{
  "success": false,
  "message": "处理离线操作失败: 当前网络不可用"
}
```

### 4. 清除完成的离线操作 [DELETE]
▍代码定位：OfflineOperationController.cs（未在代码片段中显示）

```csharp
[HttpDelete("completed")]
public async Task<IActionResult> ClearCompletedOperations()
```

▍端点路径：/api/OfflineOperation/completed
▍方法类型：DELETE

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "clearedCount": 5,
    "remainingCount": 2
  }
}

// 错误响应（500）
{
  "success": false,
  "message": "清除已完成的离线操作失败: 存储访问异常"
}
```

## 健康检查模块

▍模块状态：基本实现（需求覆盖率 100%）
▍代码锚点：/Controllers/HealthController.cs

### 1. 获取系统状态 [GET]
▍代码定位：HealthController.cs（未在代码片段中显示）

```csharp
[HttpGet]
public IActionResult Status()
```

▍端点路径：/api/Health
▍方法类型：GET

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "status": "healthy",
    "version": "1.0.0",
    "uptime": "3d 5h 45m 30s",
    "systemTime": "2023-03-24T14:30:45",
    "environment": "Production",
    "components": [
      {
        "name": "Database",
        "status": "healthy",
        "details": "Connection active"
      },
      {
        "name": "FileSystem",
        "status": "healthy",
        "details": "Available space: 120GB"
      },
      {
        "name": "NetworkMonitor",
        "status": "healthy",
        "details": "Network connection available"
      }
    ]
  }
}
```

### 2. 检查数据库连接 [GET]
▍代码定位：HealthController.cs（未在代码片段中显示）

```csharp
[HttpGet("database")]
public async Task<IActionResult> CheckDatabase()
```

▍端点路径：/api/Health/database
▍方法类型：GET

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "status": "connected",
    "server": "localhost",
    "database": "itassets",
    "responseTimeMs": 15,
    "version": "MySQL 8.0.28"
  }
}

// 错误响应（503）
{
  "success": false,
  "message": "数据库连接失败: 无法连接到服务器",
  "details": {
    "status": "disconnected",
    "server": "localhost",
    "errorCode": "ECONNREFUSED"
  }
}
```

## 公共API特性

‖ 响应格式 ‖

所有API遵循统一的响应格式：

```json
// 成功响应
{
  "success": true,
  "data": {/* 返回数据 */}
}

// 错误响应
{
  "success": false,
  "message": "错误信息"
}
```

‖ 错误处理 ‖

所有控制器方法都采用try-catch包装，统一处理异常：

```csharp
try {
    // 业务逻辑
    return Ok(new { success = true, data = result });
}
catch (Exception ex) {
    _logger.LogError(ex, "错误信息");
    return StatusCode(500, new { success = false, message = "错误信息: " + ex.Message });
}
```

‖ 韧性设计 ‖

大部分API实现了数据库连接检测和降级处理：

```csharp
bool isDatabaseAvailable = await CheckDatabaseConnectionAsync();
if (!isDatabaseAvailable) {
    // 返回模拟数据
}
```

## ▍数据模型映射

| 实体类 | 数据库表 | 对应关系 |
|--------|---------|---------|
| User.cs | users | 完全映射 |
| Asset.cs | assets | 完全映射 |
| Task.cs | tasks | 完全映射 |
| FaultRecord.cs | faultrecords | 完全映射 |
| PurchaseOrder.cs | purchaseorders | 完全映射 |
| Location.cs | locations | 完全映射 |

## 附录

### A. 未解决问题
- 所有API端点缺少[ProducesResponseType]标注
- 未实现身份认证和授权管理
- 请求模型缺少数据验证特性
- 缺少API版本控制

### B. 自动化校验报告
| 检查项 | 通过率 | 严重问题 |
|-------|--------|----------|
| 参数校验一致性 | 15% | 大部分请求缺少参数验证 |
| 响应模型匹配 | 80% | 部分接口返回的数据结构不一致 |
| 安全头配置 | 0% | 未配置安全头(HTTPS,CORS,CSP) |
| 韧性实现 | 85% | 大部分接口实现了数据库不可用时的降级方案 |




## 位置管理模块

▍模块状态：完整实现（需求覆盖率 95%）
▍代码锚点：/Controllers/LocationController.cs

### 1. 获取位置树 [GET]
▍代码定位：LocationController.cs

```csharp
[HttpGet("tree")]
public async Task<IActionResult> GetTree()
```

▍端点路径：/api/Location/tree
▍方法类型：GET

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": [
    {
      "id": 1,
      "code": "HQ",
      "name": "总部",
      "type": 0,
      "parentId": null,
      "path": "0",
      "description": "公司总部",
      "isActive": true,
      "children": [
        {
          "id": 2,
          "code": "F001",
          "name": "A工厂",
          "type": 1,
          "parentId": 1,
          "path": "0,1",
          "description": "A工厂",
          "isActive": true,
          "children": []
        }
      ]
    }
  ]
}
```

### 2. 获取位置列表 [GET]
▍代码定位：LocationController.cs

```csharp
[HttpGet("list")]
public async Task<IActionResult> GetList()
```

▍端点路径：/api/Location/list
▍方法类型：GET

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": [
    {
      "id": 1,
      "code": "HQ",
      "name": "总部",
      "type": 0,
      "parentId": null,
      "path": "0",
      "description": "公司总部",
      "isActive": true
    },
    {
      "id": 2,
      "code": "F001",
      "name": "A工厂",
      "type": 1,
      "parentId": 1,
      "path": "0,1",
      "description": "A工厂",
      "isActive": true
    }
  ],
  "count": 2,
  "message": "获取位置列表成功"
}
```

### 3. 初始化根位置 [POST]
▍代码定位：LocationController.cs

```csharp
[HttpPost("init-root")]
public async Task<IActionResult> InitializeRootLocation()
```

▍端点路径：/api/Location/init-root
▍方法类型：POST

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "message": "根位置创建成功",
  "data": {
    "id": 1,
    "code": "HQ",
    "name": "总部"
  }
}

// 错误响应（400）
{
  "success": false,
  "message": "根位置已存在"
}
```

### 4. 创建位置 [POST]
▍代码定位：LocationController.cs

```csharp
[HttpPost]
public async Task<IActionResult> Create([FromBody] Location location)
```

▍端点路径：/api/Location
▍方法类型：POST

‖ 请求结构 ‖

```json
{
  "name": "A工厂",
  "type": 1,
  "parentId": 1,
  "description": "A工厂",
  "defaultDepartmentId": 1,
  "managerId": 1,
  "isActive": true,
  "locationUsers": [
    {
      "userId": 1,
      "userType": 0
    }
  ]
}
```

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "id": 2,
    "code": "F001",
    "name": "A工厂",
    "type": 1,
    "parentId": 1,
    "path": "0,1",
    "description": "A工厂",
    "defaultDepartmentId": 1,
    "managerId": 1,
    "isActive": true,
    "createdAt": "2024-03-24T10:30:00",
    "updatedAt": "2024-03-24T10:30:00"
  },
  "message": "位置创建成功"
}
```

### 5. 更新位置 [PUT]
▍代码定位：LocationController.cs

```csharp
[HttpPut("{id}")]
public async Task<IActionResult> Update(int id, [FromBody] Location location)
```

▍端点路径：/api/Location/{id}
▍方法类型：PUT

‖ 请求结构 ‖

```json
{
  "name": "A工厂-更新",
  "description": "A工厂-更新描述",
  "defaultDepartmentId": 2,
  "managerId": 2,
  "isActive": true,
  "locationUsers": [
    {
      "userId": 2,
      "userType": 0
    }
  ]
}
```

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "id": 2,
    "code": "F001",
    "name": "A工厂-更新",
    "type": 1,
    "parentId": 1,
    "path": "0,1",
    "description": "A工厂-更新描述",
    "defaultDepartmentId": 2,
    "managerId": 2,
    "isActive": true,
    "sortOrder": 0,
    "createdAt": "2024-03-24T10:30:00",
    "updatedAt": "2024-03-24T10:35:00"
  },
  "message": "位置更新成功"
}
```

### 6. 删除位置 [DELETE]
▍代码定位：LocationController.cs

```csharp
[HttpDelete("{id}")]
public async Task<IActionResult> Delete(int id)
```

▍端点路径：/api/Location/{id}
▍方法类型：DELETE

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "message": "位置删除成功"
}

// 错误响应（400）
{
  "success": false,
  "message": "该位置下有子位置，不能删除"
}

// 错误响应（400）
{
  "success": false,
  "message": "该位置下有关联的资产，不能删除"
}
```

### 7. 获取位置资产历史记录 [GET]
▍代码定位：AssetController.cs

```csharp
[HttpGet("location/{locationId}/history")]
public async Task<IActionResult> GetLocationHistory(int locationId, DateTime? startTime = null, DateTime? endTime = null)
```

▍端点路径：/api/Asset/location/{locationId}/history
▍方法类型：GET

‖ 请求参数 ‖
- locationId: 位置ID
- startTime: 开始时间（可选）
- endTime: 结束时间（可选）

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "location": {
      "id": 1,
      "code": "IT-DEPT",
      "name": "IT部"
    },
    "history": [
      {
        "id": 1,
        "assetId": 1,
        "assetCode": "IT-PC-001",
        "assetName": "办公电脑",
        "isInbound": false,
        "targetLocationId": 2,
        "targetLocationName": "研发部",
        "operatorId": 1,
        "operatorName": "管理员",
        "reason": "部门调动",
        "notes": "员工岗位调动",
        "changeTime": "2024-03-20T10:30:00"
      }
    ]
  }
}
```

### 8. 获取位置用户关联 [GET]
▍代码定位：LocationController.cs

```csharp
[HttpGet("{id}/users")]
public async Task<IActionResult> GetLocationUsers(int id)
```

▍端点路径：/api/Location/{id}/users
▍方法类型：GET

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "location": {
      "id": 1,
      "code": "HQ-F1-R102",
      "name": "102办公室"
    },
    "users": [
      {
        "userId": 1,
        "userName": "李四",
        "userType": 0,
        "userTypeName": "使用人",
        "departmentName": "技术部",
        "position": "开发工程师",
        "isActive": true
      },
      {
        "userId": 2,
        "userName": "王五",
        "userType": 1,
        "userTypeName": "负责人",
        "departmentName": "技术部",
        "position": "技术主管",
        "isActive": true
      }
    ]
  }
}
```

### 9. 更新位置用户关联 [PUT]
▍代码定位：LocationController.cs

```csharp
[HttpPut("{id}/users")]
public async Task<IActionResult> UpdateLocationUsers(int id, [FromBody] List<LocationUser> users)
```

▍端点路径：/api/Location/{id}/users
▍方法类型：PUT

‖ 请求结构 ‖

```json
{
  "users": [
    {
      "userId": 1,
      "userType": 0,
      "isActive": true
    },
    {
      "userId": 2,
      "userType": 1,
      "isActive": true
    },
    {
      "userId": 3,
      "userType": 0,
      "isActive": false
    }
  ]
}
```

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "location": {
      "id": 1,
      "code": "HQ-F1-R102",
      "name": "102办公室"
    },
    "users": [
      {
        "userId": 1,
        "userName": "李四",
        "userType": 0,
        "userTypeName": "使用人",
        "departmentName": "技术部",
        "position": "开发工程师",
        "isActive": true
      },
      {
        "userId": 2,
        "userName": "王五",
        "userType": 1,
        "userTypeName": "负责人",
        "departmentName": "技术部",
        "position": "技术主管",
        "isActive": true
      },
      {
        "userId": 3,
        "userName": "赵六",
        "userType": 0,
        "userTypeName": "使用人",
        "departmentName": "技术部",
        "position": "测试工程师",
        "isActive": false
      }
    ],
    "message": "位置用户关联更新成功"
  }
}
```

### 10. 获取位置部门关联 [GET]
▍代码定位：LocationController.cs

```csharp
[HttpGet("{id}/department")]
public async Task<IActionResult> GetLocationDepartment(int id)
```

▍端点路径：/api/Location/{id}/department
▍方法类型：GET

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "location": {
      "id": 1,
      "code": "HQ-F1-R102",
      "name": "102办公室"
    },
    "department": {
      "id": 1,
      "code": "TECH",
      "name": "技术部",
      "managerId": 2,
      "managerName": "王五",
      "description": "负责公司技术研发"
    }
  }
}
```

### 11. 更新位置部门关联 [PUT]
▍代码定位：LocationController.cs

```csharp
[HttpPut("{id}/department")]
public async Task<IActionResult> UpdateLocationDepartment(int id, [FromBody] int departmentId)
```

▍端点路径：/api/Location/{id}/department
▍方法类型：PUT

‖ 请求结构 ‖

```json
{
  "departmentId": 1
}
```

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "location": {
      "id": 1,
      "code": "HQ-F1-R102",
      "name": "102办公室"
    },
    "department": {
      "id": 1,
      "code": "TECH",
      "name": "技术部",
      "managerId": 2,
      "managerName": "王五",
      "description": "负责公司技术研发"
    },
    "message": "位置部门关联更新成功"
  }
}
```

## 数据导入模块

▍模块状态：完整实现（需求覆盖率 90%）
▍代码锚点：/Api/Import/ImportDataEndpoint.cs

### 1. 获取支持的导入格式 [GET]
▍代码定位：ImportDataEndpoint.cs Line 47-67

```csharp
[HttpGet("formats")]
public ActionResult<List<string>> GetSupportedFormats()
```

▍端点路径：/api/import/formats
▍方法类型：GET

‖ 响应示例 ‖

```json
// 成功响应（200）
[
  "CSV",
  "XLSX",
  "JSON"
]
```

### 2. 获取导入模板 [GET]
▍代码定位：ImportDataEndpoint.cs Line 68-143

```csharp
[HttpGet("template/{entityType}/{formatStr}")]
public async Task<IActionResult> GetTemplateAsync(string entityType, string formatStr)
```

▍端点路径：/api/import/template/{entityType}/{formatStr}
▍方法类型：GET

‖ 参数 ‖

- entityType: 实体类型 (assets, locations, assettypes 等)
- formatStr: 导出格式 (CSV, XLSX, JSON)

‖ 返回值 ‖

成功返回导入模板文件流

### 3. 处理数据导入 [POST]
▍代码定位：ImportDataEndpoint.cs Line 144-200

```csharp
[HttpPost("process")]
public async Task<IActionResult> ProcessImportAsync([FromForm] IFormFile file, [FromForm] string entityType)
```

▍端点路径：/api/import/process
▍方法类型：POST

‖ 参数 ‖

- file: 导入文件
- entityType: 实体类型

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "importId": "imp_123456",
    "status": "processing",
    "message": "导入任务已启动"
  }
}
```

### 4. 获取导入结果 [GET]
▍代码定位：ImportDataEndpoint.cs Line 201-250

```csharp
[HttpGet("result/{importId}")]
public async Task<IActionResult> GetImportResultAsync(string importId)
```

▍端点路径：/api/import/result/{importId}
▍方法类型：GET

‖ 参数 ‖

- importId: 导入任务ID

‖ 响应示例 ‖

```json
// 成功响应（200）
{
  "success": true,
  "data": {
    "importId": "imp_123456",
    "status": "completed",
    "totalRows": 100,
    "successCount": 95,
    "errorCount": 5,
    "errors": [
      {
        "row": 3,
        "message": "资产编码重复"
      }
    ]
  }
}
```

### 5. 数据导入字段映射说明

导入资产数据时，系统会对Excel表头进行智能识别和映射，特别支持以下匹配规则：

1. **表头映射逻辑**:
   - 支持带空格和无空格的表头，如"购买日期"和"购买 日期"都会被正确识别
   - 支持多种表头别名，例如资产编码可通过"资产编码"、"资产编号"、"编码"、"ID"等多种表头名识别
   - 智能区分资产名称和资产编码，避免映射混淆

2. **关键字段映射**:
   ```
   资产编码 (AssetCode): 资产编码、资产编号、编码、ID、资产ID、编号、资产代码
   财务编号 (FinancialCode): 财务编号、财务编码、财务ID、财务代码、资产财务编号
   资产名称 (Name): 资产名称、名称、设备名称、设备、资产
   资产类型 (AssetTypeName/AssetTypeId): 资产类型、类型、资产种类、设备类型、类别
   位置 (LocationName/LocationId): 位置、地点、存放位置、所在位置
   部门 (DepartmentName/DepartmentId): 部门、部门名称、使用部门
   品牌 (Brand): 品牌、制造商、厂商、生产商、供应商、设备品牌、厂家
   规格型号 (Model): 型号、规格型号、设备型号、模型、规格、产品型号
   ```

3. **特殊处理逻辑**:
   - 资产编码和资产名称的智能识别，防止混淆
   - 资产类型名称自动转换为资产类型ID
   - 位置名称自动转换为位置ID
   - 部门名称自动转换为部门ID
   - 状态文本自动转换为状态编码 (例如"闲置"→0, "在用"→1, "维修"→2, "报废"→3)
   - 自动设置CreatedAt和UpdatedAt为当前时间