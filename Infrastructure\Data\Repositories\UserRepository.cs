#nullable enable

using ItAssetsSystem.Core.Abstractions;
using ItAssetsSystem.Models.Entities; // 正确的 User 实体命名空间
using Microsoft.EntityFrameworkCore; // For EF Core specific methods
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace ItAssetsSystem.Infrastructure.Data.Repositories
{
    public class UserRepository : IUserRepository // Or implements your specific user repository interface
    {
        private readonly AppDbContext _context;
        private readonly ILogger<UserRepository> _logger;

        public UserRepository(AppDbContext context, ILogger<UserRepository> logger)
        {
            _context = context;
            _logger = logger;
        }

        // ... other existing repository methods ...

        public async Task<User?> GetByIdAsync(int userId)
        {
            try
            {
                return await _context.Users
                    .Include(u => u.Department)
                    .Include(u => u.UserRoles)
                        .ThenInclude(ur => ur.Role)
                    .FirstOrDefaultAsync(u => u.Id == userId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user by ID {UserId}", userId);
                return null;
            }
        }

        public async Task<bool> UpdateAvatarAsync(int userId, string? avatarUrl)
        {
            try
            {
                var user = await _context.Users.FindAsync(userId);
                if (user == null)
                {
                    _logger.LogWarning("User with ID {UserId} not found for avatar update.", userId);
                    return false;
                }

                user.Avatar = avatarUrl;
                user.UpdatedAt = DateTime.UtcNow;

                _context.Users.Update(user);
                await _context.SaveChangesAsync();
                _logger.LogInformation("Successfully updated avatar for user {UserId}.", userId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating avatar for user ID {UserId}", userId);
                return false;
            }
        }

        public async Task<bool> UpdateAsync(User user)
        {
            if (user == null)
            {
                throw new ArgumentNullException(nameof(user));
            }
            try
            {
                user.UpdatedAt = DateTime.UtcNow;
                _context.Users.Update(user);
                await _context.SaveChangesAsync();
                _logger.LogInformation("Successfully updated user {UserId}", user.Id);
                return true;
            }
            catch (DbUpdateConcurrencyException ex)
            {
                _logger.LogError(ex, "Concurrency error updating user {UserId}", user.Id);
                return false; 
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating user {UserId}", user.Id);
                return false;
            }
        }

        public async Task<List<Role>> GetUserRolesAsync(int userId)
        {
            try
            {
                var user = await _context.Users
                    .Include(u => u.UserRoles)
                        .ThenInclude(ur => ur.Role)
                    .AsNoTracking()
                    .FirstOrDefaultAsync(u => u.Id == userId);

                return user?.UserRoles.Select(ur => ur.Role).ToList() ?? new List<Role>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting roles for user {UserId}", userId);
                return new List<Role>();
            }
        }

        public async Task<List<Permission>> GetUserPermissionsAsync(int userId)
        {
            try
            {
                // 根据用户ID获取其所有角色ID
                var roleIds = await _context.UserRoles
                    .Where(ur => ur.UserId == userId)
                    .Select(ur => ur.RoleId)
                    .ToListAsync();

                if (!roleIds.Any())
                {
                    return new List<Permission>();
                }

                // 根据角色ID列表获取所有相关的权限
                var permissions = await _context.RolePermissions
                    .Where(rp => roleIds.Contains(rp.RoleId))
                    .Include(rp => rp.Permission)
                    .Select(rp => rp.Permission)
                    .Distinct()
                    .AsNoTracking()
                    .ToListAsync();

                return permissions;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting permissions for user {UserId}", userId);
                return new List<Permission>();
            }
        }
    }
} 