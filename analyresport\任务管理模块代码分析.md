# IT资产管理系统 - 任务管理模块代码分析

## 1. 模块概述

任务管理模块是IT资产管理系统的核心功能之一，负责系统中各类任务的创建、分配、跟踪和管理。该模块支持三种主要任务类型：常规任务、周期性任务和PDCA循环任务，并提供多种视图方式（列表、看板等）进行管理。

## 2. 文件结构与功能介绍

### 2.1 前端部分

#### 2.1.1 视图文件

| 文件路径 | 功能描述 |
|---------|---------|
| `frontend/src/views/tasks/index.vue` | 任务管理模块的布局容器，作为子页面的父级路由视图 |
| `frontend/src/views/tasks/list.vue` | 任务列表页面，以表格形式展示和管理所有任务 |
| `frontend/src/views/tasks/periodic.vue` | 周期任务页面，管理定期执行的任务（每日、每周、每月等） |
| `frontend/src/views/tasks/pdca.vue` | PDCA循环管理页面，管理计划-执行-检查-行动循环任务 |
| `frontend/src/views/tasks/board.vue` | 任务看板页面，以看板形式直观展示不同状态的任务 |

#### 2.1.2 API文件

| 文件路径 | 功能描述 |
|---------|---------|
| `frontend/src/api/task.js` | 任务管理相关的API服务封装，提供与后端通信的方法 |

### 2.2 后端部分

#### 2.2.1 控制器文件

| 文件路径 | 功能描述 |
|---------|---------|
| `Controllers/TaskController.cs` | 任务控制器，提供任务管理相关的API接口 |

## 3. 主要组件代码分析

### 3.1 任务管理布局容器（index.vue）

```vue
/**
 * 航空航天级IT资产管理系统 - 任务管理页面
 * 文件路径: src/views/tasks/index.vue
 * 功能描述: 任务管理模块的布局容器
 */

<template>
  <div class="task-container">
    <router-view v-slot="{ Component }">
      <keep-alive>
        <component :is="Component" />
      </keep-alive>
    </router-view>
  </div>
</template>

<script setup>
// 该组件仅作为任务管理子页面的容器
</script>

<style lang="scss" scoped>
@import "@/styles/variables.scss";

.task-container {
  padding: 16px;
  height: 100%;
}
</style>
```

**功能说明**：
- 作为任务管理模块的容器组件
- 使用`<router-view>`和`<keep-alive>`实现子路由视图的渲染和缓存
- 简洁的布局设计，仅提供基础样式和容器结构

### 3.2 任务列表页面（list.vue）

```vue
/**
 * 航空航天级IT资产管理系统 - 任务列表页面
 * 文件路径: src/views/tasks/list.vue
 * 功能描述: 展示和管理IT资产相关任务
 */

<template>
  <div class="task-list-container">
    <!-- 页面标题和操作按钮 -->
    <div class="page-header">
      <h2 class="page-title">任务列表</h2>
      <div class="page-actions">
        <el-button type="primary" @click="handleCreateTask" :icon="Plus">
          新建任务
        </el-button>
        <el-button type="primary" @click="handleExportData" :icon="Download">
          导出数据
        </el-button>
      </div>
    </div>

    <!-- 筛选条件 -->
    <el-card class="filter-card">
      <div class="filter-container">
        <el-form :inline="true" :model="filterForm" class="filter-form">
          <!-- 多种筛选条件 -->
          <el-form-item label="任务编号">
            <el-input v-model="filterForm.code" placeholder="任务编号" clearable />
          </el-form-item>
          <!-- 其他筛选条件项... -->
          <el-form-item>
            <el-button type="primary" @click="handleSearch" :icon="Search">
              搜索
            </el-button>
            <el-button @click="resetFilter" :icon="RefreshRight">
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="data-card">
      <el-tabs v-model="activeTab" @tab-click="handleTabChange">
        <el-tab-pane label="全部" name="all"></el-tab-pane>
        <el-tab-pane label="待处理" name="pending"></el-tab-pane>
        <el-tab-pane label="处理中" name="processing"></el-tab-pane>
        <el-tab-pane label="已完成" name="completed"></el-tab-pane>
        <el-tab-pane label="已逾期" name="overdue"></el-tab-pane>
      </el-tabs>
      
      <el-table
        ref="taskTable"
        v-loading="loading"
        :data="taskList"
        border
        style="width: 100%"
      >
        <!-- 多个表格列... -->
        <el-table-column prop="code" label="任务编号" width="120" sortable />
        <el-table-column prop="name" label="任务名称" min-width="180" show-overflow-tooltip />
        <!-- 其他表格列... -->
        <el-table-column label="操作" width="220" fixed="right">
          <template #default="scope">
            <el-button type="primary" text size="small" @click="handleViewDetail(scope.row)" :icon="View">详情</el-button>
            <el-button v-if="['pending', 'processing'].includes(scope.row.status)" type="success" text size="small" @click="handleUpdateProgress(scope.row)" :icon="Edit">更新进度</el-button>
            <el-button v-if="['pending', 'processing'].includes(scope.row.status)" type="success" text size="small" @click="handleComplete(scope.row)" :icon="Check">完成</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :background="true"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
// 脚本部分...
</script>
```

**功能说明**：
- 以表格形式展示所有任务
- 提供多种筛选条件（任务编号、名称、类型、优先级、状态、执行人、截止日期等）
- 采用标签页分类显示不同状态的任务
- 支持任务详情查看、进度更新和任务完成等操作
- 实现分页功能，支持自定义每页显示数量

### 3.3 周期任务页面（periodic.vue）

```vue
/**
 * 航空航天级IT资产管理系统 - 周期任务页面
 * 文件路径: src/views/tasks/periodic.vue
 * 功能描述: 展示和管理IT资产相关周期性任务
 */

<template>
  <div class="periodic-task-container">
    <!-- 页面标题和操作按钮 -->
    <div class="page-header">
      <h2 class="page-title">周期任务</h2>
      <div class="page-actions">
        <el-button type="primary" @click="handleCreateTask" :icon="Plus">
          新建周期任务
        </el-button>
        <el-button type="primary" @click="handleExportData" :icon="Download">
          导出数据
        </el-button>
      </div>
    </div>

    <!-- 筛选条件 -->
    <el-card class="filter-card">
      <!-- 筛选表单... -->
    </el-card>

    <!-- 数据表格 -->
    <el-card class="data-card">
      <el-table
        ref="taskTable"
        v-loading="loading"
        :data="taskList"
        border
        style="width: 100%"
      >
        <el-table-column prop="name" label="任务名称" min-width="180" show-overflow-tooltip />
        <el-table-column prop="type" label="类型" width="100">
          <template #default="scope">
            <el-tag :type="getTypeTag(scope.row.type)" size="small">
              {{ getTypeLabel(scope.row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="frequency" label="周期" width="120">
          <template #default="scope">
            <el-tag :type="getFrequencyTag(scope.row.frequency)" size="small">
              {{ getFrequencyLabel(scope.row.frequency) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="lastRunTime" label="上次执行" width="180" sortable />
        <el-table-column prop="nextRunTime" label="下次执行" width="180" sortable />
        <!-- 其他列... -->
        <el-table-column label="操作" width="230" fixed="right">
          <template #default="scope">
            <el-button type="primary" text size="small" @click="handleViewHistory(scope.row)" :icon="Histogram">执行历史</el-button>
            <el-button type="success" text size="small" @click="handleEdit(scope.row)" :icon="Edit">编辑</el-button>
            <!-- 条件渲染的操作按钮... -->
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <!-- 分页组件... -->
      </div>
    </el-card>
  </div>
</template>

<script setup>
// 脚本部分...
</script>
```

**功能说明**：
- 专门管理周期性任务（每日、每周、每月等定期执行的任务）
- 显示任务周期、上次执行时间和下次执行时间等特有属性
- 提供周期任务特有的操作功能：执行历史查看、暂停/激活等
- 支持周期任务的创建、编辑和删除

### 3.4 PDCA任务页面（pdca.vue）

```vue
/**
 * 航空航天级IT资产管理系统 - PDCA任务管理页面
 * 文件路径: src/views/tasks/pdca.vue
 * 功能描述: 计划(Plan)、执行(Do)、检查(Check)、行动(Act)循环任务管理
 */

<template>
  <div class="pdca-container">
    <!-- 页面标题和操作按钮 -->
    <div class="page-header">
      <h2 class="page-title">PDCA循环管理</h2>
      <div class="page-actions">
        <el-button type="primary" @click="handleCreatePdca" :icon="Plus">
          新建PDCA
        </el-button>
        <el-button type="primary" @click="handleExportData" :icon="Download">
          导出数据
        </el-button>
      </div>
    </div>

    <!-- 筛选条件 -->
    <el-card class="filter-card">
      <!-- 筛选表单... -->
    </el-card>

    <!-- 数据表格 -->
    <el-card class="data-card">
      <el-table
        ref="pdcaTable"
        v-loading="loading"
        :data="pdcaList"
        border
        style="width: 100%"
      >
        <!-- 表格列... -->
        <el-table-column label="PDCA进度" width="350">
          <template #default="scope">
            <div class="pdca-progress">
              <div class="progress-item" :class="{'completed': scope.row.planStatus === 'completed'}">
                <div class="progress-label">计划(P)</div>
                <el-progress 
                  :percentage="scope.row.planProgress" 
                  :status="scope.row.planStatus === 'completed' ? 'success' : ''"
                />
              </div>
              <!-- 其他PDCA阶段... -->
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="230" fixed="right">
          <template #default="scope">
            <el-button type="primary" text size="small" @click="handleViewDetail(scope.row)" :icon="View">详情</el-button>
            <el-button type="success" text size="small" @click="handleUpdateProgress(scope.row)" :icon="Edit">更新进度</el-button>
            <el-button type="danger" text size="small" @click="handleDelete(scope.row)" :icon="Delete">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <!-- 分页组件... -->
      </div>
    </el-card>
  </div>
</template>

<script setup>
// 脚本部分...
</script>
```

**功能说明**：
- 管理PDCA（计划-执行-检查-行动）循环项目
- 直观展示各PDCA阶段的进度和状态
- 支持更新各阶段进度、查看详情等操作
- 适用于需要严格执行PDCA管理方法的项目

### 3.5 任务看板页面（board.vue）

```vue
/**
 * 航空航天级IT资产管理系统 - 任务看板页面
 * 文件路径: src/views/tasks/board.vue
 * 功能描述: 以看板形式展示和管理IT资产相关任务
 */

<template>
  <div class="task-board-container">
    <!-- 页面标题和操作按钮 -->
    <div class="page-header">
      <h2 class="page-title">任务看板</h2>
      <div class="page-actions">
        <el-button type="primary">新建任务</el-button>
        <el-button>刷新</el-button>
      </div>
    </div>

    <!-- 筛选条件 -->
    <el-card class="filter-card">
      <!-- 筛选表单... -->
    </el-card>

    <!-- 看板 -->
    <div class="board-wrapper">
      <el-row :gutter="16">
        <el-col :span="6">
          <el-card class="board-column">
            <template #header>
              <div class="column-header">
                <el-tag type="info">待处理 (0)</el-tag>
              </div>
            </template>
            <div class="column-content">
              <!-- 任务卡片 -->
              <div class="task-card">
                <div class="task-title">示例任务</div>
                <div class="task-info">
                  <div>负责人: 张三</div>
                  <div>截止日期: 2023-12-31</div>
                </div>
                <el-progress :percentage="30"></el-progress>
                <div class="task-actions">
                  <el-button type="primary" size="small">详情</el-button>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        <!-- 其他状态列... -->
      </el-row>
    </div>
  </div>
</template>

<script setup>
// 简化的脚本部分
import { ref } from 'vue'

// 声明但不使用的变量，用于占位
const loading = ref(false)
</script>
```

**功能说明**：
- 以看板形式直观展示不同状态的任务
- 将任务分为待处理、处理中、已逾期、已完成四个状态列
- 每个任务以卡片形式展示，包含关键信息和进度
- 提供筛选功能，便于查找特定任务

### 3.6 任务API服务（task.js）

```javascript
/**
 * 航空航天级IT资产管理系统 - 任务管理API
 * 文件路径: src/api/task.js
 * 功能描述: 提供任务管理相关的API服务
 */

import request from '@/utils/request'

// 任务API基础路径
const baseUrl = '/tasks'

export default {
  /**
   * 获取任务列表
   * @param {Object} params - 查询参数
   * @returns {Promise}
   */
  getTaskList(params) {
    return request.get(baseUrl, params)
  },
  
  /**
   * 获取任务详情
   * @param {number|string} id - 任务ID
   * @returns {Promise}
   */
  getTaskById(id) {
    return request.get(`${baseUrl}/${id}`)
  },
  
  // 创建、更新、删除任务的方法...
  
  /**
   * 分配任务
   * @param {number|string} id - 任务ID
   * @param {Object} data - 分配数据
   * @returns {Promise}
   */
  assignTask(id, data) {
    return request.post(`${baseUrl}/${id}/assign`, data)
  },
  
  /**
   * 开始任务
   * @param {number|string} id - 任务ID
   * @returns {Promise}
   */
  startTask(id) {
    return request.put(`${baseUrl}/${id}/start`)
  },
  
  // 其他任务操作方法...
  
  /**
   * 获取我的任务列表
   * @param {Object} params - 查询参数
   * @returns {Promise}
   */
  getMyTasks(params) {
    return request.get(`${baseUrl}/my-tasks`, params)
  },
  
  // 任务统计、导出、附件相关方法...
}
```

**功能说明**：
- 封装与后端API通信的方法
- 提供完整的任务CRUD操作
- 包含任务生命周期管理（开始、暂停、完成、取消等）
- 支持任务分配、进度记录等功能
- 封装特殊查询接口（我的任务、任务统计等）

### 3.7 后端任务控制器（TaskController.cs）

```csharp
// IT资产管理系统 - 任务控制器
// 文件路径: /Controllers/TaskController.cs
// 功能: 提供任务管理相关API

using ItAssetsSystem.Core.Events;
using ItAssetsSystem.Core.Plugins;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ItAssetsSystem.Controllers
{
    /// <summary>
    /// 任务控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class TaskController : ControllerBase
    {
        private readonly ILogger<TaskController> _logger;
        private readonly ItAssetsSystem.Core.Events.IEventBus _eventBus;
        private readonly ITaskService _taskService;

        /// <summary>
        /// 构造函数
        /// </summary>
        public TaskController(
            ILogger<TaskController> logger,
            ItAssetsSystem.Core.Events.IEventBus eventBus,
            ITaskService taskService)
        {
            _logger = logger;
            _eventBus = eventBus;
            _taskService = taskService;
        }

        /// <summary>
        /// 获取所有任务
        /// </summary>
        [HttpGet]
        public IActionResult GetAll([FromQuery] int? type = null)
        {
            // 实现逻辑...
        }

        /// <summary>
        /// 获取我的任务
        /// </summary>
        [HttpGet("My")]
        public IActionResult GetMyTasks([FromQuery] int? userId = null)
        {
            // 实现逻辑...
        }

        /// <summary>
        /// 获取周期性任务
        /// </summary>
        [HttpGet("Periodic")]
        public IActionResult GetPeriodicTasks([FromQuery] int? status = null)
        {
            // 实现逻辑...
        }

        /// <summary>
        /// 获取PDCA计划任务
        /// </summary>
        [HttpGet("PDCA")]
        public IActionResult GetPdcaTasks([FromQuery] int? status = null)
        {
            // 实现逻辑...
        }

        // 其他API接口方法...
        
        /// <summary>
        /// 任务创建模型
        /// </summary>
        public class CreateTaskModel
        {
            /// <summary>
            /// 任务标题
            /// </summary>
            public string Title { get; set; }
            
            /// <summary>
            /// 任务类型
            /// </summary>
            public int TaskType { get; set; }
            
            // 其他属性...
        }
    }
}
```

**功能说明**：
- 提供任务管理相关的HTTP API接口
- 支持基本的任务CRUD操作
- 提供特殊任务查询接口（我的任务、周期任务、PDCA任务）
- 实现任务状态变更、完成等生命周期管理
- 当前实现使用模拟数据，可轻松替换为实际数据库操作

## 4. API接口列表

| HTTP方法 | 接口路径 | 功能描述 |
|---------|---------|---------|
| GET | /api/Task | 获取所有任务列表 |
| GET | /api/Task/{id} | 根据ID获取任务详情 |
| POST | /api/Task | 创建新任务 |
| PUT | /api/Task/{id} | 更新任务信息 |
| PUT | /api/Task/{id}/status | 更新任务状态 |
| PUT | /api/Task/{id}/complete | 完成任务 |
| GET | /api/Task/My | 获取我的任务 |
| GET | /api/Task/Periodic | 获取周期性任务 |
| GET | /api/Task/PDCA | 获取PDCA计划任务 |
| GET | /api/Task/{id}/progress-records | 获取任务进度记录 |
| POST | /api/Task/{id}/progress-records | 添加任务进度记录 |

## 5. 功能与交互

### 5.1 主要功能

1. **任务管理基础功能**：
   - 创建、查询、更新、删除任务
   - 任务分配与重新分配
   - 任务状态变更（开始、暂停、完成、取消）
   - 任务进度跟踪与更新

2. **周期任务管理**：
   - 创建周期性重复执行的任务
   - 管理不同周期类型（每日、每周、每月）
   - 监控执行历史和下次执行时间
   - 暂停/激活周期任务

3. **PDCA循环管理**：
   - 创建遵循PDCA方法的项目任务
   - 跟踪各PDCA阶段（计划、执行、检查、行动）的进度
   - 可视化PDCA各阶段完成情况
   - 支持整体项目进度管理

4. **多视图任务展示**：
   - 列表视图：详细信息展示和高级筛选
   - 看板视图：直观的状态分组和拖拽操作
   - 支持按多种属性排序和筛选

### 5.2 用户交互流程

1. **创建任务流程**：
   - 用户点击"新建任务"按钮
   - 填写任务基本信息（标题、描述、类型等）
   - 设置任务优先级和截止日期
   - 选择任务执行人
   - 提交任务创建请求

2. **任务状态管理流程**：
   - 查看任务详情
   - 点击相应操作按钮（开始、暂停、完成等）
   - 可选填写状态变更说明
   - 确认状态变更
   - 系统更新任务状态并记录历史

3. **进度更新流程**：
   - 在任务列表/详情中点击"更新进度"
   - 输入新的进度百分比
   - 可选填写进度说明
   - 提交进度更新
   - 系统记录进度变更历史

## 6. 技术实现

### 6.1 前端技术

- **框架**：Vue 3 + Element Plus
- **状态管理**：Pinia
- **路由**：Vue Router
- **HTTP通信**：Axios
- **UI组件库**：Element Plus
- **样式处理**：SCSS

### 6.2 后端技术

- **框架**：ASP.NET Core 6.0
- **语言**：C#
- **API风格**：RESTful
- **数据持久化**：Entity Framework Core
- **日志**：ILogger + Serilog
- **事件总线**：自定义IEventBus实现

## 7. 扩展与优化方向

1. **任务依赖关系**：实现任务之间的依赖关系，确保任务按正确顺序执行
2. **任务模板**：支持任务模板创建，快速生成常见任务
3. **任务自动化**：对于周期性任务，增加自动检查和状态更新
4. **批量操作**：支持批量任务创建、状态更新等操作
5. **高级筛选与导出**：增强筛选功能并支持自定义导出格式
6. **任务关联**：与资产、故障、采购等其他模块建立关联
7. **可拖拽看板**：实现完全可交互的看板视图，支持拖拽更改状态 