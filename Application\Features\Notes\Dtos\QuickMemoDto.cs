// File: Application/Features/Notes/Dtos/QuickMemoDto.cs
// Description: DTO for QuickMemo responses.
#nullable enable
using System;

namespace ItAssetsSystem.Application.Features.Notes.Dtos 
{
    public class QuickMemoDto
    {
        public string Id { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string? Content { get; set; }
        public string? CategoryId { get; set; }
        public string? CategoryName { get; set; } // For display
        public string? CategoryColor { get; set; } // For display
        public bool IsPinned { get; set; }
        public string? Color { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }

        // New properties for panel layout
        public double PositionX { get; set; }
        public double PositionY { get; set; }
        public int SizeWidth { get; set; }
        public int SizeHeight { get; set; }
        public int ZIndex { get; set; }

        public int UserId { get; set; } // 可选：如需前端用
        public string? UserName { get; set; } // 新增：用户名
    }
} 