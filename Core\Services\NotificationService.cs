using ItAssetsSystem.Core.Abstractions;
using ItAssetsSystem.Domain.Entities;
using ItAssetsSystem.Infrastructure.Data;
using ItAssetsSystem.Models.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Linq;
using Microsoft.AspNetCore.SignalR;
using ItAssetsSystem.Core.Hubs;

namespace ItAssetsSystem.Core.Services
{
    public class NotificationService : INotificationService
    {
        private readonly AppDbContext _context;
        private readonly ILogger<NotificationService> _logger;
        private readonly IHubContext<NotificationHub> _hubContext;

        public NotificationService(
            AppDbContext context, 
            ILogger<NotificationService> logger,
            IHubContext<NotificationHub> hubContext)
        {
            _context = context;
            _logger = logger;
            _hubContext = hubContext;
        }

        public async Task<Notification> CreateNotificationAsync(
            int userId,
            string title,
            string content,
            string type,
            string resourceType = null,
            long? resourceId = null,
            int? triggeredByUserId = null,
            string priority = "Normal")
        {
            var notification = new Notification
            {
                UserId = userId,
                Title = title,
                Content = content,
                Type = type,
                ReferenceType = resourceType,
                ReferenceId = resourceId,
                IsRead = false,
                CreationTimestamp = DateTime.Now
            };

            _context.Notifications.Add(notification);
            
            try {
                await _context.SaveChangesAsync();
                
                _logger.LogInformation("Created notification {NotificationId} for user {UserId}", 
                    notification.NotificationId, userId);

                // 实时推送通知给用户
                await SendRealTimeNotificationAsync(notification);

                return notification;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建通知失败: {Message}", ex.Message);
                throw;
            }
        }

        public async Task CreateNotificationsAsync(List<Notification> notifications)
        {
            if (notifications?.Any() == true)
            {
                try
                {
                    foreach (var notification in notifications)
                    {
                        if (notification.CreationTimestamp == default)
                        {
                            notification.CreationTimestamp = DateTime.Now;
                        }
                        
                        if (notification.ResourceType != null && notification.ReferenceType == null)
                        {
                            notification.ReferenceType = notification.ResourceType;
                        }
                        
                        if (notification.ResourceId.HasValue && !notification.ReferenceId.HasValue)
                        {
                            notification.ReferenceId = notification.ResourceId;
                        }
                        
                        // 确保不使用导航属性
                        notification.TriggeredByUserId = null;
                    }
                    
                    _context.Notifications.AddRange(notifications);
                    await _context.SaveChangesAsync();

                    _logger.LogInformation("Created {Count} notifications", notifications.Count);
                    
                    foreach (var notification in notifications)
                    {
                        await SendRealTimeNotificationAsync(notification);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "批量创建通知失败: {Message}", ex.Message);
                    throw;
                }
            }
        }

        public async Task<List<Notification>> GetUserNotificationsAsync(
            int userId, int page = 1, int pageSize = 20, bool? isRead = null)
        {
            var query = _context.Notifications
                .Where(n => n.UserId == userId);

            if (isRead.HasValue)
            {
                query = query.Where(n => n.IsRead == isRead.Value);
            }

            return await query
                .OrderByDescending(n => n.CreationTimestamp)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();
        }

        public async Task<int> GetUnreadCountAsync(int userId)
        {
            return await _context.Notifications
                .CountAsync(n => n.UserId == userId && !n.IsRead);
        }

        public async Task<int> GetTotalCountAsync(int userId)
        {
            return await _context.Notifications
                .CountAsync(n => n.UserId == userId);
        }

        public async Task MarkAsReadAsync(long notificationId, int userId)
        {
            var notification = await _context.Notifications
                .FirstOrDefaultAsync(n => n.NotificationId == notificationId && n.UserId == userId);

            if (notification != null && !notification.IsRead)
            {
                notification.IsRead = true;
                notification.ReadTimestamp = DateTime.UtcNow;
                await _context.SaveChangesAsync();

                _logger.LogInformation("Marked notification {NotificationId} as read for user {UserId}", 
                    notificationId, userId);
            }
        }

        public async Task MarkAllAsReadAsync(int userId)
        {
            var unreadNotifications = await _context.Notifications
                .Where(n => n.UserId == userId && !n.IsRead)
                .ToListAsync();

            if (unreadNotifications.Any())
            {
                foreach (var notification in unreadNotifications)
                {
                    notification.IsRead = true;
                    notification.ReadTimestamp = DateTime.UtcNow;
                }

                await _context.SaveChangesAsync();

                _logger.LogInformation("Marked {Count} notifications as read for user {UserId}", 
                    unreadNotifications.Count, userId);
            }
        }

        public async Task DeleteNotificationAsync(long notificationId, int userId)
        {
            var notification = await _context.Notifications
                .FirstOrDefaultAsync(n => n.NotificationId == notificationId && n.UserId == userId);

            if (notification != null)
            {
                _context.Notifications.Remove(notification);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Deleted notification {NotificationId} for user {UserId}", 
                    notificationId, userId);
            }
        }

        // 任务相关通知方法
        public async Task NotifyTaskCommentAsync(long taskId, long commentId, int commentedByUserId, string commentContent)
        {
            var task = await _context.Tasks.Include(t => t.Assignees).FirstOrDefaultAsync(t => t.TaskId == taskId);
            if (task == null) return;

            var commenterUser = await _context.Users.FirstOrDefaultAsync(u => u.Id == commentedByUserId);
            var commenterName = commenterUser?.Name ?? "未知用户";

            var notifications = new List<Notification>();

            // 通知所有负责人（除了评论者自己）
            foreach (var assignee in task.Assignees.Where(a => a.UserId != commentedByUserId))
            {
                notifications.Add(new Notification
                {
                    UserId = assignee.UserId,
                    Title = "任务有新评论",
                    Content = $"{commenterName} 在任务 \"{task.Name}\" 中添加了评论：{commentContent}",
                    Type = "TaskComment",
                    ReferenceType = "Task",
                    ReferenceId = taskId,
                    IsRead = false,
                    CreationTimestamp = DateTime.Now,
                    ResourceType = "Task",
                    ResourceId = taskId,
                    TriggeredByUserId = commentedByUserId,
                    Priority = "Normal",
                    CreatedAt = DateTime.Now,
                    ExtraData = $"{{\"commentId\":{commentId}}}"
                });
            }

            await CreateNotificationsAsync(notifications);
        }

        public async Task NotifyTaskStatusChangedAsync(long taskId, string oldStatus, string newStatus, int changedByUserId)
        {
            var task = await _context.Tasks.Include(t => t.Assignees).FirstOrDefaultAsync(t => t.TaskId == taskId);
            if (task == null) return;

            var changerUser = await _context.Users.FirstOrDefaultAsync(u => u.Id == changedByUserId);
            var changerName = changerUser?.Name ?? "未知用户";

            var notifications = new List<Notification>();

            // 通知所有负责人（除了更改者自己）
            foreach (var assignee in task.Assignees.Where(a => a.UserId != changedByUserId))
            {
                notifications.Add(new Notification
                {
                    UserId = assignee.UserId,
                    Title = "任务状态已更新",
                    Content = $"{changerName} 将任务 \"{task.Name}\" 状态从 \"{GetStatusDisplayName(oldStatus)}\" 更改为 \"{GetStatusDisplayName(newStatus)}\"",
                    Type = "TaskStatusChanged",
                    ReferenceType = "Task",
                    ReferenceId = taskId,
                    IsRead = false,
                    CreationTimestamp = DateTime.Now,
                    ResourceType = "Task",
                    ResourceId = taskId,
                    TriggeredByUserId = changedByUserId,
                    Priority = newStatus == "Completed" ? "High" : "Normal",
                    CreatedAt = DateTime.Now,
                    ExtraData = $"{{\"oldStatus\":\"{oldStatus}\",\"newStatus\":\"{newStatus}\"}}"
                });
            }

            await CreateNotificationsAsync(notifications);
        }

        public async Task NotifyTaskAssigneeChangedAsync(long taskId, List<int> oldAssigneeIds, List<int> newAssigneeIds, int changedByUserId)
        {
            var task = await _context.Tasks.FirstOrDefaultAsync(t => t.TaskId == taskId);
            if (task == null) return;

            var changerUser = await _context.Users.FirstOrDefaultAsync(u => u.Id == changedByUserId);
            var changerName = changerUser?.Name ?? "未知用户";

            var notifications = new List<Notification>();

            // 通知新分配的用户
            var addedAssigneeIds = newAssigneeIds.Except(oldAssigneeIds).ToList();
            foreach (var userId in addedAssigneeIds)
            {
                if (userId != changedByUserId)
                {
                    notifications.Add(new Notification
                    {
                        UserId = userId,
                        Title = "您被分配了新任务",
                        Content = $"{changerName} 将任务 \"{task.Name}\" 分配给了您",
                        Type = "TaskAssigned",
                        ReferenceType = "Task",
                        ReferenceId = taskId,
                        IsRead = false,
                        CreationTimestamp = DateTime.Now,
                        ResourceType = "Task",
                        ResourceId = taskId,
                        TriggeredByUserId = changedByUserId,
                        Priority = "High",
                        CreatedAt = DateTime.Now
                    });
                }
            }

            // 通知被移除的用户
            var removedAssigneeIds = oldAssigneeIds.Except(newAssigneeIds).ToList();
            foreach (var userId in removedAssigneeIds)
            {
                if (userId != changedByUserId)
                {
                    notifications.Add(new Notification
                    {
                        UserId = userId,
                        Title = "任务分配已取消",
                        Content = $"{changerName} 取消了您对任务 \"{task.Name}\" 的分配",
                        Type = "TaskUnassigned",
                        ReferenceType = "Task",
                        ReferenceId = taskId,
                        IsRead = false,
                        CreationTimestamp = DateTime.Now,
                        ResourceType = "Task",
                        ResourceId = taskId,
                        TriggeredByUserId = changedByUserId,
                        Priority = "Normal",
                        CreatedAt = DateTime.Now
                    });
                }
            }

            await CreateNotificationsAsync(notifications);
        }

        public async Task NotifyTaskOverdueAsync(long taskId)
        {
            var task = await _context.Tasks.Include(t => t.Assignees).FirstOrDefaultAsync(t => t.TaskId == taskId);
            if (task == null) return;

            var notifications = new List<Notification>();

            foreach (var assignee in task.Assignees)
            {
                notifications.Add(new Notification
                {
                    UserId = assignee.UserId,
                    Title = "任务已逾期",
                    Content = $"任务 \"{task.Name}\" 已超过截止日期，请尽快处理",
                    Type = "TaskOverdue",
                    ReferenceType = "Task",
                    ReferenceId = taskId,
                    IsRead = false,
                    CreationTimestamp = DateTime.Now,
                    ResourceType = "Task",
                    ResourceId = taskId,
                    Priority = "High",
                    CreatedAt = DateTime.Now
                });
            }

            await CreateNotificationsAsync(notifications);
        }

        public async Task NotifyTaskContentChangedAsync(long taskId, string changeDescription, int changedByUserId)
        {
            var task = await _context.Tasks.Include(t => t.Assignees).FirstOrDefaultAsync(t => t.TaskId == taskId);
            if (task == null) return;

            var changerUser = await _context.Users.FirstOrDefaultAsync(u => u.Id == changedByUserId);
            var changerName = changerUser?.Name ?? "未知用户";

            var notifications = new List<Notification>();

            foreach (var assignee in task.Assignees.Where(a => a.UserId != changedByUserId))
            {
                notifications.Add(new Notification
                {
                    UserId = assignee.UserId,
                    Title = "任务内容已更新",
                    Content = $"{changerName} 更新了任务 \"{task.Name}\"：{changeDescription}",
                    Type = "TaskContentChanged",
                    ReferenceType = "Task",
                    ReferenceId = taskId,
                    IsRead = false,
                    CreationTimestamp = DateTime.Now,
                    ResourceType = "Task",
                    ResourceId = taskId,
                    TriggeredByUserId = changedByUserId,
                    Priority = "Normal",
                    CreatedAt = DateTime.Now
                });
            }

            await CreateNotificationsAsync(notifications);
        }

        public async Task NotifyTaskAttachmentAddedAsync(long taskId, long attachmentId, int uploadedByUserId, string fileName)
        {
            var task = await _context.Tasks.Include(t => t.Assignees).FirstOrDefaultAsync(t => t.TaskId == taskId);
            if (task == null) return;

            var uploaderUser = await _context.Users.FirstOrDefaultAsync(u => u.Id == uploadedByUserId);
            var uploaderName = uploaderUser?.Name ?? "未知用户";

            var notifications = new List<Notification>();

            foreach (var assignee in task.Assignees.Where(a => a.UserId != uploadedByUserId))
            {
                notifications.Add(new Notification
                {
                    UserId = assignee.UserId,
                    Title = "任务添加了新附件",
                    Content = $"{uploaderName} 为任务 \"{task.Name}\" 添加了附件：{fileName}",
                    Type = "TaskAttachmentAdded",
                    ReferenceType = "Task",
                    ReferenceId = taskId,
                    IsRead = false,
                    CreationTimestamp = DateTime.Now,
                    ResourceType = "Task",
                    ResourceId = taskId,
                    TriggeredByUserId = uploadedByUserId,
                    Priority = "Normal",
                    CreatedAt = DateTime.Now,
                    ExtraData = $"{{\"attachmentId\":{attachmentId},\"fileName\":\"{fileName}\"}}"
                });
            }

            await CreateNotificationsAsync(notifications);
        }

        public async Task NotifyTaskMentionAsync(long taskId, List<int> mentionedUserIds, int mentionedByUserId, string content)
        {
            var task = await _context.Tasks.FirstOrDefaultAsync(t => t.TaskId == taskId);
            if (task == null) return;

            var mentionerUser = await _context.Users.FirstOrDefaultAsync(u => u.Id == mentionedByUserId);
            var mentionerName = mentionerUser?.Name ?? "未知用户";

            var notifications = new List<Notification>();

            foreach (var userId in mentionedUserIds.Where(id => id != mentionedByUserId))
            {
                notifications.Add(new Notification
                {
                    UserId = userId,
                    Title = "您在任务中被提及",
                    Content = $"{mentionerName} 在任务 \"{task.Name}\" 中提及了您：{content}",
                    Type = "TaskMention",
                    ReferenceType = "Task",
                    ReferenceId = taskId,
                    IsRead = false,
                    CreationTimestamp = DateTime.Now,
                    ResourceType = "Task",
                    ResourceId = taskId,
                    TriggeredByUserId = mentionedByUserId,
                    Priority = "High",
                    CreatedAt = DateTime.Now
                });
            }

            await CreateNotificationsAsync(notifications);
        }

        public async Task NotifyTaskCreatedAsync(long taskId, string taskName, int creatorUserId, int? assigneeUserId, string taskType)
        {
            if (!assigneeUserId.HasValue)
                return; // 没有负责人，无需通知

            // 对于周期性任务，即使负责人是创建者也要发送通知
            if (taskType != "Periodic" && assigneeUserId.Value == creatorUserId)
                return; // 普通任务不需要通知自己

            var creatorUser = await _context.Users.FirstOrDefaultAsync(u => u.Id == creatorUserId);
            var creatorName = creatorUser?.Name ?? "未知用户";

            string notificationTitle;
            string notificationContent;

            if (taskType == "Periodic")
            {
                notificationTitle = "您有新的周期性任务";
                notificationContent = $"系统为您自动生成了周期性任务：\"{taskName}\"";
            }
            else
            {
                notificationTitle = "您被分配了新任务";
                notificationContent = $"{creatorName} 为您分配了新任务：\"{taskName}\"";
            }

            var notification = new Notification
            {
                UserId = assigneeUserId.Value,
                Title = notificationTitle,
                Content = notificationContent,
                Type = "TaskCreated",
                ReferenceType = "Task",
                ReferenceId = taskId,
                IsRead = false,
                CreationTimestamp = DateTime.Now,
                ResourceType = "Task",
                ResourceId = taskId,
                TriggeredByUserId = taskType == "Periodic" ? null : creatorUserId,
                Priority = "High",
                CreatedAt = DateTime.Now
            };

            _logger.LogInformation("🔔 准备创建任务创建通知: TaskId={TaskId}, TaskName={TaskName}, AssigneeUserId={AssigneeUserId}, TaskType={TaskType}",
                taskId, taskName, assigneeUserId, taskType);

            await CreateNotificationAsync(
                notification.UserId,
                notification.Title,
                notification.Content,
                notification.Type,
                notification.ReferenceType,
                notification.ReferenceId,
                notification.TriggeredByUserId,
                notification.Priority
            );

            _logger.LogInformation("✅ 任务创建通知已发送: TaskId={TaskId}, UserId={UserId}", taskId, assigneeUserId);
        }

        private string GetStatusDisplayName(string status)
        {
            return status switch
            {
                "Todo" => "待办",
                "InProgress" => "进行中",
                "Completed" => "已完成",
                "Cancelled" => "已取消",
                _ => status
            };
        }

        // 添加新方法用于发送实时通知
        private async Task SendRealTimeNotificationAsync(Notification notification)
        {
            try
            {
                // 构建通知数据
                var notificationData = new
                {
                    id = notification.NotificationId,
                    type = notification.Type,
                    title = notification.Title,
                    message = notification.Content,
                    timestamp = notification.CreationTimestamp,
                    taskId = notification.ReferenceType == "Task" ? notification.ReferenceId : null,
                    read = notification.IsRead
                };

                // 发送到特定用户的组
                string userGroup = $"user_{notification.UserId}";
                _logger.LogInformation("尝试向组 {UserGroup} 发送实时通知", userGroup);
                
                await _hubContext.Clients.Group(userGroup).SendAsync("ReceiveNotification", notificationData);
                
                _logger.LogInformation("实时通知已发送到用户 {UserId}, 通知ID: {NotificationId}, 组名: {UserGroup}", 
                    notification.UserId, notification.NotificationId, userGroup);
                    
                // 同时尝试向所有连接的客户端发送，以防用户尚未加入组
                await _hubContext.Clients.All.SendAsync("ReceiveNotification", notificationData);
                _logger.LogInformation("实时通知也已广播到所有连接的客户端");
            }
            catch (Exception ex)
            {
                // 实时通知发送失败不应该影响正常流程，仅记录错误
                _logger.LogError(ex, "发送实时通知失败, 用户ID: {UserId}, 通知ID: {NotificationId}", 
                    notification.UserId, notification.NotificationId);
            }
        }
    }
}