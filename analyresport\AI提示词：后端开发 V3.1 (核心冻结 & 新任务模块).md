## **AI提示词：航空航天级IT资产与任务系统后端开发 V3.1 (核心冻结 & 新任务模块)**

**I. 使命与核心策略 (Overall Goal & Strategy):**

你的核心使命是**扩展**现有的“航空航天级IT资产与任务管理平台”后端系统，首要任务是**全新开发任务管理系统**。

**关键策略 (V2.3 \- 核心冻结 & 新模块隔离):**

1. **核心模块冻结 (强制):** **绝对禁止**修改现有核心业务模块 (Asset, AssetType, Location, Personnel, User, Department 等) 的**任何内部代码逻辑**（包括数据访问方式混用）和**对外 API 接口**（/api/v1/... 及其返回结构）。这些模块被视为稳定且已与前端适配的“黑盒”。只允许在修复严重 Bug 时进行最小化、不破坏接口的改动。  
2. **新模块隔离开发 (强制):** 所有新功能模块（从本阶段的任务管理开始）**必须**按照本提示词定义的现代化规范（.NET 8.0, Clean Architecture, EF Core, DTOs, CQRS/MediatR）进行**全新、独立的开发**。  
3. **新旧 API 分离 (强制):** 新模块的 API 端点**必须**使用新的版本前缀（例如 /api/v2/tasks/...），与核心模块的 V1 API 严格区分。

**总体目标:** 在确保现有核心系统绝对稳定的前提下，高质量、高标准地构建新的功能模块，并为未来可能的整体架构统一奠定基础。

**II. Cursor规则 (AI 代码生成强制规范):**

**在生成任何代码之前，必须回顾并严格遵守《Cursor规则：AI后端代码生成强制规范 V1.0》文档中的所有规定。**

**III. 不可动摇的架构原则与技术强制要求 (适用于所有新模块):**

1. **目标框架:** **.NET 8.0 LTS**。  
2. **核心架构:** **严格遵守 Clean Architecture** (Domain, Application, Infrastructure, Api)。  
3. **数据访问:** **强制 EF Core 8** \+ **仓储模式**。**禁止原生 SQL**。  
4. **API 设计规范:** RESTful, **强制 DTOs**, **强制标准响应结构**, HTTP 状态码规范, **强制 API 版本控制** (/api/v2/... 用于新模块)。  
5. **异步优先:** **强制 async/await** 用于 I/O 操作。  
6. **结构化日志:** **强制 Serilog**，覆盖所有层，记录详细信息，输出到控制台和滚动文件 (Logs/)。  
7. **统一错误处理:** **强制全局异常处理中间件** \+ 自定义业务异常。  
8. **全面测试:** **强制高单元测试覆盖率 (\>85%)** \+ 集成测试。使用 xUnit/NUnit。  
9. **代码质量标准 (替代行数限制):** **强制低复杂度**, **强制 SOLID**, **强制清晰注释**, **强制一致风格**。  
10. **模块化与可扩展性:** **强制 SRP**, **强制按特性组织代码**。  
11. **配置管理:** 使用 appsettings.json, 环境变量, IOptions\<T\>。  
12. **安全:** **强制 JWT Bearer 认证**, **强制基于角色的授权**, **强制输入验证**, 防范 OWASP Top 10。  
13. **数据库 Schema:** **新表** (Tasks, Gamification 等) **必须**使用 **BIGINT AUTO\_INCREMENT** 主键。**核心表** (Users, assets, locations 等) **保持 INT 主键**。在 EF Core 配置中正确处理关系。

**IV. 当前阶段任务：实现任务管理系统 (Task Management \- New Implementation \- API V2)**

**目标:** 基于**新**的 Tasks 表 (BIGINT PK) 和 Clean Architecture 实现完整的任务管理功能（普通、周期、PDCA）。API 路由使用 /api/v2/tasks/...。

**关键实体/组件:**

* **Domain:** Task (新, BIGINT PK), Comment (新, BIGINT PK), Attachment (新, BIGINT PK), TaskHistory (新, BIGINT PK), PeriodicTaskSchedule (新, BIGINT PK), PdcaPlan (可选, 新, BIGINT PK), 相关枚举 (TaskStatus, TaskPriority, TaskType), 领域事件 (TaskCreatedEvent, TaskCompletedEvent 等), 仓储接口 (ITaskRepository, ICommentRepository 等)。  
* **Application:** 任务相关的 DTOs (TaskItemDto, TaskDetailsDto, CreateTaskCommand, UpdateTaskStatusCommand, AddCommentCommand, UploadAttachmentCommand 等), Commands/Queries/Handlers (使用 MediatR), ITaskService (可选的应用服务接口), 事件处理器 (TaskHistoryHandler, TaskCompletedGamificationHandler \- 后者在此阶段仅定义接口和基本结构，具体游戏化逻辑在下一阶段实现), AutoMapper 配置。  
* **Infrastructure:** TaskRepository, CommentRepository 等仓储实现 (使用 EF Core), AppDbContext 中添加新实体的 DbSet 和 Fluent API 配置, Hangfire/Quartz.NET 集成 (用于周期任务)。  
* **Api:** 任务相关的 Endpoints (TasksEndpoints.cs 或 TaskControllerV2.cs，路由为 /api/v2/tasks/...)，处理 HTTP 请求，调用 MediatR 发送 Command/Query，返回标准响应 DTO。

**关键特性实现要求:**

1. **任务 CRUD API (/api/v2/tasks/...):**  
   * POST /: 创建新任务 (普通、周期、PDCA 基础)。接收 CreateTaskCommand DTO，返回包含新任务 ID 的 201 Created 响应。**必须**能关联现有的 Asset (INT), Location (INT), User (INT \- 负责人/创建者)。  
   * GET /: 获取任务列表（分页、过滤、排序）。接收查询参数 DTO，返回 PaginatedList\<TaskItemDto\>。支持按状态、类型、优先级、负责人、截止日期等过滤。  
   * GET /{taskId}: 获取任务详情。返回 TaskDetailsDto，包含任务基本信息、评论、附件等（根据需要决定是否一次性加载）。  
   * PUT /{taskId}: 更新任务详情。接收 UpdateTaskCommand DTO。  
   * PATCH /{taskId}/status: 更新任务状态。接收 UpdateTaskStatusCommand DTO。  
   * DELETE /{taskId}: 删除任务。  
2. **任务操作 API:**  
   * POST /{taskId}/assign: 分配负责人。接收 AssignTaskCommand DTO。  
   * POST /{taskId}/progress: 更新进度。接收 UpdateTaskProgressCommand DTO。  
   * POST /{taskId}/claim: 认领任务。接收 ClaimTaskCommand DTO。  
3. **评论 API (/api/v2/tasks/{taskId}/comments/...):**  
   * POST /: 添加评论。接收 AddCommentCommand DTO (支持 @提及用户 ID 列表)。  
   * GET /: 获取评论列表（分页）。  
   * PUT /{commentId}: 编辑评论。  
   * DELETE /{commentId}: 删除评论。  
   * POST /{commentId}/pin: 置顶评论 (可选)。  
4. **附件 API (/api/v2/tasks/{taskId}/attachments/...):**  
   * POST /upload: 上传附件。处理文件上传，保存文件（本地或云存储 \- 在 Infrastructure 实现），并在 Attachments 表中记录信息。  
   * GET /: 获取附件列表。  
   * GET /{attachmentId}/download: 下载附件。  
   * DELETE /{attachmentId}: 删除附件（同时删除物理文件）。  
5. **周期性任务:**  
   * 实现 PeriodicTaskSchedule 实体的 CRUD API (/api/v2/periodic-schedules/..., DTOs)。  
   * 在 Infrastructure/BackgroundJobs 中实现后台作业（使用 Hangfire 或 Quartz.NET），定期检查 PeriodicTaskSchedules 表，根据 RecurrenceRule (使用 RRule 格式或 Cron) 和 NextGenerationTimestamp 生成新的 Task 实例。**必须**处理好下次执行时间的计算和更新。  
6. **PDCA 任务:**  
   * 实现 PdcaPlan 实体（可选，如果需要独立管理计划）和相关 API。  
   * 在 Task 实体中包含 PdcaPlanId 和 PDCAStage 字段。  
   * 提供 API 更新任务的 PDCA 阶段。  
7. **任务历史记录:**  
   * **必须**通过监听领域事件（TaskCreatedEvent, TaskStatusChangedEvent, TaskAssignedEvent, TaskDetailsUpdatedEvent, CommentAddedEvent, AttachmentAddedEvent 等）来触发 TaskHistoryHandler (Application/Features/Tasks/EventHandlers)。  
   * TaskHistoryHandler 负责创建并保存 TaskHistory 记录到数据库。

**与核心模块的交互:**

* **读取:** 新的任务模块需要显示用户名称、资产名称、位置名称等。**首选方案:** 通过注入一个专门的 ICoreDataQueryService (接口在 Application，实现在 Infrastructure) 来封装对核心模块**现有 V1 API** 的 HTTP 调用。**备选方案 (谨慎使用):** 如果 API 调用不可行，则在 ICoreDataQueryService 的实现中使用 IAppDbContext 对 CoreUsers, CoreAssets, CoreLocations 等进行**只读** (AsNoTracking()) 查询。  
* **关联:** 创建或更新任务时，需要关联核心实体 ID。**必须**使用核心实体现有的 **INT** 类型 ID (例如 AssigneeUserId (INT?), AssetId (INT?), LocationId (INT?))。EF Core 配置中要正确处理这些外键关系。  
* **写入:** **严禁**任务模块直接修改核心模块的数据表。

**V. 文件夹结构约定 (强制):**

**必须**严格遵守以下按层级和功能特性组织的文件夹结构：

└── src/  
    ├── Domain/  
    │   ├── Entities/  
    │   │   ├── Core/ (User, Role, Department, Location, Asset, AssetType) \# 核心实体 (只读引用)  
    │   │   ├── Tasks/ (新 Task, Comment, Attachment 等)  
    │   │   └── Gamification/ (新 Gamification 实体)  
    │   ├── Enums/  
    │   ├── Events/ (按特性分子目录, 如 Tasks/, Gamification/)  
    │   ├── Interfaces/ (仓储接口 IUserRepository, ITaskRepository 等)  
    │   ├── ValueObjects/ (可选)  
    │   └── Exceptions/ (自定义领域异常)  
    ├── Application/  
    │   ├── Common/  
    │   │   ├── Interfaces/ (IAppDbContext, ICurrentUserService, etc.)  
    │   │   ├── Mappings/ (AutoMapper Profile, IMapFrom)  
    │   │   ├── Models/ (PaginatedList)  
    │   │   └── Behaviors/ (MediatR Pipeline Behaviors)  
    │   └── Features/         \# 按功能特性组织  
    │       ├── Auth/         \# (已在阶段 0 实现)  
    │       ├── Users/        \# (已在阶段 0 实现)  
    │       ├── Locations/    \# (已在阶段 1 实现)  
    │       ├── Assets/       \# (已在阶段 2 实现)  
    │       ├── Tasks/        \# \--- 当前阶段: 新任务模块 \---  
    │       │   ├── Commands/  
    │       │   ├── Queries/  
    │       │   ├── Dtos/  
    │       │   └── EventHandlers/  
    │       ├── Comments/     \# (任务模块子特性)  
    │       ├── Attachments/  \# (任务模块子特性)  
    │       ├── PeriodicTasks/\# (任务模块子特性)  
    │       ├── PdcaPlans/    \# (任务模块子特性)  
    │       └── ... (其他新特性 Gamification, Notifications 等)  
    ├── Infrastructure/  
    │   ├── Persistence/      \# 数据持久化  
    │   │   ├── AppDbContext.cs  
    │   │   ├── Configurations/ (EF Core Fluent API 配置 \- 包含新旧实体)  
    │   │   └── Repositories/ (仓储实现 \- 包含新旧实体仓储)  
    │   ├── Identity/         \# 身份认证实现  
    │   ├── Notifications/    \# 通知实现  
    │   ├── BackgroundJobs/   \# 后台作业  
    │   ├── FileStorage/      \# 文件存储  
    │   ├── Services/         \# 基础设施服务  
    │   └── ...  
    └── Api/                  \# 表示层 (Web API)  
        ├── Endpoints/        \# API 端点 (按特性和版本组织)  
        │   ├── V1/           \# (核心模块 API \- 保持不变)  
        │   │   ├── AssetsEndpoints.cs  
        │   │   └── LocationsEndpoints.cs  
        │   └── V2/           \# (新模块 API)  
        │       ├── TasksEndpoints.cs  
        │       ├── CommentsEndpoints.cs  
        │       ├── AttachmentsEndpoints.cs  
        │       └── PeriodicSchedulesEndpoints.cs  
        ├── Middleware/       \# 中间件  
        ├── Services/         \# API 层特定服务  
        └── Program.cs        \# DI 设置, 中间件管道配置

**VI. 当前阶段验证清单 (任务管理模块):**

1. **编译与运行:** 包含新任务模块的代码能否成功编译并运行？  
2. **API 功能:**  
   * 任务 CRUD (/api/v2/tasks/...) 是否按预期工作（使用 DTO）？  
   * 任务状态、分配、进度、认领等操作 API 是否正常？  
   * 评论 CRUD API (/api/v2/tasks/{taskId}/comments/...) 是否正常？@提及是否能正确处理？  
   * 附件上传、下载、删除 API (/api/v2/tasks/{taskId}/attachments/...) 是否正常？文件是否正确存储/删除？  
   * 周期性任务计划 CRUD API (/api/v2/periodic-schedules/...) 是否正常？  
   * 任务列表查询 API 是否支持按各种条件过滤和分页，并返回正确的 PaginatedList\<TaskItemDto\>？  
3. **周期性任务:** 后台作业是否能根据 PeriodicTaskSchedule 的规则按时生成新的 Task 实例？下次执行时间是否正确更新？  
4. **历史记录:** 对任务的各种操作（创建、状态变更、分配、详情更新、评论、附件）是否都正确地记录到了 TaskHistory 表中？  
5. **与核心模块交互:**  
   * 创建/更新任务时，能否正确关联现有的用户、资产、位置 (使用 INT IDs)？  
   * 查询任务列表或详情时，能否正确显示关联的核心实体的名称（通过 API 调用或只读查询）？  
6. **架构与规范:**  
   * 代码是否严格遵循 Clean Architecture 和文件夹结构约定？  
   * API 是否严格使用 DTO 和标准响应结构？  
   * 是否仅使用 EF Core 进行数据访问？  
   * 日志和错误处理是否符合规范？  
7. **测试:** 任务模块相关的单元测试和集成测试是否已编写并通过？覆盖率是否达标？

**完成并验证好任务管理模块后，请告知，我们将进入下一阶段（游戏化与通知）。**