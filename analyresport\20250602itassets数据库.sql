/*
 Navicat Premium Data Transfer

 Source Server         : IT资产
 Source Server Type    : MySQL
 Source Server Version : 80029 (8.0.29)
 Source Host           : localhost:3306
 Source Schema         : itassets

 Target Server Type    : MySQL
 Target Server Version : 80029 (8.0.29)
 File Encoding         : 65001

 Date: 02/06/2025 22:17:26
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for __efmigrationshistory
-- ----------------------------
DROP TABLE IF EXISTS `__efmigrationshistory`;
CREATE TABLE `__efmigrationshistory`  (
  `MigrationId` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `ProductVersion` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  PRIMARY KEY (`MigrationId`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'EF迁移历史表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for _script_execution_log
-- ----------------------------
DROP TABLE IF EXISTS `_script_execution_log`;
CREATE TABLE `_script_execution_log`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `step` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
  `executed_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 29 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for assethistories
-- ----------------------------
DROP TABLE IF EXISTS `assethistories`;
CREATE TABLE `assethistories`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `AssetId` int NOT NULL COMMENT '资产ID',
  `OperationType` int NOT NULL COMMENT '操作类型：1创建，2修改，3删除，4位置变更，5状态变更',
  `OperatorId` int NOT NULL COMMENT '操作人ID',
  `OperationTime` datetime NOT NULL COMMENT '操作时间',
  `Description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '描述（JSON格式，记录变更前后的属性值）',
  `CreatedAt` datetime NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`) USING BTREE,
  INDEX `IX_AssetHistories_AssetId`(`AssetId` ASC) USING BTREE,
  INDEX `IX_AssetHistories_OperatorId`(`OperatorId` ASC) USING BTREE,
  CONSTRAINT `FK_AssetHistories_Assets_AssetId` FOREIGN KEY (`AssetId`) REFERENCES `assets` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_AssetHistories_Users_OperatorId` FOREIGN KEY (`OperatorId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 69 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '资产历史表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for assetreceives
-- ----------------------------
DROP TABLE IF EXISTS `assetreceives`;
CREATE TABLE `assetreceives`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `ReceiveCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '入库单号',
  `PurchaseOrderId` int NULL DEFAULT NULL COMMENT '采购订单ID',
  `Title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '标题',
  `Description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '描述',
  `ReceiverId` int NOT NULL COMMENT '接收人ID',
  `ReceiveTime` datetime NOT NULL COMMENT '接收时间',
  `Status` int NOT NULL DEFAULT 0 COMMENT '状态：0草稿，1已提交，2已确认',
  `AssetTypeId` int NULL DEFAULT NULL COMMENT '资产类型ID',
  `Quantity` int NOT NULL DEFAULT 1 COMMENT '数量',
  `TotalAmount` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '总金额',
  `InitialLocationId` int NULL DEFAULT NULL COMMENT '初始位置ID',
  `Notes` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `CreatedAt` datetime NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE INDEX `IX_AssetReceives_ReceiveCode`(`ReceiveCode` ASC) USING BTREE,
  INDEX `IX_AssetReceives_PurchaseOrderId`(`PurchaseOrderId` ASC) USING BTREE,
  INDEX `IX_AssetReceives_ReceiverId`(`ReceiverId` ASC) USING BTREE,
  INDEX `IX_AssetReceives_AssetTypeId`(`AssetTypeId` ASC) USING BTREE,
  INDEX `IX_AssetReceives_InitialLocationId`(`InitialLocationId` ASC) USING BTREE,
  CONSTRAINT `FK_AssetReceives_AssetTypes_AssetTypeId` FOREIGN KEY (`AssetTypeId`) REFERENCES `assettypes` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_AssetReceives_Locations_InitialLocationId` FOREIGN KEY (`InitialLocationId`) REFERENCES `locations` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_AssetReceives_PurchaseOrders_PurchaseOrderId` FOREIGN KEY (`PurchaseOrderId`) REFERENCES `purchaseorders` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_AssetReceives_Users_ReceiverId` FOREIGN KEY (`ReceiverId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '资产入库表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for assets
-- ----------------------------
DROP TABLE IF EXISTS `assets`;
CREATE TABLE `assets`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `assetCode` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '资产编号(IT-类型-年月日-序号)',
  `FinancialCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '财务编号',
  `Name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '资产名称',
  `AssetTypeId` int NOT NULL COMMENT '资产类型ID',
  `SerialNumber` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '序列号',
  `Model` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '型号',
  `Brand` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '品牌',
  `PurchaseDate` datetime NULL DEFAULT NULL COMMENT '购买日期',
  `WarrantyExpireDate` datetime NULL DEFAULT NULL COMMENT '保修到期日',
  `Price` decimal(18, 2) NULL DEFAULT NULL COMMENT '价格',
  `LocationId` int NULL DEFAULT NULL COMMENT '位置ID',
  `DepartmentId` int NULL DEFAULT NULL,
  `Status` int NOT NULL DEFAULT 0 COMMENT '状态：0闲置，1在用，2维修中，3报废',
  `Notes` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `CreatedAt` datetime NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime NOT NULL COMMENT '更新时间',
  `asset_code_prefix` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'IT' COMMENT '资产编号前缀',
  `inventory_id` int NULL DEFAULT NULL COMMENT '来源库存ID',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE INDEX `IX_Assets_AssetCode`(`assetCode` ASC) USING BTREE,
  INDEX `IX_Assets_AssetTypeId`(`AssetTypeId` ASC) USING BTREE,
  INDEX `IX_Assets_LocationId`(`LocationId` ASC) USING BTREE,
  INDEX `IX_Assets_FinancialCode`(`FinancialCode` ASC) USING BTREE,
  INDEX `IX_Assets_DepartmentId`(`DepartmentId` ASC) USING BTREE,
  CONSTRAINT `FK_Assets_AssetTypes_AssetTypeId` FOREIGN KEY (`AssetTypeId`) REFERENCES `assettypes` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_Assets_Departments_DepartmentId` FOREIGN KEY (`DepartmentId`) REFERENCES `departments` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_Assets_Locations_LocationId` FOREIGN KEY (`LocationId`) REFERENCES `locations` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1103 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '资产表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for assettypes
-- ----------------------------
DROP TABLE IF EXISTS `assettypes`;
CREATE TABLE `assettypes`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `Name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '类型名称',
  `Code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '类型编码',
  `Description` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '类型描述',
  `ParentId` int NULL DEFAULT NULL COMMENT '父类型ID',
  `RequireSerialNumber` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否需要序列号',
  `SortOrder` int NOT NULL DEFAULT 0 COMMENT '排序',
  `IsActive` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否激活',
  `CreatedAt` datetime NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE INDEX `IX_AssetTypes_Code`(`Code` ASC) USING BTREE,
  UNIQUE INDEX `IX_AssetTypes_Name`(`Name` ASC) USING BTREE,
  INDEX `IX_AssetTypes_ParentId`(`ParentId` ASC) USING BTREE,
  CONSTRAINT `FK_AssetTypes_AssetTypes_ParentId` FOREIGN KEY (`ParentId`) REFERENCES `assettypes` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 32 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '资产类型表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for attachments
-- ----------------------------
DROP TABLE IF EXISTS `attachments`;
CREATE TABLE `attachments`  (
  `AttachmentId` bigint NOT NULL AUTO_INCREMENT COMMENT '附件ID (BIGINT)',
  `TaskId` bigint NULL DEFAULT NULL COMMENT '关联的任务ID',
  `CommentId` bigint NULL DEFAULT NULL COMMENT '关联的评论ID',
  `UploaderUserId` int NOT NULL COMMENT '上传用户ID (关联 users.Id - INT)',
  `FileName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '原始文件名',
  `StoredFileName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '存储文件名 (避免重复，建议UUID)',
  `FilePath` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '文件存储路径 (相对路径或包含Bucket信息)',
  `FileSize` bigint NOT NULL COMMENT '文件大小 (字节)',
  `FileType` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '文件MIME类型',
  `IsPreviewable` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否可在线预览',
  `StorageType` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'Local' COMMENT '存储类型 (Local, S3, AzureBlob)',
  `CreationTimestamp` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`AttachmentId`) USING BTREE,
  UNIQUE INDEX `idx_attachments_storedfilename`(`StoredFileName` ASC) USING BTREE,
  INDEX `idx_attachments_task`(`TaskId` ASC) USING BTREE,
  INDEX `idx_attachments_comment`(`CommentId` ASC) USING BTREE,
  INDEX `idx_attachments_uploader`(`UploaderUserId` ASC) USING BTREE,
  CONSTRAINT `FK_Attachments_Comment` FOREIGN KEY (`CommentId`) REFERENCES `comments` (`CommentId`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `FK_Attachments_Task` FOREIGN KEY (`TaskId`) REFERENCES `tasks` (`TaskId`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `FK_Attachments_UploaderUser` FOREIGN KEY (`UploaderUserId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '任务附件表 (V2 - BIGINT PK)' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for auditlogs
-- ----------------------------
DROP TABLE IF EXISTS `auditlogs`;
CREATE TABLE `auditlogs`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `UserId` int NULL DEFAULT NULL COMMENT '用户ID',
  `UserName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '用户名',
  `Type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '日志类型',
  `TableName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '表名',
  `DateTime` datetime NOT NULL COMMENT '日期时间',
  `PrimaryKey` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '主键',
  `OldValues` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '旧值',
  `NewValues` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '新值',
  `Action` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '操作',
  `ClientIP` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '客户端IP',
  PRIMARY KEY (`Id`) USING BTREE,
  INDEX `IX_AuditLogs_UserId`(`UserId` ASC) USING BTREE,
  CONSTRAINT `FK_AuditLogs_Users_UserId` FOREIGN KEY (`UserId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '审计日志表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for comments
-- ----------------------------
DROP TABLE IF EXISTS `comments`;
CREATE TABLE `comments`  (
  `CommentId` bigint NOT NULL AUTO_INCREMENT COMMENT '评论ID (BIGINT)',
  `TaskId` bigint NOT NULL COMMENT '关联的任务ID',
  `UserId` int NOT NULL COMMENT '评论用户ID (关联 users.Id - INT)',
  `ParentCommentId` bigint NULL DEFAULT NULL COMMENT '父评论ID (用于回复)',
  `Content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '评论内容 (支持Markdown或HTML)',
  `MentionedUserIds` json NULL COMMENT '评论中@提及的用户ID列表 (INT IDs in JSON array)',
  `IsPinned` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否置顶',
  `IsEdited` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否被编辑过',
  `CreationTimestamp` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `LastUpdatedTimestamp` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`CommentId`) USING BTREE,
  INDEX `idx_comments_task_time`(`TaskId` ASC, `CreationTimestamp` ASC) USING BTREE,
  INDEX `idx_comments_user`(`UserId` ASC) USING BTREE,
  INDEX `idx_comments_parent`(`ParentCommentId` ASC) USING BTREE,
  CONSTRAINT `FK_Comments_ParentComment` FOREIGN KEY (`ParentCommentId`) REFERENCES `comments` (`CommentId`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `FK_Comments_Task` FOREIGN KEY (`TaskId`) REFERENCES `tasks` (`TaskId`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `FK_Comments_User` FOREIGN KEY (`UserId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 23 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '任务评论表 (V2 - BIGINT PK)' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for daily_reports
-- ----------------------------
DROP TABLE IF EXISTS `daily_reports`;
CREATE TABLE `daily_reports`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `report_date` date NOT NULL,
  `report_type` enum('daily','weekly','monthly') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'daily',
  `file_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '报表文件路径',
  `snapshot_data` json NULL COMMENT '数据快照',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_report_date_type`(`report_date` ASC, `report_type` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for dashboard_config
-- ----------------------------
DROP TABLE IF EXISTS `dashboard_config`;
CREATE TABLE `dashboard_config`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `theme` enum('light','dark') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'dark',
  `layout` json NULL COMMENT '面板布局配置',
  `auto_refresh` int NULL DEFAULT 60 COMMENT '刷新间隔(秒)',
  `updated_at` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_dashboard_user`(`user_id` ASC) USING BTREE,
  CONSTRAINT `fk_dashboard_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`Id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for db_structure_result
-- ----------------------------
DROP TABLE IF EXISTS `db_structure_result`;
CREATE TABLE `db_structure_result`  (
  `section` varchar(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '',
  `表名` varchar(64) CHARACTER SET utf8 COLLATE utf8_tolower_ci NULL DEFAULT NULL,
  `列名` varchar(64) CHARACTER SET utf8 COLLATE utf8_tolower_ci NULL DEFAULT NULL,
  `数据类型` mediumtext CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `可为空` varchar(3) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
  `默认值` text CHARACTER SET utf8 COLLATE utf8_bin NULL,
  `键` enum('','PRI','UNI','MUL') CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `额外信息` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `注释` text CHARACTER SET utf8 COLLATE utf8_bin NOT NULL
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for departments
-- ----------------------------
DROP TABLE IF EXISTS `departments`;
CREATE TABLE `departments`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `Name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '部门名称',
  `Code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '部门编码',
  `ParentId` int NULL DEFAULT NULL COMMENT '父部门ID',
  `ManagerId` int NULL DEFAULT NULL COMMENT '部门经理ID',
  `DeputyManagerId` int NULL DEFAULT NULL COMMENT '部门主任ID（二级负责人）',
  `Path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '层级路径',
  `Description` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '部门描述',
  `IsActive` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否激活',
  `CreatedAt` datetime NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE INDEX `IX_Departments_Code`(`Code` ASC) USING BTREE,
  UNIQUE INDEX `IX_Departments_Name`(`Name` ASC) USING BTREE,
  INDEX `IX_Departments_ParentId`(`ParentId` ASC) USING BTREE,
  INDEX `IX_Departments_ManagerId`(`ManagerId` ASC) USING BTREE,
  INDEX `IX_Departments_DeputyManagerId`(`DeputyManagerId` ASC) USING BTREE,
  CONSTRAINT `FK_Departments_Departments_ParentId` FOREIGN KEY (`ParentId`) REFERENCES `departments` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_Departments_Personnel_DeputyManagerId` FOREIGN KEY (`DeputyManagerId`) REFERENCES `personnel` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 31 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '部门表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for faultrecords
-- ----------------------------
DROP TABLE IF EXISTS `faultrecords`;
CREATE TABLE `faultrecords`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `Title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '标题',
  `Description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '描述',
  `AssetId` int NULL DEFAULT NULL COMMENT '资产ID（线下设备时可为空）',
  `FaultTypeId` int NOT NULL COMMENT '故障类型ID',
  `LocationId` int NULL DEFAULT NULL COMMENT '位置ID',
  `ReporterId` int NOT NULL COMMENT '报告人ID',
  `ReportTime` datetime NOT NULL COMMENT '报告时间',
  `Status` int NOT NULL DEFAULT 0 COMMENT '状态：0待处理，1处理中，2已解决，3已关闭',
  `Severity` int NOT NULL DEFAULT 0 COMMENT '严重程度：0一般，1严重，2紧急',
  `AssigneeId` int NULL DEFAULT NULL COMMENT '处理人ID',
  `AssignTime` datetime NULL DEFAULT NULL COMMENT '分配时间',
  `ResponseTime` datetime NULL DEFAULT NULL COMMENT '响应时间',
  `ResolutionTime` datetime NULL DEFAULT NULL COMMENT '解决时间',
  `Resolution` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '解决方案',
  `RootCause` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '根本原因',
  `Notes` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `IsReturned` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否返厂',
  `CreatedAt` datetime NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime NOT NULL COMMENT '更新时间',
  `faultNumber` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '故障单号(FIX-年月日-序号)',
  `expected_return_date` datetime NULL DEFAULT NULL COMMENT '预期返厂日期',
  `warning_flag` tinyint(1) NULL DEFAULT 0 COMMENT '超期预警标记',
  PRIMARY KEY (`Id`) USING BTREE,
  INDEX `IX_FaultRecords_AssetId`(`AssetId` ASC) USING BTREE,
  INDEX `IX_FaultRecords_FaultTypeId`(`FaultTypeId` ASC) USING BTREE,
  INDEX `IX_FaultRecords_LocationId`(`LocationId` ASC) USING BTREE,
  INDEX `IX_FaultRecords_ReporterId`(`ReporterId` ASC) USING BTREE,
  INDEX `IX_FaultRecords_AssigneeId`(`AssigneeId` ASC) USING BTREE,
  INDEX `idx_faultrecords_faultNumber`(`faultNumber` ASC) USING BTREE,
  CONSTRAINT `FK_FaultRecords_Assets_AssetId` FOREIGN KEY (`AssetId`) REFERENCES `assets` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_FaultRecords_FaultTypes_FaultTypeId` FOREIGN KEY (`FaultTypeId`) REFERENCES `faulttypes` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_FaultRecords_Locations_LocationId` FOREIGN KEY (`LocationId`) REFERENCES `locations` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_FaultRecords_Users_AssigneeId` FOREIGN KEY (`AssigneeId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_FaultRecords_Users_ReporterId` FOREIGN KEY (`ReporterId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '故障记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for faulttypes
-- ----------------------------
DROP TABLE IF EXISTS `faulttypes`;
CREATE TABLE `faulttypes`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `Name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '类型名称',
  `Code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '类型编码',
  `Description` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '类型描述',
  `Severity` int NOT NULL DEFAULT 0 COMMENT '严重程度：0一般，1严重，2紧急',
  `SuggestedResponseTime` int NULL DEFAULT NULL COMMENT '建议响应时间（小时）',
  `SuggestedResolutionTime` int NULL DEFAULT NULL COMMENT '建议解决时间（小时）',
  `SortOrder` int NOT NULL DEFAULT 0 COMMENT '排序',
  `IsActive` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否激活',
  `CreatedAt` datetime NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE INDEX `IX_FaultTypes_Code`(`Code` ASC) USING BTREE,
  UNIQUE INDEX `IX_FaultTypes_Name`(`Name` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 16 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '故障类型表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for gamification_badges
-- ----------------------------
DROP TABLE IF EXISTS `gamification_badges`;
CREATE TABLE `gamification_badges`  (
  `BadgeId` bigint NOT NULL AUTO_INCREMENT COMMENT '徽章ID (BIGINT)',
  `Code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '徽章唯一代码',
  `Name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '徽章名称',
  `Description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '徽章描述及获得条件',
  `IconUrl` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '徽章图标URL',
  `TriggerEvent` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '触发事件类型',
  `TriggerThreshold` int NULL DEFAULT NULL COMMENT '触发阈值',
  `RequiredLevel` int NULL DEFAULT NULL COMMENT '获得所需最低等级',
  `PointsAwarded` int NOT NULL DEFAULT 0 COMMENT '获得徽章时奖励的积分',
  `XPAwarded` int NOT NULL DEFAULT 0 COMMENT '获得徽章时奖励的XP',
  `IsRepeatable` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否可重复获得',
  `IsActive` tinyint(1) NOT NULL DEFAULT 1 COMMENT '徽章是否启用',
  `CreationTimestamp` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`BadgeId`) USING BTREE,
  UNIQUE INDEX `idx_gamification_badges_code`(`Code` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '游戏化徽章定义表 (BIGINT PK)' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for gamification_items
-- ----------------------------
DROP TABLE IF EXISTS `gamification_items`;
CREATE TABLE `gamification_items`  (
  `ItemId` bigint NOT NULL AUTO_INCREMENT COMMENT '物品ID (BIGINT)',
  `Code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '物品唯一代码',
  `Name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '物品名称',
  `Description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '物品描述',
  `IconUrl` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '物品图标URL',
  `Type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '物品类型',
  `Effect` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '物品效果描述',
  `PointsCost` int NOT NULL DEFAULT 0 COMMENT '购买所需积分',
  `Cooldown` int NULL DEFAULT NULL COMMENT '使用冷却时间(分钟)',
  `IsActive` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否激活',
  `CreationTimestamp` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`ItemId`) USING BTREE,
  UNIQUE INDEX `idx_gamification_items_code`(`Code` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '游戏化物品定义表 (BIGINT PK)' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for gamification_log
-- ----------------------------
DROP TABLE IF EXISTS `gamification_log`;
CREATE TABLE `gamification_log`  (
  `LogId` bigint NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `UserId` bigint NOT NULL COMMENT '关联用户ID',
  `Timestamp` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '事件发生时间',
  `EventType` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '事件类型 (如 TaskCompleted, BadgeEarned, LevelUp, PointsSpent, DailyLogin, CommentAdded)',
  `XPChange` int NOT NULL DEFAULT 0 COMMENT '经验值变动',
  `PointsChange` int NOT NULL DEFAULT 0 COMMENT '积分变动',
  `LevelBefore` int NULL DEFAULT NULL COMMENT '变动前等级',
  `LevelAfter` int NULL DEFAULT NULL COMMENT '变动后等级',
  `Reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '变动原因文字描述',
  `RelatedTaskId` bigint NULL DEFAULT NULL COMMENT '关联的任务ID',
  `RelatedCommentId` bigint NULL DEFAULT NULL COMMENT '关联的评论ID',
  `RelatedBadgeId` bigint NULL DEFAULT NULL COMMENT '关联的徽章ID',
  `RelatedItemId` bigint NULL DEFAULT NULL COMMENT '关联的物品ID (如果有点数商城)',
  `Metadata` json NULL COMMENT '其他元数据 (例如完成任务时的详情)',
  PRIMARY KEY (`LogId`) USING BTREE,
  INDEX `idx_gamification_log_user_time`(`UserId` ASC, `Timestamp` ASC) USING BTREE,
  INDEX `idx_gamification_log_event_type`(`EventType` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '游戏化事件日志表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for gamification_userbadges
-- ----------------------------
DROP TABLE IF EXISTS `gamification_userbadges`;
CREATE TABLE `gamification_userbadges`  (
  `UserBadgeId` bigint NOT NULL AUTO_INCREMENT COMMENT '用户徽章记录ID (BIGINT)',
  `UserId` int NOT NULL COMMENT '用户ID (关联 users.Id - INT)',
  `BadgeId` bigint NOT NULL COMMENT '徽章ID',
  `EarnedTimestamp` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `Count` int NOT NULL DEFAULT 1 COMMENT '获得次数',
  PRIMARY KEY (`UserBadgeId`) USING BTREE,
  INDEX `idx_userbadges_user_badge`(`UserId` ASC, `BadgeId` ASC) USING BTREE,
  INDEX `idx_userbadges_badge`(`BadgeId` ASC) USING BTREE,
  CONSTRAINT `FK_UserBadges_Badge` FOREIGN KEY (`BadgeId`) REFERENCES `gamification_badges` (`BadgeId`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `FK_UserBadges_User` FOREIGN KEY (`UserId`) REFERENCES `users` (`Id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户获得的徽章记录表 (BIGINT PK)' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for gamification_useritems
-- ----------------------------
DROP TABLE IF EXISTS `gamification_useritems`;
CREATE TABLE `gamification_useritems`  (
  `UserItemId` bigint NOT NULL AUTO_INCREMENT COMMENT '用户物品ID (BIGINT)',
  `UserId` int NOT NULL COMMENT '用户ID (关联 users.Id - INT)',
  `ItemId` bigint NOT NULL COMMENT '物品ID',
  `Quantity` int NOT NULL DEFAULT 1 COMMENT '物品数量',
  `AcquiredTimestamp` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '获得时间',
  `LastUsedTimestamp` datetime NULL DEFAULT NULL COMMENT '上次使用时间',
  `ExpiryTimestamp` datetime NULL DEFAULT NULL COMMENT '过期时间(如果有)',
  PRIMARY KEY (`UserItemId`) USING BTREE,
  UNIQUE INDEX `idx_useritems_user_item`(`UserId` ASC, `ItemId` ASC) USING BTREE,
  INDEX `idx_useritems_item`(`ItemId` ASC) USING BTREE,
  CONSTRAINT `FK_UserItems_Item` FOREIGN KEY (`ItemId`) REFERENCES `gamification_items` (`ItemId`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `FK_UserItems_User` FOREIGN KEY (`UserId`) REFERENCES `users` (`Id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户拥有的物品表 (BIGINT PK)' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for gamification_userstats
-- ----------------------------
DROP TABLE IF EXISTS `gamification_userstats`;
CREATE TABLE `gamification_userstats`  (
  `UserId` bigint NOT NULL COMMENT '用户统计关联ID (BIGINT PK, 逻辑关联 users.Id)',
  `CoreUserId` int NOT NULL COMMENT '对应的核心用户ID (关联 users.Id - INT, 用于查询)',
  `CurrentXP` int NOT NULL DEFAULT 0 COMMENT '当前经验值',
  `CurrentLevel` int NOT NULL DEFAULT 1 COMMENT '当前等级',
  `PointsBalance` int NOT NULL DEFAULT 0 COMMENT '当前可用积分',
  `CompletedTasksCount` int NOT NULL DEFAULT 0 COMMENT '累计完成任务数',
  `OnTimeTasksCount` int NOT NULL DEFAULT 0 COMMENT '累计按时完成任务数',
  `StreakCount` int NOT NULL DEFAULT 0 COMMENT '当前连续活动/完成任务天数',
  `LastActivityTimestamp` datetime NULL DEFAULT NULL COMMENT '最后活跃时间戳',
  `LastStreakTimestamp` date NULL DEFAULT NULL COMMENT '上次增加连续记录的日期',
  `LastUpdatedTimestamp` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`UserId`) USING BTREE,
  UNIQUE INDEX `idx_gamification_userstats_coreuserid`(`CoreUserId` ASC) USING BTREE,
  CONSTRAINT `FK_UserStats_CoreUser` FOREIGN KEY (`CoreUserId`) REFERENCES `users` (`Id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '游戏化用户统计表 (BIGINT PK)' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for inventory_thresholds
-- ----------------------------
DROP TABLE IF EXISTS `inventory_thresholds`;
CREATE TABLE `inventory_thresholds`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `asset_type_id` int NOT NULL,
  `min_threshold` int NOT NULL DEFAULT 5 COMMENT '最小安全库存',
  `warning_threshold` int NOT NULL DEFAULT 10 COMMENT '预警库存',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_inventory_asset_type`(`asset_type_id` ASC) USING BTREE,
  CONSTRAINT `fk_inventory_asset_type` FOREIGN KEY (`asset_type_id`) REFERENCES `assettypes` (`Id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for locationhistories
-- ----------------------------
DROP TABLE IF EXISTS `locationhistories`;
CREATE TABLE `locationhistories`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `AssetId` int NOT NULL COMMENT '资产ID',
  `OldLocationId` int NULL DEFAULT NULL COMMENT '旧位置ID',
  `NewLocationId` int NOT NULL COMMENT '新位置ID',
  `OperatorId` int NOT NULL COMMENT '操作人ID',
  `ChangeType` int NOT NULL DEFAULT 0 COMMENT '变更类型：0转移，1领用，2归还',
  `ChangeTime` datetime NOT NULL COMMENT '变更时间',
  `Notes` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `CreatedAt` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`Id`) USING BTREE,
  INDEX `IX_LocationHistories_AssetId`(`AssetId` ASC) USING BTREE,
  INDEX `IX_LocationHistories_OldLocationId`(`OldLocationId` ASC) USING BTREE,
  INDEX `IX_LocationHistories_NewLocationId`(`NewLocationId` ASC) USING BTREE,
  INDEX `IX_LocationHistories_OperatorId`(`OperatorId` ASC) USING BTREE,
  CONSTRAINT `FK_LocationHistories_Assets_AssetId` FOREIGN KEY (`AssetId`) REFERENCES `assets` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_LocationHistories_Locations_NewLocationId` FOREIGN KEY (`NewLocationId`) REFERENCES `locations` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_LocationHistories_Locations_OldLocationId` FOREIGN KEY (`OldLocationId`) REFERENCES `locations` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_LocationHistories_Users_OperatorId` FOREIGN KEY (`OperatorId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 13 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '位置历史表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for locations
-- ----------------------------
DROP TABLE IF EXISTS `locations`;
CREATE TABLE `locations`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `Code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '位置编码',
  `Name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '位置名称',
  `Type` int NOT NULL DEFAULT 0 COMMENT '位置类型：0厂区，1产线，2工序，3工位，4设备位置',
  `ParentId` int NULL DEFAULT NULL COMMENT '父位置ID',
  `Path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '层级路径',
  `Description` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '位置描述',
  `DefaultDepartmentId` int NULL DEFAULT NULL COMMENT '默认部门ID',
  `DefaultResponsiblePersonId` int NULL DEFAULT NULL COMMENT '默认负责人ID',
  `IsActive` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否激活',
  `CreatedAt` datetime NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime NOT NULL COMMENT '更新时间',
  `level` tinyint NOT NULL DEFAULT 3 COMMENT '位置级别(1-5)',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE INDEX `IX_Locations_Code`(`Code` ASC) USING BTREE,
  INDEX `IX_Locations_ParentId`(`ParentId` ASC) USING BTREE,
  INDEX `IX_Locations_DefaultDepartmentId`(`DefaultDepartmentId` ASC) USING BTREE,
  INDEX `IX_Locations_DefaultResponsiblePersonId`(`DefaultResponsiblePersonId` ASC) USING BTREE,
  INDEX `idx_locations_level`(`level` ASC) USING BTREE,
  CONSTRAINT `FK_Locations_Departments_DefaultDepartmentId` FOREIGN KEY (`DefaultDepartmentId`) REFERENCES `departments` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_Locations_Locations_ParentId` FOREIGN KEY (`ParentId`) REFERENCES `locations` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_Locations_Users_DefaultResponsiblePersonId` FOREIGN KEY (`DefaultResponsiblePersonId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 293 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '位置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for locationusers
-- ----------------------------
DROP TABLE IF EXISTS `locationusers`;
CREATE TABLE `locationusers`  (
  `location_id` int NOT NULL,
  `personnel_id` int NOT NULL,
  `user_type` tinyint NOT NULL COMMENT '0-使用人 1-管理员',
  `created_at` datetime NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`location_id`, `personnel_id`, `user_type`) USING BTREE,
  INDEX `personnel_id`(`personnel_id` ASC) USING BTREE,
  CONSTRAINT `locationusers_ibfk_1` FOREIGN KEY (`location_id`) REFERENCES `locations` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `locationusers_ibfk_2` FOREIGN KEY (`personnel_id`) REFERENCES `personnel` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for maintenanceorders
-- ----------------------------
DROP TABLE IF EXISTS `maintenanceorders`;
CREATE TABLE `maintenanceorders`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `OrderCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '维护单号',
  `Title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '标题',
  `Description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '描述',
  `AssetId` int NOT NULL COMMENT '资产ID',
  `FaultRecordId` int NULL DEFAULT NULL COMMENT '故障记录ID',
  `MaintenanceType` int NOT NULL DEFAULT 0 COMMENT '维护类型：0常规维护，1故障维修，2返厂维修跟踪',
  `Status` int NOT NULL DEFAULT 0 COMMENT '状态：0待处理，1处理中，2已完成，3已取消',
  `CreatorId` int NOT NULL COMMENT '创建人ID',
  `CreateTime` datetime NOT NULL COMMENT '创建时间',
  `AssigneeId` int NULL DEFAULT NULL COMMENT '处理人ID',
  `AssignTime` datetime NULL DEFAULT NULL COMMENT '分配时间',
  `PlanStartTime` datetime NULL DEFAULT NULL COMMENT '计划开始时间',
  `PlanEndTime` datetime NULL DEFAULT NULL COMMENT '计划结束时间',
  `ActualStartTime` datetime NULL DEFAULT NULL COMMENT '实际开始时间',
  `ActualEndTime` datetime NULL DEFAULT NULL COMMENT '实际结束时间',
  `Solution` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '解决方案',
  `Notes` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `CreatedAt` datetime NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE INDEX `IX_MaintenanceOrders_OrderCode`(`OrderCode` ASC) USING BTREE,
  INDEX `IX_MaintenanceOrders_AssetId`(`AssetId` ASC) USING BTREE,
  INDEX `IX_MaintenanceOrders_FaultRecordId`(`FaultRecordId` ASC) USING BTREE,
  INDEX `IX_MaintenanceOrders_CreatorId`(`CreatorId` ASC) USING BTREE,
  INDEX `IX_MaintenanceOrders_AssigneeId`(`AssigneeId` ASC) USING BTREE,
  CONSTRAINT `FK_MaintenanceOrders_Assets_AssetId` FOREIGN KEY (`AssetId`) REFERENCES `assets` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_MaintenanceOrders_FaultRecords_FaultRecordId` FOREIGN KEY (`FaultRecordId`) REFERENCES `faultrecords` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_MaintenanceOrders_Users_AssigneeId` FOREIGN KEY (`AssigneeId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_MaintenanceOrders_Users_CreatorId` FOREIGN KEY (`CreatorId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '维护订单表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for menus
-- ----------------------------
DROP TABLE IF EXISTS `menus`;
CREATE TABLE `menus`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `Name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '菜单名称',
  `Code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '菜单编码',
  `ParentId` int NULL DEFAULT NULL COMMENT '父菜单ID',
  `Type` int NOT NULL DEFAULT 0 COMMENT '菜单类型：0菜单，1按钮',
  `Icon` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '图标',
  `Path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '路由路径',
  `Component` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '组件路径',
  `Permission` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '权限标识',
  `Description` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '描述',
  `SortOrder` int NOT NULL DEFAULT 0 COMMENT '排序',
  `IsExternal` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否外链',
  `KeepAlive` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否缓存',
  `IsVisible` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否可见',
  `IsActive` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否激活',
  `CreatedAt` datetime NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE INDEX `IX_Menus_Code`(`Code` ASC) USING BTREE,
  INDEX `IX_Menus_ParentId`(`ParentId` ASC) USING BTREE,
  CONSTRAINT `FK_Menus_Menus_ParentId` FOREIGN KEY (`ParentId`) REFERENCES `menus` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 27 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '菜单表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for notifications
-- ----------------------------
DROP TABLE IF EXISTS `notifications`;
CREATE TABLE `notifications`  (
  `NotificationId` bigint NOT NULL AUTO_INCREMENT COMMENT '通知ID (BIGINT)',
  `UserId` int NOT NULL COMMENT '接收用户ID (关联 users.Id - INT)',
  `Title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `Content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `Type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '通知类型：task/system/alert/gamification',
  `ReferenceId` bigint NULL DEFAULT NULL COMMENT '关联对象ID (可能是 TaskId, BadgeId etc - BIGINT)',
  `ReferenceType` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '关联对象类型 (Task, Badge, etc)',
  `IsRead` tinyint(1) NOT NULL DEFAULT 0,
  `CreationTimestamp` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `ReadTimestamp` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`NotificationId`) USING BTREE,
  INDEX `idx_notifications_user_read_time`(`UserId` ASC, `IsRead` ASC, `CreationTimestamp` ASC) USING BTREE,
  CONSTRAINT `FK_Notifications_User` FOREIGN KEY (`UserId`) REFERENCES `users` (`Id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 34 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '通知表 (BIGINT PK)' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for pdcaplans
-- ----------------------------
DROP TABLE IF EXISTS `pdcaplans`;
CREATE TABLE `pdcaplans`  (
  `pdca_plan_id` bigint NOT NULL AUTO_INCREMENT,
  `task_id` bigint NOT NULL,
  `creator_user_id` int NOT NULL,
  `responsible_person_id` int NOT NULL,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `stage` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `goal` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
  `plan_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
  `do_record` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
  `check_result` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
  `act_action` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
  `status` int NOT NULL,
  `completion_rate` decimal(5, 2) NOT NULL,
  `notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
  `creation_timestamp` datetime(6) NOT NULL,
  `last_updated_timestamp` datetime(6) NOT NULL,
  PRIMARY KEY (`pdca_plan_id`) USING BTREE,
  UNIQUE INDEX `IX_pdcaplans_task_id`(`task_id` ASC) USING BTREE,
  INDEX `IX_pdcaplans_creator_user_id`(`creator_user_id` ASC) USING BTREE,
  INDEX `IX_pdcaplans_responsible_person_id`(`responsible_person_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for periodic_task_schedule_assignees
-- ----------------------------
DROP TABLE IF EXISTS `periodic_task_schedule_assignees`;
CREATE TABLE `periodic_task_schedule_assignees`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `periodic_task_schedule_id` bigint NOT NULL,
  `user_id` int NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_schedule_user`(`periodic_task_schedule_id` ASC, `user_id` ASC) USING BTREE,
  INDEX `idx_schedule_id`(`periodic_task_schedule_id` ASC) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 17 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for periodictaskschedules
-- ----------------------------
DROP TABLE IF EXISTS `periodictaskschedules`;
CREATE TABLE `periodictaskschedules`  (
  `periodic_task_schedule_id` bigint NOT NULL AUTO_INCREMENT,
  `template_task_id` bigint NOT NULL,
  `creator_user_id` int NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
  `recurrence_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `recurrence_interval` int NOT NULL,
  `days_of_week` json NULL,
  `day_of_month` int NULL DEFAULT NULL,
  `week_of_month` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `day_of_week_for_month` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `month_of_year` int NULL DEFAULT NULL,
  `cron_expression` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `start_date` datetime(6) NOT NULL,
  `end_condition_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `end_date` datetime(6) NULL DEFAULT NULL,
  `total_occurrences` int NULL DEFAULT NULL,
  `occurrences_generated` int NOT NULL,
  `next_generation_time` datetime(6) NOT NULL,
  `status` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `last_generated_timestamp` datetime(6) NULL DEFAULT NULL,
  `last_error` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
  `default_points` int NOT NULL,
  `creation_timestamp` datetime(6) NOT NULL,
  `last_updated_timestamp` datetime(6) NOT NULL,
  PRIMARY KEY (`periodic_task_schedule_id`) USING BTREE,
  INDEX `IX_periodictaskschedules_creator_user_id`(`creator_user_id` ASC) USING BTREE,
  INDEX `IX_periodictaskschedules_template_task_id`(`template_task_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for personnel
-- ----------------------------
DROP TABLE IF EXISTS `personnel`;
CREATE TABLE `personnel`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `position` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `contact` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `department_id` int NULL DEFAULT NULL,
  `employee_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `created_at` datetime NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `department_id`(`department_id` ASC) USING BTREE,
  CONSTRAINT `personnel_ibfk_1` FOREIGN KEY (`department_id`) REFERENCES `departments` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 20 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for point_leaderboard
-- ----------------------------
DROP TABLE IF EXISTS `point_leaderboard`;
CREATE TABLE `point_leaderboard`  (
  `LeaderboardEntryId` bigint NOT NULL AUTO_INCREMENT COMMENT '排行榜条目ID (BIGINT)',
  `UserId` int NOT NULL COMMENT '用户ID (关联 users.Id - INT)',
  `LeaderboardType` int NOT NULL COMMENT '排行榜类型 (e.g., 1=Weekly, 2=Monthly, 3=AllTime)',
  `LeaderboardPeriod` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '排行榜周期标识 (e.g., 2024-W20, 2024-05, AllTime)',
  `Points` int NOT NULL DEFAULT 0,
  `Rank` int NOT NULL,
  `CreationTimestamp` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `LastUpdatedTimestamp` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`LeaderboardEntryId`) USING BTREE,
  UNIQUE INDEX `idx_leaderboard_unique`(`UserId` ASC, `LeaderboardType` ASC, `LeaderboardPeriod` ASC) USING BTREE,
  INDEX `idx_leaderboard_type_period_rank`(`LeaderboardType` ASC, `LeaderboardPeriod` ASC, `Rank` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '积分排行榜 (BIGINT PK)' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for purchaseitems
-- ----------------------------
DROP TABLE IF EXISTS `purchaseitems`;
CREATE TABLE `purchaseitems`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `PurchaseOrderId` int NOT NULL COMMENT '采购订单ID',
  `ItemName` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '项目名称',
  `ItemCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '项目编码',
  `Specification` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '规格',
  `AssetTypeId` int NULL DEFAULT NULL COMMENT '资产类型ID',
  `UnitPrice` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '单价',
  `Quantity` int NOT NULL DEFAULT 1 COMMENT '数量',
  `TotalPrice` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '总价',
  `Notes` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `CreatedAt` datetime NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`) USING BTREE,
  INDEX `IX_PurchaseItems_PurchaseOrderId`(`PurchaseOrderId` ASC) USING BTREE,
  INDEX `IX_PurchaseItems_AssetTypeId`(`AssetTypeId` ASC) USING BTREE,
  CONSTRAINT `FK_PurchaseItems_AssetTypes_AssetTypeId` FOREIGN KEY (`AssetTypeId`) REFERENCES `assettypes` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_PurchaseItems_PurchaseOrders_PurchaseOrderId` FOREIGN KEY (`PurchaseOrderId`) REFERENCES `purchaseorders` (`Id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '采购项目表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for purchaseorders
-- ----------------------------
DROP TABLE IF EXISTS `purchaseorders`;
CREATE TABLE `purchaseorders`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `OrderCode` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '采购单号(PO-年月日-时分秒)',
  `Title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '标题',
  `Description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '描述',
  `SupplierId` int NOT NULL COMMENT '供应商ID',
  `Status` int NOT NULL DEFAULT 0 COMMENT '状态：0草稿，1待审批，2已审批，3已发出，4已收货，5已完成，6已取消',
  `EstimatedDeliveryDate` datetime NULL DEFAULT NULL COMMENT '预计交付日期',
  `ActualDeliveryDate` datetime NULL DEFAULT NULL COMMENT '实际交付日期',
  `ApplicantId` int NOT NULL COMMENT '申请人ID',
  `ApplicationTime` datetime NOT NULL COMMENT '申请时间',
  `ApproverId` int NULL DEFAULT NULL COMMENT '审批人ID',
  `ApprovalTime` datetime NULL DEFAULT NULL COMMENT '审批时间',
  `TotalAmount` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '总金额',
  `Notes` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `CreatedAt` datetime NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE INDEX `IX_PurchaseOrders_OrderCode`(`OrderCode` ASC) USING BTREE,
  INDEX `IX_PurchaseOrders_SupplierId`(`SupplierId` ASC) USING BTREE,
  INDEX `IX_PurchaseOrders_ApplicantId`(`ApplicantId` ASC) USING BTREE,
  INDEX `IX_PurchaseOrders_ApproverId`(`ApproverId` ASC) USING BTREE,
  CONSTRAINT `FK_PurchaseOrders_Suppliers_SupplierId` FOREIGN KEY (`SupplierId`) REFERENCES `suppliers` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_PurchaseOrders_Users_ApplicantId` FOREIGN KEY (`ApplicantId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_PurchaseOrders_Users_ApproverId` FOREIGN KEY (`ApproverId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '采购订单表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for quick_memo_categories
-- ----------------------------
DROP TABLE IF EXISTS `quick_memo_categories`;
CREATE TABLE `quick_memo_categories`  (
  `id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `color` varchar(7) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `user_id` int NOT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `ix_quick_memo_categories_user_name_unique`(`user_id` ASC, `name` ASC) USING BTREE,
  CONSTRAINT `FK_quick_memo_categories_users_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`Id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for quick_memos
-- ----------------------------
DROP TABLE IF EXISTS `quick_memos`;
CREATE TABLE `quick_memos`  (
  `id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
  `user_id` int NOT NULL,
  `category_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `is_pinned` tinyint(1) NOT NULL DEFAULT 0,
  `color` varchar(7) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  `PositionX` int NOT NULL DEFAULT 0,
  `PositionY` int NOT NULL DEFAULT 0,
  `SizeWidth` int NOT NULL DEFAULT 200,
  `SizeHeight` int NOT NULL DEFAULT 180,
  `ZIndex` int NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `IX_quick_memos_category_id`(`category_id` ASC) USING BTREE,
  INDEX `ix_quick_memos_user_category`(`user_id` ASC, `category_id` ASC) USING BTREE,
  INDEX `ix_quick_memos_user_id`(`user_id` ASC) USING BTREE,
  INDEX `ix_quick_memos_user_pinned_updated`(`user_id` ASC, `is_pinned` ASC, `updated_at` ASC) USING BTREE,
  CONSTRAINT `FK_quick_memos_quick_memo_categories_category_id` FOREIGN KEY (`category_id`) REFERENCES `quick_memo_categories` (`id`) ON DELETE SET NULL ON UPDATE RESTRICT,
  CONSTRAINT `FK_quick_memos_users_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`Id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for refreshtokens
-- ----------------------------
DROP TABLE IF EXISTS `refreshtokens`;
CREATE TABLE `refreshtokens`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `UserId` int NOT NULL COMMENT '用户ID',
  `Token` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '令牌',
  `JwtId` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'JWT ID',
  `IsUsed` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已使用',
  `IsRevoked` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已撤销',
  `AddedDate` datetime NOT NULL COMMENT '添加日期',
  `ExpiryDate` datetime NOT NULL COMMENT '过期日期',
  PRIMARY KEY (`Id`) USING BTREE,
  INDEX `IX_RefreshTokens_UserId`(`UserId` ASC) USING BTREE,
  CONSTRAINT `FK_RefreshTokens_Users_UserId` FOREIGN KEY (`UserId`) REFERENCES `users` (`Id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '刷新令牌表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for returntofactories
-- ----------------------------
DROP TABLE IF EXISTS `returntofactories`;
CREATE TABLE `returntofactories`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `Code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '返厂单号',
  `AssetId` int NOT NULL COMMENT '资产ID',
  `FaultRecordId` int NOT NULL COMMENT '故障记录ID',
  `SupplierId` int NOT NULL COMMENT '供应商ID',
  `Status` int NOT NULL DEFAULT 0 COMMENT '状态：0待送出，1已送出，2维修中，3已返回，4维修失败',
  `SenderId` int NOT NULL COMMENT '送出人ID',
  `SendTime` datetime NULL DEFAULT NULL COMMENT '送出时间',
  `EstimatedReturnTime` datetime NULL DEFAULT NULL COMMENT '预计返回时间',
  `ActualReturnTime` datetime NULL DEFAULT NULL COMMENT '实际返回时间',
  `RepairResult` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '维修结果',
  `RepairCost` decimal(18, 2) NULL DEFAULT NULL COMMENT '维修费用',
  `InWarranty` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否在保修期内',
  `Notes` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `CreatedAt` datetime NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE INDEX `IX_ReturnToFactories_Code`(`Code` ASC) USING BTREE,
  INDEX `IX_ReturnToFactories_AssetId`(`AssetId` ASC) USING BTREE,
  INDEX `IX_ReturnToFactories_FaultRecordId`(`FaultRecordId` ASC) USING BTREE,
  INDEX `IX_ReturnToFactories_SupplierId`(`SupplierId` ASC) USING BTREE,
  INDEX `IX_ReturnToFactories_SenderId`(`SenderId` ASC) USING BTREE,
  CONSTRAINT `FK_ReturnToFactories_Assets_AssetId` FOREIGN KEY (`AssetId`) REFERENCES `assets` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_ReturnToFactories_FaultRecords_FaultRecordId` FOREIGN KEY (`FaultRecordId`) REFERENCES `faultrecords` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_ReturnToFactories_Suppliers_SupplierId` FOREIGN KEY (`SupplierId`) REFERENCES `suppliers` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_ReturnToFactories_Users_SenderId` FOREIGN KEY (`SenderId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '返厂维修表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rolemenus
-- ----------------------------
DROP TABLE IF EXISTS `rolemenus`;
CREATE TABLE `rolemenus`  (
  `RoleId` int NOT NULL COMMENT '角色ID',
  `MenuId` int NOT NULL COMMENT '菜单ID',
  `CreatedAt` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`RoleId`, `MenuId`) USING BTREE,
  INDEX `IX_RoleMenus_MenuId`(`MenuId` ASC) USING BTREE,
  CONSTRAINT `FK_RoleMenus_Menus_MenuId` FOREIGN KEY (`MenuId`) REFERENCES `menus` (`Id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `FK_RoleMenus_Roles_RoleId` FOREIGN KEY (`RoleId`) REFERENCES `roles` (`Id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '角色菜单关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for roles
-- ----------------------------
DROP TABLE IF EXISTS `roles`;
CREATE TABLE `roles`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `Name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '角色名称',
  `Code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '角色编码',
  `Description` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '角色描述',
  `IsActive` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否激活',
  `CreatedAt` datetime NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE INDEX `IX_Roles_Code`(`Code` ASC) USING BTREE,
  UNIQUE INDEX `IX_Roles_Name`(`Name` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '角色表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for spare_part_locations
-- ----------------------------
DROP TABLE IF EXISTS `spare_part_locations`;
CREATE TABLE `spare_part_locations`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '库位编号 (例如 A-1-1-1)',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '库位名称 (例如 A区-1号货架-1层-1格)',
  `area` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '区域标识 (如 A区, B区)',
  `description` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '库位描述',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_spare_part_locations_code`(`code` ASC) USING BTREE,
  INDEX `idx_spare_part_locations_area`(`area` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '备品备件库位表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for spare_part_transactions
-- ----------------------------
DROP TABLE IF EXISTS `spare_part_transactions`;
CREATE TABLE `spare_part_transactions`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `part_id` int NOT NULL COMMENT '备件ID',
  `type` tinyint NOT NULL COMMENT '类型:1入库,2出库',
  `quantity` int NOT NULL COMMENT '数量',
  `user_id` int NOT NULL COMMENT '操作人ID',
  `location_id` int NOT NULL COMMENT '库位ID (关联 spare_part_locations.id)',
  `reference` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '关联单号 (采购单、维修单等)',
  `reason_type` tinyint NULL DEFAULT NULL COMMENT '原因类型:1采购,2退回,3领用,4报废,5盘点调整等',
  `reason` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '原因/用途详细描述',
  `related_asset_id` int NULL DEFAULT NULL COMMENT '关联资产ID (若出库用于特定资产)',
  `related_fault_id` int NULL DEFAULT NULL COMMENT '关联故障ID (若出库用于特定维修)',
  `transaction_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `batch_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '记录批次号，用于将相同批次的多个记录关联',
  `stock_after` int NULL DEFAULT NULL COMMENT '操作后库存',
  `is_system_generated` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否由系统自动生成',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_transactions_part`(`part_id` ASC) USING BTREE,
  INDEX `idx_transactions_user`(`user_id` ASC) USING BTREE,
  INDEX `idx_transactions_time`(`transaction_time` ASC) USING BTREE,
  INDEX `idx_transactions_type`(`type` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for spare_part_types
-- ----------------------------
DROP TABLE IF EXISTS `spare_part_types`;
CREATE TABLE `spare_part_types`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '类型名称',
  `code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '类型编码',
  `parent_id` int NULL DEFAULT NULL COMMENT '父类型ID',
  `path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '类型路径 (例如 1,5,15)',
  `level` int NOT NULL DEFAULT 1 COMMENT '层级',
  `description` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '描述',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_part_types_code`(`code` ASC) USING BTREE,
  INDEX `idx_part_types_parent`(`parent_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for spare_parts
-- ----------------------------
DROP TABLE IF EXISTS `spare_parts`;
CREATE TABLE `spare_parts`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '备件编号',
  `material_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '物料编号',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '备件名称',
  `type_id` int NOT NULL COMMENT '备件类型ID',
  `spec` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '规格型号',
  `brand` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '品牌',
  `quantity` int NOT NULL DEFAULT 0 COMMENT '当前库存量',
  `unit` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '单位',
  `min_threshold` int NOT NULL DEFAULT 5 COMMENT '最小安全库存',
  `warning_threshold` int NOT NULL DEFAULT 10 COMMENT '预警库存',
  `location_id` int NOT NULL COMMENT '库位ID (关联 spare_part_locations.id)',
  `purchase_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '采购价格',
  `supplier_id` int NULL DEFAULT NULL COMMENT '默认供应商',
  `notes` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_spare_parts_code`(`code` ASC) USING BTREE,
  INDEX `idx_spare_parts_type`(`type_id` ASC) USING BTREE,
  INDEX `idx_spare_parts_location`(`location_id` ASC) USING BTREE,
  INDEX `idx_spare_parts_material_number`(`material_number` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for suppliers
-- ----------------------------
DROP TABLE IF EXISTS `suppliers`;
CREATE TABLE `suppliers`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `Name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '供应商名称',
  `Code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '供应商编码',
  `ContactPerson` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '联系人',
  `ContactPhone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '联系电话',
  `ContactEmail` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '联系邮箱',
  `Address` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '地址',
  `Type` int NOT NULL DEFAULT 0 COMMENT '类型：0硬件，1软件，2服务',
  `Notes` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `IsActive` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否激活',
  `CreatedAt` datetime NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE INDEX `IX_Suppliers_Code`(`Code` ASC) USING BTREE,
  UNIQUE INDEX `IX_Suppliers_Name`(`Name` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '供应商表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for taskassignees
-- ----------------------------
DROP TABLE IF EXISTS `taskassignees`;
CREATE TABLE `taskassignees`  (
  `TaskAssigneeId` bigint NOT NULL AUTO_INCREMENT COMMENT '分配ID (BIGINT)',
  `TaskId` bigint NOT NULL COMMENT '任务ID',
  `UserId` int NOT NULL COMMENT '用户ID (关联 users.Id - INT)',
  `AssignmentType` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'Responsible' COMMENT '分配类型 (Responsible, Participant)',
  `AssignedByUserId` int NOT NULL COMMENT '分配人用户ID (关联 users.Id - INT)',
  `AssignmentTimestamp` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '分配时间',
  PRIMARY KEY (`TaskAssigneeId`) USING BTREE,
  UNIQUE INDEX `idx_taskassignees_task_user_type`(`TaskId` ASC, `UserId` ASC, `AssignmentType` ASC) USING BTREE,
  INDEX `idx_taskassignees_user`(`UserId` ASC) USING BTREE,
  INDEX `idx_taskassignees_assignedby`(`AssignedByUserId` ASC) USING BTREE,
  CONSTRAINT `FK_TaskAssignees_AssignedByUser` FOREIGN KEY (`AssignedByUserId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE CASCADE,
  CONSTRAINT `FK_TaskAssignees_Task` FOREIGN KEY (`TaskId`) REFERENCES `tasks` (`TaskId`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `FK_TaskAssignees_User` FOREIGN KEY (`UserId`) REFERENCES `users` (`Id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 71 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '任务多负责人/参与者表 (V2 - BIGINT PK)' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for taskhistory
-- ----------------------------
DROP TABLE IF EXISTS `taskhistory`;
CREATE TABLE `taskhistory`  (
  `TaskHistoryId` bigint NOT NULL AUTO_INCREMENT COMMENT '历史记录ID (BIGINT)',
  `TaskId` bigint NOT NULL COMMENT '关联的任务ID',
  `UserId` int NULL DEFAULT NULL COMMENT '操作用户ID (系统操作可为NULL, 关联 users.Id - INT)',
  `Timestamp` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  `ActionType` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '操作类型',
  `FieldName` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '变更的字段名',
  `OldValue` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '旧值 (建议JSON)',
  `NewValue` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '新值 (建议JSON)',
  `CommentId` bigint NULL DEFAULT NULL COMMENT '关联的评论ID',
  `AttachmentId` bigint NULL DEFAULT NULL COMMENT '关联的附件ID',
  `Description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '操作的文字描述',
  PRIMARY KEY (`TaskHistoryId`) USING BTREE,
  INDEX `idx_taskhistory_task_time`(`TaskId` ASC, `Timestamp` ASC) USING BTREE,
  INDEX `idx_taskhistory_user`(`UserId` ASC) USING BTREE,
  INDEX `idx_taskhistory_action`(`ActionType` ASC) USING BTREE,
  INDEX `idx_taskhistory_comment`(`CommentId` ASC) USING BTREE,
  INDEX `idx_taskhistory_attachment`(`AttachmentId` ASC) USING BTREE,
  CONSTRAINT `FK_TaskHistory_Attachment` FOREIGN KEY (`AttachmentId`) REFERENCES `attachments` (`AttachmentId`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `FK_TaskHistory_Comment` FOREIGN KEY (`CommentId`) REFERENCES `comments` (`CommentId`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `FK_TaskHistory_Task` FOREIGN KEY (`TaskId`) REFERENCES `tasks` (`TaskId`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `FK_TaskHistory_User` FOREIGN KEY (`UserId`) REFERENCES `users` (`Id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 91 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '任务变更历史记录表 (V2 - BIGINT PK)' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for tasks
-- ----------------------------
DROP TABLE IF EXISTS `tasks`;
CREATE TABLE `tasks`  (
  `TaskId` bigint NOT NULL AUTO_INCREMENT COMMENT '任务主键ID (BIGINT)',
  `ProjectId` bigint NULL DEFAULT NULL COMMENT '所属项目ID (如果需要，也应为 BIGINT)',
  `ParentTaskId` bigint NULL DEFAULT NULL COMMENT '父任务ID (指向自身 TaskId)',
  `CreatorUserId` int NOT NULL COMMENT '创建者用户ID (关联 users.Id - INT)',
  `AssigneeUserId` int NULL DEFAULT NULL COMMENT '负责人用户ID (关联 users.Id - INT)',
  `AssetId` int NULL DEFAULT NULL COMMENT '关联资产ID (关联 assets.Id - INT)',
  `LocationId` int NULL DEFAULT NULL COMMENT '关联位置ID (关联 locations.Id - INT)',
  `Name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '任务名称',
  `Description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '任务描述 (支持Markdown或富文本)',
  `Status` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'Todo' COMMENT '任务状态 (使用字符串)',
  `Priority` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'Medium' COMMENT '优先级 (使用字符串)',
  `TaskType` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'Normal' COMMENT '任务类型 (Normal, Periodic, PDCA)',
  `PlanStartDate` datetime NULL DEFAULT NULL COMMENT '计划开始时间',
  `PlanEndDate` datetime NULL DEFAULT NULL COMMENT '计划结束时间 (截止时间)',
  `ActualStartDate` datetime NULL DEFAULT NULL COMMENT '实际开始时间',
  `ActualEndDate` datetime NULL DEFAULT NULL COMMENT '实际完成时间',
  `CreationTimestamp` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `LastUpdatedTimestamp` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `IsOverdueAcknowledged` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逾期是否已知晓/处理',
  `PDCAStage` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'PDCA阶段 (Plan, Do, Check, Act)',
  `PreviousInstanceTaskId` bigint NULL DEFAULT NULL COMMENT '周期性任务的前一个实例ID (指向自身 TaskId)',
  `PeriodicTaskScheduleId` bigint NULL DEFAULT NULL COMMENT '关联的周期性任务计划ID',
  `Progress` int NOT NULL DEFAULT 0 COMMENT '任务进度百分比 (0-100)',
  `Points` int NOT NULL DEFAULT 0 COMMENT '完成任务可获得的基础积分/XP',
  `IsDeleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '软删除标记',
  PRIMARY KEY (`TaskId`) USING BTREE,
  INDEX `idx_tasks_status`(`Status` ASC) USING BTREE,
  INDEX `idx_tasks_assignee`(`AssigneeUserId` ASC) USING BTREE,
  INDEX `idx_tasks_creator`(`CreatorUserId` ASC) USING BTREE,
  INDEX `idx_tasks_parent`(`ParentTaskId` ASC) USING BTREE,
  INDEX `idx_tasks_plan_end_date`(`PlanEndDate` ASC) USING BTREE,
  INDEX `idx_tasks_type`(`TaskType` ASC) USING BTREE,
  INDEX `idx_tasks_asset`(`AssetId` ASC) USING BTREE,
  INDEX `idx_tasks_location`(`LocationId` ASC) USING BTREE,
  INDEX `FK_Tasks_PreviousInstanceTask`(`PreviousInstanceTaskId` ASC) USING BTREE,
  CONSTRAINT `FK_Tasks_Asset` FOREIGN KEY (`AssetId`) REFERENCES `assets` (`Id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `FK_Tasks_AssigneeUser` FOREIGN KEY (`AssigneeUserId`) REFERENCES `users` (`Id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `FK_Tasks_CreatorUser` FOREIGN KEY (`CreatorUserId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE CASCADE,
  CONSTRAINT `FK_Tasks_Location` FOREIGN KEY (`LocationId`) REFERENCES `locations` (`Id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `FK_Tasks_ParentTask` FOREIGN KEY (`ParentTaskId`) REFERENCES `tasks` (`TaskId`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `FK_Tasks_PreviousInstanceTask` FOREIGN KEY (`PreviousInstanceTaskId`) REFERENCES `tasks` (`TaskId`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 45 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '任务主表 (V2 - BIGINT PK)' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for user_actions
-- ----------------------------
DROP TABLE IF EXISTS `user_actions`;
CREATE TABLE `user_actions`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `action` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '行为类型',
  `reference_id` int NULL DEFAULT NULL COMMENT '关联ID',
  `reference_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '关联类型',
  `points` int NULL DEFAULT 0 COMMENT '获得积分',
  `action_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_actions_user`(`user_id` ASC) USING BTREE,
  INDEX `idx_user_actions_time`(`action_time` ASC) USING BTREE,
  CONSTRAINT `fk_user_actions_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`Id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for user_items
-- ----------------------------
DROP TABLE IF EXISTS `user_items`;
CREATE TABLE `user_items`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `description` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `type` int NOT NULL,
  `level` int NOT NULL DEFAULT 1,
  `power` int NOT NULL DEFAULT 0,
  `quantity` int NOT NULL DEFAULT 1,
  `is_used` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_items_user`(`user_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for userroles
-- ----------------------------
DROP TABLE IF EXISTS `userroles`;
CREATE TABLE `userroles`  (
  `UserId` int NOT NULL COMMENT '用户ID',
  `RoleId` int NOT NULL COMMENT '角色ID',
  `CreatedAt` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`UserId`, `RoleId`) USING BTREE,
  INDEX `IX_UserRoles_RoleId`(`RoleId` ASC) USING BTREE,
  CONSTRAINT `FK_UserRoles_Roles_RoleId` FOREIGN KEY (`RoleId`) REFERENCES `roles` (`Id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `FK_UserRoles_Users_UserId` FOREIGN KEY (`UserId`) REFERENCES `users` (`Id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户角色关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for users
-- ----------------------------
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `Username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户名',
  `PasswordHash` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '密码哈希',
  `SecurityStamp` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '安全戳',
  `Name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '姓名',
  `Email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '邮箱',
  `Mobile` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '手机号',
  `DepartmentId` int NULL DEFAULT NULL COMMENT '部门ID',
  `Position` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '职位',
  `Gender` int NOT NULL DEFAULT 0 COMMENT '性别：0未知，1男，2女',
  `DefaultRoleId` int NULL DEFAULT NULL COMMENT '默认角色ID',
  `IsActive` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否激活',
  `CreatedAt` datetime NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime NOT NULL COMMENT '更新时间',
  `LastLoginAt` datetime NULL DEFAULT NULL COMMENT '最后登录时间',
  `Avatar` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '头像',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE INDEX `IX_Users_Username`(`Username` ASC) USING BTREE,
  UNIQUE INDEX `IX_Users_Email`(`Email` ASC) USING BTREE,
  INDEX `IX_Users_DepartmentId`(`DepartmentId` ASC) USING BTREE,
  INDEX `IX_Users_DefaultRoleId`(`DefaultRoleId` ASC) USING BTREE,
  CONSTRAINT `FK_Users_Departments_DepartmentId` FOREIGN KEY (`DepartmentId`) REFERENCES `departments` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_Users_Roles_DefaultRoleId` FOREIGN KEY (`DefaultRoleId`) REFERENCES `roles` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 22 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Procedure structure for generate_asset_code
-- ----------------------------
DROP PROCEDURE IF EXISTS `generate_asset_code`;
delimiter ;;
CREATE PROCEDURE `generate_asset_code`(IN asset_type_code VARCHAR(10),
    OUT asset_code VARCHAR(30))
BEGIN
    DECLARE date_part VARCHAR(10);
    DECLARE seq_num INT;
    
    -- 当前日期部分 (年月日)
    SET date_part = DATE_FORMAT(NOW(), '%Y%m%d');
    
    -- 获取当天最大序号并+1
    SELECT IFNULL(MAX(SUBSTRING_INDEX(assetCode, '-', -1)), 0) + 1 
    INTO seq_num
    FROM assets 
    WHERE assetCode LIKE CONCAT('IT-', asset_type_code, '-', date_part, '-%');
    
    -- 格式化资产编号
    SET asset_code = CONCAT('IT-', asset_type_code, '-', date_part, '-', LPAD(seq_num, 3, '0'));
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for inventory_to_asset
-- ----------------------------
DROP PROCEDURE IF EXISTS `inventory_to_asset`;
delimiter ;;
CREATE PROCEDURE `inventory_to_asset`(IN inventory_id INT,
    IN location_id INT,
    IN user_id INT,
    OUT asset_id INT)
BEGIN
    DECLARE asset_type_code VARCHAR(10);
    DECLARE asset_code VARCHAR(30);
    
    -- 事务开始
    START TRANSACTION;
    
    -- 获取物料类型代码
    SELECT code INTO asset_type_code 
    FROM assettypes at 
    JOIN purchaseitems pi ON at.id = pi.assetTypeId
    WHERE pi.id = inventory_id;
    
    -- 生成资产编号
    CALL generate_asset_code(asset_type_code, asset_code);
    
    -- 创建资产记录
    INSERT INTO assets(
        assetCode,
        name,
        assetTypeId,
        model,
        brand,
        purchaseDate,
        locationId,
        status,
        inventoryId,
        createdAt,
        updatedAt
    )
    SELECT 
        asset_code,
        pi.name,
        pi.assetTypeId,
        pi.model,
        pi.brand,
        po.orderDate,
        location_id,
        1, -- 使用中状态
        inventory_id,
        NOW(),
        NOW()
    FROM purchaseitems pi
    JOIN purchaseorders po ON pi.purchaseOrderId = po.id
    WHERE pi.id = inventory_id;
    
    -- 更新库存状态
    UPDATE purchaseitems 
    SET status = 2 -- 已出库
    WHERE id = inventory_id;
    
    -- 获取新创建的资产ID
    SELECT LAST_INSERT_ID() INTO asset_id;
    
    -- 提交事务
    COMMIT;
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for POMELO_AFTER_ADD_PRIMARY_KEY
-- ----------------------------
DROP PROCEDURE IF EXISTS `POMELO_AFTER_ADD_PRIMARY_KEY`;
delimiter ;;
CREATE PROCEDURE `POMELO_AFTER_ADD_PRIMARY_KEY`(IN `SCHEMA_NAME_ARGUMENT` VARCHAR(255), IN `TABLE_NAME_ARGUMENT` VARCHAR(255), IN `COLUMN_NAME_ARGUMENT` VARCHAR(255))
BEGIN
	DECLARE HAS_AUTO_INCREMENT_ID INT(11);
	DECLARE PRIMARY_KEY_COLUMN_NAME VARCHAR(255);
	DECLARE PRIMARY_KEY_TYPE VARCHAR(255);
	DECLARE SQL_EXP VARCHAR(1000);
	SELECT COUNT(*)
		INTO HAS_AUTO_INCREMENT_ID
		FROM `information_schema`.`COLUMNS`
		WHERE `TABLE_SCHEMA` = (SELECT IFNULL(SCHEMA_NAME_ARGUMENT, SCHEMA()))
			AND `TABLE_NAME` = TABLE_NAME_ARGUMENT
			AND `COLUMN_NAME` = COLUMN_NAME_ARGUMENT
			AND `COLUMN_TYPE` LIKE '%int%'
			AND `COLUMN_KEY` = 'PRI';
	IF HAS_AUTO_INCREMENT_ID THEN
		SELECT `COLUMN_TYPE`
			INTO PRIMARY_KEY_TYPE
			FROM `information_schema`.`COLUMNS`
			WHERE `TABLE_SCHEMA` = (SELECT IFNULL(SCHEMA_NAME_ARGUMENT, SCHEMA()))
				AND `TABLE_NAME` = TABLE_NAME_ARGUMENT
				AND `COLUMN_NAME` = COLUMN_NAME_ARGUMENT
				AND `COLUMN_TYPE` LIKE '%int%'
				AND `COLUMN_KEY` = 'PRI';
		SELECT `COLUMN_NAME`
			INTO PRIMARY_KEY_COLUMN_NAME
			FROM `information_schema`.`COLUMNS`
			WHERE `TABLE_SCHEMA` = (SELECT IFNULL(SCHEMA_NAME_ARGUMENT, SCHEMA()))
				AND `TABLE_NAME` = TABLE_NAME_ARGUMENT
				AND `COLUMN_NAME` = COLUMN_NAME_ARGUMENT
				AND `COLUMN_TYPE` LIKE '%int%'
				AND `COLUMN_KEY` = 'PRI';
		SET SQL_EXP = CONCAT('ALTER TABLE `', (SELECT IFNULL(SCHEMA_NAME_ARGUMENT, SCHEMA())), '`.`', TABLE_NAME_ARGUMENT, '` MODIFY COLUMN `', PRIMARY_KEY_COLUMN_NAME, '` ', PRIMARY_KEY_TYPE, ' NOT NULL AUTO_INCREMENT;');
		SET @SQL_EXP = SQL_EXP;
		PREPARE SQL_EXP_EXECUTE FROM @SQL_EXP;
		EXECUTE SQL_EXP_EXECUTE;
		DEALLOCATE PREPARE SQL_EXP_EXECUTE;
	END IF;
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for POMELO_BEFORE_DROP_PRIMARY_KEY
-- ----------------------------
DROP PROCEDURE IF EXISTS `POMELO_BEFORE_DROP_PRIMARY_KEY`;
delimiter ;;
CREATE PROCEDURE `POMELO_BEFORE_DROP_PRIMARY_KEY`(IN `SCHEMA_NAME_ARGUMENT` VARCHAR(255), IN `TABLE_NAME_ARGUMENT` VARCHAR(255))
BEGIN
	DECLARE HAS_AUTO_INCREMENT_ID TINYINT(1);
	DECLARE PRIMARY_KEY_COLUMN_NAME VARCHAR(255);
	DECLARE PRIMARY_KEY_TYPE VARCHAR(255);
	DECLARE SQL_EXP VARCHAR(1000);
	SELECT COUNT(*)
		INTO HAS_AUTO_INCREMENT_ID
		FROM `information_schema`.`COLUMNS`
		WHERE `TABLE_SCHEMA` = (SELECT IFNULL(SCHEMA_NAME_ARGUMENT, SCHEMA()))
			AND `TABLE_NAME` = TABLE_NAME_ARGUMENT
			AND `Extra` = 'auto_increment'
			AND `COLUMN_KEY` = 'PRI'
			LIMIT 1;
	IF HAS_AUTO_INCREMENT_ID THEN
		SELECT `COLUMN_TYPE`
			INTO PRIMARY_KEY_TYPE
			FROM `information_schema`.`COLUMNS`
			WHERE `TABLE_SCHEMA` = (SELECT IFNULL(SCHEMA_NAME_ARGUMENT, SCHEMA()))
				AND `TABLE_NAME` = TABLE_NAME_ARGUMENT
				AND `COLUMN_KEY` = 'PRI'
			LIMIT 1;
		SELECT `COLUMN_NAME`
			INTO PRIMARY_KEY_COLUMN_NAME
			FROM `information_schema`.`COLUMNS`
			WHERE `TABLE_SCHEMA` = (SELECT IFNULL(SCHEMA_NAME_ARGUMENT, SCHEMA()))
				AND `TABLE_NAME` = TABLE_NAME_ARGUMENT
				AND `COLUMN_KEY` = 'PRI'
			LIMIT 1;
		SET SQL_EXP = CONCAT('ALTER TABLE `', (SELECT IFNULL(SCHEMA_NAME_ARGUMENT, SCHEMA())), '`.`', TABLE_NAME_ARGUMENT, '` MODIFY COLUMN `', PRIMARY_KEY_COLUMN_NAME, '` ', PRIMARY_KEY_TYPE, ' NOT NULL;');
		SET @SQL_EXP = SQL_EXP;
		PREPARE SQL_EXP_EXECUTE FROM @SQL_EXP;
		EXECUTE SQL_EXP_EXECUTE;
		DEALLOCATE PREPARE SQL_EXP_EXECUTE;
	END IF;
END
;;
delimiter ;

-- ----------------------------
-- Triggers structure for table faultrecords
-- ----------------------------
DROP TRIGGER IF EXISTS `generate_fault_number`;
delimiter ;;
CREATE TRIGGER `generate_fault_number` BEFORE INSERT ON `faultrecords` FOR EACH ROW BEGIN
    DECLARE date_part VARCHAR(10);
    DECLARE seq_num INT;
    
    -- 如果没有手动指定故障单号
    IF NEW.faultNumber IS NULL OR NEW.faultNumber = '' THEN
        -- 生成日期部分
        SET date_part = DATE_FORMAT(NOW(), '%Y%m%d');
        
        -- 获取当天最大序号
        SELECT IFNULL(MAX(SUBSTRING_INDEX(faultNumber, '-', -1)), 0) + 1 
        INTO seq_num
        FROM faultrecords 
        WHERE faultNumber LIKE CONCAT('FIX-', date_part, '-%');
        
        -- 设置故障单号
        SET NEW.faultNumber = CONCAT('FIX-', date_part, '-', LPAD(seq_num, 3, '0'));
    END IF;
END
;;
delimiter ;

-- ----------------------------
-- Triggers structure for table purchaseorders
-- ----------------------------
DROP TRIGGER IF EXISTS `format_purchase_order_number`;
delimiter ;;
CREATE TRIGGER `format_purchase_order_number` BEFORE INSERT ON `purchaseorders` FOR EACH ROW BEGIN
    -- 如果没有手动指定采购单号
    IF NEW.OrderCode IS NULL OR NEW.OrderCode = '' THEN
        -- 生成采购单号 (PO-年月日-时分秒)
        SET NEW.OrderCode = CONCAT('PO-', DATE_FORMAT(NOW(), '%Y%m%d-%H%i%s'));
    END IF;
END
;;
delimiter ;

SET FOREIGN_KEY_CHECKS = 1;
