import os
import sys
import time # 添加 time 模块导入

# --- 配置 ---
# Target only the frontend directory
FRONTEND_TARGET_DIR = 'frontend'
OUTPUT_FILENAME = '1hoursfrontend_analysis_output.txt'
WORKSPACE_ROOT = '.' # Run from the workspace root

# Directories to exclude during traversal
EXCLUDE_DIRS = [
    '.git', 'bin', 'obj', '.idea', '.vscode', 'node_modules',
    '__pycache__', '.pytest_cache', 'dist', '.pnpm', '.cursor',
    'docs' # Exclude the docs directory within frontend
]

# --- 构建路径 ---
output_file_path = os.path.abspath(os.path.join(WORKSPACE_ROOT, OUTPUT_FILENAME))
frontend_dir_abs = os.path.abspath(os.path.join(WORKSPACE_ROOT, FRONTEND_TARGET_DIR))

# 存储文件路径和内容的字典
file_data = {}
# 存储空目录路径的列表
empty_dirs = []

# --- 计算一小时前的时间戳 ---
ONE_HOUR_AGO_TIMESTAMP = time.time() - 3600 # 3600 秒 = 1 小时

# --- 遍历目标 ---
print(f"--- 开始分析前端代码: {FRONTEND_TARGET_DIR} (只处理过去1小时内修改的文件) ---")

# Function to read and store file data
def read_and_store_file(file_path_abs, root_dir):
    file_path_relative = os.path.relpath(file_path_abs, root_dir)
    try:
        # Try reading with UTF-8 first
        with open(file_path_abs, 'r', encoding='utf-8') as f:
            content = f.read()
        file_data[file_path_relative] = content
        print(f"  读取文件: {file_path_relative} (UTF-8)") # 控制台进度
    except UnicodeDecodeError:
        try:
            # Fallback to system default encoding if UTF-8 fails
            with open(file_path_abs, 'r', encoding=sys.getdefaultencoding()) as f:
                content = f.read()
            file_data[file_path_relative] = content
            print(f"  读取文件: {file_path_relative} ({sys.getdefaultencoding()})") # 控制台进度
        except Exception as e_fallback:
             print(f"  错误：无法读取文件 {file_path_relative} (尝试了 UTF-8 和 {sys.getdefaultencoding()}): {e_fallback}") # 控制台错误
             file_data[file_path_relative] = f"Error reading file: {e_fallback}"
    except Exception as e:
        print(f"  错误：读取文件 {file_path_relative} 时发生未知错误: {e}") # 控制台错误
        file_data[file_path_relative] = f"Error reading file: {e}"

# Get the absolute path of the workspace root for relative path calculation
workspace_abs_root = os.path.abspath(WORKSPACE_ROOT)

if os.path.isdir(frontend_dir_abs):
    print(f"\n--- 开始遍历目录: {FRONTEND_TARGET_DIR} ---")
    for root, dirs, files in os.walk(frontend_dir_abs, topdown=False):
        # Filter excluded directories
        # For topdown=False, this primarily affects the 'dirs' list used later in empty check,
        # not traversal pruning. Pruning of traversal itself would need topdown=True.
        dirs[:] = [d for d in dirs if d not in EXCLUDE_DIRS]

        current_dir_abs = root
        current_dir_relative = os.path.relpath(current_dir_abs, workspace_abs_root)

        # Check if the directory is empty after filtering
        # We only care about directories *within* the frontend target dir
        if root.startswith(frontend_dir_abs) and not files and not dirs:
            try:
                # Check if the directory is genuinely empty on the filesystem
                if not os.listdir(current_dir_abs):
                    empty_dirs.append(current_dir_relative)
                    print(f"  发现空目录: {current_dir_relative}")
            except OSError as e_listdir:
                 print(f"  警告: 检查目录 {current_dir_relative} 是否为空时出错: {e_listdir}")

        # Read files in the current directory
        for filename in files:
            # Skip files directly within explicitly excluded directory types (e.g., node_modules, docs within frontend)
            # This checks if the 'root' path itself is part of an excluded structure.
            if any(excluded_path_component in root 
                   for excluded_path_component in [os.path.join(frontend_dir_abs, d) for d in EXCLUDE_DIRS]):
                # file_path_for_skip_log = os.path.join(root, filename) # If needed for logging
                # print(f"  跳过文件 (在排除的目录结构中 {root}): {os.path.relpath(file_path_for_skip_log, workspace_abs_root)}")
                continue
            
            file_path_abs_walk = os.path.join(root, filename)

            try:
                file_mod_time = os.path.getmtime(file_path_abs_walk)
                if file_mod_time > ONE_HOUR_AGO_TIMESTAMP:
                    read_and_store_file(file_path_abs_walk, workspace_abs_root)
                else:
                    print(f"  跳过文件 (修改时间早于1小时): {os.path.relpath(file_path_abs_walk, workspace_abs_root)}")
            except FileNotFoundError:
                print(f"  警告: 文件在检查修改时间时未找到: {os.path.relpath(file_path_abs_walk, workspace_abs_root)}")
            except Exception as e_mtime:
                print(f"  错误: 检查文件 {os.path.relpath(file_path_abs_walk, workspace_abs_root)} 修改时间时出错: {e_mtime}")
else:
    print(f"错误: 目标目录 '{FRONTEND_TARGET_DIR}' 不存在或不是一个有效的目录。")

# --- 将结果写入文件 ---
print(f"\n--- 开始将内容写入到文件: {OUTPUT_FILENAME} ---")
try:
    with open(output_file_path, 'w', encoding='utf-8') as outfile:
        outfile.write(f"--- 前端文件内容汇总 ({FRONTEND_TARGET_DIR}, 最近1小时修改) ---\n\n")
        # Sort items by path for consistent output
        sorted_file_data = sorted(file_data.items())
        for path, content in sorted_file_data:
            outfile.write("=" * 80 + "\n")
            normalized_path = path.replace('\\', '/') # Normalize path separators first
            outfile.write(f"文件路径: {normalized_path}\n")
            outfile.write("=" * 80 + "\n")
            outfile.write(content.rstrip() + "\n\n")

        # Write empty directories if any found (this logic remains unchanged, reflects physical emptiness)
        if empty_dirs:
            outfile.write("\n" + "=" * 80 + "\n")
            outfile.write(f"在 {FRONTEND_TARGET_DIR} 中发现的空目录:\n")
            outfile.write("=" * 80 + "\n")
            # Sort empty dirs for consistent output
            sorted_empty_dirs = sorted(list(set(empty_dirs))) # Use set to remove potential duplicates
            for dir_path in sorted_empty_dirs:
                normalized_dir_path = dir_path.replace('\\', '/') # Normalize path separators first
                outfile.write(f"- {normalized_dir_path}\n")
            outfile.write("\n")

    print(f"--- 内容成功写入到: {os.path.relpath(output_file_path, workspace_abs_root)} ---")
except Exception as e:
    print(f"错误：无法写入到输出文件 {output_file_path}: {e}")

print(f"--- 前端分析 ({FRONTEND_TARGET_DIR}) 完成 ---") 