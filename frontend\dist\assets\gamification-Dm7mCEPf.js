import{bv as e,r as t,c as a}from"./index-CG5lHOPO.js";import{g as i}from"./gamification-2FBMrBgR.js";const n=[{level:1,points:0,title:"新手入门"},{level:2,points:100,title:"初级专员"},{level:3,points:300,title:"中级能手"},{level:4,points:600,title:"高级专家"},{level:5,points:1e3,title:"资深大师"}],s=e("gamification",(()=>{const e=t(0),s=t(1),c=t("新手入门"),l=t(100),o=t(0),r=t([]),d=t([]),u=t([]),v=t([]),m=t(!1),p=t(null),f=a((()=>n.find((e=>e.level===s.value))||n[0])),h=a((()=>n.find((e=>e.level===s.value+1))));async function y(){m.value=!0;try{const t=await i.getUserStats();if(t.success&&t.data){const a=t.data;e.value=a.pointsBalance||0,s.value=a.currentLevel||1,S(e.value)}else await w()}catch(t){try{await w()}catch(a){}}finally{m.value=!1}}async function w(){try{const t=await i.initializeUserStats();if(t.success&&t.data){const a=t.data;e.value=a.pointsBalance||0,s.value=a.currentLevel||1,S(e.value)}}catch(t){}}async function L(){try{const e={success:!0,data:[{id:"ach1",name:"首次任务",description:"完成你的第一个任务",icon:"Star",achievedDate:"2024-04-01"},{id:"ach2",name:"评论达人",description:"发表 10 条评论",icon:"ChatDotRound",achievedDate:null}]};e.success&&(r.value=e.data||[])}catch(e){}}async function g(){try{const e={success:!0,data:[{id:"item1",name:"积分加速卡",description:"任务积分 +10%",icon:"MagicStick",quantity:2},{id:"item2",name:"改名卡",description:"修改一次昵称",icon:"EditPen",quantity:1}]};e.success&&(d.value=e.data||[])}catch(e){}}async function k(e=!1){var t;try{e&&p.value&&p.value;const a=(new Date).toISOString(),i={success:!0,data:[{id:"act3",text:'完成了任务 "部署测试环境"',timestamp:a,icon:"CircleCheck",type:"task_completed"},{id:"act2",text:"张三 发表了评论",timestamp:new Date(Date.now()-6e4).toISOString(),icon:"ChatDotRound",type:"comment"},{id:"act1",text:'获得了成就 "首次任务"',timestamp:new Date(Date.now()-12e4).toISOString(),icon:"Trophy",type:"achievement"}]};if(i.success&&Array.isArray(i.data)){const a=i.data;if(a.length>0){if(e){const e=new Set(u.value.map((e=>e.id))),t=a.filter((t=>!e.has(t.id)));u.value=[...t,...u.value]}else u.value=a;p.value=(null==(t=u.value[0])?void 0:t.timestamp)||p.value}u.value.length>50&&(u.value=u.value.slice(0,50))}}catch(a){}}function C(e,t,a){const i=[];e>0&&i.push(`+${e} 积分`),t>0&&i.push(`+${t} 金币`),a>0&&i.push(`+${a} 钻石`),i.length}function S(e){var t,a;let i=1,r=n[0].title,d=(null==(t=n[1])?void 0:t.points)||1/0,u=0;for(let s=0;s<n.length&&e>=n[s].points;s++)i=n[s].level,r=n[s].title,u=n[s].points,d=(null==(a=n[s+1])?void 0:a.points)||1/0;if(s.value=i,c.value=r,d===1/0)l.value=0,o.value=100;else{const t=d-u,a=e-u;l.value=d-e,o.value=t>0?Math.min(100,Math.floor(a/t*100)):100}}return{score:e,level:s,levelTitle:c,pointsToNextLevel:l,currentLevelProgress:o,achievements:r,inventory:d,recentActivities:u,leaderboard:v,isLoading:m,lastActivityTimestamp:p,currentLevelInfo:f,nextLevelInfo:h,fetchGamificationStatus:y,fetchAchievements:L,fetchInventory:g,fetchRecentActivities:k,fetchLeaderboard:async function(e="weekly"){try{const t={weekly:i.LeaderboardType.WEEKLY,monthly:i.LeaderboardType.MONTHLY,allTime:i.LeaderboardType.ALL_TIME}[e]||i.LeaderboardType.WEEKLY,a=await i.getLeaderboard(t,10);a.success&&a.data&&(v.value=a.data.map((e=>({userId:e.userId,name:e.userName,score:e.totalPoints,rank:e.rank,avatar:e.userAvatar||"",department:e.department||"",level:e.currentLevel||1}))))}catch(t){v.value=[]}},fetchMultiDimensionLeaderboard:async function(e="points",t="weekly",a=20){try{const n=await i.getMultiDimensionLeaderboard(e,t,a);return n.success&&n.data?n.data.map(((e,t)=>({userId:e.userId,userName:e.userName,avatarUrl:e.avatarUrl,department:e.department,points:e.points||0,coins:e.coins||0,diamonds:e.diamonds||0,tasksCreated:e.tasksCreated||0,tasksCompleted:e.tasksCompleted||0,tasksClaimed:e.tasksClaimed||0,faultsRecorded:e.faultsRecorded||0,maintenanceCreated:e.maintenanceCreated||0,assetsUpdated:e.assetsUpdated||0,rank:t+1}))):[]}catch(n){return[]}},recordEvent:async function(e,t={}){try{let a=null;if("task_completed"===e){const e=t.taskId,n=t.isOnTime||!1;e&&(a=await i.triggerCompleteReward(e,n))}else if("task_claimed"===e){const e=t.taskId;e&&(a=await i.triggerClaimReward(e))}if(a&&a.success&&(setTimeout((()=>{y(),k(!0)}),1e3),a.data)){const e=a.data,t=e.pointsGained||0,i=e.coinsGained||0;C(t,i,e.diamondsGained||0)}}catch(a){}},initializeUserStats:w,showRewardNotification:C,initializeStore:function(){y(),L(),g(),k()}}}));export{s as u};
