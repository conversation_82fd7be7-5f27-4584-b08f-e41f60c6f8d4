// IT资产管理系统 - 资产接收记录实体
// 文件路径: /Models/Entities/AssetReceive.cs
// 功能: 定义资产接收记录实体

using System;
using System.ComponentModel.DataAnnotations;
using ItAssetsSystem.Models;

namespace ItAssetsSystem.Models.Entities
{
    /// <summary>
    /// 资产接收记录
    /// </summary>
    public class AssetReceive : IAuditableEntity
    {
        /// <summary>
        /// 接收记录ID
        /// </summary>
        [Key]
        public int Id { get; set; }
        
        /// <summary>
        /// 资产ID
        /// </summary>
        public int AssetId { get; set; }
        
        /// <summary>
        /// 相关资产
        /// </summary>
        public Asset Asset { get; set; }
        
        /// <summary>
        /// 采购订单ID
        /// </summary>
        public int? PurchaseOrderId { get; set; }
        
        /// <summary>
        /// 采购订单
        /// </summary>
        public PurchaseOrder PurchaseOrder { get; set; }
        
        /// <summary>
        /// 返厂记录ID
        /// </summary>
        public int? ReturnToFactoryId { get; set; }
        
        /// <summary>
        /// 返厂记录
        /// </summary>
        public ReturnToFactory ReturnToFactory { get; set; }
        
        /// <summary>
        /// 接收类型（1-新购，2-返厂返回，3-转入，4-其他）
        /// </summary>
        public int ReceiveType { get; set; }
        
        /// <summary>
        /// 接收日期
        /// </summary>
        public DateTime ReceiveDate { get; set; }
        
        /// <summary>
        /// 接收人ID
        /// </summary>
        public int ReceiverId { get; set; }
        
        /// <summary>
        /// 接收人
        /// </summary>
        public User Receiver { get; set; }
        
        /// <summary>
        /// 初始位置ID
        /// </summary>
        public int LocationId { get; set; }
        
        /// <summary>
        /// 初始位置
        /// </summary>
        public Location Location { get; set; }
        
        /// <summary>
        /// 状态（1-待验收，2-已验收，3-已入库，4-已分配）
        /// </summary>
        public int Status { get; set; }
        
        /// <summary>
        /// 验收结果
        /// </summary>
        [MaxLength(500)]
        public string InspectionResult { get; set; }
        
        /// <summary>
        /// 备注
        /// </summary>
        [MaxLength(500)]
        public string Remarks { get; set; }
        
        #region 审计字段
        
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }
        
        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; }
        
        /// <summary>
        /// 创建人ID
        /// </summary>
        public int? CreatedBy { get; set; }
        
        /// <summary>
        /// 更新人ID
        /// </summary>
        public int? UpdatedBy { get; set; }
        
        #endregion
    }
} 