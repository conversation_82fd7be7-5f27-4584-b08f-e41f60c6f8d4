<template>
  <el-dialog
    v-model="visible"
    :title="isEdit ? '编辑周期性任务' : '创建周期性任务'"
    width="900px"
    max-height="80vh"
    @close="handleClose"
    :close-on-click-modal="false"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="120px"
      class="periodic-form"
    >
      <!-- 基本信息 -->
      <div class="form-section">
        <h4 class="section-title">📋 基本信息</h4>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="计划名称" prop="name">
              <el-input
                v-model="formData.name"
                placeholder="请输入计划名称"
                maxlength="100"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="任务标题" prop="taskTitle">
              <el-input
                v-model="formData.taskTitle"
                placeholder="请输入任务标题模板"
                maxlength="200"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="描述" prop="description">
          <el-input
            v-model="formData.description"
            type="textarea"
            :rows="2"
            placeholder="请输入计划描述（可选）"
            maxlength="500"
          />
        </el-form-item>
      </div>

      <!-- 任务信息 -->
      <div class="form-section">
        <h4 class="section-title">📝 任务信息</h4>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="任务类型" prop="taskType">
              <el-select v-model="formData.taskType" placeholder="请选择任务类型" style="width: 100%">
                <el-option label="常规任务" value="Regular" />
                <el-option label="维护任务" value="Maintenance" />
                <el-option label="检查任务" value="Inspection" />
                <el-option label="PDCA任务" value="PDCA" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="优先级" prop="priority">
              <el-select v-model="formData.priority" placeholder="请选择优先级" style="width: 100%">
                <el-option label="🔴 高" value="High" />
                <el-option label="🟡 中" value="Medium" />
                <el-option label="🟢 低" value="Low" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="默认负责人">
              <UserSelect
                v-model="formData.defaultAssigneeUserId"
                placeholder="选择默认负责人"
                :multiple="true"
                :show-workload="false"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="任务描述" prop="taskDescription">
          <el-input
            v-model="formData.taskDescription"
            type="textarea"
            :rows="2"
            placeholder="请输入任务描述模板（可选）"
            maxlength="1000"
          />
        </el-form-item>
      </div>

      <!-- 重复规则 -->
      <div class="form-section">
        <h4 class="section-title">🔄 重复规则</h4>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="重复类型" prop="recurrenceType">
              <el-select
                v-model="formData.recurrenceType"
                placeholder="请选择重复类型"
                style="width: 100%"
                @change="handleRecurrenceTypeChange"
              >
                <el-option label="每班(12小时)" value="Shift" />
                <el-option label="每日" value="Daily" />
                <el-option label="每周" value="Weekly" />
                <el-option label="每月" value="Monthly" />
                <el-option label="每季度" value="Quarterly" />
                <el-option label="每年" value="Yearly" />
                <el-option label="自定义Cron" value="CustomCron" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="formData.recurrenceType !== 'CustomCron'">
            <el-form-item label="重复间隔" prop="recurrenceInterval">
              <el-input-number
                v-model="formData.recurrenceInterval"
                :min="1"
                :max="100"
                placeholder="间隔"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <!-- 每周特定设置 -->
        <el-form-item label="星期" prop="daysOfWeek" v-if="formData.recurrenceType === 'Weekly'">
          <el-checkbox-group v-model="selectedDaysOfWeek">
            <el-checkbox :label="0">周日</el-checkbox>
            <el-checkbox :label="1">周一</el-checkbox>
            <el-checkbox :label="2">周二</el-checkbox>
            <el-checkbox :label="3">周三</el-checkbox>
            <el-checkbox :label="4">周四</el-checkbox>
            <el-checkbox :label="5">周五</el-checkbox>
            <el-checkbox :label="6">周六</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        
        <!-- 每月特定设置 -->
        <el-form-item label="日期" prop="dayOfMonth" v-if="formData.recurrenceType === 'Monthly'">
          <el-input-number 
            v-model="formData.dayOfMonth" 
            :min="1" 
            :max="31" 
            placeholder="每月第几日"
            style="width: 100%"
          />
        </el-form-item>
        
        <!-- 每年特定设置 -->
        <template v-if="formData.recurrenceType === 'Yearly'">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="月份" prop="monthOfYear">
                <el-input-number 
                  v-model="formData.monthOfYear" 
                  :min="1" 
                  :max="12" 
                  placeholder="第几月"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="日期" prop="dayOfMonth">
                <el-input-number 
                  v-model="formData.dayOfMonth" 
                  :min="1" 
                  :max="31" 
                  placeholder="第几日"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </template>
        
        <!-- 自定义Cron -->
        <el-form-item label="Cron表达式" prop="cronExpression" v-if="formData.recurrenceType === 'CustomCron'">
          <el-input
            v-model="formData.cronExpression"
            placeholder="例如: 0 9 * * 1-5 (工作日上午9点)"
          />
          <div class="cron-hint">
            格式: 秒 分 时 日 月 星期<br>
            例如: 0 9 * * 1-5 表示工作日上午9点
          </div>
        </el-form-item>
      </div>

      <!-- 时间设置 -->
      <div class="form-section">
        <h4 class="section-title">⏰ 时间设置</h4>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="开始日期时间" prop="startDate">
              <el-date-picker
                v-model="formData.startDate"
                type="datetime"
                placeholder="选择开始日期和执行时间"
                format="YYYY-MM-DD HH:mm"
                value-format="YYYY-MM-DDTHH:mm:ss"
                style="width: 100%"
              />
              <div class="time-hint">
                设置任务的开始日期和每日执行时间
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束日期（可选）" prop="endDate">
              <el-date-picker
                v-model="formData.endDate"
                type="date"
                placeholder="选择结束日期（可选）"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
              <div class="time-hint">
                不设置则永久执行
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="任务持续时间">
              <el-input-number
                v-model="formData.durationHours"
                :min="0.5"
                :max="720"
                :step="0.5"
                placeholder="小时"
                style="width: 100%"
              />
              <div class="time-hint">
                单个任务的预计持续时间
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="最大生成任务数">
              <el-input-number
                v-model="formData.maxOccurrences"
                :min="1"
                :max="1000"
                placeholder="最大生成任务数量"
                style="width: 100%"
              />
              <div class="time-hint">
                限制总共生成的任务数量
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item>
              <el-checkbox v-model="formData.isEnabled">创建后立即启用</el-checkbox>
              <div class="time-hint">
                启用后将按计划自动生成任务
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </el-form>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          {{ isEdit ? '保存' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import UserSelect from '@/components/UserSelect.vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  schedule: {
    type: Object,
    default: null
  },
  isEdit: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'close', 'submit'])

// 响应式数据
const formRef = ref()
const submitting = ref(false)
const selectedDaysOfWeek = ref([])

// 表单数据
const formData = reactive({
  name: '',
  description: '',
  taskTitle: '',
  taskDescription: '',
  taskType: 'Regular',
  priority: 'Medium',
  recurrenceType: 'Daily',
  recurrenceInterval: 1,
  daysOfWeek: null,
  dayOfMonth: null,
  monthOfYear: null,
  cronExpression: '',
  startDate: '',
  endDate: '',
  durationHours: 1,
  maxOccurrences: null,
  defaultAssigneeUserId: [],
  isEnabled: true
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入计划名称', trigger: 'blur' },
    { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  taskTitle: [
    { required: true, message: '请输入任务标题', trigger: 'blur' },
    { min: 2, max: 200, message: '长度在 2 到 200 个字符', trigger: 'blur' }
  ],
  taskType: [
    { required: true, message: '请选择任务类型', trigger: 'change' }
  ],
  priority: [
    { required: true, message: '请选择优先级', trigger: 'change' }
  ],
  recurrenceType: [
    { required: true, message: '请选择重复类型', trigger: 'change' }
  ],
  recurrenceInterval: [
    { required: true, message: '请输入重复间隔', trigger: 'blur' },
    { type: 'number', min: 1, max: 100, message: '间隔必须在 1 到 100 之间', trigger: 'blur' }
  ],
  startDate: [
    { required: true, message: '请选择开始日期', trigger: 'change' }
  ],
  cronExpression: [
    { 
      validator: (rule, value, callback) => {
        if (formData.recurrenceType === 'CustomCron' && !value) {
          callback(new Error('请输入Cron表达式'))
        } else {
          callback()
        }
      }, 
      trigger: 'blur' 
    }
  ],
  daysOfWeek: [
    { 
      validator: (rule, value, callback) => {
        if (formData.recurrenceType === 'Weekly' && selectedDaysOfWeek.value.length === 0) {
          callback(new Error('请至少选择一个星期'))
        } else {
          callback()
        }
      }, 
      trigger: 'change' 
    }
  ]
}

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 监听器
watch(() => props.schedule, (newSchedule) => {
  if (newSchedule && props.isEdit) {
    // 编辑模式，填充表单数据
    Object.assign(formData, {
      name: newSchedule.name || '',
      description: newSchedule.description || '',
      taskTitle: newSchedule.taskTemplateTitle || newSchedule.taskTitle || '',
      taskDescription: newSchedule.taskTemplateDescription || newSchedule.taskDescription || '',
      taskType: newSchedule.taskType || 'Regular',
      priority: newSchedule.defaultPriority || newSchedule.priority || 'Medium',
      recurrenceType: newSchedule.recurrenceType || 'Daily',
      recurrenceInterval: newSchedule.recurrenceInterval || 1,
      dayOfMonth: newSchedule.dayOfMonth,
      monthOfYear: newSchedule.monthOfYear,
      cronExpression: newSchedule.cronExpression || '',
      startDate: newSchedule.startDate || '',
      endDate: newSchedule.endDate ? newSchedule.endDate.split('T')[0] : '',
      durationHours: newSchedule.defaultDurationDays ? newSchedule.defaultDurationDays * 24 : (newSchedule.durationHours || 1),
      maxOccurrences: newSchedule.totalOccurrences || newSchedule.maxOccurrences,
      defaultAssigneeUserId: newSchedule.defaultAssigneeUserIds && newSchedule.defaultAssigneeUserIds.length > 0
        ? newSchedule.defaultAssigneeUserIds
        : (newSchedule.defaultAssigneeUserId ? [newSchedule.defaultAssigneeUserId] : []),
      isEnabled: newSchedule.status === 'Active' && newSchedule.isEnabled !== false
    })
    
    // 处理星期数据
    if (newSchedule.daysOfWeek) {
      try {
        selectedDaysOfWeek.value = JSON.parse(newSchedule.daysOfWeek)
      } catch (e) {
        selectedDaysOfWeek.value = []
      }
    } else {
      selectedDaysOfWeek.value = []
    }
  }
}, { immediate: true })

watch(() => props.modelValue, (visible) => {
  if (visible && !props.isEdit) {
    // 新建模式，重置表单
    resetForm()
  }
})

// 监听星期选择变化
watch(selectedDaysOfWeek, (newDays) => {
  formData.daysOfWeek = newDays.length > 0 ? JSON.stringify(newDays) : null
}, { deep: true })

// 方法
const resetForm = () => {
  Object.assign(formData, {
    name: '',
    description: '',
    taskTitle: '',
    taskDescription: '',
    taskType: 'Regular',
    priority: 'Medium',
    recurrenceType: 'Daily',
    recurrenceInterval: 1,
    daysOfWeek: null,
    dayOfMonth: null,
    monthOfYear: null,
    cronExpression: '',
    startDate: getDefaultStartDate(),
    endDate: '',
    durationHours: 1,
    maxOccurrences: null,
    defaultAssigneeUserId: [],
    isEnabled: true
  })
  selectedDaysOfWeek.value = []
  
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

const getDefaultStartDate = () => {
  const now = new Date()
  now.setMinutes(0, 0, 0)
  return now.toISOString().slice(0, 19)
}

const handleRecurrenceTypeChange = () => {
  // 重置相关字段
  formData.recurrenceInterval = 1
  formData.daysOfWeek = null
  formData.dayOfMonth = null
  formData.monthOfYear = null
  formData.cronExpression = ''
  selectedDaysOfWeek.value = []
}

const getIntervalHint = () => {
  const type = formData.recurrenceType
  const interval = formData.recurrenceInterval || 1
  
  const hints = {
    'Shift': interval === 1 ? '每班(12小时)' : `每${interval}班`,
    'Daily': interval === 1 ? '每天' : `每${interval}天`,
    'Weekly': interval === 1 ? '每周' : `每${interval}周`,
    'Monthly': interval === 1 ? '每月' : `每${interval}月`,
    'Quarterly': interval === 1 ? '每季度' : `每${interval}季度`,
    'Yearly': interval === 1 ? '每年' : `每${interval}年`
  }
  
  return hints[type] || ''
}

const handleClose = () => {
  emit('close')
}

// 生成Cron表达式的函数
const generateCronExpression = (recurrenceType, recurrenceInterval, startDate, daysOfWeek, dayOfMonth, monthOfYear) => {
  if (!startDate) return '0 0 0 1 1 ? *' // 默认值
  
  const date = new Date(startDate)
  const minute = date.getMinutes()
  const hour = date.getHours()
  const day = date.getDate()
  const month = date.getMonth() + 1

  switch (recurrenceType) {
    case 'Shift':
      // 每12小时：8:00 和 20:00
      return `${minute} 8,20 * * *` // 每天早班8点和晚班20点
      
    case 'Daily':
      if (recurrenceInterval === 1) {
        return `${minute} ${hour} * * *` // 每天，按创建时间执行
      } else {
        return `${minute} ${hour} */${recurrenceInterval} * *` // 每N天，按创建时间执行
      }
      
    case 'Weekly':
      if (daysOfWeek) {
        // 处理daysOfWeek可能是字符串或数组的情况
        let days = []
        if (typeof daysOfWeek === 'string') {
          try {
            days = JSON.parse(daysOfWeek)
          } catch (e) {
            console.error('解析daysOfWeek失败:', e)
            days = []
          }
        } else if (Array.isArray(daysOfWeek)) {
          days = daysOfWeek
        }

        if (days.length > 0) {
          // 转换星期几：前端0=周日，Cron 0=周日
          const cronDays = days.join(',')
          return `${minute} ${hour} * * ${cronDays}` // 按创建时间执行
        }
      }

      // 默认使用开始日期的星期几
      const dayOfWeek = date.getDay()
      return `${minute} ${hour} * * ${dayOfWeek}` // 按创建时间执行
      
    case 'Monthly':
      if (dayOfMonth) {
        return `${minute} ${hour} ${dayOfMonth} * *` // 每月特定日期，按创建时间执行
      } else {
        return `${minute} ${hour} ${day} * *` // 使用开始日期的日期，按创建时间执行
      }

    case 'Quarterly':
      // 每季度：1、4、7、10月的第一天
      if (dayOfMonth) {
        return `${minute} ${hour} ${dayOfMonth} 1,4,7,10 *` // 按创建时间执行
      } else {
        return `${minute} ${hour} 1 1,4,7,10 *` // 每季度第一天，按创建时间执行
      }

    case 'Yearly':
      if (monthOfYear && dayOfMonth) {
        return `${minute} ${hour} ${dayOfMonth} ${monthOfYear} *` // 每年特定月份和日期，按创建时间执行
      } else {
        return `${minute} ${hour} ${day} ${month} *` // 使用开始日期的月份和日期，按创建时间执行
      }
      
    default:
      return '0 0 0 1 1 ? *' // 默认值
  }
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    
    submitting.value = true
    
    // 生成Cron表达式
    let cronExpression
    if (formData.recurrenceType === 'CustomCron') {
      cronExpression = formData.cronExpression
    } else {
      cronExpression = generateCronExpression(
        formData.recurrenceType,
        formData.recurrenceInterval,
        formData.startDate,
        formData.daysOfWeek,
        formData.dayOfMonth,
        formData.monthOfYear
      )
    }
    
    // 时间格式化函数 - 转换为ISO 8601 UTC格式
    const formatDateTimeToUTC = (dateTimeStr) => {
      if (!dateTimeStr) return null

      // 创建Date对象（本地时间）
      const localDate = new Date(dateTimeStr)

      // 转换为UTC时间并返回ISO字符串
      return localDate.toISOString()
    }

    const formatDateToEndOfDayUTC = (dateStr) => {
      if (!dateStr) return null

      // 创建当天23:59:59的本地时间
      const localDate = new Date(`${dateStr}T23:59:59`)

      // 转换为UTC时间并返回ISO字符串
      return localDate.toISOString()
    }

    // 构建提交数据，映射到后端DTO字段
    const submitData = {
      name: formData.name,
      description: formData.description || null,
      templateTaskId: 0, // 暂时设为0，后端可能需要创建模板任务
      taskTemplateTitle: formData.taskTitle,
      taskTemplateDescription: formData.taskDescription || null,
      recurrenceType: formData.recurrenceType,
      recurrenceInterval: formData.recurrenceType !== 'CustomCron' ? formData.recurrenceInterval : null,
      daysOfWeek: formData.recurrenceType === 'Weekly' ? formData.daysOfWeek : null,
      dayOfMonth: ['Monthly', 'Yearly'].includes(formData.recurrenceType) ? formData.dayOfMonth : null,
      monthOfYear: formData.recurrenceType === 'Yearly' ? formData.monthOfYear : null,
      cronExpression: cronExpression, // 确保始终包含Cron表达式
      startDate: formatDateTimeToUTC(formData.startDate), // 转换为UTC格式
      endDate: formatDateToEndOfDayUTC(formData.endDate), // 转换为UTC格式
      totalOccurrences: formData.maxOccurrences || null,
      defaultAssigneeUserId: Array.isArray(formData.defaultAssigneeUserId) && formData.defaultAssigneeUserId.length > 0 ? formData.defaultAssigneeUserId[0] : null,
      defaultAssigneeUserIds: Array.isArray(formData.defaultAssigneeUserId) && formData.defaultAssigneeUserId.length > 0 ? formData.defaultAssigneeUserId : [],
      defaultPriority: formData.priority,
      defaultDurationDays: formData.durationHours ? Math.ceil(formData.durationHours / 24) : 1,
      defaultAssetId: null, // 可以从资产选择器获取
      defaultLocationId: null // 可以从位置选择器获取
    }
    
    console.log('生成的Cron表达式:', cronExpression)
    console.log('提交数据:', submitData)
    
    emit('submit', submitData)
  } catch (error) {
    console.error('表单验证失败:', error)
    ElMessage.error('请检查表单填写是否正确')
  } finally {
    submitting.value = false
  }
}
</script>

<style scoped>
.periodic-form {
  max-height: 600px;
  overflow-y: auto;
  padding-right: 10px;
}

.form-section {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.form-section:last-child {
  border-bottom: none;
}

.section-title {
  margin: 0 0 20px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.interval-hint,
.duration-hint,
.max-hint,
.assignee-hint,
.time-hint {
  margin-left: 10px;
  margin-top: 4px;
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
}

.cron-hint {
  margin-top: 8px;
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-checkbox-group) {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

:deep(.el-checkbox) {
  margin-right: 0;
}
</style>