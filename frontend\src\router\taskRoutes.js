// 任务管理模块路由配置
// 支持现代化任务管理的完整路由体系

export const taskRoutes = [
  {
    path: '/tasks',
    component: () => import('@/layouts/BasicLayout.vue'),
    redirect: '/tasks/kanban',
    meta: {
      title: '任务管理',
      icon: 'Document',
      requiresAuth: true,
      roles: ['admin', 'user', 'manager'],
      breadcrumb: [{ title: '首页', to: '/' }, { title: '任务管理' }]
    },
    children: [
      // 看板视图 - 默认主页
      {
        path: 'kanban',
        name: 'TaskKanban',
        component: () => import('@/views/tasks/ModernKanbanView.vue'),
        meta: {
          title: '任务看板',
          icon: 'Grid',
          keepAlive: true,
          breadcrumb: [
            { title: '首页', to: '/' },
            { title: '任务管理', to: '/tasks' },
            { title: '任务看板' }
          ]
        }
      },

      // 列表视图
      {
        path: 'list',
        name: 'TaskList',
        component: () => import('@/views/tasks/TaskListView.vue'),
        meta: {
          title: '任务列表',
          icon: 'List',
          keepAlive: true,
          breadcrumb: [
            { title: '首页', to: '/' },
            { title: '任务管理', to: '/tasks' },
            { title: '任务列表' }
          ]
        }
      },

      // 甘特图视图
      {
        path: 'gantt',
        name: 'TaskGantt',
        component: () => import('@/views/tasks/TaskGanttView.vue'),
        meta: {
          title: '甘特图',
          icon: 'TrendCharts',
          keepAlive: true,
          breadcrumb: [
            { title: '首页', to: '/' },
            { title: '任务管理', to: '/tasks' },
            { title: '甘特图' }
          ]
        }
      },

      // 任务详情
      {
        path: ':taskId(\\d+)',
        name: 'TaskDetail',
        component: () => import('@/views/tasks/TaskDetailView.vue'),
        meta: {
          title: '任务详情',
          hideInMenu: true,
          breadcrumb: [
            { title: '首页', to: '/' },
            { title: '任务管理', to: '/tasks' },
            { title: '任务详情' }
          ]
        },
        props: (route) => ({
          taskId: parseInt(route.params.taskId),
          tab: route.query.tab || 'details'
        })
      },

      // 任务创建/编辑
      {
        path: 'create',
        name: 'TaskCreate',
        component: () => import('@/views/tasks/TaskFormView.vue'),
        meta: {
          title: '创建任务',
          hideInMenu: true,
          breadcrumb: [
            { title: '首页', to: '/' },
            { title: '任务管理', to: '/tasks' },
            { title: '创建任务' }
          ]
        }
      },
      {
        path: 'edit/:taskId(\\d+)',
        name: 'TaskEdit',
        component: () => import('@/views/tasks/TaskFormView.vue'),
        meta: {
          title: '编辑任务',
          hideInMenu: true,
          breadcrumb: [
            { title: '首页', to: '/' },
            { title: '任务管理', to: '/tasks' },
            { title: '编辑任务' }
          ]
        },
        props: (route) => ({
          taskId: parseInt(route.params.taskId),
          isEdit: true
        })
      },

      // 任务模板管理
      {
        path: 'templates',
        name: 'TaskTemplates',
        component: () => import('@/views/tasks/TaskTemplatesView.vue'),
        meta: {
          title: '任务模板',
          icon: 'DocumentCopy',
          roles: ['admin', 'manager'],
          breadcrumb: [
            { title: '首页', to: '/' },
            { title: '任务管理', to: '/tasks' },
            { title: '任务模板' }
          ]
        }
      },

      // 周期性任务
      {
        path: 'periodic',
        name: 'PeriodicTasks',
        component: () => import('@/views/tasks/PeriodicTaskView.vue'),
        meta: {
          title: '周期性任务',
          icon: 'RefreshLeft',
          breadcrumb: [
            { title: '首页', to: '/' },
            { title: '任务管理', to: '/tasks' },
            { title: '周期性任务' }
          ]
        }
      },

      // 数据分析
      {
        path: 'analytics',
        name: 'TaskAnalytics',
        component: () => import('@/views/tasks/TaskAnalyticsView.vue'),
        meta: {
          title: '数据分析',
          icon: 'DataAnalysis',
          roles: ['admin', 'manager'],
          breadcrumb: [
            { title: '首页', to: '/' },
            { title: '任务管理', to: '/tasks' },
            { title: '数据分析' }
          ]
        }
      },

      // 工作负载视图
      {
        path: 'workload',
        name: 'TaskWorkload',
        component: () => import('@/views/tasks/WorkloadView.vue'),
        meta: {
          title: '工作负载',
          icon: 'User',
          roles: ['admin', 'manager'],
          breadcrumb: [
            { title: '首页', to: '/' },
            { title: '任务管理', to: '/tasks' },
            { title: '工作负载' }
          ]
        }
      },

      // 班次统计视图
      {
        path: 'shift-statistics',
        name: 'ShiftStatistics',
        component: () => import('@/views/tasks/ShiftStatisticsView.vue'),
        meta: {
          title: '班次统计',
          icon: 'DataAnalysis',
          keepAlive: true,
          breadcrumb: [
            { title: '首页', to: '/' },
            { title: '任务管理', to: '/tasks' },
            { title: '班次统计' }
          ]
        }
      },

      // 我的任务 - 个人视图
      {
        path: 'my',
        name: 'MyTasks',
        component: () => import('@/views/tasks/MyTasksView.vue'),
        meta: {
          title: '我的任务',
          icon: 'UserFilled',
          keepAlive: true,
          breadcrumb: [
            { title: '首页', to: '/' },
            { title: '任务管理', to: '/tasks' },
            { title: '我的任务' }
          ]
        },
        children: [
          {
            path: '',
            redirect: 'assigned'
          },
          {
            path: 'assigned',
            name: 'MyAssignedTasks',
            component: () => import('@/views/tasks/my/AssignedTasksView.vue'),
            meta: {
              title: '我负责的',
              hideInMenu: true
            }
          },
          {
            path: 'participating',
            name: 'MyParticipatingTasks',
            component: () => import('@/views/tasks/my/ParticipatingTasksView.vue'),
            meta: {
              title: '我参与的',
              hideInMenu: true
            }
          },
          {
            path: 'created',
            name: 'MyCreatedTasks',
            component: () => import('@/views/tasks/my/CreatedTasksView.vue'),
            meta: {
              title: '我创建的',
              hideInMenu: true
            }
          },
          {
            path: 'watched',
            name: 'MyWatchedTasks',
            component: () => import('@/views/tasks/my/WatchedTasksView.vue'),
            meta: {
              title: '我关注的',
              hideInMenu: true
            }
          }
        ]
      },

      // 团队视图
      {
        path: 'team',
        name: 'TeamTasks',
        component: () => import('@/views/tasks/TeamTasksView.vue'),
        meta: {
          title: '团队任务',
          icon: 'Postcard',
          roles: ['admin', 'manager', 'team_lead'],
          breadcrumb: [
            { title: '首页', to: '/' },
            { title: '任务管理', to: '/tasks' },
            { title: '团队任务' }
          ]
        }
      },

      // 批量操作历史
      {
        path: 'batch-history',
        name: 'BatchHistory',
        component: () => import('@/views/tasks/BatchHistoryView.vue'),
        meta: {
          title: '批量操作历史',
          icon: 'Clock',
          hideInMenu: true,
          roles: ['admin', 'manager'],
          breadcrumb: [
            { title: '首页', to: '/' },
            { title: '任务管理', to: '/tasks' },
            { title: '批量操作历史' }
          ]
        }
      },

      // 任务归档
      {
        path: 'archive',
        name: 'TaskArchive',
        component: () => import('@/views/tasks/TaskArchiveView.vue'),
        meta: {
          title: '任务归档',
          icon: 'Box',
          roles: ['admin', 'manager'],
          breadcrumb: [
            { title: '首页', to: '/' },
            { title: '任务管理', to: '/tasks' },
            { title: '任务归档' }
          ]
        }
      },

      // 回收站
      {
        path: 'trash',
        name: 'TaskTrash',
        component: () => import('@/views/tasks/TaskTrashView.vue'),
        meta: {
          title: '回收站',
          icon: 'Delete',
          hideInMenu: true,
          roles: ['admin'],
          breadcrumb: [
            { title: '首页', to: '/' },
            { title: '任务管理', to: '/tasks' },
            { title: '回收站' }
          ]
        }
      },

      // 设置页面
      {
        path: 'settings',
        name: 'TaskSettings',
        component: () => import('@/views/tasks/TaskSettingsView.vue'),
        meta: {
          title: '任务设置',
          icon: 'Setting',
          roles: ['admin'],
          breadcrumb: [
            { title: '首页', to: '/' },
            { title: '任务管理', to: '/tasks' },
            { title: '任务设置' }
          ]
        },
        children: [
          {
            path: '',
            redirect: 'general'
          },
          {
            path: 'general',
            name: 'TaskGeneralSettings',
            component: () => import('@/views/tasks/settings/GeneralSettings.vue'),
            meta: {
              title: '基本设置',
              hideInMenu: true
            }
          },
          {
            path: 'workflow',
            name: 'TaskWorkflowSettings',
            component: () => import('@/views/tasks/settings/WorkflowSettings.vue'),
            meta: {
              title: '工作流设置',
              hideInMenu: true
            }
          },
          {
            path: 'notification',
            name: 'TaskNotificationSettings',
            component: () => import('@/views/tasks/settings/NotificationSettings.vue'),
            meta: {
              title: '通知设置',
              hideInMenu: true
            }
          },
          {
            path: 'permissions',
            name: 'TaskPermissionSettings',
            component: () => import('@/views/tasks/settings/PermissionSettings.vue'),
            meta: {
              title: '权限设置',
              hideInMenu: true
            }
          }
        ]
      }
    ]
  },

  // 任务相关的独立页面
  {
    path: '/task-quick-create',
    name: 'TaskQuickCreate',
    component: () => import('@/views/tasks/TaskQuickCreateView.vue'),
    meta: {
      title: '快速创建任务',
      hideInMenu: true,
      requiresAuth: true,
      layout: 'minimal' // 使用简化布局
    }
  },

  // 任务分享页面（支持外部访问）
  {
    path: '/task-share/:shareToken',
    name: 'TaskShare',
    component: () => import('@/views/tasks/TaskShareView.vue'),
    meta: {
      title: '任务分享',
      hideInMenu: true,
      requiresAuth: false,
      layout: 'public'
    },
    props: (route) => ({
      shareToken: route.params.shareToken
    })
  },

  // 任务打印页面
  {
    path: '/task-print/:taskIds',
    name: 'TaskPrint',
    component: () => import('@/views/tasks/TaskPrintView.vue'),
    meta: {
      title: '打印任务',
      hideInMenu: true,
      requiresAuth: true,
      layout: 'print'
    },
    props: (route) => ({
      taskIds: route.params.taskIds.split(',').map(id => parseInt(id))
    })
  }
]

// 任务管理菜单配置
export const taskMenuConfig = {
  title: '任务管理',
  icon: 'Document',
  path: '/tasks',
  children: [
    {
      title: '任务看板',
      icon: 'Grid',
      path: '/tasks/kanban',
      component: 'ModernKanbanView'
    },
    {
      title: '任务列表',
      icon: 'List',
      path: '/tasks/list',
      component: 'TaskListView'
    },
    {
      title: '甘特图',
      icon: 'TrendCharts',
      path: '/tasks/gantt',
      component: 'TaskGanttView'
    },
    {
      title: '我的任务',
      icon: 'UserFilled',
      path: '/tasks/my',
      component: 'MyTasksView'
    },
    {
      title: '团队任务',
      icon: 'Postcard',
      path: '/tasks/team',
      component: 'TeamTasksView',
      roles: ['admin', 'manager', 'team_lead']
    },
    {
      title: '周期性任务',
      icon: 'RefreshLeft',
      path: '/tasks/periodic',
      component: 'PeriodicTaskView'
    },
    {
      title: '任务模板',
      icon: 'DocumentCopy',
      path: '/tasks/templates',
      component: 'TaskTemplatesView',
      roles: ['admin', 'manager']
    },
    {
      title: '数据分析',
      icon: 'DataAnalysis',
      path: '/tasks/analytics',
      component: 'TaskAnalyticsView',
      roles: ['admin', 'manager']
    },
    {
      title: '工作负载',
      icon: 'User',
      path: '/tasks/workload',
      component: 'WorkloadView',
      roles: ['admin', 'manager']
    },
    {
      title: '班次统计',
      icon: 'DataAnalysis',
      path: '/tasks/shift-statistics',
      component: 'ShiftStatisticsView'
    },
    {
      title: '任务归档',
      icon: 'Box',
      path: '/tasks/archive',
      component: 'TaskArchiveView',
      roles: ['admin', 'manager']
    },
    {
      title: '任务设置',
      icon: 'Setting',
      path: '/tasks/settings',
      component: 'TaskSettingsView',
      roles: ['admin']
    }
  ]
}

// 快捷操作配置
export const taskShortcuts = [
  {
    title: '快速创建任务',
    icon: 'Plus',
    action: 'quick-create',
    hotkey: 'Ctrl+Shift+T',
    component: 'TaskQuickCreateDialog'
  },
  {
    title: '我的待办',
    icon: 'Bell',
    action: 'my-todos',
    path: '/tasks/my/assigned?status=Todo',
    badge: 'todoCount'
  },
  {
    title: '逾期任务',
    icon: 'WarningFilled',
    action: 'overdue-tasks',
    path: '/tasks/list?overdue=true',
    badge: 'overdueCount'
  },
  {
    title: '今日完成',
    icon: 'Check',
    action: 'today-completed',
    path: '/tasks/list?completed=today',
    badge: 'todayCompletedCount'
  }
]

// 路由守卫配置
export const taskRouteGuards = {
  // 任务详情页面权限检查
  beforeEnterTaskDetail: async (to, from, next) => {
    const taskId = parseInt(to.params.taskId)
    const userStore = useUserStore()
    const taskStore = useTaskEnhancedStore()
    
    try {
      // 检查任务是否存在
      const task = await taskStore.getTaskById(taskId)
      if (!task) {
        next({ name: 'NotFound' })
        return
      }
      
      // 检查用户权限
      const hasPermission = await userStore.hasTaskPermission(taskId, 'read')
      if (!hasPermission) {
        next({ name: 'Forbidden' })
        return
      }
      
      next()
    } catch (error) {
      console.error('任务权限检查失败:', error)
      next({ name: 'Error', params: { message: '权限检查失败' } })
    }
  },

  // 管理页面权限检查
  beforeEnterTaskManagement: (to, from, next) => {
    const userStore = useUserStore()
    const requiredRoles = to.meta.roles || []
    
    if (requiredRoles.length === 0) {
      next()
      return
    }
    
    const hasRole = requiredRoles.some(role => userStore.hasRole(role))
    if (!hasRole) {
      next({ name: 'Forbidden' })
      return
    }
    
    next()
  }
}

// 导出默认配置
export default {
  routes: taskRoutes,
  menu: taskMenuConfig,
  shortcuts: taskShortcuts,
  guards: taskRouteGuards
}