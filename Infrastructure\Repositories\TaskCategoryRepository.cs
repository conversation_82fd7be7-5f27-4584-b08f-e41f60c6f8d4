using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using ItAssetsSystem.Application.Features.Tasks.Repositories;
using ItAssetsSystem.Application.Features.Tasks.Dtos;
using ItAssetsSystem.Domain.Entities.Tasks;
using ItAssetsSystem.Infrastructure.Data;

namespace ItAssetsSystem.Infrastructure.Repositories
{
    /// <summary>
    /// 任务分类仓储实现
    /// </summary>
    public class TaskCategoryRepository : ITaskCategoryRepository
    {
        private readonly AppDbContext _context;

        public TaskCategoryRepository(AppDbContext context)
        {
            _context = context;
        }

        /// <summary>
        /// 获取所有任务分类
        /// </summary>
        public async Task<List<TaskCategory>> GetAllAsync(CancellationToken cancellationToken = default)
        {
            return await _context.TaskCategories
                .OrderBy(c => c.SortOrder)
                .ThenBy(c => c.Name)
                .ToListAsync(cancellationToken);
        }

        /// <summary>
        /// 获取启用的任务分类
        /// </summary>
        public async Task<List<TaskCategory>> GetActiveAsync(CancellationToken cancellationToken = default)
        {
            return await _context.TaskCategories
                .Where(c => c.IsActive)
                .OrderBy(c => c.SortOrder)
                .ThenBy(c => c.Name)
                .ToListAsync(cancellationToken);
        }

        /// <summary>
        /// 根据ID获取任务分类
        /// </summary>
        public async Task<TaskCategory?> GetByIdAsync(int categoryId, CancellationToken cancellationToken = default)
        {
            return await _context.TaskCategories
                .FirstOrDefaultAsync(c => c.CategoryId == categoryId, cancellationToken);
        }

        /// <summary>
        /// 根据名称获取任务分类
        /// </summary>
        public async Task<TaskCategory?> GetByNameAsync(string name, CancellationToken cancellationToken = default)
        {
            return await _context.TaskCategories
                .FirstOrDefaultAsync(c => c.Name == name, cancellationToken);
        }

        /// <summary>
        /// 分页查询任务分类
        /// </summary>
        public async Task<(List<TaskCategory> Items, int TotalCount)> GetPagedAsync(TaskCategoryQueryRequestDto request, CancellationToken cancellationToken = default)
        {
            var query = _context.TaskCategories.AsQueryable();

            // 搜索过滤
            if (!string.IsNullOrWhiteSpace(request.SearchKeyword))
            {
                var keyword = request.SearchKeyword.Trim();
                query = query.Where(c => c.Name.Contains(keyword) || 
                                        (c.Description != null && c.Description.Contains(keyword)));
            }

            // 状态过滤
            if (request.IsActive.HasValue)
            {
                query = query.Where(c => c.IsActive == request.IsActive.Value);
            }

            // 获取总数
            var totalCount = await query.CountAsync(cancellationToken);

            // 排序
            query = ApplySorting(query, request.SortBy, request.SortDirection);

            // 分页
            var items = await query
                .Skip((request.PageNumber - 1) * request.PageSize)
                .Take(request.PageSize)
                .ToListAsync(cancellationToken);

            return (items, totalCount);
        }

        /// <summary>
        /// 创建任务分类
        /// </summary>
        public async Task<TaskCategory> CreateAsync(TaskCategory category, CancellationToken cancellationToken = default)
        {
            category.CreatedAt = DateTime.UtcNow;
            category.UpdatedAt = DateTime.UtcNow;

            _context.TaskCategories.Add(category);
            await _context.SaveChangesAsync(cancellationToken);
            return category;
        }

        /// <summary>
        /// 更新任务分类
        /// </summary>
        public async Task<TaskCategory> UpdateAsync(TaskCategory category, CancellationToken cancellationToken = default)
        {
            category.UpdatedAt = DateTime.UtcNow;

            _context.TaskCategories.Update(category);
            await _context.SaveChangesAsync(cancellationToken);
            return category;
        }

        /// <summary>
        /// 删除任务分类
        /// </summary>
        public async Task<bool> DeleteAsync(int categoryId, CancellationToken cancellationToken = default)
        {
            var category = await GetByIdAsync(categoryId, cancellationToken);
            if (category == null)
                return false;

            // 检查是否有任务使用此分类
            var hasRelatedTasks = await _context.Tasks
                .AnyAsync(t => t.CategoryId == categoryId && !t.IsDeleted, cancellationToken);

            if (hasRelatedTasks)
            {
                throw new InvalidOperationException("无法删除分类，因为还有任务使用此分类");
            }

            _context.TaskCategories.Remove(category);
            await _context.SaveChangesAsync(cancellationToken);
            return true;
        }

        /// <summary>
        /// 检查分类是否存在
        /// </summary>
        public async Task<bool> ExistsAsync(int categoryId, CancellationToken cancellationToken = default)
        {
            return await _context.TaskCategories
                .AnyAsync(c => c.CategoryId == categoryId, cancellationToken);
        }

        /// <summary>
        /// 检查分类名称是否已存在
        /// </summary>
        public async Task<bool> NameExistsAsync(string name, int? excludeCategoryId = null, CancellationToken cancellationToken = default)
        {
            var query = _context.TaskCategories.Where(c => c.Name == name);

            if (excludeCategoryId.HasValue)
            {
                query = query.Where(c => c.CategoryId != excludeCategoryId.Value);
            }

            return await query.AnyAsync(cancellationToken);
        }

        /// <summary>
        /// 获取分类下的任务数量
        /// </summary>
        public async Task<int> GetTaskCountAsync(int categoryId, CancellationToken cancellationToken = default)
        {
            return await _context.Tasks
                .CountAsync(t => t.CategoryId == categoryId && !t.IsDeleted, cancellationToken);
        }

        /// <summary>
        /// 应用排序
        /// </summary>
        private static IQueryable<TaskCategory> ApplySorting(IQueryable<TaskCategory> query, string? sortBy, string? sortDirection)
        {
            var isDescending = string.Equals(sortDirection, "desc", StringComparison.OrdinalIgnoreCase);

            return sortBy?.ToLowerInvariant() switch
            {
                "name" => isDescending ? query.OrderByDescending(c => c.Name) : query.OrderBy(c => c.Name),
                "createdat" => isDescending ? query.OrderByDescending(c => c.CreatedAt) : query.OrderBy(c => c.CreatedAt),
                "updatedat" => isDescending ? query.OrderByDescending(c => c.UpdatedAt) : query.OrderBy(c => c.UpdatedAt),
                "sortorder" or _ => isDescending ? query.OrderByDescending(c => c.SortOrder).ThenByDescending(c => c.Name) 
                                                 : query.OrderBy(c => c.SortOrder).ThenBy(c => c.Name)
            };
        }
    }
}
