<template>
  <div class="location-detail">
    <el-card class="detail-card">
      <template #header>
        <div class="card-header">
          <h2>位置详情</h2>
          <el-button type="primary" @click="goBack">返回</el-button>
        </div>
      </template>
      
      <el-skeleton :loading="loading" animated>
        <template #default>
          <div class="detail-content">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="位置名称">{{ location.name }}</el-descriptions-item>
              <el-descriptions-item label="位置代码">{{ location.code }}</el-descriptions-item>
              <el-descriptions-item label="位置路径">{{ location.path }}</el-descriptions-item>
              <el-descriptions-item label="所属部门">{{ location.departmentName || '未分配' }}</el-descriptions-item>
              <el-descriptions-item label="管理员">{{ managerName || '未分配' }}</el-descriptions-item>
              <el-descriptions-item label="创建时间">{{ formatDate(location.createdAt) }}</el-descriptions-item>
              <el-descriptions-item label="更新时间">{{ formatDate(location.updatedAt) }}</el-descriptions-item>
              <el-descriptions-item label="描述" :span="2">
                {{ location.description || '暂无描述' }}
              </el-descriptions-item>
            </el-descriptions>
            
            <!-- 使用人列表 -->
            <div class="section-title">
              <h3>位置使用人</h3>
            </div>
            <el-table v-if="usageUsers.length > 0" :data="usageUsers" border style="width: 100%">
              <el-table-column prop="name" label="姓名" width="120"></el-table-column>
              <el-table-column prop="username" label="用户名" width="120"></el-table-column>
              <el-table-column prop="email" label="邮箱"></el-table-column>
              <el-table-column prop="phone" label="电话" width="120"></el-table-column>
            </el-table>
            <el-empty v-else description="暂无使用人信息"></el-empty>
            
            <!-- 资产列表 -->
            <div class="section-title">
              <h3>关联资产</h3>
            </div>
            <el-table v-if="relatedAssets.length > 0" :data="relatedAssets" border style="width: 100%">
              <el-table-column prop="code" label="资产编号" width="120"></el-table-column>
              <el-table-column prop="name" label="资产名称" width="180"></el-table-column>
              <el-table-column prop="typeName" label="资产类型" width="120"></el-table-column>
              <el-table-column prop="statusText" label="状态" width="100">
                <template #default="scope">
                  <el-tag :type="getStatusType(scope.row.status)">{{ formatStatus(scope.row.status) }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="relationType" label="关联类型" width="120">
                <template #default="scope">
                  {{ formatRelationType(scope.row.relationType) }}
                </template>
              </el-table-column>
              <el-table-column prop="related_at" label="关联时间" width="180">
                <template #default="scope">
                  {{ formatDate(scope.row.relatedAt) }}
                </template>
              </el-table-column>
            </el-table>
            <el-empty v-else description="暂无关联资产"></el-empty>
            
            <!-- 位置历史 -->
            <div class="section-title">
              <h3>位置历史</h3>
            </div>
            <el-table v-if="locationHistory.length > 0" :data="locationHistory" border style="width: 100%">
              <el-table-column prop="operationType" label="操作类型" width="120">
                <template #default="scope">
                  {{ formatOperationType(scope.row.operationType) }}
                </template>
              </el-table-column>
              <el-table-column prop="changes" label="变更内容" min-width="300">
                <template #default="scope">
                  <div v-for="(change, field) in scope.row.changes" :key="field" class="change-item">
                    <strong>{{ getFieldName(field) }}:</strong> 
                    {{ change.oldValue || '空' }} → {{ change.newValue || '空' }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="operatorName" label="操作人" width="120"></el-table-column>
              <el-table-column prop="createdAt" label="操作时间" width="180">
                <template #default="scope">
                  {{ formatDate(scope.row.createdAt) }}
                </template>
              </el-table-column>
            </el-table>
            <el-empty v-else description="暂无位置历史记录"></el-empty>
          </div>
        </template>
      </el-skeleton>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import locationApi from '@/api/location'
import userApi from '@/api/user'
import { formatDate } from '@/utils/format'

const route = useRoute()
const router = useRouter()
const locationId = route.params.id

const loading = ref(true)
const location = ref({})
const locationHistory = ref([])
const relatedAssets = ref([])
const usageUsers = ref([])
const managerName = ref('')

onMounted(async () => {
  await fetchLocationDetail()
})

const fetchLocationDetail = async () => {
  loading.value = true
  try {
    console.log(`获取位置详情，ID: ${locationId}`)
    const res = await locationApi.getLocationDetail(locationId)
    if (res.success) {
      location.value = res.data
      console.log('获取到位置详情:', location.value)
      
      // 获取位置关联用户
      await fetchLocationUsers()
      
      // 获取关联资产
      await fetchRelatedAssets()
      
      // 获取位置历史
      await fetchLocationHistory()
    } else {
      ElMessage.error(res.message || '获取位置详情失败')
    }
  } catch (error) {
    console.error('获取位置详情失败:', error)
    ElMessage.error('获取位置详情失败')
  } finally {
    loading.value = false
  }
}

const fetchLocationUsers = async () => {
  try {
    console.log(`获取位置用户关联信息，位置ID: ${locationId}`)
    const res = await locationApi.getLocationUsers(locationId)
    if (res.success && res.data) {
      // 过滤获取使用人（用户类型为0）
      const users = res.data.filter(user => user.userType === 0)
      console.log(`获取到 ${users.length} 个位置使用人`)
      
      // 解析用户ID列表，获取详细用户信息
      if (users.length > 0) {
        const userIds = users.map(user => user.userId)
        await fetchUserDetails(userIds)
      }
      
      // 获取管理员（用户类型为1）
      const managers = res.data.filter(user => user.userType === 1)
      if (managers.length > 0) {
        const managerId = managers[0].userId
        await fetchManagerDetail(managerId)
      }
    } else {
      console.log('未获取到位置用户关联信息')
    }
  } catch (error) {
    console.error('获取位置用户关联信息失败:', error)
  }
}

const fetchUserDetails = async (userIds) => {
  try {
    console.log(`获取使用人详情，用户IDs: ${userIds.join(', ')}`)
    const res = await userApi.getUsersByIds(userIds)
    if (res.success && res.data) {
      usageUsers.value = res.data
      console.log(`成功获取 ${usageUsers.value.length} 个用户详情`)
    } else {
      ElMessage.warning('获取使用人详情失败')
    }
  } catch (error) {
    console.error('获取使用人详情失败:', error)
  }
}

const fetchManagerDetail = async (managerId) => {
  try {
    console.log(`获取管理员详情，用户ID: ${managerId}`)
    const res = await userApi.getUserDetail(managerId)
    if (res.success && res.data) {
      managerName.value = res.data.name || res.data.username
      console.log(`成功获取管理员信息: ${managerName.value}`)
    } else {
      managerName.value = '未知管理员'
    }
  } catch (error) {
    console.error('获取管理员详情失败:', error)
    managerName.value = '未知管理员'
  }
}

const fetchRelatedAssets = async () => {
  try {
    console.log(`获取位置关联资产，位置ID: ${locationId}`)
    const res = await locationApi.getLocationAssets({
      locationId: locationId,
      pageIndex: 1,
      pageSize: 1000 // 获取足够多的资产用于详情页显示
    })
    if (res.success) {
      relatedAssets.value = res.data.items || []
      console.log(`获取到 ${relatedAssets.value.length} 个关联资产`)
    } else {
      ElMessage.warning(res.message || '获取关联资产失败')
    }
  } catch (error) {
    console.error('获取关联资产失败:', error)
  }
}

const fetchLocationHistory = async () => {
  try {
    console.log(`获取位置历史，位置ID: ${locationId}`)
    const res = await locationApi.getLocationHistory(locationId)
    if (res.success) {
      locationHistory.value = res.data || []
      
      // 处理变更内容为JSON对象
      locationHistory.value.forEach(history => {
        if (typeof history.changes === 'string') {
          try {
            history.changes = JSON.parse(history.changes)
          } catch (e) {
            history.changes = {}
            console.error('解析历史变更内容失败:', e)
          }
        }
      })
      
      console.log(`获取到 ${locationHistory.value.length} 条位置历史记录`)
    } else {
      ElMessage.warning(res.message || '获取位置历史失败')
    }
  } catch (error) {
    console.error('获取位置历史失败:', error)
  }
}

// 格式化状态
const formatStatus = (status) => {
  const statusMap = {
    1: '在用',
    2: '闲置',
    3: '维修',
    4: '报废',
    5: '借用'
  }
  return statusMap[status] || '未知'
}

// 获取状态标签类型
const getStatusType = (status) => {
  const typeMap = {
    1: 'success',
    2: 'info',
    3: 'warning',
    4: 'danger',
    5: 'primary'
  }
  return typeMap[status] || ''
}

// 格式化关联类型
const formatRelationType = (type) => {
  const typeMap = {
    1: '存放',
    2: '使用',
    3: '管理'
  }
  return typeMap[type] || '未知'
}

// 格式化操作类型
const formatOperationType = (type) => {
  const typeMap = {
    1: '创建',
    2: '修改',
    3: '删除',
    4: '关联资产',
    5: '解除资产'
  }
  return typeMap[type] || '未知'
}

// 获取字段名
const getFieldName = (field) => {
  const fieldMap = {
    'name': '名称',
    'code': '代码',
    'description': '描述',
    'parentId': '父位置',
    'departmentId': '部门'
  }
  return fieldMap[field] || field
}

// 返回上一页
const goBack = () => {
  router.back()
}
</script>

<style scoped>
.location-detail {
  padding: 20px;
}

.detail-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h2 {
  margin: 0;
  font-size: 18px;
}

.detail-content {
  margin-top: 20px;
}

.section-title {
  margin: 20px 0 10px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebeef5;
}

.section-title h3 {
  margin: 0;
  font-size: 16px;
  color: #303133;
}

.change-item {
  margin-bottom: 5px;
  line-height: 1.5;
}
</style> 