import os
import argparse

# 默认忽略的目录（C#项目常见生成目录）
DEFAULT_IGNORE_DIRS = {
    'bin', 'obj', 'packages', '.vs', 'TestResults', 
    '_ReSharper.Caches', '.idea', '.vscode', 'node_modules',
    'Debug', 'Release', 'x64', 'x86', 'bld', 'artifacts'
}

# 默认忽略的文件扩展名
DEFAULT_IGNORE_EXTENSIONS = {
    '.pdb', '.cache', '.exe', '.dll', '.suo', '.user',
    '.aps', '.ncb', '.sdf', '.opensdf', '.log', '.lib'
}

def is_generated_file(file_path):
    """判断是否是自动生成的文件"""
    filename = os.path.basename(file_path).lower()
    return (
        filename.endswith('.designer.cs') or 
        filename.endswith('.generated.cs') or
        'TemporaryGeneratedFile' in filename
    )

def gather_csharp_code(directory, output_file, additional_extensions=None, extra_ignore_dirs=None):
    """
    收集C#源代码，自动忽略生成文件和目录
    
    :param directory: 项目根目录
    :param output_file: 输出文件路径
    :param additional_extensions: 额外要包含的文件扩展名
    :param extra_ignore_dirs: 额外要忽略的目录
    """
    ignore_dirs = DEFAULT_IGNORE_DIRS.copy()
    if extra_ignore_dirs:
        ignore_dirs.update(extra_ignore_dirs)

    extensions = {'.cs'}
    if additional_extensions:
        extensions.update(additional_extensions)

    with open(output_file, 'w', encoding='utf-8') as out_f:
        # 写入目录结构（只显示源码目录）
        out_f.write("# C#项目源代码结构\n\n")
        out_f.write("## 有效源代码目录\n```\n")
        
        for root, dirs, files in os.walk(directory):
            # 过滤忽略的目录
            dirs[:] = [
                d for d in dirs 
                if d not in ignore_dirs 
                and not d.startswith('.')
            ]
            
            level = root.replace(directory, '').count(os.sep)
            indent = ' ' * 4 * level
            out_f.write(f"{indent}{os.path.basename(root)}/\n")
        
        out_f.write("```\n\n")
        
        # 收集并写入源代码
        out_f.write("## 源代码文件内容\n\n")
        for root, dirs, files in os.walk(directory):
            dirs[:] = [d for d in dirs if d not in ignore_dirs]
            
            for file in files:
                ext = os.path.splitext(file)[1].lower()
                file_path = os.path.join(root, file)
                
                # 过滤条件
                if (ext in extensions and 
                    not is_generated_file(file_path) and 
                    ext not in DEFAULT_IGNORE_EXTENSIONS):
                    
                    try:
                        with open(file_path, 'r', encoding='utf-8') as in_f:
                            content = in_f.read().strip()
                        
                        rel_path = os.path.relpath(file_path, directory)
                        out_f.write(f"### {rel_path}\n```csharp\n")
                        out_f.write(content)
                        out_f.write("\n```\n\n")
                    except Exception as e:
                        out_f.write(f"### {rel_path} (读取错误: {str(e)})\n\n")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='C#源代码收集工具')
    parser.add_argument('directory', help='项目根目录')
    parser.add_argument('-o', '--output', default='csharp_source.md', help='输出文件')
    parser.add_argument('-e', '--extensions', nargs='+', default=[], 
                       help='额外包含的扩展名（如.xaml,.cshtml）')
    parser.add_argument('-i', '--ignore', nargs='+', default=[], 
                       help='额外要忽略的目录')
    
    args = parser.parse_args()
    
    print("正在收集C#源代码...")
    print(f"项目目录: {args.directory}")
    print(f"包含的文件类型: .cs, {', '.join(args.extensions)}")
    print(f"额外忽略的目录: {', '.join(args.ignore)}")
    
    gather_csharp_code(
        directory=args.directory,
        output_file=args.output,
        additional_extensions=args.extensions,
        extra_ignore_dirs=args.ignore
    )
    
    print(f"完成！源代码已保存到 {args.output}")