/**
 * 性能监控初始化
 * 文件路径: src/utils/initPerformanceMonitoring.js
 * 功能描述: 初始化性能监控，设置全局监控
 */

import { startPerformanceMonitoring, getPerformanceReport } from '@/utils/performanceMonitor'
import { printApiStats } from '@/utils/apiRequestManager'

// 性能监控配置
const PERFORMANCE_CONFIG = {
  enableMonitoring: true,
  reportInterval: 5 * 60 * 1000, // 5分钟报告一次
  enableConsoleReports: true,
  enableAlerts: true
}

// 初始化性能监控
export function initPerformanceMonitoring() {
  if (!PERFORMANCE_CONFIG.enableMonitoring) {
    console.log('🔍 性能监控已禁用')
    return
  }

  console.log('🚀 初始化性能监控系统...')
  
  // 启动性能监控
  startPerformanceMonitoring()
  
  // 设置定期报告
  if (PERFORMANCE_CONFIG.enableConsoleReports) {
    setInterval(() => {
      console.log('\n📊 ===== 性能监控报告 =====')
      
      // API请求管理器统计
      console.log('🔄 API请求缓存统计:')
      printApiStats()
      
      // 性能监控统计
      console.log('⚡ 性能监控统计:')
      const report = getPerformanceReport()
      console.log('总体统计:', report.summary)
      
      if (report.apiDetails.length > 0) {
        console.log('热门API端点:')
        report.apiDetails.slice(0, 5).forEach((api, index) => {
          console.log(`${index + 1}. ${api.endpoint} - 调用${api.calls}次 - 平均${api.avgResponseTime}`)
        })
      }
      
      if (report.recentAlerts.length > 0) {
        console.log('⚠️ 最近告警:')
        report.recentAlerts.forEach(alert => {
          console.log(`- ${alert.message} (${new Date(alert.timestamp).toLocaleTimeString()})`)
        })
      }
      
      console.log('===== 性能监控报告结束 =====\n')
    }, PERFORMANCE_CONFIG.reportInterval)
  }
  
  // 添加全局错误处理
  window.addEventListener('error', (event) => {
    console.error('🚨 全局错误:', {
      message: event.message,
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno,
      error: event.error
    })
  })
  
  // 添加未处理的Promise拒绝处理
  window.addEventListener('unhandledrejection', (event) => {
    console.error('🚨 未处理的Promise拒绝:', {
      reason: event.reason,
      promise: event.promise
    })
  })
  
  // 页面可见性变化时的处理
  document.addEventListener('visibilitychange', () => {
    if (document.hidden) {
      console.log('📱 页面隐藏，暂停性能监控')
    } else {
      console.log('📱 页面显示，恢复性能监控')
    }
  })
  
  console.log('✅ 性能监控系统初始化完成')
}

// 获取性能摘要（供开发者工具使用）
export function getPerformanceSummary() {
  const report = getPerformanceReport()
  return {
    summary: report.summary,
    topAPIs: report.apiDetails.slice(0, 10),
    recentAlerts: report.recentAlerts.slice(0, 5),
    recentErrors: report.recentErrors.slice(0, 5)
  }
}

// 手动触发性能报告
export function triggerPerformanceReport() {
  console.log('\n📊 ===== 手动性能报告 =====')
  const summary = getPerformanceSummary()
  console.log('性能摘要:', summary)
  console.log('===== 手动性能报告结束 =====\n')
  return summary
}

// 清理性能数据
export function clearPerformanceData() {
  // 这里可以添加清理逻辑
  console.log('🧹 性能数据已清理')
}

// 导出到全局对象（方便调试）
if (typeof window !== 'undefined') {
  window.performanceUtils = {
    getSummary: getPerformanceSummary,
    triggerReport: triggerPerformanceReport,
    clearData: clearPerformanceData
  }
  
  console.log('🔧 性能工具已添加到 window.performanceUtils')
}

export default {
  init: initPerformanceMonitoring,
  getSummary: getPerformanceSummary,
  triggerReport: triggerPerformanceReport,
  clearData: clearPerformanceData
}
