// File: frontend/src/components/QuickMemo/QuickMemoList.vue
// Description: Component to display a list of Quick Memos.

<template>
  <div class="quick-memo-list-desktop thin-scrollbar" v-loading="isLoadingMemos">
    <div v-if="quickMemoStore.quickMemos.length > 0" class="memo-grid">
      <div
        v-for="memo in quickMemoStore.quickMemos"
        :key="memo.id"
        class="memo-note-card"
        :style="{ backgroundColor: getCategoryColor(memo.categoryId, 0.15), borderColor: getCategoryColor(memo.categoryId) }"
        @click="viewMemo(memo)"
      >
        <div class="memo-note-header">
          <el-tag size="small" :color="getCategoryColor(memo.categoryId)" effect="dark" style="color: white; border: none;">
            {{ quickMemoStore.getCategoryName(memo.categoryId) }}
          </el-tag>
          <span class="memo-note-time">{{ formatTimeAgo(memo.createdAt) }}</span>
        </div>
        <div class="memo-note-title" :title="memo.title">{{ memo.title }}</div>
        <!-- 你可以考虑在这里显示部分 content，如果需要 -->
        <!-- <div class="memo-note-content" v-if="memo.content">{{ truncateText(memo.content, 50) }}</div> -->
      </div>
    </div>
    <el-empty v-else description="暂无随手记" />
  </div>
</template>

<script setup>
import { computed, onMounted } from 'vue'
import { useQuickMemoStore } from '@/stores/modules/quickMemo'
import { formatDistanceToNow } from 'date-fns';
import { zhCN } from 'date-fns/locale';

const quickMemoStore = useQuickMemoStore()

const isLoadingMemos = computed(() => quickMemoStore.isLoadingQuickMemos)

const viewMemo = (memo) => {
  if (memo && memo.id) {
    quickMemoStore.openMemoDrawer('edit', memo)
  }
}

const formatTimeAgo = (timestamp) => {
    if (!timestamp) return '';
    try {
        return formatDistanceToNow(new Date(timestamp), { addSuffix: true, locale: zhCN });
    } catch (e) { 
      console.warn('Invalid date for formatTimeAgo:', timestamp, e); 
      return '无效日期'; 
    }
};

const getCategoryColor = (categoryId, opacity = 1) => {
  const category = quickMemoStore.getCategoryById(categoryId);
  // 假设 category.color 存储的是类似 '#RRGGBB' 的十六进制颜色或者一个预定义的颜色名
  // 如果是颜色名，你需要一个映射表将其转换为实际颜色值
  // 此处使用一个简单的预定义颜色映射
  const colorMap = {
    'work': `rgba(59, 130, 246, ${opacity})`,   // Blue (系统蓝色)
    'personal': `rgba(245, 158, 11, ${opacity})`, // Amber (系统琥珀色)
    'study': `rgba(16, 185, 129, ${opacity})`,  // Emerald (系统翡翠绿)
    'default': `rgba(108, 117, 125, ${opacity})`,// Gray (系统灰色)
    // 你可以根据 store 中 categories 的实际 color 值来动态生成，或者扩展这个 map
  };
  const idOrName = category?.id?.toLowerCase() || category?.name?.toLowerCase() || 'default';
  // 优先使用 category.color 字段（如果它是有效的CSS颜色）
  if (category && category.color && CSS.supports('color', category.color)) {
    // 如果 color 是 #rrggbb 格式，需要转换为 rgba
    if (/^#([0-9A-F]{3}){1,2}$/i.test(category.color)) {
      let r = 0, g = 0, b = 0;
      if (category.color.length === 4) { // #RGB
        r = parseInt(category.color[1] + category.color[1], 16);
        g = parseInt(category.color[2] + category.color[2], 16);
        b = parseInt(category.color[3] + category.color[3], 16);
      } else if (category.color.length === 7) { // #RRGGBB
        r = parseInt(category.color.substring(1, 3), 16);
        g = parseInt(category.color.substring(3, 5), 16);
        b = parseInt(category.color.substring(5, 7), 16);
      }
      return `rgba(${r}, ${g}, ${b}, ${opacity})`;
    }    
    // 如果 category.color 本身就是类似 'blue' 或 'rgba(...)' 的有效颜色
    // 为了确保能应用opacity，最好还是解析它或者有一个映射表
    // 为简化，如果不是#开头的，我们尝试从map取，否则用default
    return colorMap[category.color.toLowerCase()] || colorMap['default'];
  }
  return colorMap[idOrName] || colorMap['default'];
};

// 可选：截断文本的辅助函数
// const truncateText = (text, maxLength) => {
//   if (!text) return '';
//   return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
// };

onMounted(() => {
  if (quickMemoStore.quickMemos.length === 0) {
    quickMemoStore.fetchQuickMemos()
  }
  if (quickMemoStore.categories.length === 0) {
    quickMemoStore.fetchCategories();
  }
});
</script>

<style scoped>
.quick-memo-list-desktop {
  /* max-height: 380px; */ /* 根据父容器调整或移除，让grid自适应 */
  height: 380px; /* 固定高度以展示滚动条 */
  overflow-y: auto;
  padding: 10px; 
  background-color: var(--el-bg-color-page); /* 使用页面背景色或浅灰色 */
  border-radius: var(--el-border-radius-base);
}
.memo-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr)); /* 调整最小宽度 */
  gap: 15px; /* 调整间距 */
}
.memo-note-card {
  padding: 12px;
  border-radius: var(--el-border-radius-base);
  box-shadow: var(--el-box-shadow-light); /* Element Plus 阴影变量 */
  cursor: pointer;
  transition: transform 0.2s ease-out, box-shadow 0.2s ease-out;
  border-left: 5px solid; /* 边框颜色将通过 :style 设置 */
  min-height: 120px; /* 调整最小高度 */
  display: flex;
  flex-direction: column;
  background-color: var(--el-bg-color); /* 卡片背景色 */
}
.memo-note-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--el-box-shadow); /* Element Plus 悬浮阴影 */
}
.memo-note-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}
.memo-note-time {
  font-size: 0.75rem; /* 调整字体大小 */
  color: var(--el-text-color-secondary);
}
.memo-note-title {
  font-size: 0.95rem; /* 调整字体大小 */
  font-weight: 500;
  color: var(--el-text-color-primary);
  line-height: 1.4;
  word-break: break-word;
  white-space: normal; /* 允许多行 */
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3; /* 最多显示3行标题 */
  -webkit-box-orient: vertical;
  flex-grow: 1; 
}
/* 旧的 .memo-list-item, .memo-title, .el-tag 全局样式已移除 */

/* thin-scrollbar 样式保持不变 */
.thin-scrollbar::-webkit-scrollbar {
  width: 6px;
}
.thin-scrollbar::-webkit-scrollbar-track {
  background: transparent; 
}
.thin-scrollbar::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 3px;
}
.thin-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}
</style> 