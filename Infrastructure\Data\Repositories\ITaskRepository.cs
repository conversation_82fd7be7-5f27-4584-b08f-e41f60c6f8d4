using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using ItAssetsSystem.Domain.Entities.Tasks;

namespace Infrastructure.Data.Repositories
{
    /// <summary>
    /// 任务仓库接口
    /// </summary>
    public interface ITaskRepository
    {
        /// <summary>
        /// 获取任务的所有负责人/参与者
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <returns>任务负责人/参与者列表</returns>
        Task<List<TaskAssignee>> GetAssigneesByTaskIdAsync(long taskId);

        /// <summary>
        /// 添加任务负责人/参与者
        /// </summary>
        /// <param name="assignee">任务负责人/参与者</param>
        /// <returns>操作是否成功</returns>
        Task<bool> AddAssigneeAsync(TaskAssignee assignee);

        /// <summary>
        /// 移除任务负责人/参与者
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <param name="userId">用户ID</param>
        /// <param name="assignmentType">分配类型 (Assignee/Participant)</param>
        /// <returns>操作是否成功</returns>
        Task<bool> RemoveAssigneeAsync(long taskId, int userId, string assignmentType);
    }
} 