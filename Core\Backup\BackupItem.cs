// IT资产管理系统 - 备份项
// 文件路径: /Core/Backup/BackupItem.cs
// 功能: 定义备份项信息

using System;

namespace ItAssetsSystem.Core.Backup
{
    /// <summary>
    /// 备份类型
    /// </summary>
    public enum BackupType
    {
        /// <summary>
        /// 手动备份
        /// </summary>
        Manual,
        
        /// <summary>
        /// 自动备份
        /// </summary>
        Automatic,
        
        /// <summary>
        /// 系统备份
        /// </summary>
        System
    }
    
    /// <summary>
    /// 备份项
    /// </summary>
    public class BackupItem
    {
        /// <summary>
        /// 备份ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString("N");
        
        /// <summary>
        /// 备份名称
        /// </summary>
        public string Name { get; set; }
        
        /// <summary>
        /// 备份描述
        /// </summary>
        public string Description { get; set; }
        
        /// <summary>
        /// 备份类型
        /// </summary>
        public BackupType Type { get; set; }
        
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        
        /// <summary>
        /// 文件大小（字节）
        /// </summary>
        public long FileSize { get; set; }
        
        /// <summary>
        /// 数据库文件名
        /// </summary>
        public string DatabaseFileName { get; set; }
        
        /// <summary>
        /// 配置文件名
        /// </summary>
        public string ConfigFileName { get; set; }
    }
} 