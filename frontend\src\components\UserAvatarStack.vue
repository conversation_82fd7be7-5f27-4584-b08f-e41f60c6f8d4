<template>
  <div class="user-avatar-stack" :class="{ 'clickable': showPopover }">
    <div class="avatar-wrapper">
      <!-- 主要负责人头像 -->
      <el-avatar 
        v-if="mainUser && isMainUserPrimary" 
        :src="mainUser.avatarUrl" 
        :size="avatarSize"
        class="avatar primary"
      >
        {{ mainUser.name ? mainUser.name.charAt(0).toUpperCase() : '?' }}
      </el-avatar>
      
      <!-- 其他用户头像 -->
      <el-avatar 
        v-for="(user, index) in visibleUsers" 
        :key="`user-${user.id || user.userId || index}`"
        :src="user.avatarUrl" 
        :size="avatarSize"
        class="avatar"
        :style="{ 
          zIndex: visibleUsers.length - index,
          marginLeft: index > 0 ? `-${overlap}px` : '0'
        }"
      >
        {{ user.name ? user.name.charAt(0).toUpperCase() : '?' }}
      </el-avatar>
      
      <!-- 显示额外用户数量 -->
      <el-avatar 
        v-if="extraCount > 0"
        :size="avatarSize"
        class="avatar extra-count"
        :style="{ 
          zIndex: 0,
          marginLeft: `-${overlap}px`
        }"
      >
        +{{ extraCount }}
      </el-avatar>
    </div>
    
    <!-- 显示详细信息 -->
    <div v-if="showDetails && users.length > 0" class="avatar-details">
      <div class="user-info" v-if="mainUser && isMainUserPrimary">
        <span class="user-name">{{ mainUser.name }}</span>
        <span class="user-role">(负责人)</span>
      </div>
      <div v-if="otherUsers.length > 0" class="other-users">
        <span>{{ formatOtherUsers }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import { computed, onMounted, watch } from 'vue'
import UserAvatar from './UserAvatar.vue'

export default {
  name: 'UserAvatarStack',
  components: { UserAvatar },
  props: {
    users: {
      type: Array,
      default: () => []
    },
    maxUsers: {
      type: Number,
      default: 3
    },
    size: {
      type: String,
      default: 'default'
    },
    isMainUserPrimary: {
      type: Boolean,
      default: false
    },
    showNames: {
      type: Boolean,
      default: true
    },
    showPopover: {
      type: Boolean,
      default: false
    },
    showDetails: {
      type: Boolean,
      default: false
    },
    overlap: {
      type: Number,
      default: 8
    },
    avatarSize: {
      type: [String, Number],
      default: 24
    }
  },
  setup(props) {
    const mainUser = computed(() => {
      if (!props.isMainUserPrimary || !props.users.length) return null;
      return props.users.find(user => user.isPrimary === true || user.role === 'Primary');
    });
    
    const otherUsers = computed(() => {
      if (!props.users.length) return [];
      return props.users.filter(user => user.isPrimary !== true && user.role !== 'Primary');
    });
    
    const maxVisibleAvatars = computed(() => {
      return props.maxUsers;
    });
    
    const visibleUsers = computed(() => {
      let result = [];
      
      // 如果有主用户并且需要将其作为主要用户显示，则跳过主用户
      if (mainUser.value && props.isMainUserPrimary) {
        result = otherUsers.value.slice(0, maxVisibleAvatars.value - 1);
      } else {
        // 否则，显示前N个用户
        result = props.users.slice(0, maxVisibleAvatars.value);
      }
      
      // 确保每个用户对象都有avatarUrl属性
      return result.map(user => ({
        ...user,
        avatarUrl: user.avatarUrl || ''  // 确保avatarUrl属性存在
      }));
    });
    
    const hiddenCount = computed(() => {
      return Math.max(0, props.users.length - props.maxUsers);
    });
    
    const extraCount = computed(() => {
      return hiddenCount.value;
    });
    
    const getUserName = (user) => {
      if (!user) return '未知用户';
      return user.name || user.userName || user.username || '未知用户';
    };
    
    const formatOtherUsers = computed(() => {
      return otherUsers.value.map(user => user.name).join(', ');
    });
    
    // 添加用于调试的日志函数
    const logUserInfo = () => {
      if (props.users && props.users.length > 0) {
        console.log('UserAvatarStack接收到的用户数据:', props.users);
        props.users.forEach((user, index) => {
          console.log(`用户${index+1}:`, {
            id: user.id || user.userId,
            name: getUserName(user),
            avatarUrl: user.avatarUrl || user.avatar || (user.user?.avatar) || ''
          });
        });
      } else {
        console.warn('UserAvatarStack没有接收到任何用户数据');
      }
    };
    
    // 在组件初始化时执行日志
    onMounted(() => {
      logUserInfo();
    });
    
    // 当用户数据变化时再次记录日志
    watch(() => props.users, () => {
      logUserInfo();
    }, { deep: true });
    
    return {
      mainUser,
      otherUsers,
      visibleUsers,
      hiddenCount,
      extraCount,
      getUserName,
      formatOtherUsers
    }
  }
}
</script>

<style scoped>
.user-avatar-stack {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.avatar-wrapper {
  display: flex;
  align-items: center;
  position: relative;
}

.avatar {
  border: 2px solid #fff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  position: relative;
}

.avatar.primary {
  border-color: #409eff;
  z-index: 100;
}

.avatar.extra-count {
  background-color: #f0f0f0;
  color: #666;
  font-size: 12px;
  font-weight: 500;
}

/* 小头像样式（任务列表用） */
.user-avatar-stack.small .avatar {
  border: 1px solid #fff;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.08);
  margin-right: -8px; /* GitHub风格的负边距 */
}

.user-avatar-stack.small .avatar.extra-count {
  font-size: 10px;
  margin-right: 0;
}

/* 添加极小头像样式 */
.user-avatar-stack.small .avatar[style*="size: 8"] {
  border: 1px solid #fff;
  box-shadow: 0 0.5px 1px rgba(0, 0, 0, 0.05);
  font-size: 6px;
}

.user-avatar-stack.small .avatar[style*="size: 6"] {
  border: 1px solid #fff;
  box-shadow: none;
  font-size: 5px;
}

.more-count {
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f2f6fc;
  border-radius: 50%;
  min-width: 24px;
  height: 24px;
  font-size: 12px;
  color: #606266;
  margin-left: -8px;
  padding: 0 4px;
}

.users-list {
  max-height: 200px;
  overflow-y: auto;
}

.user-item {
  display: flex;
  align-items: center;
  padding: 6px 0;
}

.user-name {
  margin: 0 8px;
  flex: 1;
}

.role-tag {
  font-size: 10px;
  height: 18px;
  line-height: 16px;
}

.avatar-names {
  margin-top: 4px;
  font-size: 13px;
  display: flex;
  align-items: center;
  cursor: default;
}

.primary-name {
  font-weight: 500;
}

.secondary-names {
  color: #909399;
  margin-left: 4px;
  font-size: 12px;
}
</style> 