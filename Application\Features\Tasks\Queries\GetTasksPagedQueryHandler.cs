using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using ItAssetsSystem.Application.Common.Dtos;
using ItAssetsSystem.Application.Features.Tasks.Dtos;
using ItAssetsSystem.Infrastructure.Data;
using System.Linq.Expressions;

namespace ItAssetsSystem.Application.Features.Tasks.Queries
{
    /// <summary>
    /// 获取任务分页列表查询处理器
    /// </summary>
    public class GetTasksPagedQueryHandler : IRequestHandler<GetTasksPagedQuery, PaginatedResult<TaskDto>>
    {
        private readonly AppDbContext _context;
        private readonly ILogger<GetTasksPagedQueryHandler> _logger;

        public GetTasksPagedQueryHandler(AppDbContext context, ILogger<GetTasksPagedQueryHandler> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<PaginatedResult<TaskDto>> Handle(GetTasksPagedQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("开始处理任务分页查询: Page={Page}, Size={Size}", request.Page, request.Size);

                // 性能优化：使用AsNoTracking避免实体跟踪，包含必要的关联数据
                IQueryable<Domain.Entities.Tasks.Task> query = _context.Tasks.AsNoTracking()
                    .Include(t => t.Assignee)
                    .Include(t => t.Creator)
                    .Include(t => t.Asset)
                    .Include(t => t.Location)
                    .Include(t => t.ParentTask)
                    .Include(t => t.Category); // 包含分类信息

                // 应用筛选条件
                query = ApplyFilters(query, request);

                // 计算总数
                var totalCount = await query.CountAsync(cancellationToken);

                // 应用排序
                query = ApplySorting(query, request.SortBy, request.SortDirection);

                // 应用分页并使用Select投影优化性能
                var tasks = await query
                    .Skip((request.Page - 1) * request.Size)
                    .Take(request.Size)
                    .Select(t => new TaskDto
                    {
                        TaskId = t.TaskId,
                        Name = t.Name,
                        Description = t.Description ?? "",
                        Status = t.Status,
                        StatusName = GetStatusDisplayName(t.Status),
                        Priority = t.Priority ?? "Medium",
                        PriorityText = GetPriorityDisplayName(t.Priority ?? "Medium"),
                        TaskType = t.TaskType,
                        Progress = t.Progress,
                        Points = t.Points,
                        CreationTimestamp = t.CreationTimestamp,
                        PlanStartDate = t.PlanStartDate,
                        PlanEndDate = t.PlanEndDate,
                        ActualStartDate = t.ActualStartDate,
                        ActualEndDate = t.ActualEndDate,
                        LastUpdatedTimestamp = t.LastUpdatedTimestamp,

                        // 关联数据优化查询
                        AssigneeUserId = t.AssigneeUserId,
                        AssigneeUserName = t.Assignee != null ? t.Assignee.Name : "",
                        CreatorUserId = t.CreatorUserId,
                        CreatorUserName = t.Creator != null ? t.Creator.Name : "",
                        AssetId = t.AssetId,
                        AssetName = t.Asset != null ? t.Asset.Name : "",
                        LocationId = t.LocationId,
                        LocationName = t.Location != null ? t.Location.Name : "",
                        ParentTaskId = t.ParentTaskId,
                        ParentTaskName = t.ParentTask != null ? t.ParentTask.Name : "",
                        ProjectId = t.ProjectId,
                        PeriodicTaskScheduleId = t.PeriodicTaskScheduleId,
                        PDCAStage = t.PDCAStage ?? "",

                        // 任务分类信息 - 使用显式查询避免Include问题
                        CategoryId = t.CategoryId,
                        CategoryName = t.CategoryId.HasValue ?
                            _context.TaskCategories.Where(tc => tc.CategoryId == t.CategoryId).Select(tc => tc.Name).FirstOrDefault() ?? "" : "",
                        CategoryColor = t.CategoryId.HasValue ?
                            _context.TaskCategories.Where(tc => tc.CategoryId == t.CategoryId).Select(tc => tc.Color).FirstOrDefault() : null,
                        CategoryIcon = t.CategoryId.HasValue ?
                            _context.TaskCategories.Where(tc => tc.CategoryId == t.CategoryId).Select(tc => tc.Icon).FirstOrDefault() : null,

                        // 计算字段
                        IsOverdue = t.PlanEndDate.HasValue && t.PlanEndDate.Value < DateTime.UtcNow && t.Status != "Done",

                        // 完成水印相关
                        CompletedByUserId = t.CompletedByUserId
                    })
                    .ToListAsync(cancellationToken);

                _logger.LogInformation("任务分页查询完成: 返回{Count}条记录，总计{Total}条", tasks.Count, totalCount);

                return PaginatedResult<TaskDto>.Create(
                    tasks,
                    totalCount,
                    request.Page,
                    request.Size
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理任务分页查询时发生错误");
                throw;
            }
        }

        /// <summary>
        /// 应用筛选条件
        /// </summary>
        private IQueryable<Domain.Entities.Tasks.Task> ApplyFilters(
            IQueryable<Domain.Entities.Tasks.Task> query, 
            GetTasksPagedQuery request)
        {
            // 基础筛选
            if (!request.IncludeDeleted)
            {
                query = query.Where(t => !t.IsDeleted);
            }

            // 关键词搜索
            if (!string.IsNullOrEmpty(request.Keyword))
            {
                var keyword = request.Keyword.Trim().ToLower();
                query = query.Where(t =>
                    t.Name.ToLower().Contains(keyword) ||
                    (t.Description != null && t.Description.ToLower().Contains(keyword)) ||
                    (t.Asset != null && t.Asset.Name.ToLower().Contains(keyword))
                );
            }

            // 状态筛选
            if (!string.IsNullOrEmpty(request.Status))
            {
                query = query.Where(t => t.Status == request.Status);
            }

            // 优先级筛选
            if (!string.IsNullOrEmpty(request.Priority))
            {
                query = query.Where(t => t.Priority == request.Priority);
            }

            // 任务类型筛选
            if (!string.IsNullOrEmpty(request.TaskType))
            {
                query = query.Where(t => t.TaskType == request.TaskType);
            }

            // 负责人筛选
            if (request.AssigneeId.HasValue)
            {
                query = query.Where(t => t.AssigneeUserId == request.AssigneeId.Value);
            }

            // 创建人筛选
            if (request.CreatedBy.HasValue)
            {
                query = query.Where(t => t.CreatorUserId == request.CreatedBy.Value);
            }

            // 资产筛选
            if (request.AssetId.HasValue)
            {
                query = query.Where(t => t.AssetId == request.AssetId.Value);
            }

            // 位置筛选
            if (request.LocationId.HasValue)
            {
                query = query.Where(t => t.LocationId == request.LocationId.Value);
            }

            // 父任务筛选
            if (request.ParentTaskId.HasValue)
            {
                query = query.Where(t => t.ParentTaskId == request.ParentTaskId.Value);
            }

            // 项目筛选
            if (request.ProjectId.HasValue)
            {
                query = query.Where(t => t.ProjectId == request.ProjectId.Value);
            }

            // 日期范围筛选
            if (request.StartDate.HasValue)
            {
                query = query.Where(t => t.CreationTimestamp >= request.StartDate.Value);
            }

            if (request.EndDate.HasValue)
            {
                var endDate = request.EndDate.Value.Date.AddDays(1); // 包含整天
                query = query.Where(t => t.CreationTimestamp < endDate);
            }

            // 包含必要的关联数据
            query = query
                .Include(t => t.Assignee)
                .Include(t => t.Creator)
                .Include(t => t.Asset)
                .Include(t => t.Location)
                .Include(t => t.ParentTask);

            return query;
        }

        /// <summary>
        /// 应用排序
        /// </summary>
        private IQueryable<Domain.Entities.Tasks.Task> ApplySorting(
            IQueryable<Domain.Entities.Tasks.Task> query, 
            string? sortBy, 
            string? sortDirection)
        {
            var isDescending = string.Equals(sortDirection, "desc", StringComparison.OrdinalIgnoreCase);

            return sortBy?.ToLower() switch
            {
                "name" => isDescending ? query.OrderByDescending(t => t.Name) : query.OrderBy(t => t.Name),
                "status" => isDescending ? query.OrderByDescending(t => t.Status) : query.OrderBy(t => t.Status),
                "priority" => isDescending ? query.OrderByDescending(t => t.Priority) : query.OrderBy(t => t.Priority),
                "duedate" => isDescending ? query.OrderByDescending(t => t.PlanEndDate) : query.OrderBy(t => t.PlanEndDate),
                "progress" => isDescending ? query.OrderByDescending(t => t.Progress) : query.OrderBy(t => t.Progress),
                "assignee" => isDescending ? query.OrderByDescending(t => t.Assignee.Name) : query.OrderBy(t => t.Assignee.Name),
                _ => isDescending ? query.OrderByDescending(t => t.CreationTimestamp) : query.OrderBy(t => t.CreationTimestamp)
            };
        }

        /// <summary>
        /// 获取状态显示名称
        /// </summary>
        private static string GetStatusDisplayName(string status)
        {
            return status switch
            {
                "Todo" => "待处理",
                "InProgress" => "进行中",
                "Done" => "已完成",
                "Cancelled" => "已取消",
                "OnHold" => "暂停",
                _ => status
            };
        }

        /// <summary>
        /// 获取优先级显示名称
        /// </summary>
        private static string GetPriorityDisplayName(string priority)
        {
            return priority switch
            {
                "Low" => "低",
                "Medium" => "中",
                "High" => "高",
                "Critical" => "紧急",
                _ => priority
            };
        }
    }
}
