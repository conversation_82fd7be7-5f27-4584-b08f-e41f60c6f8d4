// IT资产管理系统 - 位置树状结构API端点
// 文件路径: /Api/Location/LocationTreeEndpoint.cs
// 功能: 提供位置树状结构的API端点，用于前端树形展示

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ItAssetsSystem.Infrastructure.Data;
using ItAssetsSystem.Models.Entities;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace ItAssetsSystem.Api.Location
{
    /// <summary>
    /// 位置树结构DTO
    /// </summary>
    public class LocationTreeNode
    {
        /// <summary>
        /// 位置ID
        /// </summary>
        public int Id { get; set; }
        
        /// <summary>
        /// 位置代码
        /// </summary>
        public string Code { get; set; }
        
        /// <summary>
        /// 位置名称
        /// </summary>
        public string Name { get; set; }
        
        /// <summary>
        /// 位置类型
        /// </summary>
        public int Type { get; set; }
        
        /// <summary>
        /// 上级位置ID
        /// </summary>
        public int? ParentId { get; set; }
        
        /// <summary>
        /// 路径
        /// </summary>
        public string Path { get; set; }
        
        /// <summary>
        /// 描述
        /// </summary>
        public string Description { get; set; }
        
        /// <summary>
        /// 是否激活
        /// </summary>
        public bool IsActive { get; set; }
        
        /// <summary>
        /// 子节点
        /// </summary>
        public List<LocationTreeNode> Children { get; set; } = new List<LocationTreeNode>();
    }
    
    /// <summary>
    /// 位置树状结构API端点
    /// </summary>
    [ApiController]
    [Route("api/location-tree")]
    public class LocationTreeEndpoint
    {
        private readonly AppDbContext _dbContext;
        private readonly ILogger<LocationTreeEndpoint> _logger;
        
        /// <summary>
        /// 构造函数
        /// </summary>
        public LocationTreeEndpoint(
            AppDbContext dbContext,
            ILogger<LocationTreeEndpoint> logger)
        {
            _dbContext = dbContext;
            _logger = logger;
        }
        
        /// <summary>
        /// 获取完整位置树
        /// </summary>
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> GetLocationTreeAsync()
        {
            _logger.LogInformation("获取完整位置树");
            
            try
            {
                // 获取所有位置，按ID排序
                var allLocations = await _dbContext.Locations
                    .OrderBy(l => l.Id)
                    .ToListAsync();
                
                // 构建树结构
                var rootNodes = BuildLocationTree(allLocations);
                
                return new OkObjectResult(rootNodes);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取位置树失败");
                return new StatusCodeResult(StatusCodes.Status500InternalServerError);
            }
        }
        
        /// <summary>
        /// 获取特定位置节点的子节点
        /// </summary>
        [HttpGet("{locationId}/children")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetLocationChildrenAsync(int locationId)
        {
            _logger.LogInformation("获取位置ID {LocationId} 的子节点", locationId);
            
            try
            {
                // 检查位置是否存在
                var location = await _dbContext.Locations.FindAsync(locationId);
                if (location == null)
                {
                    return new NotFoundResult();
                }
                
                // 获取子节点
                var children = await _dbContext.Locations
                    .Where(l => l.ParentId == locationId)
                    .OrderBy(l => l.Code)
                    .ToListAsync();
                
                // 转换为树节点
                var childNodes = children.Select(c => ConvertToTreeNode(c, null)).ToList();
                
                return new OkObjectResult(childNodes);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取位置子节点失败");
                return new StatusCodeResult(StatusCodes.Status500InternalServerError);
            }
        }
        
        /// <summary>
        /// 构建位置树
        /// </summary>
        private List<LocationTreeNode> BuildLocationTree(List<Models.Entities.Location> allLocations)
        {
            // 获取根节点
            var rootLocations = allLocations.Where(l => l.ParentId == null).ToList();
            
            // 构建树结构
            var result = new List<LocationTreeNode>();
            foreach (var rootLocation in rootLocations)
            {
                result.Add(ConvertToTreeNode(rootLocation, allLocations));
            }
            
            return result;
        }
        
        /// <summary>
        /// 转换为树节点
        /// </summary>
        private LocationTreeNode ConvertToTreeNode(Models.Entities.Location location, List<Models.Entities.Location> allLocations)
        {
            var node = new LocationTreeNode
            {
                Id = location.Id,
                Code = location.Code,
                Name = location.Name,
                Type = location.Type,
                ParentId = location.ParentId,
                Path = location.Path,
                Description = location.Description,
                IsActive = location.IsActive
            };
            
            // 如果提供了所有位置列表，则递归构建子节点
            if (allLocations != null)
            {
                var children = allLocations
                    .Where(l => l.ParentId == location.Id)
                    .ToList();
                
                foreach (var child in children)
                {
                    node.Children.Add(ConvertToTreeNode(child, allLocations));
                }
            }
            
            return node;
        }
    }
} 