// =====================================================
// 现代化游戏化系统 - Startup配置
// 在现有Startup.cs中添加以下配置
// =====================================================

using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.SignalR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.StackExchangeRedis;
using ItAssetsSystem.Application.Features.Gamification.Services;
using ItAssetsSystem.Application.Features.Gamification.Hubs;
using ItAssetsSystem.Infrastructure.Data;

namespace ItAssetsSystem
{
    public partial class Startup
    {
        // 在ConfigureServices方法中添加以下配置
        public void ConfigureModernGamificationServices(IServiceCollection services)
        {
            // 1. 添加SignalR支持
            services.AddSignalR(options =>
            {
                options.EnableDetailedErrors = true;
                options.KeepAliveInterval = TimeSpan.FromSeconds(15);
                options.ClientTimeoutInterval = TimeSpan.FromSeconds(30);
            });

            // 2. 添加缓存支持
            services.AddMemoryCache(options =>
            {
                options.SizeLimit = 1000; // 限制缓存项数量
            });

            // 3. 添加Redis分布式缓存（如果有Redis）
            if (!string.IsNullOrEmpty(Configuration.GetConnectionString("Redis")))
            {
                services.AddStackExchangeRedisCache(options =>
                {
                    options.Configuration = Configuration.GetConnectionString("Redis");
                    options.InstanceName = "ItAssetsSystem";
                });
            }
            else
            {
                // 如果没有Redis，使用内存分布式缓存
                services.AddDistributedMemoryCache();
            }

            // 4. 注册现代化游戏化服务
            services.AddScoped<IModernGamificationService, ModernGamificationService>();

            // 5. 保留现有的游戏化服务（向后兼容）
            services.AddScoped<Services.Interfaces.IGamificationService, Services.GamificationService>();
            services.AddScoped<Services.Interfaces.IUniversalGamificationService, Services.UniversalGamificationService>();
            services.AddScoped<Services.Interfaces.ILeaderboardService, Services.LeaderboardService>();
            services.AddScoped<Services.Interfaces.IAchievementService, Services.AchievementService>();

            // 6. 注册新增的等级和道具服务
            services.AddScoped<Application.Features.Gamification.Services.ILevelService, Application.Features.Gamification.Services.LevelService>();
            services.AddScoped<Application.Features.Gamification.Services.IItemService, Application.Features.Gamification.Services.ItemService>();

            // 7. 添加后台服务（可选）
            services.AddHostedService<StatisticsUpdateBackgroundService>();

            // 8. 添加HTTP客户端（如果需要外部API调用）
            services.AddHttpClient();

            // 9. 配置CORS（如果需要）
            services.AddCors(options =>
            {
                options.AddPolicy("GamificationPolicy", builder =>
                {
                    builder
                        .WithOrigins("http://localhost:8080", "https://localhost:8080") // Vue开发服务器
                        .AllowAnyMethod()
                        .AllowAnyHeader()
                        .AllowCredentials(); // SignalR需要
                });
            });
        }

        // 在Configure方法中添加以下配置
        public void ConfigureModernGamificationPipeline(IApplicationBuilder app, IWebHostEnvironment env)
        {
            // 1. 使用CORS
            app.UseCors("GamificationPolicy");

            // 2. 配置SignalR Hub
            app.UseRouting();
            app.UseAuthentication();
            app.UseAuthorization();

            app.UseEndpoints(endpoints =>
            {
                // 添加SignalR Hub路由
                endpoints.MapHub<GamificationHub>("/hubs/gamification");
                
                // 保留现有的控制器路由
                endpoints.MapControllers();
            });
        }
    }

    // =====================================================
    // 后台服务：定时更新统计数据
    // =====================================================
    public class StatisticsUpdateBackgroundService : BackgroundService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<StatisticsUpdateBackgroundService> _logger;

        public StatisticsUpdateBackgroundService(
            IServiceProvider serviceProvider,
            ILogger<StatisticsUpdateBackgroundService> logger)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("统计更新后台服务启动");

            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    using var scope = _serviceProvider.CreateScope();
                    var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();
                    var hubContext = scope.ServiceProvider.GetRequiredService<IHubContext<GamificationHub>>();

                    var now = DateTime.Now;

                    // 每日凌晨2点更新昨日统计
                    if (now.Hour == 2 && now.Minute < 5)
                    {
                        await UpdateDailyStatistics(context);
                        _logger.LogInformation("每日统计更新完成");
                    }

                    // 每周一凌晨3点更新上周统计
                    if (now.DayOfWeek == DayOfWeek.Monday && now.Hour == 3 && now.Minute < 5)
                    {
                        await UpdateWeeklyStatistics(context);
                        _logger.LogInformation("每周统计更新完成");
                    }

                    // 每月1日凌晨4点更新上月统计
                    if (now.Day == 1 && now.Hour == 4 && now.Minute < 5)
                    {
                        await UpdateMonthlyStatistics(context);
                        _logger.LogInformation("每月统计更新完成");
                    }

                    // 每5分钟推送实时排行榜更新
                    if (now.Minute % 5 == 0 && now.Second < 30)
                    {
                        await PushRealtimeUpdates(scope.ServiceProvider, hubContext);
                    }

                    // 每分钟检查一次
                    await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "后台统计更新失败");
                    await Task.Delay(TimeSpan.FromMinutes(5), stoppingToken);
                }
            }
        }

        private async Task UpdateDailyStatistics(AppDbContext context)
        {
            var yesterday = DateTime.Today.AddDays(-1);
            await UpdateWorkSummaryForPeriod(context, "daily", yesterday);
        }

        private async Task UpdateWeeklyStatistics(AppDbContext context)
        {
            var lastWeek = DateTime.Today.AddDays(-7);
            await UpdateWorkSummaryForPeriod(context, "weekly", lastWeek);
        }

        private async Task UpdateMonthlyStatistics(AppDbContext context)
        {
            var lastMonth = DateTime.Today.AddMonths(-1);
            await UpdateWorkSummaryForPeriod(context, "monthly", lastMonth);
        }

        private async Task UpdateWorkSummaryForPeriod(AppDbContext context, string periodType, DateTime periodDate)
        {
            try
            {
                // 这里实现具体的统计更新逻辑
                // 可以调用存储过程或使用LINQ查询
                var sql = $"CALL UpdateUserWorkSummary('{periodType}', '{periodDate:yyyy-MM-dd}')";
                await context.Database.ExecuteSqlRawAsync(sql);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新{PeriodType}统计失败: {Date}", periodType, periodDate);
            }
        }

        private async Task PushRealtimeUpdates(IServiceProvider serviceProvider, IHubContext<GamificationHub> hubContext)
        {
            try
            {
                var gamificationService = serviceProvider.GetRequiredService<IModernGamificationService>();
                
                // 获取最新排行榜
                var leaderboard = await gamificationService.GetLeaderboardAsync("points", 20);
                
                // 推送到所有连接的客户端
                await hubContext.Clients.Group("Statistics").SendAsync("LeaderboardUpdated", new
                {
                    Type = "points",
                    Data = leaderboard,
                    UpdatedAt = DateTime.Now,
                    Source = "scheduled_update"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "推送实时更新失败");
            }
        }
    }
}

// =====================================================
// appsettings.json 配置示例
// =====================================================
/*
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost;Database=itassets;User=root;Password=******;",
    "Redis": "localhost:6379" // 可选，如果有Redis
  },
  "Gamification": {
    "EnableRealTimeUpdates": true,
    "CacheExpirationMinutes": 5,
    "DailyPointsLimit": 200,
    "DailyItemsLimit": 5,
    "EnableBackgroundService": true
  },
  "SignalR": {
    "KeepAliveInterval": "00:00:15",
    "ClientTimeoutInterval": "00:00:30",
    "EnableDetailedErrors": true
  }
}
*/
