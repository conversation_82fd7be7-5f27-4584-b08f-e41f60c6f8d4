"use strict";var en=Object.defineProperty;var Ne=Object.getOwnPropertySymbols;var mt=Object.prototype.hasOwnProperty,vt=Object.prototype.propertyIsEnumerable;var gt=(t,e,n)=>e in t?en(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n,ce=(t,e)=>{for(var n in e||(e={}))mt.call(e,n)&&gt(t,n,e[n]);if(Ne)for(var n of Ne(e))vt.call(e,n)&&gt(t,n,e[n]);return t};var Ve=(t,e)=>{var n={};for(var o in t)mt.call(t,o)&&e.indexOf(o)<0&&(n[o]=t[o]);if(t!=null&&Ne)for(var o of Ne(t))e.indexOf(o)<0&&vt.call(t,o)&&(n[o]=t[o]);return n};Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const D=require("vue"),Rt="[vue-draggable-plus]: ";function tn(t){console.warn(Rt+t)}function nn(t){console.error(Rt+t)}function bt(t,e,n){return n>=0&&n<t.length&&t.splice(n,0,t.splice(e,1)[0]),t}function on(t){return t.replace(/-(\w)/g,(e,n)=>n?n.toUpperCase():"")}function rn(t){return Object.keys(t).reduce((e,n)=>(typeof t[n]!="undefined"&&(e[on(n)]=t[n]),e),{})}function yt(t,e){return Array.isArray(t)&&t.splice(e,1),t}function wt(t,e,n){return Array.isArray(t)&&t.splice(e,0,n),t}function an(t){return typeof t=="undefined"}function ln(t){return typeof t=="string"}function Et(t,e,n){const o=t.children[n];t.insertBefore(e,o)}function $e(t){t.parentNode&&t.parentNode.removeChild(t)}function sn(t,e=document){var o;let n=null;return typeof(e==null?void 0:e.querySelector)=="function"?n=(o=e==null?void 0:e.querySelector)==null?void 0:o.call(e,t):n=document.querySelector(t),n||tn(`Element not found: ${t}`),n}function un(t,e,n=null){return function(...o){return t.apply(n,o),e.apply(n,o)}}function fn(t,e){const n=ce({},t);return Object.keys(e).forEach(o=>{n[o]?n[o]=un(t[o],e[o]):n[o]=e[o]}),n}function cn(t){return t instanceof HTMLElement}function St(t,e){Object.keys(t).forEach(n=>{e(n,t[n])})}function dn(t){return t.charCodeAt(0)===111&&t.charCodeAt(1)===110&&(t.charCodeAt(2)>122||t.charCodeAt(2)<97)}const hn=Object.assign;/**!
 * Sortable 1.15.2
 * <AUTHOR>   <<EMAIL>>
 * <AUTHOR>    <<EMAIL>>
 * @license MIT
 */function Dt(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);e&&(o=o.filter(function(r){return Object.getOwnPropertyDescriptor(t,r).enumerable})),n.push.apply(n,o)}return n}function te(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?Dt(Object(n),!0).forEach(function(o){pn(t,o,n[o])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Dt(Object(n)).forEach(function(o){Object.defineProperty(t,o,Object.getOwnPropertyDescriptor(n,o))})}return t}function Xe(t){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?Xe=function(e){return typeof e}:Xe=function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Xe(t)}function pn(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function re(){return re=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(t[o]=n[o])}return t},re.apply(this,arguments)}function gn(t,e){if(t==null)return{};var n={},o=Object.keys(t),r,i;for(i=0;i<o.length;i++)r=o[i],!(e.indexOf(r)>=0)&&(n[r]=t[r]);return n}function mn(t,e){if(t==null)return{};var n=gn(t,e),o,r;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)o=i[r],!(e.indexOf(o)>=0)&&Object.prototype.propertyIsEnumerable.call(t,o)&&(n[o]=t[o])}return n}var vn="1.15.2";function oe(t){if(typeof window!="undefined"&&window.navigator)return!!navigator.userAgent.match(t)}var ie=oe(/(?:Trident.*rv[ :]?11\.|msie|iemobile|Windows Phone)/i),Ae=oe(/Edge/i),_t=oe(/firefox/i),_e=oe(/safari/i)&&!oe(/chrome/i)&&!oe(/android/i),Xt=oe(/iP(ad|od|hone)/i),Yt=oe(/chrome/i)&&oe(/android/i),Bt={capture:!1,passive:!1};function _(t,e,n){t.addEventListener(e,n,!ie&&Bt)}function S(t,e,n){t.removeEventListener(e,n,!ie&&Bt)}function Le(t,e){if(e){if(e[0]===">"&&(e=e.substring(1)),t)try{if(t.matches)return t.matches(e);if(t.msMatchesSelector)return t.msMatchesSelector(e);if(t.webkitMatchesSelector)return t.webkitMatchesSelector(e)}catch(n){return!1}return!1}}function bn(t){return t.host&&t!==document&&t.host.nodeType?t.host:t.parentNode}function Z(t,e,n,o){if(t){n=n||document;do{if(e!=null&&(e[0]===">"?t.parentNode===n&&Le(t,e):Le(t,e))||o&&t===n)return t;if(t===n)break}while(t=bn(t))}return null}var Tt=/\s+/g;function V(t,e,n){if(t&&e)if(t.classList)t.classList[n?"add":"remove"](e);else{var o=(" "+t.className+" ").replace(Tt," ").replace(" "+e+" "," ");t.className=(o+(n?" "+e:"")).replace(Tt," ")}}function h(t,e,n){var o=t&&t.style;if(o){if(n===void 0)return document.defaultView&&document.defaultView.getComputedStyle?n=document.defaultView.getComputedStyle(t,""):t.currentStyle&&(n=t.currentStyle),e===void 0?n:n[e];!(e in o)&&e.indexOf("webkit")===-1&&(e="-webkit-"+e),o[e]=n+(typeof n=="string"?"":"px")}}function ye(t,e){var n="";if(typeof t=="string")n=t;else do{var o=h(t,"transform");o&&o!=="none"&&(n=o+" "+n)}while(!e&&(t=t.parentNode));var r=window.DOMMatrix||window.WebKitCSSMatrix||window.CSSMatrix||window.MSCSSMatrix;return r&&new r(n)}function kt(t,e,n){if(t){var o=t.getElementsByTagName(e),r=0,i=o.length;if(n)for(;r<i;r++)n(o[r],r);return o}return[]}function ee(){var t=document.scrollingElement;return t||document.documentElement}function F(t,e,n,o,r){if(!(!t.getBoundingClientRect&&t!==window)){var i,a,l,s,u,d,c;if(t!==window&&t.parentNode&&t!==ee()?(i=t.getBoundingClientRect(),a=i.top,l=i.left,s=i.bottom,u=i.right,d=i.height,c=i.width):(a=0,l=0,s=window.innerHeight,u=window.innerWidth,d=window.innerHeight,c=window.innerWidth),(e||n)&&t!==window&&(r=r||t.parentNode,!ie))do if(r&&r.getBoundingClientRect&&(h(r,"transform")!=="none"||n&&h(r,"position")!=="static")){var m=r.getBoundingClientRect();a-=m.top+parseInt(h(r,"border-top-width")),l-=m.left+parseInt(h(r,"border-left-width")),s=a+i.height,u=l+i.width;break}while(r=r.parentNode);if(o&&t!==window){var y=ye(r||t),b=y&&y.a,E=y&&y.d;y&&(a/=E,l/=b,c/=b,d/=E,s=a+d,u=l+c)}return{top:a,left:l,bottom:s,right:u,width:c,height:d}}}function Ct(t,e,n){for(var o=ue(t,!0),r=F(t)[e];o;){var i=F(o)[n],a=void 0;if(a=r>=i,!a)return o;if(o===ee())break;o=ue(o,!1)}return!1}function we(t,e,n,o){for(var r=0,i=0,a=t.children;i<a.length;){if(a[i].style.display!=="none"&&a[i]!==p.ghost&&(o||a[i]!==p.dragged)&&Z(a[i],n.draggable,t,!1)){if(r===e)return a[i];r++}i++}return null}function ft(t,e){for(var n=t.lastElementChild;n&&(n===p.ghost||h(n,"display")==="none"||e&&!Le(n,e));)n=n.previousElementSibling;return n||null}function K(t,e){var n=0;if(!t||!t.parentNode)return-1;for(;t=t.previousElementSibling;)t.nodeName.toUpperCase()!=="TEMPLATE"&&t!==p.clone&&(!e||Le(t,e))&&n++;return n}function Ot(t){var e=0,n=0,o=ee();if(t)do{var r=ye(t),i=r.a,a=r.d;e+=t.scrollLeft*i,n+=t.scrollTop*a}while(t!==o&&(t=t.parentNode));return[e,n]}function yn(t,e){for(var n in t)if(t.hasOwnProperty(n)){for(var o in e)if(e.hasOwnProperty(o)&&e[o]===t[n][o])return Number(n)}return-1}function ue(t,e){if(!t||!t.getBoundingClientRect)return ee();var n=t,o=!1;do if(n.clientWidth<n.scrollWidth||n.clientHeight<n.scrollHeight){var r=h(n);if(n.clientWidth<n.scrollWidth&&(r.overflowX=="auto"||r.overflowX=="scroll")||n.clientHeight<n.scrollHeight&&(r.overflowY=="auto"||r.overflowY=="scroll")){if(!n.getBoundingClientRect||n===document.body)return ee();if(o||e)return n;o=!0}}while(n=n.parentNode);return ee()}function wn(t,e){if(t&&e)for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n]);return t}function qe(t,e){return Math.round(t.top)===Math.round(e.top)&&Math.round(t.left)===Math.round(e.left)&&Math.round(t.height)===Math.round(e.height)&&Math.round(t.width)===Math.round(e.width)}var Te;function Ht(t,e){return function(){if(!Te){var n=arguments,o=this;n.length===1?t.call(o,n[0]):t.apply(o,n),Te=setTimeout(function(){Te=void 0},e)}}}function En(){clearTimeout(Te),Te=void 0}function Lt(t,e,n){t.scrollLeft+=e,t.scrollTop+=n}function Wt(t){var e=window.Polymer,n=window.jQuery||window.Zepto;return e&&e.dom?e.dom(t).cloneNode(!0):n?n(t).clone(!0)[0]:t.cloneNode(!0)}function Gt(t,e,n){var o={};return Array.from(t.children).forEach(function(r){var i,a,l,s;if(!(!Z(r,e.draggable,t,!1)||r.animated||r===n)){var u=F(r);o.left=Math.min((i=o.left)!==null&&i!==void 0?i:1/0,u.left),o.top=Math.min((a=o.top)!==null&&a!==void 0?a:1/0,u.top),o.right=Math.max((l=o.right)!==null&&l!==void 0?l:-1/0,u.right),o.bottom=Math.max((s=o.bottom)!==null&&s!==void 0?s:-1/0,u.bottom)}}),o.width=o.right-o.left,o.height=o.bottom-o.top,o.x=o.left,o.y=o.top,o}var q="Sortable"+new Date().getTime();function Sn(){var t=[],e;return{captureAnimationState:function(){if(t=[],!!this.options.animation){var o=[].slice.call(this.el.children);o.forEach(function(r){if(!(h(r,"display")==="none"||r===p.ghost)){t.push({target:r,rect:F(r)});var i=te({},t[t.length-1].rect);if(r.thisAnimationDuration){var a=ye(r,!0);a&&(i.top-=a.f,i.left-=a.e)}r.fromRect=i}})}},addAnimationState:function(o){t.push(o)},removeAnimationState:function(o){t.splice(yn(t,{target:o}),1)},animateAll:function(o){var r=this;if(!this.options.animation){clearTimeout(e),typeof o=="function"&&o();return}var i=!1,a=0;t.forEach(function(l){var s=0,u=l.target,d=u.fromRect,c=F(u),m=u.prevFromRect,y=u.prevToRect,b=l.rect,E=ye(u,!0);E&&(c.top-=E.f,c.left-=E.e),u.toRect=c,u.thisAnimationDuration&&qe(m,c)&&!qe(d,c)&&(b.top-c.top)/(b.left-c.left)===(d.top-c.top)/(d.left-c.left)&&(s=_n(b,m,y,r.options)),qe(c,d)||(u.prevFromRect=d,u.prevToRect=c,s||(s=r.options.animation),r.animate(u,b,c,s)),s&&(i=!0,a=Math.max(a,s),clearTimeout(u.animationResetTimer),u.animationResetTimer=setTimeout(function(){u.animationTime=0,u.prevFromRect=null,u.fromRect=null,u.prevToRect=null,u.thisAnimationDuration=null},s),u.thisAnimationDuration=s)}),clearTimeout(e),i?e=setTimeout(function(){typeof o=="function"&&o()},a):typeof o=="function"&&o(),t=[]},animate:function(o,r,i,a){if(a){h(o,"transition",""),h(o,"transform","");var l=ye(this.el),s=l&&l.a,u=l&&l.d,d=(r.left-i.left)/(s||1),c=(r.top-i.top)/(u||1);o.animatingX=!!d,o.animatingY=!!c,h(o,"transform","translate3d("+d+"px,"+c+"px,0)"),this.forRepaintDummy=Dn(o),h(o,"transition","transform "+a+"ms"+(this.options.easing?" "+this.options.easing:"")),h(o,"transform","translate3d(0,0,0)"),typeof o.animated=="number"&&clearTimeout(o.animated),o.animated=setTimeout(function(){h(o,"transition",""),h(o,"transform",""),o.animated=!1,o.animatingX=!1,o.animatingY=!1},a)}}}}function Dn(t){return t.offsetWidth}function _n(t,e,n,o){return Math.sqrt(Math.pow(e.top-t.top,2)+Math.pow(e.left-t.left,2))/Math.sqrt(Math.pow(e.top-n.top,2)+Math.pow(e.left-n.left,2))*o.animation}var ge=[],Ke={initializeByDefault:!0},xe={mount:function(e){for(var n in Ke)Ke.hasOwnProperty(n)&&!(n in e)&&(e[n]=Ke[n]);ge.forEach(function(o){if(o.pluginName===e.pluginName)throw"Sortable: Cannot mount plugin ".concat(e.pluginName," more than once")}),ge.push(e)},pluginEvent:function(e,n,o){var r=this;this.eventCanceled=!1,o.cancel=function(){r.eventCanceled=!0};var i=e+"Global";ge.forEach(function(a){n[a.pluginName]&&(n[a.pluginName][i]&&n[a.pluginName][i](te({sortable:n},o)),n.options[a.pluginName]&&n[a.pluginName][e]&&n[a.pluginName][e](te({sortable:n},o)))})},initializePlugins:function(e,n,o,r){ge.forEach(function(l){var s=l.pluginName;if(!(!e.options[s]&&!l.initializeByDefault)){var u=new l(e,n,e.options);u.sortable=e,u.options=e.options,e[s]=u,re(o,u.defaults)}});for(var i in e.options)if(e.options.hasOwnProperty(i)){var a=this.modifyOption(e,i,e.options[i]);typeof a!="undefined"&&(e.options[i]=a)}},getEventProperties:function(e,n){var o={};return ge.forEach(function(r){typeof r.eventProperties=="function"&&re(o,r.eventProperties.call(n[r.pluginName],e))}),o},modifyOption:function(e,n,o){var r;return ge.forEach(function(i){e[i.pluginName]&&i.optionListeners&&typeof i.optionListeners[n]=="function"&&(r=i.optionListeners[n].call(e[i.pluginName],o))}),r}};function Tn(t){var e=t.sortable,n=t.rootEl,o=t.name,r=t.targetEl,i=t.cloneEl,a=t.toEl,l=t.fromEl,s=t.oldIndex,u=t.newIndex,d=t.oldDraggableIndex,c=t.newDraggableIndex,m=t.originalEvent,y=t.putSortable,b=t.extraEventProperties;if(e=e||n&&n[q],!!e){var E,H=e.options,L="on"+o.charAt(0).toUpperCase()+o.substr(1);window.CustomEvent&&!ie&&!Ae?E=new CustomEvent(o,{bubbles:!0,cancelable:!0}):(E=document.createEvent("Event"),E.initEvent(o,!0,!0)),E.to=a||n,E.from=l||n,E.item=r||n,E.clone=i,E.oldIndex=s,E.newIndex=u,E.oldDraggableIndex=d,E.newDraggableIndex=c,E.originalEvent=m,E.pullMode=y?y.lastPutMode:void 0;var R=te(te({},b),xe.getEventProperties(o,e));for(var x in R)E[x]=R[x];n&&n.dispatchEvent(E),H[L]&&H[L].call(e,E)}}var Cn=["evt"],j=function(e,n){var o=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},r=o.evt,i=mn(o,Cn);xe.pluginEvent.bind(p)(e,n,te({dragEl:f,parentEl:A,ghostEl:g,rootEl:O,nextEl:pe,lastDownEl:Ye,cloneEl:I,cloneHidden:se,dragStarted:Ee,putSortable:Y,activeSortable:p.active,originalEvent:r,oldIndex:be,oldDraggableIndex:Ce,newIndex:$,newDraggableIndex:le,hideGhostForTarget:Vt,unhideGhostForTarget:$t,cloneNowHidden:function(){se=!0},cloneNowShown:function(){se=!1},dispatchSortableEvent:function(l){G({sortable:n,name:l,originalEvent:r})}},i))};function G(t){Tn(te({putSortable:Y,cloneEl:I,targetEl:f,rootEl:O,oldIndex:be,oldDraggableIndex:Ce,newIndex:$,newDraggableIndex:le},t))}var f,A,g,O,pe,Ye,I,se,be,$,Ce,le,Pe,Y,ve=!1,We=!1,Ge=[],de,J,Je,Ze,It,At,Ee,me,Oe,Ie=!1,Me=!1,Be,k,Qe=[],it=!1,je=[],Ue=typeof document!="undefined",Fe=Xt,xt=Ae||ie?"cssFloat":"float",On=Ue&&!Yt&&!Xt&&"draggable"in document.createElement("div"),jt=function(){if(Ue){if(ie)return!1;var t=document.createElement("x");return t.style.cssText="pointer-events:auto",t.style.pointerEvents==="auto"}}(),zt=function(e,n){var o=h(e),r=parseInt(o.width)-parseInt(o.paddingLeft)-parseInt(o.paddingRight)-parseInt(o.borderLeftWidth)-parseInt(o.borderRightWidth),i=we(e,0,n),a=we(e,1,n),l=i&&h(i),s=a&&h(a),u=l&&parseInt(l.marginLeft)+parseInt(l.marginRight)+F(i).width,d=s&&parseInt(s.marginLeft)+parseInt(s.marginRight)+F(a).width;if(o.display==="flex")return o.flexDirection==="column"||o.flexDirection==="column-reverse"?"vertical":"horizontal";if(o.display==="grid")return o.gridTemplateColumns.split(" ").length<=1?"vertical":"horizontal";if(i&&l.float&&l.float!=="none"){var c=l.float==="left"?"left":"right";return a&&(s.clear==="both"||s.clear===c)?"vertical":"horizontal"}return i&&(l.display==="block"||l.display==="flex"||l.display==="table"||l.display==="grid"||u>=r&&o[xt]==="none"||a&&o[xt]==="none"&&u+d>r)?"vertical":"horizontal"},In=function(e,n,o){var r=o?e.left:e.top,i=o?e.right:e.bottom,a=o?e.width:e.height,l=o?n.left:n.top,s=o?n.right:n.bottom,u=o?n.width:n.height;return r===l||i===s||r+a/2===l+u/2},An=function(e,n){var o;return Ge.some(function(r){var i=r[q].options.emptyInsertThreshold;if(!(!i||ft(r))){var a=F(r),l=e>=a.left-i&&e<=a.right+i,s=n>=a.top-i&&n<=a.bottom+i;if(l&&s)return o=r}}),o},Ut=function(e){function n(i,a){return function(l,s,u,d){var c=l.options.group.name&&s.options.group.name&&l.options.group.name===s.options.group.name;if(i==null&&(a||c))return!0;if(i==null||i===!1)return!1;if(a&&i==="clone")return i;if(typeof i=="function")return n(i(l,s,u,d),a)(l,s,u,d);var m=(a?l:s).options.group.name;return i===!0||typeof i=="string"&&i===m||i.join&&i.indexOf(m)>-1}}var o={},r=e.group;(!r||Xe(r)!="object")&&(r={name:r}),o.name=r.name,o.checkPull=n(r.pull,!0),o.checkPut=n(r.put),o.revertClone=r.revertClone,e.group=o},Vt=function(){!jt&&g&&h(g,"display","none")},$t=function(){!jt&&g&&h(g,"display","")};Ue&&!Yt&&document.addEventListener("click",function(t){if(We)return t.preventDefault(),t.stopPropagation&&t.stopPropagation(),t.stopImmediatePropagation&&t.stopImmediatePropagation(),We=!1,!1},!0);var he=function(e){if(f){e=e.touches?e.touches[0]:e;var n=An(e.clientX,e.clientY);if(n){var o={};for(var r in e)e.hasOwnProperty(r)&&(o[r]=e[r]);o.target=o.rootEl=n,o.preventDefault=void 0,o.stopPropagation=void 0,n[q]._onDragOver(o)}}},xn=function(e){f&&f.parentNode[q]._isOutsideThisEl(e.target)};function p(t,e){if(!(t&&t.nodeType&&t.nodeType===1))throw"Sortable: `el` must be an HTMLElement, not ".concat({}.toString.call(t));this.el=t,this.options=e=re({},e),t[q]=this;var n={group:null,sort:!0,disabled:!1,store:null,handle:null,draggable:/^[uo]l$/i.test(t.nodeName)?">li":">*",swapThreshold:1,invertSwap:!1,invertedSwapThreshold:null,removeCloneOnHide:!0,direction:function(){return zt(t,this.options)},ghostClass:"sortable-ghost",chosenClass:"sortable-chosen",dragClass:"sortable-drag",ignore:"a, img",filter:null,preventOnFilter:!0,animation:0,easing:null,setData:function(a,l){a.setData("Text",l.textContent)},dropBubble:!1,dragoverBubble:!1,dataIdAttr:"data-id",delay:0,delayOnTouchOnly:!1,touchStartThreshold:(Number.parseInt?Number:window).parseInt(window.devicePixelRatio,10)||1,forceFallback:!1,fallbackClass:"sortable-fallback",fallbackOnBody:!1,fallbackTolerance:0,fallbackOffset:{x:0,y:0},supportPointer:p.supportPointer!==!1&&"PointerEvent"in window&&!_e,emptyInsertThreshold:5};xe.initializePlugins(this,t,n);for(var o in n)!(o in e)&&(e[o]=n[o]);Ut(e);for(var r in this)r.charAt(0)==="_"&&typeof this[r]=="function"&&(this[r]=this[r].bind(this));this.nativeDraggable=e.forceFallback?!1:On,this.nativeDraggable&&(this.options.touchStartThreshold=1),e.supportPointer?_(t,"pointerdown",this._onTapStart):(_(t,"mousedown",this._onTapStart),_(t,"touchstart",this._onTapStart)),this.nativeDraggable&&(_(t,"dragover",this),_(t,"dragenter",this)),Ge.push(this.el),e.store&&e.store.get&&this.sort(e.store.get(this)||[]),re(this,Sn())}p.prototype={constructor:p,_isOutsideThisEl:function(e){!this.el.contains(e)&&e!==this.el&&(me=null)},_getDirection:function(e,n){return typeof this.options.direction=="function"?this.options.direction.call(this,e,n,f):this.options.direction},_onTapStart:function(e){if(e.cancelable){var n=this,o=this.el,r=this.options,i=r.preventOnFilter,a=e.type,l=e.touches&&e.touches[0]||e.pointerType&&e.pointerType==="touch"&&e,s=(l||e).target,u=e.target.shadowRoot&&(e.path&&e.path[0]||e.composedPath&&e.composedPath()[0])||s,d=r.filter;if(Bn(o),!f&&!(/mousedown|pointerdown/.test(a)&&e.button!==0||r.disabled)&&!u.isContentEditable&&!(!this.nativeDraggable&&_e&&s&&s.tagName.toUpperCase()==="SELECT")&&(s=Z(s,r.draggable,o,!1),!(s&&s.animated)&&Ye!==s)){if(be=K(s),Ce=K(s,r.draggable),typeof d=="function"){if(d.call(this,e,s,this)){G({sortable:n,rootEl:u,name:"filter",targetEl:s,toEl:o,fromEl:o}),j("filter",n,{evt:e}),i&&e.cancelable&&e.preventDefault();return}}else if(d&&(d=d.split(",").some(function(c){if(c=Z(u,c.trim(),o,!1),c)return G({sortable:n,rootEl:c,name:"filter",targetEl:s,fromEl:o,toEl:o}),j("filter",n,{evt:e}),!0}),d)){i&&e.cancelable&&e.preventDefault();return}r.handle&&!Z(u,r.handle,o,!1)||this._prepareDragStart(e,l,s)}}},_prepareDragStart:function(e,n,o){var r=this,i=r.el,a=r.options,l=i.ownerDocument,s;if(o&&!f&&o.parentNode===i){var u=F(o);if(O=i,f=o,A=f.parentNode,pe=f.nextSibling,Ye=o,Pe=a.group,p.dragged=f,de={target:f,clientX:(n||e).clientX,clientY:(n||e).clientY},It=de.clientX-u.left,At=de.clientY-u.top,this._lastX=(n||e).clientX,this._lastY=(n||e).clientY,f.style["will-change"]="all",s=function(){if(j("delayEnded",r,{evt:e}),p.eventCanceled){r._onDrop();return}r._disableDelayedDragEvents(),!_t&&r.nativeDraggable&&(f.draggable=!0),r._triggerDragStart(e,n),G({sortable:r,name:"choose",originalEvent:e}),V(f,a.chosenClass,!0)},a.ignore.split(",").forEach(function(d){kt(f,d.trim(),et)}),_(l,"dragover",he),_(l,"mousemove",he),_(l,"touchmove",he),_(l,"mouseup",r._onDrop),_(l,"touchend",r._onDrop),_(l,"touchcancel",r._onDrop),_t&&this.nativeDraggable&&(this.options.touchStartThreshold=4,f.draggable=!0),j("delayStart",this,{evt:e}),a.delay&&(!a.delayOnTouchOnly||n)&&(!this.nativeDraggable||!(Ae||ie))){if(p.eventCanceled){this._onDrop();return}_(l,"mouseup",r._disableDelayedDrag),_(l,"touchend",r._disableDelayedDrag),_(l,"touchcancel",r._disableDelayedDrag),_(l,"mousemove",r._delayedDragTouchMoveHandler),_(l,"touchmove",r._delayedDragTouchMoveHandler),a.supportPointer&&_(l,"pointermove",r._delayedDragTouchMoveHandler),r._dragStartTimer=setTimeout(s,a.delay)}else s()}},_delayedDragTouchMoveHandler:function(e){var n=e.touches?e.touches[0]:e;Math.max(Math.abs(n.clientX-this._lastX),Math.abs(n.clientY-this._lastY))>=Math.floor(this.options.touchStartThreshold/(this.nativeDraggable&&window.devicePixelRatio||1))&&this._disableDelayedDrag()},_disableDelayedDrag:function(){f&&et(f),clearTimeout(this._dragStartTimer),this._disableDelayedDragEvents()},_disableDelayedDragEvents:function(){var e=this.el.ownerDocument;S(e,"mouseup",this._disableDelayedDrag),S(e,"touchend",this._disableDelayedDrag),S(e,"touchcancel",this._disableDelayedDrag),S(e,"mousemove",this._delayedDragTouchMoveHandler),S(e,"touchmove",this._delayedDragTouchMoveHandler),S(e,"pointermove",this._delayedDragTouchMoveHandler)},_triggerDragStart:function(e,n){n=n||e.pointerType=="touch"&&e,!this.nativeDraggable||n?this.options.supportPointer?_(document,"pointermove",this._onTouchMove):n?_(document,"touchmove",this._onTouchMove):_(document,"mousemove",this._onTouchMove):(_(f,"dragend",this),_(O,"dragstart",this._onDragStart));try{document.selection?ke(function(){document.selection.empty()}):window.getSelection().removeAllRanges()}catch(o){}},_dragStarted:function(e,n){if(ve=!1,O&&f){j("dragStarted",this,{evt:n}),this.nativeDraggable&&_(document,"dragover",xn);var o=this.options;!e&&V(f,o.dragClass,!1),V(f,o.ghostClass,!0),p.active=this,e&&this._appendGhost(),G({sortable:this,name:"start",originalEvent:n})}else this._nulling()},_emulateDragOver:function(){if(J){this._lastX=J.clientX,this._lastY=J.clientY,Vt();for(var e=document.elementFromPoint(J.clientX,J.clientY),n=e;e&&e.shadowRoot&&(e=e.shadowRoot.elementFromPoint(J.clientX,J.clientY),e!==n);)n=e;if(f.parentNode[q]._isOutsideThisEl(e),n)do{if(n[q]){var o=void 0;if(o=n[q]._onDragOver({clientX:J.clientX,clientY:J.clientY,target:e,rootEl:n}),o&&!this.options.dragoverBubble)break}e=n}while(n=n.parentNode);$t()}},_onTouchMove:function(e){if(de){var n=this.options,o=n.fallbackTolerance,r=n.fallbackOffset,i=e.touches?e.touches[0]:e,a=g&&ye(g,!0),l=g&&a&&a.a,s=g&&a&&a.d,u=Fe&&k&&Ot(k),d=(i.clientX-de.clientX+r.x)/(l||1)+(u?u[0]-Qe[0]:0)/(l||1),c=(i.clientY-de.clientY+r.y)/(s||1)+(u?u[1]-Qe[1]:0)/(s||1);if(!p.active&&!ve){if(o&&Math.max(Math.abs(i.clientX-this._lastX),Math.abs(i.clientY-this._lastY))<o)return;this._onDragStart(e,!0)}if(g){a?(a.e+=d-(Je||0),a.f+=c-(Ze||0)):a={a:1,b:0,c:0,d:1,e:d,f:c};var m="matrix(".concat(a.a,",").concat(a.b,",").concat(a.c,",").concat(a.d,",").concat(a.e,",").concat(a.f,")");h(g,"webkitTransform",m),h(g,"mozTransform",m),h(g,"msTransform",m),h(g,"transform",m),Je=d,Ze=c,J=i}e.cancelable&&e.preventDefault()}},_appendGhost:function(){if(!g){var e=this.options.fallbackOnBody?document.body:O,n=F(f,!0,Fe,!0,e),o=this.options;if(Fe){for(k=e;h(k,"position")==="static"&&h(k,"transform")==="none"&&k!==document;)k=k.parentNode;k!==document.body&&k!==document.documentElement?(k===document&&(k=ee()),n.top+=k.scrollTop,n.left+=k.scrollLeft):k=ee(),Qe=Ot(k)}g=f.cloneNode(!0),V(g,o.ghostClass,!1),V(g,o.fallbackClass,!0),V(g,o.dragClass,!0),h(g,"transition",""),h(g,"transform",""),h(g,"box-sizing","border-box"),h(g,"margin",0),h(g,"top",n.top),h(g,"left",n.left),h(g,"width",n.width),h(g,"height",n.height),h(g,"opacity","0.8"),h(g,"position",Fe?"absolute":"fixed"),h(g,"zIndex","100000"),h(g,"pointerEvents","none"),p.ghost=g,e.appendChild(g),h(g,"transform-origin",It/parseInt(g.style.width)*100+"% "+At/parseInt(g.style.height)*100+"%")}},_onDragStart:function(e,n){var o=this,r=e.dataTransfer,i=o.options;if(j("dragStart",this,{evt:e}),p.eventCanceled){this._onDrop();return}j("setupClone",this),p.eventCanceled||(I=Wt(f),I.removeAttribute("id"),I.draggable=!1,I.style["will-change"]="",this._hideClone(),V(I,this.options.chosenClass,!1),p.clone=I),o.cloneId=ke(function(){j("clone",o),!p.eventCanceled&&(o.options.removeCloneOnHide||O.insertBefore(I,f),o._hideClone(),G({sortable:o,name:"clone"}))}),!n&&V(f,i.dragClass,!0),n?(We=!0,o._loopId=setInterval(o._emulateDragOver,50)):(S(document,"mouseup",o._onDrop),S(document,"touchend",o._onDrop),S(document,"touchcancel",o._onDrop),r&&(r.effectAllowed="move",i.setData&&i.setData.call(o,r,f)),_(document,"drop",o),h(f,"transform","translateZ(0)")),ve=!0,o._dragStartId=ke(o._dragStarted.bind(o,n,e)),_(document,"selectstart",o),Ee=!0,_e&&h(document.body,"user-select","none")},_onDragOver:function(e){var n=this.el,o=e.target,r,i,a,l=this.options,s=l.group,u=p.active,d=Pe===s,c=l.sort,m=Y||u,y,b=this,E=!1;if(it)return;function H(fe,Zt){j(fe,b,te({evt:e,isOwner:d,axis:y?"vertical":"horizontal",revert:a,dragRect:r,targetRect:i,canSort:c,fromSortable:m,target:o,completed:R,onMove:function(pt,Qt){return Re(O,n,f,r,pt,F(pt),e,Qt)},changed:x},Zt))}function L(){H("dragOverAnimationCapture"),b.captureAnimationState(),b!==m&&m.captureAnimationState()}function R(fe){return H("dragOverCompleted",{insertion:fe}),fe&&(d?u._hideClone():u._showClone(b),b!==m&&(V(f,Y?Y.options.ghostClass:u.options.ghostClass,!1),V(f,l.ghostClass,!0)),Y!==b&&b!==p.active?Y=b:b===p.active&&Y&&(Y=null),m===b&&(b._ignoreWhileAnimating=o),b.animateAll(function(){H("dragOverAnimationComplete"),b._ignoreWhileAnimating=null}),b!==m&&(m.animateAll(),m._ignoreWhileAnimating=null)),(o===f&&!f.animated||o===n&&!o.animated)&&(me=null),!l.dragoverBubble&&!e.rootEl&&o!==document&&(f.parentNode[q]._isOutsideThisEl(e.target),!fe&&he(e)),!l.dragoverBubble&&e.stopPropagation&&e.stopPropagation(),E=!0}function x(){$=K(f),le=K(f,l.draggable),G({sortable:b,name:"change",toEl:n,newIndex:$,newDraggableIndex:le,originalEvent:e})}if(e.preventDefault!==void 0&&e.cancelable&&e.preventDefault(),o=Z(o,l.draggable,n,!0),H("dragOver"),p.eventCanceled)return E;if(f.contains(e.target)||o.animated&&o.animatingX&&o.animatingY||b._ignoreWhileAnimating===o)return R(!1);if(We=!1,u&&!l.disabled&&(d?c||(a=A!==O):Y===this||(this.lastPutMode=Pe.checkPull(this,u,f,e))&&s.checkPut(this,u,f,e))){if(y=this._getDirection(e,o)==="vertical",r=F(f),H("dragOverValid"),p.eventCanceled)return E;if(a)return A=O,L(),this._hideClone(),H("revert"),p.eventCanceled||(pe?O.insertBefore(f,pe):O.appendChild(f)),R(!0);var W=ft(n,l.draggable);if(!W||Fn(e,y,this)&&!W.animated){if(W===f)return R(!1);if(W&&n===e.target&&(o=W),o&&(i=F(o)),Re(O,n,f,r,o,i,e,!!o)!==!1)return L(),W&&W.nextSibling?n.insertBefore(f,W.nextSibling):n.appendChild(f),A=n,x(),R(!0)}else if(W&&Mn(e,y,this)){var ne=we(n,0,l,!0);if(ne===f)return R(!1);if(o=ne,i=F(o),Re(O,n,f,r,o,i,e,!1)!==!1)return L(),n.insertBefore(f,ne),A=n,x(),R(!0)}else if(o.parentNode===n){i=F(o);var z=0,Q,v=f.parentNode!==n,w=!In(f.animated&&f.toRect||r,o.animated&&o.toRect||i,y),N=y?"top":"left",P=Ct(o,"top","top")||Ct(f,"top","top"),T=P?P.scrollTop:void 0;me!==o&&(Q=i[N],Ie=!1,Me=!w&&l.invertSwap||v),z=Rn(e,o,i,y,w?1:l.swapThreshold,l.invertedSwapThreshold==null?l.swapThreshold:l.invertedSwapThreshold,Me,me===o);var C;if(z!==0){var X=K(f);do X-=z,C=A.children[X];while(C&&(h(C,"display")==="none"||C===g))}if(z===0||C===o)return R(!1);me=o,Oe=z;var B=o.nextElementSibling,U=!1;U=z===1;var ae=Re(O,n,f,r,o,i,e,U);if(ae!==!1)return(ae===1||ae===-1)&&(U=ae===1),it=!0,setTimeout(Pn,30),L(),U&&!B?n.appendChild(f):o.parentNode.insertBefore(f,U?B:o),P&&Lt(P,0,T-P.scrollTop),A=f.parentNode,Q!==void 0&&!Me&&(Be=Math.abs(Q-F(o)[N])),x(),R(!0)}if(n.contains(f))return R(!1)}return!1},_ignoreWhileAnimating:null,_offMoveEvents:function(){S(document,"mousemove",this._onTouchMove),S(document,"touchmove",this._onTouchMove),S(document,"pointermove",this._onTouchMove),S(document,"dragover",he),S(document,"mousemove",he),S(document,"touchmove",he)},_offUpEvents:function(){var e=this.el.ownerDocument;S(e,"mouseup",this._onDrop),S(e,"touchend",this._onDrop),S(e,"pointerup",this._onDrop),S(e,"touchcancel",this._onDrop),S(document,"selectstart",this)},_onDrop:function(e){var n=this.el,o=this.options;if($=K(f),le=K(f,o.draggable),j("drop",this,{evt:e}),A=f&&f.parentNode,$=K(f),le=K(f,o.draggable),p.eventCanceled){this._nulling();return}ve=!1,Me=!1,Ie=!1,clearInterval(this._loopId),clearTimeout(this._dragStartTimer),at(this.cloneId),at(this._dragStartId),this.nativeDraggable&&(S(document,"drop",this),S(n,"dragstart",this._onDragStart)),this._offMoveEvents(),this._offUpEvents(),_e&&h(document.body,"user-select",""),h(f,"transform",""),e&&(Ee&&(e.cancelable&&e.preventDefault(),!o.dropBubble&&e.stopPropagation()),g&&g.parentNode&&g.parentNode.removeChild(g),(O===A||Y&&Y.lastPutMode!=="clone")&&I&&I.parentNode&&I.parentNode.removeChild(I),f&&(this.nativeDraggable&&S(f,"dragend",this),et(f),f.style["will-change"]="",Ee&&!ve&&V(f,Y?Y.options.ghostClass:this.options.ghostClass,!1),V(f,this.options.chosenClass,!1),G({sortable:this,name:"unchoose",toEl:A,newIndex:null,newDraggableIndex:null,originalEvent:e}),O!==A?($>=0&&(G({rootEl:A,name:"add",toEl:A,fromEl:O,originalEvent:e}),G({sortable:this,name:"remove",toEl:A,originalEvent:e}),G({rootEl:A,name:"sort",toEl:A,fromEl:O,originalEvent:e}),G({sortable:this,name:"sort",toEl:A,originalEvent:e})),Y&&Y.save()):$!==be&&$>=0&&(G({sortable:this,name:"update",toEl:A,originalEvent:e}),G({sortable:this,name:"sort",toEl:A,originalEvent:e})),p.active&&(($==null||$===-1)&&($=be,le=Ce),G({sortable:this,name:"end",toEl:A,originalEvent:e}),this.save()))),this._nulling()},_nulling:function(){j("nulling",this),O=f=A=g=pe=I=Ye=se=de=J=Ee=$=le=be=Ce=me=Oe=Y=Pe=p.dragged=p.ghost=p.clone=p.active=null,je.forEach(function(e){e.checked=!0}),je.length=Je=Ze=0},handleEvent:function(e){switch(e.type){case"drop":case"dragend":this._onDrop(e);break;case"dragenter":case"dragover":f&&(this._onDragOver(e),Nn(e));break;case"selectstart":e.preventDefault();break}},toArray:function(){for(var e=[],n,o=this.el.children,r=0,i=o.length,a=this.options;r<i;r++)n=o[r],Z(n,a.draggable,this.el,!1)&&e.push(n.getAttribute(a.dataIdAttr)||Yn(n));return e},sort:function(e,n){var o={},r=this.el;this.toArray().forEach(function(i,a){var l=r.children[a];Z(l,this.options.draggable,r,!1)&&(o[i]=l)},this),n&&this.captureAnimationState(),e.forEach(function(i){o[i]&&(r.removeChild(o[i]),r.appendChild(o[i]))}),n&&this.animateAll()},save:function(){var e=this.options.store;e&&e.set&&e.set(this)},closest:function(e,n){return Z(e,n||this.options.draggable,this.el,!1)},option:function(e,n){var o=this.options;if(n===void 0)return o[e];var r=xe.modifyOption(this,e,n);typeof r!="undefined"?o[e]=r:o[e]=n,e==="group"&&Ut(o)},destroy:function(){j("destroy",this);var e=this.el;e[q]=null,S(e,"mousedown",this._onTapStart),S(e,"touchstart",this._onTapStart),S(e,"pointerdown",this._onTapStart),this.nativeDraggable&&(S(e,"dragover",this),S(e,"dragenter",this)),Array.prototype.forEach.call(e.querySelectorAll("[draggable]"),function(n){n.removeAttribute("draggable")}),this._onDrop(),this._disableDelayedDragEvents(),Ge.splice(Ge.indexOf(this.el),1),this.el=e=null},_hideClone:function(){if(!se){if(j("hideClone",this),p.eventCanceled)return;h(I,"display","none"),this.options.removeCloneOnHide&&I.parentNode&&I.parentNode.removeChild(I),se=!0}},_showClone:function(e){if(e.lastPutMode!=="clone"){this._hideClone();return}if(se){if(j("showClone",this),p.eventCanceled)return;f.parentNode==O&&!this.options.group.revertClone?O.insertBefore(I,f):pe?O.insertBefore(I,pe):O.appendChild(I),this.options.group.revertClone&&this.animate(f,I),h(I,"display",""),se=!1}}};function Nn(t){t.dataTransfer&&(t.dataTransfer.dropEffect="move"),t.cancelable&&t.preventDefault()}function Re(t,e,n,o,r,i,a,l){var s,u=t[q],d=u.options.onMove,c;return window.CustomEvent&&!ie&&!Ae?s=new CustomEvent("move",{bubbles:!0,cancelable:!0}):(s=document.createEvent("Event"),s.initEvent("move",!0,!0)),s.to=e,s.from=t,s.dragged=n,s.draggedRect=o,s.related=r||e,s.relatedRect=i||F(e),s.willInsertAfter=l,s.originalEvent=a,t.dispatchEvent(s),d&&(c=d.call(u,s,a)),c}function et(t){t.draggable=!1}function Pn(){it=!1}function Mn(t,e,n){var o=F(we(n.el,0,n.options,!0)),r=Gt(n.el,n.options,g),i=10;return e?t.clientX<r.left-i||t.clientY<o.top&&t.clientX<o.right:t.clientY<r.top-i||t.clientY<o.bottom&&t.clientX<o.left}function Fn(t,e,n){var o=F(ft(n.el,n.options.draggable)),r=Gt(n.el,n.options,g),i=10;return e?t.clientX>r.right+i||t.clientY>o.bottom&&t.clientX>o.left:t.clientY>r.bottom+i||t.clientX>o.right&&t.clientY>o.top}function Rn(t,e,n,o,r,i,a,l){var s=o?t.clientY:t.clientX,u=o?n.height:n.width,d=o?n.top:n.left,c=o?n.bottom:n.right,m=!1;if(!a){if(l&&Be<u*r){if(!Ie&&(Oe===1?s>d+u*i/2:s<c-u*i/2)&&(Ie=!0),Ie)m=!0;else if(Oe===1?s<d+Be:s>c-Be)return-Oe}else if(s>d+u*(1-r)/2&&s<c-u*(1-r)/2)return Xn(e)}return m=m||a,m&&(s<d+u*i/2||s>c-u*i/2)?s>d+u/2?1:-1:0}function Xn(t){return K(f)<K(t)?1:-1}function Yn(t){for(var e=t.tagName+t.className+t.src+t.href+t.textContent,n=e.length,o=0;n--;)o+=e.charCodeAt(n);return o.toString(36)}function Bn(t){je.length=0;for(var e=t.getElementsByTagName("input"),n=e.length;n--;){var o=e[n];o.checked&&je.push(o)}}function ke(t){return setTimeout(t,0)}function at(t){return clearTimeout(t)}Ue&&_(document,"touchmove",function(t){(p.active||ve)&&t.cancelable&&t.preventDefault()});p.utils={on:_,off:S,css:h,find:kt,is:function(e,n){return!!Z(e,n,e,!1)},extend:wn,throttle:Ht,closest:Z,toggleClass:V,clone:Wt,index:K,nextTick:ke,cancelNextTick:at,detectDirection:zt,getChild:we};p.get=function(t){return t[q]};p.mount=function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];e[0].constructor===Array&&(e=e[0]),e.forEach(function(o){if(!o.prototype||!o.prototype.constructor)throw"Sortable: Mounted plugin must be a constructor function, not ".concat({}.toString.call(o));o.utils&&(p.utils=te(te({},p.utils),o.utils)),xe.mount(o)})};p.create=function(t,e){return new p(t,e)};p.version=vn;var M=[],Se,lt,st=!1,tt,nt,ze,De;function kn(){function t(){this.defaults={scroll:!0,forceAutoScrollFallback:!1,scrollSensitivity:30,scrollSpeed:10,bubbleScroll:!0};for(var e in this)e.charAt(0)==="_"&&typeof this[e]=="function"&&(this[e]=this[e].bind(this))}return t.prototype={dragStarted:function(n){var o=n.originalEvent;this.sortable.nativeDraggable?_(document,"dragover",this._handleAutoScroll):this.options.supportPointer?_(document,"pointermove",this._handleFallbackAutoScroll):o.touches?_(document,"touchmove",this._handleFallbackAutoScroll):_(document,"mousemove",this._handleFallbackAutoScroll)},dragOverCompleted:function(n){var o=n.originalEvent;!this.options.dragOverBubble&&!o.rootEl&&this._handleAutoScroll(o)},drop:function(){this.sortable.nativeDraggable?S(document,"dragover",this._handleAutoScroll):(S(document,"pointermove",this._handleFallbackAutoScroll),S(document,"touchmove",this._handleFallbackAutoScroll),S(document,"mousemove",this._handleFallbackAutoScroll)),Nt(),He(),En()},nulling:function(){ze=lt=Se=st=De=tt=nt=null,M.length=0},_handleFallbackAutoScroll:function(n){this._handleAutoScroll(n,!0)},_handleAutoScroll:function(n,o){var r=this,i=(n.touches?n.touches[0]:n).clientX,a=(n.touches?n.touches[0]:n).clientY,l=document.elementFromPoint(i,a);if(ze=n,o||this.options.forceAutoScrollFallback||Ae||ie||_e){ot(n,this.options,l,o);var s=ue(l,!0);st&&(!De||i!==tt||a!==nt)&&(De&&Nt(),De=setInterval(function(){var u=ue(document.elementFromPoint(i,a),!0);u!==s&&(s=u,He()),ot(n,r.options,u,o)},10),tt=i,nt=a)}else{if(!this.options.bubbleScroll||ue(l,!0)===ee()){He();return}ot(n,this.options,ue(l,!1),!1)}}},re(t,{pluginName:"scroll",initializeByDefault:!0})}function He(){M.forEach(function(t){clearInterval(t.pid)}),M=[]}function Nt(){clearInterval(De)}var ot=Ht(function(t,e,n,o){if(e.scroll){var r=(t.touches?t.touches[0]:t).clientX,i=(t.touches?t.touches[0]:t).clientY,a=e.scrollSensitivity,l=e.scrollSpeed,s=ee(),u=!1,d;lt!==n&&(lt=n,He(),Se=e.scroll,d=e.scrollFn,Se===!0&&(Se=ue(n,!0)));var c=0,m=Se;do{var y=m,b=F(y),E=b.top,H=b.bottom,L=b.left,R=b.right,x=b.width,W=b.height,ne=void 0,z=void 0,Q=y.scrollWidth,v=y.scrollHeight,w=h(y),N=y.scrollLeft,P=y.scrollTop;y===s?(ne=x<Q&&(w.overflowX==="auto"||w.overflowX==="scroll"||w.overflowX==="visible"),z=W<v&&(w.overflowY==="auto"||w.overflowY==="scroll"||w.overflowY==="visible")):(ne=x<Q&&(w.overflowX==="auto"||w.overflowX==="scroll"),z=W<v&&(w.overflowY==="auto"||w.overflowY==="scroll"));var T=ne&&(Math.abs(R-r)<=a&&N+x<Q)-(Math.abs(L-r)<=a&&!!N),C=z&&(Math.abs(H-i)<=a&&P+W<v)-(Math.abs(E-i)<=a&&!!P);if(!M[c])for(var X=0;X<=c;X++)M[X]||(M[X]={});(M[c].vx!=T||M[c].vy!=C||M[c].el!==y)&&(M[c].el=y,M[c].vx=T,M[c].vy=C,clearInterval(M[c].pid),(T!=0||C!=0)&&(u=!0,M[c].pid=setInterval(function(){o&&this.layer===0&&p.active._onTouchMove(ze);var B=M[this.layer].vy?M[this.layer].vy*l:0,U=M[this.layer].vx?M[this.layer].vx*l:0;typeof d=="function"&&d.call(p.dragged.parentNode[q],U,B,t,ze,M[this.layer].el)!=="continue"||Lt(M[this.layer].el,U,B)}.bind({layer:c}),24))),c++}while(e.bubbleScroll&&m!==s&&(m=ue(m,!1)));st=u}},30),qt=function(e){var n=e.originalEvent,o=e.putSortable,r=e.dragEl,i=e.activeSortable,a=e.dispatchSortableEvent,l=e.hideGhostForTarget,s=e.unhideGhostForTarget;if(n){var u=o||i;l();var d=n.changedTouches&&n.changedTouches.length?n.changedTouches[0]:n,c=document.elementFromPoint(d.clientX,d.clientY);s(),u&&!u.el.contains(c)&&(a("spill"),this.onSpill({dragEl:r,putSortable:o}))}};function ct(){}ct.prototype={startIndex:null,dragStart:function(e){var n=e.oldDraggableIndex;this.startIndex=n},onSpill:function(e){var n=e.dragEl,o=e.putSortable;this.sortable.captureAnimationState(),o&&o.captureAnimationState();var r=we(this.sortable.el,this.startIndex,this.options);r?this.sortable.el.insertBefore(n,r):this.sortable.el.appendChild(n),this.sortable.animateAll(),o&&o.animateAll()},drop:qt};re(ct,{pluginName:"revertOnSpill"});function dt(){}dt.prototype={onSpill:function(e){var n=e.dragEl,o=e.putSortable,r=o||this.sortable;r.captureAnimationState(),n.parentNode&&n.parentNode.removeChild(n),r.animateAll()},drop:qt};re(dt,{pluginName:"removeOnSpill"});p.mount(new kn);p.mount(dt,ct);function Hn(t){return t==null?t:JSON.parse(JSON.stringify(t))}function Ln(t){D.getCurrentInstance()&&D.onUnmounted(t)}function Wn(t){D.getCurrentInstance()?D.onMounted(t):D.nextTick(t)}let Kt=null,Jt=null;function Pt(t=null,e=null){Kt=t,Jt=e}function Gn(){return{data:Kt,clonedData:Jt}}const Mt=Symbol("cloneElement");function ht(...t){var z,Q;const e=(z=D.getCurrentInstance())==null?void 0:z.proxy;let n=null;const o=t[0];let[,r,i]=t;Array.isArray(D.unref(r))||(i=r,r=null);let a=null;const{immediate:l=!0,clone:s=Hn,customUpdate:u}=(Q=D.unref(i))!=null?Q:{};function d(v){var X;const{from:w,oldIndex:N,item:P}=v;n=Array.from(w.childNodes);const T=D.unref((X=D.unref(r))==null?void 0:X[N]),C=s(T);Pt(T,C),P[Mt]=C}function c(v){const w=v.item[Mt];if(!an(w)){if($e(v.item),D.isRef(r)){const N=[...D.unref(r)];r.value=wt(N,v.newDraggableIndex,w);return}wt(D.unref(r),v.newDraggableIndex,w)}}function m(v){const{from:w,item:N,oldIndex:P,oldDraggableIndex:T,pullMode:C,clone:X}=v;if(Et(w,N,P),C==="clone"){$e(X);return}if(D.isRef(r)){const B=[...D.unref(r)];r.value=yt(B,T);return}yt(D.unref(r),T)}function y(v){if(u){u(v);return}const{from:w,item:N,oldIndex:P,oldDraggableIndex:T,newDraggableIndex:C}=v;if($e(N),Et(w,N,P),D.isRef(r)){const X=[...D.unref(r)];r.value=bt(X,T,C);return}bt(D.unref(r),T,C)}function b(v){const{newIndex:w,oldIndex:N,from:P,to:T}=v;let C=null;const X=w===N&&P===T;try{if(X){let B=null;n==null||n.some((U,ae)=>{if(B&&(n==null?void 0:n.length)!==T.childNodes.length)return P.insertBefore(B,U.nextSibling),!0;const fe=T.childNodes[ae];B=T==null?void 0:T.replaceChild(U,fe)})}}catch(B){C=B}finally{n=null}D.nextTick(()=>{if(Pt(),C)throw C})}const E={onUpdate:y,onStart:d,onAdd:c,onRemove:m,onEnd:b};function H(v){const w=D.unref(o);return v||(v=ln(w)?sn(w,e==null?void 0:e.$el):w),v&&!cn(v)&&(v=v.$el),v||nn("Root element not found"),v}function L(){var P;const T=(P=D.unref(i))!=null?P:{},{immediate:v,clone:w}=T,N=Ve(T,["immediate","clone"]);return St(N,(C,X)=>{dn(C)&&(N[C]=(B,...U)=>{const ae=Gn();return hn(B,ae),X(B,...U)})}),fn(r===null?{}:E,N)}const R=v=>{v=H(v),a&&x.destroy(),a=new p(v,L())};D.watch(()=>i,()=>{a&&St(L(),(v,w)=>{a==null||a.option(v,w)})},{deep:!0});const x={option:(v,w)=>a==null?void 0:a.option(v,w),destroy:()=>{a==null||a.destroy(),a=null},save:()=>a==null?void 0:a.save(),toArray:()=>a==null?void 0:a.toArray(),closest:(...v)=>a==null?void 0:a.closest(...v)},W=()=>x==null?void 0:x.option("disabled",!0),ne=()=>x==null?void 0:x.option("disabled",!1);return Wn(()=>{l&&R()}),Ln(x.destroy),ce({start:R,pause:W,resume:ne},x)}const ut=["update","start","add","remove","choose","unchoose","end","sort","filter","clone","move","change"],jn=["clone","animation","ghostClass","group","sort","disabled","store","handle","draggable","swapThreshold","invertSwap","invertedSwapThreshold","removeCloneOnHide","direction","chosenClass","dragClass","ignore","filter","preventOnFilter","easing","setData","dropBubble","dragoverBubble","dataIdAttr","delay","delayOnTouchOnly","touchStartThreshold","forceFallback","fallbackClass","fallbackOnBody","fallbackTolerance","fallbackOffset","supportPointer","emptyInsertThreshold","scroll","forceAutoScrollFallback","scrollSensitivity","scrollSpeed","bubbleScroll","modelValue","tag","target","customUpdate",...ut.map(t=>`on${t.replace(/^\S/,e=>e.toUpperCase())}`)],zn=D.defineComponent({name:"VueDraggable",model:{prop:"modelValue",event:"update:modelValue"},props:jn,emits:["update:modelValue",...ut],setup(t,{slots:e,emit:n,expose:o,attrs:r}){const i=ut.reduce((d,c)=>{const m=`on${c.replace(/^\S/,y=>y.toUpperCase())}`;return d[m]=(...y)=>n(c,...y),d},{}),a=D.computed(()=>{const y=D.toRefs(t),{modelValue:d}=y,c=Ve(y,["modelValue"]),m=Object.entries(c).reduce((b,[E,H])=>{const L=D.unref(H);return L!==void 0&&(b[E]=L),b},{});return ce(ce({},i),rn(ce(ce({},r),m)))}),l=D.computed({get:()=>t.modelValue,set:d=>n("update:modelValue",d)}),s=D.ref(),u=D.reactive(ht(t.target||s,l,a));return o(u),()=>{var d;return D.h(t.tag||"div",{ref:s},(d=e==null?void 0:e.default)==null?void 0:d.call(e,u))}}}),Ft={mounted:"mounted",unmounted:"unmounted"},rt=new WeakMap,Un={[Ft.mounted](t,e){const n=D.isProxy(e.value)?[e.value]:e.value,[o,r]=n,i=ht(t,o,r);rt.set(t,i.destroy)},[Ft.unmounted](t){var e;(e=rt.get(t))==null||e(),rt.delete(t)}};exports.VueDraggable=zn;exports.useDraggable=ht;exports.vDraggable=Un;
