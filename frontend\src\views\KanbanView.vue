<template>
  <div class="kanban-view">
    <div class="page-header">任务看板</div>
    
    <!-- 看板过滤器 -->
    <el-card shadow="hover" class="filter-card">
      <div class="filters">
        <el-select v-model="filters.type" placeholder="任务类型" clearable style="width: 150px;">
          <el-option label="全部类型" value=""></el-option>
          <el-option label="日常任务" value="daily"></el-option>
          <el-option label="周期任务" value="periodic"></el-option>
          <el-option label="PDCA任务" value="pdca"></el-option>
        </el-select>
        <el-select v-model="filters.assignee" placeholder="负责人" clearable style="width: 150px;">
          <el-option label="全部成员" value=""></el-option>
          <el-option v-for="member in teamMembers" :key="member.id" :label="member.name" :value="member.id"></el-option>
        </el-select>
        <el-button type="primary" @click="applyFilters">筛选</el-button>
        <el-button @click="resetFilters">重置</el-button>
      </div>
    </el-card>
    
    <!-- 看板区域 -->
    <div class="kanban-container">
      <div 
        v-for="status in kanbanStatuses" 
        :key="status.value" 
        class="kanban-column"
      >
        <div class="kanban-column-header">
          <span>{{ status.label }}</span>
          <el-tag type="info" size="small" round>{{ tasksByStatus[status.value]?.length || 0 }}</el-tag>
        </div>
        
        <draggable
          v-model="tasksByStatus[status.value]"
          :group="{ name: 'tasks', pull: true, put: true }"
          item-key="id"
          ghost-class="ghost-card"
          chosen-class="chosen-card"
          animation="300"
          @end="onDragEnd($event, status.value)"
        >
          <template #item="{ element }">
            <div class="kanban-card" @click="viewTaskDetail(element.id)">
              <div class="kanban-card-header">
                <div class="card-title" :class="{ 'mentioned': element.mentioned }">{{ element.title }}</div>
                <el-tag :type="getTaskTypeStyle(element.type).type" size="small">{{ getTaskTypeStyle(element.type).label }}</el-tag>
              </div>
              
              <div class="card-desc">{{ element.description }}</div>
              
              <div class="kanban-card-footer">
                <div class="due-date">
                  <el-icon><Calendar /></el-icon>
                  <span :class="{ 'overdue': isOverdue(element.dueDate) && status.value !== 'completed' }">
                    {{ element.dueDate }}
                  </span>
                </div>
                
                <div class="assignees">
                  <el-avatar-group :max="2" size="small">
                    <el-tooltip v-for="userId in element.assignees" :key="userId" :content="getUserById(userId)?.name" placement="top">
                      <el-avatar :size="24" :src="getUserById(userId)?.avatar" />
                    </el-tooltip>
                  </el-avatar-group>
                </div>
              </div>
            </div>
          </template>
        </draggable>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { Calendar } from '@element-plus/icons-vue'
import draggable from 'vuedraggable'

const router = useRouter()

// 团队成员
const teamMembers = reactive([
  { id: 'user1', name: '张三', avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png' },
  { id: 'user2', name: '李四', avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png' },
  { id: 'user3', name: '王五', avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png' },
  { id: 'user4', name: '赵六', avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png' }
])

// 看板状态
const kanbanStatuses = reactive([
  { label: '待处理', value: 'unstarted' },
  { label: '进行中', value: 'in-progress' },
  { label: '待审核', value: 'review' },
  { label: '已完成', value: 'completed' }
])

// 过滤器
const filters = reactive({
  type: '',
  assignee: ''
})

// 任务数据
const allTasks = reactive([
  {
    id: 'task1',
    title: '优化用户注册流程',
    description: '重新设计注册表单，简化注册步骤，提高转化率',
    type: 'daily',
    status: 'unstarted',
    priority: 'high',
    assignees: ['user1', 'user2'],
    createDate: '2024-05-01',
    dueDate: '2024-05-10',
    mentioned: true
  },
  {
    id: 'task2',
    title: '修复移动端显示问题',
    description: '解决在小屏设备上页面布局错乱的问题',
    type: 'daily',
    status: 'in-progress',
    priority: 'medium',
    assignees: ['user1'],
    createDate: '2024-05-02',
    dueDate: '2024-05-12',
    mentioned: false
  },
  {
    id: 'task3',
    title: '完成系统架构文档',
    description: '详细描述系统架构、模块划分和技术选型',
    type: 'daily',
    status: 'in-progress',
    priority: 'low',
    assignees: ['user3'],
    createDate: '2024-05-03',
    dueDate: '2024-05-15',
    mentioned: false
  },
  {
    id: 'task4',
    title: '实现数据导出功能',
    description: '支持将任务数据导出为Excel和PDF格式',
    type: 'daily',
    status: 'completed',
    priority: 'medium',
    assignees: ['user1', 'user4'],
    createDate: '2024-04-20',
    dueDate: '2024-04-30',
    mentioned: false
  },
  {
    id: 'task5',
    title: '每周项目进度汇报',
    description: '整理项目进展，准备周会汇报材料',
    type: 'periodic',
    status: 'unstarted',
    priority: 'high',
    assignees: ['user1'],
    createDate: '2024-05-03',
    dueDate: '2024-05-07',
    mentioned: false
  },
  {
    id: 'task6',
    title: '优化搜索算法',
    description: '提高搜索准确性和响应速度',
    type: 'pdca',
    status: 'review',
    priority: 'high',
    assignees: ['user3', 'user4'],
    createDate: '2024-04-28',
    dueDate: '2024-05-15',
    mentioned: false
  }
])

// 过滤后的任务
const filteredTasks = computed(() => {
  return allTasks.filter(task => {
    if (filters.type && task.type !== filters.type) return false
    if (filters.assignee && !task.assignees.includes(filters.assignee)) return false
    return true
  })
})

// 按状态分组的任务
const tasksByStatus = computed(() => {
  const result: Record<string, any[]> = {}
  
  // 初始化每个状态分组为空数组
  kanbanStatuses.forEach(status => {
    result[status.value] = []
  })
  
  // 根据任务状态分组
  filteredTasks.value.forEach(task => {
    if (result[task.status]) {
      result[task.status].push({ ...task })
    }
  })
  
  return result
})

// 方法
function getUserById(userId: string) {
  return teamMembers.find(user => user.id === userId)
}

function getTaskTypeStyle(type: string) {
  switch (type) {
    case 'daily':
      return { label: '日常', type: 'info' }
    case 'periodic':
      return { label: '周期', type: 'success' }
    case 'pdca':
      return { label: 'PDCA', type: 'warning' }
    default:
      return { label: '未知', type: 'info' }
  }
}

function isOverdue(dueDate: string) {
  const today = new Date()
  const due = new Date(dueDate)
  return due < today
}

function viewTaskDetail(taskId: string) {
  router.push({ name: 'taskDetail', params: { id: taskId } })
}

function onDragEnd(event: any, newStatus: string) {
  // 获取拖拽的任务
  const taskId = event.item.getAttribute('data-id')
  if (!taskId) return
  
  // 更新任务状态
  const task = allTasks.find(t => t.id === taskId)
  if (task) {
    task.status = newStatus
    console.log(`任务 "${task.title}" 状态已更新为 ${newStatus}`)
  }
}

function applyFilters() {
  console.log('应用过滤器', filters)
}

function resetFilters() {
  filters.type = ''
  filters.assignee = ''
}
</script>

<style scoped>
.page-header {
  font-size: 20px;
  font-weight: 500;
  margin-bottom: 20px;
}

.filter-card {
  margin-bottom: 20px;
  background-color: rgba(26, 86, 219, 0.02);
}

.filters {
  display: flex;
  gap: 15px;
}

.kanban-container {
  display: flex;
  gap: 16px;
  overflow-x: auto;
  padding-bottom: 10px;
  min-height: calc(100vh - 220px);
}

.kanban-column {
  flex: 0 0 300px;
  background: rgba(26, 86, 219, 0.05);
  border-radius: 8px;
  padding: 10px;
  max-height: calc(100vh - 220px);
  overflow-y: auto;
}

.kanban-column-header {
  font-weight: bold;
  color: var(--app-primary-color);
  margin-bottom: 10px;
  padding: 8px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.kanban-card {
  background: white;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 10px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  border-left: 3px solid var(--app-primary-color);
  transition: all 0.3s;
}

.kanban-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
}

.kanban-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.card-title {
  font-weight: 500;
  word-break: break-word;
  flex: 1;
  margin-right: 8px;
}

.mentioned {
  color: var(--app-accent-color);
}

.card-desc {
  font-size: 13px;
  color: rgba(26, 86, 219, 0.65);
  margin-bottom: 10px;
  word-break: break-word;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.kanban-card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: rgba(63, 131, 248, 0.8);
}

.due-date {
  display: flex;
  align-items: center;
  gap: 4px;
}

.overdue {
  color: var(--el-color-danger);
  font-weight: bold;
}

/* 更新任务类型标签样式 */
:deep(.el-tag--info) {
  background-color: rgba(26, 86, 219, 0.1);
  border-color: rgba(26, 86, 219, 0.2);
  color: var(--app-primary-color);
}

:deep(.el-tag--success) {
  background-color: rgba(16, 185, 129, 0.1);
  border-color: rgba(16, 185, 129, 0.2);
  color: var(--app-success-color);
}

:deep(.el-tag--warning) {
  background-color: rgba(214, 158, 46, 0.1);
  border-color: rgba(214, 158, 46, 0.2);
  color: var(--app-accent-color);
}

/* 拖拽相关样式 */
.ghost-card {
  opacity: 0.5;
  background: rgba(26, 86, 219, 0.1);
  border: 2px dashed var(--app-primary-color);
}

.chosen-card {
  transform: rotate(2deg);
  box-shadow: 0 0 10px rgba(26, 86, 219, 0.3);
}

/* 更新按钮样式 */
:deep(.el-button--primary) {
  background-color: var(--app-primary-color);
  border-color: var(--app-primary-color);
}

:deep(.el-button--primary:hover) {
  background-color: var(--app-secondary-color);
  border-color: var(--app-secondary-color);
}

/* 响应式布局 */
@media (max-width: 768px) {
  .filters {
    flex-direction: column;
  }
}
</style> 