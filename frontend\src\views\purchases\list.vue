/**
 * 航空航天级IT资产管理系统 - 采购列表页面
 * 文件路径: src/views/purchases/list.vue
 * 功能描述: 展示和管理IT资产采购信息
 */

<template>
  <div class="purchase-list-container">
    <!-- 页面标题和操作按钮 -->
    <div class="page-header">
      <h2 class="page-title">采购列表</h2>
      <div class="page-actions">
        <el-button type="primary" @click="handleAddPurchase" :icon="Plus">
          新建采购
        </el-button>
        <el-button type="primary" @click="handleExportData" :icon="Download">
          导出数据
        </el-button>
      </div>
    </div>

    <!-- 筛选条件 -->
    <el-card class="filter-card">
      <div class="filter-container">
        <el-form :inline="true" :model="filterForm" class="filter-form">
          <el-form-item label="采购单号">
            <el-input v-model="filterForm.code" placeholder="采购单号" clearable />
          </el-form-item>
          <el-form-item label="采购名称">
            <el-input 
              v-model="filterForm.name" 
              placeholder="采购名称" 
              clearable 
            />
          </el-form-item>
          <el-form-item label="采购类型">
            <el-select 
              v-model="filterForm.type" 
              placeholder="全部类型" 
              clearable
            >
              <el-option 
                v-for="item in typeOptions" 
                :key="item.value" 
                :label="item.label" 
                :value="item.value" 
              />
            </el-select>
          </el-form-item>
          <el-form-item label="状态">
            <el-select 
              v-model="filterForm.status" 
              placeholder="全部状态" 
              clearable
            >
              <el-option 
                v-for="item in statusOptions" 
                :key="item.value" 
                :label="item.label" 
                :value="item.value" 
              />
            </el-select>
          </el-form-item>
          <el-form-item label="采购时间">
            <el-date-picker
              v-model="filterForm.timeRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch" :icon="Search">
              搜索
            </el-button>
            <el-button @click="resetFilter" :icon="RefreshRight">
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="data-card">
      <el-tabs v-model="activeTab" @tab-click="handleTabChange">
        <el-tab-pane label="全部" name="all"></el-tab-pane>
        <el-tab-pane label="待审批" name="pending"></el-tab-pane>
        <el-tab-pane label="待采购" name="approved"></el-tab-pane>
        <el-tab-pane label="已采购" name="purchased"></el-tab-pane>
        <el-tab-pane label="已入库" name="received"></el-tab-pane>
      </el-tabs>
      
      <el-table
        ref="purchaseTable"
        v-loading="loading"
        :data="purchaseList"
        border
        style="width: 100%"
        :expand-row-keys="expandedRows"
        row-key="id"
        @expand-change="handleExpandChange"
      >
        <!-- 展开行模板 -->
        <el-table-column type="expand">
          <template #default="{ row }">
            <div class="expanded-content">
              <div class="expanded-header">
                <h4>采购物品明细</h4>
                <div class="header-info">
                  <el-tag size="small" type="info">共 {{ row.itemCount || 0 }} 种物品</el-tag>
                  <el-tag size="small" type="success">总计 {{ row.totalQuantity || 0 }} 件</el-tag>
                  <el-tag size="small" type="warning">金额 ¥{{ formatAmount(row.totalAmount || 0) }}</el-tag>
                </div>
              </div>

              <div v-loading="expandLoading[row.id]" class="items-detail">
                <el-table
                  :data="expandedItemsData[row.id] || []"
                  size="small"
                  border
                  style="margin: 10px 0;"
                  :max-height="400"
                >
                  <el-table-column type="index" label="序号" width="60" align="center" />
                  <el-table-column prop="itemName" label="物品名称" min-width="180" show-overflow-tooltip>
                    <template #default="{ row: item }">
                      <div class="item-detail">
                        <div class="item-name">{{ item.itemName || item.name || '-' }}</div>
                        <div v-if="item.itemCode || item.materialNumber" class="item-code">
                          编号: {{ item.itemCode || item.materialNumber }}
                        </div>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="specification" label="规格型号" min-width="140" show-overflow-tooltip>
                    <template #default="{ row: item }">
                      <span :title="item.specification">{{ item.specification || '-' }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="quantity" label="数量" width="80" align="center">
                    <template #default="{ row: item }">
                      <el-tag size="small" type="primary">{{ item.quantity || 0 }}</el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="unitPrice" label="单价" width="100" align="right">
                    <template #default="{ row: item }">
                      ¥{{ formatAmount(item.unitPrice || 0) }}
                    </template>
                  </el-table-column>
                  <el-table-column label="小计" width="120" align="right">
                    <template #default="{ row: item }">
                      <span class="subtotal">¥{{ formatAmount((item.unitPrice || 0) * (item.quantity || 0)) }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="notes" label="备注" min-width="120" show-overflow-tooltip>
                    <template #default="{ row: item }">
                      <span :title="item.notes">{{ item.notes || '-' }}</span>
                    </template>
                  </el-table-column>
                </el-table>

                <!-- 如果没有数据显示提示 -->
                <div v-if="!expandedItemsData[row.id] || expandedItemsData[row.id].length === 0" class="no-items">
                  <el-empty description="暂无物品明细数据" :image-size="60" />
                </div>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="orderCode" label="采购单号" width="150" sortable />
        <el-table-column label="物料编号" width="120" show-overflow-tooltip>
          <template #default="scope">
            {{ scope.row.primaryItemCode || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="采购物品" min-width="280" show-overflow-tooltip>
          <template #default="scope">
            <div class="purchase-items-summary">
              <!-- 主要物品信息 -->
              <div class="primary-item">
                <div class="item-name-row">
                  <span class="item-name">{{ scope.row.primaryItemName || scope.row.name || '-' }}</span>
                  <span v-if="scope.row.primaryItemCode" class="item-code">
                    ({{ scope.row.primaryItemCode }})
                  </span>
                </div>
                <div v-if="scope.row.primaryItemQuantity" class="item-quantity">
                  数量: {{ scope.row.primaryItemQuantity }}
                </div>
              </div>

              <!-- 物品统计和概览 -->
              <div class="items-stats">
                <div class="stats-row">
                  <el-tag size="small" type="info">
                    {{ scope.row.itemCount || 1 }}种物品
                  </el-tag>
                  <el-tag size="small" type="primary">
                    共{{ scope.row.totalQuantity || 0 }}件
                  </el-tag>
                </div>

                <!-- 显示前3个物品的名称和数量 -->
                <div v-if="scope.row.itemsSummary" class="items-preview">
                  <div
                    v-for="(item, index) in scope.row.itemsSummary.slice(0, 3)"
                    :key="index"
                    class="preview-item"
                  >
                    <span class="preview-name">{{ item.name }}</span>
                    <span class="preview-quantity">×{{ item.quantity }}</span>
                  </div>
                  <span v-if="scope.row.itemCount > 3" class="more-items">
                    +{{ scope.row.itemCount - 3 }}种其他物品
                  </span>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="类型" width="100" show-overflow-tooltip />
        <el-table-column prop="status" label="状态" width="120">
          <template #default="scope">
            <el-tag :type="getStatusTag(scope.row.status)" size="small">
              {{ scope.row.statusName }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="totalAmount" label="金额" width="120">
          <template #default="scope">
            ￥{{ (scope.row.totalAmount || 0).toLocaleString() }}
          </template>
        </el-table-column>
        <el-table-column label="数量" width="80" align="center">
          <template #default="scope">
            {{ scope.row.totalQuantity || 0 }}
          </template>
        </el-table-column>
        <el-table-column prop="applicantName" label="申请人" width="100" />
        <el-table-column prop="applicationTime" label="申请时间" width="180" sortable>
          <template #default="scope">
            {{ formatDateTime(scope.row.applicationTime) }}
          </template>
        </el-table-column>
        <el-table-column label="审批人" width="100">
          <template #default="scope">
            {{ scope.row.approverName || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间" width="180" sortable>
          <template #default="scope">
            {{ formatDateTime(scope.row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column prop="supplierName" label="供应商" width="150" show-overflow-tooltip />
        <el-table-column label="入库时间" width="180" sortable>
          <template #default="scope">
            {{ scope.row.actualDeliveryDate ? formatDateTime(scope.row.actualDeliveryDate) : '-' }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="220" fixed="right">
          <template #default="scope">
            <el-button 
              type="primary" 
              text 
              size="small" 
              @click="handleViewDetail(scope.row)"
              :icon="View"
            >
              详情
            </el-button>
            <template v-if="scope.row.status === 'pending'">
              <el-button 
                type="success" 
                text 
                size="small" 
                @click="handleApprove(scope.row)"
                :icon="Check"
              >
                审批
              </el-button>
              <el-button 
                type="danger" 
                text 
                size="small" 
                @click="handleReject(scope.row)"
                :icon="Close"
              >
                拒绝
              </el-button>
            </template>
            <template v-if="scope.row.status === 'approved'">
              <el-button 
                type="success" 
                text 
                size="small" 
                @click="handlePurchaseComplete(scope.row)"
                :icon="ShoppingCart"
              >
                完成采购
              </el-button>
            </template>
            <template v-if="scope.row.status === 'purchased'">
              <el-button 
                type="warning" 
                text 
                size="small" 
                @click="handleReceive(scope.row)"
                :icon="Box"
              >
                入库
              </el-button>
            </template>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :background="true"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 入库转化对话框 -->
    <el-dialog
      v-model="receiveDialogVisible"
      title="采购物品入库转化"
      width="80%"
      :close-on-click-modal="false"
    >
      <div class="receive-dialog-content">
        <div class="order-info">
          <h4>采购单信息</h4>
          <p><strong>采购单号：</strong>{{ currentReceiveOrder?.code }}</p>
          <p><strong>采购名称：</strong>{{ currentReceiveOrder?.name }}</p>
          <p><strong>供应商：</strong>{{ currentReceiveOrder?.vendor }}</p>
        </div>

        <div class="items-conversion">
          <h4>物品转化设置</h4>
          <el-table :data="receiveForm.items" border style="width: 100%">
            <el-table-column prop="name" label="物品名称" width="150" />
            <el-table-column prop="model" label="型号规格" width="150" />
            <el-table-column prop="quantity" label="采购数量" width="100" align="center" />
            <el-table-column label="转为资产" width="200" align="center">
              <template #default="scope">
                <el-input-number
                  v-model="scope.row.toAssetQuantity"
                  :min="0"
                  :max="scope.row.quantity"
                  size="small"
                  style="width: 80px"
                />
                <br />
                <el-select
                  v-model="scope.row.assetLocationId"
                  placeholder="选择位置"
                  size="small"
                  style="width: 100%; margin-top: 5px"
                  :disabled="scope.row.toAssetQuantity === 0"
                >
                  <el-option label="办公室A" :value="1" />
                  <el-option label="办公室B" :value="2" />
                  <el-option label="会议室" :value="3" />
                  <el-option label="机房" :value="4" />
                </el-select>
              </template>
            </el-table-column>
            <el-table-column label="转为备件" width="200" align="center">
              <template #default="scope">
                <el-input-number
                  v-model="scope.row.toSparePartQuantity"
                  :min="0"
                  :max="scope.row.quantity"
                  size="small"
                  style="width: 80px"
                />
                <br />
                <el-select
                  v-model="scope.row.sparePartLocationId"
                  placeholder="选择库位"
                  size="small"
                  style="width: 100%; margin-top: 5px"
                  :disabled="scope.row.toSparePartQuantity === 0"
                >
                  <el-option label="备件库A区" :value="1" />
                  <el-option label="备件库B区" :value="2" />
                  <el-option label="备件库C区" :value="3" />
                </el-select>
              </template>
            </el-table-column>
            <el-table-column label="剩余数量" width="100" align="center">
              <template #default="scope">
                <span :class="{ 'text-danger': scope.row.quantity - scope.row.toAssetQuantity - scope.row.toSparePartQuantity !== 0 }">
                  {{ scope.row.quantity - scope.row.toAssetQuantity - scope.row.toSparePartQuantity }}
                </span>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <div class="conversion-tips">
          <el-alert
            title="转化说明"
            type="info"
            :closable="false"
            show-icon
          >
            <ul>
              <li>每个物品的转化数量总和必须等于采购数量</li>
              <li>转为资产：物品将作为固定资产进行管理，生成资产编号</li>
              <li>转为备件：物品将进入备品备件库存，用于维修和更换</li>
              <li>可以同时转化为资产和备件，按需分配数量</li>
            </ul>
          </el-alert>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="receiveDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmReceive">确认入库</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 新建采购对话框 -->
    <el-dialog
      v-model="createDialogVisible"
      title="新建采购"
      width="800px"
      :close-on-click-modal="false"
      @close="resetCreateForm"
    >
      <el-form
        ref="createFormRef"
        :model="createForm"
        :rules="createRules"
        label-width="120px"
        label-position="right"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="采购名称" prop="name">
              <el-input
                v-model="createForm.name"
                placeholder="请输入采购名称"
                maxlength="100"
                show-word-limit
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="采购类型" prop="type">
              <el-select v-model="createForm.type" placeholder="请选择采购类型" style="width: 100%">
                <el-option label="设备采购" value="equipment" />
                <el-option label="软件采购" value="software" />
                <el-option label="服务采购" value="service" />
                <el-option label="备件采购" value="spare_parts" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="供应商" prop="supplierId">
              <el-select
                v-model="createForm.supplierId"
                placeholder="请选择供应商"
                style="width: 100%"
                filterable
                allow-create
                @change="handleSupplierChange"
              >
                <el-option
                  v-for="supplier in suppliers"
                  :key="supplier.id"
                  :label="supplier.name"
                  :value="supplier.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="预计金额" prop="estimatedAmount">
              <el-input-number
                v-model="createForm.estimatedAmount"
                :min="0"
                :precision="2"
                placeholder="请输入预计金额"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="采购说明" prop="description">
          <el-input
            v-model="createForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入采购说明"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="采购物品">
          <div class="purchase-items">
            <div
              v-for="(item, index) in createForm.items"
              :key="index"
              class="purchase-item"
            >
              <!-- 优化后的采购物品布局 -->
              <div class="purchase-item-card" style="border: 1px solid #e4e7ed; border-radius: 6px; padding: 16px; margin-bottom: 12px; background-color: #fafafa;">
                <!-- 第一行：选择备件和物品名称 -->
                <el-row :gutter="12" style="margin-bottom: 12px;">
                  <el-col :span="10">
                    <label class="item-label">选择备件：</label>
                    <el-select
                      v-model="item.sparePartId"
                      placeholder="选择备件"
                      filterable
                      remote
                      :remote-method="(query) => searchSpareParts(query, index)"
                      :loading="sparePartsLoading"
                      @change="(value) => handleSparePartSelect(value, index)"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="sparePart in sparePartOptions"
                        :key="sparePart.id"
                        :label="`${sparePart.materialNumber || sparePart.code} - ${sparePart.name}`"
                        :value="sparePart.id"
                      />
                    </el-select>
                  </el-col>
                  <el-col :span="10">
                    <label class="item-label">物品名称：</label>
                    <el-input
                      v-model="item.name"
                      placeholder="物品名称"
                      maxlength="100"
                    />
                  </el-col>
                  <el-col :span="4" style="display: flex; align-items: end;">
                    <el-button
                      type="danger"
                      size="small"
                      :icon="Close"
                      @click="removeItem(index)"
                      :disabled="createForm.items.length <= 1"
                      style="width: 100%"
                    >
                      删除
                    </el-button>
                  </el-col>
                </el-row>

                <!-- 第二行：物料编号和规格型号 -->
                <el-row :gutter="12" style="margin-bottom: 12px;">
                  <el-col :span="8">
                    <label class="item-label">物料编号：</label>
                    <el-input
                      v-model="item.materialNumber"
                      placeholder="物料编号"
                      readonly
                      style="background-color: #f5f7fa;"
                    />
                  </el-col>
                  <el-col :span="16">
                    <label class="item-label">规格型号：</label>
                    <el-input
                      v-model="item.specification"
                      placeholder="规格型号"
                      maxlength="200"
                    />
                  </el-col>
                </el-row>

                <!-- 第三行：数量、单价和备注 -->
                <el-row :gutter="12">
                  <el-col :span="6">
                    <label class="item-label">数量：</label>
                    <el-input-number
                      v-model="item.quantity"
                      :min="1"
                      placeholder="数量"
                      style="width: 100%"
                    />
                  </el-col>
                  <el-col :span="6">
                    <label class="item-label">单价：</label>
                    <el-input-number
                      v-model="item.unitPrice"
                      :min="0"
                      :precision="2"
                      placeholder="单价"
                      style="width: 100%"
                    />
                  </el-col>
                  <el-col :span="12">
                    <label class="item-label">备注：</label>
                    <el-input
                      v-model="item.notes"
                      placeholder="备注"
                      maxlength="200"
                    />
                  </el-col>
                </el-row>
              </div>
            </div>
            <el-button
              type="primary"
              size="small"
              :icon="Plus"
              @click="addItem"
              style="margin-top: 10px"
            >
              添加物品
            </el-button>
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="createDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitCreateForm" :loading="createLoading">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 采购单详情对话框 -->
    <PurchaseDetailDialog
      v-model="detailDialogVisible"
      :purchase-id="currentPurchaseId"
      @close="detailDialogVisible = false"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import purchaseApi from '@/api/purchase'
import { getSparePartsSimple } from '@/api/spareparts'
import {
  Search, Plus, Download, View, Check, Close,
  ShoppingCart, Box, RefreshRight
} from '@element-plus/icons-vue'
import PurchaseDetailDialog from './components/PurchaseDetailDialog.vue'

// 数据加载状态
const loading = ref(false)
const activeTab = ref('all')

// 采购列表数据
const purchaseList = ref([])
const purchaseTable = ref(null)

// 供应商列表
const suppliers = ref([])

// 备件相关数据
const sparePartOptions = ref([])
const sparePartsLoading = ref(false)

// 采购单详情对话框
const detailDialogVisible = ref(false)
const currentPurchaseId = ref(null)

// 展开行相关数据
const expandedRows = ref([])
const expandedItemsData = ref({})
const expandLoading = ref({})

// 获取供应商列表
const fetchSuppliers = async () => {
  try {
    // 使用新的采购供应商API
    const { getProcurementSuppliers } = await import('@/api/spareparts')
    const response = await getProcurementSuppliers()
    if (response.success) {
      suppliers.value = response.data || []
    } else {
      // 回退到原有API
      const fallbackResponse = await purchaseApi.getSuppliersV2()
      if (fallbackResponse.success) {
        suppliers.value = fallbackResponse.data || []
      } else {
        // 使用默认供应商列表
        suppliers.value = [
          { id: 1, name: '联想' },
          { id: 2, name: '惠普' },
          { id: 3, name: '戴尔' },
          { id: 4, name: '华为' }
        ]
      }
    }

    // 设置默认供应商（选择第一个）
    if (suppliers.value.length > 0 && !createForm.supplierId) {
      createForm.supplierId = suppliers.value[0].id
    }
  } catch (error) {
    console.error('获取采购供应商列表失败:', error)
    // 使用默认供应商列表
    suppliers.value = [
      { id: 1, name: '联想' },
      { id: 2, name: '惠普' },
      { id: 3, name: '戴尔' },
      { id: 4, name: '华为' }
    ]

    // 设置默认供应商
    if (suppliers.value.length > 0 && !createForm.supplierId) {
      createForm.supplierId = suppliers.value[0].id
    }
  }
}

// 分页配置
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 筛选表单
const filterForm = reactive({
  code: '',
  name: '',
  type: '',
  status: '',
  timeRange: []
})

// 采购类型选项
const typeOptions = [
  { label: '新设备', value: 'new_device' },
  { label: '更换设备', value: 'replacement' },
  { label: '配件', value: 'accessories' },
  { label: '软件', value: 'software' },
  { label: '服务', value: 'service' }
]

// 状态选项
const statusOptions = [
  { label: '待审批', value: 'pending' },
  { label: '已审批', value: 'approved' },
  { label: '已采购', value: 'purchased' },
  { label: '已入库', value: 'received' },
  { label: '已拒绝', value: 'rejected' },
  { label: '已取消', value: 'cancelled' }
]

// 生命周期钩子
onMounted(() => {
  fetchPurchaseList()
  fetchSuppliers()
})

// 获取采购列表
const fetchPurchaseList = async () => {
  loading.value = true
  
  // 构建查询参数
  const params = {
    pageIndex: pagination.currentPage,
    pageSize: pagination.pageSize,
    orderCode: filterForm.code,
    title: filterForm.name,
    status: activeTab.value === 'all' ? filterForm.status : activeTab.value
  }
  
  try {
    // 调用真实API获取采购列表
    const response = await purchaseApi.getPurchaseList(params)

    if (response.success) {
      purchaseList.value = response.data || []
      pagination.total = response.pagination?.totalCount || 0
    } else {
      ElMessage.error(response.message || '获取采购列表失败')
      purchaseList.value = []
      pagination.total = 0
    }
  } catch (error) {
    console.error('获取采购列表失败:', error)
    ElMessage.error('获取采购列表失败')
    purchaseList.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

// Tab切换
const handleTabChange = () => {
  pagination.currentPage = 1
  fetchPurchaseList()
}

// 搜索
const handleSearch = () => {
  pagination.currentPage = 1
  fetchPurchaseList()
}

// 重置筛选条件
const resetFilter = () => {
  filterForm.code = ''
  filterForm.name = ''
  filterForm.type = ''
  filterForm.status = ''
  filterForm.timeRange = []
  
  pagination.currentPage = 1
  fetchPurchaseList()
}

// 分页事件
const handleSizeChange = (size) => {
  pagination.pageSize = size
  fetchPurchaseList()
}

const handleCurrentChange = (page) => {
  pagination.currentPage = page
  fetchPurchaseList()
}

// 展开行变化处理
const handleExpandChange = async (row, expandedRows) => {
  const isExpanded = expandedRows.some(expandedRow => expandedRow.id === row.id)

  if (isExpanded && !expandedItemsData.value[row.id]) {
    // 展开时加载详细数据
    await loadPurchaseItems(row.id)
  }
}

// 加载采购单物品详情
const loadPurchaseItems = async (purchaseId) => {
  if (expandedItemsData.value[purchaseId]) {
    return // 已经加载过了
  }

  expandLoading.value[purchaseId] = true

  try {
    console.log('加载采购单物品详情，ID:', purchaseId)
    const response = await purchaseApi.getPurchaseByIdV2(purchaseId)
    console.log('采购单详情响应:', response)

    if (response && response.success && response.data) {
      expandedItemsData.value[purchaseId] = response.data.items || []
      console.log('采购单物品加载成功:', expandedItemsData.value[purchaseId])
    } else {
      ElMessage.error(response?.message || '获取采购单物品详情失败')
      expandedItemsData.value[purchaseId] = []
    }
  } catch (error) {
    console.error('加载采购单物品详情失败:', error)
    ElMessage.error('加载采购单物品详情失败: ' + (error.message || '网络错误'))
    expandedItemsData.value[purchaseId] = []
  } finally {
    expandLoading.value[purchaseId] = false
  }
}

// 查看详情
const handleViewDetail = (row) => {
  console.log('查看采购单详情:', row)
  currentPurchaseId.value = row.id
  detailDialogVisible.value = true
}

// 审批采购
const handleApprove = (row) => {
  ElMessageBox.confirm(`确认审批通过采购单"${row.name}"吗？`, '审批确认', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      // 调用真实API审批采购
      const response = await purchaseApi.approvePurchase(row.id)

      if (response.success) {
        ElMessage.success('审批通过成功')
        // 刷新列表
        fetchPurchaseList()
      } else {
        ElMessage.error(response.message || '审批失败')
      }
    } catch (error) {
      console.error('审批失败:', error)
      ElMessage.error('审批失败')
    }
  }).catch(() => {
    // 取消操作
  })
}

// 拒绝采购
const handleReject = (row) => {
  ElMessageBox.prompt('请输入拒绝原因', '拒绝理由', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputType: 'textarea',
    inputPlaceholder: '请输入拒绝理由...'
  }).then(async ({ value }) => {
    if (!value) {
      ElMessage.warning('请输入拒绝理由')
      return
    }
    
    try {
      // 调用真实API拒绝采购
      const response = await purchaseApi.rejectPurchase(row.id, { reason: value })

      if (response.success) {
        ElMessage.success('已拒绝该采购申请')
        // 刷新列表
        fetchPurchaseList()
      } else {
        ElMessage.error(response.message || '拒绝失败')
      }
    } catch (error) {
      console.error('拒绝失败:', error)
      ElMessage.error('拒绝失败')
    }
  }).catch(() => {
    // 取消操作
  })
}

// 完成采购
const handlePurchaseComplete = (row) => {
  ElMessageBox.prompt('请输入供应商信息', '完成采购', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputPlaceholder: '请输入供应商名称'
  }).then(async ({ value }) => {
    if (!value) {
      ElMessage.warning('请输入供应商信息')
      return
    }
    
    try {
      // 调用真实API完成采购
      const response = await purchaseApi.completePurchase(row.id, { vendor: value })

      if (response.success) {
        ElMessage.success('采购完成')
        // 刷新列表
        fetchPurchaseList()
      } else {
        ElMessage.error(response.message || '完成采购失败')
      }
    } catch (error) {
      console.error('完成采购失败:', error)
      ElMessage.error('完成采购失败')
    }
  }).catch(() => {
    // 取消操作
  })
}

// 入库 - 支持转化为资产或备件
const handleReceive = (row) => {
  // 打开入库转化对话框
  showReceiveDialog(row)
}

// 显示入库转化对话框
const receiveDialogVisible = ref(false)
const currentReceiveOrder = ref(null)
const receiveForm = reactive({
  items: []
})

const showReceiveDialog = (row) => {
  currentReceiveOrder.value = row
  receiveForm.items = row.items.map(item => ({
    ...item,
    toAssetQuantity: 0,
    toSparePartQuantity: item.quantity,
    assetLocationId: null,
    sparePartLocationId: null
  }))
  receiveDialogVisible.value = true
}

// 确认入库转化
const confirmReceive = async () => {
  try {
    // 验证数据
    for (const item of receiveForm.items) {
      if (item.toAssetQuantity + item.toSparePartQuantity !== item.quantity) {
        ElMessage.warning(`${item.name} 的转化数量总和必须等于采购数量`)
        return
      }
      if (item.toAssetQuantity > 0 && !item.assetLocationId) {
        ElMessage.warning(`${item.name} 转为资产时必须选择资产位置`)
        return
      }
      if (item.toSparePartQuantity > 0 && !item.sparePartLocationId) {
        ElMessage.warning(`${item.name} 转为备件时必须选择备件库位`)
        return
      }
    }

    // 构建API请求数据
    const processData = receiveForm.items.map(item => ({
      purchaseItemId: item.id,
      toSparePartQuantity: item.toSparePartQuantity,
      sparePartLocationId: item.sparePartLocationId,
      toAssetQuantity: item.toAssetQuantity,
      assetLocationId: item.assetLocationId
    }))

    // 调用API处理转化
    const response = await purchaseApi.processDeliveredItems(currentReceiveOrder.value.id, processData)

    if (response.success) {
      ElMessage.success('入库转化成功')
      receiveDialogVisible.value = false
      fetchPurchaseList()
    } else {
      ElMessage.error(response.message || '入库转化失败')
    }
  } catch (error) {
    console.error('入库转化失败:', error)
    ElMessage.error('入库转化失败：' + error.message)
  }
}

// 导出数据
const handleExportData = () => {
  ElMessage.success('开始导出数据，请稍候...')
  // 实际项目中调用导出API
  // exportPurchaseList(filterForm).then(() => {
  //   ElMessage.success('导出成功')
  // })
}

// 新建采购
const handleAddPurchase = () => {
  showCreateDialog()
}

// 新建采购对话框相关
const createDialogVisible = ref(false)
const createLoading = ref(false)
const createFormRef = ref(null)
const createForm = reactive({
  name: '',
  type: 'equipment', // 设置默认值为设备采购
  supplierId: '', // 将在获取供应商列表后设置默认值
  estimatedAmount: 0,
  description: '',
  items: [
    {
      sparePartId: null,
      materialNumber: '',
      name: '',
      quantity: 1,
      unitPrice: 0,
      specification: '',
      notes: ''
    }
  ]
})

// 新建采购表单验证规则
const createRules = {
  name: [
    { required: true, message: '请输入采购名称', trigger: 'blur' },
    { min: 2, max: 100, message: '采购名称长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择采购类型', trigger: 'change' }
  ],
  supplierId: [
    { required: true, message: '请选择供应商', trigger: 'change' }
  ],
  estimatedAmount: [
    { required: true, message: '请输入预计金额', trigger: 'blur' },
    { type: 'number', min: 0, message: '预计金额必须大于等于0', trigger: 'blur' }
  ]
}

// 显示新建采购对话框
const showCreateDialog = () => {
  resetCreateForm()
  createDialogVisible.value = true
}

// 重置新建采购表单
const resetCreateForm = () => {
  createForm.name = ''
  createForm.type = 'equipment' // 重置时也设置默认值
  createForm.supplierId = suppliers.value.length > 0 ? suppliers.value[0].id : '' // 设置默认供应商
  createForm.estimatedAmount = 0
  createForm.description = ''
  createForm.items = [
    {
      sparePartId: null,
      materialNumber: '',
      name: '',
      quantity: 1,
      unitPrice: 0,
      specification: '',
      notes: ''
    }
  ]
  createFormRef.value?.clearValidate()
}

// 添加采购物品
const addItem = () => {
  createForm.items.push({
    sparePartId: null,
    materialNumber: '',
    name: '',
    quantity: 1,
    unitPrice: 0,
    specification: '',
    notes: ''
  })
}

// 删除采购物品
const removeItem = (index) => {
  if (createForm.items.length > 1) {
    createForm.items.splice(index, 1)
  }
}

// 搜索备件
const searchSpareParts = async (query, itemIndex) => {
  if (!query) {
    sparePartOptions.value = []
    return
  }

  sparePartsLoading.value = true
  try {
    const response = await getSparePartsSimple(query)
    if (response.success) {
      sparePartOptions.value = response.data || []
    } else {
      sparePartOptions.value = []
    }
  } catch (error) {
    console.error('搜索备件失败:', error)
    sparePartOptions.value = []
  } finally {
    sparePartsLoading.value = false
  }
}

// 选择备件
const handleSparePartSelect = (sparePartId, itemIndex) => {
  const selectedSparePart = sparePartOptions.value.find(sp => sp.id === sparePartId)
  if (selectedSparePart) {
    const item = createForm.items[itemIndex]
    item.materialNumber = selectedSparePart.materialNumber || selectedSparePart.code
    item.name = selectedSparePart.name
    item.specification = selectedSparePart.specification || ''
  }
}

// 供应商变化处理
const handleSupplierChange = (value) => {
  // 可以在这里处理供应商变化的逻辑
  console.log('选择的供应商:', value)
}

// 检测重复物品
const checkDuplicateItems = (items) => {
  const duplicateGroups = {}
  const duplicates = []

  items.forEach((item, index) => {
    // 生成唯一键：物品名称 + 规格 + 单价
    const key = `${item.name?.trim()}|${item.specification?.trim()}|${item.unitPrice}`
    if (!duplicateGroups[key]) {
      duplicateGroups[key] = []
    }
    duplicateGroups[key].push({ ...item, originalIndex: index })
  })

  Object.keys(duplicateGroups).forEach(key => {
    const group = duplicateGroups[key]
    if (group.length > 1) {
      duplicates.push({
        name: group[0].name,
        specification: group[0].specification,
        count: group.length,
        totalQuantity: group.reduce((sum, item) => sum + item.quantity, 0),
        items: group
      })
    }
  })

  return duplicates
}

// 提交新建采购表单
const submitCreateForm = async () => {
  try {
    await createFormRef.value?.validate()
  } catch (error) {
    return
  }

  // 验证采购物品
  const validItems = createForm.items.filter(item => item.name && item.quantity > 0)
  if (validItems.length === 0) {
    ElMessage.warning('请至少添加一个有效的采购物品')
    return
  }

  // 检测重复物品并提示用户
  const duplicates = checkDuplicateItems(validItems)
  if (duplicates.length > 0) {
    let message = '检测到重复物品，系统将自动合并：\n'
    duplicates.forEach(dup => {
      message += `• ${dup.name} (${dup.specification || '无规格'}) - ${dup.count}项合并为${dup.totalQuantity}个\n`
    })
    message += '\n是否继续提交？'

    try {
      await ElMessageBox.confirm(message, '重复物品提示', {
        confirmButtonText: '继续提交',
        cancelButtonText: '取消',
        type: 'warning',
        showClose: false
      })
    } catch {
      return // 用户取消
    }
  }

  createLoading.value = true

  try {
    // 构建提交数据，匹配后端API期望的数据结构
    const submitData = {
      supplierId: parseInt(createForm.supplierId),
      requesterId: 1, // 当前用户ID，实际应该从用户状态获取
      expectedDeliveryDate: null, // 可选字段
      notes: createForm.description,
      items: validItems.map(item => ({
        itemName: item.name,
        itemCode: item.materialNumber || '', // 物料编号
        specification: item.specification,
        assetTypeId: 1, // 默认资产类型ID，实际应该从选择的类型获取
        unitPrice: parseFloat(item.unitPrice),
        quantity: parseInt(item.quantity),
        notes: item.notes || ''
      }))
    }

    // 调用API创建采购订单
    const response = await purchaseApi.createPurchaseOrder(submitData)

    if (response.success) {
      ElMessage.success('采购订单创建成功')
      createDialogVisible.value = false
      fetchPurchaseList() // 刷新列表
    } else {
      ElMessage.error(response.message || '创建采购订单失败')
    }
  } catch (error) {
    console.error('创建采购订单失败:', error)
    ElMessage.error('创建采购订单失败')
  } finally {
    createLoading.value = false
  }
}

// 工具方法：获取类型标签样式
const getTypeTag = (type) => {
  const map = {
    'new_device': 'primary',
    'replacement': 'success',
    'accessories': 'info',
    'software': 'warning',
    'service': ''
  }
  return map[type] || ''
}

// 工具方法：获取类型文本
const getTypeLabel = (type) => {
  const map = {
    'new_device': '新设备',
    'replacement': '更换设备',
    'accessories': '配件',
    'software': '软件',
    'service': '服务'
  }
  return map[type] || '未知'
}

// 工具方法：获取状态标签样式
const getStatusTag = (status) => {
  const map = {
    'pending': 'info',
    'approved': 'success',
    'purchased': 'warning',
    'received': 'primary',
    'rejected': 'danger',
    'cancelled': 'danger'
  }
  return map[status] || ''
}

// 工具方法：获取状态文本
const getStatusLabel = (status) => {
  const map = {
    'pending': '待审批',
    'approved': '已审批',
    'purchased': '已采购',
    'received': '已入库',
    'rejected': '已拒绝',
    'cancelled': '已取消'
  }
  return map[status] || '未知'
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'

  // 处理默认日期值
  if (dateTime === '0001-01-01T00:00:00' || dateTime.startsWith('0001-01-01')) {
    return '-'
  }

  try {
    const date = new Date(dateTime)
    if (isNaN(date.getTime())) return '-'

    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch (error) {
    console.error('日期格式化错误:', error)
    return '-'
  }
}

// 格式化金额
const formatAmount = (amount) => {
  if (amount == null || amount === '') return '0.00'
  return Number(amount).toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
}
</script>

<style lang="scss" scoped>
@use '@/styles/variables.scss';

.purchase-list-container {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    
    .page-title {
      font-size: 20px;
      font-weight: 600;
      color: #303133;
      margin: 0;
    }
    
    .page-actions {
      display: flex;
      gap: 8px;
    }
  }
  
  .filter-card {
    margin-bottom: 16px;
    
    .filter-container {
      .filter-form {
        display: flex;
        flex-wrap: wrap;
      }
    }
  }
  
  .data-card {
    .pagination-container {
      margin-top: 16px;
      display: flex;
      justify-content: flex-end;
    }
  }
}

/* 入库转化对话框样式 */
.receive-dialog-content {
  .order-info {
    background: #f5f7fa;
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 20px;

    h4 {
      margin: 0 0 10px 0;
      color: #303133;
    }

    p {
      margin: 5px 0;
      color: #606266;
    }
  }

  .items-conversion {
    margin-bottom: 20px;

    h4 {
      margin: 0 0 15px 0;
      color: #303133;
    }
  }

  .conversion-tips {
    ul {
      margin: 0;
      padding-left: 20px;

      li {
        margin: 5px 0;
        color: #606266;
      }
    }
  }

  .text-danger {
    color: #f56c6c;
    font-weight: bold;
  }
}

/* 新建采购对话框样式 */
.purchase-items {
  .purchase-item {
    margin-bottom: 10px;
    padding: 10px;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    background-color: #fafafa;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

/* 物料数量徽章样式 */
.item-count-badge {
  display: inline-block;
  background: #409eff;
  color: white;
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 10px;
  margin-left: 8px;
  font-weight: normal;
}

/* 采购物品汇总样式 */
.purchase-items-summary {
  .primary-item {
    margin-bottom: 6px;

    .item-name {
      font-weight: 600;
      color: #303133;
    }

    .item-code {
      color: #909399;
      font-size: 12px;
      margin-left: 4px;
    }
  }

  .items-stats {
    display: flex;
    align-items: center;
    gap: 6px;
    flex-wrap: wrap;

    .more-items {
      color: #909399;
      font-size: 12px;
    }
  }
}

/* 展开行样式 */
.expanded-content {
  padding: 16px;
  background: #fafafa;
  border-radius: 4px;
  margin: 8px 0;

  .expanded-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid #e4e7ed;

    h4 {
      margin: 0;
      color: #303133;
      font-size: 16px;
      font-weight: 600;
    }

    .header-info {
      display: flex;
      gap: 8px;
    }
  }

  .no-items {
    text-align: center;
    padding: 40px 0;
    color: #909399;
  }

  .items-detail {
    .item-detail {
      .item-name {
        font-weight: 600;
        color: #303133;
      }

      .item-code {
        font-size: 11px;
        color: #909399;
        margin-top: 2px;
      }
    }

    .subtotal {
      font-weight: 500;
      color: #303133;
    }
  }
}

/* 采购物品区域样式优化 */
.purchase-items {
  .purchase-item-card {
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }

  .item-label {
    display: block;
    font-size: 12px;
    color: #606266;
    margin-bottom: 4px;
    font-weight: 500;
  }
}

/* 采购物品汇总显示样式 */
.purchase-items-summary {
  .primary-item {
    margin-bottom: 8px;

    .item-name-row {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 2px;

      .item-name {
        font-weight: 600;
        color: #303133;
        font-size: 13px;
      }

      .item-code {
        font-size: 11px;
        color: #909399;
        background: #f0f2f5;
        padding: 1px 4px;
        border-radius: 2px;
      }
    }

    .item-quantity {
      font-size: 11px;
      color: #606266;
    }
  }

  .items-stats {
    .stats-row {
      display: flex;
      gap: 6px;
      margin-bottom: 6px;
    }

    .items-preview {
      .preview-item {
        display: inline-block;
        margin-right: 8px;
        font-size: 11px;
        color: #606266;

        .preview-name {
          color: #303133;
        }

        .preview-quantity {
          color: #409eff;
          font-weight: 500;
        }
      }

      .more-items {
        font-size: 11px;
        color: #909399;
        font-style: italic;
      }
    }
  }
}
</style>