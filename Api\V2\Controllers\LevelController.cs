using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using ItAssetsSystem.Application.Features.Gamification.Services;
using ItAssetsSystem.Models;
using ItAssetsSystem.Models.Gamification;

namespace ItAssetsSystem.Api.V2.Controllers
{
    [ApiController]
    [Route("api/v2/levels")]
    [Authorize]
    public class LevelController : ControllerBase
    {
        private readonly ILevelService _levelService;
        private readonly ILogger<LevelController> _logger;

        public LevelController(ILevelService levelService, ILogger<LevelController> logger)
        {
            _levelService = levelService;
            _logger = logger;
        }

        /// <summary>
        /// 获取用户等级信息
        /// </summary>
        [HttpGet("current")]
        public async Task<ActionResult<ApiResponse<UserLevelInfo>>> GetCurrentUserLevel()
        {
            try
            {
                var userId = GetCurrentUserId();
                var levelInfo = await _levelService.GetUserLevelInfoAsync(userId);

                if (levelInfo == null)
                {
                    return ApiResponse<UserLevelInfo>.CreateFail("用户等级信息不存在");
                }

                return ApiResponse<UserLevelInfo>.CreateSuccess(levelInfo, "获取用户等级信息成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户等级信息失败");
                return ApiResponse<UserLevelInfo>.CreateFail("获取用户等级信息失败");
            }
        }

        /// <summary>
        /// 获取指定用户等级信息
        /// </summary>
        [HttpGet("{userId}")]
        public async Task<ActionResult<ApiResponse<UserLevelInfo>>> GetUserLevel(int userId)
        {
            try
            {
                var levelInfo = await _levelService.GetUserLevelInfoAsync(userId);

                if (levelInfo == null)
                {
                    return ApiResponse<UserLevelInfo>.CreateFail("用户等级信息不存在");
                }

                return ApiResponse<UserLevelInfo>.CreateSuccess(levelInfo, "获取用户等级信息成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户等级信息失败: UserId={UserId}", userId);
                return ApiResponse<UserLevelInfo>.CreateFail("获取用户等级信息失败");
            }
        }

        /// <summary>
        /// 获取所有等级配置
        /// </summary>
        [HttpGet("configs")]
        public async Task<ActionResult<ApiResponse<List<UserLevel>>>> GetAllLevels()
        {
            try
            {
                var levels = await _levelService.GetAllLevelsAsync();
                return ApiResponse<List<UserLevel>>.CreateSuccess(levels, "获取等级配置成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取等级配置失败");
                return ApiResponse<List<UserLevel>>.CreateFail("获取等级配置失败");
            }
        }

        /// <summary>
        /// 手动检查用户升级
        /// </summary>
        [HttpPost("check-levelup")]
        public async Task<ActionResult<ApiResponse<bool>>> CheckLevelUp()
        {
            try
            {
                var userId = GetCurrentUserId();
                var leveledUp = await _levelService.CheckAndProcessLevelUpAsync(userId);

                var message = leveledUp ? "恭喜升级！" : "当前经验值不足以升级";
                return ApiResponse<bool>.CreateSuccess(leveledUp, message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查用户升级失败");
                return ApiResponse<bool>.CreateFail("检查升级失败");
            }
        }

        /// <summary>
        /// 手动检查指定用户升级（管理员功能）
        /// </summary>
        [HttpPost("check-levelup/{userId}")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<ApiResponse<bool>>> CheckUserLevelUp(int userId)
        {
            try
            {
                var leveledUp = await _levelService.CheckAndProcessLevelUpAsync(userId);

                var message = leveledUp ? $"用户 {userId} 升级成功！" : $"用户 {userId} 当前经验值不足以升级";
                return ApiResponse<bool>.CreateSuccess(leveledUp, message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查用户升级失败: UserId={UserId}", userId);
                return ApiResponse<bool>.CreateFail("检查升级失败");
            }
        }

        private int GetCurrentUserId()
        {
            var userIdClaim = User.FindFirst("UserId")?.Value ?? User.FindFirst("sub")?.Value;
            if (int.TryParse(userIdClaim, out var userId))
            {
                return userId;
            }
            throw new UnauthorizedAccessException("无法获取当前用户ID");
        }
    }
}
