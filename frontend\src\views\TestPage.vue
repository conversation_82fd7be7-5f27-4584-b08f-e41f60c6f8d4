/**
 * 简单测试页面
 */
<template>
  <div class="api-test-page">
    <h1>API测试页面</h1>
    
    <div class="test-section">
      <h2>登录API测试</h2>
      
      <div class="api-config">
        <el-input v-model="apiBaseUrl" placeholder="API基础URL" style="width: 300px">
          <template #prepend>基础URL</template>
        </el-input>
        
        <el-input v-model="apiPath" placeholder="API路径" style="width: 300px; margin-left: 10px">
          <template #prepend>路径</template>
        </el-input>
      </div>
      
      <div class="form-inputs">
        <el-input v-model="username" placeholder="用户名" style="width: 250px">
          <template #prepend>用户名</template>
        </el-input>
        
        <el-input v-model="password" placeholder="密码" type="password" style="width: 250px; margin-left: 10px">
          <template #prepend>密码</template>
        </el-input>
      </div>
      
      <div class="test-actions">
        <el-button type="primary" @click="testLoginWithUpperCase">
          测试大写路径 (/User/login)
        </el-button>
        
        <el-button type="success" @click="testLoginWithLowerCase" style="margin-left: 10px">
          测试小写路径 (/user/login)
        </el-button>
      </div>
      
      <div class="response-display" v-if="requestStatus">
        <h3>请求状态: {{ requestStatus }}</h3>
        <pre>{{ responseData }}</pre>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import axios from 'axios'
import { ElMessage } from 'element-plus'

// API配置
const apiBaseUrl = ref('http://localhost:5000')
const apiPath = ref('/user/login')

// 表单数据
const username = ref('admin')
const password = ref('123456')

// 响应数据
const requestStatus = ref('')
const responseData = ref('')

// 测试使用大写路径
const testLoginWithUpperCase = async () => {
  try {
    requestStatus.value = '正在请求...'
    responseData.value = ''
    
    // 构建请求URL
    const url = `${apiBaseUrl.value}/User/login`
    console.log('测试登录API(大写路径):', url)
    
    // 发送请求
    const response = await axios.post(url, {
      username: username.value,
      password: password.value
    })
    
    // 显示响应
    requestStatus.value = `成功 (${response.status})`
    responseData.value = JSON.stringify(response.data, null, 2)
    ElMessage.success('API请求成功')
  } catch (error) {
    console.error('API请求失败:', error)
    requestStatus.value = `失败 (${error.response?.status || '网络错误'})`
    responseData.value = error.response ? JSON.stringify(error.response.data, null, 2) : error.message
    ElMessage.error(`API请求失败: ${error.message}`)
  }
}

// 测试使用小写路径
const testLoginWithLowerCase = async () => {
  try {
    requestStatus.value = '正在请求...'
    responseData.value = ''
    
    // 构建请求URL
    const url = `${apiBaseUrl.value}/user/login`
    console.log('测试登录API(小写路径):', url)
    
    // 发送请求
    const response = await axios.post(url, {
      username: username.value,
      password: password.value
    })
    
    // 显示响应
    requestStatus.value = `成功 (${response.status})`
    responseData.value = JSON.stringify(response.data, null, 2)
    ElMessage.success('API请求成功')
  } catch (error) {
    console.error('API请求失败:', error)
    requestStatus.value = `失败 (${error.response?.status || '网络错误'})`
    responseData.value = error.response ? JSON.stringify(error.response.data, null, 2) : error.message
    ElMessage.error(`API请求失败: ${error.message}`)
  }
}
</script>

<style scoped>
.api-test-page {
  padding: 20px;
}

.test-section {
  margin-top: 20px;
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.api-config, .form-inputs, .test-actions {
  margin-bottom: 20px;
}

.response-display {
  margin-top: 20px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

pre {
  white-space: pre-wrap;
  word-wrap: break-word;
  background-color: #f8f8f8;
  padding: 10px;
  border-radius: 4px;
  max-height: 400px;
  overflow: auto;
}
</style> 