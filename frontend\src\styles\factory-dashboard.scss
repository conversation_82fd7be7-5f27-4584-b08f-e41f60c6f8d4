// 工厂数字化看板专用样式
// 文件路径: src/styles/factory-dashboard.scss

// 如果Tailwind CSS没有完全加载，这里提供备用样式

// 基础布局类
.min-h-screen {
  min-height: 100vh;
}

.bg-gradient-to-b {
  background: linear-gradient(to bottom, var(--tw-gradient-from), var(--tw-gradient-to));
}

.from-gray-900 {
  --tw-gradient-from: #111827;
}

.to-gray-800 {
  --tw-gradient-to: #1f2937;
}

// 状态颜色
.bg-green-500 {
  background-color: #10b981;
}

.bg-yellow-500 {
  background-color: #f59e0b;
}

.bg-red-500 {
  background-color: #ef4444;
}

.bg-blue-500 {
  background-color: #3b82f6;
}

// 透明度变体
.bg-green-500\/70 {
  background-color: rgba(16, 185, 129, 0.7);
}

.bg-yellow-500\/70 {
  background-color: rgba(245, 158, 11, 0.7);
}

.bg-red-500\/70 {
  background-color: rgba(239, 68, 68, 0.7);
}

.bg-blue-500\/70 {
  background-color: rgba(59, 130, 246, 0.7);
}

// 边框颜色
.border-green-500 {
  border-color: #10b981;
}

.border-yellow-500 {
  border-color: #f59e0b;
}

.border-red-500 {
  border-color: #ef4444;
}

.border-blue-500 {
  border-color: #3b82f6;
}

// 文本颜色
.text-green-400 {
  color: #34d399;
}

.text-yellow-400 {
  color: #fbbf24;
}

.text-red-400 {
  color: #f87171;
}

.text-blue-400 {
  color: #60a5fa;
}

// 背景色
.bg-gray-800 {
  background-color: #1f2937;
}

.bg-gray-900 {
  background-color: #111827;
}

.bg-gray-700 {
  background-color: #374151;
}

// 边框
.border-gray-700 {
  border-color: #374151;
}

.border-gray-600 {
  border-color: #4b5563;
}

// 文本颜色
.text-white {
  color: #ffffff;
}

.text-gray-100 {
  color: #f3f4f6;
}

.text-gray-400 {
  color: #9ca3af;
}

.text-gray-300 {
  color: #d1d5db;
}

// 透明度
.text-white\/80 {
  color: rgba(255, 255, 255, 0.8);
}

.text-white\/60 {
  color: rgba(255, 255, 255, 0.6);
}

// 背景透明度
.bg-gray-800\/70 {
  background-color: rgba(31, 41, 55, 0.7);
}

.bg-gray-800\/80 {
  background-color: rgba(31, 41, 55, 0.8);
}

.bg-gray-900\/50 {
  background-color: rgba(17, 24, 39, 0.5);
}

.bg-gray-700\/50 {
  background-color: rgba(55, 65, 81, 0.5);
}

// 边框透明度
.border-gray-700\/50 {
  border-color: rgba(55, 65, 81, 0.5);
}

// 圆角
.rounded-xl {
  border-radius: 0.75rem;
}

.rounded-lg {
  border-radius: 0.5rem;
}

.rounded-md {
  border-radius: 0.375rem;
}

.rounded-full {
  border-radius: 9999px;
}

// 阴影
.shadow-lg {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.shadow-2xl {
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

// 过渡效果
.transition-all {
  transition: all 0.3s ease;
}

.duration-300 {
  transition-duration: 300ms;
}

.ease-out {
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}

// 变换
.scale-105 {
  transform: scale(1.05);
}

.-translate-y-1\/2 {
  transform: translateY(-50%);
}

// 模糊效果
.backdrop-blur-sm {
  backdrop-filter: blur(4px);
}

.backdrop-blur-md {
  backdrop-filter: blur(12px);
}

// Flexbox
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-shrink-0 {
  flex-shrink: 0;
}

// Grid
.grid {
  display: grid;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-5 {
  grid-template-columns: repeat(5, minmax(0, 1fr));
}

// Gap
.gap-2 {
  gap: 0.5rem;
}

.gap-3 {
  gap: 0.75rem;
}

.gap-4 {
  gap: 1rem;
}

// Padding
.p-1 {
  padding: 0.25rem;
}

.p-2 {
  padding: 0.5rem;
}

.p-3 {
  padding: 0.75rem;
}

.p-4 {
  padding: 1rem;
}

.p-6 {
  padding: 1.5rem;
}

// Margin
.m-1 {
  margin: 0.25rem;
}

.mr-1 {
  margin-right: 0.25rem;
}

.mr-2 {
  margin-right: 0.5rem;
}

.ml-1 {
  margin-left: 0.25rem;
}

.ml-4 {
  margin-left: 1rem;
}

.mb-1 {
  margin-bottom: 0.25rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.mt-1 {
  margin-top: 0.25rem;
}

// 宽高
.w-2 {
  width: 0.5rem;
}

.h-2 {
  height: 0.5rem;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

.max-w-7xl {
  max-width: 80rem;
}

// 定位
.absolute {
  position: absolute;
}

.relative {
  position: relative;
}

.top-1 {
  top: 0.25rem;
}

.right-1 {
  right: 0.25rem;
}

.left-2 {
  left: 0.5rem;
}

.top-1\/2 {
  top: 50%;
}

.inset-0 {
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

// Z-index
.z-0 {
  z-index: 0;
}

.z-10 {
  z-index: 10;
}

.z-20 {
  z-index: 20;
}

// 字体
.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}

.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.text-base {
  font-size: 1rem;
  line-height: 1.5rem;
}

.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}

.font-medium {
  font-weight: 500;
}

.font-semibold {
  font-weight: 600;
}

.font-bold {
  font-weight: 700;
}

// 截断文本
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-center {
  text-align: center;
}

// 溢出
.overflow-hidden {
  overflow: hidden;
}

.overflow-auto {
  overflow: auto;
}

// 指针
.cursor-pointer {
  cursor: pointer;
}

.pointer-events-none {
  pointer-events: none;
}

// 边框
.border {
  border-width: 1px;
}

.border-2 {
  border-width: 2px;
}

.border-t {
  border-top-width: 1px;
}

.border-b {
  border-bottom-width: 1px;
}

.border-dashed {
  border-style: dashed;
}

// 环形边框 (ring)
.ring-2 {
  box-shadow: 0 0 0 2px var(--tw-ring-color);
}

.ring-4 {
  box-shadow: 0 0 0 4px var(--tw-ring-color);
}

.ring-cyan-400 {
  --tw-ring-color: #22d3ee;
}

.ring-sky-500 {
  --tw-ring-color: #0ea5e9;
}

.ring-offset-2 {
  box-shadow: 0 0 0 2px var(--tw-ring-offset-color), var(--tw-ring-shadow);
}

.ring-offset-gray-800 {
  --tw-ring-offset-color: #1f2937;
}

.ring-offset-gray-900 {
  --tw-ring-offset-color: #111827;
}

// 动画
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

// 响应式断点
@media (min-width: 640px) {
  .sm\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  
  .sm\:w-60 {
    width: 15rem;
  }
}

@media (min-width: 1024px) {
  .lg\:grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }
  
  .lg\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}

// 自定义动画
.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.status-pulse {
  animation: statusPulse 2s infinite;
}

@keyframes statusPulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

// 工厂布局特定样式
.factory-floor {
  background-image: 
    radial-gradient(circle at 1px 1px, rgba(255,255,255,0.05) 1px, transparent 0);
  background-size: 20px 20px;
}

.location-cell {
  backdrop-filter: blur(4px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.location-cell:hover {
  backdrop-filter: blur(8px);
  transform: translateZ(0) scale(1.05);
}

.location-cell.selected {
  backdrop-filter: blur(12px);
  transform: translateZ(0) scale(1.1);
}

// 玻璃拟态效果
.glass-morphism {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

// 渐变背景
.gradient-bg {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(147, 51, 234, 0.1) 100%);
}

// 状态指示器脉冲
.status-indicator {
  position: relative;
  overflow: hidden;
}

.status-indicator::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  background: inherit;
  border-radius: inherit;
  transform: translate(-50%, -50%) scale(0);
  animation: ripple 2s infinite;
  opacity: 0.3;
}

@keyframes ripple {
  0% {
    transform: translate(-50%, -50%) scale(0);
    opacity: 0.3;
  }
  70% {
    transform: translate(-50%, -50%) scale(2);
    opacity: 0;
  }
  100% {
    transform: translate(-50%, -50%) scale(2);
    opacity: 0;
  }
}