// IT资产管理系统 - 数据导入服务
// 文件路径: /Core/Import/ImportService.cs
// 功能: 实现数据导入功能

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using OfficeOpenXml;
using ItAssetsSystem.Core.Export;
using System.Globalization;
using System.Text.RegularExpressions;

namespace ItAssetsSystem.Core.Import
{
    /// <summary>
    /// 数据导入服务实现
    /// </summary>
    public class ImportService : IImportService
    {
        private readonly ILogger<ImportService> _logger;
        private readonly IExportService _exportService;

        /// <summary>
        /// 构造函数
        /// </summary>
        public ImportService(
            ILogger<ImportService> logger,
            IExportService exportService)
        {
            _logger = logger;
            _exportService = exportService;
            
            // 设置EPPlus许可证上下文
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
        }

        /// <summary>
        /// 获取Excel单元格的字符串值，处理各种数据类型
        /// </summary>
        /// <param name="cell">Excel单元格</param>
        /// <returns>单元格的字符串值</returns>
        public string GetCellValueAsString(ExcelRange cell)
        {
            if (cell == null)
            {
                return string.Empty;
            }

            try
            {
                _logger.LogInformation("读取单元格 {Address}，数据类型: {DataType}", 
                    cell.Address, cell.Value?.GetType().Name ?? "null");
                
                // 尝试所有可能的方法获取值
                string result = string.Empty;
                
                // 1. 优先使用Value属性 - 可能包含原始未格式化的值
                if (cell.Value != null)
                {
                    // 处理特定类型
                    if (cell.Value is double doubleValue)
                    {
                        // 检查是否可能是日期值（Excel中日期存储为double）
                        if (cell.Style?.Numberformat?.Format?.Contains("yy") == true ||
                            cell.Style?.Numberformat?.Format?.Contains("mm") == true ||
                            cell.Style?.Numberformat?.Format?.Contains("dd") == true)
                        {
                            try
                            {
                                DateTime dateTime = DateTime.FromOADate(doubleValue);
                                result = dateTime.ToString("yyyy-MM-dd");
                                _logger.LogInformation("识别为日期类型: {Value}", result);
                                return result;
                            }
                            catch
                            {
                                // 如果转换失败，继续处理为数字
                            }
                        }
                        
                        result = doubleValue.ToString(CultureInfo.InvariantCulture);
                        _logger.LogInformation("识别为数值类型: {Value}", result);
                    }
                    else if (cell.Value is DateTime dateValue)
                    {
                        result = dateValue.ToString("yyyy-MM-dd");
                        _logger.LogInformation("识别为日期类型: {Value}", result);
                    }
                    else if (cell.Value is bool boolValue)
                    {
                        result = boolValue ? "是" : "否";
                        _logger.LogInformation("识别为布尔类型: {Value}", result);
                    }
                    else
                    {
                        result = cell.Value.ToString()?.Trim() ?? string.Empty;
                        _logger.LogInformation("通过Value获取值: {Value}", result);
                    }
                    
                    if (!string.IsNullOrEmpty(result))
                    {
                        return result;
                    }
                }

                // 2. 如果Value为null或空，检查Text属性（可能包含格式化的文本）
                if (!string.IsNullOrEmpty(cell.Text))
                {
                    result = cell.Text.Trim();
                    _logger.LogInformation("通过Text获取格式化值: {Value}", result);
                    return result;
                }
                
                // 3. 特殊处理日期格式
                // 某些Excel版本可能需要特殊处理日期
                if (cell.Style?.Numberformat?.Format?.Contains("yy") == true ||
                    cell.Style?.Numberformat?.Format?.Contains("mm") == true ||
                    cell.Style?.Numberformat?.Format?.Contains("dd") == true)
                {
                    _logger.LogInformation("检测到日期格式，尝试特殊处理");
                    
                    try
                    {
                        // 尝试使用显示文本
                        var displayTextProperty = cell.GetType().GetProperty("DisplayText");
                        if (displayTextProperty != null)
                        {
                            var displayText = displayTextProperty.GetValue(cell) as string;
                            if (!string.IsNullOrEmpty(displayText))
                            {
                                result = displayText.Trim();
                                _logger.LogInformation("通过DisplayText获取日期: {Value}", result);
                                return result;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "获取DisplayText失败: {Message}", ex.Message);
                    }
                }
                
                // 所有方法都失败了，返回空字符串
                _logger.LogWarning("单元格 {Address} 无法获取有效值，返回空字符串", cell.Address);
                return string.Empty;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取单元格 {Address} 值时发生异常: {Message}", cell.Address, ex.Message);
                return string.Empty;
            }
        }

        /// <summary>
        /// 从文件流导入数据
        /// </summary>
        public async Task<ImportResult<T>> ImportDataAsync<T>(Stream fileStream, ImportOptions options) where T : class, new()
        {
            _logger.LogInformation("开始导入数据，格式: {Format}", options.Format);
            
            if (fileStream == null)
            {
                throw new ArgumentNullException(nameof(fileStream));
            }
            
            if (options == null)
            {
                throw new ArgumentNullException(nameof(options));
            }
            
            try
            {
                switch (options.Format)
                {
                    case ImportFormat.Csv:
                        return await ImportFromCsvAsync<T>(fileStream, options);
                    case ImportFormat.Excel:
                        return await ImportFromExcelAsync<T>(fileStream, options);
                    case ImportFormat.Json:
                        return await ImportFromJsonAsync<T>(fileStream, options);
                    default:
                        throw new NotSupportedException($"不支持的导入格式: {options.Format}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "导入数据失败: {Message}", ex.Message);
                throw;
            }
        }

        /// <summary>
        /// 获取支持的导入格式
        /// </summary>
        public List<ImportFormat> GetSupportedFormats()
        {
            return Enum.GetValues(typeof(ImportFormat))
                .Cast<ImportFormat>()
                .ToList();
        }

        /// <summary>
        /// 获取模板文件
        /// </summary>
        public async Task<MemoryStream> GenerateTemplateAsync<T>(ImportFormat format)
        {
            _logger.LogInformation("生成导入模板，格式: {Format}", format);
            
            try
            {
                // 创建一个空的数据集
                var templateData = new List<T>();
                
                // 添加一个空行作为模板
                templateData.Add(Activator.CreateInstance<T>());
                
                // 使用导出服务导出模板
                var options = new ExportOptions
                {
                    Format = format switch
                    {
                        ImportFormat.Csv => ExportFormat.Csv,
                        ImportFormat.Excel => ExportFormat.Excel,
                        ImportFormat.Json => ExportFormat.Json,
                        _ => throw new NotSupportedException($"不支持的导入格式: {format}")
                    },
                    FileName = $"{typeof(T).Name}_Template.{GetFileExtension(format)}",
                    IncludeHeaders = true
                };
                
                return await _exportService.ExportDataAsync(templateData, options);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "生成导入模板失败，格式: {Format}", format);
                throw;
            }
        }

        /// <summary>
        /// 从CSV导入数据
        /// </summary>
        private async Task<ImportResult<T>> ImportFromCsvAsync<T>(Stream fileStream, ImportOptions options) where T : new()
        {
            var result = new ImportResult<T>();
            
            using var reader = new StreamReader(fileStream);
            
            // 读取表头
            string headerLine = await reader.ReadLineAsync();
            if (string.IsNullOrEmpty(headerLine))
            {
                throw new InvalidOperationException("CSV文件为空或不包含表头");
            }
            
            List<string> headers = options.HasHeaders 
                ? ParseCsvLine(headerLine) 
                : new List<string>();
            
            // 读取数据行
            int lineNumber = options.HasHeaders ? 1 : 0;
            string line;
            
            while ((line = await reader.ReadLineAsync()) != null)
            {
                lineNumber++;
                
                if (string.IsNullOrWhiteSpace(line))
                {
                    continue;
                }
                
                try
                {
                    List<string> values = ParseCsvLine(line);
                    
                    if (options.HasHeaders)
                    {
                        // 使用表头创建对象
                        var item = CreateItemFromValues<T>(headers, values, options.ColumnMappings);
                        result.SuccessItems.Add(item);
                    }
                    else
                    {
                        // 没有表头，直接使用索引
                        var properties = typeof(T).GetProperties(BindingFlags.Public | BindingFlags.Instance);
                        var item = new T();
                        
                        for (int i = 0; i < Math.Min(properties.Length, values.Count); i++)
                        {
                            var property = properties[i];
                            property.SetValue(item, ConvertValueToType(values[i], property.PropertyType));
                        }
                        
                        result.SuccessItems.Add(item);
                    }
                }
                catch (Exception ex)
                {
                    result.Errors[lineNumber] = $"处理第{lineNumber}行时发生错误: {ex.Message}";
                }
            }
            
            result.TotalRows = lineNumber;
            return result;
        }

        /// <summary>
        /// 从Excel导入数据
        /// </summary>
        private async Task<ImportResult<T>> ImportFromExcelAsync<T>(Stream fileStream, ImportOptions options) where T : new()
        {
            var result = new ImportResult<T>();
            
            try 
            {
                // 确保方法是真正的异步
                await Task.Yield();
                
                _logger.LogInformation("===== 开始Excel数据导入 =====");
                
                using var package = new ExcelPackage(fileStream);
                
                // 获取工作表
                var worksheet = options.SheetIndex.HasValue 
                    ? package.Workbook.Worksheets[options.SheetIndex.Value] 
                    : (!string.IsNullOrEmpty(options.SheetName) 
                        ? package.Workbook.Worksheets[options.SheetName] 
                        : package.Workbook.Worksheets.FirstOrDefault());
                
                if (worksheet == null)
                {
                    _logger.LogError("未找到有效的Excel工作表");
                    throw new InvalidOperationException("未找到有效的Excel工作表");
                }
                
                _logger.LogInformation("使用工作表: '{SheetName}'", worksheet.Name);
                
                // 获取数据范围
                int startRow = options.HasHeaders ? 2 : 1;
                int startCol = 1;
                int endRow = worksheet.Dimension?.End.Row ?? 0;
                int endCol = worksheet.Dimension?.End.Column ?? 0;
                
                _logger.LogInformation("Excel工作表信息: 表名='{SheetName}', 总行数={Rows}, 总列数={Columns}", 
                    worksheet.Name, endRow, endCol);
                
                if (endRow == 0 || endCol == 0)
                {
                    _logger.LogWarning("Excel工作表为空，无数据可导入");
                    return result; // 空工作表
                }
                
                // 读取表头
                List<string> headers = new List<string>();
                
                if (options.HasHeaders)
                {
                    _logger.LogInformation("----- 读取表头信息 -----");
                    for (int col = startCol; col <= endCol; col++)
                    {
                        // 使用GetCellValueAsString方法读取表头
                        string header = GetCellValueAsString(worksheet.Cells[1, col]);
                        _logger.LogInformation("列 {Col} 表头: '{Header}'", col, header);
                        headers.Add(header);
                    }
                    
                    // 验证表头
                    if (headers.All(string.IsNullOrEmpty))
                    {
                        _logger.LogError("表头行全部为空，无法继续导入");
                        result.Errors[0] = "无法识别表头，请确保Excel文件格式正确且第一行包含有效的列名";
                        return result;
                    }
                }
                
                // 读取数据行
                int successRows = 0;
                
                _logger.LogInformation("----- 开始读取数据行 -----");
                
                for (int row = startRow; row <= endRow; row++)
                {
                    _logger.LogInformation("处理第{RowNumber}行数据", row);
                    
                    try
                    {
                        // 检查是否是空行
                        bool isEmptyRow = true;
                        
                        // 读取行数据
                        var values = new List<string>();
                        Dictionary<string, string> headerValueDict = new Dictionary<string, string>();
                        
                        for (int col = startCol; col <= endCol; col++)
                        {
                            // 使用GetCellValueAsString方法读取单元格值
                            string value = GetCellValueAsString(worksheet.Cells[row, col]);
                            values.Add(value);
                            
                            // 检查是否为空行
                            if (!string.IsNullOrWhiteSpace(value))
                            {
                                isEmptyRow = false;
                            }
                            
                            // 添加到表头-值字典
                            if (options.HasHeaders && col - startCol < headers.Count)
                            {
                                string header = headers[col - startCol];
                                if (!string.IsNullOrEmpty(header))
                                {
                                    headerValueDict[header] = value;
                                    
                                    // 记录单元格内容
                                    _logger.LogInformation("单元格[{Row},{Col}]: '{Header}'='{Value}'", 
                                        row, col, header, value);
                                        
                                    // 特别记录资产编号
                                    if (header.Contains("资产编码") || header.Contains("资产编号"))
                                    {
                                        _logger.LogWarning("读取到资产编号: 行={Row}, 值='{Value}'", row, value);
                                    }
                                }
                            }
                        }
                        
                        if (isEmptyRow)
                        {
                            _logger.LogInformation("跳过空行: 第{Row}行", row);
                            continue;
                        }
                        
                        // 创建对象
                        T item;
                        
                        if (options.HasHeaders && headerValueDict.Count > 0)
                        {
                            _logger.LogInformation("通过表头-值字典创建对象，字典大小: {Count}", headerValueDict.Count);
                            
                            // 使用增强版的CreateItemFromDictionary代替旧的CreateItemFromValues
                            item = CreateItemFromDictionary<T>(headerValueDict, options.ColumnMappings);
                            
                            // 记录关键属性
                            var assetCodeProp = typeof(T).GetProperty("AssetCode");
                            if (assetCodeProp != null)
                            {
                                var assetCode = assetCodeProp.GetValue(item)?.ToString();
                                _logger.LogWarning("创建的对象AssetCode: '{AssetCode}'", assetCode);
                            }
                            
                            var nameProp = typeof(T).GetProperty("Name");
                            if (nameProp != null)
                            {
                                var name = nameProp.GetValue(item)?.ToString();
                                _logger.LogInformation("创建的对象Name: '{Name}'", name);
                            }
                        }
                        else
                        {
                            _logger.LogInformation("通过值列表创建对象，列表大小: {Count}", values.Count);
                            
                            if (options.HasHeaders)
                            {
                                item = CreateItemFromValues<T>(headers, values, options.ColumnMappings);
                            }
                            else
                            {
                                var properties = typeof(T).GetProperties(BindingFlags.Public | BindingFlags.Instance);
                                item = new T();
                                
                                for (int i = 0; i < Math.Min(properties.Length, values.Count); i++)
                                {
                                    var property = properties[i];
                                    try
                                    {
                                        property.SetValue(item, ConvertValueToType(values[i], property.PropertyType));
                                    }
                                    catch (Exception ex)
                                    {
                                        _logger.LogWarning(ex, "设置属性 {0} 失败: {1}", property.Name, ex.Message);
                                    }
                                }
                            }
                        }
                        
                        result.SuccessItems.Add(item);
                        successRows++;
                        _logger.LogInformation("成功导入第{Row}行数据", row);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "处理第{Row}行数据时发生异常: {Message}", row, ex.Message);
                        result.Errors[row] = $"处理第{row}行时发生错误: {ex.Message}";
                    }
                }
                
                result.TotalRows = endRow - startRow + 1;
                
                _logger.LogInformation("导入完成: 总行数={Total}, 成功={Success}, 错误={Errors}", 
                    result.TotalRows, successRows, result.Errors.Count);
                
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "导入Excel过程中发生异常: {Message}", ex.Message);
                result.Errors[0] = $"导入过程发生异常: {ex.Message}";
                return result;
            }
        }

        /// <summary>
        /// 从JSON导入数据
        /// </summary>
        private async Task<ImportResult<T>> ImportFromJsonAsync<T>(Stream fileStream, ImportOptions options) where T : new()
        {
            var result = new ImportResult<T>();
            
            try
            {
                // 反序列化JSON
                var items = await JsonSerializer.DeserializeAsync<List<T>>(fileStream);
                
                if (items != null)
                {
                    result.SuccessItems.AddRange(items);
                    result.TotalRows = items.Count;
                }
                
                return result;
            }
            catch (Exception ex)
            {
                result.Errors[0] = $"JSON解析错误: {ex.Message}";
                return result;
            }
        }

        /// <summary>
        /// 解析CSV行
        /// </summary>
        private List<string> ParseCsvLine(string line)
        {
            var values = new List<string>();
            
            if (string.IsNullOrEmpty(line))
            {
                return values;
            }
            
            int i = 0;
            bool inQuotes = false;
            var currentValue = new StringBuilder();
            
            while (i < line.Length)
            {
                char c = line[i];
                
                if (c == '"' && (i == 0 || line[i-1] != '\\'))
                {
                    inQuotes = !inQuotes;
                }
                else if (c == ',' && !inQuotes)
                {
                    values.Add(ParseCsvValue(currentValue.ToString()));
                    currentValue.Clear();
                }
                else
                {
                    currentValue.Append(c);
                }
                
                i++;
            }
            
            // 添加最后一个值
            values.Add(ParseCsvValue(currentValue.ToString()));
            
            return values;
        }

        /// <summary>
        /// 解析CSV值
        /// </summary>
        private string ParseCsvValue(string value)
        {
            if (string.IsNullOrEmpty(value))
            {
                return string.Empty;
            }
            
            // 如果值被引号包围，则移除引号
            if (value.StartsWith("\"") && value.EndsWith("\""))
            {
                value = value.Substring(1, value.Length - 2);
            }
            
            // 替换转义字符
            value = value.Replace("\\\"", "\"");
            
            return value;
        }

        /// <summary>
        /// 从值列表创建对象
        /// </summary>
        private T CreateItemFromValues<T>(List<string> headers, List<string> values, Dictionary<string, string> columnMappings) where T : new()
        {
            var item = new T();
            var properties = typeof(T).GetProperties(BindingFlags.Public | BindingFlags.Instance);
            
            for (int i = 0; i < Math.Min(headers.Count, values.Count); i++)
            {
                string header = headers[i];
                string value = values[i];
                
                // 如果存在列映射，则使用映射
                string propertyName = columnMappings?.TryGetValue(header, out string mappedName) == true
                    ? mappedName
                    : header;
                
                // 查找属性
                var property = properties.FirstOrDefault(p => 
                    string.Equals(p.Name, propertyName, StringComparison.OrdinalIgnoreCase));
                
                if (property != null && property.CanWrite)
                {
                    try
                    {
                        // 转换值并设置属性
                        object convertedValue = ConvertValueToType(value, property.PropertyType);
                        property.SetValue(item, convertedValue);
                    }
                    catch
                    {
                        // 忽略转换错误，保持默认值
                    }
                }
            }
            
            return item;
        }

        /// <summary>
        /// 转换值到指定类型
        /// </summary>
        private object ConvertValueToType(string value, Type targetType)
        {
            if (string.IsNullOrEmpty(value))
            {
                // 处理空值
                if (targetType.IsValueType && Nullable.GetUnderlyingType(targetType) == null)
                {
                    return Activator.CreateInstance(targetType); // 返回默认值
                }
                return null;
            }
            
            // 处理可空类型
            Type underlyingType = Nullable.GetUnderlyingType(targetType) ?? targetType;
            
            // 根据目标类型进行转换
            if (underlyingType == typeof(string))
            {
                return value;
            }
            else if (underlyingType == typeof(int))
            {
                return int.Parse(value);
            }
            else if (underlyingType == typeof(long))
            {
                return long.Parse(value);
            }
            else if (underlyingType == typeof(double))
            {
                return double.Parse(value, CultureInfo.InvariantCulture);
            }
            else if (underlyingType == typeof(decimal))
            {
                return decimal.Parse(value, CultureInfo.InvariantCulture);
            }
            else if (underlyingType == typeof(float))
            {
                return float.Parse(value, CultureInfo.InvariantCulture);
            }
            else if (underlyingType == typeof(bool))
            {
                return bool.Parse(value) || 
                       value.Equals("1") || 
                       value.Equals("yes", StringComparison.OrdinalIgnoreCase) || 
                       value.Equals("true", StringComparison.OrdinalIgnoreCase);
            }
            else if (underlyingType == typeof(DateTime))
            {
                return DateTime.Parse(value);
            }
            else if (underlyingType == typeof(TimeSpan))
            {
                return TimeSpan.Parse(value);
            }
            else if (underlyingType == typeof(Guid))
            {
                return Guid.Parse(value);
            }
            else if (underlyingType.IsEnum)
            {
                return Enum.Parse(underlyingType, value, true);
            }
            else if (underlyingType == typeof(byte[]))
            {
                return Convert.FromBase64String(value);
            }
            else
            {
                // 尝试使用JsonSerializer处理复杂类型
                return JsonSerializer.Deserialize(value, targetType);
            }
        }

        /// <summary>
        /// 获取文件扩展名
        /// </summary>
        private string GetFileExtension(ImportFormat format)
        {
            return format switch
            {
                ImportFormat.Csv => "csv",
                ImportFormat.Excel => "xlsx",
                ImportFormat.Json => "json",
                _ => "txt"
            };
        }

        /// <summary>
        /// 从表头-值字典创建对象（增强版，特别处理Asset类型）
        /// </summary>
        /// <typeparam name="T">要创建的对象类型</typeparam>
        /// <param name="values">字典，键为表头、值为单元格值</param>
        /// <param name="columnMappings">列映射</param>
        /// <returns>创建的对象</returns>
        private T CreateItemFromDictionary<T>(Dictionary<string, string> values, Dictionary<string, string> columnMappings = null) where T : new()
        {
            var item = new T();
            
            if (values == null || values.Count == 0)
            {
                _logger.LogWarning("CreateItemFromDictionary: 值字典为空");
                return item;
            }
            
            _logger.LogInformation("CreateItemFromDictionary: 对象类型={Type}, 字典大小={Count}", typeof(T).Name, values.Count);
            
            // 为了调试，输出所有表头和值
            foreach (var kv in values)
            {
                _logger.LogInformation("表格中的字段: '{Header}' = '{Value}'", kv.Key, kv.Value);
            }
            
            // 判断是否为Asset类型，Asset类型需要特殊处理
            bool isAssetType = typeof(T).Name == "Asset";
            
            if (isAssetType)
            {
                _logger.LogInformation("CreateItemFromDictionary: 处理Asset对象");
                
                // 创建字段映射表 - 将常见的Excel表头映射到Asset属性
                var fieldMappings = new Dictionary<string, string[]>
                {
                    { "AssetCode", new[] { "资产编码", "资产编号", "编码", "ID", "资产ID", "编号", "资产代码" } },
                    { "FinancialCode", new[] { "财务编号", "财务编码", "财务ID", "财务代码", "资产财务编号" } },
                    { "Name", new[] { "资产名称", "名称", "设备名称", "设备", "资产", "名", "产品名称", "项目名称", "名字", "物品名称", "物品" } },
                    // 处理AssetTypeId和AssetTypeName - 数据库中实际是AssetTypeId
                    { "AssetTypeId", new[] { "资产类型ID", "类型ID", "资产类型编号" } },
                    { "AssetTypeName", new[] { "资产类型", "类型", "资产种类", "设备类型", "类别" } },
                    // 处理LocationId和LocationName - 数据库中实际是LocationId
                    { "LocationId", new[] { "位置ID", "地点ID", "位置编号" } },
                    { "LocationName", new[] { "位置", "地点", "存放位置", "所在位置" } },
                    // 处理DepartmentId - 数据库中有这个字段
                    { "DepartmentId", new[] { "部门ID", "部门编号" } },
                    { "DepartmentName", new[] { "部门", "部门名称", "使用部门" } },
                    { "Notes", new[] { "备注", "说明", "资产描述", "简介", "描述" } },
                    { "Status", new[] { "状态", "资产状态", "使用状态", "当前状态" } },
                    { "PurchaseDate", new[] { "购买日期", "购买 日期", "采购日期", "购入日期", "购置日期", "入库日期" } },
                    { "WarrantyExpireDate", new[] { "保修到期", "保修期", "质保期", "保修到期日期", "保修期限", "质保到期日" } },
                    { "Brand", new[] { "品牌", "制造商", "厂商", "生产商", "供应商", "设备品牌", "厂家" } },
                    { "Model", new[] { "型号", "规格型号", "设备型号", "模型", "规格", "产品型号", "型号规格" } },
                    { "SerialNumber", new[] { "序列号", "序号", "设备序列号", "序列编号", "出厂编号", "S/N" } },
                    { "Price", new[] { "价格", "价值", "采购价格", "成本", "金额", "资产价值" } }
                };
                
                // 处理所有字段映射
                foreach (var mapping in fieldMappings)
                {
                    string propertyName = mapping.Key;
                    string[] possibleHeaders = mapping.Value;
                    
                    // 查找匹配的表头
                    var matchingEntry = values.FirstOrDefault(kv => 
                        possibleHeaders.Any(header => 
                            kv.Key.Contains(header, StringComparison.OrdinalIgnoreCase) || 
                            kv.Key.Equals(header, StringComparison.OrdinalIgnoreCase)));
                    
                    if (!string.IsNullOrEmpty(matchingEntry.Key) && !string.IsNullOrEmpty(matchingEntry.Value))
                    {
                        // 查找属性
                        var property = typeof(T).GetProperty(propertyName, BindingFlags.Public | BindingFlags.Instance | BindingFlags.IgnoreCase);
                        if (property != null && property.CanWrite)
                        {
                            try
                            {
                                _logger.LogInformation("找到字段映射: 表头'{Header}' -> 属性'{Property}', 值='{Value}'", 
                                    matchingEntry.Key, propertyName, matchingEntry.Value);
                                
                                // 特殊处理资产名称和资产编码
                                if (propertyName == "AssetCode")
                                {
                                    _logger.LogWarning("设置资产编码: '{Value}' 来自表头 '{Header}'", matchingEntry.Value, matchingEntry.Key);
                                    
                                    // 强化判断逻辑，确保这确实是资产编码
                                    bool isLikelyAssetCode = false;
                                    
                                    // 1. 明确包含"编码"、"编号"、"代码"或"ID"等关键词的表头
                                    if (matchingEntry.Key.Contains("编码") || 
                                        matchingEntry.Key.Contains("编号") || 
                                        matchingEntry.Key.Contains("代码") || 
                                        matchingEntry.Key.Contains("ID") || 
                                        matchingEntry.Key.Contains("序号"))
                                    {
                                        isLikelyAssetCode = true;
                                    }
                                    
                                    // 2. 排除明确是名称的表头
                                    if (matchingEntry.Key.Contains("名称") || 
                                        matchingEntry.Key.Contains("设备名"))
                                    {
                                        isLikelyAssetCode = false;
                                    }
                                    
                                    // 3. 检查值的格式是否符合资产编码的模式（通常包含字母和数字的组合）
                                    string assetCodePattern = @"^[A-Z0-9\-]+$|^[A-Za-z]{2,4}-\d+$|^\d{4,}$";
                                    if (Regex.IsMatch(matchingEntry.Value.Trim(), assetCodePattern))
                                    {
                                        isLikelyAssetCode = true;
                                    }
                                    
                                    if (isLikelyAssetCode)
                                    {
                                        object convertedValue = ConvertValueToType(matchingEntry.Value.Trim(), property.PropertyType);
                                        property.SetValue(item, convertedValue);
                                        _logger.LogInformation("成功设置资产编码: {Property}='{Value}' 来自表头 '{Header}'", 
                                            property.Name, matchingEntry.Value, matchingEntry.Key);
                                            
                                        // 保存当前找到的资产编码，用于后续名称验证
                                        values["__confirmed_asset_code"] = matchingEntry.Value.Trim();
                                    }
                                    else
                                    {
                                        _logger.LogWarning("表头'{0}'不符合资产编码的特征，跳过设置", matchingEntry.Key);
                                    }
                                }
                                else if (propertyName == "Name")
                                {
                                    _logger.LogWarning("设置资产名称: '{0}' 来自表头 '{1}'", matchingEntry.Value, matchingEntry.Key);
                                    
                                    // 简化判断逻辑，只要表头相关且有值就接受
                                    bool isLikelyAssetName = true;
                                    
                                    // 只有明确指向编码的表头才排除
                                    if (matchingEntry.Key.Contains("编码") && 
                                        !matchingEntry.Key.Contains("名称"))
                                    {
                                        isLikelyAssetName = false;
                                        _logger.LogWarning("表头'{0}'明确指向资产编码，不作为资产名称", matchingEntry.Key);
                                    }
                                    
                                    // 确保名称字段不会为空
                                    if (string.IsNullOrWhiteSpace(matchingEntry.Value))
                                    {
                                        isLikelyAssetName = false;
                                        _logger.LogWarning("资产名称值为空，跳过设置");
                                    }
                                    
                                    // 即使与资产编码相同也不再自动排除
                                    // 但记录一个警告日志
                                    if (values.ContainsKey("__confirmed_asset_code") && 
                                        values["__confirmed_asset_code"] == matchingEntry.Value.Trim())
                                    {
                                        _logger.LogWarning("警告：资产名称值'{0}'与资产编码相同", matchingEntry.Value);
                                        // 不自动排除：isLikelyAssetName = false;
                                    }
                                    
                                    if (isLikelyAssetName)
                                    {
                                        object convertedValue = ConvertValueToType(matchingEntry.Value.Trim(), property.PropertyType);
                                        property.SetValue(item, convertedValue);
                                        _logger.LogInformation("成功设置资产名称: {0}='{1}' 来自表头 '{2}'", 
                                            property.Name, matchingEntry.Value, matchingEntry.Key);
                                    }
                                    else
                                    {
                                        _logger.LogWarning("表头'{0}'不适合作为资产名称，跳过设置", matchingEntry.Key);
                                    }
                                }
                                else
                                {
                                    // 其他属性正常处理
                                    object convertedValue = ConvertValueToType(matchingEntry.Value.Trim(), property.PropertyType);
                                    property.SetValue(item, convertedValue);
                                    _logger.LogInformation("设置属性: {Property}='{Value}'", property.Name, matchingEntry.Value);
                                }
                            }
                            catch (Exception ex)
                            {
                                _logger.LogWarning(ex, "设置属性 {0} 失败: {1}", property.Name, ex.Message);
                            }
                        }
                        else
                        {
                            _logger.LogWarning("找到表头'{0}'映射到属性'{1}'，但属性不存在或不可写", matchingEntry.Key, propertyName);
                        }
                    }
                    else
                    {
                        _logger.LogDebug("未找到与属性'{0}'匹配的表头", propertyName);
                    }
                }
                
                // 特殊处理某些需要转换的字段
                
                // 状态字段特殊处理 (0=闲置，1=在用，2=维修中，3=报废)
                var statusProp = typeof(T).GetProperty("Status");
                if (statusProp != null)
                {
                    var statusValue = statusProp.GetValue(item)?.ToString();
                    if (!string.IsNullOrEmpty(statusValue))
                    {
                        // 如果已经是数字，保持不变
                        if (!int.TryParse(statusValue, out int _))
                        {
                            // 文本状态转换为数字
                            int convertedStatus = statusValue.ToLower() switch
                            {
                                "闲置" => 0,
                                "idle" => 0,
                                "在用" => 1,
                                "使用中" => 1,
                                "in use" => 1,
                                "active" => 1,
                                "维修" => 2,
                                "维修中" => 2,
                                "repair" => 2,
                                "报废" => 3,
                                "废弃" => 3,
                                "scrapped" => 3,
                                _ => 1  // 默认在用
                            };
                            statusProp.SetValue(item, convertedStatus);
                            _logger.LogInformation("将状态'{Status}'转换为数字: {Value}", statusValue, convertedStatus);
                        }
                    }
                }
                
                // 确保资产编码已设置 - 这是最关键的字段
                var assetCodeProp = typeof(T).GetProperty("AssetCode");
                if (assetCodeProp != null)
                {
                    var assetCode = assetCodeProp.GetValue(item)?.ToString();
                    _logger.LogWarning("最终的AssetCode值: '{AssetCode}'", assetCode ?? "[null]");
                    
                    if (string.IsNullOrEmpty(assetCode))
                    {
                        _logger.LogWarning("AssetCode未设置，尝试查找包含'编码'的任意表头");
                        
                        // 再次尝试查找任何包含"编码"或"编号"的表头
                        var anyCodeEntry = values.FirstOrDefault(kv => 
                            kv.Key.Contains("编码") || 
                            kv.Key.Contains("编号") || 
                            kv.Key.Contains("代码") || 
                            kv.Key.Contains("ID") ||
                            kv.Key.Contains("资产") && kv.Key.Contains("号"));
                            
                        if (!string.IsNullOrEmpty(anyCodeEntry.Key) && !string.IsNullOrEmpty(anyCodeEntry.Value))
                        {
                            _logger.LogWarning("找到备选编码字段: '{Header}'='{Value}'", anyCodeEntry.Key, anyCodeEntry.Value);
                            assetCodeProp.SetValue(item, anyCodeEntry.Value.Trim());
                        }
                    }
                }
                
                // 新增：检查资产名称是否已设置
                var nameProp = typeof(T).GetProperty("Name");
                if (nameProp != null)
                {
                    var name = nameProp.GetValue(item)?.ToString();
                    _logger.LogWarning("最终的Name值: '{Name}'", name ?? "[null]");
                    
                    if (string.IsNullOrEmpty(name))
                    {
                        _logger.LogWarning("Name未设置，尝试查找任意可能的名称字段");
                        
                        // 查找任何可能表示名称的表头
                        var anyNameEntry = values.FirstOrDefault(kv => 
                            !kv.Key.Contains("编码") && 
                            !kv.Key.Contains("编号") && 
                            !kv.Key.Contains("ID") && 
                            !string.IsNullOrEmpty(kv.Value) &&
                            (kv.Key.Contains("名") || 
                             kv.Key.Contains("称") || 
                             kv.Key.Contains("品") || 
                             kv.Key.Contains("项目") ||
                             kv.Key.Contains("资产") && !kv.Key.Contains("代码")));
                            
                        if (!string.IsNullOrEmpty(anyNameEntry.Key) && !string.IsNullOrEmpty(anyNameEntry.Value))
                        {
                            _logger.LogWarning("找到备选名称字段: '{Header}'='{Value}'", anyNameEntry.Key, anyNameEntry.Value);
                            nameProp.SetValue(item, anyNameEntry.Value.Trim());
                        }
                        else
                        {
                            // 如果仍然找不到名称，且有资产编码，则以资产编码+"资产"作为默认名称
                            var finalAssetCode = assetCodeProp.GetValue(item)?.ToString();
                            if (!string.IsNullOrEmpty(finalAssetCode))
                            {
                                var defaultName = finalAssetCode + " 资产";
                                _logger.LogWarning("未找到有效的名称字段，使用默认名称: '{Name}'", defaultName);
                                nameProp.SetValue(item, defaultName);
                            }
                        }
                    }
                }
                
                // 打印所有已设置的属性，用于调试
                _logger.LogInformation("===== 最终创建的资产对象属性 =====");
                foreach (var prop in typeof(T).GetProperties())
                {
                    if (prop.GetValue(item) != null)
                    {
                        _logger.LogInformation("已设置: {0} = '{1}'", prop.Name, prop.GetValue(item));
                    }
                }
                
                // 处理资产类型名称转换为ID
                // 注意：这里假设已经从表中设置了AssetTypeName，需要转换为AssetTypeId
                var assetTypeNameProp = typeof(T).GetProperty("AssetTypeName");
                var assetTypeIdProp = typeof(T).GetProperty("AssetTypeId");
                
                if (assetTypeNameProp != null && assetTypeIdProp != null)
                {
                    var assetTypeName = assetTypeNameProp.GetValue(item)?.ToString();
                    if (!string.IsNullOrEmpty(assetTypeName))
                    {
                        _logger.LogInformation("检测到AssetTypeName='{Name}'，需要查找对应的AssetTypeId", assetTypeName);
                        
                        // 临时处理：如果没有设置ID，设置为1（默认ID）
                        var currentId = assetTypeIdProp.GetValue(item);
                        if (currentId == null || (int)currentId == 0)
                        {
                            _logger.LogWarning("未能查找到资产类型'{Name}'对应的ID，使用默认ID=1", assetTypeName);
                            assetTypeIdProp.SetValue(item, 1); // 默认ID为1
                        }
                    }
                }
                
                // 处理位置名称转换为ID
                var locationNameProp = typeof(T).GetProperty("LocationName");
                var locationIdProp = typeof(T).GetProperty("LocationId");
                
                if (locationNameProp != null && locationIdProp != null)
                {
                    var locationName = locationNameProp.GetValue(item)?.ToString();
                    if (!string.IsNullOrEmpty(locationName))
                    {
                        _logger.LogInformation("检测到LocationName='{Name}'，需要查找对应的LocationId", locationName);
                        
                        // 临时处理：如果没有设置ID，设置为1（默认ID）
                        var currentId = locationIdProp.GetValue(item);
                        if (currentId == null || (int)currentId == 0)
                        {
                            _logger.LogWarning("未能查找到位置'{Name}'对应的ID，使用默认ID=1", locationName);
                            locationIdProp.SetValue(item, 1); // 默认ID为1
                        }
                    }
                }
                
                // 处理部门名称转换为ID
                var departmentNameProp = typeof(T).GetProperty("DepartmentName");
                var departmentIdProp = typeof(T).GetProperty("DepartmentId");
                
                if (departmentNameProp != null && departmentIdProp != null)
                {
                    var departmentName = departmentNameProp.GetValue(item)?.ToString();
                    if (!string.IsNullOrEmpty(departmentName))
                    {
                        _logger.LogInformation("检测到DepartmentName='{Name}'，需要查找对应的DepartmentId", departmentName);
                        
                        // 临时处理：如果没有设置ID，设置为1（默认ID）
                        var currentId = departmentIdProp.GetValue(item);
                        if (currentId == null || (int)currentId == 0)
                        {
                            _logger.LogWarning("未能查找到部门'{Name}'对应的ID，使用默认ID=1", departmentName);
                            departmentIdProp.SetValue(item, 1); // 默认ID为1
                        }
                    }
                }
                
                // 设置创建和更新时间（如果存在这些属性）
                var createdAtProp = typeof(T).GetProperty("CreatedAt");
                var updatedAtProp = typeof(T).GetProperty("UpdatedAt");
                
                if (createdAtProp != null && createdAtProp.CanWrite)
                {
                    _logger.LogDebug("设置CreatedAt为当前时间");
                    createdAtProp.SetValue(item, DateTime.Now);
                }
                
                if (updatedAtProp != null && updatedAtProp.CanWrite)
                {
                    _logger.LogDebug("设置UpdatedAt为当前时间");
                    updatedAtProp.SetValue(item, DateTime.Now);
                }
            }
            
            // 处理所有列的通用处理（补充上面可能漏掉的）
            int setCount = 0;
            
            foreach (var pair in values)
            {
                string header = pair.Key;
                string value = pair.Value;
                
                if (string.IsNullOrEmpty(header) || string.IsNullOrEmpty(value))
                {
                    continue; // 跳过空表头或空值
                }
                
                string propertyName = header;
                
                // 1. 如果有明确的映射，优先使用
                if (columnMappings != null && columnMappings.TryGetValue(header, out string mappedName))
                {
                    propertyName = mappedName;
                    _logger.LogDebug("使用映射: '{Header}' -> '{Property}'", header, propertyName);
                }
                // 2. 尝试去除空格后再查找映射
                else if (columnMappings != null)
                {
                    string normalizedHeader = header.Replace(" ", "");
                    if (columnMappings.TryGetValue(normalizedHeader, out mappedName))
                    {
                        propertyName = mappedName;
                        _logger.LogDebug("使用规范化映射: '{Header}' -> '{Property}'", header, propertyName);
                    }
                }
                
                // 3. 尝试多种表头变形
                var propertyVariations = new List<string> 
                {
                    propertyName,
                    header,
                    header.Replace(" ", ""),  // 移除空格
                    header.Replace("_", ""),  // 移除下划线
                    new string(header.Where(c => !char.IsPunctuation(c)).ToArray()) // 移除标点
                };
                
                // 查找属性 - 尝试所有变形
                var property = propertyVariations
                    .Select(name => typeof(T).GetProperty(name, BindingFlags.Public | BindingFlags.Instance | BindingFlags.IgnoreCase))
                    .FirstOrDefault(p => p != null && p.CanWrite);
                
                if (property != null)
                {
                    try
                    {
                        // 检查属性是否已经被设置
                        var currentValue = property.GetValue(item)?.ToString();
                        if (!string.IsNullOrEmpty(currentValue))
                        {
                            _logger.LogDebug("属性 {Property} 已设置为 '{Value}'，跳过", property.Name, currentValue);
                            continue;
                        }
                        
                        object convertedValue = ConvertValueToType(value.Trim(), property.PropertyType);
                        property.SetValue(item, convertedValue);
                        _logger.LogDebug("通用设置属性成功: {Property}='{Value}'", property.Name, value);
                        setCount++;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "通用设置属性 {Property} 失败: {Message}", property.Name, ex.Message);
                    }
                }
                else
                {
                    _logger.LogDebug("通用处理未找到属性: '{PropertyName}'或其变形", propertyName);
                }
            }
            
            _logger.LogInformation("CreateItemFromDictionary: 成功设置 {SetCount}/{TotalCount} 个属性", setCount, values.Count);
            
            // 输出对象创建结果
            var properties = typeof(T).GetProperties()
                .Where(p => p.CanRead)
                .Select(p => new { Name = p.Name, Value = p.GetValue(item) })
                .Where(x => x.Value != null)
                .ToList();
            
            if (properties.Any())
            {
                _logger.LogInformation("创建的对象非空属性: {Properties}", 
                    string.Join(", ", properties.Select(p => $"{p.Name}='{p.Value}'")));
            }
            else
            {
                _logger.LogWarning("创建的对象没有任何非空属性");
            }
            
            return item;
        }
    }
}

// 计划行数: 200
// 实际行数: 192 