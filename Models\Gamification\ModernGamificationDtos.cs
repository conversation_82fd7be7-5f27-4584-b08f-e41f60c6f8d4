using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace ItAssetsSystem.Models.Gamification
{
    /// <summary>
    /// 用户工作汇总DTO
    /// </summary>
    public class UserWorkSummaryDto
    {
        public int UserId { get; set; }
        public string UserName { get; set; }
        public string DepartmentName { get; set; }
        public string PeriodType { get; set; }
        public DateTime PeriodDate { get; set; }
        
        // 任务统计
        public int TasksCreated { get; set; }
        public int TasksClaimed { get; set; }
        public int TasksCompleted { get; set; }
        public int TasksCommented { get; set; }
        public int TasksTotal => TasksCreated + TasksClaimed + TasksCompleted + TasksCommented;
        
        // 资产统计
        public int AssetsCreated { get; set; }
        public int AssetsUpdated { get; set; }
        public int AssetsDeleted { get; set; }
        public int AssetsTotal => AssetsCreated + AssetsUpdated + AssetsDeleted;
        
        // 故障统计
        public int FaultsReported { get; set; }
        public int FaultsRepaired { get; set; }
        public int FaultsTotal => FaultsReported + FaultsRepaired;
        
        // 采购统计
        public int ProcurementsCreated { get; set; }
        public int ProcurementsUpdated { get; set; }
        public int ProcurementsTotal => ProcurementsCreated + ProcurementsUpdated;
        
        // 备件统计
        public int PartsIn { get; set; }
        public int PartsOut { get; set; }
        public int PartsAdded { get; set; }
        public int PartsTotal => PartsIn + PartsOut + PartsAdded;
        
        // 游戏化汇总
        public int TotalPointsEarned { get; set; }
        public int TotalCoinsEarned { get; set; }
        public int TotalDiamondsEarned { get; set; }
        public int TotalXpEarned { get; set; }
        
        // 排名信息
        public int PointsRank { get; set; }
        public int CoinsRank { get; set; }
        public int DiamondsRank { get; set; }
        public int ProductivityRank { get; set; }
        
        // 综合评价
        public string Evaluation { get; set; }
    }

    /// <summary>
    /// 排行榜项目DTO
    /// </summary>
    public class LeaderboardItemDto
    {
        public int Rank { get; set; }
        public int UserId { get; set; }
        public string UserName { get; set; }
        public string DepartmentName { get; set; }
        public int TotalPoints { get; set; }
        public int TotalCoins { get; set; }
        public int TotalDiamonds { get; set; }
        public int CurrentLevel { get; set; }
        public string LevelName { get; set; }
        public string LevelColor { get; set; }
        public int TasksCompleted { get; set; }
        public int TotalItemsObtained { get; set; }
        public int ConsecutiveDays { get; set; }
        public DateTime? LastActivity { get; set; }
        
        // 本周数据
        public int WeeklyPoints { get; set; }
        public int WeeklyTasks { get; set; }
        public int WeeklyAssets { get; set; }
        
        // 专业特长
        public string Specialty { get; set; }
    }

    /// <summary>
    /// 用户统计DTO
    /// </summary>
    public class UserStatsDto
    {
        public int UserId { get; set; }
        public string UserName { get; set; }
        public string DepartmentName { get; set; }
        
        // 基础统计
        public int TotalPoints { get; set; }
        public int TotalCoins { get; set; }
        public int TotalDiamonds { get; set; }
        public int CurrentXP { get; set; }
        public int CurrentLevel { get; set; }
        public string LevelName { get; set; }
        public string LevelColor { get; set; }
        public decimal LevelProgress { get; set; }
        
        // 任务统计
        public int TasksCompleted { get; set; }
        public int TasksCreated { get; set; }
        public int TasksClaimed { get; set; }
        
        // 道具统计
        public int TotalItemsObtained { get; set; }
        public int RareItemsCount { get; set; }
        public int EpicItemsCount { get; set; }
        public int LegendaryItemsCount { get; set; }
        
        // 活跃度
        public int ConsecutiveDays { get; set; }
        public DateTime? LastActivity { get; set; }
        
        // 排名信息
        public int PointsRank { get; set; }
        public int LevelRank { get; set; }
        public int TasksRank { get; set; }
        
        // 本周数据
        public int WeeklyPoints { get; set; }
        public int WeeklyTasks { get; set; }
        public int WeeklyXP { get; set; }
    }

    /// <summary>
    /// 部门统计DTO
    /// </summary>
    public class DepartmentStatsDto
    {
        public int DepartmentId { get; set; }
        public string DepartmentName { get; set; }
        public int UserCount { get; set; }
        public int TotalPoints { get; set; }
        public int AvgPoints { get; set; }
        public decimal AvgLevel { get; set; }
        public int TotalTasksCompleted { get; set; }
        public int TotalItemsObtained { get; set; }
        
        // 排名
        public int PointsRank { get; set; }
        public int ProductivityRank { get; set; }
        
        // 活跃度
        public int ActiveUsersCount { get; set; }
        public decimal ActivityRate { get; set; }
    }

    /// <summary>
    /// 奖励结果
    /// </summary>
    public class RewardResult
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public RewardData Data { get; set; }

        public static RewardResult CreateSuccess(RewardData data, string message = "奖励发放成功")
        {
            return new RewardResult { Success = true, Message = message, Data = data };
        }

        public static RewardResult Failed(string message)
        {
            return new RewardResult { Success = false, Message = message };
        }
    }

    /// <summary>
    /// 奖励数据
    /// </summary>
    public class RewardData
    {
        public int Points { get; set; }
        public int Coins { get; set; }
        public int Diamonds { get; set; }
        public int Xp { get; set; }
        public LevelUpResult LevelUp { get; set; }
        public ItemDropResult ItemDrop { get; set; }
    }

    /// <summary>
    /// 升级结果
    /// </summary>
    public class LevelUpResult
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public int OldLevel { get; set; }
        public int NewLevel { get; set; }
        public string LevelName { get; set; }
        public string LevelColor { get; set; }
        public int RewardCoins { get; set; }
        public int RewardDiamonds { get; set; }
        public string[] UnlockedFeatures { get; set; }
        public DateTime LevelUpTime { get; set; }

        public static LevelUpResult CreateSuccess(int oldLevel, int newLevel, string levelName, string levelColor,
            int rewardCoins, int rewardDiamonds, string[] unlockedFeatures)
        {
            return new LevelUpResult
            {
                Success = true,
                Message = $"恭喜升级到 {levelName}！",
                OldLevel = oldLevel,
                NewLevel = newLevel,
                LevelName = levelName,
                LevelColor = levelColor,
                RewardCoins = rewardCoins,
                RewardDiamonds = rewardDiamonds,
                UnlockedFeatures = unlockedFeatures,
                LevelUpTime = DateTime.Now
            };
        }

        public static LevelUpResult Failed(string message)
        {
            return new LevelUpResult { Success = false, Message = message };
        }
    }

    /// <summary>
    /// 道具掉落结果
    /// </summary>
    public class ItemDropResult
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public long ItemId { get; set; }
        public string ItemName { get; set; }
        public string Rarity { get; set; }
        public string IconUrl { get; set; }
        public DateTime ObtainedAt { get; set; }

        public static ItemDropResult CreateSuccess(long itemId, string itemName, string rarity, string iconUrl)
        {
            return new ItemDropResult
            {
                Success = true,
                Message = $"获得{rarity}道具：{itemName}",
                ItemId = itemId,
                ItemName = itemName,
                Rarity = rarity,
                IconUrl = iconUrl,
                ObtainedAt = DateTime.Now
            };
        }

        public static ItemDropResult Failed(string message)
        {
            return new ItemDropResult { Success = false, Message = message };
        }
    }

    /// <summary>
    /// 游戏化行为规则实体
    /// </summary>
    public class GamificationBehaviorRule
    {
        public string BehaviorCode { get; set; }
        public string BehaviorName { get; set; }
        public string ModuleName { get; set; }
        public int Points { get; set; }
        public int Coins { get; set; }
        public int Xp { get; set; }
        public int Diamonds { get; set; }
        public decimal ItemDropChance { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }



    /// <summary>
    /// 游戏化日志实体
    /// </summary>
    public class GamificationLog
    {
        public long Id { get; set; }
        public int UserId { get; set; }
        public string ActionType { get; set; }
        public int PointsGained { get; set; }
        public int CoinsGained { get; set; }
        public int DiamondsGained { get; set; }
        public int XpGained { get; set; }
        public string Description { get; set; }
        public string Context { get; set; }
        public DateTime CreatedAt { get; set; }
    }
}
