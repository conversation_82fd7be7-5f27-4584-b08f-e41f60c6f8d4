using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using ItAssetsSystem.Application.Features.Gamification.Services;
using ItAssetsSystem.Models;
using ItAssetsSystem.Models.Gamification;

namespace ItAssetsSystem.Api.V2.Controllers
{
    [ApiController]
    [Route("api/v2/items")]
    [Authorize]
    public class ItemController : ControllerBase
    {
        private readonly IItemService _itemService;
        private readonly ILogger<ItemController> _logger;

        public ItemController(IItemService itemService, ILogger<ItemController> logger)
        {
            _itemService = itemService;
            _logger = logger;
        }

        /// <summary>
        /// 获取用户道具背包
        /// </summary>
        [HttpGet("inventory")]
        public async Task<ActionResult<ApiResponse<List<UserItemInfo>>>> GetUserInventory()
        {
            try
            {
                var userId = GetCurrentUserId();
                var items = await _itemService.GetUserItemsAsync(userId);

                return ApiResponse<List<UserItemInfo>>.CreateSuccess(items, "获取用户道具背包成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户道具背包失败");
                return ApiResponse<List<UserItemInfo>>.CreateFail("获取道具背包失败");
            }
        }

        /// <summary>
        /// 获取指定用户道具背包
        /// </summary>
        [HttpGet("inventory/{userId}")]
        public async Task<ActionResult<ApiResponse<List<UserItemInfo>>>> GetUserInventory(int userId)
        {
            try
            {
                var items = await _itemService.GetUserItemsAsync(userId);
                return ApiResponse<List<UserItemInfo>>.CreateSuccess(items, "获取用户道具背包成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户道具背包失败: UserId={UserId}", userId);
                return ApiResponse<List<UserItemInfo>>.CreateFail("获取道具背包失败");
            }
        }

        /// <summary>
        /// 使用道具
        /// </summary>
        [HttpPost("use")]
        public async Task<ActionResult<ApiResponse<bool>>> UseItem([FromBody] UseItemRequest request)
        {
            try
            {
                var userId = GetCurrentUserId();
                var success = await _itemService.UseItemAsync(userId, request.ItemId);

                if (success)
                {
                    return ApiResponse<bool>.CreateSuccess(true, "道具使用成功");
                }
                else
                {
                    return ApiResponse<bool>.CreateFail("道具使用失败，可能是道具不存在或数量不足");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "使用道具失败: ItemId={ItemId}", request.ItemId);
                return ApiResponse<bool>.CreateFail("使用道具失败");
            }
        }

        /// <summary>
        /// 获取所有道具配置
        /// </summary>
        [HttpGet("configs")]
        public async Task<ActionResult<ApiResponse<List<GamificationItem>>>> GetAllItems()
        {
            try
            {
                var items = await _itemService.GetAllItemsAsync();
                return ApiResponse<List<GamificationItem>>.CreateSuccess(items, "获取道具配置成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取道具配置失败");
                return ApiResponse<List<GamificationItem>>.CreateFail("获取道具配置失败");
            }
        }

        /// <summary>
        /// 手动发放随机道具（测试用）
        /// </summary>
        [HttpPost("grant-random")]
        public async Task<ActionResult<ApiResponse<ItAssetsSystem.Application.Features.Gamification.Services.ItemDropResult>>> GrantRandomItem([FromQuery] string source = "MANUAL")
        {
            try
            {
                var userId = GetCurrentUserId();
                var result = await _itemService.GrantRandomItemAsync(userId, source);

                if (result.Success)
                {
                    return ApiResponse<ItAssetsSystem.Application.Features.Gamification.Services.ItemDropResult>.CreateSuccess(result, result.Message);
                }
                else
                {
                    return ApiResponse<ItAssetsSystem.Application.Features.Gamification.Services.ItemDropResult>.CreateFail(result.Message);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "发放随机道具失败");
                return ApiResponse<ItAssetsSystem.Application.Features.Gamification.Services.ItemDropResult>.CreateFail("发放道具失败");
            }
        }

        /// <summary>
        /// 手动发放随机道具给指定用户（管理员功能）
        /// </summary>
        [HttpPost("grant-random/{userId}")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<ApiResponse<ItAssetsSystem.Application.Features.Gamification.Services.ItemDropResult>>> GrantRandomItemToUser(int userId, [FromQuery] string source = "ADMIN_GRANT")
        {
            try
            {
                var result = await _itemService.GrantRandomItemAsync(userId, source);

                if (result.Success)
                {
                    return ApiResponse<ItAssetsSystem.Application.Features.Gamification.Services.ItemDropResult>.CreateSuccess(result, result.Message);
                }
                else
                {
                    return ApiResponse<ItAssetsSystem.Application.Features.Gamification.Services.ItemDropResult>.CreateFail(result.Message);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "发放随机道具失败: UserId={UserId}", userId);
                return ApiResponse<ItAssetsSystem.Application.Features.Gamification.Services.ItemDropResult>.CreateFail("发放道具失败");
            }
        }

        /// <summary>
        /// 检查用户是否可以获得道具
        /// </summary>
        [HttpGet("can-obtain")]
        public async Task<ActionResult<ApiResponse<bool>>> CanObtainItem()
        {
            try
            {
                var userId = GetCurrentUserId();
                var canObtain = await _itemService.CanObtainItemAsync(userId);

                var message = canObtain ? "可以获得道具" : "今日道具获取已达上限";
                return ApiResponse<bool>.CreateSuccess(canObtain, message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查道具获取限制失败");
                return ApiResponse<bool>.CreateFail("检查失败");
            }
        }

        private int GetCurrentUserId()
        {
            var userIdClaim = User.FindFirst("UserId")?.Value ?? User.FindFirst("sub")?.Value;
            if (int.TryParse(userIdClaim, out var userId))
            {
                return userId;
            }
            throw new UnauthorizedAccessException("无法获取当前用户ID");
        }
    }
}
