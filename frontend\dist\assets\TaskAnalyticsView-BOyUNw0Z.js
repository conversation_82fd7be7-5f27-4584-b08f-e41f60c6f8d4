import{_ as a,b as e,e as t,w as s,r as l,ad as r,z as i,b0 as d,a as n,ag as o,E as c,o as u,d as m,t as h,p as v}from"./index-CG5lHOPO.js";import{f as p}from"./date-DeQj3nH2.js";import{i as f,av as g,et as y,es as k,eu as T,er as w,eq as D,ep as C,eo as _,en as b}from"./install-BB2cVj0s.js";g([y,k,T,w,D,C,_,b]);const x={class:"analytics-container"},R={class:"card-header"},A={class:"header-actions"},z={key:0,class:"loading-container"},O={key:1},E={class:"stat-item"},L={class:"stat-icon blue"},S={class:"stat-info"},M={class:"stat-value"},P={class:"stat-item"},F={class:"stat-icon green"},V={class:"stat-info"},j={class:"stat-value"},B={class:"stat-percent"},X={class:"stat-item"},N={class:"stat-icon yellow"},q={class:"stat-info"},G={class:"stat-value"},I={class:"stat-item"},U={class:"stat-icon red"},H={class:"stat-info"},J={class:"stat-value"},K={class:"stat-percent"},Q={class:"chart-container",ref:"statusChartRef"},W={class:"chart-container",ref:"priorityChartRef"},Y={class:"chart-container",ref:"trendChartRef"},Z={class:"chart-container",ref:"assigneeChartRef"},$={class:"chart-container",ref:"completionChartRef"};const aa=a({name:"TaskAnalyticsView",setup(){const a=l(!0),e=l([]),t=l(null),s=l(null),n=l(null),u=l(null),m=l(null);let h=null,v=null,g=null,y=null,k=null;const T=r({totalTasks:0,completedTasks:0,inProgressTasks:0,pendingTasks:0,cancelledTasks:0,overdueTasks:0,completionRate:0,overdueRate:0}),w=r({statusData:[],priorityData:[],trendData:{dates:[],created:[],completed:[]},assigneeData:[],completionData:[]}),D=[{text:"最近一周",value:()=>{const a=new Date,e=new Date;return e.setTime(e.getTime()-6048e5),[e,a]}},{text:"最近一个月",value:()=>{const a=new Date,e=new Date;return e.setTime(e.getTime()-2592e6),[e,a]}},{text:"最近三个月",value:()=>{const a=new Date,e=new Date;return e.setTime(e.getTime()-7776e6),[e,a]}}],C=async()=>{a.value=!0;try{const a={};e.value&&2===e.value.length&&(a.startDate=p(e.value[0],"yyyy-MM-dd"),a.endDate=p(e.value[1],"yyyy-MM-dd"));const t=await o.getTasksAnalytics(a);t.success?(T.totalTasks=t.data.totalTasks,T.completedTasks=t.data.completedTasks,T.inProgressTasks=t.data.inProgressTasks,T.pendingTasks=t.data.pendingTasks,T.cancelledTasks=t.data.cancelledTasks,T.overdueTasks=t.data.overdueTasks,T.completionRate=t.data.totalTasks>0?Math.round(t.data.completedTasks/t.data.totalTasks*100):0,T.overdueRate=t.data.totalTasks>0?Math.round(t.data.overdueTasks/t.data.totalTasks*100):0,w.statusData=[{value:t.data.pendingTasks,name:"待处理"},{value:t.data.inProgressTasks,name:"进行中"},{value:t.data.completedTasks,name:"已完成"},{value:t.data.cancelledTasks,name:"已取消"}],w.priorityData=[{value:t.data.priorityDistribution.low||0,name:"低"},{value:t.data.priorityDistribution.medium||0,name:"中"},{value:t.data.priorityDistribution.high||0,name:"高"},{value:t.data.priorityDistribution.urgent||0,name:"紧急"}],w.trendData=t.data.taskTrend,w.assigneeData=t.data.assigneeDistribution.map((a=>({value:a.taskCount,name:a.assigneeName||"未分配"}))),w.completionData=t.data.userCompletionRate.map((a=>({value:a.completionRate,name:a.userName}))),_()):c.error(t.message||"加载分析数据失败")}catch(t){c.error("加载分析数据时发生错误")}finally{a.value=!1}},_=()=>{h.setOption({title:{text:"任务状态分布",left:"center"},tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)"},legend:{orient:"vertical",left:"left",data:["待处理","进行中","已完成","已取消"]},series:[{name:"任务状态",type:"pie",radius:"60%",center:["50%","60%"],data:w.statusData,emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}},color:["#909399","#E6A23C","#67C23A","#F56C6C"]}]}),v.setOption({title:{text:"任务优先级分布",left:"center"},tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)"},legend:{orient:"vertical",left:"left",data:["低","中","高","紧急"]},series:[{name:"优先级",type:"pie",radius:"60%",center:["50%","60%"],data:w.priorityData,emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}},color:["#909399","#67C23A","#E6A23C","#F56C6C"]}]}),g.setOption({title:{text:"任务趋势",left:"center"},tooltip:{trigger:"axis"},legend:{data:["新建任务","完成任务"],bottom:"0%"},grid:{left:"3%",right:"4%",bottom:"10%",containLabel:!0},xAxis:{type:"category",boundaryGap:!1,data:w.trendData.dates||[]},yAxis:{type:"value"},series:[{name:"新建任务",type:"line",stack:"总量",data:w.trendData.created||[],color:"#409EFF"},{name:"完成任务",type:"line",stack:"总量",data:w.trendData.completed||[],color:"#67C23A"}]}),y.setOption({title:{text:"人员任务分配",left:"center"},tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)"},legend:{orient:"vertical",left:"left",type:"scroll",formatter:function(a){return a.length>10?a.slice(0,10)+"...":a}},series:[{name:"任务分配",type:"pie",radius:"60%",center:["50%","60%"],data:w.assigneeData,emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]}),k.setOption({title:{text:"人员任务完成率",left:"center"},tooltip:{trigger:"axis",axisPointer:{type:"shadow"},formatter:"{b}: {c}%"},grid:{left:"3%",right:"4%",bottom:"10%",containLabel:!0},xAxis:{type:"value",max:100,axisLabel:{formatter:"{value}%"}},yAxis:{type:"category",data:w.completionData.map((a=>a.name)),axisLabel:{formatter:function(a){return a.length>10?a.slice(0,10)+"...":a}}},series:[{name:"完成率",type:"bar",data:w.completionData.map((a=>a.value)),label:{show:!0,formatter:"{c}%",position:"right"},itemStyle:{color:function(a){const e=a.value;return e>=80?"#67C23A":e>=60?"#E6A23C":"#F56C6C"}}}]})},b=()=>{null==h||h.resize(),null==v||v.resize(),null==g||g.resize(),null==y||y.resize(),null==k||k.resize()};return i((()=>{const a=new Date,l=new Date;l.setTime(l.getTime()-2592e6),e.value=[l,a],setTimeout((()=>{h=f(t.value),v=f(s.value),g=f(n.value),y=f(u.value),k=f(m.value),window.addEventListener("resize",b),C()}),100)})),d((()=>{window.removeEventListener("resize",b),null==h||h.dispose(),null==v||v.dispose(),null==g||g.dispose(),null==y||y.dispose(),null==k||k.dispose()})),{loading:a,dateRange:e,dateShortcuts:D,statistics:T,statusChartRef:t,priorityChartRef:s,trendChartRef:n,assigneeChartRef:u,completionChartRef:m,handleDateRangeChange:()=>{C()},refreshData:()=>{C()}}}},[["render",function(a,l,r,i,d,o){const c=n("el-date-picker"),p=n("Refresh"),f=n("el-icon"),g=n("el-button"),y=n("el-skeleton"),k=n("Tickets"),T=n("el-card"),w=n("el-col"),D=n("Select"),C=n("Loading"),_=n("AlarmClock"),b=n("el-row");return u(),e("div",x,[t(b,{gutter:20},{default:s((()=>[t(w,{span:24},{default:s((()=>[t(T,{class:"stat-card"},{header:s((()=>[m("div",R,[l[2]||(l[2]=m("h2",null,"任务数据分析",-1)),m("div",A,[t(c,{modelValue:i.dateRange,"onUpdate:modelValue":l[0]||(l[0]=a=>i.dateRange=a),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",shortcuts:i.dateShortcuts,onChange:i.handleDateRangeChange},null,8,["modelValue","shortcuts","onChange"]),t(g,{type:"primary",onClick:i.refreshData},{default:s((()=>[t(f,null,{default:s((()=>[t(p)])),_:1}),l[1]||(l[1]=v(" 刷新 "))])),_:1},8,["onClick"])])])])),default:s((()=>[i.loading?(u(),e("div",z,[t(y,{rows:10,animated:""})])):(u(),e("div",O,[t(b,{gutter:20,class:"stat-cards"},{default:s((()=>[t(w,{xs:24,sm:12,md:6},{default:s((()=>[t(T,{shadow:"hover",class:"stat-summary-card"},{default:s((()=>[m("div",E,[m("div",L,[t(f,null,{default:s((()=>[t(k)])),_:1})]),m("div",S,[l[3]||(l[3]=m("div",{class:"stat-title"},"总任务数",-1)),m("div",M,h(i.statistics.totalTasks),1)])])])),_:1})])),_:1}),t(w,{xs:24,sm:12,md:6},{default:s((()=>[t(T,{shadow:"hover",class:"stat-summary-card"},{default:s((()=>[m("div",P,[m("div",F,[t(f,null,{default:s((()=>[t(D)])),_:1})]),m("div",V,[l[4]||(l[4]=m("div",{class:"stat-title"},"已完成任务",-1)),m("div",j,h(i.statistics.completedTasks),1),m("div",B,h(i.statistics.completionRate)+"%",1)])])])),_:1})])),_:1}),t(w,{xs:24,sm:12,md:6},{default:s((()=>[t(T,{shadow:"hover",class:"stat-summary-card"},{default:s((()=>[m("div",X,[m("div",N,[t(f,null,{default:s((()=>[t(C)])),_:1})]),m("div",q,[l[5]||(l[5]=m("div",{class:"stat-title"},"进行中任务",-1)),m("div",G,h(i.statistics.inProgressTasks),1)])])])),_:1})])),_:1}),t(w,{xs:24,sm:12,md:6},{default:s((()=>[t(T,{shadow:"hover",class:"stat-summary-card"},{default:s((()=>[m("div",I,[m("div",U,[t(f,null,{default:s((()=>[t(_)])),_:1})]),m("div",H,[l[6]||(l[6]=m("div",{class:"stat-title"},"逾期任务",-1)),m("div",J,h(i.statistics.overdueTasks),1),m("div",K,h(i.statistics.overdueRate)+"%",1)])])])),_:1})])),_:1})])),_:1}),t(b,{gutter:20,class:"chart-row"},{default:s((()=>[t(w,{span:12},{default:s((()=>[t(T,{shadow:"hover",class:"chart-card"},{header:s((()=>l[7]||(l[7]=[m("div",{class:"chart-header"},"任务状态分布",-1)]))),default:s((()=>[m("div",Q,null,512)])),_:1})])),_:1}),t(w,{span:12},{default:s((()=>[t(T,{shadow:"hover",class:"chart-card"},{header:s((()=>l[8]||(l[8]=[m("div",{class:"chart-header"},"任务优先级分布",-1)]))),default:s((()=>[m("div",W,null,512)])),_:1})])),_:1})])),_:1}),t(b,{gutter:20,class:"chart-row"},{default:s((()=>[t(w,{span:24},{default:s((()=>[t(T,{shadow:"hover",class:"chart-card"},{header:s((()=>l[9]||(l[9]=[m("div",{class:"chart-header"},"任务趋势",-1)]))),default:s((()=>[m("div",Y,null,512)])),_:1})])),_:1})])),_:1}),t(b,{gutter:20,class:"chart-row"},{default:s((()=>[t(w,{span:12},{default:s((()=>[t(T,{shadow:"hover",class:"chart-card"},{header:s((()=>l[10]||(l[10]=[m("div",{class:"chart-header"},"人员任务分配",-1)]))),default:s((()=>[m("div",Z,null,512)])),_:1})])),_:1}),t(w,{span:12},{default:s((()=>[t(T,{shadow:"hover",class:"chart-card"},{header:s((()=>l[11]||(l[11]=[m("div",{class:"chart-header"},"任务完成率",-1)]))),default:s((()=>[m("div",$,null,512)])),_:1})])),_:1})])),_:1})]))])),_:1})])),_:1})])),_:1})])}],["__scopeId","data-v-948bc639"]]);export{aa as default};
