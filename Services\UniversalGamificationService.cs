// File: Services/UniversalGamificationService.cs
// Description: 通用游戏化奖励服务

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using ItAssetsSystem.Infrastructure.Data;
using ItAssetsSystem.Models.Entities.Gamification;
using ItAssetsSystem.Services.Interfaces;
using ItAssetsSystem.Application.Features.Gamification.Services;

namespace ItAssetsSystem.Services
{
    /// <summary>
    /// 通用游戏化奖励服务
    /// </summary>
    public class UniversalGamificationService : IUniversalGamificationService
    {
        private readonly AppDbContext _context;
        private readonly ILogger<UniversalGamificationService> _logger;
        private readonly IGamificationService _gamificationService;
        private readonly IAchievementService _achievementService;
        private readonly IStandardBehaviorTracker _standardBehaviorTracker;
        private readonly Dictionary<string, BehaviorType> _behaviorCache;

        public UniversalGamificationService(
            AppDbContext context,
            ILogger<UniversalGamificationService> logger,
            IGamificationService gamificationService,
            IAchievementService achievementService,
            IStandardBehaviorTracker standardBehaviorTracker)
        {
            _context = context;
            _logger = logger;
            _gamificationService = gamificationService;
            _achievementService = achievementService;
            _standardBehaviorTracker = standardBehaviorTracker;
            _behaviorCache = new Dictionary<string, BehaviorType>();
        }

        /// <summary>
        /// 触发行为奖励
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="behaviorCode">行为代码</param>
        /// <param name="referenceId">关联对象ID</param>
        /// <param name="context">上下文数据</param>
        /// <param name="description">描述</param>
        public async Task<bool> TriggerBehaviorRewardAsync(
            int userId, 
            string behaviorCode, 
            long? referenceId = null, 
            object? context = null,
            string? description = null)
        {
            try
            {
                // 检查是否已经奖励过（防重复）
                if (await IsAlreadyRewardedAsync(userId, behaviorCode, referenceId))
                {
                    _logger.LogInformation("用户 {UserId} 的行为 {BehaviorCode} 已经奖励过，跳过", userId, behaviorCode);
                    return false;
                }

                // 获取行为配置
                var behavior = await GetBehaviorTypeAsync(behaviorCode);
                if (behavior == null || !behavior.IsActive)
                {
                    _logger.LogWarning("行为类型 {BehaviorCode} 不存在或未启用", behaviorCode);
                    return false;
                }

                // 计算奖励
                var reward = CalculateReward(behavior, context);

                // 发放奖励
                var result = await _gamificationService.AddRewardAsync(userId, new Models.Dtos.Gamification.RewardDto
                {
                    Points = reward.Points,
                    Coins = reward.Coins,
                    Diamonds = reward.Diamonds,
                    ActionType = behaviorCode,
                    Description = description ?? $"完成行为: {behavior.Name}",
                    ReferenceId = referenceId?.ToString()
                });

                if (result.Success)
                {
                    _logger.LogInformation("用户 {UserId} 完成行为 {BehaviorCode}，获得奖励: {Points}积分, {Coins}金币, {Diamonds}钻石",
                        userId, behaviorCode, reward.Points, reward.Coins, reward.Diamonds);

                    // 🎯 同时记录到标准化行为追踪表
                    try
                    {
                        // 构建包含奖励信息的上下文
                        var enhancedContext = new
                        {
                            OriginalContext = context,
                            RewardInfo = new
                            {
                                Points = reward.Points,
                                Coins = reward.Coins,
                                Diamonds = reward.Diamonds,
                                XP = 0
                            },
                            BehaviorCode = behaviorCode,
                            Description = description ?? $"完成行为: {behavior.Name}",
                            Timestamp = DateTime.Now
                        };

                        await _standardBehaviorTracker.TrackBehaviorAsync(
                            actionType: behaviorCode,
                            userId: userId,
                            referenceId: referenceId,
                            referenceTable: GetTableNameFromBehaviorCode(behaviorCode),
                            context: enhancedContext
                        );

                        _logger.LogInformation("用户 {UserId} 行为 {BehaviorCode} 已记录到标准化追踪表", userId, behaviorCode);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "记录用户 {UserId} 行为 {BehaviorCode} 到标准化追踪表时发生错误", userId, behaviorCode);
                        // 不影响主流程，继续执行
                    }

                    // 检查等级提升
                    try
                    {
                        await CheckLevelUpAsync(userId);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "检查用户 {UserId} 等级提升时发生错误", userId);
                    }

                    // 检查道具掉落
                    try
                    {
                        await CheckItemDropAsync(userId, behaviorCode);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "检查用户 {UserId} 道具掉落时发生错误", userId);
                    }

                    // 检查成就
                    await _achievementService.CheckUserAchievementsAsync(userId);
                }

                return result.Success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "触发行为奖励失败: UserId={UserId}, BehaviorCode={BehaviorCode}", userId, behaviorCode);
                return false;
            }
        }

        /// <summary>
        /// 回退行为奖励（用于删除操作）
        /// </summary>
        public async Task<bool> RevertBehaviorRewardAsync(int userId, string behaviorCode, long? referenceId = null)
        {
            try
            {
                // 查找原始奖励记录
                var log = await _context.GamificationLogs
                    .Where(l => l.UserId == userId 
                             && l.ActionType == behaviorCode 
                             && l.ReferenceId == referenceId)
                    .FirstOrDefaultAsync();

                if (log == null)
                {
                    _logger.LogWarning("未找到需要回退的奖励记录: UserId={UserId}, BehaviorCode={BehaviorCode}, ReferenceId={ReferenceId}",
                        userId, behaviorCode, referenceId);
                    return false;
                }

                // 扣除积分
                var result = await _gamificationService.SubtractRewardAsync(userId, new Models.Dtos.Gamification.RewardDto
                {
                    Points = log.PointsGained,
                    Coins = log.CoinsGained,
                    Diamonds = log.DiamondsGained,
                    ActionType = $"REVERT_{behaviorCode}",
                    Description = $"回退操作: {log.Description}",
                    ReferenceId = referenceId?.ToString()
                });

                if (result.Success)
                {
                    _logger.LogInformation("成功回退用户 {UserId} 的行为奖励: {BehaviorCode}", userId, behaviorCode);
                }

                return result.Success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "回退行为奖励失败: UserId={UserId}, BehaviorCode={BehaviorCode}", userId, behaviorCode);
                return false;
            }
        }

        /// <summary>
        /// 获取行为类型配置
        /// </summary>
        private async Task<BehaviorType?> GetBehaviorTypeAsync(string behaviorCode)
        {
            // 先从缓存获取
            if (_behaviorCache.TryGetValue(behaviorCode, out var cached))
            {
                return cached;
            }

            // 从数据库获取
            var behavior = await _context.BehaviorTypes
                .FirstOrDefaultAsync(b => b.Code == behaviorCode);

            if (behavior != null)
            {
                _behaviorCache[behaviorCode] = behavior;
            }

            return behavior;
        }

        /// <summary>
        /// 计算奖励
        /// </summary>
        private (int Points, int Coins, int Diamonds) CalculateReward(BehaviorType behavior, object? context)
        {
            var multiplier = behavior.Multiplier;

            // 根据上下文调整系数
            if (context != null)
            {
                multiplier *= GetContextMultiplier(behavior.Code, context);
            }

            return (
                Points: (int)(behavior.BasePoints * multiplier),
                Coins: (int)(behavior.BaseCoins * multiplier),
                Diamonds: (int)(behavior.BaseDiamonds * multiplier)
            );
        }

        /// <summary>
        /// 根据上下文获取系数
        /// </summary>
        private decimal GetContextMultiplier(string behaviorCode, object context)
        {
            return behaviorCode switch
            {
                BehaviorCodes.TASK_COMPLETED => GetTaskCompletionMultiplier(context),
                BehaviorCodes.FAULT_RECORDED => GetFaultSeverityMultiplier(context),
                _ => 1.0m
            };
        }

        /// <summary>
        /// 任务完成系数（按时完成有额外奖励）
        /// </summary>
        private decimal GetTaskCompletionMultiplier(object context)
        {
            if (context is Dictionary<string, object> dict && dict.TryGetValue("isOnTime", out var isOnTime))
            {
                return (bool)isOnTime ? 1.5m : 1.0m;
            }
            return 1.0m;
        }

        /// <summary>
        /// 故障严重度系数
        /// </summary>
        private decimal GetFaultSeverityMultiplier(object context)
        {
            if (context is Dictionary<string, object> dict && dict.TryGetValue("severity", out var severity))
            {
                return severity.ToString() switch
                {
                    "Critical" => 2.0m,
                    "High" => 1.5m,
                    "Medium" => 1.0m,
                    "Low" => 0.8m,
                    _ => 1.0m
                };
            }
            return 1.0m;
        }

        /// <summary>
        /// 检查是否已经奖励过
        /// </summary>
        private async Task<bool> IsAlreadyRewardedAsync(int userId, string behaviorCode, long? referenceId)
        {
            return await _context.GamificationLogs
                .AnyAsync(l => l.UserId == userId
                            && l.ActionType == behaviorCode
                            && l.ReferenceId == referenceId);
        }

        /// <summary>
        /// 检查等级提升
        /// </summary>
        private async Task CheckLevelUpAsync(int userId)
        {
            try
            {
                // 调用存储过程检查升级
                await _context.Database.ExecuteSqlRawAsync(
                    "CALL CheckAndProcessLevelUp({0})", userId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "调用升级检查存储过程失败: UserId={UserId}", userId);
            }
        }

        /// <summary>
        /// 检查道具掉落
        /// </summary>
        private async Task CheckItemDropAsync(int userId, string behaviorCode)
        {
            // 根据行为类型确定掉落概率
            var dropChance = GetItemDropChance(behaviorCode);
            var random = new Random().NextDouble();

            if (random < dropChance)
            {
                try
                {
                    // 调用存储过程发放随机道具
                    await _context.Database.ExecuteSqlRawAsync(
                        "CALL GrantRandomItem({0}, {1})", userId, behaviorCode);

                    _logger.LogInformation("用户 {UserId} 通过行为 {BehaviorCode} 获得随机道具", userId, behaviorCode);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "发放随机道具失败: UserId={UserId}, BehaviorCode={BehaviorCode}", userId, behaviorCode);
                }
            }
        }

        /// <summary>
        /// 获取道具掉落概率
        /// </summary>
        private double GetItemDropChance(string behaviorCode)
        {
            return behaviorCode switch
            {
                BehaviorCodes.TASK_COMPLETED => 0.15,      // 15%概率
                BehaviorCodes.TASK_CREATED => 0.05,        // 5%概率
                BehaviorCodes.FAULT_RECORDED => 0.10,      // 10%概率
                "LEVEL_UP" => 0.50,                        // 50%概率
                "ACHIEVEMENT_UNLOCKED" => 0.30,            // 30%概率
                _ => 0.02                                   // 默认2%概率
            };
        }

        /// <summary>
        /// 根据行为代码获取对应的表名
        /// </summary>
        private string GetTableNameFromBehaviorCode(string behaviorCode)
        {
            return behaviorCode switch
            {
                var code when code.StartsWith("TASK_") => StandardTableNames.TASKS,
                var code when code.StartsWith("ASSET_") => StandardTableNames.ASSETS,
                var code when code.StartsWith("FAULT_") => StandardTableNames.FAULTS,
                var code when code.StartsWith("PURCHASE_") => StandardTableNames.PURCHASES,
                var code when code.StartsWith("SPAREPART_") => StandardTableNames.SPAREPARTS,
                "TASK_CREATED" => StandardTableNames.TASKS,
                "TASK_CLAIMED" => StandardTableNames.TASK_CLAIMS,
                "TASK_COMPLETED" => StandardTableNames.TASKS,
                "TASK_COMMENTED" => "comments",
                "ASSET_CREATED" => StandardTableNames.ASSETS,
                "ASSET_UPDATED" => StandardTableNames.ASSETS,
                "ASSET_TRANSFERRED" => StandardTableNames.ASSETS,
                "FAULT_RECORDED" => StandardTableNames.FAULTS,
                "FAULT_RESOLVED" => StandardTableNames.FAULTS,
                "PURCHASE_CREATED" => StandardTableNames.PURCHASES,
                "PURCHASE_UPDATED" => StandardTableNames.PURCHASES,
                "SPAREPART_INBOUND" => StandardTableNames.SPAREPARTS,
                "SPAREPART_OUTBOUND" => StandardTableNames.SPAREPARTS,
                "SPAREPART_UPDATED" => StandardTableNames.SPAREPARTS,
                _ => "unknown"
            };
        }

    }
}
