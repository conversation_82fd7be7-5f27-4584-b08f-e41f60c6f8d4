// File: Application/Features/Tasks/Commands/DeleteCommentCommand.cs
// Description: 删除评论命令

using MediatR;
using ItAssetsSystem.Application.Common.Dtos;

namespace ItAssetsSystem.Application.Features.Tasks.Commands
{
    /// <summary>
    /// 删除评论命令
    /// </summary>
    public class DeleteCommentCommand : IRequest<ApiResponse<bool>>
    {
        /// <summary>
        /// 评论ID
        /// </summary>
        public long CommentId { get; set; }

        /// <summary>
        /// 任务ID
        /// </summary>
        public long TaskId { get; set; }

        /// <summary>
        /// 当前用户ID
        /// </summary>
        public int CurrentUserId { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public DeleteCommentCommand()
        {
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="commentId">评论ID</param>
        /// <param name="taskId">任务ID</param>
        /// <param name="currentUserId">当前用户ID</param>
        public DeleteCommentCommand(long commentId, long taskId, int currentUserId)
        {
            CommentId = commentId;
            TaskId = taskId;
            CurrentUserId = currentUserId;
        }
    }
}
