/**
 * 任务页面优化测试工具
 * 用于验证API调用优化和缓存机制的效果
 */

// 性能监控工具
export class TaskPerformanceMonitor {
  constructor() {
    this.apiCalls = []
    this.cacheHits = 0
    this.cacheMisses = 0
    this.startTime = null
  }

  // 开始监控
  startMonitoring() {
    this.startTime = performance.now()
    this.apiCalls = []
    this.cacheHits = 0
    this.cacheMisses = 0
    console.log('🔍 开始监控任务页面性能...')
  }

  // 记录API调用
  recordApiCall(apiName, duration, cached = false) {
    const call = {
      api: apiName,
      duration: duration,
      cached: cached,
      timestamp: performance.now()
    }
    
    this.apiCalls.push(call)
    
    if (cached) {
      this.cacheHits++
      console.log(`🔄 缓存命中: ${apiName} (${duration}ms)`)
    } else {
      this.cacheMisses++
      console.log(`📡 API调用: ${apiName} (${duration}ms)`)
    }
  }

  // 生成性能报告
  generateReport() {
    const totalTime = performance.now() - this.startTime
    const totalApiCalls = this.apiCalls.length
    const totalApiTime = this.apiCalls.reduce((sum, call) => sum + call.duration, 0)
    const cacheHitRate = totalApiCalls > 0 ? (this.cacheHits / totalApiCalls * 100).toFixed(1) : 0

    const report = {
      总耗时: `${totalTime.toFixed(2)}ms`,
      API调用次数: totalApiCalls,
      API总耗时: `${totalApiTime.toFixed(2)}ms`,
      缓存命中率: `${cacheHitRate}%`,
      缓存命中次数: this.cacheHits,
      缓存未命中次数: this.cacheMisses,
      详细调用: this.apiCalls.map(call => ({
        API: call.api,
        耗时: `${call.duration}ms`,
        缓存: call.cached ? '是' : '否'
      }))
    }

    console.log('📊 任务页面性能报告:')
    console.table(report.详细调用)
    console.log('📈 性能汇总:', {
      总耗时: report.总耗时,
      API调用次数: report.API调用次数,
      缓存命中率: report.缓存命中率
    })

    return report
  }
}

// 用户API调用优化验证
export class UserApiOptimizationValidator {
  constructor() {
    this.userApiCalls = new Map()
    this.duplicateCallCount = 0
  }

  // 记录用户API调用
  recordUserApiCall(apiType, userId = null) {
    const key = `${apiType}_${userId || 'all'}`
    const count = this.userApiCalls.get(key) || 0
    this.userApiCalls.set(key, count + 1)

    if (count > 0) {
      this.duplicateCallCount++
      console.warn(`⚠️ 检测到重复的用户API调用: ${apiType} (第${count + 1}次)`)
    }
  }

  // 验证优化效果
  validateOptimization() {
    const totalCalls = Array.from(this.userApiCalls.values()).reduce((sum, count) => sum + count, 0)
    const uniqueCalls = this.userApiCalls.size
    const optimizationRate = totalCalls > 0 ? ((totalCalls - this.duplicateCallCount) / totalCalls * 100).toFixed(1) : 100

    console.log('🎯 用户API调用优化验证:')
    console.log(`总调用次数: ${totalCalls}`)
    console.log(`唯一调用次数: ${uniqueCalls}`)
    console.log(`重复调用次数: ${this.duplicateCallCount}`)
    console.log(`优化率: ${optimizationRate}%`)

    return {
      totalCalls,
      uniqueCalls,
      duplicateCallCount: this.duplicateCallCount,
      optimizationRate: `${optimizationRate}%`
    }
  }
}

// 实时性验证工具
export class RealTimeValidator {
  constructor() {
    this.taskStatusChanges = []
    this.userInfoUpdates = []
  }

  // 记录任务状态变更
  recordTaskStatusChange(taskId, oldStatus, newStatus, timestamp = Date.now()) {
    this.taskStatusChanges.push({
      taskId,
      oldStatus,
      newStatus,
      timestamp
    })
    console.log(`📝 任务状态变更: ${taskId} (${oldStatus} → ${newStatus})`)
  }

  // 记录用户信息更新
  recordUserInfoUpdate(userId, updateType, timestamp = Date.now()) {
    this.userInfoUpdates.push({
      userId,
      updateType,
      timestamp
    })
    console.log(`👤 用户信息更新: ${userId} (${updateType})`)
  }

  // 验证实时性
  validateRealTime(maxDelay = 1000) {
    const issues = []
    
    // 检查任务状态变更后的用户信息更新延迟
    this.taskStatusChanges.forEach(statusChange => {
      if (statusChange.newStatus === 'Done') {
        const relatedUserUpdates = this.userInfoUpdates.filter(update => 
          update.timestamp >= statusChange.timestamp && 
          update.timestamp <= statusChange.timestamp + maxDelay
        )
        
        if (relatedUserUpdates.length === 0) {
          issues.push(`任务 ${statusChange.taskId} 完成后未及时更新用户信息`)
        }
      }
    })

    if (issues.length === 0) {
      console.log('✅ 实时性验证通过')
    } else {
      console.warn('⚠️ 实时性问题:', issues)
    }

    return {
      passed: issues.length === 0,
      issues
    }
  }
}

// 全局性能监控实例
export const taskPerformanceMonitor = new TaskPerformanceMonitor()
export const userApiValidator = new UserApiOptimizationValidator()
export const realTimeValidator = new RealTimeValidator()

// 便捷的监控启动函数
export function startTaskOptimizationMonitoring() {
  taskPerformanceMonitor.startMonitoring()
  console.log('🚀 任务优化监控已启动')
  
  // 在控制台提供便捷的报告生成方法
  window.generateTaskOptimizationReport = () => {
    console.log('\n=== 任务优化报告 ===')
    taskPerformanceMonitor.generateReport()
    userApiValidator.validateOptimization()
    realTimeValidator.validateRealTime()
  }
  
  console.log('💡 提示: 在控制台运行 generateTaskOptimizationReport() 生成优化报告')
}
