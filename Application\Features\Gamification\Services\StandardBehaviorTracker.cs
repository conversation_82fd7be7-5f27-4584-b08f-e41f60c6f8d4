using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using ItAssetsSystem.Infrastructure.Data;
using ItAssetsSystem.Models.Entities.Gamification;
using System.Text.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace ItAssetsSystem.Application.Features.Gamification.Services
{
    /// <summary>
    /// 标准化行为追踪服务
    /// 基于行业最佳实践的双写模式实现
    /// </summary>
    public interface IStandardBehaviorTracker
    {
        /// <summary>
        /// 记录用户行为（原子事务模式）
        /// </summary>
        Task TrackBehaviorAsync(string actionType, int userId, long? referenceId = null, 
                               string? referenceTable = null, object? context = null, 
                               string? sessionId = null, string? ipAddress = null, string? userAgent = null);

        /// <summary>
        /// 批量记录用户行为
        /// </summary>
        Task TrackBehaviorsAsync(IEnumerable<BehaviorEvent> behaviors);

        /// <summary>
        /// 获取用户行为统计
        /// </summary>
        Task<UserBehaviorSummary> GetUserBehaviorSummaryAsync(int userId, DateTime? startDate = null, DateTime? endDate = null);
    }

    public class StandardBehaviorTracker : IStandardBehaviorTracker
    {
        private readonly AppDbContext _context;
        private readonly ILogger<StandardBehaviorTracker> _logger;
        private readonly IConsistentBehaviorTracker _consistentTracker;
        private readonly Dictionary<string, BehaviorTypeConfig> _behaviorConfigs;

        public StandardBehaviorTracker(AppDbContext context, ILogger<StandardBehaviorTracker> logger,
                                     IConsistentBehaviorTracker consistentTracker)
        {
            _context = context;
            _logger = logger;
            _consistentTracker = consistentTracker;
            _behaviorConfigs = new Dictionary<string, BehaviorTypeConfig>();
            // 移除构造函数中的异步调用，改为延迟加载
        }

        /// <summary>
        /// 记录用户行为（使用一致性追踪器）
        /// </summary>
        public async Task TrackBehaviorAsync(string actionType, int userId, long? referenceId = null,
                                           string? referenceTable = null, object? context = null,
                                           string? sessionId = null, string? ipAddress = null, string? userAgent = null)
        {
            try
            {
                // 🚀 使用一致性追踪器，保证最终一致性
                var eventId = await _consistentTracker.TrackBehaviorAsync(
                    actionType, userId, referenceId, referenceTable, context,
                    sessionId, ipAddress, userAgent);

                if (eventId > 0)
                {
                    _logger.LogInformation("用户行为记录成功: EventId={EventId}, UserId={UserId}, ActionType={ActionType}",
                        eventId, userId, actionType);
                }
                else
                {
                    _logger.LogWarning("用户行为记录失败: UserId={UserId}, ActionType={ActionType}", userId, actionType);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "记录用户行为失败：UserId={UserId}, ActionType={ActionType}", userId, actionType);
                throw;
            }
        }

        /// <summary>
        /// 批量记录用户行为
        /// </summary>
        public async Task TrackBehaviorsAsync(IEnumerable<BehaviorEvent> behaviors)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                foreach (var behavior in behaviors)
                {
                    await TrackBehaviorInternalAsync(behavior);
                }

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();

                _logger.LogInformation("批量记录 {Count} 个用户行为成功", behaviors.Count());
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, "批量记录用户行为失败");
                throw;
            }
        }

        /// <summary>
        /// 获取用户行为统计
        /// </summary>
        public async Task<UserBehaviorSummary> GetUserBehaviorSummaryAsync(int userId, DateTime? startDate = null, DateTime? endDate = null)
        {
            startDate ??= DateTime.Today.AddDays(-30); // 默认30天
            endDate ??= DateTime.Now;

            var behaviors = await _context.UserBehaviorEvents
                .Where(b => b.UserId == userId && b.Timestamp >= startDate && b.Timestamp <= endDate)
                .GroupBy(b => b.ActionType)
                .Select(g => new BehaviorTypeSummary
                {
                    ActionType = g.Key,
                    Count = g.Count(),
                    TotalPoints = g.Sum(b => b.PointsEarned),
                    TotalCoins = g.Sum(b => b.CoinsEarned),
                    TotalDiamonds = g.Sum(b => b.DiamondsEarned),
                    TotalXp = g.Sum(b => b.XpEarned)
                })
                .ToListAsync();

            return new UserBehaviorSummary
            {
                UserId = userId,
                StartDate = startDate.Value,
                EndDate = endDate.Value,
                BehaviorTypes = behaviors,
                TotalPoints = behaviors.Sum(b => b.TotalPoints),
                TotalCoins = behaviors.Sum(b => b.TotalCoins),
                TotalDiamonds = behaviors.Sum(b => b.TotalDiamonds),
                TotalXp = behaviors.Sum(b => b.TotalXp)
            };
        }

        #region 私有方法

        private async Task LoadBehaviorConfigsAsync()
        {
            try
            {
                var configs = await _context.BehaviorTypeConfigs
                    .Where(c => c.IsActive)
                    .ToListAsync();

                _behaviorConfigs.Clear();
                foreach (var config in configs)
                {
                    _behaviorConfigs[config.ActionType] = config;
                }

                _logger.LogInformation("加载了 {Count} 个行为配置", configs.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加载行为配置失败");
            }
        }

        private async Task<BehaviorTypeConfig?> GetBehaviorConfigAsync(string actionType)
        {
            if (_behaviorConfigs.TryGetValue(actionType, out var config))
            {
                return config;
            }

            // 如果缓存中没有，从数据库重新加载
            config = await _context.BehaviorTypeConfigs
                .FirstOrDefaultAsync(c => c.ActionType == actionType && c.IsActive);

            if (config != null)
            {
                _behaviorConfigs[actionType] = config;
            }

            return config;
        }

        private BehaviorRewards CalculateRewards(BehaviorTypeConfig config)
        {
            return new BehaviorRewards
            {
                Points = (int)(config.BasePoints * config.Multiplier),
                Coins = (int)(config.BaseCoins * config.Multiplier),
                Diamonds = (int)(config.BaseDiamonds * config.Multiplier),
                Xp = (int)(config.BaseXp * config.Multiplier)
            };
        }

        private async Task UpdateUserStatsAsync(int userId, BehaviorRewards rewards)
        {
            // 检查是否存在gamification_userstats表
            var userStats = await _context.GamificationUserStats
                .FirstOrDefaultAsync(s => s.UserId == userId);

            if (userStats != null)
            {
                userStats.PointsBalance += rewards.Points;
                userStats.CoinsBalance += rewards.Coins;
                userStats.DiamondsBalance += rewards.Diamonds;
                userStats.CurrentXP += rewards.Xp;
                userStats.LastUpdatedTimestamp = DateTime.Now;

                _context.GamificationUserStats.Update(userStats);
            }
        }

        private async Task TrackBehaviorInternalAsync(BehaviorEvent behavior)
        {
            var config = await GetBehaviorConfigAsync(behavior.ActionType);
            if (config == null || !config.IsActive) return;

            var rewards = CalculateRewards(config);

            var behaviorEvent = new UserBehaviorEvent
            {
                UserId = behavior.UserId,
                ActionType = behavior.ActionType,
                ActionCategory = config.ActionCategory,
                ReferenceId = behavior.ReferenceId,
                ReferenceTable = behavior.ReferenceTable,
                PointsEarned = rewards.Points,
                CoinsEarned = rewards.Coins,
                DiamondsEarned = rewards.Diamonds,
                XpEarned = rewards.Xp,
                ContextData = behavior.Context != null ? JsonSerializer.Serialize(behavior.Context) : null,
                Timestamp = DateTime.Now,
                CreatedAt = DateTime.Now
            };

            _context.UserBehaviorEvents.Add(behaviorEvent);
            await UpdateUserStatsAsync(behavior.UserId, rewards);
        }

        #endregion
    }

    #region 数据传输对象

    public class BehaviorEvent
    {
        public string ActionType { get; set; } = string.Empty;
        public int UserId { get; set; }
        public long? ReferenceId { get; set; }
        public string? ReferenceTable { get; set; }
        public object? Context { get; set; }
    }

    public class BehaviorRewards
    {
        public int Points { get; set; }
        public int Coins { get; set; }
        public int Diamonds { get; set; }
        public int Xp { get; set; }
    }

    public class UserBehaviorSummary
    {
        public int UserId { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public List<BehaviorTypeSummary> BehaviorTypes { get; set; } = new();
        public int TotalPoints { get; set; }
        public int TotalCoins { get; set; }
        public int TotalDiamonds { get; set; }
        public int TotalXp { get; set; }
    }

    public class BehaviorTypeSummary
    {
        public string ActionType { get; set; } = string.Empty;
        public int Count { get; set; }
        public int TotalPoints { get; set; }
        public int TotalCoins { get; set; }
        public int TotalDiamonds { get; set; }
        public int TotalXp { get; set; }
    }

    #endregion
}
