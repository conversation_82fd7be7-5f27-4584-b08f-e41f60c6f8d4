/**
 * 格式化工具函数
 */

/**
 * 格式化日期
 * @param {string|Date} date 日期字符串或Date对象
 * @param {string} format 格式化模式，默认 'YYYY-MM-DD HH:mm:ss'
 * @returns {string} 格式化后的日期字符串
 */
export function formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
  if (!date) return '';
  
  const d = new Date(date);
  
  if (isNaN(d.getTime())) {
    return '';
  }
  
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  const hours = String(d.getHours()).padStart(2, '0');
  const minutes = String(d.getMinutes()).padStart(2, '0');
  const seconds = String(d.getSeconds()).padStart(2, '0');
  
  return format
    .replace('YYYY', year)
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds);
}

/**
 * 格式化日期时间 (考虑本地时区)
 * @param {string|Date} date 日期时间对象或ISO字符串
 * @param {string} format 格式化模式，默认 'YYYY-MM-DD HH:mm'
 * @return {string} 格式化后的日期时间字符串
 */
export function formatDateTime(date, format = 'YYYY-MM-DD HH:mm') {
  if (!date) return '';

  // 确保使用本地时区而不是UTC时间
  const dateObj = date instanceof Date ? date : new Date(date);
  
  // 使用本地时区
  const year = dateObj.getFullYear();
  const month = String(dateObj.getMonth() + 1).padStart(2, '0');
  const day = String(dateObj.getDate()).padStart(2, '0');
  const hours = String(dateObj.getHours()).padStart(2, '0');
  const minutes = String(dateObj.getMinutes()).padStart(2, '0');
  const seconds = String(dateObj.getSeconds()).padStart(2, '0');

  if (format === 'YYYY-MM-DD HH:mm') {
    return `${year}-${month}-${day} ${hours}:${minutes}`;
  } else if (format === 'YYYY-MM-DD') {
    return `${year}-${month}-${day}`;
  } else if (format === 'HH:mm') {
    return `${hours}:${minutes}`;
  } else if (format === 'YYYY-MM-DD HH:mm:ss') {
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  }

  // 默认返回完整格式
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

/**
 * 格式化价格
 * @param {number} price 价格数值
 * @param {number} decimals 小数位数，默认2位
 * @param {string} currency 货币符号，默认'¥'
 * @returns {string} 格式化后的价格字符串
 */
export function formatPrice(price, decimals = 2, currency = '¥') {
  if (typeof price !== 'number' || isNaN(price)) {
    return '';
  }
  
  return `${currency}${price.toFixed(decimals).replace(/\B(?=(\d{3})+(?!\d))/g, ',')}`;
}

/**
 * 格式化日期时间用于输入控件（避免时区转换问题）
 * @param {string|Date} date 日期时间对象或字符串
 * @returns {string} 格式化后的日期时间字符串 (YYYY-MM-DDTHH:mm:ss)
 */
export function formatDateForInput(date) {
  if (!date) return '';

  // 如果已经是正确格式的字符串，直接返回
  if (typeof date === 'string' && /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}$/.test(date)) {
    return date;
  }

  // 如果是其他格式的字符串，需要解析
  if (typeof date === 'string') {
    // 处理不同的日期字符串格式，避免时区转换
    const dateStr = date.replace(/\s/g, 'T'); // 将空格替换为T
    if (/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/.test(dateStr)) {
      return dateStr.slice(0, 19); // 截取到秒，去掉毫秒和时区信息
    }

    // 如果只有日期部分，添加默认时间
    if (/^\d{4}-\d{2}-\d{2}$/.test(date)) {
      return `${date}T00:00:00`;
    }
  }

  // 如果是Date对象，使用本地时间格式化
  const dateObj = date instanceof Date ? date : new Date(date);
  if (isNaN(dateObj.getTime())) return '';

  const year = dateObj.getFullYear();
  const month = String(dateObj.getMonth() + 1).padStart(2, '0');
  const day = String(dateObj.getDate()).padStart(2, '0');
  const hours = String(dateObj.getHours()).padStart(2, '0');
  const minutes = String(dateObj.getMinutes()).padStart(2, '0');
  const seconds = String(dateObj.getSeconds()).padStart(2, '0');

  return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}`;
}

/**
 * 格式化相对时间
 * @param {string|Date} date 日期字符串或Date对象
 * @returns {string} 相对时间字符串
 */
export function formatRelativeTime(date) {
  if (!date) return '';

  const d = new Date(date);
  if (isNaN(d.getTime())) return '';

  const now = new Date();
  const diffMs = d.getTime() - now.getTime();
  const diffDays = Math.ceil(diffMs / (1000 * 60 * 60 * 24));

  if (diffDays < 0) {
    const absDays = Math.abs(diffDays);
    if (absDays === 0) return '今天';
    if (absDays === 1) return '昨天';
    if (absDays < 7) return `${absDays}天前`;
    if (absDays < 30) return `${Math.floor(absDays / 7)}周前`;
    return formatDate(date, 'YYYY-MM-DD');
  } else if (diffDays === 0) {
    return '今天';
  } else if (diffDays === 1) {
    return '明天';
  } else if (diffDays < 7) {
    return `${diffDays}天后`;
  } else if (diffDays < 30) {
    return `${Math.floor(diffDays / 7)}周后`;
  } else {
    return formatDate(date, 'YYYY-MM-DD');
  }
}