// IT资产管理系统 - 工序实体
// 文件路径: /Models/Entities/Process.cs
// 功能: 定义工序实体，对应processes表

using System;
using System.Collections.Generic;

namespace ItAssetsSystem.Models.Entities
{
    /// <summary>
    /// 工序实体
    /// </summary>
    public class Process : IAuditableEntity
    {
        /// <summary>
        /// 工序ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 工序编码
        /// </summary>
        public string Code { get; set; }

        /// <summary>
        /// 工序名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 产线ID
        /// </summary>
        public int ProductionLineId { get; set; }

        /// <summary>
        /// 序号（在产线中的顺序）
        /// </summary>
        public int OrderNumber { get; set; }

        /// <summary>
        /// 状态（0:停用, 1:启用）
        /// </summary>
        public int Status { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; }

        /// <summary>
        /// 所属产线
        /// </summary>
        public virtual ProductionLine ProductionLine { get; set; }

        /// <summary>
        /// 关联的资产
        /// </summary>
        public virtual ICollection<Asset> Assets { get; set; }

        /// <summary>
        /// 关联的工位
        /// </summary>
        public virtual ICollection<Workstation> Workstations { get; set; }
    }
} 