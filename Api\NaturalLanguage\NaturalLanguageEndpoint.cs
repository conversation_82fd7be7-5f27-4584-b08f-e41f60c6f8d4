// IT资产管理系统 - 自然语言处理API端点
// 文件路径: /Api/NaturalLanguage/NaturalLanguageEndpoint.cs
// 功能: 提供自然语言处理API端点，用于非技术人员的领域特定语言接口

using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using ItAssetsSystem.Core.NaturalLanguage;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace ItAssetsSystem.Api.NaturalLanguage
{
    /// <summary>
    /// 自然语言命令请求
    /// </summary>
    public class CommandRequest
    {
        /// <summary>
        /// 命令文本
        /// </summary>
        public string Command { get; set; }
    }
    
    /// <summary>
    /// 命令建议请求
    /// </summary>
    public class SuggestionRequest
    {
        /// <summary>
        /// 部分输入
        /// </summary>
        public string PartialInput { get; set; }
    }
    
    /// <summary>
    /// 自然语言处理API端点
    /// </summary>
    [ApiController]
    [Route("api/natural-language")]
    public class NaturalLanguageEndpoint : ControllerBase
    {
        private readonly INaturalLanguageService _naturalLanguageService;
        private readonly ILogger<NaturalLanguageEndpoint> _logger;
        
        /// <summary>
        /// 构造函数
        /// </summary>
        public NaturalLanguageEndpoint(
            INaturalLanguageService naturalLanguageService,
            ILogger<NaturalLanguageEndpoint> logger)
        {
            _naturalLanguageService = naturalLanguageService;
            _logger = logger;
        }
        
        private string GetUserName()
        {
            // 从HttpContext中获取用户名
            if (this.HttpContext?.User?.Identity?.IsAuthenticated == true)
            {
                return this.HttpContext.User.Identity.Name;
            }
            return "anonymous";
        }
        
        /// <summary>
        /// 处理自然语言命令
        /// </summary>
        [HttpPost("process")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<IActionResult> ProcessCommandAsync([FromBody] CommandRequest request)
        {
            if (request == null || string.IsNullOrWhiteSpace(request.Command))
            {
                return new BadRequestObjectResult(new { error = "命令不能为空" });
            }
            
            _logger.LogInformation("处理自然语言命令: {Command}", request.Command);
            
            try
            {
                // 获取用户名
                string userName = GetUserName();
                
                var result = await _naturalLanguageService.ProcessCommandAsync(request.Command, userName);
                return new OkObjectResult(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理自然语言命令失败: {Command}", request.Command);
                return new StatusCodeResult(StatusCodes.Status500InternalServerError);
            }
        }
        
        /// <summary>
        /// 获取命令示例
        /// </summary>
        [HttpGet("examples")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> GetCommandExamplesAsync()
        {
            _logger.LogInformation("获取命令示例");
            
            try
            {
                var examples = await _naturalLanguageService.GetCommandExamplesAsync();
                return new OkObjectResult(examples);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取命令示例失败");
                return new StatusCodeResult(StatusCodes.Status500InternalServerError);
            }
        }
        
        /// <summary>
        /// 获取命令历史
        /// </summary>
        [HttpGet("history")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> GetCommandHistoryAsync([FromQuery] int count = 10)
        {
            _logger.LogInformation("获取命令历史: {Count}条", count);
            
            try
            {
                // 获取用户名
                string userName = GetUserName();
                
                var history = await _naturalLanguageService.GetCommandHistoryAsync(userName, count);
                return new OkObjectResult(history);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取命令历史失败");
                return new StatusCodeResult(StatusCodes.Status500InternalServerError);
            }
        }
        
        /// <summary>
        /// 获取命令建议
        /// </summary>
        [HttpPost("suggestions")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> GetCommandSuggestionsAsync([FromBody] SuggestionRequest request)
        {
            _logger.LogInformation("获取命令建议: {PartialInput}", request?.PartialInput ?? string.Empty);
            
            try
            {
                // 获取用户名
                string userName = GetUserName();
                
                var suggestions = await _naturalLanguageService.GetCommandSuggestionsAsync(
                    request?.PartialInput ?? string.Empty, 
                    userName);
                
                return new OkObjectResult(suggestions);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取命令建议失败");
                return new StatusCodeResult(StatusCodes.Status500InternalServerError);
            }
        }
    }
} 