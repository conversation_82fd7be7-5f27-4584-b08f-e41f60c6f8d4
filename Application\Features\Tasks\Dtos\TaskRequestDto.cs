#nullable enable
// File: Application/Features/Tasks/Dtos/TaskRequestDto.cs
// Description: 任务请求数据传输对象 (V2 更新)

using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
// using ItAssetsSystem.Models.Enums; // Review if still needed after DTO updates

namespace ItAssetsSystem.Application.Features.Tasks.Dtos
{
    /// <summary>
    /// 更新任务状态请求DTO (V2 Update)
    /// </summary>
    public class UpdateTaskStatusRequestDto
    {
        /// <summary>
        /// 新状态 (string)
        /// </summary>
        [Required(ErrorMessage = "任务状态不能为空")]
        [StringLength(50)]
        public string Status { get; set; } = string.Empty; // Was TaskStatus enum

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(1000, ErrorMessage = "备注不能超过1000个字符")]
        public string? Remarks { get; set; }
    }

    /// <summary>
    /// 更新任务进度请求DTO
    /// </summary>
    public class UpdateTaskProgressRequestDto
    {
        /// <summary>
        /// 新进度
        /// </summary>
        [Required(ErrorMessage = "任务进度不能为空")]
        [Range(0, 100, ErrorMessage = "进度必须在0-100之间")]
        public int Progress { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(1000, ErrorMessage = "备注不能超过1000个字符")]
        public string? Remarks { get; set; }
    }

    /// <summary>
    /// 分配任务请求DTO
    /// </summary>
    public class AssignTaskRequestDto
    {
        /// <summary>
        /// 负责人ID
        /// </summary>
        [Required(ErrorMessage = "负责人ID不能为空")]
        public int AssigneeUserId { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(1000, ErrorMessage = "备注不能超过1000个字符")]
        public string? Remarks { get; set; }
    }

    // TaskQueryParametersDto definition was here and is now removed.
    // The definition should solely exist in TaskQueryParametersDto.cs
} 