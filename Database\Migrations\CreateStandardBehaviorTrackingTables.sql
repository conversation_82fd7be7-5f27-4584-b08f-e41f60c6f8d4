-- =====================================================
-- MySQL标准化行为追踪表结构
-- 基于行业最佳实践设计
-- =====================================================

-- 1. 用户行为事件表（主表）
CREATE TABLE IF NOT EXISTS `user_behavior_events` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
    `user_id` INT NOT NULL COMMENT '用户ID',
    `action_type` VARCHAR(50) NOT NULL COMMENT '行为类型',
    `action_category` VARCHAR(20) NOT NULL COMMENT '行为分类',
    `reference_id` BIGINT NULL COMMENT '关联业务对象ID',
    `reference_table` VARCHAR(50) NULL COMMENT '关联业务表名',

    -- 奖励信息
    `points_earned` INT NOT NULL DEFAULT 0 COMMENT '获得积分',
    `coins_earned` INT NOT NULL DEFAULT 0 COMMENT '获得金币',
    `diamonds_earned` INT NOT NULL DEFAULT 0 COMMENT '获得钻石',
    `xp_earned` INT NOT NULL DEFAULT 0 COMMENT '获得经验值',

    -- 上下文信息
    `context_data` JSON NULL COMMENT '行为上下文数据',
    `session_id` VARCHAR(100) NULL COMMENT '会话ID',
    `ip_address` VARCHAR(45) NULL COMMENT 'IP地址',
    `user_agent` VARCHAR(500) NULL COMMENT '用户代理',

    -- 时间信息
    `timestamp` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '行为发生时间',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',

    -- 索引
    INDEX `idx_user_behavior_user_id` (`user_id`),
    INDEX `idx_user_behavior_action_type` (`action_type`),
    INDEX `idx_user_behavior_category` (`action_category`),
    INDEX `idx_user_behavior_timestamp` (`timestamp`),
    INDEX `idx_user_behavior_reference` (`reference_table`, `reference_id`),
    INDEX `idx_user_behavior_user_time` (`user_id`, `timestamp`),

    -- 外键约束
    FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户行为事件记录表';

-- 2. 用户行为统计汇总表（优化查询性能）
CREATE TABLE IF NOT EXISTS `user_behavior_stats` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
    `user_id` INT NOT NULL COMMENT '用户ID',
    `stat_date` DATE NOT NULL COMMENT '统计日期',
    `stat_period` ENUM('daily', 'weekly', 'monthly') NOT NULL COMMENT '统计周期',
    
    -- 任务相关统计
    `tasks_created` INT NOT NULL DEFAULT 0 COMMENT '创建任务数',
    `tasks_claimed` INT NOT NULL DEFAULT 0 COMMENT '领取任务数',
    `tasks_completed` INT NOT NULL DEFAULT 0 COMMENT '完成任务数',
    `tasks_commented` INT NOT NULL DEFAULT 0 COMMENT '评论任务数',
    
    -- 资产相关统计
    `assets_created` INT NOT NULL DEFAULT 0 COMMENT '创建资产数',
    `assets_updated` INT NOT NULL DEFAULT 0 COMMENT '更新资产数',
    `assets_transferred` INT NOT NULL DEFAULT 0 COMMENT '转移资产数',
    
    -- 故障相关统计
    `faults_recorded` INT NOT NULL DEFAULT 0 COMMENT '登记故障数',
    `faults_resolved` INT NOT NULL DEFAULT 0 COMMENT '解决故障数',
    
    -- 采购相关统计
    `purchases_created` INT NOT NULL DEFAULT 0 COMMENT '创建采购数',
    `purchases_updated` INT NOT NULL DEFAULT 0 COMMENT '更新采购数',
    
    -- 备件相关统计
    `parts_inbound` INT NOT NULL DEFAULT 0 COMMENT '备件入库数',
    `parts_outbound` INT NOT NULL DEFAULT 0 COMMENT '备件出库数',
    `parts_updated` INT NOT NULL DEFAULT 0 COMMENT '更新备件数',
    
    -- 奖励统计
    `total_points` INT NOT NULL DEFAULT 0 COMMENT '总积分',
    `total_coins` INT NOT NULL DEFAULT 0 COMMENT '总金币',
    `total_diamonds` INT NOT NULL DEFAULT 0 COMMENT '总钻石',
    `total_xp` INT NOT NULL DEFAULT 0 COMMENT '总经验值',
    
    -- 时间戳
    `last_updated` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- 索引
    INDEX `idx_user_stats_user_date` (`user_id`, `stat_date`),
    INDEX `idx_user_stats_period` (`stat_period`, `stat_date`),
    UNIQUE INDEX `idx_user_stats_unique` (`user_id`, `stat_date`, `stat_period`),
    
    -- 外键约束
    FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户行为统计汇总表';

-- 3. 行为类型配置表（标准化）
CREATE TABLE IF NOT EXISTS `behavior_type_configs` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `action_type` VARCHAR(50) NOT NULL UNIQUE COMMENT '行为类型代码',
    `action_category` VARCHAR(20) NOT NULL COMMENT '行为分类',
    `display_name` VARCHAR(100) NOT NULL COMMENT '显示名称',
    `description` TEXT NULL COMMENT '行为描述',
    
    -- 奖励配置
    `base_points` INT NOT NULL DEFAULT 0 COMMENT '基础积分',
    `base_coins` INT NOT NULL DEFAULT 0 COMMENT '基础金币',
    `base_diamonds` INT NOT NULL DEFAULT 0 COMMENT '基础钻石',
    `base_xp` INT NOT NULL DEFAULT 0 COMMENT '基础经验值',
    
    -- 规则配置
    `multiplier` DECIMAL(3,2) NOT NULL DEFAULT 1.00 COMMENT '奖励倍数',
    `daily_limit` INT NULL COMMENT '每日限制次数',
    `is_active` BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否启用',
    
    -- 时间戳
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- 索引
    INDEX `idx_behavior_config_category` (`action_category`),
    INDEX `idx_behavior_config_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='行为类型配置表';

-- 插入标准行为类型配置
INSERT IGNORE INTO `behavior_type_configs`
(`action_type`, `action_category`, `display_name`, `description`, `base_points`, `base_coins`, `base_diamonds`, `base_xp`)
VALUES
-- 任务相关
('TASK_CREATED', 'TASK', '任务创建', '创建新任务', 20, 10, 0, 30),
('TASK_CLAIMED', 'TASK', '任务领取', '领取任务', 10, 5, 0, 15),
('TASK_COMPLETED', 'TASK', '任务完成', '完成任务', 50, 25, 1, 75),
('TASK_COMMENTED', 'TASK', '任务评论', '添加任务评论', 5, 2, 0, 8),

-- 资产相关
('ASSET_CREATED', 'ASSET', '资产创建', '创建新资产', 30, 15, 0, 45),
('ASSET_UPDATED', 'ASSET', '资产更新', '更新资产信息', 15, 8, 0, 22),
('ASSET_TRANSFERRED', 'ASSET', '资产转移', '资产转移操作', 20, 10, 0, 30),

-- 故障相关
('FAULT_RECORDED', 'FAULT', '故障登记', '登记故障', 25, 12, 0, 38),
('FAULT_RESOLVED', 'FAULT', '故障解决', '解决故障', 40, 20, 1, 60),

-- 采购相关
('PURCHASE_CREATED', 'PURCHASE', '采购创建', '创建采购订单', 25, 12, 0, 38),
('PURCHASE_UPDATED', 'PURCHASE', '采购更新', '更新采购订单', 15, 8, 0, 22),

-- 备件相关
('SPAREPART_INBOUND', 'SPAREPART', '备件入库', '备件入库操作', 15, 8, 0, 22),
('SPAREPART_OUTBOUND', 'SPAREPART', '备件出库', '备件出库操作', 12, 6, 0, 18),
('SPAREPART_UPDATED', 'SPAREPART', '备件更新', '更新备件信息', 10, 5, 0, 15);
