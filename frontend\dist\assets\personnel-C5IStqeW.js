import{C as e,aV as a}from"./index-CG5lHOPO.js";const t={getDepartmentList:e=>a.get("/Department",{params:e}).then((e=>({data:e}))).catch((e=>({data:{success:!1,message:e.message||"服务器错误"}}))),getDepartments(e){return this.getDepartmentList(e)},getDepartmentTree:()=>a.get("/Department/tree").then((e=>({data:e}))).catch((e=>({data:{success:!1,message:e.message||"服务器错误"}}))),getDepartmentDetail:e=>a.get(`/Department/${e}`).then((e=>({data:e}))).catch((e=>({data:{success:!1,message:e.message||"服务器错误"}}))),createDepartment(e){const t={name:e.name,code:e.code,description:e.description||"",parentId:e.parentId||null,managerId:e.managerId||null,deputyManagerId:e.deputy<PERSON>anagerId||null,isActive:"active"===e.status};return a.post("/Department",t).then((e=>({data:e}))).catch((e=>{var a,t;return{data:{success:!1,message:(null==(t=null==(a=e.response)?void 0:a.data)?void 0:t.message)||e.message||"服务器错误"}}}))},updateDepartment(e,t){const s={name:t.name,code:t.code,description:t.description||"",parentId:t.parentId||null,managerId:t.managerId||null,deputyManagerId:t.deputyManagerId||null,isActive:"active"===t.status};return a.put(`/Department/${e}`,s).then((e=>({data:e}))).catch((e=>{var a,t;return{data:{success:!1,message:(null==(t=null==(a=e.response)?void 0:a.data)?void 0:t.message)||e.message||"服务器错误"}}}))},deleteDepartment:e=>a.delete(`/Department/${e}`).then((e=>({data:e}))).catch((e=>({data:{success:!1,message:e.message||"服务器错误"}}))),updateLocationDepartment:(e,t)=>a.put(`/Location/${e}/department`,t).then((e=>({data:e}))).catch((e=>({data:{success:!1,message:e.message||"服务器错误"}}))),getDepartmentUsers:e=>a.get(`/Department/${e}/users`).then((e=>({data:e}))).catch((e=>({data:{success:!1,message:e.message||"服务器错误"}})))},s={getPersonnelList:(e={})=>a.get("/Personnel",{params:e}).then((e=>e&&"object"==typeof e&&"success"in e?{data:e}:{data:{success:!0,data:Array.isArray(e)?e:(null==e?void 0:e.data)||[],message:"获取成功"}})).catch((e=>{var a,t;return{data:{success:!1,data:[],message:(null==(t=null==(a=e.response)?void 0:a.data)?void 0:t.message)||e.message||"服务器错误"}}})),getPersonnelById:e=>a.get(`/Personnel/${e}`).then((e=>({data:e}))).catch((e=>({data:{success:!1,message:e.message||"服务器错误"}}))),createPersonnel(e){const t={name:e.name,position:e.position||"",contact:e.contact||"",departmentId:e.departmentId,employeeCode:e.employeeCode||""};return a.post("/Personnel",t).then((e=>({data:e}))).catch((e=>{var a,t;return{data:{success:!1,message:(null==(t=null==(a=e.response)?void 0:a.data)?void 0:t.message)||e.message||"服务器错误"}}}))},updatePersonnel(e,t){const s={name:t.name,position:t.position||"",contact:t.contact||"",departmentId:t.departmentId,employeeCode:t.employeeCode||""},n=`/Personnel/${e}`;return a.put(n,s).then((e=>({data:e}))).catch((e=>{var a,t;return{data:{success:!1,message:(null==(t=null==(a=e.response)?void 0:a.data)?void 0:t.message)||e.message||"服务器错误"}}}))},deletePersonnel(e){const t=`/Personnel/${e}`;return a.delete(t).then((e=>({data:e}))).catch((e=>{var a,t;return{data:{success:!1,message:(null==(t=null==(a=e.response)?void 0:a.data)?void 0:t.message)||e.message||"服务器错误"}}}))}};export{t as d,s as p};
