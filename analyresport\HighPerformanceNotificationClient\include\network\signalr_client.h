// SignalR客户端实现
#pragma once

#include "common/types.h"
#include <string>
#include <memory>
#include <functional>
#include <thread>
#include <atomic>
#include <mutex>
#include <queue>

namespace notification {

// 前向声明
class Logger;

// SignalR连接状态
enum class SignalRConnectionState {
    DISCONNECTED,
    CONNECTING,
    CONNECTED,
    RECON<PERSON>CTING,
    FAILED
};

// SignalR消息类型
enum class SignalRMessageType {
    NEGOTIATION,
    HANDSHAKE,
    INVOCATION,
    STREAM_ITEM,
    COMPLETION,
    CANCEL,
    PING,
    CLOSE
};

// SignalR调用结果
struct SignalRInvocationResult {
    bool success = false;
    String result;
    String error;
    int invocation_id = 0;
};

class SignalRClient {
public:
    SignalRClient();
    ~SignalRClient();

    // 连接管理
    bool connect(const String& url, const String& access_token = "");
    void disconnect();
    bool isConnected() const { return connection_state_.load() == SignalRConnectionState::CONNECTED; }
    SignalRConnectionState getConnectionState() const { return connection_state_.load(); }

    // 消息发送
    bool sendMessage(const String& method, const String& data);
    bool invokeMethod(const String& method, const String& args = "", int timeout_ms = 5000);
    
    // 事件订阅
    void on(const String& method, std::function<void(const String&)> callback);
    void off(const String& method);

    // 回调设置
    void setConnectionStateCallback(std::function<void(SignalRConnectionState)> callback);
    void setErrorCallback(std::function<void(const String&)> callback);

    // 重连控制
    void enableAutoReconnect(bool enable = true);
    void setReconnectInterval(Duration interval);

private:
    // 连接状态管理
    void setConnectionState(SignalRConnectionState state);
    
    // 协议处理
    bool performNegotiation();
    bool performHandshake();
    bool processMessage(const String& message);
    
    // WebSocket消息处理
    void onWebSocketOpen();
    void onWebSocketMessage(const String& message);
    void onWebSocketClose();
    void onWebSocketError(const String& error);

    // 自动重连
    void startReconnection();
    void stopReconnection();
    void reconnectionWorker();

    // 心跳机制
    void startHeartbeat();
    void stopHeartbeat();
    void heartbeatWorker();

    // 消息解析
    struct ParsedMessage {
        SignalRMessageType type;
        String target;
        String arguments;
        int invocation_id = 0;
        String error;
        bool success = true;
    };
    
    ParsedMessage parseMessage(const String& message);
    String createInvocationMessage(const String& method, const String& args, int invocation_id);
    String createPingMessage();

    // 内部状态
    std::atomic<SignalRConnectionState> connection_state_{SignalRConnectionState::DISCONNECTED};
    std::atomic<bool> auto_reconnect_enabled_{true};
    std::atomic<bool> stop_threads_{false};

    // 连接信息
    String hub_url_;
    String access_token_;
    String connection_token_;
    String connection_id_;
    String negotiation_response_;

    // WebSocket相关
    void* websocket_handle_ = nullptr;  // 平台相关的WebSocket句柄

    // 回调函数
    std::mutex callback_mutex_;
    std::map<String, std::function<void(const String&)>> method_callbacks_;
    std::function<void(SignalRConnectionState)> connection_state_callback_;
    std::function<void(const String&)> error_callback_;

    // 调用管理
    std::mutex invocation_mutex_;
    std::map<int, SignalRInvocationResult> pending_invocations_;
    std::atomic<int> next_invocation_id_{1};

    // 工作线程
    std::thread reconnection_thread_;
    std::thread heartbeat_thread_;
    Duration reconnect_interval_{std::chrono::seconds(5)};
    Duration heartbeat_interval_{std::chrono::seconds(30)};

    // 日志
    std::shared_ptr<Logger> logger_;

    // 平台相关的WebSocket实现
    bool createWebSocketConnection(const String& url);
    void closeWebSocketConnection();
    bool sendWebSocketMessage(const String& message);

    // HTTP客户端（用于协商）
    struct HttpResponse {
        int status_code = 0;
        String body;
        String error;
        bool success = false;
    };
    
    HttpResponse makeHttpRequest(const String& url, const String& method, 
                                const String& data = "", const String& content_type = "application/json");
};

} // namespace notification