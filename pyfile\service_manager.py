import subprocess
import argparse
import os
import sys

def get_pids_by_dir(target_dir, process_name):
    """通过目录匹配进程（支持通配符路径）"""
    try:
        # 预处理路径中的反斜杠
        sanitized_dir = target_dir.replace('\\', '\\\\')

        # 使用格式化字符串构建PowerShell脚本
        ps_script = f'''
        $targetDir = "{sanitized_dir}"
        Get-WmiObject Win32_Process -Filter "Name='{process_name}'" | 
        Where-Object {{$_.ExecutablePath -like '*'+$targetDir+'*'}} | 
        Select-Object -ExpandProperty ProcessId
        '''

        result = subprocess.run(
            ['powershell', '-Command', ps_script],
            capture_output=True, text=True, encoding='utf-8'
        )
        return [pid.strip() for pid in result.stdout.splitlines() if pid.strip()]
    except Exception as e:
        print(f"进程检测失败: {e}")
        return []

def restart_dotnet():
    dir_path = r'E:\ItAssetsSystem\singleit'
    if not os.path.exists(dir_path):
        print(f"错误：目录 {dir_path} 不存在")
        return

    # 终止旧进程
    pids = get_pids_by_dir(dir_path, 'dotnet.exe')
    if pids:
        print(f"正在终止 {len(pids)} 个旧dotnet进程...")
        for pid in pids:
            subprocess.run(['taskkill', '/F', '/PID', pid], shell=True)
    else:
        print("未找到旧进程")

    # 启动新进程
    print("正在启动新dotnet实例...")
    try:
        subprocess.Popen(
            ['dotnet', 'run', '--no-launch-profile'],
            cwd=dir_path,
            creationflags=subprocess.DETACHED_PROCESS | subprocess.CREATE_NEW_PROCESS_GROUP
        )
        print(f"dotnet 服务在 {dir_path} 已重启！")
    except Exception as e:
        print(f"启动失败: {e}")

def run_npm_dev():
    dir_path = r'E:\ItAssetsSystem\singleit\frontend'
    if not os.path.exists(dir_path):
        print(f"错误：目录 {dir_path} 不存在")
        return

    # 终止相关进程
    node_pids = get_pids_by_dir(dir_path, 'node.exe')
    npm_pids = get_pids_by_dir(dir_path, 'npm.cmd')

    all_pids = node_pids + npm_pids
    if all_pids:
        print(f"正在终止 {len(all_pids)} 个旧进程...")
        for pid in all_pids:
            subprocess.run(['taskkill', '/F', '/PID', pid], shell=True)
    else:
        print("未找到旧进程")

    # 启动新进程
    print("正在启动前端开发服务器...")
    try:
        # 保存当前工作目录
        original_cwd = os.getcwd()
        # 切换到指定目录
        os.chdir(dir_path)
        subprocess.Popen(
            ['npm', 'run', 'dev'],
            creationflags=subprocess.DETACHED_PROCESS | subprocess.CREATE_NEW_PROCESS_GROUP
        )
        # 切换回原来的工作目录
        os.chdir(original_cwd)
        print("前端开发服务器已启动！")
    except Exception as e:
        print(f"启动失败: {e}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="独立服务控制脚本")
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument('--dotnet', action='store_true',
                       help='重启 E:\ItAssetsSystem\singleit 下的dotnet服务')
    group.add_argument('--npm', action='store_true',
                       help='重启 E:\ItAssetsSystem\singleit\frontend 下的前端服务')
    args = parser.parse_args()

    if args.dotnet:
        restart_dotnet()
    elif args.npm:
        run_npm_dev()