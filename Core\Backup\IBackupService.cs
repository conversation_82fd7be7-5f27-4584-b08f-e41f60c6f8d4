// IT资产管理系统 - 备份服务接口
// 文件路径: /Core/Backup/IBackupService.cs
// 功能: 定义数据备份与恢复的核心服务接口

using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ItAssetsSystem.Core.Backup
{
    /// <summary>
    /// 备份服务接口
    /// </summary>
    public interface IBackupService
    {
        /// <summary>
        /// 获取备份历史列表
        /// </summary>
        Task<List<BackupItem>> GetBackupHistoryAsync();
        
        /// <summary>
        /// 创建手动备份
        /// </summary>
        /// <param name="name">备份名称</param>
        /// <param name="description">备份描述</param>
        Task<BackupItem> CreateManualBackupAsync(string name, string description);
        
        /// <summary>
        /// 从备份恢复数据
        /// </summary>
        /// <param name="backupId">备份ID</param>
        Task<bool> RestoreFromBackupAsync(string backupId);
        
        /// <summary>
        /// 删除备份
        /// </summary>
        /// <param name="backupId">备份ID</param>
        Task<bool> DeleteBackupAsync(string backupId);
        
        /// <summary>
        /// 获取备份设置
        /// </summary>
        Task<BackupSettings> GetBackupSettingsAsync();
        
        /// <summary>
        /// 更新备份设置
        /// </summary>
        /// <param name="settings">备份设置</param>
        Task<bool> UpdateBackupSettingsAsync(BackupSettings settings);
        
        /// <summary>
        /// 执行自动备份
        /// </summary>
        Task<BackupItem> ExecuteAutomaticBackupAsync();
    }
} 