/**
 * 航空航天级IT资产管理系统 - 道具背包面板组件
 * 文件路径: src/components/Tasks/ItemInventoryPanel.vue
 * 功能描述: 显示用户拥有的游戏道具
 */

<template>
  <div class="item-inventory-panel">
    <!-- 道具分类标签页 -->
    <el-tabs v-model="activeTab" class="item-tabs">
      <el-tab-pane label="所有道具" name="all">
        <div class="category-description">
          所有可用的道具
        </div>
      </el-tab-pane>
      <el-tab-pane label="效率道具" name="efficiency">
        <div class="category-description">
          提高工作效率的道具
        </div>
      </el-tab-pane>
      <el-tab-pane label="防御道具" name="defense">
        <div class="category-description">
          降低任务失败惩罚的道具
        </div>
      </el-tab-pane>
      <el-tab-pane label="特殊道具" name="special">
        <div class="category-description">
          具有独特功能的特殊道具
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 道具列表 -->
    <div class="items-grid" v-loading="loading">
      <el-empty v-if="filteredItems.length === 0" description="暂无道具" />
      
      <div v-for="item in filteredItems" :key="item.id" class="item-card">
        <div class="item-image" :class="'rarity-' + item.rarity">
          <el-image 
            :src="item.image || '/images/items/' + item.id + '.png'" 
            fit="contain"
            :alt="item.name"
            fallback-src="/images/items/default.png"
          />
          <div class="item-count">x{{ item.count }}</div>
        </div>
        
        <div class="item-content">
          <div class="item-header">
            <span class="item-name">{{ item.name }}</span>
            <el-tag size="small" :type="getItemRarityType(item.rarity)">{{ getItemRarityLabel(item.rarity) }}</el-tag>
          </div>
          
          <div class="item-description">{{ item.description }}</div>
          
          <div class="item-actions">
            <el-button 
              type="primary" 
              size="small" 
              @click="useItem(item)"
              :disabled="item.count <= 0"
            >
              使用
            </el-button>
            
            <el-button 
              type="info" 
              plain
              size="small" 
              @click="showItemDetail(item)"
            >
              详情
            </el-button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 使用道具对话框 -->
    <el-dialog 
      v-model="useItemDialogVisible" 
      :title="`使用道具: ${selectedItem?.name}`"
      width="400px"
    >
      <div class="use-item-dialog">
        <div class="use-item-info">
          <p>确定要使用 {{ selectedItem?.name }} 吗？</p>
          <p class="use-item-effect">{{ selectedItem?.description }}</p>
        </div>
        
        <template v-if="selectedItem?.needsTarget">
          <div class="use-item-target">
            <p>请选择使用目标：</p>
            <el-select v-model="useTarget" placeholder="选择目标">
              <el-option
                v-for="target in useTargets"
                :key="target.id"
                :label="target.name"
                :value="target.id"
              />
            </el-select>
          </div>
        </template>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="useItemDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmUseItem">确认使用</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { taskApi } from '@/api/task'

// 状态变量
const activeTab = ref('all')
const items = ref([])
const loading = ref(false)
const useItemDialogVisible = ref(false)
const selectedItem = ref(null)
const useTarget = ref(null)
const useTargets = ref([
  { id: 1, name: '进行中任务1' },
  { id: 2, name: '进行中任务2' },
  { id: 3, name: '进行中任务3' }
])

// 道具稀有度映射
const rarityMap = {
  1: { label: '普通', type: 'info' },
  2: { label: '稀有', type: '' },
  3: { label: '史诗', type: 'warning' },
  4: { label: '传说', type: 'danger' }
}

// 根据当前标签筛选道具
const filteredItems = computed(() => {
  if (activeTab.value === 'all') {
    return items.value
  }
  return items.value.filter(item => item.category === activeTab.value)
})

// 获取道具稀有度标签
const getItemRarityLabel = (rarity) => {
  return rarityMap[rarity]?.label || '普通'
}

// 获取道具稀有度类型
const getItemRarityType = (rarity) => {
  return rarityMap[rarity]?.type || 'info'
}

// 获取用户道具列表
const fetchUserItems = async () => {
  loading.value = true
  try {
    const res = await taskApi.getUserItems()
    if (res.success) {
      items.value = res.data || []
    } else {
      ElMessage.error('获取道具列表失败')
    }
  } catch (error) {
    console.error('获取道具列表失败', error)
    ElMessage.error('获取道具列表失败')
    
    // 临时使用模拟数据
    items.value = [
      {
        id: 1,
        name: '青铜弹头',
        description: '可抵消一次任务逾期惩罚，保留50%积分',
        count: 3,
        image: '',
        rarity: 1,
        category: 'defense'
      },
      {
        id: 2,
        name: '白银弹头',
        description: '可抵消一次任务逾期惩罚，保留75%积分',
        count: 2,
        image: '',
        rarity: 2,
        category: 'defense'
      },
      {
        id: 3,
        name: '效率药水',
        description: '使用后下一个任务完成可获得双倍积分',
        count: 1,
        image: '',
        rarity: 2,
        category: 'efficiency'
      },
      {
        id: 4,
        name: '时间沙漏',
        description: '可为一个任务延长24小时截止时间',
        count: 1,
        image: '',
        rarity: 3,
        category: 'special',
        needsTarget: true
      }
    ]
  } finally {
    loading.value = false
  }
}

// 使用道具
const useItem = (item) => {
  selectedItem.value = item
  useTarget.value = null
  useItemDialogVisible.value = true
}

// 确认使用道具
const confirmUseItem = async () => {
  if (!selectedItem.value) return
  
  const params = {}
  if (selectedItem.value.needsTarget && useTarget.value) {
    params.targetId = useTarget.value
  }
  
  try {
    // const res = await taskApi.useItem(selectedItem.value.id, params)
    // if (res.success) {
      // 更新本地数据
      const index = items.value.findIndex(i => i.id === selectedItem.value.id)
      if (index !== -1) {
        items.value[index].count--
      }
      
      ElMessage.success(`成功使用道具：${selectedItem.value.name}`)
      useItemDialogVisible.value = false
      
      // 触发父组件事件
      emit('use-item', {
        itemId: selectedItem.value.id,
        itemName: selectedItem.value.name,
        targetId: useTarget.value
      })
    // } else {
    //   ElMessage.error(res.message || '使用道具失败')
    // }
  } catch (error) {
    console.error('使用道具失败', error)
    ElMessage.error('使用道具失败')
  }
}

// 显示道具详情
const showItemDetail = (item) => {
  ElMessage.info(`道具 ${item.name} 的详细信息`)
  // 可以实现更复杂的道具详情展示
}

// 定义组件事件
const emit = defineEmits(['use-item'])

// 初始化
onMounted(() => {
  fetchUserItems()
})
</script>

<style lang="scss" scoped>
.item-inventory-panel {
  .item-tabs {
    margin-bottom: 20px;
    
    .category-description {
      color: #909399;
      font-size: 14px;
      margin: 8px 0 16px;
    }
  }
  
  .items-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 16px;
    
    .item-card {
      display: flex;
      background: #fff;
      border-radius: 8px;
      padding: 16px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
      transition: all 0.3s;
      
      &:hover {
        transform: translateY(-3px);
        box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
      }
      
      .item-image {
        width: 64px;
        height: 64px;
        position: relative;
        margin-right: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 8px;
        background: #f5f7fa;
        overflow: hidden;
        
        .el-image {
          width: 48px;
          height: 48px;
        }
        
        .item-count {
          position: absolute;
          bottom: 0;
          right: 0;
          background: rgba(0, 0, 0, 0.6);
          color: white;
          font-size: 12px;
          padding: 2px 6px;
          border-top-left-radius: 6px;
        }
        
        &.rarity-1 {
          background: linear-gradient(135deg, #e9e9eb 0%, #b9b9bb 100%);
        }
        
        &.rarity-2 {
          background: linear-gradient(135deg, #3c8ce7 0%, #00eaff 100%);
        }
        
        &.rarity-3 {
          background: linear-gradient(135deg, #d3ad64 0%, #fee101 100%);
        }
        
        &.rarity-4 {
          background: linear-gradient(135deg, #ff6a37 0%, #ff0844 100%);
        }
      }
      
      .item-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        
        .item-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;
          
          .item-name {
            font-weight: 600;
            font-size: 16px;
            color: #303133;
          }
        }
        
        .item-description {
          color: #606266;
          font-size: 14px;
          margin-bottom: 12px;
          flex: 1;
        }
        
        .item-actions {
          display: flex;
          gap: 8px;
        }
      }
    }
  }
  
  .use-item-dialog {
    .use-item-info {
      margin-bottom: 16px;
      
      .use-item-effect {
        color: #606266;
        font-size: 14px;
        margin-top: 8px;
      }
    }
    
    .use-item-target {
      margin-top: 16px;
      
      .el-select {
        width: 100%;
        margin-top: 8px;
      }
    }
  }
}
</style> 