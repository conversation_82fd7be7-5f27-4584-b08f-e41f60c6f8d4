// File: Application/Features/SpareParts/Dtos/SparePartTransactionQuery.cs
// Description: 备品备件出入库记录查询参数类

using System;
using ItAssetsSystem.Application.Common.Dtos;

namespace ItAssetsSystem.Application.Features.SpareParts.Dtos
{
    /// <summary>
    /// 备品备件出入库记录查询参数类
    /// </summary>
    public class SparePartTransactionQuery : PaginationQuery
    {
        /// <summary>
        /// 关键字（支持备件名称、编号、关联单号）
        /// </summary>
        public string Keyword { get; set; }
        
        /// <summary>
        /// 备件ID
        /// </summary>
        public long? PartId { get; set; }
        
        /// <summary>
        /// 库位ID
        /// </summary>
        public long? LocationId { get; set; }
        
        /// <summary>
        /// 库位区域
        /// </summary>
        public string LocationArea { get; set; }
        
        /// <summary>
        /// 操作类型: 1=入库, 2=出库
        /// </summary>
        public byte? Type { get; set; }
        
        /// <summary>
        /// 原因类型: 1=采购入库, 2=退回入库, 3=领用出库, 4=报废出库, 5=盘点调整
        /// </summary>
        public byte? ReasonType { get; set; }
        
        /// <summary>
        /// 操作人ID
        /// </summary>
        public int? UserId { get; set; }
        
        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime? StartTime { get; set; }
        
        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime? EndTime { get; set; }
        
        /// <summary>
        /// 排序字段（operationtime/quantity/type）
        /// </summary>
        public new string SortBy { get; set; }
        
        /// <summary>
        /// 排序方向（asc/desc）
        /// </summary>
        public new string SortDirection { get; set; } = "desc";
    }
} 