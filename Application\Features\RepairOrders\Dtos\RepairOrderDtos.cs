// File: Application/Features/RepairOrders/Dtos/RepairOrderDtos.cs
// Description: 维修单相关DTO定义

#nullable enable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using ItAssetsSystem.Domain.Entities;

namespace ItAssetsSystem.Application.Features.RepairOrders.Dtos
{
    /// <summary>
    /// 创建维修单请求DTO
    /// </summary>
    public class CreateRepairOrderRequestDto
    {
        /// <summary>
        /// ID（创建时为null）
        /// </summary>
        public int? Id { get; set; }

        /// <summary>
        /// 维修标题
        /// </summary>
        [Required(ErrorMessage = "维修标题不能为空")]
        [StringLength(200, ErrorMessage = "维修标题不能超过200个字符")]
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// 维修描述
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// 优先级（1=紧急, 2=高, 3=中, 4=低）
        /// </summary>
        [Range(1, 4, ErrorMessage = "优先级必须在1-4之间")]
        public int Priority { get; set; } = 3;

        /// <summary>
        /// 供应商ID
        /// </summary>
        [Required(ErrorMessage = "供应商ID不能为空")]
        public int SupplierId { get; set; }

        /// <summary>
        /// 关联故障ID
        /// </summary>
        public int? FaultId { get; set; }

        /// <summary>
        /// 预估费用
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "预估费用不能为负数")]
        public decimal? EstimatedCost { get; set; }

        /// <summary>
        /// 预估天数
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "预估天数不能为负数")]
        public int? EstimatedDays { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(500, ErrorMessage = "备注不能超过500个字符")]
        public string? Notes { get; set; }

        /// <summary>
        /// 维修项目列表
        /// </summary>
        public List<CreateRepairItemDto> RepairItems { get; set; } = new List<CreateRepairItemDto>();
    }

    /// <summary>
    /// 创建维修项目DTO
    /// </summary>
    public class CreateRepairItemDto
    {
        /// <summary>
        /// 备件ID
        /// </summary>
        [Required(ErrorMessage = "备件ID不能为空")]
        public int PartId { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        [Range(1, int.MaxValue, ErrorMessage = "数量必须大于0")]
        public int Quantity { get; set; } = 1;

        /// <summary>
        /// 故障描述
        /// </summary>
        public string? FaultDescription { get; set; }

        /// <summary>
        /// 序列号
        /// </summary>
        public string? SerialNumber { get; set; }
    }

    /// <summary>
    /// 维修单响应DTO
    /// </summary>
    public class RepairOrderDto
    {
        public int Id { get; set; }
        public string OrderCode { get; set; } = string.Empty;
        public string? Title { get; set; }
        public string? Description { get; set; }
        public RepairPriority Priority { get; set; }
        public string PriorityName { get; set; } = string.Empty;
        public int SupplierId { get; set; }
        public string SupplierName { get; set; } = string.Empty;
        public int? FaultId { get; set; }
        public int? AssetId { get; set; }
        public DateTime? SendDate { get; set; }
        public DateTime? ExpectedReturnDate { get; set; }
        public DateTime? ActualReturnDate { get; set; }
        public decimal? EstimatedCost { get; set; }
        public decimal TotalCost { get; set; }
        public RepairOrderStatus Status { get; set; }
        public string StatusName { get; set; } = string.Empty;
        public string? Notes { get; set; }
        public string? RepairResult { get; set; }
        public int CreatorId { get; set; }
        public string CreatorName { get; set; } = string.Empty;
        public int? ApproverId { get; set; }
        public string? ApproverName { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
        public List<RepairItemDto> RepairItems { get; set; } = new List<RepairItemDto>();
    }

    /// <summary>
    /// 维修项目响应DTO
    /// </summary>
    public class RepairItemDto
    {
        public int Id { get; set; }
        public int RepairOrderId { get; set; }
        public int AssetId { get; set; }
        public string AssetName { get; set; } = string.Empty;
        public int? FaultRecordId { get; set; }
        public string? Description { get; set; }
        public decimal RepairCost { get; set; }
        public int RepairStatus { get; set; }
        public string RepairStatusName { get; set; } = string.Empty;
        public string? RepairResult { get; set; }
    }

    /// <summary>
    /// 维修单查询参数DTO
    /// </summary>
    public class RepairOrderQueryParametersDto
    {
        /// <summary>
        /// 页码
        /// </summary>
        public int PageNumber { get; set; } = 1;

        /// <summary>
        /// 每页大小
        /// </summary>
        public int PageSize { get; set; } = 20;

        /// <summary>
        /// 搜索关键词
        /// </summary>
        public string? SearchTerm { get; set; }

        /// <summary>
        /// 状态过滤
        /// </summary>
        public RepairOrderStatus? Status { get; set; }

        /// <summary>
        /// 优先级过滤
        /// </summary>
        public RepairPriority? Priority { get; set; }

        /// <summary>
        /// 供应商ID过滤
        /// </summary>
        public int? SupplierId { get; set; }

        /// <summary>
        /// 创建者ID过滤
        /// </summary>
        public int? CreatorId { get; set; }

        /// <summary>
        /// 开始日期过滤
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// 结束日期过滤
        /// </summary>
        public DateTime? EndDate { get; set; }
    }

    /// <summary>
    /// 更新维修单请求DTO
    /// </summary>
    public class UpdateRepairOrderRequestDto
    {
        public string? Title { get; set; }
        public string? Description { get; set; }
        public RepairPriority? Priority { get; set; }
        public int? SupplierId { get; set; }
        public DateTime? SendDate { get; set; }
        public DateTime? ExpectedReturnDate { get; set; }
        public DateTime? ActualReturnDate { get; set; }
        public decimal? EstimatedCost { get; set; }
        public decimal? TotalCost { get; set; }
        public RepairOrderStatus? Status { get; set; }
        public string? Notes { get; set; }
        public string? RepairResult { get; set; }
    }
}