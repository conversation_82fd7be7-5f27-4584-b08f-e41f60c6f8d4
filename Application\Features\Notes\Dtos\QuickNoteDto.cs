#nullable enable
// File: Application/Features/Notes/Dtos/QuickNoteDto.cs
// Description: DTO representing a quick note for API responses.

using System;

namespace ItAssetsSystem.Application.Features.Notes.Dtos
{
    public class QuickNoteDto
    {
        public long Id { get; set; } // Use simpler name 'Id' in DTO
        public int UserId { get; set; }
        public string Content { get; set; } = string.Empty;
        public bool IsPinned { get; set; }
        public string? Color { get; set; } 
        public DateTime CreatedAt { get; set; }
        public DateTime LastUpdatedAt { get; set; }
        
        // Optional: Include basic user info if needed, fetched separately or via projection
        // public string? UserName { get; set; }
    }
} 