-- =====================================================
-- IT资产管理系统 - 游戏化系统完整性验证脚本
-- 文件名: GamificationSystemValidation.sql
-- 创建时间: 2025-01-21
-- 功能描述: 验证游戏化系统的数据库结构、数据完整性和功能可用性
-- =====================================================

-- 设置字符集
SET NAMES utf8mb4;

-- =====================================================
-- 1. 数据库结构验证
-- =====================================================

SELECT '=== 数据库结构验证 ===' AS CheckSection;

-- 检查游戏化相关表是否存在
SELECT 
    'Table Existence Check' AS CheckType,
    table_name AS TableName,
    CASE 
        WHEN table_name IS NOT NULL THEN 'EXISTS'
        ELSE 'MISSING'
    END AS Status
FROM (
    SELECT 'achievements' AS expected_table
    UNION SELECT 'behavior_types'
    UNION SELECT 'gamification_log'
    UNION SELECT 'gamification_userstats'
    UNION SELECT 'user_achievements'
    UNION SELECT 'user_levels'
    UNION SELECT 'task_claims'
    UNION SELECT 'tasks'
    UNION SELECT 'users'
) expected
LEFT JOIN information_schema.tables t 
    ON expected.expected_table = t.table_name 
    AND t.table_schema = DATABASE()
ORDER BY expected.expected_table;

-- 检查关键字段是否存在
SELECT 
    'Field Existence Check' AS CheckType,
    CONCAT(table_name, '.', column_name) AS FieldName,
    data_type AS DataType,
    is_nullable AS IsNullable,
    'EXISTS' AS Status
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
  AND (
    (table_name = 'tasks' AND column_name IN ('CompletedByUserId', 'CompletedAt', 'CreatorUserId', 'AssigneeUserId'))
    OR (table_name = 'gamification_userstats' AND column_name IN ('CoreUserId', 'PointsBalance', 'CoinsBalance', 'DiamondsBalance'))
    OR (table_name = 'gamification_log' AND column_name IN ('UserId', 'EventType', 'PointsGained', 'CoinsGained'))
    OR (table_name = 'task_claims' AND column_name IN ('claimed_by', 'task_id', 'claim_status'))
  )
ORDER BY table_name, column_name;

-- =====================================================
-- 2. 数据完整性验证
-- =====================================================

SELECT '=== 数据完整性验证 ===' AS CheckSection;

-- 检查用户数据
SELECT 
    'User Data Check' AS CheckType,
    COUNT(*) AS TotalUsers,
    COUNT(CASE WHEN IsActive = 1 THEN 1 END) AS ActiveUsers,
    COUNT(CASE WHEN IsActive = 0 THEN 1 END) AS InactiveUsers
FROM users;

-- 检查任务数据
SELECT 
    'Task Data Check' AS CheckType,
    COUNT(*) AS TotalTasks,
    COUNT(CASE WHEN Status = 'Done' THEN 1 END) AS CompletedTasks,
    COUNT(CASE WHEN Status = 'Done' AND CompletedByUserId IS NOT NULL THEN 1 END) AS CompletedWithUser,
    COUNT(CASE WHEN Status = 'Done' AND CompletedAt IS NOT NULL THEN 1 END) AS CompletedWithTime
FROM tasks;

-- 检查游戏化统计数据
SELECT 
    'Gamification Stats Check' AS CheckType,
    COUNT(*) AS TotalUserStats,
    COUNT(CASE WHEN PointsBalance > 0 THEN 1 END) AS UsersWithPoints,
    COUNT(CASE WHEN CompletedTasksCount > 0 THEN 1 END) AS UsersWithCompletedTasks,
    AVG(PointsBalance) AS AvgPoints,
    MAX(PointsBalance) AS MaxPoints
FROM gamification_userstats;

-- 检查游戏化日志数据
SELECT 
    'Gamification Log Check' AS CheckType,
    COUNT(*) AS TotalLogEntries,
    COUNT(DISTINCT EventType) AS UniqueEventTypes,
    COUNT(CASE WHEN PointsGained > 0 THEN 1 END) AS PositivePointsEntries,
    SUM(PointsGained) AS TotalPointsAwarded
FROM gamification_log;

-- 检查行为类型数据
SELECT 
    'Behavior Types Check' AS CheckType,
    COUNT(*) AS TotalBehaviorTypes,
    COUNT(CASE WHEN IsActive = 1 THEN 1 END) AS ActiveBehaviorTypes,
    GROUP_CONCAT(DISTINCT Category) AS Categories
FROM behavior_types;

-- =====================================================
-- 3. 功能可用性验证
-- =====================================================

SELECT '=== 功能可用性验证 ===' AS CheckSection;

-- 检查视图是否存在并可用
SELECT 
    'View Availability Check' AS CheckType,
    table_name AS ViewName,
    'EXISTS' AS Status
FROM information_schema.views 
WHERE table_schema = DATABASE() 
  AND table_name LIKE '%gamification%' 
  OR table_name LIKE '%leaderboard%'
  OR table_name LIKE '%achievement%'
ORDER BY table_name;

-- 检查存储过程是否存在
SELECT 
    'Stored Procedure Check' AS CheckType,
    routine_name AS ProcedureName,
    routine_type AS Type,
    'EXISTS' AS Status
FROM information_schema.routines 
WHERE routine_schema = DATABASE() 
  AND (routine_name LIKE '%gamification%' 
       OR routine_name LIKE '%leaderboard%'
       OR routine_name LIKE '%UpdateUserStats%')
ORDER BY routine_name;

-- 检查触发器状态
SELECT 
    'Trigger Status Check' AS CheckType,
    trigger_name AS TriggerName,
    event_object_table AS TableName,
    action_timing AS Timing,
    event_manipulation AS Event,
    'EXISTS' AS Status
FROM information_schema.triggers 
WHERE trigger_schema = DATABASE() 
  AND (trigger_name LIKE '%task%' 
       OR trigger_name LIKE '%gamification%'
       OR trigger_name LIKE '%claim%')
ORDER BY trigger_name;

-- =====================================================
-- 4. 数据一致性验证
-- =====================================================

SELECT '=== 数据一致性验证 ===' AS CheckSection;

-- 检查用户统计与实际任务数据的一致性
SELECT 
    'Task Count Consistency' AS CheckType,
    gus.CoreUserId,
    u.Name AS UserName,
    gus.CompletedTasksCount AS StatsCompletedTasks,
    COALESCE(actual.CompletedTasks, 0) AS ActualCompletedTasks,
    CASE 
        WHEN gus.CompletedTasksCount = COALESCE(actual.CompletedTasks, 0) THEN 'CONSISTENT'
        ELSE 'INCONSISTENT'
    END AS ConsistencyStatus
FROM gamification_userstats gus
LEFT JOIN users u ON gus.CoreUserId = u.Id
LEFT JOIN (
    SELECT 
        CompletedByUserId,
        COUNT(*) AS CompletedTasks
    FROM tasks 
    WHERE Status = 'Done' AND CompletedByUserId IS NOT NULL
    GROUP BY CompletedByUserId
) actual ON gus.CoreUserId = actual.CompletedByUserId
WHERE u.IsActive = 1
ORDER BY ConsistencyStatus DESC, gus.CoreUserId
LIMIT 10;

-- 检查积分余额与日志记录的一致性
SELECT 
    'Points Balance Consistency' AS CheckType,
    gus.UserId,
    gus.CoreUserId,
    u.Name AS UserName,
    gus.PointsBalance AS StatsPointsBalance,
    COALESCE(log_points.TotalPoints, 0) AS LogTotalPoints,
    CASE 
        WHEN gus.PointsBalance = COALESCE(log_points.TotalPoints, 0) THEN 'CONSISTENT'
        ELSE 'INCONSISTENT'
    END AS ConsistencyStatus
FROM gamification_userstats gus
LEFT JOIN users u ON gus.CoreUserId = u.Id
LEFT JOIN (
    SELECT 
        UserId,
        SUM(PointsGained) AS TotalPoints
    FROM gamification_log
    GROUP BY UserId
) log_points ON gus.UserId = log_points.UserId
WHERE u.IsActive = 1
ORDER BY ConsistencyStatus DESC, gus.CoreUserId
LIMIT 10;

-- =====================================================
-- 5. 系统配置验证
-- =====================================================

SELECT '=== 系统配置验证 ===' AS CheckSection;

-- 检查游戏化配置（如果存在配置表）
SELECT 
    'Configuration Check' AS CheckType,
    ConfigKey,
    ConfigValue,
    Category,
    IsActive,
    UpdatedAt
FROM gamification_config
WHERE IsActive = 1
ORDER BY Category, ConfigKey;

-- =====================================================
-- 6. 性能指标验证
-- =====================================================

SELECT '=== 性能指标验证 ===' AS CheckSection;

-- 检查表大小和索引使用情况
SELECT 
    'Table Size Check' AS CheckType,
    table_name AS TableName,
    table_rows AS EstimatedRows,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS SizeMB,
    ROUND((index_length / 1024 / 1024), 2) AS IndexSizeMB
FROM information_schema.tables 
WHERE table_schema = DATABASE() 
  AND table_name IN ('gamification_log', 'gamification_userstats', 'tasks', 'task_claims', 'users')
ORDER BY SizeMB DESC;

-- =====================================================
-- 7. 最终验证总结
-- =====================================================

SELECT '=== 验证总结 ===' AS CheckSection;

-- 生成验证报告
SELECT 
    'Validation Summary' AS ReportType,
    CASE 
        WHEN (
            SELECT COUNT(*) FROM information_schema.tables 
            WHERE table_schema = DATABASE() 
              AND table_name IN ('achievements', 'behavior_types', 'gamification_log', 'gamification_userstats', 'user_achievements', 'user_levels')
        ) >= 6 THEN 'PASS'
        ELSE 'FAIL'
    END AS DatabaseStructure,
    
    CASE 
        WHEN (
            SELECT COUNT(*) FROM gamification_userstats WHERE PointsBalance >= 0
        ) = (
            SELECT COUNT(*) FROM gamification_userstats
        ) THEN 'PASS'
        ELSE 'FAIL'
    END AS DataIntegrity,
    
    CASE 
        WHEN (
            SELECT COUNT(*) FROM behavior_types WHERE IsActive = 1
        ) >= 5 THEN 'PASS'
        ELSE 'FAIL'
    END AS GameRules,
    
    NOW() AS ValidationTime;

-- 显示推荐的下一步操作
SELECT 
    'Next Steps' AS ActionType,
    CASE 
        WHEN NOT EXISTS (SELECT 1 FROM gamification_userstats LIMIT 1) THEN '1. 初始化用户游戏化统计数据'
        WHEN (SELECT COUNT(*) FROM behavior_types WHERE IsActive = 1) < 5 THEN '2. 添加更多游戏化规则'
        WHEN EXISTS (SELECT 1 FROM tasks WHERE Status = "Done" AND CompletedByUserId IS NULL LIMIT 1) THEN '3. 修复任务完成用户数据'
        ELSE '4. 系统验证通过，可以正常使用'
    END AS RecommendedAction;

SELECT 'GamificationSystemValidation.sql 验证完成！' AS Message;
