#!/usr/bin/env python3
"""
IT Assets Management System 项目结构分析工具
"""
import os
import sys
from collections import defaultdict
import xml.etree.ElementTree as ET
from datetime import datetime
from typing import Optional, List  # 添加类型注解支持

# --- 配置常量 ---
PROJECT_METADATA = {
    "project_name": "IT Assets Management System",
    "repository_url": "https://github.com/yourorg/it-assets-backend",
    "last_updated": "2025-04-15",
    "technology_stack": [
        "ASP.NET Core 8",
        "Entity Framework Core",
        "SQL Server",
        "Redis",
        "Docker"
    ]
}

BACKEND_TARGETS = [
    'Api', 'Configurations', 'Controllers', 'Core', 'Data',
    'Infrastructure', 'Migrations', 'Models', 'Plugins',
    'Properties', 'Services', 'appsettings.json',
    'appsettings.Development.json','Application','Domain'
]

OUTPUT_FILENAME = 'PROJECT_STRUCTURE_ANALYSIS.md'
WORKSPACE_ROOT = '.' 

# --- 忽略规则 ---
IGNORE_DIRS = [
    '.git', 'bin', 'obj', '.idea', '.vscode', 'node_modules',
    '__pycache__', '.pytest_cache', 'dist', '.pnpm', '.cursor',
    'packages', 'artifacts', 'TestResults', 'logs'
]

IGNORE_FILE_PATTERNS = [
    '*.dll', '*.pdb', '*.exe', '*.cache', '*.log', '*.suo',
    '*.user', '*.tmp', '*.bak', '*.swp', 'package-lock.json',
    '*.min.*'
]

# --- 关键文件识别 ---
KEY_FILES = {
    "appsettings.json": "主配置文件",
    "appsettings.*.json": "环境配置文件",
    "Program.cs": "应用启动入口",
    "Startup.cs": "服务配置(旧模板)",
    "Dockerfile": "容器化配置",
    "*.csproj": "项目定义文件",
    "DbContext.cs": "数据库上下文",
    "launchSettings.json": "调试配置文件"
}

# --- 架构概览 ---
ARCHITECTURE_DIAGRAM = """## 项目架构概览

```plaintext
ITAssets.Backend/
├── Controllers/        - API端点 (RESTful)
├── Services/           - 业务逻辑层
│   ├── Interfaces/     - 服务接口
│   └── Implementations/ - 服务实现
├── Data/               - 数据访问层
│   ├── Repositories/   - 存储库模式实现
│   ├── Models/         - 数据模型 (EF Core)
│   └── Migrations/     - 数据库迁移
├── Core/               - 核心共享组件
│   ├── Exceptions/     - 自定义异常
│   ├── Extensions/     - 扩展方法
│   └── Utilities/      - 工具类
├── Infrastructure/     - 基础设施实现
│   ├── Auth/           - JWT认证授权
│   ├── Caching/        - Redis缓存
│   └── Email/          - 邮件服务
├── Configurations/     - 配置管理
│   └── AutoMapper/     - 对象映射配置
├── Properties/         - 项目属性
│   └── launchSettings.json - 启动配置
├── appsettings.json    - 主配置文件
└── Program.cs          - 应用入口
```"""

# --- 初始化数据结构 ---
file_data = {}
empty_dirs = []
ignored_items = {"dirs": [], "files": []}
file_type_stats = defaultdict(lambda: {"count": 0, "size": 0})
project_dependencies = {}
key_files_analysis = {}

# --- 路径处理 ---
output_file_path = os.path.abspath(os.path.join(WORKSPACE_ROOT, OUTPUT_FILENAME))
workspace_abs_root = os.path.abspath(WORKSPACE_ROOT)

def should_ignore_file(filename: str) -> bool:
    """检查文件是否匹配忽略模式"""
    return any(filename.endswith(pattern.replace('*', '')) 
               for pattern in IGNORE_FILE_PATTERNS)

def is_key_file(filepath: str) -> Optional[str]:  # 修改返回类型注解
    """识别关键文件并返回描述"""
    filename = os.path.basename(filepath)
    for pattern, description in KEY_FILES.items():
        if pattern.startswith('*') and filename.endswith(pattern[1:]): 
            return description
        elif filename == pattern:
            return description
    return None

def analyze_csproj(file_path: str) -> List[str]:  # 修改返回类型注解
    """分析.csproj文件内容"""
    try:
        root = ET.parse(file_path).getroot()
        dependencies = []
        
        # 分析包引用
        for pkg_ref in root.findall(".//PackageReference"):
            pkg_name = pkg_ref.get('Include')
            pkg_version = pkg_ref.get('Version')
            dependencies.append(f"{pkg_name} (v{pkg_version})")
        
        # 分析项目引用
        for proj_ref in root.findall(".//ProjectReference"):
            ref_path = proj_ref.get('Include')
            dependencies.append(f"Project: {os.path.basename(ref_path)}")
        
        return dependencies
    except ET.ParseError as e:
        return [f"XML解析错误: {str(e)}"]
    except Exception as e:
        return [f"分析错误: {str(e)}"]

def read_and_store_file(file_path_abs: str) -> None:
    """读取文件内容并存储，同时收集元数据"""
    file_path_relative = os.path.relpath(file_path_abs, workspace_abs_root)
    
    # 文件类型统计
    ext = os.path.splitext(file_path_abs)[1].lower() or "no_extension"
    try:
        file_size = os.path.getsize(file_path_abs)
    except OSError as e:
        print(f"  错误：获取文件大小失败 {file_path_relative}: {e}")
        return
    
    file_type_stats[ext]["count"] += 1
    file_type_stats[ext]["size"] += file_size
    
    # 关键文件分析
    if key_desc := is_key_file(file_path_relative):
        key_files_analysis[file_path_relative] = key_desc
        
        # 特殊文件处理
        if file_path_relative.endswith('.csproj'):
            try:
                dependencies = analyze_csproj(file_path_abs)
                project_dependencies[file_path_relative] = dependencies
            except Exception as e:
                project_dependencies[file_path_relative] = [f"读取失败: {str(e)}"]
    
    # 读取文件内容
    try:
        with open(file_path_abs, 'r', encoding='utf-8') as f:
            content = f.read()
        file_data[file_path_relative] = content
        print(f"  读取文件: {file_path_relative} (UTF-8)")
    except UnicodeDecodeError:
        try:
            with open(file_path_abs, 'r', encoding=sys.getdefaultencoding()) as f:
                content = f.read()
            file_data[file_path_relative] = content
            print(f"  读取文件: {file_path_relative} ({sys.getdefaultencoding()})")
        except Exception as e:
            print(f"  错误：无法读取文件 {file_path_relative}: {e}")
            file_data[file_path_relative] = f"Error reading file: {e}"
    except Exception as e:
        print(f"  错误：读取文件 {file_path_relative} 时发生未知错误: {e}")
        file_data[file_path_relative] = f"Error reading file: {e}"

def main():
    """主分析流程"""
    print("=== 开始项目结构分析 ===")
    print(f"项目名称: {PROJECT_METADATA['project_name']}")
    print(f"分析目标: {', '.join(BACKEND_TARGETS)}")
    print(f"忽略目录: {', '.join(IGNORE_DIRS)}")
    print(f"忽略文件模式: {', '.join(IGNORE_FILE_PATTERNS)}\n")

    for target in BACKEND_TARGETS:
        target_path_abs = os.path.abspath(os.path.join(workspace_abs_root, target))
        target_path_relative = os.path.relpath(target_path_abs, workspace_abs_root)

        if os.path.isdir(target_path_abs):
            print(f"\n--- 遍历目录: {target_path_relative} ---")
            for root, dirs, files in os.walk(target_path_abs, topdown=True):
                # 处理忽略目录
                for dir_name in list(dirs):
                    full_dir_path = os.path.join(root, dir_name)
                    rel_dir_path = os.path.relpath(full_dir_path, workspace_abs_root)
                    
                    if dir_name in IGNORE_DIRS:
                        dirs.remove(dir_name)
                        ignored_items["dirs"].append(rel_dir_path)
                        print(f"  忽略目录: {rel_dir_path}")
                        continue
                
                # 处理文件
                for filename in files:
                    file_path_abs = os.path.join(root, filename)
                    rel_file_path = os.path.relpath(file_path_abs, workspace_abs_root)
                    
                    if should_ignore_file(filename):
                        ignored_items["files"].append(rel_file_path)
                        print(f"  忽略文件: {rel_file_path}")
                        continue
                        
                    read_and_store_file(file_path_abs)
                
                # 检查空目录
                if not files and not dirs:
                    rel_dir_path = os.path.relpath(root, workspace_abs_root)
                    if rel_dir_path not in empty_dirs:
                        empty_dirs.append(rel_dir_path)
                        print(f"  发现空目录: {rel_dir_path}")

        elif os.path.isfile(target_path_abs):
            if should_ignore_file(os.path.basename(target_path_abs)):
                print(f"  忽略文件: {target_path_relative}")
                ignored_items["files"].append(target_path_relative)
            else:
                print(f"\n--- 读取文件: {target_path_relative} ---")
                read_and_store_file(target_path_abs)
        else:
            print(f"警告: 目标 '{target_path_relative}' 不存在")

    # 生成报告
    generate_report()

def generate_report():
    """生成分析报告"""
    print(f"\n=== 生成分析报告: {OUTPUT_FILENAME} ===")

    try:
        with open(output_file_path, 'w', encoding='utf-8') as outfile:
            # 项目元数据
            outfile.write(f"# {PROJECT_METADATA['project_name']} 项目结构分析报告\n\n")
            outfile.write(f"**生成日期**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            outfile.write(f"**代码仓库**: [{PROJECT_METADATA['repository_url']}]({PROJECT_METADATA['repository_url']})\n")
            outfile.write(f"**技术栈**: {', '.join(PROJECT_METADATA['technology_stack'])}\n\n")
            
            # 架构概览
            outfile.write(ARCHITECTURE_DIAGRAM + "\n\n")
            
            # 分析统计
            total_files = sum(stats['count'] for stats in file_type_stats.values())
            outfile.write("## 分析统计\n")
            outfile.write(f"- 扫描目录总数: {len(BACKEND_TARGETS)}\n")
            outfile.write(f"- 分析文件总数: {total_files}\n")
            outfile.write(f"- 发现空目录数: {len(empty_dirs)}\n")
            outfile.write(f"- 忽略项目总数: {len(ignored_items['dirs']) + len(ignored_items['files'])}\n\n")
            
            # 文件类型统计
            outfile.write("### 文件类型统计\n")
            outfile.write("| 类型 | 文件数 | 总大小 |\n")
            outfile.write("|------|--------|--------|\n")
            for ext, stats in sorted(file_type_stats.items()):
                size_mb = stats['size'] / (1024 * 1024)
                ext_display = ext if ext else '无扩展名'
                outfile.write(f"| `{ext_display}` | {stats['count']} | {size_mb:.2f} MB |\n")
            outfile.write("\n")
            
            # 关键文件分析
            if key_files_analysis:
                outfile.write("## 关键文件分析\n")
                outfile.write("| 文件路径 | 描述 |\n")
                outfile.write("|----------|------|\n")
                for path, desc in key_files_analysis.items():
                    outfile.write(f"| `{path}` | {desc} |\n")
                outfile.write("\n")
            
            # 项目依赖
            if project_dependencies:
                outfile.write("## 项目依赖分析\n")
                for project, deps in project_dependencies.items():
                    outfile.write(f"### `{project}`\n")
                    outfile.write("```\n")
                    outfile.write("\n".join(deps))
                    outfile.write("\n```\n\n")
            
            # 忽略项目列表
            outfile.write("## 忽略项目列表\n")
            if ignored_items["dirs"]:
                outfile.write("### 忽略目录\n")
                for dir_path in sorted(ignored_items["dirs"]):
                    outfile.write(f"- `{dir_path}`\n")
            
            if ignored_items["files"]:
                outfile.write("\n### 忽略文件\n")
                for file_path in sorted(ignored_items["files"]):
                    outfile.write(f"- `{file_path}`\n")
            outfile.write("\n")
            
            # 空目录列表
            if empty_dirs:
                outfile.write("## 空目录列表\n")
                outfile.write("> **注意**: 这些目录当前为空，请确认是否需要保留\n\n")
                for dir_path in sorted(empty_dirs):
                    outfile.write(f"- `{dir_path}`\n")
                outfile.write("\n")
            
            # 文件内容详情
            outfile.write("## 文件内容详情\n")
            for path, content in sorted(file_data.items()):
                normalized_path = path.replace('\\', '/')
                
                # 添加文件类型标签
                file_ext = os.path.splitext(path)[1]
                lang = {
                    '.cs': 'csharp',
                    '.json': 'json',
                    '.js': 'javascript',
                    '.html': 'html',
                    '.csproj': 'xml'
                }.get(file_ext, '')
                
                outfile.write(f"### `{normalized_path}`\n\n")
                
                if lang:
                    outfile.write(f"```{lang}\n")
                else:
                    outfile.write("```\n")
                
                outfile.write(content.rstrip() + "\n")
                outfile.write("```\n\n")
        
        print(f"=== 报告生成成功: {os.path.relpath(output_file_path)} ===")
    except Exception as e:
        print(f"错误：无法写入报告文件: {e}")

if __name__ == "__main__":
    main()