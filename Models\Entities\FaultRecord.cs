// IT资产管理系统 - 故障记录实体
// 文件路径: /Models/Entities/FaultRecord.cs
// 功能: 定义故障记录实体，对应faultrecords表

using System;
using ItAssetsSystem.Models;

namespace ItAssetsSystem.Models.Entities
{
    /// <summary>
    /// 故障记录实体
    /// </summary>
    public class FaultRecord : IAuditableEntity
    {
        /// <summary>
        /// 故障记录ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 资产ID（线下设备可为空）
        /// </summary>
        public int? AssetId { get; set; }

        /// <summary>
        /// 故障类型ID
        /// </summary>
        public int FaultTypeId { get; set; }

        /// <summary>
        /// 故障标题
        /// </summary>
        public string Title { get; set; }

        /// <summary>
        /// 故障描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 位置ID
        /// </summary>
        public int? LocationId { get; set; }

        /// <summary>
        /// 报告人ID
        /// </summary>
        public int ReporterId { get; set; }

        /// <summary>
        /// 报告时间
        /// </summary>
        public DateTime ReportTime { get; set; }

        /// <summary>
        /// 状态（0:待处理, 1:处理中, 2:已修复, 3:无法修复）
        /// </summary>
        public int Status { get; set; }

        /// <summary>
        /// 严重程度
        /// </summary>
        public int Severity { get; set; }

        /// <summary>
        /// 处理人ID
        /// </summary>
        public int? AssigneeId { get; set; }

        /// <summary>
        /// 分配时间
        /// </summary>
        public DateTime? AssignTime { get; set; }

        /// <summary>
        /// 响应时间
        /// </summary>
        public DateTime? ResponseTime { get; set; }

        /// <summary>
        /// 解决时间
        /// </summary>
        public DateTime? ResolutionTime { get; set; }

        /// <summary>
        /// 解决方案
        /// </summary>
        public string Resolution { get; set; }

        /// <summary>
        /// 根本原因
        /// </summary>
        public string RootCause { get; set; }
        
        /// <summary>
        /// 是否返厂
        /// </summary>
        public bool IsReturned { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Notes { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; }

        /// <summary>
        /// 资产
        /// </summary>
        public virtual Asset Asset { get; set; }

        /// <summary>
        /// 故障类型
        /// </summary>
        public virtual FaultType FaultType { get; set; }

        /// <summary>
        /// 位置
        /// </summary>
        public virtual Location Location { get; set; }

        /// <summary>
        /// 报告人
        /// </summary>
        public virtual User Reporter { get; set; }

        /// <summary>
        /// 处理人
        /// </summary>
        public virtual User Assignee { get; set; }
    }
} 