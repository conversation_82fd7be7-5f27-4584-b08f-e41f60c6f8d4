using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ItAssetsSystem.Models.Gamification
{
    /// <summary>
    /// 用户等级配置表
    /// </summary>
    [Table("user_levels")]
    public class UserLevel
    {
        [Key]
        [Column("level")]
        public int Level { get; set; }

        [Required]
        [Column("name")]
        [StringLength(50)]
        public string Name { get; set; }

        [Column("required_xp")]
        public int RequiredXp { get; set; }

        [Column("reward_coins")]
        public int RewardCoins { get; set; } = 0;

        [Column("reward_diamonds")]
        public int RewardDiamonds { get; set; } = 0;

        [Column("unlock_features")]
        public string UnlockFeatures { get; set; }

        [Column("icon_url")]
        [StringLength(255)]
        public string IconUrl { get; set; }

        [Column("color")]
        [StringLength(7)]
        public string Color { get; set; }

        [Column("created_at")]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        [Column("updated_at")]
        public DateTime UpdatedAt { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 用户等级历史表
    /// </summary>
    [Table("user_level_history")]
    public class UserLevelHistory
    {
        [Key]
        [Column("id")]
        public long Id { get; set; }

        [Column("user_id")]
        public int UserId { get; set; }

        [Column("old_level")]
        public int? OldLevel { get; set; }

        [Column("new_level")]
        public int NewLevel { get; set; }

        [Column("level_up_time")]
        public DateTime LevelUpTime { get; set; } = DateTime.Now;

        [Column("rewards_granted")]
        public string RewardsGranted { get; set; }

        [Column("xp_at_levelup")]
        public int? XpAtLevelup { get; set; }
    }

    /// <summary>
    /// 道具配置表
    /// </summary>
    [Table("gamification_items")]
    public class GamificationItem
    {
        [Key]
        [Column("item_id")]
        public long ItemId { get; set; }

        [Required]
        [Column("name")]
        [StringLength(100)]
        public string Name { get; set; }

        [Required]
        [Column("code")]
        [StringLength(50)]
        public string Code { get; set; }

        [Required]
        [Column("type")]
        [StringLength(50)]
        public string Type { get; set; }

        [Column("rarity")]
        [StringLength(20)]
        public string Rarity { get; set; } = "Common";

        [Column("effect")]
        public string Effect { get; set; }

        [Column("effect_duration")]
        public int? EffectDuration { get; set; }

        [Column("drop_rate")]
        public decimal DropRate { get; set; } = 0.01m;

        [Column("icon_url")]
        [StringLength(255)]
        public string IconUrl { get; set; }

        [Column("is_active")]
        public bool IsActive { get; set; } = true;

        [Column("created_at")]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        [Column("updated_at")]
        public DateTime UpdatedAt { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 用户道具背包表
    /// </summary>
    [Table("user_items")]
    public class UserItem
    {
        [Key]
        [Column("id")]
        public long Id { get; set; }

        [Column("user_id")]
        public int UserId { get; set; }

        [Column("item_id")]
        public long ItemId { get; set; }

        [Column("quantity")]
        public int Quantity { get; set; } = 1;

        [Column("obtained_at")]
        public DateTime ObtainedAt { get; set; } = DateTime.Now;

        [Column("obtained_source")]
        [StringLength(50)]
        public string ObtainedSource { get; set; }

        // 导航属性
        [ForeignKey("ItemId")]
        public virtual GamificationItem Item { get; set; }
    }

    /// <summary>
    /// 道具效果状态表
    /// </summary>
    [Table("active_item_effects")]
    public class ActiveItemEffect
    {
        [Key]
        [Column("id")]
        public long Id { get; set; }

        [Column("user_id")]
        public int UserId { get; set; }

        [Column("item_id")]
        public long ItemId { get; set; }

        [Required]
        [Column("effect_type")]
        [StringLength(50)]
        public string EffectType { get; set; }

        [Column("multiplier")]
        public decimal Multiplier { get; set; } = 1.0m;

        [Column("expires_at")]
        public DateTime? ExpiresAt { get; set; }

        [Column("created_at")]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        // 导航属性
        [ForeignKey("ItemId")]
        public virtual GamificationItem Item { get; set; }
    }

    /// <summary>
    /// 每日限制表
    /// </summary>
    [Table("daily_limits")]
    public class DailyLimit
    {
        [Column("user_id")]
        public int UserId { get; set; }

        [Column("limit_date")]
        public DateTime LimitDate { get; set; }

        [Column("points_earned")]
        public int PointsEarned { get; set; } = 0;

        [Column("max_points")]
        public int MaxPoints { get; set; } = 200;

        [Column("items_obtained")]
        public int ItemsObtained { get; set; } = 0;

        [Column("max_items")]
        public int MaxItems { get; set; } = 5;

        [Column("tasks_completed")]
        public int TasksCompleted { get; set; } = 0;

        [Column("created_at")]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        [Column("updated_at")]
        public DateTime UpdatedAt { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 扩展现有UserStats实体
    /// </summary>
    public partial class UserStats
    {
        [Column("current_level")]
        public int? CurrentLevel { get; set; } = 1;

        [Column("level_progress")]
        public decimal? LevelProgress { get; set; } = 0.00m;

        [Column("total_items_obtained")]
        public int? TotalItemsObtained { get; set; } = 0;

        [Column("rare_items_count")]
        public int? RareItemsCount { get; set; } = 0;

        [Column("epic_items_count")]
        public int? EpicItemsCount { get; set; } = 0;

        [Column("legendary_items_count")]
        public int? LegendaryItemsCount { get; set; } = 0;
    }

    /// <summary>
    /// 用户成长时间线视图模型
    /// </summary>
    public class UserGrowthTimeline
    {
        public int UserId { get; set; }
        public string UserName { get; set; }
        public DateTime TimelineDate { get; set; }
        public string EventType { get; set; }
        public int EventCount { get; set; }
        public int PointsEarned { get; set; }
        public int XpEarned { get; set; }
        public int CoinsEarned { get; set; }
        public int DiamondsEarned { get; set; }
        public string Milestones { get; set; }
    }

    /// <summary>
    /// 增强排行榜视图模型
    /// </summary>
    public class EnhancedLeaderboard
    {
        public int UserId { get; set; }
        public string UserName { get; set; }
        public string DepartmentName { get; set; }
        public int TotalPoints { get; set; }
        public int CurrentLevel { get; set; }
        public string LevelName { get; set; }
        public string LevelColor { get; set; }
        public int TasksCompleted { get; set; }
        public int TotalItemsObtained { get; set; }
        public int SpecialItemsCount { get; set; }
        public int PointsRank { get; set; }
        public int LevelRank { get; set; }
        public int TasksRank { get; set; }
    }

    /// <summary>
    /// 道具使用请求模型
    /// </summary>
    public class UseItemRequest
    {
        public long ItemId { get; set; }
        public int Quantity { get; set; } = 1;
    }

    /// <summary>
    /// 等级信息响应模型
    /// </summary>
    public class LevelInfoResponse
    {
        public int CurrentLevel { get; set; }
        public string CurrentLevelName { get; set; }
        public string CurrentLevelColor { get; set; }
        public int CurrentXp { get; set; }
        public int NextLevelXp { get; set; }
        public decimal Progress { get; set; }
        public string NextLevelName { get; set; }
        public bool IsMaxLevel { get; set; }
        public string[] UnlockedFeatures { get; set; }
    }

    /// <summary>
    /// 道具掉落通知模型
    /// </summary>
    public class ItemDropNotification
    {
        public long ItemId { get; set; }
        public string ItemName { get; set; }
        public string Rarity { get; set; }
        public string IconUrl { get; set; }
        public string Message { get; set; }
        public DateTime ObtainedAt { get; set; }
    }

    /// <summary>
    /// 升级通知模型
    /// </summary>
    public class LevelUpNotification
    {
        public int OldLevel { get; set; }
        public int NewLevel { get; set; }
        public string LevelName { get; set; }
        public string LevelColor { get; set; }
        public int RewardCoins { get; set; }
        public int RewardDiamonds { get; set; }
        public string[] UnlockedFeatures { get; set; }
        public DateTime LevelUpTime { get; set; }
    }
}
