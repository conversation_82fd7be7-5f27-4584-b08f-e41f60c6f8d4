<template>
  <div class="reward-panel">
    <div class="reward-header">
      <div class="reward-icon">
        <el-icon class="icon"><component :is="getRewardIcon(reward.type)" /></el-icon>
      </div>
      <div class="reward-title">{{ reward.title }}</div>
    </div>
    
    <div class="reward-message">
      {{ reward.message }}
    </div>
    
    <div class="reward-content">
      <div class="reward-section points-section">
        <div class="section-title">获得积分</div>
        <div class="points-value">
          <el-icon><Star /></el-icon>
          <span>+{{ reward.points }}</span>
        </div>
      </div>
      
      <template v-if="reward.items && reward.items.length > 0">
        <el-divider />
        <div class="reward-section items-section">
          <div class="section-title">获得道具</div>
          <div class="items-container">
            <div 
              v-for="(item, index) in reward.items" 
              :key="index"
              class="reward-item"
            >
              <div class="item-icon">
                <img :src="getItemIcon(item.name)" :alt="item.name">
              </div>
              <div class="item-info">
                <div class="item-name">{{ item.name }}</div>
                <div class="item-count">x{{ item.count }}</div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </div>
    
    <div class="reward-footer">
      <el-button type="primary" @click="handleClose">
        领取奖励
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { Star, Trophy, Check, Position, Magic } from '@element-plus/icons-vue'

const props = defineProps({
  reward: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['close'])

// 根据奖励类型获取对应图标
const getRewardIcon = (type) => {
  const icons = {
    'task_create': 'Position',
    'task_claim': 'Position',
    'task_update': 'Check',
    'task_complete': 'Trophy',
    'sign_in': 'Magic'
  }
  return icons[type] || 'Star'
}

// 根据道具名称获取图标URL
const getItemIcon = (name) => {
  // 在实际项目中，应该根据道具名称返回对应的图标URL
  // 这里使用简单的示例逻辑
  const iconMap = {
    '青铜弹头': '/images/items/bronze_bullet.png',
    '白银弹头': '/images/items/silver_bullet.png',
    '黄金弹头': '/images/items/gold_bullet.png',
    '加速券': '/images/items/speed_ticket.png',
    '工时缩减卡': '/images/items/time_card.png'
  }
  
  // 返回图标URL或默认图标
  return iconMap[name] || '/images/items/default.png'
}

// 关闭奖励面板
const handleClose = () => {
  emit('close')
}
</script>

<style lang="scss" scoped>
.reward-panel {
  width: 100%;
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  
  .reward-header {
    background: linear-gradient(135deg, #ffd256 0%, #ffc107 100%);
    padding: 24px;
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #5e3a00;
    
    .reward-icon {
      background: #fff;
      width: 80px;
      height: 80px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 16px;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
      
      .icon {
        font-size: 40px;
        color: #f7ba2a;
      }
    }
    
    .reward-title {
      font-size: 24px;
      font-weight: 600;
      text-align: center;
    }
  }
  
  .reward-message {
    padding: 20px 24px;
    font-size: 16px;
    color: #606266;
    text-align: center;
    border-bottom: 1px solid #ebeef5;
  }
  
  .reward-content {
    padding: 24px;
    
    .reward-section {
      margin-bottom: 20px;
      
      .section-title {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
        margin-bottom: 16px;
      }
      
      &.points-section {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .points-value {
          display: flex;
          align-items: center;
          color: #f7ba2a;
          font-size: 28px;
          font-weight: 700;
          
          .el-icon {
            margin-right: 8px;
          }
        }
      }
      
      &.items-section {
        .items-container {
          display: flex;
          flex-wrap: wrap;
          gap: 16px;
          
          .reward-item {
            display: flex;
            align-items: center;
            background: #f5f7fa;
            border-radius: 8px;
            padding: 12px;
            width: calc(50% - 8px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            transition: transform 0.2s, box-shadow 0.2s;
            
            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            }
            
            .item-icon {
              width: 48px;
              height: 48px;
              margin-right: 12px;
              border-radius: 8px;
              overflow: hidden;
              background: #fff;
              display: flex;
              align-items: center;
              justify-content: center;
              
              img {
                max-width: 100%;
                max-height: 100%;
              }
            }
            
            .item-info {
              flex: 1;
              
              .item-name {
                font-weight: 600;
                color: #303133;
                margin-bottom: 4px;
              }
              
              .item-count {
                color: #409eff;
                font-weight: 600;
              }
            }
          }
        }
      }
    }
  }
  
  .reward-footer {
    padding: 16px 24px 24px;
    display: flex;
    justify-content: center;
    
    .el-button {
      width: 200px;
      height: 48px;
      font-size: 16px;
    }
  }
}

@media (max-width: 768px) {
  .reward-panel {
    .reward-content {
      .reward-section {
        &.items-section {
          .items-container {
            .reward-item {
              width: 100%;
            }
          }
        }
      }
    }
  }
}
</style> 