using System;
using System.Collections.Generic;

namespace ItAssetsSystem.Models.Import
{
    /// <summary>
    /// 导入结果类
    /// </summary>
    /// <typeparam name="T">导入实体类型</typeparam>
    public class ImportResult<T>
    {
        /// <summary>
        /// 成功导入的项目列表
        /// </summary>
        public List<T> SuccessItems { get; set; } = new List<T>();

        /// <summary>
        /// 错误信息字典，键为行号，值为错误消息
        /// </summary>
        public Dictionary<int, string> Errors { get; set; } = new Dictionary<int, string>();

        /// <summary>
        /// 总行数
        /// </summary>
        public int TotalRows { get; set; }
    }
} 