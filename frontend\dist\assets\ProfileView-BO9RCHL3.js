import{bh as e,x as a,r as s,c as l,z as t,l as i,m as n,b as c,d as r,k as d,$ as o,f as u,w as v,E as m,ag as p,a as f,u as h,o as g,e as y,bE as b,bc as _,t as w,bN as k,p as I,bO as D,bP as z,F as P,h as L,bQ as T,bR as S,a3 as j,bq as x,_ as C}from"./index-CG5lHOPO.js";import{u as q}from"./gamification-Dm7mCEPf.js";import"./gamification-2FBMrBgR.js";const A={class:"profile-view"},U={class:"user-info"},F={class:"avatar-wrapper"},G={class:"avatar-uploader-icon"},N={class:"user-name"},R={class:"user-title"},B={class:"user-level"},E={class:"level-label"},M={class:"user-meta"},O={class:"meta-item"},$={class:"meta-value"},J={class:"meta-item"},Q={class:"meta-value"},V={class:"meta-item"},W={class:"meta-value"},H={class:"meta-item"},K={class:"meta-value"},X={class:"meta-item"},Y={class:"meta-value"},Z={class:"contact-info"},ee={class:"contact-item"},ae={class:"contact-item"},se={class:"card-header"},le={class:"tags-content"},te={key:0,class:"inventory-grid"},ie=["title"],ne={class:"stat-item"},ce={class:"stat-value"},re={class:"stat-item"},de={class:"stat-value"},oe={class:"stat-item"},ue={class:"stat-value"},ve={class:"stat-item"},me={class:"stat-value"},pe={class:"card-header"},fe={key:0,class:"achievements-grid"},he={class:"achievement-info"},ge={class:"achievement-name"},ye={class:"achievement-date"},be={class:"card-header"},_e=C(e({__name:"ProfileView",setup(e){const C=h(),_e=a(),we=q(),ke=s(!1),Ie=s(!1),De=s(!1),ze=s(!1),Pe=s(!1),Le=s(null),Te=s([]),Se=s(!1),je=s({gold:0,diamonds:0}),xe=s("-"),Ce=l((()=>Se.value?we.achievements:we.achievements.slice(0,6)));async function qe(){Ie.value=!0;try{await new Promise((e=>setTimeout(e,400))),Le.value={total:75,completed:58,pending:5,onTimeRate:95}}catch(e){m.error("加载任务统计失败"),Le.value=null}finally{Ie.value=!1}}async function Ae(){ze.value=!0;try{const e={pageSize:5,assignee:_e.userInfo.id,sortBy:"createDate",sortOrder:"desc"},a=await p.getTaskList(e);Te.value=(null==a?void 0:a.list)||[]}catch(e){m.error("加载近期任务失败"),Te.value=[]}finally{ze.value=!1}}async function Ue(){try{await new Promise((e=>setTimeout(e,200))),je.value={gold:1250,diamonds:55}}catch(e){}}async function Fe(){try{await new Promise((e=>setTimeout(e,250))),xe.value="#3"}catch(e){}}const Ge=e=>{if(!e)return"-";try{return new Date(e).toLocaleDateString()}catch{return"-"}},Ne=e=>{if(!e)return"-";try{return new Date(e).toLocaleString()}catch{return"-"}},Re=e=>{if(!e)return"-";try{return new Date(e).toLocaleDateString("zh-CN",{month:"2-digit",day:"2-digit"})}catch{return"-"}},Be={unstarted:{label:"未开始",type:"info"},"in-progress":{label:"进行中",type:"primary"},completed:{label:"已完成",type:"success"},overdue:{label:"已逾期",type:"danger"},todo:{label:"待办",type:"info"}},Ee=e=>{var a;return(null==(a=Be[e])?void 0:a.label)||e},Me=async e=>{var a,s;Pe.value=!0;try{const a=await x.uploadAvatar(e.file);if(a&&a.success&&a.data){const{avatarUrl:e,accessUrl:s}=a.data;s?_e.setAvatarWithFullUrl(s):e&&_e.setAvatar(e),m.success(a.message||"头像更新成功！")}else{const e=(null==a?void 0:a.message)||"头像上传失败，响应数据格式不正确";m.error(e)}}catch(l){let e="头像上传失败，请稍后再试";(null==(s=null==(a=null==l?void 0:l.response)?void 0:a.data)?void 0:s.message)?e=l.response.data.message:(null==l?void 0:l.message)&&(e=l.message),m.error(e)}finally{Pe.value=!1}},Oe=e=>["image/jpeg","image/png","image/gif"].includes(e.type)?!(e.size/1024/1024>5)||(m.error("头像图片大小不能超过 5MB!"),!1):(m.error("头像图片必须是 JPG, PNG, 或 GIF 格式!"),!1);return t((()=>{ke.value=!0,0!==we.score||we.isLoading||we.initializeStore(),Promise.allSettled([qe(),Ae(),Ue(),Fe()]).finally((()=>{ke.value=!1}))})),(e,a)=>{const s=f("el-avatar"),l=f("el-icon"),t=f("el-divider"),m=f("el-card"),p=f("el-button"),h=f("el-empty"),x=f("el-col"),q=f("el-row"),Pe=f("el-tooltip"),qe=f("el-table-column"),Ae=f("el-table"),Ue=n("loading");return i((g(),c("div",A,[a[24]||(a[24]=r("div",{class:"page-header"},"个人中心",-1)),u(_e).userInfo&&!ke.value?(g(),d(q,{key:0,gutter:20},{default:v((()=>[y(x,{span:8},{default:v((()=>[y(m,{shadow:"hover",class:"user-card"},{default:v((()=>[r("div",U,[r("div",F,[y(u(b),{class:"avatar-uploader",action:"#","show-file-list":!1,"http-request":Me,"before-upload":Oe},{default:v((()=>[y(s,{size:100,src:u(_e).computedAvatarUrl||"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png",class:"profile-avatar"},null,8,["src"]),r("div",G,[y(l,null,{default:v((()=>[y(u(_))])),_:1}),a[2]||(a[2]=r("span",null,"点击更换",-1))])])),_:1})]),r("h2",N,w(u(_e).userInfo.name||"用户名"),1),r("div",R,w(u(_e).userInfo.department||"部门")+" - "+w(u(_e).userInfo.position||"职位"),1),r("div",B,[r("span",E,"Level "+w(u(we).level||1)+" - "+w(u(we).levelTitle),1),y(u(k),{percentage:u(we).currentLevelProgress,"stroke-width":10,status:"success"},{default:v((()=>{var e;return[r("span",null,w(u(we).score)+" / "+w((null==(e=u(we).nextLevelInfo)?void 0:e.points)||"Max"),1)]})),_:1},8,["percentage"])]),r("div",M,[r("div",O,[a[3]||(a[3]=r("div",{class:"meta-label"},"加入时间",-1)),r("div",$,w(Ge(u(_e).userInfo.joinDate)||"-"),1)]),r("div",J,[a[4]||(a[4]=r("div",{class:"meta-label"},"总经验",-1)),r("div",Q,w(u(we).score),1)]),r("div",V,[a[6]||(a[6]=r("div",{class:"meta-label"},"金币",-1)),r("div",W,[a[5]||(a[5]=r("img",{src:"https://cdn-icons-png.flaticon.com/512/2933/2933116.png",class:"coin-icon-sm"},null,-1)),I(" "+w(je.value.gold||0),1)])]),r("div",H,[a[8]||(a[8]=r("div",{class:"meta-label"},"钻石",-1)),r("div",K,[a[7]||(a[7]=r("img",{src:"https://cdn-icons-png.flaticon.com/512/2933/2933151.png",class:"diamond-icon-sm"},null,-1)),I(" "+w(je.value.diamonds||0),1)])]),r("div",X,[a[9]||(a[9]=r("div",{class:"meta-label"},"排名",-1)),r("div",Y,w(xe.value||"-"),1)])])]),y(t),r("div",Z,[a[10]||(a[10]=r("h3",null,"联系方式",-1)),r("div",ee,[y(l,null,{default:v((()=>[y(u(D))])),_:1}),r("span",null,w(u(_e).userInfo.email||"未设置"),1)]),r("div",ae,[y(l,null,{default:v((()=>[y(u(z))])),_:1}),r("span",null,w(u(_e).userInfo.phone||"未设置"),1)])])])),_:1}),y(m,{shadow:"hover",class:"tags-card"},{header:v((()=>[r("div",se,[a[12]||(a[12]=r("span",null,"我的标签",-1)),y(p,{link:"",type:"primary"},{default:v((()=>a[11]||(a[11]=[I("编辑")]))),_:1})])])),default:v((()=>[r("div",le,[u(_e).userInfo.tags&&u(_e).userInfo.tags.length>0?(g(!0),c(P,{key:0},L(u(_e).userInfo.tags,(e=>(g(),d(u(T),{key:e,class:"user-tag",type:"info"},{default:v((()=>[I(w(e),1)])),_:2},1024)))),128)):(g(),d(h,{key:1,description:"暂无标签","image-size":50}))])])),_:1}),y(m,{shadow:"hover",class:"inventory-card"},{header:v((()=>a[13]||(a[13]=[r("div",{class:"card-header"},[r("span",null,"我的背包")],-1)]))),default:v((()=>[u(we).inventory.length>0?(g(),c("div",te,[(g(!0),c(P,null,L(u(we).inventory,(e=>(g(),c("div",{key:e.id,class:"inventory-item"},[y(u(S),{value:e.quantity>1?e.quantity:null,type:"primary"},{default:v((()=>[y(s,{size:50,src:e.icon||"path/to/default/item/icon.png",class:"item-icon"},null,8,["src"])])),_:2},1032,["value"]),r("div",{class:"item-name",title:e.description},w(e.name),9,ie)])))),128))])):(g(),d(h,{key:1,description:"背包空空如也","image-size":50}))])),_:1})])),_:1}),y(x,{span:16},{default:v((()=>[i((g(),d(m,{shadow:"hover",class:"stats-card"},{header:v((()=>a[14]||(a[14]=[r("div",{class:"card-header"},[r("span",null,"任务统计")],-1)]))),default:v((()=>[Le.value?(g(),d(q,{key:0,gutter:20,class:"stat-row"},{default:v((()=>[y(x,{span:6},{default:v((()=>[r("div",ne,[r("div",ce,w(Le.value.total||0),1),a[15]||(a[15]=r("div",{class:"stat-label"},"总任务数",-1))])])),_:1}),y(x,{span:6},{default:v((()=>[r("div",re,[r("div",de,w(Le.value.completed||0),1),a[16]||(a[16]=r("div",{class:"stat-label"},"已完成",-1))])])),_:1}),y(x,{span:6},{default:v((()=>[r("div",oe,[r("div",ue,w(Le.value.onTimeRate||0)+"%",1),a[17]||(a[17]=r("div",{class:"stat-label"},"按时完成率",-1))])])),_:1}),y(x,{span:6},{default:v((()=>[r("div",ve,[r("div",me,w(Le.value.pending||0),1),a[18]||(a[18]=r("div",{class:"stat-label"},"待处理",-1))])])),_:1})])),_:1})):o("",!0),a[19]||(a[19]=r("div",{class:"chart-area"},[r("div",{class:"chart-placeholder"},"任务完成趋势图表 (待实现)")],-1))])),_:1})),[[Ue,Ie.value]]),i((g(),d(m,{shadow:"hover",class:"achievements-card"},{header:v((()=>[r("div",pe,[a[20]||(a[20]=r("span",null,"我的成就",-1)),y(p,{link:"",type:"primary",onClick:a[0]||(a[0]=e=>Se.value=!Se.value)},{default:v((()=>[I(w(Se.value?"收起":`查看全部 (${u(we).achievements.length})`),1)])),_:1})])])),default:v((()=>[u(we).achievements.length>0?(g(),c("div",fe,[(g(!0),c(P,null,L(Ce.value,(e=>(g(),c("div",{key:e.id,class:"achievement-item"},[y(Pe,{content:e.description,placement:"top"},{default:v((()=>[y(s,{size:40,icon:e.icon||u(j),class:"achievement-icon"},null,8,["icon"])])),_:2},1032,["content"]),r("div",he,[r("div",ge,w(e.name),1),r("div",ye,w(Ne(e.achievedDate)),1)])])))),128))])):o("",!0)])),_:1})),[[Ue,De.value]]),i((g(),d(m,{shadow:"hover",class:"recent-tasks-card"},{header:v((()=>[r("div",be,[a[22]||(a[22]=r("span",null,"近期任务",-1)),y(p,{link:"",type:"primary",onClick:a[1]||(a[1]=e=>u(C).push("/main/tasks/list"))},{default:v((()=>a[21]||(a[21]=[I("查看全部")]))),_:1})])])),default:v((()=>[Te.value.length>0?(g(),d(Ae,{key:0,data:Te.value,style:{width:"100%"}},{default:v((()=>[y(qe,{prop:"title",label:"任务名称","min-width":"180","show-overflow-tooltip":""}),y(qe,{prop:"endDate",label:"截止时间",width:"120"},{default:v((({row:e})=>[I(w(Re(e.endDate)),1)])),_:1}),y(qe,{label:"状态",width:"100"},{default:v((({row:e})=>{return[y(u(T),{type:(a=e.status,(null==(s=Be[a])?void 0:s.type)||"info")},{default:v((()=>[I(w(Ee(e.status)),1)])),_:2},1032,["type"])];var a,s})),_:1}),y(qe,{label:"操作",width:"100",align:"center"},{default:v((({row:e})=>[y(p,{type:"primary",link:"",onClick:a=>{return s=e.id,void C.push({name:"TaskDetail",params:{id:s}});var s}},{default:v((()=>a[23]||(a[23]=[I("查看")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"])):o("",!0)])),_:1})),[[Ue,ze.value]])])),_:1})])),_:1})):ke.value?o("",!0):(g(),d(h,{key:1,description:"无法加载用户信息"}))])),[[Ue,ke.value]])}}}),[["__scopeId","data-v-f9bf8121"]]);export{_e as default};
