using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ItAssetsSystem.Models.Entities.Statistics
{
    /// <summary>
    /// 用户工作汇总统计实体
    /// </summary>
    [Table("user_work_summary")]
    public class UserWorkSummary
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Key]
        [Column("id")]
        public long Id { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        [Column("user_id")]
        public int UserId { get; set; }

        /// <summary>
        /// 用户姓名（冗余字段，提高查询性能）
        /// </summary>
        [Column("user_name")]
        [StringLength(50)]
        public string UserName { get; set; } = string.Empty;

        /// <summary>
        /// 部门名称（冗余字段，提高查询性能）
        /// </summary>
        [Column("department_name")]
        [StringLength(50)]
        public string? DepartmentName { get; set; }

        /// <summary>
        /// 统计周期类型
        /// </summary>
        [Column("period_type")]
        [StringLength(10)]
        public string PeriodType { get; set; } = string.Empty;

        /// <summary>
        /// 统计周期起始日期
        /// </summary>
        [Column("period_date")]
        public DateTime PeriodDate { get; set; }

        // 📋 任务模块统计
        /// <summary>
        /// 新建任务数
        /// </summary>
        [Column("tasks_created")]
        public int TasksCreated { get; set; } = 0;

        /// <summary>
        /// 领取任务数
        /// </summary>
        [Column("tasks_claimed")]
        public int TasksClaimed { get; set; } = 0;

        /// <summary>
        /// 完成任务数
        /// </summary>
        [Column("tasks_completed")]
        public int TasksCompleted { get; set; } = 0;

        /// <summary>
        /// 更新任务数
        /// </summary>
        [Column("tasks_updated")]
        public int TasksUpdated { get; set; } = 0;

        /// <summary>
        /// 评论任务数
        /// </summary>
        [Column("tasks_commented")]
        public int TasksCommented { get; set; } = 0;

        // 🏢 资产模块统计
        /// <summary>
        /// 新建资产数
        /// </summary>
        [Column("assets_created")]
        public int AssetsCreated { get; set; } = 0;

        /// <summary>
        /// 更新资产数
        /// </summary>
        [Column("assets_updated")]
        public int AssetsUpdated { get; set; } = 0;

        /// <summary>
        /// 删除资产数
        /// </summary>
        [Column("assets_deleted")]
        public int AssetsDeleted { get; set; } = 0;

        // 🔧 故障模块统计
        /// <summary>
        /// 登记故障数
        /// </summary>
        [Column("faults_reported")]
        public int FaultsReported { get; set; } = 0;

        /// <summary>
        /// 维修故障数
        /// </summary>
        [Column("faults_repaired")]
        public int FaultsRepaired { get; set; } = 0;

        // 🛒 采购模块统计
        /// <summary>
        /// 新建采购数
        /// </summary>
        [Column("procurements_created")]
        public int ProcurementsCreated { get; set; } = 0;

        /// <summary>
        /// 更新采购数
        /// </summary>
        [Column("procurements_updated")]
        public int ProcurementsUpdated { get; set; } = 0;

        // 📦 备件模块统计
        /// <summary>
        /// 备件入库数
        /// </summary>
        [Column("parts_in")]
        public int PartsIn { get; set; } = 0;

        /// <summary>
        /// 备件出库数
        /// </summary>
        [Column("parts_out")]
        public int PartsOut { get; set; } = 0;

        /// <summary>
        /// 新增备件数
        /// </summary>
        [Column("parts_added")]
        public int PartsAdded { get; set; } = 0;

        // 🎮 游戏化收益统计
        /// <summary>
        /// 获得积分总数
        /// </summary>
        [Column("total_points_earned")]
        public int TotalPointsEarned { get; set; } = 0;

        /// <summary>
        /// 获得金币总数
        /// </summary>
        [Column("total_coins_earned")]
        public int TotalCoinsEarned { get; set; } = 0;

        /// <summary>
        /// 获得钻石总数
        /// </summary>
        [Column("total_diamonds_earned")]
        public int TotalDiamondsEarned { get; set; } = 0;

        /// <summary>
        /// 获得经验值总数
        /// </summary>
        [Column("total_xp_earned")]
        public int TotalXpEarned { get; set; } = 0;

        // 📈 排名信息
        /// <summary>
        /// 积分排名
        /// </summary>
        [Column("points_rank")]
        public int PointsRank { get; set; } = 0;

        /// <summary>
        /// 生产力排名
        /// </summary>
        [Column("productivity_rank")]
        public int ProductivityRank { get; set; } = 0;

        /// <summary>
        /// 生产力得分
        /// </summary>
        [Column("productivity_score")]
        public double ProductivityScore { get; set; } = 0.0;

        /// <summary>
        /// 评价等级
        /// </summary>
        [Column("evaluation")]
        [StringLength(20)]
        public string Evaluation { get; set; } = string.Empty;

        // 📅 时间戳
        /// <summary>
        /// 创建时间
        /// </summary>
        [Column("created_at")]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新时间
        /// </summary>
        [Column("updated_at")]
        public DateTime UpdatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 最后校验时间
        /// </summary>
        [Column("last_reconciled_at")]
        public DateTime? LastReconciledAt { get; set; }

        /// <summary>
        /// 数据版本号（用于乐观锁）
        /// </summary>
        [Column("version")]
        public int Version { get; set; } = 1;

        // 导航属性
        /// <summary>
        /// 关联的用户
        /// </summary>
        [ForeignKey("UserId")]
        public virtual User User { get; set; } = null!;

        // 计算属性
        /// <summary>
        /// 任务总数
        /// </summary>
        [NotMapped]
        public int TasksTotal => TasksCreated + TasksClaimed + TasksCompleted + TasksUpdated + TasksCommented;

        /// <summary>
        /// 资产总数
        /// </summary>
        [NotMapped]
        public int AssetsTotal => AssetsCreated + AssetsUpdated + AssetsDeleted;

        /// <summary>
        /// 故障总数
        /// </summary>
        [NotMapped]
        public int FaultsTotal => FaultsReported + FaultsRepaired;

        /// <summary>
        /// 采购总数
        /// </summary>
        [NotMapped]
        public int ProcurementsTotal => ProcurementsCreated + ProcurementsUpdated;

        /// <summary>
        /// 备件总数
        /// </summary>
        [NotMapped]
        public int PartsTotal => PartsIn + PartsOut + PartsAdded;
    }
}
