// File: Core/Events/Tasks/TaskEvents.cs
// Description: 任务相关领域事件

#nullable enable
using System;
using MediatR;
using ItAssetsSystem.Models.Enums;

namespace ItAssetsSystem.Core.Events.Tasks
{
    /// <summary>
    /// 任务状态变更事件
    /// </summary>
    public class TaskStatusChangedEvent : INotification
    {
        /// <summary>
        /// 任务ID
        /// </summary>
        public long TaskId { get; }

        /// <summary>
        /// 任务标题
        /// </summary>
        public string TaskTitle { get; }

        /// <summary>
        /// 操作者ID
        /// </summary>
        public int? UserId { get; }

        /// <summary>
        /// 旧状态
        /// </summary>
        public TaskStatus OldStatus { get; }

        /// <summary>
        /// 新状态
        /// </summary>
        public TaskStatus NewStatus { get; }

        /// <summary>
        /// 变更时间
        /// </summary>
        public DateTime ChangedAt { get; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <param name="taskTitle">任务标题</param>
        /// <param name="userId">操作者ID</param>
        /// <param name="oldStatus">旧状态</param>
        /// <param name="newStatus">新状态</param>
        /// <param name="changedAt">变更时间</param>
        public TaskStatusChangedEvent(long taskId, string taskTitle, int? userId, TaskStatus oldStatus, TaskStatus newStatus, DateTime changedAt)
        {
            TaskId = taskId;
            TaskTitle = taskTitle;
            UserId = userId;
            OldStatus = oldStatus;
            NewStatus = newStatus;
            ChangedAt = changedAt;
        }
    }

    /// <summary>
    /// 任务分配事件
    /// </summary>
    public class TaskAssignedEvent : INotification
    {
        /// <summary>
        /// 任务ID
        /// </summary>
        public long TaskId { get; }

        /// <summary>
        /// 任务标题
        /// </summary>
        public string TaskTitle { get; }

        /// <summary>
        /// 操作者ID
        /// </summary>
        public int? AssignedByUserId { get; }

        /// <summary>
        /// 被分配者ID
        /// </summary>
        public int AssigneeUserId { get; }

        /// <summary>
        /// 分配时间
        /// </summary>
        public DateTime AssignedAt { get; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <param name="taskTitle">任务标题</param>
        /// <param name="assignedByUserId">操作者ID</param>
        /// <param name="assigneeUserId">被分配者ID</param>
        /// <param name="assignedAt">分配时间</param>
        public TaskAssignedEvent(long taskId, string taskTitle, int? assignedByUserId, int assigneeUserId, DateTime assignedAt)
        {
            TaskId = taskId;
            TaskTitle = taskTitle;
            AssignedByUserId = assignedByUserId;
            AssigneeUserId = assigneeUserId;
            AssignedAt = assignedAt;
        }
    }

    /// <summary>
    /// 任务评论添加事件
    /// </summary>
    public class CommentAddedEvent : INotification
    {
        /// <summary>
        /// 评论ID
        /// </summary>
        public long CommentId { get; }

        /// <summary>
        /// 任务ID
        /// </summary>
        public long TaskId { get; }

        /// <summary>
        /// 任务标题
        /// </summary>
        public string TaskTitle { get; }

        /// <summary>
        /// 评论者ID
        /// </summary>
        public int CreatorUserId { get; }

        /// <summary>
        /// 评论内容
        /// </summary>
        public string Content { get; }

        /// <summary>
        /// 评论时间
        /// </summary>
        public DateTime CreatedAt { get; }

        /// <summary>
        /// 提及的用户ID列表
        /// </summary>
        public string MentionedUserIds { get; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="commentId">评论ID</param>
        /// <param name="taskId">任务ID</param>
        /// <param name="taskTitle">任务标题</param>
        /// <param name="creatorUserId">评论者ID</param>
        /// <param name="content">评论内容</param>
        /// <param name="createdAt">评论时间</param>
        /// <param name="mentionedUserIds">提及的用户ID列表</param>
        public CommentAddedEvent(long commentId, long taskId, string taskTitle, int creatorUserId, string content, DateTime createdAt, string mentionedUserIds)
        {
            CommentId = commentId;
            TaskId = taskId;
            TaskTitle = taskTitle;
            CreatorUserId = creatorUserId;
            Content = content;
            CreatedAt = createdAt;
            MentionedUserIds = mentionedUserIds;
        }
    }

    /// <summary>
    /// 附件添加事件
    /// </summary>
    public class AttachmentAddedEvent : INotification
    {
        /// <summary>
        /// 附件ID
        /// </summary>
        public long AttachmentId { get; }

        /// <summary>
        /// 任务ID
        /// </summary>
        public long TaskId { get; }

        /// <summary>
        /// 任务标题
        /// </summary>
        public string TaskTitle { get; }

        /// <summary>
        /// 上传者ID
        /// </summary>
        public int UploaderId { get; }

        /// <summary>
        /// 文件名
        /// </summary>
        public string FileName { get; }

        /// <summary>
        /// 文件大小
        /// </summary>
        public long FileSize { get; }

        /// <summary>
        /// 上传时间
        /// </summary>
        public DateTime UploadedAt { get; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="attachmentId">附件ID</param>
        /// <param name="taskId">任务ID</param>
        /// <param name="taskTitle">任务标题</param>
        /// <param name="uploaderId">上传者ID</param>
        /// <param name="fileName">文件名</param>
        /// <param name="fileSize">文件大小</param>
        /// <param name="uploadedAt">上传时间</param>
        public AttachmentAddedEvent(long attachmentId, long taskId, string taskTitle, int uploaderId, string fileName, long fileSize, DateTime uploadedAt)
        {
            AttachmentId = attachmentId;
            TaskId = taskId;
            TaskTitle = taskTitle;
            UploaderId = uploaderId;
            FileName = fileName;
            FileSize = fileSize;
            UploadedAt = uploadedAt;
        }
    }

    /// <summary>
    /// 任务进度更新事件
    /// </summary>
    public class TaskProgressUpdatedEvent : INotification
    {
        /// <summary>
        /// 任务ID
        /// </summary>
        public long TaskId { get; }

        /// <summary>
        /// 任务标题
        /// </summary>
        public string TaskTitle { get; }

        /// <summary>
        /// 操作者ID
        /// </summary>
        public int? UpdatedByUserId { get; }

        /// <summary>
        /// 旧进度
        /// </summary>
        public int OldProgress { get; }

        /// <summary>
        /// 新进度
        /// </summary>
        public int NewProgress { get; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <param name="taskTitle">任务标题</param>
        /// <param name="updatedByUserId">操作者ID</param>
        /// <param name="oldProgress">旧进度</param>
        /// <param name="newProgress">新进度</param>
        /// <param name="updatedAt">更新时间</param>
        public TaskProgressUpdatedEvent(long taskId, string taskTitle, int? updatedByUserId, int oldProgress, int newProgress, DateTime updatedAt)
        {
            TaskId = taskId;
            TaskTitle = taskTitle;
            UpdatedByUserId = updatedByUserId;
            OldProgress = oldProgress;
            NewProgress = newProgress;
            UpdatedAt = updatedAt;
        }
    }

    /// <summary>
    /// PDCA阶段变更事件
    /// </summary>
    public class PdcaStageChangedEvent : INotification
    {
        /// <summary>
        /// 任务ID
        /// </summary>
        public long TaskId { get; }

        /// <summary>
        /// 任务标题
        /// </summary>
        public string TaskTitle { get; }

        /// <summary>
        /// 操作者ID
        /// </summary>
        public int? ChangedByUserId { get; }

        /// <summary>
        /// 旧阶段
        /// </summary>
        public PDCAStage? OldStage { get; }

        /// <summary>
        /// 新阶段
        /// </summary>
        public PDCAStage NewStage { get; }

        /// <summary>
        /// 变更时间
        /// </summary>
        public DateTime ChangedAt { get; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <param name="taskTitle">任务标题</param>
        /// <param name="changedByUserId">操作者ID</param>
        /// <param name="oldStage">旧阶段</param>
        /// <param name="newStage">新阶段</param>
        /// <param name="changedAt">变更时间</param>
        public PdcaStageChangedEvent(long taskId, string taskTitle, int? changedByUserId, PDCAStage? oldStage, PDCAStage newStage, DateTime changedAt)
        {
            TaskId = taskId;
            TaskTitle = taskTitle;
            ChangedByUserId = changedByUserId;
            OldStage = oldStage;
            NewStage = newStage;
            ChangedAt = changedAt;
        }
    }
} 