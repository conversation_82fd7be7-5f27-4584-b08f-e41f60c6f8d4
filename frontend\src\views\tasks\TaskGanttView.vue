<template>
  <div class="gantt-view-container">
    <el-card class="gantt-card">
      <template #header>
        <div class="card-header">
          <h2>任务甘特图</h2>
          <div class="header-actions">
            <el-button type="primary" @click="refreshData">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="handleDateRangeChange"
              :shortcuts="dateShortcuts"
            />
          </div>
        </div>
      </template>
      
      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="10" animated />
      </div>
      
      <div v-else-if="tasks.length === 0" class="empty-data">
        <el-empty description="暂无任务数据" />
      </div>
      
      <div v-else class="gantt-chart-container">
        <div class="gantt-placeholder">
          <!-- 甘特图将在此处渲染 -->
          <p class="development-note">甘特图组件正在开发中，敬请期待...</p>
          
          <!-- 临时表格展示数据 -->
          <el-table :data="tasks" style="width: 100%">
            <el-table-column prop="taskId" label="ID" width="70" />
            <el-table-column prop="title" label="任务名称" min-width="200" />
            <el-table-column prop="status" label="状态" width="120">
              <template #default="scope">
                <el-tag :type="getStatusType(scope.row.status)">{{ getStatusText(scope.row.status) }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="startDate" label="开始日期" width="120" />
            <el-table-column prop="dueDate" label="截止日期" width="120" />
            <el-table-column prop="progress" label="进度" width="180">
              <template #default="scope">
                <el-progress :percentage="scope.row.progress" />
              </template>
            </el-table-column>
            <el-table-column prop="assigneeName" label="负责人" width="120" />
          </el-table>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { formatDate } from '@/utils/date'
import { taskApi } from '@/api/task'

export default {
  name: 'TaskGanttView',
  
  setup() {
    const tasks = ref([])
    const loading = ref(true)
    const dateRange = ref([])
    
    // 日期快捷选项
    const dateShortcuts = [
      {
        text: '最近一周',
        value: () => {
          const end = new Date()
          const start = new Date()
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
          return [start, end]
        },
      },
      {
        text: '最近一个月',
        value: () => {
          const end = new Date()
          const start = new Date()
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
          return [start, end]
        },
      },
      {
        text: '最近三个月',
        value: () => {
          const end = new Date()
          const start = new Date()
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
          return [start, end]
        },
      },
    ]
    
    // 加载任务数据
    const loadTasks = async () => {
      loading.value = true
      try {
        const params = {
          pageIndex: 1,
          pageSize: 100, // 一次加载足够多的数据用于甘特图
        }
        
        // 如果有日期范围，添加到查询参数
        if (dateRange.value && dateRange.value.length === 2) {
          params.startDate = formatDate(dateRange.value[0], 'yyyy-MM-dd')
          params.endDate = formatDate(dateRange.value[1], 'yyyy-MM-dd')
        }
        
        const response = await taskApi.getTasks(params)
        if (response.success) {
          // 处理任务数据，确保有开始日期和结束日期
          tasks.value = response.data.items.map(task => ({
            ...task,
            startDate: task.startDate || task.createdTime,
            dueDate: task.dueDate || calculateDefaultDueDate(task.startDate || task.createdTime),
            progress: task.progress || 0
          }))
        } else {
          ElMessage.error(response.message || '加载任务数据失败')
        }
      } catch (error) {
        console.error('加载任务数据出错:', error)
        ElMessage.error('加载任务数据时发生错误')
      } finally {
        loading.value = false
      }
    }
    
    // 如果没有截止日期，默认设置为开始日期后7天
    const calculateDefaultDueDate = (startDate) => {
      const date = new Date(startDate)
      date.setDate(date.getDate() + 7)
      return formatDate(date, 'yyyy-MM-dd')
    }
    
    // 刷新数据
    const refreshData = () => {
      loadTasks()
    }
    
    // 日期范围变更
    const handleDateRangeChange = (val) => {
      if (val) {
        loadTasks()
      }
    }
    
    // 获取状态类型对应的Element UI标签类型
    const getStatusType = (status) => {
      const statusMap = {
        'pending': 'info',
        'in_progress': 'warning',
        'completed': 'success',
        'cancelled': 'danger'
      }
      return statusMap[status] || 'info'
    }
    
    // 获取状态文本
    const getStatusText = (status) => {
      const statusTextMap = {
        'pending': '待处理',
        'in_progress': '进行中',
        'completed': '已完成',
        'cancelled': '已取消'
      }
      return statusTextMap[status] || status
    }
    
    onMounted(() => {
      // 设置默认日期范围为最近一个月
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
      dateRange.value = [start, end]
      
      loadTasks()
    })
    
    return {
      tasks,
      loading,
      dateRange,
      dateShortcuts,
      refreshData,
      handleDateRangeChange,
      getStatusType,
      getStatusText
    }
  }
}
</script>

<style scoped>
.gantt-view-container {
  padding: 20px;
}

.gantt-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.loading-container {
  padding: 20px;
}

.empty-data {
  padding: 40px 0;
  text-align: center;
}

.gantt-chart-container {
  min-height: 400px;
}

.gantt-placeholder {
  border: 1px dashed #ccc;
  padding: 20px;
  border-radius: 4px;
}

.development-note {
  text-align: center;
  color: #909399;
  margin: 20px 0;
  font-size: 16px;
}
</style> 