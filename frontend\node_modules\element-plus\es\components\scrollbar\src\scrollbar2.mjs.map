{"version": 3, "file": "scrollbar2.mjs", "sources": ["../../../../../../packages/components/scrollbar/src/scrollbar.vue"], "sourcesContent": ["<template>\n  <div ref=\"scrollbarRef\" :class=\"ns.b()\">\n    <div\n      ref=\"wrapRef\"\n      :class=\"wrapKls\"\n      :style=\"wrapStyle\"\n      :tabindex=\"tabindex\"\n      @scroll=\"handleScroll\"\n    >\n      <component\n        :is=\"tag\"\n        :id=\"id\"\n        ref=\"resizeRef\"\n        :class=\"resizeKls\"\n        :style=\"viewStyle\"\n        :role=\"role\"\n        :aria-label=\"ariaLabel\"\n        :aria-orientation=\"ariaOrientation\"\n      >\n        <slot />\n      </component>\n    </div>\n    <template v-if=\"!native\">\n      <bar ref=\"barRef\" :always=\"always\" :min-size=\"minSize\" />\n    </template>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport {\n  computed,\n  nextTick,\n  onActivated,\n  onMounted,\n  onUpdated,\n  provide,\n  reactive,\n  ref,\n  watch,\n} from 'vue'\nimport { useEventListener, useResizeObserver } from '@vueuse/core'\nimport { addUnit, debugWarn, isNumber, isObject } from '@element-plus/utils'\nimport { useNamespace } from '@element-plus/hooks'\nimport Bar from './bar.vue'\nimport { scrollbarContextKey } from './constants'\nimport { scrollbarEmits, scrollbarProps } from './scrollbar'\nimport type { BarInstance } from './bar'\nimport type { CSSProperties, StyleValue } from 'vue'\n\nconst COMPONENT_NAME = 'ElScrollbar'\n\ndefineOptions({\n  name: COMPONENT_NAME,\n})\n\nconst props = defineProps(scrollbarProps)\nconst emit = defineEmits(scrollbarEmits)\n\nconst ns = useNamespace('scrollbar')\n\nlet stopResizeObserver: (() => void) | undefined = undefined\nlet stopResizeListener: (() => void) | undefined = undefined\nlet wrapScrollTop = 0\nlet wrapScrollLeft = 0\n\nconst scrollbarRef = ref<HTMLDivElement>()\nconst wrapRef = ref<HTMLDivElement>()\nconst resizeRef = ref<HTMLElement>()\nconst barRef = ref<BarInstance>()\n\nconst wrapStyle = computed<StyleValue>(() => {\n  const style: CSSProperties = {}\n  if (props.height) style.height = addUnit(props.height)\n  if (props.maxHeight) style.maxHeight = addUnit(props.maxHeight)\n  return [props.wrapStyle, style]\n})\n\nconst wrapKls = computed(() => {\n  return [\n    props.wrapClass,\n    ns.e('wrap'),\n    { [ns.em('wrap', 'hidden-default')]: !props.native },\n  ]\n})\n\nconst resizeKls = computed(() => {\n  return [ns.e('view'), props.viewClass]\n})\n\nconst handleScroll = () => {\n  if (wrapRef.value) {\n    barRef.value?.handleScroll(wrapRef.value)\n    wrapScrollTop = wrapRef.value.scrollTop\n    wrapScrollLeft = wrapRef.value.scrollLeft\n\n    emit('scroll', {\n      scrollTop: wrapRef.value.scrollTop,\n      scrollLeft: wrapRef.value.scrollLeft,\n    })\n  }\n}\n\n// TODO: refactor method overrides, due to script setup dts\n// @ts-nocheck\nfunction scrollTo(xCord: number, yCord?: number): void\nfunction scrollTo(options: ScrollToOptions): void\nfunction scrollTo(arg1: unknown, arg2?: number) {\n  if (isObject(arg1)) {\n    wrapRef.value!.scrollTo(arg1)\n  } else if (isNumber(arg1) && isNumber(arg2)) {\n    wrapRef.value!.scrollTo(arg1, arg2)\n  }\n}\n\nconst setScrollTop = (value: number) => {\n  if (!isNumber(value)) {\n    debugWarn(COMPONENT_NAME, 'value must be a number')\n    return\n  }\n  wrapRef.value!.scrollTop = value\n}\n\nconst setScrollLeft = (value: number) => {\n  if (!isNumber(value)) {\n    debugWarn(COMPONENT_NAME, 'value must be a number')\n    return\n  }\n  wrapRef.value!.scrollLeft = value\n}\n\nconst update = () => {\n  barRef.value?.update()\n}\n\nwatch(\n  () => props.noresize,\n  (noresize) => {\n    if (noresize) {\n      stopResizeObserver?.()\n      stopResizeListener?.()\n    } else {\n      ;({ stop: stopResizeObserver } = useResizeObserver(resizeRef, update))\n      stopResizeListener = useEventListener('resize', update)\n    }\n  },\n  { immediate: true }\n)\n\nwatch(\n  () => [props.maxHeight, props.height],\n  () => {\n    if (!props.native)\n      nextTick(() => {\n        update()\n        if (wrapRef.value) {\n          barRef.value?.handleScroll(wrapRef.value)\n        }\n      })\n  }\n)\n\nprovide(\n  scrollbarContextKey,\n  reactive({\n    scrollbarElement: scrollbarRef,\n    wrapElement: wrapRef,\n  })\n)\n\nonActivated(() => {\n  if (wrapRef.value) {\n    wrapRef.value.scrollTop = wrapScrollTop\n    wrapRef.value.scrollLeft = wrapScrollLeft\n  }\n})\n\nonMounted(() => {\n  if (!props.native)\n    nextTick(() => {\n      update()\n    })\n})\nonUpdated(() => update())\n\ndefineExpose({\n  /** @description scrollbar wrap ref */\n  wrapRef,\n  /** @description update scrollbar state manually */\n  update,\n  /** @description scrolls to a particular set of coordinates */\n  scrollTo,\n  /** @description set distance to scroll top */\n  setScrollTop,\n  /** @description set distance to scroll left */\n  setScrollLeft,\n  /** @description handle scroll event */\n  handleScroll,\n})\n</script>\n"], "names": ["_openBlock", "_createElementBlock", "_normalizeClass", "_unref", "_createElementVNode", "_normalizeStyle"], "mappings": ";;;;;;;;;;;;;mCAmDc,CAAA;AAAA,EACZ,IAAM,EAAA,cAAA;AACR;;;;;;;AAKA,IAAM,MAAA,EAAA,GAAK,aAAa,WAAW,CAAA,CAAA;AAEnC,IAAA,IAAI,kBAA+C,GAAA,KAAA,CAAA,CAAA;AACnD,IAAA,IAAI,kBAA+C,GAAA,KAAA,CAAA,CAAA;AACnD,IAAA,IAAI,aAAgB,GAAA,CAAA,CAAA;AACpB,IAAA,IAAI,cAAiB,GAAA,CAAA,CAAA;AAErB,IAAA,MAAM,eAAe,GAAoB,EAAA,CAAA;AACzC,IAAA,MAAM,UAAU,GAAoB,EAAA,CAAA;AACpC,IAAA,MAAM,YAAY,GAAiB,EAAA,CAAA;AACnC,IAAA,MAAM,SAAS,GAAiB,EAAA,CAAA;AAEhC,IAAM,MAAA,SAAA,GAAY,SAAqB,MAAM;AAC3C,MAAA,MAAM,QAAuB,EAAC,CAAA;AAC9B,MAAA,IAAI,MAAM,MAAQ;AAClB,QAAA,YAAqB,GAAA,OAAA,CAAA,KAAkB,CAAA,MAAA,CAAA,CAAA;AACvC,MAAO,IAAA,KAAO,CAAA,SAAA;AAAgB,QAC/B,KAAA,CAAA,SAAA,GAAA,OAAA,CAAA,KAAA,CAAA,SAAA,CAAA,CAAA;AAED,MAAM,OAAA,CAAA,KAAA,CAAU,SAAS,EAAM,KAAA,CAAA,CAAA;AAC7B,KAAO,CAAA,CAAA;AAAA,IAAA,MACC,OAAA,GAAA,QAAA,CAAA,MAAA;AAAA,MACN,OAAK;AAAM,QACX,KAAG,CAAG,SAAG;AAA0C,QACrD,EAAA,CAAA,CAAA,CAAA,MAAA,CAAA;AAAA,QACD,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,MAAA,EAAA,gBAAA,CAAA,GAAA,CAAA,KAAA,CAAA,MAAA,EAAA;AAED,OAAM,CAAA;AACJ,KAAA,CAAA,CAAA;AAAqC,IACvC,MAAC,SAAA,GAAA,QAAA,CAAA,MAAA;AAED,MAAA,mBAAqB,CAAM,EAAA,KAAA,CAAA,SAAA,CAAA,CAAA;AACzB,KAAA,CAAA,CAAA;AACE,IAAO,MAAA,YAAO,GAAa,MAAA;AAC3B,MAAA,IAAA,EAAA,CAAA;AACA,MAAA,IAAA,OAAA,CAAA,KAAA,EAAiB;AAEjB,QAAA,CAAA,EAAA,GAAe,MAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,YAAA,CAAA,OAAA,CAAA,KAAA,CAAA,CAAA;AAAA,QACb,aAAW,UAAc,CAAA,KAAA,CAAA,SAAA,CAAA;AAAA,QACzB,cAAY,UAAc,CAAA,KAAA,CAAA,UAAA,CAAA;AAAA,QAC5B,IAAC,CAAA,QAAA,EAAA;AAAA,UACH,SAAA,EAAA,OAAA,CAAA,KAAA,CAAA,SAAA;AAAA,UACF,UAAA,EAAA,OAAA,CAAA,KAAA,CAAA,UAAA;AAMA,SAAS,CAAA,CAAA;AACP,OAAI;AACF,KAAQ,CAAA;AAAoB,IAAA,iBACV,CAAA,IAAA,EAAA,IAAS,EAAA;AAC3B,MAAQ,IAAA,QAAA,CAAA,IAAgB,CAAA,EAAA;AAAU,QACpC,OAAA,CAAA,KAAA,CAAA,QAAA,CAAA,IAAA,CAAA,CAAA;AAAA,OACF,MAAA,IAAA,QAAA,CAAA,IAAA,CAAA,IAAA,QAAA,CAAA,IAAA,CAAA,EAAA;AAEA,QAAM,OAAA,CAAA,KAAA,CAAA,QAAkC,CAAA,IAAA,EAAA,IAAA,CAAA,CAAA;AACtC,OAAI;AACF,KAAA;AACA,IAAA,MAAA,YAAA,GAAA,CAAA,KAAA,KAAA;AAAA,MACF,IAAA,CAAA,QAAA,CAAA,KAAA,CAAA,EAAA;AACA,QAAA,wBAA2B,EAAA,wBAAA,CAAA,CAAA;AAAA,QAC7B,OAAA;AAEA,OAAM;AACJ,MAAI,OAAU,CAAA,KAAA,CAAA,SAAQ,GAAA,KAAA,CAAA;AACpB,KAAA,CAAA;AACA,IAAA,MAAA,aAAA,GAAA,CAAA,KAAA,KAAA;AAAA,MACF,IAAA,CAAA,QAAA,CAAA,KAAA,CAAA,EAAA;AACA,QAAA,wBAA4B,EAAA,wBAAA,CAAA,CAAA;AAAA,QAC9B,OAAA;AAEA,OAAA;AACE,MAAA,OAAO,OAAO,UAAO,GAAA,KAAA,CAAA;AAAA,KACvB,CAAA;AAEA,IAAA,MAAA,MAAA,GAAA,MAAA;AAAA,MACE,MAAM,CAAM;AAAA,MACZ,CAAC,EAAa,GAAA,MAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,MAAA,EAAA,CAAA;AACZ,KAAA,CAAA;AACE,IAAqB,KAAA,CAAA,MAAA,KAAA,CAAA,QAAA,EAAA,CAAA,QAAA,KAAA;AACrB,MAAqB,IAAA,QAAA,EAAA;AAAA,QACvB,kBAAO,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,kBAAA,EAAA,CAAA;AACL,QAAA,kBAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,kBAAA,EAAA,CAAA;AAAC,OAAA,MAAG;AACkD,QACxD,CAAA,EAAA,IAAA,EAAA,kBAAA,EAAA,GAAA,iBAAA,CAAA,SAAA,EAAA,MAAA,CAAA,EAAA;AAAA,QACF,kBAAA,GAAA,gBAAA,CAAA,QAAA,EAAA,MAAA,CAAA,CAAA;AAAA,OACA;AAAkB,KACpB,EAAA,EAAA,SAAA,EAAA,IAAA,EAAA,CAAA,CAAA;AAEA,IAAA,KAAA,CAAA,MAAA,CAAA,KAAA,CAAA,SAAA,EAAA,KAAA,CAAA,MAAA,CAAA,EAAA,MAAA;AAAA,MACE,IAAM,CAAA,KAAO,CAAA,MAAA;AAAuB,QAC9B,QAAA,CAAA,MAAA;AACJ,UAAA,IAAW,EAAA,CAAA;AACT,UAAA,MAAA,EAAA,CAAS;AACP,UAAO,IAAA,OAAA,CAAA,KAAA,EAAA;AACP,YAAA,CAAA,EAAA,SAAY,CAAO,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,YAAA,CAAA,OAAA,CAAA,KAAA,CAAA,CAAA;AACjB,WAAO;AAAiC,SAC1C,CAAA,CAAA;AAAA,KAAA,CAAA,CAAA;AACD,IACL,OAAA,CAAA,mBAAA,EAAA,QAAA,CAAA;AAAA,MACF,gBAAA,EAAA,YAAA;AAEA,MAAA,WAAA,EAAA,OAAA;AAAA,KACE,CAAA,CAAA,CAAA;AAAA,IAAA,WACS,CAAA,MAAA;AAAA,MAAA,IACW,OAAA,CAAA,KAAA,EAAA;AAAA,QAClB,OAAa,CAAA,KAAA,CAAA,SAAA,GAAA,aAAA,CAAA;AAAA,QACd,OAAA,CAAA,KAAA,CAAA,UAAA,GAAA,cAAA,CAAA;AAAA,OACH;AAEA,KAAA,CAAA,CAAA;AACE,IAAA,gBAAmB;AACjB,MAAA,IAAA,CAAA,KAAQ,OAAkB;AAC1B,QAAA,QAAQ,OAAmB;AAAA,UAC7B,MAAA,EAAA,CAAA;AAAA,SACD,CAAA,CAAA;AAED,KAAA,CAAA,CAAA;AACE,IAAA,SAAW,CAAA,MAAA,MAAA,EAAA,CAAA,CAAA;AACT,IAAA,MAAA,CAAA;AACE,MAAO,OAAA;AAAA,MAAA,MACR;AAAA,MACJ,QAAA;AACD,MAAU,YAAA;AAEV,MAAa,aAAA;AAAA,MAAA,YAAA;AAAA,KAEX,CAAA,CAAA;AAAA,IAAA,OAAA,CAAA,IAAA,EAAA,MAAA,KAAA;AAAA,MAEA,OAAAA,SAAA,EAAA,EAAAC,kBAAA,CAAA,KAAA,EAAA;AAAA,QAAA,OAAA,EAAA,cAAA;AAAA,QAEA,GAAA,EAAA,YAAA;AAAA,QAAA,KAAA,EAAAC,cAAA,CAAAC,KAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA;AAAA,OAEA,EAAA;AAAA,QAAAC,kBAAA,CAAA,KAAA,EAAA;AAAA,UAEA,OAAA,EAAA,SAAA;AAAA,UAAA,GAAA,EAAA,OAAA;AAAA,UAEA,KAAA,EAAAF,cAAA,CAAAC,KAAA,CAAA,OAAA,CAAA,CAAA;AAAA,UACD,KAAA,EAAAE,cAAA,CAAAF,KAAA,CAAA,SAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}