// File: Infrastructure/Services/LocalStorageFileService.cs
// Description: Implements file storage using the local filesystem within wwwroot.

#nullable enable

using ItAssetsSystem.Core.Abstractions;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using System;
using System.IO;
using System.Threading.Tasks;
using System.Linq;

namespace ItAssetsSystem.Infrastructure.Services
{
    public class LocalStorageFileService : IFileStorageService
    {
        private readonly IWebHostEnvironment _webHostEnvironment;
        private readonly ILogger<LocalStorageFileService> _logger;
        // Define the base path within wwwroot for avatars
        private const string AvatarBasePath = "uploads/avatars";

        public LocalStorageFileService(IWebHostEnvironment webHostEnvironment, ILogger<LocalStorageFileService> logger)
        {
            _webHostEnvironment = webHostEnvironment;
            _logger = logger;
        }

        public async Task<string?> SaveAvatarAsync(IFormFile file, string userId)
        {
            if (file == null || file.Length == 0)
            {
                _logger.LogWarning("SaveAvatarAsync called with null or empty file for user {UserId}.", userId);
                return null;
            }

            // Basic validation (you might want more robust checks)
            var allowedExtensions = new[] { ".jpg", ".jpeg", ".png", ".gif" };
            var extension = Path.GetExtension(file.FileName).ToLowerInvariant();
            if (string.IsNullOrEmpty(extension) || !allowedExtensions.Contains(extension))
            {
                _logger.LogError("Invalid file type uploaded for user {UserId}: {FileName}", userId, file.FileName);
                return null; // Or throw an exception
            }

            try
            {
                var uploadsRootFolder = Path.Combine(_webHostEnvironment.WebRootPath, AvatarBasePath);

                // Ensure the directory exists
                if (!Directory.Exists(uploadsRootFolder))
                {
                    Directory.CreateDirectory(uploadsRootFolder);
                    _logger.LogInformation("Created avatar upload directory: {DirectoryPath}", uploadsRootFolder);
                }

                // 生成一个唯一的文件名（例如：user1_1678886400000.png）
                var uniqueFileName = $"user{userId}_{DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()}{extension}";
                var filePath = Path.Combine(uploadsRootFolder, uniqueFileName);

                // Save the file
                using (var fileStream = new FileStream(filePath, FileMode.Create))
                {
                    await file.CopyToAsync(fileStream);
                }

                _logger.LogInformation("Successfully saved avatar for user {UserId} to {FilePath}", userId, filePath);

                // Return the relative path for web access
                // IMPORTANT: Ensure your application serves static files from this path!
                return $"{AvatarBasePath}/{uniqueFileName}".Replace("\\", "/"); // Normalize path separators
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving avatar for user {UserId}. FileName: {FileName}", userId, file.FileName);
                return null;
            }
        }

        public async Task<string?> SaveFileAsync(byte[] fileContent, string fileName, string subPath)
        {
            if (fileContent == null || fileContent.Length == 0)
            {
                _logger.LogWarning("SaveFileAsync called with null or empty file content for fileName {FileName} in subPath {SubPath}.", fileName, subPath);
                return null;
            }

            if (string.IsNullOrWhiteSpace(fileName))
            {
                _logger.LogWarning("SaveFileAsync called with null or empty fileName in subPath {SubPath}.", subPath);
                return null;
            }

            if (string.IsNullOrWhiteSpace(subPath))
            {
                _logger.LogWarning("SaveFileAsync called with null or empty subPath for fileName {FileName}.", fileName);
                return null; 
            }

            try
            {
                string sanitizedSubPath = SanitizePathFragment(subPath);
                string sanitizedFileName = SanitizeFileName(fileName);

                if (string.IsNullOrWhiteSpace(sanitizedSubPath) || string.IsNullOrWhiteSpace(sanitizedFileName))
                {
                    _logger.LogError("Invalid characters in subPath ({SubPath}) or fileName ({FileName}) after sanitization.", subPath, fileName);
                    return null;
                }

                var targetDirectory = Path.Combine(_webHostEnvironment.WebRootPath, sanitizedSubPath);

                if (!Directory.Exists(targetDirectory))
                {
                    Directory.CreateDirectory(targetDirectory);
                    _logger.LogInformation("Created directory for general file uploads: {DirectoryPath}", targetDirectory);
                }

                var filePath = Path.Combine(targetDirectory, sanitizedFileName);

                await File.WriteAllBytesAsync(filePath, fileContent);

                _logger.LogInformation("Successfully saved file {FileName} to {FilePath}", sanitizedFileName, filePath);

                return $"{sanitizedSubPath}/{sanitizedFileName}".Replace("\\", "/");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving file {FileName} to subPath {SubPath}.", fileName, subPath);
                return null;
            }
        }

        private string SanitizePathFragment(string pathFragment)
        {
            if (string.IsNullOrWhiteSpace(pathFragment)) return string.Empty;
            string sanitized = pathFragment.Trim().TrimStart('.', '/', '\\').TrimEnd('.', '/', '\\');
            sanitized = sanitized.Replace("..", "_"); 
            return sanitized;
        }

        private string SanitizeFileName(string fileName)
        {
            if (string.IsNullOrWhiteSpace(fileName)) return string.Empty;
            string invalidChars = new string(Path.GetInvalidFileNameChars());
            string sanitized = fileName.Trim();
            foreach (char c in invalidChars)
            {
                sanitized = sanitized.Replace(c.ToString(), "_");
            }
            const int maxFileNameLength = 100; 
            if (sanitized.Length > maxFileNameLength)
            {
                string extension = Path.GetExtension(sanitized);
                sanitized = sanitized.Substring(0, maxFileNameLength - (extension?.Length ?? 0)) + (extension ?? string.Empty);
            }
            return sanitized;
        }

        public Task<bool> DeleteFileAsync(string? relativePath)
        {
            if (string.IsNullOrWhiteSpace(relativePath))
            {
                return Task.FromResult(true); // Nothing to delete
            }

            try
            {
                // Construct the full physical path
                // Trim leading slash if present to correctly combine with WebRootPath
                var physicalPath = Path.Combine(_webHostEnvironment.WebRootPath, relativePath.TrimStart('/','\\'));

                if (File.Exists(physicalPath))
                {
                    File.Delete(physicalPath);
                    _logger.LogInformation("Deleted file: {FilePath}", physicalPath);
                    return Task.FromResult(true);
                }
                else
                {
                    _logger.LogWarning("Attempted to delete non-existent file: {FilePath}", physicalPath);
                    return Task.FromResult(true); // File doesn't exist, consider it success
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting file: {RelativePath}", relativePath);
                return Task.FromResult(false);
            }
        }
    }
} 