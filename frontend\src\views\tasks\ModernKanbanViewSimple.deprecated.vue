<!--
  本文件已废弃（deprecated），请使用 ModernKanbanView.vue 替代。
  如需恢复请联系前端负责人。
-->
<template>
  <div class="modern-task-management">
    <!-- 顶部操作栏 -->
    <div class="task-header">
      <div class="flex items-center justify-between mb-6">
        <div class="flex items-center gap-4">
          <h1 class="text-2xl font-bold text-gray-800">🚀 现代化任务管理</h1>
        </div>
        
        <div class="flex items-center gap-3">
          <!-- 快速创建任务 -->
          <QuickTaskCreator 
            trigger-text="⚡ 快速创建"
            trigger-class="quick-create-btn"
            @created="onTaskCreated"
          />
          
          <!-- 详细创建任务 -->
          <el-button type="primary" @click="showCreateDialog = true">
            <el-icon><Setting /></el-icon>
            详细创建
          </el-button>
        </div>
      </div>
    </div>

    <!-- 看板视图 -->
    <div class="kanban-board">
      <div 
        v-for="column in taskColumns" 
        :key="column.status" 
        class="task-column"
      >
        <!-- 列头 -->
        <div class="task-column-header">
          <div class="flex items-center gap-2">
            <span class="task-column-title">{{ column.title }}</span>
            <el-badge :value="column.tasks.length" class="ml-2" />
          </div>
          <el-button 
            size="small" 
            type="text" 
            @click="addTaskToColumn(column.status)"
          >
            <el-icon><Plus /></el-icon>
          </el-button>
        </div>

        <!-- 任务卡片容器 -->
        <div class="task-cards-container">
          <EnhancedTaskCard
            v-for="task in column.tasks"
            :key="task.taskId"
            :task="task"
            @click="showTaskDetail"
            @quick-action="handleTaskQuickAction"
            @status-change="handleQuickStatusChange"
          />
          
          <!-- 空状态提示 -->
          <div v-if="column.tasks.length === 0" class="empty-column">
            <p>暂无任务</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 创建任务对话框 -->
    <el-dialog v-model="showCreateDialog" title="创建任务" width="600px">
      <div style="text-align: center; padding: 40px;">
        <h3>🎯 创建新任务</h3>
        <p>这里将显示完整的任务创建表单</p>
        <el-button type="primary" @click="showCreateDialog = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Setting, Plus } from '@element-plus/icons-vue'
import EnhancedTaskCard from '@/components/Tasks/EnhancedTaskCard.vue'
import QuickTaskCreator from '@/components/Tasks/QuickTaskCreatorSimple.vue'
import { useTaskEnhancedStore } from '@/stores/modules/taskEnhanced'

// Store
const taskStore = useTaskEnhancedStore()

// 响应式状态
const showCreateDialog = ref(false)

// 测试数据
const testTasks = ref([
  {
    taskId: 1,
    name: '🔧 系统优化任务',
    description: '优化IT资产管理系统的性能和用户体验',
    status: 'Todo',
    priority: 'High',
    assigneeUserId: 1,
    assigneeUserName: '张三',
    progress: 0,
    planEndDate: '2024-12-31',
    collaborators: [
      { userId: 2, userName: '李四' },
      { userId: 3, userName: '王五' }
    ]
  },
  {
    taskId: 2,
    name: '📊 数据分析报告',
    description: '生成月度IT资产使用情况分析报告',
    status: 'InProgress',
    priority: 'Medium',
    assigneeUserId: 2,
    assigneeUserName: '李四',
    progress: 45,
    planEndDate: '2024-12-25'
  },
  {
    taskId: 3,
    name: '✅ 用户培训完成',
    description: '完成新系统的用户培训工作',
    status: 'Done',
    priority: 'Low',
    assigneeUserId: 3,
    assigneeUserName: '王五',
    progress: 100,
    planEndDate: '2024-12-20'
  }
])

// 任务列分组
const taskColumns = computed(() => {
  const columns = [
    { status: 'Todo', title: '📋 待处理', tasks: [] },
    { status: 'InProgress', title: '🔄 进行中', tasks: [] },
    { status: 'Done', title: '✅ 已完成', tasks: [] }
  ]
  
  // 将任务分配到对应的列
  testTasks.value.forEach(task => {
    const column = columns.find(col => col.status === task.status)
    if (column) {
      column.tasks.push(task)
    }
  })
  
  return columns
})

// 事件处理
const onTaskCreated = (newTask) => {
  ElMessage.success('✨ 任务创建成功！')
  console.log('新任务已创建:', newTask)
}

const addTaskToColumn = (status) => {
  ElMessage.info(`➕ 准备在"${status}"列添加新任务`)
}

const showTaskDetail = (task) => {
  ElMessage.info(`👀 查看任务详情: ${task.name}`)
}

const handleTaskQuickAction = (payload) => {
  const { action, task } = payload
  ElMessage.success(`🎯 执行操作"${action}": ${task.name}`)
}

const handleQuickStatusChange = (payload) => {
  const { taskId, newStatus } = payload
  ElMessage.success(`🔄 任务状态已更新为: ${newStatus}`)
}

onMounted(() => {
  console.log('🚀 现代化任务看板已加载')
})
</script>

<style scoped>
.modern-task-management {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.task-header {
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.kanban-board {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.task-column {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  min-height: 500px;
}

.task-column-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 15px;
  border-bottom: 2px solid #f0f0f0;
  margin-bottom: 15px;
}

.task-column-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.task-cards-container {
  max-height: 70vh;
  overflow-y: auto;
}

.empty-column {
  text-align: center;
  padding: 40px 20px;
  color: #999;
}

/* 快速创建按钮样式 */
.quick-create-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  font-weight: 600;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
  transition: all 0.3s ease;
  color: white;
}

.quick-create-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
}

/* 增强任务卡片在看板中的显示 */
.kanban-board .enhanced-task-card {
  margin-bottom: 16px;
  transition: all 0.3s ease;
}

.kanban-board .enhanced-task-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(64, 158, 255, 0.2);
}
</style>