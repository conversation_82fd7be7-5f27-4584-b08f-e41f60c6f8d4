// IT资产管理系统 - 位置实体
// 文件路径: /Models/Entities/Location.cs
// 功能: 定义位置实体，对应locations表

using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ItAssetsSystem.Models.Entities
{
    /// <summary>
    /// 位置实体
    /// </summary>
    public class Location
    {
        public Location()
        {
            Children = new HashSet<Location>();
            Assets = new HashSet<Asset>();
            LocationUsers = new HashSet<LocationUser>();
            LocationHistories = new HashSet<LocationHistory>();
        }

        /// <summary>
        /// 位置ID
        /// </summary>
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// 位置编码
        /// </summary>
        [Required]
        [StringLength(50)]
        public string Code { get; set; }

        /// <summary>
        /// 位置名称
        /// </summary>
        [Required]
        [StringLength(100)]
        public string Name { get; set; }

        /// <summary>
        /// 位置类型 0:工厂, 1:产线, 2:工序, 3:工位, 4:设备位置
        /// </summary>
        public int Type { get; set; }

        /// <summary>
        /// 位置类型（别名，用于兼容）
        /// </summary>
        [NotMapped]
        public int LocationType => Type;

        /// <summary>
        /// 父级位置ID
        /// </summary>
        public int? ParentId { get; set; }

        /// <summary>
        /// 位置路径（用逗号分隔的ID）
        /// </summary>
        [StringLength(200)]
        public string Path { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        [StringLength(200)]
        public string Description { get; set; }

        /// <summary>
        /// 默认部门ID（使用部门）
        /// </summary>
        [Column("DefaultDepartmentId")]
        public int? DefaultDepartmentId { get; set; }

        /// <summary>
        /// 默认负责人ID（部门负责人）
        /// </summary>
        [Column("DefaultResponsiblePersonId")]
        public int? ManagerId { get; set; }

        /// <summary>
        /// 是否激活
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; }

        /// <summary>
        /// 父级位置
        /// </summary>
        [ForeignKey("ParentId")]
        public virtual Location Parent { get; set; }

        /// <summary>
        /// 子位置集合
        /// </summary>
        public virtual ICollection<Location> Children { get; set; }

        /// <summary>
        /// 资产集合
        /// </summary>
        public virtual ICollection<Asset> Assets { get; set; }

        /// <summary>
        /// 默认部门（使用部门）
        /// </summary>
        [ForeignKey("DefaultDepartmentId")]
        public virtual Department Department { get; set; }

        /// <summary>
        /// 默认负责人（部门负责人）
        /// </summary>
        [ForeignKey("ManagerId")]
        public virtual User Manager { get; set; }

        /// <summary>
        /// 位置用户关联
        /// </summary>
        public virtual ICollection<LocationUser> LocationUsers { get; set; }

        /// <summary>
        /// 位置历史记录
        /// </summary>
        public virtual ICollection<LocationHistory> LocationHistories { get; set; }
    }
} 