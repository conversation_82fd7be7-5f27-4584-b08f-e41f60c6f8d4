/**
 * 航空航天级IT资产管理系统 - 返厂管理API
 * 文件路径: src/api/returnToFactory.js
 * 功能描述: 提供返厂管理相关的API服务
 */

import request from '@/utils/request'

// 返厂API基础路径
const baseUrl = '/ReturnToFactory'

export default {
  /**
   * 获取返厂记录列表
   * @param {Object} params - 查询参数
   * @returns {Promise}
   */
  getReturnToFactoryList(params) {
    return request.get(baseUrl, params)
  },
  
  /**
   * 获取返厂记录详情
   * @param {number|string} id - 返厂记录ID
   * @returns {Promise}
   */
  getReturnToFactoryById(id) {
    return request.get(`${baseUrl}/${id}`)
  },
  
  /**
   * 创建返厂记录
   * @param {Object} data - 返厂数据
   * @returns {Promise}
   */
  createReturnToFactory(data) {
    return request.post(baseUrl, data)
  },
  
  /**
   * 更新返厂记录
   * @param {number|string} id - 返厂记录ID
   * @param {Object} data - 返厂数据
   * @returns {Promise}
   */
  updateReturnToFactory(id, data) {
    return request.put(`${baseUrl}/${id}`, data)
  },
  
  /**
   * 删除返厂记录
   * @param {number|string} id - 返厂记录ID
   * @returns {Promise}
   */
  deleteReturnToFactory(id) {
    return request.delete(`${baseUrl}/${id}`)
  },
  
  /**
   * 更新返厂状态
   * @param {number|string} id - 返厂记录ID
   * @param {Object} data - 状态更新数据
   * @returns {Promise}
   */
  updateReturnStatus(id, data) {
    return request.put(`${baseUrl}/${id}/status`, data)
  },
  
  /**
   * 确认返厂完成
   * @param {number|string} id - 返厂记录ID
   * @param {Object} data - 完成数据
   * @returns {Promise}
   */
  completeReturn(id, data) {
    return request.post(`${baseUrl}/${id}/complete`, data)
  },
  
  /**
   * 返厂维修后补充备件库存
   * @param {number|string} id - 返厂记录ID
   * @param {Object} data - 备件补充数据
   * @returns {Promise}
   */
  replenishSpareParts(id, data) {
    return request.post(`${baseUrl}/${id}/replenish-spare-parts`, data)
  },
  
  /**
   * 获取返厂统计数据
   * @param {Object} params - 查询参数
   * @returns {Promise}
   */
  getReturnStatistics(params) {
    return request.get(`${baseUrl}/statistics`, params)
  },
  
  /**
   * 导出返厂数据
   * @param {Object} params - 查询参数
   * @returns {Promise}
   */
  exportReturns(params) {
    return request.download(`${baseUrl}/export`, params, 'returns.xlsx')
  }
}
