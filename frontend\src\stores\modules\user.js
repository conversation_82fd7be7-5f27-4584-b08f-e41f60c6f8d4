/**
 * 系统用户状态管理模块
 * 文件路径: src/stores/modules/user.js
 * 功能描述: 管理用户登录状态、权限和信息
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import authApi from '@/api/auth'
import userApi, { getUsers as apiGetUsers } from '@/api/user'
import mockAuthApi from '@/mock/auth'
import { setToken, getToken, clearToken, setUserInfo, getUserInfo } from '@/utils/auth'
import systemConfig from '@/config/system' // For defaultAvatar

// --- START: Modified for dynamic static files base URL ---
const viteStaticFilesBaseUrlSetting = import.meta.env.VITE_STATIC_FILES_BASE_URL;
console.log('[user.js] Environment VITE_STATIC_FILES_BASE_URL setting is:', viteStaticFilesBaseUrlSetting);

const getOrigin = () => {
  const protocol = window.location.protocol;
  const hostname = window.location.hostname;
  if (hostname.toLowerCase() === 'localhost') {
    return `${protocol}//${hostname}`; // No port for localhost for static files as per requirement
  }
  const port = window.location.port;
  if ((protocol === 'http:' && port && port !== '80') || (protocol === 'https:' && port && port !== '443')) {
    return `${protocol}//${hostname}:${port}`;
  }
  return `${protocol}//${hostname}`;
};
console.log('[user.js] Calculated getOrigin():', getOrigin());

// 将 getStaticFilesBaseUrl 改为导出函数，放在模块作用域
export function getStaticFilesBaseUrl() {
  const origin = getOrigin();
  // If VITE_STATIC_FILES_BASE_URL is an absolute URL, use it directly.
  if (viteStaticFilesBaseUrlSetting && (viteStaticFilesBaseUrlSetting.startsWith('http://') || viteStaticFilesBaseUrlSetting.startsWith('https://')))
    return viteStaticFilesBaseUrlSetting.endsWith('/') ? viteStaticFilesBaseUrlSetting : viteStaticFilesBaseUrlSetting + '/';

  // If VITE_STATIC_FILES_BASE_URL is a path (e.g., /files/, /assets/, or even empty/undefined for root)
  let pathPrefix = '';
  if (viteStaticFilesBaseUrlSetting && viteStaticFilesBaseUrlSetting !== '/') {
    pathPrefix = viteStaticFilesBaseUrlSetting.replace(/^\/?(.*?)\/?$/, '$1'); // Trim leading/trailing slashes, get content
  }
  
  let finalBaseUrl = origin;
  if (pathPrefix) {
    finalBaseUrl = `${origin}/${pathPrefix}`;
  }
  
  finalBaseUrl = finalBaseUrl.endsWith('/') ? finalBaseUrl : finalBaseUrl + '/';
  console.log('[user.js getStaticFilesBaseUrl] Constructed static files base URL:', finalBaseUrl);
  return finalBaseUrl;
}
// --- END: Modified for dynamic static files base URL ---

// 从本地存储获取持久化的数据
const getStoredToken = () => localStorage.getItem('token') || ''
const getStoredUserInfo = () => {
  const storedInfo = localStorage.getItem('userInfo')
  return storedInfo ? JSON.parse(storedInfo) : null
}
const getStoredPermissions = () => {
  const storedPermissions = localStorage.getItem('permissions')
  return storedPermissions ? JSON.parse(storedPermissions) : []
}
const getStoredRoles = () => {
  const storedRoles = localStorage.getItem('roles')
  return storedRoles ? JSON.parse(storedRoles) : []
}

console.log('[user.js] Reading initial state from localStorage...');
const initialTokenFromStorage = localStorage.getItem('token') || '';
console.log('[user.js] Initial token from localStorage:', initialTokenFromStorage);

const rawUserInfoFromStorage = localStorage.getItem('userInfo');
console.log('[user.js] Initial raw userInfo string from localStorage:', rawUserInfoFromStorage);
const initialUserInfoParsedFromStorage = rawUserInfoFromStorage ? JSON.parse(rawUserInfoFromStorage) : {};
console.log('[user.js] Initial parsed userInfo from localStorage:', initialUserInfoParsedFromStorage);

const initialAvatarFromStorage = localStorage.getItem('avatar') || '';
console.log('[user.js] Initial avatar string from localStorage:', initialAvatarFromStorage);

// 根据权限生成路由
function generateRoutes(permissions = []) {
  // 基础路由，所有用户都可以访问
  const baseRoutes = [
    {
      path: '/dashboard',
      name: 'Dashboard',
      meta: { title: '仪表盘', icon: 'dashboard' }
    }
  ];

  // 资产相关路由
  if (permissions.includes('asset:view') || permissions.includes('asset:list')) {
    baseRoutes.push({
      path: '/asset',
      name: 'Asset',
      meta: { title: '资产管理', icon: 'computer' },
      children: [
        {
          path: 'list',
          name: 'AssetList',
          meta: { title: '资产列表' }
        }
      ]
    });
  }

  // 系统管理路由，通常只有管理员可以访问
  if (permissions.includes('system:view') || permissions.includes('user:manage')) {
    baseRoutes.push({
      path: '/system',
      name: 'System',
      meta: { title: '系统管理', icon: 'setting' },
      children: [
        {
          path: 'user',
          name: 'User',
          meta: { title: '用户管理' }
        }
      ]
    });
  }

  return baseRoutes;
}

// 定义用户存储
export const useUserStore = defineStore('user', () => {
  // 状态
  const token = ref(initialTokenFromStorage);
  const userInfo = ref(initialUserInfoParsedFromStorage.id ? initialUserInfoParsedFromStorage : null);
  const roles = ref(JSON.parse(localStorage.getItem('roles') || '[]'));
  const permissions = ref(JSON.parse(localStorage.getItem('permissions') || '[]'));
  const avatar = ref(initialAvatarFromStorage); // Stores relative path
  const isLogoutDialogVisible = ref(false);
  const routes = ref(generateRoutes(permissions.value))
  const name = computed(() => userInfo.value?.name || userInfo.value?.userName || '')
  const usersList = ref([])

  // Ensure initialUserInfoParsedFromStorage.avatar is correctly used if present
  // userInfo.value = initialUserInfoParsedFromStorage.id ? initialUserInfoParsedFromStorage : null;
  // avatar.value = initialUserInfoParsedFromStorage.avatar || initialAvatarFromStorage; // Prioritize avatar from userInfo obj

  console.log('[user.js store setup] Initial userInfo.value from localStorage:', JSON.parse(JSON.stringify(userInfo.value)));
  console.log('[user.js store setup] Initial avatar.value from localStorage:', avatar.value);

  // getters
  const isLogin = computed(() => !!token.value)
  const userRoles = computed(() => roles.value)
  const userPermissions = computed(() => permissions.value)

  // --- START: Modified computedAvatarUrl getter ---
  const computedAvatarUrl = computed(() => {
    const currentAvatarPath = userInfo.value?.avatar || avatar.value;
    console.log(`[user.js computedAvatarUrl] Trying to compute avatar. userInfo.value?.avatar: '${userInfo.value?.avatar}', avatar.value: '${avatar.value}'. Using: '${currentAvatarPath}'`);

    if (currentAvatarPath) {
      if (currentAvatarPath.startsWith('http://') || currentAvatarPath.startsWith('https://')) {
        console.log('[user.js computedAvatarUrl] Path is already absolute URL:', currentAvatarPath);
        return currentAvatarPath;
      }
      const baseUrl = getStaticFilesBaseUrl();

      const formattedBaseUrl = baseUrl.endsWith('/') ? baseUrl : baseUrl + '/';
      let cleanAvatarPath = currentAvatarPath.startsWith('/') ? currentAvatarPath.substring(1) : currentAvatarPath;
      
      if (formattedBaseUrl.endsWith('/uploads/') && cleanAvatarPath.startsWith('uploads/')) {
        cleanAvatarPath = cleanAvatarPath.substring('uploads/'.length);
      }

      const finalUrl = formattedBaseUrl + cleanAvatarPath;
      console.log('[user.js computedAvatarUrl] Final constructed User Avatar URL:', finalUrl);
      return finalUrl;
    }

    console.log('[user.js computedAvatarUrl] currentAvatarPath IS EMPTY or invalid. Returning empty string to hide default external avatar.');
    return '';
  });
  // --- END: Modified computedAvatarUrl getter ---
  
  // actions
  /**
   * 检查用户是否存在
   * @param {string} username - 用户名
   * @returns {Promise<Object>} 检查结果
   */
  async function checkUserExists(username) {
    try {
      console.log('正在检查用户是否存在:', username);
      
      // 如果使用模拟数据，直接返回预设结果
      if (import.meta.env.VITE_USE_MOCK === 'true') {
        console.log('使用模拟数据检查用户');
        // 模拟检查用户是否存在
        const validUsers = ['admin', 'test', 'user', 'manager'];
        const exists = validUsers.includes(username.toLowerCase());
        
        return {
          exists,
          message: exists ? '用户存在' : '用户不存在'
        };
      }
      
      // 调用后端API检查用户是否存在
      try {
        const response = await userApi.checkUser(username);
        console.log('检查用户存在API响应:', response);
        
        if (response && response.success) {
          return {
            exists: true,
            message: '用户存在',
            user: response.data
          };
        } else {
          return {
            exists: false,
            message: response?.message || '用户不存在'
          };
        }
      } catch (error) {
        console.error('检查用户API错误:', error);
        
        // 如果API调用失败，使用简单方式验证
        if (username.toLowerCase() === 'admin' || 
            username.toLowerCase() === 'test' || 
            username.toLowerCase() === 'user') {
          return {
            exists: true,
            message: '用户存在(本地验证)'
          };
        }
        
        return {
          exists: false,
          message: '用户不存在或无法验证'
        };
      }
    } catch (error) {
      console.error('检查用户存在方法出错:', error);
      throw error;
    }
  }
  
  /**
   * 用户登录
   * @param {Object} loginData - 登录表单数据
   * @returns {Promise<Object>} 登录结果
   */
  async function login(loginData) {
    try {
      console.log('[user.js login] Attempting login. Request data:', JSON.parse(JSON.stringify(loginData)));
      const apiToUse = import.meta.env.VITE_USE_MOCK === 'true' ? mockAuthApi : authApi;
      const response = await apiToUse.login(loginData);
      console.log('[user.js login] Login API response received:', JSON.parse(JSON.stringify(response)));
      
      if (response && response.success) {
        const data = response.data;
        console.log('[user.js login] Login success. Response data content:', JSON.parse(JSON.stringify(data)));
        
        if (data && data.token) {
          token.value = data.token;
          setToken(data.token);
          
          // 登录成功后，立即获取完整的用户信息，确保获取到头像URL
          try {
            await getUserInfoAction();
            console.log('[user.js login] Successfully loaded complete user profile after login');
          } catch (profileError) {
            console.error('[user.js login] Error loading user profile after login:', profileError);
            // 即使获取个人资料失败，也继续使用登录接口返回的基本用户信息
          }
        }
        
        if (data && data.user && !userInfo.value) {
          // 只有在getUserInfoAction失败时才使用登录接口返回的用户信息
          userInfo.value = data.user;
          setUserInfo(data.user); // Persists the whole user object to localStorage
          console.log('[user.js login] Using basic user info from login API:', JSON.parse(JSON.stringify(data.user)));
          console.log(`[user.js login] User object from login API contains avatar: '${data.user.avatar}'`);
          
          if (data.user.avatar) {
            avatar.value = data.user.avatar;
            localStorage.setItem('avatar', data.user.avatar); // Keep dedicated avatar localStorage for potential direct use elsewhere
            console.log(`[user.js login] Updated avatar.value to: '${avatar.value}' and localStorage 'avatar'.`);
          } else {
            avatar.value = '';
            localStorage.removeItem('avatar');
            console.log('[user.js login] No avatar in API user object from login. Cleared local avatar state.');
          }
          // ... (roles, permissions handling) ...
        }
        
        console.log(`[user.js login] FINAL state after login: userInfo.value.avatar='${userInfo.value?.avatar}', avatar.value='${avatar.value}'`);

        // 登录成功后启动通知轮询
        try {
          const { useNotificationStore } = await import('./notification')
          const notificationStore = useNotificationStore()
          console.log('登录成功，启动通知轮询...')
          notificationStore.startPolling()
        } catch (error) {
          console.error('启动通知轮询失败:', error)
        }

        return response;
      } else {
        console.warn('[user.js login] Login failed or success flag is false. Message:', response?.message);
        return response;
      }
    } catch (error) {
      console.error('[user.js login] Login action error:', error);
      throw error;
    }
  }
  
  /**
   * 获取用户信息
   * @returns {Promise<Object>} 用户信息
   */
  async function getUserInfoAction() {
    try {
      console.log('[user.js getUserInfoAction] Attempting to fetch user info via authApi.getUserInfo() which calls /v2/profile.');
      const response = await authApi.getUserInfo(); // Calls GET /v2/profile
      console.log('[user.js getUserInfoAction] API response from authApi.getUserInfo():', JSON.parse(JSON.stringify(response)));
      
      if (response && response.success && response.data) {
        const userDataFromApi = response.data;
        console.log('[user.js getUserInfoAction] Successfully fetched user data:', JSON.parse(JSON.stringify(userDataFromApi)));
        console.log(`[user.js getUserInfoAction] User data from API contains avatar: '${userDataFromApi.avatar}'`);

        userInfo.value = userDataFromApi;
        setUserInfo(userDataFromApi); // Persists the whole user object to localStorage
        
        if (userDataFromApi.avatar) {
          avatar.value = userDataFromApi.avatar;
          localStorage.setItem('avatar', userDataFromApi.avatar);
          console.log(`[user.js getUserInfoAction] Updated avatar.value to: '${avatar.value}' and localStorage 'avatar'.`);
        } else {
          avatar.value = '';
          localStorage.removeItem('avatar');
          console.log('[user.js getUserInfoAction] No avatar in API user data. Cleared local avatar state.');
        }
        // ... (roles, permissions handling if they come from this endpoint too) ...
        console.log(`[user.js getUserInfoAction] FINAL state: userInfo.value.avatar='${userInfo.value?.avatar}', avatar.value='${avatar.value}'`);
        return userInfo.value;
      } else {
        console.error('[user.js getUserInfoAction] Failed to fetch user info or API error. Response:', response);
        // Consider calling logout() or resetState() here on failure
        throw new Error(response?.message || '获取用户信息失败或API响应错误');
      }
    } catch (error) {
      console.error('[user.js getUserInfoAction] Error in getUserInfoAction:', error);
      // Consider calling logout() or resetState() here on failure
      throw error;
    }
  }
  
  /**
   * 更新用户个人信息
   * @param {Object} profileData - 个人信息数据
   * @returns {Promise<Object>} 更新结果
   */
  async function updateProfile(profileData) {
    try {
      const response = await userApi.updateProfile(profileData)
      
      if (response && response.success) {
        // 更新本地存储的用户信息
        userInfo.value = { ...userInfo.value, ...profileData }
        setUserInfo(userInfo.value)
        return response
      } else {
        throw new Error(response?.message || '更新个人信息失败')
      }
    } catch (error) {
      throw error
    }
  }
  
  /**
   * 设置用户头像
   * @param {string} newAvatarPath - 头像相对路径
   */
  function setAvatar(newAvatarPath) {
    if (!newAvatarPath) {
      console.warn('[userStore.setAvatar] 尝试设置空头像路径')
      return
    }

    console.log(`[userStore.setAvatar] 设置相对头像路径: ${newAvatarPath}`);
    avatar.value = newAvatarPath
    localStorage.setItem('avatar', newAvatarPath)

    // 同时更新userInfo对象中的avatar属性
    if (userInfo.value) {
      userInfo.value.avatar = newAvatarPath
      localStorage.setItem('userInfo', JSON.stringify(userInfo.value))
      console.log('[userStore.setAvatar] 已更新userInfo中的avatar')
    } else {
      console.warn('[userStore.setAvatar] userInfo为空，无法更新')
    }
  }

  /**
   * 设置用户头像（使用完整URL）
   * @param {string} fullAvatarUrl - 头像完整URL（包含域名和路径）
   */
  function setAvatarWithFullUrl(fullAvatarUrl) {
    if (!fullAvatarUrl) {
      console.warn('[userStore.setAvatarWithFullUrl] 尝试设置空头像URL')
      return
    }

    console.log(`[userStore.setAvatarWithFullUrl] 设置完整头像URL: ${fullAvatarUrl}`);
    
    // 为了兼容性，我们需要提取相对路径存储到avatar.value
    try {
      // 尝试从完整URL中提取相对路径
      // 如果URL包含域名，我们需要移除域名部分
      const url = new URL(fullAvatarUrl);
      // 获取pathname (例如 /uploads/avatars/user1.jpg)
      let relativePath = url.pathname;

      // 如果路径以/开头，移除它以保持与以前的格式一致
      if (relativePath.startsWith('/')) {
        relativePath = relativePath.substring(1);
      }

      // 保存相对路径
      setAvatar(relativePath);
      
      // 缓存完整URL以便直接访问，避免路径解析问题
      localStorage.setItem('fullAvatarUrl', fullAvatarUrl);
    } catch (error) {
      console.error('[userStore.setAvatarWithFullUrl] 解析URL失败，保存完整URL:', error);
      // 如果解析失败，直接保存完整路径
      setAvatar(fullAvatarUrl);
    }
  }
  
  /**
   * 修改用户密码
   * @param {Object} passwordData - 密码数据 { oldPassword, newPassword }
   * @returns {Promise<Object>} 修改结果
   */
  async function changePassword(passwordData) {
    try {
      const response = await userApi.changePassword(passwordData)
      return response
    } catch (error) {
      throw error
    }
  }
  
  /**
   * 设置登出对话框可见性
   * @param {boolean} visible - 是否可见
   */
  function setLogoutDialogVisible(visible) {
    isLogoutDialogVisible.value = visible
  }
  
  // 用户登出
  async function logout() {
    try {
      if (token.value) {
        await authApi.logout()
      }
      resetState()
      return true
    } catch (error) {
      resetState()
      throw error
    }
  }
  
  // 重置状态
  function resetState() {
    token.value = ''
    userInfo.value = null // Set to null instead of {} for clearer non-existence check
    roles.value = []
    permissions.value = []
    avatar.value = '' // Clear top-level avatar
    isLogoutDialogVisible.value = false
    routes.value = []
    clearToken()
    localStorage.removeItem('roles')
    localStorage.removeItem('permissions')
    localStorage.removeItem('userInfo')
    localStorage.removeItem('avatar') // Clear dedicated avatar item

    // 重置通知状态（使用动态导入的Promise方式）
    import('./notification').then(({ useNotificationStore }) => {
      const notificationStore = useNotificationStore()
      notificationStore.resetNotificationState()
    }).catch(error => {
      console.error('重置通知状态失败:', error)
    })
  }
  
  /**
   * 设置用户信息
   * @param {Object} info - 用户信息
   */
  function setUserInfoAction(info) {
    userInfo.value = info
    setUserInfo(info)
  }

  /**
   * 获取用户列表
   * @param {Object} params - 查询参数
   * @returns {Promise<Array>} 用户列表
   */
  async function getUsers(params) {
    try {
      const response = await userApi.getUserList(params)
      if (response && response.success && response.data) {
        usersList.value = response.data
        return response.data
      } else {
        throw new Error(response?.message || '获取用户列表失败')
      }
    } catch (error) {
      console.error('获取用户列表失败:', error)
      throw error
    }
  }

  async function getInfo() { // This is another function that fetches user info, ensure it's clear which one is primary
    console.log('[user.js getInfo] Attempting to get/refresh user info.');
    if (!userInfo.value || Object.keys(userInfo.value).length === 0 || !userInfo.value.id) { // Added !userInfo.value.id for robustness
      console.log('[user.js getInfo] No user info in state or ID missing, attempting to fetch from API via getUserInfoAction.');
      try {
        await getUserInfoAction(); // Call the consolidated action
      } catch (error) {
        console.error('[user.js getInfo] Error calling getUserInfoAction, attempting logout.', error);
        await logout(); // Logout on failure to get essential user info
        throw error; // Re-throw to signal failure
      }
    } else {
      console.log('[user.js getInfo] User info already in state:', JSON.parse(JSON.stringify(userInfo.value)));
    }
    return userInfo.value;
  }

  return {
    token,
    userInfo,
    roles,
    permissions,
    avatar,
    computedAvatarUrl,
    isLogoutDialogVisible,
    routes,
    name,
    isLogin,
    userRoles,
    userPermissions,
    usersList,
    login,
    getUserInfo: getUserInfoAction,
    updateProfile,
    setAvatar,
    setAvatarWithFullUrl,
    changePassword, 
    setLogoutDialogVisible,
    logout,
    resetState,
    setUserInfo: setUserInfoAction,
    checkUserExists,
    getUsers,
    getInfo
  }
})

/**
 * 全局头像URL拼接工具，统一所有用户头像显示逻辑
 * @param {string} avatarPath - 头像相对路径或绝对URL
 * @returns {string} - 完整可访问的头像URL
 */
export function getFullAvatarUrl(avatarPath) {
  if (!avatarPath || typeof avatarPath !== 'string') return ''
  if (avatarPath.startsWith('http://') || avatarPath.startsWith('https://')) {
    return avatarPath
  }
  const baseUrl = getStaticFilesBaseUrl()
  const formattedBaseUrl = baseUrl.endsWith('/') ? baseUrl : baseUrl + '/'
  let cleanAvatarPath = avatarPath.startsWith('/') ? avatarPath.substring(1) : avatarPath
  if (formattedBaseUrl.endsWith('/uploads/') && cleanAvatarPath.startsWith('uploads/')) {
    cleanAvatarPath = cleanAvatarPath.substring('uploads/'.length)
  }
  return formattedBaseUrl + cleanAvatarPath
}