// File: Application/Features/Tasks/Commands/UpdateCommentCommand.cs
// Description: 更新评论命令

using System;
using System.Collections.Generic;
using MediatR;
using ItAssetsSystem.Application.Common.Dtos;
using ItAssetsSystem.Application.Features.Tasks.Dtos;

namespace ItAssetsSystem.Application.Features.Tasks.Commands
{
    /// <summary>
    /// 更新评论命令
    /// </summary>
    public class UpdateCommentCommand : IRequest<ApiResponse<CommentDto>>
    {
        /// <summary>
        /// 评论ID
        /// </summary>
        public long CommentId { get; set; }

        /// <summary>
        /// 任务ID
        /// </summary>
        public long TaskId { get; set; }

        /// <summary>
        /// 评论内容
        /// </summary>
        public string Content { get; set; } = string.Empty;

        /// <summary>
        /// 提及的用户ID列表
        /// </summary>
        public List<int>? MentionedUserIds { get; set; }

        /// <summary>
        /// 是否置顶
        /// </summary>
        public bool? IsPinned { get; set; }

        /// <summary>
        /// 当前用户ID
        /// </summary>
        public int CurrentUserId { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public UpdateCommentCommand()
        {
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="commentId">评论ID</param>
        /// <param name="taskId">任务ID</param>
        /// <param name="content">评论内容</param>
        /// <param name="currentUserId">当前用户ID</param>
        /// <param name="mentionedUserIds">提及的用户ID列表</param>
        /// <param name="isPinned">是否置顶</param>
        public UpdateCommentCommand(long commentId, long taskId, string content, int currentUserId, 
            List<int>? mentionedUserIds = null, bool? isPinned = null)
        {
            CommentId = commentId;
            TaskId = taskId;
            Content = content;
            CurrentUserId = currentUserId;
            MentionedUserIds = mentionedUserIds;
            IsPinned = isPinned;
        }
    }
}
