// IT资产管理系统 - 积分计算服务
// 文件路径: /Core/Services/ScoreCalculationService.cs
// 功能: 实现批量定时计算用户积分和排行榜

using ItAssetsSystem.Infrastructure.Data;
// using ItAssetsSystem.Models.Entities; // UserPoints and PointLeaderboard were here, no longer used
using ItAssetsSystem.Domain.Entities.Tasks; // For V2 Task entity
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace ItAssetsSystem.Core.Services
{
    /// <summary>
    /// 积分计算服务 - 负责定时计算用户积分和排行榜 (积分/排行榜功能已禁用，因相关表已删除)
    /// </summary>
    public class ScoreCalculationService : BackgroundService
    {
        private readonly ILogger<ScoreCalculationService> _logger;
        private readonly IServiceProvider _serviceProvider;
        
        // 计算间隔 - 2小时 (积分计算已禁用)
        // private static readonly TimeSpan CalculationInterval = TimeSpan.FromHours(2);
        
        // 逾期任务检查间隔 - 1小时
        private static readonly TimeSpan OverdueCheckInterval = TimeSpan.FromHours(1);
        
        /// <summary>
        /// 构造函数
        /// </summary>
        public ScoreCalculationService(
            ILogger<ScoreCalculationService> logger,
            IServiceProvider serviceProvider)
        {
            _logger = logger;
            _serviceProvider = serviceProvider;
        }
        
        /// <summary>
        /// 后台服务执行方法
        /// </summary>
        protected override async System.Threading.Tasks.Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("ScoreCalculationService started. Overdue task check interval: {Interval} hours. Score/Leaderboard calculation is DISABLED.", OverdueCheckInterval.TotalHours);
            
            // 启动时立即执行一次计算
            // await CalculateUserScoresAsync(stoppingToken); // DISABLED
            await CheckOverdueTasksAsync(stoppingToken);
            
            // 创建两个计时器，一个用于积分计算，一个用于逾期任务检查
            // using var scoreTimer = new PeriodicTimer(CalculationInterval); // DISABLED
            using var overdueTimer = new PeriodicTimer(OverdueCheckInterval);
            
            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    // 等待计时器
                    // System.Threading.Tasks.Task scoreTimerTask = scoreTimer.WaitForNextTickAsync(stoppingToken).AsTask(); // DISABLED
                    // System.Threading.Tasks.Task overdueTimerTask = overdueTimer.WaitForNextTickAsync(stoppingToken).AsTask(); // Original
                    // System.Threading.Tasks.Task completedTask = await System.Threading.Tasks.Task.WhenAny(scoreTimerTask, overdueTimerTask); // Original

                    await overdueTimer.WaitForNextTickAsync(stoppingToken);
                    
                    // if (completedTask == scoreTimerTask) // DISABLED
                    // {
                    //     // 计算用户积分
                    //     await CalculateUserScoresAsync(stoppingToken);
                    // }
                    
                    // if (completedTask == overdueTimerTask || !scoreTimerTask.IsCompleted) // If only overdueTimer exists or it ticked
                    // {
                        // 检查逾期任务
                    await CheckOverdueTasksAsync(stoppingToken);
                    // }
                }
                catch (OperationCanceledException)
                {
                    break;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error in ScoreCalculationService execution loop.");
                    await System.Threading.Tasks.Task.Delay(TimeSpan.FromMinutes(1), stoppingToken);
                }
            }
            
            _logger.LogInformation("ScoreCalculationService stopped.");
        }
        
        /* // CalculateUserScoresAsync and related methods are disabled as ScoreLogs, UserPoints, PointLeaderboards tables are deleted.
        private async System.Threading.Tasks.Task CalculateUserScoresAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("CalculateUserScoresAsync called, but functionality is DISABLED.");
            await System.Threading.Tasks.Task.CompletedTask;
            // _logger.LogInformation("开始计算用户积分...");
            // DateTime startTime = DateTime.Now;
            
            // try
            // {
            //     using var scope = _serviceProvider.CreateScope();
            //     var dbContext = scope.ServiceProvider.GetRequiredService<AppDbContext>();
                
            //     // 获取过去2小时内的积分变动记录
            //     var twoHoursAgo = DateTime.Now.AddHours(-2);
            //     var recentScoreLogs = await dbContext.ScoreLogs // ERROR: ScoreLogs DbSet does not exist
            //         .Where(l => l.CreatedAt >= twoHoursAgo)
            //         .GroupBy(l => l.UserId)
            //         .Select(g => new 
            //         {
            //             UserId = g.Key,
            //             TotalScore = g.Sum(l => l.ScoreChange),
            //             DailyScore = g.Where(l => l.CreatedAt.Date == DateTime.Today).Sum(l => l.ScoreChange),
            //             WeeklyScore = g.Where(l => l.CreatedAt >= DateTime.Now.AddDays(-(int)DateTime.Now.DayOfWeek)).Sum(l => l.ScoreChange),
            //             MonthlyScore = g.Where(l => l.CreatedAt.Month == DateTime.Now.Month && l.CreatedAt.Year == DateTime.Now.Year).Sum(l => l.ScoreChange)
            //         })
            //         .ToListAsync(cancellationToken);
                
            //     // 更新用户积分
            //     foreach (var scoreData in recentScoreLogs)
            //     {
            //         var userPoints = await dbContext.UserPoints // ERROR: UserPoints DbSet does not exist
            //             .FirstOrDefaultAsync(p => p.UserId == scoreData.UserId, cancellationToken);
                    
            //         if (userPoints == null)
            //         {
            //             // 如果用户积分记录不存在，创建新记录
            //             userPoints = new UserPoints // UserPoints entity does not exist / is not defined for V2
            //             {
            //                 UserId = scoreData.UserId,
            //                 Points = scoreData.TotalScore,
            //                 DailyPoints = scoreData.DailyScore,
            //                 WeeklyPoints = scoreData.WeeklyScore,
            //                 MonthlyPoints = scoreData.MonthlyScore,
            //                 UpdatedAt = DateTime.Now
            //             };
            //             dbContext.UserPoints.Add(userPoints);
            //         }
            //         else
            //         {
            //             // 更新已有积分记录
            //             userPoints.Points += scoreData.TotalScore;
            //             userPoints.DailyPoints = scoreData.DailyScore;
            //             userPoints.WeeklyPoints = scoreData.WeeklyScore;
            //             userPoints.MonthlyPoints = scoreData.MonthlyScore;
            //             userPoints.UpdatedAt = DateTime.Now;
            //         }
            //     }
                
            //     // 计算并更新排行榜
            //     await UpdateLeaderboardAsync(dbContext, cancellationToken);
                
            //     // 保存所有更改
            //     await dbContext.SaveChangesAsync(cancellationToken);
                
            //     _logger.LogInformation("用户积分计算完成，处理了 {UserCount} 个用户的积分", recentScoreLogs.Count);
            // }
            // catch (Exception ex)
            // {
            //     _logger.LogError(ex, "计算用户积分时发生异常");
            //     throw;
            // }
            
            // TimeSpan duration = DateTime.Now - startTime;
            // _logger.LogInformation("积分计算完成，耗时: {Duration} 毫秒", duration.TotalMilliseconds);
        }
        */
        
        /// <summary>
        /// 检查逾期任务
        /// </summary>
        private async System.Threading.Tasks.Task CheckOverdueTasksAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("Checking overdue tasks...");
            DateTime startTime = DateTime.UtcNow; // Use UtcNow for consistency
            
            try
            {
                using var scope = _serviceProvider.CreateScope();
                var dbContext = scope.ServiceProvider.GetRequiredService<AppDbContext>();
                
                var now = DateTime.UtcNow; // Use UtcNow
                // Use V2 Task entity: ItAssetsSystem.Domain.Entities.Tasks.Task
                var overdueTasks = await dbContext.Tasks // This now refers to DbSet<Domain.Entities.Tasks.Task>
                    .Where(t => t.Status != "Completed" && 
                                t.Status != "Cancelled" && // Assuming "Cancelled" is a valid V2 status string
                                t.Status != "Canceled" && // Adding "Canceled" for broader compatibility with V1 data if any mixed usage
                                t.PlanEndDate.HasValue && 
                                t.PlanEndDate.Value < now && 
                                !t.IsOverdueAcknowledged) // V2 Task has IsOverdueAcknowledged, not IsOverdue
                    .ToListAsync(cancellationToken);
                
                if (overdueTasks.Any())
                {
                    foreach (var task in overdueTasks)
                    {
                        // Instead of a direct IsOverdue boolean, we might set a status or log history.
                        // For now, we'll just log it as the original code modified a non-existent IsOverdue.
                        // If IsOverdueAcknowledged should be set, uncomment below.
                        // task.IsOverdueAcknowledged = true; // This field indicates if user has seen it's overdue.
                        task.LastUpdatedTimestamp = now; // V2 Task uses LastUpdatedTimestamp
                        _logger.LogWarning("Task ID {TaskId} is overdue. PlanEndDate: {PlanEndDate}", task.TaskId, task.PlanEndDate);
                    }
                    
                    int updatedCount = await dbContext.SaveChangesAsync(cancellationToken);
                    _logger.LogInformation("Overdue task check completed. {Count} tasks were overdue and their LastUpdatedTimestamp was updated.", overdueTasks.Count);
                }
                else
                {
                    _logger.LogInformation("No overdue tasks found.");
                }
            }
            catch (Exception ex)
            {
                // Corrected Logger call for CS1503
                _logger.LogError(ex, "Exception occurred while checking overdue tasks.");
                // throw; // Avoid throwing from background service loop if possible, allow it to continue
            }
            
            TimeSpan duration = DateTime.UtcNow - startTime;
            _logger.LogInformation("Overdue task check finished. Duration: {DurationMilliseconds} ms", duration.TotalMilliseconds);
        }
        
        /* // UpdateLeaderboardAsync and related methods are disabled.
        private async System.Threading.Tasks.Task UpdateLeaderboardAsync(AppDbContext dbContext, CancellationToken cancellationToken)
        {
            _logger.LogInformation("UpdateLeaderboardAsync called, but functionality is DISABLED.");
            await System.Threading.Tasks.Task.CompletedTask;

            // _logger.LogInformation("开始更新排行榜...");
            
            // try
            // {
            //     // 获取所有用户积分数据
            //     var userPointsList = await dbContext.UserPoints // ERROR: UserPoints DbSet does not exist
            //         .OrderByDescending(p => p.Points)
            //         .ToListAsync(cancellationToken);
                
            //     // 计算总排名
            //     await UpdateLeaderboardByType(dbContext, userPointsList, "total", p => p.Points, cancellationToken);
                
            //     // 计算日排名
            //     await UpdateLeaderboardByType(dbContext, userPointsList, "daily", p => p.DailyPoints, cancellationToken);
                
            //     // 计算周排名
            //     await UpdateLeaderboardByType(dbContext, userPointsList, "weekly", p => p.WeeklyPoints, cancellationToken);
                
            //     // 计算月排名
            //     await UpdateLeaderboardByType(dbContext, userPointsList, "monthly", p => p.MonthlyPoints, cancellationToken);
                
            //     _logger.LogInformation("排行榜更新完成");
            // }
            // catch (Exception ex)
            // {
            //     _logger.LogError(ex, "更新排行榜时发生异常");
            //     throw;
            // }
        }
        
        private async System.Threading.Tasks.Task UpdateLeaderboardByType<T>(
            AppDbContext dbContext, 
            List<UserPoints> userPointsList, // UserPoints entity not used
            string leaderboardType, 
            Func<UserPoints, T> orderSelector,
            CancellationToken cancellationToken)
        {
             _logger.LogInformation("UpdateLeaderboardByType called for {LeaderboardType}, but functionality is DISABLED.", leaderboardType);
            await System.Threading.Tasks.Task.CompletedTask;
            // // 清除今日该类型的排行榜数据
            // var today = DateTime.Today;
            // var existingEntries = await dbContext.PointLeaderboards // ERROR: PointLeaderboards DbSet does not exist
            //     .Where(l => l.LeaderboardDate == today && l.LeaderboardType == leaderboardType)
            //     .ToListAsync(cancellationToken);
            
            // dbContext.PointLeaderboards.RemoveRange(existingEntries);
            
            // // 按积分排序
            // var orderedPoints = userPointsList
            //     .OrderByDescending(orderSelector)
            //     .ThenBy(p => p.UserId)  // 相同积分按用户ID排序，保证排名稳定
            //     .ToList();
            
            // // 创建新的排行榜数据
            // int rank = 1;
            // foreach (var points in orderedPoints)
            // {
            //     // 只记录积分大于0的用户
            //     int pointValue = Convert.ToInt32(orderSelector(points));
            //     if (pointValue <= 0)
            //         continue;
                
            //     var leaderboardEntry = new PointLeaderboard // PointLeaderboard entity not used
            //     {
            //         UserId = points.UserId,
            //         Points = pointValue,
            //         Ranking = rank,
            //         LeaderboardDate = today,
            //         LeaderboardType = leaderboardType,
            //         CreatedAt = DateTime.Now
            //     };
                
            //     dbContext.PointLeaderboards.Add(leaderboardEntry);
            //     rank++;
                
            //     // 限制排行榜大小
            //     if (rank > 100)
            //         break;
            // }
        }
        */
    }
} 