// File: Application/Features/Tasks/Commands/ReplyCommentCommandHandler.cs
// Description: 回复评论命令处理器

using System;
using System.Threading;
using System.Threading.Tasks;
using MediatR;
using Microsoft.Extensions.Logging;
using ItAssetsSystem.Application.Common.Dtos;
using ItAssetsSystem.Application.Features.Tasks.Dtos;
using ItAssetsSystem.Core.Abstractions;
using ItAssetsSystem.Domain.Entities.Tasks;
using System.Text.Json;

namespace ItAssetsSystem.Application.Features.Tasks.Commands
{
    /// <summary>
    /// 回复评论命令处理器
    /// </summary>
    public class ReplyCommentCommandHandler : IRequestHandler<ReplyCommentCommand, ApiResponse<CommentDto>>
    {
        private readonly ITaskRepository _taskRepository;
        private readonly ICoreDataQueryService _coreDataQueryService;
        private readonly ILogger<ReplyCommentCommandHandler> _logger;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="taskRepository">任务仓储</param>
        /// <param name="coreDataQueryService">核心数据查询服务</param>
        /// <param name="logger">日志记录器</param>
        public ReplyCommentCommandHandler(
            ITaskRepository taskRepository,
            ICoreDataQueryService coreDataQueryService,
            ILogger<ReplyCommentCommandHandler> logger)
        {
            _taskRepository = taskRepository;
            _coreDataQueryService = coreDataQueryService;
            _logger = logger;
        }

        /// <summary>
        /// 处理回复评论命令
        /// </summary>
        /// <param name="request">回复评论命令</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>评论DTO</returns>
        public async Task<ApiResponse<CommentDto>> Handle(ReplyCommentCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("开始处理回复评论命令，任务ID: {TaskId}, 父评论ID: {ParentCommentId}, 用户ID: {UserId}",
                    request.TaskId, request.ParentCommentId, request.CurrentUserId);

                // 验证任务是否存在
                var task = await _taskRepository.GetTaskByIdAsync(request.TaskId);
                if (task == null)
                {
                    _logger.LogWarning("任务不存在，任务ID: {TaskId}", request.TaskId);
                    return ApiResponse<CommentDto>.CreateFail("任务不存在");
                }

                // 验证父评论是否存在
                var parentComment = await _taskRepository.GetCommentByIdAsync(request.ParentCommentId);
                if (parentComment == null)
                {
                    _logger.LogWarning("父评论不存在，评论ID: {CommentId}", request.ParentCommentId);
                    return ApiResponse<CommentDto>.CreateFail("父评论不存在");
                }

                // 验证父评论是否属于指定任务
                if (parentComment.TaskId != request.TaskId)
                {
                    _logger.LogWarning("父评论不属于指定任务，评论ID: {CommentId}, 任务ID: {TaskId}", request.ParentCommentId, request.TaskId);
                    return ApiResponse<CommentDto>.CreateFail("父评论不属于指定任务");
                }

                // 创建新的回复评论
                var newComment = new Comment
                {
                    TaskId = request.TaskId,
                    UserId = request.CurrentUserId,
                    ParentCommentId = request.ParentCommentId,
                    Content = request.Content,
                    IsPinned = false,
                    IsEdited = false,
                    CreationTimestamp = DateTime.Now,
                    LastUpdatedTimestamp = DateTime.Now
                };

                // 保存评论
                var savedComment = await _taskRepository.AddCommentAsync(newComment);
                if (savedComment == null)
                {
                    _logger.LogError("保存回复评论失败，任务ID: {TaskId}", request.TaskId);
                    return ApiResponse<CommentDto>.CreateFail("保存回复评论失败");
                }

                // 获取用户信息
                var user = await _coreDataQueryService.GetUserAsync(request.CurrentUserId);

                // 构建返回的评论DTO
                var commentDto = new CommentDto
                {
                    CommentId = savedComment.CommentId,
                    TaskId = savedComment.TaskId,
                    Content = savedComment.Content,
                    UserId = savedComment.UserId,
                    UserName = user?.Name ?? "未知用户",
                    UserAvatarUrl = user?.AvatarUrl ?? "",
                    CreationTimestamp = savedComment.CreationTimestamp,
                    LastUpdatedTimestamp = savedComment.LastUpdatedTimestamp,
                    ParentCommentId = savedComment.ParentCommentId,
                    IsPinned = savedComment.IsPinned,
                    IsEdited = savedComment.IsEdited,
                    MentionedUserIds = JsonSerializer.Serialize(request.MentionedUserIds)
                };

                _logger.LogInformation("成功处理回复评论命令，评论ID: {CommentId}", savedComment.CommentId);
                return ApiResponse<CommentDto>.CreateSuccess(commentDto, "回复评论成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理回复评论命令时发生错误，任务ID: {TaskId}", request.TaskId);
                return ApiResponse<CommentDto>.CreateFail($"处理回复评论时发生错误: {ex.Message}");
            }
        }
    }
}
