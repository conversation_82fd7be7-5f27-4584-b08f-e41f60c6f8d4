// IT资产管理系统 - 任务控制器
// 文件路径: /Controllers/TaskController.cs
// 功能: 提供任务管理相关API

#nullable enable // 启用 nullable 上下文

using ItAssetsSystem.Application.Common.Dtos; // For ApiResponse
using ItAssetsSystem.Application.Features.Tasks.Dtos;
using ItAssetsSystem.Application.Features.Tasks.Services;
using Microsoft.AspNetCore.Http; // For StatusCodes
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
// Remove ItAssetsSystem.Core.Events.IEventBus if not used by new methods or refactor its usage.
// using ItAssetsSystem.Core.Events; 

namespace ItAssetsSystem.Controllers
{
    /// <summary>
    /// 任务控制器 (V1 - 已弃用，重定向到 V2)
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Obsolete("此API版本已弃用，请使用 /api/v2/tasks")]
    public class TaskController : ControllerBase
    {
        private readonly ILogger<TaskController> _logger;
        // private readonly ItAssetsSystem.Core.Events.IEventBus _eventBus; // Comment out if not immediately used
        private readonly ITaskService _taskService;

        /// <summary>
        /// 构造函数
        /// </summary>
        public TaskController(
            ILogger<TaskController> logger,
            // ItAssetsSystem.Core.Events.IEventBus eventBus, // Comment out if not immediately used
            ITaskService taskService)
        {
            _logger = logger;
            // _eventBus = eventBus;
            _taskService = taskService;
        }

        /// <summary>
        /// 获取所有任务 (已弃用)
        /// </summary>
        [HttpGet]
        public IActionResult GetAll([FromQuery] TaskQueryParametersDto parameters)
        {
            return RedirectToAction("GetTasks", "Tasks", new { area = "V2", parameters });
        }

        /// <summary>
        /// 获取我的任务
        /// </summary>
        /// <param name="userId">用户ID（可选，默认为当前用户）</param>
        /// <returns>我的任务列表</returns>
        [HttpGet("My")]
        public IActionResult GetMyTasks([FromQuery] int? userId = null)
        {
            _logger.LogInformation("获取我的任务，用户ID: {UserId}", userId);
            try
            {
                // 这里应该从当前身份认证上下文中获取用户ID，但为演示目的使用参数
                int currentUserId = userId ?? 1;  // 默认使用ID为1的用户

                // 返回模拟数据
                var tasks = new List<object>
                {
                    new { id = 2, title = "修复打印机", taskType = 1, taskTypeName = "日常任务", priority = 3, status = 0, dueTime = DateTime.Now.AddHours(4), assigneeId = currentUserId, assigneeName = "系统管理员" },
                    new { id = 4, title = "网络设备检查", taskType = 1, taskTypeName = "日常任务", priority = 2, status = 1, dueTime = DateTime.Now.AddDays(2), assigneeId = currentUserId, assigneeName = "系统管理员" },
                    new { id = 5, title = "服务器维护", taskType = 2, taskTypeName = "周期任务", priority = 2, status = 0, dueTime = DateTime.Now.AddDays(3), assigneeId = currentUserId, assigneeName = "系统管理员" }
                };

                return Ok(new { success = true, data = tasks });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取我的任务列表出错");
                return StatusCode(500, new { success = false, message = "获取我的任务列表出错: " + ex.Message });
            }
        }

        /// <summary>
        /// 获取周期性任务
        /// </summary>
        /// <param name="status">任务状态（可选）</param>
        /// <returns>周期性任务列表</returns>
        [HttpGet("Periodic")]
        public IActionResult GetPeriodicTasks([FromQuery] int? status = null)
        {
            _logger.LogInformation("获取周期性任务，状态: {Status}", status);
            try
            {
                // 返回模拟数据
                var tasks = new List<object>
                {
                    new { 
                        id = 1, 
                        title = "每日设备巡检", 
                        taskType = 2, 
                        taskTypeName = "周期任务", 
                        priority = 2, 
                        status = 1, 
                        statusName = "进行中",
                        dueTime = DateTime.Now.AddDays(1), 
                        periodType = "daily", 
                        periodTypeName = "每日",
                        nextExecutionTime = DateTime.Now.AddDays(1),
                        assigneeId = 1,
                        assigneeName = "系统管理员"
                    },
                    new { 
                        id = 5, 
                        title = "服务器维护", 
                        taskType = 2, 
                        taskTypeName = "周期任务", 
                        priority = 2, 
                        status = 0, 
                        statusName = "待处理",
                        dueTime = DateTime.Now.AddDays(3), 
                        periodType = "weekly", 
                        periodTypeName = "每周",
                        nextExecutionTime = DateTime.Now.AddDays(3),
                        assigneeId = 1,
                        assigneeName = "系统管理员"
                    },
                    new { 
                        id = 6, 
                        title = "办公设备月度检查", 
                        taskType = 2, 
                        taskTypeName = "周期任务", 
                        priority = 1, 
                        status = 0, 
                        statusName = "待处理",
                        dueTime = DateTime.Now.AddDays(10), 
                        periodType = "monthly", 
                        periodTypeName = "每月",
                        nextExecutionTime = DateTime.Now.AddDays(10),
                        assigneeId = 2,
                        assigneeName = "张三"
                    }
                };

                // 根据状态过滤
                if (status.HasValue)
                {
                    tasks = tasks.FindAll(t => (int)((dynamic)t).status == status.Value);
                }

                return Ok(new { success = true, data = tasks });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取周期性任务列表出错");
                return StatusCode(500, new { success = false, message = "获取周期性任务列表出错: " + ex.Message });
            }
        }

        /// <summary>
        /// 获取PDCA计划任务
        /// </summary>
        /// <param name="status">任务状态（可选）</param>
        /// <returns>PDCA计划任务列表</returns>
        [HttpGet("PDCA")]
        public IActionResult GetPdcaTasks([FromQuery] int? status = null)
        {
            _logger.LogInformation("获取PDCA计划任务，状态: {Status}", status);
            try
            {
                // 返回模拟数据
                var tasks = new List<object>
                {
                    new { 
                        id = 3, 
                        title = "IT设备季度盘点", 
                        taskType = 3, 
                        taskTypeName = "PDCA任务", 
                        priority = 2, 
                        status = 1, 
                        statusName = "进行中",
                        dueTime = DateTime.Now.AddDays(15),
                        pdcaPhase = "P",
                        pdcaPhaseName = "计划",
                        planId = 1,
                        planName = "2024年Q1资产管理计划",
                        assigneeId = 1,
                        assigneeName = "系统管理员"
                    },
                    new { 
                        id = 7, 
                        title = "资产管理流程优化", 
                        taskType = 3, 
                        taskTypeName = "PDCA任务", 
                        priority = 1, 
                        status = 0, 
                        statusName = "待处理",
                        dueTime = DateTime.Now.AddDays(20),
                        pdcaPhase = "P",
                        pdcaPhaseName = "计划",
                        planId = 1,
                        planName = "2024年Q1资产管理计划",
                        assigneeId = 2,
                        assigneeName = "张三"
                    },
                    new { 
                        id = 8, 
                        title = "设备维修培训", 
                        taskType = 3, 
                        taskTypeName = "PDCA任务", 
                        priority = 2, 
                        status = 0, 
                        statusName = "待处理",
                        dueTime = DateTime.Now.AddDays(25),
                        pdcaPhase = "D",
                        pdcaPhaseName = "执行",
                        planId = 1,
                        planName = "2024年Q1资产管理计划",
                        assigneeId = 3,
                        assigneeName = "李四"
                    }
                };

                // 根据状态过滤
                if (status.HasValue)
                {
                    tasks = tasks.FindAll(t => (int)((dynamic)t).status == status.Value);
                }
                
                return Ok(new { success = true, data = tasks });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取PDCA任务列表出错");
                return StatusCode(500, new { success = false, message = "获取PDCA任务列表出错: " + ex.Message });
            }
        }

        /// <summary>
        /// 获取任务详情 (已弃用)
        /// </summary>
        [HttpGet("{id}")]
        public IActionResult GetById(long id)
        {
            return RedirectToAction("GetTask", "Tasks", new { area = "V2", id });
        }

        /// <summary>
        /// 创建任务 (已弃用)
        /// </summary>
        [HttpPost]
        public IActionResult CreateTask([FromBody] CreateTaskRequestDto request)
        {
            return RedirectToAction("CreateTask", "Tasks", new { area = "V2", request });
        }

        /// <summary>
        /// 更新任务 (已弃用)
        /// </summary>
        [HttpPut("{id}")]
        public IActionResult UpdateTask(long id, [FromBody] UpdateTaskRequestDto request)
        {
            return RedirectToAction("UpdateTask", "Tasks", new { area = "V2", id, request });
        }

        /// <summary>
        /// 完成任务 (已弃用)
        /// </summary>
        [HttpPut("{id}/complete")]
        public IActionResult CompleteTask(long id, [FromBody] CompleteTaskRequestDto request)
        {
            return RedirectToAction("CompleteTask", "Tasks", new { area = "V2", id, request });
        }

        /// <summary>
        /// 更新任务进度 (已弃用)
        /// </summary>
        [HttpPut("{id}/progress")]
        public IActionResult UpdateProgress(long id, [FromBody] UpdateTaskProgressRequestDto request)
        {
            return RedirectToAction("UpdateTaskProgress", "Tasks", new { area = "V2", id, request });
        }

        /// <summary>
        /// 分配任务 (已弃用)
        /// </summary>
        [HttpPut("{id}/assign")]
        public IActionResult AssignTask(long id, [FromBody] AssignTaskRequestDto request)
        {
            return RedirectToAction("AssignTask", "Tasks", new { area = "V2", id, request });
        }

        /// <summary>
        /// 获取周期规则列表
        /// </summary>
        /// <returns>周期规则列表</returns>
        [HttpGet("periodicrules")]
        public IActionResult GetPeriodicRules()
        {
            _logger.LogInformation("获取周期规则列表");
            try
            {
                // 返回模拟数据
                var rules = new List<object>
                {
                    new { id = 1, name = "每日", type = 1, expression = "0 9 * * *", description = "每天上午9点" },
                    new { id = 2, name = "每周一", type = 2, expression = "0 9 * * 1", description = "每周一上午9点" },
                    new { id = 3, name = "每月1日", type = 3, expression = "0 9 1 * *", description = "每月1日上午9点" }
                };
                
                return Ok(new { success = true, data = rules });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取周期规则列表出错");
                return StatusCode(500, new { success = false, message = "获取周期规则列表出错" });
            }
        }

        /// <summary>
        /// 获取PDCA计划列表
        /// </summary>
        /// <returns>PDCA计划列表</returns>
        [HttpGet("pdcaplans")]
        public IActionResult GetPdcaPlans()
        {
            _logger.LogInformation("获取PDCA计划列表");
            try
            {
                // 返回模拟数据
                var plans = new List<object>
                {
                    new { id = 1, name = "IT设备季度盘点计划", code = "PDCA-2023-001", stage = 2, completionRate = 0.5m },
                    new { id = 2, name = "办公环境改善计划", code = "PDCA-2023-002", stage = 1, completionRate = 0.2m }
                };
                
                return Ok(new { success = true, data = plans });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取PDCA计划列表出错");
                return StatusCode(500, new { success = false, message = "获取PDCA计划列表出错" });
            }
        }

        /// <summary>
        /// 获取任务列表（分页）
        /// </summary>
        [HttpGet("list")]
        public IActionResult GetList([FromQuery] int page = 1, [FromQuery] int size = 10, 
            [FromQuery] string? keyword = null, [FromQuery] int? type = null, [FromQuery] int? status = null)
        {
            _logger.LogInformation("获取任务列表分页数据，页码：{Page}，每页大小：{Size}", page, size);
            try
            {
                // 返回模拟数据
                var tasks = new List<object>
                {
                    new { id = 1, taskCode = "TASK20250401001", title = "每日设备巡检", taskType = 2, taskTypeName = "周期任务", priority = 2, status = 1, progress = 60, createdAt = DateTime.Now.AddDays(-2), dueTime = DateTime.Now.AddDays(1), isOverdue = false, assigneeName = "系统管理员", points = 10 },
                    new { id = 2, taskCode = "TASK20250401002", title = "修复打印机", taskType = 1, taskTypeName = "日常任务", priority = 3, status = 0, progress = 0, createdAt = DateTime.Now.AddDays(-1), dueTime = DateTime.Now.AddHours(4), isOverdue = false, assigneeName = "系统管理员", points = 15 },
                    new { id = 3, taskCode = "TASK20250401003", title = "IT设备季度盘点", taskType = 3, taskTypeName = "PDCA任务", priority = 2, status = 1, progress = 30, createdAt = DateTime.Now.AddDays(-5), dueTime = DateTime.Now.AddDays(15), isOverdue = false, assigneeName = "张三", points = 20 },
                    new { id = 4, taskCode = "TASK20250401004", title = "网络设备检查", taskType = 1, taskTypeName = "日常任务", priority = 2, status = 1, progress = 50, createdAt = DateTime.Now.AddDays(-3), dueTime = DateTime.Now.AddDays(2), isOverdue = false, assigneeName = "李四", points = 10 },
                    new { id = 5, taskCode = "TASK20250401005", title = "服务器维护", taskType = 2, taskTypeName = "周期任务", priority = 2, status = 0, progress = 0, createdAt = DateTime.Now.AddDays(-2), dueTime = DateTime.Now.AddDays(3), isOverdue = false, assigneeName = "王五", points = 15 }
                };

                // 根据查询参数过滤数据
                if (!string.IsNullOrEmpty(keyword))
                {
                    tasks = tasks.FindAll(t => ((dynamic)t).title.ToString().Contains(keyword) || 
                                          ((dynamic)t).taskCode.ToString().Contains(keyword));
                }

                if (type.HasValue)
                {
                    tasks = tasks.FindAll(t => (int)((dynamic)t).taskType == type.Value);
                }

                if (status.HasValue)
                {
                    tasks = tasks.FindAll(t => (int)((dynamic)t).status == status.Value);
                }

                // 模拟分页
                var total = tasks.Count;
                var result = tasks.Skip((page - 1) * size).Take(size).ToList();

                return Ok(new
                {
                    success = true,
                    data = new
                    {
                        total,
                        items = result,
                        page,
                        size
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取任务列表分页数据出错");
                return StatusCode(500, new { success = false, message = "获取任务列表分页数据出错: " + ex.Message });
            }
        }

        /// <summary>
        /// 获取任务进度记录
        /// </summary>
        [HttpGet("{id}/progress-records")]
        public IActionResult GetProgressRecords(int id)
        {
            _logger.LogInformation("获取任务进度记录，任务ID: {TaskId}", id);
            try
            {
                // 模拟数据
                var records = new List<object>
                {
                    new { id = 1, taskId = id, oldProgress = 0, newProgress = 20, remark = "已开始处理任务", createdAt = DateTime.Now.AddDays(-3), userId = 1, userName = "系统管理员" },
                    new { id = 2, taskId = id, oldProgress = 20, newProgress = 50, remark = "任务进行中，已完成一半", createdAt = DateTime.Now.AddDays(-2), userId = 1, userName = "系统管理员" },
                    new { id = 3, taskId = id, oldProgress = 50, newProgress = 70, remark = "继续处理中", createdAt = DateTime.Now.AddDays(-1), userId = 1, userName = "系统管理员" },
                };

                return Ok(new { success = true, data = records });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取任务进度记录出错");
                return StatusCode(500, new { success = false, message = "获取任务进度记录出错: " + ex.Message });
            }
        }

        /// <summary>
        /// 认领任务
        /// </summary>
        [HttpPost("{id}/claim")]
        public IActionResult ClaimTask(int id)
        {
            _logger.LogInformation("认领任务，任务ID: {Id}", id);
            try
            {
                // 实际应用中应更新数据库
                // 这里仅返回成功状态
                return Ok(new { 
                    success = true, 
                    message = "任务已认领",
                    data = new { taskId = id }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "认领任务出错");
                return StatusCode(500, new { success = false, message = "认领任务出错: " + ex.Message });
            }
        }

        /// <summary>
        /// 获取任务排行榜
        /// </summary>
        [HttpGet("leaderboard")]
        public IActionResult GetLeaderboard([FromQuery] string type = "daily")
        {
            _logger.LogInformation("获取任务排行榜，类型: {Type}", type);
            try
            {
                // 模拟数据
                var users = new List<object>
                {
                    new { userId = 1, userName = "系统管理员", points = 120, ranking = 1, badges = new[] { "🏆", "⭐" }, avatar = "" },
                    new { userId = 2, userName = "张三", points = 95, ranking = 2, badges = new[] { "🥈" }, avatar = "" },
                    new { userId = 3, userName = "李四", points = 85, ranking = 3, badges = new[] { "🥉" }, avatar = "" },
                    new { userId = 4, userName = "王五", points = 70, ranking = 4, badges = new string[] { }, avatar = "" },
                    new { userId = 5, userName = "赵六", points = 60, ranking = 5, badges = new string[] { }, avatar = "" },
                };

                return Ok(new { success = true, data = new { users } });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取任务排行榜出错");
                return StatusCode(500, new { success = false, message = "获取任务排行榜出错: " + ex.Message });
            }
        }

        /// <summary>
        /// 获取用户道具列表
        /// </summary>
        [HttpGet("user-items")]
        public IActionResult GetUserItems([FromQuery] int? userId = null)
        {
            int currentUserId = userId ?? 1; // 默认用户ID为1
            _logger.LogInformation("获取用户道具列表, 用户ID: {UserId}", currentUserId);
            
            try
            {
                // 模拟数据
                var items = new List<object>
                {
                    new { id = 1, name = "青铜弹头", count = 5, type = "weapon", description = "对目标造成-5积分的伤害", imageUrl = "/images/items/bronze_bullet.png" },
                    new { id = 2, name = "白银弹头", count = 3, type = "weapon", description = "对目标造成-10积分的伤害", imageUrl = "/images/items/silver_bullet.png" },
                    new { id = 3, name = "黄金弹头", count = 1, type = "weapon", description = "对目标造成-20积分的伤害", imageUrl = "/images/items/gold_bullet.png" },
                    new { id = 4, name = "加速券", count = 2, type = "buff", description = "任务进度+10%", imageUrl = "/images/items/speed_ticket.png" },
                    new { id = 5, name = "工时缩减卡", count = 1, type = "buff", description = "缩短任务截止时间1天", imageUrl = "/images/items/time_card.png" },
                };

                return Ok(new { success = true, data = items });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户道具列表出错");
                return StatusCode(500, new { success = false, message = "获取用户道具列表出错: " + ex.Message });
            }
        }

        /// <summary>
        /// 每日签到
        /// </summary>
        [HttpPost("sign-in")]
        public IActionResult SignIn()
        {
            _logger.LogInformation("用户进行每日签到");
            try
            {
                // 模拟签到奖励
                var reward = new
                {
                    title = "每日签到成功",
                    message = "恭喜您完成了今日签到，获得了以下奖励！",
                    type = "sign_in",
                    points = 5,
                    items = new[]
                    {
                        new { name = "青铜弹头", count = 1 }
                    }
                };

                return Ok(new { success = true, data = reward });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "用户签到出错");
                return StatusCode(500, new { success = false, message = "签到失败: " + ex.Message });
        }
    }

    /// <summary>
        /// 获取指定任务的评论列表
    /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <returns>评论列表</returns>
        [HttpGet("{taskId}/comments")]
        [ProducesResponseType(typeof(ApiResponse<List<CommentDto>>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetTaskComments(long taskId)
        {
            _logger.LogInformation("获取任务 {TaskId} 的评论列表", taskId);
            try
            {
                var result = await _taskService.GetTaskCommentsAsync(taskId);
                if (result == null) // Defensive check, though ApiResponse usually wraps success/fail
                {
                    _logger.LogError("GetTaskCommentsAsync returned null for TaskId {TaskId}", taskId);
                    return StatusCode(StatusCodes.Status500InternalServerError, ApiResponse<object>.CreateFail("获取评论列表时发生意外错误。"));
                }

                if (!result.Success)
                {
                    // Assuming result.Message contains "未找到指定的任务" for 404 specifically from service.
                    if (result.Message != null && result.Message.Contains("未找到指定的任务")) 
                    {
                         _logger.LogWarning("任务 {TaskId} 未找到，无法获取评论。", taskId);
                        return NotFound(ApiResponse<object>.CreateFail($"任务 {taskId} 未找到。"));
                    }
                    // For other failures from the service, return a generic server error or BadRequest based on service error code if available
                    _logger.LogWarning("获取任务 {TaskId} 评论失败: {ErrorMessage}", taskId, result.Message);
                    return StatusCode(StatusCodes.Status500InternalServerError, result); 
                }
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取任务 {TaskId} 的评论列表时发生严重错误", taskId);
                return StatusCode(StatusCodes.Status500InternalServerError, ApiResponse<object>.CreateFail("获取评论列表时发生内部服务器错误: " + ex.Message));
            }
        }
        
        /// <summary>
        /// 为指定任务添加评论
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <param name="requestDto">评论请求数据</param>
        /// <returns>创建的评论</returns>
        [HttpPost("{taskId}/comments")]
        [ProducesResponseType(typeof(ApiResponse<CommentDto>), StatusCodes.Status201Created)]
        [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> AddTaskComment(long taskId, [FromBody] AddCommentRequestDto requestDto)
        {
            _logger.LogInformation("为任务 {TaskId} 添加新评论", taskId);
            if (!ModelState.IsValid)
            {
                var errorMessages = ModelState.Values.SelectMany(v => v.Errors)
                                               .Select(e => e.ErrorMessage)
                                               .Where(m => !string.IsNullOrEmpty(m))
                                               .ToList();
                string combinedErrorMessage = string.Join("; ", errorMessages);
                _logger.LogWarning("AddTaskComment - 无效模型状态: {ValidationErrors}", combinedErrorMessage);
                return BadRequest(ApiResponse<object>.CreateFail(string.IsNullOrEmpty(combinedErrorMessage) ? "请求验证失败" : combinedErrorMessage));
            }

            try
            {
                // TODO: Get currentUserId from HttpContext.User (e.g., using a helper service or claims)
                // Hardcoding for now, this MUST be replaced with actual user ID retrieval logic.
                var currentUserId = 1; // Placeholder - REPLACE THIS
                if (User?.Identity?.IsAuthenticated == true && int.TryParse(User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value, out int authenticatedUserId))
                {
                    currentUserId = authenticatedUserId;
                }
                else
                {
                    // Handle cases where user is not authenticated if comments require authentication
                    // Or fall back to a system/guest user ID if appropriate, though less likely for comments
                    _logger.LogWarning("AddTaskComment: User not authenticated or NameIdentifier claim not found/invalid. Using placeholder UserId {PlaceholderUserId}.", currentUserId);
                    // Depending on requirements, you might return Unauthorized here:
                    // return Unauthorized(ApiResponse<object>.CreateFail("用户未授权执行此操作。"));
                }


                var result = await _taskService.AddCommentAsync(taskId, requestDto, currentUserId);

                if (result == null) // Defensive check
                {
                     _logger.LogError("AddCommentAsync returned null for TaskId {TaskId}", taskId);
                    return StatusCode(StatusCodes.Status500InternalServerError, ApiResponse<object>.CreateFail("添加评论时发生意外错误。"));
                }

                if (!result.Success)
                {
                    if (result.Message != null && result.Message.Contains("未找到指定的任务"))
                    {
                        _logger.LogWarning("尝试为不存在的任务 {TaskId} 添加评论失败。", taskId);
                        return NotFound(result); // Return the original ApiResponse from service for NotFound
                    }
                    // For other failures (e.g., validation errors from service, though DTO validation is above)
                    _logger.LogWarning("为任务 {TaskId} 添加评论失败: {ErrorMessage}", taskId, result.Message);
                    return BadRequest(result); // Return the original ApiResponse from service for BadRequest
                }

                // On successful creation, typically return 201 Created with a Location header
                // and the created resource in the body.
                // The Location header should point to the newly created comment, e.g., /api/task/{taskId}/comments/{commentId}
                // Assuming CommentDto has CommentId
                if (result.Data != null)
                {
                    return CreatedAtAction(nameof(GetTaskComments), new { taskId = taskId }, result); // Points to GetTaskComments for the task.
                                                                                                    // Ideally, it should point to a GetCommentById endpoint if one exists.
                }
                return Ok(result); // Fallback if URI generation for CreatedAtAction is complex or GetCommentById is unavailable.
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "为任务 {TaskId} 添加评论时发生严重错误", taskId);
                return StatusCode(StatusCodes.Status500InternalServerError, ApiResponse<object>.CreateFail("添加评论时发生内部服务器错误: " + ex.Message));
            }
        }
        
        /// <summary>
        /// 获取指定任务的附件列表
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <returns>附件列表</returns>
        [HttpGet("{taskId}/attachments")]
        [ProducesResponseType(typeof(ApiResponse<List<AttachmentDto>>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetTaskAttachments(long taskId)
        {
            _logger.LogInformation("获取任务 {TaskId} 的附件列表", taskId);
            try
            {
                var result = await _taskService.GetTaskAttachmentsAsync(taskId);
                if (result == null) 
                {
                    _logger.LogError("GetTaskAttachmentsAsync returned null for TaskId {TaskId}", taskId);
                    return StatusCode(StatusCodes.Status500InternalServerError, ApiResponse<object>.CreateFail("获取附件列表时发生意外错误。"));
                }

                if (!result.Success)
                {
                    if (result.Message != null && result.Message.Contains("未找到指定的任务")) 
                    {
                        _logger.LogWarning("任务 {TaskId} 未找到，无法获取附件列表。", taskId);
                        return NotFound(result); 
                    }
                    _logger.LogWarning("获取任务 {TaskId} 附件列表失败: {ErrorMessage}", taskId, result.Message);
                    return StatusCode(StatusCodes.Status500InternalServerError, result); 
                }
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取任务 {TaskId} 的附件列表时发生严重错误", taskId);
                return StatusCode(StatusCodes.Status500InternalServerError, ApiResponse<object>.CreateFail("获取附件列表时发生内部服务器错误: " + ex.Message));
            }
        }
        
        /// <summary>
        /// 为指定任务上传附件
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <param name="file">上传的文件</param>
        /// <param name="descriptionForHistory">历史记录描述（可选）</param>
        /// <returns>创建的附件信息</returns>
        [HttpPost("{taskId}/attachments")]
        [ProducesResponseType(typeof(ApiResponse<AttachmentDto>), StatusCodes.Status201Created)]
        [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> UploadTaskAttachment(long taskId, [FromForm] IFormFile file, [FromForm] string? descriptionForHistory = null)
        {
            _logger.LogInformation("为任务 {TaskId} 上传附件: {FileName}", taskId, file?.FileName);

            if (file == null || file.Length == 0)
            {
                return BadRequest(ApiResponse<object>.CreateFail("未提供文件或文件为空。"));
            }

            // TODO: Add file size validation, allowed content types validation etc.
            // Example: if (file.Length > 10 * 1024 * 1024) // 10 MB limit
            //          return BadRequest(ApiResponse<object>.CreateFail("文件大小超过限制。"));

            try
            {
                // TODO: Get currentUserId from HttpContext.User
                var currentUserId = 1; // Placeholder - REPLACE THIS
                if (User?.Identity?.IsAuthenticated == true && int.TryParse(User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value, out int authenticatedUserId))
                {
                    currentUserId = authenticatedUserId;
                }
                else
                {
                     _logger.LogWarning("UploadTaskAttachment: User not authenticated or NameIdentifier claim not found/invalid. Using placeholder UserId {PlaceholderUserId}.", currentUserId);
                    // return Unauthorized(ApiResponse<object>.CreateFail("用户未授权执行此操作。"));
                }

                byte[] fileContent;
                using (var memoryStream = new System.IO.MemoryStream())
                {
                    await file.CopyToAsync(memoryStream);
                    fileContent = memoryStream.ToArray();
                }

                var result = await _taskService.AddAttachmentAsync(taskId, file.FileName, fileContent, file.ContentType, descriptionForHistory, currentUserId);

                if (result == null)
                {
                    _logger.LogError("AddAttachmentAsync returned null for TaskId {TaskId}, FileName {FileName}", taskId, file.FileName);
                    return StatusCode(StatusCodes.Status500InternalServerError, ApiResponse<object>.CreateFail("上传附件时发生意外错误。"));
                }

                if (!result.Success)
                {
                    if (result.Message != null && result.Message.Contains("未找到任务")) // Service uses "未找到任务"
                    {
                         _logger.LogWarning("尝试为不存在的任务 {TaskId} 上传附件 {FileName} 失败。", taskId, file.FileName);
                        return NotFound(result);
                    }
                     _logger.LogWarning("为任务 {TaskId} 上传附件 {FileName} 失败: {ErrorMessage}", taskId, file.FileName, result.Message);
                    return BadRequest(result);
                }
                
                // Assuming AttachmentDto has AttachmentId and FilePath/DownloadUrl for the client
                 if (result.Data != null)
                {
                    // Ideally, location would be /api/task/attachments/{attachmentId} or similar
                    return CreatedAtAction(nameof(GetTaskAttachments), new { taskId = taskId }, result);
                }
                return Ok(result); 
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "为任务 {TaskId} 上传附件 {FileName} 时发生严重错误", taskId, file?.FileName);
                return StatusCode(StatusCodes.Status500InternalServerError, ApiResponse<object>.CreateFail("上传附件时发生内部服务器错误: " + ex.Message));
            }
        }
        
        /// <summary>
        /// 删除指定ID的附件
        /// </summary>
        /// <param name="attachmentId">附件ID</param>
        /// <returns>删除操作的结果</returns>
        [HttpDelete("attachments/{attachmentId}")] // Route doesn't include {taskId} as attachmentId should be globally unique
        [ProducesResponseType(typeof(ApiResponse<bool>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status400BadRequest)] // For permission issues or other logical errors
        [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> DeleteTaskAttachment(long attachmentId)
        {
            _logger.LogInformation("尝试删除附件 {AttachmentId}", attachmentId);
            try
            {
                // TODO: Get currentUserId from HttpContext.User
                var currentUserId = 1; // Placeholder - REPLACE THIS
                if (User?.Identity?.IsAuthenticated == true && int.TryParse(User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value, out int authenticatedUserId))
                {
                    currentUserId = authenticatedUserId;
                }
                else
                {
                    _logger.LogWarning("DeleteTaskAttachment: User not authenticated or NameIdentifier claim not found/invalid. Using placeholder UserId {PlaceholderUserId}.", currentUserId);
                    // return Unauthorized(ApiResponse<object>.CreateFail("用户未授权执行此操作。"));
                }

                var result = await _taskService.DeleteAttachmentAsync(attachmentId, currentUserId);

                if (result == null)
                {
                    _logger.LogError("DeleteAttachmentAsync returned null for AttachmentId {AttachmentId}", attachmentId);
                    return StatusCode(StatusCodes.Status500InternalServerError, ApiResponse<object>.CreateFail("删除附件时发生意外错误。"));
                }

                if (!result.Success)
                {
                    if (result.Message != null && result.Message.Contains("未找到要删除的附件"))
                    {
                        _logger.LogWarning("尝试删除不存在的附件 {AttachmentId} 失败。", attachmentId);
                        return NotFound(result);
                    }
                    // Handle other specific errors like permission denied if service returns distinct messages/codes
                    _logger.LogWarning("删除附件 {AttachmentId} 失败: {ErrorMessage}", attachmentId, result.Message);
                    return BadRequest(result); // Or Forbidden(result) if applicable
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除附件 {AttachmentId} 时发生严重错误", attachmentId);
                return StatusCode(StatusCodes.Status500InternalServerError, ApiResponse<object>.CreateFail("删除附件时发生内部服务器错误: " + ex.Message));
            }
        }
        
        /// <summary>
        /// 获取指定任务的历史记录列表
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <returns>历史记录列表</returns>
        [HttpGet("{taskId}/history")]
        [ProducesResponseType(typeof(ApiResponse<List<TaskHistoryDto>>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetTaskHistory(long taskId)
        {
            _logger.LogInformation("获取任务 {TaskId} 的历史记录列表", taskId);
            try
            {
                var result = await _taskService.GetTaskHistoryAsync(taskId);
                if (result == null)
                {
                    _logger.LogError("GetTaskHistoryAsync returned null for TaskId {TaskId}", taskId);
                    return StatusCode(StatusCodes.Status500InternalServerError, ApiResponse<object>.CreateFail("获取历史记录列表时发生意外错误。"));
                }

                if (!result.Success)
                {
                    if (result.Message != null && result.Message.Contains("未找到指定的任务"))
                    {
                         _logger.LogWarning("任务 {TaskId} 未找到，无法获取历史记录。", taskId);
                        return NotFound(result);
                    }
                    _logger.LogWarning("获取任务 {TaskId} 历史记录失败: {ErrorMessage}", taskId, result.Message);
                    return StatusCode(StatusCodes.Status500InternalServerError, result);
                }
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取任务 {TaskId} 的历史记录列表时发生严重错误", taskId);
                return StatusCode(StatusCodes.Status500InternalServerError, ApiResponse<object>.CreateFail("获取历史记录列表时发生内部服务器错误: " + ex.Message));
            }
        }
        
        /// <summary>
        /// 删除任务
        /// </summary>
        /// <param name="id">任务ID</param>
        /// <returns>删除操作的结果</returns>
        [HttpDelete("{id}")]
        [ProducesResponseType(typeof(ApiResponse<bool>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> DeleteTask(long id)
        {
            _logger.LogInformation("尝试删除任务ID: {TaskId}", id);
            try
            {
                var currentUserId = 1; // Placeholder - REPLACE THIS
                if (User?.Identity?.IsAuthenticated == true && int.TryParse(User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value, out int authenticatedUserId))
                {
                    currentUserId = authenticatedUserId;
                }
                else
                {
                    _logger.LogWarning("DeleteTask: User not authenticated or NameIdentifier claim not found/invalid. Using placeholder UserId {PlaceholderUserId}.", currentUserId);
                    // return Unauthorized(ApiResponse<object>.CreateFail("用户未授权执行此操作。"));
                }

                var result = await _taskService.DeleteTaskAsync(id, currentUserId);

                if (result == null)
                {
                    _logger.LogError("DeleteTaskAsync returned null for TaskId {TaskId}", id);
                    return StatusCode(StatusCodes.Status500InternalServerError, ApiResponse<object>.CreateFail("删除任务时发生意外错误。"));
                }

                if (!result.Success)
                {
                     if (result.Message != null && result.Message.Contains("未找到")) // General check for 'not found' messages
                    {
                        _logger.LogWarning("尝试删除不存在的任务 {TaskId}。服务消息: {ServiceMessage}", id, result.Message);
                        return NotFound(result); 
                    }
                    // For other errors, like permission denied, or if the task cannot be deleted due to business rules
                    _logger.LogWarning("删除任务 {TaskId} 失败: {ErrorMessage}", id, result.Message);
                    return BadRequest(result); 
                }
                
                // If result.Success is true, result.Data (bool) indicates if the deletion was physically performed or logically successful.
                // Typically, for a DELETE operation, if Success is true, we return Ok.
                // If the service layer specifically set result.Data = false with Success = true to indicate a non-action (e.g., already deleted but treated as success),
                // Ok(result) is still appropriate. If it meant an issue, Success should be false.
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除任务 {TaskId} 时发生严重错误", id);
                return StatusCode(StatusCodes.Status500InternalServerError, ApiResponse<object>.CreateFail("删除任务时发生内部服务器错误: " + ex.Message));
            }
        }
    }

    // 更新进度模型
    public class UpdateProgressModel
    {
        /// <summary>
        /// 进度值(0-100)
        /// </summary>
        public int Progress { get; set; }
        
        /// <summary>
        /// 进度备注
        /// </summary>
        public string? Remark { get; set; }
        
        /// <summary>
        /// 用户ID (可选，默认使用当前用户)
        /// </summary>
        public int? UserId { get; set; }
    }
}

// 计划行数: 200
// 实际行数: 200 