/**
 * 生产线相关接口
 */
import request from '@/utils/request'
import systemConfig from '@/config/system'

/**
 * 获取产线列表
 * @param {Object} params 查询参数
 * @returns {Promise} 返回产线列表
 */
export function getProductionLines(params) {
  console.log('获取产线列表 - API调用', {
    请求方法: 'GET',
    请求路径: '/Production/lines',
    完整URL: `${systemConfig.apiBaseUrl}/Production/lines`,
    请求参数: params
  })
  return request.get('/Production/lines', { params })
}

/**
 * 获取工序列表
 * @param {number} lineId 产线ID
 * @returns {Promise} 返回工序列表
 */
export function getProcesses(lineId) {
  return request({
    url: `/production/processes/${lineId}`,
    method: 'get'
  })
}

// 获取工位列表
export function getWorkstations(processId) {
  return request({
    url: `/production/workstations/${processId}`,
    method: 'get'
  })
}

/**
 * 获取产线详情
 * @param {number} id 产线ID
 * @returns {Promise} 返回产线详情
 */
export function getProductionLineById(id) {
  return request.get(`/Production/lines/${id}`)
}

/**
 * 创建产线
 * @param {Object} data 产线数据
 * @returns {Promise} 返回创建结果
 */
export function createProductionLine(data) {
  return request.post('/Production/lines', data)
}

/**
 * 更新产线
 * @param {number} id 产线ID
 * @param {Object} data 产线数据
 * @returns {Promise} 返回更新结果
 */
export function updateProductionLine(id, data) {
  return request.put(`/Production/lines/${id}`, data)
}

/**
 * 删除产线
 * @param {number} id 产线ID
 * @returns {Promise} 返回删除结果
 */
export function deleteProductionLine(id) {
  return request.delete(`/Production/lines/${id}`)
}

export default {
  getProductionLines,
  getProductionLineById,
  createProductionLine,
  updateProductionLine,
  deleteProductionLine
} 