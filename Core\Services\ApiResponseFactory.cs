// File: Core/Services/ApiResponseFactory.cs
// Description: API响应工厂类，提供创建标准响应的静态方法

using System;
using System.Collections.Generic;
using System.Linq;
using ItAssetsSystem.Models;

namespace ItAssetsSystem.Core.Services
{
    /// <summary>
    /// API响应工厂类，提供创建标准响应的静态方法
    /// </summary>
    public static class ApiResponseFactory
    {
        /// <summary>
        /// 创建成功响应
        /// </summary>
        /// <typeparam name="T">数据类型</typeparam>
        /// <param name="data">响应数据</param>
        /// <param name="message">响应消息</param>
        /// <returns>成功的API响应</returns>
        public static ApiResponse<T> CreateSuccess<T>(T data, string message = "操作成功")
        {
            return new ApiResponse<T>
            {
                Success = true,
                Data = data,
                Message = message
            };
        }

        /// <summary>
        /// 创建失败响应
        /// </summary>
        /// <typeparam name="T">数据类型</typeparam>
        /// <param name="message">错误消息</param>
        /// <param name="error">详细错误信息</param>
        /// <returns>失败的API响应</returns>
        public static ApiResponse<T> CreateFail<T>(string message, string error = null)
        {
            return new ApiResponse<T>
            {
                Success = false,
                Message = message,
                Error = error ?? message
            };
        }

        /// <summary>
        /// 创建失败响应（接受多个错误消息）
        /// </summary>
        /// <typeparam name="T">数据类型</typeparam>
        /// <param name="errors">错误消息列表</param>
        /// <returns>失败的API响应</returns>
        public static ApiResponse<T> CreateFail<T>(IEnumerable<string> errors)
        {
            var errorMessages = errors?.ToList() ?? new List<string>();
            var message = errorMessages.Any() ? string.Join("; ", errorMessages) : "操作失败";
            
            return new ApiResponse<T>
            {
                Success = false,
                Message = message,
                Error = message
            };
        }

        /// <summary>
        /// 创建分页响应
        /// </summary>
        /// <typeparam name="T">数据类型</typeparam>
        /// <param name="items">分页数据</param>
        /// <param name="totalCount">总记录数</param>
        /// <param name="pageIndex">当前页码</param>
        /// <param name="pageSize">每页大小</param>
        /// <param name="message">响应消息</param>
        /// <returns>带分页信息的API响应</returns>
        public static ApiResponse<List<T>> CreatePagedSuccess<T>(List<T> items, int totalCount, int pageIndex, int pageSize, string message = "获取数据成功")
        {
            return new ApiResponse<List<T>>
            {
                Success = true,
                Data = items,
                Message = message,
                Pagination = new PaginationInfo
                {
                    PageIndex = pageIndex,
                    PageSize = pageSize,
                    TotalCount = totalCount,
                    TotalPages = (int)Math.Ceiling(totalCount / (double)pageSize)
                }
            };
        }
    }
} 