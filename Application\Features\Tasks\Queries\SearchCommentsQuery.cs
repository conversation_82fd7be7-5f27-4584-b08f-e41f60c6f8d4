// File: Application/Features/Tasks/Queries/SearchCommentsQuery.cs
// Description: 搜索评论查询

using System;
using System.Collections.Generic;
using MediatR;
using ItAssetsSystem.Application.Common.Dtos;
using ItAssetsSystem.Application.Features.Tasks.Dtos;
using PagedResultType = ItAssetsSystem.Core.Interfaces.Services.PagedResult<ItAssetsSystem.Application.Features.Tasks.Dtos.CommentDto>;

namespace ItAssetsSystem.Application.Features.Tasks.Queries
{
    /// <summary>
    /// 搜索评论查询
    /// </summary>
    public class SearchCommentsQuery : IRequest<ApiResponse<PagedResultType>>
    {
        /// <summary>
        /// 任务ID（可选，如果指定则只搜索该任务的评论）
        /// </summary>
        public long? TaskId { get; set; }

        /// <summary>
        /// 搜索关键词
        /// </summary>
        public string SearchKeyword { get; set; } = string.Empty;

        /// <summary>
        /// 用户ID（可选，如果指定则只搜索该用户的评论）
        /// </summary>
        public int? UserId { get; set; }

        /// <summary>
        /// 开始日期（可选）
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// 结束日期（可选）
        /// </summary>
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// 是否只搜索置顶评论
        /// </summary>
        public bool? IsPinned { get; set; }

        /// <summary>
        /// 页码（从1开始）
        /// </summary>
        public int PageNumber { get; set; } = 1;

        /// <summary>
        /// 每页大小
        /// </summary>
        public int PageSize { get; set; } = 20;

        /// <summary>
        /// 排序字段
        /// </summary>
        public string SortBy { get; set; } = "CreationTimestamp";

        /// <summary>
        /// 排序方向（asc或desc）
        /// </summary>
        public string SortDirection { get; set; } = "desc";

        /// <summary>
        /// 当前用户ID
        /// </summary>
        public int CurrentUserId { get; set; }

        /// <summary>
        /// 是否只返回顶层评论（非回复）
        /// </summary>
        public bool? TopLevelOnly { get; set; }

        /// <summary>
        /// 是否只返回已编辑的评论
        /// </summary>
        public bool? IsEdited { get; set; }

        /// <summary>
        /// 是否包含已删除的评论
        /// </summary>
        public bool IncludeDeleted { get; set; } = false;

        /// <summary>
        /// 评论内容最小长度
        /// </summary>
        public int? MinContentLength { get; set; }

        /// <summary>
        /// 评论内容最大长度
        /// </summary>
        public int? MaxContentLength { get; set; }

        /// <summary>
        /// 是否有提及用户
        /// </summary>
        public bool? HasMentions { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public SearchCommentsQuery()
        {
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="searchKeyword">搜索关键词</param>
        /// <param name="currentUserId">当前用户ID</param>
        public SearchCommentsQuery(string searchKeyword, int currentUserId)
        {
            SearchKeyword = searchKeyword;
            CurrentUserId = currentUserId;
        }
    }
}
