// File: Application/Features/Tasks/Dtos/TaskHistoryDto.cs
// Description: 任务历史记录数据传输对象
using System;

namespace ItAssetsSystem.Application.Features.Tasks.Dtos
{
    /// <summary>
    /// 任务历史记录数据传输对象
    /// </summary>
    public class TaskHistoryDto
    {
        /// <summary>
        /// 历史记录ID
        /// </summary>
        public long TaskHistoryId { get; set; }

        /// <summary>
        /// 任务ID
        /// </summary>
        public long TaskId { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        public int? UserId { get; set; }

        /// <summary>
        /// 用户名称
        /// </summary>
        public string UserName { get; set; } = string.Empty;

        /// <summary>
        /// 用户头像URL
        /// </summary>
        public string UserAvatarUrl { get; set; } = string.Empty;

        /// <summary>
        /// 操作时间
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// 操作类型
        /// </summary>
        public string ActionType { get; set; } = string.Empty;

        /// <summary>
        /// 字段名称
        /// </summary>
        public string FieldName { get; set; } = string.Empty;

        /// <summary>
        /// 旧值
        /// </summary>
        public string OldValue { get; set; } = string.Empty;

        /// <summary>
        /// 新值
        /// </summary>
        public string NewValue { get; set; } = string.Empty;

        /// <summary>
        /// 描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 评论ID
        /// </summary>
        public long? CommentId { get; set; }

        /// <summary>
        /// 附件ID
        /// </summary>
        public long? AttachmentId { get; set; }

        /// <summary>
        /// IP地址
        /// </summary>
        public string IpAddress { get; set; } = string.Empty;

        /// <summary>
        /// 用户代理
        /// </summary>
        public string UserAgent { get; set; } = string.Empty;

        /// <summary>
        /// 格式化的操作类型
        /// </summary>
        public string FormattedActionType => FormatActionType(ActionType);

        /// <summary>
        /// 格式化操作类型
        /// </summary>
        /// <param name="actionType">操作类型</param>
        /// <returns>格式化的操作类型</returns>
        private string FormatActionType(string actionType)
        {
            if (string.IsNullOrEmpty(actionType))
            {
                return string.Empty;
            }

            switch (actionType)
            {
                case "CreateTask":
                    return "创建任务";
                case "UpdateTask":
                    return "更新任务";
                case "DeleteTask":
                    return "删除任务";
                case "StatusChanged":
                    return "状态变更";
                case "ProgressChanged":
                    return "进度变更";
                case "AssigneeChanged":
                    return "分配任务";
                case "Complete":
                    return "完成任务";
                case "PDCAStageChanged":
                    return "PDCA阶段变更";
                case "AddComment":
                    return "添加评论";
                case "UpdateComment":
                    return "更新评论";
                case "DeleteComment":
                    return "删除评论";
                case "AddAttachment":
                    return "添加附件";
                case "DeleteAttachment":
                    return "删除附件";
                default:
                    return actionType;
            }
        }
    }
} 