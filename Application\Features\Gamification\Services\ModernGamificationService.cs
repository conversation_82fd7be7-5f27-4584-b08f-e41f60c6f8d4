using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.SignalR;
using System.Text.Json;
using ItAssetsSystem.Infrastructure.Data;
using ItAssetsSystem.Models.Entities.Gamification;
using ItAssetsSystem.Models.Gamification;
using ItAssetsSystem.Application.Features.Gamification.Hubs;

namespace ItAssetsSystem.Application.Features.Gamification.Services
{
    public interface IModernGamificationService
    {
        // 实时游戏化功能
        Task<RewardResult> ProcessRewardAsync(int userId, string behaviorCode, object context = null);
        Task<LevelUpResult> CheckAndProcessLevelUpAsync(int userId);
        Task<ItemDropResult> ProcessItemDropAsync(int userId, string source);
        
        // 统计分析功能
        Task<List<UserWorkSummaryDto>> GetWorkSummaryAsync(string periodType, DateTime periodDate, int limit = 50);
        Task<List<LeaderboardItemDto>> GetLeaderboardAsync(string type, int limit = 20);
        Task<UserStatsDto> GetUserStatsAsync(int userId);
        Task<List<DepartmentStatsDto>> GetDepartmentStatsAsync();
        
        // 实时更新
        Task NotifyStatsUpdateAsync(int userId, string actionType);
    }

    public class ModernGamificationService : IModernGamificationService
    {
        private readonly AppDbContext _context;
        private readonly IMemoryCache _memoryCache;
        private readonly IDistributedCache _distributedCache;
        private readonly IHubContext<GamificationHub> _hubContext;
        private readonly ILogger<ModernGamificationService> _logger;

        public ModernGamificationService(
            AppDbContext context,
            IMemoryCache memoryCache,
            IDistributedCache distributedCache,
            IHubContext<GamificationHub> hubContext,
            ILogger<ModernGamificationService> logger)
        {
            _context = context;
            _memoryCache = memoryCache;
            _distributedCache = distributedCache;
            _hubContext = hubContext;
            _logger = logger;
        }

        public async Task<RewardResult> ProcessRewardAsync(int userId, string behaviorCode, object context = null)
        {
            try
            {
                // 获取行为规则
                var rule = await GetBehaviorRuleAsync(behaviorCode);
                if (rule == null || !rule.IsActive)
                {
                    return RewardResult.Failed("行为规则不存在或已禁用");
                }

                // 检查每日限制
                if (!await CheckDailyLimitsAsync(userId, rule.Points))
                {
                    return RewardResult.Failed("今日积分获取已达上限");
                }

                using var transaction = await _context.Database.BeginTransactionAsync();
                try
                {
                    // 更新用户统计
                    var userStats = await GetOrCreateUserStatsAsync(userId);
                    userStats.PointsBalance += rule.Points;
                    userStats.CoinsBalance += rule.Coins;
                    userStats.DiamondsBalance += rule.Diamonds;
                    userStats.CurrentXP += rule.Xp;
                    userStats.LastActivityTimestamp = DateTime.Now;

                    // 更新每日限制
                    await UpdateDailyLimitsAsync(userId, rule.Points);

                    // 记录游戏化日志
                    var log = new ItAssetsSystem.Models.Entities.Gamification.GamificationLog
                    {
                        UserId = userId,
                        ActionType = behaviorCode,
                        PointsGained = rule.Points,
                        CoinsGained = rule.Coins,
                        DiamondsGained = rule.Diamonds,
                        XPChange = rule.Xp,
                        Description = rule.BehaviorName,
                        Metadata = context != null ? JsonSerializer.Serialize(context) : null,
                        Timestamp = DateTime.Now
                    };
                    _context.GamificationLogs.Add(log);

                    await _context.SaveChangesAsync();

                    // 检查升级
                    var levelUpResult = await CheckAndProcessLevelUpAsync(userId);

                    // 处理道具掉落
                    ItemDropResult itemDropResult = null;
                    if (rule.ItemDropChance > 0 && new Random().NextDouble() < (double)rule.ItemDropChance)
                    {
                        itemDropResult = await ProcessItemDropAsync(userId, behaviorCode);
                    }

                    await transaction.CommitAsync();

                    // 实时通知
                    await NotifyStatsUpdateAsync(userId, behaviorCode);

                    // 清除相关缓存
                    await ClearUserCacheAsync(userId);

                    return RewardResult.CreateSuccess(new RewardData
                    {
                        Points = rule.Points,
                        Coins = rule.Coins,
                        Diamonds = rule.Diamonds,
                        Xp = rule.Xp,
                        LevelUp = levelUpResult?.Success == true ? ConvertToModernLevelUpResult(levelUpResult) : null,
                        ItemDrop = itemDropResult?.Success == true ? ConvertToModernItemDropResult(itemDropResult) : null
                    });
                }
                catch
                {
                    await transaction.RollbackAsync();
                    throw;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理奖励失败: UserId={UserId}, BehaviorCode={BehaviorCode}", userId, behaviorCode);
                return RewardResult.Failed("处理奖励时发生错误");
            }
        }

        public async Task<List<UserWorkSummaryDto>> GetWorkSummaryAsync(string periodType, DateTime periodDate, int limit = 50)
        {
            var cacheKey = $"work_summary_{periodType}_{periodDate:yyyyMMdd}_{limit}";
            
            // L1缓存：内存缓存（1分钟）
            if (_memoryCache.TryGetValue(cacheKey, out List<UserWorkSummaryDto> cached))
            {
                _logger.LogDebug("从内存缓存获取工作汇总: {CacheKey}", cacheKey);
                return cached;
            }

            // L2缓存：Redis缓存（5分钟）
            var redisData = await _distributedCache.GetStringAsync(cacheKey);
            if (!string.IsNullOrEmpty(redisData))
            {
                var data = JsonSerializer.Deserialize<List<UserWorkSummaryDto>>(redisData);
                _memoryCache.Set(cacheKey, data, TimeSpan.FromMinutes(1));
                _logger.LogDebug("从Redis缓存获取工作汇总: {CacheKey}", cacheKey);
                return data;
            }

            // L3：数据库查询
            var (startDate, endDate) = GetPeriodRange(periodType, periodDate);
            
            var results = await (from u in _context.Users
                                let userLogs = _context.GamificationLogs
                                    .Where(gl => gl.UserId == u.Id &&
                                           gl.Timestamp >= startDate &&
                                           gl.Timestamp < endDate)
                                select new UserWorkSummaryDto
                                {
                                    UserId = u.Id,
                                    UserName = u.Name,
                                    DepartmentName = u.Department.Name,
                                    PeriodType = periodType,
                                    PeriodDate = startDate,
                                    
                                    // 任务统计
                                    TasksCreated = userLogs.Count(gl => gl.ActionType == "TASK_CREATE"),
                                    TasksClaimed = userLogs.Count(gl => gl.ActionType == "TASK_CLAIM"),
                                    TasksCompleted = userLogs.Count(gl => gl.ActionType == "TASK_COMPLETE"),
                                    TasksCommented = userLogs.Count(gl => gl.ActionType == "TASK_COMMENT"),
                                    
                                    // 资产统计
                                    AssetsCreated = userLogs.Count(gl => gl.ActionType == "ASSET_CREATE"),
                                    AssetsUpdated = userLogs.Count(gl => gl.ActionType == "ASSET_UPDATE"),
                                    AssetsDeleted = userLogs.Count(gl => gl.ActionType == "ASSET_DELETE"),
                                    
                                    // 故障统计
                                    FaultsReported = userLogs.Count(gl => gl.ActionType == "FAULT_REPORT"),
                                    FaultsRepaired = userLogs.Count(gl => gl.ActionType == "FAULT_REPAIR"),
                                    
                                    // 采购统计
                                    ProcurementsCreated = userLogs.Count(gl => gl.ActionType == "PROCUREMENT_CREATE"),
                                    ProcurementsUpdated = userLogs.Count(gl => gl.ActionType == "PROCUREMENT_UPDATE"),
                                    
                                    // 备件统计
                                    PartsIn = userLogs.Count(gl => gl.ActionType == "INVENTORY_IN"),
                                    PartsOut = userLogs.Count(gl => gl.ActionType == "INVENTORY_OUT"),
                                    PartsAdded = userLogs.Count(gl => gl.ActionType == "INVENTORY_ADD"),
                                    
                                    // 游戏化汇总
                                    TotalPointsEarned = userLogs.Sum(gl => gl.PointsGained),
                                    TotalCoinsEarned = userLogs.Sum(gl => gl.CoinsGained),
                                    TotalDiamondsEarned = userLogs.Sum(gl => gl.DiamondsGained),
                                    TotalXpEarned = userLogs.Sum(gl => gl.XPChange)
                                })
                                .Where(x => x.TotalPointsEarned > 0 || x.TasksCompleted > 0) // 只显示有活动的用户
                                .OrderByDescending(x => x.TotalPointsEarned)
                                .Take(limit)
                                .ToListAsync();

            // 计算排名和评价
            for (int i = 0; i < results.Count; i++)
            {
                results[i].PointsRank = i + 1;
                results[i].ProductivityRank = i + 1;
                results[i].Evaluation = GetEvaluationText(results[i].PointsRank);
            }

            // 写入缓存
            var serializedData = JsonSerializer.Serialize(results);
            await _distributedCache.SetStringAsync(cacheKey, serializedData, 
                new DistributedCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(5)
                });
            _memoryCache.Set(cacheKey, results, TimeSpan.FromMinutes(1));

            _logger.LogInformation("从数据库查询工作汇总: {Count}条记录", results.Count);
            return results;
        }

        public async Task<List<LeaderboardItemDto>> GetLeaderboardAsync(string type, int limit = 20)
        {
            var cacheKey = $"leaderboard_{type}_{limit}";
            
            // 尝试从缓存获取
            if (_memoryCache.TryGetValue(cacheKey, out List<LeaderboardItemDto> cached))
            {
                return cached;
            }

            var query = from gus in _context.GamificationUserStats
                       join u in _context.Users on gus.CoreUserId equals u.Id
                       join ul in _context.UserLevels on gus.CurrentLevel equals ul.Level into levelJoin
                       from ul in levelJoin.DefaultIfEmpty()
                       join d in _context.Departments on u.DepartmentId equals d.Id into deptJoin
                       from d in deptJoin.DefaultIfEmpty()
                       where u.Id > 0
                       select new { gus, u, ul, d };

            // 先获取数据到内存，然后进行排序和分页
            var queryData = await query.ToListAsync();

            var orderedData = type switch
            {
                "points" => queryData.OrderByDescending(x => x.gus.PointsBalance),
                "coins" => queryData.OrderByDescending(x => x.gus.CoinsBalance),
                "diamonds" => queryData.OrderByDescending(x => x.gus.DiamondsBalance),
                "level" => queryData.OrderByDescending(x => x.gus.CurrentLevel).ThenByDescending(x => x.gus.CurrentXP),
                _ => queryData.OrderByDescending(x => x.gus.PointsBalance)
            };

            var results = orderedData
                .Take(limit)
                .Select((x, index) => new LeaderboardItemDto
                {
                    Rank = index + 1,
                    UserId = x.u.Id,
                    UserName = x.u.Name,
                    DepartmentName = x.d.Name ?? "未分配",
                    TotalPoints = x.gus.PointsBalance,
                    TotalCoins = x.gus.CoinsBalance,
                    TotalDiamonds = x.gus.DiamondsBalance,
                    CurrentLevel = x.gus.CurrentLevel,
                    LevelName = x.ul.Name ?? $"等级{x.gus.CurrentLevel}",
                    LevelColor = x.ul.Color ?? "#666666",
                    TasksCompleted = x.gus.CompletedTasksCount,
                    TotalItemsObtained = 0, // 暂时设为0，因为实体中没有此属性
                    ConsecutiveDays = x.gus.StreakCount, // 使用 StreakCount 作为连续天数
                    LastActivity = x.gus.LastActivityTimestamp
                })
                .ToList();

            // 缓存结果
            _memoryCache.Set(cacheKey, results, TimeSpan.FromMinutes(2));
            
            return results;
        }

        public async Task NotifyStatsUpdateAsync(int userId, string actionType)
        {
            try
            {
                // 获取更新后的用户统计
                var userStats = await GetUserStatsAsync(userId);
                
                // 发送个人统计更新
                await _hubContext.Clients.Group($"User_{userId}")
                    .SendAsync("UserStatsUpdated", userStats);

                // 获取最新排行榜
                var leaderboard = await GetLeaderboardAsync("points", 20);
                
                // 发送排行榜更新
                await _hubContext.Clients.Group("Statistics")
                    .SendAsync("LeaderboardUpdated", new
                    {
                        Type = "points",
                        Data = leaderboard,
                        UpdatedAt = DateTime.Now,
                        TriggerUserId = userId,
                        TriggerAction = actionType
                    });

                // 如果是前10名用户，发送特殊通知
                var userRank = leaderboard.FindIndex(x => x.UserId == userId) + 1;
                if (userRank > 0 && userRank <= 10)
                {
                    await _hubContext.Clients.Group("Statistics")
                        .SendAsync("TopUserActivity", new
                        {
                            UserId = userId,
                            UserName = leaderboard[userRank - 1].UserName,
                            Rank = userRank,
                            Action = actionType,
                            Timestamp = DateTime.Now
                        });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "发送实时通知失败: UserId={UserId}, ActionType={ActionType}", userId, actionType);
            }
        }

        // 私有辅助方法
        private async Task<GamificationBehaviorRule> GetBehaviorRuleAsync(string behaviorCode)
        {
            var cacheKey = $"behavior_rule_{behaviorCode}";
            if (_memoryCache.TryGetValue(cacheKey, out GamificationBehaviorRule cached))
            {
                return cached;
            }

            var behaviorType = await _context.BehaviorTypes
                .FirstOrDefaultAsync(bt => bt.Code == behaviorCode);

            if (behaviorType == null) return null;

            var rule = new GamificationBehaviorRule
            {
                BehaviorCode = behaviorType.Code,
                BehaviorName = behaviorType.Name,
                ModuleName = behaviorType.Category ?? "General",
                Points = behaviorType.BasePoints,
                Coins = behaviorType.BaseCoins,
                Xp = behaviorType.BasePoints, // 使用积分作为经验值
                Diamonds = behaviorType.BaseDiamonds,
                ItemDropChance = 0.01m, // 默认掉落率
                IsActive = behaviorType.IsActive,
                CreatedAt = behaviorType.CreatedAt,
                UpdatedAt = behaviorType.UpdatedAt
            };
            
            if (rule != null)
            {
                _memoryCache.Set(cacheKey, rule, TimeSpan.FromMinutes(10));
            }
            
            return rule;
        }

        private async Task<bool> CheckDailyLimitsAsync(int userId, int pointsToEarn)
        {
            var today = DateTime.Today;
            var dailyLimit = await _context.DailyLimits
                .FirstOrDefaultAsync(dl => dl.UserId == userId && dl.LimitDate == today);

            if (dailyLimit == null)
            {
                // 创建今日限制记录
                dailyLimit = new DailyLimit
                {
                    UserId = userId,
                    LimitDate = today,
                    PointsEarned = 0,
                    MaxPoints = 200,
                    ItemsObtained = 0,
                    MaxItems = 5,
                    TasksCompleted = 0
                };
                _context.DailyLimits.Add(dailyLimit);
                await _context.SaveChangesAsync();
            }

            return (dailyLimit.PointsEarned + pointsToEarn) <= dailyLimit.MaxPoints;
        }

        private async Task UpdateDailyLimitsAsync(int userId, int pointsEarned)
        {
            var today = DateTime.Today;
            var dailyLimit = await _context.DailyLimits
                .FirstOrDefaultAsync(dl => dl.UserId == userId && dl.LimitDate == today);

            if (dailyLimit != null)
            {
                dailyLimit.PointsEarned += pointsEarned;
                dailyLimit.UpdatedAt = DateTime.Now;
            }
        }

        private async Task<GamificationUserStats> GetOrCreateUserStatsAsync(int userId)
        {
            var userStats = await _context.GamificationUserStats
                .FirstOrDefaultAsync(gus => gus.CoreUserId == userId);

            if (userStats == null)
            {
                userStats = new GamificationUserStats
                {
                    CoreUserId = userId,
                    CurrentXP = 0,
                    CurrentLevel = 1,
                    PointsBalance = 0,
                    CoinsBalance = 0,
                    DiamondsBalance = 0,
                    CompletedTasksCount = 0,
                    CreatedTasksCount = 0,
                    ClaimedTasksCount = 0,
                    OnTimeTasksCount = 0,
                    StreakCount = 0,
                    LastActivityTimestamp = DateTime.Now,
                    LastUpdatedTimestamp = DateTime.Now
                };

                _context.GamificationUserStats.Add(userStats);
                await _context.SaveChangesAsync();
            }

            return userStats;
        }

        private (DateTime startDate, DateTime endDate) GetPeriodRange(string periodType, DateTime periodDate)
        {
            switch (periodType.ToLower())
            {
                case "daily":
                    return (periodDate.Date, periodDate.Date.AddDays(1));
                case "weekly":
                    var startOfWeek = periodDate.AddDays(-(int)periodDate.DayOfWeek + 1);
                    return (startOfWeek.Date, startOfWeek.Date.AddDays(7));
                case "monthly":
                    var startOfMonth = new DateTime(periodDate.Year, periodDate.Month, 1);
                    return (startOfMonth, startOfMonth.AddMonths(1));
                default:
                    return (periodDate.Date, periodDate.Date.AddDays(1));
            }
        }

        private string GetEvaluation(int rank)
        {
            return rank switch
            {
                <= 3 => "🥇 超级明星",
                <= 10 => "🏆 优秀员工",
                <= 20 => "⭐ 努力奋斗",
                _ => "💪 稳步提升"
            };
        }

        private async Task ClearUserCacheAsync(int userId)
        {
            // 清除用户相关的缓存
            var patterns = new[]
            {
                $"user_stats_{userId}",
                "leaderboard_",
                "work_summary_"
            };

            foreach (var pattern in patterns)
            {
                // 这里需要根据实际的缓存实现来清除匹配的键
                // 简化实现，实际项目中可能需要更复杂的缓存管理
            }
        }

        // 其他方法的实现...
        public async Task<LevelUpResult> CheckAndProcessLevelUpAsync(int userId)
        {
            // 实现升级检查逻辑
            throw new NotImplementedException();
        }

        public async Task<ItemDropResult> ProcessItemDropAsync(int userId, string source)
        {
            // 实现道具掉落逻辑
            throw new NotImplementedException();
        }

        public async Task<UserStatsDto> GetUserStatsAsync(int userId)
        {
            // 实现用户统计获取逻辑
            throw new NotImplementedException();
        }

        public async Task<List<DepartmentStatsDto>> GetDepartmentStatsAsync()
        {
            // 实现部门统计获取逻辑
            throw new NotImplementedException();
        }

        private ItAssetsSystem.Models.Gamification.LevelUpResult ConvertToModernLevelUpResult(LevelUpResult source)
        {
            if (source == null) return null;

            return new ItAssetsSystem.Models.Gamification.LevelUpResult
            {
                Success = source.Success,
                Message = source.Message,
                OldLevel = source.OldLevel,
                NewLevel = source.NewLevel,
                LevelName = source.LevelName,
                LevelColor = "#000000", // 默认颜色，因为源类型没有此属性
                RewardCoins = source.RewardCoins,
                RewardDiamonds = source.RewardDiamonds,
                UnlockedFeatures = source.UnlockedFeatures?.Split(',') ?? new string[0],
                LevelUpTime = DateTime.Now
            };
        }

        private ItAssetsSystem.Models.Gamification.ItemDropResult ConvertToModernItemDropResult(ItAssetsSystem.Application.Features.Gamification.Services.ItemDropResult source)
        {
            if (source == null) return null;

            return new ItAssetsSystem.Models.Gamification.ItemDropResult
            {
                Success = source.Success,
                Message = source.Message,
                ItemId = source.Item?.ItemId ?? 0,
                ItemName = source.Item?.Name ?? "",
                Rarity = source.Rarity ?? "Common",
                IconUrl = source.Item?.IconUrl ?? "",
                ObtainedAt = DateTime.Now
            };
        }

        /// <summary>
        /// 获取评价文本
        /// </summary>
        private string GetEvaluationText(int rank)
        {
            return rank switch
            {
                1 => "优秀",
                2 => "良好",
                3 => "中等",
                _ => "一般"
            };
        }
    }
}
