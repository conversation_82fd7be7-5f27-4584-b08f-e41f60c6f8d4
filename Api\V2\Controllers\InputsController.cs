// File: Api/V2/Controllers/InputsController.cs
// Description: 输入内容API
using Microsoft.AspNetCore.Mvc;
using MediatR;
using ItAssetsSystem.Application.Features.Inputs.Commands;
using ItAssetsSystem.Application.Features.Inputs.Dtos;
using ItAssetsSystem.Application.Features.Inputs.Queries;
using System.Threading.Tasks;

namespace ItAssetsSystem.Api.V2.Controllers
{
    [ApiController]
    [Route("api/v2/inputs")]
    public class InputsController : ControllerBase
    {
        private readonly IMediator _mediator;
        public InputsController(IMediator mediator)
        {
            _mediator = mediator;
        }

        [HttpPost]
        public async Task<IActionResult> SubmitInput([FromBody] InputRequestDto dto)
        {
            if (string.IsNullOrWhiteSpace(dto.Content))
                return BadRequest(new { success = false, message = "内容不能为空" });

            var result = await _mediator.Send(new SubmitInputCommand { Content = dto.Content });
            return Ok(result);
        }

        [HttpGet]
        public async Task<IActionResult> GetInputList()
        {
            var result = await _mediator.Send(new GetInputListQuery());
            return Ok(result);
        }

        [HttpDelete("{time}")]
        public async Task<IActionResult> DeleteInput(string time)
        {
            var result = await _mediator.Send(new DeleteInputCommand { Time = time });
            if (result.Success)
                return Ok(result);
            return NotFound(result);
        }
    }
} 