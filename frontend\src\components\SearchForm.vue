/**
 * 航空航天级IT资产管理系统 - 搜索表单组件
 * 文件路径: src/components/SearchForm.vue
 * 功能描述: 通用搜索表单，支持多条件搜索和表单重置
 */

<template>
  <div class="search-form" :class="{ 'is-collapsed': isCollapsed && collapsible }">
    <el-form
      ref="formRef"
      :model="formData"
      :inline="true"
      size="default"
      @submit.prevent="handleSearch"
    >
      <slot :form="formData"></slot>
      
      <div class="search-buttons">
        <el-button type="primary" @click="handleSearch">
          <el-icon><Search /></el-icon>
          <span>搜索</span>
        </el-button>
        <el-button @click="handleReset">
          <el-icon><Refresh /></el-icon>
          <span>重置</span>
        </el-button>
        <el-button 
          v-if="collapsible" 
          type="text" 
          @click="toggleCollapse"
        >
          {{ isCollapsed ? '展开' : '收起' }}
          <el-icon>
            <component :is="isCollapsed ? 'ArrowDown' : 'ArrowUp'" />
          </el-icon>
        </el-button>
      </div>
    </el-form>
  </div>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { Search, Refresh, ArrowDown, ArrowUp } from '@element-plus/icons-vue'

const props = defineProps({
  // 初始表单数据
  initialFormData: {
    type: Object,
    default: () => ({})
  },
  // 是否支持折叠表单
  collapsible: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['search', 'reset'])

const formRef = ref(null)
const isCollapsed = ref(false)
const formData = reactive({ ...props.initialFormData })

// 监听初始表单数据变化
watch(() => props.initialFormData, (newVal) => {
  Object.keys(formData).forEach(key => {
    formData[key] = newVal[key] || null
  })
}, { deep: true })

// 搜索处理
const handleSearch = () => {
  emit('search', { ...formData })
}

// 重置处理
const handleReset = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  
  // 重置为初始值
  Object.keys(formData).forEach(key => {
    formData[key] = props.initialFormData[key] || null
  })
  
  emit('reset', { ...formData })
}

// 切换折叠状态
const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value
}

// 向父组件暴露方法
defineExpose({
  resetForm: handleReset,
  search: handleSearch
})
</script>

<style lang="scss" scoped>
.search-form {
  width: 100%;
  margin-bottom: 24px;
  padding: 24px;
  background-color: var(--el-bg-color);
  border-radius: 4px;
  box-shadow: var(--el-box-shadow-light);
  transition: all 0.3s;
  
  &.is-collapsed {
    padding: 16px 24px;
    
    :deep(.el-form-item) {
      &:nth-child(n+4) {
        display: none;
      }
    }
  }
  
  .search-buttons {
    display: flex;
    align-items: center;
    margin-left: 8px;
    
    .el-button {
      margin-right: 8px;
      
      &:last-child {
        margin-right: 0;
      }
    }
  }
}
</style> 