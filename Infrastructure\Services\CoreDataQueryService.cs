// File: Infrastructure/Services/CoreDataQueryService.cs
// Description: 核心数据查询服务实现，用于封装对核心模块的数据查询

using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text.Json;
using System.Threading.Tasks;
using ItAssetsSystem.Core.Abstractions;
using ItAssetsSystem.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace ItAssetsSystem.Infrastructure.Services
{
    /// <summary>
    /// 核心数据查询服务实现
    /// </summary>
    public class CoreDataQueryService : ICoreDataQueryService
    {
        private readonly AppDbContext _dbContext;
        private readonly ILogger<CoreDataQueryService> _logger;
        private readonly IHttpClientFactory _httpClientFactory;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="dbContext">数据库上下文</param>
        /// <param name="logger">日志记录器</param>
        /// <param name="httpClientFactory">HTTP客户端工厂</param>
        public CoreDataQueryService(
            AppDbContext dbContext,
            ILogger<CoreDataQueryService> logger,
            IHttpClientFactory httpClientFactory)
        {
            _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _httpClientFactory = httpClientFactory ?? throw new ArgumentNullException(nameof(httpClientFactory));
        }

        /// <summary>
        /// 获取用户信息
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>用户信息</returns>
        public async Task<CoreUserDto> GetUserAsync(int userId)
        {
            try
            {
                // 使用EF Core只读查询从核心模块查询用户信息
                var user = await _dbContext.Users
                    .AsNoTracking()
                    .Include(u => u.Department)
                    .FirstOrDefaultAsync(u => u.Id == userId);

                if (user == null)
                {
                    return null;
                }

                return new CoreUserDto
                {
                    Id = user.Id,
                    Username = user.Username,
                    Name = user.Name,
                    Email = user.Email,
                    DepartmentId = user.DepartmentId,
                    DepartmentName = user.Department?.Name,
                    AvatarUrl = user.Avatar,
                    PhoneNumber = user.Mobile,
                    Position = user.Position
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户信息(ID={UserId})时发生错误", userId);
                throw;
            }
        }

        /// <summary>
        /// 获取多个用户信息
        /// </summary>
        /// <param name="userIds">用户ID列表</param>
        /// <returns>用户信息列表</returns>
        public async Task<List<CoreUserDto>> GetUsersAsync(IEnumerable<int> userIds)
        {
            try
            {
                if (userIds == null || !userIds.Any())
                {
                    return new List<CoreUserDto>();
                }

                var uniqueUserIds = userIds.Distinct().ToList();

                var users = await _dbContext.Users
                    .AsNoTracking()
                    .Include(u => u.Department)
                    .Where(u => uniqueUserIds.Contains(u.Id))
                    .ToListAsync();

                return users.Select(user => new CoreUserDto
                {
                    Id = user.Id,
                    Username = user.Username,
                    Name = user.Name,
                    Email = user.Email,
                    DepartmentId = user.DepartmentId,
                    DepartmentName = user.Department?.Name,
                    AvatarUrl = user.Avatar,
                    PhoneNumber = user.Mobile,
                    Position = user.Position
                }).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取多个用户信息时发生错误");
                throw;
            }
        }

        /// <summary>
        /// 获取资产信息
        /// </summary>
        /// <param name="assetId">资产ID</param>
        /// <returns>资产信息</returns>
        public async Task<CoreAssetDto> GetAssetAsync(int assetId)
        {
            try
            {
                var asset = await _dbContext.Assets
                    .AsNoTracking()
                    .Include(a => a.AssetType)
                    .Include(a => a.Location)
                    .FirstOrDefaultAsync(a => a.Id == assetId);

                if (asset == null)
                {
                    return null;
                }

                return new CoreAssetDto
                {
                    Id = asset.Id,
                    Name = asset.Name,
                    AssetNumber = asset.AssetCode,
                    AssetTypeId = asset.AssetTypeId,
                    AssetTypeName = asset.AssetType?.Name,
                    LocationId = asset.LocationId,
                    LocationName = asset.Location?.Name
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取资产信息(ID={AssetId})时发生错误", assetId);
                throw;
            }
        }

        /// <summary>
        /// 获取多个资产信息
        /// </summary>
        /// <param name="assetIds">资产ID列表</param>
        /// <returns>资产信息列表</returns>
        public async Task<List<CoreAssetDto>> GetAssetsAsync(IEnumerable<int> assetIds)
        {
            try
            {
                if (assetIds == null || !assetIds.Any())
                {
                    return new List<CoreAssetDto>();
                }

                var uniqueAssetIds = assetIds.Distinct().ToList();

                var assets = await _dbContext.Assets
                    .AsNoTracking()
                    .Include(a => a.AssetType)
                    .Include(a => a.Location)
                    .Where(a => uniqueAssetIds.Contains(a.Id))
                    .ToListAsync();

                return assets.Select(asset => new CoreAssetDto
                {
                    Id = asset.Id,
                    Name = asset.Name,
                    AssetNumber = asset.AssetCode,
                    AssetTypeId = asset.AssetTypeId,
                    AssetTypeName = asset.AssetType?.Name,
                    LocationId = asset.LocationId,
                    LocationName = asset.Location?.Name
                }).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取多个资产信息时发生错误");
                throw;
            }
        }

        /// <summary>
        /// 获取位置信息
        /// </summary>
        /// <param name="locationId">位置ID</param>
        /// <returns>位置信息</returns>
        public async Task<CoreLocationDto> GetLocationAsync(int locationId)
        {
            try
            {
                var location = await _dbContext.Locations
                    .AsNoTracking()
                    .Include(l => l.Parent)
                    .FirstOrDefaultAsync(l => l.Id == locationId);

                if (location == null)
                {
                    return null;
                }

                return new CoreLocationDto
                {
                    Id = location.Id,
                    Name = location.Name,
                    LocationCode = location.Code,
                    ParentId = location.ParentId,
                    ParentName = location.Parent?.Name,
                    FullPath = location.Path
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取位置信息(ID={LocationId})时发生错误", locationId);
                throw;
            }
        }

        /// <summary>
        /// 获取多个位置信息
        /// </summary>
        /// <param name="locationIds">位置ID列表</param>
        /// <returns>位置信息列表</returns>
        public async Task<List<CoreLocationDto>> GetLocationsAsync(IEnumerable<int> locationIds)
        {
            try
            {
                if (locationIds == null || !locationIds.Any())
                {
                    return new List<CoreLocationDto>();
                }

                var uniqueLocationIds = locationIds.Distinct().ToList();

                var locations = await _dbContext.Locations
                    .AsNoTracking()
                    .Include(l => l.Parent)
                    .Where(l => uniqueLocationIds.Contains(l.Id))
                    .ToListAsync();

                return locations.Select(location => new CoreLocationDto
                {
                    Id = location.Id,
                    Name = location.Name,
                    LocationCode = location.Code,
                    ParentId = location.ParentId,
                    ParentName = location.Parent?.Name,
                    FullPath = location.Path
                }).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取多个位置信息时发生错误");
                throw;
            }
        }

        /// <summary>
        /// 获取部门信息
        /// </summary>
        /// <param name="departmentId">部门ID</param>
        /// <returns>部门信息</returns>
        public async Task<CoreDepartmentDto> GetDepartmentAsync(int departmentId)
        {
            try
            {
                var department = await _dbContext.Departments
                    .AsNoTracking()
                    .Include(d => d.Parent)
                    .FirstOrDefaultAsync(d => d.Id == departmentId);

                if (department == null)
                {
                    return null;
                }

                return new CoreDepartmentDto
                {
                    Id = department.Id,
                    Name = department.Name,
                    DepartmentCode = department.Code,
                    ParentId = department.ParentId,
                    ParentName = department.Parent?.Name
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取部门信息(ID={DepartmentId})时发生错误", departmentId);
                throw;
            }
        }

        /// <summary>
        /// 验证用户是否存在
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>用户是否存在</returns>
        public async Task<bool> UserExistsAsync(int userId)
        {
            try
            {
                return await _dbContext.Users
                    .AsNoTracking()
                    .AnyAsync(u => u.Id == userId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证用户是否存在(ID={UserId})时发生错误", userId);
                throw;
            }
        }

        /// <summary>
        /// 验证资产是否存在
        /// </summary>
        /// <param name="assetId">资产ID</param>
        /// <returns>资产是否存在</returns>
        public async Task<bool> AssetExistsAsync(int assetId)
        {
            try
            {
                return await _dbContext.Assets
                    .AsNoTracking()
                    .AnyAsync(a => a.Id == assetId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证资产是否存在(ID={AssetId})时发生错误", assetId);
                throw;
            }
        }

        /// <summary>
        /// 验证位置是否存在
        /// </summary>
        /// <param name="locationId">位置ID</param>
        /// <returns>位置是否存在</returns>
        public async Task<bool> LocationExistsAsync(int locationId)
        {
            try
            {
                return await _dbContext.Locations
                    .AsNoTracking()
                    .AnyAsync(l => l.Id == locationId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证位置是否存在(ID={LocationId})时发生错误", locationId);
                throw;
            }
        }

        /// <summary>
        /// 获取所有用户的简要信息
        /// </summary>
        /// <returns>用户简要信息列表</returns>
        public async Task<List<CoreUserBasicDto>> GetAllUsersBasicInfoAsync()
        {
            try
            {
                var users = await _dbContext.Users
                    .AsNoTracking()
                    .Include(u => u.Department)
                    .Select(u => new CoreUserBasicDto
                    {
                        Id = u.Id,
                        Name = u.Name,
                        DepartmentName = u.Department != null ? u.Department.Name : null
                    })
                    .ToListAsync();

                return users;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取所有用户简要信息时发生错误");
                throw;
            }
        }

        /// <summary>
        /// 获取所有资产的简要信息
        /// </summary>
        /// <returns>资产简要信息列表</returns>
        public async Task<List<CoreAssetBasicDto>> GetAllAssetsBasicInfoAsync()
        {
            try
            {
                var assets = await _dbContext.Assets
                    .AsNoTracking()
                    .Select(a => new CoreAssetBasicDto
                    {
                        Id = a.Id,
                        Name = a.Name,
                        AssetNumber = a.AssetCode
                    })
                    .ToListAsync();

                return assets;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取所有资产简要信息时发生错误");
                throw;
            }
        }

        /// <summary>
        /// 获取所有位置的简要信息
        /// </summary>
        /// <returns>位置简要信息列表</returns>
        public async Task<List<CoreLocationBasicDto>> GetAllLocationsBasicInfoAsync()
        {
            try
            {
                var locations = await _dbContext.Locations
                    .AsNoTracking()
                    .Select(l => new CoreLocationBasicDto
                    {
                        Id = l.Id,
                        Name = l.Name,
                        FullPath = l.Path
                    })
                    .ToListAsync();

                return locations;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取所有位置简要信息时发生错误");
                throw;
            }
        }
    }
} 