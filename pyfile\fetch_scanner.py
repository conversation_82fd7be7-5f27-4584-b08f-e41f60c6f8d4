import requests
from urllib3.exceptions import InsecureRequestWarning

# 禁用 HTTPS 不安全请求警告（仅测试用，生产环境需配置证书）
requests.packages.urllib3.disable_warnings(InsecureRequestWarning)

# 构造请求 URL
base_url = "https://*************"
endpoint = "/evo-apigw/evo-brm/1.2.0/person/page/bind"
url = base_url + endpoint

# 整理请求头（移除伪头，保留常规头）
headers = {
    "Accept": "application/json, text/plain, */*",
    "Accept-Encoding": "gzip, deflate, br, zstd",
    "Accept-Language": "zh-CN",
    "Authorization": "bearer 1:5090df43-10bd-47d5-b1dd-fea1ebf6addb",  # 按新头更新
    "Content-Length": "67",
    "Content-Type": "application/json;charset=UTF-8",
    "Cookie": "JSESSIONID=96CF328FB7DF28B0136F9659D4FC2E79; name=value",  # 按新头更新
    "Origin": "https://*************",
    "Referer": "https://*************/bResource/",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
}

# 构造请求负载（若负载无变化，沿用之前逻辑）
payload = {
    "pageNum": 1,
    "pageSize": 10,
    "statusList": [1, 2],
    "searchKey": "王伟"
}

# 发送请求
response = requests.post(
    url, 
    headers=headers, 
    json=payload, 
    verify=False  # 测试时忽略 SSL 验证
)

# 输出结果
print("响应状态码:", response.status_code)
print("响应内容:", response.text)