// File: Application/Features/Tasks/Dtos/PdcaPlanDto.cs
// Description: PDCA计划数据传输对象

using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using ItAssetsSystem.Models.Enums;

namespace ItAssetsSystem.Application.Features.Tasks.Dtos
{
    /// <summary>
    /// PDCA计划数据传输对象
    /// </summary>
    public class PdcaPlanDto
    {
        /// <summary>
        /// 计划ID
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// 计划名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 计划描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 当前PDCA阶段
        /// </summary>
        public PDCAStage CurrentStage { get; set; }

        /// <summary>
        /// 当前PDCA阶段显示名称
        /// </summary>
        public string CurrentStageDisplayName { get; set; }

        /// <summary>
        /// 计划开始时间
        /// </summary>
        public DateTime StartDate { get; set; }

        /// <summary>
        /// 计划结束时间（预计）
        /// </summary>
        public DateTime? PlannedEndDate { get; set; }

        /// <summary>
        /// 计划实际结束时间
        /// </summary>
        public DateTime? ActualEndDate { get; set; }

        /// <summary>
        /// 创建者ID
        /// </summary>
        public int CreatorUserId { get; set; }

        /// <summary>
        /// 创建者名称
        /// </summary>
        public string CreatorUserName { get; set; }

        /// <summary>
        /// 负责人ID
        /// </summary>
        public int? OwnerUserId { get; set; }

        /// <summary>
        /// 负责人名称
        /// </summary>
        public string OwnerUserName { get; set; }

        /// <summary>
        /// 所属资产ID
        /// </summary>
        public int? AssetId { get; set; }

        /// <summary>
        /// 所属资产名称
        /// </summary>
        public string AssetName { get; set; }

        /// <summary>
        /// 所属位置ID
        /// </summary>
        public int? LocationId { get; set; }

        /// <summary>
        /// 所属位置名称
        /// </summary>
        public string LocationName { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// 完成进度百分比
        /// </summary>
        public int Progress { get; set; }

        /// <summary>
        /// 是否活跃
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// 是否已归档
        /// </summary>
        public bool IsArchived { get; set; }

        /// <summary>
        /// 预计的P阶段结束时间
        /// </summary>
        public DateTime? PlannedPStageEndDate { get; set; }

        /// <summary>
        /// 预计的D阶段结束时间
        /// </summary>
        public DateTime? PlannedDStageEndDate { get; set; }

        /// <summary>
        /// 预计的C阶段结束时间
        /// </summary>
        public DateTime? PlannedCStageEndDate { get; set; }

        /// <summary>
        /// 预计的A阶段结束时间
        /// </summary>
        public DateTime? PlannedAStageEndDate { get; set; }

        /// <summary>
        /// P阶段实际结束时间
        /// </summary>
        public DateTime? ActualPStageEndDate { get; set; }

        /// <summary>
        /// D阶段实际结束时间
        /// </summary>
        public DateTime? ActualDStageEndDate { get; set; }

        /// <summary>
        /// C阶段实际结束时间
        /// </summary>
        public DateTime? ActualCStageEndDate { get; set; }

        /// <summary>
        /// A阶段实际结束时间
        /// </summary>
        public DateTime? ActualAStageEndDate { get; set; }

        /// <summary>
        /// 关联任务列表
        /// </summary>
        public List<TaskDto> Tasks { get; set; }

        /// <summary>
        /// 计划阶段任务统计
        /// </summary>
        public Dictionary<PDCAStage, int> StageTaskCounts { get; set; }
    }

    /// <summary>
    /// 创建PDCA计划请求DTO
    /// </summary>
    public class CreatePdcaPlanRequestDto
    {
        /// <summary>
        /// 计划名称
        /// </summary>
        [Required(ErrorMessage = "计划名称不能为空")]
        [StringLength(200, ErrorMessage = "计划名称不能超过200个字符")]
        public string Name { get; set; }

        /// <summary>
        /// 计划描述
        /// </summary>
        [StringLength(4000, ErrorMessage = "计划描述不能超过4000个字符")]
        public string Description { get; set; }

        /// <summary>
        /// 计划开始时间
        /// </summary>
        public DateTime StartDate { get; set; } = DateTime.Now;

        /// <summary>
        /// 计划结束时间（预计）
        /// </summary>
        public DateTime? PlannedEndDate { get; set; }

        /// <summary>
        /// 负责人ID
        /// </summary>
        public int? OwnerUserId { get; set; }

        /// <summary>
        /// 所属资产ID
        /// </summary>
        public int? AssetId { get; set; }

        /// <summary>
        /// 所属位置ID
        /// </summary>
        public int? LocationId { get; set; }

        /// <summary>
        /// 预计的P阶段结束时间
        /// </summary>
        public DateTime? PlannedPStageEndDate { get; set; }

        /// <summary>
        /// 预计的D阶段结束时间
        /// </summary>
        public DateTime? PlannedDStageEndDate { get; set; }

        /// <summary>
        /// 预计的C阶段结束时间
        /// </summary>
        public DateTime? PlannedCStageEndDate { get; set; }

        /// <summary>
        /// 预计的A阶段结束时间
        /// </summary>
        public DateTime? PlannedAStageEndDate { get; set; }
    }

    /// <summary>
    /// 更新PDCA计划请求DTO
    /// </summary>
    public class UpdatePdcaPlanRequestDto
    {
        /// <summary>
        /// 计划名称
        /// </summary>
        [StringLength(200, ErrorMessage = "计划名称不能超过200个字符")]
        public string Name { get; set; }

        /// <summary>
        /// 计划描述
        /// </summary>
        [StringLength(4000, ErrorMessage = "计划描述不能超过4000个字符")]
        public string Description { get; set; }

        /// <summary>
        /// 计划结束时间（预计）
        /// </summary>
        public DateTime? PlannedEndDate { get; set; }

        /// <summary>
        /// 负责人ID
        /// </summary>
        public int? OwnerUserId { get; set; }

        /// <summary>
        /// 所属资产ID
        /// </summary>
        public int? AssetId { get; set; }

        /// <summary>
        /// 所属位置ID
        /// </summary>
        public int? LocationId { get; set; }

        /// <summary>
        /// 预计的P阶段结束时间
        /// </summary>
        public DateTime? PlannedPStageEndDate { get; set; }

        /// <summary>
        /// 预计的D阶段结束时间
        /// </summary>
        public DateTime? PlannedDStageEndDate { get; set; }

        /// <summary>
        /// 预计的C阶段结束时间
        /// </summary>
        public DateTime? PlannedCStageEndDate { get; set; }

        /// <summary>
        /// 预计的A阶段结束时间
        /// </summary>
        public DateTime? PlannedAStageEndDate { get; set; }
    }

    /// <summary>
    /// 更新PDCA计划阶段请求DTO
    /// </summary>
    public class UpdatePdcaPlanStageRequestDto
    {
        /// <summary>
        /// 新阶段
        /// </summary>
        [Required(ErrorMessage = "PDCA阶段不能为空")]
        public PDCAStage Stage { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(1000, ErrorMessage = "备注不能超过1000个字符")]
        public string Remarks { get; set; }
    }

    /// <summary>
    /// PDCA计划查询参数DTO
    /// </summary>
    public class PdcaPlanQueryParametersDto
    {
        /// <summary>
        /// 页码（从1开始）
        /// </summary>
        public int PageNumber { get; set; } = 1;

        /// <summary>
        /// 每页大小
        /// </summary>
        public int PageSize { get; set; } = 10;

        /// <summary>
        /// 搜索关键词
        /// </summary>
        public string SearchTerm { get; set; }

        /// <summary>
        /// 是否包含已归档的计划
        /// </summary>
        public bool IncludeArchived { get; set; } = false;

        /// <summary>
        /// 创建者ID过滤
        /// </summary>
        public int? CreatorUserId { get; set; }

        /// <summary>
        /// 负责人ID过滤
        /// </summary>
        public int? OwnerUserId { get; set; }

        /// <summary>
        /// 当前阶段过滤
        /// </summary>
        public PDCAStage? CurrentStage { get; set; }

        /// <summary>
        /// 资产ID过滤
        /// </summary>
        public int? AssetId { get; set; }

        /// <summary>
        /// 位置ID过滤
        /// </summary>
        public int? LocationId { get; set; }

        /// <summary>
        /// 排序字段
        /// </summary>
        public string SortBy { get; set; } = "CreatedAt";

        /// <summary>
        /// 排序方向（asc或desc）
        /// </summary>
        public string SortDirection { get; set; } = "desc";
    }
} 