/**
 * 航空航天级IT资产管理系统 - 系统管理API
 * 文件路径: src/api/system.js
 * 功能描述: 提供系统管理相关的API服务，包括用户、角色、权限、部门管理
 */

import request from '@/utils/request'

export default {
  // 用户管理
  user: {
    /**
     * 获取用户列表
     * @param {Object} params - 查询参数
     * @returns {Promise}
     */
    getUserList(params) {
      return request.get('/users', params)
    },
    
    /**
     * 获取用户详情
     * @param {number|string} id - 用户ID
     * @returns {Promise}
     */
    getUserById(id) {
      return request.get(`/users/${id}`)
    },
    
    /**
     * 创建用户
     * @param {Object} data - 用户数据
     * @returns {Promise}
     */
    createUser(data) {
      return request.post('/users', data)
    },
    
    /**
     * 更新用户
     * @param {number|string} id - 用户ID
     * @param {Object} data - 用户数据
     * @returns {Promise}
     */
    updateUser(id, data) {
      return request.put(`/users/${id}`, data)
    },
    
    /**
     * 删除用户
     * @param {number|string} id - 用户ID
     * @returns {Promise}
     */
    deleteUser(id) {
      return request.delete(`/users/${id}`)
    },
    
    /**
     * 批量删除用户
     * @param {Array} ids - 用户ID数组
     * @returns {Promise}
     */
    batchDeleteUsers(ids) {
      return request.post('/users/batch-delete', { ids })
    },
    
    /**
     * 重置用户密码
     * @param {number|string} id - 用户ID
     * @returns {Promise}
     */
    resetUserPassword(id) {
      return request.put(`/users/${id}/reset-password`)
    },
    
    /**
     * 更新用户状态（启用/禁用）
     * @param {number|string} id - 用户ID
     * @param {boolean} status - 用户状态
     * @returns {Promise}
     */
    updateUserStatus(id, status) {
      return request.put(`/users/${id}/status`, { status })
    },
    
    /**
     * 导出用户数据
     * @param {Object} params - 查询参数
     * @returns {Promise}
     */
    exportUsers(params) {
      return request.download('/users/export', params, 'users.xlsx')
    },
    
    /**
     * 导入用户数据
     * @param {FormData} formData - 包含Excel文件的表单数据
     * @returns {Promise}
     */
    importUsers(formData) {
      return request.upload('/users/import', formData)
    }
  },
  
  // 角色管理
  role: {
    /**
     * 获取角色列表
     * @param {Object} params - 查询参数
     * @returns {Promise}
     */
    getRoleList(params) {
      return request.get('/roles', params)
    },
    
    /**
     * 获取角色详情
     * @param {number|string} id - 角色ID
     * @returns {Promise}
     */
    getRoleById(id) {
      return request.get(`/roles/${id}`)
    },
    
    /**
     * 创建角色
     * @param {Object} data - 角色数据
     * @returns {Promise}
     */
    createRole(data) {
      return request.post('/roles', data)
    },
    
    /**
     * 更新角色
     * @param {number|string} id - 角色ID
     * @param {Object} data - 角色数据
     * @returns {Promise}
     */
    updateRole(id, data) {
      return request.put(`/roles/${id}`, data)
    },
    
    /**
     * 删除角色
     * @param {number|string} id - 角色ID
     * @returns {Promise}
     */
    deleteRole(id) {
      return request.delete(`/roles/${id}`)
    },
    
    /**
     * 获取角色的权限
     * @param {number|string} id - 角色ID
     * @returns {Promise}
     */
    getRolePermissions(id) {
      return request.get(`/roles/${id}/permissions`)
    },
    
    /**
     * 更新角色的权限
     * @param {number|string} id - 角色ID
     * @param {Array} permissionIds - 权限ID数组
     * @returns {Promise}
     */
    updateRolePermissions(id, permissionIds) {
      return request.put(`/roles/${id}/permissions`, { permissionIds })
    }
  },
  
  // 权限管理
  permission: {
    /**
     * 获取权限列表
     * @param {Object} params - 查询参数
     * @returns {Promise}
     */
    getPermissionList(params) {
      return request.get('/permissions', params)
    },
    
    /**
     * 获取权限树结构
     * @returns {Promise}
     */
    getPermissionTree() {
      return request.get('/permissions/tree')
    }
  },
  
  // 部门管理
  department: {
    /**
     * 获取部门列表
     * @param {Object} params - 查询参数
     * @returns {Promise}
     */
    getDepartmentList(params) {
      return request.get('/departments', params)
    },
    
    /**
     * 获取部门详情
     * @param {number|string} id - 部门ID
     * @returns {Promise}
     */
    getDepartmentById(id) {
      return request.get(`/departments/${id}`)
    },
    
    /**
     * 创建部门
     * @param {Object} data - 部门数据
     * @returns {Promise}
     */
    createDepartment(data) {
      return request.post('/departments', data)
    },
    
    /**
     * 更新部门
     * @param {number|string} id - 部门ID
     * @param {Object} data - 部门数据
     * @returns {Promise}
     */
    updateDepartment(id, data) {
      return request.put(`/departments/${id}`, data)
    },
    
    /**
     * 删除部门
     * @param {number|string} id - 部门ID
     * @returns {Promise}
     */
    deleteDepartment(id) {
      return request.delete(`/departments/${id}`)
    },
    
    /**
     * 获取部门树结构
     * @returns {Promise}
     */
    getDepartmentTree() {
      return request.get('/departments/tree')
    },
    
    /**
     * 获取部门用户
     * @param {number|string} id - 部门ID
     * @param {Object} params - 查询参数
     * @returns {Promise}
     */
    getDepartmentUsers(id, params) {
      return request.get(`/departments/${id}/users`, params)
    }
  },
  
  // 日志管理
  log: {
    /**
     * 获取操作日志
     * @param {Object} params - 查询参数
     * @returns {Promise}
     */
    getOperationLogs(params) {
      return request.get('/logs/operation', params)
    },
    
    /**
     * 获取登录日志
     * @param {Object} params - 查询参数
     * @returns {Promise}
     */
    getLoginLogs(params) {
      return request.get('/logs/login', params)
    },
    
    /**
     * 导出操作日志
     * @param {Object} params - 查询参数
     * @returns {Promise}
     */
    exportOperationLogs(params) {
      return request.download('/logs/operation/export', params, 'operation-logs.xlsx')
    },
    
    /**
     * 导出登录日志
     * @param {Object} params - 查询参数
     * @returns {Promise}
     */
    exportLoginLogs(params) {
      return request.download('/logs/login/export', params, 'login-logs.xlsx')
    }
  },
  
  // 系统设置
  setting: {
    /**
     * 获取系统设置
     * @returns {Promise}
     */
    getSystemSettings() {
      return request.get('/settings')
    },
    
    /**
     * 更新系统设置
     * @param {Object} data - 系统设置数据
     * @returns {Promise}
     */
    updateSystemSettings(data) {
      return request.put('/settings', data)
    },
    
    /**
     * 获取系统数据字典
     * @param {Object} params - 查询参数
     * @returns {Promise}
     */
    getDictionaries(params) {
      return request.get('/dictionaries', params)
    },
    
    /**
     * 获取数据字典详情
     * @param {string} type - 字典类型
     * @returns {Promise}
     */
    getDictionaryByType(type) {
      return request.get(`/dictionaries/${type}`)
    },
    
    /**
     * 创建数据字典
     * @param {Object} data - 数据字典数据
     * @returns {Promise}
     */
    createDictionary(data) {
      return request.post('/dictionaries', data)
    },
    
    /**
     * 更新数据字典
     * @param {string} type - 字典类型
     * @param {Object} data - 数据字典数据
     * @returns {Promise}
     */
    updateDictionary(type, data) {
      return request.put(`/dictionaries/${type}`, data)
    },
    
    /**
     * 删除数据字典
     * @param {string} type - 字典类型
     * @returns {Promise}
     */
    deleteDictionary(type) {
      return request.delete(`/dictionaries/${type}`)
    }
  }
} 