import{_ as e,r as a,ad as l,z as t,b as o,d as r,e as d,w as s,E as u,a as n,m as c,o as i,p,f as m,aG as g,D as v,b7 as f,l as b,k as h,t as y,$ as w,a9 as _}from"./index-CG5lHOPO.js";import{getSuppliers as V,deleteSupplier as x,createSupplier as P,updateSupplier as T}from"./spareparts-DKUrs8IX.js";const A={class:"supplier-list-view"},U={class:"page-header"},z={class:"pagination-container"},C={class:"dialog-footer"},k=e({__name:"SupplierListView",setup(e){const k=a(!1),E=a([]),S=a(0),I=a(!1),j=a("create"),D=a({}),q=a(),O=l({name:"",code:"",supplierType:1,contactPerson:"",contactPhone:"",contactEmail:"",address:"",notes:"",isActive:!0}),B={name:[{required:!0,message:"请输入供应商名称",trigger:"blur"},{max:100,message:"供应商名称长度不能超过100个字符",trigger:"blur"}],code:[{required:!0,message:"请输入供应商编码",trigger:"blur"},{max:50,message:"供应商编码长度不能超过50个字符",trigger:"blur"}],supplierType:[{required:!0,message:"请选择供应商类型",trigger:"change"}],contactPerson:[{max:50,message:"联系人长度不能超过50个字符",trigger:"blur"}],contactPhone:[{max:20,message:"联系电话长度不能超过20个字符",trigger:"blur"}],contactEmail:[{type:"email",message:"请输入正确的邮箱格式",trigger:"blur"},{max:100,message:"联系邮箱长度不能超过100个字符",trigger:"blur"}],address:[{max:200,message:"地址长度不能超过200个字符",trigger:"blur"}],notes:[{max:500,message:"备注长度不能超过500个字符",trigger:"blur"}]},L=l({pageIndex:1,pageSize:10,name:"",code:"",supplierType:null,isActive:null}),$=async()=>{k.value=!0;try{const e=await V(L);e.success?(E.value=e.data.items||[],S.value=e.data.totalCount||0):u.error(e.message||"获取供应商列表失败")}catch(e){u.error("获取供应商列表失败")}finally{k.value=!1}},G=()=>{L.pageIndex=1,$()},N=()=>{Object.assign(L,{pageIndex:1,pageSize:10,name:"",code:"",supplierType:null,isActive:null}),$()},R=()=>{j.value="create",M(),I.value=!0},F=e=>{L.pageSize=e,L.pageIndex=1,$()},H=e=>{L.pageIndex=e,$()},J=async()=>{if(q.value)try{let e;await q.value.validate(),e="create"===j.value?await P(O):await T(D.value.id,O),e.success?(u.success("create"===j.value?"创建成功":"更新成功"),I.value=!1,$()):u.error(e.message||"操作失败")}catch(e){u.error("操作失败")}},K=()=>{M()},M=()=>{Object.assign(O,{name:"",code:"",supplierType:1,contactPerson:"",contactPhone:"",contactEmail:"",address:"",notes:"",isActive:!0}),q.value&&q.value.clearValidate()},Q=e=>{switch(e){case 1:return"primary";case 2:return"success";case 3:return"warning";default:return"info"}};return t((()=>{$()})),(e,a)=>{const l=n("el-icon"),t=n("el-button"),V=n("el-input"),P=n("el-form-item"),T=n("el-option"),M=n("el-select"),W=n("el-form"),X=n("el-card"),Y=n("el-table-column"),Z=n("el-tag"),ee=n("el-table"),ae=n("el-pagination"),le=n("el-switch"),te=n("el-dialog"),oe=c("loading");return i(),o("div",A,[r("div",U,[a[18]||(a[18]=r("h2",null,"供应商管理",-1)),d(t,{type:"primary",onClick:R},{default:s((()=>[d(l,null,{default:s((()=>[d(m(g))])),_:1}),a[17]||(a[17]=p(" 新增供应商 "))])),_:1})]),d(X,{class:"search-card",shadow:"never"},{default:s((()=>[d(W,{model:L,inline:""},{default:s((()=>[d(P,{label:"供应商名称"},{default:s((()=>[d(V,{modelValue:L.name,"onUpdate:modelValue":a[0]||(a[0]=e=>L.name=e),placeholder:"请输入供应商名称",clearable:"",style:{width:"200px"}},null,8,["modelValue"])])),_:1}),d(P,{label:"供应商编码"},{default:s((()=>[d(V,{modelValue:L.code,"onUpdate:modelValue":a[1]||(a[1]=e=>L.code=e),placeholder:"请输入供应商编码",clearable:"",style:{width:"200px"}},null,8,["modelValue"])])),_:1}),d(P,{label:"供应商类型"},{default:s((()=>[d(M,{modelValue:L.supplierType,"onUpdate:modelValue":a[2]||(a[2]=e=>L.supplierType=e),placeholder:"请选择供应商类型",clearable:"",style:{width:"180px"}},{default:s((()=>[d(T,{label:"采购供应商",value:1}),d(T,{label:"维修供应商",value:2}),d(T,{label:"采购+维修供应商",value:3})])),_:1},8,["modelValue"])])),_:1}),d(P,{label:"状态"},{default:s((()=>[d(M,{modelValue:L.isActive,"onUpdate:modelValue":a[3]||(a[3]=e=>L.isActive=e),placeholder:"请选择状态",clearable:"",style:{width:"120px"}},{default:s((()=>[d(T,{label:"激活",value:!0}),d(T,{label:"停用",value:!1})])),_:1},8,["modelValue"])])),_:1}),d(P,null,{default:s((()=>[d(t,{type:"primary",onClick:G},{default:s((()=>[d(l,null,{default:s((()=>[d(m(v))])),_:1}),a[19]||(a[19]=p(" 搜索 "))])),_:1}),d(t,{onClick:N},{default:s((()=>[d(l,null,{default:s((()=>[d(m(f))])),_:1}),a[20]||(a[20]=p(" 重置 "))])),_:1})])),_:1})])),_:1},8,["model"])])),_:1}),d(X,{class:"table-card",shadow:"never"},{default:s((()=>[b((i(),h(ee,{data:E.value,border:"",style:{width:"100%"}},{default:s((()=>[d(Y,{prop:"code",label:"供应商编码",width:"120"}),d(Y,{prop:"name",label:"供应商名称",width:"200"}),d(Y,{prop:"supplierTypeDisplay",label:"供应商类型",width:"150"},{default:s((e=>[d(Z,{type:Q(e.row.supplierType)},{default:s((()=>[p(y(e.row.supplierTypeDisplay),1)])),_:2},1032,["type"])])),_:1}),d(Y,{prop:"contactPerson",label:"联系人",width:"120"}),d(Y,{prop:"contactPhone",label:"联系电话",width:"140"}),d(Y,{prop:"contactEmail",label:"联系邮箱",width:"180"}),d(Y,{prop:"address",label:"地址","min-width":"200","show-overflow-tooltip":""}),d(Y,{prop:"isActive",label:"状态",width:"80"},{default:s((e=>[d(Z,{type:e.row.isActive?"success":"danger"},{default:s((()=>[p(y(e.row.isActive?"激活":"停用"),1)])),_:2},1032,["type"])])),_:1}),d(Y,{prop:"createdAt",label:"创建时间",width:"160"},{default:s((e=>{return[p(y((a=e.row.createdAt,new Date(a).toLocaleString("zh-CN"))),1)];var a})),_:1}),d(Y,{label:"操作",width:"180",fixed:"right"},{default:s((e=>[d(t,{type:"primary",size:"small",onClick:a=>{return l=e.row,j.value="edit",D.value={...l},Object.assign(O,{name:l.name,code:l.code,supplierType:l.supplierType,contactPerson:l.contactPerson||"",contactPhone:l.contactPhone||"",contactEmail:l.contactEmail||"",address:l.address||"",notes:l.notes||"",isActive:l.isActive}),void(I.value=!0);var l}},{default:s((()=>a[21]||(a[21]=[p(" 编辑 ")]))),_:2},1032,["onClick"]),d(t,{type:"danger",size:"small",onClick:a=>(async e=>{try{await _.confirm(`确定要删除供应商"${e.name}"吗？`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const a=await x(e.id);a.success?(u.success("删除成功"),$()):u.error(a.message||"删除失败")}catch(a){"cancel"!==a&&u.error("删除失败")}})(e.row)},{default:s((()=>a[22]||(a[22]=[p(" 删除 ")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"])),[[oe,k.value]]),r("div",z,[d(ae,{"current-page":L.pageIndex,"onUpdate:currentPage":a[4]||(a[4]=e=>L.pageIndex=e),"page-size":L.pageSize,"onUpdate:pageSize":a[5]||(a[5]=e=>L.pageSize=e),total:S.value,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:F,onCurrentChange:H},null,8,["current-page","page-size","total"])])])),_:1}),d(te,{modelValue:I.value,"onUpdate:modelValue":a[16]||(a[16]=e=>I.value=e),title:"create"===j.value?"新增供应商":"编辑供应商",width:"600px",onClose:K},{footer:s((()=>[r("div",C,[d(t,{onClick:a[15]||(a[15]=e=>I.value=!1)},{default:s((()=>a[23]||(a[23]=[p("取消")]))),_:1}),d(t,{type:"primary",onClick:J},{default:s((()=>a[24]||(a[24]=[p("确定")]))),_:1})])])),default:s((()=>[d(W,{ref_key:"formRef",ref:q,model:O,rules:B,"label-width":"100px"},{default:s((()=>[d(P,{label:"供应商名称",prop:"name"},{default:s((()=>[d(V,{modelValue:O.name,"onUpdate:modelValue":a[6]||(a[6]=e=>O.name=e),placeholder:"请输入供应商名称"},null,8,["modelValue"])])),_:1}),d(P,{label:"供应商编码",prop:"code"},{default:s((()=>[d(V,{modelValue:O.code,"onUpdate:modelValue":a[7]||(a[7]=e=>O.code=e),placeholder:"请输入供应商编码"},null,8,["modelValue"])])),_:1}),d(P,{label:"供应商类型",prop:"supplierType"},{default:s((()=>[d(M,{modelValue:O.supplierType,"onUpdate:modelValue":a[8]||(a[8]=e=>O.supplierType=e),placeholder:"请选择供应商类型",style:{width:"100%"}},{default:s((()=>[d(T,{label:"采购供应商",value:1}),d(T,{label:"维修供应商",value:2}),d(T,{label:"采购+维修供应商",value:3})])),_:1},8,["modelValue"])])),_:1}),d(P,{label:"联系人",prop:"contactPerson"},{default:s((()=>[d(V,{modelValue:O.contactPerson,"onUpdate:modelValue":a[9]||(a[9]=e=>O.contactPerson=e),placeholder:"请输入联系人"},null,8,["modelValue"])])),_:1}),d(P,{label:"联系电话",prop:"contactPhone"},{default:s((()=>[d(V,{modelValue:O.contactPhone,"onUpdate:modelValue":a[10]||(a[10]=e=>O.contactPhone=e),placeholder:"请输入联系电话"},null,8,["modelValue"])])),_:1}),d(P,{label:"联系邮箱",prop:"contactEmail"},{default:s((()=>[d(V,{modelValue:O.contactEmail,"onUpdate:modelValue":a[11]||(a[11]=e=>O.contactEmail=e),placeholder:"请输入联系邮箱"},null,8,["modelValue"])])),_:1}),d(P,{label:"地址",prop:"address"},{default:s((()=>[d(V,{modelValue:O.address,"onUpdate:modelValue":a[12]||(a[12]=e=>O.address=e),type:"textarea",rows:3,placeholder:"请输入地址"},null,8,["modelValue"])])),_:1}),d(P,{label:"备注",prop:"notes"},{default:s((()=>[d(V,{modelValue:O.notes,"onUpdate:modelValue":a[13]||(a[13]=e=>O.notes=e),type:"textarea",rows:3,placeholder:"请输入备注"},null,8,["modelValue"])])),_:1}),"edit"===j.value?(i(),h(P,{key:0,label:"状态",prop:"isActive"},{default:s((()=>[d(le,{modelValue:O.isActive,"onUpdate:modelValue":a[14]||(a[14]=e=>O.isActive=e),"active-text":"激活","inactive-text":"停用"},null,8,["modelValue"])])),_:1})):w("",!0)])),_:1},8,["model"])])),_:1},8,["modelValue","title"])])}}},[["__scopeId","data-v-20ac90fc"]]);export{k as default};
