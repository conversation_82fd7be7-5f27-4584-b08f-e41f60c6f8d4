// IT资产管理系统 - 数据导出API
// 文件路径: /Api/Export/ExportDataEndpoint.cs
// 功能: 提供数据导出的API端点

using System;
using System.IO;
using System.Threading.Tasks;
using ItAssetsSystem.Core.Export;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Linq;
using ItAssetsSystem.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using ItAssetsSystem.Models.Entities;

namespace ItAssetsSystem.Api.Export
{
    [ApiController]
    [Route("api/export")]
    public class ExportDataEndpoint : ControllerBase
    {
        private readonly IExportService _exportService;
        private readonly AppDbContext _dbContext;
        private readonly ILogger<ExportDataEndpoint> _logger;

        public ExportDataEndpoint(
            IExportService exportService,
            AppDbContext dbContext,
            ILogger<ExportDataEndpoint> logger)
        {
            _exportService = exportService;
            _dbContext = dbContext;
            _logger = logger;
        }

        /// <summary>
        /// 导出请求
        /// </summary>
        public class ExportRequest
        {
            /// <summary>
            /// 实体类型
            /// </summary>
            public string EntityType { get; set; }
            
            /// <summary>
            /// 导出格式
            /// </summary>
            public ExportFormat Format { get; set; }
            
            /// <summary>
            /// 文件名
            /// </summary>
            public string FileName { get; set; }
            
            /// <summary>
            /// 是否包含表头
            /// </summary>
            public bool IncludeHeaders { get; set; } = true;
            
            /// <summary>
            /// 过滤条件（JSON格式）
            /// </summary>
            public string Filter { get; set; }
            
            /// <summary>
            /// 列映射（键为属性名，值为显示名称）
            /// </summary>
            public Dictionary<string, string> Columns { get; set; }
        }

        /// <summary>
        /// 获取支持的导出格式
        /// </summary>
        [HttpGet("formats")]
        public ActionResult<List<ExportFormat>> GetSupportedFormats()
        {
            try
            {
                var formats = _exportService.GetSupportedFormats();
                return Ok(formats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取支持的导出格式失败");
                return StatusCode(500, "获取支持的导出格式失败");
            }
        }

        /// <summary>
        /// 获取支持导出的实体类型
        /// </summary>
        [HttpGet("entities")]
        public ActionResult<List<string>> GetExportableEntities()
        {
            try
            {
                // 返回可导出的实体类型列表
                var entities = new List<string>
                {
                    "Assets",
                    "AssetTypes",
                    "Locations",
                    "FaultRecords",
                    "FaultTypes",
                    "Departments",
                    "Users",
                    "Roles"
                };
                
                return Ok(entities);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取可导出实体列表失败");
                return StatusCode(500, "获取可导出实体列表失败");
            }
        }

        /// <summary>
        /// 导出数据
        /// </summary>
        [HttpPost]
        public async Task<IActionResult> ExportAsync([FromBody] ExportRequest request)
        {
            _logger.LogInformation("开始导出数据，类型: {EntityType}, 格式: {Format}", 
                request.EntityType, request.Format);
            
            try
            {
                // 获取文件名
                string fileName = string.IsNullOrEmpty(request.FileName)
                    ? $"{request.EntityType}_{DateTime.Now:yyyyMMdd}.{GetFileExtension(request.Format)}"
                    : request.FileName;
                
                // 导出选项
                var options = new ExportOptions
                {
                    Format = request.Format,
                    FileName = fileName,
                    IncludeHeaders = request.IncludeHeaders,
                    Columns = request.Columns
                };
                
                // 根据实体类型获取数据
                var data = await GetDataForEntityAsync(request.EntityType, request.Filter);
                
                // 导出数据
                var stream = await _exportService.ExportDataAsync(data, options);
                
                // 设置响应头
                string contentType = GetContentType(request.Format);
                Response.Headers.Add("Content-Disposition", $"attachment; filename={fileName}");
                
                // 返回文件流
                return File(stream.ToArray(), contentType, fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "导出数据失败，类型: {EntityType}, 格式: {Format}", 
                    request.EntityType, request.Format);
                
                return StatusCode(500, $"导出数据失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取实体的数据
        /// </summary>
        private async Task<IEnumerable<object>> GetDataForEntityAsync(string entityType, string filter)
        {
            // 根据实体类型返回相应的数据
            return entityType.ToLower() switch
            {
                "assets" => await _dbContext.Assets.ToListAsync(),
                "assettypes" => await _dbContext.AssetTypes.ToListAsync(),
                "locations" => await _dbContext.Locations.ToListAsync(),
                "faultrecords" => await _dbContext.FaultRecords.ToListAsync(),
                "faulttypes" => await _dbContext.FaultTypes.ToListAsync(),
                "departments" => await _dbContext.Departments.ToListAsync(),
                "users" => await _dbContext.Users.ToListAsync(),
                "roles" => await _dbContext.Roles.ToListAsync(),
                _ => throw new NotSupportedException($"不支持的实体类型: {entityType}")
            };
        }

        /// <summary>
        /// 获取内容类型
        /// </summary>
        private string GetContentType(ExportFormat format)
        {
            return format switch
            {
                ExportFormat.Csv => "text/csv",
                ExportFormat.Excel => "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                ExportFormat.Pdf => "application/pdf",
                ExportFormat.Json => "application/json",
                _ => "application/octet-stream"
            };
        }

        /// <summary>
        /// 获取文件扩展名
        /// </summary>
        private string GetFileExtension(ExportFormat format)
        {
            return format switch
            {
                ExportFormat.Csv => "csv",
                ExportFormat.Excel => "xlsx",
                ExportFormat.Pdf => "pdf",
                ExportFormat.Json => "json",
                _ => "txt"
            };
        }
    }
}

// 计划行数: 100
// 实际行数: 96 