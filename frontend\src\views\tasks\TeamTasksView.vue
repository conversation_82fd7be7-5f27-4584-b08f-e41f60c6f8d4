<template>
  <div class="team-tasks-container">
    <el-card class="task-card">
      <template #header>
        <div class="card-header">
          <h2>团队任务</h2>
          <div class="header-actions">
            <el-button type="primary" @click="refreshData">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
            <el-button type="success" @click="createTask">
              <el-icon><Plus /></el-icon>
              新建任务
            </el-button>
          </div>
        </div>
      </template>
      
      <div class="filter-container">
        <el-form :inline="true" :model="filterForm" class="filter-form">
          <el-form-item label="状态">
            <el-select v-model="filterForm.status" placeholder="任务状态" clearable>
              <el-option label="待处理" value="pending" />
              <el-option label="进行中" value="in_progress" />
              <el-option label="已完成" value="completed" />
              <el-option label="已取消" value="cancelled" />
            </el-select>
          </el-form-item>
          <el-form-item label="优先级">
            <el-select v-model="filterForm.priority" placeholder="优先级" clearable>
              <el-option label="低" value="low" />
              <el-option label="中" value="medium" />
              <el-option label="高" value="high" />
              <el-option label="紧急" value="urgent" />
            </el-select>
          </el-form-item>
          <el-form-item label="负责人">
            <el-select v-model="filterForm.assigneeUserId" placeholder="选择负责人" clearable filterable>
              <el-option 
                v-for="user in teamMembers"
                :key="user.id"
                :label="user.name"
                :value="user.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleFilter">筛选</el-button>
            <el-button @click="resetFilter">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      
      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="10" animated />
      </div>
      
      <div v-else-if="tasks.length === 0" class="empty-data">
        <el-empty description="暂无任务数据" />
      </div>
      
      <div v-else>
        <el-table
          :data="tasks"
          style="width: 100%"
          @row-click="handleRowClick"
          v-loading="tableLoading"
        >
          <el-table-column prop="taskId" label="ID" width="70" />
          <el-table-column prop="title" label="任务名称" min-width="200" show-overflow-tooltip />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag :type="getStatusType(scope.row.status)">{{ getStatusText(scope.row.status) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="priority" label="优先级" width="100">
            <template #default="scope">
              <el-tag :type="getPriorityType(scope.row.priority)" effect="dark">{{ getPriorityText(scope.row.priority) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="dueDate" label="截止日期" width="120" />
          <el-table-column prop="progress" label="进度" width="180">
            <template #default="scope">
              <el-progress :percentage="scope.row.progress || 0" :status="getProgressStatus(scope.row)" />
            </template>
          </el-table-column>
          <el-table-column prop="assigneeName" label="负责人" width="120">
            <template #default="scope">
              <el-tag type="info" v-if="!scope.row.assigneeName">未分配</el-tag>
              <span v-else>{{ scope.row.assigneeName }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="220" fixed="right">
            <template #default="scope">
              <el-button
                size="small"
                type="primary"
                @click.stop="viewTaskDetail(scope.row)"
              >
                详情
              </el-button>
              <el-button
                size="small"
                type="success"
                v-if="!scope.row.assigneeUserId"
                @click.stop="assignTask(scope.row)"
              >
                分配
              </el-button>
              <el-button
                size="small"
                type="warning"
                v-if="scope.row.status !== 'completed' && scope.row.status !== 'cancelled'"
                @click.stop="updateStatus(scope.row)"
              >
                更新状态
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <div class="pagination-container">
          <el-pagination
            background
            layout="total, sizes, prev, pager, next"
            :page-sizes="[10, 20, 50, 100]"
            :total="total"
            :page-size="pageSize"
            :current-page="currentPage"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-card>
    
    <!-- 分配任务对话框 -->
    <el-dialog
      v-model="assignDialogVisible"
      title="分配任务"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form :model="assignForm" label-width="100px">
        <el-form-item label="任务名称">
          <span>{{ assignForm.taskTitle }}</span>
        </el-form-item>
        <el-form-item label="负责人">
          <el-select v-model="assignForm.assigneeUserId" placeholder="选择负责人" filterable>
            <el-option 
              v-for="user in teamMembers"
              :key="user.id"
              :label="user.name"
              :value="user.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="assignForm.remarks"
            type="textarea"
            :rows="3"
            placeholder="请输入分配备注"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="assignDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitAssign" :loading="submitting">
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 更新状态对话框 -->
    <el-dialog
      v-model="statusDialogVisible"
      title="更新任务状态"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form :model="statusForm" label-width="100px">
        <el-form-item label="任务名称">
          <span>{{ statusForm.taskTitle }}</span>
        </el-form-item>
        <el-form-item label="当前状态">
          <el-tag :type="getStatusType(statusForm.currentStatus)">{{ getStatusText(statusForm.currentStatus) }}</el-tag>
        </el-form-item>
        <el-form-item label="新状态">
          <el-select v-model="statusForm.newStatus" placeholder="选择新状态">
            <el-option label="待处理" value="pending" />
            <el-option label="进行中" value="in_progress" />
            <el-option label="已完成" value="completed" />
            <el-option label="已取消" value="cancelled" />
          </el-select>
        </el-form-item>
        <el-form-item label="进度" v-if="statusForm.newStatus === 'completed'">
          <el-input-number v-model="statusForm.progress" :min="0" :max="100" :step="5" />
          <span class="progress-unit">%</span>
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="statusForm.remarks"
            type="textarea"
            :rows="3"
            placeholder="请输入状态更新备注"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="statusDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitStatusUpdate" :loading="submitting">
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { taskApi } from '@/api/task'
import userApi from '@/api/user' // 假设有用户API用于获取团队成员

export default {
  name: 'TeamTasksView',
  
  setup() {
    const router = useRouter()
    const tasks = ref([])
    const loading = ref(true)
    const tableLoading = ref(false)
    const total = ref(0)
    const currentPage = ref(1)
    const pageSize = ref(10)
    const teamMembers = ref([])
    
    // 筛选表单
    const filterForm = reactive({
      status: '',
      priority: '',
      assigneeUserId: null
    })
    
    // 分配任务相关
    const assignDialogVisible = ref(false)
    const assignForm = reactive({
      taskId: null,
      taskTitle: '',
      assigneeUserId: null,
      remarks: ''
    })
    
    // 更新状态相关
    const statusDialogVisible = ref(false)
    const statusForm = reactive({
      taskId: null,
      taskTitle: '',
      currentStatus: '',
      newStatus: '',
      progress: 100,
      remarks: ''
    })
    
    const submitting = ref(false)
    
    // 加载任务数据
    const loadTasks = async () => {
      loading.value = true
      try {
        const params = {
          pageIndex: currentPage.value,
          pageSize: pageSize.value,
          teamView: true // 假设后端API支持团队视图参数
        }
        
        // 添加筛选条件
        if (filterForm.status) {
          params.status = filterForm.status
        }
        if (filterForm.priority) {
          params.priority = filterForm.priority
        }
        if (filterForm.assigneeUserId) {
          params.assigneeUserId = filterForm.assigneeUserId
        }
        
        const response = await taskApi.getTasks(params)
        if (response.success) {
          tasks.value = response.data.items
          total.value = response.data.totalCount
        } else {
          ElMessage.error(response.message || '加载任务数据失败')
        }
      } catch (error) {
        console.error('加载任务数据出错:', error)
        ElMessage.error('加载任务数据时发生错误')
      } finally {
        loading.value = false
      }
    }
    
    // 加载团队成员
    const loadTeamMembers = async () => {
      try {
        const response = await userApi.getTeamMembers()
        if (response.success) {
          teamMembers.value = response.data
        } else {
          ElMessage.warning(response.message || '加载团队成员失败')
        }
      } catch (error) {
        console.error('加载团队成员出错:', error)
        ElMessage.warning('加载团队成员时发生错误')
      }
    }
    
    // 刷新数据
    const refreshData = () => {
      loadTasks()
    }
    
    // 处理筛选
    const handleFilter = () => {
      currentPage.value = 1
      loadTasks()
    }
    
    // 重置筛选
    const resetFilter = () => {
      filterForm.status = ''
      filterForm.priority = ''
      filterForm.assigneeUserId = null
      currentPage.value = 1
      loadTasks()
    }
    
    // 处理页面大小变化
    const handleSizeChange = (val) => {
      pageSize.value = val
      loadTasks()
    }
    
    // 处理页码变化
    const handleCurrentChange = (val) => {
      currentPage.value = val
      loadTasks()
    }
    
    // 获取状态类型对应的Element UI标签类型
    const getStatusType = (status) => {
      const statusMap = {
        'pending': 'info',
        'in_progress': 'warning',
        'completed': 'success',
        'cancelled': 'danger'
      }
      return statusMap[status] || 'info'
    }
    
    // 获取状态文本
    const getStatusText = (status) => {
      const statusTextMap = {
        'pending': '待处理',
        'in_progress': '进行中',
        'completed': '已完成',
        'cancelled': '已取消'
      }
      return statusTextMap[status] || status
    }
    
    // 获取优先级类型对应的Element UI标签类型
    const getPriorityType = (priority) => {
      const priorityMap = {
        'low': 'info',
        'medium': 'success',
        'high': 'warning',
        'urgent': 'danger'
      }
      return priorityMap[priority] || 'info'
    }
    
    // 获取优先级文本
    const getPriorityText = (priority) => {
      const priorityTextMap = {
        'low': '低',
        'medium': '中',
        'high': '高',
        'urgent': '紧急'
      }
      return priorityTextMap[priority] || priority
    }
    
    // 获取进度条状态
    const getProgressStatus = (task) => {
      if (task.status === 'completed') return 'success'
      if (task.status === 'cancelled') return 'exception'
      
      // 检查是否超期
      if (task.dueDate) {
        const now = new Date()
        const dueDate = new Date(task.dueDate)
        if (now > dueDate && task.progress < 100) {
          return 'exception'
        }
      }
      
      return ''
    }
    
    // 处理行点击
    const handleRowClick = (row) => {
      viewTaskDetail(row)
    }
    
    // 查看任务详情
    const viewTaskDetail = (task) => {
      router.push({
        name: 'TaskDetail',
        params: { id: task.taskId }
      })
    }
    
    // 创建任务
    const createTask = () => {
      router.push({ name: 'TaskCreate' })
    }
    
    // 分配任务
    const assignTask = (task) => {
      assignForm.taskId = task.taskId
      assignForm.taskTitle = task.title
      assignForm.assigneeUserId = task.assigneeUserId
      assignForm.remarks = ''
      assignDialogVisible.value = true
    }
    
    // 提交分配
    const submitAssign = async () => {
      if (!assignForm.taskId || !assignForm.assigneeUserId) {
        ElMessage.warning('请选择负责人')
        return
      }
      
      submitting.value = true
      try {
        const response = await taskApi.assignTask(
          assignForm.taskId,
          {
            assigneeUserId: assignForm.assigneeUserId,
            remarks: assignForm.remarks
          }
        )
        
        if (response.success) {
          ElMessage.success('任务分配成功')
          assignDialogVisible.value = false
          loadTasks()
        } else {
          ElMessage.error(response.message || '任务分配失败')
        }
      } catch (error) {
        console.error('任务分配出错:', error)
        ElMessage.error('任务分配时发生错误')
      } finally {
        submitting.value = false
      }
    }
    
    // 更新状态
    const updateStatus = (task) => {
      statusForm.taskId = task.taskId
      statusForm.taskTitle = task.title
      statusForm.currentStatus = task.status
      statusForm.newStatus = task.status
      statusForm.progress = task.progress || 0
      statusForm.remarks = ''
      statusDialogVisible.value = true
    }
    
    // 提交状态更新
    const submitStatusUpdate = async () => {
      if (!statusForm.taskId || !statusForm.newStatus) {
        ElMessage.warning('请选择新状态')
        return
      }
      
      submitting.value = true
      try {
        // 更新状态
        const statusResponse = await taskApi.updateTaskStatus(
          statusForm.taskId,
          {
            status: statusForm.newStatus,
            remarks: statusForm.remarks
          }
        )
        
        if (statusResponse.success) {
          // 如果状态更新为已完成，同时更新进度为100%
          if (statusForm.newStatus === 'completed') {
            await taskApi.updateTaskProgress(
              statusForm.taskId,
              {
                progress: 100,
                remarks: '任务已完成，进度自动更新为100%'
              }
            )
          }
          
          ElMessage.success('任务状态更新成功')
          statusDialogVisible.value = false
          loadTasks()
        } else {
          ElMessage.error(statusResponse.message || '任务状态更新失败')
        }
      } catch (error) {
        console.error('任务状态更新出错:', error)
        ElMessage.error('任务状态更新时发生错误')
      } finally {
        submitting.value = false
      }
    }
    
    onMounted(() => {
      loadTeamMembers()
      loadTasks()
    })
    
    return {
      tasks,
      loading,
      tableLoading,
      total,
      currentPage,
      pageSize,
      teamMembers,
      filterForm,
      assignDialogVisible,
      assignForm,
      statusDialogVisible,
      statusForm,
      submitting,
      refreshData,
      handleFilter,
      resetFilter,
      handleSizeChange,
      handleCurrentChange,
      getStatusType,
      getStatusText,
      getPriorityType,
      getPriorityText,
      getProgressStatus,
      handleRowClick,
      viewTaskDetail,
      createTask,
      assignTask,
      submitAssign,
      updateStatus,
      submitStatusUpdate,
      getAllAssignees
    }
    
    // 获取所有负责人（主负责人 + 协作人员）- 修复显示逻辑
    const getAllAssignees = (task) => {
      if (!task) return []

      console.log('TeamTasksView - 原始task数据:', {
        taskId: task.taskId,
        assigneeUserId: task.assigneeUserId,
        assigneeName: task.assigneeName,
        assignees: task.assignees
      })

      const assignees = []

      // 优先使用后端返回的 assignees 字段（包含完整的负责人信息）
      if (task.assignees && Array.isArray(task.assignees) && task.assignees.length > 0) {
        task.assignees.forEach(assignee => {
          assignees.push({
            id: assignee.userId,
            name: assignee.userName || '未知用户',
            avatarUrl: assignee.avatarUrl || '',
            role: assignee.role || (assignee.assignmentType === 'Assignee' ? 'Primary' : 'Collaborator'),
            isPrimary: assignee.role === 'Primary' || assignee.assignmentType === 'Assignee'
          })
        })
      } else {
        // 如果没有 assignees 字段，回退到旧逻辑（仅显示主负责人）
        if (task.assigneeUserId) {
          assignees.push({
            id: task.assigneeUserId,
            name: task.assigneeName || '未知用户',
            avatarUrl: task.assigneeAvatarUrl || '',
            role: 'Primary',
            isPrimary: true
          })
        }
      }

      console.log('TeamTasksView - 最终assignees结果:', assignees)
      return assignees
    }
  }
}
</script>

<style scoped>
.team-tasks-container {
  padding: 20px;
}

.task-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.filter-container {
  margin-bottom: 20px;
}

.filter-form {
  display: flex;
  flex-wrap: wrap;
}

.loading-container {
  padding: 20px;
}

.empty-data {
  padding: 40px 0;
  text-align: center;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.progress-unit {
  margin-left: 5px;
}
</style> 