<template>
  <div class="qr-scanner">
    <el-dialog
      v-model="visible"
      title="扫描二维码"
      width="500px"
      :before-close="handleClose"
    >
      <div class="scanner-container">
        <div v-if="!hasCamera" class="no-camera">
          <el-icon size="48"><VideoCamera /></el-icon>
          <p>未检测到摄像头</p>
        </div>
        <div v-else-if="loading" class="loading">
          <el-icon size="48" class="is-loading"><Loading /></el-icon>
          <p>正在启动摄像头...</p>
        </div>
        <div v-else class="camera-view">
          <video ref="videoRef" autoplay playsinline></video>
          <canvas ref="canvasRef" style="display: none;"></canvas>
          <div class="scan-frame">
            <div class="corner top-left"></div>
            <div class="corner top-right"></div>
            <div class="corner bottom-left"></div>
            <div class="corner bottom-right"></div>
          </div>
        </div>
      </div>
      
      <div class="scanner-tips">
        <p>请将二维码对准扫描框</p>
        <p>支持资产码、故障类型码等</p>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleClose">取消</el-button>
          <el-button type="primary" @click="handleManualInput">手动输入</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { VideoCamera, Loading } from '@element-plus/icons-vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  scanType: {
    type: String,
    default: 'asset', // asset, faultType, deviceType
    validator: (value) => ['asset', 'faultType', 'deviceType'].includes(value)
  }
})

const emit = defineEmits(['update:modelValue', 'scan-success', 'manual-input'])

const visible = ref(false)
const loading = ref(false)
const hasCamera = ref(true)
const videoRef = ref(null)
const canvasRef = ref(null)
const stream = ref(null)
const scanInterval = ref(null)

// 监听显示状态
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
  if (newVal) {
    startCamera()
  } else {
    stopCamera()
  }
})

// 启动摄像头
const startCamera = async () => {
  loading.value = true
  
  try {
    // 检查是否支持摄像头
    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
      hasCamera.value = false
      loading.value = false
      return
    }
    
    // 获取摄像头权限
    const constraints = {
      video: {
        width: { ideal: 400 },
        height: { ideal: 300 },
        facingMode: 'environment' // 优先使用后置摄像头
      }
    }
    
    stream.value = await navigator.mediaDevices.getUserMedia(constraints)
    
    if (videoRef.value) {
      videoRef.value.srcObject = stream.value
      videoRef.value.onloadedmetadata = () => {
        loading.value = false
        startScanning()
      }
    }
  } catch (error) {
    console.error('启动摄像头失败:', error)
    hasCamera.value = false
    loading.value = false
    ElMessage.error('无法访问摄像头，请检查权限设置')
  }
}

// 停止摄像头
const stopCamera = () => {
  if (stream.value) {
    stream.value.getTracks().forEach(track => track.stop())
    stream.value = null
  }
  
  if (scanInterval.value) {
    clearInterval(scanInterval.value)
    scanInterval.value = null
  }
}

// 开始扫描
const startScanning = () => {
  if (!videoRef.value || !canvasRef.value) return
  
  scanInterval.value = setInterval(() => {
    scanQRCode()
  }, 500) // 每500ms扫描一次
}

// 扫描二维码
const scanQRCode = () => {
  if (!videoRef.value || !canvasRef.value) return
  
  const video = videoRef.value
  const canvas = canvasRef.value
  const context = canvas.getContext('2d')
  
  // 设置canvas尺寸
  canvas.width = video.videoWidth
  canvas.height = video.videoHeight
  
  // 绘制视频帧到canvas
  context.drawImage(video, 0, 0, canvas.width, canvas.height)
  
  // 获取图像数据
  const imageData = context.getImageData(0, 0, canvas.width, canvas.height)
  
  // 这里应该使用二维码解析库，比如jsQR
  // 由于没有安装jsQR，这里模拟扫描结果
  // 实际项目中需要: npm install jsqr
  // import jsQR from 'jsqr'
  // const code = jsQR(imageData.data, imageData.width, imageData.height)
  
  // 模拟扫描成功（实际项目中删除这部分）
  if (Math.random() < 0.1) { // 10%概率模拟扫描成功
    const mockData = getMockScanData()
    handleScanSuccess(mockData)
  }
}

// 获取模拟扫描数据
const getMockScanData = () => {
  switch (props.scanType) {
    case 'asset':
      return {
        type: 'asset',
        code: 'PC-2024-001',
        name: '联想台式机',
        id: 1001
      }
    case 'faultType':
      return {
        type: 'faultType',
        code: 'FT-HW-001',
        name: '硬件故障',
        id: 1
      }
    case 'deviceType':
      return {
        type: 'deviceType',
        code: 'DT-PC-001',
        name: '台式电脑',
        id: 1
      }
    default:
      return {
        type: 'unknown',
        code: 'UNKNOWN-001',
        name: '未知类型',
        id: 0
      }
  }
}

// 处理扫描成功
const handleScanSuccess = (data) => {
  ElMessage.success(`扫描成功：${data.name} (${data.code})`)
  emit('scan-success', data)
  handleClose()
}

// 关闭对话框
const handleClose = () => {
  stopCamera()
  emit('update:modelValue', false)
}

// 手动输入
const handleManualInput = () => {
  emit('manual-input', props.scanType)
  handleClose()
}

onMounted(() => {
  if (props.modelValue) {
    visible.value = true
    startCamera()
  }
})

onUnmounted(() => {
  stopCamera()
})
</script>

<style scoped>
.scanner-container {
  position: relative;
  width: 100%;
  height: 300px;
  background: #f5f5f5;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.no-camera,
.loading {
  text-align: center;
  color: #999;
}

.no-camera p,
.loading p {
  margin-top: 16px;
  font-size: 14px;
}

.camera-view {
  position: relative;
  width: 100%;
  height: 100%;
}

.camera-view video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

.scan-frame {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 200px;
  height: 200px;
  border: 2px solid transparent;
}

.corner {
  position: absolute;
  width: 20px;
  height: 20px;
  border: 3px solid #409eff;
}

.corner.top-left {
  top: 0;
  left: 0;
  border-right: none;
  border-bottom: none;
}

.corner.top-right {
  top: 0;
  right: 0;
  border-left: none;
  border-bottom: none;
}

.corner.bottom-left {
  bottom: 0;
  left: 0;
  border-right: none;
  border-top: none;
}

.corner.bottom-right {
  bottom: 0;
  right: 0;
  border-left: none;
  border-top: none;
}

.scanner-tips {
  margin-top: 16px;
  text-align: center;
  color: #666;
  font-size: 14px;
}

.scanner-tips p {
  margin: 4px 0;
}

.dialog-footer {
  text-align: right;
}
</style>
