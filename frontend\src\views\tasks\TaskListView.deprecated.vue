<!--
  本文件已废弃（deprecated），请使用 EnhancedTaskListView.vue 替代。
  如需恢复请联系前端负责人。
-->
<template>
  <div class="simple-task-list page-container">
    <h2 class="page-title">任务列表</h2>

    <!-- 过滤器区域 -->
    <el-card shadow="never" class="filter-card mb-4">
      <div class="filters">
        <el-select v-model="filters.status" placeholder="状态" clearable style="width: 120px;" class="mr-2">
          <el-option label="未开始" value="todo"></el-option>
          <el-option label="进行中" value="doing"></el-option>
          <el-option label="已完成" value="done"></el-option>
          <el-option label="已逾期" value="overdue"></el-option>
        </el-select>
        <el-select v-model="filters.priority" placeholder="优先级" clearable style="width: 100px;" class="mr-2">
          <el-option label="高" :value="3"></el-option>
          <el-option label="中" :value="2"></el-option>
          <el-option label="低" :value="1"></el-option>
        </el-select>
        <el-select
          v-model="filters.assigneeId"
          placeholder="负责人"
          clearable
          filterable
          remote
          :remote-method="searchUsers"
          :loading="userSearchLoading"
          style="width: 130px;"
          class="mr-2"
        >
          <el-option
            v-for="user in userOptions"
            :key="user.id"
            :label="user.name"
            :value="user.id"
          ></el-option>
        </el-select>
        <el-input
          v-model="filters.title"
          placeholder="搜索标题..."
          clearable
          style="width: 180px;"
          class="mr-2"
          @keyup.enter="handleSearch"
        />
        <el-button type="primary" :icon="Search" @click="handleSearch">搜索</el-button>
        <el-button :icon="Refresh" @click="resetFilters">重置</el-button>
      </div>
      <div class="actions mt-4">
        <el-button type="primary" :icon="Plus" @click="openTaskForm()">新增任务</el-button>
      </div>
    </el-card>

    <!-- 任务表格 -->
    <el-card shadow="never" class="task-table-card">
       <el-table
        :data="pagedTasks"
        style="width: 100%"
        v-loading="loading"
        row-key="id"
        :header-cell-style="{ background: '#f8f9fa', color: '#303133' }"
      >
        <el-table-column label="序号" width="60" align="center">
          <template #default="scope">
            {{ (pagination.currentPage - 1) * pagination.pageSize + scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="process" label="工序" width="120">
          <template #default="scope">
            <!-- 占位符 -->
            {{ scope.row.process || '软件配置' }}
          </template>
        </el-table-column>
         <el-table-column prop="projectPhase" label="项目阶段" width="120">
          <template #default="scope">
            <!-- 占位符 -->
            {{ scope.row.projectPhase || '硬件组装' }}
          </template>
        </el-table-column>
        <el-table-column prop="title" label="问题事项" min-width="200" show-overflow-tooltip>
           <template #default="scope">
            <span class="task-title-link" @click="openTaskForm(scope.row)">{{ scope.row.title }}</span>
           </template>
        </el-table-column>
        <el-table-column label="负责人" width="120">
          <template #default="scope">
             <div v-if="scope.row.assignee" class="user-cell">
              <el-avatar :size="24" :src="getFullAvatarUrl(scope.row.assignee.avatar) || defaultAvatar" class="table-avatar" />
              <span>{{ scope.row.assignee.name }}</span>
            </div>
             <span v-else>未分配</span>
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="开始时间" width="140" sortable>
          <template #default="scope">
            {{ formatDateTime(scope.row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column prop="dueDate" label="结束时间" width="140" sortable>
           <template #default="scope">
            <span v-if="scope.row.dueDate">{{ formatDateTime(scope.row.dueDate) }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="进度" width="100" align="center">
          <template #default="scope">
            <el-tag :type="getStatusTagType(scope.row.status)" size="small" effect="light" round :class="getStatusClass(scope.row.status)">
              {{ formatStatus(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
         <el-table-column prop="details" label="明细" width="150">
           <template #default="scope">
             <!-- 占位符 -->
             {{ scope.row.details || (scope.row.status === 'done' ? '无' : '正在维修中') }}
           </template>
         </el-table-column>
        <el-table-column label="操作" width="150" align="center" fixed="right">
          <template #default="scope">
            <el-tooltip content="编辑" placement="top">
              <el-button link type="primary" size="small" :icon="Edit" @click="openTaskForm(scope.row)"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="danger" size="small" :icon="Delete" @click="handleDelete(scope.row.id)"></el-button>
            </el-tooltip>
             <el-tooltip content="查看详情" placement="top">
               <el-button link type="info" size="small" :icon="View" @click="viewTaskDetails(scope.row)"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-if="pagination.total > 0"
        class="mt-4 pagination-right"
        :current-page="pagination.currentPage"
        :page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>

    <!-- 任务详情弹窗 -->
    <TaskDetailsDialog ref="taskDetailsDialogRef" />

  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh, Plus, Edit, Delete, View } from '@element-plus/icons-vue'
import TaskDetailsDialog from './components/TaskDetailsDialog.vue' // 引入详情弹窗
import { getTasks, deleteTask, searchUsers as searchUsersApi } from '@/api/tasks' // 假设API路径
import { useUserStore, getFullAvatarUrl } from '@/stores/modules/user' // 获取用户信息

const userStore = useUserStore()

// --- 数据状态 ---
const tasks = ref([])
const loading = ref(false)
const taskDetailsDialogRef = ref(null) // 详情弹窗引用
const defaultAvatar = 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png' // 默认头像

// --- 筛选 ---
const filters = reactive({
  status: '',
  priority: null,
  assigneeId: null,
  title: '',
})
const userOptions = ref([])
const userSearchLoading = ref(false)

// --- 分页 ---
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0,
})

// 计算当前页显示的任务
const pagedTasks = computed(() => {
  // 前端分页逻辑 (如果后端分页，则不需要此计算属性，直接用 tasks.value)
  // const start = (pagination.currentPage - 1) * pagination.pageSize
  // const end = start + pagination.pageSize
  // return filteredTasks.value.slice(start, end)
  return tasks.value // 假设后端已分页
})

// --- 生命周期钩子 ---
onMounted(() => {
  fetchTasks()
})

// --- 方法 ---

// 获取任务列表
const fetchTasks = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.currentPage,
      pageSize: pagination.pageSize,
      status: filters.status || undefined,
      priority: filters.priority || undefined,
      assigneeId: filters.assigneeId || undefined,
      title: filters.title || undefined,
      // sortField: ..., // 可选：添加排序字段
      // sortOrder: ..., // 可选：添加排序顺序 (asc/desc)
    }
    const response = await getTasks(params) // 假设API返回 { data: { items: [], total: number } }
    tasks.value = response.data.items.map(task => ({
      ...task,
      // 模拟工序和项目阶段数据
      process: task.id % 2 === 0 ? '生产加工' : '来料入库',
      projectPhase: task.id % 2 === 0 ? '硬件组装' : '软件配置',
      // 模拟明细数据
      details: task.status === 'done' ? '无' : '正在维修中'
    }))
    pagination.total = response.data.total
  } catch (error) {
    console.error('获取任务列表失败:', error)
    ElMessage.error('获取任务列表失败')
    tasks.value = [] // 清空数据避免显示旧数据
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

// 搜索用户（负责人）
let searchUserDebounceTimer
const searchUsers = (query) => {
  if (query) {
    clearTimeout(searchUserDebounceTimer)
    searchUserDebounceTimer = setTimeout(async () => {
      userSearchLoading.value = true
      try {
        const response = await searchUsersApi({ keyword: query, limit: 20 }) // 假设API
        userOptions.value = response.data // 假设返回 { data: [{id, name, avatar}, ...] }
      } catch (error) {
        console.error("搜索用户失败:", error)
        userOptions.value = []
      } finally {
        userSearchLoading.value = false
      }
    }, 300) // 防抖
  } else {
    userOptions.value = []
  }
}

// 处理搜索
const handleSearch = () => {
  pagination.currentPage = 1 // 重置到第一页
  fetchTasks()
}

// 重置筛选
const resetFilters = () => {
  filters.status = ''
  filters.priority = null
  filters.assigneeId = null
  filters.title = ''
  userOptions.value = [] // 清空负责人选项
  handleSearch() // 重新获取数据
}

// 处理分页大小变化
const handleSizeChange = (newSize) => {
  pagination.pageSize = newSize
  pagination.currentPage = 1 // 重置到第一页
  fetchTasks()
}

// 处理当前页变化
const handleCurrentChange = (newPage) => {
  pagination.currentPage = newPage
  fetchTasks()
}

// 打开新增/编辑任务弹窗
const openTaskForm = (task = null) => {
  // 移除 TaskFormDialog 相关引用和使用
}

// 查看任务详情
const viewTaskDetails = (task) => {
  taskDetailsDialogRef.value?.open(task)
}


// 删除任务
const handleDelete = (id) => {
  ElMessageBox.confirm(
    '确定要删除此任务吗？此操作不可撤销。',
    '警告',
    {
      confirmButtonText: '确定删除',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(async () => {
    try {
      await deleteTask(id)
      ElMessage.success('任务删除成功')
      fetchTasks() // 重新加载列表
    } catch (error) {
      console.error('删除任务失败:', error)
      ElMessage.error('删除任务失败')
    }
  }).catch(() => {
    // 用户取消
  })
}

// 格式化日期时间 (简单示例, 可用更专业的库如dayjs)
const formatDateTime = (dateTimeString) => {
  if (!dateTimeString) return '-'
  try {
    const date = new Date(dateTimeString)
    // 只显示年月日
    return date.toLocaleDateString()
    // 如果需要 年-月-日 格式
    // const year = date.getFullYear();
    // const month = (date.getMonth() + 1).toString().padStart(2, '0');
    // const day = date.getDate().toString().padStart(2, '0');
    // return `${year}-${month}-${day}`;
  } catch (e) {
    return dateTimeString // 无效日期则原样返回
  }
}

// 格式化状态显示文本
const formatStatus = (status) => {
  switch (status) {
    case 'todo': return '未开始'
    case 'doing': return '进行中'
    case 'done': return '已完成'
    case 'overdue': return '已逾期'
    default: return '未知'
  }
}

// 获取状态标签类型 (用于Element Plus Tag的type属性)
const getStatusTagType = (status) => {
  switch (status) {
    case 'todo': return 'info'
    case 'doing': return 'warning' // 使用 warning 触发 Element Plus 的黄色系
    case 'done': return 'success' // 使用 success 触发 Element Plus 的绿色系
    case 'overdue': return 'danger'
    default: return 'info'
  }
}

// 获取状态标签的自定义CSS类 (用于匹配截图样式)
const getStatusClass = (status) => {
  switch (status) {
    case 'done': return 'status-done'
    case 'doing': return 'status-doing'
    // 可以为其他状态添加更多自定义类
    default: return ''
  }
}


// 监听筛选条件变化自动刷新 (可选)
// watch(filters, handleSearch, { deep: true })

</script>

<style scoped lang="scss">
// 引入 SCSS 变量，确保能使用 --success-color, --warning-color 等
@use "@/styles/variables.scss" as *;

.page-container {
  padding: 20px;
  height: calc(100vh - var(--header-height) - 40px); /* 减去头部和可能的底部边距 */
  display: flex;
  flex-direction: column;
}

.page-title {
  margin-bottom: 20px;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.filter-card {
  margin-bottom: 16px; // 调整卡片间距
}

.filters {
  display: flex;
  flex-wrap: wrap; // 允许换行
  align-items: center;
  gap: 8px; // 筛选条件之间的间距
}

.actions {
  margin-top: 16px; // 按钮与筛选条件的间距
  text-align: left; // 新增按钮靠左
}

.task-table-card {
  flex: 1; // 让表格卡片占据剩余空间
  display: flex;
  flex-direction: column;
  overflow: hidden; // 配合 flex: 1 防止内容溢出

  .el-card__body {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden; // 让 card body 内部也能滚动
    padding: 0; // 移除内边距，让表格占满
  }

  .el-table {
    flex: 1; // 表格本身也占据可用空间
    // 移除可能存在的边框，依赖列本身的边框
    border: none;

    // 覆盖默认表头背景和文字颜色，确保 :header-cell-style 生效
    // 如果 :header-cell-style 不生效，可以尝试用这个
    // :deep(.el-table__header-wrapper th) {
    //   background-color: #f8f9fa !important;
    //   color: #303133 !important;
    //   font-weight: 600;
    // }

    // 单元格样式
    :deep(.el-table__cell) {
      padding: 10px 12px; // 调整单元格内边距
      border-bottom: 1px solid var(--el-border-color-light); // 使用更浅的边框色
    }

    // 移除鼠标悬停行的背景色 (如果需要)
    // :deep(.el-table__body tr:hover > td) {
    //   background-color: transparent !important;
    // }

    // 移除最后一行单元格的下边框
     :deep(.el-table__body tr:last-child td) {
      border-bottom: none;
    }
  }
}


.task-title-link {
  cursor: pointer;
  color: var(--el-color-primary);
  &:hover {
    text-decoration: underline;
  }
}

.user-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.table-avatar {
  flex-shrink: 0; // 防止头像被压缩
}

.pagination-right {
  justify-content: flex-end;
  margin-top: 16px;
  padding: 0 20px 10px; // 给分页一些边距
}

// 自定义状态标签样式 (精确匹配截图)
.el-tag {
  border: none; // 移除默认边框
}

.status-done {
  // background-color: #d1e7dd !important; // Element Plus 1.x 写法
  // color: #0f5132 !important;

  // Element Plus 2.x 推荐使用 CSS 变量覆盖
  --el-tag-bg-color: #d1e7dd; // 浅绿背景
  --el-tag-text-color: #0f5132; // 深绿文字
  --el-tag-border-color: transparent; // 确保无边框
}

.status-doing {
  // background-color: #fff3cd !important;
  // color: #664d03 !important;

  --el-tag-bg-color: #fff3cd; // 浅黄背景
  --el-tag-text-color: #664d03; // 深黄文字
  --el-tag-border-color: transparent;
}

/* 修复表格在flex布局下可能无法撑满的问题 */
:deep(.el-table__inner-wrapper) {
  height: 100%;
}

</style>