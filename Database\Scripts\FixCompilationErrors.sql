-- 编译错误修复验证脚本
-- 运行此SQL验证数据库连接和基础表结构

-- 检查任务相关表是否存在
SELECT TABLE_NAME, ENGINE, TABLE_ROWS 
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME IN ('Tasks', 'TaskAssignees', 'Comments', 'Attachments', 'TaskHistories');

-- 检查是否需要创建性能优化索引
SELECT COUNT(*) as existing_indexes
FROM INFORMATION_SCHEMA.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND INDEX_NAME LIKE 'idx_tasks%';

-- 如果上面的结果显示existing_indexes为0，则运行TaskQueryOptimization.sql
-- 如果显示有索引，说明优化已应用

SHOW INDEX FROM Tasks WHERE Key_name LIKE 'idx_%';