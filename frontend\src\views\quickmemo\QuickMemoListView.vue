// File: frontend/src/views/quickmemo/QuickMemoListView.vue
// Description: 随手记列表视图，用于展示所有随手记的详细信息。
<template>
  <div class="quick-memo-list-view">
    <div class="memo-container">
      <!-- 左侧列表区域 -->
      <div class="memo-list-section">
        <div class="list-header">
          <div class="search-bar">
            <el-input
              v-model="searchText"
              placeholder="搜索标题或内容"
              class="search-input"
              clearable
              @input="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
            <el-select v-model="categoryFilter" placeholder="分类筛选" clearable @change="handleFilter">
              <el-option label="全部" value="" />
              <el-option
                v-for="category in categories"
                :key="category.id"
                :label="category.name"
                :value="category.id"
              />
            </el-select>
          </div>
          <el-button type="primary" class="create-btn" @click="createNewMemo">
            <el-icon><Plus /></el-icon> 新建随手记
          </el-button>
        </div>

        <div class="memo-list">
          <div 
            v-for="memo in filteredMemos" 
            :key="memo.id"
            class="memo-item"
            :class="{ 'active': currentMemo?.id === memo.id }"
            @click="selectMemo(memo)"
          >
            <div class="memo-item-header">
              <span class="memo-title">{{ memo.title || '无标题' }}</span>
              <el-tag v-if="memo.category" :color="getCategoryColor(memo.category)" effect="dark" size="small">
                {{ memo.category.name }}
            </el-tag>
            </div>
            <div class="memo-preview">{{ stripHtml(memo.content).slice(0, 100) }}</div>
            <div class="memo-footer">
              <div class="memo-meta">
                <span class="meta-item">
                  <el-icon><User /></el-icon>
                  {{ memo.userName }}
                </span>
                <span class="meta-item">
                  <el-icon><Clock /></el-icon>
                  {{ formatTime(memo.updatedAt) }}
                </span>
              </div>
              <div class="memo-actions">
            <el-popconfirm
              title="确定删除这条随手记吗？"
                  @confirm="deleteMemo(memo.id)"
              confirm-button-text="确定"
              cancel-button-text="取消"
                  @click.stop
            >
              <template #reference>
                    <el-button size="small" type="danger" text @click.stop>
                      <el-icon><Delete /></el-icon>
                    </el-button>
              </template>
            </el-popconfirm>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧编辑区域 -->
      <div class="memo-edit-section" v-if="currentMemo">
        <div class="edit-header">
          <el-input
            v-model="currentMemo.title"
            placeholder="请输入标题"
            class="title-input"
            @change="handleTitleChange"
          />
          <div class="header-actions">
            <el-select
              v-model="currentMemo.categoryId"
              placeholder="选择分类"
              class="category-select"
              @change="handleCategoryChange"
            >
              <el-option
                v-for="category in categories"
                :key="category.id"
                :label="category.name"
                :value="category.id"
              >
                <el-tag :color="category.color" effect="dark" size="small">
                  {{ category.name }}
                </el-tag>
              </el-option>
            </el-select>
            <div class="meta-info">
              <span class="meta-item">
                <el-icon><User /></el-icon>
                {{ currentMemo.userName }}
              </span>
              <span class="meta-item">
                <el-icon><Clock /></el-icon>
                {{ formatTime(currentMemo.updatedAt) }}
              </span>
            </div>
          </div>
        </div>

        <div class="edit-toolbar">
          <el-button-group>
            <el-button link title="标题1">H1</el-button>
            <el-button link title="标题2">H2</el-button>
            <el-button link title="标题3">H3</el-button>
          </el-button-group>
          <el-divider direction="vertical" />
          <el-button-group>
            <el-button link title="编辑">
              <el-icon><Edit /></el-icon>
            </el-button>
            <el-button link title="文档">
              <el-icon><Document /></el-icon>
            </el-button>
          </el-button-group>
          <el-divider direction="vertical" />
          <el-button-group>
            <el-button link title="有序列表">
              <el-icon><List /></el-icon>
            </el-button>
            <el-button link title="无序列表">
              <el-icon><IconMenu /></el-icon>
            </el-button>
          </el-button-group>
        </div>

        <div class="edit-content">
          <el-input
            v-model="currentMemo.content"
            type="textarea"
            :rows="20"
            placeholder="开始编写..."
            class="content-editor"
            @change="handleContentChange"
            resize="none"
          />
        </div>

        <div class="edit-footer">
          <span class="auto-save-hint">
            <el-icon><Clock /></el-icon>
            自动保存
          </span>
        </div>
      </div>

      <!-- 右侧空白提示 -->
      <div class="memo-empty" v-else>
        <el-empty description="选择一条随手记开始编辑" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useQuickMemoStore } from '@/stores/modules/quickMemo';
import { ElMessage } from 'element-plus';
import { format } from 'date-fns';
import { 
  Plus,
  Delete,
  User,
  Clock,
  Search,
  Document,
  Edit,
  List,
  Menu as IconMenu
} from '@element-plus/icons-vue';

const quickMemoStore = useQuickMemoStore();
const loading = ref(false);
const memos = computed(() => quickMemoStore.quickMemos);
const categories = computed(() => quickMemoStore.categories);
const searchText = ref('');
const categoryFilter = ref('');
const currentMemo = ref(null);
let saveTimeout = null;

// 过滤后的数据
const filteredMemos = computed(() => {
  let result = [...memos.value];
  
  if (searchText.value) {
    const search = searchText.value.toLowerCase();
    result = result.filter(memo => 
      (memo.title?.toLowerCase().includes(search) || 
       stripHtml(memo.content).toLowerCase().includes(search))
    );
  }
  
  if (categoryFilter.value) {
    result = result.filter(memo => memo.category?.id === categoryFilter.value);
  }
  
  return result.sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt));
});

onMounted(() => {
  fetchMemos();
  if (quickMemoStore.categories.length === 0) {
    quickMemoStore.fetchCategories().catch(err => {
      console.error("Failed to fetch categories on list view mount:", err);
    });
  }
});

const fetchMemos = async () => {
  loading.value = true;
  try {
    await quickMemoStore.fetchQuickMemos();
  } catch (error) {
    console.error('Failed to fetch memos:', error);
    ElMessage.error('获取随手记列表失败');
  } finally {
    loading.value = false;
  }
};

const formatTime = (timestamp) => {
  if (!timestamp) return 'N/A';
  return format(new Date(timestamp), 'yyyy-MM-dd HH:mm:ss');
};

const getCategoryColor = (category) => {
  return category && category.color ? category.color : '#409EFF';
};

const stripHtml = (html) => {
  if (!html) return '';
  return html.replace(/<[^>]*>/g, '');
};

const handleSearch = () => {
  // 搜索逻辑已通过计算属性实现
};

const handleFilter = () => {
  // 筛选逻辑已通过计算属性实现
};

const createNewMemo = async () => {
  try {
    const defaultTitle = `新建随手记 ${format(new Date(), 'yyyy-MM-dd HH:mm:ss')}`;
    const newMemo = await quickMemoStore.createQuickMemo({
      title: defaultTitle,
      content: '',
      categoryId: null
    });
    await fetchMemos();
    selectMemo(newMemo);
  } catch (error) {
    console.error('Failed to create memo:', error);
    ElMessage.error('创建随手记失败');
  }
};

const selectMemo = (memo) => {
  currentMemo.value = { ...memo, categoryId: memo.category?.id };
};

const autoSave = async () => {
  if (!currentMemo.value?.id) return;
  
  try {
    await quickMemoStore.editQuickMemo({
      id: currentMemo.value.id,
      title: currentMemo.value.title,
      content: currentMemo.value.content,
      categoryId: currentMemo.value.categoryId
    });
    await fetchMemos();
  } catch (error) {
    console.error('Failed to save memo:', error);
    ElMessage.error('保存失败');
  }
};

const handleTitleChange = () => {
  if (saveTimeout) clearTimeout(saveTimeout);
  saveTimeout = setTimeout(autoSave, 1000);
};

const handleContentChange = () => {
  if (saveTimeout) clearTimeout(saveTimeout);
  saveTimeout = setTimeout(autoSave, 1000);
};

const handleCategoryChange = () => {
  autoSave();
};

const deleteMemo = async (memoId) => {
  try {
    await quickMemoStore.removeQuickMemo(memoId);
    ElMessage.success('随手记删除成功');
    if (currentMemo.value?.id === memoId) {
      currentMemo.value = null;
    }
    fetchMemos();
  } catch (error) {
    console.error('Failed to delete memo:', error);
    ElMessage.error('删除随手记失败');
  }
};

const cancelEdit = () => {
  currentMemo.value = null;
};
</script>

<style lang="scss" scoped>
.quick-memo-list-view {
  height: 100vh;
  padding: 20px;
  background: var(--el-fill-color-light);
}

.memo-container {
  display: flex;
  height: calc(100vh - 40px);
  background: var(--el-bg-color);
  border-radius: 8px;
  box-shadow: var(--el-box-shadow-light);
  overflow: hidden;
}

.memo-list-section {
  width: 360px;
  border-right: 1px solid var(--el-border-color-lighter);
  display: flex;
  flex-direction: column;
  
  .list-header {
    padding: 16px;
    border-bottom: 1px solid var(--el-border-color-lighter);

    .search-bar {
      display: flex;
      gap: 12px;
      margin-bottom: 12px;

      .search-input {
        flex: 1;
      }

      .el-select {
        width: 120px;
      }
}

.create-btn {
      width: 100%;
    }
  }

  .memo-list {
    flex: 1;
    overflow-y: auto;
    
    .memo-item {
      padding: 16px;
      border-bottom: 1px solid var(--el-border-color-lighter);
  cursor: pointer;
      transition: all 0.3s;
      
  &:hover {
        background: var(--el-fill-color);
      }
      
      &.active {
        background: var(--el-color-primary-light-9);
      }

      .memo-item-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        .memo-title {
          font-weight: 500;
          color: var(--el-text-color-primary);
        }
      }

      .memo-preview {
        font-size: 13px;
        color: var(--el-text-color-regular);
        margin-bottom: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        line-height: 1.6;
      }

      .memo-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .memo-meta {
          display: flex;
          gap: 12px;
          color: var(--el-text-color-secondary);
          font-size: 12px;

          .meta-item {
            display: flex;
            align-items: center;
            gap: 4px;
          }
        }

        .memo-actions {
          opacity: 0;
          transition: opacity 0.3s;
        }
      }

      &:hover .memo-actions {
        opacity: 1;
      }
    }
  }
}

.memo-edit-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: var(--el-bg-color);
  
  .edit-header {
    padding: 20px;
    border-bottom: 1px solid var(--el-border-color-lighter);

    .title-input {
      margin-bottom: 16px;
      
      :deep(.el-input__inner) {
        font-size: 24px;
        font-weight: 500;
        border: none;
        padding: 0;
        height: 40px;
        
        &:focus {
          box-shadow: none;
        }
      }
    }

    .header-actions {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .category-select {
        width: 200px;
      }

      .meta-info {
        display: flex;
        gap: 16px;
        color: var(--el-text-color-secondary);
        font-size: 13px;

        .meta-item {
          display: flex;
          align-items: center;
          gap: 4px;
        }
      }
    }
  }

  .edit-toolbar {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    border-bottom: 1px solid var(--el-border-color-lighter);
    background: var(--el-fill-color-light);

    .el-button {
      padding: 6px 8px;
      
      &:hover {
        background: var(--el-fill-color);
      }
    }
  }

  .edit-content {
    flex: 1;
    padding: 20px;
    
    .content-editor {
      height: 100%;
      
      :deep(.el-textarea__inner) {
        height: 100%;
        font-size: 14px;
        line-height: 1.8;
        font-family: system-ui, -apple-system, sans-serif;
        padding: 16px;
        border: none;
        resize: none;
        
        &:focus {
          box-shadow: none;
        }
      }
    }
  }

  .edit-footer {
    padding: 12px 20px;
    border-top: 1px solid var(--el-border-color-lighter);
    color: var(--el-text-color-secondary);
    font-size: 12px;
    background: var(--el-fill-color-light);

    .auto-save-hint {
      display: flex;
      align-items: center;
      gap: 4px;
    }
  }
}

.memo-empty {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--el-fill-color-light);
}
</style> 