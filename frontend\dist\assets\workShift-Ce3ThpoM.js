import{aV as s}from"./index-CG5lHOPO.js";const t={getAllShifts:()=>s.get("/v2/work-shifts"),createShift:t=>s.post("/v2/work-shifts",t),assignUserToShift:t=>s.post("/v2/work-shifts/assignments",t),getShiftAssignments:t=>s.get(`/v2/work-shifts/${t}/assignments`),removeUserShiftAssignment:t=>s.delete(`/v2/work-shifts/assignments/${t}`),getUserCurrentShift:()=>s.get("/v2/work-shifts/current"),getUserCurrentShiftById:t=>s.get(`/v2/work-shifts/users/${t}/current`),claimTask:t=>s.post("/v2/work-shifts/claim-task",t),updateClaimStatus:(t,e)=>s.put(`/v2/work-shifts/claims/${t}/status`,e),getUserTodayClaims:()=>s.get("/v2/work-shifts/claims/today"),getUserTodayClaimsById:t=>s.get(`/v2/work-shifts/users/${t}/claims/today`),getTodayShiftStatistics:(t={})=>s.get("/v2/work-shifts/statistics/today",{params:t}),getAvailableTasks:(t={})=>s.get("/v2/tasks",{params:{...t,status:"Todo",pageSize:100}}),getAllUsers:(t={})=>s.get("/v1.1/user",{params:{...t,pageSize:1e3}})};export{t as w};
