<!DOCTYPE html>
<html>
<head>
<style>
.col {
	float:left;
	width:50%;
	left:50%;
}
</style>
</head>

<body>
<div class=col>
  <h2>EventSource</h2>
  <ul id=es-messages>
  </ul>
</div>

<div class=col>
  <h2>EventSourcePolyfill</h2>
  <ul id=es-polyfill-messages>
  </ul>
</div>

<script src=/eventsource-polyfill.js></script>

<script>
function subscribe(es, ul) {
  es.addEventListener('server-time', function (e) {
    var li = document.createElement("LI");
    li.appendChild(document.createTextNode(e.data));
    ul.appendChild(li);
  });
}

subscribe(new EventSource('/sse'),         document.getElementById('es-messages'));
subscribe(new EventSourcePolyfill('/sse'), document.getElementById('es-polyfill-messages'));
</script>
</body>
