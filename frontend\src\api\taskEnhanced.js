/**
 * 增强的任务管理API
 * 支持现代化任务管理功能：批量操作、实时通知、数据分析等
 */

import request from '@/utils/request'

// API基础路径
const API_BASE = '/v2/tasks-enhanced'

/**
 * 任务基础CRUD操作
 */
export const taskApi = {
  // 获取任务列表（支持高级筛选）
  async getTasks(params = {}) {
    try {
      const response = await request.get(API_BASE, { params })
      return {
        success: true,
        data: response.data || [],
        pagination: response.pagination,
        message: '获取任务列表成功'
      }
    } catch (error) {
      console.error('获取任务列表失败:', error)
      throw error
    }
  },

  // 获取任务详情
  async getTaskById(taskId) {
    try {
      const response = await request.get(`${API_BASE}/${taskId}`)
      return response.data
    } catch (error) {
      if (error.response && error.response.status === 404) {
        if (typeof window !== 'undefined' && window.ElMessage) {
          window.ElMessage.error('任务已被删除或不存在')
        }
      } else {
        if (typeof window !== 'undefined' && window.ElMessage) {
          window.ElMessage.error('获取任务详情失败: ' + (error.response?.data?.message || error.message))
        }
      }
      throw error
    }
  },

  // 创建任务
  async createTask(taskData) {
    try {
      const response = await request.post(API_BASE, taskData)
      return response.data
    } catch (error) {
      console.error('创建任务失败:', error)
      throw error
    }
  },

  // 更新任务
  async updateTask(taskId, taskData) {
    try {
      const response = await request.put(`${API_BASE}/${taskId}`, taskData)
      return response.data
    } catch (error) {
      console.error('更新任务失败:', error)
      throw error
    }
  },

  // 删除任务
  async deleteTask(taskId) {
    try {
      const response = await request.delete(`${API_BASE}/${taskId}`)
      return response.data
    } catch (error) {
      if (error.response && error.response.status === 404) {
        if (typeof window !== 'undefined' && window.ElMessage) {
          window.ElMessage.error('任务已被删除或不存在')
        }
      } else {
        if (typeof window !== 'undefined' && window.ElMessage) {
          window.ElMessage.error('删除任务失败: ' + (error.response?.data?.message || error.message))
        }
      }
      throw error
    }
  }
}

/**
 * 任务状态管理
 */
export const taskStatusApi = {
  // 更新任务状态
  async updateTaskStatus(taskId, status, remarks = '') {
    try {
      const response = await request.patch(`${API_BASE}/${taskId}/status`, {
        status,
        remarks
      })
      return response.data
    } catch (error) {
      console.error('更新任务状态失败:', error)
      throw error
    }
  },

  // 更新任务进度
  async updateTaskProgress(taskId, progress, remarks = '') {
    try {
      const response = await request.patch(`${API_BASE}/${taskId}/progress`, {
        progress,
        remarks
      })
      return response.data
    } catch (error) {
      console.error('更新任务进度失败:', error)
      throw error
    }
  },

  // 分配任务
  async assignTask(taskId, assigneeUserId, remarks = '') {
    try {
      const response = await request.patch(`${API_BASE}/${taskId}/assign`, {
        assigneeUserId,
        remarks
      })
      return response.data
    } catch (error) {
      console.error('分配任务失败:', error)
      throw error
    }
  },

  // 完成任务
  async completeTask(taskId, remarks = '') {
    try {
      const response = await request.patch(`${API_BASE}/${taskId}/complete`, {
        remarks
      })
      return response.data
    } catch (error) {
      console.error('完成任务失败:', error)
      throw error
    }
  }
}

/**
 * 批量操作API
 */
export const batchOperationApi = {
  // 批量更新状态
  async batchUpdateStatus(taskIds, status, remarks = '') {
    try {
      const response = await request.patch(`${API_BASE}/batch/status`, {
        taskIds,
        status,
        remarks
      })
      return response.data
    } catch (error) {
      console.error('批量更新状态失败:', error)
      throw error
    }
  },

  // 批量分配任务
  async batchAssignTasks(taskIds, assigneeUserId, remarks = '') {
    try {
      const response = await request.patch(`${API_BASE}/batch/assign`, {
        taskIds,
        assigneeUserId,
        remarks
      })
      return response.data
    } catch (error) {
      console.error('批量分配任务失败:', error)
      throw error
    }
  },

  // 批量删除任务
  async batchDeleteTasks(taskIds) {
    try {
      const response = await request.delete(`${API_BASE}/batch`, {
        data: { taskIds }
      })
      return response.data
    } catch (error) {
      console.error('批量删除任务失败:', error)
      throw error
    }
  }
}

/**
 * 评论管理API
 */
export const commentApi = {
  // 获取任务评论
  async getTaskComments(taskId) {
    try {
      const response = await request.get(`${API_BASE}/${taskId}/comments`)
      return response.data || []
    } catch (error) {
      console.error('获取任务评论失败:', error)
      throw error
    }
  },

  // 添加评论
  async addComment(taskId, commentData) {
    try {
      const response = await request.post(`${API_BASE}/${taskId}/comments`, commentData)
      return response.data
    } catch (error) {
      console.error('添加评论失败:', error)
      throw error
    }
  }
}

/**
 * 附件管理API
 */
export const attachmentApi = {
  // 获取任务附件
  async getTaskAttachments(taskId) {
    try {
      const response = await request.get(`${API_BASE}/${taskId}/attachments`)
      return response.data || []
    } catch (error) {
      console.error('获取任务附件失败:', error)
      throw error
    }
  },

  // 上传附件
  async addAttachment(taskId, formData) {
    try {
      const response = await request.post(`${API_BASE}/${taskId}/attachments`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
      return response.data
    } catch (error) {
      console.error('上传附件失败:', error)
      throw error
    }
  },

  // 删除附件
  async deleteAttachment(attachmentId) {
    try {
      const response = await request.delete(`${API_BASE}/attachments/${attachmentId}`)
      return response.data
    } catch (error) {
      console.error('删除附件失败:', error)
      throw error
    }
  }
}

/**
 * 历史记录API
 */
export const historyApi = {
  // 获取任务历史记录
  async getTaskHistory(taskId) {
    try {
      const response = await request.get(`${API_BASE}/${taskId}/history`)
      return response.data || []
    } catch (error) {
      console.error('获取任务历史记录失败:', error)
      throw error
    }
  }
}

/**
 * 统计分析API
 */
export const analyticsApi = {
  // 获取任务统计信息
  async getTaskStatistics(params = {}) {
    try {
      const response = await request.get(`${API_BASE}/statistics`, { params })
      return response.data
    } catch (error) {
      console.error('获取任务统计失败:', error)
      throw error
    }
  },

  // 获取任务趋势数据
  async getTaskTrend(params = {}) {
    try {
      const response = await request.get(`${API_BASE}/analytics/trend`, { params })
      return response.data || []
    } catch (error) {
      console.error('获取任务趋势失败:', error)
      throw error
    }
  },

  // 获取工作量分布
  async getWorkloadDistribution(params = {}) {
    try {
      const response = await request.get(`${API_BASE}/analytics/workload`, { params })
      return response.data
    } catch (error) {
      console.error('获取工作量分布失败:', error)
      throw error
    }
  }
}

/**
 * 选项数据API
 */
export const optionsApi = {
  // 获取任务状态选项
  async getTaskStatusOptions() {
    try {
      const response = await request.get(`${API_BASE}/status-options`)
      return response.data || []
    } catch (error) {
      console.error('获取状态选项失败:', error)
      return ['Todo', 'InProgress', 'Done', 'Cancelled']
    }
  },

  // 获取优先级选项
  async getTaskPriorityOptions() {
    try {
      const response = await request.get(`${API_BASE}/priority-options`)
      return response.data || []
    } catch (error) {
      console.error('获取优先级选项失败:', error)
      return ['Low', 'Medium', 'High', 'Urgent']
    }
  },

  // 获取任务类型选项
  async getTaskTypeOptions() {
    try {
      const response = await request.get(`${API_BASE}/type-options`)
      return response.data || []
    } catch (error) {
      console.error('获取类型选项失败:', error)
      return ['Normal', 'Periodic', 'PDCA']
    }
  }
}

/**
 * 用户相关API
 */
export const userApi = {
  // 获取活跃用户列表
  async getActiveUsers(params = {}) {
    try {
      const response = await request.get('/users/active', { params })
      return response.data || []
    } catch (error) {
      console.error('获取用户列表失败:', error)
      throw error
    }
  },

  // 搜索用户
  async searchUsers(keyword) {
    try {
      const response = await request.get('/users/search', {
        params: { keyword }
      })
      return response.data || []
    } catch (error) {
      console.error('搜索用户失败:', error)
      throw error
    }
  }
}

// 导出所有API
export default {
  ...taskApi,
  ...taskStatusApi,
  ...batchOperationApi,
  ...commentApi,
  ...attachmentApi,
  ...historyApi,
  ...analyticsApi,
  ...optionsApi,
  ...userApi
}