namespace ItAssetsSystem.Application.Features.AssetStatistics.Dtos
{
    /// <summary>
    /// 资产类型统计DTO
    /// </summary>
    public class AssetTypeStatisticsDto
    {
        /// <summary>
        /// 资产类型ID
        /// </summary>
        public int AssetTypeId { get; set; }

        /// <summary>
        /// 资产类型名称
        /// </summary>
        public string AssetTypeName { get; set; }

        /// <summary>
        /// 资产数量
        /// </summary>
        public int AssetCount { get; set; }

        /// <summary>
        /// 正常数量
        /// </summary>
        public int NormalCount { get; set; }

        /// <summary>
        /// 故障数量
        /// </summary>
        public int FaultCount { get; set; }

        /// <summary>
        /// 维修中数量
        /// </summary>
        public int MaintenanceCount { get; set; }

        /// <summary>
        /// 正常率
        /// </summary>
        public decimal NormalRate { get; set; }

        /// <summary>
        /// 故障率
        /// </summary>
        public decimal FaultRate { get; set; }

        /// <summary>
        /// 占比百分比
        /// </summary>
        public decimal Percentage { get; set; }
    }
}