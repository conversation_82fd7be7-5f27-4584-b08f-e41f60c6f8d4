// File: Application/Features/Gamification/Services/GamificationStatsUpdater.cs
// Description: 游戏化统计更新器实现

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using ItAssetsSystem.Infrastructure.Data;
using ItAssetsSystem.Models;
using ItAssetsSystem.Models.Entities.Gamification;

namespace ItAssetsSystem.Application.Features.Gamification.Services
{
    /// <summary>
    /// 游戏化统计更新器实现
    /// </summary>
    public class GamificationStatsUpdater : IGamificationStatsUpdater
    {
        private readonly AppDbContext _context;
        private readonly ILogger<GamificationStatsUpdater> _logger;

        public GamificationStatsUpdater(
            AppDbContext context,
            ILogger<GamificationStatsUpdater> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// 更新用户统计
        /// </summary>
        public async Task<bool> UpdateUserStatsAsync(int userId)
        {
            try
            {
                var userStats = await _context.GamificationUserStats
                    .FirstOrDefaultAsync(gus => gus.CoreUserId == userId);

                if (userStats == null)
                {
                    _logger.LogWarning("用户统计不存在: UserId={UserId}", userId);
                    return false;
                }

                // 重新计算任务统计
                var taskStats = await CalculateTaskStatsAsync(userId);
                userStats.CompletedTasksCount = taskStats.CompletedCount;
                userStats.CreatedTasksCount = taskStats.CreatedCount;
                userStats.OnTimeTasksCount = taskStats.OnTimeCount;

                // 重新计算认领统计
                var claimedCount = await _context.TaskClaims
                    .CountAsync(tc => tc.ClaimedBy == userId);
                userStats.ClaimedTasksCount = claimedCount;

                // 重新计算积分余额
                var pointsBalance = await CalculatePointsBalanceAsync(userStats.UserId);
                userStats.PointsBalance = pointsBalance.Points;
                userStats.CoinsBalance = pointsBalance.Coins;
                userStats.DiamondsBalance = pointsBalance.Diamonds;
                userStats.CurrentXP = pointsBalance.Experience;

                // 更新等级
                var newLevel = CalculateLevelFromXP(userStats.CurrentXP);
                var oldLevel = userStats.CurrentLevel;
                userStats.CurrentLevel = newLevel;

                userStats.LastUpdatedTimestamp = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                _logger.LogInformation("用户统计更新成功: UserId={UserId}, Level={Level}, Points={Points}", 
                    userId, newLevel, userStats.PointsBalance);

                // 如果等级提升，记录日志
                if (newLevel > oldLevel)
                {
                    await LogLevelUpAsync(userStats.UserId, oldLevel, newLevel);
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新用户统计时发生错误: UserId={UserId}", userId);
                return false;
            }
        }

        /// <summary>
        /// 批量更新用户统计
        /// </summary>
        public async Task<Dictionary<int, bool>> UpdateBatchUserStatsAsync(IEnumerable<int> userIds)
        {
            var results = new Dictionary<int, bool>();
            var userIdsList = userIds.ToList();

            foreach (var userId in userIdsList)
            {
                results[userId] = await UpdateUserStatsAsync(userId);
            }

            _logger.LogInformation("批量更新用户统计完成: 总数={Total}, 成功={Success}, 失败={Failed}", 
                userIdsList.Count, results.Count(r => r.Value), results.Count(r => !r.Value));

            return results;
        }

        /// <summary>
        /// 更新排行榜
        /// </summary>
        public async Task<bool> UpdateLeaderboardAsync(string leaderboardType, string period)
        {
            try
            {
                _logger.LogInformation("更新排行榜: Type={Type}, Period={Period}", leaderboardType, period);

                // 获取所有活跃用户
                var activeUserIds = await _context.Users
                    .Where(u => u.IsActive)
                    .Select(u => u.Id)
                    .ToListAsync();

                // 批量更新用户统计
                await UpdateBatchUserStatsAsync(activeUserIds);

                // 记录排行榜更新日志
                var systemLog = new GamificationLog
                {
                    UserId = 0, // 系统日志
                    EventType = "LeaderboardUpdate",
                    Description = $"排行榜更新: {leaderboardType} - {period}",
                    Timestamp = DateTime.UtcNow
                };

                _context.GamificationLogs.Add(systemLog);
                await _context.SaveChangesAsync();

                _logger.LogInformation("排行榜更新完成: Type={Type}, Period={Period}, Users={UserCount}", 
                    leaderboardType, period, activeUserIds.Count);

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新排行榜时发生错误: Type={Type}, Period={Period}", leaderboardType, period);
                return false;
            }
        }

        /// <summary>
        /// 检查并处理等级提升
        /// </summary>
        public async Task<bool> CheckAndProcessLevelUpAsync(int userId)
        {
            try
            {
                var userStats = await _context.GamificationUserStats
                    .FirstOrDefaultAsync(gus => gus.CoreUserId == userId);

                if (userStats == null)
                {
                    return false;
                }

                var newLevel = CalculateLevelFromXP(userStats.CurrentXP);
                var oldLevel = userStats.CurrentLevel;

                if (newLevel > oldLevel)
                {
                    userStats.CurrentLevel = newLevel;
                    userStats.LastUpdatedTimestamp = DateTime.UtcNow;

                    await _context.SaveChangesAsync();

                    // 记录等级提升日志
                    await LogLevelUpAsync(userStats.UserId, oldLevel, newLevel);

                    _logger.LogInformation("用户等级提升: UserId={UserId}, OldLevel={OldLevel}, NewLevel={NewLevel}", 
                        userId, oldLevel, newLevel);

                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查等级提升时发生错误: UserId={UserId}", userId);
                return false;
            }
        }

        /// <summary>
        /// 检查并处理成就解锁
        /// </summary>
        public async Task<IEnumerable<string>> CheckAndProcessAchievementsAsync(int userId, string eventType)
        {
            var unlockedAchievements = new List<string>();

            try
            {
                // 获取用户统计
                var userStats = await _context.GamificationUserStats
                    .FirstOrDefaultAsync(gus => gus.CoreUserId == userId);

                if (userStats == null)
                {
                    return unlockedAchievements;
                }

                // 获取相关的成就
                var achievements = await _context.Achievements
                    .Where(a => a.IsActive && a.Category.Contains(eventType))
                    .ToListAsync();

                foreach (var achievement in achievements)
                {
                    // 检查用户是否已经获得此成就
                    var userAchievement = await _context.UserAchievements
                        .FirstOrDefaultAsync(ua => ua.UserId == userId && ua.AchievementId == achievement.Id);

                    if (userAchievement == null)
                    {
                        userAchievement = new UserAchievement
                        {
                            UserId = userId,
                            AchievementId = achievement.Id,
                            CurrentValue = 0,
                            IsCompleted = false,
                            AchievedAt = DateTime.UtcNow
                        };
                        _context.UserAchievements.Add(userAchievement);
                    }

                    // 更新进度
                    var newProgress = CalculateAchievementProgress(achievement, userStats);
                    userAchievement.CurrentValue = newProgress;

                    // 检查是否完成
                    if (!userAchievement.IsCompleted && newProgress >= achievement.RequiredValue)
                    {
                        userAchievement.IsCompleted = true;
                        userAchievement.AchievedAt = DateTime.UtcNow;

                        unlockedAchievements.Add(achievement.Code);

                        // 发放成就奖励
                        await GrantAchievementRewardAsync(userStats, achievement);

                        _logger.LogInformation("用户解锁成就: UserId={UserId}, Achievement={Achievement}", 
                            userId, achievement.Code);
                    }
                }

                await _context.SaveChangesAsync();

                return unlockedAchievements;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查成就解锁时发生错误: UserId={UserId}, EventType={EventType}", userId, eventType);
                return unlockedAchievements;
            }
        }

        #region 私有方法

        /// <summary>
        /// 计算任务统计
        /// </summary>
        private async Task<(int CompletedCount, int CreatedCount, int OnTimeCount)> CalculateTaskStatsAsync(int userId)
        {
            var completedCount = await _context.Tasks
                .CountAsync(t => t.CompletedByUserId == userId && t.Status == "Done");

            var createdCount = await _context.Tasks
                .CountAsync(t => t.CreatorUserId == userId);

            var onTimeCount = await _context.Tasks
                .CountAsync(t => t.CompletedByUserId == userId &&
                               t.Status == "Done" &&
                               t.ActualEndDate != null &&
                               t.PlanEndDate != null &&
                               t.ActualEndDate <= t.PlanEndDate);

            return (completedCount, createdCount, onTimeCount);
        }

        /// <summary>
        /// 计算积分余额
        /// </summary>
        private async Task<(int Points, int Coins, int Diamonds, int Experience)> CalculatePointsBalanceAsync(long userStatsId)
        {
            var logs = await _context.GamificationLogs
                .Where(gl => gl.UserId == userStatsId)
                .ToListAsync();

            var points = logs.Sum(l => l.PointsGained);
            var coins = logs.Sum(l => l.CoinsGained);
            var diamonds = logs.Sum(l => l.DiamondsGained);
            var experience = logs.Sum(l => l.XPChange);

            return (points, coins, diamonds, experience);
        }

        /// <summary>
        /// 根据经验值计算等级
        /// </summary>
        private int CalculateLevelFromXP(int xp)
        {
            // 简单的等级计算公式：每100经验值升1级
            return Math.Max(1, xp / 100 + 1);
        }

        /// <summary>
        /// 记录等级提升日志
        /// </summary>
        private async Task LogLevelUpAsync(long userStatsId, int oldLevel, int newLevel)
        {
            var levelUpLog = new GamificationLog
            {
                UserId = userStatsId,
                EventType = "LevelUp",
                LevelBefore = oldLevel,
                LevelAfter = newLevel,
                Description = $"等级提升: {oldLevel} → {newLevel}",
                Timestamp = DateTime.UtcNow
            };

            _context.GamificationLogs.Add(levelUpLog);
            await Task.CompletedTask;
        }

        /// <summary>
        /// 计算成就进度
        /// </summary>
        private int CalculateAchievementProgress(Achievement achievement, GamificationUserStats userStats)
        {
            return achievement.Code switch
            {
                "FIRST_TASK" => userStats.CompletedTasksCount >= 1 ? 1 : 0,
                "TASK_MASTER_10" => Math.Min(userStats.CompletedTasksCount, 10),
                "TASK_MASTER_50" => Math.Min(userStats.CompletedTasksCount, 50),
                "TASK_MASTER_100" => Math.Min(userStats.CompletedTasksCount, 100),
                "ONTIME_MASTER" => Math.Min(userStats.OnTimeTasksCount, 10),
                _ => 0
            };
        }

        /// <summary>
        /// 发放成就奖励
        /// </summary>
        private async Task GrantAchievementRewardAsync(GamificationUserStats userStats, Achievement achievement)
        {
            userStats.PointsBalance += achievement.PointsReward;
            userStats.CoinsBalance += achievement.CoinsReward;
            userStats.DiamondsBalance += achievement.DiamondsReward;

            // 记录成就奖励日志
            var achievementLog = new GamificationLog
            {
                UserId = userStats.UserId,
                EventType = "AchievementEarned",
                PointsGained = achievement.PointsReward,
                CoinsGained = achievement.CoinsReward,
                DiamondsGained = achievement.DiamondsReward,
                Description = $"获得成就: {achievement.Name}",
                ReferenceId = achievement.Id,
                Timestamp = DateTime.UtcNow
            };

            _context.GamificationLogs.Add(achievementLog);
            await Task.CompletedTask;
        }

        #endregion
    }
}
