#nullable enable
// File: Application/Features/Notes/Dtos/UpdateQuickNoteRequestDto.cs
// Description: DTO for updating an existing quick note.

using System.ComponentModel.DataAnnotations;

namespace ItAssetsSystem.Application.Features.Notes.Dtos
{
    public class UpdateQuickNoteRequestDto
    {
        [Required(ErrorMessage = "Note content cannot be empty.")]
        [MaxLength(5000, ErrorMessage = "Note content is too long.")]
        public string Content { get; set; } = string.Empty;

        public bool IsPinned { get; set; }

        [RegularExpression(@"^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$", ErrorMessage = "Invalid color format. Use #RRGGBB or #RGB.")]
        [StringLength(7)]
        public string? Color { get; set; }
    }
} 