// File: Application/Features/Tasks/Dtos/TaskDto.cs
// Description: 任务数据传输对象

using System;
using System.Collections.Generic;
// using ItAssetsSystem.Models.Enums; // Enums will be mapped to strings if necessary by the service

namespace ItAssetsSystem.Application.Features.Tasks.Dtos
{
    /// <summary>
    /// 任务数据传输对象 (V2)
    /// </summary>
    public class TaskDto
    {
        /// <summary>
        /// 任务ID (BIGINT)
        /// </summary>
        public long TaskId { get; set; }

        /// <summary>
        /// 任务名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 任务描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 任务状态 (字符串，例如: Todo, InProgress, Done)
        /// </summary>
        public string Status { get; set; } = "Todo";

        /// <summary>
        /// 状态显示名称 (如"待处理"、"进行中"、"已完成")
        /// </summary>
        public string StatusName { get; set; } = string.Empty;

        /// <summary>
        /// 任务优先级 (字符串，例如: Low, Medium, High)
        /// </summary>
        public string Priority { get; set; } = "Medium";

        /// <summary>
        /// 优先级显示名称 (如"高"、"中"、"低")
        /// </summary>
        public string PriorityText { get; set; } = string.Empty;

        /// <summary>
        /// 任务类型 (字符串，例如: Normal, Periodic, PDCA)
        /// </summary>
        public string TaskType { get; set; } = "Normal";

        /// <summary>
        /// 记录创建时间
        /// </summary>
        public DateTime CreationTimestamp { get; set; }

        /// <summary>
        /// 创建时间（别名，用于兼容）
        /// </summary>
        public DateTime CreatedAt => CreationTimestamp;

        /// <summary>
        /// 实际完成时间
        /// </summary>
        public DateTime? ActualEndDate { get; set; }

        /// <summary>
        /// 完成时间（别名，用于兼容）- 已移除，使用独立的CompletedAt字段
        /// </summary>
        // public DateTime? CompletedAt => ActualEndDate; // 已移除，避免与独立字段冲突

        /// <summary>
        /// 计划结束时间 (截止时间)
        /// </summary>
        public DateTime? PlanEndDate { get; set; }
        
        /// <summary>
        /// 计划开始时间
        /// </summary>
        public DateTime? PlanStartDate { get; set; }

        /// <summary>
        /// 实际开始时间
        /// </summary>
        public DateTime? ActualStartDate { get; set; }

        /// <summary>
        /// 任务进度 (0-100)
        /// </summary>
        public int Progress { get; set; }

        /// <summary>
        /// 任务标签 (旧版，考虑移除或用 TagsList)
        /// </summary>
        public string Tags { get; set; } = string.Empty; // Kept for now, align with V2 Task if needed (no direct Tags property on V2 Task)

        /// <summary>
        /// 任务标签列表 (推荐方式)
        /// </summary>
        public List<string> TagList { get; set; } = new List<string>(); // V2 Task entity does not have direct tags list

        /// <summary>
        /// 负责人用户ID (INT)
        /// </summary>
        public int? AssigneeUserId { get; set; }

        /// <summary>
        /// 负责人名称 (通过 CoreDataQueryService 获取)
        /// </summary>
        public string AssigneeUserName { get; set; } = string.Empty;

        /// <summary>
        /// 负责人头像URL (通过 CoreDataQueryService 获取)
        /// </summary>
        public string AssigneeAvatarUrl { get; set; } = string.Empty;

        /// <summary>
        /// 创建者用户ID (INT)
        /// </summary>
        public int CreatorUserId { get; set; }

        /// <summary>
        /// 创建者名称 (通过 CoreDataQueryService 获取)
        /// </summary>
        public string CreatorUserName { get; set; } = string.Empty;
        
        /// <summary>
        /// 创建者头像URL (通过 CoreDataQueryService 获取)
        /// </summary>
        public string CreatorUserAvatarUrl { get; set; } = string.Empty;

        /// <summary>
        /// 关联资产ID (INT)
        /// </summary>
        public int? AssetId { get; set; } // From V2 Task entity

        /// <summary>
        /// 资产名称 (通过 CoreDataQueryService 获取)
        /// </summary>
        public string AssetName { get; set; } = string.Empty;

        /// <summary>
        /// 资产编号 (通过 CoreDataQueryService 获取)
        /// </summary>
        public string AssetNumber { get; set; } = string.Empty;

        /// <summary>
        /// 关联位置ID (INT)
        /// </summary>
        public int? LocationId { get; set; } // From V2 Task entity

        /// <summary>
        /// 位置名称 (通过 CoreDataQueryService 获取)
        /// </summary>
        public string LocationName { get; set; } = string.Empty;

        /// <summary>
        /// 父任务ID (BIGINT)
        /// </summary>
        public long? ParentTaskId { get; set; } // From V2 Task entity

        /// <summary>
        /// 父任务名称 (需要服务层填充)
        /// </summary>
        public string ParentTaskName { get; set; } = string.Empty;

        /// <summary>
        /// 子任务数量 (需要服务层计算)
        /// </summary>
        public int SubTaskCount { get; set; }

        /// <summary>
        /// 周期性任务计划ID (BIGINT)
        /// </summary>
        public long? PeriodicTaskScheduleId { get; set; } // From V2 Task entity

        /// <summary>
        /// 周期性任务计划名称 (需要服务层填充)
        /// </summary>
        public string PeriodicScheduleName { get; set; } = string.Empty;

        /// <summary>
        /// 关联的项目ID (BIGINT)
        /// </summary>
        public long? ProjectId { get; set; } // Added from V2 Task entity

        /// <summary>
        /// PDCA任务所处阶段 (Plan, Do, Check, Act)
        /// </summary>
        public string PDCAStage { get; set; } = string.Empty; // From V2 Task entity

        /// <summary>
        /// 完成任务可获得的基础积分/经验值
        /// </summary>
        public int Points { get; set; } // From V2 Task entity (Points)

        /// <summary>
        /// 任务分类ID
        /// </summary>
        public int? CategoryId { get; set; }

        /// <summary>
        /// 任务分类名称
        /// </summary>
        public string CategoryName { get; set; } = string.Empty;

        /// <summary>
        /// 任务分类颜色
        /// </summary>
        public string? CategoryColor { get; set; }

        /// <summary>
        /// 任务分类图标
        /// </summary>
        public string? CategoryIcon { get; set; }

        /// <summary>
        /// 完成用户ID (用于水印显示)
        /// </summary>
        public int? CompletedByUserId { get; set; }

        /// <summary>
        /// 任务完成时间
        /// </summary>
        public DateTime? CompletedAt { get; set; }

        /// <summary>
        /// 完成用户水印颜色 (十六进制颜色代码)
        /// </summary>
        public string? CompletionWatermarkColor { get; set; }

        /// <summary>
        /// 完成用户名称 (用于水印显示)
        /// </summary>
        public string? CompletedByUserName { get; set; }

        /// <summary>
        /// 完成用户头像URL (用于完成人显示)
        /// </summary>
        public string? CompletedByUserAvatarUrl { get; set; }

        /// <summary>
        /// 任务领取用户ID (游戏化功能)
        /// </summary>
        public int? ClaimedByUserId { get; set; }

        /// <summary>
        /// 任务领取用户名称 (游戏化功能)
        /// </summary>
        public string? ClaimedByUserName { get; set; }

        /// <summary>
        /// 是否已过期 (需要服务层根据 PlanEndDate 和 Status 计算)
        /// </summary>
        public bool IsOverdue { get; set; }

        /// <summary>
        /// 记录最后更新时间
        /// </summary>
        public DateTime LastUpdatedTimestamp { get; set; } // From V2 Task entity

        /// <summary>
        /// 评论数量 (需要服务层计算)
        /// </summary>
        public int CommentCount { get; set; }

        /// <summary>
        /// 附件数量 (需要服务层计算)
        /// </summary>
        public int AttachmentCount { get; set; }

        /// <summary>
        /// 参与人ID列表（逗号分隔，旧版，考虑 TaskAssignees 集合）
        /// </summary>
        public string ParticipantUserIds { get; set; } = string.Empty; // V2 Task has TaskAssignees collection

        /// <summary>
        /// 所有负责人列表 (包括主负责人和协作者，从 TaskAssignees 映射)
        /// </summary>
        public List<AssigneeDto> Assignees { get; set; } = new List<AssigneeDto>();
        
        /// <summary>
        /// 参与人信息列表 (推荐方式, 从 TaskAssignees 映射)
        /// </summary>
        public List<UserBasicDto> Participants { get; set; } = new List<UserBasicDto>();
        
        /// <summary>
        /// 子任务 DTO 列表 (可选，用于树形结构展示)
        /// </summary>
        public List<TaskDto> SubTasks { get; set; } = new List<TaskDto>();

        /// <summary>
        /// 附件 DTO 列表
        /// </summary>
        public List<AttachmentDto> Attachments { get; set; } = new List<AttachmentDto>();
        
        /// <summary>
        /// 评论 DTO 列表
        /// </summary>
        public List<CommentDto> Comments { get; set; } = new List<CommentDto>();

        /// <summary>
        /// 任务历史记录 DTO 列表
        /// </summary>
        public List<TaskHistoryDto> History { get; set; } = new List<TaskHistoryDto>();

        // Constructor
        public TaskDto()
        {
            // Initialize all string properties that V2 Task entity has as non-nullable strings
            Name = string.Empty;
            Status = "Todo"; // Default status
            StatusName = string.Empty; // 新增：状态显示名称
            TaskType = "Normal"; // Default type
            Description = string.Empty;
            Priority = "Medium";
            PriorityText = string.Empty; // 新增：优先级显示名称
            Tags = string.Empty;
            AssigneeUserName = string.Empty;
            AssigneeAvatarUrl = string.Empty;
            CreatorUserName = string.Empty;
            CreatorUserAvatarUrl = string.Empty;
            AssetName = string.Empty;
            AssetNumber = string.Empty;
            LocationName = string.Empty;
            ParentTaskName = string.Empty;
            PeriodicScheduleName = string.Empty;
            PDCAStage = string.Empty;
            ParticipantUserIds = string.Empty;
        }
    }

    /// <summary>
    /// 用户基本信息DTO (保持不变)
    /// </summary>
    public class UserBasicDto
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 用户名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 头像URL
        /// </summary>
        public string AvatarUrl { get; set; } = string.Empty;
    }

    /// <summary>
    /// 任务负责人信息 DTO
    /// </summary>
    public class AssigneeDto
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// 用户名称
        /// </summary>
        public string UserName { get; set; } = string.Empty;

        /// <summary>
        /// 头像URL
        /// </summary>
        public string AvatarUrl { get; set; } = string.Empty;

        /// <summary>
        /// 分配类型 (Responsible-负责人, Participant-参与者)
        /// </summary>
        public string AssignmentType { get; set; } = string.Empty;

        /// <summary>
        /// 角色 (Primary-主负责人, Collaborator-协作者)
        /// </summary>
        public string Role { get; set; } = string.Empty;

        /// <summary>
        /// 分配时间
        /// </summary>
        public DateTime AssignmentTimestamp { get; set; }
    }
} 