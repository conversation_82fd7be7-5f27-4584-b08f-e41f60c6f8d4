// File: Application/Common/Dtos/PaginationQuery.cs
// Description: 分页查询基类

using System.ComponentModel.DataAnnotations;

namespace ItAssetsSystem.Application.Common.Dtos
{
    /// <summary>
    /// 分页查询基类
    /// </summary>
    public class PaginationQuery
    {
        private int _pageIndex = 1;
        private int _pageSize = 10;
        
        /// <summary>
        /// 页码，从1开始
        /// </summary>
        [Range(1, int.MaxValue, ErrorMessage = "页码必须大于0")]
        public int PageIndex
        {
            get => _pageIndex;
            set => _pageIndex = value <= 0 ? 1 : value;
        }
        
        /// <summary>
        /// 每页数量
        /// </summary>
        [Range(1, 100, ErrorMessage = "每页数量必须在1-100之间")]
        public int PageSize
        {
            get => _pageSize;
            set => _pageSize = (value <= 0 || value > 100) ? 10 : value;
        }
        
        /// <summary>
        /// 排序字段
        /// </summary>
        public string SortBy { get; set; }
        
        /// <summary>
        /// 排序方向，默认升序
        /// </summary>
        public string SortDirection { get; set; } = "asc";
    }
} 