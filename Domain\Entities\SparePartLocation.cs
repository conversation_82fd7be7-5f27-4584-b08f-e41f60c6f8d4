// File: Domain/Entities/SparePartLocation.cs
// Description: 备品备件库位实体类

using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using ItAssetsSystem.Models;

namespace ItAssetsSystem.Domain.Entities
{
    /// <summary>
    /// 备品备件库位实体
    /// </summary>
    [Table("spare_part_locations")]
    public class SparePartLocation : IAuditableEntity
    {
        /// <summary>
        /// 库位ID
        /// </summary>
        [Key]
        [Column("id")]
        public long Id { get; set; }
        
        /// <summary>
        /// 库位编号，如"A-1-1-1"
        /// </summary>
        [Required]
        [Column("code")]
        [StringLength(50)]
        public string Code { get; set; }
        
        /// <summary>
        /// 库位名称，如"A区-1号货架-1层-1格"
        /// </summary>
        [Required]
        [Column("name")]
        [StringLength(100)]
        public string Name { get; set; }
        
        /// <summary>
        /// 区域标识，如"A区", "B区"
        /// </summary>
        [Required]
        [Column("area")]
        [StringLength(20)]
        public string Area { get; set; }
        
        /// <summary>
        /// 库位描述
        /// </summary>
        [Column("description")]
        [StringLength(200)]
        public string Description { get; set; }
        
        /// <summary>
        /// 创建人ID
        /// </summary>
        [NotMapped]
        [Column("creator_user_id")]
        public int CreatorUserId { get; set; }
        
        /// <summary>
        /// 创建时间
        /// </summary>
        [Column("created_at")]
        public DateTime CreatedAt { get; set; }
        
        /// <summary>
        /// 最后修改时间
        /// </summary>
        [Column("updated_at")]
        public DateTime UpdatedAt { get; set; }
        
        /// <summary>
        /// 是否启用
        /// </summary>
        [Column("is_active")]
        public bool IsActive { get; set; } = true;
        
        /// <summary>
        /// 排序号
        /// </summary>
        [NotMapped]
        [Column("sort_order")]
        public int SortOrder { get; set; }
        
        /// <summary>
        /// 库位中的备件集合
        /// </summary>
        public virtual ICollection<SparePart> SpareParts { get; set; } = new List<SparePart>();
        
        /// <summary>
        /// 库位中的出入库记录
        /// </summary>
        public virtual ICollection<SparePartTransaction> Transactions { get; set; } = new List<SparePartTransaction>();
    }
} 