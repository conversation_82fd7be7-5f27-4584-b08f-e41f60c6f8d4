// IT资产管理系统 - 位置控制器
// 文件路径: /Controllers/LocationController.cs
// 功能: 提供位置相关API

using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using ItAssetsSystem.Models.Entities;
using ItAssetsSystem.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using System.Threading.Tasks;
using System.Security.Claims;

namespace ItAssetsSystem.Controllers
{
    /// <summary>
    /// 位置控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class LocationController : ControllerBase
    {
        private readonly ILogger<LocationController> _logger;
        private readonly AppDbContext _context;

        public LocationController(ILogger<LocationController> logger, AppDbContext context)
        {
            _logger = logger;
            _context = context;
        }

        /// <summary>
        /// 位置树节点
        /// </summary>
        private class LocationTreeNode
        {
            public int id { get; set; }
            public string code { get; set; }
            public string name { get; set; }
            public int type { get; set; }
            public int? parentId { get; set; }
            public string path { get; set; }
            public string description { get; set; }
            public bool isActive { get; set; }
            public List<LocationTreeNode> children { get; set; } = new List<LocationTreeNode>();
        }

        /// <summary>
        /// 获取位置树形结构
        /// </summary>
        /// <returns>位置树</returns>
        [HttpGet("tree")]
        public async Task<IActionResult> GetTree()
        {
            _logger.LogInformation("获取位置树");
            try
            {
                // 获取所有位置
                var locations = await _context.Locations
                    .Select(l => new LocationTreeNode
                    {
                        id = l.Id,
                        code = l.Code,
                        name = l.Name,
                        type = l.Type,
                        parentId = l.ParentId,
                        path = l.Path,
                        description = l.Description,
                        isActive = l.IsActive
                    })
                    .ToListAsync();

                if (!locations.Any())
                {
                    return Ok(new { success = true, data = new List<LocationTreeNode>(), message = "暂无位置数据" });
                }

                // 构建树形结构
                var tree = BuildLocationTree(locations);

                return Ok(new { success = true, data = tree });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取位置树出错");
                return StatusCode(500, new { success = false, message = "获取位置树出错: " + ex.Message });
            }
        }

        /// <summary>
        /// 新增位置
        /// </summary>
        /// <param name="location">位置信息</param>
        /// <returns>新增结果</returns>
        [HttpPost]
        public async Task<IActionResult> Create([FromBody] Location location)
        {
            _logger.LogInformation("新增位置");
            try
            {
                // 验证必填字段
                if (string.IsNullOrEmpty(location.Name))
                {
                    return BadRequest(new { success = false, message = "位置名称不能为空" });
                }

                // 如果指定了部门，检查部门是否存在
                if (location.DefaultDepartmentId.HasValue)
                {
                    var department = await _context.Departments.FindAsync(location.DefaultDepartmentId.Value);
                    if (department == null)
                    {
                        return BadRequest(new { success = false, message = "指定的部门不存在" });
                    }
                }

                // 如果指定了部门负责人，检查负责人是否存在
                if (location.ManagerId.HasValue)
                {
                    var manager = await _context.Users.FindAsync(location.ManagerId.Value);
                    if (manager == null)
                    {
                        return BadRequest(new { success = false, message = "指定的部门负责人不存在" });
                    }
                }

                // 如果指定了父级，检查父级是否存在
                if (location.ParentId.HasValue)
                {
                    var parent = await _context.Locations.FindAsync(location.ParentId.Value);
                    if (parent == null)
                    {
                        return BadRequest(new { success = false, message = "父级位置不存在" });
                    }

                    // 验证层级关系
                    if (!IsValidLocationType(parent.Type, location.Type))
                    {
                        return BadRequest(new { success = false, message = "位置层级关系不正确" });
                    }

                    // 设置路径
                    location.Path = parent.Path + "," + location.ParentId.Value;
                }
                else
                {
                    // 根节点
                    location.Path = "0";
                    
                    // 根节点必须是工厂类型
                    if (location.Type != 0)
                    {
                        return BadRequest(new { success = false, message = "根节点必须是工厂类型" });
                    }
                }

                // 如果没有提供编码，则自动生成
                if (string.IsNullOrEmpty(location.Code))
                {
                    location.Code = await GenerateLocationCode(location.Type, location.ParentId);
                }
                else
                {
                    // 检查编码是否已存在
                    var existingLocation = await _context.Locations.FirstOrDefaultAsync(l => l.Code == location.Code);
                    if (existingLocation != null)
                    {
                        return BadRequest(new { success = false, message = "位置编码已存在" });
                    }
                }

                // 设置默认值
                location.IsActive = true;
                location.CreatedAt = DateTime.Now;
                location.UpdatedAt = DateTime.Now;

                // 保存位置
                _context.Locations.Add(location);
                await _context.SaveChangesAsync();

                // 如果有使用人，添加位置用户关联
                if (location.LocationUsers != null && location.LocationUsers.Any())
                {
                    foreach (var locationUser in location.LocationUsers)
                    {
                        locationUser.LocationId = location.Id;
                        locationUser.IsActive = true;
                        locationUser.CreatedAt = DateTime.Now;
                        locationUser.UpdatedAt = DateTime.Now;
                    }
                    await _context.SaveChangesAsync();
                }

                return Ok(new { success = true, data = location, message = "位置创建成功" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "新增位置出错");
                return StatusCode(500, new { success = false, message = "新增位置出错: " + ex.Message });
            }
        }

        /// <summary>
        /// 更新位置
        /// </summary>
        /// <param name="id">位置ID</param>
        /// <param name="location">位置信息</param>
        /// <returns>更新结果</returns>
        [HttpPut("{id}")]
        public async Task<IActionResult> Update(int id, [FromBody] Location location)
        {
            _logger.LogInformation($"更新位置ID: {id}");
            try
            {
                // 验证必填字段
                if (string.IsNullOrEmpty(location.Name))
                {
                    return BadRequest(new { success = false, message = "位置名称不能为空" });
                }

                // 检查位置是否存在 - 不通过Include加载LocationUsers避免EF导航问题
                var existingLocation = await _context.Locations
                    .Include(l => l.Children)
                    .FirstOrDefaultAsync(l => l.Id == id);
                    
                if (existingLocation == null)
                {
                    return NotFound(new { success = false, message = $"未找到ID为{id}的位置" });
                }

                // 如果指定了部门，检查部门是否存在
                if (location.DefaultDepartmentId.HasValue)
                {
                    var department = await _context.Departments.FindAsync(location.DefaultDepartmentId.Value);
                    if (department == null)
                    {
                        return BadRequest(new { success = false, message = "指定的部门不存在" });
                    }
                }

                // 如果指定了部门负责人，检查负责人是否存在
                if (location.ManagerId.HasValue)
                {
                    var personnel = await _context.Personnel.FindAsync(location.ManagerId.Value);
                    if (personnel == null)
                    {
                        return BadRequest(new { success = false, message = "指定的部门负责人不存在" });
                    }
                }

                // 不允许修改类型和父级
                if (existingLocation.Type != location.Type)
                {
                    return BadRequest(new { success = false, message = "不允许修改位置类型" });
                }
                if (existingLocation.ParentId != location.ParentId)
                {
                    return BadRequest(new { success = false, message = "不允许修改父级位置" });
                }

                // 处理位置编码
                if (string.IsNullOrEmpty(location.Code))
                {
                    // 如果没有提供编码，保持原有编码
                    location.Code = existingLocation.Code;
                }
                else if (location.Code != existingLocation.Code)
                {
                    // 如果提供了新的编码且与原编码不同，检查是否重复
                    var codeExists = await _context.Locations
                        .AnyAsync(l => l.Code == location.Code && l.Id != id);
                    if (codeExists)
                    {
                        return BadRequest(new { success = false, message = "位置编码已存在" });
                    }
                }

                // 更新位置信息
                existingLocation.Name = location.Name;
                existingLocation.Code = location.Code;
                existingLocation.Description = location.Description ?? string.Empty;
                existingLocation.DefaultDepartmentId = location.DefaultDepartmentId;
                existingLocation.ManagerId = location.ManagerId;
                existingLocation.IsActive = location.IsActive;
                existingLocation.UpdatedAt = DateTime.Now;

                // 保存位置更新
                await _context.SaveChangesAsync();

                // 更新位置用户关联 - 使用SQL语句而非EF导航属性
                if (location.LocationUsers != null && location.LocationUsers.Any())
                {
                    // 首先删除现有关联
                    await _context.Database.ExecuteSqlRawAsync(
                        "DELETE FROM locationusers WHERE location_id = {0}", id);

                    // 手动构建要插入的记录列表
                    var now = DateTime.Now;
                    var locationUsers = new List<LocationUser>();
                    foreach (var user in location.LocationUsers)
                    {
                        locationUsers.Add(new LocationUser
                        {
                            LocationId = id,
                            PersonnelId = user.PersonnelId,
                            UserType = user.UserType,
                            CreatedAt = now
                        });
                    }

                    // 批量添加新关联
                    _context.LocationUsers.AddRange(locationUsers);
                    await _context.SaveChangesAsync();
                }

                return Ok(new { success = true, data = existingLocation, message = "位置更新成功" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"更新位置ID {id} 出错");
                return StatusCode(500, new { success = false, message = $"更新位置ID {id} 出错: " + ex.Message });
            }
        }

        /// <summary>
        /// 删除位置
        /// </summary>
        /// <param name="id">位置ID</param>
        /// <returns>删除结果</returns>
        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(int id)
        {
            _logger.LogInformation($"删除位置 {id}");
            try
            {
                var location = await _context.Locations
                    .Include(l => l.Assets)
                    .FirstOrDefaultAsync(l => l.Id == id);

                if (location == null)
                {
                    return NotFound(new { success = false, message = "位置不存在" });
                }

                // 检查是否有子位置
                var hasChildren = await _context.Locations.AnyAsync(l => l.ParentId == id);
                if (hasChildren)
                {
                    return BadRequest(new { success = false, message = "该位置下还有子位置，无法删除" });
                }

                // 检查是否有关联的资产
                if (location.Assets != null && location.Assets.Any())
                {
                    return BadRequest(new { success = false, message = "该位置下还有资产，无法删除" });
                }

                // 删除位置用户关联
                var locationUsers = await _context.LocationUsers.Where(lu => lu.LocationId == id).ToListAsync();
                if (locationUsers.Any())
                {
                    _context.LocationUsers.RemoveRange(locationUsers);
                }

                // 删除位置
                _context.Locations.Remove(location);
                await _context.SaveChangesAsync();

                return Ok(new { success = true, message = "位置删除成功" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除位置出错");
                return StatusCode(500, new { success = false, message = "删除位置出错: " + ex.Message });
            }
        }

        /// <summary>
        /// 构建位置树
        /// </summary>
        private static List<LocationTreeNode> BuildLocationTree(List<LocationTreeNode> locations, int? parentId = null)
        {
            var result = new List<LocationTreeNode>();
            
            foreach (var location in locations)
            {
                if (location.parentId == parentId)
                {
                    // 递归获取子节点
                    location.children = BuildLocationTree(locations, location.id);
                    result.Add(location);
                }
            }
            
            return result;
        }

        /// <summary>
        /// 验证位置类型层级关系是否正确
        /// </summary>
        private static bool IsValidLocationType(int parentType, int childType)
        {
            // 验证层级关系：工厂->产线->工序->工位->设备位置
            return childType == parentType + 1;
        }

        /// <summary>
        /// 生成位置编码
        /// </summary>
        private async Task<string> GenerateLocationCode(int type, int? parentId)
        {
            // 获取同级最大编码
            var prefix = GetLocationCodePrefix(type);
            var lastCode = await _context.Locations
                .Where(l => l.ParentId == parentId && l.Type == type)
                .Select(l => l.Code)
                .OrderByDescending(c => c)
                .FirstOrDefaultAsync();

            if (string.IsNullOrEmpty(lastCode))
            {
                return $"{prefix}001";
            }

            // 提取编号并加1
            var number = int.Parse(lastCode.Substring(prefix.Length)) + 1;
            return $"{prefix}{number:D3}";
        }

        /// <summary>
        /// 获取位置编码前缀
        /// </summary>
        private static string GetLocationCodePrefix(int type)
        {
            return type switch
            {
                0 => "F", // 工厂
                1 => "L", // 产线
                2 => "P", // 工序
                3 => "W", // 工位
                4 => "E", // 设备位置
                _ => "X"
            };
        }

        /// <summary>
        /// 初始化根位置
        /// </summary>
        [HttpPost("init-root")]
        public async Task<IActionResult> InitializeRootLocation()
        {
            try
            {
                // 检查是否已存在根位置
                var existingRoot = await _context.Locations
                    .FirstOrDefaultAsync(l => l.ParentId == null);

                if (existingRoot != null)
                {
                    return BadRequest(new { success = false, message = "根位置已存在" });
                }

                // 创建根位置
                var rootLocation = new Location
                {
                    Code = "HQ",
                    Name = "总部",
                    Type = 0, // 工厂类型
                    Path = "0",
                    Description = "公司总部",
                    IsActive = true,
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now
                };

                _context.Locations.Add(rootLocation);
                await _context.SaveChangesAsync();

                return Ok(new { 
                    success = true, 
                    message = "根位置创建成功",
                    data = new {
                        id = rootLocation.Id,
                        code = rootLocation.Code,
                        name = rootLocation.Name
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "初始化根位置失败");
                return StatusCode(500, new { success = false, message = "初始化根位置失败: " + ex.Message });
            }
        }

        /// <summary>
        /// 获取所有位置（测试用）
        /// </summary>
        [HttpGet("list")]
        public async Task<IActionResult> GetList()
        {
            try
            {
                var locations = await _context.Locations
                    .Select(l => new
                    {
                        l.Id,
                        l.Code,
                        l.Name,
                        l.Type,
                        l.ParentId,
                        l.Path,
                        l.Description,
                        l.IsActive
                    })
                    .ToListAsync();

                return Ok(new { 
                    success = true, 
                    data = locations,
                    count = locations.Count,
                    message = "获取位置列表成功" 
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取位置列表出错");
                return StatusCode(500, new { success = false, message = "获取位置列表出错: " + ex.Message });
            }
        }

        /// <summary>
        /// 获取位置关联的资产列表
        /// </summary>
        /// <param name="locationId">位置ID</param>
        /// <param name="pageIndex">页码</param>
        /// <param name="pageSize">每页大小</param>
        /// <returns>资产列表</returns>
        [HttpGet("{locationId}/assets")]
        public async Task<IActionResult> GetLocationAssets(
            int locationId,
            [FromQuery] int pageIndex = 1,
            [FromQuery] int pageSize = 10)
        {
            _logger.LogInformation($"获取位置ID: {locationId} 的关联资产");
            try
            {
                // 验证位置是否存在
                var location = await _context.Locations.FindAsync(locationId);
                if (location == null)
                {
                    return NotFound(new { success = false, message = $"未找到ID为{locationId}的位置" });
                }
                
                // 验证分页参数
                if (pageIndex < 1) pageIndex = 1;
                if (pageSize < 1) pageSize = 10;
                else if (pageSize > 100) pageSize = 100;
                
                // 使用SQL查询避免导航属性问题
                var connection = _context.Database.GetDbConnection();
                if (connection.State != System.Data.ConnectionState.Open)
                {
                    await connection.OpenAsync();
                }
                
                var assetList = new List<Dictionary<string, object>>();
                int totalCount = 0;
                
                // 查询总数
                using (var command = connection.CreateCommand())
                {
                    command.CommandText = "SELECT COUNT(*) FROM assets WHERE LocationId = @locationId";
                    var parameter = command.CreateParameter();
                    parameter.ParameterName = "@locationId";
                    parameter.Value = locationId;
                    command.Parameters.Add(parameter);
                    
                    totalCount = Convert.ToInt32(await command.ExecuteScalarAsync());
                }

                // 执行分页查询
                using (var command = connection.CreateCommand())
                {
                    command.CommandText = @"
                        SELECT 
                            a.Id, a.AssetCode, a.Name, a.AssetTypeId, 
                            at.Name AS AssetTypeName, a.SerialNumber, a.Model, a.Brand, 
                            a.PurchaseDate, a.WarrantyExpireDate, a.Price, 
                            a.LocationId, l.Name AS LocationName, l.Path AS LocationPath, l.Type AS LocationType,
                            l.DefaultDepartmentId, d.Name AS DepartmentName,
                            a.Status, a.Notes, a.CreatedAt, a.UpdatedAt
                        FROM 
                            assets a
                            LEFT JOIN assettypes at ON a.AssetTypeId = at.Id
                            LEFT JOIN locations l ON a.LocationId = l.Id
                            LEFT JOIN departments d ON l.DefaultDepartmentId = d.Id
                        WHERE 
                            a.LocationId = @locationId
                        ORDER BY 
                            a.CreatedAt DESC
                        LIMIT 
                            @limit OFFSET @offset";
                    
                    var locationIdParam = command.CreateParameter();
                    locationIdParam.ParameterName = "@locationId";
                    locationIdParam.Value = locationId;
                    command.Parameters.Add(locationIdParam);
                    
                    var limitParam = command.CreateParameter();
                    limitParam.ParameterName = "@limit";
                    limitParam.Value = pageSize;
                    command.Parameters.Add(limitParam);
                    
                    var offsetParam = command.CreateParameter();
                    offsetParam.ParameterName = "@offset";
                    offsetParam.Value = (pageIndex - 1) * pageSize;
                    command.Parameters.Add(offsetParam);
                    
                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            var asset = new Dictionary<string, object>();
                            
                            asset["id"] = reader.GetInt32(reader.GetOrdinal("Id"));
                            asset["assetCode"] = reader.IsDBNull(reader.GetOrdinal("AssetCode")) ? null : reader.GetString(reader.GetOrdinal("AssetCode"));
                            asset["name"] = reader.IsDBNull(reader.GetOrdinal("Name")) ? null : reader.GetString(reader.GetOrdinal("Name"));
                            asset["assetTypeId"] = reader.GetInt32(reader.GetOrdinal("AssetTypeId"));
                            asset["assetTypeName"] = reader.IsDBNull(reader.GetOrdinal("AssetTypeName")) ? null : reader.GetString(reader.GetOrdinal("AssetTypeName"));
                            asset["serialNumber"] = reader.IsDBNull(reader.GetOrdinal("SerialNumber")) ? null : reader.GetString(reader.GetOrdinal("SerialNumber"));
                            asset["model"] = reader.IsDBNull(reader.GetOrdinal("Model")) ? null : reader.GetString(reader.GetOrdinal("Model"));
                            asset["brand"] = reader.IsDBNull(reader.GetOrdinal("Brand")) ? null : reader.GetString(reader.GetOrdinal("Brand"));
                            asset["purchaseDate"] = reader.IsDBNull(reader.GetOrdinal("PurchaseDate")) ? null : reader.GetDateTime(reader.GetOrdinal("PurchaseDate"));
                            asset["warrantyExpireDate"] = reader.IsDBNull(reader.GetOrdinal("WarrantyExpireDate")) ? null : reader.GetDateTime(reader.GetOrdinal("WarrantyExpireDate"));
                            asset["price"] = reader.IsDBNull(reader.GetOrdinal("Price")) ? null : reader.GetDecimal(reader.GetOrdinal("Price"));
                            
                            asset["locationId"] = reader.GetInt32(reader.GetOrdinal("LocationId"));
                            asset["locationName"] = reader.IsDBNull(reader.GetOrdinal("LocationName")) ? null : reader.GetString(reader.GetOrdinal("LocationName"));
                            asset["locationPath"] = reader.IsDBNull(reader.GetOrdinal("LocationPath")) ? null : reader.GetString(reader.GetOrdinal("LocationPath"));
                            int locationType = reader.IsDBNull(reader.GetOrdinal("LocationType")) ? 0 : reader.GetInt32(reader.GetOrdinal("LocationType"));
                            asset["locationType"] = locationType;
                            asset["locationTypeName"] = GetLocationTypeName(locationType);
                            
                            asset["departmentId"] = reader.IsDBNull(reader.GetOrdinal("DefaultDepartmentId")) ? null : reader.GetInt32(reader.GetOrdinal("DefaultDepartmentId"));
                            asset["departmentName"] = reader.IsDBNull(reader.GetOrdinal("DepartmentName")) ? null : reader.GetString(reader.GetOrdinal("DepartmentName"));
                            
                            asset["status"] = reader.GetInt32(reader.GetOrdinal("Status"));
                            asset["statusName"] = GetAssetStatusName(reader.GetInt32(reader.GetOrdinal("Status")));
                            asset["notes"] = reader.IsDBNull(reader.GetOrdinal("Notes")) ? null : reader.GetString(reader.GetOrdinal("Notes"));
                            asset["createdAt"] = reader.GetDateTime(reader.GetOrdinal("CreatedAt"));
                            asset["updatedAt"] = reader.GetDateTime(reader.GetOrdinal("UpdatedAt"));
                            
                            assetList.Add(asset);
                        }
                    }
                }
                
                return Ok(new { 
                    success = true, 
                    data = new { 
                        total = totalCount,
                        items = assetList
                    }, 
                    message = "获取位置关联资产成功" 
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取位置ID: {locationId} 的关联资产出错");
                return StatusCode(500, new { success = false, message = $"获取位置关联资产出错: {ex.Message}" });
            }
        }
        
        /// <summary>
        /// 获取工位层级位置列表 (Type=4)
        /// </summary>
        /// <returns>工位层级位置列表</returns>
        [HttpGet("workstations")]
        public async Task<IActionResult> GetWorkstationLocations()
        {
            _logger.LogInformation("获取工位层级位置列表");
            try
            {
                // 使用SQL查询避免导航属性问题
                var connection = _context.Database.GetDbConnection();
                if (connection.State != System.Data.ConnectionState.Open)
                {
                    await connection.OpenAsync();
                }

                var locationList = new List<Dictionary<string, object>>();

                // 执行查询
                using (var command = connection.CreateCommand())
                {
                    command.CommandText = @"
                        SELECT 
                            l.Id, l.Code, l.Name, l.Type, l.ParentId, 
                            l.Path, l.Description, l.IsActive, l.DefaultDepartmentId,
                            d.Name AS DepartmentName
                        FROM 
                            locations l
                            LEFT JOIN departments d ON l.DefaultDepartmentId = d.Id
                        WHERE 
                            l.Type = 4 AND l.IsActive = 1
                        ORDER BY 
                            l.Path";
                    
                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            var location = new Dictionary<string, object>();
                            
                            location["id"] = reader.GetInt32(reader.GetOrdinal("Id"));
                            location["code"] = reader.IsDBNull(reader.GetOrdinal("Code")) ? null : reader.GetString(reader.GetOrdinal("Code"));
                            location["name"] = reader.IsDBNull(reader.GetOrdinal("Name")) ? null : reader.GetString(reader.GetOrdinal("Name"));
                            location["type"] = reader.GetInt32(reader.GetOrdinal("Type"));
                            location["typeName"] = GetLocationTypeName(reader.GetInt32(reader.GetOrdinal("Type")));
                            location["parentId"] = reader.IsDBNull(reader.GetOrdinal("ParentId")) ? null : reader.GetInt32(reader.GetOrdinal("ParentId"));
                            location["path"] = reader.IsDBNull(reader.GetOrdinal("Path")) ? null : reader.GetString(reader.GetOrdinal("Path"));
                            location["description"] = reader.IsDBNull(reader.GetOrdinal("Description")) ? null : reader.GetString(reader.GetOrdinal("Description"));
                            location["isActive"] = reader.GetBoolean(reader.GetOrdinal("IsActive"));
                            location["defaultDepartmentId"] = reader.IsDBNull(reader.GetOrdinal("DefaultDepartmentId")) ? null : reader.GetInt32(reader.GetOrdinal("DefaultDepartmentId"));
                            location["departmentName"] = reader.IsDBNull(reader.GetOrdinal("DepartmentName")) ? null : reader.GetString(reader.GetOrdinal("DepartmentName"));
                            
                            // 解析路径获取完整位置名称
                            string path = reader.IsDBNull(reader.GetOrdinal("Path")) ? "" : reader.GetString(reader.GetOrdinal("Path"));
                            string locationName = reader.IsDBNull(reader.GetOrdinal("Name")) ? "未知位置" : reader.GetString(reader.GetOrdinal("Name"));
                            location["fullName"] = !string.IsNullOrEmpty(path) ? await GetLocationFullNameFromPathAsync(path) : locationName;
                            
                            locationList.Add(location);
                        }
                    }
                }
                
                return Ok(new { 
                    success = true, 
                    data = locationList, 
                    message = "获取工位层级位置列表成功" 
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取工位层级位置列表出错");
                return StatusCode(500, new { success = false, message = "获取工位层级位置列表出错: " + ex.Message });
            }
        }
        
        /// <summary>
        /// 获取位置类型名称
        /// </summary>
        private static string GetLocationTypeName(int type)
        {
            return type switch
            {
                0 => "工厂",
                1 => "车间",
                2 => "产线",
                3 => "工序",
                4 => "工位",
                _ => "未知类型"
            };
        }
        
        /// <summary>
        /// 获取资产状态名称
        /// </summary>
        private static string GetAssetStatusName(int status)
        {
            return status switch
            {
                0 => "闲置",
                1 => "在用",
                2 => "维修中",
                3 => "报废",
                _ => "未知状态"
            };
        }
        
        /// <summary>
        /// 根据Path获取位置的完整名称
        /// </summary>
        private async Task<string> GetLocationFullNameFromPathAsync(string path)
        {
            if (string.IsNullOrEmpty(path)) return string.Empty;
            
            try
            {
                var pathIds = new List<int>();
                
                foreach (var segment in path.Split('/'))
                {
                    if (!string.IsNullOrEmpty(segment) && segment != "0" && int.TryParse(segment, out int id))
                    {
                        pathIds.Add(id);
                    }
                }
                
                if (pathIds.Count == 0) return string.Empty;
                
                var locationNames = new List<string>();
                
                foreach (var id in pathIds)
                {
                    var location = await _context.Locations.FindAsync(id);
                    if (location != null)
                    {
                        locationNames.Add(location.Name);
                    }
                }
                
                return string.Join(" / ", locationNames);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"解析位置路径出错: {path}");
                return "路径解析错误";
            }
        }

        /// <summary>
        /// 获取位置下拉列表
        /// </summary>
        /// <param name="type">位置类型（可选），默认4表示工位</param>
        /// <param name="includeAll">是否包含所有层级，默认false</param>
        /// <returns>位置列表</returns>
        [HttpGet("dropdown")]
        public async Task<IActionResult> GetLocationsForDropdown(
            [FromQuery] int type = 4,
            [FromQuery] bool includeAll = false)
        {
            _logger.LogInformation($"获取位置下拉列表，类型: {type}, 包含所有层级: {includeAll}");
            try
            {
                IQueryable<Location> query = _context.Locations
                    .Include(l => l.Department) // For DepartmentName
                    .Where(l => l.IsActive);

                if (!includeAll)
                {
                    query = query.Where(l => l.Type == type);
                }

                var locationsFromDb = await query.OrderBy(l => l.Path).ToListAsync(); // Load all into memory

                if (!locationsFromDb.Any())
                {
                    return Ok(new { success = true, data = new List<object>(), message = "暂无符合条件的位置数据" });
                }

                // For resolving path names, get all location names into a dictionary for quick lookup
                var allLocationNames = await _context.Locations
                                            .Select(l => new { l.Id, l.Name })
                                            .ToDictionaryAsync(l => l.Id, l => l.Name);

                var resultList = new List<object>();
                foreach (var loc in locationsFromDb)
                {
                    string fullName = loc.Name; // Default to current name
                    if (!string.IsNullOrEmpty(loc.Path))
                    {
                        var pathIds = loc.Path.Split(new[] { '/', ',' }, StringSplitOptions.RemoveEmptyEntries)
                                            .Where(s => s != "0" && int.TryParse(s, out _))
                                            .Select(int.Parse);
                        
                        var namesForPath = new List<string>();
                        foreach (var idInPath in pathIds)
                        {
                            if (allLocationNames.TryGetValue(idInPath, out var nameFromDict))
                            {
                                namesForPath.Add(nameFromDict);
                            }
                            // Optionally, handle cases where an ID in the path might not be found in allLocationNames
                            // else { namesForPath.Add("?"); /* Placeholder for missing name */ }
                        }
                        if (namesForPath.Any())
                        {
                           fullName = string.Join(" / ", namesForPath);
                        }
                    }

                    resultList.Add(new {
                        id = loc.Id,
                        code = loc.Code,
                        name = loc.Name,
                        type = loc.Type,
                        typeName = GetLocationTypeName(loc.Type), // Assuming GetLocationTypeName is a local static/instance method
                        parentId = loc.ParentId,
                        path = loc.Path,
                        description = loc.Description,
                        isActive = loc.IsActive,
                        defaultDepartmentId = loc.DefaultDepartmentId,
                        departmentName = loc.Department?.Name, // Safely access DepartmentName
                        fullName = fullName + " (" + loc.Name + ")"
                    });
                }
                
                return Ok(new { success = true, data = resultList });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取位置下拉列表出错");
                return StatusCode(500, new { success = false, message = "获取位置下拉列表出错: " + ex.Message });
            }
        }

        /// <summary>
        /// 关联资产到位置
        /// </summary>
        /// <param name="locationId">位置ID</param>
        /// <param name="model">关联模型</param>
        /// <returns>操作结果</returns>
        [HttpPost("{locationId}/assets")]
        public async Task<IActionResult> RelateAssets(int locationId, [FromBody] LocationAssetRelateModel model)
        {
            _logger.LogInformation($"关联资产到位置，位置ID: {locationId}");
            try
            {
                // 检查位置是否存在
                var location = await _context.Locations.FindAsync(locationId);
                if (location == null)
                {
                    return NotFound(new { success = false, message = $"未找到ID为{locationId}的位置" });
                }
                
                // 检查关联类型是否有效
                if (!Enum.IsDefined(typeof(RelationType), model.RelationType))
                {
                    return BadRequest(new { success = false, message = "无效的关联类型" });
                }
                
                // 获取要关联的资产
                var assets = await _context.Assets
                    .Where(a => model.AssetIds.Contains(a.Id))
                    .ToListAsync();
                
                if (assets.Count == 0)
                {
                    return BadRequest(new { success = false, message = "未找到要关联的资产" });
                }
                
                // 获取当前用户
                var (operatorId, operatorName) = await GetCurrentUserInfoAsync();
                
                // 批量更新资产位置
                foreach (var asset in assets)
                {
                    // 记录原始位置
                    var oldLocationId = asset.LocationId;
                    var oldLocation = oldLocationId.HasValue ? await _context.Locations.FindAsync(oldLocationId) : null;
                    
                    // 更新资产位置
                    asset.LocationId = locationId;
                    // 设置关联类型（如果模型中没有此属性，则注释掉下一行）
                    // asset.LocationRelationType = (int)model.RelationType;
                    asset.UpdatedAt = DateTime.Now;
                    
                    // 如果有备注，更新资产备注
                    if (!string.IsNullOrEmpty(model.Notes))
                    {
                        asset.Notes = (string.IsNullOrEmpty(asset.Notes) ? "" : asset.Notes + "\n") + model.Notes;
                    }
                    
                    // 创建位置历史记录
                    await _context.LocationHistories.AddAsync(new LocationHistory
                    {
                        AssetId = asset.Id,
                        ChangeType = 0, // 转移
                        OldLocationId = oldLocationId,
                        NewLocationId = locationId,
                        OperatorId = operatorId,
                        Notes = model.Notes,
                        ChangeTime = DateTime.Now,
                        CreatedAt = DateTime.Now
                    });
                    
                    // 创建资产历史记录
                    string description = $"位置变更: {(oldLocation != null ? oldLocation.Name : "无")} → {location.Name}";
                    if (!string.IsNullOrEmpty(model.Notes))
                    {
                        description += $", 备注: {model.Notes}";
                    }
                    
                    await _context.AssetHistories.AddAsync(new AssetHistory
                    {
                        AssetId = asset.Id,
                        OperationType = 4, // 位置变更
                        OperatorId = operatorId,
                        OperationTime = DateTime.Now,
                        Description = description
                    });
                }
                
                // 保存更改
                await _context.SaveChangesAsync();
                
                // 返回关联成功的资产信息
                var relatedAssets = assets.Select(a => new
                {
                    a.Id,
                    a.AssetCode,
                    a.Name,
                    a.SerialNumber,
                    LocationId = locationId,
                    LocationName = location.Name,
                    RelationType = (int)model.RelationType,
                    RelationTypeName = GetRelationTypeName((int)model.RelationType),
                    UpdatedAt = DateTime.Now
                }).ToList();
                
                return Ok(new { 
                    success = true, 
                    data = relatedAssets, 
                    count = relatedAssets.Count,
                    message = $"成功关联{relatedAssets.Count}个资产到位置" 
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"关联资产到位置出错，位置ID: {locationId}");
                return StatusCode(500, new { success = false, message = $"关联资产到位置出错: {ex.Message}" });
            }
        }

        /// <summary>
        /// 解除资产与位置的关联
        /// </summary>
        /// <param name="locationId">位置ID</param>
        /// <param name="model">解除关联模型</param>
        /// <returns>操作结果</returns>
        [HttpPost("{locationId}/assets/unrelate")]
        public async Task<IActionResult> UnrelateAssets(int locationId, [FromBody] LocationAssetUnrelateModel model)
        {
            _logger.LogInformation($"解除资产与位置的关联，位置ID: {locationId}");
            try
            {
                // 检查位置是否存在
                var location = await _context.Locations.FindAsync(locationId);
                if (location == null)
                {
                    return NotFound(new { success = false, message = $"未找到ID为{locationId}的位置" });
                }
                
                // 获取要解除关联的资产
                var assets = await _context.Assets
                    .Where(a => a.LocationId == locationId && model.AssetIds.Contains(a.Id))
                    .ToListAsync();
                
                if (assets.Count == 0)
                {
                    return BadRequest(new { success = false, message = "未找到要解除关联的资产" });
                }
                
                // 获取当前用户
                var (operatorId, operatorName) = await GetCurrentUserInfoAsync();
                
                // 批量更新资产位置
                foreach (var asset in assets)
                {
                    // 记录原始位置
                    var oldLocationId = asset.LocationId;
                    
                    // 更新资产位置为NULL
                    asset.LocationId = null;
                    // 设置关联类型为NULL（如果模型中没有此属性，则注释掉下一行）
                    // asset.LocationRelationType = null;
                    asset.UpdatedAt = DateTime.Now;
                    
                    // 如果有备注，更新资产备注
                    if (!string.IsNullOrEmpty(model.Notes))
                    {
                        asset.Notes = (string.IsNullOrEmpty(asset.Notes) ? "" : asset.Notes + "\n") + model.Notes;
                    }
                    
                    // 创建位置历史记录
                    await _context.LocationHistories.AddAsync(new LocationHistory
                    {
                        AssetId = asset.Id,
                        ChangeType = 2, // 解除关联
                        OldLocationId = oldLocationId,
                        NewLocationId = locationId,
                        OperatorId = operatorId,
                        Notes = model.Notes,
                        ChangeTime = DateTime.Now,
                        CreatedAt = DateTime.Now
                    });
                    
                    // 创建资产历史记录
                    string description = $"解除位置关联: {location.Name} → 无";
                    if (!string.IsNullOrEmpty(model.Notes))
                    {
                        description += $", 备注: {model.Notes}";
                    }
                    
                    await _context.AssetHistories.AddAsync(new AssetHistory
                    {
                        AssetId = asset.Id,
                        OperationType = 4, // 位置变更
                        OperatorId = operatorId,
                        OperationTime = DateTime.Now,
                        Description = description
                    });
                }
                
                // 保存更改
                await _context.SaveChangesAsync();
                
                // 返回解除关联成功的资产信息
                var unrelatedAssets = assets.Select(a => new
                {
                    a.Id,
                    a.AssetCode,
                    a.Name,
                    a.SerialNumber,
                    UpdatedAt = DateTime.Now
                }).ToList();
                
                return Ok(new { 
                    success = true, 
                    data = unrelatedAssets, 
                    count = unrelatedAssets.Count,
                    message = $"成功解除{unrelatedAssets.Count}个资产与位置的关联" 
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"解除资产与位置的关联出错，位置ID: {locationId}");
                return StatusCode(500, new { success = false, message = $"解除资产与位置的关联出错: {ex.Message}" });
            }
        }

        /// <summary>
        /// 获取未关联位置的资产列表
        /// </summary>
        /// <param name="keyword">关键字</param>
        /// <param name="assetTypeId">资产类型ID</param>
        /// <param name="status">资产状态</param>
        /// <param name="departmentId">部门ID</param>
        /// <param name="page">页码</param>
        /// <param name="pageSize">每页大小</param>
        /// <returns>未关联位置的资产列表</returns>
        [HttpGet("unrelated-assets")]
        public async Task<IActionResult> GetUnrelatedAssets(
            [FromQuery] string keyword = null,
            [FromQuery] int? assetTypeId = null,
            [FromQuery] int? status = null,
            [FromQuery] int? departmentId = null,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 10)
        {
            _logger.LogInformation("获取未关联位置的资产列表");
            try
            {
                // 使用SQL查询避免导航属性问题
                var connection = _context.Database.GetDbConnection();
                if (connection.State != System.Data.ConnectionState.Open)
                {
                    await connection.OpenAsync();
                }

                string countSql = @"
                    SELECT COUNT(*)
                    FROM 
                        assets a
                        LEFT JOIN assettypes at ON a.AssetTypeId = at.Id
                    WHERE 
                        a.LocationId IS NULL";

                string sql = @"
                    SELECT 
                        a.Id, 
                        a.AssetCode, 
                        a.Name, 
                        a.AssetTypeId,
                        at.Name AS AssetTypeName,
                        a.SerialNumber, 
                        a.Model, 
                        a.Brand, 
                        a.Status
                    FROM 
                        assets a
                        LEFT JOIN assettypes at ON a.AssetTypeId = at.Id
                    WHERE 
                        a.LocationId IS NULL";

                var parameters = new List<object>();
                int paramIndex = 0;

                // 添加筛选条件
                if (!string.IsNullOrEmpty(keyword))
                {
                    sql += $" AND (a.AssetCode LIKE @p{paramIndex} OR a.Name LIKE @p{paramIndex} OR a.SerialNumber LIKE @p{paramIndex})";
                    countSql += $" AND (a.AssetCode LIKE @p{paramIndex} OR a.Name LIKE @p{paramIndex} OR a.SerialNumber LIKE @p{paramIndex})";
                    parameters.Add("%" + keyword + "%");
                    paramIndex++;
                }

                if (assetTypeId.HasValue)
                {
                    sql += $" AND a.AssetTypeId = @p{paramIndex}";
                    countSql += $" AND a.AssetTypeId = @p{paramIndex}";
                    parameters.Add(assetTypeId.Value);
                    paramIndex++;
                }

                if (status.HasValue)
                {
                    sql += $" AND a.Status = @p{paramIndex}";
                    countSql += $" AND a.Status = @p{paramIndex}";
                    parameters.Add(status.Value);
                    paramIndex++;
                }

                // 部门ID筛选在UI层处理，后端不进行筛选，避免数据库字段不匹配问题

                sql += " ORDER BY a.Id DESC";
                sql += $" LIMIT {pageSize} OFFSET {(page - 1) * pageSize}";

                // 获取总数
                int total = 0;
                using (var command = connection.CreateCommand())
                {
                    command.CommandText = countSql;
                    
                    // 添加参数
                    for (int i = 0; i < parameters.Count; i++)
                    {
                        var parameter = command.CreateParameter();
                        parameter.ParameterName = $"@p{i}";
                        parameter.Value = parameters[i];
                        command.Parameters.Add(parameter);
                    }

                    var result = await command.ExecuteScalarAsync();
                    if (result != null && result != DBNull.Value)
                    {
                        total = Convert.ToInt32(result);
                    }
                }

                // 获取分页数据
                var assets = new List<Dictionary<string, object>>();
                using (var command = connection.CreateCommand())
                {
                    command.CommandText = sql;
                    
                    // 添加参数
                    for (int i = 0; i < parameters.Count; i++)
                    {
                        var parameter = command.CreateParameter();
                        parameter.ParameterName = $"@p{i}";
                        parameter.Value = parameters[i];
                        command.Parameters.Add(parameter);
                    }

                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            var asset = new Dictionary<string, object>();
                            
                            asset["id"] = reader.GetInt32(reader.GetOrdinal("Id"));
                            asset["assetCode"] = reader.IsDBNull(reader.GetOrdinal("AssetCode")) ? null : reader.GetString(reader.GetOrdinal("AssetCode"));
                            asset["name"] = reader.IsDBNull(reader.GetOrdinal("Name")) ? null : reader.GetString(reader.GetOrdinal("Name"));
                            asset["assetTypeId"] = reader.IsDBNull(reader.GetOrdinal("AssetTypeId")) ? null : (int?)reader.GetInt32(reader.GetOrdinal("AssetTypeId"));
                            asset["assetTypeName"] = reader.IsDBNull(reader.GetOrdinal("AssetTypeName")) ? null : reader.GetString(reader.GetOrdinal("AssetTypeName"));
                            asset["serialNumber"] = reader.IsDBNull(reader.GetOrdinal("SerialNumber")) ? null : reader.GetString(reader.GetOrdinal("SerialNumber"));
                            asset["model"] = reader.IsDBNull(reader.GetOrdinal("Model")) ? null : reader.GetString(reader.GetOrdinal("Model"));
                            asset["brand"] = reader.IsDBNull(reader.GetOrdinal("Brand")) ? null : reader.GetString(reader.GetOrdinal("Brand"));
                            asset["status"] = reader.IsDBNull(reader.GetOrdinal("Status")) ? null : (int?)reader.GetInt32(reader.GetOrdinal("Status"));
                            asset["statusText"] = reader.IsDBNull(reader.GetOrdinal("Status")) ? null : GetAssetStatusName(reader.GetInt32(reader.GetOrdinal("Status")));
                            
                            // 默认为空值，由前端决定是否显示
                            asset["departmentId"] = null;
                            asset["department"] = null;
                            asset["userId"] = null;
                            asset["user"] = null;
                            
                            assets.Add(asset);
                        }
                    }
                }

                // 返回分页结果
                return Ok(new { 
                    success = true, 
                    data = new {
                        items = assets,
                        total = total,
                        page = page,
                        pageSize = pageSize,
                        pages = (int)Math.Ceiling((double)total / pageSize)
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取未关联位置的资产列表出错");
                return StatusCode(500, new { success = false, message = $"获取未关联位置的资产列表出错: {ex.Message}" });
            }
        }

        /// <summary>
        /// 获取关联类型名称
        /// </summary>
        private static string GetRelationTypeName(int relationType)
        {
            return relationType switch
            {
                0 => "主要位置",
                1 => "次要位置",
                2 => "临时位置",
                _ => "未知类型"
            };
        }

        // 关联类型枚举
        public enum RelationType
        {
            Primary = 0,    // 主要位置
            Secondary = 1,  // 次要位置
            Temporary = 2   // 临时位置
        }

        // 资产关联模型
        public class LocationAssetRelateModel
        {
            public List<int> AssetIds { get; set; }
            public RelationType RelationType { get; set; }
            public string Notes { get; set; }
        }

        // 资产解除关联模型
        public class LocationAssetUnrelateModel
        {
            public List<int> AssetIds { get; set; }
            public string Notes { get; set; }
        }

        // 添加一个通用的方法来获取当前用户ID和名称
        private async Task<(int operatorId, string operatorName)> GetCurrentUserInfoAsync()
        {
            int operatorId = 1;
            string operatorName = "系统默认";

            try
            {
                // 尝试从User声明中获取用户ID
                _logger.LogDebug($"开始尝试获取当前用户ID...");
                var claims = User?.Claims?.ToList();
                if (claims != null && claims.Any())
                {
                    _logger.LogDebug($"当前用户Claims数量: {claims.Count}");
                    foreach (var claim in claims)
                    {
                        _logger.LogDebug($"Claim类型: {claim.Type}, 值: {claim.Value}");
                    }
                    
                    // 首先尝试从uid获取
                    var userIdClaim = claims.FirstOrDefault(c => c.Type == "uid");
                    if (userIdClaim != null && int.TryParse(userIdClaim.Value, out int userId))
                    {
                        operatorId = userId;
                        _logger.LogInformation($"从Claims的uid获取到操作用户ID: {operatorId}");
                    }
                    else
                    {
                        // 然后尝试从nameidentifier获取
                        userIdClaim = claims.FirstOrDefault(c => c.Type == ClaimTypes.NameIdentifier);
                        if (userIdClaim != null && int.TryParse(userIdClaim.Value, out int userId2))
                        {
                            operatorId = userId2;
                            _logger.LogInformation($"从Claims的NameIdentifier获取到操作用户ID: {operatorId}");
                        }
                        else
                        {
                            _logger.LogWarning("无法从任何Claims获取有效的用户ID，使用默认值1");
                        }
                    }
                }
                else
                {
                    _logger.LogWarning("当前请求中没有用户Claims信息");
                }
                
                // 获取用户名
                var user = await _context.Users.FindAsync(operatorId);
                if (user != null)
                {
                    operatorName = !string.IsNullOrEmpty(user.Name) ? user.Name : user.Username;
                    _logger.LogInformation($"获取到操作用户名称: {operatorName}");
                }
                else
                {
                    _logger.LogWarning($"未找到ID为{operatorId}的用户信息，使用默认名称");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取用户信息过程中发生异常: {ex.Message}");
            }

            return (operatorId, operatorName);
        }

        /// <summary>
        /// 获取位置的用户关联
        /// </summary>
        /// <param name="locationId">位置ID</param>
        /// <returns>用户关联列表</returns>
        [HttpGet("{locationId}/users")]
        public async Task<IActionResult> GetLocationUsers(int locationId)
        {
            _logger.LogInformation($"获取位置的用户列表，位置ID: {locationId}");
            try
            {
                // 获取位置关联的用户信息
                List<object> locationUsers = new List<object>();
                using (var command = _context.Database.GetDbConnection().CreateCommand())
                {
                    command.CommandText = @"
                        SELECT 
                            lu.personnel_id,
                            p.name,
                            p.employee_code,
                            CASE WHEN d.name IS NOT NULL THEN d.name ELSE '未分配部门' END as departmentName,
                            p.department_id as departmentId,
                            lu.user_type,
                            CASE WHEN lu.user_type = 0 THEN '使用人' ELSE '管理员' END as typeName,
                            lu.is_active,
                            lu.location_id,
                            lu.created_at
                        FROM locationusers lu
                        INNER JOIN personnel p ON lu.personnel_id = p.id
                        LEFT JOIN departments d ON p.department_id = d.id
                        WHERE lu.location_id = @locationId";

                    // 添加参数
                    var parameter = command.CreateParameter();
                    parameter.ParameterName = "locationId";
                    parameter.Value = locationId;
                    command.Parameters.Add(parameter);

                    // 确保连接已打开
                    if (command.Connection.State != System.Data.ConnectionState.Open)
                    {
                        command.Connection.Open();
                    }

                    // 读取数据
                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            locationUsers.Add(new
                            {
                                personnelId = reader.GetInt32(0),
                                id = reader.GetInt32(0), // 为兼容前端
                                name = reader.IsDBNull(1) ? null : reader.GetString(1),
                                employeeCode = reader.IsDBNull(2) ? null : reader.GetString(2),
                                departmentName = reader.IsDBNull(3) ? "未分配部门" : reader.GetString(3),
                                departmentId = reader.IsDBNull(4) ? (int?)null : reader.GetInt32(4),
                                type = reader.GetInt32(5),
                                userType = reader.GetInt32(5), // 为兼容前端
                                typeName = reader.GetString(6),
                                isActive = true, // locationusers表中可能没有is_active字段
                                locationId = reader.GetInt32(7),
                                createdAt = reader.GetDateTime(8)
                            });
                        }
                    }
                }

                return Ok(locationUsers);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取位置用户列表出错，位置ID: {locationId}");
                return StatusCode(500, new { success = false, message = "获取位置用户列表出错: " + ex.Message });
            }
        }

        /// <summary>
        /// 更新位置用户关联
        /// </summary>
        /// <param name="locationId">位置ID</param>
        /// <param name="request">请求数据</param>
        /// <returns>更新结果</returns>
        [HttpPost("{locationId}/users")]
        public async Task<IActionResult> UpdateLocationUsers(int locationId, [FromBody] UpdateLocationUsersRequest request)
        {
            if (request == null || request.Users == null)
            {
                return BadRequest(new { success = false, message = "请求数据不能为空" });
            }

            _logger.LogInformation($"更新位置人员关联，位置ID: {locationId}, 人员数量: {request.Users.Count}, 替换现有: {request.ReplaceExisting}");

            try
            {
                // 验证位置是否存在
                var location = await _context.Locations.FirstOrDefaultAsync(l => l.Id == locationId);
                if (location == null)
                {
                    return NotFound(new { success = false, message = $"位置ID {locationId} 不存在" });
                }

                // 验证人员是否存在
                var personnelIds = request.Users.Select(u => u.PersonnelId).Distinct().ToList();
                var existingPersonnel = await _context.Personnel
                    .Where(p => personnelIds.Contains(p.Id))
                    .Select(p => p.Id)
                    .ToListAsync();

                var invalidIds = personnelIds.Except(existingPersonnel).ToList();
                if (invalidIds.Any())
                {
                    return BadRequest(new { success = false, message = $"以下人员ID不存在: {string.Join(", ", invalidIds)}" });
                }

                // 开始事务
                using (var transaction = await _context.Database.BeginTransactionAsync())
                {
                    try
                    {
                        // 如果是替换模式，先删除所有现有关联
                        if (request.ReplaceExisting)
                        {
                            _logger.LogInformation($"使用原始SQL删除位置 {locationId} 的所有人员关联");
                            
                            // 使用原始SQL语句删除而不是EF Core
                            await _context.Database.ExecuteSqlRawAsync(
                                "DELETE FROM locationusers WHERE location_id = {0}", 
                                locationId);
                        }
                        
                        // 新增关联 - 使用批量参数化SQL而不是EF Core对象
                        if (request.Users.Any())
                        {
                            _logger.LogInformation($"使用原始SQL添加 {request.Users.Count} 个新人员关联");
                            
                            foreach (var user in request.Users)
                            {
                                // 如果不是替换模式，先检查是否存在
                                if (!request.ReplaceExisting)
                                {
                                    var existsCount = await _context.Database.ExecuteSqlRawAsync(
                                        "SELECT COUNT(*) FROM locationusers WHERE location_id = {0} AND personnel_id = {1}",
                                        locationId, user.PersonnelId);
                                    
                                    if (existsCount > 0)
                                    {
                                        _logger.LogInformation($"人员 {user.PersonnelId} 已关联到位置 {locationId}，跳过");
                                        continue;
                                    }
                                }
                                
                                // 直接执行SQL插入
                                await _context.Database.ExecuteSqlRawAsync(
                                    "INSERT INTO locationusers (location_id, personnel_id, user_type, created_at) VALUES ({0}, {1}, {2}, {3})",
                                    locationId, user.PersonnelId, user.UserType, DateTime.Now);
                            }
                        }

                        await transaction.CommitAsync();
                        
                        return Ok(new { success = true, message = "更新位置人员关联成功" });
                    }
                    catch (Exception ex)
                    {
                        await transaction.RollbackAsync();
                        _logger.LogError(ex, $"执行SQL操作时出错: {ex.Message}");
                        throw;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"更新位置人员关联出错，位置ID: {locationId}");
                return StatusCode(500, new { success = false, message = "更新位置人员关联出错: " + ex.Message });
            }
        }

        /// <summary>
        /// 删除位置用户关联
        /// </summary>
        /// <param name="locationId">位置ID</param>
        /// <param name="personnelId">人员ID</param>
        /// <returns>删除结果</returns>
        [HttpDelete("{locationId}/users/{personnelId}")]
        public async Task<IActionResult> RemoveLocationUser(int locationId, int personnelId)
        {
            _logger.LogInformation($"删除位置人员关联，位置ID: {locationId}, 人员ID: {personnelId}");

            try
            {
                var locationUser = await _context.LocationUsers
                    .FirstOrDefaultAsync(lu => lu.LocationId == locationId && lu.PersonnelId == personnelId);

                if (locationUser == null)
                {
                    return NotFound(new { success = false, message = $"未找到位置 {locationId} 与人员 {personnelId} 的关联" });
                }

                _context.LocationUsers.Remove(locationUser);
                await _context.SaveChangesAsync();

                _logger.LogInformation($"已删除位置 {locationId} 与人员 {personnelId} 的关联");

                return Ok(new { success = true, message = "删除位置人员关联成功" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"删除位置人员关联出错，位置ID: {locationId}, 人员ID: {personnelId}");
                return StatusCode(500, new { success = false, message = "删除位置人员关联出错: " + ex.Message });
            }
        }

        /// <summary>
        /// 更新位置用户请求
        /// </summary>
        public class UpdateLocationUsersRequest
        {
            /// <summary>
            /// 是否替换现有关联
            /// </summary>
            public bool ReplaceExisting { get; set; } = false;

            /// <summary>
            /// 用户列表
            /// </summary>
            public List<LocationUserData> Users { get; set; }
        }

        /// <summary>
        /// 位置用户数据
        /// </summary>
        public class LocationUserData
        {
            /// <summary>
            /// 人员ID
            /// </summary>
            public int PersonnelId { get; set; }

            /// <summary>
            /// 用户类型 0:使用人员 1:管理员
            /// </summary>
            public byte UserType { get; set; }
        }

        /// <summary>
        /// 获取位置历史记录
        /// </summary>
        /// <param name="id">位置ID</param>
        /// <returns>历史记录列表</returns>
        [HttpGet("{id}/history")]
        public async Task<IActionResult> GetLocationHistory(int id)
        {
            try
            {
                _logger.LogInformation($"获取位置ID={id}的历史记录");
                
                // 检查位置是否存在
                var location = await _context.Locations.FindAsync(id);
                if (location == null)
                {
                    _logger.LogWarning($"位置ID={id}不存在");
                    return NotFound(new { success = false, message = "位置不存在" });
                }
                
                // 获取该位置的历史记录
                var histories = await _context.LocationHistories
                    .Where(lh => lh.OldLocationId == id || lh.NewLocationId == id)
                    .OrderByDescending(lh => lh.CreatedAt)
                    .Include(lh => lh.Operator)
                    .Include(lh => lh.Asset)
                    .Include(lh => lh.OldLocation)
                    .Include(lh => lh.NewLocation)
                    .Select(lh => new
                    {
                        id = lh.Id,
                        assetId = lh.AssetId,
                        assetName = lh.Asset != null ? lh.Asset.Name : null,
                        assetCode = lh.Asset != null ? lh.Asset.AssetCode : null,
                        oldLocationId = lh.OldLocationId,
                        oldLocationName = lh.OldLocation != null ? lh.OldLocation.Name : null,
                        newLocationId = lh.NewLocationId,
                        newLocationName = lh.NewLocation != null ? lh.NewLocation.Name : null,
                        operatorId = lh.OperatorId,
                        operatorName = lh.Operator != null ? (!string.IsNullOrEmpty(lh.Operator.Name) ? lh.Operator.Name : lh.Operator.Username) : "未知用户",
                        changeType = lh.ChangeType,
                        notes = lh.Notes,
                        changeTime = lh.ChangeTime,
                        createdAt = lh.CreatedAt
                    })
                    .ToListAsync();
                    
                _logger.LogInformation($"找到 {histories.Count} 条位置历史记录");
                
                return Ok(new { success = true, data = histories });
            }
            catch (Exception ex)
            {
                _logger.LogError($"获取位置历史记录时发生错误: {ex.Message}");
                return StatusCode(500, new { success = false, message = "获取位置历史记录时发生错误" });
            }
        }

        /// <summary>
        /// 设置位置关联部门和部门负责人
        /// </summary>
        /// <param name="locationId">位置ID</param>
        /// <param name="request">设置位置关联部门的请求体，包含 DepartmentId</param>
        /// <returns>关联结果</returns>
        [HttpPost("{locationId}/department")]
        public async Task<IActionResult> SetLocationDepartment(int locationId, [FromBody] SetLocationDepartmentRequest request)
        {
            _logger.LogInformation($"设置位置关联部门，位置ID: {locationId}, 部门ID: {request.DepartmentId}");

            try
            {
                // 验证位置是否存在
                var location = await _context.Locations.FindAsync(locationId);
                if (location == null)
                {
                    return NotFound(new { success = false, message = $"位置ID {locationId} 不存在" });
                }

                // 验证部门是否存在
                var department = await _context.Departments.FindAsync(request.DepartmentId);
                if (department == null)
                {
                    return BadRequest(new { success = false, message = $"部门ID {request.DepartmentId} 不存在" });
                }

                // 更新位置关联的部门
                location.DefaultDepartmentId = request.DepartmentId;
                location.UpdatedAt = DateTime.Now;
                await _context.SaveChangesAsync();

                // 获取部门负责人信息
                var managerId = department.ManagerId;
                if (managerId.HasValue)
                {
                    // 查找对应的人员记录
                    var manager = await _context.Personnel.FindAsync(managerId.Value);
                    if (manager != null)
                    {
                        // 检查该负责人是否已经是位置的管理员
                        var existingManager = await _context.LocationUsers
                            .FirstOrDefaultAsync(lu => lu.LocationId == locationId && 
                                                        lu.PersonnelId == managerId.Value && 
                                                        lu.UserType == 1);

                        if (existingManager == null)
                        {
                            // 添加负责人作为位置管理员
                            var locationUser = new LocationUser
                            {
                                LocationId = locationId,
                                PersonnelId = managerId.Value,
                                UserType = 1, // 1 表示管理员
                                CreatedAt = DateTime.Now
                            };

                            await _context.LocationUsers.AddAsync(locationUser);
                            await _context.SaveChangesAsync();
                            
                            _logger.LogInformation($"已自动添加部门负责人(ID:{managerId.Value})作为位置管理员");
                        }
                        else
                        {
                            _logger.LogInformation($"部门负责人(ID:{managerId.Value})已经是该位置的管理员");
                        }
                    }
                    else
                    {
                        _logger.LogWarning($"部门负责人(ID:{managerId.Value})在Personnel表中不存在");
                    }
                }
                else
                {
                    _logger.LogInformation($"部门{request.DepartmentId}没有设置负责人");
                }

                return Ok(new { 
                    success = true, 
                    message = "位置关联部门设置成功",
                    data = new {
                        departmentId = department.Id,
                        departmentName = department.Name,
                        managerId = managerId,
                        managerAutoSet = managerId.HasValue
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"设置位置关联部门出错，位置ID: {locationId}, 部门ID: {request.DepartmentId}");
                return StatusCode(500, new { success = false, message = "设置位置关联部门出错: " + ex.Message });
            }
        }

        /// <summary>
        /// 设置位置关联部门请求
        /// </summary>
        public class SetLocationDepartmentRequest
        {
            /// <summary>
            /// 部门ID
            /// </summary>
            public int DepartmentId { get; set; }
        }

        /// <summary>
        /// 根据ID获取位置详情
        /// </summary>
        /// <param name="id">位置ID</param>
        /// <returns>位置详情</returns>
        [HttpGet("{id}")]
        public async Task<IActionResult> GetById(int id)
        {
            _logger.LogInformation($"获取位置详情，ID: {id}");
            try
            {
                // 从数据库获取位置信息
                var location = await _context.Locations
                    .Where(l => l.Id == id)
                    .Select(l => new
                    {
                        l.Id,
                        l.Name,
                        l.Code,
                        l.Type,
                        l.ParentId,
                        l.Path,
                        l.Description,
                        l.DefaultDepartmentId,
                        l.ManagerId,
                        l.IsActive,
                        l.CreatedAt,
                        l.UpdatedAt
                    })
                    .FirstOrDefaultAsync();

                if (location == null)
                {
                    return NotFound(new { success = false, message = $"未找到ID为{id}的位置" });
                }

                // 获取位置关联的用户信息 - 使用原始SQL查询，避免Entity Framework Core的映射问题
                List<object> locationUsers = new List<object>();
                using (var command = _context.Database.GetDbConnection().CreateCommand())
                {
                    command.CommandText = @"
                        SELECT 
                            lu.personnel_id,
                            p.name,
                            p.employee_code,
                            CASE WHEN d.name IS NOT NULL THEN d.name ELSE '未分配部门' END as departmentName,
                            p.department_id as departmentId,
                            lu.user_type,
                            CASE WHEN lu.user_type = 0 THEN '使用人' ELSE '管理员' END as typeName,
                            lu.location_id,
                            lu.created_at
                        FROM locationusers lu
                        INNER JOIN personnel p ON lu.personnel_id = p.id
                        LEFT JOIN departments d ON p.department_id = d.id
                        WHERE lu.location_id = @locationId";

                    // 添加参数
                    var parameter = command.CreateParameter();
                    parameter.ParameterName = "locationId";
                    parameter.Value = id;
                    command.Parameters.Add(parameter);

                    // 确保连接已打开
                    if (command.Connection.State != System.Data.ConnectionState.Open)
                    {
                        command.Connection.Open();
                    }

                    // 读取数据
                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            locationUsers.Add(new
                            {
                                personnelId = reader.GetInt32(0),
                                id = reader.GetInt32(0), // 为兼容前端
                                name = reader.IsDBNull(1) ? null : reader.GetString(1),
                                employeeCode = reader.IsDBNull(2) ? null : reader.GetString(2),
                                departmentName = reader.IsDBNull(3) ? "未分配部门" : reader.GetString(3),
                                departmentId = reader.IsDBNull(4) ? (int?)null : reader.GetInt32(4),
                                type = reader.GetInt32(5),
                                userType = reader.GetInt32(5), // 为兼容前端
                                typeName = reader.GetString(6),
                                isActive = true, // locationusers表中可能没有is_active字段
                                locationId = reader.GetInt32(7),
                                createdAt = reader.GetDateTime(8)
                            });
                        }
                    }
                }

                // 创建包含用户信息的完整响应
                var response = new 
                {
                    success = true,
                    data = location,
                    users = locationUsers,
                    // 为方便前端使用，提供分类的用户信息
                    managers = locationUsers.Where(u => ((dynamic)u).type == 1).ToList(),
                    usageUsers = locationUsers.Where(u => ((dynamic)u).type == 0).ToList()
                };

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取位置详情出错，ID: {id}");
                return StatusCode(500, new { success = false, message = "获取位置详情出错: " + ex.Message });
            }
        }
    }
} 