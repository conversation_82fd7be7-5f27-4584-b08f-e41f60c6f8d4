// IT资产管理系统 - 主页控制器
// 文件路径: /Controllers/HomeController.cs
// 功能: 提供基本的主页和健康检查API

using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;

namespace ItAssetsSystem.Controllers
{
    /// <summary>
    /// 主页控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class HomeController : ControllerBase
    {
        private readonly ILogger<HomeController> _logger;

        public HomeController(ILogger<HomeController> logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// 健康检查端点
        /// </summary>
        /// <returns>系统健康状态</returns>
        [HttpGet("health")]
        public IActionResult Health()
        {
            return Ok(new { 
                status = "healthy", 
                version = "1.0.0" 
            });
        }

        /// <summary>
        /// API首页
        /// </summary>
        [HttpGet]
        public IActionResult Get()
        {
            return Ok(new { 
                message = "欢迎使用IT资产管理系统API", 
                time = DateTime.Now 
            });
        }
    }
} 