// File: Infrastructure/Services/FileStorageService.cs
// Description: Implementation for file storage operations, implementing core abstraction.

using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options; // For FileStorageSettings
using ItAssetsSystem.Core.Abstractions; // Core IFileStorageService
using ItAssetsSystem.Core.Configuration; // Core FileStorageSettings
using System;
using System.IO;
using System.Linq; // Required for .Contains on string array
using System.Threading.Tasks;

#nullable enable

namespace ItAssetsSystem.Infrastructure.Services
{
    public class FileStorageService : IFileStorageService // Implements the CORE interface
    {
        private readonly ILogger<FileStorageService> _logger;
        private readonly IOptions<FileStorageSettings> _fileStorageSettings;

        public FileStorageService(ILogger<FileStorageService> logger, IOptions<FileStorageSettings> fileStorageSettings)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _fileStorageSettings = fileStorageSettings ?? throw new ArgumentNullException(nameof(fileStorageSettings));
        }

        public async Task<string?> SaveAvatarAsync(IFormFile file, string userId)
        {
            if (file == null || file.Length == 0)
            {
                _logger.LogWarning("SaveAvatarAsync called with no file or empty file for user {UserId}.", userId);
                return null;
            }
            if (string.IsNullOrWhiteSpace(userId))
            {
                 _logger.LogWarning("SaveAvatarAsync called with null or empty userId.");
                return null;
            }

            try
            {
                var basePhysicalPath = _fileStorageSettings.Value?.BasePhysicalPathForFilesVirtualDir;
                if (string.IsNullOrWhiteSpace(basePhysicalPath))
                {
                    _logger.LogError("BasePhysicalPathForFilesVirtualDir is not configured in settings.");
                    return null; 
                }

                var targetDirectory = Path.Combine(basePhysicalPath, "uploads", "avatars");
                if (!Directory.Exists(targetDirectory))
                {
                    _logger.LogInformation("Target directory {DirectoryPath} does not exist. Creating it.", targetDirectory);
                    Directory.CreateDirectory(targetDirectory);
                }

                var fileExtension = Path.GetExtension(file.FileName)?.ToLowerInvariant();
                
                // Basic validation for extension - ensure it's a supported image type
                var allowedExtensions = new[] { ".jpg", ".jpeg", ".png" };
                if (string.IsNullOrEmpty(fileExtension) || !allowedExtensions.Contains(fileExtension))
                {
                    _logger.LogWarning("SaveAvatarAsync: Invalid file type provided: {FileExtension} for user {UserId}. Allowed are: {AllowedExtensions}", 
                        fileExtension, userId, string.Join(", ", allowedExtensions));
                    return null; 
                }

                var timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
                // Ensure userId is sanitized if it can contain special characters, though typically it's an int/guid as string.
                var uniqueFileName = $"user{userId}_{timestamp}{fileExtension}";
                var physicalFilePath = Path.Combine(targetDirectory, uniqueFileName);

                using (var stream = new FileStream(physicalFilePath, FileMode.Create))
                {
                    await file.CopyToAsync(stream);
                }
                _logger.LogInformation("Avatar {FileName} saved successfully to {FilePath} for user {UserId}", uniqueFileName, physicalFilePath, userId);
                
                var relativePath = Path.Combine("uploads", "avatars", uniqueFileName).Replace("\\", "/");
                return relativePath;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving avatar for user {UserId}", userId);
                return null; 
            }
        }

        public async Task<string?> SaveFileAsync(byte[] fileContent, string fileName, string subPath)
        {
            if (fileContent == null || fileContent.Length == 0)
            {
                _logger.LogWarning("SaveFileAsync called with no file content or empty content for file {FileName} in {SubPath}.", fileName, subPath);
                return null;
            }
            if (string.IsNullOrWhiteSpace(fileName))
            {
                 _logger.LogWarning("SaveFileAsync called with null or empty fileName for subPath {SubPath}.", subPath);
                return null;
            }
            if (string.IsNullOrWhiteSpace(subPath))
            {
                 _logger.LogWarning("SaveFileAsync called with null or empty subPath for fileName {FileName}.", fileName);
                return null;
            }

            try
            {
                var basePhysicalPath = _fileStorageSettings.Value?.BasePhysicalPathForFilesVirtualDir;
                if (string.IsNullOrWhiteSpace(basePhysicalPath))
                {
                    _logger.LogError("BasePhysicalPathForFilesVirtualDir is not configured in settings.");
                    return null; 
                }

                // Sanitize subPath and fileName to prevent path traversal or invalid characters
                var sanitizedSubPath = SanitizePathPart(subPath);
                var sanitizedFileName = SanitizePathPart(fileName, true);

                if (string.IsNullOrWhiteSpace(sanitizedSubPath) || string.IsNullOrWhiteSpace(sanitizedFileName))
                {
                    _logger.LogError("Invalid characters in subPath or fileName after sanitization. Original SubPath: {OriginalSubPath}, Original FileName: {OriginalFileName}", subPath, fileName);
                    return null;
                }

                var targetDirectory = Path.Combine(basePhysicalPath, "uploads", sanitizedSubPath);
                if (!Directory.Exists(targetDirectory))
                {
                    _logger.LogInformation("Target directory {DirectoryPath} does not exist. Creating it.", targetDirectory);
                    Directory.CreateDirectory(targetDirectory);
                }
                
                var physicalFilePath = Path.Combine(targetDirectory, sanitizedFileName);

                await File.WriteAllBytesAsync(physicalFilePath, fileContent);
                
                _logger.LogInformation("File {FileName} saved successfully to {FilePath} in subPath {SubPath}", sanitizedFileName, physicalFilePath, sanitizedSubPath);
                
                var relativePath = Path.Combine("uploads", sanitizedSubPath, sanitizedFileName).Replace("\\", "/");
                return relativePath;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving file {FileName} to subPath {SubPath}", fileName, subPath);
                return null; 
            }
        }

        private string SanitizePathPart(string pathPart, bool isFileName = false)
        {
            if (string.IsNullOrWhiteSpace(pathPart)) return string.Empty;
            
            var invalidChars = Path.GetInvalidFileNameChars().ToList(); // For filenames, allow '.'
            if (!isFileName) // For directory parts, also disallow anything GetInvalidPathChars says
            {
                invalidChars.AddRange(Path.GetInvalidPathChars());
                invalidChars.Add('/'); // Explicitly disallow / for individual parts, Path.Combine handles it
                invalidChars.Add('\\'); // Explicitly disallow \ for individual parts
            }
            else
            {
                 // For filenames, ensure '.' is not in invalidChars IF it's part of a valid extension
                 // but Path.GetInvalidFileNameChars() already handles this well (it doesn't include '.')
            }

            // Remove path traversal sequences
            string sanitized = pathPart.Replace("..", ""); 

            // Filter out invalid characters
            sanitized = new string(sanitized.Where(c => !invalidChars.Contains(c)).ToArray());
            
            // For subPath, we split by / or \, sanitize each part, then rejoin
            // This is safer than a blanket character removal that might break valid nested paths.
            if (!isFileName)
            {
                var parts = sanitized.Split(new[] { '/', '\\' }, StringSplitOptions.RemoveEmptyEntries);
                // Sanitize each part as a filename (to be strict) then join with OS-specific separator
                return Path.Combine(parts.Select(p => SanitizePathPart(p, true)).ToArray());
            }
            
            return sanitized.Trim(); // Trim leading/trailing whitespace
        }

        public Task<bool> DeleteFileAsync(string? relativePath)
        {
            if (string.IsNullOrWhiteSpace(relativePath))
            {
                _logger.LogWarning("DeleteFileAsync called with null or empty relativePath.");
                return Task.FromResult(true); 
            }

            try
            {
                var basePhysicalPath = _fileStorageSettings.Value?.BasePhysicalPathForFilesVirtualDir;
                if (string.IsNullOrWhiteSpace(basePhysicalPath))
                {
                    _logger.LogError("BasePhysicalPathForFilesVirtualDir is not configured. Cannot delete file: {RelativePath}", relativePath);
                    return Task.FromResult(false);
                }

                // Basic validation for relativePath to prevent path traversal.
                // This assumes standard avatar paths. More robust validation might be needed
                // depending on how relativePath is constructed and used elsewhere.
                if (!relativePath.StartsWith("uploads/avatars/", StringComparison.OrdinalIgnoreCase) || relativePath.Contains(".."))
                {
                    _logger.LogError("DeleteFileAsync: Invalid or potentially unsafe relative path format: {Path}", relativePath);
                    return Task.FromResult(false);
                }

                var physicalPath = Path.Combine(basePhysicalPath, relativePath.Replace("/", Path.DirectorySeparatorChar.ToString()));

                if (File.Exists(physicalPath))
                {
                    File.Delete(physicalPath);
                    _logger.LogInformation("File deleted successfully: {PhysicalPath}", physicalPath);
                }
                else
                {
                    _logger.LogWarning("Attempted to delete file that does not exist: {PhysicalPath}", physicalPath);
                }
                return Task.FromResult(true); // Return true if deletion was attempted or file didn't exist.
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting file at relative path {RelativePath}", relativePath);
                return Task.FromResult(false);
            }
        }
    }
} 