using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using ItAssetsSystem.Application.Features.Tasks.Repositories;
using ItAssetsSystem.Application.Features.Tasks.Dtos;
using ItAssetsSystem.Domain.Entities.Tasks;
using ItAssetsSystem.Models;
using ItAssetsSystem.Core.Interfaces.Services;

namespace ItAssetsSystem.Application.Features.Tasks.Services
{
    /// <summary>
    /// 任务分类服务接口
    /// </summary>
    public interface ITaskCategoryService
    {
        Task<ApiResponse<List<TaskCategoryDto>>> GetAllAsync(CancellationToken cancellationToken = default);
        Task<ApiResponse<List<TaskCategorySimpleDto>>> GetActiveSimpleAsync(CancellationToken cancellationToken = default);
        Task<ApiResponse<TaskCategoryDto>> GetByIdAsync(int categoryId, CancellationToken cancellationToken = default);
        Task<ApiResponse<PagedResult<TaskCategoryDto>>> GetPagedAsync(TaskCategoryQueryRequestDto request, CancellationToken cancellationToken = default);
        Task<ApiResponse<TaskCategoryDto>> CreateAsync(CreateTaskCategoryRequestDto request, int currentUserId, CancellationToken cancellationToken = default);
        Task<ApiResponse<TaskCategoryDto>> UpdateAsync(int categoryId, UpdateTaskCategoryRequestDto request, CancellationToken cancellationToken = default);
        Task<ApiResponse<bool>> DeleteAsync(int categoryId, CancellationToken cancellationToken = default);
        Task<ApiResponse<bool>> UpdateSortOrderAsync(int categoryId, string direction, CancellationToken cancellationToken = default);
    }

    /// <summary>
    /// 任务分类服务实现
    /// </summary>
    public class TaskCategoryService : ITaskCategoryService
    {
        private readonly ITaskCategoryRepository _categoryRepository;
        private readonly ILogger<TaskCategoryService> _logger;

        public TaskCategoryService(
            ITaskCategoryRepository categoryRepository,
            ILogger<TaskCategoryService> logger)
        {
            _categoryRepository = categoryRepository;
            _logger = logger;
        }

        /// <summary>
        /// 获取所有任务分类
        /// </summary>
        public async Task<ApiResponse<List<TaskCategoryDto>>> GetAllAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                var categories = await _categoryRepository.GetAllAsync(cancellationToken);
                var categoryDtos = categories.Select(MapToDto).ToList();
                
                return ApiResponse<List<TaskCategoryDto>>.CreateSuccess(categoryDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取所有任务分类时发生错误");
                return ApiResponse<List<TaskCategoryDto>>.CreateFail("获取任务分类失败");
            }
        }

        /// <summary>
        /// 获取启用的任务分类（简化版本）
        /// </summary>
        public async Task<ApiResponse<List<TaskCategorySimpleDto>>> GetActiveSimpleAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                var categories = await _categoryRepository.GetActiveAsync(cancellationToken);
                var simpleDtos = categories.Select(c => new TaskCategorySimpleDto
                {
                    CategoryId = c.CategoryId,
                    Name = c.Name,
                    Color = c.Color,
                    Icon = c.Icon,
                    IsActive = c.IsActive
                }).ToList();
                
                return ApiResponse<List<TaskCategorySimpleDto>>.CreateSuccess(simpleDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取启用的任务分类时发生错误");
                return ApiResponse<List<TaskCategorySimpleDto>>.CreateFail("获取任务分类失败");
            }
        }

        /// <summary>
        /// 根据ID获取任务分类
        /// </summary>
        public async Task<ApiResponse<TaskCategoryDto>> GetByIdAsync(int categoryId, CancellationToken cancellationToken = default)
        {
            try
            {
                var category = await _categoryRepository.GetByIdAsync(categoryId, cancellationToken);
                if (category == null)
                {
                    return ApiResponse<TaskCategoryDto>.CreateFail("任务分类不存在");
                }

                var categoryDto = MapToDto(category);
                return ApiResponse<TaskCategoryDto>.CreateSuccess(categoryDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取任务分类详情时发生错误: CategoryId={CategoryId}", categoryId);
                return ApiResponse<TaskCategoryDto>.CreateFail("获取任务分类详情失败");
            }
        }

        /// <summary>
        /// 分页查询任务分类
        /// </summary>
        public async Task<ApiResponse<PagedResult<TaskCategoryDto>>> GetPagedAsync(TaskCategoryQueryRequestDto request, CancellationToken cancellationToken = default)
        {
            try
            {
                var (items, totalCount) = await _categoryRepository.GetPagedAsync(request, cancellationToken);
                var categoryDtos = items.Select(MapToDto).ToList();

                var pagedResult = new PagedResult<TaskCategoryDto>
                {
                    Items = categoryDtos,
                    TotalCount = totalCount,
                    PageIndex = request.PageNumber,
                    PageSize = request.PageSize
                };

                return ApiResponse<PagedResult<TaskCategoryDto>>.CreateSuccess(pagedResult);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "分页查询任务分类时发生错误");
                return ApiResponse<PagedResult<TaskCategoryDto>>.CreateFail("查询任务分类失败");
            }
        }

        /// <summary>
        /// 创建任务分类
        /// </summary>
        public async Task<ApiResponse<TaskCategoryDto>> CreateAsync(CreateTaskCategoryRequestDto request, int currentUserId, CancellationToken cancellationToken = default)
        {
            try
            {
                // 检查名称是否已存在
                if (await _categoryRepository.NameExistsAsync(request.Name, cancellationToken: cancellationToken))
                {
                    return ApiResponse<TaskCategoryDto>.CreateFail($"分类名称 '{request.Name}' 已存在");
                }

                var category = new TaskCategory
                {
                    Name = request.Name,
                    Description = request.Description,
                    Color = request.Color,
                    Icon = request.Icon,
                    SortOrder = request.SortOrder,
                    IsActive = request.IsActive,
                    CreatedBy = currentUserId
                };

                var createdCategory = await _categoryRepository.CreateAsync(category, cancellationToken);
                var categoryDto = MapToDto(createdCategory);

                _logger.LogInformation("成功创建任务分类: {CategoryName} (ID: {CategoryId})", createdCategory.Name, createdCategory.CategoryId);
                return ApiResponse<TaskCategoryDto>.CreateSuccess(categoryDto, "任务分类创建成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建任务分类时发生错误: {CategoryName}", request.Name);
                return ApiResponse<TaskCategoryDto>.CreateFail("创建任务分类失败");
            }
        }

        /// <summary>
        /// 更新任务分类
        /// </summary>
        public async Task<ApiResponse<TaskCategoryDto>> UpdateAsync(int categoryId, UpdateTaskCategoryRequestDto request, CancellationToken cancellationToken = default)
        {
            try
            {
                var category = await _categoryRepository.GetByIdAsync(categoryId, cancellationToken);
                if (category == null)
                {
                    return ApiResponse<TaskCategoryDto>.CreateFail("任务分类不存在");
                }

                // 检查名称是否已存在（排除当前分类）
                if (await _categoryRepository.NameExistsAsync(request.Name, categoryId, cancellationToken))
                {
                    return ApiResponse<TaskCategoryDto>.CreateFail($"分类名称 '{request.Name}' 已存在");
                }

                // 更新分类信息
                category.Name = request.Name;
                category.Description = request.Description;
                category.Color = request.Color;
                category.Icon = request.Icon;
                category.SortOrder = request.SortOrder;
                category.IsActive = request.IsActive;

                var updatedCategory = await _categoryRepository.UpdateAsync(category, cancellationToken);
                var categoryDto = MapToDto(updatedCategory);

                _logger.LogInformation("成功更新任务分类: {CategoryName} (ID: {CategoryId})", updatedCategory.Name, updatedCategory.CategoryId);
                return ApiResponse<TaskCategoryDto>.CreateSuccess(categoryDto, "任务分类更新成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新任务分类时发生错误: CategoryId={CategoryId}", categoryId);
                return ApiResponse<TaskCategoryDto>.CreateFail("更新任务分类失败");
            }
        }

        /// <summary>
        /// 删除任务分类
        /// </summary>
        public async Task<ApiResponse<bool>> DeleteAsync(int categoryId, CancellationToken cancellationToken = default)
        {
            try
            {
                var success = await _categoryRepository.DeleteAsync(categoryId, cancellationToken);
                if (!success)
                {
                    return ApiResponse<bool>.CreateFail("任务分类不存在");
                }

                _logger.LogInformation("成功删除任务分类: CategoryId={CategoryId}", categoryId);
                return ApiResponse<bool>.CreateSuccess(true, "任务分类删除成功");
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning("删除任务分类失败: {Message}", ex.Message);
                return ApiResponse<bool>.CreateFail(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除任务分类时发生错误: CategoryId={CategoryId}", categoryId);
                return ApiResponse<bool>.CreateFail("删除任务分类失败");
            }
        }

        /// <summary>
        /// 更新分类排序
        /// </summary>
        public async Task<ApiResponse<bool>> UpdateSortOrderAsync(int categoryId, string direction, CancellationToken cancellationToken = default)
        {
            try
            {
                var category = await _categoryRepository.GetByIdAsync(categoryId, cancellationToken);
                if (category == null)
                {
                    return ApiResponse<bool>.CreateFail("任务分类不存在");
                }

                var allCategories = await _categoryRepository.GetAllAsync(cancellationToken);
                var sortedCategories = allCategories.OrderBy(c => c.SortOrder).ToList();

                var currentIndex = sortedCategories.FindIndex(c => c.CategoryId == categoryId);
                if (currentIndex == -1)
                {
                    return ApiResponse<bool>.CreateFail("任务分类不存在");
                }

                int targetIndex;
                if (direction.ToLower() == "up")
                {
                    if (currentIndex == 0)
                    {
                        return ApiResponse<bool>.CreateFail("已经是第一个分类");
                    }
                    targetIndex = currentIndex - 1;
                }
                else if (direction.ToLower() == "down")
                {
                    if (currentIndex == sortedCategories.Count - 1)
                    {
                        return ApiResponse<bool>.CreateFail("已经是最后一个分类");
                    }
                    targetIndex = currentIndex + 1;
                }
                else
                {
                    return ApiResponse<bool>.CreateFail("无效的移动方向");
                }

                // 交换排序顺序
                var currentCategory = sortedCategories[currentIndex];
                var targetCategory = sortedCategories[targetIndex];

                var tempSortOrder = currentCategory.SortOrder;
                currentCategory.SortOrder = targetCategory.SortOrder;
                targetCategory.SortOrder = tempSortOrder;

                // 更新数据库
                await _categoryRepository.UpdateAsync(currentCategory, cancellationToken);
                await _categoryRepository.UpdateAsync(targetCategory, cancellationToken);

                _logger.LogInformation("成功更新分类排序: CategoryId={CategoryId}, Direction={Direction}", categoryId, direction);
                return ApiResponse<bool>.CreateSuccess(true, "排序更新成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新分类排序时发生错误: CategoryId={CategoryId}, Direction={Direction}", categoryId, direction);
                return ApiResponse<bool>.CreateFail("更新排序失败");
            }
        }

        /// <summary>
        /// 映射实体到DTO
        /// </summary>
        private static TaskCategoryDto MapToDto(TaskCategory category)
        {
            return new TaskCategoryDto
            {
                CategoryId = category.CategoryId,
                Name = category.Name,
                Description = category.Description,
                Color = category.Color,
                Icon = category.Icon,
                SortOrder = category.SortOrder,
                IsActive = category.IsActive,
                CreatedBy = category.CreatedBy,
                CreatedAt = category.CreatedAt,
                UpdatedAt = category.UpdatedAt
            };
        }
    }
}
