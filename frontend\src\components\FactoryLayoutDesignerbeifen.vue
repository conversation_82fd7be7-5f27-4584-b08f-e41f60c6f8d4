<template>
  <div class="layout-designer">
    <!-- 设计器工具栏 -->
    <div class="designer-toolbar">
      <div class="toolbar-section">
        <h3>布局设计器</h3>
        <div class="layout-stats">
          <div class="stat-item">
            <span class="stat-label">总工位数:</span>
            <span class="stat-value">{{ totalWorkstations }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">区域数:</span>
            <span class="stat-value">{{ zones.length }}</span>
          </div>
        </div>
        <div class="toolbar-actions">
          <el-button type="primary" @click="addZone" :icon="Plus">添加区域</el-button>
          <el-button @click="resetLayout" :icon="Refresh">重置布局</el-button>
          <el-button type="success" @click="saveLayout" :icon="Check">保存布局</el-button>
          <el-button @click="previewLayout" :icon="View">预览</el-button>

          <!-- 文件操作 -->
          <div class="file-operations">
            <el-button type="info" @click="triggerImport" :icon="Upload">导入布局</el-button>
            <el-button @click="exportLayout" :icon="Download">导出布局</el-button>
            <input
              ref="fileInput"
              type="file"
              accept=".json"
              @change="handleFileImport"
              style="display: none"
            />
          </div>
        </div>

        <!-- 操作状态指示器 -->
        <div class="operation-status" v-if="selectedZones.length > 1 || dragState.isGroupDragging || isSelecting">
          <el-alert
            v-if="dragState.isGroupDragging"
            title="🔥 编组移动中"
            :description="`正在移动 ${selectedZones.length} 个区域`"
            type="warning"
            :closable="false"
            show-icon
          />
          <el-alert
            v-else-if="isSelecting"
            title="📦 框选模式"
            description="拖拽鼠标进行框选，按住Ctrl键可追加选择"
            type="info"
            :closable="false"
            show-icon
          />
          <el-alert
            v-else-if="selectedZones.length > 1"
            title="🎯 编组模式激活"
            :description="`已选择 ${selectedZones.length} 个区域，拖拽任意区域进行编组移动`"
            type="success"
            :closable="false"
            show-icon
          />
        </div>

        <!-- 当前选择状态详情 -->
        <div class="selection-details" v-if="selectedZones.length > 0">
          <h5>选择状态详情</h5>
          <div class="details-content">
            <div class="detail-item">
              <span class="label">选中区域:</span>
              <span class="value">{{ selectedZones.length }} 个</span>
            </div>
            <div class="detail-item">
              <span class="label">当前区域:</span>
              <span class="value">{{ selectedZone?.name || '无' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">状态:</span>
              <span class="value status" :class="getSelectionStatusClass()">
                {{ getSelectionStatusText() }}
              </span>
            </div>
            <div class="selected-zones-list" v-if="selectedZones.length > 1">
              <span class="label">选中列表:</span>
              <div class="zones-tags">
                <span
                  v-for="zoneId in selectedZones"
                  :key="zoneId"
                  class="zone-tag"
                  :style="{ backgroundColor: getZoneById(zoneId)?.color }"
                >
                  {{ getZoneById(zoneId)?.name }}
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- 操作提示 -->
        <div class="operation-tips">
          <h5>操作提示</h5>
          <div class="tip-item">
            <span class="tip-key">Ctrl + 点击</span>
            <span class="tip-desc">多选区域</span>
          </div>
          <div class="tip-item">
            <span class="tip-key">拖拽空白</span>
            <span class="tip-desc">框选区域</span>
          </div>
          <div class="tip-item">
            <span class="tip-key">拖拽区域</span>
            <span class="tip-desc">移动/编组移动</span>
          </div>
          <div class="tip-item">
            <span class="tip-key">Esc</span>
            <span class="tip-desc">取消选择</span>
          </div>
        </div>
      </div>

      <!-- 区域列表 -->
      <div class="zones-list">
        <h4>区域列表</h4>
        <div class="zone-items">
          <div
            v-for="zone in zones"
            :key="zone.id"
            class="zone-item"
            :class="{ active: selectedZone?.id === zone.id || selectedZones.includes(zone.id) }"
            @click="selectZone(zone, $event)"
          >
            <div class="zone-color" :style="{ backgroundColor: zone.color }"></div>
            <span class="zone-name">{{ zone.name }}</span>
            <div class="zone-info">{{ zone.rows }}×{{ zone.cols }}</div>
            <el-button
              size="small"
              type="danger"
              :icon="Delete"
              @click.stop="deleteZone(zone.id)"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 动态尺寸设计画布 -->
    <div class="designer-canvas" ref="canvasRef">
      <div class="canvas-container">
        <div
          class="canvas-grid"
          :style="getCanvasStyle()"
          @mousedown="handleCanvasMouseDown"
          @click="handleCanvasClick"
        >
          <!-- 网格背景 - 使用CSS背景图案 -->
          <div class="grid-background-layer" :style="getGridBackgroundStyle()"></div>

        <!-- 区域标题 - 外部显示 -->
        <div
          v-for="zone in zones"
          :key="`header-${zone.id}`"
          class="zone-header-external"
          :style="{
            left: `${zone.x}px`,
            top: `${zone.y - 25}px`,
            color: zone.color,
            fontWeight: 'bold',
            fontSize: '12px',
            position: 'absolute',
            zIndex: 10, // 确保标题在区域之上
            pointerEvents: 'none'
          }"
        >
          {{ zone.name }} ({{ zone.rows }}×{{ zone.cols }})
        </div>

        <!-- 框选框 -->
        <div
          v-if="selectionBox.visible"
          class="selection-box"
          :style="getSelectionBoxStyle()"
        >
          <div class="selection-info" v-if="isSelecting">
            框选中 {{ selectedZones.length }} 个区域
          </div>
        </div>

        <!-- 多选边框标识 -->
        <div
          v-if="selectedZones.length > 1 && !dragState.isGroupDragging"
          class="multi-selection-border"
          :style="getMultiSelectionBorderStyle()"
        ></div>

        <!-- 可拖拽的区域 -->
        <div
          v-for="zone in zones"
          :key="zone.id"
          class="draggable-zone"
          :class="{
            selected: selectedZone?.id === zone.id || selectedZones.includes(zone.id),
            'multi-selected': selectedZones.includes(zone.id) && selectedZones.length > 1,
            'single-selected': selectedZones.includes(zone.id) && selectedZones.length === 1,
            'being-resized': resizingZone?.id === zone.id,
            'group-dragging': dragState.isGroupDragging && selectedZones.includes(zone.id)
          }"
          :style="getZoneStyle(zone)"
          @mousedown="startDrag(zone, $event)"
          @click.stop="selectZone(zone, $event)"
        >
          <!-- 工位网格预览 - 占满整个区域 -->
          <div class="workstation-grid" :style="getGridStyle(zone)">
            <div
              v-for="n in (zone.rows * zone.cols)"
              :key="n"
              class="workstation-preview"
              :style="{ backgroundColor: zone.color }"
            >
              {{ getWorkstationNumber(zone, n) }}
            </div>
          </div>

          <!-- 调整手柄 -->
          <div class="resize-handles" v-if="selectedZone?.id === zone.id && selectedZones.length === 1">
            <div class="resize-handle nw" @mousedown.stop="startResize(zone, 'nw', $event)"></div>
            <div class="resize-handle ne" @mousedown.stop="startResize(zone, 'ne', $event)"></div>
            <div class="resize-handle sw" @mousedown.stop="startResize(zone, 'sw', $event)"></div>
            <div class="resize-handle se" @mousedown.stop="startResize(zone, 'se', $event)"></div>
            <div class="resize-handle n" @mousedown.stop="startResize(zone, 'n', $event)"></div>
            <div class="resize-handle s" @mousedown.stop="startResize(zone, 's', $event)"></div>
            <div class="resize-handle w" @mousedown.stop="startResize(zone, 'w', $event)"></div>
            <div class="resize-handle e" @mousedown.stop="startResize(zone, 'e', $event)"></div>
          </div>
        </div>
      </div>
      </div>
    </div>

    <!-- 属性面板 -->
    <div class="properties-panel" v-if="selectedZone && selectedZones.length <=1">
      <h4>区域属性</h4>
      <el-form :model="selectedZone" label-width="80px" size="small">
        <el-form-item label="区域名称">
          <el-input v-model="selectedZone.name" />
        </el-form-item>

        <el-form-item label="颜色">
          <el-color-picker v-model="selectedZone.color" />
        </el-form-item>

        <el-form-item label="行数">
          <el-input-number
            v-model="selectedZone.rows"
            :min="1"
            :max="20"
            @change="updateZoneGrid"
          />
        </el-form-item>

        <el-form-item label="列数">
          <el-input-number
            v-model="selectedZone.cols"
            :min="1"
            :max="20"
            @change="updateZoneGrid"
          />
        </el-form-item>

        <el-form-item label="位置X">
          <el-input-number
            v-model="selectedZone.x"
            :min="0"
            :max="canvasConfig.width - selectedZone.width"
          />
        </el-form-item>

        <el-form-item label="位置Y">
          <el-input-number
            v-model="selectedZone.y"
            :min="0"
            :max="canvasConfig.height - selectedZone.height"
          />
        </el-form-item>

        <el-form-item label="宽度">
          <el-input-number
            v-model="selectedZone.width"
            :min="50"
            :max="canvasConfig.width - selectedZone.x"
          />
        </el-form-item>

        <el-form-item label="高度">
          <el-input-number
            v-model="selectedZone.height"
            :min="40"
            :max="canvasConfig.height - selectedZone.y"
          />
        </el-form-item>

        <el-form-item label="工位起始">
          <el-input-number
            v-model="selectedZone.startWorkstation"
            :min="1"
            :max="1000"
            @change="updateWorkstationNumbers"
          />
        </el-form-item>

        <el-form-item label="水平间距">
          <el-input-number
            v-model="selectedZone.gapX"
            :min="0"
            :max="20"
            :step="1"
            placeholder="px"
          />
        </el-form-item>

        <el-form-item label="垂直间距">
          <el-input-number
            v-model="selectedZone.gapY"
            :min="0"
            :max="20"
            :step="1"
            placeholder="px"
          />
        </el-form-item>

        <!-- 微调控制 -->
        <div class="fine-tune-controls">
          <h5>精确微调</h5>
          <div class="tune-grid">
            <el-button size="small" @click="fineTunePosition('up')" :icon="ArrowUp">上移</el-button>
            <div class="tune-row">
              <el-button size="small" @click="fineTunePosition('left')" :icon="ArrowLeft">左移</el-button>
              <el-button size="small" @click="fineTunePosition('right')" :icon="ArrowRight">右移</el-button>
            </div>
            <el-button size="small" @click="fineTunePosition('down')" :icon="ArrowDown">下移</el-button>
          </div>

          <div class="tune-step">
            <el-form-item label="微调步长">
              <el-select v-model="tuneStep" size="small">
                <el-option label="1px" :value="1" />
                <el-option label="5px" :value="5" />
                <el-option label="10px" :value="10" />
                <el-option label="20px" :value="20" />
              </el-select>
            </el-form-item>
          </div>
        </div>

        <el-button type="danger" @click="deleteZone(selectedZone.id)" :icon="Delete">删除区域</el-button>
      </el-form>
    </div>
     <!-- 多选属性面板（简化版） -->
    <div class="properties-panel" v-else-if="selectedZones.length > 1">
      <h4>编组属性 ({{ selectedZones.length }}个区域)</h4>
      <p style="text-align: center; color: #666; margin-top: 20px;">
        已选择多个区域。
        <br />
        拖拽任意选中区域可进行整体移动。
        <br />
        如需编辑单个区域属性，请先取消多选。
      </p>
       <el-button type="danger" @click="deleteSelectedZones" :icon="Delete" style="width: 100%; margin-top: 20px;">
        删除选中区域 ({{ selectedZones.length }})
      </el-button>
    </div>


    <!-- 画布配置面板 -->
    <div class="canvas-config-panel" v-if="!selectedZone && selectedZones.length === 0">
      <h4>画布配置</h4>
      <el-form :model="canvasConfig" label-width="80px" size="small">
        <el-form-item label="画布宽度">
          <el-input-number
            v-model="canvasConfig.width"
            :min="canvasConfig.minWidth"
            :max="canvasConfig.maxWidth"
            :step="100"
          />
        </el-form-item>

        <el-form-item label="画布高度">
          <el-input-number
            v-model="canvasConfig.height"
            :min="canvasConfig.minHeight"
            :max="canvasConfig.maxHeight"
            :step="100"
          />
        </el-form-item>

        <el-form-item label="网格大小">
          <el-input-number
            v-model="canvasConfig.gridSize"
            :min="10"
            :max="50"
            :step="5"
          />
        </el-form-item>

        <el-form-item label="显示网格">
          <el-switch v-model="canvasConfig.showGrid" />
        </el-form-item>

        <el-form-item label="背景色">
          <el-color-picker v-model="canvasConfig.backgroundColor" />
        </el-form-item>

        <div class="canvas-info">
          <h5>画布信息</h5>
          <div class="info-item">
            <span>尺寸比例:</span>
            <span>{{ (canvasConfig.width / canvasConfig.height).toFixed(2) }}:1</span>
          </div>
          <div class="info-item">
            <span>总面积:</span>
            <span>{{ (canvasConfig.width * canvasConfig.height / 10000).toFixed(1) }}万px²</span>
          </div>
        </div>
      </el-form>
    </div>

    <!-- 预览对话框 -->
    <el-dialog v-model="showPreview" title="布局预览" width="98%" :close-on-click-modal="false" class="preview-dialog" :fullscreen="false" top="2vh">
      <div class="preview-container">
        <div class="preview-canvas" :style="{ width: `${canvasConfig.width}px`, height: `${canvasConfig.height}px`, position: 'relative', margin: '0 auto' }">
          <!-- 与编辑界面相同的网格背景 -->
          <div class="grid-background-layer" :style="getGridBackgroundStyle()"></div>

          <!-- 预览区域标题 - 外部显示 -->
          <div
            v-for="zone in zones"
            :key="`preview-header-${zone.id}`"
            class="preview-zone-header-external"
            :style="{
              left: `${zone.x}px`,
              top: `${zone.y - 20}px`, // 调整预览标题位置
              color: zone.color,
              fontWeight: 'bold',
              fontSize: '11px',
              position: 'absolute',
              zIndex: 10,
              pointerEvents: 'none'
            }"
          >
            {{ zone.name }} ({{ zone.rows }}×{{ zone.cols }})
          </div>

          <!-- 预览区域 - 使用与编辑界面相同的结构 -->
          <div
            v-for="zone in zones"
            :key="zone.id"
            class="preview-zone"
            :style="getZoneStyle(zone)"
          >
            <!-- 工位网格预览 - 与编辑界面完全一致 -->
            <div class="workstation-grid" :style="getGridStyle(zone)">
              <div
                v-for="n in (zone.rows * zone.cols)"
                :key="n"
                class="workstation-preview"
                :style="{ backgroundColor: zone.color }"
              >
                {{ getWorkstationNumber(zone, n) }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <el-button @click="showPreview = false">关闭</el-button>
        <el-button type="primary" @click="exportLayout">导出配置</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Refresh, Check, View, Delete, ArrowUp, ArrowDown, ArrowLeft, ArrowRight, Upload, Download } from '@element-plus/icons-vue'

// 响应式数据
const zones = ref([])
const selectedZone = ref(null)
const selectedZones = ref([]) // 多选区域ID列表
const draggingZone = ref(null) // 仅用于单体拖拽时的引用
const resizingZone = ref(null)
const showPreview = ref(false)
const canvasRef = ref(null)
const fileInput = ref(null)
const tuneStep = ref(5) // 微调步长，默认5px

// 画布配置
const canvasConfig = ref({
  width: 2000,
  height: 1200,
  minWidth: 1200,
  minHeight: 800,
  maxWidth: 4000,
  maxHeight: 2400,
  gridSize: 20,
  showGrid: true,
  backgroundColor: '#fafafa'
})

// 框选功能
const isSelecting = ref(false)
const selectionBox = ref({
  startX: 0,
  startY: 0,
  endX: 0,
  endY: 0,
  visible: false
})

// 拖拽状态
const dragState = reactive({
  isDragging: false,      // 单个区域拖拽
  isResizing: false,
  isGroupDragging: false, // 编组拖拽状态
  startX: 0,              // 鼠标起始X
  startY: 0,              // 鼠标起始Y
  // 单个区域拖拽时使用
  startZoneX: 0,
  startZoneY: 0,
  // 调整大小时使用
  startZoneWidth: 0,
  startZoneHeight: 0,
  resizeDirection: '',
  // 编组拖拽时使用，存储每个选中区域的初始信息
  groupStartPositions: [] // [{ id, startX, startY, width, height }, ...]
})

const defaultZones = [
  { id: 1, name: '区域1', x: 1800, y: 100, width: 120, height: 800, rows: 25, cols: 1, color: '#4A90E2', startWorkstation: 1, gapX: 2, gapY: 2 },
  { id: 2, name: '区域2', x: 600, y: 800, width: 1100, height: 300, rows: 5, cols: 22, color: '#F5A623', startWorkstation: 26, gapX: 2, gapY: 2 },
  { id: 3, name: '区域3', x: 100, y: 800, width: 400, height: 300, rows: 5, cols: 8, color: '#4A90E2', startWorkstation: 136, gapX: 2, gapY: 2 },
  { id: 4, name: '区域4', x: 100, y: 100, width: 450, height: 600, rows: 10, cols: 9, color: '#F8A5C2', startWorkstation: 176, gapX: 2, gapY: 2 },
  { id: 5, name: '区域5', x: 600, y: 100, width: 550, height: 600, rows: 10, cols: 11, color: '#50E3C2', startWorkstation: 266, gapX: 2, gapY: 2 },
  { id: 6, name: '区域6', x: 1200, y: 100, width: 550, height: 600, rows: 10, cols: 11, color: '#BD10E0', startWorkstation: 376, gapX: 2, gapY: 2 }
]

const totalWorkstations = computed(() => {
  return zones.value.reduce((sum, zone) => sum + (zone.rows * zone.cols), 0)
})

const getZoneStyle = (zone) => {
  return {
    left: `${zone.x}px`,
    top: `${zone.y}px`,
    width: `${zone.width}px`,
    height: `${zone.height}px`,
    borderColor: zone.color
  }
}

const getGridStyle = (zone) => {
  const gapX = zone.gapX || 2
  const gapY = zone.gapY || 2
  return {
    gridTemplateColumns: `repeat(${zone.cols}, 1fr)`,
    gridTemplateRows: `repeat(${zone.rows}, 1fr)`,
    gap: `${gapY}px ${gapX}px`
  }
}

const getWorkstationNumber = (zone, index) => {
  return zone.startWorkstation + index - 1
}

const getCanvasStyle = () => {
  return {
    width: `${canvasConfig.value.width}px`,
    height: `${canvasConfig.value.height}px`,
    backgroundColor: canvasConfig.value.backgroundColor,
    border: '2px solid #e5e7eb', // 已存在
    borderRadius: '8px', // 已存在
    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)', // 已存在
    position: 'relative' // 已存在
  }
}

const getGridBackgroundStyle = () => {
  if (!canvasConfig.value.showGrid) {
    return { display: 'none' }
  }
  const gridSize = canvasConfig.value.gridSize
  const majorGridSize = gridSize * 5
  return {
    position: 'absolute',
    top: '0',
    left: '0',
    width: '100%',
    height: '100%',
    pointerEvents: 'none',
    zIndex: 1, // 已存在
    backgroundImage: `
      linear-gradient(to right, #d1d5db 1px, transparent 1px),
      linear-gradient(to bottom, #d1d5db 1px, transparent 1px),
      linear-gradient(to right, #e5e7eb 1px, transparent 1px),
      linear-gradient(to bottom, #e5e7eb 1px, transparent 1px)
    `,
    backgroundSize: `
      ${majorGridSize}px ${majorGridSize}px,
      ${majorGridSize}px ${majorGridSize}px,
      ${gridSize}px ${gridSize}px,
      ${gridSize}px ${gridSize}px
    `,
    opacity: 0.6 // 已存在
  }
}

// MODIFIED: handleCanvasClick - 优化框选结束处理
const handleCanvasClick = (event) => {
  if (isSelecting.value) {
    // 如果刚刚结束框选，不应该立即取消选择，而是由 endCanvasSelection 决定
    // isSelecting.value = false; // 移至 endCanvasSelection
    return;
  }

  if (dragState.isDragging || dragState.isResizing || dragState.isGroupDragging) {
    // 如果是拖拽或调整大小刚结束，不取消选择
    return;
  }

  // 检查是否点击的是真正的空白区域
  const targetIsCanvas = event.target.classList.contains('canvas-grid') ||
                         event.target.classList.contains('grid-background-layer') ||
                         event.target === canvasRef.value?.querySelector('.canvas-grid'); // 更可靠的判断

  if (targetIsCanvas) {
    if (!event.ctrlKey) { // 只有在没有按住Ctrl键时才取消选择
      selectedZone.value = null
      selectedZones.value = []
      selectionBox.value.visible = false // 确保框选框也消失
    }
  }
}


const addZone = () => {
  const newId = Math.max(0, ...zones.value.map(z => z.id)) + 1
  const newZone = {
    id: newId,
    name: `区域${newId}`,
    x: 50 + ((newId -1) % 5) * 150 , // 简单定位，避免重叠
    y: 50 + Math.floor((newId -1) / 5) * 100,
    width: 120,
    height: 80,
    rows: 2,
    cols: 3,
    color: `#${Math.floor(Math.random()*16777215).toString(16).padStart(6, '0')}`,
    startWorkstation: getNextWorkstationStart(),
    gapX: 2,
    gapY: 2
  }
  zones.value.push(newZone)
  selectedZone.value = newZone
  selectedZones.value = [newZone.id] // 新增区域后自动选中
  ElMessage.success('区域添加成功')
}

const getNextWorkstationStart = () => {
  if (zones.value.length === 0) return 1
  let maxEnd = 0
  zones.value.forEach(zone => {
    const end = zone.startWorkstation + (zone.rows * zone.cols) - 1
    if (end > maxEnd) maxEnd = end
  })
  return maxEnd + 1
}

const getZoneById = (zoneId) => {
  return zones.value.find(z => z.id === zoneId)
}

const getSelectionStatusText = () => {
  if (dragState.isGroupDragging) return '编组拖拽中'
  if (isSelecting.value) return '框选中'
  if (selectedZones.value.length > 1) return '多选编组'
  if (selectedZones.value.length === 1) return '单选'
  return '未选择'
}

const getSelectionStatusClass = () => {
  if (dragState.isGroupDragging) return 'dragging'
  if (isSelecting.value) return 'selecting'
  if (selectedZones.value.length > 1) return 'multi'
  if (selectedZones.value.length === 1) return 'single'
  return 'none'
}

// MODIFIED: selectZone - 确保事件处理
const selectZone = (zone, event) => {
  if (event) {
    event.stopPropagation(); // 始终阻止冒泡
  }

  if (dragState.isResizing) return; // 如果正在调整大小，不改变选择

  const zoneId = zone.id;
  if (event && event.ctrlKey) {
    if (selectedZones.value.includes(zoneId)) {
      selectedZones.value = selectedZones.value.filter(id => id !== zoneId);
      if (selectedZone.value?.id === zoneId) {
        selectedZone.value = selectedZones.value.length > 0 ? getZoneById(selectedZones.value[0]) : null;
      }
    } else {
      selectedZones.value.push(zoneId);
      selectedZone.value = zone; // 将当前点击的设为 active zone
    }
  } else {
    // 如果点击的区域不在当前多选组中，或者当前不是多选状态，则单选
    if (!selectedZones.value.includes(zoneId) || selectedZones.value.length <= 1) {
        selectedZones.value = [zoneId];
        selectedZone.value = zone;
    } else if (selectedZones.value.includes(zoneId) && selectedZones.value.length > 1) {
        // 如果点击的是多选组中的一个，并且是普通点击（非Ctrl），则将此区域设为 selectedZone
        // 但保持 selectedZones 不变，以便后续可能的编组拖拽
        selectedZone.value = zone;
    }
  }
}


const getSelectionBoxStyle = () => {
  const left = Math.min(selectionBox.value.startX, selectionBox.value.endX)
  const top = Math.min(selectionBox.value.startY, selectionBox.value.endY)
  const width = Math.abs(selectionBox.value.endX - selectionBox.value.startX)
  const height = Math.abs(selectionBox.value.endY - selectionBox.value.startY)
  return {
    position: 'absolute',
    left: `${left}px`,
    top: `${top}px`,
    width: `${width}px`,
    height: `${height}px`,
    border: '2px dashed #3b82f6',
    background: 'rgba(59, 130, 246, 0.1)',
    pointerEvents: 'none',
    zIndex: 1000 // 已存在
  }
}

const getMultiSelectionBorderStyle = () => {
  if (selectedZones.value.length <= 1) return {}
  const selectedZoneObjects = zones.value.filter(z => selectedZones.value.includes(z.id))
  if (selectedZoneObjects.length === 0) return {}

  let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity
  selectedZoneObjects.forEach(zone => {
    minX = Math.min(minX, zone.x)
    minY = Math.min(minY, zone.y)
    maxX = Math.max(maxX, zone.x + zone.width)
    maxY = Math.max(maxY, zone.y + zone.height)
  })
  const padding = 10
  return {
    position: 'absolute',
    left: `${minX - padding}px`,
    top: `${minY - padding}px`,
    width: `${maxX - minX + padding * 2}px`,
    height: `${maxY - minY + padding * 2}px`,
    border: '3px dashed #10b981',
    background: 'rgba(16, 185, 129, 0.05)',
    borderRadius: '8px',
    pointerEvents: 'none',
    zIndex: 5 // 低于区域，但高于网格
  }
}

const handleCanvasMouseDown = (event) => {
  if (event.target.classList.contains('draggable-zone') ||
      event.target.closest('.draggable-zone') ||
      event.target.classList.contains('resize-handle')) {
    return
  }
  startCanvasSelection(event)
}

const startCanvasSelection = (event) => {
  // 如果没有按住Ctrl键，并且不是从一个已选中的区域开始拖拽（意图是移动），则清空选择
  if (!event.ctrlKey) {
    selectedZones.value = []
    selectedZone.value = null
  }

  const rect = event.currentTarget.getBoundingClientRect() // canvas-grid
  selectionBox.value.startX = event.clientX - rect.left
  selectionBox.value.startY = event.clientY - rect.top
  selectionBox.value.endX = selectionBox.value.startX
  selectionBox.value.endY = selectionBox.value.startY
  selectionBox.value.visible = true
  isSelecting.value = true
  event.preventDefault()
}

const updateCanvasSelection = (event) => {
  if (!isSelecting.value) return
  const canvasElement = canvasRef.value?.querySelector('.canvas-grid')
  if (!canvasElement) return
  const rect = canvasElement.getBoundingClientRect()
  const newEndX = Math.max(0, Math.min(canvasConfig.value.width, event.clientX - rect.left))
  const newEndY = Math.max(0, Math.min(canvasConfig.value.height, event.clientY - rect.top))
  selectionBox.value.endX = newEndX
  selectionBox.value.endY = newEndY

  const newSelectedIdsInBox = []
  zones.value.forEach(zone => {
    if (isZoneInSelection(zone)) {
      newSelectedIdsInBox.push(zone.id)
    }
  })

  if (event.ctrlKey) {
    // 按住Ctrl是追加。需要合并当前选中的和框选到的。
    // 但框选逻辑通常是：Ctrl时，新框选到的加入；非Ctrl时，新框选到的替换。
    // 这里简化：如果在框选过程中，按住Ctrl，则仅影响本次框选的累加。
    // 实际更常见的交互是，框选结束时，如果按住Ctrl，则将框选结果与已有选择合并。
    // 为简单起见，这里框选时，如果Ctrl按下，则保留已有的，并将框里的添加进去
    // 如果没有按Ctrl，则selectedZones直接等于框里的
    // * 更正 *：框选时，Ctrl 键通常意味着对已有选择的补充。
    //   如果初始没有按 Ctrl，框选开始时已清空了 selectedZones。
    //   如果初始按了 Ctrl，框选开始时未清空 selectedZones。
    //   所以这里只需要更新 selectedZones 为框选范围内的即可，因为初始状态已处理。

    // 找出原先在 selectedZones 中但不在新框选范围内的（如果按住Ctrl）
    const previouslySelectedAndNotInBox = event.ctrlKey
        ? selectedZones.value.filter(id => !newSelectedIdsInBox.includes(id))
        : [];

    // 合并：之前按Ctrl选中的（不在框内）+ 当前框选的
    selectedZones.value = [...new Set([...previouslySelectedAndNotInBox, ...newSelectedIdsInBox])];

  } else {
    selectedZones.value = [...newSelectedIdsInBox]
  }
}

// MODIFIED: endCanvasSelection - 优化框选结束处理
const endCanvasSelection = () => {
  if (isSelecting.value) {
    isSelecting.value = false
    selectionBox.value.visible = false // 隐藏框选框

    if (selectedZones.value.length > 0) {
      // 确保 selectedZone 是选中的一个（通常是第一个或最后一个添加的）
      if (!selectedZones.value.includes(selectedZone.value?.id)) {
          selectedZone.value = getZoneById(selectedZones.value[0]);
      }
      if (selectedZones.value.length > 1) {
          ElMessage.success(`框选完成，选中 ${selectedZones.value.length} 个区域`);
      } else if (selectedZone.value) {
          ElMessage.success(`框选完成，选中区域: ${selectedZone.value.name}`);
      }
    } else {
      selectedZone.value = null // 没有选中任何区域
    }
    // 不在这里清除 selectionBox 的坐标，以便调试或后续可能的功能
  }
}


const isZoneInSelection = (zone) => {
  const selLeft = Math.min(selectionBox.value.startX, selectionBox.value.endX)
  const selTop = Math.min(selectionBox.value.startY, selectionBox.value.endY)
  const selRight = Math.max(selectionBox.value.startX, selectionBox.value.endX)
  const selBottom = Math.max(selectionBox.value.startY, selectionBox.value.endY)
  const zoneLeft = zone.x
  const zoneTop = zone.y
  const zoneRight = zone.x + zone.width
  const zoneBottom = zone.y + zone.height
  return !(selRight < zoneLeft || selLeft > zoneRight || selBottom < zoneTop || selTop > zoneBottom)
}

const deleteZone = (zoneId) => {
  ElMessageBox.confirm('确定要删除这个区域吗？', '确认删除', { type: 'warning' })
    .then(() => {
      zones.value = zones.value.filter(z => z.id !== zoneId)
      if (selectedZone.value?.id === zoneId) {
        selectedZone.value = null
      }
      selectedZones.value = selectedZones.value.filter(id => id !== zoneId);
      if (selectedZones.value.length > 0 && !selectedZones.value.includes(selectedZone.value?.id)) {
          selectedZone.value = getZoneById(selectedZones.value[0]);
      } else if (selectedZones.value.length === 0) {
          selectedZone.value = null;
      }
      ElMessage.success('区域删除成功')
    }).catch(() => {})
}

// 新增：删除所有选中区域
const deleteSelectedZones = () => {
    if (selectedZones.value.length === 0) return;
    ElMessageBox.confirm(`确定要删除选中的 ${selectedZones.value.length} 个区域吗？`, '批量删除确认', { type: 'warning' })
    .then(() => {
        zones.value = zones.value.filter(z => !selectedZones.value.includes(z.id));
        selectedZones.value = [];
        selectedZone.value = null;
        ElMessage.success('选中的区域已全部删除');
    }).catch(() => {});
}


const resetLayout = () => {
  ElMessageBox.confirm('确定要重置为默认布局吗？', '确认重置', { type: 'warning' })
    .then(() => {
      zones.value = JSON.parse(JSON.stringify(defaultZones)) // 深拷贝
      selectedZone.value = null
      selectedZones.value = []
      ElMessage.success('布局重置成功')
    }).catch(() => {})
}

const updateZoneGrid = () => {
  updateWorkstationNumbers()
}

const updateWorkstationNumbers = () => {
  let currentStart = 1
  zones.value.sort((a, b) => a.id - b.id).forEach(zone => { // 简单排序，实际可能需要更复杂的排序逻辑
    zone.startWorkstation = currentStart
    currentStart += zone.rows * zone.cols
  })
}

const fineTunePosition = (direction) => {
  if (!selectedZone.value || selectedZones.value.length > 1) {
    ElMessage.warning('请先单独选择一个区域进行微调')
    return
  }
  const zone = selectedZone.value
  const step = tuneStep.value
  const maxX = canvasConfig.value.width - zone.width
  const maxY = canvasConfig.value.height - zone.height
  switch (direction) {
    case 'up': zone.y = Math.max(0, zone.y - step); break
    case 'down': zone.y = Math.min(maxY, zone.y + step); break
    case 'left': zone.x = Math.max(0, zone.x - step); break
    case 'right': zone.x = Math.min(maxX, zone.x + step); break
  }
}

// MODIFIED: startDrag - 确保编组拖拽时不重置选择状态，并正确记录初始位置
const startDrag = (zone, event) => {
  if (event.target.classList.contains('resize-handle')) return;
  event.stopPropagation(); // 阻止事件冒泡到画布的 mousedown

  // 判断是否为编组拖拽
  const isGroupDrag = selectedZones.value.length > 1 && selectedZones.value.includes(zone.id);

  dragState.startX = event.clientX;
  dragState.startY = event.clientY;

  if (isGroupDrag) {
    dragState.isGroupDragging = true;
    dragState.isDragging = false; // 不是单个拖拽
    // MODIFIED: 完善初始位置记录 (第3点)
    dragState.groupStartPositions = selectedZones.value.map(id => {
      const target = zones.value.find(z => z.id === id);
      return {
        id: id,
        startX: target.x,
        startY: target.y,
        width: target.width,   // 添加宽度
        height: target.height  // 添加高度
      };
    });
    // 确保当前拖拽的区域是 selectedZone (如果它还不是)
    if (selectedZone.value?.id !== zone.id) {
        selectedZone.value = zone;
    }
    console.log("Starting group drag with zones:", dragState.groupStartPositions.map(p => p.id));
  } else {
    // 单个区域拖拽或通过拖拽选择单个区域
    dragState.isDragging = true;
    dragState.isGroupDragging = false;
    draggingZone.value = zone; // 用于 handleMouseMove 中引用
    dragState.startZoneX = zone.x;
    dragState.startZoneY = zone.y;

    // MODIFIED: 多选状态在拖拽时被重置 (第1点)
    // 如果当前点击的zone不在已选列表中，或者已选列表为空/只有一个（即非多选状态下开始拖拽）
    if (!event.ctrlKey) { // 如果没有按住Ctrl，则拖拽时总是将当前区域设为唯一选中
        selectedZones.value = [zone.id];
        selectedZone.value = zone;
    } else { // 按住了Ctrl
        if (!selectedZones.value.includes(zone.id)) {
            selectedZones.value.push(zone.id);
        }
        selectedZone.value = zone; // 将当前拖拽的激活
    }
    console.log("Starting single drag for zone:", zone.id, "Current selected zones:", selectedZones.value);
  }
  event.preventDefault();
}


const startResize = (zone, direction, event) => {
  // 确保只有单选时才能调整大小
  if (selectedZones.value.length > 1) return;

  resizingZone.value = zone
  dragState.isResizing = true
  dragState.resizeDirection = direction
  dragState.startX = event.clientX
  dragState.startY = event.clientY
  dragState.startZoneX = zone.x
  dragState.startZoneY = zone.y
  dragState.startZoneWidth = zone.width
  dragState.startZoneHeight = zone.height
  event.preventDefault()
  event.stopPropagation()
}

// MODIFIED: handleMouseMove - 修复编组边界计算
const handleMouseMove = (event) => {
  if (isSelecting.value) {
    updateCanvasSelection(event)
    return
  }

  if (dragState.isGroupDragging) {
    const deltaX = event.clientX - dragState.startX;
    const deltaY = event.clientY - dragState.startY;

    let groupMinX = Infinity, groupMinY = Infinity;
    let groupMaxX = -Infinity, groupMaxY = -Infinity;

    // 计算移动后整个编组的预估边界
    dragState.groupStartPositions.forEach(posInfo => {
      const currentZone = getZoneById(posInfo.id); // 获取最新的区域信息，以防万一（虽然宽高应该不变）
      if (currentZone) {
        const newX = posInfo.startX + deltaX;
        const newY = posInfo.startY + deltaY;
        groupMinX = Math.min(groupMinX, newX);
        groupMinY = Math.min(groupMinY, newY);
        // MODIFIED: 编组拖拽边界计算错误 (第2点)
        groupMaxX = Math.max(groupMaxX, newX + currentZone.width);  // 使用当前区域的宽度
        groupMaxY = Math.max(groupMaxY, newY + currentZone.height); // 使用当前区域的高度
      }
    });

    let constrainedDeltaX = deltaX;
    let constrainedDeltaY = deltaY;

    // X轴边界约束
    if (groupMinX < 0) {
      constrainedDeltaX = deltaX - groupMinX; // 修正量，使其不小于0
    } else if (groupMaxX > canvasConfig.value.width) {
      constrainedDeltaX = deltaX - (groupMaxX - canvasConfig.value.width); // 修正量，使其不大于画布宽度
    }

    // Y轴边界约束
    if (groupMinY < 0) {
      constrainedDeltaY = deltaY - groupMinY;
    } else if (groupMaxY > canvasConfig.value.height) {
      constrainedDeltaY = deltaY - (groupMaxY - canvasConfig.value.height);
    }

    // 应用约束后的位移到每个选中的区域
    selectedZones.value.forEach(zoneId => {
      const zoneToMove = getZoneById(zoneId);
      const originalPos = dragState.groupStartPositions.find(p => p.id === zoneId);
      if (zoneToMove && originalPos) {
        zoneToMove.x = originalPos.startX + constrainedDeltaX;
        zoneToMove.y = originalPos.startY + constrainedDeltaY;
      }
    });
  } else if (dragState.isDragging && draggingZone.value) {
    const zone = draggingZone.value
    const deltaX = event.clientX - dragState.startX
    const deltaY = event.clientY - dragState.startY
    const newX = dragState.startZoneX + deltaX
    const newY = dragState.startZoneY + deltaY
    zone.x = Math.max(0, Math.min(newX, canvasConfig.value.width - zone.width))
    zone.y = Math.max(0, Math.min(newY, canvasConfig.value.height - zone.height))
  } else if (dragState.isResizing && resizingZone.value) {
    const zone = resizingZone.value
    const deltaX = event.clientX - dragState.startX
    const deltaY = event.clientY - dragState.startY
    const direction = dragState.resizeDirection
    const canvasWidth = canvasConfig.value.width
    const canvasHeight = canvasConfig.value.height

    if (direction.includes('e')) {
      zone.width = Math.max(50, Math.min(canvasWidth - zone.x, dragState.startZoneWidth + deltaX))
    }
    if (direction.includes('w')) {
      const newWidth = Math.max(50, dragState.startZoneWidth - deltaX)
      const newX = dragState.startZoneX + deltaX
      if (newX >= 0 && (dragState.startZoneX + dragState.startZoneWidth - newX) >= 50) {
        zone.x = newX
        zone.width = dragState.startZoneX + dragState.startZoneWidth - newX
      }
    }
    if (direction.includes('s')) {
      zone.height = Math.max(40, Math.min(canvasHeight - zone.y, dragState.startZoneHeight + deltaY))
    }
    if (direction.includes('n')) {
      const newHeight = Math.max(40, dragState.startZoneHeight - deltaY)
      const newY = dragState.startZoneY + deltaY
      if (newY >= 0 && (dragState.startZoneY + dragState.startZoneHeight - newY) >= 40) {
        zone.y = newY
        zone.height = dragState.startZoneY + dragState.startZoneHeight - newY
      }
    }
  }
}

// MODIFIED: handleMouseUp - 正确结束状态
const handleMouseUp = (event) => {
  if (isSelecting.value) {
    endCanvasSelection(); // 框选结束处理
    // isSelecting 状态会在 endCanvasSelection 中处理
  }

  // 拖拽或调整大小结束后，保留选择状态
  // selectedZone 和 selectedZones 在拖拽过程中已经维护好了

  dragState.isDragging = false;
  dragState.isResizing = false;
  dragState.isGroupDragging = false;
  dragState.groupStartPositions = []; // 清空编组初始位置
  draggingZone.value = null;
  resizingZone.value = null;
}


const handleKeyDown = (event) => {
  if (event.key === 'Escape') {
    selectedZones.value = []
    selectedZone.value = null
    if (isSelecting.value) {
        isSelecting.value = false;
        selectionBox.value.visible = false;
    }
  } else if (event.key === 'Delete' || event.key === 'Backspace') { // Backspace for mac
    if (selectedZones.value.length > 0) {
        deleteSelectedZones();
    } else if (selectedZone.value) { // 虽然 selectedZone 通常意味着 selectedZones 至少有一个
        deleteZone(selectedZone.value.id);
    }
  }
}

const setupEventListeners = () => {
  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
  document.addEventListener('keydown', handleKeyDown)
}

const removeEventListeners = () => {
  document.removeEventListener('mousemove', handleMouseMove)
  document.removeEventListener('mouseup', handleMouseUp)
  document.removeEventListener('keydown', handleKeyDown)
}

const previewLayout = () => {
  showPreview.value = true
}

const saveLayout = () => {
  const layoutConfig = {
    zones: zones.value.map(zone => ({ ...zone })), // 深拷贝
    totalWorkstations: totalWorkstations.value,
    canvas: { ...canvasConfig.value }, // 深拷贝
    metadata: {
      version: '2.1-fixed', // 版本更新
      timestamp: new Date().toISOString(),
      totalZones: zones.value.length,
      designerVersion: 'FactoryLayoutDesigner v2.1'
    }
  }
  emit('layout-saved', layoutConfig)
  ElMessage.success('布局保存成功')
  console.log('Saved Layout:', JSON.stringify(layoutConfig, null, 2));
}

const triggerImport = () => {
  fileInput.value?.click()
}

const handleFileImport = (event) => {
  const file = event.target.files[0]
  if (!file) return
  if (!file.name.endsWith('.json')) {
    ElMessage.error('请选择JSON格式的文件')
    return
  }
  const reader = new FileReader()
  reader.onload = (e) => {
    try {
      const config = JSON.parse(e.target.result)
      importLayout(config)
    } catch (error) {
      ElMessage.error('文件格式错误或内容无法解析，请检查JSON文件')
      console.error('JSON解析错误:', error)
    }
  }
  reader.readAsText(file)
  event.target.value = '' // 允许重复选择同一文件
}

const importLayout = (config) => {
  try {
    if (!config || typeof config !== 'object') throw new Error('配置文件无效');
    if (!config.zones || !Array.isArray(config.zones)) {
      throw new Error('配置文件格式错误：缺少zones数组')
    }
    const requiredFields = ['id', 'name', 'x', 'y', 'width', 'height', 'rows', 'cols', 'color', 'startWorkstation']
    for (const zone of config.zones) {
      for (const field of requiredFields) {
        if (zone[field] === undefined || zone[field] === null) {
          throw new Error(`区域 ${zone.name || zone.id} 缺少必要字段: ${field}`)
        }
      }
      if (zone.gapX === undefined) zone.gapX = 2
      if (zone.gapY === undefined) zone.gapY = 2
    }

    if (config.canvas) {
      // 合并，而不是完全替换，保留一些默认值可能更好
      canvasConfig.value = { ...canvasConfig.value, ...config.canvas };
    }

    zones.value = JSON.parse(JSON.stringify(config.zones)) // 深拷贝
    selectedZone.value = null
    selectedZones.value = []
    ElMessage.success(`成功导入 ${config.zones.length} 个区域的布局配置`)
    if (config.metadata?.timestamp) {
      ElMessage.info(`布局创建时间: ${new Date(config.metadata.timestamp).toLocaleString('zh-CN')}`)
    }
    ElMessage.info(`画布尺寸: ${canvasConfig.value.width} × ${canvasConfig.value.height}`)
  } catch (error) {
    ElMessage.error(`导入失败: ${error.message}`)
    console.error('导入错误:', error)
  }
}

const exportLayout = () => {
  const config = {
    zones: zones.value.map(zone => ({ ...zone })), // 深拷贝
    canvas: { ...canvasConfig.value }, // 深拷贝
    metadata: {
      version: '2.1-fixed',
      timestamp: new Date().toISOString(),
      totalZones: zones.value.length,
      totalWorkstations: totalWorkstations.value,
      designerVersion: 'FactoryLayoutDesigner v2.1',
      exportedBy: 'FactoryLayoutDesigner'
    }
  }
  const blob = new Blob([JSON.stringify(config, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `factory-layout-${Date.now()}.json`
  document.body.appendChild(a); // Required for Firefox
  a.click()
  document.body.removeChild(a);
  URL.revokeObjectURL(url)
  ElMessage.success('配置导出成功')
}

const emit = defineEmits(['layout-saved', 'layout-updated'])

onMounted(() => {
  zones.value = JSON.parse(JSON.stringify(defaultZones)) // 深拷贝
  setupEventListeners()
})

onUnmounted(() => {
  removeEventListeners()
})
</script>

<style scoped>
/* ... 原有样式 ... */
.layout-designer {
  display: grid;
  grid-template-columns: 300px 1fr 250px;
  height: 100vh;
  background: #f5f5f5;
  user-select: none; /* 防止拖拽时选中文本 */
}

.designer-toolbar {
  background: white;
  border-right: 1px solid #e5e7eb;
  padding: 1rem;
  overflow-y: auto;
}

.toolbar-section h3 {
  margin: 0 0 1rem 0;
  color: #374151;
}

.layout-stats {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
  padding: 0.75rem;
  background: #f8fafc;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-label {
  font-size: 0.875rem;
  color: #64748b;
  font-weight: 500;
}

.stat-value {
  font-size: 1rem;
  color: #1e293b;
  font-weight: 600;
  background: #3b82f6;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  min-width: 2rem;
  text-align: center;
}

.toolbar-actions {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem; /* 调整间距 */
}

.file-operations {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e5e7eb;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.operation-status {
  margin-top: 1rem;
  padding: 0.5rem; /* 调整内边距 */
  border-radius: 6px;
  background: #f9fafb;
}
.operation-status .el-alert {
  padding: 8px 16px; /* 微调 Alert 内边距 */
}


.selection-details {
  margin-top: 1rem;
  padding: 1rem;
  background: #f1f5f9;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
}

.selection-details h5 {
  margin: 0 0 0.75rem 0;
  color: #374151;
  font-size: 0.875rem;
  font-weight: 600;
}

.details-content {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.75rem;
}

.detail-item .label {
  color: #64748b;
  font-weight: 500;
}

.detail-item .value {
  color: #1e293b;
  font-weight: 600;
}

.detail-item .status {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.7rem;
}

.status.single { background: #dbeafe; color: #1e40af; }
.status.multi { background: #d1fae5; color: #065f46; }
.status.selecting { background: #fef3c7; color: #92400e; }
.status.dragging { background: #fed7aa; color: #9a3412; } /* 编组拖拽 */

.selected-zones-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.zones-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
}

.zone-tag {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  color: white;
  font-size: 0.7rem;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0,0,0,0.5);
  border: 1px solid rgba(255,255,255,0.3);
}

.operation-tips {
  margin-top: 1.5rem;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
}

.operation-tips h5 {
  margin: 0 0 0.75rem 0;
  color: #374151;
  font-size: 0.875rem;
  font-weight: 600;
}

.tip-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  font-size: 0.75rem;
}

.tip-key {
  background: #e5e7eb;
  color: #374151;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-weight: 600;
  font-family: monospace;
  min-width: 80px; /* 保持宽度 */
  text-align: center;
}

.tip-desc {
  color: #64748b;
  font-weight: 500;
}

.zones-list h4 {
  margin: 1rem 0 0.75rem 0; /* 调整上边距 */
  color: #374151; /* 调整颜色 */
  font-size: 0.9rem; /* 调整大小 */
}

.zone-items {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.zone-item {
  display: flex;
  align-items: center;
  gap: 0.75rem; /* 调整间距 */
  padding: 0.6rem 0.75rem; /* 调整内边距 */
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.15s ease-in-out;
}

.zone-item:hover {
  background: #f0f9ff; /* 调整悬浮背景色 */
  border-color: #7dd3fc; /* 调整悬浮边框色 */
  box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.zone-item.active {
  background: #e0f2fe; /* 调整激活背景色 */
  border-color: #0ea5e9; /* 调整激活边框色 */
  box-shadow: 0 0 0 1px #0ea5e9;
}
.zone-item.active .zone-name {
  font-weight: 600;
  color: #0c4a6e;
}


.zone-color {
  width: 16px;
  height: 16px;
  border-radius: 3px;
  border: 1px solid #d1d5db;
  flex-shrink: 0; /* 防止压缩 */
}

.zone-name {
  flex: 1;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151; /* 调整颜色 */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.zone-info {
  font-size: 0.75rem;
  color: #6b7280;
  margin-left: auto; /* 按钮前推 */
  padding-right: 0.5rem;
}
.zone-item .el-button {
  padding: 4px 6px; /* 微调按钮大小 */
}


.designer-canvas {
  position: relative;
  background: #ffffff;
  overflow: auto; /* 保持可滚动 */
  padding: 1rem;
}

.canvas-container { /* 用于画布居中 */
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100%; /* 确保小画布也能居中 */
}

.canvas-grid {
  position: relative;
  /* 动态尺寸通过内联样式设置 */
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  background: #fafafa; /* 已有 */
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1); /* 已有 */
  /* 确保画布边界清晰可见 */
  border-style: solid; /* 已有 */
  border-width: 3px; /* 已有 */
}

.grid-background-layer {
  /* 动态网格样式通过内联样式设置 */
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1; /* 已有 */
  opacity: 0.6; /* 已有 */
}

.draggable-zone {
  position: absolute;
  border: 2px dashed #6b7280;
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.9);
  cursor: move;
  transition: border-color 0.2s, background-color 0.2s, box-shadow 0.2s; /* 优化过渡 */
  min-width: 30px; /* 已有 */
  min-height: 25px; /* 已有 */
  z-index: 10; /* 默认层级 */
  box-sizing: border-box; /* 确保 padding 和 border 不增加尺寸 */
}

.draggable-zone:hover {
  border-color: #3b82f6;
  background: rgba(59, 130, 246, 0.05);
}

.draggable-zone.selected { /* 单选或多选中的激活项 */
  border-color: #3b82f6;
  border-style: solid;
  background: rgba(59, 130, 246, 0.1);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
  z-index: 15; /* 选中时层级更高 */
}

/* MODIFIED: CSS样式冲突 (第5点) */
.draggable-zone.multi-selected { /* 多选组中的成员（非当前拖拽的那个）*/
  border-color: #10b981;
  border-style: solid;
  border-width: 2px; /* 调整边框宽度 */
  background: rgba(16, 185, 129, 0.1);
  box-shadow: 0 0 0 1px rgba(16, 185, 129, 0.2);
  z-index: 12; /* 多选组层级 */
}
.draggable-zone.multi-selected.selected { /* 多选组中被拖拽的那个，应用更强的选中样式 */
  border-color: #0ea5e9; /* 更鲜明的颜色 */
  border-width: 3px;
  box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.4);
  z-index: 20; /* 最高层级 */
  /* animation: multi-select-pulse 1.5s infinite alternate; */
}


.draggable-zone.group-dragging { /* 应用于编组拖拽时的所有成员 */
  border-color: #f59e0b !important; /* 强制覆盖 */
  border-style: solid !important;
  background: rgba(245, 158, 11, 0.15) !important;
  box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.4) !important;
  z-index: 25 !important; /* 拖拽时最高 */
  /* transform: scale(1.02); */ /* 暂时移除，可能导致边界计算问题 */
  transition: none; /* 拖拽时不需要平滑过渡 */
}

/* 调整大小时的样式 */
.draggable-zone.being-resized {
  z-index: 30; /* 调整大小时最高层级 */
  border-style: solid;
  border-color: #ef4444;
}


/* 框选框样式 */
.selection-box {
  border: 1px dashed #0284c7; /* 调整颜色和样式 */
  background: rgba(14, 165, 233, 0.1); /* 调整颜色 */
  pointer-events: none;
  z-index: 1000; /* 已有 */
  position: absolute; /* 已有 */
}

.selection-info {
  position: absolute;
  bottom: -25px; /* 移到下方 */
  left: 0;
  background: #0284c7; /* 统一颜色 */
  color: white;
  padding: 3px 8px;
  border-radius: 4px;
  font-size: 11px; /* 调整字号 */
  font-weight: 500; /* 调整字重 */
  white-space: nowrap;
  box-shadow: 0 1px 3px rgba(0,0,0,0.15);
}

/* 多选边框样式 */
.multi-selection-border {
  border: 2px dashed #059669; /* 调整颜色 */
  background: rgba(16, 185, 129, 0.03); /* 更淡的背景 */
  border-radius: 6px; /* 调整圆角 */
  pointer-events: none;
  z-index: 5; /* 确保在区域之下，但在网格之上 */
  /* animation: pulse-border 2s infinite; 移除动画，可能影响性能 */
}

/* @keyframes pulse-border {
  0%, 100% { border-color: #10b981; box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.2); }
  50% { border-color: #059669; box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1); }
}

@keyframes multi-select-pulse {
  0%, 100% { box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.4); transform: scale(1); }
  50% { box-shadow: 0 0 0 5px rgba(14, 165, 233, 0.6); transform: scale(1.01); }
} */


.workstation-grid {
  display: grid;
  /* gap: 1px; */ /* 已通过内联样式设置 */
  padding: 2px; /* 增加内边距以便看到背景色 */
  height: 100%;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden; /* 确保工位不超过区域边界 */
}

.workstation-preview {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: 600;
  color: white;
  border-radius: 2px; /* 调整圆角 */
  min-height: 12px; /* 调整最小高度 */
  min-width: 16px; /* 调整最小宽度 */
  text-shadow: 0 1px 1px rgba(0,0,0,0.4); /* 调整阴影 */
  border: 1px solid rgba(255,255,255,0.2); /* 调整边框 */
  transition: transform 0.1s ease, box-shadow 0.1s ease; /* 调整过渡 */
  overflow: hidden;
  padding: 0 1px; /* 防止数字紧贴边缘 */
}

.workstation-preview:hover {
  transform: translateY(-1px) scale(1.05); /* 调整悬浮效果 */
  box-shadow: 0 2px 3px rgba(0,0,0,0.2);
}

.properties-panel {
  background: white;
  border-left: 1px solid #e5e7eb;
  padding: 1rem;
  overflow-y: auto;
}
.properties-panel h4 {
  margin: 0 0 1.2rem 0; /* 调整下边距 */
  color: #374151;
  font-size: 1rem; /* 调整字号 */
}


.canvas-config-panel {
  background: white;
  border-left: 1px solid #e5e7eb;
  padding: 1rem;
  overflow-y: auto;
}

.canvas-config-panel h4 {
  margin: 0 0 1.2rem 0; /* 调整下边距 */
  color: #374151;
  font-size: 1rem; /* 调整字号 */
}

.canvas-info {
  margin-top: 1.5rem;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
}

.canvas-info h5 {
  margin: 0 0 0.75rem 0;
  color: #374151;
  font-size: 0.875rem;
  font-weight: 600;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
}

.info-item span:first-child {
  color: #64748b;
  font-weight: 500;
}

.info-item span:last-child {
  color: #1e293b;
  font-weight: 600;
}

/* 微调控制样式 */
.fine-tune-controls {
  margin: 1.5rem 0; /* 调整外边距 */
  padding: 1rem;
  background: #f8fafc;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
}

.fine-tune-controls h5 {
  margin: 0 0 1rem 0; /* 调整下边距 */
  color: #374151;
  font-size: 0.875rem;
  font-weight: 600;
}

.tune-grid {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.tune-row {
  display: flex;
  gap: 0.5rem;
}

.tune-step {
  margin-top: 0.5rem;
}
.tune-grid .el-button {
  min-width: 70px; /* 调整按钮宽度 */
  padding: 8px 10px; /* 调整按钮内边距 */
}


/* 调整手柄 */
.resize-handles {
  position: absolute;
  top: 0; left: 0; right: 0; bottom: 0;
  pointer-events: none; /* 父容器不捕获事件 */
}
.resize-handle {
  position: absolute;
  width: 8px; height: 8px;
  background: #0ea5e9; /* 调整颜色 */
  border: 1px solid white;
  border-radius: 50%; /* 改为圆形 */
  pointer-events: all; /* 手柄自身捕获事件 */
  z-index: 35; /* 确保在最上层 */
  box-shadow: 0 0 3px rgba(0,0,0,0.3);
}
.resize-handle:hover {
    background: #0284c7;
    transform: scale(1.2);
}

.resize-handle.nw { top: -4px; left: -4px; cursor: nwse-resize; }
.resize-handle.ne { top: -4px; right: -4px; cursor: nesw-resize; }
.resize-handle.sw { bottom: -4px; left: -4px; cursor: nesw-resize; }
.resize-handle.se { bottom: -4px; right: -4px; cursor: nwse-resize; }
.resize-handle.n { top: -4px; left: 50%; transform: translateX(-50%); cursor: ns-resize; }
.resize-handle.s { bottom: -4px; left: 50%; transform: translateX(-50%); cursor: ns-resize; }
.resize-handle.w { top: 50%; left: -4px; transform: translateY(-50%); cursor: ew-resize; }
.resize-handle.e { top: 50%; right: -4px; transform: translateY(-50%); cursor: ew-resize; }

/* 预览样式 */
.preview-container {
  display: flex;
  justify-content: center;
  align-items: flex-start; /* 顶部对齐 */
  padding: 2rem;
  background: #f9fafb;
  border-radius: 8px;
  overflow: auto; /* 允许内容滚动 */
  min-height: 70vh; /* 保持 */
  max-height: calc(96vh - 4rem - 54px); /* 考虑dialog的padding和footer */
}

.preview-dialog .el-dialog__body {
  padding: 0; /* 由 preview-container 控制内边距 */
  /* max-height: none; */ /* 移除，由 preview-container 控制 */
  overflow: hidden; /* 防止双重滚动条 */
}

/* .preview-dialog .el-dialog {
  margin-top: 2vh !important;
  margin-bottom: 2vh !important;
  max-height: 96vh;
  display: flex;
  flex-direction: column;
} */

.preview-canvas {
  background: white;
  border: 1px solid #d1d5db; /* 调整边框 */
  border-radius: 6px; /* 调整圆角 */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08); /* 调整阴影 */
  overflow: visible; /* 预览时允许标题溢出 */
  margin: 0 auto; /* 确保在 preview-container 中居中 */
}

.preview-zone {
  position: absolute;
  border: 1px solid; /* 调整边框 */
  border-radius: 3px; /* 调整圆角 */
  overflow: hidden;
  background: rgba(255, 255, 255, 0.95); /* 更不透明一些 */
  box-sizing: border-box;
}

.preview-zone .workstation-grid {
    padding: 1px; /* 预览时更紧凑 */
}

.preview-zone .workstation-preview {
  font-size: 8px;
  font-weight: 500; /* 调整字重 */
  color: white;
  border-radius: 1px; /* 更小的圆角 */
  min-height: 8px; /* 更小的高度 */
  text-shadow: 0 0.5px 1px rgba(0,0,0,0.4);
  border: 1px solid rgba(255,255,255,0.15);
  padding: 0;
}


/* 响应式设计 - 基本保持不变 */
@media (max-width: 1600px) {
  /* .canvas-grid { width: 1400px; height: 700px; } */ /* 由js控制 */
  .layout-designer { grid-template-columns: 280px 1fr 220px; }
}
@media (max-width: 1200px) {
  .layout-designer { grid-template-columns: 250px 1fr 200px; }
  /* .canvas-grid { width: 1200px; height: 600px; } */
}
@media (max-width: 768px) {
  .layout-designer {
    grid-template-columns: 1fr;
    grid-template-rows: auto 1fr auto auto; /* 调整为4行，工具栏、画布、属性、配置 */
  }
  .designer-toolbar { border-right: none; border-bottom: 1px solid #e5e7eb; }
  .properties-panel, .canvas-config-panel {
    border-left: none;
    border-top: 1px solid #e5e7eb;
    max-height: 30vh; /* 限制移动端高度 */
    overflow-y: auto;
  }
  /* .canvas-grid { width: 100%; max-width: 800px; height: 400px; } */
  .toolbar-actions { flex-direction: row; flex-wrap: wrap; }
}
/* @media (min-width: 1920px) {
  .canvas-grid { width: 2000px; height: 1000px; }
  .layout-designer { grid-template-columns: 320px 1fr 280px; }
} */

</style>