<template>
  <div class="periodic-task-view">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1>🔄 周期性任务管理</h1>
        <p class="subtitle">管理定期执行的任务计划</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="showCreateDialog = true">
          <el-icon><DocumentAdd /></el-icon>
          创建周期性任务
        </el-button>
      </div>
    </div>

    <!-- 筛选器 -->
    <el-card class="filter-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>🔍 筛选</span>
          <el-button link @click="resetFilters" size="small">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </div>
      </template>
      
      <div class="filter-grid">
        <el-select v-model="filters.status" placeholder="状态" clearable class="filter-item">
          <el-option label="全部状态" value="" />
          <el-option label="🟢 活动中" value="Active" />
          <el-option label="⏸️ 已暂停" value="Paused" />
          <el-option label="✅ 已完成" value="Completed" />
          <el-option label="⏰ 已过期" value="Expired" />
          <el-option label="❌ 错误" value="Error" />
        </el-select>
        
        <el-select v-model="filters.recurrenceType" placeholder="重复类型" clearable class="filter-item">
          <el-option label="全部类型" value="" />
          <el-option label="每日" value="Daily" />
          <el-option label="每周" value="Weekly" />
          <el-option label="每月" value="Monthly" />
          <el-option label="每年" value="Yearly" />
          <el-option label="自定义" value="CustomCron" />
        </el-select>
        
        <el-input
          v-model="searchQuery"
          placeholder="🔍 搜索计划名称..."
          clearable
          class="search-input"
          @keyup.enter="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        
        <el-button type="primary" @click="handleSearch" :loading="loading" class="search-btn">
          <el-icon><Search /></el-icon>
          搜索
        </el-button>
      </div>
    </el-card>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <el-card class="stat-card active" shadow="hover">
        <div class="stat-content">
          <div class="stat-icon">🟢</div>
          <div class="stat-info">
            <div class="stat-number">{{ stats.active }}</div>
            <div class="stat-label">活动中</div>
          </div>
        </div>
      </el-card>
      
      <el-card class="stat-card paused" shadow="hover">
        <div class="stat-content">
          <div class="stat-icon">⏸️</div>
          <div class="stat-info">
            <div class="stat-number">{{ stats.paused }}</div>
            <div class="stat-label">已暂停</div>
          </div>
        </div>
      </el-card>
      
      <el-card class="stat-card total" shadow="hover">
        <div class="stat-content">
          <div class="stat-icon">📊</div>
          <div class="stat-info">
            <div class="stat-number">{{ stats.total }}</div>
            <div class="stat-label">总计划</div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 任务列表 -->
    <el-card class="table-card" shadow="hover">
      <template #header>
        <div class="table-header">
          <span>📋 周期性任务计划 ({{ filteredSchedules.length }})</span>
        </div>
      </template>

      <el-table
        :data="paginatedSchedules"
        v-loading="loading"
        row-key="id"
        class="periodic-table"
      >
        <el-table-column label="计划名称" min-width="200">
          <template #default="{ row }">
            <div class="schedule-name-cell">
              <div class="schedule-title">{{ row.name }}</div>
              <div v-if="row.description" class="schedule-desc">{{ truncateText(row.description, 50) }}</div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="重复规则" width="250">
          <template #default="{ row }">
            <div class="recurrence-cell">
              <div class="recurrence-header">
                <el-tag :type="getRecurrenceTagType(row.recurrenceType)" size="small">
                  {{ getRecurrenceText(row.recurrenceType) }}
                </el-tag>
                <span class="execution-time">{{ getExecutionTime(row) }}</span>
              </div>
              <div class="recurrence-desc">{{ row.recurrenceDescription || generateRecurrenceDesc(row) }}</div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="状态" width="120" align="center">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)" size="small">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="启用状态" width="100" align="center">
          <template #default="{ row }">
            <el-switch
              v-model="row.isEnabled"
              @change="handleToggleEnable(row)"
              :loading="row.switching"
              active-text=""
              inactive-text=""
            />
          </template>
        </el-table-column>
        
        <el-table-column label="下次执行" width="150">
          <template #default="{ row }">
            <div v-if="row.nextGenerationTime" class="next-time">
              {{ formatDateTime(row.nextGenerationTime) }}
            </div>
            <span v-else class="no-time">未设置</span>
          </template>
        </el-table-column>
        
        <el-table-column label="已生成" width="80" align="center">
          <template #default="{ row }">
            <span class="generated-count">{{ row.occurrencesGenerated }}</span>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="180" align="center" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button link size="small" @click="viewSchedule(row)">
                <el-icon><View /></el-icon>
                详情
              </el-button>
              <el-button link size="small" @click="editSchedule(row)">
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
              <el-button 
                link 
                size="small" 
                type="danger" 
                @click="deleteSchedule(row)"
              >
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :disabled="loading"
          :background="true"
          layout="total, sizes, prev, pager, next, jumper"
          :total="filteredSchedules.length"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 创建/编辑对话框 -->
    <PeriodicTaskDialog
      v-model="showCreateDialog"
      :schedule="editingSchedule"
      :is-edit="isEditMode"
      @close="closeDialog"
      @submit="onScheduleSubmit"
    />

    <!-- 详情对话框 -->
    <PeriodicTaskDetailDialog
      v-model="showDetailDialog"
      :schedule="currentSchedule"
      @close="showDetailDialog = false"
      @edit="editSchedule"
      @delete="deleteSchedule"
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  DocumentAdd, Refresh, Search, View, Edit, Delete
} from '@element-plus/icons-vue'
import periodicTaskApi from '@/api/periodicTask'
import PeriodicTaskDialog from './components/PeriodicTaskDialog.vue'
import PeriodicTaskDetailDialog from './components/PeriodicTaskDetailDialog.vue'

// 响应式数据
const loading = ref(false)
const schedules = ref([])
const searchQuery = ref('')

// 过滤器
const filters = reactive({
  status: '',
  recurrenceType: ''
})

// 分页
const pagination = reactive({
  currentPage: 1,
  pageSize: 20
})

// 对话框状态
const showCreateDialog = ref(false)
const showDetailDialog = ref(false)
const editingSchedule = ref(null)
const currentSchedule = ref(null)
const isEditMode = ref(false)

// 计算属性
const filteredSchedules = computed(() => {
  let filtered = schedules.value || []
  
  // 状态过滤
  if (filters.status) {
    filtered = filtered.filter(schedule => schedule.status === filters.status)
  }
  
  // 重复类型过滤
  if (filters.recurrenceType) {
    filtered = filtered.filter(schedule => schedule.recurrenceType === filters.recurrenceType)
  }
  
  // 搜索过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(schedule => 
      schedule.name?.toLowerCase().includes(query) ||
      schedule.description?.toLowerCase().includes(query)
    )
  }
  
  return filtered
})

const paginatedSchedules = computed(() => {
  const start = (pagination.currentPage - 1) * pagination.pageSize
  const end = start + pagination.pageSize
  return filteredSchedules.value.slice(start, end)
})

const stats = computed(() => {
  const all = schedules.value || []
  return {
    total: all.length,
    active: all.filter(s => s.status === 'Active' && s.isEnabled).length,
    paused: all.filter(s => s.status === 'Paused' || !s.isEnabled).length
  }
})

// 方法
const loadSchedules = async () => {
  loading.value = true
  try {
    const response = await periodicTaskApi.getPeriodicSchedules({
      pageIndex: 1,
      pageSize: 1000 // 获取所有数据，前端分页
    })
    
    if (response.success) {
      schedules.value = response.data.items || []
    } else {
      ElMessage.error('加载周期性任务计划失败: ' + response.message)
    }
  } catch (error) {
    console.error('加载周期性任务计划失败:', error)
    ElMessage.error('加载失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.currentPage = 1
}

const resetFilters = () => {
  Object.assign(filters, { status: '', recurrenceType: '' })
  searchQuery.value = ''
  pagination.currentPage = 1
}

const handleSizeChange = (size) => {
  pagination.pageSize = size
  pagination.currentPage = 1
}

const handleCurrentChange = (page) => {
  pagination.currentPage = page
}

const handleToggleEnable = async (schedule) => {
  // 添加开关状态以防止重复点击
  schedule.switching = true
  
  try {
    const response = await periodicTaskApi.enablePeriodicSchedule(schedule.id, schedule.isEnabled)
    
    if (response.success) {
      ElMessage.success(schedule.isEnabled ? '计划已启用' : '计划已禁用')
      // 刷新数据以获取最新状态
      await loadSchedules()
    } else {
      // 恢复开关状态
      schedule.isEnabled = !schedule.isEnabled
      ElMessage.error('操作失败: ' + response.message)
    }
  } catch (error) {
    // 恢复开关状态
    schedule.isEnabled = !schedule.isEnabled
    console.error('切换启用状态失败:', error)
    ElMessage.error('操作失败: ' + error.message)
  } finally {
    schedule.switching = false
  }
}

const viewSchedule = (schedule) => {
  currentSchedule.value = schedule
  showDetailDialog.value = true
}

const editSchedule = (schedule) => {
  editingSchedule.value = { ...schedule }
  isEditMode.value = true
  showCreateDialog.value = true
  showDetailDialog.value = false
}

const deleteSchedule = async (schedule) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除周期性任务计划"${schedule.name}"吗？`,
      '确认删除',
      {
        type: 'warning'
      }
    )
    
    const response = await periodicTaskApi.deletePeriodicSchedule(schedule.id)
    
    if (response.success) {
      ElMessage.success('删除成功')
      await loadSchedules()
    } else {
      ElMessage.error('删除失败: ' + response.message)
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败: ' + error.message)
    }
  }
}

const closeDialog = () => {
  showCreateDialog.value = false
  editingSchedule.value = null
  isEditMode.value = false
}

const onScheduleSubmit = async (scheduleData) => {
  try {
    let response
    
    if (isEditMode.value) {
      response = await periodicTaskApi.updatePeriodicSchedule(editingSchedule.value.id, scheduleData)
    } else {
      response = await periodicTaskApi.createPeriodicSchedule(scheduleData)
    }
    
    if (response.success) {
      ElMessage.success(isEditMode.value ? '更新成功' : '创建成功')
      closeDialog()
      await loadSchedules()
    } else {
      ElMessage.error((isEditMode.value ? '更新' : '创建') + '失败: ' + response.message)
    }
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('操作失败: ' + (error.response?.data?.message || error.message))
  }
}

// 工具方法
const truncateText = (text, length) => {
  if (!text || text.length <= length) return text
  return text.substring(0, length) + '...'
}

const getRecurrenceTagType = (type) => {
  const types = {
    'Daily': 'success',
    'Weekly': 'warning',
    'Monthly': 'info',
    'Yearly': 'danger',
    'CustomCron': ''
  }
  return types[type] || ''
}

const getRecurrenceText = (type) => {
  const texts = {
    'Daily': '每日',
    'Weekly': '每周',
    'Monthly': '每月',
    'Yearly': '每年',
    'CustomCron': '自定义'
  }
  return texts[type] || type
}

const getStatusTagType = (status) => {
  const types = {
    'Active': 'success',
    'Paused': 'warning',
    'Completed': 'info',
    'Expired': 'danger',
    'Error': 'danger'
  }
  return types[status] || ''
}

const getStatusText = (status) => {
  const texts = {
    'Active': '活动中',
    'Paused': '已暂停',
    'Completed': '已完成',
    'Expired': '已过期',
    'Error': '错误'
  }
  return texts[status] || status
}

const generateRecurrenceDesc = (schedule) => {
  if (!schedule.recurrenceType) return ''
  
  let desc = ''
  switch (schedule.recurrenceType) {
    case 'Daily':
      desc = schedule.recurrenceInterval > 1 ? `每${schedule.recurrenceInterval}天` : '每天'
      break
    case 'Weekly':
      desc = schedule.recurrenceInterval > 1 ? `每${schedule.recurrenceInterval}周` : '每周'
      if (schedule.daysOfWeek) {
        try {
          const days = JSON.parse(schedule.daysOfWeek)
          const dayNames = ['日', '一', '二', '三', '四', '五', '六']
          const dayText = days.map(d => dayNames[d]).join('、')
          desc += ` (${dayText})`
        } catch (e) {
          // 忽略解析错误
        }
      }
      break
    case 'Monthly':
      desc = schedule.recurrenceInterval > 1 ? `每${schedule.recurrenceInterval}月` : '每月'
      if (schedule.dayOfMonth) {
        desc += `${schedule.dayOfMonth}号`
      }
      break
    case 'Yearly':
      desc = '每年'
      if (schedule.monthOfYear) {
        desc += `${schedule.monthOfYear}月`
      }
      if (schedule.dayOfMonth) {
        desc += `${schedule.dayOfMonth}号`
      }
      break
    case 'CustomCron':
      desc = schedule.cronExpression || '自定义'
      break
  }
  
  return desc
}

const formatDateTime = (dateStr) => {
  if (!dateStr) return ''
  const date = new Date(dateStr)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    hour12: false // 使用24小时制
  })
}

// 获取执行时间
const getExecutionTime = (schedule) => {
  if (!schedule.startDate) return ''

  const date = new Date(schedule.startDate)
  const hours = date.getHours().toString().padStart(2, '0')
  const minutes = date.getMinutes().toString().padStart(2, '0')

  return `${hours}:${minutes}`
}

// 生命周期
onMounted(() => {
  loadSchedules()
})
</script>

<style scoped>
.periodic-task-view {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-left h1 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
}

.subtitle {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.filter-card {
  margin-bottom: 12px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  align-items: center;
}

.filter-item {
  min-width: 140px;
  max-width: 180px;
  flex: 0 0 160px;
}

.search-input {
  min-width: 180px;
  max-width: 220px;
  flex: 1 1 200px;
}

.search-btn {
  min-width: 80px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.stat-card {
  cursor: pointer;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  font-size: 32px;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.table-card {
  margin-bottom: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.periodic-table {
  border-radius: 8px;
  overflow: hidden;
}

.schedule-name-cell {
  cursor: pointer;
}

.schedule-title {
  font-weight: 600;
  color: #409eff;
  margin-bottom: 4px;
}

.schedule-desc {
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
}

.recurrence-cell {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.recurrence-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.execution-time {
  font-size: 12px;
  color: #409eff;
  font-weight: 600;
  background: #ecf5ff;
  padding: 2px 6px;
  border-radius: 4px;
}

.recurrence-desc {
  font-size: 12px;
  color: #606266;
}

.next-time {
  font-size: 12px;
  color: #606266;
}

.no-time {
  color: #c0c4cc;
  font-size: 12px;
}

.generated-count {
  font-weight: 600;
  color: #409eff;
}

.action-buttons {
  display: flex;
  gap: 4px;
  justify-content: center;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #e4e7ed;
}
</style>