<template>
  <!-- Template remains largely the same -->
  <div class="task-detail-view page-container" v-loading="loading">
    <div v-if="task && !loading" class="content-wrapper">
      <!-- Header -->
      <div class="page-header mb-4">
        <div class="header-left">
          <el-button @click="goBack" text size="small" class="back-button">
            <el-icon><ArrowLeft /></el-icon> 返回
          </el-button>
          <h2 class="page-title">{{ task.title }}</h2>
        </div>
        <div class="header-right">
          <el-button type="danger" @click="confirmDeleteTask" :icon="Delete" :loading="deleting">删除</el-button>
        </div>
      </div>

      <!-- Main Content Grid -->
      <el-row :gutter="20">
        <!-- Left Panel: Details & Description -->
        <el-col :xs="24" :sm="16" :md="17">
          <el-card shadow="never" class="details-card mb-4">
             <template #header>
              <div class="card-header">
                <span><el-icon><InfoFilled /></el-icon> 基本信息</span>
              </div>
            </template>
            <div class="info-grid">
              <div class="info-item">
                <span class="item-label">状态:</span>
                <span class="item-value">
                  <!-- Use API status for the model, map labels -->
                  <el-select v-model="task.status" size="small" @change="handleStatusChange" :loading="updatingStatus">
                    <el-option label="未开始" value="unstarted"></el-option>
                    <el-option label="进行中" value="in-progress"></el-option>
                    <el-option label="已完成" value="completed"></el-option>
                  </el-select>
                </span>
              </div>
              <!-- Other info items -->
               <div class="info-item">
                <span class="item-label">优先级:</span>
                <span class="item-value">
                  <el-tag :type="getPriorityType(task.priority)" size="small" effect="light" round>{{ priorityLabel(task.priority) }}</el-tag>
                </span>
              </div>
              <div class="info-item">
                <span class="item-label">负责人:</span>
                <span class="item-value assignee">
                   <el-avatar :size="24" :src="getUserAvatar(task.assigneeId)" class="small-avatar" @error="onAvatarError">{{ getInitials(getUserName(task.assigneeId)) }}</el-avatar>
                   <span>{{ getUserName(task.assigneeId) }}</span>
                </span>
              </div>
              <div class="info-item">
                <span class="item-label">创建时间:</span>
                <span class="item-value">{{ formatDate(task.createDate) || '-' }}</span>
              </div>
              <div class="info-item">
                <span class="item-label">截止时间:</span>
                <span class="item-value" :class="{ 'overdue': checkOverdue(task) && task.status !== 'completed' }">
                  <el-icon><Clock /></el-icon> {{ formatDate(task.endDate, false) || '未设置' }}
                </span>
              </div>
               <!-- Periodic Task Info -->
                <template v-if="task.isPeriodic">
                    <div class="info-item">
                        <span class="item-label">任务类型:</span>
                        <span class="item-value"><el-tag type="success" size="small">周期性</el-tag></span>
                    </div>
                    <div class="info-item">
                        <span class="item-label">开始日期:</span>
                        <span class="item-value">{{ formatDate(task.startDate, false) || '-' }}</span>
                    </div>
                     <div class="info-item">
                        <span class="item-label">结束日期:</span>
                        <span class="item-value">{{ formatDate(task.endDate, false) || '-' }}</span>
                    </div>
                    <div class="info-item">
                        <span class="item-label">频率:</span>
                        <span class="item-value">{{ task.frequency || '-' }}</span>
                    </div>
                </template>
            </div>
            <el-divider />
            <div class="description-section">
              <h4><el-icon><Document /></el-icon> 任务描述</h4>
              <p class="description-content">{{ task.description || '暂无描述' }}</p>
            </div>
          </el-card>

           <el-card shadow="never" class="attachments-card mb-4">
             <!-- Attachment section remains similar, ensure delete uses V2 API -->
              <template #header>
               <div class="card-header">
                 <span><el-icon><Paperclip /></el-icon> 附件</span>
               </div>
             </template>
             <el-upload
                ref="uploadRef"
                action="#"
                :http-request="handleUpload"
                :on-remove="handleRemove"
                :file-list="fileListDisplay"
                :before-upload="beforeUpload"
                :on-exceed="handleExceed"
                multiple
                :limit="5"
                list-type="text"
                class="upload-area"
             >
                <el-button type="primary" :icon="Upload">点击上传</el-button>
                <template #tip>
                  <div class="el-upload__tip">单个文件不超过10MB，最多上传5个文件</div>
                </template>
             </el-upload>
              <div class="attachments-list" v-if="task.attachments && task.attachments.length > 0">
                  <div v-for="attachment in task.attachments" :key="attachment.id || attachment.name" class="attachment-item">
                    <el-icon><Document /></el-icon>
                    <span class="attachment-name">{{ attachment.name }}</span>
                    <span class="attachment-size">({{ formatFileSize(attachment.size) }})</span>
                    <div class="attachment-actions">
                      <el-button type="danger" link size="small" :icon="Delete" @click="confirmDeleteAttachment(attachment)" :loading="deletingAttachmentId === attachment.id">删除</el-button>
                    </div>
                  </div>
              </div>
              <el-empty v-else description="暂无附件"></el-empty>
           </el-card>
        </el-col>

        <!-- Right Panel: Comments & Activity -->
        <el-col :xs="24" :sm="8" :md="7">
          <el-card shadow="never" class="activity-card">
              <!-- Tabs remain the same, ensure API calls use V2 -->
              <el-tabs v-model="activeTab">
                  <el-tab-pane label="评论" name="comments" lazy>
                     <!-- Comment Section: API calls need update -->
                      <div class="comment-section">
                          <div class="comments-list thin-scrollbar" v-loading="loadingComments">
                             <div v-if="taskComments.length > 0">
                                  <div v-for="comment in taskComments" :key="comment.id" class="comment-item">
                                      <el-avatar :size="32" :src="getUserAvatar(comment.userId)" class="comment-avatar" @error="onAvatarError">{{ getInitials(getUserName(comment.userId)) }}</el-avatar>
                                      <div class="comment-content">
                                          <div class="comment-header">
                                              <span class="comment-user">{{ getUserName(comment.userId) }}</span>
                                              <span class="comment-time">{{ formatTimeAgo(comment.createDate || comment.time) }}</span>
                                          </div>
                                          <div class="comment-text">{{ comment.content }}</div>
                                      </div>
                                  </div>
                              </div>
                              <el-empty v-else description="暂无评论"></el-empty>
                          </div>
                          <div class="comment-form">
                              <el-input v-model="newComment" type="textarea" :rows="3" placeholder="输入评论..." :disabled="isSubmittingComment" resize="none"/>
                              <el-button type="primary" @click="submitComment" :loading="isSubmittingComment" :disabled="!newComment.trim()" class="submit-comment-btn">发表</el-button>
                          </div>
                      </div>
                  </el-tab-pane>
                  <el-tab-pane label="活动日志" name="logs" lazy>
                     <!-- Activity Log: API call needs update -->
                      <div class="activity-logs thin-scrollbar" v-loading="loadingLogs">
                          <div v-if="taskLogs.length > 0">
                              <div v-for="log in taskLogs" :key="log.id" class="activity-item">
                                  <el-avatar :size="28" :src="getUserAvatar(log.userId)" class="activity-avatar" @error="onAvatarError">{{ getInitials(getUserName(log.userId)) }}</el-avatar>
                                  <div class="activity-content">
                                      <span class="activity-text">
                                          <span class="activity-user">{{ getUserName(log.userId) }}</span>
                                          {{ log.action }}
                                      </span>
                                      <span class="activity-time">{{ formatTimeAgo(log.createDate || log.time) }}</span>
                                  </div>
                              </div>
                          </div>
                           <el-empty v-else description="暂无活动记录"></el-empty>
                      </div>
                  </el-tab-pane>
              </el-tabs>
          </el-card>
        </el-col>
      </el-row>

    </div>
    <div v-else-if="!loading" class="task-not-found">
      <el-empty description="任务不存在或加载失败">
        <el-button @click="goBack">返回任务列表</el-button>
      </el-empty>
    </div>
  </div>
</template>

<script setup>
// Imports remain the same
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import {
  ArrowLeft, Delete, Edit, InfoFilled, Document, Clock, Paperclip, Upload, Download
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox, ElUpload } from 'element-plus'
import { taskApi } from '../../api/task.js'
import userApi from '../../api/user.js'
import { formatDistanceToNow } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import { getFullAvatarUrl } from '@/stores/modules/user'

const router = useRouter();
const route = useRoute();
const taskId = route.params.id;

const loading = ref(true);
const updatingStatus = ref(false);
const deleting = ref(false);
const task = ref(null);
const taskComments = ref([]);
const taskLogs = ref([]);
const newComment = ref('');
const isSubmittingComment = ref(false);
const loadingComments = ref(false);
const loadingLogs = ref(false);
const uploadRef = ref();
const fileList = ref([]);
const deletingAttachmentId = ref(null);
const activeTab = ref('comments');
const teamMembersList = ref([]);

const defaultAvatar = 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png';

const fileListDisplay = computed(() => fileList.value.map(f => ({name: f.name, size: f.size, uid: f.uid, status: f.status})));

// --- Data Fetching ---
async function fetchTaskDetails() {
  if (!taskId) {
    ElMessage.error('无效的任务ID');
    loading.value = false;
    return;
  }
  loading.value = true;
  try {
    // Fetch users first if needed
    if (teamMembersList.value.length === 0) {
      try {
        const userResponse = await userApi.getUserList({ pageSize: 1000 });
         if (userResponse && Array.isArray(userResponse.list)) {
            teamMembersList.value = userResponse.list;
        } else if (userResponse && Array.isArray(userResponse)) {
             teamMembersList.value = userResponse;
        } else {
             teamMembersList.value = [];
        }
      } catch (userError) {
        console.error('Error fetching team members:', userError);
        teamMembersList.value = []; // Ensure it's an array
      }
    }

    const response = await taskApi.getTaskDetail(taskId); // Use V2 API
    if (response && response.data) { // Check response.data for V2 structure
      task.value = processTaskData(response.data);
      fetchComments();
      fetchLogs();
    } else {
      task.value = null;
      ElMessage.error(response?.message || '获取任务详情失败');
    }
  } catch (error) {
    ElMessage.error('加载任务详情时出错');
    console.error('Error fetching task details (V2):', error);
    task.value = null;
  } finally {
    loading.value = false;
  }
}

function processTaskData(rawData) {
  const isOverdue = rawData.endDate && rawData.status !== 'completed' && new Date(rawData.endDate) < new Date();
  // const displayStatus = mapApiToDisplayStatus(rawData.status); // No longer needed for model
  return {
    ...rawData,
    isOverdue,
    // status: displayStatus, // Keep API status for model
    originalApiStatus: rawData.status, // Store original API status
    attachments: Array.isArray(rawData.attachments) ? rawData.attachments : []
  };
}

async function fetchComments() {
  if (!taskId) return;
  loadingComments.value = true;
  try {
    const response = await taskApi.getComments(taskId); // Use V2 API
    // Adapt to V2 response structure
    taskComments.value = response?.data || [];
  } catch (error) {
    console.error("Error fetching comments (V2):", error);
    taskComments.value = [];
  } finally {
    loadingComments.value = false;
  }
}

async function fetchLogs() {
  if (!taskId) return;
  loadingLogs.value = true;
  try {
    const response = await taskApi.getTaskActivityLog(taskId); // Use V2 API
    // Adapt to V2 response structure
    taskLogs.value = response?.data || [];
  } catch (error) {
    console.error("Error fetching logs (V2):", error);
    taskLogs.value = [];
  } finally {
    loadingLogs.value = false;
  }
}

// --- Actions ---
async function handleStatusChange(newApiStatus) {
  if (!task.value || newApiStatus === task.value.originalApiStatus) return;

  updatingStatus.value = true;
  try {
    await taskApi.updateTaskStatus(taskId, { status: newApiStatus }); // Use V2 API
    ElMessage.success('任务状态更新成功');
    task.value.originalApiStatus = newApiStatus; // Update stored original status
    // The v-model binding already updated task.value.status
    // Refresh derived states
    task.value.isOverdue = checkOverdue(task.value);

  } catch (error) {
    ElMessage.error('更新任务状态失败');
    task.value.status = task.value.originalApiStatus; // Revert on failure
    console.error('Status update error (V2):', error);
  } finally {
    updatingStatus.value = false;
  }
}

async function submitComment() {
  if (!newComment.value.trim() || !taskId) return;

  // 防重复提交检查
  if (isSubmittingComment.value) {
    ElMessage.warning('正在提交中，请稍候...');
    return;
  }

  isSubmittingComment.value = true;
  const commentContent = newComment.value.trim();

  try {
    await taskApi.addComment(taskId, { content: commentContent }); // Use V2 API
    await fetchComments(); // Re-fetch comments
    newComment.value = '';
    ElMessage.success('评论已发表');
  } catch (error) {
    console.error("Error submitting comment (V2):", error);
    ElMessage.error('发表评论失败');
  } finally {
    // 延迟重置提交状态，防止快速重复点击
    setTimeout(() => {
      isSubmittingComment.value = false;
    }, 1000);
  }
}

function confirmDeleteTask() {
   ElMessageBox.confirm(
    '确定要删除此任务吗？此操作不可恢复。',
    '确认删除任务',
    { confirmButtonText: '确认删除', cancelButtonText: '取消', type: 'warning' }
  ).then(async () => {
    deleting.value = true;
    try {
      await taskApi.deleteTask(taskId); // Use V2 API
      ElMessage.success('任务删除成功');
      router.push('/main/tasks/list');
    } catch (error) {
      console.error('Delete task error (V2):', error);
      ElMessage.error('删除任务失败');
    } finally {
      deleting.value = false;
    }
  }).catch(() => ElMessage.info('删除已取消'));
}

// --- Attachments ---
function beforeUpload(rawFile) {
  const isLt10M = rawFile.size / 1024 / 1024 < 10;
  if (!isLt10M) ElMessage.error('附件大小不能超过 10MB!');
  return isLt10M;
}
function handleExceed(files, uploadFiles) { ElMessage.warning(`限制上传 5 个文件，本次选择了 ${files.length} 个`); }
async function handleUpload(options) {
  const { file, onSuccess, onError, onProgress } = options;
  const formData = new FormData();
  formData.append('file', file);

  try {
    // Use taskApi.uploadTaskAttachment (V2)
    const response = await taskApi.uploadTaskAttachment(taskId, formData);
    // Check V2 response structure for success indication
    if (response && response.success) {
      ElMessage.success(`${file.name} 上传成功`);
      onSuccess(response.data); // Pass data if API returns attachment info
      fetchTaskDetails(); // Refresh to get the latest attachment list
    } else {
        throw new Error(response?.message || '上传失败');
    }
  } catch (error) {
    console.error('Upload error (V2):', error);
    ElMessage.error(`${file.name} 上传失败: ${error.message}`);
    onError(error);
  }
}
function handleRemove(file, uploadFiles) { console.log('Removed file from upload list:', file.name); }
function confirmDeleteAttachment(attachment) {
  if (!attachment?.id) return;
  ElMessageBox.confirm(`确定删除附件 "${attachment.name}"?`, '确认删除', { type: 'warning' })
  .then(async () => {
    deletingAttachmentId.value = attachment.id;
    try {
      await taskApi.deleteTaskAttachment(taskId, attachment.id); // Use V2 API
      ElMessage.success('附件删除成功');
      // Refresh task details
      fetchTaskDetails();
    } catch (error) {
      console.error('Delete attachment error (V2):', error);
      ElMessage.error('删除附件失败');
    } finally {
      deletingAttachmentId.value = null;
    }
  }).catch(() => ElMessage.info('删除已取消'));
}

// --- Helpers ---
function goBack() { router.go(-1); }
function getUserById(userId) { return teamMembersList.value.find(m => m.id === userId) || null; }
function getUserName(userId) { return getUserById(userId)?.name || '未知'; }
function getUserAvatar(userId) {
  const user = getUserById(userId)
  return getFullAvatarUrl(user?.avatar)
}
function getInitials(name) {
    if (!name || typeof name !== 'string') return '?';
    const names = name.split(' ');
    let initials = names[0].substring(0, 1).toUpperCase();
    if (names.length > 1) initials += names[names.length - 1].substring(0, 1).toUpperCase();
    return initials;
}
function onAvatarError(event) { event.target.style.display = 'none'; }
function priorityLabel(priority) { return { low: '低', medium: '中', high: '高' }[priority] || '中'; }
function getPriorityType(priority) { return { low: 'success', medium: 'warning', high: 'danger' }[priority] || 'info'; }
function formatDate(dateStr, includeTime = true) {
  if (!dateStr) return '';
  try {
    const date = new Date(dateStr);
    if (isNaN(date.getTime())) return dateStr;
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    if (includeTime) {
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      return `${year}-${month}-${day} ${hours}:${minutes}`;
    }
    return `${year}-${month}-${day}`;
  } catch (e) { return dateStr; }
}
function formatTimeAgo(dateStr) {
    if (!dateStr) return '';
    try {
        const date = new Date(dateStr);
        if (isNaN(date.getTime())) return '无效日期';
        return formatDistanceToNow(date, { addSuffix: true, locale: zhCN });
    } catch (e) { return '无效日期'; }
}
function formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    if (!bytes || isNaN(bytes) || bytes < 0) return 'N/A';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
}
function checkOverdue(taskData) {
  return taskData.endDate && taskData.status !== 'completed' && new Date(taskData.endDate) < new Date();
}

// --- Lifecycle ---
onMounted(() => {
  fetchTaskDetails();
});

</script>

<style scoped>
/* Styles remain largely the same, ensure consistency */
.page-container { padding: 20px; }
.mb-4 { margin-bottom: 20px; }
.page-header { display: flex; justify-content: space-between; align-items: center; }
.header-left { display: flex; align-items: center; gap: 15px; }
.back-button { color: var(--text-secondary-color); }
.back-button:hover { color: var(--primary-color); }
.page-title { font-size: 1.6rem; font-weight: 600; color: var(--header-bg); margin: 0; }
.header-right { display: flex; gap: 10px; }
.details-card, .attachments-card, .activity-card { border: 1px solid var(--border-color-light); background-color: var(--card-bg); border-radius: 8px; }
.card-header { font-weight: 600; color: var(--header-bg); font-size: 1.1rem; display: flex; align-items: center; gap: 8px; }
.info-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px 25px; }
.info-item { display: flex; flex-direction: column; gap: 4px; }
.item-label { font-size: 0.85rem; color: var(--text-secondary-color); }
.item-value { font-size: 0.95rem; color: var(--text-color); display: flex; align-items: center; gap: 6px; }
.item-value .el-tag, .item-value .el-select { font-size: 0.9rem; }
.item-value.assignee { gap: 8px; }
.item-value.overdue { color: var(--danger-color); font-weight: bold; display: inline-flex; align-items: center; gap: 4px; }
.item-value.overdue .el-icon { font-size: 1em; }
.small-avatar { width: 24px; height: 24px; font-size: 0.7rem; background-color: #eee; }
.description-section h4 { font-size: 1.05rem; font-weight: 600; color: var(--header-bg); margin-bottom: 10px; display: flex; align-items: center; gap: 6px; }
.description-content { font-size: 0.95rem; line-height: 1.7; color: var(--text-color); white-space: pre-wrap; }
.attachments-card .upload-area { margin-bottom: 15px; }
.attachments-list { margin-top: 15px; }
.attachment-item { display: flex; align-items: center; gap: 8px; padding: 8px 5px; border-bottom: 1px solid var(--border-color-light); font-size: 0.9rem; }
.attachment-item:last-child { border-bottom: none; }
.attachment-item .el-icon { color: var(--text-secondary-color); }
.attachment-name { flex-grow: 1; color: var(--text-color); }
.attachment-size { font-size: 0.8rem; color: var(--text-secondary-color); margin-left: 10px; }
.attachment-actions { margin-left: auto; }
.activity-card :deep(.el-tabs__header) { margin-bottom: 0; }
.activity-card :deep(.el-tabs__content) { padding: 0; height: calc(100vh - 350px); display: flex; flex-direction: column; }
.comment-section, .activity-logs { padding: 15px; flex-grow: 1; display: flex; flex-direction: column; overflow: hidden; }
.comments-list, .activity-logs { flex-grow: 1; overflow-y: auto; margin-bottom: 15px; }
.thin-scrollbar::-webkit-scrollbar { width: 5px; }
.thin-scrollbar::-webkit-scrollbar-track { background: transparent; }
.thin-scrollbar::-webkit-scrollbar-thumb { background-color: #ccc; border-radius: 3px; }
.thin-scrollbar::-webkit-scrollbar-thumb:hover { background-color: #aaa; }
.comment-item, .activity-item { display: flex; gap: 12px; margin-bottom: 18px; padding-bottom: 10px; border-bottom: 1px solid var(--border-color-light); }
.comment-item:last-child, .activity-item:last-child { margin-bottom: 0; border-bottom: none; }
.comment-content, .activity-content { flex-grow: 1; }
.comment-header, .activity-text { display: flex; justify-content: space-between; align-items: baseline; margin-bottom: 5px; flex-wrap: wrap; }
.comment-user, .activity-user { font-weight: 600; color: var(--text-color); font-size: 0.9rem; margin-right: 8px; }
.activity-text > span:not(.activity-user) { font-size: 0.9rem; color: var(--text-color); }
.comment-time, .activity-time { font-size: 0.75rem; color: var(--text-secondary-color); white-space: nowrap; }
.comment-text { font-size: 0.9rem; line-height: 1.6; color: var(--text-secondary-color); white-space: pre-wrap; }
.comment-form { margin-top: auto; padding-top: 15px; border-top: 1px solid var(--border-color-light); }
.submit-comment-btn { margin-top: 10px; float: right; }
.task-not-found { display: flex; justify-content: center; align-items: center; height: calc(100vh - 100px); }
/* Dark theme adjustments */
.dark-theme .back-button { color: var(--dark-text-secondary-color); }
.dark-theme .back-button:hover { color: var(--primary-color); }
.dark-theme .attachment-item { border-bottom-color: var(--dark-border-color); }
.dark-theme .comment-item, .dark-theme .activity-item { border-bottom-color: var(--dark-border-color); }
.dark-theme .comment-form { border-top-color: var(--dark-border-color); }
.dark-theme .small-avatar { background-color: #4a5568; }
</style>