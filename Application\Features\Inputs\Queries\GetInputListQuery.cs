// File: Application/Features/Inputs/Queries/GetInputListQuery.cs
// Description: 获取输入内容列表Query
using MediatR;
using ItAssetsSystem.Application.Common.Dtos;
using ItAssetsSystem.Application.Features.Inputs.Dtos;
using System.Collections.Generic;

namespace ItAssetsSystem.Application.Features.Inputs.Queries
{
    public class GetInputListQuery : IRequest<ApiResponse<List<InputRecordDto>>>
    {
    }
} 