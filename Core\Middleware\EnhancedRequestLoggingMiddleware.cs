// IT资产管理系统 - 增强请求日志中间件
// 文件路径: /Core/Middleware/EnhancedRequestLoggingMiddleware.cs
// 功能: 记录详细的HTTP请求和响应信息，包括路径、方法、状态码、处理时间等

using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using System;
using System.Diagnostics;
using System.IO;
using System.Text;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http.Extensions;
using Microsoft.AspNetCore.Routing;
using System.Linq;
using System.Reflection;

namespace ItAssetsSystem.Core.Middleware
{
    /// <summary>
    /// 增强的请求日志中间件，记录详细的请求和响应信息
    /// </summary>
    public class EnhancedRequestLoggingMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<EnhancedRequestLoggingMiddleware> _logger;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="next">下一个中间件</param>
        /// <param name="logger">日志记录器</param>
        public EnhancedRequestLoggingMiddleware(
            RequestDelegate next,
            ILogger<EnhancedRequestLoggingMiddleware> logger)
        {
            _next = next;
            _logger = logger;
        }

        /// <summary>
        /// 处理HTTP请求
        /// </summary>
        /// <param name="context">HTTP上下文</param>
        /// <returns>任务</returns>
        public async Task InvokeAsync(HttpContext context)
        {
            // 启动计时器
            var stopwatch = Stopwatch.StartNew();
            
            // 记录请求详情
            var requestMethod = context.Request.Method;
            var requestPath = context.Request.Path;
            var requestUrl = context.Request.GetDisplayUrl();
            var remoteIp = context.Connection.RemoteIpAddress?.ToString() ?? "unknown";
            var userAgent = context.Request.Headers["User-Agent"].ToString();
            var referer = context.Request.Headers["Referer"].ToString();
            var contentType = context.Request.ContentType;
            
            // 尝试获取处理请求的控制器和操作
            var endpoint = context.GetEndpoint();
            string controllerName = "未知";
            string actionName = "未知";
            string handlerMethod = "未知";
            
            if (endpoint != null)
            {
                var routeData = endpoint.Metadata.GetMetadata<RouteData>();
                controllerName = routeData?.Values.TryGetValue("controller", out var controller) == true 
                    ? controller?.ToString() : "未知";
                actionName = routeData?.Values.TryGetValue("action", out var action) == true 
                    ? action?.ToString() : "未知";
                
                // 尝试获取处理方法的完整信息
                var methodInfo = endpoint.Metadata
                    .OfType<MethodInfo>()
                    .FirstOrDefault();
                    
                if (methodInfo != null)
                {
                    handlerMethod = $"{methodInfo.DeclaringType?.FullName}.{methodInfo.Name}";
                }
            }
            
            // 读取请求体
            string requestBody = string.Empty;
            if (context.Request.ContentLength > 0 && context.Request.ContentType != null && 
                (context.Request.ContentType.Contains("application/json") || 
                 context.Request.ContentType.Contains("application/xml") ||
                 context.Request.ContentType.Contains("text/plain") ||
                 context.Request.ContentType.Contains("application/x-www-form-urlencoded")))
            {
                // 启用请求体重复读取
                context.Request.EnableBuffering();
                
                // 读取请求体
                using (var reader = new StreamReader(
                    context.Request.Body,
                    encoding: Encoding.UTF8,
                    detectEncodingFromByteOrderMarks: false,
                    leaveOpen: true))
                {
                    requestBody = await reader.ReadToEndAsync();
                    
                    // 重置请求体位置，以便后续中间件可以读取
                    context.Request.Body.Position = 0;
                }
            }
            
            // 替换原始响应体流，以便捕获响应内容
            var originalBodyStream = context.Response.Body;
            using var responseBodyStream = new MemoryStream();
            context.Response.Body = responseBodyStream;
            
            try
            {
                // 记录请求开始日志
                _logger.LogInformation(
                    "开始处理请求: {RequestMethod} {RequestPath}\n" +
                    "完整URL: {RequestUrl}\n" +
                    "远程IP: {RemoteIp}\n" +
                    "用户代理: {UserAgent}\n" +
                    "引用页: {Referer}\n" +
                    "内容类型: {ContentType}\n" +
                    "路由到: 控制器={ControllerName}, 操作={ActionName}\n" +
                    "处理方法: {HandlerMethod}\n" +
                    "请求体: {RequestBody}",
                    requestMethod, requestPath, requestUrl, remoteIp, userAgent, 
                    referer, contentType, controllerName, actionName, handlerMethod,
                    string.IsNullOrEmpty(requestBody) ? "(空)" : requestBody);
                
                // 调用下一个中间件
                await _next(context);
                
                // 处理响应内容
                responseBodyStream.Seek(0, SeekOrigin.Begin);
                string responseBody = await new StreamReader(responseBodyStream).ReadToEndAsync();
                responseBodyStream.Seek(0, SeekOrigin.Begin);
                
                // 将响应体复制回原始流
                await responseBodyStream.CopyToAsync(originalBodyStream);
                
                // 获取响应信息
                var statusCode = context.Response.StatusCode;
                var responseContentType = context.Response.ContentType;
                
                // 记录响应日志
                var contentToLog = responseBody;
                
                // 如果响应体太长，只记录摘要
                if (contentToLog.Length > 4000)
                {
                    contentToLog = contentToLog.Substring(0, 4000) + "... [内容被截断]";
                }
                
                stopwatch.Stop();
                var elapsedMs = stopwatch.ElapsedMilliseconds;
                
                _logger.LogInformation(
                    "请求处理完成: {RequestMethod} {RequestPath} => {StatusCode} ({ElapsedMs}ms)\n" +
                    "响应内容类型: {ResponseContentType}\n" +
                    "响应体: {ResponseBody}",
                    requestMethod, requestPath, statusCode, elapsedMs, 
                    responseContentType, contentToLog);
            }
            catch (Exception ex)
            {
                // 记录异常
                stopwatch.Stop();
                var elapsedMs = stopwatch.ElapsedMilliseconds;
                
                _logger.LogError(
                    ex,
                    "请求处理异常: {RequestMethod} {RequestPath} ({ElapsedMs}ms)\n" +
                    "异常消息: {ExceptionMessage}\n" +
                    "堆栈跟踪: {StackTrace}",
                    requestMethod, requestPath, elapsedMs, 
                    ex.Message, ex.StackTrace);
                
                // 将异常重新抛出，让错误处理中间件处理
                throw;
            }
            finally
            {
                // 恢复原始响应体流
                context.Response.Body = originalBodyStream;
            }
        }
    }
} 