/**
 * IT资产管理系统 - 样式变量
 * 文件路径: src/styles/variables.scss
 * 功能描述: 定义全局样式变量，用于保持样式一致性
 */

/* --- New Theme Variables --- */
:root {
  /* Core Palette: Blue & Orange Accent */
  --primary-color: #2196F3;          /* Blue 500 */
  --primary-color-dark: #1976D2;     /* Blue 700 */
  --primary-color-light: #BBDEFB;    /* Blue 100 */
  --primary-color-bg-light: #E3F2FD; /* Blue 50 */
  --accent-color: #F57C00;           /* Orange 700 */
  --accent-color-light: #FFE0B2;     /* Orange 100 */

  /* Status Colors (Adjusted slightly for consistency) */
  --success-color: #4CAF50;         /* Green 500 */
  --warning-color: #FFB300;         /* Amber 700 */
  --danger-color: #E53935;          /* Red 600 */
  --info-color: #546E7A;            /* Blue Grey 600 */

  /* Backgrounds - Light Theme */
  --bg-color: #F5F7FA;              /* Cooler light gray */
  --header-bg: var(--primary-color-dark); /* Dark Blue Header (now uses Blue 700) */
  --sidebar-bg: #FFFFFF;            /* White Sidebar */
  --card-bg: #FFFFFF;               /* White Cards */
  --column-bg: #F8F9FC;            /* Light gray for Kanban columns, etc. */
  --hover-bg-color: var(--primary-color-bg-light); /* Light Blue for primary hovers (now uses Blue 50) */
  --drag-over-bg: var(--primary-color-light); /* Light Blue for drag over (now uses Blue 100) */
  --bg-color-overlay: rgba(38, 50, 56, 0.6); /* Darker overlay based on Blue Grey */

  /* Text Colors - Light Theme */
  --text-color: #263238;            /* Blue Grey 900 - Dark Gray */
  --text-color-secondary: #546E7A;  /* Blue Grey 600 */
  --text-placeholder-color: #90A4AE;/* Blue Grey 400 */
  --text-color-on-primary: #FFFFFF; /* Text on dark teal header/buttons */
  --text-color-on-accent: #FFFFFF;  /* Text on orange accent */
  --text-color-disabled: #B0BEC5;    /* Blue Grey 200 */

  /* Borders - Light Theme */
  --border-color: #CFD8DC;          /* Blue Grey 100 */
  --border-color-light: #ECEFF1;    /* Blue Grey 50 */

  /* Element Plus Compatibility (Override defaults) */
  --el-color-primary: var(--primary-color);
  --el-color-success: var(--success-color);
  --el-color-warning: var(--warning-color);
  --el-color-danger: var(--danger-color);
  --el-color-info: var(--info-color);
  /* You might need to override more specific Element Plus vars (--el-color-primary-light-X etc.) if components rely heavily on them. */
  --el-text-color-primary: var(--text-color);
  --el-text-color-regular: var(--text-color-secondary);
  --el-text-color-secondary: var(--text-color-secondary);
  --el-text-color-placeholder: var(--text-placeholder-color);
  --el-border-color: var(--border-color);
  --el-border-color-light: var(--border-color-light);
  --el-border-color-lighter: #F5F5F5; /* Keep very light for subtle borders */
  --el-border-color-extra-light: var(--bg-color); /* Use page bg for lightest */
  --el-bg-color: var(--card-bg);
  --el-bg-color-page: var(--bg-color);
  --el-bg-color-overlay: var(--bg-color-overlay);


  /* Gamification Colors */
  --color-gold: #FFC107;            /* Amber 500 */
  --color-silver: #B0BEC5;          /* Blue Grey 200 */
  --color-bronze: #A1887F;          /* Brown 300 */
  --color-xp: #00BCD4;              /* Cyan 500 */
  --color-coin: var(--color-gold);
  --color-level-up: #EC407A;        /* Pink 400 */
  --color-achievement: var(--accent-color); /* Use accent color for achievements */

  /* Layout */
  --header-height: 60px;
  --sidebar-width: 220px;
  --sidebar-collapsed-width: 64px;
  --content-padding: 20px;
  --footer-height: 40px;

  /* Fonts & Typography */
  --font-family-base: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '\5FAE\8F6F\96C5\9ED1', Arial, sans-serif;
  --font-size-base: 14px;
  --line-height-base: 1.6;

  /* Borders & Shadows */
  --border-radius-base: 6px;        /* Slightly more rounded */
  --border-radius-small: 3px;
  --border-radius-large: 10px;
  --border-radius-circle: 50%;
  --shadow-light: 0 1px 3px rgba(0, 0, 0, 0.08), 0 1px 2px rgba(0, 0, 0, 0.05); /* Subtle */
  --shadow-medium: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); /* Standard */
  --shadow-large: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); /* More prominent */
  --box-shadow-base: var(--shadow-medium); /* Map old variable */

  /* Transitions */
  --transition-duration: 0.25s;
  --transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  --transition: all var(--transition-duration) var(--transition-timing-function);

  /* Z-index Layers */
  --z-index-normal: 1;
  --z-index-sticky: 100;
  --z-index-sidebar: 900;
  --z-index-header: 950;
  --z-index-dropdown: 1000;
  --z-index-modal-backdrop: 1040;
  --z-index-modal: 1050;
  --z-index-popover: 1060;
  --z-index-tooltip: 1070;
  --z-index-notification: 1100;
}

/* --- Dark Theme --- */
.dark-theme {
  --primary-color: #64B5F6;          /* Blue 300 - Lighter primary for dark */
  --primary-color-dark: #2196F3;     /* Blue 500 */
  --primary-color-light: #90CAF9;    /* Blue 200 */
  --primary-color-bg-light: #42A5F5; /* Blue 400 - for subtle bg highlights */
  --accent-color: #FFB74D;           /* Orange 300 - Lighter accent */

  --success-color: #81C784;         /* Green 300 */
  --warning-color: #FFD54F;         /* Amber 300 */
  --danger-color: #E57373;          /* Red 300 */
  --info-color: #90A4AE;            /* Blue Grey 400 */

  /* Backgrounds - Dark Theme */
  --bg-color: #263238;              /* Blue Grey 900 */
  --header-bg: #1f2931;             /* Slightly darker than main bg */
  --sidebar-bg: #37474F;            /* Blue Grey 800 */
  --card-bg: var(--sidebar-bg);
  --column-bg: var(--sidebar-bg);
  --hover-bg-color: #37474F;        /* Blue Grey 800 - Adjust hover if needed, keep dark */
  --drag-over-bg: #42A5F5;        /* Use the lighter bg highlight for drag over */
  --bg-color-overlay: rgba(0, 0, 0, 0.7);

  /* Text Colors - Dark Theme */
  --text-color: #ECEFF1;            /* Blue Grey 50 */
  --text-color-secondary: #B0BEC5;  /* Blue Grey 200 */
  --text-placeholder-color: #78909C;/* Blue Grey 400 */
  --text-color-on-primary: #FFFFFF; /* Changed from #000000 to #FFFFFF for better contrast on Teal 300 */
  --text-color-on-accent: #000000;  /* Text on light orange accent - Keep black for now */
  --text-color-disabled: #78909C;    /* Blue Grey 400 */

  /* Borders - Dark Theme */
  --border-color: #455A64;          /* Blue Grey 700 */
  --border-color-light: #546E7A;    /* Blue Grey 600 */

   /* Element Plus Dark Overrides */
  --el-color-primary: var(--primary-color);
  --el-color-success: var(--success-color);
  --el-color-warning: var(--warning-color);
  --el-color-danger: var(--danger-color);
  --el-color-info: var(--info-color);
  --el-text-color-primary: var(--text-color);
  --el-text-color-regular: var(--text-color-secondary);
  --el-text-color-secondary: var(--text-color-secondary);
  --el-text-color-placeholder: var(--text-placeholder-color);
  --el-border-color: var(--border-color);
  --el-border-color-light: var(--border-color-light);
  --el-border-color-lighter: #455A64; /* Blue Grey 700 */
  --el-border-color-extra-light: #37474F; /* Blue Grey 800 */
  --el-bg-color: var(--card-bg);
  --el-bg-color-page: var(--bg-color);
  --el-bg-color-overlay: var(--bg-color-overlay);
  --el-fill-color-blank: var(--bg-color); /* Ensure empty states match bg */
}

/* --- SCSS Variables (Keep for mixins/functions if needed, prefer CSS vars) --- */

// Keep necessary SCSS variables that CSS variables can't easily replace (e.g., for mixin logic)
$font-size-small: 12px;
$font-size-large: 16px;
$font-size-xlarge: 20px;

$spacing-mini: 4px;
$spacing-medium: 16px;
$spacing-xlarge: 32px;
$spacing-extra-large: 24px;

$z-index-popup: 1500;
$z-index-message: 2500;

// Responsive Breakpoints (Keep)
$screen-xs: 480px;
$screen-sm: 768px;
$screen-md: 992px;
$screen-lg: 1200px;
$screen-xl: 1600px;

// Text Colors (SASS variables for backward compatibility)
$text-primary: #303133;
$text-secondary: #909399;