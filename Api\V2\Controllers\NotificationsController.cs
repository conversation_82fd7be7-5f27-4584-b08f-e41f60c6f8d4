using Microsoft.AspNetCore.Mvc;
using ItAssetsSystem.Core.Abstractions;
using ItAssetsSystem.Application.Common.Dtos;
using System.Threading.Tasks;
using System;
using System.Linq;

namespace ItAssetsSystem.Api.V2.Controllers
{
    [ApiController]
    [Route("api/v2/[controller]")]
    public class NotificationsController : ControllerBase
    {
        private readonly INotificationService _notificationService;
        private readonly ICurrentUserService _currentUserService;

        public NotificationsController(
            INotificationService notificationService,
            ICurrentUserService currentUserService)
        {
            _notificationService = notificationService;
            _currentUserService = currentUserService;
        }

        /// <summary>
        /// 获取当前用户的通知列表
        /// </summary>
        [HttpGet]
        public async Task<ActionResult<ApiResponse<object>>> GetNotifications(
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 20,
            [FromQuery] bool? isRead = null)
        {
            try
            {
                var currentUserId = _currentUserService.UserId;
                Console.WriteLine($"=== 通知API调试 ===");
                Console.WriteLine($"原始用户ID: {currentUserId}");
                Console.WriteLine($"用户是否认证: {_currentUserService.IsAuthenticated}");

                // 临时解决方案：如果用户ID为0，使用默认用户ID 1 进行测试
                if (currentUserId == 0)
                {
                    Console.WriteLine("用户ID为0，使用默认用户ID 1 进行测试");
                    currentUserId = 1; // 临时使用用户ID 1
                    // return ApiResponse<object>.Error("用户未登录");
                }

                var notifications = await _notificationService.GetUserNotificationsAsync(
                    currentUserId, page, pageSize, isRead);

                var unreadCount = await _notificationService.GetUnreadCountAsync(currentUserId);
                var totalCount = await _notificationService.GetTotalCountAsync(currentUserId);

                // 添加调试日志
                Console.WriteLine($"用户 {currentUserId} 的通知查询结果:");
                Console.WriteLine($"- 通知数量: {notifications.Count}");
                Console.WriteLine($"- 未读数量: {unreadCount}");
                Console.WriteLine($"- 总数量: {totalCount}");
                Console.WriteLine($"- 分页参数: page={page}, pageSize={pageSize}");

                return Ok(ApiResponse<object>.CreateSuccess(new
                {
                    notifications = notifications.Select(n => new
                    {
                        id = n.NotificationId,
                        title = n.Title,
                        content = n.Content,
                        type = n.Type,
                        resourceType = n.ReferenceType, // 修复字段映射
                        resourceId = n.ReferenceId,     // 修复字段映射
                        referenceType = n.ReferenceType, // 添加前端期望的字段
                        referenceId = n.ReferenceId,     // 添加前端期望的字段
                        isRead = n.IsRead,
                        priority = "Normal", // 默认优先级，因为数据库中没有此字段
                        createdAt = n.CreationTimestamp, // 修复字段映射
                        creationTimestamp = n.CreationTimestamp, // 添加前端期望的字段
                        readAt = n.ReadTimestamp,        // 修复字段映射
                        timestamp = n.CreationTimestamp, // 添加前端期望的字段
                        extraData = (string)null         // 默认为null，因为数据库中没有此字段
                    }),
                    pagination = new
                    {
                        page,
                        pageSize,
                        unreadCount,
                        total = totalCount
                    }
                }));
            }
            catch (Exception ex)
            {
                return Ok(ApiResponse<object>.CreateFail($"获取通知失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 获取未读通知数量
        /// </summary>
        [HttpGet("unread-count")]
        public async Task<ActionResult<ApiResponse<int>>> GetUnreadCount()
        {
            try
            {
                var currentUserId = _currentUserService.UserId;
                if (currentUserId == 0)
                {
                    return Ok(ApiResponse<int>.CreateFail("用户未登录"));
                }

                var count = await _notificationService.GetUnreadCountAsync(currentUserId);
                return Ok(ApiResponse<int>.CreateSuccess(count));
            }
            catch (Exception ex)
            {
                return Ok(ApiResponse<int>.CreateFail($"获取未读数量失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 标记通知为已读
        /// </summary>
        [HttpPut("{id}/read")]
        public async Task<ActionResult<ApiResponse<object>>> MarkAsRead(long id)
        {
            try
            {
                var currentUserId = _currentUserService.UserId;
                if (currentUserId == 0)
                {
                    return Ok(ApiResponse<object>.CreateFail("用户未登录"));
                }

                await _notificationService.MarkAsReadAsync(id, currentUserId);
                return Ok(ApiResponse<object>.CreateSuccess(null, "标记成功"));
            }
            catch (Exception ex)
            {
                return Ok(ApiResponse<object>.CreateFail($"标记失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 标记所有通知为已读
        /// </summary>
        [HttpPut("read-all")]
        public async Task<ActionResult<ApiResponse<object>>> MarkAllAsRead()
        {
            try
            {
                var currentUserId = _currentUserService.UserId;
                if (currentUserId == 0)
                {
                    return Ok(ApiResponse<object>.CreateFail("用户未登录"));
                }

                await _notificationService.MarkAllAsReadAsync(currentUserId);
                return Ok(ApiResponse<object>.CreateSuccess(null, "全部标记成功"));
            }
            catch (Exception ex)
            {
                return Ok(ApiResponse<object>.CreateFail($"标记失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 删除通知
        /// </summary>
        [HttpDelete("{id}")]
        public async Task<ActionResult<ApiResponse<object>>> DeleteNotification(long id)
        {
            try
            {
                var currentUserId = _currentUserService.UserId;
                if (currentUserId == 0)
                {
                    return Ok(ApiResponse<object>.CreateFail("用户未登录"));
                }

                await _notificationService.DeleteNotificationAsync(id, currentUserId);
                return Ok(ApiResponse<object>.CreateSuccess(null, "删除成功"));
            }
            catch (Exception ex)
            {
                return Ok(ApiResponse<object>.CreateFail($"删除失败: {ex.Message}"));
            }
        }
    }
}