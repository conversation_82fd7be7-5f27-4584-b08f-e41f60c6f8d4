import{_ as e,r as a,z as l,b as t,e as o,w as r,E as u,a as s,m as i,o as d,l as n,k as c,p,t as m,f as v,b1 as f,aJ as g,d as w,a9 as b}from"./index-CG5lHOPO.js";import{u as h}from"./spareparts-CUOQzjwG.js";import"./spareparts-DKUrs8IX.js";const y={class:"spare-part-location-view"},_={class:"card-header"},V={class:"dialog-footer"},k=e({__name:"SparePartLocationView",setup(e){const k=h(),x=a(!1),C=a(!1),U=a(!1),L=a([]),q=a(null),j=a({code:"",name:"",area:"",status:"normal",description:""}),z={code:[{required:!0,message:"请输入库位编码",trigger:"blur"},{min:1,max:20,message:"长度在 1 到 20 个字符",trigger:"blur"}],name:[{required:!0,message:"请输入库位名称",trigger:"blur"},{min:1,max:50,message:"长度在 1 到 50 个字符",trigger:"blur"}],area:[{required:!0,message:"请输入所属区域",trigger:"blur"}],status:[{required:!0,message:"请选择库位状态",trigger:"change"}]},B=async()=>{try{x.value=!0,L.value=await k.fetchLocations()||[]}catch(e){u.error("获取库位数据失败")}finally{x.value=!1}},T=()=>{U.value=!1,C.value=!0},E=async()=>{if(q.value)try{if(await q.value.validate(),x.value=!0,U.value){const{id:e,...a}=j.value;await k.updateLocation(e,a)&&(u.success("更新成功"),C.value=!1,await B())}else{await k.createLocation(j.value)&&(u.success("创建成功"),C.value=!1,await B())}}catch(e){e.message&&u.error(e.message)}finally{x.value=!1}},F=()=>{j.value={code:"",name:"",area:"",status:"normal",description:""},q.value&&q.value.resetFields()};return l((async()=>{await B()})),(e,a)=>{const l=s("el-button"),h=s("el-table-column"),I=s("el-tag"),J=s("el-table"),P=s("el-card"),R=s("el-input"),S=s("el-form-item"),$=s("el-option"),A=s("el-select"),D=s("el-form"),G=s("el-dialog"),H=i("loading");return d(),t("div",y,[o(P,{shadow:"hover"},{header:r((()=>[w("div",_,[a[8]||(a[8]=w("span",null,"备件库位管理",-1)),o(l,{type:"primary",onClick:T},{default:r((()=>a[7]||(a[7]=[p(" 新增库位 ")]))),_:1})])])),default:r((()=>[n((d(),c(J,{data:L.value,border:"",style:{width:"100%"},"row-key":"id"},{default:r((()=>[o(h,{prop:"code",label:"库位编码",width:"150"}),o(h,{prop:"name",label:"库位名称",width:"200"}),o(h,{prop:"area",label:"区域",width:"150"}),o(h,{prop:"status",label:"状态",width:"100"},{default:r((e=>[o(I,{type:"normal"===e.row.status?"success":"warning"},{default:r((()=>{return[p(m((a=e.row.status,{normal:"正常",maintenance:"维护中",disabled:"已停用"}[a]||a)),1)];var a})),_:2},1032,["type"])])),_:1}),o(h,{prop:"description",label:"描述"}),o(h,{label:"操作",width:"200",align:"center"},{default:r((e=>[o(l,{size:"small",type:"primary",onClick:a=>{return l=e.row,U.value=!0,j.value={...l},void(C.value=!0);var l},icon:v(f),circle:""},null,8,["onClick","icon"]),o(l,{size:"small",type:"danger",onClick:a=>{return l=e.row,void b.confirm(`确定要删除库位"${l.name}"吗？`,"警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{try{x.value=!0,await k.deleteLocation(l.id)&&(u.success("删除成功"),await B())}catch(e){u.error("删除库位失败")}finally{x.value=!1}})).catch((()=>{}));var l},icon:v(g),circle:""},null,8,["onClick","icon"])])),_:1})])),_:1},8,["data"])),[[H,x.value]])])),_:1}),o(G,{modelValue:C.value,"onUpdate:modelValue":a[6]||(a[6]=e=>C.value=e),title:U.value?"编辑库位":"新增库位",width:"500px","close-on-click-modal":!1,onClosed:F},{footer:r((()=>[w("div",V,[o(l,{onClick:a[5]||(a[5]=e=>C.value=!1)},{default:r((()=>a[9]||(a[9]=[p("取消")]))),_:1}),o(l,{type:"primary",onClick:E,loading:x.value},{default:r((()=>a[10]||(a[10]=[p("确定")]))),_:1},8,["loading"])])])),default:r((()=>[o(D,{ref_key:"formRef",ref:q,model:j.value,rules:z,"label-width":"100px","label-position":"right","status-icon":""},{default:r((()=>[o(S,{label:"库位编码",prop:"code"},{default:r((()=>[o(R,{modelValue:j.value.code,"onUpdate:modelValue":a[0]||(a[0]=e=>j.value.code=e),placeholder:"请输入库位编码"},null,8,["modelValue"])])),_:1}),o(S,{label:"库位名称",prop:"name"},{default:r((()=>[o(R,{modelValue:j.value.name,"onUpdate:modelValue":a[1]||(a[1]=e=>j.value.name=e),placeholder:"请输入库位名称"},null,8,["modelValue"])])),_:1}),o(S,{label:"所属区域",prop:"area"},{default:r((()=>[o(R,{modelValue:j.value.area,"onUpdate:modelValue":a[2]||(a[2]=e=>j.value.area=e),placeholder:"请输入所属区域"},null,8,["modelValue"])])),_:1}),o(S,{label:"状态",prop:"status"},{default:r((()=>[o(A,{modelValue:j.value.status,"onUpdate:modelValue":a[3]||(a[3]=e=>j.value.status=e),placeholder:"请选择库位状态",style:{width:"100%"}},{default:r((()=>[o($,{label:"正常",value:"normal"}),o($,{label:"维护中",value:"maintenance"}),o($,{label:"已停用",value:"disabled"})])),_:1},8,["modelValue"])])),_:1}),o(S,{label:"描述",prop:"description"},{default:r((()=>[o(R,{modelValue:j.value.description,"onUpdate:modelValue":a[4]||(a[4]=e=>j.value.description=e),placeholder:"请输入描述信息",type:"textarea",rows:3},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue","title"])])}}},[["__scopeId","data-v-6e51669f"]]);export{k as default};
