// File: Application/Features/Gamification/EventHandlers/GamificationEventHandlers.cs
// Description: 游戏化事件处理器

using System;
using System.Threading;
using System.Threading.Tasks;
using MediatR;
using Microsoft.Extensions.Logging;
using ItAssetsSystem.Application.Features.Gamification.Events;
using ItAssetsSystem.Application.Features.Gamification.Services;
using ItAssetsSystem.Core.Events.Tasks;

namespace ItAssetsSystem.Application.Features.Gamification.EventHandlers
{
    /// <summary>
    /// 任务创建游戏化事件处理器
    /// </summary>
    public class TaskCreatedGamificationHandler : INotificationHandler<TaskCreatedEvent>
    {
        private readonly IGamificationRuleEngine _ruleEngine;
        private readonly IGamificationRewardProcessor _rewardProcessor;
        private readonly ILogger<TaskCreatedGamificationHandler> _logger;

        public TaskCreatedGamificationHandler(
            IGamificationRuleEngine ruleEngine,
            IGamificationRewardProcessor rewardProcessor,
            ILogger<TaskCreatedGamificationHandler> logger)
        {
            _ruleEngine = ruleEngine;
            _rewardProcessor = rewardProcessor;
            _logger = logger;
        }

        public async Task Handle(TaskCreatedEvent notification, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("处理任务创建游戏化奖励: TaskId={TaskId}, CreatorUserId={CreatorUserId}", 
                    notification.TaskId, notification.CreatorUserId);

                // 创建游戏化奖励事件
                var rewardEvent = new TaskCreatedRewardEvent(
                    notification.CreatorUserId,
                    notification.TaskId,
                    notification.TaskName,
                    notification.TaskType,
                    notification.Points
                );

                // 计算奖励
                var reward = await _ruleEngine.CalculateRewardAsync(rewardEvent);

                if (reward.HasReward)
                {
                    // 发放奖励
                    var success = await _rewardProcessor.ProcessRewardAsync(
                        notification.CreatorUserId,
                        reward,
                        "TaskCreated",
                        notification.TaskId
                    );

                    if (success)
                    {
                        _logger.LogInformation("任务创建奖励发放成功: UserId={UserId}, TaskId={TaskId}, Points={Points}, Coins={Coins}", 
                            notification.CreatorUserId, notification.TaskId, reward.Points, reward.Coins);
                    }
                    else
                    {
                        _logger.LogWarning("任务创建奖励发放失败: UserId={UserId}, TaskId={TaskId}", 
                            notification.CreatorUserId, notification.TaskId);
                    }
                }
                else
                {
                    _logger.LogDebug("任务创建无奖励: UserId={UserId}, TaskId={TaskId}", 
                        notification.CreatorUserId, notification.TaskId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理任务创建游戏化奖励时发生错误: TaskId={TaskId}", notification.TaskId);
            }
        }
    }

    /// <summary>
    /// 任务完成游戏化事件处理器
    /// </summary>
    public class TaskCompletedGamificationHandler : INotificationHandler<TaskCompletedEvent>
    {
        private readonly IGamificationRuleEngine _ruleEngine;
        private readonly IGamificationRewardProcessor _rewardProcessor;
        private readonly IGamificationStatsUpdater _statsUpdater;
        private readonly ILogger<TaskCompletedGamificationHandler> _logger;

        public TaskCompletedGamificationHandler(
            IGamificationRuleEngine ruleEngine,
            IGamificationRewardProcessor rewardProcessor,
            IGamificationStatsUpdater statsUpdater,
            ILogger<TaskCompletedGamificationHandler> logger)
        {
            _ruleEngine = ruleEngine;
            _rewardProcessor = rewardProcessor;
            _statsUpdater = statsUpdater;
            _logger = logger;
        }

        public async Task Handle(TaskCompletedEvent notification, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("处理任务完成游戏化奖励: TaskId={TaskId}, CompleterUserId={CompleterUserId}", 
                    notification.TaskId, notification.CompleterUserId);

                // 判断是否按时完成
                var isOnTime = notification.CompletedTimestamp <= DateTime.UtcNow; // 简化逻辑，实际应该比较计划完成时间

                // 创建游戏化奖励事件
                var rewardEvent = new TaskCompletedRewardEvent(
                    notification.CompleterUserId,
                    notification.TaskId,
                    notification.TaskName,
                    notification.CoreTaskType.ToString(),
                    isOnTime,
                    notification.Points,
                    notification.CompletedTimestamp
                );

                // 计算奖励
                var reward = await _ruleEngine.CalculateRewardAsync(rewardEvent);

                if (reward.HasReward)
                {
                    // 发放奖励
                    var success = await _rewardProcessor.ProcessRewardAsync(
                        notification.CompleterUserId,
                        reward,
                        "TaskCompleted",
                        notification.TaskId
                    );

                    if (success)
                    {
                        _logger.LogInformation("任务完成奖励发放成功: UserId={UserId}, TaskId={TaskId}, Points={Points}, Coins={Coins}, IsOnTime={IsOnTime}", 
                            notification.CompleterUserId, notification.TaskId, reward.Points, reward.Coins, isOnTime);

                        // 检查等级提升
                        await _statsUpdater.CheckAndProcessLevelUpAsync(notification.CompleterUserId);

                        // 检查成就解锁
                        await _statsUpdater.CheckAndProcessAchievementsAsync(notification.CompleterUserId, "TaskCompleted");
                    }
                    else
                    {
                        _logger.LogWarning("任务完成奖励发放失败: UserId={UserId}, TaskId={TaskId}", 
                            notification.CompleterUserId, notification.TaskId);
                    }
                }
                else
                {
                    _logger.LogDebug("任务完成无奖励: UserId={UserId}, TaskId={TaskId}", 
                        notification.CompleterUserId, notification.TaskId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理任务完成游戏化奖励时发生错误: TaskId={TaskId}", notification.TaskId);
            }
        }
    }

    /// <summary>
    /// 任务删除游戏化事件处理器
    /// </summary>
    public class TaskDeletedGamificationHandler : INotificationHandler<TaskDeletedEvent>
    {
        private readonly IGamificationRewardProcessor _rewardProcessor;
        private readonly ILogger<TaskDeletedGamificationHandler> _logger;

        public TaskDeletedGamificationHandler(
            IGamificationRewardProcessor rewardProcessor,
            ILogger<TaskDeletedGamificationHandler> logger)
        {
            _rewardProcessor = rewardProcessor;
            _logger = logger;
        }

        public async Task Handle(TaskDeletedEvent notification, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("处理任务删除游戏化扣分: TaskId={TaskId}, DeleterUserId={DeleterUserId}", 
                    notification.TaskId, notification.DeleterUserId);

                // 撤销任务创建奖励
                var revokeSuccess = await _rewardProcessor.RevokeRewardAsync(
                    notification.DeleterUserId,
                    "TaskCreated",
                    notification.TaskId
                );

                if (revokeSuccess)
                {
                    _logger.LogInformation("任务删除奖励撤销成功: UserId={UserId}, TaskId={TaskId}", 
                        notification.DeleterUserId, notification.TaskId);
                }
                else
                {
                    _logger.LogWarning("任务删除奖励撤销失败: UserId={UserId}, TaskId={TaskId}", 
                        notification.DeleterUserId, notification.TaskId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理任务删除游戏化扣分时发生错误: TaskId={TaskId}", notification.TaskId);
            }
        }
    }

    /// <summary>
    /// 任务认领游戏化事件处理器
    /// </summary>
    public class TaskClaimedGamificationHandler : INotificationHandler<INotification>
    {
        private readonly IGamificationRuleEngine _ruleEngine;
        private readonly IGamificationRewardProcessor _rewardProcessor;
        private readonly ILogger<TaskClaimedGamificationHandler> _logger;

        public TaskClaimedGamificationHandler(
            IGamificationRuleEngine ruleEngine,
            IGamificationRewardProcessor rewardProcessor,
            ILogger<TaskClaimedGamificationHandler> logger)
        {
            _ruleEngine = ruleEngine;
            _rewardProcessor = rewardProcessor;
            _logger = logger;
        }

        public async Task Handle(INotification notification, CancellationToken cancellationToken)
        {
            // 这里需要根据实际的任务认领事件类型进行处理
            // 由于当前代码中没有明确的任务认领事件，这里提供一个示例实现
            
            try
            {
                _logger.LogInformation("处理任务认领游戏化奖励");

                // 实际实现中，应该从notification中提取任务认领信息
                // 然后创建TaskClaimedRewardEvent并处理奖励
                
                // 示例代码：
                // if (notification is TaskClaimedEvent claimedEvent)
                // {
                //     var rewardEvent = new TaskClaimedRewardEvent(
                //         claimedEvent.UserId,
                //         claimedEvent.TaskId,
                //         claimedEvent.TaskName,
                //         claimedEvent.ClaimedAt,
                //         claimedEvent.ShiftId
                //     );
                //
                //     var reward = await _ruleEngine.CalculateRewardAsync(rewardEvent);
                //     
                //     if (reward.HasReward)
                //     {
                //         await _rewardProcessor.ProcessRewardAsync(
                //             claimedEvent.UserId,
                //             reward,
                //             "TaskClaimed",
                //             claimedEvent.TaskId
                //         );
                //     }
                // }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理任务认领游戏化奖励时发生错误");
            }
        }
    }
}
