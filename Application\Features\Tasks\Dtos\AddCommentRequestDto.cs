#nullable enable
// File: Application/Features/Tasks/Dtos/AddCommentRequestDto.cs
// Description: DTO for adding a new comment to a task.

using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace ItAssetsSystem.Application.Features.Tasks.Dtos
{
    public class AddCommentRequestDto
    {
        [Required]
        [StringLength(2000, MinimumLength = 1)]
        public string Content { get; set; } = string.Empty;

        public long? ParentCommentId { get; set; }

        public List<int>? MentionedUserIds { get; set; }

        // Optional: If comments can be associated with specific attachments
        // public long? AttachmentId { get; set; }
    }
} 