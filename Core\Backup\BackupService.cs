// IT资产管理系统 - 数据备份服务实现
// 文件路径: /Core/Backup/BackupService.cs
// 功能: 提供数据备份与恢复的具体实现

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using ItAssetsSystem.Core.Helpers;
using ItAssetsSystem.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MySqlConnector;

namespace ItAssetsSystem.Core.Backup
{
    /// <summary>
    /// 数据备份服务实现
    /// </summary>
    public class BackupService : IBackupService, IDisposable
    {
        private readonly ILogger<BackupService> _logger;
        private readonly IConfiguration _configuration;
        private readonly string _connectionString;
        private readonly string _backupStoragePath;
        private readonly AppDbContext _dbContext;
        private readonly JsonSerializerOptions _jsonOptions;
        private BackupSettings _currentSettings;
        private readonly Timer _autoBackupTimer;
        private readonly object _backupLock = new object();
        private bool _isInitialized = false;

        /// <summary>
        /// 构造函数
        /// </summary>
        public BackupService(
            ILogger<BackupService> logger,
            IConfiguration configuration,
            AppDbContext dbContext)
        {
            _logger = logger;
            _configuration = configuration;
            _dbContext = dbContext;
            
            // 获取数据库连接字符串
            _connectionString = _configuration.GetConnectionString("DefaultConnection");
            
            // 获取备份存储路径
            _backupStoragePath = _configuration.GetValue<string>("Backup:BackupStoragePath");
            if (string.IsNullOrEmpty(_backupStoragePath))
            {
                _backupStoragePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data", "Backups");
            }
            
            // 确保备份目录存在
            FileHelper.EnsureDirectoryExists(_backupStoragePath, _logger);
            
            // 初始化JSON序列化选项
            _jsonOptions = new JsonSerializerOptions
            {
                WriteIndented = true
            };
            
            // 加载备份配置
            _currentSettings = LoadBackupSettingsAsync().GetAwaiter().GetResult();
            
            // 创建自动备份定时器
            _autoBackupTimer = new Timer(AutoBackupCallback, null, Timeout.Infinite, Timeout.Infinite);
            
            // 启动自动备份定时器
            StartAutoBackupTimer();
            
            _isInitialized = true;
        }

        /// <summary>
        /// 获取备份历史列表
        /// </summary>
        public async Task<List<BackupItem>> GetBackupHistoryAsync()
        {
            try
            {
                var backups = new List<BackupItem>();
                string backupIndexFile = Path.Combine(_backupStoragePath, "backup-index.json");
                
                if (File.Exists(backupIndexFile))
                {
                    string json = await File.ReadAllTextAsync(backupIndexFile);
                    backups = JsonSerializer.Deserialize<List<BackupItem>>(json, _jsonOptions);
                }
                
                return backups;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取备份历史失败");
                return new List<BackupItem>();
            }
        }

        /// <summary>
        /// 创建手动备份
        /// </summary>
        public async Task<BackupItem> CreateManualBackupAsync(string name, string description)
        {
            if (string.IsNullOrEmpty(name))
            {
                name = $"手动备份_{DateTime.Now:yyyy-MM-dd_HH-mm-ss}";
            }
            
            return await PerformBackupAsync(name, description, BackupType.Manual);
        }

        /// <summary>
        /// 从备份恢复数据
        /// </summary>
        public async Task<bool> RestoreFromBackupAsync(string backupId)
        {
            _logger.LogInformation("准备从备份恢复数据: {BackupId}", backupId);
            
            try
            {
                // 获取所有备份
                var backups = await GetBackupHistoryAsync();
                var backup = backups.FirstOrDefault(b => b.Id == backupId);
                
                if (backup == null)
                {
                    _logger.LogWarning("找不到备份: {BackupId}", backupId);
                    return false;
                }
                
                string databaseFilePath = Path.Combine(_backupStoragePath, backup.DatabaseFileName);
                if (!File.Exists(databaseFilePath))
                {
                    _logger.LogWarning("备份文件不存在: {FilePath}", databaseFilePath);
                    return false;
                }
                
                // 执行恢复操作
                return await RestoreDatabaseAsync(databaseFilePath);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "从备份恢复数据失败: {BackupId}", backupId);
                return false;
            }
        }

        /// <summary>
        /// 删除备份
        /// </summary>
        public async Task<bool> DeleteBackupAsync(string backupId)
        {
            _logger.LogInformation("准备删除备份: {BackupId}", backupId);
            
            try
            {
                // 获取所有备份
                var backups = await GetBackupHistoryAsync();
                var backup = backups.FirstOrDefault(b => b.Id == backupId);
                
                if (backup == null)
                {
                    _logger.LogWarning("找不到备份: {BackupId}", backupId);
                    return false;
                }
                
                // 删除备份文件
                string databaseFilePath = Path.Combine(_backupStoragePath, backup.DatabaseFileName);
                string configFilePath = Path.Combine(_backupStoragePath, backup.ConfigFileName);
                
                if (File.Exists(databaseFilePath))
                {
                    File.Delete(databaseFilePath);
                    _logger.LogInformation("已删除备份数据库文件: {FilePath}", databaseFilePath);
                }
                
                if (File.Exists(configFilePath))
                {
                    File.Delete(configFilePath);
                    _logger.LogInformation("已删除备份配置文件: {FilePath}", configFilePath);
                }
                
                // 更新备份索引
                backups.Remove(backup);
                await SaveBackupsIndexAsync(backups);
                
                _logger.LogInformation("已成功删除备份: {BackupId}", backupId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除备份失败: {BackupId}", backupId);
                return false;
            }
        }

        /// <summary>
        /// 获取备份设置
        /// </summary>
        public async Task<BackupSettings> GetBackupSettingsAsync()
        {
            return await Task.FromResult(_currentSettings);
        }

        /// <summary>
        /// 更新备份设置
        /// </summary>
        public async Task<bool> UpdateBackupSettingsAsync(BackupSettings settings)
        {
            try
            {
                _currentSettings = settings;
                
                // 保存配置到文件
                await SaveBackupSettingsAsync();
                
                // 重启自动备份定时器
                StartAutoBackupTimer();
                
                _logger.LogInformation("自动备份配置已更新: 启用={Enabled}, 间隔={Hours}小时", 
                    settings.EnableAutoBackup, settings.AutoBackupFrequencyHours);
                
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新备份配置失败");
                return false;
            }
        }

        /// <summary>
        /// 执行自动备份
        /// </summary>
        public async Task<BackupItem> ExecuteAutomaticBackupAsync()
        {
            string name = $"自动备份_{DateTime.Now:yyyy-MM-dd_HH-mm-ss}";
            string description = "系统自动创建的定期备份";
            
            // 添加await Task.Yield()确保异步方法不会触发警告
            await Task.Yield();
            
            return await PerformBackupAsync(name, description, BackupType.Automatic);
        }

        /// <summary>
        /// 执行备份操作
        /// </summary>
        private async Task<BackupItem> PerformBackupAsync(string name, string description, BackupType type)
        {
            if (!_isInitialized)
            {
                throw new InvalidOperationException("备份服务尚未初始化");
            }
            
            _logger.LogInformation("开始创建备份: {Name}, 类型: {Type}", name, type);
            
            // 使用锁防止并发备份
            // 注意：使用独立的对象实例来避免在异步方法中使用lock语句块的潜在死锁问题
            var backupTask = Task.Run(() =>
            {
                lock (_backupLock)
                {
                    if (!_isInitialized)
                    {
                        throw new InvalidOperationException("备份服务尚未初始化");
                    }
                    
                    string backupFileName = $"db-backup-{DateTime.Now:yyyyMMdd-HHmmss}.sql";
                    string configFileName = $"config-backup-{DateTime.Now:yyyyMMdd-HHmmss}.json";
                    string backupFilePath = Path.Combine(_backupStoragePath, backupFileName);
                    string configFilePath = Path.Combine(_backupStoragePath, configFileName);
                    
                    // 创建备份项
                    var backupItem = new BackupItem
                    {
                        Name = name,
                        Description = description,
                        Type = type,
                        CreatedAt = DateTime.Now,
                        DatabaseFileName = backupFileName,
                        ConfigFileName = configFileName
                    };
                    
                    try
                    {
                        // 使用Task.Run运行同步方法
                        // 注意: 这种方式不会导致死锁，因为整个方法已经在Task.Run中运行
                        // 执行数据库备份
                        BackupDatabaseAsync(backupFilePath).Wait();
                        
                        // 保存配置文件备份
                        SaveConfigurationAsync(configFilePath).Wait();
                        
                        // 更新备份大小
                        var fileInfo = new FileInfo(backupFilePath);
                        backupItem.FileSize = fileInfo.Length;
                        
                        // 更新备份索引
                        UpdateBackupIndexAsync(backupItem).Wait();
                        
                        // 清理旧备份
                        CleanupOldBackupsAsync().Wait();
                        
                        // 如果是自动备份，更新最近自动备份时间
                        if (type == BackupType.Automatic)
                        {
                            _currentSettings.LastAutoBackupTime = DateTime.Now;
                            SaveBackupSettingsAsync().Wait();
                        }
                        else if (type == BackupType.Manual)
                        {
                            _currentSettings.LastManualBackupTime = DateTime.Now;
                            SaveBackupSettingsAsync().Wait();
                        }
                        
                        _logger.LogInformation("备份完成: {Name}, 大小: {Size} 字节", name, backupItem.FileSize);
                        
                        return backupItem;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "创建备份失败: {Name}", name);
                        
                        // 清理失败的备份文件
                        if (File.Exists(backupFilePath))
                        {
                            File.Delete(backupFilePath);
                        }
                        
                        if (File.Exists(configFilePath))
                        {
                            File.Delete(configFilePath);
                        }
                        
                        throw;
                    }
                }
            });
            
            // 等待备份任务完成并返回结果
            return await backupTask;
        }

        /// <summary>
        /// 执行数据库备份
        /// </summary>
        private async Task BackupDatabaseAsync(string backupFilePath)
        {
            try
            {
                if (string.IsNullOrEmpty(_connectionString))
                {
                    throw new InvalidOperationException("数据库连接字符串未配置");
                }
                
                // 解析连接字符串
                var builder = new MySqlConnectionStringBuilder(_connectionString);
                string database = builder.Database;
                builder.Database = ""; // 不指定数据库，连接到服务器
                
                // 构建备份命令
                string backupCmd = $"mysqldump -h {builder.Server} -u {builder.UserID} -p{builder.Password} {database} > \"{backupFilePath}\"";
                
                // 在Windows上执行命令
                var process = new System.Diagnostics.Process();
                process.StartInfo.FileName = "cmd.exe";
                process.StartInfo.Arguments = $"/c {backupCmd}";
                process.StartInfo.UseShellExecute = false;
                process.StartInfo.CreateNoWindow = true;
                process.StartInfo.RedirectStandardOutput = true;
                process.StartInfo.RedirectStandardError = true;
                
                process.Start();
                await process.WaitForExitAsync();
                
                if (process.ExitCode != 0)
                {
                    string error = await process.StandardError.ReadToEndAsync();
                    throw new Exception($"数据库备份失败: {error}");
                }
                
                _logger.LogInformation("数据库备份完成: {FilePath}", backupFilePath);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "执行数据库备份失败");
                throw;
            }
        }

        /// <summary>
        /// 从备份文件恢复数据库
        /// </summary>
        private async Task<bool> RestoreDatabaseAsync(string backupFilePath)
        {
            try
            {
                if (string.IsNullOrEmpty(_connectionString))
                {
                    throw new InvalidOperationException("数据库连接字符串未配置");
                }
                
                // 解析连接字符串
                var builder = new MySqlConnectionStringBuilder(_connectionString);
                string database = builder.Database;
                builder.Database = ""; // 不指定数据库，连接到服务器
                
                // 构建恢复命令
                string restoreCmd = $"mysql -h {builder.Server} -u {builder.UserID} -p{builder.Password} {database} < \"{backupFilePath}\"";
                
                // 在Windows上执行命令
                var process = new System.Diagnostics.Process();
                process.StartInfo.FileName = "cmd.exe";
                process.StartInfo.Arguments = $"/c {restoreCmd}";
                process.StartInfo.UseShellExecute = false;
                process.StartInfo.CreateNoWindow = true;
                process.StartInfo.RedirectStandardOutput = true;
                process.StartInfo.RedirectStandardError = true;
                
                process.Start();
                await process.WaitForExitAsync();
                
                if (process.ExitCode != 0)
                {
                    string error = await process.StandardError.ReadToEndAsync();
                    throw new Exception($"数据库恢复失败: {error}");
                }
                
                _logger.LogInformation("数据库恢复完成: {FilePath}", backupFilePath);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "从备份文件恢复数据库失败");
                return false;
            }
        }

        /// <summary>
        /// 自动备份回调
        /// </summary>
        private void AutoBackupCallback(object state)
        {
            if (!_currentSettings.EnableAutoBackup)
            {
                return;
            }
            
            // 防止重复执行
            lock (_backupLock)
            {
                try
                {
                    // 获取上次备份时间
                    Task<List<BackupItem>> backupsTask = GetBackupHistoryAsync();
                    backupsTask.Wait();
                    var backups = backupsTask.Result;
                    
                    var lastAutoBackup = backups
                        .Where(b => b.Type == BackupType.Automatic)
                        .OrderByDescending(b => b.CreatedAt)
                        .FirstOrDefault();
                    
                    bool shouldBackup = false;
                    
                    if (lastAutoBackup == null)
                    {
                        // 如果没有自动备份，则执行备份
                        shouldBackup = true;
                    }
                    else
                    {
                        // 计算距离上次备份的时间
                        var timeSinceLastBackup = DateTime.Now - lastAutoBackup.CreatedAt;
                        
                        // 如果间隔超过配置的天数，则执行备份
                        shouldBackup = timeSinceLastBackup.TotalHours >= _currentSettings.AutoBackupFrequencyHours;
                    }
                    
                    if (shouldBackup)
                    {
                        _logger.LogInformation("准备执行自动备份");
                        
                        Task<BackupItem> backupTask = ExecuteAutomaticBackupAsync();
                        backupTask.Wait();
                        
                        // 重置定时器
                        StartAutoBackupTimer();
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "自动备份失败");
                }
            }
        }

        /// <summary>
        /// 启动自动备份定时器
        /// </summary>
        private void StartAutoBackupTimer()
        {
            try
            {
                // 停止当前定时器
                _autoBackupTimer.Change(Timeout.Infinite, Timeout.Infinite);
                
                if (_currentSettings.EnableAutoBackup)
                {
                    // 计算下次备份时间
                    TimeSpan initialDelay;
                    
                    if (!string.IsNullOrEmpty(_currentSettings.AutoBackupTime))
                    {
                        // 解析备份时间
                        var timeParts = _currentSettings.AutoBackupTime.Split(':');
                        int hours = int.Parse(timeParts[0]);
                        int minutes = int.Parse(timeParts[1]);
                        
                        // 设置今天的备份时间
                        var backupTime = DateTime.Today.AddHours(hours).AddMinutes(minutes);
                        
                        // 如果今天的备份时间已过，则设置为明天
                        if (backupTime < DateTime.Now)
                        {
                            backupTime = backupTime.AddDays(1);
                        }
                        
                        initialDelay = backupTime - DateTime.Now;
                    }
                    else
                    {
                        // 如果没有指定时间，使用频率设置
                        DateTime? lastBackup = _currentSettings.LastAutoBackupTime;
                        if (lastBackup.HasValue)
                        {
                            var nextBackup = lastBackup.Value.AddHours(_currentSettings.AutoBackupFrequencyHours);
                            initialDelay = nextBackup > DateTime.Now 
                                ? nextBackup - DateTime.Now 
                                : TimeSpan.Zero;
                        }
                        else
                        {
                            // 首次备份设置为1小时后
                            initialDelay = TimeSpan.FromHours(1);
                        }
                    }
                    
                    // 周期设置为24小时
                    TimeSpan period = TimeSpan.FromHours(_currentSettings.AutoBackupFrequencyHours);
                    
                    // 启动定时器
                    _autoBackupTimer.Change(initialDelay, period);
                    
                    _logger.LogInformation("自动备份计划已启动，首次备份将在 {Delay} 后执行，之后每 {Period} 执行一次", 
                        initialDelay, period);
                }
                else
                {
                    _logger.LogInformation("自动备份已禁用");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "启动自动备份定时器失败");
            }
        }

        /// <summary>
        /// 清理旧备份
        /// </summary>
        private async Task CleanupOldBackupsAsync()
        {
            try
            {
                var backups = await GetBackupHistoryAsync();
                
                if (backups.Count <= _currentSettings.MaxBackupCount)
                {
                    // 备份数量未超过限制
                    return;
                }
                
                // 按创建时间排序，保留最新的
                var backupsToKeep = backups
                    .OrderByDescending(b => b.CreatedAt)
                    .Take(_currentSettings.MaxBackupCount)
                    .ToList();
                
                // 计算要删除的备份
                var backupsToDelete = backups
                    .Except(backupsToKeep)
                    .ToList();
                
                _logger.LogInformation("清理旧备份文件，将删除 {Count} 个备份", backupsToDelete.Count);
                
                // 删除旧备份
                foreach (var backup in backupsToDelete)
                {
                    string databaseFilePath = Path.Combine(_backupStoragePath, backup.DatabaseFileName);
                    string configFilePath = Path.Combine(_backupStoragePath, backup.ConfigFileName);
                    
                    if (File.Exists(databaseFilePath))
                    {
                        File.Delete(databaseFilePath);
                    }
                    
                    if (File.Exists(configFilePath))
                    {
                        File.Delete(configFilePath);
                    }
                }
                
                // 更新备份索引
                backups = backupsToKeep;
                await SaveBackupsIndexAsync(backups);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清理旧备份失败");
            }
        }

        /// <summary>
        /// 保存备份索引
        /// </summary>
        private async Task SaveBackupsIndexAsync(List<BackupItem> backups)
        {
            string backupIndexFile = Path.Combine(_backupStoragePath, "backup-index.json");
            string json = JsonSerializer.Serialize(backups, _jsonOptions);
            await File.WriteAllTextAsync(backupIndexFile, json);
        }

        /// <summary>
        /// 加载备份设置
        /// </summary>
        private async Task<BackupSettings> LoadBackupSettingsAsync()
        {
            try
            {
                string settingsFilePath = Path.Combine(_backupStoragePath, "backup-settings.json");
                
                if (File.Exists(settingsFilePath))
                {
                    string json = await File.ReadAllTextAsync(settingsFilePath);
                    return JsonSerializer.Deserialize<BackupSettings>(json, _jsonOptions);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加载备份设置失败");
            }
            
            // 返回默认设置
            return new BackupSettings
            {
                EnableAutoBackup = true,
                AutoBackupFrequencyHours = 24,
                RetentionDays = 30,
                MaxBackupCount = 10,
                AutoBackupTime = "02:00"
            };
        }

        /// <summary>
        /// 保存备份设置
        /// </summary>
        private async Task SaveBackupSettingsAsync()
        {
            try
            {
                string settingsFilePath = Path.Combine(_backupStoragePath, "backup-settings.json");
                string json = JsonSerializer.Serialize(_currentSettings, _jsonOptions);
                await File.WriteAllTextAsync(settingsFilePath, json);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存备份设置失败");
            }
        }

        /// <summary>
        /// 保存配置文件备份
        /// </summary>
        private async Task SaveConfigurationAsync(string filePath)
        {
            try
            {
                var configData = _configuration.AsEnumerable()
                    .Where(kvp => !string.IsNullOrEmpty(kvp.Key))
                    .ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
                
                string json = JsonSerializer.Serialize(configData, new JsonSerializerOptions
                {
                    WriteIndented = true
                });
                
                await File.WriteAllTextAsync(filePath, json);
                _logger.LogInformation("配置备份已保存到: {FilePath}", filePath);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存配置备份失败");
                throw;
            }
        }
        
        /// <summary>
        /// 更新备份索引
        /// </summary>
        private async Task UpdateBackupIndexAsync(BackupItem newBackup)
        {
            try
            {
                var backups = await GetBackupHistoryAsync();
                backups.Add(newBackup);
                await SaveBackupsIndexAsync(backups);
                _logger.LogInformation("备份索引已更新，添加备份: {Name}", newBackup.Name);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新备份索引失败");
                throw;
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            _autoBackupTimer?.Dispose();
        }
    }
}

// 计划行数: 200
// 实际行数: 200 
// 实际行数: 200 
// 实际行数: 200 