<template>
  <div class="input-csv-page">
    <el-card shadow="hover" class="input-area">
      <el-input
        v-model="inputValue"
        placeholder="请输入条码，回车或点击提交"
        @keyup.enter="handleSubmit"
        clearable
        style="width: 320px; margin-right: 12px;"
      />
      <el-button type="primary" @click="handleSubmit">提交</el-button>
    </el-card>

    <el-card shadow="never" class="list-area" style="margin-top: 24px;">
      <div class="list-title">已输入内容</div>
      <el-table :data="inputList" style="width: 100%; margin-top: 8px;" size="small" v-if="inputList.length">
        <el-table-column label="序号" type="index" width="60" />
        <el-table-column prop="content" label="条码" />
        <el-table-column prop="time" label="时间序号" width="200" />
        <el-table-column label="操作" width="100">
          <template #default="scope">
            <el-button type="danger" size="small" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div v-else class="empty-tip">暂无数据</div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import axios from 'axios'

const inputValue = ref('')
const inputList = ref([])

const fetchList = async () => {
  // 假设后端 GET /api/v2/inputs 返回 { success, data: [{content, time}], ... }
  try {
    const res = await axios.get('/api/v2/inputs')
    if (res.data.success) {
      inputList.value = res.data.data
    }
  } catch (e) {
    inputList.value = []
  }
}

const handleSubmit = async () => {
  if (!inputValue.value.trim()) {
    ElMessage.warning('请输入条码内容')
    return
  }
  try {
    const res = await axios.post('/api/v2/inputs', { content: inputValue.value })
    if (res.data.success) {
      ElMessage.success('提交成功')
      inputValue.value = ''
      await fetchList()
    } else {
      ElMessage.error(res.data.message || '提交失败')
    }
  } catch (e) {
    ElMessage.error('网络错误，提交失败')
  }
}

const handleDelete = async (row) => {
  try {
    await axios.delete(`/api/v2/inputs/${encodeURIComponent(row.time)}`);
    ElMessage.success('删除成功');
    await fetchList();
  } catch (e) {
    ElMessage.error('删除失败');
  }
};

onMounted(fetchList)
</script>

<style scoped>
.input-csv-page {
  max-width: 600px;
  margin: 40px auto;
}
.input-area {
  display: flex;
  align-items: center;
  padding: 24px 16px;
}
.list-area {
  padding: 16px;
}
.list-title {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 8px;
}
.empty-tip {
  color: #aaa;
  text-align: center;
  padding: 32px 0;
}
</style> 