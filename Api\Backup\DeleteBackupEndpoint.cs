// IT资产管理系统 - 删除备份API
// 文件路径: /Api/Backup/DeleteBackupEndpoint.cs
// 功能: 删除备份的API端点

using System.Threading.Tasks;
using ItAssetsSystem.Core.Backup;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace ItAssetsSystem.Api.Backup
{
    [ApiController]
    [Route("api/backup/{backupId}")]
    public class DeleteBackupEndpoint : ControllerBase
    {
        private readonly IBackupService _backupService;
        private readonly ILogger<DeleteBackupEndpoint> _logger;

        public DeleteBackupEndpoint(
            IBackupService backupService,
            ILogger<DeleteBackupEndpoint> logger)
        {
            _backupService = backupService;
            _logger = logger;
        }

        public class DeleteBackupResponse
        {
            public bool Success { get; set; }
            public string Message { get; set; }
        }

        [HttpDelete]
        public async Task<ActionResult<DeleteBackupResponse>> HandleAsync(string backupId)
        {
            _logger.LogInformation("删除备份: {BackupId}", backupId);
            
            try
            {
                bool success = await _backupService.DeleteBackupAsync(backupId);
                
                if (success)
                {
                    return Ok(new DeleteBackupResponse
                    {
                        Success = true,
                        Message = "备份删除成功"
                    });
                }
                else
                {
                    return NotFound(new DeleteBackupResponse
                    {
                        Success = false,
                        Message = "备份不存在"
                    });
                }
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "删除备份失败");
                
                return StatusCode(500, new DeleteBackupResponse
                {
                    Success = false,
                    Message = $"删除备份失败: {ex.Message}"
                });
            }
        }
    }
}

// 计划行数: 25
// 实际行数: 25 