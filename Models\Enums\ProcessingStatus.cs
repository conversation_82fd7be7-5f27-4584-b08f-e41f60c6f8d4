using System.ComponentModel;

namespace ItAssetsSystem.Models.Enums
{
    /// <summary>
    /// 事件处理状态枚举
    /// </summary>
    public enum ProcessingStatus
    {
        /// <summary>
        /// 待处理 - 事件已记录，等待处理
        /// </summary>
        [Description("待处理")]
        Pending = 0,

        /// <summary>
        /// 处理中 - 正在更新汇总表
        /// </summary>
        [Description("处理中")]
        Processing = 1,

        /// <summary>
        /// 已完成 - 处理完成
        /// </summary>
        [Description("已完成")]
        Completed = 2,

        /// <summary>
        /// 处理失败 - 需要重试
        /// </summary>
        [Description("处理失败")]
        Failed = 3,

        /// <summary>
        /// 补偿中 - 正在进行数据修复
        /// </summary>
        [Description("补偿中")]
        Compensating = 4
    }

    /// <summary>
    /// 处理状态扩展方法
    /// </summary>
    public static class ProcessingStatusExtensions
    {
        /// <summary>
        /// 转换为字符串
        /// </summary>
        public static string ToStatusString(this ProcessingStatus status)
        {
            return status switch
            {
                ProcessingStatus.Pending => "Pending",
                ProcessingStatus.Processing => "Processing",
                ProcessingStatus.Completed => "Completed",
                ProcessingStatus.Failed => "Failed",
                ProcessingStatus.Compensating => "Compensating",
                _ => "Unknown"
            };
        }

        /// <summary>
        /// 从字符串解析
        /// </summary>
        public static ProcessingStatus FromStatusString(string status)
        {
            return status switch
            {
                "Pending" => ProcessingStatus.Pending,
                "Processing" => ProcessingStatus.Processing,
                "Completed" => ProcessingStatus.Completed,
                "Failed" => ProcessingStatus.Failed,
                "Compensating" => ProcessingStatus.Compensating,
                _ => ProcessingStatus.Pending
            };
        }

        /// <summary>
        /// 是否可以重试
        /// </summary>
        public static bool CanRetry(this ProcessingStatus status)
        {
            return status == ProcessingStatus.Failed || status == ProcessingStatus.Pending;
        }

        /// <summary>
        /// 是否已完成
        /// </summary>
        public static bool IsCompleted(this ProcessingStatus status)
        {
            return status == ProcessingStatus.Completed;
        }

        /// <summary>
        /// 是否需要处理
        /// </summary>
        public static bool NeedsProcessing(this ProcessingStatus status)
        {
            return status == ProcessingStatus.Pending || status == ProcessingStatus.Failed;
        }
    }
}
