Okay, let's refactor the frontend code, focusing on the task-related parts, to align with the provided backend API documentation (prioritizing V2 Task endpoints).

**Assumptions:**

1.  We will primarily use the `/api/v2/tasks` endpoints for core Task CRUD and related operations.
2.  Gamification features currently in `TaskController.cs` (V1) will be temporarily commented out or noted as needing separate integration, as they don't map directly to the V2 Task API.
3.  The standard API response structure `{ success: boolean, data: any, message: string }` is expected. For lists, `data` is expected to be `{ items: [], totalCount: number }` or similar (like `{ list: [], total: number }`).
4.  We'll map frontend statuses ('todo', 'in-progress', 'completed', 'overdue') to backend API statuses ('unstarted', 'in-progress', 'completed') where applicable. 'overdue' will likely be a calculated display state.
5.  DTO field names (`TaskQueryParametersDto`, `CreateTaskRequestDto`, etc.) are inferred from context or assumed to align closely with existing frontend models where not explicitly detailed in the API doc.

---

**Refactoring Steps:**

**1. Refactor `frontend/src/api/task.js`**

*   Update endpoints to V2.
*   Adjust methods (e.g., PATCH for status/progress/assign).
*   Map status parameters if necessary.
*   Comment out or remove V1/Gamification specific calls.

```javascript
/**
 * IT资产管理系统 - 任务管理API (V2 Refactored)
 * 文件路径: src/api/task.js
 * 功能描述: 提供任务管理相关的API服务 (基于V2后端接口)
 */

import request from '@/utils/request'

/**
 * 任务管理相关API (V2)
 */
const taskApi = {
  /**
   * 获取任务列表 (V2)
   * @param {object} params 查询参数 (TaskQueryParametersDto)
   *   - page, pageSize, status, priority, type, assigneeId, creatorId,
   *   - assetId, locationId, startDate, endDate, search (keyword)
   * @returns {Promise}
   */
  getTaskList(params) {
    // Map frontend filter keys to potential backend keys if different
    const apiParams = {
      pageIndex: params.page || 1, // Assuming backend uses pageIndex
      pageSize: params.pageSize || 10,
      status: params.status || undefined,
      priority: params.priority || undefined,
      // type: params.type || undefined, // Ensure backend supports 'type' filtering
      assigneeId: params.assignee || undefined, // Map 'assignee' to 'assigneeId'
      search: params.keyword || undefined, // Map 'keyword' to 'search'
      // Add mappings for other V2 filter parameters if used in frontend
      startDate: params.dateRange?.[0] || undefined,
      endDate: params.dateRange?.[1] || undefined,
    };
    // Remove undefined params before sending
    Object.keys(apiParams).forEach(key => apiParams[key] === undefined && delete apiParams[key]);
    console.log('getTaskList V2 params:', apiParams);
    return request.get('/v2/tasks', { params: apiParams });
  },

  /**
   * 获取任务详情 (V2)
   * @param {string | number} id 任务ID
   * @returns {Promise}
   */
  getTaskDetail(id) {
    console.log('getTaskDetail V2 ID:', id);
    return request.get(`/v2/tasks/${id}`);
  },

  /**
   * 创建新任务 (V2)
   * @param {object} data 任务数据 (CreateTaskRequestDto)
   * @returns {Promise}
   */
  createTask(data) {
    console.log('createTask V2 data:', data);
    // Ensure payload matches CreateTaskRequestDto
    return request.post('/v2/tasks', data);
  },

  /**
   * 更新任务 (V2)
   * @param {string | number} id 任务ID
   * @param {object} data 更新数据 (UpdateTaskRequestDto)
   * @returns {Promise}
   */
  updateTask(id, data) {
    console.log('updateTask V2 ID:', id, 'data:', data);
    // Ensure payload matches UpdateTaskRequestDto
    return request.put(`/v2/tasks/${id}`, data);
  },

  /**
   * 更新任务状态 (V2)
   * @param {string | number} id 任务ID
   * @param {{ status: string, remarks?: string }} statusData 状态数据 (UpdateTaskStatusRequestDto)
   * @returns {Promise}
   */
  updateTaskStatus(id, statusData) {
    console.log('updateTaskStatus V2 ID:', id, 'statusData:', statusData);
    // Ensure payload matches UpdateTaskStatusRequestDto
    return request.patch(`/v2/tasks/${id}/status`, statusData);
  },

  /**
   * 更新任务进度 (V2)
   * @param {string | number} id 任务ID
   * @param {{ progress: number, remarks?: string }} progressData 进度数据 (UpdateTaskProgressRequestDto)
   * @returns {Promise}
   */
  updateTaskProgress(id, progressData) {
     console.log('updateTaskProgress V2 ID:', id, 'progressData:', progressData);
     return request.patch(`/v2/tasks/${id}/progress`, progressData);
  },

  /**
   * 分配任务 (V2)
   * @param {string | number} id 任务ID
   * @param {{ assigneeId: number }} assignData 分配数据 (AssignTaskRequestDto)
   * @returns {Promise}
   */
  assignTask(id, assignData) {
     console.log('assignTask V2 ID:', id, 'assignData:', assignData);
     return request.patch(`/v2/tasks/${id}/assign`, assignData);
  },

  /**
   * 完成任务 (V2)
   * @param {string | number} id 任务ID
   * @param {{ remarks?: string }} completionData 完成备注
   * @returns {Promise}
   */
  completeTask(id, completionData = {}) {
     console.log('completeTask V2 ID:', id, 'completionData:', completionData);
     return request.patch(`/v2/tasks/${id}/complete`, completionData);
  },

  /**
   * 删除任务 (V2)
   * @param {string | number} id 任务ID
   * @returns {Promise}
   */
  deleteTask(id) {
    console.log('deleteTask V2 ID:', id);
    return request.delete(`/v2/tasks/${id}`);
  },

  // --- Comments (V2) ---
  /**
   * 添加评论 (V2)
   * @param {string | number} taskId 任务ID
   * @param {{ content: string }} commentData 评论内容 (AddCommentRequestDto)
   * @returns {Promise}
   */
  addComment(taskId, commentData) {
    console.log('addComment V2 taskId:', taskId, 'commentData:', commentData);
    return request.post(`/v2/tasks/${taskId}/comments`, commentData);
  },

  /**
   * 获取评论列表 (V2)
   * @param {string | number} taskId 任务ID
   * @param {object} params 分页等参数
   * @returns {Promise}
   */
  getComments(taskId, params) {
    console.log('getComments V2 taskId:', taskId, 'params:', params);
    return request.get(`/v2/tasks/${taskId}/comments`, { params });
  },

  // --- Attachments (V2) ---
  /**
   * 上传任务附件 (V2)
   * @param {string | number} taskId 任务ID
   * @param {FormData} formData 文件数据 (包含 file 和可选 description)
   * @returns {Promise}
   */
  uploadTaskAttachment(taskId, formData) {
    // Description might need to be sent as query param or part of FormData
    console.log('uploadTaskAttachment V2 taskId:', taskId);
    return request.post(`/v2/tasks/${taskId}/attachments`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
      // If description is query param: params: { description: formData.get('description') }
    });
  },

   /**
   * 获取任务附件列表 (V2)
   * @param {string | number} taskId 任务ID
   * @returns {Promise}
   */
  getTaskAttachments(taskId) {
      console.log('getTaskAttachments V2 taskId:', taskId);
      return request.get(`/v2/tasks/${taskId}/attachments`);
  },

  /**
   * 删除任务附件 (V2)
   * @param {string | number} taskId 任务ID
   * @param {string | number} attachmentId 附件ID
   * @returns {Promise}
   */
  deleteTaskAttachment(taskId, attachmentId) {
    console.log('deleteTaskAttachment V2 taskId:', taskId, 'attachmentId:', attachmentId);
    return request.delete(`/v2/tasks/${taskId}/attachments/${attachmentId}`);
  },

  // --- History (V2) ---
  /**
   * 获取任务活动日志 (V2)
   * @param {string | number} taskId 任务ID
   * @param {object} params 分页等参数
   * @returns {Promise}
   */
  getTaskActivityLog(taskId, params) {
    console.log('getTaskActivityLog V2 taskId:', taskId, 'params:', params);
    return request.get(`/v2/tasks/${taskId}/history`, { params });
  },

  // --- V1 / Gamification APIs (Commented out or to be moved) ---
  /*
  getTaskLeaderboard(params) { // Gamification
    return request.get('/Task/leaderboard', { params });
  },
  getUserItems(userId) { // Gamification
    return request.get('/Task/user-items', { params: { userId: userId || 1 } });
  },
  dailySignIn() { // Gamification
    return request.post('/Task/sign-in');
  },
  claimTask(id) { // V1 - Replaced by assignTask in V2?
    return request.post(`/Task/${id}/claim`);
  },
  */
  // ... other V1 functions
}

export default taskApi
```

**2. Refactor `frontend/src/views/tasks/TaskListView.vue`**

*   Update API calls in `fetchTasks`, `saveTask`, `handleDeleteTask`.
*   Adjust response data handling based on V2 structure.
*   Verify filter parameters passed to `getTaskList`.
*   Update status and priority display helpers if needed.
*   Add fields for periodic tasks in the dialog and table if required.

```vue
<template>
  <div class="task-list page-container">
    <h2 class="page-title">任务列表</h2>

    <!-- 过滤器区域 -->
    <el-card shadow="never" class="filter-card mb-4">
      <div class="filters">
        <!-- Filter controls -->
        <el-select v-model="filters.status" placeholder="状态" clearable style="width: 120px;">
           <!-- Use API statuses -->
          <el-option label="未开始" value="unstarted"></el-option>
          <el-option label="进行中" value="in-progress"></el-option>
          <el-option label="已完成" value="completed"></el-option>
          <!-- Add more API statuses if available, 'overdue' might be a calculated filter -->
        </el-select>
        <el-select v-model="filters.priority" placeholder="优先级" clearable style="width: 100px;">
          <el-option label="高" value="high"></el-option>
          <el-option label="中" value="medium"></el-option>
          <el-option label="低" value="low"></el-option>
        </el-select>
        <el-select v-model="filters.assigneeId" placeholder="负责人" clearable filterable style="width: 130px;">
           <el-option label="全部" value=""></el-option>
          <el-option v-for="member in teamMembers" :key="member.id" :label="member.name" :value="member.id"></el-option>
        </el-select>
        <el-input v-model="filters.keyword" placeholder="搜索标题..." clearable style="width: 180px;" @keyup.enter="applyFilters"></el-input>
        <el-button type="primary" :icon="Search" @click="applyFilters" :loading="loading">搜索</el-button>
        <el-button :icon="Refresh" @click="resetFilters">重置</el-button>
      </div>
      <div class="actions mt-4">
         <el-button type="primary" :icon="Plus" @click="handleCreateTask">新增任务</el-button>
         <!-- Add other batch actions like Export if needed -->
      </div>
    </el-card>

    <!-- 任务表格 -->
    <el-card shadow="never" class="task-table-card">
      <el-table :data="tasks" v-loading="loading" style="width: 100%">
        <el-table-column prop="title" label="任务标题" min-width="250">
          <template #default="{ row }">
            <span class="task-title-link" @click="viewTaskDetail(row.id)">{{ row.title }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
             <!-- Use V2 API status -->
            <el-tag :type="getTaskStatusStyle(row.status).type" size="small" effect="light" round>
              {{ getTaskStatusStyle(row.status).label }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="priority" label="优先级" width="80" align="center">
          <template #default="{ row }">
             <el-tag :color="getPriorityColor(row.priority)" effect="dark" size="small" style="border: none; color: white;">{{ getPriorityLabel(row.priority) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="assigneeName" label="负责人" width="120">
           <template #default="{ row }">
             <div class="user-cell">
                <el-avatar :size="24" :src="getUserAvatar(row.assigneeId)" class="table-avatar" @error="onAvatarError">{{ getInitials(getUserName(row.assigneeId)) }}</el-avatar>
                <span>{{ getUserName(row.assigneeId) }}</span>
             </div>
           </template>
        </el-table-column>
        <el-table-column prop="endDate" label="截止时间" width="140" sortable>
          <template #default="{ row }">
             <!-- Check if isOverdue is calculated or from API -->
            <span :class="{ overdue: checkOverdue(row) && row.status !== 'completed' }">
              <el-icon v-if="row.endDate" size="small"><Clock /></el-icon> {{ formatDate(row.endDate, false) || '-' }}
            </span>
          </template>
        </el-table-column>
         <el-table-column prop="createDate" label="创建时间" width="140" sortable>
           <template #default="{ row }">
             {{ formatDate(row.createDate, false) || '-' }}
           </template>
         </el-table-column>
         <!-- Add column for Periodic Task info -->
         <el-table-column label="周期性" width="100" align="center">
             <template #default="{ row }">
                 <el-tag v-if="row.isPeriodic" type="success" size="small">是</el-tag>
                 <el-tag v-else type="info" size="small">否</el-tag>
             </template>
         </el-table-column>
        <el-table-column label="操作" width="150" fixed="right" align="center">
          <template #default="{ row }">
            <el-tooltip content="编辑" placement="top">
                <el-button type="primary" link :icon="Edit" @click="handleEditTask(row)" size="small"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
                 <el-button type="danger" link :icon="Delete" @click="handleDeleteTask(row.id)" size="small"></el-button>
            </el-tooltip>
             <el-tooltip content="查看详情" placement="top">
                 <el-button type="info" link :icon="View" @click="viewTaskDetail(row.id)" size="small"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        class="mt-4 pagination-right"
        v-model:current-page="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="totalTasks"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :disabled="loading"
      />
    </el-card>

    <!-- 新增/编辑任务弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogMode === 'create' ? '新增任务' : '编辑任务'"
      width="clamp(500px, 60%, 800px)"
      top="8vh"
      :close-on-click-modal="false"
      draggable
      append-to-body
      @close="handleDialogClose"
    >
      <el-form
        ref="taskFormRef"
        :model="taskForm"
        :rules="taskRules"
        label-width="100px"
        label-position="right"
        style="padding: 0 10px;"
        @submit.prevent="saveTask"
      >
        <el-form-item label="任务标题" prop="title">
          <el-input v-model="taskForm.title" placeholder="请输入任务标题" maxlength="100" show-word-limit clearable />
        </el-form-item>
         <!-- Periodic Task Fields -->
         <el-form-item label="任务类型">
            <el-checkbox v-model="taskForm.isPeriodic">是否为周期性任务</el-checkbox>
         </el-form-item>
         <el-row :gutter="20" v-if="taskForm.isPeriodic">
            <el-col :span="12">
                 <el-form-item label="开始日期" prop="startDate">
                     <el-date-picker v-model="taskForm.startDate" type="date" placeholder="选择开始日期" style="width: 100%" format="YYYY-MM-DD" value-format="YYYY-MM-DD"/>
                 </el-form-item>
            </el-col>
             <el-col :span="12">
                 <el-form-item label="结束日期" prop="endDate">
                     <el-date-picker v-model="taskForm.endDate" type="date" placeholder="选择结束日期" style="width: 100%" format="YYYY-MM-DD" value-format="YYYY-MM-DD"/>
                 </el-form-item>
             </el-col>
             <el-col :span="12">
                  <el-form-item label="频率" prop="frequency">
                       <el-select v-model="taskForm.frequency" placeholder="选择频率" style="width: 100%">
                           <el-option label="每日" value="Daily"></el-option>
                           <el-option label="每周" value="Weekly"></el-option>
                           <el-option label="每月" value="Monthly"></el-option>
                           <el-option label="每年" value="Yearly"></el-option>
                           <!-- Add more frequencies if needed -->
                       </el-select>
                   </el-form-item>
             </el-col>
         </el-row>

        <el-row :gutter="20">
            <el-col :span="12">
                 <el-form-item label="负责人" prop="assigneeId">
                    <el-select v-model="taskForm.assigneeId" placeholder="请选择负责人" filterable clearable style="width: 100%">
                        <el-option v-for="member in teamMembers" :key="member.id" :label="member.name" :value="member.id"></el-option>
                    </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="12">
                <el-form-item label="优先级" prop="priority">
                    <el-select v-model="taskForm.priority" placeholder="请选择优先级" style="width: 100%">
                        <el-option label="低" value="low"></el-option>
                        <el-option label="中" value="medium"></el-option>
                        <el-option label="高" value="high"></el-option>
                    </el-select>
                </el-form-item>
            </el-col>
        </el-row>
        <el-row :gutter="20">
             <el-col :span="12" v-if="!taskForm.isPeriodic"> <!-- End date only for non-periodic -->
                <el-form-item label="截止日期" prop="endDate">
                    <el-date-picker v-model="taskForm.endDate" type="date" placeholder="选择截止日期" style="width: 100%" format="YYYY-MM-DD" value-format="YYYY-MM-DD" :editable="false" />
                </el-form-item>
            </el-col>
             <el-col :span="12">
                 <el-form-item label="状态" prop="status">
                     <el-select v-model="taskForm.status" placeholder="选择状态" style="width: 100%">
                         <el-option label="未开始" value="unstarted"></el-option>
                         <el-option label="进行中" value="in-progress"></el-option>
                         <!-- Usually completion is done via action, not initial status -->
                         <!-- <el-option label="已完成" value="completed"></el-option> -->
                     </el-select>
                 </el-form-item>
             </el-col>
        </el-row>
        <el-form-item label="任务描述" prop="description">
          <el-input v-model="taskForm.description" type="textarea" :rows="4" placeholder="请输入详细的任务描述" show-word-limit maxlength="500" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveTask" :loading="isSaving">{{ isSaving ? '保存中...' : '保存' }}</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
// Import statements remain the same
import { ref, reactive, onMounted, nextTick, computed } from 'vue'
import { useRouter } from 'vue-router'
import {
    Search, Refresh, Plus, Edit, Delete, View, Clock
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import taskApi from '../../api/task.js' // Use the refactored taskApi
import { useUserStore } from '@/stores/modules/user'

const router = useRouter()
const userStore = useUserStore()
const loading = ref(false)
const tasks = ref([])
const totalTasks = ref(0)
const defaultAvatar = 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png'

// --- Dialog State ---
const dialogVisible = ref(false)
const dialogMode = ref('create') // 'create' or 'edit'
const taskFormRef = ref(null) // Reference to the form
const taskForm = ref(getDefaultTaskForm()) // Form data object
const isSaving = ref(false)
const currentTaskId = ref(null) // Store ID when editing

// Use computed property for team members
const teamMembers = computed(() => userStore.usersList || [])

// --- Filters & Pagination ---
const filters = reactive({
  status: '',
  priority: '',
  assigneeId: '', // Match the API parameter
  keyword: ''
})

const pagination = reactive({
  currentPage: 1,
  pageSize: 10
})

// --- Form Validation Rules ---
const taskRules = {
  title: [
    { required: true, message: '请输入任务标题', trigger: 'blur' },
    { min: 3, max: 100, message: '标题长度为 3-100 字符', trigger: 'blur' },
  ],
  assigneeId: [{ required: true, message: '请选择负责人', trigger: 'change' }],
  priority: [{ required: true, message: '请选择优先级', trigger: 'change' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }],
  startDate: [{ required: function(){ return taskForm.value.isPeriodic }, message: '请输入开始日期', trigger: 'change'}],
  endDate: [{ required: function(){ return taskForm.value.isPeriodic }, message: '请输入结束日期', trigger: 'change'}],
  frequency: [{ required: function(){ return taskForm.value.isPeriodic }, message: '请选择频率', trigger: 'change'}]
};

// --- Data Fetching ---
async function fetchTasks() {
  loading.value = true
  try {
    const params = {
      page: pagination.currentPage,
      pageSize: pagination.pageSize,
      status: filters.status || undefined,
      priority: filters.priority || undefined,
      assigneeId: filters.assigneeId || undefined, // Use assigneeId
      search: filters.keyword || undefined,
    }
    Object.keys(params).forEach(key => params[key] === undefined && delete params[key]);

    const response = await taskApi.getTaskList(params) // Use refactored API
    // Adapt to expected V2 response structure
    if (response && (Array.isArray(response.items) || Array.isArray(response.list)) ) {
        const taskItems = response.items || response.list;
        tasks.value = taskItems.map(task => ({
            ...task,
            isOverdue: checkOverdue(task),
            assigneeName: getUserName(task.assigneeId) // Ensure assigneeId is correct
        }));
        totalTasks.value = response.totalCount || response.total || tasks.value.length;
    }
     else {
      tasks.value = []
      totalTasks.value = 0
      console.warn('API getTaskList (V2) did not return expected structure:', response)
    }
  } catch (error) {
    ElMessage.error('获取任务列表失败')
    console.error('Error fetching tasks (V2):', error)
    tasks.value = []
    totalTasks.value = 0
  } finally {
    loading.value = false
  }
}

// --- Filtering & Pagination Handlers ---
function applyFilters() {
  pagination.currentPage = 1
  fetchTasks()
}

function resetFilters() {
  filters.status = ''
  filters.priority = ''
  filters.assigneeId = '' // Reset assigneeId
  filters.keyword = ''
  pagination.currentPage = 1
  fetchTasks()
}

function handleSizeChange(val) {
  pagination.pageSize = val
  pagination.currentPage = 1
  fetchTasks()
}

function handleCurrentChange(val) {
  pagination.currentPage = val
  fetchTasks()
}

// --- CRUD Handlers ---
function getDefaultTaskForm() {
    return {
        title: '',
        description: '',
        assigneeId: null,
        priority: 'medium',
        endDate: null,
        status: 'unstarted', // API status
        isPeriodic: false,   // New field for periodic tasks
        startDate: null,     // New field
        frequency: null,     // New field (e.g., 'Daily', 'Weekly')
    };
}

function handleCreateTask() {
  dialogMode.value = 'create';
  taskForm.value = getDefaultTaskForm();
  currentTaskId.value = null;
  dialogVisible.value = true;
  nextTick(() => {
      taskFormRef.value?.clearValidate();
  });
}

function handleEditTask(task) {
  dialogMode.value = 'edit';
  // Map API data to form fields
  taskForm.value = {
      ...getDefaultTaskForm(), // Start with defaults
      title: task.title,
      description: task.description,
      assigneeId: task.assigneeId,
      priority: task.priority,
      endDate: task.endDate, // Keep original format if picker handles it
      status: task.status, // Use the API status directly
      isPeriodic: task.isPeriodic || false,
      startDate: task.startDate,
      frequency: task.frequency,
      // Map other fields if needed
  };
  currentTaskId.value = task.id;
  dialogVisible.value = true;
   nextTick(() => {
      taskFormRef.value?.clearValidate();
  });
}

function handleDialogClose() {
    taskFormRef.value?.clearValidate();
}

async function saveTask() {
  if (!taskFormRef.value) return;
  await taskFormRef.value.validate(async (valid) => {
    if (valid) {
      isSaving.value = true;
      try {
        const dataToSave = { ...taskForm.value };
        // Remove the ID from data for create, ensure it's present for update
        if (dialogMode.value === 'create') {
           delete dataToSave.id;
        } else {
           dataToSave.id = currentTaskId.value; // Ensure ID is present for update
        }

        // Clean up periodic fields if not a periodic task
        if (!dataToSave.isPeriodic) {
            delete dataToSave.startDate;
            delete dataToSave.frequency;
            // Keep endDate for non-periodic if it was set
        } else {
            // Ensure endDate is null if it's periodic and startDate exists? Check backend logic.
            // dataToSave.endDate = null; // Maybe? Depends on backend.
        }

        if (dialogMode.value === 'create') {
          await taskApi.createTask(dataToSave); // Use refactored API
          ElMessage.success('任务创建成功');
        } else if (dialogMode.value === 'edit' && currentTaskId.value) {
          await taskApi.updateTask(currentTaskId.value, dataToSave); // Use refactored API
          ElMessage.success('任务更新成功');
        }
        dialogVisible.value = false;
        fetchTasks(); // Refresh the list
      } catch (error) {
        ElMessage.error(dialogMode.value === 'create' ? '创建任务失败' : '更新任务失败');
        console.error('Save task error (V2):', error);
      } finally {
        isSaving.value = false;
      }
    } else {
      ElMessage.warning('请检查表单填写是否正确');
      return false;
    }
  });
}

function handleDeleteTask(taskId) {
  ElMessageBox.confirm('确定要删除此任务吗？此操作不可恢复。', '确认删除', {
    confirmButtonText: '删除',
    cancelButtonText: '取消',
    type: 'warning',
  })
  .then(async () => {
      loading.value = true;
      try {
          await taskApi.deleteTask(taskId); // Use refactored API
          ElMessage.success('任务删除成功');
          fetchTasks(); // Refresh the list
      } catch (error) {
          ElMessage.error('删除任务失败');
          console.error('Delete task error (V2):', error);
          loading.value = false;
      }
      // No finally loading false here, fetchTasks handles it
  })
  .catch(() => {
      ElMessage.info('删除已取消');
  });
}

// --- View Task Detail ---
function viewTaskDetail(taskId) {
  if (!taskId) return;
  // Ensure the route name matches your router configuration
  router.push({ name: 'TaskDetail', params: { id: taskId } });
}

// --- Helper Functions ---
function getUserName(userId) {
  const user = teamMembers.value.find(u => u.id === userId); // Use computed teamMembers
  return user?.name || '未知用户';
}

function getUserAvatar(userId) {
  const user = teamMembers.value.find(u => u.id === userId);
  return user?.avatar || defaultAvatar;
}

function getInitials(name) {
    if (!name || typeof name !== 'string') return '?';
    const names = name.split(' ');
    let initials = names[0].substring(0, 1).toUpperCase();
    if (names.length > 1) {
        initials += names[names.length - 1].substring(0, 1).toUpperCase();
    }
    return initials;
}

function onAvatarError(event) {
  event.target.style.display = 'none'; // Let the initials show
}

// Map API status to display style
const taskStatusMapV2 = {
  unstarted: { label: '未开始', type: 'info' },
  'in-progress': { label: '进行中', type: 'primary' }, // Changed to primary
  completed: { label: '已完成', type: 'success' },
  // 'overdue' is calculated, not a direct API status usually
};

function getTaskStatusStyle(status) {
  return taskStatusMapV2[status] || { label: status, type: 'info' };
}

const priorityMap = {
    high: { label: '高', color: 'var(--danger-color)' },
    medium: { label: '中', color: 'var(--warning-color)'},
    low: { label: '低', color: 'var(--success-color)' },
};

function getPriorityLabel(priority) {
    return priorityMap[priority]?.label || '中'; // Default to Medium
}

function getPriorityColor(priority) {
    return priorityMap[priority]?.color || 'var(--warning-color)'; // Default to Medium color
}

function formatDate(dateStr, includeTime = true) {
  if (!dateStr) return '';
  try {
    const date = new Date(dateStr);
    if (isNaN(date.getTime())) return dateStr; // Return original if invalid
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    if (includeTime) {
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      return `${year}-${month}-${day} ${hours}:${minutes}`;
    }
    return `${year}-${month}-${day}`;
  } catch (e) {
    return dateStr; // Return original on error
  }
}

function checkOverdue(task) {
  // Assumes task.status is the API status ('unstarted', 'in-progress', 'completed')
  return task.endDate && task.status !== 'completed' && new Date(task.endDate) < new Date();
}

// --- Lifecycle Hook ---
onMounted(async () => {
  // Fetch users if not already in store
  if (!userStore.usersList || userStore.usersList.length === 0) {
    try {
       await userStore.getUsers(); // Assuming this action fetches and stores users
    } catch (error) {
        console.error("Failed to load users on mount:", error);
        ElMessage.error("加载用户列表失败");
    }
  }
  // Fetch initial tasks
  fetchTasks();
})
</script>

<style scoped>
/* Styles remain largely the same, adjust if needed */
.task-list {
  /* Basic container styles */
}
.filter-card {
    border: none;
    background-color: var(--card-bg);
}
.filters {
  display: flex;
  gap: 10px;
  align-items: center;
  flex-wrap: wrap;
}
.actions {
    display: flex;
    justify-content: flex-start;
}
.task-table-card {
    margin-top: 20px;
    border: none;
    background-color: var(--card-bg);
}
.task-title-link {
    cursor: pointer;
    color: var(--primary-color);
    transition: color 0.2s;
}
.task-title-link:hover {
    color: var(--primary-color-dark);
    text-decoration: underline;
}
.user-cell {
    display: flex;
    align-items: center;
    gap: 8px;
}
.table-avatar {
    background-color: #eee;
    font-size: 0.7rem;
}
.dark-theme .table-avatar {
    background-color: var(--sidebar-bg);
}
.overdue {
  color: var(--danger-color);
  font-weight: bold;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}
.pagination-right {
    justify-content: flex-end;
}
:deep(.el-dialog) {
    background-color: var(--card-bg);
}
:deep(.el-dialog__title) {
    color: var(--text-color);
}
:deep(.el-form-item__label) {
    color: var(--text-color-secondary);
}
.mt-4 { margin-top: 1rem; }
.mb-4 { margin-bottom: 1rem; }
</style>
```

**3. Refactor `frontend/src/views/tasks/KanbanView.vue`**

*   Update `fetchKanbanData` to use `taskApi.getTaskList` (V2).
*   Update `onDrop` to use `taskApi.updateTaskStatus` (V2).
*   Crucially, update the status mapping logic (`mapApiToKanbanStatus`, `mapKanbanToApiStatus`) to align V2 API statuses (`unstarted`, `in-progress`, `completed`) with Kanban columns (`todo`, `in-progress`, `completed`). 'overdue' needs careful handling (likely client-side determination for column placement).
*   Remove V1-specific logic if any remains.

```vue
<template>
  <!-- Template remains largely the same -->
  <div class="kanban-view page-container">
    <h2 class="page-title">任务看板</h2>

    <!-- Filter Card -->
    <el-card shadow="never" class="filter-card mb-4">
      <div class="filters">
        <el-input v-model="searchQuery" placeholder="搜索任务标题..." clearable style="width: 200px;" @input="debouncedFetchData">
          <template #prepend><el-icon><Search /></el-icon></template>
        </el-input>
        <!-- Add other filters if needed -->
        <el-button @click="fetchKanbanData" :icon="Refresh" :loading="loading">刷新</el-button>
      </div>
    </el-card>

    <!-- Loading Skeleton -->
    <div v-if="loading" class="loading-skeleton">
      <el-skeleton :rows="10" animated />
    </div>

    <!-- Kanban Board -->
    <div class="kanban-board" v-show="!loading">
      <div
        v-for="column in taskColumns"
        :key="column.status"
        class="task-column"
        :class="{ 'drag-over': dragOverStatus === column.status }"
        @dragover.prevent="onDragOver($event, column.status)"
        @dragenter.prevent="onDragEnter($event, column.status)"
        @dragleave.prevent="onDragLeave($event)"
        @drop.prevent="onDrop($event, column.status)"
      >
        <div class="column-header">
          <h4>{{ column.title }}</h4>
          <span class="status-count">{{ column.tasks.length }} 项</span>
        </div>
        <draggable
          class="task-list"
          :list="column.tasks"
          group="tasks"
          item-key="id"
          ghost-class="ghost-card"
          chosen-class="chosen-card"
          drag-class="drag-card"
          :animation="300"
          @start="onDragStart"
          @end="onDragEnd"
        >
          <template #item="{ element: task }">
            <div
              class="task-card"
              :class="[`priority-${task.priority || 'medium'}`, { 'is-dragging': draggedTask && draggedTask.id === task.id }]"
              draggable="true"
              @dragstart="setDraggedTask($event, task)"
              @click="viewTaskDetail(task.id)"
              :data-id="task.id" <!-- Add data-id for easier retrieval in drop -->
            >
              <!-- Task card content remains the same -->
               <div class="task-title">{{ task.title }}</div>
              <div class="task-meta">
                <div class="assignee">
                  <el-avatar :size="22" :src="getUserAvatar(task.assigneeId)" class="small-avatar" @error="onAvatarError">{{ getInitials(getUserName(task.assigneeId)) }}</el-avatar>
                  <span>{{ getUserName(task.assigneeId) }}</span>
                </div>
                <span :class="['priority-tag', `priority-${task.priority || 'medium'}`]">{{ priorityLabel(task.priority) }}</span>
              </div>
              <div class="task-meta">
                <span class="due-date" :class="{ overdue: task.isOverdue && task.status !== 'completed' }">
                  <el-icon><Clock /></el-icon>
                   <!-- Use Kanban status for display logic -->
                  {{ task.status === 'completed' ? '完成:' : task.isOverdue ? '逾期:' : '截止:' }} {{ formatDate(task.endDate, false) || '无' }}
                </span>
              </div>
            </div>
          </template>
        </draggable>
        <div v-if="!loading && column.tasks.length === 0" class="empty-column-placeholder">
          <el-icon><InfoFilled /></el-icon>
          <span>暂无任务</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// Imports remain the same
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { Clock, Search, Refresh, InfoFilled } from '@element-plus/icons-vue'
import draggable from 'vuedraggable'
import taskApi from '../../api/task.js' // Use the refactored taskApi
import userApi from '../../api/user.js'
import { ElMessage, ElSkeleton } from 'element-plus'
import { useUserStore } from '@/stores/modules/user'

const router = useRouter();
const userStore = useUserStore();
const loading = ref(false);
const searchQuery = ref('');
const draggedTask = ref(null);
const dragOverStatus = ref(null);
const defaultAvatar = 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png';
const teamMembersList = computed(() => userStore.usersList || []); // Use computed for users

// --- Kanban Status Definitions ---
const kanbanStatusMap = {
  todo: '待办',
  'in-progress': '进行中',
  completed: '已完成',
  // 'overdue' is not a target status for dropping
};
const definedStatuses = ['todo', 'in-progress', 'completed']; // Columns to display

// --- Task Data ---
const allTasks = ref([]);

// --- API Status Mapping ---
function mapApiToDisplayStatus(apiStatus, isOverdue = false) {
    // Overdue check takes precedence unless completed
    if (isOverdue && apiStatus !== 'completed') {
        // Even if API status is 'in-progress', show in 'overdue' if applicable?
        // Decide based on desired UX. Let's assume overdue has its own visual cue,
        // but the task *stays* in its logical API status column.
        // So, we don't return 'overdue' here, but calculate isOverdue flag separately.
    }
    switch (apiStatus) {
        case 'unstarted': return 'todo';
        case 'in-progress': return 'in-progress';
        case 'completed': return 'completed';
        default:
            console.warn(`Unmapped API status to display: ${apiStatus}`);
            return 'todo'; // Default to 'todo'
    }
}

function mapDisplayToApiStatus(displayStatus) {
    switch (displayStatus) {
        case 'todo': return 'unstarted';
        case 'in-progress': return 'in-progress';
        case 'completed': return 'completed';
        default: return null; // Cannot map unknown display status back
    }
}


// --- Data Fetching ---
async function fetchKanbanData() {
  loading.value = true;
  try {
    const params = {
      pageSize: 1000, // Fetch all for client-side grouping
      search: searchQuery.value || undefined,
    };
    Object.keys(params).forEach(key => params[key] === undefined && delete params[key]);

    const response = await taskApi.getTaskList(params); // Use refactored API
    let rawTasks = [];
    // Adapt to V2 response structure
    if (response && (Array.isArray(response.items) || Array.isArray(response.list))) {
         rawTasks = response.items || response.list;
    } else {
      console.warn('API for Kanban (V2) did not return expected structure:', response);
      rawTasks = [];
    }

    allTasks.value = rawTasks.map(task => {
      const isOverdue = checkOverdue(task);
      // Map API status to display status for column assignment
      const displayStatus = mapApiToDisplayStatus(task.status, isOverdue);
      return {
        ...task,
        assigneeName: getUserName(task.assigneeId),
        isOverdue: isOverdue,
        status: displayStatus, // Use the mapped display status for the column
        originalApiStatus: task.status // Store original API status
      };
    });

  } catch (error) {
    ElMessage.error('获取看板数据失败');
    console.error('Error fetching kanban data (V2):', error);
    allTasks.value = [];
  } finally {
    loading.value = false;
  }
}

// --- Computed Properties ---
const taskColumns = computed(() => {
  const columns = definedStatuses.map(statusKey => ({
    status: statusKey,
    title: kanbanStatusMap[statusKey] || statusKey,
    tasks: []
  }));

  allTasks.value.forEach(task => {
    const targetColumn = columns.find(col => col.status === task.status); // Use display status
    if (targetColumn) {
      targetColumn.tasks.push(task);
    } else {
      // Handle tasks with unmappable status if necessary (e.g., put in 'todo')
      const todoColumn = columns.find(col => col.status === 'todo');
      if (todoColumn) {
          todoColumn.tasks.push(task);
          console.warn(`Task ${task.id} has unmapped display status '${task.status}', placing in 'todo'.`);
      }
    }
  });
  return columns;
});

// --- Drag and Drop Handlers ---
function setDraggedTask(event, task) {
  event.dataTransfer.setData('text/plain', task.id);
  event.dataTransfer.effectAllowed = 'move';
  draggedTask.value = task;
}

function onDragStart() { /* Optional: Add specific logic */ }
function onDragEnd() {
  draggedTask.value = null;
  dragOverStatus.value = null;
}
function onDragOver(event, targetStatus) {
    event.preventDefault();
    if (draggedTask.value && draggedTask.value.status !== targetStatus) {
        dragOverStatus.value = targetStatus;
    } else {
        dragOverStatus.value = null;
    }
}
function onDragEnter(event, targetStatus) { event.preventDefault(); }
function onDragLeave(event) {
    // Simplified leave logic
    const columnElement = event.currentTarget; // The column div
    // Check if the related target (where the mouse is going) is outside the current column
    if (!columnElement.contains(event.relatedTarget)) {
      dragOverStatus.value = null;
    }
}

async function onDrop(event, newDisplayStatus) {
  dragOverStatus.value = null;
  if (!draggedTask.value) return;

  const taskId = draggedTask.value.id;
  const originalDisplayStatus = draggedTask.value.status;

  if (newDisplayStatus === originalDisplayStatus) {
    draggedTask.value = null;
    return;
  }

  // Map display status back to API status for the update
  const apiStatusToUpdate = mapDisplayToApiStatus(newDisplayStatus);

  if (!apiStatusToUpdate) {
      ElMessage.warning(`无法将任务移动到 '${kanbanStatusMap[newDisplayStatus] || newDisplayStatus}' 状态。`);
      draggedTask.value = null;
      return; // Prevent drop
  }

  // --- Optimistic UI Update ---
  const sourceColumn = taskColumns.value.find(col => col.status === originalDisplayStatus);
  const taskIndex = sourceColumn?.tasks.findIndex(t => t.id === taskId);
  let taskToMove = null;

  if (sourceColumn && taskIndex !== -1) {
      taskToMove = sourceColumn.tasks.splice(taskIndex, 1)[0];
      taskToMove.originalDisplayStatus = originalDisplayStatus; // Store original display status for rollback
  } else {
      console.error(`Optimistic update failed: Could not find task ${taskId} in column ${originalDisplayStatus}`);
      ElMessage.error('看板更新失败，请刷新重试。');
      draggedTask.value = null;
      return;
  }

  const targetColumn = taskColumns.value.find(col => col.status === newDisplayStatus);
  if (targetColumn && taskToMove) {
      taskToMove.status = newDisplayStatus; // Update display status
      taskToMove.originalApiStatus = apiStatusToUpdate; // Update original API status *before* API call
      taskToMove.isOverdue = checkOverdue(taskToMove); // Recalculate overdue
      targetColumn.tasks.push(taskToMove);
  } else {
      console.error(`Optimistic update failed: Could not find target column ${newDisplayStatus}`);
      if (sourceColumn && taskToMove) sourceColumn.tasks.splice(taskIndex, 0, taskToMove); // Rollback
      ElMessage.error('看板更新失败，请刷新重试。');
      draggedTask.value = null;
      return;
  }

  // --- API Call ---
  try {
    await taskApi.updateTaskStatus(taskId, { status: apiStatusToUpdate }); // Use refactored API
    ElMessage.success(`任务状态已更新为: ${kanbanStatusMap[newDisplayStatus]}`);
    if (taskToMove) delete taskToMove.originalDisplayStatus; // Clear rollback helper
    // Optional: fetchKanbanData();
  } catch (error) {
    ElMessage.error('更新任务状态失败');
    console.error('Error updating task status (V2):', error);
    // Rollback optimistic UI
    if (targetColumn && taskToMove) {
      const movedIndex = targetColumn.tasks.findIndex(t => t.id === taskId);
      if (movedIndex !== -1) targetColumn.tasks.splice(movedIndex, 1);
    }
    if (sourceColumn && taskToMove) {
      taskToMove.status = taskToMove.originalDisplayStatus; // Restore original display status
      taskToMove.isOverdue = checkOverdue(taskToMove);
      sourceColumn.tasks.splice(taskIndex, 0, taskToMove);
    }
  } finally {
    draggedTask.value = null;
  }
}


// --- Helper Functions (getUserName, getUserAvatar, etc. remain the same) ---
function getUserById(userId) {
  return teamMembersList.value.find(m => m.id === userId) || null;
}
function getUserName(userId) {
  return getUserById(userId)?.name || '未知';
}
function getUserAvatar(userId) {
  return getUserById(userId)?.avatar || defaultAvatar;
}
function getInitials(name) {
    if (!name || typeof name !== 'string') return '?';
    const names = name.split(' ');
    let initials = names[0].substring(0, 1).toUpperCase();
    if (names.length > 1) initials += names[names.length - 1].substring(0, 1).toUpperCase();
    return initials;
}
function onAvatarError(event) { event.target.style.display = 'none'; }
function priorityLabel(priority) { return { low: '低', medium: '中', high: '高' }[priority] || '中'; }
function formatDate(dateStr, includeTime = true) {
  if (!dateStr) return '';
  try {
    const date = new Date(dateStr);
    if (isNaN(date.getTime())) return '无效日期';
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    if (includeTime) {
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      return `${year}-${month}-${day} ${hours}:${minutes}`;
    }
    return `${year}-${month}-${day}`;
  } catch (e) { return '无效日期'; }
}
function checkOverdue(task) {
    // Check against API status if available, otherwise use display status
    const checkStatus = task.originalApiStatus || task.status;
    return task.endDate && checkStatus !== 'completed' && new Date(task.endDate) < new Date();
}
function viewTaskDetail(taskId) {
  if (!taskId) return;
  router.push({ name: 'TaskDetail', params: { id: taskId } });
}

// --- Debounce ---
let debounceTimer;
function debouncedFetchData() {
    clearTimeout(debounceTimer);
    debounceTimer = setTimeout(() => fetchKanbanData(), 500);
}

// --- Lifecycle ---
onMounted(() => {
    // Ensure users are loaded before fetching tasks
    if (!userStore.usersList || userStore.usersList.length === 0) {
      userStore.getUsers().then(() => fetchKanbanData()).catch(err => {
          console.error("Failed to load users for Kanban:", err);
          fetchKanbanData(); // Try fetching tasks anyway
      });
    } else {
      fetchKanbanData();
    }
});

</script>

<style scoped>
/* Styles remain largely the same as TaskListView */
.kanban-view {
  padding: 20px;
  background-color: var(--bg-color);
  height: calc(100vh - 60px); /* Adjust based on actual header height */
  display: flex;
  flex-direction: column;
}
.page-title {
  font-size: 1.8rem;
  font-weight: 600;
  color: var(--header-bg);
  margin-bottom: 15px;
}
.filter-card {
  margin-bottom: 20px;
  background-color: var(--card-bg);
  border: none;
  border-radius: 8px;
}
.filters {
  display: flex;
  gap: 15px;
  align-items: center;
  flex-wrap: wrap;
}
.loading-skeleton { padding: 20px; }
.kanban-board {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  flex-grow: 1;
  overflow-x: auto;
  padding-bottom: 10px;
}
.task-column {
  background-color: var(--column-bg);
  border-radius: 8px;
  padding: 15px;
  display: flex;
  flex-direction: column;
  border: 1px solid var(--border-color-light);
  max-height: calc(100vh - 230px);
  min-height: 200px;
  transition: background-color 0.3s ease;
}
.task-column.drag-over {
  background-color: var(--drag-over-bg);
  border: 1px dashed var(--primary-color);
}
.column-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 12px;
  margin-bottom: 15px;
  border-bottom: 1px solid var(--border-color-light);
}
.column-header h4 {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--header-bg);
  margin: 0;
}
.status-count {
  background-color: #e9ecef;
  color: var(--text-secondary-color);
  border-radius: 12px;
  padding: 4px 10px;
  font-size: 0.75rem;
  font-weight: 600;
}
.task-list {
  flex-grow: 1;
  overflow-y: auto;
  min-height: 100px;
  padding-right: 5px;
}
.task-list::-webkit-scrollbar { width: 6px; }
.task-list::-webkit-scrollbar-track { background: transparent; }
.task-list::-webkit-scrollbar-thumb { background-color: #ccc; border-radius: 3px; }
.task-list::-webkit-scrollbar-thumb:hover { background-color: #aaa; }
.task-card {
  background-color: var(--card-bg);
  border-radius: 6px;
  padding: 12px 15px;
  margin-bottom: 12px;
  box-shadow: var(--shadow-light);
  transition: var(--transition);
  cursor: grab;
  border-left: 5px solid var(--info-color);
  position: relative;
}
.task-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}
.task-card.priority-high { border-left-color: var(--danger-color); }
.task-card.priority-medium { border-left-color: var(--warning-color); }
.task-card.priority-low { border-left-color: var(--success-color); }
.task-card.is-dragging {
  opacity: 0.7;
  transform: rotate(2deg);
  cursor: grabbing;
}
.task-card.ghost-card {
  opacity: 0.4;
  background: var(--hover-bg-color);
  border: 2px dashed var(--border-color);
}
.task-title {
  font-weight: 600;
  font-size: 0.95rem;
  color: var(--header-bg);
  line-height: 1.4;
  margin-bottom: 8px;
  word-break: break-word;
}
.task-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.8rem;
  color: var(--text-secondary-color);
  margin-bottom: 6px;
  flex-wrap: wrap;
  gap: 8px;
}
.task-meta:last-child { margin-bottom: 0; }
.assignee { display: flex; align-items: center; gap: 6px; }
.small-avatar {
  width: 22px;
  height: 22px;
  border-radius: 50%;
  object-fit: cover;
  background-color: #eee;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  font-weight: bold;
  color: #555;
}
.priority-tag { padding: 2px 6px; border-radius: 4px; font-size: 0.7rem; font-weight: 600; }
.priority-tag.priority-low { background: #e1fcef; color: #1a7431; }
.priority-tag.priority-medium { background: #fff8e1; color: #b8860b; }
.priority-tag.priority-high { background: #ffebee; color: #b71c1c; }
.due-date { display: inline-flex; align-items: center; gap: 4px; }
.due-date .el-icon { font-size: 0.9em; }
.due-date.overdue { color: var(--danger-color); font-weight: bold; }
.empty-column-placeholder {
  text-align: center;
  color: var(--text-placeholder-color);
  margin-top: 20px;
  font-size: 0.9em;
  padding: 20px;
  border: 2px dashed var(--border-color-light);
  border-radius: 6px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
}
.empty-column-placeholder .el-icon { font-size: 1.5rem; }
.task-list > div { width: 100%; }
.dark-theme .status-count { background-color: var(--dark-border-color); color: var(--dark-text-color); }
.dark-theme .priority-tag.priority-low { background: #2c5d3b; color: #c8e6c9; }
.dark-theme .priority-tag.priority-medium { background: #6f4f14; color: #ffecb3; }
.dark-theme .priority-tag.priority-high { background: #7f1d1d; color: #ffcdd2; }
.dark-theme .small-avatar { background-color: #4a5568; color: #e2e8f0;}
</style>
```

**4. Refactor `frontend/src/views/tasks/TaskDetailView.vue`**

*   Update API calls (`fetchTaskDetails`, `handleStatusChange`, `submitComment`, `handleUpload`, `confirmDeleteAttachment`, `confirmDeleteTask`, `fetchComments`, `fetchLogs`) to use the *refactored* `taskApi` V2 functions.
*   Adjust data access based on the V2 task object structure (e.g., `task.value.assigneeId`).
*   Ensure status mapping (`mapApiToDisplayStatus`, `mapDisplayToApiStatus`) is consistent with the V2 API.
*   Handle periodic task details display if relevant fields are added to the V2 task object.

```vue
<template>
  <!-- Template remains largely the same -->
  <div class="task-detail-view page-container" v-loading="loading">
    <div v-if="task && !loading" class="content-wrapper">
      <!-- Header -->
      <div class="page-header mb-4">
        <div class="header-left">
          <el-button @click="goBack" text size="small" class="back-button">
            <el-icon><ArrowLeft /></el-icon> 返回
          </el-button>
          <h2 class="page-title">{{ task.title }}</h2>
        </div>
        <div class="header-right">
          <el-button type="danger" @click="confirmDeleteTask" :icon="Delete" :loading="deleting">删除</el-button>
        </div>
      </div>

      <!-- Main Content Grid -->
      <el-row :gutter="20">
        <!-- Left Panel: Details & Description -->
        <el-col :xs="24" :sm="16" :md="17">
          <el-card shadow="never" class="details-card mb-4">
             <template #header>
              <div class="card-header">
                <span><el-icon><InfoFilled /></el-icon> 基本信息</span>
              </div>
            </template>
            <div class="info-grid">
              <div class="info-item">
                <span class="item-label">状态:</span>
                <span class="item-value">
                  <!-- Use API status for the model, map labels -->
                  <el-select v-model="task.status" size="small" @change="handleStatusChange" :loading="updatingStatus">
                    <el-option label="未开始" value="unstarted"></el-option>
                    <el-option label="进行中" value="in-progress"></el-option>
                    <el-option label="已完成" value="completed"></el-option>
                  </el-select>
                </span>
              </div>
              <!-- Other info items -->
               <div class="info-item">
                <span class="item-label">优先级:</span>
                <span class="item-value">
                  <el-tag :type="getPriorityType(task.priority)" size="small" effect="light" round>{{ priorityLabel(task.priority) }}</el-tag>
                </span>
              </div>
              <div class="info-item">
                <span class="item-label">负责人:</span>
                <span class="item-value assignee">
                   <el-avatar :size="24" :src="getUserAvatar(task.assigneeId)" class="small-avatar" @error="onAvatarError">{{ getInitials(getUserName(task.assigneeId)) }}</el-avatar>
                   <span>{{ getUserName(task.assigneeId) }}</span>
                </span>
              </div>
              <div class="info-item">
                <span class="item-label">创建时间:</span>
                <span class="item-value">{{ formatDate(task.createDate) || '-' }}</span>
              </div>
              <div class="info-item">
                <span class="item-label">截止时间:</span>
                <span class="item-value" :class="{ 'overdue': checkOverdue(task) && task.status !== 'completed' }">
                  <el-icon><Clock /></el-icon> {{ formatDate(task.endDate, false) || '未设置' }}
                </span>
              </div>
               <!-- Periodic Task Info -->
                <template v-if="task.isPeriodic">
                    <div class="info-item">
                        <span class="item-label">任务类型:</span>
                        <span class="item-value"><el-tag type="success" size="small">周期性</el-tag></span>
                    </div>
                    <div class="info-item">
                        <span class="item-label">开始日期:</span>
                        <span class="item-value">{{ formatDate(task.startDate, false) || '-' }}</span>
                    </div>
                     <div class="info-item">
                        <span class="item-label">结束日期:</span>
                        <span class="item-value">{{ formatDate(task.endDate, false) || '-' }}</span>
                    </div>
                    <div class="info-item">
                        <span class="item-label">频率:</span>
                        <span class="item-value">{{ task.frequency || '-' }}</span>
                    </div>
                </template>
            </div>
            <el-divider />
            <div class="description-section">
              <h4><el-icon><Document /></el-icon> 任务描述</h4>
              <p class="description-content">{{ task.description || '暂无描述' }}</p>
            </div>
          </el-card>

           <el-card shadow="never" class="attachments-card mb-4">
             <!-- Attachment section remains similar, ensure delete uses V2 API -->
              <template #header>
               <div class="card-header">
                 <span><el-icon><Paperclip /></el-icon> 附件</span>
               </div>
             </template>
             <el-upload
                ref="uploadRef"
                action="#"
                :http-request="handleUpload"
                :on-remove="handleRemove"
                :file-list="fileListDisplay"
                :before-upload="beforeUpload"
                :on-exceed="handleExceed"
                multiple
                :limit="5"
                list-type="text"
                class="upload-area"
             >
                <el-button type="primary" :icon="Upload">点击上传</el-button>
                <template #tip>
                  <div class="el-upload__tip">单个文件不超过10MB，最多上传5个文件</div>
                </template>
             </el-upload>
              <div class="attachments-list" v-if="task.attachments && task.attachments.length > 0">
                  <div v-for="attachment in task.attachments" :key="attachment.id || attachment.name" class="attachment-item">
                    <el-icon><Document /></el-icon>
                    <span class="attachment-name">{{ attachment.name }}</span>
                    <span class="attachment-size">({{ formatFileSize(attachment.size) }})</span>
                    <div class="attachment-actions">
                      <el-button type="danger" link size="small" :icon="Delete" @click="confirmDeleteAttachment(attachment)" :loading="deletingAttachmentId === attachment.id">删除</el-button>
                    </div>
                  </div>
              </div>
              <el-empty v-else description="暂无附件"></el-empty>
           </el-card>
        </el-col>

        <!-- Right Panel: Comments & Activity -->
        <el-col :xs="24" :sm="8" :md="7">
          <el-card shadow="never" class="activity-card">
              <!-- Tabs remain the same, ensure API calls use V2 -->
              <el-tabs v-model="activeTab">
                  <el-tab-pane label="评论" name="comments" lazy>
                     <!-- Comment Section: API calls need update -->
                      <div class="comment-section">
                          <div class="comments-list thin-scrollbar" v-loading="loadingComments">
                             <div v-if="taskComments.length > 0">
                                  <div v-for="comment in taskComments" :key="comment.id" class="comment-item">
                                      <el-avatar :size="32" :src="getUserAvatar(comment.userId)" class="comment-avatar" @error="onAvatarError">{{ getInitials(getUserName(comment.userId)) }}</el-avatar>
                                      <div class="comment-content">
                                          <div class="comment-header">
                                              <span class="comment-user">{{ getUserName(comment.userId) }}</span>
                                              <span class="comment-time">{{ formatTimeAgo(comment.createDate || comment.time) }}</span>
                                          </div>
                                          <div class="comment-text">{{ comment.content }}</div>
                                      </div>
                                  </div>
                              </div>
                              <el-empty v-else description="暂无评论"></el-empty>
                          </div>
                          <div class="comment-form">
                              <el-input v-model="newComment" type="textarea" :rows="3" placeholder="输入评论..." :disabled="isSubmittingComment" resize="none"/>
                              <el-button type="primary" @click="submitComment" :loading="isSubmittingComment" :disabled="!newComment.trim()" class="submit-comment-btn">发表</el-button>
                          </div>
                      </div>
                  </el-tab-pane>
                  <el-tab-pane label="活动日志" name="logs" lazy>
                     <!-- Activity Log: API call needs update -->
                      <div class="activity-logs thin-scrollbar" v-loading="loadingLogs">
                          <div v-if="taskLogs.length > 0">
                              <div v-for="log in taskLogs" :key="log.id" class="activity-item">
                                  <el-avatar :size="28" :src="getUserAvatar(log.userId)" class="activity-avatar" @error="onAvatarError">{{ getInitials(getUserName(log.userId)) }}</el-avatar>
                                  <div class="activity-content">
                                      <span class="activity-text">
                                          <span class="activity-user">{{ getUserName(log.userId) }}</span>
                                          {{ log.action }}
                                      </span>
                                      <span class="activity-time">{{ formatTimeAgo(log.createDate || log.time) }}</span>
                                  </div>
                              </div>
                          </div>
                           <el-empty v-else description="暂无活动记录"></el-empty>
                      </div>
                  </el-tab-pane>
              </el-tabs>
          </el-card>
        </el-col>
      </el-row>

    </div>
    <div v-else-if="!loading" class="task-not-found">
      <el-empty description="任务不存在或加载失败">
        <el-button @click="goBack">返回任务列表</el-button>
      </el-empty>
    </div>
  </div>
</template>

<script setup>
// Imports remain the same
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import {
  ArrowLeft, Delete, Edit, InfoFilled, Document, Clock, Paperclip, Upload, Download
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox, ElUpload } from 'element-plus'
import taskApi from '../../api/task.js' // Use refactored V2 API
import userApi from '../../api/user.js'
import { formatDistanceToNow } from 'date-fns'
import { zhCN } from 'date-fns/locale'

const router = useRouter();
const route = useRoute();
const taskId = route.params.id;

const loading = ref(true);
const updatingStatus = ref(false);
const deleting = ref(false);
const task = ref(null);
const taskComments = ref([]);
const taskLogs = ref([]);
const newComment = ref('');
const isSubmittingComment = ref(false);
const loadingComments = ref(false);
const loadingLogs = ref(false);
const uploadRef = ref();
const fileList = ref([]);
const deletingAttachmentId = ref(null);
const activeTab = ref('comments');
const teamMembersList = ref([]);

const defaultAvatar = 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png';

const fileListDisplay = computed(() => fileList.value.map(f => ({name: f.name, size: f.size, uid: f.uid, status: f.status})));

// --- Data Fetching ---
async function fetchTaskDetails() {
  if (!taskId) {
    ElMessage.error('无效的任务ID');
    loading.value = false;
    return;
  }
  loading.value = true;
  try {
    // Fetch users first if needed
    if (teamMembersList.value.length === 0) {
      try {
        const userResponse = await userApi.getUserList({ pageSize: 1000 });
         if (userResponse && Array.isArray(userResponse.list)) {
            teamMembersList.value = userResponse.list;
        } else if (userResponse && Array.isArray(userResponse)) {
             teamMembersList.value = userResponse;
        } else {
             teamMembersList.value = [];
        }
      } catch (userError) {
        console.error('Error fetching team members:', userError);
        teamMembersList.value = []; // Ensure it's an array
      }
    }

    const response = await taskApi.getTaskDetail(taskId); // Use V2 API
    if (response && response.data) { // Check response.data for V2 structure
      task.value = processTaskData(response.data);
      fetchComments();
      fetchLogs();
    } else {
      task.value = null;
      ElMessage.error(response?.message || '获取任务详情失败');
    }
  } catch (error) {
    ElMessage.error('加载任务详情时出错');
    console.error('Error fetching task details (V2):', error);
    task.value = null;
  } finally {
    loading.value = false;
  }
}

function processTaskData(rawData) {
  const isOverdue = rawData.endDate && rawData.status !== 'completed' && new Date(rawData.endDate) < new Date();
  // const displayStatus = mapApiToDisplayStatus(rawData.status); // No longer needed for model
  return {
    ...rawData,
    isOverdue,
    // status: displayStatus, // Keep API status for model
    originalApiStatus: rawData.status, // Store original API status
    attachments: Array.isArray(rawData.attachments) ? rawData.attachments : []
  };
}

async function fetchComments() {
  if (!taskId) return;
  loadingComments.value = true;
  try {
    const response = await taskApi.getComments(taskId); // Use V2 API
    // Adapt to V2 response structure
    taskComments.value = response?.data || [];
  } catch (error) {
    console.error("Error fetching comments (V2):", error);
    taskComments.value = [];
  } finally {
    loadingComments.value = false;
  }
}

async function fetchLogs() {
  if (!taskId) return;
  loadingLogs.value = true;
  try {
    const response = await taskApi.getTaskActivityLog(taskId); // Use V2 API
    // Adapt to V2 response structure
    taskLogs.value = response?.data || [];
  } catch (error) {
    console.error("Error fetching logs (V2):", error);
    taskLogs.value = [];
  } finally {
    loadingLogs.value = false;
  }
}

// --- Actions ---
async function handleStatusChange(newApiStatus) {
  if (!task.value || newApiStatus === task.value.originalApiStatus) return;

  updatingStatus.value = true;
  try {
    await taskApi.updateTaskStatus(taskId, { status: newApiStatus }); // Use V2 API
    ElMessage.success('任务状态更新成功');
    task.value.originalApiStatus = newApiStatus; // Update stored original status
    // The v-model binding already updated task.value.status
    // Refresh derived states
    task.value.isOverdue = checkOverdue(task.value);

  } catch (error) {
    ElMessage.error('更新任务状态失败');
    task.value.status = task.value.originalApiStatus; // Revert on failure
    console.error('Status update error (V2):', error);
  } finally {
    updatingStatus.value = false;
  }
}

async function submitComment() {
  if (!newComment.value.trim() || !taskId) return;
  isSubmittingComment.value = true;
  try {
    await taskApi.addComment(taskId, { content: newComment.value }); // Use V2 API
    await fetchComments(); // Re-fetch comments
    newComment.value = '';
    ElMessage.success('评论已发表');
  } catch (error) {
    console.error("Error submitting comment (V2):", error);
    ElMessage.error('发表评论失败');
  } finally {
    isSubmittingComment.value = false;
  }
}

function confirmDeleteTask() {
   ElMessageBox.confirm(
    '确定要删除此任务吗？此操作不可恢复。',
    '确认删除任务',
    { confirmButtonText: '确认删除', cancelButtonText: '取消', type: 'warning' }
  ).then(async () => {
    deleting.value = true;
    try {
      await taskApi.deleteTask(taskId); // Use V2 API
      ElMessage.success('任务删除成功');
      router.push('/main/tasks/list');
    } catch (error) {
      console.error('Delete task error (V2):', error);
      ElMessage.error('删除任务失败');
    } finally {
      deleting.value = false;
    }
  }).catch(() => ElMessage.info('删除已取消'));
}

// --- Attachments ---
function beforeUpload(rawFile) {
  const isLt10M = rawFile.size / 1024 / 1024 < 10;
  if (!isLt10M) ElMessage.error('附件大小不能超过 10MB!');
  return isLt10M;
}
function handleExceed(files, uploadFiles) { ElMessage.warning(`限制上传 5 个文件，本次选择了 ${files.length} 个`); }
async function handleUpload(options) {
  const { file, onSuccess, onError, onProgress } = options;
  const formData = new FormData();
  formData.append('file', file);

  try {
    // Use taskApi.uploadTaskAttachment (V2)
    const response = await taskApi.uploadTaskAttachment(taskId, formData);
    // Check V2 response structure for success indication
    if (response && response.success) {
      ElMessage.success(`${file.name} 上传成功`);
      onSuccess(response.data); // Pass data if API returns attachment info
      fetchTaskDetails(); // Refresh to get the latest attachment list
    } else {
        throw new Error(response?.message || '上传失败');
    }
  } catch (error) {
    console.error('Upload error (V2):', error);
    ElMessage.error(`${file.name} 上传失败: ${error.message}`);
    onError(error);
  }
}
function handleRemove(file, uploadFiles) { console.log('Removed file from upload list:', file.name); }
function confirmDeleteAttachment(attachment) {
  if (!attachment?.id) return;
  ElMessageBox.confirm(`确定删除附件 "${attachment.name}"?`, '确认删除', { type: 'warning' })
  .then(async () => {
    deletingAttachmentId.value = attachment.id;
    try {
      await taskApi.deleteTaskAttachment(taskId, attachment.id); // Use V2 API
      ElMessage.success('附件删除成功');
      // Refresh task details
      fetchTaskDetails();
    } catch (error) {
      console.error('Delete attachment error (V2):', error);
      ElMessage.error('删除附件失败');
    } finally {
      deletingAttachmentId.value = null;
    }
  }).catch(() => ElMessage.info('删除已取消'));
}

// --- Helpers ---
function goBack() { router.go(-1); }
function getUserById(userId) { return teamMembersList.value.find(m => m.id === userId) || null; }
function getUserName(userId) { return getUserById(userId)?.name || '未知'; }
function getUserAvatar(userId) { return getUserById(userId)?.avatar || defaultAvatar; }
function getInitials(name) {
    if (!name || typeof name !== 'string') return '?';
    const names = name.split(' ');
    let initials = names[0].substring(0, 1).toUpperCase();
    if (names.length > 1) initials += names[names.length - 1].substring(0, 1).toUpperCase();
    return initials;
}
function onAvatarError(event) { event.target.style.display = 'none'; }
function priorityLabel(priority) { return { low: '低', medium: '中', high: '高' }[priority] || '中'; }
function getPriorityType(priority) { return { low: 'success', medium: 'warning', high: 'danger' }[priority] || 'info'; }
function formatDate(dateStr, includeTime = true) {
  if (!dateStr) return '';
  try {
    const date = new Date(dateStr);
    if (isNaN(date.getTime())) return dateStr;
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    if (includeTime) {
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      return `${year}-${month}-${day} ${hours}:${minutes}`;
    }
    return `${year}-${month}-${day}`;
  } catch (e) { return dateStr; }
}
function formatTimeAgo(dateStr) {
    if (!dateStr) return '';
    try {
        const date = new Date(dateStr);
        if (isNaN(date.getTime())) return '无效日期';
        return formatDistanceToNow(date, { addSuffix: true, locale: zhCN });
    } catch (e) { return '无效日期'; }
}
function formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    if (!bytes || isNaN(bytes) || bytes < 0) return 'N/A';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
}
function checkOverdue(taskData) {
  return taskData.endDate && taskData.status !== 'completed' && new Date(taskData.endDate) < new Date();
}

// --- Lifecycle ---
onMounted(() => {
  fetchTaskDetails();
});

</script>

<style scoped>
/* Styles remain largely the same, ensure consistency */
.page-container { padding: 20px; }
.mb-4 { margin-bottom: 20px; }
.page-header { display: flex; justify-content: space-between; align-items: center; }
.header-left { display: flex; align-items: center; gap: 15px; }
.back-button { color: var(--text-secondary-color); }
.back-button:hover { color: var(--primary-color); }
.page-title { font-size: 1.6rem; font-weight: 600; color: var(--header-bg); margin: 0; }
.header-right { display: flex; gap: 10px; }
.details-card, .attachments-card, .activity-card { border: 1px solid var(--border-color-light); background-color: var(--card-bg); border-radius: 8px; }
.card-header { font-weight: 600; color: var(--header-bg); font-size: 1.1rem; display: flex; align-items: center; gap: 8px; }
.info-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px 25px; }
.info-item { display: flex; flex-direction: column; gap: 4px; }
.item-label { font-size: 0.85rem; color: var(--text-secondary-color); }
.item-value { font-size: 0.95rem; color: var(--text-color); display: flex; align-items: center; gap: 6px; }
.item-value .el-tag, .item-value .el-select { font-size: 0.9rem; }
.item-value.assignee { gap: 8px; }
.item-value.overdue { color: var(--danger-color); font-weight: bold; display: inline-flex; align-items: center; gap: 4px; }
.item-value.overdue .el-icon { font-size: 1em; }
.small-avatar { width: 24px; height: 24px; font-size: 0.7rem; background-color: #eee; }
.description-section h4 { font-size: 1.05rem; font-weight: 600; color: var(--header-bg); margin-bottom: 10px; display: flex; align-items: center; gap: 6px; }
.description-content { font-size: 0.95rem; line-height: 1.7; color: var(--text-color); white-space: pre-wrap; }
.attachments-card .upload-area { margin-bottom: 15px; }
.attachments-list { margin-top: 15px; }
.attachment-item { display: flex; align-items: center; gap: 8px; padding: 8px 5px; border-bottom: 1px solid var(--border-color-light); font-size: 0.9rem; }
.attachment-item:last-child { border-bottom: none; }
.attachment-item .el-icon { color: var(--text-secondary-color); }
.attachment-name { flex-grow: 1; color: var(--text-color); }
.attachment-size { font-size: 0.8rem; color: var(--text-secondary-color); margin-left: 10px; }
.attachment-actions { margin-left: auto; }
.activity-card :deep(.el-tabs__header) { margin-bottom: 0; }
.activity-card :deep(.el-tabs__content) { padding: 0; height: calc(100vh - 350px); display: flex; flex-direction: column; }
.comment-section, .activity-logs { padding: 15px; flex-grow: 1; display: flex; flex-direction: column; overflow: hidden; }
.comments-list, .activity-logs { flex-grow: 1; overflow-y: auto; margin-bottom: 15px; }
.thin-scrollbar::-webkit-scrollbar { width: 5px; }
.thin-scrollbar::-webkit-scrollbar-track { background: transparent; }
.thin-scrollbar::-webkit-scrollbar-thumb { background-color: #ccc; border-radius: 3px; }
.thin-scrollbar::-webkit-scrollbar-thumb:hover { background-color: #aaa; }
.comment-item, .activity-item { display: flex; gap: 12px; margin-bottom: 18px; padding-bottom: 10px; border-bottom: 1px solid var(--border-color-light); }
.comment-item:last-child, .activity-item:last-child { margin-bottom: 0; border-bottom: none; }
.comment-content, .activity-content { flex-grow: 1; }
.comment-header, .activity-text { display: flex; justify-content: space-between; align-items: baseline; margin-bottom: 5px; flex-wrap: wrap; }
.comment-user, .activity-user { font-weight: 600; color: var(--text-color); font-size: 0.9rem; margin-right: 8px; }
.activity-text > span:not(.activity-user) { font-size: 0.9rem; color: var(--text-color); }
.comment-time, .activity-time { font-size: 0.75rem; color: var(--text-secondary-color); white-space: nowrap; }
.comment-text { font-size: 0.9rem; line-height: 1.6; color: var(--text-secondary-color); white-space: pre-wrap; }
.comment-form { margin-top: auto; padding-top: 15px; border-top: 1px solid var(--border-color-light); }
.submit-comment-btn { margin-top: 10px; float: right; }
.task-not-found { display: flex; justify-content: center; align-items: center; height: calc(100vh - 100px); }
/* Dark theme adjustments */
.dark-theme .back-button { color: var(--dark-text-secondary-color); }
.dark-theme .back-button:hover { color: var(--primary-color); }
.dark-theme .attachment-item { border-bottom-color: var(--dark-border-color); }
.dark-theme .comment-item, .dark-theme .activity-item { border-bottom-color: var(--dark-border-color); }
.dark-theme .comment-form { border-top-color: var(--dark-border-color); }
.dark-theme .small-avatar { background-color: #4a5568; }
</style>
```

**5. Review Other Files (Optional but Recommended):**

*   **`frontend/src/views/dashboard/index.vue`:** If it displays task counts or lists, ensure it calls the V2 task API (or a dedicated dashboard API that uses V2 data) and uses the correct status mapping.
*   **`frontend/src/stores/modules/gamification.js`:** Review any `recordEvent` calls related to tasks (e.g., `task_completed`). Ensure the event data sent (if any) is still relevant or update it. The calls to V1 task API endpoints within this store should be removed or replaced with calls to dedicated gamification endpoints if they exist.
*   **`frontend/src/components/ActivityTicker.vue`:** Ensure the `handleItemClick` function correctly navigates to the V2 task detail route (`/main/tasks/detail/:id`).
*   **`frontend/src/router/routes.js`:** Confirm that the routes for tasks (`/main/tasks/list`, `/main/tasks/kanban`, `/main/tasks/detail/:id`) are correctly defined and point to the refactored views.
*   **`frontend/src/layouts/DefaultLayout.vue`:** Update the hardcoded menu items related to tasks if their paths or names changed significantly (e.g., if `/main/tasks/board` was removed or renamed).

---

**Summary of Changes:**

1.  **API Module (`task.js`):** Updated endpoints to `/api/v2/tasks/...`, adjusted HTTP methods (PATCH), mapped parameters, and removed/commented V1/gamification functions.
2.  **Task List View (`TaskListView.vue`):** Updated API calls (`getTaskList`, `createTask`, `updateTask`, `deleteTask`), adjusted response handling, verified filter parameters, updated status/priority display, added periodic task fields.
3.  **Kanban View (`KanbanView.vue`):** Updated API calls (`getTaskList`, `updateTaskStatus`), refined status mapping logic for V2, and verified drag-and-drop update calls.
4.  **Task Detail View (`TaskDetailView.vue`):** Updated API calls (`getTaskDetail`, `updateTaskStatus`, `addComment`, `uploadTaskAttachment`, `deleteTaskAttachment`, `deleteTask`, `getComments`, `getTaskActivityLog`), adjusted status handling, and ensured data display matches V2 object structure.

This refactoring aligns the core task management functionality of the frontend with the specified V2 backend API. Remember to test thoroughly after applying these changes.

好的，没问题！让前端任务模块变得更“酷炫”可以从 UI 设计、交互动画、用户体验流程等多个方面入手。下面是一些具体的建议，你可以根据项目的实际情况和技术栈选择性地实施：

**一、 UI/UX 视觉增强**

1.  **现代化的卡片设计 (看板和列表项):**
    *   **毛玻璃效果 (Glassmorphism):** 给任务卡片或看板列添加半透明的背景和模糊效果，增加层次感。（需要注意浏览器兼容性和性能）
    *   **阴影和圆角:** 使用更柔和、更自然的阴影 (`box-shadow`) 和适度的圆角 (`border-radius`)，让界面元素看起来更精致。
    *   **色彩和渐变:** 引入主题色或根据任务优先级/类型使用渐变色背景或边框，使其更具视觉区分度。
    *   **悬浮效果 (Hover):** 添加更明显的悬浮效果，比如轻微放大 (`transform: scale(1.02)`), 阴影加深或边框高亮。

2.  **数据可视化:**
    *   **进度条:** 在任务列表和卡片上使用更美观的进度条 (Element Plus 的 `el-progress` 可以定制颜色，或使用自定义 SVG 进度条)。
    *   **仪表盘图表:** 引入 ECharts、Chart.js 等库，在仪表盘或个人中心展示任务完成率、类型分布等统计图表，使用动态效果。
    *   **日历视图:** 提供一个日历视图来展示任务的截止日期和分布。

3.  **图标和微交互:**
    *   **动态图标:** 使用 Lottie 或 SVG 动画图标，为加载、成功、失败等状态增加趣味性。
    *   **按钮反馈:** 按钮点击时加入轻微的缩放或颜色变化反馈。
    *   **空状态优化:** 使用更生动有趣的插画或动画替代单调的文字提示（Element Plus 的 `el-empty` 支持自定义图片）。

4.  **个性化和主题:**
    *   **主题切换:** (你似乎已有此功能) 允许用户选择不同的颜色主题或明暗模式，并确保切换过程平滑。
    *   **布局调整:** 允许用户自定义看板列的宽度或列表的显示列。

**二、 交互与动画**

1.  **列表加载与过滤:**
    *   **骨架屏 (Skeleton Loading):** 在加载数据时显示骨架屏，比简单的 `v-loading` 体验更好。Element Plus 提供 `el-skeleton` 组件。
    *   **列表项动画:** 使用 `<TransitionGroup>` 对列表项的进入、离开、移动添加动画（如渐入、滑动、交错效果 Staggering）。
    *   **过滤动画:** 筛选条件变化时，结果列表平滑过滤而不是瞬间跳变。

2.  **看板拖拽 (KanbanView):**
    *   **拖拽视觉反馈:** 拖动卡片时，卡片本身可以有轻微倾斜、放大或半透明效果。目标列可以有更明显的背景高亮或边框提示。
    *   **平滑滚动:** 当拖动卡片到列边缘时，自动平滑滚动列内容。
    *   **放置动画:** 卡片放置到新列时，有一个平滑的“落位”动画。

3.  **弹窗与通知:**
    *   **模态框动画:** 使用更流畅的弹窗打开/关闭动画（Element Plus Dialog 支持）。
    *   **动态通知:** 使用 Element Plus 的 `ElNotification` 或自定义 Toast 组件，添加图标和动画，使其更醒目。成就解锁、任务完成等可以使用带有特殊样式的通知。

4.  **状态切换:**
    *   **即时反馈:** 在列表或看板上直接更改任务状态（如点击完成按钮），状态标签应立即变化并伴随轻微动画（如颜色渐变、图标变化）。

**三、 功能与流程优化**

1.  **快速操作:**
    *   **列表项快捷操作:** 在任务列表项悬浮时显示常用操作按钮（如：完成、编辑、分配、设置截止日期）。
    *   **看板快速添加:** 在每个看板列底部添加一个“快速添加任务”的输入框。

2.  **实时更新 (需要后端配合):**
    *   **WebSockets/SSE:** 使用这些技术实现任务列表、看板的实时更新，当其他用户创建、更新或评论任务时，界面能自动刷新，无需手动操作。

3.  **评论与@提及:**
    *   **富文本编辑器:** 评论区使用简单的富文本编辑器（如 TipTap、Quill.js 的轻量级版本），支持基本的格式化和 @提及用户。
    *   **@提及高亮与链接:** 实现 @ 用户时弹出用户选择列表，评论中高亮显示被提及的用户，并可能链接到其个人资料。

4.  **键盘快捷键:**
    *   为常用操作添加键盘快捷键，如 `N` 新建任务，`F` 搜索，`Esc` 关闭弹窗等。

**四、 游戏化深度融合 (利用 `gamificationStore`)**

1.  **视觉反馈:**
    *   **积分/经验动画:** 完成任务、签到等获得积分/经验时，显示一个向上飘动的 `+X` 数字动画。
    *   **等级提升动画:** 用户升级时，显示一个醒目的祝贺动画或弹窗。
    *   **成就解锁:** 成就达成时，使用专门设计的 Toast 通知或弹窗，展示成就徽章和名称。
    *   **道具使用效果:** 使用道具时，可以有一个简单的视觉效果（如卡片闪烁、图标飞入等）。

2.  **排行榜:**
    *   **动态排名变化:** 如果是实时排行榜，用户排名上升或下降时有动画提示。
    *   **用户头像 Tooltip:** 鼠标悬浮在排行榜用户头像上时，显示更详细的 Mini Profile 卡片。

3.  **任务与游戏化关联:**
    *   在任务详情中清晰展示该任务可获得的积分、经验值或可能掉落的道具。
    *   在个人中心或仪表盘展示游戏化相关的统计图表（如积分获取趋势、成就完成度等）。

**实施建议:**

*   **循序渐进:** 不要一次性尝试所有改动，选择 1-2 个影响较大或实现相对容易的点开始。
*   **性能优先:** 动画和视觉效果虽然酷炫，但要时刻关注性能，避免过度动画导致卡顿。使用 CSS 动画优于 JS 动画，利用 `transform` 和 `opacity` 进行动画。
*   **一致性:** 保持整体 UI 风格和交互逻辑的一致性。
*   **用户反馈:** 如果可能，获取真实用户的反馈，了解哪些“酷炫”的功能真正提升了体验。
*   **库的选择:** 对于复杂动画，可以考虑引入轻量级动画库如 `anime.js` 或 `GSAP`，但要评估其对项目体积的影响。对于拖拽，`vuedraggable` 已经集成。

选择哪些建议取决于你的项目目标、时间和技术能力。可以先从优化 Element Plus 组件样式、添加基础的 CSS 过渡和动画、引入骨架屏等开始。