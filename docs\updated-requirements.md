# 航空航天级IT资产管理系统需求规格说明书

## 1. 项目背景与目标

### 1.1 项目背景

企业IT资产管理面临日益复杂的挑战，包括资产多样化、分布广泛、生命周期管理复杂、责任划分不明确等问题。随着企业规模扩大和信息化程度提高，传统的IT资产管理方式已无法满足高效、精确、可靠的管理需求。

### 1.2 项目目标

开发一个全面的企业级IT资产管理系统，实现IT资产全生命周期管理，符合航空航天级高精确性与高可靠性标准。系统旨在：

- 提供从采购到报废的完整IT资产生命周期管理
- 实现多级位置跟踪，精确定位每一个IT资产
- 提供故障管理和数据分析功能
- 明确责任归属，提高管理效率
- 降低资产总体拥有成本
- 支持数据驱动决策
- 提供简洁高效的工作任务管理

## 2. 系统架构

### 2.1 架构理念

系统基于以下核心理念设计：

1. **UNIX哲学与组合原则**：继承"Do One Thing Well"思想，组合优于继承
2. **极简高效代码**：每行代码必须高精确、高可靠、高效能
3. **微内核+插件架构**：核心引擎约500行，业务功能作为独立插件（每个<200行）
4. **事件溯源机制**：完整记录资产生命周期，支持任意历史点重建

### 2.2 架构约束

1. **数据模型**：严格限制数据表数量（核心表22张左右）
2. **代码规模**：整系统不超过5,000行，核心引擎约500行，各插件<200行/个
3. **函数设计**：每个函数不超过25行，保持单一职责和清晰接口
4. **表达风格**：声明式优于命令式，配置优于代码，约定优于配置

### 2.3 系统层次结构

系统采用三层架构设计：

```
+--------------------------------------------------+
|               表现层 (Presentation)               |
|  +----------------+   +------------------------+ |
|  | 前端Web应用    |   | 移动端自适应界面       | |
|  | Vue3 + Element |   | 响应式设计            | |
|  +----------------+   +------------------------+ |
+--------------------------------------------------+
|                  API层 (API)                     |
|  +-----------------+  +------------------------+ |
|  | REST API接口    |  | 身份认证与授权        | |
|  | ASP.NET Core    |  | JWT Token + RBAC      | |
|  +-----------------+  +------------------------+ |
+--------------------------------------------------+
|                 业务层 (Business)                |
|  +---------------+  +--------------+  +--------+ |
|  | 资产管理服务  |  | 故障管理服务 |  | 其他... | |
|  +---------------+  +--------------+  +--------+ |
+--------------------------------------------------+
|                 数据层 (Data)                    |
|  +---------------+  +-------------+  +----------+|
|  | 数据访问      |  | 缓存(可选)  |  | 日志     ||
|  | EF Core+Dapper|  |             |  | Serilog  ||
|  +---------------+  +-------------+  +----------+|
+--------------------------------------------------+
|                  存储层 (Storage)                |
|  +---------------+  +-------------+  +----------+|
|  | 关系型数据库  |  | 文件存储    |  | 备份     ||
|  | MySQL 8.0     |  | 服务器文件系统| | 定期备份 ||
|  +---------------+  +-------------+  +----------+|
+--------------------------------------------------+
```

**注意**:
- Redis缓存为可选组件，系统不强制依赖缓存层
- Docker容器化为可选部署方式，系统不强制要求容器化部署

## 3. 功能需求

### 3.1 资产管理模块

#### 3.1.1 资产基础管理
- 支持多种资产类型定义与管理
- 资产信息录入与编辑功能
- 资产状态管理（库存、使用中、维修中、报废等）
- 自定义属性配置
- 资产信息导出（Excel、PDF格式）

#### 3.1.2 资产位置管理
- 支持四/五级位置体系
- 资产位置变更记录与历史追溯
- 位置变更审批流程
- 批量位置变更功能

#### 3.1.3 资产编号与识别
- 基于规则自动生成资产编号
- 支持生成和打印资产标签（二维码/条形码）
- 扫码快速定位资产信息
- 资产标签模板自定义

#### 3.1.4 资产查询与筛选
- 多条件组合查询
- 高级搜索功能
- 个性化查询视图保存
- 资产报表生成
- 通过API直接查询资产清单并显示在前端页面

### 3.2 位置管理模块

#### 3.2.1 多级位置体系
- 支持四/五级位置体系：工厂-产线-工序-工位-[位置]
- 层级结构管理
- 位置启用/禁用状态控制
- 位置关联资产查看

#### 3.2.2 位置结构管理
- 位置创建与编辑
- 位置层级调整
- 位置编码规则配置
- 位置状态管理

#### 3.2.3 位置-部门-用户关联管理
- 位置与使用部门关联
- 位置与使用人关联
- 位置与部门负责人关联
- 关联关系历史记录

### 3.3 采购管理模块

#### 3.3.1 简化采购流程
- 采购单一键创建（精简流程，新建采购单即完成采购）
- 采购单包含批量生成的本批次资产编号、数量、名称类别
- 记录使用原因、经办人、负责人、预计到货时间
- 状态简化为：已到货、未到货
- 实际到货时间录入后采购订单自动完成闭环

#### 3.3.2 采购-资产流转
- 当采购单状态为已到货时，可直接转入库存
- 可从已到货采购单直接流转到资产清单
- 流转时填入必要的位置信息
- 批量流转功能

#### 3.3.3 采购执行与跟踪
- 采购单状态跟踪
- 到货提醒与逾期提醒
- 采购单查询与筛选
- 采购报表生成

#### 3.3.4 资产入库
- 整批或部分资产入库
- 入库资产详细信息录入
- 资产初始状态设置
- 资产编号自动生成与标签打印

### 3.4 故障管理模块

#### 3.4.1 故障报修
- 多渠道故障报修（Web、移动端、邮件）
- 故障分类与优先级设置
- 故障描述与附件上传
- 关联已有资产或手动输入

#### 3.4.2 故障处理
- 故障处理状态更新
- 处理过程记录
- 维修配件使用记录
- 维修时间统计

#### 3.4.3 返厂维修管理
- 返厂登记与跟踪
- 供应商信息管理
- 物流信息记录
- 返厂提醒

#### 3.4.4 故障分析
- 故障统计与趋势分析
- MTBF(平均故障间隔时间)和MTTR(平均修复时间)计算
- 故障高发设备识别
- 故障分析报表

### 3.5 工作任务管理模块

#### 3.5.1 每日任务清单
- 简单任务创建与管理（标题、描述、负责人、日期）
- 任务状态管理（未开始、进行中、已完成）
- 任务完成标记
- 任务筛选与排序
- 基本的增删改查功能

#### 3.5.2 周期性任务
- 简单周期设置（每日、每周、每月）
- 自动生成周期任务
- 周期任务执行确认
- 周期任务查看与管理

#### 3.5.3 PDCA行动计划
- 简化的PDCA管理（计划-执行-检查-行动）
- 任务类型分类
- 任务明细记录
- 计划开始与结束时间
- 完成状态跟踪
- 备注信息
- 简单的完成率统计

#### 3.5.4 与固定资产模块解耦
- 任务管理作为独立插件实现
- 与资产管理模块保持松耦合关系
- 可选择性关联资产或位置
- 独立的任务数据存储

### 3.6 数据分析与可视化模块

#### 3.6.1 资产分析
- 资产分布多维度分析
- 资产价值与折旧分析
- 资产使用率与效率分析
- 资产生命周期分析

#### 3.6.2 故障分析
- 故障频率与趋势分析
- 故障类型与原因分析
- 设备故障率分析
- 维修效率评估

#### 3.6.3 采购分析
- 采购支出趋势分析
- 部门、资产类型、供应商维度分析
- 预算执行情况分析
- 采购周期与效率分析

#### 3.6.4 可视化仪表盘
- 关键指标集成展示
- 仪表盘自定义
- 交互式数据钻取
- 关键指标预警

### 3.7 系统管理模块

#### 3.7.1 用户管理
- 用户账号创建与管理
- 用户与部门关联
- 用户权限设置
- 用户状态控制

#### 3.7.2 角色与权限管理
- 系统角色定义
- 细粒度权限控制
- 权限继承与覆盖
- 数据权限控制

#### 3.7.3 菜单管理
- 系统菜单结构通过JSON配置管理
- 菜单权限配置
- 菜单显示控制
- 自定义菜单排序

#### 3.7.4 系统参数配置
- 基本系统参数设置
- 编号规则配置
- 通知规则设置
- 系统行为配置

#### 3.7.5 数据备份与恢复
- 手动与自动定期备份
- 备份历史管理
- 数据恢复功能
- 备份策略配置

#### 3.7.6 审计日志
- 系统操作日志记录
- 数据变更记录
- 日志查询与筛选
- 日志导出

### 3.8 数据导入导出功能

#### 3.8.1 数据导入
- 标准化Excel导入模板
- 导入数据验证与预览
- 批量导入与更新
- 导入错误处理

#### 3.8.2 数据导出
- 多格式导出（Excel、CSV、PDF）
- 自定义导出字段
- 导出范围控制
- 导出统计信息

## 4. 非功能需求

### 4.1 性能需求
- 页面加载时间 < 3秒
- 数据查询响应时间 < 1秒
- 报表生成时间 < 5秒
- 支持100名并发用户（峰值200名）
- 支持管理至少100,000条资产记录
- 批量操作性能优化（1000条记录处理时间 < 10秒）

### 4.2 安全需求
- 支持账号密码登录与多因素认证
- 密码复杂度控制与定期修改
- 敏感数据加密存储
- 传输加密（HTTPS）
- 基于角色的权限管理(RBAC)
- 细粒度操作权限控制
- 数据权限隔离
- 详细审计日志

### 4.3 兼容性需求
- 浏览器兼容：Chrome 70+, Firefox 60+, Edge 80+, Safari 13+
- 设备兼容：台式机/笔记本/平板/移动设备
- 响应式设计，适应不同屏幕尺寸
- 服务器兼容：Windows Server 2016+, Ubuntu 18.04+, CentOS 7+

### 4.4 用户体验要求
- 简洁直观的用户界面
- 一致的视觉风格和交互模式
- 关键操作不超过3次点击
- 上下文帮助和提示
- 友好的错误处理
- 符合WCAG 2.1 AA级无障碍标准

### 4.5 技术特性
- 韧性设计（断网操作队列、优雅降级、冗余容错）
- JSON驱动UI（通过声明式配置生成复杂界面）
- 领域特定语言（非技术人员的自然语言操作界面）
- REPR模式API（每个端点一个文件，清晰可维护）
- 零信任安全模型（细粒度权限、异常检测、最小特权原则）

## 5. 位置-部门-用户关联模型

### 5.1 三维关联模型设计

每个位置关联三个关键要素：
1. **使用部门**：负责该位置的部门单位
2. **使用人**：直接使用/操作该位置设备的人员
3. **部门负责人**：对该位置负有管理责任的主管人员

### 5.2 责任唯一性原则

每个位置（设备/操作点）必须明确关联：
- 一个使用部门
- 至少一个使用人
- 一个部门负责人

### 5.3 权限分离原则

- **使用人（Operator）**：
  - 直接操作使用设备的员工
  - 可以有多名使用人轮班使用同一位置的设备
  - 具有设备日常操作权限
  - 可以发起故障报修流程

- **部门负责人（Manager）**：
  - 对位置和设备的安全、效率负有管理责任
  - 不直接操作设备
  - 具有审批和管理权限
  - 负责处理故障上报和维修申请

### 5.4 位置层级继承原则

- 子位置可继承父位置的部门和负责人设置
- 可在任意层级修改默认设置，子位置将继承最近的祖先设置
- 直接在位置上设置的部门和负责人优先级最高

## 6. 系统集成

### 6.1 与企业系统集成
- 与ERP系统集成（同步组织架构和人员信息）
- 与财务系统集成（同步资产财务信息和折旧数据）
- 与工单系统集成（自动创建维修工单）
- 与域账户系统集成（Active Directory单点登录）

### 6.2 通知与提醒
- 邮件通知（故障申报、审批提醒、保修到期提醒）
- 短信通知（紧急故障通知、重要审批提醒）
- 系统内通知（待办事项、状态变更、任务分配）

### 6.3 API集成
- 提供RESTful API接口
- API文档（Swagger/OpenAPI）
- API认证与授权
- 数据格式标准化

## 7. 技术实现方案

### 7.1 技术栈选择
- **后端技术**：
  - ASP.NET Core 6.0
  - RESTful API
  - Entity Framework Core + Dapper
  - JWT令牌认证
  - 可选缓存组件
  - Serilog日志

- **前端技术**：
  - Vue 3 + Composition API
  - Element Plus UI组件库
  - Pinia状态管理
  - Axios HTTP客户端
  - ECharts图表库
  - SCSS样式预处理器

- **数据库**：
  - MySQL 8.0
  - 索引优化
  - 查询优化

- **部署方案**：
  - 传统部署或容器化部署（Docker为可选）
  - Web服务器（Nginx或IIS）
  - SSL证书加密

### 7.2 数据库设计概览

**核心数据表结构**：

系统共有24个主要数据表，核心表结构包括：

1. **资产管理相关表**：
   - `assets` - 资产信息表
   - `assettypes` - 资产类型表
   - `assetreceives` - 资产入库记录表
   - `locationhistories` - 位置历史表

2. **位置管理相关表**：
   - `locations` - 位置信息表（支持四/五级层级结构）
   - `locationusers` - 位置与用户关联表
   - `location_users` - 位置与用户关联表（扩展）

3. **组织架构相关表**：
   - `departments` - 部门表
   - `users` - 用户表
   - `userroles` - 用户角色关系表
   - `roles` - 角色表
   - `rolemenus` - 角色菜单权限表
   - `menus` - 菜单表

4. **故障管理相关表**：
   - `faultrecords` - 故障记录表
   - `faulttypes` - 故障类型表
   - `returntofactories` - 返厂记录表
   - `maintenanceorders` - 维修工单表

5. **采购管理相关表**：
   - `purchaseorders` - 采购订单表
   - `purchaseitems` - 采购明细表
   - `suppliers` - 供应商表

6. **任务管理相关表**：
   - `tasks` - 通用任务表
   - `periodicrules` - 周期规则表
   - `pdcaplans` - PDCA行动计划表

7. **系统管理相关表**：
   - `auditlogs` - 审计日志表
   - `refreshtokens` - 令牌刷新表

**核心数据关系**：
- 资产-位置关系: assets.LocationId -> locations.Id
- 资产-类型关系: assets.AssetTypeId -> assettypes.Id
- 位置-部门关系: locations.DefaultDepartmentId -> departments.Id
- 位置-用户关系: locationusers.LocationId -> locations.Id, locationusers.UserId -> users.Id
- 故障-资产关系: faultrecords.AssetId -> assets.Id
- 任务-用户关系: tasks.AssignedToId -> users.Id
- 周期任务关系: tasks.PeriodicRuleId -> periodicrules.Id (可空)
- PDCA计划关系: tasks.PdcaPlanId -> pdcaplans.Id (可空)

**位置层级结构**:
- 工厂（Factory）- Type=0
- 产线（Line）- Type=1
- 工序（Process）- Type=1/2
- 工位（Workstation）- Type=2
- 位置（Position）- Type=2

**位置层级配置方式**:
- 位置结构通过JSON配置文件定义基础模板
- 运行时从配置中加载位置层级定义
- 支持通过界面修改并保存到配置文件
- 配置变更记录到版本控制系统

**任务表设计**:
- `tasks` 表设计:
  ```
  Id: 主键
  Title: 任务标题
  Description: 任务描述
  TaskType: 任务类型 (日常、周期、PDCA)
  Status: 状态 (未开始、进行中、已完成)
  AssignedToId: 分配给谁 (关联users表)
  CreatedById: 创建人 (关联users表)
  CreatedAt: 创建时间
  DueDate: 截止日期
  CompletedAt: 完成时间
  RelatedAssetId: 关联资产 (可空，关联assets表)
  RelatedLocationId: 关联位置 (可空，关联locations表)
  PeriodicRuleId: 周期规则ID (可空，关联periodicrules表)
  PdcaPlanId: PDCA计划ID (可空，关联pdcaplans表)
  Notes: 备注
  ```

- `periodicrules` 表设计:
  ```
  Id: 主键
  Name: 规则名称
  Frequency: 频率类型 (每日、每周、每月)
  DayOfWeek: 每周几 (可空，适用于周频率)
  DayOfMonth: 每月几号 (可空，适用于月频率)
  IsActive: 是否激活
  CreatedAt: 创建时间
  LastGeneratedAt: 上次生成任务时间
  ```

- `pdcaplans` 表设计:
  ```
  Id: 主键
  Title: 计划标题
  Type: 计划类型 (改善类、预防类、纠正类等)
  PlanStartDate: 计划开始时间
  PlanEndDate: 计划结束时间
  ActualEndDate: 实际结束时间
  Status: 当前阶段 (P计划、D执行、C检查、A行动)
  CompletionRate: 完成率
  ResponsiblePersonId: 责任人 (关联users表)
  Notes: 备注
  ```

## 8. 实施计划

### 8.1 开发规范与约定
- **代码规范**：
  - 后端：C#编码规范
  - 前端：Vue风格指南
  - 数据库：命名规范和SQL编写标准

- **API设计规范**：
  - RESTful API设计原则
  - 统一的响应格式
  - 统一的错误处理

- **UI设计规范**：
  - 统一的色彩和字体
  - 一致的组件样式
  - 响应式设计原则

- **版本控制**：
  - Git版本管理
  - GitFlow工作流
  - 提交信息规范

### 8.2 交付物
- **软件交付物**：
  - 前后端源代码
  - 数据库脚本
  - 部署说明文档
  - 编译后程序

- **文档交付物**：
  - 用户手册和操作指南
  - 系统架构和API文档
  - 数据库设计文档
  - 部署和维护文档
  - 测试报告和验收文档

### 8.3 培训与支持
- **管理员培训**：系统管理和配置、用户权限管理、数据备份恢复
- **资产管理员培训**：资产管理、位置管理、采购管理、报表分析
- **维修人员培训**：故障处理流程、返厂管理、维修记录
- **普通用户培训**：基本操作、故障报修、资产查询、任务管理

- **上线支持**：上线前准备、上线过程技术支持、问题快速响应
- **持续支持**：电话和邮件支持、远程技术支持、定期系统检查

### 8.4 后续发展规划
- **近期计划**：移动应用开发、数据分析增强、第三方系统集成
- **长期规划**：AI辅助决策、物联网集成、多租户支持

## 9. 术语表

| 术语 | 定义 |
|------|------|
| 位置（Location） | 物理设备放置点或操作点，是资产清单和产线工序工位的交汇点 |
| 使用部门（Department） | 负责位置的组织单元 |
| 使用人（Operator） | 直接使用/操作设备的人员 |
| 部门负责人（Manager） | 对位置和设备负有管理责任的人员 |
| 位置层级（Location Hierarchy） | 工厂-产线-工序-工位-位置的多级结构 |
| 资产关联（Asset Association） | 资产与位置/部门/用户的绑定关系 |
| 责任追溯（Responsibility Tracking） | 确定资产责任归属的过程 |
| MTBF（Mean Time Between Failures） | 平均故障间隔时间，评估设备可靠性指标 |
| MTTR（Mean Time To Repair） | 平均修复时间，评估维修效率指标 |
| 事件溯源（Event Sourcing） | 完整记录所有状态变更事件，支持任意历史点重建 |
| PDCA（Plan-Do-Check-Act） | 计划-执行-检查-行动循环，持续改进方法论 |

## 10. 数据库结构实现

系统已完成的数据库结构设计完善，能够支持IT资产管理系统的核心功能：

1. 实现了资产全生命周期管理流程：采购(purchaseorders) -> 入库(assetreceives) -> 使用(assets) -> 维修(faultrecords) -> 报废

2. 支持四/五级位置体系：通过locations表的层级结构Path字段和ParentId关系实现，可选配置为JSON格式

3. 建立了完善的位置-部门-用户三维关联关系：通过locations.DefaultDepartmentId、DefaultResponsiblePersonId和locationusers表实现

4. 提供了故障管理功能：faultrecords记录故障，returntofactories处理返厂维修

5. 完整的采购管理：purchaseorders和purchaseitems管理采购过程，支持简化流程和批量生成资产编号

6. 简洁高效的任务管理：
   - 通过单个tasks表存储所有类型的任务
   - 通过periodicrules表定义周期规则
   - 通过pdcaplans表管理PDCA行动计划
   - 支持任务完成率统计

7. 强大的数据分析基础：可通过表关系查询各种统计数据

8. 完整的审计系统：auditlogs记录系统操作

9. 支持通过API直接查询资产清单与任务信息

10. 无外部依赖：数据库结构不依赖Redis缓存组件，部署不强制要求Docker容器化

测试数据覆盖了系统各个方面，包括不同类型的资产、多级位置结构、用户角色以及各类任务，可以充分验证系统功能。

## 总结

航空航天级IT资产管理系统是一个全面的企业级解决方案，旨在实现IT资产全生命周期的精确管理与工作任务的高效管理。系统遵循极简高效的设计理念，采用微内核+插件架构，保证代码简洁高效。

系统采用简化的采购流程，用户创建采购单即完成采购过程，包含批量生成资产编号、数量、名称类别、使用原因、经办人、负责人和预计到货时间。当采购单状态变为已到货并录入实际到货时间后，采购订单自动完成闭环，且可直接将资产流转到资产清单中。

新增的工作任务管理模块保持简洁高效，提供每日任务、周期性任务和PDCA行动计划的基础管理功能。该模块与固定资产模块保持松耦合关系，可作为独立插件实现，同时支持简单的任务完成率统计。

位置层级配置采用JSON格式而非直接读取数据库，更符合系统设计理念，提高了配置灵活性。系统的数据模型保持精简，仅增加了3个任务相关表，符合严格控制数据表数量的架构约束。

系统的整体设计注重实用性和简洁性，确保AI能够实现并便于后期协作，为企业提供一套高效、可靠的IT资产与工作任务管理解决方案。
