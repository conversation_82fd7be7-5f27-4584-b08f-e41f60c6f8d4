import request from '@/utils/request'

// 获取支持的导入格式
export const getSupportedFormats = () => {
  return request.get('/import/formats')
}

// 获取导入模板
export const getImportTemplate = (entityType, format) => {
  return request.download(`/import/template/${entityType}/${format}`, null, `导入模板_${entityType}_${format}.xlsx`)
}

// 上传导入文件
export const uploadImportFile = (entityType, file) => {
  const formData = new FormData()
  formData.append('file', file)
  return request.post(`/import/upload/${entityType}`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    onUploadProgress: (progressEvent) => {
      const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
      return progress
    }
  })
}

// 获取导入结果
export const getImportResult = (importId) => {
  return request.get(`/import/result/${importId}`)
}