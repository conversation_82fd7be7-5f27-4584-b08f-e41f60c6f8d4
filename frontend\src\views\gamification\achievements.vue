<template>
  <div class="achievements-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1>🏆 成就系统</h1>
      <p>查看和管理用户成就，激励团队成员积极参与</p>
    </div>

    <!-- 成就统计卡片 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon">🎯</div>
            <div class="stat-info">
              <div class="stat-value">{{ achievementStats.totalAchievements }}</div>
              <div class="stat-label">总成就数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon">👥</div>
            <div class="stat-info">
              <div class="stat-value">{{ achievementStats.activeUsers }}</div>
              <div class="stat-label">活跃用户</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon">⭐</div>
            <div class="stat-info">
              <div class="stat-value">{{ achievementStats.completedToday }}</div>
              <div class="stat-label">今日完成</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon">🔥</div>
            <div class="stat-info">
              <div class="stat-value">{{ achievementStats.completionRate }}%</div>
              <div class="stat-label">完成率</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 主要内容 -->
    <el-card class="main-content">
      <el-tabs v-model="activeTab" type="border-card">
        <!-- 成就列表 -->
        <el-tab-pane label="成就列表" name="list">
          <div class="achievements-header">
            <el-button type="primary" @click="showAddAchievementDialog">
              <el-icon><Plus /></el-icon>
              添加成就
            </el-button>
            <div class="filter-controls">
              <el-select v-model="categoryFilter" placeholder="选择分类" clearable>
                <el-option label="全部分类" value="" />
                <el-option label="任务成就" value="Task" />
                <el-option label="质量成就" value="Quality" />
                <el-option label="创新成就" value="Innovation" />
                <el-option label="协作成就" value="Collaboration" />
              </el-select>
              <el-select v-model="difficultyFilter" placeholder="选择难度" clearable>
                <el-option label="全部难度" value="" />
                <el-option label="简单" value="Easy" />
                <el-option label="普通" value="Normal" />
                <el-option label="困难" value="Hard" />
                <el-option label="史诗" value="Epic" />
              </el-select>
            </div>
          </div>

          <div class="achievements-grid">
            <div
              v-for="achievement in filteredAchievements"
              :key="achievement.id"
              class="achievement-card"
              :class="{ 'achievement-inactive': !achievement.isActive }"
            >
              <div class="achievement-icon">
                <img v-if="achievement.iconUrl" :src="achievement.iconUrl" alt="成就图标" />
                <div v-else class="default-icon">🏆</div>
              </div>
              <div class="achievement-info">
                <h3 class="achievement-name">{{ achievement.name }}</h3>
                <p class="achievement-description">{{ achievement.description }}</p>
                <div class="achievement-meta">
                  <el-tag :type="getDifficultyType(achievement.difficulty)" size="small">
                    {{ achievement.difficulty }}
                  </el-tag>
                  <el-tag type="info" size="small">{{ achievement.category }}</el-tag>
                </div>
                <div class="achievement-rewards">
                  <span v-if="achievement.pointsReward" class="reward-item">
                    💰 {{ achievement.pointsReward }}积分
                  </span>
                  <span v-if="achievement.coinsReward" class="reward-item">
                    🪙 {{ achievement.coinsReward }}金币
                  </span>
                  <span v-if="achievement.diamondsReward" class="reward-item">
                    💎 {{ achievement.diamondsReward }}钻石
                  </span>
                </div>
                <div class="achievement-progress">
                  <span class="progress-text">完成次数: {{ achievement.completedCount || 0 }}</span>
                </div>
              </div>
              <div class="achievement-actions">
                <el-button size="small" @click="editAchievement(achievement)">编辑</el-button>
                <el-button size="small" @click="viewAchievementUsers(achievement)">查看用户</el-button>
                <el-button 
                  size="small" 
                  :type="achievement.isActive ? 'warning' : 'success'"
                  @click="toggleAchievementActive(achievement)"
                >
                  {{ achievement.isActive ? '禁用' : '启用' }}
                </el-button>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 用户成就 -->
        <el-tab-pane label="用户成就" name="users">
          <div class="user-achievements-section">
            <div class="search-controls">
              <el-input
                v-model="userSearch"
                placeholder="搜索用户"
                style="width: 300px"
                clearable
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
            </div>

            <el-table :data="userAchievements" style="width: 100%">
              <el-table-column prop="userName" label="用户名" width="120" />
              <el-table-column prop="department" label="部门" width="120" />
              <el-table-column prop="achievementName" label="成就名称" />
              <el-table-column prop="achievementCategory" label="成就分类" width="100" />
              <el-table-column prop="completedAt" label="完成时间" width="160" />
              <el-table-column prop="progress" label="进度" width="100">
                <template #default="scope">
                  {{ scope.row.progress }}/{{ scope.row.requiredCount }}
                </template>
              </el-table-column>
              <el-table-column label="状态" width="80">
                <template #default="scope">
                  <el-tag :type="scope.row.isCompleted ? 'success' : 'info'" size="small">
                    {{ scope.row.isCompleted ? '已完成' : '进行中' }}
                  </el-tag>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>

        <!-- 成就统计 -->
        <el-tab-pane label="成就统计" name="statistics">
          <div class="statistics-section">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-card header="成就分类统计">
                  <div ref="categoryChartRef" style="height: 300px;"></div>
                </el-card>
              </el-col>
              <el-col :span="12">
                <el-card header="完成趋势">
                  <div ref="trendChartRef" style="height: 300px;"></div>
                </el-card>
              </el-col>
            </el-row>

            <el-card header="热门成就排行" class="mt-20">
              <el-table :data="popularAchievements" style="width: 100%">
                <el-table-column type="index" label="排名" width="80" />
                <el-table-column prop="name" label="成就名称" />
                <el-table-column prop="category" label="分类" width="100" />
                <el-table-column prop="completedCount" label="完成人数" width="100" sortable />
                <el-table-column prop="completionRate" label="完成率" width="100">
                  <template #default="scope">
                    {{ scope.row.completionRate }}%
                  </template>
                </el-table-column>
              </el-table>
            </el-card>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 添加/编辑成就对话框 -->
    <el-dialog
      :title="achievementDialogTitle"
      v-model="achievementDialogVisible"
      width="600px"
    >
      <el-form :model="achievementForm" :rules="achievementFormRules" ref="achievementFormRef" label-width="120px">
        <el-form-item label="成就代码" prop="code">
          <el-input v-model="achievementForm.code" :disabled="isEditMode" />
        </el-form-item>
        <el-form-item label="成就名称" prop="name">
          <el-input v-model="achievementForm.name" />
        </el-form-item>
        <el-form-item label="成就描述" prop="description">
          <el-input v-model="achievementForm.description" type="textarea" rows="3" />
        </el-form-item>
        <el-form-item label="成就分类" prop="category">
          <el-select v-model="achievementForm.category" style="width: 100%">
            <el-option label="任务成就" value="Task" />
            <el-option label="质量成就" value="Quality" />
            <el-option label="创新成就" value="Innovation" />
            <el-option label="协作成就" value="Collaboration" />
            <el-option label="其他" value="Other" />
          </el-select>
        </el-form-item>
        <el-form-item label="难度等级" prop="difficulty">
          <el-select v-model="achievementForm.difficulty" style="width: 100%">
            <el-option label="简单" value="Easy" />
            <el-option label="普通" value="Normal" />
            <el-option label="困难" value="Hard" />
            <el-option label="史诗" value="Epic" />
          </el-select>
        </el-form-item>
        <el-form-item label="所需次数" prop="requiredCount">
          <el-input-number v-model="achievementForm.requiredCount" :min="1" />
        </el-form-item>
        <el-form-item label="积分奖励" prop="pointsReward">
          <el-input-number v-model="achievementForm.pointsReward" :min="0" />
        </el-form-item>
        <el-form-item label="金币奖励" prop="coinsReward">
          <el-input-number v-model="achievementForm.coinsReward" :min="0" />
        </el-form-item>
        <el-form-item label="钻石奖励" prop="diamondsReward">
          <el-input-number v-model="achievementForm.diamondsReward" :min="0" />
        </el-form-item>
        <el-form-item label="图标URL">
          <el-input v-model="achievementForm.iconUrl" placeholder="成就图标URL" />
        </el-form-item>
        <el-form-item label="可重复获得">
          <el-switch v-model="achievementForm.isRepeatable" />
        </el-form-item>
        <el-form-item label="是否启用">
          <el-switch v-model="achievementForm.isActive" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="achievementDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveAchievement">保存</el-button>
      </template>
    </el-dialog>

    <!-- 用户成就详情对话框 -->
    <el-dialog
      title="成就获得用户"
      v-model="userAchievementDialogVisible"
      width="800px"
    >
      <el-table :data="selectedAchievementUsers" style="width: 100%">
        <el-table-column prop="userName" label="用户名" />
        <el-table-column prop="department" label="部门" />
        <el-table-column prop="completedAt" label="完成时间" />
        <el-table-column prop="completedCount" label="完成次数" />
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search } from '@element-plus/icons-vue'

export default {
  name: 'AchievementsManagement',
  components: {
    Plus,
    Search
  },
  setup() {
    const activeTab = ref('list')
    const categoryFilter = ref('')
    const difficultyFilter = ref('')
    const userSearch = ref('')

    // 成就统计数据
    const achievementStats = reactive({
      totalAchievements: 12,
      activeUsers: 25,
      completedToday: 8,
      completionRate: 65
    })

    // 成就列表
    const achievements = ref([
      {
        id: 1,
        code: 'FIRST_TASK',
        name: '初出茅庐',
        description: '完成第一个任务',
        category: 'Task',
        difficulty: 'Easy',
        requiredCount: 1,
        pointsReward: 10,
        coinsReward: 5,
        diamondsReward: 0,
        iconUrl: '',
        isRepeatable: false,
        isActive: true,
        completedCount: 25
      },
      {
        id: 2,
        code: 'TASK_MASTER_10',
        name: '任务达人',
        description: '累计完成10个任务',
        category: 'Task',
        difficulty: 'Normal',
        requiredCount: 10,
        pointsReward: 50,
        coinsReward: 25,
        diamondsReward: 1,
        iconUrl: '',
        isRepeatable: false,
        isActive: true,
        completedCount: 18
      },
      {
        id: 3,
        code: 'QUALITY_EXPERT',
        name: '质量专家',
        description: '连续完成5个高质量任务',
        category: 'Quality',
        difficulty: 'Hard',
        requiredCount: 5,
        pointsReward: 100,
        coinsReward: 50,
        diamondsReward: 3,
        iconUrl: '',
        isRepeatable: true,
        isActive: true,
        completedCount: 8
      },
      {
        id: 4,
        code: 'INNOVATION_PIONEER',
        name: '创新先锋',
        description: '提出创新解决方案',
        category: 'Innovation',
        difficulty: 'Epic',
        requiredCount: 1,
        pointsReward: 200,
        coinsReward: 100,
        diamondsReward: 5,
        iconUrl: '',
        isRepeatable: true,
        isActive: true,
        completedCount: 3
      }
    ])

    // 用户成就数据
    const userAchievements = ref([
      {
        id: 1,
        userName: '张三',
        department: '研发部',
        achievementName: '初出茅庐',
        achievementCategory: 'Task',
        progress: 1,
        requiredCount: 1,
        isCompleted: true,
        completedAt: '2024-01-15 10:30:00'
      },
      {
        id: 2,
        userName: '李四',
        department: '产品部',
        achievementName: '任务达人',
        achievementCategory: 'Task',
        progress: 8,
        requiredCount: 10,
        isCompleted: false,
        completedAt: null
      }
    ])

    // 热门成就
    const popularAchievements = ref([
      { name: '初出茅庐', category: 'Task', completedCount: 25, completionRate: 100 },
      { name: '任务达人', category: 'Task', completedCount: 18, completionRate: 72 },
      { name: '质量专家', category: 'Quality', completedCount: 8, completionRate: 32 },
      { name: '创新先锋', category: 'Innovation', completedCount: 3, completionRate: 12 }
    ])

    // 对话框状态
    const achievementDialogVisible = ref(false)
    const userAchievementDialogVisible = ref(false)
    const isEditMode = ref(false)
    const achievementFormRef = ref()
    const selectedAchievementUsers = ref([])

    // 成就表单
    const achievementForm = reactive({
      code: '',
      name: '',
      description: '',
      category: 'Task',
      difficulty: 'Normal',
      requiredCount: 1,
      pointsReward: 0,
      coinsReward: 0,
      diamondsReward: 0,
      iconUrl: '',
      isRepeatable: false,
      isActive: true
    })

    const achievementFormRules = {
      code: [{ required: true, message: '请输入成就代码', trigger: 'blur' }],
      name: [{ required: true, message: '请输入成就名称', trigger: 'blur' }],
      description: [{ required: true, message: '请输入成就描述', trigger: 'blur' }],
      category: [{ required: true, message: '请选择成就分类', trigger: 'change' }],
      difficulty: [{ required: true, message: '请选择难度等级', trigger: 'change' }],
      requiredCount: [{ required: true, message: '请输入所需次数', trigger: 'blur' }]
    }

    // 计算属性
    const filteredAchievements = computed(() => {
      return achievements.value.filter(achievement => {
        const categoryMatch = !categoryFilter.value || achievement.category === categoryFilter.value
        const difficultyMatch = !difficultyFilter.value || achievement.difficulty === difficultyFilter.value
        return categoryMatch && difficultyMatch
      })
    })

    const achievementDialogTitle = computed(() => {
      return isEditMode.value ? '编辑成就' : '添加成就'
    })

    // 方法
    const getDifficultyType = (difficulty) => {
      const types = {
        'Easy': 'success',
        'Normal': 'info',
        'Hard': 'warning',
        'Epic': 'danger'
      }
      return types[difficulty] || 'info'
    }

    const showAddAchievementDialog = () => {
      isEditMode.value = false
      Object.assign(achievementForm, {
        code: '',
        name: '',
        description: '',
        category: 'Task',
        difficulty: 'Normal',
        requiredCount: 1,
        pointsReward: 0,
        coinsReward: 0,
        diamondsReward: 0,
        iconUrl: '',
        isRepeatable: false,
        isActive: true
      })
      achievementDialogVisible.value = true
    }

    const editAchievement = (achievement) => {
      isEditMode.value = true
      Object.assign(achievementForm, achievement)
      achievementDialogVisible.value = true
    }

    const saveAchievement = async () => {
      try {
        await achievementFormRef.value.validate()

        if (isEditMode.value) {
          // 更新成就
          const index = achievements.value.findIndex(a => a.id === achievementForm.id)
          if (index !== -1) {
            achievements.value[index] = { ...achievementForm }
          }
          ElMessage.success('成就更新成功')
        } else {
          // 添加成就
          const newAchievement = {
            ...achievementForm,
            id: Date.now(),
            completedCount: 0
          }
          achievements.value.push(newAchievement)
          ElMessage.success('成就添加成功')
        }

        achievementDialogVisible.value = false
      } catch (error) {
        console.error('保存成就失败:', error)
        ElMessage.error('保存成就失败')
      }
    }

    const toggleAchievementActive = async (achievement) => {
      try {
        achievement.isActive = !achievement.isActive
        ElMessage.success(`成就已${achievement.isActive ? '启用' : '禁用'}`)
      } catch (error) {
        console.error('切换成就状态失败:', error)
        ElMessage.error('切换成就状态失败')
        achievement.isActive = !achievement.isActive // 回滚状态
      }
    }

    const viewAchievementUsers = (achievement) => {
      // 模拟获取成就用户数据
      selectedAchievementUsers.value = [
        {
          userName: '张三',
          department: '研发部',
          completedAt: '2024-01-15 10:30:00',
          completedCount: 1
        },
        {
          userName: '李四',
          department: '产品部',
          completedAt: '2024-01-16 14:20:00',
          completedCount: 1
        }
      ]
      userAchievementDialogVisible.value = true
    }

    // 加载成就数据
    const loadAchievements = async () => {
      try {
        // 这里需要调用后端API获取成就列表
        // 暂时使用模拟数据，等待后端API完善
        console.log('加载成就数据...')
      } catch (error) {
        console.error('加载成就数据失败:', error)
        ElMessage.error('加载成就数据失败')
      }
    }

    // 加载成就统计
    const loadAchievementStats = async () => {
      try {
        // 这里需要调用后端API获取成就统计
        console.log('加载成就统计...')
      } catch (error) {
        console.error('加载成就统计失败:', error)
        ElMessage.error('加载成就统计失败')
      }
    }

    // 初始化
    onMounted(() => {
      loadAchievements()
      loadAchievementStats()
    })

    return {
      activeTab,
      categoryFilter,
      difficultyFilter,
      userSearch,
      achievementStats,
      achievements,
      userAchievements,
      popularAchievements,
      achievementDialogVisible,
      userAchievementDialogVisible,
      isEditMode,
      achievementFormRef,
      selectedAchievementUsers,
      achievementForm,
      achievementFormRules,
      filteredAchievements,
      achievementDialogTitle,
      getDifficultyType,
      showAddAchievementDialog,
      editAchievement,
      saveAchievement,
      toggleAchievementActive,
      viewAchievementUsers
    }
  }
}
</script>

<style scoped>
.achievements-container {
  padding: 20px;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
}

.page-header h1 {
  font-size: 2.5em;
  margin-bottom: 10px;
  color: #409EFF;
}

.page-header p {
  font-size: 1.1em;
  color: #666;
}

.stats-cards {
  margin-bottom: 30px;
}

.stat-card {
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 15px;
}

.stat-icon {
  font-size: 3em;
}

.stat-info {
  text-align: left;
}

.stat-value {
  font-size: 2em;
  font-weight: bold;
  color: #409EFF;
}

.stat-label {
  color: #666;
  font-size: 0.9em;
}

.main-content {
  min-height: 600px;
}

.achievements-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.filter-controls {
  display: flex;
  gap: 10px;
}

.achievements-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
}

.achievement-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 20px;
  background: white;
  transition: all 0.3s ease;
  position: relative;
}

.achievement-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.achievement-card.achievement-inactive {
  opacity: 0.6;
  background: #f5f7fa;
}

.achievement-icon {
  text-align: center;
  margin-bottom: 15px;
}

.achievement-icon img {
  width: 60px;
  height: 60px;
  border-radius: 50%;
}

.default-icon {
  font-size: 3em;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  margin: 0 auto;
}

.achievement-info {
  text-align: center;
  margin-bottom: 15px;
}

.achievement-name {
  font-size: 1.2em;
  font-weight: bold;
  margin-bottom: 8px;
  color: #333;
}

.achievement-description {
  color: #666;
  font-size: 0.9em;
  margin-bottom: 10px;
  line-height: 1.4;
}

.achievement-meta {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin-bottom: 10px;
}

.achievement-rewards {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-bottom: 10px;
}

.reward-item {
  font-size: 0.8em;
  padding: 2px 8px;
  background: #f0f9ff;
  border-radius: 12px;
  color: #1890ff;
}

.achievement-progress {
  font-size: 0.8em;
  color: #666;
}

.achievement-actions {
  display: flex;
  justify-content: center;
  gap: 8px;
}

.search-controls {
  margin-bottom: 20px;
}

.statistics-section .mt-20 {
  margin-top: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .achievements-grid {
    grid-template-columns: 1fr;
  }

  .achievements-header {
    flex-direction: column;
    gap: 15px;
  }

  .filter-controls {
    width: 100%;
    justify-content: space-between;
  }

  .stats-cards .el-col {
    margin-bottom: 15px;
  }
}
</style>
