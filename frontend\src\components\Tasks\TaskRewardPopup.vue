<template>
  <el-dialog
    :title="reward.title"
    v-model="dialogVisible"
    width="400px"
    :show-close="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    center
    custom-class="reward-popup-dialog"
  >
    <reward-panel :reward="reward" @close="handleClose" />
  </el-dialog>
</template>

<script setup>
import { ref, watch } from 'vue'
import RewardPanel from './RewardPanel.vue'

const props = defineProps({
  reward: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['close'])

// 对话框显示状态
const dialogVisible = ref(true)

// 监听对话框状态变化
watch(dialogVisible, (newValue) => {
  if (!newValue) {
    emit('close')
  }
})

// 处理关闭
const handleClose = () => {
  dialogVisible.value = false
}
</script>

<style>
.reward-popup-dialog {
  border-radius: 10px;
  overflow: hidden;
  padding: 0 !important;
}

.reward-popup-dialog .el-dialog__header {
  display: none;
}

.reward-popup-dialog .el-dialog__body {
  padding: 0;
}

/* 动画效果 */
.reward-popup-dialog-enter-active,
.reward-popup-dialog-leave-active {
  transition: all 0.4s;
}

.reward-popup-dialog-enter-from,
.reward-popup-dialog-leave-to {
  opacity: 0;
  transform: scale(0.5);
}
</style> 