/**
 * 航空航天级IT资产管理系统 - 采购管理页面
 * 文件路径: src/views/purchases/index.vue
 * 功能描述: 采购管理模块的布局容器
 */

<template>
  <div class="purchase-container">
    <router-view v-slot="{ Component }">
      <keep-alive>
        <component :is="Component" />
      </keep-alive>
    </router-view>
  </div>
</template>

<script setup>
// 该组件仅作为采购管理子页面的容器
</script>

<style lang="scss" scoped>
@use "@/styles/variables.scss" as *;

.purchase-container {
  padding: 16px;
  height: 100%;
}
</style> 