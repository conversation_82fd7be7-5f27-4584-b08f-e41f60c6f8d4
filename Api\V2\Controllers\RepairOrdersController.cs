// File: Api/V2/Controllers/RepairOrdersController.cs
// Description: 维修单管理API控制器

using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using ItAssetsSystem.Application.Common.Dtos;
using ItAssetsSystem.Application.Features.RepairOrders.Dtos;
using ItAssetsSystem.Core.Services;
using ItAssetsSystem.Domain.Entities;
using ItAssetsSystem.Infrastructure.Data;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace ItAssetsSystem.Api.V2.Controllers
{
    /// <summary>
    /// 维修单管理API
    /// </summary>
    [ApiController]
    [Route("api/v2/repair-orders")]
    [Authorize]
    public class RepairOrdersController : ControllerBase
    {
        private readonly AppDbContext _dbContext;
        private readonly ILogger<RepairOrdersController> _logger;

        public RepairOrdersController(AppDbContext dbContext, ILogger<RepairOrdersController> logger)
        {
            _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        private int GetCurrentUserId()
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
            if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out var userId))
            {
                _logger.LogWarning("无法从JWT声明中获取用户ID。");
                throw new UnauthorizedAccessException("未能获取有效的用户ID。");
            }
            return userId;
        }

        /// <summary>
        /// 获取维修单列表（分页）
        /// </summary>
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<PaginatedResult<RepairOrderDto>>))]
        public async Task<IActionResult> GetRepairOrders([FromQuery] RepairOrderQueryParametersDto parameters)
        {
            try
            {
                var query = _dbContext.RepairOrders
                    .Include(r => r.Supplier)
                    .Include(r => r.Creator)
                    .Include(r => r.Approver)
                    .Include(r => r.RepairItems)
                        .ThenInclude(ri => ri.Asset)
                    .AsNoTracking();

                // 应用过滤条件
                if (!string.IsNullOrWhiteSpace(parameters.SearchTerm))
                {
                    query = query.Where(r => r.Title.Contains(parameters.SearchTerm) ||
                                           r.Description.Contains(parameters.SearchTerm) ||
                                           r.OrderCode.Contains(parameters.SearchTerm));
                }

                if (parameters.Status.HasValue)
                {
                    query = query.Where(r => r.Status == parameters.Status.Value);
                }

                if (parameters.Priority.HasValue)
                {
                    query = query.Where(r => r.Priority == parameters.Priority.Value);
                }

                if (parameters.SupplierId.HasValue)
                {
                    query = query.Where(r => r.SupplierId == parameters.SupplierId.Value);
                }

                if (parameters.CreatorId.HasValue)
                {
                    query = query.Where(r => r.CreatorId == parameters.CreatorId.Value);
                }

                if (parameters.StartDate.HasValue)
                {
                    query = query.Where(r => r.CreatedAt >= parameters.StartDate.Value);
                }

                if (parameters.EndDate.HasValue)
                {
                    query = query.Where(r => r.CreatedAt <= parameters.EndDate.Value);
                }

                // 计算总数
                var totalCount = await query.CountAsync();

                // 分页和排序
                var repairOrders = await query
                    .OrderByDescending(r => r.CreatedAt)
                    .Skip((parameters.PageNumber - 1) * parameters.PageSize)
                    .Take(parameters.PageSize)
                    .ToListAsync();

                var dtos = repairOrders.Select(MapToDto).ToList();

                var result = PaginatedResult<RepairOrderDto>.Create(
                    dtos,
                    totalCount,
                    parameters.PageNumber,
                    parameters.PageSize
                );

                return Ok(ApiResponseFactory.CreateSuccess(result));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取维修单列表时发生错误");
                return StatusCode(StatusCodes.Status500InternalServerError,
                    ApiResponseFactory.CreateFail<PaginatedResult<RepairOrderDto>>("获取维修单列表时发生服务器错误：" + ex.Message));
            }
        }

        /// <summary>
        /// 根据ID获取维修单详情
        /// </summary>
        [HttpGet("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<RepairOrderDto>))]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetRepairOrder(int id)
        {
            try
            {
                var repairOrder = await _dbContext.RepairOrders
                    .Include(r => r.Supplier)
                    .Include(r => r.Creator)
                    .Include(r => r.Approver)
                    .Include(r => r.RepairItems)
                        .ThenInclude(ri => ri.Asset)
                    .AsNoTracking()
                    .FirstOrDefaultAsync(r => r.Id == id);

                if (repairOrder == null)
                {
                    return NotFound(ApiResponseFactory.CreateFail<RepairOrderDto>("维修单未找到"));
                }

                var dto = MapToDto(repairOrder);
                return Ok(ApiResponseFactory.CreateSuccess(dto));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取维修单详情时发生错误，ID: {Id}", id);
                return StatusCode(StatusCodes.Status500InternalServerError,
                    ApiResponseFactory.CreateFail<RepairOrderDto>("获取维修单详情时发生服务器错误：" + ex.Message));
            }
        }

        /// <summary>
        /// 创建维修单
        /// </summary>
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status201Created, Type = typeof(ApiResponse<RepairOrderDto>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<IActionResult> CreateRepairOrder([FromBody] CreateRepairOrderRequestDto request)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ApiResponseFactory.CreateFail<RepairOrderDto>(
                    ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage)));
            }

            try
            {
                var currentUserId = GetCurrentUserId();

                // 生成维修单号
                var orderCode = await GenerateOrderCodeAsync();

                // 创建维修单
                var repairOrder = new RepairOrder
                {
                    OrderCode = orderCode,
                    Title = request.Title,
                    Description = request.Description,
                    Priority = (RepairPriority)request.Priority,
                    SupplierId = request.SupplierId,
                    FaultId = request.FaultId,
                    EstimatedCost = request.EstimatedCost,
                    Notes = request.Notes,
                    CreatorId = currentUserId,
                    Status = RepairOrderStatus.PendingApproval,
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now
                };

                // 计算预计返回日期
                if (request.EstimatedDays.HasValue && request.EstimatedDays > 0)
                {
                    repairOrder.ExpectedReturnDate = DateTime.Now.AddDays(request.EstimatedDays.Value);
                }

                _dbContext.RepairOrders.Add(repairOrder);
                await _dbContext.SaveChangesAsync();

                // 创建维修项目
                foreach (var itemDto in request.RepairItems)
                {
                    // 检查PartId是否是有效的资产ID
                    var asset = await _dbContext.Assets.FindAsync(itemDto.PartId);
                    if (asset == null)
                    {
                        _logger.LogWarning("创建维修单时，资产ID {AssetId} 不存在", itemDto.PartId);
                        continue; // 跳过无效的资产ID
                    }

                    var repairItem = new RepairItem
                    {
                        RepairOrderId = repairOrder.Id,
                        AssetId = itemDto.PartId, // PartId对应AssetId
                        Description = itemDto.FaultDescription,
                        RepairCost = 0, // 初始为0
                        RepairStatus = 1 // 待维修
                    };

                    _dbContext.RepairItems.Add(repairItem);
                }

                await _dbContext.SaveChangesAsync();

                // 重新查询包含关联数据的维修单
                var createdRepairOrder = await _dbContext.RepairOrders
                    .Include(r => r.Supplier)
                    .Include(r => r.Creator)
                    .Include(r => r.RepairItems)
                        .ThenInclude(ri => ri.Asset)
                    .FirstAsync(r => r.Id == repairOrder.Id);

                var dto = MapToDto(createdRepairOrder);

                return CreatedAtAction(nameof(GetRepairOrder), new { id = repairOrder.Id },
                    ApiResponseFactory.CreateSuccess(dto, "维修单创建成功"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建维修单时发生错误");
                return StatusCode(StatusCodes.Status500InternalServerError,
                    ApiResponseFactory.CreateFail<RepairOrderDto>("创建维修单时发生服务器错误：" + ex.Message));
            }
        }

        /// <summary>
        /// 更新维修单
        /// </summary>
        [HttpPut("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<RepairOrderDto>))]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> UpdateRepairOrder(int id, [FromBody] UpdateRepairOrderRequestDto request)
        {
            try
            {
                var repairOrder = await _dbContext.RepairOrders.FindAsync(id);
                if (repairOrder == null)
                {
                    return NotFound(ApiResponseFactory.CreateFail<RepairOrderDto>("维修单未找到"));
                }

                // 更新字段
                if (!string.IsNullOrEmpty(request.Title))
                    repairOrder.Title = request.Title;
                if (request.Description != null)
                    repairOrder.Description = request.Description;
                if (request.Priority.HasValue)
                    repairOrder.Priority = request.Priority.Value;
                if (request.SupplierId.HasValue)
                    repairOrder.SupplierId = request.SupplierId.Value;
                if (request.SendDate.HasValue)
                    repairOrder.SendDate = request.SendDate.Value;
                if (request.ExpectedReturnDate.HasValue)
                    repairOrder.ExpectedReturnDate = request.ExpectedReturnDate.Value;
                if (request.ActualReturnDate.HasValue)
                    repairOrder.ActualReturnDate = request.ActualReturnDate.Value;
                if (request.EstimatedCost.HasValue)
                    repairOrder.EstimatedCost = request.EstimatedCost.Value;
                if (request.TotalCost.HasValue)
                    repairOrder.TotalCost = request.TotalCost.Value;
                if (request.Status.HasValue)
                    repairOrder.Status = request.Status.Value;
                if (request.Notes != null)
                    repairOrder.Notes = request.Notes;
                if (request.RepairResult != null)
                    repairOrder.RepairResult = request.RepairResult;

                repairOrder.UpdatedAt = DateTime.Now;

                await _dbContext.SaveChangesAsync();

                // 重新查询包含关联数据的维修单
                var updatedRepairOrder = await _dbContext.RepairOrders
                    .Include(r => r.Supplier)
                    .Include(r => r.Creator)
                    .Include(r => r.Approver)
                    .Include(r => r.RepairItems)
                        .ThenInclude(ri => ri.Asset)
                    .FirstAsync(r => r.Id == id);

                var dto = MapToDto(updatedRepairOrder);

                return Ok(ApiResponseFactory.CreateSuccess(dto, "维修单更新成功"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新维修单时发生错误，ID: {Id}", id);
                return StatusCode(StatusCodes.Status500InternalServerError,
                    ApiResponseFactory.CreateFail<RepairOrderDto>("更新维修单时发生服务器错误：" + ex.Message));
            }
        }

        /// <summary>
        /// 删除维修单
        /// </summary>
        [HttpDelete("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<bool>))]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> DeleteRepairOrder(int id)
        {
            try
            {
                var repairOrder = await _dbContext.RepairOrders
                    .Include(r => r.RepairItems)
                    .FirstOrDefaultAsync(r => r.Id == id);

                if (repairOrder == null)
                {
                    return NotFound(ApiResponseFactory.CreateFail<bool>("维修单未找到"));
                }

                // 删除关联的维修项目
                _dbContext.RepairItems.RemoveRange(repairOrder.RepairItems);
                
                // 删除维修单
                _dbContext.RepairOrders.Remove(repairOrder);
                
                await _dbContext.SaveChangesAsync();

                return Ok(ApiResponseFactory.CreateSuccess(true, "维修单删除成功"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除维修单时发生错误，ID: {Id}", id);
                return StatusCode(StatusCodes.Status500InternalServerError,
                    ApiResponseFactory.CreateFail<bool>("删除维修单时发生服务器错误：" + ex.Message));
            }
        }

        #region 私有方法

        /// <summary>
        /// 生成维修单号
        /// </summary>
        private async Task<string> GenerateOrderCodeAsync()
        {
            var today = DateTime.Today;
            var prefix = $"RO{today:yyyyMMdd}";
            
            var maxCode = await _dbContext.RepairOrders
                .Where(r => r.OrderCode.StartsWith(prefix))
                .Select(r => r.OrderCode)
                .OrderByDescending(c => c)
                .FirstOrDefaultAsync();

            int nextNumber = 1;
            if (!string.IsNullOrEmpty(maxCode))
            {
                var numberPart = maxCode.Substring(prefix.Length);
                if (int.TryParse(numberPart, out int currentNumber))
                {
                    nextNumber = currentNumber + 1;
                }
            }

            return $"{prefix}{nextNumber:D3}";
        }

        /// <summary>
        /// 将实体映射为DTO
        /// </summary>
        private RepairOrderDto MapToDto(RepairOrder repairOrder)
        {
            return new RepairOrderDto
            {
                Id = repairOrder.Id,
                OrderCode = repairOrder.OrderCode,
                Title = repairOrder.Title,
                Description = repairOrder.Description,
                Priority = repairOrder.Priority,
                PriorityName = GetPriorityName(repairOrder.Priority),
                SupplierId = repairOrder.SupplierId,
                SupplierName = repairOrder.Supplier?.Name ?? "未知供应商",
                FaultId = repairOrder.FaultId,
                AssetId = repairOrder.AssetId,
                SendDate = repairOrder.SendDate,
                ExpectedReturnDate = repairOrder.ExpectedReturnDate,
                ActualReturnDate = repairOrder.ActualReturnDate,
                EstimatedCost = repairOrder.EstimatedCost,
                TotalCost = repairOrder.TotalCost,
                Status = repairOrder.Status,
                StatusName = GetStatusName(repairOrder.Status),
                Notes = repairOrder.Notes,
                RepairResult = repairOrder.RepairResult,
                CreatorId = repairOrder.CreatorId,
                CreatorName = repairOrder.Creator?.Username ?? "未知用户",
                ApproverId = repairOrder.ApproverId,
                ApproverName = repairOrder.Approver?.Username,
                CreatedAt = repairOrder.CreatedAt,
                UpdatedAt = repairOrder.UpdatedAt,
                RepairItems = repairOrder.RepairItems?.Select(MapRepairItemToDto).ToList() ?? new List<RepairItemDto>()
            };
        }

        /// <summary>
        /// 将维修项目实体映射为DTO
        /// </summary>
        private RepairItemDto MapRepairItemToDto(RepairItem repairItem)
        {
            return new RepairItemDto
            {
                Id = repairItem.Id,
                RepairOrderId = repairItem.RepairOrderId,
                AssetId = repairItem.AssetId,
                AssetName = repairItem.Asset?.Name ?? "未知资产",
                FaultRecordId = repairItem.FaultRecordId,
                Description = repairItem.Description,
                RepairCost = repairItem.RepairCost,
                RepairStatus = repairItem.RepairStatus,
                RepairStatusName = GetRepairStatusName(repairItem.RepairStatus),
                RepairResult = repairItem.RepairResult
            };
        }

        /// <summary>
        /// 获取优先级名称
        /// </summary>
        private string GetPriorityName(RepairPriority priority)
        {
            return priority switch
            {
                RepairPriority.Emergency => "紧急",
                RepairPriority.High => "高",
                RepairPriority.Medium => "中",
                RepairPriority.Low => "低",
                _ => "未知"
            };
        }

        /// <summary>
        /// 获取状态名称
        /// </summary>
        private string GetStatusName(RepairOrderStatus status)
        {
            return status switch
            {
                RepairOrderStatus.PendingApproval => "待审核",
                RepairOrderStatus.Approved => "已审核",
                RepairOrderStatus.Shipped => "已发货",
                RepairOrderStatus.InRepair => "维修中",
                RepairOrderStatus.Completed => "已完成",
                RepairOrderStatus.Cancelled => "已取消",
                _ => "未知"
            };
        }

        /// <summary>
        /// 获取维修状态名称
        /// </summary>
        private string GetRepairStatusName(int status)
        {
            return status switch
            {
                1 => "待维修",
                2 => "维修中",
                3 => "维修完成",
                4 => "维修失败",
                _ => "未知"
            };
        }

        #endregion
    }
}