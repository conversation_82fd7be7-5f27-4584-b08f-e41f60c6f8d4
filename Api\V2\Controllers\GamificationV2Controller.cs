// File: Api/V2/Controllers/GamificationV2Controller.cs
// Description: 游戏化系统V2 API控制器

using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Logging;
using MediatR;
using ItAssetsSystem.Application.Features.Gamification.Services;
using ItAssetsSystem.Application.Features.Gamification.Events;
using ItAssetsSystem.Core.Abstractions;
using ItAssetsSystem.Models.Dtos;
using ItAssetsSystem.Models;
using ItAssetsSystem.Models.Dtos.Gamification;

namespace ItAssetsSystem.Api.V2.Controllers
{
    /// <summary>
    /// 游戏化系统V2 API控制器
    /// </summary>
    [ApiController]
    [Route("api/v2/gamification-v2")]
    [Authorize]
    public class GamificationV2Controller : ControllerBase
    {
        private readonly IGamificationRuleEngine _ruleEngine;
        private readonly IGamificationRewardProcessor _rewardProcessor;
        private readonly IGamificationStatsUpdater _statsUpdater;
        private readonly IMediator _mediator;
        private readonly ICurrentUserService _currentUserService;
        private readonly ILogger<GamificationV2Controller> _logger;

        public GamificationV2Controller(
            IGamificationRuleEngine ruleEngine,
            IGamificationRewardProcessor rewardProcessor,
            IGamificationStatsUpdater statsUpdater,
            IMediator mediator,
            ICurrentUserService currentUserService,
            ILogger<GamificationV2Controller> logger)
        {
            _ruleEngine = ruleEngine;
            _rewardProcessor = rewardProcessor;
            _statsUpdater = statsUpdater;
            _mediator = mediator;
            _currentUserService = currentUserService;
            _logger = logger;
        }

        /// <summary>
        /// 获取所有游戏化规则
        /// </summary>
        /// <returns>规则列表</returns>
        [HttpGet("rules")]
        public async Task<IActionResult> GetRules()
        {
            try
            {
                var rules = await _ruleEngine.GetAllRulesAsync();
                return Ok(ApiResponse<object>.CreateSuccess(rules, "获取规则列表成功"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取游戏化规则时发生错误");
                return StatusCode(500, ApiResponse<object>.CreateFail("获取规则列表失败"));
            }
        }

        /// <summary>
        /// 添加游戏化规则
        /// </summary>
        /// <param name="rule">规则配置</param>
        /// <returns>操作结果</returns>
        [HttpPost("rules")]
        public async Task<IActionResult> AddRule([FromBody] GamificationRule rule)
        {
            try
            {
                if (rule == null)
                {
                    return BadRequest(ApiResponse<object>.CreateFail("规则配置不能为空"));
                }

                var success = await _ruleEngine.AddRuleAsync(rule);
                if (success)
                {
                    return Ok(ApiResponse<object>.CreateSuccess(null, "规则添加成功"));
                }
                else
                {
                    return BadRequest(ApiResponse<object>.CreateFail("规则添加失败"));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "添加游戏化规则时发生错误");
                return StatusCode(500, ApiResponse<object>.CreateFail("添加规则失败"));
            }
        }

        /// <summary>
        /// 更新游戏化规则
        /// </summary>
        /// <param name="rule">规则配置</param>
        /// <returns>操作结果</returns>
        [HttpPut("rules")]
        public async Task<IActionResult> UpdateRule([FromBody] GamificationRule rule)
        {
            try
            {
                if (rule == null)
                {
                    return BadRequest(ApiResponse<object>.CreateFail("规则配置不能为空"));
                }

                var success = await _ruleEngine.UpdateRuleAsync(rule);
                if (success)
                {
                    return Ok(ApiResponse<object>.CreateSuccess(null, "规则更新成功"));
                }
                else
                {
                    return BadRequest(ApiResponse<object>.CreateFail("规则更新失败"));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新游戏化规则时发生错误");
                return StatusCode(500, ApiResponse<object>.CreateFail("更新规则失败"));
            }
        }

        /// <summary>
        /// 删除游戏化规则
        /// </summary>
        /// <param name="ruleId">规则ID</param>
        /// <returns>操作结果</returns>
        [HttpDelete("rules/{ruleId}")]
        public async Task<IActionResult> DeleteRule(string ruleId)
        {
            try
            {
                var success = await _ruleEngine.DeleteRuleAsync(ruleId);
                if (success)
                {
                    return Ok(ApiResponse<object>.CreateSuccess(null, "规则删除成功"));
                }
                else
                {
                    return BadRequest(ApiResponse<object>.CreateFail("规则删除失败"));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除游戏化规则时发生错误: {RuleId}", ruleId);
                return StatusCode(500, ApiResponse<object>.CreateFail("删除规则失败"));
            }
        }

        /// <summary>
        /// 启用/禁用规则
        /// </summary>
        /// <param name="ruleId">规则ID</param>
        /// <param name="isActive">是否启用</param>
        /// <returns>操作结果</returns>
        [HttpPatch("rules/{ruleId}/active")]
        public async Task<IActionResult> SetRuleActive(string ruleId, [FromQuery] bool isActive)
        {
            try
            {
                var success = await _ruleEngine.SetRuleActiveAsync(ruleId, isActive);
                if (success)
                {
                    return Ok(ApiResponse<object>.CreateSuccess(null, $"规则{(isActive ? "启用" : "禁用")}成功"));
                }
                else
                {
                    return BadRequest(ApiResponse<object>.CreateFail($"规则{(isActive ? "启用" : "禁用")}失败"));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "{Action}游戏化规则时发生错误: {RuleId}", isActive ? "启用" : "禁用", ruleId);
                return StatusCode(500, ApiResponse<object>.CreateFail($"规则{(isActive ? "启用" : "禁用")}失败"));
            }
        }

        /// <summary>
        /// 重新加载规则缓存
        /// </summary>
        /// <returns>操作结果</returns>
        [HttpPost("rules/reload")]
        public async Task<IActionResult> ReloadRules()
        {
            try
            {
                await _ruleEngine.ReloadRulesAsync();
                return Ok(ApiResponse<object>.CreateSuccess(null, "规则缓存重新加载成功"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "重新加载游戏化规则缓存时发生错误");
                return StatusCode(500, ApiResponse<object>.CreateFail("重新加载规则缓存失败"));
            }
        }

        /// <summary>
        /// 手动触发任务创建奖励 (测试用)
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <param name="taskName">任务名称</param>
        /// <param name="taskType">任务类型</param>
        /// <param name="points">任务积分</param>
        /// <returns>奖励结果</returns>
        [HttpPost("test/task-created")]
        public async Task<IActionResult> TestTaskCreatedReward(
            [FromQuery] long taskId,
            [FromQuery] string taskName = "测试任务",
            [FromQuery] string taskType = "General",
            [FromQuery] int points = 10)
        {
            try
            {
                var currentUserId = _currentUserService.GetCurrentUserId();
                if (currentUserId <= 0)
                {
                    return Unauthorized(ApiResponse<object>.CreateFail("用户未登录"));
                }

                // 发布任务创建奖励事件
                var rewardEvent = new TaskCreatedRewardEvent(currentUserId, taskId, taskName, taskType, points);
                await _mediator.Publish(rewardEvent);

                return Ok(ApiResponse<object>.CreateSuccess(null, "任务创建奖励测试完成"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "测试任务创建奖励时发生错误");
                return StatusCode(500, ApiResponse<object>.CreateFail("测试任务创建奖励失败"));
            }
        }

        /// <summary>
        /// 触发任务领取奖励
        /// </summary>
        /// <param name="request">任务领取请求</param>
        /// <returns>奖励结果</returns>
        [HttpPost("trigger-claim-reward")]
        public async Task<IActionResult> TriggerClaimReward([FromBody] TaskClaimRequest request)
        {
            try
            {
                var currentUserId = _currentUserService.GetCurrentUserId();
                if (currentUserId <= 0)
                {
                    return Unauthorized(ApiResponse<object>.CreateFail("用户未登录"));
                }

                // 发布任务领取奖励事件
                var rewardEvent = new TaskClaimedRewardEvent(currentUserId, request.TaskId, "任务领取", DateTime.UtcNow, 1);
                await _mediator.Publish(rewardEvent);

                return Ok(ApiResponse<object>.CreateSuccess(null, "任务领取奖励触发成功"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "触发任务领取奖励时发生错误");
                return StatusCode(500, ApiResponse<object>.CreateFail("触发任务领取奖励失败"));
            }
        }

        /// <summary>
        /// 触发任务完成奖励
        /// </summary>
        /// <param name="request">任务完成请求</param>
        /// <returns>奖励结果</returns>
        [HttpPost("trigger-complete-reward")]
        public async Task<IActionResult> TriggerCompleteReward([FromBody] TaskCompleteRequest request)
        {
            try
            {
                var currentUserId = _currentUserService.GetCurrentUserId();
                if (currentUserId <= 0)
                {
                    return Unauthorized(ApiResponse<object>.CreateFail("用户未登录"));
                }

                // 发布任务完成奖励事件
                var rewardEvent = new TaskCompletedRewardEvent(
                    currentUserId, request.TaskId, "任务", "General", request.IsOnTime, 20, DateTime.UtcNow);
                await _mediator.Publish(rewardEvent);

                return Ok(ApiResponse<object>.CreateSuccess(null, "任务完成奖励触发成功"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "触发任务完成奖励时发生错误");
                return StatusCode(500, ApiResponse<object>.CreateFail("触发任务完成奖励失败"));
            }
        }

        /// <summary>
        /// 手动触发任务完成奖励 (测试用)
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <param name="taskName">任务名称</param>
        /// <param name="taskType">任务类型</param>
        /// <param name="isOnTime">是否按时完成</param>
        /// <param name="points">任务积分</param>
        /// <returns>奖励结果</returns>
        [HttpPost("test/task-completed")]
        public async Task<IActionResult> TestTaskCompletedReward(
            [FromQuery] long taskId,
            [FromQuery] string taskName = "测试任务",
            [FromQuery] string taskType = "General",
            [FromQuery] bool isOnTime = true,
            [FromQuery] int points = 20)
        {
            try
            {
                var currentUserId = _currentUserService.GetCurrentUserId();
                if (currentUserId <= 0)
                {
                    return Unauthorized(ApiResponse<object>.CreateFail("用户未登录"));
                }

                // 发布任务完成奖励事件
                var rewardEvent = new TaskCompletedRewardEvent(
                    currentUserId, taskId, taskName, taskType, isOnTime, points, DateTime.UtcNow);
                await _mediator.Publish(rewardEvent);

                return Ok(ApiResponse<object>.CreateSuccess(null, "任务完成奖励测试完成"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "测试任务完成奖励时发生错误");
                return StatusCode(500, ApiResponse<object>.CreateFail("测试任务完成奖励失败"));
            }
        }

        /// <summary>
        /// 更新用户统计
        /// </summary>
        /// <param name="userId">用户ID（可选，默认为当前用户）</param>
        /// <returns>操作结果</returns>
        [HttpPost("stats/update")]
        public async Task<IActionResult> UpdateUserStats([FromQuery] int? userId = null)
        {
            try
            {
                var targetUserId = userId ?? _currentUserService.GetCurrentUserId();
                if (targetUserId <= 0)
                {
                    return Unauthorized(ApiResponse<object>.CreateFail("用户未登录"));
                }

                var success = await _statsUpdater.UpdateUserStatsAsync(targetUserId);
                if (success)
                {
                    return Ok(ApiResponse<object>.CreateSuccess(null, "用户统计更新成功"));
                }
                else
                {
                    return BadRequest(ApiResponse<object>.CreateFail("用户统计更新失败"));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新用户统计时发生错误");
                return StatusCode(500, ApiResponse<object>.CreateFail("更新用户统计失败"));
            }
        }

        /// <summary>
        /// 更新排行榜
        /// </summary>
        /// <param name="leaderboardType">排行榜类型</param>
        /// <param name="period">时间周期</param>
        /// <returns>操作结果</returns>
        [HttpPost("leaderboard/update")]
        public async Task<IActionResult> UpdateLeaderboard(
            [FromQuery] string leaderboardType = "weekly",
            [FromQuery] string period = "current")
        {
            try
            {
                var success = await _statsUpdater.UpdateLeaderboardAsync(leaderboardType, period);
                if (success)
                {
                    return Ok(ApiResponse<object>.CreateSuccess(null, "排行榜更新成功"));
                }
                else
                {
                    return BadRequest(ApiResponse<object>.CreateFail("排行榜更新失败"));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新排行榜时发生错误");
                return StatusCode(500, ApiResponse<object>.CreateFail("更新排行榜失败"));
            }
        }

        /// <summary>
        /// 初始化游戏化系统
        /// </summary>
        /// <returns>初始化结果</returns>
        [HttpPost("initialize")]
        public async Task<IActionResult> Initialize()
        {
            try
            {
                // 重新加载规则缓存
                await _ruleEngine.ReloadRulesAsync();

                return Ok(ApiResponse<object>.CreateSuccess(null, "游戏化系统初始化成功"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "初始化游戏化系统时发生错误");
                return StatusCode(500, ApiResponse<object>.CreateFail("游戏化系统初始化失败"));
            }
        }

        /// <summary>
        /// 获取当前用户游戏化统计
        /// </summary>
        /// <returns>用户统计信息</returns>
        [HttpGet("stats/current")]
        public async Task<IActionResult> GetCurrentUserStats()
        {
            try
            {
                var currentUserId = _currentUserService.GetCurrentUserId();
                if (currentUserId <= 0)
                {
                    return Unauthorized(ApiResponse<object>.CreateFail("用户未登录"));
                }

                // 这里应该调用实际的用户统计服务
                // 暂时返回模拟数据
                var userStats = new
                {
                    userId = currentUserId,
                    pointsBalance = 1250,
                    coinsBalance = 680,
                    diamondsBalance = 25,
                    currentLevel = 8,
                    currentXP = 1250,
                    completedTasksCount = 45,
                    createdTasksCount = 12,
                    claimedTasksCount = 38,
                    onTimeTasksCount = 42,
                    streakCount = 7,
                    lastActivityTimestamp = DateTime.UtcNow.AddHours(-2)
                };

                return Ok(ApiResponse<object>.CreateSuccess(userStats, "获取用户统计成功"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户统计时发生错误");
                return StatusCode(500, ApiResponse<object>.CreateFail("获取用户统计失败"));
            }
        }
    }
}
