// File: Models/Enums/PDCAStage.cs
// Description: PDCA循环阶段枚举

using System.ComponentModel.DataAnnotations;

namespace ItAssetsSystem.Models.Enums
{
    /// <summary>
    /// PDCA循环阶段枚举
    /// </summary>
    public enum PDCAStage
    {
        /// <summary>
        /// 计划阶段(Plan)
        /// </summary>
        [Display(Name = "计划阶段")]
        Plan = 0,

        /// <summary>
        /// 执行阶段(Do)
        /// </summary>
        [Display(Name = "执行阶段")]
        Do = 1,

        /// <summary>
        /// 检查阶段(Check)
        /// </summary>
        [Display(Name = "检查阶段")]
        Check = 2,

        /// <summary>
        /// 行动阶段(Act)
        /// </summary>
        [Display(Name = "行动阶段")]
        Act = 3
    }
} 