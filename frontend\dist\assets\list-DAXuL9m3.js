const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/spareparts-DKUrs8IX.js","assets/index-CG5lHOPO.js","assets/index-D61I4__u.css"])))=>i.map(i=>d[i]);
import{aV as e,_ as a,r as t,j as l,k as n,o as i,w as o,l as s,m as r,b as d,e as u,$ as c,a as p,p as m,t as f,d as v,f as g,ba as h,an as b,aH as y,bf as w,E as x,ad as _,z as V,aG as P,q as $,bg as k,F as C,h as I,D as z,b7 as N,ah as U,al as S,T as A,Z as q,i as D,a9 as T}from"./index-CG5lHOPO.js";import{getSparePartsSimple as L}from"./spareparts-DKUrs8IX.js";const Q="/v2/purchase",j={getPurchaseList:a=>e.get(Q,a),getPurchaseById:a=>e.get(`${Q}/${a}`),getPurchaseByIdV2:a=>e.get(`/v2/Purchase/${a}`),createPurchase:a=>e.post(Q,a),createPurchaseOrder:a=>e.post(Q,a),updatePurchase:(a,t)=>e.put(`${Q}/${a}`,t),deletePurchase:a=>e.delete(`${Q}/${a}`),getSupplierList:a=>e.get("/suppliers",a),getSupplierById:a=>e.get(`/suppliers/${a}`),createSupplier:a=>e.post("/suppliers",a),updateSupplier:(a,t)=>e.put(`/suppliers/${a}`,t),deleteSupplier:a=>e.delete(`/suppliers/${a}`),submitPurchaseForApproval:a=>e.put(`${Q}/${a}/submit`),approvePurchase:(a,t)=>e.put(`${Q}/${a}/approve`,t),rejectPurchase:(a,t)=>e.put(`${Q}/${a}/reject`,t),confirmPurchaseOrder:(a,t)=>e.put(`${Q}/${a}/order`,t),confirmPurchaseReceived:(a,t)=>e.put(`${Q}/${a}/receive`,t),confirmPurchaseStorage:(a,t)=>e.put(`${Q}/${a}/storage`,t),completePurchase:a=>e.put(`${Q}/${a}/complete`),cancelPurchase:(a,t)=>e.put(`${Q}/${a}/cancel`,t),getPurchaseApprovalHistory:(a,t)=>e.get(`${Q}/${a}/approval-history`,t),uploadPurchaseAttachment:(a,t)=>e.upload(`${Q}/${a}/attachments`,t),getPurchaseAttachments:(a,t)=>e.get(`${Q}/${a}/attachments`,t),deletePurchaseAttachment:(a,t)=>e.delete(`${Q}/${a}/attachments/${t}`),exportPurchases:a=>e.download(`${Q}/export`,a,"purchases.xlsx"),getPurchaseStatistics:a=>e.get(`${Q}/statistics`,a),getSuppliersV2:()=>e.get(`${Q}/suppliers`),getPurchasableAssetTypes:()=>e.get(`${Q}/asset-types`),processDeliveredItems:(a,t)=>e.post(`${Q}/${a}/process-items`,t),approvePurchase:a=>e.put(`${Q}/${a}/approve`),rejectPurchase:(a,t)=>e.put(`${Q}/${a}/reject`,t),completePurchase:(a,t)=>e.put(`${Q}/${a}/complete`,t)},B={class:"purchase-detail-content"},F={key:0,class:"detail-container"},E={class:"amount"},R={key:0,class:"notes-section"},H={class:"notes-text"},M={class:"card-header"},O={class:"item-count"},Y={class:"item-name"},W={key:0,class:"item-code"},G={class:"subtotal"},Z={class:"total-section"},J={class:"total-row"},K={class:"total-amount"},X={key:1,class:"loading-container"},ee={key:2,class:"error-container"},ae={class:"dialog-footer"},te=a({__name:"PurchaseDetailDialog",props:{modelValue:{type:Boolean,default:!1},purchaseId:{type:[Number,String],default:null}},emits:["update:modelValue","close"],setup(e,{emit:a}){const _=e,V=a,P=t(!1),$=t(!1),k=t(null);l((()=>_.modelValue),(e=>{P.value=e,e&&_.purchaseId&&C()})),l(P,(e=>{V("update:modelValue",e)}));const C=async()=>{if(_.purchaseId){$.value=!0,k.value=null;try{const e=await j.getPurchaseByIdV2(_.purchaseId);e&&e.success&&e.data?k.value=e.data:x.error((null==e?void 0:e.message)||"获取采购单详情失败")}catch(e){x.error("加载采购单详情失败: "+(e.message||"网络错误"))}finally{$.value=!1}}else x.error("采购单ID无效")},I=()=>{P.value=!1,k.value=null,V("close")},z=()=>{const e=N(),a=window.open("","_blank","width=800,height=600");if(a)a.document.write(e),a.document.close(),a.onload=()=>{a.focus(),a.print(),a.close()};else{const a=document.body.innerHTML;document.body.innerHTML=e,window.print(),document.body.innerHTML=a,location.reload()}},N=()=>{var e;const a=k.value;return a?`\n    <!DOCTYPE html>\n    <html lang="zh-CN">\n    <head>\n      <meta charset="UTF-8">\n      <meta name="viewport" content="width=device-width, initial-scale=1.0">\n      <title>采购单详情 - ${a.orderNumber||a.orderCode}</title>\n      <style>\n        * {\n          margin: 0;\n          padding: 0;\n          box-sizing: border-box;\n        }\n\n        body {\n          font-family: 'Microsoft YaHei', Arial, sans-serif;\n          font-size: 12px;\n          line-height: 1.6;\n          color: #333;\n          background: white;\n          padding: 20px;\n        }\n\n        .print-header {\n          text-align: center;\n          margin-bottom: 30px;\n          border-bottom: 2px solid #333;\n          padding-bottom: 15px;\n        }\n\n        .print-title {\n          font-size: 24px;\n          font-weight: bold;\n          margin-bottom: 10px;\n          color: #333;\n        }\n\n        .print-subtitle {\n          font-size: 14px;\n          color: #666;\n        }\n\n        .info-section {\n          margin-bottom: 25px;\n        }\n\n        .section-title {\n          font-size: 16px;\n          font-weight: bold;\n          margin-bottom: 15px;\n          color: #333;\n          border-left: 4px solid #409eff;\n          padding-left: 10px;\n        }\n\n        .info-grid {\n          display: grid;\n          grid-template-columns: repeat(2, 1fr);\n          gap: 15px;\n          margin-bottom: 15px;\n        }\n\n        .info-item {\n          display: flex;\n          align-items: center;\n        }\n\n        .info-label {\n          font-weight: bold;\n          min-width: 100px;\n          color: #555;\n        }\n\n        .info-value {\n          flex: 1;\n          color: #333;\n        }\n\n        .status-badge {\n          display: inline-block;\n          padding: 2px 8px;\n          border-radius: 4px;\n          font-size: 11px;\n          font-weight: bold;\n        }\n\n        .status-success { background: #f0f9ff; color: #1890ff; border: 1px solid #d1ecf1; }\n        .status-warning { background: #fffbf0; color: #fa8c16; border: 1px solid #ffeaa7; }\n        .status-info { background: #f6f8fa; color: #666; border: 1px solid #d1d5da; }\n\n        .amount-highlight {\n          font-weight: bold;\n          color: #e6a23c;\n          font-size: 14px;\n        }\n\n        .notes-section {\n          margin-top: 15px;\n          padding: 12px;\n          background: #f8f9fa;\n          border-radius: 4px;\n          border-left: 4px solid #409eff;\n        }\n\n        .notes-title {\n          font-weight: bold;\n          margin-bottom: 8px;\n          color: #333;\n        }\n\n        .notes-content {\n          color: #666;\n          line-height: 1.8;\n        }\n\n        .items-table {\n          width: 100%;\n          border-collapse: collapse;\n          margin-top: 10px;\n          font-size: 11px;\n        }\n\n        .items-table th,\n        .items-table td {\n          border: 1px solid #ddd;\n          padding: 8px;\n          text-align: left;\n        }\n\n        .items-table th {\n          background: #f5f7fa;\n          font-weight: bold;\n          color: #333;\n          text-align: center;\n        }\n\n        .items-table td {\n          vertical-align: top;\n        }\n\n        .items-table .text-center {\n          text-align: center;\n        }\n\n        .items-table .text-right {\n          text-align: right;\n        }\n\n        .item-name {\n          font-weight: bold;\n          color: #333;\n        }\n\n        .item-code {\n          font-size: 10px;\n          color: #999;\n          margin-top: 2px;\n        }\n\n        .quantity-badge {\n          display: inline-block;\n          background: #e6f7ff;\n          color: #1890ff;\n          padding: 2px 6px;\n          border-radius: 3px;\n          font-weight: bold;\n        }\n\n        .total-section {\n          margin-top: 20px;\n          padding-top: 15px;\n          border-top: 2px solid #eee;\n          text-align: right;\n        }\n\n        .total-row {\n          font-size: 16px;\n          font-weight: bold;\n          color: #333;\n        }\n\n        .total-amount {\n          color: #e6a23c;\n          font-size: 18px;\n        }\n\n        .print-footer {\n          margin-top: 40px;\n          padding-top: 20px;\n          border-top: 1px solid #ddd;\n          text-align: center;\n          color: #999;\n          font-size: 11px;\n        }\n\n        @media print {\n          body {\n            padding: 10px;\n          }\n\n          .print-header {\n            margin-bottom: 20px;\n          }\n\n          .info-section {\n            margin-bottom: 20px;\n            page-break-inside: avoid;\n          }\n\n          .items-table {\n            page-break-inside: auto;\n          }\n\n          .items-table tr {\n            page-break-inside: avoid;\n            page-break-after: auto;\n          }\n\n          .total-section {\n            page-break-inside: avoid;\n          }\n\n          .print-footer {\n            position: fixed;\n            bottom: 0;\n            left: 0;\n            right: 0;\n            text-align: center;\n          }\n        }\n      </style>\n    </head>\n    <body>\n      <div class="print-header">\n        <div class="print-title">采购单详情</div>\n        <div class="print-subtitle">采购单号：${a.orderNumber||a.orderCode}</div>\n      </div>\n\n      <div class="info-section">\n        <div class="section-title">📋 基本信息</div>\n        <div class="info-grid">\n          <div class="info-item">\n            <span class="info-label">采购单号：</span>\n            <span class="info-value">${a.orderNumber||a.orderCode}</span>\n          </div>\n          <div class="info-item">\n            <span class="info-label">采购状态：</span>\n            <span class="info-value">\n              <span class="status-badge ${S(a.status)}">${A(a.status)}</span>\n            </span>\n          </div>\n          <div class="info-item">\n            <span class="info-label">供应商：</span>\n            <span class="info-value">${a.supplierName||"-"}</span>\n          </div>\n          <div class="info-item">\n            <span class="info-label">申请人：</span>\n            <span class="info-value">${a.applicantName||"-"}</span>\n          </div>\n          <div class="info-item">\n            <span class="info-label">预计交付：</span>\n            <span class="info-value">${q(a.expectedDeliveryDate)}</span>\n          </div>\n          <div class="info-item">\n            <span class="info-label">实际交付：</span>\n            <span class="info-value">${q(a.actualDeliveryDate)}</span>\n          </div>\n          <div class="info-item">\n            <span class="info-label">总金额：</span>\n            <span class="info-value amount-highlight">¥${D(a.totalAmount)}</span>\n          </div>\n          <div class="info-item">\n            <span class="info-label">创建时间：</span>\n            <span class="info-value">${q(a.createdAt)}</span>\n          </div>\n        </div>\n\n        ${a.notes?`\n          <div class="notes-section">\n            <div class="notes-title">备注说明</div>\n            <div class="notes-content">${a.notes}</div>\n          </div>\n        `:""}\n      </div>\n\n      <div class="info-section">\n        <div class="section-title">📦 采购物品清单 (共 ${(null==(e=a.items)?void 0:e.length)||0} 项)</div>\n        <table class="items-table">\n          <thead>\n            <tr>\n              <th style="width: 40px;">序号</th>\n              <th style="width: 200px;">物品名称</th>\n              <th style="width: 120px;">规格型号</th>\n              <th style="width: 60px;">数量</th>\n              <th style="width: 80px;">单价</th>\n              <th style="width: 80px;">小计</th>\n              <th>备注</th>\n            </tr>\n          </thead>\n          <tbody>\n            ${(a.items||[]).map(((e,a)=>`\n              <tr>\n                <td class="text-center">${a+1}</td>\n                <td>\n                  <div class="item-name">${e.name||e.itemName}</div>\n                  ${e.itemCode?`<div class="item-code">编号: ${e.itemCode}</div>`:""}\n                </td>\n                <td>${e.specification||"-"}</td>\n                <td class="text-center">\n                  <span class="quantity-badge">${e.quantity}</span>\n                </td>\n                <td class="text-right">¥${D(e.unitPrice)}</td>\n                <td class="text-right">¥${D(e.unitPrice*e.quantity)}</td>\n                <td>${e.notes||"-"}</td>\n              </tr>\n            `)).join("")}\n          </tbody>\n        </table>\n\n        <div class="total-section">\n          <div class="total-row">\n            总计金额：<span class="total-amount">¥${D(a.totalAmount)}</span>\n          </div>\n        </div>\n      </div>\n\n      <div class="print-footer">\n        打印时间：${(new Date).toLocaleString("zh-CN")} | 航空航天级IT资产管理系统\n      </div>\n    </body>\n    </html>\n  `:""},U=()=>{const e=N(),a=window.open("","_blank","width=900,height=700,scrollbars=yes");a?(a.document.write(e),a.document.close(),a.focus()):x.error("无法打开预览窗口，请检查浏览器弹窗设置")},S=e=>({0:"status-warning",1:"status-success",pending:"status-info",approved:"status-success",purchased:"status-warning",received:"status-success",rejected:"status-danger",cancelled:"status-danger"}[e]||"status-info"),A=e=>({0:"未到货",1:"已到货",pending:"待审批",approved:"已审批",purchased:"已采购",received:"已入库",rejected:"已拒绝",cancelled:"已取消"}[e]||"未知状态"),q=e=>{if(!e||"0001-01-01T00:00:00"===e||e.startsWith("0001-01-01"))return"-";try{const a=new Date(e);return isNaN(a.getTime())?"-":a.toLocaleDateString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit"})}catch(a){return"-"}},D=e=>null==e||""===e?"0.00":Number(e).toLocaleString("zh-CN",{minimumFractionDigits:2,maximumFractionDigits:2});return(e,a)=>{const t=p("el-tag"),l=p("el-descriptions-item"),x=p("el-descriptions"),_=p("el-card"),V=p("el-table-column"),C=p("el-table"),N=p("el-icon"),S=p("el-button"),T=p("el-dialog"),L=r("loading");return i(),n(T,{modelValue:P.value,"onUpdate:modelValue":a[0]||(a[0]=e=>P.value=e),title:"采购单详情",width:"800px","destroy-on-close":"",onClose:I},{footer:o((()=>[v("div",ae,[u(S,{onClick:I},{default:o((()=>a[7]||(a[7]=[m("关闭")]))),_:1}),k.value?(i(),n(S,{key:0,onClick:U},{default:o((()=>[u(N,null,{default:o((()=>[u(g(y))])),_:1}),a[8]||(a[8]=m(" 打印预览 "))])),_:1})):c("",!0),k.value?(i(),n(S,{key:1,type:"primary",onClick:z},{default:o((()=>[u(N,null,{default:o((()=>[u(g(w))])),_:1}),a[9]||(a[9]=m(" 打印 "))])),_:1})):c("",!0)])])),default:o((()=>[s((i(),d("div",B,[k.value?(i(),d("div",F,[u(_,{class:"info-card",shadow:"never"},{header:o((()=>a[1]||(a[1]=[v("div",{class:"card-header"},[v("span",{class:"card-title"},"📋 基本信息")],-1)]))),default:o((()=>[u(x,{column:2,border:""},{default:o((()=>[u(l,{label:"采购单号"},{default:o((()=>[u(t,{type:"primary"},{default:o((()=>[m(f(k.value.orderNumber||k.value.orderCode),1)])),_:1})])),_:1}),u(l,{label:"采购状态"},{default:o((()=>{return[u(t,{type:(e=k.value.status,{0:"warning",1:"success",pending:"info",approved:"success",purchased:"warning",received:"primary",rejected:"danger",cancelled:"danger"}[e]||"info")},{default:o((()=>[m(f(A(k.value.status)),1)])),_:1},8,["type"])];var e})),_:1}),u(l,{label:"供应商"},{default:o((()=>[m(f(k.value.supplierName||"-"),1)])),_:1}),u(l,{label:"申请人"},{default:o((()=>[m(f(k.value.applicantName||"-"),1)])),_:1}),u(l,{label:"预计交付日期"},{default:o((()=>[m(f(q(k.value.expectedDeliveryDate)),1)])),_:1}),u(l,{label:"实际交付日期"},{default:o((()=>[m(f(q(k.value.actualDeliveryDate)),1)])),_:1}),u(l,{label:"总金额"},{default:o((()=>[v("span",E,"¥"+f(D(k.value.totalAmount)),1)])),_:1}),u(l,{label:"创建时间"},{default:o((()=>[m(f(q(k.value.createdAt)),1)])),_:1})])),_:1}),k.value.notes?(i(),d("div",R,[a[2]||(a[2]=v("h4",null,"备注说明",-1)),v("p",H,f(k.value.notes),1)])):c("",!0)])),_:1}),u(_,{class:"items-card",shadow:"never"},{header:o((()=>{var e;return[v("div",M,[a[3]||(a[3]=v("span",{class:"card-title"},"📦 采购物品清单",-1)),v("span",O,"共 "+f((null==(e=k.value.items)?void 0:e.length)||0)+" 项",1)])]})),default:o((()=>[u(C,{data:k.value.items||[],border:"",stripe:"",style:{width:"100%"}},{default:o((()=>[u(V,{type:"index",label:"序号",width:"60",align:"center"}),u(V,{prop:"name",label:"物品名称","min-width":"150"},{default:o((({row:e})=>[v("div",Y,[v("strong",null,f(e.name||e.itemName),1),e.itemCode?(i(),d("div",W,"编号: "+f(e.itemCode),1)):c("",!0)])])),_:1}),u(V,{prop:"specification",label:"规格型号","min-width":"120"},{default:o((({row:e})=>[m(f(e.specification||"-"),1)])),_:1}),u(V,{prop:"quantity",label:"数量",width:"80",align:"center"},{default:o((({row:e})=>[u(t,{size:"small"},{default:o((()=>[m(f(e.quantity),1)])),_:2},1024)])),_:1}),u(V,{prop:"unitPrice",label:"单价",width:"100",align:"right"},{default:o((({row:e})=>[m(" ¥"+f(D(e.unitPrice)),1)])),_:1}),u(V,{label:"小计",width:"120",align:"right"},{default:o((({row:e})=>[v("span",G,"¥"+f(D(e.unitPrice*e.quantity)),1)])),_:1}),u(V,{prop:"notes",label:"备注","min-width":"100"},{default:o((({row:e})=>[m(f(e.notes||"-"),1)])),_:1})])),_:1},8,["data"]),v("div",Z,[v("div",J,[a[4]||(a[4]=v("span",{class:"total-label"},"总计金额：",-1)),v("span",K,"¥"+f(D(k.value.totalAmount)),1)])])])),_:1})])):$.value?(i(),d("div",X,[u(N,{class:"is-loading"},{default:o((()=>[u(g(h))])),_:1}),a[5]||(a[5]=v("p",null,"正在加载采购单详情...",-1))])):(i(),d("div",ee,[u(N,null,{default:o((()=>[u(g(b))])),_:1}),a[6]||(a[6]=v("p",null,"加载采购单详情失败",-1))]))])),[[L,$.value]])])),_:1},8,["modelValue"])}}},[["__scopeId","data-v-a674e351"]]),le={class:"purchase-list-container"},ne={class:"page-header"},ie={class:"page-actions"},oe={class:"filter-container"},se={class:"expanded-content"},re={class:"expanded-header"},de={class:"items-detail"},ue={class:"item-detail"},ce={class:"item-name"},pe={key:0,class:"item-code"},me={class:"subtotal"},fe={class:"purchase-items-summary"},ve={class:"primary-item"},ge={class:"item-name"},he={key:0,class:"item-code"},be={class:"items-stats"},ye={key:0,class:"more-items"},we={class:"pagination-container"},xe={class:"receive-dialog-content"},_e={class:"order-info"},Ve={class:"items-conversion"},Pe={class:"conversion-tips"},$e={class:"dialog-footer"},ke={class:"purchase-items"},Ce={class:"dialog-footer"},Ie=a({__name:"list",setup(e){const a=t(!1),l=t("all"),h=t([]),b=t(null),w=t([]),Q=t([]),B=t(!1),F=t(!1),E=t(null),R=t([]),H=t({}),M=t({}),O=_({currentPage:1,pageSize:10,total:0}),Y=_({code:"",name:"",type:"",status:"",timeRange:[]}),W=[{label:"新设备",value:"new_device"},{label:"更换设备",value:"replacement"},{label:"配件",value:"accessories"},{label:"软件",value:"software"},{label:"服务",value:"service"}],G=[{label:"待审批",value:"pending"},{label:"已审批",value:"approved"},{label:"已采购",value:"purchased"},{label:"已入库",value:"received"},{label:"已拒绝",value:"rejected"},{label:"已取消",value:"cancelled"}];V((()=>{Z(),(async()=>{try{const{getProcurementSuppliers:e}=await k((async()=>{const{getProcurementSuppliers:e}=await import("./spareparts-DKUrs8IX.js");return{getProcurementSuppliers:e}}),__vite__mapDeps([0,1,2])),a=await e();if(a.success)w.value=a.data||[];else{const e=await j.getSuppliersV2();e.success?w.value=e.data||[]:w.value=[{id:1,name:"联想"},{id:2,name:"惠普"},{id:3,name:"戴尔"},{id:4,name:"华为"}]}}catch(e){w.value=[{id:1,name:"联想"},{id:2,name:"惠普"},{id:3,name:"戴尔"},{id:4,name:"华为"}]}})()}));const Z=async()=>{var e;a.value=!0;const t={pageIndex:O.currentPage,pageSize:O.pageSize,orderCode:Y.code,title:Y.name,status:"all"===l.value?Y.status:l.value};try{const a=await j.getPurchaseList(t);a.success?(h.value=a.data||[],O.total=(null==(e=a.pagination)?void 0:e.totalCount)||0):(x.error(a.message||"获取采购列表失败"),h.value=[],O.total=0)}catch(n){x.error("获取采购列表失败"),h.value=[],O.total=0}finally{a.value=!1}},J=()=>{O.currentPage=1,Z()},K=()=>{O.currentPage=1,Z()},X=()=>{Y.code="",Y.name="",Y.type="",Y.status="",Y.timeRange=[],O.currentPage=1,Z()},ee=e=>{O.pageSize=e,Z()},ae=e=>{O.currentPage=e,Z()},Ie=async(e,a)=>{a.some((a=>a.id===e.id))&&!H.value[e.id]&&await ze(e.id)},ze=async e=>{if(!H.value[e]){M.value[e]=!0;try{const a=await j.getPurchaseByIdV2(e);a&&a.success&&a.data?H.value[e]=a.data.items||[]:(x.error((null==a?void 0:a.message)||"获取采购单物品详情失败"),H.value[e]=[])}catch(a){x.error("加载采购单物品详情失败: "+(a.message||"网络错误")),H.value[e]=[]}finally{M.value[e]=!1}}},Ne=t(!1),Ue=t(null),Se=_({items:[]}),Ae=e=>{Ue.value=e,Se.items=e.items.map((e=>({...e,toAssetQuantity:0,toSparePartQuantity:e.quantity,assetLocationId:null,sparePartLocationId:null}))),Ne.value=!0},qe=async()=>{try{for(const t of Se.items){if(t.toAssetQuantity+t.toSparePartQuantity!==t.quantity)return void x.warning(`${t.name} 的转化数量总和必须等于采购数量`);if(t.toAssetQuantity>0&&!t.assetLocationId)return void x.warning(`${t.name} 转为资产时必须选择资产位置`);if(t.toSparePartQuantity>0&&!t.sparePartLocationId)return void x.warning(`${t.name} 转为备件时必须选择备件库位`)}const e=Se.items.map((e=>({purchaseItemId:e.id,toSparePartQuantity:e.toSparePartQuantity,sparePartLocationId:e.sparePartLocationId,toAssetQuantity:e.toAssetQuantity,assetLocationId:e.assetLocationId}))),a=await j.processDeliveredItems(Ue.value.id,e);a.success?(x.success("入库转化成功"),Ne.value=!1,Z()):x.error(a.message||"入库转化失败")}catch(e){x.error("入库转化失败："+e.message)}},De=()=>{x.success("开始导出数据，请稍候...")},Te=()=>{Ee()},Le=t(!1),Qe=t(!1),je=t(null),Be=_({name:"",type:"",supplierId:"",estimatedAmount:0,description:"",items:[{sparePartId:null,materialNumber:"",name:"",quantity:1,unitPrice:0,specification:"",notes:""}]}),Fe={name:[{required:!0,message:"请输入采购名称",trigger:"blur"},{min:2,max:100,message:"采购名称长度在 2 到 100 个字符",trigger:"blur"}],type:[{required:!0,message:"请选择采购类型",trigger:"change"}],supplierId:[{required:!0,message:"请选择供应商",trigger:"change"}],estimatedAmount:[{required:!0,message:"请输入预计金额",trigger:"blur"},{type:"number",min:0,message:"预计金额必须大于等于0",trigger:"blur"}]},Ee=()=>{Re(),Le.value=!0},Re=()=>{var e;Be.name="",Be.type="",Be.supplierId="",Be.estimatedAmount=0,Be.description="",Be.items=[{sparePartId:null,materialNumber:"",name:"",quantity:1,unitPrice:0,specification:"",notes:""}],null==(e=je.value)||e.clearValidate()},He=()=>{Be.items.push({sparePartId:null,materialNumber:"",name:"",quantity:1,unitPrice:0,specification:"",notes:""})},Me=e=>{},Oe=async()=>{var e;try{await(null==(e=je.value)?void 0:e.validate())}catch(l){return}const a=Be.items.filter((e=>e.name&&e.quantity>0));if(0===a.length)return void x.warning("请至少添加一个有效的采购物品");const t=(e=>{const a={},t=[];return e.forEach(((e,t)=>{var l,n;const i=`${null==(l=e.name)?void 0:l.trim()}|${null==(n=e.specification)?void 0:n.trim()}|${e.unitPrice}`;a[i]||(a[i]=[]),a[i].push({...e,originalIndex:t})})),Object.keys(a).forEach((e=>{const l=a[e];l.length>1&&t.push({name:l[0].name,specification:l[0].specification,count:l.length,totalQuantity:l.reduce(((e,a)=>e+a.quantity),0),items:l})})),t})(a);if(t.length>0){let e="检测到重复物品，系统将自动合并：\n";t.forEach((a=>{e+=`• ${a.name} (${a.specification||"无规格"}) - ${a.count}项合并为${a.totalQuantity}个\n`})),e+="\n是否继续提交？";try{await T.confirm(e,"重复物品提示",{confirmButtonText:"继续提交",cancelButtonText:"取消",type:"warning",showClose:!1})}catch{return}}Qe.value=!0;try{const e={supplierId:parseInt(Be.supplierId),requesterId:1,expectedDeliveryDate:null,notes:Be.description,items:a.map((e=>({itemName:e.name,itemCode:e.materialNumber||"",specification:e.specification,assetTypeId:1,unitPrice:parseFloat(e.unitPrice),quantity:parseInt(e.quantity),notes:e.notes||""})))},t=await j.createPurchaseOrder(e);t.success?(x.success("采购订单创建成功"),Le.value=!1,Z()):x.error(t.message||"创建采购订单失败")}catch(l){x.error("创建采购订单失败")}finally{Qe.value=!1}},Ye=e=>{if(!e)return"-";if("0001-01-01T00:00:00"===e||e.startsWith("0001-01-01"))return"-";try{const a=new Date(e);return isNaN(a.getTime())?"-":a.toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"})}catch(a){return"-"}},We=e=>null==e||""===e?"0.00":Number(e).toLocaleString("zh-CN",{minimumFractionDigits:2,maximumFractionDigits:2});return(e,t)=>{const _=p("el-button"),V=p("el-input"),k=p("el-form-item"),ze=p("el-option"),Ee=p("el-select"),Ge=p("el-date-picker"),Ze=p("el-form"),Je=p("el-card"),Ke=p("el-tab-pane"),Xe=p("el-tabs"),ea=p("el-tag"),aa=p("el-table-column"),ta=p("el-table"),la=p("el-pagination"),na=p("el-input-number"),ia=p("el-alert"),oa=p("el-dialog"),sa=p("el-col"),ra=p("el-row"),da=r("loading");return i(),d("div",le,[v("div",ne,[t[21]||(t[21]=v("h2",{class:"page-title"},"采购列表",-1)),v("div",ie,[u(_,{type:"primary",onClick:Te,icon:g(P)},{default:o((()=>t[19]||(t[19]=[m(" 新建采购 ")]))),_:1},8,["icon"]),u(_,{type:"primary",onClick:De,icon:g($)},{default:o((()=>t[20]||(t[20]=[m(" 导出数据 ")]))),_:1},8,["icon"])])]),u(Je,{class:"filter-card"},{default:o((()=>[v("div",oe,[u(Ze,{inline:!0,model:Y,class:"filter-form"},{default:o((()=>[u(k,{label:"采购单号"},{default:o((()=>[u(V,{modelValue:Y.code,"onUpdate:modelValue":t[0]||(t[0]=e=>Y.code=e),placeholder:"采购单号",clearable:""},null,8,["modelValue"])])),_:1}),u(k,{label:"采购名称"},{default:o((()=>[u(V,{modelValue:Y.name,"onUpdate:modelValue":t[1]||(t[1]=e=>Y.name=e),placeholder:"采购名称",clearable:""},null,8,["modelValue"])])),_:1}),u(k,{label:"采购类型"},{default:o((()=>[u(Ee,{modelValue:Y.type,"onUpdate:modelValue":t[2]||(t[2]=e=>Y.type=e),placeholder:"全部类型",clearable:""},{default:o((()=>[(i(),d(C,null,I(W,(e=>u(ze,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),64))])),_:1},8,["modelValue"])])),_:1}),u(k,{label:"状态"},{default:o((()=>[u(Ee,{modelValue:Y.status,"onUpdate:modelValue":t[3]||(t[3]=e=>Y.status=e),placeholder:"全部状态",clearable:""},{default:o((()=>[(i(),d(C,null,I(G,(e=>u(ze,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),64))])),_:1},8,["modelValue"])])),_:1}),u(k,{label:"采购时间"},{default:o((()=>[u(Ge,{modelValue:Y.timeRange,"onUpdate:modelValue":t[4]||(t[4]=e=>Y.timeRange=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"YYYY-MM-DD"},null,8,["modelValue"])])),_:1}),u(k,null,{default:o((()=>[u(_,{type:"primary",onClick:K,icon:g(z)},{default:o((()=>t[22]||(t[22]=[m(" 搜索 ")]))),_:1},8,["icon"]),u(_,{onClick:X,icon:g(N)},{default:o((()=>t[23]||(t[23]=[m(" 重置 ")]))),_:1},8,["icon"])])),_:1})])),_:1},8,["model"])])])),_:1}),u(Je,{class:"data-card"},{default:o((()=>[u(Xe,{modelValue:l.value,"onUpdate:modelValue":t[5]||(t[5]=e=>l.value=e),onTabClick:J},{default:o((()=>[u(Ke,{label:"全部",name:"all"}),u(Ke,{label:"待审批",name:"pending"}),u(Ke,{label:"待采购",name:"approved"}),u(Ke,{label:"已采购",name:"purchased"}),u(Ke,{label:"已入库",name:"received"})])),_:1},8,["modelValue"]),s((i(),n(ta,{ref_key:"purchaseTable",ref:b,data:h.value,border:"",style:{width:"100%"},"expand-row-keys":R.value,"row-key":"id",onExpandChange:Ie},{default:o((()=>[u(aa,{type:"expand"},{default:o((({row:e})=>[v("div",se,[v("div",re,[t[24]||(t[24]=v("h4",null,"📦 采购物品明细",-1)),u(ea,{size:"small",type:"info"},{default:o((()=>[m("共 "+f(e.itemCount)+" 种物品",1)])),_:2},1024)]),s((i(),d("div",de,[u(ta,{data:H.value[e.id]||[],size:"small",border:"",style:{margin:"10px 0"}},{default:o((()=>[u(aa,{type:"index",label:"序号",width:"60",align:"center"}),u(aa,{prop:"itemName",label:"物品名称","min-width":"150"},{default:o((({row:e})=>[v("div",ue,[v("div",ce,f(e.itemName||e.name),1),e.itemCode?(i(),d("div",pe,"编号: "+f(e.itemCode),1)):c("",!0)])])),_:1}),u(aa,{prop:"specification",label:"规格型号","min-width":"120"},{default:o((({row:e})=>[m(f(e.specification||"-"),1)])),_:1}),u(aa,{prop:"quantity",label:"数量",width:"80",align:"center"},{default:o((({row:e})=>[u(ea,{size:"small",type:"primary"},{default:o((()=>[m(f(e.quantity),1)])),_:2},1024)])),_:1}),u(aa,{prop:"unitPrice",label:"单价",width:"100",align:"right"},{default:o((({row:e})=>[m(" ¥"+f(We(e.unitPrice)),1)])),_:1}),u(aa,{label:"小计",width:"120",align:"right"},{default:o((({row:e})=>[v("span",me,"¥"+f(We(e.unitPrice*e.quantity)),1)])),_:1}),u(aa,{prop:"notes",label:"备注","min-width":"100"},{default:o((({row:e})=>[m(f(e.notes||"-"),1)])),_:1})])),_:2},1032,["data"])])),[[da,M.value[e.id]]])])])),_:1}),u(aa,{prop:"orderCode",label:"采购单号",width:"150",sortable:""}),u(aa,{label:"物料编号",width:"120","show-overflow-tooltip":""},{default:o((e=>[m(f(e.row.primaryItemCode||"-"),1)])),_:1}),u(aa,{label:"采购物品","min-width":"220","show-overflow-tooltip":""},{default:o((e=>[v("div",fe,[v("div",ve,[v("span",ge,f(e.row.primaryItemName||"-"),1),e.row.primaryItemCode?(i(),d("span",he," ("+f(e.row.primaryItemCode)+") ",1)):c("",!0)]),v("div",be,[u(ea,{size:"small",type:"info"},{default:o((()=>[m(f(e.row.itemCount)+"种物品 ",1)])),_:2},1024),u(ea,{size:"small",type:"primary"},{default:o((()=>[m(" 共"+f(e.row.totalQuantity)+"件 ",1)])),_:2},1024),e.row.itemCount>1?(i(),d("span",ye," +"+f(e.row.itemCount-1)+"种其他物品 ",1)):c("",!0)])])])),_:1}),u(aa,{prop:"description",label:"类型",width:"100","show-overflow-tooltip":""}),u(aa,{prop:"status",label:"状态",width:"120"},{default:o((e=>{return[u(ea,{type:(a=e.row.status,{pending:"info",approved:"success",purchased:"warning",received:"primary",rejected:"danger",cancelled:"danger"}[a]||""),size:"small"},{default:o((()=>[m(f(e.row.statusName),1)])),_:2},1032,["type"])];var a})),_:1}),u(aa,{prop:"totalAmount",label:"金额",width:"120"},{default:o((e=>[m(" ￥"+f((e.row.totalAmount||0).toLocaleString()),1)])),_:1}),u(aa,{label:"数量",width:"80",align:"center"},{default:o((e=>[m(f(e.row.totalQuantity||0),1)])),_:1}),u(aa,{prop:"applicantName",label:"申请人",width:"100"}),u(aa,{prop:"applicationTime",label:"申请时间",width:"180",sortable:""},{default:o((e=>[m(f(Ye(e.row.applicationTime)),1)])),_:1}),u(aa,{label:"审批人",width:"100"},{default:o((e=>[m(f(e.row.approverName||"-"),1)])),_:1}),u(aa,{prop:"createdAt",label:"创建时间",width:"180",sortable:""},{default:o((e=>[m(f(Ye(e.row.createdAt)),1)])),_:1}),u(aa,{prop:"supplierName",label:"供应商",width:"150","show-overflow-tooltip":""}),u(aa,{label:"入库时间",width:"180",sortable:""},{default:o((e=>[m(f(e.row.actualDeliveryDate?Ye(e.row.actualDeliveryDate):"-"),1)])),_:1}),u(aa,{label:"操作",width:"220",fixed:"right"},{default:o((e=>[u(_,{type:"primary",text:"",size:"small",onClick:a=>{return t=e.row,E.value=t.id,void(F.value=!0);var t},icon:g(y)},{default:o((()=>t[25]||(t[25]=[m(" 详情 ")]))),_:2},1032,["onClick","icon"]),"pending"===e.row.status?(i(),d(C,{key:0},[u(_,{type:"success",text:"",size:"small",onClick:a=>{return t=e.row,void T.confirm(`确认审批通过采购单"${t.name}"吗？`,"审批确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{try{const e=await j.approvePurchase(t.id);e.success?(x.success("审批通过成功"),Z()):x.error(e.message||"审批失败")}catch(e){x.error("审批失败")}})).catch((()=>{}));var t},icon:g(U)},{default:o((()=>t[26]||(t[26]=[m(" 审批 ")]))),_:2},1032,["onClick","icon"]),u(_,{type:"danger",text:"",size:"small",onClick:a=>{return t=e.row,void T.prompt("请输入拒绝原因","拒绝理由",{confirmButtonText:"确定",cancelButtonText:"取消",inputType:"textarea",inputPlaceholder:"请输入拒绝理由..."}).then((async({value:e})=>{if(e)try{const a=await j.rejectPurchase(t.id,{reason:e});a.success?(x.success("已拒绝该采购申请"),Z()):x.error(a.message||"拒绝失败")}catch(a){x.error("拒绝失败")}else x.warning("请输入拒绝理由")})).catch((()=>{}));var t},icon:g(S)},{default:o((()=>t[27]||(t[27]=[m(" 拒绝 ")]))),_:2},1032,["onClick","icon"])],64)):c("",!0),"approved"===e.row.status?(i(),n(_,{key:1,type:"success",text:"",size:"small",onClick:a=>{return t=e.row,void T.prompt("请输入供应商信息","完成采购",{confirmButtonText:"确定",cancelButtonText:"取消",inputPlaceholder:"请输入供应商名称"}).then((async({value:e})=>{if(e)try{const a=await j.completePurchase(t.id,{vendor:e});a.success?(x.success("采购完成"),Z()):x.error(a.message||"完成采购失败")}catch(a){x.error("完成采购失败")}else x.warning("请输入供应商信息")})).catch((()=>{}));var t},icon:g(A)},{default:o((()=>t[28]||(t[28]=[m(" 完成采购 ")]))),_:2},1032,["onClick","icon"])):c("",!0),"purchased"===e.row.status?(i(),n(_,{key:2,type:"warning",text:"",size:"small",onClick:a=>{return t=e.row,void Ae(t);var t},icon:g(q)},{default:o((()=>t[29]||(t[29]=[m(" 入库 ")]))),_:2},1032,["onClick","icon"])):c("",!0)])),_:1})])),_:1},8,["data","expand-row-keys"])),[[da,a.value]]),v("div",we,[u(la,{"current-page":O.currentPage,"onUpdate:currentPage":t[6]||(t[6]=e=>O.currentPage=e),"page-size":O.pageSize,"onUpdate:pageSize":t[7]||(t[7]=e=>O.pageSize=e),"page-sizes":[10,20,50,100],background:!0,layout:"total, sizes, prev, pager, next, jumper",total:O.total,onSizeChange:ee,onCurrentChange:ae},null,8,["current-page","page-size","total"])])])),_:1}),u(oa,{modelValue:Ne.value,"onUpdate:modelValue":t[9]||(t[9]=e=>Ne.value=e),title:"采购物品入库转化",width:"80%","close-on-click-modal":!1},{footer:o((()=>[v("span",$e,[u(_,{onClick:t[8]||(t[8]=e=>Ne.value=!1)},{default:o((()=>t[38]||(t[38]=[m("取消")]))),_:1}),u(_,{type:"primary",onClick:qe},{default:o((()=>t[39]||(t[39]=[m("确认入库")]))),_:1})])])),default:o((()=>{var e,a,l;return[v("div",xe,[v("div",_e,[t[33]||(t[33]=v("h4",null,"采购单信息",-1)),v("p",null,[t[30]||(t[30]=v("strong",null,"采购单号：",-1)),m(f(null==(e=Ue.value)?void 0:e.code),1)]),v("p",null,[t[31]||(t[31]=v("strong",null,"采购名称：",-1)),m(f(null==(a=Ue.value)?void 0:a.name),1)]),v("p",null,[t[32]||(t[32]=v("strong",null,"供应商：",-1)),m(f(null==(l=Ue.value)?void 0:l.vendor),1)])]),v("div",Ve,[t[36]||(t[36]=v("h4",null,"物品转化设置",-1)),u(ta,{data:Se.items,border:"",style:{width:"100%"}},{default:o((()=>[u(aa,{prop:"name",label:"物品名称",width:"150"}),u(aa,{prop:"model",label:"型号规格",width:"150"}),u(aa,{prop:"quantity",label:"采购数量",width:"100",align:"center"}),u(aa,{label:"转为资产",width:"200",align:"center"},{default:o((e=>[u(na,{modelValue:e.row.toAssetQuantity,"onUpdate:modelValue":a=>e.row.toAssetQuantity=a,min:0,max:e.row.quantity,size:"small",style:{width:"80px"}},null,8,["modelValue","onUpdate:modelValue","max"]),t[34]||(t[34]=v("br",null,null,-1)),u(Ee,{modelValue:e.row.assetLocationId,"onUpdate:modelValue":a=>e.row.assetLocationId=a,placeholder:"选择位置",size:"small",style:{width:"100%","margin-top":"5px"},disabled:0===e.row.toAssetQuantity},{default:o((()=>[u(ze,{label:"办公室A",value:1}),u(ze,{label:"办公室B",value:2}),u(ze,{label:"会议室",value:3}),u(ze,{label:"机房",value:4})])),_:2},1032,["modelValue","onUpdate:modelValue","disabled"])])),_:1}),u(aa,{label:"转为备件",width:"200",align:"center"},{default:o((e=>[u(na,{modelValue:e.row.toSparePartQuantity,"onUpdate:modelValue":a=>e.row.toSparePartQuantity=a,min:0,max:e.row.quantity,size:"small",style:{width:"80px"}},null,8,["modelValue","onUpdate:modelValue","max"]),t[35]||(t[35]=v("br",null,null,-1)),u(Ee,{modelValue:e.row.sparePartLocationId,"onUpdate:modelValue":a=>e.row.sparePartLocationId=a,placeholder:"选择库位",size:"small",style:{width:"100%","margin-top":"5px"},disabled:0===e.row.toSparePartQuantity},{default:o((()=>[u(ze,{label:"备件库A区",value:1}),u(ze,{label:"备件库B区",value:2}),u(ze,{label:"备件库C区",value:3})])),_:2},1032,["modelValue","onUpdate:modelValue","disabled"])])),_:1}),u(aa,{label:"剩余数量",width:"100",align:"center"},{default:o((e=>[v("span",{class:D({"text-danger":e.row.quantity-e.row.toAssetQuantity-e.row.toSparePartQuantity!=0})},f(e.row.quantity-e.row.toAssetQuantity-e.row.toSparePartQuantity),3)])),_:1})])),_:1},8,["data"])]),v("div",Pe,[u(ia,{title:"转化说明",type:"info",closable:!1,"show-icon":""},{default:o((()=>t[37]||(t[37]=[v("ul",null,[v("li",null,"每个物品的转化数量总和必须等于采购数量"),v("li",null,"转为资产：物品将作为固定资产进行管理，生成资产编号"),v("li",null,"转为备件：物品将进入备品备件库存，用于维修和更换"),v("li",null,"可以同时转化为资产和备件，按需分配数量")],-1)]))),_:1})])])]})),_:1},8,["modelValue"]),u(oa,{modelValue:Le.value,"onUpdate:modelValue":t[16]||(t[16]=e=>Le.value=e),title:"新建采购",width:"800px","close-on-click-modal":!1,onClose:Re},{footer:o((()=>[v("div",Ce,[u(_,{onClick:t[15]||(t[15]=e=>Le.value=!1)},{default:o((()=>t[42]||(t[42]=[m("取消")]))),_:1}),u(_,{type:"primary",onClick:Oe,loading:Qe.value},{default:o((()=>t[43]||(t[43]=[m(" 确定 ")]))),_:1},8,["loading"])])])),default:o((()=>[u(Ze,{ref_key:"createFormRef",ref:je,model:Be,rules:Fe,"label-width":"120px","label-position":"right"},{default:o((()=>[u(ra,{gutter:20},{default:o((()=>[u(sa,{span:12},{default:o((()=>[u(k,{label:"采购名称",prop:"name"},{default:o((()=>[u(V,{modelValue:Be.name,"onUpdate:modelValue":t[10]||(t[10]=e=>Be.name=e),placeholder:"请输入采购名称",maxlength:"100","show-word-limit":""},null,8,["modelValue"])])),_:1})])),_:1}),u(sa,{span:12},{default:o((()=>[u(k,{label:"采购类型",prop:"type"},{default:o((()=>[u(Ee,{modelValue:Be.type,"onUpdate:modelValue":t[11]||(t[11]=e=>Be.type=e),placeholder:"请选择采购类型",style:{width:"100%"}},{default:o((()=>[u(ze,{label:"设备采购",value:"equipment"}),u(ze,{label:"软件采购",value:"software"}),u(ze,{label:"服务采购",value:"service"}),u(ze,{label:"备件采购",value:"spare_parts"})])),_:1},8,["modelValue"])])),_:1})])),_:1})])),_:1}),u(ra,{gutter:20},{default:o((()=>[u(sa,{span:12},{default:o((()=>[u(k,{label:"供应商",prop:"supplierId"},{default:o((()=>[u(Ee,{modelValue:Be.supplierId,"onUpdate:modelValue":t[12]||(t[12]=e=>Be.supplierId=e),placeholder:"请选择供应商",style:{width:"100%"},filterable:"","allow-create":"",onChange:Me},{default:o((()=>[(i(!0),d(C,null,I(w.value,(e=>(i(),n(ze,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1})])),_:1}),u(sa,{span:12},{default:o((()=>[u(k,{label:"预计金额",prop:"estimatedAmount"},{default:o((()=>[u(na,{modelValue:Be.estimatedAmount,"onUpdate:modelValue":t[13]||(t[13]=e=>Be.estimatedAmount=e),min:0,precision:2,placeholder:"请输入预计金额",style:{width:"100%"}},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),u(k,{label:"采购说明",prop:"description"},{default:o((()=>[u(V,{modelValue:Be.description,"onUpdate:modelValue":t[14]||(t[14]=e=>Be.description=e),type:"textarea",rows:3,placeholder:"请输入采购说明",maxlength:"500","show-word-limit":""},null,8,["modelValue"])])),_:1}),u(k,{label:"采购物品"},{default:o((()=>[v("div",ke,[(i(!0),d(C,null,I(Be.items,((e,a)=>(i(),d("div",{key:a,class:"purchase-item"},[u(ra,{gutter:10,align:"middle",style:{"margin-bottom":"10px"}},{default:o((()=>[u(sa,{span:5},{default:o((()=>[u(Ee,{modelValue:e.sparePartId,"onUpdate:modelValue":a=>e.sparePartId=a,placeholder:"选择备件",filterable:"",remote:"","remote-method":e=>(async e=>{if(e){B.value=!0;try{const a=await L(e);a.success?Q.value=a.data||[]:Q.value=[]}catch(a){Q.value=[]}finally{B.value=!1}}else Q.value=[]})(e),loading:B.value,onChange:e=>((e,a)=>{const t=Q.value.find((a=>a.id===e));if(t){const e=Be.items[a];e.materialNumber=t.materialNumber||t.code,e.name=t.name,e.specification=t.specification||""}})(e,a),style:{width:"100%"}},{default:o((()=>[(i(!0),d(C,null,I(Q.value,(e=>(i(),n(ze,{key:e.id,label:`${e.materialNumber||e.code} - ${e.name}`,value:e.id},null,8,["label","value"])))),128))])),_:2},1032,["modelValue","onUpdate:modelValue","remote-method","loading","onChange"])])),_:2},1024),u(sa,{span:4},{default:o((()=>[u(V,{modelValue:e.materialNumber,"onUpdate:modelValue":a=>e.materialNumber=a,placeholder:"物料编号",readonly:"",style:{"background-color":"#f5f7fa"}},null,8,["modelValue","onUpdate:modelValue"])])),_:2},1024),u(sa,{span:5},{default:o((()=>[u(V,{modelValue:e.name,"onUpdate:modelValue":a=>e.name=a,placeholder:"物品名称",maxlength:"100"},null,8,["modelValue","onUpdate:modelValue"])])),_:2},1024),u(sa,{span:3},{default:o((()=>[u(na,{modelValue:e.quantity,"onUpdate:modelValue":a=>e.quantity=a,min:1,placeholder:"数量",style:{width:"100%"}},null,8,["modelValue","onUpdate:modelValue"])])),_:2},1024),u(sa,{span:3},{default:o((()=>[u(na,{modelValue:e.unitPrice,"onUpdate:modelValue":a=>e.unitPrice=a,min:0,precision:2,placeholder:"单价",style:{width:"100%"}},null,8,["modelValue","onUpdate:modelValue"])])),_:2},1024),u(sa,{span:2},{default:o((()=>[u(_,{type:"danger",size:"small",icon:g(S),onClick:e=>(e=>{Be.items.length>1&&Be.items.splice(e,1)})(a),disabled:Be.items.length<=1},{default:o((()=>t[40]||(t[40]=[m(" 删除 ")]))),_:2},1032,["icon","onClick","disabled"])])),_:2},1024)])),_:2},1024),u(ra,{gutter:10,style:{"margin-bottom":"10px"}},{default:o((()=>[u(sa,{span:12},{default:o((()=>[u(V,{modelValue:e.specification,"onUpdate:modelValue":a=>e.specification=a,placeholder:"规格型号",maxlength:"200"},null,8,["modelValue","onUpdate:modelValue"])])),_:2},1024),u(sa,{span:12},{default:o((()=>[u(V,{modelValue:e.notes,"onUpdate:modelValue":a=>e.notes=a,placeholder:"备注",maxlength:"200"},null,8,["modelValue","onUpdate:modelValue"])])),_:2},1024)])),_:2},1024)])))),128)),u(_,{type:"primary",size:"small",icon:g(P),onClick:He,style:{"margin-top":"10px"}},{default:o((()=>t[41]||(t[41]=[m(" 添加物品 ")]))),_:1},8,["icon"])])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue"]),u(te,{modelValue:F.value,"onUpdate:modelValue":t[17]||(t[17]=e=>F.value=e),"purchase-id":E.value,onClose:t[18]||(t[18]=e=>F.value=!1)},null,8,["modelValue","purchase-id"])])}}},[["__scopeId","data-v-fc2ed260"]]);export{Ie as default};
