<template>
  <el-dialog
    v-model="visible"
    title="工作汇总报告"
    width="90%"
    :before-close="handleClose"
    class="work-summary-dialog"
  >
    <template #header>
      <div class="dialog-header">
        <div class="header-left">
          <el-icon class="header-icon"><DataAnalysis /></el-icon>
          <span class="dialog-title">工作汇总报告</span>
          <el-tag type="info" size="small" class="period-tag">
            {{ getPeriodLabel(periodType) }}
          </el-tag>
        </div>
        <div class="header-right">
          <el-select v-model="periodType" @change="loadWorkSummary" size="small" class="period-select">
            <el-option label="今日" value="daily" />
            <el-option label="本周" value="weekly" />
            <el-option label="本月" value="monthly" />
            <el-option label="本季度" value="quarterly" />
            <el-option label="本年" value="yearly" />
          </el-select>
          <el-button
            size="small"
            type="primary"
            :icon="Refresh"
            @click="loadWorkSummary"
            :loading="loading"
          >
            刷新
          </el-button>
          <el-button
            size="small"
            type="success"
            :icon="Refresh"
            @click="forceRefreshWorkSummary"
            :loading="refreshing"
          >
            强制刷新
          </el-button>
        </div>
      </div>
    </template>

    <div class="dialog-content">
      <!-- 统计概览 -->
      <div class="summary-stats">
        <el-row :gutter="16">
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-value">{{ workSummary.length }}</div>
              <div class="stat-label">参与人数</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-value">{{ totalTasks }}</div>
              <div class="stat-label">总任务数</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-value">{{ totalPoints }}</div>
              <div class="stat-label">总积分</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-value">{{ avgProductivity.toFixed(1) }}</div>
              <div class="stat-label">平均生产力</div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 工作汇总表格 -->
      <el-table
        :data="workSummary"
        v-loading="loading"
        stripe
        border
        height="400"
        :default-sort="{ prop: 'pointsRank', order: 'ascending' }"
        class="summary-table"
        row-key="userId"
      >
        <el-table-column type="index" label="#" width="50" align="center" fixed="left" />
        <el-table-column prop="userName" label="姓名" width="100" fixed="left" />
        
        <!-- 任务统计组 -->
        <el-table-column label="📋 任务" align="center">
          <el-table-column prop="tasksCompleted" label="完成" width="60" align="center" />
          <el-table-column prop="tasksCreated" label="新建" width="60" align="center" />
          <el-table-column prop="tasksClaimed" label="领取" width="60" align="center" />
          <el-table-column label="总计" width="60" align="center">
            <template #default="{ row }">
              {{ (row.tasksCompleted || 0) + (row.tasksCreated || 0) + (row.tasksClaimed || 0) }}
            </template>
          </el-table-column>
        </el-table-column>

        <!-- 资产统计组 -->
        <el-table-column label="🏢 资产" align="center">
          <el-table-column prop="assetsCreated" label="新建" width="60" align="center" />
          <el-table-column prop="assetsUpdated" label="更新" width="60" align="center" />
        </el-table-column>

        <!-- 故障统计组 -->
        <el-table-column label="🔧 故障" align="center">
          <el-table-column prop="faultsReported" label="登记" width="60" align="center" />
          <el-table-column prop="faultsRepaired" label="维修" width="60" align="center" />
        </el-table-column>

        <!-- 采购统计组 -->
        <el-table-column label="🛒 采购" align="center">
          <el-table-column prop="procurementsCreated" label="创建" width="60" align="center" />
          <el-table-column prop="procurementsUpdated" label="更新" width="60" align="center" />
        </el-table-column>

        <!-- 积分统计组 -->
        <el-table-column label="🏆 积分" align="center">
          <el-table-column prop="totalPointsEarned" label="积分" width="80" align="center">
            <template #default="{ row }">
              <span class="points-value">{{ row.totalPointsEarned || 0 }}</span>
            </template>
          </el-table-column>
        </el-table-column>

        <!-- 排名 -->
        <el-table-column prop="pointsRank" label="排名" width="60" align="center">
          <template #default="{ row }">
            <el-tag 
              :type="getRankTagType(row.pointsRank)" 
              size="small"
              round
            >
              {{ row.pointsRank || '-' }}
            </el-tag>
          </template>
        </el-table-column>

        <!-- 评价 -->
        <el-table-column prop="evaluation" label="评价" width="100" align="center">
          <template #default="{ row }">
            <el-tag 
              :type="getEvaluationTagType(row.evaluation)" 
              size="small"
              effect="light"
            >
              {{ row.evaluation || '待评价' }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="exportData">
          <el-icon><Download /></el-icon>
          导出数据
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { DataAnalysis, Refresh, Download } from '@element-plus/icons-vue'
import { statisticsApi } from '@/api/statistics'
import { notificationService } from '@/utils/notification-service'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:modelValue'])

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const loading = ref(false)
const refreshing = ref(false)
const periodType = ref('weekly')
const workSummary = ref([])

// 计算属性
const totalTasks = computed(() => {
  return workSummary.value.reduce((sum, item) =>
    sum + (item.tasksCompleted || 0) + (item.tasksCreated || 0) + (item.tasksClaimed || 0), 0)
})

const totalPoints = computed(() => {
  return workSummary.value.reduce((sum, item) => sum + (item.totalPointsEarned || 0), 0)
})

const avgProductivity = computed(() => {
  if (workSummary.value.length === 0) return 0
  const totalProductivity = workSummary.value.reduce((sum, item) => 
    sum + (item.productivityScore || 0), 0)
  return totalProductivity / workSummary.value.length
})

// 方法
const getPeriodLabel = (type) => {
  const labels = {
    daily: '今日',
    weekly: '本周',
    monthly: '本月',
    quarterly: '本季度',
    yearly: '本年'
  }
  return labels[type] || '未知'
}

const loadWorkSummary = async (forceRefresh = false) => {
  loading.value = true
  try {
    const response = await statisticsApi.getWorkSummary({
      periodType: periodType.value,
      limit: 100,
      forceRefresh: forceRefresh
    })

    console.log('工作汇总API响应:', response)

    // 由于request.js已经返回了response.data，所以直接检查response.success
    if (response.success) {
      workSummary.value = response.data || []
      console.log('工作汇总数据加载成功:', workSummary.value)

      if (forceRefresh) {
        ElMessage.success(`数据已刷新，加载 ${workSummary.value.length} 条记录`)
      }
    } else {
      throw new Error(response.message || '获取工作汇总失败')
    }
  } catch (error) {
    console.error('获取工作汇总失败:', error)
    ElMessage.error('获取工作汇总失败')
    // 使用模拟数据
    workSummary.value = generateMockData()
  } finally {
    loading.value = false
  }
}

// 强制刷新工作汇总数据（清除缓存）
const forceRefreshWorkSummary = async () => {
  refreshing.value = true
  try {
    // 方案1: 先调用后端更新数据，再获取最新数据
    console.log('开始强制刷新工作汇总数据...')

    // 先更新后端数据
    const updateResponse = await statisticsApi.updateWorkSummary({
      periodType: periodType.value
    })

    console.log('后端数据更新响应:', updateResponse)

    // 然后获取最新数据（强制刷新，跳过缓存）
    await loadWorkSummary(true)

  } catch (error) {
    console.error('强制刷新失败:', error)
    ElMessage.error('强制刷新失败，尝试普通刷新')
    // 如果强制刷新失败，尝试普通刷新
    await loadWorkSummary(false)
  } finally {
    refreshing.value = false
  }
}

const generateMockData = () => {
  return [
    {
      userName: '张三',
      tasksCompleted: 12,
      tasksCreated: 8,
      assetsCreated: 5,
      assetsUpdated: 2,
      faultsReported: 3,
      faultsRepaired: 1,
      totalPointsEarned: 156,
      pointsRank: 1,
      evaluation: '超级',
      productivityScore: 85.5
    },
    {
      userName: '李四',
      tasksCompleted: 9,
      tasksCreated: 15,
      assetsCreated: 3,
      assetsUpdated: 4,
      faultsReported: 1,
      faultsRepaired: 2,
      totalPointsEarned: 142,
      pointsRank: 2,
      evaluation: '优秀',
      productivityScore: 78.2
    }
  ]
}

const getRankTagType = (rank) => {
  if (rank === 1) return 'danger'
  if (rank === 2) return 'warning'
  if (rank === 3) return 'success'
  return 'info'
}

const getEvaluationTagType = (evaluation) => {
  switch (evaluation) {
    case '超级': return 'danger'
    case '优秀': return 'success'
    case '良好': return 'warning'
    case '一般': return 'info'
    default: return ''
  }
}

const exportData = () => {
  ElMessage.info('导出功能开发中...')
}

const handleClose = () => {
  visible.value = false
}

// 监听弹窗打开，自动刷新最新数据
watch(visible, (newVal) => {
  if (newVal) {
    // 弹窗打开时强制刷新最新数据，不使用缓存
    forceRefreshWorkSummary()
    // 注册实时更新监听
    registerRealtimeUpdates()
  } else {
    // 弹窗关闭时取消监听
    unregisterRealtimeUpdates()
  }
})

// 实时更新相关方法
const registerRealtimeUpdates = () => {
  if (notificationService.isConnected) {
    // 监听工作汇总更新事件
    notificationService.connection?.on('WorkSummaryUpdated', handleWorkSummaryUpdate)
    console.log('已注册工作汇总实时更新监听')
  }
}

const unregisterRealtimeUpdates = () => {
  if (notificationService.isConnected) {
    // 取消监听工作汇总更新事件
    notificationService.connection?.off('WorkSummaryUpdated', handleWorkSummaryUpdate)
    console.log('已取消工作汇总实时更新监听')
  }
}

const handleWorkSummaryUpdate = (data) => {
  console.log('收到工作汇总实时更新:', data)

  if (data.Type === 'leaderboard_update' && data.Data) {
    // 更新排行榜数据
    workSummary.value = data.Data
    ElMessage.success('数据已实时更新')
  } else if (data.Type === 'data_refresh') {
    // 数据刷新，重新加载
    loadWorkSummary(true)
  }
}

// 生命周期钩子
onMounted(() => {
  // 组件挂载时不自动加载，等待弹窗打开
})

onUnmounted(() => {
  // 组件卸载时取消所有监听
  unregisterRealtimeUpdates()
})
</script>

<style scoped>
.work-summary-dialog {
  --el-dialog-border-radius: 12px;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-icon {
  color: #409EFF;
  font-size: 18px;
}

.dialog-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.period-tag {
  margin-left: 8px;
}

.header-right {
  display: flex;
  gap: 8px;
  align-items: center;
}

.period-select {
  width: 100px;
}

.dialog-content {
  padding: 0;
}

.summary-stats {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.stat-card {
  text-align: center;
  padding: 12px;
  background: white;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #409EFF;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #909399;
}

.summary-table {
  font-size: 13px;
}

.points-value {
  font-weight: 600;
  color: #E6A23C;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

:deep(.el-dialog__header) {
  padding: 20px 20px 10px 20px;
}

:deep(.el-dialog__body) {
  padding: 10px 20px 20px 20px;
}

:deep(.el-table th) {
  background-color: #fafafa;
  font-weight: 600;
}

:deep(.el-table .el-table__cell) {
  padding: 6px 0;
}
</style>
