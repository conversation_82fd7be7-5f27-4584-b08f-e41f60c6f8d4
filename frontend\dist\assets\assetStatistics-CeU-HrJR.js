import{aV as t}from"./index-CG5lHOPO.js";const a="/v2/asset-statistics",r={async getOverallStatistics(r={}){var e;try{const n=await t.get(`${a}/overall`,{params:r});return(null==(e=n.data)?void 0:e.data)||n.data}catch(n){throw n}},async getStatisticsByType(r={}){var e;try{const n=await t.get(`${a}/by-type`,{params:r});return(null==(e=n.data)?void 0:e.data)||n.data}catch(n){throw n}},async getStatisticsByRegion(r={}){var e;try{const n={...r,LocationType:2},s=await t.get(`${a}/by-region`,{params:n});return(null==(e=s.data)?void 0:e.data)||s.data}catch(n){throw n}},async getStatisticsByDepartment(r={}){var e;try{const n=await t.get(`${a}/by-department`,{params:r});return(null==(e=n.data)?void 0:e.data)||n.data}catch(n){throw n}},async getWeeklyTrend(r={}){var e;try{const n=await t.get(`${a}/trend/weekly`,{params:r});return(null==(e=n.data)?void 0:e.data)||n.data}catch(n){throw n}},async getDailyTrend(r={}){var e;try{const n=await t.get(`${a}/trend/daily`,{params:r});return(null==(e=n.data)?void 0:e.data)||n.data}catch(n){throw n}},async getRegionOptions(){try{return(await t.get(`${a}/region-options`)).data||[]}catch(r){throw r}},async getDepartmentOptions(){try{return(await t.get(`${a}/department-options`)).data||[]}catch(r){throw r}},async getMonthlyTrend(r={}){var e;try{const n=await t.get(`${a}/trend/monthly`,{params:r});return(null==(e=n.data)?void 0:e.data)||n.data}catch(n){throw n}},async getCombinedStatistics(r={}){var e;try{const n=await t.get(`${a}/combined`,{params:r});return(null==(e=n.data)?void 0:e.data)||n.data}catch(n){throw n}},async getAssetTypes(){var r;try{const e=await t.get(`${a}/asset-types`);return(null==(r=e.data)?void 0:r.data)||e.data}catch(e){throw e}},async getRegions(){var r;try{const e=await t.get(`${a}/regions`);return(null==(r=e.data)?void 0:r.data)||e.data}catch(e){throw e}},async getDepartments(){var r;try{const e=await t.get(`${a}/departments`);return(null==(r=e.data)?void 0:r.data)||e.data}catch(e){throw e}}};export{r as a};
