/**
 * 资产统计分析API
 * 提供多维度资产统计数据接口
 * 使用V2 API规范
 */
import request from '@/utils/request'

const API_BASE = '/v2/asset-statistics'

export const assetStatisticsApi = {
  /**
   * 获取资产总体统计
   * @param {Object} params - 查询参数
   * @returns {Promise}
   */
  async getOverallStatistics(params = {}) {
    try {
      const response = await request.get(`${API_BASE}/overall`, { params })
      return response.data?.data || response.data
    } catch (error) {
      console.error('获取总体统计失败:', error)
      throw error
    }
  },

  /**
   * 获取按资产类型统计
   * @param {Object} params - 查询参数
   * @returns {Promise}
   */
  async getStatisticsByType(params = {}) {
    try {
      const response = await request.get(`${API_BASE}/by-type`, { params })
      return response.data?.data || response.data
    } catch (error) {
      console.error('获取类型统计失败:', error)
      throw error
    }
  },

  /**
   * 获取按区域统计 (位置类型=2的位置)
   * @param {Object} params - 查询参数
   * @returns {Promise}
   */
  async getStatisticsByRegion(params = {}) {
    try {
      // 添加位置类型参数，LocationType=2 表示工序级别的位置
      const queryParams = { ...params, LocationType: 2 }
      const response = await request.get(`${API_BASE}/by-region`, { params: queryParams })
      return response.data?.data || response.data
    } catch (error) {
      console.error('获取区域统计失败:', error)
      throw error
    }
  },

  /**
   * 获取按部门统计
   * @param {Object} params - 查询参数
   * @returns {Promise}
   */
  async getStatisticsByDepartment(params = {}) {
    try {
      const response = await request.get(`${API_BASE}/by-department`, { params })
      return response.data?.data || response.data
    } catch (error) {
      console.error('获取部门统计失败:', error)
      throw error
    }
  },

  /**
   * 获取趋势数据 - 按周
   * @param {Object} params - 查询参数
   * @returns {Promise}
   */
  async getWeeklyTrend(params = {}) {
    try {
      const response = await request.get(`${API_BASE}/trend/weekly`, { params })
      return response.data?.data || response.data
    } catch (error) {
      console.error('获取周趋势失败:', error)
      throw error
    }
  },

  /**
   * 获取趋势数据 - 按天
   * @param {Object} params - 查询参数
   * @returns {Promise}
   */
  async getDailyTrend(params = {}) {
    try {
      const response = await request.get(`${API_BASE}/trend/daily`, { params })
      return response.data?.data || response.data
    } catch (error) {
      console.error('获取日趋势失败:', error)
      throw error
    }
  },

  /**
   * 获取区域筛选选项（type=2的位置列表）
   * @returns {Promise}
   */
  async getRegionOptions() {
    try {
      const response = await request.get(`${API_BASE}/region-options`)
      return response.data || []
    } catch (error) {
      console.error('获取区域选项失败:', error)
      throw error
    }
  },

  /**
   * 获取部门筛选选项（基于资产位置的部门关联）
   * @returns {Promise}
   */
  async getDepartmentOptions() {
    try {
      const response = await request.get(`${API_BASE}/department-options`)
      return response.data || []
    } catch (error) {
      console.error('获取部门选项失败:', error)
      throw error
    }
  },

  /**
   * 获取趋势数据 - 按月
   * @param {Object} params - 查询参数
   * @returns {Promise}
   */
  async getMonthlyTrend(params = {}) {
    try {
      const response = await request.get(`${API_BASE}/trend/monthly`, { params })
      return response.data?.data || response.data
    } catch (error) {
      console.error('获取月趋势失败:', error)
      throw error
    }
  },

  /**
   * 获取组合统计数据 (区域+类型交叉统计)
   * @param {Object} params - 查询参数
   * @returns {Promise}
   */
  async getCombinedStatistics(params = {}) {
    try {
      const response = await request.get(`${API_BASE}/combined`, { params })
      return response.data?.data || response.data
    } catch (error) {
      console.error('获取组合统计失败:', error)
      throw error
    }
  },

  /**
   * 获取资产类型列表
   * @returns {Promise}
   */
  async getAssetTypes() {
    try {
      const response = await request.get(`${API_BASE}/asset-types`)
      return response.data?.data || response.data
    } catch (error) {
      console.error('获取资产类型失败:', error)
      throw error
    }
  },

  /**
   * 获取区域列表 (位置类型=0)
   * @returns {Promise}
   */
  async getRegions() {
    try {
      const response = await request.get(`${API_BASE}/regions`)
      return response.data?.data || response.data
    } catch (error) {
      console.error('获取区域列表失败:', error)
      throw error
    }
  },

  /**
   * 获取部门列表
   * @returns {Promise}
   */
  async getDepartments() {
    try {
      const response = await request.get(`${API_BASE}/departments`)
      return response.data?.data || response.data
    } catch (error) {
      console.error('获取部门列表失败:', error)
      throw error
    }
  }
}

export default assetStatisticsApi
