import{_ as e,r as a,ad as l,z as t,b as o,d as r,e as s,w as n,f as i,q as u,aJ as d,a as p,m as c,o as m,p as g,F as f,h,D as b,b7 as v,l as y,k as M,$ as _,t as T,E as w,a9 as z}from"./index-CG5lHOPO.js";const S={class:"audit-logs-container"},k={class:"page-header"},P={class:"page-actions"},x={class:"filter-container"},V={class:"log-details"},q={class:"detail-item"},D={class:"detail-value"},$={class:"detail-item"},C={class:"detail-value"},U={class:"detail-item"},N={class:"code-block"},E={key:0,class:"detail-item"},O={class:"code-block"},J={class:"pagination-container"},Y=e({__name:"logs",setup(e){const Y=a(!1),H=a([]),I=a(null),j=l({currentPage:1,pageSize:10,total:0}),B=l({username:"",operationType:"",status:"",operationTime:[]}),F=[{label:"查询",value:"query"},{label:"新增",value:"insert"},{label:"修改",value:"update"},{label:"删除",value:"delete"},{label:"导出",value:"export"},{label:"导入",value:"import"},{label:"登录",value:"login"},{label:"登出",value:"logout"}],G=[{label:"成功",value:"success"},{label:"失败",value:"error"}];t((()=>{L()}));const L=()=>{var e,a;Y.value=!0,j.currentPage,j.pageSize,B.username,B.operationType,B.status,null==(e=B.operationTime)||e[0],null==(a=B.operationTime)||a[1],setTimeout((()=>{const e=[];for(let a=0;a<Math.min(j.pageSize,124-(j.currentPage-1)*j.pageSize);a++){const l=(j.currentPage-1)*j.pageSize+a,t=["query","insert","update","delete","export","import","login","logout"][Math.floor(8*Math.random())],o=["success","error"][0===Math.floor(Math.random()*("query"===t?5:3))?1:0],r=["用户管理","角色管理","菜单管理","部门管理","资产管理","资产类型","位置管理","故障管理","采购管理","任务管理"],s=r[Math.floor(Math.random()*r.length)];let n="";"query"===t?n=`查询${s}列表数据`:"insert"===t?n=`新增${s}记录`:"update"===t?n=`修改${s}记录`:"delete"===t?n=`删除${s}记录`:"export"===t?n=`导出${s}数据`:"import"===t?n=`导入${s}数据`:"login"===t?n="用户登录系统":"logout"===t&&(n="用户退出系统");const i=new Date,u=new Date;u.setDate(i.getDate()-Math.floor(30*Math.random())),u.setHours(Math.floor(24*Math.random()),Math.floor(60*Math.random()),Math.floor(60*Math.random()));const d=u.toISOString().replace("T"," ").substring(0,19),p=Math.floor(1e3*Math.random())+10,c={pageNum:1,pageSize:10,id:Math.floor(1e3*Math.random())+1e3,name:`测试数据${Math.floor(100*Math.random())}`,status:["active","disabled"][Math.floor(2*Math.random())]};e.push({id:1e4+l,username:["admin","operator","system","test_user","zhang_san","li_si"][Math.floor(6*Math.random())],moduleName:s,operationType:t,operationDescription:n,operationContent:n+"，详细操作内容...",ip:`192.168.${Math.floor(255*Math.random())}.${Math.floor(255*Math.random())}`,method:["GET","POST","PUT","DELETE"]["query"===t?0:"insert"===t?1:"update"===t?2:3],requestParams:c,operationTime:d,status:o,executionTime:p,errorMessage:"error"===o?"操作失败："+["参数错误","权限不足","数据不存在","系统异常","网络超时"][Math.floor(5*Math.random())]:""})}H.value=e,j.total=124,Y.value=!1}),500)},A=()=>{j.currentPage=1,L()},K=()=>{B.username="",B.operationType="",B.status="",B.operationTime=[],j.currentPage=1,L()},Q=e=>{j.pageSize=e,L()},R=e=>{j.currentPage=e,L()},W=()=>{w.success("开始导出数据，请稍候...")},X=()=>{z.confirm("确定要清理30天前的日志数据吗？此操作不可恢复！","清理日志",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{w.success("日志清理成功"),L()})).catch((()=>{}))},Z=e=>({query:"查询",insert:"新增",update:"修改",delete:"删除",export:"导出",import:"导入",login:"登录",logout:"登出"}[e]||"未知"),ee=e=>{try{return"string"==typeof e?JSON.stringify(JSON.parse(e),null,2):JSON.stringify(e,null,2)}catch(a){return e}};return(e,a)=>{const l=p("el-button"),t=p("el-input"),w=p("el-form-item"),z=p("el-option"),L=p("el-select"),ae=p("el-date-picker"),le=p("el-form"),te=p("el-card"),oe=p("el-table-column"),re=p("el-tag"),se=p("el-table"),ne=p("el-pagination"),ie=c("loading");return m(),o("div",S,[r("div",k,[a[8]||(a[8]=r("h2",{class:"page-title"},"审计日志",-1)),r("div",P,[s(l,{type:"primary",onClick:W,icon:i(u)},{default:n((()=>a[6]||(a[6]=[g(" 导出数据 ")]))),_:1},8,["icon"]),s(l,{type:"warning",onClick:X,icon:i(d)},{default:n((()=>a[7]||(a[7]=[g(" 清理日志 ")]))),_:1},8,["icon"])])]),s(te,{class:"filter-card"},{default:n((()=>[r("div",x,[s(le,{inline:!0,model:B,class:"filter-form"},{default:n((()=>[s(w,{label:"操作用户"},{default:n((()=>[s(t,{modelValue:B.username,"onUpdate:modelValue":a[0]||(a[0]=e=>B.username=e),placeholder:"操作用户",clearable:""},null,8,["modelValue"])])),_:1}),s(w,{label:"操作类型"},{default:n((()=>[s(L,{modelValue:B.operationType,"onUpdate:modelValue":a[1]||(a[1]=e=>B.operationType=e),placeholder:"全部类型",clearable:""},{default:n((()=>[(m(),o(f,null,h(F,(e=>s(z,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),64))])),_:1},8,["modelValue"])])),_:1}),s(w,{label:"操作状态"},{default:n((()=>[s(L,{modelValue:B.status,"onUpdate:modelValue":a[2]||(a[2]=e=>B.status=e),placeholder:"全部状态",clearable:""},{default:n((()=>[(m(),o(f,null,h(G,(e=>s(z,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),64))])),_:1},8,["modelValue"])])),_:1}),s(w,{label:"操作时间"},{default:n((()=>[s(ae,{modelValue:B.operationTime,"onUpdate:modelValue":a[3]||(a[3]=e=>B.operationTime=e),type:"datetimerange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue"])])),_:1}),s(w,null,{default:n((()=>[s(l,{type:"primary",onClick:A,icon:i(b)},{default:n((()=>a[9]||(a[9]=[g(" 搜索 ")]))),_:1},8,["icon"]),s(l,{onClick:K,icon:i(v)},{default:n((()=>a[10]||(a[10]=[g(" 重置 ")]))),_:1},8,["icon"])])),_:1})])),_:1},8,["model"])])])),_:1}),s(te,{class:"data-card"},{default:n((()=>[y((m(),M(se,{ref_key:"logTable",ref:I,data:H.value,border:"",style:{width:"100%"}},{default:n((()=>[s(oe,{type:"expand"},{default:n((e=>[r("div",V,[r("div",q,[a[11]||(a[11]=r("span",{class:"detail-label"},"操作内容：",-1)),r("span",D,T(e.row.operationContent),1)]),r("div",$,[a[12]||(a[12]=r("span",{class:"detail-label"},"请求方法：",-1)),r("span",C,T(e.row.method),1)]),r("div",U,[a[13]||(a[13]=r("span",{class:"detail-label"},"请求参数：",-1)),r("div",N,[r("pre",null,T(ee(e.row.requestParams)),1)])]),"error"===e.row.status?(m(),o("div",E,[a[14]||(a[14]=r("span",{class:"detail-label"},"错误信息：",-1)),r("div",O,[r("pre",null,T(e.row.errorMessage),1)])])):_("",!0)])])),_:1}),s(oe,{prop:"id",label:"日志编号",width:"100"}),s(oe,{prop:"username",label:"操作用户",width:"120"}),s(oe,{prop:"moduleName",label:"操作模块",width:"120"}),s(oe,{prop:"operationType",label:"操作类型",width:"100"},{default:n((e=>{return[s(re,{type:(a=e.row.operationType,{query:"info",insert:"success",update:"warning",delete:"danger",export:"primary",import:"primary",login:"success",logout:"info"}[a]||""),size:"small"},{default:n((()=>[g(T(Z(e.row.operationType)),1)])),_:2},1032,["type"])];var a})),_:1}),s(oe,{prop:"operationDescription",label:"操作描述","min-width":"200","show-overflow-tooltip":""}),s(oe,{prop:"ip",label:"IP地址",width:"140"}),s(oe,{prop:"operationTime",label:"操作时间",width:"170",sortable:""}),s(oe,{prop:"status",label:"状态",width:"100",align:"center"},{default:n((e=>["success"===e.row.status?(m(),M(re,{key:0,type:"success"},{default:n((()=>a[15]||(a[15]=[g("成功")]))),_:1})):"error"===e.row.status?(m(),M(re,{key:1,type:"danger"},{default:n((()=>a[16]||(a[16]=[g("失败")]))),_:1})):_("",!0)])),_:1}),s(oe,{prop:"executionTime",label:"执行时长",width:"100",align:"center"},{default:n((e=>[g(T(e.row.executionTime)+" ms ",1)])),_:1})])),_:1},8,["data"])),[[ie,Y.value]]),r("div",J,[s(ne,{"current-page":j.currentPage,"onUpdate:currentPage":a[4]||(a[4]=e=>j.currentPage=e),"page-size":j.pageSize,"onUpdate:pageSize":a[5]||(a[5]=e=>j.pageSize=e),"page-sizes":[10,20,50,100],background:!0,layout:"total, sizes, prev, pager, next, jumper",total:j.total,onSizeChange:Q,onCurrentChange:R},null,8,["current-page","page-size","total"])])])),_:1})])}}},[["__scopeId","data-v-7f6f8f6a"]]);export{Y as default};
