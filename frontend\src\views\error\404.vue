/**
 * 404错误页面
 * 文件路径: src/views/error/404.vue
 * 功能描述: 系统404页面，当路由不存在时显示
 */

<template>
  <div class="error-page">
    <div class="error-container">
      <div class="error-image">
        <el-image :src="'/images/404.svg'" alt="404" class="error-img"></el-image>
      </div>
      <h1 class="error-title">404</h1>
      <p class="error-message">抱歉，您访问的页面不存在</p>
      <div class="error-actions">
        <el-button type="primary" @click="goBack">返回上一页</el-button>
        <el-button @click="goHome">返回首页</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'

const router = useRouter()

const goBack = () => {
  router.go(-1)
}

const goHome = () => {
  router.push('/')
}
</script>

<style lang="scss" scoped>
.error-page {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #f5f7fa;
  
  .error-container {
    text-align: center;
    padding: 40px;
    max-width: 500px;
    
    .error-image {
      margin-bottom: 30px;
      
      .error-img {
        max-width: 100%;
        height: auto;
      }
    }
    
    .error-title {
      font-size: 72px;
      color: #409EFF;
      margin: 0 0 20px;
      line-height: 1;
    }
    
    .error-message {
      font-size: 20px;
      color: #606266;
      margin-bottom: 30px;
    }
    
    .error-actions {
      display: flex;
      justify-content: center;
      gap: 16px;
    }
  }
}

@media (max-width: 576px) {
  .error-page {
    .error-container {
      padding: 20px;
      
      .error-title {
        font-size: 60px;
      }
      
      .error-message {
        font-size: 16px;
      }
    }
  }
}
</style> 