<template>
  <div v-if="!item.hidden">
    <!-- 外部链接 -->
    <template v-if="isExternal(item.path)">
      <a :href="item.path" target="_blank" rel="noopener">
        <el-menu-item :index="item.path">
          <el-icon v-if="item.meta && item.meta.icon"><component :is="item.meta.icon" /></el-icon>
          <span>{{ item.meta.title }}</span>
        </el-menu-item>
      </a>
    </template>
    
    <!-- 一级菜单 -->
    <template v-else>
      <!-- 有子菜单的情况 -->
      <template v-if="hasOneShowingChild(item.children, item) && !onlyOneChild.children">
        <router-link v-if="onlyOneChild.meta" :to="resolvePath(onlyOneChild.path)">
          <el-menu-item :index="resolvePath(onlyOneChild.path)">
            <el-icon v-if="onlyOneChild.meta && onlyOneChild.meta.icon"><component :is="onlyOneChild.meta.icon" /></el-icon>
            <span>{{ onlyOneChild.meta.title }}</span>
          </el-menu-item>
        </router-link>
      </template>
      
      <!-- 有多个子菜单的情况 -->
      <el-sub-menu v-else :index="resolvePath(item.path)" popper-append-to-body>
        <template #title>
          <el-icon v-if="item.meta && item.meta.icon"><component :is="item.meta.icon" /></el-icon>
          <span>{{ item.meta.title }}</span>
        </template>
        
        <!-- 递归渲染子菜单 -->
        <sidebar-item
          v-for="child in item.children"
          :key="child.path"
          :item="child"
          :base-path="resolvePath(child.path)"
        />
      </el-sub-menu>
    </template>
  </div>
</template>

<script>
import path from 'path-browserify'
import { isExternal } from '@/utils/validate'
import { ref } from 'vue'

export default {
  name: 'SidebarItem',
  props: {
    // 菜单项对象
    item: {
      type: Object,
      required: true
    },
    // 基础路径
    basePath: {
      type: [String, Object],
      default: ''
    }
  },
  setup(props) {
    const onlyOneChild = ref(null)
    
    const hasOneShowingChild = (children = [], parent) => {
      if (!children) {
        children = []
      }
      
      const showingChildren = children.filter(item => {
        if (item.hidden) {
          return false
        } else {
          // Set onlyOneChild to the only showing child
          onlyOneChild.value = item
          return true
        }
      })
      
      // When there is only one child, return true by default
      if (showingChildren.length === 1) {
        return true
      }
      
      // If there is no child shown, show parent
      if (showingChildren.length === 0) {
        onlyOneChild.value = { ...parent, path: '', noShowingChildren: true }
        return true
      }
      
      return false
    }
    
    const resolvePath = (routePath) => {
      if (isExternal(routePath)) {
        return routePath
      }
      
      // 如果basePath不是字符串，直接返回routePath
      if (typeof props.basePath !== 'string') {
        return routePath
      }
      
      if (isExternal(props.basePath)) {
        return props.basePath
      }
      
      return path.resolve(props.basePath, routePath)
    }
    
    return {
      onlyOneChild,
      hasOneShowingChild,
      resolvePath,
      isExternal
    }
  }
}
</script> 