-- 修复 user_work_summary 表结构
-- 添加缺失的 tasks_updated 字段

-- 检查表是否存在
SELECT 'Checking user_work_summary table structure...' as Status;

-- 检查 tasks_updated 字段是否存在
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'user_work_summary' 
  AND COLUMN_NAME = 'tasks_updated';

-- 如果字段不存在，则添加
SET @sql = '';
SELECT COUNT(*) INTO @col_exists 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'user_work_summary' 
  AND COLUMN_NAME = 'tasks_updated';

-- 动态添加字段
SET @sql = IF(@col_exists = 0, 
    'ALTER TABLE user_work_summary ADD COLUMN tasks_updated INT NOT NULL DEFAULT 0 COMMENT ''更新任务数'' AFTER tasks_claimed;',
    'SELECT ''tasks_updated 字段已存在'' as Status;'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 验证字段是否添加成功
SELECT 'Verifying tasks_updated field...' as Status;

SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'user_work_summary' 
  AND COLUMN_NAME = 'tasks_updated';

-- 显示完整的表结构
SELECT 'Complete table structure:' as Status;

SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'user_work_summary'
ORDER BY ORDINAL_POSITION;

-- 完成提示
SELECT '✅ user_work_summary 表结构修复完成！' as Message;
SELECT '✅ tasks_updated 字段已添加或已存在' as Status;
