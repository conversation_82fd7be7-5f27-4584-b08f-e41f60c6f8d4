// File: Models/Enums/TaskPriority.cs
// Description: 任务优先级枚举

using System.ComponentModel.DataAnnotations;

namespace ItAssetsSystem.Models.Enums
{
    /// <summary>
    /// 任务优先级枚举
    /// </summary>
    public enum TaskPriority
    {
        /// <summary>
        /// 低优先级
        /// </summary>
        [Display(Name = "低")]
        Low = 0,

        /// <summary>
        /// 中优先级
        /// </summary>
        [Display(Name = "中")]
        Medium = 1,

        /// <summary>
        /// 高优先级
        /// </summary>
        [Display(Name = "高")]
        High = 2,

        /// <summary>
        /// 紧急
        /// </summary>
        [Display(Name = "紧急")]
        Urgent = 3,

        /// <summary>
        /// 关键任务
        /// </summary>
        [Display(Name = "关键")]
        Critical = 4
    }
} 