import{_ as e,D as a,aG as l,b as t,d as i,e as s,w as o,r as d,ad as c,c as n,z as r,a as m,E as u,o as p,t as v,p as h,F as f,h as b,i as y,$ as g}from"./index-CG5lHOPO.js";const w={class:"achievements-container"},_={class:"stat-content"},V={class:"stat-info"},R={class:"stat-value"},A={class:"stat-content"},C={class:"stat-info"},F={class:"stat-value"},U={class:"stat-content"},k={class:"stat-info"},T={class:"stat-value"},q={class:"stat-content"},N={class:"stat-info"},E={class:"stat-value"},D={class:"achievements-header"},S={class:"filter-controls"},x={class:"achievements-grid"},I={class:"achievement-icon"},z=["src"],O={key:1,class:"default-icon"},Q={class:"achievement-info"},H={class:"achievement-name"},M={class:"achievement-description"},P={class:"achievement-meta"},j={class:"achievement-rewards"},L={key:0,class:"reward-item"},K={key:1,class:"reward-item"},G={key:2,class:"reward-item"},X={class:"achievement-progress"},Y={class:"progress-text"},$={class:"achievement-actions"},B={class:"user-achievements-section"},J={class:"search-controls"},W={class:"statistics-section"},Z={ref:"categoryChartRef",style:{height:"300px"}},ee={ref:"trendChartRef",style:{height:"300px"}};const ae=e({name:"AchievementsManagement",components:{Plus:l,Search:a},setup(){const e=d("list"),a=d(""),l=d(""),t=d(""),i=c({totalAchievements:12,activeUsers:25,completedToday:8,completionRate:65}),s=d([{id:1,code:"FIRST_TASK",name:"初出茅庐",description:"完成第一个任务",category:"Task",difficulty:"Easy",requiredCount:1,pointsReward:10,coinsReward:5,diamondsReward:0,iconUrl:"",isRepeatable:!1,isActive:!0,completedCount:25},{id:2,code:"TASK_MASTER_10",name:"任务达人",description:"累计完成10个任务",category:"Task",difficulty:"Normal",requiredCount:10,pointsReward:50,coinsReward:25,diamondsReward:1,iconUrl:"",isRepeatable:!1,isActive:!0,completedCount:18},{id:3,code:"QUALITY_EXPERT",name:"质量专家",description:"连续完成5个高质量任务",category:"Quality",difficulty:"Hard",requiredCount:5,pointsReward:100,coinsReward:50,diamondsReward:3,iconUrl:"",isRepeatable:!0,isActive:!0,completedCount:8},{id:4,code:"INNOVATION_PIONEER",name:"创新先锋",description:"提出创新解决方案",category:"Innovation",difficulty:"Epic",requiredCount:1,pointsReward:200,coinsReward:100,diamondsReward:5,iconUrl:"",isRepeatable:!0,isActive:!0,completedCount:3}]),o=d([{id:1,userName:"张三",department:"研发部",achievementName:"初出茅庐",achievementCategory:"Task",progress:1,requiredCount:1,isCompleted:!0,completedAt:"2024-01-15 10:30:00"},{id:2,userName:"李四",department:"产品部",achievementName:"任务达人",achievementCategory:"Task",progress:8,requiredCount:10,isCompleted:!1,completedAt:null}]),m=d([{name:"初出茅庐",category:"Task",completedCount:25,completionRate:100},{name:"任务达人",category:"Task",completedCount:18,completionRate:72},{name:"质量专家",category:"Quality",completedCount:8,completionRate:32},{name:"创新先锋",category:"Innovation",completedCount:3,completionRate:12}]),p=d(!1),v=d(!1),h=d(!1),f=d(),b=d([]),y=c({code:"",name:"",description:"",category:"Task",difficulty:"Normal",requiredCount:1,pointsReward:0,coinsReward:0,diamondsReward:0,iconUrl:"",isRepeatable:!1,isActive:!0}),g=n((()=>s.value.filter((e=>{const t=!a.value||e.category===a.value,i=!l.value||e.difficulty===l.value;return t&&i})))),w=n((()=>h.value?"编辑成就":"添加成就"));return r((()=>{})),{activeTab:e,categoryFilter:a,difficultyFilter:l,userSearch:t,achievementStats:i,achievements:s,userAchievements:o,popularAchievements:m,achievementDialogVisible:p,userAchievementDialogVisible:v,isEditMode:h,achievementFormRef:f,selectedAchievementUsers:b,achievementForm:y,achievementFormRules:{code:[{required:!0,message:"请输入成就代码",trigger:"blur"}],name:[{required:!0,message:"请输入成就名称",trigger:"blur"}],description:[{required:!0,message:"请输入成就描述",trigger:"blur"}],category:[{required:!0,message:"请选择成就分类",trigger:"change"}],difficulty:[{required:!0,message:"请选择难度等级",trigger:"change"}],requiredCount:[{required:!0,message:"请输入所需次数",trigger:"blur"}]},filteredAchievements:g,achievementDialogTitle:w,getDifficultyType:e=>({Easy:"success",Normal:"info",Hard:"warning",Epic:"danger"}[e]||"info"),showAddAchievementDialog:()=>{h.value=!1,Object.assign(y,{code:"",name:"",description:"",category:"Task",difficulty:"Normal",requiredCount:1,pointsReward:0,coinsReward:0,diamondsReward:0,iconUrl:"",isRepeatable:!1,isActive:!0}),p.value=!0},editAchievement:e=>{h.value=!0,Object.assign(y,e),p.value=!0},saveAchievement:async()=>{try{if(await f.value.validate(),h.value){const e=s.value.findIndex((e=>e.id===y.id));-1!==e&&(s.value[e]={...y}),u.success("成就更新成功")}else{const e={...y,id:Date.now(),completedCount:0};s.value.push(e),u.success("成就添加成功")}p.value=!1}catch(e){u.error("保存成就失败")}},toggleAchievementActive:async e=>{try{e.isActive=!e.isActive,u.success("成就已"+(e.isActive?"启用":"禁用"))}catch(a){u.error("切换成就状态失败"),e.isActive=!e.isActive}},viewAchievementUsers:e=>{b.value=[{userName:"张三",department:"研发部",completedAt:"2024-01-15 10:30:00",completedCount:1},{userName:"李四",department:"产品部",completedAt:"2024-01-16 14:20:00",completedCount:1}],v.value=!0}}}},[["render",function(e,a,l,d,c,n){const r=m("el-card"),u=m("el-col"),ae=m("el-row"),le=m("Plus"),te=m("el-icon"),ie=m("el-button"),se=m("el-option"),oe=m("el-select"),de=m("el-tag"),ce=m("el-tab-pane"),ne=m("Search"),re=m("el-input"),me=m("el-table-column"),ue=m("el-table"),pe=m("el-tabs"),ve=m("el-form-item"),he=m("el-input-number"),fe=m("el-switch"),be=m("el-form"),ye=m("el-dialog");return p(),t("div",w,[a[32]||(a[32]=i("div",{class:"page-header"},[i("h1",null,"🏆 成就系统"),i("p",null,"查看和管理用户成就，激励团队成员积极参与")],-1)),s(ae,{gutter:20,class:"stats-cards"},{default:o((()=>[s(u,{span:6},{default:o((()=>[s(r,{class:"stat-card"},{default:o((()=>[i("div",_,[a[20]||(a[20]=i("div",{class:"stat-icon"},"🎯",-1)),i("div",V,[i("div",R,v(d.achievementStats.totalAchievements),1),a[19]||(a[19]=i("div",{class:"stat-label"},"总成就数",-1))])])])),_:1})])),_:1}),s(u,{span:6},{default:o((()=>[s(r,{class:"stat-card"},{default:o((()=>[i("div",A,[a[22]||(a[22]=i("div",{class:"stat-icon"},"👥",-1)),i("div",C,[i("div",F,v(d.achievementStats.activeUsers),1),a[21]||(a[21]=i("div",{class:"stat-label"},"活跃用户",-1))])])])),_:1})])),_:1}),s(u,{span:6},{default:o((()=>[s(r,{class:"stat-card"},{default:o((()=>[i("div",U,[a[24]||(a[24]=i("div",{class:"stat-icon"},"⭐",-1)),i("div",k,[i("div",T,v(d.achievementStats.completedToday),1),a[23]||(a[23]=i("div",{class:"stat-label"},"今日完成",-1))])])])),_:1})])),_:1}),s(u,{span:6},{default:o((()=>[s(r,{class:"stat-card"},{default:o((()=>[i("div",q,[a[26]||(a[26]=i("div",{class:"stat-icon"},"🔥",-1)),i("div",N,[i("div",E,v(d.achievementStats.completionRate)+"%",1),a[25]||(a[25]=i("div",{class:"stat-label"},"完成率",-1))])])])),_:1})])),_:1})])),_:1}),s(r,{class:"main-content"},{default:o((()=>[s(pe,{modelValue:d.activeTab,"onUpdate:modelValue":a[3]||(a[3]=e=>d.activeTab=e),type:"border-card"},{default:o((()=>[s(ce,{label:"成就列表",name:"list"},{default:o((()=>[i("div",D,[s(ie,{type:"primary",onClick:d.showAddAchievementDialog},{default:o((()=>[s(te,null,{default:o((()=>[s(le)])),_:1}),a[27]||(a[27]=h(" 添加成就 "))])),_:1},8,["onClick"]),i("div",S,[s(oe,{modelValue:d.categoryFilter,"onUpdate:modelValue":a[0]||(a[0]=e=>d.categoryFilter=e),placeholder:"选择分类",clearable:""},{default:o((()=>[s(se,{label:"全部分类",value:""}),s(se,{label:"任务成就",value:"Task"}),s(se,{label:"质量成就",value:"Quality"}),s(se,{label:"创新成就",value:"Innovation"}),s(se,{label:"协作成就",value:"Collaboration"})])),_:1},8,["modelValue"]),s(oe,{modelValue:d.difficultyFilter,"onUpdate:modelValue":a[1]||(a[1]=e=>d.difficultyFilter=e),placeholder:"选择难度",clearable:""},{default:o((()=>[s(se,{label:"全部难度",value:""}),s(se,{label:"简单",value:"Easy"}),s(se,{label:"普通",value:"Normal"}),s(se,{label:"困难",value:"Hard"}),s(se,{label:"史诗",value:"Epic"})])),_:1},8,["modelValue"])])]),i("div",x,[(p(!0),t(f,null,b(d.filteredAchievements,(e=>(p(),t("div",{key:e.id,class:y(["achievement-card",{"achievement-inactive":!e.isActive}])},[i("div",I,[e.iconUrl?(p(),t("img",{key:0,src:e.iconUrl,alt:"成就图标"},null,8,z)):(p(),t("div",O,"🏆"))]),i("div",Q,[i("h3",H,v(e.name),1),i("p",M,v(e.description),1),i("div",P,[s(de,{type:d.getDifficultyType(e.difficulty),size:"small"},{default:o((()=>[h(v(e.difficulty),1)])),_:2},1032,["type"]),s(de,{type:"info",size:"small"},{default:o((()=>[h(v(e.category),1)])),_:2},1024)]),i("div",j,[e.pointsReward?(p(),t("span",L," 💰 "+v(e.pointsReward)+"积分 ",1)):g("",!0),e.coinsReward?(p(),t("span",K," 🪙 "+v(e.coinsReward)+"金币 ",1)):g("",!0),e.diamondsReward?(p(),t("span",G," 💎 "+v(e.diamondsReward)+"钻石 ",1)):g("",!0)]),i("div",X,[i("span",Y,"完成次数: "+v(e.completedCount||0),1)])]),i("div",$,[s(ie,{size:"small",onClick:a=>d.editAchievement(e)},{default:o((()=>a[28]||(a[28]=[h("编辑")]))),_:2},1032,["onClick"]),s(ie,{size:"small",onClick:a=>d.viewAchievementUsers(e)},{default:o((()=>a[29]||(a[29]=[h("查看用户")]))),_:2},1032,["onClick"]),s(ie,{size:"small",type:e.isActive?"warning":"success",onClick:a=>d.toggleAchievementActive(e)},{default:o((()=>[h(v(e.isActive?"禁用":"启用"),1)])),_:2},1032,["type","onClick"])])],2)))),128))])])),_:1}),s(ce,{label:"用户成就",name:"users"},{default:o((()=>[i("div",B,[i("div",J,[s(re,{modelValue:d.userSearch,"onUpdate:modelValue":a[2]||(a[2]=e=>d.userSearch=e),placeholder:"搜索用户",style:{width:"300px"},clearable:""},{prefix:o((()=>[s(te,null,{default:o((()=>[s(ne)])),_:1})])),_:1},8,["modelValue"])]),s(ue,{data:d.userAchievements,style:{width:"100%"}},{default:o((()=>[s(me,{prop:"userName",label:"用户名",width:"120"}),s(me,{prop:"department",label:"部门",width:"120"}),s(me,{prop:"achievementName",label:"成就名称"}),s(me,{prop:"achievementCategory",label:"成就分类",width:"100"}),s(me,{prop:"completedAt",label:"完成时间",width:"160"}),s(me,{prop:"progress",label:"进度",width:"100"},{default:o((e=>[h(v(e.row.progress)+"/"+v(e.row.requiredCount),1)])),_:1}),s(me,{label:"状态",width:"80"},{default:o((e=>[s(de,{type:e.row.isCompleted?"success":"info",size:"small"},{default:o((()=>[h(v(e.row.isCompleted?"已完成":"进行中"),1)])),_:2},1032,["type"])])),_:1})])),_:1},8,["data"])])])),_:1}),s(ce,{label:"成就统计",name:"statistics"},{default:o((()=>[i("div",W,[s(ae,{gutter:20},{default:o((()=>[s(u,{span:12},{default:o((()=>[s(r,{header:"成就分类统计"},{default:o((()=>[i("div",Z,null,512)])),_:1})])),_:1}),s(u,{span:12},{default:o((()=>[s(r,{header:"完成趋势"},{default:o((()=>[i("div",ee,null,512)])),_:1})])),_:1})])),_:1}),s(r,{header:"热门成就排行",class:"mt-20"},{default:o((()=>[s(ue,{data:d.popularAchievements,style:{width:"100%"}},{default:o((()=>[s(me,{type:"index",label:"排名",width:"80"}),s(me,{prop:"name",label:"成就名称"}),s(me,{prop:"category",label:"分类",width:"100"}),s(me,{prop:"completedCount",label:"完成人数",width:"100",sortable:""}),s(me,{prop:"completionRate",label:"完成率",width:"100"},{default:o((e=>[h(v(e.row.completionRate)+"% ",1)])),_:1})])),_:1},8,["data"])])),_:1})])])),_:1})])),_:1},8,["modelValue"])])),_:1}),s(ye,{title:d.achievementDialogTitle,modelValue:d.achievementDialogVisible,"onUpdate:modelValue":a[17]||(a[17]=e=>d.achievementDialogVisible=e),width:"600px"},{footer:o((()=>[s(ie,{onClick:a[16]||(a[16]=e=>d.achievementDialogVisible=!1)},{default:o((()=>a[30]||(a[30]=[h("取消")]))),_:1}),s(ie,{type:"primary",onClick:d.saveAchievement},{default:o((()=>a[31]||(a[31]=[h("保存")]))),_:1},8,["onClick"])])),default:o((()=>[s(be,{model:d.achievementForm,rules:d.achievementFormRules,ref:"achievementFormRef","label-width":"120px"},{default:o((()=>[s(ve,{label:"成就代码",prop:"code"},{default:o((()=>[s(re,{modelValue:d.achievementForm.code,"onUpdate:modelValue":a[4]||(a[4]=e=>d.achievementForm.code=e),disabled:d.isEditMode},null,8,["modelValue","disabled"])])),_:1}),s(ve,{label:"成就名称",prop:"name"},{default:o((()=>[s(re,{modelValue:d.achievementForm.name,"onUpdate:modelValue":a[5]||(a[5]=e=>d.achievementForm.name=e)},null,8,["modelValue"])])),_:1}),s(ve,{label:"成就描述",prop:"description"},{default:o((()=>[s(re,{modelValue:d.achievementForm.description,"onUpdate:modelValue":a[6]||(a[6]=e=>d.achievementForm.description=e),type:"textarea",rows:"3"},null,8,["modelValue"])])),_:1}),s(ve,{label:"成就分类",prop:"category"},{default:o((()=>[s(oe,{modelValue:d.achievementForm.category,"onUpdate:modelValue":a[7]||(a[7]=e=>d.achievementForm.category=e),style:{width:"100%"}},{default:o((()=>[s(se,{label:"任务成就",value:"Task"}),s(se,{label:"质量成就",value:"Quality"}),s(se,{label:"创新成就",value:"Innovation"}),s(se,{label:"协作成就",value:"Collaboration"}),s(se,{label:"其他",value:"Other"})])),_:1},8,["modelValue"])])),_:1}),s(ve,{label:"难度等级",prop:"difficulty"},{default:o((()=>[s(oe,{modelValue:d.achievementForm.difficulty,"onUpdate:modelValue":a[8]||(a[8]=e=>d.achievementForm.difficulty=e),style:{width:"100%"}},{default:o((()=>[s(se,{label:"简单",value:"Easy"}),s(se,{label:"普通",value:"Normal"}),s(se,{label:"困难",value:"Hard"}),s(se,{label:"史诗",value:"Epic"})])),_:1},8,["modelValue"])])),_:1}),s(ve,{label:"所需次数",prop:"requiredCount"},{default:o((()=>[s(he,{modelValue:d.achievementForm.requiredCount,"onUpdate:modelValue":a[9]||(a[9]=e=>d.achievementForm.requiredCount=e),min:1},null,8,["modelValue"])])),_:1}),s(ve,{label:"积分奖励",prop:"pointsReward"},{default:o((()=>[s(he,{modelValue:d.achievementForm.pointsReward,"onUpdate:modelValue":a[10]||(a[10]=e=>d.achievementForm.pointsReward=e),min:0},null,8,["modelValue"])])),_:1}),s(ve,{label:"金币奖励",prop:"coinsReward"},{default:o((()=>[s(he,{modelValue:d.achievementForm.coinsReward,"onUpdate:modelValue":a[11]||(a[11]=e=>d.achievementForm.coinsReward=e),min:0},null,8,["modelValue"])])),_:1}),s(ve,{label:"钻石奖励",prop:"diamondsReward"},{default:o((()=>[s(he,{modelValue:d.achievementForm.diamondsReward,"onUpdate:modelValue":a[12]||(a[12]=e=>d.achievementForm.diamondsReward=e),min:0},null,8,["modelValue"])])),_:1}),s(ve,{label:"图标URL"},{default:o((()=>[s(re,{modelValue:d.achievementForm.iconUrl,"onUpdate:modelValue":a[13]||(a[13]=e=>d.achievementForm.iconUrl=e),placeholder:"成就图标URL"},null,8,["modelValue"])])),_:1}),s(ve,{label:"可重复获得"},{default:o((()=>[s(fe,{modelValue:d.achievementForm.isRepeatable,"onUpdate:modelValue":a[14]||(a[14]=e=>d.achievementForm.isRepeatable=e)},null,8,["modelValue"])])),_:1}),s(ve,{label:"是否启用"},{default:o((()=>[s(fe,{modelValue:d.achievementForm.isActive,"onUpdate:modelValue":a[15]||(a[15]=e=>d.achievementForm.isActive=e)},null,8,["modelValue"])])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["title","modelValue"]),s(ye,{title:"成就获得用户",modelValue:d.userAchievementDialogVisible,"onUpdate:modelValue":a[18]||(a[18]=e=>d.userAchievementDialogVisible=e),width:"800px"},{default:o((()=>[s(ue,{data:d.selectedAchievementUsers,style:{width:"100%"}},{default:o((()=>[s(me,{prop:"userName",label:"用户名"}),s(me,{prop:"department",label:"部门"}),s(me,{prop:"completedAt",label:"完成时间"}),s(me,{prop:"completedCount",label:"完成次数"})])),_:1},8,["data"])])),_:1},8,["modelValue"])])}],["__scopeId","data-v-e994c988"]]);export{ae as default};
