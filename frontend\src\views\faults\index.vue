/**
 * 航空航天级IT资产管理系统 - 故障管理页面
 * 文件路径: src/views/faults/index.vue
 * 功能描述: 故障管理模块的布局容器
 */

<template>
  <div class="fault-container">
    <router-view v-slot="{ Component }">
      <keep-alive>
        <component :is="Component" />
      </keep-alive>
    </router-view>
  </div>
</template>

<script setup>
// 该组件仅作为故障管理子页面的容器
</script>

<style lang="scss" scoped>
@use "@/styles/variables.scss" as *;

.fault-container {
  padding: 16px;
  height: 100%;
}
</style> 