# 📡 API 接口文档

## 🌐 API 概览

IT资产管理系统提供完整的RESTful API，支持多版本并存。

### API版本策略
- **V1**: `/api/{controller}` - 传统API
- **V1.1**: `/api/v1.1/{controller}` - 优化版本  
- **V2**: `/api/v2/{controller}` - 新架构API (推荐)

### 认证方式
```http
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json
```

## 📋 核心API模块

### 1. 🔐 认证授权
```http
POST /api/auth/login
{
  "username": "admin",
  "password": "123456"
}
```

### 2. 📦 资产管理 (V2)
```http
GET /api/v2/assets?pageIndex=1&pageSize=20&status=在用
POST /api/v2/assets
PUT /api/v2/assets/{id}
DELETE /api/v2/assets/{id}
```

### 3. ⚡ 任务管理 (V2)
```http
GET /api/v2/tasks?status=Todo&assigneeUserId=1
POST /api/v2/tasks
POST /api/v2/tasks/{id}/complete
```

### 4. 🔧 备件管理
```http
GET /api/v2/spare-parts?locationId=1&lowStock=true
POST /api/v2/spare-parts/{id}/transaction
```

### 5. 📊 统计分析
```http
GET /api/v2/statistics/asset-overview
GET /api/v2/statistics/task-overview
```

## 🚨 错误处理

### 标准响应格式
```json
{
  "success": true,
  "data": {...},
  "message": "操作成功",
  "pagination": {...}
}
```

### 错误响应
```json
{
  "success": false,
  "message": "错误描述",
  "error": {
    "code": "VALIDATION_ERROR",
    "details": [...]
  }
}
```

## 🔧 开发工具
- **Swagger**: https://localhost:5001/swagger
- **Postman**: 导入API集合

---
**最后更新**: 2025年6月21日