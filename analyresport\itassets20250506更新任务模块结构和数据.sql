/*
 Navicat Premium Data Transfer

 Source Server         : IT资产
 Source Server Type    : MySQL
 Source Server Version : 80029
 Source Host           : localhost:3306
 Source Schema         : itassets

 Target Server Type    : MySQL
 Target Server Version : 80029
 File Encoding         : 65001

 Date: 06/05/2025 18:01:56
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for __efmigrationshistory
-- ----------------------------
DROP TABLE IF EXISTS `__efmigrationshistory`;
CREATE TABLE `__efmigrationshistory`  (
  `MigrationId` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `ProductVersion` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  PRIMARY KEY (`MigrationId`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'EF迁移历史表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of __efmigrationshistory
-- ----------------------------
INSERT INTO `__efmigrationshistory` VALUES ('20250501231410_InitialCreate', '8.0.3');

-- ----------------------------
-- Table structure for _script_execution_log
-- ----------------------------
DROP TABLE IF EXISTS `_script_execution_log`;
CREATE TABLE `_script_execution_log`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `step` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
  `executed_at` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 29 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of _script_execution_log
-- ----------------------------
INSERT INTO `_script_execution_log` VALUES (1, '创建表', '开始', '开始创建新表', '2025-04-06 16:36:02');
INSERT INTO `_script_execution_log` VALUES (2, '创建表', '成功', 'task_history', '2025-04-06 16:36:02');
INSERT INTO `_script_execution_log` VALUES (3, '创建表', '成功', 'user_points', '2025-04-06 16:36:02');
INSERT INTO `_script_execution_log` VALUES (4, '创建表', '成功', 'point_history', '2025-04-06 16:36:02');
INSERT INTO `_script_execution_log` VALUES (5, '创建表', '成功', 'point_leaderboard', '2025-04-06 16:36:02');
INSERT INTO `_script_execution_log` VALUES (6, '创建表', '成功', 'notifications', '2025-04-06 16:36:02');
INSERT INTO `_script_execution_log` VALUES (7, '创建表', '成功', 'dashboard_config', '2025-04-06 16:36:02');
INSERT INTO `_script_execution_log` VALUES (8, '创建表', '成功', 'daily_reports', '2025-04-06 16:36:03');
INSERT INTO `_script_execution_log` VALUES (9, '创建表', '成功', 'user_actions', '2025-04-06 16:36:03');
INSERT INTO `_script_execution_log` VALUES (10, '创建表', '成功', 'inventory_thresholds', '2025-04-06 16:36:03');
INSERT INTO `_script_execution_log` VALUES (11, '创建表', '完成', '所有新表创建完成', '2025-04-06 16:36:03');
INSERT INTO `_script_execution_log` VALUES (12, '修改表', '开始', '开始修改现有表结构', '2025-04-06 16:36:03');
INSERT INTO `_script_execution_log` VALUES (13, '修改表', '成功', 'assets', '2025-04-06 16:36:03');
INSERT INTO `_script_execution_log` VALUES (14, '修改表', '成功', 'locations', '2025-04-06 16:36:03');
INSERT INTO `_script_execution_log` VALUES (15, '错误捕获', '失败', '', '2025-04-06 16:36:03');
INSERT INTO `_script_execution_log` VALUES (16, '回滚操作', '开始', '执行失败,开始回滚创建的对象', '2025-04-06 16:36:03');
INSERT INTO `_script_execution_log` VALUES (17, '删除事件', '完成', 'check_inventory_warning', '2025-04-06 16:36:03');
INSERT INTO `_script_execution_log` VALUES (18, '删除触发器', '完成', 'All triggers', '2025-04-06 16:36:03');
INSERT INTO `_script_execution_log` VALUES (19, '删除表', '完成', 'All new tables', '2025-04-06 16:36:03');
INSERT INTO `_script_execution_log` VALUES (20, '回滚操作', '完成', '所有对象已回滚(存储过程需手动删除)', '2025-04-06 16:36:03');
INSERT INTO `_script_execution_log` VALUES (21, '创建存储过程', '开始', '开始创建业务存储过程', '2025-04-06 16:36:03');
INSERT INTO `_script_execution_log` VALUES (22, '创建存储过程', '成功', 'generate_asset_code', '2025-04-06 16:36:03');
INSERT INTO `_script_execution_log` VALUES (23, '创建存储过程', '成功', 'inventory_to_asset', '2025-04-06 16:36:03');
INSERT INTO `_script_execution_log` VALUES (24, '创建存储过程', '成功', 'calculate_points', '2025-04-06 16:36:03');
INSERT INTO `_script_execution_log` VALUES (25, '创建存储过程', '成功', 'generate_daily_leaderboard', '2025-04-06 16:36:03');
INSERT INTO `_script_execution_log` VALUES (26, '创建存储过程', '完成', '所有存储过程创建完成', '2025-04-06 16:36:03');
INSERT INTO `_script_execution_log` VALUES (27, '创建触发器', '开始', '开始创建触发器', '2025-04-06 16:36:03');
INSERT INTO `_script_execution_log` VALUES (28, '创建触发器', '成功', 'check_asset_location_level', '2025-04-06 16:36:03');

-- ----------------------------
-- Table structure for assethistories
-- ----------------------------
DROP TABLE IF EXISTS `assethistories`;
CREATE TABLE `assethistories`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `AssetId` int NOT NULL COMMENT '资产ID',
  `OperationType` int NOT NULL COMMENT '操作类型：1创建，2修改，3删除，4位置变更，5状态变更',
  `OperatorId` int NOT NULL COMMENT '操作人ID',
  `OperationTime` datetime(0) NOT NULL COMMENT '操作时间',
  `Description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '描述（JSON格式，记录变更前后的属性值）',
  `CreatedAt` datetime(0) NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime(0) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`) USING BTREE,
  INDEX `IX_AssetHistories_AssetId`(`AssetId`) USING BTREE,
  INDEX `IX_AssetHistories_OperatorId`(`OperatorId`) USING BTREE,
  CONSTRAINT `FK_AssetHistories_Assets_AssetId` FOREIGN KEY (`AssetId`) REFERENCES `assets` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_AssetHistories_Users_OperatorId` FOREIGN KEY (`OperatorId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 66 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '资产历史表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of assethistories
-- ----------------------------
INSERT INTO `assethistories` VALUES (59, 818, 2, 8, '2025-04-03 19:25:04', '{\r\n  \"model\": {\r\n    \"oldValue\": \"TPU-20BQ\",\r\n    \"newValue\": \"TPU-20BQ1\"\r\n  },\r\n  \"locationId\": {\r\n    \"oldValue\": 149,\r\n    \"newValue\": 157\r\n  },\r\n  \"位置\": {\r\n    \"oldValue\": \" (毛坯检验01)\",\r\n    \"newValue\": \" (仓库区域GP12)\"\r\n  }\r\n}', '2025-04-03 19:25:04', '2025-04-03 19:25:04');
INSERT INTO `assethistories` VALUES (60, 818, 2, 8, '2025-04-03 19:26:00', '{\r\n  \"model\": {\r\n    \"oldValue\": \"TPU-20BQ1\",\r\n    \"newValue\": \"TPU-20BQ\"\r\n  },\r\n  \"locationId\": {\r\n    \"oldValue\": 157,\r\n    \"newValue\": 156\r\n  },\r\n  \"位置\": {\r\n    \"oldValue\": \" (仓库区域GP12)\",\r\n    \"newValue\": \" (机加区域GP12)\"\r\n  }\r\n}', '2025-04-03 19:26:00', '2025-04-03 19:26:00');
INSERT INTO `assethistories` VALUES (61, 719, 2, 8, '2025-04-03 19:28:10', '{\r\n  \"assetCode\": {\r\n    \"oldValue\": \"无\",\r\n    \"newValue\": \"IPC049\"\r\n  },\r\n  \"name\": {\r\n    \"oldValue\": \"无\",\r\n    \"newValue\": \"工控机1\"\r\n  },\r\n  \"assetTypeId\": {\r\n    \"oldValue\": \"无\",\r\n    \"newValue\": 7\r\n  },\r\n  \"model\": {\r\n    \"oldValue\": \"无\",\r\n    \"newValue\": \"VFACE-156LC 15.6寸\"\r\n  },\r\n  \"brand\": {\r\n    \"oldValue\": \"无\",\r\n    \"newValue\": \"威嵌沃\"\r\n  },\r\n  \"locationId\": {\r\n    \"oldValue\": \"无\",\r\n    \"newValue\": 151\r\n  },\r\n  \"status\": {\r\n    \"oldValue\": \"无\",\r\n    \"newValue\": 1\r\n  },\r\n  \"资产类型\": {\r\n    \"oldValue\": \"未知\",\r\n    \"newValue\": \"工控机\"\r\n  },\r\n  \"位置\": {\r\n    \"oldValue\": \"未分配\",\r\n    \"newValue\": \" (毛坯检验04)\"\r\n  },\r\n  \"状态\": {\r\n    \"oldValue\": \"未知\",\r\n    \"newValue\": \"在用\"\r\n  }\r\n}', '2025-04-03 19:28:10', '2025-04-03 19:28:10');
INSERT INTO `assethistories` VALUES (62, 818, 2, 7, '2025-04-03 19:32:03', '{\r\n  \"model\": {\r\n    \"oldValue\": \"TPU-20BQ\",\r\n    \"newValue\": \"TPU-20BQ11\"\r\n  }\r\n}', '2025-04-03 19:32:03', '2025-04-03 19:32:03');
INSERT INTO `assethistories` VALUES (65, 818, 2, 1, '2025-04-09 05:30:40', '{\r\n  \"model\": {\r\n    \"oldValue\": \"TPU-20BQ11\",\r\n    \"newValue\": \"TPU-20BQ\"\r\n  }\r\n}', '2025-04-09 05:30:40', '2025-04-09 05:30:40');

-- ----------------------------
-- Table structure for assetreceives
-- ----------------------------
DROP TABLE IF EXISTS `assetreceives`;
CREATE TABLE `assetreceives`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `ReceiveCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '入库单号',
  `PurchaseOrderId` int NULL DEFAULT NULL COMMENT '采购订单ID',
  `Title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '标题',
  `Description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '描述',
  `ReceiverId` int NOT NULL COMMENT '接收人ID',
  `ReceiveTime` datetime(0) NOT NULL COMMENT '接收时间',
  `Status` int NOT NULL DEFAULT 0 COMMENT '状态：0草稿，1已提交，2已确认',
  `AssetTypeId` int NULL DEFAULT NULL COMMENT '资产类型ID',
  `Quantity` int NOT NULL DEFAULT 1 COMMENT '数量',
  `TotalAmount` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '总金额',
  `InitialLocationId` int NULL DEFAULT NULL COMMENT '初始位置ID',
  `Notes` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `CreatedAt` datetime(0) NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime(0) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE INDEX `IX_AssetReceives_ReceiveCode`(`ReceiveCode`) USING BTREE,
  INDEX `IX_AssetReceives_PurchaseOrderId`(`PurchaseOrderId`) USING BTREE,
  INDEX `IX_AssetReceives_ReceiverId`(`ReceiverId`) USING BTREE,
  INDEX `IX_AssetReceives_AssetTypeId`(`AssetTypeId`) USING BTREE,
  INDEX `IX_AssetReceives_InitialLocationId`(`InitialLocationId`) USING BTREE,
  CONSTRAINT `FK_AssetReceives_AssetTypes_AssetTypeId` FOREIGN KEY (`AssetTypeId`) REFERENCES `assettypes` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_AssetReceives_Locations_InitialLocationId` FOREIGN KEY (`InitialLocationId`) REFERENCES `locations` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_AssetReceives_PurchaseOrders_PurchaseOrderId` FOREIGN KEY (`PurchaseOrderId`) REFERENCES `purchaseorders` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_AssetReceives_Users_ReceiverId` FOREIGN KEY (`ReceiverId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '资产入库表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of assetreceives
-- ----------------------------

-- ----------------------------
-- Table structure for assets
-- ----------------------------
DROP TABLE IF EXISTS `assets`;
CREATE TABLE `assets`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `assetCode` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '资产编号(IT-类型-年月日-序号)',
  `FinancialCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '财务编号',
  `Name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '资产名称',
  `AssetTypeId` int NOT NULL COMMENT '资产类型ID',
  `SerialNumber` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '序列号',
  `Model` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '型号',
  `Brand` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '品牌',
  `PurchaseDate` datetime(0) NULL DEFAULT NULL COMMENT '购买日期',
  `WarrantyExpireDate` datetime(0) NULL DEFAULT NULL COMMENT '保修到期日',
  `Price` decimal(18, 2) NULL DEFAULT NULL COMMENT '价格',
  `LocationId` int NULL DEFAULT NULL COMMENT '位置ID',
  `DepartmentId` int NULL DEFAULT NULL,
  `Status` int NOT NULL DEFAULT 0 COMMENT '状态：0闲置，1在用，2维修中，3报废',
  `Notes` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `CreatedAt` datetime(0) NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime(0) NOT NULL COMMENT '更新时间',
  `asset_code_prefix` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'IT' COMMENT '资产编号前缀',
  `inventory_id` int NULL DEFAULT NULL COMMENT '来源库存ID',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE INDEX `IX_Assets_AssetCode`(`assetCode`) USING BTREE,
  INDEX `IX_Assets_AssetTypeId`(`AssetTypeId`) USING BTREE,
  INDEX `IX_Assets_LocationId`(`LocationId`) USING BTREE,
  INDEX `IX_Assets_FinancialCode`(`FinancialCode`) USING BTREE,
  INDEX `IX_Assets_DepartmentId`(`DepartmentId`) USING BTREE,
  CONSTRAINT `FK_Assets_AssetTypes_AssetTypeId` FOREIGN KEY (`AssetTypeId`) REFERENCES `assettypes` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_Assets_Departments_DepartmentId` FOREIGN KEY (`DepartmentId`) REFERENCES `departments` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_Assets_Locations_LocationId` FOREIGN KEY (`LocationId`) REFERENCES `locations` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1103 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '资产表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of assets
-- ----------------------------
INSERT INTO `assets` VALUES (671, 'IPC001', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 43, NULL, 1, NULL, '2025-04-03 00:57:09', '2025-04-03 00:57:09', 'IT', NULL);
INSERT INTO `assets` VALUES (672, 'IPC002', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 44, NULL, 1, NULL, '2025-04-03 00:57:09', '2025-04-03 00:57:09', 'IT', NULL);
INSERT INTO `assets` VALUES (673, 'IPC003', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 45, NULL, 1, NULL, '2025-04-03 00:57:09', '2025-04-03 00:57:09', 'IT', NULL);
INSERT INTO `assets` VALUES (674, 'IPC004', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 48, NULL, 1, NULL, '2025-04-03 00:57:09', '2025-04-03 00:57:09', 'IT', NULL);
INSERT INTO `assets` VALUES (675, 'IPC005', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 50, NULL, 1, NULL, '2025-04-03 00:57:09', '2025-04-03 00:57:09', 'IT', NULL);
INSERT INTO `assets` VALUES (676, 'IPC006', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 51, NULL, 1, NULL, '2025-04-03 00:57:09', '2025-04-03 00:57:09', 'IT', NULL);
INSERT INTO `assets` VALUES (677, 'IPC007', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 49, NULL, 1, NULL, '2025-04-03 00:57:09', '2025-04-03 00:57:09', 'IT', NULL);
INSERT INTO `assets` VALUES (678, 'IPC008', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 52, NULL, 1, NULL, '2025-04-03 00:57:09', '2025-04-03 00:57:09', 'IT', NULL);
INSERT INTO `assets` VALUES (679, 'IPC009', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 53, NULL, 1, NULL, '2025-04-03 00:57:09', '2025-04-03 00:57:09', 'IT', NULL);
INSERT INTO `assets` VALUES (680, 'IPC010', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 60, NULL, 1, NULL, '2025-04-03 00:57:09', '2025-04-03 00:57:09', 'IT', NULL);
INSERT INTO `assets` VALUES (681, 'IPC011', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 61, NULL, 1, NULL, '2025-04-03 00:57:09', '2025-04-03 00:57:09', 'IT', NULL);
INSERT INTO `assets` VALUES (682, 'IPC012', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 62, NULL, 1, NULL, '2025-04-03 00:57:09', '2025-04-03 00:57:09', 'IT', NULL);
INSERT INTO `assets` VALUES (683, 'IPC013', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 63, NULL, 1, NULL, '2025-04-03 00:57:09', '2025-04-03 00:57:09', 'IT', NULL);
INSERT INTO `assets` VALUES (684, 'IPC014', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 64, NULL, 1, NULL, '2025-04-03 00:57:09', '2025-04-03 00:57:09', 'IT', NULL);
INSERT INTO `assets` VALUES (685, 'IPC015', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 65, NULL, 1, NULL, '2025-04-03 00:57:09', '2025-04-03 00:57:09', 'IT', NULL);
INSERT INTO `assets` VALUES (686, 'IPC016', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 66, NULL, 1, NULL, '2025-04-03 00:57:09', '2025-04-03 00:57:09', 'IT', NULL);
INSERT INTO `assets` VALUES (687, 'IPC017', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 66, NULL, 1, NULL, '2025-04-03 00:57:09', '2025-04-03 00:57:09', 'IT', NULL);
INSERT INTO `assets` VALUES (688, 'IPC018', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 67, NULL, 1, NULL, '2025-04-03 00:57:09', '2025-04-03 00:57:09', 'IT', NULL);
INSERT INTO `assets` VALUES (689, 'IPC019', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 68, NULL, 1, NULL, '2025-04-03 00:57:09', '2025-04-03 00:57:09', 'IT', NULL);
INSERT INTO `assets` VALUES (690, 'IPC020', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 69, NULL, 1, NULL, '2025-04-03 00:57:09', '2025-04-03 00:57:09', 'IT', NULL);
INSERT INTO `assets` VALUES (691, 'IPC021', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 70, NULL, 1, NULL, '2025-04-03 00:57:09', '2025-04-03 00:57:09', 'IT', NULL);
INSERT INTO `assets` VALUES (692, 'IPC022', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 71, NULL, 1, NULL, '2025-04-03 00:57:09', '2025-04-03 00:57:09', 'IT', NULL);
INSERT INTO `assets` VALUES (693, 'IPC023', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 72, NULL, 1, NULL, '2025-04-03 00:57:09', '2025-04-03 00:57:09', 'IT', NULL);
INSERT INTO `assets` VALUES (694, 'IPC024', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 73, NULL, 1, NULL, '2025-04-03 00:57:09', '2025-04-03 00:57:09', 'IT', NULL);
INSERT INTO `assets` VALUES (695, 'IPC025', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 74, NULL, 1, NULL, '2025-04-03 00:57:09', '2025-04-03 00:57:09', 'IT', NULL);
INSERT INTO `assets` VALUES (696, 'IPC026', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 75, NULL, 1, NULL, '2025-04-03 00:57:09', '2025-04-03 00:57:09', 'IT', NULL);
INSERT INTO `assets` VALUES (697, 'IPC027', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 76, NULL, 1, NULL, '2025-04-03 00:57:09', '2025-04-03 00:57:09', 'IT', NULL);
INSERT INTO `assets` VALUES (698, 'IPC028', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 77, NULL, 1, NULL, '2025-04-03 00:57:09', '2025-04-03 00:57:09', 'IT', NULL);
INSERT INTO `assets` VALUES (699, 'IPC029', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 78, NULL, 1, NULL, '2025-04-03 00:57:09', '2025-04-03 00:57:09', 'IT', NULL);
INSERT INTO `assets` VALUES (700, 'IPC030', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 79, NULL, 1, NULL, '2025-04-03 00:57:09', '2025-04-03 00:57:09', 'IT', NULL);
INSERT INTO `assets` VALUES (701, 'IPC031', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 80, NULL, 1, NULL, '2025-04-03 00:57:09', '2025-04-03 00:57:09', 'IT', NULL);
INSERT INTO `assets` VALUES (702, 'IPC032', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 145, NULL, 1, NULL, '2025-04-03 00:57:09', '2025-04-03 00:57:09', 'IT', NULL);
INSERT INTO `assets` VALUES (703, 'IPC033', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 145, NULL, 1, NULL, '2025-04-03 00:57:09', '2025-04-03 00:57:09', 'IT', NULL);
INSERT INTO `assets` VALUES (704, 'IPC034', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 146, NULL, 1, NULL, '2025-04-03 00:57:09', '2025-04-03 00:57:09', 'IT', NULL);
INSERT INTO `assets` VALUES (705, 'IPC035', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 146, NULL, 1, NULL, '2025-04-03 00:57:09', '2025-04-03 00:57:09', 'IT', NULL);
INSERT INTO `assets` VALUES (706, 'IPC036', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 146, NULL, 1, NULL, '2025-04-03 00:57:09', '2025-04-03 00:57:09', 'IT', NULL);
INSERT INTO `assets` VALUES (707, 'IPC037', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 147, NULL, 1, NULL, '2025-04-03 00:57:09', '2025-04-03 00:57:09', 'IT', NULL);
INSERT INTO `assets` VALUES (708, 'IPC038', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 133, NULL, 1, NULL, '2025-04-03 00:57:09', '2025-04-03 00:57:09', 'IT', NULL);
INSERT INTO `assets` VALUES (709, 'IPC039', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 134, NULL, 1, NULL, '2025-04-03 00:57:09', '2025-04-03 00:57:09', 'IT', NULL);
INSERT INTO `assets` VALUES (710, 'IPC040', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 135, NULL, 1, NULL, '2025-04-03 00:57:09', '2025-04-03 00:57:09', 'IT', NULL);
INSERT INTO `assets` VALUES (711, 'IPC041', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 136, NULL, 1, NULL, '2025-04-03 00:57:09', '2025-04-03 00:57:09', 'IT', NULL);
INSERT INTO `assets` VALUES (712, 'IPC042', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 137, NULL, 1, NULL, '2025-04-03 00:57:09', '2025-04-03 00:57:09', 'IT', NULL);
INSERT INTO `assets` VALUES (713, 'IPC043', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 138, NULL, 1, NULL, '2025-04-03 00:57:09', '2025-04-03 00:57:09', 'IT', NULL);
INSERT INTO `assets` VALUES (714, 'IPC044', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 139, NULL, 1, NULL, '2025-04-03 00:57:09', '2025-04-03 00:57:09', 'IT', NULL);
INSERT INTO `assets` VALUES (715, 'IPC045', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 140, NULL, 1, NULL, '2025-04-03 00:57:09', '2025-04-03 00:57:09', 'IT', NULL);
INSERT INTO `assets` VALUES (716, 'IPC046', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 149, NULL, 1, NULL, '2025-04-03 00:57:09', '2025-04-03 00:57:09', 'IT', NULL);
INSERT INTO `assets` VALUES (717, 'IPC047', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 150, NULL, 1, NULL, '2025-04-03 00:57:09', '2025-04-03 00:57:09', 'IT', NULL);
INSERT INTO `assets` VALUES (718, 'IPC048', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 152, NULL, 1, NULL, '2025-04-03 00:57:09', '2025-04-03 00:57:09', 'IT', NULL);
INSERT INTO `assets` VALUES (719, 'IPC049', '', '工控机1', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 151, NULL, 1, '', '2025-04-03 00:57:09', '2025-04-03 19:28:10', 'IT', NULL);
INSERT INTO `assets` VALUES (720, 'IPC050', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 153, NULL, 1, NULL, '2025-04-03 00:57:09', '2025-04-03 00:57:09', 'IT', NULL);
INSERT INTO `assets` VALUES (721, 'IPC051', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 158, NULL, 1, NULL, '2025-04-03 00:57:09', '2025-04-03 00:57:09', 'IT', NULL);
INSERT INTO `assets` VALUES (722, 'IPC052', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 228, NULL, 1, NULL, '2025-04-03 00:57:09', '2025-04-03 00:57:09', 'IT', NULL);
INSERT INTO `assets` VALUES (723, 'IPC053', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 230, NULL, 1, NULL, '2025-04-03 00:57:09', '2025-04-03 00:57:09', 'IT', NULL);
INSERT INTO `assets` VALUES (724, 'IPC054', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 232, NULL, 1, NULL, '2025-04-03 00:57:09', '2025-04-03 00:57:09', 'IT', NULL);
INSERT INTO `assets` VALUES (725, 'IPC055', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 234, NULL, 1, NULL, '2025-04-03 00:57:09', '2025-04-03 00:57:09', 'IT', NULL);
INSERT INTO `assets` VALUES (726, 'IPC056', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 236, NULL, 1, NULL, '2025-04-03 00:57:09', '2025-04-03 00:57:09', 'IT', NULL);
INSERT INTO `assets` VALUES (727, 'IPC057', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 240, NULL, 1, NULL, '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (728, 'IPC058', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 242, NULL, 1, NULL, '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (729, 'IPC059', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 246, NULL, 1, NULL, '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (730, 'IPC060', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 248, NULL, 1, NULL, '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (731, 'IPC061', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 250, NULL, 1, NULL, '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (732, 'IPC062', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 1, NULL, 1, NULL, '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (733, 'IPC063', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 253, NULL, 1, NULL, '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (734, 'IPC064', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 255, NULL, 1, NULL, '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (735, 'IPC065', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 257, NULL, 1, NULL, '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (736, 'IPC066', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 259, NULL, 1, NULL, '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (737, 'IPC067', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 263, NULL, 1, NULL, '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (738, 'IPC068', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 265, NULL, 1, NULL, '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (739, 'IPC069', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 267, NULL, 1, NULL, '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (740, 'IPC070', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 269, NULL, 1, NULL, '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (741, 'IPC071', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 155, NULL, 1, '洛轲焊接站OP20', '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (742, 'IPC072', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 155, NULL, 1, '洛轲焊接站OP30', '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (743, 'IPC073', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 155, NULL, 1, '客退件绑定站', '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (744, 'IPC074', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 155, NULL, 1, '铝液称重', '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (745, 'IPC075', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 156, NULL, 1, 'F2后副GP12', '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (746, 'IPC076', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 156, NULL, 1, 'E0XGP12', '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (747, 'IPC077', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 157, NULL, 1, 'GP12-3号工位', '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (748, 'IPC078', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 157, NULL, 1, 'W01-GP12', '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (749, 'IPC079', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 157, NULL, 1, 'GP12-11号工位', '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (750, 'IPC080', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 157, NULL, 1, 'GP12-VC1', '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (751, 'IPC081', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 157, NULL, 1, 'GP12-9号工位', '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (752, 'IPC082', NULL, '工控机', 7, NULL, 'VFACE-156LC 15.6寸', '威嵌沃', NULL, NULL, NULL, 157, NULL, 1, 'GP12-5号工位', '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (753, 'COMPUTER001', NULL, '台式电脑', 1, NULL, '台式电脑', '电脑设备', NULL, NULL, NULL, 142, NULL, 1, NULL, '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (754, 'COMPUTER002', NULL, '台式电脑', 1, NULL, '台式电脑', '电脑设备', NULL, NULL, NULL, 143, NULL, 1, NULL, '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (755, 'COMPUTER003', NULL, '台式电脑', 1, NULL, '台式电脑', '电脑设备', NULL, NULL, NULL, 144, NULL, 1, NULL, '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (756, 'SCANNER001', NULL, '扫码枪', 4, NULL, 'TPU-20BQ', '海康威视', NULL, NULL, NULL, 43, NULL, 1, NULL, '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (757, 'SCANNER002', NULL, '扫码枪', 4, NULL, 'TPU-20BQ', '海康威视', NULL, NULL, NULL, 44, NULL, 1, NULL, '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (758, 'SCANNER003', NULL, '扫码枪', 4, NULL, 'TPU-20BQ', '海康威视', NULL, NULL, NULL, 45, NULL, 1, NULL, '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (759, 'SCANNER004', NULL, '扫码枪', 4, NULL, 'TPU-20BQ', '海康威视', NULL, NULL, NULL, 48, NULL, 1, '黑枪', '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (760, 'SCANNER005', NULL, '扫码枪', 4, NULL, 'TPU-20BQ', '海康威视', NULL, NULL, NULL, 50, NULL, 1, NULL, '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (761, 'SCANNER006', NULL, '扫码枪', 4, NULL, 'TPU-20BQ', '海康威视', NULL, NULL, NULL, 51, NULL, 1, '黑枪', '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (762, 'SCANNER007', NULL, '扫码枪', 4, NULL, 'TPU-20BQ', '海康威视', NULL, NULL, NULL, 60, NULL, 1, NULL, '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (763, 'SCANNER008', NULL, '扫码枪', 4, NULL, 'TPU-20BQ', '海康威视', NULL, NULL, NULL, 61, NULL, 1, '黑枪', '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (764, 'SCANNER009', NULL, '扫码枪', 4, NULL, 'TPU-20BQ', '海康威视', NULL, NULL, NULL, 63, NULL, 1, '黑枪', '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (765, 'SCANNER010', NULL, '扫码枪', 4, NULL, 'TPU-20BQ', '海康威视', NULL, NULL, NULL, 64, NULL, 1, '黑枪', '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (766, 'SCANNER011', NULL, '扫码枪', 4, NULL, 'TPU-20BQ', '海康威视', NULL, NULL, NULL, 65, NULL, 1, NULL, '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (767, 'SCANNER012', NULL, '扫码枪', 4, NULL, 'TPU-20BQ', '海康威视', NULL, NULL, NULL, 66, NULL, 1, NULL, '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (768, 'SCANNER013', NULL, '扫码枪', 4, NULL, 'TPU-20BQ', '海康威视', NULL, NULL, NULL, 67, NULL, 1, NULL, '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (769, 'SCANNER014', NULL, '扫码枪', 4, NULL, 'TPU-20BQ', '海康威视', NULL, NULL, NULL, 68, NULL, 1, NULL, '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (770, 'SCANNER015', NULL, '扫码枪', 4, NULL, 'TPU-20BQ', '海康威视', NULL, NULL, NULL, 69, NULL, 1, NULL, '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (771, 'SCANNER016', NULL, '扫码枪', 4, NULL, 'TPU-20BQ', '海康威视', NULL, NULL, NULL, 70, NULL, 1, NULL, '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (772, 'SCANNER017', NULL, '扫码枪', 4, NULL, 'TPU-20BQ', '海康威视', NULL, NULL, NULL, 71, NULL, 1, NULL, '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (773, 'SCANNER018', NULL, '扫码枪', 4, NULL, 'TPU-20BQ', '海康威视', NULL, NULL, NULL, 73, NULL, 1, NULL, '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (774, 'SCANNER019', NULL, '扫码枪', 4, NULL, 'TPU-20BQ', '海康威视', NULL, NULL, NULL, 76, NULL, 1, NULL, '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (775, 'SCANNER020', NULL, '扫码枪', 4, NULL, 'TPU-20BQ', '海康威视', NULL, NULL, NULL, 80, NULL, 1, NULL, '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (776, 'SCANNER021', NULL, '扫码枪', 4, NULL, 'TPU-20BQ', '海康威视', NULL, NULL, NULL, 87, NULL, 1, NULL, '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (777, 'SCANNER022', NULL, '扫码枪', 4, NULL, 'TPU-20BQ', '海康威视', NULL, NULL, NULL, 88, NULL, 1, NULL, '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (778, 'SCANNER023', NULL, '扫码枪', 4, NULL, 'TPU-20BQ', '海康威视', NULL, NULL, NULL, 89, NULL, 1, NULL, '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (779, 'SCANNER024', NULL, '扫码枪', 4, NULL, 'TPU-20BQ', '海康威视', NULL, NULL, NULL, 91, NULL, 1, NULL, '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (780, 'SCANNER025', NULL, '扫码枪', 4, NULL, 'TPU-20BQ', '海康威视', NULL, NULL, NULL, 92, NULL, 1, NULL, '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (781, 'SCANNER026', NULL, '扫码枪', 4, NULL, 'TPU-20BQ', '海康威视', NULL, NULL, NULL, 93, NULL, 1, NULL, '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (782, 'SCANNER027', NULL, '扫码枪', 4, NULL, 'TPU-20BQ', '海康威视', NULL, NULL, NULL, 95, NULL, 1, NULL, '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (783, 'SCANNER028', NULL, '扫码枪', 4, NULL, 'TPU-20BQ', '海康威视', NULL, NULL, NULL, 145, NULL, 1, NULL, '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (784, 'SCANNER029', NULL, '扫码枪', 4, NULL, 'TPU-20BQ', '海康威视', NULL, NULL, NULL, 145, NULL, 1, NULL, '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (785, 'SCANNER030', NULL, '扫码枪', 4, NULL, 'TPU-20BQ', '海康威视', NULL, NULL, NULL, 146, NULL, 1, NULL, '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (786, 'SCANNER031', NULL, '扫码枪', 4, NULL, 'TPU-20BQ', '海康威视', NULL, NULL, NULL, 146, NULL, 1, NULL, '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (787, 'SCANNER032', NULL, '扫码枪', 4, NULL, 'TPU-20BQ', '海康威视', NULL, NULL, NULL, 146, NULL, 1, NULL, '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (788, 'SCANNER033', NULL, '扫码枪', 4, NULL, 'TPU-20BQ', '海康威视', NULL, NULL, NULL, 147, NULL, 1, NULL, '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (789, 'SCANNER034', NULL, '扫码枪', 4, NULL, 'TPU-20BQ', '海康威视', NULL, NULL, NULL, 133, NULL, 1, NULL, '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (790, 'SCANNER035', NULL, '扫码枪', 4, NULL, 'TPU-20BQ', '海康威视', NULL, NULL, NULL, 134, NULL, 1, NULL, '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (791, 'SCANNER036', NULL, '扫码枪', 4, NULL, 'TPU-20BQ', '海康威视', NULL, NULL, NULL, 135, NULL, 1, NULL, '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (792, 'SCANNER037', NULL, '扫码枪', 4, NULL, 'TPU-20BQ', '海康威视', NULL, NULL, NULL, 136, NULL, 1, NULL, '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (793, 'SCANNER038', NULL, '扫码枪', 4, NULL, 'TPU-20BQ', '海康威视', NULL, NULL, NULL, 137, NULL, 1, NULL, '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (794, 'SCANNER039', NULL, '扫码枪', 4, NULL, 'TPU-20BQ', '海康威视', NULL, NULL, NULL, 139, NULL, 1, NULL, '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (795, 'SCANNER040', NULL, '扫码枪', 4, NULL, 'TPU-20BQ', '海康威视', NULL, NULL, NULL, 149, NULL, 1, NULL, '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (796, 'SCANNER041', NULL, '扫码枪', 4, NULL, 'TPU-20BQ', '海康威视', NULL, NULL, NULL, 150, NULL, 1, NULL, '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (797, 'SCANNER042', NULL, '扫码枪', 4, NULL, 'TPU-20BQ', '海康威视', NULL, NULL, NULL, 152, NULL, 1, NULL, '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (798, 'SCANNER043', NULL, '扫码枪', 4, NULL, 'TPU-20BQ', '海康威视', NULL, NULL, NULL, 151, NULL, 1, NULL, '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (799, 'SCANNER044', NULL, '扫码枪', 4, NULL, 'TPU-20BQ', '海康威视', NULL, NULL, NULL, 153, NULL, 1, NULL, '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (800, 'SCANNER045', NULL, '扫码枪', 4, NULL, 'TPU-20BQ', '海康威视', NULL, NULL, NULL, 158, NULL, 1, NULL, '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (801, 'SCANNER046', NULL, '扫码枪', 4, NULL, 'TPU-20BQ', '海康威视', NULL, NULL, NULL, 230, NULL, 1, NULL, '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (802, 'SCANNER047', NULL, '扫码枪', 4, NULL, 'TPU-20BQ', '海康威视', NULL, NULL, NULL, 234, NULL, 1, NULL, '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (803, 'SCANNER048', NULL, '扫码枪', 4, NULL, 'TPU-20BQ', '海康威视', NULL, NULL, NULL, 248, NULL, 1, NULL, '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (804, 'SCANNER049', NULL, '扫码枪', 4, NULL, 'TPU-20BQ', '海康威视', NULL, NULL, NULL, 250, NULL, 1, NULL, '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (805, 'SCANNER050', NULL, '扫码枪', 4, NULL, 'TPU-20BQ', '海康威视', NULL, NULL, NULL, 1, NULL, 1, NULL, '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (806, 'SCANNER051', NULL, '扫码枪', 4, NULL, 'TPU-20BQ', '海康威视', NULL, NULL, NULL, 255, NULL, 1, NULL, '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (807, 'SCANNER052', NULL, '扫码枪', 4, NULL, 'TPU-20BQ', '海康威视', NULL, NULL, NULL, 257, NULL, 1, NULL, '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (808, 'SCANNER053', NULL, '扫码枪', 4, NULL, 'TPU-20BQ', '海康威视', NULL, NULL, NULL, 259, NULL, 1, NULL, '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (809, 'SCANNER054', NULL, '扫码枪', 4, NULL, 'TPU-20BQ', '海康威视', NULL, NULL, NULL, 259, NULL, 1, NULL, '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (810, 'SCANNER055', NULL, '扫码枪', 4, NULL, 'TPU-20BQ', '海康威视', NULL, NULL, NULL, 263, NULL, 1, NULL, '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (811, 'SCANNER056', NULL, '扫码枪', 4, NULL, 'TPU-20BQ', '海康威视', NULL, NULL, NULL, 265, NULL, 1, NULL, '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (812, 'SCANNER057', NULL, '扫码枪', 4, NULL, 'TPU-20BQ', '海康威视', NULL, NULL, NULL, 267, NULL, 1, NULL, '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (813, 'SCANNER058', NULL, '扫码枪', 4, NULL, 'TPU-20BQ', '海康威视', NULL, NULL, NULL, 269, NULL, 1, NULL, '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (814, 'SCANNER059', NULL, '扫码枪', 4, NULL, 'TPU-20BQ', '海康威视', NULL, NULL, NULL, 156, NULL, 1, 'F2后副GP12', '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (815, 'SCANNER060', NULL, '扫码枪', 4, NULL, 'TPU-20BQ', '海康威视', NULL, NULL, NULL, 156, NULL, 1, 'F2后副GP12', '2025-04-03 00:57:10', '2025-04-03 00:57:10', 'IT', NULL);
INSERT INTO `assets` VALUES (816, 'SCANNER061', NULL, '扫码枪', 4, NULL, 'TPU-20BQ', '海康威视', NULL, NULL, NULL, 156, NULL, 1, 'E0XGP12', '2025-04-03 00:57:11', '2025-04-03 00:57:11', 'IT', NULL);
INSERT INTO `assets` VALUES (817, 'SCANNER062', NULL, '扫码枪', 4, NULL, 'TPU-20BQ', '海康威视', NULL, NULL, NULL, 157, NULL, 1, 'W01-GP12', '2025-04-03 00:57:11', '2025-04-03 00:57:11', 'IT', NULL);
INSERT INTO `assets` VALUES (818, 'SCANNER063', '', '扫码枪', 4, NULL, 'TPU-20BQ', '海康威视', NULL, NULL, NULL, 156, NULL, 1, 'GP12-11号工位', '2025-04-03 00:57:11', '2025-04-09 05:30:40', 'IT', NULL);
INSERT INTO `assets` VALUES (1009, '8512GZBTPR-001', NULL, '蓝牙打印机', 16, NULL, 'ZEBRA-ZR138', '斑马', NULL, NULL, NULL, 291, NULL, 1, '铝液熔炼C线', '2025-04-09 05:32:18', '2025-04-09 05:32:18', 'IT', NULL);
INSERT INTO `assets` VALUES (1010, '8512GZBTPR-002', NULL, '蓝牙打印机', 16, NULL, 'ZEBRA-ZR138', '斑马', NULL, NULL, NULL, 291, NULL, 1, '铝液熔炼B线', '2025-04-09 05:32:18', '2025-04-09 05:32:18', 'IT', NULL);
INSERT INTO `assets` VALUES (1011, '8512GZBTPR-003', NULL, '蓝牙打印机', 16, NULL, 'ZEBRA-ZR138', '斑马', NULL, NULL, NULL, 291, NULL, 1, '仓库发货', '2025-04-09 05:32:18', '2025-04-09 05:32:18', 'IT', NULL);
INSERT INTO `assets` VALUES (1012, '8512GZBTPR-004', NULL, '蓝牙打印机', 16, NULL, 'ZEBRA-ZR138', '斑马', NULL, NULL, NULL, 291, NULL, 1, '质量部质检一科', '2025-04-09 05:32:18', '2025-04-09 05:32:18', 'IT', NULL);
INSERT INTO `assets` VALUES (1013, '8512GZBTPR-005', NULL, '蓝牙打印机', 16, NULL, 'ZEBRA-ZR138', '斑马', NULL, NULL, NULL, 291, NULL, 1, '质量部质检一科', '2025-04-09 05:32:18', '2025-04-09 05:32:18', 'IT', NULL);
INSERT INTO `assets` VALUES (1014, '8512GZBTPR-006', NULL, '蓝牙打印机', 16, NULL, 'ZEBRA-ZR138', '斑马', NULL, NULL, NULL, 291, NULL, 1, '质量部质检二科GP12洛轲', '2025-04-09 05:32:18', '2025-04-09 05:32:18', 'IT', NULL);
INSERT INTO `assets` VALUES (1015, '8512GZBTPR-007', NULL, '蓝牙打印机', 16, NULL, 'ZEBRA-ZR138', '斑马', NULL, NULL, NULL, 291, NULL, 1, '质量部质检二科GP12陈光进', '2025-04-09 05:32:18', '2025-04-09 05:32:18', 'IT', NULL);
INSERT INTO `assets` VALUES (1016, '8512GZBTPR-008', NULL, '蓝牙打印机', 16, NULL, 'ZEBRA-ZR138', '斑马', NULL, NULL, NULL, 291, NULL, 1, '质量部质检二科巡检', '2025-04-09 05:32:18', '2025-04-09 05:32:18', 'IT', NULL);
INSERT INTO `assets` VALUES (1017, '8512GZBTPR-009', NULL, '蓝牙打印机', 16, NULL, 'ZEBRA-ZR138', '斑马', NULL, NULL, NULL, 291, NULL, 1, '质量部质检二科巡检', '2025-04-09 05:32:18', '2025-04-09 05:32:18', 'IT', NULL);
INSERT INTO `assets` VALUES (1018, '8512GZBTPR-010', NULL, '蓝牙打印机', 16, NULL, 'ZEBRA-ZR138', '斑马', NULL, NULL, NULL, 291, NULL, 1, '质量部质检二科', '2025-04-09 05:32:18', '2025-04-09 05:32:18', 'IT', NULL);
INSERT INTO `assets` VALUES (1019, '8512GZBTPR-011', NULL, '蓝牙打印机', 16, NULL, 'ZEBRA-ZR138', '斑马', NULL, NULL, NULL, 291, NULL, 1, '铝液熔炼A线', '2025-04-09 05:32:18', '2025-04-09 05:32:18', 'IT', NULL);
INSERT INTO `assets` VALUES (1020, '8512GZBTPR-012', NULL, '蓝牙打印机', 16, NULL, 'ZEBRA-ZR138', '斑马', NULL, NULL, NULL, 291, NULL, 1, '成品库', '2025-04-09 05:32:18', '2025-04-09 05:32:18', 'IT', NULL);
INSERT INTO `assets` VALUES (1021, '8512GZBTPR-013', NULL, '蓝牙打印机', 16, NULL, 'ZEBRA-ZR138', '斑马', NULL, NULL, NULL, 291, NULL, 1, '质量部质检一科', '2025-04-09 05:32:18', '2025-04-09 05:32:18', 'IT', NULL);
INSERT INTO `assets` VALUES (1022, '8512GZBTPR-014', NULL, '蓝牙打印机', 16, NULL, 'ZEBRA-ZR138', '斑马', NULL, NULL, NULL, 291, NULL, 1, '质量部质检一科', '2025-04-09 05:32:18', '2025-04-09 05:32:18', 'IT', NULL);
INSERT INTO `assets` VALUES (1023, '8512GZBTPR-015', NULL, '蓝牙打印机', 16, NULL, 'ZEBRA-ZR138', '斑马', NULL, NULL, NULL, 291, NULL, 1, '质量部质检一科', '2025-04-09 05:32:18', '2025-04-09 05:32:18', 'IT', NULL);
INSERT INTO `assets` VALUES (1024, '8512GZBTPR-016', NULL, '蓝牙打印机', 16, NULL, 'ZEBRA-ZR138', '斑马', NULL, NULL, NULL, 291, NULL, 1, '质量部质检一科', '2025-04-09 05:32:18', '2025-04-09 05:32:18', 'IT', NULL);
INSERT INTO `assets` VALUES (1025, '8512GZBTPR-017', NULL, '蓝牙打印机', 16, NULL, 'ZEBRA-ZR138', '斑马', NULL, NULL, NULL, 291, NULL, 1, '质量部质检二科返修区', '2025-04-09 05:32:19', '2025-04-09 05:32:19', 'IT', NULL);
INSERT INTO `assets` VALUES (1026, '8512GZBTPR-018', NULL, '蓝牙打印机', 16, NULL, 'ZEBRA-ZR138', '斑马', NULL, NULL, NULL, 291, NULL, 1, '质量部质检二科巡检', '2025-04-09 05:32:19', '2025-04-09 05:32:19', 'IT', NULL);
INSERT INTO `assets` VALUES (1027, '8512GZBTPR-019', NULL, '蓝牙打印机', 16, NULL, 'ZEBRA-ZR138', '斑马', NULL, NULL, NULL, 291, NULL, 1, '质量部质检二科巡检', '2025-04-09 05:32:19', '2025-04-09 05:32:19', 'IT', NULL);
INSERT INTO `assets` VALUES (1028, '8512GZBTPR-020', NULL, '蓝牙打印机', 16, NULL, 'ZEBRA-ZR138', '斑马', NULL, NULL, NULL, 291, NULL, 1, '质量部质检二科GP12-W01', '2025-04-09 05:32:19', '2025-04-09 05:32:19', 'IT', NULL);
INSERT INTO `assets` VALUES (1029, '8512GZBTPR-021', NULL, '蓝牙打印机', 16, NULL, 'ZEBRA-ZR138', '斑马', NULL, NULL, NULL, 291, NULL, 1, '质量部质检二科GP12-EOX', '2025-04-09 05:32:19', '2025-04-09 05:32:19', 'IT', NULL);
INSERT INTO `assets` VALUES (1030, '8512GZBTPR-022', NULL, '蓝牙打印机', 16, NULL, 'ZEBRA-ZR138', '斑马', NULL, NULL, NULL, 291, NULL, 1, '质量部质检二科GP12-EVR', '2025-04-09 05:32:19', '2025-04-09 05:32:19', 'IT', NULL);
INSERT INTO `assets` VALUES (1031, '8512GZBTPR-023', NULL, '蓝牙打印机', 16, NULL, 'ZEBRA-ZR138', '斑马', NULL, NULL, NULL, 291, NULL, 1, '质量部质检二科GP12-F2后副', '2025-04-09 05:32:19', '2025-04-09 05:32:19', 'IT', NULL);
INSERT INTO `assets` VALUES (1032, '8512GZWP001', NULL, '无线PDA', 19, NULL, 'TPU-10WT', '海康威视', NULL, NULL, NULL, 291, NULL, 1, '赵国强仓库', '2025-04-09 05:32:19', '2025-04-09 05:32:19', 'IT', NULL);
INSERT INTO `assets` VALUES (1033, '8512GZWP002', NULL, '无线PDA', 19, NULL, 'TPU-10WT', '海康威视', NULL, NULL, NULL, 291, NULL, 1, '铝液熔炼', '2025-04-09 05:32:19', '2025-04-09 05:32:19', 'IT', NULL);
INSERT INTO `assets` VALUES (1034, '8512GZWP003', NULL, '无线PDA', 19, NULL, 'TPU-10WT', '海康威视', NULL, NULL, NULL, 291, NULL, 1, '赵国强成品入库', '2025-04-09 05:32:19', '2025-04-09 05:32:19', 'IT', NULL);
INSERT INTO `assets` VALUES (1035, '8512GZWP004', NULL, '无线PDA', 19, NULL, 'TPU-10WT', '海康威视', NULL, NULL, NULL, 291, NULL, 1, '铝液熔炼', '2025-04-09 05:32:19', '2025-04-09 05:32:19', 'IT', NULL);
INSERT INTO `assets` VALUES (1036, '8512GZWP005', NULL, '无线PDA', 19, NULL, 'TPU-10WT', '海康威视', NULL, NULL, NULL, 291, NULL, 1, '仓库外协入库', '2025-04-09 05:32:19', '2025-04-09 05:32:19', 'IT', NULL);
INSERT INTO `assets` VALUES (1037, '8512GZWP006', NULL, '无线PDA', 19, NULL, 'TPU-10WT', '海康威视', NULL, NULL, NULL, 291, NULL, 1, '孙栋博', '2025-04-09 05:32:19', '2025-04-09 05:32:19', 'IT', NULL);
INSERT INTO `assets` VALUES (1038, '8512GZWP007', NULL, '无线PDA', 19, NULL, 'TPU-10WT', '海康威视', NULL, NULL, NULL, 291, NULL, 1, '翟志浩', '2025-04-09 05:32:19', '2025-04-09 05:32:19', 'IT', NULL);
INSERT INTO `assets` VALUES (1039, '8512GZWP008', NULL, '无线PDA', 19, NULL, 'TPU-10WT', '海康威视', NULL, NULL, NULL, 291, NULL, 1, '质量部质检一科', '2025-04-09 05:32:19', '2025-04-09 05:32:19', 'IT', NULL);
INSERT INTO `assets` VALUES (1040, '8512GZWP009', NULL, '无线PDA', 19, NULL, 'TPU-10WT', '海康威视', NULL, NULL, NULL, 291, NULL, 1, '质量部质检一科', '2025-04-09 05:32:19', '2025-04-09 05:32:19', 'IT', NULL);
INSERT INTO `assets` VALUES (1041, '8512GZWP010', NULL, '无线PDA', 19, NULL, 'TPU-10WT', '海康威视', NULL, NULL, NULL, 291, NULL, 1, '质量部质检二科GP12', '2025-04-09 05:32:19', '2025-04-09 05:32:19', 'IT', NULL);
INSERT INTO `assets` VALUES (1042, '8512GZWP011', NULL, '无线PDA', 19, NULL, 'TPU-10WT', '海康威视', NULL, NULL, NULL, 291, NULL, 1, '质量部质检二科GP12', '2025-04-09 05:32:19', '2025-04-09 05:32:19', 'IT', NULL);
INSERT INTO `assets` VALUES (1043, '8512GZWP012', NULL, '无线PDA', 19, NULL, 'TPU-10WT', '海康威视', NULL, NULL, NULL, 291, NULL, 1, '仓库外协入库', '2025-04-09 05:32:19', '2025-04-09 05:32:19', 'IT', NULL);
INSERT INTO `assets` VALUES (1044, '8512GZWP013', NULL, '无线PDA', 19, NULL, 'TPU-10WT', '海康威视', NULL, NULL, NULL, 291, NULL, 1, '制造一部砂芯模块', '2025-04-09 05:32:19', '2025-04-09 05:32:19', 'IT', NULL);
INSERT INTO `assets` VALUES (1045, '8512GZWP014', NULL, '无线PDA', 19, NULL, 'TPU-10WT', '海康威视', NULL, NULL, NULL, 291, NULL, 1, '制造一部砂芯模块', '2025-04-09 05:32:19', '2025-04-09 05:32:19', 'IT', NULL);
INSERT INTO `assets` VALUES (1046, '8512GZWP015', NULL, '无线PDA', 19, NULL, 'TPU-10WT', '海康威视', NULL, NULL, NULL, 291, NULL, 1, '制造一部砂芯模块', '2025-04-09 05:32:19', '2025-04-09 05:32:19', 'IT', NULL);
INSERT INTO `assets` VALUES (1047, '8512GZWP016', NULL, '无线PDA', 19, NULL, 'TPU-10WT', '海康威视', NULL, NULL, NULL, 291, NULL, 1, '制造一部砂芯模块', '2025-04-09 05:32:19', '2025-04-09 05:32:19', 'IT', NULL);
INSERT INTO `assets` VALUES (1048, '8512GZWP017', NULL, '无线PDA', 19, NULL, 'TPU-10WT', '海康威视', NULL, NULL, NULL, 291, NULL, 1, '制造一部砂芯模块', '2025-04-09 05:32:19', '2025-04-09 05:32:19', 'IT', NULL);
INSERT INTO `assets` VALUES (1049, '8512GZWP018', NULL, '无线PDA', 19, NULL, 'TPU-10WT', '海康威视', NULL, NULL, NULL, 291, NULL, 1, '制造一部砂芯模块', '2025-04-09 05:32:19', '2025-04-09 05:32:19', 'IT', NULL);
INSERT INTO `assets` VALUES (1050, '8512GZWP019', NULL, '无线PDA', 19, NULL, 'TPU-10WT', '海康威视', NULL, NULL, NULL, 291, NULL, 1, NULL, '2025-04-09 05:32:19', '2025-04-09 05:32:19', 'IT', NULL);
INSERT INTO `assets` VALUES (1051, '8512GZWP020', NULL, '无线PDA', 19, NULL, 'TPU-10WT', '海康威视', NULL, NULL, NULL, 291, NULL, 1, '铝液熔炼', '2025-04-09 05:32:19', '2025-04-09 05:32:19', 'IT', NULL);
INSERT INTO `assets` VALUES (1052, '8512GZWP021', NULL, '无线PDA', 19, NULL, 'TPU-10WT', '海康威视', NULL, NULL, NULL, 291, NULL, 1, '质量部质检二科巡检', '2025-04-09 05:32:19', '2025-04-09 05:32:19', 'IT', NULL);
INSERT INTO `assets` VALUES (1053, '8512GZWP022', NULL, '无线PDA', 19, NULL, 'TPU-10WT', '海康威视', NULL, NULL, NULL, 291, NULL, 1, '质量部质检二科巡检', '2025-04-09 05:32:19', '2025-04-09 05:32:19', 'IT', NULL);
INSERT INTO `assets` VALUES (1054, '8512GZWP023', NULL, '无线PDA', 19, NULL, 'TPU-10WT', '海康威视', NULL, NULL, NULL, 291, NULL, 1, '质量部质检二科巡检', '2025-04-09 05:32:19', '2025-04-09 05:32:19', 'IT', NULL);
INSERT INTO `assets` VALUES (1055, '8512GZWP024', NULL, '无线PDA', 19, NULL, 'TPU-10WT', '海康威视', NULL, NULL, NULL, 291, NULL, 1, 'F2后副', '2025-04-09 05:32:19', '2025-04-09 05:32:19', 'IT', NULL);
INSERT INTO `assets` VALUES (1056, '8512GZWP025', NULL, '无线PDA', 19, NULL, 'TPU-10WT', '海康威视', NULL, NULL, NULL, 291, NULL, 1, NULL, '2025-04-09 05:32:19', '2025-04-09 05:32:19', 'IT', NULL);
INSERT INTO `assets` VALUES (1057, '8512GZWP026', NULL, '无线PDA', 19, NULL, 'TPU-10WT', '海康威视', NULL, NULL, NULL, 291, NULL, 1, NULL, '2025-04-09 05:32:19', '2025-04-09 05:32:19', 'IT', NULL);
INSERT INTO `assets` VALUES (1058, '8512GZWP027', NULL, '无线PDA', 19, NULL, 'TPU-10WT', '海康威视', NULL, NULL, NULL, 291, NULL, 1, 'w01螺纹检测', '2025-04-09 05:32:19', '2025-04-09 05:32:19', 'IT', NULL);
INSERT INTO `assets` VALUES (1059, '8512GZWP028', NULL, '无线PDA', 19, NULL, 'TPU-10WT', '海康威视', NULL, NULL, NULL, 291, NULL, 1, '质量部质检二科巡检', '2025-04-09 05:32:19', '2025-04-09 05:32:19', 'IT', NULL);
INSERT INTO `assets` VALUES (1060, '8512GZWP029', NULL, '无线PDA', 19, NULL, 'TPU-10WT', '海康威视', NULL, NULL, NULL, 291, NULL, 1, '质量部质检二科返修区', '2025-04-09 05:32:19', '2025-04-09 05:32:19', 'IT', NULL);
INSERT INTO `assets` VALUES (1061, '8512GZWP030', NULL, '无线PDA', 19, NULL, 'TPU-10WT', '海康威视', NULL, NULL, NULL, 291, NULL, 1, '质量部质检二科GP12（洛轲）', '2025-04-09 05:32:19', '2025-04-09 05:32:19', 'IT', NULL);
INSERT INTO `assets` VALUES (1062, '8512GZWP031', NULL, '无线PDA', 19, NULL, 'TPU-10WT', '海康威视', NULL, NULL, NULL, 291, NULL, 1, '质量部质检二科GP12-W01', '2025-04-09 05:32:19', '2025-04-09 05:32:19', 'IT', NULL);
INSERT INTO `assets` VALUES (1063, '8512GZWP032', NULL, '无线PDA', 19, NULL, 'TPU-10WT', '海康威视', NULL, NULL, NULL, 291, NULL, 1, '质量部质检二科GP12-E0X', '2025-04-09 05:32:19', '2025-04-09 05:32:19', 'IT', NULL);
INSERT INTO `assets` VALUES (1064, '8512GZWP033', NULL, '无线PDA', 19, NULL, 'TPU-10WT', '海康威视', NULL, NULL, NULL, 291, NULL, 1, NULL, '2025-04-09 05:32:19', '2025-04-09 05:32:19', 'IT', NULL);
INSERT INTO `assets` VALUES (1065, '8512GZWP034', NULL, '无线PDA', 19, NULL, 'TPU-10WT', '海康威视', NULL, NULL, NULL, 291, NULL, 1, NULL, '2025-04-09 05:32:19', '2025-04-09 05:32:19', 'IT', NULL);
INSERT INTO `assets` VALUES (1066, '8512GZWP035', NULL, '无线PDA', 19, NULL, 'TPU-10WT', '海康威视', NULL, NULL, NULL, 291, NULL, 1, NULL, '2025-04-09 05:32:19', '2025-04-09 05:32:19', 'IT', NULL);
INSERT INTO `assets` VALUES (1067, '8512GZWP036', NULL, '无线PDA', 19, NULL, 'TPU-10WT', '海康威视', NULL, NULL, NULL, 291, NULL, 1, NULL, '2025-04-09 05:32:19', '2025-04-09 05:32:19', 'IT', NULL);
INSERT INTO `assets` VALUES (1068, '8512GZWP037', NULL, '无线PDA', 19, NULL, 'TPU-10WT', '海康威视', NULL, NULL, NULL, 291, NULL, 1, NULL, '2025-04-09 05:32:19', '2025-04-09 05:32:19', 'IT', NULL);
INSERT INTO `assets` VALUES (1069, '8512GZWP038', NULL, '无线PDA', 19, NULL, 'TPU-10WT', '海康威视', NULL, NULL, NULL, 291, NULL, 1, '制造一部砂芯模块', '2025-04-09 05:32:19', '2025-04-09 05:32:19', 'IT', NULL);
INSERT INTO `assets` VALUES (1070, '8512GZWP039', NULL, '无线PDA', 19, NULL, 'TPU-10WT', '海康威视', NULL, NULL, NULL, 291, NULL, 1, '制造一部砂芯模块', '2025-04-09 05:32:19', '2025-04-09 05:32:19', 'IT', NULL);
INSERT INTO `assets` VALUES (1071, '8512GZWP040', NULL, '无线PDA', 19, NULL, 'TPU-10WT', '海康威视', NULL, NULL, NULL, 291, NULL, 1, '铝液熔炼', '2025-04-09 05:32:19', '2025-04-09 05:32:19', 'IT', NULL);
INSERT INTO `assets` VALUES (1072, '8512GZWP041', NULL, '无线PDA', 19, NULL, 'TPU-10WT', '海康威视', NULL, NULL, NULL, 291, NULL, 1, '铝液熔炼', '2025-04-09 05:32:19', '2025-04-09 05:32:19', 'IT', NULL);
INSERT INTO `assets` VALUES (1073, '8512GZWP042', NULL, '无线PDA', 19, NULL, 'TPU-10WT', '海康威视', NULL, NULL, NULL, 291, NULL, 1, '质量部质检二科GP12-EVR', '2025-04-09 05:32:19', '2025-04-09 05:32:19', 'IT', NULL);
INSERT INTO `assets` VALUES (1074, '8512GZWP043', NULL, '无线PDA', 19, NULL, 'TPU-10WT', '海康威视', NULL, NULL, NULL, 291, NULL, 1, '质量部质检二科GP12-F2后副', '2025-04-09 05:32:19', '2025-04-09 05:32:19', 'IT', NULL);
INSERT INTO `assets` VALUES (1075, '8512GZWP044', NULL, '无线PDA', 19, NULL, 'TPU-10WT', '海康威视', NULL, NULL, NULL, 291, NULL, 1, NULL, '2025-04-09 05:32:19', '2025-04-09 05:32:19', 'IT', NULL);
INSERT INTO `assets` VALUES (1076, '8512GZWP045', NULL, '无线PDA', 19, NULL, 'TPU-10WT', '海康威视', NULL, NULL, NULL, 291, NULL, 1, '巡检', '2025-04-09 05:32:19', '2025-04-09 05:32:19', 'IT', NULL);
INSERT INTO `assets` VALUES (1077, '8512GZWP046', NULL, '无线PDA', 19, NULL, 'TPU-10WT', '海康威视', NULL, NULL, NULL, 291, NULL, 1, '巡检', '2025-04-09 05:32:19', '2025-04-09 05:32:19', 'IT', NULL);
INSERT INTO `assets` VALUES (1078, '8512GZWP047', NULL, '无线PDA', 19, NULL, 'TPU-10WT', '海康威视', NULL, NULL, NULL, 291, NULL, 1, '巡检', '2025-04-09 05:32:19', '2025-04-09 05:32:19', 'IT', NULL);
INSERT INTO `assets` VALUES (1079, '8512GZWP048', NULL, '无线PDA', 19, NULL, 'TPU-10WT', '海康威视', NULL, NULL, NULL, 291, NULL, 1, 'GP12（F1后副）', '2025-04-09 05:32:19', '2025-04-09 05:32:19', 'IT', NULL);
INSERT INTO `assets` VALUES (1080, '8512GZWP049', NULL, '无线PDA', 19, NULL, 'TPU-10WT', '海康威视', NULL, NULL, NULL, 291, NULL, 1, 'GP12（F2前副）', '2025-04-09 05:32:19', '2025-04-09 05:32:19', 'IT', NULL);
INSERT INTO `assets` VALUES (1081, '8512GZWP050', NULL, '无线PDA', 19, NULL, 'TPU-10WT', '海康威视', NULL, NULL, NULL, 291, NULL, 1, 'GP12（F2后副）', '2025-04-09 05:32:19', '2025-04-09 05:32:19', 'IT', NULL);
INSERT INTO `assets` VALUES (1082, '8512GZWP051', NULL, '无线PDA', 19, NULL, 'TPU-10WT', '海康威视', NULL, NULL, NULL, 291, NULL, 1, '质量部质检一科', '2025-04-09 05:32:19', '2025-04-09 05:32:19', 'IT', NULL);
INSERT INTO `assets` VALUES (1083, '8512GZWP052', NULL, '无线PDA', 19, NULL, 'TPU-10WT', '海康威视', NULL, NULL, NULL, 291, NULL, 1, '质量部质检一科', '2025-04-09 05:32:19', '2025-04-09 05:32:19', 'IT', NULL);
INSERT INTO `assets` VALUES (1084, '8512GZWP053', NULL, '无线PDA', 19, NULL, 'TPU-10WT', '海康威视', NULL, NULL, NULL, 291, NULL, 1, '质量部质检一科', '2025-04-09 05:32:19', '2025-04-09 05:32:19', 'IT', NULL);
INSERT INTO `assets` VALUES (1085, '8512GZWP054', NULL, '无线PDA', 19, NULL, 'TPU-10WT', '海康威视', NULL, NULL, NULL, 291, NULL, 1, '质量部质检一科', '2025-04-09 05:32:19', '2025-04-09 05:32:19', 'IT', NULL);
INSERT INTO `assets` VALUES (1086, '8512GZWP055', NULL, '无线PDA', 19, NULL, 'TPU-10WT', '海康威视', NULL, NULL, NULL, 291, NULL, 1, '质量实验室', '2025-04-09 05:32:19', '2025-04-09 05:32:19', 'IT', NULL);
INSERT INTO `assets` VALUES (1087, '8512GZWP056', NULL, '无线PDA', 19, NULL, 'TPU-10WT', '海康威视', NULL, NULL, NULL, 291, NULL, 1, '机加车间-毛坯检', '2025-04-09 05:32:19', '2025-04-09 05:32:19', 'IT', NULL);
INSERT INTO `assets` VALUES (1088, '8512GZWP057', NULL, '无线PDA', 19, NULL, 'TPU-10WT', '海康威视', NULL, NULL, NULL, 291, NULL, 1, '机加车间-毛坯检', '2025-04-09 05:32:19', '2025-04-09 05:32:19', 'IT', NULL);
INSERT INTO `assets` VALUES (1089, '8512GZWP058', NULL, '无线PDA', 19, NULL, 'TPU-10WT', '海康威视', NULL, NULL, NULL, 291, NULL, 1, '质量部质检一科铸造', '2025-04-09 05:32:19', '2025-04-09 05:32:19', 'IT', NULL);
INSERT INTO `assets` VALUES (1090, '8512GZWP059', NULL, '无线PDA', 19, NULL, 'TPU-10WT', '海康威视', NULL, NULL, NULL, 291, NULL, 1, '质量部质检一科铸造', '2025-04-09 05:32:19', '2025-04-09 05:32:19', 'IT', NULL);
INSERT INTO `assets` VALUES (1091, '8512GZWP060', NULL, '无线PDA', 19, NULL, 'TPU-10WT', '海康威视', NULL, NULL, NULL, 291, NULL, 1, '铸造铝液熔炼', '2025-04-09 05:32:19', '2025-04-09 05:32:19', 'IT', NULL);
INSERT INTO `assets` VALUES (1092, '8512GZWP061', NULL, '无线PDA', 19, NULL, 'TPU-10WT', '海康威视', NULL, NULL, NULL, 291, NULL, 1, '铸造铝液熔炼', '2025-04-09 05:32:19', '2025-04-09 05:32:19', 'IT', NULL);
INSERT INTO `assets` VALUES (1093, '8512GZWP062', NULL, '无线PDA', 19, NULL, 'TPU-10WT', '海康威视', NULL, NULL, NULL, 291, NULL, 1, '质量部一科', '2025-04-09 05:32:19', '2025-04-09 05:32:19', 'IT', NULL);
INSERT INTO `assets` VALUES (1094, '8512GZWP063', NULL, '无线PDA', 19, NULL, 'TPU-10WT', '海康威视', NULL, NULL, NULL, 291, NULL, 1, '质量部一科', '2025-04-09 05:32:19', '2025-04-09 05:32:19', 'IT', NULL);
INSERT INTO `assets` VALUES (1095, '8512GZWP064', NULL, '无线PDA', 19, NULL, 'TPU-10WT', '海康威视', NULL, NULL, NULL, 291, NULL, 1, '质量部一科', '2025-04-09 05:32:19', '2025-04-09 05:32:19', 'IT', NULL);
INSERT INTO `assets` VALUES (1096, '8512GZWP065', NULL, '无线PDA', 19, NULL, 'TPU-10WT', '海康威视', NULL, NULL, NULL, 291, NULL, 1, '质量部一科', '2025-04-09 05:32:19', '2025-04-09 05:32:19', 'IT', NULL);
INSERT INTO `assets` VALUES (1097, '8512GZWP066', NULL, '无线PDA', 19, NULL, 'TPU-10WT', '海康威视', NULL, NULL, NULL, 291, NULL, 1, '质量部一科', '2025-04-09 05:32:19', '2025-04-09 05:32:19', 'IT', NULL);
INSERT INTO `assets` VALUES (1098, '8512GZWP067', NULL, '无线PDA', 19, NULL, 'TPU-10WT', '海康威视', NULL, NULL, NULL, 291, NULL, 1, '质量部一科', '2025-04-09 05:32:19', '2025-04-09 05:32:19', 'IT', NULL);
INSERT INTO `assets` VALUES (1099, '8512GZWP068', NULL, '无线PDA', 19, NULL, 'TPU-10WT', '海康威视', NULL, NULL, NULL, 291, NULL, 1, '质量部一科', '2025-04-09 05:32:19', '2025-04-09 05:32:19', 'IT', NULL);
INSERT INTO `assets` VALUES (1100, '8512GZWP069', NULL, '无线PDA', 19, NULL, 'TPU-10WT', '海康威视', NULL, NULL, NULL, 291, NULL, 1, '质量部一科', '2025-04-09 05:32:19', '2025-04-09 05:32:19', 'IT', NULL);
INSERT INTO `assets` VALUES (1101, '8512GZWP070', NULL, '无线PDA', 19, NULL, 'TPU-10WT', '海康威视', NULL, NULL, NULL, 291, NULL, 1, '质量部一科', '2025-04-09 05:32:19', '2025-04-09 05:32:19', 'IT', NULL);
INSERT INTO `assets` VALUES (1102, '8512GZWP071', NULL, '无线PDA', 19, NULL, 'TPU-10WT', '海康威视', NULL, NULL, NULL, 291, NULL, 1, '质量部一科', '2025-04-09 05:32:19', '2025-04-09 05:32:19', 'IT', NULL);

-- ----------------------------
-- Table structure for assettypes
-- ----------------------------
DROP TABLE IF EXISTS `assettypes`;
CREATE TABLE `assettypes`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `Name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '类型名称',
  `Code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '类型编码',
  `Description` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '类型描述',
  `ParentId` int NULL DEFAULT NULL COMMENT '父类型ID',
  `RequireSerialNumber` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否需要序列号',
  `SortOrder` int NOT NULL DEFAULT 0 COMMENT '排序',
  `IsActive` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否激活',
  `CreatedAt` datetime(0) NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime(0) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE INDEX `IX_AssetTypes_Code`(`Code`) USING BTREE,
  UNIQUE INDEX `IX_AssetTypes_Name`(`Name`) USING BTREE,
  INDEX `IX_AssetTypes_ParentId`(`ParentId`) USING BTREE,
  CONSTRAINT `FK_AssetTypes_AssetTypes_ParentId` FOREIGN KEY (`ParentId`) REFERENCES `assettypes` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 31 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '资产类型表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of assettypes
-- ----------------------------
INSERT INTO `assettypes` VALUES (1, '电脑设备', 'COMPUTER', '计算机类设备', NULL, 1, 10, 1, '2025-03-23 20:45:09', '2025-04-03 15:25:57');
INSERT INTO `assettypes` VALUES (2, '网络设备', 'NETWORK', '网络通信设备', NULL, 1, 20, 1, '2025-03-23 20:45:09', '2025-04-03 15:26:01');
INSERT INTO `assettypes` VALUES (3, '打印设备', 'PRINTER', '打印类设备', NULL, 1, 30, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `assettypes` VALUES (4, '扫描设备', 'SCANNER', '扫描类设备', NULL, 1, 40, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `assettypes` VALUES (5, '移动设备', 'MOBILE', '移动终端设备', NULL, 1, 50, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `assettypes` VALUES (6, '存储设备', 'STORAGE', '数据存储设备', NULL, 1, 60, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `assettypes` VALUES (7, '工控机', 'IPC', '工业控制计算机', 1, 1, 11, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `assettypes` VALUES (9, '笔记本电脑', 'LAPTOP', '笔记本电脑', 1, 1, 13, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `assettypes` VALUES (10, '服务器', 'SERVER', '服务器设备', 1, 1, 14, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `assettypes` VALUES (11, '交换机', 'SWITCH', '网络交换机', 2, 1, 21, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `assettypes` VALUES (12, '路由器', 'ROUTER', '网络路由器', 2, 1, 22, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `assettypes` VALUES (13, '防火墙', 'FIREWALL', '网络防火墙', 2, 1, 23, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `assettypes` VALUES (14, '激光打印机', 'LASER_PRINTER', '激光打印机', 3, 1, 31, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `assettypes` VALUES (15, '标签打印机', 'LABEL_PRINTER', '标签打印机', 3, 1, 32, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `assettypes` VALUES (16, '蓝牙打印机', 'BT_PRINTER', '蓝牙打印机', 3, 1, 33, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `assettypes` VALUES (17, '条码扫描枪', 'BARCODE_SCANNER', '条码扫描枪', 4, 1, 41, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `assettypes` VALUES (18, '扫码枪', 'QR_SCANNER', '扫码枪', 4, 1, 42, 1, '2025-03-23 20:45:09', '2025-03-26 22:02:16');
INSERT INTO `assettypes` VALUES (19, 'PDA', 'PDA', '掌上数据终端', 5, 1, 51, 1, '2025-03-23 20:45:09', '2025-03-26 22:10:39');
INSERT INTO `assettypes` VALUES (24, '其他', 'OTHER', '默认资产类型-其他', NULL, 1, 0, 1, '2025-03-30 13:01:19', '2025-03-30 13:01:19');
INSERT INTO `assettypes` VALUES (27, '电池', 'SCBATTERY', '扫码枪电池', NULL, 1, 0, 1, '2025-04-03 09:20:31', '2025-04-03 15:25:53');

-- ----------------------------
-- Table structure for attachments
-- ----------------------------
DROP TABLE IF EXISTS `attachments`;
CREATE TABLE `attachments`  (
  `AttachmentId` bigint NOT NULL AUTO_INCREMENT COMMENT '附件ID (BIGINT)',
  `TaskId` bigint NULL DEFAULT NULL COMMENT '关联的任务ID',
  `CommentId` bigint NULL DEFAULT NULL COMMENT '关联的评论ID',
  `UploaderUserId` int NOT NULL COMMENT '上传用户ID (关联 users.Id - INT)',
  `FileName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '原始文件名',
  `StoredFileName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '存储文件名 (避免重复，建议UUID)',
  `FilePath` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '文件存储路径 (相对路径或包含Bucket信息)',
  `FileSize` bigint NOT NULL COMMENT '文件大小 (字节)',
  `FileType` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '文件MIME类型',
  `IsPreviewable` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否可在线预览',
  `StorageType` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'Local' COMMENT '存储类型 (Local, S3, AzureBlob)',
  `CreationTimestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`AttachmentId`) USING BTREE,
  UNIQUE INDEX `idx_attachments_storedfilename`(`StoredFileName`) USING BTREE,
  INDEX `idx_attachments_task`(`TaskId`) USING BTREE,
  INDEX `idx_attachments_comment`(`CommentId`) USING BTREE,
  INDEX `idx_attachments_uploader`(`UploaderUserId`) USING BTREE,
  CONSTRAINT `FK_Attachments_Comment` FOREIGN KEY (`CommentId`) REFERENCES `comments` (`CommentId`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `FK_Attachments_Task` FOREIGN KEY (`TaskId`) REFERENCES `tasks` (`TaskId`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `FK_Attachments_UploaderUser` FOREIGN KEY (`UploaderUserId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '任务附件表 (V2 - BIGINT PK)' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of attachments
-- ----------------------------

-- ----------------------------
-- Table structure for auditlogs
-- ----------------------------
DROP TABLE IF EXISTS `auditlogs`;
CREATE TABLE `auditlogs`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `UserId` int NULL DEFAULT NULL COMMENT '用户ID',
  `UserName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '用户名',
  `Type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '日志类型',
  `TableName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '表名',
  `DateTime` datetime(0) NOT NULL COMMENT '日期时间',
  `PrimaryKey` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '主键',
  `OldValues` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '旧值',
  `NewValues` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '新值',
  `Action` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '操作',
  `ClientIP` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '客户端IP',
  PRIMARY KEY (`Id`) USING BTREE,
  INDEX `IX_AuditLogs_UserId`(`UserId`) USING BTREE,
  CONSTRAINT `FK_AuditLogs_Users_UserId` FOREIGN KEY (`UserId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '审计日志表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of auditlogs
-- ----------------------------

-- ----------------------------
-- Table structure for comments
-- ----------------------------
DROP TABLE IF EXISTS `comments`;
CREATE TABLE `comments`  (
  `CommentId` bigint NOT NULL AUTO_INCREMENT COMMENT '评论ID (BIGINT)',
  `TaskId` bigint NOT NULL COMMENT '关联的任务ID',
  `UserId` int NOT NULL COMMENT '评论用户ID (关联 users.Id - INT)',
  `ParentCommentId` bigint NULL DEFAULT NULL COMMENT '父评论ID (用于回复)',
  `Content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '评论内容 (支持Markdown或HTML)',
  `MentionedUserIds` json NULL COMMENT '评论中@提及的用户ID列表 (INT IDs in JSON array)',
  `IsPinned` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否置顶',
  `IsEdited` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否被编辑过',
  `CreationTimestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
  `LastUpdatedTimestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`CommentId`) USING BTREE,
  INDEX `idx_comments_task_time`(`TaskId`, `CreationTimestamp`) USING BTREE,
  INDEX `idx_comments_user`(`UserId`) USING BTREE,
  INDEX `idx_comments_parent`(`ParentCommentId`) USING BTREE,
  CONSTRAINT `FK_Comments_ParentComment` FOREIGN KEY (`ParentCommentId`) REFERENCES `comments` (`CommentId`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `FK_Comments_Task` FOREIGN KEY (`TaskId`) REFERENCES `tasks` (`TaskId`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `FK_Comments_User` FOREIGN KEY (`UserId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '任务评论表 (V2 - BIGINT PK)' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of comments
-- ----------------------------

-- ----------------------------
-- Table structure for daily_reports
-- ----------------------------
DROP TABLE IF EXISTS `daily_reports`;
CREATE TABLE `daily_reports`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `report_date` date NOT NULL,
  `report_type` enum('daily','weekly','monthly') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'daily',
  `file_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '报表文件路径',
  `snapshot_data` json NULL COMMENT '数据快照',
  `created_at` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_report_date_type`(`report_date`, `report_type`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of daily_reports
-- ----------------------------

-- ----------------------------
-- Table structure for dashboard_config
-- ----------------------------
DROP TABLE IF EXISTS `dashboard_config`;
CREATE TABLE `dashboard_config`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `theme` enum('light','dark') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'dark',
  `layout` json NULL COMMENT '面板布局配置',
  `auto_refresh` int NULL DEFAULT 60 COMMENT '刷新间隔(秒)',
  `updated_at` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_dashboard_user`(`user_id`) USING BTREE,
  CONSTRAINT `fk_dashboard_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`Id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of dashboard_config
-- ----------------------------

-- ----------------------------
-- Table structure for db_structure_result
-- ----------------------------
DROP TABLE IF EXISTS `db_structure_result`;
CREATE TABLE `db_structure_result`  (
  `section` varchar(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '',
  `表名` varchar(64) CHARACTER SET utf8 COLLATE utf8_tolower_ci NULL DEFAULT NULL,
  `列名` varchar(64) CHARACTER SET utf8 COLLATE utf8_tolower_ci NULL DEFAULT NULL,
  `数据类型` mediumtext CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `可为空` varchar(3) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
  `默认值` text CHARACTER SET utf8 COLLATE utf8_bin NULL,
  `键` enum('','PRI','UNI','MUL') CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `额外信息` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `注释` text CHARACTER SET utf8 COLLATE utf8_bin NOT NULL
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of db_structure_result
-- ----------------------------
INSERT INTO `db_structure_result` VALUES ('列信息', '__efmigrationshistory', 'MigrationId', 'varchar(150)', 'NO', NULL, 'PRI', '', '');
INSERT INTO `db_structure_result` VALUES ('列信息', '__efmigrationshistory', 'ProductVersion', 'varchar(32)', 'NO', NULL, '', '', '');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assethistories', 'Id', 'int', 'NO', NULL, 'PRI', 'auto_increment', '');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assethistories', 'AssetId', 'int', 'NO', NULL, 'MUL', '', '资产ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assethistories', 'OperationType', 'int', 'NO', NULL, '', '', '操作类型：1创建，2修改，3删除，4位置变更，5状态变更');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assethistories', 'OperatorId', 'int', 'NO', NULL, 'MUL', '', '操作人ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assethistories', 'OperationTime', 'datetime', 'NO', NULL, '', '', '操作时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assethistories', 'Description', 'text', 'YES', NULL, '', '', '描述（JSON格式，记录变更前后的属性值）');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assethistories', 'CreatedAt', 'datetime', 'NO', NULL, '', '', '创建时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assethistories', 'UpdatedAt', 'datetime', 'NO', NULL, '', '', '更新时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assetreceives', 'Id', 'int', 'NO', NULL, 'PRI', 'auto_increment', '');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assetreceives', 'ReceiveCode', 'varchar(50)', 'NO', NULL, 'UNI', '', '入库单号');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assetreceives', 'PurchaseOrderId', 'int', 'YES', NULL, 'MUL', '', '采购订单ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assetreceives', 'Title', 'varchar(100)', 'NO', NULL, '', '', '标题');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assetreceives', 'Description', 'varchar(500)', 'YES', NULL, '', '', '描述');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assetreceives', 'ReceiverId', 'int', 'NO', NULL, 'MUL', '', '接收人ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assetreceives', 'ReceiveTime', 'datetime', 'NO', NULL, '', '', '接收时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assetreceives', 'Status', 'int', 'NO', '0', '', '', '状态：0草稿，1已提交，2已确认');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assetreceives', 'AssetTypeId', 'int', 'YES', NULL, 'MUL', '', '资产类型ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assetreceives', 'Quantity', 'int', 'NO', '1', '', '', '数量');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assetreceives', 'TotalAmount', 'decimal(18,2)', 'NO', '0.00', '', '', '总金额');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assetreceives', 'InitialLocationId', 'int', 'YES', NULL, 'MUL', '', '初始位置ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assetreceives', 'Notes', 'varchar(500)', 'YES', NULL, '', '', '备注');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assetreceives', 'CreatedAt', 'datetime', 'NO', NULL, '', '', '创建时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assetreceives', 'UpdatedAt', 'datetime', 'NO', NULL, '', '', '更新时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assets', 'Id', 'int', 'NO', NULL, 'PRI', 'auto_increment', '');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assets', 'AssetCode', 'varchar(50)', 'NO', NULL, 'UNI', '', '资产编码');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assets', 'FinancialCode', 'varchar(50)', 'YES', NULL, 'MUL', '', '财务编号');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assets', 'Name', 'varchar(100)', 'NO', NULL, '', '', '资产名称');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assets', 'AssetTypeId', 'int', 'NO', NULL, 'MUL', '', '资产类型ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assets', 'SerialNumber', 'varchar(100)', 'YES', NULL, '', '', '序列号');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assets', 'Model', 'varchar(100)', 'YES', NULL, '', '', '型号');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assets', 'Brand', 'varchar(100)', 'YES', NULL, '', '', '品牌');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assets', 'PurchaseDate', 'datetime', 'YES', NULL, '', '', '购买日期');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assets', 'WarrantyExpireDate', 'datetime', 'YES', NULL, '', '', '保修到期日');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assets', 'Price', 'decimal(18,2)', 'YES', NULL, '', '', '价格');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assets', 'LocationId', 'int', 'YES', NULL, 'MUL', '', '位置ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assets', 'DepartmentId', 'int', 'YES', NULL, 'MUL', '', '');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assets', 'Status', 'int', 'NO', '0', '', '', '状态：0闲置，1在用，2维修中，3报废');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assets', 'Notes', 'varchar(500)', 'YES', NULL, '', '', '备注');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assets', 'CreatedAt', 'datetime', 'NO', NULL, '', '', '创建时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assets', 'UpdatedAt', 'datetime', 'NO', NULL, '', '', '更新时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assettypes', 'Id', 'int', 'NO', NULL, 'PRI', 'auto_increment', '');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assettypes', 'Name', 'varchar(50)', 'NO', NULL, 'UNI', '', '类型名称');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assettypes', 'Code', 'varchar(50)', 'NO', NULL, 'UNI', '', '类型编码');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assettypes', 'Description', 'varchar(200)', 'YES', NULL, '', '', '类型描述');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assettypes', 'ParentId', 'int', 'YES', NULL, 'MUL', '', '父类型ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assettypes', 'RequireSerialNumber', 'tinyint(1)', 'NO', '1', '', '', '是否需要序列号');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assettypes', 'SortOrder', 'int', 'NO', '0', '', '', '排序');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assettypes', 'IsActive', 'tinyint(1)', 'NO', '1', '', '', '是否激活');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assettypes', 'CreatedAt', 'datetime', 'NO', NULL, '', '', '创建时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assettypes', 'UpdatedAt', 'datetime', 'NO', NULL, '', '', '更新时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'auditlogs', 'Id', 'int', 'NO', NULL, 'PRI', 'auto_increment', '');
INSERT INTO `db_structure_result` VALUES ('列信息', 'auditlogs', 'UserId', 'int', 'YES', NULL, 'MUL', '', '用户ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'auditlogs', 'UserName', 'varchar(50)', 'YES', NULL, '', '', '用户名');
INSERT INTO `db_structure_result` VALUES ('列信息', 'auditlogs', 'Type', 'varchar(50)', 'NO', NULL, '', '', '日志类型');
INSERT INTO `db_structure_result` VALUES ('列信息', 'auditlogs', 'TableName', 'varchar(50)', 'YES', NULL, '', '', '表名');
INSERT INTO `db_structure_result` VALUES ('列信息', 'auditlogs', 'DateTime', 'datetime', 'NO', NULL, '', '', '日期时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'auditlogs', 'PrimaryKey', 'varchar(50)', 'YES', NULL, '', '', '主键');
INSERT INTO `db_structure_result` VALUES ('列信息', 'auditlogs', 'OldValues', 'longtext', 'YES', NULL, '', '', '旧值');
INSERT INTO `db_structure_result` VALUES ('列信息', 'auditlogs', 'NewValues', 'longtext', 'YES', NULL, '', '', '新值');
INSERT INTO `db_structure_result` VALUES ('列信息', 'auditlogs', 'Action', 'varchar(50)', 'YES', NULL, '', '', '操作');
INSERT INTO `db_structure_result` VALUES ('列信息', 'auditlogs', 'ClientIP', 'varchar(50)', 'YES', NULL, '', '', '客户端IP');
INSERT INTO `db_structure_result` VALUES ('列信息', 'db_structure_result', 'section', 'varchar(3)', 'NO', '', '', '', '');
INSERT INTO `db_structure_result` VALUES ('列信息', 'db_structure_result', '表名', 'varchar(64)', 'YES', NULL, '', '', '');
INSERT INTO `db_structure_result` VALUES ('列信息', 'db_structure_result', '列名', 'varchar(64)', 'YES', NULL, '', '', '');
INSERT INTO `db_structure_result` VALUES ('列信息', 'db_structure_result', '数据类型', 'mediumtext', 'NO', NULL, '', '', '');
INSERT INTO `db_structure_result` VALUES ('列信息', 'db_structure_result', '可为空', 'varchar(3)', 'NO', '', '', '', '');
INSERT INTO `db_structure_result` VALUES ('列信息', 'db_structure_result', '默认值', 'text', 'YES', NULL, '', '', '');
INSERT INTO `db_structure_result` VALUES ('列信息', 'db_structure_result', '键', 'enum(\'\',\'PRI\',\'UNI\',\'MUL\')', 'NO', NULL, '', '', '');
INSERT INTO `db_structure_result` VALUES ('列信息', 'db_structure_result', '额外信息', 'varchar(256)', 'YES', NULL, '', '', '');
INSERT INTO `db_structure_result` VALUES ('列信息', 'db_structure_result', '注释', 'text', 'NO', NULL, '', '', '');
INSERT INTO `db_structure_result` VALUES ('列信息', 'departments', 'Id', 'int', 'NO', NULL, 'PRI', 'auto_increment', '');
INSERT INTO `db_structure_result` VALUES ('列信息', 'departments', 'Name', 'varchar(50)', 'NO', NULL, 'UNI', '', '部门名称');
INSERT INTO `db_structure_result` VALUES ('列信息', 'departments', 'Code', 'varchar(50)', 'NO', NULL, 'UNI', '', '部门编码');
INSERT INTO `db_structure_result` VALUES ('列信息', 'departments', 'ParentId', 'int', 'YES', NULL, 'MUL', '', '父部门ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'departments', 'ManagerId', 'int', 'YES', NULL, 'MUL', '', '部门经理ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'departments', 'Path', 'varchar(200)', 'YES', NULL, '', '', '层级路径');
INSERT INTO `db_structure_result` VALUES ('列信息', 'departments', 'Description', 'varchar(200)', 'YES', NULL, '', '', '部门描述');
INSERT INTO `db_structure_result` VALUES ('列信息', 'departments', 'IsActive', 'tinyint(1)', 'NO', '1', '', '', '是否激活');
INSERT INTO `db_structure_result` VALUES ('列信息', 'departments', 'CreatedAt', 'datetime', 'NO', NULL, '', '', '创建时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'departments', 'UpdatedAt', 'datetime', 'NO', NULL, '', '', '更新时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'faultrecords', 'Id', 'int', 'NO', NULL, 'PRI', 'auto_increment', '');
INSERT INTO `db_structure_result` VALUES ('列信息', 'faultrecords', 'Title', 'varchar(100)', 'NO', NULL, '', '', '标题');
INSERT INTO `db_structure_result` VALUES ('列信息', 'faultrecords', 'Description', 'varchar(500)', 'YES', NULL, '', '', '描述');
INSERT INTO `db_structure_result` VALUES ('列信息', 'faultrecords', 'AssetId', 'int', 'NO', NULL, 'MUL', '', '资产ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'faultrecords', 'FaultTypeId', 'int', 'NO', NULL, 'MUL', '', '故障类型ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'faultrecords', 'LocationId', 'int', 'YES', NULL, 'MUL', '', '位置ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'faultrecords', 'ReporterId', 'int', 'NO', NULL, 'MUL', '', '报告人ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'faultrecords', 'ReportTime', 'datetime', 'NO', NULL, '', '', '报告时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'faultrecords', 'Status', 'int', 'NO', '0', '', '', '状态：0待处理，1处理中，2已解决，3已关闭');
INSERT INTO `db_structure_result` VALUES ('列信息', 'faultrecords', 'Severity', 'int', 'NO', '0', '', '', '严重程度：0一般，1严重，2紧急');
INSERT INTO `db_structure_result` VALUES ('列信息', 'faultrecords', 'AssigneeId', 'int', 'YES', NULL, 'MUL', '', '处理人ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'faultrecords', 'AssignTime', 'datetime', 'YES', NULL, '', '', '分配时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'faultrecords', 'ResponseTime', 'datetime', 'YES', NULL, '', '', '响应时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'faultrecords', 'ResolutionTime', 'datetime', 'YES', NULL, '', '', '解决时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'faultrecords', 'Resolution', 'varchar(500)', 'YES', NULL, '', '', '解决方案');
INSERT INTO `db_structure_result` VALUES ('列信息', 'faultrecords', 'RootCause', 'varchar(500)', 'YES', NULL, '', '', '根本原因');
INSERT INTO `db_structure_result` VALUES ('列信息', 'faultrecords', 'Notes', 'varchar(500)', 'YES', NULL, '', '', '备注');
INSERT INTO `db_structure_result` VALUES ('列信息', 'faultrecords', 'IsReturned', 'tinyint(1)', 'NO', '0', '', '', '是否返厂');
INSERT INTO `db_structure_result` VALUES ('列信息', 'faultrecords', 'CreatedAt', 'datetime', 'NO', NULL, '', '', '创建时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'faultrecords', 'UpdatedAt', 'datetime', 'NO', NULL, '', '', '更新时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'faulttypes', 'Id', 'int', 'NO', NULL, 'PRI', 'auto_increment', '');
INSERT INTO `db_structure_result` VALUES ('列信息', 'faulttypes', 'Name', 'varchar(50)', 'NO', NULL, 'UNI', '', '类型名称');
INSERT INTO `db_structure_result` VALUES ('列信息', 'faulttypes', 'Code', 'varchar(50)', 'NO', NULL, 'UNI', '', '类型编码');
INSERT INTO `db_structure_result` VALUES ('列信息', 'faulttypes', 'Description', 'varchar(200)', 'YES', NULL, '', '', '类型描述');
INSERT INTO `db_structure_result` VALUES ('列信息', 'faulttypes', 'Severity', 'int', 'NO', '0', '', '', '严重程度：0一般，1严重，2紧急');
INSERT INTO `db_structure_result` VALUES ('列信息', 'faulttypes', 'SuggestedResponseTime', 'int', 'YES', NULL, '', '', '建议响应时间（小时）');
INSERT INTO `db_structure_result` VALUES ('列信息', 'faulttypes', 'SuggestedResolutionTime', 'int', 'YES', NULL, '', '', '建议解决时间（小时）');
INSERT INTO `db_structure_result` VALUES ('列信息', 'faulttypes', 'SortOrder', 'int', 'NO', '0', '', '', '排序');
INSERT INTO `db_structure_result` VALUES ('列信息', 'faulttypes', 'IsActive', 'tinyint(1)', 'NO', '1', '', '', '是否激活');
INSERT INTO `db_structure_result` VALUES ('列信息', 'faulttypes', 'CreatedAt', 'datetime', 'NO', NULL, '', '', '创建时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'faulttypes', 'UpdatedAt', 'datetime', 'NO', NULL, '', '', '更新时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'location_users', 'LocationId', 'int', 'NO', NULL, 'PRI', '', '位置ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'location_users', 'UserId', 'int', 'NO', NULL, 'PRI', '', '用户ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'location_users', 'UserType', 'int', 'NO', '0', 'PRI', '', '用户类型：0使用人，1负责人');
INSERT INTO `db_structure_result` VALUES ('列信息', 'location_users', 'IsActive', 'tinyint(1)', 'NO', '1', '', '', '是否激活');
INSERT INTO `db_structure_result` VALUES ('列信息', 'location_users', 'CreatedAt', 'datetime', 'NO', NULL, '', '', '创建时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'location_users', 'UpdatedAt', 'datetime', 'NO', NULL, '', '', '更新时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'locationhistories', 'Id', 'int', 'NO', NULL, 'PRI', 'auto_increment', '');
INSERT INTO `db_structure_result` VALUES ('列信息', 'locationhistories', 'AssetId', 'int', 'NO', NULL, 'MUL', '', '资产ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'locationhistories', 'OldLocationId', 'int', 'YES', NULL, 'MUL', '', '旧位置ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'locationhistories', 'NewLocationId', 'int', 'NO', NULL, 'MUL', '', '新位置ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'locationhistories', 'OperatorId', 'int', 'NO', NULL, 'MUL', '', '操作人ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'locationhistories', 'ChangeType', 'int', 'NO', '0', '', '', '变更类型：0转移，1领用，2归还');
INSERT INTO `db_structure_result` VALUES ('列信息', 'locationhistories', 'ChangeTime', 'datetime', 'NO', NULL, '', '', '变更时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'locationhistories', 'Notes', 'varchar(500)', 'YES', NULL, '', '', '备注');
INSERT INTO `db_structure_result` VALUES ('列信息', 'locationhistories', 'CreatedAt', 'datetime', 'NO', NULL, '', '', '创建时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'locations', 'Id', 'int', 'NO', NULL, 'PRI', 'auto_increment', '');
INSERT INTO `db_structure_result` VALUES ('列信息', 'locations', 'Code', 'varchar(50)', 'NO', NULL, 'UNI', '', '位置编码');
INSERT INTO `db_structure_result` VALUES ('列信息', 'locations', 'Name', 'varchar(100)', 'NO', NULL, '', '', '位置名称');
INSERT INTO `db_structure_result` VALUES ('列信息', 'locations', 'Type', 'int', 'NO', '0', '', '', '位置类型：0工厂，1产线，2工位');
INSERT INTO `db_structure_result` VALUES ('列信息', 'locations', 'ParentId', 'int', 'YES', NULL, 'MUL', '', '父位置ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'locations', 'Path', 'varchar(200)', 'YES', NULL, '', '', '层级路径');
INSERT INTO `db_structure_result` VALUES ('列信息', 'locations', 'Description', 'varchar(200)', 'YES', NULL, '', '', '位置描述');
INSERT INTO `db_structure_result` VALUES ('列信息', 'locations', 'DefaultDepartmentId', 'int', 'YES', NULL, 'MUL', '', '默认部门ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'locations', 'DefaultResponsiblePersonId', 'int', 'YES', NULL, 'MUL', '', '默认负责人ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'locations', 'IsActive', 'tinyint(1)', 'NO', '1', '', '', '是否激活');
INSERT INTO `db_structure_result` VALUES ('列信息', 'locations', 'CreatedAt', 'datetime', 'NO', NULL, '', '', '创建时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'locations', 'UpdatedAt', 'datetime', 'NO', NULL, '', '', '更新时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'locationusers', 'LocationId', 'int', 'NO', NULL, 'PRI', '', '位置ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'locationusers', 'UserId', 'int', 'NO', NULL, 'PRI', '', '用户ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'locationusers', 'UserType', 'int', 'NO', '0', 'PRI', '', '用户类型：0使用人，1负责人');
INSERT INTO `db_structure_result` VALUES ('列信息', 'locationusers', 'IsActive', 'tinyint(1)', 'NO', '1', '', '', '是否激活');
INSERT INTO `db_structure_result` VALUES ('列信息', 'locationusers', 'CreatedAt', 'datetime', 'NO', NULL, '', '', '创建时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'locationusers', 'UpdatedAt', 'datetime', 'NO', NULL, '', '', '更新时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'maintenanceorders', 'Id', 'int', 'NO', NULL, 'PRI', 'auto_increment', '');
INSERT INTO `db_structure_result` VALUES ('列信息', 'maintenanceorders', 'OrderCode', 'varchar(50)', 'NO', NULL, 'UNI', '', '维护单号');
INSERT INTO `db_structure_result` VALUES ('列信息', 'maintenanceorders', 'Title', 'varchar(100)', 'NO', NULL, '', '', '标题');
INSERT INTO `db_structure_result` VALUES ('列信息', 'maintenanceorders', 'Description', 'varchar(500)', 'YES', NULL, '', '', '描述');
INSERT INTO `db_structure_result` VALUES ('列信息', 'maintenanceorders', 'AssetId', 'int', 'NO', NULL, 'MUL', '', '资产ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'maintenanceorders', 'FaultRecordId', 'int', 'YES', NULL, 'MUL', '', '故障记录ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'maintenanceorders', 'MaintenanceType', 'int', 'NO', '0', '', '', '维护类型：0常规维护，1故障维修，2返厂维修跟踪');
INSERT INTO `db_structure_result` VALUES ('列信息', 'maintenanceorders', 'Status', 'int', 'NO', '0', '', '', '状态：0待处理，1处理中，2已完成，3已取消');
INSERT INTO `db_structure_result` VALUES ('列信息', 'maintenanceorders', 'CreatorId', 'int', 'NO', NULL, 'MUL', '', '创建人ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'maintenanceorders', 'CreateTime', 'datetime', 'NO', NULL, '', '', '创建时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'maintenanceorders', 'AssigneeId', 'int', 'YES', NULL, 'MUL', '', '处理人ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'maintenanceorders', 'AssignTime', 'datetime', 'YES', NULL, '', '', '分配时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'maintenanceorders', 'PlanStartTime', 'datetime', 'YES', NULL, '', '', '计划开始时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'maintenanceorders', 'PlanEndTime', 'datetime', 'YES', NULL, '', '', '计划结束时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'maintenanceorders', 'ActualStartTime', 'datetime', 'YES', NULL, '', '', '实际开始时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'maintenanceorders', 'ActualEndTime', 'datetime', 'YES', NULL, '', '', '实际结束时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'maintenanceorders', 'Solution', 'varchar(500)', 'YES', NULL, '', '', '解决方案');
INSERT INTO `db_structure_result` VALUES ('列信息', 'maintenanceorders', 'Notes', 'varchar(500)', 'YES', NULL, '', '', '备注');
INSERT INTO `db_structure_result` VALUES ('列信息', 'maintenanceorders', 'CreatedAt', 'datetime', 'NO', NULL, '', '', '创建时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'maintenanceorders', 'UpdatedAt', 'datetime', 'NO', NULL, '', '', '更新时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'menus', 'Id', 'int', 'NO', NULL, 'PRI', 'auto_increment', '');
INSERT INTO `db_structure_result` VALUES ('列信息', 'menus', 'Name', 'varchar(50)', 'NO', NULL, '', '', '菜单名称');
INSERT INTO `db_structure_result` VALUES ('列信息', 'menus', 'Code', 'varchar(50)', 'NO', NULL, 'UNI', '', '菜单编码');
INSERT INTO `db_structure_result` VALUES ('列信息', 'menus', 'ParentId', 'int', 'YES', NULL, 'MUL', '', '父菜单ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'menus', 'Type', 'int', 'NO', '0', '', '', '菜单类型：0菜单，1按钮');
INSERT INTO `db_structure_result` VALUES ('列信息', 'menus', 'Icon', 'varchar(50)', 'YES', NULL, '', '', '图标');
INSERT INTO `db_structure_result` VALUES ('列信息', 'menus', 'Path', 'varchar(200)', 'YES', NULL, '', '', '路由路径');
INSERT INTO `db_structure_result` VALUES ('列信息', 'menus', 'Component', 'varchar(200)', 'YES', NULL, '', '', '组件路径');
INSERT INTO `db_structure_result` VALUES ('列信息', 'menus', 'Permission', 'varchar(100)', 'YES', NULL, '', '', '权限标识');
INSERT INTO `db_structure_result` VALUES ('列信息', 'menus', 'Description', 'varchar(200)', 'YES', NULL, '', '', '描述');
INSERT INTO `db_structure_result` VALUES ('列信息', 'menus', 'SortOrder', 'int', 'NO', '0', '', '', '排序');
INSERT INTO `db_structure_result` VALUES ('列信息', 'menus', 'IsExternal', 'tinyint(1)', 'NO', '0', '', '', '是否外链');
INSERT INTO `db_structure_result` VALUES ('列信息', 'menus', 'KeepAlive', 'tinyint(1)', 'NO', '0', '', '', '是否缓存');
INSERT INTO `db_structure_result` VALUES ('列信息', 'menus', 'IsVisible', 'tinyint(1)', 'NO', '1', '', '', '是否可见');
INSERT INTO `db_structure_result` VALUES ('列信息', 'menus', 'IsActive', 'tinyint(1)', 'NO', '1', '', '', '是否激活');
INSERT INTO `db_structure_result` VALUES ('列信息', 'menus', 'CreatedAt', 'datetime', 'NO', NULL, '', '', '创建时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'menus', 'UpdatedAt', 'datetime', 'NO', NULL, '', '', '更新时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'pdcaplans', 'Id', 'int', 'NO', NULL, 'PRI', 'auto_increment', '');
INSERT INTO `db_structure_result` VALUES ('列信息', 'pdcaplans', 'Title', 'varchar(100)', 'NO', NULL, '', '', '计划标题');
INSERT INTO `db_structure_result` VALUES ('列信息', 'pdcaplans', 'Type', 'int', 'NO', '0', '', '', '类型：0硬件，1软件，2流程');
INSERT INTO `db_structure_result` VALUES ('列信息', 'pdcaplans', 'PlanStartDate', 'datetime', 'NO', NULL, '', '', '计划开始日期');
INSERT INTO `db_structure_result` VALUES ('列信息', 'pdcaplans', 'PlanEndDate', 'datetime', 'NO', NULL, '', '', '计划结束日期');
INSERT INTO `db_structure_result` VALUES ('列信息', 'pdcaplans', 'ActualEndDate', 'datetime', 'YES', NULL, '', '', '实际结束日期');
INSERT INTO `db_structure_result` VALUES ('列信息', 'pdcaplans', 'Status', 'int', 'NO', '0', '', '', '状态：0计划，1进行中，2已完成，3已取消');
INSERT INTO `db_structure_result` VALUES ('列信息', 'pdcaplans', 'CompletionRate', 'decimal(5,2)', 'NO', '0.00', '', '', '完成率');
INSERT INTO `db_structure_result` VALUES ('列信息', 'pdcaplans', 'ResponsiblePersonId', 'int', 'NO', NULL, 'MUL', '', '负责人ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'pdcaplans', 'Notes', 'varchar(500)', 'YES', NULL, '', '', '备注');
INSERT INTO `db_structure_result` VALUES ('列信息', 'pdcaplans', 'CreatedAt', 'datetime', 'NO', NULL, '', '', '创建时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'pdcaplans', 'UpdatedAt', 'datetime', 'NO', NULL, '', '', '更新时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'periodicrules', 'Id', 'int', 'NO', NULL, 'PRI', 'auto_increment', '');
INSERT INTO `db_structure_result` VALUES ('列信息', 'periodicrules', 'Name', 'varchar(100)', 'NO', NULL, 'UNI', '', '规则名称');
INSERT INTO `db_structure_result` VALUES ('列信息', 'periodicrules', 'Frequency', 'int', 'NO', '0', '', '', '频率：0每天，1每周，2每月，3每季度，4每年');
INSERT INTO `db_structure_result` VALUES ('列信息', 'periodicrules', 'DayOfWeek', 'int', 'YES', NULL, '', '', '每周几（0-6）');
INSERT INTO `db_structure_result` VALUES ('列信息', 'periodicrules', 'DayOfMonth', 'int', 'YES', NULL, '', '', '每月几号（1-31）');
INSERT INTO `db_structure_result` VALUES ('列信息', 'periodicrules', 'IsActive', 'tinyint(1)', 'NO', '1', '', '', '是否激活');
INSERT INTO `db_structure_result` VALUES ('列信息', 'periodicrules', 'CreatedAt', 'datetime', 'NO', NULL, '', '', '创建时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'periodicrules', 'LastGeneratedAt', 'datetime', 'YES', NULL, '', '', '上次生成时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'purchaseitems', 'Id', 'int', 'NO', NULL, 'PRI', 'auto_increment', '');
INSERT INTO `db_structure_result` VALUES ('列信息', 'purchaseitems', 'PurchaseOrderId', 'int', 'NO', NULL, 'MUL', '', '采购订单ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'purchaseitems', 'ItemName', 'varchar(100)', 'NO', NULL, '', '', '项目名称');
INSERT INTO `db_structure_result` VALUES ('列信息', 'purchaseitems', 'ItemCode', 'varchar(50)', 'YES', NULL, '', '', '项目编码');
INSERT INTO `db_structure_result` VALUES ('列信息', 'purchaseitems', 'Specification', 'varchar(200)', 'YES', NULL, '', '', '规格');
INSERT INTO `db_structure_result` VALUES ('列信息', 'purchaseitems', 'AssetTypeId', 'int', 'YES', NULL, 'MUL', '', '资产类型ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'purchaseitems', 'UnitPrice', 'decimal(18,2)', 'NO', '0.00', '', '', '单价');
INSERT INTO `db_structure_result` VALUES ('列信息', 'purchaseitems', 'Quantity', 'int', 'NO', '1', '', '', '数量');
INSERT INTO `db_structure_result` VALUES ('列信息', 'purchaseitems', 'TotalPrice', 'decimal(18,2)', 'NO', '0.00', '', '', '总价');
INSERT INTO `db_structure_result` VALUES ('列信息', 'purchaseitems', 'Notes', 'varchar(500)', 'YES', NULL, '', '', '备注');
INSERT INTO `db_structure_result` VALUES ('列信息', 'purchaseitems', 'CreatedAt', 'datetime', 'NO', NULL, '', '', '创建时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'purchaseitems', 'UpdatedAt', 'datetime', 'NO', NULL, '', '', '更新时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'purchaseorders', 'Id', 'int', 'NO', NULL, 'PRI', 'auto_increment', '');
INSERT INTO `db_structure_result` VALUES ('列信息', 'purchaseorders', 'OrderCode', 'varchar(50)', 'NO', NULL, 'UNI', '', '订单编码');
INSERT INTO `db_structure_result` VALUES ('列信息', 'purchaseorders', 'Title', 'varchar(100)', 'NO', NULL, '', '', '标题');
INSERT INTO `db_structure_result` VALUES ('列信息', 'purchaseorders', 'Description', 'varchar(500)', 'YES', NULL, '', '', '描述');
INSERT INTO `db_structure_result` VALUES ('列信息', 'purchaseorders', 'SupplierId', 'int', 'NO', NULL, 'MUL', '', '供应商ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'purchaseorders', 'Status', 'int', 'NO', '0', '', '', '状态：0草稿，1待审批，2已审批，3已发出，4已收货，5已完成，6已取消');
INSERT INTO `db_structure_result` VALUES ('列信息', 'purchaseorders', 'EstimatedDeliveryDate', 'datetime', 'YES', NULL, '', '', '预计交付日期');
INSERT INTO `db_structure_result` VALUES ('列信息', 'purchaseorders', 'ActualDeliveryDate', 'datetime', 'YES', NULL, '', '', '实际交付日期');
INSERT INTO `db_structure_result` VALUES ('列信息', 'purchaseorders', 'ApplicantId', 'int', 'NO', NULL, 'MUL', '', '申请人ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'purchaseorders', 'ApplicationTime', 'datetime', 'NO', NULL, '', '', '申请时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'purchaseorders', 'ApproverId', 'int', 'YES', NULL, 'MUL', '', '审批人ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'purchaseorders', 'ApprovalTime', 'datetime', 'YES', NULL, '', '', '审批时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'purchaseorders', 'TotalAmount', 'decimal(18,2)', 'NO', '0.00', '', '', '总金额');
INSERT INTO `db_structure_result` VALUES ('列信息', 'purchaseorders', 'Notes', 'varchar(500)', 'YES', NULL, '', '', '备注');
INSERT INTO `db_structure_result` VALUES ('列信息', 'purchaseorders', 'CreatedAt', 'datetime', 'NO', NULL, '', '', '创建时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'purchaseorders', 'UpdatedAt', 'datetime', 'NO', NULL, '', '', '更新时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'refreshtokens', 'Id', 'int', 'NO', NULL, 'PRI', 'auto_increment', '');
INSERT INTO `db_structure_result` VALUES ('列信息', 'refreshtokens', 'UserId', 'int', 'NO', NULL, 'MUL', '', '用户ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'refreshtokens', 'Token', 'varchar(200)', 'NO', NULL, '', '', '令牌');
INSERT INTO `db_structure_result` VALUES ('列信息', 'refreshtokens', 'JwtId', 'varchar(200)', 'NO', NULL, '', '', 'JWT ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'refreshtokens', 'IsUsed', 'tinyint(1)', 'NO', '0', '', '', '是否已使用');
INSERT INTO `db_structure_result` VALUES ('列信息', 'refreshtokens', 'IsRevoked', 'tinyint(1)', 'NO', '0', '', '', '是否已撤销');
INSERT INTO `db_structure_result` VALUES ('列信息', 'refreshtokens', 'AddedDate', 'datetime', 'NO', NULL, '', '', '添加日期');
INSERT INTO `db_structure_result` VALUES ('列信息', 'refreshtokens', 'ExpiryDate', 'datetime', 'NO', NULL, '', '', '过期日期');
INSERT INTO `db_structure_result` VALUES ('列信息', 'returntofactories', 'Id', 'int', 'NO', NULL, 'PRI', 'auto_increment', '');
INSERT INTO `db_structure_result` VALUES ('列信息', 'returntofactories', 'Code', 'varchar(50)', 'NO', NULL, 'UNI', '', '返厂单号');
INSERT INTO `db_structure_result` VALUES ('列信息', 'returntofactories', 'AssetId', 'int', 'NO', NULL, 'MUL', '', '资产ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'returntofactories', 'FaultRecordId', 'int', 'NO', NULL, 'MUL', '', '故障记录ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'returntofactories', 'SupplierId', 'int', 'NO', NULL, 'MUL', '', '供应商ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'returntofactories', 'Status', 'int', 'NO', '0', '', '', '状态：0待送出，1已送出，2维修中，3已返回，4维修失败');
INSERT INTO `db_structure_result` VALUES ('列信息', 'returntofactories', 'SenderId', 'int', 'NO', NULL, 'MUL', '', '送出人ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'returntofactories', 'SendTime', 'datetime', 'YES', NULL, '', '', '送出时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'returntofactories', 'EstimatedReturnTime', 'datetime', 'YES', NULL, '', '', '预计返回时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'returntofactories', 'ActualReturnTime', 'datetime', 'YES', NULL, '', '', '实际返回时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'returntofactories', 'RepairResult', 'varchar(500)', 'YES', NULL, '', '', '维修结果');
INSERT INTO `db_structure_result` VALUES ('列信息', 'returntofactories', 'RepairCost', 'decimal(18,2)', 'YES', NULL, '', '', '维修费用');
INSERT INTO `db_structure_result` VALUES ('列信息', 'returntofactories', 'InWarranty', 'tinyint(1)', 'NO', '1', '', '', '是否在保修期内');
INSERT INTO `db_structure_result` VALUES ('列信息', 'returntofactories', 'Notes', 'varchar(500)', 'YES', NULL, '', '', '备注');
INSERT INTO `db_structure_result` VALUES ('列信息', 'returntofactories', 'CreatedAt', 'datetime', 'NO', NULL, '', '', '创建时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'returntofactories', 'UpdatedAt', 'datetime', 'NO', NULL, '', '', '更新时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'rolemenus', 'RoleId', 'int', 'NO', NULL, 'PRI', '', '角色ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'rolemenus', 'MenuId', 'int', 'NO', NULL, 'PRI', '', '菜单ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'rolemenus', 'CreatedAt', 'datetime', 'NO', NULL, '', '', '创建时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'roles', 'Id', 'int', 'NO', NULL, 'PRI', 'auto_increment', '');
INSERT INTO `db_structure_result` VALUES ('列信息', 'roles', 'Name', 'varchar(50)', 'NO', NULL, 'UNI', '', '角色名称');
INSERT INTO `db_structure_result` VALUES ('列信息', 'roles', 'Code', 'varchar(50)', 'NO', NULL, 'UNI', '', '角色编码');
INSERT INTO `db_structure_result` VALUES ('列信息', 'roles', 'Description', 'varchar(200)', 'YES', NULL, '', '', '角色描述');
INSERT INTO `db_structure_result` VALUES ('列信息', 'roles', 'IsActive', 'tinyint(1)', 'NO', '1', '', '', '是否激活');
INSERT INTO `db_structure_result` VALUES ('列信息', 'roles', 'CreatedAt', 'datetime', 'NO', NULL, '', '', '创建时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'roles', 'UpdatedAt', 'datetime', 'NO', NULL, '', '', '更新时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'suppliers', 'Id', 'int', 'NO', NULL, 'PRI', 'auto_increment', '');
INSERT INTO `db_structure_result` VALUES ('列信息', 'suppliers', 'Name', 'varchar(100)', 'NO', NULL, 'UNI', '', '供应商名称');
INSERT INTO `db_structure_result` VALUES ('列信息', 'suppliers', 'Code', 'varchar(50)', 'NO', NULL, 'UNI', '', '供应商编码');
INSERT INTO `db_structure_result` VALUES ('列信息', 'suppliers', 'ContactPerson', 'varchar(50)', 'YES', NULL, '', '', '联系人');
INSERT INTO `db_structure_result` VALUES ('列信息', 'suppliers', 'ContactPhone', 'varchar(20)', 'YES', NULL, '', '', '联系电话');
INSERT INTO `db_structure_result` VALUES ('列信息', 'suppliers', 'ContactEmail', 'varchar(100)', 'YES', NULL, '', '', '联系邮箱');
INSERT INTO `db_structure_result` VALUES ('列信息', 'suppliers', 'Address', 'varchar(200)', 'YES', NULL, '', '', '地址');
INSERT INTO `db_structure_result` VALUES ('列信息', 'suppliers', 'Type', 'int', 'NO', '0', '', '', '类型：0硬件，1软件，2服务');
INSERT INTO `db_structure_result` VALUES ('列信息', 'suppliers', 'Notes', 'varchar(500)', 'YES', NULL, '', '', '备注');
INSERT INTO `db_structure_result` VALUES ('列信息', 'suppliers', 'IsActive', 'tinyint(1)', 'NO', '1', '', '', '是否激活');
INSERT INTO `db_structure_result` VALUES ('列信息', 'suppliers', 'CreatedAt', 'datetime', 'NO', NULL, '', '', '创建时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'suppliers', 'UpdatedAt', 'datetime', 'NO', NULL, '', '', '更新时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'tasks', 'Id', 'int', 'NO', NULL, 'PRI', 'auto_increment', '');
INSERT INTO `db_structure_result` VALUES ('列信息', 'tasks', 'Title', 'varchar(100)', 'NO', NULL, '', '', '任务标题');
INSERT INTO `db_structure_result` VALUES ('列信息', 'tasks', 'Description', 'varchar(500)', 'YES', NULL, '', '', '任务描述');
INSERT INTO `db_structure_result` VALUES ('列信息', 'tasks', 'TaskType', 'int', 'NO', '0', '', '', '任务类型：0普通任务，1周期任务，2计划任务');
INSERT INTO `db_structure_result` VALUES ('列信息', 'tasks', 'Status', 'int', 'NO', '0', '', '', '状态：0待处理，1处理中，2已完成，3已取消');
INSERT INTO `db_structure_result` VALUES ('列信息', 'tasks', 'AssignedToId', 'int', 'YES', NULL, 'MUL', '', '分配给谁');
INSERT INTO `db_structure_result` VALUES ('列信息', 'tasks', 'CreatedById', 'int', 'NO', NULL, 'MUL', '', '创建人ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'tasks', 'CreatedAt', 'datetime', 'NO', NULL, '', '', '创建时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'tasks', 'DueDate', 'datetime', 'YES', NULL, '', '', '截止日期');
INSERT INTO `db_structure_result` VALUES ('列信息', 'tasks', 'CompletedAt', 'datetime', 'YES', NULL, '', '', '完成时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'tasks', 'RelatedAssetId', 'int', 'YES', NULL, 'MUL', '', '相关资产ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'tasks', 'RelatedLocationId', 'int', 'YES', NULL, 'MUL', '', '相关位置ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'tasks', 'PeriodicRuleId', 'int', 'YES', NULL, 'MUL', '', '周期规则ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'tasks', 'PdcaPlanId', 'int', 'YES', NULL, 'MUL', '', 'PDCA计划ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'tasks', 'Notes', 'varchar(500)', 'YES', NULL, '', '', '备注');
INSERT INTO `db_structure_result` VALUES ('列信息', 'tasks', 'UpdatedAt', 'datetime', 'NO', NULL, '', '', '更新时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'userroles', 'UserId', 'int', 'NO', NULL, 'PRI', '', '用户ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'userroles', 'RoleId', 'int', 'NO', NULL, 'PRI', '', '角色ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'userroles', 'CreatedAt', 'datetime', 'NO', NULL, '', '', '创建时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'users', 'Id', 'int', 'NO', NULL, 'PRI', 'auto_increment', '');
INSERT INTO `db_structure_result` VALUES ('列信息', 'users', 'Username', 'varchar(50)', 'NO', NULL, 'UNI', '', '用户名');
INSERT INTO `db_structure_result` VALUES ('列信息', 'users', 'PasswordHash', 'varchar(200)', 'NO', NULL, '', '', '密码哈希');
INSERT INTO `db_structure_result` VALUES ('列信息', 'users', 'SecurityStamp', 'varchar(50)', 'NO', NULL, '', '', '安全戳');
INSERT INTO `db_structure_result` VALUES ('列信息', 'users', 'Name', 'varchar(50)', 'NO', NULL, '', '', '姓名');
INSERT INTO `db_structure_result` VALUES ('列信息', 'users', 'Email', 'varchar(100)', 'YES', NULL, 'UNI', '', '邮箱');
INSERT INTO `db_structure_result` VALUES ('列信息', 'users', 'Mobile', 'varchar(20)', 'YES', NULL, '', '', '手机号');
INSERT INTO `db_structure_result` VALUES ('列信息', 'users', 'DepartmentId', 'int', 'YES', NULL, 'MUL', '', '部门ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'users', 'Position', 'varchar(50)', 'YES', NULL, '', '', '职位');
INSERT INTO `db_structure_result` VALUES ('列信息', 'users', 'Gender', 'int', 'NO', '0', '', '', '性别：0未知，1男，2女');
INSERT INTO `db_structure_result` VALUES ('列信息', 'users', 'DefaultRoleId', 'int', 'YES', NULL, 'MUL', '', '默认角色ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'users', 'IsActive', 'tinyint(1)', 'NO', '1', '', '', '是否激活');
INSERT INTO `db_structure_result` VALUES ('列信息', 'users', 'CreatedAt', 'datetime', 'NO', NULL, '', '', '创建时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'users', 'UpdatedAt', 'datetime', 'NO', NULL, '', '', '更新时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'users', 'LastLoginAt', 'datetime', 'YES', NULL, '', '', '最后登录时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'users', 'Avatar', 'varchar(200)', 'YES', NULL, '', '', '头像');

-- ----------------------------
-- Table structure for departments
-- ----------------------------
DROP TABLE IF EXISTS `departments`;
CREATE TABLE `departments`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `Name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '部门名称',
  `Code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '部门编码',
  `ParentId` int NULL DEFAULT NULL COMMENT '父部门ID',
  `ManagerId` int NULL DEFAULT NULL COMMENT '部门经理ID',
  `DeputyManagerId` int NULL DEFAULT NULL COMMENT '部门主任ID（二级负责人）',
  `Path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '层级路径',
  `Description` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '部门描述',
  `IsActive` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否激活',
  `CreatedAt` datetime(0) NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime(0) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE INDEX `IX_Departments_Code`(`Code`) USING BTREE,
  UNIQUE INDEX `IX_Departments_Name`(`Name`) USING BTREE,
  INDEX `IX_Departments_ParentId`(`ParentId`) USING BTREE,
  INDEX `IX_Departments_ManagerId`(`ManagerId`) USING BTREE,
  INDEX `IX_Departments_DeputyManagerId`(`DeputyManagerId`) USING BTREE,
  CONSTRAINT `FK_Departments_Departments_ParentId` FOREIGN KEY (`ParentId`) REFERENCES `departments` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_Departments_Personnel_DeputyManagerId` FOREIGN KEY (`DeputyManagerId`) REFERENCES `personnel` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 31 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '部门表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of departments
-- ----------------------------
INSERT INTO `departments` VALUES (1, '总经办', 'D12ZJB', NULL, NULL, NULL, '1', '公司总经理办公室', 1, '2025-03-23 20:45:08', '2025-04-07 23:46:05');
INSERT INTO `departments` VALUES (2, 'IT部门', 'D12ITB', NULL, NULL, NULL, '2', '信息技术部门', 1, '2025-03-23 20:45:08', '2025-04-07 23:45:47');
INSERT INTO `departments` VALUES (3, '底十二人力资源部', 'D12RZB', NULL, NULL, NULL, '3', '人力资源管理部门', 1, '2025-03-23 20:45:08', '2025-04-07 23:45:25');
INSERT INTO `departments` VALUES (4, '底十二质量部', 'D12ZLB', NULL, NULL, NULL, '4', '财务管理部门', 1, '2025-03-23 20:45:08', '2025-04-07 23:45:11');
INSERT INTO `departments` VALUES (5, '底十二动力部', 'D12DLB', NULL, NULL, NULL, '5', '生产制造部门', 1, '2025-03-23 20:45:08', '2025-04-07 23:44:23');
INSERT INTO `departments` VALUES (6, '底十二制造三部', 'D12ZZ3B', NULL, NULL, NULL, '6', '工程技术部门', 1, '2025-03-23 20:45:08', '2025-04-07 23:43:42');
INSERT INTO `departments` VALUES (7, '底十二制造二部', 'D12ZZ2B', NULL, NULL, NULL, '7', '质量管理部门', 1, '2025-03-23 20:45:08', '2025-04-07 23:44:29');
INSERT INTO `departments` VALUES (10, 'IT基础设施', 'IT_INFRASTRUCTURE', 2, NULL, NULL, '2-1', 'IT基础设施管理', 1, '2025-03-23 20:45:08', '2025-04-07 23:54:23');
INSERT INTO `departments` VALUES (11, 'IT应用开发', 'IT_APPLICATION', 2, NULL, NULL, '2-2', 'IT应用系统开发', 1, '2025-03-23 20:45:08', '2025-04-07 23:54:26');
INSERT INTO `departments` VALUES (12, 'IT运维', 'IT_OPERATION', 2, NULL, NULL, '2-3', 'IT系统运维管理', 1, '2025-03-23 20:45:08', '2025-04-07 23:54:31');
INSERT INTO `departments` VALUES (13, 'IT安全', 'IT_SECURITY', 2, NULL, NULL, '2-4', 'IT安全管理', 1, '2025-03-23 20:45:08', '2025-04-07 23:54:34');
INSERT INTO `departments` VALUES (14, '动力一部', 'D12DL1B', 5, NULL, NULL, '5-1', '生产制造一部', 1, '2025-03-23 20:45:08', '2025-04-07 23:41:51');
INSERT INTO `departments` VALUES (15, '动力二部', 'D12DL2B', 5, NULL, NULL, '5-2', '生产制造二部', 1, '2025-03-23 20:45:08', '2025-04-07 23:54:43');
INSERT INTO `departments` VALUES (27, '底十二制造一部', 'D12ZZYB', NULL, NULL, NULL, NULL, '南车间制造部', 1, '2025-04-06 01:40:59', '2025-04-07 23:54:46');
INSERT INTO `departments` VALUES (30, '陈正华', 'chenzhenghua', NULL, NULL, NULL, NULL, '', 1, '2025-04-10 19:22:44', '2025-04-10 19:22:44');

-- ----------------------------
-- Table structure for faultrecords
-- ----------------------------
DROP TABLE IF EXISTS `faultrecords`;
CREATE TABLE `faultrecords`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `Title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '标题',
  `Description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '描述',
  `AssetId` int NOT NULL COMMENT '资产ID',
  `FaultTypeId` int NOT NULL COMMENT '故障类型ID',
  `LocationId` int NULL DEFAULT NULL COMMENT '位置ID',
  `ReporterId` int NOT NULL COMMENT '报告人ID',
  `ReportTime` datetime(0) NOT NULL COMMENT '报告时间',
  `Status` int NOT NULL DEFAULT 0 COMMENT '状态：0待处理，1处理中，2已解决，3已关闭',
  `Severity` int NOT NULL DEFAULT 0 COMMENT '严重程度：0一般，1严重，2紧急',
  `AssigneeId` int NULL DEFAULT NULL COMMENT '处理人ID',
  `AssignTime` datetime(0) NULL DEFAULT NULL COMMENT '分配时间',
  `ResponseTime` datetime(0) NULL DEFAULT NULL COMMENT '响应时间',
  `ResolutionTime` datetime(0) NULL DEFAULT NULL COMMENT '解决时间',
  `Resolution` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '解决方案',
  `RootCause` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '根本原因',
  `Notes` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `IsReturned` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否返厂',
  `CreatedAt` datetime(0) NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime(0) NOT NULL COMMENT '更新时间',
  `faultNumber` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '故障单号(FIX-年月日-序号)',
  `expected_return_date` datetime(0) NULL DEFAULT NULL COMMENT '预期返厂日期',
  `warning_flag` tinyint(1) NULL DEFAULT 0 COMMENT '超期预警标记',
  PRIMARY KEY (`Id`) USING BTREE,
  INDEX `IX_FaultRecords_AssetId`(`AssetId`) USING BTREE,
  INDEX `IX_FaultRecords_FaultTypeId`(`FaultTypeId`) USING BTREE,
  INDEX `IX_FaultRecords_LocationId`(`LocationId`) USING BTREE,
  INDEX `IX_FaultRecords_ReporterId`(`ReporterId`) USING BTREE,
  INDEX `IX_FaultRecords_AssigneeId`(`AssigneeId`) USING BTREE,
  INDEX `idx_faultrecords_faultNumber`(`faultNumber`) USING BTREE,
  CONSTRAINT `FK_FaultRecords_Assets_AssetId` FOREIGN KEY (`AssetId`) REFERENCES `assets` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_FaultRecords_FaultTypes_FaultTypeId` FOREIGN KEY (`FaultTypeId`) REFERENCES `faulttypes` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_FaultRecords_Locations_LocationId` FOREIGN KEY (`LocationId`) REFERENCES `locations` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_FaultRecords_Users_AssigneeId` FOREIGN KEY (`AssigneeId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_FaultRecords_Users_ReporterId` FOREIGN KEY (`ReporterId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '故障记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of faultrecords
-- ----------------------------

-- ----------------------------
-- Table structure for faulttypes
-- ----------------------------
DROP TABLE IF EXISTS `faulttypes`;
CREATE TABLE `faulttypes`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `Name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '类型名称',
  `Code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '类型编码',
  `Description` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '类型描述',
  `Severity` int NOT NULL DEFAULT 0 COMMENT '严重程度：0一般，1严重，2紧急',
  `SuggestedResponseTime` int NULL DEFAULT NULL COMMENT '建议响应时间（小时）',
  `SuggestedResolutionTime` int NULL DEFAULT NULL COMMENT '建议解决时间（小时）',
  `SortOrder` int NOT NULL DEFAULT 0 COMMENT '排序',
  `IsActive` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否激活',
  `CreatedAt` datetime(0) NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime(0) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE INDEX `IX_FaultTypes_Code`(`Code`) USING BTREE,
  UNIQUE INDEX `IX_FaultTypes_Name`(`Name`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 16 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '故障类型表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of faulttypes
-- ----------------------------
INSERT INTO `faulttypes` VALUES (1, '硬件损坏', 'HW_DAMAGE', '设备硬件部件物理损坏', 1, 4, 48, 10, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `faulttypes` VALUES (2, '系统崩溃', 'SYS_CRASH', '操作系统或应用程序崩溃', 2, 2, 24, 20, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `faulttypes` VALUES (3, '网络连接中断', 'NET_DISCONNECT', '网络连接无法建立或中断', 1, 2, 12, 30, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `faulttypes` VALUES (4, '设备无法启动', 'BOOT_FAILURE', '设备无法正常启动或开机', 2, 2, 24, 40, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `faulttypes` VALUES (5, '性能下降', 'PERF_DEGRADE', '设备性能显著降低', 0, 8, 48, 50, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `faulttypes` VALUES (6, '设备过热', 'OVERHEAT', '设备温度异常升高', 1, 2, 12, 60, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `faulttypes` VALUES (7, '电源故障', 'POWER_FAILURE', '电源供应故障或不稳定', 2, 2, 24, 70, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `faulttypes` VALUES (8, '显示异常', 'DISPLAY_ERROR', '显示器或屏幕显示异常', 0, 4, 24, 80, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `faulttypes` VALUES (9, '噪音异常', 'NOISE_ABNORMAL', '设备运行噪音异常', 0, 8, 48, 90, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `faulttypes` VALUES (10, '软件异常', 'SW_ERROR', '软件运行异常或错误', 1, 4, 24, 100, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `faulttypes` VALUES (11, '端口故障', 'PORT_FAILURE', '通信端口或接口故障', 1, 4, 24, 110, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `faulttypes` VALUES (12, '打印质量问题', 'PRINT_QUALITY', '打印设备输出质量问题', 0, 8, 48, 120, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `faulttypes` VALUES (13, '扫描故障', 'SCAN_FAILURE', '扫描设备无法正常工作', 1, 4, 24, 130, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `faulttypes` VALUES (14, '电池故障', 'BATTERY_ISSUE', '电池无法充电或续航时间短', 0, 8, 48, 140, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `faulttypes` VALUES (15, '存储故障', 'STORAGE_FAILURE', '存储设备读写错误或无法识别', 1, 4, 24, 150, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');

-- ----------------------------
-- Table structure for gamification_badges
-- ----------------------------
DROP TABLE IF EXISTS `gamification_badges`;
CREATE TABLE `gamification_badges`  (
  `BadgeId` bigint NOT NULL AUTO_INCREMENT COMMENT '徽章ID (BIGINT)',
  `Code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '徽章唯一代码',
  `Name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '徽章名称',
  `Description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '徽章描述及获得条件',
  `IconUrl` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '徽章图标URL',
  `TriggerEvent` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '触发事件类型',
  `TriggerThreshold` int NULL DEFAULT NULL COMMENT '触发阈值',
  `RequiredLevel` int NULL DEFAULT NULL COMMENT '获得所需最低等级',
  `PointsAwarded` int NOT NULL DEFAULT 0 COMMENT '获得徽章时奖励的积分',
  `XPAwarded` int NOT NULL DEFAULT 0 COMMENT '获得徽章时奖励的XP',
  `IsRepeatable` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否可重复获得',
  `IsActive` tinyint(1) NOT NULL DEFAULT 1 COMMENT '徽章是否启用',
  `CreationTimestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`BadgeId`) USING BTREE,
  UNIQUE INDEX `idx_gamification_badges_code`(`Code`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '游戏化徽章定义表 (BIGINT PK)' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of gamification_badges
-- ----------------------------

-- ----------------------------
-- Table structure for gamification_items
-- ----------------------------
DROP TABLE IF EXISTS `gamification_items`;
CREATE TABLE `gamification_items`  (
  `ItemId` bigint NOT NULL AUTO_INCREMENT COMMENT '物品ID (BIGINT)',
  `Code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '物品唯一代码',
  `Name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '物品名称',
  `Description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '物品描述',
  `IconUrl` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '物品图标URL',
  `Type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '物品类型',
  `Effect` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '物品效果描述',
  `PointsCost` int NOT NULL DEFAULT 0 COMMENT '购买所需积分',
  `Cooldown` int NULL DEFAULT NULL COMMENT '使用冷却时间(分钟)',
  `IsActive` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否激活',
  `CreationTimestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`ItemId`) USING BTREE,
  UNIQUE INDEX `idx_gamification_items_code`(`Code`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '游戏化物品定义表 (BIGINT PK)' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of gamification_items
-- ----------------------------

-- ----------------------------
-- Table structure for gamification_userbadges
-- ----------------------------
DROP TABLE IF EXISTS `gamification_userbadges`;
CREATE TABLE `gamification_userbadges`  (
  `UserBadgeId` bigint NOT NULL AUTO_INCREMENT COMMENT '用户徽章记录ID (BIGINT)',
  `UserId` int NOT NULL COMMENT '用户ID (关联 users.Id - INT)',
  `BadgeId` bigint NOT NULL COMMENT '徽章ID',
  `EarnedTimestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
  `Count` int NOT NULL DEFAULT 1 COMMENT '获得次数',
  PRIMARY KEY (`UserBadgeId`) USING BTREE,
  INDEX `idx_userbadges_user_badge`(`UserId`, `BadgeId`) USING BTREE,
  INDEX `idx_userbadges_badge`(`BadgeId`) USING BTREE,
  CONSTRAINT `FK_UserBadges_Badge` FOREIGN KEY (`BadgeId`) REFERENCES `gamification_badges` (`BadgeId`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `FK_UserBadges_User` FOREIGN KEY (`UserId`) REFERENCES `users` (`Id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户获得的徽章记录表 (BIGINT PK)' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of gamification_userbadges
-- ----------------------------

-- ----------------------------
-- Table structure for gamification_useritems
-- ----------------------------
DROP TABLE IF EXISTS `gamification_useritems`;
CREATE TABLE `gamification_useritems`  (
  `UserItemId` bigint NOT NULL AUTO_INCREMENT COMMENT '用户物品ID (BIGINT)',
  `UserId` int NOT NULL COMMENT '用户ID (关联 users.Id - INT)',
  `ItemId` bigint NOT NULL COMMENT '物品ID',
  `Quantity` int NOT NULL DEFAULT 1 COMMENT '物品数量',
  `AcquiredTimestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '获得时间',
  `LastUsedTimestamp` datetime(0) NULL DEFAULT NULL COMMENT '上次使用时间',
  `ExpiryTimestamp` datetime(0) NULL DEFAULT NULL COMMENT '过期时间(如果有)',
  PRIMARY KEY (`UserItemId`) USING BTREE,
  UNIQUE INDEX `idx_useritems_user_item`(`UserId`, `ItemId`) USING BTREE,
  INDEX `idx_useritems_item`(`ItemId`) USING BTREE,
  CONSTRAINT `FK_UserItems_Item` FOREIGN KEY (`ItemId`) REFERENCES `gamification_items` (`ItemId`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `FK_UserItems_User` FOREIGN KEY (`UserId`) REFERENCES `users` (`Id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户拥有的物品表 (BIGINT PK)' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of gamification_useritems
-- ----------------------------

-- ----------------------------
-- Table structure for gamification_userstats
-- ----------------------------
DROP TABLE IF EXISTS `gamification_userstats`;
CREATE TABLE `gamification_userstats`  (
  `UserId` bigint NOT NULL COMMENT '用户统计关联ID (BIGINT PK, 逻辑关联 users.Id)',
  `CoreUserId` int NOT NULL COMMENT '对应的核心用户ID (关联 users.Id - INT, 用于查询)',
  `CurrentXP` int NOT NULL DEFAULT 0 COMMENT '当前经验值',
  `CurrentLevel` int NOT NULL DEFAULT 1 COMMENT '当前等级',
  `PointsBalance` int NOT NULL DEFAULT 0 COMMENT '当前可用积分',
  `CompletedTasksCount` int NOT NULL DEFAULT 0 COMMENT '累计完成任务数',
  `OnTimeTasksCount` int NOT NULL DEFAULT 0 COMMENT '累计按时完成任务数',
  `StreakCount` int NOT NULL DEFAULT 0 COMMENT '当前连续活动/完成任务天数',
  `LastActivityTimestamp` datetime(0) NULL DEFAULT NULL COMMENT '最后活跃时间戳',
  `LastStreakTimestamp` date NULL DEFAULT NULL COMMENT '上次增加连续记录的日期',
  `LastUpdatedTimestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`UserId`) USING BTREE,
  UNIQUE INDEX `idx_gamification_userstats_coreuserid`(`CoreUserId`) USING BTREE,
  CONSTRAINT `FK_UserStats_CoreUser` FOREIGN KEY (`CoreUserId`) REFERENCES `users` (`Id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '游戏化用户统计表 (BIGINT PK)' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of gamification_userstats
-- ----------------------------

-- ----------------------------
-- Table structure for inventory_thresholds
-- ----------------------------
DROP TABLE IF EXISTS `inventory_thresholds`;
CREATE TABLE `inventory_thresholds`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `asset_type_id` int NOT NULL,
  `min_threshold` int NOT NULL DEFAULT 5 COMMENT '最小安全库存',
  `warning_threshold` int NOT NULL DEFAULT 10 COMMENT '预警库存',
  `updated_at` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_inventory_asset_type`(`asset_type_id`) USING BTREE,
  CONSTRAINT `fk_inventory_asset_type` FOREIGN KEY (`asset_type_id`) REFERENCES `assettypes` (`Id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of inventory_thresholds
-- ----------------------------

-- ----------------------------
-- Table structure for locationhistories
-- ----------------------------
DROP TABLE IF EXISTS `locationhistories`;
CREATE TABLE `locationhistories`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `AssetId` int NOT NULL COMMENT '资产ID',
  `OldLocationId` int NULL DEFAULT NULL COMMENT '旧位置ID',
  `NewLocationId` int NOT NULL COMMENT '新位置ID',
  `OperatorId` int NOT NULL COMMENT '操作人ID',
  `ChangeType` int NOT NULL DEFAULT 0 COMMENT '变更类型：0转移，1领用，2归还',
  `ChangeTime` datetime(0) NOT NULL COMMENT '变更时间',
  `Notes` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `CreatedAt` datetime(0) NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`Id`) USING BTREE,
  INDEX `IX_LocationHistories_AssetId`(`AssetId`) USING BTREE,
  INDEX `IX_LocationHistories_OldLocationId`(`OldLocationId`) USING BTREE,
  INDEX `IX_LocationHistories_NewLocationId`(`NewLocationId`) USING BTREE,
  INDEX `IX_LocationHistories_OperatorId`(`OperatorId`) USING BTREE,
  CONSTRAINT `FK_LocationHistories_Assets_AssetId` FOREIGN KEY (`AssetId`) REFERENCES `assets` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_LocationHistories_Locations_NewLocationId` FOREIGN KEY (`NewLocationId`) REFERENCES `locations` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_LocationHistories_Locations_OldLocationId` FOREIGN KEY (`OldLocationId`) REFERENCES `locations` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_LocationHistories_Users_OperatorId` FOREIGN KEY (`OperatorId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 13 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '位置历史表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of locationhistories
-- ----------------------------
INSERT INTO `locationhistories` VALUES (11, 818, 149, 157, 8, 0, '2025-04-03 19:25:04', '位置变更: 毛坯检验01 → 仓库区域GP12', '2025-04-03 19:25:04');
INSERT INTO `locationhistories` VALUES (12, 818, 157, 156, 8, 0, '2025-04-03 19:26:00', '位置变更: 仓库区域GP12 → 机加区域GP12', '2025-04-03 19:26:00');

-- ----------------------------
-- Table structure for locations
-- ----------------------------
DROP TABLE IF EXISTS `locations`;
CREATE TABLE `locations`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `Code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '位置编码',
  `Name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '位置名称',
  `Type` int NOT NULL DEFAULT 0 COMMENT '位置类型：0厂区，1产线，2工序，3工位，4设备位置',
  `ParentId` int NULL DEFAULT NULL COMMENT '父位置ID',
  `Path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '层级路径',
  `Description` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '位置描述',
  `DefaultDepartmentId` int NULL DEFAULT NULL COMMENT '默认部门ID',
  `DefaultResponsiblePersonId` int NULL DEFAULT NULL COMMENT '默认负责人ID',
  `IsActive` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否激活',
  `CreatedAt` datetime(0) NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime(0) NOT NULL COMMENT '更新时间',
  `level` tinyint NOT NULL DEFAULT 3 COMMENT '位置级别(1-5)',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE INDEX `IX_Locations_Code`(`Code`) USING BTREE,
  INDEX `IX_Locations_ParentId`(`ParentId`) USING BTREE,
  INDEX `IX_Locations_DefaultDepartmentId`(`DefaultDepartmentId`) USING BTREE,
  INDEX `IX_Locations_DefaultResponsiblePersonId`(`DefaultResponsiblePersonId`) USING BTREE,
  INDEX `idx_locations_level`(`level`) USING BTREE,
  CONSTRAINT `FK_Locations_Departments_DefaultDepartmentId` FOREIGN KEY (`DefaultDepartmentId`) REFERENCES `departments` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_Locations_Locations_ParentId` FOREIGN KEY (`ParentId`) REFERENCES `locations` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_Locations_Users_DefaultResponsiblePersonId` FOREIGN KEY (`DefaultResponsiblePersonId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 292 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '位置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of locations
-- ----------------------------
INSERT INTO `locations` VALUES (1, 'D12', 'D12', 0, NULL, '1', '', NULL, NULL, 1, '2025-03-23 20:45:09', '2025-04-23 14:57:53', 3);
INSERT INTO `locations` VALUES (36, 'CX8512ZX', '制芯产线', 1, 1, '1,1', '', 27, NULL, 1, '2025-04-01 23:39:58', '2025-04-08 00:00:23', 3);
INSERT INTO `locations` VALUES (38, 'CX8512RZ', '熔铸产线', 1, 1, '1,1', '', 27, NULL, 1, '2025-04-01 23:52:42', '2025-04-08 00:03:10', 3);
INSERT INTO `locations` VALUES (39, 'GX8512RL', '熔炼', 2, 38, '1,1,38', '', 27, NULL, 1, '2025-04-01 23:53:56', '2025-04-08 00:03:23', 3);
INSERT INTO `locations` VALUES (40, 'GX8512DYZZ', '低压铸造', 2, 38, '1,1,38', '', 27, NULL, 1, '2025-04-01 23:55:03', '2025-04-08 00:03:31', 3);
INSERT INTO `locations` VALUES (41, 'GX8512ZX', '制芯', 2, 36, '1,1,36', '', NULL, NULL, 1, '2025-04-01 23:55:33', '2025-04-01 23:55:33', 3);
INSERT INTO `locations` VALUES (42, 'ZX_A', '制芯A线', 3, 41, '1,1,36,41', '', NULL, NULL, 1, '2025-04-01 23:56:21', '2025-04-01 23:56:21', 3);
INSERT INTO `locations` VALUES (43, 'ZX_A_1', '制芯A1', 4, 42, '1,1,36,41,42', '', 27, 2, 1, '2025-04-01 23:56:50', '2025-04-07 00:48:24', 3);
INSERT INTO `locations` VALUES (44, 'ZX_A_2', '制芯A2', 4, 42, '1,1,36,41,42', '', 27, NULL, 1, '2025-04-01 23:57:09', '2025-04-07 13:05:51', 3);
INSERT INTO `locations` VALUES (45, 'ZX_A_3', '制芯A3', 4, 42, '1,1,36,41,42', '', 27, 2, 1, '2025-04-01 23:57:18', '2025-04-07 00:48:57', 3);
INSERT INTO `locations` VALUES (46, 'ZX_B', '制芯B线', 3, 41, '1,1,36,41', '', NULL, NULL, 1, '2025-04-01 23:57:40', '2025-04-01 23:57:40', 3);
INSERT INTO `locations` VALUES (47, 'ZX_C', '制芯C线', 3, 41, '1,1,36,41', '', NULL, NULL, 1, '2025-04-01 23:58:01', '2025-04-01 23:58:01', 3);
INSERT INTO `locations` VALUES (48, 'ZX_B_1', '制芯B1', 4, 46, '1,1,36,41,46', '', NULL, NULL, 1, '2025-04-01 23:58:22', '2025-04-01 23:58:22', 3);
INSERT INTO `locations` VALUES (49, 'ZX_C_1', '制芯C1', 4, 47, '1,1,36,41,47', '', NULL, NULL, 1, '2025-04-01 23:58:33', '2025-04-01 23:58:33', 3);
INSERT INTO `locations` VALUES (50, 'ZX_B_2', '制芯B2', 4, 46, '1,1,36,41,46', '', NULL, NULL, 1, '2025-04-01 23:59:33', '2025-04-01 23:59:33', 3);
INSERT INTO `locations` VALUES (51, 'ZX_B_3', '制芯B3', 4, 46, '1,1,36,41,46', '', NULL, NULL, 1, '2025-04-01 23:59:44', '2025-04-01 23:59:44', 3);
INSERT INTO `locations` VALUES (52, 'ZX_C_2', '制芯C2', 4, 47, '1,1,36,41,47', '', NULL, NULL, 1, '2025-04-01 23:59:53', '2025-04-01 23:59:53', 3);
INSERT INTO `locations` VALUES (53, 'ZX_C_3', '制芯C3', 4, 47, '1,1,36,41,47', '', NULL, NULL, 1, '2025-04-01 23:59:57', '2025-04-01 23:59:57', 3);
INSERT INTO `locations` VALUES (54, 'RL_A', '熔炼A线', 3, 39, '1,1,38,39', '', NULL, NULL, 1, '2025-04-02 00:00:49', '2025-04-02 00:00:49', 3);
INSERT INTO `locations` VALUES (55, 'RL_B', '熔炼B线', 3, 39, '1,1,38,39', '', NULL, NULL, 1, '2025-04-02 00:01:40', '2025-04-02 00:01:40', 3);
INSERT INTO `locations` VALUES (56, 'RL_C', '熔炼C线', 3, 39, '1,1,38,39', '', NULL, NULL, 1, '2025-04-02 00:01:57', '2025-04-02 00:01:57', 3);
INSERT INTO `locations` VALUES (57, 'ZZ_A', '铸造A线', 3, 40, '1,1,38,40', '', NULL, NULL, 1, '2025-04-02 00:02:13', '2025-04-02 00:02:13', 3);
INSERT INTO `locations` VALUES (58, 'ZZ_B', '铸造B线', 3, 40, '1,1,38,40', '', NULL, NULL, 1, '2025-04-02 00:02:23', '2025-04-02 00:02:23', 3);
INSERT INTO `locations` VALUES (59, 'ZZ_C', '铸造C线', 3, 40, '1,1,38,40', '', NULL, NULL, 1, '2025-04-02 00:03:24', '2025-04-02 00:03:24', 3);
INSERT INTO `locations` VALUES (60, 'ZZ_A_1', '铸造A1', 4, 57, '1,1,38,40,57', '', NULL, NULL, 1, '2025-04-02 00:03:43', '2025-04-02 00:03:43', 3);
INSERT INTO `locations` VALUES (61, 'ZZ_A_2', '铸造A2', 4, 57, '1,1,38,40,57', '', NULL, NULL, 1, '2025-04-02 00:03:49', '2025-04-02 00:03:49', 3);
INSERT INTO `locations` VALUES (62, 'ZZ_A_3', '铸造A3', 4, 57, '1,1,38,40,57', '', NULL, NULL, 1, '2025-04-02 00:04:14', '2025-04-02 00:04:14', 3);
INSERT INTO `locations` VALUES (63, 'ZZ_A_4', '铸造A4', 4, 57, '1,1,38,40,57', '', NULL, NULL, 1, '2025-04-02 00:04:25', '2025-04-02 00:04:25', 3);
INSERT INTO `locations` VALUES (64, 'ZZ_A_5', '铸造A5', 4, 57, '1,1,38,40,57', '', NULL, NULL, 1, '2025-04-02 00:04:32', '2025-04-02 00:04:32', 3);
INSERT INTO `locations` VALUES (65, 'ZZ_A_6', '铸造A6', 4, 57, '1,1,38,40,57', '', NULL, NULL, 1, '2025-04-02 00:04:47', '2025-04-02 00:04:47', 3);
INSERT INTO `locations` VALUES (66, 'ZZ_A_B', '铸造A补码', 4, 57, '1,1,38,40,57', '', NULL, NULL, 1, '2025-04-02 00:05:10', '2025-04-02 00:05:10', 3);
INSERT INTO `locations` VALUES (67, 'ZZ_B_1', '铸造B1', 4, 58, '1,1,38,40,58', '', NULL, NULL, 1, '2025-04-02 00:05:29', '2025-04-02 00:05:29', 3);
INSERT INTO `locations` VALUES (68, 'ZZ_B_2', '铸造B2', 4, 58, '1,1,38,40,58', '', NULL, NULL, 1, '2025-04-02 00:05:38', '2025-04-02 00:05:38', 3);
INSERT INTO `locations` VALUES (69, 'ZZ_B_3', '铸造B3', 4, 58, '1,1,38,40,58', '', NULL, NULL, 1, '2025-04-02 00:06:19', '2025-04-02 00:06:19', 3);
INSERT INTO `locations` VALUES (70, 'ZZ_B_4', '铸造B4', 4, 58, '1,1,38,40,58', '', NULL, NULL, 1, '2025-04-02 00:06:26', '2025-04-02 00:06:26', 3);
INSERT INTO `locations` VALUES (71, 'ZZ_B_5', '铸造B5', 4, 58, '1,1,38,40,58', '', NULL, NULL, 1, '2025-04-02 00:06:32', '2025-04-02 00:06:32', 3);
INSERT INTO `locations` VALUES (72, 'ZZ_B_6', '铸造B6', 4, 58, '1,1,38,40,58', '', NULL, NULL, 1, '2025-04-02 00:06:36', '2025-04-02 00:06:36', 3);
INSERT INTO `locations` VALUES (73, 'ZZ_B_B', '铸造B补码', 4, 58, '1,1,38,40,58', '', NULL, NULL, 1, '2025-04-02 00:06:59', '2025-04-02 00:06:59', 3);
INSERT INTO `locations` VALUES (74, 'ZZ_C_1', '铸造C1', 4, 59, '1,1,38,40,59', '', NULL, NULL, 1, '2025-04-02 00:07:10', '2025-04-02 00:07:10', 3);
INSERT INTO `locations` VALUES (75, 'ZZ_C_2', '铸造C2', 4, 59, '1,1,38,40,59', '', NULL, NULL, 1, '2025-04-02 00:07:16', '2025-04-02 00:07:16', 3);
INSERT INTO `locations` VALUES (76, 'ZZ_C_3', '铸造C3', 4, 59, '1,1,38,40,59', '', NULL, NULL, 1, '2025-04-02 00:07:22', '2025-04-02 00:07:22', 3);
INSERT INTO `locations` VALUES (77, 'ZZ_C_4', '铸造C4', 4, 59, '1,1,38,40,59', '', NULL, NULL, 1, '2025-04-02 00:07:31', '2025-04-02 00:07:31', 3);
INSERT INTO `locations` VALUES (78, 'ZZ_C_5', '铸造C5', 4, 59, '1,1,38,40,59', '', NULL, NULL, 1, '2025-04-02 00:07:35', '2025-04-02 00:07:35', 3);
INSERT INTO `locations` VALUES (79, 'ZZ_C_6', '铸造C6', 4, 59, '1,1,38,40,59', '', NULL, NULL, 1, '2025-04-02 00:07:40', '2025-04-02 00:07:40', 3);
INSERT INTO `locations` VALUES (80, 'ZZ_C_B', '铸造C补码', 4, 59, '1,1,38,40,59', '', NULL, NULL, 1, '2025-04-02 00:07:55', '2025-04-02 00:07:55', 3);
INSERT INTO `locations` VALUES (81, 'CX8512HCL', '后处理产线', 1, 1, '1,1', '', 7, NULL, 1, '2025-04-02 00:08:33', '2025-04-08 00:03:51', 3);
INSERT INTO `locations` VALUES (82, 'GX8512XG', 'X-RAY检测', 2, 81, '1,1,81', '', NULL, NULL, 1, '2025-04-02 00:09:29', '2025-04-02 00:09:29', 3);
INSERT INTO `locations` VALUES (83, 'GX8512DM', '打磨', 2, 81, '1,1,81', '', NULL, NULL, 1, '2025-04-02 00:10:16', '2025-04-02 00:10:16', 3);
INSERT INTO `locations` VALUES (84, 'XG_A', 'X光机A线', 3, 82, '1,1,81,82', '', NULL, NULL, 1, '2025-04-02 01:00:22', '2025-04-02 01:00:22', 3);
INSERT INTO `locations` VALUES (85, 'XG_B', 'X光机B线', 3, 82, '1,1,81,82', '', NULL, NULL, 1, '2025-04-02 01:00:45', '2025-04-02 01:00:45', 3);
INSERT INTO `locations` VALUES (86, 'XG_C', 'X光机C线', 3, 82, '1,1,81,82', '', NULL, NULL, 1, '2025-04-02 01:01:09', '2025-04-02 01:01:09', 3);
INSERT INTO `locations` VALUES (87, 'XG_A_1', 'X光机A1', 4, 84, '1,1,81,82,84', '', NULL, NULL, 1, '2025-04-02 01:01:27', '2025-04-02 01:01:27', 3);
INSERT INTO `locations` VALUES (88, 'XG_A_2', 'X光机A2', 4, 84, '1,1,81,82,84', '', NULL, NULL, 1, '2025-04-02 01:01:32', '2025-04-02 01:01:32', 3);
INSERT INTO `locations` VALUES (89, 'XG_A_3', 'X光机A3', 4, 84, '1,1,81,82,84', '', NULL, NULL, 1, '2025-04-02 01:01:36', '2025-04-02 01:01:36', 3);
INSERT INTO `locations` VALUES (90, 'XG_B_1', 'X光机B1', 4, 85, '1,1,81,82,85', '', NULL, NULL, 1, '2025-04-02 01:01:42', '2025-04-02 01:01:42', 3);
INSERT INTO `locations` VALUES (91, 'XG_B_2', 'X光机B2', 4, 85, '1,1,81,82,85', '', NULL, NULL, 1, '2025-04-02 01:01:48', '2025-04-02 01:01:48', 3);
INSERT INTO `locations` VALUES (92, 'XG_B_3', 'X光机B3', 4, 85, '1,1,81,82,85', '', NULL, NULL, 1, '2025-04-02 01:01:53', '2025-04-02 01:01:53', 3);
INSERT INTO `locations` VALUES (93, 'XG_C_1', 'X光机C1', 4, 86, '1,1,81,82,86', '', NULL, NULL, 1, '2025-04-02 01:02:01', '2025-04-02 01:02:01', 3);
INSERT INTO `locations` VALUES (94, 'XG_C_2', 'X光机C2', 4, 86, '1,1,81,82,86', '', NULL, NULL, 1, '2025-04-02 01:02:06', '2025-04-02 01:02:06', 3);
INSERT INTO `locations` VALUES (95, 'XG_C_3', 'X光机C3', 4, 86, '1,1,81,82,86', '', NULL, NULL, 1, '2025-04-02 01:02:11', '2025-04-02 01:02:11', 3);
INSERT INTO `locations` VALUES (96, 'HCL_B', 'B线处绑定料架', 3, 83, '1,1,81,83', '', NULL, NULL, 1, '2025-04-02 01:06:38', '2025-04-02 01:06:47', 3);
INSERT INTO `locations` VALUES (97, 'HCL_C', 'C线处绑定料架', 3, 83, '1,1,81,83', '', NULL, NULL, 1, '2025-04-02 01:07:12', '2025-04-02 01:07:24', 3);
INSERT INTO `locations` VALUES (98, 'CX8512RCL', '热处理产线', 1, 1, '1,1', '', NULL, NULL, 1, '2025-04-02 01:08:34', '2025-04-02 01:08:34', 3);
INSERT INTO `locations` VALUES (99, 'GX8512RCL', '热处理线', 2, 98, '1,1,98', '', NULL, NULL, 1, '2025-04-02 01:09:11', '2025-04-02 01:09:11', 3);
INSERT INTO `locations` VALUES (100, 'GX8512YGTS', '荧光探伤', 2, 98, '1,1,98', '', NULL, NULL, 1, '2025-04-02 01:09:36', '2025-04-02 01:09:36', 3);
INSERT INTO `locations` VALUES (101, 'RCL_A', '热处理A线', 3, 99, '1,1,98,99', '', NULL, NULL, 1, '2025-04-02 01:10:01', '2025-04-02 01:10:01', 3);
INSERT INTO `locations` VALUES (102, 'RCL_B', '热处理B线', 3, 99, '1,1,98,99', '', NULL, NULL, 1, '2025-04-02 01:10:31', '2025-04-02 01:10:31', 3);
INSERT INTO `locations` VALUES (103, 'RCL_C', '热处理C线', 3, 99, '1,1,98,99', '', NULL, NULL, 1, '2025-04-02 01:10:43', '2025-04-02 01:10:43', 3);
INSERT INTO `locations` VALUES (104, 'RCL_D', '热处理D线', 3, 99, '1,1,98,99', '', NULL, NULL, 1, '2025-04-02 01:10:55', '2025-04-02 01:10:55', 3);
INSERT INTO `locations` VALUES (105, 'YGTS_A', '荧光探伤A线', 3, 100, '1,1,98,100', '', NULL, NULL, 1, '2025-04-02 01:11:24', '2025-04-02 01:11:24', 3);
INSERT INTO `locations` VALUES (106, 'YGTS_B', '荧光探伤B线', 3, 100, '1,1,98,100', '', NULL, NULL, 1, '2025-04-02 01:12:01', '2025-04-02 01:12:01', 3);
INSERT INTO `locations` VALUES (107, 'CX8512MPJY', '毛坯检验产线', 1, 1, '1,1', '', 4, NULL, 1, '2025-04-02 01:14:08', '2025-04-08 00:05:10', 3);
INSERT INTO `locations` VALUES (108, 'GX8512MPJY', '毛坯检验', 2, 107, '1,1,107', '', NULL, NULL, 1, '2025-04-02 01:15:12', '2025-04-02 01:15:12', 3);
INSERT INTO `locations` VALUES (109, 'MPJY_01', '毛坯检验01', 3, 108, '1,1,107,108', '', NULL, NULL, 1, '2025-04-02 01:16:04', '2025-04-02 01:16:04', 3);
INSERT INTO `locations` VALUES (110, 'MPJY_02', '毛坯检验02', 3, 108, '1,1,107,108', '', NULL, NULL, 1, '2025-04-02 01:16:29', '2025-04-02 01:16:29', 3);
INSERT INTO `locations` VALUES (111, 'MPJY_03', '毛坯检验03', 3, 108, '1,1,107,108', '', NULL, NULL, 1, '2025-04-02 01:16:46', '2025-04-02 01:16:46', 3);
INSERT INTO `locations` VALUES (112, 'MPJY_04', '毛坯检验04', 3, 108, '1,1,107,108', '', NULL, NULL, 1, '2025-04-02 01:17:04', '2025-04-02 01:17:04', 3);
INSERT INTO `locations` VALUES (113, 'MPJY_05', '毛坯检验05', 3, 108, '1,1,107,108', '', NULL, NULL, 1, '2025-04-02 01:17:25', '2025-04-02 01:17:25', 3);
INSERT INTO `locations` VALUES (114, 'MPJY_FM', '毛坯检验区反码', 3, 108, '1,1,107,108', '', NULL, NULL, 1, '2025-04-02 01:17:47', '2025-04-02 01:17:47', 3);
INSERT INTO `locations` VALUES (115, 'CX8512JJG', '机加产线', 1, 1, '1,1', '', 6, NULL, 1, '2025-04-02 01:18:31', '2025-04-08 00:05:34', 3);
INSERT INTO `locations` VALUES (116, 'GX8512JJG', '机加工', 2, 115, '1,1,115', '', NULL, NULL, 1, '2025-04-02 01:31:22', '2025-04-03 00:32:45', 3);
INSERT INTO `locations` VALUES (117, 'JJG_B', '机加B线', 3, 116, '1,1,115,116', '', NULL, NULL, 1, '2025-04-02 01:32:01', '2025-04-02 21:45:21', 3);
INSERT INTO `locations` VALUES (118, 'JJG_C', '机加C线', 3, 116, '1,1,115,116', '', NULL, NULL, 1, '2025-04-02 01:32:18', '2025-04-02 21:45:09', 3);
INSERT INTO `locations` VALUES (119, 'JJG_D', '机加D线', 3, 116, '1,1,115,116', '', NULL, NULL, 1, '2025-04-02 01:32:34', '2025-04-02 21:44:53', 3);
INSERT INTO `locations` VALUES (120, 'JJG_B_1', '机加B1', 4, 117, '1,1,115,116,117', '', NULL, NULL, 1, '2025-04-02 01:33:01', '2025-04-03 00:17:23', 3);
INSERT INTO `locations` VALUES (121, 'JJG_B_2', '机加B2', 4, 117, '1,1,115,116,117', '', NULL, NULL, 1, '2025-04-02 01:33:14', '2025-04-03 00:17:17', 3);
INSERT INTO `locations` VALUES (122, 'JJG_B_3', '机加B3', 4, 117, '1,1,115,116,117', '', NULL, NULL, 1, '2025-04-02 01:33:19', '2025-04-03 00:17:30', 3);
INSERT INTO `locations` VALUES (123, 'JJG_C_1', '机加C1', 4, 118, '1,1,115,116,118', '', NULL, NULL, 1, '2025-04-02 01:33:28', '2025-04-03 00:14:15', 3);
INSERT INTO `locations` VALUES (124, 'JJG_C_2', '机加C2', 4, 118, '1,1,115,116,118', '', NULL, NULL, 1, '2025-04-02 01:33:32', '2025-04-03 00:14:20', 3);
INSERT INTO `locations` VALUES (125, 'JJG_C_3', '机加C3', 4, 118, '1,1,115,116,118', '', NULL, NULL, 1, '2025-04-02 01:33:36', '2025-04-03 00:14:26', 3);
INSERT INTO `locations` VALUES (126, 'JJG_D_1', '机加D1', 4, 119, '1,1,115,116,119', '', NULL, NULL, 1, '2025-04-02 01:33:43', '2025-04-03 00:10:59', 3);
INSERT INTO `locations` VALUES (127, 'JJG_D_2', '机加D2', 4, 119, '1,1,115,116,119', '', NULL, NULL, 1, '2025-04-02 01:33:47', '2025-04-03 00:11:06', 3);
INSERT INTO `locations` VALUES (128, 'JJG_D_3', '机加D3', 4, 119, '1,1,115,116,119', '', NULL, NULL, 1, '2025-04-02 01:33:53', '2025-04-03 00:11:13', 3);
INSERT INTO `locations` VALUES (129, 'CX8512GP12', 'GP12产线', 1, 1, '1,1', '', 4, NULL, 1, '2025-04-02 01:34:35', '2025-04-08 00:05:49', 3);
INSERT INTO `locations` VALUES (130, 'GX8512GP12', 'GP12', 2, 129, '1,1,129', '', NULL, NULL, 1, '2025-04-02 01:34:57', '2025-04-02 01:34:57', 3);
INSERT INTO `locations` VALUES (131, 'GP12_JJ', '机加区域GP12', 3, 130, '1,1,129,130', '', NULL, NULL, 1, '2025-04-02 01:35:26', '2025-04-02 01:35:26', 3);
INSERT INTO `locations` VALUES (132, 'GP12_CK', '仓库区域GP12', 3, 130, '1,1,129,130', '', NULL, NULL, 1, '2025-04-02 01:35:43', '2025-04-02 01:35:43', 3);
INSERT INTO `locations` VALUES (133, 'RCL_A_S', '热处理A上料', 4, 101, '1,1,98,99,101', '', NULL, NULL, 1, '2025-04-02 02:44:34', '2025-04-02 02:44:34', 3);
INSERT INTO `locations` VALUES (134, 'RCL_A_X', '热处理A下料', 4, 101, '1,1,98,99,101', '', NULL, NULL, 1, '2025-04-02 02:44:48', '2025-04-02 02:44:48', 3);
INSERT INTO `locations` VALUES (135, 'RCL_B_S', '热处理B上料', 4, 102, '1,1,98,99,102', '', NULL, NULL, 1, '2025-04-02 02:45:13', '2025-04-02 02:45:13', 3);
INSERT INTO `locations` VALUES (136, 'RCL_B_X', '热处理B下料', 4, 102, '1,1,98,99,102', '', NULL, NULL, 1, '2025-04-02 02:45:24', '2025-04-02 02:45:24', 3);
INSERT INTO `locations` VALUES (137, 'RCL_C_S', '热处理C上料', 4, 103, '1,1,98,99,103', '', NULL, NULL, 1, '2025-04-02 02:45:33', '2025-04-02 02:45:33', 3);
INSERT INTO `locations` VALUES (138, 'RCL_C_X', '热处理C下料', 4, 103, '1,1,98,99,103', '', NULL, NULL, 1, '2025-04-02 02:45:41', '2025-04-02 02:45:41', 3);
INSERT INTO `locations` VALUES (139, 'RCL_D_S', '热处理D上料', 4, 104, '1,1,98,99,104', '', NULL, NULL, 1, '2025-04-02 02:45:50', '2025-04-02 02:45:50', 3);
INSERT INTO `locations` VALUES (140, 'RCL_D_X', '热处理D下料', 4, 104, '1,1,98,99,104', '', NULL, NULL, 1, '2025-04-02 02:45:57', '2025-04-02 02:45:57', 3);
INSERT INTO `locations` VALUES (141, 'GX8512ZZ', '组装线', 2, 115, '1,1,115', '', NULL, NULL, 1, '2025-04-02 02:50:24', '2025-04-02 02:50:24', 3);
INSERT INTO `locations` VALUES (142, 'RL_A_01', '熔炼A线', 4, 54, '1,1,38,39,54', '', NULL, NULL, 1, '2025-04-02 21:15:59', '2025-04-02 21:15:59', 3);
INSERT INTO `locations` VALUES (143, 'RL_B_01', '熔炼B线', 4, 55, '1,1,38,39,55', '', NULL, NULL, 1, '2025-04-02 21:16:10', '2025-04-02 21:16:10', 3);
INSERT INTO `locations` VALUES (144, 'RL_C_01', '熔炼C线', 4, 56, '1,1,38,39,56', '', NULL, NULL, 1, '2025-04-02 21:16:20', '2025-04-02 21:16:20', 3);
INSERT INTO `locations` VALUES (145, 'HCL_B_01', 'B线处绑定料架', 4, 96, '1,1,81,83,96', '', NULL, NULL, 1, '2025-04-02 21:16:47', '2025-04-02 21:16:47', 3);
INSERT INTO `locations` VALUES (146, 'HCL_C_01', 'C线处绑定料架', 4, 97, '1,1,81,83,97', '', NULL, NULL, 1, '2025-04-02 21:16:51', '2025-04-02 21:16:51', 3);
INSERT INTO `locations` VALUES (147, 'YGTS_A_01', '荧光探伤A线', 4, 105, '1,1,98,100,105', '', NULL, NULL, 1, '2025-04-02 21:17:15', '2025-04-02 21:17:15', 3);
INSERT INTO `locations` VALUES (148, 'YGTS_B_01', '荧光探伤B线', 4, 106, '1,1,98,100,106', '', NULL, NULL, 1, '2025-04-02 21:17:19', '2025-04-02 21:17:19', 3);
INSERT INTO `locations` VALUES (149, 'MPJY_01_01', '毛坯检验01', 4, 109, '1,1,107,108,109', '', NULL, NULL, 1, '2025-04-02 21:17:45', '2025-04-02 21:17:45', 3);
INSERT INTO `locations` VALUES (150, 'MPJY_02_01', '毛坯检验02', 4, 110, '1,1,107,108,110', '', NULL, NULL, 1, '2025-04-02 21:17:49', '2025-04-02 21:17:49', 3);
INSERT INTO `locations` VALUES (151, 'MPJY_04_01', '毛坯检验04', 4, 112, '1,1,107,108,112', '', NULL, NULL, 1, '2025-04-02 21:17:54', '2025-04-02 21:17:54', 3);
INSERT INTO `locations` VALUES (152, 'MPJY_03_01', '毛坯检验03', 4, 111, '1,1,107,108,111', '', NULL, NULL, 1, '2025-04-02 21:17:59', '2025-04-02 21:17:59', 3);
INSERT INTO `locations` VALUES (153, 'MPJY_05_01', '毛坯检验05', 4, 113, '1,1,107,108,113', '', NULL, NULL, 1, '2025-04-02 21:18:03', '2025-04-02 21:18:03', 3);
INSERT INTO `locations` VALUES (154, 'GX8512ZZ_01', '组装线', 3, 141, '1,1,115,141', '', NULL, NULL, 1, '2025-04-02 21:18:30', '2025-04-02 21:18:30', 3);
INSERT INTO `locations` VALUES (155, 'GX8512ZZ_01_01', '组装线', 4, 154, '1,1,115,141,154', '', NULL, NULL, 1, '2025-04-02 21:18:44', '2025-04-02 21:18:44', 3);
INSERT INTO `locations` VALUES (156, 'GP12_JJ_01', '机加区域GP12', 4, 131, '1,1,129,130,131', '', NULL, NULL, 1, '2025-04-02 21:19:01', '2025-04-02 21:19:01', 3);
INSERT INTO `locations` VALUES (157, 'GP12_CK_01', '仓库区域GP12', 4, 132, '1,1,129,130,132', '', NULL, NULL, 1, '2025-04-02 21:19:07', '2025-04-02 21:19:07', 3);
INSERT INTO `locations` VALUES (158, 'MPJY_FM_01', '毛坯检验区反码', 4, 114, '1,1,107,108,114', '', NULL, NULL, 1, '2025-04-02 21:24:09', '2025-04-02 21:24:09', 3);
INSERT INTO `locations` VALUES (159, 'JJG_B_1_26', '加26', 5, 120, '1,1,115,116,117,120', '', NULL, NULL, 1, '2025-04-02 21:25:50', '2025-04-03 00:16:44', 3);
INSERT INTO `locations` VALUES (160, 'JJG_B_1_27', '加27', 5, 120, '1,1,115,116,117,120', '', NULL, NULL, 1, '2025-04-02 21:26:07', '2025-04-03 00:16:48', 3);
INSERT INTO `locations` VALUES (161, 'JJG_B_1_28', '加28', 5, 120, '1,1,115,116,117,120', '', NULL, NULL, 1, '2025-04-02 21:27:16', '2025-04-03 00:16:51', 3);
INSERT INTO `locations` VALUES (162, 'JJG_B_1_29', '加29', 5, 120, '1,1,115,116,117,120', '', NULL, NULL, 1, '2025-04-02 21:27:25', '2025-04-03 00:16:55', 3);
INSERT INTO `locations` VALUES (163, 'JJG_B_1_30', '加30', 5, 120, '1,1,115,116,117,120', '', NULL, NULL, 1, '2025-04-02 21:27:32', '2025-04-03 00:16:59', 3);
INSERT INTO `locations` VALUES (164, 'JJG_B_1_31', '加31', 5, 120, '1,1,115,116,117,120', '', NULL, NULL, 1, '2025-04-02 21:27:38', '2025-04-03 00:17:03', 3);
INSERT INTO `locations` VALUES (165, 'JJG_B_1_32', '加32', 5, 120, '1,1,115,116,117,120', '', NULL, NULL, 1, '2025-04-02 21:27:47', '2025-04-03 00:17:08', 3);
INSERT INTO `locations` VALUES (166, 'JJG_B_1_33', '加33', 5, 120, '1,1,115,116,117,120', '', NULL, NULL, 1, '2025-04-02 21:27:52', '2025-04-03 00:17:12', 3);
INSERT INTO `locations` VALUES (167, 'JJG_B_2_35', '加35', 5, 121, '1,1,115,116,117,121', '', NULL, NULL, 1, '2025-04-02 21:28:17', '2025-04-03 00:17:43', 3);
INSERT INTO `locations` VALUES (168, 'JJG_B_2_36', '加36', 5, 121, '1,1,115,116,117,121', '', NULL, NULL, 1, '2025-04-02 21:28:24', '2025-04-03 00:17:48', 3);
INSERT INTO `locations` VALUES (169, 'JJG_B_2_37', '加37', 5, 121, '1,1,115,116,117,121', '', NULL, NULL, 1, '2025-04-02 21:28:32', '2025-04-03 00:17:52', 3);
INSERT INTO `locations` VALUES (170, 'JJG_B_2_38', '加38', 5, 121, '1,1,115,116,117,121', '', NULL, NULL, 1, '2025-04-02 21:28:38', '2025-04-03 00:17:56', 3);
INSERT INTO `locations` VALUES (171, 'JJG_B_2_39', '加39', 5, 121, '1,1,115,116,117,121', '', NULL, NULL, 1, '2025-04-02 21:28:45', '2025-04-03 00:18:01', 3);
INSERT INTO `locations` VALUES (172, 'JJG_B_2_40', '加40', 5, 121, '1,1,115,116,117,121', '', NULL, NULL, 1, '2025-04-02 21:28:51', '2025-04-03 00:18:06', 3);
INSERT INTO `locations` VALUES (173, 'JJG_B_2_41', '加41', 5, 121, '1,1,115,116,117,121', '', NULL, NULL, 1, '2025-04-02 21:28:57', '2025-04-03 00:18:10', 3);
INSERT INTO `locations` VALUES (174, 'JJG_B_2_42', '加42', 5, 121, '1,1,115,116,117,121', '', NULL, NULL, 1, '2025-04-02 21:29:03', '2025-04-03 00:18:14', 3);
INSERT INTO `locations` VALUES (175, 'JJG_B_2_43', '加43', 5, 121, '1,1,115,116,117,121', '', NULL, NULL, 1, '2025-04-02 21:29:14', '2025-04-03 00:18:18', 3);
INSERT INTO `locations` VALUES (176, 'JJG_B_2_44', '加44', 5, 121, '1,1,115,116,117,121', '', NULL, NULL, 1, '2025-04-02 21:29:19', '2025-04-03 00:18:23', 3);
INSERT INTO `locations` VALUES (177, 'JJG_C_1_51', '加51', 5, 123, '1,1,115,116,118,123', '', NULL, NULL, 1, '2025-04-02 21:29:49', '2025-04-03 00:14:33', 3);
INSERT INTO `locations` VALUES (178, 'JJG_C_1_52', '加52', 5, 123, '1,1,115,116,118,123', '', NULL, NULL, 1, '2025-04-02 21:29:55', '2025-04-03 00:14:37', 3);
INSERT INTO `locations` VALUES (179, 'JJG_C_1_53', '加53', 5, 123, '1,1,115,116,118,123', '', NULL, NULL, 1, '2025-04-02 21:30:01', '2025-04-03 00:14:42', 3);
INSERT INTO `locations` VALUES (180, 'JJG_C_1_54', '加54', 5, 123, '1,1,115,116,118,123', '', NULL, NULL, 1, '2025-04-02 21:30:10', '2025-04-03 00:14:50', 3);
INSERT INTO `locations` VALUES (181, 'JJG_C_1_55', '加55', 5, 123, '1,1,115,116,118,123', '', NULL, NULL, 1, '2025-04-02 21:30:14', '2025-04-03 00:14:54', 3);
INSERT INTO `locations` VALUES (182, 'JJG_C_1_56', '加56', 5, 123, '1,1,115,116,118,123', '', NULL, NULL, 1, '2025-04-02 21:30:20', '2025-04-03 00:14:58', 3);
INSERT INTO `locations` VALUES (183, 'JJG_C_1_57', '加57', 5, 123, '1,1,115,116,118,123', '', NULL, NULL, 1, '2025-04-02 21:30:25', '2025-04-03 00:15:02', 3);
INSERT INTO `locations` VALUES (184, 'JJG_C_1_58', '加58', 5, 123, '1,1,115,116,118,123', '', NULL, NULL, 1, '2025-04-02 21:30:31', '2025-04-03 00:15:08', 3);
INSERT INTO `locations` VALUES (185, 'JJG_C_1_59', '加59', 5, 123, '1,1,115,116,118,123', '', NULL, NULL, 1, '2025-04-02 21:30:37', '2025-04-03 00:15:12', 3);
INSERT INTO `locations` VALUES (186, 'JJG_C_2_60', '加60', 5, 124, '1,1,115,116,118,124', '', NULL, NULL, 1, '2025-04-02 21:31:11', '2025-04-03 00:15:20', 3);
INSERT INTO `locations` VALUES (187, 'JJG_C_2_61', '加61', 5, 124, '1,1,115,116,118,124', '', NULL, NULL, 1, '2025-04-02 21:31:17', '2025-04-03 00:15:25', 3);
INSERT INTO `locations` VALUES (188, 'JJG_C_2_62', '加62', 5, 124, '1,1,115,116,118,124', '', NULL, NULL, 1, '2025-04-02 21:31:22', '2025-04-03 00:15:29', 3);
INSERT INTO `locations` VALUES (189, 'JJG_C_2_63', '加63', 5, 124, '1,1,115,116,118,124', '', NULL, NULL, 1, '2025-04-02 21:31:27', '2025-04-03 00:15:33', 3);
INSERT INTO `locations` VALUES (190, 'JJG_C_2_64', '加64', 5, 124, '1,1,115,116,118,124', '', NULL, NULL, 1, '2025-04-02 21:31:33', '2025-04-03 00:15:37', 3);
INSERT INTO `locations` VALUES (191, 'JJG_C_2_65', '加65', 5, 124, '1,1,115,116,118,124', '', NULL, NULL, 1, '2025-04-02 21:31:38', '2025-04-03 00:15:41', 3);
INSERT INTO `locations` VALUES (192, 'JJG_C_2_66', '加66', 5, 124, '1,1,115,116,118,124', '', NULL, NULL, 1, '2025-04-02 21:31:43', '2025-04-03 00:15:46', 3);
INSERT INTO `locations` VALUES (193, 'JJG_C_2_67', '加67', 5, 124, '1,1,115,116,118,124', '', NULL, NULL, 1, '2025-04-02 21:31:51', '2025-04-03 00:15:50', 3);
INSERT INTO `locations` VALUES (194, 'JJG_C_2_68', '加68', 5, 124, '1,1,115,116,118,124', '', NULL, NULL, 1, '2025-04-02 21:31:57', '2025-04-03 00:15:53', 3);
INSERT INTO `locations` VALUES (195, 'JJG_C_2_69', '加69', 5, 124, '1,1,115,116,118,124', '', NULL, NULL, 1, '2025-04-02 21:32:06', '2025-04-03 00:15:57', 3);
INSERT INTO `locations` VALUES (196, 'JJG_C_3_70', '加70', 5, 125, '1,1,115,116,118,125', '', NULL, NULL, 1, '2025-04-02 21:40:41', '2025-04-03 00:16:07', 3);
INSERT INTO `locations` VALUES (197, 'JJG_C_3_71', '加71', 5, 125, '1,1,115,116,118,125', '', NULL, NULL, 1, '2025-04-02 21:40:46', '2025-04-03 00:16:11', 3);
INSERT INTO `locations` VALUES (198, 'JJG_C_3_72', '加72', 5, 125, '1,1,115,116,118,125', '', NULL, NULL, 1, '2025-04-02 21:41:11', '2025-04-03 00:16:16', 3);
INSERT INTO `locations` VALUES (199, 'JJG_C_3_73', '加73', 5, 125, '1,1,115,116,118,125', '', NULL, NULL, 1, '2025-04-02 21:41:24', '2025-04-03 00:16:20', 3);
INSERT INTO `locations` VALUES (200, 'JJG_C_3_74', '加74', 5, 125, '1,1,115,116,118,125', '', NULL, NULL, 1, '2025-04-02 21:41:32', '2025-04-03 00:16:24', 3);
INSERT INTO `locations` VALUES (201, 'JJG_D_1_76', '加76', 5, 126, '1,1,115,116,119,126', '', NULL, NULL, 1, '2025-04-03 00:11:46', '2025-04-03 00:11:46', 3);
INSERT INTO `locations` VALUES (202, 'JJG_D_1_77', '加77', 5, 126, '1,1,115,116,119,126', '', NULL, NULL, 1, '2025-04-03 00:11:54', '2025-04-03 00:11:54', 3);
INSERT INTO `locations` VALUES (203, 'JJG_D_1_78', '加78', 5, 126, '1,1,115,116,119,126', '', NULL, NULL, 1, '2025-04-03 00:12:02', '2025-04-03 00:12:02', 3);
INSERT INTO `locations` VALUES (204, 'JJG_D_1_79', '加79', 5, 126, '1,1,115,116,119,126', '', NULL, NULL, 1, '2025-04-03 00:12:08', '2025-04-03 00:12:08', 3);
INSERT INTO `locations` VALUES (205, 'JJG_D_1_80', '加80', 5, 126, '1,1,115,116,119,126', '', NULL, NULL, 1, '2025-04-03 00:12:17', '2025-04-03 00:12:17', 3);
INSERT INTO `locations` VALUES (206, 'JJG_D_1_81', '加81', 5, 126, '1,1,115,116,119,126', '', NULL, NULL, 1, '2025-04-03 00:12:26', '2025-04-03 00:12:26', 3);
INSERT INTO `locations` VALUES (207, 'JJG_D_2_82', '加82', 5, 127, '1,1,115,116,119,127', '', NULL, NULL, 1, '2025-04-03 00:12:42', '2025-04-03 00:12:42', 3);
INSERT INTO `locations` VALUES (208, 'JJG_D_2_83', '加83', 5, 127, '1,1,115,116,119,127', '', NULL, NULL, 1, '2025-04-03 00:12:52', '2025-04-03 00:12:52', 3);
INSERT INTO `locations` VALUES (209, 'JJG_D_2_84', '加84', 5, 127, '1,1,115,116,119,127', '', NULL, NULL, 1, '2025-04-03 00:13:00', '2025-04-03 00:13:00', 3);
INSERT INTO `locations` VALUES (210, 'JJG_D_2_85', '加85', 5, 127, '1,1,115,116,119,127', '', NULL, NULL, 1, '2025-04-03 00:13:09', '2025-04-03 00:13:09', 3);
INSERT INTO `locations` VALUES (211, 'JJG_D_2_86', '加86', 5, 127, '1,1,115,116,119,127', '', NULL, NULL, 1, '2025-04-03 00:13:16', '2025-04-03 00:13:16', 3);
INSERT INTO `locations` VALUES (212, 'JJG_D_2_87', '加87', 5, 127, '1,1,115,116,119,127', '', NULL, NULL, 1, '2025-04-03 00:13:24', '2025-04-03 00:13:24', 3);
INSERT INTO `locations` VALUES (213, 'JJG_D_2_88', '加88', 5, 127, '1,1,115,116,119,127', '', NULL, NULL, 1, '2025-04-03 00:13:29', '2025-04-03 00:13:29', 3);
INSERT INTO `locations` VALUES (214, 'JJG_D_2_89', '加89', 5, 127, '1,1,115,116,119,127', '', NULL, NULL, 1, '2025-04-03 00:13:37', '2025-04-03 00:13:37', 3);
INSERT INTO `locations` VALUES (215, 'JJG_D_2_90', '加90', 5, 127, '1,1,115,116,119,127', '', NULL, NULL, 1, '2025-04-03 00:13:43', '2025-04-03 00:13:43', 3);
INSERT INTO `locations` VALUES (216, 'JJG_D_2_91', '加91', 5, 127, '1,1,115,116,119,127', '', NULL, NULL, 1, '2025-04-03 00:13:52', '2025-04-03 00:13:52', 3);
INSERT INTO `locations` VALUES (217, 'JJG_D_2_92', '加92', 5, 127, '1,1,115,116,119,127', '', NULL, NULL, 1, '2025-04-03 00:14:00', '2025-04-03 00:14:00', 3);
INSERT INTO `locations` VALUES (218, 'JJG_B1', '机加B1', 3, 116, '1,1,115,116', '', NULL, NULL, 1, '2025-04-03 00:31:32', '2025-04-03 00:31:32', 3);
INSERT INTO `locations` VALUES (219, 'JJG_B2', '机加B2', 3, 116, '1,1,115,116', '', NULL, NULL, 1, '2025-04-03 00:33:03', '2025-04-03 00:33:03', 3);
INSERT INTO `locations` VALUES (220, 'JJG_B3', '机加B3', 3, 116, '1,1,115,116', '', NULL, NULL, 1, '2025-04-03 00:33:18', '2025-04-03 00:33:18', 3);
INSERT INTO `locations` VALUES (221, 'JJG_C1', '机加C1', 3, 116, '1,1,115,116', '', NULL, NULL, 1, '2025-04-03 00:33:35', '2025-04-03 00:33:35', 3);
INSERT INTO `locations` VALUES (222, 'JJG_C2', '机加C2', 3, 116, '1,1,115,116', '', NULL, NULL, 1, '2025-04-03 00:33:48', '2025-04-03 00:33:48', 3);
INSERT INTO `locations` VALUES (223, 'JJG_C3', '机加C3', 3, 116, '1,1,115,116', '', NULL, NULL, 1, '2025-04-03 00:34:04', '2025-04-03 00:34:04', 3);
INSERT INTO `locations` VALUES (224, 'JJG_D1', '机加D1', 3, 116, '1,1,115,116', '', NULL, NULL, 1, '2025-04-03 00:34:24', '2025-04-03 00:34:24', 3);
INSERT INTO `locations` VALUES (225, 'JJG_D2', '机加D2', 3, 116, '1,1,115,116', '', NULL, NULL, 1, '2025-04-03 00:34:41', '2025-04-03 00:35:29', 3);
INSERT INTO `locations` VALUES (226, 'JJG_D3', '机加D3', 3, 116, '1,1,115,116', '', NULL, NULL, 1, '2025-04-03 00:34:49', '2025-04-03 00:35:34', 3);
INSERT INTO `locations` VALUES (227, 'JJG_B1_26', '加26', 4, 218, '1,1,115,116,218', '', NULL, NULL, 1, '2025-04-03 00:47:27', '2025-04-03 00:47:27', 3);
INSERT INTO `locations` VALUES (228, 'JJG_B1_27', '加27', 4, 218, '1,1,115,116,218', '', NULL, NULL, 1, '2025-04-03 00:47:36', '2025-04-03 00:47:36', 3);
INSERT INTO `locations` VALUES (229, 'JJG_B1_28', '加28', 4, 218, '1,1,115,116,218', '', NULL, NULL, 1, '2025-04-03 00:47:43', '2025-04-03 00:47:43', 3);
INSERT INTO `locations` VALUES (230, 'JJG_B1_29', '加29', 4, 218, '1,1,115,116,218', '', NULL, NULL, 1, '2025-04-03 00:47:48', '2025-04-03 00:47:48', 3);
INSERT INTO `locations` VALUES (231, 'JJG_B1_30', '加30', 4, 218, '1,1,115,116,218', '', NULL, NULL, 1, '2025-04-03 00:47:55', '2025-04-03 00:47:55', 3);
INSERT INTO `locations` VALUES (232, 'JJG_B1_31', '加31', 4, 218, '1,1,115,116,218', '', NULL, NULL, 1, '2025-04-03 00:48:01', '2025-04-03 00:48:01', 3);
INSERT INTO `locations` VALUES (233, 'JJG_B1_32', '加32', 4, 218, '1,1,115,116,218', '', NULL, NULL, 1, '2025-04-03 00:48:10', '2025-04-03 00:48:10', 3);
INSERT INTO `locations` VALUES (234, 'JJG_B1_33', '加33', 4, 218, '1,1,115,116,218', '', NULL, NULL, 1, '2025-04-03 00:48:18', '2025-04-03 00:48:18', 3);
INSERT INTO `locations` VALUES (235, 'JJG_B2_35', '加35', 4, 219, '1,1,115,116,219', '', NULL, NULL, 1, '2025-04-03 00:48:57', '2025-04-03 00:48:57', 3);
INSERT INTO `locations` VALUES (236, 'JJG_B2_36', '加36', 4, 219, '1,1,115,116,219', '', NULL, NULL, 1, '2025-04-03 00:49:04', '2025-04-03 00:49:04', 3);
INSERT INTO `locations` VALUES (237, 'JJG_B2_37', '加37', 4, 219, '1,1,115,116,219', '', NULL, NULL, 1, '2025-04-03 00:49:10', '2025-04-03 00:49:10', 3);
INSERT INTO `locations` VALUES (238, 'JJG_B2_38', '加38', 4, 219, '1,1,115,116,219', '', NULL, NULL, 1, '2025-04-03 00:49:16', '2025-04-03 00:49:16', 3);
INSERT INTO `locations` VALUES (239, 'JJG_B2_39', '加39', 4, 219, '1,1,115,116,219', '', NULL, NULL, 1, '2025-04-03 00:49:21', '2025-04-03 00:49:21', 3);
INSERT INTO `locations` VALUES (240, 'JJG_B2_40', '加40', 4, 219, '1,1,115,116,219', '', NULL, NULL, 1, '2025-04-03 00:49:28', '2025-04-03 00:49:28', 3);
INSERT INTO `locations` VALUES (241, 'JJG_B2_41', '41', 4, 219, '1,1,115,116,219', '', NULL, NULL, 1, '2025-04-03 00:49:34', '2025-04-03 00:49:34', 3);
INSERT INTO `locations` VALUES (242, 'JJG_B2_42', '加42', 4, 219, '1,1,115,116,219', '', NULL, NULL, 1, '2025-04-03 00:49:39', '2025-04-03 00:49:39', 3);
INSERT INTO `locations` VALUES (243, 'JJG_B2_43', '加43', 4, 219, '1,1,115,116,219', '', NULL, NULL, 1, '2025-04-03 00:49:47', '2025-04-03 00:49:47', 3);
INSERT INTO `locations` VALUES (244, 'JJG_B2_44', '加44', 4, 219, '1,1,115,116,219', '', NULL, NULL, 1, '2025-04-03 00:49:54', '2025-04-03 00:49:54', 3);
INSERT INTO `locations` VALUES (245, 'JJG_C1_51', '加51', 4, 221, '1,1,115,116,221', '', NULL, NULL, 1, '2025-04-03 00:50:06', '2025-04-03 00:50:06', 3);
INSERT INTO `locations` VALUES (246, 'JJG_C1_52', '加52', 4, 221, '1,1,115,116,221', '', NULL, NULL, 1, '2025-04-03 00:50:13', '2025-04-03 00:50:13', 3);
INSERT INTO `locations` VALUES (247, 'JJG_C1_53', '加53', 4, 221, '1,1,115,116,221', '', NULL, NULL, 1, '2025-04-03 00:50:23', '2025-04-03 00:50:23', 3);
INSERT INTO `locations` VALUES (248, 'JJG_C1_54', '加54', 4, 221, '1,1,115,116,221', '', NULL, NULL, 1, '2025-04-03 00:50:35', '2025-04-03 00:50:35', 3);
INSERT INTO `locations` VALUES (249, 'JJG_C1_55', '加55', 4, 221, '1,1,115,116,221', '', NULL, NULL, 1, '2025-04-03 00:50:39', '2025-04-03 00:50:39', 3);
INSERT INTO `locations` VALUES (250, 'JJG_C1_56', '加56', 4, 221, '1,1,115,116,221', '', NULL, NULL, 1, '2025-04-03 00:50:45', '2025-04-03 00:50:45', 3);
INSERT INTO `locations` VALUES (251, 'JJG_C1_57', '加57', 4, 221, '1,1,115,116,221', '', NULL, NULL, 1, '2025-04-03 00:50:52', '2025-04-03 00:50:52', 3);
INSERT INTO `locations` VALUES (252, 'JJG_C1_58', '加58      ', 4, 221, '1,1,115,116,221', '', NULL, NULL, 1, '2025-04-03 00:51:12', '2025-04-03 00:51:12', 3);
INSERT INTO `locations` VALUES (253, 'JJG_C1_59', '加59', 4, 221, '1,1,115,116,221', '', NULL, NULL, 1, '2025-04-03 00:51:18', '2025-04-03 00:51:18', 3);
INSERT INTO `locations` VALUES (254, 'JJG_C2_60', '加60', 4, 222, '1,1,115,116,222', '', NULL, NULL, 1, '2025-04-03 00:51:30', '2025-04-03 00:51:30', 3);
INSERT INTO `locations` VALUES (255, 'JJG_C2_61', '加61', 4, 222, '1,1,115,116,222', '', NULL, NULL, 1, '2025-04-03 00:51:41', '2025-04-03 00:51:41', 3);
INSERT INTO `locations` VALUES (256, 'JJG_C2_62', '加62', 4, 222, '1,1,115,116,222', '', NULL, NULL, 1, '2025-04-03 00:51:46', '2025-04-03 00:51:46', 3);
INSERT INTO `locations` VALUES (257, 'JJG_C2_63', '加63', 4, 222, '1,1,115,116,222', '', NULL, NULL, 1, '2025-04-03 00:51:54', '2025-04-03 00:51:54', 3);
INSERT INTO `locations` VALUES (258, 'JJG_C2_64', '加64', 4, 222, '1,1,115,116,222', '', NULL, NULL, 1, '2025-04-03 00:52:01', '2025-04-03 00:52:01', 3);
INSERT INTO `locations` VALUES (259, 'JJG_C2_65', '加65', 4, 222, '1,1,115,116,222', '', NULL, NULL, 1, '2025-04-03 00:52:06', '2025-04-03 00:52:06', 3);
INSERT INTO `locations` VALUES (260, 'JJG_C2_66', '加66', 4, 222, '1,1,115,116,222', '', NULL, NULL, 1, '2025-04-03 00:52:11', '2025-04-03 00:52:11', 3);
INSERT INTO `locations` VALUES (261, 'JJG_C2_67', '加67', 4, 222, '1,1,115,116,222', '', NULL, NULL, 1, '2025-04-03 00:52:18', '2025-04-03 00:52:18', 3);
INSERT INTO `locations` VALUES (262, 'JJG_C2_68', '加68', 4, 222, '1,1,115,116,222', '', NULL, NULL, 1, '2025-04-03 00:52:23', '2025-04-03 00:52:23', 3);
INSERT INTO `locations` VALUES (263, 'JJG_C2_69', '加69', 4, 222, '1,1,115,116,222', '', NULL, NULL, 1, '2025-04-03 00:52:29', '2025-04-03 00:52:29', 3);
INSERT INTO `locations` VALUES (264, 'JJG_C3_70', '加70', 4, 223, '1,1,115,116,223', '', NULL, NULL, 1, '2025-04-03 00:52:48', '2025-04-03 00:52:48', 3);
INSERT INTO `locations` VALUES (265, 'JJG_C3_71', '加71', 4, 223, '1,1,115,116,223', '', NULL, NULL, 1, '2025-04-03 00:52:55', '2025-04-03 00:52:55', 3);
INSERT INTO `locations` VALUES (266, 'JJG_C3_72', '加72', 4, 223, '1,1,115,116,223', '', NULL, NULL, 1, '2025-04-03 00:53:01', '2025-04-03 00:53:01', 3);
INSERT INTO `locations` VALUES (267, 'JJG_C3_73', '加73', 4, 223, '1,1,115,116,223', '', NULL, NULL, 1, '2025-04-03 00:53:06', '2025-04-03 00:53:06', 3);
INSERT INTO `locations` VALUES (268, 'JJG_C3_74', '加74', 4, 223, '1,1,115,116,223', '', NULL, NULL, 1, '2025-04-03 00:53:11', '2025-04-03 00:53:11', 3);
INSERT INTO `locations` VALUES (269, 'JJG_C3_75', '加75', 4, 223, '1,1,115,116,223', '', NULL, NULL, 1, '2025-04-03 00:53:22', '2025-04-03 00:53:22', 3);
INSERT INTO `locations` VALUES (270, 'JJG_D1_76', '加76', 4, 224, '1,1,115,116,224', '', NULL, NULL, 1, '2025-04-03 00:53:38', '2025-04-03 00:53:38', 3);
INSERT INTO `locations` VALUES (271, 'JJG_D1_77', '加77', 4, 224, '1,1,115,116,224', '', NULL, NULL, 1, '2025-04-03 00:53:42', '2025-04-03 00:53:42', 3);
INSERT INTO `locations` VALUES (272, 'JJG_D1_78', '加78', 4, 224, '1,1,115,116,224', '', NULL, NULL, 1, '2025-04-03 00:53:48', '2025-04-03 00:53:48', 3);
INSERT INTO `locations` VALUES (273, 'JJG_D1_79', '加79', 4, 224, '1,1,115,116,224', '', NULL, NULL, 1, '2025-04-03 00:53:53', '2025-04-03 00:53:53', 3);
INSERT INTO `locations` VALUES (274, 'JJG_D1_80', '加80', 4, 224, '1,1,115,116,224', '', NULL, NULL, 1, '2025-04-03 00:53:59', '2025-04-03 00:53:59', 3);
INSERT INTO `locations` VALUES (275, 'JJG_D1_81', '加81', 4, 224, '1,1,115,116,224', '', NULL, NULL, 1, '2025-04-03 00:54:07', '2025-04-03 00:54:07', 3);
INSERT INTO `locations` VALUES (276, 'JJG_D2_82', '加82', 4, 225, '1,1,115,116,225', '', NULL, NULL, 1, '2025-04-03 00:54:18', '2025-04-03 00:54:18', 3);
INSERT INTO `locations` VALUES (277, 'JJG_D2_83', '加83', 4, 225, '1,1,115,116,225', '', NULL, NULL, 1, '2025-04-03 00:54:32', '2025-04-03 00:54:32', 3);
INSERT INTO `locations` VALUES (278, 'JJG_D2_84', '加84', 4, 225, '1,1,115,116,225', '', NULL, NULL, 1, '2025-04-03 00:54:39', '2025-04-03 00:54:39', 3);
INSERT INTO `locations` VALUES (279, 'JJG_D2_85', '加85', 4, 225, '1,1,115,116,225', '', NULL, NULL, 1, '2025-04-03 00:54:45', '2025-04-03 00:54:45', 3);
INSERT INTO `locations` VALUES (280, 'JJG_D2_86', '加86', 4, 225, '1,1,115,116,225', '', NULL, NULL, 1, '2025-04-03 00:54:50', '2025-04-03 00:54:50', 3);
INSERT INTO `locations` VALUES (281, 'JJG_D2_87', '加87', 4, 225, '1,1,115,116,225', '', NULL, NULL, 1, '2025-04-03 00:54:55', '2025-04-03 00:54:55', 3);
INSERT INTO `locations` VALUES (282, 'JJG_D2_88', '加88', 4, 225, '1,1,115,116,225', '', NULL, NULL, 1, '2025-04-03 00:55:00', '2025-04-03 00:55:00', 3);
INSERT INTO `locations` VALUES (283, 'JJG_D2_89', '加89', 4, 225, '1,1,115,116,225', '', NULL, NULL, 1, '2025-04-03 00:55:06', '2025-04-03 00:55:06', 3);
INSERT INTO `locations` VALUES (284, 'JJG_D2_90', '加90', 4, 225, '1,1,115,116,225', '', NULL, NULL, 1, '2025-04-03 00:55:13', '2025-04-03 00:55:13', 3);
INSERT INTO `locations` VALUES (285, 'JJG_D2_91', '加91', 4, 225, '1,1,115,116,225', '', NULL, NULL, 1, '2025-04-03 00:55:19', '2025-04-03 00:55:19', 3);
INSERT INTO `locations` VALUES (286, 'JJG_D2_92', '加92', 4, 225, '1,1,115,116,225', '', NULL, NULL, 1, '2025-04-03 00:55:26', '2025-04-03 00:55:26', 3);
INSERT INTO `locations` VALUES (287, 'D12XC', '现场使用', 0, NULL, '0', '', 4, NULL, 1, '2025-04-09 05:23:25', '2025-04-09 05:25:00', 3);
INSERT INTO `locations` VALUES (288, 'D12XC_01', '现场使用', 1, 287, '0,287', '', NULL, NULL, 1, '2025-04-09 05:23:48', '2025-04-09 05:23:48', 3);
INSERT INTO `locations` VALUES (289, 'D12XC_01_01', '现场使用', 2, 288, '0,287,288', '', NULL, NULL, 1, '2025-04-09 05:24:00', '2025-04-09 05:24:00', 3);
INSERT INTO `locations` VALUES (290, 'D12XC_01_01_01', '现场使用', 3, 289, '0,287,288,289', '', NULL, NULL, 1, '2025-04-09 05:24:11', '2025-04-09 05:24:11', 3);
INSERT INTO `locations` VALUES (291, 'D12XCSY', '现场使用', 4, 290, '0,287,288,289,290', '', NULL, NULL, 1, '2025-04-09 05:24:29', '2025-04-09 05:24:29', 3);

-- ----------------------------
-- Table structure for locationusers
-- ----------------------------
DROP TABLE IF EXISTS `locationusers`;
CREATE TABLE `locationusers`  (
  `location_id` int NOT NULL,
  `personnel_id` int NOT NULL,
  `user_type` tinyint NOT NULL COMMENT '0-使用人 1-管理员',
  `created_at` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`location_id`, `personnel_id`, `user_type`) USING BTREE,
  INDEX `personnel_id`(`personnel_id`) USING BTREE,
  CONSTRAINT `locationusers_ibfk_1` FOREIGN KEY (`location_id`) REFERENCES `locations` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `locationusers_ibfk_2` FOREIGN KEY (`personnel_id`) REFERENCES `personnel` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of locationusers
-- ----------------------------
INSERT INTO `locationusers` VALUES (36, 4, 0, '2025-04-08 00:00:24');
INSERT INTO `locationusers` VALUES (36, 12, 0, '2025-04-08 00:00:24');
INSERT INTO `locationusers` VALUES (38, 12, 0, '2025-04-08 00:03:10');
INSERT INTO `locationusers` VALUES (39, 12, 0, '2025-04-08 00:03:23');
INSERT INTO `locationusers` VALUES (39, 18, 0, '2025-04-08 00:03:23');
INSERT INTO `locationusers` VALUES (40, 5, 0, '2025-04-08 00:03:31');
INSERT INTO `locationusers` VALUES (40, 12, 0, '2025-04-08 00:03:31');
INSERT INTO `locationusers` VALUES (43, 2, 0, '2025-04-07 00:48:24');
INSERT INTO `locationusers` VALUES (44, 2, 0, '2025-04-07 13:05:52');
INSERT INTO `locationusers` VALUES (45, 2, 0, '2025-04-07 00:48:57');
INSERT INTO `locationusers` VALUES (81, 6, 0, '2025-04-08 00:03:51');
INSERT INTO `locationusers` VALUES (81, 7, 0, '2025-04-08 00:03:51');
INSERT INTO `locationusers` VALUES (81, 13, 0, '2025-04-08 00:03:51');
INSERT INTO `locationusers` VALUES (107, 15, 0, '2025-04-08 00:05:11');
INSERT INTO `locationusers` VALUES (107, 19, 0, '2025-04-08 00:05:11');
INSERT INTO `locationusers` VALUES (115, 8, 0, '2025-04-08 00:05:34');
INSERT INTO `locationusers` VALUES (115, 9, 0, '2025-04-08 00:05:34');
INSERT INTO `locationusers` VALUES (115, 10, 0, '2025-04-08 00:05:34');
INSERT INTO `locationusers` VALUES (115, 11, 0, '2025-04-08 00:05:34');
INSERT INTO `locationusers` VALUES (115, 14, 0, '2025-04-08 00:05:34');
INSERT INTO `locationusers` VALUES (129, 15, 0, '2025-04-08 00:05:49');
INSERT INTO `locationusers` VALUES (129, 19, 0, '2025-04-08 00:05:49');
INSERT INTO `locationusers` VALUES (287, 15, 0, '2025-04-09 05:25:00');
INSERT INTO `locationusers` VALUES (287, 19, 0, '2025-04-09 05:25:00');

-- ----------------------------
-- Table structure for maintenanceorders
-- ----------------------------
DROP TABLE IF EXISTS `maintenanceorders`;
CREATE TABLE `maintenanceorders`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `OrderCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '维护单号',
  `Title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '标题',
  `Description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '描述',
  `AssetId` int NOT NULL COMMENT '资产ID',
  `FaultRecordId` int NULL DEFAULT NULL COMMENT '故障记录ID',
  `MaintenanceType` int NOT NULL DEFAULT 0 COMMENT '维护类型：0常规维护，1故障维修，2返厂维修跟踪',
  `Status` int NOT NULL DEFAULT 0 COMMENT '状态：0待处理，1处理中，2已完成，3已取消',
  `CreatorId` int NOT NULL COMMENT '创建人ID',
  `CreateTime` datetime(0) NOT NULL COMMENT '创建时间',
  `AssigneeId` int NULL DEFAULT NULL COMMENT '处理人ID',
  `AssignTime` datetime(0) NULL DEFAULT NULL COMMENT '分配时间',
  `PlanStartTime` datetime(0) NULL DEFAULT NULL COMMENT '计划开始时间',
  `PlanEndTime` datetime(0) NULL DEFAULT NULL COMMENT '计划结束时间',
  `ActualStartTime` datetime(0) NULL DEFAULT NULL COMMENT '实际开始时间',
  `ActualEndTime` datetime(0) NULL DEFAULT NULL COMMENT '实际结束时间',
  `Solution` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '解决方案',
  `Notes` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `CreatedAt` datetime(0) NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime(0) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE INDEX `IX_MaintenanceOrders_OrderCode`(`OrderCode`) USING BTREE,
  INDEX `IX_MaintenanceOrders_AssetId`(`AssetId`) USING BTREE,
  INDEX `IX_MaintenanceOrders_FaultRecordId`(`FaultRecordId`) USING BTREE,
  INDEX `IX_MaintenanceOrders_CreatorId`(`CreatorId`) USING BTREE,
  INDEX `IX_MaintenanceOrders_AssigneeId`(`AssigneeId`) USING BTREE,
  CONSTRAINT `FK_MaintenanceOrders_Assets_AssetId` FOREIGN KEY (`AssetId`) REFERENCES `assets` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_MaintenanceOrders_FaultRecords_FaultRecordId` FOREIGN KEY (`FaultRecordId`) REFERENCES `faultrecords` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_MaintenanceOrders_Users_AssigneeId` FOREIGN KEY (`AssigneeId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_MaintenanceOrders_Users_CreatorId` FOREIGN KEY (`CreatorId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '维护订单表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of maintenanceorders
-- ----------------------------

-- ----------------------------
-- Table structure for menus
-- ----------------------------
DROP TABLE IF EXISTS `menus`;
CREATE TABLE `menus`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `Name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '菜单名称',
  `Code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '菜单编码',
  `ParentId` int NULL DEFAULT NULL COMMENT '父菜单ID',
  `Type` int NOT NULL DEFAULT 0 COMMENT '菜单类型：0菜单，1按钮',
  `Icon` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '图标',
  `Path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '路由路径',
  `Component` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '组件路径',
  `Permission` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '权限标识',
  `Description` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '描述',
  `SortOrder` int NOT NULL DEFAULT 0 COMMENT '排序',
  `IsExternal` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否外链',
  `KeepAlive` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否缓存',
  `IsVisible` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否可见',
  `IsActive` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否激活',
  `CreatedAt` datetime(0) NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime(0) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE INDEX `IX_Menus_Code`(`Code`) USING BTREE,
  INDEX `IX_Menus_ParentId`(`ParentId`) USING BTREE,
  CONSTRAINT `FK_Menus_Menus_ParentId` FOREIGN KEY (`ParentId`) REFERENCES `menus` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 27 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '菜单表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of menus
-- ----------------------------
INSERT INTO `menus` VALUES (1, '首页', 'DASHBOARD', NULL, 0, 'dashboard', '/dashboard', 'Dashboard', 'dashboard', '系统首页', 1, 0, 1, 1, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `menus` VALUES (2, '资产管理', 'ASSET', NULL, 0, 'computer', '/asset', 'Layout', 'asset', '资产管理模块', 2, 0, 1, 1, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `menus` VALUES (3, '故障管理', 'FAULT', NULL, 0, 'bug', '/fault', 'Layout', 'fault', '故障管理模块', 3, 0, 1, 1, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `menus` VALUES (4, '维修管理', 'MAINTENANCE', NULL, 0, 'tool', '/maintenance', 'Layout', 'maintenance', '维修管理模块', 4, 0, 1, 1, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `menus` VALUES (5, '采购管理', 'PURCHASE', NULL, 0, 'shopping-cart', '/purchase', 'Layout', 'purchase', '采购管理模块', 5, 0, 1, 1, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `menus` VALUES (6, '系统管理', 'SYSTEM', NULL, 0, 'setting', '/system', 'Layout', 'system', '系统管理模块', 6, 0, 1, 1, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `menus` VALUES (7, '任务管理', 'TASK', NULL, 0, 'calendar', '/task', 'Layout', 'task', '任务管理模块', 7, 0, 1, 1, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `menus` VALUES (8, '资产列表', 'ASSET:LIST', 2, 0, 'list', '/asset/list', 'asset/List', 'asset:list', '资产列表', 1, 0, 1, 1, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `menus` VALUES (9, '资产类型', 'ASSET:TYPE', 2, 0, 'tag', '/asset/type', 'asset/Type', 'asset:type', '资产类型管理', 2, 0, 1, 1, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `menus` VALUES (10, '位置管理', 'ASSET:LOCATION', 2, 0, 'environment', '/asset/location', 'asset/Location', 'asset:location', '位置管理', 3, 0, 1, 1, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `menus` VALUES (11, '故障列表', 'FAULT:LIST', 3, 0, 'exception', '/fault/list', 'fault/List', 'fault:list', '故障列表', 1, 0, 1, 1, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `menus` VALUES (12, '故障类型', 'FAULT:TYPE', 3, 0, 'tag', '/fault/type', 'fault/Type', 'fault:type', '故障类型管理', 2, 0, 1, 1, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `menus` VALUES (13, '返厂维修', 'FAULT:RETURN', 3, 0, 'rollback', '/fault/return', 'fault/Return', 'fault:return', '返厂维修管理', 3, 0, 1, 1, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `menus` VALUES (14, '维修列表', 'MAINTENANCE:LIST', 4, 0, 'build', '/maintenance/list', 'maintenance/List', 'maintenance:list', '维修列表', 1, 0, 1, 1, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `menus` VALUES (15, '维修统计', 'MAINTENANCE:STATS', 4, 0, 'bar-chart', '/maintenance/stats', 'maintenance/Stats', 'maintenance:stats', '维修统计', 2, 0, 1, 1, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `menus` VALUES (16, '采购单', 'PURCHASE:ORDER', 5, 0, 'file-text', '/purchase/order', 'purchase/Order', 'purchase:order', '采购单管理', 1, 0, 1, 1, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `menus` VALUES (17, '供应商', 'PURCHASE:SUPPLIER', 5, 0, 'team', '/purchase/supplier', 'purchase/Supplier', 'purchase:supplier', '供应商管理', 2, 0, 1, 1, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `menus` VALUES (18, '资产入库', 'PURCHASE:RECEIVE', 5, 0, 'import', '/purchase/receive', 'purchase/Receive', 'purchase:receive', '资产入库', 3, 0, 1, 1, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `menus` VALUES (19, '用户管理', 'SYSTEM:USER', 6, 0, 'user', '/system/user', 'system/User', 'system:user', '用户管理', 1, 0, 1, 1, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `menus` VALUES (20, '角色管理', 'SYSTEM:ROLE', 6, 0, 'team', '/system/role', 'system/Role', 'system:role', '角色管理', 2, 0, 1, 1, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `menus` VALUES (21, '菜单管理', 'SYSTEM:MENU', 6, 0, 'menu', '/system/menu', 'system/Menu', 'system:menu', '菜单管理', 3, 0, 1, 1, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `menus` VALUES (22, '部门管理', 'SYSTEM:DEPARTMENT', 6, 0, 'apartment', '/system/department', 'system/Department', 'system:department', '部门管理', 4, 0, 1, 1, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `menus` VALUES (23, '日志管理', 'SYSTEM:LOG', 6, 0, 'file-text', '/system/log', 'system/Log', 'system:log', '日志管理', 5, 0, 1, 1, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `menus` VALUES (24, '任务列表', 'TASK:LIST', 7, 0, 'unordered-list', '/task/list', 'task/List', 'task:list', '任务列表', 1, 0, 1, 1, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `menus` VALUES (25, '周期任务', 'TASK:PERIODIC', 7, 0, 'reload', '/task/periodic', 'task/Periodic', 'task:periodic', '周期任务管理', 2, 0, 1, 1, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `menus` VALUES (26, 'PDCA计划', 'TASK:PDCA', 7, 0, 'sync', '/task/pdca', 'task/Pdca', 'task:pdca', 'PDCA计划管理', 3, 0, 1, 1, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');

-- ----------------------------
-- Table structure for notifications
-- ----------------------------
DROP TABLE IF EXISTS `notifications`;
CREATE TABLE `notifications`  (
  `NotificationId` bigint NOT NULL AUTO_INCREMENT COMMENT '通知ID (BIGINT)',
  `UserId` int NOT NULL COMMENT '接收用户ID (关联 users.Id - INT)',
  `Title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `Content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `Type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '通知类型：task/system/alert/gamification',
  `ReferenceId` bigint NULL DEFAULT NULL COMMENT '关联对象ID (可能是 TaskId, BadgeId etc - BIGINT)',
  `ReferenceType` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '关联对象类型 (Task, Badge, etc)',
  `IsRead` tinyint(1) NOT NULL DEFAULT 0,
  `CreationTimestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
  `ReadTimestamp` datetime(0) NULL DEFAULT NULL,
  PRIMARY KEY (`NotificationId`) USING BTREE,
  INDEX `idx_notifications_user_read_time`(`UserId`, `IsRead`, `CreationTimestamp`) USING BTREE,
  CONSTRAINT `FK_Notifications_User` FOREIGN KEY (`UserId`) REFERENCES `users` (`Id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '通知表 (BIGINT PK)' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of notifications
-- ----------------------------

-- ----------------------------
-- Table structure for pdcaplans
-- ----------------------------
DROP TABLE IF EXISTS `pdcaplans`;
CREATE TABLE `pdcaplans`  (
  `PdcaPlanId` bigint NOT NULL AUTO_INCREMENT COMMENT 'PDCA计划ID (BIGINT)',
  `Title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '计划标题',
  `Type` int NOT NULL DEFAULT 0 COMMENT '类型：0硬件，1软件，2流程',
  `PlanStartDate` datetime(0) NULL DEFAULT NULL COMMENT '计划开始日期',
  `PlanEndDate` datetime(0) NULL DEFAULT NULL COMMENT '计划结束日期',
  `ActualEndDate` datetime(0) NULL DEFAULT NULL COMMENT '实际结束日期',
  `Status` int NOT NULL DEFAULT 0 COMMENT '状态：0计划，1进行中，2已完成，3已取消',
  `CompletionRate` decimal(5, 2) NOT NULL DEFAULT 0.00 COMMENT '完成率',
  `ResponsiblePersonId` int NOT NULL COMMENT '负责人ID (关联 users.Id - INT)',
  `Notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '备注',
  `CreationTimestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `LastUpdatedTimestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`PdcaPlanId`) USING BTREE,
  INDEX `idx_pdcaplans_responsible_person`(`ResponsiblePersonId`) USING BTREE,
  CONSTRAINT `FK_PdcaPlans_ResponsibleUser` FOREIGN KEY (`ResponsiblePersonId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'PDCA计划表 (V2 - BIGINT PK)' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of pdcaplans
-- ----------------------------

-- ----------------------------
-- Table structure for periodictaskschedules
-- ----------------------------
DROP TABLE IF EXISTS `periodictaskschedules`;
CREATE TABLE `periodictaskschedules`  (
  `PeriodicTaskScheduleId` bigint NOT NULL AUTO_INCREMENT COMMENT '周期性计划ID (BIGINT)',
  `TemplateTaskId` bigint NULL DEFAULT NULL COMMENT '用于生成任务的模板任务ID',
  `CreatorUserId` int NOT NULL COMMENT '创建者用户ID (关联 users.Id - INT)',
  `Name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '计划名称',
  `Description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '计划描述',
  `StartDate` datetime(0) NOT NULL COMMENT '计划开始生效时间',
  `RecurrenceRule` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '重复规则 (如 RRule 格式)',
  `DefaultAssigneeUserId` int NULL DEFAULT NULL COMMENT '默认负责人ID (关联 users.Id - INT)',
  `DefaultPriority` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '默认优先级',
  `DefaultPoints` int NOT NULL DEFAULT 0 COMMENT '默认积分',
  `EndConditionType` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'Never' COMMENT '结束条件类型 (Never, EndDate, Occurrences)',
  `EndConditionValue` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '结束条件值 (日期或次数)',
  `Status` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'Active' COMMENT '状态 (Active, Paused, Completed)',
  `LastGeneratedTimestamp` datetime(0) NULL DEFAULT NULL COMMENT '上次成功生成任务的时间',
  `NextGenerationTimestamp` datetime(0) NULL DEFAULT NULL COMMENT '下次计划生成任务的时间',
  `CreationTimestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
  `LastUpdatedTimestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`PeriodicTaskScheduleId`) USING BTREE,
  INDEX `idx_periodictaskschedules_nextgen_status`(`Status`, `NextGenerationTimestamp`) USING BTREE,
  INDEX `idx_periodictaskschedules_creator`(`CreatorUserId`) USING BTREE,
  INDEX `idx_periodictaskschedules_template_task`(`TemplateTaskId`) USING BTREE,
  INDEX `idx_periodictaskschedules_default_assignee`(`DefaultAssigneeUserId`) USING BTREE,
  CONSTRAINT `FK_PeriodicTaskSchedules_CreatorUser` FOREIGN KEY (`CreatorUserId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE CASCADE,
  CONSTRAINT `FK_PeriodicTaskSchedules_DefaultAssigneeUser` FOREIGN KEY (`DefaultAssigneeUserId`) REFERENCES `users` (`Id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `FK_PeriodicTaskSchedules_TemplateTask` FOREIGN KEY (`TemplateTaskId`) REFERENCES `tasks` (`TaskId`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '周期性任务计划表 (V2 - BIGINT PK)' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of periodictaskschedules
-- ----------------------------

-- ----------------------------
-- Table structure for personnel
-- ----------------------------
DROP TABLE IF EXISTS `personnel`;
CREATE TABLE `personnel`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `position` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `contact` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `department_id` int NULL DEFAULT NULL,
  `employee_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `created_at` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0),
  `updated_at` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `department_id`(`department_id`) USING BTREE,
  CONSTRAINT `personnel_ibfk_1` FOREIGN KEY (`department_id`) REFERENCES `departments` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 20 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of personnel
-- ----------------------------
INSERT INTO `personnel` VALUES (2, '方平', '管理员', 'TP2023102136', 1, 'TP2023102136', '2025-04-06 02:01:23', '2025-04-07 23:55:40');
INSERT INTO `personnel` VALUES (4, '孙长喜', '制芯车间主任', 'TP2023022309', 27, 'TP2023022309', '2025-04-07 23:46:58', '2025-04-07 23:47:05');
INSERT INTO `personnel` VALUES (5, '聂兆伟', '铸造车间主任', 'TP2022110797', 27, 'TP2022110797', '2025-04-07 23:49:23', '2025-04-07 23:49:23');
INSERT INTO `personnel` VALUES (6, '闫远远', '后处理车间主任', 'TP2023051019', 7, 'TP2023051019', '2025-04-07 23:50:03', '2025-04-07 23:50:03');
INSERT INTO `personnel` VALUES (7, '胡良浩', '后处理车间主任', 'TP2021081447', 7, 'TP2021081447', '2025-04-07 23:51:18', '2025-04-07 23:51:18');
INSERT INTO `personnel` VALUES (8, '温锋锋', '经理助理', 'TP2021032263', 6, 'TP2021032263', '2025-04-07 23:52:00', '2025-04-07 23:52:00');
INSERT INTO `personnel` VALUES (9, '孟令坤', '经理助理', 'TP2021030834', 6, 'TP2021030834', '2025-04-07 23:52:55', '2025-04-07 23:52:55');
INSERT INTO `personnel` VALUES (10, '苏少华', '机组车间主任', 'TP2021031025', 6, 'TP2021031025', '2025-04-07 23:53:37', '2025-04-07 23:53:37');
INSERT INTO `personnel` VALUES (11, '王伟东', '机组车间主任', 'TP2021101509', 6, 'TP2021101509', '2025-04-07 23:53:59', '2025-04-07 23:53:59');
INSERT INTO `personnel` VALUES (12, '耿化幸', '制造一部经理', 'TP2022082343', 27, 'TP2022082343', '2025-04-07 23:56:19', '2025-04-07 23:56:19');
INSERT INTO `personnel` VALUES (13, '夏磊', '制造二部经理', 'TP2021110841', 7, 'TP2021110841', '2025-04-07 23:56:46', '2025-04-07 23:56:46');
INSERT INTO `personnel` VALUES (14, '何志雄', '制造三部经理', 'TP2009079619', 6, 'TP2009079619', '2025-04-07 23:57:15', '2025-04-07 23:57:15');
INSERT INTO `personnel` VALUES (15, '杨小鹏', '质量部经理', 'TP2022092797', 4, 'TP2022092797', '2025-04-07 23:57:54', '2025-04-07 23:57:54');
INSERT INTO `personnel` VALUES (16, '王鹏', '动力一部经理', 'TP2013040413', 5, 'TP2013040413', '2025-04-07 23:58:33', '2025-04-07 23:59:16');
INSERT INTO `personnel` VALUES (17, '王伟', '动力二部经理', 'TP2020110950', 5, 'TP2020110950', '2025-04-07 23:59:04', '2025-04-07 23:59:04');
INSERT INTO `personnel` VALUES (18, '晋明', '熔炼车间主任', 'TP2023080550', 27, 'TP2023080550', '2025-04-08 00:01:49', '2025-04-08 00:01:49');
INSERT INTO `personnel` VALUES (19, '陈光进', '质量科长', 'TP2024022790', 4, 'TP2024022790', '2025-04-08 00:04:51', '2025-04-08 00:04:51');

-- ----------------------------
-- Table structure for point_leaderboard
-- ----------------------------
DROP TABLE IF EXISTS `point_leaderboard`;
CREATE TABLE `point_leaderboard`  (
  `LeaderboardEntryId` bigint NOT NULL AUTO_INCREMENT COMMENT '排行榜条目ID (BIGINT)',
  `UserId` int NOT NULL COMMENT '用户ID (关联 users.Id - INT)',
  `LeaderboardType` int NOT NULL COMMENT '排行榜类型 (e.g., 1=Weekly, 2=Monthly, 3=AllTime)',
  `LeaderboardPeriod` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '排行榜周期标识 (e.g., 2024-W20, 2024-05, AllTime)',
  `Points` int NOT NULL DEFAULT 0,
  `Rank` int NOT NULL,
  `CreationTimestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
  `LastUpdatedTimestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`LeaderboardEntryId`) USING BTREE,
  UNIQUE INDEX `idx_leaderboard_unique`(`UserId`, `LeaderboardType`, `LeaderboardPeriod`) USING BTREE,
  INDEX `idx_leaderboard_type_period_rank`(`LeaderboardType`, `LeaderboardPeriod`, `Rank`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '积分排行榜 (BIGINT PK)' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of point_leaderboard
-- ----------------------------

-- ----------------------------
-- Table structure for purchaseitems
-- ----------------------------
DROP TABLE IF EXISTS `purchaseitems`;
CREATE TABLE `purchaseitems`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `PurchaseOrderId` int NOT NULL COMMENT '采购订单ID',
  `ItemName` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '项目名称',
  `ItemCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '项目编码',
  `Specification` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '规格',
  `AssetTypeId` int NULL DEFAULT NULL COMMENT '资产类型ID',
  `UnitPrice` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '单价',
  `Quantity` int NOT NULL DEFAULT 1 COMMENT '数量',
  `TotalPrice` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '总价',
  `Notes` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `CreatedAt` datetime(0) NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime(0) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`) USING BTREE,
  INDEX `IX_PurchaseItems_PurchaseOrderId`(`PurchaseOrderId`) USING BTREE,
  INDEX `IX_PurchaseItems_AssetTypeId`(`AssetTypeId`) USING BTREE,
  CONSTRAINT `FK_PurchaseItems_AssetTypes_AssetTypeId` FOREIGN KEY (`AssetTypeId`) REFERENCES `assettypes` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_PurchaseItems_PurchaseOrders_PurchaseOrderId` FOREIGN KEY (`PurchaseOrderId`) REFERENCES `purchaseorders` (`Id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '采购项目表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of purchaseitems
-- ----------------------------

-- ----------------------------
-- Table structure for purchaseorders
-- ----------------------------
DROP TABLE IF EXISTS `purchaseorders`;
CREATE TABLE `purchaseorders`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `OrderCode` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '采购单号(PO-年月日-时分秒)',
  `Title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '标题',
  `Description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '描述',
  `SupplierId` int NOT NULL COMMENT '供应商ID',
  `Status` int NOT NULL DEFAULT 0 COMMENT '状态：0草稿，1待审批，2已审批，3已发出，4已收货，5已完成，6已取消',
  `EstimatedDeliveryDate` datetime(0) NULL DEFAULT NULL COMMENT '预计交付日期',
  `ActualDeliveryDate` datetime(0) NULL DEFAULT NULL COMMENT '实际交付日期',
  `ApplicantId` int NOT NULL COMMENT '申请人ID',
  `ApplicationTime` datetime(0) NOT NULL COMMENT '申请时间',
  `ApproverId` int NULL DEFAULT NULL COMMENT '审批人ID',
  `ApprovalTime` datetime(0) NULL DEFAULT NULL COMMENT '审批时间',
  `TotalAmount` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '总金额',
  `Notes` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `CreatedAt` datetime(0) NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime(0) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE INDEX `IX_PurchaseOrders_OrderCode`(`OrderCode`) USING BTREE,
  INDEX `IX_PurchaseOrders_SupplierId`(`SupplierId`) USING BTREE,
  INDEX `IX_PurchaseOrders_ApplicantId`(`ApplicantId`) USING BTREE,
  INDEX `IX_PurchaseOrders_ApproverId`(`ApproverId`) USING BTREE,
  CONSTRAINT `FK_PurchaseOrders_Suppliers_SupplierId` FOREIGN KEY (`SupplierId`) REFERENCES `suppliers` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_PurchaseOrders_Users_ApplicantId` FOREIGN KEY (`ApplicantId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_PurchaseOrders_Users_ApproverId` FOREIGN KEY (`ApproverId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '采购订单表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of purchaseorders
-- ----------------------------

-- ----------------------------
-- Table structure for quicknotes
-- ----------------------------
DROP TABLE IF EXISTS `quicknotes`;
CREATE TABLE `quicknotes`  (
  `QuickNoteId` bigint NOT NULL AUTO_INCREMENT COMMENT '随手记ID (BIGINT)',
  `UserId` int NOT NULL COMMENT '记录用户ID (关联 users.Id - INT)',
  `Content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '记录内容',
  `CreationTimestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `Status` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'Active' COMMENT '状态 (Active, Converted)',
  `ConvertedTaskId` bigint NULL DEFAULT NULL COMMENT '转换后的任务ID',
  PRIMARY KEY (`QuickNoteId`) USING BTREE,
  INDEX `idx_quicknotes_user_status`(`UserId`, `Status`) USING BTREE,
  INDEX `idx_quicknotes_converted_task`(`ConvertedTaskId`) USING BTREE,
  CONSTRAINT `FK_QuickNotes_ConvertedTask` FOREIGN KEY (`ConvertedTaskId`) REFERENCES `tasks` (`TaskId`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `FK_QuickNotes_User` FOREIGN KEY (`UserId`) REFERENCES `users` (`Id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '随手记快速记录表 (V2 - BIGINT PK)' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of quicknotes
-- ----------------------------

-- ----------------------------
-- Table structure for refreshtokens
-- ----------------------------
DROP TABLE IF EXISTS `refreshtokens`;
CREATE TABLE `refreshtokens`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `UserId` int NOT NULL COMMENT '用户ID',
  `Token` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '令牌',
  `JwtId` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'JWT ID',
  `IsUsed` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已使用',
  `IsRevoked` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已撤销',
  `AddedDate` datetime(0) NOT NULL COMMENT '添加日期',
  `ExpiryDate` datetime(0) NOT NULL COMMENT '过期日期',
  PRIMARY KEY (`Id`) USING BTREE,
  INDEX `IX_RefreshTokens_UserId`(`UserId`) USING BTREE,
  CONSTRAINT `FK_RefreshTokens_Users_UserId` FOREIGN KEY (`UserId`) REFERENCES `users` (`Id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '刷新令牌表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of refreshtokens
-- ----------------------------

-- ----------------------------
-- Table structure for returntofactories
-- ----------------------------
DROP TABLE IF EXISTS `returntofactories`;
CREATE TABLE `returntofactories`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `Code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '返厂单号',
  `AssetId` int NOT NULL COMMENT '资产ID',
  `FaultRecordId` int NOT NULL COMMENT '故障记录ID',
  `SupplierId` int NOT NULL COMMENT '供应商ID',
  `Status` int NOT NULL DEFAULT 0 COMMENT '状态：0待送出，1已送出，2维修中，3已返回，4维修失败',
  `SenderId` int NOT NULL COMMENT '送出人ID',
  `SendTime` datetime(0) NULL DEFAULT NULL COMMENT '送出时间',
  `EstimatedReturnTime` datetime(0) NULL DEFAULT NULL COMMENT '预计返回时间',
  `ActualReturnTime` datetime(0) NULL DEFAULT NULL COMMENT '实际返回时间',
  `RepairResult` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '维修结果',
  `RepairCost` decimal(18, 2) NULL DEFAULT NULL COMMENT '维修费用',
  `InWarranty` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否在保修期内',
  `Notes` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `CreatedAt` datetime(0) NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime(0) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE INDEX `IX_ReturnToFactories_Code`(`Code`) USING BTREE,
  INDEX `IX_ReturnToFactories_AssetId`(`AssetId`) USING BTREE,
  INDEX `IX_ReturnToFactories_FaultRecordId`(`FaultRecordId`) USING BTREE,
  INDEX `IX_ReturnToFactories_SupplierId`(`SupplierId`) USING BTREE,
  INDEX `IX_ReturnToFactories_SenderId`(`SenderId`) USING BTREE,
  CONSTRAINT `FK_ReturnToFactories_Assets_AssetId` FOREIGN KEY (`AssetId`) REFERENCES `assets` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_ReturnToFactories_FaultRecords_FaultRecordId` FOREIGN KEY (`FaultRecordId`) REFERENCES `faultrecords` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_ReturnToFactories_Suppliers_SupplierId` FOREIGN KEY (`SupplierId`) REFERENCES `suppliers` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_ReturnToFactories_Users_SenderId` FOREIGN KEY (`SenderId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '返厂维修表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of returntofactories
-- ----------------------------

-- ----------------------------
-- Table structure for rolemenus
-- ----------------------------
DROP TABLE IF EXISTS `rolemenus`;
CREATE TABLE `rolemenus`  (
  `RoleId` int NOT NULL COMMENT '角色ID',
  `MenuId` int NOT NULL COMMENT '菜单ID',
  `CreatedAt` datetime(0) NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`RoleId`, `MenuId`) USING BTREE,
  INDEX `IX_RoleMenus_MenuId`(`MenuId`) USING BTREE,
  CONSTRAINT `FK_RoleMenus_Menus_MenuId` FOREIGN KEY (`MenuId`) REFERENCES `menus` (`Id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `FK_RoleMenus_Roles_RoleId` FOREIGN KEY (`RoleId`) REFERENCES `roles` (`Id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '角色菜单关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of rolemenus
-- ----------------------------
INSERT INTO `rolemenus` VALUES (1, 1, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (1, 2, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (1, 3, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (1, 4, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (1, 5, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (1, 6, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (1, 7, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (1, 8, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (1, 9, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (1, 10, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (1, 11, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (1, 12, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (1, 13, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (1, 14, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (1, 15, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (1, 16, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (1, 17, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (1, 18, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (1, 19, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (1, 20, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (1, 21, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (1, 22, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (1, 23, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (1, 24, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (1, 25, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (1, 26, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (2, 1, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (2, 2, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (2, 3, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (2, 4, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (2, 5, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (2, 7, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (2, 8, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (2, 9, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (2, 10, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (2, 11, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (2, 12, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (2, 13, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (2, 14, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (2, 15, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (2, 16, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (2, 17, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (2, 18, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (2, 24, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (2, 25, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (2, 26, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (3, 1, '2025-03-23 20:45:09');
INSERT INTO `rolemenus` VALUES (3, 2, '2025-03-23 20:45:09');
INSERT INTO `rolemenus` VALUES (3, 8, '2025-03-23 20:45:09');
INSERT INTO `rolemenus` VALUES (3, 9, '2025-03-23 20:45:09');
INSERT INTO `rolemenus` VALUES (3, 10, '2025-03-23 20:45:09');
INSERT INTO `rolemenus` VALUES (3, 24, '2025-03-23 20:45:09');
INSERT INTO `rolemenus` VALUES (4, 1, '2025-03-23 20:45:09');
INSERT INTO `rolemenus` VALUES (4, 2, '2025-03-23 20:45:09');
INSERT INTO `rolemenus` VALUES (4, 10, '2025-03-23 20:45:09');
INSERT INTO `rolemenus` VALUES (4, 24, '2025-03-23 20:45:09');
INSERT INTO `rolemenus` VALUES (5, 1, '2025-03-23 20:45:09');
INSERT INTO `rolemenus` VALUES (5, 3, '2025-03-23 20:45:09');
INSERT INTO `rolemenus` VALUES (5, 11, '2025-03-23 20:45:09');
INSERT INTO `rolemenus` VALUES (5, 12, '2025-03-23 20:45:09');
INSERT INTO `rolemenus` VALUES (5, 13, '2025-03-23 20:45:09');
INSERT INTO `rolemenus` VALUES (5, 24, '2025-03-23 20:45:09');
INSERT INTO `rolemenus` VALUES (6, 1, '2025-03-23 20:45:09');
INSERT INTO `rolemenus` VALUES (6, 4, '2025-03-23 20:45:09');
INSERT INTO `rolemenus` VALUES (6, 14, '2025-03-23 20:45:09');
INSERT INTO `rolemenus` VALUES (6, 24, '2025-03-23 20:45:09');
INSERT INTO `rolemenus` VALUES (7, 1, '2025-03-23 20:45:09');
INSERT INTO `rolemenus` VALUES (7, 8, '2025-03-23 20:45:09');
INSERT INTO `rolemenus` VALUES (7, 11, '2025-03-23 20:45:09');
INSERT INTO `rolemenus` VALUES (7, 24, '2025-03-23 20:45:09');

-- ----------------------------
-- Table structure for roles
-- ----------------------------
DROP TABLE IF EXISTS `roles`;
CREATE TABLE `roles`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `Name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '角色名称',
  `Code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '角色编码',
  `Description` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '角色描述',
  `IsActive` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否激活',
  `CreatedAt` datetime(0) NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime(0) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE INDEX `IX_Roles_Code`(`Code`) USING BTREE,
  UNIQUE INDEX `IX_Roles_Name`(`Name`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '角色表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of roles
-- ----------------------------
INSERT INTO `roles` VALUES (1, '超级管理员', 'SUPER_ADMIN', '系统超级管理员，拥有所有权限', 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `roles` VALUES (2, 'IT管理员', 'IT_ADMIN', 'IT部门管理员，管理IT资产和系统配置', 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `roles` VALUES (3, '资产管理员', 'ASSET_ADMIN', '负责资产的录入、调拨和报废管理', 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `roles` VALUES (4, '位置管理员', 'LOCATION_ADMIN', '负责管理位置和资产位置分配', 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `roles` VALUES (5, '故障管理员', 'FAULT_ADMIN', '负责故障的处理和维修管理', 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `roles` VALUES (6, '维修人员', 'MAINTENANCE_STAFF', '执行设备维修和保养任务', 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `roles` VALUES (7, '普通用户', 'NORMAL_USER', '普通用户，可以查看和使用设备', 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');

-- ----------------------------
-- Table structure for suppliers
-- ----------------------------
DROP TABLE IF EXISTS `suppliers`;
CREATE TABLE `suppliers`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `Name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '供应商名称',
  `Code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '供应商编码',
  `ContactPerson` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '联系人',
  `ContactPhone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '联系电话',
  `ContactEmail` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '联系邮箱',
  `Address` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '地址',
  `Type` int NOT NULL DEFAULT 0 COMMENT '类型：0硬件，1软件，2服务',
  `Notes` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `IsActive` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否激活',
  `CreatedAt` datetime(0) NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime(0) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE INDEX `IX_Suppliers_Code`(`Code`) USING BTREE,
  UNIQUE INDEX `IX_Suppliers_Name`(`Name`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '供应商表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of suppliers
-- ----------------------------
INSERT INTO `suppliers` VALUES (1, '联想集团', 'LENOVO', '王经理', '13566778899', '<EMAIL>', '北京市海淀区联想大厦', 0, '电脑设备主要供应商', 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `suppliers` VALUES (2, '戴尔科技', 'DELL', '李经理', '13677889900', '<EMAIL>', '上海市浦东新区张江高科技园区', 0, '服务器主要供应商', 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `suppliers` VALUES (3, '华为技术', 'HUAWEI', '张经理', '13788990011', '<EMAIL>', '深圳市龙岗区华为基地', 0, '网络设备主要供应商', 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `suppliers` VALUES (4, '西门子工业', 'SIEMENS', '赵经理', '13899001122', '<EMAIL>', '北京市朝阳区西门子大厦', 0, '工控机主要供应商', 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `suppliers` VALUES (5, '霍尼韦尔', 'HONEYWELL', '钱经理', '13900112233', '<EMAIL>', '上海市长宁区霍尼韦尔大厦', 0, '扫描设备主要供应商', 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `suppliers` VALUES (6, '佳能', 'CANON', '孙经理', '13911223344', '<EMAIL>', '北京市朝阳区佳能大厦', 0, '打印设备主要供应商', 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `suppliers` VALUES (7, '微软', 'MICROSOFT', '周经理', '13922334455', '<EMAIL>', '北京市海淀区微软大厦', 1, '软件主要供应商', 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `suppliers` VALUES (8, '东方通信', 'EASTCOM', '吴经理', '13933445566', '<EMAIL>', '杭州市滨江区东方通信大厦', 0, '通信设备供应商', 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `suppliers` VALUES (9, '普联技术', 'TP-LINK', '郑经理', '13944556677', '<EMAIL>', '深圳市南山区普联科技大厦', 0, '网络设备供应商', 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `suppliers` VALUES (10, '科大讯飞', 'IFLYTEK', '冯经理', '13955667788', '<EMAIL>', '合肥市高新区科大讯飞大厦', 1, '语音识别技术供应商', 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');

-- ----------------------------
-- Table structure for taskassignees
-- ----------------------------
DROP TABLE IF EXISTS `taskassignees`;
CREATE TABLE `taskassignees`  (
  `TaskAssigneeId` bigint NOT NULL AUTO_INCREMENT COMMENT '分配ID (BIGINT)',
  `TaskId` bigint NOT NULL COMMENT '任务ID',
  `UserId` int NOT NULL COMMENT '用户ID (关联 users.Id - INT)',
  `AssignmentType` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'Responsible' COMMENT '分配类型 (Responsible, Participant)',
  `AssignedByUserId` int NOT NULL COMMENT '分配人用户ID (关联 users.Id - INT)',
  `AssignmentTimestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '分配时间',
  PRIMARY KEY (`TaskAssigneeId`) USING BTREE,
  UNIQUE INDEX `idx_taskassignees_task_user_type`(`TaskId`, `UserId`, `AssignmentType`) USING BTREE,
  INDEX `idx_taskassignees_user`(`UserId`) USING BTREE,
  INDEX `idx_taskassignees_assignedby`(`AssignedByUserId`) USING BTREE,
  CONSTRAINT `FK_TaskAssignees_AssignedByUser` FOREIGN KEY (`AssignedByUserId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE CASCADE,
  CONSTRAINT `FK_TaskAssignees_Task` FOREIGN KEY (`TaskId`) REFERENCES `tasks` (`TaskId`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `FK_TaskAssignees_User` FOREIGN KEY (`UserId`) REFERENCES `users` (`Id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '任务多负责人/参与者表 (V2 - BIGINT PK)' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of taskassignees
-- ----------------------------

-- ----------------------------
-- Table structure for taskhistory
-- ----------------------------
DROP TABLE IF EXISTS `taskhistory`;
CREATE TABLE `taskhistory`  (
  `TaskHistoryId` bigint NOT NULL AUTO_INCREMENT COMMENT '历史记录ID (BIGINT)',
  `TaskId` bigint NOT NULL COMMENT '关联的任务ID',
  `UserId` int NULL DEFAULT NULL COMMENT '操作用户ID (系统操作可为NULL, 关联 users.Id - INT)',
  `Timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '操作时间',
  `ActionType` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '操作类型',
  `FieldName` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '变更的字段名',
  `OldValue` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '旧值 (建议JSON)',
  `NewValue` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '新值 (建议JSON)',
  `CommentId` bigint NULL DEFAULT NULL COMMENT '关联的评论ID',
  `AttachmentId` bigint NULL DEFAULT NULL COMMENT '关联的附件ID',
  `Description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '操作的文字描述',
  PRIMARY KEY (`TaskHistoryId`) USING BTREE,
  INDEX `idx_taskhistory_task_time`(`TaskId`, `Timestamp`) USING BTREE,
  INDEX `idx_taskhistory_user`(`UserId`) USING BTREE,
  INDEX `idx_taskhistory_action`(`ActionType`) USING BTREE,
  INDEX `idx_taskhistory_comment`(`CommentId`) USING BTREE,
  INDEX `idx_taskhistory_attachment`(`AttachmentId`) USING BTREE,
  CONSTRAINT `FK_TaskHistory_Attachment` FOREIGN KEY (`AttachmentId`) REFERENCES `attachments` (`AttachmentId`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `FK_TaskHistory_Comment` FOREIGN KEY (`CommentId`) REFERENCES `comments` (`CommentId`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `FK_TaskHistory_Task` FOREIGN KEY (`TaskId`) REFERENCES `tasks` (`TaskId`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `FK_TaskHistory_User` FOREIGN KEY (`UserId`) REFERENCES `users` (`Id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '任务变更历史记录表 (V2 - BIGINT PK)' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of taskhistory
-- ----------------------------

-- ----------------------------
-- Table structure for tasks
-- ----------------------------
DROP TABLE IF EXISTS `tasks`;
CREATE TABLE `tasks`  (
  `TaskId` bigint NOT NULL AUTO_INCREMENT COMMENT '任务主键ID (BIGINT)',
  `ProjectId` bigint NULL DEFAULT NULL COMMENT '所属项目ID (如果需要，也应为 BIGINT)',
  `ParentTaskId` bigint NULL DEFAULT NULL COMMENT '父任务ID (指向自身 TaskId)',
  `CreatorUserId` int NOT NULL COMMENT '创建者用户ID (关联 users.Id - INT)',
  `AssigneeUserId` int NULL DEFAULT NULL COMMENT '负责人用户ID (关联 users.Id - INT)',
  `AssetId` int NULL DEFAULT NULL COMMENT '关联资产ID (关联 assets.Id - INT)',
  `LocationId` int NULL DEFAULT NULL COMMENT '关联位置ID (关联 locations.Id - INT)',
  `Name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '任务名称',
  `Description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '任务描述 (支持Markdown或富文本)',
  `Status` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'Todo' COMMENT '任务状态 (使用字符串)',
  `Priority` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'Medium' COMMENT '优先级 (使用字符串)',
  `TaskType` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'Normal' COMMENT '任务类型 (Normal, Periodic, PDCA)',
  `PlanStartDate` datetime(0) NULL DEFAULT NULL COMMENT '计划开始时间',
  `PlanEndDate` datetime(0) NULL DEFAULT NULL COMMENT '计划结束时间 (截止时间)',
  `ActualStartDate` datetime(0) NULL DEFAULT NULL COMMENT '实际开始时间',
  `ActualEndDate` datetime(0) NULL DEFAULT NULL COMMENT '实际完成时间',
  `CreationTimestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `LastUpdatedTimestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '最后更新时间',
  `IsOverdueAcknowledged` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逾期是否已知晓/处理',
  `PDCAStage` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'PDCA阶段 (Plan, Do, Check, Act)',
  `PreviousInstanceTaskId` bigint NULL DEFAULT NULL COMMENT '周期性任务的前一个实例ID (指向自身 TaskId)',
  `PeriodicTaskScheduleId` bigint NULL DEFAULT NULL COMMENT '关联的周期性任务计划ID',
  `Progress` int NOT NULL DEFAULT 0 COMMENT '任务进度百分比 (0-100)',
  `Points` int NOT NULL DEFAULT 0 COMMENT '完成任务可获得的基础积分/XP',
  PRIMARY KEY (`TaskId`) USING BTREE,
  INDEX `idx_tasks_status`(`Status`) USING BTREE,
  INDEX `idx_tasks_assignee`(`AssigneeUserId`) USING BTREE,
  INDEX `idx_tasks_creator`(`CreatorUserId`) USING BTREE,
  INDEX `idx_tasks_parent`(`ParentTaskId`) USING BTREE,
  INDEX `idx_tasks_plan_end_date`(`PlanEndDate`) USING BTREE,
  INDEX `idx_tasks_type`(`TaskType`) USING BTREE,
  INDEX `idx_tasks_asset`(`AssetId`) USING BTREE,
  INDEX `idx_tasks_location`(`LocationId`) USING BTREE,
  INDEX `idx_tasks_periodic_schedule`(`PeriodicTaskScheduleId`) USING BTREE,
  INDEX `FK_Tasks_PreviousInstanceTask`(`PreviousInstanceTaskId`) USING BTREE,
  CONSTRAINT `FK_Tasks_Asset` FOREIGN KEY (`AssetId`) REFERENCES `assets` (`Id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `FK_Tasks_AssigneeUser` FOREIGN KEY (`AssigneeUserId`) REFERENCES `users` (`Id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `FK_Tasks_CreatorUser` FOREIGN KEY (`CreatorUserId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE CASCADE,
  CONSTRAINT `FK_Tasks_Location` FOREIGN KEY (`LocationId`) REFERENCES `locations` (`Id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `FK_Tasks_ParentTask` FOREIGN KEY (`ParentTaskId`) REFERENCES `tasks` (`TaskId`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `FK_Tasks_PeriodicTaskSchedule` FOREIGN KEY (`PeriodicTaskScheduleId`) REFERENCES `periodictaskschedules` (`PeriodicTaskScheduleId`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `FK_Tasks_PreviousInstanceTask` FOREIGN KEY (`PreviousInstanceTaskId`) REFERENCES `tasks` (`TaskId`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '任务主表 (V2 - BIGINT PK)' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of tasks
-- ----------------------------

-- ----------------------------
-- Table structure for user_actions
-- ----------------------------
DROP TABLE IF EXISTS `user_actions`;
CREATE TABLE `user_actions`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `action` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '行为类型',
  `reference_id` int NULL DEFAULT NULL COMMENT '关联ID',
  `reference_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '关联类型',
  `points` int NULL DEFAULT 0 COMMENT '获得积分',
  `action_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_actions_user`(`user_id`) USING BTREE,
  INDEX `idx_user_actions_time`(`action_time`) USING BTREE,
  CONSTRAINT `fk_user_actions_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`Id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of user_actions
-- ----------------------------

-- ----------------------------
-- Table structure for user_items
-- ----------------------------
DROP TABLE IF EXISTS `user_items`;
CREATE TABLE `user_items`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `description` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `type` int NOT NULL,
  `level` int NOT NULL DEFAULT 1,
  `power` int NOT NULL DEFAULT 0,
  `quantity` int NOT NULL DEFAULT 1,
  `is_used` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
  `updated_at` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_items_user`(`user_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of user_items
-- ----------------------------

-- ----------------------------
-- Table structure for userroles
-- ----------------------------
DROP TABLE IF EXISTS `userroles`;
CREATE TABLE `userroles`  (
  `UserId` int NOT NULL COMMENT '用户ID',
  `RoleId` int NOT NULL COMMENT '角色ID',
  `CreatedAt` datetime(0) NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`UserId`, `RoleId`) USING BTREE,
  INDEX `IX_UserRoles_RoleId`(`RoleId`) USING BTREE,
  CONSTRAINT `FK_UserRoles_Roles_RoleId` FOREIGN KEY (`RoleId`) REFERENCES `roles` (`Id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `FK_UserRoles_Users_UserId` FOREIGN KEY (`UserId`) REFERENCES `users` (`Id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户角色关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of userroles
-- ----------------------------
INSERT INTO `userroles` VALUES (1, 1, '2025-03-23 20:45:08');
INSERT INTO `userroles` VALUES (2, 2, '2025-03-23 20:45:08');
INSERT INTO `userroles` VALUES (3, 3, '2025-03-23 20:45:08');
INSERT INTO `userroles` VALUES (4, 4, '2025-03-23 20:45:08');
INSERT INTO `userroles` VALUES (5, 5, '2025-03-23 20:45:08');
INSERT INTO `userroles` VALUES (6, 6, '2025-03-23 20:45:08');
INSERT INTO `userroles` VALUES (7, 7, '2025-03-23 20:45:08');
INSERT INTO `userroles` VALUES (8, 7, '2025-03-23 20:45:08');
INSERT INTO `userroles` VALUES (9, 7, '2025-03-23 20:45:08');

-- ----------------------------
-- Table structure for users
-- ----------------------------
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `Username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户名',
  `PasswordHash` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '密码哈希',
  `SecurityStamp` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '安全戳',
  `Name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '姓名',
  `Email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '邮箱',
  `Mobile` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '手机号',
  `DepartmentId` int NULL DEFAULT NULL COMMENT '部门ID',
  `Position` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '职位',
  `Gender` int NOT NULL DEFAULT 0 COMMENT '性别：0未知，1男，2女',
  `DefaultRoleId` int NULL DEFAULT NULL COMMENT '默认角色ID',
  `IsActive` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否激活',
  `CreatedAt` datetime(0) NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime(0) NOT NULL COMMENT '更新时间',
  `LastLoginAt` datetime(0) NULL DEFAULT NULL COMMENT '最后登录时间',
  `Avatar` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '头像',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE INDEX `IX_Users_Username`(`Username`) USING BTREE,
  UNIQUE INDEX `IX_Users_Email`(`Email`) USING BTREE,
  INDEX `IX_Users_DepartmentId`(`DepartmentId`) USING BTREE,
  INDEX `IX_Users_DefaultRoleId`(`DefaultRoleId`) USING BTREE,
  CONSTRAINT `FK_Users_Departments_DepartmentId` FOREIGN KEY (`DepartmentId`) REFERENCES `departments` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_Users_Roles_DefaultRoleId` FOREIGN KEY (`DefaultRoleId`) REFERENCES `roles` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 22 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of users
-- ----------------------------
INSERT INTO `users` VALUES (1, 'admin', 'AQAAAAIAAYagAAAAECkIJmNaLvk9o9JVh7RoQTI9VvL6B8U3uLQkzQvCeD2qcHKJWmjGsdzIgFg3Mg==', 'E89F4B8C79F14A798D97D57E97D45D46', '系统管理员', '<EMAIL>', '13800000000', 1, '管理员', 0, 1, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08', NULL, NULL);
INSERT INTO `users` VALUES (2, 'itadmin', 'AQAAAAIAAYagAAAAECkIJmNaLvk9o9JVh7RoQTI9VvL6B8U3uLQkzQvCeD2qcHKJWmjGsdzIgFg3Mg==', 'E89F4B8C79F14A798D97D57E97D45D46', 'IT管理员', '<EMAIL>', '13800000001', 2, '管理员', 1, 2, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08', NULL, NULL);
INSERT INTO `users` VALUES (3, 'assetmgr', 'AQAAAAIAAYagAAAAECkIJmNaLvk9o9JVh7RoQTI9VvL6B8U3uLQkzQvCeD2qcHKJWmjGsdzIgFg3Mg==', 'E89F4B8C79F14A798D97D57E97D45D46', '资产管理员', '<EMAIL>', '13800000002', 2, '经理', 1, 3, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08', NULL, NULL);
INSERT INTO `users` VALUES (4, 'locationmgr', 'AQAAAAIAAYagAAAAECkIJmNaLvk9o9JVh7RoQTI9VvL6B8U3uLQkzQvCeD2qcHKJWmjGsdzIgFg3Mg==', 'E89F4B8C79F14A798D97D57E97D45D46', '位置管理员', '<EMAIL>', '13800000003', 2, '经理', 1, 4, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08', NULL, NULL);
INSERT INTO `users` VALUES (5, 'faultmgr', 'AQAAAAIAAYagAAAAECkIJmNaLvk9o9JVh7RoQTI9VvL6B8U3uLQkzQvCeD2qcHKJWmjGsdzIgFg3Mg==', 'E89F4B8C79F14A798D97D57E97D45D46', '故障管理员', '<EMAIL>', '13800000004', 2, '经理', 1, 5, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08', NULL, NULL);
INSERT INTO `users` VALUES (6, 'maintenance', 'AQAAAAIAAYagAAAAECkIJmNaLvk9o9JVh7RoQTI9VvL6B8U3uLQkzQvCeD2qcHKJWmjGsdzIgFg3Mg==', 'E89F4B8C79F14A798D97D57E97D45D46', '维修人员', '<EMAIL>', '13800000005', 2, '技术员', 1, 6, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08', NULL, NULL);
INSERT INTO `users` VALUES (7, 'fangping', 'AQAAAAIAAYagAAAAECkIJmNaLvk9o9JVh7RoQTI9VvL6B8U3uLQkzQvCeD2qcHKJWmjGsdzIgFg3Mg==', 'E89F4B8C79F14A798D97D57E97D45D46', '方平', '<EMAIL>', '13900000001', 3, '职员', 1, 7, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08', NULL, NULL);
INSERT INTO `users` VALUES (8, 'chenzhenghua', 'AQAAAAIAAYagAAAAECkIJmNaLvk9o9JVh7RoQTI9VvL6B8U3uLQkzQvCeD2qcHKJWmjGsdzIgFg3Mg==', 'E89F4B8C79F14A798D97D57E97D45D46', '陈正华', '<EMAIL>', '13900000002', 4, '职员', 1, 7, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08', NULL, NULL);
INSERT INTO `users` VALUES (9, 'user3', 'AQAAAAIAAYagAAAAECkIJmNaLvk9o9JVh7RoQTI9VvL6B8U3uLQkzQvCeD2qcHKJWmjGsdzIgFg3Mg==', 'E89F4B8C79F14A798D97D57E97D45D46', '王五', '<EMAIL>', '13900000003', 5, '职员', 2, 7, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08', NULL, NULL);
INSERT INTO `users` VALUES (10, 'engineer1', 'AQAAAAIAAYagAAAAECkIJmNaLvk9o9JVh7RoQTI9VvL6B8U3uLQkzQvCeD2qcHKJWmjGsdzIgFg3Mg==', 'E89F4B8C79F14A798D97D57E97D45D46', '工程师1', '<EMAIL>', '13900000004', 6, '工程师', 1, 7, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08', NULL, NULL);
INSERT INTO `users` VALUES (11, 'M01', 'AQAAAAIAAYagAAAAECkIJmNaLvk9o9JVh7RoQTI9VvL6B8U3uLQkzQvCeD2qcHKJWmjGsdzIgFg3Mg==', 'E89F4B8C79F14A798D97D57E97D45D46', '王经理', '<EMAIL>', '13900000001', 10, '经理', 0, 7, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08', NULL, NULL);
INSERT INTO `users` VALUES (12, 'M02', 'AQAAAAIAAYagAAAAECkIJmNaLvk9o9JVh7RoQTI9VvL6B8U3uLQkzQvCeD2qcHKJWmjGsdzIgFg3Mg==', 'E89F4B8C79F14A798D97D57E97D45D46', '李经理', '<EMAIL>', '13900000002', 11, '经理', 0, 7, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08', NULL, NULL);
INSERT INTO `users` VALUES (13, 'M03', 'AQAAAAIAAYagAAAAECkIJmNaLvk9o9JVh7RoQTI9VvL6B8U3uLQkzQvCeD2qcHKJWmjGsdzIgFg3Mg==', 'E89F4B8C79F14A798D97D57E97D45D46', '张经理', '<EMAIL>', '13900000003', 12, '经理', 0, 7, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08', NULL, NULL);
INSERT INTO `users` VALUES (14, 'M04', 'AQAAAAIAAYagAAAAECkIJmNaLvk9o9JVh7RoQTI9VvL6B8U3uLQkzQvCeD2qcHKJWmjGsdzIgFg3Mg==', 'E89F4B8C79F14A798D97D57E97D45D46', '刘经理', '<EMAIL>', '13900000004', 13, '经理', 0, 7, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08', NULL, NULL);
INSERT INTO `users` VALUES (15, 'M05', 'AQAAAAIAAYagAAAAECkIJmNaLvk9o9JVh7RoQTI9VvL6B8U3uLQkzQvCeD2qcHKJWmjGsdzIgFg3Mg==', 'E89F4B8C79F14A798D97D57E97D45D46', '赵经理', '<EMAIL>', '13900000005', 14, '组长', 0, 7, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08', NULL, NULL);
INSERT INTO `users` VALUES (16, 'M06', 'AQAAAAIAAYagAAAAECkIJmNaLvk9o9JVh7RoQTI9VvL6B8U3uLQkzQvCeD2qcHKJWmjGsdzIgFg3Mg==', 'E89F4B8C79F14A798D97D57E97D45D46', '钱经理', '<EMAIL>', '13900000006', 15, '组长', 0, 7, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08', NULL, NULL);
INSERT INTO `users` VALUES (17, 'U01', 'AQAAAAIAAYagAAAAECkIJmNaLvk9o9JVh7RoQTI9VvL6B8U3uLQkzQvCeD2qcHKJWmjGsdzIgFg3Mg==', 'E89F4B8C79F14A798D97D57E97D45D46', '张操作员', '<EMAIL>', '13911111101', 10, '操作员', 0, 7, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08', NULL, NULL);
INSERT INTO `users` VALUES (18, 'U02', 'AQAAAAIAAYagAAAAECkIJmNaLvk9o9JVh7RoQTI9VvL6B8U3uLQkzQvCeD2qcHKJWmjGsdzIgFg3Mg==', 'E89F4B8C79F14A798D97D57E97D45D46', '王操作员', '<EMAIL>', '13911111102', 10, '操作员', 0, 7, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08', NULL, NULL);
INSERT INTO `users` VALUES (19, 'U03', 'AQAAAAIAAYagAAAAECkIJmNaLvk9o9JVh7RoQTI9VvL6B8U3uLQkzQvCeD2qcHKJWmjGsdzIgFg3Mg==', 'E89F4B8C79F14A798D97D57E97D45D46', '李操作员', '<EMAIL>', '13911111103', 11, '操作员', 0, 7, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08', NULL, NULL);
INSERT INTO `users` VALUES (20, 'U04', 'AQAAAAIAAYagAAAAECkIJmNaLvk9o9JVh7RoQTI9VvL6B8U3uLQkzQvCeD2qcHKJWmjGsdzIgFg3Mg==', 'E89F4B8C79F14A798D97D57E97D45D46', '赵操作员', '<EMAIL>', '13911111104', 12, '操作员', 0, 7, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08', NULL, NULL);
INSERT INTO `users` VALUES (21, 'U05', 'AQAAAAIAAYagAAAAECkIJmNaLvk9o9JVh7RoQTI9VvL6B8U3uLQkzQvCeD2qcHKJWmjGsdzIgFg3Mg==', 'E89F4B8C79F14A798D97D57E97D45D46', '钱操作员', '<EMAIL>', '13911111105', 13, '操作员', 0, 7, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08', NULL, NULL);

-- ----------------------------
-- Procedure structure for generate_asset_code
-- ----------------------------
DROP PROCEDURE IF EXISTS `generate_asset_code`;
delimiter ;;
CREATE PROCEDURE `generate_asset_code`(IN asset_type_code VARCHAR(10),
    OUT asset_code VARCHAR(30))
BEGIN
    DECLARE date_part VARCHAR(10);
    DECLARE seq_num INT;
    
    -- 当前日期部分 (年月日)
    SET date_part = DATE_FORMAT(NOW(), '%Y%m%d');
    
    -- 获取当天最大序号并+1
    SELECT IFNULL(MAX(SUBSTRING_INDEX(assetCode, '-', -1)), 0) + 1 
    INTO seq_num
    FROM assets 
    WHERE assetCode LIKE CONCAT('IT-', asset_type_code, '-', date_part, '-%');
    
    -- 格式化资产编号
    SET asset_code = CONCAT('IT-', asset_type_code, '-', date_part, '-', LPAD(seq_num, 3, '0'));
END
;;
delimiter ;

-- ----------------------------
-- Procedure structure for inventory_to_asset
-- ----------------------------
DROP PROCEDURE IF EXISTS `inventory_to_asset`;
delimiter ;;
CREATE PROCEDURE `inventory_to_asset`(IN inventory_id INT,
    IN location_id INT,
    IN user_id INT,
    OUT asset_id INT)
BEGIN
    DECLARE asset_type_code VARCHAR(10);
    DECLARE asset_code VARCHAR(30);
    
    -- 事务开始
    START TRANSACTION;
    
    -- 获取物料类型代码
    SELECT code INTO asset_type_code 
    FROM assettypes at 
    JOIN purchaseitems pi ON at.id = pi.assetTypeId
    WHERE pi.id = inventory_id;
    
    -- 生成资产编号
    CALL generate_asset_code(asset_type_code, asset_code);
    
    -- 创建资产记录
    INSERT INTO assets(
        assetCode,
        name,
        assetTypeId,
        model,
        brand,
        purchaseDate,
        locationId,
        status,
        inventoryId,
        createdAt,
        updatedAt
    )
    SELECT 
        asset_code,
        pi.name,
        pi.assetTypeId,
        pi.model,
        pi.brand,
        po.orderDate,
        location_id,
        1, -- 使用中状态
        inventory_id,
        NOW(),
        NOW()
    FROM purchaseitems pi
    JOIN purchaseorders po ON pi.purchaseOrderId = po.id
    WHERE pi.id = inventory_id;
    
    -- 更新库存状态
    UPDATE purchaseitems 
    SET status = 2 -- 已出库
    WHERE id = inventory_id;
    
    -- 获取新创建的资产ID
    SELECT LAST_INSERT_ID() INTO asset_id;
    
    -- 提交事务
    COMMIT;
END
;;
delimiter ;

-- ----------------------------
-- Triggers structure for table faultrecords
-- ----------------------------
DROP TRIGGER IF EXISTS `generate_fault_number`;
delimiter ;;
CREATE TRIGGER `generate_fault_number` BEFORE INSERT ON `faultrecords` FOR EACH ROW BEGIN
    DECLARE date_part VARCHAR(10);
    DECLARE seq_num INT;
    
    -- 如果没有手动指定故障单号
    IF NEW.faultNumber IS NULL OR NEW.faultNumber = '' THEN
        -- 生成日期部分
        SET date_part = DATE_FORMAT(NOW(), '%Y%m%d');
        
        -- 获取当天最大序号
        SELECT IFNULL(MAX(SUBSTRING_INDEX(faultNumber, '-', -1)), 0) + 1 
        INTO seq_num
        FROM faultrecords 
        WHERE faultNumber LIKE CONCAT('FIX-', date_part, '-%');
        
        -- 设置故障单号
        SET NEW.faultNumber = CONCAT('FIX-', date_part, '-', LPAD(seq_num, 3, '0'));
    END IF;
END
;;
delimiter ;

-- ----------------------------
-- Triggers structure for table purchaseorders
-- ----------------------------
DROP TRIGGER IF EXISTS `format_purchase_order_number`;
delimiter ;;
CREATE TRIGGER `format_purchase_order_number` BEFORE INSERT ON `purchaseorders` FOR EACH ROW BEGIN
    -- 如果没有手动指定采购单号
    IF NEW.OrderCode IS NULL OR NEW.OrderCode = '' THEN
        -- 生成采购单号 (PO-年月日-时分秒)
        SET NEW.OrderCode = CONCAT('PO-', DATE_FORMAT(NOW(), '%Y%m%d-%H%i%s'));
    END IF;
END
;;
delimiter ;

SET FOREIGN_KEY_CHECKS = 1;
