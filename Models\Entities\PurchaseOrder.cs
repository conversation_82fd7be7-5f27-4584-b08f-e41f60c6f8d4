// IT资产管理系统 - 采购订单实体
// 文件路径: /Models/Entities/PurchaseOrder.cs
// 功能: 定义采购订单实体，对应purchaseorders表，实现简化的采购流程

using System;
using System.Collections.Generic;
using ItAssetsSystem.Models;

namespace ItAssetsSystem.Models.Entities
{
    /// <summary>
    /// 采购订单实体
    /// </summary>
    public class PurchaseOrder : IAuditableEntity
    {
        /// <summary>
        /// 采购订单ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 订单编号
        /// </summary>
        public string OrderNumber { get; set; }

        /// <summary>
        /// 标题
        /// </summary>
        public string Title { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 供应商ID
        /// </summary>
        public int SupplierId { get; set; }

        /// <summary>
        /// 申请人ID
        /// </summary>
        public int RequesterId { get; set; }

        /// <summary>
        /// 申请时间
        /// </summary>
        public DateTime ApplicationTime { get; set; }

        /// <summary>
        /// 审批人ID
        /// </summary>
        public int? ApproverId { get; set; }

        /// <summary>
        /// 审批时间
        /// </summary>
        public DateTime? ApprovalTime { get; set; }

        /// <summary>
        /// 预计到货日期
        /// </summary>
        public DateTime? ExpectedDeliveryDate { get; set; }

        /// <summary>
        /// 实际到货日期
        /// </summary>
        public DateTime? ActualDeliveryDate { get; set; }

        /// <summary>
        /// 总金额
        /// </summary>
        public decimal TotalAmount { get; set; }

        /// <summary>
        /// 状态 (0:未到货, 1:已到货)
        /// </summary>
        public int Status { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Notes { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; }

        /// <summary>
        /// 供应商
        /// </summary>
        public virtual Supplier Supplier { get; set; }

        /// <summary>
        /// 申请人
        /// </summary>
        public virtual User Requester { get; set; }

        /// <summary>
        /// 审批人
        /// </summary>
        public virtual User Approver { get; set; }

        /// <summary>
        /// 采购物品列表
        /// </summary>
        public virtual ICollection<PurchaseItem> PurchaseItems { get; set; }
    }
}

// 计划行数: 50
// 实际行数: 50 