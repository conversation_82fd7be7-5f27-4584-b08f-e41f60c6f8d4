/**
 * 航空航天级IT资产管理系统 - 位置管理API
 * 文件路径: src/api/location.js
 * 功能描述: 提供位置管理相关的API服务
 */

import request from '@/utils/request'
import systemConfig from '@/config/system'

const locationApi = {
  /**
   * 获取位置树形结构
   */
  getLocationTree() {
    return request.get('/Location/tree')
  },
  
  /**
   * 获取位置列表
   * @param {Object} params 查询参数
   */
  getLocationList(params) {
    return request.get('/Location/list', { params })
  },
  
  /**
   * 获取位置列表 (兼容原来的API)
   * @param {Object} params 查询参数
   */
  getLocations(params) {
    console.log('搜索位置列表 - API调用', {
      请求方法: 'GET',
      请求路径: '/Location',
      完整URL: `${systemConfig.apiBaseUrl}/Location`,
      请求参数: params
    })
    return request.get('/Location', { params })
  },
  
  /**
   * 获取工位位置列表
   */
  getWorkstationLocations() {
    return request.get('/Location/workstations')
  },
  
  /**
   * 获取用于下拉选择的位置列表
   */
  getLocationsForDropdown(params) {
    return request.get('/Location/dropdown', { params })
  },
  
  /**
   * 初始化根位置
   */
  initRootLocation() {
    return request.post('/Location/init')
  },
  
  /**
   * 创建位置
   * @param {object} data 位置数据
   */
  createLocation(data) {
    return request.post('/Location', data)
  },
  
  /**
   * 更新位置
   * @param {string} id 位置ID
   * @param {object} data 位置数据
   */
  updateLocation(id, data) {
    return request.put(`/Location/${id}`, data)
  },
  
  /**
   * 删除位置
   * @param {string} id 位置ID
   */
  deleteLocation(id) {
    return request.delete(`/Location/${id}`)
  },
  
  /**
   * 获取位置详情
   * @param {number} locationId - 位置ID
   */
  getLocationDetail(locationId) {
    return request.get(`/Location/${locationId}`)
  },
  
  /**
   * 获取位置用户列表
   * @param {number} locationId 位置ID
   */
  getLocationUsers(locationId) {
    return request({
      url: `/Location/${locationId}/users`,
      method: 'get'
    })
  },
  
  /**
   * 更新位置用户
   * @param {number} locationId 位置ID
   * @param {object} data 包含用户列表和替换标志的对象
   * @param {boolean} data.replaceExisting 是否替换现有用户
   * @param {Array} data.users 用户数据数组，每个元素包含personnelId和userType
   */
  updateLocationUsers(locationId, data) {
    return request.post(`/Location/${locationId}/users`, data)
  },
  
  /**
   * 移除位置用户
   * @param {number} locationId 位置ID
   * @param {number} userId 用户ID
   */
  removeLocationUser(locationId, userId) {
    return request({
      url: `/Location/${locationId}/users/${userId}`,
      method: 'delete'
    })
  },
  
  /**
   * 获取位置部门
   * @param {number} locationId 位置ID
   */
  getLocationDepartment(locationId) {
    return request.get(`/Location/${locationId}/department`)
  },
  
  /**
   * 更新位置关联的部门
   * @param {number} locationId 位置ID
   * @param {Object} data 部门关联数据
   * @returns {Promise} 返回更新结果
   */
  updateLocationDepartment(locationId, data) {
    console.log('更新位置关联部门 - API调用', {
      请求方法: 'POST',
      请求路径: `/Location/${locationId}/department`,
      完整URL: `${systemConfig.apiBaseUrl}/Location/${locationId}/department`,
      请求数据: data
    })
    return request.post(`/Location/${locationId}/department`, data)
  },
  
  /**
   * 搜索位置列表
   * @param {Object} params 查询参数
   */
  searchLocations(params) {
    console.log('调用searchLocations API，参数:', params)
    return request.get('/Location/search', { params })
  },
  
  /**
   * 获取位置关联的资产
   * @param {object} params 查询参数
   */
  getLocationAssets(params) {
    console.log('调用getLocationAssets API，参数:', params)
    const locationId = params.locationId
    delete params.locationId
    return request.get(`/Location/${locationId}/assets`, { params })
  },
  
  /**
   * 关联部门到位置
   * @param {object} data 关联数据
   */
  relateDepartment(data) {
    return request.post('/Location/relate-department', data)
  },
  
  /**
   * 关联资产到位置
   * @param {number} locationId 位置ID
   * @param {object} data 关联数据
   */
  relateAssets(locationId, data) {
    return request.post(`/Location/${locationId}/relate-assets`, data)
  },
  
  /**
   * 解除资产与位置的关联
   * @param {number} locationId 位置ID
   * @param {number[]} assetIds 资产ID列表
   */
  unrelateAssets(locationId, assetIds) {
    return request.post(`/Location/${locationId}/unrelate-assets`, { assetIds })
  },
  
  /**
   * 获取位置历史记录
   * @param {number} locationId 位置ID
   * @param {object} params 查询参数
   */
  getLocationHistory(locationId, params) {
    return request.get(`/Location/${locationId}/history`, { params })
  },
  
  /**
   * 批量获取位置用户信息
   * @param {number[]} locationIds 位置ID列表
   * @returns {Promise} 返回用户信息
   */
  getLocationUsersBatch(locationIds) {
    return request.post('/Location/users/batch', { locationIds })
  },
  
  /**
   * 设置位置关联部门
   * @param {Number} locationId 位置ID
   * @param {Number} departmentId 部门ID
   */
  setLocationDepartment(locationId, departmentId) {
    return request({
      url: `/Location/${locationId}/department`,
      method: 'post',
      data: { departmentId }
    })
  },
  
  /**
   * 设置位置关联的人员
   * @param {Number} locationId 位置ID
   * @param {Array} users 人员列表
   * @param {Boolean} replaceExisting 是否替换现有关联
   */
  setLocationUsers(locationId, users, replaceExisting = false) {
    return request({
      url: `/Location/${locationId}/users`,
      method: 'post',
      data: {
        users,
        replaceExisting
      }
    })
  }
}

export default locationApi