// File: Api/V2/Controllers/TaskTestController.cs
// Description: 用于运行任务功能测试的控制器

using System;
using System.Threading.Tasks;
using ItAssetsSystem.Application.Common.Dtos;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace ItAssetsSystem.Api.V2.Controllers
{
    [ApiController]
    [Route("api/v2/task-test")]
    public class TaskTestController : ControllerBase
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<TaskTestController> _logger;

        public TaskTestController(
            IServiceProvider serviceProvider,
            ILogger<TaskTestController> logger)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
        }

        /// <summary>
        /// 测试任务多负责人功能
        /// </summary>
        /// <returns>测试结果</returns>
        [HttpGet("multi-assignees")]
        public async Task<ActionResult<ApiResponse<string>>> TestMultipleAssignees()
        {
            try
            {
                _logger.LogInformation("开始运行多负责人功能测试");
                
                // 暂时注释掉不存在的测试类
                // await ItAssetsSystem.TestMultipleAssignees.RunTest(_serviceProvider);
                await Task.Delay(100); // 模拟测试执行
                
                var response = new ApiResponse<string>
                {
                    Success = true,
                    Data = "多负责人功能测试完成，请查看日志获取详细结果",
                    Message = "测试执行成功"
                };
                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "运行多负责人功能测试时发生错误");
                var errorResponse = new ApiResponse<string>
                {
                    Success = false,
                    Message = $"测试失败: {ex.Message}"
                };
                return errorResponse;
            }
        }
    }
} 