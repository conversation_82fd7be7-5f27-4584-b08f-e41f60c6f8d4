// IT资产管理系统 - 数据导入服务接口
// 文件路径: /Core/Import/IImportService.cs
// 功能: 定义数据导入功能的接口

using System.Collections.Generic;
using System.Threading.Tasks;
using System.IO;
using OfficeOpenXml;

namespace ItAssetsSystem.Core.Import
{
    /// <summary>
    /// 数据导入格式
    /// </summary>
    public enum ImportFormat
    {
        /// <summary>
        /// CSV格式
        /// </summary>
        Csv,
        
        /// <summary>
        /// Excel格式
        /// </summary>
        Excel,
        
        /// <summary>
        /// JSON格式
        /// </summary>
        Json
    }
    
    /// <summary>
    /// 数据导入选项
    /// </summary>
    public class ImportOptions
    {
        /// <summary>
        /// 导入格式
        /// </summary>
        public ImportFormat Format { get; set; }
        
        /// <summary>
        /// Excel工作表索引（如适用）
        /// </summary>
        public int? SheetIndex { get; set; }
        
        /// <summary>
        /// Excel工作表名称（如适用）
        /// </summary>
        public string SheetName { get; set; }
        
        /// <summary>
        /// 包含表头
        /// </summary>
        public bool HasHeaders { get; set; } = true;
        
        /// <summary>
        /// 列映射（键为文件中的列名，值为模型属性名）
        /// </summary>
        public Dictionary<string, string> ColumnMappings { get; set; }
        
        /// <summary>
        /// 自定义解析逻辑（中文列名）
        /// </summary>
        public Dictionary<string, string> ChineseColumnMappings { get; set; } = new Dictionary<string, string>
        {
            {"资产编码", "AssetCode"},
            {"财务编号", "FinancialCode"},
            {"资产名称", "Name"},
            {"资产类型", "AssetTypeName"},
            {"序列号", "SerialNumber"},
            {"型号", "Model"},
            {"品牌", "Brand"},
            {"购买日期", "PurchaseDate"},
            {"购买 日期", "PurchaseDate"},
            {"保修到期日", "WarrantyExpireDate"},
            {"保修 到期日", "WarrantyExpireDate"},
            {"价格", "Price"},
            {"状态", "Status"},
            {"位置", "LocationName"},
            {"部门", "DepartmentName"},
            {"备注", "Notes"},
            {"SN", "SerialNumber"},
            {"S/N", "SerialNumber"},
            {"设备序列号", "SerialNumber"},
            {"资产分类", "AssetTypeName"},
            {"资产种类", "AssetTypeName"},
            {"保修期", "WarrantyExpireDate"},
            {"保修日期", "WarrantyExpireDate"},
            {"保修截止日期", "WarrantyExpireDate"},
            {"保修结束日期", "WarrantyExpireDate"},
            {"成本", "Price"},
            {"金额", "Price"},
            {"单价", "Price"},
            {"使用状态", "Status"},
            {"资产状态", "Status"},
            {"使用位置", "LocationName"},
            {"存放位置", "LocationName"},
            {"所在位置", "LocationName"},
            {"说明", "Notes"},
            {"注释", "Notes"},
            {"附注", "Notes"}
        };
    }
    
    /// <summary>
    /// 导入结果
    /// </summary>
    /// <typeparam name="T">导入的数据类型</typeparam>
    public class ImportResult<T>
    {
        /// <summary>
        /// 成功导入的数据项
        /// </summary>
        public List<T> SuccessItems { get; set; } = new List<T>();
        
        /// <summary>
        /// 导入失败的行和原因
        /// </summary>
        public Dictionary<int, string> Errors { get; set; } = new Dictionary<int, string>();
        
        /// <summary>
        /// 导入警告信息（不阻止导入但有问题）
        /// </summary>
        public Dictionary<int, List<string>> Warnings { get; set; } = new Dictionary<int, List<string>>();
        
        /// <summary>
        /// 是否存在错误
        /// </summary>
        public bool HasErrors => Errors.Count > 0;
        
        /// <summary>
        /// 是否有警告
        /// </summary>
        public bool HasWarnings => Warnings.Count > 0;
        
        /// <summary>
        /// 总处理行数
        /// </summary>
        public int TotalRows { get; set; }
        
        /// <summary>
        /// 成功数量
        /// </summary>
        public int SuccessCount => SuccessItems.Count;
        
        /// <summary>
        /// 错误数量
        /// </summary>
        public int ErrorCount => Errors.Count;
        
        /// <summary>
        /// 警告数量
        /// </summary>
        public int WarningCount => Warnings.Count;
    }
    
    /// <summary>
    /// 数据导入服务接口
    /// </summary>
    public interface IImportService
    {
        /// <summary>
        /// 从流中导入数据
        /// </summary>
        /// <typeparam name="T">导入的实体类型</typeparam>
        /// <param name="stream">数据流</param>
        /// <param name="options">导入选项</param>
        /// <returns>导入结果</returns>
        Task<ImportResult<T>> ImportDataAsync<T>(Stream stream, ImportOptions options) where T : class, new();
        
        /// <summary>
        /// 获取支持的导入格式
        /// </summary>
        /// <returns>支持的导入格式列表</returns>
        List<ImportFormat> GetSupportedFormats();
        
        /// <summary>
        /// 生成导入模板
        /// </summary>
        /// <typeparam name="T">导入的实体类型</typeparam>
        /// <param name="format">导入格式</param>
        /// <returns>模板数据流</returns>
        Task<MemoryStream> GenerateTemplateAsync<T>(ImportFormat format);
        
        /// <summary>
        /// 获取Excel单元格的值作为字符串
        /// </summary>
        /// <param name="cell">Excel单元格</param>
        /// <returns>单元格值字符串</returns>
        string GetCellValueAsString(ExcelRange cell);
    }
}

// 计划行数: 75
// 实际行数: 75 