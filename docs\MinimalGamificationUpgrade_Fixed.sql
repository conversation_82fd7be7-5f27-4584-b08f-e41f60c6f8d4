-- =====================================================
-- 修正版精简游戏化系统升级脚本
-- 基于实际数据库结构的最小化升级方案
-- =====================================================

-- 🔍 分析结果：现有gamification基础设施已完善，无需创建新表
-- ✅ 建议：直接使用现有 gamification_userstats 和 gamification_log 表

-- =====================================================
-- 1. 创建工作汇总统计视图（基于现有数据）
-- =====================================================

CREATE OR REPLACE VIEW v_user_work_summary AS
SELECT 
    us.CoreUserId as user_id,
    u.name as user_name,
    d.name as department_name,
    
    -- 📊 从现有 gamification_userstats 表获取统计
    us.CompletedTasksCount as tasks_completed,
    us.CreatedTasksCount as tasks_created,
    us.ClaimedTasksCount as tasks_claimed,
    
    -- 🎮 游戏化数据
    us.PointsBalance as total_points,
    us.CoinsBalance as total_coins,
    us.DiamondsBalance as total_diamonds,
    us.CurrentXP as total_xp,
    us.CurrentLevel as current_level,
    us.StreakCount as consecutive_days,
    
    -- 📈 排名计算
    ROW_NUMBER() OVER (ORDER BY us.PointsBalance DESC) as points_rank,
    ROW_NUMBER() OVER (ORDER BY (us.CompletedTasksCount * 3 + us.CreatedTasksCount * 2 + us.ClaimedTasksCount * 1) DESC) as productivity_rank,
    
    -- 🏆 综合评分
    (us.CompletedTasksCount * 3 + us.CreatedTasksCount * 2 + us.ClaimedTasksCount * 1) as productivity_score,
    
    -- 🎯 专业特长
    CASE 
        WHEN us.CompletedTasksCount >= us.CreatedTasksCount AND us.CompletedTasksCount >= us.ClaimedTasksCount THEN '任务执行专家'
        WHEN us.CreatedTasksCount >= us.CompletedTasksCount AND us.CreatedTasksCount >= us.ClaimedTasksCount THEN '任务规划专家'
        WHEN us.ClaimedTasksCount >= us.CompletedTasksCount AND us.ClaimedTasksCount >= us.CreatedTasksCount THEN '主动承担专家'
        ELSE '全能型员工'
    END as specialty,
    
    -- 📅 时间信息
    us.LastActivityTimestamp as last_activity,
    us.LastUpdatedTimestamp as updated_at

FROM gamification_userstats us
JOIN users u ON us.CoreUserId = u.Id
LEFT JOIN departments d ON u.department_id = d.id
WHERE u.IsDeleted = 0
ORDER BY us.PointsBalance DESC;

-- =====================================================
-- 2. 创建增强排行榜视图
-- =====================================================

CREATE OR REPLACE VIEW v_enhanced_leaderboard AS
SELECT 
    -- 📊 排名信息
    ROW_NUMBER() OVER (ORDER BY us.PointsBalance DESC) as rank_no,
    us.CoreUserId as user_id,
    u.name as user_name,
    u.avatar as user_avatar,
    d.name as department_name,
    
    -- 🎮 游戏化数据
    us.PointsBalance as total_points,
    us.CoinsBalance as total_coins,
    us.DiamondsBalance as total_diamonds,
    us.CurrentLevel as current_level,
    us.CurrentXP as current_xp,
    
    -- 📈 活动统计
    us.CompletedTasksCount as tasks_completed,
    us.CreatedTasksCount as tasks_created,
    us.ClaimedTasksCount as tasks_claimed,
    us.OnTimeTasksCount as on_time_tasks,
    us.StreakCount as streak_days,
    
    -- 🏆 评价等级
    CASE 
        WHEN ROW_NUMBER() OVER (ORDER BY us.PointsBalance DESC) <= 3 THEN '🥇 超级明星'
        WHEN ROW_NUMBER() OVER (ORDER BY us.PointsBalance DESC) <= 10 THEN '🏆 优秀员工'
        WHEN ROW_NUMBER() OVER (ORDER BY us.PointsBalance DESC) <= 20 THEN '⭐ 积极进取'
        ELSE '💪 稳步提升'
    END as evaluation,
    
    -- 🎯 专业特长
    CASE 
        WHEN us.CompletedTasksCount >= us.CreatedTasksCount AND us.CompletedTasksCount >= us.ClaimedTasksCount THEN '任务执行专家'
        WHEN us.CreatedTasksCount >= us.CompletedTasksCount AND us.CreatedTasksCount >= us.ClaimedTasksCount THEN '任务规划专家'
        WHEN us.ClaimedTasksCount >= us.CompletedTasksCount AND us.ClaimedTasksCount >= us.CreatedTasksCount THEN '主动承担专家'
        ELSE '全能型员工'
    END as specialty,
    
    -- 📊 等级进度
    CASE 
        WHEN us.CurrentLevel >= 100 THEN 100.00
        ELSE us.level_progress
    END as level_progress_percent,
    
    -- 📅 活跃度
    CASE 
        WHEN us.LastActivityTimestamp >= DATE_SUB(NOW(), INTERVAL 1 DAY) THEN '🟢 高度活跃'
        WHEN us.LastActivityTimestamp >= DATE_SUB(NOW(), INTERVAL 3 DAY) THEN '🟡 正常活跃'
        WHEN us.LastActivityTimestamp >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN '🟠 稍显沉寂'
        ELSE '🔴 需要激活'
    END as activity_status,
    
    us.LastActivityTimestamp as last_activity,
    us.LastUpdatedTimestamp as updated_at

FROM gamification_userstats us
JOIN users u ON us.CoreUserId = u.Id
LEFT JOIN departments d ON u.department_id = d.id
WHERE u.IsDeleted = 0
ORDER BY us.PointsBalance DESC;

-- =====================================================
-- 3. 创建周期统计分析视图
-- =====================================================

CREATE OR REPLACE VIEW v_period_statistics AS
SELECT 
    -- 📅 本周统计 (基于 gamification_log 表)
    'weekly' as period_type,
    DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY) as period_date,
    gl.UserId as user_id,
    u.name as user_name,
    d.name as department_name,
    
    -- 📊 本周活动统计
    COUNT(CASE WHEN gl.EventType LIKE '%TaskComplete%' OR gl.ActionType LIKE '%TaskComplete%' THEN 1 END) as tasks_completed,
    COUNT(CASE WHEN gl.EventType LIKE '%TaskCreate%' OR gl.ActionType LIKE '%TaskCreate%' THEN 1 END) as tasks_created,
    COUNT(CASE WHEN gl.EventType LIKE '%TaskClaim%' OR gl.ActionType LIKE '%TaskClaim%' THEN 1 END) as tasks_claimed,
    COUNT(CASE WHEN gl.EventType LIKE '%Asset%' OR gl.ActionType LIKE '%Asset%' THEN 1 END) as assets_operations,
    COUNT(CASE WHEN gl.EventType LIKE '%Fault%' OR gl.ActionType LIKE '%Fault%' THEN 1 END) as faults_operations,
    
    -- 🎮 本周收益统计
    SUM(COALESCE(gl.PointsGained, 0)) as points_earned,
    SUM(COALESCE(gl.CoinsGained, 0)) as coins_earned,
    SUM(COALESCE(gl.DiamondsGained, 0)) as diamonds_earned,
    
    -- 📈 本周排名
    ROW_NUMBER() OVER (ORDER BY SUM(COALESCE(gl.PointsGained, 0)) DESC) as weekly_rank,
    
    -- 🏆 本周评价
    CASE 
        WHEN ROW_NUMBER() OVER (ORDER BY SUM(COALESCE(gl.PointsGained, 0)) DESC) <= 3 THEN '🏆 本周冠军'
        WHEN ROW_NUMBER() OVER (ORDER BY SUM(COALESCE(gl.PointsGained, 0)) DESC) <= 10 THEN '⭐ 本周之星'
        ELSE '💪 继续努力'
    END as weekly_evaluation

FROM gamification_log gl
JOIN users u ON gl.UserId = u.Id
LEFT JOIN departments d ON u.department_id = d.id
WHERE u.IsDeleted = 0
    AND gl.Timestamp >= DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY)
    AND gl.Timestamp < DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY) + INTERVAL 7 DAY
GROUP BY gl.UserId, u.name, d.name

UNION ALL

SELECT 
    -- 📅 本月统计
    'monthly' as period_type,
    DATE_FORMAT(CURDATE(), '%Y-%m-01') as period_date,
    gl.UserId as user_id,
    u.name as user_name,
    d.name as department_name,
    
    -- 📊 本月活动统计
    COUNT(CASE WHEN gl.EventType LIKE '%TaskComplete%' OR gl.ActionType LIKE '%TaskComplete%' THEN 1 END) as tasks_completed,
    COUNT(CASE WHEN gl.EventType LIKE '%TaskCreate%' OR gl.ActionType LIKE '%TaskCreate%' THEN 1 END) as tasks_created,
    COUNT(CASE WHEN gl.EventType LIKE '%TaskClaim%' OR gl.ActionType LIKE '%TaskClaim%' THEN 1 END) as tasks_claimed,
    COUNT(CASE WHEN gl.EventType LIKE '%Asset%' OR gl.ActionType LIKE '%Asset%' THEN 1 END) as assets_operations,
    COUNT(CASE WHEN gl.EventType LIKE '%Fault%' OR gl.ActionType LIKE '%Fault%' THEN 1 END) as faults_operations,
    
    -- 🎮 本月收益统计
    SUM(COALESCE(gl.PointsGained, 0)) as points_earned,
    SUM(COALESCE(gl.CoinsGained, 0)) as coins_earned,
    SUM(COALESCE(gl.DiamondsGained, 0)) as diamonds_earned,
    
    -- 📈 本月排名
    ROW_NUMBER() OVER (ORDER BY SUM(COALESCE(gl.PointsGained, 0)) DESC) as weekly_rank,
    
    -- 🏆 本月评价
    CASE 
        WHEN ROW_NUMBER() OVER (ORDER BY SUM(COALESCE(gl.PointsGained, 0)) DESC) <= 3 THEN '🏆 月度冠军'
        WHEN ROW_NUMBER() OVER (ORDER BY SUM(COALESCE(gl.PointsGained, 0)) DESC) <= 10 THEN '⭐ 月度之星'
        ELSE '💪 继续努力'
    END as weekly_evaluation

FROM gamification_log gl
JOIN users u ON gl.UserId = u.Id
LEFT JOIN departments d ON u.department_id = d.id
WHERE u.IsDeleted = 0
    AND gl.Timestamp >= DATE_FORMAT(CURDATE(), '%Y-%m-01')
    AND gl.Timestamp < DATE_FORMAT(CURDATE(), '%Y-%m-01') + INTERVAL 1 MONTH
GROUP BY gl.UserId, u.name, d.name

ORDER BY period_type, weekly_rank;

-- =====================================================
-- 4. 性能优化索引
-- =====================================================

-- 为 gamification_log 表添加性能索引（如果不存在）
CREATE INDEX IF NOT EXISTS idx_gamification_log_user_time 
ON gamification_log(UserId, Timestamp);

CREATE INDEX IF NOT EXISTS idx_gamification_log_event_time 
ON gamification_log(EventType, Timestamp);

CREATE INDEX IF NOT EXISTS idx_gamification_log_action_time 
ON gamification_log(ActionType, Timestamp);

-- 为 gamification_userstats 表添加索引（如果不存在）
CREATE INDEX IF NOT EXISTS idx_gamification_userstats_points 
ON gamification_userstats(PointsBalance DESC);

CREATE INDEX IF NOT EXISTS idx_gamification_userstats_level 
ON gamification_userstats(CurrentLevel, CurrentXP);

CREATE INDEX IF NOT EXISTS idx_gamification_userstats_activity 
ON gamification_userstats(LastActivityTimestamp);

-- =====================================================
-- 5. 验证视图创建
-- =====================================================

-- 测试工作汇总视图
SELECT '✅ v_user_work_summary 视图创建成功' as status, COUNT(*) as user_count 
FROM v_user_work_summary 
LIMIT 1;

-- 测试排行榜视图
SELECT '✅ v_enhanced_leaderboard 视图创建成功' as status, COUNT(*) as user_count 
FROM v_enhanced_leaderboard 
LIMIT 1;

-- 测试周期统计视图
SELECT '✅ v_period_statistics 视图创建成功' as status, COUNT(*) as record_count 
FROM v_period_statistics 
LIMIT 1;

-- 显示前5名用户数据预览
SELECT 
    '🏆 排行榜前5名预览' as title,
    rank_no,
    user_name,
    department_name,
    total_points,
    current_level,
    evaluation,
    specialty
FROM v_enhanced_leaderboard 
LIMIT 5;

-- =====================================================
-- 6. 安装完成信息
-- =====================================================

SELECT 
    '🎉 修正版精简游戏化系统升级完成！' as message,
    '✅ 基于现有gamification基础设施创建统计视图' as infrastructure,
    '📊 无需新建表，直接使用现有数据' as approach,
    '🚀 现在可以开发API服务层' as next_step;

-- =====================================================
-- 7. API开发所需的关键查询示例
-- =====================================================

-- 示例1: 获取用户工作汇总
-- SELECT * FROM v_user_work_summary WHERE user_id = ?;

-- 示例2: 获取排行榜（支持分页）
-- SELECT * FROM v_enhanced_leaderboard LIMIT 20 OFFSET 0;

-- 示例3: 获取周期统计
-- SELECT * FROM v_period_statistics WHERE period_type = 'weekly' ORDER BY weekly_rank;

-- 示例4: 获取用户详细信息
-- SELECT us.*, u.name, u.avatar, d.name as department 
-- FROM gamification_userstats us 
-- JOIN users u ON us.CoreUserId = u.Id 
-- LEFT JOIN departments d ON u.department_id = d.id 
-- WHERE us.CoreUserId = ?;