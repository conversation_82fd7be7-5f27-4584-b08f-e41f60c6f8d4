/**
 * 备品备件管理模块API
 * 文件路径: src/api/spareparts.js
 * 功能描述: 封装与后端备品备件相关的API交互
 */

import request from '@/utils/request';

// API 函数 - 备件类型管理
// 获取备件类型列表
export function getSparePartTypes(params) {
  return request({
    url: '/v2/spareparttype',
    method: 'get',
    params
  });
}

// 获取备件类型树形结构
export function getSparePartTypesTree(params) {
  return request({
    url: '/v2/spareparttype/tree',
    method: 'get',
    params
  });
}

// 获取单个备件类型详细信息
export function getSparePartType(id) {
  return request({
    url: `/v2/spareparttype/${id}`,
    method: 'get'
  });
}

// 创建备件类型
export function createSparePartType(data) {
  return request({
    url: '/v2/spareparttype',
    method: 'post',
    data
  });
}

// 更新备件类型
export function updateSparePartType(id, data) {
  return request({
    url: `/v2/spareparttype/${id}`,
    method: 'put',
    data
  });
}

// 删除备件类型
export function deleteSparePartType(id) {
  return request({
    url: `/v2/spareparttype/${id}`,
    method: 'delete'
  });
}

// API 函数 - 备件仓库管理
// 获取备件仓库列表
export function getSparePartWarehouses(params) {
  return request.get('/v2/sparepartwarehouse', params);
}

// 创建备件仓库
export function createSparePartWarehouse(data) {
  return request.post('/v2/sparepartwarehouse', data);
}

// 更新备件仓库
export function updateSparePartWarehouse(id, data) {
  return request.put(`/v2/sparepartwarehouse/${id}`, data);
}

// 删除备件仓库
export function deleteSparePartWarehouse(id) {
  return request.delete(`/v2/sparepartwarehouse/${id}`);
}

// API 函数 - 备件库位管理
// 获取备件库位列表
export function getSparePartLocations(params) {
  return request({
    url: '/v2/sparepartlocation',
    method: 'get',
    params
  });
}

// 获取单个备件库位详细信息
export function getSparePartLocation(id) {
  return request({
    url: `/v2/sparepartlocation/${id}`,
    method: 'get'
  });
}

// 创建备件库位
export function createSparePartLocation(data) {
  return request({
    url: '/v2/sparepartlocation',
    method: 'post',
    data
  });
}

// 更新备件库位
export function updateSparePartLocation(id, data) {
  return request({
    url: `/v2/sparepartlocation/${id}`,
    method: 'put',
    data
  });
}

// 删除备件库位
export function deleteSparePartLocation(id) {
  return request({
    url: `/v2/sparepartlocation/${id}`,
    method: 'delete'
  });
}

// 获取所有备件库位（用于下拉选择）
export function getAllSparePartLocations() {
  return request({
    url: '/v2/sparepartlocation/all',
    method: 'get'
  });
}

// API 函数 - 备件管理
// 获取备件列表
export function getSpareParts(params = {}) {
  return request({
    url: '/v2/spare-parts',
    method: 'get',
    params
  });
}

// 获取备件详情
export function getSparePart(id) {
  return request({
    url: `/v2/spare-parts/${id}`,
    method: 'get'
  });
}

// 创建备件
export function createSparePart(data) {
  return request({
    url: '/v2/spare-parts',
    method: 'post',
    data
  });
}

// 更新备件
export function updateSparePart(id, data) {
  return request({
    url: `/v2/spare-parts/${id}`,
    method: 'put',
    data
  });
}

// 删除备件
export function deleteSparePart(id) {
  return request({
    url: `/v2/spare-parts/${id}`,
    method: 'delete'
  });
}

// 获取备件简要信息列表（供采购选择使用）
export function getSparePartsSimple(keyword = '') {
  return request({
    url: '/v2/spare-parts/simple',
    method: 'get',
    params: {
      keyword
    }
  });
}

// API 函数 - 出入库管理
// 获取出入库记录
export function getSparePartTransactions(params = {}) {
  return request({
    url: '/v2/spare-part-transactions',
    method: 'get',
    params
  });
}

// 入库操作
export function sparePartInbound(data) {
  return request({
    url: '/v2/spare-parts/transactions/in',
    method: 'post',
    data
  });
}

// 出库操作
export function sparePartOutbound(data) {
  return request({
    url: '/v2/spare-parts/transactions/out',
    method: 'post',
    data
  });
}

// 库存调整
export function stockAdjustment(data) {
  return request({
    url: '/v2/spare-parts/transactions/adjust',
    method: 'post',
    data
  });
}

// 状态调整
export function adjustSparePartStatus(data) {
  return request({
    url: '/v2/spare-part-inventory/status/adjust',
    method: 'post',
    data
  });
}

// 返厂维修单管理
export function getRepairOrders(params) {
  return request({
    url: '/v2/repair-orders',
    method: 'get',
    params
  });
}

export function createRepairOrder(data) {
  return request({
    url: '/v2/repair-orders',
    method: 'post',
    data
  });
}

export function updateRepairOrder(id, data) {
  return request({
    url: `/v2/repair-orders/${id}`,
    method: 'put',
    data
  });
}

export function submitRepairOrder(id) {
  return request({
    url: `/v2/repair-orders/${id}/submit`,
    method: 'post'
  });
}

export function getRepairOrderDetails(id) {
  return request({
    url: `/v2/repair-orders/${id}`,
    method: 'get'
  });
}

// 供应商管理
export function getSuppliers(params) {
  return request({
    url: '/v2/suppliers',
    method: 'get',
    params
  });
}

// 获取供应商详情
export function getSupplier(id) {
  return request({
    url: `/v2/suppliers/${id}`,
    method: 'get'
  });
}

// 创建供应商
export function createSupplier(data) {
  return request({
    url: '/v2/suppliers',
    method: 'post',
    data
  });
}

// 更新供应商
export function updateSupplier(id, data) {
  return request({
    url: `/v2/suppliers/${id}`,
    method: 'put',
    data
  });
}

// 删除供应商
export function deleteSupplier(id) {
  return request({
    url: `/v2/suppliers/${id}`,
    method: 'delete'
  });
}

// 获取维修供应商列表
export function getMaintenanceSuppliers() {
  return request({
    url: '/v2/suppliers/maintenance',
    method: 'get'
  });
}

// 获取采购供应商列表
export function getProcurementSuppliers() {
  return request({
    url: '/v2/suppliers/procurement',
    method: 'get'
  });
}

// API 函数 - 统计与预警
// 获取库存预警
export function getSparePartWarnings() {
  return request({
    url: '/v2/spare-parts/warnings',
    method: 'get'
  });
}

// 获取备件统计信息
export function getSparePartStats() {
  return request({
    url: '/v2/spare-parts/stats',
    method: 'get'
  });
}

// 获取备件区域分布统计
export function getSparePartAreaStats() {
  return request({
    url: '/v2/spare-parts/area-stats',
    method: 'get'
  });
}

// API 函数 - 库存状态管理
// 获取备件库存汇总
export function getSparePartStockSummary(partId) {
  return request({
    url: `/v2/spare-part-inventory/${partId}/stock-summary`,
    method: 'get'
  });
}

// 获取备件库存明细列表
export function getSparePartInventories(partId, params = {}) {
  return request({
    url: `/v2/spare-part-inventory/${partId}/inventories`,
    method: 'get',
    params
  });
}

// 获取所有状态类型
export function getSparePartStatusTypes() {
  return request({
    url: '/v2/spare-part-inventory/status-types',
    method: 'get'
  });
}

// 批量更新库存状态
export function batchUpdateSparePartStatus(data) {
  return request({
    url: '/v2/spare-part-inventory/status/batch-update',
    method: 'post',
    data
  });
}

// 获取库存状态变更历史
export function getSparePartStatusHistory(inventoryId, params = {}) {
  return request({
    url: `/v2/spare-part-inventory/inventories/${inventoryId}/status-history`,
    method: 'get',
    params
  });
}

