import{_ as s,r as e,x as a,c as t,z as l,b as n,d as u,t as i,e as o,w as r,f as c,aY as v,F as d,h as g,aN as p,o as m,p as y,E as f}from"./index-CG5lHOPO.js";import{a as h}from"./assetStatistics-CeU-HrJR.js";const w={class:"debug-container"},S={class:"section"},R={class:"section"},T={class:"section"},D={class:"result-display"},E={key:0},$={key:1},A={class:"section"},I={class:"logs"},O=s({__name:"AssetStatisticsDebug",setup(s){const O=e(!1),_=e({}),k=e([]),P=e([]),b=e([]),G=a(),B=t((()=>!!p())),N=t((()=>G.userInfo)),j=(s,e,a,t,l)=>{b.value.unshift({method:s,url:e,status:a,time:t,data:l,timestamp:(new Date).toLocaleTimeString()}),b.value.length>10&&b.value.pop()},x=async()=>{var s,e;O.value=!0,P.value=[];try{const s=Date.now(),e=await h.getOverallStatistics(),a=Date.now();_.value=e,j("GET","/v2/asset-statistics/overall",200,a-s,e),f.success("总体统计获取成功")}catch(a){P.value.push(`总体统计失败: ${a.message}`),j("GET","/v2/asset-statistics/overall",(null==(s=a.response)?void 0:s.status)||"ERROR",0,{error:a.message,response:null==(e=a.response)?void 0:e.data}),f.error("总体统计获取失败")}finally{O.value=!1}},C=async()=>{var s,e;O.value=!0,P.value=[];try{const s=Date.now(),e=await h.getStatisticsByType(),a=Date.now();k.value=e,j("GET","/v2/asset-statistics/by-type",200,a-s,e),f.success("类型统计获取成功")}catch(a){P.value.push(`类型统计失败: ${a.message}`),j("GET","/v2/asset-statistics/by-type",(null==(s=a.response)?void 0:s.status)||"ERROR",0,{error:a.message,response:null==(e=a.response)?void 0:e.data}),f.error("类型统计获取失败")}finally{O.value=!1}},J=async()=>{var s,e;O.value=!0,P.value=[];const a=[{name:"总体统计",fn:()=>h.getOverallStatistics()},{name:"类型统计",fn:()=>h.getStatisticsByType()},{name:"区域统计",fn:()=>h.getStatisticsByRegion()},{name:"部门统计",fn:()=>h.getStatisticsByDepartment()},{name:"资产类型列表",fn:()=>h.getAssetTypes()},{name:"区域列表",fn:()=>h.getRegions()},{name:"部门列表",fn:()=>h.getDepartments()}];let t=0;for(const n of a)try{const s=Date.now(),e=await n.fn(),a=Date.now();t++,j("GET",`API-${n.name}`,200,a-s,e)}catch(l){P.value.push(`${n.name}失败: ${l.message}`),j("GET",`API-${n.name}`,(null==(s=l.response)?void 0:s.status)||"ERROR",0,{error:l.message,response:null==(e=l.response)?void 0:e.data})}O.value=!1,t===a.length?f.success(`所有API测试完成，${t}/${a.length} 成功`):f.warning(`API测试完成，${t}/${a.length} 成功`)};return l((()=>{})),(s,e)=>(m(),n("div",w,[e[11]||(e[11]=u("h1",null,"资产统计API调试页面",-1)),u("div",S,[e[0]||(e[0]=u("h2",null,"🔐 认证状态",-1)),u("p",null,"Token存在: "+i(B.value?"是":"否"),1),u("p",null,"用户信息: "+i(N.value),1)]),u("div",R,[e[4]||(e[4]=u("h2",null,"📊 API调用测试",-1)),o(c(v),{onClick:x,loading:O.value},{default:r((()=>e[1]||(e[1]=[y("测试总体统计")]))),_:1},8,["loading"]),o(c(v),{onClick:C,loading:O.value},{default:r((()=>e[2]||(e[2]=[y("测试类型统计")]))),_:1},8,["loading"]),o(c(v),{onClick:J,loading:O.value},{default:r((()=>e[3]||(e[3]=[y("测试所有API")]))),_:1},8,["loading"])]),u("div",T,[e[8]||(e[8]=u("h2",null,"📋 调用结果",-1)),u("div",D,[e[5]||(e[5]=u("h3",null,"总体统计数据：",-1)),u("pre",null,i(JSON.stringify(_.value,null,2)),1),e[6]||(e[6]=u("h3",null,"类型统计数据：",-1)),u("pre",null,i(JSON.stringify(k.value,null,2)),1),e[7]||(e[7]=u("h3",null,"错误信息：",-1)),P.value.length?(m(),n("pre",E,i(P.value.join("\n")),1)):(m(),n("p",$,"无错误"))])]),u("div",A,[e[10]||(e[10]=u("h2",null,"🌐 网络请求日志",-1)),u("div",I,[(m(!0),n(d,null,g(b.value,((s,a)=>(m(),n("div",{key:a,class:"log-item"},[u("strong",null,i(s.method)+" "+i(s.url),1),u("p",null,"状态: "+i(s.status),1),u("p",null,"响应时间: "+i(s.time)+"ms",1),u("details",null,[e[9]||(e[9]=u("summary",null,"详细信息",-1)),u("pre",null,i(JSON.stringify(s.data,null,2)),1)])])))),128))])])]))}},[["__scopeId","data-v-83117b1c"]]);export{O as default};
