// IT资产管理系统 - 数据库上下文扩展
// 文件路径: /Infrastructure/Data/Extensions/DbContextExtensions.cs
// 功能: 提供数据库上下文扩展方法用于数据库初始化和种子数据

using ItAssetsSystem.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Configuration;
using System;
using System.Threading.Tasks;

namespace ItAssetsSystem.Infrastructure.Data.Extensions
{
    /// <summary>
    /// 数据库上下文扩展类
    /// </summary>
    public static class DbContextExtensions
    {
        /// <summary>
        /// 创建数据库并初始化
        /// </summary>
        public static async Task<IHost> InitializeDatabaseAsync(this IHost host)
        {
            using var scope = host.Services.CreateScope();
            var services = scope.ServiceProvider;
            try
            {
                var context = services.GetRequiredService<AppDbContext>();
                
                // 确保数据库已创建
                await context.Database.EnsureCreatedAsync();
            }
            catch (Exception ex)
            {
                // 记录错误信息，但不中断应用程序启动
                Console.WriteLine($"初始化数据库时发生错误: {ex.Message}");
            }
            
            return host;
        }
    }
} 