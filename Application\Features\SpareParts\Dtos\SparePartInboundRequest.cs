// File: Application/Features/SpareParts/Dtos/SparePartInboundRequest.cs
// Description: 备品备件入库请求数据传输对象

using System.ComponentModel.DataAnnotations;

namespace ItAssetsSystem.Application.Features.SpareParts.Dtos
{
    /// <summary>
    /// 备品备件入库请求数据传输对象
    /// </summary>
    public class SparePartInboundRequest : SparePartTransactionRequest
    {
        /// <summary>
        /// 原因类型: 1=采购入库, 2=退回入库, 5=盘点调整
        /// </summary>
        [Required(ErrorMessage = "原因类型不能为空")]
        [Range(1, 5, ErrorMessage = "原因类型无效")]
        public new byte ReasonType { get; set; }
        
        /// <summary>
        /// 关联单号
        /// </summary>
        [StringLength(100, ErrorMessage = "关联单号长度不能超过100个字符")]
        public string ReferenceNumber { get; set; }
        
        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(500, ErrorMessage = "备注长度不能超过500个字符")]
        public string Remarks { get; set; }
        
        /// <summary>
        /// 将属性映射到基类
        /// </summary>
        public void MapToBaseClass()
        {
            // 将ReferenceNumber映射到Reference
            this.Reference = this.ReferenceNumber;
            
            // 将Remarks映射到Notes
            this.Notes = this.Remarks;
            
            // ReasonType已经在当前类中覆盖
            base.ReasonType = this.ReasonType;
        }
    }
} 