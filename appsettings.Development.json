{"ConnectionStrings": {"DefaultConnection": "server=localhost;port=3306;database=itassets;user=aerospace_itam;password=*********;CharSet=utf8;"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.EntityFrameworkCore": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "Serilog": {"MinimumLevel": {"Override": {"Microsoft.AspNetCore": "Warning", "ItAssetsSystem.Core.Import.ImportService": "Debug"}}}, "FileStorageSettings": {"BasePhysicalPathForFilesVirtualDir": "wwwroot"}}