{"version": 3, "file": "config-provider.mjs", "sources": ["../../../../../../packages/components/config-provider/src/config-provider.ts"], "sourcesContent": ["import { defineComponent, renderSlot, watch } from 'vue'\nimport { provideGlobalConfig } from './hooks/use-global-config'\nimport { configProviderProps } from './config-provider-props'\n\nimport type { MessageConfigContext } from '@element-plus/components/message'\n\nexport const messageConfig: MessageConfigContext = {}\n\nconst ConfigProvider = defineComponent({\n  name: 'ElConfigProvider',\n  props: configProviderProps,\n\n  setup(props, { slots }) {\n    watch(\n      () => props.message,\n      (val) => {\n        Object.assign(messageConfig, val ?? {})\n      },\n      { immediate: true, deep: true }\n    )\n    const config = provideGlobalConfig(props)\n    return () => renderSlot(slots, 'default', { config: config?.value })\n  },\n})\nexport type ConfigProviderInstance = InstanceType<typeof ConfigProvider> &\n  unknown\n\nexport default ConfigProvider\n"], "names": [], "mappings": ";;;;AAGY,MAAC,aAAa,GAAG,GAAG;AAC3B,MAAC,cAAc,GAAG,eAAe,CAAC;AACvC,EAAE,IAAI,EAAE,kBAAkB;AAC1B,EAAE,KAAK,EAAE,mBAAmB;AAC5B,EAAE,KAAK,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE,EAAE;AAC1B,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,OAAO,EAAE,CAAC,GAAG,KAAK;AACxC,MAAM,MAAM,CAAC,MAAM,CAAC,aAAa,EAAE,GAAG,IAAI,IAAI,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC;AAC3D,KAAK,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;AACxC,IAAI,MAAM,MAAM,GAAG,mBAAmB,CAAC,KAAK,CAAC,CAAC;AAC9C,IAAI,OAAO,MAAM,UAAU,CAAC,KAAK,EAAE,SAAS,EAAE,EAAE,MAAM,EAAE,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;AAClG,GAAG;AACH,CAAC;;;;"}