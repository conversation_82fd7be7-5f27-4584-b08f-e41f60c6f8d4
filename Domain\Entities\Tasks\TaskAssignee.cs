// File: Domain/Entities/Tasks/TaskAssignee.cs
// Description: 任务负责人/参与者关联实体

using System;
using ItAssetsSystem.Models.Entities; // Corrected: Assuming User is here

namespace ItAssetsSystem.Domain.Entities.Tasks
{
    /// <summary>
    /// 任务多负责人/参与者关联表
    /// </summary>
    public class TaskAssignee
    {
        /// <summary>
        /// 分配记录主键ID (BIGINT)
        /// </summary>
        public long TaskAssigneeId { get; set; }

        /// <summary>
        /// 关联的任务ID (BIGINT)
        /// </summary>
        public long TaskId { get; set; }

        /// <summary>
        /// 关联的任务实体
        /// </summary>
        public virtual Task Task { get; set; } = null!;

        /// <summary>
        /// 被分配的用户ID (INT, 关联 users.Id)
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// 关联的用户实体
        /// </summary>
        public virtual User User { get; set; } = null!;

        /// <summary>
        /// 分配类型 (Responsible-负责人, Participant-参与者)
        /// </summary>
        public string AssignmentType { get; set; }

        /// <summary>
        /// 执行分配操作的用户ID (INT, 关联 users.Id)
        /// </summary>
        public int AssignedByUserId { get; set; }

        /// <summary>
        /// 关联的分配者用户实体
        /// </summary>
        public virtual User AssignedByUser { get; set; } = null!;

        /// <summary>
        /// 分配操作的时间戳
        /// </summary>
        public DateTime AssignmentTimestamp { get; set; }
    }
} 