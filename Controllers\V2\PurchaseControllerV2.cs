// File: Controllers/V2/PurchaseControllerV2.cs
// Description: 采购管理控制器V2 - 支持采购到备件库和资产的转化

using ItAssetsSystem.Application.Features.Purchase.Dtos;
using ItAssetsSystem.Application.Features.Purchase.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ItAssetsSystem.Services.Interfaces;
using ItAssetsSystem.Models.Entities.Gamification;
using ItAssetsSystem.Core.Abstractions;

namespace ItAssetsSystem.Controllers.V2
{
    /// <summary>
    /// 采购管理控制器V2 - 实现采购到备件库和资产的强关联转化
    /// </summary>
    [ApiController]
    [Route("api/v2/purchase")]
    // [Authorize] // 临时注释用于测试API端点
    public class PurchaseControllerV2 : ControllerBase
    {
        private readonly IPurchaseService _purchaseService;
        private readonly ILogger<PurchaseControllerV2> _logger;
        private readonly IUniversalGamificationService _universalGamificationService;
        private readonly ICurrentUserService _currentUserService;

        public PurchaseControllerV2(
            IPurchaseService purchaseService,
            ILogger<PurchaseControllerV2> logger,
            IUniversalGamificationService universalGamificationService,
            ICurrentUserService currentUserService)
        {
            _purchaseService = purchaseService;
            _logger = logger;
            _universalGamificationService = universalGamificationService;
            _currentUserService = currentUserService;
        }

        /// <summary>
        /// 获取采购订单列表（分页）
        /// </summary>
        /// <param name="query">查询参数</param>
        /// <returns>分页结果</returns>
        [HttpGet]
        public async Task<IActionResult> GetPurchaseOrders([FromQuery] PurchaseOrderQuery query)
        {
            try
            {
                _logger.LogInformation("获取采购订单列表，页码: {PageIndex}", query.PageIndex);
                
                var result = await _purchaseService.GetPurchaseOrdersAsync(query);
                
                return Ok(new
                {
                    success = true,
                    data = result.Items,
                    pagination = new
                    {
                        pageIndex = result.PageIndex,
                        pageSize = result.PageSize,
                        totalCount = result.TotalCount,
                        totalPages = result.TotalPages
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取采购订单列表失败");
                return StatusCode(500, new
                {
                    success = false,
                    message = "获取采购订单列表失败",
                    error = ex.Message
                });
            }
        }

        /// <summary>
        /// 获取采购订单详情
        /// </summary>
        /// <param name="id">订单ID</param>
        /// <returns>订单详情</returns>
        [HttpGet("{id}")]
        public async Task<IActionResult> GetPurchaseOrder(int id)
        {
            try
            {
                _logger.LogInformation("获取采购订单详情，ID: {Id}", id);
                
                var result = await _purchaseService.GetPurchaseOrderByIdAsync(id);
                
                return Ok(new
                {
                    success = true,
                    data = result
                });
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex, "采购订单不存在，ID: {Id}", id);
                return NotFound(new
                {
                    success = false,
                    message = ex.Message
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取采购订单详情失败，ID: {Id}", id);
                return StatusCode(500, new
                {
                    success = false,
                    message = "获取采购订单详情失败",
                    error = ex.Message
                });
            }
        }

        /// <summary>
        /// 创建采购订单
        /// </summary>
        /// <param name="request">创建请求</param>
        /// <returns>创建结果</returns>
        [HttpPost]
        public async Task<IActionResult> CreatePurchaseOrder([FromBody] CreatePurchaseOrderRequest request)
        {
            try
            {
                _logger.LogInformation("创建采购订单，供应商ID: {SupplierId}", request.SupplierId);
                
                var result = await _purchaseService.CreatePurchaseOrderAsync(request);

                // 触发采购订单创建奖励
                try
                {
                    await _universalGamificationService.TriggerBehaviorRewardAsync(
                        _currentUserService.UserId,
                        BehaviorCodes.PURCHASE_CREATED,
                        result.Id,
                        context: new {
                            SupplierId = request.SupplierId,
                            TotalAmount = request.Items?.Sum(i => i.Quantity * i.UnitPrice) ?? 0,
                            ItemCount = request.Items?.Count ?? 0
                        },
                        description: $"创建采购订单: {result.Id}"
                    );
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "触发采购订单创建奖励失败: OrderId={OrderId}", result.Id);
                }

                return CreatedAtAction(nameof(GetPurchaseOrder), new { id = result.Id }, new
                {
                    success = true,
                    data = result,
                    message = "采购订单创建成功"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建采购订单失败");
                return StatusCode(500, new
                {
                    success = false,
                    message = "创建采购订单失败",
                    error = ex.Message
                });
            }
        }

        /// <summary>
        /// 更新采购订单
        /// </summary>
        /// <param name="id">订单ID</param>
        /// <param name="request">更新请求</param>
        /// <returns>更新结果</returns>
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdatePurchaseOrder(int id, [FromBody] UpdatePurchaseOrderRequest request)
        {
            try
            {
                _logger.LogInformation("更新采购订单，ID: {Id}", id);
                
                var result = await _purchaseService.UpdatePurchaseOrderAsync(id, request);

                // 触发采购订单更新奖励
                try
                {
                    await _universalGamificationService.TriggerBehaviorRewardAsync(
                        _currentUserService.UserId,
                        BehaviorCodes.PURCHASE_UPDATED,
                        id,
                        context: new {
                            OrderId = result.Id,
                            Status = result.Status,
                            TotalAmount = result.TotalAmount
                        },
                        description: $"更新采购订单: {result.Id}"
                    );
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "触发采购订单更新奖励失败: OrderId={OrderId}", id);
                }

                return Ok(new
                {
                    success = true,
                    data = result,
                    message = "采购订单更新成功"
                });
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex, "采购订单不存在，ID: {Id}", id);
                return NotFound(new
                {
                    success = false,
                    message = ex.Message
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新采购订单失败，ID: {Id}", id);
                return StatusCode(500, new
                {
                    success = false,
                    message = "更新采购订单失败",
                    error = ex.Message
                });
            }
        }

        /// <summary>
        /// 删除采购订单
        /// </summary>
        /// <param name="id">订单ID</param>
        /// <returns>删除结果</returns>
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeletePurchaseOrder(int id)
        {
            try
            {
                _logger.LogInformation("删除采购订单，ID: {Id}", id);
                
                var result = await _purchaseService.DeletePurchaseOrderAsync(id);
                
                if (result)
                {
                    return Ok(new
                    {
                        success = true,
                        message = "采购订单删除成功"
                    });
                }
                else
                {
                    return NotFound(new
                    {
                        success = false,
                        message = "采购订单不存在"
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除采购订单失败，ID: {Id}", id);
                return StatusCode(500, new
                {
                    success = false,
                    message = "删除采购订单失败",
                    error = ex.Message
                });
            }
        }

        /// <summary>
        /// 确认采购订单到货
        /// </summary>
        /// <param name="id">订单ID</param>
        /// <param name="request">到货确认请求</param>
        /// <returns>确认结果</returns>
        [HttpPost("{id}/confirm-delivery")]
        public async Task<IActionResult> ConfirmDelivery(int id, [FromBody] ConfirmDeliveryRequest request)
        {
            try
            {
                _logger.LogInformation("确认采购订单到货，ID: {Id}", id);
                
                var result = await _purchaseService.ConfirmDeliveryAsync(id, request);
                
                return Ok(new
                {
                    success = true,
                    data = result,
                    message = "到货确认成功"
                });
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex, "采购订单不存在，ID: {Id}", id);
                return NotFound(new
                {
                    success = false,
                    message = ex.Message
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "确认到货失败，ID: {Id}", id);
                return StatusCode(500, new
                {
                    success = false,
                    message = "确认到货失败",
                    error = ex.Message
                });
            }
        }

        /// <summary>
        /// 处理采购物品到货（转换为资产或备件）
        /// </summary>
        /// <param name="id">订单ID</param>
        /// <param name="items">物品处理列表</param>
        /// <returns>处理结果</returns>
        [HttpPost("{id}/process-items")]
        public async Task<IActionResult> ProcessDeliveredItems(int id, [FromBody] List<ProcessDeliveredItemRequest> items)
        {
            try
            {
                _logger.LogInformation("处理采购物品到货转化，订单ID: {Id}", id);
                
                var result = await _purchaseService.ProcessDeliveredItemsAsync(id, items);
                
                return Ok(new
                {
                    success = result.Success,
                    data = result,
                    message = result.Message
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理采购物品失败，订单ID: {Id}", id);
                return StatusCode(500, new
                {
                    success = false,
                    message = "处理采购物品失败",
                    error = ex.Message
                });
            }
        }

        /// <summary>
        /// 获取供应商列表
        /// </summary>
        /// <returns>供应商列表</returns>
        [HttpGet("suppliers")]
        public async Task<IActionResult> GetSuppliers()
        {
            try
            {
                _logger.LogInformation("获取供应商列表");
                
                var result = await _purchaseService.GetSuppliersAsync();
                
                return Ok(new
                {
                    success = true,
                    data = result
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取供应商列表失败");
                return StatusCode(500, new
                {
                    success = false,
                    message = "获取供应商列表失败",
                    error = ex.Message
                });
            }
        }

        /// <summary>
        /// 获取可采购的资产类型列表（从备件类型读取）
        /// </summary>
        /// <returns>资产类型列表</returns>
        [HttpGet("asset-types")]
        public async Task<IActionResult> GetPurchasableAssetTypes()
        {
            try
            {
                _logger.LogInformation("获取可采购的资产类型列表");
                
                var result = await _purchaseService.GetPurchasableAssetTypesAsync();
                
                return Ok(new
                {
                    success = true,
                    data = result
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取资产类型列表失败");
                return StatusCode(500, new
                {
                    success = false,
                    message = "获取资产类型列表失败",
                    error = ex.Message
                });
            }
        }
    }
}
