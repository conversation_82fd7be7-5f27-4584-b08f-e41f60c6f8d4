-- 任务查询性能优化SQL脚本
-- 创建数据库索引以提升任务查询性能

-- 1. Tasks表核心索引
-- 状态+创建时间复合索引(最常用的查询组合)
CREATE INDEX IF NOT EXISTS idx_tasks_status_creation 
ON Tasks (Status, CreationTimestamp DESC);

-- 负责人+状态复合索引
CREATE INDEX IF NOT EXISTS idx_tasks_assignee_status 
ON Tasks (AssigneeUserId, Status, CreationTimestamp DESC);

-- 创建人+状态复合索引  
CREATE INDEX IF NOT EXISTS idx_tasks_creator_status
ON Tasks (CreatorUserId, Status, CreationTimestamp DESC);

-- 资产ID+状态复合索引
CREATE INDEX IF NOT EXISTS idx_tasks_asset_status
ON Tasks (AssetId, Status, CreationTimestamp DESC);

-- 位置ID+状态复合索引
CREATE INDEX IF NOT EXISTS idx_tasks_location_status
ON Tasks (LocationId, Status, CreationTimestamp DESC);

-- 任务类型+优先级+状态复合索引
CREATE INDEX IF NOT EXISTS idx_tasks_type_priority_status
ON Tasks (TaskType, Priority, Status, CreationTimestamp DESC);

-- 父任务ID索引
CREATE INDEX IF NOT EXISTS idx_tasks_parent_id
ON Tasks (ParentTaskId, CreationTimestamp DESC);

-- 项目ID索引
CREATE INDEX IF NOT EXISTS idx_tasks_project_id
ON Tasks (ProjectId, CreationTimestamp DESC);

-- 计划结束时间索引(用于逾期任务查询)
CREATE INDEX IF NOT EXISTS idx_tasks_plan_end_date
ON Tasks (PlanEndDate, Status);

-- 2. TaskAssignees表索引
-- 任务ID索引(用于批量加载负责人)
CREATE INDEX IF NOT EXISTS idx_task_assignees_task_id
ON TaskAssignees (TaskId, AssignmentType, UserId);

-- 用户ID索引(用于查询用户的所有任务)
CREATE INDEX IF NOT EXISTS idx_task_assignees_user_id
ON TaskAssignees (UserId, AssignmentType, TaskId);

-- 3. Comments表索引
-- 任务ID+创建时间索引
CREATE INDEX IF NOT EXISTS idx_comments_task_creation
ON Comments (TaskId, CreationTimestamp DESC);

-- 4. Attachments表索引
-- 任务ID+创建时间索引(排除评论附件)
CREATE INDEX IF NOT EXISTS idx_attachments_task_creation
ON Attachments (TaskId, CreationTimestamp DESC) 
WHERE CommentId IS NULL;

-- 评论ID索引
CREATE INDEX IF NOT EXISTS idx_attachments_comment_id
ON Attachments (CommentId, CreationTimestamp DESC)
WHERE CommentId IS NOT NULL;

-- 5. TaskHistories表索引
-- 任务ID+时间戳索引
CREATE INDEX IF NOT EXISTS idx_task_histories_task_timestamp
ON TaskHistories (TaskId, Timestamp DESC);

-- 6. PeriodicTaskSchedules表索引
-- 状态+下次生成时间索引(用于周期任务生成)
CREATE INDEX IF NOT EXISTS idx_periodic_schedules_status_next_gen
ON PeriodicTaskSchedules (Status, NextGenerationTime);

-- 创建人+状态索引
CREATE INDEX IF NOT EXISTS idx_periodic_schedules_creator_status
ON PeriodicTaskSchedules (CreatorUserId, Status, Name);

-- 循环类型+状态索引
CREATE INDEX IF NOT EXISTS idx_periodic_schedules_recurrence_status
ON PeriodicTaskSchedules (RecurrenceType, Status, NextGenerationTime);

-- 7. PdcaPlans表索引
-- 状态+创建时间索引
CREATE INDEX IF NOT EXISTS idx_pdca_plans_status_creation
ON PdcaPlans (Status, CreationTimestamp DESC);

-- 负责人+状态索引
CREATE INDEX IF NOT EXISTS idx_pdca_plans_responsible_status
ON PdcaPlans (ResponsiblePersonUserId, Status, CreationTimestamp DESC);

-- 创建人+状态索引
CREATE INDEX IF NOT EXISTS idx_pdca_plans_creator_status
ON PdcaPlans (CreatorUserId, Status, CreationTimestamp DESC);

-- 8. 覆盖索引优化(包含常用查询字段)
-- 任务列表查询覆盖索引
CREATE INDEX IF NOT EXISTS idx_tasks_list_covering
ON Tasks (Status, CreationTimestamp DESC, TaskId, Name, Priority, TaskType, AssigneeUserId, CreatorUserId, Progress, PlanEndDate);

-- 9. 分区索引建议(适用于大数据量)
-- 按月份分区的创建时间索引
-- CREATE INDEX IF NOT EXISTS idx_tasks_creation_monthly
-- ON Tasks (YEAR(CreationTimestamp), MONTH(CreationTimestamp), CreationTimestamp DESC);

-- 10. 查询优化统计信息更新
-- ANALYZE TABLE Tasks;
-- ANALYZE TABLE TaskAssignees;
-- ANALYZE TABLE Comments;
-- ANALYZE TABLE Attachments;
-- ANALYZE TABLE TaskHistories;

-- 验证索引创建结果
SELECT 
    INDEX_NAME,
    TABLE_NAME,
    COLUMN_NAME,
    NON_UNIQUE,
    SEQ_IN_INDEX
FROM INFORMATION_SCHEMA.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME IN ('Tasks', 'TaskAssignees', 'Comments', 'Attachments', 'TaskHistories', 'PeriodicTaskSchedules', 'PdcaPlans')
    AND INDEX_NAME LIKE 'idx_%'
ORDER BY TABLE_NAME, INDEX_NAME, SEQ_IN_INDEX;