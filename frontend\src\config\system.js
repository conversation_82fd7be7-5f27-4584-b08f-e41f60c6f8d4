/**
 * 系统全局配置文件
 * 文件路径: src/config/system.js
 * 功能描述: 存储系统基本配置信息，方便集中管理和修改
 */

const systemConfig = {
  // 系统名称
  name: 'IT资产管理系统',
  
  // 系统简称
  shortName: '管理',
  
  // 系统描述
  description: '企业IT资产全生命周期管理系统',
  
  // 版权信息
  copyright: '',
  
  // 系统版本
  version: '1.0.0',
  
  // 系统主题色
  primaryColor: '#409EFF',
  
  // API基础路径 - 与Vite代理和Axios设置保持一致
  apiBaseUrl: '/api',  // API基础路径，所有请求都会加上这个前缀
  
  // 是否使用模拟数据
  useMock: false, // 改为false，使用真实后端API
  
  // 模拟数据响应延迟(毫秒)
  mockTimeout: 500,
  
  // 每页默认显示数量
  defaultPageSize: 10,
  
  // 分页选项
  pageSizeOptions: [10, 20, 50, 100],
  
  // 上传文件大小限制(MB)
  maxUploadSize: 10,
  
  // 默认头像
  defaultAvatar: '/images/default-avatar.png',
  
  // Token相关配置
  tokenConfig: {
    // token在localStorage中的键名
    tokenKey: 'it_assets_token',
    // 刷新token在localStorage中的键名
    refreshTokenKey: 'it_assets_refresh_token'
  },
  
  // 应用名称
  appName: 'IT资产管理系统'
}

export default systemConfig 