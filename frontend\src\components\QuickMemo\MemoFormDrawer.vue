// File: frontend/src/components/QuickMemo/MemoFormDrawer.vue
// Description: Drawer component for creating and editing Quick Memos.

<template>
  <el-drawer
    :model-value="isVisible"
    :title="drawerTitle"
    direction="rtl"
    size="400px"
    @update:modelValue="handleUpdateModelValue"
    :before-close="handleBeforeClose"
    destroy-on-close
  >
    <div class="memo-form-drawer-content">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-position="top"
      >
        <el-form-item label="内容" prop="content">
          <el-input
            v-model="formData.content"
            type="textarea"
            :rows="8"
            placeholder="记录您的想法、任务或灵感..."
            clearable
            :disabled="isReadonly"
          />
        </el-form-item>
        <el-form-item label="分类" prop="categoryId">
          <el-select
            v-model="formData.categoryId"
            placeholder="选择或创建分类"
            filterable
            allow-create
            clearable
            default-first-option
            style="width:100%"
            @visible-change="handleCategoryDropdownVisible"
            :disabled="isReadonly"
          >
            <el-option label="无分类" :value="null" />
            <el-option
              v-for="cat in quickMemoStore.categories"
              :key="cat.id"
              :label="cat.name"
              :value="cat.id"
            />
          </el-select>
        </el-form-item>
        <!-- Add more fields if necessary, e.g., a more detailed content field -->
      </el-form>
    </div>
    <template #footer>
      <div style="flex: auto; text-align: right;">
        <el-button @click="cancelForm">{{ isReadonly ? '关闭' : '取消' }}</el-button>
        <el-button v-if="!isReadonly" type="primary" @click="confirmSave" :loading="isSaving">
          {{ isSaving ? '保存中...' : (currentMode === 'create' ? '快速保存' : '更新') }}
        </el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup>
import { ref, reactive, watch, computed, nextTick } from 'vue'
import { useQuickMemoStore } from '@/stores/modules/quickMemo'
import { ElMessage } from 'element-plus'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  memoId: {
    type: [String, Number],
    default: null
  },
  isEditing: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['close', 'saved']);

const quickMemoStore = useQuickMemoStore();
const formRef = ref(null);
const isSaving = ref(false);
const memoData = ref(null);

// 确定当前使用哪种控制方式：props或store
const isUsingProps = computed(() => props.visible !== undefined);

// 计算抽屉可见性
const isVisible = computed(() => {
  return isUsingProps.value ? props.visible : quickMemoStore.isMemoDrawerOpen;
});

// 计算当前模式：创建或编辑
const currentMode = computed(() => {
  if (isUsingProps.value) {
    return props.memoId && props.isEditing ? 'edit' : props.memoId ? 'view' : 'create';
  }
  return quickMemoStore.memoDrawerMode;
});

// 计算是否只读模式
const isReadonly = computed(() => {
  if (isUsingProps.value) {
    return props.memoId && !props.isEditing; // 有ID但不是编辑模式
  }
  return false; // 目前store模式不支持只读
});

// 计算抽屉标题
const drawerTitle = computed(() => {
  if (currentMode.value === 'create') return '新增随手记';
  if (currentMode.value === 'edit') return '编辑随手记';
  return '查看随手记';
});

const initialFormData = () => ({
  id: null,
  title: '',
  content: '',
  categoryId: null,
  // 添加其他必要字段
});

const formData = reactive(initialFormData());

const formRules = {
  content: [{ required: true, message: '请输入内容', trigger: 'blur' }],
  // categoryId 是可选的
};

const handleUpdateModelValue = (value) => {
  if (!value) {
    closeDrawer();
  }
};

const handleBeforeClose = (done) => {
  closeDrawer();
  done();
};

const closeDrawer = () => {
  if (isUsingProps.value) {
    emit('close');
  } else {
    quickMemoStore.closeMemoDrawer();
  }
};

const resetForm = () => {
  Object.assign(formData, initialFormData());
  if (formRef.value) {
    nextTick(() => {
      formRef.value.clearValidate();
    });
  }
};

// 处理外部memoId变化
watch(() => props.memoId, async (newId) => {
  if (isUsingProps.value && newId) {
    // 如果传入了ID，从列表中找到对应的备忘录
    const memo = quickMemoStore.quickMemos.find(m => m.id === newId);
    if (memo) {
      memoData.value = memo;
      Object.assign(formData, {
        id: memo.id,
        title: memo.title || '',
        content: memo.content || memo.title || '',
        categoryId: memo.categoryId || null
      });
    } else {
      // 如果在store中找不到，可以考虑从API获取
      ElMessage.warning('未找到对应的随手记');
      closeDrawer();
    }
  }
}, { immediate: true });

// 监听抽屉开关状态 (store方式)
watch(() => quickMemoStore.isMemoDrawerOpen, (isOpen) => {
  if (!isUsingProps.value && isOpen) {
    resetForm();
    if (quickMemoStore.memoDrawerMode === 'edit' && quickMemoStore.editingMemo) {
      const editingData = { 
        ...quickMemoStore.editingMemo,
        content: quickMemoStore.editingMemo.content || quickMemoStore.editingMemo.title || '',
        categoryId: quickMemoStore.editingMemo.categoryId ? quickMemoStore.editingMemo.categoryId.toString() : null
      };
      Object.assign(formData, editingData);
    }
  }
}, { immediate: true });

// 保证分类数据已加载
watch(() => isVisible.value, (visible) => {
  if (visible && quickMemoStore.categories.length === 0) {
    quickMemoStore.fetchCategories();
  }
  if (!visible) {
    resetForm();
  }
}, { immediate: true });

// Watch content to auto-generate title
watch(() => formData.content, (newContent) => {
  if (newContent) {
    // Simple text extraction: remove HTML tags (basic) and take first 15 chars
    const plainText = newContent.replace(/<[^>]*>/g, ''); // Basic HTML stripping
    formData.title = plainText.substring(0, 15);
  } else {
    formData.title = '';
  }
});

const cancelForm = () => {
  closeDrawer();
};

const confirmSave = async () => {
  if (!formRef.value) return;
  
  await formRef.value.validate(async (valid) => {
    if (valid) {
      isSaving.value = true;
      try {
        let categoryToSave = formData.categoryId;

        // 处理新分类
        if (formData.categoryId !== null && !quickMemoStore.categories.find(cat => cat.id === formData.categoryId)) {
          const newCategoryName = formData.categoryId;
          
          const createdCategory = await quickMemoStore.createCategory({ name: newCategoryName });
          if (createdCategory && createdCategory.id) {
            categoryToSave = createdCategory.id;
          } else {
            ElMessage.error('创建分类失败');
            isSaving.value = false;
            return;
          }
        }

        const dataToSave = { ...formData, categoryId: categoryToSave };

        // 创建或更新随手记
        if (currentMode.value === 'create') {
          await quickMemoStore.createQuickMemo(dataToSave);
          ElMessage.success('随手记已添加！');
          emit('saved');
        } else if (currentMode.value === 'edit' && formData.id) {
          await quickMemoStore.editQuickMemo(dataToSave);
          ElMessage.success('随手记已更新！');
          emit('saved');
        }
        
        // 关闭抽屉
        closeDrawer();
      } catch (error) {
        console.error('Save error:', error);
        ElMessage.error(currentMode.value === 'create' ? '添加失败' : '更新失败');
      }
      isSaving.value = false;
    } else {
      ElMessage.warning('请检查输入内容');
    }
  });
};

const handleCategoryDropdownVisible = (visible) => {
  if (visible && quickMemoStore.categories.length === 0) {
    quickMemoStore.fetchCategories();
  }
};

</script>

<style scoped>
.memo-form-drawer-content {
  padding: 10px 20px;
}

:deep(.el-drawer__header) {
  margin-bottom: 15px !important;
  padding: 16px 20px !important;
  border-bottom: 1px solid var(--border-color-light) !important;
  .el-drawer__title {
      color: var(--text-color);
      font-weight: 600;
  }
}
:deep(.el-drawer__body) {
  padding: 0 20px 15px 20px !important;
}
:deep(.el-drawer__footer) {
  padding: 12px 20px !important;
  border-top: 1px solid var(--border-color-light) !important;
  box-shadow: 0 -2px 5px rgba(0,0,0,0.04);
}
</style> 