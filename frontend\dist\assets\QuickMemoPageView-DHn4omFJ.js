import{_ as e,y as t,r as n,c as a,a as o,b as r,o as i,F as l,d as s,$ as c,t as u,aB as m,e as d,w as p,f,aJ as g,k as h,af as y,n as T,E as v,ax as E,h as A,z as D,p as _,bG as N}from"./index-CG5lHOPO.js";import{c as b,t as S,j as w,k as C}from"./en-US-BvtvdVHO.js";import{f as R,z as k}from"./zh-CN-B1csyosV.js";function M(e,t){const n=()=>b(null==t?void 0:t.in,NaN),a=function(e){const t={},n=e.split(x.dateTimeDelimiter);let a;if(n.length>2)return t;/:/.test(n[0])?a=n[0]:(t.date=n[0],a=n[1],x.timeZoneDelimiter.test(t.date)&&(t.date=e.split(x.timeZoneDelimiter)[0],a=e.substr(t.date.length,e.length)));if(a){const e=x.timezone.exec(a);e?(t.time=a.replace(e[1],""),t.timezone=e[1]):t.time=a}return t}(e);let o;if(a.date){const e=function(e,t){const n=new RegExp("^(?:(\\d{4}|[+-]\\d{"+(4+t)+"})|(\\d{2}|[+-]\\d{"+(2+t)+"})$)"),a=e.match(n);if(!a)return{year:NaN,restDateString:""};const o=a[1]?parseInt(a[1]):null,r=a[2]?parseInt(a[2]):null;return{year:null===r?o:100*r,restDateString:e.slice((a[1]||a[2]).length)}}(a.date,2);o=function(e,t){if(null===t)return new Date(NaN);const n=e.match(I);if(!n)return new Date(NaN);const a=!!n[4],o=z(n[1]),r=z(n[2])-1,i=z(n[3]),l=z(n[4]),s=z(n[5])-1;if(a)return function(e,t,n){return t>=1&&t<=53&&n>=0&&n<=6}(0,l,s)?function(e,t,n){const a=new Date(0);a.setUTCFullYear(e,0,4);const o=a.getUTCDay()||7,r=7*(t-1)+n+1-o;return a.setUTCDate(a.getUTCDate()+r),a}(t,l,s):new Date(NaN);{const e=new Date(0);return function(e,t,n){return t>=0&&t<=11&&n>=1&&n<=(F[t]||(P(e)?29:28))}(t,r,i)&&function(e,t){return t>=1&&t<=(P(e)?366:365)}(t,o)?(e.setUTCFullYear(t,r,Math.max(o,i)),e):new Date(NaN)}}(e.restDateString,e.year)}if(!o||isNaN(+o))return n();const r=+o;let i,l=0;if(a.time&&(l=function(e){const t=e.match(L);if(!t)return NaN;const n=U(t[1]),a=U(t[2]),o=U(t[3]);if(!function(e,t,n){if(24===e)return 0===t&&0===n;return n>=0&&n<60&&t>=0&&t<60&&e>=0&&e<25}(n,a,o))return NaN;return n*w+a*C+1e3*o}(a.time),isNaN(l)))return n();if(!a.timezone){const e=new Date(r+l),n=S(0,null==t?void 0:t.in);return n.setFullYear(e.getUTCFullYear(),e.getUTCMonth(),e.getUTCDate()),n.setHours(e.getUTCHours(),e.getUTCMinutes(),e.getUTCSeconds(),e.getUTCMilliseconds()),n}return i=function(e){if("Z"===e)return 0;const t=e.match(O);if(!t)return 0;const n="+"===t[1]?-1:1,a=parseInt(t[2]),o=t[3]&&parseInt(t[3])||0;if(!function(e,t){return t>=0&&t<=59}
/*! @license DOMPurify 3.2.6 | (c) Cure53 and other contributors | Released under the Apache license 2.0 and Mozilla Public License 2.0 | github.com/cure53/DOMPurify/blob/3.2.6/LICENSE */(0,o))return NaN;return n*(a*w+o*C)}(a.timezone),isNaN(i)?n():S(r+l+i,null==t?void 0:t.in)}const x={dateTimeDelimiter:/[T ]/,timeZoneDelimiter:/[Z ]/i,timezone:/([Z+-].*)$/},I=/^-?(?:(\d{3})|(\d{2})(?:-?(\d{2}))?|W(\d{2})(?:-?(\d{1}))?|)$/,L=/^(\d{2}(?:[.,]\d*)?)(?::?(\d{2}(?:[.,]\d*)?))?(?::?(\d{2}(?:[.,]\d*)?))?$/,O=/^([+-])(\d{2})(?::?(\d{2}))?$/;function z(e){return e?parseInt(e):1}function U(e){return e&&parseFloat(e.replace(",","."))||0}const F=[31,null,31,30,31,30,31,31,30,31,30,31];function P(e){return e%400==0||e%4==0&&e%100!=0}const{entries:H,setPrototypeOf:B,isFrozen:W,getPrototypeOf:G,getOwnPropertyDescriptor:Y}=Object;let{freeze:j,seal:$,create:V}=Object,{apply:X,construct:q}="undefined"!=typeof Reflect&&Reflect;j||(j=function(e){return e}),$||($=function(e){return e}),X||(X=function(e,t,n){return e.apply(t,n)}),q||(q=function(e,t){return new e(...t)});const K=me(Array.prototype.forEach),Z=me(Array.prototype.lastIndexOf),Q=me(Array.prototype.pop),J=me(Array.prototype.push),ee=me(Array.prototype.splice),te=me(String.prototype.toLowerCase),ne=me(String.prototype.toString),ae=me(String.prototype.match),oe=me(String.prototype.replace),re=me(String.prototype.indexOf),ie=me(String.prototype.trim),le=me(Object.prototype.hasOwnProperty),se=me(RegExp.prototype.test),ce=(ue=TypeError,function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return q(ue,t)});var ue;function me(e){return function(t){t instanceof RegExp&&(t.lastIndex=0);for(var n=arguments.length,a=new Array(n>1?n-1:0),o=1;o<n;o++)a[o-1]=arguments[o];return X(e,t,a)}}function de(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:te;B&&B(e,null);let a=t.length;for(;a--;){let o=t[a];if("string"==typeof o){const e=n(o);e!==o&&(W(t)||(t[a]=e),o=e)}e[o]=!0}return e}function pe(e){for(let t=0;t<e.length;t++){le(e,t)||(e[t]=null)}return e}function fe(e){const t=V(null);for(const[n,a]of H(e)){le(e,n)&&(Array.isArray(a)?t[n]=pe(a):a&&"object"==typeof a&&a.constructor===Object?t[n]=fe(a):t[n]=a)}return t}function ge(e,t){for(;null!==e;){const n=Y(e,t);if(n){if(n.get)return me(n.get);if("function"==typeof n.value)return me(n.value)}e=G(e)}return function(){return null}}const he=j(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),ye=j(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),Te=j(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),ve=j(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),Ee=j(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),Ae=j(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),De=j(["#text"]),_e=j(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","popover","popovertarget","popovertargetaction","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","wrap","xmlns","slot"]),Ne=j(["accent-height","accumulate","additive","alignment-baseline","amplitude","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","exponent","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","intercept","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","slope","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","tablevalues","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),be=j(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),Se=j(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),we=$(/\{\{[\w\W]*|[\w\W]*\}\}/gm),Ce=$(/<%[\w\W]*|[\w\W]*%>/gm),Re=$(/\$\{[\w\W]*/gm),ke=$(/^data-[\-\w.\u00B7-\uFFFF]+$/),Me=$(/^aria-[\-\w]+$/),xe=$(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp|matrix):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),Ie=$(/^(?:\w+script|data):/i),Le=$(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),Oe=$(/^html$/i),ze=$(/^[a-z][.\w]*(-[.\w]+)+$/i);var Ue=Object.freeze({__proto__:null,ARIA_ATTR:Me,ATTR_WHITESPACE:Le,CUSTOM_ELEMENT:ze,DATA_ATTR:ke,DOCTYPE_NAME:Oe,ERB_EXPR:Ce,IS_ALLOWED_URI:xe,IS_SCRIPT_OR_DATA:Ie,MUSTACHE_EXPR:we,TMPLIT_EXPR:Re});const Fe=1,Pe=3,He=7,Be=8,We=9;var Ge=function e(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"undefined"==typeof window?null:window;const n=t=>e(t);if(n.version="3.2.6",n.removed=[],!t||!t.document||t.document.nodeType!==We||!t.Element)return n.isSupported=!1,n;let{document:a}=t;const o=a,r=o.currentScript,{DocumentFragment:i,HTMLTemplateElement:l,Node:s,Element:c,NodeFilter:u,NamedNodeMap:m=t.NamedNodeMap||t.MozNamedAttrMap,HTMLFormElement:d,DOMParser:p,trustedTypes:f}=t,g=c.prototype,h=ge(g,"cloneNode"),y=ge(g,"remove"),T=ge(g,"nextSibling"),v=ge(g,"childNodes"),E=ge(g,"parentNode");if("function"==typeof l){const e=a.createElement("template");e.content&&e.content.ownerDocument&&(a=e.content.ownerDocument)}let A,D="";const{implementation:_,createNodeIterator:N,createDocumentFragment:b,getElementsByTagName:S}=a,{importNode:w}=o;let C={afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]};n.isSupported="function"==typeof H&&"function"==typeof E&&_&&void 0!==_.createHTMLDocument;const{MUSTACHE_EXPR:R,ERB_EXPR:k,TMPLIT_EXPR:M,DATA_ATTR:x,ARIA_ATTR:I,IS_SCRIPT_OR_DATA:L,ATTR_WHITESPACE:O,CUSTOM_ELEMENT:z}=Ue;let{IS_ALLOWED_URI:U}=Ue,F=null;const P=de({},[...he,...ye,...Te,...Ee,...De]);let B=null;const W=de({},[..._e,...Ne,...be,...Se]);let G=Object.seal(V(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),Y=null,$=null,X=!0,q=!0,ue=!1,me=!0,pe=!1,we=!0,Ce=!1,Re=!1,ke=!1,Me=!1,Ie=!1,Le=!1,ze=!0,Ge=!1,Ye=!0,je=!1,$e={},Ve=null;const Xe=de({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]);let qe=null;const Ke=de({},["audio","video","img","source","image","track"]);let Ze=null;const Qe=de({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),Je="http://www.w3.org/1998/Math/MathML",et="http://www.w3.org/2000/svg",tt="http://www.w3.org/1999/xhtml";let nt=tt,at=!1,ot=null;const rt=de({},[Je,et,tt],ne);let it=de({},["mi","mo","mn","ms","mtext"]),lt=de({},["annotation-xml"]);const st=de({},["title","style","font","a","script"]);let ct=null;const ut=["application/xhtml+xml","text/html"];let mt=null,dt=null;const pt=a.createElement("form"),ft=function(e){return e instanceof RegExp||e instanceof Function},gt=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(!dt||dt!==e){if(e&&"object"==typeof e||(e={}),e=fe(e),ct=-1===ut.indexOf(e.PARSER_MEDIA_TYPE)?"text/html":e.PARSER_MEDIA_TYPE,mt="application/xhtml+xml"===ct?ne:te,F=le(e,"ALLOWED_TAGS")?de({},e.ALLOWED_TAGS,mt):P,B=le(e,"ALLOWED_ATTR")?de({},e.ALLOWED_ATTR,mt):W,ot=le(e,"ALLOWED_NAMESPACES")?de({},e.ALLOWED_NAMESPACES,ne):rt,Ze=le(e,"ADD_URI_SAFE_ATTR")?de(fe(Qe),e.ADD_URI_SAFE_ATTR,mt):Qe,qe=le(e,"ADD_DATA_URI_TAGS")?de(fe(Ke),e.ADD_DATA_URI_TAGS,mt):Ke,Ve=le(e,"FORBID_CONTENTS")?de({},e.FORBID_CONTENTS,mt):Xe,Y=le(e,"FORBID_TAGS")?de({},e.FORBID_TAGS,mt):fe({}),$=le(e,"FORBID_ATTR")?de({},e.FORBID_ATTR,mt):fe({}),$e=!!le(e,"USE_PROFILES")&&e.USE_PROFILES,X=!1!==e.ALLOW_ARIA_ATTR,q=!1!==e.ALLOW_DATA_ATTR,ue=e.ALLOW_UNKNOWN_PROTOCOLS||!1,me=!1!==e.ALLOW_SELF_CLOSE_IN_ATTR,pe=e.SAFE_FOR_TEMPLATES||!1,we=!1!==e.SAFE_FOR_XML,Ce=e.WHOLE_DOCUMENT||!1,Me=e.RETURN_DOM||!1,Ie=e.RETURN_DOM_FRAGMENT||!1,Le=e.RETURN_TRUSTED_TYPE||!1,ke=e.FORCE_BODY||!1,ze=!1!==e.SANITIZE_DOM,Ge=e.SANITIZE_NAMED_PROPS||!1,Ye=!1!==e.KEEP_CONTENT,je=e.IN_PLACE||!1,U=e.ALLOWED_URI_REGEXP||xe,nt=e.NAMESPACE||tt,it=e.MATHML_TEXT_INTEGRATION_POINTS||it,lt=e.HTML_INTEGRATION_POINTS||lt,G=e.CUSTOM_ELEMENT_HANDLING||{},e.CUSTOM_ELEMENT_HANDLING&&ft(e.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(G.tagNameCheck=e.CUSTOM_ELEMENT_HANDLING.tagNameCheck),e.CUSTOM_ELEMENT_HANDLING&&ft(e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(G.attributeNameCheck=e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),e.CUSTOM_ELEMENT_HANDLING&&"boolean"==typeof e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements&&(G.allowCustomizedBuiltInElements=e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),pe&&(q=!1),Ie&&(Me=!0),$e&&(F=de({},De),B=[],!0===$e.html&&(de(F,he),de(B,_e)),!0===$e.svg&&(de(F,ye),de(B,Ne),de(B,Se)),!0===$e.svgFilters&&(de(F,Te),de(B,Ne),de(B,Se)),!0===$e.mathMl&&(de(F,Ee),de(B,be),de(B,Se))),e.ADD_TAGS&&(F===P&&(F=fe(F)),de(F,e.ADD_TAGS,mt)),e.ADD_ATTR&&(B===W&&(B=fe(B)),de(B,e.ADD_ATTR,mt)),e.ADD_URI_SAFE_ATTR&&de(Ze,e.ADD_URI_SAFE_ATTR,mt),e.FORBID_CONTENTS&&(Ve===Xe&&(Ve=fe(Ve)),de(Ve,e.FORBID_CONTENTS,mt)),Ye&&(F["#text"]=!0),Ce&&de(F,["html","head","body"]),F.table&&(de(F,["tbody"]),delete Y.tbody),e.TRUSTED_TYPES_POLICY){if("function"!=typeof e.TRUSTED_TYPES_POLICY.createHTML)throw ce('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if("function"!=typeof e.TRUSTED_TYPES_POLICY.createScriptURL)throw ce('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');A=e.TRUSTED_TYPES_POLICY,D=A.createHTML("")}else void 0===A&&(A=function(e,t){if("object"!=typeof e||"function"!=typeof e.createPolicy)return null;let n=null;const a="data-tt-policy-suffix";t&&t.hasAttribute(a)&&(n=t.getAttribute(a));const o="dompurify"+(n?"#"+n:"");try{return e.createPolicy(o,{createHTML:e=>e,createScriptURL:e=>e})}catch(r){return null}}(f,r)),null!==A&&"string"==typeof D&&(D=A.createHTML(""));j&&j(e),dt=e}},ht=de({},[...ye,...Te,...ve]),yt=de({},[...Ee,...Ae]),Tt=function(e){J(n.removed,{element:e});try{E(e).removeChild(e)}catch(t){y(e)}},vt=function(e,t){try{J(n.removed,{attribute:t.getAttributeNode(e),from:t})}catch(a){J(n.removed,{attribute:null,from:t})}if(t.removeAttribute(e),"is"===e)if(Me||Ie)try{Tt(t)}catch(a){}else try{t.setAttribute(e,"")}catch(a){}},Et=function(e){let t=null,n=null;if(ke)e="<remove></remove>"+e;else{const t=ae(e,/^[\r\n\t ]+/);n=t&&t[0]}"application/xhtml+xml"===ct&&nt===tt&&(e='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+e+"</body></html>");const o=A?A.createHTML(e):e;if(nt===tt)try{t=(new p).parseFromString(o,ct)}catch(i){}if(!t||!t.documentElement){t=_.createDocument(nt,"template",null);try{t.documentElement.innerHTML=at?D:o}catch(i){}}const r=t.body||t.documentElement;return e&&n&&r.insertBefore(a.createTextNode(n),r.childNodes[0]||null),nt===tt?S.call(t,Ce?"html":"body")[0]:Ce?t.documentElement:r},At=function(e){return N.call(e.ownerDocument||e,e,u.SHOW_ELEMENT|u.SHOW_COMMENT|u.SHOW_TEXT|u.SHOW_PROCESSING_INSTRUCTION|u.SHOW_CDATA_SECTION,null)},Dt=function(e){return e instanceof d&&("string"!=typeof e.nodeName||"string"!=typeof e.textContent||"function"!=typeof e.removeChild||!(e.attributes instanceof m)||"function"!=typeof e.removeAttribute||"function"!=typeof e.setAttribute||"string"!=typeof e.namespaceURI||"function"!=typeof e.insertBefore||"function"!=typeof e.hasChildNodes)},_t=function(e){return"function"==typeof s&&e instanceof s};function Nt(e,t,a){K(e,(e=>{e.call(n,t,a,dt)}))}const bt=function(e){let t=null;if(Nt(C.beforeSanitizeElements,e,null),Dt(e))return Tt(e),!0;const a=mt(e.nodeName);if(Nt(C.uponSanitizeElement,e,{tagName:a,allowedTags:F}),we&&e.hasChildNodes()&&!_t(e.firstElementChild)&&se(/<[/\w!]/g,e.innerHTML)&&se(/<[/\w!]/g,e.textContent))return Tt(e),!0;if(e.nodeType===He)return Tt(e),!0;if(we&&e.nodeType===Be&&se(/<[/\w]/g,e.data))return Tt(e),!0;if(!F[a]||Y[a]){if(!Y[a]&&wt(a)){if(G.tagNameCheck instanceof RegExp&&se(G.tagNameCheck,a))return!1;if(G.tagNameCheck instanceof Function&&G.tagNameCheck(a))return!1}if(Ye&&!Ve[a]){const t=E(e)||e.parentNode,n=v(e)||e.childNodes;if(n&&t){for(let a=n.length-1;a>=0;--a){const o=h(n[a],!0);o.__removalCount=(e.__removalCount||0)+1,t.insertBefore(o,T(e))}}}return Tt(e),!0}return e instanceof c&&!function(e){let t=E(e);t&&t.tagName||(t={namespaceURI:nt,tagName:"template"});const n=te(e.tagName),a=te(t.tagName);return!!ot[e.namespaceURI]&&(e.namespaceURI===et?t.namespaceURI===tt?"svg"===n:t.namespaceURI===Je?"svg"===n&&("annotation-xml"===a||it[a]):Boolean(ht[n]):e.namespaceURI===Je?t.namespaceURI===tt?"math"===n:t.namespaceURI===et?"math"===n&&lt[a]:Boolean(yt[n]):e.namespaceURI===tt?!(t.namespaceURI===et&&!lt[a])&&!(t.namespaceURI===Je&&!it[a])&&!yt[n]&&(st[n]||!ht[n]):!("application/xhtml+xml"!==ct||!ot[e.namespaceURI]))}(e)?(Tt(e),!0):"noscript"!==a&&"noembed"!==a&&"noframes"!==a||!se(/<\/no(script|embed|frames)/i,e.innerHTML)?(pe&&e.nodeType===Pe&&(t=e.textContent,K([R,k,M],(e=>{t=oe(t,e," ")})),e.textContent!==t&&(J(n.removed,{element:e.cloneNode()}),e.textContent=t)),Nt(C.afterSanitizeElements,e,null),!1):(Tt(e),!0)},St=function(e,t,n){if(ze&&("id"===t||"name"===t)&&(n in a||n in pt))return!1;if(q&&!$[t]&&se(x,t));else if(X&&se(I,t));else if(!B[t]||$[t]){if(!(wt(e)&&(G.tagNameCheck instanceof RegExp&&se(G.tagNameCheck,e)||G.tagNameCheck instanceof Function&&G.tagNameCheck(e))&&(G.attributeNameCheck instanceof RegExp&&se(G.attributeNameCheck,t)||G.attributeNameCheck instanceof Function&&G.attributeNameCheck(t))||"is"===t&&G.allowCustomizedBuiltInElements&&(G.tagNameCheck instanceof RegExp&&se(G.tagNameCheck,n)||G.tagNameCheck instanceof Function&&G.tagNameCheck(n))))return!1}else if(Ze[t]);else if(se(U,oe(n,O,"")));else if("src"!==t&&"xlink:href"!==t&&"href"!==t||"script"===e||0!==re(n,"data:")||!qe[e]){if(ue&&!se(L,oe(n,O,"")));else if(n)return!1}else;return!0},wt=function(e){return"annotation-xml"!==e&&ae(e,z)},Ct=function(e){Nt(C.beforeSanitizeAttributes,e,null);const{attributes:t}=e;if(!t||Dt(e))return;const a={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:B,forceKeepAttr:void 0};let o=t.length;for(;o--;){const i=t[o],{name:l,namespaceURI:s,value:c}=i,u=mt(l),m=c;let d="value"===l?m:ie(m);if(a.attrName=u,a.attrValue=d,a.keepAttr=!0,a.forceKeepAttr=void 0,Nt(C.uponSanitizeAttribute,e,a),d=a.attrValue,!Ge||"id"!==u&&"name"!==u||(vt(l,e),d="user-content-"+d),we&&se(/((--!?|])>)|<\/(style|title)/i,d)){vt(l,e);continue}if(a.forceKeepAttr)continue;if(!a.keepAttr){vt(l,e);continue}if(!me&&se(/\/>/i,d)){vt(l,e);continue}pe&&K([R,k,M],(e=>{d=oe(d,e," ")}));const p=mt(e.nodeName);if(St(p,u,d)){if(A&&"object"==typeof f&&"function"==typeof f.getAttributeType)if(s);else switch(f.getAttributeType(p,u)){case"TrustedHTML":d=A.createHTML(d);break;case"TrustedScriptURL":d=A.createScriptURL(d)}if(d!==m)try{s?e.setAttributeNS(s,l,d):e.setAttribute(l,d),Dt(e)?Tt(e):Q(n.removed)}catch(r){vt(l,e)}}else vt(l,e)}Nt(C.afterSanitizeAttributes,e,null)},Rt=function e(t){let n=null;const a=At(t);for(Nt(C.beforeSanitizeShadowDOM,t,null);n=a.nextNode();)Nt(C.uponSanitizeShadowNode,n,null),bt(n),Ct(n),n.content instanceof i&&e(n.content);Nt(C.afterSanitizeShadowDOM,t,null)};return n.sanitize=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a=null,r=null,l=null,c=null;if(at=!e,at&&(e="\x3c!--\x3e"),"string"!=typeof e&&!_t(e)){if("function"!=typeof e.toString)throw ce("toString is not a function");if("string"!=typeof(e=e.toString()))throw ce("dirty is not a string, aborting")}if(!n.isSupported)return e;if(Re||gt(t),n.removed=[],"string"==typeof e&&(je=!1),je){if(e.nodeName){const t=mt(e.nodeName);if(!F[t]||Y[t])throw ce("root node is forbidden and cannot be sanitized in-place")}}else if(e instanceof s)a=Et("\x3c!----\x3e"),r=a.ownerDocument.importNode(e,!0),r.nodeType===Fe&&"BODY"===r.nodeName||"HTML"===r.nodeName?a=r:a.appendChild(r);else{if(!Me&&!pe&&!Ce&&-1===e.indexOf("<"))return A&&Le?A.createHTML(e):e;if(a=Et(e),!a)return Me?null:Le?D:""}a&&ke&&Tt(a.firstChild);const u=At(je?e:a);for(;l=u.nextNode();)bt(l),Ct(l),l.content instanceof i&&Rt(l.content);if(je)return e;if(Me){if(Ie)for(c=b.call(a.ownerDocument);a.firstChild;)c.appendChild(a.firstChild);else c=a;return(B.shadowroot||B.shadowrootmode)&&(c=w.call(o,c,!0)),c}let m=Ce?a.outerHTML:a.innerHTML;return Ce&&F["!doctype"]&&a.ownerDocument&&a.ownerDocument.doctype&&a.ownerDocument.doctype.name&&se(Oe,a.ownerDocument.doctype.name)&&(m="<!DOCTYPE "+a.ownerDocument.doctype.name+">\n"+m),pe&&K([R,k,M],(e=>{m=oe(m,e," ")})),A&&Le?A.createHTML(m):m},n.setConfig=function(){gt(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}),Re=!0},n.clearConfig=function(){dt=null,Re=!1},n.isValidAttribute=function(e,t,n){dt||gt({});const a=mt(e),o=mt(t);return St(a,o,n)},n.addHook=function(e,t){"function"==typeof t&&J(C[e],t)},n.removeHook=function(e,t){if(void 0!==t){const n=Z(C[e],t);return-1===n?void 0:ee(C[e],n,1)[0]}return Q(C[e])},n.removeHooks=function(e){C[e]=[]},n.removeAllHooks=function(){C={afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]}},n}();const Ye={class:"memo-title"},je=["innerHTML"],$e={class:"footer-info"},Ve={key:0,class:"memo-creator"},Xe={class:"memo-time"},qe={key:1,class:"memo-time memo-updated-time"},Ke={class:"actions"},Ze=e({__name:"StickyNote",props:{memoData:{type:Object,required:!0}},setup(e){const A=e,D=t(),_=n(!1),N=n(""),b=n(""),S=n(null),w=n(null),C=n(""),x=n(""),I=a((()=>A.memoData.content&&A.memoData.content!==A.memoData.title)),L=a((()=>{if(A.memoData.content){const e=A.memoData.content.replace(/\\n/g,"<br>");return Ge.sanitize(e)}return""})),O=["#FFF9C4","#FFCDD2","#C8E6C9","#BBDEFB","#D1C4E9","#B2DFDB","#FFE0B2","#F0F4C3"],z=()=>O[Math.floor(Math.random()*O.length)],U=a((()=>{let e=z();A.memoData.category&&A.memoData.category.color?/^#([0-9A-F]{3}){1,2}$/i.test(A.memoData.category.color)&&(e=A.memoData.category.color):A.memoData.color&&/^#([0-9A-F]{3}){1,2}$/i.test(A.memoData.color)&&(e=A.memoData.color);const t=`${e}${Math.floor(178.5).toString(16).padStart(2,"0")}`;return{backgroundColor:/^#([0-9A-F]{3}){1,2}$/i.test(e)?t:`${z()}B3`}})),F=a((()=>{var e;if(!A.memoData.createdAt)return"未知";try{return R(M(A.memoData.createdAt),{addSuffix:!0,locale:k})}catch(t){return(null==(e=A.memoData.createdAt)?void 0:e.split("T")[0])||"未知时间"}})),P=a((()=>{var e;if(!A.memoData.updatedAt)return"";try{const e=M(A.memoData.createdAt),t=M(A.memoData.updatedAt);return Math.abs(t.getTime()-e.getTime())<6e4?R(e,{addSuffix:!0,locale:k}):R(t,{addSuffix:!0,locale:k})}catch(t){return(null==(e=A.memoData.updatedAt)?void 0:e.split("T")[0])||"未知时间"}})),H=e=>{var t;(null==(t=w.value)?void 0:t.contains(e.relatedTarget))||W()},B=()=>{N.value=C.value,b.value=x.value,_.value=!1},W=async()=>{if(!_.value)return;const e=N.value.trim(),t=I.value&&b.value.trim()||e,n=I.value&&x.value.trim()||C.value.trim();if(e===C.value.trim()&&t===n)return void(_.value=!1);if(!e)return void v.warning("标题不能为空");const a={title:e,content:t,positionX:A.memoData.positionX,positionY:A.memoData.positionY,sizeWidth:A.memoData.sizeWidth,sizeHeight:A.memoData.sizeHeight,zIndex:A.memoData.zIndex,isPinned:A.memoData.isPinned,categoryId:A.memoData.categoryId};try{await D.editQuickMemo({...A.memoData,...a}),v.success("更新成功")}catch(o){v.error(`更新失败: ${o.message||"请重试"}`),N.value=C.value,b.value=x.value}finally{_.value=!1}},G=async()=>{try{await D.removeQuickMemo(A.memoData.id),v.success("删除成功")}catch(e){v.error(`删除失败: ${e.message||"请重试"}`)}};return(t,n)=>{const a=o("el-icon"),v=o("el-popconfirm"),D=o("el-input");return i(),r("div",{class:"memo-card",style:T(U.value),onClick:n[8]||(n[8]=e=>_.value?null:void(_.value||(C.value=A.memoData.title,x.value=A.memoData.content||"",N.value=A.memoData.title,b.value=A.memoData.content||"",_.value=!0,E((()=>{var e,t,n;null==(e=S.value)||e.focus(),null==(n=null==(t=S.value)?void 0:t.textarea)||n.select()}))))),tabindex:"0"},[_.value?(i(),r("div",{key:1,class:"editing-container",ref_key:"editingContainerRef",ref:w,onFocusout:H},[d(D,{type:"textarea",modelValue:N.value,"onUpdate:modelValue":n[4]||(n[4]=e=>N.value=e),autosize:"",ref_key:"titleInputRef",ref:S,placeholder:"便签标题",class:"editing-title-input",onMousedown:n[5]||(n[5]=m((()=>{}),["stop"])),onKeydown:y(m(B,["stop"]),["esc"])},null,8,["modelValue","onKeydown"]),I.value?(i(),h(D,{key:0,type:"textarea",modelValue:b.value,"onUpdate:modelValue":n[6]||(n[6]=e=>b.value=e),autosize:{minRows:2,maxRows:6},placeholder:"便签内容 (可选)",class:"editing-content-input",onMousedown:n[7]||(n[7]=m((()=>{}),["stop"])),onKeydown:y(m(B,["stop"]),["esc"])},null,8,["modelValue","onKeydown"])):c("",!0)],544)):(i(),r(l,{key:0},[s("div",{class:"card-header",onMousedown:n[0]||(n[0]=m((()=>{}),["stop"]))},[s("h4",Ye,u(e.memoData.title),1)],32),e.memoData.content&&e.memoData.content!==e.memoData.title?(i(),r("div",{key:0,class:"card-content",innerHTML:L.value,onMousedown:n[1]||(n[1]=m((()=>{}),["stop"]))},null,40,je)):c("",!0),s("div",{class:"card-footer",onMousedown:n[3]||(n[3]=m((()=>{}),["stop"]))},[s("div",$e,[e.memoData.user&&e.memoData.user.name?(i(),r("span",Ve," 创建者: "+u(e.memoData.user.name),1)):c("",!0),s("span",Xe,"创建: "+u(F.value),1),e.memoData.updatedAt&&P.value!==F.value?(i(),r("span",qe," 更新: "+u(P.value),1)):c("",!0)]),s("div",Ke,[d(v,{title:"确定删除这个便签吗?","confirm-button-text":"确定","cancel-button-text":"取消",onConfirm:G,teleported:!1,trigger:"click"},{reference:p((()=>[d(a,{class:"action-icon delete-icon",onClick:n[2]||(n[2]=m((()=>{}),["stop"])),title:"删除便签"},{default:p((()=>[d(f(g))])),_:1})])),_:1})])],32)],64))],4)}}},[["__scopeId","data-v-908c4b8e"]]),Qe={class:"memo-board-wrapper"},Je={key:0,class:"empty-board-message"},et=e({__name:"QuickMemoBoard",setup(e){const u=t(),m=n(null),p=a((()=>u.quickMemos)),g=a((()=>[...p.value].sort(((e,t)=>e.isPinned&&!t.isPinned?-1:!e.isPinned&&t.isPinned?1:new Date(t.updatedAt||t.createdAt)-new Date(e.updatedAt||e.createdAt)))));return(e,t)=>{const n=o("el-empty");return i(),r("div",Qe,[s("div",{class:"memo-board",ref_key:"memoBoardRef",ref:m},[(i(!0),r(l,null,A(g.value,(e=>(i(),h(Ze,{key:e.id,"memo-data":e},null,8,["memo-data"])))),128)),0!==p.value.length||f(u).isLoading?c("",!0):(i(),r("div",Je,[d(n,{description:"还没有随手记，快写下你的灵感吧！"})]))],512)])}}},[["__scopeId","data-v-257b0869"]]),tt={class:"top-input-bar"},nt={class:"content-area"},at={class:"content-panel"},ot=e({__name:"QuickMemoPageView",setup(e){const a=t(),l=n(null),c=n(""),u=async()=>{if(c.value.trim())try{const e=Math.floor(300*Math.random())+50,t=Math.floor(200*Math.random())+50;await a.createQuickMemo({title:c.value,content:c.value,positionX:e,positionY:t,sizeWidth:240,sizeHeight:150,isPinned:!1,categoryId:null}),c.value="",v.success("便签已添加")}catch(e){v.error("添加便签失败")}else v.warning("便签内容不能为空")};return D((()=>{a.fetchQuickMemos()})),(e,t)=>{const n=o("el-input"),a=o("el-button");return i(),r("div",{class:"memo-page-container",ref_key:"pageContainer",ref:l},[s("div",tt,[d(n,{modelValue:c.value,"onUpdate:modelValue":t[0]||(t[0]=e=>c.value=e),placeholder:"写点什么...",clearable:"",class:"input-new-memo",onKeyup:y(u,["enter"]),size:"large"},null,8,["modelValue"]),d(a,{type:"primary",onClick:u,class:"add-memo-button",size:"large"},{default:p((()=>t[1]||(t[1]=[_("添加便签")]))),_:1})]),s("div",nt,[s("div",at,[d(et)])]),d(N)],512)}}},[["__scopeId","data-v-b8125fe0"]]);export{ot as default};
