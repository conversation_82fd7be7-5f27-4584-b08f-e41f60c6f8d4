#nullable enable
// IT资产管理系统 - 用户控制器
// 文件路径: /Controllers/UserController.cs
// 功能: 提供用户相关API

using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using ItAssetsSystem.Models.Entities;
using ItAssetsSystem.Models.Enums;
using ItAssetsSystem.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using System.Threading.Tasks;
using System.Security.Cryptography;
using System.Text;
using ItAssetsSystem.Services;
using Microsoft.AspNetCore.Http;
using System.IO;
using Microsoft.AspNetCore.Authorization;
using ItAssetsSystem.Models.Dtos;

namespace ItAssetsSystem.Controllers
{
    /// <summary>
    /// 用户控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class UserController : ControllerBase
    {
        private readonly ILogger<UserController> _logger;
        private readonly AppDbContext _context;
        private readonly ITokenService _tokenService;

        public UserController(
            ILogger<UserController> logger, 
            AppDbContext context,
            ITokenService tokenService)
        {
            _logger = logger;
            _context = context;
            _tokenService = tokenService;
        }

        /// <summary>
        /// 获取所有激活用户
        /// </summary>
        /// <returns>用户列表</returns>
        [HttpGet]
        public async Task<IActionResult> GetAll()
        {
            _logger.LogInformation("获取所有激活用户");
            try
            {
                // 检查是否可以连接数据库，如果不可用则返回模拟应答
                bool isDatabaseAvailable = await CheckDatabaseConnectionAsync();
                
                if (!isDatabaseAvailable)
                {
                    _logger.LogWarning("数据库连接不可用，返回模拟用户列表");
                    var mockUsers = new List<object>
                    {
                        new { id = 1, username = "admin", name = "系统管理员", position = "技术主管", departmentId = 1, departmentName = "信息技术部", email = "<EMAIL>", isActive = true },
                        new { id = 2, username = "zhangsan", name = "张三", position = "财务经理", departmentId = 2, departmentName = "财务部", email = "<EMAIL>", isActive = true },
                        new { id = 3, username = "lisi", name = "李四", position = "人事专员", departmentId = 3, departmentName = "人力资源部", email = "<EMAIL>", isActive = true }
                    };
                    return Ok(new { success = true, data = mockUsers, message = "注意: 这是模拟数据，数据库连接不可用" });
                }
                
                // 只获取激活用户，并返回 isActive 字段
                var users = await _context.Users
                    .Where(u => u.IsActive)
                    .Select(u => new
                    {
                        id = u.Id,
                        username = u.Username,
                        name = u.Name,
                        position = u.Position,
                        departmentId = u.DepartmentId,
                        departmentName = u.Department != null ? u.Department.Name : null,
                        email = u.Email,
                        isActive = u.IsActive
                    })
                    .ToListAsync();
                
                return Ok(new { success = true, data = users });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户列表出错");
                return StatusCode(500, new { success = false, message = "获取用户列表出错: " + ex.Message });
            }
        }

        /// <summary>
        /// 根据ID获取用户
        /// </summary>
        /// <param name="id">用户ID</param>
        /// <returns>用户详情</returns>
        [HttpGet("{id}")]
        public async Task<IActionResult> GetById(int id)
        {
            _logger.LogInformation($"获取用户ID: {id}");
            try
            {
                // 检查是否可以连接数据库，如果不可用则返回模拟应答
                bool isDatabaseAvailable = await CheckDatabaseConnectionAsync();
                
                if (!isDatabaseAvailable)
                {
                    _logger.LogWarning($"数据库连接不可用，返回模拟用户数据，ID: {id}");
                    
                    // 根据ID返回不同的模拟数据
                    if (id == 1)
                    {
                        var mockUser = new
                        {
                            id = 1,
                            username = "admin",
                            name = "系统管理员",
                            position = "技术主管",
                            departmentId = 1,
                            departmentName = "信息技术部",
                            email = "<EMAIL>",
                            mobile = "13800138000",
                            gender = 1,
                            isActive = true,
                            roles = new[] { new { id = 1, name = "管理员" } }
                        };
                        return Ok(new { success = true, data = mockUser, message = "注意: 这是模拟数据，数据库连接不可用" });
                    }
                    
                    return NotFound(new { success = false, message = $"未找到ID为{id}的用户（模拟数据）" });
                }
                
                // 从数据库获取用户
                var user = await _context.Users
                    .Include(u => u.Department)
                    .Include(u => u.UserRoles)
                        .ThenInclude(ur => ur.Role)
                    .FirstOrDefaultAsync(u => u.Id == id);
                
                if (user == null)
                {
                    return NotFound(new { success = false, message = $"未找到ID为{id}的用户" });
                }
                
                var userDetails = new
                {
                    id = user.Id,
                    username = user.Username,
                    name = user.Name,
                    position = user.Position,
                    departmentId = user.DepartmentId,
                    departmentName = user.Department?.Name,
                    email = user.Email,
                    mobile = user.Mobile,
                    gender = user.Gender,
                    isActive = user.IsActive,
                    roles = user.UserRoles.Select(ur => new { id = ur.RoleId, name = ur.Role.Name }).ToList()
                };
                
                return Ok(new { success = true, data = userDetails });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取用户ID {id} 出错");
                return StatusCode(500, new { success = false, message = $"获取用户ID {id} 出错: " + ex.Message });
            }
        }

        /// <summary>
        /// 用户登录
        /// </summary>
        /// <param name="model">登录信息</param>
        /// <returns>登录结果</returns>
        [HttpPost("login")]
        [AllowAnonymous]
        public async Task<IActionResult> Login([FromBody] LoginModel model)
        {
            try
            {
                // 验证用户名和密码
                var user = await _context.Users
                    .Include(u => u.UserRoles)
                        .ThenInclude(ur => ur.Role)
                    .Select(u => new {
                        u.Id,
                        u.Username,
                        u.Name,
                        u.IsActive,
                        u.LastLoginAt,
                        u.CreatedAt,
                        UserRoles = u.UserRoles.Select(ur => new {
                            ur.Role.Name
                        })
                    })
                    .FirstOrDefaultAsync(u => u.Username == model.Username);

                if (user == null)
                {
                    return BadRequest(new { success = false, message = "用户名不存在" });
                }

                // 简化的密码验证，只要密码是123456就通过
                if (model.Password != "123456")
                {
                    return BadRequest(new { success = false, message = "密码错误" });
                }

                // 生成Token
                var token = _tokenService.GenerateToken(new User { Id = user.Id, Username = user.Username });
                
                return Ok(new 
                { 
                    success = true,
                    data = new
                    {
                        token = token,
                        user = new
                        {
                            id = user.Id,
                            username = user.Username,
                            name = user.Name,
                            roles = user.UserRoles.Select(ur => ur.Name).ToList()
                        }
                    },
                    message = "登录成功"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "用户登录出错");
                return StatusCode(500, new { success = false, message = "登录出错: " + ex.Message });
            }
        }
        
        /// <summary>
        /// 用户退出登录
        /// </summary>
        /// <returns>退出结果</returns>
        [HttpPost("logout")]
        [AllowAnonymous]
        public IActionResult Logout()
        {
            _logger.LogInformation("用户退出登录");
            
            // 由于JWT是无状态的，服务器端不需要特别的处理
            // 客户端只需要清除本地存储的token即可
            // 这里返回成功响应即可
            
            return Ok(new { success = true, message = "退出登录成功" });
        }
        
        /// <summary>
        /// 添加新用户
        /// </summary>
        /// <param name="userModel">用户信息</param>
        /// <returns>添加结果</returns>
        [HttpPost]
        public async Task<IActionResult> AddUser([FromBody] UserCreateModel userModel)
        {
            _logger.LogInformation($"添加新用户: {userModel.Username}");
            
            try
            {
                // 检查是否可以连接数据库，如果不可用则返回模拟应答
                bool isDatabaseAvailable = await CheckDatabaseConnectionAsync();
                
                if (!isDatabaseAvailable)
                {
                    _logger.LogWarning("数据库连接不可用，返回模拟成功响应");
                    return Ok(new { 
                        success = true, 
                        data = new { 
                            id = 100, 
                            username = userModel.Username, 
                            name = userModel.Name 
                        },
                        message = "注意: 这是模拟数据，数据库连接不可用"
                    });
                }
                
                // 检查用户名是否已存在
                if (await _context.Users.AnyAsync(u => u.Username == userModel.Username))
                {
                    return BadRequest(new { success = false, message = "用户名已存在" });
                }
                
                // 生成密码盐和哈希
                string salt = GenerateRandomSalt();
                string passwordHash = HashPassword(userModel.Password, salt);
                
                // 创建新用户对象
                var user = new User
                {
                    Username = userModel.Username,
                    Name = userModel.Name,
                    Position = userModel.Position,
                    Email = userModel.Email,
                    DepartmentId = userModel.DepartmentId,
                    Mobile = userModel.Mobile,
                    Gender = (Gender)userModel.Gender,
                    PasswordHash = passwordHash,
                    SecurityStamp = salt,
                    IsActive = true,
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now
                };
                
                // 添加到数据库
                _context.Users.Add(user);
                await _context.SaveChangesAsync();
                
                // 如果提供了角色ID，添加用户角色关联
                if (userModel.RoleIds != null && userModel.RoleIds.Count > 0)
                {
                    foreach (var roleId in userModel.RoleIds)
                    {
                        var userRole = new UserRole
                        {
                            UserId = user.Id,
                            RoleId = roleId
                        };
                        _context.UserRoles.Add(userRole);
                    }
                    await _context.SaveChangesAsync();
                }
                
                // 返回新用户信息
                return Ok(new 
                { 
                    success = true, 
                    data = new 
                    { 
                        id = user.Id, 
                        username = user.Username, 
                        name = user.Name 
                    },
                    message = "用户添加成功"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "添加用户出错");
                return StatusCode(500, new { success = false, message = "添加用户出错: " + ex.Message });
            }
        }

        /// <summary>
        /// 检查数据库连接是否可用
        /// </summary>
        private async Task<bool> CheckDatabaseConnectionAsync()
        {
            try
            {
                // 尝试执行一个简单的查询来测试数据库连接
                return await _context.Database.CanConnectAsync();
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 生成随机盐值
        /// </summary>
        private string GenerateRandomSalt()
        {
            byte[] salt = new byte[16];
            using (var rng = RandomNumberGenerator.Create())
            {
                rng.GetBytes(salt);
            }
            return Convert.ToBase64String(salt);
        }

        /// <summary>
        /// 使用盐值哈希密码
        /// </summary>
        private string HashPassword(string password, string salt)
        {
            if (string.IsNullOrEmpty(password))
            {
                return string.Empty;
            }

            byte[] saltBytes = Convert.FromBase64String(salt);
            byte[] passwordBytes = Encoding.UTF8.GetBytes(password);
            
            // 创建盐化密码字节数组
            byte[] saltedPasswordBytes = new byte[saltBytes.Length + passwordBytes.Length];
            saltBytes.CopyTo(saltedPasswordBytes, 0);
            passwordBytes.CopyTo(saltedPasswordBytes, saltBytes.Length);
            
            // 计算哈希
            using (var sha256 = SHA256.Create())
            {
                byte[] hashBytes = sha256.ComputeHash(saltedPasswordBytes);
                return Convert.ToBase64String(hashBytes);
            }
        }

        [HttpPost("reset-password")]
        public async Task<IActionResult> ResetPassword([FromBody] int userId)
        {
            try
            {
                var user = await _context.Users.FindAsync(userId);
                if (user == null)
                {
                    return NotFound(new { success = false, message = "用户不存在" });
                }

                string defaultPassword = "123456";
                string salt = GenerateRandomSalt();
                string passwordHash = HashPassword(defaultPassword, salt);

                user.PasswordHash = passwordHash;
                user.SecurityStamp = salt;

                await _context.SaveChangesAsync();

                return Ok(new { 
                    success = true, 
                    message = "密码重置成功",
                    data = new {
                        username = user.Username,
                        defaultPassword = defaultPassword
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "重置密码失败");
                return StatusCode(500, new { success = false, message = "重置密码失败：" + ex.Message });
            }
        }

        [HttpPost("reset-all-passwords")]
        public async Task<IActionResult> ResetAllPasswords()
        {
            try
            {
                var users = await _context.Users.ToListAsync();
                foreach (var user in users)
                {
                    string defaultPassword = "123456";
                    string salt = GenerateRandomSalt();
                    string passwordHash = HashPassword(defaultPassword, salt);

                    user.PasswordHash = passwordHash;
                    user.SecurityStamp = salt;
                }

                await _context.SaveChangesAsync();

                return Ok(new { 
                    success = true, 
                    message = "所有用户密码重置成功",
                    data = new {
                        defaultPassword = "123456",
                        userCount = users.Count
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "重置所有密码失败");
                return StatusCode(500, new { success = false, message = "重置所有密码失败：" + ex.Message });
            }
        }

        /// <summary>
        /// 获取当前用户信息
        /// </summary>
        /// <returns>当前用户信息</returns>
        [HttpGet("profile")]
        public async Task<IActionResult> GetProfile()
        {
            try
            {
                // 从token中获取用户ID
                int userId = 0;
                
                // 尝试从Claims中获取用户ID
                var userIdClaim = User?.Claims?.FirstOrDefault(c => c.Type == "uid" || c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier");
                if (userIdClaim != null && int.TryParse(userIdClaim.Value, out int id))
                {
                    userId = id;
                }
                else
                {
                    // 如果无法从Claims获取，则使用默认值1（测试用）
                    userId = 1;
                    _logger.LogWarning("无法从Claims获取用户ID，使用默认值1");
                }
                
                var user = await _context.Users
                    .Include(u => u.Department)
                    .Include(u => u.UserRoles)
                        .ThenInclude(ur => ur.Role)
                    .FirstOrDefaultAsync(u => u.Id == userId);
                
                if (user == null)
                {
                    return NotFound(new { success = false, message = "未找到当前用户信息" });
                }
                
                var userProfile = new
                {
                    id = user.Id,
                    username = user.Username,
                    name = user.Name,
                    position = user.Position,
                    departmentId = user.DepartmentId,
                    department = user.Department?.Name,
                    email = user.Email,
                    phone = user.Mobile,
                    gender = user.Gender,
                    avatar = user.Avatar ?? "", // 用户头像
                    roles = user.UserRoles.Select(ur => ur.Role.Name).ToList(),
                    lastLogin = user.LastLoginAt
                };
                
                return Ok(new { success = true, data = userProfile });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取当前用户信息出错");
                return StatusCode(500, new { success = false, message = "获取当前用户信息出错: " + ex.Message });
            }
        }
        
        /// <summary>
        /// 更新当前用户个人信息
        /// </summary>
        /// <param name="profileModel">个人信息</param>
        /// <returns>更新结果</returns>
        [HttpPut("profile")]
        public async Task<IActionResult> UpdateProfile([FromBody] UserProfileUpdateModel profileModel)
        {
            try
            {
                // 从token中获取用户ID
                int userId = 0;
                
                // 尝试从Claims中获取用户ID
                var userIdClaim = User?.Claims?.FirstOrDefault(c => c.Type == "uid" || c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier");
                if (userIdClaim != null && int.TryParse(userIdClaim.Value, out int id))
                {
                    userId = id;
                }
                else
                {
                    // 如果无法从Claims获取，则使用默认值1（测试用）
                    userId = 1;
                    _logger.LogWarning("无法从Claims获取用户ID，使用默认值1");
                }
                
                var user = await _context.Users.FindAsync(userId);
                if (user == null)
                {
                    return NotFound(new { success = false, message = "未找到当前用户" });
                }
                
                // 更新用户信息
                if (!string.IsNullOrEmpty(profileModel.Name))
                {
                    user.Name = profileModel.Name;
                }
                
                if (!string.IsNullOrEmpty(profileModel.Email))
                {
                    user.Email = profileModel.Email;
                }
                
                if (!string.IsNullOrEmpty(profileModel.Phone))
                {
                    user.Mobile = profileModel.Phone;
                }
                
                // 如果有其他字段也可以添加...
                
                user.UpdatedAt = DateTime.Now;
                
                await _context.SaveChangesAsync();
                
                return Ok(new { success = true, message = "个人信息更新成功" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新个人信息出错");
                return StatusCode(500, new { success = false, message = "更新个人信息出错: " + ex.Message });
            }
        }
        
        /// <summary>
        /// 上传用户头像
        /// </summary>
        /// <returns>上传结果</returns>
        [HttpPost("avatar")]
        public async Task<IActionResult> UploadAvatar([FromForm] IFormFile file)
        {
            try
            {
                if (file == null || file.Length == 0)
                {
                    return BadRequest(new { success = false, message = "未上传有效的头像文件" });
                }
                
                // 从token中获取用户ID
                int userId = 0;
                
                // 尝试从Claims中获取用户ID
                var userIdClaim = User?.Claims?.FirstOrDefault(c => c.Type == "uid" || c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier");
                if (userIdClaim != null && int.TryParse(userIdClaim.Value, out int id))
                {
                    userId = id;
                }
                else
                {
                    // 如果无法从Claims获取，则使用默认值1（测试用）
                    userId = 1;
                    _logger.LogWarning("无法从Claims获取用户ID，使用默认值1");
                }
                
                var user = await _context.Users.FindAsync(userId);
                if (user == null)
                {
                    return NotFound(new { success = false, message = "未找到当前用户" });
                }
                
                // 文件大小限制（2MB）
                if (file.Length > 2 * 1024 * 1024)
                {
                    return BadRequest(new { success = false, message = "头像文件大小不能超过2MB" });
                }
                
                // 检查文件类型
                var allowedTypes = new[] { "image/jpeg", "image/png", "image/gif" };
                if (!allowedTypes.Contains(file.ContentType.ToLower()))
                {
                    return BadRequest(new { success = false, message = "仅支持JPEG、PNG和GIF格式的图片" });
                }
                
                // 生成文件名
                string fileName = $"avatar_{userId}_{DateTime.Now.Ticks}{Path.GetExtension(file.FileName)}";
                
                // 设置保存路径
                string uploadsFolder = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "uploads", "avatars");
                
                // 确保文件夹存在
                if (!Directory.Exists(uploadsFolder))
                {
                    Directory.CreateDirectory(uploadsFolder);
                }
                
                string filePath = Path.Combine(uploadsFolder, fileName);
                
                // 保存文件
                using (var stream = new FileStream(filePath, FileMode.Create))
                {
                    await file.CopyToAsync(stream);
                }
                
                // 更新用户头像URL
                string avatarUrl = $"/uploads/avatars/{fileName}";
                user.Avatar = avatarUrl;
                user.UpdatedAt = DateTime.Now;
                
                await _context.SaveChangesAsync();
                
                return Ok(new { 
                    success = true, 
                    data = new { url = avatarUrl }, 
                    message = "头像上传成功" 
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "上传头像出错");
                return StatusCode(500, new { success = false, message = "上传头像出错: " + ex.Message });
            }
        }
        
        /// <summary>
        /// 修改当前用户密码
        /// </summary>
        /// <param name="passwordModel">密码信息</param>
        /// <returns>修改结果</returns>
        [HttpPut("change-password")]
        public async Task<IActionResult> ChangePassword([FromBody] ChangePasswordModel passwordModel)
        {
            try
            {
                // 从token中获取用户ID
                int userId = 0;
                
                // 尝试从Claims中获取用户ID
                var userIdClaim = User?.Claims?.FirstOrDefault(c => c.Type == "uid" || c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier");
                if (userIdClaim != null && int.TryParse(userIdClaim.Value, out int id))
                {
                    userId = id;
                }
                else
                {
                    // 如果无法从Claims获取，则使用默认值1（测试用）
                    userId = 1;
                    _logger.LogWarning("无法从Claims获取用户ID，使用默认值1");
                }
                
                var user = await _context.Users.FindAsync(userId);
                if (user == null)
                {
                    return NotFound(new { success = false, message = "未找到当前用户" });
                }
                
                // 验证旧密码
                string oldPasswordHash = HashPassword(passwordModel.OldPassword, user.PasswordSalt);
                if (oldPasswordHash != user.PasswordHash)
                {
                    return BadRequest(new { success = false, message = "当前密码不正确" });
                }
                
                // 更新密码
                user.PasswordSalt = GenerateRandomSalt();
                user.PasswordHash = HashPassword(passwordModel.NewPassword, user.PasswordSalt);
                user.UpdatedAt = DateTime.Now;
                
                await _context.SaveChangesAsync();
                
                return Ok(new { success = true, message = "密码修改成功" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "修改密码出错");
                return StatusCode(500, new { success = false, message = "修改密码出错: " + ex.Message });
            }
        }

        /// <summary>
        /// 批量获取用户信息
        /// </summary>
        /// <param name="request">包含用户ID列表的请求</param>
        /// <returns>用户信息列表</returns>
        [HttpPost("batch")]
        public async Task<IActionResult> GetUsersByIds([FromBody] BatchUserRequest request)
        {
            if (request == null || request.UserIds == null || request.UserIds.Count == 0)
            {
                return BadRequest(new { success = false, message = "用户ID列表不能为空" });
            }

            _logger.LogInformation($"批量获取用户信息，ID列表: {string.Join(", ", request.UserIds)}");

            try
            {
                var users = await _context.Users
                    .Where(u => request.UserIds.Contains(u.Id))
                    .Select(u => new
                    {
                        id = u.Id,
                        username = u.Username,
                        name = u.Name,
                        email = u.Email,
                        phone = u.Mobile,
                        departmentId = u.DepartmentId,
                        departmentName = u.Department != null ? u.Department.Name : null,
                        isActive = u.IsActive,
                        createdAt = u.CreatedAt,
                        updatedAt = u.UpdatedAt
                    })
                    .ToListAsync();

                _logger.LogInformation($"找到 {users.Count} 个用户");

                return Ok(new { success = true, data = users });
            }
            catch (Exception ex)
            {
                _logger.LogError($"批量获取用户信息时发生错误: {ex.Message}");
                return StatusCode(500, new { success = false, message = "获取用户信息时发生错误" });
            }
        }

        /// <summary>
        /// 获取可关联到位置的用户候选人
        /// </summary>
        /// <param name="userType">用户类型 0-使用人 1-管理员</param>
        /// <param name="departmentId">部门ID，可选</param>
        /// <returns>用户列表</returns>
        [HttpGet("location-users")]
        public async Task<IActionResult> GetLocationUserCandidates([FromQuery] int userType, [FromQuery] int? departmentId = null)
        {
            try
            {
                _logger.LogInformation($"获取位置{(userType == 0 ? "使用人" : "管理员")}候选人列表，部门ID: {departmentId}");
                
                // 构建基础查询
                var query = _context.Users.AsQueryable();
                
                // 应用用户类型筛选条件
                if (userType == 0) // 使用人
                {
                    // 普通用户，不需要特殊权限
                    // 可以进一步根据角色或权限筛选
                }
                else if (userType == 1) // 管理员
                {
                    // 获取管理员角色ID，假设管理员角色ID为2
                    int adminRoleId = 2;
                    
                    // 筛选具有管理员角色的用户
                    query = query.Where(u => u.UserRoles.Any(ur => ur.RoleId == adminRoleId));
                }
                
                // 应用部门筛选条件
                if (departmentId.HasValue)
                {
                    query = query.Where(u => u.DepartmentId == departmentId.Value);
                }
                
                // 只获取激活的用户
                query = query.Where(u => u.IsActive);
                
                // 执行查询并映射结果
                var users = await query
                    .OrderBy(u => u.Name)
                    .Select(u => new
                    {
                        id = u.Id,
                        username = u.Username,
                        name = u.Name,
                        departmentId = u.DepartmentId,
                        departmentName = u.Department != null ? u.Department.Name : null,
                        email = u.Email,
                        phone = u.Mobile
                    })
                    .ToListAsync();
                    
                _logger.LogInformation($"找到 {users.Count} 个用户候选人");
                
                return Ok(new { success = true, data = users });
            }
            catch (Exception ex)
            {
                _logger.LogError($"获取位置用户候选人时发生错误: {ex.Message}");
                return StatusCode(500, new { success = false, message = "获取位置用户候选人时发生错误" });
            }
        }

        /// <summary>
        /// 获取当前登录用户信息
        /// </summary>
        [HttpGet("info")]
        public async Task<IActionResult> GetUserInfo()
        {
            try
            {
                var userId = User.Claims.FirstOrDefault(c => c.Type == "uid")?.Value;
                
                if (string.IsNullOrEmpty(userId) || !int.TryParse(userId, out int id))
                {
                    return Unauthorized(new { success = false, message = "未授权，请重新登录" });
                }
                
                var user = await _context.Users
                    .Include(u => u.Department)
                    .Include(u => u.UserRoles)
                        .ThenInclude(ur => ur.Role)
                    .FirstOrDefaultAsync(u => u.Id == id);
                
                if (user == null)
                {
                    return NotFound(new { success = false, message = "未找到用户信息" });
                }
                
                // 判断数据库是否可用，这里为了简化，提供一些模拟的权限数据
                var mockPermissions = new List<string>
                {
                    "dashboard:view",
                    "asset:view",
                    "asset:list", 
                    "asset:add",
                    "asset:edit",
                    "asset:delete",
                    "system:view",
                    "user:view",
                    "user:add",
                    "user:edit",
                    "user:delete"
                };
                
                var userInfo = new
                {
                    id = user.Id,
                    username = user.Username,
                    name = user.Name ?? user.Username,
                    avatar = "/avatar.png", // 默认头像
                    email = user.Email,
                    mobile = user.Mobile,
                    gender = (int)user.Gender,
                    departmentId = user.DepartmentId,
                    departmentName = user.Department?.Name,
                    roles = user.UserRoles.Select(ur => ur.Role.Name).ToList(),
                    permissions = mockPermissions,
                    isActive = user.IsActive,
                    lastLoginAt = user.LastLoginAt
                };
                
                return Ok(new { success = true, data = userInfo });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户信息出错");
                return StatusCode(500, new { success = false, message = "获取用户信息出错: " + ex.Message });
            }
        }

        [HttpGet("search")]
        [AllowAnonymous]
        [ProducesResponseType(typeof(IEnumerable<MentionUserDto>), StatusCodes.Status200OK)]
        public async Task<IActionResult> SearchMentionUsers(
            [FromQuery] string? search,
            [FromQuery] int limit = 10)
        {
            if (limit <= 0) limit = 10;
            if (limit > 50) limit = 50;

            try 
            {
                var query = _context.Users.AsNoTracking();

                if (!string.IsNullOrWhiteSpace(search))
                {
                    query = query.Where(u => u.Username.Contains(search) || (u.Name != null && u.Name.Contains(search)));
                }

                // 先获取符合条件的用户基本数据
                var usersData = await query
                    .OrderBy(u => u.Name ?? u.Username)
                    .Take(limit)
                    .Select(u => new { u.Id, Name = u.Name ?? u.Username, u.Avatar })
                    .ToListAsync();
                    
                // 将数据转换为 MentionUserDto 对象
                var users = usersData.Select(u => new MentionUserDto(u.Name) 
                { 
                    Id = u.Id, 
                    Avatar = u.Avatar 
                }).ToList();

                return Ok(users);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "搜索用户提及失败");
                return StatusCode(500, new { message = "搜索用户时发生错误" });
            }
        }
    }

    /// <summary>
    /// 登录模型
    /// </summary>
    public class LoginModel
    {
        /// <summary>
        /// 用户名
        /// </summary>
        public string Username { get; set; } = string.Empty;
        
        /// <summary>
        /// 密码
        /// </summary>
        public string Password { get; set; } = string.Empty;
    }

    /// <summary>
    /// 用户创建模型
    /// </summary>
    public class UserCreateModel
    {
        /// <summary>
        /// 用户名
        /// </summary>
        public string Username { get; set; } = string.Empty;
        
        /// <summary>
        /// 密码
        /// </summary>
        public string Password { get; set; } = string.Empty;
        
        /// <summary>
        /// 姓名
        /// </summary>
        public string Name { get; set; } = string.Empty;
        
        /// <summary>
        /// 职位
        /// </summary>
        public string Position { get; set; } = string.Empty;
        
        /// <summary>
        /// 部门ID
        /// </summary>
        public int? DepartmentId { get; set; }
        
        /// <summary>
        /// 电子邮件
        /// </summary>
        public string Email { get; set; } = string.Empty;
        
        /// <summary>
        /// 手机号码
        /// </summary>
        public string Mobile { get; set; } = string.Empty;
        
        /// <summary>
        /// 性别
        /// </summary>
        public int Gender { get; set; }
        
        /// <summary>
        /// 角色ID列表
        /// </summary>
        public List<int> RoleIds { get; set; } = new List<int>();
    }

    // 添加新的模型类
    public class UserProfileUpdateModel
    {
        public string Name { get; set; } = string.Empty;
        
        public string Email { get; set; } = string.Empty;
        
        public string Phone { get; set; } = string.Empty;
        
        // 可以添加其他个人信息字段...
    }
    
    public class ChangePasswordModel
    {
        public string OldPassword { get; set; } = string.Empty;
        
        public string NewPassword { get; set; } = string.Empty;
    }

    public class BatchUserRequest
    {
        public List<int> UserIds { get; set; } = new List<int>();
    }
} 