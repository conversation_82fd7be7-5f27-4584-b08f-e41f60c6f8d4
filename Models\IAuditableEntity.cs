// IT资产管理系统 - 可审计实体接口
// 文件路径: /Models/IAuditableEntity.cs
// 功能: 定义可审计实体接口，包含创建时间和更新时间

using System;

namespace ItAssetsSystem.Models
{
    /// <summary>
    /// 可审计实体接口，提供创建和更新时间跟踪
    /// </summary>
    public interface IAuditableEntity
    {
        /// <summary>
        /// 创建时间
        /// </summary>
        DateTime CreatedAt { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        DateTime UpdatedAt { get; set; }
    }
} 