-- =====================================================
-- 精简游戏化系统升级脚本 (基于现有系统)
-- 只添加缺失的核心功能，不重复现有功能
-- =====================================================

-- 1. 工作汇总统计表 (新增核心功能)
CREATE TABLE IF NOT EXISTS user_work_summary (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    period_type ENUM('daily', 'weekly', 'monthly') NOT NULL,
    period_date DATE NOT NULL COMMENT '周期起始日期',
    
    -- 📋 任务模块统计
    tasks_created INT DEFAULT 0 COMMENT '新建任务数',
    tasks_claimed INT DEFAULT 0 COMMENT '领取任务数',
    tasks_completed INT DEFAULT 0 COMMENT '完成任务数',
    tasks_commented INT DEFAULT 0 COMMENT '评论任务数',
    
    -- 🏢 资产模块统计
    assets_created INT DEFAULT 0 COMMENT '新建资产数',
    assets_updated INT DEFAULT 0 COMMENT '更新资产数',
    assets_deleted INT DEFAULT 0 COMMENT '删除资产数',
    
    -- 🔧 故障模块统计
    faults_reported INT DEFAULT 0 COMMENT '登记故障数',
    faults_repaired INT DEFAULT 0 COMMENT '维修故障数',
    
    -- 🛒 采购模块统计
    procurements_created INT DEFAULT 0 COMMENT '新建采购单数',
    procurements_updated INT DEFAULT 0 COMMENT '更新采购进度数',
    
    -- 📦 备件模块统计
    parts_in INT DEFAULT 0 COMMENT '备件入库数',
    parts_out INT DEFAULT 0 COMMENT '备件出库数',
    parts_added INT DEFAULT 0 COMMENT '新增备件数',
    
    -- 🎮 游戏化汇总 (从现有gamification_log计算)
    total_points_earned INT DEFAULT 0 COMMENT '总积分',
    total_coins_earned INT DEFAULT 0 COMMENT '总金币',
    total_diamonds_earned INT DEFAULT 0 COMMENT '总钻石',
    total_xp_earned INT DEFAULT 0 COMMENT '总经验',
    
    -- 📊 排名缓存
    points_rank INT DEFAULT 0 COMMENT '积分排名',
    productivity_rank INT DEFAULT 0 COMMENT '生产力排名',
    
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_user_period (user_id, period_type, period_date),
    INDEX idx_period_rank (period_type, period_date, points_rank),
    INDEX idx_productivity_rank (productivity_rank)
) ENGINE=InnoDB COMMENT='用户工作汇总统计表';

-- 2. 增强现有排行榜视图 (基于现有数据)
CREATE OR REPLACE VIEW v_enhanced_leaderboard AS
SELECT 
    ROW_NUMBER() OVER (ORDER BY gus.PointsBalance DESC) as rank_no,
    u.id as user_id,
    u.name as user_name,
    d.name as department_name,
    gus.PointsBalance as total_points,
    gus.CoinsBalance as total_coins,
    gus.DiamondsBalance as total_diamonds,
    gus.CurrentLevel as current_level,
    gus.CompletedTasksCount as tasks_completed,
    
    -- 本周数据 (从汇总表获取)
    COALESCE(uws.total_points_earned, 0) as weekly_points,
    COALESCE(uws.tasks_completed, 0) as weekly_tasks,
    COALESCE(uws.assets_created + uws.assets_updated, 0) as weekly_assets,
    
    -- 专业特长识别
    CASE 
        WHEN COALESCE(uws.tasks_completed, 0) >= GREATEST(COALESCE(uws.assets_created, 0), COALESCE(uws.faults_reported, 0)) THEN '任务专家'
        WHEN COALESCE(uws.assets_created, 0) >= GREATEST(COALESCE(uws.tasks_completed, 0), COALESCE(uws.faults_reported, 0)) THEN '资产能手'
        WHEN COALESCE(uws.faults_reported, 0) >= GREATEST(COALESCE(uws.tasks_completed, 0), COALESCE(uws.assets_created, 0)) THEN '维修达人'
        ELSE '全能选手'
    END as specialty,
    
    gus.LastActivityTimestamp as last_activity
FROM gamification_userstats gus
JOIN users u ON gus.CoreUserId = u.id
LEFT JOIN departments d ON u.department_id = d.id
LEFT JOIN user_work_summary uws ON u.id = uws.user_id 
    AND uws.period_type = 'weekly' 
    AND uws.period_date = DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY)
WHERE u.IsDeleted = 0
ORDER BY gus.PointsBalance DESC;

-- 3. 工作汇总视图 (新增功能)
CREATE OR REPLACE VIEW v_work_summary_report AS
SELECT 
    uws.user_id,
    u.name as user_name,
    d.name as department_name,
    uws.period_type,
    uws.period_date,
    
    -- 任务汇总
    uws.tasks_created,
    uws.tasks_claimed,
    uws.tasks_completed,
    uws.tasks_commented,
    (uws.tasks_created + uws.tasks_claimed + uws.tasks_completed + uws.tasks_commented) as tasks_total,
    
    -- 资产汇总
    uws.assets_created,
    uws.assets_updated,
    uws.assets_deleted,
    (uws.assets_created + uws.assets_updated + uws.assets_deleted) as assets_total,
    
    -- 故障汇总
    uws.faults_reported,
    uws.faults_repaired,
    (uws.faults_reported + uws.faults_repaired) as faults_total,
    
    -- 采购汇总
    uws.procurements_created,
    uws.procurements_updated,
    (uws.procurements_created + uws.procurements_updated) as procurements_total,
    
    -- 备件汇总
    uws.parts_in,
    uws.parts_out,
    uws.parts_added,
    (uws.parts_in + uws.parts_out + uws.parts_added) as parts_total,
    
    -- 游戏化汇总
    uws.total_points_earned,
    uws.total_coins_earned,
    uws.total_diamonds_earned,
    uws.total_xp_earned,
    
    -- 排名
    uws.points_rank,
    uws.productivity_rank,
    
    -- 生产力得分
    (uws.tasks_completed * 3.0 + 
     (uws.assets_created + uws.assets_updated) * 2.0 + 
     uws.faults_reported * 2.0 + 
     uws.procurements_created * 1.5 + 
     (uws.parts_in + uws.parts_out + uws.parts_added) * 1.0
    ) as productivity_score,
    
    -- 综合评价
    CASE 
        WHEN uws.productivity_rank <= 3 THEN '🥇 超级明星'
        WHEN uws.productivity_rank <= 10 THEN '🏆 优秀员工'
        WHEN uws.productivity_rank <= 20 THEN '⭐ 努力奋斗'
        ELSE '💪 稳步提升'
    END as evaluation
    
FROM user_work_summary uws
JOIN users u ON uws.user_id = u.id
LEFT JOIN departments d ON u.department_id = d.id
WHERE u.IsDeleted = 0
ORDER BY uws.productivity_rank;

-- 4. 创建统计更新存储过程 (利用现有gamification_log)
DELIMITER $$

CREATE PROCEDURE UpdateUserWorkSummary(
    IN p_period_type VARCHAR(10),
    IN p_target_date DATE
)
BEGIN
    DECLARE v_start_date DATE;
    DECLARE v_end_date DATE;
    
    -- 计算周期范围
    CASE p_period_type
        WHEN 'daily' THEN 
            SET v_start_date = p_target_date;
            SET v_end_date = DATE_ADD(p_target_date, INTERVAL 1 DAY);
        WHEN 'weekly' THEN
            SET v_start_date = DATE_SUB(p_target_date, INTERVAL WEEKDAY(p_target_date) DAY);
            SET v_end_date = DATE_ADD(v_start_date, INTERVAL 7 DAY);
        WHEN 'monthly' THEN
            SET v_start_date = DATE_FORMAT(p_target_date, '%Y-%m-01');
            SET v_end_date = DATE_ADD(v_start_date, INTERVAL 1 MONTH);
    END CASE;
    
    -- 插入或更新统计数据
    INSERT INTO user_work_summary (
        user_id, period_type, period_date,
        tasks_created, tasks_claimed, tasks_completed, tasks_commented,
        assets_created, assets_updated, assets_deleted,
        faults_reported, faults_repaired,
        procurements_created, procurements_updated,
        parts_in, parts_out, parts_added,
        total_points_earned, total_coins_earned, total_diamonds_earned, total_xp_earned
    )
    SELECT 
        u.id as user_id,
        p_period_type as period_type,
        v_start_date as period_date,
        
        -- 从现有gamification_log统计各模块数据
        COALESCE(SUM(CASE WHEN gl.ActionType = 'TASK_CREATE' THEN 1 ELSE 0 END), 0) as tasks_created,
        COALESCE(SUM(CASE WHEN gl.ActionType = 'TASK_CLAIM' THEN 1 ELSE 0 END), 0) as tasks_claimed,
        COALESCE(SUM(CASE WHEN gl.ActionType = 'TASK_COMPLETE' THEN 1 ELSE 0 END), 0) as tasks_completed,
        COALESCE(SUM(CASE WHEN gl.ActionType = 'TASK_COMMENT' THEN 1 ELSE 0 END), 0) as tasks_commented,
        
        COALESCE(SUM(CASE WHEN gl.ActionType = 'ASSET_CREATE' THEN 1 ELSE 0 END), 0) as assets_created,
        COALESCE(SUM(CASE WHEN gl.ActionType = 'ASSET_UPDATE' THEN 1 ELSE 0 END), 0) as assets_updated,
        COALESCE(SUM(CASE WHEN gl.ActionType = 'ASSET_DELETE' THEN 1 ELSE 0 END), 0) as assets_deleted,
        
        COALESCE(SUM(CASE WHEN gl.ActionType = 'FAULT_REPORT' THEN 1 ELSE 0 END), 0) as faults_reported,
        COALESCE(SUM(CASE WHEN gl.ActionType = 'FAULT_REPAIR' THEN 1 ELSE 0 END), 0) as faults_repaired,
        
        COALESCE(SUM(CASE WHEN gl.ActionType = 'PROCUREMENT_CREATE' THEN 1 ELSE 0 END), 0) as procurements_created,
        COALESCE(SUM(CASE WHEN gl.ActionType = 'PROCUREMENT_UPDATE' THEN 1 ELSE 0 END), 0) as procurements_updated,
        
        COALESCE(SUM(CASE WHEN gl.ActionType = 'INVENTORY_IN' THEN 1 ELSE 0 END), 0) as parts_in,
        COALESCE(SUM(CASE WHEN gl.ActionType = 'INVENTORY_OUT' THEN 1 ELSE 0 END), 0) as parts_out,
        COALESCE(SUM(CASE WHEN gl.ActionType = 'INVENTORY_ADD' THEN 1 ELSE 0 END), 0) as parts_added,
        
        -- 游戏化汇总
        COALESCE(SUM(gl.PointsGained), 0) as total_points_earned,
        COALESCE(SUM(gl.CoinsGained), 0) as total_coins_earned,
        COALESCE(SUM(gl.DiamondsGained), 0) as total_diamonds_earned,
        COALESCE(SUM(gl.XpGained), 0) as total_xp_earned
        
    FROM users u
    LEFT JOIN gamification_log gl ON u.id = gl.UserId 
        AND gl.CreatedAt >= v_start_date 
        AND gl.CreatedAt < v_end_date
    WHERE u.IsDeleted = 0
    GROUP BY u.id
    
    ON DUPLICATE KEY UPDATE
        tasks_created = VALUES(tasks_created),
        tasks_claimed = VALUES(tasks_claimed),
        tasks_completed = VALUES(tasks_completed),
        tasks_commented = VALUES(tasks_commented),
        assets_created = VALUES(assets_created),
        assets_updated = VALUES(assets_updated),
        assets_deleted = VALUES(assets_deleted),
        faults_reported = VALUES(faults_reported),
        faults_repaired = VALUES(faults_repaired),
        procurements_created = VALUES(procurements_created),
        procurements_updated = VALUES(procurements_updated),
        parts_in = VALUES(parts_in),
        parts_out = VALUES(parts_out),
        parts_added = VALUES(parts_added),
        total_points_earned = VALUES(total_points_earned),
        total_coins_earned = VALUES(total_coins_earned),
        total_diamonds_earned = VALUES(total_diamonds_earned),
        total_xp_earned = VALUES(total_xp_earned),
        updated_at = CURRENT_TIMESTAMP;
        
    -- 更新排名
    SET @rank = 0;
    UPDATE user_work_summary uws
    JOIN (
        SELECT user_id, (@rank := @rank + 1) as new_rank
        FROM user_work_summary
        WHERE period_type = p_period_type AND period_date = v_start_date
        ORDER BY total_points_earned DESC
    ) ranked ON uws.user_id = ranked.user_id
    SET uws.points_rank = ranked.new_rank
    WHERE uws.period_type = p_period_type AND uws.period_date = v_start_date;
    
    SET @rank = 0;
    UPDATE user_work_summary uws
    JOIN (
        SELECT user_id, (@rank := @rank + 1) as new_rank
        FROM user_work_summary
        WHERE period_type = p_period_type AND period_date = v_start_date
        ORDER BY (tasks_completed * 3.0 + (assets_created + assets_updated) * 2.0 + faults_reported * 2.0 + procurements_created * 1.5 + (parts_in + parts_out + parts_added) * 1.0) DESC
    ) ranked ON uws.user_id = ranked.user_id
    SET uws.productivity_rank = ranked.new_rank
    WHERE uws.period_type = p_period_type AND uws.period_date = v_start_date;
    
END$$

DELIMITER ;

-- 5. 创建性能优化索引
CREATE INDEX IF NOT EXISTS idx_gamification_log_user_action_date ON gamification_log(UserId, ActionType, CreatedAt);
CREATE INDEX IF NOT EXISTS idx_user_work_summary_period ON user_work_summary(period_type, period_date);

-- 6. 初始化本周数据
CALL UpdateUserWorkSummary('weekly', CURDATE());

-- 7. 验证安装
SELECT 
    'user_work_summary' as table_name, 
    COUNT(*) as record_count 
FROM user_work_summary
UNION ALL
SELECT 
    'v_enhanced_leaderboard' as view_name,
    COUNT(*) as record_count
FROM v_enhanced_leaderboard LIMIT 5
UNION ALL
SELECT 
    'v_work_summary_report' as view_name,
    COUNT(*) as record_count
FROM v_work_summary_report LIMIT 5;

SELECT '✅ 精简游戏化系统升级完成！基于现有系统，只添加了工作汇总统计功能。' as result;
