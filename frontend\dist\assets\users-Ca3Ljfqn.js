import{_ as e,r as a,ad as l,z as t,b as n,d as r,e as o,w as s,f as u,aG as i,q as c,a as d,m,o as p,p as b,F as f,h as g,D as h,b7 as v,l as _,k as w,t as y,$ as k,aH as z,b1 as M,bJ as S,bK as C,aJ as x,E as $,a9 as T}from"./index-CG5lHOPO.js";const V={class:"user-management-container"},P={class:"page-header"},D={class:"page-actions"},U={class:"filter-container"},B={class:"pagination-container"},F=e({__name:"users",setup(e){const F=a(!1),H=a([]),I=a(null),R=l({currentPage:1,pageSize:10,total:0}),j=l({username:"",name:"",department:"",role:"",status:""}),J=[{label:"系统管理员",value:"admin"},{label:"资产管理员",value:"asset_manager"},{label:"采购员",value:"purchaser"},{label:"维护人员",value:"maintenance"},{label:"普通用户",value:"user"}],L=[{label:"IT部门",value:"IT"},{label:"人力资源部",value:"HR"},{label:"财务部",value:"Finance"},{label:"研发部",value:"R&D"},{label:"运营部",value:"Operations"}],O=[{label:"活跃",value:"active"},{label:"已禁用",value:"disabled"}];t((()=>{q()}));const q=()=>{F.value=!0,R.currentPage,R.pageSize,j.username,j.name,j.department,j.role,j.status,setTimeout((()=>{const e=[];for(let a=0;a<Math.min(R.pageSize,56-(R.currentPage-1)*R.pageSize);a++){const l=(R.currentPage-1)*R.pageSize+a,t=["active","disabled"][Math.floor(2*Math.random())],n=["admin","asset_manager","purchaser","maintenance","user"][Math.floor(5*Math.random())],r=["IT","HR","Finance","R&D","Operations"][Math.floor(5*Math.random())],o=new Date;o.setMonth(o.getMonth()-Math.floor(24*Math.random()));const s=A(o),u=new Date;u.setDate(u.getDate()-Math.floor(30*Math.random()));const i=A(u);e.push({id:1e3+l,username:`user${1e3+l}`,name:["张三","李四","王五","赵六","陈七","钱八","孙九","周十","吴一","郑二","冯三","蒋四","沈五","韩六","杨七","朱八"][Math.floor(16*Math.random())],department:r,role:n,email:`user${1e3+l}@example.com`,phone:`1${Math.floor(9*Math.random())+1}${Array(9).fill(0).map((()=>Math.floor(10*Math.random()))).join("")}`,status:t,createTime:s,lastLoginTime:i})}H.value=e,R.total=56,F.value=!1}),500)},A=e=>`${e.getFullYear()}-${String(e.getMonth()+1).padStart(2,"0")}-${String(e.getDate()).padStart(2,"0")} ${String(e.getHours()).padStart(2,"0")}:${String(e.getMinutes()).padStart(2,"0")}:${String(e.getSeconds()).padStart(2,"0")}`,E=()=>{R.currentPage=1,q()},G=()=>{j.username="",j.name="",j.department="",j.role="",j.status="",R.currentPage=1,q()},K=e=>{R.pageSize=e,q()},Y=e=>{R.currentPage=e,q()},N=()=>{$.info("打开新建用户对话框")},Q=()=>{$.success("开始导出数据，请稍候...")},W=e=>({admin:"系统管理员",asset_manager:"资产管理员",purchaser:"采购员",maintenance:"维护人员",user:"普通用户"}[e]||"未知"),X=e=>({active:"活跃",disabled:"已禁用"}[e]||"未知");return(e,a)=>{const l=d("el-button"),t=d("el-input"),A=d("el-form-item"),Z=d("el-option"),ee=d("el-select"),ae=d("el-form"),le=d("el-card"),te=d("el-table-column"),ne=d("el-tag"),re=d("el-table"),oe=d("el-pagination"),se=m("loading");return p(),n("div",V,[r("div",P,[a[9]||(a[9]=r("h2",{class:"page-title"},"用户管理",-1)),r("div",D,[o(l,{type:"primary",onClick:N,icon:u(i)},{default:s((()=>a[7]||(a[7]=[b(" 新建用户 ")]))),_:1},8,["icon"]),o(l,{type:"primary",onClick:Q,icon:u(c)},{default:s((()=>a[8]||(a[8]=[b(" 导出数据 ")]))),_:1},8,["icon"])])]),o(le,{class:"filter-card"},{default:s((()=>[r("div",U,[o(ae,{inline:!0,model:j,class:"filter-form"},{default:s((()=>[o(A,{label:"用户名"},{default:s((()=>[o(t,{modelValue:j.username,"onUpdate:modelValue":a[0]||(a[0]=e=>j.username=e),placeholder:"用户名",clearable:""},null,8,["modelValue"])])),_:1}),o(A,{label:"姓名"},{default:s((()=>[o(t,{modelValue:j.name,"onUpdate:modelValue":a[1]||(a[1]=e=>j.name=e),placeholder:"姓名",clearable:""},null,8,["modelValue"])])),_:1}),o(A,{label:"部门"},{default:s((()=>[o(ee,{modelValue:j.department,"onUpdate:modelValue":a[2]||(a[2]=e=>j.department=e),placeholder:"全部部门",clearable:""},{default:s((()=>[(p(),n(f,null,g(L,(e=>o(Z,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),64))])),_:1},8,["modelValue"])])),_:1}),o(A,{label:"角色"},{default:s((()=>[o(ee,{modelValue:j.role,"onUpdate:modelValue":a[3]||(a[3]=e=>j.role=e),placeholder:"全部角色",clearable:""},{default:s((()=>[(p(),n(f,null,g(J,(e=>o(Z,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),64))])),_:1},8,["modelValue"])])),_:1}),o(A,{label:"状态"},{default:s((()=>[o(ee,{modelValue:j.status,"onUpdate:modelValue":a[4]||(a[4]=e=>j.status=e),placeholder:"全部状态",clearable:""},{default:s((()=>[(p(),n(f,null,g(O,(e=>o(Z,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),64))])),_:1},8,["modelValue"])])),_:1}),o(A,null,{default:s((()=>[o(l,{type:"primary",onClick:E,icon:u(h)},{default:s((()=>a[10]||(a[10]=[b(" 搜索 ")]))),_:1},8,["icon"]),o(l,{onClick:G,icon:u(v)},{default:s((()=>a[11]||(a[11]=[b(" 重置 ")]))),_:1},8,["icon"])])),_:1})])),_:1},8,["model"])])])),_:1}),o(le,{class:"data-card"},{default:s((()=>[_((p(),w(re,{ref_key:"userTable",ref:I,data:H.value,border:"",style:{width:"100%"}},{default:s((()=>[o(te,{prop:"username",label:"用户名",width:"120",sortable:""}),o(te,{prop:"name",label:"姓名",width:"120"}),o(te,{prop:"department",label:"部门",width:"150"}),o(te,{prop:"role",label:"角色",width:"150"},{default:s((e=>{return[o(ne,{type:(a=e.row.role,{admin:"danger",asset_manager:"warning",purchaser:"success",maintenance:"primary",user:"info"}[a]||""),size:"small"},{default:s((()=>[b(y(W(e.row.role)),1)])),_:2},1032,["type"])];var a})),_:1}),o(te,{prop:"email",label:"邮箱","min-width":"180","show-overflow-tooltip":""}),o(te,{prop:"phone",label:"手机号",width:"130"}),o(te,{prop:"status",label:"状态",width:"100"},{default:s((e=>{return[o(ne,{type:(a=e.row.status,{active:"success",disabled:"info"}[a]||""),size:"small"},{default:s((()=>[b(y(X(e.row.status)),1)])),_:2},1032,["type"])];var a})),_:1}),o(te,{prop:"createTime",label:"创建时间",width:"170",sortable:""}),o(te,{prop:"lastLoginTime",label:"最后登录",width:"170",sortable:""}),o(te,{label:"操作",width:"230",fixed:"right"},{default:s((e=>[o(l,{type:"text",size:"small",onClick:a=>{return l=e.row,void $.info(`查看用户详情：${l.username}`);var l},icon:u(z)},{default:s((()=>a[12]||(a[12]=[b(" 详情 ")]))),_:2},1032,["onClick","icon"]),o(l,{type:"text",size:"small",onClick:a=>{return l=e.row,void $.info(`编辑用户：${l.username}`);var l},icon:u(M)},{default:s((()=>a[13]||(a[13]=[b(" 编辑 ")]))),_:2},1032,["onClick","icon"]),"active"===e.row.status?(p(),w(l,{key:0,type:"text",size:"small",onClick:a=>{return l=e.row,void T.confirm(`确定要禁用用户"${l.username}"吗？禁用后该用户将无法登录系统。`,"禁用用户",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{$.success("用户已禁用"),q()})).catch((()=>{}));var l},icon:u(S)},{default:s((()=>a[14]||(a[14]=[b(" 禁用 ")]))),_:2},1032,["onClick","icon"])):k("",!0),"disabled"===e.row.status?(p(),w(l,{key:1,type:"text",size:"small",onClick:a=>{return l=e.row,void T.confirm(`确定要启用用户"${l.username}"吗？`,"启用用户",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{$.success("用户已启用"),q()})).catch((()=>{}));var l},icon:u(C)},{default:s((()=>a[15]||(a[15]=[b(" 启用 ")]))),_:2},1032,["onClick","icon"])):k("",!0),o(l,{type:"text",size:"small",onClick:a=>{return l=e.row,void T.confirm(`确定要删除用户"${l.username}"吗？此操作不可恢复！`,"删除用户",{confirmButtonText:"确定",cancelButtonText:"取消",type:"error"}).then((()=>{$.success("用户已删除"),q()})).catch((()=>{}));var l},icon:u(x)},{default:s((()=>a[16]||(a[16]=[b(" 删除 ")]))),_:2},1032,["onClick","icon"])])),_:1})])),_:1},8,["data"])),[[se,F.value]]),r("div",B,[o(oe,{"current-page":R.currentPage,"onUpdate:currentPage":a[5]||(a[5]=e=>R.currentPage=e),"page-size":R.pageSize,"onUpdate:pageSize":a[6]||(a[6]=e=>R.pageSize=e),"page-sizes":[10,20,50,100],background:!0,layout:"total, sizes, prev, pager, next, jumper",total:R.total,onSizeChange:K,onCurrentChange:Y},null,8,["current-page","page-size","total"])])])),_:1})])}}},[["__scopeId","data-v-b3930614"]]);export{F as default};
