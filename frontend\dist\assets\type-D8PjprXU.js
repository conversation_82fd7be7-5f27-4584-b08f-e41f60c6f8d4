import{_ as e,r as a,aN as l,z as t,b as s,e as o,w as r,E as u,a as n,m as d,o as c,af as i,p,d as v,l as m,k as y,t as f,$ as g,f as h,q as w,C as _,aI as k,F as b,h as C,a9 as x}from"./index-CG5lHOPO.js";import{a as V}from"./assetType-FfrNKE6C.js";const A={class:"asset-type"},U={class:"operation-buttons"},z={class:"pagination-container"},S={class:"dialog-footer"},T={class:"import-dialog-content"},B={class:"import-steps"},O={class:"step"},I={class:"step"},R={class:"step"},L={class:"template-download"},M={key:0,class:"import-progress"},$={key:1,class:"import-result"},j={class:"result-info"},N={key:0},E={key:1},D={key:2,class:"error-messages"},q={class:"dialog-footer"},J=e({__name:"type",setup(e){const J=a([]),F=a(!1),K=a(0),P=a(1),G=a(10),H=a({keyword:""}),Q=a(!1),W=a("add"),X=a("新增资产类型"),Y=a(null),Z=a({code:"",name:"",description:""}),ee={code:[{required:!0,message:"请输入类型编码",trigger:"blur"}],name:[{required:!0,message:"请输入类型名称",trigger:"blur"}]},ae=a(!1),le=a(null),te=a(null),se=a(!1),oe=a(0),re=a(null),ue={Authorization:`Bearer ${l()}`},ne=async()=>{try{F.value=!0;const e={page:P.value,pageSize:G.value,isActive:null};H.value.keyword&&""!==H.value.keyword.trim()&&(e.keyword=H.value.keyword.trim());const a=await V.getAssetTypes(e);!0===a.success?(J.value=a.data.items||[],K.value=a.data.total||0,0===J.value.length&&K.value>0&&(P.value=1,ne())):u.error(a.message||"获取资产类型列表失败")}catch(e){u.error("获取资产类型列表失败: "+(e.message||"未知错误"))}finally{F.value=!1}},de=()=>{P.value=1,ne()},ce=()=>{H.value={keyword:""},de()},ie=()=>{W.value="add",X.value="新增资产类型",Z.value={code:"",name:"",description:""},Q.value=!0},pe=async()=>{Y.value&&await Y.value.validate((async(e,a)=>{if(e){F.value=!0;try{const e={...Z.value};let a;if("add"===W.value)a=await V.createAssetType(e);else{const l=e.id;if("number"!=typeof l&&(e.id=parseInt(l,10),isNaN(e.id)))throw new Error("资产类型ID无效");a=await V.updateAssetType(e.id,e)}a.success?(u.success("add"===W.value?"添加成功":"更新成功"),Q.value=!1,ne()):u.error(a.message||("add"===W.value?"添加失败":"更新失败"))}catch(l){u.error("操作失败: "+(l.message||"未知错误"))}finally{F.value=!1}}}))},ve=async()=>{try{const e=u({message:"正在准备导出数据，请稍候...",type:"warning",duration:0}),a=await V.exportAssetTypes({keyword:H.value.keyword});e.close();let l="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",t="xlsx";const s=a.type||"application/octet-stream";s.includes("sheet")||s.includes("excel")?(l="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",t="xlsx"):s.includes("csv")&&(l="text/csv",t="csv");const o=new Blob([a],{type:l}),r=`资产类型导出_${(new Date).toISOString().substring(0,10)}.${t}`;if(window.navigator.msSaveOrOpenBlob)window.navigator.msSaveBlob(o,r);else{const e=document.createElement("a");e.href=URL.createObjectURL(o),e.download=r,e.style.display="none",document.body.appendChild(e),e.click(),URL.revokeObjectURL(e.href),document.body.removeChild(e)}u.success("导出成功")}catch(e){u.error("导出失败，请重试")}},me=()=>{ae.value=!0,te.value=null,se.value=!1,oe.value=0,re.value=null},ye=async()=>{try{const e=await V.getAssetTypeImportTemplate(),a=new Blob([e],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),l=`资产类型导入模板_${(new Date).toISOString().substring(0,10)}.xlsx`;if(window.navigator.msSaveOrOpenBlob)window.navigator.msSaveBlob(a,l);else{const e=document.createElement("a");e.href=URL.createObjectURL(a),e.download=l,e.style.display="none",document.body.appendChild(e),e.click(),URL.revokeObjectURL(e.href),document.body.removeChild(e)}u.success("模板下载成功")}catch(e){u.error("模板下载失败，请重试")}},fe=e=>{const a="application/vnd.ms-excel"===e.type||"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"===e.type||"text/csv"===e.type||/\.(csv|xlsx|xls)$/i.test(e.name),l=e.size/1024/1024<10;return a?l?(te.value=e,a&&l):(u.error("文件大小不能超过10MB!"),!1):(u.error("请上传Excel或CSV格式的文件!"),!1)},ge=()=>{te.value?(se.value=!0,oe.value=0,re.value=null,le.value&&le.value.submit()):u.warning("请先选择要导入的文件")},he=e=>{oe.value=Math.round(e.percent)},we=e=>{var a,l,t,s;se.value=!1,oe.value=100,e.success?re.value={success:!0,data:e.data||{successCount:(null==(a=e.data)?void 0:a.totalCount)||0,errorCount:0,errorMessages:[]}}:re.value={success:!1,data:{successCount:0,errorCount:(null==(t=null==(l=e.data)?void 0:l.errors)?void 0:t.length)||0,errorMessages:(null==(s=e.data)?void 0:s.errors)||[e.message||"导入失败"]}}},_e=e=>{se.value=!1;let a="导入失败";try{e.response&&e.response.data&&(a=e.response.data.message||e.response.data.error||"导入失败")}catch(l){}re.value={success:!1,data:{successCount:0,errorCount:1,errorMessages:[a]}}},ke=()=>{ae.value=!1,ne()},be=e=>{G.value=e,P.value=1,ne()},Ce=e=>{P.value=e,ne()};return t((()=>{ne()})),(e,a)=>{const l=n("el-input"),t=n("el-form-item"),xe=n("el-button"),Ve=n("el-form"),Ae=n("el-card"),Ue=n("el-table-column"),ze=n("el-tag"),Se=n("el-button-group"),Te=n("el-table"),Be=n("el-pagination"),Oe=n("el-dialog"),Ie=n("el-icon"),Re=n("el-divider"),Le=n("el-upload"),Me=n("el-progress"),$e=n("el-alert"),je=d("loading");return c(),s("div",A,[o(Ae,{class:"search-card"},{default:r((()=>[o(Ve,{inline:!0,model:H.value,class:"search-form"},{default:r((()=>[o(t,{label:"关键词"},{default:r((()=>[o(l,{modelValue:H.value.keyword,"onUpdate:modelValue":a[0]||(a[0]=e=>H.value.keyword=e),placeholder:"请输入类型名称/编码",clearable:"",onKeyup:i(de,["enter"])},null,8,["modelValue"])])),_:1}),o(t,null,{default:r((()=>[o(xe,{type:"primary",onClick:de},{default:r((()=>a[10]||(a[10]=[p("搜索")]))),_:1}),o(xe,{onClick:ce},{default:r((()=>a[11]||(a[11]=[p("重置")]))),_:1})])),_:1})])),_:1},8,["model"])])),_:1}),o(Ae,{class:"operation-card"},{default:r((()=>[v("div",U,[o(xe,{type:"primary",onClick:ie},{default:r((()=>a[12]||(a[12]=[p("新增类型")]))),_:1}),o(xe,{type:"success",onClick:ve},{default:r((()=>a[13]||(a[13]=[p("导出")]))),_:1}),o(xe,{type:"warning",onClick:me},{default:r((()=>a[14]||(a[14]=[p("批量导入")]))),_:1})])])),_:1}),o(Ae,{class:"list-card"},{default:r((()=>[m((c(),y(Te,{data:J.value,border:"",style:{width:"100%"}},{default:r((()=>[o(Ue,{prop:"code",label:"类型编码",width:"120"}),o(Ue,{prop:"name",label:"类型名称",width:"150"}),o(Ue,{prop:"description",label:"描述"}),o(Ue,{label:"是否激活",width:"80",align:"center"},{default:r((({row:e})=>[o(ze,{type:e.isActive?"success":"danger",size:"small"},{default:r((()=>[p(f(e.isActive?"是":"否"),1)])),_:2},1032,["type"])])),_:1}),o(Ue,{prop:"createdAt",label:"创建时间",width:"180"}),o(Ue,{prop:"updatedAt",label:"更新时间",width:"180"}),o(Ue,{label:"操作",width:"200",fixed:"right"},{default:r((({row:e})=>[o(Se,null,{default:r((()=>[o(xe,{type:"primary",link:"",onClick:a=>(e=>{W.value="edit",X.value="编辑资产类型",Z.value=JSON.parse(JSON.stringify(e)),"string"==typeof Z.value.id&&(Z.value.id=parseInt(Z.value.id,10),isNaN(Z.value.id))?u.error("资产类型ID无效"):Q.value=!0})(e)},{default:r((()=>a[15]||(a[15]=[p("编辑")]))),_:2},1032,["onClick"]),o(xe,{type:e.isActive?"warning":"success",link:"",onClick:a=>(async e=>{try{F.value=!0;const a=await V.toggleAssetTypeActive(e.id);a.success?(u.success("激活状态切换成功"),ne()):u.error(a.message||"激活状态切换失败")}catch(a){u.error("切换激活状态失败")}finally{F.value=!1}})(e)},{default:r((()=>[p(f(e.isActive?"停用":"激活"),1)])),_:2},1032,["type","onClick"]),o(xe,{type:"danger",link:"",onClick:a=>(e=>{x.confirm(`确定要删除"${e.name}"吗？`,"警告",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"warning"}).then((async()=>{F.value=!0;try{const a=await V.deleteAssetType(e.id);a.success?(u.success("删除成功"),ne()):u.error(a.message||"删除失败")}catch(a){u.error("删除失败: "+(a.message||"未知错误"))}finally{F.value=!1}})).catch((()=>{}))})(e)},{default:r((()=>a[16]||(a[16]=[p("删除")]))),_:2},1032,["onClick"])])),_:2},1024)])),_:1})])),_:1},8,["data"])),[[je,F.value]]),v("div",z,[o(Be,{"current-page":P.value,"onUpdate:currentPage":a[1]||(a[1]=e=>P.value=e),"page-size":G.value,"onUpdate:pageSize":a[2]||(a[2]=e=>G.value=e),"page-sizes":[10,20,50,100],total:K.value,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:be,onCurrentChange:Ce},null,8,["current-page","page-size","total"])])])),_:1}),o(Oe,{modelValue:Q.value,"onUpdate:modelValue":a[7]||(a[7]=e=>Q.value=e),title:X.value,width:"50%"},{footer:r((()=>[v("span",S,[o(xe,{onClick:a[6]||(a[6]=e=>Q.value=!1)},{default:r((()=>a[17]||(a[17]=[p("取消")]))),_:1}),o(xe,{type:"primary",onClick:pe},{default:r((()=>a[18]||(a[18]=[p("确定")]))),_:1})])])),default:r((()=>[o(Ve,{ref_key:"formRef",ref:Y,model:Z.value,rules:ee,"label-width":"100px"},{default:r((()=>[o(t,{label:"类型编码",prop:"code"},{default:r((()=>[o(l,{modelValue:Z.value.code,"onUpdate:modelValue":a[3]||(a[3]=e=>Z.value.code=e),placeholder:"请输入类型编码"},null,8,["modelValue"])])),_:1}),o(t,{label:"类型名称",prop:"name"},{default:r((()=>[o(l,{modelValue:Z.value.name,"onUpdate:modelValue":a[4]||(a[4]=e=>Z.value.name=e),placeholder:"请输入类型名称"},null,8,["modelValue"])])),_:1}),o(t,{label:"描述",prop:"description"},{default:r((()=>[o(l,{modelValue:Z.value.description,"onUpdate:modelValue":a[5]||(a[5]=e=>Z.value.description=e),type:"textarea",rows:3,placeholder:"请输入描述信息"},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue","title"]),o(Oe,{modelValue:ae.value,"onUpdate:modelValue":a[9]||(a[9]=e=>ae.value=e),title:"资产类型批量导入",width:"500px","destroy-on-close":""},{footer:r((()=>[v("span",q,[o(xe,{onClick:a[8]||(a[8]=e=>ae.value=!1)},{default:r((()=>a[32]||(a[32]=[p("关闭")]))),_:1}),re.value&&re.value.success?(c(),y(xe,{key:0,type:"primary",onClick:ke},{default:r((()=>a[33]||(a[33]=[p(" 完成并刷新 ")]))),_:1})):g("",!0)])])),default:r((()=>[v("div",T,[v("div",B,[v("p",O,[o(ze,{size:"small"},{default:r((()=>a[19]||(a[19]=[p("步骤 1")]))),_:1}),a[20]||(a[20]=p(" 下载导入模板"))]),v("p",I,[o(ze,{size:"small"},{default:r((()=>a[21]||(a[21]=[p("步骤 2")]))),_:1}),a[22]||(a[22]=p(" 按照模板格式填写数据"))]),v("p",R,[o(ze,{size:"small"},{default:r((()=>a[23]||(a[23]=[p("步骤 3")]))),_:1}),a[24]||(a[24]=p(" 上传填写好的文件"))])]),v("div",L,[o(xe,{type:"primary",plain:"",onClick:ye},{default:r((()=>[o(Ie,null,{default:r((()=>[o(h(w))])),_:1}),a[25]||(a[25]=p(" 下载导入模板 "))])),_:1}),a[26]||(a[26]=v("p",{class:"template-tip"},"请确保按照模板格式填写数据，否则可能导致导入失败",-1))]),o(Re),o(Le,{ref_key:"uploadRef",ref:le,class:"import-upload",action:`${h(_).apiBaseUrl}/Import/data?entityType=AssetTypes`,headers:ue,"before-upload":fe,"on-success":we,"on-error":_e,"on-progress":he,"auto-upload":!1,accept:".csv,.xlsx,.xls",limit:1},{trigger:r((()=>[o(xe,{type:"primary"},{default:r((()=>[o(Ie,null,{default:r((()=>[o(h(k))])),_:1}),a[27]||(a[27]=p(" 选择文件 "))])),_:1})])),tip:r((()=>a[28]||(a[28]=[v("div",{class:"el-upload__tip"}," 仅支持 xlsx, xls 或 csv 格式文件，文件大小不超过10MB ",-1)]))),default:r((()=>[o(xe,{class:"ml-3",type:"success",onClick:ge,disabled:null===te.value},{default:r((()=>a[29]||(a[29]=[p(" 开始导入 ")]))),_:1},8,["disabled"])])),_:1},8,["action"]),se.value?(c(),s("div",M,[o(Me,{percentage:oe.value,status:"success"},null,8,["percentage"]),a[30]||(a[30]=v("p",{class:"progress-text"},"正在导入，请勿关闭窗口...",-1))])):g("",!0),re.value?(c(),s("div",$,[o($e,{title:re.value.success?"导入成功":"导入失败",type:re.value.success?"success":"error","show-icon":""},{default:r((()=>{var e,l,t,o,r,u;return[v("div",j,[re.value.success?(c(),s("p",N," 成功导入 "+f((null==(e=re.value.data)?void 0:e.successCount)||0)+" 条记录 ",1)):g("",!0),(null==(l=re.value.data)?void 0:l.errorCount)?(c(),s("p",E," 失败 "+f((null==(t=re.value.data)?void 0:t.errorCount)||0)+" 条记录 ",1)):g("",!0),(null==(r=null==(o=re.value.data)?void 0:o.errorMessages)?void 0:r.length)>0?(c(),s("div",D,[a[31]||(a[31]=v("p",null,"错误信息:",-1)),v("ul",null,[(c(!0),s(b,null,C(null==(u=re.value.data)?void 0:u.errorMessages,((e,a)=>(c(),s("li",{key:a},f(e),1)))),128))])])):g("",!0)])]})),_:1},8,["title","type"])])):g("",!0)])])),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-57d0fa44"]]);export{J as default};
