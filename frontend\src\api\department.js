/**
 * 航空航天级IT资产管理系统 - 部门管理API
 * 文件路径: src/api/department.js
 * 功能描述: 提供部门管理相关的API服务
 */

import request from '@/utils/request'
import systemConfig from '@/config/system'

const departmentApi = {
  /**
   * 获取部门列表
   * @param {Object} params 查询参数
   */
  getDepartmentList(params) {
    console.log('获取部门列表 - API调用', {
      请求方法: 'GET',
      请求路径: '/Department',
      完整URL: `${systemConfig.apiBaseUrl}/Department`
    })
    return request.get('/Department', { params })
      .then(response => {
        return { data: response };
      })
      .catch(error => {
        console.error('获取部门列表失败:', error);
        return { data: { success: false, message: error.message || '服务器错误' } };
      });
  },
  
  /**
   * 获取部门列表 (getDepartments 是 getDepartmentList 的别名)
   * @param {Object} params 查询参数
   */
  getDepartments(params) {
    return this.getDepartmentList(params);
  },
  
  /**
   * 获取部门树
   */
  getDepartmentTree() {
    return request.get('/Department/tree')
      .then(response => {
        return { data: response };
      })
      .catch(error => {
        console.error('获取部门树失败:', error);
        return { data: { success: false, message: error.message || '服务器错误' } };
      });
  },
  
  /**
   * 获取部门详情
   * @param {number} id 部门ID
   */
  getDepartmentDetail(id) {
    console.log('获取部门详情 - API调用', {
      请求方法: 'GET',
      请求路径: `/Department/${id}`,
      完整URL: `${systemConfig.apiBaseUrl}/Department/${id}`,
      部门ID: id
    })
    return request.get(`/Department/${id}`)
      .then(response => {
        return { data: response };
      })
      .catch(error => {
        console.error('获取部门详情失败:', error);
        return { data: { success: false, message: error.message || '服务器错误' } };
      });
  },
  
  /**
   * 创建部门
   * @param {Object} data 部门数据
   * @param {String} data.name 部门名称
   * @param {String} data.code 部门编码
   * @param {Number} data.parentId 父部门ID
   * @param {Number} data.managerId 部门经理ID
   * @param {Number} data.deputyManagerId 部门主任ID
   * @returns {Promise<Object>} 创建结果
   */
  createDepartment(data) {
    const submitData = {
      name: data.name,
      code: data.code,
      description: data.description || '',
      parentId: data.parentId || null,
      managerId: data.managerId || null,
      deputyManagerId: data.deputyManagerId || null,
      isActive: data.status === 'active'
    };

    console.log('创建部门 - API调用', {
      请求方法: 'POST',
      请求路径: '/Department',
      请求数据: submitData
    });

    return request.post('/Department', submitData)
      .then(response => {
        return { data: response };
      })
      .catch(error => {
        console.error('创建部门失败:', error);
        return { 
          data: { 
            success: false, 
            message: error.response?.data?.message || error.message || '服务器错误' 
          } 
        };
      });
  },
  
  /**
   * 更新部门
   * @param {Number} id 部门ID
   * @param {Object} data 部门数据
   * @param {String} data.name 部门名称
   * @param {String} data.code 部门编码
   * @param {Number} data.parentId 父部门ID
   * @param {Number} data.managerId 部门经理ID
   * @param {Number} data.deputyManagerId 部门主任ID
   * @returns {Promise<Object>} 更新结果
   */
  updateDepartment(id, data) {
    const submitData = {
      name: data.name,
      code: data.code,
      description: data.description || '',
      parentId: data.parentId || null,
      managerId: data.managerId || null,
      deputyManagerId: data.deputyManagerId || null,
      isActive: data.status === 'active'
    };

    console.log('更新部门 - API调用', {
      请求方法: 'PUT',
      请求路径: `/Department/${id}`,
      部门ID: id,
      请求数据: submitData
    });

    return request.put(`/Department/${id}`, submitData)
      .then(response => {
        return { data: response };
      })
      .catch(error => {
        console.error('更新部门失败:', error);
        return { 
          data: { 
            success: false, 
            message: error.response?.data?.message || error.message || '服务器错误' 
          } 
        };
      });
  },
  
  /**
   * 删除部门
   * @param {number} id 部门ID
   */
  deleteDepartment(id) {
    console.log('删除部门 - API调用', {
      请求方法: 'DELETE',
      请求路径: `/Department/${id}`,
      完整URL: `${systemConfig.apiBaseUrl}/Department/${id}`,
      部门ID: id
    })
    return request.delete(`/Department/${id}`)
      .then(response => {
        return { data: response };
      })
      .catch(error => {
        console.error('删除部门失败:', error);
        return { data: { success: false, message: error.message || '服务器错误' } };
      });
  },
  
  /**
   * 更新位置关联的部门
   * @param {number} locationId 位置ID
   * @param {Object} data 部门关联数据
   */
  updateLocationDepartment(locationId, data) {
    return request.put(`/Location/${locationId}/department`, data)
      .then(response => {
        return { data: response };
      })
      .catch(error => {
        console.error('更新位置关联部门失败:', error);
        return { data: { success: false, message: error.message || '服务器错误' } };
      });
  },
  
  /**
   * 获取部门用户
   * @param {number} departmentId 部门ID
   */
  getDepartmentUsers(departmentId) {
    console.log('获取部门用户列表 - API调用', {
      请求方法: 'GET',
      请求路径: `/Department/${departmentId}/users`,
      完整URL: `${systemConfig.apiBaseUrl}/Department/${departmentId}/users`,
      部门ID: departmentId
    })
    return request.get(`/Department/${departmentId}/users`)
      .then(response => {
        return { data: response };
      })
      .catch(error => {
        console.error('获取部门用户列表失败:', error);
        return { data: { success: false, message: error.message || '服务器错误' } };
      });
  }
}

export default departmentApi 