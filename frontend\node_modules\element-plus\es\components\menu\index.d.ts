import Menu from './src/menu';
import MenuItem from './src/menu-item.vue';
import MenuItemGroup from './src/menu-item-group.vue';
import SubMenu from './src/sub-menu';
import type { SFCWithInstall } from 'element-plus/es/utils';
export declare const ElMenu: SFCWithInstall<typeof Menu> & {
    MenuItem: typeof MenuItem;
    MenuItemGroup: typeof MenuItemGroup;
    SubMenu: typeof SubMenu;
};
export default ElMenu;
export declare const ElMenuItem: SFCWithInstall<typeof MenuItem>;
export declare const ElMenuItemGroup: SFCWithInstall<typeof MenuItemGroup>;
export declare const ElSubMenu: SFCWithInstall<typeof SubMenu>;
export * from './src/menu';
export * from './src/menu-item';
export * from './src/menu-item-group';
export * from './src/sub-menu';
export * from './src/types';
export * from './src/instance';
