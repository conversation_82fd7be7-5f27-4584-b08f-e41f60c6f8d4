@echo off
chcp 65001 >nul
echo ========================================
echo Build Environment Test Script
echo ========================================

echo Testing build environment...
echo.

:: Test 1: Check CMake
echo [1/5] Checking CMake...
cmake --version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ❌ CMake not found
    echo   Please install CMake from: https://cmake.org/download/
    set BUILD_OK=0
) else (
    echo ✅ CMake found
    cmake --version | findstr "cmake version"
)

:: Test 2: Check Visual Studio
echo.
echo [2/5] Checking Visual Studio Build Tools...
where cl >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ❌ Visual Studio Build Tools not found
    echo   Please install Visual Studio 2019+ or run from Developer Command Prompt
    set BUILD_OK=0
) else (
    echo ✅ Visual Studio Build Tools found
    cl 2>&1 | findstr "Microsoft"
)

:: Test 3: Check Windows SDK
echo.
echo [3/5] Checking Windows SDK...
if exist "C:\Program Files (x86)\Windows Kits\10" (
    echo ✅ Windows SDK found
) else (
    echo ⚠️  Windows SDK location not found (might still work)
)

:: Test 4: Check project files
echo.
echo [4/5] Checking project files...
if exist "CMakeLists_simple.txt" (
    echo ✅ CMakeLists_simple.txt found
) else (
    echo ❌ CMakeLists_simple.txt missing
    set BUILD_OK=0
)

if exist "src\demo_main.cpp" (
    echo ✅ src\demo_main.cpp found
) else (
    echo ❌ src\demo_main.cpp missing
    set BUILD_OK=0
)

:: Test 5: Try minimal build
echo.
echo [5/5] Testing minimal build configuration...
if not exist test_build mkdir test_build
cd test_build

echo Configuring test project...
cmake .. -f ../CMakeLists_simple.txt -G "Visual Studio 16 2019" -A x64 >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ❌ CMake configuration failed
    echo Trying alternative generator...
    cmake .. -f ../CMakeLists_simple.txt -G "Visual Studio 17 2022" -A x64 >nul 2>&1
    if %ERRORLEVEL% neq 0 (
        echo ❌ All generators failed
        set BUILD_OK=0
    ) else (
        echo ✅ CMake configuration successful (VS 2022)
    )
) else (
    echo ✅ CMake configuration successful (VS 2019)
)

cd ..

:: Summary
echo.
echo ========================================
echo BUILD ENVIRONMENT SUMMARY
echo ========================================

if "%BUILD_OK%" == "0" (
    echo ❌ Build environment has issues
    echo.
    echo Next steps:
    echo 1. Install missing components
    echo 2. Run from Visual Studio Developer Command Prompt
    echo 3. Try build_simple.bat after fixing issues
) else (
    echo ✅ Build environment looks good
    echo.
    echo Ready to build! Run:
    echo   build_simple.bat
)

echo.
pause