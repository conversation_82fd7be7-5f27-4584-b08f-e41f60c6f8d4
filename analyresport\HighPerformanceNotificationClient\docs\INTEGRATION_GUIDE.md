# 后端对接集成指南

## API认证状态分析

根据对现有后端代码的分析，发现以下认证状态：

### 需要认证的API
- `/api/v2/gamification/*` - 游戏化API（有`[Authorize]`标记）
- `/api/v2/notifications/*` - 通知API
- `/hubs/notification` - SignalR通知中心

### 不需要认证的API（用于测试）
- `/api/v2/work-summary/*` - 工作汇总API（临时移除了`[Authorize]`）
- `/api/v2/work-summary/update` - 有`[AllowAnonymous]`标记

## 对接配置

### 1. 配置文件修改

编辑 `config/config.json`：

```json
{
  "server": {
    "ip": "127.0.0.1",
    "udp_port": 8081,
    "websocket_port": 8080,
    "signalr_hub": "/hubs/notification",
    "api_base_url": "http://localhost:5000/api",
    "api_version": "v2"
  },
  "auth": {
    "username": "your_username",
    "password": "your_password", 
    "auto_login": true,
    "support_anonymous": true
  }
}
```

### 2. 认证策略

客户端支持多种认证模式：

#### 模式1：JWT令牌认证（推荐）
```cpp
// 自动获取和管理JWT令牌
auth_manager->login("username", "password");
// 后续API调用自动携带Authorization头
```

#### 模式2：匿名模式（用于测试）
```cpp
// 对于不需要认证的API
auth_manager->enableAnonymousMode(true);
```

#### 模式3：混合模式（智能适配）
```cpp
// 客户端自动检测API认证要求
// 需要认证时使用令牌，不需要时匿名访问
```

## SignalR集成

### 连接配置

```cpp
// SignalR连接字符串
String signalr_url = "ws://localhost:8080/hubs/notification";

// 如果需要认证
String auth_token = auth_manager->getCurrentToken();
signalr_client->connect(signalr_url, auth_token);
```

### 消息订阅

```cpp
// 订阅游戏化奖励通知
signalr_client->on("GamificationReward", [](const String& data) {
    // 处理游戏化奖励通知
    handleGamificationReward(data);
});

// 订阅任务更新通知  
signalr_client->on("TaskUpdate", [](const String& data) {
    // 处理任务更新通知
    handleTaskUpdate(data);
});
```

## API端点映射

### 游戏化相关API

```cpp
// 获取用户游戏化统计
GET /api/v2/gamification/stats
Headers: Authorization: Bearer <token>

// 获取排行榜
GET /api/v2/gamification/leaderboard
Headers: Authorization: Bearer <token>

// 获取用户成就
GET /api/v2/gamification/achievements  
Headers: Authorization: Bearer <token>
```

### 工作汇总API

```cpp
// 获取工作汇总（可匿名访问）
GET /api/v2/work-summary?periodType=weekly&limit=50

// 获取排行榜（可匿名访问）
GET /api/v2/work-summary/leaderboard?limit=20

// 手动更新汇总（可匿名访问）
POST /api/v2/work-summary/update?periodType=weekly
```

## 错误处理

### 认证错误处理

```cpp
void handleAuthError(int status_code, const String& response) {
    switch (status_code) {
        case 401:
            // 未认证或令牌过期
            auth_manager->refreshToken();
            break;
        case 403:
            // 访问被拒绝
            logger->error("Access denied: " + response);
            break;
        case 500:
            // 服务器错误
            logger->error("Server error: " + response);
            break;
    }
}
```

### 网络错误处理

```cpp
void handleNetworkError(const String& error) {
    // 自动降级到备用通道
    if (current_protocol == UDP) {
        switchToWebSocket();
    } else {
        // 启动重连逻辑
        startReconnection();
    }
}
```

## 消息格式

### 游戏化奖励消息

```json
{
  "type": "GAMIFICATION_REWARD",
  "title": "🎮 获得奖励",
  "content": "完成任务获得 50 积分",
  "reward_info": {
    "points": 50,
    "coins": 10,
    "diamonds": 0,
    "experience": 20,
    "reason": "TASK_COMPLETED"
  },
  "timestamp": "2025-06-27T10:30:00Z"
}
```

### 任务更新消息

```json
{
  "type": "TASK_UPDATE", 
  "title": "📋 任务更新",
  "content": "任务 #123 已被领取",
  "task_info": {
    "task_id": 123,
    "task_name": "设备维护检查",
    "status": "in_progress",
    "assigned_to": "张三"
  },
  "timestamp": "2025-06-27T10:30:00Z"
}
```

## 性能优化建议

### 1. 连接池管理

```cpp
// 复用HTTP连接
class ConnectionPool {
    static const int MAX_CONNECTIONS = 5;
    std::vector<HINTERNET> connections;
    
    HINTERNET getConnection();
    void releaseConnection(HINTERNET handle);
};
```

### 2. 请求批量处理

```cpp
// 批量发送多个API请求
class BatchRequestManager {
    void addRequest(const ApiRequest& request);
    void flush(); // 批量发送所有请求
};
```

### 3. 缓存策略

```cpp
// 缓存用户信息和配置
class ApiCache {
    std::unordered_map<String, CacheEntry> cache;
    Duration cache_ttl = std::chrono::minutes(5);
    
    void set(const String& key, const String& value);
    String get(const String& key);
};
```

## 测试和调试

### 1. 启用调试模式

```bash
./HighPerformanceNotificationClient.exe --debug
```

### 2. API测试工具

```bash
# 测试认证
./config_tool.exe --test-auth --username=test --password=test

# 测试连接
./performance_benchmark.exe --test-connection

# 查看详细日志
tail -f logs/client.log
```

### 3. 网络抓包分析

```bash
# 使用Wireshark或Fiddler监控网络流量
# 检查HTTP请求头和响应状态码
# 验证WebSocket连接和消息格式
```

## 部署注意事项

### 1. 防火墙配置

```bash
# 确保以下端口开放
- TCP 5000: HTTP API
- TCP 8080: WebSocket/SignalR  
- UDP 8081: UDP通信
```

### 2. 服务器配置

```csharp
// 确保后端Startup.cs中CORS配置正确
services.AddCors(options => {
    options.AddPolicy("AllowNotificationClient", builder => {
        builder.WithOrigins("http://localhost")
               .AllowAnyMethod()
               .AllowAnyHeader()
               .AllowCredentials();
    });
});
```

### 3. 负载均衡

```nginx
# Nginx配置WebSocket代理
upstream notification_backend {
    server 127.0.0.1:8080;
}

server {
    location /hubs/notification {
        proxy_pass http://notification_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
}