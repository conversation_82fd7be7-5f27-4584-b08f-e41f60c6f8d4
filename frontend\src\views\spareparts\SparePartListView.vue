<template>
  <div class="spare-part-list-view">
    <div class="page-header">
      <h2>备件台账管理</h2>
      <el-button type="primary" @click="handleAddClick">新增备件</el-button>
    </div>

    <!-- 搜索和筛选区域 -->
    <div class="filter-container">
      <el-form :inline="true" :model="queryParams" class="demo-form-inline">
        <el-form-item label="备件名称">
          <el-input v-model="queryParams.name" placeholder="备件名称" clearable @keyup.enter="handleQuery" />
        </el-form-item>
        <el-form-item label="备件编号">
          <el-input v-model="queryParams.code" placeholder="备件编号" clearable @keyup.enter="handleQuery" />
        </el-form-item>
        <el-form-item label="备件类型">
          <el-select v-model="queryParams.typeId" placeholder="备件类型" clearable>
            <el-option v-for="item in typeOptions" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="库存状态">
          <el-select v-model="queryParams.stockStatus" placeholder="库存状态" clearable>
            <el-option label="正常" value="normal" />
            <el-option label="预警" value="warning" />
            <el-option label="不足" value="danger" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 表格区域 -->
    <el-table
      v-loading="loading"
      :data="sparePartList"
      border
      style="width: 100%"
      @sort-change="handleSortChange"
    >
      <el-table-column prop="code" label="备件编号" width="120" sortable="custom" />
      <el-table-column prop="materialNumber" label="物料编号" width="120" sortable="custom" />
      <el-table-column prop="name" label="备件名称" width="150" sortable="custom" />
      <el-table-column prop="typeName" label="备件类型" width="120" />
      <el-table-column prop="specification" label="规格型号" width="120" />
      <el-table-column prop="brand" label="品牌" width="100" />
      <el-table-column prop="stockQuantity" label="库存数量" width="100" sortable="custom">
        <template #default="scope">
          <span :class="getStockClass(scope.row)">{{ scope.row.stockQuantity }} {{ scope.row.unit }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="minStock" label="最小库存" width="100" />
      <el-table-column prop="locationName" label="库位" width="120" />
      <el-table-column prop="price" label="单价(元)" width="100">
        <template #default="scope">
          {{ scope.row.price ? scope.row.price.toFixed(2) : '-' }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="280" fixed="right">
        <template #default="scope">
          <el-button type="info" size="small" @click="handleDetail(scope.row)">详情</el-button>
          <el-button type="primary" size="small" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button type="warning" size="small" @click="handleReturnToFactory(scope.row)">返厂</el-button>
          <el-button type="success" size="small" @click="handleInbound(scope.row)">入库</el-button>
          <el-button type="danger" size="small" @click="handleOutbound(scope.row)">出库</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页组件 -->
    <div class="pagination-container">
      <el-pagination
        v-model:currentPage="queryParams.pageIndex"
        v-model:page-size="queryParams.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 新增/编辑备件对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'add' ? '新增备件' : '编辑备件'"
      width="650px"
      @close="resetForm"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
        style="max-height: 500px; overflow-y: auto;"
      >
        <el-form-item label="备件编号" prop="code">
          <el-input v-model="form.code" placeholder="请输入备件编号" />
        </el-form-item>
        <el-form-item label="物料编号" prop="materialNumber">
          <el-input v-model="form.materialNumber" placeholder="请输入物料编号" />
        </el-form-item>
        <el-form-item label="备件名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入备件名称" />
        </el-form-item>
        <el-form-item label="备件类型" prop="typeId">
          <el-select v-model="form.typeId" placeholder="请选择备件类型" style="width: 100%">
            <el-option v-for="item in typeOptions" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="规格型号" prop="specification">
          <el-input v-model="form.specification" placeholder="请输入规格型号" />
        </el-form-item>
        <el-form-item label="品牌" prop="brand">
          <el-input v-model="form.brand" placeholder="请输入品牌" />
        </el-form-item>
        <el-form-item label="单位" prop="unit">
          <el-input v-model="form.unit" placeholder="请输入单位" />
        </el-form-item>
        <el-form-item label="初始库存" prop="initialStock" v-if="dialogType === 'add'">
          <el-input-number v-model="form.initialStock" :min="0" placeholder="请输入初始库存" />
        </el-form-item>
        <el-form-item label="当前库存" v-if="dialogType === 'edit'">
          <div class="stock-display">
            <span class="stock-value">{{ form.stockQuantity || 0 }} {{ form.unit }}</span>
            <el-button
              type="text"
              size="small"
              @click="showStockAdjustment = !showStockAdjustment"
              v-if="canAdjustStock"
            >
              {{ showStockAdjustment ? '取消调整' : '库存调整' }}
            </el-button>
          </div>
          <div v-if="showStockAdjustment && canAdjustStock" class="stock-adjustment">
            <el-alert
              title="库存调整"
              type="warning"
              description="库存调整将生成调整记录，请谨慎操作。建议通过入库/出库操作来管理库存。"
              :closable="false"
              style="margin: 10px 0;"
            />
            <el-form-item label="调整数量" prop="stockAdjustment">
              <el-input-number
                v-model="form.stockAdjustment"
                placeholder="正数为增加，负数为减少"
                :precision="0"
              />
            </el-form-item>
            <el-form-item label="调整原因" prop="adjustmentReason">
              <el-input
                v-model="form.adjustmentReason"
                type="textarea"
                placeholder="请输入调整原因"
                :rows="2"
              />
            </el-form-item>
          </div>
        </el-form-item>
        <el-form-item label="预警阈值" prop="warningThreshold">
          <el-input-number v-model="form.warningThreshold" :min="0" placeholder="请输入预警阈值" />
        </el-form-item>
        <el-form-item label="最小库存" prop="minStock">
          <el-input-number v-model="form.minStock" :min="0" placeholder="请输入最小库存" />
        </el-form-item>
        <el-form-item label="库位" prop="locationId">
          <el-select v-model="form.locationId" placeholder="请选择库位" style="width: 100%">
            <el-option v-for="item in locationOptions" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="价格(元)" prop="price">
          <el-input-number v-model="form.price" :min="0" :precision="2" placeholder="请输入价格" />
        </el-form-item>
        <el-form-item label="备注" prop="remarks">
          <el-input v-model="form.remarks" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 入库对话框 -->
    <el-dialog v-model="inboundDialogVisible" title="备件入库" width="550px">
      <el-form
        ref="inboundFormRef"
        :model="inboundForm"
        :rules="inboundRules"
        label-width="100px"
      >
        <el-form-item label="备件名称">
          <el-input v-model="currentSparePart.name" disabled />
        </el-form-item>
        <el-form-item label="当前库存">
          <el-input :value="`${currentSparePart.stockQuantity} ${currentSparePart.unit || ''}`" disabled />
        </el-form-item>
        <el-form-item label="入库数量" prop="quantity">
          <el-input-number v-model="inboundForm.quantity" :min="1" />
        </el-form-item>
        <el-form-item label="库位" prop="locationId">
          <el-select v-model="inboundForm.locationId" placeholder="请选择库位">
            <el-option v-for="item in locationOptions" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="入库类型" prop="reasonType">
          <el-select v-model="inboundForm.reasonType" placeholder="请选择入库类型">
            <el-option label="采购入库" :value="1" />
            <el-option label="退回入库" :value="2" />
            <el-option label="盘点调整" :value="5" />
          </el-select>
        </el-form-item>
        <el-form-item label="关联单号" prop="referenceNumber">
          <el-input v-model="inboundForm.referenceNumber" placeholder="请输入关联单号" />
        </el-form-item>
        <el-form-item label="备注" prop="remarks">
          <el-input v-model="inboundForm.remarks" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="inboundDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitInbound">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 出库对话框 -->
    <el-dialog v-model="outboundDialogVisible" title="备件出库" width="550px">
      <el-form
        ref="outboundFormRef"
        :model="outboundForm"
        :rules="outboundRules"
        label-width="100px"
      >
        <el-form-item label="备件名称">
          <el-input v-model="currentSparePart.name" disabled />
        </el-form-item>
        <el-form-item label="当前库存">
          <el-input :value="`${currentSparePart.stockQuantity} ${currentSparePart.unit || ''}`" disabled />
        </el-form-item>
        <el-form-item label="出库数量" prop="quantity">
          <el-input-number v-model="outboundForm.quantity" :min="1" :max="currentSparePart.stockQuantity" />
        </el-form-item>
        <el-form-item label="库位" prop="locationId">
          <el-select v-model="outboundForm.locationId" placeholder="请选择库位">
            <el-option v-for="item in locationOptions" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="出库类型" prop="reasonType">
          <el-select v-model="outboundForm.reasonType" placeholder="请选择出库类型">
            <el-option label="领用出库" :value="3" />
            <el-option label="报废出库" :value="4" />
            <el-option label="盘点调整" :value="5" />
          </el-select>
        </el-form-item>
        <el-form-item label="关联单号" prop="referenceNumber">
          <el-input v-model="outboundForm.referenceNumber" placeholder="请输入关联单号" />
        </el-form-item>
        <el-form-item label="关联资产" prop="relatedAssetId">
          <el-input v-model="outboundForm.relatedAssetId" placeholder="请输入关联资产ID" />
        </el-form-item>
        <el-form-item label="关联故障" prop="relatedFaultId">
          <el-input v-model="outboundForm.relatedFaultId" placeholder="请输入关联故障ID" />
        </el-form-item>
        <el-form-item label="备注" prop="remarks">
          <el-input v-model="outboundForm.remarks" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="outboundDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitOutbound">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 备件详情对话框 -->
    <el-dialog v-model="detailDialogVisible" title="备件详情" width="800px">
      <div v-if="currentSparePart" class="detail-content">
        <!-- 基本信息 -->
        <el-card class="detail-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span class="card-title">📋 基本信息</span>
            </div>
          </template>

          <el-descriptions :column="2" border>
            <el-descriptions-item label="备件编号">
              <el-tag type="primary">{{ currentSparePart.code }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="物料编号">
              {{ currentSparePart.materialNumber || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="备件名称">
              <strong>{{ currentSparePart.name }}</strong>
            </el-descriptions-item>
            <el-descriptions-item label="备件类型">
              {{ currentSparePart.typeName || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="规格型号">
              {{ currentSparePart.specification || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="品牌">
              {{ currentSparePart.brand || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="单位">
              {{ currentSparePart.unit || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="库位">
              {{ currentSparePart.locationName || '-' }}
            </el-descriptions-item>
            <el-descriptions-item label="单价">
              <span class="price">¥{{ currentSparePart.price ? currentSparePart.price.toFixed(2) : '0.00' }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="备注">
              {{ currentSparePart.remarks || '-' }}
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 库存信息 -->
        <el-card class="detail-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span class="card-title">📦 库存信息</span>
              <el-tag :type="getStockTagType(currentSparePart)" size="small">
                {{ getStockStatusText(currentSparePart) }}
              </el-tag>
            </div>
          </template>

          <el-descriptions :column="3" border>
            <el-descriptions-item label="当前库存">
              <span :class="getStockClass(currentSparePart)" class="stock-number">
                {{ currentSparePart.stockQuantity }} {{ currentSparePart.unit }}
              </span>
            </el-descriptions-item>
            <el-descriptions-item label="最小库存">
              <span class="threshold-number">{{ currentSparePart.minStock }} {{ currentSparePart.unit }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="预警阈值">
              <span class="threshold-number">{{ currentSparePart.warningThreshold }} {{ currentSparePart.unit }}</span>
            </el-descriptions-item>
          </el-descriptions>

          <!-- 库存状态图表 -->
          <div class="stock-chart">
            <div class="chart-title">库存状态</div>
            <div class="chart-bar">
              <div class="bar-container">
                <div
                  class="bar-fill"
                  :style="{
                    width: getStockPercentage(currentSparePart) + '%',
                    backgroundColor: getStockBarColor(currentSparePart)
                  }"
                ></div>
                <div class="bar-markers">
                  <div
                    class="marker min-marker"
                    :style="{ left: getMinStockPercentage(currentSparePart) + '%' }"
                    title="最小库存"
                  ></div>
                  <div
                    class="marker warning-marker"
                    :style="{ left: getWarningPercentage(currentSparePart) + '%' }"
                    title="预警阈值"
                  ></div>
                </div>
              </div>
              <div class="chart-labels">
                <span>0</span>
                <span>{{ Math.max(currentSparePart.stockQuantity, currentSparePart.warningThreshold * 2) }}</span>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 操作记录 -->
        <el-card class="detail-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span class="card-title">📝 最近操作记录</span>
              <el-button size="small" @click="loadTransactionHistory">刷新</el-button>
            </div>
          </template>

          <el-table
            :data="transactionHistory"
            v-loading="historyLoading"
            size="small"
            max-height="300"
          >
            <el-table-column prop="operationTime" label="操作时间" width="150">
              <template #default="{ row }">
                {{ formatDateTime(row.operationTime) }}
              </template>
            </el-table-column>
            <el-table-column prop="typeName" label="操作类型" width="100">
              <template #default="{ row }">
                <el-tag :type="getTransactionTypeTag(row.type)" size="small">
                  {{ row.typeName }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="quantity" label="数量" width="80" align="center">
              <template #default="{ row }">
                <span :class="row.type === 1 ? 'text-success' : 'text-danger'">
                  {{ row.type === 1 ? '+' : '-' }}{{ Math.abs(row.quantity) }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="referenceNumber" label="关联单号" width="120" />
            <el-table-column prop="operatorName" label="操作人" width="100" />
            <el-table-column prop="remarks" label="备注" min-width="150" />
          </el-table>
        </el-card>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="handleEdit(currentSparePart)">编辑</el-button>
          <el-button type="success" @click="handleInbound(currentSparePart)">入库</el-button>
          <el-button type="warning" @click="handleOutbound(currentSparePart)">出库</el-button>
          <el-button type="info" @click="handleStatusManagement(currentSparePart)">状态管理</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 备件状态管理对话框 -->
    <el-dialog v-model="statusManagementDialogVisible" title="备件状态管理" width="1200px" top="5vh">
      <SparePartStatusManager
        v-if="statusManagementDialogVisible && currentSparePart.id"
        :part-id="currentSparePart.id"
      />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="statusManagementDialogVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useSparePartsStore } from '@/stores/modules/spareparts';
import * as sparePartsApi from '@/api/spareparts';
import SparePartStatusManager from './components/SparePartStatusManager.vue';

// 创建响应式状态
const loading = ref(false);
const sparePartList = ref([]);
const total = ref(0);
const dialogVisible = ref(false);
const dialogType = ref('add'); // 'add' 或 'edit'
const formRef = ref(null);
const inboundDialogVisible = ref(false);
const outboundDialogVisible = ref(false);
const inboundFormRef = ref(null);
const outboundFormRef = ref(null);
const currentSparePart = ref({});
const detailDialogVisible = ref(false);
const statusManagementDialogVisible = ref(false);
const transactionHistory = ref([]);
const historyLoading = ref(false);
const showStockAdjustment = ref(false);
const canAdjustStock = ref(true); // 这里可以根据用户权限来设置

// 获取store
const sparePartsStore = useSparePartsStore();

// 查询参数
const queryParams = reactive({
  pageIndex: 1,
  pageSize: 10,
  name: '',
  code: '',
  typeId: '',
  stockStatus: '',
  sortBy: '',
  sortOrder: ''
});

// 表单数据
const form = reactive({
  id: null,
  code: '',
  materialNumber: '',
  name: '',
  typeId: '',
  specification: '',
  brand: '',
  unit: '',
  initialStock: 0,
  stockQuantity: 0,
  stockAdjustment: 0,
  adjustmentReason: '',
  warningThreshold: 10,
  minStock: 5,
  locationId: '',
  price: null,
  remarks: ''
});

// 入库表单数据
const inboundForm = reactive({
  partId: null,
  quantity: 1,
  locationId: '',
  reasonType: 1,
  referenceNumber: '',
  remarks: ''
});

// 出库表单数据
const outboundForm = reactive({
  partId: null,
  quantity: 1,
  locationId: '',
  reasonType: 3,
  referenceNumber: '',
  relatedAssetId: null,
  relatedFaultId: null,
  remarks: ''
});

// 表单验证规则
const rules = {
  code: [
    { required: true, message: '请输入备件编号', trigger: 'blur' },
    { max: 50, message: '长度不能超过50个字符', trigger: 'blur' }
  ],
  materialNumber: [
    { max: 50, message: '长度不能超过50个字符', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入备件名称', trigger: 'blur' },
    { max: 100, message: '长度不能超过100个字符', trigger: 'blur' }
  ],
  typeId: [
    { required: true, message: '请选择备件类型', trigger: 'change' }
  ],
  unit: [
    { required: true, message: '请输入单位', trigger: 'blur' },
    { max: 10, message: '长度不能超过10个字符', trigger: 'blur' }
  ],
  locationId: [
    { required: true, message: '请选择库位', trigger: 'change' }
  ]
};

// 入库表单验证规则
const inboundRules = {
  quantity: [
    { required: true, message: '请输入入库数量', trigger: 'blur' },
    { type: 'number', min: 1, message: '数量必须大于0', trigger: 'blur' }
  ],
  locationId: [
    { required: true, message: '请选择库位', trigger: 'change' }
  ],
  reasonType: [
    { required: true, message: '请选择入库类型', trigger: 'change' }
  ]
};

// 出库表单验证规则
const outboundRules = {
  quantity: [
    { required: true, message: '请输入出库数量', trigger: 'blur' },
    { type: 'number', min: 1, message: '数量必须大于0', trigger: 'blur' }
  ],
  locationId: [
    { required: true, message: '请选择库位', trigger: 'change' }
  ],
  reasonType: [
    { required: true, message: '请选择出库类型', trigger: 'change' }
  ]
};

// 计算属性：备件类型选项
const typeOptions = computed(() => {
  return sparePartsStore.typesTree;
});

// 计算属性：库位选项
const locationOptions = computed(() => {
  return sparePartsStore.locations;
});

// 生命周期钩子
onMounted(async () => {
  // 加载备件类型和库位数据
  await Promise.all([
    sparePartsStore.fetchTypesTree(),
    sparePartsStore.fetchLocations()
  ]);
  
  // 加载备件列表
  fetchSparePartList();
});

// 获取备件列表数据
const fetchSparePartList = async () => {
  loading.value = true;
  try {
    const response = await sparePartsApi.getSpareParts({
      pageIndex: queryParams.pageIndex,
      pageSize: queryParams.pageSize,
      name: queryParams.name || undefined,
      code: queryParams.code || undefined,
      typeId: queryParams.typeId || undefined,
      stockStatus: queryParams.stockStatus || undefined,
      sortBy: queryParams.sortBy || undefined,
      sortOrder: queryParams.sortOrder || undefined
    });

    if (response.success) {
      sparePartList.value = response.data.items;
      total.value = response.data.totalCount;
    } else {
      ElMessage.error(response.message || '获取备件列表失败');
    }
  } catch (error) {
    console.error('获取备件列表出错:', error);
    ElMessage.error('获取备件列表失败');
  } finally {
    loading.value = false;
  }
};

// 处理查询
const handleQuery = () => {
  queryParams.pageIndex = 1;
  fetchSparePartList();
};

// 重置查询
const resetQuery = () => {
  queryParams.name = '';
  queryParams.code = '';
  queryParams.typeId = '';
  queryParams.stockStatus = '';
  queryParams.sortBy = '';
  queryParams.sortOrder = '';
  handleQuery();
};

// 处理排序变化
const handleSortChange = (column) => {
  if (column.prop && column.order) {
    queryParams.sortBy = column.prop;
    queryParams.sortOrder = column.order === 'ascending' ? 'asc' : 'desc';
  } else {
    queryParams.sortBy = '';
    queryParams.sortOrder = '';
  }
  fetchSparePartList();
};

// 处理分页大小变化
const handleSizeChange = (size) => {
  queryParams.pageSize = size;
  fetchSparePartList();
};

// 处理页码变化
const handleCurrentChange = (page) => {
  queryParams.pageIndex = page;
  fetchSparePartList();
};

// 获取库存状态样式
const getStockClass = (row) => {
  if (row.stockQuantity <= row.minStock) {
    return 'stock-danger';
  } else if (row.stockQuantity <= row.warningThreshold) {
    return 'stock-warning';
  }
  return 'stock-normal';
};

// 处理添加按钮点击
const handleAddClick = () => {
  dialogType.value = 'add';
  resetForm();
  dialogVisible.value = true;
};

// 处理详情按钮点击
const handleDetail = (row) => {
  currentSparePart.value = row;
  detailDialogVisible.value = true;
  loadTransactionHistory();
};

// 处理状态管理按钮点击
const handleStatusManagement = (row) => {
  currentSparePart.value = row;
  statusManagementDialogVisible.value = true;
};

// 处理编辑按钮点击
const handleEdit = (row) => {
  dialogType.value = 'edit';
  Object.keys(form).forEach(key => {
    form[key] = row[key];
  });
  dialogVisible.value = true;
};

// 处理返厂按钮点击
const handleReturnToFactory = (row) => {
  ElMessage.info(`申请返厂：${row.name}`);
  // TODO: 实现返厂申请对话框
  // returnToFactoryDialogVisible.value = true;
  // currentSparePart.value = row;
};

// 处理入库按钮点击
const handleInbound = (row) => {
  currentSparePart.value = row;
  inboundForm.partId = row.id;
  inboundForm.locationId = row.locationId;
  inboundForm.quantity = 1;
  inboundDialogVisible.value = true;
};

// 处理出库按钮点击
const handleOutbound = (row) => {
  currentSparePart.value = row;
  outboundForm.partId = row.id;
  outboundForm.locationId = row.locationId;
  outboundForm.quantity = 1;
  outboundForm.relatedAssetId = null;
  outboundForm.relatedFaultId = null;
  outboundDialogVisible.value = true;
};

// 处理删除按钮点击
const handleDelete = (row) => {
  ElMessageBox.confirm(`确认删除备件 "${row.name}" 吗？`, '确认删除', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const response = await sparePartsApi.deleteSparePart(row.id);
      if (response.success) {
        ElMessage.success('删除成功');
        fetchSparePartList();
      } else {
        ElMessage.error(response.message || '删除失败');
      }
    } catch (error) {
      console.error('删除备件出错:', error);
      ElMessage.error('删除失败');
    }
  }).catch(() => {});
};

// 加载交易历史记录
const loadTransactionHistory = async () => {
  if (!currentSparePart.value.id) return;

  historyLoading.value = true;
  try {
    const response = await sparePartsApi.getSparePartTransactions({
      sparePartId: currentSparePart.value.id,
      pageIndex: 1,
      pageSize: 10
    });

    if (response.success) {
      transactionHistory.value = response.data.items || [];
    } else {
      console.error('获取交易历史失败:', response.message);
      transactionHistory.value = [];
    }
  } catch (error) {
    console.error('获取交易历史出错:', error);
    transactionHistory.value = [];
  } finally {
    historyLoading.value = false;
  }
};

// 获取库存状态标签类型
const getStockTagType = (row) => {
  if (row.stockQuantity <= row.minStock) return 'danger';
  if (row.stockQuantity <= row.warningThreshold) return 'warning';
  return 'success';
};

// 获取库存状态文本
const getStockStatusText = (row) => {
  if (row.stockQuantity <= row.minStock) return '库存不足';
  if (row.stockQuantity <= row.warningThreshold) return '库存预警';
  return '库存正常';
};

// 获取库存百分比
const getStockPercentage = (row) => {
  const max = Math.max(row.stockQuantity, row.warningThreshold * 2);
  return Math.min((row.stockQuantity / max) * 100, 100);
};

// 获取最小库存百分比
const getMinStockPercentage = (row) => {
  const max = Math.max(row.stockQuantity, row.warningThreshold * 2);
  return (row.minStock / max) * 100;
};

// 获取预警阈值百分比
const getWarningPercentage = (row) => {
  const max = Math.max(row.stockQuantity, row.warningThreshold * 2);
  return (row.warningThreshold / max) * 100;
};

// 获取库存条颜色
const getStockBarColor = (row) => {
  if (row.stockQuantity <= row.minStock) return '#f56c6c';
  if (row.stockQuantity <= row.warningThreshold) return '#e6a23c';
  return '#67c23a';
};

// 获取交易类型标签
const getTransactionTypeTag = (type) => {
  // type: 1=入库, 2=出库
  if (type === 1) return 'success';
  if (type === 2) return 'danger';
  return 'info';
};

// 获取交易类型文本
const getTransactionTypeText = (type) => {
  // type: 1=入库, 2=出库
  if (type === 1) return '入库';
  if (type === 2) return '出库';
  return '未知';
};

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return '-';
  return new Date(dateTime).toLocaleString('zh-CN');
};

// 重置表单
const resetForm = () => {
  showStockAdjustment.value = false;

  if (dialogType.value === 'add') {
    Object.keys(form).forEach(key => {
      if (key === 'warningThreshold') {
        form[key] = 10;
      } else if (key === 'minStock') {
        form[key] = 5;
      } else if (key === 'initialStock') {
        form[key] = 0;
      } else if (key === 'stockAdjustment') {
        form[key] = 0;
      } else {
        form[key] = '';
      }
    });
  }
  if (formRef.value) {
    formRef.value.resetFields();
  }
};

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return;

  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        let response;

        // 处理库存调整
        if (dialogType.value === 'edit' && form.stockAdjustment && form.stockAdjustment !== 0) {
          // 验证调整原因
          if (!form.adjustmentReason || form.adjustmentReason.trim() === '') {
            ElMessage.error('请输入库存调整原因');
            return;
          }

          // 先更新备件基本信息
          response = await sparePartsApi.updateSparePart(form.id, form);
          if (!response.success) {
            ElMessage.error(response.message || '更新备件信息失败');
            return;
          }

          // 然后进行库存调整
          const adjustmentResponse = await sparePartsApi.stockAdjustment({
            partId: form.id,
            adjustmentQuantity: form.stockAdjustment,
            locationId: form.locationId,
            reason: form.adjustmentReason,
            referenceNumber: `ADJ-${Date.now()}`
          });

          if (adjustmentResponse.success) {
            ElMessage.success('备件信息更新成功，库存调整完成');
          } else {
            ElMessage.warning(`备件信息更新成功，但库存调整失败: ${adjustmentResponse.message}`);
          }
        } else {
          // 正常的新增或更新操作
          if (dialogType.value === 'add') {
            response = await sparePartsApi.createSparePart(form);
          } else {
            response = await sparePartsApi.updateSparePart(form.id, form);
          }

          if (response.success) {
            ElMessage.success(dialogType.value === 'add' ? '新增成功' : '更新成功');
          } else {
            ElMessage.error(response.message || (dialogType.value === 'add' ? '新增失败' : '更新失败'));
            return;
          }
        }

        dialogVisible.value = false;
        fetchSparePartList();
      } catch (error) {
        console.error(dialogType.value === 'add' ? '新增备件出错:' : '更新备件出错:', error);
        ElMessage.error(dialogType.value === 'add' ? '新增失败' : '更新失败');
      }
    } else {
      return false;
    }
  });
};

// 提交入库表单
const submitInbound = async () => {
  if (!inboundFormRef.value) return;
  
  await inboundFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        const response = await sparePartsApi.sparePartInbound(inboundForm);
        if (response.success) {
          ElMessage.success('入库成功');
          inboundDialogVisible.value = false;
          fetchSparePartList();
        } else {
          ElMessage.error(response.message || '入库失败');
        }
      } catch (error) {
        console.error('备件入库出错:', error);
        ElMessage.error('入库失败');
      }
    } else {
      return false;
    }
  });
};

// 提交出库表单
const submitOutbound = async () => {
  if (!outboundFormRef.value) return;
  
  await outboundFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        const response = await sparePartsApi.sparePartOutbound(outboundForm);
        if (response.success) {
          ElMessage.success('出库成功');
          outboundDialogVisible.value = false;
          fetchSparePartList();
        } else {
          ElMessage.error(response.message || '出库失败');
        }
      } catch (error) {
        console.error('备件出库出错:', error);
        ElMessage.error('出库失败');
      }
    } else {
      return false;
    }
  });
};
</script>

<style scoped>
/* 库存状态样式 */
.stock-normal {
  color: #67c23a;
  font-weight: 600;
}

.stock-warning {
  color: #e6a23c;
  font-weight: 600;
}

.stock-danger {
  color: #f56c6c;
  font-weight: 600;
}

/* 详情对话框样式 */
.detail-content {
  max-height: 70vh;
  overflow-y: auto;
}

.detail-card {
  margin-bottom: 16px;
}

.detail-card:last-child {
  margin-bottom: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-weight: 600;
  font-size: 14px;
}

.price {
  color: #e6a23c;
  font-weight: 600;
}

.stock-number {
  font-size: 16px;
  font-weight: 600;
}

.threshold-number {
  color: #909399;
  font-weight: 500;
}

/* 库存图表样式 */
.stock-chart {
  margin-top: 16px;
}

.chart-title {
  font-size: 12px;
  color: #909399;
  margin-bottom: 8px;
}

.chart-bar {
  position: relative;
}

.bar-container {
  position: relative;
  height: 20px;
  background: #f5f7fa;
  border-radius: 10px;
  overflow: hidden;
}

.bar-fill {
  height: 100%;
  border-radius: 10px;
  transition: all 0.3s ease;
}

.bar-markers {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100%;
}

.marker {
  position: absolute;
  top: -2px;
  width: 2px;
  height: 24px;
  border-radius: 1px;
}

.min-marker {
  background: #f56c6c;
}

.warning-marker {
  background: #e6a23c;
}

.chart-labels {
  display: flex;
  justify-content: space-between;
  margin-top: 4px;
  font-size: 12px;
  color: #909399;
}

/* 文本颜色 */
.text-success {
  color: #67c23a;
  font-weight: 600;
}

.text-danger {
  color: #f56c6c;
  font-weight: 600;
}

/* 库存调整样式 */
.stock-display {
  display: flex;
  align-items: center;
  gap: 12px;
}

.stock-value {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.stock-adjustment {
  margin-top: 12px;
  padding: 12px;
  background: #fafafa;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

/* 页面布局样式 */
.spare-part-list-view {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.filter-container {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style>