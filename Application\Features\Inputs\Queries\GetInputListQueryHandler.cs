// File: Application/Features/Inputs/Queries/GetInputListQueryHandler.cs
// Description: 读取CSV并返回输入内容列表
#nullable enable
using MediatR;
using ItAssetsSystem.Application.Common.Dtos;
using ItAssetsSystem.Application.Features.Inputs.Dtos;
using System.Globalization;
using System.Text;
using System.IO;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using System;

namespace ItAssetsSystem.Application.Features.Inputs.Queries
{
    public class GetInputListQueryHandler : IRequestHandler<GetInputListQuery, ApiResponse<List<InputRecordDto>>>
    {
        private readonly string _csvPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "inputs.csv");

        public async Task<ApiResponse<List<InputRecordDto>>> Handle(GetInputListQuery request, CancellationToken cancellationToken)
        {
            var result = new List<InputRecordDto>();
            if (!File.Exists(_csvPath))
                return ApiResponse<List<InputRecordDto>>.CreateSuccess(result);

            using (var stream = new FileStream(_csvPath, FileMode.Open, FileAccess.Read, FileShare.Read))
            using (var reader = new StreamReader(stream, Encoding.UTF8))
            {
                string? line;
                bool isFirst = true;
                while ((line = await reader.ReadLineAsync()) != null)
                {
                    if (isFirst) { isFirst = false; continue; } // 跳过表头
                    var parts = ParseCsvLine(line);
                    if (parts.Length >= 2)
                    {
                        result.Add(new InputRecordDto { Content = parts[0], Time = parts[1] });
                    }
                }
            }
            return ApiResponse<List<InputRecordDto>>.CreateSuccess(result);
        }

        private string[] ParseCsvLine(string line)
        {
            // 简单CSV解析，假设每行格式为 "内容","时间"
            var parts = new List<string>();
            int i = 0;
            while (i < line.Length)
            {
                if (line[i] == '"')
                {
                    int j = i + 1;
                    var sb = new StringBuilder();
                    while (j < line.Length)
                    {
                        if (line[j] == '"')
                        {
                            if (j + 1 < line.Length && line[j + 1] == '"')
                            {
                                sb.Append('"');
                                j += 2;
                            }
                            else
                            {
                                break;
                            }
                        }
                        else
                        {
                            sb.Append(line[j]);
                            j++;
                        }
                    }
                    parts.Add(sb.ToString());
                    i = j + 2; // 跳过逗号
                }
                else
                {
                    i++;
                }
            }
            return parts.ToArray();
        }
    }
} 