// File: frontend/src/views/tasks/components/TaskDetailsDialog.vue
// Description: 任务详情对话框组件，提供任务详细信息的查看功能。

<template>
  <el-dialog
    v-model="visible"
    title="任务详情"
    width="700px"
    destroy-on-close
  >
    <div v-loading="loading" class="task-details-container">
      <template v-if="task">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="任务名称" :span="2">{{ task.name || task.title }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusTagType(task.status)" size="small" effect="light" round>
              {{ formatStatus(task.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="优先级">
            <el-tag :type="getPriorityType(task.priority)" size="small" effect="plain">
              {{ getPriorityLabel(task.priority) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="负责人">
            <UserAvatarStack
              :users="getAllAssignees(task)"
              :is-main-user-primary="true"
              :max-users="4"
              avatar-size="24"
              :overlap="8"
            />
          </el-descriptions-item>
          <el-descriptions-item label="创建人">
            <div v-if="task.creatorUserId" class="user-info">
              <el-avatar :size="24" :src="getFullAvatarUrl(task.creator?.avatar) || defaultAvatar" />
              <span>{{ task.creator?.name || task.creatorUserName || '未知' }}</span>
            </div>
            <span v-else>未知</span>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDateTime(task.createdAt) }}</el-descriptions-item>
          <el-descriptions-item label="开始日期">{{ formatDateTime(task.startDate) || '-' }}</el-descriptions-item>
          <el-descriptions-item label="截止日期">{{ formatDateTime(task.dueDate || task.planEndDate) || '-' }}</el-descriptions-item>
          <el-descriptions-item label="最后更新">{{ formatDateTime(task.updatedAt) || '-' }}</el-descriptions-item>
          <el-descriptions-item label="描述" :span="2">
            <div class="task-description">{{ task.description || '暂无描述' }}</div>
          </el-descriptions-item>
        </el-descriptions>
        
        <div class="related-section" v-if="task.attachments && task.attachments.length > 0">
          <h4>附件</h4>
          <div class="attachments-list">
            <el-tag
              v-for="(file, index) in task.attachments"
              :key="index"
              size="small"
              class="attachment-tag"
              @click="downloadAttachment(file)"
            >
              <el-icon class="el-icon"><Document /></el-icon>
              {{ file.name }}
            </el-tag>
          </div>
        </div>
        
        <div class="related-section comments-section" v-if="task.comments && task.comments.length > 0">
          <h4>评论记录</h4>
          <div class="comments-list">
            <div v-for="(comment, index) in task.comments" :key="index" class="comment-item">
              <div class="comment-header">
                <el-avatar :size="28" :src="getFullAvatarUrl(comment.user?.avatar) || defaultAvatar"></el-avatar>
                <span class="comment-author">{{ comment.user?.name || '用户' }}</span>
                <span class="comment-time">{{ formatDateTime(comment.createdAt) }}</span>
              </div>
              <div class="comment-content">{{ comment.content }}</div>
            </div>
          </div>
        </div>

        <!-- 新增：任务历史记录区域 -->
        <div class="related-section history-section" v-if="task.history && task.history.length > 0">
          <h4>操作记录</h4>
          <el-timeline>
            <el-timeline-item
              v-for="(entry, index) in task.history"
              :key="index"
              :timestamp="formatDateTime(entry.timestamp)"
              placement="top"
            >
              <el-card shadow="hover" class="history-card">
                <div class="history-item-header">
                  <el-avatar :size="24" :src="getFullAvatarUrl(entry.userAvatarUrl) || defaultAvatar" class="history-avatar"></el-avatar>
                  <span class="history-user">{{ entry.userName || '系统' }}</span>
                  <span class="history-action-type">{{ entry.formattedActionType || entry.actionType }}</span>
                </div>
                <p v-if="entry.description" class="history-description">{{ entry.description }}</p>
                <p v-if="entry.fieldName" class="history-field-change">
                  字段: {{ entry.fieldName }} 从 '{{ entry.oldValue || "空" }}' 改为 '{{ entry.newValue || "空" }}'
                </p>
              </el-card>
            </el-timeline-item>
          </el-timeline>
        </div>

      </template>
      
      <div v-else-if="!loading" class="no-data">
        <el-empty description="暂无任务数据" />
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="visible = false">关闭</el-button>
        <el-button type="primary" @click="handleEdit" v-if="task">编辑任务</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Document } from '@element-plus/icons-vue'
import { taskApi } from '@/api/task'
import { getFullAvatarUrl } from '@/stores/modules/user'
import UserAvatarStack from '@/components/UserAvatarStack.vue'

// 对话框可见性和加载状态
const visible = ref(false)
const loading = ref(false)
const task = ref(null)
const defaultAvatar = 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png'

// 打开对话框
const open = async (taskIdFromList) => {
  visible.value = true
  task.value = null // 先清空

  if (!taskIdFromList) {
    ElMessage.warning('任务ID无效')
    loading.value = false; // 确保在ID无效时也停止加载状态
    return
  }

  console.log('Opening TaskDetailsDialog with taskId:', taskIdFromList)

  // 加载完整的任务详情
  await loadTaskDetails(taskIdFromList)
}

// 加载任务详情
const loadTaskDetails = async (taskId) => {
  loading.value = true
  try {
    const response = await taskApi.getTaskById(taskId)
    console.log('Full task details response:', response)
    if (response && response.data) {
      // 使用从 API 获取的完整数据更新 task ref
      task.value = response.data
    }
  } catch (error) {
    console.error('获取任务详情失败:', error)
    ElMessage.error(`获取任务详情失败: ${error.message || '请稍后重试'}`)
    // 保留列表传入的部分数据，或显示错误
    if (!task.value) { // 如果连列表数据都没有
       visible.value = false; // 关闭对话框或显示错误信息
    }
  } finally {
    loading.value = false
  }
}

// 获取所有负责人和协作者
const getAllAssignees = (task) => {
  if (!task) return []
  
  const assignees = []
  const processedUserIds = new Set() // 防止重复添加同一用户
  
  // 记录开始处理日志
  console.log(`处理任务详情[${task.taskId}]的负责人信息：`, task)
  
  // 添加主负责人
  if (task.assigneeUserId) {
    // 尝试获取可能的头像路径
    const avatarUrl = task.assigneeAvatarUrl || task.assigneeAvatar || 
                      (task.assignee?.avatarUrl) || (task.assignee?.avatar) ||
                      getFullAvatarUrl(task.assigneeUserId) || ''
    console.log(`主负责人头像URL(raw): ${avatarUrl}`)
    
    assignees.push({
      id: task.assigneeUserId,
      userId: task.assigneeUserId,
      name: task.assigneeUserName || '未知用户',
      userName: task.assigneeUserName || '未知用户',
      avatarUrl: avatarUrl,
      role: 'Primary'
    })
    processedUserIds.add(task.assigneeUserId)
    console.log(`添加主负责人: ${task.assigneeUserName || '未知用户'}(ID:${task.assigneeUserId})`)
  }
  
  // 处理taskAssignees属性（文件截图中提到的问题点）
  if (task.taskAssignees && Array.isArray(task.taskAssignees) && task.taskAssignees.length > 0) {
    console.log(`处理taskAssignees数组数据，包含${task.taskAssignees.length}条记录`, task.taskAssignees)
    
    task.taskAssignees.forEach(assignee => {
      // 防止重复添加用户
      if (!assignee) return
      
      // 尝试从各种可能的属性中获取用户ID
      const userId = assignee.userId || assignee.id || assignee.assigneeUserId || 
                     (assignee.user?.id) || (assignee.user?.userId) || null
      
      if (!userId || processedUserIds.has(userId)) return
      
      // 尝试从各种可能的属性中获取用户名
      const userName = assignee.userName || assignee.name || assignee.assigneeName || 
                      (assignee.user?.name) || (assignee.user?.userName) || '未知用户'
      
      // 尝试获取可能的头像路径
      let avatarUrl = assignee.avatarUrl || assignee.avatar || assignee.assigneeAvatarUrl || 
                     (assignee.user?.avatarUrl) || (assignee.user?.avatar) || ''
      
      // 如果没有找到头像，尝试使用用户ID获取
      if (!avatarUrl) {
        avatarUrl = getFullAvatarUrl(userId) || ''
      }
                 
      console.log(`从taskAssignees添加: ${userName}(ID:${userId})，头像URL=${avatarUrl}`)
      
      assignees.push({
        id: userId,
        userId: userId,
        name: userName,
        userName: userName,
        avatarUrl: avatarUrl,
        role: assignee.role || (userId === task.assigneeUserId ? 'Primary' : 'Collaborator')
      })
      processedUserIds.add(userId)
    })
  }
  
  // 处理Assignees字段（优先使用，包含完整信息）
  if (task.assignees && Array.isArray(task.assignees) && task.assignees.length > 0) {
    console.log(`处理assignees数组数据，包含${task.assignees.length}条记录`)
    
    task.assignees.forEach(collab => {
      // 防止重复添加用户
      if (!collab) return
      
      const userId = collab.userId || collab.id
      if (!userId || processedUserIds.has(userId)) return
      
      // 尝试获取可能的头像路径
      let avatarUrl = collab.avatarUrl || collab.avatar || 
                     (collab.user?.avatarUrl) || (collab.user?.avatar) || ''
                     
      // 如果没有找到头像，尝试使用用户ID获取
      if (!avatarUrl) {
        avatarUrl = getFullAvatarUrl(userId) || ''
      }
      
      console.log(`协作者头像URL(raw): ${avatarUrl}`)
      
      assignees.push({
        id: userId,
        userId: userId,
        name: collab.userName || collab.name || '未知用户',
        userName: collab.userName || collab.name || '未知用户',
        avatarUrl: avatarUrl,
        role: collab.role || (userId === task.assigneeUserId ? 'Primary' : 'Collaborator')
      })
      processedUserIds.add(userId)
      
      console.log(`从assignees添加: ${collab.userName || collab.name || '未知用户'}(ID:${userId})`)
    })
  }
  // 添加参与者列表中的协作者
  else if (task.participants && Array.isArray(task.participants) && task.participants.length > 0) {
    console.log(`处理participants数据，包含${task.participants.length}条记录`)
    
    task.participants.forEach(participant => {
      // 防止重复添加用户
      if (!participant) return
      
      const userId = participant.id || participant.userId
      if (!userId || processedUserIds.has(userId) || userId === task.assigneeUserId) return
      
      // 尝试获取可能的头像路径
      let avatarUrl = participant.avatarUrl || participant.avatar || 
                     (participant.user?.avatarUrl) || (participant.user?.avatar) || ''
                     
      // 如果没有找到头像，尝试使用用户ID获取
      if (!avatarUrl) {
        avatarUrl = getFullAvatarUrl(userId) || ''
      }
      
      console.log(`参与者头像URL(raw): ${avatarUrl}`)
      
      assignees.push({
        id: userId,
        userId: userId,
        name: participant.name || participant.userName || '未知用户',
        userName: participant.name || participant.userName || '未知用户',
        avatarUrl: avatarUrl,
        role: 'Collaborator'
      })
      processedUserIds.add(userId)
      
      console.log(`从participants添加: ${participant.name || participant.userName || '未知用户'}(ID:${userId})`)
    })
  }
  
  // 记录结果
  console.log(`任务有${assignees.length}个负责人:`, assignees.map(a => ({
    name: a.name,
    role: a.role,
    avatarUrl: a.avatarUrl
  })))
  
  return assignees
}

// 下载附件 (模拟方法)
const downloadAttachment = (file) => {
  ElMessage.info(`模拟下载文件: ${file.name}`)
  // 实际项目中调用API进行下载
  // window.open(file.url, '_blank')
}

// 编辑任务
const handleEdit = () => {
  // 通知父组件打开编辑对话框
  emit('edit', task.value)
  // 关闭当前对话框
  visible.value = false
}

// 格式化日期时间
const formatDateTime = (dateTimeString) => {
  if (!dateTimeString) return '-'
  try {
    const date = new Date(dateTimeString)
    if (isNaN(date.getTime())) return dateTimeString
    
    const year = date.getFullYear()
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')
    const hours = date.getHours().toString().padStart(2, '0')
    const minutes = date.getMinutes().toString().padStart(2, '0')
    
    return `${year}-${month}-${day} ${hours}:${minutes}`
  } catch (e) {
    return dateTimeString
  }
}

// 格式化状态显示文本
const formatStatus = (status) => {
  switch (status?.toLowerCase()) {
    case 'todo': return '未开始'
    case 'inprogress':
    case 'doing': return '进行中'
    case 'done': return '已完成'
    case 'overdue': return '已逾期'
    default: return status || '未知'
  }
}

// 获取状态标签类型
const getStatusTagType = (status) => {
  switch (status?.toLowerCase()) {
    case 'todo': return 'info'
    case 'inprogress':
    case 'doing': return 'warning'
    case 'done': return 'success'
    case 'overdue': return 'danger'
    default: return 'info'
  }
}

// 获取优先级标签
const getPriorityLabel = (priority) => {
  switch (priority) {
    case 'Low':
    case '1':
    case 1: return '低'
    case 'Medium':
    case '2':
    case 2: return '中'
    case 'High':
    case '3':
    case 3: return '高'
    default: return priority || '未设置'
  }
}

// 获取优先级类型
const getPriorityType = (priority) => {
  switch (priority) {
    case 'Low':
    case '1':
    case 1: return 'info'
    case 'Medium':
    case '2':
    case 2: return 'warning'
    case 'High':
    case '3':
    case 3: return 'danger'
    default: return 'info'
  }
}

// 定义组件事件
const emit = defineEmits(['edit'])

// 暴露公共方法
defineExpose({ open })
</script>

<style scoped lang="scss">
.task-details-container {
  padding: 0 10px;
}

.task-description {
  white-space: pre-line;
  line-height: 1.5;
  color: var(--el-text-color-regular);
}

.related-section {
  margin-top: 24px;
  border-top: 1px solid var(--el-border-color-lighter);
  padding-top: 16px;
  
  h4 {
    margin-top: 0;
    margin-bottom: 12px;
    font-weight: 600;
    color: var(--el-text-color-primary);
  }
}

.attachments-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  
  .attachment-tag {
    cursor: pointer;
    display: flex;
    align-items: center;
    
    .el-icon {
      margin-right: 4px;
    }
  }
}

.comments-section {
  .comments-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }
  
  .comment-item {
    background-color: var(--el-fill-color-lighter);
    border-radius: 4px;
    padding: 12px;
    
    .comment-header {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      
      .comment-author {
        margin-left: 8px;
        font-weight: 500;
      }
      
      .comment-time {
        margin-left: auto;
        font-size: 12px;
        color: var(--el-text-color-secondary);
      }
    }
    
    .comment-content {
      line-height: 1.5;
      color: var(--el-text-color-regular);
    }
  }
}

// 新增：历史记录区域样式
.history-section {
  .el-timeline {
    padding-left: 10px; // 给时间线图标留出空间
  }
  .history-card {
    margin-bottom: 10px;
    background-color: #f9f9f9; // 轻微的背景色
    border: 1px solid #e0e0e0;
  }
  .history-item-header {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    .history-avatar {
      margin-right: 8px;
    }
    .history-user {
      font-weight: 500;
      margin-right: 8px;
    }
    .history-action-type {
      font-size: 0.9em;
      color: var(--el-text-color-secondary);
    }
  }
  .history-description {
    font-size: 0.95em;
    margin-bottom: 5px;
    color: var(--el-text-color-regular);
    white-space: pre-wrap; // 保留换行
  }
  .history-field-change {
    font-size: 0.85em;
    color: var(--el-text-color-secondary);
    background-color: #f0f0f0;
    padding: 4px 8px;
    border-radius: 4px;
    display: inline-block;
  }
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.no-data {
  text-align: center;
  padding: 40px 0;
}
</style> 