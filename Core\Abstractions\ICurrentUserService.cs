// File: Core/Abstractions/ICurrentUserService.cs
// Description: 当前用户上下文服务接口

namespace ItAssetsSystem.Core.Abstractions
{
    /// <summary>
    /// 当前用户上下文服务接口
    /// </summary>
    public interface ICurrentUserService
    {
        /// <summary>
        /// 当前登录用户ID
        /// </summary>
        int UserId { get; }
        
        /// <summary>
        /// 当前登录用户名
        /// </summary>
        string UserName { get; }
        
        /// <summary>
        /// 当前用户是否已认证
        /// </summary>
        bool IsAuthenticated { get; }
        
        /// <summary>
        /// 获取当前用户ID
        /// </summary>
        /// <returns>当前用户ID，未登录则返回0</returns>
        int GetCurrentUserId();
        
        /// <summary>
        /// 当前用户是否有指定角色
        /// </summary>
        /// <param name="role">角色名称</param>
        /// <returns>是否有该角色</returns>
        bool IsInRole(string role);
        
        /// <summary>
        /// 当前用户是否有指定权限
        /// </summary>
        /// <param name="permission">权限名称</param>
        /// <returns>是否有该权限</returns>
        bool HasPermission(string permission);
    }
} 