using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;
using ItAssetsSystem.Application.Features.Gamification.Services;
using ItAssetsSystem.Models;
using ItAssetsSystem.Models.Gamification;
using ItAssetsSystem.Core.Services;

namespace ItAssetsSystem.Api.V2.Controllers
{
    [ApiController]
    [Route("api/v2/gamification")]
    [Authorize]
    public class ModernGamificationController : ControllerBase
    {
        private readonly IModernGamificationService _gamificationService;
        private readonly ILogger<ModernGamificationController> _logger;

        public ModernGamificationController(
            IModernGamificationService gamificationService,
            ILogger<ModernGamificationController> logger)
        {
            _gamificationService = gamificationService;
            _logger = logger;
        }

        /// <summary>
        /// 处理用户行为奖励
        /// </summary>
        [HttpPost("reward")]
        public async Task<ActionResult<ApiResponse<RewardData>>> ProcessReward([FromBody] ProcessRewardRequest request)
        {
            try
            {
                var userId = GetCurrentUserId();
                var result = await _gamificationService.ProcessRewardAsync(userId, request.BehaviorCode, request.Context);

                if (result.Success)
                {
                    return ApiResponseFactory.CreateSuccess(result.Data, result.Message);
                }
                else
                {
                    return ApiResponseFactory.CreateFail<RewardData>(result.Message);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理奖励失败: BehaviorCode={BehaviorCode}", request.BehaviorCode);
                return ApiResponseFactory.CreateFail<RewardData>("处理奖励失败");
            }
        }

        /// <summary>
        /// 获取用户个人统计
        /// </summary>
        [HttpGet("stats")]
        public async Task<ActionResult<ApiResponse<UserStatsDto>>> GetUserStats()
        {
            try
            {
                var userId = GetCurrentUserId();
                var stats = await _gamificationService.GetUserStatsAsync(userId);

                return ApiResponseFactory.CreateSuccess(stats, "获取用户统计成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户统计失败");
                return ApiResponseFactory.CreateFail<UserStatsDto>("获取用户统计失败");
            }
        }

        /// <summary>
        /// 获取指定用户统计
        /// </summary>
        [HttpGet("stats/{userId}")]
        public async Task<ActionResult<ApiResponse<UserStatsDto>>> GetUserStats(int userId)
        {
            try
            {
                var stats = await _gamificationService.GetUserStatsAsync(userId);
                return ApiResponseFactory.CreateSuccess(stats, "获取用户统计成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户统计失败: UserId={UserId}", userId);
                return ApiResponseFactory.CreateFail<UserStatsDto>("获取用户统计失败");
            }
        }

        /// <summary>
        /// 手动检查升级
        /// </summary>
        [HttpPost("check-levelup")]
        public async Task<ActionResult<ApiResponse<ItAssetsSystem.Application.Features.Gamification.Services.LevelUpResult>>> CheckLevelUp()
        {
            try
            {
                var userId = GetCurrentUserId();
                var result = await _gamificationService.CheckAndProcessLevelUpAsync(userId);

                if (result.Success)
                {
                    return ApiResponseFactory.CreateSuccess(result, result.Message);
                }
                else
                {
                    return ApiResponseFactory.CreateFail<ItAssetsSystem.Application.Features.Gamification.Services.LevelUpResult>(result.Message);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查升级失败");
                return ApiResponseFactory.CreateFail<ItAssetsSystem.Application.Features.Gamification.Services.LevelUpResult>("检查升级失败");
            }
        }

        private int GetCurrentUserId()
        {
            var userIdClaim = User.FindFirst("UserId")?.Value ?? User.FindFirst("sub")?.Value;
            if (int.TryParse(userIdClaim, out var userId))
            {
                return userId;
            }
            throw new UnauthorizedAccessException("无法获取当前用户ID");
        }
    }



    /// <summary>
    /// 处理奖励请求模型
    /// </summary>
    public class ProcessRewardRequest
    {
        public string BehaviorCode { get; set; }
        public object Context { get; set; }
    }
}
