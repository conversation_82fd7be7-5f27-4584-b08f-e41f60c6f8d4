<template>
  <el-dialog
    v-model="visible"
    title="批量分配任务"
    width="500px"
    :close-on-click-modal="false"
    class="batch-assign-dialog"
    @close="handleClose"
  >
    <!-- 任务预览 -->
    <div class="task-preview-section">
      <h4 class="section-title">
        将要分配的任务 ({{ selectedTasks.length }}个)
      </h4>
      <div class="task-preview-list">
        <div 
          v-for="task in displayTasks" 
          :key="task.taskId"
          class="task-preview-item"
        >
          <div class="task-info">
            <span class="task-name">{{ task.name }}</span>
            <el-tag 
              :type="getPriorityTagType(task.priority)" 
              size="small" 
              class="priority-tag"
            >
              {{ getPriorityText(task.priority) }}
            </el-tag>
          </div>
          <div class="current-assignee">
            <span class="label">当前负责人:</span>
            <UserAvatar
              v-if="task.assigneeUserId"
              :user-id="task.assigneeUserId"
              :user-name="task.assigneeUserName"
              :avatar-url="getUserAvatar(task.assigneeUserId)"
              size="mini"
            />
            <span v-else class="no-assignee">未分配</span>
          </div>
        </div>
        
        <div v-if="selectedTasks.length > 5" class="more-tasks">
          还有 {{ selectedTasks.length - 5 }} 个任务...
        </div>
      </div>
    </div>

    <!-- 分配选项 -->
    <div class="assign-section">
      <h4 class="section-title">选择新的负责人</h4>
      
      <!-- 分配模式选择 -->
      <el-radio-group v-model="assignMode" class="assign-mode">
        <el-radio value="single">分配给单个用户</el-radio>
        <el-radio value="distribute">平均分配给多个用户</el-radio>
      </el-radio-group>

      <!-- 单个用户分配 -->
      <div v-if="assignMode === 'single'" class="single-assign">
        <UserSelect
          v-model="selectedUserId"
          placeholder="选择负责人"
          :filterable="true"
          class="user-select"
        />
        
        <!-- 用户信息预览 -->
        <div v-if="selectedUser" class="user-preview">
          <UserAvatar
            :user-id="selectedUser.id"
            :user-name="selectedUser.name"
            :avatar-url="getUserAvatar(selectedUser.id)"
            size="small"
          />
          <div class="user-info">
            <div class="user-name">{{ selectedUser.name }}</div>
            <div class="user-workload">
              当前任务: {{ selectedUser.currentTasks || 0 }}个
            </div>
          </div>
        </div>
      </div>

      <!-- 多用户分配 -->
      <div v-else class="multiple-assign">
        <UserSelect
          v-model="selectedUserIds"
          placeholder="选择多个负责人"
          :multiple="true"
          :filterable="true"
          class="user-select"
        />
        
        <!-- 分配预览 -->
        <div v-if="selectedUserIds.length > 0" class="distribution-preview">
          <h5>分配预览:</h5>
          <div class="distribution-list">
            <div 
              v-for="(userId, index) in selectedUserIds" 
              :key="userId"
              class="distribution-item"
            >
              <UserAvatar
                :user-id="userId"
                :user-name="getUserName(userId)"
                :avatar-url="getUserAvatar(userId)"
                size="small"
              />
              <div class="distribution-info">
                <div class="user-name">{{ getUserName(userId) }}</div>
                <div class="task-count">
                  将分配 {{ getTaskCountForUser(index) }} 个任务
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 高级选项 -->
    <div class="advanced-options">
      <el-collapse>
        <el-collapse-item title="高级选项" name="advanced">
          <div class="options-content">
            <!-- 是否通知 -->
            <el-checkbox v-model="notifyAssignees">
              通知新的负责人
            </el-checkbox>
            
            <!-- 是否保留原有参与者 -->
            <el-checkbox v-model="keepParticipants">
              保留原有参与者
            </el-checkbox>
            
            <!-- 分配原因 -->
            <div class="reason-section">
              <label class="reason-label">分配原因:</label>
              <el-input
                v-model="assignReason"
                type="textarea"
                :rows="2"
                placeholder="输入分配原因（可选）"
                maxlength="200"
                show-word-limit
              />
            </div>
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>

    <!-- 操作结果预览 -->
    <div v-if="showPreview" class="operation-preview">
      <h4 class="section-title">操作预览</h4>
      <div class="preview-content">
        <div class="preview-summary">
          <el-icon class="preview-icon"><User /></el-icon>
          <span>
            将 {{ selectedTasks.length }} 个任务
            {{ assignMode === 'single' ? '分配给' : '分配给' }}
            {{ assignMode === 'single' ? selectedUser?.name : selectedUserIds.length + '个用户' }}
          </span>
        </div>
        
        <div v-if="notifyAssignees" class="notification-info">
          <el-icon class="info-icon"><Bell /></el-icon>
          <span>将向新负责人发送通知</span>
        </div>
      </div>
    </div>

    <!-- 对话框底部 -->
    <template #footer>
      <div class="dialog-footer">
        <div class="footer-info">
          <span class="task-count-info">
            共 {{ selectedTasks.length }} 个任务
          </span>
        </div>
        <div class="footer-actions">
          <el-button @click="handleClose">取消</el-button>
          <el-button 
            type="primary" 
            @click="handleAssign"
            :disabled="!canAssign"
            :loading="assigning"
          >
            确认分配
          </el-button>
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import UserAvatar from '@/components/UserAvatar.vue'
import UserSelect from '@/components/UserSelect.vue'
import { useUserStore, getFullAvatarUrl } from '@/stores/modules/user'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  selectedTasks: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:modelValue', 'assign', 'close'])

// Store
const userStore = useUserStore()

// 响应式状态
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const assignMode = ref('single')
const selectedUserId = ref(null)
const selectedUserIds = ref([])
const notifyAssignees = ref(true)
const keepParticipants = ref(true)
const assignReason = ref('')
const assigning = ref(false)

// 用户数据
const users = ref([])
const selectedUser = ref(null)

// 计算属性
const displayTasks = computed(() => {
  return props.selectedTasks.slice(0, 5)
})

const canAssign = computed(() => {
  if (assignMode.value === 'single') {
    return selectedUserId.value !== null
  } else {
    return selectedUserIds.value.length > 0
  }
})

const showPreview = computed(() => {
  return canAssign.value
})

// 监听器
watch(() => selectedUserId.value, (newUserId) => {
  if (newUserId) {
    selectedUser.value = users.value.find(user => user.id === newUserId)
  } else {
    selectedUser.value = null
  }
})

watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    resetForm()
    loadUsers()
  }
})

// 生命周期
onMounted(() => {
  loadUsers()
})

// 方法
const loadUsers = async () => {
  try {
    users.value = await userStore.getUsers()
  } catch (error) {
    console.error('加载用户列表失败:', error)
    ElMessage.error('加载用户列表失败')
  }
}

const resetForm = () => {
  assignMode.value = 'single'
  selectedUserId.value = null
  selectedUserIds.value = []
  assignReason.value = ''
  notifyAssignees.value = true
  keepParticipants.value = true
  selectedUser.value = null
}

const handleAssign = async () => {
  if (!canAssign.value) {
    ElMessage.warning('请选择负责人')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要将 ${props.selectedTasks.length} 个任务${assignMode.value === 'single' ? '分配给' : '分配给'}${
        assignMode.value === 'single' ? selectedUser.value?.name : selectedUserIds.value.length + '个用户'
      }吗？`,
      '确认批量分配',
      {
        type: 'warning'
      }
    )

    assigning.value = true

    const assignData = {
      taskIds: props.selectedTasks.map(task => task.taskId),
      assignMode: assignMode.value,
      userIds: assignMode.value === 'single' ? [selectedUserId.value] : selectedUserIds.value,
      notifyAssignees: notifyAssignees.value,
      keepParticipants: keepParticipants.value,
      reason: assignReason.value
    }

    emit('assign', assignData)
    handleClose()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量分配失败: ' + error.message)
    }
  } finally {
    assigning.value = false
  }
}

const handleClose = () => {
  emit('close')
}

// 分配计算
const getTaskCountForUser = (userIndex) => {
  const taskCount = props.selectedTasks.length
  const userCount = selectedUserIds.value.length
  const baseCount = Math.floor(taskCount / userCount)
  const remainder = taskCount % userCount
  
  return userIndex < remainder ? baseCount + 1 : baseCount
}

// 用户信息获取
const getUserName = (userId) => {
  const user = users.value.find(u => u.id === userId)
  return user?.name || '未知用户'
}

const getUserAvatar = (userId) => {
  const user = users.value.find(u => u.id === userId)
  return getFullAvatarUrl(user?.avatar)
}

// 辅助方法
const getPriorityTagType = (priority) => {
  const types = {
    'High': 'danger',
    'Medium': 'warning',
    'Low': 'success',
    'Urgent': 'danger'
  }
  return types[priority] || ''
}

const getPriorityText = (priority) => {
  const texts = {
    'High': '高',
    'Medium': '中',
    'Low': '低',
    'Urgent': '紧急'
  }
  return texts[priority] || priority
}
</script>

<style scoped>
.batch-assign-dialog {
  :deep(.el-dialog) {
    border-radius: 12px;
  }
  
  :deep(.el-dialog__body) {
    max-height: 60vh;
    overflow-y: auto;
  }
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 12px 0;
}

.task-preview-section {
  margin-bottom: 24px;
}

.task-preview-list {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  padding: 8px;
}

.task-preview-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.task-preview-item:last-child {
  border-bottom: none;
}

.task-info {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.task-name {
  font-size: 13px;
  color: #303133;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.priority-tag {
  font-size: 10px;
}

.current-assignee {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
}

.current-assignee .label {
  color: #909399;
}

.no-assignee {
  color: #c0c4cc;
  font-style: italic;
}

.more-tasks {
  text-align: center;
  color: #909399;
  font-size: 12px;
  padding: 8px;
  font-style: italic;
}

.assign-section {
  margin-bottom: 24px;
}

.assign-mode {
  margin-bottom: 16px;
}

.assign-mode :deep(.el-radio) {
  margin-right: 24px;
}

.user-select {
  width: 100%;
  margin-bottom: 12px;
}

.user-preview {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #f5f7fa;
  border-radius: 6px;
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

.user-workload {
  font-size: 12px;
  color: #909399;
}

.distribution-preview {
  margin-top: 12px;
}

.distribution-preview h5 {
  font-size: 13px;
  color: #606266;
  margin: 0 0 8px 0;
}

.distribution-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.distribution-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px;
  background: #f5f7fa;
  border-radius: 6px;
}

.distribution-info {
  flex: 1;
}

.distribution-info .user-name {
  font-size: 13px;
  font-weight: 500;
  color: #303133;
}

.task-count {
  font-size: 12px;
  color: #909399;
}

.advanced-options {
  margin-bottom: 24px;
}

.options-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.reason-section {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.reason-label {
  font-size: 12px;
  color: #909399;
  font-weight: 500;
}

.operation-preview {
  background: #f0f9ff;
  border: 1px solid #bfdbfe;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 16px;
}

.preview-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.preview-summary {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #1e40af;
}

.preview-icon {
  color: #3b82f6;
}

.notification-info {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #059669;
}

.info-icon {
  color: #10b981;
}

.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.footer-info {
  display: flex;
  align-items: center;
}

.task-count-info {
  font-size: 12px;
  color: #909399;
}

.footer-actions {
  display: flex;
  gap: 8px;
}

/* 滚动条样式 */
.task-preview-list::-webkit-scrollbar {
  width: 6px;
}

.task-preview-list::-webkit-scrollbar-thumb {
  background: #c0c4cc;
  border-radius: 3px;
}

.task-preview-list::-webkit-scrollbar-thumb:hover {
  background: #a8abb2;
}
</style>