import{b as e}from"./format-DfhXadVZ.js";import{_ as t,b as a,$ as i,e as l,a as s,o,d as n,l as r,af as d,w as m,m as p,k as c,aw as u,F as g,h,p as y,t as f}from"./index-CG5lHOPO.js";import{p as b,d as w}from"./personnel-C5IStqeW.js";const C={name:"Pagination",props:{total:{required:!0,type:Number},page:{type:Number,default:1},limit:{type:Number,default:20},pageCount:{type:Number,default:7},background:{type:Boolean,default:!0},layout:{type:String,default:"total, sizes, prev, pager, next, jumper"},pageSizes:{type:Array,default:()=>[10,20,30,50,100]},autoScroll:{type:Boolean,default:!0}},computed:{currentPage:{get(){return this.page},set(e){this.$emit("update:page",e)}},pageSize:{get(){return this.limit},set(e){this.$emit("update:limit",e)}}},methods:{handleSizeChange(e){this.$emit("pagination",{page:this.currentPage,limit:e})},handleCurrentChange(e){this.$emit("pagination",{page:e,limit:this.pageSize}),this.autoScroll&&this.scrollToTop()},scrollToTop(){const e=document.querySelector(".app-main");e&&(e.scrollTop=0)}}},k={key:0,class:"pagination-container"};const V={class:"app-container"},_={class:"filter-container"},v={key:0},$={key:0},I={key:0},L={key:0},F={key:0},T={key:0},z={key:0},D={key:0},S={class:"dialog-footer"};const x=t({name:"PersonnelManagement",components:{Pagination:t(C,[["render",function(e,t,n,r,d,m){const p=s("el-pagination");return n.total>0?(o(),a("div",k,[l(p,{background:n.background,"current-page":m.currentPage,"page-size":m.pageSize,layout:n.layout,"page-sizes":n.pageSizes,total:n.total,onSizeChange:m.handleSizeChange,onCurrentChange:m.handleCurrentChange},null,8,["background","current-page","page-size","layout","page-sizes","total","onSizeChange","onCurrentChange"])])):i("",!0)}],["__scopeId","data-v-f5889c8b"]])},data:()=>({list:[],total:0,listLoading:!1,listQuery:{page:1,limit:20,keyword:"",departmentId:null},departmentOptions:[],dialogFormVisible:!1,dialogTitle:"",formLoading:!1,temp:{id:void 0,name:"",position:"",contact:"",departmentId:null,departmentName:"",employeeCode:""},rules:{name:[{required:!0,message:"姓名不能为空",trigger:"blur"}],employeeCode:[{required:!0,message:"工号不能为空",trigger:"blur"}],contact:[{required:!0,message:"联系方式不能为空",trigger:"blur"}],departmentId:[{required:!0,message:"请选择所属部门",trigger:"change"}]}}),created(){this.initData()},methods:{formatDateTime:e,async initData(){try{await this.getDepartments(),await this.getList()}catch(e){}},async getList(){var e;this.listLoading=!0;try{const t={keyword:this.listQuery.keyword,departmentId:this.listQuery.departmentId},a=await b.getPersonnelList(t);if(a.data&&a.data.success){const e=Array.isArray(a.data.data)?a.data.data:[];this.list=e.map((e=>{if(e.departmentId&&!e.departmentName){const t=this.departmentOptions.find((t=>t.id===e.departmentId));e.departmentName=t?t.name:"未知部门"}return e})),this.total=this.list.length}else this.$message.error("获取人员列表失败: "+((null==(e=a.data)?void 0:e.message)||"未知错误")),this.list=[],this.total=0}catch(t){this.$message.error("获取人员列表失败"),this.list=[],this.total=0}finally{this.listLoading=!1}},async getDepartments(){try{const e=await w.getDepartmentList();e.data&&e.data.success&&(this.departmentOptions=e.data.data)}catch(e){this.$message.error("获取部门列表失败")}},handleFilter(){this.listQuery.page=1,this.getList()},resetTemp(){this.temp={id:void 0,name:"",position:"",contact:"",departmentId:null,departmentName:"",employeeCode:""}},handleCreate(){this.resetTemp(),this.dialogTitle="新增人员",this.dialogFormVisible=!0,this.$nextTick((()=>{const e=this.$refs.dataForm;e&&e.clearValidate()}))},handleUpdate(e){this.temp=JSON.parse(JSON.stringify(e)),this.dialogTitle="编辑人员",this.dialogFormVisible=!0,this.$nextTick((()=>{const e=this.$refs.dataForm;e&&e.clearValidate()}))},submitForm(){const e=this.$refs.dataForm;e?e.validate((e=>{if(!e)return!1;"新增人员"===this.dialogTitle?this.createData():this.updateData()})):this.$message.error("表单引用不存在，请稍后重试")},async handleDelete(e){var t;try{await this.$confirm("确定要删除该人员吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const a=await b.deletePersonnel(e.id);a.data&&a.data.success?(this.$message({type:"success",message:"删除成功!"}),this.getList()):this.$message.error((null==(t=a.data)?void 0:t.message)||"删除失败")}catch(a){"cancel"!==a&&this.$message.error("删除人员失败: "+(a.message||"未知错误"))}},hasRelations:e=>!1,async createData(){var e;if(!this.formLoading){this.formLoading=!0;try{const t={name:this.temp.name,employeeCode:this.temp.employeeCode,position:this.temp.position||"",contact:this.temp.contact||"",departmentId:this.temp.departmentId},a=await b.createPersonnel(t);if(a.data&&a.data.success)this.dialogFormVisible=!1,this.$message({type:"success",message:"创建成功!"}),this.getList();else{const t=(null==(e=a.data)?void 0:e.message)||"创建失败";this.$message.error(t)}}catch(t){this.$message.error("创建人员失败: "+(t.message||"服务器错误"))}finally{this.formLoading=!1}}},async updateData(){var e;if(!this.formLoading){this.formLoading=!0;try{const t={name:this.temp.name,employeeCode:this.temp.employeeCode,position:this.temp.position||"",contact:this.temp.contact||"",departmentId:this.temp.departmentId},a=await b.updatePersonnel(this.temp.id,t);if(a.data&&a.data.success)this.dialogFormVisible=!1,this.$message({type:"success",message:"更新成功!"}),this.getList();else{const t=(null==(e=a.data)?void 0:e.message)||"更新失败";this.$message.error(t)}}catch(t){this.$message.error("更新人员失败: "+(t.message||"服务器错误"))}finally{this.formLoading=!1}}},handleDepartmentChange(e){if(this.temp.departmentId=e,e){const t=this.departmentOptions.find((t=>t.id===e));t&&(this.temp.departmentName=t.name)}else this.temp.departmentName=""}}},[["render",function(e,t,b,w,C,k){const x=s("el-input"),N=s("el-option"),P=s("el-select"),Q=s("el-button"),U=s("el-table-column"),O=s("el-table"),q=s("pagination"),j=s("el-form-item"),A=s("el-form"),B=s("el-dialog"),J=p("loading");return o(),a("div",V,[n("div",_,[l(x,{modelValue:C.listQuery.keyword,"onUpdate:modelValue":t[0]||(t[0]=e=>C.listQuery.keyword=e),placeholder:"姓名/工号/联系方式",style:{width:"200px"},class:"filter-item",onKeyup:d(k.handleFilter,["enter","native"])},null,8,["modelValue","onKeyup"]),l(P,{modelValue:C.listQuery.departmentId,"onUpdate:modelValue":t[1]||(t[1]=e=>C.listQuery.departmentId=e),placeholder:"所属部门",clearable:"",style:{width:"200px"},class:"filter-item"},{default:m((()=>[(o(!0),a(g,null,h(C.departmentOptions,(e=>(o(),c(N,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"]),l(Q,{class:"filter-item",type:"primary",icon:"el-icon-search",onClick:k.handleFilter},{default:m((()=>t[9]||(t[9]=[y(" 搜索 ")]))),_:1},8,["onClick"]),l(Q,{class:"filter-item",style:{"margin-left":"10px"},type:"primary",icon:"el-icon-plus",onClick:k.handleCreate},{default:m((()=>t[10]||(t[10]=[y(" 新增人员 ")]))),_:1},8,["onClick"])]),r((o(),c(O,{data:C.list,"element-loading-text":"加载中...",border:"",fit:"","highlight-current-row":"",style:{width:"100%"}},{default:m((()=>[l(U,{label:"ID",align:"center",width:"80"},{default:m((e=>[e&&e.row?(o(),a("span",v,f(e.row.id),1)):i("",!0)])),_:1}),l(U,{label:"工号",align:"center",width:"120"},{default:m((e=>[e&&e.row?(o(),a("span",$,f(e.row.employeeCode),1)):i("",!0)])),_:1}),l(U,{label:"姓名",align:"center",width:"120"},{default:m((e=>[e&&e.row?(o(),a("span",I,f(e.row.name),1)):i("",!0)])),_:1}),l(U,{label:"职位",align:"center",width:"150"},{default:m((e=>[e&&e.row?(o(),a("span",L,f(e.row.position),1)):i("",!0)])),_:1}),l(U,{label:"联系方式",align:"center",width:"150"},{default:m((e=>[e&&e.row?(o(),a("span",F,f(e.row.contact),1)):i("",!0)])),_:1}),l(U,{label:"所属部门",align:"center"},{default:m((e=>[e&&e.row?(o(),a("span",T,f(e.row.departmentName),1)):i("",!0)])),_:1}),l(U,{label:"创建时间",align:"center",width:"180"},{default:m((e=>[e&&e.row?(o(),a("span",z,f(k.formatDateTime(e.row.createdAt)),1)):i("",!0)])),_:1}),l(U,{label:"操作",align:"center",width:"230","class-name":"small-padding fixed-width"},{default:m((e=>[e&&e.row?(o(),a("div",D,[l(Q,{size:"mini",type:"primary",onClick:t=>k.handleUpdate(e.row)},{default:m((()=>t[11]||(t[11]=[y(" 编辑 ")]))),_:2},1032,["onClick"]),k.hasRelations(e.row)?i("",!0):(o(),c(Q,{key:0,size:"mini",type:"danger",onClick:t=>k.handleDelete(e.row)},{default:m((()=>t[12]||(t[12]=[y(" 删除 ")]))),_:2},1032,["onClick"]))])):i("",!0)])),_:1})])),_:1},8,["data"])),[[J,C.listLoading]]),r(l(q,{total:C.total,page:C.listQuery.page,limit:C.listQuery.limit,onPagination:k.getList},null,8,["total","page","limit","onPagination"]),[[u,C.total>0]]),l(B,{title:C.dialogTitle,modelValue:C.dialogFormVisible,"onUpdate:modelValue":t[8]||(t[8]=e=>C.dialogFormVisible=e),width:"500px"},{footer:m((()=>[n("div",S,[l(Q,{onClick:t[7]||(t[7]=e=>C.dialogFormVisible=!1)},{default:m((()=>t[13]||(t[13]=[y(" 取消 ")]))),_:1}),l(Q,{type:"primary",onClick:k.submitForm,loading:C.formLoading},{default:m((()=>t[14]||(t[14]=[y(" 确定 ")]))),_:1},8,["onClick","loading"])])])),default:m((()=>[l(A,{ref:"dataForm",rules:C.rules,model:C.temp,"label-position":"left","label-width":"100px"},{default:m((()=>[l(j,{label:"姓名",prop:"name"},{default:m((()=>[l(x,{modelValue:C.temp.name,"onUpdate:modelValue":t[2]||(t[2]=e=>C.temp.name=e),placeholder:"请输入姓名"},null,8,["modelValue"])])),_:1}),l(j,{label:"工号",prop:"employeeCode"},{default:m((()=>[l(x,{modelValue:C.temp.employeeCode,"onUpdate:modelValue":t[3]||(t[3]=e=>C.temp.employeeCode=e),placeholder:"请输入工号"},null,8,["modelValue"])])),_:1}),l(j,{label:"职位",prop:"position"},{default:m((()=>[l(x,{modelValue:C.temp.position,"onUpdate:modelValue":t[4]||(t[4]=e=>C.temp.position=e),placeholder:"请输入职位"},null,8,["modelValue"])])),_:1}),l(j,{label:"联系方式",prop:"contact"},{default:m((()=>[l(x,{modelValue:C.temp.contact,"onUpdate:modelValue":t[5]||(t[5]=e=>C.temp.contact=e),placeholder:"请输入联系方式"},null,8,["modelValue"])])),_:1}),l(j,{label:"所属部门",prop:"departmentId"},{default:m((()=>[l(P,{modelValue:C.temp.departmentId,"onUpdate:modelValue":t[6]||(t[6]=e=>C.temp.departmentId=e),placeholder:"请选择部门",clearable:"",style:{width:"100%"},onChange:k.handleDepartmentChange},{default:m((()=>[(o(!0),a(g,null,h(C.departmentOptions,(e=>(o(),c(N,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue","onChange"])])),_:1})])),_:1},8,["rules","model"])])),_:1},8,["title","modelValue"])])}],["__scopeId","data-v-7f2ee739"]]);export{x as default};
