// File: Application/Features/SpareParts/Services/ISparePartService.cs
// Description: 备品备件服务接口

using System.Collections.Generic;
using System.Threading.Tasks;
using ItAssetsSystem.Application.Common.Dtos;
using ItAssetsSystem.Application.Features.SpareParts.Dtos;

namespace ItAssetsSystem.Application.Features.SpareParts.Services
{
    /// <summary>
    /// 备品备件服务接口
    /// </summary>
    public interface ISparePartService
    {
        /// <summary>
        /// 获取备品备件列表（分页）
        /// </summary>
        /// <param name="query">查询参数</param>
        /// <returns>分页结果</returns>
        Task<PaginatedResult<SparePartDto>> GetSparePartsAsync(SparePartQuery query);
        
        /// <summary>
        /// 获取单个备品备件详情
        /// </summary>
        /// <param name="id">备件ID</param>
        /// <returns>备件DTO</returns>
        Task<SparePartDto> GetSparePartByIdAsync(long id);
        
        /// <summary>
        /// 创建备品备件
        /// </summary>
        /// <param name="request">创建请求</param>
        /// <returns>创建的备件DTO</returns>
        Task<SparePartDto> CreateSparePartAsync(CreateSparePartRequest request);
        
        /// <summary>
        /// 更新备品备件
        /// </summary>
        /// <param name="id">备件ID</param>
        /// <param name="request">更新请求</param>
        /// <returns>更新后的备件DTO</returns>
        Task<SparePartDto> UpdateSparePartAsync(long id, CreateSparePartRequest request);
        
        /// <summary>
        /// 删除备品备件
        /// </summary>
        /// <param name="id">备件ID</param>
        /// <returns>是否成功</returns>
        Task<bool> DeleteSparePartAsync(long id);
        
        /// <summary>
        /// 获取库存预警列表
        /// </summary>
        /// <param name="onlyLowStock">是否只获取低于最小安全库存的备件</param>
        /// <returns>预警备件列表</returns>
        Task<List<SparePartDto>> GetLowStockSparePartsAsync(bool onlyLowStock = false);
        
        /// <summary>
        /// 备件入库
        /// </summary>
        /// <param name="request">入库请求</param>
        /// <returns>入库记录DTO</returns>
        Task<SparePartTransactionDto> SparePartInboundAsync(SparePartInboundRequest request);
        
        /// <summary>
        /// 备件出库
        /// </summary>
        /// <param name="request">出库请求</param>
        /// <returns>出库记录DTO</returns>
        Task<SparePartTransactionDto> SparePartOutboundAsync(SparePartOutboundRequest request);

        /// <summary>
        /// 库存调整
        /// </summary>
        /// <param name="request">库存调整请求</param>
        /// <returns>调整记录DTO</returns>
        Task<SparePartTransactionDto> StockAdjustmentAsync(StockAdjustmentRequest request);
    }
} 