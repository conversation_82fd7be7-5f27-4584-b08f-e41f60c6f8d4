<template>
  <div 
    :style="{
      left: `${location.x}%`,
      top: `${location.y}%`,
      width: `${location.width}%`,
      height: `${location.height}%`,
    }"
    :class="[
      'absolute rounded-lg shadow-lg transition-all duration-300 ease-out cursor-pointer flex flex-col items-center justify-center p-2 overflow-hidden border',
      statusClasses,
      hoverClasses,
      selectedClasses,
      highlightedClasses
    ]"
    @click="handleClick"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
  >
    <!-- 位置信息 -->
    <div class="flex items-center justify-between w-full mb-1">
      <span class="font-bold text-white text-sm truncate">{{ location.locationName }}</span>
      <component :is="statusIcon" class="flex-shrink-0 ml-1" />
    </div>
    
    <!-- 部门信息 -->
    <div class="text-xs text-white/80 text-center truncate w-full">
      {{ location.effectiveDepartmentName || '未分配部门' }}
    </div>
    
    <!-- 资产数量 -->
    <div class="text-xs text-white/60 mt-1">
      {{ location.assetCount }} 台设备
    </div>
    
    <!-- 状态指示器 -->
    <div class="absolute top-1 right-1">
      <div :class="[
        'w-2 h-2 rounded-full',
        statusDotClasses
      ]"></div>
    </div>
    
    <!-- 高亮边框动画 -->
    <div v-if="location.isHighlighted" class="absolute inset-0 border-2 border-dashed border-cyan-400 rounded-lg animate-pulse pointer-events-none"></div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { 
  Check, 
  Warning, 
  Close, 
  Setting,
  CircleCheck,
  WarningFilled,
  CircleClose
} from '@element-plus/icons-vue'

const props = defineProps({
  location: {
    type: Object,
    required: true
  },
  isSelected: {
    type: Boolean,
    default: false
  },
  isHovered: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['click', 'mouseenter', 'mouseleave'])

// 状态配置
const statusConfig = {
  operational: {
    bgClass: 'bg-green-500/70 border-green-500',
    icon: CircleCheck,
    iconClass: 'text-green-400',
    dotClass: 'bg-green-400',
    animation: ''
  },
  warning: {
    bgClass: 'bg-yellow-500/70 border-yellow-500',
    icon: WarningFilled,
    iconClass: 'text-yellow-400',
    dotClass: 'bg-yellow-400',
    animation: 'animate-pulse'
  },
  error: {
    bgClass: 'bg-red-500/70 border-red-500',
    icon: CircleClose,
    iconClass: 'text-red-400',
    dotClass: 'bg-red-400',
    animation: 'animate-pulse'
  },
  idle: {
    bgClass: 'bg-blue-500/70 border-blue-500',
    icon: Setting,
    iconClass: 'text-blue-400',
    dotClass: 'bg-blue-400',
    animation: ''
  }
}

// 计算样式类
const statusClasses = computed(() => {
  const config = statusConfig[props.location.status] || statusConfig.idle
  return `${config.bgClass} ${config.animation}`
})

const statusIcon = computed(() => {
  const config = statusConfig[props.location.status] || statusConfig.idle
  return config.icon
})

const statusIconClass = computed(() => {
  const config = statusConfig[props.location.status] || statusConfig.idle
  return config.iconClass
})

const statusDotClasses = computed(() => {
  const config = statusConfig[props.location.status] || statusConfig.idle
  return config.dotClass
})

const hoverClasses = computed(() => 
  props.isHovered ? 'ring-2 ring-cyan-400 ring-offset-2 ring-offset-gray-800 scale-105 z-10' : 'z-0'
)

const selectedClasses = computed(() => 
  props.isSelected ? 'ring-4 ring-sky-500 ring-offset-2 ring-offset-gray-900 z-20' : ''
)

const highlightedClasses = computed(() => 
  props.location.isHighlighted ? 'border-2 border-dashed border-teal-400' : ''
)

// 事件处理
const handleClick = () => {
  emit('click', props.location.locationId)
}

const handleMouseEnter = () => {
  emit('mouseenter', props.location.locationId)
}

const handleMouseLeave = () => {
  emit('mouseleave')
}
</script>

<style scoped>
/* 自定义动画 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* 悬停效果 */
.cursor-pointer:hover {
  transform: translateZ(0);
}

/* 确保文本不会溢出 */
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 响应式字体大小 */
@media (max-width: 768px) {
  .text-sm {
    font-size: 0.75rem;
  }
  .text-xs {
    font-size: 0.625rem;
  }
}
</style>