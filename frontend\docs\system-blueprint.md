# 航空航天级IT资产管理系统 - 后端实现蓝图

版本: 1.0
最后更新: 2025-03-22

## 目录
1. [系统架构概览](#1-系统架构概览)
2. [核心设计原则](#2-核心设计原则)
3. [技术栈选择](#3-技术栈选择)
4. [系统架构图](#4-系统架构图)
5. [目录结构](#5-目录结构)
6. [核心文件实现指南](#6-核心文件实现指南)
7. [完整文件清单](#7-完整文件清单)
8. [数据模型设计](#8-数据模型设计)
9. [API接口设计](#9-api接口设计)
10. [实现注意事项](#10-实现注意事项)

## 1. 系统架构概览

航空航天级IT资产管理系统采用微内核+插件架构，通过高度模块化和松耦合的设计，实现系统的高可靠性、可扩展性和可维护性。系统包含以下核心部分：

- **核心引擎(Core Engine)**: 提供基础功能和插件加载机制，不超过500行代码
- **插件系统(Plugin System)**: 负责管理各业务插件的加载、卸载和生命周期
- **业务模块(Business Modules)**: 各业务功能作为独立插件实现，每个插件不超过200行
- **事件机制(Event System)**: 通过事件发布-订阅模式实现模块间通信和解耦
- **数据访问层(Data Access)**: 使用EF Core和Dapper优化数据库访问
- **API层(API Layer)**: 采用REPR模式实现API接口，每个端点一个文件

## 2. 核心设计原则

### 2.1 极简高效设计
- 整系统不超过5,000行代码
- 核心引擎约500行
- 各插件不超过200行/个
- 每个函数不超过25行

### 2.2 微内核+插件架构
- 核心引擎仅提供基础功能
- 业务功能通过独立插件实现
- 插件间通过事件机制松耦合

### 2.3 REPR模式API
- 每个端点一个文件
- 清晰可维护的API结构

### 2.4 业务特性实现要求
- 简化采购流程：采购只有"一键创建"操作，无需复杂审批流程
- 位置层级配置：位置结构通过JSON配置文件定义基础模板
- 任务管理与资产管理解耦：任务管理作为独立插件实现
- 事件溯源机制：完整记录资产生命周期，支持任意历史点重建
- 韧性设计：断网操作队列、优雅降级、冗余容错
- JSON驱动UI：通过配置生成复杂界面

## 3. 技术栈选择

### 3.1 后端技术
- ASP.NET Core 6.0
- Entity Framework Core + Dapper
- JWT身份验证
- Serilog日志记录
- Swagger API文档

### 3.2 数据库
- MySQL 8.0

### 3.3 开发工具
- Visual Studio 2022 / VS Code
- Git 版本控制
- NUnit/xUnit 测试框架

## 4. 系统架构图

```
+-----------------------------------------------------------+
|                     前端应用 (可选)                         |
+-----------------------------------------------------------+
                           |
                           | HTTP/HTTPS
                           v
+-----------------------------------------------------------+
|                         API 层                             |
|                                                           |
|  +-------------------+  +-------------------+             |
|  |   控制器 Controllers |  |   REPR 端点       |             |
|  +-------------------+  +-------------------+             |
|                     |        |                           |
|  +-------------------+  +--------------------+           |
|  |   中间件 Middlewares |  |    过滤器 Filters  |           |
|  +-------------------+  +--------------------+           |
+-----------------------------------------------------------+
                           |
                           v
+-----------------------------------------------------------+
|                     核心引擎 (Core Engine)                  |
|                                                           |
|  +-------------------+  +-------------------+             |
|  |   引擎核心 Engine   |  |  插件管理 PluginMgr |             |
|  +-------------------+  +-------------------+             |
|                                                           |
|  +-------------------+  +-------------------+             |
|  |   服务 Services    |  |  韧性管理 Resilience|             |
|  +-------------------+  +-------------------+             |
+-----------------------------------------------------------+
                           |
                           v
+-----------------------------------------------------------+
|                       业务插件 (Plugins)                    |
|                                                           |
|  +-------------------+  +-------------------+             |
|  | 资产管理 AssetMgmt  |  | 位置管理 LocationMgmt|           |
|  +-------------------+  +-------------------+             |
|                                                           |
|  +-------------------+  +-------------------+             |
|  | 采购管理 PurchaseMgmt|  | 故障管理 FaultMgmt |            |
|  +-------------------+  +-------------------+             |
|                                                           |
|  +-------------------+  +-------------------+             |
|  | 任务管理 TaskMgmt   |  | 用户管理 UserMgmt  |             |
|  +-------------------+  +-------------------+             |
+-----------------------------------------------------------+
                           |
                           v
+-----------------------------------------------------------+
|                       基础设施 (Infrastructure)             |
|                                                           |
|  +-------------------+  +-------------------+             |
|  |    数据访问 Data    |  |    事件 Events     |             |
|  +-------------------+  +-------------------+             |
|                                                           |
|  +-------------------+  +-------------------+             |
|  |    安全 Security   |  |    缓存 Caching    |             |
|  +-------------------+  +-------------------+             |
+-----------------------------------------------------------+
                           |
                           v
+-----------------------------------------------------------+
|                         数据存储                            |
|  +-------------------+  +-------------------+             |
|  |      MySQL        |  |   File System     |             |
|  +-------------------+  +-------------------+             |
+-----------------------------------------------------------+
```

## 5. 目录结构

```
AerospaceITAM/
├── src/
│   ├── AerospaceITAM.API/              # API层
│   │   ├── Controllers/                # 控制器
│   │   ├── Endpoints/                  # REPR模式API端点
│   │   ├── Filters/                    # API过滤器
│   │   ├── Middlewares/                # 中间件
│   │   ├── Program.cs                  # 应用入口
│   │   ├── appsettings.json            # 应用配置
│   │   └── AerospaceITAM.API.csproj    # 项目文件
│   │
│   ├── AerospaceITAM.Core/             # 核心引擎
│   │   ├── Engine/                     # 核心引擎实现
│   │   ├── Interfaces/                 # 接口定义
│   │   ├── Services/                   # 核心服务
│   │   └── AerospaceITAM.Core.csproj   # 项目文件
│   │
│   ├── AerospaceITAM.Plugins/          # 插件管理
│   │   ├── PluginLoader.cs             # 插件加载器
│   │   ├── PluginManager.cs            # 插件管理器
│   │   ├── IPlugin.cs                  # 插件接口
│   │   └── AerospaceITAM.Plugins.csproj# 项目文件
│   │
│   ├── AerospaceITAM.Modules/          # 业务模块
│   │   ├── AssetManagement/            # 资产管理模块
│   │   ├── LocationManagement/         # 位置管理模块
│   │   ├── PurchaseManagement/         # 采购管理模块
│   │   ├── FaultManagement/            # 故障管理模块
│   │   ├── TaskManagement/             # 任务管理模块
│   │   ├── UserManagement/             # 用户管理模块
│   │   └── AerospaceITAM.Modules.csproj# 项目文件
│   │
│   ├── AerospaceITAM.Infrastructure/   # 基础设施
│   │   ├── Data/                       # 数据访问
│   │   │   ├── Context/                # 数据上下文
│   │   │   ├── Repositories/           # 仓储实现
│   │   │   ├── Migrations/             # 数据迁移
│   │   ├── Events/                     # 事件处理
│   │   ├── Security/                   # 安全相关
│   │   ├── Caching/                    # 缓存处理
│   │   └── AerospaceITAM.Infrastructure.csproj # 项目文件
│   │
│   ├── AerospaceITAM.Shared/           # 共享库
│   │   ├── Models/                     # 模型定义
│   │   │   ├── Entities/               # 实体类
│   │   │   ├── Dtos/                   # 数据传输对象
│   │   │   ├── ViewModels/             # 视图模型
│   │   ├── Constants/                  # 常量定义
│   │   ├── Enums/                      # 枚举定义
│   │   ├── Extensions/                 # 扩展方法
│   │   ├── Utilities/                  # 工具类
│   │   └── AerospaceITAM.Shared.csproj # 项目文件
│   │
│   ├── AerospaceITAM.Tests/            # 测试项目
│       ├── UnitTests/                  # 单元测试
│       ├── IntegrationTests/           # 集成测试
│       └── AerospaceITAM.Tests.csproj  # 项目文件
│
├── configs/                            # 配置文件目录
│   ├── LocationHierarchy.json          # 位置层级配置
│   ├── MenuDefinitions.json            # 菜单定义配置
│   └── PermissionSettings.json         # 权限设置配置
│
├── scripts/                            # 脚本文件目录
│   ├── db-init.sql                     # 数据库初始化脚本
│   └── deploy.sh                       # 部署脚本
│
├── docs/                               # 文档目录
│   ├── api/                            # API文档
│   └── architecture/                   # 架构文档
│
├── AerospaceITAM.sln                   # 解决方案文件
└── README.md                           # 项目说明文件
```

## 6. 核心文件实现指南

### 6.1 Program.cs
- **文件路径**: src/AerospaceITAM.API/Program.cs
- **功能简介**: 应用程序入口点，配置服务、中间件和应用启动
- **代码行数约束**: 不超过 50 行
- **依赖关系**: 依赖各种服务注册扩展方法
- **实现要点**:
  - 配置服务注册
  - 配置中间件管道
  - 加载配置文件
  - 初始化插件系统
  - 配置Serilog日志
  - 启用Swagger文档

### 6.2 CoreEngine.cs
- **文件路径**: src/AerospaceITAM.Core/Engine/CoreEngine.cs
- **功能简介**: 系统核心引擎，提供基础功能和插件管理
- **代码行数约束**: 不超过 200 行
- **依赖关系**: 依赖插件接口和事件管理器
- **实现要点**:
  - 初始化核心服务
  - 加载并管理插件
  - 处理核心事件
  - 提供系统基础能力

### 6.3 IPlugin.cs
- **文件路径**: src/AerospaceITAM.Plugins/IPlugin.cs
- **功能简介**: 定义插件接口，所有插件必须实现此接口
- **代码行数约束**: 不超过 20 行
- **依赖关系**: 无
- **实现要点**:
  - 定义插件生命周期方法
  - 定义插件元数据
  - 定义插件事件订阅机制

### 6.4 PluginManager.cs
- **文件路径**: src/AerospaceITAM.Plugins/PluginManager.cs
- **功能简介**: 管理插件的加载、卸载和生命周期
- **代码行数约束**: 不超过 150 行
- **依赖关系**: 依赖IPlugin接口和事件管理器
- **实现要点**:
  - 动态加载插件
  - 管理插件依赖
  - 处理插件事件
  - 插件版本控制

### 6.5 EventManager.cs
- **文件路径**: src/AerospaceITAM.Infrastructure/Events/EventManager.cs
- **功能简介**: 事件管理器，处理系统内事件的发布和订阅
- **代码行数约束**: 不超过 100 行
- **依赖关系**: 无
- **实现要点**:
  - 事件注册和订阅
  - 异步事件处理
  - 事件优先级
  - 事件过滤

### 6.6 AppDbContext.cs
- **文件路径**: src/AerospaceITAM.Infrastructure/Data/Context/AppDbContext.cs
- **功能简介**: 数据库上下文，定义实体关系和数据库映射
- **代码行数约束**: 不超过 200 行
- **依赖关系**: 依赖实体模型
- **实现要点**:
  - 实体配置
  - 关系映射
  - 查询优化
  - 审计记录

### 6.7 AssetManagementPlugin.cs
- **文件路径**: src/AerospaceITAM.Modules/AssetManagement/AssetManagementPlugin.cs
- **功能简介**: 资产管理插件，实现资产相关功能
- **代码行数约束**: 不超过 200 行
- **依赖关系**: 依赖核心引擎和数据库上下文
- **实现要点**:
  - 资产创建和管理
  - 资产状态变更
  - 资产历史记录
  - 事件处理

### 6.8 LocationManagementPlugin.cs
- **文件路径**: src/AerospaceITAM.Modules/LocationManagement/LocationManagementPlugin.cs
- **功能简介**: 位置管理插件，实现位置层级和关联功能
- **代码行数约束**: 不超过 200 行
- **依赖关系**: 依赖核心引擎和数据库上下文
- **实现要点**:
  - 位置层级加载
  - 位置关联管理
  - JSON配置解析
  - 位置变更记录

### 6.9 PurchaseManagementPlugin.cs
- **文件路径**: src/AerospaceITAM.Modules/PurchaseManagement/PurchaseManagementPlugin.cs
- **功能简介**: 采购管理插件，实现简化的采购流程
- **代码行数约束**: 不超过 200 行
- **依赖关系**: 依赖核心引擎和数据库上下文
- **实现要点**:
  - 一键创建采购
  - 状态管理（只有已到货和未到货两种状态）
  - 自动完成闭环
  - 采购-资产转换

### 6.10 TaskManagementPlugin.cs
- **文件路径**: src/AerospaceITAM.Modules/TaskManagement/TaskManagementPlugin.cs
- **功能简介**: 任务管理插件，实现任务相关功能
- **代码行数约束**: 不超过 200 行
- **依赖关系**: 依赖核心引擎和数据库上下文
- **实现要点**:
  - 任务创建和管理
  - 周期任务处理
  - PDCA计划执行
  - 任务提醒
  - 与资产管理模块保持松耦合关系

### 6.11 AssetController.cs
- **文件路径**: src/AerospaceITAM.API/Controllers/AssetController.cs
- **功能简介**: 资产管理API控制器
- **代码行数约束**: 不超过 150 行
- **依赖关系**: 依赖资产管理服务
- **实现要点**:
  - 资产CRUD操作
  - 资产查询和过滤
  - 异常处理
  - 响应格式化

### 6.12 GetAssetById.cs (REPR模式示例)
- **文件路径**: src/AerospaceITAM.API/Endpoints/Asset/GetAssetById.cs
- **功能简介**: 根据ID获取资产信息的API端点
- **代码行数约束**: 不超过 50 行
- **依赖关系**: 依赖资产服务
- **实现要点**:
  - 请求验证
  - 资产查询
  - 响应格式化
  - 异常处理

### 6.13 LocationHierarchyLoader.cs
- **文件路径**: src/AerospaceITAM.Core/Services/LocationHierarchyLoader.cs
- **功能简介**: 位置层级配置加载器
- **代码行数约束**: 不超过 100 行
- **依赖关系**: 依赖位置层级配置模型
- **实现要点**:
  - 配置文件读取
  - JSON解析
  - 位置层级构建
  - 缓存机制

### 6.14 EventSourcingManager.cs
- **文件路径**: src/AerospaceITAM.Infrastructure/Events/EventSourcingManager.cs
- **功能简介**: 事件溯源管理器
- **代码行数约束**: 不超过 150 行
- **依赖关系**: 依赖事件仓储
- **实现要点**:
  - 事件记录
  - 状态重建
  - 历史查询
  - 版本控制

### 6.15 ResilienceManager.cs
- **文件路径**: src/AerospaceITAM.Core/Engine/ResilienceManager.cs
- **功能简介**: 韧性管理器，处理断网操作队列和容错机制
- **代码行数约束**: 不超过 150 行
- **依赖关系**: 依赖事件管理器
- **实现要点**:
  - 操作队列
  - 重试机制
  - 降级策略
  - 冗余处理

## 7. 完整文件清单

### 7.1 基础配置与启动文件
1. src/AerospaceITAM.API/Program.cs
2. src/AerospaceITAM.API/appsettings.json
3. src/AerospaceITAM.API/appsettings.Development.json
4. src/AerospaceITAM.API/AerospaceITAM.API.csproj
5. src/AerospaceITAM.Core/AerospaceITAM.Core.csproj
6. src/AerospaceITAM.Plugins/AerospaceITAM.Plugins.csproj
7. src/AerospaceITAM.Modules/AerospaceITAM.Modules.csproj
8. src/AerospaceITAM.Infrastructure/AerospaceITAM.Infrastructure.csproj
9. src/AerospaceITAM.Shared/AerospaceITAM.Shared.csproj
10. src/AerospaceITAM.Tests/AerospaceITAM.Tests.csproj
11. AerospaceITAM.sln

### 7.2 核心引擎文件
12. src/AerospaceITAM.Core/Engine/CoreEngine.cs
13. src/AerospaceITAM.Core/Engine/ResilienceManager.cs
14. src/AerospaceITAM.Core/Engine/ConfigurationManager.cs
15. src/AerospaceITAM.Core/Interfaces/IEngine.cs
16. src/AerospaceITAM.Core/Services/AssetService.cs
17. src/AerospaceITAM.Core/Services/LocationService.cs
18. src/AerospaceITAM.Core/Services/PurchaseService.cs
19. src/AerospaceITAM.Core/Services/TaskService.cs
20. src/AerospaceITAM.Core/Services/UserService.cs
21. src/AerospaceITAM.Core/Services/LocationHierarchyLoader.cs

### 7.3 插件系统文件
22. src/AerospaceITAM.Plugins/IPlugin.cs
23. src/AerospaceITAM.Plugins/PluginManager.cs
24. src/AerospaceITAM.Plugins/PluginLoader.cs
25. src/AerospaceITAM.Plugins/PluginContext.cs

### 7.4 业务模块插件文件
26. src/AerospaceITAM.Modules/AssetManagement/AssetManagementPlugin.cs
27. src/AerospaceITAM.Modules/AssetManagement/Services/AssetLifecycleService.cs
28. src/AerospaceITAM.Modules/AssetManagement/Services/AssetTrackingService.cs

29. src/AerospaceITAM.Modules/LocationManagement/LocationManagementPlugin.cs
30. src/AerospaceITAM.Modules/LocationManagement/Services/LocationHierarchyService.cs
31. src/AerospaceITAM.Modules/LocationManagement/Services/LocationAssociationService.cs

32. src/AerospaceITAM.Modules/PurchaseManagement/PurchaseManagementPlugin.cs
33. src/AerospaceITAM.Modules/PurchaseManagement/Services/SimplifiedPurchaseService.cs
34. src/AerospaceITAM.Modules/PurchaseManagement/Services/AssetGenerationService.cs

35. src/AerospaceITAM.Modules/FaultManagement/FaultManagementPlugin.cs
36. src/AerospaceITAM.Modules/FaultManagement/Services/FaultReportingService.cs
37. src/AerospaceITAM.Modules/FaultManagement/Services/RepairTrackingService.cs

38. src/AerospaceITAM.Modules/TaskManagement/TaskManagementPlugin.cs
39. src/AerospaceITAM.Modules/TaskManagement/Services/DailyTaskService.cs
40. src/AerospaceITAM.Modules/TaskManagement/Services/PeriodicTaskService.cs
41. src/AerospaceITAM.Modules/TaskManagement/Services/PdcaPlanService.cs

42. src/AerospaceITAM.Modules/UserManagement/UserManagementPlugin.cs
43. src/AerospaceITAM.Modules/UserManagement/Services/UserAccountService.cs
44. src/AerospaceITAM.Modules/UserManagement/Services/RolePermissionService.cs

### 7.5 API控制器文件
45. src/AerospaceITAM.API/Controllers/AssetController.cs
46. src/AerospaceITAM.API/Controllers/LocationController.cs
47. src/AerospaceITAM.API/Controllers/PurchaseController.cs
48. src/AerospaceITAM.API/Controllers/FaultController.cs
49. src/AerospaceITAM.API/Controllers/TaskController.cs
50. src/AerospaceITAM.API/Controllers/UserController.cs
51. src/AerospaceITAM.API/Controllers/AuthController.cs
52. src/AerospaceITAM.API/Controllers/ConfigController.cs

### 7.6 REPR模式端点文件
53. src/AerospaceITAM.API/Endpoints/Asset/GetAssetById.cs
54. src/AerospaceITAM.API/Endpoints/Asset/GetAssetList.cs
55. src/AerospaceITAM.API/Endpoints/Asset/CreateAsset.cs
56. src/AerospaceITAM.API/Endpoints/Asset/UpdateAsset.cs
57. src/AerospaceITAM.API/Endpoints/Asset/DeleteAsset.cs

58. src/AerospaceITAM.API/Endpoints/Location/GetLocationById.cs
59. src/AerospaceITAM.API/Endpoints/Location/GetLocationList.cs
60. src/AerospaceITAM.API/Endpoints/Location/CreateLocation.cs
61. src/AerospaceITAM.API/Endpoints/Location/UpdateLocation.cs
62. src/AerospaceITAM.API/Endpoints/Location/DeleteLocation.cs

63. src/AerospaceITAM.API/Endpoints/Purchase/GetPurchaseById.cs
64. src/AerospaceITAM.API/Endpoints/Purchase/GetPurchaseList.cs
65. src/AerospaceITAM.API/Endpoints/Purchase/CreatePurchase.cs
66. src/AerospaceITAM.API/Endpoints/Purchase/UpdatePurchase.cs

67. src/AerospaceITAM.API/Endpoints/Task/GetTaskById.cs
68. src/AerospaceITAM.API/Endpoints/Task/GetTaskList.cs
69. src/AerospaceITAM.API/Endpoints/Task/CreateTask.cs
70. src/AerospaceITAM.API/Endpoints/Task/UpdateTask.cs
71. src/AerospaceITAM.API/Endpoints/Task/DeleteTask.cs

### 7.7 数据访问层文件
72. src/AerospaceITAM.Infrastructure/Data/Context/AppDbContext.cs
73. src/AerospaceITAM.Infrastructure/Data/Context/EventStoreDbContext.cs

74. src/AerospaceITAM.Infrastructure/Data/Repositories/Interfaces/IAssetRepository.cs
75. src/AerospaceITAM.Infrastructure/Data/Repositories/Interfaces/ILocationRepository.cs
76. src/AerospaceITAM.Infrastructure/Data/Repositories/Interfaces/IPurchaseRepository.cs
77. src/AerospaceITAM.Infrastructure/Data/Repositories/Interfaces/IFaultRepository.cs
78. src/AerospaceITAM.Infrastructure/Data/Repositories/Interfaces/ITaskRepository.cs
79. src/AerospaceITAM.Infrastructure/Data/Repositories/Interfaces/IUserRepository.cs
80. src/AerospaceITAM.Infrastructure/Data/Repositories/Interfaces/IEventRepository.cs

81. src/AerospaceITAM.Infrastructure/Data/Repositories/AssetRepository.cs
82. src/AerospaceITAM.Infrastructure/Data/Repositories/LocationRepository.cs
83. src/AerospaceITAM.Infrastructure/Data/Repositories/PurchaseRepository.cs
84. src/AerospaceITAM.Infrastructure/Data/Repositories/FaultRepository.cs
85. src/AerospaceITAM.Infrastructure/Data/Repositories/TaskRepository.cs
86. src/AerospaceITAM.Infrastructure/Data/Repositories/UserRepository.cs
87. src/AerospaceITAM.Infrastructure/Data/Repositories/EventRepository.cs

88. src/AerospaceITAM.Infrastructure/Data/Migrations/InitialCreate.cs

### 7.8 事件与安全相关文件
89. src/AerospaceITAM.Infrastructure/Events/EventManager.cs
90. src/AerospaceITAM.Infrastructure/Events/EventSourcingManager.cs
91. src/AerospaceITAM.Infrastructure/Events/DomainEvent.cs
92. src/AerospaceITAM.Infrastructure/Events/EventTypes.cs

93. src/AerospaceITAM.Infrastructure/Security/JwtAuthenticationHandler.cs
94. src/AerospaceITAM.Infrastructure/Security/PasswordHasher.cs
95. src/AerospaceITAM.Infrastructure/Security/PermissionManager.cs

### 7.9 中间件与过滤器文件
96. src/AerospaceITAM.API/Middlewares/ExceptionHandlingMiddleware.cs
97. src/AerospaceITAM.API/Middlewares/AuditLoggingMiddleware.cs
98. src/AerospaceITAM.API/Middlewares/CorrelationIdMiddleware.cs

99. src/AerospaceITAM.API/Filters/ValidationFilter.cs
100. src/AerospaceITAM.API/Filters/PermissionFilter.cs
101. src/AerospaceITAM.API/Filters/AuditLogFilter.cs

### 7.10 实体与模型文件
102. src/AerospaceITAM.Shared/Models/Entities/Asset.cs
103. src/AerospaceITAM.Shared/Models/Entities/AssetType.cs
104. src/AerospaceITAM.Shared/Models/Entities/Location.cs
105. src/AerospaceITAM.Shared/Models/Entities/LocationUser.cs
106. src/AerospaceITAM.Shared/Models/Entities/Purchase.cs
107. src/AerospaceITAM.Shared/Models/Entities/PurchaseItem.cs
108. src/AerospaceITAM.Shared/Models/Entities/Fault.cs
109. src/AerospaceITAM.Shared/Models/Entities/FaultType.cs
110. src/AerospaceITAM.Shared/Models/Entities/ReturnToFactory.cs
111. src/AerospaceITAM.Shared/Models/Entities/Task.cs
112. src/AerospaceITAM.Shared/Models/Entities/PeriodicRule.cs
113. src/AerospaceITAM.Shared/Models/Entities/PdcaPlan.cs
114. src/AerospaceITAM.Shared/Models/Entities/User.cs
115. src/AerospaceITAM.Shared/Models/Entities/Role.cs
116. src/AerospaceITAM.Shared/Models/Entities/Menu.cs
117. src/AerospaceITAM.Shared/Models/Entities/AuditLog.cs

118. src/AerospaceITAM.Shared/Models/Dtos/AssetDto.cs
119. src/AerospaceITAM.Shared/Models/Dtos/LocationDto.cs
120. src/AerospaceITAM.Shared/Models/Dtos/PurchaseDto.cs
121. src/AerospaceITAM.Shared/Models/Dtos/FaultDto.cs
122. src/AerospaceITAM.Shared/Models/Dtos/TaskDto.cs
123. src/AerospaceITAM.Shared/Models/Dtos/UserDto.cs

124. src/AerospaceITAM.Shared/Models/LocationHierarchyConfig.cs
125. src/AerospaceITAM.Shared/Models/MenuConfig.cs

### 7.11 工具与扩展类文件
126. src/AerospaceITAM.Shared/Utilities/StringUtils.cs
127. src/AerospaceITAM.Shared/Utilities/DateTimeUtils.cs
128. src/AerospaceITAM.Shared/Utilities/JsonUtils.cs
129. src/AerospaceITAM.Shared/Utilities/FileUtils.cs
130. src/AerospaceITAM.Shared/Utilities/SecurityUtils.cs

131. src/AerospaceITAM.Shared/Extensions/EnumExtensions.cs
132. src/AerospaceITAM.Shared/Extensions/StringExtensions.cs
133. src/AerospaceITAM.Shared/Extensions/DateTimeExtensions.cs
134. src/AerospaceITAM.Shared/Extensions/QueryableExtensions.cs
135. src/AerospaceITAM.Shared/Extensions/ServiceCollectionExtensions.cs

136. src/AerospaceITAM.Shared/Constants/SystemConstants.cs
137. src/AerospaceITAM.Shared/Constants/ErrorCodes.cs
138. src/AerospaceITAM.Shared/Constants/PermissionKeys.cs

139. src/AerospaceITAM.Shared/Enums/AssetStatus.cs
140. src/AerospaceITAM.Shared/Enums/LocationType.cs
141. src/AerospaceITAM.Shared/Enums/PurchaseStatus.cs
142. src/AerospaceITAM.Shared/Enums/FaultSeverity.cs
143. src/AerospaceITAM.Shared/Enums/TaskType.cs
144. src/AerospaceITAM.Shared/Enums/TaskStatus.cs
145. src/AerospaceITAM.Shared/Enums/PdcaStage.cs

## 8. 数据模型设计

### 8.1 主要实体关系

```
+----------------+       +----------------+       +----------------+
|     Asset      |------>|    Location    |------>|   Department   |
+----------------+       +----------------+       +----------------+
       |                        |                       |
       |                        |                       |
       v                        v                       v
+----------------+       +----------------+       +----------------+
|   AssetType    |       | LocationUser   |       |     User      |
+----------------+       +----------------+       +----------------+
                                |                       |
                                v                       v
                         +----------------+       +----------------+
                         |     Task       |------>|    Role       |
                         +----------------+       +----------------+
                                |
                                |
                         +----------------+       +----------------+
                         | PeriodicRule/  |       |    Fault      |
                         |   PdcaPlan     |       +----------------+
                         +----------------+
```

### 8.2 核心实体定义

#### 8.2.1 Asset.cs
```csharp
// 资产实体
public class Asset
{
    public int Id { get; set; }
    public string AssetCode { get; set; }  // 资产编码
    public string Name { get; set; }       // 资产名称
    public int AssetTypeId { get; set; }   // 资产类型ID
    public string SerialNumber { get; set; } // 序列号
    public string Model { get; set; }      // 型号
    public string Brand { get; set; }      // 品牌
    public DateTime? PurchaseDate { get; set; } // 购买日期
    public DateTime? WarrantyExpireDate { get; set; } // 保修期
    public decimal? Price { get; set; }    // 价格
    public int? LocationId { get; set; }   // 位置ID
    public AssetStatus Status { get; set; } // 状态
    public string Notes { get; set; }      // 备注
    public DateTime CreatedAt { get; set; } // 创建时间
    public DateTime UpdatedAt { get; set; } // 更新时间
    
    // 导航属性
    public AssetType AssetType { get; set; }
    public Location Location { get; set; }
    public ICollection<FaultRecord> FaultRecords { get; set; }
    public ICollection<LocationHistory> LocationHistories { get; set; }
}
```

#### 8.2.2 Location.cs
```csharp
// 位置实体
public class Location
{
    public int Id { get; set; }
    public string Code { get; set; }       // 位置编码
    public string Name { get; set; }       // 位置名称
    public LocationType Type { get; set; } // 位置类型
    public int? ParentId { get; set; }     // 父位置ID
    public string Path { get; set; }       // 层级路径
    public int? DepartmentId { get; set; } // 部门ID
    public string Description { get; set; } // 描述
    public bool IsActive { get; set; }     // 是否激活
    public DateTime CreatedAt { get; set; } // 创建时间
    public DateTime UpdatedAt { get; set; } // 更新时间
    public int? DefaultDepartmentId { get; set; } // 默认使用部门
    public int? DefaultResponsiblePersonId { get; set; } // 默认负责人
    
    // 导航属性
    public Location Parent { get; set; }
    public ICollection<Location> Children { get; set; }
    public Department Department { get; set; }
    public Department DefaultDepartment { get; set; }
    public User DefaultResponsiblePerson { get; set; }
    public ICollection<Asset> Assets { get; set; }
    public ICollection<LocationUser> LocationUsers { get; set; }
}
```

#### 8.2.3 Purchase.cs
```csharp
// 采购实体
public class PurchaseOrder
{
    public int Id { get; set; }
    public string OrderNumber { get; set; } // 订单编号
    public DateTime OrderDate { get; set; } // 订单日期
    public int? SupplierId { get; set; }   // 供应商ID
    public decimal? TotalAmount { get; set; } // 总金额
    public string Currency { get; set; }   // 货币
    public PurchaseStatus Status { get; set; } // 状态 (0:未到货, 1:已到货)
    public int? RequestedById { get; set; } // 申请人ID
    public string RequesterName { get; set; } // 申请人姓名
    public DateTime? ExpectedDeliveryDate { get; set; } // 预计到货日期
    public DateTime? ActualDeliveryDate { get; set; } // 实际到货日期
    public string Notes { get; set; }      // 备注
    public DateTime CreatedAt { get; set; } // 创建时间
    public DateTime UpdatedAt { get; set; } // 更新时间
    
    // 导航属性
    public Supplier Supplier { get; set; }
    public User RequestedBy { get; set; }
    public ICollection<PurchaseItem> PurchaseItems { get; set; }
}
```

#### 8.2.4 Task.cs
```csharp
// 任务实体
public class Task
{
    public int Id { get; set; }
    public string Title { get; set; }      // 任务标题
    public string Description { get; set; } // 任务描述
    public TaskType TaskType { get; set; } // 任务类型
    public TaskStatus Status { get; set; } // 状态
    public int? AssignedToId { get; set; } // 分配给谁
    public int CreatedById { get; set; }   // 创建人
    public DateTime CreatedAt { get; set; } // 创建时间
    public DateTime? DueDate { get; set; } // 截止日期
    public DateTime? CompletedAt { get; set; } // 完成时间
    public int? RelatedAssetId { get; set; } // 关联资产ID
    public int? RelatedLocationId { get; set; } // 关联位置ID
    public int? PeriodicRuleId { get; set; } // 周期规则ID
    public int? PdcaPlanId { get; set; }   // PDCA计划ID
    public string Notes { get; set; }      // 备注
    public DateTime UpdatedAt { get; set; } // 更新时间
    
    // 导航属性
    public User AssignedTo { get; set; }
    public User CreatedBy { get; set; }
    public Asset RelatedAsset { get; set; }
    public Location RelatedLocation { get; set; }
    public PeriodicRule PeriodicRule { get; set; }
    public PdcaPlan PdcaPlan { get; set; }
}
```

## 9. API接口设计

以下是系统主要API接口设计，采用RESTful风格：

### 9.1 资产管理API

#### 9.1.1 获取资产列表
- **URL**: GET /api/assets
- **权限**: asset:view
- **查询参数**: 
  - page: 页码
  - size: 每页数量
  - status: 资产状态
  - type: 资产类型
  - location: 位置ID
  - keyword: 搜索关键词
- **响应**: 资产列表分页结果

#### 9.1.2 获取资产详情
- **URL**: GET /api/assets/{id}
- **权限**: asset:view
- **响应**: 资产详细信息

#### 9.1.3 创建资产
- **URL**: POST /api/assets
- **权限**: asset:create
- **请求体**: 资产创建数据
- **响应**: 创建的资产信息

#### 9.1.4 更新资产
- **URL**: PUT /api/assets/{id}
- **权限**: asset:update
- **请求体**: 资产更新数据
- **响应**: 更新后的资产信息

#### 9.1.5 删除资产
- **URL**: DELETE /api/assets/{id}
- **权限**: asset:delete
- **响应**: 操作结果

### 9.2 位置管理API

#### 9.2.1 获取位置列表
- **URL**: GET /api/locations
- **权限**: location:view
- **查询参数**: 
  - page: 页码
  - size: 每页数量
  - type: 位置类型
  - parent: 父位置ID
  - keyword: 搜索关键词
- **响应**: 位置列表分页结果

#### 9.2.2 获取位置详情
- **URL**: GET /api/locations/{id}
- **权限**: location:view
- **响应**: 位置详细信息

#### 9.2.3 创建位置
- **URL**: POST /api/locations
- **权限**: location:create
- **请求体**: 位置创建数据
- **响应**: 创建的位置信息

#### 9.2.4 更新位置
- **URL**: PUT /api/locations/{id}
- **权限**: location:update
- **请求体**: 位置更新数据
- **响应**: 更新后的位置信息

#### 9.2.5 获取位置层级结构
- **URL**: GET /api/locations/hierarchy
- **权限**: location:view
- **响应**: 位置层级结构树

### 9.3 采购管理API

#### 9.3.1 获取采购列表
- **URL**: GET /api/purchases
- **权限**: purchase:view
- **查询参数**: 
  - page: 页码
  - size: 每页数量
  - status: 采购状态
  - startDate: 开始日期
  - endDate: 结束日期
  - keyword: 搜索关键词
- **响应**: 采购列表分页结果

#### 9.3.2 获取采购详情
- **URL**: GET /api/purchases/{id}
- **权限**: purchase:view
- **响应**: 采购详细信息

#### 9.3.3 创建采购
- **URL**: POST /api/purchases
- **权限**: purchase:create
- **请求体**: 采购创建数据
- **响应**: 创建的采购信息

#### 9.3.4 更新采购
- **URL**: PUT /api/purchases/{id}
- **权限**: purchase:update
- **请求体**: 采购更新数据
- **响应**: 更新后的采购信息

#### 9.3.5 采购到货确认
- **URL**: POST /api/purchases/{id}/delivery
- **权限**: purchase:update
- **请求体**: 到货信息
- **响应**: 操作结果

### 9.4 任务管理API

#### 9.4.1 获取任务列表
- **URL**: GET /api/tasks
- **权限**: task:view
- **查询参数**: 
  - page: 页码
  - size: 每页数量
  - type: 任务类型
  - status: 任务状态
  - assignedTo: 分配人ID
  - keyword: 搜索关键词
- **响应**: 任务列表分页结果

#### 9.4.2 获取任务详情
- **URL**: GET /api/tasks/{id}
- **权限**: task:view
- **响应**: 任务详细信息

#### 9.4.3 创建任务
- **URL**: POST /api/tasks
- **权限**: task:create
- **请求体**: 任务创建数据
- **响应**: 创建的任务信息

#### 9.4.4 更新任务
- **URL**: PUT /api/tasks/{id}
- **权限**: task:update
- **请求体**: 任务更新数据
- **响应**: 更新后的任务信息

#### 9.4.5 完成任务
- **URL**: POST /api/tasks/{id}/complete
- **权限**: task:update
- **响应**: 操作结果

## 10. 实现注意事项

### 10.1 代码规范
- 所有代码必须遵循C#编码规范
- 方法应尽量保持简短，不超过25行
- 类应尽量保持简单，聚焦于单一职责
- 使用注释说明复杂逻辑，但避免过度注释
- 使用强类型和防御性编程，避免空引用异常

### 10.2 性能优化
- 避免N+1查询问题，合理使用Include和Join
- 适当使用索引，优化查询性能
- 对于频繁访问的数据，考虑缓存策略
- 使用异步方法处理I/O操作
- 避免不必要的对象创建和内存分配

### 10.3 安全考虑
- 所有用户输入必须进行验证和清洗
- 敏感数据必须加密存储
- 使用参数化SQL查询，避免SQL注入
- 实施最小权限原则
- 审计敏感操作并记录日志

### 10.4 事件溯源实现
- 所有状态变更应作为不可变事件记录
- 系统状态应能从事件流重建
- 事件应包含完整上下文信息
- 事件应支持版本控制和回溯

### 10.5 断网操作队列实现
- 客户端应能在离线状态下继续工作
- 操作应排队并在连接恢复后同步
- 应处理冲突和版本冲突
- 提供同步状态指示器

### 10.6 位置层级配置实现
- 位置结构通过JSON配置文件定义
- 支持动态加载和热更新
- 提供默认配置和回退机制
- 配置更改应记录日志

### 10.7 插件机制实现
- 插件应符合接口约定
- 支持插件的热加载和卸载
- 插件应独立部署和版本控制
- 核心系统不应直接依赖插件实现

所有代码实现应严格遵循本文档中定义的设计原则和约束，确保系统的高度可维护性、可靠性和效率。

### 10.8 用户界面生成实现
- 使用JSON配置定义界面结构
- 支持动态字段和表单验证
- 配置应包含表现层和数据绑定
- 支持主题和样式定制化

### 10.9 测试策略
- 核心引擎应有高测试覆盖率
- 使用单元测试验证业务逻辑
- 使用集成测试验证模块间交互
- 使用端到端测试验证关键流程

## 11. 结论与最佳实践

这份蓝图文档为航空航天级IT资产管理系统的后端实现提供了详细的指导。遵循这些设计原则和实现约束，可以确保系统具有高度的可维护性、可扩展性和性能效率。

### 关键实施原则总结

1. **简洁高效**: 控制代码量，保持函数精简，遵循单一职责原则
2. **模块化与解耦**: 通过微内核+插件架构实现高度的模块化和松耦合
3. **标准化与一致性**: 遵循统一的编码标准、命名约定和架构模式
4. **可测试性**: 设计应支持自动化测试，便于验证系统行为
5. **安全性**: 实施最佳安全实践，保护数据和防止未授权访问

开发团队应严格按照本文档定义的目录结构和文件清单进行实施，确保系统架构的一致性和完整性。每个文件应专注于其定义的单一职责，并且不超过规定的代码行数限制。

通过这种方式，航空航天级IT资产管理系统将成为一个精简、高效、可靠的企业级解决方案，满足要求的所有功能需求，同时保持代码库的可维护性和可扩展性。