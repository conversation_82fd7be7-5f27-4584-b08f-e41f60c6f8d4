<template>
  <div class="factory-dashboard-v2">
    <!-- 顶部导航栏 -->
    <div class="dashboard-header">
      <div class="header-left">
        <h1 class="dashboard-title">
          <el-icon class="title-icon"><Monitor /></el-icon>
          智能制造监控系统 V2.0
        </h1>
        <div class="subtitle">基于版本 ef7aee39-e800-4bb8-8878-3d453576c3fc</div>
      </div>
      
      <div class="header-center">
        <div class="status-overview">
          <div class="status-item operational">
            <span class="count">{{ statusCounts.operational }}</span>
            <span class="label">正常运行</span>
          </div>
          <div class="status-item warning">
            <span class="count">{{ statusCounts.warning }}</span>
            <span class="label">警告</span>
          </div>
          <div class="status-item error">
            <span class="count">{{ statusCounts.error }}</span>
            <span class="label">故障</span>
          </div>
          <div class="status-item idle">
            <span class="count">{{ statusCounts.idle }}</span>
            <span class="label">空闲</span>
          </div>
        </div>
      </div>
      
      <div class="header-right">
        <div class="time-display">
          <div class="current-time">{{ currentTime }}</div>
          <div class="last-update">更新: {{ formatTime(lastUpdate) }}</div>
        </div>
        <el-button @click="refreshData" :loading="refreshing" type="primary" size="small">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
    </div>

    <!-- 主要内容区 -->
    <div class="dashboard-main">
      <!-- 左侧控制面板 -->
      <div class="control-panel">
        <div class="panel-section">
          <h3>区域筛选</h3>
          <div class="zone-filters">
            <el-checkbox v-model="showAllZones" @change="toggleAllZones">全部区域</el-checkbox>
            <el-checkbox-group v-model="selectedZones" @change="onZoneFilterChange">
              <el-checkbox 
                v-for="zone in uniqueZones" 
                :key="zone.id" 
                :label="zone.id"
                :style="{ color: zone.color }"
              >
                {{ zone.name }}
              </el-checkbox>
            </el-checkbox-group>
          </div>
        </div>

        <div class="panel-section">
          <h3>状态筛选</h3>
          <div class="status-filters">
            <el-checkbox-group v-model="selectedStatuses">
              <el-checkbox label="operational">正常运行</el-checkbox>
              <el-checkbox label="warning">警告</el-checkbox>
              <el-checkbox label="error">故障</el-checkbox>
              <el-checkbox label="idle">空闲</el-checkbox>
            </el-checkbox-group>
          </div>
        </div>

        <div class="panel-section">
          <h3>显示设置</h3>
          <div class="display-settings">
            <el-switch v-model="showWorkstationNumbers" active-text="显示工位号" />
            <el-switch v-model="showZoneLabels" active-text="显示区域标签" />
            <el-switch v-model="enableAnimation" active-text="启用动画" />
          </div>
        </div>
      </div>

      <!-- 中央工厂布局 -->
      <div class="factory-layout-container">
        <div class="layout-header">
          <h2>工厂布局监控</h2>
          <div class="layout-stats">
            <span>总工位: {{ totalWorkstations }}</span>
            <span>活跃区域: {{ activeZones }}</span>
            <span>在线率: {{ onlineRate }}%</span>
          </div>
        </div>

        <div class="factory-canvas" ref="canvasRef">
          <!-- 工厂布局画布 -->
          <div class="canvas-container" :style="canvasStyle">
            <!-- 区域容器 -->
            <div
              v-for="zone in filteredZones"
              :key="zone.id"
              class="zone-container"
              :class="[
                `zone-${zone.id}`,
                { 'zone-highlighted': highlightedZone === zone.id }
              ]"
              :style="getZoneStyle(zone)"
              @mouseenter="highlightZone(zone.id)"
              @mouseleave="clearHighlight"
            >
              <!-- 区域标签 -->
              <div v-if="showZoneLabels" class="zone-label" :style="{ color: zone.color }">
                {{ zone.name }}
              </div>

              <!-- 工位网格 -->
              <div class="workstation-grid" :style="getGridStyle(zone)">
                <div
                  v-for="workstation in getZoneWorkstations(zone)"
                  :key="workstation.id"
                  class="workstation-cell"
                  :class="getWorkstationClasses(workstation)"
                  :style="{ backgroundColor: zone.color }"
                  @click="selectWorkstation(workstation)"
                  @mouseenter="showWorkstationTooltip(workstation, $event)"
                  @mouseleave="hideWorkstationTooltip"
                >
                  <div class="cell-content">
                    <el-icon v-if="workstation.status === 'error'" class="status-icon error">
                      <Close />
                    </el-icon>
                    <el-icon v-else-if="workstation.status === 'warning'" class="status-icon warning">
                      <Warning />
                    </el-icon>
                    <span v-if="showWorkstationNumbers" class="workstation-number">
                      {{ workstation.id }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧详情面板 -->
      <div class="detail-panel">
        <div class="panel-section">
          <h3>实时监控</h3>
          <div class="monitoring-cards">
            <div class="monitor-card">
              <div class="card-title">设备效率</div>
              <div class="card-value">{{ averageEfficiency }}%</div>
              <div class="card-trend up">↑ 2.3%</div>
            </div>
            <div class="monitor-card">
              <div class="card-title">运行时间</div>
              <div class="card-value">{{ averageUptime }}%</div>
              <div class="card-trend down">↓ 0.8%</div>
            </div>
            <div class="monitor-card">
              <div class="card-title">产能利用率</div>
              <div class="card-value">{{ capacityUtilization }}%</div>
              <div class="card-trend up">↑ 1.5%</div>
            </div>
          </div>
        </div>

        <div class="panel-section" v-if="selectedWorkstation">
          <h3>工位详情</h3>
          <div class="workstation-details">
            <div class="detail-item">
              <span class="label">工位编号:</span>
              <span class="value">{{ selectedWorkstation.id }}</span>
            </div>
            <div class="detail-item">
              <span class="label">所属区域:</span>
              <span class="value">{{ selectedWorkstation.zoneName }}</span>
            </div>
            <div class="detail-item">
              <span class="label">运行状态:</span>
              <span class="value" :class="selectedWorkstation.status">
                {{ getStatusText(selectedWorkstation.status) }}
              </span>
            </div>
            <div class="detail-item">
              <span class="label">设备效率:</span>
              <span class="value">{{ selectedWorkstation.efficiency }}%</span>
            </div>
            <div class="detail-item">
              <span class="label">运行时间:</span>
              <span class="value">{{ selectedWorkstation.uptime }}%</span>
            </div>
            <div class="detail-item">
              <span class="label">任务数量:</span>
              <span class="value">{{ selectedWorkstation.taskCount }}</span>
            </div>
          </div>
        </div>

        <div class="panel-section">
          <h3>告警信息</h3>
          <div class="alert-list">
            <div 
              v-for="alert in recentAlerts" 
              :key="alert.id"
              class="alert-item"
              :class="alert.level"
            >
              <div class="alert-time">{{ formatTime(alert.time) }}</div>
              <div class="alert-message">{{ alert.message }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 工位详情弹窗 -->
    <el-dialog
      v-model="showWorkstationDialog"
      :title="`工位 ${selectedWorkstation?.id} 详细信息`"
      width="600px"
      :close-on-click-modal="false"
    >
      <div v-if="selectedWorkstation" class="workstation-dialog-content">
        <!-- 详细信息内容将在后续添加 -->
        <p>工位详细信息对话框内容...</p>
      </div>
    </el-dialog>

    <!-- 工位提示框 -->
    <div
      v-if="tooltipVisible"
      class="workstation-tooltip"
      :style="tooltipStyle"
    >
      <div class="tooltip-content">
        <div class="tooltip-title">工位 {{ tooltipData?.id }}</div>
        <div class="tooltip-info">
          <div>区域: {{ tooltipData?.zoneName }}</div>
          <div>状态: {{ getStatusText(tooltipData?.status) }}</div>
          <div>效率: {{ tooltipData?.efficiency }}%</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Monitor, Refresh, Close, Warning } from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(true)
const refreshing = ref(false)
const currentTime = ref('')
const lastUpdate = ref(new Date())
const canvasRef = ref(null)

// 布局配置数据
const layoutConfig = ref(null)
const workstations = ref([])
const selectedWorkstation = ref(null)
const highlightedZone = ref(null)

// 筛选和显示设置
const showAllZones = ref(true)
const selectedZones = ref([])
const selectedStatuses = ref(['operational', 'warning', 'error', 'idle'])
const showWorkstationNumbers = ref(true)
const showZoneLabels = ref(true)
const enableAnimation = ref(true)

// 弹窗和提示框
const showWorkstationDialog = ref(false)
const tooltipVisible = ref(false)
const tooltipData = ref(null)
const tooltipStyle = ref({})

// 告警数据
const recentAlerts = ref([])

// 加载布局配置
const loadLayoutConfig = async () => {
  try {
    loading.value = true
    
    // 加载特定版本的配置文件
    const response = await fetch('/analyresport/factory-layout-1748945213262.json')
    if (!response.ok) {
      throw new Error('配置文件加载失败')
    }
    
    const config = await response.json()
    layoutConfig.value = config
    
    // 生成工位数据
    generateWorkstationData()
    
    ElMessage.success('布局配置加载成功')
  } catch (error) {
    console.error('加载配置失败:', error)
    ElMessage.error('加载配置失败，使用默认数据')
    useDefaultConfig()
  } finally {
    loading.value = false
  }
}

// 生成工位数据
const generateWorkstationData = () => {
  const stations = []
  
  layoutConfig.value.zones.forEach(zone => {
    const totalWorkstations = zone.rows * zone.cols
    
    for (let i = 0; i < totalWorkstations; i++) {
      const workstationId = zone.startWorkstation + i
      
      stations.push({
        id: workstationId,
        zoneId: zone.id,
        zoneName: zone.name,
        zoneColor: zone.color,
        status: getRandomStatus(),
        efficiency: Math.floor(Math.random() * 30) + 70,
        uptime: Math.floor(Math.random() * 20) + 80,
        taskCount: Math.floor(Math.random() * 8) + 1,
        assetCount: Math.floor(Math.random() * 5) + 2,
        lastUpdate: new Date()
      })
    }
  })
  
  workstations.value = stations
  selectedZones.value = layoutConfig.value.zones.map(z => z.id)
}

// 使用默认配置
const useDefaultConfig = () => {
  layoutConfig.value = {
    zones: [
      {
        id: 1,
        name: "默认区域",
        x: 100,
        y: 100,
        width: 200,
        height: 150,
        rows: 3,
        cols: 3,
        color: "#4A90E2",
        startWorkstation: 1
      }
    ]
  }
  generateWorkstationData()
}

// 获取随机状态
const getRandomStatus = () => {
  const statuses = ['operational', 'warning', 'error', 'idle']
  const weights = [0.7, 0.15, 0.1, 0.05]
  const random = Math.random()
  let sum = 0

  for (let i = 0; i < weights.length; i++) {
    sum += weights[i]
    if (random <= sum) {
      return statuses[i]
    }
  }
  return 'operational'
}

// 计算属性
const uniqueZones = computed(() => {
  return layoutConfig.value?.zones || []
})

const filteredZones = computed(() => {
  if (!layoutConfig.value) return []

  return layoutConfig.value.zones.filter(zone => {
    return showAllZones.value || selectedZones.value.includes(zone.id)
  })
})

const filteredWorkstations = computed(() => {
  return workstations.value.filter(ws => {
    const zoneMatch = showAllZones.value || selectedZones.value.includes(ws.zoneId)
    const statusMatch = selectedStatuses.value.includes(ws.status)
    return zoneMatch && statusMatch
  })
})

const statusCounts = computed(() => {
  const counts = { operational: 0, warning: 0, error: 0, idle: 0 }
  filteredWorkstations.value.forEach(ws => {
    counts[ws.status]++
  })
  return counts
})

const totalWorkstations = computed(() => {
  return filteredWorkstations.value.length
})

const activeZones = computed(() => {
  return filteredZones.value.length
})

const onlineRate = computed(() => {
  const total = filteredWorkstations.value.length
  const online = filteredWorkstations.value.filter(ws => ws.status !== 'idle').length
  return total > 0 ? Math.round((online / total) * 100) : 0
})

const averageEfficiency = computed(() => {
  const total = filteredWorkstations.value.length
  if (total === 0) return 0
  const sum = filteredWorkstations.value.reduce((acc, ws) => acc + ws.efficiency, 0)
  return Math.round(sum / total)
})

const averageUptime = computed(() => {
  const total = filteredWorkstations.value.length
  if (total === 0) return 0
  const sum = filteredWorkstations.value.reduce((acc, ws) => acc + ws.uptime, 0)
  return Math.round(sum / total)
})

const capacityUtilization = computed(() => {
  return Math.round(Math.random() * 20 + 75) // 模拟数据
})

const canvasStyle = computed(() => {
  if (!layoutConfig.value?.zones) return {}

  // 计算画布尺寸
  let maxX = 0, maxY = 0
  layoutConfig.value.zones.forEach(zone => {
    maxX = Math.max(maxX, zone.x + zone.width)
    maxY = Math.max(maxY, zone.y + zone.height)
  })

  return {
    width: `${maxX + 50}px`,
    height: `${maxY + 50}px`,
    position: 'relative',
    background: 'linear-gradient(135deg, #0f172a 0%, #1e293b 100%)',
    border: '2px solid #334155',
    borderRadius: '8px'
  }
})

// 样式方法
const getZoneStyle = (zone) => {
  return {
    position: 'absolute',
    left: `${zone.x}px`,
    top: `${zone.y}px`,
    width: `${zone.width}px`,
    height: `${zone.height}px`,
    border: `2px solid ${zone.color}`,
    borderRadius: '6px',
    backgroundColor: `${zone.color}15`,
    transition: enableAnimation.value ? 'all 0.3s ease' : 'none'
  }
}

const getGridStyle = (zone) => {
  return {
    display: 'grid',
    gridTemplateRows: `repeat(${zone.rows}, 1fr)`,
    gridTemplateColumns: `repeat(${zone.cols}, 1fr)`,
    gap: `${zone.gapY || 2}px ${zone.gapX || 2}px`,
    width: '100%',
    height: '100%',
    padding: '4px'
  }
}

const getZoneWorkstations = (zone) => {
  return workstations.value.filter(ws => ws.zoneId === zone.id)
}

const getWorkstationClasses = (workstation) => {
  return [
    'workstation',
    `status-${workstation.status}`,
    { 'selected': selectedWorkstation.value?.id === workstation.id },
    { 'animated': enableAnimation.value }
  ]
}

const getStatusText = (status) => {
  const statusMap = {
    operational: '正常运行',
    warning: '警告',
    error: '故障',
    idle: '空闲'
  }
  return statusMap[status] || '未知'
}

// 事件处理方法
const refreshData = async () => {
  refreshing.value = true
  try {
    // 模拟数据刷新
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 重新生成工位数据
    generateWorkstationData()
    lastUpdate.value = new Date()

    ElMessage.success('数据刷新成功')
  } catch (error) {
    ElMessage.error('数据刷新失败')
  } finally {
    refreshing.value = false
  }
}

const toggleAllZones = (checked) => {
  if (checked) {
    selectedZones.value = layoutConfig.value?.zones.map(z => z.id) || []
  } else {
    selectedZones.value = []
  }
}

const onZoneFilterChange = (zones) => {
  showAllZones.value = zones.length === (layoutConfig.value?.zones.length || 0)
}

const highlightZone = (zoneId) => {
  highlightedZone.value = zoneId
}

const clearHighlight = () => {
  highlightedZone.value = null
}

const selectWorkstation = (workstation) => {
  selectedWorkstation.value = workstation
  showWorkstationDialog.value = true
}

const showWorkstationTooltip = (workstation, event) => {
  tooltipData.value = workstation
  tooltipVisible.value = true

  const rect = event.target.getBoundingClientRect()
  tooltipStyle.value = {
    position: 'fixed',
    left: `${rect.right + 10}px`,
    top: `${rect.top}px`,
    zIndex: 9999
  }
}

const hideWorkstationTooltip = () => {
  tooltipVisible.value = false
  tooltipData.value = null
}

// 时间格式化
const formatTime = (date) => {
  return date.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 更新当前时间
const updateCurrentTime = () => {
  currentTime.value = new Date().toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 生成模拟告警数据
const generateAlerts = () => {
  const alertTypes = [
    { level: 'error', message: '工位异常停机' },
    { level: 'warning', message: '设备温度过高' },
    { level: 'warning', message: '生产效率下降' },
    { level: 'error', message: '传感器通信故障' }
  ]

  recentAlerts.value = Array.from({ length: 5 }, (_, i) => {
    const alert = alertTypes[Math.floor(Math.random() * alertTypes.length)]
    return {
      id: i + 1,
      level: alert.level,
      message: alert.message,
      time: new Date(Date.now() - Math.random() * 3600000) // 最近1小时内
    }
  }).sort((a, b) => b.time - a.time)
}

// 生命周期
onMounted(async () => {
  await loadLayoutConfig()
  generateAlerts()
  updateCurrentTime()

  // 定时更新时间
  const timeInterval = setInterval(updateCurrentTime, 1000)

  // 定时更新数据
  const dataInterval = setInterval(() => {
    lastUpdate.value = new Date()
    // 可以在这里添加实时数据更新逻辑
  }, 30000) // 30秒更新一次

  // 清理定时器
  onUnmounted(() => {
    clearInterval(timeInterval)
    clearInterval(dataInterval)
  })
})
</script>

<style scoped>
.factory-dashboard-v2 {
  min-height: 100vh;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
  color: #f1f5f9;
  font-family: 'SF Pro Display', 'Segoe UI', sans-serif;
}

/* 顶部导航栏 */
.dashboard-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  background: rgba(15, 23, 42, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid #334155;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.header-left {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.dashboard-title {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0;
  font-size: 24px;
  font-weight: 700;
  color: #f1f5f9;
}

.title-icon {
  font-size: 28px;
  color: #3b82f6;
}

.subtitle {
  font-size: 12px;
  color: #94a3b8;
  font-family: 'Courier New', monospace;
}

.header-center {
  flex: 1;
  display: flex;
  justify-content: center;
}

.status-overview {
  display: flex;
  gap: 24px;
}

.status-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 16px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  min-width: 80px;
}

.status-item.operational {
  border-color: #10b981;
  background: rgba(16, 185, 129, 0.1);
}

.status-item.warning {
  border-color: #f59e0b;
  background: rgba(245, 158, 11, 0.1);
}

.status-item.error {
  border-color: #ef4444;
  background: rgba(239, 68, 68, 0.1);
}

.status-item.idle {
  border-color: #6b7280;
  background: rgba(107, 114, 128, 0.1);
}

.status-item .count {
  font-size: 24px;
  font-weight: 700;
  line-height: 1;
}

.status-item .label {
  font-size: 12px;
  color: #94a3b8;
  margin-top: 4px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.time-display {
  text-align: right;
}

.current-time {
  font-size: 16px;
  font-weight: 600;
  color: #f1f5f9;
}

.last-update {
  font-size: 12px;
  color: #94a3b8;
}

/* 主要内容区 */
.dashboard-main {
  display: grid;
  grid-template-columns: 280px 1fr 320px;
  gap: 24px;
  padding: 24px;
  min-height: calc(100vh - 100px);
}

/* 控制面板 */
.control-panel {
  background: rgba(15, 23, 42, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid #334155;
  border-radius: 12px;
  padding: 20px;
  height: fit-content;
}

.panel-section {
  margin-bottom: 24px;
}

.panel-section:last-child {
  margin-bottom: 0;
}

.panel-section h3 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #f1f5f9;
  border-bottom: 1px solid #334155;
  padding-bottom: 8px;
}

.zone-filters {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.status-filters {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.display-settings {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* 工厂布局容器 */
.factory-layout-container {
  background: rgba(15, 23, 42, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid #334155;
  border-radius: 12px;
  padding: 20px;
  overflow: hidden;
}

.layout-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #334155;
}

.layout-header h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #f1f5f9;
}

.layout-stats {
  display: flex;
  gap: 16px;
  font-size: 14px;
  color: #94a3b8;
}

.factory-canvas {
  width: 100%;
  height: 600px;
  overflow: auto;
  border-radius: 8px;
  background: #0f172a;
}

.canvas-container {
  margin: 20px;
}

/* 区域样式 */
.zone-container {
  cursor: pointer;
  transition: all 0.3s ease;
}

.zone-container:hover {
  transform: scale(1.02);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.zone-container.zone-highlighted {
  transform: scale(1.05);
  box-shadow: 0 12px 35px rgba(59, 130, 246, 0.4);
}

.zone-label {
  position: absolute;
  top: -20px;
  left: 0;
  font-size: 12px;
  font-weight: 600;
  padding: 2px 8px;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 4px;
  white-space: nowrap;
}

.workstation-grid {
  width: 100%;
  height: 100%;
}

/* 工位样式 */
.workstation-cell {
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.workstation-cell:hover {
  transform: scale(1.1);
  z-index: 10;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
}

.workstation-cell.selected {
  ring: 2px solid #3b82f6;
  transform: scale(1.15);
}

.workstation-cell.animated {
  animation: pulse 2s infinite;
}

.cell-content {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  min-height: 20px;
}

.status-icon {
  position: absolute;
  top: 2px;
  right: 2px;
  font-size: 10px;
}

.status-icon.error {
  color: #ef4444;
}

.status-icon.warning {
  color: #f59e0b;
}

.workstation-number {
  font-size: 10px;
  font-weight: 600;
  color: white;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
}

/* 工位状态样式 */
.workstation.status-operational {
  background-color: #10b981 !important;
  border: 1px solid #059669;
}

.workstation.status-warning {
  background-color: #f59e0b !important;
  border: 1px solid #d97706;
}

.workstation.status-error {
  background-color: #ef4444 !important;
  border: 1px solid #dc2626;
}

.workstation.status-idle {
  background-color: #6b7280 !important;
  border: 1px solid #4b5563;
}

/* 详情面板 */
.detail-panel {
  background: rgba(15, 23, 42, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid #334155;
  border-radius: 12px;
  padding: 20px;
  height: fit-content;
}

.monitoring-cards {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.monitor-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 14px;
  color: #94a3b8;
}

.card-value {
  font-size: 20px;
  font-weight: 700;
  color: #f1f5f9;
}

.card-trend {
  font-size: 12px;
  font-weight: 600;
}

.card-trend.up {
  color: #10b981;
}

.card-trend.down {
  color: #ef4444;
}

.workstation-details {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-item .label {
  font-size: 14px;
  color: #94a3b8;
}

.detail-item .value {
  font-size: 14px;
  font-weight: 600;
  color: #f1f5f9;
}

.detail-item .value.operational {
  color: #10b981;
}

.detail-item .value.warning {
  color: #f59e0b;
}

.detail-item .value.error {
  color: #ef4444;
}

.detail-item .value.idle {
  color: #6b7280;
}

.alert-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 200px;
  overflow-y: auto;
}

.alert-item {
  padding: 12px;
  border-radius: 6px;
  border-left: 4px solid;
}

.alert-item.error {
  background: rgba(239, 68, 68, 0.1);
  border-left-color: #ef4444;
}

.alert-item.warning {
  background: rgba(245, 158, 11, 0.1);
  border-left-color: #f59e0b;
}

.alert-time {
  font-size: 12px;
  color: #94a3b8;
  margin-bottom: 4px;
}

.alert-message {
  font-size: 14px;
  color: #f1f5f9;
}

/* 工位提示框 */
.workstation-tooltip {
  background: rgba(0, 0, 0, 0.9);
  border: 1px solid #334155;
  border-radius: 8px;
  padding: 12px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  pointer-events: none;
}

.tooltip-content {
  color: #f1f5f9;
}

.tooltip-title {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #3b82f6;
}

.tooltip-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
  font-size: 12px;
  color: #94a3b8;
}

/* 动画效果 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* 响应式设计 */
@media (max-width: 1600px) {
  .dashboard-main {
    grid-template-columns: 260px 1fr 300px;
    gap: 20px;
    padding: 20px;
  }

  .factory-canvas {
    height: 500px;
  }
}

@media (max-width: 1200px) {
  .dashboard-main {
    grid-template-columns: 1fr;
    grid-template-rows: auto auto auto;
    gap: 16px;
  }

  .control-panel,
  .detail-panel {
    order: 2;
  }

  .factory-layout-container {
    order: 1;
  }

  .factory-canvas {
    height: 400px;
  }
}

@media (max-width: 768px) {
  .dashboard-header {
    flex-direction: column;
    gap: 16px;
    padding: 16px;
  }

  .header-center {
    order: 3;
  }

  .status-overview {
    gap: 12px;
  }

  .status-item {
    min-width: 60px;
    padding: 8px 12px;
  }

  .dashboard-main {
    padding: 16px;
    gap: 12px;
  }

  .factory-canvas {
    height: 300px;
  }
}
</style>
