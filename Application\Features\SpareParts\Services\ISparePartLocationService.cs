// File: Application/Features/SpareParts/Services/ISparePartLocationService.cs
// Description: 备品备件库位服务接口

using System.Collections.Generic;
using System.Threading.Tasks;
using ItAssetsSystem.Application.Features.SpareParts.Dtos;

namespace ItAssetsSystem.Application.Features.SpareParts.Services
{
    /// <summary>
    /// 备品备件库位服务接口
    /// </summary>
    public interface ISparePartLocationService
    {
        /// <summary>
        /// 获取所有库位列表
        /// </summary>
        /// <returns>库位列表</returns>
        Task<List<SparePartLocationDto>> GetAllLocationsAsync();
        
        /// <summary>
        /// 根据区域获取库位列表
        /// </summary>
        /// <param name="area">区域标识</param>
        /// <returns>库位列表</returns>
        Task<List<SparePartLocationDto>> GetLocationsByAreaAsync(string area);
        
        /// <summary>
        /// 获取库位详情
        /// </summary>
        /// <param name="id">库位ID</param>
        /// <returns>库位DTO</returns>
        Task<SparePartLocationDto> GetLocationByIdAsync(long id);
        
        /// <summary>
        /// 创建库位
        /// </summary>
        /// <param name="request">创建请求</param>
        /// <returns>创建的库位DTO</returns>
        Task<SparePartLocationDto> CreateLocationAsync(CreateSparePartLocationRequest request);
        
        /// <summary>
        /// 更新库位
        /// </summary>
        /// <param name="id">库位ID</param>
        /// <param name="request">更新请求</param>
        /// <returns>更新后的库位DTO</returns>
        Task<SparePartLocationDto> UpdateLocationAsync(long id, CreateSparePartLocationRequest request);
        
        /// <summary>
        /// 删除库位
        /// </summary>
        /// <param name="id">库位ID</param>
        /// <returns>是否成功</returns>
        Task<bool> DeleteLocationAsync(long id);
        
        /// <summary>
        /// 获取所有区域列表
        /// </summary>
        /// <returns>区域列表</returns>
        Task<List<string>> GetAllAreasAsync();
    }
} 