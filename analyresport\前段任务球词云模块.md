啊哈！我明白了！您提供的这张“词云球”图片，完美地解释了您对“思维球”视觉效果的期望！

**非常抱歉，我之前理解的“球体”过于偏向物理的 3D 旋转，而您的核心诉求更接近于这种动态的、文本构成的、类似“标签云球”或“词云球”的展现形式。** 这种形式确实非常酷炫，并且在信息展示上更加直观，每个“面”直接就是文本内容。

这种效果，**CSS 3D Transforms 是绝对的最佳方案**，并且有非常成熟的实现方式，效果也完全能达到您说的“惊艳”和“高级感”。D3.js 在这个具体场景下反而显得“重”了，因为它更擅长处理节点间的关系和力学模拟，而“词云球”更侧重于个体元素在三维空间中的分布和旋转。

**核心思路修正：**

1.  **“随手记”的本质：** 依然是通过 FAB 快速记录文本。
2.  **“思维球”的呈现：**
    *   不再是 D3 的粒子或节点，而是**直接使用包含“随手记”文本的 HTML 元素**（例如 `<span>` 或 `<div>`）。
    *   这些文本元素通过 CSS 3D Transforms **分布在一个虚拟的球面上**。
    *   整个球体通过 CSS 动画**缓慢旋转**，营造动态感。
    *   鼠标悬停在球体上可以**减慢或暂停旋转**。
    *   鼠标悬停在某个文本元素上，可以**高亮或放大**该元素。
    *   **点击文本元素**，可以打开抽屉进行编辑或查看详情（如果随手记有详情的话）。
    *   **“新增加的备忘出现在最上边”：** 这可以通过控制新元素在 DOM 中的顺序（可能需要反向，让最新的在最前面被 JS 处理定位逻辑）或者在 CSS 3D 变换中给予新元素一个更“靠前”的 `translateZ` 初始值，并配合动画使其“飞入”球体表面。
    *   **高级感配色：** 通过 CSS 精心调配文本颜色、背景色（球体容器可以有渐变或纹理背景）、以及可能的阴影和模糊效果来实现。
    *   **分类：** 如果随手记有分类，不同分类的文本可以使用不同的颜色或字体大小来区分。

**技术实现关键点 (CSS 3D)：**

*   **HTML 结构:**
    *   一个外层容器（`.sphere-container`）设置 `perspective`。
    *   一个内层容器（`.sphere`）设置 `transform-style: preserve-3d;` 并应用旋转动画。
    *   `v-for` 遍历您的“随手记”列表，为每个“随手记”渲染一个文本元素（`.memo-item`）。

*   **JavaScript (Vue Component):**
    *   `onMounted` 和 `watch` (监听随手记列表变化) 时，调用一个函数 `distributeMemosOnSphere()`。
    *   `distributeMemosOnSphere()` 函数：
        *   获取所有 `.memo-item` 元素。
        *   定义球体半径 `radius`。
        *   **球面坐标分布算法：** 最常用的就是 Fibonacci Sphere (黄金螺旋点) 算法，或者简单的经纬线均匀分布。这个算法会为每个元素计算出其在球面上的 `(theta, phi)` 角度。
            *   **Fibonacci Sphere 示例 (简化):**
                ```javascript
                const N = memos.length;
                const goldenRatio = (1 + Math.sqrt(5)) / 2;
                for (let i = 0; i < N; i++) {
                    const y = 1 - (i / (N - 1)) * 2; // y goes from 1 to -1
                    const radiusAtY = Math.sqrt(1 - y * y);
                    const theta = 2 * Math.PI * i / goldenRatio; // Golden angle increment
                    const x = Math.cos(theta) * radiusAtY;
                    const z = Math.sin(theta) * radiusAtY;

                    // 将 (x, y, z) 单位向量乘以半径，并转换为 CSS transform
                    // 注意：这里 x, y, z 是单位球上的点，需要乘以半径
                    // CSS transform 的角度单位通常是 deg 或 rad
                    // const el = memoElements[i];
                    // el.style.transform = `translate3d(-50%, -50%, 0) rotateY(${...}rad) rotateX(${...}rad) translateZ(${radius}px) ...`;
                }
                ```
        *   **CSS `transform` 应用:** 为每个 `.memo-item` 设置 `style.transform`。关键在于将计算出的 3D 坐标转换为 `rotateX()`, `rotateY()`, `translateZ()` 的组合。通常需要先将元素的中心点移到原点 (`translate(-50%, -50%)`)，然后进行旋转，最后沿 Z 轴平移到球面。
        *   **新备忘在最上层：** 确保新添加的备忘在 DOM 结构中靠前，或者在计算分布时，新备忘优先分配到视野更靠前的位置（比如 Z 轴正方向，或者通过调整角度）。或者，可以在新备忘加入时，给它一个短暂的、更突出的动画或样式。

*   **CSS:**
    *   `.sphere-container { perspective: 1000px; ... }`
    *   `.sphere { transform-style: preserve-3d; animation: rotate-sphere 30s linear infinite; ... }`
    *   `.memo-item { position: absolute; top: 50%; left: 50%; backface-visibility: hidden; transition: transform 0.3s, opacity 0.3s; ... }`
    *   `@keyframes rotate-sphere { from { transform: rotateY(0deg); } to { transform: rotateY(360deg); } }`
    *   定义不同分类的颜色、字体大小等。
    *   使用 `filter: blur()` 和 `opacity` 可以让背向或远处的元素模糊、变淡，增强立体感。

**处理“随手记”的“默认分类”和“选择/添加分类”：**

1.  **数据模型：** 您的“随手记”数据对象需要包含一个 `category` 字段。
2.  **FAB 快速创建：**
    *   默认情况下，通过 FAB 快速创建的“随手记”可以不指定分类，或者有一个“默认”分类。
    *   可以在快速创建的输入框旁边提供一个非常小的图标按钮，点击后允许用户快速选择已有分类或输入新分类名。
3.  **抽屉/详情编辑：** 在完整的任务/备忘编辑抽屉中，提供一个下拉选择器或可输入标签的组件，让用户可以编辑或指定分类。
4.  **思维球视觉区分：**
    *   在 `ThinkingSphereCSS3D.vue` 中，当为每个 `.memo-item` 设置样式时，可以根据其 `category` 属性应用不同的 CSS 类或内联样式（如不同的 `background-color`）。

---

**基于您提供的图片和更新的需求，之前的 CSS 3D 预览代码是更接近的起点。** 我们需要在那个基础上进行调整和完善。

**下面是更新后的 CSS 3D 预览代码，更贴近“词云球”和您的具体要求：**

```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>备忘思维球 (CSS 3D 词云) - 效果预览</title>
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css" />
    <script src="//unpkg.com/@element-plus/icons-vue"></script>
    <style>
        body { margin: 0; font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif; background-color: #e9eef3; /* Lighter grey background */ overflow: hidden; color: #333; }
        #app { display: flex; flex-direction: column; height: 100vh; }
        .header { height: 60px; background: linear-gradient(90deg, #409EFF 0%, #79bbff 100%); /* Element Plus Blue Gradient */ color: white; display: flex; align-items: center; padding: 0 25px; font-size: 1.15em; font-weight: 500; flex-shrink: 0; box-shadow: 0 2px 8px rgba(0,0,0,0.12); }
        .main-content { flex-grow: 1; padding: 25px; overflow-y: auto; display: flex; flex-direction: column; } /* Allow main content to scroll if needed */
        .dashboard-grid { display: grid; grid-template-columns: 1fr; /* Single column for focus */ gap: 25px; max-width: 900px; margin: 0 auto; width: 100%;}
        .el-card { border-radius: 8px; border: 1px solid #dcdfe6; box-shadow: 0 2px 12px 0 rgba(0,0,0,0.06); }
        .el-card__header { font-weight: 600; font-size: 1rem; color: #303133; padding: 16px 20px; border-bottom: 1px solid #e4e7ed; }
        .card-header-icon { margin-right: 8px; vertical-align: middle; font-size: 1.2em; color: #409EFF; }

        /* FAB Styles */
        .global-fab { position: fixed; bottom: 35px; right: 35px; z-index: 1050; }
        .fab-button { width: 58px; height: 58px; font-size: 26px; background-color: #409EFF; border:none; }
        .fab-button:hover { background-color: #66b1ff; transform: scale(1.05); }

        /* Memo Drawer Styles */
        .memo-form-drawer-content { padding: 10px 25px; }
        :deep(.el-drawer__header) { margin-bottom: 12px !important; padding: 16px 25px !important; border-bottom: 1px solid #e9ecef !important; font-size: 1.1rem;}
        :deep(.el-drawer__body) { padding: 0 25px 15px 25px !important; }
        :deep(.el-drawer__footer) { padding: 12px 25px !important; border-top: 1px solid #e9ecef !important; box-shadow: 0 -1px 5px rgba(0,0,0,0.04); }

        /* Thinking Sphere CSS 3D Styles */
        .thinking-sphere-css3d-container {
            width: 100%;
            height: 500px; /* Adjust as needed */
            position: relative;
            perspective: 1000px;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: visible; /* Allow items to pop out a bit */
            background-color: #f5f7fa; /* Lighter background for card */
            border-radius: 6px;
        }
        .sphere {
            position: relative; /* Changed from absolute */
            width: 10px;
            height: 10px;
            transform-style: preserve-3d;
            animation: sphere-rotate 60s linear infinite; /* Slower more subtle rotation */
            animation-play-state: running;
        }
        .thinking-sphere-css3d-container:hover .sphere {
             /* animation-play-state: paused; Optionally pause on container hover */
        }
        .memo-item {
            position: absolute;
            top: 50%;
            left: 50%;
            padding: 8px 14px;
            background-color: #fff; /* White background for items */
            color: #4A5568; /* Darker text */
            border-radius: 4px;
            font-size: 13px; /* Adjust for readability */
            text-align: center;
            cursor: pointer;
            backface-visibility: hidden;
            transition: transform 0.3s ease-out, opacity 0.3s ease-out, box-shadow 0.2s ease-out;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1), 0 1px 2px rgba(0,0,0,0.06);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            border: 1px solid #e2e8f0;
        }
        .memo-item:hover {
            transform: scale(1.1) translateZ(10px) !important; /* Bring forward and scale */
            opacity: 1 !important;
            z-index: 100 !important; /* Ensure hover item is on top */
            box-shadow: 0 5px 15px rgba(0,0,0,0.15);
            background-color: #f0f9eb; /* Light green for hover */
            color: #529b2e;
            border-color: #b3e19d;
        }
        /* Category colors - example */
        .memo-item.category-work { background-color: rgba(64, 158, 255, 0.9); color: white; border-color: rgba(64, 158, 255, 1); }
        .memo-item.category-personal { background-color: rgba(230, 162, 60, 0.9); color: white; border-color: rgba(230, 162, 60, 1); }
        .memo-item.category-study { background-color: rgba(103, 194, 58, 0.9); color: white; border-color: rgba(103, 194, 58, 1); }
        .memo-item.category-default { background-color: rgba(144, 147, 153, 0.85); color: white; border-color: rgba(144, 147, 153, 1); }


        @keyframes sphere-rotate {
            from { transform: rotateY(0deg) rotateX(10deg); }
            to { transform: rotateY(360deg) rotateX(10deg); }
        }

        /* Quick Memo List Styles */
        .quick-memo-list-card .el-card__body { padding: 0; }
        .quick-memo-list { max-height: 450px; overflow-y: auto; }
        .quick-memo-list .memo-list-item { display: flex; justify-content: space-between; align-items: center; padding: 10px 18px; border-bottom: 1px solid #f0f2f5; font-size: 0.9rem; transition: background-color 0.2s; cursor: pointer; }
        .quick-memo-list .memo-list-item:last-child { border-bottom: none; }
        .quick-memo-list .memo-list-item:hover { background-color: #f5f7fa; }
        .quick-memo-list .memo-title { flex-grow: 1; margin-right: 12px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; color: #333; }
        .quick-memo-list .category-tag { margin-left: 8px; }
    </style>
</head>
<body>
    <div id="app">
        <header class="header">备忘思维球 (CSS 3D) 预览</header>
        <main class="main-content">
            <div class="dashboard-grid">
                <el-card shadow="hover">
                    <template #header><span><el-icon class="card-header-icon"><ElementPlusIconsVue.Opportunity /></el-icon> 备忘思维球</span></template>
                    <thinking-sphere-css3d></thinking-sphere-css3d>
                </el-card>
                <el-card shadow="hover" class="quick-memo-list-card">
                     <template #header><span><el-icon class="card-header-icon"><ElementPlusIconsVue.Tickets /></el-icon> 随手记列表</span></template>
                    <quick-memo-list></quick-memo-list>
                </el-card>
            </div>
        </main>
        <global-fab></global-fab>
        <memo-form-drawer></memo-form-drawer>
    </div>

    <script src="https://unpkg.com/vue@3/dist/vue.global.prod.js"></script>
    <script src="https://unpkg.com/element-plus"></script>
    <script src="https://unpkg.com/element-plus/dist/locale/zh-cn.min.js"></script>

    <script type="module">
        const { createApp, ref, reactive, computed, onMounted, onUnmounted, watch, nextTick } = Vue;
        const { ElMessage, ElMessageBox } = ElementPlus;
        const ElementPlusIconsVue = window.ElementPlusIconsVue; // Access globally exposed icons

        // --- Simulated Stores & Actions ---
        const appState = reactive({ isMemoDrawerOpen: false, memoDrawerMode: 'create', editingMemo: null });
        const memoState = reactive({
            quickMemos: [],
            isLoadingQuickMemos: false,
            nextMemoId: 1,
            categories: [ // Sample categories
                { id: 'work', name: '工作', color: 'category-work' },
                { id: 'personal', name: '个人', color: 'category-personal' },
                { id: 'study', name: '学习', color: 'category-study' },
                { id: 'default', name: '默认', color: 'category-default' },
            ]
        });
        const appActions = {
            openMemoDrawer: (mode = 'create', memo = null) => {
                appState.memoDrawerMode = mode;
                appState.editingMemo = memo ? { ...memo } : null;
                appState.isMemoDrawerOpen = true;
            },
            closeMemoDrawer: () => { appState.isMemoDrawerOpen = false; appState.editingMemo = null; }
        };
        const memoActions = {
            fetchQuickMemos: async (limit = 30) => {
                if (memoState.isLoadingQuickMemos) return;
                memoState.isLoadingQuickMemos = true; memoState.quickMemos = [];
                await new Promise(resolve => setTimeout(resolve, 600));
                const sampleTitles = ["项目周会纪要", "新功能点子A", "待购清单", "技术博客草稿", "健身计划", "周末出游安排", "学习React Native", "年度总结大纲", "客户电话回访", "代码重构思考"];
                for (let i = 0; i < limit; i++) {
                    memoState.quickMemos.push({
                        id: String(memoState.nextMemoId++),
                        title: sampleTitles[i % sampleTitles.length] + ` #${memoState.nextMemoId -1}`,
                        category: memoState.categories[i % memoState.categories.length].id,
                        createdAt: new Date(Date.now() - Math.random() * 1000 * 3600 * 24 * 7).toISOString() // Random creation within last week
                    });
                }
                memoState.isLoadingQuickMemos = false;
            },
            addQuickMemo: async (memoData) => {
                await new Promise(resolve => setTimeout(resolve, 150));
                const newMemo = {
                    id: String(memoState.nextMemoId++),
                    title: memoData.title || '新的备忘',
                    category: memoData.category || 'default',
                    createdAt: new Date().toISOString()
                };
                memoState.quickMemos.unshift(newMemo);
                ElMessage.success('备忘已添加！'); return newMemo;
            },
            updateQuickMemo: async (memoData) => {
                 await new Promise(resolve => setTimeout(resolve, 150));
                const memoIndex = memoState.quickMemos.findIndex(m => m.id === memoData.id);
                if (memoIndex !== -1) {
                    memoState.quickMemos.splice(memoIndex, 1, { ...memoState.quickMemos[memoIndex], ...memoData });
                    ElMessage.success('备忘已更新！');
                    return memoState.quickMemos[memoIndex];
                }
                ElMessage.error('更新备忘失败'); return null;
            }
        };

        // --- Components ---
        const GlobalFAB = {
            template: `<div class="global-fab"><el-button type="primary" :icon="Plus" circle size="large" @click="openDrawer" class="fab-button" /></div>`,
            setup() { const { Plus } = ElementPlusIconsVue; return { openDrawer: () => appActions.openMemoDrawer('create'), Plus }; }
        };

        const MemoFormDrawer = {
            template: `<el-drawer :model-value="appState.isMemoDrawerOpen" :title="appState.memoDrawerMode === 'create' ? '新增随手记' : '编辑随手记'" direction="rtl" size="400px" @update:modelValue="handleUpdateModelValue" :before-close="cancel" destroy-on-close>
                <div class="memo-form-drawer-content">
                    <el-form ref="formRef" :model="formData" :rules="formRules" label-position="top">
                        <el-form-item label="内容" prop="title">
                            <el-input v-model="formData.title" type="textarea" :rows="4" placeholder="记录您的想法、任务或灵感..." clearable maxlength="200" show-word-limit />
                        </el-form-item>
                        <el-form-item label="分类" prop="category">
                            <el-select v-model="formData.category" placeholder="选择或创建分类" filterable allow-create default-first-option style="width:100%">
                                <el-option v-for="cat in memoState.categories" :key="cat.id" :label="cat.name" :value="cat.id"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-form>
                </div>
                <template #footer>
                    <div style="flex: auto; text-align: right;">
                        <el-button @click="cancel">取消</el-button>
                        <el-button type="primary" @click="confirmSave" :loading="isSaving">{{ isSaving ? '保存中...' : (appState.memoDrawerMode === 'create' ? '快速保存' : '更新') }}</el-button>
                    </div>
                </template>
            </el-drawer>`,
            setup() {
                const formRef = ref(null); const isSaving = ref(false);
                const initialFormData = () => ({ id: null, title: '', category: 'default' });
                const formData = reactive(initialFormData());
                const formRules = { title: [{ required: true, message: '请输入内容', trigger: 'blur' }] };
                const handleUpdateModelValue = (value) => { if (!value) appActions.closeMemoDrawer(); };
                const resetForm = () => { Object.assign(formData, initialFormData()); if(formRef.value) formRef.value.clearValidate(); };
                const cancel = () => { appActions.closeMemoDrawer(); };
                watch(() => appState.isMemoDrawerOpen, (isOpen) => {
                    resetForm();
                    if (isOpen && appState.memoDrawerMode === 'edit' && appState.editingMemo) {
                        Object.assign(formData, appState.editingMemo);
                    } else if (isOpen && appState.memoDrawerMode === 'create') {
                        formData.category = 'default'; // Default category for new memos
                    }
                }, { immediate: true });
                const confirmSave = async () => {
                    if (!formRef.value) return;
                    await formRef.value.validate(async (valid) => {
                        if (valid) {
                            isSaving.value = true;
                            try {
                                if (appState.memoDrawerMode === 'create') { await memoActions.addQuickMemo({ ...formData }); }
                                else if (appState.memoDrawerMode === 'edit' && formData.id) { await memoActions.updateQuickMemo({ ...formData }); }
                                appActions.closeMemoDrawer();
                            } catch (e) { console.error("Save error", e); ElMessage.error("保存失败"); }
                            finally { isSaving.value = false; }
                        } else { ElMessage.warning('请输入内容'); }
                    });
                };
                return { appState, memoState, formRef, formData, formRules, isSaving, handleUpdateModelValue, cancel, confirmSave };
            }
        };

        const ThinkingSphereCSS3D = {
            template: `
                <div ref="sphereContainerRef" class="thinking-sphere-css3d-container">
                    <div v-if="isLoading" class="loading-placeholder"><el-icon class="is-loading"><Loading /></el-icon> <span>构建思维球...</span></div>
                    <div v-else-if="memos.length === 0" class="empty-placeholder"><el-icon><Notebook /></el-icon> <span>思维球空空如也</span><span>点击右下角 + 快速记录</span></div>
                    <div v-else ref="sphereElementRef" class="sphere">
                        <div v-for="(memo, index) in memos"
                             :key="memo.id"
                             class="memo-item"
                             :class="getCategoryClass(memo.category)"
                             :style="memoStyles[index]"
                             @click="handleMemoClick(memo)"
                             :title="memo.title">
                            {{ memo.title.length > 15 ? memo.title.substring(0, 12) + '...' : memo.title }}
                        </div>
                    </div>
                </div>
            `,
            setup() {
                const sphereContainerRef = ref(null);
                const sphereElementRef = ref(null); // Renamed from sphereRef to avoid conflict
                const store = memoState;
                const isLoading = computed(() => store.isLoadingQuickMemos);
                const memos = computed(() => store.quickMemos);
                const memoStyles = ref([]);
                const radius = 180; // Sphere radius

                const positionMemoItems = () => {
                    const N = memos.value.length;
                    if (N === 0 || !sphereElementRef.value) { memoStyles.value = []; return; }

                    const newStyles = [];
                    const phi = Math.PI * (Math.sqrt(5) - 1); // Golden angle

                    for (let i = 0; i < N; i++) {
                        const y_norm = 1 - (i / (N - 1)) * 2; // y normalized: -1 to 1
                        const radiusAtY = Math.sqrt(1 - y_norm * y_norm);
                        const theta_angle = phi * i;

                        // Determine rotation angles for the face itself to look "outwards"
                        // These are complex and might need fine-tuning or a library for perfect orientation.
                        // For simplicity, we primarily focus on position and basic orientation.
                        const rotY_face = theta_angle; // Rotation around Y axis
                        const rotX_face = Math.acos(y_norm) - Math.PI / 2; // Rotation around X axis

                        // Final transform:
                        // 1. translate(-50%, -50%): Center the element's origin for rotation.
                        // 2. rotateY(rotY_face): Orient along the sphere's longitude.
                        // 3. rotateX(rotX_face): Orient along the sphere's latitude.
                        // 4. translateZ(radius): Push out to the sphere's surface.
                        // 5. (Optional) rotateX(angle) / rotateY(angle): Make face point outwards if not naturally.
                        //    The default transform-origin is center, so it might already face outwards.
                        const transform = `translate(-50%, -50%) rotateY(${rotY_face}rad) rotateX(${rotX_face}rad) translateZ(${radius}px)`;
                        newStyles.push({ transform, opacity: 0.7 + Math.random() * 0.3 }); // Add random opacity for depth
                    }
                    memoStyles.value = newStyles;
                };

                const handleMemoClick = (memo) => { appActions.openMemoDrawer('edit', memo); };
                const getCategoryClass = (categoryId) => {
                    const cat = store.categories.find(c => c.id === categoryId);
                    return cat ? cat.color : 'category-default';
                };

                watch(memos, () => { nextTick(positionMemoItems); }, { deep: true, immediate: true });
                onMounted(() => { if(memos.value.length === 0) memoActions.fetchQuickMemos(); });
                
                const { Loading, Notebook, Opportunity, Tickets } = ElementPlusIconsVue;
                return { sphereContainerRef, sphereElementRef, isLoading, memos, memoStyles, handleMemoClick, getCategoryClass, Loading, Notebook, Opportunity, Tickets };
            }
        };

        const QuickNoteList = { /* ... Same as previous, just s/task/memo/g and display category ... */
            template: `<div class="quick-memo-list thin-scrollbar" v-loading="isLoading"><div v-if="memos.length > 0"><div v-for="memo in memos" :key="memo.id" class="memo-list-item" @click="viewMemo(memo)"><span class="memo-title" :title="memo.title">{{ memo.title }}</span><div><el-tag :class="getCategoryClass(memo.category)" size="small" effect="dark" style="border:none; color:white; margin-right: 5px;">{{ getCategoryName(memo.category) }}</el-tag></div></div></div><el-empty v-else description="暂无随手记" /></div>`,
            setup() {
                const store = memoState;
                const isLoading = computed(() => store.isLoadingQuickMemos);
                const memos = computed(() => store.quickMemos);
                const getCategoryClass = (categoryId) => { const cat = store.categories.find(c => c.id === categoryId); return cat ? cat.color : 'category-default'; };
                const getCategoryName = (categoryId) => { const cat = store.categories.find(c => c.id === categoryId); return cat ? cat.name : '默认'; };
                const viewMemo = (memo) => { if (memo && memo.id) appActions.openMemoDrawer('edit', memo); };
                return { memos, isLoading, viewMemo, getCategoryClass, getCategoryName };
            }
        };

        const app = createApp({
            setup() { onMounted(() => { if(memoState.quickMemos.length === 0) memoActions.fetchQuickMemos(); }); return { appState, memoState }; }
        });
        app.use(ElementPlus, { locale: ElementPlusLocaleZhCn });
        if (ElementPlusIconsVue) { for (const [key, component] of Object.entries(ElementPlusIconsVue)) { app.component(key, component); } }
        app.component('GlobalFab', GlobalFAB);
        app.component('MemoFormDrawer', MemoFormDrawer); // Renamed
        app.component('ThinkingSphereCss3d', ThinkingSphereCSS3D);
        app.component('QuickNoteList', QuickNoteList);
        app.mount('#app');
    </script>
</body>
</html>
```

**代码更新说明:**

1.  **重命名:** "Task" 相关的内容已重命名为 "Memo" (备忘)，更符合“随手记”的场景。
2.  **ThinkingSphereCSS3D 组件:**
    *   **HTML 结构:** 基本不变。
    *   **`positionMemoItems()` 核心逻辑:**
        *   这是实现“词云球”分布的关键。我使用了 **Fibonacci Sphere (黄金螺旋点)** 算法来尝试在球面上均匀分布元素。这个算法比简单的经纬度划分效果更好，分布更自然。
        *   计算出每个备忘元素在单位球面上的 `(x, y, z)` 坐标。
        *   然后，通过 CSS `transform: rotateY() rotateX() translateZ()` 将每个备忘元素定位到球面上，并使其**朝向球心**（通常 `backface-visibility: hidden` 配合正确的旋转顺序可以实现面向外部）。
            *   **注意:** 精确计算每个元素使其文本始终朝向观察者（不发生透视变形或上下颠倒）是 CSS 3D 的一个难点。这里的实现会使文本附着在球面上，随着球体旋转，文本方向也会改变。如果需要文本始终正对用户，需要更复杂的 `transform` 计算（可能需要反向旋转元素自身以抵消球体旋转带来的影响），或者考虑使用 SVG 文本并进行 3D 变换。
        *   **`taskStyles` (现为 `memoStyles`)** 被用来存储每个备忘元素的动态样式 (主要是 `transform`)。
    *   **样式 (`.memo-item`):**
        *   背景色改为了更适合文本的白色或浅色，并为不同分类添加了背景色（`.category-work` 等）。
        *   增加了 `white-space: normal;` 和 `overflow: hidden; text-overflow: ellipsis;` 来处理可能过长的备忘标题（但为了球体效果，通常建议标题简短）。实际上，对于球体上的标签，`white-space: nowrap;` 可能更常见，然后用 `text-overflow: ellipsis;` 截断。
        *   **悬停效果:** 放大并尝试拉近（`translateZ(10px)`），增加 `z-index`。
    *   **旋转动画 (`sphere-rotate`):** 调整了旋转速度和轴向，使其更平缓。
    *   **交互:** 点击备忘元素打开编辑抽屉。
3.  **MemoFormDrawer 组件:**
    *   添加了“分类”选择器，允许用户选择或**创建新分类** (`allow-create`, `filterable`, `default-first-option`)。
    *   保存时将分类信息一并保存。
4.  **QuickNoteList 组件:**
    *   显示备忘标题和其分类标签。
    *   点击列表项也打开编辑抽屉。
5.  **模拟数据 (`memoActions.fetchQuickMemos`):**
    *   生成的备忘数据现在包含 `category` 字段。
    *   增加了更多样化的备忘标题。
6.  **配色和样式调整:**
    *   整体配色方案微调，更接近Element Plus的默认风格，同时给思维球一个独特的背景。
    *   卡片、抽屉等组件的样式细节调整，提升质感。

**预览效果预期：**

*   仪表盘上会有一个区域显示“备忘思维球”。
*   球体上会分布着您快速记录的备忘文本（以卡片或标签形式）。
*   **新添加的备忘会动态出现在球体上，并位于视觉上相对靠前的位置** (因为 `unshift` 使得新数据在数组头部，Fibonacci Sphere 算法通常会先填充球体的一极)。
*   整个球体会**缓慢旋转**。
*   鼠标悬停在球体上时，**旋转会暂停**（或减慢，CSS 可配置）。
*   鼠标悬停在某个备忘文本上，该文本会**高亮并放大**。
*   **点击某个备忘文本**，会打开右侧的抽屉，并填充该备忘的内容，供您编辑。
*   **分类颜色：** 不同分类的备忘在球上可以用不同的背景色显示。
*   **随手记列表**会同步显示这些备忘，点击列表项也会打开编辑抽屉。

这个版本应该非常接近您给出的“词云球”参考图的视觉效果和交互逻辑了，并且完全使用 CSS 3D 和 Vue 实现，避免了引入重度 3D 库。请您再次尝试运行这段代码。


Okay, let's break this down into a step-by-step guide to get your "随手记" (QuickMemo) components visible and then integrate a Cek-smart-inspired UI.

**Phase 1: Making "随手记" Components Visible**

The most likely reason your `ThinkingSphereCSS3D.vue` and `QuickMemoList.vue` are not showing (besides the FAB) is due to data loading issues or conditional rendering within those components based on data.

**Step 1: Verify API Endpoints and Backend Readiness**

Your `quickMemoStore.js` uses these API calls:

*   `getQuickMemos` (GET `/api/v2/quick-memos`)
*   `addQuickMemo` (POST `/api/v2/quick-memos`)
*   `updateQuickMemo` (PUT `/api/v2/quick-memos/{id}`)
*   `deleteQuickMemo` (DELETE `/api/v2/quick-memos/{id}`)
*   `getMemoCategories` (GET `/api/v2/quick-memo-categories`)
*   `addMemoCategory` (POST `/api/v2/quick-memo-categories`)

**Correction for API Paths in `quickMemoStore.js`:**
Your `request.js` already sets `baseURL: '/api'`. So, in `quickMemo.js`, the URLs should *not* start with `/api` again.

**Modify `frontend/src/api/quickMemo.js`:**

```javascript
// File: frontend/src/api/quickMemo.js
// Description: API service for Quick Memos (随手记).

import request from '@/utils/request'

/**
 * Fetches quick memos from the server.
 * @param {object} params Query parameters for fetching memos (e.g., limit, offset, category)
 */
export function getQuickMemos(params) {
  console.log('[API] getQuickMemos params:', params); // Debug log
  return request({
    url: '/v2/quick-memos', // CORRECTED: Removed leading /api
    method: 'get',
    params
  })
}

/**
 * Adds a new quick memo.
 * @param {object} data The quick memo data to add.
 * Expected data: { title: string, categoryId?: string, content?: string } // Note: categoryId from store
 */
export function addQuickMemo(data) {
  console.log('[API] addQuickMemo data:', data); // Debug log
  return request({
    url: '/v2/quick-memos', // CORRECTED: Removed leading /api
    method: 'post',
    data
  })
}

/**
 * Updates an existing quick memo.
 * @param {string|number} id The ID of the memo to update.
 * @param {object} data The quick memo data to update.
 */
export function updateQuickMemo(id, data) {
  console.log('[API] updateQuickMemo id:', id, 'data:', data); // Debug log
  return request({
    url: `/v2/quick-memos/${id}`, // CORRECTED: Removed leading /api
    method: 'put',
    data
  })
}

/**
 * Deletes a quick memo.
 * @param {string|number} id The ID of the memo to delete.
 */
export function deleteQuickMemo(id) {
  console.log('[API] deleteQuickMemo id:', id); // Debug log
  return request({
    url: `/v2/quick-memos/${id}`, // CORRECTED: Removed leading /api
    method: 'delete'
  })
}

/**
 * Fetches quick memo categories.
 */
export function getMemoCategories() {
  console.log('[API] getMemoCategories'); // Debug log
  return request({
    url: '/v2/quick-memo-categories', // CORRECTED: Removed leading /api
    method: 'get'
  })
}

/**
 * Adds a new quick memo category.
 * @param {object} data The category data to add.
 * Expected data: { name: string, color?: string }
 */
export function addMemoCategory(data) {
  console.log('[API] addMemoCategory data:', data); // Debug log
  return request({
    url: '/v2/quick-memo-categories', // CORRECTED: Removed leading /api
    method: 'post',
    data
  })
}
```

**Action:**
1.  **Ensure your backend (running on `http://0.0.0.0:5001`) has these V2 endpoints ready:**
    *   `/v2/quick-memos` (GET, POST)
    *   `/v2/quick-memos/{id}` (PUT, DELETE)
    *   `/v2/quick-memo-categories` (GET, POST)
2.  **Verify Backend Response Structure:**
    *   The store expects responses like `{ success: true, data: [...] }` or `{ success: true, data: {...} }`.
    *   `getQuickMemos` should return an array of memo objects in `response.data`.
    *   `getMemoCategories` should return an array of category objects in `response.data`. Each category object should ideally have `id`, `name`, and a `color` (which is used as a CSS class name like `category-work`).

**Step 2: Ensure Store Actions are Called and Data is Loaded**

The components `ThinkingSphereCSS3D.vue` and `QuickMemoList.vue` already call `fetchQuickMemos` and `fetchCategories` in their `onMounted` hooks. This is good.

1.  **Open your browser's developer console (F12).**
2.  Navigate to your dashboard page (`/main/dashboard`).
3.  **Check the console for:**
    *   The `[API] getQuickMemos` and `[API] getMemoCategories` logs from `quickMemo.js`.
    *   Network requests to `/api/v2/quick-memos` and `/api/v2/quick-memo-categories`. Verify they are successful (status 200) and the response data is as expected.
    *   Any JavaScript errors.
4.  **Check Pinia store state in console:**
    `window.__pinia.state.value.quickMemo.quickMemos`
    `window.__pinia.state.value.quickMemo.categories`
    `window.__pinia.state.value.quickMemo.isLoadingQuickMemos`
    `window.__pinia.state.value.quickMemo.isLoadingCategories`
    Ensure the arrays are populated and loading flags are `false` after loading.

**Step 3: Debug Component Rendering**

If data is loading correctly into the store, but components are still not visible:

1.  **In `ThinkingSphereCSS3D.vue`:**
    *   Temporarily add a `<div>Hello from Sphere</div>` at the very top of its `<template>` to see if the component itself is rendering at all.
    *   Check the `v-else-if="memosToDisplay.length === 0"` condition. If `memosToDisplay` is empty, it will show the "思维球空空如也" message.
2.  **In `QuickMemoList.vue`:**
    *   Add a `<div>Hello from List</div>` at the top of its `<template>`.
    *   Check the `v-if="quickMemoStore.quickMemos.length > 0"` condition. If the array is empty, it shows `el-empty`.

**Step 4: Address Potential CSS Issues (Minimal for now, focus on visibility)**

*   Ensure the parent `el-col` and `el-card` in `DashboardView.vue` don't have styles that would hide their children. The current structure looks okay for visibility.

**Phase 2: Integrating Cek-smart UI Style**

We'll assume your "随手记" components are now visible and have data.

**Step 1: Update Global Styles and Variables**

1.  **`frontend/src/styles/variables.scss`:**
    You already have a new theme defined here. Ensure this is the active theme being used. The Cek-smart theme uses light blues, whites, and an orange accent. Your current theme in `variables.scss` is:
    ```scss
    // Core Palette: Blue & Orange Accent
    --primary-color: #2196F3;          /* Blue 500 */
    --accent-color: #F57C00;           /* Orange 700 */
    // ... other colors ...
    --bg-color: #F5F7FA;              /* Cooler light gray */
    --header-bg: var(--primary-color-dark); /* Dark Blue Header */
    --sidebar-bg: #FFFFFF;            /* White Sidebar */
    --card-bg: #FFFFFF;               /* White Cards */
    ```
    This is a good starting point. You can fine-tune these colors to more closely match Cek-smart if desired (e.g., Cek-smart's primary might be a bit lighter, accent orange might be slightly different).

2.  **`frontend/src/styles/global.scss`:**
    *   Ensure `font-family` matches Cek-smart (e.g., Inter or a similar sans-serif). Your current `variables.scss` has a good `font-family-base`.
    *   Apply `background-color: var(--bg-color);` to `html, body`.
    *   **Element Plus Overrides:** This is where you'd put styles to make Element Plus components match Cek-smart.
        ```scss
        // frontend/src/styles/global.scss
        // ... (your existing global styles) ...

        /* Element Plus Overrides to match Cek-smart style */
        :root { /* Ensure these are applied globally */
          --el-border-radius-base: 6px; // Cek-smart uses slightly rounded corners
          --el-box-shadow-light: 0 2px 12px 0 rgba(0, 0, 0, 0.06); // Softer shadows
        }

        .el-card {
          border-radius: var(--el-border-radius-large, 10px); // Larger radius for cards
          box-shadow: var(--shadow-light, 0 1px 3px rgba(0,0,0,0.08), 0 1px 2px rgba(0,0,0,0.05));
          border: 1px solid var(--border-color-light, #ECEFF1); // Light border for cards
          background-color: var(--card-bg, #FFFFFF);
        }

        .el-button--primary {
          background-color: var(--accent-color); // Use accent for primary buttons
          border-color: var(--accent-color);
          &:hover, &:focus {
            background-color: var(--accent-color-light); // A lighter shade for hover
            border-color: var(--accent-color-light);
          }
        }
        // Add more overrides for .el-input, .el-select, .el-menu-item etc. as needed
        // to match Cek-smart's look & feel (e.g., input field styles, sidebar item styles).
        ```

**Step 2: Style Layout Components (`DefaultLayout.vue`)**

Your `DefaultLayout.vue` already uses CSS variables from `variables.scss`.

*   **Header:**
    *   `background-color: var(--header-bg);` -> Cek-smart's header is light. Consider changing `--header-bg` in `variables.scss` to `var(--card-bg)` or a very light gray.
    *   `color: var(--text-color-on-primary);` -> If header is light, text should be `var(--text-color)`.
*   **Sidebar:**
    *   `background-color: var(--sidebar-bg);` -> This is already white, which matches.
    *   Menu item colors:
        *   `text-color="#bfcbd9"` (default) -> `var(--sidebar-text-color)`
        *   `active-text-color="#409EFF"` (active) -> `var(--sidebar-active-text-color)` (make this your orange accent)
        *   Hover states need to be adjusted in the SCSS.

**Modified `DefaultLayout.vue` `<style>` (Illustrative for sidebar menu items):**

```scss
// frontend/src/layouts/DefaultLayout.vue <style lang="scss" scoped>
// ... (other styles) ...

.sidebar {
  // ...
  .el-menu-vertical {
    border-right: none;
    background-color: var(--sidebar-bg) !important;

    .el-menu-item,
    :deep(.el-sub-menu__title) { // Use :deep for child components
      color: var(--sidebar-text-color, var(--text-color-secondary)) !important;
      &:hover {
        background-color: var(--sidebar-hover-bg, var(--hover-bg-color)) !important;
        color: var(--sidebar-active-text-color, var(--accent-color)) !important; // Use accent for hover
        .el-icon {
          color: var(--sidebar-active-text-color, var(--accent-color)) !important;
        }
      }
      .el-icon {
        color: var(--sidebar-text-color, var(--text-color-secondary));
      }
    }

    .el-menu-item.is-active {
      background-color: var(--sidebar-active-bg, transparent) !important; // Can be transparent or very light accent
      color: var(--sidebar-active-text-color, var(--accent-color)) !important; // Accent color for active
      border-left: 3px solid var(--sidebar-active-text-color, var(--accent-color)); // Accent color border
      .el-icon {
        color: var(--sidebar-active-text-color, var(--accent-color)) !important;
      }
    }

    :deep(.el-sub-menu.is-active > .el-sub-menu__title) {
      color: var(--sidebar-active-text-color, var(--accent-color)) !important;
      .el-icon {
        color: var(--sidebar-active-text-color, var(--accent-color)) !important;
      }
    }
  }
}

// ...
</style>
```

**Step 3: Style Dashboard Cards (`DashboardView.vue`)**

Your `DashboardView.vue` has cards for "思维球" and "随手记列表".

```html
<!-- frontend/src/views/DashboardView.vue -->
<el-row :gutter="20" class="dashboard-row">
  <el-col :xs="24" :sm="24" :md="12" :lg="10" :xl="10">
    <el-card shadow="hover" class="dashboard-card quickmemo-sphere-card"> <!-- Added a specific class -->
      <template #header>
        <div class="card-header">
          <span><el-icon style="margin-right: 8px;"><Opportunity /></el-icon>备忘思维球</span>
        </div>
      </template>
      <ThinkingSphereCSS3D />
    </el-card>
  </el-col>
  <el-col :xs="24" :sm="24" :md="12" :lg="14" :xl="14">
    <el-card shadow="hover" class="dashboard-card quickmemo-list-card">
      <template #header>
        <div class="card-header">
          <span><el-icon style="margin-right: 8px;"><Tickets /></el-icon>随手记列表</span>
        </div>
      </template>
      <QuickMemoList />
    </el-card>
  </el-col>
</el-row>
```

```scss
// frontend/src/views/DashboardView.vue <style scoped>
// ... (existing styles) ...

.dashboard-card { // General card styling for dashboard items
  margin-bottom: 20px;
  border-radius: var(--el-border-radius-large, 10px); // Cek-smart like radius
  border: 1px solid var(--border-color-light, #ECEFF1);
  background-color: var(--card-bg, #FFFFFF);
  box-shadow: var(--shadow-light);
  transition: box-shadow 0.3s ease;
}

.dashboard-card:hover {
  box-shadow: var(--shadow-medium);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600; // Bolder header text
  color: var(--text-color);
  span {
    display: flex;
    align-items: center;
  }
}

// Specific styling for quick memo cards
.quickmemo-sphere-card {
  // Ensure the ThinkingSphereCSS3D component fits well
  :deep(.thinking-sphere-css3d-container) { // Use :deep if styles are scoped in child
    min-height: 350px; // Adjust as needed
    background-color: transparent; // Let card background show
  }
}

.quickmemo-list-card {
  :deep(.el-card__body) { // If QuickMemoList is directly in card body
    padding: 0; // Remove card body padding if list has its own
  }
  :deep(.quick-memo-list) {
    max-height: 350px; // Adjust height
  }
}
</style>
```

**Step 4: Style `ThinkingSphereCSS3D.vue`**

```html
<!-- frontend/src/components/QuickMemo/ThinkingSphereCSS3D.vue -->
<template>
  <div ref="sphereContainerRef" class="thinking-sphere-css3d-container">
    <!-- ... loading/empty states ... -->
    <div v-else ref="sphereElementRef" class="sphere">
      <div
        v-for="(memo, index) in memosToDisplay"
        :key="memo.id"
        class="memo-item"
        :style="[memoStyles[index], getCategoryStyle(memo.categoryId)]" <!-- Apply dynamic style -->
        @click="handleMemoClick(memo)"
        :title="memo.title"
      >
        {{ memo.title.length > 15 ? memo.title.substring(0, 12) + '...' : memo.title }}
      </div>
    </div>
  </div>
</template>
```

```javascript
// frontend/src/components/QuickMemo/ThinkingSphereCSS3D.vue <script setup>
// ...
const getCategoryStyle = (categoryId) => {
  const category = quickMemoStore.getCategoryById(categoryId);
  // Example: if category object has a hex color
  // return category && category.hexColor ? { backgroundColor: category.hexColor, color: '#fff', borderColor: category.hexColor } : {};
  // If using CSS classes from store as before:
  // return {}; // The class is already applied via :class
  // For Cek-smart, let's assume distinct colors for tags
  const colorMap = {
    'work': { backgroundColor: 'rgba(59, 130, 246, 0.8)', color: 'white', borderColor: 'rgba(59, 130, 246,1)'}, // Blue
    'personal': { backgroundColor: 'rgba(245, 158, 11, 0.8)', color: 'white', borderColor: 'rgba(245, 158, 11,1)'}, // Amber
    'study': { backgroundColor: 'rgba(16, 185, 129, 0.8)', color: 'white', borderColor: 'rgba(16, 185, 129,1)'}, // Emerald
    'default': { backgroundColor: 'rgba(108, 117, 125, 0.8)', color: 'white', borderColor: 'rgba(108, 117, 125,1)'}, // Gray
  };
  return (category && colorMap[category.id]) || colorMap['default'];
};
// ...
</script>
```

```scss
// frontend/src/components/QuickMemo/ThinkingSphereCSS3D.vue <style scoped>
.thinking-sphere-css3d-container {
  width: 100%;
  min-height: 300px;
  height: 380px; // Adjusted for better fit
  position: relative;
  perspective: 1000px;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden; // Changed from visible to hidden for cleaner card look
  background-color: var(--card-bg); // Use card background
  border-radius: var(--el-border-radius-base);
}

.sphere {
  // ... (no change, animation is fine)
}

.memo-item {
  position: absolute;
  top: 50%;
  left: 50%;
  padding: 6px 12px; // Slightly smaller padding
  // background-color: var(--el-color-primary-light-9); // Very light theme color for items
  // color: var(--el-color-primary);
  border-radius: var(--el-border-radius-small);
  font-size: 12px; // Smaller font
  // ... (rest of the styles)
  // border: 1px solid var(--el-color-primary-light-5);
  opacity: 0.85; // Default opacity
}

.memo-item:hover {
  transform: scale(1.15) translateZ(20px) !important;
  opacity: 1 !important;
  z-index: 100 !important;
  box-shadow: var(--shadow-medium);
  // background-color: var(--accent-color-light); // Use accent for hover
  // color: var(--text-color-on-accent);
  // border-color: var(--accent-color);
}

// Remove hardcoded category classes if using dynamic style binding
// .category-work { ... }
// .category-personal { ... }
// ...
</style>
```

**Step 5: Style `QuickMemoList.vue`**

```scss
// frontend/src/components/QuickMemo/QuickMemoList.vue <style scoped>
.quick-memo-list {
  max-height: 360px; // Adjust as needed
  overflow-y: auto;
  // background-color: var(--card-bg); // List items will have their own bg
}

.memo-list-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px; // Adjusted padding
  border-bottom: 1px solid var(--border-color-light);
  font-size: 0.88rem; // Slightly smaller
  transition: background-color 0.2s;
  cursor: pointer;
  color: var(--text-color);
}

.memo-list-item:last-child {
  border-bottom: none;
}

.memo-list-item:hover {
  background-color: var(--hover-bg-color); // Use theme hover color
}

.memo-title {
  flex-grow: 1;
  margin-right: 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  // color: var(--text-color); // Inherits from .memo-list-item
}

.el-tag { // Style for category tags inside the list
  // Ensure el-tag within the list inherits card context or define specific styles
  // background-color and color will be set by the getCategoryStyle or CSS classes
  border-radius: var(--el-border-radius-small);
}
</style>
```

**Step 6: Style `GlobalFAB.vue`**

```scss
// frontend/src/components/QuickMemo/GlobalFAB.vue <style scoped>
.global-fab {
  position: fixed;
  bottom: 30px; // Adjust as needed
  right: 30px;  // Adjust as needed
  z-index: 1050;
}

.fab-button {
  width: 56px; // Standard FAB size
  height: 56px;
  font-size: 24px;
  // The el-button type="primary" should now pick up the --accent-color
  // from global.scss override. If not, explicitly set:
  // background-color: var(--accent-color);
  // border-color: var(--accent-color);
  box-shadow: var(--shadow-medium);
}

.fab-button:hover {
  // background-color: var(--accent-color-light); // Lighter accent for hover
  // border-color: var(--accent-color-light);
  transform: scale(1.05);
}
</style>
```

**Step 7: Style `MemoFormDrawer.vue`**

The `:deep` selectors for `el-drawer__header`, `el-drawer__body`, `el-drawer__footer` should largely respect the theme variables set for Element Plus.

```scss
// frontend/src/components/QuickMemo/MemoFormDrawer.vue <style scoped>
.memo-form-drawer-content {
  padding: 10px 20px; // Adjust padding
}

:deep(.el-drawer__header) {
  margin-bottom: 15px !important;
  padding: 16px 20px !important; // Adjust padding
  border-bottom: 1px solid var(--border-color-light) !important;
  // Title color should be var(--text-color) or var(--header-text-color) if defined
  .el-drawer__title {
      color: var(--text-color);
      font-weight: 600;
  }
}
:deep(.el-drawer__body) {
  padding: 0 20px 15px 20px !important; // Adjust padding
}
:deep(.el-drawer__footer) {
  padding: 12px 20px !important; // Adjust padding
  border-top: 1px solid var(--border-color-light) !important;
  box-shadow: 0 -2px 5px rgba(0,0,0,0.04); // Softer shadow
}

// Ensure form elements like el-input, el-select also pick up theme styles
// (usually handled by Element Plus's own CSS variable usage)
</style>
```

**Final Steps & Considerations:**

1.  **Backup Your Code:** Before making large-scale style changes, always back up your project.
2.  **Incremental Changes:** Apply these changes step-by-step and test frequently in your browser.
3.  **Browser Developer Tools:** Use the "Inspect Element" feature extensively to see which styles are being applied and to experiment with CSS changes live.
4.  **Theme Consistency:** After styling the "随手记" components, review other parts of your application (Asset list, Location management, etc.) to ensure they also align with the new Cek-smart inspired theme. Apply `var(--card-bg)`, `var(--border-color-light)`, etc., to other `el-card` components and adjust text colors as needed.
5.  **Category Colors:** In `quickMemoStore.js`, the `getCategoryClass` getter returns CSS class names like `category-work`. You need to define these CSS classes in `global.scss` or `variables.scss` using your theme colors, or modify `getCategoryClass` to return dynamic style objects if your categories (and their colors) come from the backend.
    *   For example, in `variables.scss` or a new `_categories.scss` imported by `global.scss`:
        ```scss
        .category-work { background-color: var(--el-color-primary-light-5); color: var(--el-color-primary); border: 1px solid var(--el-color-primary-light-3); }
        .category-personal { background-color: var(--el-color-success-light-5); color: var(--el-color-success); border: 1px solid var(--el-color-success-light-3); }
        /* ... and so on for other categories ... */
        .category-default { background-color: var(--el-color-info-light-5); color: var(--el-color-info); border: 1px solid var(--el-color-info-light-3); }
        ```
    *   Or, if colors are dynamic from the backend and stored in `category.hexColor`:
        In `ThinkingSphereCSS3D.vue`, your `getCategoryStyle` function would directly return `{ backgroundColor: category.hexColor, color: 'white', ... }`.
        In `QuickMemoList.vue`, you'd bind `:style="getCategoryStyle(memo.categoryId)"` to the `el-tag`.

This guide provides a comprehensive approach. Start with Phase 1 to ensure functionality, then move to Phase 2 for styling. Good luck!