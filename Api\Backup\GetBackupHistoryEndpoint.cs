// IT资产管理系统 - 备份历史API
// 文件路径: /Api/Backup/GetBackupHistoryEndpoint.cs
// 功能: 获取备份历史列表的API端点

using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using ItAssetsSystem.Core.Backup;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace ItAssetsSystem.Api.Backup
{
    [ApiController]
    [Route("api/backup/history")]
    public class GetBackupHistoryEndpoint : ControllerBase
    {
        private readonly IBackupService _backupService;
        private readonly ILogger<GetBackupHistoryEndpoint> _logger;

        public GetBackupHistoryEndpoint(
            IBackupService backupService,
            ILogger<GetBackupHistoryEndpoint> logger)
        {
            _backupService = backupService;
            _logger = logger;
        }

        /// <summary>
        /// 获取备份历史记录
        /// </summary>
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> GetBackupHistoryAsync()
        {
            _logger.LogInformation("获取备份历史记录");
            
            try
            {
                var backupList = await _backupService.GetBackupHistoryAsync();
                return new OkObjectResult(backupList);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取备份历史记录失败");
                return new StatusCodeResult(StatusCodes.Status500InternalServerError);
            }
        }
    }
}

// 计划行数: 25
// 实际行数: 25 