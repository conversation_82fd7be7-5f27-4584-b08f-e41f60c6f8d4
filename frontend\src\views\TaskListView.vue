<template>
  <div class="task-list">
    <div class="page-header">任务列表</div>

    <!-- 过滤器区域 -->
    <el-card shadow="hover" class="filter-card">
      <div class="filters">
        <el-select v-model="filters.status" placeholder="状态" clearable style="width: 150px;">
          <el-option label="未开始" value="unstarted"></el-option>
          <el-option label="进行中" value="in-progress"></el-option>
          <el-option label="已完成" value="completed"></el-option>
          <el-option label="已逾期" value="overdue"></el-option>
        </el-select>
        <el-select v-model="filters.type" placeholder="类型" clearable style="width: 150px;">
          <el-option label="日常任务" value="daily"></el-option>
          <el-option label="周期任务" value="periodic"></el-option>
          <el-option label="PDCA任务" value="pdca"></el-option>
        </el-select>
        <el-select v-model="filters.priority" placeholder="优先级" clearable style="width: 120px;">
          <el-option label="高" value="high"></el-option>
          <el-option label="中" value="medium"></el-option>
          <el-option label="低" value="low"></el-option>
        </el-select>
        <el-select v-model="filters.assignee" placeholder="负责人" clearable style="width: 150px;">
          <el-option v-for="member in teamMembers" :key="member.id" :label="member.name" :value="member.id"></el-option>
        </el-select>
        <el-date-picker v-model="filters.dateRange" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" style="width: 240px;"></el-date-picker>
        <el-input v-model="filters.keyword" placeholder="搜索任务标题..." clearable style="width: 200px;"></el-input>
        <el-button type="primary" @click="applyFilters">搜索</el-button>
        <el-button @click="resetFilters">重置</el-button>
      </div>
    </el-card>

    <!-- 任务表格 -->
    <el-card shadow="hover" class="task-table-card">
      <el-table :data="paginatedTasks" style="width: 100%">
        <el-table-column prop="title" label="任务标题" min-width="200">
          <template #default="{ row }">
            <el-tooltip :content="row.description" placement="top">
              <span style="cursor: pointer;" @click="viewTaskDetail(row.id)">{{ row.title }}</span>
            </el-tooltip>
            <el-tag v-if="row.mentioned" type="danger" size="small" style="margin-left: 5px;">@我</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="type" label="类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getTaskTypeStyle(row.type).type" effect="light">
              {{ getTaskTypeStyle(row.type).label }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getTaskStatusStyle(row.status).type" effect="plain">
              {{ getTaskStatusStyle(row.status).label }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="priority" label="优先级" width="80" align="center">
          <template #default="{ row }">
            <el-icon :color="getPriorityColor(row.priority)" class="priority-icon">
              <ArrowUp v-if="row.priority === 'high'" />
              <Minus v-else-if="row.priority === 'medium'" />
              <ArrowDown v-else-if="row.priority === 'low'" />
            </el-icon>
          </template>
        </el-table-column>
        <el-table-column prop="assignees" label="负责人" width="150">
          <template #default="{ row }">
            <el-avatar-group :max="2">
              <el-tooltip v-for="userId in row.assignees" :key="userId" :content="getUserById(userId)?.name" placement="top">
                <el-avatar :size="28" :src="getUserById(userId)?.avatar" />
              </el-tooltip>
            </el-avatar-group>
          </template>
        </el-table-column>
        <el-table-column prop="dueDate" label="截止时间" width="150" sortable>
          <template #default="{ row }">
            <span :class="{ overdue: isOverdue(row.dueDate) && row.status !== 'completed' }">
              {{ row.dueDate }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" link size="small" @click="viewTaskDetail(row.id)">详情</el-button>
            <el-button v-if="row.status === 'unstarted'" type="success" link size="small" @click="startTask(row.id)">开始</el-button>
            <el-button v-if="row.status === 'in-progress'" type="success" link size="small" @click="completeTask(row.id)">完成</el-button>
            <el-button type="warning" link size="small" @click="editTask(row.id)">编辑</el-button>
            <el-popconfirm title="确定删除此任务吗？" @confirm="deleteTask(row.id)">
              <template #reference>
                <el-button type="danger" link size="small">删除</el-button>
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        style="margin-top: 20px; justify-content: flex-end;"
        v-model:current-page="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="filteredTasks.length"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ArrowUp, Minus, ArrowDown } from '@element-plus/icons-vue'

const router = useRouter()

// 团队成员
const teamMembers = reactive([
  { id: 'user1', name: '张三', avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png' },
  { id: 'user2', name: '李四', avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png' },
  { id: 'user3', name: '王五', avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png' },
  { id: 'user4', name: '赵六', avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png' }
])

// 过滤器
const filters = reactive({
  status: '',
  type: '',
  priority: '',
  assignee: '',
  dateRange: null,
  keyword: ''
})

// 分页
const pagination = reactive({
  currentPage: 1,
  pageSize: 10
})

// 任务数据
const tasks = reactive([
  {
    id: 'task1',
    title: '优化用户注册流程',
    description: '重新设计注册表单，简化注册步骤，提高转化率',
    type: 'daily',
    status: 'unstarted',
    priority: 'high',
    assignees: ['user1', 'user2'],
    createDate: '2024-05-01',
    dueDate: '2024-05-10',
    mentioned: true
  },
  {
    id: 'task2',
    title: '修复移动端显示问题',
    description: '解决在小屏设备上页面布局错乱的问题',
    type: 'daily',
    status: 'in-progress',
    priority: 'medium',
    assignees: ['user1'],
    createDate: '2024-05-02',
    dueDate: '2024-05-12',
    mentioned: false
  },
  {
    id: 'task3',
    title: '完成系统架构文档',
    description: '详细描述系统架构、模块划分和技术选型',
    type: 'daily',
    status: 'in-progress',
    priority: 'low',
    assignees: ['user3'],
    createDate: '2024-05-03',
    dueDate: '2024-05-15',
    mentioned: false
  },
  {
    id: 'task4',
    title: '实现数据导出功能',
    description: '支持将任务数据导出为Excel和PDF格式',
    type: 'daily',
    status: 'completed',
    priority: 'medium',
    assignees: ['user1', 'user4'],
    createDate: '2024-04-20',
    dueDate: '2024-04-30',
    mentioned: false
  },
  {
    id: 'task5',
    title: '每周项目进度汇报',
    description: '整理项目进展，准备周会汇报材料',
    type: 'periodic',
    status: 'unstarted',
    priority: 'high',
    assignees: ['user1'],
    createDate: '2024-05-03',
    dueDate: '2024-05-07',
    mentioned: false
  }
])

// 过滤后的任务
const filteredTasks = computed(() => {
  return tasks.filter(task => {
    if (filters.status && task.status !== filters.status) return false
    if (filters.type && task.type !== filters.type) return false
    if (filters.priority && task.priority !== filters.priority) return false
    if (filters.assignee && !task.assignees.includes(filters.assignee)) return false
    if (filters.keyword && !task.title.toLowerCase().includes(filters.keyword.toLowerCase())) return false
    return true
  })
})

// 分页后的任务
const paginatedTasks = computed(() => {
  const start = (pagination.currentPage - 1) * pagination.pageSize
  const end = start + pagination.pageSize
  return filteredTasks.value.slice(start, end)
})

// 查找用户
function getUserById(userId: string) {
  return teamMembers.find(user => user.id === userId)
}

// 获取任务类型样式
function getTaskTypeStyle(type: string) {
  switch (type) {
    case 'daily':
      return { label: '日常任务', type: 'info' }
    case 'periodic':
      return { label: '周期任务', type: 'success' }
    case 'pdca':
      return { label: 'PDCA任务', type: 'warning' }
    default:
      return { label: '未知类型', type: 'info' }
  }
}

// 获取任务状态样式
function getTaskStatusStyle(status: string) {
  switch (status) {
    case 'unstarted':
      return { label: '未开始', type: 'info' }
    case 'in-progress':
      return { label: '进行中', type: 'warning' }
    case 'completed':
      return { label: '已完成', type: 'success' }
    case 'overdue':
      return { label: '已逾期', type: 'danger' }
    default:
      return { label: '未知状态', type: 'info' }
  }
}

// 获取优先级颜色
function getPriorityColor(priority: string) {
  switch (priority) {
    case 'high':
      return '#F56C6C'
    case 'medium':
      return '#E6A23C'
    case 'low':
      return '#909399'
    default:
      return '#909399'
  }
}

// 判断是否逾期
function isOverdue(dueDate: string) {
  const today = new Date()
  const due = new Date(dueDate)
  return due < today
}

// 分页处理
function handleSizeChange(size: number) {
  pagination.pageSize = size
  pagination.currentPage = 1
}

function handleCurrentChange(page: number) {
  pagination.currentPage = page
}

// 应用过滤器
function applyFilters() {
  console.log('应用过滤器', filters)
  pagination.currentPage = 1
}

// 重置过滤器
function resetFilters() {
  Object.keys(filters).forEach(key => {
    filters[key as keyof typeof filters] = key === 'dateRange' ? null : ''
  })
  pagination.currentPage = 1
}

// 查看任务详情
function viewTaskDetail(taskId: string) {
  router.push({ name: 'taskDetail', params: { id: taskId } })
}

// 开始任务
function startTask(taskId: string) {
  const task = tasks.find(t => t.id === taskId)
  if (task) {
    task.status = 'in-progress'
  }
}

// 完成任务
function completeTask(taskId: string) {
  const task = tasks.find(t => t.id === taskId)
  if (task) {
    task.status = 'completed'
  }
}

// 编辑任务
function editTask(taskId: string) {
  console.log('编辑任务', taskId)
}

// 删除任务
function deleteTask(taskId: string) {
  const index = tasks.findIndex(t => t.id === taskId)
  if (index !== -1) {
    tasks.splice(index, 1)
  }
}
</script>

<style scoped>
.page-header {
  font-size: 20px;
  font-weight: 500;
  margin-bottom: 20px;
}

.filter-card {
  margin-bottom: 20px;
  background-color: rgba(26, 86, 219, 0.02);
}

.filters {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 10px;
}

.task-table-card {
  margin-bottom: 20px;
}

/* 更新任务类型标签样式 */
:deep(.el-tag--info) {
  background-color: rgba(26, 86, 219, 0.1);
  border-color: rgba(26, 86, 219, 0.2);
  color: var(--app-primary-color);
}

:deep(.el-tag--success) {
  background-color: rgba(16, 185, 129, 0.1);
  border-color: rgba(16, 185, 129, 0.2);
  color: var(--app-success-color);
}

:deep(.el-tag--warning) {
  background-color: rgba(214, 158, 46, 0.1);
  border-color: rgba(214, 158, 46, 0.2);
  color: var(--app-accent-color);
}

/* 更新任务状态标签样式 */
:deep(.el-tag.el-tag--plain) {
  border-width: 2px;
}

:deep(.el-tag--plain.el-tag--info) {
  border-color: var(--app-primary-color);
  color: var(--app-primary-color);
}

:deep(.el-tag--plain.el-tag--success) {
  border-color: var(--app-success-color);
  color: var(--app-success-color);
}

:deep(.el-tag--plain.el-tag--warning) {
  border-color: var(--app-accent-color);
  color: var(--app-accent-color);
}

:deep(.el-tag--plain.el-tag--danger) {
  border-color: var(--el-color-danger);
  color: var(--el-color-danger);
}

/* 更新优先级图标颜色 */
.priority-icon {
  font-size: 16px;
}

.priority-high {
  color: var(--el-color-danger);
}

.priority-medium {
  color: var(--app-accent-color);
}

.priority-low {
  color: rgba(63, 131, 248, 0.6);
}

/* 更新表格样式 */
:deep(.el-table) {
  --el-table-header-bg-color: rgba(26, 86, 219, 0.05);
}

:deep(.el-table__row:hover) td {
  background-color: rgba(26, 86, 219, 0.02) !important;
}

/* 更新按钮样式 */
:deep(.el-button--primary.is-link) {
  color: var(--app-primary-color);
}

:deep(.el-button--primary.is-link:hover) {
  color: var(--app-secondary-color);
}

:deep(.el-button--success.is-link) {
  color: var(--app-success-color);
}

:deep(.el-button--success.is-link:hover) {
  color: var(--app-success-color);
  opacity: 0.8;
}

:deep(.el-button--warning.is-link) {
  color: var(--app-accent-color);
}

:deep(.el-button--warning.is-link:hover) {
  color: var(--app-accent-color);
  opacity: 0.8;
}

:deep(.el-button--danger.is-link) {
  color: var(--el-color-danger);
}

:deep(.el-button--danger.is-link:hover) {
  color: var(--el-color-danger);
  opacity: 0.8;
}

.overdue {
  color: var(--el-color-danger);
  font-weight: bold;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .filters {
    flex-direction: column;
  }
}

.card-desc {
  font-size: 13px;
  color: rgba(26, 86, 219, 0.65);
  margin-bottom: 10px;
  word-break: break-word;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style> 