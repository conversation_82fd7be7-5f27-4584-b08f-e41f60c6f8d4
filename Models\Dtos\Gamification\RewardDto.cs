// File: Models/Dtos/Gamification/RewardDto.cs
// Description: 奖励数据传输对象

namespace ItAssetsSystem.Models.Dtos.Gamification
{
    /// <summary>
    /// 奖励数据传输对象
    /// </summary>
    public class RewardDto
    {
        /// <summary>
        /// 积分
        /// </summary>
        public int Points { get; set; }

        /// <summary>
        /// 金币
        /// </summary>
        public int Coins { get; set; }

        /// <summary>
        /// 钻石
        /// </summary>
        public int Diamonds { get; set; }

        /// <summary>
        /// 行为类型
        /// </summary>
        public string ActionType { get; set; } = string.Empty;

        /// <summary>
        /// 描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 关联对象ID
        /// </summary>
        public string? ReferenceId { get; set; }
    }
}
