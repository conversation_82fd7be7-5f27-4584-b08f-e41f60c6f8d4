// File: Infrastructure/Data/Repositories/SparePartTransactionRepository.cs
// Description: 备品备件出入库记录仓储实现类

using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ItAssetsSystem.Application.Common.Dtos;
using ItAssetsSystem.Application.Features.SpareParts.Dtos;
using ItAssetsSystem.Core.Abstractions;
using ItAssetsSystem.Domain.Entities;
using ItAssetsSystem.Infrastructure.Data;
using ItAssetsSystem.Models.Entities;
using Microsoft.EntityFrameworkCore;

namespace ItAssetsSystem.Infrastructure.Data.Repositories
{
    /// <summary>
    /// 备品备件出入库记录仓储实现类
    /// </summary>
    public class SparePartTransactionRepository : ISparePartTransactionRepository
    {
        private readonly AppDbContext _dbContext;
        
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="dbContext">数据库上下文</param>
        public SparePartTransactionRepository(AppDbContext dbContext)
        {
            _dbContext = dbContext;
        }
        
        /// <summary>
        /// 获取出入库记录分页列表
        /// </summary>
        /// <param name="query">查询参数</param>
        /// <returns>记录列表和总数</returns>
        public async Task<(List<SparePartTransaction>, int)> GetTransactionsPagedAsync(SparePartTransactionQuery query)
        {
            var transactionsQuery = _dbContext.SparePartTransactions
                .Include(t => t.Part)
                .Include(t => t.Location)
                .AsQueryable();
            
            // 应用筛选条件
            if (!string.IsNullOrEmpty(query.Keyword))
            {
                transactionsQuery = transactionsQuery.Where(t => 
                    t.Part.Name.Contains(query.Keyword) || 
                    t.Part.Code.Contains(query.Keyword) || 
                    t.ReferenceNumber.Contains(query.Keyword));
            }
            
            if (query.PartId.HasValue)
            {
                transactionsQuery = transactionsQuery.Where(t => t.PartId == query.PartId);
            }
            
            if (query.LocationId.HasValue)
            {
                transactionsQuery = transactionsQuery.Where(t => t.LocationId == query.LocationId);
            }
            
            if (!string.IsNullOrEmpty(query.LocationArea))
            {
                transactionsQuery = transactionsQuery.Where(t => t.Location.Area == query.LocationArea);
            }
            
            if (query.Type.HasValue)
            {
                transactionsQuery = transactionsQuery.Where(t => t.Type == query.Type);
            }
            
            if (query.ReasonType.HasValue)
            {
                transactionsQuery = transactionsQuery.Where(t => t.ReasonType == query.ReasonType);
            }
            
            if (query.UserId.HasValue)
            {
                transactionsQuery = transactionsQuery.Where(t => t.OperatorUserId == query.UserId);
            }
            
            if (query.StartTime.HasValue)
            {
                transactionsQuery = transactionsQuery.Where(t => t.OperationTime >= query.StartTime);
            }
            
            if (query.EndTime.HasValue)
            {
                transactionsQuery = transactionsQuery.Where(t => t.OperationTime <= query.EndTime);
            }
            
            // 应用排序
            if (!string.IsNullOrEmpty(query.SortBy))
            {
                switch (query.SortBy.ToLower())
                {
                    case "operationtime":
                        transactionsQuery = query.SortDirection.ToLower() == "desc" 
                            ? transactionsQuery.OrderByDescending(t => t.OperationTime)
                            : transactionsQuery.OrderBy(t => t.OperationTime);
                        break;
                    case "quantity":
                        transactionsQuery = query.SortDirection.ToLower() == "desc" 
                            ? transactionsQuery.OrderByDescending(t => t.Quantity)
                            : transactionsQuery.OrderBy(t => t.Quantity);
                        break;
                    case "type":
                        transactionsQuery = query.SortDirection.ToLower() == "desc" 
                            ? transactionsQuery.OrderByDescending(t => t.Type)
                            : transactionsQuery.OrderBy(t => t.Type);
                        break;
                    default:
                        transactionsQuery = transactionsQuery.OrderByDescending(t => t.OperationTime);
                        break;
                }
            }
            else
            {
                // 默认排序：操作时间降序
                transactionsQuery = transactionsQuery.OrderByDescending(t => t.OperationTime);
            }
            
            // 计算总记录数
            var totalCount = await transactionsQuery.CountAsync();
            
            // 应用分页并加载用户信息
            var transactions = await transactionsQuery
                .Skip((query.PageIndex - 1) * query.PageSize)
                .Take(query.PageSize)
                .ToListAsync();
            
            // 获取所有相关的用户ID
            var userIds = transactions.Select(t => t.OperatorUserId).Distinct().ToList();
            
            // 批量加载用户信息
            var users = await _dbContext.Users
                .Where(u => userIds.Contains(u.Id))
                .ToDictionaryAsync(u => u.Id, u => u);
            
            // 为每个交易记录关联用户信息
            foreach (var transaction in transactions)
            {
                if (users.TryGetValue(transaction.OperatorUserId, out var user))
                {
                    // 在这里我们不能直接设置User属性，因为SparePartTransaction实体没有定义User导航属性
                    // 但我们可以在后续的DTO映射中使用这个用户字典
                }
            }
            
            return (transactions, totalCount);
        }
        
        /// <summary>
        /// 获取特定备件的出入库记录
        /// </summary>
        /// <param name="partId">备件ID</param>
        /// <param name="query">分页参数</param>
        /// <returns>记录列表和总数</returns>
        public async Task<(List<SparePartTransaction>, int)> GetTransactionsByPartIdAsync(long partId, PaginationQuery query)
        {
            var transactionsQuery = _dbContext.SparePartTransactions
                .Include(t => t.Part)
                .Include(t => t.Location)
                .Where(t => t.PartId == partId)
                .OrderByDescending(t => t.OperationTime);
            
            // 计算总记录数
            var totalCount = await transactionsQuery.CountAsync();
            
            // 应用分页
            var transactions = await transactionsQuery
                .Skip((query.PageIndex - 1) * query.PageSize)
                .Take(query.PageSize)
                .ToListAsync();
            
            // 获取所有相关的用户ID
            var userIds = transactions.Select(t => t.OperatorUserId).Distinct().ToList();
            
            // 批量加载用户信息
            var users = await _dbContext.Users
                .Where(u => userIds.Contains(u.Id))
                .ToDictionaryAsync(u => u.Id, u => u);
            
            return (transactions, totalCount);
        }
        
        /// <summary>
        /// 根据ID获取出入库记录
        /// </summary>
        /// <param name="id">记录ID</param>
        /// <returns>记录实体</returns>
        public async Task<SparePartTransaction> GetByIdAsync(long id)
        {
            return await _dbContext.SparePartTransactions
                .Include(t => t.Part)
                .Include(t => t.Location)
                .FirstOrDefaultAsync(t => t.Id == id);
        }
        
        /// <summary>
        /// 创建出入库记录
        /// </summary>
        /// <param name="entity">记录实体</param>
        /// <returns>创建的记录实体</returns>
        public async Task<SparePartTransaction> CreateAsync(SparePartTransaction entity)
        {
            await _dbContext.SparePartTransactions.AddAsync(entity);
            await _dbContext.SaveChangesAsync();
            
            return entity;
        }
        
        /// <summary>
        /// 获取用户名称
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>用户名称</returns>
        public async Task<string> GetUserNameAsync(int userId)
        {
            var user = await _dbContext.Users.FindAsync(userId);
            return user?.Name ?? "未知";
        }
    }
} 