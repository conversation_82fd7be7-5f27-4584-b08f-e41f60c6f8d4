/**
 * 航空航天级IT资产管理系统 - 仪表盘页面
 * 文件路径: src/views/dashboard/index.vue
 * 功能描述: 系统首页仪表盘，展示系统概览数据
 */

<template>
  <div class="dashboard-container">
    <!-- 添加调试信息，可在生产环境移除 -->
    <div class="debug-info" style="margin-bottom: 10px; padding: 8px; background-color: #f5f7fa; border-radius: 4px; font-size: 12px;">
      <p>当前路由路径: {{ currentRoute }}</p>
      <p>匹配结果: {{ isMainDashboard ? '主仪表盘' : '简易仪表盘' }}</p>
    </div>
    
    <!-- 根据不同路由显示不同的仪表盘内容 -->
    <template v-if="isMainDashboard">
      <!-- 主应用完整仪表盘内容 -->
      <div class="welcome-section">
        <div class="welcome-info">
          <h2 class="welcome-title">
            {{ greeting }}，{{ userStore.userInfo?.name || '用户' }}
          </h2>
          <p class="welcome-subtitle">今天是 {{ today }}，欢迎使用{{ systemConfig.appName || 'IT资产管理系统' }}</p>
        </div>
        
        <!-- 添加用户积分和等级信息 -->
        <div class="user-game-info" v-if="userGameData">
          <div class="level-badge">
            <el-tooltip content="当前等级">
              <el-tag size="large" effect="dark" class="level-tag">LV.{{ userGameData.level }}</el-tag>
            </el-tooltip>
          </div>
          <div class="score-info">
            <div class="score-value">
              <el-icon><Star /></el-icon> {{ userGameData.score }} 积分
            </div>
            <el-progress 
              :percentage="userGameData.levelProgress" 
              :stroke-width="8" 
              :show-text="false"
              class="level-progress"
            />
            <div class="level-progress-text">
              距离下一级还需 {{ userGameData.nextLevelScore }} 积分
            </div>
          </div>
        </div>
      </div>
      
      <!-- 数据概览卡片 -->
      <el-row :gutter="20" class="data-overview">
        <el-col :xs="24" :sm="12" :md="6">
          <el-card shadow="hover" class="data-card">
            <template #header>
              <div class="card-header">
                <span>资产总数</span>
                <el-tag size="small">总计</el-tag>
              </div>
            </template>
            <div class="card-content">
              <div class="card-value">{{ overview.assetCount }}</div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :xs="24" :sm="12" :md="6">
          <el-card shadow="hover" class="data-card">
            <template #header>
              <div class="card-header">
                <span>待处理故障</span>
                <el-tag size="small" type="warning">待处理</el-tag>
              </div>
            </template>
            <div class="card-content">
              <div class="card-value">{{ overview.pendingFaultCount }}</div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :xs="24" :sm="12" :md="6">
          <el-card shadow="hover" class="data-card task-card-highlight">
            <template #header>
              <div class="card-header">
                <span>待办任务</span>
                <el-tag size="small" type="info">进行中</el-tag>
              </div>
            </template>
            <div class="card-content">
              <div class="card-value">{{ overview.pendingTaskCount }}</div>
              <div class="card-actions">
                <el-button type="text" @click="navigateToTaskBoard">查看任务看板</el-button>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :xs="24" :sm="12" :md="6">
          <el-card shadow="hover" class="data-card">
            <template #header>
              <div class="card-header">
                <span>待审批采购</span>
                <el-tag size="small" type="success">审批中</el-tag>
              </div>
            </template>
            <div class="card-content">
              <div class="card-value">{{ overview.pendingPurchaseCount }}</div>
            </div>
          </el-card>
        </el-col>
      </el-row>
      
      <!-- 游戏化任务指标 -->
      <el-row :gutter="20" class="game-metrics-section">
        <el-col :xs="24">
          <el-card shadow="hover" class="game-metrics-card">
            <template #header>
              <div class="card-header">
                <span>任务成就指标</span>
                <el-button type="primary" link @click="showLeaderboard">查看排行榜</el-button>
              </div>
            </template>
            <div class="game-metrics-content">
              <el-row :gutter="30">
                <el-col :xs="24" :sm="8">
                  <div class="metric-item">
                    <div class="metric-icon task-completion">
                      <el-icon><Check /></el-icon>
                    </div>
                    <div class="metric-info">
                      <div class="metric-value">{{ taskMetrics.completedCount || 0 }}</div>
                      <div class="metric-label">已完成任务</div>
                    </div>
                  </div>
                </el-col>
                
                <el-col :xs="24" :sm="8">
                  <div class="metric-item">
                    <div class="metric-icon task-streak">
                      <el-icon><Calendar /></el-icon>
                    </div>
                    <div class="metric-info">
                      <div class="metric-value">{{ taskMetrics.completionStreak || 0 }}</div>
                      <div class="metric-label">连续完成天数</div>
                    </div>
                  </div>
                </el-col>
                
                <el-col :xs="24" :sm="8">
                  <div class="metric-item">
                    <div class="metric-icon task-items">
                      <el-icon><Suitcase /></el-icon>
                    </div>
                    <div class="metric-info">
                      <div class="metric-value">{{ taskMetrics.itemCount || 0 }}</div>
                      <div class="metric-label">拥有道具数</div>
                      <el-button v-if="taskMetrics.itemCount > 0" type="success" size="small" @click="navigateToItems">查看道具</el-button>
                    </div>
                  </div>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-col>
      </el-row>
      
      <!-- 图表区域 -->
      <el-row :gutter="20" class="chart-section">
        <el-col :xs="24" :md="12">
          <el-card shadow="hover" class="chart-card">
            <template #header>
              <div class="card-header">
                <span>资产分类统计</span>
                <el-button type="link">详情</el-button>
              </div>
            </template>
            <div class="chart-placeholder">
              <div class="chart-loading" v-if="loading">
                <el-skeleton animated :rows="5" />
              </div>
              <div v-else class="chart-empty">
                <el-empty description="资产分类数据加载中" />
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :xs="24" :md="12">
          <el-card shadow="hover" class="chart-card">
            <template #header>
              <div class="card-header">
                <span>故障趋势分析</span>
                <el-button type="link">详情</el-button>
              </div>
            </template>
            <div class="chart-placeholder">
              <div class="chart-loading" v-if="loading">
                <el-skeleton animated :rows="5" />
              </div>
              <div v-else class="chart-empty">
                <el-empty description="故障趋势数据加载中" />
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
      
      <!-- 任务和通知区域 -->
      <el-row :gutter="20" class="task-notification-section">
        <el-col :xs="24" :lg="16">
          <el-card shadow="hover" class="task-card">
            <template #header>
              <div class="card-header">
                <span>我的任务</span>
                <el-button type="link" @click="viewMoreTasks">查看更多</el-button>
              </div>
            </template>
            <div class="task-list" v-if="!loading && tasks.length > 0">
              <el-table :data="tasks" style="width: 100%" :show-header="false">
                <el-table-column width="40">
                  <template #default="scope">
                    <el-tag
                      :type="getTaskStatusType(scope.row.status)"
                      size="small"
                      effect="plain"
                    ></el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="title" label="任务标题" />
                <el-table-column prop="deadline" label="截止日期" width="120" />
                <el-table-column width="80">
                  <template #default>
                    <el-button type="text">处理</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <div v-else-if="loading" class="loading-placeholder">
              <el-skeleton animated :rows="3" />
            </div>
            <el-empty v-else description="暂无任务" />
          </el-card>
        </el-col>
        
        <el-col :xs="24" :lg="8">
          <el-card shadow="hover" class="notification-card">
            <template #header>
              <div class="card-header">
                <span>系统通知</span>
                <el-button type="link">全部已读</el-button>
              </div>
            </template>
            <div class="notification-list" v-if="!loading && notifications.length > 0">
              <div 
                v-for="(item, index) in notifications" 
                :key="index"
                class="notification-item"
                :class="{ 'is-unread': !item.read }"
              >
                <el-badge is-dot :hidden="item.read" class="notification-badge">
                  <div class="notification-content">
                    <p class="notification-title">{{ item.title }}</p>
                    <p class="notification-time">{{ item.time }}</p>
                  </div>
                </el-badge>
              </div>
            </div>
            <div v-else-if="loading" class="loading-placeholder">
              <el-skeleton animated :rows="5" />
            </div>
            <el-empty v-else description="暂无通知" />
          </el-card>
        </el-col>
      </el-row>
    </template>
    
    <!-- 简易仪表盘布局 - 适用于 /dashboard-simple 和 /dashboard-basic -->
    <template v-else>
      <div class="simple-dashboard">
        <el-result
          icon="success"
          title="仪表盘加载成功"
          sub-title="您正在查看简化版仪表盘，缺少完整菜单结构"
        >
          <template #extra>
            <el-button type="primary" @click="redirectToMainDashboard">进入完整仪表盘</el-button>
          </template>
        </el-result>
        
        <!-- 简单数据概览 -->
        <el-row :gutter="20" class="simple-data-cards">
          <el-col :span="8" v-for="(item, index) in simpleData" :key="index">
            <el-card shadow="hover">
              <div class="simple-card-content">
                <h3>{{ item.title }}</h3>
                <p class="simple-value">{{ item.value }}</p>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </template>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useUserStore } from '@/stores/modules/user'
import { taskApi } from '@/api/task.js'
import { useGamificationStore } from '@/stores/modules/gamification'
import { ElMessage } from 'element-plus'
import { Star, Check, Calendar, Suitcase } from '@element-plus/icons-vue'
import systemConfig from '@/config/system'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()
const gamificationStore = useGamificationStore()
const loading = ref(true)

// 获取当前路由路径
const currentRoute = computed(() => route.path)

// 判断是否为主仪表盘 - 更灵活的匹配方式
const isMainDashboard = computed(() => {
  // 当路径包含 main/dashboard 或 main 时都视为主仪表盘
  return currentRoute.value.includes('/main/dashboard') || currentRoute.value === '/main'
})

// 监听路由变化并记录日志
watch(route, (newRoute) => {
  console.log('路由变化:', {
    path: newRoute.path,
    fullPath: newRoute.fullPath,
    name: newRoute.name,
    params: newRoute.params,
    query: newRoute.query,
    isMainDashboard: isMainDashboard.value
  })
}, { immediate: true })

// 简化版仪表盘数据
const simpleData = reactive([
  { title: '资产总数', value: '123' },
  { title: '待处理故障', value: '5' },
  { title: '待办任务', value: '8' }
])

// 重定向到主仪表盘
const redirectToMainDashboard = () => {
  router.push('/main/dashboard')
}

// 仪表盘概览数据 - Initialize with defaults
const overview = reactive({
  assetCount: 0, // Keep non-task data, fetch separately if needed
  pendingFaultCount: 0, // Keep non-task data, fetch separately if needed
  pendingTaskCount: 0, // Will be fetched
  pendingPurchaseCount: 0 // Keep non-task data, fetch separately if needed
})

// 任务列表示例数据 - Initialize as empty
const tasks = ref([])

// 通知列表示例数据 - Keep for now, fetch separately if needed
const notifications = ref([
  // ... mock notifications ...
])

// 获取当前问候语
const greeting = computed(() => {
  const hour = new Date().getHours()
  if (hour < 6) return '凌晨好'
  if (hour < 9) return '早上好'
  if (hour < 12) return '上午好'
  if (hour < 14) return '中午好'
  if (hour < 18) return '下午好'
  return '晚上好'
})

// 获取今天日期
const today = new Date().toISOString().split('T')[0]

// 获取任务状态对应的类型 - Update for V2 statuses
const getTaskStatusType = (status) => {
  const statusMap = {
    'unstarted': 'info', // V2
    'in-progress': 'primary', // V2
    'completed': 'success', // V2
    // 'pending': 'info', // Old
    // 'processing': 'warning', // Old
    // 'overdue': 'danger' // Old
  }
  return statusMap[status] || 'info' // Default
}

// 查看更多任务
const viewMoreTasks = () => {
  router.push('/task/list')
}

// 获取仪表盘数据 - Update to fetch real task data
const fetchDashboardData = async () => {
  loading.value = true
  try {
    // Fetch non-task data here if needed (Assets, Faults, Purchases)
    // For now, we only fetch task-related data

    const userId = userStore.userInfo?.id;
    if (!userId) {
        console.warn("User ID not found, cannot fetch user-specific tasks.");
        // Handle appropriately, maybe show message or zero counts
    }

    // --- Fetch Task Data Concurrently ---
    const promises = [];

    // 1. Get pending task count (unstarted or in-progress)
    // Note: V2 API might not support multiple statuses. Querying 'in-progress'
    // is a common requirement for 'pending'. Add 'unstarted' count if needed.
    promises.push(taskApi.getTaskList({ status: 'in-progress', pageSize: 1 }).catch(e => { console.error('Failed to fetch in-progress task count:', e); return null; }));
    promises.push(taskApi.getTaskList({ status: 'unstarted', pageSize: 1 }).catch(e => { console.error('Failed to fetch unstarted task count:', e); return null; }));

    // 2. Get 'My Tasks' (assigned to current user, limit to 5 for display)
    if (userId) {
        promises.push(taskApi.getTaskList({ assigneeId: userId, pageSize: 5, sortBy: 'creationTimestamp', sortOrder: 'desc' }).catch(e => { console.error('Failed to fetch my tasks:', e); return null; }));
    } else {
        promises.push(Promise.resolve(null)); // Placeholder if no userId
    }

    // 3. Get completed task count (for taskMetrics)
    promises.push(taskApi.getTaskList({ status: 'completed', pageSize: 1 }).catch(e => { console.error('Failed to fetch completed task count:', e); return null; }));

    // --- Wait for all fetches ---
    const [inProgressTasksResp, unstartedTasksResp, myTasksResp, completedTasksResp] = await Promise.all(promises);

    // --- Process Results ---
    // Update pending count
    let pendingCount = 0;
    if (inProgressTasksResp?.totalCount) pendingCount += inProgressTasksResp.totalCount;
    if (inProgressTasksResp?.total) pendingCount += inProgressTasksResp.total;
    if (unstartedTasksResp?.totalCount) pendingCount += unstartedTasksResp.totalCount;
    if (unstartedTasksResp?.total) pendingCount += unstartedTasksResp.total;
    overview.pendingTaskCount = pendingCount;

    // Update my tasks list
    if (myTasksResp?.items) {
        tasks.value = myTasksResp.items;
    } else if (myTasksResp?.list) {
         tasks.value = myTasksResp.list;
    } else {
        tasks.value = [];
    }

    // Update task metrics (only completed count for now)
    let completedCount = 0;
     if (completedTasksResp?.totalCount) completedCount += completedTasksResp.totalCount;
     if (completedTasksResp?.total) completedCount += completedTasksResp.total;
    taskMetrics.value.completedCount = completedCount;
    // Keep other metrics as 0 until gamification API is ready
    taskMetrics.value.completionStreak = 0; 
    taskMetrics.value.itemCount = gamificationStore.inventory.length; // Get item count from gamification store if available


    if (isMainDashboard.value) {
      ElMessage.success('仪表盘数据加载完成')
    }

  } catch (error) {
    console.error('获取仪表盘数据失败:', error)
    ElMessage.error('仪表盘数据加载失败')
    // Reset task related data on error
    overview.pendingTaskCount = 0;
    tasks.value = [];
    taskMetrics.value.completedCount = 0;
  } finally {
    loading.value = false
  }
}

// 用户游戏化数据 - Fetch from Gamification Store
const userGameData = computed(() => ({
  level: gamificationStore.level,
  score: gamificationStore.score,
  levelProgress: gamificationStore.currentLevelProgress,
  nextLevelScore: gamificationStore.pointsToNextLevel
}))

// 任务游戏化指标 - Initialize, completedCount fetched above
const taskMetrics = ref({
  completedCount: 0,
  completionStreak: 0, // Requires dedicated gamification logic/API
  itemCount: 0 // Get from gamification store inventory
})

// 导航到任务看板
const navigateToTaskBoard = () => {
  router.push('/main/tasks/board')
}

// 显示排行榜
const showLeaderboard = () => {
  router.push('/main/tasks/board')
  // 这里可以触发排行榜弹窗显示
  // 或者导航到独立的排行榜页面
}

// 导航到道具页面
const navigateToItems = () => {
  router.push('/main/tasks/board')
  // 这里可以触发道具背包弹窗显示
}

onMounted(() => {
  // 输出调试信息
  console.log('仪表盘组件挂载成功，当前路由:', {
    path: route.path,
    fullPath: route.fullPath,
    isMainDashboard: isMainDashboard.value
  })
  
  // Fetch dashboard data
  fetchDashboardData()
  
  // 欢迎消息 (修改后)
  ElMessage({
    message: `${greeting.value}，${userStore.userInfo?.name || '用户'}！欢迎使用${ systemConfig.appName || 'IT资产管理系统' }`,
    type: 'success',
    duration: 3000
  })
})
</script>

<style lang="scss" scoped>
.dashboard-container {
  padding: 20px;
  
  // 欢迎区域
  .welcome-section {
    margin-bottom: 24px;
    background-color: #fff;
    border-radius: 4px;
    padding: 24px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .welcome-info {
      .welcome-title {
        font-size: 24px;
        color: #303133;
        margin-bottom: 8px;
        font-weight: 600;
      }
      
      .welcome-subtitle {
        font-size: 16px;
        color: #606266;
      }
    }
  }
  
  // 数据概览卡片
  .data-overview {
    margin-bottom: 24px;
    
    .data-card {
      margin-bottom: 20px;
      
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      
      .card-content {
        padding: 20px 0;
        text-align: center;
        
        .card-value {
          font-size: 36px;
          font-weight: 600;
          color: #303133;
        }
      }
    }
  }
  
  // 图表区域
  .chart-section {
    margin-bottom: 24px;
    
    .chart-card {
      margin-bottom: 20px;
      
      .chart-placeholder {
        height: 300px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
  
  // 任务和通知区域
  .task-notification-section {
    .task-card, .notification-card {
      margin-bottom: 20px;
      
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
    }
    
    .notification-item {
      padding: 10px 0;
      border-bottom: 1px solid #f0f0f0;
      
      &:last-child {
        border-bottom: none;
      }
      
      .notification-content {
        .notification-title {
          font-size: 14px;
          color: #303133;
          margin-bottom: 4px;
        }
        
        .notification-time {
          font-size: 12px;
          color: #909399;
        }
      }
      
      &.is-unread {
        .notification-title {
          font-weight: bold;
        }
      }
    }
  }
  
  // 简易仪表盘区域样式
  .simple-dashboard {
    .simple-data-cards {
      margin-top: 30px;
      
      .simple-card-content {
        text-align: center;
        padding: 20px 0;
        
        h3 {
          font-size: 16px;
          color: #606266;
          margin-bottom: 16px;
        }
        
        .simple-value {
          font-size: 28px;
          font-weight: 600;
          color: #409EFF;
        }
      }
    }
  }
}

.welcome-section {
  .user-game-info {
    display: flex;
    align-items: center;
    background: #f9fafc;
    padding: 12px 20px;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
    
    .level-badge {
      margin-right: 16px;
      
      .level-tag {
        font-size: 16px;
        padding: 8px 12px;
        background: linear-gradient(135deg, #3c8ce7 0%, #00eaff 100%);
        border: none;
      }
    }
    
    .score-info {
      .score-value {
        display: flex;
        align-items: center;
        color: #F7BA2A;
        font-weight: bold;
        font-size: 16px;
        margin-bottom: 8px;
        
        .el-icon {
          margin-right: 6px;
        }
      }
      
      .level-progress {
        width: 180px;
        margin-bottom: 4px;
      }
      
      .level-progress-text {
        font-size: 12px;
        color: #909399;
      }
    }
  }
}

.task-card-highlight {
  border-top: 3px solid #409EFF;
  
  .card-actions {
    margin-top: 8px;
  }
}

.game-metrics-section {
  margin-bottom: 24px;
  
  .game-metrics-card {
    .game-metrics-content {
      padding: 12px 0;
    }
    
    .metric-item {
      display: flex;
      align-items: center;
      padding: 16px;
      
      .metric-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 16px;
        
        .el-icon {
          font-size: 24px;
          color: white;
        }
        
        &.task-completion {
          background: linear-gradient(135deg, #67C23A 0%, #95D475 100%);
        }
        
        &.task-streak {
          background: linear-gradient(135deg, #E6A23C 0%, #F8D488 100%);
        }
        
        &.task-items {
          background: linear-gradient(135deg, #409EFF 0%, #79BBFF 100%);
        }
      }
      
      .metric-info {
        .metric-value {
          font-size: 28px;
          font-weight: bold;
          color: #303133;
          margin-bottom: 4px;
        }
        
        .metric-label {
          font-size: 14px;
          color: #909399;
          margin-bottom: 8px;
        }
      }
    }
  }
}

// 卡片过渡动画
.fade-transform-enter-active, .fade-transform-leave-active {
  transition: all 0.3s;
}

.fade-transform-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}
</style> 