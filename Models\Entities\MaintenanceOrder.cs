// IT资产管理系统 - 维护订单实体
// 文件路径: /Models/Entities/MaintenanceOrder.cs
// 功能: 定义资产维护订单实体

using System;
using System.ComponentModel.DataAnnotations;
using ItAssetsSystem.Models;

namespace ItAssetsSystem.Models.Entities
{
    /// <summary>
    /// 维护订单
    /// </summary>
    public class MaintenanceOrder : IAuditableEntity
    {
        /// <summary>
        /// 维护订单ID
        /// </summary>
        [Key]
        public int Id { get; set; }
        
        /// <summary>
        /// 订单编号
        /// </summary>
        [Required]
        [MaxLength(50)]
        public string OrderNumber { get; set; }
        
        /// <summary>
        /// 维护类型（1-预防性维护，2-修复性维护，3-周期性维护，4-例行检查，5-其他）
        /// </summary>
        public int MaintenanceType { get; set; }
        
        /// <summary>
        /// 资产ID
        /// </summary>
        public int AssetId { get; set; }
        
        /// <summary>
        /// 相关资产
        /// </summary>
        public Asset Asset { get; set; }
        
        /// <summary>
        /// 故障记录ID
        /// </summary>
        public int? FaultRecordId { get; set; }
        
        /// <summary>
        /// 故障记录
        /// </summary>
        public FaultRecord FaultRecord { get; set; }
        
        /// <summary>
        /// 维护描述
        /// </summary>
        [Required]
        [MaxLength(500)]
        public string Description { get; set; }
        
        /// <summary>
        /// 维护日期
        /// </summary>
        public DateTime MaintenanceDate { get; set; }
        
        /// <summary>
        /// 计划完成日期
        /// </summary>
        public DateTime? PlannedCompletionDate { get; set; }
        
        /// <summary>
        /// 实际完成日期
        /// </summary>
        public DateTime? ActualCompletionDate { get; set; }
        
        /// <summary>
        /// 负责人ID
        /// </summary>
        public int AssigneeId { get; set; }
        
        /// <summary>
        /// 负责人
        /// </summary>
        public User Assignee { get; set; }
        
        /// <summary>
        /// 维护结果
        /// </summary>
        [MaxLength(500)]
        public string Result { get; set; }
        
        /// <summary>
        /// 状态（1-待处理，2-处理中，3-已完成，4-已取消）
        /// </summary>
        public int Status { get; set; }
        
        /// <summary>
        /// 位置ID
        /// </summary>
        public int LocationId { get; set; }
        
        /// <summary>
        /// 位置
        /// </summary>
        public Location Location { get; set; }
        
        /// <summary>
        /// 备注
        /// </summary>
        [MaxLength(500)]
        public string Remarks { get; set; }
        
        /// <summary>
        /// 维护成本
        /// </summary>
        public decimal? MaintenanceCost { get; set; }
        
        #region 审计字段
        
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }
        
        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; }
        
        /// <summary>
        /// 创建人ID
        /// </summary>
        public int? CreatedBy { get; set; }
        
        /// <summary>
        /// 更新人ID
        /// </summary>
        public int? UpdatedBy { get; set; }
        
        #endregion
    }
} 