/**
 * 航空航天级IT资产管理系统 - 修改密码页面
 * 文件路径: src/views/user/ChangePassword.vue
 * 功能描述: 用户修改自己的登录密码
 */

<template>
  <div class="change-password">
    <page-header title="修改密码" description="修改您的账户登录密码" />
    
    <el-card shadow="hover" class="password-card">
      <el-form
        ref="passwordFormRef"
        :model="passwordForm"
        :rules="formRules"
        label-width="120px"
        class="password-form"
      >
        <el-form-item label="当前密码" prop="oldPassword">
          <el-input 
            v-model="passwordForm.oldPassword" 
            type="password" 
            placeholder="请输入当前密码"
            show-password
          />
        </el-form-item>
        
        <el-form-item label="新密码" prop="newPassword">
          <el-input 
            v-model="passwordForm.newPassword" 
            type="password" 
            placeholder="请输入新密码"
            show-password
          />
        </el-form-item>
        
        <el-form-item label="确认新密码" prop="confirmPassword">
          <el-input 
            v-model="passwordForm.confirmPassword" 
            type="password" 
            placeholder="请再次输入新密码"
            show-password
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="submitForm">保存修改</el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
      
      <div class="password-tips">
        <h4>密码安全提示：</h4>
        <ul>
          <li>密码长度应不少于8个字符</li>
          <li>应包含大小写字母、数字和特殊字符</li>
          <li>不要使用容易被猜到的信息（如生日、姓名等）</li>
          <li>定期更换密码以提高账户安全性</li>
        </ul>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/modules/user'
import PageHeader from '@/components/PageHeader.vue'

// 状态管理
const router = useRouter()
const userStore = useUserStore()
const passwordFormRef = ref(null)

// 表单数据
const passwordForm = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 密码匹配验证
const validateConfirmPassword = (rule, value, callback) => {
  if (value === '') {
    callback(new Error('请再次输入密码'))
  } else if (value !== passwordForm.newPassword) {
    callback(new Error('两次输入密码不一致'))
  } else {
    callback()
  }
}

// 密码强度验证
const validatePasswordStrength = (rule, value, callback) => {
  if (value === '') {
    callback(new Error('请输入密码'))
  } else if (value.length < 8) {
    callback(new Error('密码长度不能少于8个字符'))
  } else {
    // 密码强度检查：至少包含数字、小写字母、大写字母、特殊字符中的三种
    let strength = 0
    if (/\d/.test(value)) strength++ // 包含数字
    if (/[a-z]/.test(value)) strength++ // 包含小写字母
    if (/[A-Z]/.test(value)) strength++ // 包含大写字母
    if (/[^a-zA-Z0-9]/.test(value)) strength++ // 包含特殊字符
    
    if (strength < 3) {
      callback(new Error('密码强度不足，请包含数字、大小写字母和特殊字符'))
    } else {
      callback()
    }
  }
}

// 表单验证规则
const formRules = {
  oldPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6个字符', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { validator: validatePasswordStrength, trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请再次输入新密码', trigger: 'blur' },
    { validator: validateConfirmPassword, trigger: 'blur' }
  ]
}

// 提交表单
const submitForm = async () => {
  if (!passwordFormRef.value) return
  
  await passwordFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        // 调用API修改密码
        const response = await userStore.changePassword({
          oldPassword: passwordForm.oldPassword,
          newPassword: passwordForm.newPassword
        })
        
        if (response.success) {
          ElMessage.success('密码修改成功，请重新登录')
          
          // 清除用户会话，跳转到登录页
          setTimeout(() => {
            userStore.logout()
            router.push('/login')
          }, 1500)
        } else {
          ElMessage.error(response.message || '修改密码失败')
        }
      } catch (error) {
        console.error('修改密码失败:', error)
        ElMessage.error('修改密码失败: ' + (error.message || '未知错误'))
      }
    } else {
      return false
    }
  })
}

// 重置表单
const resetForm = () => {
  if (passwordFormRef.value) {
    passwordFormRef.value.resetFields()
  }
}

// 页面初始化
onMounted(() => {
  console.log('修改密码页面已加载')
})
</script>

<style lang="scss" scoped>
.change-password {
  padding: 20px;
  
  .password-card {
    margin-top: 20px;
    max-width: 600px;
  }
  
  .password-form {
    margin-bottom: 20px;
  }
  
  .password-tips {
    margin-top: 30px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 4px;
    border-left: 4px solid #409EFF;
    
    h4 {
      margin-top: 0;
      color: #409EFF;
    }
    
    ul {
      padding-left: 20px;
      color: #606266;
      
      li {
        line-height: 1.8;
      }
    }
  }
}
</style> 