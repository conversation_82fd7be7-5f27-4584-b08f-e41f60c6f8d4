using System;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using ItAssetsSystem.Models.Entities;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;

namespace ItAssetsSystem.Services
{
    /// <summary>
    /// Token服务实现
    /// </summary>
    public class TokenService : ITokenService
    {
        private readonly IConfiguration _configuration;

        public TokenService(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        /// <summary>
        /// 生成Token
        /// </summary>
        public string GenerateToken(User user)
        {
            // 获取密钥，使用配置中的值或默认值
            var secretKey = _configuration["Jwt:SecretKey"] ?? "ItAssetsSystem_SecretKey_2025_ABCDEFGHIJKLMN";
            var key = Encoding.ASCII.GetBytes(secretKey);

            // 创建Claims
            var claims = new[]
            {
                new Claim("uid", user.Id.ToString()),
                new Claim(ClaimTypes.NameIdentifier, user.Id.ToString()),
                new Claim(ClaimTypes.Name, user.Username),
                new Claim("username", user.Username)
            };

            // 设置Token参数
            var tokenDescriptor = new SecurityTokenDescriptor
            {
                Subject = new ClaimsIdentity(claims),
                Expires = DateTime.UtcNow.AddHours(24), // Token有效期24小时
                SigningCredentials = new SigningCredentials(
                    new SymmetricSecurityKey(key),
                    SecurityAlgorithms.HmacSha256Signature)
            };

            // 创建Token
            var tokenHandler = new JwtSecurityTokenHandler();
            var token = tokenHandler.CreateToken(tokenDescriptor);

            // 返回Token字符串
            return tokenHandler.WriteToken(token);
        }
    }
} 