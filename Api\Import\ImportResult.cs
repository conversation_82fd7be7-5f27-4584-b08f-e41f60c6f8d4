using System;
using System.Collections.Generic;

namespace ItAssetsSystem.Api.Import
{
    public class ImportResult
    {
        public string ImportId { get; set; }
        public string Status { get; set; }
        public bool Success { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public List<ImportError> Errors { get; set; } = new List<ImportError>();
    }

    public class ImportError
    {
        public int Row { get; set; }
        public string Field { get; set; }
        public string Message { get; set; }
    }
} 