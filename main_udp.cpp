// UDP极致性能客户端主程序
#include "udp_notification_client.h"
#include "tray_manager.h"
#include <iostream>
#include <thread>
#include <chrono>
#include <sstream>

class UDPNotificationApp {
private:
    std::unique_ptr<UDPNotificationClient> client;
    std::unique_ptr<TrayManager> tray;
    bool running = true;
    
public:
    bool initialize() {
        try {
            // 创建UDP客户端
            client = std::make_unique<UDPNotificationClient>();
            
            // 设置消息回调
            client->setMessageCallback([this](const UDPPacket& packet) {
                handleNotification(packet);
            });
            
            // 连接到服务器
            if (!client->connect("127.0.0.1", 8081)) {
                std::cerr << "连接UDP服务器失败" << std::endl;
                return false;
            }
            
            // 创建系统托盘
            tray = std::make_unique<TrayManager>();
            if (!tray->initialize()) {
                std::cerr << "初始化系统托盘失败" << std::endl;
                return false;
            }
            
            std::cout << "UDP通知客户端启动成功" << std::endl;
            return true;
            
        } catch (const std::exception& e) {
            std::cerr << "初始化失败: " << e.what() << std::endl;
            return false;
        }
    }
    
    void run() {
        std::cout << "UDP客户端运行中..." << std::endl;
        std::cout << "按Ctrl+C退出" << std::endl;
        
        // 启动性能监控线程
        std::thread stats_thread(&UDPNotificationApp::statsWorker, this);
        
        // 主消息循环
        MSG msg;
        while (running && GetMessage(&msg, nullptr, 0, 0)) {
            TranslateMessage(&msg);
            DispatchMessage(&msg);
        }
        
        if (stats_thread.joinable()) {
            stats_thread.join();
        }
    }
    
    void shutdown() {
        running = false;
        
        if (client) {
            client->disconnect();
        }
        
        if (tray) {
            tray->cleanup();
        }
        
        std::cout << "UDP客户端已退出" << std::endl;
    }
    
private:
    void handleNotification(const UDPPacket& packet) {
        MessageType type = (MessageType)packet.type;
        Priority priority = (Priority)packet.priority;
        
        // 解析JSON数据
        std::string payload(packet.payload, packet.payload_size);
        
        switch (type) {
            case MessageType::GAMIFICATION_REWARD:
                handleGamificationReward(payload, priority);
                break;
                
            case MessageType::TASK_UPDATE:
                handleTaskUpdate(payload, priority);
                break;
                
            case MessageType::SYSTEM_ALERT:
                handleSystemAlert(payload, priority);
                break;
                
            case MessageType::HEARTBEAT:
                // 心跳消息，不需要处理
                break;
        }
    }
    
    void handleGamificationReward(const std::string& payload, Priority priority) {
        // 显示游戏化奖励通知
        std::string title = "🎮 游戏化奖励";
        
        // 根据优先级设置不同的通知样式
        if (priority >= Priority::HIGH) {
            // 高优先级：弹出气泡通知
            tray->showBalloonTip(title, payload, 3000);
        } else {
            // 普通优先级：仅更新托盘图标
            tray->updateBadge(1);
        }
        
        std::cout << "[奖励] " << payload << std::endl;
    }
    
    void handleTaskUpdate(const std::string& payload, Priority priority) {
        std::string title = "📋 任务更新";
        
        if (priority >= Priority::NORMAL) {
            tray->showBalloonTip(title, payload, 2000);
        }
        
        std::cout << "[任务] " << payload << std::endl;
    }
    
    void handleSystemAlert(const std::string& payload, Priority priority) {
        std::string title = "⚠️ 系统提醒";
        
        // 系统提醒总是显示
        tray->showBalloonTip(title, payload, 5000);
        tray->updateBadge(1);
        
        std::cout << "[系统] " << payload << std::endl;
    }
    
    void statsWorker() {
        while (running) {
            std::this_thread::sleep_for(std::chrono::seconds(10));
            
            if (client) {
                auto stats = client->getStats();
                
                std::ostringstream oss;
                oss << "性能统计 - 发送: " << stats.packets_sent 
                    << " 包/秒: " << std::fixed << std::setprecision(1) << stats.send_rate_pps
                    << " 带宽: " << (stats.send_rate_bps / 1024) << " KB/s";
                
                std::cout << oss.str() << std::endl;
            }
        }
    }
};

// 全局变量用于信号处理
UDPNotificationApp* g_app = nullptr;

BOOL WINAPI ConsoleHandler(DWORD dwType) {
    switch (dwType) {
        case CTRL_C_EVENT:
        case CTRL_CLOSE_EVENT:
        case CTRL_BREAK_EVENT:
            if (g_app) {
                g_app->shutdown();
            }
            return TRUE;
        default:
            return FALSE;
    }
}

int main() {
    std::cout << "=== UDP极致性能通知客户端 ===" << std::endl;
    std::cout << "版本: 1.0" << std::endl;
    std::cout << "协议: UDP + 自定义协议" << std::endl;
    std::cout << "目标延迟: < 0.5ms" << std::endl;
    std::cout << "===========================" << std::endl;
    
    // 设置控制台信号处理
    SetConsoleCtrlHandler(ConsoleHandler, TRUE);
    
    try {
        UDPNotificationApp app;
        g_app = &app;
        
        if (!app.initialize()) {
            std::cerr << "应用初始化失败" << std::endl;
            return 1;
        }
        
        app.run();
        
    } catch (const std::exception& e) {
        std::cerr << "应用运行时错误: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}