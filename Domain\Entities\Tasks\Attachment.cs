// File: Domain/Entities/Tasks/Attachment.cs
// Description: 附件实体
#nullable enable
using System;
using System.Collections.Generic;
using ItAssetsSystem.Models.Entities; // Corrected: Assuming User is here

namespace ItAssetsSystem.Domain.Entities.Tasks
{
    /// <summary>
    /// 任务附件表 (V2 - BIGINT PK)
    /// </summary>
    public class Attachment
    {
        /// <summary>
        /// 附件主键ID (BIGINT)
        /// </summary>
        public long AttachmentId { get; set; }

        /// <summary>
        /// 关联的任务ID (BIGINT, 可为空)
        /// </summary>
        public long? TaskId { get; set; }
        /// <summary>
        /// 关联的任务实体
        /// </summary>
        public virtual Task? Task { get; set; }

        /// <summary>
        /// 关联的评论ID (BIGINT, 可为空)
        /// </summary>
        public long? CommentId { get; set; }
        /// <summary>
        /// 关联的评论实体
        /// </summary>
        public virtual Comment? Comment { get; set; }

        /// <summary>
        /// 上传用户ID (INT, 关联 users.Id)
        /// </summary>
        public int UploaderUserId { get; set; }
        /// <summary>
        /// 关联的用户实体
        /// </summary>
        public virtual User UploaderUser { get; set; } = null!; // Adjust User namespace if needed

        /// <summary>
        /// 原始文件名
        /// </summary>
        public string FileName { get; set; } = string.Empty;

        /// <summary>
        /// 存储系统中的文件名 (建议UUID, 确保唯一)
        /// </summary>
        public string StoredFileName { get; set; } = string.Empty;

        /// <summary>
        /// 文件存储路径 (相对路径或包含Bucket信息)
        /// </summary>
        public string FilePath { get; set; } = string.Empty;

        /// <summary>
        /// 文件大小 (字节)
        /// </summary>
        public long FileSize { get; set; }

        /// <summary>
        /// 文件MIME类型 (如 application/pdf, image/jpeg)
        /// </summary>
        public string FileType { get; set; } = string.Empty;

        /// <summary>
        /// 是否可以在线预览 (0:否, 1:是)
        /// </summary>
        public bool IsPreviewable { get; set; }

        /// <summary>
        /// 存储类型 (Local-本地, S3, AzureBlob等)
        /// </summary>
        public string StorageType { get; set; } = "Local";

        /// <summary>
        /// 记录上传时间 (替代 CreatedAt/UploadedAt)
        /// </summary>
        public DateTime CreationTimestamp { get; set; }
    }
} 