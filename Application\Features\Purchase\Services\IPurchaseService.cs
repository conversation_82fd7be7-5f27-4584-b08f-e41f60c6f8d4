// File: Application/Features/Purchase/Services/IPurchaseService.cs
// Description: 采购管理服务接口 - 实现采购到备件库和资产的转化

using ItAssetsSystem.Application.Features.Purchase.Dtos;
using ItAssetsSystem.Models.Entities;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ItAssetsSystem.Application.Features.Purchase.Services
{
    /// <summary>
    /// 采购服务接口 - 支持采购到备件库和资产的强关联转化
    /// </summary>
    public interface IPurchaseService
    {
        /// <summary>
        /// 获取采购订单列表（分页）
        /// </summary>
        /// <param name="query">查询参数</param>
        /// <returns>分页结果</returns>
        Task<PagedResult<PurchaseOrderDto>> GetPurchaseOrdersAsync(PurchaseOrderQuery query);

        /// <summary>
        /// 获取采购订单详情
        /// </summary>
        /// <param name="id">订单ID</param>
        /// <returns>订单详情</returns>
        Task<PurchaseOrderDetailDto> GetPurchaseOrderByIdAsync(int id);

        /// <summary>
        /// 创建采购订单
        /// </summary>
        /// <param name="request">创建请求</param>
        /// <returns>创建的订单</returns>
        Task<PurchaseOrderDto> CreatePurchaseOrderAsync(CreatePurchaseOrderRequest request);

        /// <summary>
        /// 更新采购订单
        /// </summary>
        /// <param name="id">订单ID</param>
        /// <param name="request">更新请求</param>
        /// <returns>更新结果</returns>
        Task<PurchaseOrderDto> UpdatePurchaseOrderAsync(int id, UpdatePurchaseOrderRequest request);

        /// <summary>
        /// 删除采购订单
        /// </summary>
        /// <param name="id">订单ID</param>
        /// <returns>删除结果</returns>
        Task<bool> DeletePurchaseOrderAsync(int id);

        /// <summary>
        /// 确认采购订单到货并处理转化
        /// </summary>
        /// <param name="id">订单ID</param>
        /// <param name="request">到货确认请求</param>
        /// <returns>处理结果</returns>
        Task<PurchaseDeliveryResultDto> ConfirmDeliveryAsync(int id, ConfirmDeliveryRequest request);

        /// <summary>
        /// 处理采购物品到货（转换为资产或备件）
        /// </summary>
        /// <param name="orderId">订单ID</param>
        /// <param name="items">物品处理列表</param>
        /// <returns>处理结果</returns>
        Task<PurchaseProcessResultDto> ProcessDeliveredItemsAsync(int orderId, List<ProcessDeliveredItemRequest> items);

        /// <summary>
        /// 获取供应商列表
        /// </summary>
        /// <returns>供应商列表</returns>
        Task<List<SupplierDto>> GetSuppliersAsync();

        /// <summary>
        /// 获取可采购的资产类型列表（从备件类型读取）
        /// </summary>
        /// <returns>资产类型列表</returns>
        Task<List<AssetTypeDto>> GetPurchasableAssetTypesAsync();

        /// <summary>
        /// 从采购物品创建备品备件
        /// </summary>
        /// <param name="purchaseItem">采购物品</param>
        /// <param name="quantity">转为备件的数量</param>
        /// <param name="locationId">库位ID</param>
        /// <returns>创建的备件ID</returns>
        Task<long> CreateSparePartFromPurchaseAsync(PurchaseItem purchaseItem, int quantity, long locationId);

        /// <summary>
        /// 从采购物品创建资产
        /// </summary>
        /// <param name="purchaseItem">采购物品</param>
        /// <param name="quantity">转为资产的数量</param>
        /// <param name="locationId">资产位置ID</param>
        /// <returns>创建的资产ID列表</returns>
        Task<List<int>> CreateAssetsFromPurchaseAsync(PurchaseItem purchaseItem, int quantity, int locationId);
    }
}
