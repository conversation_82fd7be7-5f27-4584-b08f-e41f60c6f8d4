using System;
using System.Collections.Concurrent;
using System.Threading.Tasks;

namespace ItAssetsSystem.Api.Import
{
    public class ImportResultStore : IImportResultStore
    {
        private readonly ConcurrentDictionary<string, ImportResult> _results = new ConcurrentDictionary<string, ImportResult>();

        public Task SaveResultAsync(string importId, ImportResult result)
        {
            _results.AddOrUpdate(importId, result, (_, __) => result);
            return Task.CompletedTask;
        }

        public Task<ImportResult> GetResultAsync(string importId)
        {
            if (_results.TryGetValue(importId, out var result))
            {
                return Task.FromResult(result);
            }
            return Task.FromResult<ImportResult>(null);
        }
    }
} 