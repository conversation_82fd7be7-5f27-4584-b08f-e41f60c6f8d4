import{_ as l,r as a,z as e,b as s,d as u,e as o,w as n,$ as i,t,a as d,o as r,p}from"./index-CG5lHOPO.js";const c={class:"spare-part-quick-ops-view"},m={class:"ops-type-selector"},v={class:"ops-form"},b={class:"form-item"},f={key:0,class:"form-item"},_={class:"form-actions"},k=l({__name:"SparePartQuickOpsView",setup(l){const k=a("inbound"),y=()=>{};return e((()=>{})),(l,a)=>{const e=d("el-radio-button"),V=d("el-radio-group"),g=d("el-button");return r(),s("div",c,[a[7]||(a[7]=u("div",{class:"ops-header"},[u("h2",null,"快速出入库"),u("p",null,"专为移动端设计，简化操作流程。")],-1)),u("div",m,[o(V,{modelValue:k.value,"onUpdate:modelValue":a[0]||(a[0]=l=>k.value=l),size:"large"},{default:n((()=>[o(e,{label:"inbound"},{default:n((()=>a[1]||(a[1]=[p("入库")]))),_:1}),o(e,{label:"outbound"},{default:n((()=>a[2]||(a[2]=[p("出库")]))),_:1})])),_:1},8,["modelValue"])]),u("div",v,[a[5]||(a[5]=u("div",{class:"form-item"},[u("label",null,"备件选择:"),u("p",null,"交互: 点击后弹出搜索框，或提供扫码按钮。")],-1)),a[6]||(a[6]=u("div",{class:"form-item"},[u("label",null,"数量:"),u("p",null,"交互: 数字输入框，可带加减按钮。")],-1)),u("div",b,[u("label",null,t("inbound"===k.value?"入库到仓库/库位:":"从仓库/库位出库:"),1),a[3]||(a[3]=u("p",null,"交互: 简洁的选择器，可能需要分步选择仓库再选库位。",-1))]),"outbound"===k.value?(r(),s("div",f,a[4]||(a[4]=[u("label",null,"领用人/关联信息:",-1),u("p",null,"交互: 文本输入或选择器。",-1)]))):i("",!0),u("div",_,[o(g,{type:"primary",size:"large",onClick:y},{default:n((()=>[p("确认"+t("inbound"===k.value?"入库":"出库"),1)])),_:1})])]),a[8]||(a[8]=u("div",{class:"recent-ops"},[u("h3",null,"最近操作记录 (仅示意)"),u("ul",null,[u("li",null,"入库：CPU * 5 到 主仓库-A01 (张三)"),u("li",null,"出库：内存条 * 2 从 主仓库-B02 (李四，用于维修单号 XXX)")])],-1))])}}},[["__scopeId","data-v-8baa2881"]]);export{k as default};
