# 🛠️ 开发规范与最佳实践

## 📋 编码规范

### C# 后端编码规范

#### 1. 命名约定
```csharp
// ✅ 正确的命名风格
public class TaskService                    // 类名：PascalCase
{
    private readonly ITaskRepository _repository;  // 私有字段：_camelCase
    public string TaskName { get; set; }          // 属性：PascalCase
    
    public async Task<TaskDto> CreateTaskAsync(CreateTaskRequest request)  // 方法：PascalCase
    {
        var newTask = new Domain.Entities.Tasks.Task(...);  // 局部变量：camelCase
        const int MAX_RETRY_COUNT = 3;                      // 常量：UPPER_CASE
    }
}

// ❌ 错误的命名风格
public class taskservice                    // 应该是PascalCase
{
    private readonly ITaskRepository repo; // 应该用_repository
    public string taskName { get; set; }   // 应该是PascalCase
}
```

#### 2. 文件组织结构
```csharp
// 文件头部注释标准格式
// File: Application/Features/Tasks/Services/TaskService.cs
// Description: 任务管理核心业务逻辑服务
// Created: 2025-06-21
// Author: 开发者姓名

using System;
using System.Threading.Tasks;
// 系统命名空间在前

using ItAssetsSystem.Domain.Entities.Tasks;
using ItAssetsSystem.Application.Common.Interfaces;
// 项目命名空间在后，按字母排序

namespace ItAssetsSystem.Application.Features.Tasks.Services
{
    /// <summary>
    /// 任务管理服务
    /// 提供任务的创建、更新、分配等核心业务功能
    /// </summary>
    public class TaskService : ITaskService
    {
        // 1. 私有字段
        private readonly ITaskRepository _taskRepository;
        private readonly ICurrentUserService _currentUser;
        
        // 2. 构造函数
        public TaskService(ITaskRepository taskRepository, ICurrentUserService currentUser)
        {
            _taskRepository = taskRepository;
            _currentUser = currentUser;
        }
        
        // 3. 公共方法
        public async Task<TaskDto> CreateTaskAsync(CreateTaskRequest request)
        {
            // 实现逻辑
        }
        
        // 4. 私有方法
        private void ValidateTaskRequest(CreateTaskRequest request)
        {
            // 验证逻辑
        }
    }
}
```

#### 3. 异步编程规范
```csharp
// ✅ 正确的异步模式
public async Task<List<TaskDto>> GetTasksAsync(CancellationToken cancellationToken = default)
{
    var tasks = await _repository.GetTasksAsync(cancellationToken);
    return tasks.Select(t => t.ToDto()).ToList();
}

// ✅ 正确的异常处理
public async Task<Result<TaskDto>> CreateTaskAsync(CreateTaskRequest request)
{
    try
    {
        var task = new Task(request.Name, request.Description);
        await _repository.AddAsync(task);
        
        return Result<TaskDto>.Success(task.ToDto());
    }
    catch (DomainException ex)
    {
        _logger.LogWarning(ex, "业务规则违反: {Message}", ex.Message);
        return Result<TaskDto>.Failure(ex.Message);
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "创建任务时发生未知错误");
        return Result<TaskDto>.Failure("系统错误，请稍后重试");
    }
}

// ❌ 错误的异步模式
public Task<List<TaskDto>> GetTasksAsync()
{
    return Task.Run(() => {  // 不要用Task.Run包装同步代码
        var tasks = _repository.GetTasks(); // 同步调用
        return tasks.Select(t => t.ToDto()).ToList();
    });
}
```

### Vue.js 前端编码规范

#### 1. 组件命名与结构
```vue
<!-- ✅ 正确的组件结构 -->
<template>
  <div class="task-list-view">
    <div class="task-header">
      <h2 class="header-title">任务管理</h2>
      <el-button type="primary" @click="handleCreateTask">
        <el-icon><Plus /></el-icon>
        创建任务
      </el-button>
    </div>
    
    <div class="task-content">
      <task-filter @filter-change="handleFilterChange" />
      <task-table :tasks="filteredTasks" @task-select="handleTaskSelect" />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import TaskFilter from './components/TaskFilter.vue'
import TaskTable from './components/TaskTable.vue'

// 响应式数据
const tasks = ref([])
const filterCriteria = ref({})

// 计算属性
const filteredTasks = computed(() => {
  return tasks.value.filter(task => {
    // 过滤逻辑
  })
})

// 方法
const handleCreateTask = () => {
  // 创建任务逻辑
}

const handleFilterChange = (criteria) => {
  filterCriteria.value = criteria
}

// 生命周期
onMounted(async () => {
  await loadTasks()
})
</script>

<style scoped>
.task-list-view {
  padding: 20px;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-title {
  margin: 0;
  color: #303133;
}
</style>
```

#### 2. API调用规范
```javascript
// ✅ 正确的API调用模式
import { taskApi } from '@/api/task'
import { ElMessage } from 'element-plus'

export const useTaskManagement = () => {
  const loading = ref(false)
  const tasks = ref([])
  
  const loadTasks = async (params = {}) => {
    loading.value = true
    try {
      const response = await taskApi.getTasks(params)
      if (response.success) {
        tasks.value = response.data
      } else {
        ElMessage.error(response.message || '获取任务列表失败')
      }
    } catch (error) {
      console.error('获取任务列表错误:', error)
      ElMessage.error('网络错误，请稍后重试')
    } finally {
      loading.value = false
    }
  }
  
  const createTask = async (taskData) => {
    try {
      const response = await taskApi.createTask(taskData)
      if (response.success) {
        ElMessage.success('任务创建成功')
        await loadTasks() // 刷新列表
        return response.data
      } else {
        ElMessage.error(response.message || '创建任务失败')
        return null
      }
    } catch (error) {
      console.error('创建任务错误:', error)
      ElMessage.error('创建任务失败')
      return null
    }
  }
  
  return {
    loading: readonly(loading),
    tasks: readonly(tasks),
    loadTasks,
    createTask
  }
}
```

## 🏗️ 架构设计原则

### Clean Architecture 实施标准

#### 1. 依赖方向规则
```
External → Infrastructure → Application → Domain
```

```csharp
// ✅ 正确：Application层依赖Domain层接口
namespace ItAssetsSystem.Application.Features.Tasks
{
    public class TaskService
    {
        private readonly ITaskRepository _repository; // Domain层接口
        
        public async Task<TaskDto> CreateTaskAsync(CreateTaskCommand command)
        {
            var task = new Task(command.Name); // Domain层实体
            await _repository.AddAsync(task);
            return task.ToDto();
        }
    }
}

// ❌ 错误：Domain层不应依赖Infrastructure层
namespace ItAssetsSystem.Domain.Entities.Tasks
{
    public class Task
    {
        public void Save()
        {
            var context = new AppDbContext(); // 违反依赖方向
            context.Tasks.Add(this);
        }
    }
}
```

#### 2. CQRS模式实施
```csharp
// 命令（写操作）
public class CreateTaskCommand : IRequest<TaskDto>
{
    public string Name { get; set; }
    public string Description { get; set; }
    public int AssigneeUserId { get; set; }
}

public class CreateTaskCommandHandler : IRequestHandler<CreateTaskCommand, TaskDto>
{
    public async Task<TaskDto> Handle(CreateTaskCommand request, CancellationToken cancellationToken)
    {
        // 验证 → 创建实体 → 保存 → 返回DTO
    }
}

// 查询（读操作）
public class GetTaskListQuery : IRequest<PaginatedResult<TaskDto>>
{
    public int PageIndex { get; set; }
    public int PageSize { get; set; }
    public string Status { get; set; }
}

public class GetTaskListQueryHandler : IRequestHandler<GetTaskListQuery, PaginatedResult<TaskDto>>
{
    public async Task<PaginatedResult<TaskDto>> Handle(GetTaskListQuery request, CancellationToken cancellationToken)
    {
        // 查询逻辑，只读操作
    }
}
```

### 3. 事件驱动设计
```csharp
// 领域事件定义
public class TaskCompletedEvent : IDomainEvent
{
    public long TaskId { get; }
    public int CompletedByUserId { get; }
    public DateTime CompletedAt { get; }
    
    public TaskCompletedEvent(long taskId, int userId, DateTime completedAt)
    {
        TaskId = taskId;
        CompletedByUserId = userId;
        CompletedAt = completedAt;
    }
}

// 领域实体中发布事件
public class Task : AggregateRoot<long>
{
    public void Complete(int userId)
    {
        if (Status == TaskStatus.Completed)
            throw new DomainException("任务已完成");
            
        Status = TaskStatus.Completed;
        ActualEndDate = DateTime.UtcNow;
        
        // 发布领域事件
        AddDomainEvent(new TaskCompletedEvent(TaskId, userId, ActualEndDate.Value));
    }
}

// 事件处理器
public class TaskCompletedEventHandler : INotificationHandler<TaskCompletedEvent>
{
    public async Task Handle(TaskCompletedEvent notification, CancellationToken cancellationToken)
    {
        // 为用户增加积分
        await _gamificationService.AddPointsAsync(notification.CompletedByUserId, 10);
        
        // 发送通知
        await _notificationService.NotifyTaskCompletedAsync(notification.TaskId);
    }
}
```

## 🗄️ 数据库开发规范

### 1. 迁移文件规范
```csharp
// ✅ 正确的迁移文件命名和结构
public partial class AddTaskCompletionTracking : Migration
{
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        // 1. 创建表
        migrationBuilder.CreateTable(
            name: "task_completion_logs",
            columns: table => new
            {
                id = table.Column<long>(type: "bigint", nullable: false)
                    .Annotation("MySql:ValueGenerationStrategy", MySqlValueGenerationStrategy.IdentityColumn),
                task_id = table.Column<long>(type: "bigint", nullable: false),
                completed_by_user_id = table.Column<int>(type: "int", nullable: false),
                completed_at = table.Column<DateTime>(type: "datetime(6)", nullable: false),
                completion_notes = table.Column<string>(type: "longtext", nullable: true),
                created_at = table.Column<DateTime>(type: "datetime(6)", nullable: false)
            },
            constraints: table =>
            {
                table.PrimaryKey("PK_task_completion_logs", x => x.id);
                table.ForeignKey(
                    name: "FK_task_completion_logs_tasks_task_id",
                    column: x => x.task_id,
                    principalTable: "tasks",
                    principalColumn: "TaskId",
                    onDelete: ReferentialAction.Cascade);
            });

        // 2. 创建索引
        migrationBuilder.CreateIndex(
            name: "IX_task_completion_logs_task_id",
            table: "task_completion_logs",
            column: "task_id");
    }

    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropTable(name: "task_completion_logs");
    }
}
```

### 2. Entity Configuration规范
```csharp
public class TaskConfiguration : IEntityTypeConfiguration<Task>
{
    public void Configure(EntityTypeBuilder<Task> builder)
    {
        // 表名配置
        builder.ToTable("tasks");
        
        // 主键配置
        builder.HasKey(t => t.TaskId);
        builder.Property(t => t.TaskId).HasColumnName("TaskId");
        
        // 属性配置
        builder.Property(t => t.Name)
            .IsRequired()
            .HasMaxLength(255)
            .HasColumnName("Name");
            
        builder.Property(t => t.Status)
            .IsRequired()
            .HasMaxLength(50)
            .HasColumnName("Status")
            .HasConversion<string>(); // 枚举转字符串
        
        // 外键关系
        builder.HasOne<User>()
            .WithMany()
            .HasForeignKey(t => t.AssigneeUserId)
            .OnDelete(DeleteBehavior.SetNull);
            
        // 索引配置
        builder.HasIndex(t => new { t.Status, t.AssigneeUserId })
            .HasDatabaseName("IX_Tasks_Status_Assignee");
    }
}
```

## 🧪 测试规范

### 1. 单元测试规范
```csharp
[TestClass]
public class TaskServiceTests
{
    private readonly Mock<ITaskRepository> _mockRepository;
    private readonly Mock<ICurrentUserService> _mockCurrentUser;
    private readonly TaskService _taskService;

    public TaskServiceTests()
    {
        _mockRepository = new Mock<ITaskRepository>();
        _mockCurrentUser = new Mock<ICurrentUserService>();
        _taskService = new TaskService(_mockRepository.Object, _mockCurrentUser.Object);
    }

    [TestMethod]
    public async Task CreateTaskAsync_ValidRequest_ShouldReturnTaskDto()
    {
        // Arrange
        var request = new CreateTaskRequest
        {
            Name = "测试任务",
            Description = "这是一个测试任务"
        };
        
        _mockCurrentUser.Setup(x => x.UserId).Returns(1);
        _mockRepository.Setup(x => x.AddAsync(It.IsAny<Task>()))
            .Returns(Task.CompletedTask);

        // Act
        var result = await _taskService.CreateTaskAsync(request);

        // Assert
        Assert.IsNotNull(result);
        Assert.IsTrue(result.Success);
        Assert.AreEqual(request.Name, result.Data.Name);
        
        _mockRepository.Verify(x => x.AddAsync(It.IsAny<Task>()), Times.Once);
    }

    [TestMethod]
    public async Task CreateTaskAsync_EmptyName_ShouldThrowDomainException()
    {
        // Arrange
        var request = new CreateTaskRequest { Name = "", Description = "描述" };

        // Act & Assert
        await Assert.ThrowsExceptionAsync<DomainException>(
            () => _taskService.CreateTaskAsync(request));
    }
}
```

### 2. 前端测试规范
```javascript
// Vue组件测试
import { mount } from '@vue/test-utils'
import { describe, it, expect, vi } from 'vitest'
import TaskList from '@/views/tasks/TaskList.vue'

describe('TaskList.vue', () => {
  it('should render task list correctly', async () => {
    // Arrange
    const mockTasks = [
      { id: 1, name: '任务1', status: 'Todo' },
      { id: 2, name: '任务2', status: 'InProgress' }
    ]
    
    // Act
    const wrapper = mount(TaskList, {
      props: { tasks: mockTasks }
    })

    // Assert
    expect(wrapper.findAll('.task-item')).toHaveLength(2)
    expect(wrapper.text()).toContain('任务1')
    expect(wrapper.text()).toContain('任务2')
  })

  it('should emit task-select event when task is clicked', async () => {
    // Arrange
    const mockTasks = [{ id: 1, name: '任务1', status: 'Todo' }]
    const wrapper = mount(TaskList, {
      props: { tasks: mockTasks }
    })

    // Act
    await wrapper.find('.task-item').trigger('click')

    // Assert
    expect(wrapper.emitted('task-select')).toBeTruthy()
    expect(wrapper.emitted('task-select')[0]).toEqual([mockTasks[0]])
  })
})
```

## 📝 文档规范

### 1. API文档注释
```csharp
/// <summary>
/// 创建新任务
/// </summary>
/// <param name="request">任务创建请求</param>
/// <returns>创建成功的任务信息</returns>
/// <response code="200">任务创建成功</response>
/// <response code="400">请求参数无效</response>
/// <response code="401">用户未认证</response>
/// <response code="500">服务器内部错误</response>
[HttpPost]
[ProducesResponseType(typeof(ApiResponse<TaskDto>), StatusCodes.Status200OK)]
[ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status400BadRequest)]
public async Task<ActionResult<ApiResponse<TaskDto>>> CreateTask([FromBody] CreateTaskRequest request)
{
    if (!ModelState.IsValid)
        return BadRequest(ApiResponse.Failure("请求参数无效", ModelState));

    var result = await _taskService.CreateTaskAsync(request);
    
    return result.Success 
        ? Ok(ApiResponse.Success(result.Data, "任务创建成功"))
        : BadRequest(ApiResponse.Failure(result.ErrorMessage));
}
```

### 2. 组件文档注释
```vue
<template>
  <!-- 任务卡片组件模板 -->
</template>

<script setup>
/**
 * TaskCard 组件
 * 
 * @description 显示单个任务的卡片组件，支持点击选择和状态显示
 * 
 * @props {Object} task - 任务对象
 * @props {Boolean} selected - 是否选中状态
 * 
 * @emits {Function} task-select - 任务被选中时触发
 * @emits {Function} status-change - 任务状态改变时触发
 * 
 * @example
 * <TaskCard 
 *   :task="taskData" 
 *   :selected="selectedTaskId === taskData.id"
 *   @task-select="handleTaskSelect"
 *   @status-change="handleStatusChange"
 * />
 */

defineProps({
  task: {
    type: Object,
    required: true,
    validator: (task) => {
      return task && typeof task.id !== 'undefined' && task.name
    }
  },
  selected: {
    type: Boolean,
    default: false
  }
})

defineEmits(['task-select', 'status-change'])
</script>
```

## 🔧 开发工具配置

### 1. ESLint配置 (.eslintrc.js)
```javascript
module.exports = {
  extends: [
    '@vue/standard',
    '@vue/typescript/recommended'
  ],
  rules: {
    // Vue特定规则
    'vue/component-name-in-template-casing': ['error', 'kebab-case'],
    'vue/max-attributes-per-line': ['error', { singleline: 3 }],
    
    // JavaScript规则
    'quotes': ['error', 'single'],
    'semi': ['error', 'never'],
    'comma-dangle': ['error', 'never'],
    
    // 自定义规则
    'no-console': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'no-debugger': process.env.NODE_ENV === 'production' ? 'warn' : 'off'
  }
}
```

### 2. EditorConfig配置 (.editorconfig)
```ini
root = true

[*]
charset = utf-8
end_of_line = lf
insert_final_newline = true
trim_trailing_whitespace = true
indent_style = space
indent_size = 2

[*.{cs,csx}]
indent_size = 4

[*.md]
trim_trailing_whitespace = false
```

## 🚀 部署与发布规范

### 1. 环境配置管理
```json
// appsettings.Development.json
{
  "ConnectionStrings": {
    "DefaultConnection": "Data Source=itassets_dev.db"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Debug",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "JwtSettings": {
    "Key": "development-secret-key-change-in-production",
    "Issuer": "ItAssetsSystem",
    "Audience": "ItAssetsSystem",
    "ExpirationMinutes": 60
  }
}

// appsettings.Production.json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=prod-server;Database=itassets;Uid=user;Pwd=password;"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  }
}
```

### 2. Docker配置
```dockerfile
# Dockerfile
FROM mcr.microsoft.com/dotnet/aspnet:6.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

FROM mcr.microsoft.com/dotnet/sdk:6.0 AS build
WORKDIR /src
COPY ["ItAssetsSystem.csproj", "."]
RUN dotnet restore
COPY . .
WORKDIR "/src"
RUN dotnet build -c Release -o /app/build

FROM build AS publish
RUN dotnet publish -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "ItAssetsSystem.dll"]
```

---

**开发规范版本**: v1.0  
**最后更新**: 2025年6月21日  
**维护者**: 技术架构团队