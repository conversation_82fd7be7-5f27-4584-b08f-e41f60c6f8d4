// IT资产管理系统 - 资产历史记录实体
// 文件路径: /Models/Entities/AssetHistory.cs
// 功能: 定义资产历史记录，记录资产属性变更历史

using System;
using ItAssetsSystem.Models;

namespace ItAssetsSystem.Models.Entities
{
    /// <summary>
    /// 资产历史记录实体
    /// </summary>
    public class AssetHistory : IAuditableEntity
    {
        /// <summary>
        /// 资产历史ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 资产ID
        /// </summary>
        public int AssetId { get; set; }

        /// <summary>
        /// 操作类型（1:创建, 2:修改, 3:删除, 4:位置变更, 5:状态变更）
        /// </summary>
        public int OperationType { get; set; }

        /// <summary>
        /// 操作人ID
        /// </summary>
        public int OperatorId { get; set; }

        /// <summary>
        /// 操作时间
        /// </summary>
        public DateTime OperationTime { get; set; }

        /// <summary>
        /// 描述（JSON格式，记录变更前后的属性值）
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; }

        /// <summary>
        /// 资产
        /// </summary>
        public virtual Asset Asset { get; set; }

        /// <summary>
        /// 操作人
        /// </summary>
        public virtual User Operator { get; set; }
    }
} 