<!DOCTYPE html>
<html>
<head>
    <title>测试任务更新功能</title>
    <meta charset="UTF-8">
</head>
<body>
    <h1>测试任务更新功能</h1>
    
    <div>
        <h2>当前任务信息</h2>
        <div id="current-task"></div>
    </div>
    
    <div>
        <h2>更新任务分类</h2>
        <button onclick="updateTaskCategory()">更新任务分类为"设备维护"</button>
        <div id="update-result"></div>
    </div>
    
    <div>
        <h2>检查统计数据</h2>
        <button onclick="checkStatistics()">检查工作汇总统计</button>
        <div id="statistics-result"></div>
    </div>
    
    <div>
        <h2>检查行为事件</h2>
        <button onclick="checkBehaviorEvents()">检查TASK_UPDATED事件</button>
        <div id="events-result"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5001/api/v2';
        
        // 获取当前任务信息
        async function getCurrentTask() {
            try {
                const response = await fetch(`${API_BASE}/tasks/1`);
                const data = await response.json();
                document.getElementById('current-task').innerHTML = `
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                document.getElementById('current-task').innerHTML = `错误: ${error.message}`;
            }
        }
        
        // 更新任务分类
        async function updateTaskCategory() {
            try {
                const updateData = {
                    categoryId: 1, // 假设分类ID为1是"设备维护"
                    name: "测试任务",
                    description: "测试任务更新功能 - 已更新分类"
                };
                
                const response = await fetch(`${API_BASE}/tasks/1`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(updateData)
                });
                
                const result = await response.json();
                document.getElementById('update-result').innerHTML = `
                    <h3>更新结果:</h3>
                    <pre>${JSON.stringify(result, null, 2)}</pre>
                `;
                
                // 自动刷新当前任务信息
                setTimeout(getCurrentTask, 1000);
                
            } catch (error) {
                document.getElementById('update-result').innerHTML = `错误: ${error.message}`;
            }
        }
        
        // 检查统计数据
        async function checkStatistics() {
            try {
                const response = await fetch(`${API_BASE}/work-summary?periodType=weekly&limit=5`);
                const data = await response.json();
                
                // 查找包含tasksUpdated > 0的用户
                const usersWithUpdates = data.data.filter(user => user.tasksUpdated > 0);
                
                document.getElementById('statistics-result').innerHTML = `
                    <h3>统计结果:</h3>
                    <p>有任务更新记录的用户数: ${usersWithUpdates.length}</p>
                    <pre>${JSON.stringify(usersWithUpdates, null, 2)}</pre>
                    <h4>所有用户统计:</h4>
                    <pre>${JSON.stringify(data.data.map(u => ({
                        userName: u.userName,
                        tasksUpdated: u.tasksUpdated,
                        tasksTotal: u.tasksTotal
                    })), null, 2)}</pre>
                `;
            } catch (error) {
                document.getElementById('statistics-result').innerHTML = `错误: ${error.message}`;
            }
        }
        
        // 检查行为事件（这需要数据库查询，我们用一个模拟的方式）
        async function checkBehaviorEvents() {
            document.getElementById('events-result').innerHTML = `
                <h3>行为事件检查:</h3>
                <p>请在数据库中执行以下查询来检查TASK_UPDATED事件:</p>
                <pre>SELECT COUNT(*) as total_events FROM user_behavior_events WHERE action_type = 'TASK_UPDATED';</pre>
                <pre>SELECT user_id, action_type, reference_id, points_earned, timestamp FROM user_behavior_events WHERE action_type = 'TASK_UPDATED' ORDER BY timestamp DESC LIMIT 5;</pre>
            `;
        }
        
        // 页面加载时获取当前任务信息
        window.onload = getCurrentTask;
    </script>
</body>
</html>
