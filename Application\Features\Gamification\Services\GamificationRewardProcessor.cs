// File: Application/Features/Gamification/Services/GamificationRewardProcessor.cs
// Description: 游戏化奖励处理器实现

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using ItAssetsSystem.Infrastructure.Data;
using ItAssetsSystem.Models;
using ItAssetsSystem.Models.Entities.Gamification;
using ItAssetsSystem.Models.DTOs.Gamification;

namespace ItAssetsSystem.Application.Features.Gamification.Services
{
    /// <summary>
    /// 游戏化奖励处理器实现
    /// </summary>
    public class GamificationRewardProcessor : IGamificationRewardProcessor
    {
        private readonly AppDbContext _context;
        private readonly ILogger<GamificationRewardProcessor> _logger;

        public GamificationRewardProcessor(
            AppDbContext context,
            ILogger<GamificationRewardProcessor> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// 处理奖励发放
        /// </summary>
        public async Task<bool> ProcessRewardAsync(int userId, GamificationReward reward, string eventSource, long? referenceId = null)
        {
            if (!reward.HasReward)
            {
                _logger.LogDebug("用户 {UserId} 无奖励需要发放", userId);
                return true;
            }

            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                // 获取或创建用户统计
                var userStats = await GetOrCreateUserStatsAsync(userId);

                // 更新用户余额
                userStats.PointsBalance += reward.Points;
                userStats.CoinsBalance += reward.Coins;
                userStats.DiamondsBalance += reward.Diamonds;
                userStats.CurrentXP += reward.Experience;
                userStats.LastActivityTimestamp = DateTime.UtcNow;
                userStats.LastUpdatedTimestamp = DateTime.UtcNow;

                // 更新统计计数
                await UpdateUserStatsCountsAsync(userStats, eventSource);

                // 记录游戏化日志
                await CreateGamificationLogAsync(userStats.UserId, reward, eventSource, referenceId);

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();

                _logger.LogInformation("奖励发放成功: UserId={UserId}, Points={Points}, Coins={Coins}, Diamonds={Diamonds}, Source={Source}", 
                    userId, reward.Points, reward.Coins, reward.Diamonds, eventSource);

                return true;
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, "处理奖励发放时发生错误: UserId={UserId}, Source={Source}", userId, eventSource);
                return false;
            }
        }

        /// <summary>
        /// 批量处理奖励
        /// </summary>
        public async Task<Dictionary<int, bool>> ProcessBatchRewardsAsync(IEnumerable<(int UserId, GamificationReward Reward, string EventSource, long? ReferenceId)> rewards)
        {
            var results = new Dictionary<int, bool>();
            var rewardsList = rewards.ToList();

            if (!rewardsList.Any())
            {
                return results;
            }

            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                foreach (var (userId, reward, eventSource, referenceId) in rewardsList)
                {
                    try
                    {
                        if (!reward.HasReward)
                        {
                            results[userId] = true;
                            continue;
                        }

                        // 获取或创建用户统计
                        var userStats = await GetOrCreateUserStatsAsync(userId);

                        // 更新用户余额
                        userStats.PointsBalance += reward.Points;
                        userStats.CoinsBalance += reward.Coins;
                        userStats.DiamondsBalance += reward.Diamonds;
                        userStats.CurrentXP += reward.Experience;
                        userStats.LastActivityTimestamp = DateTime.UtcNow;
                        userStats.LastUpdatedTimestamp = DateTime.UtcNow;

                        // 更新统计计数
                        await UpdateUserStatsCountsAsync(userStats, eventSource);

                        // 记录游戏化日志
                        await CreateGamificationLogAsync(userStats.UserId, reward, eventSource, referenceId);

                        results[userId] = true;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "批量处理奖励时发生错误: UserId={UserId}, Source={EventSource}", userId, eventSource);
                        results[userId] = false;
                    }
                }

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();

                _logger.LogInformation("批量奖励处理完成: 总数={Total}, 成功={Success}, 失败={Failed}", 
                    rewardsList.Count, results.Count(r => r.Value), results.Count(r => !r.Value));

                return results;
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, "批量处理奖励时发生错误");
                
                // 返回全部失败的结果
                foreach (var (userId, _, _, _) in rewardsList)
                {
                    results[userId] = false;
                }
                return results;
            }
        }

        /// <summary>
        /// 撤销奖励
        /// </summary>
        public async Task<bool> RevokeRewardAsync(int userId, string eventSource, long? referenceId)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                // 查找相关的游戏化日志
                var logs = await _context.GamificationLogs
                    .Where(gl => gl.UserId == userId && 
                                gl.EventType == eventSource && 
                                gl.ReferenceId == referenceId)
                    .ToListAsync();

                if (!logs.Any())
                {
                    _logger.LogWarning("未找到需要撤销的奖励记录: UserId={UserId}, Source={Source}, ReferenceId={ReferenceId}", 
                        userId, eventSource, referenceId);
                    return true;
                }

                // 获取用户统计
                var userStats = await _context.GamificationUserStats
                    .FirstOrDefaultAsync(gus => gus.CoreUserId == userId);

                if (userStats == null)
                {
                    _logger.LogWarning("用户统计不存在: UserId={UserId}", userId);
                    return false;
                }

                // 计算需要撤销的总奖励
                var totalPointsToRevoke = logs.Sum(l => l.PointsGained);
                var totalCoinsToRevoke = logs.Sum(l => l.CoinsGained);
                var totalDiamondsToRevoke = logs.Sum(l => l.DiamondsGained);
                var totalXPToRevoke = logs.Sum(l => l.XPChange);

                // 撤销奖励（确保不会变成负数）
                userStats.PointsBalance = Math.Max(0, userStats.PointsBalance - totalPointsToRevoke);
                userStats.CoinsBalance = Math.Max(0, userStats.CoinsBalance - totalCoinsToRevoke);
                userStats.DiamondsBalance = Math.Max(0, userStats.DiamondsBalance - totalDiamondsToRevoke);
                userStats.CurrentXP = Math.Max(0, userStats.CurrentXP - totalXPToRevoke);
                userStats.LastUpdatedTimestamp = DateTime.UtcNow;

                // 记录撤销日志
                var revokeLog = new GamificationLog
                {
                    UserId = userStats.UserId,
                    EventType = $"{eventSource}_REVOKED",
                    PointsGained = -totalPointsToRevoke,
                    CoinsGained = -totalCoinsToRevoke,
                    DiamondsGained = -totalDiamondsToRevoke,
                    XPChange = -totalXPToRevoke,
                    Description = $"撤销奖励: {eventSource}",
                    ReferenceId = referenceId,
                    Timestamp = DateTime.UtcNow
                };

                _context.GamificationLogs.Add(revokeLog);

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();

                _logger.LogInformation("奖励撤销成功: UserId={UserId}, Points={Points}, Coins={Coins}, Diamonds={Diamonds}, Source={Source}", 
                    userId, totalPointsToRevoke, totalCoinsToRevoke, totalDiamondsToRevoke, eventSource);

                return true;
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, "撤销奖励时发生错误: UserId={UserId}, Source={Source}", userId, eventSource);
                return false;
            }
        }

        #region 私有方法

        /// <summary>
        /// 获取或创建用户统计
        /// </summary>
        private async Task<GamificationUserStats> GetOrCreateUserStatsAsync(int userId)
        {
            var userStats = await _context.GamificationUserStats
                .FirstOrDefaultAsync(gus => gus.CoreUserId == userId);

            if (userStats == null)
            {
                userStats = new GamificationUserStats
                {
                    UserId = userId, // 这里可能需要调整，根据实际的UserId生成逻辑
                    CoreUserId = userId,
                    CurrentXP = 0,
                    CurrentLevel = 1,
                    PointsBalance = 0,
                    CoinsBalance = 0,
                    DiamondsBalance = 0,
                    CompletedTasksCount = 0,
                    CreatedTasksCount = 0,
                    ClaimedTasksCount = 0,
                    OnTimeTasksCount = 0,
                    StreakCount = 0,
                    LastActivityTimestamp = DateTime.UtcNow,
                    LastUpdatedTimestamp = DateTime.UtcNow
                };

                _context.GamificationUserStats.Add(userStats);
                await _context.SaveChangesAsync(); // 保存以获取生成的UserId
            }

            return userStats;
        }

        /// <summary>
        /// 更新用户统计计数
        /// </summary>
        private async Task UpdateUserStatsCountsAsync(GamificationUserStats userStats, string eventSource)
        {
            switch (eventSource)
            {
                case "TaskCreated":
                    userStats.CreatedTasksCount++;
                    break;
                case "TaskCompleted":
                    userStats.CompletedTasksCount++;
                    break;
                case "TaskClaimed":
                    userStats.ClaimedTasksCount++;
                    break;
            }

            await Task.CompletedTask;
        }

        /// <summary>
        /// 创建游戏化日志
        /// </summary>
        private async Task CreateGamificationLogAsync(long userStatsId, GamificationReward reward, string eventSource, long? referenceId)
        {
            var log = new GamificationLog
            {
                UserId = userStatsId,
                EventType = eventSource,
                PointsGained = reward.Points,
                CoinsGained = reward.Coins,
                DiamondsGained = reward.Diamonds,
                XPChange = reward.Experience,
                PointsChange = reward.Points, // 兼容旧字段
                Description = reward.Description,
                ReferenceId = referenceId,
                Timestamp = DateTime.UtcNow
            };

            _context.GamificationLogs.Add(log);
            await Task.CompletedTask;
        }

        #endregion

        /// <summary>
        /// 处理任务领取奖励
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="taskId">任务ID</param>
        /// <returns>奖励结果</returns>
        public async Task<GamificationRewardDto> ProcessTaskClaimedRewardAsync(int userId, long taskId)
        {
            try
            {
                _logger.LogInformation("开始处理任务领取奖励: UserId={UserId}, TaskId={TaskId}", userId, taskId);

                // 创建默认的任务领取奖励
                var reward = new GamificationReward
                {
                    Points = 3,
                    Coins = 2,
                    Diamonds = 0,
                    Experience = 3,
                    Description = "任务领取奖励"
                };

                // 应用奖励
                var success = await ProcessRewardAsync(userId, reward, "TaskClaimed", taskId);

                var result = new GamificationRewardDto
                {
                    Success = success,
                    Message = success ? "任务领取奖励发放成功" : "任务领取奖励发放失败",
                    PointsGained = success ? reward.Points : 0,
                    XPGained = success ? reward.Experience : 0
                };

                _logger.LogInformation("任务领取奖励处理完成: UserId={UserId}, TaskId={TaskId}, Success={Success}, Points={Points}, XP={XP}",
                    userId, taskId, success, result.PointsGained, result.XPGained);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理任务领取奖励时发生错误: UserId={UserId}, TaskId={TaskId}", userId, taskId);
                return new GamificationRewardDto { Success = false, Message = "处理任务领取奖励失败" };
            }
        }
    }
}
