// File: Application/Features/Tasks/Services/ITaskService.cs
// Description: Interface for the Task Service, defining task management operations.
#nullable enable // Enabling nullable context for the file

using ItAssetsSystem.Application.Common.Dtos;
using ItAssetsSystem.Application.Features.Tasks.Dtos;
// using Microsoft.AspNetCore.Http; // For IFormFile if needed later for attachments directly in interface. Removed for now to avoid unused using.
using System.Collections.Generic;
using System.Threading.Tasks;

// Assuming PaginationParameters and TaskFilterParameters are in a root Models namespace or similar for now
// If they are in ItAssetsSystem.Application.Common.Models, that namespace needs to be created or found.
// For now, using object to bypass linter, will refine later.
// using ItAssetsSystem.Models; // Tentative: Assuming root Models namespace. Replaced by TaskQueryParametersDto

namespace ItAssetsSystem.Application.Features.Tasks.Services
{
    public interface ITaskService
    {
        Task<ApiResponse<List<CommentDto>>> GetTaskCommentsAsync(long taskId);
        Task<ApiResponse<CommentDto>> AddCommentAsync(long taskId, AddCommentRequestDto request, int currentUserId);
        Task<ApiResponse<List<CommentDto>>> GetCommentTreeAsync(long taskId, int maxDepth = 3, string sortBy = "CreationTimestamp", string sortDirection = "asc");

        Task<ApiResponse<TaskDto>> CreateTaskAsync(CreateTaskRequestDto request, int currentUserId);
        Task<ApiResponse<List<TaskDto>>> GetTasksAsync(TaskQueryParametersDto queryParameters);
        Task<ApiResponse<TaskDto>> GetTaskByIdAsync(long id, bool includeComments = false, bool includeAttachments = false, bool includeHistory = false);
        Task<ApiResponse<TaskDto>> UpdateTaskAsync(long taskId, UpdateTaskRequestDto request, int currentUserId);
        Task<ApiResponse<bool>> DeleteTaskAsync(long id, int currentUserId);
        Task<bool> TaskExistsAsync(long taskId);

        Task<ApiResponse<TaskDto>> UpdateTaskStatusAsync(long taskId, string newStatus, string? remarks, int currentUserId);
        Task<ApiResponse<TaskDto>> UpdateTaskProgressAsync(long taskId, int progress, string? remarks, int currentUserId);
        Task<ApiResponse<TaskDto>> AssignTaskAsync(long taskId, int assigneeUserId, string? remarks, int currentUserId);
        Task<ApiResponse<TaskDto>> CompleteTaskAsync(long taskId, string? remarks, int currentUserId); // remarks can be nullable

        Task<ApiResponse<List<AttachmentDto>>> GetTaskAttachmentsAsync(long taskId);
        // For AddAttachmentAsync, the IFormFile makes it tricky for a pure interface if service handles file ops.
        // Often, the file content (byte[]) is passed to the service instead, controller handles IFormFile.
        // TaskService has: AddAttachmentAsync(long taskId, string fileName, byte[] fileContent, string contentType, string? descriptionForHistory, int uploaderUserId)
        Task<ApiResponse<AttachmentDto>> AddAttachmentAsync(long taskId, string fileName, byte[] fileContent, string contentType, string? descriptionForHistory, int uploaderUserId);
        Task<ApiResponse<bool>> DeleteAttachmentAsync(long attachmentId, int currentUserId);

        Task<ApiResponse<List<TaskHistoryDto>>> GetTaskHistoryAsync(long taskId);
        Task AddTaskHistoryAsync(long taskId, string actionType, string description, int userId);

        // Utility methods if they need to be exposed, otherwise keep private in TaskService
        List<string> GetTaskPriorities();
        List<string> GetTaskTypes();
        List<string> GetTaskStatuses();

        #region Periodic Task Schedule Management

        /// <summary>
        /// 获取周期性任务计划列表（分页）
        /// </summary>
        Task<ApiResponse<PaginatedResult<PeriodicTaskScheduleDto>>> GetPeriodicSchedulesPagedAsync(PeriodicTaskScheduleQueryParametersDto parameters);

        /// <summary>
        /// 根据ID获取周期性任务计划详情
        /// </summary>
        Task<ApiResponse<PeriodicTaskScheduleDto>> GetPeriodicScheduleByIdAsync(long scheduleId);

        /// <summary>
        /// 创建新的周期性任务计划
        /// </summary>
        Task<ApiResponse<PeriodicTaskScheduleDto>> CreatePeriodicScheduleAsync(CreatePeriodicTaskScheduleRequestDto request, int currentUserId);

        /// <summary>
        /// 更新现有的周期性任务计划
        /// </summary>
        Task<ApiResponse<PeriodicTaskScheduleDto>> UpdatePeriodicScheduleAsync(long scheduleId, UpdatePeriodicTaskScheduleRequestDto request, int currentUserId);

        /// <summary>
        /// 删除周期性任务计划（软删除）
        /// </summary>
        Task<ApiResponse<bool>> DeletePeriodicScheduleAsync(long scheduleId, int currentUserId);

        /// <summary>
        /// 启用或禁用周期性任务计划
        /// </summary>
        Task<ApiResponse<bool>> EnablePeriodicScheduleAsync(long scheduleId, bool isEnabled, int currentUserId);

        #endregion
    }
} 