// File: Application/Features/SpareParts/Dtos/CreateSparePartRequest.cs
// Description: 备品备件创建请求数据传输对象

using System.ComponentModel.DataAnnotations;

namespace ItAssetsSystem.Application.Features.SpareParts.Dtos
{
    /// <summary>
    /// 备品备件创建请求数据传输对象
    /// </summary>
    public class CreateSparePartRequest
    {
        /// <summary>
        /// 备件编号
        /// </summary>
        [Required(ErrorMessage = "备件编号不能为空")]
        [StringLength(50, ErrorMessage = "备件编号长度不能超过50个字符")]
        public string Code { get; set; }

        /// <summary>
        /// 物料编号
        /// </summary>
        [StringLength(50, ErrorMessage = "物料编号长度不能超过50个字符")]
        public string MaterialNumber { get; set; }

        /// <summary>
        /// 备件名称
        /// </summary>
        [Required(ErrorMessage = "备件名称不能为空")]
        [StringLength(100, ErrorMessage = "备件名称长度不能超过100个字符")]
        public string Name { get; set; }
        
        /// <summary>
        /// 备件类型ID
        /// </summary>
        [Required(ErrorMessage = "备件类型不能为空")]
        public long TypeId { get; set; }
        
        /// <summary>
        /// 规格型号
        /// </summary>
        [StringLength(100, ErrorMessage = "规格型号长度不能超过100个字符")]
        public string Specification { get; set; }
        
        /// <summary>
        /// 品牌
        /// </summary>
        [StringLength(50, ErrorMessage = "品牌长度不能超过50个字符")]
        public string Brand { get; set; }
        
        /// <summary>
        /// 单位
        /// </summary>
        [StringLength(10, ErrorMessage = "单位长度不能超过10个字符")]
        public string Unit { get; set; }
        
        /// <summary>
        /// 初始库存
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "初始库存不能为负数")]
        public int InitialStock { get; set; }
        
        /// <summary>
        /// 安全库存预警阈值
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "预警阈值不能为负数")]
        public int WarningThreshold { get; set; }
        
        /// <summary>
        /// 最小安全库存
        /// </summary>
        [Range(0, int.MaxValue, ErrorMessage = "最小安全库存不能为负数")]
        public int MinStock { get; set; }
        
        /// <summary>
        /// 库位ID
        /// </summary>
        public long? LocationId { get; set; }
        
        /// <summary>
        /// 价格(元)
        /// </summary>
        [Range(0, 9999999.99, ErrorMessage = "价格不能为负数且不能超过9999999.99")]
        public decimal? Price { get; set; }
        
        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(500, ErrorMessage = "备注长度不能超过500个字符")]
        public string Remarks { get; set; }
    }
} 