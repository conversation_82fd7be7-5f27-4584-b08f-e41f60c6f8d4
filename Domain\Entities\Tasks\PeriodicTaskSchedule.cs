// File: Domain/Entities/Tasks/PeriodicTaskSchedule.cs
// Description: 周期性任务计划实体
#nullable enable
using System;
using System.Collections.Generic;
using ItAssetsSystem.Models.Entities; // Corrected: Assuming User is here

namespace ItAssetsSystem.Domain.Entities.Tasks
{
    /// <summary>
    /// 周期性任务计划表 (V2 - BIGINT PK)
    /// </summary>
    public class PeriodicTaskSchedule
    {
        /// <summary>
        /// 周期性计划主键ID (BIGINT)
        /// </summary>
        public long PeriodicTaskScheduleId { get; set; }

        /// <summary>
        /// 任务模板ID (BIGINT, 关联 tasks.TaskId，指向一个作为模板的任务)
        /// </summary>
        public long TemplateTaskId { get; set; }

        /// <summary>
        /// 任务模板导航属性 - 标记为NotMapped避免EF Core自动生成外键
        /// </summary>
        [System.ComponentModel.DataAnnotations.Schema.NotMapped]
        public virtual Task? TemplateTask { get; set; }

        /// <summary>
        /// 创建此计划的用户ID (INT, 关联 users.Id)
        /// </summary>
        public int CreatorUserId { get; set; }

        /// <summary>
        /// 创建用户导航属性 - 标记为NotMapped避免EF Core自动生成外键
        /// </summary>
        [System.ComponentModel.DataAnnotations.Schema.NotMapped]
        public virtual User? CreatorUser { get; set; }

        /// <summary>
        /// 计划名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 计划描述
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// 重复类型 (如 Daily, Weekly, Monthly, Yearly, CustomCron)
        /// </summary>
        public string RecurrenceType { get; set; } = "Daily";

        /// <summary>
        /// 重复间隔 (与RecurrenceType配合, 如每2天, 每3周)
        /// </summary>
        public int RecurrenceInterval { get; set; } = 1;

        /// <summary>
        /// 星期几重复 (仅 Weekly, JSON 数组 [1, 3, 5] 表示周一三五)
        /// </summary>
        public string? DaysOfWeek { get; set; } // Store as JSON string like [1, 3, 5]

        /// <summary>
        /// 每月第几天重复 (仅 Monthly, 如 15 表示每月15号)
        /// </summary>
        public int? DayOfMonth { get; set; }

        /// <summary>
        /// 每月第几周的星期几重复 (仅 Monthly, 如 'Last Friday' 或 'Second Tuesday')
        /// </summary>
        public string? WeekOfMonth { get; set; } // e.g., 'First', 'Second', 'Third', 'Fourth', 'Last'
        public string? DayOfWeekForMonth { get; set; } // e.g., 'Monday', 'Tuesday', ..., 'Day' (for DayOfMonth), 'Weekday', 'WeekendDay'

        /// <summary>
        /// 每年几月重复 (仅 Yearly, 如 8 表示每年8月)
        /// </summary>
        public int? MonthOfYear { get; set; }

        /// <summary>
        /// 自定义CRON表达式 (当 RecurrenceType 为 CustomCron 时使用)
        /// </summary>
        public string? CronExpression { get; set; }

        /// <summary>
        /// 计划开始生效时间
        /// </summary>
        public DateTime StartDate { get; set; }

        /// <summary>
        /// 结束条件类型 (Never-永不, EndDate-指定日期, Occurrences-次数)
        /// </summary>
        public string EndConditionType { get; set; } = "Never";

        /// <summary>
        /// 计划结束日期 (当 EndConditionType 为 EndDate 时)
        /// </summary>
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// 计划总生成次数 (当 EndConditionType 为 Occurrences 时)
        /// </summary>
        public int? TotalOccurrences { get; set; }

        /// <summary>
        /// 已生成任务次数
        /// </summary>
        public int OccurrencesGenerated { get; set; }

        /// <summary>
        /// 下次生成任务的时间戳 (替代 NextGenerationTimestamp)
        /// </summary>
        public DateTime NextGenerationTime { get; set; }

        /// <summary>
        /// 计划状态 (Active-活动, Paused-暂停, Completed-完成, Expired-过期, Error)
        /// 替代 IsEnabled/Status
        /// </summary>
        public string Status { get; set; } = "Active";

        /// <summary>
        /// 最后一次成功生成任务的时间戳
        /// </summary>
        public DateTime? LastGeneratedTimestamp { get; set; }

        /// <summary>
        /// 最后一次尝试生成任务时遇到的错误信息
        /// </summary>
        public string? LastError { get; set; }

        /// <summary>
        /// 从该模板生成的任务默认应获得的积分/经验值
        /// </summary>
        public int DefaultPoints { get; set; }

        /// <summary>
        /// 记录创建时间
        /// </summary>
        public DateTime CreationTimestamp { get; set; }

        /// <summary>
        /// 记录最后更新时间
        /// </summary>
        public DateTime LastUpdatedTimestamp { get; set; }

        // Navigation property for generated tasks
        [System.ComponentModel.DataAnnotations.Schema.NotMapped]
        public virtual ICollection<Task> GeneratedTasks { get; set; } = new List<Task>();

        /// <summary>
        /// 导航属性：负责人关联
        /// </summary>
        public virtual ICollection<PeriodicTaskScheduleAssignee> Assignees { get; set; } = new List<PeriodicTaskScheduleAssignee>();
    }
} 