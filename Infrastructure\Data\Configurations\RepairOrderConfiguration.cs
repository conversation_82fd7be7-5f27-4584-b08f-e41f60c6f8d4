// File: Infrastructure/Data/Configurations/RepairOrderConfiguration.cs
// Description: RepairOrder实体配置

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ItAssetsSystem.Domain.Entities;

namespace ItAssetsSystem.Infrastructure.Data.Configurations;

/// <summary>
/// RepairOrder实体配置
/// </summary>
public class RepairOrderConfiguration : IEntityTypeConfiguration<RepairOrder>
{
    public void Configure(EntityTypeBuilder<RepairOrder> builder)
    {
        builder.ToTable("RepairOrders");
        builder.<PERSON>Key(e => e.Id);
        
        builder.Property(e => e.Id)
            .HasColumnName("Id")
            .ValueGeneratedOnAdd();
            
        builder.Property(e => e.OrderCode)
            .HasColumnName("OrderCode")
            .HasMaxLength(50)
            .IsRequired();
            
        builder.Property(e => e.SupplierId)
            .HasColumnName("SupplierId")
            .IsRequired();
            
        builder.Property(e => e.SendDate)
            .HasColumnName("SendDate");
            
        builder.Property(e => e.ExpectedReturnDate)
            .HasColumnName("ExpectedReturnDate");
            
        builder.Property(e => e.ActualReturnDate)
            .HasColumnName("ActualReturnDate");
            
        builder.Property(e => e.Status)
            .HasColumnName("Status")
            .IsRequired();
            
        builder.Property(e => e.CreatedAt)
            .HasColumnName("CreatedAt")
            .IsRequired();
            
        builder.Property(e => e.UpdatedAt)
            .HasColumnName("UpdatedAt")
            .IsRequired();
            
        builder.Property(e => e.CreatorId)
            .HasColumnName("CreatorId")
            .IsRequired();
            
        builder.Property(e => e.ApproverId)
            .HasColumnName("approver_id");
            
        // 添加其他缺失的字段映射
        builder.Property(e => e.RepairType)
            .HasColumnName("repair_type")
            .IsRequired();
            
        builder.Property(e => e.Title)
            .HasColumnName("title")
            .HasMaxLength(200);
            
        builder.Property(e => e.Description)
            .HasColumnName("description");
            
        builder.Property(e => e.Priority)
            .HasColumnName("priority")
            .IsRequired();
            
        builder.Property(e => e.FaultId)
            .HasColumnName("fault_id");
            
        builder.Property(e => e.AssetId)
            .HasColumnName("asset_id");
            
        builder.Property(e => e.EstimatedCost)
            .HasColumnName("estimated_cost");
            
        builder.Property(e => e.TotalCost)
            .HasColumnName("TotalCost");
            
        builder.Property(e => e.Notes)
            .HasColumnName("Notes")
            .HasMaxLength(500);
            
        // 忽略数据库中不存在的属性
        builder.Ignore(e => e.RepairResult);

        // 索引
        builder.HasIndex(e => e.OrderCode)
            .IsUnique()
            .HasDatabaseName("IX_RepairOrders_OrderCode");
            
        builder.HasIndex(e => e.SupplierId)
            .HasDatabaseName("IX_RepairOrders_SupplierId");
            
        builder.HasIndex(e => e.Status)
            .HasDatabaseName("IX_RepairOrders_Status");
            
        builder.HasIndex(e => e.SendDate)
            .HasDatabaseName("IX_RepairOrders_SendDate");

        // 关系配置
        builder.HasOne(e => e.Supplier)
            .WithMany()
            .HasForeignKey(e => e.SupplierId)
            .OnDelete(DeleteBehavior.Restrict)
            .HasConstraintName("FK_RepairOrders_Suppliers_SupplierId");

        builder.HasOne(e => e.Creator)
            .WithMany()
            .HasForeignKey(e => e.CreatorId)
            .OnDelete(DeleteBehavior.Restrict)
            .HasConstraintName("FK_RepairOrders_Users_CreatorId");

        // 一对多关系：RepairOrder -> RepairItems
        builder.HasMany(e => e.RepairItems)
            .WithOne(ri => ri.RepairOrder)
            .HasForeignKey(ri => ri.RepairOrderId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
