# 航空航天级IT资产管理系统 - Cursor前端开发指南

## 项目概述
本文档为航空航天级IT资产管理系统前端开发提供完整指导。系统实现IT资产全生命周期管理，包含资产管理、位置管理、故障管理、工作任务管理等核心功能。

## 技术栈
- **框架**: Vue 3 + Composition API
- **UI组件库**: Element Plus
- **状态管理**: Pinia
- **路由**: Vue Router
- **HTTP客户端**: Axios
- **图表库**: ECharts
- **CSS预处理器**: SCSS

## 标准目录结构
```
src/
├── api/                    # API请求模块
│   ├── index.js            # API基础配置和拦截器
│   ├── asset.js            # 资产相关API
│   ├── location.js         # 位置相关API  
│   ├── fault.js            # 故障相关API
│   ├── task.js             # 任务相关API
│   ├── system.js           # 系统管理API
│   ├── purchase.js         # 采购管理API
│   └── dashboard.js        # 仪表盘数据API
├── assets/                 # 静态资源
│   ├── icons/              # 图标资源
│   ├── images/             # 图片资源
│   └── styles/             # 样式资源
├── components/             # 全局公共组件
│   ├── common/             # 通用UI组件
│   │   ├── BaseTable.vue   # 基础表格组件
│   │   ├── SearchForm.vue  # 搜索表单组件
│   │   ├── StatusTag.vue   # 状态标签组件
│   │   └── ...
│   ├── business/           # 业务公共组件
│   │   ├── AssetSelector.vue     # 资产选择器
│   │   ├── LocationTree.vue      # 位置树形选择器
│   │   ├── UserSelector.vue      # 用户选择器
│   │   └── ...
│   ├── charts/             # 图表组件
│   │   ├── BaseChart.vue   # 基础图表封装
│   │   ├── AssetChart.vue  # 资产统计图表
│   │   ├── FaultChart.vue  # 故障统计图表
│   │   └── ...
│   └── dashboard/          # 仪表盘专用组件
│       ├── StatCard.vue    # 统计卡片
│       ├── TrendCard.vue   # 趋势卡片
│       └── AlertCard.vue   # 告警卡片
├── composables/            # 组合式函数
│   ├── useTable.js         # 表格逻辑复用
│   ├── useForm.js          # 表单逻辑复用
│   ├── useChartData.js     # 图表数据处理
│   ├── usePagination.js    # 分页逻辑
│   ├── usePermission.js    # 权限判断
│   └── useDashboard.js     # 仪表盘逻辑复用
├── layouts/                # 布局组件
│   ├── DefaultLayout.vue   # 默认布局
│   └── DashboardLayout.vue # 仪表盘专用布局
├── router/                 # 路由配置
│   ├── index.js            # 路由主入口
│   ├── routes/             # 按模块划分的路由
│   │   ├── asset.js        # 资产路由
│   │   ├── location.js     # 位置路由
│   │   └── ...
│   └── guard.js            # 路由守卫
├── stores/                 # Pinia状态管理
│   ├── modules/            # 按模块划分的状态
│   │   ├── asset.js        # 资产状态
│   │   ├── location.js     # 位置状态
│   │   ├── user.js         # 用户状态
│   │   ├── dashboard.js    # 仪表盘状态
│   │   └── ...
│   └── index.js            # 状态管理入口
├── styles/                 # 全局样式
│   ├── variables.scss      # 变量定义
│   ├── mixins.scss         # 混合器
│   ├── global.scss         # 全局样式
│   └── themes/             # 主题配置
├── utils/                  # 工具函数
│   ├── request.js          # Axios封装
│   ├── auth.js             # 认证工具
│   ├── formatter.js        # 数据格式化
│   ├── validator.js        # 验证工具
│   ├── storage.js          # 本地存储工具
│   ├── chartOptions.js     # 图表配置生成器
│   └── eventBus.js         # 事件总线
├── views/                  # 页面组件
│   ├── asset/              # 资产管理页面
│   │   ├── AssetList.vue   # 资产列表页
│   │   ├── AssetDetail.vue # 资产详情页
│   │   ├── AssetForm.vue   # 资产表单页
│   │   └── components/     # 资产模块专用组件
│   ├── location/           # 位置管理页面
│   │   ├── LocationTree.vue     # 位置树形页面
│   │   ├── LocationDetail.vue   # 位置详情页
│   │   └── components/          # 位置模块专用组件
│   ├── fault/              # 故障管理页面
│   │   ├── FaultList.vue   # 故障列表页面
│   │   ├── FaultDetail.vue # 故障详情页面
│   │   └── components/     # 故障模块专用组件
│   ├── task/               # 任务管理页面
│   │   ├── TaskList.vue    # 任务列表页面
│   │   ├── TaskBoard.vue   # 任务看板页面
│   │   └── components/     # 任务模块专用组件
│   ├── system/             # 系统管理页面
│   │   ├── UserList.vue    # 用户列表页面
│   │   ├── RoleList.vue    # 角色列表页面
│   │   └── components/     # 系统模块专用组件
│   ├── purchase/           # 采购管理页面
│   └── dashboard/          # 仪表盘页面
│       ├── index.vue       # 主仪表盘
│       ├── AssetDashboard.vue  # 资产仪表盘
│       ├── FaultDashboard.vue  # 故障仪表盘
│       └── components/     # 仪表盘专用组件
├── App.vue                 # 根组件
└── main.js                 # 入口文件
```

## 编码规范

### 命名约定
- **文件名**: 
  - 组件使用PascalCase (如`AssetList.vue`)
  - 工具函数、API使用camelCase (如`useTable.js`, `asset.js`)
- **组件名**: 使用PascalCase (如`AssetList`)
- **变量名**: 使用camelCase (如`assetList`)
- **常量**: 使用UPPER_SNAKE_CASE (如`MAX_ITEMS_PER_PAGE`)
- **样式类名**: 使用kebab-case (如`asset-item`)

### 编码风格
- 使用`<script setup>`语法，更简洁直观
- 使用组合式API (Composition API)
- 使用ES6+语法特性
- 善用解构赋值和组合函数
- 使用异步/等待(async/await)处理异步操作

## 功能模块开发指南

### 资产管理模块
资产管理模块负责管理IT资产信息，包括资产录入、查询、编辑、状态管理等功能。

**主要功能**:
- 资产列表 (支持多条件筛选、分页)
- 资产详情 (显示资产基本信息和关联信息)
- 资产创建/编辑 (表单验证)
- 资产状态管理 (库存、使用中、维修中、报废等)
- 资产位置变更 (流程化操作)

**重要组件**:
- `AssetList.vue` - 资产列表页面
- `AssetDetail.vue` - 资产详情页面
- `AssetForm.vue` - 资产创建/编辑表单
- `LocationChange.vue` - 位置变更组件

### 位置管理模块
位置管理模块负责管理资产存放位置信息，支持四/五级位置层级结构。

**主要功能**:
- 位置树形结构展示
- 位置详情与关联资产
- 位置创建/编辑
- 位置与部门/用户关联管理

**重要组件**:
- `LocationTree.vue` - 位置树形结构页面
- `LocationDetail.vue` - 位置详情页面
- `LocationForm.vue` - 位置创建/编辑表单
- `LocationRelation.vue` - 位置关联管理组件

### 故障管理模块
故障管理模块负责处理资产故障报修、处理、统计等功能。

**主要功能**:
- 故障工单创建
- 故障处理流程
- 故障统计与分析
- 返厂维修管理

**重要组件**:
- `FaultList.vue` - 故障列表页面
- `FaultDetail.vue` - 故障详情页面
- `FaultCreate.vue` - 故障创建表单
- `ReturnToFactory.vue` - 返厂维修管理组件

### 工作任务管理模块
工作任务管理模块负责日常任务、周期任务和PDCA计划管理。

**主要功能**:
- 任务列表与看板
- 任务创建与分配
- 周期任务管理
- PDCA计划管理

**重要组件**:
- `TaskList.vue` - 任务列表页面
- `TaskBoard.vue` - 任务看板页面
- `PeriodicTask.vue` - 周期任务管理组件
- `PdcaPlan.vue` - PDCA计划管理组件

### 仪表盘模块
仪表盘模块提供系统关键指标的可视化展示。

**主要功能**:
- 资产统计与分布
- 故障趋势分析
- 任务完成情况
- 关键预警指标

**重要组件**:
- `DashboardIndex.vue` - 主仪表盘页面
- `AssetDashboard.vue` - 资产统计仪表盘
- `FaultDashboard.vue` - 故障统计仪表盘
- `TaskDashboard.vue` - 任务统计仪表盘

## API集成指南

### 基础API配置
使用Axios进行统一配置，设置拦截器处理认证和错误。参考API文档设置具体端点地址。

```javascript
// api/index.js示例
import axios from 'axios';
import { useUserStore } from '@/stores/modules/user';
import router from '@/router';

const request = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
  timeout: 10000
});

// 请求拦截器
request.interceptors.request.use(
  config => {
    const userStore = useUserStore();
    const token = userStore.token;
    
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    
    return config;
  },
  error => Promise.reject(error)
);

// 响应拦截器
request.interceptors.response.use(
  response => response.data,
  error => {
    const { status } = error.response || {};
    
    // 处理401未授权
    if (status === 401) {
      const userStore = useUserStore();
      userStore.logout();
      router.push('/login');
    }
    
    // 处理其他错误
    ElMessage.error(error.response?.data?.message || '请求失败');
    return Promise.reject(error);
  }
);

export default request;
```

### 模块API实现
每个功能模块创建独立的API文件，按API文档实现相应端点的调用方法。

```javascript
// api/asset.js示例架构
import request from './index';

// 按照API文档提供的端点实现
export function getAssetList(params) {
  return request({
    url: '/assets', // API文档中定义的端点
    method: 'get',
    params
  });
}

export function getAssetById(id) {
  return request({
    url: `/assets/${id}`,
    method: 'get'
  });
}

// 更多API方法...
```

## 状态管理指南

使用Pinia实现模块化状态管理，为每个主要功能模块创建独立的Store。

```javascript
// stores/modules/asset.js示例架构
import { defineStore } from 'pinia';
import { getAssetList, getAssetById /* 其他API */ } from '@/api/asset';

export const useAssetStore = defineStore('asset', {
  state: () => ({
    assetList: [],
    currentAsset: null,
    pagination: {
      page: 1,
      pageSize: 10,
      total: 0
    },
    filters: {
      assetCode: '',
      assetName: '',
      assetTypeId: null,
      locationId: null,
      status: null
    },
    loading: false
  }),
  
  getters: {
    // 计算属性
  },
  
  actions: {
    // 请根据API文档实现
    async fetchAssetList() {
      this.loading = true;
      try {
        const params = {
          ...this.pagination,
          ...this.filters
        };
        const response = await getAssetList(params);
        this.assetList = response.data;
        this.pagination.total = response.total;
      } catch (error) {
        console.error('获取资产列表失败', error);
      } finally {
        this.loading = false;
      }
    },
    
    // 更多操作...
  }
});
```

## 通用组件实现指南

### 基础表格组件

```vue
<!-- components/common/BaseTable.vue示例架构 -->
<template>
  <div class="base-table">
    <el-table
      v-loading="loading"
      :data="data"
      border
      stripe
      style="width: 100%"
      @selection-change="handleSelectionChange"
    >
      <el-table-column v-if="showSelection" type="selection" width="55" />
      
      <slot></slot>
      
      <el-table-column v-if="showActions" label="操作" width="150" fixed="right">
        <template #default="scope">
          <slot name="actions" :row="scope.row"></slot>
        </template>
      </el-table-column>
    </el-table>
    
    <div class="pagination-container" v-if="showPagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
// 实现组件逻辑
</script>
```

### 搜索表单组件

```vue
<!-- components/common/SearchForm.vue示例架构 -->
<template>
  <el-form :inline="true" :model="form" class="search-form" @submit.prevent="handleSearch">
    <slot></slot>
    
    <el-form-item>
      <el-button type="primary" @click="handleSearch">查询</el-button>
      <el-button @click="handleReset">重置</el-button>
    </el-form-item>
  </el-form>
</template>

<script setup>
// 实现组件逻辑
</script>
```

## 实现前端页面的方法

1. **创建页面组件**:
   - 在views目录下相应模块文件夹中创建页面组件
   - 根据功能划分组件职责
   
2. **组合复用逻辑**:
   - 使用composables中定义的组合式函数
   - 如useTable.js处理表格逻辑，useForm.js处理表单逻辑
   
3. **连接API和状态**:
   - 通过API调用后端服务
   - 使用Pinia store管理状态
   
4. **实现页面UI**:
   - 使用Element Plus组件
   - 应用项目统一的样式规范

## 性能优化建议

1. **懒加载**:
   - 使用Vue Router的懒加载特性
   - 图表组件按需引入
   
2. **虚拟滚动**:
   - 对长列表使用虚拟滚动
   - 可使用vue-virtual-scroller等库
   
3. **缓存策略**:
   - 使用keep-alive缓存频繁访问的组件
   - 合理使用本地存储缓存静态数据
   
4. **节流和防抖**:
   - 对搜索操作使用防抖
   - 对滚动事件使用节流
   
5. **组件拆分**:
   - 将大型组件拆分为更小的组件
   - 避免不必要的组件渲染

## 开发流程建议

1. **自底向上开发**:
   - 先开发通用组件和composables
   - 然后开发业务组件
   - 最后组装页面
   
2. **模块化开发**:
   - 先开发核心功能模块
   - 再开发扩展功能
   - 最后集成仪表盘

## 开发指令

开发时请参考API文档实现具体的前端功能，确保与后端API正确集成。代码实现应遵循本指南提供的架构设计和编码规范，保持系统的一致性和可维护性。

API接口请严格按照API文档中的定义实现，包括接口URL、请求方法、参数和返回值格式。前端开发应基于API文档提供的信息，而不是自行假设。

---

本指南提供了项目架构的顶层设计和实现方向，具体代码实现请根据API文档和业务需求灵活调整。遵循这一架构可确保系统的高可用性和可扩展性，避免后期重构的需要。
