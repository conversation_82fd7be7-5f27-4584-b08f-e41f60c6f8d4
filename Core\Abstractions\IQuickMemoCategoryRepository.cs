// File: Core/Abstractions/IQuickMemoCategoryRepository.cs
// Description: Interface for QuickMemoCategory data access operations.
#nullable enable
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Threading; // For CancellationToken
using ItAssetsSystem.Domain.Entities.Notes; // For QuickMemoCategory

namespace ItAssetsSystem.Core.Abstractions
{
    public interface IQuickMemoCategoryRepository
    {
        Task<IEnumerable<QuickMemoCategory>> GetByUserIdAsync(int userId, CancellationToken cancellationToken = default);
        Task<QuickMemoCategory?> GetByIdAsync(string categoryId, CancellationToken cancellationToken = default); // Removed userId from here, service layer should verify ownership if needed post-fetch
        Task<QuickMemoCategory?> GetByIdAndUserIdAsync(string categoryId, int userId, CancellationToken cancellationToken = default); // For direct ownership check
        Task AddAsync(QuickMemoCategory category, CancellationToken cancellationToken = default);
        void Update(QuickMemoCategory category);
        void Delete(QuickMemoCategory category);
        Task<bool> UserOwnsCategoryAsync(string categoryId, int userId, CancellationToken cancellationToken = default);
        Task<QuickMemoCategory?> FindByNameAsync(int userId, string name, CancellationToken cancellationToken = default);
        Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);
    }
} 