// File: Api/V2/QuickMemosController.cs
// Description: API controller for QuickMemos and QuickMemoCategories.
#nullable enable
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using System.Threading.Tasks;
using System.Threading; // For CancellationToken
using ItAssetsSystem.Application.Features.Notes.Services;
using ItAssetsSystem.Application.Features.Notes.Dtos;
using ItAssetsSystem.Application.Common.Dtos; // For ApiResponse
using ItAssetsSystem.Core.Services; // For ApiResponseFactory
using System.Security.Claims;
using Microsoft.Extensions.Logging; // Required for ILogger
using System; // Required for Guid and UnauthorizedAccessException
using System.Collections.Generic; // Required for List<T>
using System.Linq; // Added this line

namespace ItAssetsSystem.Api.V2.Controllers
{
    [ApiController]
    [Route("api/v2/quick-memos")]
    [Authorize] // All endpoints require authentication
    public class QuickMemosController : ControllerBase
    {
        private readonly IQuickMemoService _quickMemoService;
        private readonly ILogger<QuickMemosController> _logger;

        public QuickMemosController(IQuickMemoService quickMemoService, ILogger<QuickMemosController> logger)
        {
            _quickMemoService = quickMemoService;
            _logger = logger;
        }

        private int GetCurrentUserId()
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? User.FindFirst("uid")?.Value;
            if (int.TryParse(userIdClaim, out var userId))
            {
                return userId;
            }
            _logger.LogWarning("Unable to parse UserID from token claims. NameIdentifier: {NameIdentifier}, uid: {UidClaim}", User.FindFirst(ClaimTypes.NameIdentifier)?.Value, User.FindFirst("uid")?.Value);
            throw new UnauthorizedAccessException("Current user ID could not be determined from token.");
        }

        // --- QuickMemo Endpoints ---

        [HttpGet]
        [ProducesResponseType(typeof(ApiResponse<List<QuickMemoDto>>), 200)]
        [ProducesResponseType(typeof(ApiResponse<List<QuickMemoDto>>), 400)] // For service layer errors
        public async Task<IActionResult> GetMemos(CancellationToken cancellationToken)
        {
            try
            {
                var userId = GetCurrentUserId();
                var result = await _quickMemoService.GetMemosAsync(userId, cancellationToken);
                // The service layer now wraps its response in ApiResponse.
                // So we directly return Ok(result) or BadRequest(result).
                if (!result.Success)
                {
                    // Log the error message from the service if available for server-side diagnostics
                    if (!string.IsNullOrEmpty(result.Error))
                        _logger.LogWarning("Service call to GetMemosAsync failed for user {UserId}: {Error}", userId, result.Error);
                    return BadRequest(result); // Return the whole ApiResponse from service
                }
                return Ok(result);
            }
            catch (UnauthorizedAccessException ex)
            {
                _logger.LogWarning(ex, "Unauthorized access in GetMemos.");
                return Unauthorized(ApiResponseFactory.CreateFail<List<QuickMemoDto>>(ex.Message));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "An unexpected error occurred in GetMemos.");
                return StatusCode(500, ApiResponseFactory.CreateFail<List<QuickMemoDto>>("An unexpected error occurred."));
            }
        }

        [HttpGet("{memoId}")]
        [ProducesResponseType(typeof(ApiResponse<QuickMemoDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse<QuickMemoDto>), 404)]
        [ProducesResponseType(typeof(ApiResponse<QuickMemoDto>), 400)]
        public async Task<IActionResult> GetMemoById(string memoId, CancellationToken cancellationToken)
        {
            try
            {
                var userId = GetCurrentUserId();
                var result = await _quickMemoService.GetMemoByIdAsync(memoId, userId, cancellationToken);
                if (!result.Success)
                {
                    // Distinguish between Not Found and other errors based on how service signals it
                    // Assuming service returns a specific message or error code for "Not Found"
                    if (result.Error != null && result.Error.Contains("未找到")) // Heuristic, better to use error codes
                        return NotFound(result);
                    return BadRequest(result);
                }
                return Ok(result);
            }
            catch (UnauthorizedAccessException ex)
            {
                _logger.LogWarning(ex, "Unauthorized access in GetMemoById for memo {MemoId}.", memoId);
                return Unauthorized(ApiResponseFactory.CreateFail<QuickMemoDto>(ex.Message));
            }
             catch (Exception ex)
            {
                _logger.LogError(ex, "An unexpected error occurred in GetMemoById for memo {MemoId}.", memoId);
                return StatusCode(500, ApiResponseFactory.CreateFail<QuickMemoDto>("An unexpected error occurred."));
            }
        }

        [HttpPost]
        [ProducesResponseType(typeof(ApiResponse<QuickMemoDto>), 201)]
        [ProducesResponseType(typeof(ApiResponse<QuickMemoDto>), 400)]
        public async Task<IActionResult> CreateMemo([FromBody] CreateQuickMemoRequestDto request, CancellationToken cancellationToken)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ApiResponseFactory.CreateFail<QuickMemoDto>(ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage).ToList()));
            }
            try
            {
                var userId = GetCurrentUserId();
                var result = await _quickMemoService.CreateMemoAsync(request, userId, cancellationToken);
                if (!result.Success || result.Data == null)
                {
                    return BadRequest(result);
                }
                // For CreatedAtAction, ensure GetMemoById route name matches if you use nameof()
                return CreatedAtAction(nameof(GetMemoById), new { memoId = result.Data.Id }, result);
            }
            catch (UnauthorizedAccessException ex)
            {
                _logger.LogWarning(ex, "Unauthorized access in CreateMemo.");
                return Unauthorized(ApiResponseFactory.CreateFail<QuickMemoDto>(ex.Message));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "An unexpected error occurred in CreateMemo.");
                return StatusCode(500, ApiResponseFactory.CreateFail<QuickMemoDto>("An unexpected error occurred."));
            }
        }

        [HttpPut("{memoId}")]
        [ProducesResponseType(typeof(ApiResponse<QuickMemoDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse<QuickMemoDto>), 400)]
        [ProducesResponseType(typeof(ApiResponse<QuickMemoDto>), 404)]
        public async Task<IActionResult> UpdateMemo(string memoId, [FromBody] UpdateQuickMemoRequestDto request, CancellationToken cancellationToken)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ApiResponseFactory.CreateFail<QuickMemoDto>(ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage).ToList()));
            }
            try
            {
                var userId = GetCurrentUserId();
                var result = await _quickMemoService.UpdateMemoAsync(memoId, request, userId, cancellationToken);
                if (!result.Success)
                {
                    if (result.Error != null && result.Error.Contains("未找到"))
                        return NotFound(result);
                    return BadRequest(result);
                }
                return Ok(result);
            }
            catch (UnauthorizedAccessException ex)
            {
                _logger.LogWarning(ex, "Unauthorized access in UpdateMemo for memo {MemoId}.", memoId);
                return Unauthorized(ApiResponseFactory.CreateFail<QuickMemoDto>(ex.Message));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "An unexpected error occurred in UpdateMemo for memo {MemoId}.", memoId);
                return StatusCode(500, ApiResponseFactory.CreateFail<QuickMemoDto>("An unexpected error occurred."));
            }
        }

        [HttpDelete("{memoId}")]
        [ProducesResponseType(typeof(ApiResponse<bool>), 200)]
        [ProducesResponseType(typeof(ApiResponse<bool>), 404)]
        [ProducesResponseType(typeof(ApiResponse<bool>), 400)]
        public async Task<IActionResult> DeleteMemo(string memoId, CancellationToken cancellationToken)
        {
            try
            {
                var userId = GetCurrentUserId();
                var result = await _quickMemoService.DeleteMemoAsync(memoId, userId, cancellationToken);
                if (!result.Success)
                {
                     if (result.Error != null && result.Error.Contains("未找到"))
                        return NotFound(result);
                    return BadRequest(result);
                }
                return Ok(result); // Or return NoContent() if ApiResponse<bool>.Data is true and Success.
            }
            catch (UnauthorizedAccessException ex)
            {
                _logger.LogWarning(ex, "Unauthorized access in DeleteMemo for memo {MemoId}.", memoId);
                return Unauthorized(ApiResponseFactory.CreateFail<bool>(ex.Message));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "An unexpected error occurred in DeleteMemo for memo {MemoId}.", memoId);
                return StatusCode(500, ApiResponseFactory.CreateFail<bool>("An unexpected error occurred."));
            }
        }

        // --- QuickMemoCategory Endpoints ---

        [HttpGet("categories")]
        [ProducesResponseType(typeof(ApiResponse<List<QuickMemoCategoryDto>>), 200)]
        [ProducesResponseType(typeof(ApiResponse<List<QuickMemoCategoryDto>>), 400)]
        public async Task<IActionResult> GetCategories(CancellationToken cancellationToken)
        {
             try
            {
                var userId = GetCurrentUserId();
                var result = await _quickMemoService.GetCategoriesAsync(userId, cancellationToken);
                if (!result.Success)
                {
                    return BadRequest(result);
                }
                return Ok(result);
            }
            catch (UnauthorizedAccessException ex)
            {
                _logger.LogWarning(ex, "Unauthorized access in GetCategories.");
                return Unauthorized(ApiResponseFactory.CreateFail<List<QuickMemoCategoryDto>>(ex.Message));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "An unexpected error occurred in GetCategories.");
                return StatusCode(500, ApiResponseFactory.CreateFail<List<QuickMemoCategoryDto>>("An unexpected error occurred."));
            }
        }

        [HttpGet("categories/{categoryId}")]
        [ProducesResponseType(typeof(ApiResponse<QuickMemoCategoryDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse<QuickMemoCategoryDto>), 404)]
        public async Task<IActionResult> GetCategoryById(string categoryId, CancellationToken cancellationToken)
        {
            try
            {
                var userId = GetCurrentUserId();
                var result = await _quickMemoService.GetCategoryByIdAsync(categoryId, userId, cancellationToken);
                if (!result.Success)
                {
                    if (result.Error != null && result.Error.Contains("未找到"))
                        return NotFound(result);
                    return BadRequest(result);
                }
                return Ok(result);
            }
            catch (UnauthorizedAccessException ex)
            {
                _logger.LogWarning(ex, "Unauthorized access for GetCategoryById, categoryId {CategoryId}", categoryId);
                return Unauthorized(ApiResponseFactory.CreateFail<QuickMemoCategoryDto>(ex.Message));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetCategoryById for categoryId {CategoryId}", categoryId);
                return StatusCode(500, ApiResponseFactory.CreateFail<QuickMemoCategoryDto>("An unexpected server error occurred."));
            }
        }

        [HttpPost("categories")]
        [ProducesResponseType(typeof(ApiResponse<QuickMemoCategoryDto>), 201)]
        [ProducesResponseType(typeof(ApiResponse<QuickMemoCategoryDto>), 400)]
        public async Task<IActionResult> CreateCategory([FromBody] CreateQuickMemoCategoryRequestDto request, CancellationToken cancellationToken)
        {
            if (!ModelState.IsValid)
            {
                 return BadRequest(ApiResponseFactory.CreateFail<QuickMemoCategoryDto>(ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage).ToList()));
            }
            try
            {
                var userId = GetCurrentUserId();
                var result = await _quickMemoService.CreateCategoryAsync(request, userId, cancellationToken);
                if (!result.Success || result.Data == null)
                {
                    return BadRequest(result);
                }
                return CreatedAtAction(nameof(GetCategoryById), new { categoryId = result.Data.Id }, result);
            }
            catch (UnauthorizedAccessException ex)
            {
                _logger.LogWarning(ex, "Unauthorized access in CreateCategory.");
                return Unauthorized(ApiResponseFactory.CreateFail<QuickMemoCategoryDto>(ex.Message));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "An unexpected error occurred in CreateCategory.");
                return StatusCode(500, ApiResponseFactory.CreateFail<QuickMemoCategoryDto>("An unexpected error occurred."));
            }
        }

        [HttpPut("categories/{categoryId}")]
        [ProducesResponseType(typeof(ApiResponse<QuickMemoCategoryDto>), 200)]
        [ProducesResponseType(typeof(ApiResponse<QuickMemoCategoryDto>), 400)]
        [ProducesResponseType(typeof(ApiResponse<QuickMemoCategoryDto>), 404)]
        public async Task<IActionResult> UpdateCategory(string categoryId, [FromBody] CreateQuickMemoCategoryRequestDto request, CancellationToken cancellationToken) // Using Create DTO for update as per service interface
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ApiResponseFactory.CreateFail<QuickMemoCategoryDto>(ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage).ToList()));
            }
             try
            {
                var userId = GetCurrentUserId();
                var result = await _quickMemoService.UpdateCategoryAsync(categoryId, request, userId, cancellationToken);
                if (!result.Success)
                {
                    if (result.Error != null && result.Error.Contains("未找到"))
                        return NotFound(result);
                    return BadRequest(result);
                }
                return Ok(result);
            }
            catch (UnauthorizedAccessException ex)
            {
                _logger.LogWarning(ex, "Unauthorized access in UpdateCategory for category {CategoryId}.", categoryId);
                return Unauthorized(ApiResponseFactory.CreateFail<QuickMemoCategoryDto>(ex.Message));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "An unexpected error occurred in UpdateCategory for category {CategoryId}.", categoryId);
                return StatusCode(500, ApiResponseFactory.CreateFail<QuickMemoCategoryDto>("An unexpected error occurred."));
            }
        }

        [HttpDelete("categories/{categoryId}")]
        [ProducesResponseType(typeof(ApiResponse<bool>), 200)]
        [ProducesResponseType(typeof(ApiResponse<bool>), 404)]
        [ProducesResponseType(typeof(ApiResponse<bool>), 400)]
        public async Task<IActionResult> DeleteCategory(string categoryId, CancellationToken cancellationToken)
        {
            try
            {
                var userId = GetCurrentUserId();
                var result = await _quickMemoService.DeleteCategoryAsync(categoryId, userId, cancellationToken);
                if (!result.Success)
                {
                    if (result.Error != null && result.Error.Contains("未找到"))
                        return NotFound(result);
                    return BadRequest(result);
                }
                return Ok(result);
            }
            catch (UnauthorizedAccessException ex)
            {
                _logger.LogWarning(ex, "Unauthorized access in DeleteCategory for category {CategoryId}.", categoryId);
                return Unauthorized(ApiResponseFactory.CreateFail<bool>(ex.Message));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "An unexpected error occurred in DeleteCategory for category {CategoryId}.", categoryId);
                return StatusCode(500, ApiResponseFactory.CreateFail<bool>("An unexpected error occurred."));
            }
        }
    }
} 