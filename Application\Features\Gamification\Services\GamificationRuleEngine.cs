// File: Application/Features/Gamification/Services/GamificationRuleEngine.cs
// Description: 游戏化规则引擎实现

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using ItAssetsSystem.Application.Features.Gamification.Events;
using ItAssetsSystem.Models.Dtos.Gamification;
using ItAssetsSystem.Models;
using ItAssetsSystem.Infrastructure.Data;
using ItAssetsSystem.Models.Entities.Gamification;

namespace ItAssetsSystem.Application.Features.Gamification.Services
{
    /// <summary>
    /// 游戏化规则引擎实现
    /// </summary>
    public class GamificationRuleEngine : IGamificationRuleEngine
    {
        private readonly AppDbContext _context;
        private readonly ILogger<GamificationRuleEngine> _logger;
        private readonly IMemoryCache _cache;
        
        private const string RULES_CACHE_KEY = "gamification_rules";
        private const int CACHE_EXPIRY_MINUTES = 30;

        public GamificationRuleEngine(
            AppDbContext context,
            ILogger<GamificationRuleEngine> logger,
            IMemoryCache cache)
        {
            _context = context;
            _logger = logger;
            _cache = cache;
        }

        /// <summary>
        /// 计算事件奖励
        /// </summary>
        public async Task<GamificationReward> CalculateRewardAsync(GamificationEventBase gamificationEvent, string? clientId = null)
        {
            try
            {
                var eventType = gamificationEvent.GetType().Name.Replace("RewardEvent", "");
                var rules = await GetApplicableRulesAsync(eventType, clientId);

                var totalReward = GamificationReward.Empty;

                foreach (var rule in rules.OrderBy(r => r.Priority))
                {
                    try
                    {
                        var eventData = ConvertEventToData(gamificationEvent);
                        var conditionMet = await EvaluateConditionAsync(rule.Condition, eventData);

                        if (conditionMet)
                        {
                            var reward = GamificationReward.Create(
                                rule.PointsReward,
                                rule.CoinsReward,
                                rule.DiamondsReward,
                                rule.ExperienceReward,
                                $"规则: {rule.Name}",
                                rule.Id
                            );

                            totalReward = CombineRewards(totalReward, reward);
                            
                            _logger.LogDebug("规则 {RuleId} 匹配，奖励: {Points}积分, {Coins}金币, {Diamonds}钻石", 
                                rule.Id, rule.PointsReward, rule.CoinsReward, rule.DiamondsReward);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "评估规则 {RuleId} 时发生错误", rule.Id);
                    }
                }

                // 如果没有匹配的规则，使用默认奖励
                if (!totalReward.HasReward)
                {
                    totalReward = GetDefaultReward(eventType);
                }

                return totalReward;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "计算游戏化奖励时发生错误，事件类型: {EventType}", gamificationEvent.GetType().Name);
                return GamificationReward.Empty;
            }
        }

        /// <summary>
        /// 获取适用的规则
        /// </summary>
        public async Task<IEnumerable<GamificationRule>> GetApplicableRulesAsync(string eventType, string? clientId = null)
        {
            var allRules = await GetCachedRulesAsync();
            
            return allRules.Where(r => 
                r.IsActive && 
                r.EventType.Equals(eventType, StringComparison.OrdinalIgnoreCase) &&
                (string.IsNullOrEmpty(clientId) || string.IsNullOrEmpty(r.ClientId) || r.ClientId == clientId)
            );
        }

        /// <summary>
        /// 添加规则
        /// </summary>
        public async Task<bool> AddRuleAsync(GamificationRule rule)
        {
            try
            {
                // 验证规则
                if (!ValidateRule(rule))
                {
                    return false;
                }

                // 检查规则ID是否已存在
                var existingRule = await _context.BehaviorTypes
                    .FirstOrDefaultAsync(bt => bt.Code == rule.Id);

                if (existingRule != null)
                {
                    _logger.LogWarning("规则ID {RuleId} 已存在", rule.Id);
                    return false;
                }

                // 创建行为类型记录
                var behaviorType = new BehaviorType
                {
                    Code = rule.Id,
                    Name = rule.Name,
                    Description = $"规则: {rule.Name}",
                    Category = rule.EventType,
                    BasePoints = rule.PointsReward,
                    BaseCoins = rule.CoinsReward,
                    BaseDiamonds = rule.DiamondsReward,
                    IsActive = rule.IsActive,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                _context.BehaviorTypes.Add(behaviorType);
                await _context.SaveChangesAsync();

                // 清除缓存
                _cache.Remove(RULES_CACHE_KEY);

                _logger.LogInformation("成功添加游戏化规则: {RuleId}", rule.Id);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "添加游戏化规则时发生错误: {RuleId}", rule.Id);
                return false;
            }
        }

        /// <summary>
        /// 更新规则
        /// </summary>
        public async Task<bool> UpdateRuleAsync(GamificationRule rule)
        {
            try
            {
                var behaviorType = await _context.BehaviorTypes
                    .FirstOrDefaultAsync(bt => bt.Code == rule.Id);

                if (behaviorType == null)
                {
                    _logger.LogWarning("规则 {RuleId} 不存在", rule.Id);
                    return false;
                }

                // 更新字段
                behaviorType.Name = rule.Name;
                behaviorType.Description = $"规则: {rule.Name}";
                behaviorType.Category = rule.EventType;
                behaviorType.BasePoints = rule.PointsReward;
                behaviorType.BaseCoins = rule.CoinsReward;
                behaviorType.BaseDiamonds = rule.DiamondsReward;
                behaviorType.IsActive = rule.IsActive;
                behaviorType.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                // 清除缓存
                _cache.Remove(RULES_CACHE_KEY);

                _logger.LogInformation("成功更新游戏化规则: {RuleId}", rule.Id);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新游戏化规则时发生错误: {RuleId}", rule.Id);
                return false;
            }
        }

        /// <summary>
        /// 删除规则
        /// </summary>
        public async Task<bool> DeleteRuleAsync(string ruleId)
        {
            try
            {
                var behaviorType = await _context.BehaviorTypes
                    .FirstOrDefaultAsync(bt => bt.Code == ruleId);

                if (behaviorType == null)
                {
                    _logger.LogWarning("规则 {RuleId} 不存在", ruleId);
                    return false;
                }

                _context.BehaviorTypes.Remove(behaviorType);
                await _context.SaveChangesAsync();

                // 清除缓存
                _cache.Remove(RULES_CACHE_KEY);

                _logger.LogInformation("成功删除游戏化规则: {RuleId}", ruleId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除游戏化规则时发生错误: {RuleId}", ruleId);
                return false;
            }
        }

        /// <summary>
        /// 启用/禁用规则
        /// </summary>
        public async Task<bool> SetRuleActiveAsync(string ruleId, bool isActive)
        {
            try
            {
                var behaviorType = await _context.BehaviorTypes
                    .FirstOrDefaultAsync(bt => bt.Code == ruleId);

                if (behaviorType == null)
                {
                    _logger.LogWarning("规则 {RuleId} 不存在", ruleId);
                    return false;
                }

                behaviorType.IsActive = isActive;
                behaviorType.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                // 清除缓存
                _cache.Remove(RULES_CACHE_KEY);

                _logger.LogInformation("成功{Action}游戏化规则: {RuleId}", isActive ? "启用" : "禁用", ruleId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "{Action}游戏化规则时发生错误: {RuleId}", isActive ? "启用" : "禁用", ruleId);
                return false;
            }
        }

        /// <summary>
        /// 获取所有规则
        /// </summary>
        public async Task<IEnumerable<GamificationRule>> GetAllRulesAsync(string? clientId = null)
        {
            var allRules = await GetCachedRulesAsync();
            
            if (string.IsNullOrEmpty(clientId))
            {
                return allRules;
            }

            return allRules.Where(r => string.IsNullOrEmpty(r.ClientId) || r.ClientId == clientId);
        }

        /// <summary>
        /// 验证规则条件
        /// </summary>
        public async Task<bool> EvaluateConditionAsync(string condition, object eventData)
        {
            try
            {
                // 如果条件为空或默认值，返回true
                if (string.IsNullOrEmpty(condition) || condition == "{}")
                {
                    return true;
                }

                var conditionObj = JObject.Parse(condition);
                var eventDataObj = JObject.FromObject(eventData);

                // 简单的条件评估逻辑
                foreach (var property in conditionObj.Properties())
                {
                    var expectedValue = property.Value;
                    var actualValue = eventDataObj.SelectToken(property.Name);

                    if (actualValue == null || !JToken.DeepEquals(expectedValue, actualValue))
                    {
                        return false;
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "评估条件时发生错误: {Condition}", condition);
                return false;
            }
        }

        /// <summary>
        /// 重新加载规则缓存
        /// </summary>
        public async Task ReloadRulesAsync()
        {
            _cache.Remove(RULES_CACHE_KEY);
            await GetCachedRulesAsync();
            _logger.LogInformation("游戏化规则缓存已重新加载");
        }

        #region 私有方法

        /// <summary>
        /// 获取缓存的规则
        /// </summary>
        private async Task<IEnumerable<GamificationRule>> GetCachedRulesAsync()
        {
            return await _cache.GetOrCreateAsync(RULES_CACHE_KEY, async entry =>
            {
                entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(CACHE_EXPIRY_MINUTES);

                var behaviorTypes = await _context.BehaviorTypes
                    .Where(bt => bt.IsActive)
                    .ToListAsync();

                return behaviorTypes.Select(bt => new GamificationRule
                {
                    Id = bt.Code,
                    Name = bt.Name,
                    EventType = bt.Category,
                    Condition = "{}",
                    PointsReward = bt.BasePoints,
                    CoinsReward = bt.BaseCoins,
                    DiamondsReward = bt.BaseDiamonds,
                    ExperienceReward = bt.BasePoints, // 经验值等于积分
                    IsActive = bt.IsActive,
                    Priority = 100,
                    ClientId = null,
                    CreatedAt = bt.CreatedAt,
                    UpdatedAt = bt.UpdatedAt
                }).ToList();
            }) ?? Enumerable.Empty<GamificationRule>();
        }

        /// <summary>
        /// 验证规则
        /// </summary>
        private bool ValidateRule(GamificationRule rule)
        {
            if (string.IsNullOrEmpty(rule.Id) || string.IsNullOrEmpty(rule.Name) || string.IsNullOrEmpty(rule.EventType))
            {
                _logger.LogWarning("规则验证失败: ID、名称或事件类型为空");
                return false;
            }

            if (rule.PointsReward < 0 || rule.CoinsReward < 0 || rule.DiamondsReward < 0 || rule.ExperienceReward < 0)
            {
                _logger.LogWarning("规则验证失败: 奖励值不能为负数");
                return false;
            }

            return true;
        }

        /// <summary>
        /// 将事件转换为数据对象
        /// </summary>
        private object ConvertEventToData(GamificationEventBase gamificationEvent)
        {
            return new
            {
                UserId = gamificationEvent.UserId,
                Source = gamificationEvent.Source,
                ReferenceId = gamificationEvent.ReferenceId,
                OccurredAt = gamificationEvent.OccurredAt,
                EventData = gamificationEvent
            };
        }

        /// <summary>
        /// 合并奖励
        /// </summary>
        private GamificationReward CombineRewards(GamificationReward reward1, GamificationReward reward2)
        {
            return GamificationReward.Create(
                reward1.Points + reward2.Points,
                reward1.Coins + reward2.Coins,
                reward1.Diamonds + reward2.Diamonds,
                reward1.Experience + reward2.Experience,
                $"{reward1.Description}; {reward2.Description}".Trim(';', ' '),
                $"{reward1.Source},{reward2.Source}".Trim(',')
            );
        }

        /// <summary>
        /// 获取默认奖励
        /// </summary>
        private GamificationReward GetDefaultReward(string eventType)
        {
            return eventType switch
            {
                "TaskCreated" => GamificationReward.Create(5, 3, 0, 5, "创建任务默认奖励", "default"),
                "TaskCompleted" => GamificationReward.Create(20, 10, 1, 20, "完成任务默认奖励", "default"),
                "TaskClaimed" => GamificationReward.Create(3, 2, 0, 3, "认领任务默认奖励", "default"),
                "AssetUpdated" => GamificationReward.Create(8, 5, 0, 8, "更新资产默认奖励", "default"),
                "FaultReported" => GamificationReward.Create(15, 8, 0, 15, "报告故障默认奖励", "default"),
                "DailyLogin" => GamificationReward.Create(2, 1, 0, 2, "每日登录默认奖励", "default"),
                "CommentAdded" => GamificationReward.Create(1, 1, 0, 1, "添加评论默认奖励", "default"),
                _ => GamificationReward.Empty
            };
        }

        #endregion
    }
}
