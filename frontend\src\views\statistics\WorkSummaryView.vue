<template>
  <div class="work-summary-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h1 class="page-title">
            <el-icon><DataAnalysis /></el-icon>
            工作汇总报告
          </h1>
          <p class="page-subtitle">本周工作汇总 (2024年第25周)</p>
        </div>
        <div class="action-section">
          <el-select v-model="periodType" @change="loadWorkSummary" class="period-select">
            <el-option label="今日" value="daily" />
            <el-option label="本周" value="weekly" />
            <el-option label="本月" value="monthly" />
            <el-option label="本季度" value="quarterly" />
            <el-option label="本年" value="yearly" />
          </el-select>
          <el-button
            type="primary"
            :icon="Refresh"
            @click="forceRefreshWorkSummary"
            :loading="loading"
          >
            刷新
          </el-button>
          <el-button 
            type="success" 
            :icon="Download" 
            @click="exportData"
          >
            导出
          </el-button>
        </div>
      </div>
    </div>

    <!-- 工作汇总表格 -->
    <el-card class="summary-card">
      <template #header>
        <div class="card-header">
          <span>📊 工作汇总统计</span>
          <div class="header-stats">
            <span class="stat-item">共 {{ workSummary.length }} 人</span>
          </div>
        </div>
      </template>
      
      <el-table 
        :data="workSummary" 
        v-loading="loading"
        stripe
        border
        :default-sort="{ prop: 'pointsRank', order: 'ascending' }"
        class="work-summary-table"
      >
        <el-table-column type="index" label="#" width="50" align="center" />
        <el-table-column prop="userName" label="姓名" width="100" fixed="left" />
        <el-table-column prop="departmentName" label="部门" width="120" />
        
        <!-- 任务统计组 -->
        <el-table-column label="📋 任务" align="center">
          <el-table-column prop="tasksCreated" label="新建" width="60" align="center" />
          <el-table-column prop="tasksClaimed" label="认领" width="60" align="center" />
          <el-table-column prop="tasksCompleted" label="完成" width="60" align="center" />
          <el-table-column prop="tasksTotal" label="总计" width="60" align="center">
            <template #default="{ row }">
              <strong>{{ row.tasksTotal || 0 }}</strong>
            </template>
          </el-table-column>
        </el-table-column>

        <!-- 资产统计组 -->
        <el-table-column label="🏢 资产" align="center">
          <el-table-column prop="assetsCreated" label="新建" width="60" align="center" />
          <el-table-column prop="assetsUpdated" label="更新" width="60" align="center" />
          <el-table-column prop="assetsDeleted" label="删除" width="60" align="center" />
          <el-table-column prop="assetsTotal" label="总计" width="60" align="center">
            <template #default="{ row }">
              <strong>{{ row.assetsTotal || 0 }}</strong>
            </template>
          </el-table-column>
        </el-table-column>

        <!-- 故障统计组 -->
        <el-table-column label="🔧 故障" align="center">
          <el-table-column prop="faultsReported" label="登记" width="60" align="center" />
          <el-table-column prop="faultsRepaired" label="维修" width="60" align="center" />
          <el-table-column prop="faultsTotal" label="总计" width="60" align="center">
            <template #default="{ row }">
              <strong>{{ row.faultsTotal || 0 }}</strong>
            </template>
          </el-table-column>
        </el-table-column>

        <!-- 采购统计组 -->
        <el-table-column label="🛒 采购" align="center">
          <el-table-column prop="procurementsCreated" label="新建" width="60" align="center" />
          <el-table-column prop="procurementsUpdated" label="更新" width="60" align="center" />
          <el-table-column prop="procurementsTotal" label="总计" width="60" align="center">
            <template #default="{ row }">
              <strong>{{ row.procurementsTotal || 0 }}</strong>
            </template>
          </el-table-column>
        </el-table-column>

        <!-- 备件统计组 -->
        <el-table-column label="⚙️ 备件" align="center">
          <el-table-column prop="partsIn" label="入库" width="60" align="center" />
          <el-table-column prop="partsOut" label="出库" width="60" align="center" />
          <el-table-column prop="partsAdded" label="新增" width="60" align="center" />
          <el-table-column prop="partsTotal" label="总计" width="60" align="center">
            <template #default="{ row }">
              <strong>{{ row.partsTotal || 0 }}</strong>
            </template>
          </el-table-column>
        </el-table-column>

        <!-- 积分统计组 -->
        <el-table-column label="🏆 积分" align="center">
          <el-table-column prop="totalPointsEarned" label="积分" width="80" align="center">
            <template #default="{ row }">
              <span class="points-value">{{ row.totalPointsEarned || 0 }}</span>
            </template>
          </el-table-column>
        </el-table-column>

        <!-- 排名 -->
        <el-table-column prop="pointsRank" label="排名" width="60" align="center">
          <template #default="{ row }">
            <el-tag 
              :type="getRankTagType(row.pointsRank)" 
              size="small"
              round
            >
              {{ row.pointsRank || '-' }}
            </el-tag>
          </template>
        </el-table-column>

        <!-- 评价 -->
        <el-table-column prop="evaluation" label="评价" width="100" align="center">
          <template #default="{ row }">
            <el-tag 
              :type="getEvaluationTagType(row.evaluation)" 
              size="small"
              effect="light"
            >
              {{ row.evaluation || '待评价' }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { DataAnalysis, Refresh, Download } from '@element-plus/icons-vue'
import { statisticsApi } from '@/api/statistics'
import notificationService from '@/utils/notification-service'

// 响应式数据
const loading = ref(false)
const periodType = ref('weekly')
const workSummary = ref([])

// 加载工作汇总数据
const loadWorkSummary = async (forceRefresh = false) => {
  loading.value = true
  try {
    const response = await statisticsApi.getWorkSummary({
      periodType: periodType.value,
      limit: 100,
      forceRefresh: forceRefresh
    })

    // 修复响应数据结构处理
    if (response.success) {
      workSummary.value = response.data || []
      ElMessage.success(`成功加载 ${workSummary.value.length} 条工作汇总数据`)
    } else {
      throw new Error(response.message || '获取工作汇总失败')
    }
  } catch (error) {
    console.error('获取工作汇总失败:', error)
    ElMessage.error('获取工作汇总失败，使用模拟数据')
    // 使用模拟数据作为后备
    workSummary.value = generateMockData()
  } finally {
    loading.value = false
  }
}

// 生成模拟数据
const generateMockData = () => {
  return [
    {
      userName: '张三',
      tasksCompleted: 12,
      tasksCreated: 8,
      assetsCreated: 5,
      assetsUpdated: 2,
      faultsReported: 3,
      faultsRepaired: 1,
      procurementsCreated: 2,
      procurementsUpdated: 4,
      partsIn: 15,
      partsOut: 6,
      partsAdded: 3,
      totalPointsEarned: 156,
      pointsRank: 1,
      evaluation: '超级'
    },
    {
      userName: '李四',
      tasksCompleted: 9,
      tasksCreated: 15,
      assetsCreated: 3,
      assetsUpdated: 4,
      faultsReported: 1,
      faultsRepaired: 2,
      procurementsCreated: 1,
      procurementsUpdated: 1,
      partsIn: 14,
      partsOut: 2,
      partsAdded: 2,
      totalPointsEarned: 142,
      pointsRank: 2,
      evaluation: '优秀'
    },
    {
      userName: '王五',
      tasksCompleted: 10,
      tasksCreated: 6,
      assetsCreated: 8,
      assetsUpdated: 1,
      faultsReported: 2,
      faultsRepaired: 1,
      procurementsCreated: 2,
      procurementsUpdated: 2,
      partsIn: 13,
      partsOut: 8,
      partsAdded: 2,
      totalPointsEarned: 138,
      pointsRank: 3,
      evaluation: '优秀'
    }
  ]
}

// 获取排名标签类型
const getRankTagType = (rank) => {
  if (rank === 1) return 'danger'
  if (rank === 2) return 'warning'
  if (rank === 3) return 'success'
  return 'info'
}

// 获取评价标签类型
const getEvaluationTagType = (evaluation) => {
  switch (evaluation) {
    case '超级': return 'danger'
    case '优秀': return 'success'
    case '良好': return 'warning'
    case '一般': return 'info'
    default: return ''
  }
}

// 导出数据
const exportData = () => {
  ElMessage.info('导出功能开发中...')
}

// 强制刷新工作汇总数据
const forceRefreshWorkSummary = async () => {
  try {
    // 先调用后端更新数据
    await statisticsApi.updateWorkSummary({
      periodType: periodType.value
    })

    // 然后获取最新数据
    await loadWorkSummary(true)
  } catch (error) {
    console.error('强制刷新失败:', error)
    ElMessage.error('刷新数据失败')
  }
}

// 注册实时更新监听
const registerRealtimeUpdates = () => {
  if (notificationService.isConnected) {
    // 监听工作汇总更新事件
    notificationService.connection?.on('WorkSummaryUpdated', handleWorkSummaryUpdate)
    console.log('已注册工作汇总实时更新监听')
  } else {
    console.warn('SignalR未连接，无法注册实时更新')
  }
}

// 取消实时更新监听
const unregisterRealtimeUpdates = () => {
  if (notificationService.isConnected) {
    // 取消监听工作汇总更新事件
    notificationService.connection?.off('WorkSummaryUpdated', handleWorkSummaryUpdate)
    console.log('已取消工作汇总实时更新监听')
  }
}

// 处理实时更新数据
const handleWorkSummaryUpdate = (data) => {
  console.log('收到工作汇总实时更新:', data)

  if (data.Type === 'leaderboard_update' && data.Data) {
    // 更新排行榜数据
    workSummary.value = data.Data
    ElMessage.success('数据已实时更新')
  } else if (data.Type === 'data_refresh') {
    // 数据刷新，重新加载
    loadWorkSummary(true)
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadWorkSummary()
  registerRealtimeUpdates()
})

// 组件卸载时取消监听
onUnmounted(() => {
  unregisterRealtimeUpdates()
})
</script>

<style scoped>
.work-summary-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 20px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.title-section h1 {
  margin: 0;
  color: #303133;
  font-size: 24px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-subtitle {
  margin: 5px 0 0 0;
  color: #909399;
  font-size: 14px;
}

.action-section {
  display: flex;
  gap: 12px;
  align-items: center;
}

.period-select {
  width: 120px;
}

.summary-card {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-stats {
  display: flex;
  gap: 16px;
}

.stat-item {
  color: #909399;
  font-size: 14px;
}

.work-summary-table {
  font-size: 13px;
}

.points-value {
  font-weight: 600;
  color: #E6A23C;
}

:deep(.el-table th) {
  background-color: #fafafa;
  font-weight: 600;
}

:deep(.el-table .el-table__cell) {
  padding: 8px 0;
}
</style>
