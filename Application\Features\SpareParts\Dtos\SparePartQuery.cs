// File: Application/Features/SpareParts/Dtos/SparePartQuery.cs
// Description: 备品备件查询参数类

using ItAssetsSystem.Application.Common.Dtos;

namespace ItAssetsSystem.Application.Features.SpareParts.Dtos
{
    /// <summary>
    /// 备品备件查询参数类
    /// </summary>
    public class SparePartQuery : PaginationQuery
    {
        /// <summary>
        /// 关键字（支持名称、编号、规格、品牌）- 可选，如果Name和Code也提供，优先使用Name和Code
        /// </summary>
        public string Keyword { get; set; }

        /// <summary>
        /// 备件名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 备件编号
        /// </summary>
        public string Code { get; set; }
        
        /// <summary>
        /// 备件类型ID
        /// </summary>
        public long? TypeId { get; set; }
        
        /// <summary>
        /// 库位ID
        /// </summary>
        public long? LocationId { get; set; }
        
        /// <summary>
        /// 库位区域
        /// </summary>
        public string LocationArea { get; set; }
        
        /// <summary>
        /// 库存状态 (normal, warning, danger)
        /// </summary>
        public string StockStatus { get; set; }

        /// <summary>
        /// 库存预警标识（true: 低于预警阈值, false: 低于最小库存） - 注意：此字段可能与StockStatus功能重叠，优先使用StockStatus
        /// </summary>
        public bool? LowStock { get; set; }
        
        /// <summary>
        /// 排序字段（code/name/stock/stockQuantity/creationtime）
        /// </summary>
        public new string SortBy { get; set; }
        
        /// <summary>
        /// 排序方向（asc/desc）
        /// </summary>
        public new string SortDirection { get; set; } = "asc";
    }
} 