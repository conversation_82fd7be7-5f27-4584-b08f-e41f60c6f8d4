-- 创建周期性任务计划负责人关联表
CREATE TABLE IF NOT EXISTS `periodic_task_schedule_assignees` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `periodic_task_schedule_id` bigint NOT NULL,
  `user_id` int NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_schedule_user` (`periodic_task_schedule_id`, `user_id`),
  KEY `idx_schedule_id` (`periodic_task_schedule_id`),
  KEY `idx_user_id` (`user_id`),
  CONSTRAINT `fk_periodic_schedule_assignees_schedule` 
    FOREIGN KEY (`periodic_task_schedule_id`) 
    REFERENCES `periodictaskschedules` (`periodic_task_schedule_id`) 
    ON DELETE CASCADE,
  CONSTRAINT `fk_periodic_schedule_assignees_user` 
    FOREIGN KEY (`user_id`) 
    REFERENCES `users` (`Id`) 
    ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 迁移现有数据：从描述字段中提取多负责人信息并插入到新表
INSERT INTO `periodic_task_schedule_assignees` (`periodic_task_schedule_id`, `user_id`, `created_at`)
SELECT 
    pts.periodic_task_schedule_id,
    CAST(JSON_EXTRACT(
        SUBSTRING(
            pts.description, 
            LOCATE('[ASSIGNEES:', pts.description) + 11,
            LOCATE(']', pts.description, LOCATE('[ASSIGNEES:', pts.description)) - LOCATE('[ASSIGNEES:', pts.description) - 11
        ),
        CONCAT('$[', numbers.n, ']')
    ) AS UNSIGNED) as user_id,
    NOW() as created_at
FROM periodictaskschedules pts
CROSS JOIN (
    SELECT 0 as n UNION ALL SELECT 1 UNION ALL SELECT 2 UNION ALL 
    SELECT 3 UNION ALL SELECT 4 UNION ALL SELECT 5 UNION ALL 
    SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9
) numbers
WHERE pts.description LIKE '%[ASSIGNEES:%]%'
  AND JSON_EXTRACT(
        SUBSTRING(
            pts.description, 
            LOCATE('[ASSIGNEES:', pts.description) + 11,
            LOCATE(']', pts.description, LOCATE('[ASSIGNEES:', pts.description)) - LOCATE('[ASSIGNEES:', pts.description) - 11
        ),
        CONCAT('$[', numbers.n, ']')
    ) IS NOT NULL
ON DUPLICATE KEY UPDATE created_at = VALUES(created_at);

-- 清理描述字段中的多负责人信息（可选，建议先备份）
-- UPDATE periodictaskschedules 
-- SET description = TRIM(REGEXP_REPLACE(description, '\\[ASSIGNEES:\\[.*?\\]\\]', ''))
-- WHERE description LIKE '%[ASSIGNEES:%]%';
