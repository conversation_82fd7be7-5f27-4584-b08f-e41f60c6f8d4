<template>
  <div class="my-tasks-container">
    <el-card class="task-card">
      <template #header>
        <div class="card-header">
          <h2>我的任务</h2>
          <div class="header-actions">
            <el-button type="primary" @click="refreshData">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
            <el-button type="success" @click="createTask">
              <el-icon><Plus /></el-icon>
              新建任务
            </el-button>
          </div>
        </div>
      </template>
      
      <div class="filter-container">
        <el-form :inline="true" :model="filterForm" class="filter-form">
          <el-form-item label="状态">
            <el-select v-model="filterForm.status" placeholder="任务状态" clearable>
              <el-option label="待处理" value="pending" />
              <el-option label="进行中" value="in_progress" />
              <el-option label="已完成" value="completed" />
              <el-option label="已取消" value="cancelled" />
            </el-select>
          </el-form-item>
          <el-form-item label="优先级">
            <el-select v-model="filterForm.priority" placeholder="优先级" clearable>
              <el-option label="低" value="low" />
              <el-option label="中" value="medium" />
              <el-option label="高" value="high" />
              <el-option label="紧急" value="urgent" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleFilter">筛选</el-button>
            <el-button @click="resetFilter">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      
      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="10" animated />
      </div>
      
      <div v-else-if="tasks.length === 0" class="empty-data">
        <el-empty description="暂无任务数据" />
      </div>
      
      <div v-else>
        <el-table
          :data="tasks"
          style="width: 100%"
          @row-click="handleRowClick"
          v-loading="tableLoading"
        >
          <el-table-column prop="taskId" label="ID" width="70" />
          <el-table-column prop="title" label="任务名称" min-width="200" show-overflow-tooltip />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag :type="getStatusType(scope.row.status)">{{ getStatusText(scope.row.status) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="priority" label="优先级" width="100">
            <template #default="scope">
              <el-tag :type="getPriorityType(scope.row.priority)" effect="dark">{{ getPriorityText(scope.row.priority) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="dueDate" label="截止日期" width="120" />
          <el-table-column prop="progress" label="进度" width="180">
            <template #default="scope">
              <el-progress :percentage="scope.row.progress || 0" :status="getProgressStatus(scope.row)" />
            </template>
          </el-table-column>
          <el-table-column label="负责人" width="160">
            <template #default="scope">
              <UserAvatarStack
                :users="getAllAssignees(scope.row)"
                :is-main-user-primary="true"
                :max-users="4"
                avatar-size="18"
                :overlap="6"
                class="small"
              />
            </template>
          </el-table-column>
          <el-table-column prop="creatorName" label="创建人" width="120" />
          <el-table-column label="操作" width="180" fixed="right">
            <template #default="scope">
              <el-button
                size="small"
                type="primary"
                @click.stop="viewTaskDetail(scope.row)"
              >
                详情
              </el-button>
              <el-button
                size="small"
                type="success"
                v-if="scope.row.status !== 'completed'"
                @click.stop="updateProgress(scope.row)"
              >
                更新进度
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <div class="pagination-container">
          <el-pagination
            background
            layout="total, sizes, prev, pager, next"
            :page-sizes="[10, 20, 50, 100]"
            :total="total"
            :page-size="pageSize"
            :current-page="currentPage"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-card>
    
    <!-- 更新进度对话框 -->
    <el-dialog
      v-model="progressDialogVisible"
      title="更新任务进度"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form :model="progressForm" label-width="100px">
        <el-form-item label="当前进度">
          <el-slider
            v-model="progressForm.progress"
            :format-tooltip="formatProgressTooltip"
            :step="5"
            show-stops
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="progressForm.status" placeholder="选择状态">
            <el-option label="待处理" value="pending" />
            <el-option label="进行中" value="in_progress" />
            <el-option label="已完成" value="completed" />
          </el-select>
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="progressForm.remarks"
            type="textarea"
            :rows="3"
            placeholder="请输入进度更新备注"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="progressDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitProgressUpdate" :loading="submitting">
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { taskApi } from '@/api/task'
import UserAvatarStack from '@/components/UserAvatarStack.vue'

export default {
  name: 'MyTasksView',
  
  components: {
    UserAvatarStack
  },
  
  setup() {
    const router = useRouter()
    const tasks = ref([])
    const loading = ref(true)
    const tableLoading = ref(false)
    const total = ref(0)
    const currentPage = ref(1)
    const pageSize = ref(10)
    
    // 筛选表单
    const filterForm = reactive({
      status: '',
      priority: ''
    })
    
    // 进度更新相关
    const progressDialogVisible = ref(false)
    const progressForm = reactive({
      taskId: null,
      progress: 0,
      status: '',
      remarks: ''
    })
    const submitting = ref(false)
    
    // 加载任务数据
    const loadTasks = async () => {
      loading.value = true
      try {
        const params = {
          pageIndex: currentPage.value,
          pageSize: pageSize.value,
          assignedToMe: true // 只查询分配给当前用户的任务
        }
        
        // 添加筛选条件
        if (filterForm.status) {
          params.status = filterForm.status
        }
        if (filterForm.priority) {
          params.priority = filterForm.priority
        }
        
        const response = await taskApi.getTasks(params)
        if (response.success) {
          tasks.value = response.data.items
          total.value = response.data.totalCount
        } else {
          ElMessage.error(response.message || '加载任务数据失败')
        }
      } catch (error) {
        console.error('加载任务数据出错:', error)
        ElMessage.error('加载任务数据时发生错误')
      } finally {
        loading.value = false
      }
    }
    
    // 刷新数据
    const refreshData = () => {
      loadTasks()
    }
    
    // 处理筛选
    const handleFilter = () => {
      currentPage.value = 1
      loadTasks()
    }
    
    // 重置筛选
    const resetFilter = () => {
      filterForm.status = ''
      filterForm.priority = ''
      currentPage.value = 1
      loadTasks()
    }
    
    // 处理页面大小变化
    const handleSizeChange = (val) => {
      pageSize.value = val
      loadTasks()
    }
    
    // 处理页码变化
    const handleCurrentChange = (val) => {
      currentPage.value = val
      loadTasks()
    }
    
    // 获取状态类型对应的Element UI标签类型
    const getStatusType = (status) => {
      const statusMap = {
        'pending': 'info',
        'in_progress': 'warning',
        'completed': 'success',
        'cancelled': 'danger'
      }
      return statusMap[status] || 'info'
    }
    
    // 获取状态文本
    const getStatusText = (status) => {
      const statusTextMap = {
        'pending': '待处理',
        'in_progress': '进行中',
        'completed': '已完成',
        'cancelled': '已取消'
      }
      return statusTextMap[status] || status
    }
    
    // 获取优先级类型对应的Element UI标签类型
    const getPriorityType = (priority) => {
      const priorityMap = {
        'low': 'info',
        'medium': 'success',
        'high': 'warning',
        'urgent': 'danger'
      }
      return priorityMap[priority] || 'info'
    }
    
    // 获取优先级文本
    const getPriorityText = (priority) => {
      const priorityTextMap = {
        'low': '低',
        'medium': '中',
        'high': '高',
        'urgent': '紧急'
      }
      return priorityTextMap[priority] || priority
    }
    
    // 获取进度条状态
    const getProgressStatus = (task) => {
      if (task.status === 'completed') return 'success'
      if (task.status === 'cancelled') return 'exception'
      
      // 检查是否超期
      if (task.dueDate) {
        const now = new Date()
        const dueDate = new Date(task.dueDate)
        if (now > dueDate && task.progress < 100) {
          return 'exception'
        }
      }
      
      return ''
    }
    
    // 格式化进度条提示
    const formatProgressTooltip = (val) => {
      return `${val}%`
    }
    
    // 处理行点击
    const handleRowClick = (row) => {
      viewTaskDetail(row)
    }
    
    // 查看任务详情
    const viewTaskDetail = (task) => {
      router.push({
        name: 'TaskDetail',
        params: { id: task.taskId }
      })
    }
    
    // 创建任务
    const createTask = () => {
      router.push({ name: 'TaskCreate' })
    }
    
    // 更新进度
    const updateProgress = (task) => {
      progressForm.taskId = task.taskId
      progressForm.progress = task.progress || 0
      progressForm.status = task.status
      progressForm.remarks = ''
      progressDialogVisible.value = true
    }
    
    // 提交进度更新
    const submitProgressUpdate = async () => {
      if (!progressForm.taskId) return
      
      submitting.value = true
      try {
        const response = await taskApi.updateTaskProgress(
          progressForm.taskId,
          {
            progress: progressForm.progress,
            remarks: progressForm.remarks
          }
        )
        
        if (response.success) {
          ElMessage.success('任务进度更新成功')
          progressDialogVisible.value = false
          
          // 如果状态发生变化，还需要更新状态
          if (progressForm.status !== response.data.status) {
            await taskApi.updateTaskStatus(
              progressForm.taskId,
              {
                status: progressForm.status,
                remarks: `状态更新为: ${getStatusText(progressForm.status)}`
              }
            )
          }
          
          // 刷新数据
          loadTasks()
        } else {
          ElMessage.error(response.message || '更新任务进度失败')
        }
      } catch (error) {
        console.error('更新任务进度出错:', error)
        ElMessage.error('更新任务进度时发生错误')
      } finally {
        submitting.value = false
      }
    }
    
    // 获取所有负责人（主负责人 + 协作人员）- 修复显示逻辑
    const getAllAssignees = (task) => {
      if (!task) return []

      console.log('MyTasksView - 原始task数据:', {
        taskId: task.taskId,
        assigneeUserId: task.assigneeUserId,
        assigneeName: task.assigneeName,
        assignees: task.assignees
      })

      const assignees = []

      // 优先使用后端返回的 assignees 字段（包含完整的负责人信息）
      if (task.assignees && Array.isArray(task.assignees) && task.assignees.length > 0) {
        task.assignees.forEach(assignee => {
          assignees.push({
            id: assignee.userId,
            name: assignee.userName || '未知用户',
            avatarUrl: assignee.avatarUrl || '',
            role: assignee.role || (assignee.assignmentType === 'Assignee' ? 'Primary' : 'Collaborator'),
            isPrimary: assignee.role === 'Primary' || assignee.assignmentType === 'Assignee'
          })
        })
      } else {
        // 如果没有 assignees 字段，回退到旧逻辑（仅显示主负责人）
        if (task.assigneeUserId) {
          assignees.push({
            id: task.assigneeUserId,
            name: task.assigneeName || '未知用户',
            avatarUrl: task.assigneeAvatarUrl || '',
            role: 'Primary',
            isPrimary: true
          })
        }
      }

      console.log('MyTasksView - 最终assignees结果:', assignees)
      return assignees
    }
    
    onMounted(() => {
      loadTasks()
    })
    
    return {
      tasks,
      loading,
      tableLoading,
      total,
      currentPage,
      pageSize,
      filterForm,
      progressDialogVisible,
      progressForm,
      submitting,
      refreshData,
      handleFilter,
      resetFilter,
      handleSizeChange,
      handleCurrentChange,
      getStatusType,
      getStatusText,
      getPriorityType,
      getPriorityText,
      getProgressStatus,
      formatProgressTooltip,
      handleRowClick,
      viewTaskDetail,
      createTask,
      updateProgress,
      submitProgressUpdate,
      getAllAssignees
    }
  }
}
</script>

<style scoped>
.my-tasks-container {
  padding: 20px;
}

.task-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.filter-container {
  margin-bottom: 20px;
}

.filter-form {
  display: flex;
  flex-wrap: wrap;
}

.loading-container {
  padding: 20px;
}

.empty-data {
  padding: 40px 0;
  text-align: center;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style> 