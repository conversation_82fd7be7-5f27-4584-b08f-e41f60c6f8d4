using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using ItAssetsSystem.Infrastructure.Data;
using ItAssetsSystem.Models.Entities.Gamification;
using ItAssetsSystem.Models.Entities.Statistics;
using ItAssetsSystem.Models.Enums;

namespace ItAssetsSystem.Application.Features.Gamification.Services
{
    /// <summary>
    /// 数据一致性后台服务
    /// 负责处理待处理事件、校验数据一致性、修复不一致数据
    /// </summary>
    public class DataConsistencyService : BackgroundService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<DataConsistencyService> _logger;
        private readonly TimeSpan _checkInterval = TimeSpan.FromMinutes(5); // 每5分钟检查一次
        private readonly TimeSpan _eventTimeout = TimeSpan.FromMinutes(10); // 事件超时时间

        public DataConsistencyService(IServiceProvider serviceProvider, ILogger<DataConsistencyService> logger)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("数据一致性服务已启动");

            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    using var scope = _serviceProvider.CreateScope();
                    var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();
                    var consistentTracker = scope.ServiceProvider.GetRequiredService<IConsistentBehaviorTracker>();

                    // 🔍 处理待处理和失败的事件
                    await ProcessPendingEventsAsync(context, consistentTracker);

                    // 🔍 验证数据一致性
                    await ValidateDataConsistencyAsync(context);

                    // 🔍 清理过期事件
                    await CleanupOldEventsAsync(context);

                    _logger.LogDebug("数据一致性检查完成");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "数据一致性检查失败");
                }

                try
                {
                    await Task.Delay(_checkInterval, stoppingToken);
                }
                catch (OperationCanceledException)
                {
                    break;
                }
            }

            _logger.LogInformation("数据一致性服务已停止");
        }

        /// <summary>
        /// 处理待处理的事件
        /// </summary>
        private async Task ProcessPendingEventsAsync(AppDbContext context, IConsistentBehaviorTracker tracker)
        {
            try
            {
                var cutoffTime = DateTime.Now.Subtract(_eventTimeout);
                
                // 查找超时的待处理事件
                var pendingEvents = await context.UserBehaviorEvents
                    .Where(e => (e.ProcessingStatus == ProcessingStatus.Pending.ToStatusString() || 
                                e.ProcessingStatus == ProcessingStatus.Failed.ToStatusString()) &&
                               e.CreatedAt < cutoffTime &&
                               e.RetryCount < 3) // 最多重试3次
                    .OrderBy(e => e.CreatedAt)
                    .Take(50) // 每次处理50个
                    .ToListAsync();

                if (pendingEvents.Any())
                {
                    _logger.LogInformation("发现 {Count} 个待处理事件", pendingEvents.Count);

                    foreach (var @event in pendingEvents)
                    {
                        try
                        {
                            var success = await tracker.RetryFailedEventAsync(@event.Id);
                            if (success)
                            {
                                _logger.LogInformation("事件重试成功: EventId={EventId}", @event.Id);
                            }
                            else
                            {
                                _logger.LogWarning("事件重试失败: EventId={EventId}", @event.Id);
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "重试事件时发生错误: EventId={EventId}", @event.Id);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理待处理事件时发生错误");
            }
        }

        /// <summary>
        /// 验证数据一致性
        /// </summary>
        private async Task ValidateDataConsistencyAsync(AppDbContext context)
        {
            try
            {
                // 🔍 抽样检查活跃用户的数据一致性
                var randomUsers = await GetRandomActiveUsersAsync(context, 10);
                
                foreach (var userId in randomUsers)
                {
                    var isConsistent = await ValidateUserDataAsync(context, userId);
                    if (!isConsistent)
                    {
                        _logger.LogWarning("发现用户数据不一致: UserId={UserId}", userId);
                        await RepairUserDataAsync(context, userId);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证数据一致性时发生错误");
            }
        }

        /// <summary>
        /// 验证单个用户的数据一致性
        /// </summary>
        private async Task<bool> ValidateUserDataAsync(AppDbContext context, int userId)
        {
            try
            {
                var currentWeek = GetCurrentWeekStart();
                
                // 从事件表计算真实数据
                var actualStats = await CalculateStatsFromEventsAsync(context, userId, currentWeek);
                
                // 从汇总表获取当前数据
                var summaryStats = await GetStatsFromSummaryAsync(context, userId, currentWeek);
                
                // 比较数据是否一致
                return CompareStats(actualStats, summaryStats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证用户数据时发生错误: UserId={UserId}", userId);
                return true; // 出错时假设一致，避免误报
            }
        }

        /// <summary>
        /// 修复用户数据
        /// </summary>
        private async Task RepairUserDataAsync(AppDbContext context, int userId)
        {
            using var transaction = await context.Database.BeginTransactionAsync();
            try
            {
                var currentWeek = GetCurrentWeekStart();
                
                // 🔄 从事件表重新计算
                var actualStats = await CalculateStatsFromEventsAsync(context, userId, currentWeek);
                
                // 🔧 修复汇总表
                var summary = await context.UserWorkSummaries
                    .FirstOrDefaultAsync(s => s.UserId == userId && 
                                       s.PeriodType == "weekly" && 
                                       s.PeriodDate == currentWeek);

                if (summary != null)
                {
                    // 更新汇总数据
                    summary.TasksClaimed = actualStats.TasksClaimed;
                    summary.TasksCompleted = actualStats.TasksCompleted;
                    summary.TasksCreated = actualStats.TasksCreated;
                    summary.TasksCommented = actualStats.TasksCommented;
                    summary.AssetsCreated = actualStats.AssetsCreated;
                    summary.AssetsUpdated = actualStats.AssetsUpdated;
                    summary.AssetsDeleted = actualStats.AssetsDeleted;
                    summary.FaultsReported = actualStats.FaultsReported;
                    summary.FaultsRepaired = actualStats.FaultsRepaired;
                    summary.TotalPointsEarned = actualStats.TotalPoints;
                    summary.TotalCoinsEarned = actualStats.TotalCoins;
                    summary.TotalDiamondsEarned = actualStats.TotalDiamonds;
                    summary.TotalXpEarned = actualStats.TotalXp;
                    summary.UpdatedAt = DateTime.Now;
                    summary.LastReconciledAt = DateTime.Now;
                    summary.Version++;

                    await context.SaveChangesAsync();
                    await transaction.CommitAsync();
                    
                    _logger.LogInformation("用户数据修复完成: UserId={UserId}, Week={Week}", userId, currentWeek);
                }
                else
                {
                    await transaction.RollbackAsync();
                    _logger.LogWarning("未找到用户汇总数据: UserId={UserId}, Week={Week}", userId, currentWeek);
                }
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, "修复用户数据失败: UserId={UserId}", userId);
            }
        }

        /// <summary>
        /// 清理过期事件
        /// </summary>
        private async Task CleanupOldEventsAsync(AppDbContext context)
        {
            try
            {
                var cutoffDate = DateTime.Now.AddDays(-30); // 保留30天的事件
                
                var oldEventsCount = await context.UserBehaviorEvents
                    .Where(e => e.CreatedAt < cutoffDate && 
                               e.ProcessingStatus == ProcessingStatus.Completed.ToStatusString())
                    .CountAsync();

                if (oldEventsCount > 1000) // 只有超过1000条才清理
                {
                    var oldEvents = await context.UserBehaviorEvents
                        .Where(e => e.CreatedAt < cutoffDate && 
                                   e.ProcessingStatus == ProcessingStatus.Completed.ToStatusString())
                        .OrderBy(e => e.CreatedAt)
                        .Take(500) // 每次清理500条
                        .ToListAsync();

                    context.UserBehaviorEvents.RemoveRange(oldEvents);
                    await context.SaveChangesAsync();
                    
                    _logger.LogInformation("清理了 {Count} 条过期事件", oldEvents.Count);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清理过期事件时发生错误");
            }
        }

        #region 私有辅助方法

        private async Task<List<int>> GetRandomActiveUsersAsync(AppDbContext context, int count)
        {
            return await context.Users
                .Where(u => u.IsActive)
                .OrderBy(u => Guid.NewGuid()) // 随机排序
                .Take(count)
                .Select(u => u.Id)
                .ToListAsync();
        }

        private DateTime GetCurrentWeekStart()
        {
            var today = DateTime.Today;
            var daysFromMonday = (int)today.DayOfWeek - (int)DayOfWeek.Monday;
            if (daysFromMonday < 0) daysFromMonday += 7;
            return today.AddDays(-daysFromMonday);
        }

        private async Task<UserStatsSnapshot> CalculateStatsFromEventsAsync(AppDbContext context, int userId, DateTime weekStart)
        {
            var weekEnd = weekStart.AddDays(7);
            
            var events = await context.UserBehaviorEvents
                .Where(e => e.UserId == userId && 
                           e.Timestamp >= weekStart && 
                           e.Timestamp < weekEnd &&
                           e.ProcessingStatus == ProcessingStatus.Completed.ToStatusString())
                .ToListAsync();

            return new UserStatsSnapshot
            {
                TasksClaimed = events.Count(e => e.ActionType == "TASK_CLAIMED"),
                TasksCompleted = events.Count(e => e.ActionType == "TASK_COMPLETED"),
                TasksCreated = events.Count(e => e.ActionType == "TASK_CREATED"),
                TasksCommented = events.Count(e => e.ActionType == "TASK_COMMENTED"),
                AssetsCreated = events.Count(e => e.ActionType == "ASSET_CREATED"),
                AssetsUpdated = events.Count(e => e.ActionType == "ASSET_UPDATED"),
                AssetsDeleted = events.Count(e => e.ActionType == "ASSET_DELETED"),
                FaultsReported = events.Count(e => e.ActionType == "FAULT_RECORDED"),
                FaultsRepaired = events.Count(e => e.ActionType == "FAULT_REPAIRED"),
                TotalPoints = events.Sum(e => e.PointsEarned),
                TotalCoins = events.Sum(e => e.CoinsEarned),
                TotalDiamonds = events.Sum(e => e.DiamondsEarned),
                TotalXp = events.Sum(e => e.XpEarned)
            };
        }

        private async Task<UserStatsSnapshot> GetStatsFromSummaryAsync(AppDbContext context, int userId, DateTime weekStart)
        {
            var summary = await context.UserWorkSummaries
                .FirstOrDefaultAsync(s => s.UserId == userId && 
                                   s.PeriodType == "weekly" && 
                                   s.PeriodDate == weekStart);

            if (summary == null)
                return new UserStatsSnapshot();

            return new UserStatsSnapshot
            {
                TasksClaimed = summary.TasksClaimed,
                TasksCompleted = summary.TasksCompleted,
                TasksCreated = summary.TasksCreated,
                TasksCommented = summary.TasksCommented,
                AssetsCreated = summary.AssetsCreated,
                AssetsUpdated = summary.AssetsUpdated,
                AssetsDeleted = summary.AssetsDeleted,
                FaultsReported = summary.FaultsReported,
                FaultsRepaired = summary.FaultsRepaired,
                TotalPoints = summary.TotalPointsEarned,
                TotalCoins = summary.TotalCoinsEarned,
                TotalDiamonds = summary.TotalDiamondsEarned,
                TotalXp = summary.TotalXpEarned
            };
        }

        private bool CompareStats(UserStatsSnapshot actual, UserStatsSnapshot summary)
        {
            return actual.TasksClaimed == summary.TasksClaimed &&
                   actual.TasksCompleted == summary.TasksCompleted &&
                   actual.TasksCreated == summary.TasksCreated &&
                   actual.TasksCommented == summary.TasksCommented &&
                   actual.AssetsCreated == summary.AssetsCreated &&
                   actual.AssetsUpdated == summary.AssetsUpdated &&
                   actual.AssetsDeleted == summary.AssetsDeleted &&
                   actual.FaultsReported == summary.FaultsReported &&
                   actual.FaultsRepaired == summary.FaultsRepaired &&
                   actual.TotalPoints == summary.TotalPoints &&
                   actual.TotalCoins == summary.TotalCoins &&
                   actual.TotalDiamonds == summary.TotalDiamonds &&
                   actual.TotalXp == summary.TotalXp;
        }

        #endregion
    }

    /// <summary>
    /// 用户统计快照
    /// </summary>
    public class UserStatsSnapshot
    {
        public int TasksClaimed { get; set; }
        public int TasksCompleted { get; set; }
        public int TasksCreated { get; set; }
        public int TasksCommented { get; set; }
        public int AssetsCreated { get; set; }
        public int AssetsUpdated { get; set; }
        public int AssetsDeleted { get; set; }
        public int FaultsReported { get; set; }
        public int FaultsRepaired { get; set; }
        public int TotalPoints { get; set; }
        public int TotalCoins { get; set; }
        public int TotalDiamonds { get; set; }
        public int TotalXp { get; set; }
    }
}
