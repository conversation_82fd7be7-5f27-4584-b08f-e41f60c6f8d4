// File: Application/Features/Notes/Services/IQuickMemoService.cs
// Description: Interface for QuickMemo business logic operations.
#nullable enable
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Threading; // For CancellationToken
using ItAssetsSystem.Application.Common.Dtos; // For ApiResponse
using ItAssetsSystem.Application.Features.Notes.Dtos; // For QuickMemo DTOs

namespace ItAssetsSystem.Application.Features.Notes.Services
{
    public interface IQuickMemoService
    {
        Task<ApiResponse<List<QuickMemoDto>>> GetMemosAsync(int userId, CancellationToken cancellationToken = default);
        Task<ApiResponse<QuickMemoDto>> GetMemoByIdAsync(string memoId, int userId, CancellationToken cancellationToken = default);
        Task<ApiResponse<QuickMemoDto>> CreateMemoAsync(CreateQuickMemoRequestDto request, int userId, CancellationToken cancellationToken = default);
        Task<ApiResponse<QuickMemoDto>> UpdateMemoAsync(string memoId, UpdateQuickMemoRequestDto request, int userId, CancellationToken cancellationToken = default);
        Task<ApiResponse<bool>> DeleteMemoAsync(string memoId, int userId, CancellationToken cancellationToken = default);

        Task<ApiResponse<List<QuickMemoCategoryDto>>> GetCategoriesAsync(int userId, CancellationToken cancellationToken = default);
        Task<ApiResponse<QuickMemoCategoryDto>> GetCategoryByIdAsync(string categoryId, int userId, CancellationToken cancellationToken = default); // Added for completeness
        Task<ApiResponse<QuickMemoCategoryDto>> CreateCategoryAsync(CreateQuickMemoCategoryRequestDto request, int userId, CancellationToken cancellationToken = default);
        Task<ApiResponse<QuickMemoCategoryDto>> UpdateCategoryAsync(string categoryId, CreateQuickMemoCategoryRequestDto request, int userId, CancellationToken cancellationToken = default); // Using Create DTO for update, can be specific UpdateDTO
        Task<ApiResponse<bool>> DeleteCategoryAsync(string categoryId, int userId, CancellationToken cancellationToken = default);
    }
} 