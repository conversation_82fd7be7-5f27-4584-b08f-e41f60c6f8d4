/**
 * 航空航天级IT资产管理系统 - 分页组件
 * 文件路径: src/components/Pagination.vue
 * 功能描述: 通用分页组件，提供分页控制和页面大小切换功能
 */

<template>
  <div class="pagination-container" v-if="total > 0">
    <el-pagination
      :background="background"
      :current-page.sync="currentPage"
      :page-size.sync="pageSize"
      :layout="layout"
      :page-sizes="pageSizes"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script>
export default {
  name: 'Pagination',
  props: {
    // 总记录数
    total: {
      required: true,
      type: Number
    },
    // 当前页码
    page: {
      type: Number,
      default: 1
    },
    // 每页记录数
    limit: {
      type: Number,
      default: 20
    },
    // 页码按钮的数量
    pageCount: {
      type: Number,
      default: 7
    },
    // 是否使用背景色
    background: {
      type: <PERSON><PERSON><PERSON>,
      default: true
    },
    // 分页布局
    layout: {
      type: String,
      default: 'total, sizes, prev, pager, next, jumper'
    },
    // 每页显示个数选择器的选项
    pageSizes: {
      type: Array,
      default() {
        return [10, 20, 30, 50, 100]
      }
    },
    // 自动滚动
    autoScroll: {
      type: Boolean,
      default: true
    }
  },
  computed: {
    currentPage: {
      get() {
        return this.page
      },
      set(val) {
        this.$emit('update:page', val)
      }
    },
    pageSize: {
      get() {
        return this.limit
      },
      set(val) {
        this.$emit('update:limit', val)
      }
    }
  },
  methods: {
    handleSizeChange(val) {
      this.$emit('pagination', { page: this.currentPage, limit: val })
    },
    handleCurrentChange(val) {
      this.$emit('pagination', { page: val, limit: this.pageSize })
      if (this.autoScroll) {
        this.scrollToTop()
      }
    },
    scrollToTop() {
      const mainEl = document.querySelector('.app-main')
      if (mainEl) {
        mainEl.scrollTop = 0
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.pagination-container {
  display: flex;
  justify-content: center;
  padding: 30px 5px;
  margin-top: 5px;
  background-color: var(--el-bg-color);
}
</style> 