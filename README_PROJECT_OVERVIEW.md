# 🚀 航空航天级IT资产管理系统

<div align="center">

![IT Asset Management System](https://img.shields.io/badge/Version-v2.0-blue.svg)
![.NET](https://img.shields.io/badge/.NET-6.0-purple.svg)
![Vue](https://img.shields.io/badge/Vue-3.3-green.svg)
![MySQL](https://img.shields.io/badge/MySQL-8.0-orange.svg)
![License](https://img.shields.io/badge/License-MIT-yellow.svg)

**现代化、智能化、游戏化的企业级资产管理平台**

[快速开始](#-快速开始) • [技术架构](#-技术架构) • [功能特性](#-功能特性) • [部署指南](#-部署指南) • [贡献指南](#-贡献指南)

</div>

## 📋 项目简介

这是一个专为**航空航天等高端制造业**设计的企业级IT资产管理系统，采用现代化技术栈，提供完整的资产生命周期管理、智能任务调度、游戏化协作和实时数据可视化功能。

### 🎯 核心价值
- **💰 降本增效**: 智能库存管理减少30%成本，预防性维护降低40%维修费用
- **⚡ 运营提升**: 游戏化设计提升员工积极性，自动化流程减少70%手工操作
- **📊 数据驱动**: 实时监控和预测分析，支持科学决策
- **🛡️ 风险管控**: 完整审计追踪，故障预防和应急响应

## 🌟 核心特性

### 🏭 完整业务闭环
```
资产采购 → 入库接收 → 位置分配 → 使用维护 → 故障处理 → 返厂维修 → 报废处置
```

### 🎮 创新功能亮点
- **🎯 游戏化任务管理**: 积分奖励、等级系统、排行榜、成就徽章
- **🌐 3D工厂可视化**: Three.js立体展示、实时热力图、设备状态监控
- **🤖 智能预测分析**: 故障预警、库存优化、维护提醒
- **📱 移动端支持**: 现场快速操作、离线同步、扫码管理
- **⚡ 实时协作**: SignalR推送、多人协作、@提醒通知

## 🏗️ 技术架构

### 整体架构图
```
┌─────────────────┬─────────────────┬─────────────────┐
│   前端层 (Vue3)   │   API网关层      │   可视化层       │
├─────────────────┼─────────────────┼─────────────────┤
│ • Element Plus  │ • .NET 6 WebAPI │ • ECharts图表   │
│ • Pinia状态管理  │ • JWT认证       │ • Three.js 3D   │
│ • Vue Router    │ • SignalR实时   │ • 热力图可视化   │
└─────────────────┴─────────────────┴─────────────────┘
           ↓                 ↓                 ↓
┌─────────────────┬─────────────────┬─────────────────┐
│  业务逻辑层      │   数据访问层     │   基础设施层     │
├─────────────────┼─────────────────┼─────────────────┤
│ • 领域服务      │ • EF Core ORM   │ • MySQL数据库   │
│ • CQRS模式      │ • Repository    │ • Redis缓存     │
│ • 事件驱动      │ • 查询优化      │ • 文件存储      │
└─────────────────┴─────────────────┴─────────────────┘
```

### 技术栈详情

#### 🎨 前端技术
- **框架**: Vue 3.3 + Composition API + TypeScript
- **UI组件**: Element Plus + Element Plus Icons
- **状态管理**: Pinia (替代Vuex)
- **路由**: Vue Router 4
- **构建工具**: Vite 5.0 (快速开发构建)
- **可视化**: ECharts 5.6 + Three.js (3D可视化)
- **工具库**: Axios + Day.js + DOMPurify

#### ⚙️ 后端技术
- **框架**: .NET 6 + ASP.NET Core WebAPI
- **架构**: Clean Architecture + CQRS + DDD
- **ORM**: Entity Framework Core 6
- **数据库**: MySQL 8.0 (主) + SQLite (开发)
- **实时通信**: SignalR Hub
- **认证**: JWT Bearer Token
- **日志**: Serilog + 结构化日志
- **缓存**: 内存缓存 + 分布式缓存

#### 💾 数据存储
- **关系数据库**: MySQL 8.0 (69张表)
- **文件存储**: 本地文件系统 + OSS (可扩展)
- **缓存**: Redis (生产环境)
- **搜索**: MySQL全文索引 + Elasticsearch (可扩展)

## 🚀 快速开始

### 环境要求
- **Node.js**: 16+ 
- **.NET SDK**: 6.0+
- **MySQL**: 8.0+ (或SQLite用于开发)
- **Git**: 2.0+

### 30秒启动指南

```bash
# 1. 克隆项目
git clone <repository-url>
cd singleit20250406

# 2. 启动后端 (终端1)
dotnet run  # 默认运行在 https://localhost:5001

# 3. 启动前端 (终端2)
cd frontend
npm install
npm run dev  # 默认运行在 http://localhost:5173

# 4. 访问系统
浏览器打开: http://localhost:5173
默认账号: admin / 123456
```

### 数据库配置

#### 开发环境 (SQLite)
```json
// appsettings.Development.json
{
  "ConnectionStrings": {
    "DefaultConnection": "Data Source=itassets.db"
  }
}
```

#### 生产环境 (MySQL)
```json
// appsettings.json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost;Database=itassets;Uid=root;Pwd=password;"
  }
}
```

## 📚 项目结构

```
singleit20250406/
├── 📁 frontend/                    # Vue3前端项目
│   ├── src/
│   │   ├── api/                    # API接口封装
│   │   ├── components/             # 可复用组件
│   │   ├── views/                  # 页面组件
│   │   ├── stores/                 # Pinia状态管理
│   │   ├── router/                 # 路由配置
│   │   └── utils/                  # 工具函数
│   └── package.json
│
├── 📁 Api/                         # V2 API端点 (Clean Architecture)
├── 📁 Application/                 # 应用层 (用例、DTOs、服务)
├── 📁 Domain/                      # 领域层 (实体、聚合根、领域服务)
├── 📁 Infrastructure/              # 基础设施层 (数据访问、外部服务)
├── 📁 Core/                        # 核心层 (抽象、接口、横切关注点)
│
├── 📁 Controllers/                 # V1 传统控制器 (向后兼容)
├── 📁 Models/                      # 传统模型层 (逐步迁移)
├── 📁 Services/                    # 传统服务层
│
├── 📁 Migrations/                  # EF Core数据库迁移
├── 📁 Configurations/              # 配置文件
├── 📁 Scripts/                     # 数据库脚本和工具
│
├── 📄 Program.cs                   # 应用程序入口
├── 📄 Startup.cs                   # 服务配置
├── 📄 appsettings.json             # 应用配置
└── 📄 ItAssetsSystem.csproj        # 项目文件
```

## 🔍 核心模块说明

### 1. 资产管理模块 📦
```csharp
// 核心实体关系
Asset → AssetType (分类)
Asset → Location (位置) 
Asset → Department (部门)
Asset → AssetHistory (历史)
```
- **功能**: 资产全生命周期管理、智能编码、状态跟踪
- **特色**: 支持树形分类、5级位置层次、完整历史追踪

### 2. 任务管理模块 ⚡
```csharp
// V2 BIGINT主键设计
Task (BIGINT) → TaskAssignee (多人分配)
Task → Comment + Attachment (协作)
Task → PeriodicTaskSchedule (周期任务)
```
- **功能**: 日常任务、周期任务、PDCA改进、游戏化激励
- **特色**: Cron调度、多人协作、积分奖励、实时通知

### 3. 备件管理模块 🔧
```csharp
// 完整供应链管理
SparePart → SparePartType + Location
SparePart → Transaction (出入库)
SparePart → Supplier (供应商)
```
- **功能**: 库存管理、智能预警、供应商管理、成本控制
- **特色**: 安全库存、自动补货、成本分析

### 4. 3D可视化模块 🌐
- **技术**: Three.js + ECharts + Canvas
- **功能**: 3D工厂布局、实时热力图、设备状态监控
- **特色**: 可交互3D场景、数据实时更新、移动端适配

## 🔧 开发指南

### 代码规范
- **C#**: 遵循微软官方编码规范
- **JavaScript**: ESLint + Prettier
- **Git**: Conventional Commits规范
- **数据库**: snake_case命名约定

### 分支策略
```
main            # 生产环境分支
├── develop     # 开发主分支
├── feature/*   # 功能开发分支
├── hotfix/*    # 紧急修复分支
└── release/*   # 发布准备分支
```

### API设计规范
```csharp
// V2 API标准响应格式
{
  "success": true,
  "data": {...},
  "message": "操作成功",
  "pagination": {...}  // 分页信息
}
```

## 🚀 部署指南

### Docker部署
```bash
# 构建镜像
docker build -t itassets:latest .

# 运行容器
docker-compose up -d
```

### 生产环境清单
- [ ] MySQL数据库配置
- [ ] Redis缓存配置  
- [ ] 文件存储配置
- [ ] SSL证书配置
- [ ] 反向代理配置 (Nginx)
- [ ] 日志收集配置
- [ ] 监控告警配置

## 📖 学习资源

### 新人必读文档
1. **[新人入门指南](docs/GETTING_STARTED.md)** - 5分钟上手指南
2. **[技术架构详解](docs/ARCHITECTURE.md)** - 系统设计原理
3. **[API文档](docs/API.md)** - 接口说明和示例
4. **[数据库设计](docs/DATABASE.md)** - 表结构和关系图
5. **[开发规范](docs/DEVELOPMENT.md)** - 编码规范和最佳实践

### 视频教程 📺
- [ ] 系统概览和演示 (10分钟)
- [ ] 开发环境搭建 (15分钟)  
- [ ] 核心功能开发 (30分钟)
- [ ] 前端组件开发 (20分钟)
- [ ] 后端API开发 (25分钟)

## 🤝 贡献指南

### 如何参与
1. **🍴 Fork** 本项目
2. **🌿 创建**功能分支: `git checkout -b feature/AmazingFeature`
3. **📝 提交**代码: `git commit -m 'Add some AmazingFeature'`
4. **📤 推送**分支: `git push origin feature/AmazingFeature`
5. **🔀 提交** Pull Request

### 贡献类型
- 🐛 Bug修复
- ✨ 新功能开发
- 📚 文档完善
- 🎨 UI/UX改进
- ⚡ 性能优化
- 🧪 测试用例

## 📊 项目统计

```
📈 项目规模
├── 前端: 150+ Vue组件, 30+ 页面
├── 后端: 200+ API端点, 69张数据表
├── 代码量: 100K+ 行代码
└── 测试: 500+ 单元测试

🏆 技术亮点
├── Clean Architecture: 清晰分层
├── CQRS模式: 读写分离优化
├── 事件驱动: 解耦模块关系
└── 游戏化设计: 提升用户体验
```

## 📞 支持与反馈

### 联系方式
- **📧 邮箱**: <EMAIL>
- **💬 微信群**: 扫码加入开发者群
- **🐞 Issue**: [GitHub Issues](https://github.com/your-repo/issues)
- **📖 Wiki**: [项目Wiki](https://github.com/your-repo/wiki)

### 常见问题
1. **Q: 如何重置数据库？**
   A: 删除Migrations文件夹后运行 `dotnet ef database update`

2. **Q: 前端启动失败怎么办？**
   A: 检查Node.js版本，清除node_modules后重新安装

3. **Q: API调用失败？**
   A: 检查后端是否正常启动，确认API地址配置正确

---

<div align="center">

**⭐ 如果这个项目对你有帮助，请给个Star支持一下！ ⭐**

Made with ❤️ by IT Assets Team

</div>