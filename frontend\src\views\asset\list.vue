<!-- 资产列表页面 -->
<template>
  <div class="asset-list">
    <!-- 页面头部和搜索表单区域，使用sticky定位 -->
    <div class="sticky-header">
      <div class="page-header">
        <h2>资产列表</h2>
        <div class="header-actions">
          <el-button type="primary" @click="handleAdd">新增资产</el-button>
          <el-button type="danger" @click="handleBatchDelete" :disabled="selectedAssets.length === 0">批量删除</el-button>
          <el-button @click="handleImport">导入</el-button>
          <el-button @click="handleExport">导出</el-button>
        </div>
      </div>

      <!-- 搜索表单 -->
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="资产编号">
          <el-input v-model="searchForm.assetCode" placeholder="请输入资产编号" />
        </el-form-item>
        <el-form-item label="资产名称">
          <el-input v-model="searchForm.name" placeholder="请输入资产名称" />
        </el-form-item>
        <el-form-item label="资产类型">
          <el-input v-model="searchForm.assetTypeName" placeholder="资产类型" disabled />
        </el-form-item>
        <el-form-item label="使用状态">
          <el-input v-model="searchForm.statusName" placeholder="使用状态" disabled />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 主内容区，添加上边距 -->
    <div class="content-area">
      <!-- 资产列表表格 -->
      <el-table
        ref="multipleTableRef"
        v-loading="loading"
        element-loading-text="加载资产数据中..."
        :data="assetList"
        style="width: 100%; margin-top: 15px; border-radius: 5px"
        row-key="id"
        :highlight-current-row="true"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <!-- 新增序号列 -->
        <el-table-column
          type="index"
          label="序号"
          width="60"
          align="center"
          :index="calculateSerialNumber"
        />
        <el-table-column
          prop="assetCode"
          label="资产编号"
          min-width="120"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <el-tooltip
              :content="row.assetCode"
              placement="top"
              :show-after="500"
            >
              <span>{{ row.assetCode }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="financialCode" label="财务编号" width="120">
          <template #default="{ row }">
            {{ row.financialCode || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="name" label="资产名称" width="150">
          <template #default="{ row }">
            <el-tooltip
              :content="row.name"
              placement="top"
              :show-after="500"
            >
              <span>{{ row.name }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="资产类型" width="120">
          <template #default="{ row }">
            {{ row.assetTypeName || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="model" label="规格型号" width="120">
          <template #default="{ row }">
            {{ row.model || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="brand" label="品牌" width="100">
          <template #default="{ row }">
            {{ row.brand || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="locationName" label="位置" width="150" show-overflow-tooltip>
          <template #default="{ row }">
            {{ row.locationName || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="状态" width="80">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button link @click="handleEdit(row)">编辑</el-button>
            <el-button link @click="handleView(row)">查看</el-button>
            <el-button link @click="handleHistory(row)">历史</el-button>
            <el-button link class="delete-btn" @click="handleDelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          :key="`pagination-${pageSize}-${total}`"
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100, 200]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />

        <!-- 调试信息 -->
        <div v-if="true" style="margin-top: 10px; padding: 10px; background: #f5f5f5; border-radius: 4px; font-size: 12px;">
          <strong>🔍 分页调试信息:</strong>
          当前页: {{ currentPage }} |
          每页大小: {{ pageSize }} |
          总记录数: {{ total }} |
          总页数: {{ Math.ceil(total / pageSize) }}
          <br>
          <el-button size="small" @click="forceSetPageSize(200)" style="margin-top: 5px;">
            🔧 强制设置每页200条
          </el-button>
        </div>
      </div>
    </div>

    <!-- 资产详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="资产详情"
      width="650px"
      destroy-on-close
    >
      <el-descriptions :column="2" border>
        <el-descriptions-item label="资产编号">{{ currentAsset.assetCode }}</el-descriptions-item>
        <el-descriptions-item label="财务编号">{{ currentAsset.financialCode || '-' }}</el-descriptions-item>
        <el-descriptions-item label="资产名称">{{ currentAsset.name }}</el-descriptions-item>
        <el-descriptions-item label="资产类型">{{ currentAsset.assetTypeName }}</el-descriptions-item>
        <el-descriptions-item label="规格型号">{{ currentAsset.model }}</el-descriptions-item>
        <el-descriptions-item label="品牌">{{ currentAsset.brand }}</el-descriptions-item>
        <el-descriptions-item label="位置">{{ currentAsset.locationName }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="getStatusType(currentAsset.status)">
            {{ getStatusText(currentAsset.status) }}
          </el-tag>
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>

    <!-- 资产编辑对话框 -->
    <el-dialog
      v-model="editDialogVisible"
      :title="isAdd ? '新增资产' : '编辑资产'"
      width="500px"
      destroy-on-close
    >
      <el-form
        ref="assetFormRef"
        :model="assetForm"
        :rules="assetFormRules"
        label-width="100px"
        style="max-height: 500px; overflow-y: auto;"
      >
        <el-form-item label="资产编号" prop="assetCode">
          <el-input v-model="assetForm.assetCode" placeholder="请输入资产编号" />
        </el-form-item>
        <el-form-item label="财务编号" prop="financialCode">
          <el-input v-model="assetForm.financialCode" placeholder="请输入财务编号" />
        </el-form-item>
        <el-form-item label="资产名称" prop="name">
          <el-input v-model="assetForm.name" placeholder="请输入资产名称" />
        </el-form-item>
        <el-form-item label="资产类型" prop="assetTypeId">
          <el-select v-model="assetForm.assetTypeId" placeholder="请选择资产类型" style="width: 100%">
            <el-option 
              v-for="type in assetTypes" 
              :key="type.id" 
              :label="type.name" 
              :value="type.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="规格型号" prop="model">
          <el-input v-model="assetForm.model" placeholder="请输入规格型号" />
        </el-form-item>
        <el-form-item label="品牌" prop="brand">
          <el-input v-model="assetForm.brand" placeholder="请输入品牌" />
        </el-form-item>
        <el-form-item label="位置" prop="locationId">
          <el-select v-model="assetForm.locationId" filterable placeholder="请选择位置" style="width: 100%">
            <el-option 
              v-for="location in locations" 
              :key="location.id" 
              :label="location.fullName || location.name" 
              :value="location.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="assetForm.status" placeholder="请选择状态">
            <el-option label="闲置" :value="0" />
            <el-option label="在用" :value="1" />
            <el-option label="维修" :value="2" />
            <el-option label="报废" :value="3" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="editDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitAssetForm">确定</el-button>
      </template>
    </el-dialog>

    <!-- 资产历史对话框 -->
    <el-dialog
      v-model="historyDialogVisible"
      title="资产历史记录"
      width="800px"
      destroy-on-close
      class="history-dialog"
    >
      <h3 v-if="currentAsset.id" class="asset-title">
        <el-icon><Suitcase /></el-icon>
        资产: {{ currentAsset.name }} 
        <el-tag type="info" size="small">{{ currentAsset.assetCode }}</el-tag>
      </h3>
      
      <div v-loading="historyLoading" class="history-table">
        <div v-if="assetHistory.length === 0 && !historyLoading" class="empty-history">
          <el-empty description="暂无历史记录" />
        </div>
        
                      <el-table 
          v-else
          ref="historyTable"
          :data="assetHistory" 
          style="width: 100%"
                        border
          stripe
          size="default"
        >
          <el-table-column 
            prop="operationTime" 
            label="变更时间" 
            width="170"
            sortable
          >
            <template #default="scope">
              {{ formatDate(scope.row.changeTime || scope.row.operationTime) }}
                          </template>
                        </el-table-column>
          
          <el-table-column 
            prop="operationTypeName" 
            label="操作类型" 
            width="100"
          >
            <template #default="scope">
              <el-tag :type="getUpdateTypeTag(scope.row.operationType)">
                {{ scope.row.operationTypeName || getOperationTypeText(scope.row.operationType) }}
                            </el-tag>
                          </template>
                        </el-table-column>
          
          <el-table-column 
            label="变更内容" 
            min-width="450"
          >
            <template #default="scope">
              <div v-if="scope.row.parsedChanges" class="changes-list">
                <div v-for="(value, field) in filteredChanges(scope.row.parsedChanges)" :key="field" class="change-item">
                  <div class="field-name">{{ formatFieldName(field) }}</div>
                  <div class="change-values">
                    <span class="change-value-old">{{ getOldValue(value) }}</span>
                    <el-icon class="change-arrow"><ArrowRight /></el-icon>
                    <span class="change-value-new">{{ getNewValue(value) }}</span>
                    </div>
                </div>
              </div>
              <div v-else class="unknown-change">
                <el-button size="small" type="primary" @click="showJsonDetails(scope.row)">解析变更</el-button>
                </div>
            </template>
          </el-table-column>
          
          <el-table-column 
            prop="operatorName" 
            label="操作人" 
            width="150"
          />
        </el-table>
      </div>
      
      <div class="pagination-container" v-if="assetHistory.length > 0">
        <el-pagination
          v-model:current-page="historyCurrentPage"
          v-model:page-size="historyPageSize"
          :total="historyTotal"
          :page-sizes="[10, 20, 50]"
          layout="total, sizes, prev, pager, next"
          @size-change="handleHistorySizeChange"
          @current-change="handleHistoryCurrentChange"
        />
      </div>
    </el-dialog>

    <!-- 资产导入对话框 -->
    <el-dialog
      v-model="importDialogVisible"
      title="资产数据导入"
      width="500px"
      destroy-on-close
    >
      <div class="import-dialog-content">
        <div class="import-steps">
          <p class="step"><el-tag size="small">步骤 1</el-tag> 下载导入模板</p>
          <p class="step"><el-tag size="small">步骤 2</el-tag> 按照模板格式填写数据</p>
          <p class="step"><el-tag size="small">步骤 3</el-tag> 上传填写好的文件</p>
        </div>
        
        <div class="template-download">
          <el-button type="primary" plain @click="downloadTemplate">
            <el-icon><Download /></el-icon> 下载导入模板
          </el-button>
          <p class="template-tip">请确保按照模板格式填写数据，否则可能导致导入失败</p>
        </div>
        
        <el-divider />
        
        <el-upload
          ref="uploadRef"
          class="import-upload"
          action=""
          :multiple="false"
          :auto-upload="false"
          :http-request="customUploadRequest"
          :on-change="handleFileChange"
          :before-upload="beforeImportUpload"
          :on-success="handleImportSuccess"
          :on-error="handleImportError"
          :on-progress="handleImportProgress"
          accept=".csv,.xlsx,.xls"
          :limit="1"
        >
          <template #trigger>
            <el-button type="primary">
              <el-icon><Upload /></el-icon> 选择文件
            </el-button>
          </template>
          
          <template #tip>
            <div class="el-upload__tip">
              仅支持 xlsx, xls 或 csv 格式文件，文件大小不超过10MB
            </div>
          </template>
          
          <el-button 
            class="ml-3" 
            type="success" 
            @click="submitImport"
            :loading="importing"
            :disabled="uploadFile === null"
          >
            开始导入
          </el-button>
        </el-upload>
        
        <div v-if="importing" class="import-progress">
          <el-progress 
            :percentage="importProgress" 
            status="success"
          />
          <p class="progress-text">正在导入，请勿关闭窗口...</p>
        </div>
        
        <div v-if="importResult" class="import-result">
          <el-alert
            :title="importResult.success ? '导入成功' : '导入失败'"
            :type="importResult.success ? 'success' : 'error'"
            show-icon
          >
            <div class="result-info">
              <p>
                总计 {{ importResult.data?.totalRows || 0 }} 条记录，
                成功导入 {{ importResult.data?.successCount || 0 }} 条，
                失败 {{ importResult.data?.errorCount || 0 }} 条
              </p>
              
              <!-- 错误信息列表 -->
              <div v-if="importResult.data?.errorMessages && importResult.data.errorMessages.length > 0" class="error-messages">
                <p class="message-title">错误信息:</p>
                <el-scrollbar height="150px">
                  <ul class="message-list">
                    <li v-for="(error, index) in importResult.data.errorMessages" :key="'error-'+index">
                      <el-tag type="danger" size="small">错误</el-tag> {{ error }}
                  </li>
                </ul>
                </el-scrollbar>
              </div>
              
              <!-- 警告信息列表 -->
              <div v-if="importResult.data?.warningMessages && importResult.data.warningMessages.length > 0" class="warning-messages">
                <p class="message-title">警告信息:</p>
                <el-scrollbar height="150px">
                  <ul class="message-list">
                    <li v-for="(warning, index) in importResult.data.warningMessages" :key="'warning-'+index">
                      <el-tag type="warning" size="small">警告</el-tag> {{ warning }}
                    </li>
                  </ul>
                </el-scrollbar>
              </div>
            </div>
          </el-alert>
        </div>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="importDialogVisible = false">关闭</el-button>
          <el-button 
            v-if="importResult && importResult.success" 
            type="primary" 
            @click="finishImport"
          >
            完成并刷新
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Location, 
  Right, 
  Edit, 
  InfoFilled, 
  Suitcase,
  ArrowRight,
  Download,
  Upload
} from '@element-plus/icons-vue'
import * as assetApi from '@/api/asset'
import * as assetTypeApi from '@/api/assetType'
import * as locationApi from '@/api/location'
import systemConfig from '@/config/system'
import { formatDate, formatPrice } from '@/utils/format'
import { getToken } from '@/utils/auth'

// 数据定义
const loading = ref(false)
const assetList = ref([])
const assetTypes = ref([])
const locations = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(20)
const searchForm = reactive({
  assetCode: '',
  name: '',
  assetTypeName: '',
  statusName: ''
})

// 详情对话框
const detailDialogVisible = ref(false)
const currentAsset = ref({})

// 编辑对话框
const editDialogVisible = ref(false)
const isAdd = ref(false)
const assetFormRef = ref(null)
const assetForm = reactive({
  id: '',
  assetCode: '',
  financialCode: '',
  name: '',
  assetTypeId: undefined,
  model: '',
  brand: '',
  locationId: undefined,
  status: 1,
  notes: ''
})
const assetFormRules = {
  assetCode: [{ required: true, message: '请输入资产编号', trigger: 'blur' }],
  name: [{ required: true, message: '请输入资产名称', trigger: 'blur' }],
  assetTypeId: [{ required: true, message: '请选择资产类型', trigger: 'change' }],
  model: [{ required: true, message: '请输入规格型号', trigger: 'blur' }],
  brand: [{ required: true, message: '请输入品牌', trigger: 'blur' }]
}

// 历史对话框
const historyDialogVisible = ref(false)
const historyLoading = ref(false)
const assetHistory = ref([])
const historyCurrentPage = ref(1)
const historyPageSize = ref(10)
const historyTotal = ref(0)

// 资产导入相关
const importDialogVisible = ref(false)
const uploadRef = ref(null)
const uploadFile = ref(null)
const importing = ref(false)
const importProgress = ref(0)
const importResult = ref(null)
const uploadHeaders = {
  Authorization: `Bearer ${getToken()}`
}

// 已选资产数组
const selectedAssets = ref([])

// 获取资产类型列表
const fetchAssetTypeList = async () => {
  try {
    console.log('获取资产类型列表...')
    const response = await assetTypeApi.getAssetTypes()
    console.log('资产类型API响应:', response)
    
    if (response && response.success) {
      // 确保response.data是数组
      if (Array.isArray(response.data)) {
        assetTypes.value = response.data
        console.log(`成功获取${assetTypes.value.length}个资产类型`)
      } else if (response.data && typeof response.data === 'object') {
        // 尝试从对象中获取正确的数组数据
        console.warn('API返回的资产类型不是数组格式，尝试从对象中提取数组')
        let foundArray = false
        
        // 尝试从对象的属性中找到数组
        for (const key in response.data) {
          if (Array.isArray(response.data[key])) {
            assetTypes.value = response.data[key]
            console.log(`从response.data.${key}中找到资产类型数组，长度:${assetTypes.value.length}`)
            foundArray = true
            break
          }
        }
        
        if (!foundArray) {
          // 如果没有找到数组，则初始化为空数组
          console.error('无法从响应中提取资产类型数组')
          assetTypes.value = []
          ElMessage.warning('获取资产类型数据格式错误')
        }
      } else {
        console.error('API返回的资产类型数据无效:', response.data)
        assetTypes.value = []
        ElMessage.warning('获取资产类型失败：数据格式错误')
      }
    } else {
      console.error('获取资产类型列表失败:', response?.message || '未知错误')
      assetTypes.value = []
      ElMessage.warning('获取资产类型列表失败')
    }
  } catch (error) {
    console.error('获取资产类型列表异常:', error)
    assetTypes.value = []
    ElMessage.warning('获取资产类型列表失败：' + (error.message || '未知错误'))
  }
  
  // 调试输出最终的资产类型列表
  console.log('最终资产类型列表:', {
    是数组: Array.isArray(assetTypes.value),
    长度: assetTypes.value.length,
    内容: assetTypes.value
  })
  
  return assetTypes.value
}

// 获取位置列表
const fetchLocationList = async () => {
  try {
    // 修正API调用，使用正确的函数名称
    console.log('获取位置列表...')
    const response = await locationApi.default.getLocationsForDropdown({ includeAll: true })
    console.log('位置列表API响应:', response)
    
    if (response.success) {
      locations.value = response.data || []
      console.log(`成功获取${locations.value.length}个位置`)
    } else {
      console.error('获取位置列表失败:', response.message)
    }
  } catch (error) {
    console.error('获取位置列表失败:', error)
  }
}

// 获取资产列表
const fetchAssetList = async () => {
  loading.value = true
  assetList.value = []
  total.value = 0
  try {
    console.log('开始获取资产列表，查询参数:', {
      pageIndex: currentPage.value,
      pageSize: pageSize.value,
      ...searchForm
    })
    const res = await assetApi.getAssets({
      pageIndex: currentPage.value,
      pageSize: pageSize.value,
      ...searchForm
    })
    console.log('获取资产列表响应结果:', res)
    
    // 检查响应是否成功
    if (res && res.success) {
      console.log('API请求成功，处理返回数据')
      
      // 处理不同的数据结构
      if (res.data) {
        // 处理 {data: {items: [], totalCount: number}} 格式
        if (res.data.items && Array.isArray(res.data.items)) {
          console.log(`获取到${res.data.items.length}条资产数据，总数:${res.data.totalCount || res.data.total}`)
          assetList.value = res.data.items
          // 注意这里：确保总数使用totalCount字段
          total.value = res.data.totalCount || res.data.total || res.data.items.length
          console.log(`设置总数为: ${total.value}`)
        }
        // 处理 {data: []} 格式
        else if (Array.isArray(res.data)) {
          console.log(`获取到${res.data.length}条资产数据，使用数组长度作为总数`)
          assetList.value = res.data
          total.value = res.data.length
        }
        // 处理直接返回items和total的格式
        else if (res.items && Array.isArray(res.items)) {
          console.log(`获取到${res.items.length}条资产数据，总数:${res.total}`)
          assetList.value = res.items
          total.value = res.total || res.items.length
        }
        // 处理直接返回列表数据的格式
        else {
          console.log('API返回数据结构不匹配预期格式，尝试遍历所有可能的数据结构')
          // 尝试遍历res对象寻找数组数据
          Object.keys(res).forEach(key => {
            if (Array.isArray(res[key]) && res[key].length > 0) {
              console.log(`在${key}字段中找到数组数据，长度:${res[key].length}`)
              assetList.value = res[key]
              total.value = res[key].length
            }
            if (typeof res[key] === 'object' && res[key] !== null) {
              Object.keys(res[key]).forEach(subKey => {
                if (Array.isArray(res[key][subKey]) && res[key][subKey].length > 0) {
                  console.log(`在${key}.${subKey}字段中找到数组数据，长度:${res[key][subKey].length}`)
                  assetList.value = res[key][subKey]
                  if (res[key].totalCount || res[key].total) {
                    total.value = res[key].totalCount || res[key].total
                    console.log(`从${key}对象中找到总数: ${total.value}`)
                  } else {
                    total.value = res[key][subKey].length
                  }
                }
              })
            }
          })
        }
      }
      
      // 特殊处理：如果从res根节点获取总数
      if (res.total !== undefined) {
        console.log(`从响应根对象获取总数: ${res.total}`)
        total.value = res.total
      } else if (res.totalCount !== undefined) {
        console.log(`从响应根对象获取总数: ${res.totalCount}`)
        total.value = res.totalCount
      }
      
      // 从用户提供的截图中看，JSON中有一个顶级'total'字段值为242
      if (total.value === 0 && res.data && Array.isArray(res.data.items)) {
        // 从响应中提取"total"字段作为后备选项
        for (const key in res) {
          if (key === 'total' && typeof res[key] === 'number') {
            console.log(`找到顶级total字段: ${res[key]}`);
            total.value = res[key];
            break;
          }
        }
      }
      
      // 最后的数据检查
      if (assetList.value.length > 0) {
        console.log('成功获取资产数据，第一条:', assetList.value[0])
        console.log('总数据量:', total.value)
        console.log(`🎯 分页调试信息:`)
        console.log(`  当前页码: ${currentPage.value}`)
        console.log(`  每页大小: ${pageSize.value}`)
        console.log(`  实际返回: ${assetList.value.length}条`)
        console.log(`  总记录数: ${total.value}`)
        console.log(`  期望数据量: ${Math.min(pageSize.value, Math.max(0, total.value - (currentPage.value - 1) * pageSize.value))}条`)

        // 验证数据是否正确
        const expectedCount = Math.min(pageSize.value, Math.max(0, total.value - (currentPage.value - 1) * pageSize.value))
        const totalPages = Math.ceil(total.value / pageSize.value)

        console.log(`🔍 分页状态验证:`)
        console.log(`  总记录数: ${total.value}`)
        console.log(`  每页大小: ${pageSize.value}`)
        console.log(`  计算总页数: ${totalPages}`)
        console.log(`  当前页码: ${currentPage.value}`)
        console.log(`  页码是否有效: ${currentPage.value <= totalPages}`)

        if (assetList.value.length !== expectedCount) {
          console.error(`⚠️ 数据量异常: 期望${expectedCount}条，实际${assetList.value.length}条`)
        } else {
          console.log(`✅ 数据量正确`)
        }

        // 检查分页组件状态
        nextTick(() => {
          console.log(`📄 分页组件状态检查:`)
          console.log(`  currentPage.value: ${currentPage.value}`)
          console.log(`  pageSize.value: ${pageSize.value}`)
          console.log(`  total.value: ${total.value}`)

          // 检查分页按钮是否可点击
          const paginationButtons = document.querySelectorAll('.el-pager .number')
          console.log(`  分页按钮数量: ${paginationButtons.length}`)
          paginationButtons.forEach((btn, index) => {
            console.log(`  按钮${index + 1}: 文本="${btn.textContent}", 类名="${btn.className}"`)
          })
        })
      } else {
        console.warn('没有获取到任何资产数据')
      }
    } else {
      console.error('API请求失败:', res)
      ElMessage.error(res?.message || '获取资产列表失败')
    }
  } catch (err) {
    console.error('获取资产列表错误:', err)
    ElMessage.error('获取资产列表失败: ' + (err.message || '未知错误'))
  } finally {
    loading.value = false

    // 确保表格滚动到顶部
    nextTick(() => {
      const tableContainer = document.querySelector('.el-table__body-wrapper')
      if (tableContainer) {
        tableContainer.scrollTop = 0
      }

      // 滚动页面到表格顶部
      const tableElement = document.querySelector('.el-table')
      if (tableElement) {
        tableElement.scrollIntoView({ behavior: 'smooth', block: 'start' })
      }
    })
  }
}

/**
 * 从API获取资产详情
 * @param {number} id 资产ID
 * @returns {Promise} 返回资产详情数据
 */
const fetchAssetDetail = async (id) => {
  console.log(`开始获取资产详情，ID: ${id}`);
  
  if (!id) {
    console.error('获取资产详情失败: ID为空');
    ElMessage.error('获取资产详情失败: 资产ID无效');
    return null;
  }
  
  try {
    const result = await assetApi.getAssetById(id);
    console.log('获取资产详情API响应:', result);
    
    if (result && result.success) {
      console.log('获取资产详情成功:', result.data);
      return result.data;
    } else {
      console.error('获取资产详情失败:', result?.message || '未知错误');
      ElMessage.error(result?.message || '获取资产详情失败');
      return null;
    }
  } catch (error) {
    console.error('获取资产详情异常:', error);
    ElMessage.error('获取资产详情失败: ' + (error.message || '网络错误'));
    return null;
  }
}

// 获取资产历史记录
const fetchAssetHistory = async (id) => {
  historyLoading.value = true
  try {
    const params = {
      page: historyCurrentPage.value,
      pageSize: historyPageSize.value
    }
    
    const res = await assetApi.getAssetHistory(id, params)
    console.log('资产历史记录API响应', res)
    
    if (res.success === true && res.data) {
      // 适配后端返回的数据结构
      if (res.data.history) {
        // 后端返回的是 {asset: {...}, history: [...]} 结构
        assetHistory.value = res.data.history.map(item => preprocessHistoryItem(item)) || []
        historyTotal.value = res.data.history?.length || 0
        
        // 更新当前资产信息
        if (res.data.asset) {
          currentAsset.value = {
            ...currentAsset.value,
            ...res.data.asset
          }
        }
      } else if (res.data.items) {
        // 兼容老格式{items: [...], total: number}
        assetHistory.value = res.data.items.map(item => preprocessHistoryItem(item)) || []
      historyTotal.value = res.data.total || 0
      } else {
        // 如果是直接返回数组
        assetHistory.value = Array.isArray(res.data) ? res.data.map(item => preprocessHistoryItem(item)) : []
        historyTotal.value = Array.isArray(res.data) ? res.data.length : 0
      }
    } else {
      ElMessage.error(res.message || '获取资产历史记录失败')
      assetHistory.value = []
      historyTotal.value = 0
    }
  } catch (error) {
    console.error('获取资产历史记录失败:', error)
    ElMessage.error('获取资产历史记录失败: ' + (error.message || '未知错误'))
    assetHistory.value = []
    historyTotal.value = 0
  } finally {
    historyLoading.value = false
  }
}

// 状态标签函数
const getStatusType = (status) => {
  const types = {
    0: 'info',    // 闲置
    1: 'success', // 在用
    2: 'warning', // 维修
    3: 'danger'   // 报废
  }
  return types[status] || 'info'
}

// 状态文本转换函数
const getStatusText = (status) => {
  const statusMap = {
    0: '闲置',
    1: '在用',
    2: '维修',
    3: '报废'
  }
  return statusMap[status] || '未知'
}

// 操作类型文本转换
const getOperationTypeText = (type) => {
  const typeMap = {
    1: '创建',
    2: '修改',
    3: '删除',
    4: '位置变更',
    5: '状态变更'
  }
  console.log('转换操作类型:', type, '为文本:', typeMap[type] || '未知操作')
  return typeMap[type] || '未知操作'
}

// 事件处理
const handleSearch = () => {
  currentPage.value = 1
  console.log('执行搜索，参数:', {
    资产编号: searchForm.assetCode,
    资产名称: searchForm.name,
    资产类型: searchForm.assetTypeName,
    使用状态: searchForm.statusName
  })
  fetchAssetList()
}

const handleReset = () => {
  searchForm.assetCode = ''
  searchForm.name = ''
  searchForm.assetTypeName = ''
  searchForm.statusName = ''
  currentPage.value = 1
  console.log('重置搜索表单')
  fetchAssetList()
}

// 分页大小变更处理
const handleSizeChange = async (val) => {
  console.log(`📏 页面大小变更事件触发:`)
  console.log(`  从每页: ${pageSize.value}条`)
  console.log(`  到每页: ${val}条`)
  console.log(`  当前页码: ${currentPage.value}`)
  console.log(`  总记录数: ${total.value}`)

  // 强制更新分页大小
  pageSize.value = Number(val)
  currentPage.value = 1  // 重置到第一页

  const newTotalPages = Math.ceil(total.value / val)
  console.log(`  新的总页数: ${newTotalPages}`)
  console.log(`✅ 重置到第1页，开始获取数据`)

  // 确保数据更新后再获取列表
  await nextTick()
  await fetchAssetList()

  // 验证更新是否成功
  console.log(`🔍 更新后验证: pageSize.value = ${pageSize.value}`)
}

// 强制设置分页大小的辅助方法
const forceSetPageSize = async (size) => {
  console.log(`🔧 强制设置分页大小为: ${size}`)
  pageSize.value = Number(size)
  currentPage.value = 1
  await nextTick()
  await fetchAssetList()
  console.log(`✅ 强制设置完成: pageSize.value = ${pageSize.value}`)
}

// 暴露给全局调试使用
window.forceSetPageSize = forceSetPageSize

// 页码变更处理
const handleCurrentChange = async (val) => {
  console.log(`🔄 页码变更事件触发:`)
  console.log(`  从页码: ${currentPage.value}`)
  console.log(`  到页码: ${val}`)
  console.log(`  每页大小: ${pageSize.value}`)
  console.log(`  总记录数: ${total.value}`)
  console.log(`  总页数: ${Math.ceil(total.value / pageSize.value)}`)

  // 验证页码是否有效
  const totalPages = Math.ceil(total.value / pageSize.value)
  if (val > totalPages) {
    console.error(`⚠️ 无效页码: 请求页码${val}超出总页数${totalPages}`)
    ElMessage.warning(`页码${val}超出范围，总共只有${totalPages}页`)
    return
  }

  if (val < 1) {
    console.error(`⚠️ 无效页码: 页码不能小于1`)
    return
  }

  console.log(`✅ 页码有效，开始切换到第${val}页`)
  currentPage.value = val
  await fetchAssetList()
}

// 计算序号
const calculateSerialNumber = (index) => {
  return (currentPage.value - 1) * pageSize.value + index + 1
}

const handleHistorySizeChange = (val) => {
  historyPageSize.value = val
  fetchAssetHistory(currentAsset.value.id)
}

const handleHistoryCurrentChange = (val) => {
  historyCurrentPage.value = val
  fetchAssetHistory(currentAsset.value.id)
}

const handleAdd = () => {
  isAdd.value = true
  // 重置表单
  Object.keys(assetForm).forEach(key => {
    assetForm[key] = ''
  })
  assetForm.status = 1
  editDialogVisible.value = true
}

const handleEdit = async (row) => {
  isAdd.value = false
  console.log('编辑资产，行数据:', row)
  
  // 先重置表单，确保没有旧数据
  Object.keys(assetForm).forEach(key => {
    assetForm[key] = key === 'status' ? 1 : ''
  })
  
  try {
    // 首先加载资产类型和位置列表，确保下拉框数据可用
    console.log('加载资产类型和位置列表...')
    if (assetTypes.value.length === 0) {
      console.log('资产类型列表为空，开始加载...')
      try {
        // 使用已修改的fetchAssetTypeList函数
        await fetchAssetTypeList()
        console.log(`加载了 ${assetTypes.value.length} 个资产类型，是否为数组: ${Array.isArray(assetTypes.value)}`)
      } catch (typeError) {
        console.error('加载资产类型失败:', typeError)
        ElMessage.warning('资产类型加载失败，可能影响编辑功能')
      }
    }
    
    if (locations.value.length === 0) {
      console.log('位置列表为空，开始加载...')
      // 修正为使用默认导出的locationApi对象
      const locationResult = await locationApi.default.getLocationsForDropdown({ includeAll: true })
      if (locationResult && locationResult.success) {
        locations.value = locationResult.data || []
        console.log(`加载了 ${locations.value.length} 个位置`)
      } else {
        console.error('加载位置列表失败:', locationResult?.message)
        ElMessage.warning('位置列表加载失败，可能影响编辑功能')
      }
    }
    
    // 打印当前可用的类型和位置列表，用于调试
    console.log('可用的资产类型列表:', assetTypes.value)
    console.log('可用的位置列表:', locations.value)
    
    // 获取资产详情
    console.log(`获取资产详情，ID: ${row.id}...`)
    const asset = await fetchAssetDetail(row.id)
    
    if (!asset) {
      ElMessage.error('无法获取资产详情，编辑失败')
      return
    }
    
    console.log('成功获取资产详情:', asset)
    
    // 填充表单数据
    assetForm.id = asset.id || row.id
    assetForm.assetCode = asset.assetCode || row.assetCode || ''
    assetForm.financialCode = asset.financialCode || row.financialCode || ''
    assetForm.name = asset.name || row.name || ''
    assetForm.model = asset.model || row.model || ''
    assetForm.brand = asset.brand || row.brand || ''
    assetForm.notes = asset.notes || row.notes || ''
    
    // 特殊处理数值类型
    assetForm.status = asset.status !== undefined ? Number(asset.status) : (row.status !== undefined ? Number(row.status) : 1)
    
    // 处理资产类型ID
    if (asset.assetTypeId) {
      assetForm.assetTypeId = Number(asset.assetTypeId)
      console.log(`设置资产类型ID: ${assetForm.assetTypeId}`)
      
      // 确保assetTypes.value是数组
      if (Array.isArray(assetTypes.value)) {
        // 验证资产类型ID是否存在于资产类型列表中
        const assetType = assetTypes.value.find(t => t.id === assetForm.assetTypeId)
        if (assetType) {
          console.log(`资产类型ID ${assetForm.assetTypeId} 对应名称: "${assetType.name}"`)
        } else {
          console.warn(`未找到资产类型ID ${assetForm.assetTypeId} 对应的类型`)
          
          // 如果有资产类型名称但没有对应ID，尝试通过名称查找
          if (asset.assetTypeName && Array.isArray(assetTypes.value)) {
            const typeByName = assetTypes.value.find(t => t.name === asset.assetTypeName)
            if (typeByName) {
              assetForm.assetTypeId = typeByName.id
              console.log(`通过名称 "${asset.assetTypeName}" 找到资产类型ID: ${typeByName.id}`)
            }
          }
        }
      } else {
        console.error('assetTypes.value 不是数组:', assetTypes.value)
        ElMessage.warning('资产类型数据格式错误，可能影响显示')
      }
    } else if (asset.assetTypeName && Array.isArray(assetTypes.value)) {
      // 通过名称查找ID
      const typeByName = assetTypes.value.find(t => t.name === asset.assetTypeName)
      if (typeByName) {
        assetForm.assetTypeId = typeByName.id
        console.log(`通过名称 "${asset.assetTypeName}" 找到资产类型ID: ${typeByName.id}`)
      }
    }
    
    // 处理位置ID
    if (asset.locationId) {
      assetForm.locationId = Number(asset.locationId)
      console.log(`设置位置ID: ${assetForm.locationId}`)
      
      // 确保locations.value是数组
      if (Array.isArray(locations.value)) {
        // 验证位置ID是否存在于位置列表中
        const location = locations.value.find(l => l.id === assetForm.locationId)
        if (location) {
          console.log(`位置ID ${assetForm.locationId} 对应名称: "${location.name}"`)
        } else {
          console.warn(`未找到位置ID ${assetForm.locationId} 对应的位置`)
          
          // 如果有位置名称但没有对应ID，尝试通过名称查找
          if (asset.locationName && Array.isArray(locations.value)) {
            const locationByName = locations.value.find(l => 
              l.name === asset.locationName || 
              l.fullName === asset.locationName)
            if (locationByName) {
              assetForm.locationId = locationByName.id
              console.log(`通过名称 "${asset.locationName}" 找到位置ID: ${locationByName.id}`)
            }
          }
        }
      } else {
        console.error('locations.value 不是数组:', locations.value)
        ElMessage.warning('位置数据格式错误，可能影响显示')
      }
    } else if (asset.locationName && Array.isArray(locations.value)) {
      // 通过名称查找ID
      const locationByName = locations.value.find(l => 
        l.name === asset.locationName || 
        l.fullName === asset.locationName)
      if (locationByName) {
        assetForm.locationId = locationByName.id
        console.log(`通过名称 "${asset.locationName}" 找到位置ID: ${locationByName.id}`)
      }
    }
    
    // 保存当前资产以便后续比较变更
    currentAsset.value = { ...asset }
    
    // 打印最终的表单数据
    console.log('表单已填充完成:', assetForm)
    
    // 打开编辑对话框
    editDialogVisible.value = true
  } catch (error) {
    console.error('准备编辑对话框时出错:', error)
    ElMessage.error('准备编辑对话框失败: ' + (error.message || '未知错误'))
  }
}

const handleView = async (row) => {
  console.log('查看资产详情，行数据:', row);
  try {
    // 获取资产详情
    const asset = await fetchAssetDetail(row.id);
    console.log('获取到的资产详情数据:', asset);
    
  if (asset) {
      // 确保所有字段都被正确赋值
      currentAsset.value = {
        ...row,  // 先保留表格中的数据
        ...asset // 然后用API获取的详细数据覆盖
      };
      
      // 确保关键字段存在，如果不存在则使用备选值
      if (!currentAsset.value.assetTypeName && currentAsset.value.assetTypeId) {
        // 如果缺少资产类型名称但有ID，尝试从资产类型列表中查找
        const assetType = assetTypes.value.find(t => t.id === currentAsset.value.assetTypeId);
        if (assetType) {
          currentAsset.value.assetTypeName = assetType.name;
          console.log('从资产类型列表中找到类型名称:', assetType.name);
        }
      }
      
      if (!currentAsset.value.locationName && currentAsset.value.locationId) {
        // 如果缺少位置名称但有ID，尝试从位置列表中查找
        const location = locations.value.find(l => l.id === currentAsset.value.locationId);
        if (location) {
          currentAsset.value.locationName = location.fullName || location.name;
          console.log('从位置列表中找到位置名称:', currentAsset.value.locationName);
        }
      }
      
      console.log('最终显示的资产详情数据:', currentAsset.value);
      detailDialogVisible.value = true;
    }
  } catch (error) {
    console.error('获取资产详情失败:', error);
    ElMessage.error('获取资产详情失败');
  }
}

const handleHistory = async (row) => {
  currentAsset.value = row
  historyCurrentPage.value = 1
  await fetchAssetHistory(row.id)
  historyDialogVisible.value = true
}

const handleDelete = (row) => {
  ElMessageBox.confirm('确认删除该资产吗?', '提示', {
    type: 'warning'
  }).then(async () => {
    try {
      const res = await assetApi.deleteAsset(row.id)
      if (res.success === true) {
        ElMessage.success('删除成功')
        fetchAssetList()
      } else {
        ElMessage.error(res.message || '删除失败')
      }
    } catch (error) {
      console.error('删除资产失败:', error)
      ElMessage.error('删除失败')
    }
  })
}

const submitAssetForm = async () => {
  if (!assetFormRef.value) return
  
  try {
    await assetFormRef.value.validate()
    
    // 格式化提交的数据 - 特别处理ID字段
    const formData = formatAssetFormData()
    
    // 如果是编辑模式，检测哪些字段发生了变更
    let changedFields = {}
    
    if (!isAdd.value && currentAsset.value) {
      console.log('原始资产数据:', currentAsset.value)
      console.log('提交的表单数据:', formData)
      
      // 构建要检查变更的字段列表
      const fieldsToCheck = [
        'assetCode', 'financialCode', 'name', 'assetTypeId', 
        'model', 'brand', 'locationId', 'status', 'notes'
      ]
      
      // 只检查有意义的字段是否发生变更
      fieldsToCheck.forEach(key => {
        // 跳过未定义的字段
        if (formData[key] === undefined) return
        
        // 获取原始值和新值
        let oldValue = currentAsset.value[key]
        let newValue = formData[key]
        
        // 对于数字类型进行特殊处理
        if (key === 'assetTypeId' || key === 'locationId' || key === 'status') {
          oldValue = oldValue !== null && oldValue !== undefined ? Number(oldValue) : null
          newValue = newValue !== null && newValue !== undefined ? Number(newValue) : null
        }
        
        // 对于字符串进行特殊处理，避免空白字符或null/undefined导致的误判
        if (typeof oldValue === 'string' || typeof newValue === 'string') {
          const normalizedOld = (oldValue || '').toString().trim()
          const normalizedNew = (newValue || '').toString().trim()
          
          if (normalizedOld !== normalizedNew) {
            changedFields[key] = { 
              oldValue: normalizedOld || '无', 
              newValue: normalizedNew || '无' 
            }
          }
        }
        // 对于其他类型，直接比较
        else if (oldValue !== newValue) {
          // 确保两边都不是undefined或null才算变更
          if ((oldValue !== null && oldValue !== undefined) || 
              (newValue !== null && newValue !== undefined)) {
            changedFields[key] = { 
              oldValue: oldValue === null || oldValue === undefined ? '无' : oldValue, 
              newValue: newValue === null || newValue === undefined ? '无' : newValue 
            }
          }
        }
      })
      
      console.log('检测到的变更字段:', changedFields)
      console.log('变更字段数量:', Object.keys(changedFields).length)
      
      // 如果没有实际变更，提醒用户并返回
      if (Object.keys(changedFields).length === 0) {
        ElMessage.info('没有修改任何内容，无需保存')
        return
      }
      
      // 如果是资产类型或位置变更，添加名称信息
      if (changedFields['assetTypeId']) {
        const oldAssetType = assetTypes.value.find(t => t.id === Number(currentAsset.value.assetTypeId))
        const newAssetType = assetTypes.value.find(t => t.id === Number(formData.assetTypeId))
        
        changedFields['资产类型'] = {
          oldValue: oldAssetType ? oldAssetType.name : '未知',
          newValue: newAssetType ? newAssetType.name : '未知'
        }
      }
      
      if (changedFields['locationId']) {
        const oldLocation = locations.value.find(l => l.id === Number(currentAsset.value.locationId))
        const newLocation = locations.value.find(l => l.id === Number(formData.locationId))
        
        changedFields['位置'] = {
          oldValue: oldLocation ? oldLocation.fullName || oldLocation.name : '未分配',
          newValue: newLocation ? newLocation.fullName || newLocation.name : '未分配'
        }
      }
      
      if (changedFields['status']) {
        changedFields['状态'] = {
          oldValue: getStatusText(currentAsset.value.status),
          newValue: getStatusText(formData.status)
        }
      }
    }
    
    loading.value = true
    try {
      if (isAdd.value) {
        // 创建模式
        const result = await assetApi.createAsset(formData)
        if (result.success) {
          ElMessage.success('创建资产成功')
          editDialogVisible.value = false
          fetchAssetList() // 刷新列表
      } else {
          ElMessage.error(result.message || '创建资产失败')
        }
      } else {
        // 编辑模式 - 将资产数据和变更信息一起发送
        const requestData = {
          asset: formData,
          changes: changedFields
        }
        
        console.log('发送到后端的数据:', requestData)
        
        const result = await assetApi.updateAsset(assetForm.id, requestData)
        if (result.success) {
          ElMessage.success('更新资产成功')
        editDialogVisible.value = false
          fetchAssetList() // 刷新列表
      } else {
          ElMessage.error(result.message || '更新资产失败')
        }
      }
    } catch (error) {
      console.error(isAdd.value ? '创建资产失败:' : '更新资产失败:', error)
      ElMessage.error(isAdd.value ? '创建资产失败' : '更新资产失败')
    } finally {
      loading.value = false
    }
  } catch (error) {
    console.error('表单验证失败:', error)
    ElMessage.error('请完成必填项')
  }
}

// 格式化表单数据，处理特殊类型
const formatAssetFormData = () => {
  const formData = { ...assetForm }

  // 创建新资产时，移除id字段避免序列化错误
  if (isAdd.value && (formData.id === '' || formData.id === null || formData.id === undefined)) {
    delete formData.id
  }

  // 确保assetTypeId是数字
  if (formData.assetTypeId !== undefined && formData.assetTypeId !== null) {
    formData.assetTypeId = Number(formData.assetTypeId)
    console.log(`转换后的assetTypeId: ${formData.assetTypeId}, 类型: ${typeof formData.assetTypeId}`)
  }

  // 确保locationId是数字
  if (formData.locationId !== undefined && formData.locationId !== null) {
    formData.locationId = Number(formData.locationId)
    console.log(`转换后的locationId: ${formData.locationId}, 类型: ${typeof formData.locationId}`)
  }

  // 确保status是数字
  if (formData.status !== undefined) {
    formData.status = Number(formData.status)
  }

  console.log('格式化后的表单数据:', formData)
  return formData
}

const handleImport = () => {
  importDialogVisible.value = true
  uploadFile.value = null
  importing.value = false
  importProgress.value = 0
  importResult.value = null
}

// 下载导入模板
const downloadTemplate = async () => {
  try {
    console.log('开始下载资产导入模板')
    
    // 直接使用fetch API下载模板
    const url = `${systemConfig.apiBaseUrl}/import/template?entityType=Assets&format=excel`
    const token = getToken()
    
    console.log('请求URL:', url)
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Authorization': token ? `Bearer ${token}` : ''
      }
    })
    
    if (!response.ok) {
      const errorText = await response.text()
      console.error('下载模板失败，状态码:', response.status, errorText)
      throw new Error(`服务器返回错误: ${response.status} ${response.statusText}`)
    }
    
    const blob = await response.blob()
    const fileName = `资产导入模板_${new Date().toISOString().substring(0, 10)}.xlsx`
    
    if (window.navigator.msSaveOrOpenBlob) {
      window.navigator.msSaveBlob(blob, fileName)
    } else {
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = fileName
      link.style.display = 'none'
      document.body.appendChild(link)
      link.click()
      URL.revokeObjectURL(url)
      document.body.removeChild(link)
    }
    
    ElMessage.success('模板下载成功')
  } catch (error) {
    console.error('下载导入模板失败:', error)
    ElMessage.error(`模板下载失败: ${error.message}`)
  }
}

// 上传前验证
const beforeImportUpload = (file) => {
  const isExcel = file.type === 'application/vnd.ms-excel' || 
                 file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                 file.name.endsWith('.xlsx') || 
                 file.name.endsWith('.xls')
  
  const isCsv = file.type === 'text/csv' || file.name.endsWith('.csv')
  const isValidType = isExcel || isCsv
  
  const isLt10M = file.size / 1024 / 1024 < 10
  
  if (!isValidType) {
    ElMessage.error('请上传Excel或CSV格式的文件!')
    return false
  }
  
  if (!isLt10M) {
    ElMessage.error('文件大小不能超过10MB!')
    return false
  }
  
  // 保存文件引用
  uploadFile.value = file
  console.log('选择的文件:', file.name, file.type, file.size)
  return true
}

// 文件变更处理
const handleFileChange = (file) => {
  console.log('文件选择变更:', file)
  if (file) {
    uploadFile.value = file.raw || file
  }
}

// 自定义上传请求
const customUploadRequest = (options) => {
  if (!options.file) {
    ElMessage.warning('没有选择文件')
    return
  }
  
  console.log('开始自定义上传:', options.file.name)
  const formData = new FormData()
  formData.append('file', options.file)
  formData.append('entityType', 'Assets')
  
  importing.value = true
  importProgress.value = 10
  
  // 使用XMLHttpRequest进行上传
  const xhr = new XMLHttpRequest()
  xhr.open('POST', `${systemConfig.apiBaseUrl}/import/data`, true)
  
  // 添加请求头
  const token = getToken()
  if (token) {
    xhr.setRequestHeader('Authorization', `Bearer ${token}`)
  }
  
  // 监听上传进度
  xhr.upload.addEventListener('progress', (event) => {
    if (event.lengthComputable) {
      const progress = Math.round((event.loaded * 100) / event.total)
      importProgress.value = progress
      console.log('上传进度:', progress + '%')
      
      if (options.onProgress) {
        options.onProgress({ percent: progress })
      }
    }
  })
  
  // 请求完成
  xhr.onload = () => {
    if (xhr.status >= 200 && xhr.status < 300) {
      // 上传成功
      let responseData
      try {
        responseData = JSON.parse(xhr.responseText)
      } catch (e) {
        responseData = { success: false, message: '无法解析服务器响应' }
      }
      
      console.log('上传成功：', responseData)
      
      if (options.onSuccess) {
        options.onSuccess(responseData)
      }
      
      // 调用统一的成功处理函数
      handleImportSuccess(responseData)
    } else {
      // 上传失败
      let errorMessage = '上传失败'
      let errorData = null
      
      try {
        errorData = JSON.parse(xhr.responseText)
        errorMessage = errorData.message || `服务器返回错误: ${xhr.status}`
      } catch (e) {
        errorMessage = `上传失败: ${xhr.status} ${xhr.statusText}`
      }
      
      console.error('上传失败：', errorMessage, xhr.responseText)
      
      if (options.onError) {
        options.onError(new Error(errorMessage))
      }
      
      // 调用统一的错误处理函数
      handleImportError({ message: errorMessage, response: { data: errorData } })
    }
  }
  
  // 请求错误
  xhr.onerror = () => {
    const errorMessage = '网络错误，上传失败'
    console.error('上传网络错误')
    
    if (options.onError) {
      options.onError(new Error(errorMessage))
    }
    
    // 调用统一的错误处理函数
    handleImportError({ message: errorMessage })
  }
  
  // 发送请求
  xhr.send(formData)
}

// 提交导入
const submitImport = () => {
  if (uploadFile.value) {
    console.log('开始导入，提交上传文件:', uploadFile.value.name)
    importing.value = true
    importProgress.value = 0
    importResult.value = null
    
    // 获取上传组件中的文件列表
    if (uploadRef.value && uploadRef.value.uploadFiles && uploadRef.value.uploadFiles.length > 0) {
      console.log('使用上传组件的submit方法')
      uploadRef.value.submit()
    } else {
      // 手动创建FormData进行上传
      console.log('手动上传文件')
      const formData = new FormData()
      formData.append('file', uploadFile.value)
      formData.append('entityType', 'Assets')
      
      // 使用自定义上传函数
      customUploadRequest({
        file: uploadFile.value,
        onProgress: (e) => handleImportProgress(e),
        onSuccess: (res) => handleImportSuccess(res),
        onError: (err) => handleImportError(err)
      })
    }
  } else {
    ElMessage.warning('请先选择要导入的文件')
  }
}

// 导入进度
const handleImportProgress = (event) => {
  importProgress.value = Math.round(event.percent)
}

// 导入成功
const handleImportSuccess = (response) => {
  importing.value = false
  importProgress.value = 100
  
  if (response.success) {
    ElMessage.success('数据导入成功')
    
    importResult.value = {
      success: true,
      message: response.message || '导入成功',
      data: {
        successCount: response.successCount || 0,
        errorCount: response.errorCount || 0,
        totalRows: response.totalRows || 0,
        errorMessages: []
      }
    }
    
    // 添加错误信息（如果有）
    if (response.errors && Array.isArray(response.errors)) {
      importResult.value.data.errorMessages = response.errors
    }
    else if (response.errors && typeof response.errors === 'object') {
      // 转换错误对象为数组
      const errorMessages = []
      for (const row in response.errors) {
        errorMessages.push(`行 ${row}: ${response.errors[row]}`)
      }
      importResult.value.data.errorMessages = errorMessages
    }
    
    // 添加警告信息（如果有）
    if (response.warnings && typeof response.warnings === 'object') {
      const warningMessages = []
      for (const row in response.warnings) {
        const rowWarnings = response.warnings[row]
        if (Array.isArray(rowWarnings)) {
          rowWarnings.forEach(warning => {
            warningMessages.push(`行 ${row} 警告: ${warning}`)
          })
  } else {
          warningMessages.push(`行 ${row} 警告: ${rowWarnings}`)
        }
      }
      importResult.value.data.warningMessages = warningMessages
    }
  } else {
    // 处理导入失败的情况
    ElMessage.error(response.message || '导入失败')
    
    importResult.value = {
      success: false,
      message: response.message || '导入失败',
      data: {
        successCount: response.successCount || 0,
        errorCount: response.errorCount || 0,
        totalRows: response.totalRows || 0,
        errorMessages: []
      }
    }
    
    // 添加错误信息（如果有）
    if (response.errors && Array.isArray(response.errors)) {
      importResult.value.data.errorMessages = response.errors
    }
    else if (response.errors && typeof response.errors === 'object') {
      // 转换错误对象为数组
      const errorMessages = []
      for (const row in response.errors) {
        errorMessages.push(`行 ${row}: ${response.errors[row]}`)
      }
      importResult.value.data.errorMessages = errorMessages
    }
  }
}

// 导入错误
const handleImportError = (error) => {
  importing.value = false
  console.error('导入出错:', error)
  
  let errorMessage = error.message || '导入失败'
  let errorDetails = []
  
  // 尝试提取详细错误信息
    if (error.response && error.response.data) {
    const data = error.response.data
    if (data.message) {
      errorMessage = data.message
    }
    if (data.errors && Array.isArray(data.errors)) {
      errorDetails = data.errors
    }
    else if (data.errors && typeof data.errors === 'object') {
      for (const key in data.errors) {
        errorDetails.push(`${key}: ${data.errors[key]}`)
      }
    }
  }
  
  ElMessage.error(errorMessage)
  
  importResult.value = {
    success: false,
    message: errorMessage,
    data: {
      successCount: 0,
      errorCount: 0,
      errorMessages: errorDetails
    }
  }
}

// 完成导入并刷新
const finishImport = () => {
  importDialogVisible.value = false
  fetchAssetList() // 刷新资产列表
}

const handleExport = async () => {
  try {
    loading.value = true
    const params = {
      assetCode: searchForm.assetCode || '',
      name: searchForm.name || '',
      assetTypeName: searchForm.assetTypeName || '',
      statusName: searchForm.statusName || ''
    }
    
    await assetApi.exportAssets(params)
    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败', error)
    ElMessage.error('导出失败：' + (error.message || '未知错误'))
  } finally {
    loading.value = false
  }
}

// 预处理历史记录项，确保可以统一处理LocationHistories和AssetHistories
const preprocessHistoryItem = (item) => {
  // 深拷贝以避免修改原始对象
  const result = { ...item };
  
  console.log('处理历史记录项:', item);
  
  // 处理AssetHistories类型记录
  if (item.operationType !== undefined || item.description) {
    // 这是AssetHistories类型
    result.isAssetHistory = true;
    result.operationTypeName = getOperationTypeText(item.operationType);
    
    // 尝试解析description字段中的JSON
    if (item.description && typeof item.description === 'string') {
      try {
        const parsed = JSON.parse(item.description);
        
        // 确保生成一个标准化的解析结果，支持多种格式
        const standardizedChanges = {};
        
        Object.keys(parsed).forEach(key => {
          const value = parsed[key];
          if (typeof value === 'object') {
            // 处理不同格式的变更记录
            if ('oldValue' in value && 'newValue' in value) {
              standardizedChanges[key] = {
                oldValue: value.oldValue === null || value.oldValue === undefined ? '无' : String(value.oldValue),
                newValue: value.newValue === null || value.newValue === undefined ? '无' : String(value.newValue)
              };
            } else if ('Old' in value && 'New' in value) {
              standardizedChanges[key] = {
                oldValue: value.Old === null || value.Old === undefined ? '无' : String(value.Old),
                newValue: value.New === null || value.New === undefined ? '无' : String(value.New)
              };
            } else if ('old' in value && 'new' in value) {
              standardizedChanges[key] = {
                oldValue: value.old === null || value.old === undefined ? '无' : String(value.old),
                newValue: value.new === null || value.new === undefined ? '无' : String(value.new)
              };
            } else {
              standardizedChanges[key] = value;
            }
          } else {
            standardizedChanges[key] = value;
          }
        });
        
        result.parsedChanges = standardizedChanges;
        console.log('成功解析变更信息:', result.parsedChanges);
      } catch (e) {
        console.error('解析变更信息失败:', e);
        result.parsedChanges = null;
      }
    }
  } 
  // 处理LocationHistories类型记录
  else if (item.changeType !== undefined || item.oldLocationId !== undefined) {
    // 这是LocationHistories类型
    result.isLocationHistory = true;
    result.operationType = 4; // 位置变更
    result.operationTypeName = '位置变更';
    result.operationTime = item.changeTime;
    
    // 构建位置变更的信息
    result.parsedChanges = {
      '位置': {
        oldValue: item.oldLocationName || '未知位置',
        newValue: item.newLocationName || '未知位置'
      }
    };
    
    // 如果有备注信息，也添加到解析结果中
    if (item.notes) {
      result.parsedChanges['备注'] = item.notes;
    }
  }
  
  return result;
};

// 用于显示操作类型对应的标签样式
const getUpdateTypeTag = (type) => {
  const types = {
    1: 'success', // 创建
    2: 'primary', // 修改
    3: 'danger',  // 删除
    4: 'warning', // 位置变更
    5: 'info'     // 状态变更
  };
  return types[type] || 'info';
};

// 获取旧值
const getOldValue = (change) => {
  if (!change) return '无';
  
  if (typeof change === 'object') {
    if ('oldValue' in change) {
      return change.oldValue === null || change.oldValue === undefined || change.oldValue === '' ? '无' : String(change.oldValue);
    } else if ('Old' in change) {
      return change.Old === null || change.Old === undefined || change.Old === '' ? '无' : String(change.Old);
    } else if ('old' in change) {
      return change.old === null || change.old === undefined || change.old === '' ? '无' : String(change.old);
    }
  }
  
  return '无';
};

// 获取新值
const getNewValue = (change) => {
  if (!change) return '无';
  
  if (typeof change === 'object') {
    if ('newValue' in change) {
      return change.newValue === null || change.newValue === undefined || change.newValue === '' ? '无' : String(change.newValue);
    } else if ('New' in change) {
      return change.New === null || change.New === undefined || change.New === '' ? '无' : String(change.New);
    } else if ('new' in change) {
      return change.new === null || change.new === undefined || change.new === '' ? '无' : String(change.new);
    }
  }
  
  return String(change);
};

// 显示JSON详情
const showJsonDetails = (row) => {
  if (!row.description) return;
  
  try {
    const parsed = JSON.parse(row.description);
    row.parsedChanges = parsed;
    console.log('手动解析变更详情:', parsed);
  } catch (e) {
    console.error('无法解析JSON详情:', e);
    ElMessage.error('无法解析变更详情');
  }
}

// 判断字符串是否为JSON
const isJsonString = (str) => {
  if (!str || typeof str !== 'string') return false;
  
  try {
    JSON.parse(str);
    return true;
  } catch (e) {
    return false;
  }
}

// 初始化
onMounted(async () => {
  try {
    console.log('组件挂载，开始初始化数据')
    // 并行加载资产类型和位置
    await Promise.all([
      fetchAssetTypeList(),
      fetchLocationList()
    ])
    console.log('资产类型和位置加载完成，开始获取资产列表')
    // 确保立即获取资产列表
    fetchAssetList()
  } catch (error) {
    console.error('初始化数据失败:', error)
    ElMessage.error('初始化数据失败，请刷新页面重试')
  }
})

// 格式化字段值
const formatFieldValue = (field, value) => {
  if (value === null || value === undefined) return '无'
  
  // 根据字段名称进行特殊格式化
  switch (field.toLowerCase()) {
    case 'price':
      return formatPrice(value)
    case 'purchasedate':
    case 'warrantyexpiredate':
      return formatDate(value)
    case 'status':
      return getStatusName(parseInt(value))
    default:
      return value
  }
}

// 表格选择变更
const handleSelectionChange = (selection) => {
  selectedAssets.value = selection
}

// 批量删除
const handleBatchDelete = () => {
  if (selectedAssets.value.length === 0) {
    ElMessage.warning('请选择要删除的资产')
    return
  }
  
  const assetNames = selectedAssets.value.map(asset => asset.name || asset.assetCode).join(', ')
  
  ElMessageBox.confirm(
    `确定要删除选中的 ${selectedAssets.value.length} 个资产吗？删除后不可恢复！`,
    '警告',
    {
      confirmButtonText: '确定删除',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    loading.value = true
    try {
      // 创建删除请求数组
      const deletePromises = selectedAssets.value.map(asset => 
        assetApi.deleteAsset(asset.id)
      )
      
      // 并行处理所有请求
      const results = await Promise.allSettled(deletePromises)
      
      // 检查结果
      const succeeded = results.filter(r => r.status === 'fulfilled' && r.value?.success).length
      const failed = results.length - succeeded
      
      if (failed === 0) {
        ElMessage.success(`成功删除 ${succeeded} 个资产`)
      } else if (succeeded === 0) {
        ElMessage.error('删除失败，请重试')
      } else {
        ElMessage.warning(`部分资产删除成功，${succeeded} 成功，${failed} 失败`)
      }
      
      // 刷新资产列表
  fetchAssetList()
    } catch (error) {
      console.error('批量删除资产失败:', error)
      ElMessage.error('批量删除资产失败:' + (error.message || '未知错误'))
    } finally {
      loading.value = false
      // 清空选择
      selectedAssets.value = []
    }
  }).catch(() => {
    // 用户取消删除操作
  })
}

// 格式化字段名称
const formatFieldName = (field) => {
  const fieldNameMap = {
    'assetCode': '资产编号',
    'financialCode': '财务编号',
    'name': '资产名称',
    'assetTypeId': '资产类型ID',
    'locationId': '位置ID',
    'model': '型号',
    'brand': '品牌',
    'status': '状态',
    'notes': '备注',
    '资产编码': '资产编号',
    '财务编码': '财务编号',
    '名称': '资产名称',
    '资产类型': '资产类型',
    '序列号': '序列号',
    '型号': '型号',
    '品牌': '品牌',
    '购买日期': '购买日期',
    '保修到期日': '保修到期日',
    '价格': '价格',
    '位置': '位置',
    '状态': '状态',
    '备注': '备注',
    'id': 'ID'
  };
  
  return fieldNameMap[field] || field;
}

// 过滤展示变更，隐藏技术性字段，只显示有变化的字段
const filteredChanges = (changes) => {
  if (!changes) return {};
  
  const filtered = {};
  const hiddenFields = ['id', 'assetTypeId', 'locationId', 'ID'];
  
  Object.keys(changes).forEach(field => {
    // 跳过包含"ID"的技术字段，但保留用户友好的字段
    if (!hiddenFields.includes(field) && !field.endsWith('Id') && !field.includes('ID')) {
      // 只显示实际发生了变化的字段
      const value = changes[field];
      const oldValue = getOldValue(value);
      const newValue = getNewValue(value);
      
      // 如果新旧值不同，才显示这个字段
      if (oldValue !== newValue) {
        filtered[field] = changes[field];
      }
    }
  });
  
  return filtered;
};
</script>

<style lang="scss" scoped>
.asset-list {
  padding: 20px;

  .sticky-header {
    // 移除sticky定位，让搜索区域随页面滚动
    position: relative;
    background-color: white;
    padding-bottom: 10px;
    // 移除阴影和边距调整
    box-shadow: none;
    margin: 0;
    padding: 0;
  }

  .content-area {
    padding-top: 10px;
  }

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    h2 {
      margin: 0;
    }

    .header-actions {
      .el-button {
        margin-left: 10px;
      }
    }
  }

  .search-form {
    margin-bottom: 20px;
    
    .el-form-item {
      margin-bottom: 10px;
    }
  }

  .el-table {
    margin-bottom: 20px;
  }

  .pagination-container {
    margin-top: 20px;
    text-align: right;
    margin-bottom: 20px;
  }

  .delete-btn {
    color: #F56C6C;
  }
}

// 历史记录对话框样式
.history-dialog {
  .asset-title {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    color: #303133;
    font-size: 1.2em;
    padding: 10px;
    background: #f5f7fa;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    .el-icon {
      margin-right: 10px;
      font-size: 1.2em;
      color: #409EFF;
    }

    .el-tag {
      margin-left: 10px;
    }
  }

  .history-table {
    width: 100%;

    .empty-history {
      text-align: center;
      padding: 20px;
    }

    .el-table {
      margin-bottom: 10px;
    }
  }

  .pagination-container {
    padding: 10px 0;
    margin-top: 15px;
    display: flex;
    justify-content: flex-end;
    border-top: 1px solid #EBEEF5;
  }
}

// 导入对话框样式
.import-dialog-content {
  padding: 10px 0;

  .import-steps {
    margin-bottom: 20px;
    .step {
      margin: 10px 0;
      display: flex;
      align-items: center;
      gap: 10px;
    }
  }
  
  .template-download {
  display: flex;
    flex-direction: column;
  align-items: center;
    margin: 20px 0;
    
    .template-tip {
      margin-top: 10px;
      color: #909399;
      font-size: 0.9em;
    }
  }
  
  .import-upload {
    margin: 20px 0;
  }
  
  .import-progress {
    margin: 20px 0;
    
    .progress-text {
      text-align: center;
      margin-top: 10px;
      color: #409EFF;
    }
  }
  
  .import-result {
    margin: 20px 0;
    
    .result-info {
      padding: 10px;
      
      .message-title {
  font-weight: bold;
        margin: 10px 0 5px 0;
      }
      
      .message-list {
        list-style: none;
        padding: 0;
        margin: 0;
        
        li {
  padding: 8px;
          margin-bottom: 5px;
          background-color: #f8f8f8;
  border-radius: 4px;
  display: flex;
          align-items: flex-start;
          gap: 8px;
          
          .el-tag {
            margin-top: 2px;
          }
        }
      }
      
      .error-messages {
        margin-top: 15px;
      }
      
      .warning-messages {
        margin-top: 15px;
      }
    }
  }
}

.ml-3 {
  margin-left: 12px;
}

// 对话框内容滚动
:deep(.el-dialog__body) {
  padding: 20px;
  
  // 对于特别长的内容，可以设置一个非常大的最大高度
  // 这样对于大多数内容不会出现滚动条，但对于极长内容仍有保护
  max-height: 80vh;
  overflow-y: auto;
}

// 变更内容清单样式
.changes-list {
  padding: 5px 0;
  
  .change-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    padding-bottom: 8px;
    border-bottom: 1px dashed #ebeef5;
    
    &:last-child {
      margin-bottom: 0;
      padding-bottom: 0;
      border-bottom: none;
    }
    
    .field-name {
      font-weight: 500;
      color: #606266;
      min-width: 80px;
      margin-right: 15px;
    }
    
    .change-values {
      display: flex;
      align-items: center;
      
      .change-arrow {
        margin: 0 8px;
    color: #909399;
      }
    }
  }
}

.change-value-old {
  text-decoration: line-through;
        color: #F56C6C;
}

.change-value-new {
  color: #67C23A;
}
</style> 

