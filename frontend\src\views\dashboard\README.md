# 数字化工厂看板使用说明

## 🎯 功能概述

数字化工厂看板是基于Vue 3和Element Plus开发的实时工厂监控界面，展示位置设备状态、部门分配和资产分布情况。

## 🚀 核心功能

### 1. 实时状态监控
- **正常运行**: 绿色显示，设备正常工作
- **警告状态**: 黄色显示，设备需要关注
- **故障状态**: 红色显示，设备需要维修
- **空闲状态**: 蓝色显示，设备暂时停机

### 2. 智能筛选功能
- **部门筛选**: 按部门查看管理的位置
- **位置类型**: 按工厂、车间、工序、工位分类
- **设备筛选**: 只显示有设备的位置
- **关键词搜索**: 快速查找特定位置

### 3. 详细信息查看
- **位置信息**: 位置路径、部门分配、设备数量
- **监控数据**: 温度、压力、运行时间、维护记录
- **告警信息**: 错误代码、警告消息
- **设备列表**: 该位置的所有设备清单

### 4. 交互式操作
- **点击查看**: 点击位置卡片查看详细信息
- **悬停预览**: 鼠标悬停高亮显示
- **全屏模式**: 支持全屏查看工厂布局
- **实时刷新**: 自动更新状态数据

## 📱 界面布局

### 顶部导航栏
- **系统标题**: 数字化工厂制造看板
- **更新时间**: 显示最后数据更新时间
- **搜索框**: 输入位置名称快速搜索
- **筛选按钮**: 打开高级筛选选项
- **刷新按钮**: 手动刷新数据
- **全屏按钮**: 切换全屏模式

### 统计面板
- **总位置数**: 显示系统中的位置总数
- **正常运行**: 正常工作的位置数量和百分比
- **警告状态**: 有警告的位置数量
- **故障状态**: 故障位置数量
- **空闲状态**: 空闲位置数量

### 工厂布局区域
- **位置卡片**: 每个卡片代表一个位置
- **状态颜色**: 不同颜色表示不同状态
- **设备信息**: 显示位置名称、部门、设备数量
- **交互反馈**: 悬停和选中效果

### 底部状态栏
- **系统版本**: 显示当前系统版本
- **状态统计**: 各状态位置的实时统计

## 🎨 界面元素说明

### 位置卡片
```
┌─────────────────┐
│ 位置名称    ●   │  ← 状态指示灯
│ 部门名称        │
│ X 台设备        │
└─────────────────┘
```

### 状态指示
- **🟢 绿色圆点**: 正常运行
- **🟡 黄色圆点**: 警告状态（闪烁）
- **🔴 红色圆点**: 故障状态（闪烁）
- **🔵 蓝色圆点**: 空闲状态

### 卡片交互效果
- **悬停**: 青色边框高亮 + 轻微放大
- **选中**: 蓝色边框高亮 + 更大放大
- **高亮**: 虚线边框特殊标记

## ⚙️ 使用方法

### 基础浏览
1. 打开页面后自动加载所有位置数据
2. 鼠标悬停位置卡片查看高亮效果
3. 点击位置卡片打开详情弹窗
4. 查看顶部统计数据了解整体状况

### 搜索和筛选
1. **关键词搜索**:
   - 在搜索框输入位置名称
   - 实时过滤显示匹配结果
   
2. **高级筛选**:
   - 点击"筛选"按钮打开筛选面板
   - 选择部门、位置类型等条件
   - 点击"应用"执行筛选
   - 点击"重置"清除筛选条件

### 详情查看
1. **打开详情弹窗**:
   - 点击任意位置卡片
   - 弹窗显示详细信息
   
2. **查看监控数据**:
   - 温度、压力等实时数据
   - 运行时间和维护记录
   
3. **处理告警**:
   - 查看错误代码和警告信息
   - 点击"启动诊断"进行故障排查

### 全屏模式
1. 点击右上角全屏按钮
2. 在全屏模式下查看完整布局
3. 再次点击退出全屏

## 🔧 API集成

### 数据源
页面数据来源于位置分类查询API：
- `GET /api/v2/locationdepartmentinheritance` - 获取所有位置
- `POST /api/v2/locationdepartmentinheritance/search` - 高级搜索
- `GET /api/v2/locationdepartmentinheritance/classification-stats` - 统计数据

### 模拟数据
为了演示效果，系统会为每个位置生成模拟的监控数据：
- 温度: 20-40°C 随机值
- 压力: 95-105 kPa 随机值
- 运行时间: 0-100% 随机值
- 状态: 根据概率分布随机分配

## 🎯 应用场景

### 1. 生产监控
- 实时查看所有生产位置状态
- 快速识别故障和警告位置
- 按部门查看生产设备分布

### 2. 设备管理
- 统计各部门设备数量
- 查看设备维护记录
- 追踪设备运行状态

### 3. 故障处理
- 快速定位故障位置
- 查看详细错误信息
- 启动远程诊断功能

### 4. 管理决策
- 查看整体运行效率
- 分析部门设备分布
- 评估生产能力利用率

## 📱 响应式设计

### 桌面端 (>1024px)
- 5列统计面板布局
- 完整功能展示
- 大尺寸位置卡片

### 平板端 (768-1024px)
- 2列统计面板布局
- 适中尺寸卡片
- 保持主要功能

### 移动端 (<768px)
- 单列统计面板布局
- 紧凑型卡片设计
- 触摸友好的交互

## 🎨 自定义主题

### 配色方案
- **主色调**: 深灰色背景 (#111827, #1f2937)
- **状态色**: 绿色(正常)、黄色(警告)、红色(故障)、蓝色(空闲)
- **强调色**: 青色(悬停)、蓝色(选中)

### 动画效果
- **悬停动画**: 0.3s 缓动放大
- **状态闪烁**: 2s 循环脉冲动画
- **页面过渡**: 平滑的淡入淡出

## 🚀 性能优化

### 数据加载
- 懒加载位置数据
- 分页查询大量位置
- 缓存常用筛选结果

### 渲染优化
- 虚拟滚动长列表
- 防抖搜索输入
- 组件懒加载

### 用户体验
- 加载状态提示
- 错误处理反馈
- 离线状态提醒

## 🔗 相关文档

- [位置分类查询API文档](../../docs/api/位置分类查询API文档.md)
- [技术文档](../../docs/位置分类查询功能技术文档.md)
- [功能说明](../../位置分类查询功能说明.md)

---

**开发团队**: IT资产管理系统前端组  
**文档版本**: v1.0  
**最后更新**: 2025-06-02