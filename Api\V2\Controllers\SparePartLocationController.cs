// File: Api/V2/Controllers/SparePartLocationController.cs
// Description: 备品备件库位API控制器

using System.Threading.Tasks;
using ItAssetsSystem.Application.Features.SpareParts.Dtos;
using ItAssetsSystem.Application.Features.SpareParts.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ItAssetsSystem.Api.V2.Controllers; // Assuming BaseController is in this namespace
using System.Collections.Generic; // Required for List

namespace ItAssetsSystem.Api.V2.Controllers
{
    /// <summary>
    /// 备品备件库位控制器
    /// </summary>
    [ApiController]
    [Route("api/v2/sparepartlocation")] // Corrected route
    [Authorize]
    public class SparePartLocationController : ControllerBase
    {
        private readonly ISparePartLocationService _locationService;
        
        /// <summary>
        /// 构造函数
        /// </summary>
        public SparePartLocationController(ISparePartLocationService locationService)
        {
            _locationService = locationService;
        }
        
        /// <summary>
        /// 获取所有库位
        /// </summary>
        /// <returns>库位列表</returns>
        [HttpGet]
        public async Task<IActionResult> GetAllLocations()
        {
            var result = await _locationService.GetAllLocationsAsync();
            // Using the standard API response structure
            return Ok(new { success = true, data = result });
        }

        /// <summary>
        /// 按区域筛选库位
        /// </summary>
        /// <param name="area">区域标识</param>
        /// <returns>库位列表</returns>
        [HttpGet("by-area/{area}")]
        public async Task<IActionResult> GetLocationsByArea(string area)
        {
            var result = await _locationService.GetLocationsByAreaAsync(area);
            return Ok(new { success = true, data = result });
        }

        /// <summary>
        /// 获取所有区域列表
        /// </summary>
        /// <returns>区域列表</returns>
        [HttpGet("areas")]
        public async Task<IActionResult> GetAllAreas()
        {
            var result = await _locationService.GetAllAreasAsync();
            return Ok(new { success = true, data = result });
        }
        
        /// <summary>
        /// 获取库位详情
        /// </summary>
        /// <param name="id">库位ID</param>
        /// <returns>库位DTO</returns>
        [HttpGet("{id}")]
        public async Task<IActionResult> GetLocationById(long id)
        {
            var result = await _locationService.GetLocationByIdAsync(id);
            if (result == null)
            {
                return NotFound(new { success = false, message = $"库位 {id} 未找到" });
            }
            return Ok(new { success = true, data = result });
        }
        
        /// <summary>
        /// 创建库位
        /// </summary>
        /// <param name="request">创建请求</param>
        /// <returns>创建的库位DTO</returns>
        [HttpPost]
        public async Task<IActionResult> CreateLocation([FromBody] CreateSparePartLocationRequest request)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(new { success = false, message = "请求无效", errors = ModelState });
            }
            try
            {
                var result = await _locationService.CreateLocationAsync(request);
                return CreatedAtAction(nameof(GetLocationById), new { id = result.Id }, new { success = true, data = result });
            }
            catch (System.ArgumentException ex) // Catch specific exceptions like code already exists
            {
                return BadRequest(new { success = false, message = ex.Message });
            }
        }
        
        /// <summary>
        /// 更新库位
        /// </summary>
        /// <param name="id">库位ID</param>
        /// <param name="request">更新请求</param>
        /// <returns>更新后的库位DTO</returns>
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateLocation(long id, [FromBody] CreateSparePartLocationRequest request)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(new { success = false, message = "请求无效", errors = ModelState });
            }
            try
            {
                var result = await _locationService.UpdateLocationAsync(id, request);
                if (result == null)
                {
                    return NotFound(new { success = false, message = $"库位 {id} 未找到" });
                }
                return Ok(new { success = true, data = result });
            }
            catch (System.ArgumentException ex) // Catch specific exceptions like code already exists
            {
                return BadRequest(new { success = false, message = ex.Message });
            }
        }
        
        /// <summary>
        /// 删除库位
        /// </summary>
        /// <param name="id">库位ID</param>
        /// <returns>是否成功</returns>
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteLocation(long id)
        {
            var success = await _locationService.DeleteLocationAsync(id);
            if (!success)
            {
                return NotFound(new { success = false, message = $"库位 {id} 未找到或删除失败" });
            }
            return Ok(new { success = true });
        }
    }
} 