using System;
using System.ComponentModel.DataAnnotations;

namespace ItAssetsSystem.Models.Dtos
{
    /// <summary>
    /// 创建资产DTO
    /// </summary>
    public class CreateAssetDto
    {
        /// <summary>
        /// 资产编码
        /// </summary>
        [Required(ErrorMessage = "资产编码不能为空")]
        [MaxLength(30, ErrorMessage = "资产编码长度不能超过30个字符")]
        public string AssetCode { get; set; }

        /// <summary>
        /// 财务编号
        /// </summary>
        [MaxLength(50, ErrorMessage = "财务编号长度不能超过50个字符")]
        public string FinancialCode { get; set; }

        /// <summary>
        /// 资产名称
        /// </summary>
        [Required(ErrorMessage = "资产名称不能为空")]
        [MaxLength(100, ErrorMessage = "资产名称长度不能超过100个字符")]
        public string Name { get; set; }

        /// <summary>
        /// 资产类型ID
        /// </summary>
        [Required(ErrorMessage = "资产类型不能为空")]
        [Range(1, int.MaxValue, ErrorMessage = "资产类型ID必须大于0")]
        public int AssetTypeId { get; set; }

        /// <summary>
        /// 序列号
        /// </summary>
        [MaxLength(100, ErrorMessage = "序列号长度不能超过100个字符")]
        public string SerialNumber { get; set; }

        /// <summary>
        /// 型号
        /// </summary>
        [MaxLength(100, ErrorMessage = "型号长度不能超过100个字符")]
        public string Model { get; set; }

        /// <summary>
        /// 品牌
        /// </summary>
        [MaxLength(100, ErrorMessage = "品牌长度不能超过100个字符")]
        public string Brand { get; set; }

        /// <summary>
        /// 购买日期
        /// </summary>
        public DateTime? PurchaseDate { get; set; }

        /// <summary>
        /// 保修到期日
        /// </summary>
        public DateTime? WarrantyExpireDate { get; set; }

        /// <summary>
        /// 价格
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "价格不能为负数")]
        public decimal? Price { get; set; }

        /// <summary>
        /// 位置ID
        /// </summary>
        public int? LocationId { get; set; }

        /// <summary>
        /// 部门ID
        /// </summary>
        public int? DepartmentId { get; set; }

        /// <summary>
        /// 状态：0闲置，1在用，2维修中，3报废
        /// </summary>
        [Range(0, 3, ErrorMessage = "状态值必须在0-3之间")]
        public int Status { get; set; } = 0;

        /// <summary>
        /// 备注
        /// </summary>
        [MaxLength(500, ErrorMessage = "备注长度不能超过500个字符")]
        public string Notes { get; set; }
    }
}
