// File: Application/Features/Inputs/Commands/SubmitInputCommandHandler.cs
// Description: 处理输入内容写入CSV
using MediatR;
using ItAssetsSystem.Application.Common.Dtos;
using System.Globalization;
using System.Text;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using System;

namespace ItAssetsSystem.Application.Features.Inputs.Commands
{
    public class SubmitInputCommandHandler : IRequestHandler<SubmitInputCommand, ApiResponse<bool>>
    {
        private readonly string _csvPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "inputs.csv");

        public async Task<ApiResponse<bool>> Handle(SubmitInputCommand request, CancellationToken cancellationToken)
        {
            var now = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff", CultureInfo.InvariantCulture);
            var isNewFile = !File.Exists(_csvPath);

            using (var stream = new FileStream(_csvPath, FileMode.Append, FileAccess.Write, FileShare.None))
            using (var writer = new StreamWriter(stream, Encoding.UTF8))
            {
                if (isNewFile)
                {
                    await writer.WriteLineAsync("条码,时间序号");
                }
                var safeContent = request.Content.Replace("\"", "\"\"");
                await writer.WriteLineAsync($"\"{safeContent}\",\"{now}\"");
            }

            return ApiResponse<bool>.CreateSuccess(true, "写入成功");
        }
    }
} 