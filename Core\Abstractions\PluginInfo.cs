using System;
using System.Reflection;
using System.Runtime.Loader;
using ItAssetsSystem.Core.Plugins;

namespace ItAssetsSystem.Core.Abstractions
{
    /// <summary>
    /// 插件信息
    /// </summary>
    public class PluginInfo
    {
        /// <summary>
        /// 插件ID
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// 插件实例
        /// </summary>
        public IPlugin Instance { get; set; }

        /// <summary>
        /// 插件加载上下文
        /// </summary>
        public AssemblyLoadContext LoadContext { get; set; }

        /// <summary>
        /// 插件程序集
        /// </summary>
        public Assembly Assembly { get; set; }

        /// <summary>
        /// 插件路径
        /// </summary>
        public string Path { get; set; }

        /// <summary>
        /// 是否为内置插件
        /// </summary>
        public bool IsBuiltIn { get; set; }

        /// <summary>
        /// 加载时间
        /// </summary>
        public DateTime LoadTime { get; set; }
    }
} 