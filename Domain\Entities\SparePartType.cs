// File: Domain/Entities/SparePartType.cs
// Description: 备品备件类型实体类

using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ItAssetsSystem.Domain.Entities
{
    /// <summary>
    /// 备品备件类型实体
    /// </summary>
    [Table("spare_part_types")]
    public class SparePartType
    {
        /// <summary>
        /// 类型ID
        /// </summary>
        [Key]
        [Column("id")]
        public long Id { get; set; }
        
        /// <summary>
        /// 类型名称
        /// </summary>
        [Required]
        [Column("name")]
        [StringLength(50)]
        public string Name { get; set; }
        
        /// <summary>
        /// 类型编码
        /// </summary>
        [Required]
        [Column("code")]
        [StringLength(20)]
        public string Code { get; set; }
        
        /// <summary>
        /// 父类型ID
        /// </summary>
        [Column("parent_id")]
        public long? ParentId { get; set; }
        
        /// <summary>
        /// 父类型导航属性
        /// </summary>
        [ForeignKey("ParentId")]
        public virtual SparePartType Parent { get; set; }
        
        /// <summary>
        /// 子类型集合
        /// </summary>
        public virtual ICollection<SparePartType> Children { get; set; } = new List<SparePartType>();
        
        /// <summary>
        /// 此类型下的备件集合
        /// </summary>
        public virtual ICollection<SparePart> SpareParts { get; set; } = new List<SparePart>();
        
        /// <summary>
        /// 描述
        /// </summary>
        [Column("description")]
        [StringLength(200)]
        public string Description { get; set; }
        
        /// <summary>
        /// 创建人ID
        /// </summary>
        [NotMapped]
        public int CreatorUserId { get; set; }
        
        /// <summary>
        /// 创建时间
        /// </summary>
        [Column("created_at")]
        public DateTime CreatedAt { get; set; }
        
        /// <summary>
        /// 最后修改时间
        /// </summary>
        [Column("updated_at")]
        public DateTime? UpdatedAt { get; set; }
        
        /// <summary>
        /// 类型路径，例如"1,2,3,"，用于快速查询层级关系
        /// </summary>
        [Column("path")]
        [StringLength(255)]
        public string Path { get; set; }
        
        /// <summary>
        /// 层级深度，从0开始
        /// </summary>
        [Column("level")]
        public int Level { get; set; }
        
        /// <summary>
        /// 是否启用
        /// </summary>
        [NotMapped]
        public bool IsActive { get; set; } = true;
        
        /// <summary>
        /// 排序号
        /// </summary>
        [NotMapped]
        public int SortOrder { get; set; }
    }
} 