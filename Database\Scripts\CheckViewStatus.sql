-- 检查视图状态和数据的诊断脚本
-- 用于确定为什么后端返回测试数据

-- ============================================================================
-- 1. 检查视图是否存在
-- ============================================================================
SELECT 'Views Existence Check' AS CheckType;

SELECT 
    TABLE_NAME as ViewName,
    TABLE_TYPE as Type,
    ENGINE,
    TABLE_COMMENT as Comment
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME IN (
    'v_assets_enhanced',
    'v_asset_kpi_enhanced', 
    'v_asset_statistics_fast',
    'v_asset_matrix_enhanced',
    'v_asset_value_distribution_enhanced'
)
ORDER BY TABLE_NAME;

-- ============================================================================
-- 2. 检查基础表的数据量
-- ============================================================================
SELECT 'Base Tables Data Count' AS CheckType;

SELECT 
    'assets' as TableName,
    COUNT(*) as RecordCount,
    MIN(CreatedAt) as EarliestRecord,
    MAX(CreatedAt) as LatestRecord
FROM assets

UNION ALL

SELECT 
    'assettypes' as TableName,
    COUNT(*) as RecordCount,
    MIN(CreatedAt) as EarliestRecord,
    MAX(CreatedAt) as LatestRecord  
FROM assettypes

UNION ALL

SELECT 
    'locations' as TableName,
    COUNT(*) as RecordCount,
    MIN(CreatedAt) as EarliestRecord,
    MAX(CreatedAt) as LatestRecord
FROM locations

UNION ALL

SELECT 
    'departments' as TableName,
    COUNT(*) as RecordCount,
    MIN(CreatedAt) as EarliestRecord,
    MAX(CreatedAt) as LatestRecord
FROM departments;

-- ============================================================================
-- 3. 检查视图的数据量
-- ============================================================================
SELECT 'Views Data Count' AS CheckType;

-- 检查 v_assets_enhanced
SELECT 
    'v_assets_enhanced' as ViewName,
    COUNT(*) as RecordCount,
    COUNT(DISTINCT AssetTypeId) as UniqueAssetTypes,
    COUNT(DISTINCT InheritedDepartmentId) as UniqueDepartments,
    COUNT(DISTINCT RegionId) as UniqueRegions
FROM v_assets_enhanced;

-- 检查 v_asset_kpi_enhanced  
SELECT 
    'v_asset_kpi_enhanced' as ViewName,
    COUNT(*) as RecordCount,
    TotalAssets,
    TotalValueWan,
    InUseRate,
    FaultRate
FROM v_asset_kpi_enhanced;

-- 检查 v_asset_statistics_fast
SELECT 
    'v_asset_statistics_fast' as ViewName,
    COUNT(*) as RecordCount,
    COUNT(DISTINCT DimensionType) as DimensionTypes,
    GROUP_CONCAT(DISTINCT DimensionType) as AvailableDimensions
FROM v_asset_statistics_fast;

-- ============================================================================
-- 4. 检查具体的统计数据
-- ============================================================================
SELECT 'Dimension Statistics Detail' AS CheckType;

-- 按维度类型统计
SELECT 
    DimensionType,
    COUNT(*) as RecordCount,
    SUM(TotalCount) as TotalAssets,
    AVG(InUseRate) as AvgInUseRate
FROM v_asset_statistics_fast
GROUP BY DimensionType;

-- ============================================================================
-- 5. 检查价值分布视图
-- ============================================================================
SELECT 'Value Distribution Check' AS CheckType;

SELECT 
    COUNT(*) as RecordCount
FROM v_asset_value_distribution_enhanced;

-- 显示价值分布数据
SELECT 
    ValueRange,
    AssetCount,
    TotalValue,
    RangeColor
FROM v_asset_value_distribution_enhanced
ORDER BY SortOrder;

-- ============================================================================
-- 6. 检查可能的问题
-- ============================================================================
SELECT 'Potential Issues Check' AS CheckType;

-- 检查资产表中Status字段的分布
SELECT 
    Status,
    COUNT(*) as Count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM assets), 2) as Percentage
FROM assets
GROUP BY Status;

-- 检查是否有NULL值问题
SELECT 
    'Assets with NULL LocationId' as Issue,
    COUNT(*) as Count
FROM assets 
WHERE LocationId IS NULL

UNION ALL

SELECT 
    'Assets with NULL AssetTypeId' as Issue,
    COUNT(*) as Count
FROM assets 
WHERE AssetTypeId IS NULL

UNION ALL

SELECT 
    'Locations with NULL DefaultDepartmentId' as Issue,
    COUNT(*) as Count
FROM locations 
WHERE DefaultDepartmentId IS NULL;

-- ============================================================================
-- 7. 测试视图查询
-- ============================================================================
SELECT 'Sample View Data' AS CheckType;

-- 测试v_assets_enhanced的前几条记录
SELECT 
    AssetId,
    AssetName,
    AssetTypeName,
    DepartmentName,
    StatusText,
    Price
FROM v_assets_enhanced
LIMIT 5;

-- 测试KPI视图的具体值
SELECT 
    TotalAssets,
    TotalValueWan,
    InUseRate,
    IdleRate,
    FaultRate,
    HealthScore
FROM v_asset_kpi_enhanced;

SELECT 'Diagnostic Complete' AS Result;