#include <windows.h>
#include <iostream>
#include <string>
#include <chrono>
#include <thread>

// Simple demo notification client that actually compiles
class SimpleNotificationClient {
private:
    SOCKET udp_socket;
    bool running;
    std::string server_host;
    int server_port;

public:
    SimpleNotificationClient() : udp_socket(INVALID_SOCKET), running(false), 
                               server_host("localhost"), server_port(8080) {
        // Initialize Winsock
        WSADATA wsaData;
        WSAStartup(MAKEWORD(2, 2), &wsaData);
    }
    
    ~SimpleNotificationClient() {
        stop();
        WSACleanup();
    }
    
    bool initialize(const std::string& host = "localhost", int port = 8080) {
        server_host = host;
        server_port = port;
        
        // Create UDP socket
        udp_socket = socket(AF_INET, SOCK_DGRAM, IPPROTO_UDP);
        if (udp_socket == INVALID_SOCKET) {
            std::cerr << "Failed to create UDP socket. Error: " << WSAGetLastError() << std::endl;
            return false;
        }
        
        std::cout << "Notification client initialized successfully" << std::endl;
        std::cout << "Target server: " << server_host << ":" << server_port << std::endl;
        return true;
    }
    
    bool start() {
        if (udp_socket == INVALID_SOCKET) {
            std::cerr << "Client not initialized!" << std::endl;
            return false;
        }
        
        running = true;
        std::cout << "Notification client started" << std::endl;
        return true;
    }
    
    void stop() {
        running = false;
        if (udp_socket != INVALID_SOCKET) {
            closesocket(udp_socket);
            udp_socket = INVALID_SOCKET;
        }
        std::cout << "Notification client stopped" << std::endl;
    }
    
    bool isRunning() const {
        return running;
    }
    
    void sendTestMessage() {
        if (udp_socket == INVALID_SOCKET || !running) {
            return;
        }
        
        sockaddr_in server_addr;
        server_addr.sin_family = AF_INET;
        server_addr.sin_port = htons(server_port);
        
        // Convert hostname to IP
        hostent* host_entry = gethostbyname(server_host.c_str());
        if (host_entry) {
            server_addr.sin_addr = *((in_addr*)host_entry->h_addr_list[0]);
        } else {
            server_addr.sin_addr.s_addr = inet_addr(server_host.c_str());
        }
        
        std::string message = "PING from notification client";
        int result = sendto(udp_socket, message.c_str(), (int)message.length(), 0,
                           (sockaddr*)&server_addr, sizeof(server_addr));
        
        if (result == SOCKET_ERROR) {
            std::cerr << "Send failed. Error: " << WSAGetLastError() << std::endl;
        } else {
            std::cout << "Test message sent: " << message << std::endl;
        }
    }
};

// Console control handler
BOOL WINAPI ConsoleHandler(DWORD dwType) {
    switch (dwType) {
        case CTRL_C_EVENT:
        case CTRL_CLOSE_EVENT:
        case CTRL_BREAK_EVENT:
            std::cout << "\nShutting down notification client..." << std::endl;
            return TRUE;
        default:
            return FALSE;
    }
}

int main(int argc, char* argv[]) {
    // Set console title
    SetConsoleTitleA("Simple Notification Client Demo");
    
    // Set console control handler
    SetConsoleCtrlHandler(ConsoleHandler, TRUE);
    
    std::cout << "========================================" << std::endl;
    std::cout << "Simple High Performance Notification Client" << std::endl;
    std::cout << "Version: 1.0.0 Demo" << std::endl;
    std::cout << "Protocol: UDP" << std::endl;
    std::cout << "========================================" << std::endl;
    
    // Parse command line arguments
    std::string host = "localhost";
    int port = 8080;
    
    for (int i = 1; i < argc; ++i) {
        std::string arg = argv[i];
        if (arg == "--host" && i + 1 < argc) {
            host = argv[++i];
        } else if (arg == "--port" && i + 1 < argc) {
            port = std::atoi(argv[++i]);
        } else if (arg == "--help") {
            std::cout << "Usage: " << argv[0] << " [options]" << std::endl;
            std::cout << "Options:" << std::endl;
            std::cout << "  --host <hostname>    Server hostname (default: localhost)" << std::endl;
            std::cout << "  --port <port>        Server port (default: 8080)" << std::endl;
            std::cout << "  --help               Show this help message" << std::endl;
            return 0;
        }
    }
    
    try {
        SimpleNotificationClient client;
        
        if (!client.initialize(host, port)) {
            std::cerr << "Failed to initialize client" << std::endl;
            return 1;
        }
        
        if (!client.start()) {
            std::cerr << "Failed to start client" << std::endl;
            return 1;
        }
        
        std::cout << "Client started successfully. Press Ctrl+C to exit..." << std::endl;
        std::cout << "Sending test messages every 5 seconds..." << std::endl;
        
        // Main loop
        auto last_ping = std::chrono::steady_clock::now();
        while (client.isRunning()) {
            auto now = std::chrono::steady_clock::now();
            auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(now - last_ping);
            
            if (elapsed.count() >= 5) {
                client.sendTestMessage();
                last_ping = now;
            }
            
            Sleep(100);
        }
        
    } catch (const std::exception& e) {
        std::cerr << "Exception: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << "Unknown exception occurred" << std::endl;
        return 1;
    }
    
    std::cout << "Application exited" << std::endl;
    return 0;
}