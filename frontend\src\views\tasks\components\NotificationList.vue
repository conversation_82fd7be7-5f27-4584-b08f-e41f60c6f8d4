<!-- File: frontend/src/views/tasks/components/NotificationList.vue -->
<!-- Description: 通知列表组件，用于显示通知项目 -->

<template>
  <div class="notification-list">
    <div v-if="notifications.length === 0" class="empty-state">
      <el-empty description="暂无通知" />
    </div>

    <div
      v-else
      v-for="notification in notifications"
      :key="notification.id || notification.notificationId"
      class="notification-item"
      :class="{ 'unread': !notification.isRead && !notification.read }"
      @click="handleClick(notification)"
    >
      <div class="notification-icon">
        <el-icon><component :is="getIconForType(notification.type)" /></el-icon>
      </div>
      <div class="notification-content">
        <div class="notification-title">{{ notification.title || '无标题' }}</div>
        <div class="notification-message">{{ notification.content || notification.message || '无内容' }}</div>
        <div class="notification-time">{{ formatTime(notification.timestamp || notification.creationTimestamp || notification.createdAt) }}</div>
      </div>
      <div class="notification-actions">
        <el-button
          v-if="!(notification.isRead || notification.read)"
          type="primary"
          link
          size="small"
          @click.stop="handleMarkRead(notification.id || notification.notificationId)"
        >
          标为已读
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { Bell, ChatDotRound, Document, User } from '@element-plus/icons-vue'

const props = defineProps({
  notifications: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['read', 'click'])

// 根据通知类型获取图标
const getIconForType = (type) => {
  switch (type) {
    case 'TaskAssigned':
    case 'TaskStatusChanged':
    case 'TaskContentChanged':
      return Document
    case 'TaskComment':
    case 'TaskAttachmentAdded':
      return ChatDotRound
    case 'TaskMention':
      return User
    case 'TaskOverdue':
      return 'Timer'
    default:
      return Bell
  }
}

// 格式化时间显示
const formatTime = (dateString) => {
  if (!dateString) return ''
  
  const date = new Date(dateString)
  const now = new Date()
  const diffMinutes = Math.floor((now - date) / (1000 * 60))
  
  if (diffMinutes < 1) return '刚刚'
  if (diffMinutes < 60) return `${diffMinutes}分钟前`
  if (diffMinutes < 1440) return `${Math.floor(diffMinutes / 60)}小时前`
  if (diffMinutes < 10080) return `${Math.floor(diffMinutes / 1440)}天前`
  
  return date.toLocaleDateString('zh-CN')
}

// 点击标记已读按钮
const handleMarkRead = (id) => {
  emit('read', id)
}

// 点击通知项
const handleClick = (notification) => {
  emit('click', notification)
}
</script>

<style scoped>
.notification-list {
  max-height: 350px;
  overflow-y: auto;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px 0;
}

.notification-item {
  display: flex;
  padding: 10px;
  border-bottom: 1px solid #ebeef5;
  cursor: pointer;
  transition: background-color 0.2s;
}

.notification-item:hover {
  background-color: #f5f7fa;
}

.notification-item.unread {
  background-color: #ecf5ff;
}

.notification-item.unread:hover {
  background-color: #e3f0ff;
}

.notification-icon {
  margin-right: 12px;
  color: #409eff;
  font-size: 18px;
  display: flex;
  align-items: flex-start;
  padding-top: 2px;
}

.notification-content {
  flex: 1;
}

.notification-title {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 4px;
  color: #303133;
}

.notification-item.unread .notification-title {
  font-weight: 600;
}

.notification-message {
  font-size: 13px;
  color: #606266;
  margin-bottom: 4px;
  line-height: 1.4;
  word-break: break-word;
}

.notification-time {
  font-size: 12px;
  color: #909399;
}

.notification-actions {
  display: flex;
  align-items: flex-start;
  margin-left: 8px;
}
</style> 