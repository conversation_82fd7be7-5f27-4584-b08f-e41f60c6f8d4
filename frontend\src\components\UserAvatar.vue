<template>
  <div class="user-avatar" :class="[sizeClass, { 'show-name': showName }]">
    <div class="avatar-container">
      <el-avatar 
        :size="avatarSize" 
        :src="fullAvatarUrl" 
        :alt="userName"
        class="avatar"
        @error="handleAvatarError"
      >
        <span class="avatar-text">{{ getInitials(userName) }}</span>
      </el-avatar>
    </div>
    <span v-if="showName" class="user-name">{{ userName }}</span>
  </div>
</template>

<script>
import { computed, onMounted, ref } from 'vue'

export default {
  name: 'UserAvatar',

  props: {
    user: {
      type: Object,
      default: () => ({})
    },
    userId: {
      type: [Number, String],
      default: null
    },
    userName: {
      type: String,
      default: ''
    },
    avatarUrl: {
      type: String,
      default: ''
    },
    size: {
      type: String,
      default: 'default',
      validator: value => ['mini', 'small', 'default', 'medium', 'large'].includes(value)
    },
    showName: {
      type: Boolean,
      default: false
    }
  },

  setup(props) {
    const sizeClass = computed(() => `size-${props.size}`)
    const hasErrorLoading = ref(false)
    
    const avatarSize = computed(() => {
      const sizeMap = {
        mini: 24,
        small: 32,
        default: 40,
        medium: 56,
        large: 72
      }
      return sizeMap[props.size] || 40
    })
    
    // 计算用户名，优先使用传入的userName，然后从user对象中获取
    const userName = computed(() => {
      if (props.userName) return props.userName
      
      const user = props.user || {}
      return user.name || user.userName || user.username || '未知用户'
    })

    // 统一补全头像URL
    const fullAvatarUrl = computed(() => {
      // 如果之前加载有错误，直接返回空，使用文字头像
      if (hasErrorLoading.value) return ''
      
      // 1. 优先使用直接传入的avatarUrl
      let url = props.avatarUrl || ''
      
      // 2. 如果没有直接的avatarUrl，从user对象获取
      if (!url && props.user) {
        const user = props.user
        url = user.avatarUrl || user.avatar || user.userAvatarUrl || 
              user.userAvatar || user.picture || user.imageUrl || ''
              
        // 检查嵌套结构
        if (!url && user.user) {
          url = user.user.avatarUrl || user.user.avatar || 
                user.user.userAvatarUrl || user.user.picture || ''
        }
      }
      
      // 3. 如果没有值，返回空字符串，将使用文字头像
      if (!url) return ''
      
      // 4. 已经是完整URL的情况
      if (url.startsWith('http')) return url
      
      // 5. 处理相对路径
      let base = import.meta.env.VITE_STATIC_FILES_BASE_URL || ''
      if (base && !base.endsWith('/')) base += '/'
      
      let path = url.startsWith('/') ? url.substring(1) : url
      
      // 输出最终构建的URL用于调试
      const finalUrl = base + path
      console.log(`头像URL构建过程: 原始URL=${url}, base=${base}, 最终URL=${finalUrl}`)
      
      return finalUrl
    })

    const getInitials = (name) => {
      if (!name) return '?'
      
      // 如果是中文名，取最后一个字
      if (/[\u4e00-\u9fa5]/.test(name)) {
        return name.slice(-1)
      }
      
      // 如果是英文名，取首字母
      const words = name.split(' ')
      if (words.length >= 2) {
        return (words[0][0] + words[1][0]).toUpperCase()
      }
      return name[0]?.toUpperCase() || '?'
    }
    
    // 头像加载错误处理
    const handleAvatarError = (e) => {
      console.warn('头像加载失败:', fullAvatarUrl.value)
      hasErrorLoading.value = true // 标记加载失败，后续使用文字头像
    }
    
    // 组件挂载时输出调试信息
    onMounted(() => {
      console.log('UserAvatar组件接收到数据:', {
        userId: props.userId || (props.user?.id || props.user?.userId),
        userName: userName.value,
        avatarUrl: props.avatarUrl,
        userObject: JSON.stringify(props.user),
        finalAvatarUrl: fullAvatarUrl.value
      })
    })

    return {
      sizeClass,
      avatarSize,
      userName,
      getInitials,
      fullAvatarUrl,
      handleAvatarError
    }
  }
}
</script>

<style scoped>
.user-avatar {
  display: inline-flex;
  align-items: center;
  gap: 6px;
}

.user-avatar.show-name {
  gap: 8px;
}

.avatar-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-weight: 600;
}

.avatar-text {
  font-size: 14px;
}

.user-name {
  font-size: 13px;
  color: #303133;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 80px;
}

/* 尺寸样式 */
.user-avatar.size-mini .avatar-text {
  font-size: 10px;
}

.user-avatar.size-mini .user-name {
  font-size: 11px;
  max-width: 60px;
}

.user-avatar.size-small .avatar-text {
  font-size: 12px;
}

.user-avatar.size-small .user-name {
  font-size: 12px;
  max-width: 70px;
}

.user-avatar.size-medium .avatar-text {
  font-size: 18px;
}

.user-avatar.size-medium .user-name {
  font-size: 14px;
  max-width: 100px;
}

.user-avatar.size-large .avatar-text {
  font-size: 24px;
}

.user-avatar.size-large .user-name {
  font-size: 16px;
  max-width: 120px;
}
</style>