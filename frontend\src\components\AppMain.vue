<template>
  <section class="app-main">
    <router-view v-slot="{ Component }">
      <transition name="fade-transform" mode="out-in">
        <keep-alive :include="cachedViews">
          <component :is="Component" :key="key" />
        </keep-alive>
      </transition>
    </router-view>
  </section>
</template>

<script>
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import { useStore } from 'vuex'

export default {
  name: 'AppMain',
  setup() {
    const route = useRoute()
    const store = useStore()
    
    const cachedViews = computed(() => {
      return store.state.tagsView?.cachedViews || []
    })
    
    const key = computed(() => {
      return route.path
    })
    
    return {
      cachedViews,
      key
    }
  }
}
</script>

<style lang="scss" scoped>
.app-main {
  padding: 20px;
  overflow: hidden;
  position: relative;
  min-height: calc(100vh - 50px);
}

.fixed-header + .app-main {
  padding-top: 50px;
}

.fade-transform-enter-active,
.fade-transform-leave-active {
  transition: all 0.5s;
}

.fade-transform-enter-from {
  opacity: 0;
  transform: translateX(-30px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(30px);
}
</style> 