/**
 * 航空航天级IT资产管理系统 - 全局指令注册工具
 * 文件路径: src/utils/globalDirectives.js
 * 功能描述: 注册并管理全局自定义指令
 */

import { useUserStore } from '@/stores/modules/user'

/**
 * 设置全局自定义指令
 * @param {Object} app - Vue应用实例
 */
export function setupGlobalDirectives(app) {
  // 权限控制指令 v-permission
  app.directive('permission', {
    mounted(el, binding) {
      const userStore = useUserStore()
      const { value } = binding
      const permissions = userStore.permissions

      if (value && value instanceof Array && value.length > 0) {
        const hasPermission = permissions.some(permission => 
          value.includes(permission)
        )
        
        if (!hasPermission) {
          el.parentNode?.removeChild(el)
        }
      } else {
        throw new Error('需要权限！如 v-permission="[\'admin\',\'editor\']"')
      }
    }
  })

  // 点击外部区域指令 v-click-outside
  app.directive('click-outside', {
    mounted(el, binding) {
      el._clickOutside = (event) => {
        if (!(el === event.target || el.contains(event.target))) {
          binding.value(event)
        }
      }
      document.body.addEventListener('click', el._clickOutside)
    },
    unmounted(el) {
      document.body.removeEventListener('click', el._clickOutside)
    }
  })

  // 长按指令 v-long-press
  app.directive('long-press', {
    mounted(el, binding) {
      if (typeof binding.value !== 'function') {
        throw new Error('回调必须是一个函数')
      }
      
      let pressTimer = null
      
      const start = (e) => {
        if (e.button === 0) {
          pressTimer = setTimeout(() => {
            binding.value(e)
          }, 1000)
        }
      }
      
      const cancel = () => {
        if (pressTimer) {
          clearTimeout(pressTimer)
          pressTimer = null
        }
      }
      
      el.addEventListener('mousedown', start, { passive: true })
      el.addEventListener('touchstart', start, { passive: true })
      el.addEventListener('mouseout', cancel, { passive: true })
      el.addEventListener('touchend', cancel, { passive: true })
      el.addEventListener('touchcancel', cancel, { passive: true })
    }
  })
} 