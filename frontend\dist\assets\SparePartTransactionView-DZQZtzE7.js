import{_ as e,r as a,ad as t,c as l,z as r,E as o,b as d,d as p,l as n,e as s,w as i,m as u,k as m,a as c,o as g,af as v,p as y,t as b,i as h}from"./index-CG5lHOPO.js";import{getSparePartTransactions as w}from"./spareparts-DKUrs8IX.js";const f={class:"spare-part-transaction-view"},D={class:"filter-container"},_={class:"pagination-container"},T=e({__name:"SparePartTransactionView",setup(e){const T=a(!1),V=a([]),C=a(0),z=a([]),N=t({pageIndex:1,pageSize:10,partName:"",partCode:"",type:"",reasonType:"",startDate:"",endDate:"",sortBy:"",sortOrder:""}),x=[{text:"最近一周",value:()=>{const e=new Date,a=new Date;return a.setTime(a.getTime()-6048e5),[a,e]}},{text:"最近一个月",value:()=>{const e=new Date,a=new Date;return a.setTime(a.getTime()-2592e6),[a,e]}},{text:"最近三个月",value:()=>{const e=new Date,a=new Date;return a.setTime(a.getTime()-7776e6),[a,e]}}];l((()=>(z.value&&2===z.value.length?(N.startDate=z.value[0],N.endDate=z.value[1]):(N.startDate="",N.endDate=""),z.value))),r((()=>{S()}));const S=async()=>{T.value=!0;try{const e={pageIndex:N.pageIndex,pageSize:N.pageSize,partName:N.partName||void 0,partCode:N.partCode||void 0,type:N.type||void 0,reasonType:N.reasonType||void 0,startDate:N.startDate||void 0,endDate:N.endDate||void 0,sortBy:N.sortBy||void 0,sortOrder:N.sortOrder||void 0},a=await w(e);a.success?(V.value=a.data.items,C.value=a.data.totalCount):o.error(a.message||"获取出入库记录失败")}catch(e){o.error("获取出入库记录失败，请稍后重试")}finally{T.value=!1}},I=e=>{if(!e)return"";return new Date(e).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",hour12:!1})},U=()=>{N.pageIndex=1,S()},k=()=>{N.partName="",N.partCode="",N.type="",N.reasonType="",z.value=[],N.startDate="",N.endDate="",N.sortBy="",N.sortOrder="",U()},B=e=>{e.prop&&e.order?(N.sortBy=e.prop,N.sortOrder="ascending"===e.order?"asc":"desc"):(N.sortBy="",N.sortOrder=""),S()},O=e=>{N.pageSize=e,S()},q=e=>{N.pageIndex=e,S()};return(e,a)=>{const t=c("el-input"),l=c("el-form-item"),r=c("el-option"),o=c("el-select"),w=c("el-date-picker"),S=c("el-button"),P=c("el-form"),Y=c("el-table-column"),j=c("el-tag"),M=c("el-table"),K=c("el-pagination"),A=u("loading");return g(),d("div",f,[a[9]||(a[9]=p("div",{class:"page-header"},[p("h2",null,"出入库记录")],-1)),p("div",D,[s(P,{inline:!0,model:N,class:"demo-form-inline"},{default:i((()=>[s(l,{label:"备件名称"},{default:i((()=>[s(t,{modelValue:N.partName,"onUpdate:modelValue":a[0]||(a[0]=e=>N.partName=e),placeholder:"备件名称",clearable:"",onKeyup:v(U,["enter"])},null,8,["modelValue"])])),_:1}),s(l,{label:"备件编号"},{default:i((()=>[s(t,{modelValue:N.partCode,"onUpdate:modelValue":a[1]||(a[1]=e=>N.partCode=e),placeholder:"备件编号",clearable:"",onKeyup:v(U,["enter"])},null,8,["modelValue"])])),_:1}),s(l,{label:"操作类型"},{default:i((()=>[s(o,{modelValue:N.type,"onUpdate:modelValue":a[2]||(a[2]=e=>N.type=e),placeholder:"操作类型",clearable:""},{default:i((()=>[s(r,{label:"入库",value:1}),s(r,{label:"出库",value:2})])),_:1},8,["modelValue"])])),_:1}),s(l,{label:"原因类型"},{default:i((()=>[s(o,{modelValue:N.reasonType,"onUpdate:modelValue":a[3]||(a[3]=e=>N.reasonType=e),placeholder:"原因类型",clearable:""},{default:i((()=>[s(r,{label:"采购入库",value:1}),s(r,{label:"退回入库",value:2}),s(r,{label:"领用出库",value:3}),s(r,{label:"报废出库",value:4}),s(r,{label:"盘点调整",value:5})])),_:1},8,["modelValue"])])),_:1}),s(l,{label:"操作时间"},{default:i((()=>[s(w,{modelValue:z.value,"onUpdate:modelValue":a[4]||(a[4]=e=>z.value=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"YYYY-MM-DD",shortcuts:x},null,8,["modelValue"])])),_:1}),s(l,null,{default:i((()=>[s(S,{type:"primary",onClick:U},{default:i((()=>a[7]||(a[7]=[y("查询")]))),_:1}),s(S,{onClick:k},{default:i((()=>a[8]||(a[8]=[y("重置")]))),_:1})])),_:1})])),_:1},8,["model"])]),n((g(),m(M,{data:V.value,border:"",style:{width:"100%"},onSortChange:B},{default:i((()=>[s(Y,{prop:"id",label:"记录ID",width:"80",sortable:"custom"}),s(Y,{prop:"partCode",label:"备件编号",width:"120",sortable:"custom"}),s(Y,{prop:"partName",label:"备件名称",width:"150",sortable:"custom"}),s(Y,{prop:"typeName",label:"操作类型",width:"100"},{default:i((e=>[s(j,{type:1===e.row.type?"success":"warning"},{default:i((()=>[y(b(e.row.typeName),1)])),_:2},1032,["type"])])),_:1}),s(Y,{prop:"quantity",label:"数量",width:"100"},{default:i((e=>[p("span",{class:h(1===e.row.type?"quantity-in":"quantity-out")},b(1===e.row.type?"+":"-")+b(Math.abs(e.row.quantity)),3)])),_:1}),s(Y,{prop:"stockAfter",label:"操作后库存",width:"120"}),s(Y,{prop:"reasonTypeName",label:"原因类型",width:"120"}),s(Y,{prop:"locationName",label:"库位",width:"120"}),s(Y,{prop:"operationTime",label:"操作时间",width:"180",sortable:"custom"},{default:i((e=>[y(b(I(e.row.operationTime)),1)])),_:1}),s(Y,{prop:"operatorName",label:"操作人",width:"120"}),s(Y,{prop:"referenceNumber",label:"关联单号",width:"150"}),s(Y,{prop:"remarks",label:"备注","min-width":"200","show-overflow-tooltip":""})])),_:1},8,["data"])),[[A,T.value]]),p("div",_,[s(K,{currentPage:N.pageIndex,"onUpdate:currentPage":a[5]||(a[5]=e=>N.pageIndex=e),"page-size":N.pageSize,"onUpdate:pageSize":a[6]||(a[6]=e=>N.pageSize=e),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:C.value,onSizeChange:O,onCurrentChange:q},null,8,["currentPage","page-size","total"])])])}}},[["__scopeId","data-v-6cd46764"]]);export{T as default};
