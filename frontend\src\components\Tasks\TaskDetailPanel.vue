<template>
  <div class="task-detail-panel">
    <el-descriptions :column="2" border size="medium">
      <template #title>
        <div class="task-title">
          <div class="title-text">{{ task.title }}</div>
          <el-tag :type="getTaskStatusTag(task.status)" size="small">
            {{ getTaskStatusLabel(task.status) }}
          </el-tag>
        </div>
      </template>
      
      <el-descriptions-item label="任务编号">
        <span>{{ task.taskCode }}</span>
      </el-descriptions-item>
      
      <el-descriptions-item label="任务类型">
        <el-tag size="small" :type="getTaskTypeColor(task.taskType)">
          {{ getTaskTypeName(task.taskType) }}
        </el-tag>
      </el-descriptions-item>
      
      <el-descriptions-item label="创建时间">
        <span>{{ formatDate(task.createdAt) }}</span>
      </el-descriptions-item>
      
      <el-descriptions-item label="截止时间">
        <span :class="{ 'text-danger': task.isOverdue }">
          {{ formatDate(task.dueTime) }}
          <el-tag v-if="task.isOverdue" type="danger" size="small">逾期</el-tag>
        </span>
      </el-descriptions-item>
      
      <el-descriptions-item label="优先级">
        <el-tag size="small" :type="getPriorityColor(task.priority)">
          {{ getPriorityName(task.priority) }}
        </el-tag>
      </el-descriptions-item>
      
      <el-descriptions-item label="执行人">
        <div v-if="getAllAssignees(task).length === 0">
          <span>未分配</span>
        </div>
        <UserAvatarStack
          v-else
          :users="getAllAssignees(task)"
          :is-main-user-primary="true"
          :max-users="6"
          size="medium"
          :show-names="true"
        />
      </el-descriptions-item>
      
      <el-descriptions-item label="任务积分">
        <span class="task-points">
          <el-icon><Star /></el-icon> +{{ task.points }}
        </span>
      </el-descriptions-item>
      
      <el-descriptions-item label="关联资产">
        <div v-if="task.relatedAssets && task.relatedAssets.length">
          <el-tag
            v-for="asset in task.relatedAssets"
            :key="asset.id"
            size="small"
            class="asset-tag"
          >
            {{ asset.name }}
          </el-tag>
        </div>
        <span v-else>无关联资产</span>
      </el-descriptions-item>
      
      <el-descriptions-item label="任务进度" :span="2">
        <el-progress
          :percentage="task.progress"
          :status="getProgressStatus(task)"
          :stroke-width="15"
        />
      </el-descriptions-item>
      
      <el-descriptions-item label="任务描述" :span="2">
        <div class="task-description" v-html="formatDescription(task.description)"></div>
      </el-descriptions-item>
    </el-descriptions>
    
    <!-- 任务进度记录 -->
    <div class="progress-records" v-if="progressRecords.length">
      <h3 class="section-title">进度记录</h3>
      <el-timeline>
        <el-timeline-item
          v-for="record in progressRecords"
          :key="record.id"
          :timestamp="formatDateTime(record.createdAt)"
          :type="getTimelineItemType(record)"
        >
          <div class="record-content">
            <div class="record-header">
              <span class="record-user">{{ record.userName }}</span>
              <span class="record-progress">进度: {{ record.oldProgress }}% → {{ record.newProgress }}%</span>
            </div>
            <div class="record-remark" v-if="record.remark">{{ record.remark }}</div>
          </div>
        </el-timeline-item>
      </el-timeline>
    </div>
    
    <!-- 任务附件 -->
    <div class="task-attachments" v-if="taskAttachments.length">
      <h3 class="section-title">附件</h3>
      <div class="attachment-list">
        <div
          v-for="attachment in taskAttachments"
          :key="attachment.id"
          class="attachment-item"
        >
          <el-icon><Document /></el-icon>
          <span class="attachment-name">{{ attachment.fileName }}</span>
          <span class="attachment-size">{{ formatFileSize(attachment.fileSize) }}</span>
          <div class="attachment-actions">
            <el-button type="primary" link @click="downloadAttachment(attachment)">
              下载
            </el-button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 任务操作 -->
    <div class="task-actions">
      <el-divider />
      <div class="action-buttons">
        <el-button v-if="canClaim" type="success" @click="claimTask">
          认领任务
        </el-button>
        <el-button v-if="canUpdateProgress" type="primary" @click="showUpdateProgress">
          更新进度
        </el-button>
        <el-button v-if="canComplete" type="success" @click="completeTask">
          完成任务
        </el-button>
        <el-button v-if="canCancel" type="danger" @click="cancelTask">
          取消任务
        </el-button>
        <el-button type="primary" @click="editTask">
          编辑任务
        </el-button>
      </div>
    </div>
    
    <!-- 更新进度对话框 -->
    <el-dialog
      title="更新任务进度"
      v-model="updateProgressVisible"
      width="500px"
    >
      <el-form :model="progressForm" label-width="100px">
        <el-form-item label="当前进度">
          <el-progress
            :percentage="task.progress"
            :stroke-width="15"
          />
        </el-form-item>
        
        <el-form-item label="新进度">
          <el-slider
            v-model="progressForm.progress"
            :min="0"
            :max="100"
            :step="5"
            show-stops
          />
        </el-form-item>
        
        <el-form-item label="进度说明">
          <el-input
            v-model="progressForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入进度更新说明"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="updateProgressVisible = false">
          取消
        </el-button>
        <el-button type="primary" @click="submitProgress">
          确认更新
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { formatDate, formatDateTime, formatFileSize } from '@/utils/format'
import { taskApi } from '@/api/task'
import { Star, Document } from '@element-plus/icons-vue'
import UserAvatarStack from '@/components/UserAvatarStack.vue'

const props = defineProps({
  task: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update'])

// 任务进度记录
const progressRecords = ref([])
// 任务附件
const taskAttachments = ref([])
// 更新进度对话框
const updateProgressVisible = ref(false)
const progressForm = ref({
  progress: 0,
  remark: ''
})

// 计算属性：各种操作权限
const canClaim = computed(() => 
  props.task.status === 'pending' && !props.task.isClaimed
)
const canUpdateProgress = computed(() => 
  ['pending', 'inProgress'].includes(props.task.status)
)
const canComplete = computed(() => 
  ['pending', 'inProgress'].includes(props.task.status)
)
const canCancel = computed(() => 
  ['pending', 'inProgress'].includes(props.task.status)
)

// 获取任务进度记录
const fetchProgressRecords = async () => {
  try {
    const res = await taskApi.getTaskProgressRecords(props.task.id)
    if (res.success) {
      progressRecords.value = res.data || []
    }
  } catch (error) {
    console.error('获取任务进度记录失败', error)
  }
}

// 获取任务附件
const fetchTaskAttachments = async () => {
  try {
    const res = await taskApi.getTaskAttachments(props.task.id)
    if (res.success) {
      taskAttachments.value = res.data || []
    }
  } catch (error) {
    console.error('获取任务附件失败', error)
  }
}

// 认领任务
const claimTask = async () => {
  try {
    const confirmed = await ElMessageBox.confirm(
      `确认认领任务"${props.task.title}"？`,
      '认领任务',
      {
        confirmButtonText: '确认认领',
        cancelButtonText: '取消',
        type: 'info'
      }
    )
    
    if (confirmed) {
      const res = await taskApi.claimTask(props.task.id)
      if (res.success) {
        ElMessage.success('任务认领成功')
        emit('update')
      } else {
        ElMessage.error(res.message || '认领任务失败')
      }
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('认领任务失败', error)
      ElMessage.error('认领任务失败')
    }
  }
}

// 显示更新进度对话框
const showUpdateProgress = () => {
  progressForm.value.progress = props.task.progress
  progressForm.value.remark = ''
  updateProgressVisible.value = true
}

// 提交进度更新
const submitProgress = async () => {
  try {
    const res = await taskApi.updateTaskProgress(
      props.task.id,
      progressForm.value.progress,
      progressForm.value.remark
    )
    
    if (res.success) {
      ElMessage.success('进度更新成功')
      updateProgressVisible.value = false
      emit('update')
    } else {
      ElMessage.error(res.message || '更新进度失败')
    }
  } catch (error) {
    console.error('更新进度失败', error)
    ElMessage.error('更新进度失败')
  }
}

// 完成任务
const completeTask = async () => {
  try {
    const confirmed = await ElMessageBox.confirm(
      `确认将任务"${props.task.title}"标记为已完成？`,
      '完成任务',
      {
        confirmButtonText: '确认完成',
        cancelButtonText: '取消',
        type: 'success'
      }
    )
    
    if (confirmed) {
      const res = await taskApi.completeTask(props.task.id)
      if (res.success) {
        ElMessage.success('任务已标记为完成')
        emit('update')
      } else {
        ElMessage.error(res.message || '完成任务失败')
      }
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('完成任务失败', error)
      ElMessage.error('完成任务失败')
    }
  }
}

// 取消任务
const cancelTask = async () => {
  try {
    const confirmed = await ElMessageBox.confirm(
      `确认取消任务"${props.task.title}"？`,
      '取消任务',
      {
        confirmButtonText: '确认取消',
        cancelButtonText: '返回',
        type: 'warning'
      }
    )
    
    if (confirmed) {
      const res = await taskApi.cancelTask(props.task.id)
      if (res.success) {
        ElMessage.success('任务已取消')
        emit('update')
      } else {
        ElMessage.error(res.message || '取消任务失败')
      }
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('取消任务失败', error)
      ElMessage.error('取消任务失败')
    }
  }
}

// 编辑任务
const editTask = () => {
  ElMessage.info('任务编辑功能即将上线')
}

// 下载附件
const downloadAttachment = (attachment) => {
  ElMessage.info(`正在下载文件: ${attachment.fileName}`)
  // 实际项目中应实现文件下载逻辑
}

// 格式化任务描述
const formatDescription = (description) => {
  if (!description) return '暂无描述'
  // 简单的换行处理
  return description.replace(/\n/g, '<br>')
}

// 获取任务类型名称
const getTaskTypeName = (type) => {
  const types = {
    1: '日常任务',
    2: '周期任务',
    3: 'PDCA任务'
  }
  return types[type] || '未知类型'
}

// 获取任务类型颜色
const getTaskTypeColor = (type) => {
  const colors = {
    1: '',        // 日常任务 - 默认颜色
    2: 'warning', // 周期任务 - 橙色
    3: 'success'  // PDCA任务 - 绿色
  }
  return colors[type] || ''
}

// 获取优先级名称
const getPriorityName = (priority) => {
  const priorities = {
    1: '低',
    2: '中',
    3: '高',
    4: '紧急'
  }
  return priorities[priority] || '未知'
}

// 获取优先级颜色
const getPriorityColor = (priority) => {
  const colors = {
    1: 'info',    // 低 - 灰色
    2: '',        // 中 - 默认颜色
    3: 'warning', // 高 - 橙色
    4: 'danger'   // 紧急 - 红色
  }
  return colors[priority] || ''
}

// 获取任务状态标签类型
const getTaskStatusTag = (status) => {
  const map = {
    'pending': 'info',
    'inProgress': 'warning',
    'completed': 'success',
    'overdue': 'danger',
    'cancelled': 'danger'
  }
  return map[status] || ''
}

// 获取任务状态文本
const getTaskStatusLabel = (status) => {
  const map = {
    'pending': '待处理',
    'inProgress': '处理中',
    'completed': '已完成',
    'overdue': '已逾期',
    'cancelled': '已取消'
  }
  return map[status] || '未知'
}

// 获取进度条状态
const getProgressStatus = (task) => {
  if (task.status === 'completed') return 'success'
  if (task.isOverdue) return 'exception'
  return ''
}

// 获取时间线项目类型
const getTimelineItemType = (record) => {
  if (record.newProgress >= 100) return 'success'
  if (record.newProgress >= 50) return 'warning'
  return 'primary'
}

// 获取所有负责人（主负责人 + 协作人员）- 使用清晰的逻辑
const getAllAssignees = (task) => {
  if (!task) return []
  
  console.log('TaskDetailPanel - 原始task数据:', {
    id: task.id,
    assigneeUserId: task.assigneeUserId,
    assigneeName: task.assigneeName,
    assignees: task.assignees
  })
  
  const assignees = []
  
  // 1. 添加主负责人 (来自tasks表的AssigneeUserId)
  if (task.assigneeUserId) {
    assignees.push({
      id: task.assigneeUserId,
      name: task.assigneeName || '未知用户',
      avatarUrl: task.assigneeAvatarUrl || '',
      role: 'Primary',
      isPrimary: true
    })
  }
  
  // 2. 添加协作者 (来自taskassignees表)
  if (task.assignees && Array.isArray(task.assignees) && task.assignees.length > 0) {
    task.assignees.forEach(assignee => {
      // 防止重复添加主负责人
      if (assignee.userId !== task.assigneeUserId) {
        assignees.push({
          id: assignee.userId,
          name: assignee.userName || '未知用户',
          avatarUrl: assignee.avatarUrl || '',
          role: 'Collaborator',
          isPrimary: false
        })
      }
    })
  }
  
  console.log('TaskDetailPanel - 最终assignees结果:', assignees)
  return assignees
}

// 初始化
onMounted(() => {
  fetchProgressRecords()
  fetchTaskAttachments()
})
</script>

<style lang="scss" scoped>
.task-detail-panel {
  padding: 10px;
  
  .task-title {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 10px;
    
    .title-text {
      font-size: 18px;
      font-weight: 600;
    }
  }
  
  .text-danger {
    color: #F56C6C;
  }
  
  .task-points {
    display: flex;
    align-items: center;
    color: #F7BA2A;
    font-weight: bold;
    
    .el-icon {
      margin-right: 4px;
    }
  }
  
  .task-description {
    line-height: 1.6;
    word-break: break-word;
  }
  
  .asset-tag {
    margin-right: 5px;
    margin-bottom: 5px;
  }
  
  .section-title {
    font-size: 16px;
    font-weight: 600;
    margin: 20px 0 10px;
    color: #303133;
  }
  
  .progress-records {
    margin-top: 20px;
    
    .record-content {
      padding: 12px;
      background: #f5f7fa;
      border-radius: 4px;
      
      .record-header {
        display: flex;
        justify-content: space-between;
        margin-bottom: 6px;
        
        .record-user {
          font-weight: 500;
        }
        
        .record-progress {
          color: #67C23A;
          font-weight: 500;
        }
      }
      
      .record-remark {
        color: #606266;
        font-size: 14px;
      }
    }
  }
  
  .task-attachments {
    margin-top: 20px;
    
    .attachment-list {
      .attachment-item {
        display: flex;
        align-items: center;
        padding: 10px;
        border-bottom: 1px solid #EBEEF5;
        
        &:last-child {
          border-bottom: none;
        }
        
        .el-icon {
          margin-right: 10px;
          font-size: 20px;
          color: #909399;
        }
        
        .attachment-name {
          flex: 1;
          font-weight: 500;
        }
        
        .attachment-size {
          color: #909399;
          margin-right: 20px;
          font-size: 13px;
        }
      }
    }
  }
  
  .task-actions {
    margin-top: 20px;
    
    .action-buttons {
      display: flex;
      justify-content: flex-end;
      gap: 10px;
    }
  }
}
</style> 