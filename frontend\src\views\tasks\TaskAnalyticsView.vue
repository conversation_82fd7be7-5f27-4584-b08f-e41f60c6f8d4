<template>
  <div class="analytics-container">
    <el-row :gutter="20">
      <el-col :span="24">
        <el-card class="stat-card">
          <template #header>
            <div class="card-header">
              <h2>任务数据分析</h2>
              <div class="header-actions">
                <el-date-picker
                  v-model="dateRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  :shortcuts="dateShortcuts"
                  @change="handleDateRangeChange"
                />
                <el-button type="primary" @click="refreshData">
                  <el-icon><Refresh /></el-icon>
                  刷新
                </el-button>
              </div>
            </div>
          </template>
          
          <div v-if="loading" class="loading-container">
            <el-skeleton :rows="10" animated />
          </div>
          
          <div v-else>
            <!-- 统计卡片 -->
            <el-row :gutter="20" class="stat-cards">
              <el-col :xs="24" :sm="12" :md="6">
                <el-card shadow="hover" class="stat-summary-card">
                  <div class="stat-item">
                    <div class="stat-icon blue">
                      <el-icon><Tickets /></el-icon>
                    </div>
                    <div class="stat-info">
                      <div class="stat-title">总任务数</div>
                      <div class="stat-value">{{ statistics.totalTasks }}</div>
                    </div>
                  </div>
                </el-card>
              </el-col>
              
              <el-col :xs="24" :sm="12" :md="6">
                <el-card shadow="hover" class="stat-summary-card">
                  <div class="stat-item">
                    <div class="stat-icon green">
                      <el-icon><Select /></el-icon>
                    </div>
                    <div class="stat-info">
                      <div class="stat-title">已完成任务</div>
                      <div class="stat-value">{{ statistics.completedTasks }}</div>
                      <div class="stat-percent">{{ statistics.completionRate }}%</div>
                    </div>
                  </div>
                </el-card>
              </el-col>
              
              <el-col :xs="24" :sm="12" :md="6">
                <el-card shadow="hover" class="stat-summary-card">
                  <div class="stat-item">
                    <div class="stat-icon yellow">
                      <el-icon><Loading /></el-icon>
                    </div>
                    <div class="stat-info">
                      <div class="stat-title">进行中任务</div>
                      <div class="stat-value">{{ statistics.inProgressTasks }}</div>
                    </div>
                  </div>
                </el-card>
              </el-col>
              
              <el-col :xs="24" :sm="12" :md="6">
                <el-card shadow="hover" class="stat-summary-card">
                  <div class="stat-item">
                    <div class="stat-icon red">
                      <el-icon><AlarmClock /></el-icon>
                    </div>
                    <div class="stat-info">
                      <div class="stat-title">逾期任务</div>
                      <div class="stat-value">{{ statistics.overdueTasks }}</div>
                      <div class="stat-percent">{{ statistics.overdueRate }}%</div>
                    </div>
                  </div>
                </el-card>
              </el-col>
            </el-row>
            
            <!-- 图表区域 -->
            <el-row :gutter="20" class="chart-row">
              <el-col :span="12">
                <el-card shadow="hover" class="chart-card">
                  <template #header>
                    <div class="chart-header">任务状态分布</div>
                  </template>
                  <div class="chart-container" ref="statusChartRef"></div>
                </el-card>
              </el-col>
              
              <el-col :span="12">
                <el-card shadow="hover" class="chart-card">
                  <template #header>
                    <div class="chart-header">任务优先级分布</div>
                  </template>
                  <div class="chart-container" ref="priorityChartRef"></div>
                </el-card>
              </el-col>
            </el-row>
            
            <el-row :gutter="20" class="chart-row">
              <el-col :span="24">
                <el-card shadow="hover" class="chart-card">
                  <template #header>
                    <div class="chart-header">任务趋势</div>
                  </template>
                  <div class="chart-container" ref="trendChartRef"></div>
                </el-card>
              </el-col>
            </el-row>
            
            <el-row :gutter="20" class="chart-row">
              <el-col :span="12">
                <el-card shadow="hover" class="chart-card">
                  <template #header>
                    <div class="chart-header">人员任务分配</div>
                  </template>
                  <div class="chart-container" ref="assigneeChartRef"></div>
                </el-card>
              </el-col>
              
              <el-col :span="12">
                <el-card shadow="hover" class="chart-card">
                  <template #header>
                    <div class="chart-header">任务完成率</div>
                  </template>
                  <div class="chart-container" ref="completionChartRef"></div>
                </el-card>
              </el-col>
            </el-row>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { ref, reactive, onMounted, onBeforeUnmount } from 'vue'
import { ElMessage } from 'element-plus'
import { formatDate } from '@/utils/date'
import { taskApi } from '@/api/task'
import * as echarts from 'echarts/core'
import { PieChart, BarChart, LineChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
} from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'

// 注册必要的组件
echarts.use([
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  PieChart,
  BarChart,
  LineChart,
  CanvasRenderer
])

export default {
  name: 'TaskAnalyticsView',
  
  setup() {
    const loading = ref(true)
    const dateRange = ref([])
    
    // 图表引用
    const statusChartRef = ref(null)
    const priorityChartRef = ref(null)
    const trendChartRef = ref(null)
    const assigneeChartRef = ref(null)
    const completionChartRef = ref(null)
    
    // 图表实例
    let statusChart = null
    let priorityChart = null
    let trendChart = null
    let assigneeChart = null
    let completionChart = null
    
    // 统计数据
    const statistics = reactive({
      totalTasks: 0,
      completedTasks: 0,
      inProgressTasks: 0,
      pendingTasks: 0,
      cancelledTasks: 0,
      overdueTasks: 0,
      completionRate: 0,
      overdueRate: 0
    })
    
    // 图表数据
    const chartData = reactive({
      statusData: [],
      priorityData: [],
      trendData: {
        dates: [],
        created: [],
        completed: []
      },
      assigneeData: [],
      completionData: []
    })
    
    // 日期快捷选项
    const dateShortcuts = [
      {
        text: '最近一周',
        value: () => {
          const end = new Date()
          const start = new Date()
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
          return [start, end]
        },
      },
      {
        text: '最近一个月',
        value: () => {
          const end = new Date()
          const start = new Date()
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
          return [start, end]
        },
      },
      {
        text: '最近三个月',
        value: () => {
          const end = new Date()
          const start = new Date()
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
          return [start, end]
        },
      },
    ]
    
    // 加载数据
    const loadData = async () => {
      loading.value = true
      try {
        const params = {}
        
        // 如果有日期范围，添加到查询参数
        if (dateRange.value && dateRange.value.length === 2) {
          params.startDate = formatDate(dateRange.value[0], 'yyyy-MM-dd')
          params.endDate = formatDate(dateRange.value[1], 'yyyy-MM-dd')
        }
        
        const response = await taskApi.getTasksAnalytics(params)
        if (response.success) {
          // 更新统计数据
          statistics.totalTasks = response.data.totalTasks
          statistics.completedTasks = response.data.completedTasks
          statistics.inProgressTasks = response.data.inProgressTasks
          statistics.pendingTasks = response.data.pendingTasks
          statistics.cancelledTasks = response.data.cancelledTasks
          statistics.overdueTasks = response.data.overdueTasks
          statistics.completionRate = response.data.totalTasks > 0 
            ? Math.round((response.data.completedTasks / response.data.totalTasks) * 100) 
            : 0
          statistics.overdueRate = response.data.totalTasks > 0 
            ? Math.round((response.data.overdueTasks / response.data.totalTasks) * 100) 
            : 0
          
          // 更新图表数据
          chartData.statusData = [
            { value: response.data.pendingTasks, name: '待处理' },
            { value: response.data.inProgressTasks, name: '进行中' },
            { value: response.data.completedTasks, name: '已完成' },
            { value: response.data.cancelledTasks, name: '已取消' }
          ]
          
          chartData.priorityData = [
            { value: response.data.priorityDistribution.low || 0, name: '低' },
            { value: response.data.priorityDistribution.medium || 0, name: '中' },
            { value: response.data.priorityDistribution.high || 0, name: '高' },
            { value: response.data.priorityDistribution.urgent || 0, name: '紧急' }
          ]
          
          chartData.trendData = response.data.taskTrend
          
          chartData.assigneeData = response.data.assigneeDistribution.map(item => ({
            value: item.taskCount,
            name: item.assigneeName || '未分配'
          }))
          
          chartData.completionData = response.data.userCompletionRate.map(item => ({
            value: item.completionRate,
            name: item.userName
          }))
          
          // 更新图表
          updateCharts()
        } else {
          ElMessage.error(response.message || '加载分析数据失败')
        }
      } catch (error) {
        console.error('加载分析数据出错:', error)
        ElMessage.error('加载分析数据时发生错误')
      } finally {
        loading.value = false
      }
    }
    
    // 初始化图表
    const initCharts = () => {
      // 状态分布图表
      statusChart = echarts.init(statusChartRef.value)
      
      // 优先级分布图表
      priorityChart = echarts.init(priorityChartRef.value)
      
      // 任务趋势图表
      trendChart = echarts.init(trendChartRef.value)
      
      // 人员任务分配图表
      assigneeChart = echarts.init(assigneeChartRef.value)
      
      // 任务完成率图表
      completionChart = echarts.init(completionChartRef.value)
      
      // 窗口大小变化时重新调整图表大小
      window.addEventListener('resize', handleResize)
    }
    
    // 更新图表
    const updateCharts = () => {
      // 更新状态分布图表
      statusChart.setOption({
        title: {
          text: '任务状态分布',
          left: 'center'
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          data: ['待处理', '进行中', '已完成', '已取消']
        },
        series: [
          {
            name: '任务状态',
            type: 'pie',
            radius: '60%',
            center: ['50%', '60%'],
            data: chartData.statusData,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            },
            color: ['#909399', '#E6A23C', '#67C23A', '#F56C6C']
          }
        ]
      })
      
      // 更新优先级分布图表
      priorityChart.setOption({
        title: {
          text: '任务优先级分布',
          left: 'center'
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          data: ['低', '中', '高', '紧急']
        },
        series: [
          {
            name: '优先级',
            type: 'pie',
            radius: '60%',
            center: ['50%', '60%'],
            data: chartData.priorityData,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            },
            color: ['#909399', '#67C23A', '#E6A23C', '#F56C6C']
          }
        ]
      })
      
      // 更新任务趋势图表
      trendChart.setOption({
        title: {
          text: '任务趋势',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: ['新建任务', '完成任务'],
          bottom: '0%'
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '10%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: chartData.trendData.dates || []
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '新建任务',
            type: 'line',
            stack: '总量',
            data: chartData.trendData.created || [],
            color: '#409EFF'
          },
          {
            name: '完成任务',
            type: 'line',
            stack: '总量',
            data: chartData.trendData.completed || [],
            color: '#67C23A'
          }
        ]
      })
      
      // 更新人员任务分配图表
      assigneeChart.setOption({
        title: {
          text: '人员任务分配',
          left: 'center'
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          type: 'scroll',
          formatter: function(name) {
            return name.length > 10 ? name.slice(0, 10) + '...' : name;
          }
        },
        series: [
          {
            name: '任务分配',
            type: 'pie',
            radius: '60%',
            center: ['50%', '60%'],
            data: chartData.assigneeData,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      })
      
      // 更新任务完成率图表
      completionChart.setOption({
        title: {
          text: '人员任务完成率',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: '{b}: {c}%'
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '10%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          max: 100,
          axisLabel: {
            formatter: '{value}%'
          }
        },
        yAxis: {
          type: 'category',
          data: chartData.completionData.map(item => item.name),
          axisLabel: {
            formatter: function(value) {
              return value.length > 10 ? value.slice(0, 10) + '...' : value;
            }
          }
        },
        series: [
          {
            name: '完成率',
            type: 'bar',
            data: chartData.completionData.map(item => item.value),
            label: {
              show: true,
              formatter: '{c}%',
              position: 'right'
            },
            itemStyle: {
              color: function(params) {
                const value = params.value;
                if (value >= 80) return '#67C23A';
                if (value >= 60) return '#E6A23C';
                return '#F56C6C';
              }
            }
          }
        ]
      })
    }
    
    // 处理窗口大小变化
    const handleResize = () => {
      statusChart?.resize()
      priorityChart?.resize()
      trendChart?.resize()
      assigneeChart?.resize()
      completionChart?.resize()
    }
    
    // 处理日期范围变更
    const handleDateRangeChange = () => {
      loadData()
    }
    
    // 刷新数据
    const refreshData = () => {
      loadData()
    }
    
    onMounted(() => {
      // 设置默认日期范围为最近一个月
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
      dateRange.value = [start, end]
      
      // 在下一个 tick 初始化图表，确保 DOM 已经渲染
      setTimeout(() => {
        initCharts()
        loadData()
      }, 100)
    })
    
    onBeforeUnmount(() => {
      // 移除窗口大小变化事件监听
      window.removeEventListener('resize', handleResize)
      
      // 销毁图表实例
      statusChart?.dispose()
      priorityChart?.dispose()
      trendChart?.dispose()
      assigneeChart?.dispose()
      completionChart?.dispose()
    })
    
    return {
      loading,
      dateRange,
      dateShortcuts,
      statistics,
      statusChartRef,
      priorityChartRef,
      trendChartRef,
      assigneeChartRef,
      completionChartRef,
      handleDateRangeChange,
      refreshData
    }
  }
}
</script>

<style scoped>
.analytics-container {
  padding: 20px;
}

.stat-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.loading-container {
  padding: 20px;
}

.stat-cards {
  margin-bottom: 20px;
}

.stat-summary-card {
  height: 120px;
  margin-bottom: 20px;
}

.stat-item {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 64px;
  height: 64px;
  border-radius: 50%;
  margin-right: 15px;
  font-size: 24px;
  color: #fff;
}

.stat-icon.blue {
  background-color: #409EFF;
}

.stat-icon.green {
  background-color: #67C23A;
}

.stat-icon.yellow {
  background-color: #E6A23C;
}

.stat-icon.red {
  background-color: #F56C6C;
}

.stat-info {
  flex: 1;
}

.stat-title {
  font-size: 14px;
  color: #909399;
  margin-bottom: 5px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.stat-percent {
  font-size: 14px;
  color: #67C23A;
  margin-top: 5px;
}

.chart-row {
  margin-bottom: 20px;
}

.chart-card {
  margin-bottom: 20px;
}

.chart-header {
  font-size: 16px;
  font-weight: bold;
}

.chart-container {
  height: 300px;
}
</style> 