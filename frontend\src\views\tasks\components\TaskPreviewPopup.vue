// File: frontend/src/views/tasks/components/TaskPreviewPopup.vue
// Description: 任务预览弹出组件，用于鼠标悬停时预览任务内容

<template>
  <div 
    v-if="task" 
    class="task-preview-popup" 
    :style="{ top: `${top}px`, left: `${left}px` }"
  >
    <div class="preview-header">
      <span class="task-id">#{{ task.taskId }}</span>
      <el-tag :type="getStatusTagType(task.status)" size="small" effect="light" round>
        {{ formatStatus(task.status) }}
      </el-tag>
    </div>
    
    <h3 class="task-title">{{ task.name || task.title }}</h3>
    
    <div class="task-info">
      <div class="info-item">
        <el-icon><Calendar /></el-icon>
        <span>{{ formatDate(task.planEndDate) || '未设置截止日期' }}</span>
      </div>
      
      <div v-if="task.assigneeUserId" class="info-item">
        <el-icon><User /></el-icon>
        <span>{{ task.assignee?.name || `用户ID: ${task.assigneeUserId}` }}</span>
      </div>
      
      <div v-if="task.priority" class="info-item">
        <el-icon><Star /></el-icon>
        <span>{{ getPriorityLabel(task.priority) }}</span>
      </div>
    </div>
    
    <div v-if="task.description" class="task-description">
      {{ truncateText(task.description, 150) }}
    </div>
    
    <div class="preview-footer">
      <el-tag v-if="task.taskType" size="small" type="info" effect="plain">
        {{ formatTaskType(task.taskType) }}
      </el-tag>
      
      <div v-if="task.comments?.length" class="comment-count">
        <el-icon><ChatDotRound /></el-icon>
        <span>{{ task.comments.length }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { Calendar, User, Star, ChatDotRound } from '@element-plus/icons-vue'

const props = defineProps({
  task: {
    type: Object,
    default: null
  },
  top: {
    type: Number,
    default: 0
  },
  left: {
    type: Number,
    default: 0
  }
})

// 格式化状态显示文本
const formatStatus = (status) => {
  switch (status?.toLowerCase()) {
    case 'todo': return '未开始'
    case 'inprogress':
    case 'doing': return '进行中'
    case 'done': return '已完成'
    case 'overdue': return '已逾期'
    default: return status || '未知'
  }
}

// 获取状态标签类型
const getStatusTagType = (status) => {
  switch (status?.toLowerCase()) {
    case 'todo': return 'info'
    case 'inprogress':
    case 'doing': return 'warning'
    case 'done': return 'success'
    case 'overdue': return 'danger'
    default: return 'info'
  }
}

// 获取优先级标签
const getPriorityLabel = (priority) => {
  switch (priority?.toLowerCase()) {
    case 'high': return '高优先级'
    case 'medium': return '中优先级'
    case 'low': return '低优先级'
    case 'urgent': return '紧急'
    default: return priority || '未设置'
  }
}

// 格式化任务类型
const formatTaskType = (type) => {
  switch (type?.toLowerCase()) {
    case 'normal': return '普通任务'
    case 'periodic': return '周期任务'
    case 'pdca': return 'PDCA任务'
    default: return type || '未知类型'
  }
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return ''
  try {
    const date = new Date(dateString)
    if (isNaN(date.getTime())) return dateString
    
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    })
  } catch (e) {
    return dateString
  }
}

// 截断文本
const truncateText = (text, maxLength) => {
  if (!text) return ''
  return text.length > maxLength ? text.substring(0, maxLength) + '...' : text
}
</script>

<style lang="scss" scoped>
.task-preview-popup {
  position: fixed;
  z-index: 9999;
  width: 320px;
  background-color: var(--el-bg-color);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 16px;
  pointer-events: none;
  
  .preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    
    .task-id {
      color: var(--el-text-color-secondary);
      font-size: 12px;
    }
  }
  
  .task-title {
    margin: 0 0 12px;
    font-size: 16px;
    line-height: 1.4;
  }
  
  .task-info {
    margin-bottom: 12px;
    
    .info-item {
      display: flex;
      align-items: center;
      margin-bottom: 4px;
      color: var(--el-text-color-regular);
      font-size: 13px;
      
      .el-icon {
        margin-right: 6px;
        font-size: 14px;
      }
    }
  }
  
  .task-description {
    font-size: 13px;
    line-height: 1.5;
    color: var(--el-text-color-regular);
    margin-bottom: 12px;
    white-space: pre-line;
  }
  
  .preview-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .comment-count {
      display: flex;
      align-items: center;
      color: var(--el-text-color-secondary);
      font-size: 12px;
      
      .el-icon {
        margin-right: 4px;
      }
    }
  }
}
</style> 