// File: frontend/src/components/QuickMemo/ThinkingSphereCSS3D.vue
// Description: CSS 3D Thinking Sphere component for displaying Quick Memos.

<template>
  <div ref="sphereContainerRef" class="thinking-sphere-css3d-container">
    <div v-if="isLoadingMemos" class="loading-placeholder">
      <el-icon class="is-loading"><Loading /></el-icon>
      <span>构建思维球...</span>
    </div>
    <div v-else-if="memosToDisplay.length === 0" class="empty-placeholder">
      <el-icon><Notebook /></el-icon>
      <span>思维球空空如也</span>
      <span>点击右下角 + 快速记录</span>
    </div>
    <div v-else ref="sphereElementRef" class="sphere">
      <div
        v-for="(memo, index) in memosToDisplay"
        :key="memo.id"
        class="memo-item"
        :style="[memoStyles[index], getCategoryStyle(memo.categoryId)]"
        @click="handleMemoClick(memo)"
        :title="memo.title"
      >
        {{ memo.title.length > 15 ? memo.title.substring(0, 12) + '...' : memo.title }}
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch, nextTick } from 'vue'
import { useQuickMemoStore } from '@/stores/modules/quickMemo'
import { Loading, Notebook } from '@element-plus/icons-vue' // Import necessary icons

const props = defineProps({
  maxItems: {
    type: Number,
    default: 30 // Max items to display in the sphere
  },
  sphereRadius: {
    type: Number,
    default: 180 // px
  }
});

const quickMemoStore = useQuickMemoStore()
const sphereContainerRef = ref(null)
const sphereElementRef = ref(null)

const isLoadingMemos = computed(() => quickMemoStore.isLoadingQuickMemos)
const memosToDisplay = computed(() => quickMemoStore.quickMemos.slice(0, props.maxItems))
const memoStyles = ref([])

const positionMemoItems = () => {
  const N = memosToDisplay.value.length
  if (N === 0 || !sphereElementRef.value) {
    memoStyles.value = []
    return
  }

  const newStyles = []
  const phi = Math.PI * (Math.sqrt(5) - 1) // Golden angle for Fibonacci sphere

  for (let i = 0; i < N; i++) {
    const y_norm = 1 - (i / (N - 1)) * 2 // y normalized: -1 to 1
    const radiusAtY = Math.sqrt(1 - y_norm * y_norm)
    const theta_angle = phi * i

    // Calculate rotation angles for the item to be positioned on the sphere surface
    const rotY_face = theta_angle // Rotation around Y axis (longitude)
    const rotX_face = Math.acos(y_norm) - Math.PI / 2 // Rotation around X axis (latitude)

    const transform = `translate(-50%, -50%) rotateY(${rotY_face}rad) rotateX(${rotX_face}rad) translateZ(${props.sphereRadius}px)`
    newStyles.push({ transform, opacity: 0.7 + Math.random() * 0.3 })
  }
  memoStyles.value = newStyles
}

const handleMemoClick = (memo) => {
  quickMemoStore.openMemoDrawer('edit', memo)
}

watch(memosToDisplay, () => {
  nextTick(positionMemoItems)
}, { deep: true, immediate: true })

// Also re-calculate on radius change if it becomes a dynamic prop later
watch(() => props.sphereRadius, () => {
    nextTick(positionMemoItems);
});

onMounted(() => {
  if (quickMemoStore.quickMemos.length === 0) {
    quickMemoStore.fetchQuickMemos({ limit: props.maxItems }); // Fetch initial memos
  }
  if (quickMemoStore.categories.length === 0) {
      quickMemoStore.fetchCategories(); // Fetch categories if not already loaded
  }
  // Ensure initial positioning if memos are already in store
  nextTick(positionMemoItems);
})

const getCategoryStyle = (categoryId) => {
  const category = quickMemoStore.getCategoryById(categoryId);
  // Example: if category object has a hex color
  // return category && category.hexColor ? { backgroundColor: category.hexColor, color: '#fff', borderColor: category.hexColor } : {};
  // If using CSS classes from store as before:
  // return {}; // The class is already applied via :class
  // For Cek-smart, let's assume distinct colors for tags
  const colorMap = {
    'work': { backgroundColor: 'rgba(59, 130, 246, 0.8)', color: 'white', borderColor: 'rgba(59, 130, 246,1)'}, // Blue
    'personal': { backgroundColor: 'rgba(245, 158, 11, 0.8)', color: 'white', borderColor: 'rgba(245, 158, 11,1)'}, // Amber
    'study': { backgroundColor: 'rgba(16, 185, 129, 0.8)', color: 'white', borderColor: 'rgba(16, 185, 129,1)'}, // Emerald
    'default': { backgroundColor: 'rgba(108, 117, 125, 0.8)', color: 'white', borderColor: 'rgba(108, 117, 125,1)'}, // Gray
  };
  return (category && colorMap[category.id]) || colorMap['default'];
};

</script>

<style scoped>
.thinking-sphere-css3d-container {
  width: 100%;
  min-height: 300px;
  height: 380px; // Adjusted for better fit
  position: relative;
  perspective: 1000px;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden; // Changed from visible to hidden for cleaner card look
  background-color: var(--card-bg); // Use card background
  border-radius: var(--el-border-radius-base);
}

.sphere {
  position: relative; /* Was absolute in prototype, relative might be better for centering */
  width: 10px; /* Small base for transforms */
  height: 10px;
  transform-style: preserve-3d;
  animation: sphere-rotate 60s linear infinite;
  animation-play-state: running;
}

.thinking-sphere-css3d-container:hover .sphere {
  /* animation-play-state: paused; */ /* Optional: Pause rotation on container hover */
}

.memo-item {
  position: absolute;
  top: 50%;
  left: 50%;
  padding: 6px 12px; // Slightly smaller padding
  // background-color: var(--el-color-primary-light-9); // Very light theme color for items
  // color: var(--el-color-primary);
  border-radius: var(--el-border-radius-small);
  font-size: 12px; // Smaller font
  text-align: center;
  cursor: pointer;
  backface-visibility: hidden; /* Important for 3D effect */
  transition: opacity 0.3s ease, transform 0.3s ease, background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
  opacity: 0.85; // Default opacity
}

.memo-item:hover {
  transform: scale(1.15) translateZ(20px) !important;
  opacity: 1 !important;
  z-index: 100 !important;
  box-shadow: var(--shadow-medium);
  // background-color: var(--accent-color-light); // Use accent for hover (handled by getCategoryStyle or direct binding)
  // color: var(--text-color-on-accent);
  // border-color: var(--accent-color);
}

/* Category colors - These classes should be defined in a global CSS or dynamically generated if colors come from DB */
/* These are example classes that the quickMemoStore.getCategoryClass(categoryId) should return */
// .category-work {
//   background-color: rgba(64, 158, 255, 0.9);
//   color: white;
//   border-color: rgba(64, 158, 255, 1);
// }
// .category-personal {
//   background-color: rgba(230, 162, 60, 0.9);
//   color: white;
//   border-color: rgba(230, 162, 60, 1);
// }
// .category-study {
//   background-color: rgba(103, 194, 58, 0.9);
//   color: white;
//   border-color: rgba(103, 194, 58, 1);
// }
// .category-default {
//   background-color: rgba(144, 147, 153, 0.85);
//   color: white;
//   border-color: rgba(144, 147, 153, 1);
// }

@keyframes sphere-rotate {
  from { transform: rotateY(0deg) rotateX(10deg); }
  to { transform: rotateY(360deg) rotateX(10deg); }
}

.loading-placeholder,
.empty-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #909399;
  font-size: 14px;
}
.loading-placeholder .el-icon,
.empty-placeholder .el-icon {
  font-size: 32px;
  margin-bottom: 10px;
}
.empty-placeholder span + span {
    margin-top: 5px;
    font-size: 12px;
}
</style> 