using System;
using System.IO;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using System.Globalization;
using OfficeOpenXml; // 添加EPPlus引用
using ItAssetsSystem.Core.Import;
using ItAssetsSystem.Core.Export;
using ItAssetsSystem.Models.Entities;
using ItAssetsSystem.Models.Import;
using ItAssetsSystem.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
// 使用完全限定名称以避免命名空间冲突
using LocationEntity = ItAssetsSystem.Models.Entities.Location;
using Task = System.Threading.Tasks.Task;
// 明确指定ImportResult的类型
using ImportResultCore = ItAssetsSystem.Core.Import.ImportResult<object>;
using ImportResponse = ItAssetsSystem.Models.Import.ImportResponse;

namespace ItAssetsSystem.Api.Import
{
    /// <summary>
    /// 数据导入API端点
    /// </summary>
    [ApiController]
    [Route("api/import")]
    public class ImportDataEndpoint : ControllerBase
    {
        private readonly ILogger<ImportDataEndpoint> _logger;
        private readonly IImportService _importService;
        private readonly IExportService _exportService;
        private readonly AppDbContext _dbContext;
        private readonly IServiceScopeFactory _serviceScopeFactory;
        private readonly string _connectionString;

        /// <summary>
        /// 构造函数
        /// </summary>
        public ImportDataEndpoint(
            ILogger<ImportDataEndpoint> logger,
            IImportService importService,
            IExportService exportService,
            AppDbContext dbContext,
            IServiceScopeFactory serviceScopeFactory,
            IConfiguration configuration)
        {
            _logger = logger;
            _importService = importService;
            _exportService = exportService;
            _dbContext = dbContext;
            _serviceScopeFactory = serviceScopeFactory;
            _connectionString = configuration.GetConnectionString("DefaultConnection");
        }

        /// <summary>
        /// 获取支持的导入格式
        /// </summary>
        [HttpGet("formats")]
        public ActionResult<List<string>> GetSupportedFormats()
        {
            try
            {
                var formats = _importService.GetSupportedFormats()
                    .Select(f => f.ToString().ToUpper())
                    .ToList();
                
                return Ok(formats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取支持的导入格式失败");
                return StatusCode(500, "获取导入格式失败: " + ex.Message);
            }
        }

        /// <summary>
        /// 获取导入模板
        /// </summary>
        [HttpGet("template/{entityType}/{formatStr}")]
        public async Task<IActionResult> GetTemplateAsync(string entityType, string formatStr)
        {
            try
            {
                _logger.LogInformation("生成导入模板: {EntityType}, 格式: {Format}", entityType, formatStr);

                if (string.IsNullOrEmpty(entityType))
                {
                    return BadRequest("必须指定实体类型");
                }

                if (string.IsNullOrEmpty(formatStr))
                {
                    return BadRequest("必须指定导出格式");
                }

                // 解析格式
                if (!Enum.TryParse<ImportFormat>(formatStr, true, out var format))
                {
                    return BadRequest($"不支持的导出格式: {formatStr}");
                }

                // 获取实体类型
                Type type = GetEntityTypeByName(entityType);
                if (type == null)
                {
                    return BadRequest($"未知的实体类型: {entityType}");
                }

                // 调用生成模板方法
                var templateMethod = typeof(IExportService).GetMethod("GenerateTemplateForType").MakeGenericMethod(type);
                var stream = (MemoryStream)await (Task<MemoryStream>)templateMethod.Invoke(_exportService, new object[] { format });

                // 设置文件名
                string extension = format switch
                {
                    ImportFormat.Csv => ".csv",
                    ImportFormat.Excel => ".xlsx",
                    ImportFormat.Json => ".json",
                    _ => ".txt"
                };

                string fileName = $"{entityType}_template{extension}";
                stream.Position = 0;

                // 设置MIME类型
                string contentType = format switch
                {
                    ImportFormat.Csv => "text/csv",
                    ImportFormat.Excel => "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                    ImportFormat.Json => "application/json",
                    _ => "application/octet-stream"
                };

                return File(stream, contentType, fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "生成导入模板失败: {Message}", ex.Message);
                return StatusCode(500, "生成导入模板失败: " + ex.Message);
            }
        }

        /// <summary>
        /// 获取导入模板（兼容前端路由）
        /// </summary>
        [HttpGet("template")]
        public async Task<IActionResult> GetTemplateCompatAsync(
            [FromQuery] string entityType, 
            [FromQuery] string format, 
            [FromQuery] Dictionary<string, string> @params)
        {
            try
            {
                // 支持直接传参和params对象包装的参数
                string actualEntityType = entityType;
                string actualFormat = format;
                
                // 如果直接参数为空，尝试从params对象中获取
                if ((string.IsNullOrEmpty(actualEntityType) || string.IsNullOrEmpty(actualFormat)) && @params != null)
                {
                    if (@params.ContainsKey("entityType"))
                    {
                        actualEntityType = @params["entityType"];
                    }
                    
                    if (@params.ContainsKey("format"))
                    {
                        actualFormat = @params["format"];
                    }
                }
                
                if (string.IsNullOrEmpty(actualEntityType) || string.IsNullOrEmpty(actualFormat))
                {
                    return BadRequest(new { success = false, message = "实体类型和格式不能为空" });
                }

                _logger.LogInformation("请求兼容路由导入模板：{EntityType} {Format}", actualEntityType, actualFormat);
                
                // 明确指定方法调用
                return await this.GetTemplateAsync(actualEntityType, actualFormat);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取导入模板失败");
                return BadRequest(new { success = false, message = "获取导入模板失败：" + ex.Message });
            }
        }

        /// <summary>
        /// 处理导入流程
        /// </summary>
        [HttpPost("process")]
        public async Task<IActionResult> ProcessImportAsync([FromForm] IFormFile file, [FromForm] string entityType)
        {
            if (file == null || file.Length == 0)
            {
                return BadRequest(new { success = false, message = "请提供有效的文件" });
            }

            try
            {
                _logger.LogInformation($"开始处理导入请求，实体类型: {entityType}，文件: {file.FileName}，文件大小: {file.Length}字节");
                
                // 创建导入选项
                        var options = new ImportOptions 
                        { 
                    Format = DetermineFormat(file.FileName),
                            HasHeaders = true,
                    ChineseColumnMappings = GetChineseColumnMappings(GetEntityTypeByName(entityType))
                };
                
                // 记录导入配置
                _logger.LogInformation($"导入配置：格式={options.Format}，包含表头={options.HasHeaders}");
                _logger.LogInformation($"中文列映射数量: {options.ChineseColumnMappings.Count}");
                foreach (var mapping in options.ChineseColumnMappings.Take(10))
                {
                    _logger.LogInformation($"列映射: {mapping.Key} -> {mapping.Value}");
                }
                
                // 生成导入ID
                string importId = Guid.NewGuid().ToString();
                
                // 判断实体类型并处理导入
                ImportResponse response;
                
                using (var stream = file.OpenReadStream())
                {
                    // 记录实体类型
                    _logger.LogInformation($"开始导入 {entityType} 数据，导入ID: {importId}");
                    
                    // 预检查Excel文件是否可读
                    if (options.Format == ImportFormat.Excel)
                    {
                        try {
                            // 创建一个临时ExcelPackage来测试文件是否可读
                            using (var package = new ExcelPackage(stream))
                            {
                                var workbook = package.Workbook;
                                var worksheet = workbook.Worksheets.FirstOrDefault();
                                if (worksheet == null)
                                {
                                    throw new InvalidOperationException("Excel文件中没有找到工作表");
                                }
                                
                                var rowCount = worksheet.Dimension?.Rows ?? 0;
                                var colCount = worksheet.Dimension?.Columns ?? 0;
                                
                                _logger.LogInformation("Excel文件预检查成功: 包含{RowCount}行, {ColCount}列", 
                                    rowCount, colCount);
                                    
                                // 如果有数据行，检查第一行数据
                                if (rowCount > 1)
                                {
                                    var firstRowDetails = new List<string>();
                                    
                                    // 寻找资产编号列的索引
                                    int assetCodeColIndex = -1;
                                    for (int col = 1; col <= colCount; col++)
                                    {
                                        var header = _importService.GetCellValueAsString(worksheet.Cells[1, col]);
                                        if (header.Contains("资产编码") || header.Contains("资产编号") || 
                                            header.Contains("AssetCode") || header.Equals("编码", StringComparison.OrdinalIgnoreCase))
                                        {
                                            assetCodeColIndex = col;
                                            _logger.LogWarning("找到资产编号列: 索引={Col}, 名称='{Name}'", col, header);
                                            break;
                                        }
                                    }
                                    
                                    // 读取前几行数据预览
                                    for (int col = 1; col <= colCount; col++)
                                    {
                                        string cellValue = "";
                                        try
                                        {
                                            var cell = worksheet.Cells[2, col];
                                            cellValue = _importService.GetCellValueAsString(cell);
                                            
                                            // 记录单元格内容详情，特别是空值和特殊字符
                                            if (string.IsNullOrEmpty(cellValue))
                                            {
                                                firstRowDetails.Add($"列{col}: [空]");
                                            }
                                            else
                                            {
                                                // 检测不可见字符
                                                string debugValue = string.Join(" ", cellValue.Select(c => ((int)c).ToString("X2")));
                                                if (cellValue.Any(c => char.IsControl(c) || char.IsWhiteSpace(c)))
                                                {
                                                    firstRowDetails.Add($"列{col}: '{cellValue}' [包含特殊字符:{debugValue}]");
                                                }
                                                else
                                                {
                                                    firstRowDetails.Add($"列{col}: '{cellValue}'");
                                                }
                                            }
                    }
                    catch (Exception ex)
                    {
                                            _logger.LogDebug(ex, "读取第一行数据单元格[2,{Col}]时出错", col);
                                        }
                                    }
                                    
                                    _logger.LogInformation("第1行数据预览: {Details}", 
                                        string.Join(" | ", firstRowDetails));
                                    
                                    // 如果找到了资产编号列，额外检查前几行的资产编号值
                                    if (assetCodeColIndex > 0)
                                    {
                                        _logger.LogWarning("检查资产编号列(列{Col})的前几行数据:", assetCodeColIndex);
                                        for (int row = 2; row <= Math.Min(rowCount, 5); row++)
                                        {
                                            try
                                            {
                                                string assetCode = _importService.GetCellValueAsString(worksheet.Cells[row, assetCodeColIndex]);
                                                string debugValue = string.Join(" ", (assetCode ?? "").Select(c => ((int)c).ToString("X2")));
                                                _logger.LogWarning("第{Row}行资产编号: '{AssetCode}', 长度:{Length}, 是否为空:{IsEmpty}, 十六进制:{HexValue}", 
                                                    row, assetCode, assetCode?.Length ?? 0, string.IsNullOrEmpty(assetCode), debugValue);
                                            }
                                            catch (Exception ex)
                                            {
                                                _logger.LogError(ex, "检查第{Row}行资产编号时出错", row);
                                            }
                                        }
                                    }
                                }
                            }
                            
                            // 需要重置流位置以便后续读取
                            stream.Position = 0;
                        }
                        catch (Exception ex) {
                            _logger.LogError(ex, "预检查Excel文件失败: {Message}", ex.Message);
                        }
                    }
                    
                    // 根据实体类型处理导入
                    switch (entityType.ToLower())
                    {
                        case "assets":
                        case "asset":
                            response = await ImportAssetsAsync(stream, options);
                            break;
                        case "locations":
                        case "location":
                            response = await ImportLocationAsync(stream, options);
                            break;
                        case "assettypes":
                        case "assettype":
                            response = await ImportAssetTypeAsync(stream, options);
                            break;
                        default:
                            return BadRequest(new { success = false, message = $"不支持的实体类型: {entityType}" });
                    }
                }
                
                // 设置导入ID
                response.ImportId = importId;
                
                // 记录导入结果
                _logger.LogInformation($"导入完成，ID: {importId}, 总行数: {response.TotalRows}, 成功: {response.SuccessCount}, 错误: {response.ErrorCount}, 警告: {response.WarningCount}");
                
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"导入数据失败: {ex.Message}");
                return StatusCode(500, new { success = false, message = $"导入失败: {ex.Message}" });
            }
        }

        /// <summary>
        /// 导入数据（支持前端旧API）
        /// </summary>
        [HttpPost("data")]
        public async Task<IActionResult> ImportDataAsync([FromForm] IFormFile file, [FromForm] string entityType)
        {
            _logger.LogInformation("接收到/api/import/data请求，重定向到处理方法");
            return await ProcessImportAsync(file, entityType);
        }

        /// <summary>
        /// 导入数据（专用端点）
        /// </summary>
        [HttpPost("upload/{entityType}")]
        public async Task<IActionResult> UploadFileAsync(string entityType, IFormFile file)
        {
            try
            {
                if (file == null || file.Length == 0)
                {
                    return BadRequest(new { success = false, message = "未选择文件" });
                }

                _logger.LogInformation("接收到上传文件请求：{EntityType}, 文件: {FileName}, 大小: {FileSize}字节", 
                    entityType, file.FileName, file.Length);

                var entityTypeObj = GetEntityTypeByName(entityType);
                if (entityTypeObj == null)
                {
                    return BadRequest(new { success = false, message = $"未知的实体类型: {entityType}" });
                }

                var format = DetermineFormat(file.FileName);
                
                using var stream = file.OpenReadStream();
                var options = new ImportOptions 
                { 
                    Format = format, 
                    HasHeaders = true,
                    ChineseColumnMappings = GetChineseColumnMappings(entityTypeObj)
                };

                var response = await ImportDataCompatAsync(stream, options, entityTypeObj);

                return Ok(response);
            }
            catch (Exception ex)
            {
                // 增强异常处理，捕获所有异常并返回给前端
                _logger.LogError(ex, "文件上传失败: {Message}", ex.Message);
                
                // 构建详细的错误响应
                var response = new ImportResponse
                {
                    Success = false,
                    Message = $"导入处理失败: {ex.Message}",
                    Errors = new List<string> { ex.Message }
                };
                
                // 添加内部异常信息
                var currentEx = ex.InnerException;
                while (currentEx != null)
                {
                    response.Errors.Add($"内部错误: {currentEx.Message}");
                    _logger.LogError("内部异常: {Message}", currentEx.Message);
                    currentEx = currentEx.InnerException;
                }
                
                // 返回400状态码，但包含详细的错误信息
                return StatusCode(400, response);
            }
        }

        /// <summary>
        /// 兼容导入接口
        /// </summary>
        [HttpPost("upload")]
        public async Task<IActionResult> UploadCompatAsync(IFormFile file, [FromQuery] string entityType)
        {
            if (string.IsNullOrEmpty(entityType))
            {
                // 尝试从表单中获取实体类型
                var form = await Request.ReadFormAsync();
                entityType = form["entityType"].ToString();
            }

            // 明确指定方法调用
            return await this.UploadFileAsync(entityType, file);
        }

        /// <summary>
        /// 处理不同类型的导入
        /// </summary>
        private async Task<ImportResponse> ImportDataCompatAsync(Stream stream, ImportOptions options, Type entityType)
        {
            _logger.LogInformation("开始兼容导入处理: {EntityType}", entityType.Name);
            
            // 针对不同的实体类型调用相应的导入方法
            if (entityType == typeof(Asset))
            {
                return await ImportAssetsAsync(stream, options);
            }
            else if (entityType == typeof(LocationEntity))
            {
                return await ImportLocationAsync(stream, options);
            }
            else if (entityType == typeof(AssetType))
            {
                return await ImportAssetTypeAsync(stream, options);
            }
            // 可以继续添加其他实体类型的导入处理
            
            throw new NotSupportedException($"不支持的实体类型导入: {entityType.Name}");
        }

        /// <summary>
        /// 导入资产数据
        /// </summary>
        private async Task<ImportResponse> ImportAssetsAsync(Stream stream, ImportOptions options)
        {
            try
            {
                _logger.LogInformation("开始导入资产数据，配置: 格式={Format}, 包含表头={HasHeaders}", 
                    options.Format, options.HasHeaders);
                
                // 预检查Excel文件是否可读，但不要硬编码表头
                if (options.Format == ImportFormat.Excel)
                {
                    try {
                        // 创建一个临时ExcelPackage来测试文件是否可读
                        using (var package = new ExcelPackage(stream))
                        {
                            var workbook = package.Workbook;
                            var worksheet = workbook.Worksheets.FirstOrDefault();
                            if (worksheet == null)
                            {
                                throw new InvalidOperationException("Excel文件中没有找到工作表");
                            }
                            
                            var rowCount = worksheet.Dimension?.Rows ?? 0;
                            var colCount = worksheet.Dimension?.Columns ?? 0;
                            
                            _logger.LogInformation("Excel文件预检查成功: 包含{RowCount}行, {ColCount}列", 
                                rowCount, colCount);
                                
                            // 如果有数据行，检查第一行数据
                            if (rowCount > 1)
                            {
                                var firstRowDetails = new List<string>();
                                
                                // 寻找资产编号列的索引
                                int assetCodeColIndex = -1;
                                for (int col = 1; col <= colCount; col++)
                                {
                                    var header = _importService.GetCellValueAsString(worksheet.Cells[1, col]);
                                    if (header.Contains("资产编码") || header.Contains("资产编号") || 
                                        header.Contains("AssetCode") || header.Equals("编码", StringComparison.OrdinalIgnoreCase))
                                    {
                                        assetCodeColIndex = col;
                                        _logger.LogWarning("找到资产编号列: 索引={Col}, 名称='{Name}'", col, header);
                                        break;
                                    }
                                }
                                
                                // 读取前几行数据预览
                                for (int col = 1; col <= colCount; col++)
                                {
                                    string cellValue = "";
                                    try
                                    {
                                        var cell = worksheet.Cells[2, col];
                                        cellValue = _importService.GetCellValueAsString(cell);
                                        
                                        // 记录单元格内容详情，特别是空值和特殊字符
                                        if (string.IsNullOrEmpty(cellValue))
                                        {
                                            firstRowDetails.Add($"列{col}: [空]");
                                        }
                                        else
                                        {
                                            // 检测不可见字符
                                            string debugValue = string.Join(" ", cellValue.Select(c => ((int)c).ToString("X2")));
                                            if (cellValue.Any(c => char.IsControl(c) || char.IsWhiteSpace(c)))
                                            {
                                                firstRowDetails.Add($"列{col}: '{cellValue}' [包含特殊字符:{debugValue}]");
                                            }
                                            else
                                            {
                                                firstRowDetails.Add($"列{col}: '{cellValue}'");
                                            }
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        _logger.LogDebug(ex, "读取第一行数据单元格[2,{Col}]时出错", col);
                                    }
                                }
                                
                                _logger.LogInformation("第1行数据预览: {Details}", 
                                    string.Join(" | ", firstRowDetails));
                                    
                                // 如果找到了资产编号列，额外检查前几行的资产编号值
                                if (assetCodeColIndex > 0)
                                {
                                    _logger.LogWarning("检查资产编号列(列{Col})的前几行数据:", assetCodeColIndex);
                                    for (int row = 2; row <= Math.Min(rowCount, 5); row++)
                                    {
                                        try
                                        {
                                            string assetCode = _importService.GetCellValueAsString(worksheet.Cells[row, assetCodeColIndex]);
                                            string debugValue = string.Join(" ", (assetCode ?? "").Select(c => ((int)c).ToString("X2")));
                                            _logger.LogWarning("第{Row}行资产编号: '{AssetCode}', 长度:{Length}, 是否为空:{IsEmpty}, 十六进制:{HexValue}", 
                                                row, assetCode, assetCode?.Length ?? 0, string.IsNullOrEmpty(assetCode), debugValue);
                                        }
                                        catch (Exception ex)
                                        {
                                            _logger.LogError(ex, "检查第{Row}行资产编号时出错", row);
                                        }
                                    }
                                }
                            }
                        }
                        
                        // 需要重置流位置以便后续读取
                        stream.Position = 0;
                    }
                    catch (Exception ex) {
                        _logger.LogError(ex, "预检查Excel文件失败: {Message}", ex.Message);
                    }
                }
                
                // 1. 预加载资产类型数据
                var assetTypes = await _dbContext.Set<AssetType>().ToListAsync();
                var assetTypeDictionary = assetTypes.ToDictionary(x => x.Name, x => x.Id, StringComparer.OrdinalIgnoreCase);
                
                // 默认资产类型ID设为24
                int defaultAssetTypeId = 24;
                _logger.LogInformation("设置默认资产类型ID: {DefaultId}", defaultAssetTypeId);
                
                // 2. 预加载位置数据(type=4)
                var locations = await _dbContext.Set<LocationEntity>()
                    .Where(l => l.Type == 4) // 只查询type=4的位置
                    .ToListAsync();
                    
                var locationDictionary = locations.ToDictionary(x => x.Name, x => x.Id, StringComparer.OrdinalIgnoreCase);
                
                // 默认位置ID设为1
                int defaultLocationId = 1;
                _logger.LogInformation("设置默认位置ID: {DefaultId}", defaultLocationId);
                
                // 3. 导入数据
                stream.Position = 0;
                var result = await _importService.ImportDataAsync<Asset>(stream, options);
                
                // 记录导入结果的基本信息
                _logger.LogInformation("资产导入初步结果: 总行数={TotalRows}, 成功项={SuccessCount}, 错误数={ErrorCount}", 
                    result.TotalRows, result.SuccessItems.Count, result.Errors.Count);
                
                if (result.SuccessItems.Count == 0)
                {
                    _logger.LogWarning("没有成功导入的项目，可能是Excel格式不兼容或列名不匹配");
                    foreach (var error in result.Errors)
                    {
                        _logger.LogWarning("行 {Row} 导入错误: {Error}", error.Key, error.Value);
                    }
                    
                    // 返回导入失败信息
                    return new ImportResponse
                    {
                        Success = false,
                        Message = "导入失败: 未能识别任何有效数据。请检查Excel表格格式和列名是否正确。",
                        TotalRows = result.TotalRows,
                        SuccessCount = 0,
                        ErrorCount = result.Errors.Count,
                        Errors = result.Errors.Select(e => $"行 {e.Key}: {e.Value}").ToList()
                    };
                }

                // 4. 处理关联字段和检查资产编号
                foreach (var asset in result.SuccessItems.ToList())
                {
                    try 
                    {
                        // 记录每个资产的基本信息
                        _logger.LogInformation("处理资产: 资产名称={Name}, 资产编码={AssetCode}, 资产类型={AssetTypeName}, 位置={LocationName}", 
                            asset.Name, asset.AssetCode, asset.AssetTypeName, asset.LocationName);
                        
                        // 检查资产编号是否为空
                        if (string.IsNullOrEmpty(asset.AssetCode))
                        {
                            _logger.LogWarning("资产编号为空，尝试生成临时编号");
                            
                            // 生成临时资产编号
                            string tempAssetCode = $"TEMP_{DateTime.Now:yyyyMMdd}_{Guid.NewGuid().ToString().Substring(0, 6).ToUpper()}";
                            asset.AssetCode = tempAssetCode;
                            _logger.LogWarning("为空资产编号生成临时编码: {AssetCode}", tempAssetCode);
                            
                            // 添加警告信息而不是错误
                            int rowIndex = asset.ImportRowIndex > 0 ? asset.ImportRowIndex : 2;
                            if (!result.Warnings.ContainsKey(rowIndex))
                            {
                                result.Warnings[rowIndex] = new List<string>();
                            }
                            result.Warnings[rowIndex].Add($"资产编号为空，已自动生成临时编号: {tempAssetCode}");
                            
                            // 继续处理而不是跳过
                            _logger.LogInformation("使用临时资产编号继续处理");
                        }
                        
                        // 检查资产编号是否已存在
                        bool assetCodeExists = await _dbContext.Set<Asset>().AnyAsync(a => a.AssetCode == asset.AssetCode);
                        if (assetCodeExists)
                        {
                            _logger.LogWarning("资产编号 {AssetCode} 已存在，跳过导入", asset.AssetCode);
                            // 使用固定行号，避免在集合中查找索引导致的错误
                            int rowIndex = 2; // 假设数据从第2行开始（第1行为表头）
                            if (asset.ImportRowIndex > 0)
                            {
                                // 如果导入过程中已经记录了行号，则使用该行号
                                rowIndex = asset.ImportRowIndex;
                            }
                            result.Errors[rowIndex] = $"资产编号 {asset.AssetCode} 已存在";
                            result.SuccessItems.Remove(asset);
                            continue;
                        }

                        // 处理资产类型 - 精确匹配名称
                        if (!string.IsNullOrEmpty(asset.AssetTypeName))
                        {
                            string trimmedTypeName = asset.AssetTypeName.Trim();
                            if (assetTypeDictionary.TryGetValue(trimmedTypeName, out var assetTypeId))
                            {
                                asset.AssetTypeId = assetTypeId;
                                _logger.LogInformation("资产类型匹配成功: '{AssetTypeName}' 对应 ID: {Id}", 
                                    trimmedTypeName, assetTypeId);
                            }
                            else
                            {
                                // 资产类型匹配失败，使用默认资产类型ID 24
                                _logger.LogWarning("未找到资产类型 '{AssetTypeName}'，使用默认资产类型ID: {DefaultId}", 
                                    trimmedTypeName, defaultAssetTypeId);
                                
                                asset.AssetTypeId = defaultAssetTypeId;
                                
                                // 添加警告信息
                                int rowIndex = result.SuccessItems.IndexOf(asset) + 2;
                                if (!result.Warnings.ContainsKey(rowIndex))
                                {
                                    result.Warnings[rowIndex] = new List<string>();
                                }
                                result.Warnings[rowIndex].Add($"资产类型 '{trimmedTypeName}' 不存在，已使用默认资产类型ID: {defaultAssetTypeId}");
                            }
                        }
                        else
                        {
                            // 资产类型名称为空，使用默认资产类型ID 24
                            _logger.LogWarning("资产类型名称为空，使用默认资产类型ID: {DefaultId}", defaultAssetTypeId);
                            asset.AssetTypeId = defaultAssetTypeId;
                            
                            // 添加警告信息
                            int rowIndex = result.SuccessItems.IndexOf(asset) + 2;
                            if (!result.Warnings.ContainsKey(rowIndex))
                            {
                                result.Warnings[rowIndex] = new List<string>();
                            }
                            result.Warnings[rowIndex].Add($"资产类型名称为空，已使用默认资产类型ID: {defaultAssetTypeId}");
                        }
                        
                        // 处理位置 - 精确匹配名称且type=4
                        if (!string.IsNullOrEmpty(asset.LocationName))
                        {
                            string trimmedLocationName = asset.LocationName.Trim();
                            if (locationDictionary.TryGetValue(trimmedLocationName, out var locationId))
                            {
                                asset.LocationId = locationId;
                                _logger.LogInformation("位置匹配成功: '{LocationName}' 对应 ID: {Id}", 
                                    trimmedLocationName, locationId);
                            }
                            else
                            {
                                // 位置匹配失败，使用默认位置ID 1
                                _logger.LogWarning("未找到type=4的位置 '{LocationName}'，使用默认位置ID: {DefaultId}", 
                                    trimmedLocationName, defaultLocationId);
                                
                                asset.LocationId = defaultLocationId;
                                
                                // 添加警告信息
                                int rowIndex = result.SuccessItems.IndexOf(asset) + 2;
                                if (!result.Warnings.ContainsKey(rowIndex))
                                {
                                    result.Warnings[rowIndex] = new List<string>();
                                }
                                result.Warnings[rowIndex].Add($"未找到位置 '{trimmedLocationName}'，已使用默认位置ID: {defaultLocationId}");
                            }
                        }
                        else
                        {
                            // 位置名称为空，使用默认位置ID 1
                            _logger.LogWarning("位置名称为空，使用默认位置ID: {DefaultId}", defaultLocationId);
                            asset.LocationId = defaultLocationId;
                            
                            // 添加警告信息
                            int rowIndex = result.SuccessItems.IndexOf(asset) + 2;
                            if (!result.Warnings.ContainsKey(rowIndex))
                            {
                                result.Warnings[rowIndex] = new List<string>();
                            }
                            result.Warnings[rowIndex].Add($"位置名称为空，已使用默认位置ID: {defaultLocationId}");
                        }
                        
                        // 确保资产名称不为空
                        if (string.IsNullOrEmpty(asset.Name))
                        {
                            asset.Name = $"资产_{asset.AssetCode}"; // 用资产编号生成默认名称
                            _logger.LogWarning("资产名称为空，已使用资产编号生成名称: {Name}", asset.Name);
                            
                            // 添加警告信息
                            int rowIndex = result.SuccessItems.IndexOf(asset) + 2;
                            if (!result.Warnings.ContainsKey(rowIndex))
                            {
                                result.Warnings[rowIndex] = new List<string>();
                            }
                            result.Warnings[rowIndex].Add($"资产名称为空，已自动生成名称: {asset.Name}");
                        }
                        
                        // 设置资产状态为"在用"(值为1)，如果未指定
                        if (asset.Status <= 0)
                        {
                            asset.Status = 1; // 设置为"在用"状态
                            _logger.LogInformation("资产状态未指定，设置为默认状态'在用'(1)");
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "处理资产时发生异常: {Message}", ex.Message);
                        result.SuccessItems.Remove(asset);
                        int rowIndex = result.SuccessItems.IndexOf(asset) + 2;
                        result.Errors[rowIndex] = $"处理资产时发生异常: {ex.Message}";
                    }
                }

                var response = new ImportResponse
                {
                    TotalRows = result.TotalRows,
                    SuccessCount = result.SuccessItems.Count,
                    ErrorCount = result.Errors.Count,
                    WarningCount = result.Warnings.Count
                };

                // 保存处理后的实体
                await SaveImportedEntitiesAsync(result, response);
                
                return response;
                    }
                    catch (Exception ex)
                    {
                _logger.LogError(ex, "导入资产数据时发生异常: {Message}", ex.Message);
                return new ImportResponse
                {
                    Success = false,
                    Message = $"导入失败: {ex.Message}",
                    Errors = new List<string> { ex.Message }
                };
            }
        }

        /// <summary>
        /// 导入位置数据
        /// </summary>
        private async Task<ImportResponse> ImportLocationAsync(Stream stream, ImportOptions options)
        {
            try
            {
                // 确保使用正确的ImportResult类型
                var result = await _importService.ImportDataAsync<LocationEntity>(stream, options);
                var response = new ImportResponse
                {
                    TotalRows = result.TotalRows,
                    SuccessCount = result.SuccessItems.Count,
                    ErrorCount = result.Errors.Count
                };

                // 手动转换集合内容
                await SaveImportedEntitiesAsync(result, response);
                
                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "导入位置数据时发生异常");
                return new ImportResponse
                {
                    Success = false,
                    Message = $"导入失败: {ex.Message}",
                    Errors = new List<string> { ex.Message }
                };
            }
        }

        /// <summary>
        /// 导入资产类型数据
        /// </summary>
        private async Task<ImportResponse> ImportAssetTypeAsync(Stream stream, ImportOptions options)
        {
            try
            {
                // 确保使用正确的ImportResult类型
                var result = await _importService.ImportDataAsync<AssetType>(stream, options);
                var response = new ImportResponse
                {
                    TotalRows = result.TotalRows,
                    SuccessCount = result.SuccessItems.Count,
                    ErrorCount = result.Errors.Count
                };

                // 手动转换集合内容
                await SaveImportedEntitiesAsync(result, response);
                
                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "导入资产类型数据时发生异常");
                return new ImportResponse
                {
                    Success = false,
                    Message = $"导入失败: {ex.Message}",
                    Errors = new List<string> { ex.Message }
                };
            }
        }

        /// <summary>
        /// 将导入的实体保存到数据库
        /// </summary>
        private async Task SaveImportedEntitiesAsync<T>(ItAssetsSystem.Core.Import.ImportResult<T> result, ImportResponse response) where T : class
        {
            response.Errors = new List<string>();
            response.SuccessItems = new List<object>();
            
            foreach (var entity in result.SuccessItems)
            {
                try
                {
                    // 特殊处理Asset类型
                    if (entity is Asset asset)
                    {
                        // 检查资产编号是否已存在
                        bool exists = await _dbContext.Set<Asset>().AnyAsync(a => a.AssetCode == asset.AssetCode);
                        if (exists)
                        {
                            _logger.LogWarning("资产编号 {AssetCode} 已存在，跳过导入", asset.AssetCode);
                            response.Errors.Add($"资产编号 {asset.AssetCode} 已存在，跳过导入");
                            continue;
                        }
                        
                        // 添加到数据库
                        _dbContext.Set<Asset>().Add(asset);
                        
                        // 添加到成功项目列表
                        response.SuccessItems.Add(new {
                            AssetCode = asset.AssetCode,
                            Name = asset.Name
                        });
                    }
                    else
                    {
                        // 处理其他类型的实体
                        _dbContext.Set<T>().Add(entity);
                        
                        // 尝试获取实体的ID和名称
                        var idProp = typeof(T).GetProperty("Id");
                        var nameProp = typeof(T).GetProperty("Name");
                        
                        if (idProp != null)
                        {
                            var id = idProp.GetValue(entity);
                            response.SuccessItems.Add(new {
                                Id = id?.ToString(),
                                Type = typeof(T).Name,
                                Name = nameProp?.GetValue(entity)?.ToString() ?? "未知"
                    });
                }
            }
            
                    await _dbContext.SaveChangesAsync();
                    response.SuccessCount++;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "保存实体时发生异常: {Message}", ex.Message);
                    response.Errors.Add($"保存实体时发生异常: {ex.Message}");
                }
            }
            
            // 合并警告信息到响应
            if (result.Warnings.Count > 0)
            {
                response.Warnings = new List<string>();
                foreach (var warning in result.Warnings)
                {
                    response.Warnings.AddRange(warning.Value.Select(w => $"行 {warning.Key}: {w}"));
                }
                response.WarningCount = response.Warnings.Count;
            }
            
            // 合并错误信息到响应
            if (result.Errors.Count > 0)
            {
                foreach (var error in result.Errors)
                {
                    response.Errors.Add($"行 {error.Key}: {error.Value}");
                }
                response.ErrorCount = response.Errors.Count;
            }
            
            response.Success = response.SuccessCount > 0;
            if (response.SuccessCount > 0)
            {
                response.Message = $"成功导入 {response.SuccessCount} 条记录";
                if (response.ErrorCount > 0)
                {
                    response.Message += $", {response.ErrorCount} 条记录导入失败";
                }
                if (response.WarningCount > 0)
                {
                    response.Message += $", {response.WarningCount} 条警告";
                }
            }
            else
            {
                response.Message = "导入失败，没有成功导入的记录";
            }
        }

        /// <summary>
        /// 生成资产编码
        /// </summary>
        private async Task<string> GenerateAssetCodeAsync(Asset asset)
        {
            // 获取资产类型信息
            var assetType = await _dbContext.AssetTypes.FindAsync(asset.AssetTypeId);
            string typeCode = "IT"; // 默认前缀
            
            if (assetType != null && !string.IsNullOrEmpty(assetType.Code))
            {
                typeCode = assetType.Code;
            }
            
            // 获取当前最大序列号
            int sequence = 1;
            var lastAsset = await _dbContext.Assets
                .Where(a => a.AssetTypeId == asset.AssetTypeId)
                .OrderByDescending(a => a.Id)
                .FirstOrDefaultAsync();
                
            if (lastAsset != null)
            {
                // 尝试从现有编码中提取序列号
                string existingCode = lastAsset.AssetCode ?? "";
                var match = System.Text.RegularExpressions.Regex.Match(existingCode, @"(\d+)$");
                if (match.Success && int.TryParse(match.Groups[1].Value, out int lastSequence))
                {
                    sequence = lastSequence + 1;
                }
                else
                {
                    sequence = lastAsset.Id + 1;
                }
            }
            
            // 生成编码格式：类型代码-序列号(4位)
            return $"{typeCode}-{sequence:D4}";
        }

        /// <summary>
        /// 根据名称获取实体类型
        /// </summary>
        private Type GetEntityTypeByName(string entityTypeName)
        {
            if (string.IsNullOrEmpty(entityTypeName))
                return null;
                
            return entityTypeName.ToLower() switch
            {
                "asset" => typeof(Asset),
                "assets" => typeof(Asset),
                "location" => typeof(LocationEntity),
                "locations" => typeof(LocationEntity),
                "assettype" => typeof(AssetType),
                "assettypes" => typeof(AssetType),
                "department" => typeof(Department),
                "departments" => typeof(Department),
                "supplier" => typeof(Supplier),
                "suppliers" => typeof(Supplier),
                "faultrecord" => typeof(FaultRecord),
                "faultrecords" => typeof(FaultRecord),
                "faulttype" => typeof(FaultType),
                "faulttypes" => typeof(FaultType),
                "purchaseorder" => typeof(PurchaseOrder),
                "purchaseorders" => typeof(PurchaseOrder),
                _ => null
            };
        }

        /// <summary>
        /// 根据文件名确定导入格式
        /// </summary>
        private ImportFormat DetermineFormat(string fileName)
        {
            string extension = Path.GetExtension(fileName).ToLower();
            
            return extension switch
            {
                ".csv" => ImportFormat.Csv,
                ".xlsx" => ImportFormat.Excel,
                ".json" => ImportFormat.Json,
                _ => ImportFormat.Excel // 默认
            };
        }

        /// <summary>
        /// 获取中文列名映射
        /// </summary>
        private Dictionary<string, string> GetChineseColumnMappings(Type entityType)
        {
            // 返回中文列名到属性名的映射
            if (entityType == typeof(Asset))
            {
                return new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase) // 使用不区分大小写的比较器
                {
                    // 资产编码相关别名
                    { "资产编码", "AssetCode" },
                    { "资产编号", "AssetCode" },
                    { "编码", "AssetCode" },
                    { "编号", "AssetCode" },
                    { "设备编号", "AssetCode" },
                    { "SC", "AssetCode" },
                    
                    // 财务编号相关别名
                    { "财务编号", "FinancialCode" },
                    { "财务编码", "FinancialCode" },
                    { "财务号", "FinancialCode" },
                    { "账务编号", "FinancialCode" },
                    
                    // 资产名称相关别名
                    { "资产名称", "Name" },
                    { "名称", "Name" },
                    { "设备名称", "Name" },
                    { "设备", "Name" },
                    
                    // 序列号相关别名
                    { "SN", "SerialNumber" },
                    { "序列号", "SerialNumber" },
                    { "S/N", "SerialNumber" },
                    { "设备序列号", "SerialNumber" },
                    
                    // 型号相关别名
                    { "型号", "Model" },
                    { "设备型号", "Model" },
                    { "规格型号", "Model" },
                    
                    // 品牌相关别名
                    { "品牌", "Brand" },
                    { "品牌名称", "Brand" },
                    { "厂商品牌", "Brand" },
                    
                    // 资产类型相关别名
                    { "资产类型", "AssetTypeName" },
                    { "类型", "AssetTypeName" },
                    { "设备类型", "AssetTypeName" },
                    { "资产分类", "AssetTypeName" },
                    
                    // 资产类型ID相关别名
                    { "资产类型ID", "AssetTypeId" },
                    { "类型ID", "AssetTypeId" },
                    { "资产类型编号", "AssetTypeId" },
                    
                    // 状态相关别名
                    { "状态", "Status" },
                    { "使用状态", "Status" },
                    { "设备状态", "Status" },
                    
                    // 购买日期相关别名
                    { "购买日期", "PurchaseDate" },
                    { "采购日期", "PurchaseDate" },
                    { "购置日期", "PurchaseDate" },
                    { "入库日期", "PurchaseDate" },
                    
                    // 价格相关别名
                    { "价格", "Price" },
                    { "单价", "Price" },
                    { "金额", "Price" },
                    { "采购价", "Price" },
                    
                    // 规格相关别名
                    { "规格", "Specification" },
                    { "参数", "Specification" },
                    { "详细规格", "Specification" },
                    
                    // 制造商相关别名
                    { "制造商", "Manufacturer" },
                    { "生产厂商", "Manufacturer" },
                    { "厂商", "Manufacturer" },
                    
                    // 供应商相关别名
                    { "供应商", "Supplier" },
                    { "供货商", "Supplier" },
                    { "经销商", "Supplier" },
                    
                    // 部门相关别名
                    { "部门", "DepartmentName" },
                    { "所属部门", "DepartmentName" },
                    { "使用部门", "DepartmentName" },
                    
                    { "部门ID", "DepartmentId" },
                    { "部门编号", "DepartmentId" },
                    
                    // 位置相关别名
                    { "位置", "LocationName" },
                    { "存放位置", "LocationName" },
                    { "使用位置", "LocationName" },
                    { "所在位置", "LocationName" },
                    
                    { "位置ID", "LocationId" },
                    { "位置编号", "LocationId" },
                    
                    // 保修相关别名
                    { "保修到期日", "WarrantyExpireDate" },
                    { "保修结束日期", "WarrantyExpireDate" },
                    { "保修截止日", "WarrantyExpireDate" },
                    { "保修截至日期", "WarrantyExpireDate" },
                    { "保修期满日期", "WarrantyExpireDate" },
                    { "到期日", "WarrantyExpireDate" },
                    
                    { "保修期", "WarrantyPeriod" },
                    { "保修期(月)", "WarrantyPeriod" },
                    { "保修期限", "WarrantyPeriod" },
                    { "保修月数", "WarrantyPeriod" },
                    
                    // 备注相关别名
                    { "备注", "Notes" },
                    { "说明", "Notes" },
                    { "附注", "Notes" },
                    { "描述", "Notes" }
                };
            }
            else if (entityType == typeof(LocationEntity))
            {
                return new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase)
                {
                    { "位置编码", "Code" },
                    { "位置编号", "Code" },
                    { "编码", "Code" },
                    { "编号", "Code" },
                    
                    { "位置名称", "Name" },
                    { "名称", "Name" },
                    
                    { "位置类型", "Type" },
                    { "类型", "Type" },
                    
                    { "父级位置", "ParentId" },
                    { "上级位置", "ParentId" },
                    { "父级ID", "ParentId" },
                    
                    { "父级名称", "ParentName" },
                    { "上级名称", "ParentName" },
                    
                    { "描述", "Description" },
                    { "说明", "Description" },
                    { "备注", "Description" },
                    
                    { "状态", "IsActive" },
                    { "是否启用", "IsActive" },
                    { "启用状态", "IsActive" }
                };
            }
            else if (entityType == typeof(AssetType))
            {
                return new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase)
                {
                    { "类型编码", "Code" },
                    { "类型编号", "Code" },
                    { "编码", "Code" },
                    { "编号", "Code" },
                    
                    { "类型名称", "Name" },
                    { "名称", "Name" },
                    
                    { "描述", "Description" },
                    { "说明", "Description" },
                    { "备注", "Description" },
                    
                    { "父级类型", "ParentId" },
                    { "上级类型", "ParentId" },
                    { "父级ID", "ParentId" },
                    
                    { "父级名称", "ParentName" },
                    { "上级名称", "ParentName" }
                };
            }
            else if (entityType == typeof(Department))
            {
                return new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase)
                {
                    { "部门编码", "Code" },
                    { "部门编号", "Code" },
                    { "编码", "Code" },
                    { "编号", "Code" },
                    
                    { "部门名称", "Name" },
                    { "名称", "Name" },
                    
                    { "上级部门", "ParentId" },
                    { "父级部门", "ParentId" },
                    { "父级ID", "ParentId" },
                    
                    { "上级名称", "ParentName" },
                    { "父级名称", "ParentName" },
                    
                    { "描述", "Description" },
                    { "说明", "Description" },
                    { "备注", "Description" },
                    
                    { "状态", "IsActive" },
                    { "是否启用", "IsActive" },
                    { "启用状态", "IsActive" }
                };
            }
            else if (entityType == typeof(Supplier))
            {
                return new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase)
                {
                    { "供应商编码", "Code" },
                    { "供应商编号", "Code" },
                    { "编码", "Code" },
                    { "编号", "Code" },
                    
                    { "供应商名称", "Name" },
                    { "名称", "Name" },
                    
                    { "联系人", "ContactPerson" },
                    { "联系人姓名", "ContactPerson" },
                    
                    { "联系电话", "ContactPhone" },
                    { "电话", "ContactPhone" },
                    { "手机", "ContactPhone" },
                    
                    { "地址", "Address" },
                    { "详细地址", "Address" },
                    
                    { "电子邮件", "Email" },
                    { "邮箱", "Email" },
                    { "E-mail", "Email" },
                    
                    { "备注", "Notes" },
                    { "说明", "Notes" },
                    { "附注", "Notes" }
                };
            }
            
            return new Dictionary<string, string>();
        }

        /// <summary>
        /// 获取文件MIME类型
        /// </summary>
        private string GetContentType(string format)
        {
            return format.ToLower() switch
            {
                "csv" => "text/csv",
                "excel" => "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                "json" => "application/json",
                _ => "application/octet-stream"
            };
        }

        /// <summary>
        /// 获取文件扩展名
        /// </summary>
        private string GetFileExtension(ImportFormat format)
        {
            return format switch
            {
                ImportFormat.Csv => "csv",
                ImportFormat.Excel => "xlsx",
                ImportFormat.Json => "json",
                _ => "txt"
            };
        }

        /// <summary>
        /// 获取状态文本
        /// </summary>
        private string GetStatusText(int status)
        {
            return status switch
            {
                0 => "在用",
                1 => "闲置",
                2 => "维修中",
                3 => "报废",
                _ => "未知"
            };
        }
    }
} 