// File: Core/Abstractions/ICoreDataQueryService.cs
// Description: 核心数据查询服务接口，用于封装对核心模块的数据查询

using System.Collections.Generic;
using System.Threading.Tasks;

namespace ItAssetsSystem.Core.Abstractions
{
    /// <summary>
    /// 核心数据查询服务接口
    /// </summary>
    /// <remarks>
    /// 该接口用于封装对核心模块的只读数据查询，避免直接依赖于核心模块。
    /// 实现方式可以是通过调用核心模块的V1 API或只读的数据库查询。
    /// </remarks>
    public interface ICoreDataQueryService
    {
        /// <summary>
        /// 获取用户信息
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>用户信息</returns>
        Task<CoreUserDto> GetUserAsync(int userId);

        /// <summary>
        /// 获取多个用户信息
        /// </summary>
        /// <param name="userIds">用户ID列表</param>
        /// <returns>用户信息列表</returns>
        Task<List<CoreUserDto>> GetUsersAsync(IEnumerable<int> userIds);

        /// <summary>
        /// 获取资产信息
        /// </summary>
        /// <param name="assetId">资产ID</param>
        /// <returns>资产信息</returns>
        Task<CoreAssetDto> GetAssetAsync(int assetId);

        /// <summary>
        /// 获取多个资产信息
        /// </summary>
        /// <param name="assetIds">资产ID列表</param>
        /// <returns>资产信息列表</returns>
        Task<List<CoreAssetDto>> GetAssetsAsync(IEnumerable<int> assetIds);

        /// <summary>
        /// 获取位置信息
        /// </summary>
        /// <param name="locationId">位置ID</param>
        /// <returns>位置信息</returns>
        Task<CoreLocationDto> GetLocationAsync(int locationId);

        /// <summary>
        /// 获取多个位置信息
        /// </summary>
        /// <param name="locationIds">位置ID列表</param>
        /// <returns>位置信息列表</returns>
        Task<List<CoreLocationDto>> GetLocationsAsync(IEnumerable<int> locationIds);

        /// <summary>
        /// 获取部门信息
        /// </summary>
        /// <param name="departmentId">部门ID</param>
        /// <returns>部门信息</returns>
        Task<CoreDepartmentDto> GetDepartmentAsync(int departmentId);

        /// <summary>
        /// 验证用户是否存在
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>用户是否存在</returns>
        Task<bool> UserExistsAsync(int userId);

        /// <summary>
        /// 验证资产是否存在
        /// </summary>
        /// <param name="assetId">资产ID</param>
        /// <returns>资产是否存在</returns>
        Task<bool> AssetExistsAsync(int assetId);

        /// <summary>
        /// 验证位置是否存在
        /// </summary>
        /// <param name="locationId">位置ID</param>
        /// <returns>位置是否存在</returns>
        Task<bool> LocationExistsAsync(int locationId);

        /// <summary>
        /// 获取所有用户的简要信息
        /// </summary>
        /// <returns>用户简要信息列表</returns>
        Task<List<CoreUserBasicDto>> GetAllUsersBasicInfoAsync();

        /// <summary>
        /// 获取所有资产的简要信息
        /// </summary>
        /// <returns>资产简要信息列表</returns>
        Task<List<CoreAssetBasicDto>> GetAllAssetsBasicInfoAsync();

        /// <summary>
        /// 获取所有位置的简要信息
        /// </summary>
        /// <returns>位置简要信息列表</returns>
        Task<List<CoreLocationBasicDto>> GetAllLocationsBasicInfoAsync();
    }

    /// <summary>
    /// 核心用户DTO
    /// </summary>
    public class CoreUserDto
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 用户名
        /// </summary>
        public string Username { get; set; }

        /// <summary>
        /// 姓名
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 电子邮件
        /// </summary>
        public string Email { get; set; }

        /// <summary>
        /// 部门ID
        /// </summary>
        public int? DepartmentId { get; set; }

        /// <summary>
        /// 部门名称
        /// </summary>
        public string DepartmentName { get; set; }

        /// <summary>
        /// 头像URL
        /// </summary>
        public string AvatarUrl { get; set; }

        /// <summary>
        /// 手机号码
        /// </summary>
        public string PhoneNumber { get; set; }

        /// <summary>
        /// 职位
        /// </summary>
        public string Position { get; set; }
    }

    /// <summary>
    /// 核心用户基本信息DTO
    /// </summary>
    public class CoreUserBasicDto
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 姓名
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 部门名称
        /// </summary>
        public string DepartmentName { get; set; }
    }

    /// <summary>
    /// 核心资产DTO
    /// </summary>
    public class CoreAssetDto
    {
        /// <summary>
        /// 资产ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 资产名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 资产编号
        /// </summary>
        public string AssetNumber { get; set; }

        /// <summary>
        /// 资产类型ID
        /// </summary>
        public int AssetTypeId { get; set; }

        /// <summary>
        /// 资产类型名称
        /// </summary>
        public string AssetTypeName { get; set; }

        /// <summary>
        /// 位置ID
        /// </summary>
        public int? LocationId { get; set; }

        /// <summary>
        /// 位置名称
        /// </summary>
        public string LocationName { get; set; }
    }

    /// <summary>
    /// 核心资产基本信息DTO
    /// </summary>
    public class CoreAssetBasicDto
    {
        /// <summary>
        /// 资产ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 资产名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 资产编号
        /// </summary>
        public string AssetNumber { get; set; }
    }

    /// <summary>
    /// 核心位置DTO
    /// </summary>
    public class CoreLocationDto
    {
        /// <summary>
        /// 位置ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 位置名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 位置编号
        /// </summary>
        public string LocationCode { get; set; }

        /// <summary>
        /// 父位置ID
        /// </summary>
        public int? ParentId { get; set; }

        /// <summary>
        /// 父位置名称
        /// </summary>
        public string ParentName { get; set; }

        /// <summary>
        /// 完整路径
        /// </summary>
        public string FullPath { get; set; }
    }

    /// <summary>
    /// 核心位置基本信息DTO
    /// </summary>
    public class CoreLocationBasicDto
    {
        /// <summary>
        /// 位置ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 位置名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 完整路径
        /// </summary>
        public string FullPath { get; set; }
    }

    /// <summary>
    /// 核心部门DTO
    /// </summary>
    public class CoreDepartmentDto
    {
        /// <summary>
        /// 部门ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 部门名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 部门编号
        /// </summary>
        public string DepartmentCode { get; set; }

        /// <summary>
        /// 父部门ID
        /// </summary>
        public int? ParentId { get; set; }

        /// <summary>
        /// 父部门名称
        /// </summary>
        public string ParentName { get; set; }
    }
} 