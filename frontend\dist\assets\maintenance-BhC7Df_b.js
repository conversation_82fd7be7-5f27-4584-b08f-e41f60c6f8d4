import{aV as e,_ as a,r as t,ad as l,z as r,E as s,b as o,d as n,e as i,w as d,f as c,aG as u,q as p,a as m,m as y,o as g,p as v,F as b,h as f,D as w,b7 as h,l as _,k as R,t as C,$ as k,aH as $,bd as x,be as z,aJ as S,a9 as V}from"./index-CG5lHOPO.js";import{getRepairOrders as T,createRepairOrder as D}from"./spareparts-DKUrs8IX.js";import{R as N}from"./RepairOrderDialog--HNxb8Jy.js";const P="/ReturnToFactory",U={getReturnToFactoryList:a=>e.get(P,a),getReturnToFactoryById:a=>e.get(`${P}/${a}`),createReturnToFactory:a=>e.post(P,a),updateReturnToFactory:(a,t)=>e.put(`${P}/${a}`,t),deleteReturnToFactory:a=>e.delete(`${P}/${a}`),updateReturnStatus:(a,t)=>e.put(`${P}/${a}/status`,t),completeReturn:(a,t)=>e.post(`${P}/${a}/complete`,t),replenishSpareParts:(a,t)=>e.post(`${P}/${a}/replenish-spare-parts`,t),getReturnStatistics:a=>e.get(`${P}/statistics`,a),exportReturns:a=>e.download(`${P}/export`,a,"returns.xlsx")},F={class:"maintenance-container"},I={class:"page-header"},B={class:"page-actions"},K={class:"filter-container"},j={class:"asset-info"},Y={class:"asset-name"},A={class:"asset-code text-secondary"},L={class:"asset-sn text-secondary"},M={class:"pagination-container"},q=a({__name:"maintenance",setup(e){const a=t(!1),P=t("all"),q=t([]),E=t(null),G=t(!1),H=t({}),J=t("create"),O=l({currentPage:1,pageSize:10,total:0}),Q=l({code:"",assetKeyword:"",type:"",status:"",timeRange:[]}),W=[{label:"返厂维修",value:"factory"},{label:"第三方维修",value:"third_party"},{label:"保修服务",value:"warranty"}],X=[{label:"待发出",value:"pending"},{label:"维修中",value:"repairing"},{label:"已完成",value:"completed"},{label:"已取消",value:"cancelled"}];r((()=>{Z()}));const Z=async()=>{var e,t;a.value=!0;const l={pageNumber:O.currentPage,pageSize:O.pageSize,searchTerm:Q.code||Q.assetKeyword,type:Q.type,status:"all"===P.value?Q.status:P.value,startDate:null==(e=Q.timeRange)?void 0:e[0],endDate:null==(t=Q.timeRange)?void 0:t[1]};try{const e=await T(l);if(e.success){const a=e.data.items||e.data||[];q.value=a.map((e=>{var a,t,l,r,s,o;return{id:e.id,code:e.orderCode,assetName:(null==(t=null==(a=e.repairItems)?void 0:a[0])?void 0:t.assetName)||"未知资产",assetCode:(null==(r=null==(l=e.repairItems)?void 0:l[0])?void 0:r.assetCode)||"",assetSn:(null==(o=null==(s=e.repairItems)?void 0:s[0])?void 0:o.assetSn)||"",type:ue(e.type),status:pe(e.status),priority:e.priority,supplierName:e.supplierName,estimatedCost:e.estimatedCost,actualCost:e.actualCost,createdAt:e.createdAt,expectedReturnDate:e.expectedReturnDate,actualReturnDate:e.actualReturnDate,notes:e.notes}})),O.total=e.data.total||a.length||0}else s.error(e.message||"获取维修列表失败"),q.value=[],O.total=0}catch(r){s.error("获取维修列表失败"),q.value=[],O.total=0}finally{a.value=!1}},ee=()=>{O.currentPage=1,Z()},ae=()=>{O.currentPage=1,Z()},te=()=>{Q.code="",Q.assetKeyword="",Q.type="",Q.status="",Q.timeRange=[],O.currentPage=1,Z()},le=e=>{O.pageSize=e,Z()},re=e=>{O.currentPage=e,Z()},se=(e,a)=>{let t="",l="",r="";"send"===a?(t="确认发出",l=`确认将资产 ${e.assetName} 发出维修吗？`,r="已更新为维修中状态"):"receive"===a&&(t="确认接收",l=`确认已接收维修完成的资产 ${e.assetName} 吗？`,r="已更新为维修完成状态"),V.confirm(l,t,{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{try{const t=await U.updateReturnStatus(e.id,{status:"send"===a?"sent":"returned",notes:`${"send"===a?"发出维修":"接收维修完成"} - ${(new Date).toLocaleString()}`});t.success?(s.success(r),Z()):s.error(t.message||"状态更新失败")}catch(t){s.error("状态更新失败")}})).catch((()=>{}))},oe=()=>{H.value={},J.value="create",G.value=!0},ne=async e=>{try{const a=await D(e);a.success?(s.success("维修单创建成功"),G.value=!1,await Z()):s.error(a.message||"维修单创建失败")}catch(a){s.error("维修单创建失败")}},ie=()=>{s.success("开始导出数据，请稍候...")},de=e=>({factory:"返厂维修",third_party:"第三方维修",warranty:"保修服务"}[e]||"未知"),ce=e=>({pending:"待发出",repairing:"维修中",completed:"已完成",cancelled:"已取消"}[e]||"未知"),ue=e=>({1:"factory",2:"third_party",3:"warranty"}[e]||"factory"),pe=e=>({0:"pending",1:"pending",2:"repairing",3:"completed",4:"cancelled"}[e]||"pending");return(e,t)=>{const l=m("el-button"),r=m("el-input"),T=m("el-form-item"),D=m("el-option"),ue=m("el-select"),pe=m("el-date-picker"),me=m("el-form"),ye=m("el-card"),ge=m("el-tab-pane"),ve=m("el-tabs"),be=m("el-table-column"),fe=m("el-tag"),we=m("el-table"),he=m("el-pagination"),_e=y("loading");return g(),o("div",F,[n("div",I,[t[11]||(t[11]=n("h2",{class:"page-title"},"返厂/维修管理",-1)),n("div",B,[i(l,{type:"primary",onClick:oe,icon:c(u)},{default:d((()=>t[9]||(t[9]=[v(" 创建维修单 ")]))),_:1},8,["icon"]),i(l,{type:"primary",onClick:ie,icon:c(p)},{default:d((()=>t[10]||(t[10]=[v(" 导出数据 ")]))),_:1},8,["icon"])])]),i(ye,{class:"filter-card"},{default:d((()=>[n("div",K,[i(me,{inline:!0,model:Q,class:"filter-form"},{default:d((()=>[i(T,{label:"单号"},{default:d((()=>[i(r,{modelValue:Q.code,"onUpdate:modelValue":t[0]||(t[0]=e=>Q.code=e),placeholder:"维修单号",clearable:""},null,8,["modelValue"])])),_:1}),i(T,{label:"资产信息"},{default:d((()=>[i(r,{modelValue:Q.assetKeyword,"onUpdate:modelValue":t[1]||(t[1]=e=>Q.assetKeyword=e),placeholder:"资产名称/编号/SN",clearable:""},null,8,["modelValue"])])),_:1}),i(T,{label:"类型"},{default:d((()=>[i(ue,{modelValue:Q.type,"onUpdate:modelValue":t[2]||(t[2]=e=>Q.type=e),placeholder:"全部类型",clearable:""},{default:d((()=>[(g(),o(b,null,f(W,(e=>i(D,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),64))])),_:1},8,["modelValue"])])),_:1}),i(T,{label:"状态"},{default:d((()=>[i(ue,{modelValue:Q.status,"onUpdate:modelValue":t[3]||(t[3]=e=>Q.status=e),placeholder:"全部状态",clearable:""},{default:d((()=>[(g(),o(b,null,f(X,(e=>i(D,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),64))])),_:1},8,["modelValue"])])),_:1}),i(T,{label:"申请时间"},{default:d((()=>[i(pe,{modelValue:Q.timeRange,"onUpdate:modelValue":t[4]||(t[4]=e=>Q.timeRange=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"YYYY-MM-DD"},null,8,["modelValue"])])),_:1}),i(T,null,{default:d((()=>[i(l,{type:"primary",onClick:ae,icon:c(w)},{default:d((()=>t[12]||(t[12]=[v(" 搜索 ")]))),_:1},8,["icon"]),i(l,{onClick:te,icon:c(h)},{default:d((()=>t[13]||(t[13]=[v(" 重置 ")]))),_:1},8,["icon"])])),_:1})])),_:1},8,["model"])])])),_:1}),i(ye,{class:"data-card"},{default:d((()=>[i(ve,{modelValue:P.value,"onUpdate:modelValue":t[5]||(t[5]=e=>P.value=e),onTabClick:ee},{default:d((()=>[i(ge,{label:"全部",name:"all"}),i(ge,{label:"待发出",name:"pending"}),i(ge,{label:"维修中",name:"repairing"}),i(ge,{label:"已完成",name:"completed"})])),_:1},8,["modelValue"]),_((g(),R(we,{ref_key:"maintenanceTable",ref:E,data:q.value,border:"",style:{width:"100%"}},{default:d((()=>[i(be,{prop:"code",label:"维修单号",width:"150",sortable:""}),i(be,{prop:"assetInfo",label:"资产信息",width:"220","show-overflow-tooltip":""},{default:d((e=>[n("div",j,[n("div",Y,C(e.row.assetName),1),n("div",A,C(e.row.assetCode),1),n("div",L,"SN: "+C(e.row.assetSn),1)])])),_:1}),i(be,{prop:"type",label:"类型",width:"100"},{default:d((e=>{return[i(fe,{type:(a=e.row.type,{factory:"primary",third_party:"warning",warranty:"success"}[a]||""),size:"small"},{default:d((()=>[v(C(de(e.row.type)),1)])),_:2},1032,["type"])];var a})),_:1}),i(be,{prop:"status",label:"状态",width:"120"},{default:d((e=>{return[i(fe,{type:(a=e.row.status,{pending:"info",repairing:"warning",completed:"success",cancelled:"danger"}[a]||""),size:"small"},{default:d((()=>[v(C(ce(e.row.status)),1)])),_:2},1032,["type"])];var a})),_:1}),i(be,{prop:"vendor",label:"维修厂商",width:"150"}),i(be,{prop:"applicant",label:"申请人",width:"100"}),i(be,{prop:"applyTime",label:"申请时间",width:"180",sortable:""}),i(be,{prop:"sendTime",label:"发出时间",width:"180",sortable:""}),i(be,{prop:"estimatedReturnTime",label:"预计返回时间",width:"180"}),i(be,{prop:"actualReturnTime",label:"实际返回时间",width:"180"}),i(be,{prop:"cost",label:"维修费用",width:"120"},{default:d((e=>[v(C(e.row.cost?`￥${e.row.cost}`:"-"),1)])),_:1}),i(be,{label:"操作",width:"240",fixed:"right"},{default:d((e=>[i(l,{type:"primary",text:"",size:"small",onClick:a=>{return t=e.row,void s.info(`查看维修单详情：${t.code}`);var t},icon:c($)},{default:d((()=>t[14]||(t[14]=[v(" 详情 ")]))),_:2},1032,["onClick","icon"]),"pending"===e.row.status?(g(),R(l,{key:0,type:"success",text:"",size:"small",onClick:a=>se(e.row,"send"),icon:c(x)},{default:d((()=>t[15]||(t[15]=[v(" 发出 ")]))),_:2},1032,["onClick","icon"])):k("",!0),"repairing"===e.row.status?(g(),R(l,{key:1,type:"warning",text:"",size:"small",onClick:a=>se(e.row,"receive"),icon:c(z)},{default:d((()=>t[16]||(t[16]=[v(" 接收 ")]))),_:2},1032,["onClick","icon"])):k("",!0),"pending"===e.row.status?(g(),R(l,{key:2,type:"danger",text:"",size:"small",onClick:a=>{return t=e.row,void V.confirm(`确认删除维修单 ${t.code} 吗？`,"删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{try{const e=await U.deleteReturnToFactory(t.id);e.success?(s.success("删除成功"),Z()):s.error(e.message||"删除失败")}catch(e){s.error("删除失败")}})).catch((()=>{}));var t},icon:c(S)},{default:d((()=>t[17]||(t[17]=[v(" 删除 ")]))),_:2},1032,["onClick","icon"])):k("",!0)])),_:1})])),_:1},8,["data"])),[[_e,a.value]]),n("div",M,[i(he,{"current-page":O.currentPage,"onUpdate:currentPage":t[6]||(t[6]=e=>O.currentPage=e),"page-size":O.pageSize,"onUpdate:pageSize":t[7]||(t[7]=e=>O.pageSize=e),"page-sizes":[10,20,50,100],background:!0,layout:"total, sizes, prev, pager, next, jumper",total:O.total,onSizeChange:le,onCurrentChange:re},null,8,["current-page","page-size","total"])])])),_:1}),i(N,{visible:G.value,"onUpdate:visible":t[8]||(t[8]=e=>G.value=e),"order-data":H.value,mode:J.value,onSubmit:ne},null,8,["visible","order-data","mode"])])}}},[["__scopeId","data-v-6cd8b896"]]);export{q as default};
