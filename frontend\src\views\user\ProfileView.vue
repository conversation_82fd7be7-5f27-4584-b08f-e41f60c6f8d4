<template>
  <div class="profile-view" v-loading="loading">
    <div class="page-header">个人中心</div>
    
    <el-row :gutter="20" v-if="userStore.userInfo && !loading">
      <!-- 左侧个人信息 -->
      <el-col :span="8">
        <el-card shadow="hover" class="user-card">
          <div class="user-info">
            <div class="avatar-wrapper">
              <el-upload
                class="avatar-uploader"
                action="#" 
                :show-file-list="false"
                :http-request="handleUploadAvatar"
                :before-upload="beforeAvatarUpload"
              >
                <el-avatar :size="100" :src="userStore.computedAvatarUrl || defaultAvatar" class="profile-avatar" />
                <div class="avatar-uploader-icon">
                  <el-icon><Camera /></el-icon>
                  <span>点击更换</span>
                </div>
              </el-upload>
            </div>
            <h2 class="user-name">{{ userStore.userInfo.name || '用户名' }}</h2>
            <div class="user-title">{{ userStore.userInfo.department || '部门' }} - {{ userStore.userInfo.position || '职位' }}</div>
            
            <div class="user-level">
              <span class="level-label">Level {{ gamificationStore.level || 1 }} - {{ gamificationStore.levelTitle }}</span>
              <el-progress :percentage="gamificationStore.currentLevelProgress" :stroke-width="10" status="success">
                <span>{{ gamificationStore.score }} / {{ gamificationStore.nextLevelInfo?.points || 'Max' }}</span>
              </el-progress>
            </div>
            
            <div class="user-meta">
              <div class="meta-item">
                <div class="meta-label">加入时间</div>
                <div class="meta-value">{{ formatJoinDate(userStore.userInfo.joinDate) || '-' }}</div>
              </div>
              <div class="meta-item">
                <div class="meta-label">总经验</div>
                <div class="meta-value">{{ gamificationStore.score }}</div>
              </div>
              <div class="meta-item">
                <div class="meta-label">金币</div>
                <div class="meta-value">
                  <img src="https://cdn-icons-png.flaticon.com/512/2933/2933116.png" class="coin-icon-sm" />
                  {{ userGameCurrency.gold || 0 }}
                </div>
              </div>
              <div class="meta-item">
                <div class="meta-label">钻石</div>
                <div class="meta-value">
                  <img src="https://cdn-icons-png.flaticon.com/512/2933/2933151.png" class="diamond-icon-sm" />
                  {{ userGameCurrency.diamonds || 0 }}
                </div>
              </div>
              <div class="meta-item">
                <div class="meta-label">排名</div>
                <div class="meta-value">{{ userRank || '-' }}</div>
              </div>
            </div>
          </div>
          
          <el-divider />
          
          <div class="contact-info">
            <h3>联系方式</h3>
            <div class="contact-item">
              <el-icon><Message /></el-icon>
              <span>{{ userStore.userInfo.email || '未设置' }}</span>
            </div>
            <div class="contact-item">
              <el-icon><Phone /></el-icon>
              <span>{{ userStore.userInfo.phone || '未设置' }}</span>
            </div>
          </div>
        </el-card>
        
        <!-- 我的标签 -->
        <el-card shadow="hover" class="tags-card">
          <template #header>
            <div class="card-header">
              <span>我的标签</span>
              <el-button link type="primary">编辑</el-button>
            </div>
          </template>
          <div class="tags-content">
            <el-tag v-if="userStore.userInfo.tags && userStore.userInfo.tags.length > 0" v-for="tag in userStore.userInfo.tags" :key="tag" class="user-tag" type="info">{{ tag }}</el-tag>
            <el-empty v-else description="暂无标签" :image-size="50"></el-empty>
          </div>
        </el-card>
        
        <!-- 我的背包 -->
        <el-card shadow="hover" class="inventory-card">
           <template #header>
              <div class="card-header">
                <span>我的背包</span>
              </div>
           </template>
           <div class="inventory-grid" v-if="gamificationStore.inventory.length > 0">
              <div v-for="item in gamificationStore.inventory" :key="item.id" class="inventory-item">
                 <el-badge :value="item.quantity > 1 ? item.quantity : null" type="primary">
                    <el-avatar :size="50" :src="item.icon || defaultItemIcon" class="item-icon" />
                 </el-badge>
                 <div class="item-name" :title="item.description">{{ item.name }}</div>
              </div>
           </div>
            <el-empty v-else description="背包空空如也" :image-size="50"></el-empty>
        </el-card>
      </el-col>
      
      <!-- 右侧内容区 -->
      <el-col :span="16">
        <!-- 任务统计 -->
        <el-card shadow="hover" class="stats-card" v-loading="loadingStats">
          <template #header>
            <div class="card-header">
              <span>任务统计</span>
            </div>
          </template>
          
          <el-row :gutter="20" class="stat-row" v-if="taskStatsData">
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-value">{{ taskStatsData.total || 0 }}</div>
                <div class="stat-label">总任务数</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-value">{{ taskStatsData.completed || 0 }}</div>
                <div class="stat-label">已完成</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-value">{{ taskStatsData.onTimeRate || 0 }}%</div>
                <div class="stat-label">按时完成率</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-value">{{ taskStatsData.pending || 0 }}</div>
                <div class="stat-label">待处理</div>
              </div>
            </el-col>
          </el-row>
          
          <div class="chart-area">
            <div class="chart-placeholder">任务完成趋势图表 (待实现)</div>
          </div>
        </el-card>
        
        <!-- 我的成就 -->
        <el-card shadow="hover" class="achievements-card" v-loading="loadingAchievements">
          <template #header>
            <div class="card-header">
              <span>我的成就</span>
              <el-button link type="primary" @click="showAllAchievements = !showAllAchievements">
                {{ showAllAchievements ? '收起' : `查看全部 (${gamificationStore.achievements.length})` }}
              </el-button>
            </div>
          </template>
          
          <div class="achievements-grid" v-if="gamificationStore.achievements.length > 0">
            <div v-for="achievement in displayedAchievements" :key="achievement.id" class="achievement-item">
              <el-tooltip :content="achievement.description" placement="top">
                 <el-avatar :size="40" :icon="achievement.icon || Trophy" class="achievement-icon" />
              </el-tooltip>
              <div class="achievement-info">
                <div class="achievement-name">{{ achievement.name }}</div>
                <div class="achievement-date">{{ formatTime(achievement.achievedDate) }}</div>
              </div>
            </div>
          </div>
        </el-card>
        
        <!-- 近期任务 -->
        <el-card shadow="hover" class="recent-tasks-card" v-loading="loadingRecentTasks">
          <template #header>
            <div class="card-header">
              <span>近期任务</span>
              <el-button link type="primary" @click="router.push('/main/tasks/list')">查看全部</el-button>
            </div>
          </template>
          
          <el-table :data="recentTasksData" style="width: 100%" v-if="recentTasksData.length > 0">
            <el-table-column prop="title" label="任务名称" min-width="180" show-overflow-tooltip/>
            <el-table-column prop="endDate" label="截止时间" width="120">
               <template #default="{ row }">{{ formatDateShort(row.endDate) }}</template>
            </el-table-column>
            <el-table-column label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="getStatusType(row.status)">
                  {{ getStatusLabel(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="100" align="center">
              <template #default="{ row }">
                <el-button type="primary" link @click="viewTask(row.id)">查看</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>
    <el-empty v-else-if="!loading" description="无法加载用户信息"></el-empty>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { Message, Phone, Trophy, Camera } from '@element-plus/icons-vue'
import { ElMessage, ElProgress, ElTag, ElBadge, ElUpload } from 'element-plus'
import { useUserStore } from '@/stores/modules/user'
import { useGamificationStore } from '@/stores/modules/gamification'
import { taskApi } from '../../api/task.js'
import userApi from '@/api/user'

const router = useRouter()
const userStore = useUserStore()
const gamificationStore = useGamificationStore()

const loading = ref(false)
const loadingStats = ref(false)
const loadingAchievements = ref(false)
const loadingRecentTasks = ref(false)
const avatarUploading = ref(false)

const taskStatsData = ref(null)
const recentTasksData = ref([])
const showAllAchievements = ref(false)

const userGameCurrency = ref({ gold: 0, diamonds: 0 })
const userRank = ref('-')
const defaultItemIcon = 'path/to/default/item/icon.png'

const defaultAvatar = 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png'

const displayedAchievements = computed(() => {
    return showAllAchievements.value 
        ? gamificationStore.achievements 
        : gamificationStore.achievements.slice(0, 6)
})

async function fetchTaskStats() {
    loadingStats.value = true
    try {
        await new Promise(resolve => setTimeout(resolve, 400))
        taskStatsData.value = { total: 75, completed: 58, pending: 5, onTimeRate: 95 }
    } catch (error) {
        console.error("Error fetching task stats:", error)
        ElMessage.error('加载任务统计失败')
        taskStatsData.value = null
    } finally {
        loadingStats.value = false
    }
}

async function fetchRecentTasks() {
    loadingRecentTasks.value = true
    try {
        const params = { pageSize: 5, assignee: userStore.userInfo.id, sortBy: 'createDate', sortOrder: 'desc' }
        const response = await taskApi.getTaskList(params)
        recentTasksData.value = response?.list || []
    } catch (error) {
        console.error("Error fetching recent tasks:", error)
        ElMessage.error('加载近期任务失败')
        recentTasksData.value = []
    } finally {
        loadingRecentTasks.value = false
    }
}

async function fetchGameCurrency() {
    try {
        await new Promise(resolve => setTimeout(resolve, 200))
        userGameCurrency.value = { gold: 1250, diamonds: 55 }
    } catch (error) {
        console.error("Error fetching game currency:", error)
    }
}

async function fetchUserRank() {
    try {
        await new Promise(resolve => setTimeout(resolve, 250))
        userRank.value = '#3'
    } catch (error) {
        console.error("Error fetching user rank:", error)
    }
}

const formatJoinDate = (dateStr) => {
    if (!dateStr) return '-'
    try {
        return new Date(dateStr).toLocaleDateString()
    } catch { return '-' }
}

const formatTime = (timestamp) => {
  if (!timestamp) return '-'
  try {
      return new Date(timestamp).toLocaleString() 
  } catch { return '-' }
}

const formatDateShort = (dateStr) => {
     if (!dateStr) return '-'
     try {
         return new Date(dateStr).toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' })
     } catch { return '-' }
}

const statusMap = {
  unstarted: { label: '未开始', type: 'info' },
  'in-progress': { label: '进行中', type: 'primary' },
  completed: { label: '已完成', type: 'success' },
  overdue: { label: '已逾期', type: 'danger' },
  todo: { label: '待办', type: 'info' }
}

const getStatusLabel = (status) => {
  return statusMap[status]?.label || status
}

const getStatusType = (status) => {
  return statusMap[status]?.type || 'info'
}

const viewTask = (taskId) => {
  router.push({ name: 'TaskDetail', params: { id: taskId } })
}

// Avatar Upload Handlers
const handleUploadAvatar = async (options) => {
  avatarUploading.value = true;
  try {
    // 上传头像文件
    const res = await userApi.uploadAvatar(options.file);

    if (res && res.success && res.data) {
      // 获取上传成功返回的头像信息
      const { avatarUrl, accessUrl } = res.data;
      
      // 优先使用完整URL处理开发/生产环境差异
      if (accessUrl) {
        // 使用完整URL直接更新用户头像，包含域名和端口
        userStore.setAvatarWithFullUrl(accessUrl);
        console.log('头像已使用完整URL更新:', accessUrl);
      } else if (avatarUrl) {
        // 回退到相对路径
        userStore.setAvatar(avatarUrl);
        console.log('头像已使用相对路径更新:', avatarUrl);
      }
      
      ElMessage.success(res.message || '头像更新成功！');
    } else {
      // 处理API响应中的错误
      const errorMessage = res?.message || '头像上传失败，响应数据格式不正确';
      ElMessage.error(errorMessage);
      console.error('头像上传响应错误:', res);
    }
  } catch (error) {
    // 处理网络错误
    console.error('上传头像时发生错误:', error);
    let message = '头像上传失败，请稍后再试';
    if (error?.response?.data?.message) {
        message = error.response.data.message;
    } else if (error?.message) {
        message = error.message;
    }
    ElMessage.error(message);
  } finally {
    avatarUploading.value = false;
  }
};

const beforeAvatarUpload = (rawFile) => {
  const allowedTypes = ['image/jpeg', 'image/png', 'image/gif']
  if (!allowedTypes.includes(rawFile.type)) {
    ElMessage.error('头像图片必须是 JPG, PNG, 或 GIF 格式!')
    return false
  }
  if (rawFile.size / 1024 / 1024 > 5) { // 5MB limit matches backend
    ElMessage.error('头像图片大小不能超过 5MB!')
    return false
  }
  return true
}

onMounted(() => {
  loading.value = true
  if (gamificationStore.score === 0 && !gamificationStore.isLoading) {
       gamificationStore.initializeStore() 
  }
  
  Promise.allSettled([
      fetchTaskStats(),
      fetchRecentTasks(),
      fetchGameCurrency(),
      fetchUserRank()
  ]).finally(() => {
      loading.value = false
  })
})
</script>

<style scoped>
.profile-view {
  padding: 20px;
}

.page-header {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 20px;
}

.user-card {
  text-align: center;
  margin-bottom: 20px;
}

.user-info {
  text-align: center;
  position: relative;
}

.avatar-wrapper {
  position: relative;
  display: inline-block;
  width: 100px;
  height: 100px;
  margin: 0 auto;
  border-radius: 50%;
  overflow: hidden;
}

.avatar-uploader .el-upload {
  border: none;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
  display: inline-block;
  width: 100%;
  height: 100%;
}

.avatar-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.profile-avatar {
  display: block;
  width: 100%;
  height: 100%;
}

.avatar-uploader-icon {
  font-size: 16px;
  color: #fff;
  background-color: rgba(0, 0, 0, 0.5);
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  opacity: 0; /* 默认不显示 */
  transition: opacity var(--el-transition-duration-fast);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border-radius: 50%;

  .el-icon {
    font-size: 24px;
    margin-bottom: 4px;
  }
  span {
    font-size: 12px;
  }
}

.avatar-uploader:hover .avatar-uploader-icon {
  opacity: 1; /* 鼠标悬停时显示 */
}

.user-name {
  margin-top: 15px;
  font-size: 20px;
  margin: 10px 0 5px 0;
}

.user-title {
  color: var(--el-text-color-secondary);
  font-size: 14px;
  margin-bottom: 15px;
}

.user-level {
  margin-bottom: 20px;
  padding: 0 20px;
}

.level-label {
  display: block;
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 8px;
}

.user-level .el-progress {
  margin-bottom: 5px;
}

.user-level .el-progress span {
  font-size: 12px;
  color: var(--el-text-color-placeholder);
}

.user-meta {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
  margin-top: 20px;
}

.meta-item {
  text-align: center;
}

.meta-label {
  font-size: 13px;
  color: var(--el-text-color-secondary);
  margin-bottom: 4px;
}

.meta-value {
  font-size: 16px;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
}

.coin-icon-sm, .diamond-icon-sm {
  width: 16px;
  height: 16px;
  margin-right: 3px;
}

.contact-info {
  text-align: left;
}

.contact-info h3 {
  margin-bottom: 15px;
  font-size: 16px;
}

.contact-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  font-size: 14px;
}

.contact-item .el-icon {
  margin-right: 8px;
  color: var(--el-text-color-secondary);
}

.tags-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tags-content {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  min-height: 50px;
}

.user-tag {
  cursor: default;
}

.stats-card {
  margin-bottom: 20px;
}

.stat-row {
  margin-bottom: 20px;
}

.stat-item {
  text-align: center;
  padding: 15px 10px;
  background-color: var(--el-fill-color-lighter);
  border-radius: var(--el-border-radius-base);
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 5px;
  color: var(--el-color-primary);
}

.stat-label {
  font-size: 14px;
  color: var(--el-text-color-secondary);
}

.chart-area {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--el-fill-color-lighter);
  border-radius: var(--el-border-radius-base);
  color: var(--el-text-color-placeholder);
}

.achievements-card {
  margin-bottom: 20px;
}

.achievements-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 15px;
}

.achievement-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  background-color: var(--el-fill-color-lighter);
  border-radius: var(--el-border-radius-base);
}

.achievement-icon {
  flex-shrink: 0;
}

.achievement-info {
  flex-grow: 1;
}

.achievement-name {
  font-weight: bold;
  margin-bottom: 3px;
}

.achievement-desc {
  font-size: 13px;
  color: var(--el-text-color-secondary);
  margin-bottom: 5px;
}

.achievement-date {
  font-size: 12px;
  color: var(--el-text-color-placeholder);
}

.recent-tasks-card {
  /* Add specific styling if needed */
}

:deep(.el-card__body) {
  padding: 20px;
}
</style> 