// File: Core/Hubs/NotificationHub.cs
// Description: SignalR Hub用于实时推送通知

using Microsoft.AspNetCore.SignalR;
using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;

namespace ItAssetsSystem.Core.Hubs
{
    /// <summary>
    /// 通知Hub，用于实时推送通知到客户端
    /// </summary>
    // 移除了[Authorize]注解，允许匿名访问，避免认证问题
    [EnableCors("MyAllowSpecificOrigins")]
    public class NotificationHub : Hub
    {
        private readonly ILogger<NotificationHub> _logger;

        public NotificationHub(ILogger<NotificationHub> logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// 用户加入特定组，如任务组或个人通知组
        /// </summary>
        /// <param name="groupName">组名称</param>
        public async Task JoinGroup(string groupName)
        {
            try
            {
                await Groups.AddToGroupAsync(Context.ConnectionId, groupName);
                _logger.LogInformation("用户连接 {ConnectionId} 加入组 {GroupName}", Context.ConnectionId, groupName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加入组 {GroupName} 失败", groupName);
            }
        }

        /// <summary>
        /// 用户离开特定组
        /// </summary>
        /// <param name="groupName">组名称</param>
        public async Task LeaveGroup(string groupName)
        {
            try
            {
                await Groups.RemoveFromGroupAsync(Context.ConnectionId, groupName);
                _logger.LogInformation("用户连接 {ConnectionId} 离开组 {GroupName}", Context.ConnectionId, groupName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "离开组 {GroupName} 失败", groupName);
            }
        }

        /// <summary>
        /// 用户上线
        /// </summary>
        /// <param name="userId">用户ID</param>
        public async Task UserOnline(int userId)
        {
            try
            {
                // 将用户加入到个人通知组
                await JoinGroup($"user_{userId}");
                _logger.LogInformation("用户 {UserId} 上线, ConnectionId: {ConnectionId}", userId, Context.ConnectionId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "用户 {UserId} 上线处理失败", userId);
            }
        }

        /// <summary>
        /// 连接建立时
        /// </summary>
        public override async Task OnConnectedAsync()
        {
            _logger.LogInformation("客户端连接建立: {ConnectionId}", Context.ConnectionId);

            // 尝试从查询字符串获取用户ID
            var userId = Context.GetHttpContext()?.Request.Query["userId"].ToString();
            if (!string.IsNullOrEmpty(userId) && int.TryParse(userId, out int userIdInt))
            {
                await JoinGroup($"user_{userIdInt}");
                _logger.LogInformation("从查询字符串自动将用户 {UserId} 加入到通知组", userIdInt);
            }

            await base.OnConnectedAsync();
        }

        /// <summary>
        /// 连接断开时
        /// </summary>
        /// <param name="exception">异常信息</param>
        public override async Task OnDisconnectedAsync(Exception exception)
        {
            _logger.LogInformation("客户端连接断开: {ConnectionId}, 异常: {Exception}", 
                Context.ConnectionId, exception?.Message ?? "无");
            await base.OnDisconnectedAsync(exception);
        }

        /// <summary>
        /// 测试连接
        /// </summary>
        /// <returns>成功消息</returns>
        public string TestConnection()
        {
            _logger.LogInformation("TestConnection被调用，ConnectionId: {ConnectionId}", Context.ConnectionId);
            return "连接成功";
        }
    }
} 