// File: Core/Abstractions/ITaskRepository.cs
// Description: 任务仓储接口，定义任务相关数据访问方法 (V2 完全更新)

#nullable enable
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
// using ItAssetsSystem.Models.Entities; // No longer directly using old core entities here for Task module
// using ItAssetsSystem.Models.Enums; // Enums will be handled as strings if passed to repo

// V2 Entities for Task module
using ItAssetsSystem.Domain.Entities.Tasks;
using EntityTask = ItAssetsSystem.Domain.Entities.Tasks.Task; // Alias for our Task entity
using PagedResultType = ItAssetsSystem.Core.Interfaces.Services.PagedResult<ItAssetsSystem.Domain.Entities.Tasks.Comment>;
// Keep other using directives if other Core entities/enums are used by other methods not part of Task module V2 in this interface

namespace ItAssetsSystem.Core.Abstractions
{
    /// <summary>
    /// 任务仓储接口 (V2)
    /// </summary>
    public interface ITaskRepository
    {
        #region 任务操作 (V2)

        /// <summary>
        /// 获取所有任务 (V2)
        /// </summary>
        /// <returns>任务列表</returns>
        Task<List<EntityTask>> GetAllTasksAsync();

        /// <summary>
        /// 获取任务分页列表 (V2)
        /// </summary>
        /// <param name="pageNumber">页码</param>
        /// <param name="pageSize">每页大小</param>
        /// <param name="searchTerm">搜索关键词</param>
        /// <param name="status">状态过滤 (string)</param>
        /// <param name="priority">优先级过滤 (string)</param>
        /// <param name="taskType">任务类型过滤 (string)</param>
        /// <param name="assigneeUserId">负责人ID过滤</param>
        /// <param name="creatorUserId">创建者ID过滤</param>
        /// <param name="assetId">资产ID过滤</param>
        /// <param name="locationId">位置ID过滤</param>
        /// <param name="fromDate">开始日期过滤</param>
        /// <param name="toDate">结束日期过滤</param>
        /// <param name="includeDeleted">是否包含已删除任务 (V2 Task does not have IsDeleted, handle soft delete via Status or a new field if needed)</param>
        /// <param name="parentTaskId">父任务ID过滤</param>
        /// <param name="projectId">项目ID过滤</param>
        /// <param name="sortBy">排序字段</param>
        /// <param name="sortDirection">排序方向</param>
        /// <returns>任务分页列表</returns>
        Task<(List<EntityTask> Items, int TotalCount)> GetTasksPagedAsync(
            int pageNumber,
            int pageSize,
            string? searchTerm = null,
            string? status = null,
            string? priority = null,
            string? taskType = null,
            int? assigneeUserId = null,
            int? creatorUserId = null,
            int? assetId = null,
            int? locationId = null,
            DateTime? fromDate = null,
            DateTime? toDate = null,
            bool includeDeleted = false,
            long? parentTaskId = null,
            long? projectId = null,
            string? sortBy = null,
            string? sortDirection = null);

        /// <summary>
        /// 根据ID获取任务 (V2)
        /// </summary>
        /// <param name="taskId">任务ID (long)</param>
        /// <returns>任务实体</returns>
        Task<EntityTask?> GetTaskByIdAsync(long taskId);

        /// <summary>
        /// 根据ID获取任务（包含关联数据）(V2)
        /// </summary>
        /// <param name="taskId">任务ID (long)</param>
        /// <returns>任务实体（包含评论、附件、历史记录等）</returns>
        Task<EntityTask?> GetTaskByIdWithDetailsAsync(long taskId);

        /// <summary>
        /// 添加任务 (V2)
        /// </summary>
        /// <param name="task">任务实体 (V2)</param>
        /// <returns>添加后的任务实体</returns>
        Task<EntityTask> AddTaskAsync(EntityTask task);

        /// <summary>
        /// 更新任务 (V2)
        /// </summary>
        /// <param name="task">任务实体 (V2)</param>
        /// <returns>操作是否成功</returns>
        Task<bool> UpdateTaskAsync(EntityTask task);

        /// <summary>
        /// 删除任务 (V2 - 实现通常是软删除)
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <returns>操作是否成功</returns>
        Task<bool> DeleteTaskAsync(long taskId);

        /// <summary>
        /// 获取用户的任务数量 (V2)
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="status">状态过滤 (string)</param>
        /// <returns>任务数量</returns>
        Task<int> GetUserTaskCountAsync(int userId, string? status = null);

        /// <summary>
        /// 检查任务是否存在 (V2)
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <returns>任务是否存在</returns>
        Task<bool> TaskExistsAsync(long taskId);

        // These specific update methods might be handled by UpdateTaskAsync with more granular DTOs in service layer
        // Or kept if direct repo calls for these are preferred.
        // For now, assume they are updated to use V2 Task or string parameters as appropriate.

        /// <summary>
        /// 更新任务状态 (V2)
        /// </summary>
        Task<bool> UpdateTaskStatusAsync(long taskId, string newStatus, int? updatedByUserId);

        /// <summary>
        /// 分配任务 (V2 - 主要更新Task.AssigneeUserId, TaskAssignees表通过 AddAssigneeAsync 等单独管理)
        /// </summary>
        Task<bool> AssignTaskAsync(long taskId, int assigneeUserId, int? assignedByUserId);

        /// <summary>
        /// 更新任务进度 (V2)
        /// </summary>
        Task<bool> UpdateTaskProgressAsync(long taskId, int progress, int? updatedByUserId);

        /// <summary>
        /// 完成任务 (V2)
        /// </summary>
        Task<bool> CompleteTaskAsync(long taskId, int? completedByUserId);

        /// <summary>
        /// 更新任务的PDCA阶段 (V2)
        /// </summary>
        Task<bool> UpdateTaskPdcaStageAsync(long taskId, string stage, int? updatedByUserId);

        /// <summary>
        /// 获取逾期任务列表 (V2)
        /// </summary>
        /// <returns>逾期任务列表</returns>
        Task<List<EntityTask>> GetOverdueTasksAsync();

        /// <summary>
        /// 获取子任务数量 (V2)
        /// </summary>
        /// <param name="parentTaskId">父任务ID</param>
        /// <returns>子任务数量</returns>
        Task<int> GetSubTaskCountAsync(long parentTaskId);

        #endregion

        #region 评论操作 (V2)

        /// <summary>
        /// 获取任务的评论列表 (V2)
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <returns>评论列表</returns>
        Task<List<Comment>> GetCommentsByTaskIdAsync(long taskId);

        /// <summary>
        /// 根据ID获取评论 (V2)
        /// </summary>
        /// <param name="commentId">评论ID</param>
        /// <returns>评论实体</returns>
        Task<Comment?> GetCommentByIdAsync(long commentId);

        /// <summary>
        /// 添加评论 (V2)
        /// </summary>
        /// <param name="comment">评论实体</param>
        /// <returns>添加后的评论实体</returns>
        Task<Comment> AddCommentAsync(Comment comment);

        /// <summary>
        /// 获取用户在指定时间范围内的最近评论 (用于防重复提交)
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <param name="userId">用户ID</param>
        /// <param name="timeSpan">时间范围</param>
        /// <returns>最近评论列表</returns>
        Task<List<Comment>> GetRecentCommentsByUserAsync(long taskId, int userId, TimeSpan timeSpan);

        /// <summary>
        /// 更新评论 (V2)
        /// </summary>
        /// <param name="comment">评论实体</param>
        /// <returns>操作是否成功</returns>
        Task<bool> UpdateCommentAsync(Comment comment);

        /// <summary>
        /// 删除评论 (V2 - 实现通常是软删除)
        /// </summary>
        /// <param name="commentId">评论ID</param>
        /// <returns>操作是否成功</returns>
        Task<bool> DeleteCommentAsync(long commentId);

        /// <summary>
        /// 置顶评论 (V2)
        /// </summary>
        /// <param name="commentId">评论ID</param>
        /// <param name="isPinned">是否置顶</param>
        /// <returns>操作是否成功</returns>
        Task<bool> PinCommentAsync(long commentId, bool isPinned);

        /// <summary>
        /// 检查评论是否存在 (V2)
        /// </summary>
        /// <param name="commentId">评论ID</param>
        /// <param name="taskId">任务ID</param>
        /// <returns>评论是否存在</returns>
        Task<bool> CommentExistsAsync(long commentId, long taskId);

        /// <summary>
        /// 获取评论数量 (V2)
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <returns>评论数量</returns>
        Task<int> GetCommentCountAsync(long taskId);

        /// <summary>
        /// 搜索评论 (V2)
        /// </summary>
        /// <param name="taskId">任务ID（可选）</param>
        /// <param name="searchKeyword">搜索关键词</param>
        /// <param name="userId">用户ID（可选）</param>
        /// <param name="startDate">开始日期（可选）</param>
        /// <param name="endDate">结束日期（可选）</param>
        /// <param name="isPinned">是否置顶（可选）</param>
        /// <param name="pageNumber">页码</param>
        /// <param name="pageSize">每页大小</param>
        /// <param name="sortBy">排序字段</param>
        /// <param name="sortDirection">排序方向</param>
        /// <returns>分页评论结果</returns>
        Task<PagedResultType> SearchCommentsAsync(
            long? taskId = null,
            string searchKeyword = "",
            int? userId = null,
            DateTime? startDate = null,
            DateTime? endDate = null,
            bool? isPinned = null,
            int pageNumber = 1,
            int pageSize = 20,
            string sortBy = "CreationTimestamp",
            string sortDirection = "desc",
            bool? topLevelOnly = null,
            bool? isEdited = null,
            bool includeDeleted = false,
            int? minContentLength = null,
            int? maxContentLength = null,
            bool? hasMentions = null);

        /// <summary>
        /// 获取任务的评论树结构（包含回复层次）
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <param name="maxDepth">最大深度（默认3层）</param>
        /// <param name="sortBy">排序字段</param>
        /// <param name="sortDirection">排序方向</param>
        /// <returns>评论树结构</returns>
        Task<List<Comment>> GetCommentTreeAsync(long taskId, int maxDepth = 3, string sortBy = "CreationTimestamp", string sortDirection = "asc");

        /// <summary>
        /// 获取评论的所有回复（递归）
        /// </summary>
        /// <param name="parentCommentId">父评论ID</param>
        /// <param name="maxDepth">最大深度</param>
        /// <returns>回复列表</returns>
        Task<List<Comment>> GetCommentRepliesAsync(long parentCommentId, int maxDepth = 3);

        #endregion

        #region 附件操作 (V2)

        /// <summary>
        /// 获取任务的附件列表 (V2)
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <returns>附件列表</returns>
        Task<List<Attachment>> GetAttachmentsByTaskIdAsync(long taskId);

        /// <summary>
        /// 获取评论的附件列表 (V2)
        /// </summary>
        /// <param name="commentId">评论ID</param>
        /// <returns>评论附件列表</returns>
        Task<List<Attachment>> GetCommentAttachmentsAsync(long commentId);

        /// <summary>
        /// 根据ID获取附件 (V2)
        /// </summary>
        /// <param name="attachmentId">附件ID</param>
        /// <returns>附件实体</returns>
        Task<Attachment?> GetAttachmentByIdAsync(long attachmentId);

        /// <summary>
        /// 添加附件 (V2)
        /// </summary>
        /// <param name="attachment">附件实体</param>
        /// <returns>添加后的附件实体</returns>
        Task<Attachment> AddAttachmentAsync(Attachment attachment);

        /// <summary>
        /// 更新附件 (V2)
        /// </summary>
        /// <param name="attachment">附件实体</param>
        /// <returns>操作是否成功</returns>
        Task<bool> UpdateAttachmentAsync(Attachment attachment);

        /// <summary>
        /// 删除附件 (V2 - 实现通常是软删除)
        /// </summary>
        /// <param name="attachmentId">附件ID</param>
        /// <returns>操作是否成功</returns>
        Task<bool> DeleteAttachmentAsync(long attachmentId);

        /// <summary>
        /// 获取附件数量 (V2)
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <returns>附件数量</returns>
        Task<int> GetAttachmentCountAsync(long taskId);

        /// <summary>
        /// 批量获取任务的子任务数量 (V2 性能优化)
        /// </summary>
        /// <param name="taskIds">任务ID列表</param>
        /// <returns>任务ID与子任务数量的字典</returns>
        Task<Dictionary<long, int>> GetSubTaskCountsBatchAsync(List<long> taskIds);

        /// <summary>
        /// 批量获取任务的评论数量 (V2 性能优化)
        /// </summary>
        /// <param name="taskIds">任务ID列表</param>
        /// <returns>任务ID与评论数量的字典</returns>
        Task<Dictionary<long, int>> GetCommentCountsBatchAsync(List<long> taskIds);

        /// <summary>
        /// 批量获取任务的附件数量 (V2 性能优化)
        /// </summary>
        /// <param name="taskIds">任务ID列表</param>
        /// <returns>任务ID与附件数量的字典</returns>
        Task<Dictionary<long, int>> GetAttachmentCountsBatchAsync(List<long> taskIds);

        /// <summary>
        /// 批量获取任务实体 (V2 性能优化)
        /// </summary>
        /// <param name="taskIds">任务ID列表</param>
        /// <returns>任务实体列表</returns>
        Task<List<EntityTask>> GetTasksByIdsAsync(List<long> taskIds);

        #endregion

        #region 任务历史记录操作 (V2)

        /// <summary>
        /// 获取任务的历史记录列表 (V2)
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <returns>任务历史记录列表</returns>
        Task<List<TaskHistory>> GetHistoryByTaskIdAsync(long taskId);

        /// <summary>
        /// 添加任务历史记录 (V2)
        /// </summary>
        /// <param name="history">任务历史记录实体</param>
        /// <returns>添加后的任务历史记录实体</returns>
        Task<TaskHistory> AddTaskHistoryAsync(TaskHistory history);

        #endregion

        #region 任务负责人操作 (V2)
        /// <summary>
        /// 添加任务负责人/参与者 (V2)
        /// </summary>
        /// <param name="assignee">任务负责人/参与者实体</param>
        /// <returns>添加后的任务负责人/参与者实体</returns>
        Task<TaskAssignee> AddAssigneeAsync(TaskAssignee assignee);

        /// <summary>
        /// 移除任务负责人/参与者 (V2)
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <param name="userId">用户ID</param>
        /// <param name="assignmentType">任务类型</param>
        /// <returns>操作是否成功</returns>
        Task<bool> RemoveAssigneeAsync(long taskId, int userId, string assignmentType = "Assignee");
        
        /// <summary>
        /// 根据任务ID获取所有负责人/参与者 (V2)
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <returns>任务负责人/参与者列表</returns>
        Task<List<TaskAssignee>> GetAssigneesByTaskIdAsync(long taskId);

        /// <summary>
        /// 批量获取多个任务的所有负责人/参与者 (V2)
        /// </summary>
        /// <param name="taskIds">任务ID列表</param>
        /// <returns>任务负责人/参与者列表</returns>
        Task<List<TaskAssignee>> GetTaskAssigneesByTaskIdsAsync(List<long> taskIds);

        /// <summary>
        /// 添加或更新任务负责人/参与者 (V2)
        /// </summary>
        /// <param name="assignee">任务负责人/参与者实体</param>
        /// <returns>操作是否成功</returns>
        Task<bool> AddOrUpdateAssigneeAsync(TaskAssignee assignee);

        #endregion

        #region 周期性任务计划操作 (V2 Entities - PeriodicTaskSchedule)
        // Ensure these methods use Domain.Entities.Tasks.PeriodicTaskSchedule

        /// <summary>
        /// 获取周期性任务计划列表（分页）
        /// </summary>
        /// <param name="parameters">查询参数</param>
        /// <returns>周期性任务计划分页列表</returns>
        Task<(List<PeriodicTaskSchedule> Items, int TotalCount)> GetPeriodicSchedulesPagedAsync(Application.Features.Tasks.Dtos.PeriodicTaskScheduleQueryParametersDto parameters);

        /// <summary>
        /// 获取所有周期性任务计划
        /// </summary>
        /// <param name="includeDisabled">是否包含已禁用的计划</param>
        /// <returns>周期性任务计划列表</returns>
        Task<List<PeriodicTaskSchedule>> GetAllPeriodicSchedulesAsync(bool includeDisabled = false);

        /// <summary>
        /// 获取需要生成任务的周期性计划
        /// </summary>
        /// <param name="currentTime">当前时间</param>
        /// <returns>需要生成任务的周期性计划列表</returns>
        Task<List<PeriodicTaskSchedule>> GetPeriodicSchedulesDueForGenerationAsync(DateTime currentTime);

        /// <summary>
        /// 根据ID获取周期性任务计划
        /// </summary>
        /// <param name="id">计划ID</param>
        /// <returns>周期性任务计划实体</returns>
        Task<PeriodicTaskSchedule?> GetPeriodicScheduleByIdAsync(long id);

        /// <summary>
        /// 添加周期性任务计划
        /// </summary>
        /// <param name="schedule">周期性任务计划实体</param>
        /// <returns>添加后的周期性任务计划实体</returns>
        Task<PeriodicTaskSchedule> AddPeriodicScheduleAsync(PeriodicTaskSchedule schedule);

        /// <summary>
        /// 更新周期性任务计划
        /// </summary>
        /// <param name="schedule">周期性任务计划实体</param>
        /// <returns>操作是否成功</returns>
        Task<bool> UpdatePeriodicScheduleAsync(PeriodicTaskSchedule schedule);

        /// <summary>
        /// 删除周期性任务计划（软删除）
        /// </summary>
        /// <param name="id">计划ID</param>
        /// <returns>操作是否成功</returns>
        Task<bool> DeletePeriodicScheduleAsync(long id);

        /// <summary>
        /// 启用或禁用周期性任务计划
        /// </summary>
        /// <param name="id">计划ID</param>
        /// <param name="isEnabled">是否启用</param>
        /// <returns>操作是否成功</returns>
        Task<bool> EnablePeriodicScheduleAsync(long id, bool isEnabled);

        /// <summary>
        /// 更新周期性任务计划的下次生成时间
        /// </summary>
        /// <param name="id">计划ID</param>
        /// <param name="nextGenerationTime">下次生成时间</param>
        /// <returns>操作是否成功</returns>
        Task<bool> UpdateNextGenerationTimeAsync(long id, DateTime nextGenerationTime);

        /// <summary>
        /// 更新周期性任务计划的生成统计信息
        /// </summary>
        /// <param name="id">计划ID</param>
        /// <param name="generatedCount">本次生成的任务数量</param>
        /// <param name="nextGenerationTime">下次生成时间（可选）</param>
        /// <returns>操作是否成功</returns>
        Task<bool> UpdateGenerationStatisticsAsync(long id, int generatedCount, DateTime? nextGenerationTime = null);

        /// <summary>
        /// 添加周期性任务计划负责人关联
        /// </summary>
        /// <param name="assignee">负责人关联实体</param>
        /// <returns>添加的实体</returns>
        Task<PeriodicTaskScheduleAssignee> AddPeriodicScheduleAssigneeAsync(PeriodicTaskScheduleAssignee assignee);

        /// <summary>
        /// 获取周期性任务计划的所有负责人
        /// </summary>
        /// <param name="scheduleId">计划ID</param>
        /// <returns>负责人列表</returns>
        Task<List<PeriodicTaskScheduleAssignee>> GetPeriodicScheduleAssigneesAsync(long scheduleId);

        /// <summary>
        /// 删除周期性任务计划的所有负责人关联
        /// </summary>
        /// <param name="scheduleId">计划ID</param>
        /// <returns>操作是否成功</returns>
        Task<bool> DeletePeriodicScheduleAssigneesAsync(long scheduleId);

        /// <summary>
        /// 根据ID获取周期性任务计划
        /// </summary>
        /// <param name="scheduleId">计划ID</param>
        /// <param name="includeTasks">是否包含任务</param>
        /// <returns>周期性任务计划实体</returns>
        Task<PeriodicTaskSchedule?> GetPeriodicTaskScheduleByIdAsync(long scheduleId, bool includeTasks = false);

        /// <summary>
        /// 检查周期性任务计划在指定时间窗口内是否已生成任务
        /// </summary>
        /// <param name="scheduleId">计划ID</param>
        /// <param name="targetTime">目标时间</param>
        /// <param name="windowMinutes">时间窗口（分钟）</param>
        /// <returns>如果已存在任务则返回任务实体，否则返回null</returns>
        Task<EntityTask?> GetTaskByScheduleAndTimeWindowAsync(long scheduleId, DateTime targetTime, int windowMinutes = 5);

        /// <summary>
        /// 根据ID获取周期性任务计划名称
        /// </summary>
        /// <param name="scheduleId">计划ID</param>
        /// <returns>周期性任务计划名称</returns>
        Task<string?> GetPeriodicTaskScheduleNameByIdAsync(long scheduleId);

        #endregion

        #region PDCA计划操作 (V2 Entities - PdcaPlan)
        // Ensure these methods use Domain.Entities.Tasks.PdcaPlan

        /// <summary>
        /// 获取所有PDCA计划
        /// </summary>
        /// <param name="includeArchived">是否包含已归档的计划</param>
        /// <returns>PDCA计划列表</returns>
        Task<List<PdcaPlan>> GetAllPdcaPlansAsync(bool includeArchived = false);

        /// <summary>
        /// 根据ID获取PDCA计划
        /// </summary>
        /// <param name="id">计划ID</param>
        /// <returns>PDCA计划实体</returns>
        Task<PdcaPlan?> GetPdcaPlanByIdAsync(long id);

        /// <summary>
        /// 根据ID获取PDCA计划（包含关联任务）
        /// </summary>
        /// <param name="id">计划ID</param>
        /// <returns>PDCA计划实体（包含关联任务）</returns>
        Task<PdcaPlan?> GetPdcaPlanByIdWithTasksAsync(long id);

        /// <summary>
        /// 添加PDCA计划
        /// </summary>
        /// <param name="plan">PDCA计划实体</param>
        /// <returns>添加后的PDCA计划实体</returns>
        Task<PdcaPlan> AddPdcaPlanAsync(PdcaPlan plan);

        /// <summary>
        /// 更新PDCA计划
        /// </summary>
        /// <param name="plan">PDCA计划实体</param>
        /// <returns>操作是否成功</returns>
        Task<bool> UpdatePdcaPlanAsync(PdcaPlan plan);

        /// <summary>
        /// 删除PDCA计划（软删除）
        /// </summary>
        /// <param name="id">计划ID</param>
        /// <returns>操作是否成功</returns>
        Task<bool> DeletePdcaPlanAsync(long id);

        /// <summary>
        /// 更新PDCA计划阶段
        /// </summary>
        /// <param name="id">计划ID</param>
        /// <param name="stage">新阶段</param>
        /// <param name="updatedByUserId">操作者ID</param>
        /// <returns>操作是否成功</returns>
        Task<bool> UpdatePdcaPlanStageAsync(long id, string stage, int? updatedByUserId);

        /// <summary>
        /// 归档PDCA计划
        /// </summary>
        /// <param name="id">计划ID</param>
        /// <returns>操作是否成功</returns>
        Task<bool> ArchivePdcaPlanAsync(long id);

        #endregion

        /// <summary>
        /// 根据ID获取任务名称 (V2)
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <returns>任务名称</returns>
        Task<string?> GetTaskNameByIdAsync(long taskId);
    }
} 