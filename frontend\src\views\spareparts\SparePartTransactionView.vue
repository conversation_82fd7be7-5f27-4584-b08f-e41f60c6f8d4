<template>
  <div class="spare-part-transaction-view">
    <div class="page-header">
      <h2>出入库记录</h2>
    </div>

    <!-- 搜索和筛选区域 -->
    <div class="filter-container">
      <el-form :inline="true" :model="queryParams" class="demo-form-inline">
        <el-form-item label="备件名称">
          <el-input v-model="queryParams.partName" placeholder="备件名称" clearable @keyup.enter="handleQuery" />
        </el-form-item>
        <el-form-item label="备件编号">
          <el-input v-model="queryParams.partCode" placeholder="备件编号" clearable @keyup.enter="handleQuery" />
        </el-form-item>
        <el-form-item label="操作类型">
          <el-select v-model="queryParams.type" placeholder="操作类型" clearable>
            <el-option label="入库" :value="1" />
            <el-option label="出库" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="原因类型">
          <el-select v-model="queryParams.reasonType" placeholder="原因类型" clearable>
            <el-option label="采购入库" :value="1" />
            <el-option label="退回入库" :value="2" />
            <el-option label="领用出库" :value="3" />
            <el-option label="报废出库" :value="4" />
            <el-option label="盘点调整" :value="5" />
          </el-select>
        </el-form-item>
        <el-form-item label="操作时间">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
            :shortcuts="dateShortcuts"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 表格区域 -->
    <el-table
      v-loading="loading"
      :data="transactionList"
      border
      style="width: 100%"
      @sort-change="handleSortChange"
    >
      <el-table-column prop="id" label="记录ID" width="80" sortable="custom" />
      <el-table-column prop="partCode" label="备件编号" width="120" sortable="custom" />
      <el-table-column prop="partName" label="备件名称" width="150" sortable="custom" />
      <el-table-column prop="typeName" label="操作类型" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.type === 1 ? 'success' : 'warning'">
            {{ scope.row.typeName }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="quantity" label="数量" width="100">
        <template #default="scope">
          <span :class="scope.row.type === 1 ? 'quantity-in' : 'quantity-out'">
            {{ scope.row.type === 1 ? '+' : '-' }}{{ Math.abs(scope.row.quantity) }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="stockAfter" label="操作后库存" width="120" />
      <el-table-column prop="reasonTypeName" label="原因类型" width="120" />
      <el-table-column prop="locationName" label="库位" width="120" />
      <el-table-column prop="operationTime" label="操作时间" width="180" sortable="custom">
        <template #default="scope">
          {{ formatDateTime(scope.row.operationTime) }}
        </template>
      </el-table-column>
      <el-table-column prop="operatorName" label="操作人" width="120" />
      <el-table-column prop="referenceNumber" label="关联单号" width="150" />
      <el-table-column prop="remarks" label="备注" min-width="200" show-overflow-tooltip />
    </el-table>

    <!-- 分页组件 -->
    <div class="pagination-container">
      <el-pagination
        v-model:currentPage="queryParams.pageIndex"
        v-model:page-size="queryParams.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { ElMessage } from 'element-plus';
import * as sparePartsApi from '@/api/spareparts';

// 创建响应式状态
const loading = ref(false);
const transactionList = ref([]);
const total = ref(0);
const dateRange = ref([]);

// 查询参数
const queryParams = reactive({
  pageIndex: 1,
  pageSize: 10,
  partName: '',
  partCode: '',
  type: '',
  reasonType: '',
  startDate: '',
  endDate: '',
  sortBy: '',
  sortOrder: ''
});

// 日期快捷选项
const dateShortcuts = [
  {
    text: '最近一周',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    }
  },
  {
    text: '最近一个月',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    }
  },
  {
    text: '最近三个月',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
      return [start, end];
    }
  }
];

// 监听日期范围变化
const watchDateRange = computed(() => {
  if (dateRange.value && dateRange.value.length === 2) {
    queryParams.startDate = dateRange.value[0];
    queryParams.endDate = dateRange.value[1];
  } else {
    queryParams.startDate = '';
    queryParams.endDate = '';
  }
  return dateRange.value;
});

// 生命周期钩子
onMounted(() => {
  fetchTransactionList();
});

// 获取出入库记录数据
const fetchTransactionList = async () => {
  loading.value = true;
  try {
    // 构建查询参数
    const params = {
      pageIndex: queryParams.pageIndex,
      pageSize: queryParams.pageSize,
      partName: queryParams.partName || undefined,
      partCode: queryParams.partCode || undefined,
      type: queryParams.type || undefined,
      reasonType: queryParams.reasonType || undefined,
      startDate: queryParams.startDate || undefined,
      endDate: queryParams.endDate || undefined,
      sortBy: queryParams.sortBy || undefined,
      sortOrder: queryParams.sortOrder || undefined
    };

    const response = await sparePartsApi.getSparePartTransactions(params);
    if (response.success) {
      transactionList.value = response.data.items;
      total.value = response.data.totalCount;
    } else {
      ElMessage.error(response.message || '获取出入库记录失败');
    }
  } catch (error) {
    console.error('获取出入库记录出错:', error);
    ElMessage.error('获取出入库记录失败，请稍后重试');
  } finally {
    loading.value = false;
  }
};

// 格式化日期时间
const formatDateTime = (dateTimeStr) => {
  if (!dateTimeStr) return '';
  const date = new Date(dateTimeStr);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  });
};

// 处理查询
const handleQuery = () => {
  queryParams.pageIndex = 1;
  fetchTransactionList();
};

// 重置查询
const resetQuery = () => {
  queryParams.partName = '';
  queryParams.partCode = '';
  queryParams.type = '';
  queryParams.reasonType = '';
  dateRange.value = [];
  queryParams.startDate = '';
  queryParams.endDate = '';
  queryParams.sortBy = '';
  queryParams.sortOrder = '';
  handleQuery();
};

// 处理排序变化
const handleSortChange = (column) => {
  if (column.prop && column.order) {
    queryParams.sortBy = column.prop;
    queryParams.sortOrder = column.order === 'ascending' ? 'asc' : 'desc';
  } else {
    queryParams.sortBy = '';
    queryParams.sortOrder = '';
  }
  fetchTransactionList();
};

// 处理分页大小变化
const handleSizeChange = (size) => {
  queryParams.pageSize = size;
  fetchTransactionList();
};

// 处理页码变化
const handleCurrentChange = (page) => {
  queryParams.pageIndex = page;
  fetchTransactionList();
};
</script>

<style scoped>
.spare-part-transaction-view {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.filter-container {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.quantity-in {
  color: #67c23a;
  font-weight: bold;
}

.quantity-out {
  color: #f56c6c;
  font-weight: bold;
}
</style> 