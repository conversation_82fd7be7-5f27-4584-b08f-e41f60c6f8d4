// File: Application/Features/SpareParts/Dtos/SparePartOutboundRequest.cs
// Description: 备品备件出库请求数据传输对象

using System.ComponentModel.DataAnnotations;

namespace ItAssetsSystem.Application.Features.SpareParts.Dtos
{
    /// <summary>
    /// 备品备件出库请求数据传输对象
    /// </summary>
    public class SparePartOutboundRequest : SparePartTransactionRequest
    {
        /// <summary>
        /// 原因类型: 3=领用出库, 4=报废出库, 5=盘点调整
        /// </summary>
        [Required(ErrorMessage = "原因类型不能为空")]
        [Range(3, 5, ErrorMessage = "原因类型无效")]
        public new byte ReasonType { get; set; }
        
        /// <summary>
        /// 关联单号
        /// </summary>
        [StringLength(100, ErrorMessage = "关联单号长度不能超过100个字符")]
        public string ReferenceNumber { get; set; }
        
        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(500, ErrorMessage = "备注长度不能超过500个字符")]
        public string Remarks { get; set; }
        
        /// <summary>
        /// 关联资产ID (若出库用于特定资产)
        /// </summary>
        public new int? RelatedAssetId { get; set; }
        
        /// <summary>
        /// 关联故障ID (若出库用于特定维修)
        /// </summary>
        public new int? RelatedFaultId { get; set; }
        
        /// <summary>
        /// 将属性映射到基类
        /// </summary>
        public void MapToBaseClass()
        {
            // 将ReferenceNumber映射到Reference
            this.Reference = this.ReferenceNumber;
            
            // 将Remarks映射到Notes
            this.Notes = this.Remarks;
            
            // ReasonType已经在当前类中覆盖
            base.ReasonType = this.ReasonType;
        }
    }
} 