import{aV as t,bv as e,x as i}from"./index-CG5lHOPO.js";const n={getNotifications:(e={})=>t({url:"/v2/notifications",method:"get",params:e}),getUnreadCount:()=>t({url:"/v2/notifications/unread-count",method:"get"}),markAsRead:e=>t({url:`/v2/notifications/${e}/read`,method:"put"}),markAllAsRead:()=>t({url:"/v2/notifications/read-all",method:"put"}),deleteNotification:e=>t({url:`/v2/notifications/${e}`,method:"delete"}),sendTestNotification:()=>t({url:"/v2/notificationtest/send",method:"post"}),sendTestEvent:()=>t({url:"/v2/notificationtest/send-event",method:"post"}),getConnectionStatus:()=>t({url:"/v2/notificationtest/connection-status",method:"get"})},a=e("notification",{state:()=>({notifications:[],unreadCount:0,loading:!1,lastFetchTime:null,pollingInterval:null,total:0}),getters:{unreadNotifications:t=>t.notifications.filter((t=>!t.isRead)),taskNotifications:t=>{const e=["task","comment","mention","TaskAssigned","TaskStatusChanged","TaskContentChanged","TaskComment","TaskAttachmentAdded","TaskMention","TaskOverdue","Test"];return t.notifications.filter((t=>e.includes(t.type)))},systemNotifications:t=>t.notifications.filter((t=>"system"===t.type)),hasUnread:t=>t.unreadCount>0},actions:{async fetchNotifications(t={}){const e="boolean"==typeof t?t:t.force||!1,i="object"==typeof t?t:{},a=Date.now();if(!(!e&&this.lastFetchTime&&a-this.lastFetchTime<3e5))try{this.loading=!0;const t=await n.getNotifications(i);if(!t.success)throw new Error(t.message||"获取通知失败");this.notifications=t.data.notifications.map((t=>({id:t.id,notificationId:t.id,type:t.type,title:t.title,message:t.content,content:t.content,timestamp:t.createdAt||t.creationTimestamp||t.timestamp,createdAt:t.createdAt||t.creationTimestamp,creationTimestamp:t.creationTimestamp||t.createdAt,taskId:"Task"===t.resourceType||"Task"===t.referenceType?t.resourceId||t.referenceId:null,resourceType:t.resourceType||t.referenceType,resourceId:t.resourceId||t.referenceId,referenceType:t.referenceType||t.resourceType,referenceId:t.referenceId||t.resourceId,read:t.isRead,isRead:t.isRead}))),this.unreadCount=t.data.pagination.unreadCount,this.total=t.data.pagination.total||this.notifications.length,this.lastFetchTime=a}catch(s){throw s}finally{this.loading=!1}},async fetchUnreadCount(){var t,e,a,s;try{const a=i();if(!a.isLogin||!(null==(t=a.userInfo)?void 0:t.id))return void(this.unreadCount=0);const s=await n.getUnreadCount();if(!s.success){if(null==(e=s.message)?void 0:e.includes("未登录"))return void(this.unreadCount=0);throw new Error(s.message||"获取未读数量失败")}this.unreadCount=s.data}catch(o){throw((null==(a=o.message)?void 0:a.includes("401"))||(null==(s=o.message)?void 0:s.includes("未登录")))&&(this.unreadCount=0),o}},async markAsRead(t){try{const e=await n.markAsRead(t);if(!e.success)throw new Error(e.message||"标记失败");{const e=this.notifications.find((e=>e.id===t));e&&!e.isRead&&(e.read=!0,e.isRead=!0,this.unreadCount=Math.max(0,this.unreadCount-1))}}catch(e){throw e}},async markAllAsRead(){try{const t=await n.markAllAsRead();if(!t.success)throw new Error(t.message||"标记失败");this.notifications.forEach((t=>{t.read=!0,t.isRead=!0})),this.unreadCount=0}catch(t){throw t}},async deleteNotification(t){try{const e=await n.deleteNotification(t);if(!e.success)throw new Error(e.message||"删除失败");{const e=this.notifications.findIndex((e=>e.id===t));if(e>-1){this.notifications[e].read||(this.unreadCount=Math.max(0,this.unreadCount-1)),this.notifications.splice(e,1)}}}catch(e){throw e}},addNotification(t){const e=this.notifications.findIndex((e=>e.id===t.id||t.notificationId&&e.id===t.notificationId));-1!==e?this.notifications[e]={...this.notifications[e],id:t.id||t.notificationId,type:t.type,title:t.title,message:t.content||t.message,timestamp:t.createdAt||t.creationTimestamp||t.timestamp,taskId:"Task"===t.resourceType?t.resourceId:t.taskId,read:t.isRead||t.read||!1}:(this.notifications.unshift({id:t.id||t.notificationId,type:t.type,title:t.title,message:t.content||t.message,timestamp:t.createdAt||t.creationTimestamp||t.timestamp,taskId:"Task"===t.resourceType?t.resourceId:t.taskId,read:t.isRead||t.read||!1}),t.isRead||t.read||this.unreadCount++),this.notifications.sort(((t,e)=>new Date(e.timestamp)-new Date(t.timestamp)))},startPolling(){var t;const e=i();e.isLogin&&(null==(t=e.userInfo)?void 0:t.id)&&(this.pollingInterval||(this.pollingInterval=setInterval((()=>{this.fetchUnreadCount().catch((t=>{var e,i;((null==(e=t.message)?void 0:e.includes("401"))||(null==(i=t.message)?void 0:i.includes("未登录")))&&this.stopPolling()}))}),3e4)))},stopPolling(){this.pollingInterval&&(clearInterval(this.pollingInterval),this.pollingInterval=null)},resetNotificationState(){this.stopPolling(),this.notifications=[],this.unreadCount=0,this.total=0,this.lastFetchTime=null}}}),s=Object.freeze(Object.defineProperty({__proto__:null,useNotificationStore:a},Symbol.toStringTag,{value:"Module"}));export{s as a,n,a as u};
