// IT资产管理系统 - 供应商实体
// 文件路径: /Models/Entities/Supplier.cs
// 功能: 定义供应商实体，对应suppliers表

using System;
using System.Collections.Generic;
using ItAssetsSystem.Models;
using ItAssetsSystem.Models.Enums;

namespace ItAssetsSystem.Models.Entities
{
    /// <summary>
    /// 供应商实体
    /// </summary>
    public class Supplier : IAuditableEntity
    {
        /// <summary>
        /// 供应商ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 供应商名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 供应商编码
        /// </summary>
        public string Code { get; set; }

        /// <summary>
        /// 供应商类型（采购/维修/两者兼有）
        /// </summary>
        public SupplierType SupplierType { get; set; } = SupplierType.Procurement;

        /// <summary>
        /// 供应商类型（多选，使用位标志）
        /// </summary>
        public SupplierType Type { get; set; }



        /// <summary>
        /// 主要联系人
        /// </summary>
        public string ContactPerson { get; set; }

        /// <summary>
        /// 联系电话
        /// </summary>
        public string ContactPhone { get; set; }

        /// <summary>
        /// 联系邮箱
        /// </summary>
        public string ContactEmail { get; set; }

        /// <summary>
        /// 地址
        /// </summary>
        public string Address { get; set; }



        /// <summary>
        /// 备注
        /// </summary>
        public string Notes { get; set; }

        /// <summary>
        /// 是否激活
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; }

        /// <summary>
        /// 采购订单
        /// </summary>
        public virtual ICollection<PurchaseOrder> PurchaseOrders { get; set; }
    }
}

// 计划行数: 50
// 实际行数: 50 