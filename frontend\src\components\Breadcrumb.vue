/**
 * 面包屑导航组件
 * 文件路径: src/components/Breadcrumb.vue
 * 功能描述: 显示当前页面的导航路径
 */

<template>
  <el-breadcrumb class="app-breadcrumb" separator="/">
    <transition-group name="breadcrumb">
      <el-breadcrumb-item v-for="(item, index) in breadcrumbs" :key="item.path">
        <span
          v-if="index === breadcrumbs.length - 1 || item.redirect === 'noRedirect'"
          class="no-redirect"
        >{{ item.meta.title }}</span>
        <a v-else @click.prevent="handleLink(item)">{{ item.meta.title }}</a>
      </el-breadcrumb-item>
    </transition-group>
  </el-breadcrumb>
</template>

<script>
import { computed, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { isExternal } from '@/utils/validate'

export default {
  name: 'Breadcrumb',
  setup() {
    const router = useRouter()
    const route = useRoute()
    const breadcrumbs = ref([])
    
    const getBreadcrumbs = () => {
      let matched = route.matched.filter(item => item.meta && item.meta.title)
      const first = matched[0]
      
      // 如果不是首页
      if (first && first.path !== '/dashboard' && first.path !== '/') {
        matched = [{ path: '/dashboard', meta: { title: '首页' } }].concat(matched)
      }
      
      breadcrumbs.value = matched.filter(item => item.meta && item.meta.title && !item.meta.hideBreadcrumb)
    }
    
    watch(
      () => route.path,
      () => {
        getBreadcrumbs()
      },
      { immediate: true }
    )
    
    const handleLink = (item) => {
      const { path, redirect } = item
      
      if (redirect) {
        router.push(redirect)
        return
      }
      
      if (isExternal(path)) {
        window.open(path, '_blank')
      } else {
        router.push(path)
      }
    }
    
    return {
      breadcrumbs,
      getBreadcrumbs,
      handleLink
    }
  }
}
</script>

<style lang="scss" scoped>
.app-breadcrumb.el-breadcrumb {
  display: inline-block;
  line-height: 50px;
  margin-left: 8px;
  
  .no-redirect {
    color: #97a8be;
    cursor: text;
  }
}

.breadcrumb-enter-active,
.breadcrumb-leave-active {
  transition: all .5s;
}

.breadcrumb-enter-from,
.breadcrumb-leave-to {
  opacity: 0;
  transform: translateX(20px);
}

.breadcrumb-move {
  transition: all .5s;
}

.breadcrumb-leave-active {
  position: absolute;
}
</style> 