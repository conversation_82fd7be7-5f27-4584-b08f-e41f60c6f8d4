// IT资产管理系统 - 离线队列
// 文件路径: /Core/Resilience/OfflineQueue.cs
// 功能: 提供离线操作队列管理功能，确保在网络不稳定时系统可以继续工作

using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace ItAssetsSystem.Core.Resilience
{
    /// <summary>
    /// 离线操作实现类
    /// </summary>
    public class OfflineOperation : IOfflineOperation
    {
        /// <summary>
        /// 操作ID
        /// </summary>
        public string Id { get; }
        
        /// <summary>
        /// 操作类型
        /// </summary>
        public string OperationType { get; }
        
        /// <summary>
        /// 操作数据
        /// </summary>
        public string OperationData { get; }
        
        /// <summary>
        /// 操作状态
        /// </summary>
        public OperationStatus Status { get; private set; }
        
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; }
        
        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime UpdatedAt { get; private set; }
        
        /// <summary>
        /// 错误信息
        /// </summary>
        public string ErrorMessage { get; private set; }
        
        /// <summary>
        /// 重试次数
        /// </summary>
        public int RetryCount { get; private set; }
        
        /// <summary>
        /// 优先级（值越小优先级越高）
        /// </summary>
        public int Priority { get; }
        
        /// <summary>
        /// 超时（毫秒），0表示永不超时
        /// </summary>
        public int TimeoutMs { get; }
        
        /// <summary>
        /// 操作执行委托
        /// </summary>
        private readonly Func<CancellationToken, Task<bool>> _executeFunc;
        
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="operationType">操作类型</param>
        /// <param name="operationData">操作数据</param>
        /// <param name="executeFunc">执行函数</param>
        /// <param name="priority">优先级</param>
        /// <param name="timeoutMs">超时毫秒</param>
        public OfflineOperation(
            string operationType,
            string operationData,
            Func<CancellationToken, Task<bool>> executeFunc,
            int priority = 0,
            int timeoutMs = 0)
        {
            Id = Guid.NewGuid().ToString("N");
            OperationType = operationType ?? throw new ArgumentNullException(nameof(operationType));
            OperationData = operationData ?? throw new ArgumentNullException(nameof(operationData));
            _executeFunc = executeFunc ?? throw new ArgumentNullException(nameof(executeFunc));
            Priority = priority;
            TimeoutMs = timeoutMs;
            Status = OperationStatus.Pending;
            CreatedAt = DateTime.UtcNow;
            UpdatedAt = CreatedAt;
            RetryCount = 0;
        }
        
        /// <summary>
        /// 执行操作
        /// </summary>
        /// <param name="cancellationToken">取消标记</param>
        /// <returns>执行结果</returns>
        public async Task<bool> ExecuteAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                UpdateStatus(OperationStatus.InProgress);
                
                // 如果设置了超时，创建一个超时取消令牌
                CancellationToken executionToken = cancellationToken;
                
                using var timeoutCts = TimeoutMs > 0 
                    ? CancellationTokenSource.CreateLinkedTokenSource(cancellationToken)
                    : null;
                
                if (timeoutCts != null)
                {
                    timeoutCts.CancelAfter(TimeoutMs);
                    executionToken = timeoutCts.Token;
                }
                
                // 执行操作
                bool result = await _executeFunc(executionToken);
                
                // 根据执行结果更新状态
                if (result)
                {
                    UpdateStatus(OperationStatus.Completed);
                }
                else
                {
                    UpdateStatus(OperationStatus.Failed, "操作执行返回失败");
                }
                
                return result;
            }
            catch (OperationCanceledException ex)
            {
                if (cancellationToken.IsCancellationRequested)
                {
                    UpdateStatus(OperationStatus.Cancelled, "操作被取消");
                }
                else
                {
                    UpdateStatus(OperationStatus.Failed, $"操作超时: {ex.Message}");
                }
                return false;
            }
            catch (Exception ex)
            {
                UpdateStatus(OperationStatus.Failed, $"操作执行异常: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 更新状态
        /// </summary>
        /// <param name="status">新状态</param>
        /// <param name="errorMessage">错误信息</param>
        public void UpdateStatus(OperationStatus status, string errorMessage = null)
        {
            Status = status;
            ErrorMessage = errorMessage;
            UpdatedAt = DateTime.UtcNow;
        }
        
        /// <summary>
        /// 增加重试次数
        /// </summary>
        public void IncrementRetryCount()
        {
            RetryCount++;
            UpdatedAt = DateTime.UtcNow;
        }
    }

    /// <summary>
    /// 离线队列实现
    /// </summary>
    public class OfflineQueue : IOfflineQueue, IDisposable
    {
        private readonly ILogger<OfflineQueue> _logger;
        private readonly OfflineQueueOptions _options;
        private readonly INetworkMonitor _networkMonitor;
        private readonly ConcurrentDictionary<string, IOfflineOperation> _operations;
        private readonly SemaphoreSlim _semaphore;
        private readonly Timer _autoSaveTimer;
        private readonly Timer _processPendingTimer;
        private bool _isEnabled;
        private bool _isDisposed;
        
        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled => _isEnabled;
        
        /// <summary>
        /// 队列中等待处理的操作数
        /// </summary>
        public int PendingCount => _operations.Values.Count(op => op.Status == OperationStatus.Pending);
        
        /// <summary>
        /// 操作入队事件
        /// </summary>
        public event EventHandler<OfflineQueueEventArgs> OperationEnqueued;
        
        /// <summary>
        /// 操作取消事件
        /// </summary>
        public event EventHandler<OfflineQueueEventArgs> OperationCancelled;
        
        /// <summary>
        /// 操作成功事件
        /// </summary>
        public event EventHandler<OfflineQueueEventArgs> OperationSucceeded;
        
        /// <summary>
        /// 操作失败事件
        /// </summary>
        public event EventHandler<OfflineQueueEventArgs> OperationFailed;
        
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志记录器</param>
        /// <param name="options">队列选项</param>
        /// <param name="networkMonitor">网络监控</param>
        public OfflineQueue(
            ILogger<OfflineQueue> logger,
            IOptions<OfflineQueueOptions> options,
            INetworkMonitor networkMonitor)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
            _networkMonitor = networkMonitor ?? throw new ArgumentNullException(nameof(networkMonitor));
            
            _operations = new ConcurrentDictionary<string, IOfflineOperation>();
            _semaphore = new SemaphoreSlim(_options.ConcurrentProcessingCount, _options.ConcurrentProcessingCount);
            
            // 创建自动保存定时器
            _autoSaveTimer = new Timer(
                async _ => await SaveQueueStateAsync(),
                null,
                Timeout.Infinite,
                Timeout.Infinite
            );
            
            // 创建处理待处理操作的定时器
            _processPendingTimer = new Timer(
                async _ => await ProcessPendingOperationsAsync(),
                null,
                Timeout.Infinite,
                Timeout.Infinite
            );
            
            // 监听网络状态变化
            _networkMonitor.NetworkStatusChanged += OnNetworkStatusChanged;
            
            _logger.LogInformation("离线队列已初始化，存储路径：{0}", _options.StoragePath);
        }
        
        /// <summary>
        /// 启用队列
        /// </summary>
        public void Enable()
        {
            if (_isEnabled)
                return;
            
            _isEnabled = true;
            
            // 启动自动保存定时器
            _autoSaveTimer.Change(0, _options.AutoSaveIntervalMs);
            
            _logger.LogInformation("离线队列已启用");
        }
        
        /// <summary>
        /// 禁用队列
        /// </summary>
        public void Disable()
        {
            if (!_isEnabled)
                return;
            
            _isEnabled = false;
            
            // 停止自动保存定时器
            _autoSaveTimer.Change(Timeout.Infinite, Timeout.Infinite);
            
            // 停止处理待处理操作定时器
            _processPendingTimer.Change(Timeout.Infinite, Timeout.Infinite);
            
            _logger.LogInformation("离线队列已禁用");
        }
        
        /// <summary>
        /// 添加操作到队列
        /// </summary>
        /// <param name="operation">离线操作</param>
        /// <returns>操作ID</returns>
        public async Task<string> EnqueueAsync(IOfflineOperation operation)
        {
            if (operation == null)
                throw new ArgumentNullException(nameof(operation));
            
            if (!_isEnabled)
            {
                _logger.LogWarning("离线队列未启用，无法添加操作");
                throw new InvalidOperationException("离线队列未启用");
            }
            
            // 添加到操作字典
            if (!_operations.TryAdd(operation.Id, operation))
            {
                _logger.LogWarning("操作ID已存在：{0}", operation.Id);
                throw new InvalidOperationException($"操作ID已存在：{operation.Id}");
            }
            
            _logger.LogInformation("操作已添加到队列：{0}, 类型：{1}", operation.Id, operation.OperationType);
            
            // 触发操作入队事件
            OnOperationEnqueued(operation);
            
            // 保存队列状态
            await SaveQueueStateAsync();
            
            // 如果网络已连接，尝试立即执行
            if (_networkMonitor.IsConnected())
            {
                _ = Task.Run(async () => await ExecuteOperationAsync(operation.Id));
            }
            
            return operation.Id;
        }
        
        /// <summary>
        /// 取消操作
        /// </summary>
        /// <param name="operationId">操作ID</param>
        /// <returns>是否成功取消</returns>
        public async Task<bool> CancelOperationAsync(string operationId)
        {
            if (string.IsNullOrEmpty(operationId))
                throw new ArgumentNullException(nameof(operationId));
            
            if (!_operations.TryGetValue(operationId, out var operation))
            {
                _logger.LogWarning("操作未找到：{0}", operationId);
                return false;
            }
            
            if (operation.Status != OperationStatus.Pending)
            {
                _logger.LogWarning("只能取消待处理操作，当前状态：{0}", operation.Status);
                return false;
            }
            
            // 更新操作状态
            operation.UpdateStatus(OperationStatus.Cancelled, "操作被用户取消");
            
            _logger.LogInformation("操作已取消：{0}", operationId);
            
            // 触发操作取消事件
            OnOperationCancelled(operation);
            
            // 保存队列状态
            await SaveQueueStateAsync();
            
            return true;
        }
        
        /// <summary>
        /// 执行操作
        /// </summary>
        /// <param name="operationId">操作ID</param>
        /// <returns>是否执行成功</returns>
        public async Task<bool> ExecuteOperationAsync(string operationId)
        {
            if (string.IsNullOrEmpty(operationId))
                throw new ArgumentNullException(nameof(operationId));
            
            if (!_operations.TryGetValue(operationId, out var operation))
            {
                _logger.LogWarning("操作未找到：{0}", operationId);
                return false;
            }
            
            if (operation.Status != OperationStatus.Pending && operation.Status != OperationStatus.Failed)
            {
                _logger.LogWarning("只能执行待处理或失败的操作，当前状态：{0}", operation.Status);
                return false;
            }
            
            // 如果网络断开且不是强制执行，则返回失败
            if (!_networkMonitor.IsConnected())
            {
                _logger.LogWarning("网络已断开，无法执行操作：{0}", operationId);
                return false;
            }
            
            try
            {
                await _semaphore.WaitAsync();
                
                _logger.LogInformation("开始执行操作：{0}, 类型：{1}", operationId, operation.OperationType);
                
                // 执行操作
                bool result = await operation.ExecuteAsync();
                
                if (result)
                {
                    _logger.LogInformation("操作执行成功：{0}", operationId);
                    
                    // 触发操作成功事件
                    OnOperationSucceeded(operation);
                }
                else
                {
                    _logger.LogWarning("操作执行失败：{0}, 错误：{1}", operationId, operation.ErrorMessage);
                    
                    // 触发操作失败事件
                    OnOperationFailed(operation);
                }
                
                // 保存队列状态
                await SaveQueueStateAsync();
                
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "操作执行时发生错误：{0}", operationId);
                
                // 更新操作状态
                operation.UpdateStatus(OperationStatus.Failed, $"执行异常：{ex.Message}");
                
                // 触发操作失败事件
                OnOperationFailed(operation);
                
                // 保存队列状态
                await SaveQueueStateAsync();
                
                return false;
            }
            finally
            {
                _semaphore.Release();
            }
        }
        
        /// <summary>
        /// 获取待处理操作列表
        /// </summary>
        /// <returns>待处理操作列表</returns>
        public Task<IEnumerable<IOfflineOperation>> GetPendingOperationsAsync()
        {
            var pendingOperations = _operations.Values
                .Where(op => op.Status == OperationStatus.Pending)
                .OrderBy(op => op.Priority)
                .ThenBy(op => op.CreatedAt)
                .ToList();
            
            return Task.FromResult<IEnumerable<IOfflineOperation>>(pendingOperations);
        }
        
        /// <summary>
        /// 获取失败操作列表
        /// </summary>
        /// <returns>失败操作列表</returns>
        public Task<IEnumerable<IOfflineOperation>> GetFailedOperationsAsync()
        {
            var failedOperations = _operations.Values
                .Where(op => op.Status == OperationStatus.Failed)
                .OrderBy(op => op.Priority)
                .ThenBy(op => op.UpdatedAt)
                .ToList();
            
            return Task.FromResult<IEnumerable<IOfflineOperation>>(failedOperations);
        }
        
        /// <summary>
        /// 重试失败操作
        /// </summary>
        /// <param name="operationId">操作ID，如果为null则重试所有失败操作</param>
        /// <returns>重试成功的操作数</returns>
        public async Task<int> RetryFailedOperationsAsync(string operationId = null)
        {
            if (!_isEnabled)
            {
                _logger.LogWarning("离线队列未启用，无法重试操作");
                return 0;
            }
            
            // 如果网络断开，则返回0
            if (!_networkMonitor.IsConnected())
            {
                _logger.LogWarning("网络已断开，无法重试操作");
                return 0;
            }
            
            int successCount = 0;
            
            if (!string.IsNullOrEmpty(operationId))
            {
                // 重试单个操作
                if (_operations.TryGetValue(operationId, out var operation) && operation.Status == OperationStatus.Failed)
                {
                    // 如果重试次数已达上限，不再重试
                    if (operation.RetryCount >= _options.MaxRetryCount)
                    {
                        _logger.LogWarning("操作重试次数已达上限：{0}, 重试次数：{1}", operationId, operation.RetryCount);
                        return 0;
                    }
                    
                    // 增加重试次数
                    operation.IncrementRetryCount();
                    
                    // 重置状态为待处理
                    operation.UpdateStatus(OperationStatus.Pending);
                    
                    _logger.LogInformation("重试操作：{0}, 重试次数：{1}", operationId, operation.RetryCount);
                    
                    // 执行操作
                    if (await ExecuteOperationAsync(operationId))
                    {
                        successCount++;
                    }
                }
            }
            else
            {
                // 重试所有失败操作
                var failedOperations = await GetFailedOperationsAsync();
                
                foreach (var operation in failedOperations)
                {
                    // 如果重试次数已达上限，不再重试
                    if (operation.RetryCount >= _options.MaxRetryCount)
                    {
                        _logger.LogWarning("操作重试次数已达上限：{0}, 重试次数：{1}", operation.Id, operation.RetryCount);
                        continue;
                    }
                    
                    // 增加重试次数
                    operation.IncrementRetryCount();
                    
                    // 重置状态为待处理
                    operation.UpdateStatus(OperationStatus.Pending);
                    
                    _logger.LogInformation("重试操作：{0}, 重试次数：{1}", operation.Id, operation.RetryCount);
                    
                    // 执行操作
                    if (await ExecuteOperationAsync(operation.Id))
                    {
                        successCount++;
                    }
                    
                    // 在每次操作之间添加延迟，避免并发压力
                    await Task.Delay(TimeSpan.FromMilliseconds(_options.RetryDelayBaseMs));
                }
            }
            
            // 保存队列状态
            await SaveQueueStateAsync();
            
            return successCount;
        }
        
        /// <summary>
        /// 清空队列
        /// </summary>
        /// <returns>是否成功清空</returns>
        public async Task<bool> ClearQueueAsync()
        {
            try
            {
                _operations.Clear();
                
                _logger.LogInformation("队列已清空");
                
                // 保存队列状态
                await SaveQueueStateAsync();
                
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清空队列时发生错误");
                return false;
            }
        }
        
        /// <summary>
        /// 保存队列状态
        /// </summary>
        /// <returns>是否保存成功</returns>
        public async Task<bool> SaveQueueStateAsync()
        {
            try
            {
                // 确保存储目录存在
                Directory.CreateDirectory(_options.StoragePath);
                
                // 序列化操作数据
                var serializableOperations = _operations.Values.ToList();
                var json = JsonSerializer.Serialize(serializableOperations, new JsonSerializerOptions
                {
                    WriteIndented = true
                });
                
                // 保存到文件
                var filePath = Path.Combine(_options.StoragePath, "offline_queue.json");
                await File.WriteAllTextAsync(filePath, json);
                
                _logger.LogDebug("队列状态已保存，操作数量：{0}", _operations.Count);
                
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存队列状态时发生错误");
                return false;
            }
        }
        
        /// <summary>
        /// 加载队列状态
        /// </summary>
        /// <returns>是否加载成功</returns>
        public async Task<bool> LoadQueueStateAsync()
        {
            try
            {
                // 确保存储目录存在
                Directory.CreateDirectory(_options.StoragePath);
                
                var filePath = Path.Combine(_options.StoragePath, "offline_queue.json");
                
                // 如果文件不存在，返回true
                if (!File.Exists(filePath))
                {
                    _logger.LogInformation("队列状态文件不存在，将创建新队列");
                    return true;
                }
                
                // 读取文件内容
                var json = await File.ReadAllTextAsync(filePath);
                
                // 反序列化
                var operations = JsonSerializer.Deserialize<List<OfflineOperation>>(json);
                
                // 清空现有操作
                _operations.Clear();
                
                // 添加到操作字典
                foreach (var operation in operations)
                {
                    _operations.TryAdd(operation.Id, operation);
                }
                
                _logger.LogInformation("队列状态已加载，操作数量：{0}", _operations.Count);
                
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加载队列状态时发生错误");
                return false;
            }
        }
        
        /// <summary>
        /// 处理所有待处理的操作
        /// </summary>
        /// <returns>处理成功的操作数</returns>
        private async Task<int> ProcessPendingOperationsAsync()
        {
            if (!_isEnabled || !_networkMonitor.IsConnected())
            {
                return 0;
            }
            
            int successCount = 0;
            var pendingOperations = await GetPendingOperationsAsync();
            
            foreach (var operation in pendingOperations)
            {
                try
                {
                    if (await ExecuteOperationAsync(operation.Id))
                    {
                        successCount++;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "处理待处理操作时发生错误：{0}", operation.Id);
                }
                
                // 在每次操作之间添加短暂延迟，避免并发压力
                await Task.Delay(100);
            }
            
            return successCount;
        }
        
        /// <summary>
        /// 网络状态变化处理
        /// </summary>
        /// <param name="sender">发送者</param>
        /// <param name="status">网络状态</param>
        private void OnNetworkStatusChanged(object sender, NetworkStatus status)
        {
            if (status == NetworkStatus.Connected && _isEnabled && _options.AutoProcessPendingOnReconnect)
            {
                _logger.LogInformation("网络已恢复连接，将处理待处理操作");
                
                // 设置定时器延迟3秒后执行，防止网络状态抖动
                _processPendingTimer.Change(3000, Timeout.Infinite);
            }
        }
        
        /// <summary>
        /// 触发操作入队事件
        /// </summary>
        /// <param name="operation">操作</param>
        private void OnOperationEnqueued(IOfflineOperation operation)
        {
            try
            {
                OperationEnqueued?.Invoke(this, new OfflineQueueEventArgs(operation));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "触发操作入队事件时发生错误");
            }
        }
        
        /// <summary>
        /// 触发操作取消事件
        /// </summary>
        /// <param name="operation">操作</param>
        private void OnOperationCancelled(IOfflineOperation operation)
        {
            try
            {
                OperationCancelled?.Invoke(this, new OfflineQueueEventArgs(operation));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "触发操作取消事件时发生错误");
            }
        }
        
        /// <summary>
        /// 触发操作成功事件
        /// </summary>
        /// <param name="operation">操作</param>
        private void OnOperationSucceeded(IOfflineOperation operation)
        {
            try
            {
                OperationSucceeded?.Invoke(this, new OfflineQueueEventArgs(operation));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "触发操作成功事件时发生错误");
            }
        }
        
        /// <summary>
        /// 触发操作失败事件
        /// </summary>
        /// <param name="operation">操作</param>
        private void OnOperationFailed(IOfflineOperation operation)
        {
            try
            {
                OperationFailed?.Invoke(this, new OfflineQueueEventArgs(operation));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "触发操作失败事件时发生错误");
            }
        }
        
        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (_isDisposed)
                return;
                
            _isDisposed = true;
            
            // 停止网络监控事件监听
            if (_networkMonitor != null)
            {
                _networkMonitor.NetworkStatusChanged -= OnNetworkStatusChanged;
            }
            
            // 停止并释放定时器
            _autoSaveTimer?.Dispose();
            _processPendingTimer?.Dispose();
            
            // 释放信号量
            _semaphore?.Dispose();
            
            // 保存队列状态
            _ = SaveQueueStateAsync();
            
            _logger.LogInformation("离线队列已释放资源");
        }
    }
}