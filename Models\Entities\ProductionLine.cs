// IT资产管理系统 - 产线实体
// 文件路径: /Models/Entities/ProductionLine.cs
// 功能: 定义产线实体，对应production_lines表

using System;
using System.Collections.Generic;

namespace ItAssetsSystem.Models.Entities
{
    /// <summary>
    /// 产线实体
    /// </summary>
    public class ProductionLine : IAuditableEntity
    {
        /// <summary>
        /// 产线ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 产线编码
        /// </summary>
        public string Code { get; set; }

        /// <summary>
        /// 产线名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 状态（0:停用, 1:启用）
        /// </summary>
        public int Status { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; }

        /// <summary>
        /// 关联的资产
        /// </summary>
        public virtual ICollection<Asset> Assets { get; set; }

        /// <summary>
        /// 关联的工序
        /// </summary>
        public virtual ICollection<Process> Processes { get; set; }
    }
} 