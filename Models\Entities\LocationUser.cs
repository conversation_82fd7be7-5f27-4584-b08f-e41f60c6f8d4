#nullable enable
// IT资产管理系统 - 位置用户关联实体
// 文件路径: /Models/Entities/LocationUser.cs
// 功能: 定义位置与人员的关联关系

using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
// using ItAssetsSystem.Models; // 根据原始文件，这个 using 可能不需要，除非 Personnel/Location 在此命名空间

namespace ItAssetsSystem.Models.Entities
{
    /// <summary>
    /// 位置用户关联实体
    /// </summary>
    [Table("locationusers")] // 确保表名正确
    public class LocationUser : IAuditableEntity // IAuditableEntity 通常提供 CreatedAt, UpdatedAt
    {
        // 复合主键将在 AppDbContext 中配置，这里不定义单独的 Id 主键

        /// <summary>
        /// 位置ID
        /// </summary>
        [Column("location_id")]
        public int LocationId { get; set; }
        
        /// <summary>
        /// 人员ID (关联到 Personnel 表)
        /// </summary>
        [Column("personnel_id")]
        public int PersonnelId { get; set; }
        
        /// <summary>
        /// 用户类型（0:使用人, 1:管理员）
        /// </summary>
        [Column("user_type")]
        public int UserType { get; set; }
        
        /// <summary>
        /// 是否激活 (业务逻辑字段，不在数据库持久化)
        /// </summary>
        [NotMapped]
        public bool IsActive { get; set; } = true; // 与原始文件一致
        
        /// <summary>
        /// 创建时间
        /// </summary>
        [Column("created_at")]
        public DateTime CreatedAt { get; set; } // IAuditableEntity 提供
        
        /// <summary>
        /// 更新时间 (业务逻辑字段或由IAuditableEntity处理，但不在locationusers表)
        /// </summary>
        [NotMapped] // 与原始文件一致，且数据库 DDL 没有 updated_at 列
        public DateTime UpdatedAt { get; set; } // IAuditableEntity 提供
        
        // 导航属性
        /// <summary>
        /// 位置
        /// </summary>
        public virtual Location Location { get; set; } = null!; // 恢复 = null!
        
        /// <summary>
        /// 人员
        /// </summary>
        public virtual Personnel Personnel { get; set; } = null!; // 恢复 = null!
    }
} 