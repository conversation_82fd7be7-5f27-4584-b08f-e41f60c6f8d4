using System;
using System.Collections.Generic;

namespace ItAssetsSystem.Application.Common.Dtos
{
    /// <summary>
    /// 工作汇总数据传输对象
    /// </summary>
    public class WorkSummaryDto
    {
        public int UserId { get; set; }
        public string UserName { get; set; } = string.Empty;
        public string DepartmentName { get; set; } = string.Empty;
        public string PeriodType { get; set; } = string.Empty;
        public DateTime PeriodDate { get; set; }

        // 任务统计
        public int TasksCreated { get; set; }
        public int TasksClaimed { get; set; }
        public int TasksCompleted { get; set; }
        public int TasksUpdated { get; set; }
        public int TasksCommented { get; set; }
        public int TasksTotal { get; set; }

        // 资产统计
        public int AssetsCreated { get; set; }
        public int AssetsUpdated { get; set; }
        public int AssetsDeleted { get; set; }
        public int AssetsTotal { get; set; }

        // 故障统计
        public int FaultsReported { get; set; }
        public int FaultsRepaired { get; set; }
        public int FaultsTotal { get; set; }

        // 采购统计
        public int ProcurementsCreated { get; set; }
        public int ProcurementsUpdated { get; set; }
        public int ProcurementsTotal { get; set; }

        // 备件统计
        public int PartsIn { get; set; }
        public int PartsOut { get; set; }
        public int PartsAdded { get; set; }
        public int PartsTotal { get; set; }

        // 游戏化统计
        public int TotalPointsEarned { get; set; }
        public int TotalCoinsEarned { get; set; }
        public int TotalDiamondsEarned { get; set; }
        public int TotalXpEarned { get; set; }

        // 排名和评价
        public int PointsRank { get; set; }
        public int ProductivityRank { get; set; }
        public string Evaluation { get; set; } = string.Empty;
        public double ProductivityScore { get; set; }
    }

    /// <summary>
    /// 用户工作汇总数据传输对象（内部使用）
    /// </summary>
    public class UserWorkSummaryDto
    {
        public int UserId { get; set; }
        public string UserName { get; set; } = string.Empty;
        public string DepartmentName { get; set; } = string.Empty;
        public string PeriodType { get; set; } = string.Empty;
        public DateTime PeriodDate { get; set; }

        // 任务统计
        public int TasksCreated { get; set; }
        public int TasksClaimed { get; set; }
        public int TasksCompleted { get; set; }
        public int TasksUpdated { get; set; }
        public int TasksCommented { get; set; }
        public int TasksTotal { get; set; }

        // 资产统计
        public int AssetsCreated { get; set; }
        public int AssetsUpdated { get; set; }
        public int AssetsDeleted { get; set; }
        public int AssetsTotal { get; set; }

        // 故障统计
        public int FaultsReported { get; set; }
        public int FaultsRepaired { get; set; }
        public int FaultsTotal { get; set; }

        // 采购统计
        public int ProcurementsCreated { get; set; }
        public int ProcurementsUpdated { get; set; }
        public int ProcurementsTotal { get; set; }

        // 备件统计
        public int PartsIn { get; set; }
        public int PartsOut { get; set; }
        public int PartsAdded { get; set; }
        public int PartsTotal { get; set; }

        // 游戏化统计
        public int TotalPointsEarned { get; set; }
        public int TotalCoinsEarned { get; set; }
        public int TotalDiamondsEarned { get; set; }
        public int TotalXpEarned { get; set; }

        // 排名和评价
        public int PointsRank { get; set; }
        public int ProductivityRank { get; set; }
        public string Evaluation { get; set; } = string.Empty;
        public double ProductivityScore { get; set; }
    }

    /// <summary>
    /// 任务领取统计数据传输对象
    /// </summary>
    public class TaskClaimStatisticsDto
    {
        public int UserId { get; set; }
        public string UserName { get; set; } = string.Empty;
        public string DepartmentName { get; set; } = string.Empty;
        public int ShiftId { get; set; }
        public string ShiftName { get; set; } = string.Empty;
        public DateTime StatisticsDate { get; set; }
        public int ClaimedTasksCount { get; set; }
        public int StartedTasksCount { get; set; }
        public int CompletedTasksCount { get; set; }
        public int CancelledTasksCount { get; set; }
        public int UnclaimedTasksCount { get; set; }
        public decimal CompletionRate { get; set; }
        public int DailyTasksClaimed { get; set; }
        public int WeeklyTasksClaimed { get; set; }
        public int MonthlyTasksClaimed { get; set; }
        public int TotalTasksClaimed { get; set; }
        public DateTime LastClaimTime { get; set; }
        public double ClaimRate { get; set; }
    }

    /// <summary>
    /// 增强排行榜数据传输对象
    /// </summary>
    public class EnhancedLeaderboardDto
    {
        public int UserId { get; set; }
        public string UserName { get; set; } = string.Empty;
        public string DepartmentName { get; set; } = string.Empty;
        public int TotalPoints { get; set; }
        public int TotalCoins { get; set; }
        public int TotalDiamonds { get; set; }
        public int Level { get; set; }
        public int Rank { get; set; }
        public double ProductivityScore { get; set; }
        public string Badge { get; set; } = string.Empty;
    }

    /// <summary>
    /// 排行榜项目数据传输对象
    /// </summary>
    public class LeaderboardItemDto
    {
        public int UserId { get; set; }
        public string UserName { get; set; } = string.Empty;
        public string DepartmentName { get; set; } = string.Empty;
        public int Score { get; set; }
        public int Rank { get; set; }
        public string MetricType { get; set; } = string.Empty;
        public string Badge { get; set; } = string.Empty;
    }

    /// <summary>
    /// 概览数据传输对象
    /// </summary>
    public class OverviewDto
    {
        public List<LeaderboardItemDto> TopPerformers { get; set; } = new List<LeaderboardItemDto>();
        public int TotalActiveUsers { get; set; }
        public int WeeklyTasksCompleted { get; set; }
        public int WeeklyAssetsManaged { get; set; }
        public DateTime LastUpdateTime { get; set; }
    }
}
