<template>
  <div class="enhanced-task-list">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1>📋 任务管理中心</h1>
        <p class="subtitle">高效管理，轻松协作</p>
      </div>
      <div class="header-right">

        <QuickTaskCreator
          trigger-text="⚡ 快速创建"
          trigger-class="quick-create-btn"
          @created="onTaskCreated"
          @expand-to-full-form="handleExpandToFullForm"
        />
        <el-button type="primary" @click="showCreateFormDialog">
          <el-icon><DocumentAdd /></el-icon>
          详细创建
        </el-button>
      </div>
    </div>

    <!-- 智能过滤器 -->
    <el-card class="filter-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>🔍 智能筛选</span>
          <el-button link @click="resetFilters" size="small">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </div>
      </template>
      
      <div class="filter-grid">
        <!-- 状态 -->
        <el-select v-model="filters.status" placeholder="任务状态" clearable class="filter-item">
          <el-option label="全部状态" value="" />
          <el-option label="📋 待处理" value="Todo" />
          <el-option label="🔄 进行中" value="InProgress" />
          <el-option label="✅ 已完成" value="Done" />
          <el-option label="❌ 已取消" value="Cancelled" />
        </el-select>
        <!-- 优先级 -->
        <el-select v-model="filters.priority" placeholder="优先级" clearable class="filter-item">
          <el-option label="全部优先级" value="" />
          <el-option label="🔴 高优先级" value="High" />
          <el-option label="🟡 中优先级" value="Medium" />
          <el-option label="🟢 低优先级" value="Low" />
        </el-select>
        <!-- 分类 -->
        <el-select v-model="filters.categoryId" placeholder="任务分类" clearable class="filter-item" :loading="categoriesLoading">
          <el-option label="全部分类" value="" />
          <el-option label="未分类" value="null" />
          <el-option
            v-for="category in categories"
            :key="category.categoryId"
            :label="category.name"
            :value="category.categoryId"
          >
            <div class="category-filter-option">
              <el-icon v-if="category.icon" :style="{ color: category.color || '#409EFF' }">
                <component :is="category.icon" />
              </el-icon>
              <span>{{ category.name }}</span>
            </div>
          </el-option>
        </el-select>
        <!-- 负责人多选+头像 -->
        <el-select v-model="filters.assigneeIds" multiple placeholder="负责人" clearable filterable class="filter-item" collapse-tags collapse-tags-tooltip>
          <el-option
            v-for="user in users"
            :key="user.id"
            :label="user.name"
            :value="user.id"
          >
            <el-avatar :src="user.avatar" :size="18" style="margin-right:6px" />
            <span>{{ user.name }}</span>
            <span class="user-dept">{{ user.department }}</span>
          </el-option>
        </el-select>
        <!-- 搜索 -->
        <el-input
          v-model="searchQuery"
          placeholder="🔍 搜索任务标题、描述..."
          clearable
          class="search-input"
          @keyup.enter="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        <!-- 日期 -->
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          class="date-picker"
          @change="handleDateRangeChange"
        />
        <!-- 搜索按钮 -->
        <el-button type="primary" @click="handleSearch" :loading="loading" class="search-btn">
          <el-icon><Search /></el-icon>
          搜索
        </el-button>
      </div>
    </el-card>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <el-card class="stat-card total" shadow="hover">
        <div class="stat-content">
          <div class="stat-icon">📊</div>
          <div class="stat-info">
            <div class="stat-number">{{ taskStats.total }}</div>
            <div class="stat-label">总任务</div>
          </div>
        </div>
      </el-card>
      
      <el-card class="stat-card progress" shadow="hover">
        <div class="stat-content">
          <div class="stat-icon">🔄</div>
          <div class="stat-info">
            <div class="stat-number">{{ taskStats.inProgress }}</div>
            <div class="stat-label">进行中</div>
          </div>
        </div>
      </el-card>
      
      <el-card class="stat-card completed" shadow="hover">
        <div class="stat-content">
          <div class="stat-icon">✅</div>
          <div class="stat-info">
            <div class="stat-number">{{ taskStats.completed }}</div>
            <div class="stat-label">已完成</div>
          </div>
        </div>
      </el-card>
      
      <el-card class="stat-card overdue" shadow="hover">
        <div class="stat-content">
          <div class="stat-icon">⏰</div>
          <div class="stat-info">
            <div class="stat-number">{{ taskStats.overdue }}</div>
            <div class="stat-label">已逾期</div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 批量操作栏 -->
    <div v-if="selectedTasks.length > 0" class="batch-actions-bar">
      <div class="batch-info">
        <span>已选择 {{ selectedTasks.length }} 个任务</span>
        <el-button link @click="clearSelection">取消选择</el-button>
      </div>
      <div class="batch-actions">
        <el-button size="small" @click="showBatchAssignDialog = true">
          <el-icon><User /></el-icon>
          批量分配
        </el-button>
        <el-button size="small" @click="showBatchStatusDialog = true">
          <el-icon><Edit /></el-icon>
          批量修改状态
        </el-button>
        <el-button size="small" type="danger" @click="batchDeleteTasks">
          <el-icon><Delete /></el-icon>
          批量删除
        </el-button>
      </div>
    </div>

    <!-- 增强任务表格 -->
    <el-card class="table-card" shadow="hover">
      <template #header>
        <div class="table-header">
          <span>📋 任务列表 ({{ filteredTasks.length }})</span>
          <div class="table-actions">
            <el-button-group size="small">
              <el-button 
                :type="viewMode === 'table' ? 'primary' : ''"
                @click="viewMode = 'table'"
              >
                <el-icon><Grid /></el-icon>
                表格
              </el-button>
              <el-button 
                :type="viewMode === 'card' ? 'primary' : ''"
                @click="viewMode = 'card'"
              >
                <el-icon><Collection /></el-icon>
                卡片
              </el-button>
            </el-button-group>
          </div>
        </div>
      </template>

      <!-- 表格视图 -->
      <div v-if="viewMode === 'table'">
        <el-table
          :data="tasksWithAvatar"
          v-loading="loading"
          @selection-change="handleSelectionChange"
          row-key="taskId"
          class="enhanced-table"
          :row-class-name="getTableRowClass"
        >
          <el-table-column type="selection" width="50" />
          
          <el-table-column label="优先级" width="70" align="center">
            <template #default="{ row }">
              <el-tag
                :type="getPriorityTagType(row.priority)"
                size="small"
                class="priority-tag-compact"
                :title="getPriorityText(row.priority)"
              >
                {{ getPriorityIcon(row.priority) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="分类" width="120" align="center">
            <template #default="{ row }">
              <div class="category-cell">
                <el-tag
                  v-if="row.categoryName"
                  :color="row.categoryColor || '#409EFF'"
                  size="small"
                  class="category-tag"
                  :title="row.categoryName"
                >
                  <el-icon v-if="row.categoryIcon" class="category-icon">
                    <component :is="row.categoryIcon" />
                  </el-icon>
                  {{ row.categoryName }}
                </el-tag>
                <span v-else class="no-category">未分类</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="任务名称" min-width="200">
            <template #default="{ row }">
              <div class="task-name-cell" @click="showTaskDetail(row)">
                <div class="task-content">
                  <span class="task-title">{{ row.name }}</span>
                  <div v-if="row.description" class="task-desc">{{ truncateText(row.description, 50) }}</div>
                </div>
                <!-- 完成水印 - 行业最佳实践：后端返回完整信息 -->
                <div
                  v-if="row.status === 'Done' && row.completedByUserName"
                  class="completion-watermark"
                  :style="{ '--watermark-color': row.completionWatermarkColor || '#409EFF' }"
                >
                  <span class="watermark-text">
                    完成人：{{ row.completedByUserName }}
                  </span>
                </div>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column label="负责人" width="150">
            <template #default="{ row }">
              <div class="assignee-cell">
                <UserAvatarStack
                  :users="getAllAssignees(row)"
                  :is-main-user-primary="true"
                  :max-users="4"
                  avatar-size="6"
                  :overlap="-10"
                  class="small"
                />

                <span v-if="!row.assigneeUserId" class="no-assignee">未分配</span>
              </div>
            </template>
          </el-table-column>



          <el-table-column label="状态" width="120" align="center">
            <template #default="{ row }">
              <div class="status-cell">
                <el-select
                  :model-value="row.status"
                  @change="(value) => handleStatusChange(row, value)"
                  size="small"
                  class="status-select"
                  :class="`status-select--${row.status?.toLowerCase()}`"
                >
                  <el-option label="📋 待处理" value="Todo" class="status-option--todo" />
                  <el-option label="🔄 进行中" value="InProgress" class="status-option--inprogress" />
                  <el-option label="✅ 已完成" value="Done" class="status-option--done" />
                  <el-option label="❌ 已取消" value="Cancelled" class="status-option--cancelled" />
                </el-select>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column label="开始日期" width="120">
            <template #default="{ row }">
              <div class="date-cell">
                <span v-if="row.planStartDate" class="start-date">
                  {{ formatDate(row.planStartDate) }}
                </span>
                <span v-else class="no-date">未设置</span>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column label="截止日期" width="150">
            <template #default="{ row }">
              <div v-if="row.planEndDate" class="date-cell">
                <div class="due-date-container">
                  <span :class="{ 'overdue': isOverdue(row), 'due-soon': isDueSoon(row) }">
                    {{ formatDate(row.planEndDate) }}
                  </span>
                  <div v-if="isOverdue(row)" class="overdue-warning">
                    <el-text type="danger" size="small">
                      已逾期 {{ getOverdueDays(row) }} 天
                    </el-text>
                  </div>
                </div>
              </div>
              <span v-else class="no-date">未设置</span>
            </template>
          </el-table-column>
          
          <el-table-column label="操作" width="220" align="center">
            <template #default="{ row }">
              <div class="action-buttons">
                <!-- 游戏化任务领取按钮 -->
                <el-button
                  v-if="canClaimTask(row)"
                  type="success"
                  size="small"
                  @click="claimTask(row)"
                  :loading="row.claiming"
                  class="claim-button"
                >
                  <el-icon><Trophy /></el-icon>
                  领取
                </el-button>

                <!-- 当前用户已领取标识 -->
                <el-tag
                  v-else-if="isTaskClaimedByCurrentUser(row)"
                  type="success"
                  size="small"
                  class="claimed-tag claimed-by-me"
                >
                  <el-icon class="status-icon"><CircleCheckFilled /></el-icon>
                  <span class="status-text">我已领取</span>
                  <div class="status-indicator"></div>
                </el-tag>

                <!-- 其他人已领取标识 -->
                <el-tag
                  v-else-if="isTaskClaimedByOthers(row)"
                  type="info"
                  size="small"
                  class="claimed-tag claimed-by-others"
                >
                  <el-icon class="status-icon"><User /></el-icon>
                  <span class="status-text">{{ row.claimedByUserName || '其他人' }}已领取</span>
                  <div class="status-indicator"></div>
                </el-tag>

                <!-- 常规操作按钮 -->
                <el-button link size="small" @click="showTaskDetail(row)">
                  <el-icon><View /></el-icon>
                </el-button>
                <el-button link size="small" @click="editTask(row)">
                  <el-icon><Edit /></el-icon>
                </el-button>
                <el-dropdown @command="(command) => handleQuickAction(command, row)">
                  <el-button link size="small">
                    <el-icon><More /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="clone">克隆任务</el-dropdown-item>
                      <el-dropdown-item command="assign">重新分配</el-dropdown-item>
                      <el-dropdown-item command="complete" v-if="row.status !== 'Done'">标记完成</el-dropdown-item>
                      <el-dropdown-item command="delete" divided>删除任务</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 卡片视图 -->
      <div v-else class="card-view">
        <div class="task-cards-grid">
          <EnhancedTaskCard
            v-for="task in paginatedTasks"
            :key="task.taskId"
            :task="task"
            :selected="selectedTaskIds.includes(task.taskId)"
            @select="toggleTaskSelection"
            @click="showTaskDetail"
            @quick-action="handleTaskQuickAction"
            @status-change="handleQuickStatusChange"
          />
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :small="false"
          :disabled="loading"
          :background="true"
          layout="total, sizes, prev, pager, next, jumper"
          :total="filteredTasks.length"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 对话框：任务详情 -->
    <TaskDetailDialog
      v-model="showDetailDialog"
      :task="currentTask"
      @close="showDetailDialog = false"
      @updated="onTaskUpdated"
    />

    <!-- 对话框：批量分配 -->
    <BatchAssignDialog
      v-model="showBatchAssignDialog"
      :task-ids="selectedTaskIds"
      @close="showBatchAssignDialog = false"
      @assigned="onBatchAssigned"
    />

    <!-- 对话框：批量状态修改 -->
    <BatchStatusDialog
      v-model="showBatchStatusDialog"
      :task-ids="selectedTaskIds"
      @close="showBatchStatusDialog = false"
      @updated="onBatchUpdated"
    />

    <!-- 对话框：任务表单 -->
    <TaskFormDialog
      v-model:visible="showCreateDialog"
      :isEdit="isEditMode"
      :formData="editingTask"
      @close="closeTaskForm"
      @submit="onTaskFormSubmit"
    />

    <!-- 对话框：任务领取 -->
    <TaskClaimDialog
      v-model="showClaimDialog"
      @success="onTaskClaimed"
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  DocumentAdd, Refresh, Search, User, Edit, Delete, Grid, Collection,
  View, More, Trophy, CircleCheckFilled
} from '@element-plus/icons-vue'
import { debounce } from 'lodash-es'

// 组件导入
import QuickTaskCreator from '@/components/Tasks/QuickTaskCreatorSimple.vue'
import EnhancedTaskCard from '@/components/Tasks/EnhancedTaskCard.vue'
import UserAvatar from '@/components/UserAvatar.vue'
import UserAvatarStack from '@/components/UserAvatarStack.vue'
import TaskDetailDialog from './components/TaskDetailDialog.vue'
import BatchAssignDialog from './components/BatchAssignDialog.vue'
import BatchStatusDialog from './components/BatchStatusDialog.vue'
import TaskFormDialog from './components/TaskFormDialog.vue'
import TaskClaimDialog from '@/components/Tasks/TaskClaimDialog.vue'

// Store 和 API
import { useTaskEnhancedStore } from '@/stores/modules/taskEnhanced'
import { useGamificationStore } from '@/stores/modules/gamification'
import { taskApi } from '@/api/task'
import userApi from '@/api/user'
import taskCategoryApi from '@/api/taskCategory'
import gamificationApi from '@/api/gamification'
import { getFullAvatarUrl } from '@/stores/modules/user'
import { ElNotification } from 'element-plus'

// 响应式数据
const taskStore = useTaskEnhancedStore()
const gamificationStore = useGamificationStore()
const loading = ref(false)
const viewMode = ref('table') // 'table' | 'card'
const users = ref([])
const categories = ref([])
const categoriesLoading = ref(false)
const searchQuery = ref('')
const dateRange = ref([])
const selectedTasks = ref([])
const editingTask = ref(null)
const isEditMode = ref(false)
const todayViewedCount = ref(0)
const currentUser = ref(null)

// 过滤器
const filters = reactive({
  status: '',
  priority: '',
  categoryId: '', // 任务分类
  assigneeIds: [] // 多选负责人
})

// 分页
const pagination = reactive({
  currentPage: 1,
  pageSize: 20
})

// 对话框状态
const showDetailDialog = ref(false)
const showCreateDialog = ref(false)
const showBatchAssignDialog = ref(false)
const showBatchStatusDialog = ref(false)
const showClaimDialog = ref(false)
const currentTask = ref(null)

// 计算属性
const filteredTasks = computed(() => {
  let tasks = taskStore.tasks || []
  
  // 应用状态过滤
  if (filters.status) {
    tasks = tasks.filter(task => task.status === filters.status)
  }
  
  // 应用优先级过滤
  if (filters.priority) {
    tasks = tasks.filter(task => task.priority === filters.priority)
  }

  // 应用分类过滤
  if (filters.categoryId) {
    if (filters.categoryId === 'null') {
      // 过滤未分类的任务
      tasks = tasks.filter(task => !task.categoryId)
    } else {
      // 过滤指定分类的任务
      tasks = tasks.filter(task => task.categoryId === parseInt(filters.categoryId))
    }
  }

  // 应用负责人多选过滤
  if (filters.assigneeIds && filters.assigneeIds.length > 0) {
    tasks = tasks.filter(task => filters.assigneeIds.includes(task.assigneeUserId))
  }
  
  // 应用搜索
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    tasks = tasks.filter(task => 
      task.name?.toLowerCase().includes(query) ||
      task.description?.toLowerCase().includes(query)
    )
  }
  
  // 应用日期范围
  if (dateRange.value && dateRange.value.length === 2) {
    const [start, end] = dateRange.value
    tasks = tasks.filter(task => {
      if (!task.planEndDate) return false
      const taskDate = new Date(task.planEndDate)
      return taskDate >= start && taskDate <= end
    })
  }
  
  return tasks
})

const paginatedTasks = computed(() => {
  const start = (pagination.currentPage - 1) * pagination.pageSize
  const end = start + pagination.pageSize
  return filteredTasks.value.slice(start, end)
})

const taskStats = computed(() => {
  const tasks = filteredTasks.value
  return {
    total: tasks.length,
    inProgress: tasks.filter(t => t.status === 'InProgress').length,
    completed: tasks.filter(t => t.status === 'Done').length,
    overdue: tasks.filter(t => isOverdue(t)).length
  }
})

const selectedTaskIds = computed(() => selectedTasks.value.map(t => t.taskId))

// 用户信息映射缓存 - 性能优化
const userMap = computed(() => {
  const map = new Map()
  users.value.forEach(user => {
    if (user && user.id) {
      map.set(user.id, user)
    }
  })
  return map
})

// 优化的任务用户信息映射 - 使用Map查找提升性能
const tasksWithAvatar = computed(() => {
  return paginatedTasks.value.map(row => {
    // 防御性编程，确保数据安全
    if (!row) return row

    // 使用Map快速查找用户信息，避免每次都遍历数组
    const user = userMap.value.get(row.assigneeUserId)
    const completerUser = row.completedByUserId ? userMap.value.get(row.completedByUserId) : null

    return {
      ...row,
      assigneeUserName: user?.name || row.assigneeUserName || '未知用户',
      assigneeAvatarUrl: getFullAvatarUrl(row.assigneeAvatarUrl || user?.avatarUrl || user?.avatar || ''),
      // 预处理完成人信息，避免重复查找
      completerUser: completerUser ? {
        id: completerUser.id,
        name: completerUser.name || completerUser.username,
        avatar: completerUser.avatar || completerUser.avatarUrl || ''
      } : null
    }
  }).filter(Boolean) // 过滤掉空值
})

// API调用状态管理
const apiCallStates = reactive({
  loadingTasks: false,
  loadingUsers: false,
  lastTasksCall: 0,
  lastUsersCall: 0
})

// 防抖的任务加载函数
const debouncedLoadTasks = debounce(async (forceRefresh = false) => {
  // 防止重复调用
  if (apiCallStates.loadingTasks && !forceRefresh) {
    console.log('⏳ 任务加载中，跳过重复调用')
    return
  }

  // 检查调用频率限制 (最少间隔1秒)
  const now = Date.now()
  if (!forceRefresh && (now - apiCallStates.lastTasksCall) < 1000) {
    console.log('🚫 任务加载频率限制，跳过调用')
    return
  }

  apiCallStates.loadingTasks = true
  apiCallStates.lastTasksCall = now
  loading.value = true

  try {
    // 清除完成人缓存以确保数据一致性
    if (forceRefresh) {
      clearCompleterCache()
    }

    await taskStore.fetchTasks({
      status: filters.status,
      priority: filters.priority,
      assigneeIds: filters.assigneeIds,
      search: searchQuery.value,
      pageNumber: 1,
      pageSize: 1000, // 获取更多数据用于前端分页
      forceRefresh: forceRefresh // 添加强制刷新参数
    })
  } catch (error) {
    ElMessage.error('加载任务列表失败: ' + error.message)
  } finally {
    loading.value = false
    apiCallStates.loadingTasks = false
  }
}, 300) // 300ms防抖

// 兼容性包装函数
const loadTasks = (forceRefresh = false) => {
  return debouncedLoadTasks(forceRefresh)
}

// 用户数据缓存配置 - 智能缓存，保证实时性
const USER_CACHE_DURATION = 5 * 60 * 1000 // 5分钟缓存
const userCacheTimestamp = ref(0)

// 防抖的用户加载函数
const debouncedLoadUsers = debounce(async (forceRefresh = false) => {
  // 防止重复调用
  if (apiCallStates.loadingUsers && !forceRefresh) {
    console.log('⏳ 用户加载中，跳过重复调用')
    return
  }

  // 检查调用频率限制 (最少间隔2秒)
  const now = Date.now()
  if (!forceRefresh && (now - apiCallStates.lastUsersCall) < 2000) {
    console.log('🚫 用户加载频率限制，跳过调用')
    return
  }

  // 检查缓存是否有效（仅在非强制刷新时使用缓存）
  if (!forceRefresh && users.value.length > 0 &&
      (now - userCacheTimestamp.value) < USER_CACHE_DURATION) {
    console.log('🔄 使用用户数据缓存')
    return // 使用缓存，避免重复API调用
  }

  apiCallStates.loadingUsers = true
  apiCallStates.lastUsersCall = now

  try {
    console.log('📡 重新获取用户数据')
    const response = await userApi.getUserList()
    if (response && response.data) {
      users.value = response.data.map(user => {
        if (!user) return null
        return {
          id: user.id || user.userId || user.ID || 0,
          name: user.name || user.userName || user.username || user.displayName || '未知用户',
          department: user.department || user.departmentName || '',
          avatar: user.avatar || user.avatarUrl || ''
        }
      }).filter(user => user && user.id > 0)

      // 更新缓存时间戳
      userCacheTimestamp.value = now
      console.log(`✅ 用户数据加载完成，共 ${users.value.length} 个用户`)
    } else {
      users.value = []
    }
  } catch (error) {
    console.error('加载用户列表失败:', error)
    users.value = []
  } finally {
    apiCallStates.loadingUsers = false
  }
}, 500) // 500ms防抖

// 兼容性包装函数
const loadUsers = (forceRefresh = false) => {
  return debouncedLoadUsers(forceRefresh)
}

// 智能用户信息更新 - 当任务状态变更时动态更新用户信息
const updateUserInfoForTask = async (task, newStatus) => {
  // 如果任务变为完成状态，需要获取完成人信息
  if (newStatus === 'Done' && task.completedByUserId) {
    const existingUser = userMap.value.get(task.completedByUserId)
    if (!existingUser) {
      console.log('🔄 任务完成，获取完成人信息:', task.completedByUserId)
      // 如果完成人不在当前用户列表中，强制刷新用户数据
      await loadUsers(true)
    }
  }
}

const loadCurrentUser = async () => {
  try {
    const response = await userApi.getCurrentUser()
    if (response && response.data) {
      currentUser.value = {
        id: response.data.id || response.data.userId || response.data.ID,
        name: response.data.name || response.data.userName || response.data.username || response.data.displayName,
        department: response.data.department || response.data.departmentName || '',
        avatar: response.data.avatar || response.data.avatarUrl || ''
      }
    }
  } catch (error) {
    console.error('获取当前用户信息失败:', error)
    // 从localStorage获取用户信息作为备选
    const userInfo = localStorage.getItem('userInfo')
    if (userInfo) {
      try {
        const parsed = JSON.parse(userInfo)
        currentUser.value = {
          id: parsed.id || parsed.userId || parsed.ID,
          name: parsed.name || parsed.userName || parsed.username || parsed.displayName,
          department: parsed.department || parsed.departmentName || '',
          avatar: parsed.avatar || parsed.avatarUrl || ''
        }
      } catch (e) {
        console.error('解析本地用户信息失败:', e)
      }
    }
  }
}

// 加载任务分类列表
const loadTaskCategories = async () => {
  console.log('开始加载任务分类列表')
  categoriesLoading.value = true
  try {
    const response = await taskCategoryApi.getActiveCategories()
    console.log('任务分类API响应:', response)

    if (response && response.data) {
      categories.value = response.data
      console.log(`任务分类列表加载完成，共 ${categories.value.length} 个分类`)
    } else {
      console.warn('API返回的response或response.data为空')
      categories.value = []
    }
  } catch (error) {
    console.error('获取任务分类列表失败:', error)
    ElMessage.error('获取任务分类列表失败')
    categories.value = []
  } finally {
    categoriesLoading.value = false
  }
}

const handleSearch = () => {
  pagination.currentPage = 1
  loadTasks()
}

const resetFilters = () => {
  Object.assign(filters, { status: '', priority: '', categoryId: '', assigneeIds: [] })
  searchQuery.value = ''
  dateRange.value = []
  pagination.currentPage = 1
  loadTasks()
}

const handleDateRangeChange = () => {
  handleSearch()
}

const handleSelectionChange = (selection) => {
  selectedTasks.value = selection
}

const clearSelection = () => {
  selectedTasks.value = []
}

const toggleTaskSelection = (taskId, selected) => {
  if (selected) {
    const task = filteredTasks.value.find(t => t.taskId === taskId)
    if (task && !selectedTasks.value.find(t => t.taskId === taskId)) {
      selectedTasks.value.push(task)
    }
  } else {
    selectedTasks.value = selectedTasks.value.filter(t => t.taskId !== taskId)
  }
}

const handleSizeChange = (size) => {
  pagination.pageSize = size
  pagination.currentPage = 1
}

const handleCurrentChange = (page) => {
  pagination.currentPage = page
}

// 任务操作
const showTaskDetail = (task) => {
  currentTask.value = task
  showDetailDialog.value = true
  recordTaskView(task.taskId)
}

const editTask = async (task) => {
  try {
    // 获取完整的任务详情，包括所有负责人和协作者信息
    console.log('开始编辑任务，获取完整详情:', task.taskId)
    const response = await taskApi.getTaskDetail(task.taskId)
    
    if (response.success && response.data) {
      console.log('获取到完整任务详情:', response.data)
      editingTask.value = response.data
      isEditMode.value = true
      showCreateDialog.value = true
    } else {
      ElMessage.error('获取任务详情失败: ' + (response.message || '未知错误'))
    }
  } catch (error) {
    console.error('获取任务详情出错:', error)
    ElMessage.error('获取任务详情时发生错误')
  }
}

const showCreateFormDialog = () => {
  editingTask.value = null
  isEditMode.value = false
  showCreateDialog.value = true
}

const navigateToCreateTask = () => {
  showCreateFormDialog()
}

const closeTaskForm = () => {
  showCreateDialog.value = false
  editingTask.value = null
  isEditMode.value = false
}

const onTaskCreated = (newTask) => {
  ElMessage.success('任务创建成功!')
  loadTasks(true) // 强制刷新
  // 如果任务涉及新用户，可能需要刷新用户数据
  if (newTask?.assigneeUserId && !userMap.value.has(newTask.assigneeUserId)) {
    loadUsers(true) // 强制刷新用户数据
  }
}

const onTaskUpdated = () => {
  loadTasks(true) // 强制刷新，确保获取最新数据
  // 任务更新通常不需要刷新用户数据，除非涉及新用户
}

const handleExpandToFullForm = (formData) => {
  editingTask.value = formData
  isEditMode.value = false
  showCreateDialog.value = true
}

const handleQuickAction = async (command, task) => {
  switch (command) {
    case 'clone':
      await cloneTask(task)
      break
    case 'assign':
      selectedTasks.value = [task]
      showBatchAssignDialog.value = true
      break
    case 'complete':
      await completeTask(task)
      break
    case 'delete':
      await deleteTask(task)
      break
  }
}

const handleTaskQuickAction = (payload) => {
  handleQuickAction(payload.action, payload.task)
}

const handleQuickStatusChange = async (payload) => {
  try {
    await taskStore.updateTaskStatus(payload.taskId, payload.newStatus)
    ElMessage.success('任务状态更新成功')
    loadTasks(true) // 强制刷新
  } catch (error) {
    ElMessage.error('状态更新失败: ' + error.message)
  }
}

const cloneTask = async (task) => {
  try {
    const clonedTask = {
      ...task,
      name: `${task.name} (副本)`,
      status: 'Todo',
      progress: 0
    }
    delete clonedTask.taskId
    
    await taskStore.createTask(clonedTask)
    ElMessage.success('任务克隆成功')
    loadTasks(true) // 强制刷新
  } catch (error) {
    ElMessage.error('任务克隆失败: ' + error.message)
  }
}

const completeTask = async (task) => {
  try {
    // 使用专门的完成任务API，这样会自动设置完成用户和水印
    const response = await taskApi.completeTask(task.taskId, {
      remarks: `任务完成操作`
    })

    if (response && response.success) {
      ElMessage.success('任务已标记为完成')

      // 触发游戏化奖励
      const isOnTime = task.planEndDate ? new Date() <= new Date(task.planEndDate) : false
      gamificationStore.recordEvent('task_completed', {
        taskId: task.taskId,
        taskName: task.name,
        isOnTime: isOnTime
      })

      loadTasks(true) // 强制刷新以显示水印
    } else {
      ElMessage.error(response?.message || '任务完成失败')
    }
  } catch (error) {
    console.error('完成任务失败:', error)
    ElMessage.error('操作失败: ' + error.message)
  }
}

const deleteTask = async (task) => {
  try {
    await ElMessageBox.confirm(`确定要删除任务"${task.name}"吗？`, '确认删除', {
      type: 'warning'
    })
    await taskStore.deleteTask(task.taskId)
    ElMessage.success('任务删除成功')
    loadTasks(true) // 强制刷新
  } catch (error) {
    if (error?.response?.status === 404) {
      ElMessage.error('任务已被删除或不存在')
      loadTasks(true) // 强制刷新
    } else if (error !== 'cancel') {
      ElMessage.error('删除失败: ' + error.message)
    }
  }
}

// 批量操作
const batchDeleteTasks = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedTasks.value.length} 个任务吗？`, 
      '确认批量删除', 
      { type: 'warning' }
    )
    await taskStore.batchDeleteTasks(selectedTaskIds.value)
    ElMessage.success('批量删除成功')
    clearSelection()
    loadTasks()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败: ' + error.message)
    }
  }
}

const onBatchAssigned = () => {
  ElMessage.success('批量分配成功')
  clearSelection()
  loadTasks(true) // 强制刷新，确保显示最新的负责人分配
}

const onBatchUpdated = () => {
  ElMessage.success('批量状态修改成功')
  clearSelection()
  loadTasks(true) // 强制刷新，确保显示最新的状态更新
}

// 工具方法
const truncateText = (text, length) => {
  if (!text || text.length <= length) return text
  return text.substring(0, length) + '...'
}

const getCollaborators = (task) => {
  if (!task) return []
  return task.collaborators || task.assignees?.filter(a => a && a.role !== 'Primary') || task.participants || []
}

const getPriorityTagType = (priority) => {
  const types = { High: 'danger', Medium: 'warning', Low: 'info' }
  return types[priority] || 'info'
}

const getPriorityIcon = (priority) => {
  const icons = { High: '🔴', Medium: '🟡', Low: '🟢' }
  return icons[priority] || '⚪'
}

const getPriorityText = (priority) => {
  const texts = { High: '高', Medium: '中', Low: '低' }
  return texts[priority] || priority
}

const getStatusTagType = (status) => {
  const types = { Todo: 'info', InProgress: 'warning', Done: 'success', Cancelled: 'danger' }
  return types[status] || 'info'
}

const getStatusIcon = (status) => {
  const icons = { Todo: '📋', InProgress: '🔄', Done: '✅', Cancelled: '❌' }
  return icons[status] || '❓'
}

const getStatusText = (status) => {
  const texts = { Todo: '待处理', InProgress: '进行中', Done: '已完成', Cancelled: '已取消' }
  return texts[status] || status
}

const getProgressColor = (progress) => {
  if (progress >= 80) return '#67c23a'
  if (progress >= 50) return '#e6a23c'
  if (progress >= 20) return '#409eff'
  return '#f56c6c'
}

const isOverdue = (task) => {
  if (!task.planEndDate || task.status === 'Done') return false
  return new Date(task.planEndDate) < new Date()
}

const isDueSoon = (task) => {
  if (!task.planEndDate || task.status === 'Done') return false
  const dueDate = new Date(task.planEndDate)
  const today = new Date()
  const diffDays = Math.ceil((dueDate - today) / (1000 * 60 * 60 * 24))
  return diffDays <= 2 && diffDays >= 0
}

const formatRelativeDate = (date) => {
  if (!date) return ''
  const d = new Date(date)
  const now = new Date()
  const diffDays = Math.ceil((d - now) / (1000 * 60 * 60 * 24))
  
  if (diffDays < 0) return `${Math.abs(diffDays)}天前`
  if (diffDays === 0) return '今天'
  if (diffDays === 1) return '明天'
  if (diffDays <= 7) return `${diffDays}天后`
  return d.toLocaleDateString()
}

const formatDate = (date) => {
  if (!date) return ''
  return new Date(date).toLocaleDateString('zh-CN')
}

const getOverdueDays = (task) => {
  if (!task.planEndDate) return 0
  const dueDate = new Date(task.planEndDate)
  const today = new Date()
  return Math.floor((today - dueDate) / (1000 * 60 * 60 * 24))
}

const handleStatusChange = async (task, newStatus) => {
  try {
    loading.value = true
    console.log('开始更新任务状态:', { taskId: task.taskId, newStatus })

    // 保存原始状态用于错误回滚
    const originalStatus = task.status
    const originalStatusName = task.statusName

    // 乐观更新 - 立即更新UI提供快速反馈
    task.status = newStatus
    task.statusName = getStatusDisplayName(newStatus)

    // 🔥 关键：任务状态变更时的实时用户信息更新
    await updateUserInfoForTask(task, newStatus)

    // 调用API更新状态
    const response = await taskStore.updateTaskStatus(task.taskId, newStatus, '通过状态下拉框更新')

    console.log('状态更新API响应:', response)

    // 调试API响应
    debugApiResponse({ success: true, data: response })

    if (response) {
      // 精准更新：只更新变化的任务项，使用响应式数组方法
      const taskIndex = taskStore.tasks?.findIndex(t => t && t.taskId === task.taskId) ?? -1
      if (taskIndex !== -1 && taskStore.tasks) {
        // 使用splice确保响应式更新，合并API返回的完整数据
        const updatedTask = {
          ...taskStore.tasks[taskIndex],
          ...response
        }

        // 验证完成人数据
        console.log('🔍 验证完成人数据:', {
          newStatus,
          completedByUserId: updatedTask.completedByUserId,
          completedByUserName: updatedTask.completedByUserName,
          completedByUserAvatarUrl: updatedTask.completedByUserAvatarUrl,
          actualEndDate: updatedTask.actualEndDate,
          currentUser: currentUser.value
        })

        // 如果是完成状态且后端没有返回完成人信息，使用当前用户信息作为备用
        if (newStatus === 'Done' && !updatedTask.completedByUserId && currentUser.value) {
          console.log('⚠️ 后端未返回完成人信息，使用当前用户作为备用')
          updatedTask.completedByUserId = currentUser.value.id
          updatedTask.completedByUserName = currentUser.value.name || currentUser.value.username
          updatedTask.completedByUserAvatarUrl = currentUser.value.avatar || currentUser.value.avatarUrl
          updatedTask.completedAt = updatedTask.actualEndDate || new Date().toISOString()

          console.log('✅ 使用当前用户作为完成人备用信息:', {
            completedByUserId: updatedTask.completedByUserId,
            completedByUserName: updatedTask.completedByUserName
          })
        } else if (newStatus === 'Done' && updatedTask.completedByUserId) {
          console.log('✅ 后端正确返回了完成人信息:', {
            completedByUserId: updatedTask.completedByUserId,
            completedByUserName: updatedTask.completedByUserName
          })
        } else if (newStatus === 'Done') {
          console.log('❌ 任务已完成但没有完成人信息，这可能是一个问题')
        }
        taskStore.tasks.splice(taskIndex, 1, updatedTask)

        // 同时更新本地task对象以确保UI立即更新
        Object.assign(task, updatedTask)
      }

      // 如果状态变为完成，触发游戏化奖励
      if (newStatus === 'Done') {
        try {
          const isOnTime = task.planEndDate ? new Date() <= new Date(task.planEndDate) : false
          await gamificationStore.recordEvent('task_completed', {
            taskId: task.taskId,
            taskName: task.name,
            isOnTime: isOnTime
          })
          // 游戏化奖励成功后显示完成消息
          ElMessage.success('任务已完成，获得奖励！')
        } catch (error) {
          console.error('触发游戏化奖励失败:', error)
          // 即使游戏化奖励失败，任务状态更新仍然成功
          ElMessage.success('任务状态更新成功')
        }
      } else {
        // 非完成状态的普通更新消息
        ElMessage.success('状态更新成功')
      }

      console.log('任务状态更新完成，无需刷新整个列表')

    } else {
      throw new Error('API响应为空')
    }
  } catch (error) {
    console.error('状态更新失败，执行回滚:', {
      error: error,
      message: error.message,
      taskId: task.taskId,
      originalStatus: originalStatus
    })

    // 错误回滚：恢复原始状态
    task.status = originalStatus
    task.statusName = originalStatusName

    ElMessage.error('状态更新失败: ' + (error.message || '未知错误'))
  } finally {
    loading.value = false
  }
}

// 获取状态显示名称的辅助函数
const getStatusDisplayName = (status) => {
  const statusMap = {
    'Todo': '待处理',
    'InProgress': '进行中',
    'Done': '已完成',
    'Cancelled': '已取消'
  }
  return statusMap[status] || status
}

const getUserById = (id) => users.value.find(u => u.id === id)

// 生命周期
onMounted(() => {
  loadTasks()
  loadUsers()
  loadTaskCategories()
  loadCurrentUser()
  loadTodayViewedCount()
})

// 组件卸载时清理
onUnmounted(() => {
  // 取消防抖函数
  debouncedLoadTasks.cancel()
  debouncedLoadUsers.cancel()

  // 重置API调用状态
  apiCallStates.loadingTasks = false
  apiCallStates.loadingUsers = false

  console.log('🧹 组件卸载，清理API调用状态')
})

// 实现 onTaskFormSubmit 方法，调用后端创建/编辑接口，成功后关闭弹窗并刷新任务列表。
const onTaskFormSubmit = async (taskData) => {
  try {
    if (isEditMode.value) {
      // 确保在更新时传递正确的taskId
      const taskToUpdate = {
        ...taskData,
        taskId: editingTask.value.taskId
      }
      console.log('编辑任务，提交数据:', taskToUpdate)
      await taskStore.updateTask(taskToUpdate)
      ElMessage.success('任务更新成功!')
    } else {
      console.log('创建任务，提交数据:', taskData)
      await taskStore.createTask(taskData)
      ElMessage.success('任务创建成功!')
    }
    closeTaskForm()
    
    // 重新加载任务列表以更新头像显示
    console.log('任务操作完成，重新加载任务列表')
    await loadTasks()
    
  } catch (error) {
    console.error('任务操作失败:', error)
    ElMessage.error('操作失败: ' + (error.message || '未知错误'))
  }
}

async function recordTaskView(taskId) {
  await taskApi.recordTaskView(taskId)
  await loadTodayViewedCount()
}

async function loadTodayViewedCount() {
  const res = await taskApi.getTodayViewedCount()
  todayViewedCount.value = res.data?.count || 0
}

// 游戏化任务领取相关方法
const canClaimTask = (task) => {
  // 任务可以被领取的条件：
  // 1. 任务状态不是已完成或已取消
  // 2. 任务没有被分配给任何人，或者分配给当前用户
  // 3. 任务没有被任何人领取（今日）
  return task.status !== 'Done' && task.status !== 'Cancelled' &&
         (!task.assigneeUserId || task.assigneeUserId === currentUser.value?.id) &&
         !task.claimedByUserId
}

const isTaskClaimed = (task) => {
  // 检查任务是否已被领取（包括被当前用户领取）
  return !!task.claimedByUserId
}

const isTaskClaimedByCurrentUser = (task) => {
  // 检查任务是否被当前用户领取
  return task.claimedByUserId === currentUser.value?.id
}

const isTaskClaimedByOthers = (task) => {
  // 检查任务是否被其他人领取
  return task.claimedByUserId && task.claimedByUserId !== currentUser.value?.id
}

const claimTask = async (task) => {
  if (!canClaimTask(task)) {
    ElMessage.warning('该任务无法领取')
    return
  }

  try {
    // 设置领取状态
    task.claiming = true

    // 调用游戏化任务领取API (已集成奖励系统)
    const response = await gamificationApi.claimTask(task.taskId, `用户主动领取任务: ${task.name}`)

    console.log('游戏化任务领取API响应:', response)

    if (response && response.success) {
      // 🎮 显示游戏化奖励消息
      const rewardMessage = response.data?.reward ?
        `🎉 成功领取任务: ${task.name}\n🎁 获得 ${response.data.reward.pointsGained} 金币 + ${response.data.reward.xpGained} 经验值` :
        `🎉 成功领取任务: ${task.name}`

      ElMessage.success({
        message: rewardMessage,
        duration: 4000,
        showClose: true,
        dangerouslyUseHTMLString: true
      })

      // 🎮 如果升级了，显示升级提示
      if (response.data?.reward?.leveledUp) {
        ElNotification.success({
          title: '🎊 恭喜升级！',
          message: `您已升级到 ${response.data.reward.newLevel} 级！`,
          duration: 5000,
          position: 'top-right'
        })
      }

      // 刷新游戏化数据
      await refreshGamificationData()

      // 🔧 优化：使用精确更新而不是全量刷新，避免状态闪烁
      // 先更新本地状态以提供即时反馈
      const updatedTask = {
        ...task,
        claimedByUserId: currentUser.value?.id,
        claimedByUserName: currentUser.value?.name || currentUser.value?.username,
        claiming: false
      }

      // 在任务列表中找到对应任务并更新
      const taskIndex = taskStore.tasks?.findIndex(t => t.taskId === task.taskId) ?? -1
      if (taskIndex !== -1 && taskStore.tasks) {
        // 使用 Vue 3 的响应式更新
        taskStore.tasks[taskIndex] = updatedTask
      }

      console.log('任务领取成功，本地状态已更新:', {
        taskId: updatedTask.taskId,
        claimedByUserId: updatedTask.claimedByUserId,
        claimedByUserName: updatedTask.claimedByUserName,
        status: updatedTask.status
      })

      // 🔧 延迟刷新：给用户足够时间看到即时反馈，然后同步服务器状态
      // 使用 setTimeout 避免立即覆盖本地状态
      setTimeout(async () => {
        try {
          // 只获取这个特定任务的最新状态，而不是刷新整个列表
          const taskDetailResponse = await taskApi.getTaskById(task.taskId)
          if (taskDetailResponse.success && taskDetailResponse.data) {
            const serverTask = taskDetailResponse.data
            const taskIndex = taskStore.tasks?.findIndex(t => t.taskId === task.taskId) ?? -1
            if (taskIndex !== -1 && taskStore.tasks) {
              // 合并服务器状态，保持本地的 claiming 状态
              taskStore.tasks[taskIndex] = {
                ...serverTask,
                claiming: false
              }
              console.log('任务状态已与服务器同步:', serverTask)
            }
          }
        } catch (error) {
          console.warn('同步任务状态失败，但不影响用户体验:', error)
        }
      }, 1500) // 1.5秒后同步，给用户足够的视觉反馈时间

    } else {
      ElMessage.error(response?.message || response?.error || '任务领取失败')
    }
  } catch (error) {
    console.error('任务领取失败:', error)
    ElMessage.error('任务领取失败，请稍后重试')
  } finally {
    // 确保 claiming 状态被重置
    task.claiming = false
  }
}

// 🎮 刷新游戏化数据
const refreshGamificationData = async () => {
  try {
    // 这里可以触发游戏化store的数据刷新
    // 或者发送事件通知其他组件更新游戏化数据
    console.log('刷新游戏化数据')
  } catch (error) {
    console.error('刷新游戏化数据失败:', error)
  }
}

// 获取表格行样式类 - 根据任务状态设置不同的视觉效果
const getTableRowClass = ({ row }) => {
  const classes = ['task-row']

  // 根据任务状态添加不同的样式类
  switch (row.status) {
    case 'Todo':
      classes.push('task-row--todo')
      break
    case 'InProgress':
      classes.push('task-row--in-progress')
      break
    case 'Done':
      classes.push('task-row--done')
      break
    case 'Cancelled':
      classes.push('task-row--cancelled')
      break
  }

  // 逾期任务特殊样式
  if (isOverdue(row) && row.status !== 'Done') {
    classes.push('task-row--overdue')
  }

  // 即将到期任务特殊样式
  if (isDueSoon(row) && row.status !== 'Done') {
    classes.push('task-row--due-soon')
  }

  return classes.join(' ')
}

// 获取所有负责人和协作者 - 修复显示逻辑
const getAllAssignees = (task) => {
  if (!task) return []

  console.log('getAllAssignees - 原始task数据:', {
    taskId: task.taskId,
    assigneeUserId: task.assigneeUserId,
    assigneeUserName: task.assigneeUserName,
    assignees: task.assignees,
    participants: task.participants
  })

  const assignees = []

  // 优先使用后端返回的 assignees 字段（包含完整的负责人信息）
  if (task.assignees && Array.isArray(task.assignees) && task.assignees.length > 0) {
    task.assignees.forEach(assignee => {
      assignees.push({
        id: assignee.userId,
        name: assignee.userName || '未知用户',
        avatarUrl: getFullAvatarUrl(assignee.avatarUrl || ''),
        role: assignee.role || (assignee.assignmentType === 'Assignee' ? 'Primary' : 'Collaborator'),
        isPrimary: assignee.role === 'Primary' || assignee.assignmentType === 'Assignee'
      })
      console.log('添加负责人:', {
        id: assignee.userId,
        name: assignee.userName,
        role: assignee.role,
        assignmentType: assignee.assignmentType
      })
    })
  } else {
    // 如果没有 assignees 字段，回退到旧逻辑（仅显示主负责人）
    if (task.assigneeUserId) {
      assignees.push({
        id: task.assigneeUserId,
        name: task.assigneeUserName || '未知用户',
        avatarUrl: getFullAvatarUrl(task.assigneeAvatarUrl || ''),
        role: 'Primary',
        isPrimary: true
      })
      console.log('回退逻辑 - 添加主负责人:', {
        id: task.assigneeUserId,
        name: task.assigneeUserName
      })
    }
  }

  console.log('最终负责人列表:', assignees)
  return assignees
}

// 获取完成人用户信息 - 优化版本，优先使用预处理的数据
const getCompleterUser = (task) => {
  if (!task.completedByUserId) return null

  // 优先使用预处理的完成人信息（来自 tasksWithAvatar 计算属性）
  if (task.completerUser) {
    return task.completerUser
  }

  // 备用方案：从用户映射中查找
  const completerUser = userMap.value.get(task.completedByUserId)
  if (completerUser) {
    return {
      id: completerUser.id,
      name: completerUser.name || completerUser.username,
      avatar: completerUser.avatar || completerUser.avatarUrl || ''
    }
  }

  // 最后备用：使用任务中的完成人信息
  return {
    id: task.completedByUserId,
    name: task.completedByUserName || '未知用户',
    avatar: task.completedByUserAvatarUrl || ''
  }
}

// 完成人信息缓存（性能优化）
const completerCache = ref(new Map())

// 获取缓存的完成人信息（性能优化）- 现在直接使用预处理的数据
const getCachedCompleterUser = (task) => {
  // 直接使用 getCompleterUser，它已经优化为使用预处理数据
  return getCompleterUser(task)
}

// 清除完成人缓存
const clearCompleterCache = () => {
  completerCache.value.clear()
}

// 获取水印文本的方法
const getWatermarkText = (task) => {
  console.log('🔍 获取水印文本 - 任务数据:', {
    taskId: task.taskId,
    taskName: task.name,
    status: task.status,
    completedByUserId: task.completedByUserId,
    completedByUserName: task.completedByUserName,
    completedByUserAvatarUrl: task.completedByUserAvatarUrl,
    completedAt: task.completedAt,
    actualEndDate: task.actualEndDate,
    // 添加所有可能的完成人相关字段
    completedBy: task.completedBy,
    completer: task.completer,
    finishedBy: task.finishedBy,
    finishedByUserId: task.finishedByUserId,
    finishedByUserName: task.finishedByUserName
  })

  // 优先使用 completedByUserName
  if (task.completedByUserName) {
    console.log('✅ 使用 completedByUserName:', task.completedByUserName)
    return `完成人：${task.completedByUserName}`
  }

  // 检查其他可能的完成人姓名字段
  if (task.finishedByUserName) {
    console.log('✅ 使用 finishedByUserName:', task.finishedByUserName)
    return `完成人：${task.finishedByUserName}`
  }

  // 其次尝试从缓存获取用户信息
  const cachedUser = getCachedCompleterUser(task)
  if (cachedUser?.name) {
    console.log('✅ 使用缓存用户信息:', cachedUser.name)
    return `完成人：${cachedUser.name}`
  }

  // 再次尝试从用户列表获取
  const completerUser = getCompleterUser(task)
  if (completerUser?.name) {
    console.log('✅ 使用用户列表信息:', completerUser.name)
    return `完成人：${completerUser.name}`
  }

  // 如果有完成人ID但没有姓名，显示ID
  if (task.completedByUserId) {
    console.log('⚠️ 只有完成人ID，没有姓名:', task.completedByUserId)
    return `完成人：用户${task.completedByUserId}`
  }

  // 检查其他可能的完成人ID字段
  if (task.finishedByUserId) {
    console.log('⚠️ 只有 finishedByUserId，没有姓名:', task.finishedByUserId)
    return `完成人：用户${task.finishedByUserId}`
  }

  // 如果任务已完成但没有完成人信息，尝试使用当前用户
  if (task.status === 'Done' && currentUser.value) {
    console.log('⚠️ 使用当前用户作为完成人备用:', currentUser.value.name)
    return `完成人：${currentUser.value.name || currentUser.value.username || '当前用户'}`
  }

  // 默认显示
  console.log('❌ 没有完成人信息，使用默认显示')
  return '已完成'
}

// 验证完成人逻辑的调试方法
const debugCompleterInfo = (task) => {
  console.log('🔍 完成人信息调试:', {
    taskId: task.taskId,
    taskName: task.name,
    status: task.status,
    completedByUserId: task.completedByUserId,
    completedByUserName: task.completedByUserName,
    completedByUserAvatarUrl: task.completedByUserAvatarUrl,
    completedAt: task.completedAt,
    actualEndDate: task.actualEndDate,
    completerUser: getCompleterUser(task),
    cachedCompleterUser: getCachedCompleterUser(task),
    usersCount: users.value.length,
    currentUser: currentUser.value,
    cacheSize: completerCache.value.size,
    watermarkText: getWatermarkText(task),
    // 添加原始任务数据调试
    rawTaskData: {
      ...task
    }
  })
}

// 调试API返回的原始数据
const debugApiResponse = (response) => {
  console.log('🔍 API响应调试:', {
    success: response.success,
    data: response.data,
    completedByUserId: response.data?.completedByUserId,
    completedByUserName: response.data?.completedByUserName,
    completedByUserAvatarUrl: response.data?.completedByUserAvatarUrl,
    status: response.data?.status,
    actualEndDate: response.data?.actualEndDate
  })
}

// 测试完成人逻辑的方法
const testCompleterLogic = () => {
  console.log('🧪 开始测试完成人逻辑...')

  // 测试已完成的任务
  const completedTasks = taskStore.tasks?.filter(t => t.status === 'Done') || []
  console.log(`找到 ${completedTasks.length} 个已完成任务`)

  completedTasks.forEach((task, index) => {
    console.log(`\n--- 测试任务 ${index + 1} ---`)
    debugCompleterInfo(task)

    // 检查水印显示条件
    const shouldShowWatermark = task.status === 'Done' && (task.completedByUserName || task.completedByUserId)
    console.log('水印显示条件:', shouldShowWatermark)

    // 检查完成人列显示条件
    const shouldShowCompleter = task.status === 'Done' && (task.completedByUserName || task.completedByUserId)
    console.log('完成人列显示条件:', shouldShowCompleter)
  })

  console.log('🧪 完成人逻辑测试完成')
}

// 测试任务状态更新和完成人数据的方法
const testTaskStatusUpdate = async (taskId) => {
  try {
    console.log('🧪 开始测试任务状态更新...')

    const task = taskStore.tasks?.find(t => t.taskId === taskId)
    if (!task) {
      console.error('❌ 找不到指定的任务:', taskId)
      return
    }

    console.log('📋 原始任务数据:', {
      taskId: task.taskId,
      name: task.name,
      status: task.status,
      completedByUserId: task.completedByUserId,
      completedByUserName: task.completedByUserName
    })

    // 模拟状态更新
    console.log('🔄 模拟将任务状态更新为 Done...')
    await handleStatusChange(task, 'Done')

    console.log('✅ 状态更新测试完成')
  } catch (error) {
    console.error('❌ 状态更新测试失败:', error)
  }
}

// 测试API版本的方法
const testApiVersion = () => {
  console.log('🔍 当前API版本信息:')

  // 检查版本管理器
  const versionManager = window.$apiVersionManager || {}
  console.log('版本管理器当前版本:', versionManager.currentVersions || versionManager.getCurrentVersionStatus?.()?.versions)

  // 检查API适配器
  const apiAdapter = window.$apiAdapter || {}
  console.log('API适配器可用版本:', Object.keys(apiAdapter.apiVersions || {}))
  console.log('API适配器当前版本:', apiAdapter.getCurrentVersions?.())

  // 测试任务API调用
  console.log('测试任务API调用...')
  taskStore.fetchTasks({ pageNumber: 1, pageSize: 1 }).then(response => {
    console.log('✅ 任务API调用成功，使用的是v2版本')
    console.log('响应数据示例:', response)
  }).catch(error => {
    console.error('❌ 任务API调用失败:', error)
  })
}

// 调试特定任务的水印显示
const debugTaskWatermark = (taskName) => {
  const task = taskStore.tasks?.find(t => t.name?.includes(taskName) || t.taskId === taskName)
  if (!task) {
    console.error('❌ 找不到任务:', taskName)
    console.log('可用任务列表:', taskStore.tasks?.map(t => ({ id: t.taskId, name: t.name, status: t.status })))
    return
  }

  console.log('🔍 调试任务水印显示:')
  console.log('任务基本信息:', {
    taskId: task.taskId,
    name: task.name,
    status: task.status,
    description: task.description
  })

  console.log('完成人相关字段:', {
    completedByUserId: task.completedByUserId,
    completedByUserName: task.completedByUserName,
    completedByUserAvatarUrl: task.completedByUserAvatarUrl,
    completedAt: task.completedAt,
    actualEndDate: task.actualEndDate,
    // 检查其他可能的字段
    finishedByUserId: task.finishedByUserId,
    finishedByUserName: task.finishedByUserName,
    completedBy: task.completedBy,
    completer: task.completer
  })

  console.log('水印显示条件检查:')
  console.log('- status === "Done":', task.status === 'Done')
  console.log('- completedByUserName存在:', !!task.completedByUserName)
  console.log('- completedByUserId存在:', !!task.completedByUserId)
  console.log('- 显示条件满足:', task.status === 'Done' && (task.completedByUserName || task.completedByUserId))

  console.log('水印文本:', getWatermarkText(task))

  // 显示任务的完整数据结构
  console.log('📋 任务完整数据结构:')
  console.log(task)

  debugCompleterInfo(task)
}

// 调试所有已完成任务的完成人信息
const debugAllCompletedTasks = () => {
  const completedTasks = taskStore.tasks?.filter(t => t.status === 'Done') || []
  console.log(`🔍 找到 ${completedTasks.length} 个已完成任务`)

  completedTasks.forEach((task, index) => {
    console.log(`\n--- 已完成任务 ${index + 1} ---`)
    console.log('基本信息:', {
      taskId: task.taskId,
      name: task.name,
      status: task.status
    })
    console.log('完成人信息:', {
      completedByUserId: task.completedByUserId,
      completedByUserName: task.completedByUserName,
      watermarkText: getWatermarkText(task)
    })
  })
}

// 暴露测试方法到全局，方便在浏览器控制台调用
if (typeof window !== 'undefined') {
  window.testCompleterLogic = testCompleterLogic
  window.debugCompleterInfo = debugCompleterInfo
  window.debugApiResponse = debugApiResponse
  window.testTaskStatusUpdate = testTaskStatusUpdate
  window.testApiVersion = testApiVersion
  window.debugTaskWatermark = debugTaskWatermark
  window.debugAllCompletedTasks = debugAllCompletedTasks
  window.getWatermarkText = getWatermarkText
  window.taskStore = taskStore
}
</script>

<style scoped>
.enhanced-task-list {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-left h1 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
}

.subtitle {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.header-right {
  display: flex;
  gap: 12px;
}

.filter-card {
  margin-bottom: 12px;
  padding: 12px 16px 8px 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  align-items: center;
  padding: 0;
}

.filter-item {
  min-width: 140px;
  max-width: 180px;
  flex: 0 0 160px;
}

.search-input {
  min-width: 180px;
  max-width: 220px;
  flex: 1 1 200px;
}

.date-picker {
  min-width: 180px;
  max-width: 220px;
  flex: 1 1 200px;
}

.search-btn {
  min-width: 80px;
  padding: 0 12px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.stat-card {
  cursor: pointer;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  font-size: 32px;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.batch-actions-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 16px 24px;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1px solid #e4e7ed;
}

.batch-info {
  display: flex;
  align-items: center;
  gap: 12px;
  color: #606266;
}

.batch-actions {
  display: flex;
  gap: 8px;
}

.table-card {
  margin-bottom: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.enhanced-table {
  border-radius: 8px;
  overflow: hidden;
}

.task-name-cell {
  cursor: pointer;
}

.task-title {
  font-weight: 600;
  color: #409eff;
  margin-bottom: 4px;
}

.task-desc {
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
}

.assignee-cell {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 8px;
}

.main-assignee {
  display: flex;
  align-items: center;
  gap: 4px;
}

.assignee-name {
  margin-left: 4px;
  font-size: 13px;
  color: #303133;
}

.collaborators {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: 8px;
}

.collaborator-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.collaborator-name {
  font-size: 12px;
  color: #606266;
  margin-left: 2px;
}

.more-collaborators {
  font-size: 12px;
  color: #409eff;
  margin-left: 4px;
}

.no-assignee {
  color: #c0c4cc;
  font-size: 12px;
}

.completer-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.completer-info {
  display: flex;
  align-items: center;
  gap: 6px;
}

.completer-avatar {
  flex-shrink: 0;
}

.completer-name {
  font-size: 12px;
  color: #67c23a;
  font-weight: 500;
}

.no-completer {
  color: #c0c4cc;
  font-size: 12px;
}

.progress-cell {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.progress-text {
  font-size: 12px;
  color: #606266;
  text-align: center;
}

.date-cell .overdue {
  color: #f56c6c;
  font-weight: bold;
}

.date-cell .due-soon {
  color: #e6a23c;
  font-weight: bold;
}

.no-date {
  color: #c0c4cc;
  font-size: 12px;
}

.action-buttons {
  display: flex;
  gap: 4px;
  justify-content: center;
}

.card-view {
  padding: 16px 0;
}

.task-cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 16px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #e4e7ed;
}

.user-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.user-dept {
  font-size: 12px;
  color: #999;
}

/* 负责人单元格自定义样式 */
.assignee-cell :deep(.el-avatar) {
  transform: scale(0.7);
  transform-origin: left center;
}

/* 快速创建按钮样式 */
:deep(.quick-create-btn) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  font-weight: 600;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
  transition: all 0.3s ease;
  color: white;
}

:deep(.quick-create-btn:hover) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
}

/* 状态选择器样式 - 根据状态显示不同颜色 */
.status-select {
  width: 110px;
  transition: all 0.3s ease;
}

.status-select :deep(.el-input__inner) {
  text-align: center;
  font-size: 12px;
}

/* 任务领取按钮样式 - 增强视觉吸引力 */
.claim-button {
  background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
  border: none;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(103, 194, 58, 0.3);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  padding: 6px 16px;
  border-radius: 6px;
}

.claim-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s ease;
}

.claim-button:hover::before {
  left: 100%;
}

.claim-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(103, 194, 58, 0.5);
  background: linear-gradient(135deg, #5daf34 0%, #7bc142 100%);
}

.claim-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(103, 194, 58, 0.3);
}

/* 已领取标识样式 - 优化视觉效果 */
.claimed-tag {
  font-weight: 600;
  border-radius: 6px;
  padding: 4px 12px;
  font-size: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

/* 当前用户已领取 - 更明显的深绿色主题，增强对比度 */
.claimed-tag.el-tag--success {
  background: linear-gradient(135deg, #2d8f47 0%, #4caf50 100%);
  border: 2px solid #2d8f47;
  color: #ffffff;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  font-weight: 600;
  padding: 4px 12px;
  border-radius: 8px;
  box-shadow: 0 3px 10px rgba(45, 143, 71, 0.4);
  position: relative;
  overflow: hidden;
}

.claimed-tag.el-tag--success::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s ease;
}

.claimed-tag.el-tag--success:hover::before {
  left: 100%;
}

.claimed-tag.el-tag--success:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(45, 143, 71, 0.5);
  border-color: #1b5e20;
}

/* 其他人已领取 - 更明显的深橙色主题，增强对比度 */
.claimed-tag.el-tag--info {
  background: linear-gradient(135deg, #d84315 0%, #ff5722 100%);
  border: 2px solid #d84315;
  color: #ffffff;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  font-weight: 600;
  padding: 4px 12px;
  border-radius: 8px;
  box-shadow: 0 3px 10px rgba(216, 67, 21, 0.4);
  position: relative;
  overflow: hidden;
}

.claimed-tag.el-tag--info::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s ease;
}

.claimed-tag.el-tag--info:hover::before {
  left: 100%;
}

.claimed-tag.el-tag--info:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(216, 67, 21, 0.5);
  border-color: #bf360c;
}

/* 状态指示器增强效果 - 更明显的视觉反馈 */
.claimed-tag {
  position: relative;
  display: flex;
  align-items: center;
  gap: 8px;
  min-height: 28px;
}

.claimed-tag .status-icon {
  font-size: 16px;
  animation: pulse-icon 2s infinite;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

.claimed-tag .status-text {
  font-size: 13px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.claimed-tag .status-indicator {
  position: absolute;
  top: -3px;
  right: -3px;
  width: 10px;
  height: 10px;
  border: 2px solid rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  animation: blink-enhanced 2s infinite;
}

/* 当前用户已领取的指示器 - 更明显的绿色 */
.claimed-by-me .status-indicator {
  background: #81c784;
  box-shadow: 0 0 12px rgba(129, 199, 132, 0.8);
}

/* 其他人已领取的指示器 - 更明显的橙色 */
.claimed-by-others .status-indicator {
  background: #ffab40;
  box-shadow: 0 0 12px rgba(255, 171, 64, 0.8);
}

/* 动画效果 - 增强版本 */
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

@keyframes pulse-icon {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.15);
  }
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.3; }
}

@keyframes blink-enhanced {
  0%, 40% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.2);
  }
  60%, 100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* 待处理状态选择器 */
.status-select--todo :deep(.el-input__inner) {
  background-color: rgba(96, 98, 102, 0.15);
  border-color: #606266;
  color: #606266;
}

.status-select--todo:hover :deep(.el-input__inner) {
  background-color: rgba(96, 98, 102, 0.25);
  border-color: #606266;
}

/* 进行中状态选择器 */
.status-select--inprogress :deep(.el-input__inner) {
  background-color: rgba(24, 144, 255, 0.15);
  border-color: #1890ff;
  color: #1890ff;
  font-weight: 600;
}

.status-select--inprogress:hover :deep(.el-input__inner) {
  background-color: rgba(24, 144, 255, 0.25);
  border-color: #1890ff;
}

/* 已完成状态选择器 */
.status-select--done :deep(.el-input__inner) {
  background-color: rgba(82, 196, 26, 0.15);
  border-color: #52c41a;
  color: #52c41a;
  font-weight: 600;
}

.status-select--done:hover :deep(.el-input__inner) {
  background-color: rgba(82, 196, 26, 0.25);
  border-color: #52c41a;
}

/* 已取消状态选择器 */
.status-select--cancelled :deep(.el-input__inner) {
  background-color: rgba(255, 77, 79, 0.15);
  border-color: #ff4d4f;
  color: #ff4d4f;
}

.status-select--cancelled:hover :deep(.el-input__inner) {
  background-color: rgba(255, 77, 79, 0.25);
  border-color: #ff4d4f;
}

/* 状态选项样式 */
:deep(.status-option--todo) {
  color: #909399 !important;
}

:deep(.status-option--inprogress) {
  color: #409EFF !important;
}

:deep(.status-option--done) {
  color: #67C23A !important;
}

:deep(.status-option--cancelled) {
  color: #F56C6C !important;
}

/* 日期单元格样式 */
.date-cell {
  font-size: 12px;
  line-height: 1.4;
}

.start-date {
  color: #606266;
}

.due-date-container {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.overdue-warning {
  margin-top: 2px;
}

.overdue-warning .el-text {
  font-size: 11px !important;
  line-height: 1.2;
}

/* 状态单元格和完成水印样式 */
.status-cell {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

/* 任务名称单元格样式 */
.task-name-cell {
  position: relative;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.task-name-cell:hover {
  background-color: #f5f7fa;
}

.task-content {
  position: relative;
  z-index: 1;
}

.task-title {
  font-weight: 500;
  color: #303133;
  line-height: 1.4;
}

.task-desc {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.3;
}

/* 完成水印样式 - 真正的透明水印效果 */
.completion-watermark {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(-15deg);
  pointer-events: none;
  z-index: 2;
  user-select: none;
}

.watermark-text {
  font-size: 14px;
  font-weight: bold;
  color: var(--watermark-color, #409EFF);
  opacity: 0.4;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.15);
  border: 2px solid var(--watermark-color, #409EFF);
  padding: 4px 8px;
  border-radius: 6px;
  background: transparent;
  white-space: nowrap;
  transition: opacity 0.3s ease;
}

.task-name-cell:hover .watermark-text {
  opacity: 0.6;
}

/* 游戏化任务领取按钮样式 */
.claim-button {
  background: linear-gradient(135deg, #67c23a, #85ce61);
  border: none;
  color: white;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(103, 194, 58, 0.3);
  transition: all 0.3s ease;
  animation: pulse-glow 2s infinite;
}

.claim-button:hover {
  background: linear-gradient(135deg, #85ce61, #67c23a);
  box-shadow: 0 4px 12px rgba(103, 194, 58, 0.5);
  transform: translateY(-1px);
}

.claim-button:active {
  transform: translateY(0);
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 2px 8px rgba(103, 194, 58, 0.3);
  }
  50% {
    box-shadow: 0 2px 12px rgba(103, 194, 58, 0.6);
  }
}

.claimed-tag {
  background: linear-gradient(135deg, #909399, #c0c4cc);
  border: none;
  color: white;
  font-weight: 500;
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
  justify-content: center;
}

/* 紧凑优先级标签样式 */
.priority-tag-compact {
  min-width: 24px !important;
  padding: 2px 6px !important;
  font-size: 14px !important;
  border-radius: 50% !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-weight: bold !important;
}

/* 任务状态行样式 - 不同状态的视觉区分 */
:deep(.task-row) {
  transition: all 0.3s ease;
  border-left: 4px solid transparent;
}

/* 📋 待处理 - 深灰色调，明显区分 */
:deep(.task-row--todo) {
  border-left-color: #606266;
  background-color: rgba(96, 98, 102, 0.08);
}

:deep(.task-row--todo:hover) {
  background-color: rgba(96, 98, 102, 0.15);
  /* 移除 transform，避免影响布局对齐 */
}

/* 🔄 进行中 - 鲜明蓝色调，使用边框动画避免布局问题 */
:deep(.task-row--in-progress) {
  border-left: 4px solid #1890ff;
  background-color: rgba(24, 144, 255, 0.12);
  animation: progress-border-pulse 3s ease-in-out infinite;
}

:deep(.task-row--in-progress:hover) {
  background-color: rgba(24, 144, 255, 0.18);
  box-shadow: 0 1px 4px rgba(24, 144, 255, 0.25);
  /* 移除 transform，避免影响布局 */
}

/* ✅ 已完成 - 鲜明绿色调 */
:deep(.task-row--done) {
  border-left-color: #52c41a;
  background-color: rgba(82, 196, 26, 0.12);
}

:deep(.task-row--done:hover) {
  background-color: rgba(82, 196, 26, 0.18);
  /* 移除 transform，避免影响布局对齐 */
}

/* ❌ 已取消 - 鲜明红色调 */
:deep(.task-row--cancelled) {
  border-left-color: #ff4d4f;
  background-color: rgba(255, 77, 79, 0.12);
  opacity: 0.8;
}

:deep(.task-row--cancelled:hover) {
  background-color: rgba(255, 77, 79, 0.18);
  opacity: 0.9;
  /* 移除 transform，避免影响布局对齐 */
}

/* ⏰ 已逾期 - 鲜明橙红色调，紧急状态 */
:deep(.task-row--overdue) {
  border-left-color: #fa541c !important;
  background-color: rgba(250, 84, 28, 0.15) !important;
  animation: overdue-warning 3s ease-in-out infinite;
}

:deep(.task-row--overdue:hover) {
  background-color: rgba(250, 84, 28, 0.25) !important;
  /* 移除 transform，避免影响布局对齐 */
}

/* 🟡 即将到期 - 鲜明黄色调 */
:deep(.task-row--due-soon) {
  border-left-color: #faad14;
  background-color: rgba(250, 173, 20, 0.12);
}

:deep(.task-row--due-soon:hover) {
  background-color: rgba(250, 173, 20, 0.18);
  /* 移除 transform，避免影响布局对齐 */
}

/* 优化的动画效果 - 使用边框动画避免布局问题 */
@keyframes progress-border-pulse {
  0%, 100% {
    border-left-color: #1890ff;
    background-color: rgba(24, 144, 255, 0.12);
  }
  50% {
    border-left-color: #40a9ff;
    background-color: rgba(24, 144, 255, 0.18);
  }
}

@keyframes overdue-warning {
  0%, 100% {
    border-left-color: #faad14;
  }
  50% {
    border-left-color: #fa541c;
  }
}

/* 分类相关样式 */
.category-cell {
  display: flex;
  align-items: center;
  justify-content: center;
}

.category-tag {
  display: flex;
  align-items: center;
  gap: 4px;
  border: none;
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 12px;
  color: white;
  font-weight: 500;
}

.category-icon {
  font-size: 12px;
}

.no-category {
  color: #909399;
  font-size: 12px;
  font-style: italic;
}

.category-filter-option {
  display: flex;
  align-items: center;
  gap: 8px;
}
</style>