# 班次管理和任务调度系统文档

## 系统概述

本系统实现了完整的班次管理和任务调度功能，包括：
- 班次管理
- 用户班次分配
- 任务领取机制
- 任务完成水印系统
- 任务提醒系统
- 班次任务统计

## 核心功能

### 1. 班次管理
- **班次定义**: 支持白班、夜班、中班等多种班次类型
- **跨天班次**: 支持跨天班次配置（如夜班20:00-次日8:00）
- **任务领取时间**: 每个班次可配置特定的任务领取时间点

### 2. 用户班次分配
- **灵活分配**: 支持固定、临时、轮班等多种分配类型
- **时间范围**: 支持生效日期和失效日期配置
- **多班次支持**: 用户可以分配到多个班次

### 3. 任务领取机制
- **按班次领取**: 用户只能在所属班次的任务领取时间内领取任务
- **防重复领取**: 同一用户同一天不能重复领取同一任务
- **状态跟踪**: 完整的任务领取状态跟踪（已领取→已开始→已完成）

### 4. 任务完成水印系统
- **彩色水印**: 为每个用户分配固定的颜色标识
- **完成标记**: 已完成任务显示完成用户的彩色名称水印
- **视觉识别**: 便于快速识别任务完成情况

### 5. 任务提醒系统
- **行业最佳实践**: 基于任务优先级的多级提醒配置
- **智能提醒**: 支持截止前提醒、逾期提醒等多种类型
- **重复提醒**: 支持重复提醒机制，确保重要任务不被遗漏

## 数据库表结构

### work_shifts (班次表)
```sql
- shift_id: 班次ID (主键)
- shift_name: 班次名称
- shift_code: 班次代码
- shift_type: 班次类型 (Day/Night/Swing)
- start_time: 开始时间
- end_time: 结束时间
- task_claim_time: 任务领取时间
- is_overnight: 是否跨天
- is_active: 是否启用
```

### user_shift_assignments (用户班次分配表)
```sql
- assignment_id: 分配ID (主键)
- user_id: 用户ID
- shift_id: 班次ID
- effective_date: 生效日期
- expiry_date: 失效日期
- assignment_type: 分配类型 (Permanent/Temporary/Rotation)
- is_active: 是否启用
```

### task_claims (任务领取记录表)
```sql
- claim_id: 领取记录ID (主键)
- task_id: 任务ID
- claimed_by: 领取用户ID
- shift_id: 班次ID
- claimed_at: 领取时间
- claim_date: 领取日期
- claim_status: 领取状态 (Claimed/Started/Completed/Cancelled)
```

### task_reminders (任务提醒配置表)
```sql
- reminder_id: 提醒ID (主键)
- task_id: 任务ID
- reminder_type: 提醒类型 (BeforeStart/BeforeDeadline/Overdue)
- offset_minutes: 提醒时间偏移
- reminder_level: 提醒级别 (Info/Warning/Critical)
- is_recurring: 是否重复提醒
```

## API接口

### 班次管理接口
- `GET /api/v2/work-shifts` - 获取所有班次
- `POST /api/v2/work-shifts` - 创建班次
- `POST /api/v2/work-shifts/assignments` - 分配用户到班次
- `GET /api/v2/work-shifts/current` - 获取用户当前班次

### 任务领取接口
- `POST /api/v2/work-shifts/claim-task` - 领取任务
- `PUT /api/v2/work-shifts/claims/{claimId}/status` - 更新任务领取状态
- `GET /api/v2/work-shifts/claims/today` - 获取今日任务领取记录

### 统计接口
- `GET /api/v2/work-shifts/statistics/today` - 获取今日班次任务统计

## 前端页面

### 班次任务统计页面 (/TasksView/ShiftStatistics)
- 实时显示各班次任务完成情况
- 支持按日期查询统计数据
- 自动刷新功能（每30秒）
- 任务领取快速操作

## 后台服务

### TaskClaimGenerationService
- 自动检测班次任务领取时间
- 发送任务领取提醒通知
- 处理到期任务提醒

## 行业最佳实践配置

### 提醒配置策略
- **Critical优先级**: 1天前、8小时前、2小时前、30分钟前提醒，逾期后每小时提醒
- **High优先级**: 2天前、1天前、4小时前、1小时前提醒，逾期后每2小时提醒
- **Medium优先级**: 3天前、1天前、8小时前提醒，逾期后每8小时提醒
- **Low优先级**: 5天前、1天前提醒，逾期后每天提醒

### 班次配置建议
- **白班**: 8:00-20:00，任务领取时间8:00
- **夜班**: 20:00-次日8:00，任务领取时间20:00
- **中班**: 根据实际需要配置

## 使用流程

### 1. 系统初始化
1. 运行数据库迁移脚本创建相关表
2. 配置默认班次（白班、夜班）
3. 分配用户到相应班次

### 2. 日常使用
1. 用户在班次任务领取时间登录系统
2. 查看可领取的任务列表
3. 选择并领取任务
4. 开始执行任务
5. 完成任务并更新状态

### 3. 管理监控
1. 查看班次任务统计页面
2. 监控各班次任务完成情况
3. 处理逾期任务和异常情况

## 注意事项

1. **时区处理**: 系统使用本地时间，需要确保服务器时区设置正确
2. **权限控制**: 用户只能领取和操作自己的任务
3. **数据一致性**: 任务状态变更会自动更新相关记录
4. **性能优化**: 统计数据支持缓存，减少数据库查询压力

## 扩展功能

### 未来可扩展的功能
1. 移动端支持
2. 微信/钉钉集成
3. 更复杂的班次轮换规则
4. 任务自动分配算法
5. 绩效考核集成
6. 报表导出功能

## 故障排除

### 常见问题
1. **任务领取失败**: 检查用户班次分配和当前时间
2. **提醒不生效**: 检查后台服务运行状态
3. **统计数据不准确**: 检查数据库表结构和索引

### 日志查看
- 应用日志: 查看任务领取和状态变更日志
- 后台服务日志: 查看定时任务执行情况
- 数据库日志: 查看SQL执行情况

## 技术架构

- **后端**: ASP.NET Core + Entity Framework Core
- **数据库**: MySQL
- **前端**: Bootstrap + jQuery + 原生JavaScript
- **架构模式**: Clean Architecture + CQRS
- **缓存**: 内存缓存
- **日志**: Serilog

## 版本信息

- **当前版本**: 1.0.0
- **发布日期**: 2025-06-19
- **兼容性**: .NET 6.0+, MySQL 8.0+
