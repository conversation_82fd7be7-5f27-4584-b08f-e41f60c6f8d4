// File: Core/Abstractions/IQuickMemoRepository.cs
// Description: Interface for QuickMemo data access operations.
#nullable enable
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Threading; // For CancellationToken
using ItAssetsSystem.Domain.Entities.Notes; // For QuickMemo

namespace ItAssetsSystem.Core.Abstractions
{
    public interface IQuickMemoRepository
    {
        Task<IEnumerable<QuickMemo>> GetByUserIdAsync(int userId, CancellationToken cancellationToken = default);
        Task<QuickMemo?> GetByIdAsync(string memoId, CancellationToken cancellationToken = default);
        Task AddAsync(QuickMemo memo, CancellationToken cancellationToken = default);
        void Update(QuickMemo memo); // Typically synchronous for marking entity as modified
        void Delete(QuickMemo memo); // Typically synchronous for marking entity as deleted
        Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);
        Task<bool> UserOwnsMemoAsync(string memoId, int userId, CancellationToken cancellationToken = default);
    }
} 