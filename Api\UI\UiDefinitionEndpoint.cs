// IT资产管理系统 - UI定义API端点
// 文件路径: /Api/UI/UiDefinitionEndpoint.cs
// 功能: 提供UI定义访问的API端点

using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using ItAssetsSystem.Core.UI;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace ItAssetsSystem.Api.UI
{
    /// <summary>
    /// UI定义API端点
    /// </summary>
    [ApiController]
    [Route("api/ui")]
    public class UiDefinitionEndpoint
    {
        private readonly IUiDefinitionService _uiDefinitionService;
        private readonly ILogger<UiDefinitionEndpoint> _logger;

        /// <summary>
        /// 构造函数
        /// </summary>
        public UiDefinitionEndpoint(
            IUiDefinitionService uiDefinitionService,
            ILogger<UiDefinitionEndpoint> logger)
        {
            _uiDefinitionService = uiDefinitionService;
            _logger = logger;
        }

        /// <summary>
        /// 获取UI定义列表
        /// </summary>
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> GetAllAsync()
        {
            _logger.LogInformation("获取所有UI定义");
            
            try
            {
                var definitions = await _uiDefinitionService.GetAllUiDefinitionsAsync();
                return new OkObjectResult(definitions);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取所有UI定义失败");
                return new StatusCodeResult(StatusCodes.Status500InternalServerError);
            }
        }

        /// <summary>
        /// 获取指定的UI定义
        /// </summary>
        [HttpGet("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetByIdAsync(string id)
        {
            _logger.LogInformation("获取UI定义: {Id}", id);
            
            try
            {
                var definition = await _uiDefinitionService.GetUiDefinitionAsync(id);
                
                if (definition == null)
                {
                    return new NotFoundResult();
                }
                
                return new OkObjectResult(definition);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取UI定义失败: {Id}", id);
                return new StatusCodeResult(StatusCodes.Status500InternalServerError);
            }
        }

        /// <summary>
        /// 保存UI定义
        /// </summary>
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<IActionResult> SaveAsync([FromBody] UiDefinition definition)
        {
            if (definition == null)
            {
                return new BadRequestResult();
            }
            
            _logger.LogInformation("保存UI定义: {Id}", definition.Id ?? "(新)");
            
            try
            {
                // 禁止客户端修改系统内置定义
                if (!string.IsNullOrEmpty(definition.Id) && definition.Id.StartsWith("system_"))
                {
                    var existingDefinition = await _uiDefinitionService.GetUiDefinitionAsync(definition.Id);
                    if (existingDefinition != null && existingDefinition.IsSystem)
                    {
                        _logger.LogWarning("尝试修改系统内置UI定义: {Id}", definition.Id);
                        return new BadRequestObjectResult(new { error = "不能修改系统内置UI定义" });
                    }
                }
                
                // 保存定义
                bool success = await _uiDefinitionService.SaveUiDefinitionAsync(definition);
                
                if (success)
                {
                    return new CreatedAtActionResult(
                        nameof(GetByIdAsync),
                        null,
                        new { id = definition.Id },
                        definition);
                }
                else
                {
                    return new StatusCodeResult(StatusCodes.Status500InternalServerError);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存UI定义失败");
                return new StatusCodeResult(StatusCodes.Status500InternalServerError);
            }
        }

        /// <summary>
        /// 删除UI定义
        /// </summary>
        [HttpDelete("{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> DeleteAsync(string id)
        {
            if (string.IsNullOrEmpty(id))
            {
                return new BadRequestResult();
            }
            
            // 不允许删除系统内置定义
            if (id.StartsWith("system_"))
            {
                _logger.LogWarning("尝试删除系统内置UI定义: {Id}", id);
                return new BadRequestObjectResult(new { error = "不能删除系统内置UI定义" });
            }
            
            _logger.LogInformation("删除UI定义: {Id}", id);
            
            try
            {
                bool success = await _uiDefinitionService.DeleteUiDefinitionAsync(id);
                
                if (success)
                {
                    return new NoContentResult();
                }
                else
                {
                    return new NotFoundResult();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除UI定义失败: {Id}", id);
                return new StatusCodeResult(StatusCodes.Status500InternalServerError);
            }
        }

        /// <summary>
        /// 重置所有系统内置UI定义
        /// </summary>
        [HttpPost("reset-system")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        public async Task<IActionResult> ResetSystemDefinitionsAsync()
        {
            _logger.LogInformation("重置系统内置UI定义");
            
            try
            {
                await _uiDefinitionService.LoadSystemUiDefinitionsAsync();
                return new NoContentResult();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "重置系统内置UI定义失败");
                return new StatusCodeResult(StatusCodes.Status500InternalServerError);
            }
        }
    }
} 