// File: Application/Features/SpareParts/Dtos/SparePartTypeDto.cs
// Description: 备品备件类型DTO

using System;
using System.Collections.Generic;

namespace ItAssetsSystem.Application.Features.SpareParts.Dtos
{
    /// <summary>
    /// 备品备件类型DTO
    /// </summary>
    public class SparePartTypeDto
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        public long Id { get; set; }
        
        /// <summary>
        /// 类型名称
        /// </summary>
        public string Name { get; set; }
        
        /// <summary>
        /// 类型编码
        /// </summary>
        public string Code { get; set; }
        
        /// <summary>
        /// 父类型ID
        /// </summary>
        public long? ParentId { get; set; }
        
        /// <summary>
        /// 父类型名称
        /// </summary>
        public string ParentName { get; set; }
        
        /// <summary>
        /// 类型路径，如 "1,5,15"
        /// </summary>
        public string Path { get; set; }
        
        /// <summary>
        /// 层级
        /// </summary>
        public int Level { get; set; }
        
        /// <summary>
        /// 描述
        /// </summary>
        public string Description { get; set; }
        
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }
        
        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; }
        
        /// <summary>
        /// 子类型列表
        /// </summary>
        public List<SparePartTypeDto> Children { get; set; } = new List<SparePartTypeDto>();
    }
} 