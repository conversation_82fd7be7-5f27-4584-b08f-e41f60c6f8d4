-- 创建任务分类表和相关字段
-- 执行时间: 2025-01-03

-- 1. 创建任务分类表
CREATE TABLE IF NOT EXISTS `task_categories` (
    `category_id` INT AUTO_INCREMENT PRIMARY KEY COMMENT '分类主键ID',
    `name` VARCHAR(100) NOT NULL COMMENT '分类名称',
    `description` VARCHAR(500) NULL COMMENT '分类描述',
    `color` VARCHAR(7) NULL COMMENT '分类颜色(十六进制)',
    `icon` VARCHAR(50) NULL COMMENT '分类图标',
    `sort_order` INT NOT NULL DEFAULT 0 COMMENT '排序顺序',
    `is_active` BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否启用',
    `created_by` INT NOT NULL COMMENT '创建人ID',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    UNIQUE KEY `uk_task_category_name` (`name`),
    KEY `idx_task_category_active` (`is_active`),
    KEY `idx_task_category_sort` (`sort_order`),
    KEY `idx_task_category_created_by` (`created_by`),
    
    CONSTRAINT `fk_task_category_created_by` 
        FOREIGN KEY (`created_by`) 
        REFERENCES `users` (`Id`) 
        ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='任务分类表';

-- 2. 为tasks表添加CategoryId字段
ALTER TABLE `tasks`
ADD COLUMN `CategoryId` INT NULL COMMENT '任务分类ID' AFTER `TaskType`;

-- 3. 添加外键约束
ALTER TABLE `tasks`
ADD CONSTRAINT `fk_task_category`
    FOREIGN KEY (`CategoryId`)
    REFERENCES `task_categories` (`category_id`)
    ON DELETE SET NULL;

-- 4. 添加索引
CREATE INDEX `idx_tasks_category` ON `tasks` (`CategoryId`);

-- 5. 插入默认分类数据
INSERT INTO `task_categories` (`name`, `description`, `color`, `icon`, `sort_order`, `is_active`, `created_by`) VALUES
('Daily Maintenance', 'Equipment daily maintenance and care tasks', '#409EFF', 'Tools', 1, TRUE, 1),
('Fault Handling', 'Equipment fault troubleshooting and repair tasks', '#F56C6C', 'Warning', 2, TRUE, 1),
('Project Implementation', 'Project-related implementation tasks', '#67C23A', 'Folder', 3, TRUE, 1),
('System Upgrade', 'System and software upgrade tasks', '#E6A23C', 'Upload', 4, TRUE, 1),
('Training', 'Skills training and learning tasks', '#909399', 'Reading', 5, TRUE, 1),
('Documentation', 'Document writing and organization tasks', '#606266', 'Document', 6, TRUE, 1),
('Security Check', 'Security-related inspection tasks', '#F56C6C', 'Lock', 7, TRUE, 1),
('Others', 'Other types of tasks', '#C0C4CC', 'More', 99, TRUE, 1);

-- 6. 验证表创建结果
SELECT 
    'task_categories表创建验证' AS 检查项目,
    COUNT(*) AS 记录数量,
    '默认分类已插入' AS 状态
FROM task_categories;

-- 7. 验证tasks表字段添加
SELECT
    'tasks表CategoryId字段验证' AS 检查项目,
    COLUMN_NAME AS 字段名,
    DATA_TYPE AS 数据类型,
    IS_NULLABLE AS 可为空,
    COLUMN_DEFAULT AS 默认值
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_SCHEMA = DATABASE()
  AND TABLE_NAME = 'tasks'
  AND COLUMN_NAME = 'CategoryId';

-- 8. 显示创建的分类
SELECT 
    category_id AS 分类ID,
    name AS 分类名称,
    description AS 描述,
    color AS 颜色,
    icon AS 图标,
    sort_order AS 排序,
    is_active AS 是否启用
FROM task_categories 
ORDER BY sort_order;

SELECT '✅ 任务分类表和字段创建完成！' AS 完成状态;
