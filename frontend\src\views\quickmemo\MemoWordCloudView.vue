// File: frontend/src/views/quickmemo/MemoWordCloudView.vue
// Description: 独立页面，用于展示随手记词云。

<template>
  <div class="memo-wordcloud-container">
    <div class="wordcloud-header">
      <h2>随手记词云</h2>
      <div class="header-actions">
        <el-button type="primary" @click="handleAddMemo">
          <el-icon><Plus /></el-icon>
          新建随手记
        </el-button>
      </div>
    </div>
    
    <div class="wordcloud-content">
      <memo-word-cloud />
    </div>
    
    <!-- 新建/编辑随手记抽屉 -->
    <memo-form-drawer />
  </div>
</template>

<script setup>
import { onMounted } from 'vue';
import { Plus } from '@element-plus/icons-vue';
import MemoWordCloud from '@/components/QuickMemo/MemoWordCloud.vue';
import MemoFormDrawer from '@/components/QuickMemo/MemoFormDrawer.vue';
import { useQuickMemoStore } from '@/stores/modules/quickMemo';

const quickMemoStore = useQuickMemoStore();

const handleAddMemo = () => {
  quickMemoStore.openMemoDrawer('create');
};

onMounted(() => {
  // 预加载随手记数据
  quickMemoStore.fetchQuickMemos();
});
</script>

<style scoped>
.memo-wordcloud-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.wordcloud-header {
  padding: 16px 24px;
  border-bottom: 1px solid var(--el-border-color-light);
  background-color: var(--el-bg-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.wordcloud-header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.header-actions {
  display: flex;
  gap: 12px;
}

.wordcloud-content {
  flex: 1;
  overflow: hidden;
  position: relative;
}
</style> 