-- =====================================================
-- 任务完成人信息和游戏化统计综合修复方案
-- 解决任务领取、完成人显示、积分统计、排行榜等问题
-- =====================================================

-- 1. 检查并修复任务表结构
SELECT '=== 1. 检查任务表结构 ===' AS Info;

-- 检查任务表是否有完成相关字段
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'tasks'
  AND COLUMN_NAME IN ('CompletedByUserId', 'CompletedAt', 'CompletionWatermarkColor', 'ClaimedByUserId', 'ClaimedAt')
ORDER BY ORDINAL_POSITION;

-- 2. 检查任务领取表结构
SELECT '=== 2. 检查任务领取表结构 ===' AS Info;
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'task_claims'
ORDER BY ORDINAL_POSITION;

-- 3. 检查游戏化相关表
SELECT '=== 3. 检查游戏化表结构 ===' AS Info;
SHOW TABLES LIKE '%gamification%';
SHOW TABLES LIKE '%behavior%';
SHOW TABLES LIKE '%achievement%';
SHOW TABLES LIKE '%leaderboard%';

-- 4. 检查任务领取状态统计
SELECT '=== 4. 任务领取状态统计 ===' AS Info;
SELECT 
    '总任务数' AS 统计类型,
    COUNT(*) AS 数量
FROM tasks
WHERE IsDeleted = 0

UNION ALL

SELECT 
    '已完成任务数' AS 统计类型,
    COUNT(*) AS 数量
FROM tasks 
WHERE Status = 'Done' AND IsDeleted = 0

UNION ALL

SELECT 
    '有完成人的任务数' AS 统计类型,
    COUNT(*) AS 数量
FROM tasks 
WHERE Status = 'Done' AND CompletedByUserId IS NOT NULL AND IsDeleted = 0

UNION ALL

SELECT 
    '有领取记录的任务数' AS 统计类型,
    COUNT(*) AS 数量
FROM tasks t
WHERE EXISTS (
    SELECT 1 FROM task_claims tc 
    WHERE tc.TaskId = t.TaskId AND tc.IsDeleted = 0
) AND t.IsDeleted = 0;

-- 5. 检查用户任务统计（按日、按周）
SELECT '=== 5. 用户任务统计（最近7天）===' AS Info;
SELECT 
    u.id AS UserId,
    u.name AS UserName,
    DATE(t.CompletedAt) AS CompletionDate,
    COUNT(CASE WHEN t.Status = 'Done' THEN 1 END) AS CompletedTasks,
    COUNT(CASE WHEN tc.ClaimId IS NOT NULL THEN 1 END) AS ClaimedTasks,
    COUNT(CASE WHEN t.CreatorUserId = u.id THEN 1 END) AS CreatedTasks
FROM users u
LEFT JOIN tasks t ON (t.CompletedByUserId = u.id OR t.CreatorUserId = u.id OR t.AssigneeUserId = u.id)
LEFT JOIN task_claims tc ON tc.TaskId = t.TaskId AND tc.UserId = u.id AND tc.IsDeleted = 0
WHERE t.IsDeleted = 0 
  AND (t.CompletedAt >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) OR t.CreationTimestamp >= DATE_SUB(CURDATE(), INTERVAL 7 DAY))
GROUP BY u.id, u.name, DATE(t.CompletedAt)
ORDER BY u.id, CompletionDate DESC
LIMIT 20;

-- 6. 检查故障、返厂、采购统计
SELECT '=== 6. 其他模块统计（最近7天）===' AS Info;

-- 故障统计
SELECT 
    '故障登记' AS 模块,
    COUNT(*) AS 总数,
    COUNT(CASE WHEN DATE(CreationTimestamp) = CURDATE() THEN 1 END) AS 今日数量,
    COUNT(CASE WHEN DATE(CreationTimestamp) >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) THEN 1 END) AS 本周数量
FROM faults
WHERE IsDeleted = 0

UNION ALL

-- 返厂统计
SELECT 
    '返厂申请' AS 模块,
    COUNT(*) AS 总数,
    COUNT(CASE WHEN DATE(CreationTimestamp) = CURDATE() THEN 1 END) AS 今日数量,
    COUNT(CASE WHEN DATE(CreationTimestamp) >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) THEN 1 END) AS 本周数量
FROM return_to_factory
WHERE IsDeleted = 0

UNION ALL

-- 采购统计
SELECT 
    '采购申请' AS 模块,
    COUNT(*) AS 总数,
    COUNT(CASE WHEN DATE(CreationTimestamp) = CURDATE() THEN 1 END) AS 今日数量,
    COUNT(CASE WHEN DATE(CreationTimestamp) >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) THEN 1 END) AS 本周数量
FROM purchases
WHERE IsDeleted = 0;

-- 7. 检查游戏化积分统计
SELECT '=== 7. 游戏化积分统计 ===' AS Info;
SELECT 
    u.id AS UserId,
    u.name AS UserName,
    COALESCE(us.PointsBalance, 0) AS 当前积分,
    COALESCE(us.XpBalance, 0) AS 当前经验,
    COALESCE(us.CoinsBalance, 0) AS 当前金币,
    COALESCE(us.DiamondsBalance, 0) AS 当前钻石,
    COALESCE(us.TasksCompletedCount, 0) AS 完成任务数,
    COALESCE(us.TasksClaimedCount, 0) AS 领取任务数,
    COALESCE(us.TasksCreatedCount, 0) AS 创建任务数
FROM users u
LEFT JOIN user_stats us ON us.UserId = u.id
WHERE u.IsDeleted = 0
ORDER BY us.PointsBalance DESC
LIMIT 10;

-- 8. 检查排行榜数据
SELECT '=== 8. 排行榜数据检查 ===' AS Info;
SELECT 
    lb.UserId,
    u.name AS UserName,
    lb.LeaderboardType,
    lb.Period,
    lb.RankScore,
    lb.Rank,
    lb.LastUpdated
FROM leaderboards lb
JOIN users u ON u.id = lb.UserId
WHERE lb.Period = 'current'
ORDER BY lb.LeaderboardType, lb.Rank
LIMIT 15;

-- 9. 修复建议和问题诊断
SELECT '=== 9. 问题诊断和修复建议 ===' AS Info;

-- 检查缺失的完成人信息
SELECT 
    '缺失完成人的已完成任务' AS 问题类型,
    COUNT(*) AS 问题数量,
    '需要修复CompletedByUserId字段' AS 修复建议
FROM tasks 
WHERE Status = 'Done' AND CompletedByUserId IS NULL AND IsDeleted = 0

UNION ALL

-- 检查缺失的完成时间
SELECT 
    '缺失完成时间的已完成任务' AS 问题类型,
    COUNT(*) AS 问题数量,
    '需要修复CompletedAt字段' AS 修复建议
FROM tasks 
WHERE Status = 'Done' AND CompletedAt IS NULL AND IsDeleted = 0

UNION ALL

-- 检查游戏化统计数据
SELECT 
    '缺失游戏化统计的用户' AS 问题类型,
    COUNT(*) AS 问题数量,
    '需要初始化user_stats表' AS 修复建议
FROM users u
LEFT JOIN user_stats us ON us.UserId = u.id
WHERE u.IsDeleted = 0 AND us.UserId IS NULL

UNION ALL

-- 检查排行榜数据
SELECT 
    '缺失排行榜数据' AS 问题类型,
    CASE WHEN COUNT(*) = 0 THEN 1 ELSE 0 END AS 问题数量,
    '需要生成排行榜数据' AS 修复建议
FROM leaderboards
WHERE Period = 'current';

-- 10. 生成修复脚本建议
SELECT '=== 10. 修复脚本执行建议 ===' AS Info;
SELECT 
    '1. 执行TaskCompletionFieldsFix.sql修复数据库字段' AS 步骤1,
    '2. 运行游戏化数据初始化脚本' AS 步骤2,
    '3. 更新任务完成人信息' AS 步骤3,
    '4. 重新计算用户统计数据' AS 步骤4,
    '5. 生成排行榜数据' AS 步骤5,
    '6. 验证前端API返回数据' AS 步骤6;
