<template>
  <div class="task-comments">
    <div class="comments-header">
      <h4>评论 ({{ comments.length }})</h4>
    </div>
    
    <!-- 评论列表 -->
    <div v-if="comments.length" class="comments-list">
      <div v-for="comment in comments" :key="comment.commentId" class="comment-item">
        <div class="comment-user">
          <el-avatar 
            :src="comment.userAvatarUrl" 
            :size="32"
            class="user-avatar"
          >
            {{ comment.userName ? comment.userName.charAt(0).toUpperCase() : '?' }}
          </el-avatar>
          <div class="comment-meta">
            <div class="comment-header">
              <span class="user-name">{{ comment.userName || '未知用户' }}</span>
              <span class="comment-time">{{ formatTime(comment.creationTimestamp) }}</span>
            </div>
          </div>
        </div>
        <div class="comment-content">{{ comment.content }}</div>
        <div v-if="comment.isEdited" class="edit-indicator">已编辑</div>
      </div>
    </div>
    <div v-else class="empty-comments">
      <div class="empty-icon">💬</div>
      <div class="empty-text">暂无评论</div>
    </div>
    
    <!-- 添加评论 -->
    <div class="add-comment">
      <el-input
        v-model="newComment"
        type="textarea"
        :rows="3"
        placeholder="写下你的评论..."
        maxlength="2000"
        show-word-limit
        @keyup.ctrl.enter="submitComment"
      />
      <div class="comment-actions">
        <el-button type="primary" size="small" @click="submitComment" :loading="submitting">
          发表评论
        </el-button>
        <div class="comment-tip">Ctrl + Enter 快速发送</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { taskApi } from '@/api/task'

const props = defineProps({
  taskId: {
    type: [String, Number],
    required: true
  },
  comments: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['add-comment', 'load-comments'])

const newComment = ref('')
const submitting = ref(false)

// 使用计算属性直接引用props中的comments，避免重复状态管理
const comments = computed(() => props.comments || [])

// 格式化时间
const formatTime = (timestamp) => {
  if (!timestamp) return ''
  const date = new Date(timestamp)
  const now = new Date()
  const diff = now - date
  
  // 1分钟内
  if (diff < 60000) {
    return '刚刚'
  }
  // 1小时内
  if (diff < 3600000) {
    return `${Math.floor(diff / 60000)}分钟前`
  }
  // 24小时内
  if (diff < 86400000) {
    return `${Math.floor(diff / 3600000)}小时前`
  }
  // 超过24小时
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

async function loadComments() {
  if (!props.taskId) return
  try {
    emit('load-comments')
  } catch (error) {
    console.error('加载评论失败:', error)
  }
}

async function submitComment() {
  if (!newComment.value.trim()) {
    ElMessage.warning('请输入评论内容')
    return
  }

  // 防重复提交检查
  if (submitting.value) {
    ElMessage.warning('正在提交中，请稍候...')
    return
  }

  submitting.value = true
  const commentContent = newComment.value.trim()

  try {
    await taskApi.addComment(props.taskId, { content: commentContent })
    emit('add-comment', { content: commentContent })
    newComment.value = ''
    ElMessage.success('评论发表成功')
  } catch (error) {
    console.error('提交评论失败:', error)
    ElMessage.error('评论发表失败: ' + (error.message || '未知错误'))
  } finally {
    // 延迟重置提交状态，防止快速重复点击
    setTimeout(() => {
      submitting.value = false
    }, 1000)
  }
}

// 当taskId变化时通知父组件加载评论
watch(() => props.taskId, loadComments, { immediate: true })
</script>

<style scoped>
.task-comments {
  padding: 16px 0;
}

.comments-header {
  margin-bottom: 16px;
}

.comments-header h4 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 500;
}

.comments-list {
  max-height: 400px;
  overflow-y: auto;
  margin-bottom: 16px;
}

.comment-item {
  padding: 12px 0;
  border-bottom: 1px solid #f5f7fa;
}

.comment-item:last-child {
  border-bottom: none;
}

.comment-user {
  display: flex;
  align-items: flex-start;
  margin-bottom: 8px;
}

.user-avatar {
  margin-right: 12px;
  flex-shrink: 0;
}

.comment-meta {
  flex: 1;
}

.comment-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.user-name {
  font-weight: 500;
  color: #303133;
  font-size: 14px;
}

.comment-time {
  font-size: 12px;
  color: #909399;
}

.comment-content {
  margin-left: 44px;
  line-height: 1.5;
  color: #606266;
  white-space: pre-wrap;
  word-break: break-word;
}

.edit-indicator {
  margin-left: 44px;
  font-size: 12px;
  color: #c0c4cc;
  margin-top: 4px;
}

.empty-comments {
  text-align: center;
  padding: 40px 0;
  color: #c0c4cc;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 12px;
}

.empty-text {
  font-size: 14px;
}

.add-comment {
  margin-top: 16px;
}

.comment-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 12px;
}

.comment-tip {
  font-size: 12px;
  color: #909399;
}
</style> 