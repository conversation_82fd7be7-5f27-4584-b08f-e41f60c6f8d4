<template>
  <div v-loading="isLoading" element-loading-text="加载中...">
    <el-form
      v-if="!isLoading" 
      ref="locationFormRef"
      :model="locationForm"
      :rules="locationRules"
      label-width="100px"
    >
      <el-form-item label="位置名称" prop="name">
        <el-input v-model="locationForm.name" placeholder="请输入位置名称" />
      </el-form-item>
      <el-form-item label="位置编码" prop="code">
        <el-input v-model="locationForm.code" placeholder="请输入位置编码" />
        <div class="form-tip">编码只能包含大写字母、数字和下划线</div>
      </el-form-item>
      <el-form-item label="上级位置" prop="parentId">
        <el-cascader
          v-model="locationForm.parentId"
          :options="locationTree"
          :props="{
            checkStrictly: true,
            label: 'name',
            value: 'id',
            emitPath: false
          }"
          placeholder="请选择上级位置"
          clearable
          style="width: 100%;"
        />
      </el-form-item>
      <el-form-item label="详细地址" prop="address">
        <el-input v-model="locationForm.address" placeholder="请输入详细地址" />
      </el-form-item>
      <el-form-item label="位置类型" prop="type">
        <el-select v-model="locationForm.type" placeholder="请选择位置类型" style="width: 100%;">
          <el-option
            v-for="type in LOCATION_TYPES"
            :key="type.value"
            :label="type.label"
            :value="type.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="默认部门" prop="defaultDepartmentId">
        <el-select
          v-model="locationForm.defaultDepartmentId"
          placeholder="请选择默认部门"
          clearable
          filterable
          style="width: 100%;"
        >
          <el-option
            v-for="dept in departmentList"
            :key="dept.id"
            :label="dept.name"
            :value="dept.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="位置负责人" prop="defaultResponsiblePersonId">
        <el-select
          v-model="locationForm.defaultResponsiblePersonId"
          placeholder="请选择位置负责人"
          clearable
          filterable
          style="width: 100%;"
        >
          <el-option
            v-for="user in userList"
            :key="user.id"
            :label="user.name + (user.username ? ` (${user.username})` : '')"
            :value="user.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="使用人" prop="usageUserIds">
        <el-select
          v-model="locationForm.usageUserIds"
          multiple
          filterable
          placeholder="请选择使用人（可多选）"
          clearable
          style="width: 100%;"
        >
          <el-option
            v-for="user in userList"
            :key="user.id"
            :label="user.name + (user.username ? ` (${user.username})` : '')"
            :value="user.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="locationForm.remark" type="textarea" :rows="3" placeholder="请输入备注信息" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="submitForm(locationFormRef)" :loading="isSubmitting">
          {{ locationForm.id ? '保存修改' : '立即创建' }}
        </el-button>
        <el-button @click="resetForm(locationFormRef)">重置</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  getLocationTree,
  getLocationDetail,
  createLocation,
  updateLocation,
  getLocationUsers,
  updateLocationUsers
} from '@/api/location'
import { getDepartmentList } from '@/api/department'
import { getUserList } from '@/api/user'

// 位置类型常量
const LOCATION_TYPES = ref([
  { value: 0, label: '工厂' },
  { value: 1, label: '产线/工序' },
  { value: 2, label: '工位' },
  { value: 4, label: '设备位置' }
])

const route = useRoute()
const router = useRouter()
const locationFormRef = ref()
const isSubmitting = ref(false)
const isLoading = ref(true)

const locationForm = ref({
  id: null,
  name: '',
  code: '',
  type: undefined,
  parentId: null,
  address: '',
  remark: '',
  defaultDepartmentId: null,
  defaultResponsiblePersonId: null,
  usageUserIds: []
})

const locationRules = reactive({
  name: [
    { required: true, message: '请输入位置名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入位置编码', trigger: 'blur' },
    { pattern: /^[A-Z0-9_]+$/, message: '编码只能包含大写字母、数字和下划线', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择位置类型', trigger: 'change' }
  ],
  defaultDepartmentId: [
    { required: true, message: '请选择默认部门', trigger: 'change' }
  ],
  defaultResponsiblePersonId: [
    { required: true, message: '请选择位置负责人', trigger: 'change' }
  ]
})

const departmentList = ref([])
const userList = ref([])
const locationTree = ref([])

const fetchDepartmentList = async () => {
  console.log("[fetchDepartmentList] 开始获取部门列表...");
  try {
    const res = await getDepartmentList({ pageNum: 1, pageSize: 9999 });
    console.log("[fetchDepartmentList] API 响应:", res);
    if (res.success) {
      departmentList.value = res.data?.list || res.data || [];
      console.log("[fetchDepartmentList] 部门列表加载成功，数量:", departmentList.value.length);
    } else {
      ElMessage.error(res.message || '获取部门列表失败');
      console.error("[fetchDepartmentList] 获取失败:", res.message);
    }
  } catch (error) {
    console.error('[fetchDepartmentList] 捕获到错误:', error);
    ElMessage.error('获取部门列表时发生错误');
  }
}

const fetchUserList = async () => {
  console.log("[fetchUserList] 开始获取用户列表...");
  try {
    const res = await getUserList({ pageNum: 1, pageSize: 9999 });
    console.log("[fetchUserList] API 响应:", res);
    if (res.success) {
      userList.value = res.data?.list || res.data || [];
      console.log("[fetchUserList] 用户列表加载成功，数量:", userList.value.length);
    } else {
      ElMessage.error(res.message || '获取用户列表失败');
      console.error("[fetchUserList] 获取失败:", res.message);
    }
  } catch (error) {
    console.error('[fetchUserList] 捕获到错误:', error);
    ElMessage.error('获取用户列表时发生错误');
  }
}

const fetchLocationTree = async () => {
  console.log('[fetchLocationTree] 开始获取位置树...');
  try {
    const res = await getLocationTree()
    console.log('[fetchLocationTree] API 响应:', res)
    if (Array.isArray(res.data)) {
       locationTree.value = res.data;
    } else if(res.success && Array.isArray(res.data?.list)) {
        locationTree.value = res.data.list;
    }
     else {
       locationTree.value = [];
       console.warn("[fetchLocationTree] 获取位置树数据格式不符合预期:", res);
    }
    console.log("[fetchLocationTree] 位置树加载完成，节点数量:", locationTree.value.length);
  } catch (error) {
    console.error('[fetchLocationTree] 捕获到错误:', error)
    ElMessage.error('获取位置树失败')
  }
}

const loadLocationDetail = async (id) => {
  console.log(`[loadLocationDetail] 开始加载位置详情，ID: ${id}`);
  if (!id || isNaN(id)) {
      console.error("[loadLocationDetail] 无效的 ID:", id);
      ElMessage.error("无效的位置 ID，无法加载详情");
      return;
  }
  try {
    // 1. 获取位置核心信息
    console.log(`[loadLocationDetail] 正在调用 getLocationDetail(${id})...`);
    const resLoc = await getLocationDetail(id);
    console.log(`[loadLocationDetail] getLocationDetail(${id}) 响应:`, resLoc);
    if (resLoc.success && resLoc.data) {
      Object.assign(locationForm.value, {
         id: resLoc.data.id,
         name: resLoc.data.name,
         code: resLoc.data.code,
         type: resLoc.data.type,
         parentId: resLoc.data.parentId,
         address: resLoc.data.address || '',
         remark: resLoc.data.description || resLoc.data.remark || '',
         defaultDepartmentId: resLoc.data.defaultDepartmentId,
         defaultResponsiblePersonId: resLoc.data.defaultResponsiblePersonId || resLoc.data.managerId,
         usageUserIds: [],
      });
      console.log("[loadLocationDetail] 位置核心信息已填充到表单:", locationForm.value);
    } else {
      ElMessage.error(resLoc.message || '获取位置详情失败');
      console.error(`[loadLocationDetail] getLocationDetail(${id}) 失败:`, resLoc.message);
      return; // 获取失败则不继续
    }

    // 2. 获取关联的使用人
    try {
        console.log(`[loadLocationDetail] 正在调用 getLocationUsers(${id})...`);
        const resUsers = await getLocationUsers(id);
        console.log(`[loadLocationDetail] getLocationUsers(${id}) 响应:`, resUsers);
        if (resUsers.success && Array.isArray(resUsers.data)) {
           locationForm.value.usageUserIds = resUsers.data || [];
           console.log("[loadLocationDetail] 关联用户已填充:", locationForm.value.usageUserIds);
        } else {
           console.warn(`[loadLocationDetail] 获取位置关联用户失败或数据格式不正确:`, resUsers.message || resUsers);
           locationForm.value.usageUserIds = [];
        }
    } catch (userError) {
        console.error(`[loadLocationDetail] 调用 getLocationUsers(${id}) 捕获到错误:`, userError);
        ElMessage.warning('获取关联用户失败，请检查API');
        locationForm.value.usageUserIds = [];
    }

  } catch (error) {
    console.error(`[loadLocationDetail] 加载位置数据（ID: ${id}）时捕获到顶层错误:`, error);
    ElMessage.error('加载位置数据时发生错误');
  }
};

const getLocationTypeName = (type) => {
  const typeObj = LOCATION_TYPES.value.find(t => t.value === type)
  return typeObj ? typeObj.label : '未知类型'
}

const submitForm = async (formEl) => {
  console.log("[submitForm] 开始提交表单...");
  if (!formEl) {
      console.error("[submitForm] formEl 无效");
      return;
  }
  isSubmitting.value = true;

  await formEl.validate(async (valid) => {
    if (valid) {
      console.log("[submitForm] 表单验证通过");
      let locationId = locationForm.value.id;
      const isEdit = !!locationId;
      console.log(`[submitForm] 模式: ${isEdit ? '编辑' : '创建'}, ID: ${locationId}`);
      const mainApiCall = isEdit ? updateLocation : createLocation;
      let mainApiResponse = null;

      try {
        const payload = { ...locationForm.value };
        delete payload.usageUserIds;
        if (payload.parentId === '' || payload.parentId === undefined) {
          payload.parentId = null;
        }
        payload.description = payload.remark;
        console.log("[submitForm] 准备提交主数据:", payload);

        if (isEdit) {
          mainApiResponse = await mainApiCall(locationId, payload);
        } else {
          mainApiResponse = await mainApiCall(payload);
        }
        console.log("[submitForm] 主数据 API 响应:", mainApiResponse);

        if (!mainApiResponse.success) {
          throw new Error(mainApiResponse.message || (isEdit ? '修改位置失败' : '添加位置失败'));
        }

        ElMessage.success(isEdit ? '位置信息保存成功' : '位置创建成功');

        if (!isEdit && mainApiResponse.data?.id) {
          locationId = mainApiResponse.data.id;
          locationForm.value.id = locationId; // 更新表单ID
          console.log("[submitForm] 创建成功，获取到新 ID:", locationId);
        } else if (!isEdit && !mainApiResponse.data?.id) {
           console.error("[submitForm] 创建成功但未返回ID，无法更新使用人。");
           ElMessage.warning('位置已创建，但更新使用人关联时可能出现问题（缺少ID）');
        }

        if (locationId) {
          try {
             const userIdsToUpdate = Array.isArray(locationForm.value.usageUserIds)
                                       ? locationForm.value.usageUserIds
                                       : [];
             console.log(`[submitForm] 准备更新使用人 (ID: ${locationId}):`, userIdsToUpdate);
             const userUpdateRes = await updateLocationUsers(locationId, userIdsToUpdate);
             console.log(`[submitForm] 更新使用人 API 响应:`, userUpdateRes);
             if (!userUpdateRes.success) {
               console.error('[submitForm] 更新使用人失败:', userUpdateRes.message);
               ElMessage.warning('位置信息已保存，但更新使用人失败: ' + (userUpdateRes.message || '未知错误'));
             } else {
               console.log("[submitForm] 使用人更新成功");
             }
          } catch (userError) {
             console.error('[submitForm] 调用 updateLocationUsers 捕获到错误:', userError);
             ElMessage.warning('位置信息已保存，但更新使用人时发生错误');
          }
        } else {
           console.warn("[submitForm] 无法获取有效的 Location ID，跳过使用人更新");
        }

        fetchLocationTree();

        if (!isEdit) {
          console.log("[submitForm] 创建模式，重置表单");
          resetForm(formEl);
        } else {
          console.log("[submitForm] 编辑模式完成");
        }

      } catch (error) {
        console.error('[submitForm] 处理时捕获到错误:', error);
        ElMessage.error(error.message || '操作失败');
      } finally {
         isSubmitting.value = false;
         console.log("[submitForm] 提交处理结束");
      }
    } else {
      console.log('[submitForm] 表单验证失败');
      isSubmitting.value = false;
      return false;
    }
  });
};

const resetForm = (formEl) => {
  console.log("[resetForm] 开始重置表单...");
  if (!formEl) return;
  formEl.resetFields();
  locationForm.value.id = null;
  locationForm.value.usageUserIds = [];
  console.log("[resetForm] 表单已重置");
};

onMounted(async () => {
  console.log("[onMounted] 组件开始挂载...");
  isLoading.value = true; // 开始加载
  const fetchDataPromises = [
    fetchDepartmentList(),
    fetchUserList(),
    fetchLocationTree()
  ];

  try {
      console.log("[onMounted] 开始并行加载基础数据...");
      await Promise.all(fetchDataPromises);
      console.log("[onMounted] 基础数据加载完成");

      const locationIdParam = route.params.id || route.query.id; // 尝试从 params 和 query 获取
      console.log("[onMounted] 获取到的路由 ID 参数:", locationIdParam);
      if (locationIdParam) {
        const numericId = parseInt(locationIdParam, 10);
        if (!isNaN(numericId)) {
           console.log(`[onMounted] 检测到编辑模式，准备加载 Location ID: ${numericId}`);
           await loadLocationDetail(numericId);
        } else {
            console.warn("[onMounted] 路由参数 ID 不是有效的数字:", locationIdParam);
            ElMessage.error("无效的位置 ID 参数");
        }
      } else {
          console.log("[onMounted] 检测到创建模式");
      }
  } catch (error) {
      console.error("[onMounted] 数据加载过程中出错:", error);
      ElMessage.error("页面初始化失败，请检查网络或联系管理员");
  } finally {
      isLoading.value = false; // 加载完成
      console.log("[onMounted] 组件挂载流程结束，isLoading 设置为 false");
  }
});
</script>

<style scoped>
.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

/* 如果需要特定样式可以保留或添加 */
/* :deep(.location-type-select) {
  .el-select-dropdown__item {
    padding: 0 20px;
  }
} */
</style> 