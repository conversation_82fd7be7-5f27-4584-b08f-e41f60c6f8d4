// File: Application/Features/Tasks/Commands/DeleteCommentCommandHandler.cs
// Description: 删除评论命令处理器

using System;
using System.Threading;
using System.Threading.Tasks;
using MediatR;
using Microsoft.Extensions.Logging;
using ItAssetsSystem.Application.Common.Dtos;
using ItAssetsSystem.Core.Abstractions;

namespace ItAssetsSystem.Application.Features.Tasks.Commands
{
    /// <summary>
    /// 删除评论命令处理器
    /// </summary>
    public class DeleteCommentCommandHandler : IRequestHandler<DeleteCommentCommand, ApiResponse<bool>>
    {
        private readonly ITaskRepository _taskRepository;
        private readonly ILogger<DeleteCommentCommandHandler> _logger;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="taskRepository">任务仓储</param>
        /// <param name="logger">日志记录器</param>
        public DeleteCommentCommandHandler(
            ITaskRepository taskRepository,
            ILogger<DeleteCommentCommandHandler> logger)
        {
            _taskRepository = taskRepository;
            _logger = logger;
        }

        /// <summary>
        /// 处理删除评论命令
        /// </summary>
        /// <param name="request">删除评论命令</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>删除结果</returns>
        public async Task<ApiResponse<bool>> Handle(DeleteCommentCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("开始处理删除评论命令，评论ID: {CommentId}, 任务ID: {TaskId}, 用户ID: {UserId}",
                    request.CommentId, request.TaskId, request.CurrentUserId);

                // 验证评论是否存在
                var existingComment = await _taskRepository.GetCommentByIdAsync(request.CommentId);
                if (existingComment == null)
                {
                    _logger.LogWarning("评论不存在，评论ID: {CommentId}", request.CommentId);
                    return ApiResponse<bool>.CreateFail("评论不存在");
                }

                // 验证评论是否属于指定任务
                if (existingComment.TaskId != request.TaskId)
                {
                    _logger.LogWarning("评论不属于指定任务，评论ID: {CommentId}, 任务ID: {TaskId}", request.CommentId, request.TaskId);
                    return ApiResponse<bool>.CreateFail("评论不属于指定任务");
                }

                // 验证任务是否存在
                var task = await _taskRepository.GetTaskByIdAsync(request.TaskId);
                if (task == null)
                {
                    _logger.LogWarning("任务不存在，任务ID: {TaskId}", request.TaskId);
                    return ApiResponse<bool>.CreateFail("任务不存在");
                }

                // 验证用户权限（只有评论作者、任务负责人、创建者或管理员可以删除评论）
                if (existingComment.UserId != request.CurrentUserId && 
                    task.AssigneeUserId != request.CurrentUserId && 
                    task.CreatorUserId != request.CurrentUserId)
                {
                    _logger.LogWarning("用户无权限删除评论，用户ID: {UserId}, 评论ID: {CommentId}", request.CurrentUserId, request.CommentId);
                    return ApiResponse<bool>.CreateFail("无权限删除评论");
                }

                // 删除评论
                var result = await _taskRepository.DeleteCommentAsync(request.CommentId);
                if (!result)
                {
                    _logger.LogError("删除评论失败，评论ID: {CommentId}", request.CommentId);
                    return ApiResponse<bool>.CreateFail("删除评论失败");
                }

                _logger.LogInformation("成功处理删除评论命令，评论ID: {CommentId}", request.CommentId);
                return ApiResponse<bool>.CreateSuccess(true, "评论删除成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理删除评论命令时发生错误，评论ID: {CommentId}", request.CommentId);
                return ApiResponse<bool>.CreateFail($"处理删除评论时发生错误: {ex.Message}");
            }
        }
    }
}
