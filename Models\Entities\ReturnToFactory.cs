// IT资产管理系统 - 返厂记录实体
// 文件路径: /Models/Entities/ReturnToFactory.cs
// 功能: 定义资产返厂记录实体

using System;
using System.ComponentModel.DataAnnotations;
using ItAssetsSystem.Models;

namespace ItAssetsSystem.Models.Entities
{
    /// <summary>
    /// 返厂记录
    /// </summary>
    public class ReturnToFactory : IAuditableEntity
    {
        /// <summary>
        /// 返厂记录ID
        /// </summary>
        [Key]
        public int Id { get; set; }
        
        /// <summary>
        /// 资产ID
        /// </summary>
        public int AssetId { get; set; }
        
        /// <summary>
        /// 相关资产
        /// </summary>
        public Asset Asset { get; set; }
        
        /// <summary>
        /// 供应商ID
        /// </summary>
        public int SupplierId { get; set; }
        
        /// <summary>
        /// 相关供应商
        /// </summary>
        public Supplier Supplier { get; set; }
        

        
        /// <summary>
        /// 送出时间
        /// </summary>
        public DateTime? SendTime { get; set; }

        /// <summary>
        /// 预计返回时间
        /// </summary>
        public DateTime? EstimatedReturnTime { get; set; }

        /// <summary>
        /// 实际返回时间
        /// </summary>
        public DateTime? ActualReturnTime { get; set; }
        
        /// <summary>
        /// 状态（1-待发送，2-已发送，3-维修中，4-已返回，5-已取消）
        /// </summary>
        public int Status { get; set; }
        

        
        /// <summary>
        /// 返厂单号
        /// </summary>
        [MaxLength(50)]
        public string Code { get; set; }

        /// <summary>
        /// 故障记录ID
        /// </summary>
        public int FaultRecordId { get; set; }

        /// <summary>
        /// 送出人ID
        /// </summary>
        public int SenderId { get; set; }

        /// <summary>
        /// 维修结果
        /// </summary>
        [MaxLength(500)]
        public string RepairResult { get; set; }

        /// <summary>
        /// 维修费用
        /// </summary>
        public decimal? RepairCost { get; set; }

        /// <summary>
        /// 是否在保修期内
        /// </summary>
        public bool InWarranty { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [MaxLength(500)]
        public string Notes { get; set; }
        
        #region 审计字段
        
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }
        
        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; }
        

        
        #endregion
    }
} 