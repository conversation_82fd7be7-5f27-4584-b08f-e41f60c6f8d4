@echo off
chcp 65001 >nul
echo ========================================
echo High Performance Notification Client Build Script
echo ========================================

:: Check if CMake exists
cmake --version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ERROR: CMake not found, please install CMake first
    pause
    exit /b 1
)

:: Create build directory
if not exist build (
    mkdir build
    echo Creating build directory: build
)

cd build

:: Configure project
echo Configuring project...
cmake .. -G "Visual Studio 16 2019" -A x64
if %ERRORLEVEL% neq 0 (
    echo ERROR: CMake configuration failed
    pause
    exit /b 1
)

:: Build project
echo Building project...
cmake --build . --config Release --parallel
if %ERRORLEVEL% neq 0 (
    echo ERROR: Project build failed
    pause
    exit /b 1
)

:: Copy config file to output directory
if exist ..\config\config.json (
    copy ..\config\config.json bin\Release\
    echo Configuration file copied to output directory
)

:: Create necessary directories
if not exist bin\Release\logs (
    mkdir bin\Release\logs
    echo Created logs directory: bin\Release\logs
)

if not exist bin\Release\dumps (
    mkdir bin\Release\dumps
    echo Created dumps directory: bin\Release\dumps
)

echo.
echo ========================================
echo Build completed successfully!
echo.
echo Executable locations:
echo - Main program: build\bin\Release\HighPerformanceNotificationClient.exe
echo - Performance test: build\bin\Release\performance_benchmark.exe
echo - Config tool: build\bin\Release\config_tool.exe
echo.
echo Usage:
echo   cd build\bin\Release
echo   HighPerformanceNotificationClient.exe
echo.
echo Or double-click to run: run.bat
echo ========================================

:: Create run script
cd bin\Release
echo @echo off > run.bat
echo echo Starting High Performance Notification Client... >> run.bat
echo HighPerformanceNotificationClient.exe >> run.bat
echo pause >> run.bat

cd ..\..\..

pause