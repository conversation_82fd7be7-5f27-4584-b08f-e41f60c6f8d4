/**
 * 航空航天级IT资产管理系统 - 故障管理API
 * 文件路径: src/api/fault.js
 * 功能描述: 提供故障管理相关的API服务
 */

import request from '@/utils/request'

// 故障API基础路径 - 使用V2 API
const baseUrl = '/v2/faults'

export default {
  /**
   * 获取故障列表
   * @param {Object} params - 查询参数
   * @returns {Promise}
   */
  getFaultList(params) {
    return request.get(baseUrl, { params })
  },
  
  /**
   * 获取故障详情
   * @param {number|string} id - 故障ID
   * @returns {Promise}
   */
  getFaultById(id) {
    return request.get(`${baseUrl}/${id}`)
  },
  
  /**
   * 创建故障记录
   * @param {Object} data - 故障数据
   * @returns {Promise}
   */
  createFault(data) {
    // 使用V2 API，支持资产ID可选和自动生成备件记录
    return request.post('/v2/faults', {
      faultMode: data.faultMode || 'asset',
      assetId: data.assetId || null,
      assetKeyword: data.assetKeyword || null,
      deviceName: data.deviceName || null,
      faultType: data.faultType,
      title: data.title,
      description: data.description,
      priority: data.priority,
      happenTime: data.happenTime,
      autoGenerateSparePartRecord: data.autoGenerateSparePartRecord || false,
      sparePartInfo: data.sparePartInfo || null
    })
  },
  
  /**
   * 更新故障记录
   * @param {number|string} id - 故障ID
   * @param {Object} data - 故障数据
   * @returns {Promise}
   */
  updateFault(id, data) {
    return request.put(`${baseUrl}/${id}`, data)
  },
  
  /**
   * 删除故障记录
   * @param {number|string} id - 故障ID
   * @returns {Promise}
   */
  deleteFault(id) {
    return request.delete(`${baseUrl}/${id}`)
  },
  
  /**
   * 获取故障类型列表
   * @param {Object} params - 查询参数
   * @returns {Promise}
   */
  getFaultTypeList(params) {
    return request.get('/fault-types', params)
  },
  
  /**
   * 创建故障类型
   * @param {Object} data - 故障类型数据
   * @returns {Promise}
   */
  createFaultType(data) {
    return request.post('/fault-types', data)
  },
  
  /**
   * 更新故障类型
   * @param {number|string} id - 故障类型ID
   * @param {Object} data - 故障类型数据
   * @returns {Promise}
   */
  updateFaultType(id, data) {
    return request.put(`/fault-types/${id}`, data)
  },
  
  /**
   * 删除故障类型
   * @param {number|string} id - 故障类型ID
   * @returns {Promise}
   */
  deleteFaultType(id) {
    return request.delete(`/fault-types/${id}`)
  },
  
  /**
   * 分配故障处理人员
   * @param {number|string} id - 故障ID
   * @param {Object} data - 分配数据
   * @returns {Promise}
   */
  assignFault(id, data) {
    return request.post(`${baseUrl}/${id}/assign`, data)
  },
  
  /**
   * 开始处理故障
   * @param {number|string} id - 故障ID
   * @returns {Promise}
   */
  startFaultProcessing(id) {
    return request.put(`${baseUrl}/${id}/start-processing`)
  },
  
  /**
   * 暂停故障处理
   * @param {number|string} id - 故障ID
   * @param {Object} data - 暂停原因
   * @returns {Promise}
   */
  pauseFaultProcessing(id, data) {
    return request.put(`${baseUrl}/${id}/pause-processing`, data)
  },
  
  /**
   * 完成故障处理
   * @param {number|string} id - 故障ID
   * @param {Object} data - 完成处理的相关数据
   * @returns {Promise}
   */
  completeFault(id, data) {
    return request.put(`${baseUrl}/${id}/complete`, data)
  },
  
  /**
   * 关闭故障
   * @param {number|string} id - 故障ID
   * @param {Object} data - 关闭故障的相关数据
   * @returns {Promise}
   */
  closeFault(id, data) {
    return request.put(`${baseUrl}/${id}/close`, data)
  },
  
  /**
   * 重新打开故障
   * @param {number|string} id - 故障ID
   * @param {Object} data - 重新打开故障的相关数据
   * @returns {Promise}
   */
  reopenFault(id, data) {
    return request.put(`${baseUrl}/${id}/reopen`, data)
  },
  
  /**
   * 添加故障处理记录
   * @param {number|string} id - 故障ID
   * @param {Object} data - 处理记录数据
   * @returns {Promise}
   */
  addFaultRecord(id, data) {
    return request.post(`${baseUrl}/${id}/records`, data)
  },
  
  /**
   * 获取故障处理记录
   * @param {number|string} id - 故障ID
   * @param {Object} params - 查询参数
   * @returns {Promise}
   */
  getFaultRecords(id, params) {
    return request.get(`${baseUrl}/${id}/records`, params)
  },
  
  /**
   * 导出故障数据
   * @param {Object} params - 查询参数
   * @returns {Promise}
   */
  exportFaults(params) {
    return request.download(`${baseUrl}/export`, params, 'faults.xlsx')
  },
  
  /**
   * 获取故障统计数据
   * @param {Object} params - 查询参数
   * @returns {Promise}
   */
  getFaultStatistics(params) {
    return request.get(`${baseUrl}/statistics`, params)
  },

  // ===== 新增功能 =====

  /**
   * 故障维修使用备件
   * @param {number} faultId - 故障ID
   * @param {Object} data - 备件使用数据
   * @returns {Promise}
   */
  useSpareParts(faultId, data) {
    return request.post(`${baseUrl}/${faultId}/use-spare-parts`, data)
  },

  /**
   * 创建返厂记录
   * @param {number} faultId - 故障ID
   * @param {Object} data - 返厂数据
   * @returns {Promise}
   */
  createReturnToFactory(faultId, data) {
    return request.post(`${baseUrl}/${faultId}/return-to-factory`, data)
  },



  /**
   * 搜索资产
   * @param {Object} params - 搜索参数
   * @returns {Promise}
   */
  searchAssets(params) {
    return request.get('/assets/search', params)
  }
}

// 为了向后兼容性，提供命名导出
export const getFaults = (params) => {
  return request.get(baseUrl, { params })
}