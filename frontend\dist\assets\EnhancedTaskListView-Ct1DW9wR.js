import{_ as e,bo as a,ah as s,al as l,aG as t,a as r,b as n,o as i,k as d,$ as o,l as u,w as c,e as m,p,t as v,i as g,aw as f,d as k,aB as y,af as w,F as h,h as b,r as U,x as I,ad as _,z as D,bq as C,E as V,ag as S,c as T,br as B,m as N,f as z,bs as x,D as E,v as q,J as F,b1 as P,aJ as $,n as A,a3 as L,aH as M,az as j,bt as O,a9 as H,bu as Q}from"./index-CG5lHOPO.js";import{u as R,T as G,B as J,b as K,a as W,E as X}from"./BatchStatusDialog-BJmUrW6n.js";import{U as Y,a as Z}from"./UserAvatarStack-X4c9Liov.js";import{T as ee}from"./TaskClaimDialog-Dx9jUgxp.js";import{u as ae}from"./gamification-Dm7mCEPf.js";import{g as se}from"./gamification-2FBMrBgR.js";import"./format-DfhXadVZ.js";import"./UserSelect-BwdFLAkx.js";import"./workShift-Ce3ThpoM.js";const le={class:"quick-task-creator"},te={class:"quick-form-container floating"},re={class:"form-header"},ne={class:"quick-settings"},ie={class:"quick-actions"},de={class:"right-actions"};const oe=e({name:"QuickTaskCreatorSimple",components:{Plus:t,Close:l,Check:s,More:a},props:{triggerText:{type:String,default:"快速创建任务"},triggerClass:{type:String,default:""}},emits:["created","expand","collapse","expandToFullForm"],setup(e,{emit:a}){const s=U(null),l=U(!1),t=U(!1),r=I(),n=U([]),i=U(!1),d=_({title:"",description:"",assigneeUserIds:[],priority:"Medium",dueDate:null}),o=()=>{l.value=!1,u(),a("collapse")},u=()=>{d.title="",d.description="",d.assigneeUserIds=[],d.priority="Medium",d.dueDate=null,s.value&&s.value.clearValidate()},c=e=>e?e.toISOString().split("T")[0]:null;return D((()=>{(async()=>{i.value=!0;try{const e=await C.getUserList();e&&e.data&&(n.value=e.data.map((e=>({id:e.id,name:e.name||e.userName,department:e.department||"未知部门"}))),r.userInfo&&r.userInfo.id&&(d.assigneeUserIds=[r.userInfo.id]))}catch(e){V.error("获取用户列表失败，使用测试数据"),n.value=[{id:1,name:"张三",department:"技术部"},{id:2,name:"李四",department:"运维部"},{id:3,name:"王五",department:"产品部"},{id:4,name:"赵六",department:"设计部"}],n.value.length>0&&(d.assigneeUserIds=[n.value[0].id])}finally{i.value=!1}})(),(()=>{const e=new Date,a=new Date;a.setMonth(e.getMonth()+1),d.dueDate=a})()})),{quickFormRef:s,isExpanded:l,submitting:t,users:n,quickForm:d,quickRules:{title:[{required:!0,message:"请输入任务标题",trigger:"blur"},{min:2,max:100,message:"长度在 2 到 100 个字符",trigger:"blur"}]},expandCreator:()=>{l.value=!0,a("expand")},collapseCreator:o,resetForm:u,handleQuickSubmit:async()=>{if(!s.value)return;if(await s.value.validate().catch((()=>!1))){t.value=!0;try{const e=d.assigneeUserIds.length>0?d.assigneeUserIds[0]:null,s=d.assigneeUserIds.slice(1),l=new Date,t={name:d.title,description:d.description||"",assigneeUserId:e,collaboratorUserIds:s,priority:d.priority,planStartDate:c(l),planEndDate:d.dueDate?c(d.dueDate):null,status:"Todo",taskType:"Normal"},r=await S.createTask(t);if(!r.success)throw new Error(r.message||"创建失败");V.success("🎉 任务创建成功！"),a("created",r.data),u(),o()}catch(e){V.error("创建任务失败: "+(e.message||"未知错误"))}finally{t.value=!1}}},expandToFullForm:()=>{a("expandToFullForm",{title:d.title,description:d.description,assigneeUserIds:d.assigneeUserIds,participantUserIds:d.participantUserIds,priority:d.priority,dueDate:d.dueDate}),o()},loading:i}}},[["render",function(e,a,s,l,t,U){const I=r("Plus"),_=r("el-icon"),D=r("el-button"),C=r("Close"),V=r("el-input"),S=r("el-form-item"),T=r("el-option"),B=r("el-select"),N=r("el-tag"),z=r("el-date-picker"),x=r("More"),E=r("Check"),q=r("el-form"),F=r("el-card");return i(),n("div",le,[l.isExpanded?o("",!0):(i(),d(D,{key:0,type:"primary",onClick:l.expandCreator,class:g(s.triggerClass)},{default:c((()=>[m(_,null,{default:c((()=>[m(I)])),_:1}),p(" "+v(s.triggerText),1)])),_:1},8,["onClick","class"])),u(k("div",te,[m(F,{shadow:"always",class:"quick-form-card"},{header:c((()=>[k("div",re,[a[5]||(a[5]=k("span",null,"⚡ 快速创建任务",-1)),m(D,{type:"text",onClick:l.collapseCreator,class:"close-btn"},{default:c((()=>[m(_,null,{default:c((()=>[m(C)])),_:1})])),_:1},8,["onClick"])])])),default:c((()=>[m(q,{ref:"quickFormRef",model:l.quickForm,rules:l.quickRules,size:"small",onSubmit:y(l.handleQuickSubmit,["prevent"])},{default:c((()=>[m(S,{prop:"title"},{default:c((()=>[m(V,{modelValue:l.quickForm.title,"onUpdate:modelValue":a[0]||(a[0]=e=>l.quickForm.title=e),placeholder:"任务标题（必填）",maxlength:"100",clearable:"",onKeyup:w(l.handleQuickSubmit,["enter"]),autofocus:""},null,8,["modelValue","onKeyup"])])),_:1}),m(S,{prop:"description"},{default:c((()=>[m(V,{type:"textarea",modelValue:l.quickForm.description,"onUpdate:modelValue":a[1]||(a[1]=e=>l.quickForm.description=e),placeholder:"任务内容/描述",rows:3,maxlength:"500","show-word-limit":""},null,8,["modelValue"])])),_:1}),k("div",ne,[m(B,{modelValue:l.quickForm.assigneeUserIds,"onUpdate:modelValue":a[2]||(a[2]=e=>l.quickForm.assigneeUserIds=e),multiple:"","collapse-tags":"",placeholder:"负责人",filterable:"",clearable:"",class:"quick-assignee"},{default:c((()=>[(i(!0),n(h,null,b(l.users,(e=>(i(),d(T,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"]),m(B,{modelValue:l.quickForm.priority,"onUpdate:modelValue":a[3]||(a[3]=e=>l.quickForm.priority=e),placeholder:"优先级",class:"quick-priority"},{default:c((()=>[m(T,{label:"低",value:"Low"},{default:c((()=>[m(N,{type:"info",size:"small"},{default:c((()=>a[6]||(a[6]=[p("低")]))),_:1})])),_:1}),m(T,{label:"中",value:"Medium"},{default:c((()=>[m(N,{type:"warning",size:"small"},{default:c((()=>a[7]||(a[7]=[p("中")]))),_:1})])),_:1}),m(T,{label:"高",value:"High"},{default:c((()=>[m(N,{type:"danger",size:"small"},{default:c((()=>a[8]||(a[8]=[p("高")]))),_:1})])),_:1})])),_:1},8,["modelValue"]),m(z,{modelValue:l.quickForm.dueDate,"onUpdate:modelValue":a[4]||(a[4]=e=>l.quickForm.dueDate=e),type:"date",placeholder:"截止日期",class:"quick-date",size:"small"},null,8,["modelValue"])]),k("div",ie,[a[11]||(a[11]=k("div",{class:"left-actions"},[k("span",{class:"tip-text"},"💡 填写标题即可快速创建")],-1)),k("div",de,[m(D,{type:"text",onClick:l.expandToFullForm,class:"expand-btn"},{default:c((()=>[m(_,null,{default:c((()=>[m(x)])),_:1}),a[9]||(a[9]=p(" 详细设置 "))])),_:1},8,["onClick"]),m(D,{type:"primary",onClick:l.handleQuickSubmit,loading:l.submitting,size:"small"},{default:c((()=>[m(_,null,{default:c((()=>[m(E)])),_:1}),a[10]||(a[10]=p(" 创建 "))])),_:1},8,["onClick","loading"])])])])),_:1},8,["model","rules","onSubmit"])])),_:1})],512),[[f,l.isExpanded]])])}],["__scopeId","data-v-7ea0e0c4"]]),ue={class:"enhanced-task-list"},ce={class:"page-header"},me={class:"header-right"},pe={class:"card-header"},ve={class:"filter-grid"},ge={class:"user-dept"},fe={class:"stats-grid"},ke={class:"stat-content"},ye={class:"stat-info"},we={class:"stat-number"},he={class:"stat-content"},be={class:"stat-info"},Ue={class:"stat-number"},Ie={class:"stat-content"},_e={class:"stat-info"},De={class:"stat-number"},Ce={class:"stat-content"},Ve={class:"stat-info"},Se={class:"stat-number"},Te={key:0,class:"batch-actions-bar"},Be={class:"batch-info"},Ne={class:"batch-actions"},ze={class:"table-header"},xe={class:"table-actions"},Ee={key:0},qe=["onClick"],Fe={class:"task-content"},Pe={class:"task-title"},$e={key:0,class:"task-desc"},Ae={class:"watermark-text"},Le={class:"assignee-cell"},Me={key:0,class:"no-assignee"},je={class:"completer-cell"},Oe={key:0,class:"completer-info"},He={class:"completer-name"},Qe={key:1,class:"no-completer"},Re={class:"status-cell"},Ge={class:"date-cell"},Je={key:0,class:"start-date"},Ke={key:1,class:"no-date"},We={key:0,class:"date-cell"},Xe={class:"due-date-container"},Ye={key:0,class:"overdue-warning"},Ze={key:1,class:"no-date"},ea={class:"action-buttons"},aa={key:1,class:"card-view"},sa={class:"task-cards-grid"},la={class:"pagination-wrapper"},ta=e({__name:"EnhancedTaskListView",setup(e){const s=R(),l=ae(),t=U(!1),f=U("table"),y=U([]),I=U(""),le=U([]),te=U([]),re=U(null),ne=U(!1),ie=U(0),de=U(null),ta=_({status:"",priority:"",assigneeIds:[]}),ra=_({currentPage:1,pageSize:20}),na=U(!1),ia=U(!1),da=U(!1),oa=U(!1),ua=U(!1),ca=U(null),ma=T((()=>{let e=s.tasks||[];if(ta.status&&(e=e.filter((e=>e.status===ta.status))),ta.priority&&(e=e.filter((e=>e.priority===ta.priority))),ta.assigneeIds&&ta.assigneeIds.length>0&&(e=e.filter((e=>ta.assigneeIds.includes(e.assigneeUserId)))),I.value){const a=I.value.toLowerCase();e=e.filter((e=>{var s,l;return(null==(s=e.name)?void 0:s.toLowerCase().includes(a))||(null==(l=e.description)?void 0:l.toLowerCase().includes(a))}))}if(le.value&&2===le.value.length){const[a,s]=le.value;e=e.filter((e=>{if(!e.planEndDate)return!1;const l=new Date(e.planEndDate);return l>=a&&l<=s}))}return e})),pa=T((()=>{const e=(ra.currentPage-1)*ra.pageSize,a=e+ra.pageSize;return ma.value.slice(e,a)})),va=T((()=>{const e=ma.value;return{total:e.length,inProgress:e.filter((e=>"InProgress"===e.status)).length,completed:e.filter((e=>"Done"===e.status)).length,overdue:e.filter((e=>Oa(e))).length}})),ga=T((()=>te.value.map((e=>e.taskId)))),fa=T((()=>pa.value.map((e=>{if(!e)return e;const a=y.value.find((a=>a&&a.id===e.assigneeUserId));return{...e,assigneeUserName:(null==a?void 0:a.name)||e.assigneeUserName||"未知用户",assigneeAvatarUrl:B(e.assigneeAvatarUrl||(null==a?void 0:a.avatarUrl)||(null==a?void 0:a.avatar)||"")}})).filter(Boolean))),ka=async(e=!1)=>{t.value=!0;try{e&&ts(),await s.fetchTasks({status:ta.status,priority:ta.priority,assigneeIds:ta.assigneeIds,search:I.value,pageNumber:1,pageSize:1e3,forceRefresh:e})}catch(a){V.error("加载任务列表失败: "+a.message)}finally{t.value=!1}},ya=()=>{ra.currentPage=1,ka()},wa=()=>{Object.assign(ta,{status:"",priority:"",assigneeIds:[]}),I.value="",le.value=[],ra.currentPage=1,ka()},ha=()=>{ya()},ba=e=>{te.value=e},Ua=()=>{te.value=[]},Ia=(e,a)=>{if(a){const a=ma.value.find((a=>a.taskId===e));a&&!te.value.find((a=>a.taskId===e))&&te.value.push(a)}else te.value=te.value.filter((a=>a.taskId!==e))},_a=e=>{ra.pageSize=e,ra.currentPage=1},Da=e=>{ra.currentPage=e},Ca=e=>{ca.value=e,na.value=!0,async function(e){await S.recordTaskView(e),await Wa()}(e.taskId)},Va=()=>{re.value=null,ne.value=!1,ia.value=!0},Sa=()=>{ia.value=!1,re.value=null,ne.value=!1},Ta=e=>{V.success("任务创建成功!"),ka(!0)},Ba=()=>{ka(!0)},Na=e=>{re.value=e,ne.value=!1,ia.value=!0},za=async(e,a)=>{switch(e){case"clone":await qa(a);break;case"assign":te.value=[a],da.value=!0;break;case"complete":await Fa(a);break;case"delete":await Pa(a)}},xa=e=>{za(e.action,e.task)},Ea=async e=>{try{await s.updateTaskStatus(e.taskId,e.newStatus),V.success("任务状态更新成功"),ka(!0)}catch(a){V.error("状态更新失败: "+a.message)}},qa=async e=>{try{const a={...e,name:`${e.name} (副本)`,status:"Todo",progress:0};delete a.taskId,await s.createTask(a),V.success("任务克隆成功"),ka(!0)}catch(a){V.error("任务克隆失败: "+a.message)}},Fa=async e=>{try{const a=await S.completeTask(e.taskId,{remarks:"任务完成操作"});if(a&&a.success){V.success("任务已标记为完成");const a=!!e.planEndDate&&new Date<=new Date(e.planEndDate);l.recordEvent("task_completed",{taskId:e.taskId,taskName:e.name,isOnTime:a}),ka(!0)}else V.error((null==a?void 0:a.message)||"任务完成失败")}catch(a){V.error("操作失败: "+a.message)}},Pa=async e=>{var a;try{await H.confirm(`确定要删除任务"${e.name}"吗？`,"确认删除",{type:"warning"}),await s.deleteTask(e.taskId),V.success("任务删除成功"),ka(!0)}catch(l){404===(null==(a=null==l?void 0:l.response)?void 0:a.status)?(V.error("任务已被删除或不存在"),ka(!0)):"cancel"!==l&&V.error("删除失败: "+l.message)}},$a=async()=>{try{await H.confirm(`确定要删除选中的 ${te.value.length} 个任务吗？`,"确认批量删除",{type:"warning"}),await s.batchDeleteTasks(ga.value),V.success("批量删除成功"),Ua(),ka()}catch(e){"cancel"!==e&&V.error("批量删除失败: "+e.message)}},Aa=()=>{V.success("批量分配成功"),Ua(),ka(!0)},La=()=>{V.success("批量状态修改成功"),Ua(),ka(!0)},Ma=e=>({High:"🔴",Medium:"🟡",Low:"🟢"}[e]||"⚪"),ja=e=>({High:"高",Medium:"中",Low:"低"}[e]||e),Oa=e=>!(!e.planEndDate||"Done"===e.status)&&new Date(e.planEndDate)<new Date,Ha=e=>{if(!e.planEndDate||"Done"===e.status)return!1;const a=new Date(e.planEndDate),s=new Date,l=Math.ceil((a-s)/864e5);return l<=2&&l>=0},Qa=e=>e?new Date(e).toLocaleDateString("zh-CN"):"",Ra=e=>{if(!e.planEndDate)return 0;const a=new Date(e.planEndDate),s=new Date;return Math.floor((s-a)/864e5)},Ga=async(e,a)=>{var r;try{t.value=!0;e.status,e.statusName;e.status=a,e.statusName=Ja(a);const i=await s.updateTaskStatus(e.taskId,a,"通过状态下拉框更新");if(is({success:!0,data:i}),!i)throw new Error("API响应为空");{const t=(null==(r=s.tasks)?void 0:r.findIndex((a=>a&&a.taskId===e.taskId)))??-1;if(-1!==t&&s.tasks){const l={...s.tasks[t],...i};"Done"===a&&!l.completedByUserId&&de.value?(l.completedByUserId=de.value.id,l.completedByUserName=de.value.name||de.value.username,l.completedByUserAvatarUrl=de.value.avatar||de.value.avatarUrl,l.completedAt=l.actualEndDate||(new Date).toISOString()):"Done"===a&&l.completedByUserId,s.tasks.splice(t,1,l),Object.assign(e,l)}if("Done"===a)try{const a=!!e.planEndDate&&new Date<=new Date(e.planEndDate);await l.recordEvent("task_completed",{taskId:e.taskId,taskName:e.name,isOnTime:a}),V.success("任务已完成，获得奖励！")}catch(n){V.success("任务状态更新成功")}else V.success("状态更新成功")}}catch(n){e.status=originalStatus,e.statusName=originalStatusName,V.error("状态更新失败: "+(n.message||"未知错误"))}finally{t.value=!1}},Ja=e=>({Todo:"待处理",InProgress:"进行中",Done:"已完成",Cancelled:"已取消"}[e]||e);D((()=>{ka(),(async()=>{try{const e=await C.getUserList();e&&e.data?y.value=e.data.map((e=>e?{id:e.id||e.userId||e.ID||0,name:e.name||e.userName||e.username||e.displayName||"未知用户",department:e.department||e.departmentName||"",avatar:e.avatar||e.avatarUrl||""}:null)).filter((e=>e&&e.id>0)):y.value=[]}catch(e){y.value=[]}})(),(async()=>{try{const e=await C.getCurrentUser();e&&e.data&&(de.value={id:e.data.id||e.data.userId||e.data.ID,name:e.data.name||e.data.userName||e.data.username||e.data.displayName,department:e.data.department||e.data.departmentName||"",avatar:e.data.avatar||e.data.avatarUrl||""})}catch(e){const s=localStorage.getItem("userInfo");if(s)try{const e=JSON.parse(s);de.value={id:e.id||e.userId||e.ID,name:e.name||e.userName||e.username||e.displayName,department:e.department||e.departmentName||"",avatar:e.avatar||e.avatarUrl||""}}catch(a){}}})(),Wa()}));const Ka=async e=>{try{if(ne.value){const a={...e,taskId:re.value.taskId};await s.updateTask(a),V.success("任务更新成功!")}else await s.createTask(e),V.success("任务创建成功!");Sa(),await ka()}catch(a){V.error("操作失败: "+(a.message||"未知错误"))}};async function Wa(){var e;const a=await S.getTodayViewedCount();ie.value=(null==(e=a.data)?void 0:e.count)||0}const Xa=e=>{var a;return!("Done"===e.status||"Cancelled"===e.status||e.assigneeUserId&&e.assigneeUserId!==(null==(a=de.value)?void 0:a.id)||e.claimedByUserId)},Ya=e=>{var a;return e.claimedByUserId&&e.claimedByUserId!==(null==(a=de.value)?void 0:a.id)},Za=async()=>{},es=({row:e})=>{const a=["task-row"];switch(e.status){case"Todo":a.push("task-row--todo");break;case"InProgress":a.push("task-row--in-progress");break;case"Done":a.push("task-row--done");break;case"Cancelled":a.push("task-row--cancelled")}return Oa(e)&&"Done"!==e.status&&a.push("task-row--overdue"),Ha(e)&&"Done"!==e.status&&a.push("task-row--due-soon"),a.join(" ")},as=e=>{if(!e)return[];const a=[];return e.assignees&&Array.isArray(e.assignees)&&e.assignees.length>0?e.assignees.forEach((e=>{a.push({id:e.userId,name:e.userName||"未知用户",avatarUrl:B(e.avatarUrl||""),role:e.role||("Assignee"===e.assignmentType?"Primary":"Collaborator"),isPrimary:"Primary"===e.role||"Assignee"===e.assignmentType})})):e.assigneeUserId&&a.push({id:e.assigneeUserId,name:e.assigneeUserName||"未知用户",avatarUrl:B(e.assigneeAvatarUrl||""),role:"Primary",isPrimary:!0}),a},ss=e=>{if(!e.completedByUserId)return null;const a=y.value.find((a=>a.id===e.completedByUserId));return a?{id:a.id,name:a.name||a.username,avatar:a.avatar||a.avatarUrl||""}:{id:e.completedByUserId,name:e.completedByUserName||"未知用户",avatar:e.completedByUserAvatarUrl||""}},ls=U(new Map),ts=()=>{ls.value.clear()},rs=e=>{if(e.completedByUserName)return`完成人：${e.completedByUserName}`;if(e.finishedByUserName)return`完成人：${e.finishedByUserName}`;const a=(e=>{if(!e.completedByUserId)return null;const a=`${e.completedByUserId}-${e.completedByUserName}`;if(ls.value.has(a))return ls.value.get(a);const s=ss(e);return s&&ls.value.set(a,s),s})(e);if(null==a?void 0:a.name)return`完成人：${a.name}`;const s=ss(e);return(null==s?void 0:s.name)?`完成人：${s.name}`:e.completedByUserId?`完成人：用户${e.completedByUserId}`:e.finishedByUserId?`完成人：用户${e.finishedByUserId}`:"Done"===e.status&&de.value?`完成人：${de.value.name||de.value.username||"当前用户"}`:"已完成"},ns=e=>{},is=e=>{},ds=()=>{var e;((null==(e=s.tasks)?void 0:e.filter((e=>"Done"===e.status)))||[]).forEach(((e,a)=>{"Done"===e.status&&(e.completedByUserName||e.completedByUserId),"Done"===e.status&&(e.completedByUserName||e.completedByUserId)}))},os=async e=>{var a;try{const l=null==(a=s.tasks)?void 0:a.find((a=>a.taskId===e));if(!l)return;await Ga(l,"Done")}catch(l){}},us=()=>{window.$apiVersionManager,window.$apiAdapter;s.fetchTasks({pageNumber:1,pageSize:1}).then((e=>{})).catch((e=>{}))},cs=e=>{var a;null==(a=s.tasks)||a.find((a=>{var s;return(null==(s=a.name)?void 0:s.includes(e))||a.taskId===e}))},ms=()=>{var e;((null==(e=s.tasks)?void 0:e.filter((e=>"Done"===e.status)))||[]).forEach(((e,a)=>{}))};return"undefined"!=typeof window&&(window.testCompleterLogic=ds,window.debugCompleterInfo=ns,window.debugApiResponse=is,window.testTaskStatusUpdate=os,window.testApiVersion=us,window.debugTaskWatermark=cs,window.debugAllCompletedTasks=ms,window.getWatermarkText=rs,window.taskStore=s),(e,s)=>{const l=r("el-icon"),U=r("el-button"),_=r("el-option"),D=r("el-select"),C=r("el-avatar"),T=r("el-input"),B=r("el-date-picker"),H=r("el-card"),R=r("el-button-group"),ae=r("el-table-column"),ie=r("el-tag"),qa=r("el-text"),Fa=r("el-dropdown-item"),Pa=r("el-dropdown-menu"),Ja=r("el-dropdown"),Wa=r("el-table"),ss=r("el-pagination"),ls=N("loading");return i(),n("div",ue,[k("div",ce,[s[20]||(s[20]=k("div",{class:"header-left"},[k("h1",null,"📋 任务管理中心"),k("p",{class:"subtitle"},"高效管理，轻松协作")],-1)),k("div",me,[m(oe,{"trigger-text":"⚡ 快速创建","trigger-class":"quick-create-btn",onCreated:Ta,onExpandToFullForm:Na}),m(U,{type:"primary",onClick:Va},{default:c((()=>[m(l,null,{default:c((()=>[m(z(x))])),_:1}),s[19]||(s[19]=p(" 详细创建 "))])),_:1})])]),m(H,{class:"filter-card",shadow:"hover"},{header:c((()=>[k("div",pe,[s[22]||(s[22]=k("span",null,"🔍 智能筛选",-1)),m(U,{link:"",onClick:wa,size:"small"},{default:c((()=>[m(l,null,{default:c((()=>[m(z(q))])),_:1}),s[21]||(s[21]=p(" 重置 "))])),_:1})])])),default:c((()=>[k("div",ve,[m(D,{modelValue:ta.status,"onUpdate:modelValue":s[0]||(s[0]=e=>ta.status=e),placeholder:"任务状态",clearable:"",class:"filter-item"},{default:c((()=>[m(_,{label:"全部状态",value:""}),m(_,{label:"📋 待处理",value:"Todo"}),m(_,{label:"🔄 进行中",value:"InProgress"}),m(_,{label:"✅ 已完成",value:"Done"}),m(_,{label:"❌ 已取消",value:"Cancelled"})])),_:1},8,["modelValue"]),m(D,{modelValue:ta.priority,"onUpdate:modelValue":s[1]||(s[1]=e=>ta.priority=e),placeholder:"优先级",clearable:"",class:"filter-item"},{default:c((()=>[m(_,{label:"全部优先级",value:""}),m(_,{label:"🔴 高优先级",value:"High"}),m(_,{label:"🟡 中优先级",value:"Medium"}),m(_,{label:"🟢 低优先级",value:"Low"})])),_:1},8,["modelValue"]),m(D,{modelValue:ta.assigneeIds,"onUpdate:modelValue":s[2]||(s[2]=e=>ta.assigneeIds=e),multiple:"",placeholder:"负责人",clearable:"",filterable:"",class:"filter-item","collapse-tags":"","collapse-tags-tooltip":""},{default:c((()=>[(i(!0),n(h,null,b(y.value,(e=>(i(),d(_,{key:e.id,label:e.name,value:e.id},{default:c((()=>[m(C,{src:e.avatar,size:18,style:{"margin-right":"6px"}},null,8,["src"]),k("span",null,v(e.name),1),k("span",ge,v(e.department),1)])),_:2},1032,["label","value"])))),128))])),_:1},8,["modelValue"]),m(T,{modelValue:I.value,"onUpdate:modelValue":s[3]||(s[3]=e=>I.value=e),placeholder:"🔍 搜索任务标题、描述...",clearable:"",class:"search-input",onKeyup:w(ya,["enter"])},{prefix:c((()=>[m(l,null,{default:c((()=>[m(z(E))])),_:1})])),_:1},8,["modelValue"]),m(B,{modelValue:le.value,"onUpdate:modelValue":s[4]||(s[4]=e=>le.value=e),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",class:"date-picker",onChange:ha},null,8,["modelValue"]),m(U,{type:"primary",onClick:ya,loading:t.value,class:"search-btn"},{default:c((()=>[m(l,null,{default:c((()=>[m(z(E))])),_:1}),s[23]||(s[23]=p(" 搜索 "))])),_:1},8,["loading"])])])),_:1}),k("div",fe,[m(H,{class:"stat-card total",shadow:"hover"},{default:c((()=>[k("div",ke,[s[25]||(s[25]=k("div",{class:"stat-icon"},"📊",-1)),k("div",ye,[k("div",we,v(va.value.total),1),s[24]||(s[24]=k("div",{class:"stat-label"},"总任务",-1))])])])),_:1}),m(H,{class:"stat-card progress",shadow:"hover"},{default:c((()=>[k("div",he,[s[27]||(s[27]=k("div",{class:"stat-icon"},"🔄",-1)),k("div",be,[k("div",Ue,v(va.value.inProgress),1),s[26]||(s[26]=k("div",{class:"stat-label"},"进行中",-1))])])])),_:1}),m(H,{class:"stat-card completed",shadow:"hover"},{default:c((()=>[k("div",Ie,[s[29]||(s[29]=k("div",{class:"stat-icon"},"✅",-1)),k("div",_e,[k("div",De,v(va.value.completed),1),s[28]||(s[28]=k("div",{class:"stat-label"},"已完成",-1))])])])),_:1}),m(H,{class:"stat-card overdue",shadow:"hover"},{default:c((()=>[k("div",Ce,[s[31]||(s[31]=k("div",{class:"stat-icon"},"⏰",-1)),k("div",Ve,[k("div",Se,v(va.value.overdue),1),s[30]||(s[30]=k("div",{class:"stat-label"},"已逾期",-1))])])])),_:1})]),te.value.length>0?(i(),n("div",Te,[k("div",Be,[k("span",null,"已选择 "+v(te.value.length)+" 个任务",1),m(U,{link:"",onClick:Ua},{default:c((()=>s[32]||(s[32]=[p("取消选择")]))),_:1})]),k("div",Ne,[m(U,{size:"small",onClick:s[5]||(s[5]=e=>da.value=!0)},{default:c((()=>[m(l,null,{default:c((()=>[m(z(F))])),_:1}),s[33]||(s[33]=p(" 批量分配 "))])),_:1}),m(U,{size:"small",onClick:s[6]||(s[6]=e=>oa.value=!0)},{default:c((()=>[m(l,null,{default:c((()=>[m(z(P))])),_:1}),s[34]||(s[34]=p(" 批量修改状态 "))])),_:1}),m(U,{size:"small",type:"danger",onClick:$a},{default:c((()=>[m(l,null,{default:c((()=>[m(z($))])),_:1}),s[35]||(s[35]=p(" 批量删除 "))])),_:1})])])):o("",!0),m(H,{class:"table-card",shadow:"hover"},{header:c((()=>[k("div",ze,[k("span",null,"📋 任务列表 ("+v(ma.value.length)+")",1),k("div",xe,[m(R,{size:"small"},{default:c((()=>[m(U,{type:"table"===f.value?"primary":"",onClick:s[7]||(s[7]=e=>f.value="table")},{default:c((()=>[m(l,null,{default:c((()=>[m(z(j))])),_:1}),s[36]||(s[36]=p(" 表格 "))])),_:1},8,["type"]),m(U,{type:"card"===f.value?"primary":"",onClick:s[8]||(s[8]=e=>f.value="card")},{default:c((()=>[m(l,null,{default:c((()=>[m(z(O))])),_:1}),s[37]||(s[37]=p(" 卡片 "))])),_:1},8,["type"])])),_:1})])])])),default:c((()=>["table"===f.value?(i(),n("div",Ee,[u((i(),d(Wa,{data:fa.value,onSelectionChange:ba,"row-key":"taskId",class:"enhanced-table","row-class-name":es},{default:c((()=>[m(ae,{type:"selection",width:"50"}),m(ae,{label:"优先级",width:"70",align:"center"},{default:c((({row:e})=>{return[m(ie,{type:(a=e.priority,{High:"danger",Medium:"warning",Low:"info"}[a]||"info"),size:"small",class:"priority-tag-compact",title:ja(e.priority)},{default:c((()=>[p(v(Ma(e.priority)),1)])),_:2},1032,["type","title"])];var a})),_:1}),m(ae,{label:"任务名称","min-width":"200"},{default:c((({row:e})=>{return[k("div",{class:"task-name-cell",onClick:a=>Ca(e)},[k("div",Fe,[k("span",Pe,v(e.name),1),e.description?(i(),n("div",$e,v((a=e.description,s=50,!a||a.length<=s?a:a.substring(0,s)+"...")),1)):o("",!0)]),"Done"===e.status&&e.completedByUserName?(i(),n("div",{key:0,class:"completion-watermark",style:A({"--watermark-color":e.completionWatermarkColor||"#409EFF"})},[k("span",Ae," 完成人："+v(e.completedByUserName),1)],4)):o("",!0)],8,qe)];var a,s})),_:1}),m(ae,{label:"负责人",width:"150"},{default:c((({row:e})=>[k("div",Le,[m(Y,{users:as(e),"is-main-user-primary":!0,"max-users":4,"avatar-size":"6",overlap:-10,class:"small"},null,8,["users"]),e.assigneeUserId?o("",!0):(i(),n("span",Me,"未分配"))])])),_:1}),m(ae,{label:"完成人",width:"120"},{default:c((({row:e})=>[k("div",je,["Done"===e.status&&e.completedByUserName?(i(),n("div",Oe,[m(Z,{user:{id:e.completedByUserId,name:e.completedByUserName,avatar:e.completedByUserAvatarUrl},size:24,class:"completer-avatar"},null,8,["user"]),k("span",He,v(e.completedByUserName),1)])):(i(),n("span",Qe,"-"))])])),_:1}),m(ae,{label:"状态",width:"120",align:"center"},{default:c((({row:e})=>{var a;return[k("div",Re,[m(D,{"model-value":e.status,onChange:a=>Ga(e,a),size:"small",class:g(["status-select",`status-select--${null==(a=e.status)?void 0:a.toLowerCase()}`])},{default:c((()=>[m(_,{label:"📋 待处理",value:"Todo",class:"status-option--todo"}),m(_,{label:"🔄 进行中",value:"InProgress",class:"status-option--inprogress"}),m(_,{label:"✅ 已完成",value:"Done",class:"status-option--done"}),m(_,{label:"❌ 已取消",value:"Cancelled",class:"status-option--cancelled"})])),_:2},1032,["model-value","onChange","class"])])]})),_:1}),m(ae,{label:"开始日期",width:"120"},{default:c((({row:e})=>[k("div",Ge,[e.planStartDate?(i(),n("span",Je,v(Qa(e.planStartDate)),1)):(i(),n("span",Ke,"未设置"))])])),_:1}),m(ae,{label:"截止日期",width:"150"},{default:c((({row:e})=>[e.planEndDate?(i(),n("div",We,[k("div",Xe,[k("span",{class:g({overdue:Oa(e),"due-soon":Ha(e)})},v(Qa(e.planEndDate)),3),Oa(e)?(i(),n("div",Ye,[m(qa,{type:"danger",size:"small"},{default:c((()=>[p(" 已逾期 "+v(Ra(e))+" 天 ",1)])),_:2},1024)])):o("",!0)])])):(i(),n("span",Ze,"未设置"))])),_:1}),m(ae,{label:"操作",width:"220",align:"center"},{default:c((({row:e})=>{return[k("div",ea,[Xa(e)?(i(),d(U,{key:0,type:"success",size:"small",onClick:a=>(async e=>{var a,s,l,t,r;if(Xa(e))try{e.claiming=!0;const n=await se.claimTask(e.taskId,`用户主动领取任务: ${e.name}`);if(n&&n.success){Object.assign(e,{claimedByUserId:null==(a=de.value)?void 0:a.id,claimedByUserName:null==(s=de.value)?void 0:s.name,claiming:!1});const i=(null==(l=n.data)?void 0:l.reward)?`🎉 成功领取任务: ${e.name}\n🎁 获得 ${n.data.reward.pointsGained} 金币 + ${n.data.reward.xpGained} 经验值`:`🎉 成功领取任务: ${e.name}`;V.success({message:i,duration:4e3,showClose:!0,dangerouslyUseHTMLString:!0}),(null==(r=null==(t=n.data)?void 0:t.reward)?void 0:r.leveledUp)&&Q.success({title:"🎊 恭喜升级！",message:`您已升级到 ${n.data.reward.newLevel} 级！`,duration:5e3,position:"top-right"}),await Za(),await ka(!0)}else V.error((null==n?void 0:n.message)||(null==n?void 0:n.error)||"任务领取失败")}catch(n){V.error("任务领取失败，请稍后重试")}finally{e.claiming=!1}else V.warning("该任务无法领取")})(e),loading:e.claiming,class:"claim-button"},{default:c((()=>[m(l,null,{default:c((()=>[m(z(L))])),_:1}),s[38]||(s[38]=p(" 领取 "))])),_:2},1032,["onClick","loading"])):(t=e,t.claimedByUserId===(null==(r=de.value)?void 0:r.id)?(i(),d(ie,{key:1,type:"success",size:"small",class:"claimed-tag"},{default:c((()=>[m(l,null,{default:c((()=>[m(z(F))])),_:1}),s[39]||(s[39]=p(" 已领取 "))])),_:1})):Ya(e)?(i(),d(ie,{key:2,type:"info",size:"small",class:"claimed-tag"},{default:c((()=>[m(l,null,{default:c((()=>[m(z(F))])),_:1}),p(" "+v(e.claimedByUserName||"其他人")+"已领取 ",1)])),_:2},1024)):o("",!0)),m(U,{link:"",size:"small",onClick:a=>Ca(e)},{default:c((()=>[m(l,null,{default:c((()=>[m(z(M))])),_:1})])),_:2},1032,["onClick"]),m(U,{link:"",size:"small",onClick:a=>(async e=>{try{const a=await S.getTaskDetail(e.taskId);a.success&&a.data?(re.value=a.data,ne.value=!0,ia.value=!0):V.error("获取任务详情失败: "+(a.message||"未知错误"))}catch(a){V.error("获取任务详情时发生错误")}})(e)},{default:c((()=>[m(l,null,{default:c((()=>[m(z(P))])),_:1})])),_:2},1032,["onClick"]),m(Ja,{onCommand:a=>za(a,e)},{dropdown:c((()=>[m(Pa,null,{default:c((()=>[m(Fa,{command:"clone"},{default:c((()=>s[40]||(s[40]=[p("克隆任务")]))),_:1}),m(Fa,{command:"assign"},{default:c((()=>s[41]||(s[41]=[p("重新分配")]))),_:1}),"Done"!==e.status?(i(),d(Fa,{key:0,command:"complete"},{default:c((()=>s[42]||(s[42]=[p("标记完成")]))),_:1})):o("",!0),m(Fa,{command:"delete",divided:""},{default:c((()=>s[43]||(s[43]=[p("删除任务")]))),_:1})])),_:2},1024)])),default:c((()=>[m(U,{link:"",size:"small"},{default:c((()=>[m(l,null,{default:c((()=>[m(z(a))])),_:1})])),_:1})])),_:2},1032,["onCommand"])])];var t,r})),_:1})])),_:1},8,["data"])),[[ls,t.value]])])):(i(),n("div",aa,[k("div",sa,[(i(!0),n(h,null,b(pa.value,(e=>(i(),d(X,{key:e.taskId,task:e,selected:ga.value.includes(e.taskId),onSelect:Ia,onClick:Ca,onQuickAction:xa,onStatusChange:Ea},null,8,["task","selected"])))),128))])])),k("div",la,[m(ss,{"current-page":ra.currentPage,"onUpdate:currentPage":s[9]||(s[9]=e=>ra.currentPage=e),"page-size":ra.pageSize,"onUpdate:pageSize":s[10]||(s[10]=e=>ra.pageSize=e),"page-sizes":[10,20,50,100],small:!1,disabled:t.value,background:!0,layout:"total, sizes, prev, pager, next, jumper",total:ma.value.length,onSizeChange:_a,onCurrentChange:Da},null,8,["current-page","page-size","disabled","total"])])])),_:1}),m(G,{modelValue:na.value,"onUpdate:modelValue":s[11]||(s[11]=e=>na.value=e),task:ca.value,onClose:s[12]||(s[12]=e=>na.value=!1),onUpdated:Ba},null,8,["modelValue","task"]),m(J,{modelValue:da.value,"onUpdate:modelValue":s[13]||(s[13]=e=>da.value=e),"task-ids":ga.value,onClose:s[14]||(s[14]=e=>da.value=!1),onAssigned:Aa},null,8,["modelValue","task-ids"]),m(K,{modelValue:oa.value,"onUpdate:modelValue":s[15]||(s[15]=e=>oa.value=e),"task-ids":ga.value,onClose:s[16]||(s[16]=e=>oa.value=!1),onUpdated:La},null,8,["modelValue","task-ids"]),m(W,{visible:ia.value,"onUpdate:visible":s[17]||(s[17]=e=>ia.value=e),isEdit:ne.value,formData:re.value,onClose:Sa,onSubmit:Ka},null,8,["visible","isEdit","formData"]),m(ee,{modelValue:ua.value,"onUpdate:modelValue":s[18]||(s[18]=e=>ua.value=e),onSuccess:e.onTaskClaimed},null,8,["modelValue","onSuccess"])])}}},[["__scopeId","data-v-e00658bf"]]);export{ta as default};
