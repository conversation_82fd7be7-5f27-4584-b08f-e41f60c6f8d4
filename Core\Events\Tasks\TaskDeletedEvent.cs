// File: Core/Events/Tasks/TaskDeletedEvent.cs
// Description: 任务删除时发布的领域事件

using MediatR;
using System;

namespace ItAssetsSystem.Core.Events.Tasks
{
    /// <summary>
    /// 表示一个任务已被删除的事件。
    /// </summary>
    public class TaskDeletedEvent : INotification
    {
        /// <summary>
        /// 任务ID (V2 TaskId, BIGINT)
        /// </summary>
        public long TaskId { get; }

        /// <summary>
        /// 任务名称
        /// </summary>
        public string TaskName { get; }

        /// <summary>
        /// 删除者用户ID (Core User Id, INT)
        /// </summary>
        public int DeleterUserId { get; }

        public TaskDeletedEvent(long taskId, string taskName, int deleterUserId)
        {
            TaskId = taskId;
            TaskName = taskName ?? throw new ArgumentNullException(nameof(taskName));
            DeleterUserId = deleterUserId;
        }
    }
} 