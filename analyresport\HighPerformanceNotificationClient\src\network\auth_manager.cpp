// 认证管理器实现
#include "network/auth_manager.h"
#include "core/config_manager.h"
#include "utils/logger.h"
#include "utils/json_parser.h"
#include "common/constants.h"
#include <windows.h>
#include <wininet.h>
#include <chrono>
#include <algorithm>

#pragma comment(lib, "wininet.lib")

namespace notification {

AuthManager::AuthManager(std::shared_ptr<ConfigManager> config_manager)
    : config_manager_(config_manager)
    , logger_(std::make_shared<Logger>("AuthManager")) {
}

AuthManager::~AuthManager() {
    shutdown();
}

bool AuthManager::initialize() {
    if (!config_manager_) {
        if (logger_) logger_->error("ConfigManager is null");
        return false;
    }

    // 加载配置
    auto auth_config = config_manager_->getAuthConfig();
    auto server_config = config_manager_->getServerConfig();
    
    api_base_url_ = server_config.api_base_url;
    support_anonymous_ = auth_config.support_anonymous;
    token_refresh_threshold_ = auth_config.token_refresh_threshold;

    if (logger_) logger_->info("AuthManager initialized");

    // 如果配置了自动登录且有用户名密码，尝试登录
    if (auth_config.auto_login && !auth_config.username.empty() && !auth_config.password.empty()) {
        if (logger_) logger_->info("Attempting auto-login");
        login(auth_config.username, auth_config.password);
    }
    // 如果有保存的令牌，尝试使用令牌登录
    else if (!auth_config.token.empty()) {
        if (logger_) logger_->info("Attempting token-based login");
        loginWithToken(auth_config.token);
    }
    // 如果支持匿名模式，直接设置为已认证
    else if (support_anonymous_) {
        if (logger_) logger_->info("Using anonymous mode");
        enableAnonymousMode(true);
        setAuthStatus(AuthStatus::AUTHENTICATED);
    }

    return true;
}

void AuthManager::shutdown() {
    stopAutoRefreshWorker();
    logout();
    if (logger_) logger_->info("AuthManager shutdown");
}

bool AuthManager::login(const String& username, const String& password) {
    if (username.empty() || password.empty()) {
        if (logger_) logger_->error("Username or password is empty");
        return false;
    }

    setAuthStatus(AuthStatus::AUTHENTICATING);

    if (performLogin(username, password)) {
        setAuthStatus(AuthStatus::AUTHENTICATED);
        startAutoRefreshWorker();
        if (logger_) logger_->info("Login successful for user: " + username);
        return true;
    } else {
        setAuthStatus(AuthStatus::AUTH_FAILED);
        if (logger_) logger_->error("Login failed for user: " + username);
        return false;
    }
}

bool AuthManager::loginWithToken(const String& token) {
    if (token.empty()) {
        if (logger_) logger_->error("Token is empty");
        return false;
    }

    setAuthStatus(AuthStatus::AUTHENTICATING);

    if (validateToken(token)) {
        // 解析令牌信息
        auto token_info = parseJwtToken(token);
        token_info.access_token = token;
        updateTokenInfo(token_info);
        
        setAuthStatus(AuthStatus::AUTHENTICATED);
        startAutoRefreshWorker();
        if (logger_) logger_->info("Token-based login successful");
        return true;
    } else {
        setAuthStatus(AuthStatus::AUTH_FAILED);
        if (logger_) logger_->error("Token validation failed");
        return false;
    }
}

void AuthManager::logout() {
    stopAutoRefreshWorker();
    clearTokenInfo();
    anonymous_mode_enabled_ = false;
    setAuthStatus(AuthStatus::NOT_AUTHENTICATED);
    if (logger_) logger_->info("User logged out");
}

bool AuthManager::refreshToken() {
    std::lock_guard<std::mutex> lock(token_mutex_);
    
    if (current_token_.refresh_token.empty()) {
        if (logger_) logger_->warn("No refresh token available");
        return false;
    }

    if (performTokenRefresh()) {
        if (logger_) logger_->info("Token refresh successful");
        return true;
    } else {
        if (logger_) logger_->error("Token refresh failed");
        setAuthStatus(AuthStatus::TOKEN_EXPIRED);
        return false;
    }
}

bool AuthManager::isAuthenticated() const {
    if (anonymous_mode_enabled_.load()) {
        return true;
    }
    
    auto status = auth_status_.load();
    return status == AuthStatus::AUTHENTICATED && !isTokenExpired();
}

TokenInfo AuthManager::getTokenInfo() const {
    std::lock_guard<std::mutex> lock(token_mutex_);
    return current_token_;
}

String AuthManager::getCurrentToken() const {
    std::lock_guard<std::mutex> lock(token_mutex_);
    return current_token_.access_token;
}

bool AuthManager::isTokenExpired() const {
    if (anonymous_mode_enabled_.load()) {
        return false;
    }
    
    std::lock_guard<std::mutex> lock(token_mutex_);
    if (current_token_.access_token.empty()) {
        return true;
    }
    
    auto now = std::chrono::steady_clock::now();
    return now >= current_token_.expires_at;
}

Duration AuthManager::getTokenTimeToExpire() const {
    std::lock_guard<std::mutex> lock(token_mutex_);
    if (current_token_.access_token.empty()) {
        return Duration::zero();
    }
    
    auto now = std::chrono::steady_clock::now();
    if (now >= current_token_.expires_at) {
        return Duration::zero();
    }
    
    return std::chrono::duration_cast<Duration>(current_token_.expires_at - now);
}

void AuthManager::enableAutoRefresh(bool enable) {
    auto old_value = auto_refresh_enabled_.exchange(enable);
    if (enable && !old_value && isAuthenticated()) {
        startAutoRefreshWorker();
    } else if (!enable && old_value) {
        stopAutoRefreshWorker();
    }
}

void AuthManager::setAuthStatusCallback(AuthStatusCallback callback) {
    std::lock_guard<std::mutex> lock(callback_mutex_);
    auth_status_callback_ = callback;
}

String AuthManager::getAuthorizationHeader() const {
    if (anonymous_mode_enabled_.load()) {
        return "";
    }
    
    auto token = getCurrentToken();
    if (token.empty()) {
        return "";
    }
    
    return "Bearer " + token;
}

bool AuthManager::supportsAnonymous() const {
    return support_anonymous_;
}

void AuthManager::enableAnonymousMode(bool enable) {
    anonymous_mode_enabled_ = enable;
    if (enable && logger_) {
        logger_->info("Anonymous mode enabled");
    }
}

// 私有方法实现

AuthManager::HttpResponse AuthManager::makeHttpRequest(const String& url, const String& method, 
                                                      const String& data, const String& content_type) {
    HttpResponse response;
    
    HINTERNET hInternet = InternetOpenA("NotificationClient/1.0", INTERNET_OPEN_TYPE_PRECONFIG, nullptr, nullptr, 0);
    if (!hInternet) {
        response.error = "Failed to initialize WinINet";
        return response;
    }

    // 解析URL
    String hostname, path;
    DWORD port = 80;
    bool https = false;
    
    if (url.find("https://") == 0) {
        https = true;
        port = 443;
        size_t start = 8; // "https://"的长度
        size_t slash_pos = url.find('/', start);
        if (slash_pos != String::npos) {
            hostname = url.substr(start, slash_pos - start);
            path = url.substr(slash_pos);
        } else {
            hostname = url.substr(start);
            path = "/";
        }
    } else if (url.find("http://") == 0) {
        size_t start = 7; // "http://"的长度
        size_t slash_pos = url.find('/', start);
        if (slash_pos != String::npos) {
            hostname = url.substr(start, slash_pos - start);
            path = url.substr(slash_pos);
        } else {
            hostname = url.substr(start);
            path = "/";
        }
    }

    // 处理端口
    size_t colon_pos = hostname.find(':');
    if (colon_pos != String::npos) {
        port = std::stoi(hostname.substr(colon_pos + 1));
        hostname = hostname.substr(0, colon_pos);
    }

    HINTERNET hConnect = InternetConnectA(hInternet, hostname.c_str(), port, nullptr, nullptr, 
                                         INTERNET_SERVICE_HTTP, 0, 0);
    if (!hConnect) {
        response.error = "Failed to connect to server";
        InternetCloseHandle(hInternet);
        return response;
    }

    DWORD flags = INTERNET_FLAG_RELOAD | INTERNET_FLAG_NO_CACHE_WRITE;
    if (https) {
        flags |= INTERNET_FLAG_SECURE;
    }

    HINTERNET hRequest = HttpOpenRequestA(hConnect, method.c_str(), path.c_str(), 
                                         nullptr, nullptr, nullptr, flags, 0);
    if (!hRequest) {
        response.error = "Failed to create HTTP request";
        InternetCloseHandle(hConnect);
        InternetCloseHandle(hInternet);
        return response;
    }

    // 设置请求头
    String headers = "Content-Type: " + content_type + "\r\n";
    if (!anonymous_mode_enabled_.load()) {
        String auth_header = getAuthorizationHeader();
        if (!auth_header.empty()) {
            headers += "Authorization: " + auth_header + "\r\n";
        }
    }

    BOOL result = HttpSendRequestA(hRequest, headers.c_str(), headers.length(), 
                                  const_cast<char*>(data.c_str()), data.length());
    
    if (result) {
        // 读取状态码
        char status_code_str[16];
        DWORD status_code_len = sizeof(status_code_str);
        HttpQueryInfoA(hRequest, HTTP_QUERY_STATUS_CODE, status_code_str, &status_code_len, nullptr);
        response.status_code = std::atoi(status_code_str);

        // 读取响应体
        char buffer[4096];
        DWORD bytes_read;
        while (InternetReadFile(hRequest, buffer, sizeof(buffer), &bytes_read) && bytes_read > 0) {
            response.body.append(buffer, bytes_read);
        }
        
        response.success = (response.status_code >= 200 && response.status_code < 300);
    } else {
        response.error = "HTTP request failed";
    }

    InternetCloseHandle(hRequest);
    InternetCloseHandle(hConnect);
    InternetCloseHandle(hInternet);

    return response;
}

bool AuthManager::performLogin(const String& username, const String& password) {
    String login_url = api_base_url_ + constants::API_AUTH_LOGIN;
    String json_data = "{\"username\":\"" + username + "\",\"password\":\"" + password + "\"}";
    
    auto response = makeHttpRequest(login_url, "POST", json_data);
    
    if (response.success) {
        return parseLoginResponse(response.body);
    } else {
        if (logger_) logger_->error("Login request failed: " + response.error);
        return false;
    }
}

bool AuthManager::performTokenRefresh() {
    String refresh_url = api_base_url_ + constants::API_AUTH_REFRESH;
    String json_data = "{\"refresh_token\":\"" + current_token_.refresh_token + "\"}";
    
    auto response = makeHttpRequest(refresh_url, "POST", json_data);
    
    if (response.success) {
        return parseRefreshResponse(response.body);
    } else {
        if (logger_) logger_->error("Token refresh request failed: " + response.error);
        return false;
    }
}

bool AuthManager::validateToken(const String& token) {
    // 简单的令牌格式验证
    return !token.empty() && token.find('.') != String::npos;
}

bool AuthManager::parseLoginResponse(const String& response) {
    try {
        // 简化的JSON解析（生产环境建议使用专业的JSON库）
        String token = extractJsonValue(response, "access_token");
        String refresh_token = extractJsonValue(response, "refresh_token");
        
        if (!token.empty()) {
            TokenInfo token_info = parseJwtToken(token);
            token_info.access_token = token;
            token_info.refresh_token = refresh_token;
            updateTokenInfo(token_info);
            return true;
        }
    } catch (...) {
        if (logger_) logger_->error("Failed to parse login response");
    }
    
    return false;
}

bool AuthManager::parseRefreshResponse(const String& response) {
    return parseLoginResponse(response); // 响应格式通常相同
}

TokenInfo AuthManager::parseJwtToken(const String& token) {
    TokenInfo info;
    
    // 简化的JWT解析（生产环境建议使用专业的JWT库）
    size_t first_dot = token.find('.');
    size_t second_dot = token.find('.', first_dot + 1);
    
    if (first_dot != String::npos && second_dot != String::npos) {
        String payload = token.substr(first_dot + 1, second_dot - first_dot - 1);
        String decoded_payload = base64Decode(payload);
        
        // 解析payload中的信息
        String exp_str = extractJsonValue(decoded_payload, "exp");
        if (!exp_str.empty()) {
            time_t exp_time = std::stoll(exp_str);
            info.expires_at = std::chrono::steady_clock::now() + 
                             std::chrono::seconds(exp_time - time(nullptr));
        }
        
        info.user_id = extractJsonValue(decoded_payload, "sub");
        info.username = extractJsonValue(decoded_payload, "username");
        info.roles = extractJsonValue(decoded_payload, "roles");
    }
    
    return info;
}

void AuthManager::setAuthStatus(AuthStatus status) {
    auto old_status = auth_status_.exchange(status);
    if (old_status != status) {
        std::lock_guard<std::mutex> lock(callback_mutex_);
        if (auth_status_callback_) {
            auth_status_callback_(status);
        }
    }
}

void AuthManager::updateTokenInfo(const TokenInfo& token_info) {
    std::lock_guard<std::mutex> lock(token_mutex_);
    current_token_ = token_info;
}

void AuthManager::clearTokenInfo() {
    std::lock_guard<std::mutex> lock(token_mutex_);
    current_token_ = TokenInfo{};
}

void AuthManager::startAutoRefreshWorker() {
    if (!auto_refresh_enabled_.load()) return;
    
    stopAutoRefreshWorker();
    stop_auto_refresh_ = false;
    auto_refresh_thread_ = std::thread(&AuthManager::autoRefreshWorker, this);
}

void AuthManager::stopAutoRefreshWorker() {
    stop_auto_refresh_ = true;
    if (auto_refresh_thread_.joinable()) {
        auto_refresh_thread_.join();
    }
}

void AuthManager::autoRefreshWorker() {
    while (!stop_auto_refresh_.load()) {
        if (isAuthenticated() && !anonymous_mode_enabled_.load()) {
            auto time_to_expire = getTokenTimeToExpire();
            if (time_to_expire <= token_refresh_threshold_) {
                if (logger_) logger_->info("Token is about to expire, refreshing...");
                refreshToken();
            }
        }
        
        // 每分钟检查一次
        std::this_thread::sleep_for(std::chrono::minutes(1));
    }
}

String AuthManager::extractJsonValue(const String& json, const String& key) {
    // 简化的JSON值提取（生产环境建议使用专业的JSON库）
    String search_key = "\"" + key + "\":";
    size_t key_pos = json.find(search_key);
    if (key_pos == String::npos) {
        return "";
    }
    
    size_t value_start = key_pos + search_key.length();
    while (value_start < json.length() && (json[value_start] == ' ' || json[value_start] == '\t')) {
        value_start++;
    }
    
    if (value_start >= json.length()) {
        return "";
    }
    
    if (json[value_start] == '"') {
        // 字符串值
        size_t value_end = json.find('"', value_start + 1);
        if (value_end != String::npos) {
            return json.substr(value_start + 1, value_end - value_start - 1);
        }
    } else {
        // 数值或布尔值
        size_t value_end = json.find_first_of(",}", value_start);
        if (value_end != String::npos) {
            return json.substr(value_start, value_end - value_start);
        }
    }
    
    return "";
}

bool AuthManager::isValidJson(const String& json) {
    return !json.empty() && (json[0] == '{' || json[0] == '[');
}

String AuthManager::base64Decode(const String& encoded) {
    // 简化的Base64解码实现
    // 生产环境建议使用更完善的实现
    const String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
    String decoded;
    
    int val = 0, valb = -8;
    for (char c : encoded) {
        if (chars.find(c) == String::npos) break;
        val = (val << 6) + chars.find(c);
        valb += 6;
        if (valb >= 0) {
            decoded.push_back(char((val >> valb) & 0xFF));
            valb -= 8;
        }
    }
    
    return decoded;
}

} // namespace notification