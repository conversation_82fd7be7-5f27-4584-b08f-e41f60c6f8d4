// File: Domain/Entities/Tasks/Comment.cs
// Description: 评论实体
#nullable enable
using System;
using System.Collections.Generic;
using ItAssetsSystem.Models.Entities; // Corrected: Assuming User is here

namespace ItAssetsSystem.Domain.Entities.Tasks
{
    /// <summary>
    /// 任务评论表 (V2 - BIGINT PK)
    /// </summary>
    public class Comment
    {
        /// <summary>
        /// 评论主键ID (BIGINT)
        /// </summary>
        public long CommentId { get; set; }

        /// <summary>
        /// 关联的任务ID (BIGINT)
        /// </summary>
        public long TaskId { get; set; }
        public virtual Task Task { get; set; } = null!;

        /// <summary>
        /// 评论用户ID (INT, 关联 users.Id)
        /// </summary>
        public int UserId { get; set; }
        public virtual User User { get; set; } = null!; // Adjust User namespace if needed

        /// <summary>
        /// 父评论ID (BIGINT, 用于回复)
        /// </summary>
        public long? ParentCommentId { get; set; }
        public virtual Comment? ParentComment { get; set; }
        public virtual ICollection<Comment> Replies { get; set; } = new List<Comment>();

        /// <summary>
        /// 评论内容 (支持Markdown或HTML)
        /// </summary>
        public string Content { get; set; } = string.Empty;

        /// <summary>
        /// 评论中@提及的用户ID列表 (存储 INT 用户ID 的 JSON 数组)
        /// </summary>
        public string? MentionedUserIds { get; set; } // Store as JSON string

        /// <summary>
        /// 是否置顶评论 (0:否, 1:是)
        /// </summary>
        public bool IsPinned { get; set; }

        /// <summary>
        /// 是否被编辑过 (0:否, 1:是)
        /// </summary>
        public bool IsEdited { get; set; }

        /// <summary>
        /// 记录创建时间 (替代 CreatedAt)
        /// </summary>
        public DateTime CreationTimestamp { get; set; }

        /// <summary>
        /// 记录最后更新时间 (替代 UpdatedAt)
        /// </summary>
        public DateTime LastUpdatedTimestamp { get; set; }

        // Navigation property for attachments
        public virtual ICollection<Attachment> Attachments { get; set; } = new List<Attachment>();
    }
} 