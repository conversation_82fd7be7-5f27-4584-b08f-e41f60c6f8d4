// UDP极致性能通知客户端
#pragma once
#include <windows.h>
#include <winsock2.h>
#include <ws2tcpip.h>
#include <thread>
#include <atomic>
#include <queue>
#include <mutex>
#include <condition_variable>
#include <chrono>
#include <functional>

#pragma comment(lib, "ws2_32.lib")

// 消息类型
enum class MessageType : uint8_t {
    GAMIFICATION_REWARD = 0x01,
    TASK_UPDATE = 0x02,
    SYSTEM_ALERT = 0x03,
    HEARTBEAT = 0xFF
};

// 消息优先级
enum class Priority : uint8_t {
    LOW = 0,
    NORMAL = 1, 
    HIGH = 2,
    CRITICAL = 3
};

// UDP消息包结构（针对MTU优化）
#pragma pack(push, 1)
struct UDPPacket {
    uint32_t magic;          // 魔术字 0xDEADBEEF
    uint16_t sequence;       // 序列号
    uint8_t type;           // MessageType
    uint8_t priority;       // Priority
    uint32_t timestamp;     // 时间戳（毫秒）
    uint16_t payload_size;  // 负载大小
    char payload[1400];     // 负载数据（确保不超过MTU）
    uint32_t checksum;      // 校验和
};
#pragma pack(pop)

class UDPNotificationClient {
private:
    SOCKET udp_socket;
    sockaddr_in server_addr;
    std::atomic<bool> running;
    std::atomic<uint16_t> sequence_counter;
    
    // 发送队列
    std::queue<UDPPacket> send_queue;
    std::mutex queue_mutex;
    std::condition_variable queue_cv;
    
    // 接收回调
    std::function<void(const UDPPacket&)> message_callback;
    
    // 工作线程
    std::thread send_thread;
    std::thread recv_thread;
    
    // 性能统计
    std::atomic<uint64_t> packets_sent{0};
    std::atomic<uint64_t> packets_received{0};
    std::atomic<uint64_t> bytes_sent{0};
    
public:
    UDPNotificationClient();
    ~UDPNotificationClient();
    
    // 连接到服务器
    bool connect(const std::string& server_ip, int port);
    
    // 断开连接
    void disconnect();
    
    // 发送消息（异步）
    void sendMessage(MessageType type, Priority priority, const void* data, size_t size);
    
    // 发送消息（同步，等待确认）
    bool sendReliable(MessageType type, const void* data, size_t size, int timeout_ms = 1000);
    
    // 设置消息接收回调
    void setMessageCallback(std::function<void(const UDPPacket&)> callback);
    
    // 获取性能统计
    struct Stats {
        uint64_t packets_sent;
        uint64_t packets_received;
        uint64_t bytes_sent;
        double send_rate_pps;  // 每秒包数
        double send_rate_bps;  // 每秒字节数
    };
    Stats getStats() const;
    
private:
    // 发送线程函数
    void sendWorker();
    
    // 接收线程函数  
    void receiveWorker();
    
    // 计算校验和
    uint32_t calculateChecksum(const void* data, size_t size);
    
    // 验证数据包
    bool validatePacket(const UDPPacket& packet);
    
    // 创建数据包
    UDPPacket createPacket(MessageType type, Priority priority, const void* data, size_t size);
};