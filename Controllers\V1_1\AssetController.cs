using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System.Threading.Tasks;
using ItAssetsSystem.Core.Interfaces.Services;
using ItAssetsSystem.Services.Interfaces;
using ItAssetsSystem.Models.Entities.Gamification;
using ItAssetsSystem.Core.Abstractions;

namespace ItAssetsSystem.Controllers.V1_1
{
    /// <summary>
    /// 资产控制器 V1.1 - 使用新的服务接口
    /// 与现有V1控制器并行部署，确保向后兼容
    /// </summary>
    [ApiController]
    [Route("api/v1.1/[controller]")]
    [Authorize]
    public class AssetController : ControllerBase
    {
        private readonly IAssetService _assetService;
        private readonly ILogger<AssetController> _logger;
        private readonly IUniversalGamificationService _universalGamificationService;
        private readonly ICurrentUserService _currentUserService;

        public AssetController(
            IAssetService assetService,
            ILogger<AssetController> logger,
            IUniversalGamificationService universalGamificationService,
            ICurrentUserService currentUserService)
        {
            _assetService = assetService;
            _logger = logger;
            _universalGamificationService = universalGamificationService;
            _currentUserService = currentUserService;
        }

        /// <summary>
        /// 分页获取资产列表 - 使用新的服务接口
        /// </summary>
        /// <param name="pageIndex">页码</param>
        /// <param name="pageSize">页大小</param>
        /// <param name="keyword">关键词</param>
        /// <param name="assetCode">资产编码</param>
        /// <param name="assetTypeId">资产类型ID</param>
        /// <param name="locationId">位置ID</param>
        /// <param name="status">状态</param>
        /// <returns>分页结果</returns>
        [HttpGet]
        public async Task<IActionResult> GetAssets(
            [FromQuery] int pageIndex = 1,
            [FromQuery] int pageSize = 20,
            [FromQuery] string keyword = null,
            [FromQuery] string assetCode = null,
            [FromQuery] int? assetTypeId = null,
            [FromQuery] int? locationId = null,
            [FromQuery] int? status = null)
        {
            _logger.LogInformation("V1.1 获取资产列表: pageIndex={PageIndex}, pageSize={PageSize}", 
                pageIndex, pageSize);

            try
            {
                var query = new AssetQueryDto
                {
                    PageIndex = pageIndex,
                    PageSize = pageSize,
                    Keyword = keyword,
                    AssetTypeId = assetTypeId,
                    LocationId = locationId,
                    Status = status?.ToString() // 转换为字符串
                };

                var result = await _assetService.GetPagedAsync(query);

                return Ok(new
                {
                    success = true,
                    data = new
                    {
                        items = result.Items,
                        totalCount = result.TotalCount,
                        pageIndex = result.PageIndex,
                        pageSize = result.PageSize,
                        totalPages = result.TotalPages
                    },
                    message = "获取资产列表成功",
                    version = "v1.1"
                });
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "V1.1 获取资产列表异常");
                return StatusCode(500, new
                {
                    success = false,
                    message = "获取资产列表失败",
                    version = "v1.1"
                });
            }
        }

        /// <summary>
        /// 根据ID获取资产信息
        /// </summary>
        /// <param name="id">资产ID</param>
        /// <returns>资产信息</returns>
        [HttpGet("{id}")]
        public async Task<IActionResult> GetAsset(int id)
        {
            _logger.LogInformation("V1.1 获取资产信息: {AssetId}", id);

            try
            {
                var asset = await _assetService.GetByIdAsync(id);
                if (asset == null)
                {
                    return NotFound(new
                    {
                        success = false,
                        message = "资产不存在",
                        version = "v1.1"
                    });
                }

                return Ok(new
                {
                    success = true,
                    data = asset,
                    message = "获取资产信息成功",
                    version = "v1.1"
                });
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "V1.1 获取资产信息异常: {AssetId}", id);
                return StatusCode(500, new
                {
                    success = false,
                    message = "获取资产信息失败",
                    version = "v1.1"
                });
            }
        }

        /// <summary>
        /// 创建资产
        /// </summary>
        /// <param name="dto">创建资产DTO</param>
        /// <returns>创建结果</returns>
        [HttpPost]
        public async Task<IActionResult> CreateAsset([FromBody] CreateAssetDto dto)
        {
            _logger.LogInformation("V1.1 创建资产: {AssetCode}", dto.AssetCode);

            try
            {
                var asset = await _assetService.CreateAsync(dto);

                // 触发资产创建奖励
                try
                {
                    await _universalGamificationService.TriggerBehaviorRewardAsync(
                        _currentUserService.UserId,
                        BehaviorCodes.ASSET_CREATED,
                        asset.Id,
                        context: new {
                            AssetCode = dto.AssetCode,
                            AssetName = dto.Name,
                            AssetTypeId = dto.AssetTypeId
                        },
                        description: $"创建资产: {dto.Name}"
                    );
                }
                catch (System.Exception ex)
                {
                    _logger.LogWarning(ex, "触发资产创建奖励失败: AssetId={AssetId}", asset.Id);
                }

                return Ok(new
                {
                    success = true,
                    data = asset,
                    message = "资产创建成功",
                    version = "v1.1"
                });
            }
            catch (System.InvalidOperationException ex)
            {
                _logger.LogWarning("V1.1 创建资产业务异常: {Message}", ex.Message);
                return BadRequest(new
                {
                    success = false,
                    message = ex.Message,
                    version = "v1.1"
                });
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "V1.1 创建资产异常: {AssetCode}", dto.AssetCode);
                return StatusCode(500, new
                {
                    success = false,
                    message = "创建资产失败",
                    version = "v1.1"
                });
            }
        }

        /// <summary>
        /// 更新资产
        /// </summary>
        /// <param name="id">资产ID</param>
        /// <param name="dto">更新资产DTO</param>
        /// <returns>更新结果</returns>
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateAsset(int id, [FromBody] UpdateAssetDto dto)
        {
            _logger.LogInformation("V1.1 更新资产: {AssetId}", id);

            try
            {
                var asset = await _assetService.UpdateAsync(id, dto);

                // 触发资产更新奖励
                try
                {
                    await _universalGamificationService.TriggerBehaviorRewardAsync(
                        _currentUserService.UserId,
                        BehaviorCodes.ASSET_UPDATED,
                        id,
                        context: new {
                            AssetName = dto.Name,
                            AssetTypeId = dto.AssetTypeId
                        },
                        description: $"更新资产: {dto.Name}"
                    );
                }
                catch (System.Exception ex)
                {
                    _logger.LogWarning(ex, "触发资产更新奖励失败: AssetId={AssetId}", id);
                }

                return Ok(new
                {
                    success = true,
                    data = asset,
                    message = "资产更新成功",
                    version = "v1.1"
                });
            }
            catch (System.InvalidOperationException ex)
            {
                _logger.LogWarning("V1.1 更新资产业务异常: {Message}", ex.Message);
                return BadRequest(new
                {
                    success = false,
                    message = ex.Message,
                    version = "v1.1"
                });
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "V1.1 更新资产异常: {AssetId}", id);
                return StatusCode(500, new
                {
                    success = false,
                    message = "更新资产失败",
                    version = "v1.1"
                });
            }
        }

        /// <summary>
        /// 删除资产
        /// </summary>
        /// <param name="id">资产ID</param>
        /// <returns>删除结果</returns>
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteAsset(int id)
        {
            _logger.LogInformation("V1.1 删除资产: {AssetId}", id);

            try
            {
                var success = await _assetService.DeleteAsync(id);
                if (!success)
                {
                    return NotFound(new
                    {
                        success = false,
                        message = "资产不存在",
                        version = "v1.1"
                    });
                }

                return Ok(new
                {
                    success = true,
                    message = "资产删除成功",
                    version = "v1.1"
                });
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "V1.1 删除资产异常: {AssetId}", id);
                return StatusCode(500, new
                {
                    success = false,
                    message = "删除资产失败",
                    version = "v1.1"
                });
            }
        }

        /// <summary>
        /// 健康检查端点
        /// </summary>
        /// <returns>服务状态</returns>
        [HttpGet("health")]
        [AllowAnonymous]
        public IActionResult Health()
        {
            return Ok(new
            {
                status = "healthy",
                version = "v1.1",
                service = "AssetService",
                timestamp = System.DateTime.UtcNow
            });
        }
    }
}
