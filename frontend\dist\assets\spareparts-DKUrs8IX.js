import{aV as t}from"./index-CG5lHOPO.js";function r(r){return t({url:"/v2/spareparttype",method:"get",params:r})}function e(r){return t({url:"/v2/spareparttype/tree",method:"get",params:r})}function n(r){return t({url:"/v2/spareparttype",method:"post",data:r})}function a(r,e){return t({url:`/v2/spareparttype/${r}`,method:"put",data:e})}function u(r){return t({url:`/v2/spareparttype/${r}`,method:"delete"})}function o(r){return t({url:"/v2/sparepartlocation",method:"get",params:r})}function p(r){return t({url:"/v2/sparepartlocation",method:"post",data:r})}function s(r,e){return t({url:`/v2/sparepartlocation/${r}`,method:"put",data:e})}function i(r){return t({url:`/v2/sparepartlocation/${r}`,method:"delete"})}function d(r={}){return t({url:"/v2/spare-parts",method:"get",params:r})}function m(r){return t({url:`/v2/spare-parts/${r}`,method:"get"})}function l(r){return t({url:"/v2/spare-parts",method:"post",data:r})}function c(r,e){return t({url:`/v2/spare-parts/${r}`,method:"put",data:e})}function v(r){return t({url:`/v2/spare-parts/${r}`,method:"delete"})}function f(r=""){return t({url:"/v2/spare-parts/simple",method:"get",params:{keyword:r}})}function h(r={}){return t({url:"/v2/spare-part-transactions",method:"get",params:r})}function g(r){return t({url:"/v2/spare-parts/transactions/in",method:"post",data:r})}function y(r){return t({url:"/v2/spare-parts/transactions/out",method:"post",data:r})}function $(r){return t({url:"/v2/spare-parts/transactions/adjust",method:"post",data:r})}function j(r){return t({url:"/v2/spare-part-inventory/status/adjust",method:"post",data:r})}function k(r){return t({url:"/v2/repair-orders",method:"get",params:r})}function x(r){return t({url:"/v2/repair-orders",method:"post",data:r})}function b(r,e){return t({url:`/v2/repair-orders/${r}`,method:"put",data:e})}function w(r){return t({url:"/v2/suppliers",method:"get",params:r})}function V(r){return t({url:"/v2/suppliers",method:"post",data:r})}function q(r,e){return t({url:`/v2/suppliers/${r}`,method:"put",data:e})}function z(r){return t({url:`/v2/suppliers/${r}`,method:"delete"})}function A(){return t({url:"/v2/suppliers/maintenance",method:"get"})}function B(){return t({url:"/v2/suppliers/procurement",method:"get"})}function C(){return t({url:"/v2/spare-parts/area-stats",method:"get"})}function D(r){return t({url:`/v2/spare-part-inventory/${r}/stock-summary`,method:"get"})}function E(r,e={}){return t({url:`/v2/spare-part-inventory/${r}/inventories`,method:"get",params:e})}function F(){return t({url:"/v2/spare-part-inventory/status-types",method:"get"})}function G(r){return t({url:"/v2/spare-part-inventory/status/batch-update",method:"post",data:r})}export{j as adjustSparePartStatus,G as batchUpdateSparePartStatus,x as createRepairOrder,l as createSparePart,p as createSparePartLocation,n as createSparePartType,V as createSupplier,v as deleteSparePart,i as deleteSparePartLocation,u as deleteSparePartType,z as deleteSupplier,A as getMaintenanceSuppliers,B as getProcurementSuppliers,k as getRepairOrders,m as getSparePart,C as getSparePartAreaStats,E as getSparePartInventories,o as getSparePartLocations,F as getSparePartStatusTypes,D as getSparePartStockSummary,h as getSparePartTransactions,r as getSparePartTypes,e as getSparePartTypesTree,d as getSpareParts,f as getSparePartsSimple,w as getSuppliers,g as sparePartInbound,y as sparePartOutbound,$ as stockAdjustment,b as updateRepairOrder,c as updateSparePart,s as updateSparePartLocation,a as updateSparePartType,q as updateSupplier};
