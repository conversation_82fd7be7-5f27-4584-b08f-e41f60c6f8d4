-- ================================================================================
-- IT资产管理系统 - 数据库优化脚本 (Step 3)
-- 目标：50%+ 效率提升，支持现代任务管理模块
-- 创建时间：2025-01-26
-- ================================================================================

-- 启用查询缓存和性能监控
SET SESSION query_cache_type = ON;
SET SESSION query_cache_size = 67108864; -- 64MB

-- ================================================================================
-- 1. 核心任务表索引优化 (V2 - BIGINT主键架构)
-- ================================================================================

-- 1.1 任务表 (tasks) 性能索引
ALTER TABLE `tasks` 
ADD INDEX `idx_tasks_status_priority_assignee` (`Status`, `Priority`, `AssigneeUserId`),
ADD INDEX `idx_tasks_plan_dates` (`PlanStartDate`, `PlanEndDate`),
ADD INDEX `idx_tasks_actual_dates` (`ActualStartDate`, `ActualEndDate`),
ADD INDEX `idx_tasks_creation_time` (`CreationTimestamp` DESC),
ADD INDEX `idx_tasks_overdue_tasks` (`Status`, `PlanEndDate`, `IsOverdueAcknowledged`),
ADD INDEX `idx_tasks_pdca_stage` (`TaskType`, `PDCAStage`),
ADD INDEX `idx_tasks_periodic_schedule` (`PeriodicTaskScheduleId`, `Status`),
ADD INDEX `idx_tasks_progress_tracking` (`Progress`, `Status`, `AssigneeUserId`),
ADD INDEX `idx_tasks_points_gamification` (`Points`, `Status`);

-- 1.2 评论表 (comments) 高频查询优化
ALTER TABLE `comments`
ADD INDEX `idx_comments_task_creation_desc` (`TaskId`, `CreationTimestamp` DESC),
ADD INDEX `idx_comments_user_recent` (`UserId`, `CreationTimestamp` DESC),
ADD INDEX `idx_comments_pinned_first` (`TaskId`, `IsPinned` DESC, `CreationTimestamp` ASC),
ADD INDEX `idx_comments_parent_child` (`ParentCommentId`, `CreationTimestamp` ASC);

-- 1.3 附件表 (attachments) 文件管理优化
ALTER TABLE `attachments`
ADD INDEX `idx_attachments_task_creation` (`TaskId`, `CreationTimestamp` DESC),
ADD INDEX `idx_attachments_file_type` (`FileType`, `IsPreviewable`),
ADD INDEX `idx_attachments_storage_type` (`StorageType`, `CreationTimestamp` DESC),
ADD INDEX `idx_attachments_uploader_recent` (`UploaderUserId`, `CreationTimestamp` DESC);

-- 1.4 任务分配表 (taskassignees) 团队协作优化
ALTER TABLE `taskassignees`
ADD INDEX `idx_taskassignees_user_assignment_time` (`UserId`, `AssignmentTimestamp` DESC),
ADD INDEX `idx_taskassignees_task_type_time` (`TaskId`, `AssignmentType`, `AssignmentTimestamp`),
ADD INDEX `idx_taskassignees_assignedby_stats` (`AssignedByUserId`, `AssignmentTimestamp` DESC);

-- 1.5 任务历史表 (taskhistory) 审计跟踪优化
ALTER TABLE `taskhistory`
ADD INDEX `idx_taskhistory_task_action_time` (`TaskId`, `ActionType`, `Timestamp` DESC),
ADD INDEX `idx_taskhistory_user_activity` (`UserId`, `Timestamp` DESC),
ADD INDEX `idx_taskhistory_field_changes` (`FieldName`, `Timestamp` DESC),
ADD INDEX `idx_taskhistory_recent_activities` (`Timestamp` DESC, `ActionType`);

-- 1.6 周期任务计划表 (periodictaskschedules) 调度优化
ALTER TABLE `periodictaskschedules`
ADD INDEX `idx_periodic_next_generation` (`status`, `next_generation_time`),
ADD INDEX `idx_periodic_creator_active` (`creator_user_id`, `status`),
ADD INDEX `idx_periodic_template_schedule` (`template_task_id`, `status`),
ADD INDEX `idx_periodic_occurrence_tracking` (`occurrences_generated`, `total_occurrences`);

-- ================================================================================
-- 2. 传统模块索引优化 (V1 - INT主键架构)
-- ================================================================================

-- 2.1 资产表 (assets) 性能提升
ALTER TABLE `assets`
ADD INDEX `idx_assets_status_location` (`Status`, `LocationId`),
ADD INDEX `idx_assets_type_department` (`AssetTypeId`, `DepartmentId`),
ADD INDEX `idx_assets_purchase_warranty` (`PurchaseDate`, `WarrantyExpireDate`),
ADD INDEX `idx_assets_search_optimization` (`Name`, `Model`, `Brand`),
ADD INDEX `idx_assets_code_search` (`assetCode`, `FinancialCode`);

-- 2.2 位置表 (locations) 层级查询优化
-- 注意：需要根据实际表结构调整
-- ALTER TABLE `locations`
-- ADD INDEX `idx_locations_hierarchy` (`ParentId`, `Name`),
-- ADD INDEX `idx_locations_active_status` (`IsActive`, `Name`);

-- 2.3 用户表 (users) 认证和权限优化
ALTER TABLE `users`
ADD INDEX `idx_users_login_optimization` (`Username`, `IsActive`),
ADD INDEX `idx_users_email_unique` (`Email`),
ADD INDEX `idx_users_department_role` (`DepartmentId`, `IsActive`),
ADD INDEX `idx_users_last_login` (`LastLoginTime` DESC);

-- ================================================================================
-- 3. QuickMemos 备忘录系统优化
-- ================================================================================

-- 3.1 快速备忘录表优化
ALTER TABLE `quick_memos`
ADD INDEX `idx_quickmemos_user_updated_pinned` (`user_id`, `updated_at` DESC, `is_pinned`),
ADD INDEX `idx_quickmemos_category_updated` (`category_id`, `updated_at` DESC),
ADD INDEX `idx_quickmemos_position_zindex` (`user_id`, `PositionX`, `PositionY`, `ZIndex`),
ADD INDEX `idx_quickmemos_search_content` (`user_id`, `title`, `content`(100));

-- 3.2 备忘录分类表优化
ALTER TABLE `quick_memo_categories`
ADD INDEX `idx_quickmemo_categories_user_name` (`user_id`, `name`);

-- ================================================================================
-- 4. 系统性能表优化
-- ================================================================================

-- 4.1 审计日志表 (auditlogs) 大数据量优化
ALTER TABLE `auditlogs`
ADD INDEX `idx_auditlogs_datetime_type` (`DateTime` DESC, `Type`),
ADD INDEX `idx_auditlogs_user_recent` (`UserId`, `DateTime` DESC),
ADD INDEX `idx_auditlogs_table_action` (`TableName`, `Action`, `DateTime` DESC),
ADD INDEX `idx_auditlogs_cleanup` (`DateTime`); -- 用于定期清理

-- 4.2 用户行为表 (user_actions) 积分系统优化
ALTER TABLE `user_actions`
ADD INDEX `idx_user_actions_user_time_points` (`user_id`, `action_time` DESC, `points`),
ADD INDEX `idx_user_actions_type_time` (`action`, `action_time` DESC),
ADD INDEX `idx_user_actions_reference` (`reference_type`, `reference_id`, `action_time` DESC);

-- ================================================================================
-- 5. 全文搜索优化
-- ================================================================================

-- 5.1 为任务表添加全文搜索索引
ALTER TABLE `tasks` ADD FULLTEXT INDEX `ft_tasks_search` (`Name`, `Description`);

-- 5.2 为资产表添加全文搜索索引  
ALTER TABLE `assets` ADD FULLTEXT INDEX `ft_assets_search` (`Name`, `Model`, `Brand`, `SerialNumber`);

-- 5.3 为评论添加全文搜索
ALTER TABLE `comments` ADD FULLTEXT INDEX `ft_comments_content` (`Content`);

-- ================================================================================
-- 6. 分区表优化 (大数据量场景)
-- ================================================================================

-- 6.1 审计日志表按月分区 (如果数据量大)
-- ALTER TABLE `auditlogs` 
-- PARTITION BY RANGE (YEAR(`DateTime`) * 100 + MONTH(`DateTime`)) (
--     PARTITION p202501 VALUES LESS THAN (202502),
--     PARTITION p202502 VALUES LESS THAN (202503),
--     PARTITION p202503 VALUES LESS THAN (202504),
--     PARTITION p202504 VALUES LESS THAN (202505),
--     PARTITION p202505 VALUES LESS THAN (202506),
--     PARTITION p202506 VALUES LESS THAN (202507),
--     PARTITION pmax VALUES LESS THAN MAXVALUE
-- );

-- 6.2 任务历史表按季度分区 (如果需要)
-- ALTER TABLE `taskhistory`
-- PARTITION BY RANGE (YEAR(`Timestamp`) * 100 + QUARTER(`Timestamp`)) (
--     PARTITION p20251 VALUES LESS THAN (20252),
--     PARTITION p20252 VALUES LESS THAN (20253),
--     PARTITION p20253 VALUES LESS THAN (20254),
--     PARTITION p20254 VALUES LESS THAN (20261),
--     PARTITION pmax VALUES LESS THAN MAXVALUE
-- );

-- ================================================================================
-- 7. 视图优化 (常用查询)
-- ================================================================================

-- 7.1 任务概览视图 - 仪表板用
CREATE OR REPLACE VIEW `v_task_overview` AS
SELECT 
    t.TaskId,
    t.Name AS TaskName,
    t.Status,
    t.Priority,
    t.Progress,
    t.PlanEndDate,
    CASE WHEN t.PlanEndDate < NOW() AND t.Status != 'Done' THEN 1 ELSE 0 END AS IsOverdue,
    u.Username AS AssigneeName,
    u.Email AS AssigneeEmail,
    a.Name AS AssetName,
    l.Name AS LocationName,
    t.CreationTimestamp,
    t.Points
FROM tasks t
LEFT JOIN users u ON t.AssigneeUserId = u.Id
LEFT JOIN assets a ON t.AssetId = a.Id
LEFT JOIN locations l ON t.LocationId = l.Id
WHERE t.TaskId IS NOT NULL;

-- 7.2 用户工作负载视图 - 智能分配用
CREATE OR REPLACE VIEW `v_user_workload` AS
SELECT 
    u.Id AS UserId,
    u.Username,
    u.Email,
    COUNT(CASE WHEN t.Status != 'Done' THEN 1 END) AS ActiveTaskCount,
    COUNT(CASE WHEN t.Status = 'Done' THEN 1 END) AS CompletedTaskCount,
    AVG(t.Progress) AS AvgProgress,
    SUM(CASE WHEN t.PlanEndDate < NOW() AND t.Status != 'Done' THEN 1 ELSE 0 END) AS OverdueCount,
    SUM(t.Points) AS TotalPoints
FROM users u
LEFT JOIN tasks t ON u.Id = t.AssigneeUserId
WHERE u.IsActive = 1
GROUP BY u.Id, u.Username, u.Email;

-- 7.3 团队效率视图 - 分析用
CREATE OR REPLACE VIEW `v_team_efficiency` AS
SELECT 
    d.Id AS DepartmentId,
    d.Name AS DepartmentName,
    COUNT(DISTINCT u.Id) AS TeamSize,
    COUNT(t.TaskId) AS TotalTasks,
    COUNT(CASE WHEN t.Status = 'Done' THEN 1 END) AS CompletedTasks,
    ROUND(COUNT(CASE WHEN t.Status = 'Done' THEN 1 END) * 100.0 / NULLIF(COUNT(t.TaskId), 0), 2) AS CompletionRate,
    AVG(DATEDIFF(t.ActualEndDate, t.ActualStartDate)) AS AvgCompletionDays,
    SUM(t.Points) AS TotalPoints
FROM departments d
LEFT JOIN users u ON d.Id = u.DepartmentId
LEFT JOIN tasks t ON u.Id = t.AssigneeUserId
WHERE d.Id IS NOT NULL
GROUP BY d.Id, d.Name;

-- ================================================================================
-- 8. 存储过程优化 (高频操作)
-- ================================================================================

-- 8.1 批量任务状态更新存储过程
DELIMITER //
CREATE PROCEDURE `sp_batch_update_task_status`(
    IN task_ids TEXT,
    IN new_status VARCHAR(50),
    IN operator_user_id INT,
    IN remarks TEXT
)
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE task_id BIGINT;
    DECLARE task_cursor CURSOR FOR 
        SELECT CAST(TRIM(value) AS UNSIGNED) 
        FROM JSON_TABLE(CONCAT('[', REPLACE(task_ids, ',', '","'), ']'), '$[*]' COLUMNS (value VARCHAR(255) PATH '$')) AS jt;
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;

    START TRANSACTION;
    
    OPEN task_cursor;
    read_loop: LOOP
        FETCH task_cursor INTO task_id;
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        -- 更新任务状态
        UPDATE tasks 
        SET Status = new_status, 
            LastUpdatedTimestamp = NOW(),
            ActualEndDate = CASE WHEN new_status = 'Done' THEN NOW() ELSE ActualEndDate END
        WHERE TaskId = task_id;
        
        -- 记录历史
        INSERT INTO taskhistory (TaskId, UserId, Timestamp, ActionType, FieldName, OldValue, NewValue, Description)
        SELECT TaskId, operator_user_id, NOW(), 'StatusUpdate', 'Status', 
               (SELECT Status FROM tasks WHERE TaskId = task_id), new_status, 
               CONCAT('批量状态更新: ', IFNULL(remarks, ''))
        FROM tasks WHERE TaskId = task_id;
        
    END LOOP;
    CLOSE task_cursor;
    
    COMMIT;
END//
DELIMITER ;

-- 8.2 智能任务分配存储过程
DELIMITER //
CREATE PROCEDURE `sp_smart_task_assignment`(
    IN task_ids TEXT,
    IN assignment_strategy VARCHAR(50), -- 'balanced', 'performance', 'random'
    IN department_id INT,
    IN operator_user_id INT
)
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE task_id BIGINT;
    DECLARE assigned_user_id INT;
    DECLARE task_cursor CURSOR FOR 
        SELECT CAST(TRIM(value) AS UNSIGNED) 
        FROM JSON_TABLE(CONCAT('[', REPLACE(task_ids, ',', '","'), ']'), '$[*]' COLUMNS (value VARCHAR(255) PATH '$')) AS jt;
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;

    START TRANSACTION;
    
    OPEN task_cursor;
    read_loop: LOOP
        FETCH task_cursor INTO task_id;
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        -- 根据策略选择分配用户
        CASE assignment_strategy
            WHEN 'balanced' THEN
                SELECT Id INTO assigned_user_id 
                FROM v_user_workload 
                WHERE UserId IN (SELECT Id FROM users WHERE DepartmentId = department_id OR department_id IS NULL)
                ORDER BY ActiveTaskCount ASC, AvgProgress DESC 
                LIMIT 1;
                
            WHEN 'performance' THEN
                SELECT Id INTO assigned_user_id
                FROM v_user_workload
                WHERE UserId IN (SELECT Id FROM users WHERE DepartmentId = department_id OR department_id IS NULL)
                ORDER BY CompletionRate DESC, TotalPoints DESC
                LIMIT 1;
                
            ELSE -- random
                SELECT Id INTO assigned_user_id
                FROM users 
                WHERE DepartmentId = department_id OR department_id IS NULL
                ORDER BY RAND()
                LIMIT 1;
        END CASE;
        
        -- 更新任务分配
        UPDATE tasks 
        SET AssigneeUserId = assigned_user_id,
            LastUpdatedTimestamp = NOW()
        WHERE TaskId = task_id;
        
        -- 记录分配历史
        INSERT INTO taskhistory (TaskId, UserId, Timestamp, ActionType, FieldName, NewValue, Description)
        VALUES (task_id, operator_user_id, NOW(), 'Assignment', 'AssigneeUserId', 
                assigned_user_id, CONCAT('智能分配策略: ', assignment_strategy));
        
        -- 记录分配关系
        INSERT INTO taskassignees (TaskId, UserId, AssignmentType, AssignedByUserId, AssignmentTimestamp)
        VALUES (task_id, assigned_user_id, 'Responsible', operator_user_id, NOW())
        ON DUPLICATE KEY UPDATE 
            AssignedByUserId = operator_user_id,
            AssignmentTimestamp = NOW();
            
    END LOOP;
    CLOSE task_cursor;
    
    COMMIT;
END//
DELIMITER ;

-- ================================================================================
-- 9. 数据库配置优化
-- ================================================================================

-- 9.1 MyISAM/InnoDB 配置优化建议
-- 在 my.cnf 中添加以下配置：
/*
[mysqld]
# InnoDB 优化
innodb_buffer_pool_size = 1G          # 根据服务器内存调整
innodb_log_file_size = 256M
innodb_log_buffer_size = 16M
innodb_flush_log_at_trx_commit = 2
innodb_file_per_table = ON
innodb_buffer_pool_instances = 8

# 查询缓存
query_cache_size = 128M
query_cache_type = ON
query_cache_limit = 4M

# 连接优化
max_connections = 200
max_connect_errors = 10000
wait_timeout = 600
interactive_timeout = 600

# 临时表优化
tmp_table_size = 64M
max_heap_table_size = 64M

# 排序优化
sort_buffer_size = 4M
read_buffer_size = 2M
read_rnd_buffer_size = 8M
*/

-- ================================================================================
-- 10. 清理和维护脚本
-- ================================================================================

-- 10.1 定期清理审计日志 (保留6个月)
CREATE EVENT IF NOT EXISTS `ev_cleanup_audit_logs`
ON SCHEDULE EVERY 1 WEEK
STARTS CURRENT_TIMESTAMP
DO
  DELETE FROM auditlogs WHERE DateTime < DATE_SUB(NOW(), INTERVAL 6 MONTH);

-- 10.2 优化表碎片整理
CREATE EVENT IF NOT EXISTS `ev_optimize_tables`
ON SCHEDULE EVERY 1 MONTH
STARTS CURRENT_TIMESTAMP
DO
BEGIN
    OPTIMIZE TABLE tasks;
    OPTIMIZE TABLE comments;
    OPTIMIZE TABLE attachments;
    OPTIMIZE TABLE taskhistory;
    OPTIMIZE TABLE auditlogs;
    OPTIMIZE TABLE user_actions;
END;

-- ================================================================================
-- 11. 性能监控查询
-- ================================================================================

-- 11.1 慢查询监控
SELECT 
    'tasks' AS table_name,
    COUNT(*) AS total_records,
    AVG(CHAR_LENGTH(Description)) AS avg_description_length,
    COUNT(CASE WHEN Status = 'Done' THEN 1 END) AS completed_tasks,
    COUNT(CASE WHEN PlanEndDate < NOW() AND Status != 'Done' THEN 1 END) AS overdue_tasks
FROM tasks
UNION ALL
SELECT 
    'comments' AS table_name,
    COUNT(*) AS total_records,
    AVG(CHAR_LENGTH(Content)) AS avg_content_length,
    COUNT(CASE WHEN IsPinned = 1 THEN 1 END) AS pinned_comments,
    0 AS overdue_tasks
FROM comments;

-- 11.2 索引使用率检查
SELECT 
    TABLE_NAME,
    INDEX_NAME,
    CARDINALITY,
    NULLABLE,
    INDEX_TYPE
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME IN ('tasks', 'comments', 'attachments', 'taskhistory', 'taskassignees')
ORDER BY TABLE_NAME, INDEX_NAME;

-- ================================================================================
-- 执行完成提示
-- ================================================================================
SELECT '数据库优化脚本执行完成！预计性能提升50%+' AS optimization_status,
       NOW() AS completion_time;