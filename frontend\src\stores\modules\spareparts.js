/**
 * 备品备件管理模块状态管理
 * 文件路径: src/stores/modules/spareparts.js
 * 功能描述: 管理备品备件相关的状态和操作
 */

import { defineStore } from 'pinia';
import { ElMessage } from 'element-plus';
import * as sparePartsApi from '@/api/spareparts';

export const useSparePartsStore = defineStore('spareparts', {
  // 状态
  state: () => ({
    // 通用状态
    loading: false,
    formLoading: false,
    
    // 备件类型相关
    types: [],
    typesTree: [],
    typesLoading: false,
    
    // 库位相关
    locations: [],
    locationsLoading: false,
    
    // 备件相关
    spareParts: [],
    sparePartsTotal: 0,
    sparePartsLoading: false,
    currentSparePart: null,
    
    // 出入库相关
    transactions: [],
    transactionsTotal: 0,
    transactionsLoading: false,
    
    // 区域数据
    areas: []
  }),
  
  // 计算属性
  getters: {
    // 获取备件类型的选项，用于下拉菜单
    typeOptions: (state) => {
      return state.types.map(type => ({
        label: type.name,
        value: type.id
      }));
    },
    
    // 获取备件库位的选项，用于下拉菜单
    locationOptions: (state) => {
      return state.locations.map(location => ({
        label: `${location.name} (${location.area || '未知区域'})`,
        value: location.id
      }));
    },
    
    // 获取特定区域下的库位选项
    locationOptionsByArea: (state) => (area) => {
      return state.locations
        .filter(location => location.area === area)
        .map(location => ({
          label: location.name,
          value: location.id
        }));
    },
    
    // 区域选项
    areaOptions: (state) => {
      return state.areas.map(area => ({
        label: area,
        value: area
      }));
    },
    
    // 低库存备件数量
    lowStockCount: (state) => {
      return state.spareParts.filter(part => part.quantity <= part.min_threshold).length;
    },
    
    // 预警库存备件数量
    warningStockCount: (state) => {
      return state.spareParts.filter(part => part.quantity > part.min_threshold && part.quantity <= part.warning_threshold).length;
    }
  },
  
  // 操作方法
  actions: {
    // 初始化模块，加载通用数据
    async initializeStore() {
      try {
        await Promise.all([
          this.fetchTypes(),
          this.fetchLocations(),
          this.fetchAreas()
        ]);
      } catch (error) {
        console.error('初始化备品备件模块失败:', error);
        ElMessage.error('初始化备品备件模块失败，请刷新重试');
      }
    },
    
    // ===== 备件类型相关 =====
    // 获取备件类型列表
    async fetchTypes() {
      try {
        this.typesLoading = true;
        const response = await sparePartsApi.getSparePartTypes();
        if (response.success) {
          this.types = response.data;
        } else {
          ElMessage.warning(response.message || '获取备件类型失败');
        }
      } catch (error) {
        console.error('获取备件类型出错:', error);
        ElMessage.error('获取备件类型失败，请稍后重试');
      } finally {
        this.typesLoading = false;
      }
    },
    
    // 获取树形结构的备件类型
    async fetchTypesTree() {
      try {
        this.typesLoading = true;
        const response = await sparePartsApi.getSparePartTypesTree();
        if (response.success) {
          this.typesTree = response.data;
        } else {
          ElMessage.warning(response.message || '获取备件类型树失败');
        }
      } catch (error) {
        console.error('获取备件类型树出错:', error);
        ElMessage.error('获取备件类型树失败，请稍后重试');
      } finally {
        this.typesLoading = false;
      }
    },
    
    // 创建备件类型
    async createType(data) {
      try {
        this.formLoading = true;
        const response = await sparePartsApi.createSparePartType(data);
        if (response.success) {
          ElMessage.success('创建备件类型成功');
          // 刷新类型列表
          await this.fetchTypes();
          if (this.typesTree.length > 0) {
            await this.fetchTypesTree();
          }
          return response.data;
        } else {
          ElMessage.warning(response.message || '创建备件类型失败');
          return null;
        }
      } catch (error) {
        console.error('创建备件类型出错:', error);
        ElMessage.error('创建备件类型失败，请稍后重试');
        return null;
      } finally {
        this.formLoading = false;
      }
    },
    
    // 更新备件类型
    async updateType(id, data) {
      try {
        this.formLoading = true;
        const response = await sparePartsApi.updateSparePartType(id, data);
        if (response.success) {
          ElMessage.success('更新备件类型成功');
          // 刷新类型列表
          await this.fetchTypes();
          if (this.typesTree.length > 0) {
            await this.fetchTypesTree();
          }
          return response.data;
        } else {
          ElMessage.warning(response.message || '更新备件类型失败');
          return null;
        }
      } catch (error) {
        console.error('更新备件类型出错:', error);
        ElMessage.error('更新备件类型失败，请稍后重试');
        return null;
      } finally {
        this.formLoading = false;
      }
    },
    
    // 删除备件类型
    async deleteType(id) {
      try {
        this.formLoading = true;
        const response = await sparePartsApi.deleteSparePartType(id);
        if (response.success) {
          ElMessage.success('删除备件类型成功');
          // 刷新类型列表
          await this.fetchTypes();
          if (this.typesTree.length > 0) {
            await this.fetchTypesTree();
          }
          return true;
        } else {
          ElMessage.warning(response.message || '删除备件类型失败');
          return false;
        }
      } catch (error) {
        console.error('删除备件类型出错:', error);
        ElMessage.error('删除备件类型失败，请稍后重试');
        return false;
      } finally {
        this.formLoading = false;
      }
    },
    
    // ===== 库位相关 =====
    // 获取库位列表
    async fetchLocations() {
      try {
        this.locationsLoading = true;
        const response = await sparePartsApi.getSparePartLocations();
        if (response.success) {
          this.locations = response.data;
          return this.locations;
        } else {
          ElMessage.warning(response.message || '获取库位失败');
          return [];
        }
      } catch (error) {
        console.error('获取库位出错:', error);
        ElMessage.error('获取库位失败，请稍后重试');
        return [];
      } finally {
        this.locationsLoading = false;
      }
    },
    
    // 获取所有区域
    async fetchAreas() {
      try {
        const response = await sparePartsApi.getSparePartAreaStats();
        if (response.success) {
          this.areas = response.data || [];
          return this.areas;
        } else {
          ElMessage.warning(response.message || '获取区域失败');
          return [];
        }
      } catch (error) {
        console.error('获取区域出错:', error);
        ElMessage.error('获取区域失败，请稍后重试');
        return [];
      }
    },
    
    // 创建库位
    async createLocation(data) {
      try {
        this.formLoading = true;
        const response = await sparePartsApi.createSparePartLocation(data);
        if (response.success) {
          ElMessage.success('创建库位成功');
          // 刷新库位列表
          await this.fetchLocations();
          return response.data;
        } else {
          ElMessage.warning(response.message || '创建库位失败');
          return null;
        }
      } catch (error) {
        console.error('创建库位出错:', error);
        ElMessage.error('创建库位失败，请稍后重试');
        return null;
      } finally {
        this.formLoading = false;
      }
    },
    
    // 更新库位
    async updateLocation(id, data) {
      try {
        this.formLoading = true;
        const response = await sparePartsApi.updateSparePartLocation(id, data);
        if (response.success) {
          ElMessage.success('更新库位成功');
          // 刷新库位列表
          await this.fetchLocations();
          return response.data;
        } else {
          ElMessage.warning(response.message || '更新库位失败');
          return null;
        }
      } catch (error) {
        console.error('更新库位出错:', error);
        ElMessage.error('更新库位失败，请稍后重试');
        return null;
      } finally {
        this.formLoading = false;
      }
    },
    
    // 删除库位
    async deleteLocation(id) {
      try {
        this.formLoading = true;
        const response = await sparePartsApi.deleteSparePartLocation(id);
        if (response.success) {
          ElMessage.success('删除库位成功');
          // 刷新库位列表
          await this.fetchLocations();
          return true;
        } else {
          ElMessage.warning(response.message || '删除库位失败');
          return false;
        }
      } catch (error) {
        console.error('删除库位出错:', error);
        ElMessage.error('删除库位失败，请稍后重试');
        return false;
      } finally {
        this.formLoading = false;
      }
    },
    
    // ===== 备件相关 =====
    // 获取备件列表
    async fetchSpareParts(params = {}) {
      try {
        this.sparePartsLoading = true;
        const response = await sparePartsApi.getSpareParts(params);
        if (response.success) {
          this.spareParts = response.data.items || response.data;
          this.sparePartsTotal = response.data.total || response.data.length;
          return {
            items: this.spareParts,
            total: this.sparePartsTotal
          };
        } else {
          ElMessage.warning(response.message || '获取备件列表失败');
          return { items: [], total: 0 };
        }
      } catch (error) {
        console.error('获取备件列表出错:', error);
        ElMessage.error('获取备件列表失败，请稍后重试');
        return { items: [], total: 0 };
      } finally {
        this.sparePartsLoading = false;
      }
    },
    
    // 获取单个备件详情
    async fetchSparePart(id) {
      try {
        this.loading = true;
        const response = await sparePartsApi.getSparePart(id);
        if (response.success) {
          this.currentSparePart = response.data;
          return response.data;
        } else {
          ElMessage.warning(response.message || '获取备件详情失败');
          return null;
        }
      } catch (error) {
        console.error('获取备件详情出错:', error);
        ElMessage.error('获取备件详情失败，请稍后重试');
        return null;
      } finally {
        this.loading = false;
      }
    },
    
    // 创建备件
    async createSparePart(data) {
      try {
        this.formLoading = true;
        const response = await sparePartsApi.createSparePart(data);
        if (response.success) {
          ElMessage.success('创建备件成功');
          return response.data;
        } else {
          ElMessage.warning(response.message || '创建备件失败');
          return null;
        }
      } catch (error) {
        console.error('创建备件出错:', error);
        ElMessage.error('创建备件失败，请稍后重试');
        return null;
      } finally {
        this.formLoading = false;
      }
    },
    
    // 更新备件
    async updateSparePart(id, data) {
      try {
        this.formLoading = true;
        const response = await sparePartsApi.updateSparePart(id, data);
        if (response.success) {
          ElMessage.success('更新备件成功');
          // 如果当前查看的是这个备件，更新本地数据
          if (this.currentSparePart && this.currentSparePart.id === id) {
            this.currentSparePart = { ...this.currentSparePart, ...data };
          }
          return response.data;
        } else {
          ElMessage.warning(response.message || '更新备件失败');
          return null;
        }
      } catch (error) {
        console.error('更新备件出错:', error);
        ElMessage.error('更新备件失败，请稍后重试');
        return null;
      } finally {
        this.formLoading = false;
      }
    },
    
    // 删除备件
    async deleteSparePart(id) {
      try {
        this.formLoading = true;
        const response = await sparePartsApi.deleteSparePart(id);
        if (response.success) {
          ElMessage.success('删除备件成功');
          // 如果当前查看的是这个备件，清空本地数据
          if (this.currentSparePart && this.currentSparePart.id === id) {
            this.currentSparePart = null;
          }
          return true;
        } else {
          ElMessage.warning(response.message || '删除备件失败');
          return false;
        }
      } catch (error) {
        console.error('删除备件出错:', error);
        ElMessage.error('删除备件失败，请稍后重试');
        return false;
      } finally {
        this.formLoading = false;
      }
    },
    
    // ===== 出入库相关 =====
    // 获取出入库记录
    async fetchTransactions(params = {}) {
      try {
        this.transactionsLoading = true;
        const response = await sparePartsApi.getSparePartTransactions(params);
        if (response.success) {
          this.transactions = response.data.items || response.data;
          this.transactionsTotal = response.data.total || response.data.length;
          return {
            items: this.transactions,
            total: this.transactionsTotal
          };
        } else {
          ElMessage.warning(response.message || '获取出入库记录失败');
          return { items: [], total: 0 };
        }
      } catch (error) {
        console.error('获取出入库记录出错:', error);
        ElMessage.error('获取出入库记录失败，请稍后重试');
        return { items: [], total: 0 };
      } finally {
        this.transactionsLoading = false;
      }
    },
    
    // 入库操作
    async createInbound(data) {
      try {
        this.formLoading = true;
        const response = await sparePartsApi.sparePartInbound(data);
        if (response.success) {
          ElMessage.success('入库操作成功');
          return response.data;
        } else {
          ElMessage.warning(response.message || '入库操作失败');
          return null;
        }
      } catch (error) {
        console.error('入库操作出错:', error);
        ElMessage.error('入库操作失败，请稍后重试');
        return null;
      } finally {
        this.formLoading = false;
      }
    },
    
    // 出库操作
    async createOutbound(data) {
      try {
        this.formLoading = true;
        const response = await sparePartsApi.sparePartOutbound(data);
        if (response.success) {
          ElMessage.success('出库操作成功');
          return response.data;
        } else {
          ElMessage.warning(response.message || '出库操作失败');
          return null;
        }
      } catch (error) {
        console.error('出库操作出错:', error);
        ElMessage.error('出库操作失败，请稍后重试');
        return null;
      } finally {
        this.formLoading = false;
      }
    },
    
    // 重置状态
    resetStore() {
      this.loading = false;
      this.formLoading = false;
      this.types = [];
      this.typesTree = [];
      this.typesLoading = false;
      this.locations = [];
      this.locationsLoading = false;
      this.spareParts = [];
      this.sparePartsTotal = 0;
      this.sparePartsLoading = false;
      this.currentSparePart = null;
      this.transactions = [];
      this.transactionsTotal = 0;
      this.transactionsLoading = false;
      this.areas = [];
    }
  }
}); 