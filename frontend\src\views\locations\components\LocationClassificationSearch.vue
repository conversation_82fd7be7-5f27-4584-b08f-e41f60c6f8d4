<!-- File: frontend/src/views/locations/components/LocationClassificationSearch.vue -->
<!-- Description: 位置分类高级搜索组件 -->

<template>
  <div class="location-classification-search">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>位置分类高级搜索</span>
          <el-button type="text" @click="resetSearch">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </div>
      </template>

      <!-- 搜索条件表单 -->
      <el-form :model="searchForm" label-width="120px" ref="searchFormRef">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="部门筛选">
              <el-select 
                v-model="searchForm.departmentId" 
                placeholder="选择部门" 
                clearable 
                filterable
                style="width: 100%"
              >
                <el-option
                  v-for="dept in departments"
                  :key="dept.departmentId"
                  :label="dept.departmentName"
                  :value="dept.departmentId"
                />
              </el-select>
            </el-form-item>
          </el-col>
          
          <el-col :span="8">
            <el-form-item label="位置类型">
              <el-select 
                v-model="searchForm.locationType" 
                placeholder="选择位置类型" 
                clearable
                style="width: 100%"
              >
                <el-option label="工厂" :value="1" />
                <el-option label="车间" :value="2" />
                <el-option label="工序" :value="3" />
                <el-option label="工位" :value="4" />
              </el-select>
            </el-form-item>
          </el-col>
          
          <el-col :span="8">
            <el-form-item label="父级位置">
              <el-select 
                v-model="searchForm.parentLocationId" 
                placeholder="选择父级位置" 
                clearable 
                filterable
                style="width: 100%"
              >
                <el-option
                  v-for="location in allLocations"
                  :key="location.locationId"
                  :label="location.locationPath"
                  :value="location.locationId"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="资产类型">
              <el-select 
                v-model="searchForm.assetTypeId" 
                placeholder="选择资产类型" 
                clearable 
                filterable
                style="width: 100%"
              >
                <el-option
                  v-for="type in assetTypes"
                  :key="type.assetTypeId"
                  :label="type.assetTypeName"
                  :value="type.assetTypeId"
                />
              </el-select>
            </el-form-item>
          </el-col>
          
          <el-col :span="8">
            <el-form-item label="资产状态">
              <el-select 
                v-model="searchForm.assetStatus" 
                placeholder="选择资产状态" 
                clearable
                style="width: 100%"
              >
                <el-option label="正常" value="正常" />
                <el-option label="维修中" value="维修中" />
                <el-option label="报废" value="报废" />
                <el-option label="闲置" value="闲置" />
              </el-select>
            </el-form-item>
          </el-col>
          
          <el-col :span="8">
            <el-form-item label="关键词搜索">
              <el-input 
                v-model="searchForm.keyword" 
                placeholder="位置名称、部门、资产名称..."
                clearable
                @keyup.enter="performSearch"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item>
              <el-checkbox v-model="searchForm.includeChildren">包含子位置</el-checkbox>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item>
              <el-checkbox v-model="searchForm.onlyWithAssets">仅显示有资产的位置</el-checkbox>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item>
              <el-checkbox v-model="searchForm.onlyDirectDepartment">仅显示直接分配部门的位置</el-checkbox>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item>
              <el-button type="primary" @click="performSearch" :loading="searching">
                <el-icon><Search /></el-icon>
                搜索
              </el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 搜索结果 -->
    <el-card class="results-card" v-if="searchResults.length > 0 || hasSearched">
      <template #header>
        <div class="results-header">
          <span>搜索结果 (共 {{ totalCount }} 条)</span>
          <div class="results-actions">
            <el-select v-model="sortBy" size="small" style="width: 120px; margin-right: 10px;">
              <el-option label="位置路径" value="locationPath" />
              <el-option label="位置名称" value="locationName" />
              <el-option label="部门名称" value="departmentName" />
              <el-option label="资产数量" value="assetCount" />
              <el-option label="层级" value="level" />
            </el-select>
            <el-select v-model="sortDirection" size="small" style="width: 80px; margin-right: 10px;">
              <el-option label="升序" value="asc" />
              <el-option label="降序" value="desc" />
            </el-select>
            <el-button size="small" @click="performSearch">应用排序</el-button>
          </div>
        </div>
      </template>

      <!-- 结果表格 -->
      <el-table 
        :data="searchResults" 
        v-loading="searching"
        border
        stripe
        @sort-change="handleSortChange"
      >
        <el-table-column prop="locationPath" label="位置路径" min-width="200" show-overflow-tooltip />
        <el-table-column prop="locationName" label="位置名称" width="120" />
        <el-table-column label="位置类型" width="100" align="center">
          <template #default="{ row }">
            <el-tag size="small" :type="getLocationTypeTag(row.locationType)">
              {{ getLocationTypeName(row.locationType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="有效部门" width="120">
          <template #default="{ row }">
            <el-tag v-if="row.effectiveDepartmentName" type="success" size="small">
              {{ row.effectiveDepartmentName }}
            </el-tag>
            <span v-else class="text-muted">未分配</span>
          </template>
        </el-table-column>
        <el-table-column label="部门来源" width="100" align="center">
          <template #default="{ row }">
            <el-tag v-if="row.directDepartmentId" type="primary" size="small">直接</el-tag>
            <el-tag v-else-if="row.inheritedDepartmentId" type="warning" size="small">继承</el-tag>
            <span v-else class="text-muted">无</span>
          </template>
        </el-table-column>
        <el-table-column prop="assetCount" label="资产数量" width="100" align="center" sortable />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="text" size="small" @click="viewLocationDetail(row)">
              详情
            </el-button>
            <el-button type="text" size="small" @click="viewLocationAssets(row)">
              资产列表
            </el-button>
            <el-button 
              v-if="row.effectiveDepartmentId" 
              type="text" 
              size="small" 
              @click="viewDepartmentLocations(row.effectiveDepartmentId)"
            >
              同部门位置
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="totalCount"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 分类统计信息 -->
    <el-card class="stats-card" v-if="classificationStats">
      <template #header>
        <span>分类统计信息</span>
      </template>
      
      <el-tabs>
        <!-- 部门统计 -->
        <el-tab-pane label="按部门" name="department">
          <el-table :data="classificationStats.byDepartment" size="small">
            <el-table-column prop="departmentName" label="部门名称" />
            <el-table-column prop="directLocationCount" label="直接位置" width="100" align="center" />
            <el-table-column prop="inheritedLocationCount" label="继承位置" width="100" align="center" />
            <el-table-column prop="totalAssetCount" label="总资产" width="100" align="center" />
          </el-table>
        </el-tab-pane>
        
        <!-- 位置类型统计 -->
        <el-tab-pane label="按类型" name="type">
          <el-table :data="classificationStats.byLocationType" size="small">
            <el-table-column prop="locationTypeName" label="位置类型" />
            <el-table-column prop="locationCount" label="位置数量" width="100" align="center" />
            <el-table-column prop="assetCount" label="资产数量" width="100" align="center" />
            <el-table-column prop="locationsWithDepartment" label="有部门位置" width="120" align="center" />
          </el-table>
        </el-tab-pane>
        
        <!-- 资产类型统计 -->
        <el-tab-pane label="按资产类型" name="assetType">
          <el-table :data="classificationStats.byAssetType" size="small">
            <el-table-column prop="assetTypeName" label="资产类型" />
            <el-table-column prop="assetCount" label="资产数量" width="100" align="center" />
            <el-table-column prop="locationCount" label="分布位置" width="100" align="center" />
            <el-table-column prop="departmentCount" label="涉及部门" width="100" align="center" />
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Refresh } from '@element-plus/icons-vue'
import locationDepartmentInheritanceApi from '@/api/locationDepartmentInheritance'

// 响应式数据
const searchFormRef = ref()
const searching = ref(false)
const hasSearched = ref(false)
const searchResults = ref([])
const totalCount = ref(0)
const currentPage = ref(1)
const pageSize = ref(20)
const sortBy = ref('locationPath')
const sortDirection = ref('asc')

// 基础数据
const departments = ref([])
const allLocations = ref([])
const assetTypes = ref([])
const classificationStats = ref(null)

// 搜索表单
const searchForm = reactive({
  departmentId: null,
  locationType: null,
  parentLocationId: null,
  assetTypeId: null,
  assetStatus: null,
  includeChildren: true,
  onlyWithAssets: false,
  onlyDirectDepartment: false,
  keyword: '',
  sortBy: 'locationPath',
  sortDirection: 'asc',
  pageNumber: 1,
  pageSize: 20
})

// 监听排序变化
watch([sortBy, sortDirection], () => {
  searchForm.sortBy = sortBy.value
  searchForm.sortDirection = sortDirection.value
})

// 监听分页变化
watch([currentPage, pageSize], () => {
  searchForm.pageNumber = currentPage.value
  searchForm.pageSize = pageSize.value
})

// 执行搜索
const performSearch = async () => {
  searching.value = true
  hasSearched.value = true
  
  try {
    searchForm.pageNumber = currentPage.value
    searchForm.pageSize = pageSize.value
    searchForm.sortBy = sortBy.value
    searchForm.sortDirection = sortDirection.value
    
    const response = await locationDepartmentInheritanceApi.searchLocationsByClassification(searchForm)
    
    if (response.success) {
      searchResults.value = response.data.locations
      totalCount.value = response.data.totalCount
      classificationStats.value = response.data.stats
      
      ElMessage.success(`搜索完成，找到 ${totalCount.value} 个位置`)
    } else {
      ElMessage.error(response.message || '搜索失败')
    }
  } catch (error) {
    console.error('搜索错误:', error)
    ElMessage.error('搜索失败')
  } finally {
    searching.value = false
  }
}

// 重置搜索
const resetSearch = () => {
  searchFormRef.value?.resetFields()
  Object.assign(searchForm, {
    departmentId: null,
    locationType: null,
    parentLocationId: null,
    assetTypeId: null,
    assetStatus: null,
    includeChildren: true,
    onlyWithAssets: false,
    onlyDirectDepartment: false,
    keyword: '',
    sortBy: 'locationPath',
    sortDirection: 'asc',
    pageNumber: 1,
    pageSize: 20
  })
  
  searchResults.value = []
  totalCount.value = 0
  currentPage.value = 1
  hasSearched.value = false
  classificationStats.value = null
}

// 处理表格排序
const handleSortChange = ({ prop, order }) => {
  if (prop && order) {
    sortBy.value = prop
    sortDirection.value = order === 'ascending' ? 'asc' : 'desc'
    performSearch()
  }
}

// 处理分页大小变化
const handleSizeChange = (newSize) => {
  pageSize.value = newSize
  currentPage.value = 1
  performSearch()
}

// 处理页码变化
const handleCurrentChange = (newPage) => {
  currentPage.value = newPage
  performSearch()
}

// 获取位置类型名称
const getLocationTypeName = (type) => {
  const typeMap = { 1: '工厂', 2: '车间', 3: '工序', 4: '工位' }
  return typeMap[type] || '其他'
}

// 获取位置类型标签样式
const getLocationTypeTag = (type) => {
  const tagMap = { 1: 'danger', 2: 'warning', 3: 'primary', 4: 'success' }
  return tagMap[type] || 'info'
}

// 查看位置详情
const viewLocationDetail = (location) => {
  console.log('查看位置详情:', location)
  // 实现位置详情查看逻辑
}

// 查看位置资产
const viewLocationAssets = (location) => {
  console.log('查看位置资产:', location)
  // 实现位置资产查看逻辑
}

// 查看同部门位置
const viewDepartmentLocations = async (departmentId) => {
  try {
    const response = await locationDepartmentInheritanceApi.getLocationsByDepartment(departmentId)
    if (response.success) {
      console.log('同部门位置:', response.data)
      ElMessage.success(`该部门管理 ${response.data.length} 个位置`)
    }
  } catch (error) {
    console.error('获取同部门位置错误:', error)
    ElMessage.error('获取同部门位置失败')
  }
}

// 加载基础数据
const loadBaseData = async () => {
  try {
    // 加载部门统计（获取部门列表）
    const deptResponse = await locationDepartmentInheritanceApi.getDepartmentLocationStats()
    if (deptResponse.success) {
      departments.value = deptResponse.data
    }
    
    // 加载所有位置（用于父级位置选择）
    const locResponse = await locationDepartmentInheritanceApi.getLocationDepartmentInheritance()
    if (locResponse.success) {
      allLocations.value = locResponse.data
    }
    
    // 加载分类统计
    const statsResponse = await locationDepartmentInheritanceApi.getLocationClassificationStats()
    if (statsResponse.success) {
      classificationStats.value = statsResponse.data
      // 从统计中提取资产类型
      assetTypes.value = statsResponse.data.byAssetType || []
    }
  } catch (error) {
    console.error('加载基础数据错误:', error)
  }
}

// 组件挂载
onMounted(() => {
  loadBaseData()
})
</script>

<style lang="scss" scoped>
.location-classification-search {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .results-card {
    margin-top: 20px;
  }

  .results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .results-actions {
    display: flex;
    align-items: center;
  }

  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }

  .stats-card {
    margin-top: 20px;
  }

  .text-muted {
    color: #909399;
  }
}
</style>