// File: Application/Features/Purchase/Services/PurchaseService.cs
// Description: 采购管理服务实现 - 实现采购到备件库和资产的转化

using ItAssetsSystem.Application.Features.Purchase.Dtos;
using ItAssetsSystem.Infrastructure.Data;
using ItAssetsSystem.Models.Entities;
using ItAssetsSystem.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace ItAssetsSystem.Application.Features.Purchase.Services
{
    /// <summary>
    /// 采购服务实现 - 支持采购到备件库和资产的强关联转化
    /// </summary>
    public class PurchaseService : IPurchaseService
    {
        private readonly AppDbContext _context;
        private readonly ILogger<PurchaseService> _logger;

        public PurchaseService(AppDbContext context, ILogger<PurchaseService> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// 获取采购订单列表（分页）
        /// </summary>
        public async Task<PagedResult<PurchaseOrderDto>> GetPurchaseOrdersAsync(PurchaseOrderQuery query)
        {
            _logger.LogInformation("获取采购订单列表，页码: {PageIndex}, 页大小: {PageSize}", query.PageIndex, query.PageSize);

            var queryable = _context.PurchaseOrders
                .Include(p => p.Supplier)
                .Include(p => p.Requester)
                .Include(p => p.PurchaseItems)
                .AsQueryable();

            // 关键字搜索
            if (!string.IsNullOrEmpty(query.Keyword))
            {
                queryable = queryable.Where(p => p.OrderNumber.Contains(query.Keyword) || 
                                                p.Notes.Contains(query.Keyword));
            }

            // 供应商筛选
            if (query.SupplierId.HasValue)
            {
                queryable = queryable.Where(p => p.SupplierId == query.SupplierId.Value);
            }

            // 状态筛选
            if (query.Status.HasValue)
            {
                queryable = queryable.Where(p => p.Status == query.Status.Value);
            }

            // 日期范围筛选
            if (query.StartDate.HasValue)
            {
                queryable = queryable.Where(p => p.CreatedAt >= query.StartDate.Value);
            }
            if (query.EndDate.HasValue)
            {
                queryable = queryable.Where(p => p.CreatedAt <= query.EndDate.Value);
            }

            // 排序
            queryable = query.SortDirection?.ToLower() == "desc" 
                ? queryable.OrderByDescending(p => EF.Property<object>(p, query.SortBy))
                : queryable.OrderBy(p => EF.Property<object>(p, query.SortBy));

            var totalCount = await queryable.CountAsync();
            var items = await queryable
                .Skip((query.PageIndex - 1) * query.PageSize)
                .Take(query.PageSize)
                .Select(p => new PurchaseOrderDto
                {
                    Id = p.Id,
                    OrderCode = p.OrderNumber,
                    Title = p.Title,
                    Description = p.Description,
                    SupplierId = p.SupplierId,
                    SupplierName = p.Supplier.Name,
                    Status = p.Status,
                    StatusName = GetStatusName(p.Status),
                    EstimatedDeliveryDate = p.ExpectedDeliveryDate,
                    ActualDeliveryDate = p.ActualDeliveryDate,
                    ApplicantId = p.RequesterId,
                    ApplicantName = p.Requester.Name,
                    ApplicationTime = p.ApplicationTime,
                    ApproverId = p.ApproverId,
                    ApprovalTime = p.ApprovalTime,
                    TotalAmount = p.TotalAmount,
                    Notes = p.Notes,
                    CreatedAt = p.CreatedAt,
                    UpdatedAt = p.UpdatedAt,
                    // 物料信息
                    PrimaryItemCode = p.PurchaseItems.OrderBy(pi => pi.Id).FirstOrDefault().ItemCode ?? "",
                    PrimaryItemName = p.PurchaseItems.OrderBy(pi => pi.Id).FirstOrDefault().ItemName ?? "",
                    TotalQuantity = p.PurchaseItems.Sum(pi => pi.Quantity),
                    ItemCount = p.PurchaseItems.Count()
                })
                .ToListAsync();

            return new PagedResult<PurchaseOrderDto>
            {
                Items = items,
                TotalCount = totalCount,
                PageIndex = query.PageIndex,
                PageSize = query.PageSize
            };
        }

        /// <summary>
        /// 获取采购订单详情
        /// </summary>
        public async Task<PurchaseOrderDetailDto> GetPurchaseOrderByIdAsync(int id)
        {
            _logger.LogInformation("获取采购订单详情，ID: {Id}", id);

            var order = await _context.PurchaseOrders
                .Include(p => p.Supplier)
                .Include(p => p.Requester)
                .Include(p => p.PurchaseItems)
                .ThenInclude(pi => pi.AssetType)
                .FirstOrDefaultAsync(p => p.Id == id);

            if (order == null)
            {
                throw new InvalidOperationException($"采购订单 {id} 不存在");
            }

            return new PurchaseOrderDetailDto
            {
                Id = order.Id,
                OrderCode = order.OrderNumber,
                Title = order.Title,
                Description = order.Description,
                SupplierId = order.SupplierId,
                SupplierName = order.Supplier.Name,
                Status = order.Status,
                StatusName = GetStatusName(order.Status),
                EstimatedDeliveryDate = order.ExpectedDeliveryDate,
                ActualDeliveryDate = order.ActualDeliveryDate,
                ApplicantId = order.RequesterId,
                ApplicantName = order.Requester.Name,
                ApplicationTime = order.ApplicationTime,
                ApproverId = order.ApproverId,
                ApprovalTime = order.ApprovalTime,
                TotalAmount = order.TotalAmount,
                Notes = order.Notes,
                CreatedAt = order.CreatedAt,
                UpdatedAt = order.UpdatedAt,
                Items = order.PurchaseItems.Select(pi => new PurchaseItemDto
                {
                    Id = pi.Id,
                    PurchaseOrderId = pi.PurchaseOrderId,
                    ItemName = pi.ItemName,
                    ItemCode = pi.ItemCode,
                    Specification = pi.Specification,
                    AssetTypeId = pi.AssetTypeId,
                    AssetTypeName = pi.AssetType?.Name,
                    UnitPrice = pi.UnitPrice,
                    Quantity = pi.Quantity,
                    TotalPrice = pi.TotalPrice,
                    Notes = pi.Notes,
                    CreatedAt = pi.CreatedAt,
                    UpdatedAt = pi.UpdatedAt
                }).ToList()
            };
        }

        /// <summary>
        /// 创建采购订单
        /// </summary>
        public async Task<PurchaseOrderDto> CreatePurchaseOrderAsync(CreatePurchaseOrderRequest request)
        {
            _logger.LogInformation("创建采购订单，供应商ID: {SupplierId}", request.SupplierId);

            var order = new PurchaseOrder
            {
                OrderNumber = GenerateOrderNumber(),
                Title = $"采购申请-{DateTime.Now:yyyy年MM月dd日}", // 默认标题
                Description = request.Notes, // 使用备注作为描述
                SupplierId = request.SupplierId,
                RequesterId = request.RequesterId,
                ApplicationTime = DateTime.Now, // 申请时间
                ExpectedDeliveryDate = request.ExpectedDeliveryDate,
                TotalAmount = request.Items.Sum(i => i.UnitPrice * i.Quantity),
                Status = 0, // 未到货
                Notes = request.Notes,
                CreatedAt = DateTime.Now,
                UpdatedAt = DateTime.Now
            };

            _context.PurchaseOrders.Add(order);
            await _context.SaveChangesAsync();

            // 合并重复的采购物品
            var mergedItems = MergeDuplicateItems(request.Items);
            _logger.LogInformation("原始物品数量: {OriginalCount}, 合并后数量: {MergedCount}",
                request.Items.Count, mergedItems.Count);

            // 添加采购物品
            foreach (var itemRequest in mergedItems)
            {
                var item = new PurchaseItem
                {
                    PurchaseOrderId = order.Id,
                    AssetTypeId = itemRequest.AssetTypeId,
                    ItemName = itemRequest.ItemName,
                    ItemCode = itemRequest.ItemCode, // 添加物料编号
                    Specification = itemRequest.Specification,
                    UnitPrice = itemRequest.UnitPrice,
                    Quantity = itemRequest.Quantity,
                    TotalPrice = itemRequest.UnitPrice * itemRequest.Quantity,
                    Notes = itemRequest.Notes,
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now
                };
                _context.PurchaseItems.Add(item);
            }

            await _context.SaveChangesAsync();

            return await GetPurchaseOrderDtoAsync(order.Id);
        }

        /// <summary>
        /// 更新采购订单
        /// </summary>
        public async Task<PurchaseOrderDto> UpdatePurchaseOrderAsync(int id, UpdatePurchaseOrderRequest request)
        {
            _logger.LogInformation("更新采购订单，ID: {Id}", id);

            var order = await _context.PurchaseOrders.FindAsync(id);
            if (order == null)
            {
                throw new InvalidOperationException($"采购订单 {id} 不存在");
            }

            order.SupplierId = request.SupplierId;
            order.ExpectedDeliveryDate = request.ExpectedDeliveryDate;
            order.Notes = request.Notes;
            order.UpdatedAt = DateTime.Now;

            await _context.SaveChangesAsync();

            return await GetPurchaseOrderDtoAsync(id);
        }

        /// <summary>
        /// 删除采购订单
        /// </summary>
        public async Task<bool> DeletePurchaseOrderAsync(int id)
        {
            _logger.LogInformation("删除采购订单，ID: {Id}", id);

            var order = await _context.PurchaseOrders
                .Include(p => p.PurchaseItems)
                .FirstOrDefaultAsync(p => p.Id == id);

            if (order == null)
            {
                return false;
            }

            // 删除采购物品
            _context.PurchaseItems.RemoveRange(order.PurchaseItems);
            // 删除订单
            _context.PurchaseOrders.Remove(order);

            await _context.SaveChangesAsync();
            return true;
        }

        /// <summary>
        /// 确认采购订单到货并处理转化
        /// </summary>
        public async Task<PurchaseDeliveryResultDto> ConfirmDeliveryAsync(int id, ConfirmDeliveryRequest request)
        {
            _logger.LogInformation("确认采购订单到货，ID: {Id}", id);

            var order = await _context.PurchaseOrders
                .Include(p => p.PurchaseItems)
                .FirstOrDefaultAsync(p => p.Id == id);

            if (order == null)
            {
                throw new InvalidOperationException($"采购订单 {id} 不存在");
            }

            // 更新订单状态
            order.Status = 1; // 已到货
            order.ActualDeliveryDate = request.ActualDeliveryDate ?? DateTime.Now;
            order.UpdatedAt = DateTime.Now;

            await _context.SaveChangesAsync();

            return new PurchaseDeliveryResultDto
            {
                OrderId = id,
                Success = true,
                Message = "采购订单到货确认成功",
                DeliveryDate = order.ActualDeliveryDate.Value
            };
        }

        /// <summary>
        /// 获取状态名称
        /// </summary>
        private static string GetStatusName(int status)
        {
            return status switch
            {
                0 => "未到货",
                1 => "已到货",
                2 => "已取消",
                _ => "未知状态"
            };
        }

        /// <summary>
        /// 生成订单编号
        /// </summary>
        private string GenerateOrderNumber()
        {
            return $"PO-{DateTime.Now:yyyyMMdd}-{DateTime.Now.Ticks % 10000:D4}";
        }

        /// <summary>
        /// 获取采购订单DTO
        /// </summary>
        private async Task<PurchaseOrderDto> GetPurchaseOrderDtoAsync(int id)
        {
            var order = await _context.PurchaseOrders
                .Include(p => p.Supplier)
                .Include(p => p.Requester)
                .FirstOrDefaultAsync(p => p.Id == id);

            return new PurchaseOrderDto
            {
                Id = order.Id,
                OrderCode = order.OrderNumber,
                Title = order.Title,
                Description = order.Description,
                SupplierId = order.SupplierId,
                SupplierName = order.Supplier.Name,
                Status = order.Status,
                StatusName = GetStatusName(order.Status),
                EstimatedDeliveryDate = order.ExpectedDeliveryDate,
                ActualDeliveryDate = order.ActualDeliveryDate,
                ApplicantId = order.RequesterId,
                ApplicantName = order.Requester.Name,
                ApplicationTime = order.ApplicationTime,
                ApproverId = order.ApproverId,
                ApprovalTime = order.ApprovalTime,
                TotalAmount = order.TotalAmount,
                Notes = order.Notes,
                CreatedAt = order.CreatedAt,
                UpdatedAt = order.UpdatedAt
            };
        }

        /// <summary>
        /// 处理采购物品到货（转换为资产或备件）
        /// </summary>
        public async Task<PurchaseProcessResultDto> ProcessDeliveredItemsAsync(int orderId, List<ProcessDeliveredItemRequest> items)
        {
            _logger.LogInformation("处理采购物品到货转化，订单ID: {OrderId}", orderId);

            var result = new PurchaseProcessResultDto
            {
                OrderId = orderId,
                ProcessedItems = new List<ProcessedItemResult>()
            };

            foreach (var itemRequest in items)
            {
                var purchaseItem = await _context.PurchaseItems.FindAsync(itemRequest.PurchaseItemId);
                if (purchaseItem == null) continue;

                var itemResult = new ProcessedItemResult
                {
                    PurchaseItemId = itemRequest.PurchaseItemId,
                    ItemName = purchaseItem.ItemName
                };

                try
                {
                    // 转为备件
                    if (itemRequest.ToSparePartQuantity > 0)
                    {
                        var sparePartId = await CreateSparePartFromPurchaseAsync(
                            purchaseItem,
                            itemRequest.ToSparePartQuantity,
                            itemRequest.SparePartLocationId.Value);

                        itemResult.CreatedSparePartIds.Add(sparePartId);
                        itemResult.SparePartQuantity = itemRequest.ToSparePartQuantity;
                    }

                    // 转为资产
                    if (itemRequest.ToAssetQuantity > 0)
                    {
                        var assetIds = await CreateAssetsFromPurchaseAsync(
                            purchaseItem,
                            itemRequest.ToAssetQuantity,
                            itemRequest.AssetLocationId.Value);

                        itemResult.CreatedAssetIds.AddRange(assetIds);
                        itemResult.AssetQuantity = itemRequest.ToAssetQuantity;
                    }

                    itemResult.Success = true;
                    itemResult.Message = "处理成功";
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "处理采购物品失败，物品ID: {ItemId}", itemRequest.PurchaseItemId);
                    itemResult.Success = false;
                    itemResult.Message = ex.Message;
                }

                result.ProcessedItems.Add(itemResult);
            }

            result.Success = result.ProcessedItems.All(i => i.Success);
            result.Message = result.Success ? "所有物品处理成功" : "部分物品处理失败";

            return result;
        }

        /// <summary>
        /// 获取供应商列表
        /// </summary>
        public async Task<List<SupplierDto>> GetSuppliersAsync()
        {
            _logger.LogInformation("获取供应商列表");

            return await _context.Suppliers
                .Select(s => new SupplierDto
                {
                    Id = s.Id,
                    Name = s.Name,
                    ContactPerson = s.ContactPerson,
                    Phone = s.ContactPhone,
                    Email = s.ContactEmail,
                    Address = s.Address
                })
                .ToListAsync();
        }

        /// <summary>
        /// 获取可采购的资产类型列表（从备件类型读取）
        /// </summary>
        public async Task<List<AssetTypeDto>> GetPurchasableAssetTypesAsync()
        {
            _logger.LogInformation("获取可采购的资产类型列表");

            // 从备件类型表读取，实现统一的类型管理
            return await _context.SparePartTypes
                .Select(spt => new AssetTypeDto
                {
                    Id = (int)spt.Id, // 转换为int以兼容现有资产类型
                    Name = spt.Name,
                    Code = spt.Code,
                    Description = spt.Description
                })
                .ToListAsync();
        }

        /// <summary>
        /// 从采购物品创建备品备件
        /// </summary>
        public async Task<long> CreateSparePartFromPurchaseAsync(PurchaseItem purchaseItem, int quantity, long locationId)
        {
            _logger.LogInformation("从采购物品创建备品备件，物品ID: {ItemId}, 数量: {Quantity}", purchaseItem.Id, quantity);

            // 检查是否已存在相同规格的备件
            var existingSparePart = await _context.SpareParts
                .FirstOrDefaultAsync(sp => sp.Name == purchaseItem.ItemName &&
                                          sp.Specification == purchaseItem.Specification &&
                                          sp.LocationId == locationId);

            if (existingSparePart != null)
            {
                // 更新现有备件库存
                existingSparePart.StockQuantity += quantity;
                existingSparePart.UpdatedAt = DateTime.Now;

                // 创建入库记录
                var inboundTransaction = new SparePartTransaction
                {
                    PartId = existingSparePart.Id,
                    LocationId = locationId,
                    Type = 1, // 入库
                    Quantity = quantity,
                    OperationTime = DateTime.Now,
                    OperatorUserId = 1, // TODO: 从当前用户获取
                    ReasonType = 1, // 采购入库
                    ReferenceNumber = $"采购订单-{purchaseItem.PurchaseOrderId}",
                    Reason = $"采购物品转入备件库，采购物品ID: {purchaseItem.Id}",
                    StockAfter = existingSparePart.StockQuantity + quantity
                };

                _context.SparePartTransactions.Add(inboundTransaction);
                await _context.SaveChangesAsync();

                return existingSparePart.Id;
            }
            else
            {
                // 创建新备件
                var sparePart = new SparePart
                {
                    Code = GenerateSparePartCode(),
                    Name = purchaseItem.ItemName,
                    TypeId = purchaseItem.AssetTypeId ?? 1, // 使用采购物品的资产类型ID，默认为1
                    Specification = purchaseItem.Specification,
                    Unit = "个", // 默认单位
                    StockQuantity = quantity,
                    WarningThreshold = 10, // 默认预警阈值
                    MinStock = 5, // 默认最小库存
                    LocationId = locationId,
                    Price = purchaseItem.UnitPrice,
                    Remarks = $"从采购订单 {purchaseItem.PurchaseOrderId} 转入",
                    CreatedAt = DateTime.Now
                };

                _context.SpareParts.Add(sparePart);
                await _context.SaveChangesAsync();

                // 创建入库记录
                var inboundTransaction = new SparePartTransaction
                {
                    PartId = sparePart.Id,
                    LocationId = locationId,
                    Type = 1, // 入库
                    Quantity = quantity,
                    OperationTime = DateTime.Now,
                    OperatorUserId = 1, // TODO: 从当前用户获取
                    ReasonType = 1, // 采购入库
                    ReferenceNumber = $"采购订单-{purchaseItem.PurchaseOrderId}",
                    Reason = $"新建备件并入库，采购物品ID: {purchaseItem.Id}",
                    StockAfter = quantity
                };

                _context.SparePartTransactions.Add(inboundTransaction);
                await _context.SaveChangesAsync();

                return sparePart.Id;
            }
        }

        /// <summary>
        /// 从采购物品创建资产
        /// </summary>
        public async Task<List<int>> CreateAssetsFromPurchaseAsync(PurchaseItem purchaseItem, int quantity, int locationId)
        {
            _logger.LogInformation("从采购物品创建资产，物品ID: {ItemId}, 数量: {Quantity}", purchaseItem.Id, quantity);

            var createdAssetIds = new List<int>();

            for (int i = 0; i < quantity; i++)
            {
                var asset = new Asset
                {
                    AssetCode = GenerateAssetCode(),
                    Name = purchaseItem.ItemName,
                    AssetTypeId = purchaseItem.AssetTypeId ?? 1,
                    Model = purchaseItem.Specification,
                    LocationId = locationId,
                    Price = purchaseItem.UnitPrice,
                    PurchaseDate = DateTime.Now,
                    Status = 1, // 正常状态
                    Notes = $"从采购订单 {purchaseItem.PurchaseOrderId} 创建",
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now
                };

                _context.Assets.Add(asset);
                await _context.SaveChangesAsync();

                createdAssetIds.Add(asset.Id);

                // 注意：AssetReceive表是用于记录采购入库单，不是单个资产记录
                // 这里不创建AssetReceive记录，因为表结构不匹配
                // 如果需要记录资产接收历史，应该创建专门的AssetHistory记录
            }

            await _context.SaveChangesAsync();
            return createdAssetIds;
        }

        /// <summary>
        /// 生成资产编号
        /// </summary>
        private string GenerateAssetCode()
        {
            return $"IT-{DateTime.Now:yyyyMMdd}-{DateTime.Now.Ticks % 10000:D4}";
        }

        /// <summary>
        /// 合并重复的采购物品
        /// 相同物品名称、规格、单价的物品将被合并，数量相加
        /// </summary>
        /// <param name="items">原始物品列表</param>
        /// <returns>合并后的物品列表</returns>
        private List<CreatePurchaseItemRequest> MergeDuplicateItems(List<CreatePurchaseItemRequest> items)
        {
            _logger.LogInformation("开始合并重复采购物品，原始数量: {Count}", items.Count);

            // 使用字典按照关键字段分组
            var itemGroups = new Dictionary<string, List<CreatePurchaseItemRequest>>();

            foreach (var item in items)
            {
                // 生成唯一键：物品名称 + 规格 + 单价 + 资产类型
                var key = $"{item.ItemName?.Trim()}|{item.Specification?.Trim()}|{item.UnitPrice}|{item.AssetTypeId}";

                if (!itemGroups.ContainsKey(key))
                {
                    itemGroups[key] = new List<CreatePurchaseItemRequest>();
                }

                itemGroups[key].Add(item);
            }

            var mergedItems = new List<CreatePurchaseItemRequest>();

            foreach (var group in itemGroups.Values)
            {
                if (group.Count == 1)
                {
                    // 没有重复，直接添加
                    mergedItems.Add(group[0]);
                }
                else
                {
                    // 有重复，合并数量
                    var firstItem = group[0];
                    var totalQuantity = group.Sum(g => g.Quantity);

                    _logger.LogInformation("合并重复物品: {ItemName}, 原始记录数: {Count}, 总数量: {TotalQuantity}",
                        firstItem.ItemName, group.Count, totalQuantity);

                    // 合并备注信息
                    var mergedNotes = string.Join("; ", group
                        .Where(g => !string.IsNullOrWhiteSpace(g.Notes))
                        .Select(g => g.Notes)
                        .Distinct());

                    var mergedItem = new CreatePurchaseItemRequest
                    {
                        ItemName = firstItem.ItemName,
                        ItemCode = firstItem.ItemCode,
                        Specification = firstItem.Specification,
                        AssetTypeId = firstItem.AssetTypeId,
                        UnitPrice = firstItem.UnitPrice,
                        Quantity = totalQuantity,
                        Notes = string.IsNullOrWhiteSpace(mergedNotes) ? firstItem.Notes :
                               $"{firstItem.Notes}{(string.IsNullOrWhiteSpace(firstItem.Notes) ? "" : "; ")}合并自{group.Count}个重复项{(string.IsNullOrWhiteSpace(mergedNotes) ? "" : ": " + mergedNotes)}"
                    };

                    mergedItems.Add(mergedItem);
                }
            }

            _logger.LogInformation("物品合并完成，合并后数量: {MergedCount}", mergedItems.Count);
            return mergedItems;
        }

        /// <summary>
        /// 生成备件编号
        /// </summary>
        private string GenerateSparePartCode()
        {
            return $"SP-{DateTime.Now:yyyyMMdd}-{DateTime.Now.Ticks % 10000:D4}";
        }
    }
}
