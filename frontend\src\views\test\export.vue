<template>
  <div class="export-test-container">
    <el-card class="export-card">
      <template #header>
        <div class="card-header">
          <h2>导出功能测试</h2>
          <p>用于测试不同格式的Excel导出功能</p>
        </div>
      </template>
      
      <div class="export-options">
        <el-button 
          type="primary" 
          @click="handleExportXlsx" 
          :loading="isXlsxLoading"
          size="large">
          导出XLSX格式 (Excel 2007+)
        </el-button>
        
        <el-button 
          type="success" 
          @click="handleExportXls" 
          :loading="isXlsLoading"
          size="large">
          导出XLS格式 (Excel 97-2003)
        </el-button>
      </div>
      
      <div class="result-section" v-if="lastResult">
        <h3>最近操作结果:</h3>
        <div :class="['result-message', lastResult.success ? 'success' : 'error']">
          {{ lastResult.message }}
        </div>
        <div v-if="lastResult.details" class="result-details">
          <pre>{{ lastResult.details }}</pre>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import axios from 'axios'
import systemConfig from '@/config/system'
import testExportApi from '@/api/testExport'

const isXlsxLoading = ref(false)
const isXlsLoading = ref(false)
const lastResult = ref(null)

// 获取token
const getToken = () => {
  try {
    const userInfo = localStorage.getItem('user')
    if (userInfo) {
      const user = JSON.parse(userInfo)
      return user.token
    }
  } catch (e) {
    console.error('获取token失败:', e)
  }
  return ''
}

// 直接使用axios下载文件
const downloadFile = async (url, fileName, fileType) => {
  try {
    const token = getToken()
    const response = await axios({
      method: 'get',
      url: `${systemConfig.apiBaseUrl}${url}`,
      responseType: 'blob',
      headers: {
        'Authorization': token ? `Bearer ${token}` : '',
        'Accept': fileType === 'xlsx' 
          ? 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
          : 'application/vnd.ms-excel'
      }
    })
    
    // 创建Blob对象
    const blob = new Blob([response.data], { 
      type: fileType === 'xlsx' 
        ? 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        : 'application/vnd.ms-excel'
    })
    
    // 创建下载链接
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = fileName
    document.body.appendChild(link)
    
    // 触发下载
    link.click()
    
    // 清理资源
    setTimeout(() => {
      document.body.removeChild(link)
      URL.revokeObjectURL(url)
    }, 100)
    
    return {
      success: true,
      data: blob,
      size: blob.size
    }
  } catch (error) {
    console.error('下载文件失败:', error)
    throw error
  }
}

// 处理XLSX格式导出
const handleExportXlsx = async () => {
  isXlsxLoading.value = true
  lastResult.value = null
  
  try {
    // 显示加载中提示
    const loadingMessage = ElMessage({
      message: '正在准备XLSX导出，请稍候...',
      duration: 0,
      type: 'info'
    })
    
    try {
      // 调用导出API - 修复URL路径，移除重复的api前缀
      const response = await axios({
        method: 'get',
        url: `${systemConfig.apiBaseUrl}/ExportTest/xlsx`,
        responseType: 'blob',
        headers: {
          'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        }
      })
      
      // 关闭加载提示
      loadingMessage.close()
      
      // 检查响应
      const blob = response.data
      if (!blob || !(blob instanceof Blob)) {
        throw new Error('导出失败：服务器响应格式不正确')
      }
      
      // 检查Blob类型和大小
      if (blob.size === 0) {
        throw new Error('导出失败：收到的文件为空')
      }
      
      // 设置文件名
      const fileName = `测试导出_XLSX_${new Date().toISOString().substring(0, 10)}.xlsx`
      
      // 创建下载链接并触发下载
      const blobUrl = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = blobUrl
      link.setAttribute('download', fileName)
      document.body.appendChild(link)
      link.click()
      
      // 清理资源
      setTimeout(() => {
        document.body.removeChild(link)
        URL.revokeObjectURL(blobUrl)
      }, 100)
      
      // 记录成功结果
      lastResult.value = {
        success: true,
        message: '成功导出XLSX格式文件',
        details: `文件大小: ${(blob.size / 1024).toFixed(2)} KB\n文件类型: ${blob.type}`
      }
      
      ElMessage.success('XLSX导出成功')
    } catch (error) {
      // 关闭可能还存在的加载提示
      loadingMessage.close()
      throw error
    }
  } catch (error) {
    console.error('XLSX导出出错:', error)
    
    // 记录错误结果
    lastResult.value = {
      success: false,
      message: 'XLSX导出失败: ' + (error.message || '未知错误'),
      details: error.stack
    }
    
    ElMessage.error('XLSX导出失败: ' + (error.message || '未知错误'))
  } finally {
    isXlsxLoading.value = false
  }
}

// 处理XLS格式导出
const handleExportXls = async () => {
  isXlsLoading.value = true
  lastResult.value = null
  
  try {
    // 显示加载中提示
    const loadingMessage = ElMessage({
      message: '正在准备XLS导出，请稍候...',
      duration: 0,
      type: 'info'
    })
    
    try {
      // 调用导出API - 修复URL路径，移除重复的api前缀
      const response = await axios({
        method: 'get',
        url: `${systemConfig.apiBaseUrl}/ExportTest/xls`,
        responseType: 'blob',
        headers: {
          'Accept': 'application/vnd.ms-excel'
        }
      })
      
      // 关闭加载提示
      loadingMessage.close()
      
      // 检查响应
      const blob = response.data
      if (!blob || !(blob instanceof Blob)) {
        throw new Error('导出失败：服务器响应格式不正确')
      }
      
      // 检查Blob类型和大小
      if (blob.size === 0) {
        throw new Error('导出失败：收到的文件为空')
      }
      
      // 设置文件名
      const fileName = `测试导出_XLS_${new Date().toISOString().substring(0, 10)}.xls`
      
      // 创建下载链接并触发下载
      const blobUrl = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = blobUrl
      link.setAttribute('download', fileName)
      document.body.appendChild(link)
      link.click()
      
      // 清理资源
      setTimeout(() => {
        document.body.removeChild(link)
        URL.revokeObjectURL(blobUrl)
      }, 100)
      
      // 记录成功结果
      lastResult.value = {
        success: true,
        message: '成功导出XLS格式文件',
        details: `文件大小: ${(blob.size / 1024).toFixed(2)} KB\n文件类型: ${blob.type}`
      }
      
      ElMessage.success('XLS导出成功')
    } catch (error) {
      // 关闭可能还存在的加载提示
      loadingMessage.close()
      throw error
    }
  } catch (error) {
    console.error('XLS导出出错:', error)
    
    // 记录错误结果
    lastResult.value = {
      success: false,
      message: 'XLS导出失败: ' + (error.message || '未知错误'),
      details: error.stack
    }
    
    ElMessage.error('XLS导出失败: ' + (error.message || '未知错误'))
  } finally {
    isXlsLoading.value = false
  }
}
</script>

<style scoped>
.export-test-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.export-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.card-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.card-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.export-options {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.result-section {
  margin-top: 30px;
  padding: 15px;
  border-radius: 4px;
  background-color: #f8f9fa;
}

.result-section h3 {
  margin: 0 0 10px 0;
  font-size: 16px;
  color: #303133;
}

.result-message {
  padding: 10px;
  border-radius: 4px;
  font-weight: bold;
}

.result-message.success {
  background-color: #f0f9eb;
  color: #67c23a;
}

.result-message.error {
  background-color: #fef0f0;
  color: #f56c6c;
}

.result-details {
  margin-top: 10px;
  background-color: #fff;
  padding: 10px;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.result-details pre {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-all;
  font-family: monospace;
  font-size: 12px;
  line-height: 1.5;
}
</style> 