// IT资产管理系统 - 位置层级配置服务接口
// 文件路径: /Core/Location/ILocationHierarchyService.cs
// 功能: 通过JSON配置文件定义和管理位置层级结构的接口

using System.Threading.Tasks;

namespace ItAssetsSystem.Core.Location
{
    /// <summary>
    /// 位置层级服务接口
    /// </summary>
    public interface ILocationHierarchyService
    {
        /// <summary>
        /// 获取位置层级配置
        /// </summary>
        Task<LocationHierarchyConfig> GetHierarchyConfigAsync();
        
        /// <summary>
        /// 保存位置层级配置
        /// </summary>
        Task<bool> SaveHierarchyConfigAsync(LocationHierarchyConfig config);
        
        /// <summary>
        /// 同步位置层级结构到数据库
        /// </summary>
        Task<bool> SyncHierarchyToDbAsync();
        
        /// <summary>
        /// 从数据库重建位置层级结构
        /// </summary>
        Task<bool> RebuildHierarchyFromDbAsync();
    }
} 