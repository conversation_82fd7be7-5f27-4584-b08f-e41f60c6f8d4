/**
 * 航空航天级IT资产管理系统 - 每日签到面板组件
 * 文件路径: src/components/Tasks/SignInPanel.vue
 * 功能描述: 实现每日签到功能，获取积分和奖励
 */

<template>
  <div class="sign-in-panel">
    <!-- 当前签到状态 -->
    <div class="sign-in-status">
      <div class="current-month">{{ currentMonth }}月签到日历</div>
      <div class="streak-info">
        <el-tag type="success" effect="dark">
          <el-icon><Calendar /></el-icon>
          <span>已连续签到 {{ signInData.streak }} 天</span>
        </el-tag>
      </div>
    </div>
    
    <!-- 签到日历 -->
    <div class="calendar-wrapper">
      <div class="weekday-header">
        <div v-for="day in weekDays" :key="day" class="weekday-item">{{ day }}</div>
      </div>
      
      <div class="calendar-grid">
        <!-- 填充前置空白日期 -->
        <div 
          v-for="i in firstDayOfMonth" 
          :key="`empty-${i}`" 
          class="calendar-day empty"
        ></div>
        
        <!-- 实际日期 -->
        <div 
          v-for="day in daysInMonth" 
          :key="day"
          class="calendar-day"
          :class="{
            'is-signed': isSignedDay(day),
            'is-today': isToday(day),
            'is-future': isFutureDay(day),
          }"
        >
          <span class="day-number">{{ day }}</span>
          <el-icon v-if="isSignedDay(day)" class="sign-icon"><Check /></el-icon>
        </div>
      </div>
    </div>
    
    <!-- 签到奖励提示 -->
    <div class="reward-preview">
      <h4>今日签到可获得：</h4>
      <div class="reward-items">
        <div class="reward-item">
          <el-icon><Star /></el-icon>
          <span>{{ todayPoints }} 积分</span>
        </div>
        <div v-if="todayItem" class="reward-item">
          <el-icon><Gift /></el-icon>
          <span>{{ todayItem.name }} x{{ todayItem.count }}</span>
        </div>
      </div>
      
      <div class="streaks-rewards">
        <div class="streak-title">连续签到奖励</div>
        <el-steps :active="getStreakLevel()" finish-status="success" simple>
          <el-step v-for="(reward, index) in streakRewards" :key="index" :title="`${reward.days}天`">
            <template #description>
              <div class="streak-reward-desc">
                <span>+{{ reward.points }}积分</span>
                <span v-if="reward.item">{{ reward.item }}</span>
              </div>
            </template>
          </el-step>
        </el-steps>
      </div>
    </div>
    
    <!-- 签到按钮 -->
    <div class="sign-in-action">
      <el-button 
        type="primary" 
        size="large" 
        :disabled="signInData.todaySigned"
        @click="handleSignIn"
        :loading="signing"
        round
      >
        {{ signInData.todaySigned ? '今日已签到' : '立即签到' }}
      </el-button>
      <p class="sign-tip" v-if="!signInData.todaySigned">每天签到可获得积分和道具奖励！</p>
      <p class="sign-tip" v-else>明天再来继续签到，可获得更多奖励！</p>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Calendar, Check, Star, Gift } from '@element-plus/icons-vue'
import { taskApi } from '@/api/task'

// 签到数据
const signInData = ref({
  streak: 3,
  todaySigned: false,
  signedDays: [1, 2, 3, 5, 8, 12, 15]
})

// 签到状态
const signing = ref(false)

// 获取当前日期信息
const today = new Date()
const currentYear = today.getFullYear()
const currentMonth = today.getMonth() + 1
const currentDay = today.getDate()

// 计算当月天数
const daysInMonth = computed(() => {
  return new Date(currentYear, currentMonth, 0).getDate()
})

// 计算当月第一天是星期几
const firstDayOfMonth = computed(() => {
  return new Date(currentYear, currentMonth - 1, 1).getDay()
})

// 星期几的标签
const weekDays = ['日', '一', '二', '三', '四', '五', '六']

// 奖励配置
const todayPoints = computed(() => {
  // 基础积分 + 连续签到加成
  return 2 + Math.min(3, Math.floor(signInData.value.streak / 7))
})

const todayItem = computed(() => {
  // 根据连续签到天数决定今日奖励
  if (signInData.value.streak >= 7 && signInData.value.streak < 14) {
    return { name: '青铜弹头', count: 1 }
  } else if (signInData.value.streak >= 14 && signInData.value.streak < 21) {
    return { name: '白银弹头', count: 1 }
  } else if (signInData.value.streak >= 21) {
    return { name: '效率药水', count: 1 }
  }
  return null
})

// 连续签到奖励档位
const streakRewards = [
  { days: 3, points: 3 },
  { days: 7, points: 5, item: '青铜弹头' },
  { days: 14, points: 10, item: '白银弹头' },
  { days: 21, points: 15, item: '效率药水' }
]

// 获取月份名称
const currentMonth = computed(() => {
  return currentMonth
})

// 判断是否已签到的日期
const isSignedDay = (day) => {
  return signInData.value.signedDays.includes(day)
}

// 判断是否今天
const isToday = (day) => {
  return day === currentDay
}

// 判断是否未来日期
const isFutureDay = (day) => {
  return day > currentDay
}

// 获取当前在哪个连续签到档位
const getStreakLevel = () => {
  const streak = signInData.value.streak
  
  if (streak >= 21) return 4
  if (streak >= 14) return 3
  if (streak >= 7) return 2
  if (streak >= 3) return 1
  
  return 0
}

// 签到操作
const handleSignIn = async () => {
  if (signing.value || signInData.value.todaySigned) return
  
  signing.value = true
  try {
    // 调用签到API
    // const res = await taskApi.dailySignIn()
    // if (res.success) {
      // 签到成功处理
      signInData.value.todaySigned = true
      signInData.value.streak++
      signInData.value.signedDays.push(currentDay)
      
      // 触发父组件事件
      emit('signed', {
        points: todayPoints.value,
        days: signInData.value.streak,
        items: todayItem.value ? [todayItem.value] : []
      })
      
      ElMessage.success('签到成功！')
    // } else {
    //   ElMessage.error(res.message || '签到失败')
    // }
  } catch (error) {
    console.error('签到失败', error)
    ElMessage.error('签到失败，请稍后重试')
  } finally {
    signing.value = false
  }
}

// 获取签到信息
const fetchSignInInfo = async () => {
  try {
    // 调用获取签到信息API
    // const res = await taskApi.getSignInInfo()
    // if (res.success) {
    //   signInData.value = res.data
    // }
  } catch (error) {
    console.error('获取签到信息失败', error)
  }
}

// 定义组件事件
const emit = defineEmits(['signed'])

// 初始化
onMounted(() => {
  fetchSignInInfo()
})
</script>

<style lang="scss" scoped>
.sign-in-panel {
  padding: 10px 0;
  
  .sign-in-status {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    
    .current-month {
      font-size: 18px;
      font-weight: 600;
      color: #303133;
    }
    
    .streak-info {
      .el-tag {
        display: flex;
        align-items: center;
        
        .el-icon {
          margin-right: 6px;
        }
      }
    }
  }
  
  .calendar-wrapper {
    margin-bottom: 24px;
    
    .weekday-header {
      display: grid;
      grid-template-columns: repeat(7, 1fr);
      text-align: center;
      margin-bottom: 8px;
      
      .weekday-item {
        font-size: 14px;
        color: #606266;
        padding: 8px 0;
      }
    }
    
    .calendar-grid {
      display: grid;
      grid-template-columns: repeat(7, 1fr);
      gap: 8px;
      
      .calendar-day {
        position: relative;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 6px;
        border: 1px solid #ebeef5;
        transition: all 0.3s;
        
        .day-number {
          font-size: 14px;
          color: #303133;
        }
        
        .sign-icon {
          position: absolute;
          bottom: -2px;
          right: -2px;
          background: #67c23a;
          color: white;
          border-radius: 50%;
          font-size: 10px;
          padding: 2px;
        }
        
        &.empty {
          border: none;
        }
        
        &.is-signed {
          background: #f0f9eb;
          border-color: #95d475;
          
          .day-number {
            color: #67c23a;
          }
        }
        
        &.is-today:not(.is-signed) {
          background: #ecf5ff;
          border-color: #a0cfff;
          
          .day-number {
            color: #409eff;
            font-weight: bold;
          }
        }
        
        &.is-future {
          background: #f5f7fa;
          
          .day-number {
            color: #c0c4cc;
          }
        }
      }
    }
  }
  
  .reward-preview {
    margin-bottom: 24px;
    
    h4 {
      margin-top: 0;
      margin-bottom: 12px;
      font-size: 16px;
      color: #303133;
    }
    
    .reward-items {
      display: flex;
      gap: 16px;
      margin-bottom: 16px;
      
      .reward-item {
        display: flex;
        align-items: center;
        padding: 8px 16px;
        background: #f5f7fa;
        border-radius: 6px;
        
        .el-icon {
          margin-right: 6px;
          font-size: 16px;
          
          &.star-icon {
            color: #F7BA2A;
          }
          
          &.gift-icon {
            color: #F56C6C;
          }
        }
      }
    }
    
    .streaks-rewards {
      margin-top: 16px;
      
      .streak-title {
        margin-bottom: 12px;
        font-size: 15px;
        color: #303133;
      }
      
      .streak-reward-desc {
        display: flex;
        flex-direction: column;
        gap: 4px;
        font-size: 12px;
      }
    }
  }
  
  .sign-in-action {
    display: flex;
    flex-direction: column;
    align-items: center;
    
    .el-button {
      width: 180px;
    }
    
    .sign-tip {
      margin-top: 12px;
      color: #909399;
      font-size: 13px;
    }
  }
}
</style> 