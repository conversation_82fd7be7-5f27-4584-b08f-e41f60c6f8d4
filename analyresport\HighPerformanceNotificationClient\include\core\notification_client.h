// 主通知客户端类
#pragma once

#include "common/types.h"
#include "common/constants.h"
#include <memory>
#include <atomic>
#include <thread>
#include <mutex>

namespace notification {

// 前向声明
class UDPClient;
class WebSocketClient;
class AuthManager;
class ConfigManager;
class TrayManager;
class Logger;
class PerformanceMonitor;

class NotificationClient {
public:
    NotificationClient();
    ~NotificationClient();

    // 生命周期管理
    bool initialize(const String& config_path = "config/config.json");
    bool start();
    void stop();
    bool isRunning() const { return running_.load(); }

    // 连接管理
    bool connect();
    void disconnect();
    ConnectionStatus getConnectionStatus() const { return connection_status_.load(); }
    ProtocolType getCurrentProtocol() const { return current_protocol_.load(); }

    // 认证管理
    bool login(const String& username, const String& password);
    void logout();
    AuthStatus getAuthStatus() const { return auth_status_.load(); }

    // 消息发送
    bool sendMessage(const NotificationMessage& message);
    bool sendHeartbeat();

    // 回调设置
    void setNotificationCallback(NotificationCallback callback);
    void setConnectionStatusCallback(ConnectionStatusCallback callback);
    void setAuthStatusCallback(AuthStatusCallback callback);
    void setErrorCallback(ErrorCallback callback);
    void setStatisticsCallback(StatisticsCallback callback);

    // 统计信息
    Statistics getStatistics() const;
    PerformanceMetrics getPerformanceMetrics() const;

    // 配置管理
    bool reloadConfig();
    bool saveConfig();

    // UI控制
    void showMainWindow();
    void hideMainWindow();
    void showNotification(const NotificationMessage& message);

private:
    // 初始化子系统
    bool initializeLogging();
    bool initializeNetworking();
    bool initializeAuthentication();
    bool initializeUI();

    // 网络处理
    void handleConnectionStatusChange(ConnectionStatus status, ProtocolType protocol);
    void handleAuthStatusChange(AuthStatus status);
    void handleIncomingMessage(const NotificationMessage& message);
    void handleError(const String& error);

    // 自动重连逻辑
    void startReconnectionLoop();
    void stopReconnectionLoop();
    void reconnectionWorker();

    // 心跳机制
    void startHeartbeat();
    void stopHeartbeat();
    void heartbeatWorker();

    // 统计更新
    void startStatisticsUpdate();
    void stopStatisticsUpdate();
    void statisticsWorker();

    // 协议切换
    void switchToUDP();
    void switchToWebSocket();
    bool tryConnectProtocol(ProtocolType protocol);

    // 内部状态
    std::atomic<bool> running_{false};
    std::atomic<bool> initialized_{false};
    std::atomic<ConnectionStatus> connection_status_{ConnectionStatus::DISCONNECTED};
    std::atomic<AuthStatus> auth_status_{AuthStatus::NOT_AUTHENTICATED};
    std::atomic<ProtocolType> current_protocol_{ProtocolType::UDP};

    // 子系统组件
    std::unique_ptr<UDPClient> udp_client_;
    std::unique_ptr<WebSocketClient> websocket_client_;
    std::unique_ptr<AuthManager> auth_manager_;
    std::unique_ptr<ConfigManager> config_manager_;
    std::unique_ptr<TrayManager> tray_manager_;
    std::unique_ptr<Logger> logger_;
    std::unique_ptr<PerformanceMonitor> performance_monitor_;

    // 工作线程
    std::thread reconnection_thread_;
    std::thread heartbeat_thread_;
    std::thread statistics_thread_;

    // 线程同步
    mutable std::mutex callback_mutex_;
    mutable std::mutex statistics_mutex_;
    std::atomic<bool> stop_reconnection_{false};
    std::atomic<bool> stop_heartbeat_{false};
    std::atomic<bool> stop_statistics_{false};

    // 回调函数
    NotificationCallback notification_callback_;
    ConnectionStatusCallback connection_status_callback_;
    AuthStatusCallback auth_status_callback_;
    ErrorCallback error_callback_;
    StatisticsCallback statistics_callback_;

    // 统计数据
    mutable Statistics statistics_;
    TimePoint start_time_;
    std::atomic<uint64_t> reconnection_attempts_{0};

    // 重连参数
    int current_retry_count_ = 0;
    Duration current_retry_interval_{std::chrono::seconds(1)};
};

} // namespace notification