// IT资产管理系统 - 人员实体
// 文件路径: /Models/Entities/Personnel.cs
// 功能: 定义人员实体，用于记录位置使用人和部门负责人

using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ItAssetsSystem.Models.Entities
{
    /// <summary>
    /// 人员实体
    /// </summary>
    [Table("personnel")]
    public class Personnel
    {
        /// <summary>
        /// 人员ID
        /// </summary>
        [Column("id")]
        public int Id { get; set; }
        
        /// <summary>
        /// 姓名
        /// </summary>
        [Column("name")]
        public string Name { get; set; }
        
        /// <summary>
        /// 职位
        /// </summary>
        [Column("position")]
        public string Position { get; set; }
        
        /// <summary>
        /// 联系方式
        /// </summary>
        [Column("contact")]
        public string Contact { get; set; }
        
        /// <summary>
        /// 部门ID
        /// </summary>
        [Column("department_id")]
        public int? DepartmentId { get; set; }
        
        /// <summary>
        /// 工号
        /// </summary>
        [Column("employee_code")]
        public string EmployeeCode { get; set; }
        
        /// <summary>
        /// 是否部门负责人
        /// </summary>
        [NotMapped] // 数据库中实际没有这个字段
        public bool IsDepartmentManager { get; set; }
        
        /// <summary>
        /// 创建时间
        /// </summary>
        [Column("created_at")]
        public DateTime CreatedAt { get; set; }
        
        /// <summary>
        /// 更新时间
        /// </summary>
        [Column("updated_at")]
        public DateTime? UpdatedAt { get; set; }
        
        /// <summary>
        /// 所属部门
        /// </summary>
        [ForeignKey("DepartmentId")]
        public virtual Department Department { get; set; }
        
        /// <summary>
        /// 位置关联
        /// </summary>
        public virtual ICollection<LocationUser> LocationUsers { get; set; }
    }
} 