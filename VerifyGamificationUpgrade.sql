-- =====================================================
-- 游戏化系统升级验证脚本
-- =====================================================

-- 1. 检查新表是否创建成功
SELECT 'user_levels' as table_name, COUNT(*) as record_count FROM user_levels
UNION ALL
SELECT 'user_level_history' as table_name, COUNT(*) as record_count FROM user_level_history
UNION ALL
SELECT 'gamification_items' as table_name, COUNT(*) as record_count FROM gamification_items
UNION ALL
SELECT 'user_items' as table_name, COUNT(*) as record_count FROM user_items
UNION ALL
SELECT 'active_item_effects' as table_name, COUNT(*) as record_count FROM active_item_effects
UNION ALL
SELECT 'daily_limits' as table_name, COUNT(*) as record_count FROM daily_limits;

-- 2. 检查gamification_userstats表是否添加了新字段
DESCRIBE gamification_userstats;

-- 3. 检查等级配置数据
SELECT level, name, required_xp, reward_coins, reward_diamonds, color 
FROM user_levels 
ORDER BY level;

-- 4. 检查道具配置数据
SELECT name, code, type, rarity, effect, drop_rate 
FROM gamification_items 
ORDER BY rarity, name;

-- 5. 检查存储过程是否创建成功
SHOW PROCEDURE STATUS WHERE Name IN ('CheckAndProcessLevelUp', 'GrantRandomItem');

-- 6. 检查函数是否创建成功
SHOW FUNCTION STATUS WHERE Name = 'CanEarnPoints';

-- 7. 检查视图是否创建成功
SHOW FULL TABLES WHERE Table_type = 'VIEW' AND Tables_in_itassets IN ('user_growth_timeline', 'enhanced_leaderboard');

-- 8. 测试存储过程（使用现有用户）
-- 注意：这里使用用户ID 6进行测试，请根据实际情况调整
SET @test_user_id = 6;

-- 检查用户当前状态
SELECT 
    CoreUserId,
    CurrentXP,
    CurrentLevel,
    PointsBalance,
    CoinsBalance,
    DiamondsBalance
FROM gamification_userstats 
WHERE CoreUserId = @test_user_id;

-- 9. 检查用户等级计算是否正确
SELECT 
    us.CoreUserId,
    us.CurrentXP,
    us.CurrentLevel,
    ul.name as current_level_name,
    ul.required_xp as current_level_xp,
    next_ul.level as next_level,
    next_ul.name as next_level_name,
    next_ul.required_xp as next_level_xp,
    CASE 
        WHEN next_ul.required_xp IS NULL THEN 100
        ELSE ROUND(((us.CurrentXP - ul.required_xp) / (next_ul.required_xp - ul.required_xp)) * 100, 2)
    END as level_progress
FROM gamification_userstats us
LEFT JOIN user_levels ul ON us.CurrentLevel = ul.level
LEFT JOIN user_levels next_ul ON us.CurrentLevel + 1 = next_ul.level
WHERE us.CoreUserId = @test_user_id;

-- 10. 检查排行榜视图
SELECT * FROM enhanced_leaderboard LIMIT 5;

-- 11. 检查成长时间线视图
SELECT * FROM user_growth_timeline 
WHERE user_id = @test_user_id 
ORDER BY timeline_date DESC 
LIMIT 5;

-- 12. 验证数据完整性
-- 检查是否有用户没有对应的等级配置
SELECT 
    us.CoreUserId,
    us.CurrentLevel,
    ul.name
FROM gamification_userstats us
LEFT JOIN user_levels ul ON us.CurrentLevel = ul.level
WHERE ul.level IS NULL;

-- 13. 检查外键约束
SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    CONSTRAINT_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM information_schema.KEY_COLUMN_USAGE
WHERE TABLE_SCHEMA = 'itassets'
AND TABLE_NAME IN ('user_items', 'active_item_effects', 'user_level_history')
AND REFERENCED_TABLE_NAME IS NOT NULL;

-- 14. 检查索引
SELECT 
    TABLE_NAME,
    INDEX_NAME,
    COLUMN_NAME,
    NON_UNIQUE
FROM information_schema.STATISTICS
WHERE TABLE_SCHEMA = 'itassets'
AND TABLE_NAME IN ('user_levels', 'gamification_items', 'user_items', 'active_item_effects', 'daily_limits')
ORDER BY TABLE_NAME, INDEX_NAME, SEQ_IN_INDEX;

-- 15. 最终验证报告
SELECT 
    '游戏化系统升级验证' as report_title,
    CASE 
        WHEN (SELECT COUNT(*) FROM user_levels) >= 12 THEN '✅ 等级配置正常'
        ELSE '❌ 等级配置异常'
    END as level_config_status,
    CASE 
        WHEN (SELECT COUNT(*) FROM gamification_items) >= 9 THEN '✅ 道具配置正常'
        ELSE '❌ 道具配置异常'
    END as item_config_status,
    CASE 
        WHEN (SELECT COUNT(*) FROM information_schema.ROUTINES WHERE ROUTINE_SCHEMA = 'itassets' AND ROUTINE_NAME = 'CheckAndProcessLevelUp') > 0 THEN '✅ 升级存储过程正常'
        ELSE '❌ 升级存储过程异常'
    END as levelup_procedure_status,
    CASE 
        WHEN (SELECT COUNT(*) FROM information_schema.ROUTINES WHERE ROUTINE_SCHEMA = 'itassets' AND ROUTINE_NAME = 'GrantRandomItem') > 0 THEN '✅ 道具掉落存储过程正常'
        ELSE '❌ 道具掉落存储过程异常'
    END as item_drop_procedure_status,
    CASE 
        WHEN (SELECT COUNT(*) FROM information_schema.VIEWS WHERE TABLE_SCHEMA = 'itassets' AND TABLE_NAME IN ('user_growth_timeline', 'enhanced_leaderboard')) = 2 THEN '✅ 视图创建正常'
        ELSE '❌ 视图创建异常'
    END as views_status;
