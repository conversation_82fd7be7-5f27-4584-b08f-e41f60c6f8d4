// File: Models/Entities/Gamification/Achievement.cs
// Description: 成就系统实体

using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ItAssetsSystem.Models.Entities.Gamification
{
    /// <summary>
    /// 成就定义表
    /// </summary>
    [Table("gamification_achievements")]
    public class Achievement
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Key]
        [Column("id")]
        public int Id { get; set; }

        /// <summary>
        /// 成就代码（唯一标识）
        /// </summary>
        [Required]
        [MaxLength(50)]
        [Column("code")]
        public string Code { get; set; } = string.Empty;

        /// <summary>
        /// 成就名称
        /// </summary>
        [Required]
        [MaxLength(100)]
        [Column("name")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 成就描述
        /// </summary>
        [Column("description")]
        public string? Description { get; set; }

        /// <summary>
        /// 图标URL
        /// </summary>
        [MaxLength(255)]
        [Column("icon")]
        public string? Icon { get; set; }

        /// <summary>
        /// 达成条件值
        /// </summary>
        [Column("required_value")]
        public int RequiredValue { get; set; }

        /// <summary>
        /// 关联指标
        /// </summary>
        [Required]
        [MaxLength(50)]
        [Column("metric")]
        public string Metric { get; set; } = string.Empty;

        /// <summary>
        /// 成就分类
        /// </summary>
        [MaxLength(50)]
        [Column("category")]
        public string Category { get; set; } = string.Empty;

        /// <summary>
        /// 成就等级（1=青铜，2=白银，3=黄金，4=钻石）
        /// </summary>
        [Column("level")]
        public int Level { get; set; } = 1;

        /// <summary>
        /// 奖励积分
        /// </summary>
        [Column("reward_points")]
        public int PointsReward { get; set; } = 0;

        /// <summary>
        /// 奖励金币
        /// </summary>
        [Column("reward_coins")]
        public int CoinsReward { get; set; } = 0;

        /// <summary>
        /// 奖励钻石
        /// </summary>
        [Column("reward_diamonds")]
        public int DiamondsReward { get; set; } = 0;

        /// <summary>
        /// 是否启用
        /// </summary>
        [Column("is_active")]
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// 排序顺序
        /// </summary>
        [Column("sort_order")]
        public int SortOrder { get; set; } = 0;

        /// <summary>
        /// 创建时间
        /// </summary>
        [Column("created_at")]
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        [Column("updated_at")]
        public DateTime UpdatedAt { get; set; }
    }

    /// <summary>
    /// 用户成就记录表
    /// </summary>
    [Table("gamification_user_achievements")]
    public class UserAchievement
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Key]
        [Column("id")]
        public long Id { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        [Column("user_id")]
        public int UserId { get; set; }

        /// <summary>
        /// 成就ID
        /// </summary>
        [Column("achievement_id")]
        public int AchievementId { get; set; }

        /// <summary>
        /// 获得时间
        /// </summary>
        [Column("achieved_at")]
        public DateTime AchievedAt { get; set; }

        /// <summary>
        /// 当前进度值
        /// </summary>
        [Column("current_value")]
        public int CurrentValue { get; set; }

        /// <summary>
        /// 进度值（别名）
        /// </summary>
        public int Progress => CurrentValue;

        /// <summary>
        /// 创建时间
        /// </summary>
        [Column("created_at")]
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        [Column("updated_at")]
        public DateTime UpdatedAt { get; set; }

        /// <summary>
        /// 是否已完成
        /// </summary>
        [Column("is_completed")]
        public bool IsCompleted { get; set; } = false;

        /// <summary>
        /// 是否已通知
        /// </summary>
        [Column("is_notified")]
        public bool IsNotified { get; set; } = false;

        // 导航属性
        /// <summary>
        /// 关联的用户
        /// </summary>
        [ForeignKey("UserId")]
        public virtual User User { get; set; } = null!;

        /// <summary>
        /// 关联的成就
        /// </summary>
        [ForeignKey("AchievementId")]
        public virtual Achievement Achievement { get; set; } = null!;
    }

    /// <summary>
    /// 成就指标枚举
    /// </summary>
    public static class AchievementMetrics
    {
        public const string POINTS = "points";
        public const string LEVEL = "level";
        public const string TASKS_CREATED = "tasks_created";
        public const string TASKS_COMPLETED = "tasks_completed";
        public const string TASKS_CLAIMED = "tasks_claimed";
        public const string FAULTS_RECORDED = "faults_recorded";
        public const string MAINTENANCE_CREATED = "maintenance_created";
        public const string ASSETS_UPDATED = "assets_updated";
        public const string RETURN_FACTORY = "return_factory";
        public const string CONSECUTIVE_DAYS = "consecutive_days";
    }

    /// <summary>
    /// 成就代码枚举
    /// </summary>
    public static class AchievementCodes
    {
        // 积分成就
        public const string POINTS_ROOKIE = "POINTS_ROOKIE";           // 100积分
        public const string POINTS_EXPERT = "POINTS_EXPERT";           // 1000积分
        public const string POINTS_MASTER = "POINTS_MASTER";           // 5000积分
        public const string POINTS_LEGEND = "POINTS_LEGEND";           // 10000积分

        // 等级成就
        public const string LEVEL_NOVICE = "LEVEL_NOVICE";             // 等级5
        public const string LEVEL_ADVANCED = "LEVEL_ADVANCED";         // 等级10
        public const string LEVEL_EXPERT = "LEVEL_EXPERT";             // 等级20
        public const string LEVEL_MASTER = "LEVEL_MASTER";             // 等级50

        // 任务成就
        public const string TASK_CREATOR = "TASK_CREATOR";             // 创建10个任务
        public const string TASK_MANAGER = "TASK_MANAGER";             // 创建50个任务
        public const string TASK_FINISHER = "TASK_FINISHER";           // 完成10个任务
        public const string TASK_CHAMPION = "TASK_CHAMPION";           // 完成100个任务

        // 故障成就
        public const string FAULT_REPORTER = "FAULT_REPORTER";         // 登记10个故障
        public const string FAULT_HUNTER = "FAULT_HUNTER";             // 登记50个故障

        // 维修成就
        public const string MAINTENANCE_STARTER = "MAINTENANCE_STARTER"; // 创建10个维修单
        public const string MAINTENANCE_EXPERT = "MAINTENANCE_EXPERT";   // 创建50个维修单

        // 资产成就
        public const string ASSET_KEEPER = "ASSET_KEEPER";             // 更新50个资产
        public const string ASSET_MASTER = "ASSET_MASTER";             // 更新200个资产

        // 活跃度成就
        public const string DAILY_ACTIVE_7 = "DAILY_ACTIVE_7";         // 连续活跃7天
        public const string DAILY_ACTIVE_30 = "DAILY_ACTIVE_30";       // 连续活跃30天
    }
}
