using System;
using System.Collections.Generic;

namespace ItAssetsSystem.Api.Import
{
    /// <summary>
    /// 导入响应
    /// </summary>
    public class ImportResponse
    {
        /// <summary>
        /// 导入ID
        /// </summary>
        public string ImportId { get; set; }
        
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; } = true;
        
        /// <summary>
        /// 处理状态
        /// </summary>
        public string Status { get; set; } = "completed";
        
        /// <summary>
        /// 消息
        /// </summary>
        public string Message { get; set; }
        
        /// <summary>
        /// 总行数
        /// </summary>
        public int TotalRows { get; set; }
        
        /// <summary>
        /// 成功数量
        /// </summary>
        public int SuccessCount { get; set; }
        
        /// <summary>
        /// 错误数量
        /// </summary>
        public int ErrorCount { get; set; }

        /// <summary>
        /// 警告数量
        /// </summary>
        public int WarningCount { get; set; }
        
        /// <summary>
        /// 错误列表
        /// </summary>
        public List<string> Errors { get; set; } = new List<string>();

        /// <summary>
        /// 警告列表
        /// </summary>
        public List<string> Warnings { get; set; } = new List<string>();
        
        /// <summary>
        /// 成功导入的项目列表
        /// </summary>
        public List<object> SuccessItems { get; set; } = new List<object>();
    }
} 