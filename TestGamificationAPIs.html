<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>游戏化API测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .api-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 5px;
        }
        .api-title {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 18px;
            font-weight: bold;
        }
        .api-url {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            margin: 10px 0;
            border-left: 4px solid #007bff;
        }
        .test-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        .test-btn:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .menu-links {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .menu-links h3 {
            margin-top: 0;
            color: #1976d2;
        }
        .menu-links a {
            display: inline-block;
            margin: 5px 10px 5px 0;
            padding: 8px 12px;
            background: #1976d2;
            color: white;
            text-decoration: none;
            border-radius: 4px;
        }
        .menu-links a:hover {
            background: #1565c0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎮 游戏化系统API测试页面</h1>
        <p>测试修复后的API路径和前端菜单入口</p>
    </div>

    <div class="container">
        <div class="menu-links">
            <h3>📱 前端菜单入口测试</h3>
            <a href="http://localhost:5173/main/tasks/list" target="_blank">📋 增强任务列表</a>
            <a href="http://localhost:5173/main/tasks/shift-statistics" target="_blank">📊 班次统计</a>
            <a href="http://localhost:5173/main/gamification/leaderboard" target="_blank">🏆 排行榜</a>
            <a href="http://localhost:5173/main/leaderboard" target="_blank">🏆 排行榜(备用)</a>
        </div>
    </div>

    <div class="container">
        <h2>🔧 API测试</h2>
        
        <div class="api-section">
            <div class="api-title">1. 任务完成奖励测试</div>
            <div class="api-url">POST /v2/gamification-v2/test/task-completed</div>
            <button class="test-btn" onclick="testTaskCompleted()">测试任务完成奖励</button>
            <div id="taskCompletedResult" class="result" style="display:none;"></div>
        </div>

        <div class="api-section">
            <div class="api-title">2. 任务创建奖励测试</div>
            <div class="api-url">POST /v2/gamification-v2/test/task-created</div>
            <button class="test-btn" onclick="testTaskCreated()">测试任务创建奖励</button>
            <div id="taskCreatedResult" class="result" style="display:none;"></div>
        </div>

        <div class="api-section">
            <div class="api-title">3. 获取用户统计</div>
            <div class="api-url">GET /v2/gamification-v2/stats/current</div>
            <button class="test-btn" onclick="getUserStats()">获取当前用户统计</button>
            <div id="userStatsResult" class="result" style="display:none;"></div>
        </div>

        <div class="api-section">
            <div class="api-title">4. 触发任务完成奖励</div>
            <div class="api-url">POST /v2/gamification-v2/trigger-complete-reward</div>
            <button class="test-btn" onclick="triggerCompleteReward()">触发完成奖励</button>
            <div id="triggerCompleteResult" class="result" style="display:none;"></div>
        </div>

        <div class="api-section">
            <div class="api-title">5. 触发任务领取奖励</div>
            <div class="api-url">POST /v2/gamification-v2/trigger-claim-reward</div>
            <button class="test-btn" onclick="triggerClaimReward()">触发领取奖励</button>
            <div id="triggerClaimResult" class="result" style="display:none;"></div>
        </div>

        <div class="api-section">
            <div class="api-title">6. 任务领取测试</div>
            <div class="api-url">POST /v2/tasks/1/claim</div>
            <button class="test-btn" onclick="testClaimTask()">测试任务领取</button>
            <div id="claimTaskResult" class="result" style="display:none;"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5001/api';
        
        // 获取认证token (需要先登录)
        function getAuthToken() {
            return localStorage.getItem('token') || 'Bearer your-token-here';
        }

        // 通用API请求函数
        async function apiRequest(url, method = 'GET', data = null) {
            const options = {
                method,
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': getAuthToken()
                }
            };
            
            if (data) {
                if (method === 'GET') {
                    url += '?' + new URLSearchParams(data).toString();
                } else {
                    options.body = JSON.stringify(data);
                }
            }
            
            try {
                console.log(`发送请求: ${method} ${API_BASE}${url}`);
                const response = await fetch(`${API_BASE}${url}`, options);
                const result = await response.json();
                
                return {
                    success: response.ok,
                    status: response.status,
                    data: result
                };
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
        }

        // 显示结果
        function showResult(elementId, result) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            
            if (result.success) {
                element.className = 'result success';
                element.textContent = `✅ 成功 (${result.status})\n${JSON.stringify(result.data, null, 2)}`;
            } else {
                element.className = 'result error';
                element.textContent = `❌ 失败\n${result.error || JSON.stringify(result.data, null, 2)}`;
            }
        }

        // 测试任务完成奖励
        async function testTaskCompleted() {
            const result = await apiRequest('/v2/gamification-v2/test/task-completed', 'POST', {
                taskId: 1,
                taskName: '测试任务',
                taskType: 'General',
                isOnTime: true,
                points: 20
            });
            showResult('taskCompletedResult', result);
        }

        // 测试任务创建奖励
        async function testTaskCreated() {
            const result = await apiRequest('/v2/gamification-v2/test/task-created', 'POST', {
                taskId: 2,
                taskName: '测试创建任务',
                taskType: 'General',
                points: 10
            });
            showResult('taskCreatedResult', result);
        }

        // 获取用户统计
        async function getUserStats() {
            const result = await apiRequest('/v2/gamification-v2/stats/current');
            showResult('userStatsResult', result);
        }

        // 触发任务完成奖励
        async function triggerCompleteReward() {
            const result = await apiRequest('/v2/gamification-v2/trigger-complete-reward', 'POST', {
                taskId: 1,
                isOnTime: true
            });
            showResult('triggerCompleteResult', result);
        }

        // 触发任务领取奖励
        async function triggerClaimReward() {
            const result = await apiRequest('/v2/gamification-v2/trigger-claim-reward', 'POST', {
                taskId: 1
            });
            showResult('triggerClaimResult', result);
        }

        // 测试任务领取
        async function testClaimTask() {
            const result = await apiRequest('/v2/tasks/1/claim', 'POST', {
                notes: '测试领取任务'
            });
            showResult('claimTaskResult', result);
        }

        // 页面加载时的提示
        window.onload = function() {
            console.log('🎮 游戏化API测试页面已加载');
            console.log('📝 请确保：');
            console.log('1. 后端服务运行在 http://localhost:5001');
            console.log('2. 前端服务运行在 http://localhost:5173');
            console.log('3. 已登录并获取有效的认证token');
            
            // 检查是否有token
            const token = getAuthToken();
            if (token === 'Bearer your-token-here') {
                alert('⚠️ 请先登录获取认证token，或在浏览器控制台设置：localStorage.setItem("token", "your-actual-token")');
            }
        };
    </script>
</body>
</html>
