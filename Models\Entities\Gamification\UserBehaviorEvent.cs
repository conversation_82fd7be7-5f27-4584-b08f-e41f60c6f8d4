using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ItAssetsSystem.Models.Entities.Gamification
{
    /// <summary>
    /// 用户行为事件实体
    /// 标准化行为追踪表
    /// </summary>
    [Table("user_behavior_events")]
    public class UserBehaviorEvent
    {
        /// <summary>
        /// 事件唯一标识
        /// </summary>
        [Key]
        [Column("id")]
        public long Id { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        [Column("user_id")]
        public int UserId { get; set; }

        /// <summary>
        /// 行为类型
        /// </summary>
        [Column("action_type")]
        [MaxLength(50)]
        public string ActionType { get; set; } = string.Empty;

        /// <summary>
        /// 行为分类
        /// </summary>
        [Column("action_category")]
        [MaxLength(20)]
        public string ActionCategory { get; set; } = string.Empty;

        /// <summary>
        /// 关联业务对象ID
        /// </summary>
        [Column("reference_id")]
        public long? ReferenceId { get; set; }

        /// <summary>
        /// 关联业务表名
        /// </summary>
        [Column("reference_table")]
        [MaxLength(50)]
        public string? ReferenceTable { get; set; }

        /// <summary>
        /// 获得积分
        /// </summary>
        [Column("points_earned")]
        public int PointsEarned { get; set; } = 0;

        /// <summary>
        /// 获得金币
        /// </summary>
        [Column("coins_earned")]
        public int CoinsEarned { get; set; } = 0;

        /// <summary>
        /// 获得钻石
        /// </summary>
        [Column("diamonds_earned")]
        public int DiamondsEarned { get; set; } = 0;

        /// <summary>
        /// 获得经验值
        /// </summary>
        [Column("xp_earned")]
        public int XpEarned { get; set; } = 0;

        /// <summary>
        /// 行为上下文数据（JSON格式）
        /// </summary>
        [Column("context_data")]
        public string? ContextData { get; set; }

        /// <summary>
        /// 会话ID
        /// </summary>
        [Column("session_id")]
        [MaxLength(100)]
        public string? SessionId { get; set; }

        /// <summary>
        /// IP地址
        /// </summary>
        [Column("ip_address")]
        [MaxLength(45)]
        public string? IpAddress { get; set; }

        /// <summary>
        /// 用户代理
        /// </summary>
        [Column("user_agent")]
        [MaxLength(500)]
        public string? UserAgent { get; set; }

        /// <summary>
        /// 行为发生时间
        /// </summary>
        [Column("timestamp")]
        public DateTime Timestamp { get; set; } = DateTime.Now;

        /// <summary>
        /// 记录创建时间
        /// </summary>
        [Column("created_at")]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 处理状态：Pending, Processing, Completed, Failed, Compensating
        /// </summary>
        [Column("processing_status")]
        [MaxLength(20)]
        public string ProcessingStatus { get; set; } = "Pending";

        /// <summary>
        /// 处理失败原因
        /// </summary>
        [Column("failure_reason")]
        [MaxLength(1000)]
        public string? FailureReason { get; set; }

        /// <summary>
        /// 重试次数
        /// </summary>
        [Column("retry_count")]
        public int RetryCount { get; set; } = 0;

        /// <summary>
        /// 最后处理时间
        /// </summary>
        [Column("last_processed_at")]
        public DateTime? LastProcessedAt { get; set; }
    }
}
