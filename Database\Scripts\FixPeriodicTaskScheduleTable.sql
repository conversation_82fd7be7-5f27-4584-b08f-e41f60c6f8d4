-- Fix PeriodicTaskSchedule table structure
-- This script ensures the periodictaskschedules table exists with correct structure

-- Check if table exists and create if not
CREATE TABLE IF NOT EXISTS `periodictaskschedules` (
  `periodic_task_schedule_id` bigint NOT NULL AUTO_INCREMENT,
  `template_task_id` bigint NOT NULL,
  `creator_user_id` int NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
  `recurrence_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'Daily',
  `recurrence_interval` int NOT NULL DEFAULT 1,
  `days_of_week` json NULL,
  `day_of_month` int NULL DEFAULT NULL,
  `week_of_month` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `day_of_week_for_month` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `month_of_year` int NULL DEFAULT NULL,
  `cron_expression` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `start_date` datetime(6) NOT NULL,
  `end_condition_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'Never',
  `end_date` datetime(6) NULL DEFAULT NULL,
  `total_occurrences` int NULL DEFAULT NULL,
  `occurrences_generated` int NOT NULL DEFAULT 0,
  `next_generation_time` datetime(6) NOT NULL,
  `status` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'Active',
  `last_generated_timestamp` datetime(6) NULL DEFAULT NULL,
  `last_error` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
  `default_points` int NOT NULL DEFAULT 0,
  `creation_timestamp` datetime(6) NOT NULL,
  `last_updated_timestamp` datetime(6) NOT NULL,
  PRIMARY KEY (`periodic_task_schedule_id`) USING BTREE,
  INDEX `IX_periodictaskschedules_creator_user_id`(`creator_user_id`) USING BTREE,
  INDEX `IX_periodictaskschedules_template_task_id`(`template_task_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- Check if periodic_task_schedule_assignees table exists and create if not
CREATE TABLE IF NOT EXISTS `periodic_task_schedule_assignees` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `periodic_task_schedule_id` bigint NOT NULL,
  `user_id` int NOT NULL,
  `created_at` datetime(6) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_schedule_user`(`periodic_task_schedule_id`, `user_id`) USING BTREE,
  INDEX `idx_schedule_id`(`periodic_task_schedule_id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;