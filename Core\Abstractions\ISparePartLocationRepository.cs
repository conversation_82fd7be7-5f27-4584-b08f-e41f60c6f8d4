// File: Core/Abstractions/ISparePartLocationRepository.cs
// Description: 备品备件库位仓储接口，定义数据访问方法

using System.Collections.Generic;
using System.Threading.Tasks;
using ItAssetsSystem.Domain.Entities;

namespace ItAssetsSystem.Core.Abstractions
{
    /// <summary>
    /// 备品备件库位仓储接口
    /// </summary>
    public interface ISparePartLocationRepository
    {
        /// <summary>
        /// 获取所有库位
        /// </summary>
        /// <returns>库位列表</returns>
        Task<List<SparePartLocation>> GetAllAsync();
        
        /// <summary>
        /// 根据区域获取库位
        /// </summary>
        /// <param name="area">区域标识</param>
        /// <returns>库位列表</returns>
        Task<List<SparePartLocation>> GetByAreaAsync(string area);
        
        /// <summary>
        /// 根据ID获取库位
        /// </summary>
        /// <param name="id">库位ID</param>
        /// <returns>库位实体</returns>
        Task<SparePartLocation> GetByIdAsync(long id);
        
        /// <summary>
        /// 创建库位
        /// </summary>
        /// <param name="entity">库位实体</param>
        /// <returns>创建的库位实体</returns>
        Task<SparePartLocation> CreateAsync(SparePartLocation entity);
        
        /// <summary>
        /// 更新库位
        /// </summary>
        /// <param name="entity">库位实体</param>
        /// <returns>更新后的库位实体</returns>
        Task<SparePartLocation> UpdateAsync(SparePartLocation entity);
        
        /// <summary>
        /// 删除库位
        /// </summary>
        /// <param name="id">库位ID</param>
        /// <returns>是否成功</returns>
        Task<bool> DeleteAsync(long id);
        
        /// <summary>
        /// 检查库位编码是否已存在
        /// </summary>
        /// <param name="code">编码</param>
        /// <param name="excludeId">排除的ID</param>
        /// <returns>是否存在</returns>
        Task<bool> IsCodeExistsAsync(string code, long? excludeId = null);
        
        /// <summary>
        /// 获取所有区域列表
        /// </summary>
        /// <returns>区域标识列表</returns>
        Task<List<string>> GetAllAreasAsync();
    }
} 