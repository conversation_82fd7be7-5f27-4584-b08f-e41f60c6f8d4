<!-- File: frontend/src/views/spareparts/MobileQuickInView.vue -->
<!-- Description: 备品备件移动端快速入库视图 -->

<template>
  <div class="quick-inbound-view">
    <!-- 顶部导航 -->
    <div class="mobile-header">
      <div class="back-button" @click="goBack">
        <el-icon><ArrowLeft /></el-icon>
      </div>
      <div class="title">快速入库</div>
      <div></div> <!-- 占位 -->
    </div>
    
    <!-- 扫码/搜索区域 -->
    <div class="scan-search-area">
      <el-input 
        v-model="searchCode" 
        placeholder="扫描或输入备件编号/条码"
        clearable
      >
        <template #prefix>
          <el-icon><Search /></el-icon>
        </template>
        <template #append>
          <el-button :icon="Aim" @click="activateScanner"></el-button>
        </template>
      </el-input>
    </div>
    
    <!-- 备件信息卡片 -->
    <div class="part-info-card" v-if="selectedPart">
      <div class="part-header">
        <div>
          <div class="part-name">{{ selectedPart.name }}</div>
          <div class="part-code">{{ selectedPart.code }}</div>
        </div>
        <el-button type="danger" size="small" :icon="Close" circle @click="clearSelectedPart"></el-button>
      </div>
      <div class="part-details">
        <div class="info-item">
          <span class="label">类型:</span>
          <span class="value">{{ selectedPart.typeName }}</span>
        </div>
        <div class="info-item">
          <span class="label">规格:</span>
          <span class="value">{{ selectedPart.specification }}</span>
        </div>
        <div class="info-item">
          <span class="label">库存:</span>
          <span class="value">{{ selectedPart.currentStock }}</span>
        </div>
      </div>
    </div>
    
    <!-- 备件选择器 -->
    <div class="part-selector" v-if="!selectedPart">
      <el-button type="primary" block @click="showPartSelector">选择备件</el-button>
    </div>
    
    <!-- 入库表单 -->
    <div class="inbound-form" v-if="selectedPart">
      <el-form :model="inboundForm" label-position="top">
        <el-form-item label="入库数量">
          <el-input-number v-model="inboundForm.quantity" :min="1" controls-position="right" style="width: 100%"></el-input-number>
        </el-form-item>
        
        <el-form-item label="入库原因">
          <el-select v-model="inboundForm.reasonType" placeholder="选择入库原因" style="width: 100%">
            <el-option label="采购入库" :value="1"></el-option>
            <el-option label="退回入库" :value="2"></el-option>
            <el-option label="盘点调整" :value="5"></el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="库位">
          <el-select v-model="inboundForm.locationId" filterable placeholder="选择库位" style="width: 100%">
            <el-option
              v-for="item in locationOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="关联单号">
          <el-input v-model="inboundForm.referenceNumber" placeholder="选填，相关采购单号"></el-input>
        </el-form-item>
        
        <el-form-item label="备注">
          <el-input 
            v-model="inboundForm.remarks" 
            type="textarea" 
            placeholder="选填，备注信息"
            :rows="3">
          </el-input>
        </el-form-item>
      </el-form>
    </div>
    
    <!-- 提交按钮 -->
    <div class="submit-area" v-if="selectedPart">
      <el-button type="primary" block @click="submitInbound" :loading="submitting">
        确认入库
      </el-button>
    </div>
    
    <!-- 备件选择器抽屉 -->
    <el-drawer v-model="partSelectorVisible" title="选择备件" direction="bottom" size="90%">
      <div class="part-search">
        <el-input v-model="partSearchKeyword" placeholder="搜索备件" clearable>
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
      <div class="part-list">
        <div 
          v-for="(part, index) in filteredParts" 
          :key="index" 
          class="part-item"
          @click="selectPart(part)">
          <div class="part-main-info">
            <div class="part-item-name">{{ part.name }}</div>
            <div class="part-item-code">{{ part.code }}</div>
          </div>
          <div class="part-extra-info">
            <span class="stock">库存: {{ part.currentStock }}</span>
            <el-icon><ArrowRight /></el-icon>
          </div>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import * as sparePartsApi from '@/api/spareparts';
import { 
  ArrowLeft, 
  Search, 
  Aim, 
  Close, 
  ArrowRight 
} from '@element-plus/icons-vue';

// 路由
const router = useRouter();

// 创建响应式状态
const searchCode = ref('');
const selectedPart = ref(null);
const partSelectorVisible = ref(false);
const partSearchKeyword = ref('');
const submitting = ref(false);
const locationOptions = ref([]);
const partsList = ref([]);

// 入库表单
const inboundForm = reactive({
  partId: null,
  quantity: 1,
  reasonType: 1,
  locationId: null,
  referenceNumber: '',
  remarks: ''
});

// 过滤后的备件列表
const filteredParts = computed(() => {
  if (!partSearchKeyword.value) {
    return partsList.value;
  }
  
  const keyword = partSearchKeyword.value.toLowerCase();
  return partsList.value.filter(part => 
    part.name.toLowerCase().includes(keyword) || 
    part.code.toLowerCase().includes(keyword)
  );
});

// 生命周期钩子
onMounted(() => {
  fetchLocationOptions();
  fetchSpareParts();
});

// 获取库位选项
const fetchLocationOptions = async () => {
  try {
    const response = await sparePartsApi.getSparePartLocations();
    if (response.success) {
      locationOptions.value = response.data;
      // 如果有默认库位，自动选择
      if (locationOptions.value.length > 0) {
        inboundForm.locationId = locationOptions.value[0].id;
      }
    } else {
      ElMessage.error(response.message || '获取库位失败');
    }
  } catch (error) {
    console.error('获取库位出错:', error);
    ElMessage.error('获取库位失败，请稍后重试');
  }
};

// 获取备件列表
const fetchSpareParts = async () => {
  try {
    const response = await sparePartsApi.getSpareParts({ pageIndex: 1, pageSize: 100 });
    if (response.success) {
      partsList.value = response.data.items;
    } else {
      ElMessage.error(response.message || '获取备件列表失败');
    }
  } catch (error) {
    console.error('获取备件列表出错:', error);
    ElMessage.error('获取备件列表失败，请稍后重试');
  }
};

// 返回上一页
const goBack = () => {
  router.back();
};

// 激活扫码器
const activateScanner = () => {
  // 这里实现扫码功能，可能需要调用原生API或第三方库
  ElMessage.info('扫码功能待实现');
};

// 显示备件选择器
const showPartSelector = () => {
  partSelectorVisible.value = true;
};

// 选择备件
const selectPart = (part) => {
  selectedPart.value = part;
  inboundForm.partId = part.id;
  partSelectorVisible.value = false;
};

// 清除选中的备件
const clearSelectedPart = () => {
  selectedPart.value = null;
  inboundForm.partId = null;
};

// 提交入库
const submitInbound = async () => {
  if (!selectedPart.value) {
    ElMessage.warning('请先选择备件');
    return;
  }
  
  if (!inboundForm.locationId) {
    ElMessage.warning('请选择库位');
    return;
  }
  
  submitting.value = true;
  
  try {
    const transaction = {
      partId: inboundForm.partId,
      quantity: inboundForm.quantity,
      type: 1, // 入库
      reasonType: inboundForm.reasonType,
      locationId: inboundForm.locationId,
      referenceNumber: inboundForm.referenceNumber,
      remarks: inboundForm.remarks
    };
    
    const response = await sparePartsApi.createSparePartTransaction(transaction);
    
    if (response.success) {
      ElMessage.success('入库成功');
      // 重置表单
      clearSelectedPart();
      inboundForm.quantity = 1;
      inboundForm.referenceNumber = '';
      inboundForm.remarks = '';
      searchCode.value = '';
    } else {
      ElMessage.error(response.message || '入库失败');
    }
  } catch (error) {
    console.error('入库出错:', error);
    ElMessage.error('入库失败，请稍后重试');
  } finally {
    submitting.value = false;
  }
};
</script>

<style scoped>
.quick-inbound-view {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 20px;
}

.mobile-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background-color: #fff;
  box-shadow: 0 1px 4px rgba(0,0,0,0.1);
}

.mobile-header .title {
  font-size: 18px;
  font-weight: bold;
}

.back-button {
  font-size: 18px;
}

.scan-search-area {
  padding: 15px;
}

.part-info-card, .inbound-form {
  margin: 0 15px 15px;
  background-color: #fff;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.part-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 10px;
}

.part-name {
  font-weight: bold;
  font-size: 16px;
}

.part-code {
  color: #666;
  font-size: 14px;
  margin-top: 5px;
}

.part-details {
  padding-top: 10px;
  border-top: 1px solid #f0f0f0;
}

.info-item {
  display: flex;
  margin-bottom: 8px;
}

.info-item .label {
  color: #666;
  width: 60px;
  flex-shrink: 0;
}

.info-item .value {
  flex: 1;
}

.part-selector {
  padding: 0 15px;
  margin-top: 20px;
}

.submit-area {
  padding: 0 15px;
  margin-top: auto;
}

.part-search {
  padding: 15px;
}

.part-list {
  overflow-y: auto;
  height: calc(100% - 70px);
}

.part-item {
  padding: 15px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.part-item-name {
  font-weight: 500;
}

.part-item-code {
  color: #666;
  font-size: 14px;
  margin-top: 5px;
}

.part-extra-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.stock {
  color: #666;
}
</style> 