<template>
  <el-dialog v-model="dialogVisible" :title="isEdit ? '编辑任务' : '新建任务'" width="600px" @close="handleClose">
    <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
      <el-form-item label="任务名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入任务名称" />
      </el-form-item>
      <el-form-item label="描述" prop="description">
        <el-input type="textarea" v-model="form.description" placeholder="请输入描述" />
      </el-form-item>
      <el-form-item label="优先级" prop="priority">
        <el-select v-model="form.priority" placeholder="请选择优先级">
          <el-option label="高" value="High" />
          <el-option label="中" value="Medium" />
          <el-option label="低" value="Low" />
        </el-select>
      </el-form-item>
      <el-form-item label="负责人" prop="assigneeUserIds">
        <el-select 
          v-model="form.assigneeUserIds" 
          multiple 
          collapse-tags
          collapse-tags-tooltip
          filterable 
          placeholder="请选择负责人(多选)"
          :loading="loading"
          style="width: 100%"
        >
          <el-option 
            v-for="user in users" 
            :key="user.id" 
            :label="user.name" 
            :value="user.id" 
          >
            <div class="user-option">
              <el-avatar :size="24" :src="user.avatarUrl" v-if="user.avatarUrl">{{ user.name.substring(0, 1) }}</el-avatar>
              <span>{{ user.name }}</span>
              <span class="user-dept">{{ user.department }}</span>
            </div>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="计划开始" prop="planStartDate">
        <el-date-picker v-model="form.planStartDate" type="date" placeholder="选择日期" />
      </el-form-item>
      <el-form-item label="计划结束" prop="planEndDate">
        <el-date-picker v-model="form.planEndDate" type="date" placeholder="选择日期" />
      </el-form-item>
      <el-form-item label="任务类型" prop="taskType">
        <el-select v-model="form.taskType" placeholder="请选择任务类型">
          <el-option label="普通" value="Normal" />
          <el-option label="周期" value="Periodic" />
          <el-option label="PDCA" value="PDCA" />
        </el-select>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSubmit">{{ isEdit ? '保存' : '创建' }}</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { ref, reactive, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import userApi from '@/api/user'
import { useUserStore } from '@/stores/modules/user'

export default {
  name: 'TaskFormDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    isEdit: {
      type: Boolean,
      default: false
    },
    formData: {
      type: Object,
      default: () => ({})
    }
  },
  setup(props, { emit }) {
    const dialogVisible = ref(props.visible)
    const formRef = ref(null)
    const userStore = useUserStore()
    const loading = ref(false)
    
    // 真实用户列表
    const users = ref([])
    
    // 加载用户列表
    const loadUserList = async () => {
      console.log('开始加载用户列表')
      loading.value = true
      try {
        const response = await userApi.getUserList()
        console.log('用户API响应:', response)
        
        if (response && response.data) {
          // 打印原始数据结构帮助调试
          console.log('原始用户数据:', JSON.stringify(response.data).substring(0, 500) + '...')
          
          users.value = response.data.map(user => {
            // 确保每个用户对象都有id和name属性
            if (!user) {
              console.warn('发现空用户对象')
              return null
            }
            
            const mappedUser = {
              id: user.id || user.userId || user.ID || 0,
              name: user.name || user.userName || user.username || user.displayName || '未知用户',
              department: user.department || user.departmentName || '',
              avatarUrl: user.avatarUrl || ''
            }
            
            // 验证必需字段
            if (!mappedUser.id || mappedUser.id === 0) {
              console.warn('用户缺少有效ID:', user)
              return null
            }
            if (!mappedUser.name || mappedUser.name === '未知用户') {
              console.warn('用户缺少有效名称:', user)
              // 不跳过，继续使用默认名称
            }
            
            console.log('映射后的用户:', mappedUser)
            return mappedUser
          }).filter(user => user !== null && user.id > 0) // 过滤掉无效的用户对象
          
          console.log(`用户列表加载完成，共 ${users.value.length} 个用户`)
        } else {
          console.warn('API返回的response或response.data为空')
          users.value = []
        }
      } catch (error) {
        console.error('获取用户列表失败:', error)
        ElMessage.error('获取用户列表失败，使用测试数据')
        // 加载失败时使用测试数据作为后备
        users.value = [
          { id: 1, name: '张三', department: '部门A', avatarUrl: 'https://example.com/avatar1.jpg' },
          { id: 2, name: '李四', department: '部门B', avatarUrl: 'https://example.com/avatar2.jpg' },
          { id: 3, name: '王五', department: '部门C', avatarUrl: 'https://example.com/avatar3.jpg' }
        ]
      } finally {
        loading.value = false
      }
    }
    
    // 表单数据结构
    const form = reactive({
      name: '',
      description: '',
      priority: 'Medium',
      assigneeUserIds: [], // 多选负责人数组
      planStartDate: '',
      planEndDate: '',
      taskType: 'Normal'
    })
    
    const rules = {
      name: [{ required: true, message: '请输入任务名称', trigger: 'blur' }],
      priority: [{ required: true, message: '请选择优先级', trigger: 'change' }]
    }
    
    // 初始化表单数据，将assigneeUserId转换为assigneeUserIds数组
    const initFormData = (data) => {
      if (!data) {
        // 如果没有数据且不是编辑模式，设置默认日期
        if (!props.isEdit) {
          const today = new Date()
          const oneMonthLater = new Date()
          oneMonthLater.setMonth(today.getMonth() + 1)
          
          form.planStartDate = today
          form.planEndDate = oneMonthLater
        }
        return
      }
      
      console.log('初始化表单数据:', data)
      
      // 深拷贝，避免直接修改props
      const newForm = { ...data }
      
      // 处理多负责人数据：合并主负责人和协作者
      const assigneeUserIds = []
      
      // 先添加主负责人
      if (newForm.assigneeUserId) {
        assigneeUserIds.push(newForm.assigneeUserId)
      }
      
      // 再添加协作者（如果有的话）
      if (newForm.collaboratorUserIds && Array.isArray(newForm.collaboratorUserIds)) {
        assigneeUserIds.push(...newForm.collaboratorUserIds)
      } else if (newForm.assignees && Array.isArray(newForm.assignees)) {
        // 从assignees数组中提取协作者ID
        const collaborators = newForm.assignees
          .filter(assignee => assignee.assignmentType === 'Participant')
          .map(assignee => assignee.userId || assignee.id)
        assigneeUserIds.push(...collaborators)
      }
      
      // 去重（防止主负责人在协作者列表中重复）
      newForm.assigneeUserIds = [...new Set(assigneeUserIds)]
      
      // 处理日期字段 - 确保日期格式正确
      if (newForm.planStartDate) {
        newForm.planStartDate = new Date(newForm.planStartDate)
      }
      if (newForm.planEndDate) {
        newForm.planEndDate = new Date(newForm.planEndDate)
      }
      
      // 复制其他属性
      Object.assign(form, newForm)
      console.log('初始化后的表单数据:', form)
      console.log('合并后的负责人列表:', newForm.assigneeUserIds)
    }
    
    // 监听visible属性变化
    watch(() => props.visible, (newVal) => {
      dialogVisible.value = newVal
    })
    
    // 监听dialogVisible变化，同步回父组件
    watch(dialogVisible, (newVal) => {
      if (!newVal) {
        emit('update:visible', false)
      }
    })
    
    // 监听formData属性变化
    watch(() => props.formData, (newVal) => {
      initFormData(newVal)
    }, { immediate: true, deep: true })
    
    const handleClose = () => {
      dialogVisible.value = false
      emit('close')
    }
    
    const handleSubmit = () => {
      if (!formRef.value) {
        console.error('表单引用不存在')
        return
      }
      
      formRef.value.validate(valid => {
        if (valid) {
          console.log('表单验证通过，准备提交')
          console.log('提交前的表单数据:', form)
          
          // 将assigneeUserIds分解为主负责人和协作人
          const assigneeUserIds = form.assigneeUserIds || []
          const assigneeUserId = assigneeUserIds.length > 0 ? assigneeUserIds[0] : null // 第一个作为主负责人
          const collaboratorUserIds = assigneeUserIds.length > 1 ? assigneeUserIds.slice(1) : [] // 其余作为协作人
          
          const data = {
            name: form.name,
            description: form.description,
            assigneeUserId: assigneeUserId,  // 主负责人（单个）
            collaboratorUserIds: collaboratorUserIds,  // 协作人（数组）
            priority: form.priority,
            planStartDate: form.planStartDate ? new Date(form.planStartDate).toISOString() : null,
            planEndDate: form.planEndDate ? new Date(form.planEndDate).toISOString() : null,
            status: form.status || 'Todo',
            taskType: form.taskType || 'Normal',
            points: form.points || 0
          }
          
          console.log('最终提交的数据:', data)
          emit('submit', data)
        } else {
          console.warn('表单验证失败')
        }
      })
    }
    
    // 组件挂载时加载用户列表
    onMounted(() => {
      console.log('TaskFormDialog组件挂载')
      loadUserList()
      
      // 如果是新建任务，设置默认值
      if (!props.isEdit) {
        // 默认选择当前用户为负责人
        if (userStore.userInfo && userStore.userInfo.id) {
          console.log('设置当前用户为默认负责人:', userStore.userInfo.id)
          form.assigneeUserIds = [userStore.userInfo.id]
        }
        
        // 设置默认日期：开始日期为今天，结束日期为一个月后
        const today = new Date()
        const oneMonthLater = new Date()
        oneMonthLater.setMonth(today.getMonth() + 1)
        
        form.planStartDate = today
        form.planEndDate = oneMonthLater
        
        console.log('设置默认日期:', {
          startDate: today.toISOString().split('T')[0],
          endDate: oneMonthLater.toISOString().split('T')[0]
        })
      }
    })
    
    return {
      dialogVisible,
      formRef,
      form,
      rules,
      users,
      loading,
      handleClose,
      handleSubmit
    }
  }
}
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}

.user-option {
  display: flex;
  align-items: center;
}

.user-dept {
  margin-left: 8px;
  font-size: 0.8em;
  color: #909399;
}
</style> 