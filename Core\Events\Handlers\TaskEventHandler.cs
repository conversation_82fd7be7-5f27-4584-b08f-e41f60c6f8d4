// File: Core/Events/Handlers/TaskEventHandler.cs
// Description: 任务事件处理器，处理任务相关的事件通知

using Microsoft.Extensions.Logging;
using System.Threading.Tasks;
using ItAssetsSystem.Core.Events.Tasks;
using ItAssetsSystem.Core.Abstractions;

namespace ItAssetsSystem.Core.Events.Handlers
{
    /// <summary>
    /// 任务事件处理器
    /// </summary>
    public class TaskEventHandler
    {
        private readonly INotificationService _notificationService;
        private readonly ILogger<TaskEventHandler> _logger;

        public TaskEventHandler(
            INotificationService notificationService,
            ILogger<TaskEventHandler> logger)
        {
            _notificationService = notificationService;
            _logger = logger;
        }

        /// <summary>
        /// 处理任务创建事件
        /// </summary>
        /// <param name="taskCreatedEvent">任务创建事件</param>
        public async Task HandleTaskCreatedAsync(TaskCreatedEvent taskCreatedEvent)
        {
            try
            {
                _logger.LogInformation("处理任务创建事件，任务ID: {TaskId}, 任务名称: {TaskName}", 
                    taskCreatedEvent.TaskId, taskCreatedEvent.TaskName);

                await _notificationService.NotifyTaskCreatedAsync(
                    taskCreatedEvent.TaskId,
                    taskCreatedEvent.TaskName,
                    taskCreatedEvent.CreatorUserId,
                    taskCreatedEvent.AssigneeUserId,
                    taskCreatedEvent.TaskType
                );

                _logger.LogInformation("任务创建通知已发送，任务ID: {TaskId}", taskCreatedEvent.TaskId);
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "处理任务创建事件时发生错误，任务ID: {TaskId}", taskCreatedEvent.TaskId);
            }
        }
    }
}