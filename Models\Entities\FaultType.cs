// IT资产管理系统 - 故障类型实体
// 文件路径: /Models/Entities/FaultType.cs
// 功能: 定义故障类型实体，对应faulttypes表

using System;
using System.Collections.Generic;
using ItAssetsSystem.Models;

namespace ItAssetsSystem.Models.Entities
{
    /// <summary>
    /// 故障类型实体
    /// </summary>
    public class FaultType : IAuditableEntity
    {
        /// <summary>
        /// 故障类型ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 故障类型名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 故障类型编码
        /// </summary>
        public string Code { get; set; }

        /// <summary>
        /// 上级类型ID
        /// </summary>
        public int? ParentId { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 排序
        /// </summary>
        public int SortOrder { get; set; }

        /// <summary>
        /// 是否激活
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; }

        /// <summary>
        /// 上级类型
        /// </summary>
        public virtual FaultType Parent { get; set; }

        /// <summary>
        /// 下级类型
        /// </summary>
        public virtual ICollection<FaultType> Children { get; set; }

        /// <summary>
        /// 故障记录
        /// </summary>
        public virtual ICollection<FaultRecord> FaultRecords { get; set; }
    }
} 