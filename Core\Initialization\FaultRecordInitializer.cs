// File: Core/Initialization/FaultRecordInitializer.cs
// Description: 故障记录表初始化和修复工具

using ItAssetsSystem.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System;
using System.Data.Common;
using System.Threading.Tasks;
using MySqlConnector;

namespace ItAssetsSystem.Core.Initialization
{
    /// <summary>
    /// 故障记录表初始化器，用于检查和修复故障记录表结构
    /// </summary>
    public static class FaultRecordInitializer
    {
        /// <summary>
        /// 初始化并修复故障记录表结构
        /// </summary>
        public static async Task InitializeFaultRecordTableAsync(IServiceProvider serviceProvider)
        {
            using var scope = serviceProvider.CreateScope();
            var logger = scope.ServiceProvider.GetRequiredService<ILogger<AppDbContext>>();
            var dbContext = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            logger.LogInformation("检查故障记录表结构");
            
            try
            {
                // 检查AssetId字段是否允许NULL
                bool assetIdAllowsNull = await CheckColumnAllowsNullAsync(dbContext, "faultrecords", "AssetId");
                
                if (!assetIdAllowsNull)
                {
                    logger.LogWarning("发现故障记录表AssetId字段不允许NULL，将进行修复以支持线下设备");

                    // 使用新的数据库上下文来修改表结构
                    using var newScope = serviceProvider.CreateScope();
                    var newDbContext = newScope.ServiceProvider.GetRequiredService<AppDbContext>();

                    // 修改AssetId字段允许NULL
                    await ModifyColumnToAllowNullAsync(newDbContext, "faultrecords", "AssetId", "int", "资产ID（线下设备时可为空）");

                    logger.LogInformation("成功修改故障记录表AssetId字段为允许NULL");
                }
                else
                {
                    logger.LogInformation("故障记录表AssetId字段已允许NULL，无需修复");
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "检查/修复故障记录表结构失败");
            }
        }
        
        /// <summary>
        /// 检查列是否允许NULL
        /// </summary>
        private static async Task<bool> CheckColumnAllowsNullAsync(AppDbContext dbContext, string tableName, string columnName)
        {
            string sql = @"
                SELECT IS_NULLABLE
                FROM INFORMATION_SCHEMA.COLUMNS
                WHERE TABLE_SCHEMA = @schema
                  AND TABLE_NAME = @tableName
                  AND COLUMN_NAME = @columnName";

            // 获取数据库名称
            var connectionString = dbContext.Database.GetConnectionString();
            var builder = new MySqlConnectionStringBuilder(connectionString);
            string dbName = builder.Database;

            var parameters = new[]
            {
                new MySqlParameter("@schema", dbName),
                new MySqlParameter("@tableName", tableName),
                new MySqlParameter("@columnName", columnName)
            };

            try
            {
                // 使用ExecuteSqlRaw执行查询并获取结果
                using var connection = dbContext.Database.GetDbConnection();
                await connection.OpenAsync();

                using var command = connection.CreateCommand();
                command.CommandText = sql;
                command.Parameters.AddRange(parameters);

                var result = await command.ExecuteScalarAsync();
                var isNullable = result?.ToString();

                return isNullable == "YES";
            }
            catch (Exception)
            {
                // 如果查询失败，假设不允许NULL
                return false;
            }
        }
        
        /// <summary>
        /// 修改列允许NULL
        /// </summary>
        private static async Task ModifyColumnToAllowNullAsync(AppDbContext dbContext, string tableName, string columnName, string dataType, string comment)
        {
            string sql = $@"
                ALTER TABLE `{tableName}` 
                MODIFY COLUMN `{columnName}` {dataType} NULL COMMENT '{comment}'";

            await dbContext.Database.ExecuteSqlRawAsync(sql);
        }
    }
}
