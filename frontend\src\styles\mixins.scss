/**
 * 航空航天级IT资产管理系统 - Mixins样式工具
 * 文件路径: src/styles/mixins.scss
 * 功能描述: 定义可复用的样式混合宏，提高样式代码复用率
 */

@use './variables.scss' as vars;

// 清除浮动
@mixin clearfix {
  &:after {
    content: "";
    display: table;
    clear: both;
  }
}

// 单行文本溢出省略
@mixin ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

// 多行文本溢出省略
@mixin multi-ellipsis($lines) {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: $lines;
  overflow: hidden;
}

// 滚动条样式
@mixin scrollbar {
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
    
    &:hover {
      background-color: rgba(0, 0, 0, 0.3);
    }
  }
}

// 弹性布局
@mixin flex($direction: row, $wrap: nowrap, $justify: flex-start, $align: stretch) {
  display: flex;
  flex-direction: $direction;
  flex-wrap: $wrap;
  justify-content: $justify;
  align-items: $align;
}

// 绝对定位居中
@mixin absolute-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

// 固定宽高比例
@mixin aspect-ratio($width, $height) {
  position: relative;
  
  &:before {
    content: "";
    display: block;
    padding-top: ($height / $width) * 100%;
  }
  
  > * {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
}

// 响应式媒体查询
@mixin respond-to($breakpoint) {
  @if $breakpoint == "xs" {
    @media (max-width: 576px) { @content; }
  } @else if $breakpoint == "sm" {
    @media (min-width: 576px) and (max-width: 768px) { @content; }
  } @else if $breakpoint == "md" {
    @media (min-width: 768px) and (max-width: 992px) { @content; }
  } @else if $breakpoint == "lg" {
    @media (min-width: 992px) and (max-width: 1200px) { @content; }
  } @else if $breakpoint == "xl" {
    @media (min-width: 1200px) { @content; }
  }
}

// 文本溢出省略号
@mixin text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 多行文本溢出省略号
@mixin text-ellipsis-multi($lines: 2) {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: $lines;
  -webkit-box-orient: vertical;
}

// 卡片样式
@mixin card {
  background-color: var(--el-bg-color);
  border-radius: 4px;
  box-shadow: var(--el-box-shadow-light);
  padding: 16px;
}

// 渐变背景
@mixin gradient-bg($start-color, $end-color, $direction: to right) {
  background: linear-gradient($direction, $start-color, $end-color);
}

// 状态标签
@mixin status-tag($bg-color, $text-color) {
  display: inline-block;
  padding: 2px 8px;
  font-size: vars.$font-size-small;
  border-radius: vars.$border-radius-small;
  background-color: rgba($bg-color, 0.1);
  color: $text-color;
}

// 居中布局
@mixin centered($horizontal: true, $vertical: true) {
  position: absolute;
  
  @if $horizontal and $vertical {
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  } @else if $horizontal {
    left: 50%;
    transform: translateX(-50%);
  } @else if $vertical {
    top: 50%;
    transform: translateY(-50%);
  }
}

// 按钮基本样式
@mixin button-base {
  display: inline-block;
  padding: 8px 16px;
  border-radius: vars.$border-radius-base;
  font-size: vars.$font-size-base;
  cursor: pointer;
  transition: all vars.$transition-duration vars.$transition-timing-function;
  border: 1px solid transparent;
  outline: none;
}

// 过渡效果
@mixin transition($property: all, $duration: 0.3s, $timing: ease-in-out) {
  transition: $property $duration $timing;
} 