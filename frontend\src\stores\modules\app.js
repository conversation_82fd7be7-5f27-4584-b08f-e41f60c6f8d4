/**
 * 航空航天级IT资产管理系统 - 应用状态管理
 * 文件路径: src/stores/modules/app.js
 * 功能描述: 管理应用全局状态，如侧边栏状态、主题设置等
 */

import { defineStore } from 'pinia'

// 侧边栏状态本地存储键
const SIDEBAR_STATUS_KEY = 'itam_sidebar_status'
// 布局尺寸本地存储键
const SIZE_KEY = 'itam_size'

export const useAppStore = defineStore('app', {
  state: () => ({
    // 侧边栏状态
    sidebar: {
      opened: localStorage.getItem(SIDEBAR_STATUS_KEY) !== 'closed',
      withoutAnimation: false
    },
    // 设备类型
    device: 'desktop',
    // 布局尺寸
    size: localStorage.getItem(SIZE_KEY) || 'default',
    // 是否为移动设备
    isMobile: false,
    // 当前全局加载状态
    loading: false,
    // 主题色
    theme: {
      // 主色
      primary: '#409EFF',
      // 成功色
      success: '#67C23A',
      // 警告色
      warning: '#E6A23C',
      // 危险色
      danger: '#F56C6C',
      // 信息色
      info: '#909399'
    }
  }),
  
  getters: {
    // 侧边栏是否打开
    sidebarOpened: (state) => state.sidebar.opened
  },
  
  actions: {
    /**
     * 切换侧边栏状态
     * @param {boolean} withoutAnimation - 是否使用动画
     */
    toggleSidebar(withoutAnimation = false) {
      this.sidebar.opened = !this.sidebar.opened
      this.sidebar.withoutAnimation = withoutAnimation
      
      if (this.sidebar.opened) {
        localStorage.setItem(SIDEBAR_STATUS_KEY, 'opened')
      } else {
        localStorage.setItem(SIDEBAR_STATUS_KEY, 'closed')
      }
    },
    
    /**
     * 关闭侧边栏
     * @param {boolean} withoutAnimation - 是否使用动画
     */
    closeSidebar(withoutAnimation = false) {
      this.sidebar.opened = false
      this.sidebar.withoutAnimation = withoutAnimation
      localStorage.setItem(SIDEBAR_STATUS_KEY, 'closed')
    },
    
    /**
     * 设置设备类型
     * @param {string} device - 设备类型
     */
    setDevice(device) {
      this.device = device
      this.isMobile = device === 'mobile'
      
      // 移动设备自动关闭侧边栏
      if (device === 'mobile') {
        this.closeSidebar(true)
      }
    },
    
    /**
     * 设置布局尺寸
     * @param {string} size - 尺寸
     */
    setSize(size) {
      this.size = size
      localStorage.setItem(SIZE_KEY, size)
    },
    
    /**
     * 设置加载状态
     * @param {boolean} status - 加载状态
     */
    setLoading(status) {
      this.loading = status
    },
    
    /**
     * 设置主题色
     * @param {Object} theme - 主题颜色配置
     */
    setTheme(theme) {
      this.theme = { ...this.theme, ...theme }
    }
  }
})