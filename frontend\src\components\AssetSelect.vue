<template>
  <el-select
    v-model="selectedValue"
    :placeholder="placeholder"
    :multiple="multiple"
    :filterable="true"
    :remote="true"
    :remote-method="searchAssets"
    :loading="loading"
    :clearable="true"
    @change="handleChange"
  >
    <el-option
      v-for="asset in assets"
      :key="asset.id"
      :label="`${asset.name} (${asset.code})`"
      :value="asset.id"
    >
      <div class="asset-option">
        <span class="asset-name">{{ asset.name }}</span>
        <span class="asset-code">{{ asset.code }}</span>
        <el-tag v-if="asset.status" :type="getStatusType(asset.status)" size="small">
          {{ asset.status }}
        </el-tag>
      </div>
    </el-option>
  </el-select>
</template>

<script>
import { ref, watch } from 'vue'
import { assetApi } from '@/api/asset'

export default {
  name: 'AssetSelect',
  props: {
    modelValue: {
      type: [String, Number, Array],
      default: null
    },
    multiple: {
      type: Boolean,
      default: false
    },
    placeholder: {
      type: String,
      default: '请选择资产'
    }
  },
  emits: ['update:modelValue', 'change'],
  setup(props, { emit }) {
    const selectedValue = ref(props.modelValue)
    const assets = ref([])
    const loading = ref(false)

    const searchAssets = async (query) => {
      if (!query) {
        assets.value = []
        return
      }
      
      loading.value = true
      try {
        const response = await assetApi.search(query)
        assets.value = response.data || []
      } catch (error) {
        console.error('搜索资产失败:', error)
        assets.value = []
      } finally {
        loading.value = false
      }
    }

    const handleChange = (value) => {
      emit('update:modelValue', value)
      emit('change', value)
    }

    const getStatusType = (status) => {
      const statusMap = {
        '正常': 'success',
        '维修中': 'warning', 
        '报废': 'danger',
        '闲置': 'info'
      }
      return statusMap[status] || 'info'
    }

    watch(() => props.modelValue, (newValue) => {
      selectedValue.value = newValue
    })

    return {
      selectedValue,
      assets,
      loading,
      searchAssets,
      handleChange,
      getStatusType
    }
  }
}
</script>

<style scoped>
.asset-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.asset-name {
  font-weight: 500;
}

.asset-code {
  color: #909399;
  font-size: 12px;
}
</style>