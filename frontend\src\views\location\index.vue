<template>
  <div class="location-page">
    <div class="page-header">
      <h2>位置结构</h2>
      <el-button type="primary" @click="handleAdd(null)">
        <el-icon><Plus /></el-icon>添加工厂
      </el-button>
    </div>

    <!-- 位置树形表格 -->
    <el-table
      :data="locationTree"
      row-key="id"
      border
      default-expand-all
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
    >
      <el-table-column prop="code" label="编码" width="120" />
      <el-table-column prop="name" label="名称" width="200" />
      <el-table-column label="类型" width="120">
        <template #default="{ row }">
          {{ getLocationTypeName(row.type) }}
        </template>
      </el-table-column>
      <el-table-column prop="description" label="描述" />
      <el-table-column prop="assetCount" label="资产数量" width="100" align="center" />
      <el-table-column label="状态" width="100" align="center">
        <template #default="{ row }">
          <el-tag :type="row.status === 1 ? 'success' : 'info'">
            {{ row.status === 1 ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="250" fixed="right">
        <template #default="{ row }">
          <el-button 
            v-if="row.type < 4"
            type="primary" 
            link
            @click="handleAdd(row)"
          >
            添加{{ getNextLevelName(row.type) }}
          </el-button>
          <el-button type="primary" link @click="handleEdit(row)">
            编辑
          </el-button>
          <el-button 
            type="danger" 
            link 
            @click="handleDelete(row)"
            :disabled="row.children?.length > 0 || row.assetCount > 0"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="500px"
      @close="resetForm"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入位置名称" />
        </el-form-item>
        <el-form-item label="类型">
          <el-input :value="getLocationTypeName(form.type)" disabled />
        </el-form-item>
        <el-form-item label="上级位置">
          <el-input 
            :value="form.parentId ? parentLocation?.name : '无'" 
            disabled 
          />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            placeholder="请输入位置描述"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :value="1">启用</el-radio>
            <el-radio :value="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { getLocationTree, createLocation, updateLocation, deleteLocation } from '@/api/location'

// 位置类型名称映射
const locationTypes = {
  0: '工厂',
  1: '产线',
  2: '工序',
  3: '工位',
  4: '设备位置'
}

// 数据
const locationTree = ref([])
const dialogVisible = ref(false)
const dialogType = ref('add') // add or edit
const formRef = ref(null)
const form = ref({
  name: '',
  type: 0,
  parentId: null,
  description: '',
  status: 1
})
const parentLocation = ref(null)

// 表单校验规则
const rules = {
  name: [
    { required: true, message: '请输入位置名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  description: [
    { max: 200, message: '长度不能超过 200 个字符', trigger: 'blur' }
  ]
}

// 计算属性
const dialogTitle = computed(() => {
  const action = dialogType.value === 'add' ? '新增' : '编辑'
  const type = getLocationTypeName(form.value.type)
  return `${action}${type}`
})

// 获取位置类型名称
const getLocationTypeName = (type) => locationTypes[type] || '未知'

// 获取下一级类型名称
const getNextLevelName = (type) => locationTypes[type + 1] || ''

// 加载位置树
const loadLocationTree = async () => {
  try {
    const res = await getLocationTree()
    if (res.success) {
      locationTree.value = res.data
    }
  } catch (error) {
    console.error('加载位置树出错:', error)
    ElMessage.error('加载位置树失败')
  }
}

// 打开新增对话框
const handleAdd = (parent) => {
  dialogType.value = 'add'
  form.value = {
    name: '',
    type: parent ? parent.type + 1 : 0,
    parentId: parent ? parent.id : null,
    description: '',
    status: 1
  }
  parentLocation.value = parent
  dialogVisible.value = true
}

// 打开编辑对话框
const handleEdit = (row) => {
  dialogType.value = 'edit'
  form.value = {
    id: row.id,
    name: row.name,
    type: row.type,
    parentId: row.parentId,
    description: row.description,
    status: row.status
  }
  parentLocation.value = locationTree.value.find(item => item.id === row.parentId)
  dialogVisible.value = true
}

// 处理删除
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除该位置吗？删除后不可恢复！',
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const res = await deleteLocation(row.id)
    if (res.success) {
      ElMessage.success('删除成功')
      loadLocationTree()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除位置出错:', error)
      ElMessage.error(error.message || '删除失败')
    }
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    const api = dialogType.value === 'add' ? createLocation : updateLocation
    const res = await api(form.value)
    
    if (res.success) {
      ElMessage.success(dialogType.value === 'add' ? '新增成功' : '更新成功')
      dialogVisible.value = false
      loadLocationTree()
    }
  } catch (error) {
    console.error('提交表单出错:', error)
    ElMessage.error(error.message || '操作失败')
  }
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  form.value = {
    name: '',
    type: 0,
    parentId: null,
    description: '',
    status: 1
  }
}

// 页面加载时获取位置树
onMounted(() => {
  loadLocationTree()
})
</script>

<style scoped>
.location-page {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  font-size: 24px;
}
</style> 