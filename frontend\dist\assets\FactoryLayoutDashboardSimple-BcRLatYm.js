import{_ as a,r as e,c as t,z as s,b as l,d as o,e as i,w as n,t as r,$ as u,n as c,F as d,h as v,E as m,a as h,o as f,f as p,ak as g,K as w,p as y,v as x,ah as k,S as $,al as z,i as C,k as b}from"./index-CG5lHOPO.js";const S={class:"factory-dashboard"},M={class:"dashboard-header"},N={class:"max-w-7xl mx-auto flex flex-wrap items-center justify-between gap-2"},_={class:"flex items-center"},D={class:"header-icon"},O={class:"header-title"},I={class:"update-badge"},J={class:"font-medium"},E={class:"header-controls"},R={key:0,class:"file-import-section"},j={key:0,class:"imported-file-name"},T={class:"max-w-7xl mx-auto p-3 flex-grow"},q={key:0,class:"loading-container"},A={key:1,class:"dashboard-main"},L={class:"stats-panel"},U={class:"card-header"},F={class:"update-time"},H={class:"stats-grid"},P={class:"stat-item operational"},V={class:"stat-icon"},W={class:"stat-content"},B={class:"stat-value"},K={class:"stat-percent"},G={class:"stat-item warning"},Q={class:"stat-icon"},X={class:"stat-content"},Y={class:"stat-value"},Z={class:"stat-item error"},aa={class:"stat-icon"},ea={class:"stat-content"},ta={class:"stat-value"},sa={class:"stat-item idle"},la={class:"stat-icon"},oa={class:"stat-content"},ia={class:"stat-value"},na={class:"factory-layout"},ra={class:"layout-header"},ua={key:0,class:"layout-info"},ca={key:1,class:"current-file"},da={class:"factory-floor"},va=["viewBox"],ma=["data-zone"],ha=["onClick"],fa={class:"cell-content"},pa={key:3,class:"cell-number"},ga=a({__name:"FactoryLayoutDashboardSimple",setup(a){const ga=e([]),wa=e(!0),ya=e(!1),xa=e("import"),ka=e("factory-layout-1748945213262.json"),$a=e(null),za=e(new Date),Ca=e(""),ba=e(null),Sa=e(null),Ma=async()=>{try{let e;if(wa.value=!0,"import"===xa.value&&ba.value)e=ba.value;else{let t;if("custom"===xa.value)t=`/analyresport/${ka.value}`;else{if("default"!==xa.value)return $a.value=_a(),$a.value;t="/Configurations/factory-layout.json"}const s=await fetch(t);if(!s.ok)throw new Error(`HTTP ${s.status}: ${s.statusText}`);const l=await s.text();try{e=JSON.parse(l)}catch(a){throw new Error(`JSON解析失败: ${a.message}`)}}if(e.factoryLayout)$a.value=e.factoryLayout;else{if(!e.zones||!Array.isArray(e.zones))throw new Error("未识别的配置文件格式");$a.value=Na(e)}return $a.value}catch(e){return m.error(`加载布局配置失败: ${e.message}，使用默认配置`),$a.value=_a(),$a.value}finally{wa.value=!1}},Na=a=>{let e=0,t=0;a.zones.forEach((a=>{e=Math.max(e,a.x+a.width),t=Math.max(t,a.y+a.height)}));const s=e+50,l=t+50,o={name:"自定义工厂布局",description:`包含${a.zones.length}个区域的工厂布局`,version:a.version||"1.0",canvas:{width:s,height:l,backgroundColor:"#0f172a",gridSize:20},zones:[],statusDistribution:{operational:.7,warning:.15,error:.1,idle:.05},defaultMetrics:{efficiency:{min:70,max:100},uptime:{min:80,max:100},assetCount:{min:2,max:7},taskCount:{min:1,max:9}}};return a.zones.forEach((a=>{const e=a.rows*a.cols,t=[],s=a.width/a.cols,l=a.height/a.rows;for(let o=0;o<a.rows;o++)for(let e=0;e<a.cols;e++){const i=a.startWorkstation+o*a.cols+e,n=e*s,r=o*l;t.push({id:i,x:n,y:r,name:`${a.name}-工位${i.toString().padStart(3,"0")}`})}o.zones.push({id:`zone${a.id}`,name:a.name,uniqueName:`${a.name}-${a.id}`,description:`${a.name}生产区域`,color:a.color,position:{x:a.x,y:a.y,width:a.width,height:a.height},layout:{type:"grid",rows:a.rows,cols:a.cols},workstations:{startId:a.startWorkstation,count:e,pattern:"grid",positions:t}})})),o},_a=()=>({name:"默认工厂布局",description:"包含1个区域的默认工厂布局",version:"1.0",canvas:{width:1e3,height:600,backgroundColor:"#0f172a",gridSize:20},zones:[{id:"zone1",name:"默认区域",uniqueName:"默认区域-1",description:"默认区域生产区域",color:"#4A90E2",position:{x:100,y:100,width:300,height:270},layout:{type:"grid",rows:3,cols:3},workstations:{startId:1,count:9,pattern:"grid",positions:Array.from({length:9},((a,e)=>({id:e+1,x:e%3*100,y:90*Math.floor(e/3),name:`默认区域-工位${(e+1).toString().padStart(3,"0")}`})))}}],statusDistribution:{operational:.7,warning:.15,error:.1,idle:.05},defaultMetrics:{efficiency:{min:70,max:100},uptime:{min:80,max:100},assetCount:{min:2,max:7},taskCount:{min:1,max:9}}}),Da=()=>{if(!$a.value)return[];const a=[],e=["operational","warning","error","idle"],t=$a.value.statusDistribution,s=[t.operational,t.warning,t.error,t.idle];return $a.value.zones.forEach((t=>{const{rows:l,cols:o}=t.layout,i=t.workstations.startId;for(let n=0;n<l;n++)for(let l=0;l<o;l++){const r=i+n*o+l,u=Math.random();let c="operational",d=0;for(let a=0;a<s.length;a++)if(d+=s[a],u<d){c=e[a];break}const v=$a.value.defaultMetrics||{efficiency:{min:70,max:100},uptime:{min:80,max:100},assetCount:{min:2,max:7},taskCount:{min:1,max:9}};a.push({locationId:r,locationName:`${t.name}-工位${r.toString().padStart(3,"0")}`,locationCode:`WS${r.toString().padStart(3,"0")}`,departmentName:t.uniqueName,originalDepartmentName:t.name,status:c,efficiency:Math.floor(Math.random()*(v.efficiency.max-v.efficiency.min+1))+v.efficiency.min,uptime:Math.floor(Math.random()*(v.uptime.max-v.uptime.min+1))+v.uptime.min,assetCount:Math.floor(Math.random()*(v.assetCount.max-v.assetCount.min+1))+v.assetCount.min,taskCount:Math.floor(Math.random()*(v.taskCount.max-v.taskCount.min+1))+v.taskCount.min,faultCount:"error"===c?Math.floor(3*Math.random())+1:"warning"===c?Math.floor(2*Math.random()):0,lastUpdate:new Date,zoneColor:t.color,zoneId:t.id,isHighlighted:!1,relativeRow:n,relativeCol:l})}})),$a.value.zones.forEach((e=>{a.filter((a=>a.departmentName===e.uniqueName))})),a},Oa=a=>ga.value.filter((e=>e.departmentName===a.uniqueName)).sort(((a,e)=>a.relativeRow!==e.relativeRow?a.relativeRow-e.relativeRow:a.relativeCol-e.relativeCol)),Ia=a=>[`status-${a.status}`,{highlighted:a.isHighlighted}],Ja=a=>a.position?{position:"absolute",left:`${a.position.x}px`,top:`${a.position.y}px`,width:`${a.position.width}px`,height:`${a.position.height}px`,border:`2px dashed ${a.color}40`,borderRadius:"8px",backgroundColor:`${a.color}08`}:{},Ea=a=>{if(!a.layout)return{};const{rows:e,cols:t}=a.layout;return{display:"grid",gridTemplateColumns:`repeat(${t}, 1fr)`,gridTemplateRows:`repeat(${e}, 1fr)`,gap:"2px",height:"100%",width:"100%",padding:"5px"}},Ra=t((()=>{const a=ga.value.length,e=ga.value.filter((a=>"operational"===a.status)).length;return{total:a,operational:e,warning:ga.value.filter((a=>"warning"===a.status)).length,error:ga.value.filter((a=>"error"===a.status)).length,idle:ga.value.filter((a=>"idle"===a.status)).length,operationalPercent:a>0?Math.round(e/a*100):0}})),ja=t((()=>za.value.toLocaleTimeString("zh-CN",{hour:"2-digit",minute:"2-digit",second:"2-digit"}))),Ta=t((()=>{if($a.value&&$a.value.canvas){const{width:a,height:e}=$a.value.canvas;return`0 0 ${a} ${e}`}return"0 0 1200 500"})),qa=t((()=>{if(!$a.value||!$a.value.canvas)return{};const{width:a,height:e}=$a.value.canvas,t=900/a,s=400/e;return{width:`${a}px`,height:`${e}px`,transform:`scale(${Math.min(t,s,1)})`,transformOrigin:"center center"}})),Aa=a=>{if(!a.raw)return void m.error("文件读取失败");const e=new FileReader;e.onload=e=>{try{const t=e.target.result,s=JSON.parse(t);Ca.value=a.name,ba.value=s,m.success(`文件 ${a.name} 导入成功`),Ma().then((()=>{ga.value=Da(),za.value=new Date}))}catch(t){m.error(`JSON文件解析失败: ${t.message}`),Ca.value="",ba.value=null}},e.onerror=()=>{m.error("文件读取失败"),Ca.value="",ba.value=null},e.readAsText(a.raw)},La=async()=>{try{await Ha()?(await Ma(),ga.value=Da(),za.value=new Date):m.warning("默认JSON文件不存在，请选择其他文件或使用默认布局")}catch(a){m.error("加载默认JSON文件失败")}},Ua=async()=>{ya.value=!0;try{await Ma(),ga.value=Da(),za.value=new Date,m.success("数据已刷新")}catch(a){m.error("刷新失败")}finally{ya.value=!1}},Fa=async()=>{try{let a="";switch(xa.value){case"default":a="默认";break;case"custom":a="自定义";break;case"import":if(a="导入文件",!ba.value)return Ca.value="",void m.info("请选择JSON文件进行导入")}await Ma(),ga.value=Da(),za.value=new Date,m.success(`已切换到${a}布局: ${ga.value.length}个工位`)}catch(a){m.error("切换配置失败")}},Ha=async()=>{try{const a=await fetch(`/analyresport/${ka.value}`);if(a.ok){const e=await a.json();return Ca.value=ka.value,ba.value=e,m.success(`自动加载布局文件: ${ka.value}`),!0}}catch(a){}return!1};return s((async()=>{await(async()=>{try{"import"===xa.value&&await Ha(),await Ma(),ga.value=Da(),setInterval((()=>{za.value=new Date}),5e3)}catch(a){m.error("初始化失败，请刷新页面重试")}})()})),(a,e)=>{var t,s;const ka=h("el-icon"),za=h("el-option"),ba=h("el-select"),Ma=h("el-button"),Na=h("el-upload"),_a=h("el-loading-spinner"),Da=h("el-card");return f(),l("div",S,[o("header",M,[o("div",N,[o("div",_,[o("div",D,[i(ka,{size:"32"},{default:n((()=>[i(p(g))])),_:1})]),o("div",O,[e[1]||(e[1]=o("h1",null,"智能制造监控系统",-1)),o("p",null,"实时工厂状态监控 • "+r(Ra.value.total)+"个工位",1)]),o("div",I,[e[2]||(e[2]=o("span",{class:"text-gray-400"},"最后更新: ",-1)),o("span",J,r(ja.value),1)])]),o("div",E,[i(ba,{modelValue:xa.value,"onUpdate:modelValue":e[0]||(e[0]=a=>xa.value=a),onChange:Fa,placeholder:"选择布局配置",class:"config-selector",size:"small"},{default:n((()=>[i(za,{label:"默认布局",value:"default"}),i(za,{label:"自定义布局",value:"custom"}),i(za,{label:"导入JSON文件",value:"import"})])),_:1},8,["modelValue"]),"import"===xa.value?(f(),l("div",R,[i(Na,{ref_key:"uploadRef",ref:Sa,"auto-upload":!1,"show-file-list":!1,accept:".json","on-change":Aa,class:"json-uploader"},{default:n((()=>[i(Ma,{type:"info",size:"small",icon:p(w)},{default:n((()=>e[3]||(e[3]=[y("选择JSON")]))),_:1},8,["icon"])])),_:1},512),i(Ma,{type:"success",size:"small",onClick:La,title:"加载默认JSON文件"},{default:n((()=>e[4]||(e[4]=[y(" 加载默认 ")]))),_:1}),Ca.value?(f(),l("span",j,r(Ca.value),1)):u("",!0)])):u("",!0),i(Ma,{onClick:Ua,loading:ya.value,size:"small",circle:"",title:"刷新数据"},{default:n((()=>[i(ka,null,{default:n((()=>[i(p(x))])),_:1})])),_:1},8,["loading"])])])]),o("main",T,[wa.value?(f(),l("div",q,[i(_a,{text:"正在加载工厂布局配置..."})])):(f(),l("div",A,[o("div",L,[i(Da,{class:"stats-card"},{header:n((()=>[o("div",U,[e[5]||(e[5]=o("span",null,"实时概览",-1)),o("span",F,r(ja.value),1)])])),default:n((()=>[o("div",H,[o("div",P,[o("div",V,[i(ka,null,{default:n((()=>[i(p(k))])),_:1})]),o("div",W,[o("div",B,r(Ra.value.operational),1),e[6]||(e[6]=o("div",{class:"stat-label"},"运行正常",-1)),o("div",K,r(Ra.value.operationalPercent)+"%",1)])]),o("div",G,[o("div",Q,[i(ka,null,{default:n((()=>[i(p($))])),_:1})]),o("div",X,[o("div",Y,r(Ra.value.warning),1),e[7]||(e[7]=o("div",{class:"stat-label"},"警告状态",-1))])]),o("div",Z,[o("div",aa,[i(ka,null,{default:n((()=>[i(p(z))])),_:1})]),o("div",ea,[o("div",ta,r(Ra.value.error),1),e[8]||(e[8]=o("div",{class:"stat-label"},"错误状态",-1))])]),o("div",sa,[o("div",la,[i(ka,null,{default:n((()=>[i(p(w))])),_:1})]),o("div",oa,[o("div",ia,r(Ra.value.idle),1),e[9]||(e[9]=o("div",{class:"stat-label"},"空闲工位",-1))])])])])),_:1})]),o("div",na,[o("div",ra,[o("span",null,"显示: "+r(ga.value.length)+" 个工位",1),$a.value?(f(),l("span",ua," | "+r((null==(t=$a.value.zones)?void 0:t.length)||0)+" 个区域 | "+r($a.value.name||"未命名布局"),1)):u("",!0),Ca.value?(f(),l("span",ca," | 当前文件: "+r(Ca.value),1)):u("",!0)]),o("div",da,[(f(),l("svg",{class:"factory-grid",viewBox:Ta.value},e[10]||(e[10]=[o("defs",null,[o("pattern",{id:"grid",width:"20",height:"20",patternUnits:"userSpaceOnUse"},[o("path",{d:"M 20 0 L 0 0 0 20",fill:"none",stroke:"rgba(107, 148, 214, 0.08)","stroke-width":"1"})])],-1),o("rect",{width:"100%",height:"100%",fill:"url(#grid)"},null,-1)]),8,va)),o("div",{class:"zone-containers",style:c(qa.value)},[(f(!0),l(d,null,v((null==(s=$a.value)?void 0:s.zones)||[],(a=>(f(),l("div",{key:a.id,class:C(["zone-container",`zone-${a.id}`]),"data-zone":a.name,style:c(Ja(a))},[o("div",{class:"zone-workstations",style:c(Ea(a))},[(f(!0),l(d,null,v(Oa(a),(a=>(f(),l("div",{key:a.locationId,class:C(["workstation-cell",Ia(a)]),onClick:e=>{return t=a.locationId,void m.info(`点击了工位 ${t}`);var t}},[o("div",fa,["error"===a.status?(f(),b(ka,{key:0,class:"status-icon error"},{default:n((()=>[i(p(z))])),_:1})):"warning"===a.status?(f(),b(ka,{key:1,class:"status-icon warning"},{default:n((()=>[i(p($))])),_:1})):"operational"===a.status?(f(),b(ka,{key:2,class:"status-icon operational"},{default:n((()=>[i(p(k))])),_:1})):(f(),l("span",pa,r(a.locationId),1))])],10,ha)))),128))],4)],14,ma)))),128))],4)])])]))])])}}},[["__scopeId","data-v-fb0fd298"]]);export{ga as default};
