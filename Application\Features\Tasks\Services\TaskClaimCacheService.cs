#nullable enable
// File: Application/Features/Tasks/Services/TaskClaimCacheService.cs
// Description: 任务领取状态缓存服务，实现分层缓存策略

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using ItAssetsSystem.Infrastructure.Data;
using ItAssetsSystem.Application.Features.Tasks.Dtos;
using TaskClaim = ItAssetsSystem.Domain.Entities.Tasks.TaskClaim;

namespace ItAssetsSystem.Application.Features.Tasks.Services
{
    /// <summary>
    /// 任务领取状态信息
    /// </summary>
    public class TaskClaimInfo
    {
        public long TaskId { get; set; }
        public int ClaimedByUserId { get; set; }
        public string? ClaimedByUserName { get; set; }
        public DateTime ClaimedAt { get; set; }
        public string ClaimStatus { get; set; } = string.Empty;
        public bool IsClaimedByCurrentUser { get; set; }
    }

    /// <summary>
    /// 用户任务领取状态缓存
    /// </summary>
    public class UserTaskClaimCache
    {
        public int UserId { get; set; }
        public DateTime Date { get; set; }
        public Dictionary<long, TaskClaimInfo> TaskClaimStatuses { get; set; } = new();
        public DateTime CacheTime { get; set; }
        public TimeSpan ValidDuration { get; set; } = TimeSpan.FromSeconds(30); // 30秒有效期

        public bool IsValid => DateTime.Now - CacheTime < ValidDuration;
    }

    /// <summary>
    /// 任务领取状态缓存服务接口
    /// </summary>
    public interface ITaskClaimCacheService
    {
        Task<UserTaskClaimCache?> GetUserClaimCacheAsync(int userId, DateTime date);
        Task SetUserClaimCacheAsync(int userId, DateTime date, UserTaskClaimCache cache);
        Task InvalidateUserClaimCacheAsync(int userId, DateTime date);
        Task InvalidateTaskClaimCacheAsync(long taskId, DateTime date);
        Task<Dictionary<long, TaskClaimInfo>> GetTaskClaimStatusesAsync(List<long> taskIds, int currentUserId, DateTime date);
        Task UpdateTaskClaimStatusAsync(long taskId, int userId, TaskClaimInfo claimInfo, DateTime date);
    }

    /// <summary>
    /// 任务领取状态缓存服务实现
    /// </summary>
    public class TaskClaimCacheService : ITaskClaimCacheService
    {
        private readonly IMemoryCache _memoryCache;
        private readonly AppDbContext _context;
        private readonly ILogger<TaskClaimCacheService> _logger;

        // 缓存键前缀
        private const string USER_CLAIM_CACHE_PREFIX = "user_claims";
        private const string GLOBAL_CLAIM_CACHE_PREFIX = "global_claims";

        // 缓存过期时间
        private static readonly TimeSpan USER_CACHE_DURATION = TimeSpan.FromSeconds(30); // 用户特定缓存30秒
        private static readonly TimeSpan GLOBAL_CACHE_DURATION = TimeSpan.FromMinutes(1); // 全局缓存1分钟

        public TaskClaimCacheService(
            IMemoryCache memoryCache,
            AppDbContext context,
            ILogger<TaskClaimCacheService> logger)
        {
            _memoryCache = memoryCache;
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// 获取用户任务领取状态缓存
        /// </summary>
        public async Task<UserTaskClaimCache?> GetUserClaimCacheAsync(int userId, DateTime date)
        {
            var cacheKey = GenerateUserCacheKey(userId, date);
            
            if (_memoryCache.TryGetValue(cacheKey, out UserTaskClaimCache? cache) && cache != null && cache.IsValid)
            {
                _logger.LogDebug("从缓存获取用户 {UserId} 的任务领取状态，日期: {Date}", userId, date.ToString("yyyy-MM-dd"));
                return cache;
            }

            return null;
        }

        /// <summary>
        /// 设置用户任务领取状态缓存
        /// </summary>
        public async Task SetUserClaimCacheAsync(int userId, DateTime date, UserTaskClaimCache cache)
        {
            var cacheKey = GenerateUserCacheKey(userId, date);
            cache.CacheTime = DateTime.Now;
            
            _memoryCache.Set(cacheKey, cache, USER_CACHE_DURATION);
            _logger.LogDebug("设置用户 {UserId} 的任务领取状态缓存，包含 {Count} 个任务，日期: {Date}", 
                userId, cache.TaskClaimStatuses.Count, date.ToString("yyyy-MM-dd"));
        }

        /// <summary>
        /// 失效用户任务领取状态缓存
        /// </summary>
        public async Task InvalidateUserClaimCacheAsync(int userId, DateTime date)
        {
            var cacheKey = GenerateUserCacheKey(userId, date);
            _memoryCache.Remove(cacheKey);
            _logger.LogDebug("失效用户 {UserId} 的任务领取状态缓存，日期: {Date}", userId, date.ToString("yyyy-MM-dd"));
        }

        /// <summary>
        /// 失效特定任务的领取状态缓存
        /// </summary>
        public async Task InvalidateTaskClaimCacheAsync(long taskId, DateTime date)
        {
            // 由于我们不知道哪些用户缓存了这个任务，这里采用标记失效的方式
            // 在实际应用中，可以考虑使用Redis等支持模式匹配的缓存
            var globalCacheKey = GenerateGlobalCacheKey(date);
            _memoryCache.Remove(globalCacheKey);
            _logger.LogDebug("失效任务 {TaskId} 的全局领取状态缓存，日期: {Date}", taskId, date.ToString("yyyy-MM-dd"));
        }

        /// <summary>
        /// 获取任务领取状态（带缓存优化）
        /// </summary>
        public async Task<Dictionary<long, TaskClaimInfo>> GetTaskClaimStatusesAsync(List<long> taskIds, int currentUserId, DateTime date)
        {
            if (!taskIds.Any())
                return new Dictionary<long, TaskClaimInfo>();

            // 1. 尝试从用户缓存获取
            var userCache = await GetUserClaimCacheAsync(currentUserId, date);
            if (userCache != null)
            {
                var cachedResults = new Dictionary<long, TaskClaimInfo>();
                var uncachedTaskIds = new List<long>();

                foreach (var taskId in taskIds)
                {
                    if (userCache.TaskClaimStatuses.TryGetValue(taskId, out var cachedInfo))
                    {
                        cachedResults[taskId] = cachedInfo;
                    }
                    else
                    {
                        uncachedTaskIds.Add(taskId);
                    }
                }

                // 如果所有任务都在缓存中，直接返回
                if (!uncachedTaskIds.Any())
                {
                    _logger.LogDebug("所有任务领取状态都从用户缓存获取，用户: {UserId}, 任务数: {Count}", currentUserId, taskIds.Count);
                    return cachedResults;
                }

                // 查询未缓存的任务
                var uncachedResults = await QueryTaskClaimStatusesFromDatabase(uncachedTaskIds, currentUserId, date);
                
                // 合并结果
                foreach (var kvp in uncachedResults)
                {
                    cachedResults[kvp.Key] = kvp.Value;
                    userCache.TaskClaimStatuses[kvp.Key] = kvp.Value; // 更新缓存
                }

                // 更新用户缓存
                await SetUserClaimCacheAsync(currentUserId, date, userCache);
                
                return cachedResults;
            }

            // 2. 缓存未命中，从数据库查询
            var results = await QueryTaskClaimStatusesFromDatabase(taskIds, currentUserId, date);

            // 3. 创建新的用户缓存
            var newUserCache = new UserTaskClaimCache
            {
                UserId = currentUserId,
                Date = date,
                TaskClaimStatuses = results
            };

            await SetUserClaimCacheAsync(currentUserId, date, newUserCache);

            return results;
        }

        /// <summary>
        /// 更新任务领取状态并刷新缓存
        /// </summary>
        public async Task UpdateTaskClaimStatusAsync(long taskId, int userId, TaskClaimInfo claimInfo, DateTime date)
        {
            // 1. 更新用户缓存
            var userCache = await GetUserClaimCacheAsync(userId, date);
            if (userCache != null)
            {
                userCache.TaskClaimStatuses[taskId] = claimInfo;
                await SetUserClaimCacheAsync(userId, date, userCache);
            }

            // 2. 失效相关缓存
            await InvalidateTaskClaimCacheAsync(taskId, date);

            _logger.LogDebug("更新任务 {TaskId} 的领取状态缓存，用户: {UserId}", taskId, userId);
        }

        /// <summary>
        /// 从数据库查询任务领取状态
        /// </summary>
        private async Task<Dictionary<long, TaskClaimInfo>> QueryTaskClaimStatusesFromDatabase(List<long> taskIds, int currentUserId, DateTime date)
        {
            var results = new Dictionary<long, TaskClaimInfo>();

            try
            {
                // 查询今日的任务领取记录
                var taskClaims = await _context.TaskClaims
                    .Where(tc => taskIds.Contains(tc.TaskId) 
                              && tc.ClaimDate.Date == date.Date 
                              && tc.ClaimStatus == "Claimed")
                    .Include(tc => tc.ClaimedByUser)
                    .ToListAsync();

                // 按任务ID分组，支持多负责人分别领取
                var claimsByTaskId = taskClaims
                    .GroupBy(tc => tc.TaskId)
                    .ToDictionary(g => g.Key, g => g.ToList());

                foreach (var taskId in taskIds)
                {
                    if (claimsByTaskId.TryGetValue(taskId, out var claims) && claims.Any())
                    {
                        // 优先显示当前用户的领取记录
                        var currentUserClaim = claims.FirstOrDefault(c => c.ClaimedBy == currentUserId);
                        var displayClaim = currentUserClaim ?? claims.First();

                        results[taskId] = new TaskClaimInfo
                        {
                            TaskId = taskId,
                            ClaimedByUserId = displayClaim.ClaimedBy,
                            ClaimedByUserName = displayClaim.ClaimedByUser?.Name ?? displayClaim.ClaimedByUser?.Username,
                            ClaimedAt = displayClaim.ClaimedAt,
                            ClaimStatus = displayClaim.ClaimStatus,
                            IsClaimedByCurrentUser = displayClaim.ClaimedBy == currentUserId
                        };
                    }
                }

                _logger.LogDebug("从数据库查询任务领取状态，任务数: {TaskCount}, 有领取记录: {ClaimedCount}", 
                    taskIds.Count, results.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "查询任务领取状态时发生错误");
            }

            return results;
        }

        /// <summary>
        /// 生成用户缓存键
        /// </summary>
        private static string GenerateUserCacheKey(int userId, DateTime date)
        {
            return $"{USER_CLAIM_CACHE_PREFIX}:user:{userId}:date:{date:yyyyMMdd}";
        }

        /// <summary>
        /// 生成全局缓存键
        /// </summary>
        private static string GenerateGlobalCacheKey(DateTime date)
        {
            return $"{GLOBAL_CLAIM_CACHE_PREFIX}:date:{date:yyyyMMdd}";
        }
    }
}
