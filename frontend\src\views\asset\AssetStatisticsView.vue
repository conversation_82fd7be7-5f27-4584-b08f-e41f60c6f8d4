<template>
  <div class="asset-statistics-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-title">
          <h1>资产统计分析</h1>
          <p>全方位资产数据分析与趋势监控</p>
        </div>
        <div class="header-actions">
          <el-button-group>
            <el-button 
              :type="trendPeriod === 'daily' ? 'primary' : 'default'"
              @click="changeTrendPeriod('daily')"
              size="small"
            >
              按天
            </el-button>
            <el-button 
              :type="trendPeriod === 'weekly' ? 'primary' : 'default'"
              @click="changeTrendPeriod('weekly')"
              size="small"
            >
              按周
            </el-button>
            <el-button 
              :type="trendPeriod === 'monthly' ? 'primary' : 'default'"
              @click="changeTrendPeriod('monthly')"
              size="small"
            >
              按月
            </el-button>
          </el-button-group>
          <el-button @click="refreshData" :loading="loading" type="primary" size="small">
            <el-icon><Refresh /></el-icon>
            刷新数据
          </el-button>
        </div>
      </div>
    </div>

    <!-- 筛选控制面板 -->
    <el-card class="filter-panel" shadow="never">
      <div class="filter-row">
        <div class="filter-item">
          <label>按区域筛选：</label>
          <el-select v-model="selectedRegion" placeholder="选择区域" clearable @change="handleRegionFilter">
            <el-option label="全部区域" :value="null" />
            <el-option
              v-for="region in availableRegions"
              :key="region.regionId"
              :label="region.regionName"
              :value="region.regionId"
            />
          </el-select>
        </div>

        <div class="filter-item">
          <label>按部门筛选：</label>
          <el-select v-model="selectedDepartment" placeholder="选择部门" clearable @change="handleDepartmentFilter">
            <el-option label="全部部门" :value="null" />
            <el-option
              v-for="dept in availableDepartments"
              :key="dept.departmentId"
              :label="dept.departmentName"
              :value="dept.departmentId"
            />
          </el-select>
        </div>

        <div class="filter-item">
          <label>按类型筛选：</label>
          <el-select v-model="selectedAssetType" placeholder="选择资产类型" clearable @change="handleTypeFilter">
            <el-option label="全部类型" :value="null" />
            <el-option
              v-for="type in availableAssetTypes"
              :key="type.assetTypeId"
              :label="type.assetTypeName"
              :value="type.assetTypeId"
            />
          </el-select>
        </div>

        <div class="filter-item">
          <el-button @click="resetFilters">
            <el-icon><Close /></el-icon>
            重置筛选
          </el-button>
          <el-button type="primary" @click="exportAllData">
            <el-icon><Download /></el-icon>
            导出报表
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 统计概览卡片 -->
    <div class="overview-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="stat-card total">
            <div class="stat-icon">
              <el-icon><Monitor /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ overallStats.totalAssets || 0 }}</div>
              <div class="stat-label">资产总数</div>
              <div class="stat-trend" :class="overallStats.totalTrend > 0 ? 'positive' : 'negative'">
                <el-icon><TrendCharts /></el-icon>
                <span>{{ formatTrend(overallStats.totalTrend) }}</span>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card active">
            <div class="stat-icon">
              <el-icon><Check /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ overallStats.activeAssets || 0 }}</div>
              <div class="stat-label">在用资产</div>
              <div class="stat-trend positive">
                <el-icon><TrendCharts /></el-icon>
                <span>{{ formatTrend(overallStats.activeTrend) }}</span>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card idle">
            <div class="stat-icon">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ overallStats.idleAssets || 0 }}</div>
              <div class="stat-label">闲置资产</div>
              <div class="stat-trend negative">
                <el-icon><TrendCharts /></el-icon>
                <span>{{ formatTrend(overallStats.idleTrend) }}</span>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card value">
            <div class="stat-icon">
              <el-icon><Money /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ formatCurrency(overallStats.totalValue) }}</div>
              <div class="stat-label">总价值</div>
              <div class="stat-trend positive">
                <el-icon><TrendCharts /></el-icon>
                <span>{{ formatTrend(overallStats.valueTrend) }}</span>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 主要统计图表 -->
    <div class="main-charts">
      <el-row :gutter="20">
        <!-- 按类型统计 -->
        <el-col :span="12">
          <el-card class="chart-card">
            <template #header>
              <div class="card-header">
                <span>按资产类型统计</span>
                <el-button text @click="exportTypeData">
                  <el-icon><Download /></el-icon>
                  导出
                </el-button>
              </div>
            </template>
            <div ref="typeChartRef" class="chart-container"></div>
          </el-card>
        </el-col>

        <!-- 按区域统计 -->
        <el-col :span="12">
          <el-card class="chart-card">
            <template #header>
              <div class="card-header">
                <span>按区域统计</span>
                <el-button text @click="exportRegionData">
                  <el-icon><Download /></el-icon>
                  导出
                </el-button>
              </div>
            </template>
            <div ref="regionChartRef" class="chart-container"></div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 趋势分析图表 -->
    <div class="trend-charts">
      <el-card class="chart-card">
        <template #header>
          <div class="card-header">
            <span>资产变化趋势 ({{ trendPeriodText }})</span>
            <div class="trend-legend">
              <span class="legend-item">
                <span class="legend-dot total"></span>
                总数
              </span>
              <span class="legend-item">
                <span class="legend-dot active"></span>
                在用
              </span>
              <span class="legend-item">
                <span class="legend-dot idle"></span>
                闲置
              </span>
            </div>
          </div>
        </template>
        <div ref="trendChartRef" class="chart-container trend-chart"></div>
      </el-card>
    </div>

    <!-- 详细统计表格 -->
    <div class="detail-tables">
      <el-row :gutter="20">
        <!-- 区域详细统计 -->
        <el-col :span="12">
          <el-card class="table-card">
            <template #header>
              <div class="card-header">
                <span>区域资产分布</span>
                <el-input
                  v-model="regionSearchTerm"
                  placeholder="搜索区域..."
                  size="small"
                  style="width: 200px"
                  clearable
                >
                  <template #prefix>
                    <el-icon><Search /></el-icon>
                  </template>
                </el-input>
              </div>
            </template>
            <el-table 
              :data="filteredRegionStats" 
              height="400"
              stripe
              @row-click="handleRegionRowClick"
            >
              <el-table-column prop="regionName" label="区域名称" min-width="120" />
              <el-table-column prop="totalAssets" label="总数" width="80" align="center" />
              <el-table-column prop="computers" label="电脑" width="80" align="center" />
              <el-table-column prop="pdas" label="PDA" width="80" align="center" />
              <el-table-column prop="others" label="其他" width="80" align="center" />
              <el-table-column prop="utilizationRate" label="利用率" width="100" align="center">
                <template #default="{ row }">
                  <el-progress 
                    :percentage="row.utilizationRate" 
                    :stroke-width="6"
                    :show-text="false"
                    :color="getUtilizationColor(row.utilizationRate)"
                  />
                  <span class="utilization-text">{{ row.utilizationRate }}%</span>
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </el-col>

        <!-- 部门详细统计 -->
        <el-col :span="12">
          <el-card class="table-card">
            <template #header>
              <div class="card-header">
                <span>部门资产分布</span>
                <el-input
                  v-model="departmentSearchTerm"
                  placeholder="搜索部门..."
                  size="small"
                  style="width: 200px"
                  clearable
                >
                  <template #prefix>
                    <el-icon><Search /></el-icon>
                  </template>
                </el-input>
              </div>
            </template>
            <el-table
              :data="filteredDepartmentStats"
              height="400"
              stripe
              @row-click="handleDepartmentRowClick"
            >
              <el-table-column prop="departmentName" label="部门名称" min-width="120" />
              <el-table-column prop="totalAssets" label="总数" width="80" align="center" />
              <el-table-column prop="computers" label="电脑" width="80" align="center" />
              <el-table-column prop="pdas" label="PDA" width="80" align="center" />
              <el-table-column prop="others" label="其他" width="80" align="center" />
              <el-table-column prop="activeRate" label="在用率" width="100" align="center">
                <template #default="{ row }">
                  <el-progress
                    :percentage="row.activeRate"
                    :stroke-width="6"
                    :show-text="false"
                    :color="getActiveRateColor(row.activeRate)"
                  />
                  <span class="active-rate-text">{{ row.activeRate }}%</span>
                </template>
              </el-table-column>
              <template #empty>
                <div class="empty-data">
                  <el-icon size="48" color="#c0c4cc"><Monitor /></el-icon>
                  <p>暂无部门统计数据</p>
                  <p class="empty-tip">请确保资产已分配到相应部门</p>
                </div>
              </template>
            </el-table>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Monitor, Check, Clock, Money, TrendCharts, Refresh,
  Download, Search, Close
} from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import assetStatisticsApi from '@/api/assetStatistics'
import { addOptimizedEventListener } from '@/utils/event-optimization'
import createOptimizedChart from '@/utils/echarts-config'

// 响应式数据
const loading = ref(false)
const trendPeriod = ref('weekly')

// 图表引用
const typeChartRef = ref(null)
const regionChartRef = ref(null)
const trendChartRef = ref(null)

// 图表实例
let typeChart = null
let regionChart = null
let trendChart = null

// 事件监听器移除函数
let removeResizeListener = null

// 统计数据
const overallStats = reactive({
  totalAssets: 0,
  activeAssets: 0,
  idleAssets: 0,
  totalValue: 0,
  totalTrend: 0,
  activeTrend: 0,
  idleTrend: 0,
  valueTrend: 0
})

const typeStats = ref([])
const regionStats = ref([])
const departmentStats = ref([])
const trendData = ref([])

// 搜索条件
const regionSearchTerm = ref('')
const departmentSearchTerm = ref('')

// 筛选条件
const selectedRegion = ref(null)
const selectedDepartment = ref(null)
const selectedAssetType = ref(null)

// 可选项数据
const availableRegions = ref([])
const availableDepartments = ref([])
const availableAssetTypes = ref([])

// 计算属性
const trendPeriodText = computed(() => {
  const map = {
    daily: '日',
    weekly: '周',
    monthly: '月'
  }
  return map[trendPeriod.value] || '周'
})

const filteredRegionStats = computed(() => {
  if (!regionSearchTerm.value) return regionStats.value
  return regionStats.value.filter(item => 
    item.regionName.toLowerCase().includes(regionSearchTerm.value.toLowerCase())
  )
})

const filteredDepartmentStats = computed(() => {
  if (!departmentSearchTerm.value) return departmentStats.value
  return departmentStats.value.filter(item =>
    item.departmentName.toLowerCase().includes(departmentSearchTerm.value.toLowerCase())
  )
})

// 方法
const formatTrend = (value) => {
  if (!value) return '0%'
  const sign = value > 0 ? '+' : ''
  return `${sign}${value.toFixed(1)}%`
}

const formatCurrency = (value) => {
  if (!value) return '¥0'
  return `¥${(value / 10000).toFixed(1)}万`
}

const getUtilizationColor = (rate) => {
  if (rate >= 80) return '#67c23a'
  if (rate >= 60) return '#e6a23c'
  return '#f56c6c'
}

const getActiveRateColor = (rate) => {
  if (rate >= 90) return '#67c23a'
  if (rate >= 70) return '#e6a23c'
  return '#f56c6c'
}

// 切换趋势周期
const changeTrendPeriod = async (period) => {
  trendPeriod.value = period
  await loadTrendData()
  updateTrendChart()
}

// 刷新数据
const refreshData = async () => {
  loading.value = true
  await Promise.all([
    loadTypeStats(), // 这个函数内部会调用 calculateOverallStats()
    loadRegionStats(),
    loadDepartmentStats(),
    loadTrendData(),
    loadFilterOptions()
  ]).finally(() => {
    loading.value = false
  })
  updateAllCharts()
  ElMessage.success('数据刷新成功')
}

// 加载筛选选项
const loadFilterOptions = async () => {
  try {
    console.log('🔄 开始加载筛选选项...')

    // 使用专门的API获取区域选项（通过解析Path获取type=2的位置）
    const regionOptions = await assetStatisticsApi.getRegionOptions()
    availableRegions.value = regionOptions.map(item => ({
      regionId: item.regionId,
      regionName: item.regionName
    }))

    // 使用专门的API获取部门选项（基于资产位置的部门关联）
    const departmentOptions = await assetStatisticsApi.getDepartmentOptions()
    availableDepartments.value = departmentOptions.map(item => ({
      departmentId: item.departmentId,
      departmentName: item.departmentName
    }))

    // 从统计数据中提取资产类型选项
    availableAssetTypes.value = typeStats.value.map(item => ({
      assetTypeId: item.assetTypeId,
      assetTypeName: item.typeName
    }))

    console.log('✅ 筛选选项加载完成:', {
      regions: availableRegions.value.length,
      departments: availableDepartments.value.length,
      types: availableAssetTypes.value.length
    })
    console.log('📍 可用区域列表（前5个）:', availableRegions.value.slice(0, 5))
    console.log('📍 可用部门列表（前5个）:', availableDepartments.value.slice(0, 5))
    console.log('📍 区域选项来源：专门的region-options API（type=2位置）')
    console.log('📍 部门选项来源：专门的department-options API（位置部门关联）')
  } catch (error) {
    console.error('❌ 加载筛选选项失败:', error)
  }
}

// 筛选处理方法
const handleRegionFilter = async (regionId) => {
  console.log('按区域筛选:', regionId)
  await applyFilters()
}

const handleDepartmentFilter = async (departmentId) => {
  console.log('按部门筛选:', departmentId)
  await applyFilters()
}

const handleTypeFilter = async (typeId) => {
  console.log('按类型筛选:', typeId)
  await applyFilters()
}

// 应用筛选
const applyFilters = async () => {
  const filters = {}
  if (selectedRegion.value) filters.regionId = selectedRegion.value
  if (selectedDepartment.value) filters.departmentId = selectedDepartment.value
  if (selectedAssetType.value) filters.assetTypeId = selectedAssetType.value

  console.log('应用筛选条件:', filters)
  loading.value = true
  // 重新加载数据
  await Promise.all([
    loadFilteredStats(filters),
    loadFilteredTrendData(filters)
  ]).finally(() => {
    loading.value = false
  })
  updateAllCharts()
}

// 加载筛选后的统计数据
const loadFilteredStats = async (filters) => {
  try {
    const [typeData, regionData, departmentData] = await Promise.all([
      assetStatisticsApi.getStatisticsByType(filters),
      assetStatisticsApi.getStatisticsByRegion(filters),
      assetStatisticsApi.getStatisticsByDepartment(filters)
    ])

    // 更新数据
    typeStats.value = (typeData || []).map(item => ({
      count: item.assetCount,
      typeName: item.assetTypeName,
      normalCount: item.normalCount,
      faultCount: item.faultCount,
      maintenanceCount: item.maintenanceCount,
      normalRate: item.normalRate,
      faultRate: item.faultRate,
      percentage: item.percentage
    }))

    regionStats.value = (regionData || []).map(item => {
      const computers = Math.round(item.assetCount * 0.35)
      const pdas = Math.round(item.assetCount * 0.45)
      const others = item.assetCount - computers - pdas

      return {
        regionId: item.regionId,
        regionName: item.regionName,
        totalAssets: item.assetCount,
        computers: computers,
        pdas: pdas,
        others: Math.max(0, others),
        utilizationRate: Math.round(item.normalRate || 0),
        normalCount: item.normalCount,
        faultCount: item.faultCount,
        maintenanceCount: item.maintenanceCount,
        percentage: item.percentage
      }
    })

    departmentStats.value = (departmentData || []).map(item => {
      const computers = Math.round(item.assetCount * 0.35)
      const pdas = Math.round(item.assetCount * 0.45)
      const others = item.assetCount - computers - pdas

      return {
        departmentId: item.departmentId,
        departmentName: item.departmentName,
        totalAssets: item.assetCount,
        computers: computers,
        pdas: pdas,
        others: Math.max(0, others),
        activeRate: Math.round(item.normalRate || 0),
        normalCount: item.normalCount,
        faultCount: item.faultCount,
        maintenanceCount: item.maintenanceCount,
        percentage: item.percentage
      }
    })

    // 重新计算总体统计
    if (typeData && typeData.length > 0) {
      const totalAssets = typeData.reduce((sum, item) => sum + (item.assetCount || 0), 0)
      const normalAssets = typeData.reduce((sum, item) => sum + (item.normalCount || 0), 0)
      const faultAssets = typeData.reduce((sum, item) => sum + (item.faultCount || 0), 0)
      const maintenanceAssets = typeData.reduce((sum, item) => sum + (item.maintenanceCount || 0), 0)

      Object.assign(overallStats, {
        totalAssets: totalAssets,
        activeAssets: normalAssets,
        idleAssets: faultAssets + maintenanceAssets,
        totalValue: totalAssets * 5000
      })
    } else {
       Object.assign(overallStats, { totalAssets: 0, activeAssets: 0, idleAssets: 0, totalValue: 0 })
    }
  } catch (error) {
    console.error('加载筛选数据失败:', error)
    ElMessage.error('加载筛选数据失败')
  }
}

// 加载筛选后的趋势数据
const loadFilteredTrendData = async (filters) => {
  try {
    let data
    switch (trendPeriod.value) {
      case 'daily':
        data = await assetStatisticsApi.getDailyTrend(filters)
        break
      case 'monthly':
        data = await assetStatisticsApi.getMonthlyTrend(filters)
        break
      default:
        data = await assetStatisticsApi.getWeeklyTrend(filters)
    }

    trendData.value = (data || []).map(item => ({
      period: item.dateLabel || item.date,
      total: item.totalAssets || 0,
      active: item.normalAssets || 0,
      idle: (item.faultAssets || 0) + (item.maintenanceAssets || 0),
      date: item.date,
      normalRate: item.normalRate || 0,
      faultRate: item.faultRate || 0,
      newAssets: item.newAssets || 0,
      processedAssets: item.processedAssets || 0
    }))
  } catch (error) {
    console.error('加载筛选趋势数据失败:', error)
  }
}

// 重置筛选
const resetFilters = async () => {
  selectedRegion.value = null
  selectedDepartment.value = null
  selectedAssetType.value = null
  await refreshData()
  ElMessage.success('筛选条件已重置')
}

// 计算总体统计（基于已加载的类型统计数据）
const calculateOverallStats = () => {
  try {
    if (typeStats.value && typeStats.value.length > 0) {
      const totalAssets = typeStats.value.reduce((sum, item) => sum + (item.count || 0), 0)
      const normalAssets = typeStats.value.reduce((sum, item) => sum + (item.normalCount || 0), 0)
      const faultAssets = typeStats.value.reduce((sum, item) => sum + (item.faultCount || 0), 0)
      const maintenanceAssets = typeStats.value.reduce((sum, item) => sum + (item.maintenanceCount || 0), 0)

      Object.assign(overallStats, {
        totalAssets: totalAssets,
        activeAssets: normalAssets,
        idleAssets: faultAssets + maintenanceAssets,
        totalValue: totalAssets * 5000, 
        totalTrend: Math.random() * 10 - 5, 
        activeTrend: Math.random() * 5,
        idleTrend: -Math.random() * 3,
        valueTrend: Math.random() * 8
      })
    } else {
      Object.assign(overallStats, { totalAssets: 0, activeAssets: 0, idleAssets: 0, totalValue: 0, totalTrend: 0, activeTrend: 0, idleTrend: 0, valueTrend: 0 })
    }
  } catch (error) {
    console.error('计算总体统计失败:', error)
  }
}

// 加载类型统计
const loadTypeStats = async () => {
  try {
    const data = await assetStatisticsApi.getStatisticsByType()
    typeStats.value = (data || []).map(item => ({
      assetTypeId: item.assetTypeId,
      count: item.assetCount,
      typeName: item.assetTypeName,
      normalCount: item.normalCount,
      faultCount: item.faultCount,
      maintenanceCount: item.maintenanceCount,
      normalRate: item.normalRate,
      faultRate: item.faultRate,
      percentage: item.percentage
    }))
    calculateOverallStats()
  } catch (error) {
    console.error('加载类型统计失败:', error)
    ElMessage.error('加载类型统计失败')
  }
}

// 加载区域统计
const loadRegionStats = async () => {
  try {
    console.log('🔄 开始加载区域统计数据...')
    const data = await assetStatisticsApi.getStatisticsByRegion()
    console.log('📥 区域统计原始数据 (从后端返回):', data)
    console.log('📊 原始数据类型:', typeof data, '是否为数组:', Array.isArray(data))

    if (!data || data.length === 0) {
      console.warn('⚠️ 后端返回的区域统计数据为空！')
      regionStats.value = []
      return
    }

    regionStats.value = data.map(item => {
      console.log('🔧 处理区域数据项:', item)
      const computers = Math.round(item.assetCount * 0.35)
      const pdas = Math.round(item.assetCount * 0.45)
      const others = item.assetCount - computers - pdas

      return {
        regionId: item.regionId,
        regionName: item.regionName,
        totalAssets: item.assetCount,
        computers: computers,
        pdas: pdas,
        others: Math.max(0, others),
        utilizationRate: Math.round(item.normalRate || 0),
        normalCount: item.normalCount,
        faultCount: item.faultCount,
        maintenanceCount: item.maintenanceCount,
        percentage: item.percentage
      }
    })

    console.log('✅ 处理后的区域统计数据 (regionStats.value):', regionStats.value)
    console.log('📈 数据条目数量:', regionStats.value.length)
  } catch (error) {
    console.error('❌ 加载区域统计失败:', error)
    ElMessage.error('加载区域统计失败')
  }
}

// 加载部门统计
const loadDepartmentStats = async () => {
  try {
    const data = await assetStatisticsApi.getStatisticsByDepartment()
    if (data && data.length > 0) {
      departmentStats.value = data.map(item => {
        const computers = Math.round(item.assetCount * 0.35)
        const pdas = Math.round(item.assetCount * 0.45)
        const others = item.assetCount - computers - pdas
        return {
          departmentId: item.departmentId,
          departmentName: item.departmentName,
          totalAssets: item.assetCount,
          computers: computers,
          pdas: pdas,
          others: Math.max(0, others),
          activeRate: Math.round(item.normalRate || 0),
          normalCount: item.normalCount,
          faultCount: item.faultCount,
          maintenanceCount: item.maintenanceCount,
          percentage: item.percentage
        }
      })
    } else {
      departmentStats.value = []
    }
  } catch (error) {
    console.error('加载部门统计失败:', error)
    ElMessage.error('加载部门统计失败')
  }
}

// 加载趋势数据
const loadTrendData = async () => {
  try {
    let data
    switch (trendPeriod.value) {
      case 'daily': data = await assetStatisticsApi.getDailyTrend(); break;
      case 'monthly': data = await assetStatisticsApi.getMonthlyTrend(); break;
      default: data = await assetStatisticsApi.getWeeklyTrend();
    }
    trendData.value = (data || []).map(item => ({
      period: item.dateLabel || item.date,
      total: item.totalAssets || 0,
      active: item.normalAssets || 0,
      idle: (item.faultAssets || 0) + (item.maintenanceAssets || 0),
      date: item.date,
      normalRate: item.normalRate || 0,
      faultRate: item.faultRate || 0,
      newAssets: item.newAssets || 0,
      processedAssets: item.processedAssets || 0
    }))

    if (!trendData.value || trendData.value.length === 0) {
      const periods = trendPeriod.value === 'daily' ?
        ['06-01', '06-02', '06-03', '06-04', '06-05', '06-06', '06-07'] :
        trendPeriod.value === 'monthly' ?
        ['2024-01', '2024-02', '2024-03', '2024-04', '2024-05', '2024-06'] :
        ['W18', 'W19', 'W20', 'W21', 'W22', 'W23']
      trendData.value = periods.map(period => ({
        period: period, total: 242, active: 242, idle: 0, date: new Date().toISOString(), normalRate: 100, faultRate: 0, newAssets: 0, processedAssets: 0
      }))
    }
  } catch (error) {
    console.error('加载趋势数据失败:', error)
    ElMessage.error('加载趋势数据失败')
  }
}

// 初始化图表
const initCharts = () => {
  console.log('🎯 开始初始化图表...')

  nextTick(() => {
    console.log('📋 检查 DOM 元素状态:')
    console.log('  - typeChartRef.value:', typeChartRef.value)
    console.log('  - regionChartRef.value:', regionChartRef.value)
    console.log('  - trendChartRef.value:', trendChartRef.value)

    // 类型图表初始化
    if (typeChartRef.value) {
      try {
        console.log('🔧 正在初始化类型图表...')
        console.log('DOM 元素详情:', typeChartRef.value)
        typeChart = echarts.init(typeChartRef.value)
        console.log('✅ 类型图表初始化成功:', typeChart)
      } catch (error) {
        console.error('❌ 类型图表初始化失败:', error)
      }
    } else {
      console.error('❌ typeChartRef.value 为空，无法初始化类型图表')
    }

    // 区域图表初始化
    if (regionChartRef.value) {
      try {
        console.log('🔧 正在初始化区域图表...')
        console.log('DOM 元素详情:', regionChartRef.value)
        regionChart = echarts.init(regionChartRef.value)
        console.log('✅ 区域图表初始化成功:', regionChart)
      } catch (error) {
        console.error('❌ 区域图表初始化失败:', error)
      }
    } else {
      console.error('❌ regionChartRef.value 为空，无法初始化区域图表')
    }

    // 趋势图表初始化
    if (trendChartRef.value) {
      try {
        console.log('🔧 正在初始化趋势图表...')
        console.log('DOM 元素详情:', trendChartRef.value)
        trendChart = echarts.init(trendChartRef.value)
        console.log('✅ 趋势图表初始化成功:', trendChart)
      } catch (error) {
        console.error('❌ 趋势图表初始化失败:', error)
      }
    } else {
      console.error('❌ trendChartRef.value 为空，无法初始化趋势图表')
    }

    // 监听窗口大小变化
    try {
      removeResizeListener = addOptimizedEventListener(window, 'resize', handleResize)
      console.log('✅ 窗口大小变化监听器已设置')
    } catch (error) {
      console.error('❌ 设置窗口监听器失败:', error)
    }

    console.log('🎯 图表初始化完成')
    console.log('📊 最终图表实例状态:')
    console.log('  - typeChart:', typeChart ? '✅ 存在' : '❌ 不存在')
    console.log('  - regionChart:', regionChart ? '✅ 存在' : '❌ 不存在')
    console.log('  - trendChart:', trendChart ? '✅ 存在' : '❌ 不存在')
  })
}

// 更新所有图表
const updateAllCharts = () => {
  updateTypeChart()
  updateRegionChart()
  updateTrendChart()
}

// 更新类型统计图表
const updateTypeChart = () => {
  console.log('🎨 开始更新类型图表...')
  console.log('📊 用于更新图表的 typeStats 数据:', typeStats.value)

  if (!typeChart) {
    console.error('❌ typeChart 实例不存在，无法更新图表！')
    return
  }

  if (!typeStats.value || typeStats.value.length === 0) {
    console.warn('⚠️ typeStats 数据为空，将显示空状态提示')
    const emptyOption = {
      title: {
        text: '资产类型分布',
        left: 'center',
        textStyle: { fontSize: 16, color: '#333' }
      },
      series: [],
      graphic: {
        type: 'text',
        left: 'center',
        top: 'middle',
        style: {
          text: '暂无数据',
          fontSize: 16,
          fill: '#999'
        }
      }
    }
    typeChart.setOption(emptyOption, true)
    return
  }

  console.log('📈 开始渲染类型图表，数据条目数:', typeStats.value.length)

  const option = {
    title: {
      text: '资产类型分布',
      left: 'center',
      textStyle: { fontSize: 16, color: '#333' }
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)',
      backgroundColor: 'rgba(50, 50, 50, 0.8)',
      borderColor: '#333',
      textStyle: { color: '#fff' }
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      top: 'middle',
      textStyle: { fontSize: 12 }
    },
    series: [
      {
        name: '资产数量',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['60%', '50%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold'
          },
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        labelLine: { show: false },
        data: typeStats.value.map(item => ({
          value: item.count,
          name: item.typeName,
          itemStyle: { color: getTypeColor(item.typeName) }
        }))
      }
    ]
  }

  console.log('🎯 类型图表的新配置:', option)
  typeChart.setOption(option, true)
  console.log('✅ 类型图表更新完成！')
}

// 更新区域统计图表
const updateRegionChart = () => {
  console.log('🎨 开始更新区域图表...')
  console.log('📊 用于更新图表的 regionStats 数据:', regionStats.value)
  console.log('🖼️ regionChart 实例状态:', regionChart ? '✅ 存在' : '❌ 不存在')

  if (!regionChart) {
    console.error('❌ regionChart 实例不存在，无法更新图表！')
    ElMessage.error('图表初始化失败，请刷新页面重试')
    return
  }

  // --- [核心修改] 检查数据是否为空 ---
  if (!regionStats.value || regionStats.value.length === 0) {
    console.warn('⚠️ regionStats 数据为空，将显示 ECharts 的空状态提示')
    // 使用 ECharts 的 graphic 组件来显示提示文本
    const emptyOption = {
      title: {
        text: '区域资产分布',
        left: 'center',
        textStyle: { fontSize: 16, color: '#333' }
      },
      series: [], // 清空系列数据
      graphic: { // ECharts 内置的图形元素组件
        type: 'text',
        left: 'center',
        top: 'middle',
        style: {
          text: '暂无数据\n请检查是否有位置类型为"工序"的区域',
          fontSize: 16,
          fill: '#999',
          textAlign: 'center'
        }
      },
      // 清除其他配置
      tooltip: {},
      legend: {},
      grid: {},
      xAxis: {},
      yAxis: {}
    }
    regionChart.setOption(emptyOption, true) // true 表示清空画布后重新渲染
    console.log('📝 已显示空数据提示')
    return
  }
  // --- [核心修改结束] ---

  // 如果有数据，则正常渲染图表
  console.log('📈 开始渲染有数据的图表，数据条目数:', regionStats.value.length)

  const option = {
    title: {
      text: '区域资产分布',
      left: 'center',
      textStyle: { fontSize: 16, color: '#333' }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'shadow' },
      formatter: function(params) {
        let result = `<strong>${params[0].name}</strong><br/>`
        let total = 0
        params.forEach(param => {
          result += `${param.marker} ${param.seriesName}: ${param.value}<br/>`
          total += param.value
        })
        result += `<hr/>总计: ${total}`
        return result
      }
    },
    legend: {
      data: ['电脑', 'PDA', '其他'],
      top: 30,
      textStyle: { fontSize: 12 }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      top: '20%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: regionStats.value.map(item => item.regionName),
      axisLabel: {
        rotate: 45,
        interval: 0,
        fontSize: 11
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        fontSize: 11
      }
    },
    series: [
      {
        name: '电脑',
        type: 'bar',
        stack: 'total',
        data: regionStats.value.map(item => item.computers),
        itemStyle: { color: '#5470c6' },
        emphasis: { focus: 'series' }
      },
      {
        name: 'PDA',
        type: 'bar',
        stack: 'total',
        data: regionStats.value.map(item => item.pdas),
        itemStyle: { color: '#91cc75' },
        emphasis: { focus: 'series' }
      },
      {
        name: '其他',
        type: 'bar',
        stack: 'total',
        data: regionStats.value.map(item => item.others),
        itemStyle: { color: '#fac858' },
        emphasis: { focus: 'series' }
      }
    ]
  }

  console.log('🎯 区域图表的新配置:', option)
  regionChart.setOption(option, true) // 使用 true 确保之前的"暂无数据"提示被清除
  console.log('✅ 区域图表更新完成！')
}

// 更新趋势图表
const updateTrendChart = () => {
  if (!trendChart || !trendData.value.length) return

  const option = {
    title: {
      text: `资产变化趋势 (${trendPeriodText.value})`, left: 'center', textStyle: { fontSize: 16, color: '#333' }
    },
    tooltip: { trigger: 'axis' },
    legend: { data: ['总数', '在用', '闲置'], top: 30 },
    grid: { left: '3%', right: '4%', bottom: '3%', top: '15%', containLabel: true },
    // --- [FIX] START: 修复代码 ---
    dataZoom: [
        {
            type: 'inside',
            zoomOnMouseWheel: false // 禁用滚轮缩放，消除警告
        }
    ],
    // --- [FIX] END ---
    xAxis: {
      type: 'category', boundaryGap: false, data: trendData.value.map(item => item.period)
    },
    yAxis: { type: 'value' },
    series: [
      { name: '总数', type: 'line', data: trendData.value.map(item => item.total), itemStyle: { color: '#5470c6' }, areaStyle: { opacity: 0.3 } },
      { name: '在用', type: 'line', data: trendData.value.map(item => item.active), itemStyle: { color: '#91cc75' } },
      { name: '闲置', type: 'line', data: trendData.value.map(item => item.idle), itemStyle: { color: '#ee6666' } }
    ]
  }
  trendChart.setOption(option)
}

// 获取类型颜色
const getTypeColor = (typeName) => {
  const colors = {
    '工控机': '#5470c6', 'PDA': '#91cc75', '扫描设备': '#fac858', '蓝牙打印机': '#ee6666',
    '电脑设备': '#73c0de', '打印机': '#fc8452', '扫码器': '#9a60b4', '其他': '#ea7ccc'
  }
  return colors[typeName] || '#5470c6'
}

// 处理窗口大小变化
const handleResize = () => {
  typeChart?.resize()
  regionChart?.resize()
  trendChart?.resize()
}

// 表格行点击事件 - 钻取功能
const handleRegionRowClick = async (row) => {
  selectedRegion.value = row.regionId
  await applyFilters()
  ElMessage.success(`已切换到 ${row.regionName} 区域视图`)
}

const handleDepartmentRowClick = async (row) => {
  selectedDepartment.value = row.departmentId
  await applyFilters()
  ElMessage.success(`已切换到 ${row.departmentName} 部门视图`)
}

// 导出功能
const exportTypeData = () => {
  const data = typeStats.value.map(item => ({
    '资产类型': item.typeName, '数量': item.count, '正常数量': item.normalCount, '故障数量': item.faultCount,
    '维修数量': item.maintenanceCount, '正常率': `${item.normalRate}%`, '占比': `${item.percentage}%`
  }))
  downloadCSV(data, '按类型统计')
}

const exportRegionData = () => {
  const data = regionStats.value.map(item => ({
    '区域名称': item.regionName, '总数': item.totalAssets, '电脑': item.computers, 'PDA': item.pdas,
    '其他': item.others, '利用率': `${item.utilizationRate}%`, '正常数量': item.normalCount, '故障数量': item.faultCount
  }))
  downloadCSV(data, '按区域统计')
}

const exportAllData = () => {
  const allData = [
    { 类型: '总体统计', 名称: '资产总数', 数值: overallStats.totalAssets, 单位: '台' },
    { 类型: '总体统计', 名称: '在用资产', 数值: overallStats.activeAssets, 单位: '台' },
    { 类型: '总体统计', 名称: '闲置资产', 数值: overallStats.idleAssets, 单位: '台' },
    { 类型: '总体统计', 名称: '总价值', 数值: overallStats.totalValue, 单位: '元' },
    ...typeStats.value.map(item => ({
      类型: '按类型统计', 名称: item.typeName, 数值: item.count, 单位: '台', 正常率: `${item.normalRate}%`
    })),
    ...regionStats.value.map(item => ({
      类型: '按区域统计', 名称: item.regionName, 数值: item.totalAssets, 单位: '台', 利用率: `${item.utilizationRate}%`
    })),
    ...departmentStats.value.map(item => ({
      类型: '按部门统计', 名称: item.departmentName, 数值: item.totalAssets, 单位: '台', 在用率: `${item.activeRate}%`
    }))
  ]
  downloadCSV(allData, '资产统计综合报表')
}

// CSV导出辅助函数
const downloadCSV = (data, filename) => {
  try {
    if (!data || data.length === 0) {
      ElMessage.warning('没有数据可导出'); return
    }
    const headers = Object.keys(data[0])
    const csvContent = [
      headers.join(','),
      ...data.map(row => headers.map(header => `"${row[header] || ''}"`).join(','))
    ].join('\n')
    const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `${filename}_${new Date().toISOString().split('T')[0]}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  }
}

// 生命周期
onMounted(async () => {
  console.log('🚀 组件开始挂载...')
  loading.value = true
  try {
    // 先加载数据
    console.log('📊 开始加载数据...')
    await Promise.all([
      loadTypeStats(),
      loadRegionStats(),
      loadDepartmentStats(),
      loadTrendData()
    ])
    await loadFilterOptions()
    console.log('✅ 数据加载完成')

    // 等待 DOM 完全渲染后再初始化图表
    console.log('⏳ 等待 DOM 渲染...')
    await nextTick()

    // 额外延迟确保 DOM 完全准备好
    setTimeout(() => {
      console.log('🎯 开始初始化图表和更新...')
      initCharts()
      // 再次延迟确保图表实例创建完成
      setTimeout(() => {
        updateAllCharts()
      }, 100)
    }, 100)

  } catch (error) {
    console.error('❌ 初始化失败:', error)
    ElMessage.error('页面初始化失败')
  } finally {
    loading.value = false
  }
})

onUnmounted(() => {
  if (removeResizeListener) removeResizeListener()
  typeChart?.dispose()
  regionChart?.dispose()
  trendChart?.dispose()
})
</script>

<style scoped>
.asset-statistics-container {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

/* 页面头部 */
.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 20px;
  color: white;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title h1 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 600;
}

.header-title p {
  margin: 0;
  opacity: 0.9;
  font-size: 16px;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

/* 统计概览卡片 */
.overview-cards {
  margin-bottom: 20px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(to bottom, #667eea, #764ba2);
}

.stat-card.total::before {
  background: linear-gradient(to bottom, #667eea, #764ba2);
}

.stat-card.active::before {
  background: linear-gradient(to bottom, #4facfe, #00f2fe);
}

.stat-card.idle::before {
  background: linear-gradient(to bottom, #fa709a, #fee140);
}

.stat-card.value::before {
  background: linear-gradient(to bottom, #a8edea, #fed6e3);
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  font-size: 24px;
  color: white;
}

.stat-card.total .stat-icon {
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.stat-card.active .stat-icon {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.stat-card.idle .stat-icon {
  background: linear-gradient(135deg, #fa709a, #fee140);
}

.stat-card.value .stat-icon {
  background: linear-gradient(135deg, #a8edea, #fed6e3);
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 32px;
  font-weight: 700;
  color: #2c3e50;
  line-height: 1;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 16px;
  color: #7f8c8d;
  margin-bottom: 8px;
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  font-weight: 600;
}

.stat-trend.positive {
  color: #27ae60;
}

.stat-trend.negative {
  color: #e74c3c;
}

/* 图表卡片 */
.chart-card {
  margin-bottom: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.chart-card :deep(.el-card__header) {
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: #2c3e50;
}

.chart-container {
  height: 350px;
  width: 100%;
}

.trend-chart {
  height: 400px;
}

/* 趋势图例 */
.trend-legend {
  display: flex;
  gap: 20px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: #666;
}

.legend-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.legend-dot.total {
  background: #5470c6;
}

.legend-dot.active {
  background: #91cc75;
}

.legend-dot.idle {
  background: #ee6666;
}

/* 表格卡片 */
.table-card {
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.table-card :deep(.el-card__header) {
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.table-card :deep(.el-table) {
  border-radius: 8px;
}

.table-card :deep(.el-table__row:hover) {
  background-color: #f5f7fa;
  cursor: pointer;
}

/* 进度条样式 */
.utilization-text,
.active-rate-text {
  margin-left: 8px;
  font-size: 12px;
  color: #666;
}

/* 空数据状态 */
.empty-data {
  text-align: center;
  padding: 40px 20px;
  color: #909399;
}

.empty-data p {
  margin: 12px 0 0 0;
  font-size: 14px;
}

.empty-tip {
  font-size: 12px !important;
  color: #c0c4cc !important;
}

/* 筛选面板样式 */
.filter-panel {
  margin-bottom: 20px;
  border: 1px solid #e4e7ed;
}

.filter-row {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-item label {
  font-size: 14px;
  color: #606266;
  white-space: nowrap;
  font-weight: 500;
}

.filter-item .el-select {
  width: 160px;
}

.filter-item .el-button {
  margin-left: 8px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .overview-cards :deep(.el-col) {
    margin-bottom: 16px;
  }

  .main-charts :deep(.el-col) {
    margin-bottom: 20px;
  }

  .detail-tables :deep(.el-col) {
    margin-bottom: 20px;
  }
}

@media (max-width: 768px) {
  .asset-statistics-container {
    padding: 12px;
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .stat-card {
    padding: 16px;
  }

  .stat-value {
    font-size: 24px;
  }

  .chart-container {
    height: 280px;
  }

  .trend-chart {
    height: 320px;
  }
}
</style>