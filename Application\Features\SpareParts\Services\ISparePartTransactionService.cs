// File: Application/Features/SpareParts/Services/ISparePartTransactionService.cs
// Description: 备品备件出入库记录服务接口

using System.Threading.Tasks;
using ItAssetsSystem.Application.Common.Dtos;
using ItAssetsSystem.Application.Features.SpareParts.Dtos;

namespace ItAssetsSystem.Application.Features.SpareParts.Services
{
    /// <summary>
    /// 备品备件出入库记录服务接口
    /// </summary>
    public interface ISparePartTransactionService
    {
        /// <summary>
        /// 获取出入库记录列表（分页）
        /// </summary>
        /// <param name="query">查询参数</param>
        /// <returns>分页结果</returns>
        Task<PaginatedResult<SparePartTransactionDto>> GetTransactionsAsync(SparePartTransactionQuery query);
        
        /// <summary>
        /// 获取特定备件的出入库记录（分页）
        /// </summary>
        /// <param name="partId">备件ID</param>
        /// <param name="query">分页参数</param>
        /// <returns>分页结果</returns>
        Task<PaginatedResult<SparePartTransactionDto>> GetTransactionsByPartIdAsync(long partId, PaginationQuery query);
        
        /// <summary>
        /// 获取出入库记录详情
        /// </summary>
        /// <param name="id">记录ID</param>
        /// <returns>记录DTO</returns>
        Task<SparePartTransactionDto> GetTransactionByIdAsync(long id);
    }
} 