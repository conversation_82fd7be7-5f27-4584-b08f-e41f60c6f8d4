<template>
  <div class="location-container">
    <router-view v-slot="{ Component }">
      <keep-alive>
        <component :is="Component" />
      </keep-alive>
    </router-view>
  </div>
</template>

<script setup>
import { onMounted } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()

onMounted(() => {
  console.log('位置管理页面加载:', {
    currentRoute: route.path,
    name: route.name
  })
})
</script>

<style lang="scss" scoped>
.location-container {
  height: 100%;
  padding: 16px;
  background-color: #f5f7fa;
}
</style> 