<template>
  <div class="spare-part-type-view">
    <el-card class="type-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>备件类型管理</span>
          <el-button type="primary" @click="handleAddType(null)">
            新增根类型
          </el-button>
        </div>
      </template>
      
      <div class="card-content">
        <div class="tree-container">
          <el-tree
            v-loading="sparePartsStore.typesLoading"
            :data="sparePartsStore.typesTree"
            node-key="id"
            default-expand-all
            :expand-on-click-node="false"
            highlight-current
            :check-strictly="true"
            @node-click="handleNodeClick"
          >
            <template #default="{ node, data }">
              <span class="custom-tree-node">
                <span class="node-label">{{ data.name }} <span class="node-code">[{{ data.code }}]</span></span>
                <span class="node-actions">
                  <el-tooltip content="新增子类型" placement="top">
                    <el-button 
                      class="action-btn" 
                      type="primary" 
                      :icon="Plus" 
                      circle 
                      size="small"
                      @click.stop="handleAddType(data.id)"
                    ></el-button>
                  </el-tooltip>
                  <el-tooltip content="编辑" placement="top">
                    <el-button 
                      class="action-btn" 
                      type="info" 
                      :icon="Edit" 
                      circle 
                      size="small"
                      @click.stop="handleEditType(data)"
                    ></el-button>
                  </el-tooltip>
                  <el-tooltip content="删除" placement="top">
                    <el-button 
                      class="action-btn" 
                      type="danger" 
                      :icon="Delete" 
                      circle 
                      size="small"
                      @click.stop="handleDeleteType(data)"
                    ></el-button>
                  </el-tooltip>
                </span>
              </span>
            </template>
          </el-tree>
          
          <el-empty
            v-if="!sparePartsStore.typesLoading && sparePartsStore.typesTree.length === 0"
            description="暂无类型数据"
          >
            <el-button type="primary" @click="handleAddType(null)">新增类型</el-button>
          </el-empty>
        </div>
        
        <div class="type-details" v-if="currentType">
          <h3>类型详情</h3>
          <el-descriptions :column="1" border>
            <el-descriptions-item label="类型名称">{{ currentType.name }}</el-descriptions-item>
            <el-descriptions-item label="类型编码">{{ currentType.code }}</el-descriptions-item>
            <el-descriptions-item label="上级类型">
              {{ getParentTypeName(currentType.parent_id) }}
            </el-descriptions-item>
            <el-descriptions-item label="描述">{{ currentType.description || '无' }}</el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </el-card>
    
    <!-- 添加/编辑类型对话框 -->
    <el-dialog
      v-model="typeDialogVisible"
      :title="isEdit ? '编辑类型' : '新增类型'"
      width="500px"
      :close-on-click-modal="false"
      @closed="resetTypeForm"
    >
      <el-form
        ref="typeFormRef"
        :model="typeForm"
        :rules="typeRules"
        label-width="100px"
        label-position="right"
        status-icon
      >
        <el-form-item label="类型名称" prop="name">
          <el-input v-model="typeForm.name" placeholder="请输入类型名称"></el-input>
        </el-form-item>
        
        <el-form-item label="类型编码" prop="code">
          <el-input v-model="typeForm.code" placeholder="请输入类型编码"></el-input>
        </el-form-item>
        
        <el-form-item label="上级类型" prop="parent_id">
          <el-select
            v-model="typeForm.parent_id"
            placeholder="请选择上级类型"
            clearable
            style="width: 100%"
          >
            <el-option 
              v-for="option in parentTypeOptions" 
              :key="option.value" 
              :label="option.label" 
              :value="option.value">
            </el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="typeForm.description"
            placeholder="请输入类型描述"
            type="textarea"
            :rows="3"
          ></el-input>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="typeDialogVisible = false">取消</el-button>
          <el-button 
            type="primary" 
            @click="submitTypeForm"
            :loading="sparePartsStore.formLoading"
          >
            确认
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, nextTick } from 'vue';
import { useSparePartsStore } from '@/stores/modules/spareparts';
import { ElMessageBox, ElMessage } from 'element-plus';
import { Plus, Edit, Delete } from '@element-plus/icons-vue';

// 状态管理
const sparePartsStore = useSparePartsStore();

// 状态变量
const typeDialogVisible = ref(false);
const isEdit = ref(false);
const currentType = ref(null);
const typeFormRef = ref(null);

// 类型表单
const typeForm = ref({
  name: '',
  code: '',
  parent_id: null,
  description: ''
});

// 表单验证规则
const typeRules = {
  name: [
    { required: true, message: '请输入类型名称', trigger: 'blur' },
    { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入类型编码', trigger: 'blur' },
    { min: 1, max: 20, message: '长度在 1 到 20 个字符', trigger: 'blur' }
  ]
};

// 计算属性 - 上级类型选项
const parentTypeOptions = computed(() => {
  // 过滤掉当前类型及其所有子类型，防止循环引用
  let excludeIds = [];
  
  if (isEdit.value && typeForm.value.id) {
    excludeIds.push(typeForm.value.id);
    // 递归查找所有子类型ID
    const findChildrenIds = (parentId) => {
      sparePartsStore.types
        .filter(t => t.parent_id === parentId)
        .forEach(child => {
          excludeIds.push(child.id);
          findChildrenIds(child.id);
        });
    };
    
    findChildrenIds(typeForm.value.id);
  }
  
  return [
    { label: '无 (作为根类型)', value: null },
    ...sparePartsStore.types
      .filter(type => !excludeIds.includes(type.id))
      .map(type => ({
        label: type.name,
        value: type.id
      }))
  ];
});

// 方法 - 获取父类型名称
const getParentTypeName = (parentId) => {
  if (parentId === null) return '无 (根类型)';
  const parent = sparePartsStore.types.find(t => t.id === parentId);
  return parent ? parent.name : '未知';
};

// 方法 - 处理节点点击
const handleNodeClick = (data) => {
  currentType.value = data;
};

// 方法 - 添加类型
const handleAddType = (parentId) => {
  isEdit.value = false;
  typeForm.value = {
    name: '',
    code: '',
    parent_id: parentId,
    description: ''
  };
  typeDialogVisible.value = true;
  
  // 等待DOM更新后设置焦点
  nextTick(() => {
    typeFormRef.value && typeFormRef.value.resetFields();
  });
};

// 方法 - 编辑类型
const handleEditType = (data) => {
  isEdit.value = true;
  typeForm.value = {
    id: data.id,
    name: data.name,
    code: data.code,
    parent_id: data.parent_id,
    description: data.description || ''
  };
  typeDialogVisible.value = true;
  
  // 等待DOM更新后设置焦点
  nextTick(() => {
    typeFormRef.value && typeFormRef.value.clearValidate();
  });
};

// 方法 - 删除类型
const handleDeleteType = (data) => {
  ElMessageBox.confirm(
    `确定要删除类型"${data.name}"吗？如果该类型下有子类型或关联的备件，将无法删除。`,
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    const result = await sparePartsStore.deleteType(data.id);
    if (result) {
      // 如果是当前选中的类型，清除选中状态
      if (currentType.value && currentType.value.id === data.id) {
        currentType.value = null;
      }
    }
  }).catch(() => {
    // 用户取消删除
  });
};

// 方法 - 提交类型表单
const submitTypeForm = async () => {
  // 表单验证
  if (!typeFormRef.value) return;
  
  try {
    await typeFormRef.value.validate();
    
    if (isEdit.value) {
      // 编辑类型
      const { id, ...updateData } = typeForm.value;
      await sparePartsStore.updateType(id, updateData);
    } else {
      // 新增类型
      await sparePartsStore.createType(typeForm.value);
    }
    
    // 关闭对话框
    typeDialogVisible.value = false;
    
  } catch (error) {
    console.error('表单验证失败:', error);
  }
};

// 方法 - 重置类型表单
const resetTypeForm = () => {
  typeForm.value = {
    name: '',
    code: '',
    parent_id: null,
    description: ''
  };
  nextTick(() => {
    typeFormRef.value && typeFormRef.value.resetFields();
  });
};

// 组件挂载时的处理
onMounted(async () => {
  // 获取类型列表和树形结构
  await sparePartsStore.fetchTypes();
  await sparePartsStore.fetchTypesTree();
});
</script>

<style scoped>
.spare-part-type-view {
  padding: 20px;
}

.type-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-content {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.tree-container {
  flex: 1;
  min-width: 300px;
  max-width: 100%;
}

.type-details {
  flex: 1;
  min-width: 300px;
  max-width: 100%;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-right: 8px;
}

.node-label {
  font-size: 14px;
}

.node-code {
  color: #909399;
  font-size: 12px;
  margin-left: 4px;
}

.node-actions {
  display: none;
}

.custom-tree-node:hover .node-actions {
  display: flex;
  gap: 5px;
}

.action-btn {
  padding: 4px;
}

@media (max-width: 768px) {
  .card-content {
    flex-direction: column;
  }
  
  .node-actions {
    display: flex; /* 在移动端始终显示操作按钮 */
  }
}
</style> 