<template>
  <div class="zone-manager">
    <el-card class="manager-card">
      <template #header>
        <div class="card-header">
          <h3>区域布局管理</h3>
          <el-button type="primary" @click="saveLayout">
            <el-icon><DocumentAdd /></el-icon>
            保存布局
          </el-button>
        </div>
      </template>

      <!-- 区域配置面板 -->
      <div class="zone-config-panel">
        <el-tabs v-model="activeZone" @tab-change="handleZoneChange">
          <el-tab-pane 
            v-for="zone in zones" 
            :key="zone.id"
            :label="`${zone.name} (${zone.workstationIds.length}个工位)`"
            :name="zone.id.toString()"
          >
            <div class="zone-detail">
              <!-- 区域基本信息 -->
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form label-width="80px">
                    <el-form-item label="区域名称">
                      <el-input v-model="zone.name" />
                    </el-form-item>
                    <el-form-item label="区域颜色">
                      <el-color-picker v-model="zone.color" />
                    </el-form-item>
                    <el-form-item label="布局类型">
                      <el-select v-model="zone.type">
                        <el-option label="网格布局" value="grid" />
                        <el-option label="复合布局" value="complex" />
                        <el-option label="自由布局" value="free" />
                      </el-select>
                    </el-form-item>
                  </el-form>
                </el-col>
                <el-col :span="12">
                  <div class="zone-preview" :style="{ backgroundColor: zone.color + '20', border: `2px dashed ${zone.color}` }">
                    <div class="preview-title">{{ zone.name }}</div>
                    <div class="preview-stats">
                      <div>工位数量: {{ zone.workstationIds.length }}</div>
                      <div>布局类型: {{ zone.type === 'grid' ? '网格' : zone.type === 'complex' ? '复合' : '自由' }}</div>
                    </div>
                  </div>
                </el-col>
              </el-row>

              <!-- 网格布局配置 -->
              <div v-if="zone.type === 'grid'" class="layout-config">
                <h4>网格布局配置</h4>
                <el-row :gutter="20">
                  <el-col :span="6">
                    <el-form-item label="行数">
                      <el-input-number v-model="zone.layout.rows" :min="1" :max="20" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item label="列数">
                      <el-input-number v-model="zone.layout.cols" :min="1" :max="20" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-button @click="regenerateGridWorkstations(zone)">重新生成工位</el-button>
                  </el-col>
                </el-row>
              </div>

              <!-- 工位列表 -->
              <div class="workstation-list">
                <h4>工位列表</h4>
                <div class="workstation-grid">
                  <div 
                    v-for="workstationId in zone.workstationIds" 
                    :key="workstationId"
                    class="workstation-item"
                    :class="{ 'selected': selectedWorkstation === workstationId }"
                    @click="selectWorkstation(workstationId)"
                  >
                    <div class="workstation-id">{{ workstationId }}</div>
                    <div class="workstation-actions">
                      <el-button size="small" type="danger" @click.stop="removeWorkstation(zone, workstationId)">
                        <el-icon><Delete /></el-icon>
                      </el-button>
                    </div>
                  </div>
                </div>
                
                <!-- 添加工位 -->
                <div class="add-workstation">
                  <el-input-number 
                    v-model="newWorkstationId" 
                    :min="1" 
                    :max="999" 
                    placeholder="工位ID"
                  />
                  <el-button @click="addWorkstation(zone)" type="primary">添加工位</el-button>
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-card>

    <!-- 工位详情编辑 -->
    <el-drawer
      v-model="workstationDrawerVisible"
      title="工位详情编辑"
      direction="rtl"
      size="400px"
    >
      <div v-if="selectedWorkstationDetail" class="workstation-editor">
        <el-form :model="selectedWorkstationDetail" label-width="80px">
          <el-form-item label="工位ID">
            <el-input v-model="selectedWorkstationDetail.id" disabled />
          </el-form-item>
          <el-form-item label="工位名称">
            <el-input v-model="selectedWorkstationDetail.name" />
          </el-form-item>
          <el-form-item label="X坐标">
            <el-input-number v-model="selectedWorkstationDetail.x" />
          </el-form-item>
          <el-form-item label="Y坐标">
            <el-input-number v-model="selectedWorkstationDetail.y" />
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="selectedWorkstationDetail.status">
              <el-option label="运行正常" value="operational" />
              <el-option label="警告状态" value="warning" />
              <el-option label="故障状态" value="error" />
              <el-option label="空闲状态" value="idle" />
            </el-select>
          </el-form-item>
        </el-form>
        
        <div class="drawer-actions">
          <el-button @click="workstationDrawerVisible = false">取消</el-button>
          <el-button type="primary" @click="saveWorkstationDetail">保存</el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { DocumentAdd, Delete } from '@element-plus/icons-vue'
import { zoneApi, workstationLayoutConfig } from '@/api/zone'

// 响应式数据
const activeZone = ref('1')
const selectedWorkstation = ref(null)
const newWorkstationId = ref(null)
const workstationDrawerVisible = ref(false)
const selectedWorkstationDetail = ref(null)

// 区域配置数据
const zones = ref([])

// 初始化区域数据
const initZones = () => {
  zones.value = Object.values(workstationLayoutConfig.zones).map(zone => ({
    ...zone,
    layout: zone.layout || { rows: 1, cols: 1 }
  }))
}

// 处理区域切换
const handleZoneChange = (zoneId) => {
  activeZone.value = zoneId
  selectedWorkstation.value = null
}

// 选择工位
const selectWorkstation = (workstationId) => {
  selectedWorkstation.value = workstationId
  // 加载工位详情
  loadWorkstationDetail(workstationId)
}

// 加载工位详情
const loadWorkstationDetail = async (workstationId) => {
  try {
    // 模拟加载工位详情
    selectedWorkstationDetail.value = {
      id: workstationId,
      name: `工位${workstationId.toString().padStart(3, '0')}`,
      x: 100,
      y: 100,
      status: 'operational'
    }
    workstationDrawerVisible.value = true
  } catch (error) {
    ElMessage.error('加载工位详情失败')
  }
}

// 保存工位详情
const saveWorkstationDetail = async () => {
  try {
    // 调用API保存工位详情
    await zoneApi.updateWorkstationPosition(
      selectedWorkstationDetail.value.id,
      {
        x: selectedWorkstationDetail.value.x,
        y: selectedWorkstationDetail.value.y
      }
    )
    
    await zoneApi.updateWorkstationStatus(
      selectedWorkstationDetail.value.id,
      selectedWorkstationDetail.value.status
    )
    
    ElMessage.success('工位详情保存成功')
    workstationDrawerVisible.value = false
  } catch (error) {
    ElMessage.error('保存工位详情失败')
  }
}

// 添加工位
const addWorkstation = (zone) => {
  if (!newWorkstationId.value) {
    ElMessage.warning('请输入工位ID')
    return
  }
  
  if (zone.workstationIds.includes(newWorkstationId.value)) {
    ElMessage.warning('工位ID已存在')
    return
  }
  
  zone.workstationIds.push(newWorkstationId.value)
  zone.workstationIds.sort((a, b) => a - b)
  newWorkstationId.value = null
  ElMessage.success('工位添加成功')
}

// 移除工位
const removeWorkstation = (zone, workstationId) => {
  const index = zone.workstationIds.indexOf(workstationId)
  if (index > -1) {
    zone.workstationIds.splice(index, 1)
    ElMessage.success('工位移除成功')
  }
}

// 重新生成网格工位
const regenerateGridWorkstations = (zone) => {
  const totalWorkstations = zone.layout.rows * zone.layout.cols
  const startId = zone.workstationIds.length > 0 ? Math.min(...zone.workstationIds) : 1
  
  zone.workstationIds = Array.from({length: totalWorkstations}, (_, i) => startId + i)
  ElMessage.success('网格工位重新生成成功')
}

// 保存布局
const saveLayout = async () => {
  try {
    // 批量保存所有区域配置
    for (const zone of zones.value) {
      await zoneApi.updateZoneConfig(zone.id, {
        name: zone.name,
        color: zone.color,
        type: zone.type,
        layout: zone.layout,
        workstationIds: zone.workstationIds
      })
    }
    
    ElMessage.success('布局保存成功')
  } catch (error) {
    ElMessage.error('布局保存失败')
  }
}

// 生命周期
onMounted(() => {
  initZones()
})
</script>

<style scoped>
.zone-manager {
  padding: 20px;
}

.manager-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.zone-detail {
  padding: 20px 0;
}

.zone-preview {
  padding: 20px;
  border-radius: 8px;
  text-align: center;
  min-height: 120px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.preview-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 10px;
}

.preview-stats div {
  margin: 5px 0;
}

.layout-config {
  margin: 20px 0;
  padding: 15px;
  background: #f5f5f5;
  border-radius: 8px;
}

.workstation-list {
  margin-top: 20px;
}

.workstation-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
  gap: 10px;
  margin: 15px 0;
}

.workstation-item {
  padding: 10px;
  border: 2px solid #ddd;
  border-radius: 6px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
}

.workstation-item:hover {
  border-color: #409eff;
  background: #f0f9ff;
}

.workstation-item.selected {
  border-color: #409eff;
  background: #e1f5fe;
}

.workstation-id {
  font-weight: bold;
  margin-bottom: 5px;
}

.workstation-actions {
  display: flex;
  justify-content: center;
}

.add-workstation {
  display: flex;
  gap: 10px;
  align-items: center;
  margin-top: 15px;
}

.workstation-editor {
  padding: 20px;
}

.drawer-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}
</style>
