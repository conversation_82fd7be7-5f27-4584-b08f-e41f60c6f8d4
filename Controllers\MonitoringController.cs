using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System.Threading.Tasks;
using ItAssetsSystem.Core.Monitoring;
using MySqlConnector;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;

namespace ItAssetsSystem.Controllers
{
    /// <summary>
    /// 监控控制器 - 用于基础解耦过程中的性能监控
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class MonitoringController : ControllerBase
    {
        private readonly IPerformanceMonitor _performanceMonitor;
        private readonly ILogger<MonitoringController> _logger;
        private readonly string _connectionString;

        public MonitoringController(
            IPerformanceMonitor performanceMonitor,
            ILogger<MonitoringController> logger,
            IConfiguration configuration)
        {
            _performanceMonitor = performanceMonitor;
            _logger = logger;
            _connectionString = configuration.GetConnectionString("DefaultConnection");
        }

        /// <summary>
        /// 获取性能报告
        /// </summary>
        /// <param name="operationName">操作名称</param>
        /// <returns>性能报告</returns>
        [HttpGet("performance/{operationName}")]
        public IActionResult GetPerformanceReport(string operationName)
        {
            try
            {
                var report = _performanceMonitor.GetPerformanceReport(operationName);
                return Ok(new { 
                    success = true, 
                    data = report,
                    message = "性能报告获取成功"
                });
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "获取性能报告失败: {OperationName}", operationName);
                return StatusCode(500, new { 
                    success = false, 
                    message = "获取性能报告失败"
                });
            }
        }

        /// <summary>
        /// 获取所有支持的操作列表
        /// </summary>
        /// <returns>操作列表</returns>
        [HttpGet("operations")]
        public IActionResult GetSupportedOperations()
        {
            var operations = new[]
            {
                new { name = "UserLogin", description = "用户登录", baseline = 200.0 },
                new { name = "UserAuthenticate", description = "用户认证", baseline = 150.0 },
                new { name = "UserGetById", description = "根据ID获取用户", baseline = 50.0 },
                new { name = "AssetGetById", description = "根据ID获取资产", baseline = 50.0 },
                new { name = "AssetGetPaged", description = "资产分页查询", baseline = 300.0 },
                new { name = "AssetCreate", description = "资产创建", baseline = 200.0 },
                new { name = "AssetUpdate", description = "资产更新", baseline = 150.0 },
                new { name = "AssetDelete", description = "资产删除", baseline = 100.0 },
                new { name = "TaskGetById", description = "根据ID获取任务", baseline = 50.0 },
                new { name = "TaskGetPaged", description = "任务分页查询", baseline = 400.0 },
                new { name = "TaskCreate", description = "任务创建", baseline = 300.0 },
                new { name = "TaskUpdate", description = "任务更新", baseline = 200.0 },
                new { name = "TaskAssign", description = "任务分配", baseline = 100.0 },
                new { name = "TaskComplete", description = "任务完成", baseline = 150.0 },
                new { name = "DatabaseQuery", description = "数据库查询", baseline = 100.0 }
            };

            return Ok(new { 
                success = true, 
                data = operations,
                message = "支持的操作列表"
            });
        }

        /// <summary>
        /// 设置性能基准线
        /// </summary>
        /// <param name="request">设置基准线请求</param>
        /// <returns>设置结果</returns>
        [HttpPost("baseline")]
        public IActionResult SetBaseline([FromBody] SetBaselineRequest request)
        {
            try
            {
                _performanceMonitor.SetBaseline(request.OperationName, request.BaselineMs);
                return Ok(new { 
                    success = true, 
                    message = $"已设置 {request.OperationName} 的基准线为 {request.BaselineMs}ms"
                });
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "设置性能基准线失败: {OperationName}", request.OperationName);
                return StatusCode(500, new { 
                    success = false, 
                    message = "设置性能基准线失败"
                });
            }
        }

        /// <summary>
        /// 获取系统健康状态
        /// </summary>
        /// <returns>健康状态</returns>
        [HttpGet("health")]
        [AllowAnonymous] // 健康检查不需要认证
        public IActionResult GetHealthStatus()
        {
            try
            {
                // 检查各个版本的API健康状态
                var healthStatus = new
                {
                    status = "healthy",
                    timestamp = System.DateTime.UtcNow,
                    services = new
                    {
                        userServiceV1 = new { status = "active", endpoint = "/api/user" },
                        userServiceV1_1 = new { status = "active", endpoint = "/api/v1.1/user" },
                        assetServiceV1 = new { status = "active", endpoint = "/api/asset" },
                        assetServiceV1_1 = new { status = "active", endpoint = "/api/v1.1/asset" },
                        taskServiceV1 = new { status = "active", endpoint = "/api/task" },
                        taskServiceV1_1 = new { status = "active", endpoint = "/api/v1.1/task" },
                        performanceMonitor = new { status = "active", endpoint = "/api/monitoring" },
                        database = new { status = "connected" }
                    },
                    decouplingProgress = new
                    {
                        phase = "基础解耦",
                        progress = "用户服务接口化完成",
                        nextStep = "资产服务接口化"
                    }
                };

                return Ok(new { 
                    success = true, 
                    data = healthStatus,
                    message = "系统健康状态正常"
                });
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "获取健康状态失败");
                return StatusCode(500, new { 
                    success = false, 
                    message = "获取健康状态失败"
                });
            }
        }

        /// <summary>
        /// 获取解耦进度报告
        /// </summary>
        /// <returns>解耦进度</returns>
        [HttpGet("decoupling-progress")]
        public IActionResult GetDecouplingProgress()
        {
            try
            {
                var progress = new
                {
                    currentPhase = "基础解耦",
                    completedSteps = new[]
                    {
                        "✅ 创建用户服务接口",
                        "✅ 实现用户服务适配器",
                        "✅ 注册新服务到DI容器",
                        "✅ 创建V1.1版本控制器",
                        "✅ 集成性能监控",
                        "✅ 创建资产服务接口",
                        "✅ 实现资产服务适配器",
                        "✅ 创建V1.1资产控制器",
                        "✅ 创建任务服务接口",
                        "✅ 实现任务服务适配器",
                        "✅ 创建V1.1任务控制器",
                        "✅ 完成任务服务接口化",
                        "✅ 完成周期性任务接口化",
                        "✅ 创建API版本管理系统"
                    },
                    currentStep = "核心服务接口化和周期性任务管理完成，系统已支持完整的任务生命周期管理",
                    nextSteps = new[]
                    {
                        "🔄 前端API版本切换",
                        "⏳ A/B测试验证",
                        "⏳ 性能优化",
                        "⏳ 微服务化准备"
                    },
                    metrics = new
                    {
                        interfacesCreated = 3,
                        totalInterfaces = 3,
                        progressPercentage = 100.0,
                        estimatedCompletion = "已完成"
                    }
                };

                return Ok(new { 
                    success = true, 
                    data = progress,
                    message = "解耦进度报告"
                });
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "获取解耦进度失败");
                return StatusCode(500, new { 
                    success = false, 
                    message = "获取解耦进度失败"
                });
            }
        }

        /// <summary>
        /// 测试新旧API性能对比
        /// </summary>
        /// <returns>对比结果</returns>
        [HttpPost("compare-apis")]
        public async Task<IActionResult> CompareApis()
        {
            try
            {
                _logger.LogInformation("开始API性能对比测试");

                var comparisonResults = new
                {
                    testTime = System.DateTime.UtcNow,
                    userLoginComparison = _performanceMonitor.GetPerformanceReport("UserAuthenticate"),
                    userGetByIdComparison = _performanceMonitor.GetPerformanceReport("UserGetById"),
                    summary = new
                    {
                        message = "新接口性能对比测试完成",
                        recommendation = "建议继续使用新接口，性能表现良好"
                    }
                };

                return Ok(new { 
                    success = true, 
                    data = comparisonResults,
                    message = "API性能对比完成"
                });
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "API性能对比失败");
                return StatusCode(500, new {
                    success = false,
                    message = "API性能对比失败"
                });
            }
        }

        /// <summary>
        /// 修复游戏化日志表结构
        /// </summary>
        /// <returns>修复结果</returns>
        [HttpPost("fix-gamification-table")]
        [AllowAnonymous] // 修复操作不需要认证
        public async Task<IActionResult> FixGamificationTable()
        {
            try
            {
                using var connection = new MySqlConnection(_connectionString);
                await connection.OpenAsync();

                // 检查并添加缺失的列
                var checkAndAddColumns = new[]
                {
                    new { Name = "ActionType", Type = "varchar(100)", Default = "NULL", Comment = "行为类型", After = "EventType" },
                    new { Name = "ReferenceId", Type = "bigint", Default = "NULL", Comment = "关联对象ID", After = "ActionType" },
                    new { Name = "Description", Type = "varchar(500)", Default = "''", Comment = "描述", After = "ReferenceId" },
                    new { Name = "PointsGained", Type = "int", Default = "0", Comment = "获得积分", After = "PointsChange" },
                    new { Name = "CoinsGained", Type = "int", Default = "0", Comment = "获得金币数", After = "PointsGained" },
                    new { Name = "DiamondsGained", Type = "int", Default = "0", Comment = "获得钻石数", After = "CoinsGained" }
                };

                foreach (var column in checkAndAddColumns)
                {
                    var checkSql = $@"
                        SELECT COUNT(*)
                        FROM INFORMATION_SCHEMA.COLUMNS
                        WHERE TABLE_SCHEMA = DATABASE()
                        AND TABLE_NAME = 'gamification_log'
                        AND COLUMN_NAME = '{column.Name}'";

                    using var checkCommand = new MySqlCommand(checkSql, connection);
                    var exists = Convert.ToInt32(await checkCommand.ExecuteScalarAsync()) > 0;

                    if (!exists)
                    {
                        var addSql = $@"
                            ALTER TABLE `gamification_log`
                            ADD COLUMN `{column.Name}` {column.Type} DEFAULT {column.Default} COMMENT '{column.Comment}' AFTER `{column.After}`";

                        using var addCommand = new MySqlCommand(addSql, connection);
                        await addCommand.ExecuteNonQueryAsync();
                        _logger.LogInformation($"已添加列: {column.Name}");
                    }
                }

                _logger.LogInformation("游戏化日志表结构修复完成");
                return Ok(new { success = true, message = "游戏化日志表结构修复完成" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "修复游戏化日志表结构失败");
                return StatusCode(500, new { success = false, message = "修复失败: " + ex.Message });
            }
        }

        /// <summary>
        /// 简单测试方法
        /// </summary>
        /// <returns>测试结果</returns>
        [HttpGet("test")]
        [AllowAnonymous]
        public IActionResult Test()
        {
            return Ok(new { success = true, message = "MonitoringController 工作正常", timestamp = DateTime.Now });
        }

        /// <summary>
        /// 获取所有数据库触发器
        /// </summary>
        /// <returns>触发器列表</returns>
        [HttpGet("triggers")]
        [AllowAnonymous]
        public async Task<IActionResult> ListTriggers()
        {
            try
            {
                var sql = @"
                    SELECT
                        TRIGGER_NAME as TriggerName,
                        EVENT_MANIPULATION as EventType,
                        EVENT_OBJECT_TABLE as TableName,
                        ACTION_TIMING as Timing
                    FROM INFORMATION_SCHEMA.TRIGGERS
                    WHERE TRIGGER_SCHEMA = DATABASE()
                    ORDER BY EVENT_OBJECT_TABLE, TRIGGER_NAME";

                using var connection = new MySqlConnection(_connectionString);
                await connection.OpenAsync();

                using var command = new MySqlCommand(sql, connection);
                using var reader = await command.ExecuteReaderAsync();

                var triggers = new List<object>();
                while (await reader.ReadAsync())
                {
                    triggers.Add(new
                    {
                        TriggerName = reader["TriggerName"].ToString(),
                        EventType = reader["EventType"].ToString(),
                        TableName = reader["TableName"].ToString(),
                        Timing = reader["Timing"].ToString()
                    });
                }

                return Ok(new { success = true, data = triggers, message = $"找到 {triggers.Count} 个触发器" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取触发器失败");
                return StatusCode(500, new { success = false, message = $"获取触发器失败: {ex.Message}" });
            }
        }

        /// <summary>
        /// 修复任务更新触发器冲突问题
        /// </summary>
        /// <returns>修复结果</returns>
        [HttpPost("fix-task-triggers")]
        [AllowAnonymous] // 修复操作不需要认证
        public async Task<IActionResult> FixTaskTriggers()
        {
            try
            {
                using var connection = new MySqlConnection(_connectionString);
                await connection.OpenAsync();

                // 删除所有有问题的触发器
                var dropTriggersSql = @"
                    DROP TRIGGER IF EXISTS `tr_tasks_status_update`;
                    DROP TRIGGER IF EXISTS `tr_tasks_status_update_backup`;
                    DROP TRIGGER IF EXISTS `tr_tasks_insert`;
                    DROP TRIGGER IF EXISTS `tr_tasks_update`;
                    DROP TRIGGER IF EXISTS `tr_task_claims_insert`;
                    DROP TRIGGER IF EXISTS `tr_task_claims_update`;
                    DROP TRIGGER IF EXISTS `tr_task_claims_update_backup`;
                    DROP TRIGGER IF EXISTS `tr_gamification_log_insert`;
                ";

                using var command = new MySqlCommand(dropTriggersSql, connection);
                await command.ExecuteNonQueryAsync();

                _logger.LogInformation("任务触发器冲突修复完成");
                return Ok(new { success = true, message = "任务触发器冲突修复完成，现在可以正常更新任务状态" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "修复任务触发器冲突失败");
                return StatusCode(500, new { success = false, message = "修复失败: " + ex.Message });
            }
        }

        /// <summary>
        /// 删除多余的触发器
        /// </summary>
        /// <returns>删除结果</returns>
        [HttpPost("remove-redundant-triggers")]
        [AllowAnonymous]
        public async Task<IActionResult> RemoveRedundantTriggers()
        {
            try
            {
                using var connection = new MySqlConnection(_connectionString);
                await connection.OpenAsync();

                // 根据当前业务逻辑，这些触发器是多余的，因为我们使用Entity Framework和应用层逻辑处理
                var redundantTriggers = new[]
                {
                    // 任务相关的自动触发器 - 现在通过应用层处理
                    "tr_tasks_status_update",
                    "tr_tasks_status_update_backup",
                    "tr_tasks_insert",
                    "tr_tasks_update",
                    "tr_tasks_after_update",
                    "tr_tasks_before_update",

                    // 任务领取相关触发器 - 现在通过TaskService处理
                    "tr_task_claims_insert",
                    "tr_task_claims_update",
                    "tr_task_claims_update_backup",
                    "tr_task_claims_after_insert",
                    "tr_task_claims_after_update",

                    // 游戏化日志触发器 - 现在通过GamificationService处理
                    "tr_gamification_log_insert",
                    "tr_gamification_log_update",
                    "tr_gamification_log_after_insert",

                    // 用户积分/等级触发器 - 现在通过UniversalGamificationService处理
                    "tr_user_points_update",
                    "tr_user_level_update",

                    // 资产相关触发器 - 如果通过应用层处理
                    "tr_assets_status_update",
                    "tr_assets_location_update",

                    // 通知触发器 - 现在通过NotificationService处理
                    "tr_notifications_insert",
                    "tr_notifications_update",

                    // 审计日志触发器 - 如果有专门的审计服务
                    "tr_audit_log_insert",
                    "tr_audit_log_update"
                };

                var deletedTriggers = new List<string>();
                var notFoundTriggers = new List<string>();

                foreach (var triggerName in redundantTriggers)
                {
                    try
                    {
                        var dropSql = $"DROP TRIGGER IF EXISTS `{triggerName}`";
                        using var command = new MySqlCommand(dropSql, connection);
                        var result = await command.ExecuteNonQueryAsync();

                        // 检查触发器是否真的存在并被删除
                        var checkSql = $"SELECT COUNT(*) FROM INFORMATION_SCHEMA.TRIGGERS WHERE TRIGGER_SCHEMA = DATABASE() AND TRIGGER_NAME = '{triggerName}'";
                        using var checkCommand = new MySqlCommand(checkSql, connection);
                        var exists = Convert.ToInt32(await checkCommand.ExecuteScalarAsync()) > 0;

                        if (!exists)
                        {
                            deletedTriggers.Add(triggerName);
                            _logger.LogInformation($"已删除触发器: {triggerName}");
                        }
                        else
                        {
                            notFoundTriggers.Add(triggerName);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning($"删除触发器 {triggerName} 时出错: {ex.Message}");
                        notFoundTriggers.Add(triggerName);
                    }
                }

                var message = $"删除操作完成。成功删除: {deletedTriggers.Count} 个，未找到: {notFoundTriggers.Count} 个";

                return Ok(new
                {
                    success = true,
                    message = message,
                    deletedTriggers = deletedTriggers,
                    notFoundTriggers = notFoundTriggers
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除多余触发器失败");
                return StatusCode(500, new { success = false, message = "删除失败: " + ex.Message });
            }
        }

        /// <summary>
        /// 快速删除所有已知的多余触发器
        /// </summary>
        /// <returns>删除结果</returns>
        [HttpPost("quick-clean-triggers")]
        [AllowAnonymous]
        public async Task<IActionResult> QuickCleanTriggers()
        {
            try
            {
                using var connection = new MySqlConnection(_connectionString);
                await connection.OpenAsync();

                // 执行批量删除SQL
                var dropSql = @"
                    DROP TRIGGER IF EXISTS `tr_tasks_status_update`;
                    DROP TRIGGER IF EXISTS `tr_tasks_status_update_backup`;
                    DROP TRIGGER IF EXISTS `tr_tasks_insert`;
                    DROP TRIGGER IF EXISTS `tr_tasks_update`;
                    DROP TRIGGER IF EXISTS `tr_tasks_after_update`;
                    DROP TRIGGER IF EXISTS `tr_tasks_before_update`;
                    DROP TRIGGER IF EXISTS `tr_task_claims_insert`;
                    DROP TRIGGER IF EXISTS `tr_task_claims_update`;
                    DROP TRIGGER IF EXISTS `tr_task_claims_update_backup`;
                    DROP TRIGGER IF EXISTS `tr_task_claims_after_insert`;
                    DROP TRIGGER IF EXISTS `tr_task_claims_after_update`;
                    DROP TRIGGER IF EXISTS `tr_gamification_log_insert`;
                    DROP TRIGGER IF EXISTS `tr_gamification_log_update`;
                    DROP TRIGGER IF EXISTS `tr_gamification_log_after_insert`;
                    DROP TRIGGER IF EXISTS `tr_user_points_update`;
                    DROP TRIGGER IF EXISTS `tr_user_level_update`;
                    DROP TRIGGER IF EXISTS `tr_assets_status_update`;
                    DROP TRIGGER IF EXISTS `tr_assets_location_update`;
                    DROP TRIGGER IF EXISTS `tr_notifications_insert`;
                    DROP TRIGGER IF EXISTS `tr_notifications_update`;
                    DROP TRIGGER IF EXISTS `tr_audit_log_insert`;
                    DROP TRIGGER IF EXISTS `tr_audit_log_update`;";

                using var command = new MySqlCommand(dropSql, connection);
                await command.ExecuteNonQueryAsync();

                // 检查剩余的触发器
                var checkSql = "SELECT COUNT(*) FROM INFORMATION_SCHEMA.TRIGGERS WHERE TRIGGER_SCHEMA = DATABASE()";
                using var checkCommand = new MySqlCommand(checkSql, connection);
                var remainingCount = Convert.ToInt32(await checkCommand.ExecuteScalarAsync());

                _logger.LogInformation("快速清理触发器完成");
                return Ok(new
                {
                    success = true,
                    message = $"快速清理完成，剩余触发器数量: {remainingCount}",
                    remainingTriggers = remainingCount
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "快速清理触发器失败");
                return StatusCode(500, new { success = false, message = "清理失败: " + ex.Message });
            }
        }

        /// <summary>
        /// 执行完整的触发器清理脚本
        /// </summary>
        /// <returns>清理结果</returns>
        [HttpPost("execute-trigger-cleanup")]
        [AllowAnonymous]
        public async Task<IActionResult> ExecuteTriggerCleanup()
        {
            try
            {
                using var connection = new MySqlConnection(_connectionString);
                await connection.OpenAsync();

                // 先获取清理前的触发器列表
                var beforeSql = @"
                    SELECT
                        TRIGGER_NAME,
                        EVENT_OBJECT_TABLE,
                        EVENT_MANIPULATION,
                        ACTION_TIMING
                    FROM INFORMATION_SCHEMA.TRIGGERS
                    WHERE TRIGGER_SCHEMA = DATABASE()
                    ORDER BY EVENT_OBJECT_TABLE, TRIGGER_NAME";

                var triggersBefore = new List<object>();
                using (var beforeCommand = new MySqlCommand(beforeSql, connection))
                using (var beforeReader = await beforeCommand.ExecuteReaderAsync())
                {
                    while (await beforeReader.ReadAsync())
                    {
                        triggersBefore.Add(new
                        {
                            Name = beforeReader["TRIGGER_NAME"].ToString(),
                            Table = beforeReader["EVENT_OBJECT_TABLE"].ToString(),
                            Event = beforeReader["EVENT_MANIPULATION"].ToString(),
                            Timing = beforeReader["ACTION_TIMING"].ToString()
                        });
                    }
                }

                // 执行清理操作
                var cleanupSql = @"
                    -- 任务相关触发器
                    DROP TRIGGER IF EXISTS `tr_tasks_status_update`;
                    DROP TRIGGER IF EXISTS `tr_tasks_status_update_backup`;
                    DROP TRIGGER IF EXISTS `tr_tasks_insert`;
                    DROP TRIGGER IF EXISTS `tr_tasks_update`;
                    DROP TRIGGER IF EXISTS `tr_tasks_after_update`;
                    DROP TRIGGER IF EXISTS `tr_tasks_before_update`;

                    -- 任务领取相关触发器
                    DROP TRIGGER IF EXISTS `tr_task_claims_insert`;
                    DROP TRIGGER IF EXISTS `tr_task_claims_update`;
                    DROP TRIGGER IF EXISTS `tr_task_claims_update_backup`;
                    DROP TRIGGER IF EXISTS `tr_task_claims_after_insert`;
                    DROP TRIGGER IF EXISTS `tr_task_claims_after_update`;

                    -- 游戏化相关触发器
                    DROP TRIGGER IF EXISTS `tr_gamification_log_insert`;
                    DROP TRIGGER IF EXISTS `tr_gamification_log_update`;
                    DROP TRIGGER IF EXISTS `tr_gamification_log_after_insert`;
                    DROP TRIGGER IF EXISTS `tr_user_points_update`;
                    DROP TRIGGER IF EXISTS `tr_user_level_update`;
                    DROP TRIGGER IF EXISTS `tr_user_stats_update`;
                    DROP TRIGGER IF EXISTS `tr_user_gamification_update`;

                    -- 通知相关触发器
                    DROP TRIGGER IF EXISTS `tr_notifications_insert`;
                    DROP TRIGGER IF EXISTS `tr_notifications_update`;
                    DROP TRIGGER IF EXISTS `tr_notifications_after_insert`;

                    -- 资产相关触发器
                    DROP TRIGGER IF EXISTS `tr_assets_status_update`;
                    DROP TRIGGER IF EXISTS `tr_assets_location_update`;
                    DROP TRIGGER IF EXISTS `tr_assets_after_update`;
                    DROP TRIGGER IF EXISTS `tr_assets_before_update`;

                    -- 审计日志相关触发器
                    DROP TRIGGER IF EXISTS `tr_audit_log_insert`;
                    DROP TRIGGER IF EXISTS `tr_audit_log_update`;
                    DROP TRIGGER IF EXISTS `tr_audit_after_insert`;

                    -- 其他触发器
                    DROP TRIGGER IF EXISTS `tr_users_update`;
                    DROP TRIGGER IF EXISTS `tr_users_after_update`;
                    DROP TRIGGER IF EXISTS `tr_departments_update`;
                    DROP TRIGGER IF EXISTS `tr_departments_after_update`;
                    DROP TRIGGER IF EXISTS `tr_locations_update`;
                    DROP TRIGGER IF EXISTS `tr_locations_after_update`;
                    DROP TRIGGER IF EXISTS `tr_faults_update`;
                    DROP TRIGGER IF EXISTS `tr_faults_after_update`;
                    DROP TRIGGER IF EXISTS `tr_repairs_update`;
                    DROP TRIGGER IF EXISTS `tr_repairs_after_update`;
                    DROP TRIGGER IF EXISTS `tr_spare_parts_update`;
                    DROP TRIGGER IF EXISTS `tr_spare_parts_after_update`;";

                using (var cleanupCommand = new MySqlCommand(cleanupSql, connection))
                {
                    await cleanupCommand.ExecuteNonQueryAsync();
                }

                // 获取清理后的触发器列表
                var triggersAfter = new List<object>();
                using (var afterCommand = new MySqlCommand(beforeSql, connection))
                using (var afterReader = await afterCommand.ExecuteReaderAsync())
                {
                    while (await afterReader.ReadAsync())
                    {
                        triggersAfter.Add(new
                        {
                            Name = afterReader["TRIGGER_NAME"].ToString(),
                            Table = afterReader["EVENT_OBJECT_TABLE"].ToString(),
                            Event = afterReader["EVENT_MANIPULATION"].ToString(),
                            Timing = afterReader["ACTION_TIMING"].ToString()
                        });
                    }
                }

                var deletedCount = triggersBefore.Count - triggersAfter.Count;

                _logger.LogInformation($"触发器清理完成：删除了 {deletedCount} 个触发器，剩余 {triggersAfter.Count} 个");

                return Ok(new
                {
                    success = true,
                    message = $"触发器清理完成：删除了 {deletedCount} 个触发器，剩余 {triggersAfter.Count} 个",
                    triggersBefore = triggersBefore,
                    triggersAfter = triggersAfter,
                    deletedCount = deletedCount,
                    remainingCount = triggersAfter.Count
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "执行触发器清理失败");
                return StatusCode(500, new { success = false, message = "清理失败: " + ex.Message });
            }
        }

        /// <summary>
        /// 删除剩余的5个多余触发器
        /// </summary>
        /// <returns>删除结果</returns>
        [HttpDelete("triggers/final-cleanup")]
        [AllowAnonymous]
        public async Task<IActionResult> FinalTriggerCleanup()
        {
            try
            {
                using var connection = new MySqlConnection(_connectionString);
                await connection.OpenAsync();

                // 要删除的最后5个触发器
                var finalTriggers = new[]
                {
                    "generate_fault_number",
                    "format_purchase_order_number",
                    "update_spare_part_quantity_after_inventory_change",
                    "update_spare_part_quantity_after_inventory_delete",
                    "update_spare_part_quantity_after_inventory_update"
                };

                var deletedTriggers = new List<string>();
                var errors = new List<string>();

                foreach (var triggerName in finalTriggers)
                {
                    try
                    {
                        var sql = $"DROP TRIGGER IF EXISTS `{triggerName}`";
                        using var command = new MySqlCommand(sql, connection);
                        await command.ExecuteNonQueryAsync();

                        deletedTriggers.Add(triggerName);
                        _logger.LogInformation($"成功删除触发器: {triggerName}");
                    }
                    catch (Exception ex)
                    {
                        errors.Add($"删除触发器 {triggerName} 失败: {ex.Message}");
                        _logger.LogError(ex, $"删除触发器 {triggerName} 失败");
                    }
                }

                // 检查剩余触发器数量
                var checkSql = "SELECT COUNT(*) FROM INFORMATION_SCHEMA.TRIGGERS WHERE TRIGGER_SCHEMA = DATABASE()";
                using var checkCommand = new MySqlCommand(checkSql, connection);
                var remainingCount = Convert.ToInt32(await checkCommand.ExecuteScalarAsync());

                var message = errors.Count == 0
                    ? $"🎉 触发器清理完全成功！删除了 {deletedTriggers.Count} 个触发器，剩余 {remainingCount} 个触发器"
                    : $"触发器清理部分成功：删除 {deletedTriggers.Count} 个，失败 {errors.Count} 个，剩余 {remainingCount} 个触发器";

                return Ok(new
                {
                    success = errors.Count == 0,
                    data = new
                    {
                        deletedTriggers = deletedTriggers,
                        deletedCount = deletedTriggers.Count,
                        remainingCount = remainingCount,
                        errors = errors
                    },
                    message = message
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "最终触发器清理失败");
                return StatusCode(500, new { success = false, message = "最终清理失败: " + ex.Message });
            }
        }
    }

    /// <summary>
    /// 设置基准线请求模型
    /// </summary>
    public class SetBaselineRequest
    {
        public string OperationName { get; set; }
        public double BaselineMs { get; set; }
    }
}
