// File: frontend/src/api/quickMemo.js
// Description: API service for Quick Memos (随手记).

import request from '@/utils/request'

/**
 * Fetches quick memos from the server.
 * @param {object} params Query parameters for fetching memos (e.g., limit, offset, category)
 */
export function getQuickMemos(params) {
  console.log('[API] getQuickMemos params:', params); // Debug log
  return request({
    url: '/v2/quick-memos', // CORRECTED: Removed leading /api
    method: 'get',
    params
  })
}

/**
 * Adds a new quick memo.
 * @param {object} data The quick memo data to add.
 * Expected data: { title: string, categoryId?: string, content?: string } // Note: categoryId from store
 */
export function addQuickMemo(data) {
  console.log('[API] addQuickMemo data:', data); // Debug log
  return request({
    url: '/v2/quick-memos', // CORRECTED: Removed leading /api
    method: 'post',
    data
  })
}

/**
 * Updates an existing quick memo.
 * @param {string|number} id The ID of the memo to update.
 * @param {object} data The quick memo data to update.
 */
export function updateQuickMemo(id, data) {
  console.log('[API] updateQuickMemo id:', id, 'data:', data); // Debug log
  return request({
    url: `/v2/quick-memos/${id}`, // CORRECTED: Removed leading /api
    method: 'put',
    data
  })
}

/**
 * Deletes a quick memo.
 * @param {string|number} id The ID of the memo to delete.
 */
export function deleteQuickMemo(id) {
  console.log('[API] deleteQuickMemo id:', id); // Debug log
  return request({
    url: `/v2/quick-memos/${id}`, // CORRECTED: Removed leading /api
    method: 'delete'
  })
}

/**
 * Fetches quick memo categories.
 */
export function getMemoCategories() {
  console.log('[API] getMemoCategories'); // Debug log
  return request({
    url: '/v2/quick-memos/categories', // Changed from /v2/quick-memo-categories
    method: 'get'
  })
}

/**
 * Adds a new quick memo category.
 * @param {object} data The category data to add.
 * Expected data: { name: string, color?: string }
 */
export function addMemoCategory(data) {
  console.log('[API] addMemoCategory data:', data); // Debug log
  return request({
    url: '/v2/quick-memo-categories', // CORRECTED: Removed leading /api
    method: 'post',
    data
  })
} 