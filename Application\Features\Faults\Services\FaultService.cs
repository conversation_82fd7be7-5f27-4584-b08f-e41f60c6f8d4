using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using ItAssetsSystem.Infrastructure.Data;
using ItAssetsSystem.Models.Entities;
using ItAssetsSystem.Application.Features.SpareParts.Services;
using ItAssetsSystem.Application.Features.SpareParts.Dtos;
using Microsoft.EntityFrameworkCore;

namespace ItAssetsSystem.Application.Features.Faults.Services
{
    /// <summary>
    /// 故障管理服务
    /// </summary>
    public class FaultService
    {
        private readonly AppDbContext _context;
        private readonly ILogger<FaultService> _logger;
        private readonly ISparePartService _sparePartService;

        public FaultService(
            AppDbContext context,
            ILogger<FaultService> logger,
            ISparePartService sparePartService)
        {
            _context = context;
            _logger = logger;
            _sparePartService = sparePartService;
        }

        /// <summary>
        /// 获取故障列表
        /// </summary>
        /// <param name="request">查询请求</param>
        /// <returns>故障列表</returns>
        public async Task<object> GetFaultListAsync(GetFaultListRequest request)
        {
            _logger.LogInformation($"获取故障列表，页码: {request.Page}，页大小: {request.PageSize}");

            try
            {
                var query = _context.FaultRecords.AsQueryable();

                // 应用筛选条件
                if (!string.IsNullOrEmpty(request.Code))
                {
                    query = query.Where(f => f.Id.ToString().Contains(request.Code));
                }

                if (!string.IsNullOrEmpty(request.AssetKeyword))
                {
                    query = query.Where(f => f.Description.Contains(request.AssetKeyword) ||
                                           (f.AssetId.HasValue && _context.Assets.Any(a => a.Id == f.AssetId &&
                                           (a.Name.Contains(request.AssetKeyword) || a.AssetCode.Contains(request.AssetKeyword)))));
                }

                if (request.FaultType.HasValue)
                {
                    query = query.Where(f => f.FaultTypeId == request.FaultType.Value);
                }

                if (request.Status.HasValue)
                {
                    query = query.Where(f => f.Status == request.Status.Value);
                }

                if (request.StartTime.HasValue)
                {
                    query = query.Where(f => f.ReportTime >= request.StartTime.Value);
                }

                if (request.EndTime.HasValue)
                {
                    query = query.Where(f => f.ReportTime <= request.EndTime.Value);
                }

                // 获取总数
                var total = await query.CountAsync();

                // 分页查询
                var faults = await query
                    .OrderByDescending(f => f.ReportTime)
                    .Skip((request.Page - 1) * request.PageSize)
                    .Take(request.PageSize)
                    .Select(f => new
                    {
                        id = f.Id,
                        code = "FIX-" + f.ReportTime.ToString("yyyyMMdd") + "-" + f.Id.ToString().PadLeft(3, '0'), // 生成故障编号
                        title = f.Title,
                        description = f.Description,
                        assetId = f.AssetId,
                        assetName = f.AssetId.HasValue ?
                            _context.Assets.Where(a => a.Id == f.AssetId).Select(a => a.AssetCode + " - " + a.Name).FirstOrDefault() :
                            "线下设备",
                        assetCode = f.AssetId.HasValue ?
                            _context.Assets.Where(a => a.Id == f.AssetId).Select(a => a.AssetCode).FirstOrDefault() :
                            null,
                        faultType = f.FaultTypeId,
                        faultTypeName = GetFaultTypeName(f.FaultTypeId),
                        priority = GetPriorityFromSeverity(f.Severity),
                        status = f.Status,
                        statusName = GetStatusName(f.Status),
                        reporterId = f.ReporterId,
                        reportUser = _context.Users.Where(u => u.Id == f.ReporterId).Select(u => u.Username).FirstOrDefault() ?? "未知用户",
                        reportTime = f.ReportTime.ToString("yyyy-MM-dd HH:mm"),
                        assigneeId = f.AssigneeId,
                        handler = f.AssigneeId.HasValue ?
                            _context.Users.Where(u => u.Id == f.AssigneeId).Select(u => u.Username).FirstOrDefault() :
                            null,
                        updateTime = f.UpdatedAt.ToString("yyyy-MM-dd HH:mm"),
                        severity = f.Severity
                    })
                    .ToListAsync();

                return new
                {
                    items = faults,
                    total = total,
                    page = request.Page,
                    pageSize = request.PageSize,
                    totalPages = (int)Math.Ceiling((double)total / request.PageSize)
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取故障列表失败");
                throw;
            }
        }

        /// <summary>
        /// 获取故障详情
        /// </summary>
        /// <param name="id">故障ID</param>
        /// <returns>故障详情</returns>
        public async Task<object> GetFaultByIdAsync(int id)
        {
            _logger.LogInformation($"获取故障详情，故障ID: {id}");

            try
            {
                var fault = await _context.FaultRecords
                    .Where(f => f.Id == id)
                    .Select(f => new
                    {
                        id = f.Id,
                        code = "FIX-" + f.ReportTime.ToString("yyyyMMdd") + "-" + f.Id.ToString().PadLeft(3, '0'),
                        title = f.Title,
                        description = f.Description,
                        assetId = f.AssetId,
                        assetName = f.AssetId.HasValue ?
                            _context.Assets.Where(a => a.Id == f.AssetId).Select(a => a.AssetCode + " - " + a.Name).FirstOrDefault() :
                            "线下设备",
                        assetCode = f.AssetId.HasValue ?
                            _context.Assets.Where(a => a.Id == f.AssetId).Select(a => a.AssetCode).FirstOrDefault() :
                            null,
                        faultType = f.FaultTypeId,
                        faultTypeName = GetFaultTypeName(f.FaultTypeId),
                        priority = GetPriorityFromSeverity(f.Severity),
                        status = f.Status,
                        statusName = GetStatusName(f.Status),
                        reporterId = f.ReporterId,
                        reportUser = _context.Users.Where(u => u.Id == f.ReporterId).Select(u => u.Username).FirstOrDefault() ?? "未知用户",
                        reportTime = f.ReportTime.ToString("yyyy-MM-dd HH:mm:ss"),
                        assigneeId = f.AssigneeId,
                        handler = f.AssigneeId.HasValue ?
                            _context.Users.Where(u => u.Id == f.AssigneeId).Select(u => u.Username).FirstOrDefault() :
                            null,
                        createTime = f.CreatedAt.ToString("yyyy-MM-dd HH:mm:ss"),
                        updateTime = f.UpdatedAt.ToString("yyyy-MM-dd HH:mm:ss"),
                        severity = f.Severity,
                        happenTime = f.ReportTime.ToString("yyyy-MM-dd HH:mm:ss"), // 使用报告时间作为发生时间
                        locationId = f.LocationId,
                        locationName = f.LocationId.HasValue ?
                            _context.Locations.Where(l => l.Id == f.LocationId).Select(l => l.Name).FirstOrDefault() :
                            null
                    })
                    .FirstOrDefaultAsync();

                if (fault == null)
                {
                    throw new Exception($"故障记录不存在，ID: {id}");
                }

                return fault;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取故障详情失败，故障ID: {Id}", id);
                throw;
            }
        }

        /// <summary>
        /// 创建故障记录
        /// </summary>
        /// <param name="request">故障创建请求</param>
        /// <returns>创建的故障记录</returns>
        public async Task<object> CreateFaultAsync(CreateFaultRequest request)
        {
            _logger.LogInformation($"开始创建故障记录: {request.Title}");

            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                // 创建故障记录
                var fault = new FaultRecord
                {
                    AssetId = request.AssetId, // 线下设备时为null
                    FaultTypeId = request.FaultTypeId,
                    Title = request.Title,
                    Description = BuildFaultDescription(request),
                    Status = 0, // 待处理
                    Severity = GetSeverityFromPriority(request.Priority),
                    ReporterId = request.ReporterId,
                    ReportTime = request.HappenTime ?? DateTime.Now,
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now
                };

                _context.FaultRecords.Add(fault);
                await _context.SaveChangesAsync();

                _logger.LogInformation($"故障记录创建成功，ID: {fault.Id}");

                // 如果需要自动生成备件入库记录
                if (request.AutoGenerateSparePartRecord && request.SparePartInfo != null)
                {
                    await CreateSparePartFromFaultAsync(fault.Id, request.SparePartInfo);
                }

                await transaction.CommitAsync();

                return new
                {
                    id = fault.Id,
                    title = fault.Title,
                    status = fault.Status,
                    statusName = GetStatusName(fault.Status),
                    createdAt = fault.CreatedAt
                };
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, $"创建故障记录失败: {request.Title}");
                throw;
            }
        }

        /// <summary>
        /// 从故障信息自动创建备件记录
        /// </summary>
        /// <param name="faultId">故障ID</param>
        /// <param name="sparePartInfo">备件信息</param>
        private async Task CreateSparePartFromFaultAsync(int faultId, SparePartInfoRequest sparePartInfo)
        {
            _logger.LogInformation($"开始为故障 {faultId} 创建备件记录: {sparePartInfo.Name}");

            try
            {
                // 生成备件编号
                var sparePartCode = await GenerateSparePartCodeAsync();

                // 创建备件记录
                var createRequest = new CreateSparePartRequest
                {
                    Code = sparePartCode,
                    Name = sparePartInfo.Name,
                    TypeId = sparePartInfo.TypeId ?? 1, // 如果没有指定类型，使用默认类型ID 1
                    Specification = sparePartInfo.Specification,
                    Brand = sparePartInfo.Brand,
                    Unit = "个", // 默认单位
                    Price = sparePartInfo.Price,
                    LocationId = sparePartInfo.LocationId,
                    InitialStock = sparePartInfo.Quantity,
                    MinStock = 1, // 默认最小库存
                    WarningThreshold = 2, // 默认预警阈值
                    Remarks = $"故障登记自动生成 - 故障ID: {faultId}"
                };

                var createdSparePart = await _sparePartService.CreateSparePartAsync(createRequest);

                _logger.LogInformation($"备件记录创建成功，ID: {createdSparePart.Id}，故障ID: {faultId}");

                // 记录关联关系（可以在数据库中添加一个关联表）
                // TODO: 如果需要，可以创建一个 FaultSparePart 关联表来记录这种关系
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"为故障 {faultId} 创建备件记录失败");
                throw;
            }
        }

        /// <summary>
        /// 生成备件编号
        /// </summary>
        /// <returns>备件编号</returns>
        private async Task<string> GenerateSparePartCodeAsync()
        {
            var datePrefix = DateTime.Now.ToString("yyyyMMdd");
            var lastCode = await _context.SpareParts
                .Where(sp => sp.Code.StartsWith($"SP-{datePrefix}-"))
                .OrderByDescending(sp => sp.Code)
                .Select(sp => sp.Code)
                .FirstOrDefaultAsync();

            int sequence = 1;
            if (!string.IsNullOrEmpty(lastCode))
            {
                var lastSequence = lastCode.Substring(lastCode.LastIndexOf('-') + 1);
                if (int.TryParse(lastSequence, out int lastSeq))
                {
                    sequence = lastSeq + 1;
                }
            }

            return $"SP-{datePrefix}-{sequence:D3}";
        }

        /// <summary>
        /// 获取状态名称
        /// </summary>
        /// <param name="status">状态值</param>
        /// <returns>状态名称</returns>
        private static string GetStatusName(int status)
        {
            return status switch
            {
                0 => "待处理",
                1 => "处理中",
                2 => "已完成",
                3 => "已关闭",
                _ => "未知状态"
            };
        }

        /// <summary>
        /// 构建故障描述
        /// </summary>
        /// <param name="request">故障请求</param>
        /// <returns>完整的故障描述</returns>
        private static string BuildFaultDescription(CreateFaultRequest request)
        {
            var description = request.Description ?? "";

            // 如果是线下设备模式，在描述中添加设备信息
            if (request.FaultMode == "offline" && !string.IsNullOrEmpty(request.DeviceName))
            {
                var deviceInfo = $"【线下设备】设备名称: {request.DeviceName}";
                if (!string.IsNullOrEmpty(description))
                {
                    description = $"{deviceInfo}\n\n{description}";
                }
                else
                {
                    description = deviceInfo;
                }
            }

            return description;
        }

        /// <summary>
        /// 从优先级字符串获取严重程度数值
        /// </summary>
        /// <param name="priority">优先级字符串</param>
        /// <returns>严重程度数值</returns>
        private static int GetSeverityFromPriority(string priority)
        {
            return priority?.ToLower() switch
            {
                "high" => 2,    // 紧急
                "urgent" => 2,  // 紧急
                "medium" => 1,  // 严重
                "low" => 0,     // 一般
                _ => 0          // 默认一般
            };
        }

        /// <summary>
        /// 从严重程度数值获取优先级字符串
        /// </summary>
        /// <param name="severity">严重程度数值</param>
        /// <returns>优先级字符串</returns>
        private static string GetPriorityFromSeverity(int severity)
        {
            return severity switch
            {
                2 => "high",    // 紧急
                1 => "medium",  // 严重
                0 => "low",     // 一般
                _ => "low"      // 默认一般
            };
        }

        /// <summary>
        /// 获取故障类型名称
        /// </summary>
        /// <param name="faultTypeId">故障类型ID</param>
        /// <returns>故障类型名称</returns>
        private static string GetFaultTypeName(int faultTypeId)
        {
            return faultTypeId switch
            {
                1 => "硬件故障",
                2 => "软件故障",
                3 => "网络故障",
                4 => "外设故障",
                5 => "其他故障",
                _ => "未知类型"
            };
        }


    }

    /// <summary>
    /// 创建故障请求
    /// </summary>
    public class CreateFaultRequest
    {
        /// <summary>
        /// 故障模式：asset(有资产) / offline(线下设备)
        /// </summary>
        public string FaultMode { get; set; } = "asset";

        /// <summary>
        /// 资产ID（可选）
        /// </summary>
        public int? AssetId { get; set; }

        /// <summary>
        /// 资产关键字（用于搜索）
        /// </summary>
        public string AssetKeyword { get; set; }

        /// <summary>
        /// 线下设备名称（当故障模式为offline时使用）
        /// </summary>
        public string DeviceName { get; set; }

        /// <summary>
        /// 故障类型ID
        /// </summary>
        public int FaultTypeId { get; set; }

        /// <summary>
        /// 故障标题
        /// </summary>
        public string Title { get; set; }

        /// <summary>
        /// 故障描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 优先级
        /// </summary>
        public string Priority { get; set; }

        /// <summary>
        /// 报告人ID
        /// </summary>
        public int ReporterId { get; set; }

        /// <summary>
        /// 故障发生时间
        /// </summary>
        public DateTime? HappenTime { get; set; }

        /// <summary>
        /// 是否自动生成备件记录
        /// </summary>
        public bool AutoGenerateSparePartRecord { get; set; } = false;

        /// <summary>
        /// 备件信息
        /// </summary>
        public SparePartInfoRequest SparePartInfo { get; set; }
    }

    /// <summary>
    /// 备件信息请求
    /// </summary>
    public class SparePartInfoRequest
    {
        /// <summary>
        /// 备件名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 备件类型ID
        /// </summary>
        public long? TypeId { get; set; }

        /// <summary>
        /// 规格型号
        /// </summary>
        public string Specification { get; set; }

        /// <summary>
        /// 品牌
        /// </summary>
        public string Brand { get; set; }

        /// <summary>
        /// 入库数量
        /// </summary>
        public int Quantity { get; set; } = 1;

        /// <summary>
        /// 单价
        /// </summary>
        public decimal? Price { get; set; }

        /// <summary>
        /// 库位ID
        /// </summary>
        public long? LocationId { get; set; }
    }

    /// <summary>
    /// 获取故障列表请求
    /// </summary>
    public class GetFaultListRequest
    {
        /// <summary>
        /// 页码
        /// </summary>
        public int Page { get; set; } = 1;

        /// <summary>
        /// 页大小
        /// </summary>
        public int PageSize { get; set; } = 10;

        /// <summary>
        /// 故障编号
        /// </summary>
        public string Code { get; set; }

        /// <summary>
        /// 资产关键字
        /// </summary>
        public string AssetKeyword { get; set; }

        /// <summary>
        /// 故障类型
        /// </summary>
        public int? FaultType { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public int? Status { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime? StartTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime? EndTime { get; set; }
    }
}
