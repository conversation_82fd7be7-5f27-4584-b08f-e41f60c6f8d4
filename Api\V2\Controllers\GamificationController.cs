using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using ItAssetsSystem.Services.Interfaces;
using ItAssetsSystem.Models.DTOs.Gamification;
using ItAssetsSystem.Application.Common.Dtos;
using ItAssetsSystem.Core.Services;
using ItAssetsSystem.Core.Abstractions;
using ItAssetsSystem.Application.Features.Gamification.Services;

namespace ItAssetsSystem.Api.V2.Controllers
{
    /// <summary>
    /// 游戏化系统API控制器
    /// </summary>
    [ApiController]
    [Route("api/v2/gamification")]
    [Route("api/gamification")]
    [Authorize]
    public class GamificationController : ControllerBase
    {
        private readonly IGamificationService _gamificationService;
        private readonly ICurrentUserService _currentUserService;
        private readonly ILogger<GamificationController> _logger;
        private readonly IGamificationRuleEngine _ruleEngine;

        public GamificationController(
            IGamificationService gamificationService,
            ICurrentUserService currentUserService,
            ILogger<GamificationController> logger,
            IGamificationRuleEngine ruleEngine)
        {
            _gamificationService = gamificationService;
            _currentUserService = currentUserService;
            _logger = logger;
            _ruleEngine = ruleEngine;
        }

        /// <summary>
        /// 获取当前用户游戏化统计信息
        /// </summary>
        /// <returns>用户游戏化统计信息</returns>
        [HttpGet("stats")]
        public async Task<IActionResult> GetUserStats()
        {
            try
            {
                var currentUserId = _currentUserService.GetCurrentUserId();
                if (currentUserId <= 0)
                {
                    return Unauthorized(ApiResponse<object>.CreateFail("用户未登录"));
                }

                var stats = await _gamificationService.GetUserStatsAsync(currentUserId);
                if (stats == null)
                {
                    return NotFound(ApiResponse<object>.CreateFail("用户游戏化数据不存在"));
                }

                return Ok(ApiResponse<GamificationUserStatsDto>.CreateSuccess(stats, "获取用户游戏化统计信息成功"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户游戏化统计信息时发生错误");
                return StatusCode(500, ApiResponse<object>.CreateFail("获取用户游戏化统计信息失败"));
            }
        }

        /// <summary>
        /// 获取指定用户游戏化统计信息
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>用户游戏化统计信息</returns>
        [HttpGet("stats/{userId}")]
        public async Task<IActionResult> GetUserStatsById(int userId)
        {
            try
            {
                var stats = await _gamificationService.GetUserStatsAsync(userId);
                if (stats == null)
                {
                    return NotFound(ApiResponse<object>.CreateFail("用户游戏化数据不存在"));
                }

                return Ok(ApiResponse<GamificationUserStatsDto>.CreateSuccess(stats, "获取用户游戏化统计信息成功"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户 {UserId} 游戏化统计信息时发生错误", userId);
                return StatusCode(500, ApiResponse<object>.CreateFail("获取用户游戏化统计信息失败"));
            }
        }

        /// <summary>
        /// 获取用户每日任务统计
        /// </summary>
        /// <param name="date">日期 (可选，默认今天)</param>
        /// <returns>每日任务统计</returns>
        [HttpGet("daily-stats")]
        public async Task<IActionResult> GetDailyTaskStats([FromQuery] DateTime? date = null)
        {
            try
            {
                var currentUserId = _currentUserService.GetCurrentUserId();
                if (currentUserId <= 0)
                {
                    return Unauthorized(ApiResponse<object>.CreateFail("用户未登录"));
                }

                var targetDate = date ?? DateTime.Today;
                var stats = await _gamificationService.GetDailyTaskStatsAsync(currentUserId, targetDate);

                return Ok(ApiResponse<DailyTaskStatsDto>.CreateSuccess(stats, "获取每日任务统计成功"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取每日任务统计时发生错误");
                return StatusCode(500, ApiResponse<object>.CreateFail("获取每日任务统计失败"));
            }
        }

        /// <summary>
        /// 获取排行榜
        /// </summary>
        /// <param name="type">排行榜类型 (Weekly=1, Monthly=2, AllTime=3)</param>
        /// <param name="topN">前N名 (默认10)</param>
        /// <returns>排行榜列表</returns>
        [HttpGet("leaderboard")]
        public async Task<IActionResult> GetLeaderboard([FromQuery] LeaderboardType type = LeaderboardType.Weekly, [FromQuery] int topN = 10)
        {
            try
            {
                var leaderboard = await _gamificationService.GetTopLeaderboardAsync(type, topN);
                return Ok(ApiResponse<List<UserLeaderboardDto>>.CreateSuccess(leaderboard, "获取排行榜成功"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取排行榜时发生错误");
                return StatusCode(500, ApiResponse<object>.CreateFail("获取排行榜失败"));
            }
        }

        /// <summary>
        /// 获取当前用户在排行榜中的位置
        /// </summary>
        /// <param name="type">排行榜类型</param>
        /// <returns>用户排行榜信息</returns>
        [HttpGet("my-rank")]
        public async Task<IActionResult> GetMyRank([FromQuery] LeaderboardType type = LeaderboardType.Weekly)
        {
            try
            {
                var currentUserId = _currentUserService.GetCurrentUserId();
                if (currentUserId <= 0)
                {
                    return Unauthorized(ApiResponse<object>.CreateFail("用户未登录"));
                }

                var userRank = await _gamificationService.GetUserLeaderboardAsync(currentUserId, type);
                if (userRank == null)
                {
                    return NotFound(ApiResponse<object>.CreateFail("用户排行榜信息不存在"));
                }

                return Ok(ApiResponse<UserLeaderboardDto>.CreateSuccess(userRank, "获取用户排行榜信息成功"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户排行榜信息时发生错误");
                return StatusCode(500, ApiResponse<object>.CreateFail("获取用户排行榜信息失败"));
            }
        }

        /// <summary>
        /// 初始化用户游戏化数据
        /// </summary>
        /// <returns>初始化结果</returns>
        [HttpPost("initialize")]
        public async Task<IActionResult> InitializeUserStats()
        {
            try
            {
                var currentUserId = _currentUserService.GetCurrentUserId();
                if (currentUserId <= 0)
                {
                    return Unauthorized(ApiResponse<object>.CreateFail("用户未登录"));
                }

                var stats = await _gamificationService.InitializeUserStatsAsync(currentUserId);
                return Ok(ApiResponse<GamificationUserStatsDto>.CreateSuccess(stats, "用户游戏化数据初始化成功"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "初始化用户游戏化数据时发生错误");
                return StatusCode(500, ApiResponse<object>.CreateFail("初始化用户游戏化数据失败"));
            }
        }

        /// <summary>
        /// 手动触发任务领取奖励 (测试用)
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <returns>奖励结果</returns>
        [HttpPost("rewards/claim/{taskId}")]
        public async Task<IActionResult> TriggerClaimReward(long taskId)
        {
            try
            {
                var currentUserId = _currentUserService.GetCurrentUserId();
                if (currentUserId <= 0)
                {
                    return Unauthorized(ApiResponse<object>.CreateFail("用户未登录"));
                }

                var reward = await _gamificationService.ClaimTaskRewardAsync(currentUserId, taskId);
                return Ok(ApiResponse<GamificationRewardDto>.CreateSuccess(reward, "任务领取奖励处理完成"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理任务领取奖励时发生错误");
                return StatusCode(500, ApiResponse<object>.CreateFail("处理任务领取奖励失败"));
            }
        }

        /// <summary>
        /// 手动触发任务完成奖励 (测试用)
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <param name="isOnTime">是否按时完成</param>
        /// <returns>奖励结果</returns>
        [HttpPost("rewards/complete/{taskId}")]
        public async Task<IActionResult> TriggerCompleteReward(long taskId, [FromQuery] bool isOnTime = false)
        {
            try
            {
                var currentUserId = _currentUserService.GetCurrentUserId();
                if (currentUserId <= 0)
                {
                    return Unauthorized(ApiResponse<object>.CreateFail("用户未登录"));
                }

                var reward = await _gamificationService.CompleteTaskRewardAsync(currentUserId, taskId, isOnTime);
                return Ok(ApiResponse<GamificationRewardDto>.CreateSuccess(reward, "任务完成奖励处理完成"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理任务完成奖励时发生错误");
                return StatusCode(500, ApiResponse<object>.CreateFail("处理任务完成奖励失败"));
            }
        }

        /// <summary>
        /// 获取周统计汇总 - 所有人按周的任务创建、领取、完成数量
        /// </summary>
        /// <param name="weekOffset">周偏移量 (0=本周, -1=上周, 1=下周)</param>
        /// <returns>周统计汇总</returns>
        [HttpGet("weekly-stats")]
        public async Task<IActionResult> GetWeeklyStats([FromQuery] int weekOffset = 0)
        {
            try
            {
                var weeklyStats = await _gamificationService.GetWeeklyStatsAsync(weekOffset);
                return Ok(ApiResponse<List<WeeklyTaskStatsDto>>.CreateSuccess(weeklyStats, "获取周统计汇总成功"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取周统计汇总时发生错误");
                return StatusCode(500, ApiResponse<object>.CreateFail("获取周统计汇总失败"));
            }
        }

        /// <summary>
        /// 获取用户周统计详情
        /// </summary>
        /// <param name="userId">用户ID (可选，默认当前用户)</param>
        /// <param name="weekOffset">周偏移量 (0=本周, -1=上周, 1=下周)</param>
        /// <returns>用户周统计详情</returns>
        [HttpGet("weekly-stats/user")]
        public async Task<IActionResult> GetUserWeeklyStats([FromQuery] int? userId = null, [FromQuery] int weekOffset = 0)
        {
            try
            {
                var currentUserId = _currentUserService.GetCurrentUserId();
                var targetUserId = userId ?? currentUserId;
                if (targetUserId <= 0)
                {
                    return Unauthorized(ApiResponse<object>.CreateFail("用户未登录"));
                }

                var userWeeklyStats = await _gamificationService.GetUserWeeklyStatsAsync(targetUserId, weekOffset);
                if (userWeeklyStats == null)
                {
                    return NotFound(ApiResponse<object>.CreateFail("用户周统计数据不存在"));
                }

                return Ok(ApiResponse<WeeklyTaskStatsDto>.CreateSuccess(userWeeklyStats, "获取用户周统计详情成功"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户周统计详情时发生错误");
                return StatusCode(500, ApiResponse<object>.CreateFail("获取用户周统计详情失败"));
            }
        }

        #region 规则管理

        /// <summary>
        /// 获取游戏化规则列表
        /// </summary>
        /// <returns>规则列表</returns>
        [HttpGet("rules")]
        public async Task<IActionResult> GetRules()
        {
            try
            {
                var rules = await _ruleEngine.GetAllRulesAsync();
                return Ok(ApiResponse<object>.CreateSuccess(rules, "获取规则列表成功"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取游戏化规则列表时发生错误");
                return StatusCode(500, ApiResponse<object>.CreateFail("获取规则列表失败"));
            }
        }

        /// <summary>
        /// 添加游戏化规则
        /// </summary>
        /// <param name="rule">规则配置</param>
        /// <returns>操作结果</returns>
        [HttpPost("rules")]
        public async Task<IActionResult> AddRule([FromBody] object rule)
        {
            try
            {
                if (rule == null)
                {
                    return BadRequest(ApiResponse<object>.CreateFail("规则配置不能为空"));
                }

                // 这里需要根据实际的规则类型进行转换
                // 暂时返回成功，实际实现需要调用规则引擎
                return Ok(ApiResponse<object>.CreateSuccess(null, "规则添加成功"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "添加游戏化规则时发生错误");
                return StatusCode(500, ApiResponse<object>.CreateFail("添加规则失败"));
            }
        }

        /// <summary>
        /// 更新游戏化规则
        /// </summary>
        /// <param name="rule">规则配置</param>
        /// <returns>操作结果</returns>
        [HttpPut("rules")]
        public async Task<IActionResult> UpdateRule([FromBody] object rule)
        {
            try
            {
                if (rule == null)
                {
                    return BadRequest(ApiResponse<object>.CreateFail("规则配置不能为空"));
                }

                // 这里需要根据实际的规则类型进行转换
                // 暂时返回成功，实际实现需要调用规则引擎
                return Ok(ApiResponse<object>.CreateSuccess(null, "规则更新成功"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新游戏化规则时发生错误");
                return StatusCode(500, ApiResponse<object>.CreateFail("更新规则失败"));
            }
        }

        /// <summary>
        /// 删除游戏化规则
        /// </summary>
        /// <param name="ruleId">规则ID</param>
        /// <returns>操作结果</returns>
        [HttpDelete("rules/{ruleId}")]
        public async Task<IActionResult> DeleteRule(string ruleId)
        {
            try
            {
                // 暂时返回成功，实际实现需要调用规则引擎
                return Ok(ApiResponse<object>.CreateSuccess(null, "规则删除成功"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除游戏化规则时发生错误: {RuleId}", ruleId);
                return StatusCode(500, ApiResponse<object>.CreateFail("删除规则失败"));
            }
        }

        /// <summary>
        /// 启用/禁用规则
        /// </summary>
        /// <param name="ruleId">规则ID</param>
        /// <param name="isActive">是否启用</param>
        /// <returns>操作结果</returns>
        [HttpPatch("rules/{ruleId}/active")]
        public async Task<IActionResult> SetRuleActive(string ruleId, [FromQuery] bool isActive)
        {
            try
            {
                var success = await _ruleEngine.SetRuleActiveAsync(ruleId, isActive);
                if (success)
                {
                    return Ok(ApiResponse<object>.CreateSuccess(null, $"规则{(isActive ? "启用" : "禁用")}成功"));
                }
                else
                {
                    return BadRequest(ApiResponse<object>.CreateFail($"规则{(isActive ? "启用" : "禁用")}失败"));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "{Action}游戏化规则时发生错误: {RuleId}", isActive ? "启用" : "禁用", ruleId);
                return StatusCode(500, ApiResponse<object>.CreateFail($"规则{(isActive ? "启用" : "禁用")}失败"));
            }
        }

        /// <summary>
        /// 重新加载规则缓存
        /// </summary>
        /// <returns>操作结果</returns>
        [HttpPost("rules/reload")]
        public async Task<IActionResult> ReloadRules()
        {
            try
            {
                await _ruleEngine.ReloadRulesAsync();
                return Ok(ApiResponse<object>.CreateSuccess(null, "规则缓存重新加载成功"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "重新加载游戏化规则缓存时发生错误");
                return StatusCode(500, ApiResponse<object>.CreateFail("重新加载规则缓存失败"));
            }
        }

        #endregion
    }
}
