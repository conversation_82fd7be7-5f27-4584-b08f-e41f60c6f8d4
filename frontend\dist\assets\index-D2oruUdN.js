import{_ as e,v as a,aG as l,b as t,d,e as r,w as o,r as s,ad as u,c as i,z as n,a as c,E as p,a9 as m,m as f,o as b,l as v,p as w,k as _,$ as y,t as V}from"./index-CG5lHOPO.js";import{b as k,c as h,u as g,d as R,t as F,e as C,r as T,s as U,f as I,h as A,i as D}from"./gamification-2FBMrBgR.js";const x={class:"gamification-container"},L={class:"overview-section"},N={class:"rules-section"},P={class:"rules-header"},j={class:"reward-display"},q={key:0},G={key:1},z={key:2},E={class:"leaderboard-section"},M={class:"leaderboard-controls"},O={class:"test-section"};const S=e({name:"GamificationManagement",components:{Plus:l,Refresh:a},setup(){const e=s("overview"),a=s(!1),l=s("weekly"),t=u({activeUsers:0,totalPoints:0,activeRules:0}),d=s([]),r=s([]),o=s([]),c=u({taskId:"",taskName:"测试任务",taskType:"General",userId:""}),f=s(!1),b=s(!1),v=s(),w=u({id:"",name:"",eventType:"",condition:"{}",pointsReward:0,coinsReward:0,diamondsReward:0,experienceReward:0,isActive:!0,priority:100}),_=i((()=>b.value?"编辑规则":"添加规则")),y=async()=>{try{a.value=!0;const e=await k();e.success&&(r.value=e.data||[],t.activeRules=r.value.filter((e=>e.isActive)).length)}catch(e){p.error("加载规则失败")}finally{a.value=!1}};return n((()=>{y(),(async()=>{try{(await h()).success&&(t.activeUsers=25,t.totalPoints=15e3)}catch(e){}})()})),{activeTab:e,rulesLoading:a,leaderboardType:l,stats:t,recentActivities:d,rules:r,leaderboardData:o,testForm:c,ruleDialogVisible:f,isEditMode:b,ruleFormRef:v,ruleForm:w,ruleFormRules:{id:[{required:!0,message:"请输入规则ID",trigger:"blur"}],name:[{required:!0,message:"请输入规则名称",trigger:"blur"}],eventType:[{required:!0,message:"请选择事件类型",trigger:"change"}],pointsReward:[{required:!0,message:"请输入积分奖励",trigger:"blur"}]},ruleDialogTitle:_,showAddRuleDialog:()=>{b.value=!1,Object.assign(w,{id:"",name:"",eventType:"",condition:"{}",pointsReward:0,coinsReward:0,diamondsReward:0,experienceReward:0,isActive:!0,priority:100}),f.value=!0},editRule:e=>{b.value=!0,Object.assign(w,e),f.value=!0},saveRule:async()=>{try{await v.value.validate(),b.value?(await A(w),p.success("规则更新成功")):(await D(w),p.success("规则添加成功")),f.value=!1,await y()}catch(e){p.error("保存规则失败")}},deleteRule:async e=>{try{await m.confirm(`确定要删除规则 "${e.name}" 吗？`,"确认删除",{type:"warning"}),await I(e.id),p.success("规则删除成功"),await y()}catch(a){"cancel"!==a&&p.error("删除规则失败")}},toggleRuleActive:async e=>{try{await U(e.id,e.isActive),p.success("规则已"+(e.isActive?"启用":"禁用")),await y()}catch(a){p.error("切换规则状态失败"),e.isActive=!e.isActive}},reloadRules:async()=>{try{await T(),p.success("规则缓存重新加载成功"),await y()}catch(e){p.error("重新加载规则缓存失败")}},updateLeaderboard:async()=>{try{await g(l.value),p.success("排行榜更新成功")}catch(e){p.error("更新排行榜失败")}},testTaskCreated:async()=>{try{if(!c.taskId)return void p.warning("请输入任务ID");await C(c.taskId,c.taskName,c.taskType),p.success("任务创建奖励测试完成")}catch(e){p.error("测试任务创建奖励失败")}},testTaskCompleted:async()=>{try{if(!c.taskId)return void p.warning("请输入任务ID");await F(c.taskId,c.taskName,c.taskType,!0),p.success("任务完成奖励测试完成")}catch(e){p.error("测试任务完成奖励失败")}},testUpdateStats:async()=>{try{const e=c.userId?parseInt(c.userId):null;await R(e),p.success("用户统计更新完成")}catch(e){p.error("更新用户统计失败")}},testUpdateLeaderboard:async()=>{try{await g(),p.success("排行榜更新完成")}catch(e){p.error("更新排行榜失败")}}}}},[["render",function(e,a,l,s,u,i){const n=c("el-card"),p=c("el-col"),m=c("el-row"),k=c("el-statistic"),h=c("el-divider"),g=c("el-table-column"),R=c("el-table"),F=c("el-tab-pane"),C=c("Plus"),T=c("el-icon"),U=c("el-button"),I=c("Refresh"),A=c("el-switch"),D=c("el-radio-button"),S=c("el-radio-group"),B=c("el-alert"),$=c("el-input"),J=c("el-form-item"),H=c("el-option"),K=c("el-select"),Q=c("el-form"),W=c("el-tabs"),X=c("el-input-number"),Y=c("el-dialog"),Z=f("loading");return b(),t("div",x,[a[41]||(a[41]=d("div",{class:"page-header"},[d("h1",null,"🎮 游戏化系统管理"),d("p",null,"管理游戏化规则、查看统计数据和系统状态")],-1)),r(m,{gutter:20,class:"feature-cards"},{default:o((()=>[r(p,{span:6},{default:o((()=>[r(n,{class:"feature-card",onClick:a[0]||(a[0]=e=>s.activeTab="overview")},{default:o((()=>a[22]||(a[22]=[d("div",{class:"feature-icon"},"📊",-1),d("h3",null,"系统概览",-1),d("p",null,"查看游戏化系统整体状态",-1)]))),_:1})])),_:1}),r(p,{span:6},{default:o((()=>[r(n,{class:"feature-card",onClick:a[1]||(a[1]=e=>s.activeTab="rules")},{default:o((()=>a[23]||(a[23]=[d("div",{class:"feature-icon"},"⚙️",-1),d("h3",null,"规则管理",-1),d("p",null,"配置和管理奖励规则",-1)]))),_:1})])),_:1}),r(p,{span:6},{default:o((()=>[r(n,{class:"feature-card",onClick:a[2]||(a[2]=e=>s.activeTab="leaderboard")},{default:o((()=>a[24]||(a[24]=[d("div",{class:"feature-icon"},"🏆",-1),d("h3",null,"排行榜",-1),d("p",null,"查看用户积分排名",-1)]))),_:1})])),_:1}),r(p,{span:6},{default:o((()=>[r(n,{class:"feature-card",onClick:a[3]||(a[3]=e=>s.activeTab="test")},{default:o((()=>a[25]||(a[25]=[d("div",{class:"feature-icon"},"🧪",-1),d("h3",null,"功能测试",-1),d("p",null,"测试游戏化功能",-1)]))),_:1})])),_:1})])),_:1}),r(n,{class:"main-content"},{default:o((()=>[r(W,{modelValue:s.activeTab,"onUpdate:modelValue":a[9]||(a[9]=e=>s.activeTab=e),type:"border-card"},{default:o((()=>[r(F,{label:"系统概览",name:"overview"},{default:o((()=>[d("div",L,[r(m,{gutter:20},{default:o((()=>[r(p,{span:8},{default:o((()=>[r(k,{title:"活跃用户数",value:s.stats.activeUsers},null,8,["value"])])),_:1}),r(p,{span:8},{default:o((()=>[r(k,{title:"总积分发放",value:s.stats.totalPoints},null,8,["value"])])),_:1}),r(p,{span:8},{default:o((()=>[r(k,{title:"活跃规则数",value:s.stats.activeRules},null,8,["value"])])),_:1})])),_:1}),r(h),a[26]||(a[26]=d("h3",null,"最近活动",-1)),r(R,{data:s.recentActivities,style:{width:"100%"}},{default:o((()=>[r(g,{prop:"userName",label:"用户",width:"120"}),r(g,{prop:"eventType",label:"事件类型",width:"120"}),r(g,{prop:"description",label:"描述"}),r(g,{prop:"points",label:"积分",width:"80"}),r(g,{prop:"timestamp",label:"时间",width:"160"})])),_:1},8,["data"])])])),_:1}),r(F,{label:"规则管理",name:"rules"},{default:o((()=>[d("div",N,[d("div",P,[r(U,{type:"primary",onClick:s.showAddRuleDialog},{default:o((()=>[r(T,null,{default:o((()=>[r(C)])),_:1}),a[27]||(a[27]=w(" 添加规则 "))])),_:1},8,["onClick"]),r(U,{onClick:s.reloadRules},{default:o((()=>[r(T,null,{default:o((()=>[r(I)])),_:1}),a[28]||(a[28]=w(" 重新加载缓存 "))])),_:1},8,["onClick"])]),v((b(),_(R,{data:s.rules,style:{width:"100%"}},{default:o((()=>[r(g,{prop:"id",label:"规则ID",width:"200"}),r(g,{prop:"name",label:"规则名称"}),r(g,{prop:"eventType",label:"事件类型",width:"120"}),r(g,{label:"奖励",width:"200"},{default:o((e=>[d("div",j,[e.row.pointsReward?(b(),t("span",q,V(e.row.pointsReward)+"积分",1)):y("",!0),e.row.coinsReward?(b(),t("span",G,V(e.row.coinsReward)+"金币",1)):y("",!0),e.row.diamondsReward?(b(),t("span",z,V(e.row.diamondsReward)+"钻石",1)):y("",!0)])])),_:1}),r(g,{prop:"priority",label:"优先级",width:"80"}),r(g,{label:"状态",width:"80"},{default:o((e=>[r(A,{modelValue:e.row.isActive,"onUpdate:modelValue":a=>e.row.isActive=a,onChange:a=>s.toggleRuleActive(e.row)},null,8,["modelValue","onUpdate:modelValue","onChange"])])),_:1}),r(g,{label:"操作",width:"200"},{default:o((e=>[r(U,{size:"small",onClick:a=>s.editRule(e.row)},{default:o((()=>a[29]||(a[29]=[w("编辑")]))),_:2},1032,["onClick"]),r(U,{size:"small",type:"danger",onClick:a=>s.deleteRule(e.row)},{default:o((()=>a[30]||(a[30]=[w("删除")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"])),[[Z,s.rulesLoading]])])])),_:1}),r(F,{label:"排行榜",name:"leaderboard"},{default:o((()=>[d("div",E,[d("div",M,[r(S,{modelValue:s.leaderboardType,"onUpdate:modelValue":a[4]||(a[4]=e=>s.leaderboardType=e)},{default:o((()=>[r(D,{label:"weekly"},{default:o((()=>a[31]||(a[31]=[w("本周")]))),_:1}),r(D,{label:"monthly"},{default:o((()=>a[32]||(a[32]=[w("本月")]))),_:1}),r(D,{label:"all"},{default:o((()=>a[33]||(a[33]=[w("全部")]))),_:1})])),_:1},8,["modelValue"]),r(U,{onClick:s.updateLeaderboard},{default:o((()=>[r(T,null,{default:o((()=>[r(I)])),_:1}),a[34]||(a[34]=w(" 更新排行榜 "))])),_:1},8,["onClick"])]),r(R,{data:s.leaderboardData,style:{width:"100%"}},{default:o((()=>[r(g,{type:"index",label:"排名",width:"80"}),r(g,{prop:"userName",label:"用户名"}),r(g,{prop:"department",label:"部门"}),r(g,{prop:"pointsBalance",label:"积分",sortable:""}),r(g,{prop:"coinsBalance",label:"金币",sortable:""}),r(g,{prop:"completedTasksCount",label:"完成任务",sortable:""}),r(g,{prop:"currentLevel",label:"等级"})])),_:1},8,["data"])])])),_:1}),r(F,{label:"功能测试",name:"test"},{default:o((()=>[d("div",O,[r(B,{title:"测试功能",description:"以下功能仅用于测试游戏化系统，请谨慎使用",type:"warning","show-icon":"",closable:!1}),r(h),r(m,{gutter:20},{default:o((()=>[r(p,{span:12},{default:o((()=>[r(n,{header:"任务奖励测试"},{default:o((()=>[r(Q,{model:s.testForm,"label-width":"100px"},{default:o((()=>[r(J,{label:"任务ID"},{default:o((()=>[r($,{modelValue:s.testForm.taskId,"onUpdate:modelValue":a[5]||(a[5]=e=>s.testForm.taskId=e),placeholder:"输入任务ID"},null,8,["modelValue"])])),_:1}),r(J,{label:"任务名称"},{default:o((()=>[r($,{modelValue:s.testForm.taskName,"onUpdate:modelValue":a[6]||(a[6]=e=>s.testForm.taskName=e),placeholder:"输入任务名称"},null,8,["modelValue"])])),_:1}),r(J,{label:"任务类型"},{default:o((()=>[r(K,{modelValue:s.testForm.taskType,"onUpdate:modelValue":a[7]||(a[7]=e=>s.testForm.taskType=e)},{default:o((()=>[r(H,{label:"普通任务",value:"General"}),r(H,{label:"紧急任务",value:"Urgent"}),r(H,{label:"重要任务",value:"Important"})])),_:1},8,["modelValue"])])),_:1}),r(J,null,{default:o((()=>[r(U,{type:"primary",onClick:s.testTaskCreated},{default:o((()=>a[35]||(a[35]=[w("测试任务创建奖励")]))),_:1},8,["onClick"]),r(U,{type:"success",onClick:s.testTaskCompleted},{default:o((()=>a[36]||(a[36]=[w("测试任务完成奖励")]))),_:1},8,["onClick"])])),_:1})])),_:1},8,["model"])])),_:1})])),_:1}),r(p,{span:12},{default:o((()=>[r(n,{header:"统计更新测试"},{default:o((()=>[r(Q,{"label-width":"100px"},{default:o((()=>[r(J,{label:"用户ID"},{default:o((()=>[r($,{modelValue:s.testForm.userId,"onUpdate:modelValue":a[8]||(a[8]=e=>s.testForm.userId=e),placeholder:"留空为当前用户"},null,8,["modelValue"])])),_:1}),r(J,null,{default:o((()=>[r(U,{onClick:s.testUpdateStats},{default:o((()=>a[37]||(a[37]=[w("更新用户统计")]))),_:1},8,["onClick"]),r(U,{onClick:s.testUpdateLeaderboard},{default:o((()=>a[38]||(a[38]=[w("更新排行榜")]))),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1})])),_:1})])),_:1})])])),_:1})])),_:1},8,["modelValue"])])),_:1}),r(Y,{title:s.ruleDialogTitle,modelValue:s.ruleDialogVisible,"onUpdate:modelValue":a[21]||(a[21]=e=>s.ruleDialogVisible=e),width:"600px"},{footer:o((()=>[r(U,{onClick:a[20]||(a[20]=e=>s.ruleDialogVisible=!1)},{default:o((()=>a[39]||(a[39]=[w("取消")]))),_:1}),r(U,{type:"primary",onClick:s.saveRule},{default:o((()=>a[40]||(a[40]=[w("保存")]))),_:1},8,["onClick"])])),default:o((()=>[r(Q,{model:s.ruleForm,rules:s.ruleFormRules,ref:"ruleFormRef","label-width":"120px"},{default:o((()=>[r(J,{label:"规则ID",prop:"id"},{default:o((()=>[r($,{modelValue:s.ruleForm.id,"onUpdate:modelValue":a[10]||(a[10]=e=>s.ruleForm.id=e),disabled:s.isEditMode},null,8,["modelValue","disabled"])])),_:1}),r(J,{label:"规则名称",prop:"name"},{default:o((()=>[r($,{modelValue:s.ruleForm.name,"onUpdate:modelValue":a[11]||(a[11]=e=>s.ruleForm.name=e)},null,8,["modelValue"])])),_:1}),r(J,{label:"事件类型",prop:"eventType"},{default:o((()=>[r(K,{modelValue:s.ruleForm.eventType,"onUpdate:modelValue":a[12]||(a[12]=e=>s.ruleForm.eventType=e),style:{width:"100%"}},{default:o((()=>[r(H,{label:"任务创建",value:"TaskCreated"}),r(H,{label:"任务完成",value:"TaskCompleted"}),r(H,{label:"任务认领",value:"TaskClaimed"}),r(H,{label:"资产更新",value:"AssetUpdated"}),r(H,{label:"故障报告",value:"FaultReported"}),r(H,{label:"每日登录",value:"DailyLogin"}),r(H,{label:"评论添加",value:"CommentAdded"})])),_:1},8,["modelValue"])])),_:1}),r(J,{label:"积分奖励",prop:"pointsReward"},{default:o((()=>[r(X,{modelValue:s.ruleForm.pointsReward,"onUpdate:modelValue":a[13]||(a[13]=e=>s.ruleForm.pointsReward=e),min:0},null,8,["modelValue"])])),_:1}),r(J,{label:"金币奖励",prop:"coinsReward"},{default:o((()=>[r(X,{modelValue:s.ruleForm.coinsReward,"onUpdate:modelValue":a[14]||(a[14]=e=>s.ruleForm.coinsReward=e),min:0},null,8,["modelValue"])])),_:1}),r(J,{label:"钻石奖励",prop:"diamondsReward"},{default:o((()=>[r(X,{modelValue:s.ruleForm.diamondsReward,"onUpdate:modelValue":a[15]||(a[15]=e=>s.ruleForm.diamondsReward=e),min:0},null,8,["modelValue"])])),_:1}),r(J,{label:"经验奖励",prop:"experienceReward"},{default:o((()=>[r(X,{modelValue:s.ruleForm.experienceReward,"onUpdate:modelValue":a[16]||(a[16]=e=>s.ruleForm.experienceReward=e),min:0},null,8,["modelValue"])])),_:1}),r(J,{label:"优先级",prop:"priority"},{default:o((()=>[r(X,{modelValue:s.ruleForm.priority,"onUpdate:modelValue":a[17]||(a[17]=e=>s.ruleForm.priority=e),min:1,max:999},null,8,["modelValue"])])),_:1}),r(J,{label:"条件表达式"},{default:o((()=>[r($,{modelValue:s.ruleForm.condition,"onUpdate:modelValue":a[18]||(a[18]=e=>s.ruleForm.condition=e),type:"textarea",placeholder:'JSON格式，例如: {"TaskType": "Important"}'},null,8,["modelValue"])])),_:1}),r(J,{label:"是否启用"},{default:o((()=>[r(A,{modelValue:s.ruleForm.isActive,"onUpdate:modelValue":a[19]||(a[19]=e=>s.ruleForm.isActive=e)},null,8,["modelValue"])])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["title","modelValue"])])}],["__scopeId","data-v-757972cd"]]);export{S as default};
