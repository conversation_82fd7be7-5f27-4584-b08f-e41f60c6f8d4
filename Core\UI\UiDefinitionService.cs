// IT资产管理系统 - UI定义服务实现
// 文件路径: /Core/UI/UiDefinitionService.cs
// 功能: 实现JSON驱动UI的服务

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using ItAssetsSystem.Core.Helpers;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace ItAssetsSystem.Core.UI
{
    /// <summary>
    /// UI定义服务实现
    /// </summary>
    public class UiDefinitionService : IUiDefinitionService
    {
        private readonly ILogger<UiDefinitionService> _logger;
        private readonly IConfiguration _configuration;
        private readonly string _definitionsPath;
        private readonly string _systemDefinitionsPath;
        private readonly JsonSerializerOptions _jsonOptions;

        /// <summary>
        /// 构造函数
        /// </summary>
        public UiDefinitionService(
            ILogger<UiDefinitionService> logger,
            IConfiguration configuration)
        {
            _logger = logger;
            _configuration = configuration;
            
            // 获取UI定义存储路径
            _definitionsPath = _configuration.GetValue<string>("UI:DefinitionsPath");
            if (string.IsNullOrEmpty(_definitionsPath))
            {
                _definitionsPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data", "UI");
            }
            
            // 获取系统UI定义路径
            _systemDefinitionsPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data", "UI", "System");
            
            // 确保目录存在
            FileHelper.EnsureDirectoryExists(_definitionsPath, _logger);
            FileHelper.EnsureDirectoryExists(_systemDefinitionsPath, _logger);
            
            // 初始化JSON序列化选项
            _jsonOptions = new JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            };
        }

        /// <summary>
        /// 获取UI定义
        /// </summary>
        public async Task<UiDefinition> GetUiDefinitionAsync(string definitionId)
        {
            _logger.LogInformation("获取UI定义: {DefinitionId}", definitionId);
            
            try
            {
                // 尝试从自定义定义中查找
                string customPath = Path.Combine(_definitionsPath, $"{definitionId}.json");
                if (File.Exists(customPath))
                {
                    string json = await File.ReadAllTextAsync(customPath);
                    return JsonSerializer.Deserialize<UiDefinition>(json, _jsonOptions);
                }
                
                // 尝试从系统定义中查找
                string systemPath = Path.Combine(_systemDefinitionsPath, $"{definitionId}.json");
                if (File.Exists(systemPath))
                {
                    string json = await File.ReadAllTextAsync(systemPath);
                    return JsonSerializer.Deserialize<UiDefinition>(json, _jsonOptions);
                }
                
                _logger.LogWarning("未找到UI定义: {DefinitionId}", definitionId);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取UI定义失败: {DefinitionId}", definitionId);
                return null;
            }
        }

        /// <summary>
        /// 保存UI定义
        /// </summary>
        public async Task<bool> SaveUiDefinitionAsync(UiDefinition definition)
        {
            if (definition == null)
            {
                throw new ArgumentNullException(nameof(definition));
            }
            
            if (string.IsNullOrEmpty(definition.Id))
            {
                definition.Id = Guid.NewGuid().ToString("N");
            }
            
            // 更新修改时间
            definition.LastModified = DateTime.Now;
            
            _logger.LogInformation("保存UI定义: {DefinitionId}, {Name}", definition.Id, definition.Name);
            
            try
            {
                // 选择保存路径
                string savePath = definition.IsSystem
                    ? Path.Combine(_systemDefinitionsPath, $"{definition.Id}.json")
                    : Path.Combine(_definitionsPath, $"{definition.Id}.json");
                
                // 序列化为JSON并保存
                string json = JsonSerializer.Serialize(definition, _jsonOptions);
                await File.WriteAllTextAsync(savePath, json);
                
                _logger.LogInformation("已保存UI定义: {DefinitionId}", definition.Id);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存UI定义失败: {DefinitionId}", definition.Id);
                return false;
            }
        }

        /// <summary>
        /// 获取所有UI定义
        /// </summary>
        public async Task<List<UiDefinition>> GetAllUiDefinitionsAsync()
        {
            _logger.LogInformation("获取所有UI定义");
            
            try
            {
                var definitions = new List<UiDefinition>();
                
                // 获取系统内置定义
                await LoadDefinitionsFromDirectoryAsync(_systemDefinitionsPath, definitions);
                
                // 获取自定义定义
                await LoadDefinitionsFromDirectoryAsync(_definitionsPath, definitions);
                
                // 按名称排序
                return definitions.OrderBy(d => d.Name).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取所有UI定义失败");
                return new List<UiDefinition>();
            }
        }

        /// <summary>
        /// 删除UI定义
        /// </summary>
        public async Task<bool> DeleteUiDefinitionAsync(string definitionId)
        {
            _logger.LogInformation("删除UI定义: {DefinitionId}", definitionId);
            
            try
            {
                // 获取定义
                var definition = await GetUiDefinitionAsync(definitionId);
                if (definition == null)
                {
                    _logger.LogWarning("未找到UI定义: {DefinitionId}", definitionId);
                    return false;
                }
                
                // 不允许删除系统内置定义
                if (definition.IsSystem)
                {
                    _logger.LogWarning("不能删除系统内置UI定义: {DefinitionId}", definitionId);
                    return false;
                }
                
                // 删除定义文件
                string filePath = Path.Combine(_definitionsPath, $"{definitionId}.json");
                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                    _logger.LogInformation("已删除UI定义: {DefinitionId}", definitionId);
                    return true;
                }
                
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除UI定义失败: {DefinitionId}", definitionId);
                return false;
            }
        }

        /// <summary>
        /// 加载系统内置UI定义
        /// </summary>
        public async Task<bool> LoadSystemUiDefinitionsAsync()
        {
            _logger.LogInformation("加载系统内置UI定义");
            
            try
            {
                // 创建常用系统定义
                await CreateOrUpdateAssetFormDefinitionAsync();
                await CreateOrUpdateAssetTableDefinitionAsync();
                await CreateOrUpdateFaultFormDefinitionAsync();
                await CreateOrUpdateDashboardDefinitionAsync();
                
                _logger.LogInformation("系统内置UI定义加载完成");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加载系统内置UI定义失败");
                return false;
            }
        }

        /// <summary>
        /// 从目录加载定义
        /// </summary>
        private async Task LoadDefinitionsFromDirectoryAsync(string directory, List<UiDefinition> definitions)
        {
            if (!Directory.Exists(directory))
            {
                return;
            }
            
            // 获取所有JSON文件
            string[] files = Directory.GetFiles(directory, "*.json");
            
            foreach (string file in files)
            {
                try
                {
                    string json = await File.ReadAllTextAsync(file);
                    var definition = JsonSerializer.Deserialize<UiDefinition>(json, _jsonOptions);
                    
                    if (definition != null)
                    {
                        // 检查是否已存在相同ID的定义，如存在则替换
                        int existingIndex = definitions.FindIndex(d => d.Id == definition.Id);
                        if (existingIndex >= 0)
                        {
                            definitions[existingIndex] = definition;
                        }
                        else
                        {
                            definitions.Add(definition);
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "加载UI定义文件失败: {FilePath}", file);
                }
            }
        }

        /// <summary>
        /// 创建或更新资产表单定义
        /// </summary>
        private async Task CreateOrUpdateAssetFormDefinitionAsync()
        {
            var definition = new UiDefinition
            {
                Id = "system_asset_form",
                Name = "资产表单",
                Description = "资产创建和编辑表单",
                Type = UiElementType.Form,
                IsSystem = true,
                LastModified = DateTime.Now,
                EntityType = "Asset",
                ApiEndpoint = "/api/assets",
                Content = JsonSerializer.Serialize(new
                {
                    component = "form",
                    fields = new object[]
                    {
                        new { name = "name", label = "资产名称", type = "text", required = true },
                        new { name = "assetTypeId", label = "资产类型", type = "select", required = true, dataSource = "/api/assettypes" },
                        new { name = "serialNumber", label = "序列号", type = "text", required = true },
                        new { name = "purchaseDate", label = "购买日期", type = "date" },
                        new { name = "price", label = "价格", type = "number" },
                        new { name = "status", label = "状态", type = "select", options = new object[] 
                        { 
                            new { value = "InUse", label = "使用中" },
                            new { value = "InStock", label = "库存中" },
                            new { value = "UnderMaintenance", label = "维护中" },
                            new { value = "Disposed", label = "已处置" }
                        }},
                        new { name = "locationId", label = "位置", type = "select", dataSource = "/api/locations" },
                        new { name = "description", label = "描述", type = "textarea" }
                    },
                    buttons = new object[]
                    {
                        new { text = "保存", action = "submit", type = "primary" },
                        new { text = "取消", action = "cancel" }
                    }
                }, _jsonOptions)
            };
            
            await SaveUiDefinitionAsync(definition);
        }

        /// <summary>
        /// 创建或更新资产表格定义
        /// </summary>
        private async Task CreateOrUpdateAssetTableDefinitionAsync()
        {
            var definition = new UiDefinition
            {
                Id = "system_asset_table",
                Name = "资产列表",
                Description = "资产列表表格",
                Type = UiElementType.Table,
                IsSystem = true,
                LastModified = DateTime.Now,
                EntityType = "Asset",
                ApiEndpoint = "/api/assets",
                Content = JsonSerializer.Serialize(new
                {
                    component = "table",
                    dataSource = "/api/assets",
                    pagination = true,
                    columns = new object[]
                    {
                        new { title = "名称", dataIndex = "name", sorter = true },
                        new { title = "资产类型", dataIndex = "assetTypeName", sorter = true },
                        new { title = "序列号", dataIndex = "serialNumber" },
                        new { title = "状态", dataIndex = "status", sorter = true },
                        new { title = "位置", dataIndex = "locationName", sorter = true },
                        new { title = "购买日期", dataIndex = "purchaseDate", type = "date", sorter = true },
                        new { title = "价格", dataIndex = "price", type = "currency", sorter = true }
                    },
                    search = new
                    {
                        enable = true,
                        placeholder = "搜索资产名称或序列号"
                    },
                    filters = new object[]
                    {
                        new { title = "资产类型", field = "assetTypeId", type = "select", dataSource = "/api/assettypes" },
                        new { title = "状态", field = "status", type = "select", options = new object[]
                        { 
                            new { value = "InUse", label = "使用中" },
                            new { value = "InStock", label = "库存中" },
                            new { value = "UnderMaintenance", label = "维护中" },
                            new { value = "Disposed", label = "已处置" }
                        }},
                        new { title = "位置", field = "locationId", type = "select", dataSource = "/api/locations" }
                    },
                    actions = new object[]
                    {
                        new { name = "view", text = "查看", type = "primary" },
                        new { name = "edit", text = "编辑", type = "default" },
                        new { name = "delete", text = "删除", type = "danger", confirm = "确定要删除该资产吗？" }
                    }
                }, _jsonOptions)
            };
            
            await SaveUiDefinitionAsync(definition);
        }

        /// <summary>
        /// 创建或更新故障表单定义
        /// </summary>
        private async Task CreateOrUpdateFaultFormDefinitionAsync()
        {
            var definition = new UiDefinition
            {
                Id = "system_fault_form",
                Name = "故障表单",
                Description = "故障创建和编辑表单",
                Type = UiElementType.Form,
                IsSystem = true,
                LastModified = DateTime.Now,
                EntityType = "FaultRecord",
                ApiEndpoint = "/api/faults",
                Content = JsonSerializer.Serialize(new
                {
                    component = "form",
                    fields = new object[]
                    {
                        new { name = "title", label = "故障标题", type = "text", required = true },
                        new { name = "assetId", label = "相关资产", type = "select", required = true, dataSource = "/api/assets" },
                        new { name = "faultTypeId", label = "故障类型", type = "select", required = true, dataSource = "/api/faulttypes" },
                        new { name = "description", label = "故障描述", type = "textarea", required = true },
                        new { name = "priority", label = "优先级", type = "select", options = new object[] 
                        { 
                            new { value = "Low", label = "低" },
                            new { value = "Medium", label = "中" },
                            new { value = "High", label = "高" },
                            new { value = "Critical", label = "紧急" }
                        }},
                        new { name = "status", label = "状态", type = "select", options = new object[] 
                        { 
                            new { value = "Open", label = "待处理" },
                            new { value = "InProgress", label = "处理中" },
                            new { value = "Resolved", label = "已解决" },
                            new { value = "Closed", label = "已关闭" }
                        }},
                        new { name = "assigneeId", label = "处理人", type = "select", dataSource = "/api/users" }
                    },
                    buttons = new object[]
                    {
                        new { text = "保存", action = "submit", type = "primary" },
                        new { text = "取消", action = "cancel" }
                    }
                }, _jsonOptions)
            };
            
            await SaveUiDefinitionAsync(definition);
        }

        /// <summary>
        /// 创建或更新仪表盘定义
        /// </summary>
        private async Task CreateOrUpdateDashboardDefinitionAsync()
        {
            var definition = new UiDefinition
            {
                Id = "system_dashboard",
                Name = "系统仪表盘",
                Description = "系统主仪表盘布局",
                Type = UiElementType.Custom,
                IsSystem = true,
                LastModified = DateTime.Now,
                ApiEndpoint = "/api/dashboard",
                Content = JsonSerializer.Serialize(new
                {
                    component = "dashboard",
                    layout = new object[]
                    {
                        new { i = "total_assets", x = 0, y = 0, w = 6, h = 4 },
                        new { i = "asset_status", x = 6, y = 0, w = 6, h = 4 },
                        new { i = "recent_faults", x = 0, y = 4, w = 12, h = 6 },
                        new { i = "asset_distribution", x = 0, y = 10, w = 12, h = 8 }
                    },
                    widgets = new object[]
                    {
                        new { 
                            id = "total_assets", 
                            title = "资产总览", 
                            type = "stat", 
                            dataSource = "/api/analytics/dashboard/summary",
                            refreshInterval = 60
                        },
                        new { 
                            id = "asset_status", 
                            title = "资产状态", 
                            type = "pie", 
                            dataSource = "/api/analytics/dashboard/asset-status",
                            refreshInterval = 60
                        },
                        new { 
                            id = "recent_faults", 
                            title = "最近故障", 
                            type = "table", 
                            dataSource = "/api/analytics/dashboard/recent-faults",
                            refreshInterval = 60,
                            columns = new object[]
                            {
                                new { title = "故障", dataIndex = "title" },
                                new { title = "资产", dataIndex = "assetName" },
                                new { title = "优先级", dataIndex = "priority" },
                                new { title = "状态", dataIndex = "status" },
                                new { title = "报告时间", dataIndex = "reportedAt", type = "date" }
                            }
                        },
                        new { 
                            id = "asset_distribution", 
                            title = "资产分布", 
                            type = "bar", 
                            dataSource = "/api/analytics/dashboard/asset-distribution",
                            refreshInterval = 300
                        }
                    }
                }, _jsonOptions)
            };
            
            await SaveUiDefinitionAsync(definition);
        }
    }
} 