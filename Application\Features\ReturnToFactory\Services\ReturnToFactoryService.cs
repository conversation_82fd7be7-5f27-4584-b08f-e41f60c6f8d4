// File: Application/Features/ReturnToFactory/Services/ReturnToFactoryService.cs
// Description: 返厂维修服务 - 处理返厂维修相关业务逻辑

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using ItAssetsSystem.Infrastructure.Data;
using ItAssetsSystem.Models.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace ItAssetsSystem.Application.Features.ReturnToFactory.Services
{
    /// <summary>
    /// 返厂维修服务
    /// </summary>
    public class ReturnToFactoryService
    {
        private readonly AppDbContext _context;
        private readonly ILogger<ReturnToFactoryService> _logger;

        public ReturnToFactoryService(AppDbContext context, ILogger<ReturnToFactoryService> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// 获取所有返厂记录
        /// </summary>
        /// <returns>返厂记录列表</returns>
        public async Task<List<object>> GetAllReturnToFactoryRecordsAsync()
        {
            try
            {
                _logger.LogInformation("开始获取所有返厂记录");

                // 使用LINQ查询而不是原生SQL
                var returnToFactoryRecords = await _context.Set<ItAssetsSystem.Models.Entities.ReturnToFactory>()
                    .OrderByDescending(rtf => rtf.CreatedAt)
                    .ToListAsync();

                var returnRecords = returnToFactoryRecords.Select(rtf => new
                {
                    id = rtf.Id,
                    code = rtf.Code ?? ("RTF-" + rtf.Id.ToString().PadLeft(6, '0')), // 使用数据库中的Code字段
                    assetId = rtf.AssetId,
                    assetName = "资产-" + rtf.AssetId, // 临时显示
                    faultId = rtf.FaultRecordId,
                    faultTitle = "故障记录", // 临时显示
                    supplierId = rtf.SupplierId,
                    supplierName = "供应商-" + rtf.SupplierId, // 临时显示
                    status = rtf.Status,
                    statusName = GetStatusName(rtf.Status),
                    senderId = rtf.SenderId,
                    senderName = "用户-" + rtf.SenderId, // 临时显示
                    sendTime = rtf.SendTime,
                    estimatedReturnTime = rtf.EstimatedReturnTime,
                    actualReturnTime = rtf.ActualReturnTime,
                    repairResult = rtf.RepairResult,
                    repairCost = rtf.RepairCost,
                    inWarranty = rtf.InWarranty,
                    notes = rtf.Notes,
                    createdAt = rtf.CreatedAt,
                    updatedAt = rtf.UpdatedAt
                }).Cast<object>().ToList();

                _logger.LogInformation($"成功获取 {returnRecords.Count} 条返厂记录");
                return returnRecords;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取返厂记录列表失败");
                throw;
            }
        }

        /// <summary>
        /// 根据ID获取返厂记录详情
        /// </summary>
        /// <param name="id">返厂记录ID</param>
        /// <returns>返厂记录详情</returns>
        public async Task<object> GetReturnToFactoryByIdAsync(int id)
        {
            try
            {
                _logger.LogInformation($"开始获取返厂记录详情，ID: {id}");

                var rtf = await _context.Set<ItAssetsSystem.Models.Entities.ReturnToFactory>()
                    .Where(r => r.Id == id)
                    .FirstOrDefaultAsync();

                if (rtf == null)
                {
                    _logger.LogWarning($"未找到ID为 {id} 的返厂记录");
                    return null;
                }

                var returnRecord = new
                {
                    id = rtf.Id,
                    code = rtf.Code ?? ("RTF-" + rtf.Id.ToString().PadLeft(6, '0')),
                    assetId = rtf.AssetId,
                    assetName = "资产-" + rtf.AssetId,
                    assetCode = "ASSET-" + rtf.AssetId.ToString().PadLeft(6, '0'),
                    faultId = rtf.FaultRecordId,
                    faultTitle = "故障记录",
                    faultDescription = "故障描述信息",
                    supplierId = rtf.SupplierId,
                    supplierName = "供应商-" + rtf.SupplierId,
                    supplierContact = "联系人",
                    supplierPhone = "联系电话",

                    status = rtf.Status,
                    statusName = GetStatusName(rtf.Status),
                    senderId = rtf.SenderId,
                    senderName = "用户-" + rtf.SenderId,
                    sendTime = rtf.SendTime,
                    estimatedReturnTime = rtf.EstimatedReturnTime,
                    actualReturnTime = rtf.ActualReturnTime,
                    repairResult = rtf.RepairResult,
                    repairCost = rtf.RepairCost,
                    inWarranty = rtf.InWarranty,
                    notes = rtf.Notes,
                    createdAt = rtf.CreatedAt,
                    updatedAt = rtf.UpdatedAt
                };

                _logger.LogInformation($"成功获取返厂记录详情，ID: {id}");
                return returnRecord;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取返厂记录详情失败，ID: {id}");
                throw;
            }
        }

        /// <summary>
        /// 获取状态名称
        /// </summary>
        /// <param name="status">状态值</param>
        /// <returns>状态名称</returns>
        public static string GetStatusName(int status)
        {
            return status switch
            {
                0 => "待送出",
                1 => "已送出",
                2 => "维修中",
                3 => "已返回",
                4 => "维修失败",
                _ => "未知状态"
            };
        }
    }


}
