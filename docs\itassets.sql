/*
 Navicat Premium Data Transfer

 Source Server         : IT资产
 Source Server Type    : MySQL
 Source Server Version : 80029 (8.0.29)
 Source Host           : localhost:3306
 Source Schema         : itassets

 Target Server Type    : MySQL
 Target Server Version : 80029 (8.0.29)
 File Encoding         : 65001

 Date: 30/03/2025 12:22:34
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for __efmigrationshistory
-- ----------------------------
DROP TABLE IF EXISTS `__efmigrationshistory`;
CREATE TABLE `__efmigrationshistory`  (
  `MigrationId` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `ProductVersion` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  PRIMARY KEY (`MigrationId`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'EF迁移历史表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of __efmigrationshistory
-- ----------------------------

-- ----------------------------
-- Table structure for assethistories
-- ----------------------------
DROP TABLE IF EXISTS `assethistories`;
CREATE TABLE `assethistories`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `AssetId` int NOT NULL COMMENT '资产ID',
  `OperationType` int NOT NULL COMMENT '操作类型：1创建，2修改，3删除，4位置变更，5状态变更',
  `OperatorId` int NOT NULL COMMENT '操作人ID',
  `OperationTime` datetime NOT NULL COMMENT '操作时间',
  `Description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '描述（JSON格式，记录变更前后的属性值）',
  `CreatedAt` datetime NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`) USING BTREE,
  INDEX `IX_AssetHistories_AssetId`(`AssetId` ASC) USING BTREE,
  INDEX `IX_AssetHistories_OperatorId`(`OperatorId` ASC) USING BTREE,
  CONSTRAINT `FK_AssetHistories_Assets_AssetId` FOREIGN KEY (`AssetId`) REFERENCES `assets` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_AssetHistories_Users_OperatorId` FOREIGN KEY (`OperatorId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '资产历史表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of assethistories
-- ----------------------------
INSERT INTO `assethistories` VALUES (1, 3, 2, 1, '2025-03-26 20:56:43', '品牌: 组装 → 研华', '2025-03-26 20:56:43', '2025-03-26 20:56:43');
INSERT INTO `assethistories` VALUES (2, 3, 2, 1, '2025-03-26 21:54:02', '{\"locationId\":null}', '2025-03-26 21:54:02', '2025-03-26 21:54:02');
INSERT INTO `assethistories` VALUES (3, 3, 2, 1, '2025-03-26 21:57:14', '{\"locationId\":null}', '2025-03-26 21:57:14', '2025-03-26 21:57:14');

-- ----------------------------
-- Table structure for assetreceives
-- ----------------------------
DROP TABLE IF EXISTS `assetreceives`;
CREATE TABLE `assetreceives`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `ReceiveCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '入库单号',
  `PurchaseOrderId` int NULL DEFAULT NULL COMMENT '采购订单ID',
  `Title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '标题',
  `Description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '描述',
  `ReceiverId` int NOT NULL COMMENT '接收人ID',
  `ReceiveTime` datetime NOT NULL COMMENT '接收时间',
  `Status` int NOT NULL DEFAULT 0 COMMENT '状态：0草稿，1已提交，2已确认',
  `AssetTypeId` int NULL DEFAULT NULL COMMENT '资产类型ID',
  `Quantity` int NOT NULL DEFAULT 1 COMMENT '数量',
  `TotalAmount` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '总金额',
  `InitialLocationId` int NULL DEFAULT NULL COMMENT '初始位置ID',
  `Notes` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `CreatedAt` datetime NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE INDEX `IX_AssetReceives_ReceiveCode`(`ReceiveCode` ASC) USING BTREE,
  INDEX `IX_AssetReceives_PurchaseOrderId`(`PurchaseOrderId` ASC) USING BTREE,
  INDEX `IX_AssetReceives_ReceiverId`(`ReceiverId` ASC) USING BTREE,
  INDEX `IX_AssetReceives_AssetTypeId`(`AssetTypeId` ASC) USING BTREE,
  INDEX `IX_AssetReceives_InitialLocationId`(`InitialLocationId` ASC) USING BTREE,
  CONSTRAINT `FK_AssetReceives_AssetTypes_AssetTypeId` FOREIGN KEY (`AssetTypeId`) REFERENCES `assettypes` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_AssetReceives_Locations_InitialLocationId` FOREIGN KEY (`InitialLocationId`) REFERENCES `locations` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_AssetReceives_PurchaseOrders_PurchaseOrderId` FOREIGN KEY (`PurchaseOrderId`) REFERENCES `purchaseorders` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_AssetReceives_Users_ReceiverId` FOREIGN KEY (`ReceiverId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '资产入库表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of assetreceives
-- ----------------------------

-- ----------------------------
-- Table structure for assets
-- ----------------------------
DROP TABLE IF EXISTS `assets`;
CREATE TABLE `assets`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `AssetCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '资产编码',
  `FinancialCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '财务编号',
  `Name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '资产名称',
  `AssetTypeId` int NOT NULL COMMENT '资产类型ID',
  `SerialNumber` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '序列号',
  `Model` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '型号',
  `Brand` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '品牌',
  `PurchaseDate` datetime NULL DEFAULT NULL COMMENT '购买日期',
  `WarrantyExpireDate` datetime NULL DEFAULT NULL COMMENT '保修到期日',
  `Price` decimal(18, 2) NULL DEFAULT NULL COMMENT '价格',
  `LocationId` int NULL DEFAULT NULL COMMENT '位置ID',
  `DepartmentId` int NULL DEFAULT NULL,
  `Status` int NOT NULL DEFAULT 0 COMMENT '状态：0闲置，1在用，2维修中，3报废',
  `Notes` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `CreatedAt` datetime NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE INDEX `IX_Assets_AssetCode`(`AssetCode` ASC) USING BTREE,
  INDEX `IX_Assets_AssetTypeId`(`AssetTypeId` ASC) USING BTREE,
  INDEX `IX_Assets_LocationId`(`LocationId` ASC) USING BTREE,
  INDEX `IX_Assets_FinancialCode`(`FinancialCode` ASC) USING BTREE,
  INDEX `IX_Assets_DepartmentId`(`DepartmentId` ASC) USING BTREE,
  CONSTRAINT `FK_Assets_AssetTypes_AssetTypeId` FOREIGN KEY (`AssetTypeId`) REFERENCES `assettypes` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_Assets_Departments_DepartmentId` FOREIGN KEY (`DepartmentId`) REFERENCES `departments` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_Assets_Locations_LocationId` FOREIGN KEY (`LocationId`) REFERENCES `locations` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 33 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '资产表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of assets
-- ----------------------------
INSERT INTO `assets` VALUES (3, 'IPC2023003', NULL, 'C2产线工控机01', 7, NULL, 'IPC-610H', '组装', NULL, NULL, NULL, 32, NULL, 1, NULL, '2025-03-23 20:45:09', '2025-03-26 21:57:14');
INSERT INTO `assets` VALUES (4, 'IPC2023004', NULL, 'C1工序控制机01', 7, 'SN2023IPC004', 'IPC-610H', '研华', '2023-01-20 00:00:00', '2026-01-19 00:00:00', 12800.00, 30, 12, 1, '用于C1工序质量控制', '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `assets` VALUES (6, 'PC2023001', NULL, 'IT部门台式机01', 8, 'SN2023PC001', 'ThinkCentre M720q', '联想', '2023-02-10 00:00:00', '2026-02-09 00:00:00', 5800.00, 6, NULL, 1, 'IT部门日常办公使用', '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `assets` VALUES (7, 'PC2023002', NULL, 'IT部门台式机02', 8, 'SN2023PC002', 'ThinkCentre M720q', '联想', '2023-02-10 00:00:00', '2026-02-09 00:00:00', 5800.00, 6, NULL, 1, 'IT部门开发使用', '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `assets` VALUES (8, 'PC2023003', NULL, '行政部台式机01', 8, 'SN2023PC003', 'OptiPlex 7080', '戴尔', '2023-02-15 00:00:00', '2026-02-14 00:00:00', 6200.00, 6, NULL, 1, '行政部日常办公使用', '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `assets` VALUES (9, 'PC2023004', NULL, '财务部台式机01', 8, 'SN2023PC004', 'OptiPlex 7080', '戴尔', '2023-02-15 00:00:00', '2026-02-14 00:00:00', 6200.00, 9, NULL, 1, '财务部日常办公使用', '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `assets` VALUES (10, 'PC2023005', NULL, '生产部台式机01', 8, 'SN2023PC005', 'ThinkCentre M720q', '联想', '2023-02-20 00:00:00', '2026-02-19 00:00:00', 5800.00, 9, NULL, 1, '生产部日常办公使用', '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `assets` VALUES (11, 'LP2023001', NULL, 'IT主管笔记本01', 9, 'SN2023LP001', 'ThinkPad X1 Carbon', '联想', '2023-03-05 00:00:00', '2026-03-04 00:00:00', 12500.00, 6, NULL, 1, 'IT主管移动办公使用', '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `assets` VALUES (12, 'LP2023002', NULL, '销售经理笔记本01', 9, 'SN2023LP002', 'XPS 13', '戴尔', '2023-03-10 00:00:00', '2026-03-09 00:00:00', 13200.00, 6, NULL, 1, '销售经理客户拜访使用', '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `assets` VALUES (13, 'LP2023003', NULL, '总经理笔记本01', 9, 'SN2023LP003', 'ThinkPad X1 Carbon', '联想', '2023-03-15 00:00:00', '2026-03-14 00:00:00', 14500.00, 9, NULL, 1, '总经理移动办公使用', '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `assets` VALUES (14, 'PR2023001', NULL, '行政部激光打印机01', 14, 'SN2023PR001', 'LaserJet Pro M404dn', '惠普', '2023-04-10 00:00:00', '2026-04-09 00:00:00', 2800.00, 6, NULL, 1, '行政部文档打印使用', '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `assets` VALUES (15, 'PR2023002', NULL, 'C1产线标签打印机01', 15, 'SN2023PR002', 'QL-820NWB', '兄弟', '2023-04-15 00:00:00', '2026-04-14 00:00:00', 1800.00, 27, 10, 1, 'C1产线标签打印使用', '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `assets` VALUES (16, 'PR2023003', NULL, 'C2产线标签打印机01', 15, 'SN2023PR003', 'QL-820NWB', '兄弟', '2023-04-20 00:00:00', '2026-04-19 00:00:00', 1800.00, 31, 13, 1, 'C2产线标签打印使用', '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `assets` VALUES (17, 'SC2023001', NULL, 'C1产线条码扫描枪01', 17, 'SN2023SC001', 'DS4308', '讯宝', '2023-05-05 00:00:00', '2026-05-04 00:00:00', 1200.00, 27, 10, 1, 'C1产线物料扫描使用', '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `assets` VALUES (18, 'SC2023002', NULL, 'C1产线条码扫描枪02', 17, 'SN2023SC002', 'DS4308', '讯宝', '2023-05-05 00:00:00', '2026-05-04 00:00:00', 1200.00, 28, 10, 1, 'C1产线物料扫描使用', '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `assets` VALUES (19, 'SC2023003', NULL, 'C2产线条码扫描枪01', 17, 'SN2023SC003', 'DS4308', '讯宝', '2023-05-10 00:00:00', '2026-05-09 00:00:00', 1200.00, 31, 13, 1, 'C2产线物料扫描使用', '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `assets` VALUES (20, 'SC2023004', NULL, '仓库二维码扫描器01', 18, 'SN2023SC004', 'DS9908', '讯宝', '2023-05-15 00:00:00', '2026-05-14 00:00:00', 1800.00, 6, NULL, 1, '仓库物料盘点使用', '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `assets` VALUES (21, 'MB2023001', NULL, 'C1产线PDA01', 19, 'SN2023MB001', 'TC520K', '讯宝', '2023-06-05 00:00:00', '2026-06-04 00:00:00', 6800.00, 27, 10, 1, 'C1产线移动数据采集使用', '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `assets` VALUES (22, 'MB2023002', NULL, 'C1产线PDA02', 19, 'SN2023MB002', 'TC520K', '讯宝', '2023-06-05 00:00:00', '2026-06-04 00:00:00', 6800.00, 28, 10, 1, 'C1产线移动数据采集使用', '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `assets` VALUES (23, 'MB2023003', NULL, 'C2产线PDA01', 19, 'SN2023MB003', 'TC520K', '讯宝', '2023-06-10 00:00:00', '2026-06-09 00:00:00', 6800.00, 31, 13, 1, 'C2产线移动数据采集使用', '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `assets` VALUES (24, 'MB2023004', NULL, '质检部平板电脑01', 1, 'SN2023MB004', 'iPad Pro', '苹果', '2023-06-15 00:00:00', '2026-06-14 00:00:00', 7200.00, 30, 12, 1, '质检部移动办公使用', '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `assets` VALUES (26, 'NW2023001', NULL, 'C1产线交换机01', 11, 'SN2023NW001', 'S5700-28C-EI', '华为', '2023-07-05 00:00:00', '2026-07-04 00:00:00', 4800.00, 27, 10, 1, 'C1产线网络互联使用', '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `assets` VALUES (27, 'NW2023002', NULL, 'C2产线交换机01', 11, 'SN2023NW002', 'S5700-28C-EI', '华为', '2023-07-10 00:00:00', '2026-07-09 00:00:00', 4800.00, 31, 13, 1, 'C2产线网络互联使用', '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `assets` VALUES (28, 'NW2023003', NULL, '办公区路由器01', 12, 'SN2023NW003', 'AR2220E', '华为', '2023-07-15 00:00:00', '2026-07-14 00:00:00', 8500.00, 33, NULL, 1, '办公区网络路由使用', '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `assets` VALUES (29, 'NW2023004', NULL, '工厂防火墙01', 13, 'SN2023NW004', 'USG6630E', '华为', '2023-07-20 00:00:00', '2026-07-19 00:00:00', 25000.00, 32, NULL, 1, '工厂网络安全防护使用', '2025-03-23 20:45:09', '2025-03-23 20:45:09');

-- ----------------------------
-- Table structure for assettypes
-- ----------------------------
DROP TABLE IF EXISTS `assettypes`;
CREATE TABLE `assettypes`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `Name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '类型名称',
  `Code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '类型编码',
  `Description` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '类型描述',
  `ParentId` int NULL DEFAULT NULL COMMENT '父类型ID',
  `RequireSerialNumber` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否需要序列号',
  `SortOrder` int NOT NULL DEFAULT 0 COMMENT '排序',
  `IsActive` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否激活',
  `CreatedAt` datetime NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE INDEX `IX_AssetTypes_Code`(`Code` ASC) USING BTREE,
  UNIQUE INDEX `IX_AssetTypes_Name`(`Name` ASC) USING BTREE,
  INDEX `IX_AssetTypes_ParentId`(`ParentId` ASC) USING BTREE,
  CONSTRAINT `FK_AssetTypes_AssetTypes_ParentId` FOREIGN KEY (`ParentId`) REFERENCES `assettypes` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 24 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '资产类型表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of assettypes
-- ----------------------------
INSERT INTO `assettypes` VALUES (1, '电脑设备', 'COMPUTER', '计算机类设备', NULL, 1, 10, 0, '2025-03-23 20:45:09', '2025-03-26 10:51:18');
INSERT INTO `assettypes` VALUES (2, '网络设备', 'NETWORK', '网络通信设备', NULL, 1, 20, 1, '2025-03-23 20:45:09', '2025-03-26 22:07:56');
INSERT INTO `assettypes` VALUES (3, '打印设备', 'PRINTER', '打印类设备', NULL, 1, 30, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `assettypes` VALUES (4, '扫描设备', 'SCANNER', '扫描类设备', NULL, 1, 40, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `assettypes` VALUES (5, '移动设备', 'MOBILE', '移动终端设备', NULL, 1, 50, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `assettypes` VALUES (6, '存储设备', 'STORAGE', '数据存储设备', NULL, 1, 60, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `assettypes` VALUES (7, '工控机', 'IPC', '工业控制计算机', 1, 1, 11, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `assettypes` VALUES (8, '台式电脑', 'DESKTOP', '普通台式电脑', 1, 1, 12, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `assettypes` VALUES (9, '笔记本电脑', 'LAPTOP', '笔记本电脑', 1, 1, 13, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `assettypes` VALUES (10, '服务器', 'SERVER', '服务器设备', 1, 1, 14, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `assettypes` VALUES (11, '交换机', 'SWITCH', '网络交换机', 2, 1, 21, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `assettypes` VALUES (12, '路由器', 'ROUTER', '网络路由器', 2, 1, 22, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `assettypes` VALUES (13, '防火墙', 'FIREWALL', '网络防火墙', 2, 1, 23, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `assettypes` VALUES (14, '激光打印机', 'LASER_PRINTER', '激光打印机', 3, 1, 31, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `assettypes` VALUES (15, '标签打印机', 'LABEL_PRINTER', '标签打印机', 3, 1, 32, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `assettypes` VALUES (16, '蓝牙打印机', 'BT_PRINTER', '蓝牙打印机', 3, 1, 33, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `assettypes` VALUES (17, '条码扫描枪', 'BARCODE_SCANNER', '条码扫描枪', 4, 1, 41, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `assettypes` VALUES (18, '扫码枪', 'QR_SCANNER', '扫码枪', 4, 1, 42, 1, '2025-03-23 20:45:09', '2025-03-26 22:02:16');
INSERT INTO `assettypes` VALUES (19, 'PDA', 'PDA', '掌上数据终端', 5, 1, 51, 1, '2025-03-23 20:45:09', '2025-03-26 22:10:39');
INSERT INTO `assettypes` VALUES (23, '测试', 'test', '测试', NULL, 1, 0, 1, '2025-03-26 22:18:20', '2025-03-26 22:18:20');

-- ----------------------------
-- Table structure for auditlogs
-- ----------------------------
DROP TABLE IF EXISTS `auditlogs`;
CREATE TABLE `auditlogs`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `UserId` int NULL DEFAULT NULL COMMENT '用户ID',
  `UserName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '用户名',
  `Type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '日志类型',
  `TableName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '表名',
  `DateTime` datetime NOT NULL COMMENT '日期时间',
  `PrimaryKey` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '主键',
  `OldValues` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '旧值',
  `NewValues` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '新值',
  `Action` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '操作',
  `ClientIP` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '客户端IP',
  PRIMARY KEY (`Id`) USING BTREE,
  INDEX `IX_AuditLogs_UserId`(`UserId` ASC) USING BTREE,
  CONSTRAINT `FK_AuditLogs_Users_UserId` FOREIGN KEY (`UserId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '审计日志表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of auditlogs
-- ----------------------------

-- ----------------------------
-- Table structure for db_structure_result
-- ----------------------------
DROP TABLE IF EXISTS `db_structure_result`;
CREATE TABLE `db_structure_result`  (
  `section` varchar(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '',
  `表名` varchar(64) CHARACTER SET utf8 COLLATE utf8_tolower_ci NULL DEFAULT NULL,
  `列名` varchar(64) CHARACTER SET utf8 COLLATE utf8_tolower_ci NULL DEFAULT NULL,
  `数据类型` mediumtext CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `可为空` varchar(3) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
  `默认值` text CHARACTER SET utf8 COLLATE utf8_bin NULL,
  `键` enum('','PRI','UNI','MUL') CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `额外信息` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `注释` text CHARACTER SET utf8 COLLATE utf8_bin NOT NULL
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of db_structure_result
-- ----------------------------
INSERT INTO `db_structure_result` VALUES ('列信息', '__efmigrationshistory', 'MigrationId', 'varchar(150)', 'NO', NULL, 'PRI', '', '');
INSERT INTO `db_structure_result` VALUES ('列信息', '__efmigrationshistory', 'ProductVersion', 'varchar(32)', 'NO', NULL, '', '', '');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assethistories', 'Id', 'int', 'NO', NULL, 'PRI', 'auto_increment', '');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assethistories', 'AssetId', 'int', 'NO', NULL, 'MUL', '', '资产ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assethistories', 'OperationType', 'int', 'NO', NULL, '', '', '操作类型：1创建，2修改，3删除，4位置变更，5状态变更');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assethistories', 'OperatorId', 'int', 'NO', NULL, 'MUL', '', '操作人ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assethistories', 'OperationTime', 'datetime', 'NO', NULL, '', '', '操作时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assethistories', 'Description', 'text', 'YES', NULL, '', '', '描述（JSON格式，记录变更前后的属性值）');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assethistories', 'CreatedAt', 'datetime', 'NO', NULL, '', '', '创建时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assethistories', 'UpdatedAt', 'datetime', 'NO', NULL, '', '', '更新时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assetreceives', 'Id', 'int', 'NO', NULL, 'PRI', 'auto_increment', '');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assetreceives', 'ReceiveCode', 'varchar(50)', 'NO', NULL, 'UNI', '', '入库单号');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assetreceives', 'PurchaseOrderId', 'int', 'YES', NULL, 'MUL', '', '采购订单ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assetreceives', 'Title', 'varchar(100)', 'NO', NULL, '', '', '标题');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assetreceives', 'Description', 'varchar(500)', 'YES', NULL, '', '', '描述');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assetreceives', 'ReceiverId', 'int', 'NO', NULL, 'MUL', '', '接收人ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assetreceives', 'ReceiveTime', 'datetime', 'NO', NULL, '', '', '接收时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assetreceives', 'Status', 'int', 'NO', '0', '', '', '状态：0草稿，1已提交，2已确认');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assetreceives', 'AssetTypeId', 'int', 'YES', NULL, 'MUL', '', '资产类型ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assetreceives', 'Quantity', 'int', 'NO', '1', '', '', '数量');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assetreceives', 'TotalAmount', 'decimal(18,2)', 'NO', '0.00', '', '', '总金额');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assetreceives', 'InitialLocationId', 'int', 'YES', NULL, 'MUL', '', '初始位置ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assetreceives', 'Notes', 'varchar(500)', 'YES', NULL, '', '', '备注');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assetreceives', 'CreatedAt', 'datetime', 'NO', NULL, '', '', '创建时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assetreceives', 'UpdatedAt', 'datetime', 'NO', NULL, '', '', '更新时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assets', 'Id', 'int', 'NO', NULL, 'PRI', 'auto_increment', '');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assets', 'AssetCode', 'varchar(50)', 'NO', NULL, 'UNI', '', '资产编码');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assets', 'FinancialCode', 'varchar(50)', 'YES', NULL, 'MUL', '', '财务编号');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assets', 'Name', 'varchar(100)', 'NO', NULL, '', '', '资产名称');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assets', 'AssetTypeId', 'int', 'NO', NULL, 'MUL', '', '资产类型ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assets', 'SerialNumber', 'varchar(100)', 'YES', NULL, '', '', '序列号');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assets', 'Model', 'varchar(100)', 'YES', NULL, '', '', '型号');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assets', 'Brand', 'varchar(100)', 'YES', NULL, '', '', '品牌');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assets', 'PurchaseDate', 'datetime', 'YES', NULL, '', '', '购买日期');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assets', 'WarrantyExpireDate', 'datetime', 'YES', NULL, '', '', '保修到期日');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assets', 'Price', 'decimal(18,2)', 'YES', NULL, '', '', '价格');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assets', 'LocationId', 'int', 'YES', NULL, 'MUL', '', '位置ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assets', 'DepartmentId', 'int', 'YES', NULL, 'MUL', '', '');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assets', 'Status', 'int', 'NO', '0', '', '', '状态：0闲置，1在用，2维修中，3报废');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assets', 'Notes', 'varchar(500)', 'YES', NULL, '', '', '备注');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assets', 'CreatedAt', 'datetime', 'NO', NULL, '', '', '创建时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assets', 'UpdatedAt', 'datetime', 'NO', NULL, '', '', '更新时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assettypes', 'Id', 'int', 'NO', NULL, 'PRI', 'auto_increment', '');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assettypes', 'Name', 'varchar(50)', 'NO', NULL, 'UNI', '', '类型名称');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assettypes', 'Code', 'varchar(50)', 'NO', NULL, 'UNI', '', '类型编码');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assettypes', 'Description', 'varchar(200)', 'YES', NULL, '', '', '类型描述');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assettypes', 'ParentId', 'int', 'YES', NULL, 'MUL', '', '父类型ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assettypes', 'RequireSerialNumber', 'tinyint(1)', 'NO', '1', '', '', '是否需要序列号');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assettypes', 'SortOrder', 'int', 'NO', '0', '', '', '排序');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assettypes', 'IsActive', 'tinyint(1)', 'NO', '1', '', '', '是否激活');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assettypes', 'CreatedAt', 'datetime', 'NO', NULL, '', '', '创建时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'assettypes', 'UpdatedAt', 'datetime', 'NO', NULL, '', '', '更新时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'auditlogs', 'Id', 'int', 'NO', NULL, 'PRI', 'auto_increment', '');
INSERT INTO `db_structure_result` VALUES ('列信息', 'auditlogs', 'UserId', 'int', 'YES', NULL, 'MUL', '', '用户ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'auditlogs', 'UserName', 'varchar(50)', 'YES', NULL, '', '', '用户名');
INSERT INTO `db_structure_result` VALUES ('列信息', 'auditlogs', 'Type', 'varchar(50)', 'NO', NULL, '', '', '日志类型');
INSERT INTO `db_structure_result` VALUES ('列信息', 'auditlogs', 'TableName', 'varchar(50)', 'YES', NULL, '', '', '表名');
INSERT INTO `db_structure_result` VALUES ('列信息', 'auditlogs', 'DateTime', 'datetime', 'NO', NULL, '', '', '日期时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'auditlogs', 'PrimaryKey', 'varchar(50)', 'YES', NULL, '', '', '主键');
INSERT INTO `db_structure_result` VALUES ('列信息', 'auditlogs', 'OldValues', 'longtext', 'YES', NULL, '', '', '旧值');
INSERT INTO `db_structure_result` VALUES ('列信息', 'auditlogs', 'NewValues', 'longtext', 'YES', NULL, '', '', '新值');
INSERT INTO `db_structure_result` VALUES ('列信息', 'auditlogs', 'Action', 'varchar(50)', 'YES', NULL, '', '', '操作');
INSERT INTO `db_structure_result` VALUES ('列信息', 'auditlogs', 'ClientIP', 'varchar(50)', 'YES', NULL, '', '', '客户端IP');
INSERT INTO `db_structure_result` VALUES ('列信息', 'db_structure_result', 'section', 'varchar(3)', 'NO', '', '', '', '');
INSERT INTO `db_structure_result` VALUES ('列信息', 'db_structure_result', '表名', 'varchar(64)', 'YES', NULL, '', '', '');
INSERT INTO `db_structure_result` VALUES ('列信息', 'db_structure_result', '列名', 'varchar(64)', 'YES', NULL, '', '', '');
INSERT INTO `db_structure_result` VALUES ('列信息', 'db_structure_result', '数据类型', 'mediumtext', 'NO', NULL, '', '', '');
INSERT INTO `db_structure_result` VALUES ('列信息', 'db_structure_result', '可为空', 'varchar(3)', 'NO', '', '', '', '');
INSERT INTO `db_structure_result` VALUES ('列信息', 'db_structure_result', '默认值', 'text', 'YES', NULL, '', '', '');
INSERT INTO `db_structure_result` VALUES ('列信息', 'db_structure_result', '键', 'enum(\'\',\'PRI\',\'UNI\',\'MUL\')', 'NO', NULL, '', '', '');
INSERT INTO `db_structure_result` VALUES ('列信息', 'db_structure_result', '额外信息', 'varchar(256)', 'YES', NULL, '', '', '');
INSERT INTO `db_structure_result` VALUES ('列信息', 'db_structure_result', '注释', 'text', 'NO', NULL, '', '', '');
INSERT INTO `db_structure_result` VALUES ('列信息', 'departments', 'Id', 'int', 'NO', NULL, 'PRI', 'auto_increment', '');
INSERT INTO `db_structure_result` VALUES ('列信息', 'departments', 'Name', 'varchar(50)', 'NO', NULL, 'UNI', '', '部门名称');
INSERT INTO `db_structure_result` VALUES ('列信息', 'departments', 'Code', 'varchar(50)', 'NO', NULL, 'UNI', '', '部门编码');
INSERT INTO `db_structure_result` VALUES ('列信息', 'departments', 'ParentId', 'int', 'YES', NULL, 'MUL', '', '父部门ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'departments', 'ManagerId', 'int', 'YES', NULL, 'MUL', '', '部门经理ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'departments', 'Path', 'varchar(200)', 'YES', NULL, '', '', '层级路径');
INSERT INTO `db_structure_result` VALUES ('列信息', 'departments', 'Description', 'varchar(200)', 'YES', NULL, '', '', '部门描述');
INSERT INTO `db_structure_result` VALUES ('列信息', 'departments', 'IsActive', 'tinyint(1)', 'NO', '1', '', '', '是否激活');
INSERT INTO `db_structure_result` VALUES ('列信息', 'departments', 'CreatedAt', 'datetime', 'NO', NULL, '', '', '创建时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'departments', 'UpdatedAt', 'datetime', 'NO', NULL, '', '', '更新时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'faultrecords', 'Id', 'int', 'NO', NULL, 'PRI', 'auto_increment', '');
INSERT INTO `db_structure_result` VALUES ('列信息', 'faultrecords', 'Title', 'varchar(100)', 'NO', NULL, '', '', '标题');
INSERT INTO `db_structure_result` VALUES ('列信息', 'faultrecords', 'Description', 'varchar(500)', 'YES', NULL, '', '', '描述');
INSERT INTO `db_structure_result` VALUES ('列信息', 'faultrecords', 'AssetId', 'int', 'NO', NULL, 'MUL', '', '资产ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'faultrecords', 'FaultTypeId', 'int', 'NO', NULL, 'MUL', '', '故障类型ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'faultrecords', 'LocationId', 'int', 'YES', NULL, 'MUL', '', '位置ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'faultrecords', 'ReporterId', 'int', 'NO', NULL, 'MUL', '', '报告人ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'faultrecords', 'ReportTime', 'datetime', 'NO', NULL, '', '', '报告时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'faultrecords', 'Status', 'int', 'NO', '0', '', '', '状态：0待处理，1处理中，2已解决，3已关闭');
INSERT INTO `db_structure_result` VALUES ('列信息', 'faultrecords', 'Severity', 'int', 'NO', '0', '', '', '严重程度：0一般，1严重，2紧急');
INSERT INTO `db_structure_result` VALUES ('列信息', 'faultrecords', 'AssigneeId', 'int', 'YES', NULL, 'MUL', '', '处理人ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'faultrecords', 'AssignTime', 'datetime', 'YES', NULL, '', '', '分配时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'faultrecords', 'ResponseTime', 'datetime', 'YES', NULL, '', '', '响应时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'faultrecords', 'ResolutionTime', 'datetime', 'YES', NULL, '', '', '解决时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'faultrecords', 'Resolution', 'varchar(500)', 'YES', NULL, '', '', '解决方案');
INSERT INTO `db_structure_result` VALUES ('列信息', 'faultrecords', 'RootCause', 'varchar(500)', 'YES', NULL, '', '', '根本原因');
INSERT INTO `db_structure_result` VALUES ('列信息', 'faultrecords', 'Notes', 'varchar(500)', 'YES', NULL, '', '', '备注');
INSERT INTO `db_structure_result` VALUES ('列信息', 'faultrecords', 'IsReturned', 'tinyint(1)', 'NO', '0', '', '', '是否返厂');
INSERT INTO `db_structure_result` VALUES ('列信息', 'faultrecords', 'CreatedAt', 'datetime', 'NO', NULL, '', '', '创建时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'faultrecords', 'UpdatedAt', 'datetime', 'NO', NULL, '', '', '更新时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'faulttypes', 'Id', 'int', 'NO', NULL, 'PRI', 'auto_increment', '');
INSERT INTO `db_structure_result` VALUES ('列信息', 'faulttypes', 'Name', 'varchar(50)', 'NO', NULL, 'UNI', '', '类型名称');
INSERT INTO `db_structure_result` VALUES ('列信息', 'faulttypes', 'Code', 'varchar(50)', 'NO', NULL, 'UNI', '', '类型编码');
INSERT INTO `db_structure_result` VALUES ('列信息', 'faulttypes', 'Description', 'varchar(200)', 'YES', NULL, '', '', '类型描述');
INSERT INTO `db_structure_result` VALUES ('列信息', 'faulttypes', 'Severity', 'int', 'NO', '0', '', '', '严重程度：0一般，1严重，2紧急');
INSERT INTO `db_structure_result` VALUES ('列信息', 'faulttypes', 'SuggestedResponseTime', 'int', 'YES', NULL, '', '', '建议响应时间（小时）');
INSERT INTO `db_structure_result` VALUES ('列信息', 'faulttypes', 'SuggestedResolutionTime', 'int', 'YES', NULL, '', '', '建议解决时间（小时）');
INSERT INTO `db_structure_result` VALUES ('列信息', 'faulttypes', 'SortOrder', 'int', 'NO', '0', '', '', '排序');
INSERT INTO `db_structure_result` VALUES ('列信息', 'faulttypes', 'IsActive', 'tinyint(1)', 'NO', '1', '', '', '是否激活');
INSERT INTO `db_structure_result` VALUES ('列信息', 'faulttypes', 'CreatedAt', 'datetime', 'NO', NULL, '', '', '创建时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'faulttypes', 'UpdatedAt', 'datetime', 'NO', NULL, '', '', '更新时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'location_users', 'LocationId', 'int', 'NO', NULL, 'PRI', '', '位置ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'location_users', 'UserId', 'int', 'NO', NULL, 'PRI', '', '用户ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'location_users', 'UserType', 'int', 'NO', '0', 'PRI', '', '用户类型：0使用人，1负责人');
INSERT INTO `db_structure_result` VALUES ('列信息', 'location_users', 'IsActive', 'tinyint(1)', 'NO', '1', '', '', '是否激活');
INSERT INTO `db_structure_result` VALUES ('列信息', 'location_users', 'CreatedAt', 'datetime', 'NO', NULL, '', '', '创建时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'location_users', 'UpdatedAt', 'datetime', 'NO', NULL, '', '', '更新时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'locationhistories', 'Id', 'int', 'NO', NULL, 'PRI', 'auto_increment', '');
INSERT INTO `db_structure_result` VALUES ('列信息', 'locationhistories', 'AssetId', 'int', 'NO', NULL, 'MUL', '', '资产ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'locationhistories', 'OldLocationId', 'int', 'YES', NULL, 'MUL', '', '旧位置ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'locationhistories', 'NewLocationId', 'int', 'NO', NULL, 'MUL', '', '新位置ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'locationhistories', 'OperatorId', 'int', 'NO', NULL, 'MUL', '', '操作人ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'locationhistories', 'ChangeType', 'int', 'NO', '0', '', '', '变更类型：0转移，1领用，2归还');
INSERT INTO `db_structure_result` VALUES ('列信息', 'locationhistories', 'ChangeTime', 'datetime', 'NO', NULL, '', '', '变更时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'locationhistories', 'Notes', 'varchar(500)', 'YES', NULL, '', '', '备注');
INSERT INTO `db_structure_result` VALUES ('列信息', 'locationhistories', 'CreatedAt', 'datetime', 'NO', NULL, '', '', '创建时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'locations', 'Id', 'int', 'NO', NULL, 'PRI', 'auto_increment', '');
INSERT INTO `db_structure_result` VALUES ('列信息', 'locations', 'Code', 'varchar(50)', 'NO', NULL, 'UNI', '', '位置编码');
INSERT INTO `db_structure_result` VALUES ('列信息', 'locations', 'Name', 'varchar(100)', 'NO', NULL, '', '', '位置名称');
INSERT INTO `db_structure_result` VALUES ('列信息', 'locations', 'Type', 'int', 'NO', '0', '', '', '位置类型：0工厂，1产线，2工位');
INSERT INTO `db_structure_result` VALUES ('列信息', 'locations', 'ParentId', 'int', 'YES', NULL, 'MUL', '', '父位置ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'locations', 'Path', 'varchar(200)', 'YES', NULL, '', '', '层级路径');
INSERT INTO `db_structure_result` VALUES ('列信息', 'locations', 'Description', 'varchar(200)', 'YES', NULL, '', '', '位置描述');
INSERT INTO `db_structure_result` VALUES ('列信息', 'locations', 'DefaultDepartmentId', 'int', 'YES', NULL, 'MUL', '', '默认部门ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'locations', 'DefaultResponsiblePersonId', 'int', 'YES', NULL, 'MUL', '', '默认负责人ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'locations', 'IsActive', 'tinyint(1)', 'NO', '1', '', '', '是否激活');
INSERT INTO `db_structure_result` VALUES ('列信息', 'locations', 'CreatedAt', 'datetime', 'NO', NULL, '', '', '创建时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'locations', 'UpdatedAt', 'datetime', 'NO', NULL, '', '', '更新时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'locationusers', 'LocationId', 'int', 'NO', NULL, 'PRI', '', '位置ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'locationusers', 'UserId', 'int', 'NO', NULL, 'PRI', '', '用户ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'locationusers', 'UserType', 'int', 'NO', '0', 'PRI', '', '用户类型：0使用人，1负责人');
INSERT INTO `db_structure_result` VALUES ('列信息', 'locationusers', 'IsActive', 'tinyint(1)', 'NO', '1', '', '', '是否激活');
INSERT INTO `db_structure_result` VALUES ('列信息', 'locationusers', 'CreatedAt', 'datetime', 'NO', NULL, '', '', '创建时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'locationusers', 'UpdatedAt', 'datetime', 'NO', NULL, '', '', '更新时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'maintenanceorders', 'Id', 'int', 'NO', NULL, 'PRI', 'auto_increment', '');
INSERT INTO `db_structure_result` VALUES ('列信息', 'maintenanceorders', 'OrderCode', 'varchar(50)', 'NO', NULL, 'UNI', '', '维护单号');
INSERT INTO `db_structure_result` VALUES ('列信息', 'maintenanceorders', 'Title', 'varchar(100)', 'NO', NULL, '', '', '标题');
INSERT INTO `db_structure_result` VALUES ('列信息', 'maintenanceorders', 'Description', 'varchar(500)', 'YES', NULL, '', '', '描述');
INSERT INTO `db_structure_result` VALUES ('列信息', 'maintenanceorders', 'AssetId', 'int', 'NO', NULL, 'MUL', '', '资产ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'maintenanceorders', 'FaultRecordId', 'int', 'YES', NULL, 'MUL', '', '故障记录ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'maintenanceorders', 'MaintenanceType', 'int', 'NO', '0', '', '', '维护类型：0常规维护，1故障维修，2返厂维修跟踪');
INSERT INTO `db_structure_result` VALUES ('列信息', 'maintenanceorders', 'Status', 'int', 'NO', '0', '', '', '状态：0待处理，1处理中，2已完成，3已取消');
INSERT INTO `db_structure_result` VALUES ('列信息', 'maintenanceorders', 'CreatorId', 'int', 'NO', NULL, 'MUL', '', '创建人ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'maintenanceorders', 'CreateTime', 'datetime', 'NO', NULL, '', '', '创建时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'maintenanceorders', 'AssigneeId', 'int', 'YES', NULL, 'MUL', '', '处理人ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'maintenanceorders', 'AssignTime', 'datetime', 'YES', NULL, '', '', '分配时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'maintenanceorders', 'PlanStartTime', 'datetime', 'YES', NULL, '', '', '计划开始时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'maintenanceorders', 'PlanEndTime', 'datetime', 'YES', NULL, '', '', '计划结束时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'maintenanceorders', 'ActualStartTime', 'datetime', 'YES', NULL, '', '', '实际开始时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'maintenanceorders', 'ActualEndTime', 'datetime', 'YES', NULL, '', '', '实际结束时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'maintenanceorders', 'Solution', 'varchar(500)', 'YES', NULL, '', '', '解决方案');
INSERT INTO `db_structure_result` VALUES ('列信息', 'maintenanceorders', 'Notes', 'varchar(500)', 'YES', NULL, '', '', '备注');
INSERT INTO `db_structure_result` VALUES ('列信息', 'maintenanceorders', 'CreatedAt', 'datetime', 'NO', NULL, '', '', '创建时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'maintenanceorders', 'UpdatedAt', 'datetime', 'NO', NULL, '', '', '更新时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'menus', 'Id', 'int', 'NO', NULL, 'PRI', 'auto_increment', '');
INSERT INTO `db_structure_result` VALUES ('列信息', 'menus', 'Name', 'varchar(50)', 'NO', NULL, '', '', '菜单名称');
INSERT INTO `db_structure_result` VALUES ('列信息', 'menus', 'Code', 'varchar(50)', 'NO', NULL, 'UNI', '', '菜单编码');
INSERT INTO `db_structure_result` VALUES ('列信息', 'menus', 'ParentId', 'int', 'YES', NULL, 'MUL', '', '父菜单ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'menus', 'Type', 'int', 'NO', '0', '', '', '菜单类型：0菜单，1按钮');
INSERT INTO `db_structure_result` VALUES ('列信息', 'menus', 'Icon', 'varchar(50)', 'YES', NULL, '', '', '图标');
INSERT INTO `db_structure_result` VALUES ('列信息', 'menus', 'Path', 'varchar(200)', 'YES', NULL, '', '', '路由路径');
INSERT INTO `db_structure_result` VALUES ('列信息', 'menus', 'Component', 'varchar(200)', 'YES', NULL, '', '', '组件路径');
INSERT INTO `db_structure_result` VALUES ('列信息', 'menus', 'Permission', 'varchar(100)', 'YES', NULL, '', '', '权限标识');
INSERT INTO `db_structure_result` VALUES ('列信息', 'menus', 'Description', 'varchar(200)', 'YES', NULL, '', '', '描述');
INSERT INTO `db_structure_result` VALUES ('列信息', 'menus', 'SortOrder', 'int', 'NO', '0', '', '', '排序');
INSERT INTO `db_structure_result` VALUES ('列信息', 'menus', 'IsExternal', 'tinyint(1)', 'NO', '0', '', '', '是否外链');
INSERT INTO `db_structure_result` VALUES ('列信息', 'menus', 'KeepAlive', 'tinyint(1)', 'NO', '0', '', '', '是否缓存');
INSERT INTO `db_structure_result` VALUES ('列信息', 'menus', 'IsVisible', 'tinyint(1)', 'NO', '1', '', '', '是否可见');
INSERT INTO `db_structure_result` VALUES ('列信息', 'menus', 'IsActive', 'tinyint(1)', 'NO', '1', '', '', '是否激活');
INSERT INTO `db_structure_result` VALUES ('列信息', 'menus', 'CreatedAt', 'datetime', 'NO', NULL, '', '', '创建时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'menus', 'UpdatedAt', 'datetime', 'NO', NULL, '', '', '更新时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'pdcaplans', 'Id', 'int', 'NO', NULL, 'PRI', 'auto_increment', '');
INSERT INTO `db_structure_result` VALUES ('列信息', 'pdcaplans', 'Title', 'varchar(100)', 'NO', NULL, '', '', '计划标题');
INSERT INTO `db_structure_result` VALUES ('列信息', 'pdcaplans', 'Type', 'int', 'NO', '0', '', '', '类型：0硬件，1软件，2流程');
INSERT INTO `db_structure_result` VALUES ('列信息', 'pdcaplans', 'PlanStartDate', 'datetime', 'NO', NULL, '', '', '计划开始日期');
INSERT INTO `db_structure_result` VALUES ('列信息', 'pdcaplans', 'PlanEndDate', 'datetime', 'NO', NULL, '', '', '计划结束日期');
INSERT INTO `db_structure_result` VALUES ('列信息', 'pdcaplans', 'ActualEndDate', 'datetime', 'YES', NULL, '', '', '实际结束日期');
INSERT INTO `db_structure_result` VALUES ('列信息', 'pdcaplans', 'Status', 'int', 'NO', '0', '', '', '状态：0计划，1进行中，2已完成，3已取消');
INSERT INTO `db_structure_result` VALUES ('列信息', 'pdcaplans', 'CompletionRate', 'decimal(5,2)', 'NO', '0.00', '', '', '完成率');
INSERT INTO `db_structure_result` VALUES ('列信息', 'pdcaplans', 'ResponsiblePersonId', 'int', 'NO', NULL, 'MUL', '', '负责人ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'pdcaplans', 'Notes', 'varchar(500)', 'YES', NULL, '', '', '备注');
INSERT INTO `db_structure_result` VALUES ('列信息', 'pdcaplans', 'CreatedAt', 'datetime', 'NO', NULL, '', '', '创建时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'pdcaplans', 'UpdatedAt', 'datetime', 'NO', NULL, '', '', '更新时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'periodicrules', 'Id', 'int', 'NO', NULL, 'PRI', 'auto_increment', '');
INSERT INTO `db_structure_result` VALUES ('列信息', 'periodicrules', 'Name', 'varchar(100)', 'NO', NULL, 'UNI', '', '规则名称');
INSERT INTO `db_structure_result` VALUES ('列信息', 'periodicrules', 'Frequency', 'int', 'NO', '0', '', '', '频率：0每天，1每周，2每月，3每季度，4每年');
INSERT INTO `db_structure_result` VALUES ('列信息', 'periodicrules', 'DayOfWeek', 'int', 'YES', NULL, '', '', '每周几（0-6）');
INSERT INTO `db_structure_result` VALUES ('列信息', 'periodicrules', 'DayOfMonth', 'int', 'YES', NULL, '', '', '每月几号（1-31）');
INSERT INTO `db_structure_result` VALUES ('列信息', 'periodicrules', 'IsActive', 'tinyint(1)', 'NO', '1', '', '', '是否激活');
INSERT INTO `db_structure_result` VALUES ('列信息', 'periodicrules', 'CreatedAt', 'datetime', 'NO', NULL, '', '', '创建时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'periodicrules', 'LastGeneratedAt', 'datetime', 'YES', NULL, '', '', '上次生成时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'purchaseitems', 'Id', 'int', 'NO', NULL, 'PRI', 'auto_increment', '');
INSERT INTO `db_structure_result` VALUES ('列信息', 'purchaseitems', 'PurchaseOrderId', 'int', 'NO', NULL, 'MUL', '', '采购订单ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'purchaseitems', 'ItemName', 'varchar(100)', 'NO', NULL, '', '', '项目名称');
INSERT INTO `db_structure_result` VALUES ('列信息', 'purchaseitems', 'ItemCode', 'varchar(50)', 'YES', NULL, '', '', '项目编码');
INSERT INTO `db_structure_result` VALUES ('列信息', 'purchaseitems', 'Specification', 'varchar(200)', 'YES', NULL, '', '', '规格');
INSERT INTO `db_structure_result` VALUES ('列信息', 'purchaseitems', 'AssetTypeId', 'int', 'YES', NULL, 'MUL', '', '资产类型ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'purchaseitems', 'UnitPrice', 'decimal(18,2)', 'NO', '0.00', '', '', '单价');
INSERT INTO `db_structure_result` VALUES ('列信息', 'purchaseitems', 'Quantity', 'int', 'NO', '1', '', '', '数量');
INSERT INTO `db_structure_result` VALUES ('列信息', 'purchaseitems', 'TotalPrice', 'decimal(18,2)', 'NO', '0.00', '', '', '总价');
INSERT INTO `db_structure_result` VALUES ('列信息', 'purchaseitems', 'Notes', 'varchar(500)', 'YES', NULL, '', '', '备注');
INSERT INTO `db_structure_result` VALUES ('列信息', 'purchaseitems', 'CreatedAt', 'datetime', 'NO', NULL, '', '', '创建时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'purchaseitems', 'UpdatedAt', 'datetime', 'NO', NULL, '', '', '更新时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'purchaseorders', 'Id', 'int', 'NO', NULL, 'PRI', 'auto_increment', '');
INSERT INTO `db_structure_result` VALUES ('列信息', 'purchaseorders', 'OrderCode', 'varchar(50)', 'NO', NULL, 'UNI', '', '订单编码');
INSERT INTO `db_structure_result` VALUES ('列信息', 'purchaseorders', 'Title', 'varchar(100)', 'NO', NULL, '', '', '标题');
INSERT INTO `db_structure_result` VALUES ('列信息', 'purchaseorders', 'Description', 'varchar(500)', 'YES', NULL, '', '', '描述');
INSERT INTO `db_structure_result` VALUES ('列信息', 'purchaseorders', 'SupplierId', 'int', 'NO', NULL, 'MUL', '', '供应商ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'purchaseorders', 'Status', 'int', 'NO', '0', '', '', '状态：0草稿，1待审批，2已审批，3已发出，4已收货，5已完成，6已取消');
INSERT INTO `db_structure_result` VALUES ('列信息', 'purchaseorders', 'EstimatedDeliveryDate', 'datetime', 'YES', NULL, '', '', '预计交付日期');
INSERT INTO `db_structure_result` VALUES ('列信息', 'purchaseorders', 'ActualDeliveryDate', 'datetime', 'YES', NULL, '', '', '实际交付日期');
INSERT INTO `db_structure_result` VALUES ('列信息', 'purchaseorders', 'ApplicantId', 'int', 'NO', NULL, 'MUL', '', '申请人ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'purchaseorders', 'ApplicationTime', 'datetime', 'NO', NULL, '', '', '申请时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'purchaseorders', 'ApproverId', 'int', 'YES', NULL, 'MUL', '', '审批人ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'purchaseorders', 'ApprovalTime', 'datetime', 'YES', NULL, '', '', '审批时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'purchaseorders', 'TotalAmount', 'decimal(18,2)', 'NO', '0.00', '', '', '总金额');
INSERT INTO `db_structure_result` VALUES ('列信息', 'purchaseorders', 'Notes', 'varchar(500)', 'YES', NULL, '', '', '备注');
INSERT INTO `db_structure_result` VALUES ('列信息', 'purchaseorders', 'CreatedAt', 'datetime', 'NO', NULL, '', '', '创建时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'purchaseorders', 'UpdatedAt', 'datetime', 'NO', NULL, '', '', '更新时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'refreshtokens', 'Id', 'int', 'NO', NULL, 'PRI', 'auto_increment', '');
INSERT INTO `db_structure_result` VALUES ('列信息', 'refreshtokens', 'UserId', 'int', 'NO', NULL, 'MUL', '', '用户ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'refreshtokens', 'Token', 'varchar(200)', 'NO', NULL, '', '', '令牌');
INSERT INTO `db_structure_result` VALUES ('列信息', 'refreshtokens', 'JwtId', 'varchar(200)', 'NO', NULL, '', '', 'JWT ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'refreshtokens', 'IsUsed', 'tinyint(1)', 'NO', '0', '', '', '是否已使用');
INSERT INTO `db_structure_result` VALUES ('列信息', 'refreshtokens', 'IsRevoked', 'tinyint(1)', 'NO', '0', '', '', '是否已撤销');
INSERT INTO `db_structure_result` VALUES ('列信息', 'refreshtokens', 'AddedDate', 'datetime', 'NO', NULL, '', '', '添加日期');
INSERT INTO `db_structure_result` VALUES ('列信息', 'refreshtokens', 'ExpiryDate', 'datetime', 'NO', NULL, '', '', '过期日期');
INSERT INTO `db_structure_result` VALUES ('列信息', 'returntofactories', 'Id', 'int', 'NO', NULL, 'PRI', 'auto_increment', '');
INSERT INTO `db_structure_result` VALUES ('列信息', 'returntofactories', 'Code', 'varchar(50)', 'NO', NULL, 'UNI', '', '返厂单号');
INSERT INTO `db_structure_result` VALUES ('列信息', 'returntofactories', 'AssetId', 'int', 'NO', NULL, 'MUL', '', '资产ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'returntofactories', 'FaultRecordId', 'int', 'NO', NULL, 'MUL', '', '故障记录ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'returntofactories', 'SupplierId', 'int', 'NO', NULL, 'MUL', '', '供应商ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'returntofactories', 'Status', 'int', 'NO', '0', '', '', '状态：0待送出，1已送出，2维修中，3已返回，4维修失败');
INSERT INTO `db_structure_result` VALUES ('列信息', 'returntofactories', 'SenderId', 'int', 'NO', NULL, 'MUL', '', '送出人ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'returntofactories', 'SendTime', 'datetime', 'YES', NULL, '', '', '送出时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'returntofactories', 'EstimatedReturnTime', 'datetime', 'YES', NULL, '', '', '预计返回时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'returntofactories', 'ActualReturnTime', 'datetime', 'YES', NULL, '', '', '实际返回时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'returntofactories', 'RepairResult', 'varchar(500)', 'YES', NULL, '', '', '维修结果');
INSERT INTO `db_structure_result` VALUES ('列信息', 'returntofactories', 'RepairCost', 'decimal(18,2)', 'YES', NULL, '', '', '维修费用');
INSERT INTO `db_structure_result` VALUES ('列信息', 'returntofactories', 'InWarranty', 'tinyint(1)', 'NO', '1', '', '', '是否在保修期内');
INSERT INTO `db_structure_result` VALUES ('列信息', 'returntofactories', 'Notes', 'varchar(500)', 'YES', NULL, '', '', '备注');
INSERT INTO `db_structure_result` VALUES ('列信息', 'returntofactories', 'CreatedAt', 'datetime', 'NO', NULL, '', '', '创建时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'returntofactories', 'UpdatedAt', 'datetime', 'NO', NULL, '', '', '更新时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'rolemenus', 'RoleId', 'int', 'NO', NULL, 'PRI', '', '角色ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'rolemenus', 'MenuId', 'int', 'NO', NULL, 'PRI', '', '菜单ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'rolemenus', 'CreatedAt', 'datetime', 'NO', NULL, '', '', '创建时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'roles', 'Id', 'int', 'NO', NULL, 'PRI', 'auto_increment', '');
INSERT INTO `db_structure_result` VALUES ('列信息', 'roles', 'Name', 'varchar(50)', 'NO', NULL, 'UNI', '', '角色名称');
INSERT INTO `db_structure_result` VALUES ('列信息', 'roles', 'Code', 'varchar(50)', 'NO', NULL, 'UNI', '', '角色编码');
INSERT INTO `db_structure_result` VALUES ('列信息', 'roles', 'Description', 'varchar(200)', 'YES', NULL, '', '', '角色描述');
INSERT INTO `db_structure_result` VALUES ('列信息', 'roles', 'IsActive', 'tinyint(1)', 'NO', '1', '', '', '是否激活');
INSERT INTO `db_structure_result` VALUES ('列信息', 'roles', 'CreatedAt', 'datetime', 'NO', NULL, '', '', '创建时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'roles', 'UpdatedAt', 'datetime', 'NO', NULL, '', '', '更新时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'suppliers', 'Id', 'int', 'NO', NULL, 'PRI', 'auto_increment', '');
INSERT INTO `db_structure_result` VALUES ('列信息', 'suppliers', 'Name', 'varchar(100)', 'NO', NULL, 'UNI', '', '供应商名称');
INSERT INTO `db_structure_result` VALUES ('列信息', 'suppliers', 'Code', 'varchar(50)', 'NO', NULL, 'UNI', '', '供应商编码');
INSERT INTO `db_structure_result` VALUES ('列信息', 'suppliers', 'ContactPerson', 'varchar(50)', 'YES', NULL, '', '', '联系人');
INSERT INTO `db_structure_result` VALUES ('列信息', 'suppliers', 'ContactPhone', 'varchar(20)', 'YES', NULL, '', '', '联系电话');
INSERT INTO `db_structure_result` VALUES ('列信息', 'suppliers', 'ContactEmail', 'varchar(100)', 'YES', NULL, '', '', '联系邮箱');
INSERT INTO `db_structure_result` VALUES ('列信息', 'suppliers', 'Address', 'varchar(200)', 'YES', NULL, '', '', '地址');
INSERT INTO `db_structure_result` VALUES ('列信息', 'suppliers', 'Type', 'int', 'NO', '0', '', '', '类型：0硬件，1软件，2服务');
INSERT INTO `db_structure_result` VALUES ('列信息', 'suppliers', 'Notes', 'varchar(500)', 'YES', NULL, '', '', '备注');
INSERT INTO `db_structure_result` VALUES ('列信息', 'suppliers', 'IsActive', 'tinyint(1)', 'NO', '1', '', '', '是否激活');
INSERT INTO `db_structure_result` VALUES ('列信息', 'suppliers', 'CreatedAt', 'datetime', 'NO', NULL, '', '', '创建时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'suppliers', 'UpdatedAt', 'datetime', 'NO', NULL, '', '', '更新时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'tasks', 'Id', 'int', 'NO', NULL, 'PRI', 'auto_increment', '');
INSERT INTO `db_structure_result` VALUES ('列信息', 'tasks', 'Title', 'varchar(100)', 'NO', NULL, '', '', '任务标题');
INSERT INTO `db_structure_result` VALUES ('列信息', 'tasks', 'Description', 'varchar(500)', 'YES', NULL, '', '', '任务描述');
INSERT INTO `db_structure_result` VALUES ('列信息', 'tasks', 'TaskType', 'int', 'NO', '0', '', '', '任务类型：0普通任务，1周期任务，2计划任务');
INSERT INTO `db_structure_result` VALUES ('列信息', 'tasks', 'Status', 'int', 'NO', '0', '', '', '状态：0待处理，1处理中，2已完成，3已取消');
INSERT INTO `db_structure_result` VALUES ('列信息', 'tasks', 'AssignedToId', 'int', 'YES', NULL, 'MUL', '', '分配给谁');
INSERT INTO `db_structure_result` VALUES ('列信息', 'tasks', 'CreatedById', 'int', 'NO', NULL, 'MUL', '', '创建人ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'tasks', 'CreatedAt', 'datetime', 'NO', NULL, '', '', '创建时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'tasks', 'DueDate', 'datetime', 'YES', NULL, '', '', '截止日期');
INSERT INTO `db_structure_result` VALUES ('列信息', 'tasks', 'CompletedAt', 'datetime', 'YES', NULL, '', '', '完成时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'tasks', 'RelatedAssetId', 'int', 'YES', NULL, 'MUL', '', '相关资产ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'tasks', 'RelatedLocationId', 'int', 'YES', NULL, 'MUL', '', '相关位置ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'tasks', 'PeriodicRuleId', 'int', 'YES', NULL, 'MUL', '', '周期规则ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'tasks', 'PdcaPlanId', 'int', 'YES', NULL, 'MUL', '', 'PDCA计划ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'tasks', 'Notes', 'varchar(500)', 'YES', NULL, '', '', '备注');
INSERT INTO `db_structure_result` VALUES ('列信息', 'tasks', 'UpdatedAt', 'datetime', 'NO', NULL, '', '', '更新时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'userroles', 'UserId', 'int', 'NO', NULL, 'PRI', '', '用户ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'userroles', 'RoleId', 'int', 'NO', NULL, 'PRI', '', '角色ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'userroles', 'CreatedAt', 'datetime', 'NO', NULL, '', '', '创建时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'users', 'Id', 'int', 'NO', NULL, 'PRI', 'auto_increment', '');
INSERT INTO `db_structure_result` VALUES ('列信息', 'users', 'Username', 'varchar(50)', 'NO', NULL, 'UNI', '', '用户名');
INSERT INTO `db_structure_result` VALUES ('列信息', 'users', 'PasswordHash', 'varchar(200)', 'NO', NULL, '', '', '密码哈希');
INSERT INTO `db_structure_result` VALUES ('列信息', 'users', 'SecurityStamp', 'varchar(50)', 'NO', NULL, '', '', '安全戳');
INSERT INTO `db_structure_result` VALUES ('列信息', 'users', 'Name', 'varchar(50)', 'NO', NULL, '', '', '姓名');
INSERT INTO `db_structure_result` VALUES ('列信息', 'users', 'Email', 'varchar(100)', 'YES', NULL, 'UNI', '', '邮箱');
INSERT INTO `db_structure_result` VALUES ('列信息', 'users', 'Mobile', 'varchar(20)', 'YES', NULL, '', '', '手机号');
INSERT INTO `db_structure_result` VALUES ('列信息', 'users', 'DepartmentId', 'int', 'YES', NULL, 'MUL', '', '部门ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'users', 'Position', 'varchar(50)', 'YES', NULL, '', '', '职位');
INSERT INTO `db_structure_result` VALUES ('列信息', 'users', 'Gender', 'int', 'NO', '0', '', '', '性别：0未知，1男，2女');
INSERT INTO `db_structure_result` VALUES ('列信息', 'users', 'DefaultRoleId', 'int', 'YES', NULL, 'MUL', '', '默认角色ID');
INSERT INTO `db_structure_result` VALUES ('列信息', 'users', 'IsActive', 'tinyint(1)', 'NO', '1', '', '', '是否激活');
INSERT INTO `db_structure_result` VALUES ('列信息', 'users', 'CreatedAt', 'datetime', 'NO', NULL, '', '', '创建时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'users', 'UpdatedAt', 'datetime', 'NO', NULL, '', '', '更新时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'users', 'LastLoginAt', 'datetime', 'YES', NULL, '', '', '最后登录时间');
INSERT INTO `db_structure_result` VALUES ('列信息', 'users', 'Avatar', 'varchar(200)', 'YES', NULL, '', '', '头像');

-- ----------------------------
-- Table structure for departments
-- ----------------------------
DROP TABLE IF EXISTS `departments`;
CREATE TABLE `departments`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `Name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '部门名称',
  `Code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '部门编码',
  `ParentId` int NULL DEFAULT NULL COMMENT '父部门ID',
  `ManagerId` int NULL DEFAULT NULL COMMENT '部门经理ID',
  `Path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '层级路径',
  `Description` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '部门描述',
  `IsActive` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否激活',
  `CreatedAt` datetime NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE INDEX `IX_Departments_Code`(`Code` ASC) USING BTREE,
  UNIQUE INDEX `IX_Departments_Name`(`Name` ASC) USING BTREE,
  INDEX `IX_Departments_ParentId`(`ParentId` ASC) USING BTREE,
  INDEX `IX_Departments_ManagerId`(`ManagerId` ASC) USING BTREE,
  CONSTRAINT `FK_Departments_Departments_ParentId` FOREIGN KEY (`ParentId`) REFERENCES `departments` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 27 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '部门表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of departments
-- ----------------------------
INSERT INTO `departments` VALUES (1, '总经办', 'CEO_OFFICE', NULL, NULL, '1', '公司总经理办公室', 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `departments` VALUES (2, 'IT部门', 'IT_DEPT', NULL, NULL, '2', '信息技术部门', 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `departments` VALUES (3, '人力资源部', 'HR_DEPT', NULL, NULL, '3', '人力资源管理部门', 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `departments` VALUES (4, '财务部', 'FINANCE_DEPT', NULL, NULL, '4', '财务管理部门', 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `departments` VALUES (5, '生产部', 'PRODUCTION_DEPT', NULL, NULL, '5', '生产制造部门', 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `departments` VALUES (6, '工程部', 'ENGINEERING_DEPT', NULL, NULL, '6', '工程技术部门', 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `departments` VALUES (7, '质量部', 'QUALITY_DEPT', NULL, NULL, '7', '质量管理部门', 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `departments` VALUES (8, '采购部', 'PURCHASE_DEPT', NULL, NULL, '8', '采购管理部门', 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `departments` VALUES (9, '销售部', 'SALES_DEPT', NULL, NULL, '9', '销售管理部门', 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `departments` VALUES (10, 'IT基础设施', 'IT_INFRASTRUCTURE', 2, 11, '2-1', 'IT基础设施管理', 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `departments` VALUES (11, 'IT应用开发', 'IT_APPLICATION', 2, 12, '2-2', 'IT应用系统开发', 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `departments` VALUES (12, 'IT运维', 'IT_OPERATION', 2, 13, '2-3', 'IT系统运维管理', 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `departments` VALUES (13, 'IT安全', 'IT_SECURITY', 2, 14, '2-4', 'IT安全管理', 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `departments` VALUES (14, '生产一部', 'PRODUCTION_DEPT_1', 5, 15, '5-1', '生产制造一部', 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `departments` VALUES (15, '生产二部', 'PRODUCTION_DEPT_2', 5, 16, '5-2', '生产制造二部', 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `departments` VALUES (16, '生产计划', 'PRODUCTION_PLANNING', 5, NULL, '5-3', '生产计划管理', 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `departments` VALUES (17, '设备工程', 'EQUIPMENT_ENGINEERING', 6, NULL, '6-1', '设备工程管理', 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `departments` VALUES (18, '工艺工程', 'PROCESS_ENGINEERING', 6, NULL, '6-2', '工艺工程管理', 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `departments` VALUES (19, '自动化工程', 'AUTOMATION_ENGINEERING', 6, NULL, '6-3', '自动化工程管理', 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `departments` VALUES (20, 'C工厂', 'FACTORY_C', NULL, NULL, '10', 'C工厂管理部门', 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `departments` VALUES (21, 'C1产线部', 'C1_LINE_DEPT', 20, NULL, '10-1', 'C1产线管理部门', 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `departments` VALUES (22, 'C2产线部', 'C2_LINE_DEPT', 20, NULL, '10-2', 'C2产线管理部门', 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `departments` VALUES (23, 'C工厂设备部', 'C_EQUIPMENT_DEPT', 20, NULL, '10-3', 'C工厂设备管理部门', 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `departments` VALUES (24, 'C工厂质检部', 'C_QC_DEPT', 20, NULL, '10-4', 'C工厂质量检查部门', 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `departments` VALUES (25, 'C工厂物流部', 'C_LOGISTICS_DEPT', 20, NULL, '10-5', 'C工厂物流管理部门', 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `departments` VALUES (26, 'C工厂行政部', 'C_ADMIN_DEPT', 20, NULL, '10-6', 'C工厂行政管理部门', 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');

-- ----------------------------
-- Table structure for faultrecords
-- ----------------------------
DROP TABLE IF EXISTS `faultrecords`;
CREATE TABLE `faultrecords`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `Title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '标题',
  `Description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '描述',
  `AssetId` int NOT NULL COMMENT '资产ID',
  `FaultTypeId` int NOT NULL COMMENT '故障类型ID',
  `LocationId` int NULL DEFAULT NULL COMMENT '位置ID',
  `ReporterId` int NOT NULL COMMENT '报告人ID',
  `ReportTime` datetime NOT NULL COMMENT '报告时间',
  `Status` int NOT NULL DEFAULT 0 COMMENT '状态：0待处理，1处理中，2已解决，3已关闭',
  `Severity` int NOT NULL DEFAULT 0 COMMENT '严重程度：0一般，1严重，2紧急',
  `AssigneeId` int NULL DEFAULT NULL COMMENT '处理人ID',
  `AssignTime` datetime NULL DEFAULT NULL COMMENT '分配时间',
  `ResponseTime` datetime NULL DEFAULT NULL COMMENT '响应时间',
  `ResolutionTime` datetime NULL DEFAULT NULL COMMENT '解决时间',
  `Resolution` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '解决方案',
  `RootCause` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '根本原因',
  `Notes` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `IsReturned` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否返厂',
  `CreatedAt` datetime NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`) USING BTREE,
  INDEX `IX_FaultRecords_AssetId`(`AssetId` ASC) USING BTREE,
  INDEX `IX_FaultRecords_FaultTypeId`(`FaultTypeId` ASC) USING BTREE,
  INDEX `IX_FaultRecords_LocationId`(`LocationId` ASC) USING BTREE,
  INDEX `IX_FaultRecords_ReporterId`(`ReporterId` ASC) USING BTREE,
  INDEX `IX_FaultRecords_AssigneeId`(`AssigneeId` ASC) USING BTREE,
  CONSTRAINT `FK_FaultRecords_Assets_AssetId` FOREIGN KEY (`AssetId`) REFERENCES `assets` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_FaultRecords_FaultTypes_FaultTypeId` FOREIGN KEY (`FaultTypeId`) REFERENCES `faulttypes` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_FaultRecords_Locations_LocationId` FOREIGN KEY (`LocationId`) REFERENCES `locations` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_FaultRecords_Users_AssigneeId` FOREIGN KEY (`AssigneeId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_FaultRecords_Users_ReporterId` FOREIGN KEY (`ReporterId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '故障记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of faultrecords
-- ----------------------------

-- ----------------------------
-- Table structure for faulttypes
-- ----------------------------
DROP TABLE IF EXISTS `faulttypes`;
CREATE TABLE `faulttypes`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `Name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '类型名称',
  `Code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '类型编码',
  `Description` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '类型描述',
  `Severity` int NOT NULL DEFAULT 0 COMMENT '严重程度：0一般，1严重，2紧急',
  `SuggestedResponseTime` int NULL DEFAULT NULL COMMENT '建议响应时间（小时）',
  `SuggestedResolutionTime` int NULL DEFAULT NULL COMMENT '建议解决时间（小时）',
  `SortOrder` int NOT NULL DEFAULT 0 COMMENT '排序',
  `IsActive` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否激活',
  `CreatedAt` datetime NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE INDEX `IX_FaultTypes_Code`(`Code` ASC) USING BTREE,
  UNIQUE INDEX `IX_FaultTypes_Name`(`Name` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 16 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '故障类型表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of faulttypes
-- ----------------------------
INSERT INTO `faulttypes` VALUES (1, '硬件损坏', 'HW_DAMAGE', '设备硬件部件物理损坏', 1, 4, 48, 10, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `faulttypes` VALUES (2, '系统崩溃', 'SYS_CRASH', '操作系统或应用程序崩溃', 2, 2, 24, 20, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `faulttypes` VALUES (3, '网络连接中断', 'NET_DISCONNECT', '网络连接无法建立或中断', 1, 2, 12, 30, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `faulttypes` VALUES (4, '设备无法启动', 'BOOT_FAILURE', '设备无法正常启动或开机', 2, 2, 24, 40, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `faulttypes` VALUES (5, '性能下降', 'PERF_DEGRADE', '设备性能显著降低', 0, 8, 48, 50, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `faulttypes` VALUES (6, '设备过热', 'OVERHEAT', '设备温度异常升高', 1, 2, 12, 60, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `faulttypes` VALUES (7, '电源故障', 'POWER_FAILURE', '电源供应故障或不稳定', 2, 2, 24, 70, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `faulttypes` VALUES (8, '显示异常', 'DISPLAY_ERROR', '显示器或屏幕显示异常', 0, 4, 24, 80, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `faulttypes` VALUES (9, '噪音异常', 'NOISE_ABNORMAL', '设备运行噪音异常', 0, 8, 48, 90, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `faulttypes` VALUES (10, '软件异常', 'SW_ERROR', '软件运行异常或错误', 1, 4, 24, 100, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `faulttypes` VALUES (11, '端口故障', 'PORT_FAILURE', '通信端口或接口故障', 1, 4, 24, 110, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `faulttypes` VALUES (12, '打印质量问题', 'PRINT_QUALITY', '打印设备输出质量问题', 0, 8, 48, 120, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `faulttypes` VALUES (13, '扫描故障', 'SCAN_FAILURE', '扫描设备无法正常工作', 1, 4, 24, 130, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `faulttypes` VALUES (14, '电池故障', 'BATTERY_ISSUE', '电池无法充电或续航时间短', 0, 8, 48, 140, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `faulttypes` VALUES (15, '存储故障', 'STORAGE_FAILURE', '存储设备读写错误或无法识别', 1, 4, 24, 150, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');

-- ----------------------------
-- Table structure for location_users
-- ----------------------------
DROP TABLE IF EXISTS `location_users`;
CREATE TABLE `location_users`  (
  `LocationId` int NOT NULL COMMENT '位置ID',
  `UserId` int NOT NULL COMMENT '用户ID',
  `UserType` int NOT NULL DEFAULT 0 COMMENT '用户类型：0使用人，1负责人',
  `IsActive` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否激活',
  `CreatedAt` datetime NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`LocationId`, `UserId`, `UserType`) USING BTREE,
  INDEX `IX_Location_Users_UserId`(`UserId` ASC) USING BTREE,
  CONSTRAINT `FK_Location_Users_Locations_LocationId` FOREIGN KEY (`LocationId`) REFERENCES `locations` (`Id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `FK_Location_Users_Users_UserId` FOREIGN KEY (`UserId`) REFERENCES `users` (`Id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '位置用户关联表（备用表）' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of location_users
-- ----------------------------
INSERT INTO `location_users` VALUES (27, 11, 1, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `location_users` VALUES (27, 17, 0, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `location_users` VALUES (28, 11, 1, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `location_users` VALUES (28, 18, 0, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `location_users` VALUES (29, 12, 1, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `location_users` VALUES (29, 19, 0, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `location_users` VALUES (30, 13, 1, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `location_users` VALUES (30, 20, 0, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `location_users` VALUES (31, 14, 1, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `location_users` VALUES (31, 21, 0, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');

-- ----------------------------
-- Table structure for locationhistories
-- ----------------------------
DROP TABLE IF EXISTS `locationhistories`;
CREATE TABLE `locationhistories`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `AssetId` int NOT NULL COMMENT '资产ID',
  `OldLocationId` int NULL DEFAULT NULL COMMENT '旧位置ID',
  `NewLocationId` int NOT NULL COMMENT '新位置ID',
  `OperatorId` int NOT NULL COMMENT '操作人ID',
  `ChangeType` int NOT NULL DEFAULT 0 COMMENT '变更类型：0转移，1领用，2归还',
  `ChangeTime` datetime NOT NULL COMMENT '变更时间',
  `Notes` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `CreatedAt` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`Id`) USING BTREE,
  INDEX `IX_LocationHistories_AssetId`(`AssetId` ASC) USING BTREE,
  INDEX `IX_LocationHistories_OldLocationId`(`OldLocationId` ASC) USING BTREE,
  INDEX `IX_LocationHistories_NewLocationId`(`NewLocationId` ASC) USING BTREE,
  INDEX `IX_LocationHistories_OperatorId`(`OperatorId` ASC) USING BTREE,
  CONSTRAINT `FK_LocationHistories_Assets_AssetId` FOREIGN KEY (`AssetId`) REFERENCES `assets` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_LocationHistories_Locations_NewLocationId` FOREIGN KEY (`NewLocationId`) REFERENCES `locations` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_LocationHistories_Locations_OldLocationId` FOREIGN KEY (`OldLocationId`) REFERENCES `locations` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_LocationHistories_Users_OperatorId` FOREIGN KEY (`OperatorId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '位置历史表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of locationhistories
-- ----------------------------

-- ----------------------------
-- Table structure for locations
-- ----------------------------
DROP TABLE IF EXISTS `locations`;
CREATE TABLE `locations`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `Code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '位置编码',
  `Name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '位置名称',
  `Type` int NOT NULL DEFAULT 0 COMMENT '位置类型：0工厂，1产线，2工位',
  `ParentId` int NULL DEFAULT NULL COMMENT '父位置ID',
  `Path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '层级路径',
  `Description` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '位置描述',
  `DefaultDepartmentId` int NULL DEFAULT NULL COMMENT '默认部门ID',
  `DefaultResponsiblePersonId` int NULL DEFAULT NULL COMMENT '默认负责人ID',
  `IsActive` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否激活',
  `CreatedAt` datetime NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE INDEX `IX_Locations_Code`(`Code` ASC) USING BTREE,
  INDEX `IX_Locations_ParentId`(`ParentId` ASC) USING BTREE,
  INDEX `IX_Locations_DefaultDepartmentId`(`DefaultDepartmentId` ASC) USING BTREE,
  INDEX `IX_Locations_DefaultResponsiblePersonId`(`DefaultResponsiblePersonId` ASC) USING BTREE,
  CONSTRAINT `FK_Locations_Departments_DefaultDepartmentId` FOREIGN KEY (`DefaultDepartmentId`) REFERENCES `departments` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_Locations_Locations_ParentId` FOREIGN KEY (`ParentId`) REFERENCES `locations` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_Locations_Users_DefaultResponsiblePersonId` FOREIGN KEY (`DefaultResponsiblePersonId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 34 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '位置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of locations
-- ----------------------------
INSERT INTO `locations` VALUES (1, 'FACTORY_A', '工厂A', 0, NULL, '1', '', NULL, NULL, 1, '2025-03-23 20:45:09', '2025-03-25 01:26:51');
INSERT INTO `locations` VALUES (2, 'FACTORY_B', 'B工厂', 0, NULL, '2', 'B工厂主厂区', NULL, NULL, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `locations` VALUES (3, 'A_LINE_1', '铸造产线', 1, 1, '1-1', '', NULL, NULL, 1, '2025-03-23 20:45:09', '2025-03-25 13:29:37');
INSERT INTO `locations` VALUES (4, 'A_LINE_2', 'A2产线', 1, 1, '1-2', 'A工厂2号产线', NULL, NULL, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `locations` VALUES (5, 'A_LINE_3', 'A3产线', 1, 1, '1-3', 'A工厂3号产线', NULL, NULL, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `locations` VALUES (6, 'A_WAREHOUSE', 'A工厂仓库', 3, 1, '1-4', 'A工厂仓库区域', NULL, NULL, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `locations` VALUES (7, 'B_LINE_1', 'B1产线', 1, 2, '2-1', 'B工厂1号产线', NULL, NULL, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `locations` VALUES (8, 'B_LINE_2', 'B2产线', 1, 2, '2-2', 'B工厂2号产线', NULL, NULL, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `locations` VALUES (9, 'B_WAREHOUSE', 'B工厂仓库', 3, 2, '2-3', 'B工厂仓库区域', NULL, NULL, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `locations` VALUES (10, 'A1_WS_1', '铸造工序', 2, 3, '1-3-1', '', NULL, NULL, 1, '2025-03-23 20:45:09', '2025-03-25 01:31:42');
INSERT INTO `locations` VALUES (11, 'A1_WS_2', 'A1-2工位', 2, 3, '1-3-2', 'A1产线2号工位', NULL, NULL, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `locations` VALUES (12, 'A1_WS_3', 'A1-3工位', 2, 3, '1-3-3', 'A1产线3号工位', NULL, NULL, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `locations` VALUES (13, 'A2_WS_1', 'A2-1工位', 2, 4, '1-4-1', 'A2产线1号工位', NULL, NULL, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `locations` VALUES (14, 'A2_WS_2', 'A2-2工位', 2, 4, '1-4-2', 'A2产线2号工位', NULL, NULL, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `locations` VALUES (15, 'B1_WS_1', 'B1-1工位', 2, 7, '2-7-1', 'B1产线1号工位', NULL, NULL, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `locations` VALUES (16, 'B1_WS_2', 'B1-2工位', 2, 7, '2-7-2', 'B1产线2号工位', NULL, NULL, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `locations` VALUES (17, 'FACTORY_C', 'C工厂', 0, NULL, '3', 'C工厂主厂区', 10, 11, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `locations` VALUES (18, 'C_LINE_1', 'C1产线', 1, 17, '3-1', 'C工厂1号产线', 10, 11, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `locations` VALUES (19, 'C_LINE_2', 'C2产线', 1, 17, '3-2', 'C工厂2号产线', 11, 12, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `locations` VALUES (20, 'C1_PROC_1', 'C1-1工序', 1, 18, '3-1-1', 'C1产线1号工序', 10, 11, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `locations` VALUES (21, 'C1_PROC_2', 'C1-2工序', 1, 18, '3-1-2', 'C1产线2号工序', 12, 13, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `locations` VALUES (22, 'C2_PROC_1', 'C2-1工序', 1, 19, '3-2-1', 'C2产线1号工序', 13, 14, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `locations` VALUES (23, 'C1_PROC1_WS1', 'C1-1-1工位', 2, 20, '3-1-1-1', 'C1产线1工序1号工位', 10, 11, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `locations` VALUES (24, 'C1_PROC1_WS2', 'C1-1-2工位', 2, 20, '3-1-1-2', 'C1产线1工序2号工位', 10, 11, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `locations` VALUES (25, 'C1_PROC2_WS1', 'C1-2-1工位', 2, 21, '3-1-2-1', 'C1产线2工序1号工位', 12, 13, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `locations` VALUES (26, 'C2_PROC1_WS1', 'C2-1-1工位', 2, 22, '3-2-1-1', 'C2产线1工序1号工位', 13, 14, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `locations` VALUES (27, 'C1_P1_W1_L1', 'C1-1-1-1号位置', 2, 23, '3-1-1-1-1', 'C1产线1工序1工位1号位置', 10, 11, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `locations` VALUES (28, 'C1_P1_W1_L2', 'C1-1-1-2号位置', 2, 23, '3-1-1-1-2', 'C1产线1工序1工位2号位置', 10, 11, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `locations` VALUES (29, 'C1_P1_W2_L1', 'C1-1-2-1号位置', 2, 24, '3-1-1-2-1', 'C1产线1工序2工位1号位置', 11, 12, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `locations` VALUES (30, 'C1_P2_W1_L1', 'C1-2-1-1号位置', 2, 25, '3-1-2-1-1', 'C1产线2工序1工位1号位置', 12, 13, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `locations` VALUES (31, 'C2_P1_W1_L1', 'C2-1-1-1号位置', 2, 26, '3-2-1-1-1', 'C2产线1工序1工位1号位置', 13, 14, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `locations` VALUES (32, 'W001', '低压铸造', 3, 10, '1-3-1,10', '', NULL, NULL, 1, '2025-03-25 21:45:58', '2025-03-25 21:45:58');
INSERT INTO `locations` VALUES (33, 'E001', '铸造A1', 4, 32, '1-3-1,10,32', '', NULL, NULL, 1, '2025-03-25 21:57:13', '2025-03-25 22:36:48');

-- ----------------------------
-- Table structure for locationusers
-- ----------------------------
DROP TABLE IF EXISTS `locationusers`;
CREATE TABLE `locationusers`  (
  `LocationId` int NOT NULL COMMENT '位置ID',
  `UserId` int NOT NULL COMMENT '用户ID',
  `UserType` int NOT NULL DEFAULT 0 COMMENT '用户类型：0使用人，1负责人',
  `IsActive` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否激活',
  `CreatedAt` datetime NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`LocationId`, `UserId`, `UserType`) USING BTREE,
  INDEX `IX_LocationUsers_UserId`(`UserId` ASC) USING BTREE,
  CONSTRAINT `FK_LocationUsers_Locations_LocationId` FOREIGN KEY (`LocationId`) REFERENCES `locations` (`Id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `FK_LocationUsers_Users_UserId` FOREIGN KEY (`UserId`) REFERENCES `users` (`Id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '位置用户关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of locationusers
-- ----------------------------
INSERT INTO `locationusers` VALUES (27, 11, 1, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `locationusers` VALUES (27, 17, 0, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `locationusers` VALUES (28, 11, 1, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `locationusers` VALUES (28, 18, 0, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `locationusers` VALUES (29, 12, 1, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `locationusers` VALUES (29, 19, 0, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `locationusers` VALUES (30, 13, 1, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `locationusers` VALUES (30, 20, 0, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `locationusers` VALUES (31, 14, 1, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `locationusers` VALUES (31, 21, 0, 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');

-- ----------------------------
-- Table structure for maintenanceorders
-- ----------------------------
DROP TABLE IF EXISTS `maintenanceorders`;
CREATE TABLE `maintenanceorders`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `OrderCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '维护单号',
  `Title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '标题',
  `Description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '描述',
  `AssetId` int NOT NULL COMMENT '资产ID',
  `FaultRecordId` int NULL DEFAULT NULL COMMENT '故障记录ID',
  `MaintenanceType` int NOT NULL DEFAULT 0 COMMENT '维护类型：0常规维护，1故障维修，2返厂维修跟踪',
  `Status` int NOT NULL DEFAULT 0 COMMENT '状态：0待处理，1处理中，2已完成，3已取消',
  `CreatorId` int NOT NULL COMMENT '创建人ID',
  `CreateTime` datetime NOT NULL COMMENT '创建时间',
  `AssigneeId` int NULL DEFAULT NULL COMMENT '处理人ID',
  `AssignTime` datetime NULL DEFAULT NULL COMMENT '分配时间',
  `PlanStartTime` datetime NULL DEFAULT NULL COMMENT '计划开始时间',
  `PlanEndTime` datetime NULL DEFAULT NULL COMMENT '计划结束时间',
  `ActualStartTime` datetime NULL DEFAULT NULL COMMENT '实际开始时间',
  `ActualEndTime` datetime NULL DEFAULT NULL COMMENT '实际结束时间',
  `Solution` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '解决方案',
  `Notes` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `CreatedAt` datetime NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE INDEX `IX_MaintenanceOrders_OrderCode`(`OrderCode` ASC) USING BTREE,
  INDEX `IX_MaintenanceOrders_AssetId`(`AssetId` ASC) USING BTREE,
  INDEX `IX_MaintenanceOrders_FaultRecordId`(`FaultRecordId` ASC) USING BTREE,
  INDEX `IX_MaintenanceOrders_CreatorId`(`CreatorId` ASC) USING BTREE,
  INDEX `IX_MaintenanceOrders_AssigneeId`(`AssigneeId` ASC) USING BTREE,
  CONSTRAINT `FK_MaintenanceOrders_Assets_AssetId` FOREIGN KEY (`AssetId`) REFERENCES `assets` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_MaintenanceOrders_FaultRecords_FaultRecordId` FOREIGN KEY (`FaultRecordId`) REFERENCES `faultrecords` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_MaintenanceOrders_Users_AssigneeId` FOREIGN KEY (`AssigneeId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_MaintenanceOrders_Users_CreatorId` FOREIGN KEY (`CreatorId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '维护订单表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of maintenanceorders
-- ----------------------------

-- ----------------------------
-- Table structure for menus
-- ----------------------------
DROP TABLE IF EXISTS `menus`;
CREATE TABLE `menus`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `Name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '菜单名称',
  `Code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '菜单编码',
  `ParentId` int NULL DEFAULT NULL COMMENT '父菜单ID',
  `Type` int NOT NULL DEFAULT 0 COMMENT '菜单类型：0菜单，1按钮',
  `Icon` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '图标',
  `Path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '路由路径',
  `Component` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '组件路径',
  `Permission` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '权限标识',
  `Description` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '描述',
  `SortOrder` int NOT NULL DEFAULT 0 COMMENT '排序',
  `IsExternal` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否外链',
  `KeepAlive` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否缓存',
  `IsVisible` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否可见',
  `IsActive` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否激活',
  `CreatedAt` datetime NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE INDEX `IX_Menus_Code`(`Code` ASC) USING BTREE,
  INDEX `IX_Menus_ParentId`(`ParentId` ASC) USING BTREE,
  CONSTRAINT `FK_Menus_Menus_ParentId` FOREIGN KEY (`ParentId`) REFERENCES `menus` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 27 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '菜单表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of menus
-- ----------------------------
INSERT INTO `menus` VALUES (1, '首页', 'DASHBOARD', NULL, 0, 'dashboard', '/dashboard', 'Dashboard', 'dashboard', '系统首页', 1, 0, 1, 1, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `menus` VALUES (2, '资产管理', 'ASSET', NULL, 0, 'computer', '/asset', 'Layout', 'asset', '资产管理模块', 2, 0, 1, 1, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `menus` VALUES (3, '故障管理', 'FAULT', NULL, 0, 'bug', '/fault', 'Layout', 'fault', '故障管理模块', 3, 0, 1, 1, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `menus` VALUES (4, '维修管理', 'MAINTENANCE', NULL, 0, 'tool', '/maintenance', 'Layout', 'maintenance', '维修管理模块', 4, 0, 1, 1, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `menus` VALUES (5, '采购管理', 'PURCHASE', NULL, 0, 'shopping-cart', '/purchase', 'Layout', 'purchase', '采购管理模块', 5, 0, 1, 1, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `menus` VALUES (6, '系统管理', 'SYSTEM', NULL, 0, 'setting', '/system', 'Layout', 'system', '系统管理模块', 6, 0, 1, 1, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `menus` VALUES (7, '任务管理', 'TASK', NULL, 0, 'calendar', '/task', 'Layout', 'task', '任务管理模块', 7, 0, 1, 1, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `menus` VALUES (8, '资产列表', 'ASSET:LIST', 2, 0, 'list', '/asset/list', 'asset/List', 'asset:list', '资产列表', 1, 0, 1, 1, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `menus` VALUES (9, '资产类型', 'ASSET:TYPE', 2, 0, 'tag', '/asset/type', 'asset/Type', 'asset:type', '资产类型管理', 2, 0, 1, 1, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `menus` VALUES (10, '位置管理', 'ASSET:LOCATION', 2, 0, 'environment', '/asset/location', 'asset/Location', 'asset:location', '位置管理', 3, 0, 1, 1, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `menus` VALUES (11, '故障列表', 'FAULT:LIST', 3, 0, 'exception', '/fault/list', 'fault/List', 'fault:list', '故障列表', 1, 0, 1, 1, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `menus` VALUES (12, '故障类型', 'FAULT:TYPE', 3, 0, 'tag', '/fault/type', 'fault/Type', 'fault:type', '故障类型管理', 2, 0, 1, 1, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `menus` VALUES (13, '返厂维修', 'FAULT:RETURN', 3, 0, 'rollback', '/fault/return', 'fault/Return', 'fault:return', '返厂维修管理', 3, 0, 1, 1, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `menus` VALUES (14, '维修列表', 'MAINTENANCE:LIST', 4, 0, 'build', '/maintenance/list', 'maintenance/List', 'maintenance:list', '维修列表', 1, 0, 1, 1, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `menus` VALUES (15, '维修统计', 'MAINTENANCE:STATS', 4, 0, 'bar-chart', '/maintenance/stats', 'maintenance/Stats', 'maintenance:stats', '维修统计', 2, 0, 1, 1, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `menus` VALUES (16, '采购单', 'PURCHASE:ORDER', 5, 0, 'file-text', '/purchase/order', 'purchase/Order', 'purchase:order', '采购单管理', 1, 0, 1, 1, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `menus` VALUES (17, '供应商', 'PURCHASE:SUPPLIER', 5, 0, 'team', '/purchase/supplier', 'purchase/Supplier', 'purchase:supplier', '供应商管理', 2, 0, 1, 1, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `menus` VALUES (18, '资产入库', 'PURCHASE:RECEIVE', 5, 0, 'import', '/purchase/receive', 'purchase/Receive', 'purchase:receive', '资产入库', 3, 0, 1, 1, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `menus` VALUES (19, '用户管理', 'SYSTEM:USER', 6, 0, 'user', '/system/user', 'system/User', 'system:user', '用户管理', 1, 0, 1, 1, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `menus` VALUES (20, '角色管理', 'SYSTEM:ROLE', 6, 0, 'team', '/system/role', 'system/Role', 'system:role', '角色管理', 2, 0, 1, 1, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `menus` VALUES (21, '菜单管理', 'SYSTEM:MENU', 6, 0, 'menu', '/system/menu', 'system/Menu', 'system:menu', '菜单管理', 3, 0, 1, 1, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `menus` VALUES (22, '部门管理', 'SYSTEM:DEPARTMENT', 6, 0, 'apartment', '/system/department', 'system/Department', 'system:department', '部门管理', 4, 0, 1, 1, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `menus` VALUES (23, '日志管理', 'SYSTEM:LOG', 6, 0, 'file-text', '/system/log', 'system/Log', 'system:log', '日志管理', 5, 0, 1, 1, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `menus` VALUES (24, '任务列表', 'TASK:LIST', 7, 0, 'unordered-list', '/task/list', 'task/List', 'task:list', '任务列表', 1, 0, 1, 1, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `menus` VALUES (25, '周期任务', 'TASK:PERIODIC', 7, 0, 'reload', '/task/periodic', 'task/Periodic', 'task:periodic', '周期任务管理', 2, 0, 1, 1, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `menus` VALUES (26, 'PDCA计划', 'TASK:PDCA', 7, 0, 'sync', '/task/pdca', 'task/Pdca', 'task:pdca', 'PDCA计划管理', 3, 0, 1, 1, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');

-- ----------------------------
-- Table structure for pdcaplans
-- ----------------------------
DROP TABLE IF EXISTS `pdcaplans`;
CREATE TABLE `pdcaplans`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `Title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '计划标题',
  `Type` int NOT NULL DEFAULT 0 COMMENT '类型：0硬件，1软件，2流程',
  `PlanStartDate` datetime NOT NULL COMMENT '计划开始日期',
  `PlanEndDate` datetime NOT NULL COMMENT '计划结束日期',
  `ActualEndDate` datetime NULL DEFAULT NULL COMMENT '实际结束日期',
  `Status` int NOT NULL DEFAULT 0 COMMENT '状态：0计划，1进行中，2已完成，3已取消',
  `CompletionRate` decimal(5, 2) NOT NULL DEFAULT 0.00 COMMENT '完成率',
  `ResponsiblePersonId` int NOT NULL COMMENT '负责人ID',
  `Notes` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `CreatedAt` datetime NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`) USING BTREE,
  INDEX `IX_PdcaPlans_ResponsiblePersonId`(`ResponsiblePersonId` ASC) USING BTREE,
  CONSTRAINT `FK_PdcaPlans_Users_ResponsiblePersonId` FOREIGN KEY (`ResponsiblePersonId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'PDCA计划表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of pdcaplans
-- ----------------------------

-- ----------------------------
-- Table structure for periodicrules
-- ----------------------------
DROP TABLE IF EXISTS `periodicrules`;
CREATE TABLE `periodicrules`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `Name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '规则名称',
  `Frequency` int NOT NULL DEFAULT 0 COMMENT '频率：0每天，1每周，2每月，3每季度，4每年',
  `DayOfWeek` int NULL DEFAULT NULL COMMENT '每周几（0-6）',
  `DayOfMonth` int NULL DEFAULT NULL COMMENT '每月几号（1-31）',
  `IsActive` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否激活',
  `CreatedAt` datetime NOT NULL COMMENT '创建时间',
  `LastGeneratedAt` datetime NULL DEFAULT NULL COMMENT '上次生成时间',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE INDEX `IX_PeriodicRules_Name`(`Name` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '周期规则表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of periodicrules
-- ----------------------------

-- ----------------------------
-- Table structure for purchaseitems
-- ----------------------------
DROP TABLE IF EXISTS `purchaseitems`;
CREATE TABLE `purchaseitems`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `PurchaseOrderId` int NOT NULL COMMENT '采购订单ID',
  `ItemName` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '项目名称',
  `ItemCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '项目编码',
  `Specification` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '规格',
  `AssetTypeId` int NULL DEFAULT NULL COMMENT '资产类型ID',
  `UnitPrice` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '单价',
  `Quantity` int NOT NULL DEFAULT 1 COMMENT '数量',
  `TotalPrice` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '总价',
  `Notes` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `CreatedAt` datetime NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`) USING BTREE,
  INDEX `IX_PurchaseItems_PurchaseOrderId`(`PurchaseOrderId` ASC) USING BTREE,
  INDEX `IX_PurchaseItems_AssetTypeId`(`AssetTypeId` ASC) USING BTREE,
  CONSTRAINT `FK_PurchaseItems_AssetTypes_AssetTypeId` FOREIGN KEY (`AssetTypeId`) REFERENCES `assettypes` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_PurchaseItems_PurchaseOrders_PurchaseOrderId` FOREIGN KEY (`PurchaseOrderId`) REFERENCES `purchaseorders` (`Id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '采购项目表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of purchaseitems
-- ----------------------------

-- ----------------------------
-- Table structure for purchaseorders
-- ----------------------------
DROP TABLE IF EXISTS `purchaseorders`;
CREATE TABLE `purchaseorders`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `OrderCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '订单编码',
  `Title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '标题',
  `Description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '描述',
  `SupplierId` int NOT NULL COMMENT '供应商ID',
  `Status` int NOT NULL DEFAULT 0 COMMENT '状态：0草稿，1待审批，2已审批，3已发出，4已收货，5已完成，6已取消',
  `EstimatedDeliveryDate` datetime NULL DEFAULT NULL COMMENT '预计交付日期',
  `ActualDeliveryDate` datetime NULL DEFAULT NULL COMMENT '实际交付日期',
  `ApplicantId` int NOT NULL COMMENT '申请人ID',
  `ApplicationTime` datetime NOT NULL COMMENT '申请时间',
  `ApproverId` int NULL DEFAULT NULL COMMENT '审批人ID',
  `ApprovalTime` datetime NULL DEFAULT NULL COMMENT '审批时间',
  `TotalAmount` decimal(18, 2) NOT NULL DEFAULT 0.00 COMMENT '总金额',
  `Notes` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `CreatedAt` datetime NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE INDEX `IX_PurchaseOrders_OrderCode`(`OrderCode` ASC) USING BTREE,
  INDEX `IX_PurchaseOrders_SupplierId`(`SupplierId` ASC) USING BTREE,
  INDEX `IX_PurchaseOrders_ApplicantId`(`ApplicantId` ASC) USING BTREE,
  INDEX `IX_PurchaseOrders_ApproverId`(`ApproverId` ASC) USING BTREE,
  CONSTRAINT `FK_PurchaseOrders_Suppliers_SupplierId` FOREIGN KEY (`SupplierId`) REFERENCES `suppliers` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_PurchaseOrders_Users_ApplicantId` FOREIGN KEY (`ApplicantId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_PurchaseOrders_Users_ApproverId` FOREIGN KEY (`ApproverId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '采购订单表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of purchaseorders
-- ----------------------------

-- ----------------------------
-- Table structure for refreshtokens
-- ----------------------------
DROP TABLE IF EXISTS `refreshtokens`;
CREATE TABLE `refreshtokens`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `UserId` int NOT NULL COMMENT '用户ID',
  `Token` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '令牌',
  `JwtId` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'JWT ID',
  `IsUsed` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已使用',
  `IsRevoked` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已撤销',
  `AddedDate` datetime NOT NULL COMMENT '添加日期',
  `ExpiryDate` datetime NOT NULL COMMENT '过期日期',
  PRIMARY KEY (`Id`) USING BTREE,
  INDEX `IX_RefreshTokens_UserId`(`UserId` ASC) USING BTREE,
  CONSTRAINT `FK_RefreshTokens_Users_UserId` FOREIGN KEY (`UserId`) REFERENCES `users` (`Id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '刷新令牌表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of refreshtokens
-- ----------------------------

-- ----------------------------
-- Table structure for returntofactories
-- ----------------------------
DROP TABLE IF EXISTS `returntofactories`;
CREATE TABLE `returntofactories`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `Code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '返厂单号',
  `AssetId` int NOT NULL COMMENT '资产ID',
  `FaultRecordId` int NOT NULL COMMENT '故障记录ID',
  `SupplierId` int NOT NULL COMMENT '供应商ID',
  `Status` int NOT NULL DEFAULT 0 COMMENT '状态：0待送出，1已送出，2维修中，3已返回，4维修失败',
  `SenderId` int NOT NULL COMMENT '送出人ID',
  `SendTime` datetime NULL DEFAULT NULL COMMENT '送出时间',
  `EstimatedReturnTime` datetime NULL DEFAULT NULL COMMENT '预计返回时间',
  `ActualReturnTime` datetime NULL DEFAULT NULL COMMENT '实际返回时间',
  `RepairResult` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '维修结果',
  `RepairCost` decimal(18, 2) NULL DEFAULT NULL COMMENT '维修费用',
  `InWarranty` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否在保修期内',
  `Notes` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `CreatedAt` datetime NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE INDEX `IX_ReturnToFactories_Code`(`Code` ASC) USING BTREE,
  INDEX `IX_ReturnToFactories_AssetId`(`AssetId` ASC) USING BTREE,
  INDEX `IX_ReturnToFactories_FaultRecordId`(`FaultRecordId` ASC) USING BTREE,
  INDEX `IX_ReturnToFactories_SupplierId`(`SupplierId` ASC) USING BTREE,
  INDEX `IX_ReturnToFactories_SenderId`(`SenderId` ASC) USING BTREE,
  CONSTRAINT `FK_ReturnToFactories_Assets_AssetId` FOREIGN KEY (`AssetId`) REFERENCES `assets` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_ReturnToFactories_FaultRecords_FaultRecordId` FOREIGN KEY (`FaultRecordId`) REFERENCES `faultrecords` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_ReturnToFactories_Suppliers_SupplierId` FOREIGN KEY (`SupplierId`) REFERENCES `suppliers` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_ReturnToFactories_Users_SenderId` FOREIGN KEY (`SenderId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '返厂维修表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of returntofactories
-- ----------------------------

-- ----------------------------
-- Table structure for rolemenus
-- ----------------------------
DROP TABLE IF EXISTS `rolemenus`;
CREATE TABLE `rolemenus`  (
  `RoleId` int NOT NULL COMMENT '角色ID',
  `MenuId` int NOT NULL COMMENT '菜单ID',
  `CreatedAt` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`RoleId`, `MenuId`) USING BTREE,
  INDEX `IX_RoleMenus_MenuId`(`MenuId` ASC) USING BTREE,
  CONSTRAINT `FK_RoleMenus_Menus_MenuId` FOREIGN KEY (`MenuId`) REFERENCES `menus` (`Id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `FK_RoleMenus_Roles_RoleId` FOREIGN KEY (`RoleId`) REFERENCES `roles` (`Id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '角色菜单关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of rolemenus
-- ----------------------------
INSERT INTO `rolemenus` VALUES (1, 1, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (1, 2, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (1, 3, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (1, 4, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (1, 5, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (1, 6, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (1, 7, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (1, 8, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (1, 9, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (1, 10, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (1, 11, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (1, 12, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (1, 13, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (1, 14, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (1, 15, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (1, 16, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (1, 17, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (1, 18, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (1, 19, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (1, 20, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (1, 21, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (1, 22, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (1, 23, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (1, 24, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (1, 25, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (1, 26, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (2, 1, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (2, 2, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (2, 3, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (2, 4, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (2, 5, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (2, 7, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (2, 8, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (2, 9, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (2, 10, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (2, 11, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (2, 12, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (2, 13, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (2, 14, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (2, 15, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (2, 16, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (2, 17, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (2, 18, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (2, 24, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (2, 25, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (2, 26, '2025-03-23 20:45:08');
INSERT INTO `rolemenus` VALUES (3, 1, '2025-03-23 20:45:09');
INSERT INTO `rolemenus` VALUES (3, 2, '2025-03-23 20:45:09');
INSERT INTO `rolemenus` VALUES (3, 8, '2025-03-23 20:45:09');
INSERT INTO `rolemenus` VALUES (3, 9, '2025-03-23 20:45:09');
INSERT INTO `rolemenus` VALUES (3, 10, '2025-03-23 20:45:09');
INSERT INTO `rolemenus` VALUES (3, 24, '2025-03-23 20:45:09');
INSERT INTO `rolemenus` VALUES (4, 1, '2025-03-23 20:45:09');
INSERT INTO `rolemenus` VALUES (4, 2, '2025-03-23 20:45:09');
INSERT INTO `rolemenus` VALUES (4, 10, '2025-03-23 20:45:09');
INSERT INTO `rolemenus` VALUES (4, 24, '2025-03-23 20:45:09');
INSERT INTO `rolemenus` VALUES (5, 1, '2025-03-23 20:45:09');
INSERT INTO `rolemenus` VALUES (5, 3, '2025-03-23 20:45:09');
INSERT INTO `rolemenus` VALUES (5, 11, '2025-03-23 20:45:09');
INSERT INTO `rolemenus` VALUES (5, 12, '2025-03-23 20:45:09');
INSERT INTO `rolemenus` VALUES (5, 13, '2025-03-23 20:45:09');
INSERT INTO `rolemenus` VALUES (5, 24, '2025-03-23 20:45:09');
INSERT INTO `rolemenus` VALUES (6, 1, '2025-03-23 20:45:09');
INSERT INTO `rolemenus` VALUES (6, 4, '2025-03-23 20:45:09');
INSERT INTO `rolemenus` VALUES (6, 14, '2025-03-23 20:45:09');
INSERT INTO `rolemenus` VALUES (6, 24, '2025-03-23 20:45:09');
INSERT INTO `rolemenus` VALUES (7, 1, '2025-03-23 20:45:09');
INSERT INTO `rolemenus` VALUES (7, 8, '2025-03-23 20:45:09');
INSERT INTO `rolemenus` VALUES (7, 11, '2025-03-23 20:45:09');
INSERT INTO `rolemenus` VALUES (7, 24, '2025-03-23 20:45:09');

-- ----------------------------
-- Table structure for roles
-- ----------------------------
DROP TABLE IF EXISTS `roles`;
CREATE TABLE `roles`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `Name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '角色名称',
  `Code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '角色编码',
  `Description` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '角色描述',
  `IsActive` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否激活',
  `CreatedAt` datetime NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE INDEX `IX_Roles_Code`(`Code` ASC) USING BTREE,
  UNIQUE INDEX `IX_Roles_Name`(`Name` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '角色表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of roles
-- ----------------------------
INSERT INTO `roles` VALUES (1, '超级管理员', 'SUPER_ADMIN', '系统超级管理员，拥有所有权限', 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `roles` VALUES (2, 'IT管理员', 'IT_ADMIN', 'IT部门管理员，管理IT资产和系统配置', 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `roles` VALUES (3, '资产管理员', 'ASSET_ADMIN', '负责资产的录入、调拨和报废管理', 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `roles` VALUES (4, '位置管理员', 'LOCATION_ADMIN', '负责管理位置和资产位置分配', 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `roles` VALUES (5, '故障管理员', 'FAULT_ADMIN', '负责故障的处理和维修管理', 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `roles` VALUES (6, '维修人员', 'MAINTENANCE_STAFF', '执行设备维修和保养任务', 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');
INSERT INTO `roles` VALUES (7, '普通用户', 'NORMAL_USER', '普通用户，可以查看和使用设备', 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08');

-- ----------------------------
-- Table structure for suppliers
-- ----------------------------
DROP TABLE IF EXISTS `suppliers`;
CREATE TABLE `suppliers`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `Name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '供应商名称',
  `Code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '供应商编码',
  `ContactPerson` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '联系人',
  `ContactPhone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '联系电话',
  `ContactEmail` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '联系邮箱',
  `Address` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '地址',
  `Type` int NOT NULL DEFAULT 0 COMMENT '类型：0硬件，1软件，2服务',
  `Notes` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `IsActive` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否激活',
  `CreatedAt` datetime NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE INDEX `IX_Suppliers_Code`(`Code` ASC) USING BTREE,
  UNIQUE INDEX `IX_Suppliers_Name`(`Name` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '供应商表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of suppliers
-- ----------------------------
INSERT INTO `suppliers` VALUES (1, '联想集团', 'LENOVO', '王经理', '13566778899', '<EMAIL>', '北京市海淀区联想大厦', 0, '电脑设备主要供应商', 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `suppliers` VALUES (2, '戴尔科技', 'DELL', '李经理', '13677889900', '<EMAIL>', '上海市浦东新区张江高科技园区', 0, '服务器主要供应商', 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `suppliers` VALUES (3, '华为技术', 'HUAWEI', '张经理', '13788990011', '<EMAIL>', '深圳市龙岗区华为基地', 0, '网络设备主要供应商', 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `suppliers` VALUES (4, '西门子工业', 'SIEMENS', '赵经理', '13899001122', '<EMAIL>', '北京市朝阳区西门子大厦', 0, '工控机主要供应商', 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `suppliers` VALUES (5, '霍尼韦尔', 'HONEYWELL', '钱经理', '13900112233', '<EMAIL>', '上海市长宁区霍尼韦尔大厦', 0, '扫描设备主要供应商', 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `suppliers` VALUES (6, '佳能', 'CANON', '孙经理', '13911223344', '<EMAIL>', '北京市朝阳区佳能大厦', 0, '打印设备主要供应商', 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `suppliers` VALUES (7, '微软', 'MICROSOFT', '周经理', '13922334455', '<EMAIL>', '北京市海淀区微软大厦', 1, '软件主要供应商', 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `suppliers` VALUES (8, '东方通信', 'EASTCOM', '吴经理', '13933445566', '<EMAIL>', '杭州市滨江区东方通信大厦', 0, '通信设备供应商', 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `suppliers` VALUES (9, '普联技术', 'TP-LINK', '郑经理', '13944556677', '<EMAIL>', '深圳市南山区普联科技大厦', 0, '网络设备供应商', 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');
INSERT INTO `suppliers` VALUES (10, '科大讯飞', 'IFLYTEK', '冯经理', '13955667788', '<EMAIL>', '合肥市高新区科大讯飞大厦', 1, '语音识别技术供应商', 1, '2025-03-23 20:45:09', '2025-03-23 20:45:09');

-- ----------------------------
-- Table structure for tasks
-- ----------------------------
DROP TABLE IF EXISTS `tasks`;
CREATE TABLE `tasks`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `Title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '任务标题',
  `Description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '任务描述',
  `TaskType` int NOT NULL DEFAULT 0 COMMENT '任务类型：0普通任务，1周期任务，2计划任务',
  `Status` int NOT NULL DEFAULT 0 COMMENT '状态：0待处理，1处理中，2已完成，3已取消',
  `AssignedToId` int NULL DEFAULT NULL COMMENT '分配给谁',
  `CreatedById` int NOT NULL COMMENT '创建人ID',
  `CreatedAt` datetime NOT NULL COMMENT '创建时间',
  `DueDate` datetime NULL DEFAULT NULL COMMENT '截止日期',
  `CompletedAt` datetime NULL DEFAULT NULL COMMENT '完成时间',
  `RelatedAssetId` int NULL DEFAULT NULL COMMENT '相关资产ID',
  `RelatedLocationId` int NULL DEFAULT NULL COMMENT '相关位置ID',
  `PeriodicRuleId` int NULL DEFAULT NULL COMMENT '周期规则ID',
  `PdcaPlanId` int NULL DEFAULT NULL COMMENT 'PDCA计划ID',
  `Notes` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `UpdatedAt` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`Id`) USING BTREE,
  INDEX `IX_Tasks_AssignedToId`(`AssignedToId` ASC) USING BTREE,
  INDEX `IX_Tasks_CreatedById`(`CreatedById` ASC) USING BTREE,
  INDEX `IX_Tasks_RelatedAssetId`(`RelatedAssetId` ASC) USING BTREE,
  INDEX `IX_Tasks_RelatedLocationId`(`RelatedLocationId` ASC) USING BTREE,
  INDEX `IX_Tasks_PeriodicRuleId`(`PeriodicRuleId` ASC) USING BTREE,
  INDEX `IX_Tasks_PdcaPlanId`(`PdcaPlanId` ASC) USING BTREE,
  CONSTRAINT `FK_Tasks_Assets_RelatedAssetId` FOREIGN KEY (`RelatedAssetId`) REFERENCES `assets` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_Tasks_Locations_RelatedLocationId` FOREIGN KEY (`RelatedLocationId`) REFERENCES `locations` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_Tasks_PdcaPlans_PdcaPlanId` FOREIGN KEY (`PdcaPlanId`) REFERENCES `pdcaplans` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_Tasks_PeriodicRules_PeriodicRuleId` FOREIGN KEY (`PeriodicRuleId`) REFERENCES `periodicrules` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_Tasks_Users_AssignedToId` FOREIGN KEY (`AssignedToId`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_Tasks_Users_CreatedById` FOREIGN KEY (`CreatedById`) REFERENCES `users` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '任务表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of tasks
-- ----------------------------

-- ----------------------------
-- Table structure for userroles
-- ----------------------------
DROP TABLE IF EXISTS `userroles`;
CREATE TABLE `userroles`  (
  `UserId` int NOT NULL COMMENT '用户ID',
  `RoleId` int NOT NULL COMMENT '角色ID',
  `CreatedAt` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`UserId`, `RoleId`) USING BTREE,
  INDEX `IX_UserRoles_RoleId`(`RoleId` ASC) USING BTREE,
  CONSTRAINT `FK_UserRoles_Roles_RoleId` FOREIGN KEY (`RoleId`) REFERENCES `roles` (`Id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `FK_UserRoles_Users_UserId` FOREIGN KEY (`UserId`) REFERENCES `users` (`Id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户角色关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of userroles
-- ----------------------------
INSERT INTO `userroles` VALUES (1, 1, '2025-03-23 20:45:08');
INSERT INTO `userroles` VALUES (2, 2, '2025-03-23 20:45:08');
INSERT INTO `userroles` VALUES (3, 3, '2025-03-23 20:45:08');
INSERT INTO `userroles` VALUES (4, 4, '2025-03-23 20:45:08');
INSERT INTO `userroles` VALUES (5, 5, '2025-03-23 20:45:08');
INSERT INTO `userroles` VALUES (6, 6, '2025-03-23 20:45:08');
INSERT INTO `userroles` VALUES (7, 7, '2025-03-23 20:45:08');
INSERT INTO `userroles` VALUES (8, 7, '2025-03-23 20:45:08');
INSERT INTO `userroles` VALUES (9, 7, '2025-03-23 20:45:08');

-- ----------------------------
-- Table structure for users
-- ----------------------------
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users`  (
  `Id` int NOT NULL AUTO_INCREMENT,
  `Username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户名',
  `PasswordHash` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '密码哈希',
  `SecurityStamp` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '安全戳',
  `Name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '姓名',
  `Email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '邮箱',
  `Mobile` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '手机号',
  `DepartmentId` int NULL DEFAULT NULL COMMENT '部门ID',
  `Position` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '职位',
  `Gender` int NOT NULL DEFAULT 0 COMMENT '性别：0未知，1男，2女',
  `DefaultRoleId` int NULL DEFAULT NULL COMMENT '默认角色ID',
  `IsActive` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否激活',
  `CreatedAt` datetime NOT NULL COMMENT '创建时间',
  `UpdatedAt` datetime NOT NULL COMMENT '更新时间',
  `LastLoginAt` datetime NULL DEFAULT NULL COMMENT '最后登录时间',
  `Avatar` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '头像',
  PRIMARY KEY (`Id`) USING BTREE,
  UNIQUE INDEX `IX_Users_Username`(`Username` ASC) USING BTREE,
  UNIQUE INDEX `IX_Users_Email`(`Email` ASC) USING BTREE,
  INDEX `IX_Users_DepartmentId`(`DepartmentId` ASC) USING BTREE,
  INDEX `IX_Users_DefaultRoleId`(`DefaultRoleId` ASC) USING BTREE,
  CONSTRAINT `FK_Users_Departments_DepartmentId` FOREIGN KEY (`DepartmentId`) REFERENCES `departments` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK_Users_Roles_DefaultRoleId` FOREIGN KEY (`DefaultRoleId`) REFERENCES `roles` (`Id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 22 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of users
-- ----------------------------
INSERT INTO `users` VALUES (1, 'admin', 'AQAAAAIAAYagAAAAECkIJmNaLvk9o9JVh7RoQTI9VvL6B8U3uLQkzQvCeD2qcHKJWmjGsdzIgFg3Mg==', 'E89F4B8C79F14A798D97D57E97D45D46', '系统管理员', '<EMAIL>', '13800000000', 1, '管理员', 0, 1, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08', NULL, NULL);
INSERT INTO `users` VALUES (2, 'itadmin', 'AQAAAAIAAYagAAAAECkIJmNaLvk9o9JVh7RoQTI9VvL6B8U3uLQkzQvCeD2qcHKJWmjGsdzIgFg3Mg==', 'E89F4B8C79F14A798D97D57E97D45D46', 'IT管理员', '<EMAIL>', '13800000001', 2, '管理员', 1, 2, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08', NULL, NULL);
INSERT INTO `users` VALUES (3, 'assetmgr', 'AQAAAAIAAYagAAAAECkIJmNaLvk9o9JVh7RoQTI9VvL6B8U3uLQkzQvCeD2qcHKJWmjGsdzIgFg3Mg==', 'E89F4B8C79F14A798D97D57E97D45D46', '资产管理员', '<EMAIL>', '13800000002', 2, '经理', 1, 3, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08', NULL, NULL);
INSERT INTO `users` VALUES (4, 'locationmgr', 'AQAAAAIAAYagAAAAECkIJmNaLvk9o9JVh7RoQTI9VvL6B8U3uLQkzQvCeD2qcHKJWmjGsdzIgFg3Mg==', 'E89F4B8C79F14A798D97D57E97D45D46', '位置管理员', '<EMAIL>', '13800000003', 2, '经理', 1, 4, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08', NULL, NULL);
INSERT INTO `users` VALUES (5, 'faultmgr', 'AQAAAAIAAYagAAAAECkIJmNaLvk9o9JVh7RoQTI9VvL6B8U3uLQkzQvCeD2qcHKJWmjGsdzIgFg3Mg==', 'E89F4B8C79F14A798D97D57E97D45D46', '故障管理员', '<EMAIL>', '13800000004', 2, '经理', 1, 5, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08', NULL, NULL);
INSERT INTO `users` VALUES (6, 'maintenance', 'AQAAAAIAAYagAAAAECkIJmNaLvk9o9JVh7RoQTI9VvL6B8U3uLQkzQvCeD2qcHKJWmjGsdzIgFg3Mg==', 'E89F4B8C79F14A798D97D57E97D45D46', '维修人员', '<EMAIL>', '13800000005', 2, '技术员', 1, 6, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08', NULL, NULL);
INSERT INTO `users` VALUES (7, 'user1', 'AQAAAAIAAYagAAAAECkIJmNaLvk9o9JVh7RoQTI9VvL6B8U3uLQkzQvCeD2qcHKJWmjGsdzIgFg3Mg==', 'E89F4B8C79F14A798D97D57E97D45D46', '张三', '<EMAIL>', '13900000001', 3, '职员', 1, 7, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08', NULL, NULL);
INSERT INTO `users` VALUES (8, 'user2', 'AQAAAAIAAYagAAAAECkIJmNaLvk9o9JVh7RoQTI9VvL6B8U3uLQkzQvCeD2qcHKJWmjGsdzIgFg3Mg==', 'E89F4B8C79F14A798D97D57E97D45D46', '李四', '<EMAIL>', '13900000002', 4, '职员', 1, 7, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08', NULL, NULL);
INSERT INTO `users` VALUES (9, 'user3', 'AQAAAAIAAYagAAAAECkIJmNaLvk9o9JVh7RoQTI9VvL6B8U3uLQkzQvCeD2qcHKJWmjGsdzIgFg3Mg==', 'E89F4B8C79F14A798D97D57E97D45D46', '王五', '<EMAIL>', '13900000003', 5, '职员', 2, 7, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08', NULL, NULL);
INSERT INTO `users` VALUES (10, 'engineer1', 'AQAAAAIAAYagAAAAECkIJmNaLvk9o9JVh7RoQTI9VvL6B8U3uLQkzQvCeD2qcHKJWmjGsdzIgFg3Mg==', 'E89F4B8C79F14A798D97D57E97D45D46', '工程师1', '<EMAIL>', '13900000004', 6, '工程师', 1, 7, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08', NULL, NULL);
INSERT INTO `users` VALUES (11, 'M01', 'AQAAAAIAAYagAAAAECkIJmNaLvk9o9JVh7RoQTI9VvL6B8U3uLQkzQvCeD2qcHKJWmjGsdzIgFg3Mg==', 'E89F4B8C79F14A798D97D57E97D45D46', '王经理', '<EMAIL>', '13900000001', 10, '经理', 0, 7, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08', NULL, NULL);
INSERT INTO `users` VALUES (12, 'M02', 'AQAAAAIAAYagAAAAECkIJmNaLvk9o9JVh7RoQTI9VvL6B8U3uLQkzQvCeD2qcHKJWmjGsdzIgFg3Mg==', 'E89F4B8C79F14A798D97D57E97D45D46', '李经理', '<EMAIL>', '13900000002', 11, '经理', 0, 7, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08', NULL, NULL);
INSERT INTO `users` VALUES (13, 'M03', 'AQAAAAIAAYagAAAAECkIJmNaLvk9o9JVh7RoQTI9VvL6B8U3uLQkzQvCeD2qcHKJWmjGsdzIgFg3Mg==', 'E89F4B8C79F14A798D97D57E97D45D46', '张经理', '<EMAIL>', '13900000003', 12, '经理', 0, 7, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08', NULL, NULL);
INSERT INTO `users` VALUES (14, 'M04', 'AQAAAAIAAYagAAAAECkIJmNaLvk9o9JVh7RoQTI9VvL6B8U3uLQkzQvCeD2qcHKJWmjGsdzIgFg3Mg==', 'E89F4B8C79F14A798D97D57E97D45D46', '刘经理', '<EMAIL>', '13900000004', 13, '经理', 0, 7, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08', NULL, NULL);
INSERT INTO `users` VALUES (15, 'M05', 'AQAAAAIAAYagAAAAECkIJmNaLvk9o9JVh7RoQTI9VvL6B8U3uLQkzQvCeD2qcHKJWmjGsdzIgFg3Mg==', 'E89F4B8C79F14A798D97D57E97D45D46', '赵经理', '<EMAIL>', '13900000005', 14, '组长', 0, 7, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08', NULL, NULL);
INSERT INTO `users` VALUES (16, 'M06', 'AQAAAAIAAYagAAAAECkIJmNaLvk9o9JVh7RoQTI9VvL6B8U3uLQkzQvCeD2qcHKJWmjGsdzIgFg3Mg==', 'E89F4B8C79F14A798D97D57E97D45D46', '钱经理', '<EMAIL>', '13900000006', 15, '组长', 0, 7, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08', NULL, NULL);
INSERT INTO `users` VALUES (17, 'U01', 'AQAAAAIAAYagAAAAECkIJmNaLvk9o9JVh7RoQTI9VvL6B8U3uLQkzQvCeD2qcHKJWmjGsdzIgFg3Mg==', 'E89F4B8C79F14A798D97D57E97D45D46', '张操作员', '<EMAIL>', '13911111101', 10, '操作员', 0, 7, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08', NULL, NULL);
INSERT INTO `users` VALUES (18, 'U02', 'AQAAAAIAAYagAAAAECkIJmNaLvk9o9JVh7RoQTI9VvL6B8U3uLQkzQvCeD2qcHKJWmjGsdzIgFg3Mg==', 'E89F4B8C79F14A798D97D57E97D45D46', '王操作员', '<EMAIL>', '13911111102', 10, '操作员', 0, 7, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08', NULL, NULL);
INSERT INTO `users` VALUES (19, 'U03', 'AQAAAAIAAYagAAAAECkIJmNaLvk9o9JVh7RoQTI9VvL6B8U3uLQkzQvCeD2qcHKJWmjGsdzIgFg3Mg==', 'E89F4B8C79F14A798D97D57E97D45D46', '李操作员', '<EMAIL>', '13911111103', 11, '操作员', 0, 7, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08', NULL, NULL);
INSERT INTO `users` VALUES (20, 'U04', 'AQAAAAIAAYagAAAAECkIJmNaLvk9o9JVh7RoQTI9VvL6B8U3uLQkzQvCeD2qcHKJWmjGsdzIgFg3Mg==', 'E89F4B8C79F14A798D97D57E97D45D46', '赵操作员', '<EMAIL>', '13911111104', 12, '操作员', 0, 7, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08', NULL, NULL);
INSERT INTO `users` VALUES (21, 'U05', 'AQAAAAIAAYagAAAAECkIJmNaLvk9o9JVh7RoQTI9VvL6B8U3uLQkzQvCeD2qcHKJWmjGsdzIgFg3Mg==', 'E89F4B8C79F14A798D97D57E97D45D46', '钱操作员', '<EMAIL>', '13911111105', 13, '操作员', 0, 7, 1, '2025-03-23 20:45:08', '2025-03-23 20:45:08', NULL, NULL);

SET FOREIGN_KEY_CHECKS = 1;
