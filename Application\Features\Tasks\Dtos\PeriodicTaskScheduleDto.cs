// File: Application/Features/Tasks/Dtos/PeriodicTaskScheduleDto.cs
// Description: 周期性任务计划数据传输对象
#nullable enable

using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using ItAssetsSystem.Models.Enums;

namespace ItAssetsSystem.Application.Features.Tasks.Dtos
{
    /// <summary>
    /// 周期性任务计划数据传输对象 (Response DTO)
    /// </summary>
    public class PeriodicTaskScheduleDto
    {
        /// <summary>
        /// 计划ID
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// 计划名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 计划描述
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// 模板任务ID
        /// </summary>
        public long TemplateTaskId { get; set; }

        /// <summary>
        /// 模板任务名称
        /// </summary>
        public string? TemplateTaskName { get; set; }

        /// <summary>
        /// 任务模板标题
        /// </summary>
        public string? TaskTemplateTitle { get; set; }

        /// <summary>
        /// 任务模板描述
        /// </summary>
        public string? TaskTemplateDescription { get; set; }

        /// <summary>
        /// 周期类型
        /// </summary>
        public string? RecurrenceType { get; set; }

        /// <summary>
        /// 周期间隔
        /// </summary>
        public int RecurrenceInterval { get; set; }

        /// <summary>
        /// 星期几（用于每周重复）
        /// </summary>
        public string? DaysOfWeek { get; set; }

        /// <summary>
        /// 日期（用于每月重复）
        /// </summary>
        public int? DayOfMonth { get; set; }

        /// <summary>
        /// 月中周数（用于每月特定周重复）
        /// </summary>
        public int? WeekOfMonth { get; set; }

        /// <summary>
        /// 月中星期几（用于每月特定周的特定天重复）
        /// </summary>
        public int? DayOfWeekForMonth { get; set; }

        /// <summary>
        /// 年中月份（用于每年重复）
        /// </summary>
        public int? MonthOfYear { get; set; }

        /// <summary>
        /// Cron表达式
        /// </summary>
        public string? CronExpression { get; set; }

        /// <summary>
        /// 周期规则
        /// </summary>
        public string? RecurrenceRule { get; set; }

        /// <summary>
        /// 周期规则的友好描述
        /// </summary>
        public string? RecurrenceDescription { get; set; }

        /// <summary>
        /// 计划开始时间
        /// </summary>
        public DateTime StartDate { get; set; }

        /// <summary>
        /// 计划结束时间
        /// </summary>
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// 结束条件类型
        /// </summary>
        public string? EndConditionType { get; set; }

        /// <summary>
        /// 计划总共生成次数
        /// </summary>
        public int? TotalOccurrences { get; set; }

        /// <summary>
        /// 已生成任务次数
        /// </summary>
        public int OccurrencesGenerated { get; set; }

        /// <summary>
        /// 下次任务生成时间
        /// </summary>
        public DateTime? NextGenerationTime { get; set; }

        /// <summary>
        /// 最后一次生成时间
        /// </summary>
        public DateTime? LastGenerationTime { get; set; }

        /// <summary>
        /// 最后生成的时间戳
        /// </summary>
        public DateTime? LastGeneratedTimestamp { get; set; }

        /// <summary>
        /// 默认负责人ID
        /// </summary>
        public int DefaultAssigneeUserId { get; set; }

        /// <summary>
        /// 默认负责人名称
        /// </summary>
        public string? DefaultAssigneeUserName { get; set; }

        /// <summary>
        /// 默认负责人ID列表（支持多个负责人）
        /// </summary>
        public List<int>? DefaultAssigneeUserIds { get; set; }

        /// <summary>
        /// 创建者ID
        /// </summary>
        public int CreatorUserId { get; set; }

        /// <summary>
        /// 创建者名称
        /// </summary>
        public string? CreatorUserName { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreationTimestamp { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? LastUpdatedTimestamp { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public string? Status { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; }

        /// <summary>
        /// 默认积分
        /// </summary>
        public int DefaultPoints { get; set; }

        /// <summary>
        /// 默认优先级
        /// </summary>
        public string? DefaultPriority { get; set; }

        /// <summary>
        /// 默认优先级显示名称
        /// </summary>
        public string? DefaultPriorityDisplayName { get; set; }

        /// <summary>
        /// 默认任务持续天数
        /// </summary>
        public int DefaultDurationDays { get; set; }

        /// <summary>
        /// 默认资产ID
        /// </summary>
        public int? DefaultAssetId { get; set; }

        /// <summary>
        /// 默认资产名称
        /// </summary>
        public string? DefaultAssetName { get; set; }

        /// <summary>
        /// 默认位置ID
        /// </summary>
        public int? DefaultLocationId { get; set; }

        /// <summary>
        /// 默认位置名称
        /// </summary>
        public string? DefaultLocationName { get; set; }

        /// <summary>
        /// 最近生成的任务列表
        /// </summary>
        public List<TaskDto>? RecentGeneratedTasks { get; set; } = new List<TaskDto>();
    }

    /// <summary>
    /// 创建周期性任务计划请求DTO
    /// </summary>
    public class CreatePeriodicTaskScheduleRequestDto
    {
        /// <summary>
        /// 计划名称
        /// </summary>
        [Required(ErrorMessage = "计划名称不能为空")]
        [StringLength(200, ErrorMessage = "计划名称不能超过200个字符")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 计划描述
        /// </summary>
        [StringLength(2000, ErrorMessage = "计划描述不能超过2000个字符")]
        public string? Description { get; set; }

        /// <summary>
        /// 模板任务ID
        /// </summary>
        [Required(ErrorMessage = "模板任务ID不能为空")]
        public long TemplateTaskId { get; set; }

        /// <summary>
        /// 任务模板标题
        /// </summary>
        [StringLength(200, ErrorMessage = "任务标题模板不能超过200个字符")]
        public string? TaskTemplateTitle { get; set; }

        /// <summary>
        /// 任务模板描述
        /// </summary>
        [StringLength(4000, ErrorMessage = "任务描述模板不能超过4000个字符")]
        public string? TaskTemplateDescription { get; set; }

        /// <summary>
        /// 周期类型
        /// </summary>
        public string? RecurrenceType { get; set; }

        /// <summary>
        /// 周期间隔
        /// </summary>
        public int? RecurrenceInterval { get; set; }

        /// <summary>
        /// 星期几（用于每周重复）
        /// </summary>
        public string? DaysOfWeek { get; set; }

        /// <summary>
        /// 日期（用于每月重复）
        /// </summary>
        public int? DayOfMonth { get; set; }

        /// <summary>
        /// 月中周数（用于每月特定周重复）
        /// </summary>
        public int? WeekOfMonth { get; set; }

        /// <summary>
        /// 月中星期几（用于每月特定周的特定天重复）
        /// </summary>
        public int? DayOfWeekForMonth { get; set; }

        /// <summary>
        /// 年中月份（用于每年重复）
        /// </summary>
        public int? MonthOfYear { get; set; }

        /// <summary>
        /// Cron表达式
        /// </summary>
        [Required(ErrorMessage = "Cron表达式不能为空")]
        public string CronExpression { get; set; } = string.Empty;

        /// <summary>
        /// 周期规则
        /// </summary>
        public string? RecurrenceRule { get; set; }

        /// <summary>
        /// 计划开始时间
        /// </summary>
        [Required(ErrorMessage = "计划开始时间不能为空")]
        public DateTime StartDate { get; set; }

        /// <summary>
        /// 计划结束时间
        /// </summary>
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// 结束条件类型
        /// </summary>
        public string? EndConditionType { get; set; }

        /// <summary>
        /// 计划总共生成次数
        /// </summary>
        public int? TotalOccurrences { get; set; }

        /// <summary>
        /// 默认负责人ID
        /// </summary>
        public int? DefaultAssigneeUserId { get; set; }

        /// <summary>
        /// 默认负责人ID列表（支持多个负责人）
        /// </summary>
        public List<int>? DefaultAssigneeUserIds { get; set; }

        /// <summary>
        /// 默认优先级
        /// </summary>
        public TaskPriority? DefaultPriority { get; set; }

        /// <summary>
        /// 默认任务持续天数
        /// </summary>
        [Range(1, 365, ErrorMessage = "默认任务持续天数必须在1-365之间")]
        public int? DefaultDurationDays { get; set; } = 1;

        /// <summary>
        /// 默认资产ID
        /// </summary>
        public int? DefaultAssetId { get; set; }

        /// <summary>
        /// 默认位置ID
        /// </summary>
        public int? DefaultLocationId { get; set; }

        /// <summary>
        /// 周几（用于周任务，1-7代表周一至周日）
        /// </summary>
        public int? WeekDay { get; set; }

        /// <summary>
        /// 月中日期（用于月任务，1-31代表日期）
        /// </summary>
        public int? MonthDay { get; set; }
    }

    /// <summary>
    /// 更新周期性任务计划请求DTO
    /// </summary>
    public class UpdatePeriodicTaskScheduleRequestDto
    {
        /// <summary>
        /// 计划名称
        /// </summary>
        [StringLength(200, ErrorMessage = "计划名称不能超过200个字符")]
        public string? Name { get; set; }

        /// <summary>
        /// 计划描述
        /// </summary>
        [StringLength(2000, ErrorMessage = "计划描述不能超过2000个字符")]
        public string? Description { get; set; }

        /// <summary>
        /// 模板任务ID
        /// </summary>
        public long? TemplateTaskId { get; set; }

        /// <summary>
        /// 任务模板标题
        /// </summary>
        [StringLength(200, ErrorMessage = "任务标题模板不能超过200个字符")]
        public string? TaskTemplateTitle { get; set; }

        /// <summary>
        /// 任务模板描述
        /// </summary>
        [StringLength(4000, ErrorMessage = "任务描述模板不能超过4000个字符")]
        public string? TaskTemplateDescription { get; set; }

        /// <summary>
        /// 周期类型
        /// </summary>
        public string? RecurrenceType { get; set; }

        /// <summary>
        /// 周期间隔
        /// </summary>
        public int? RecurrenceInterval { get; set; }

        /// <summary>
        /// 星期几（用于每周重复）
        /// </summary>
        public string? DaysOfWeek { get; set; }

        /// <summary>
        /// 日期（用于每月重复）
        /// </summary>
        public int? DayOfMonth { get; set; }

        /// <summary>
        /// 月中周数（用于每月特定周重复）
        /// </summary>
        public int? WeekOfMonth { get; set; }

        /// <summary>
        /// 月中星期几（用于每月特定周的特定天重复）
        /// </summary>
        public int? DayOfWeekForMonth { get; set; }

        /// <summary>
        /// 年中月份（用于每年重复）
        /// </summary>
        public int? MonthOfYear { get; set; }

        /// <summary>
        /// Cron表达式
        /// </summary>
        public string? CronExpression { get; set; }

        /// <summary>
        /// 周期规则
        /// </summary>
        public string? RecurrenceRule { get; set; }

        /// <summary>
        /// 计划开始时间
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// 计划结束时间
        /// </summary>
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public string? Status { get; set; }

        /// <summary>
        /// 默认负责人ID
        /// </summary>
        public int? DefaultAssigneeUserId { get; set; }

        /// <summary>
        /// 默认负责人ID列表（支持多个负责人）
        /// </summary>
        public List<int>? DefaultAssigneeUserIds { get; set; }

        /// <summary>
        /// 默认优先级
        /// </summary>
        public TaskPriority? DefaultPriority { get; set; }

        /// <summary>
        /// 默认任务持续天数
        /// </summary>
        [Range(1, 365, ErrorMessage = "默认任务持续天数必须在1-365之间")]
        public int? DefaultDurationDays { get; set; }

        /// <summary>
        /// 默认资产ID
        /// </summary>
        public int? DefaultAssetId { get; set; }

        /// <summary>
        /// 默认位置ID
        /// </summary>
        public int? DefaultLocationId { get; set; }
    }
} 