<template>
  <div class="profile-view">
    <div class="page-header">个人中心</div>
    
    <el-row :gutter="20">
      <!-- 左侧个人信息 -->
      <el-col :span="8">
        <el-card shadow="hover" class="user-card">
          <div class="user-info">
            <el-avatar :size="100" :src="user.avatar" />
            <h2 class="user-name">{{ user.name }}</h2>
            <div class="user-title">{{ user.department }} - {{ user.position }}</div>
            
            <div class="user-level">
              <span class="level-label">Level {{ user.level }}</span>
              <el-progress :percentage="levelProgress" :stroke-width="10" status="success">
                <span>{{ user.score }} / {{ nextLevelScore }}</span>
              </el-progress>
            </div>
            
            <div class="user-meta">
              <div class="meta-item">
                <div class="meta-label">加入时间</div>
                <div class="meta-value">{{ user.joinDate }}</div>
              </div>
              <div class="meta-item">
                <div class="meta-label">总经验</div>
                <div class="meta-value">{{ user.experiencePoints }}</div>
              </div>
              <div class="meta-item">
                <div class="meta-label">金币</div>
                <div class="meta-value">
                  <img src="https://cdn-icons-png.flaticon.com/512/2933/2933116.png" class="coin-icon-sm" />
                  {{ user.gold }}
                </div>
              </div>
              <div class="meta-item">
                <div class="meta-label">钻石</div>
                <div class="meta-value">
                  <img src="https://cdn-icons-png.flaticon.com/512/2933/2933151.png" class="diamond-icon-sm" />
                  {{ user.diamonds }}
                </div>
              </div>
              <div class="meta-item">
                <div class="meta-label">排名</div>
                <div class="meta-value">{{ user.rank }}</div>
              </div>
            </div>
          </div>
          
          <el-divider />
          
          <div class="contact-info">
            <h3>联系方式</h3>
            <div class="contact-item">
              <el-icon><Message /></el-icon>
              <span>{{ user.email }}</span>
            </div>
            <div class="contact-item">
              <el-icon><Phone /></el-icon>
              <span>{{ user.phone }}</span>
            </div>
          </div>
        </el-card>
        
        <!-- 我的标签 -->
        <el-card shadow="hover" class="tags-card">
          <template #header>
            <div class="card-header">
              <span>我的标签</span>
              <el-button type="text">编辑</el-button>
            </div>
          </template>
          <div class="tags-content">
            <el-tag v-for="tag in user.tags" :key="tag" class="user-tag">{{ tag }}</el-tag>
          </div>
        </el-card>
      </el-col>
      
      <!-- 右侧内容区 -->
      <el-col :span="16">
        <!-- 任务统计 -->
        <el-card shadow="hover" class="stats-card">
          <template #header>
            <div class="card-header">
              <span>任务统计</span>
            </div>
          </template>
          
          <el-row :gutter="20" class="stat-row">
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-value">{{ taskStats.total }}</div>
                <div class="stat-label">总任务数</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-value">{{ taskStats.completed }}</div>
                <div class="stat-label">已完成</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-value">{{ taskStats.onTime }}%</div>
                <div class="stat-label">按时完成率</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-value">{{ taskStats.pending }}</div>
                <div class="stat-label">待处理</div>
              </div>
            </el-col>
          </el-row>
          
          <div class="chart-area">
            <div class="chart-placeholder">任务完成趋势图表</div>
          </div>
        </el-card>
        
        <!-- 我的成就 -->
        <el-card shadow="hover" class="achievements-card">
          <template #header>
            <div class="card-header">
              <span>我的成就</span>
              <el-button type="text" @click="showAllAchievements = !showAllAchievements">
                {{ showAllAchievements ? '收起' : '查看全部' }}
              </el-button>
            </div>
          </template>
          
          <div class="achievements-grid">
            <div v-for="achievement in displayedAchievements" :key="achievement.id" class="achievement-item">
              <el-avatar :size="40" :src="achievement.icon || user.avatar" class="achievement-icon" />
              <div class="achievement-info">
                <div class="achievement-name">{{ achievement.name }}</div>
                <div class="achievement-desc">{{ achievement.description }}</div>
                <div class="achievement-date">{{ achievement.unlockTime }}</div>
              </div>
            </div>
          </div>
        </el-card>
        
        <!-- 近期任务 -->
        <el-card shadow="hover" class="recent-tasks-card">
          <template #header>
            <div class="card-header">
              <span>近期任务</span>
              <el-button type="text" @click="router.push('/tasks')">查看全部</el-button>
            </div>
          </template>
          
          <el-table :data="recentTasks" style="width: 100%">
            <el-table-column prop="title" label="任务名称" min-width="180" />
            <el-table-column prop="dueDate" label="截止时间" width="120" />
            <el-table-column label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="getStatusType(row.status)">
                  {{ getStatusLabel(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120">
              <template #default="{ row }">
                <el-button type="primary" link @click="viewTask(row.id)">查看</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { useRouter } from 'vue-router'
import { Message, Phone, Star, Trophy } from '@element-plus/icons-vue'

const router = useRouter()

// 用户信息
const user = reactive({
  id: 'user1',
  name: '张三',
  avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
  department: '研发部',
  position: '前端开发工程师',
  email: '<EMAIL>',
  phone: '13800138000',
  joinDate: '2022-01-15',
  level: 5,
  score: 320,
  experiencePoints: 15500,
  gold: 950,
  diamonds: 35,
  rank: 5,
  tags: ['Vue.js', 'React', 'TypeScript', '用户体验', '团队合作']
})

// 下一级所需积分
const nextLevelScore = computed(() => user.experiencePoints + 1000)

// 当前等级进度
const levelProgress = computed(() => {
  const currentLevelExp = 14500
  const progress = ((user.experiencePoints - currentLevelExp) / (nextLevelScore.value - currentLevelExp)) * 100
  return Math.min(Math.max(progress, 0), 100)
})

// 任务统计
const taskStats = reactive({
  total: 67,
  completed: 42,
  pending: 8,
  onTime: 92
})

// 成就列表
const achievements = reactive([
  { id: 1, name: '任务达人', description: '完成超过50个任务', unlockTime: '2024-01-10', icon: null },
  { id: 2, name: '早起鸟', description: '连续7天早上8点前签到', unlockTime: '2024-02-15', icon: null },
  { id: 3, name: '团队支柱', description: '在一个月内协助10个同事完成任务', unlockTime: '2024-03-22', icon: null },
  { id: 4, name: '高效能', description: '连续完成5个任务且全部提前交付', unlockTime: '2024-04-05', icon: null },
  { id: 5, name: '及时雨', description: '解决3个紧急问题', unlockTime: '2024-05-01', icon: null },
  { id: 6, name: '学习达人', description: '完成10门在线课程', unlockTime: '2024-05-10', icon: null }
])

// 是否显示全部成就
const showAllAchievements = ref(false)

// 展示的成就列表
const displayedAchievements = computed(() => {
  return showAllAchievements.value ? achievements : achievements.slice(0, 3)
})

// 近期任务
const recentTasks = reactive([
  { id: 'task1', title: '优化用户注册流程', dueDate: '2024-05-10', status: 'unstarted' },
  { id: 'task2', title: '修复移动端显示问题', dueDate: '2024-05-12', status: 'in-progress' },
  { id: 'task5', title: '每周项目进度汇报', dueDate: '2024-05-07', status: 'unstarted' },
  { id: 'task4', title: '实现数据导出功能', dueDate: '2024-04-30', status: 'completed' }
])

// 查看任务详情
function viewTask(taskId: string) {
  router.push({ name: 'taskDetail', params: { id: taskId } })
}

// 获取状态样式
function getStatusType(status: string) {
  switch (status) {
    case 'unstarted':
      return 'info'
    case 'in-progress':
      return 'warning'
    case 'completed':
      return 'success'
    case 'overdue':
      return 'danger'
    default:
      return 'info'
  }
}

// 获取状态文本
function getStatusLabel(status: string) {
  switch (status) {
    case 'unstarted':
      return '未开始'
    case 'in-progress':
      return '进行中'
    case 'completed':
      return '已完成'
    case 'overdue':
      return '已逾期'
    default:
      return '未知'
  }
}
</script>

<style scoped>
.profile-view {
  padding-bottom: 40px;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

.page-header {
  font-size: 20px;
  font-weight: 500;
  margin-bottom: 20px;
}

.user-card, .tags-card, .stats-card, .achievements-card, .recent-tasks-card {
  margin-bottom: 20px;
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 0;
}

.user-name {
  margin: 15px 0 5px 0;
}

.user-title {
  color: #909399;
  margin-bottom: 15px;
}

.user-level {
  width: 100%;
  margin: 15px 0;
}

.level-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
}

.user-meta {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
  margin-top: 20px;
  text-align: center;
}

.meta-item {
  text-align: center;
}

.meta-label {
  color: #606266;
  font-size: 12px;
  margin-bottom: 5px;
}

.meta-value {
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
}

.coin-icon-sm, .diamond-icon-sm {
  width: 14px;
  height: 14px;
  margin-right: 4px;
}

.contact-info {
  padding: 10px 0;
}

.contact-info h3 {
  margin-bottom: 15px;
  font-size: 16px;
}

.contact-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.contact-item i {
  margin-right: 10px;
  color: #909399;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tags-content {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.user-tag {
  margin-right: 0;
}

.stat-row {
  margin-bottom: 20px;
}

.stat-item {
  text-align: center;
  padding: 15px 0;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: var(--el-color-primary);
}

.stat-label {
  color: #909399;
  margin-top: 5px;
}

.chart-area {
  margin-top: 20px;
}

.chart-placeholder {
  height: 200px;
  background-color: #f5f7fa;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #909399;
  border-radius: 4px;
}

.achievements-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
}

.achievement-item {
  display: flex;
  gap: 15px;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.achievement-icon {
  background-color: #ecf5ff;
}

.achievement-name {
  font-weight: 500;
  margin-bottom: 5px;
}

.achievement-desc {
  color: #606266;
  font-size: 13px;
  margin-bottom: 5px;
}

.achievement-date {
  color: #909399;
  font-size: 12px;
}

/* 响应式布局 */
@media (max-width: 1200px) {
  .el-row {
    margin-right: 0 !important;
    margin-left: 0 !important;
  }
}

@media (max-width: 992px) {
  .achievements-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .stat-row {
    flex-direction: column;
  }
  
  .stat-item {
    margin-bottom: 10px;
  }
  
  .user-meta {
    flex-direction: column;
    align-items: center;
  }
  
  .meta-item {
    margin-bottom: 10px;
  }
}
</style> 