// 认证管理器
#pragma once

#include "common/types.h"
#include <string>
#include <memory>
#include <mutex>
#include <thread>
#include <atomic>

namespace notification {

// 前向声明
class Logger;
class ConfigManager;

// JWT令牌信息
struct TokenInfo {
    String access_token;
    String refresh_token;
    TimePoint expires_at;
    String user_id;
    String username;
    String roles;
};

class AuthManager {
public:
    explicit AuthManager(std::shared_ptr<ConfigManager> config_manager);
    ~AuthManager();

    // 初始化
    bool initialize();
    void shutdown();

    // 认证操作
    bool login(const String& username, const String& password);
    bool loginWithToken(const String& token);
    void logout();
    bool refreshToken();

    // 状态查询
    bool isAuthenticated() const;
    AuthStatus getAuthStatus() const { return auth_status_.load(); }
    TokenInfo getTokenInfo() const;

    // 令牌管理
    String getCurrentToken() const;
    bool isTokenExpired() const;
    Duration getTokenTimeToExpire() const;

    // 自动刷新
    void enableAutoRefresh(bool enable = true);
    bool isAutoRefreshEnabled() const { return auto_refresh_enabled_.load(); }

    // 回调设置
    void setAuthStatusCallback(AuthStatusCallback callback);

    // API认证头部
    String getAuthorizationHeader() const;
    
    // 匿名模式支持
    bool supportsAnonymous() const;
    void enableAnonymousMode(bool enable = true);

private:
    // 网络请求
    struct HttpResponse {
        int status_code = 0;
        String body;
        String error;
        bool success = false;
    };

    HttpResponse makeHttpRequest(const String& url, const String& method, 
                                const String& data = "", const String& content_type = "application/json");
    
    // 认证API调用
    bool performLogin(const String& username, const String& password);
    bool performTokenRefresh();
    bool validateToken(const String& token);

    // 令牌解析
    bool parseLoginResponse(const String& response);
    bool parseRefreshResponse(const String& response);
    TokenInfo parseJwtToken(const String& token);

    // 状态管理
    void setAuthStatus(AuthStatus status);
    void updateTokenInfo(const TokenInfo& token_info);
    void clearTokenInfo();

    // 自动刷新线程
    void startAutoRefreshWorker();
    void stopAutoRefreshWorker();
    void autoRefreshWorker();

    // JSON解析辅助
    String extractJsonValue(const String& json, const String& key);
    bool isValidJson(const String& json);

    // Base64解码（用于JWT解析）
    String base64Decode(const String& encoded);

private:
    std::shared_ptr<ConfigManager> config_manager_;
    std::shared_ptr<Logger> logger_;

    // 认证状态
    std::atomic<AuthStatus> auth_status_{AuthStatus::NOT_AUTHENTICATED};
    std::atomic<bool> auto_refresh_enabled_{true};
    std::atomic<bool> anonymous_mode_enabled_{false};

    // 令牌信息
    mutable std::mutex token_mutex_;
    TokenInfo current_token_;

    // 自动刷新线程
    std::thread auto_refresh_thread_;
    std::atomic<bool> stop_auto_refresh_{false};

    // 回调函数
    mutable std::mutex callback_mutex_;
    AuthStatusCallback auth_status_callback_;

    // 配置缓存
    String api_base_url_;
    bool support_anonymous_ = true;
    Duration token_refresh_threshold_{std::chrono::minutes(5)};
};

} // namespace notification