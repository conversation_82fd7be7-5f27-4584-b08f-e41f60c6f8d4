<template>
  <div 
    class="task-card" 
    :class="[
      `priority-${task.priority?.toLowerCase()}`,
      { 
        selected: selected,
        overdue: task.isOverdue
      }
    ]"
    :data-task-id="task.taskId"
    @click="handleClick"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
  >
    <!-- 选择框 -->
    <div class="task-card-checkbox" @click.stop>
      <el-checkbox 
        :model-value="selected" 
        @change="toggleSelection"
        size="small"
      />
    </div>

    <!-- 任务标题 -->
    <div class="task-title" @click.stop="$emit('click', task)">
      {{ task.name }}
    </div>

    <!-- 任务描述（如果有） -->
    <div v-if="task.description" class="task-description">
      {{ truncateText(task.description, 100) }}
    </div>

    <!-- 任务标签 -->
    <div v-if="task.tagList && task.tagList.length > 0" class="task-tags">
      <el-tag 
        v-for="tag in task.tagList.slice(0, 3)" 
        :key="tag"
        size="small"
        class="task-tag"
      >
        {{ tag }}
      </el-tag>
      <span v-if="task.tagList.length > 3" class="more-tags">
        +{{ task.tagList.length - 3 }}
      </span>
    </div>

    <!-- 进度条 -->
    <div class="task-progress">
      <div class="progress-info">
        <span class="progress-text">{{ task.progress }}%</span>
        <span class="progress-date" v-if="task.planEndDate">
          {{ formatDate(task.planEndDate) }}
        </span>
      </div>
      <el-progress 
        :percentage="task.progress" 
        :stroke-width="4"
        :show-text="false"
        :color="getProgressColor(task.progress)"
      />
    </div>

    <!-- 底部信息栏 -->
    <div class="task-footer">
      <!-- 负责人头像 -->
      <div class="assignees">
        <UserAvatarStack
          :users="getAllAssignees(task)"
          :is-main-user-primary="true"
          :max-users="3"
          avatar-size="20"
          :overlap="7"
        />
      </div>

      <!-- 任务元信息 -->
      <div class="task-meta">
        <!-- 评论数 -->
        <div v-if="task.commentCount > 0" class="meta-item">
          <el-icon class="meta-icon"><ChatLineRound /></el-icon>
          <span>{{ task.commentCount }}</span>
        </div>
        
        <!-- 附件数 -->
        <div v-if="task.attachmentCount > 0" class="meta-item">
          <el-icon class="meta-icon"><Paperclip /></el-icon>
          <span>{{ task.attachmentCount }}</span>
        </div>
        
        <!-- 子任务数 -->
        <div v-if="task.subTaskCount > 0" class="meta-item">
          <el-icon class="meta-icon"><Collection /></el-icon>
          <span>{{ task.subTaskCount }}</span>
        </div>

        <!-- 优先级标识 -->
        <el-tag 
          :type="getPriorityTagType(task.priority)"
          size="small"
          round
          class="priority-tag"
        >
          {{ getPriorityText(task.priority) }}
        </el-tag>
      </div>
    </div>

    <!-- 逾期提醒 -->
    <div v-if="task.isOverdue" class="overdue-badge">
      <el-icon><WarningFilled /></el-icon>
      逾期
    </div>

    <!-- PDCA阶段标识 -->
    <div v-if="task.taskType === 'PDCA' && task.pdcaStage" class="pdca-badge">
      {{ task.pdcaStage }}
    </div>

    <!-- 积分奖励 -->
    <div v-if="task.points > 0" class="points-badge">
      <el-icon><Star /></el-icon>
      {{ task.points }}
    </div>

    <!-- 任务认领状态和操作区域 -->
    <div class="task-actions">
      <!-- 任务认领按钮 -->
      <el-button
        v-if="canClaimTask"
        size="small"
        type="success"
        @click.stop="handleClaimTask"
        :loading="claiming"
        class="claim-button"
      >
        <el-icon><Trophy /></el-icon>
        今日认领
      </el-button>

      <!-- 已认领标识 -->
      <el-tag
        v-else-if="isClaimedByCurrentUser"
        type="success"
        size="small"
        class="claimed-tag"
      >
        <el-icon><User /></el-icon>
        已认领
      </el-tag>

      <!-- 其他人已认领 -->
      <el-tag
        v-else-if="isClaimedByOthers"
        type="info"
        size="small"
        class="claimed-tag"
      >
        <el-icon><User /></el-icon>
        {{ task.claimedByUserName || '其他人' }}已认领
      </el-tag>

      <!-- 编辑按钮 -->
      <el-button
        size="small"
        type="primary"
        @click.stop="$emit('edit', task)"
        circle
        plain
        class="edit-button"
      >
        <el-icon><Edit /></el-icon>
      </el-button>
    </div>

    <!-- 任务完成水印 -->
    <div
      v-if="task.status === 'Done' && task.completedByUserName"
      class="completion-watermark"
    >
      {{ task.completedByUserName }}
    </div>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'
import { formatDate } from '@/utils/format'
import { getFullAvatarUrl } from '@/stores/modules/user'
import { useUserStore } from '@/stores/modules/user'
import { useGamificationStore } from '@/stores/modules/gamification'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Trophy, User, Edit } from '@element-plus/icons-vue'
import UserAvatarStack from '@/components/UserAvatarStack.vue'
import { claimTask as claimTaskApi } from '@/api/gamification'

const props = defineProps({
  task: {
    type: Object,
    required: true
  },
  selected: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['click', 'select', 'mouseenter', 'mouseleave', 'edit', 'claim', 'refresh'])

// 状态管理
const userStore = useUserStore()
const gamificationStore = useGamificationStore()
const claiming = ref(false)

// 当前用户信息
const currentUser = computed(() => userStore.userInfo)

// 方法
const handleClick = (event) => {
  emit('click', props.task, event)
}

const handleMouseEnter = (event) => {
  emit('mouseenter', props.task, event)
}

const handleMouseLeave = (event) => {
  emit('mouseleave', props.task, event)
}

const toggleSelection = (checked) => {
  emit('select', props.task.taskId, { ctrlKey: true })
}

// 任务认领相关计算属性
const canClaimTask = computed(() => {
  // 任务可以被认领的条件：
  // 1. 任务状态不是已完成或已取消
  // 2. 当前用户是任务的负责人之一（主负责人或多负责人中的一员）
  // 3. 当前用户今日没有领取过该任务
  // 4. 当前用户已登录

  if (!currentUser.value?.id) return false

  // 检查当前用户是否为任务负责人
  const isMainAssignee = props.task.assigneeUserId === currentUser.value?.id
  const isMultiAssignee = props.task.assignees && props.task.assignees.some(assignee => assignee.userId === currentUser.value?.id)
  const isAssignedToTask = isMainAssignee || isMultiAssignee

  // 🔧 修复多负责人领取逻辑：
  // - 如果当前用户不是负责人，不能领取
  // - 如果当前用户是负责人，但已经领取过（claimedByUserId === 当前用户ID），不能重复领取
  // - 如果当前用户是负责人，且未领取过，可以领取（即使其他负责人已领取）
  const currentUserAlreadyClaimed = props.task.claimedByUserId === currentUser.value?.id

  return props.task.status !== 'Done' &&
         props.task.status !== 'Cancelled' &&
         isAssignedToTask &&  // 当前用户必须是负责人之一
         !currentUserAlreadyClaimed  // 当前用户今日未领取（允许其他负责人已领取）
})

const isClaimedByCurrentUser = computed(() => {
  // 检查任务是否被当前用户认领
  return props.task.claimedByUserId === currentUser.value?.id
})

const isClaimedByOthers = computed(() => {
  // 检查任务是否被其他人认领
  return props.task.claimedByUserId && props.task.claimedByUserId !== currentUser.value?.id
})

// 任务认领处理
const handleClaimTask = async () => {
  if (!canClaimTask.value) {
    ElMessage.warning('该任务无法认领')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确认认领任务"${props.task.name}"？认领后将获得游戏化奖励！`,
      '任务认领',
      {
        confirmButtonText: '确认认领',
        cancelButtonText: '取消',
        type: 'info'
      }
    )

    claiming.value = true

    // 调用游戏化任务认领API
    const response = await claimTaskApi(props.task.taskId, `用户主动认领任务: ${props.task.name}`)

    if (response.success) {
      ElMessage.success('任务认领成功！获得奖励积分')

      // 触发游戏化事件记录
      gamificationStore.recordEvent('task_claimed', {
        taskId: props.task.taskId,
        taskName: props.task.name
      })

      // 通知父组件刷新数据
      emit('claim', props.task)
      emit('refresh')
    } else {
      ElMessage.error(response.message || '任务认领失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('任务认领失败:', error)
      ElMessage.error('任务认领失败')
    }
  } finally {
    claiming.value = false
  }
}

const truncateText = (text, maxLength) => {
  if (!text || text.length <= maxLength) return text
  return text.substring(0, maxLength) + '...'
}

const getProgressColor = (progress) => {
  if (progress >= 80) return '#67c23a'
  if (progress >= 50) return '#e6a23c'
  return '#f56c6c'
}

const getPriorityTagType = (priority) => {
  const types = {
    'High': 'danger',
    'Medium': 'warning', 
    'Low': 'success',
    'Urgent': 'danger'
  }
  return types[priority] || ''
}

const getPriorityText = (priority) => {
  const texts = {
    'High': '高',
    'Medium': '中',
    'Low': '低', 
    'Urgent': '紧急'
  }
  return texts[priority] || priority
}

const handleAvatarError = (event) => {
  // 头像加载失败时的处理
  console.warn('Avatar loading failed:', event)
}

// 获取所有负责人和协作者 - 修复显示逻辑
const getAllAssignees = (task) => {
  if (!task) return []

  const assignees = []

  // 记录开始处理日志
  console.log(`处理任务卡片[${task.taskId}]的负责人信息：`, task)

  // 优先使用后端返回的 assignees 字段（包含完整的负责人信息）
  if (task.assignees && Array.isArray(task.assignees) && task.assignees.length > 0) {
    task.assignees.forEach(assignee => {
      assignees.push({
        id: assignee.userId,
        userId: assignee.userId,
        name: assignee.userName || '未知用户',
        userName: assignee.userName || '未知用户',
        avatarUrl: assignee.avatarUrl || '',
        role: assignee.role || (assignee.assignmentType === 'Assignee' ? 'Primary' : 'Collaborator')
      })
      console.log(`添加负责人: ${assignee.userName || '未知用户'}(ID:${assignee.userId}, Role:${assignee.role})`)
    })
  } else {
    // 如果没有 assignees 字段，回退到旧逻辑（仅显示主负责人）
    if (task.assigneeUserId) {
      const avatarUrl = task.assigneeAvatarUrl || task.assigneeAvatar ||
                       (task.assignee?.avatarUrl) || (task.assignee?.avatar) || ''

      assignees.push({
        id: task.assigneeUserId,
        userId: task.assigneeUserId,
        name: task.assigneeUserName || '未知用户',
        userName: task.assigneeUserName || '未知用户',
        avatarUrl: avatarUrl,
        role: 'Primary'
      })
      console.log(`回退逻辑 - 添加主负责人: ${task.assigneeUserName || '未知用户'}(ID:${task.assigneeUserId})`)
    }
  }

  console.log(`最终负责人列表:`, assignees)
  return assignees
}
</script>

<style scoped>
.task-card {
  background: white;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #e5e7eb;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  user-select: none;
}

.task-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.task-card.selected {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.task-card.overdue {
  border-left-color: #f56c6c;
  background: linear-gradient(135deg, #fff 0%, #fef2f2 100%);
}

/* 优先级边框颜色 */
.task-card.priority-high {
  border-left-color: #f56c6c;
}

.task-card.priority-medium {
  border-left-color: #e6a23c;
}

.task-card.priority-low {
  border-left-color: #67c23a;
}

.task-card.priority-urgent {
  border-left-color: #ff4d4f;
  background: linear-gradient(135deg, #fff 0%, #fff2f0 100%);
}

.task-card-checkbox {
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 10;
}

.task-title {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
  line-height: 1.4;
  cursor: pointer;
  word-break: break-word;
}

.task-title:hover {
  color: #409eff;
}

.task-description {
  font-size: 12px;
  color: #606266;
  margin-bottom: 12px;
  line-height: 1.4;
  word-break: break-word;
}

.task-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  margin-bottom: 12px;
}

.task-tag {
  font-size: 10px;
  height: 20px;
  line-height: 20px;
  padding: 0 6px;
}

.more-tags {
  font-size: 10px;
  color: #909399;
  background: #f4f4f5;
  padding: 2px 6px;
  border-radius: 10px;
  line-height: 16px;
}

.task-progress {
  margin-bottom: 12px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.progress-text {
  font-size: 12px;
  font-weight: 600;
  color: #303133;
}

.progress-date {
  font-size: 10px;
  color: #909399;
}

.task-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.assignees {
  display: flex;
  align-items: center;
  gap: 4px;
}

.task-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 2px;
  font-size: 12px;
  color: #909399;
}

.meta-icon {
  font-size: 12px;
}

.priority-tag {
  font-size: 10px;
  height: 18px;
  line-height: 18px;
  padding: 0 6px;
}

.overdue-badge {
  position: absolute;
  top: -2px;
  left: -2px;
  background: #f56c6c;
  color: white;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 2px;
}

.pdca-badge {
  position: absolute;
  top: 8px;
  left: 8px;
  background: #409eff;
  color: white;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: bold;
}

.points-badge {
  position: absolute;
  top: 30px;
  right: 8px;
  background: #ffb400;
  color: white;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 2px;
}

/* 暗色主题 */
.dark .task-card {
  background: #2d2d2d;
  color: #e0e0e0;
  border-left-color: #4a4a4a;
}

.dark .task-title {
  color: #e0e0e0;
}

.dark .task-title:hover {
  color: #409eff;
}

.dark .task-description {
  color: #a0a0a0;
}

.dark .progress-text {
  color: #e0e0e0;
}

.dark .progress-date {
  color: #a0a0a0;
}

.dark .meta-item {
  color: #a0a0a0;
}

.dark .more-tags,
.dark .more-participants {
  background: #404040;
  color: #a0a0a0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .task-card {
    padding: 12px;
  }
  
  .task-title {
    font-size: 13px;
  }
  
  .task-description {
    font-size: 11px;
  }
  
  .task-meta {
    gap: 6px;
  }
  
  .meta-item {
    font-size: 11px;
  }
}

.task-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
  margin-top: 8px;
  position: relative;
}

.claim-button {
  font-size: 12px;
  padding: 4px 8px;
  height: 24px;
  border-radius: 12px;
}

.claimed-tag {
  font-size: 10px;
  height: 20px;
  line-height: 20px;
  padding: 0 6px;
}

.edit-button {
  width: 24px;
  height: 24px;
  padding: 0;
}

/* 任务完成水印 */
.completion-watermark {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(-15deg);
  font-size: 24px;
  font-weight: bold;
  color: rgba(103, 194, 58, 0.3);
  pointer-events: none;
  z-index: 1;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
  border: 2px solid rgba(103, 194, 58, 0.3);
  padding: 8px 16px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.8);
}
</style>