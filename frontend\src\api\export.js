import request from '@/utils/request'

// 统一的导出函数
const exportData = (url, params, filename) => {
  return request.download(url, params, filename)
}

// 资产导出
export const exportAssets = (params) => {
  return exportData('/asset/export', params, `资产列表_${new Date().toISOString().substring(0, 10)}.xlsx`)
}

// 资产类型导出
export const exportAssetTypes = (params) => {
  return exportData('/assettype/export', params, `资产类型列表_${new Date().toISOString().substring(0, 10)}.xlsx`)
}

// 位置导出
export const exportLocations = (params) => {
  return exportData('/location/export', params, `位置列表_${new Date().toISOString().substring(0, 10)}.xlsx`)
}

// 部门导出
export const exportDepartments = (params) => {
  return exportData('/department/export', params, `部门列表_${new Date().toISOString().substring(0, 10)}.xlsx`)
}

// 用户导出
export const exportUsers = (params) => {
  return exportData('/user/export', params, `用户列表_${new Date().toISOString().substring(0, 10)}.xlsx`)
}