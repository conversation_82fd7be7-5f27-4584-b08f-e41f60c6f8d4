// File: Application/Features/Gamification/Configuration/GamificationServiceExtensions.cs
// Description: 游戏化系统服务注册扩展

using System;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using ItAssetsSystem.Application.Features.Gamification.Services;
using ItAssetsSystem.Application.Features.Gamification.EventHandlers;

namespace ItAssetsSystem.Application.Features.Gamification.Configuration
{
    /// <summary>
    /// 游戏化系统服务注册扩展
    /// </summary>
    public static class GamificationServiceExtensions
    {
        /// <summary>
        /// 添加游戏化系统服务
        /// </summary>
        /// <param name="services">服务集合</param>
        /// <param name="configuration">配置</param>
        /// <returns>服务集合</returns>
        public static IServiceCollection AddGamificationSystem(this IServiceCollection services, IConfiguration configuration)
        {
            // 注册核心服务
            services.AddScoped<IGamificationRuleEngine, GamificationRuleEngine>();
            services.AddScoped<IGamificationRewardProcessor, GamificationRewardProcessor>();
            services.AddScoped<IGamificationStatsUpdater, GamificationStatsUpdater>();

            // 注册事件处理器
            services.AddScoped<TaskCreatedGamificationHandler>();
            services.AddScoped<TaskCompletedGamificationHandler>();
            services.AddScoped<TaskDeletedGamificationHandler>();
            services.AddScoped<TaskClaimedGamificationHandler>();

            // 注册配置
            services.Configure<GamificationOptions>(configuration.GetSection("Gamification"));

            return services;
        }

        /// <summary>
        /// 添加游戏化系统默认规则
        /// </summary>
        /// <param name="services">服务集合</param>
        /// <returns>服务集合</returns>
        public static IServiceCollection AddDefaultGamificationRules(this IServiceCollection services)
        {
            services.AddHostedService<GamificationRuleInitializationService>();
            return services;
        }
    }

    /// <summary>
    /// 游戏化系统配置选项
    /// </summary>
    public class GamificationOptions
    {
        /// <summary>
        /// 是否启用游戏化系统
        /// </summary>
        public bool Enabled { get; set; } = true;

        /// <summary>
        /// 是否启用自动奖励
        /// </summary>
        public bool AutoRewardEnabled { get; set; } = true;

        /// <summary>
        /// 是否启用成就系统
        /// </summary>
        public bool AchievementEnabled { get; set; } = true;

        /// <summary>
        /// 是否启用排行榜
        /// </summary>
        public bool LeaderboardEnabled { get; set; } = true;

        /// <summary>
        /// 规则缓存过期时间（分钟）
        /// </summary>
        public int RuleCacheExpiryMinutes { get; set; } = 30;

        /// <summary>
        /// 默认奖励配置
        /// </summary>
        public DefaultRewards DefaultRewards { get; set; } = new DefaultRewards();

        /// <summary>
        /// 等级配置
        /// </summary>
        public LevelConfiguration LevelConfiguration { get; set; } = new LevelConfiguration();

        /// <summary>
        /// 客户端ID（多租户支持）
        /// </summary>
        public string? ClientId { get; set; }
    }

    /// <summary>
    /// 默认奖励配置
    /// </summary>
    public class DefaultRewards
    {
        /// <summary>
        /// 任务创建奖励
        /// </summary>
        public RewardConfig TaskCreated { get; set; } = new RewardConfig { Points = 5, Coins = 3, Diamonds = 0, Experience = 5 };

        /// <summary>
        /// 任务完成奖励
        /// </summary>
        public RewardConfig TaskCompleted { get; set; } = new RewardConfig { Points = 20, Coins = 10, Diamonds = 1, Experience = 20 };

        /// <summary>
        /// 任务认领奖励
        /// </summary>
        public RewardConfig TaskClaimed { get; set; } = new RewardConfig { Points = 3, Coins = 2, Diamonds = 0, Experience = 3 };

        /// <summary>
        /// 按时完成任务额外奖励
        /// </summary>
        public RewardConfig OnTimeBonus { get; set; } = new RewardConfig { Points = 10, Coins = 5, Diamonds = 0, Experience = 10 };

        /// <summary>
        /// 资产更新奖励
        /// </summary>
        public RewardConfig AssetUpdated { get; set; } = new RewardConfig { Points = 8, Coins = 5, Diamonds = 0, Experience = 8 };

        /// <summary>
        /// 故障报告奖励
        /// </summary>
        public RewardConfig FaultReported { get; set; } = new RewardConfig { Points = 15, Coins = 8, Diamonds = 0, Experience = 15 };

        /// <summary>
        /// 每日登录奖励
        /// </summary>
        public RewardConfig DailyLogin { get; set; } = new RewardConfig { Points = 2, Coins = 1, Diamonds = 0, Experience = 2 };

        /// <summary>
        /// 评论添加奖励
        /// </summary>
        public RewardConfig CommentAdded { get; set; } = new RewardConfig { Points = 1, Coins = 1, Diamonds = 0, Experience = 1 };
    }

    /// <summary>
    /// 奖励配置
    /// </summary>
    public class RewardConfig
    {
        /// <summary>
        /// 积分奖励
        /// </summary>
        public int Points { get; set; }

        /// <summary>
        /// 金币奖励
        /// </summary>
        public int Coins { get; set; }

        /// <summary>
        /// 钻石奖励
        /// </summary>
        public int Diamonds { get; set; }

        /// <summary>
        /// 经验值奖励
        /// </summary>
        public int Experience { get; set; }
    }

    /// <summary>
    /// 等级配置
    /// </summary>
    public class LevelConfiguration
    {
        /// <summary>
        /// 基础经验值（每级所需）
        /// </summary>
        public int BaseExperiencePerLevel { get; set; } = 100;

        /// <summary>
        /// 经验值增长系数
        /// </summary>
        public double ExperienceGrowthFactor { get; set; } = 1.2;

        /// <summary>
        /// 最大等级
        /// </summary>
        public int MaxLevel { get; set; } = 100;

        /// <summary>
        /// 计算指定等级所需的总经验值
        /// </summary>
        /// <param name="level">等级</param>
        /// <returns>所需总经验值</returns>
        public int CalculateRequiredExperience(int level)
        {
            if (level <= 1) return 0;

            int totalExperience = 0;
            for (int i = 1; i < level; i++)
            {
                totalExperience += (int)(BaseExperiencePerLevel * Math.Pow(ExperienceGrowthFactor, i - 1));
            }
            return totalExperience;
        }

        /// <summary>
        /// 根据经验值计算等级
        /// </summary>
        /// <param name="experience">当前经验值</param>
        /// <returns>等级</returns>
        public int CalculateLevel(int experience)
        {
            if (experience <= 0) return 1;

            int level = 1;
            int totalRequired = 0;

            while (level < MaxLevel)
            {
                int nextLevelRequired = CalculateRequiredExperience(level + 1);
                if (experience < nextLevelRequired)
                {
                    break;
                }
                level++;
            }

            return level;
        }
    }
}
