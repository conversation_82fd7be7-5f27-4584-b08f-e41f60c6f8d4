// IT资产管理系统 - 插件管理器
// 文件路径: /Core/Plugins/PluginManager.cs
// 功能: 提供插件的动态加载、卸载和管理功能，实现微内核+插件架构的核心组件

using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Runtime.Loader;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using ItAssetsSystem.Core.Abstractions;

namespace ItAssetsSystem.Core.Plugins
{
    /// <summary>
    /// 插件加载上下文，用于动态加载和卸载程序集
    /// </summary>
    public class PluginLoadContext : AssemblyLoadContext
    {
        private readonly AssemblyDependencyResolver _resolver;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="pluginPath">插件程序集路径</param>
        public PluginLoadContext(string pluginPath) : base(isCollectible: true)
        {
            _resolver = new AssemblyDependencyResolver(pluginPath);
        }

        /// <summary>
        /// 加载程序集
        /// </summary>
        protected override Assembly Load(AssemblyName assemblyName)
        {
            string assemblyPath = _resolver.ResolveAssemblyToPath(assemblyName);
            if (assemblyPath != null)
            {
                return LoadFromAssemblyPath(assemblyPath);
            }

            return null;
        }

        /// <summary>
        /// 加载本机库
        /// </summary>
        protected override IntPtr LoadUnmanagedDll(string unmanagedDllName)
        {
            string libraryPath = _resolver.ResolveUnmanagedDllToPath(unmanagedDllName);
            if (libraryPath != null)
            {
                return LoadUnmanagedDllFromPath(libraryPath);
            }

            return IntPtr.Zero;
        }
    }

    /// <summary>
    /// 插件管理器实现类
    /// </summary>
    public class PluginManager : IPluginManager
    {
        private readonly ILogger<PluginManager> _logger;
        private readonly IServiceProvider _serviceProvider;
        private readonly IServiceCollection _services;
        private readonly string _pluginsDirectory;
        private readonly ConcurrentDictionary<string, PluginInfo> _plugins = new();

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志记录器</param>
        /// <param name="serviceProvider">服务提供程序</param>
        /// <param name="services">服务集合</param>
        /// <param name="pluginsDirectory">插件目录</param>
        public PluginManager(
            ILogger<PluginManager> logger,
            IServiceProvider serviceProvider,
            IServiceCollection services,
            string pluginsDirectory = null)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
            _services = services ?? throw new ArgumentNullException(nameof(services));
            _pluginsDirectory = pluginsDirectory ?? Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Plugins");
        }

        /// <inheritdoc/>
        public void Initialize()
        {
            _logger.LogInformation("初始化插件管理器...");

            // 确保插件目录存在
            if (!Directory.Exists(_pluginsDirectory))
            {
                Directory.CreateDirectory(_pluginsDirectory);
                _logger.LogInformation($"创建插件目录: {_pluginsDirectory}");
            }

            // 扫描并加载内置插件
            LoadBuiltInPlugins();
        }

        /// <inheritdoc/>
        public void LoadAllPlugins()
        {
            _logger.LogInformation("开始加载所有插件...");

            // 扫描外部插件目录中所有的DLL文件
            string[] pluginFiles = Directory.GetFiles(_pluginsDirectory, "*.dll", SearchOption.AllDirectories);
            foreach (string pluginPath in pluginFiles)
            {
                try
                {
                    LoadPlugin(pluginPath);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"加载插件失败: {pluginPath}");
                }
            }

            _logger.LogInformation($"已加载 {_plugins.Count} 个插件");
        }

        /// <inheritdoc/>
        public PluginInfo LoadPlugin(string pluginPath)
        {
            if (string.IsNullOrEmpty(pluginPath))
                throw new ArgumentNullException(nameof(pluginPath));

            if (!File.Exists(pluginPath))
            {
                _logger.LogWarning($"插件文件不存在: {pluginPath}");
                return null;
            }

            _logger.LogInformation($"加载插件: {pluginPath}");

            try
            {
                // 创建插件加载上下文
                var loadContext = new PluginLoadContext(pluginPath);
                
                // 加载插件程序集
                Assembly pluginAssembly = loadContext.LoadFromAssemblyPath(pluginPath);
                
                // 查找实现IPlugin接口的所有类型
                Type[] pluginTypes = pluginAssembly.GetTypes()
                    .Where(t => typeof(IPlugin).IsAssignableFrom(t) && !t.IsAbstract)
                    .ToArray();

                if (pluginTypes.Length == 0)
                {
                    _logger.LogWarning($"在程序集中未找到有效的插件类型: {pluginPath}");
                    loadContext.Unload();
                    return null;
                }

                // 获取构造函数参数
                var constructors = pluginTypes[0].GetConstructors();
                if (constructors.Length == 0)
                {
                    _logger.LogWarning($"插件类型 {pluginTypes[0].Name} 没有构造函数");
                    return null;
                }

                // 获取第一个构造函数
                var constructor = constructors[0];
                var parameters = constructor.GetParameters();
                var parameterValues = new object[parameters.Length];

                // 从服务提供程序解析构造函数参数
                for (int i = 0; i < parameters.Length; i++)
                {
                    try
                    {
                        parameterValues[i] = _serviceProvider.GetRequiredService(parameters[i].ParameterType);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, $"无法解析插件 {pluginTypes[0].Name} 的构造函数参数 {parameters[i].Name}");
                        continue;
                    }
                }

                // 创建插件实例
                var plugin = (IPlugin)constructor.Invoke(parameterValues);

                // 如果插件为空，跳过
                if (plugin == null)
                {
                    _logger.LogWarning($"无法创建插件实例: {pluginTypes[0].Name}");
                    return null;
                }

                // 注册插件服务
                var services = new ServiceCollection();
                plugin.RegisterServices(services);
                
                // 将插件服务添加到主服务容器
                foreach (var service in services)
                {
                    _serviceProvider.GetRequiredService<IServiceCollection>().Add(service);
                }

                // 创建插件信息
                var pluginInfo = new PluginInfo
                {
                    Id = $"{plugin.Name}_{plugin.Version}",
                    Instance = plugin,
                    LoadContext = loadContext,
                    Assembly = pluginAssembly,
                    Path = pluginPath,
                    IsBuiltIn = false,
                    LoadTime = DateTime.Now
                };

                // 添加到插件字典
                if (_plugins.TryAdd(pluginInfo.Id, pluginInfo))
                {
                    _logger.LogInformation($"插件加载成功: {plugin.Name} v{plugin.Version}");
                    return pluginInfo;
                }
                else
                {
                    _logger.LogWarning($"插件已存在: {plugin.Name} v{plugin.Version}");
                    loadContext.Unload();
                    return null;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"加载插件时发生异常: {pluginPath}");
                return null;
            }
        }

        /// <inheritdoc/>
        public bool UnloadPlugin(string pluginId)
        {
            if (string.IsNullOrEmpty(pluginId))
                throw new ArgumentNullException(nameof(pluginId));

            if (!_plugins.TryGetValue(pluginId, out var pluginInfo))
            {
                _logger.LogWarning($"插件不存在: {pluginId}");
                return false;
            }

            try
            {
                // 停止插件
                if (pluginInfo.Instance.IsRunning)
                {
                    pluginInfo.Instance.Stop();
                }

                // 卸载插件程序集
                if (pluginInfo.LoadContext != null)
                {
                    pluginInfo.LoadContext.Unload();
                }

                // 从插件字典中移除
                if (_plugins.TryRemove(pluginId, out _))
                {
                    _logger.LogInformation($"插件已卸载: {pluginInfo.Instance.Name}");
                    return true;
                }
                else
                {
                    _logger.LogWarning($"无法从字典中移除插件: {pluginId}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"卸载插件失败: {pluginInfo.Instance.Name}");
                return false;
            }
        }

        /// <inheritdoc/>
        public void StartAllPlugins()
        {
            _logger.LogInformation("启动所有插件...");

            foreach (var pluginInfo in _plugins.Values)
            {
                try
                {
                    if (!pluginInfo.Instance.IsRunning)
                    {
                        // 调用插件方法注册服务
                        var services = new ServiceCollection();
                        pluginInfo.Instance.RegisterServices(services);
                        
                        // 启动插件
                        pluginInfo.Instance.Start(_serviceProvider);
                        
                        _logger.LogInformation($"插件已启动: {pluginInfo.Instance.Name} v{pluginInfo.Instance.Version}");
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"启动插件失败: {pluginInfo.Instance.Name}");
                }
            }
        }

        /// <inheritdoc/>
        public bool StartPlugin(string pluginId)
        {
            if (string.IsNullOrEmpty(pluginId))
                throw new ArgumentNullException(nameof(pluginId));

            if (!_plugins.TryGetValue(pluginId, out var pluginInfo))
            {
                _logger.LogWarning($"插件不存在: {pluginId}");
                return false;
            }

            try
            {
                if (pluginInfo.Instance.IsRunning)
                {
                    _logger.LogInformation($"插件已经处于运行状态: {pluginInfo.Instance.Name}");
                    return true;
                }

                // 调用插件方法注册服务
                var services = new ServiceCollection();
                pluginInfo.Instance.RegisterServices(services);
                
                // 启动插件
                pluginInfo.Instance.Start(_serviceProvider);
                
                _logger.LogInformation($"插件已启动: {pluginInfo.Instance.Name} v{pluginInfo.Instance.Version}");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"启动插件失败: {pluginInfo.Instance.Name}");
                return false;
            }
        }

        /// <inheritdoc/>
        public void StopAllPlugins()
        {
            _logger.LogInformation("停止所有插件...");

            foreach (var pluginInfo in _plugins.Values)
            {
                try
                {
                    if (pluginInfo.Instance.IsRunning)
                    {
                        pluginInfo.Instance.Stop();
                        _logger.LogInformation($"插件已停止: {pluginInfo.Instance.Name}");
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"停止插件失败: {pluginInfo.Instance.Name}");
                }
            }
        }

        /// <inheritdoc/>
        public bool StopPlugin(string pluginId)
        {
            if (string.IsNullOrEmpty(pluginId))
                throw new ArgumentNullException(nameof(pluginId));

            if (!_plugins.TryGetValue(pluginId, out var pluginInfo))
            {
                _logger.LogWarning($"插件不存在: {pluginId}");
                return false;
            }

            try
            {
                if (!pluginInfo.Instance.IsRunning)
                {
                    _logger.LogInformation($"插件已经处于停止状态: {pluginInfo.Instance.Name}");
                    return true;
                }

                pluginInfo.Instance.Stop();
                _logger.LogInformation($"插件已停止: {pluginInfo.Instance.Name}");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"停止插件失败: {pluginInfo.Instance.Name}");
                return false;
            }
        }

        /// <inheritdoc/>
        public List<PluginInfo> GetAllPlugins()
        {
            return _plugins.Values.ToList();
        }

        /// <inheritdoc/>
        public PluginInfo GetPlugin(string pluginId)
        {
            if (string.IsNullOrEmpty(pluginId))
                throw new ArgumentNullException(nameof(pluginId));

            if (_plugins.TryGetValue(pluginId, out var pluginInfo))
            {
                return pluginInfo;
            }

            return null;
        }

        /// <summary>
        /// 加载内置插件
        /// </summary>
        private void LoadBuiltInPlugins()
        {
            _logger.LogInformation("加载内置插件...");

            var assembly = Assembly.GetExecutingAssembly();

            // 查找当前程序集中实现IPlugin接口的所有类型
            var pluginTypes = assembly.GetTypes()
                .Where(t => typeof(IPlugin).IsAssignableFrom(t) && !t.IsAbstract && t != typeof(PluginBase))
                .ToArray();

            foreach (var pluginType in pluginTypes)
            {
                try
                {
                    // 获取构造函数参数
                    var constructors = pluginType.GetConstructors();
                    if (constructors.Length == 0)
                    {
                        _logger.LogWarning($"插件类型 {pluginType.Name} 没有构造函数");
                        continue;
                    }

                    // 获取第一个构造函数
                    var constructor = constructors[0];
                    var parameters = constructor.GetParameters();
                    var parameterValues = new object[parameters.Length];

                    // 从服务提供程序解析构造函数参数
                    for (int i = 0; i < parameters.Length; i++)
                    {
                        try
                        {
                            parameterValues[i] = _serviceProvider.GetRequiredService(parameters[i].ParameterType);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, $"无法解析插件 {pluginType.Name} 的构造函数参数 {parameters[i].Name}");
                            continue;
                        }
                    }

                    // 创建插件实例
                    var plugin = (IPlugin)constructor.Invoke(parameterValues);

                    // 如果插件为空，跳过
                    if (plugin == null)
                    {
                        _logger.LogWarning($"无法创建插件实例: {pluginType.Name}");
                        continue;
                    }

                    // 注册插件服务
                    var pluginServices = new ServiceCollection();
                    plugin.RegisterServices(pluginServices);
                    
                    // 将插件服务添加到主服务容器
                    foreach (var service in pluginServices)
                    {
                        _services.Add(service);
                    }

                    // 创建插件信息
                    var pluginInfo = new PluginInfo
                    {
                        Id = $"{plugin.Name}_{plugin.Version}",
                        Instance = plugin,
                        LoadContext = null, // 内置插件没有独立的加载上下文
                        Assembly = assembly,
                        Path = assembly.Location,
                        IsBuiltIn = true,
                        LoadTime = DateTime.Now
                    };

                    // 添加到插件字典
                    if (_plugins.TryAdd(pluginInfo.Id, pluginInfo))
                    {
                        _logger.LogInformation($"内置插件加载成功: {plugin.Name} v{plugin.Version}");
                    }
                    else
                    {
                        _logger.LogWarning($"内置插件已存在: {plugin.Name} v{plugin.Version}");
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"加载内置插件失败: {pluginType.Name}");
                }
            }
        }
    }
}

// 计划行数: 450
// 实际行数: 450 