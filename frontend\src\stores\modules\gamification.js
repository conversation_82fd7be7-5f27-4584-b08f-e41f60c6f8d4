/**
 * 游戏化系统状态管理模块
 * 文件路径: src/stores/modules/gamification.js
 * 功能描述: 管理用户积分、等级、成就、道具、活动流等游戏化相关状态
 */

import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import gamificationApi from '@/api/gamification';

// 假设的用户等级定义 (实际可能来自后端)
const levelThresholds = [
    { level: 1, points: 0, title: '新手入门' },
    { level: 2, points: 100, title: '初级专员' },
    { level: 3, points: 300, title: '中级能手' },
    { level: 4, points: 600, title: '高级专家' },
    { level: 5, points: 1000, title: '资深大师' },
    // ... more levels
];

export const useGamificationStore = defineStore('gamification', () => {
    // --- State ---
    const score = ref(0); // 用户当前总积分/分数
    const level = ref(1); // 用户当前等级
    const levelTitle = ref('新手入门'); // 用户当前等级称号
    const pointsToNextLevel = ref(100); // 距离下一级所需积分
    const currentLevelProgress = ref(0); // 当前等级进度百分比

    const achievements = ref([]); // 用户已获得的成就列表 { id, name, description, icon, achievedDate }
    const inventory = ref([]); // 用户背包中的道具列表 { id, name, description, icon, quantity }
    const recentActivities = ref([]); // 最近活动流 { id, text, timestamp, icon, type }
    const leaderboard = ref([]); // 排行榜数据 { userId, name, score, rank, avatar }
    
    const isLoading = ref(false); // 通用加载状态
    const lastActivityTimestamp = ref(null); // 用于增量获取活动

    // --- Getters ---
    const currentLevelInfo = computed(() => {
        return levelThresholds.find(lt => lt.level === level.value) || levelThresholds[0];
    });

    const nextLevelInfo = computed(() => {
        return levelThresholds.find(lt => lt.level === level.value + 1);
    });

    // --- Actions ---
    
    // 获取完整的游戏化状态 (积分、等级等)
    async function fetchGamificationStatus() {
        isLoading.value = true;
        try {
            const response = await gamificationApi.getUserStats();
            if (response.success && response.data) {
                const stats = response.data;
                score.value = stats.pointsBalance || 0;
                level.value = stats.currentLevel || 1;

                // 计算等级和进度
                updateLevelAndProgress(score.value);
            } else {
                console.warn("Failed to fetch gamification status:", response?.message);
                // 如果获取失败，尝试初始化用户数据
                await initializeUserStats();
            }
        } catch (error) {
            console.error("Error fetching gamification status:", error);
            // 如果出错，尝试初始化用户数据
            try {
                await initializeUserStats();
            } catch (initError) {
                console.error("Error initializing user stats:", initError);
            }
        } finally {
            isLoading.value = false;
        }
    }

    // 初始化用户游戏化数据
    async function initializeUserStats() {
        try {
            const response = await gamificationApi.initializeUserStats();
            if (response.success && response.data) {
                const stats = response.data;
                score.value = stats.pointsBalance || 0;
                level.value = stats.currentLevel || 1;
                updateLevelAndProgress(score.value);
            }
        } catch (error) {
            console.error("Error initializing user stats:", error);
        }
    }

    // 获取成就列表
    async function fetchAchievements() {
        // isLoading.value = true; // Or use specific loading state
        try {
             // const response = await gamificationApi.getAchievements();
             // Mock data:
             const response = { success: true, data: [
                 { id: 'ach1', name: '首次任务', description: '完成你的第一个任务', icon: 'Star', achievedDate: '2024-04-01' },
                 { id: 'ach2', name: '评论达人', description: '发表 10 条评论', icon: 'ChatDotRound', achievedDate: null }, // Example unachieved
             ]};
             if (response.success) {
                 achievements.value = response.data || [];
             }
        } catch (error) { console.error("Error fetching achievements:", error); }
        // finally { isLoading.value = false; }
    }
    
    // 获取背包道具
    async function fetchInventory() {
         try {
             // const response = await gamificationApi.getInventory();
             // Mock data:
             const response = { success: true, data: [
                 { id: 'item1', name: '积分加速卡', description: '任务积分 +10%', icon: 'MagicStick', quantity: 2 },
                 { id: 'item2', name: '改名卡', description: '修改一次昵称', icon: 'EditPen', quantity: 1 },
             ]};
             if (response.success) {
                 inventory.value = response.data || [];
             }
        } catch (error) { console.error("Error fetching inventory:", error); }
    }

    // 获取最近活动 (考虑增量更新)
    async function fetchRecentActivities(incremental = false) {
        // isLoading.value = true; // Or specific loading state
        try {
            const params = incremental && lastActivityTimestamp.value ? { since: lastActivityTimestamp.value } : {};
            // const response = await gamificationApi.getActivities(params);
            // Mock data:
            const now = new Date().toISOString();
            const mockActivities = [
                 { id: 'act3', text: '完成了任务 "部署测试环境"', timestamp: now, icon: 'CircleCheck', type: 'task_completed' },
                 { id: 'act2', text: '张三 发表了评论', timestamp: new Date(Date.now() - 60000).toISOString(), icon: 'ChatDotRound', type: 'comment' },
                 { id: 'act1', text: '获得了成就 "首次任务"', timestamp: new Date(Date.now() - 120000).toISOString(), icon: 'Trophy', type: 'achievement' },
            ];
            const response = { success: true, data: mockActivities };

            if (response.success && Array.isArray(response.data)) {
                 const newActivities = response.data;
                if (newActivities.length > 0) {
                    if (incremental) {
                        // Add new activities to the beginning, avoid duplicates
                        const existingIds = new Set(recentActivities.value.map(a => a.id));
                        const uniqueNew = newActivities.filter(a => !existingIds.has(a.id));
                        recentActivities.value = [...uniqueNew, ...recentActivities.value];
                    } else {
                        recentActivities.value = newActivities;
                    }
                    // Update timestamp for next incremental fetch
                    lastActivityTimestamp.value = recentActivities.value[0]?.timestamp || lastActivityTimestamp.value; 
                }
                 // Limit the list size for performance
                if (recentActivities.value.length > 50) {
                   recentActivities.value = recentActivities.value.slice(0, 50);
                }
            }
        } catch (error) { console.error("Error fetching recent activities:", error); }
        // finally { isLoading.value = false; }
    }

    // 获取排行榜 (兼容旧版本)
    async function fetchLeaderboard(period = 'weekly') { // e.g., 'weekly', 'monthly', 'allTime'
         try {
             // 将前端期间映射到后端类型
             const typeMap = {
                 'weekly': gamificationApi.LeaderboardType.WEEKLY,
                 'monthly': gamificationApi.LeaderboardType.MONTHLY,
                 'allTime': gamificationApi.LeaderboardType.ALL_TIME
             };

             const leaderboardType = typeMap[period] || gamificationApi.LeaderboardType.WEEKLY;
             const response = await gamificationApi.getLeaderboard(leaderboardType, 10);

             if (response.success && response.data) {
                 leaderboard.value = response.data.map(item => ({
                     userId: item.userId,
                     name: item.userName,
                     score: item.totalPoints,
                     rank: item.rank,
                     avatar: item.userAvatar || '',
                     department: item.department || '',
                     level: item.currentLevel || 1
                 }));
             }
        } catch (error) {
            console.error("Error fetching leaderboard:", error);
            // 使用空数组作为fallback
            leaderboard.value = [];
        }
    }

    // 获取多维度排行榜
    async function fetchMultiDimensionLeaderboard(metric = 'points', period = 'weekly', limit = 20) {
        try {
            const response = await gamificationApi.getMultiDimensionLeaderboard(metric, period, limit);

            if (response.success && response.data) {
                return response.data.map((item, index) => ({
                    userId: item.userId,
                    userName: item.userName,
                    avatarUrl: item.avatarUrl,
                    department: item.department,
                    points: item.points || 0,
                    coins: item.coins || 0,
                    diamonds: item.diamonds || 0,
                    tasksCreated: item.tasksCreated || 0,
                    tasksCompleted: item.tasksCompleted || 0,
                    tasksClaimed: item.tasksClaimed || 0,
                    faultsRecorded: item.faultsRecorded || 0,
                    maintenanceCreated: item.maintenanceCreated || 0,
                    assetsUpdated: item.assetsUpdated || 0,
                    rank: index + 1
                }));
            }
            return [];
        } catch (error) {
            console.error("Error fetching multi-dimension leaderboard:", error);
            return [];
        }
    }
    
    // 记录事件 (通知后端发生了特定行为，后端处理积分/成就等)
    async function recordEvent(eventType, eventData = {}) {
        console.log('Recording gamification event:', eventType, eventData);
        try {
            // 根据事件类型调用相应的API
            let response = null;

            if (eventType === 'task_completed') {
                const taskId = eventData.taskId;
                const isOnTime = eventData.isOnTime || false;
                if (taskId) {
                    response = await gamificationApi.triggerCompleteReward(taskId, isOnTime);
                }
            } else if (eventType === 'task_claimed') {
                const taskId = eventData.taskId;
                if (taskId) {
                    response = await gamificationApi.triggerClaimReward(taskId);
                }
            }

            // 如果有响应且成功，更新本地状态
            if (response && response.success) {
                // 延迟刷新状态，让后端处理完成
                setTimeout(() => {
                    fetchGamificationStatus();
                    fetchRecentActivities(true); // 增量获取最新活动
                }, 1000);

                // 立即显示奖励反馈
                if (response.data) {
                    const reward = response.data;
                    const pointsEarned = reward.pointsGained || 0;
                    const coinsEarned = reward.coinsGained || 0;
                    const diamondsEarned = reward.diamondsGained || 0;

                    // 显示奖励通知
                    showRewardNotification(pointsEarned, coinsEarned, diamondsEarned);
                }
            }

        } catch (error) {
            console.error(`Error recording event ${eventType}:`, error);
        }
    }

    // 显示奖励通知
    function showRewardNotification(points, coins, diamonds) {
        const rewards = [];
        if (points > 0) rewards.push(`+${points} 积分`);
        if (coins > 0) rewards.push(`+${coins} 金币`);
        if (diamonds > 0) rewards.push(`+${diamonds} 钻石`);

        if (rewards.length > 0) {
            // 这里可以集成通知组件
            console.log('🎉 获得奖励:', rewards.join(', '));
            // 可以触发全局通知事件
            // EventBus.emit('showReward', { message: rewards.join(', ') });
        }
    }

    // 辅助函数：根据积分计算等级和进度
    function updateLevelAndProgress(currentScore) {
        let currentLvl = 1;
        let currentTitle = levelThresholds[0].title;
        let nextLvlThreshold = levelThresholds[1]?.points || Infinity;
        let prevLvlThreshold = 0;

        for (let i = 0; i < levelThresholds.length; i++) {
            if (currentScore >= levelThresholds[i].points) {
                currentLvl = levelThresholds[i].level;
                currentTitle = levelThresholds[i].title;
                prevLvlThreshold = levelThresholds[i].points;
                nextLvlThreshold = levelThresholds[i + 1]?.points || Infinity;
            } else {
                break; // Stop when score is less than the threshold
            }
        }

        level.value = currentLvl;
        levelTitle.value = currentTitle;

        if (nextLvlThreshold === Infinity) {
            pointsToNextLevel.value = 0;
            currentLevelProgress.value = 100;
        } else {
            const pointsInCurrentLevel = nextLvlThreshold - prevLvlThreshold;
            const pointsEarnedInLevel = currentScore - prevLvlThreshold;
            pointsToNextLevel.value = nextLvlThreshold - currentScore;
            currentLevelProgress.value = pointsInCurrentLevel > 0 ? Math.min(100, Math.floor((pointsEarnedInLevel / pointsInCurrentLevel) * 100)) : 100;
        }
    }

    // --- Initial Load ---
    // Consider when to initially load the data, e.g., after user logs in.
    // Can be called from App.vue or a layout component.
    function initializeStore() {
        console.log("Initializing Gamification Store...");
        fetchGamificationStatus();
        fetchAchievements();
        fetchInventory();
        fetchRecentActivities();
        // fetchLeaderboard(); // Maybe load leaderboard on demand
    }


    return {
        // State
        score,
        level,
        levelTitle,
        pointsToNextLevel,
        currentLevelProgress,
        achievements,
        inventory,
        recentActivities,
        leaderboard,
        isLoading,
        lastActivityTimestamp,

        // Getters
        currentLevelInfo,
        nextLevelInfo,

        // Actions
        fetchGamificationStatus,
        fetchAchievements,
        fetchInventory,
        fetchRecentActivities,
        fetchLeaderboard,
        fetchMultiDimensionLeaderboard,
        recordEvent,
        initializeUserStats,
        showRewardNotification,
        initializeStore, // Expose initialization function
    };
}); 