// IT资产管理系统 - 用户积分实体
// 文件路径: /Models/Entities/UserPoints.cs
// 功能: 定义用户积分系统数据结构

using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ItAssetsSystem.Models.Entities
{
    /// <summary>
    /// 用户积分实体类
    /// </summary>
    [Table("user_points")]
    public class UserPoints
    {
        /// <summary>
        /// 记录ID
        /// </summary>
        [Key]
        [Column("id")]
        public int Id { get; set; }
        
        /// <summary>
        /// 用户ID
        /// </summary>
        [Required]
        [Column("user_id")]
        public int UserId { get; set; }
        
        /// <summary>
        /// 总积分
        /// </summary>
        [Column("points")]
        public int Points { get; set; } = 0;
        
        /// <summary>
        /// 当日获得积分
        /// </summary>
        [Column("daily_points")]
        public int DailyPoints { get; set; } = 0;
        
        /// <summary>
        /// 本周获得积分
        /// </summary>
        [Column("weekly_points")]
        public int WeeklyPoints { get; set; } = 0;
        
        /// <summary>
        /// 本月获得积分
        /// </summary>
        [Column("monthly_points")]
        public int MonthlyPoints { get; set; } = 0;
        
        /// <summary>
        /// 连续签到天数
        /// </summary>
        [Column("consecutive_days")]
        public int ConsecutiveDays { get; set; } = 0;
        
        /// <summary>
        /// 上次签到时间
        /// </summary>
        [Column("last_sign_in")]
        public DateTime? LastSignIn { get; set; }
        
        /// <summary>
        /// 健康值（血条）
        /// </summary>
        [Column("health")]
        public int Health { get; set; } = 100;
        
        /// <summary>
        /// 健康值恢复时间
        /// </summary>
        [Column("health_recovery_time")]
        public DateTime? HealthRecoveryTime { get; set; }
        
        /// <summary>
        /// 更新时间
        /// </summary>
        [Column("updated_at")]
        public DateTime UpdatedAt { get; set; } = DateTime.Now;
        
        /// <summary>
        /// 导航属性 - 用户
        /// </summary>
        public virtual User User { get; set; }
    }
} 