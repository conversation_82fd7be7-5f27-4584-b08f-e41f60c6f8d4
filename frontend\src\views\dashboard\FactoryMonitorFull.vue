<template>
  <div class="factory-monitor-full">
    <!-- 顶部状态栏 -->
    <div class="top-status-bar">
      <div class="status-info">
        <!-- 状态信息已移除 -->
      </div>
      
      <div class="zoom-controls">
        <el-button-group size="small">
          <el-button @click="zoomOut" :disabled="zoomLevel <= 0.5">
            <el-icon><ZoomOut /></el-icon>
          </el-button>
          <el-button @click="resetZoom">
            <el-icon><Refresh /></el-icon>
          </el-button>
          <el-button @click="zoomIn" :disabled="zoomLevel >= 2">
            <el-icon><ZoomIn /></el-icon>
          </el-button>
        </el-button-group>
      </div>
    </div>

    <!-- 主要工厂布局区域 -->
    <div class="factory-layout-container" :style="containerStyle">
      <div class="factory-canvas" :style="canvasStyle">
        <!-- 网格背景 - 与设计器保持一致 -->
        <div class="grid-background-layer" :style="getGridBackgroundStyle()"></div>

        <!-- 区域容器 -->
        <div
          v-for="zone in layoutConfig?.zones || []"
          :key="zone.id"
          class="zone-container"
          :style="getZoneStyle(zone)"
        >
          <!-- 区域标签已移除 -->
          
          <!-- 工位网格 -->
          <div class="workstation-grid" :style="getGridStyle(zone)">
            <div
              v-for="workstation in getZoneWorkstations(zone)"
              :key="workstation.locationId"
              class="workstation-cell"
              :class="getWorkstationClass(workstation)"
              @click="selectWorkstation(workstation)"
            >
              <!-- 状态图标 -->
              <div class="status-icon">
                <el-icon v-if="workstation.status === 'operational'">
                  <Check />
                </el-icon>
                <el-icon v-else-if="workstation.status === 'warning'">
                  <Warning />
                </el-icon>
                <el-icon v-else-if="workstation.status === 'error'">
                  <Close />
                </el-icon>
                <el-icon v-else>
                  <Minus />
                </el-icon>
              </div>
              
              <!-- 工位编号和效率指示器已移除 -->
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧状态图例 -->
    <div class="status-legend">
      <div class="legend-items">
        <div class="legend-item">
          <div class="legend-dot operational"></div>
        </div>
        <div class="legend-item">
          <div class="legend-dot warning"></div>
        </div>
        <div class="legend-item">
          <div class="legend-dot error"></div>
        </div>
        <div class="legend-item">
          <div class="legend-dot idle"></div>
        </div>
      </div>
    </div>

    <!-- 悬浮提示框已移除 -->
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Check, Warning, Close, Minus, ZoomIn, ZoomOut, Refresh
} from '@element-plus/icons-vue'

// 响应式数据
const layoutConfig = ref(null)
const workstations = ref([])
const zoomLevel = ref(1)
const selectedWorkstation = ref(null)

// 提示框相关
const tooltipVisible = ref(false)
const tooltipX = ref(0)
const tooltipY = ref(0)
const tooltipWorkstation = ref(null)

// 计算属性
const totalWorkstations = computed(() => workstations.value.length)
const totalZones = computed(() => layoutConfig.value?.zones?.length || 0)

const containerStyle = computed(() => ({
  transform: `scale(${zoomLevel.value})`,
  transformOrigin: 'top left'
}))

const canvasStyle = computed(() => {
  if (!layoutConfig.value?.canvas) return {}
  return {
    width: layoutConfig.value.canvas.width + 'px',
    height: layoutConfig.value.canvas.height + 'px',
    backgroundColor: layoutConfig.value.canvas.backgroundColor || '#1e293b',
    border: '2px solid #e5e7eb',
    borderRadius: '8px',
    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
    position: 'relative'
  }
})

// 网格背景样式 - 与设计器保持一致
const getGridBackgroundStyle = () => {
  const gridSize = 20 // 与设计器一致的网格大小
  const majorGridSize = gridSize * 5 // 主网格线是小网格的5倍

  return {
    position: 'absolute',
    top: '0',
    left: '0',
    width: '100%',
    height: '100%',
    pointerEvents: 'none',
    zIndex: 1,
    backgroundImage: `
      linear-gradient(to right, #d1d5db 1px, transparent 1px),
      linear-gradient(to bottom, #d1d5db 1px, transparent 1px),
      linear-gradient(to right, #e5e7eb 1px, transparent 1px),
      linear-gradient(to bottom, #e5e7eb 1px, transparent 1px)
    `,
    backgroundSize: `
      ${majorGridSize}px ${majorGridSize}px,
      ${majorGridSize}px ${majorGridSize}px,
      ${gridSize}px ${gridSize}px,
      ${gridSize}px ${gridSize}px
    `,
    opacity: 0.3
  }
}

// 方法 - 修复：使用正确的配置文件路径
const loadLayoutConfig = async () => {
  try {
    // 按优先级尝试加载配置文件
    const configPaths = [
      '/analyresport/factory-layout-1748964326771.json', // 用户偏好的配置文件
      '/analyresport/factory-layout-1748945213262.json', // 备用配置文件
      '/analyresport/DP.json' // 原始路径（兼容性）
    ]

    let config = null
    let loadedPath = null

    for (const path of configPaths) {
      try {
        console.log(`🔍 尝试加载配置文件: ${path}`)
        const response = await fetch(path)
        if (response.ok) {
          config = await response.json()
          loadedPath = path
          console.log(`✅ 成功加载配置文件: ${path}`)
          break
        }
      } catch (err) {
        console.log(`❌ 加载失败: ${path} - ${err.message}`)
        continue
      }
    }

    if (!config) {
      throw new Error('所有配置文件加载失败，使用默认配置')
    }

    layoutConfig.value = adaptConfig(config)
    workstations.value = generateWorkstationData()

    console.log('🎯 布局配置加载成功:', {
      path: loadedPath,
      zones: layoutConfig.value.zones.length,
      canvas: layoutConfig.value.canvas
    })

    ElMessage.success(`配置加载成功: ${loadedPath.split('/').pop()}`)
  } catch (error) {
    console.error('❌ 加载布局配置失败:', error)
    ElMessage.error(`加载布局配置失败: ${error.message}`)
    // 使用默认配置
    layoutConfig.value = getDefaultConfig()
    workstations.value = generateWorkstationData()
    console.log('🔄 使用默认配置')
  }
}

const adaptConfig = (config) => {
  console.log('🔧 开始适配配置:', config)

  // 验证配置结构
  if (!config || !config.zones || !Array.isArray(config.zones)) {
    console.error('❌ 配置格式错误:', config)
    throw new Error('配置文件格式错误：缺少zones数组')
  }

  // 计算画布尺寸 - 确保包含所有区域
  let maxX = 0, maxY = 0
  let minX = Infinity, minY = Infinity

  config.zones.forEach(zone => {
    if (typeof zone.x !== 'number' || typeof zone.y !== 'number' ||
        typeof zone.width !== 'number' || typeof zone.height !== 'number') {
      console.warn('⚠️ 区域坐标数据异常:', zone)
      return
    }

    minX = Math.min(minX, zone.x)
    minY = Math.min(minY, zone.y)
    maxX = Math.max(maxX, zone.x + zone.width)
    maxY = Math.max(maxY, zone.y + zone.height)
  })

  // 如果配置中已有画布信息，直接使用；否则计算
  let canvasConfig
  if (config.canvas && config.canvas.width && config.canvas.height) {
    // 使用配置文件中的画布设置
    canvasConfig = {
      width: config.canvas.width,
      height: config.canvas.height,
      backgroundColor: config.canvas.backgroundColor || "#fafafa"
    }
    console.log('📐 使用配置文件中的画布尺寸:', canvasConfig)
  } else {
    // 添加边距，确保布局完整显示
    const padding = 100
    canvasConfig = {
      width: Math.max(2000, maxX + padding),
      height: Math.max(1200, maxY + padding),
      backgroundColor: "#fafafa"
    }
    console.log('📐 计算画布尺寸:', {
      原始范围: { minX, minY, maxX, maxY },
      最终画布: canvasConfig
    })
  }

  const adaptedConfig = {
    name: config.name || "工厂布局监控",
    canvas: canvasConfig,
    zones: config.zones.map(zone => {
      const adaptedZone = {
        id: zone.id,
        name: zone.name || `区域${zone.id}`,
        color: zone.color || '#3b82f6',
        position: {
          x: zone.x,
          y: zone.y,
          width: zone.width,
          height: zone.height
        },
        layout: {
          rows: zone.rows || 1,
          cols: zone.cols || 1
        },
        startWorkstation: zone.startWorkstation || 1
      }

      console.log(`📍 适配区域 ${zone.id}:`, adaptedZone)
      return adaptedZone
    })
  }

  console.log('✅ 配置适配完成:', {
    zones: adaptedConfig.zones.length,
    canvas: adaptedConfig.canvas
  })

  return adaptedConfig
}

const generateWorkstationData = () => {
  if (!layoutConfig.value || !layoutConfig.value.zones) {
    console.warn('⚠️ 布局配置为空，无法生成工位数据')
    return []
  }

  console.log('🏭 开始生成工位数据...')

  const data = []
  const statuses = ['operational', 'warning', 'error', 'idle']
  const weights = [0.7, 0.15, 0.1, 0.05] // 70%正常, 15%警告, 10%故障, 5%空闲

  layoutConfig.value.zones.forEach(zone => {
    const { rows, cols } = zone.layout
    const startId = zone.startWorkstation
    const totalWorkstations = rows * cols

    console.log(`📍 处理区域 ${zone.name} (ID: ${zone.id}):`, {
      布局: `${rows}行 × ${cols}列`,
      起始工位: startId,
      工位总数: totalWorkstations
    })

    for (let row = 0; row < rows; row++) {
      for (let col = 0; col < cols; col++) {
        const workstationId = startId + row * cols + col

        // 基于权重随机分配状态
        const random = Math.random()
        let status = 'operational'
        let cumulative = 0

        for (let i = 0; i < weights.length; i++) {
          cumulative += weights[i]
          if (random < cumulative) {
            status = statuses[i]
            break
          }
        }

        const workstation = {
          locationId: workstationId,
          locationName: `${zone.name}-工位${workstationId.toString().padStart(3, '0')}`,
          locationCode: `WS${workstationId.toString().padStart(3, '0')}`,
          zoneId: zone.id,
          zoneName: zone.name,
          status: status,
          efficiency: Math.floor(Math.random() * 31) + 70, // 70-100%
          assetCount: Math.floor(Math.random() * 6) + 2, // 2-7个资产
          taskCount: Math.floor(Math.random() * 9) + 1, // 1-9个任务
          row: row,
          col: col,
          position: {
            zoneX: zone.position.x,
            zoneY: zone.position.y,
            relativeRow: row,
            relativeCol: col
          }
        }

        data.push(workstation)
      }
    }
  })

  console.log(`✅ 工位数据生成完成: 共 ${data.length} 个工位`)
  console.log('📊 状态分布:', {
    正常: data.filter(w => w.status === 'operational').length,
    警告: data.filter(w => w.status === 'warning').length,
    故障: data.filter(w => w.status === 'error').length,
    空闲: data.filter(w => w.status === 'idle').length
  })

  return data
}

const getDefaultConfig = () => ({
  name: "默认工厂布局",
  canvas: { width: 1000, height: 600, backgroundColor: "#1e293b" },
  zones: [{
    id: 1,
    name: "默认区域",
    color: "#3b82f6",
    position: { x: 100, y: 100, width: 400, height: 300 },
    layout: { rows: 3, cols: 4 },
    startWorkstation: 1
  }]
})

const getZoneStyle = (zone) => ({
  position: 'absolute',
  left: zone.position.x + 'px',
  top: zone.position.y + 'px',
  width: zone.position.width + 'px',
  height: zone.position.height + 'px',
  border: `2px solid ${zone.color}`,
  borderRadius: '8px',
  backgroundColor: `${zone.color}15`,
  boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
  transition: 'all 0.3s ease'
})

const getGridStyle = (zone) => ({
  display: 'grid',
  gridTemplateColumns: `repeat(${zone.layout.cols}, 1fr)`,
  gridTemplateRows: `repeat(${zone.layout.rows}, 1fr)`,
  gap: '2px',
  padding: '4px',
  width: '100%',
  height: '100%'
})

const getZoneWorkstations = (zone) => {
  return workstations.value
    .filter(w => w.zoneId === zone.id)
    .sort((a, b) => a.row * zone.layout.cols + a.col - (b.row * zone.layout.cols + b.col))
}

const getWorkstationClass = (workstation) => {
  return [`status-${workstation.status}`]
}

const getStatusText = (status) => {
  const statusMap = {
    operational: '正常运行',
    warning: '警告状态', 
    error: '故障状态',
    idle: '空闲状态'
  }
  return statusMap[status] || '未知状态'
}

// 缩放控制
const zoomIn = () => {
  if (zoomLevel.value < 2) {
    zoomLevel.value = Math.min(2, zoomLevel.value + 0.1)
  }
}

const zoomOut = () => {
  if (zoomLevel.value > 0.5) {
    zoomLevel.value = Math.max(0.5, zoomLevel.value - 0.1)
  }
}

const resetZoom = () => {
  zoomLevel.value = 1
}

// 工位选择
const selectWorkstation = (workstation) => {
  selectedWorkstation.value = workstation
  console.log('选中工位:', workstation)
}

// 提示框
const showTooltip = (workstation, event) => {
  tooltipWorkstation.value = workstation
  tooltipX.value = event.clientX + 10
  tooltipY.value = event.clientY + 10
  tooltipVisible.value = true
}

const hideTooltip = () => {
  tooltipVisible.value = false
}

// 生命周期
onMounted(() => {
  loadLayoutConfig()
})
</script>

<style scoped>
.factory-monitor-full {
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  overflow: hidden;
  position: relative;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* 顶部状态栏 */
.top-status-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 60px;
  background: rgba(15, 23, 42, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  z-index: 1000;
}

.status-info {
  display: flex;
  align-items: center;
  gap: 16px;
  color: #e2e8f0;
  font-size: 14px;
  font-weight: 500;
}

.total-info {
  color: #22c55e;
}

.zone-info, .layout-name {
  color: #94a3b8;
}

/* 主要布局容器 */
.factory-layout-container {
  position: absolute;
  top: 60px;
  left: 0;
  right: 200px;
  bottom: 0;
  overflow: auto;
  padding: 20px;
}

.factory-canvas {
  position: relative;
  margin: 0 auto;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

/* 区域容器 */
.zone-container {
  border-radius: 8px;
  transition: all 0.3s ease;
}

.zone-container:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.zone-label {
  position: absolute;
  top: -25px;
  left: 8px;
  font-size: 14px;
  font-weight: 600;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

/* 工位网格 */
.workstation-grid {
  width: 100%;
  height: 100%;
}

.workstation-cell {
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  min-height: 20px;
  font-size: 10px;
  font-weight: 500;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.workstation-cell:hover {
  transform: scale(1.05);
  z-index: 10;
}

/* 状态样式 */
.workstation-cell.status-operational {
  background: #22c55e;
}

.workstation-cell.status-warning {
  background: #f59e0b;
}

.workstation-cell.status-error {
  background: #ef4444;
  animation: pulse-error 2s infinite;
}

.workstation-cell.status-idle {
  background: #64748b;
}

@keyframes pulse-error {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* 右侧状态图例 */
.status-legend {
  position: fixed;
  top: 60px;
  right: 0;
  width: 200px;
  height: calc(100vh - 60px);
  background: rgba(15, 23, 42, 0.95);
  backdrop-filter: blur(10px);
  border-left: 1px solid rgba(148, 163, 184, 0.1);
  padding: 24px;
}

.legend-title {
  font-size: 16px;
  font-weight: 600;
  color: #e2e8f0;
  margin-bottom: 20px;
  text-align: center;
}

.legend-items {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 12px;
  color: #cbd5e1;
  font-size: 14px;
}

.legend-dot {
  width: 16px;
  height: 16px;
  border-radius: 4px;
  flex-shrink: 0;
}

.legend-dot.operational {
  background: #22c55e;
}

.legend-dot.warning {
  background: #f59e0b;
}

.legend-dot.error {
  background: #ef4444;
}

.legend-dot.idle {
  background: #64748b;
}

/* 提示框样式已移除 */
</style>
