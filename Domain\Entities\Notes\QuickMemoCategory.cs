// File: Domain/Entities/Notes/QuickMemoCategory.cs
// Description: Represents a category for organizing quick memos.
#nullable enable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using ItAssetsSystem.Models.Entities; // For User entity

namespace ItAssetsSystem.Domain.Entities.Notes
{
    [Table("quick_memo_categories")]
    public class QuickMemoCategory
    {
        [Key]
        [Column("id")]
        // The markdown suggests Guid.NewGuid().ToString() or long/int. 
        // For consistency with QuickMemo.Id and to simplify DTOs/API, string GUID is chosen.
        // DatabaseGeneratedOption.Identity is typically for int/long. If using GUIDs generated by app, remove it or ensure DB supports it.
        // Let's assume app-generated GUIDs for now, matching QuickMemo.
        public string Id { get; set; } = Guid.NewGuid().ToString();

        [Required]
        [MaxLength(100)]
        [Column("name")]
        public string Name { get; set; } = string.Empty;

        [MaxLength(7)] // e.g., "#RRGGBB"
        [Column("color")]
        public string? Color { get; set; }

        [Required]
        [Column("user_id")]
        public int UserId { get; set; }

        [Column("created_at")]
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        [Column("updated_at")]
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // Navigation property
        public virtual ICollection<QuickMemo> QuickMemos { get; set; } = new List<QuickMemo>();

        [ForeignKey("UserId")]
        public virtual User User { get; set; } = null!;
    }
} 