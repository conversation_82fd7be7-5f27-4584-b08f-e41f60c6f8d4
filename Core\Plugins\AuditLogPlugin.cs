// IT资产管理系统 - 审计日志插件
// 文件路径: /Core/Plugins/AuditLogPlugin.cs
// 功能: 审计日志插件模块，实现系统操作日志记录

using ItAssetsSystem.Core.Abstractions;
using ItAssetsSystem.Core.Events;
using ItAssetsSystem.Models.Entities;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System;
using System.Security.Claims;
using System.Threading.Tasks;

namespace ItAssetsSystem.Core.Plugins
{
    /// <summary>
    /// 审计日志插件
    /// </summary>
    public class AuditLogPlugin : PluginBase
    {
        /// <inheritdoc/>
        public override string Name => "AuditLog";

        /// <inheritdoc/>
        public override string Description => "审计日志插件，提供系统操作日志记录功能";

        /// <inheritdoc/>
        public override Version Version => new Version(1, 0, 0);

        /// <summary>
        /// 事件总线
        /// </summary>
        private ItAssetsSystem.Core.Events.IEventBus _eventBus;

        /// <inheritdoc/>
        public override void RegisterServices(IServiceCollection services)
        {
            // 注册审计日志相关服务
            services.AddScoped<IAuditLogService, AuditLogService>();
        }

        /// <summary>
        /// 插件启动
        /// </summary>
        protected override void OnStart()
        {
            _eventBus = ServiceProvider.GetRequiredService<ItAssetsSystem.Core.Events.IEventBus>();
            
            // 订阅事件
            _eventBus.Subscribe<UserLoginEvent>(HandleUserLogin);
            _eventBus.Subscribe<EntityCreatedEvent>(HandleEntityCreated);
            _eventBus.Subscribe<EntityUpdatedEvent>(HandleEntityUpdated);
            _eventBus.Subscribe<EntityDeletedEvent>(HandleEntityDeleted);
            
            Logger.LogInformation("审计日志插件已启动");
        }

        /// <summary>
        /// 插件停止
        /// </summary>
        protected override void OnStop()
        {
            // 取消订阅事件
            _eventBus.Unsubscribe<UserLoginEvent>(HandleUserLogin);
            _eventBus.Unsubscribe<EntityCreatedEvent>(HandleEntityCreated);
            _eventBus.Unsubscribe<EntityUpdatedEvent>(HandleEntityUpdated);
            _eventBus.Unsubscribe<EntityDeletedEvent>(HandleEntityDeleted);
            
            Logger.LogInformation("审计日志插件已停止");
        }

        /// <summary>
        /// 处理用户登录事件
        /// </summary>
        private void HandleUserLogin(UserLoginEvent @event)
        {
            var auditLogService = ServiceProvider.GetRequiredService<IAuditLogService>();
            auditLogService.LogAction(
                @event.UserId,
                @event.Username,
                1, // 登录
                "Auth",
                "Login",
                "用户登录",
                "User",
                @event.UserId.ToString(),
                @event.IPAddress,
                @event.Success ? 1 : 0
            );
        }

        /// <summary>
        /// 处理实体创建事件
        /// </summary>
        private void HandleEntityCreated(EntityCreatedEvent @event)
        {
            var auditLogService = ServiceProvider.GetRequiredService<IAuditLogService>();
            auditLogService.LogAction(
                @event.UserId,
                @event.Username,
                3, // 新增
                @event.Module,
                "Create",
                $"创建{@event.EntityName}",
                @event.EntityName,
                @event.EntityId.ToString(),
                @event.IPAddress,
                1 // 成功
            );
        }

        /// <summary>
        /// 处理实体更新事件
        /// </summary>
        private void HandleEntityUpdated(EntityUpdatedEvent @event)
        {
            var auditLogService = ServiceProvider.GetRequiredService<IAuditLogService>();
            auditLogService.LogAction(
                @event.UserId,
                @event.Username,
                4, // 修改
                @event.Module,
                "Update",
                $"更新{@event.EntityName}",
                @event.EntityName,
                @event.EntityId.ToString(),
                @event.IPAddress,
                1 // 成功
            );
        }

        /// <summary>
        /// 处理实体删除事件
        /// </summary>
        private void HandleEntityDeleted(EntityDeletedEvent @event)
        {
            var auditLogService = ServiceProvider.GetRequiredService<IAuditLogService>();
            auditLogService.LogAction(
                @event.UserId,
                @event.Username,
                5, // 删除
                @event.Module,
                "Delete",
                $"删除{@event.EntityName}",
                @event.EntityName,
                @event.EntityId.ToString(),
                @event.IPAddress,
                1 // 成功
            );
        }
    }

    /// <summary>
    /// 用户登录事件
    /// </summary>
    public class UserLoginEvent
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public int UserId { get; set; }
        
        /// <summary>
        /// 用户名
        /// </summary>
        public string Username { get; set; }
        
        /// <summary>
        /// IP地址
        /// </summary>
        public string IPAddress { get; set; }
        
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }
    }

    /// <summary>
    /// 实体创建事件
    /// </summary>
    public class EntityCreatedEvent
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public int UserId { get; set; }
        
        /// <summary>
        /// 用户名
        /// </summary>
        public string Username { get; set; }
        
        /// <summary>
        /// 模块
        /// </summary>
        public string Module { get; set; }
        
        /// <summary>
        /// 实体名称
        /// </summary>
        public string EntityName { get; set; }
        
        /// <summary>
        /// 实体ID
        /// </summary>
        public int EntityId { get; set; }
        
        /// <summary>
        /// IP地址
        /// </summary>
        public string IPAddress { get; set; }
    }

    /// <summary>
    /// 实体更新事件
    /// </summary>
    public class EntityUpdatedEvent : EntityCreatedEvent { }

    /// <summary>
    /// 实体删除事件
    /// </summary>
    public class EntityDeletedEvent : EntityCreatedEvent { }

    /// <summary>
    /// 审计日志服务接口
    /// </summary>
    public interface IAuditLogService
    {
        void LogAction(int? userId, string username, int actionType, string module, string function, 
                      string content, string target, string targetId, string ipAddress, int result);
    }

    /// <summary>
    /// 审计日志服务实现
    /// </summary>
    public class AuditLogService : IAuditLogService
    {
        private readonly ILogger<AuditLogService> _logger;
        
        public AuditLogService(ILogger<AuditLogService> logger)
        {
            _logger = logger;
        }
        
        public void LogAction(int? userId, string username, int actionType, string module, string function, 
                             string content, string target, string targetId, string ipAddress, int result)
        {
            _logger.LogInformation($"记录审计日志: 用户={username}, 操作={actionType}, 模块={module}, 功能={function}");
            
            // 实际实现中会保存日志到数据库
            // 这里仅记录到日志系统
        }
    }
}

// 计划行数: 200
// 实际行数: 200 