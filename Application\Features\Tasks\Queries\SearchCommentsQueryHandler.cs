// File: Application/Features/Tasks/Queries/SearchCommentsQueryHandler.cs
// Description: 搜索评论查询处理器

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using MediatR;
using Microsoft.Extensions.Logging;
using ItAssetsSystem.Application.Common.Dtos;
using ItAssetsSystem.Application.Features.Tasks.Dtos;
using ItAssetsSystem.Core.Abstractions;
using PagedResultType = ItAssetsSystem.Core.Interfaces.Services.PagedResult<ItAssetsSystem.Application.Features.Tasks.Dtos.CommentDto>;

namespace ItAssetsSystem.Application.Features.Tasks.Queries
{
    /// <summary>
    /// 搜索评论查询处理器
    /// </summary>
    public class SearchCommentsQueryHandler : IRequestHandler<SearchCommentsQuery, ApiResponse<PagedResultType>>
    {
        private readonly ITaskRepository _taskRepository;
        private readonly ICoreDataQueryService _coreDataQueryService;
        private readonly ILogger<SearchCommentsQueryHandler> _logger;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="taskRepository">任务仓储</param>
        /// <param name="coreDataQueryService">核心数据查询服务</param>
        /// <param name="logger">日志记录器</param>
        public SearchCommentsQueryHandler(
            ITaskRepository taskRepository,
            ICoreDataQueryService coreDataQueryService,
            ILogger<SearchCommentsQueryHandler> logger)
        {
            _taskRepository = taskRepository;
            _coreDataQueryService = coreDataQueryService;
            _logger = logger;
        }

        /// <summary>
        /// 处理搜索评论查询
        /// </summary>
        /// <param name="request">搜索评论查询</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>分页评论结果</returns>
        public async Task<ApiResponse<PagedResultType>> Handle(SearchCommentsQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("开始处理搜索评论查询，关键词: {SearchKeyword}, 用户ID: {UserId}",
                    request.SearchKeyword, request.CurrentUserId);

                // 验证搜索参数
                if (string.IsNullOrWhiteSpace(request.SearchKeyword) && 
                    !request.TaskId.HasValue && 
                    !request.UserId.HasValue && 
                    !request.StartDate.HasValue && 
                    !request.EndDate.HasValue && 
                    !request.IsPinned.HasValue)
                {
                    _logger.LogWarning("搜索参数不能全部为空");
                    return ApiResponse<PagedResultType>.CreateFail("至少需要提供一个搜索条件");
                }

                // 验证分页参数
                if (request.PageNumber < 1)
                {
                    request.PageNumber = 1;
                }

                if (request.PageSize < 1 || request.PageSize > 100)
                {
                    request.PageSize = 20;
                }

                // 搜索评论
                var searchResult = await _taskRepository.SearchCommentsAsync(
                    taskId: request.TaskId,
                    searchKeyword: request.SearchKeyword,
                    userId: request.UserId,
                    startDate: request.StartDate,
                    endDate: request.EndDate,
                    isPinned: request.IsPinned,
                    pageNumber: request.PageNumber,
                    pageSize: request.PageSize,
                    sortBy: request.SortBy,
                    sortDirection: request.SortDirection,
                    topLevelOnly: request.TopLevelOnly,
                    isEdited: request.IsEdited,
                    includeDeleted: request.IncludeDeleted,
                    minContentLength: request.MinContentLength,
                    maxContentLength: request.MaxContentLength,
                    hasMentions: request.HasMentions
                );

                if (searchResult == null || !searchResult.Items.Any())
                {
                    _logger.LogInformation("未找到匹配的评论");
                    return ApiResponse<PagedResultType>.CreateSuccess(
                        new PagedResultType
                        {
                            Items = new List<CommentDto>(),
                            TotalCount = 0,
                            PageIndex = request.PageNumber,
                            PageSize = request.PageSize
                        },
                        "未找到匹配的评论"
                    );
                }

                // 获取所有相关用户信息
                var userIds = searchResult.Items.Select(c => c.UserId).Distinct().ToList();
                var users = await _coreDataQueryService.GetUsersAsync(userIds);
                var userDict = users.ToDictionary(u => u.Id, u => u);

                // 转换为DTO
                var commentDtos = searchResult.Items.Select(comment => new CommentDto
                {
                    CommentId = comment.CommentId,
                    TaskId = comment.TaskId,
                    Content = comment.Content,
                    UserId = comment.UserId,
                    UserName = userDict.TryGetValue(comment.UserId, out var user) ? user.Name : "未知用户",
                    UserAvatarUrl = userDict.TryGetValue(comment.UserId, out var userAvatar) ? userAvatar.AvatarUrl ?? "" : "",
                    CreationTimestamp = comment.CreationTimestamp,
                    LastUpdatedTimestamp = comment.LastUpdatedTimestamp,
                    ParentCommentId = comment.ParentCommentId,
                    IsPinned = comment.IsPinned,
                    IsEdited = comment.IsEdited,
                    MentionedUserIds = comment.MentionedUserIds ?? ""
                }).ToList();

                var result = new PagedResultType
                {
                    Items = commentDtos,
                    TotalCount = searchResult.TotalCount,
                    PageIndex = request.PageNumber,
                    PageSize = request.PageSize
                };

                _logger.LogInformation("成功处理搜索评论查询，找到 {Count} 条评论", commentDtos.Count);
                return ApiResponse<PagedResultType>.CreateSuccess(result, "搜索评论成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理搜索评论查询时发生错误，关键词: {SearchKeyword}", request.SearchKeyword);
                return ApiResponse<PagedResultType>.CreateFail($"搜索评论时发生错误: {ex.Message}");
            }
        }
    }
}
