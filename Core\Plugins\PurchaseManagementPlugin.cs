// IT资产管理系统 - 采购管理插件
// 文件路径: /Core/Plugins/PurchaseManagementPlugin.cs
// 功能: 采购管理插件模块，实现简化的采购流程

using ItAssetsSystem.Core.Abstractions;
using ItAssetsSystem.Core.Events;
using ItAssetsSystem.Models.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System;

namespace ItAssetsSystem.Core.Plugins
{
    /// <summary>
    /// 采购管理插件
    /// </summary>
    public class PurchaseManagementPlugin : PluginBase
    {
        /// <inheritdoc/>
        public override string Name => "PurchaseManagement";

        /// <inheritdoc/>
        public override string Description => "采购管理插件，提供简化的采购流程";

        /// <inheritdoc/>
        public override Version Version => new Version(1, 0, 0);

        /// <summary>
        /// 事件总线
        /// </summary>
        private ItAssetsSystem.Core.Events.IEventBus _eventBus;

        /// <inheritdoc/>
        public override void RegisterServices(IServiceCollection services)
        {
            // 注册采购管理相关服务
            services.AddScoped<ILegacyPurchaseService, LegacyPurchaseService>();
        }

        /// <summary>
        /// 插件启动
        /// </summary>
        protected override void OnStart()
        {
            _eventBus = ServiceProvider.GetRequiredService<ItAssetsSystem.Core.Events.IEventBus>();
            
            // 订阅事件
            _eventBus.Subscribe<PurchaseOrderDeliveredEvent>(HandlePurchaseOrderDelivered);
            
            Logger.LogInformation("采购管理插件已启动");
        }

        /// <summary>
        /// 插件停止
        /// </summary>
        protected override void OnStop()
        {
            // 取消订阅事件
            _eventBus.Unsubscribe<PurchaseOrderDeliveredEvent>(HandlePurchaseOrderDelivered);
            
            Logger.LogInformation("采购管理插件已停止");
        }

        /// <summary>
        /// 处理采购订单已到货事件
        /// </summary>
        private void HandlePurchaseOrderDelivered(PurchaseOrderDeliveredEvent @event)
        {
            Logger.LogInformation($"采购订单 {@event.OrderNumber} 已到货，准备生成资产");

            var purchaseService = ServiceProvider.GetRequiredService<ILegacyPurchaseService>();
            purchaseService.GenerateAssetsFromOrder(@event.OrderId);
        }
    }

    /// <summary>
    /// 采购订单已到货事件
    /// </summary>
    public class PurchaseOrderDeliveredEvent
    {
        /// <summary>
        /// 订单ID
        /// </summary>
        public int OrderId { get; set; }
        
        /// <summary>
        /// 订单编号
        /// </summary>
        public string OrderNumber { get; set; }
    }

    /// <summary>
    /// 遗留采购服务接口（用于插件兼容）
    /// </summary>
    public interface ILegacyPurchaseService
    {
        /// <summary>
        /// 从采购订单生成资产
        /// </summary>
        void GenerateAssetsFromOrder(int orderId);
    }

    /// <summary>
    /// 遗留采购服务实现（用于插件兼容）
    /// </summary>
    public class LegacyPurchaseService : ILegacyPurchaseService
    {
        private readonly ILogger<LegacyPurchaseService> _logger;

        /// <summary>
        /// 构造函数
        /// </summary>
        public LegacyPurchaseService(ILogger<LegacyPurchaseService> logger)
        {
            _logger = logger;
        }

        /// <inheritdoc/>
        public void GenerateAssetsFromOrder(int orderId)
        {
            _logger.LogInformation($"从采购订单 {orderId} 生成资产");
            // 实际实现中会从数据库查询订单和物品信息，生成资产记录
        }
    }
}

// 计划行数: 100
// 实际行数: 100 