// File: Application/Features/Tasks/Services/TaskClaimService.cs
// Description: 任务领取服务

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using ItAssetsSystem.Infrastructure.Data;
using ItAssetsSystem.Domain.Entities.Tasks;
using ItAssetsSystem.Application.Common.Dtos;
using ItAssetsSystem.Application.Features.Tasks.Dtos;
using ItAssetsSystem.Services.Interfaces;
using TaskClaimStatisticsDto = ItAssetsSystem.Application.Common.Dtos.TaskClaimStatisticsDto;

namespace ItAssetsSystem.Application.Features.Tasks.Services
{
    /// <summary>
    /// 任务领取服务
    /// </summary>
    public class TaskClaimService
    {
        private readonly AppDbContext _context;
        private readonly ILogger<TaskClaimService> _logger;
        private readonly WorkShiftService _workShiftService;
        private readonly IGamificationService _gamificationService;
        private readonly ITaskClaimCacheService _taskClaimCacheService;

        public TaskClaimService(
            AppDbContext context,
            ILogger<TaskClaimService> logger,
            WorkShiftService workShiftService,
            IGamificationService gamificationService,
            ITaskClaimCacheService taskClaimCacheService)
        {
            _context = context;
            _logger = logger;
            _workShiftService = workShiftService;
            _gamificationService = gamificationService;
            _taskClaimCacheService = taskClaimCacheService;
        }

        /// <summary>
        /// 领取任务
        /// </summary>
        public async Task<ApiResponse<TaskClaimDto>> ClaimTaskAsync(CreateTaskClaimDto request, int currentUserId)
        {
            try
            {
                // 检查任务是否存在
                var task = await _context.Tasks.FirstOrDefaultAsync(t => t.TaskId == request.TaskId);
                if (task == null)
                {
                    return ApiResponse<TaskClaimDto>.CreateFail("任务不存在");
                }

                // 检查任务状态是否可以领取
                if (task.Status != "Todo")
                {
                    return ApiResponse<TaskClaimDto>.CreateFail("任务状态不允许领取");
                }

                // 🔧 检查当前用户是否为任务负责人（主负责人或多负责人中的一员）
                var isMainAssignee = task.AssigneeUserId == currentUserId;
                var isMultiAssignee = false;

                if (!isMainAssignee)
                {
                    // 检查是否为多负责人中的一员
                    isMultiAssignee = await _context.TaskAssignees
                        .AnyAsync(ta => ta.TaskId == request.TaskId && ta.UserId == currentUserId);
                }

                if (!isMainAssignee && !isMultiAssignee)
                {
                    return ApiResponse<TaskClaimDto>.CreateFail("您不是该任务的负责人，无法领取");
                }

                // 获取用户当前班次
                var userShiftResult = await _workShiftService.GetUserCurrentShiftAsync(currentUserId);
                if (!userShiftResult.Success || userShiftResult.Data == null)
                {
                    return ApiResponse<TaskClaimDto>.CreateFail("用户未分配班次，无法领取任务");
                }

                var currentShift = userShiftResult.Data;
                var today = DateTime.Today;

                // 检查是否已经领取过该任务
                var existingClaim = await _context.TaskClaims
                    .FirstOrDefaultAsync(c => c.TaskId == request.TaskId 
                                           && c.ClaimedBy == currentUserId 
                                           && c.ClaimDate.Date == today);

                if (existingClaim != null)
                {
                    return ApiResponse<TaskClaimDto>.CreateFail("今日已领取该任务");
                }

                // 创建领取记录
                var claim = new TaskClaim
                {
                    TaskId = request.TaskId,
                    ClaimedBy = currentUserId,
                    ShiftId = currentShift.ShiftId,
                    ClaimedAt = DateTime.Now,
                    ClaimDate = today,
                    ClaimStatus = "Claimed",
                    Notes = request.Notes,
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now
                };

                _context.TaskClaims.Add(claim);

                // 任务领取不自动改变任务状态，只记录领取信息
                // 任务状态应该通过单独的操作来更改
                task.LastUpdatedTimestamp = DateTime.Now;

                await _context.SaveChangesAsync();

                // 🔧 更新任务领取状态缓存
                try
                {
                    var claimInfo = new TaskClaimInfo
                    {
                        TaskId = request.TaskId,
                        ClaimedByUserId = currentUserId,
                        ClaimedByUserName = null, // 将在缓存服务中查询用户名
                        ClaimedAt = claim.ClaimedAt,
                        ClaimStatus = claim.ClaimStatus,
                        IsClaimedByCurrentUser = true
                    };

                    await _taskClaimCacheService.UpdateTaskClaimStatusAsync(request.TaskId, currentUserId, claimInfo, DateTime.Today);
                    _logger.LogDebug("已更新任务 {TaskId} 的领取状态缓存，用户: {UserId}", request.TaskId, currentUserId);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "更新任务领取状态缓存失败，但不影响业务流程");
                }

                // 🎮 游戏化奖励：任务领取
                try
                {
                    var reward = await _gamificationService.ClaimTaskRewardAsync(currentUserId, request.TaskId);
                    if (reward.Success)
                    {
                        _logger.LogInformation("用户 {UserId} 领取任务 {TaskId} 获得奖励: {XP}XP, {Points}金币",
                            currentUserId, request.TaskId, reward.XPGained, reward.PointsGained);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "用户 {UserId} 领取任务 {TaskId} 时游戏化奖励发放失败", currentUserId, request.TaskId);
                }

                var claimDto = new TaskClaimDto
                {
                    ClaimId = claim.ClaimId,
                    TaskId = claim.TaskId,
                    ClaimedBy = claim.ClaimedBy,
                    ShiftId = claim.ShiftId,
                    ClaimedAt = claim.ClaimedAt,
                    ClaimDate = claim.ClaimDate,
                    ClaimStatus = claim.ClaimStatus,
                    Notes = claim.Notes,
                    TaskName = task.Name
                };

                return ApiResponse<TaskClaimDto>.CreateSuccess(claimDto, "任务领取成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "领取任务时发生错误");
                return ApiResponse<TaskClaimDto>.CreateFail("领取任务失败: " + ex.Message);
            }
        }

        /// <summary>
        /// 更新任务领取状态
        /// </summary>
        public async Task<ApiResponse<TaskClaimDto>> UpdateClaimStatusAsync(long claimId, UpdateTaskClaimStatusDto request, int currentUserId)
        {
            try
            {
                var claim = await _context.TaskClaims
                    .Include(c => c.Task)
                    .FirstOrDefaultAsync(c => c.ClaimId == claimId);

                if (claim == null)
                {
                    return ApiResponse<TaskClaimDto>.CreateFail("领取记录不存在");
                }

                // 检查权限
                if (claim.ClaimedBy != currentUserId)
                {
                    return ApiResponse<TaskClaimDto>.CreateFail("无权限操作该领取记录");
                }

                var oldStatus = claim.ClaimStatus;
                claim.ClaimStatus = request.ClaimStatus;
                claim.Notes = request.Notes;
                claim.UpdatedAt = DateTime.Now;

                // 根据状态更新时间戳
                switch (request.ClaimStatus)
                {
                    case "Started":
                        claim.StartedAt = DateTime.Now;
                        claim.Task.Status = "InProgress";
                        claim.Task.ActualStartDate = DateTime.Now;
                        break;
                    case "Completed":
                        claim.CompletedAt = DateTime.Now;
                        claim.Task.Status = "Done";
                        claim.Task.ActualEndDate = DateTime.Now;

                        // 🎮 游戏化奖励：任务完成
                        try
                        {
                            var isOnTime = claim.Task.PlanEndDate == null || DateTime.Now <= claim.Task.PlanEndDate;
                            var reward = await _gamificationService.CompleteTaskRewardAsync(currentUserId, claim.TaskId, isOnTime);
                            if (reward.Success)
                            {
                                _logger.LogInformation("用户 {UserId} 完成任务 {TaskId} 获得奖励: {XP}XP, {Points}金币 (按时: {OnTime})",
                                    currentUserId, claim.TaskId, reward.XPGained, reward.PointsGained, isOnTime);
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, "用户 {UserId} 完成任务 {TaskId} 时游戏化奖励发放失败", currentUserId, claim.TaskId);
                        }
                        break;
                    case "Cancelled":
                        claim.Task.Status = "Todo";
                        claim.Task.ActualStartDate = null;
                        break;
                }

                claim.Task.LastUpdatedTimestamp = DateTime.Now;
                await _context.SaveChangesAsync();

                var claimDto = new TaskClaimDto
                {
                    ClaimId = claim.ClaimId,
                    TaskId = claim.TaskId,
                    ClaimedBy = claim.ClaimedBy,
                    ShiftId = claim.ShiftId,
                    ClaimedAt = claim.ClaimedAt,
                    ClaimDate = claim.ClaimDate,
                    ClaimStatus = claim.ClaimStatus,
                    StartedAt = claim.StartedAt,
                    CompletedAt = claim.CompletedAt,
                    Notes = claim.Notes,
                    TaskName = claim.Task.Name
                };

                return ApiResponse<TaskClaimDto>.CreateSuccess(claimDto, "更新领取状态成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新任务领取状态时发生错误");
                return ApiResponse<TaskClaimDto>.CreateFail("更新领取状态失败: " + ex.Message);
            }
        }

        /// <summary>
        /// 获取用户今日任务领取记录
        /// </summary>
        public async Task<ApiResponse<List<TaskClaimDto>>> GetUserTodayClaimsAsync(int userId)
        {
            try
            {
                var today = DateTime.Today;
                var claims = await _context.TaskClaims
                    .Include(c => c.Task)
                    .Include(c => c.WorkShift)
                    .Where(c => c.ClaimedBy == userId && c.ClaimDate.Date == today)
                    .OrderByDescending(c => c.ClaimedAt)
                    .Select(c => new TaskClaimDto
                    {
                        ClaimId = c.ClaimId,
                        TaskId = c.TaskId,
                        ClaimedBy = c.ClaimedBy,
                        ShiftId = c.ShiftId,
                        ClaimedAt = c.ClaimedAt,
                        ClaimDate = c.ClaimDate,
                        ClaimStatus = c.ClaimStatus,
                        StartedAt = c.StartedAt,
                        CompletedAt = c.CompletedAt,
                        Notes = c.Notes,
                        TaskName = c.Task.Name,
                        ShiftName = c.WorkShift.ShiftName
                    })
                    .ToListAsync();

                return ApiResponse<List<TaskClaimDto>>.CreateSuccess(claims, "获取今日任务领取记录成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户今日任务领取记录时发生错误");
                return ApiResponse<List<TaskClaimDto>>.CreateFail("获取今日任务领取记录失败: " + ex.Message);
            }
        }

        /// <summary>
        /// 获取今日班次任务统计
        /// </summary>
        public async Task<ApiResponse<List<TaskClaimStatisticsDto>>> GetTodayShiftStatisticsAsync(ShiftTaskStatisticsQueryDto query)
        {
            try
            {
                var statisticsDate = query.StatisticsDate?.Date ?? DateTime.Today;

                // 获取所有用户班次分配
                var userShiftQuery = _context.UserShiftAssignments
                    .Include(a => a.User)
                    .Include(a => a.WorkShift)
                    .Where(a => a.IsActive 
                             && a.EffectiveDate <= statisticsDate 
                             && (a.ExpiryDate == null || a.ExpiryDate > statisticsDate));

                if (query.ShiftId.HasValue)
                {
                    userShiftQuery = userShiftQuery.Where(a => a.ShiftId == query.ShiftId.Value);
                }

                if (query.UserId.HasValue)
                {
                    userShiftQuery = userShiftQuery.Where(a => a.UserId == query.UserId.Value);
                }

                var userShifts = await userShiftQuery.ToListAsync();

                var statistics = new List<TaskClaimStatisticsDto>();

                foreach (var userShift in userShifts)
                {
                    // 获取该用户在该班次的任务领取统计
                    var claims = await _context.TaskClaims
                        .Where(c => c.ClaimedBy == userShift.UserId 
                                 && c.ShiftId == userShift.ShiftId 
                                 && c.ClaimDate.Date == statisticsDate)
                        .ToListAsync();

                    // 获取该用户可领取但未领取的任务数（这里简化处理，实际可能需要更复杂的逻辑）
                    var totalAvailableTasks = await _context.Tasks
                        .Where(t => t.Status == "Todo" 
                                 && t.CreationTimestamp.Date <= statisticsDate)
                        .CountAsync();

                    var claimedCount = claims.Count;
                    var startedCount = claims.Count(c => c.ClaimStatus == "Started" || c.ClaimStatus == "Completed");
                    var completedCount = claims.Count(c => c.ClaimStatus == "Completed");
                    var cancelledCount = claims.Count(c => c.ClaimStatus == "Cancelled");

                    var stat = new TaskClaimStatisticsDto
                    {
                        UserId = userShift.UserId,
                        UserName = userShift.User.Name,
                        ShiftId = (int)userShift.ShiftId,
                        ShiftName = userShift.WorkShift.ShiftName,
                        StatisticsDate = statisticsDate,
                        ClaimedTasksCount = claimedCount,
                        StartedTasksCount = startedCount,
                        CompletedTasksCount = completedCount,
                        CancelledTasksCount = cancelledCount,
                        UnclaimedTasksCount = Math.Max(0, totalAvailableTasks - claimedCount),
                        CompletionRate = claimedCount > 0 ? (decimal)completedCount / claimedCount * 100 : 0
                    };

                    statistics.Add(stat);
                }

                return ApiResponse<List<TaskClaimStatisticsDto>>.CreateSuccess(statistics, "获取班次任务统计成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取今日班次任务统计时发生错误");
                return ApiResponse<List<TaskClaimStatisticsDto>>.CreateFail("获取班次任务统计失败: " + ex.Message);
            }
        }
    }
}
