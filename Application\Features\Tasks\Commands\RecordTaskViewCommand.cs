// File: Application/Features/Tasks/Commands/RecordTaskViewCommand.cs
// Description: 记录当前用户查看某任务

using MediatR;
using System.Threading;
using System.Threading.Tasks;

namespace ItAssetsSystem.Application.Features.Tasks.Commands
{
    public class RecordTaskViewCommand : IRequest<bool>
    {
        public long UserId { get; set; }
        public long TaskId { get; set; }
    }

    public class RecordTaskViewCommandHandler : IRequestHandler<RecordTaskViewCommand, bool>
    {
        public RecordTaskViewCommandHandler()
        {
        }

        public async Task<bool> Handle(RecordTaskViewCommand request, CancellationToken cancellationToken)
        {
            // TODO: 这里应落库"任务查看日志"表，记录用户查看任务的行为
            // 目前先空实现，直接返回true
            return await Task.FromResult(true);
        }
    }
} 