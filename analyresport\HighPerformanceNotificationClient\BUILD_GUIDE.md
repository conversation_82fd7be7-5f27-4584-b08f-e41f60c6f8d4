# High Performance Notification Client - Build Guide

## 构建问题修复指南

由于原始项目缺少很多源文件，我创建了一个简化版本来解决构建问题。

## 文件说明

### 新增文件
- `CMakeLists_simple.txt` - 简化的CMake配置，只编译存在的文件
- `src/demo_main.cpp` - 可以实际编译运行的演示客户端
- `build_simple.bat` - 简化的构建脚本（英文版，避免编码问题）
- `test_build.bat` - 构建环境测试脚本
- `BUILD_GUIDE.md` - 本指南

### 问题分析
原始项目的问题：
1. CMakeLists.txt引用了大量不存在的源文件
2. main.cpp依赖缺失的头文件
3. 项目结构不完整
4. 批处理文件编码问题导致乱码

## 构建步骤

### 1. 环境检查
首先运行环境测试：
```bash
test_build.bat
```

### 2. 安装必要工具
如果测试失败，请安装：
- **CMake**: https://cmake.org/download/
- **Visual Studio 2019+** 或 **Visual Studio Build Tools**

### 3. 运行构建
```bash
build_simple.bat
```

### 4. 运行程序
构建成功后：
```bash
cd build_simple\bin\Release
SimpleNotificationDemo.exe --help
```

## 程序功能

演示客户端功能：
- UDP通信测试
- 命令行参数支持
- 基本的网络连接测试
- 定时发送测试消息

## 使用示例

```bash
# 默认连接本地服务器
SimpleNotificationDemo.exe

# 连接指定服务器
SimpleNotificationDemo.exe --host ************* --port 8080

# 显示帮助
SimpleNotificationDemo.exe --help
```

## 故障排除

### 构建失败
1. 确保从"Developer Command Prompt"运行
2. 检查Visual Studio版本兼容性
3. 确认Windows SDK已安装

### 运行时错误
1. 检查防火墙设置
2. 确认目标服务器可达
3. 检查端口是否被占用

## 扩展开发

要扩展功能，可以：
1. 添加缺失的头文件到`include/`目录
2. 实现缺失的源文件
3. 更新CMakeLists_simple.txt添加新文件
4. 参考demo_main.cpp的简化实现模式

## 与后端集成

此客户端可以与IT资产管理系统的通知模块集成：
- 修改服务器地址指向后端API
- 实现JWT认证
- 添加SignalR支持
- 集成实时通知显示

---

通过这个简化版本，你可以：
✅ 成功编译和运行
✅ 测试基本网络功能  
✅ 作为进一步开发的基础
✅ 避免编码和依赖问题