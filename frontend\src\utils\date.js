/**
 * 日期工具函数
 */

/**
 * 格式化日期
 * @param {Date|string|number} date - 日期对象、日期字符串或时间戳
 * @param {string} format - 格式字符串，默认为 'YYYY-MM-DD HH:mm:ss'
 * @returns {string} 格式化后的日期字符串
 */
export function formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
  if (!date) return ''
  
  const d = new Date(date)
  if (isNaN(d.getTime())) return ''
  
  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  const hour = String(d.getHours()).padStart(2, '0')
  const minute = String(d.getMinutes()).padStart(2, '0')
  const second = String(d.getSeconds()).padStart(2, '0')
  
  return format
    .replace('YYYY', year)
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hour)
    .replace('mm', minute)
    .replace('ss', second)
}

/**
 * 格式化日期为简短格式
 * @param {Date|string|number} date - 日期
 * @returns {string} 格式化后的日期字符串 (YYYY-MM-DD)
 */
export function formatDateShort(date) {
  return formatDate(date, 'YYYY-MM-DD')
}

/**
 * 格式化时间为简短格式
 * @param {Date|string|number} date - 日期
 * @returns {string} 格式化后的时间字符串 (HH:mm:ss)
 */
export function formatTimeShort(date) {
  return formatDate(date, 'HH:mm:ss')
}

/**
 * 获取相对时间描述
 * @param {Date|string|number} date - 日期
 * @returns {string} 相对时间描述
 */
export function getRelativeTime(date) {
  if (!date) return ''
  
  const d = new Date(date)
  if (isNaN(d.getTime())) return ''
  
  const now = new Date()
  const diff = now.getTime() - d.getTime()
  const seconds = Math.floor(diff / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)
  const days = Math.floor(hours / 24)
  
  if (days > 0) {
    return `${days}天前`
  } else if (hours > 0) {
    return `${hours}小时前`
  } else if (minutes > 0) {
    return `${minutes}分钟前`
  } else {
    return '刚刚'
  }
}

/**
 * 判断是否为今天
 * @param {Date|string|number} date - 日期
 * @returns {boolean} 是否为今天
 */
export function isToday(date) {
  if (!date) return false
  
  const d = new Date(date)
  if (isNaN(d.getTime())) return false
  
  const today = new Date()
  return d.toDateString() === today.toDateString()
}

/**
 * 判断是否为昨天
 * @param {Date|string|number} date - 日期
 * @returns {boolean} 是否为昨天
 */
export function isYesterday(date) {
  if (!date) return false
  
  const d = new Date(date)
  if (isNaN(d.getTime())) return false
  
  const yesterday = new Date()
  yesterday.setDate(yesterday.getDate() - 1)
  return d.toDateString() === yesterday.toDateString()
}

/**
 * 获取日期范围
 * @param {number} days - 天数
 * @returns {Array} [开始日期, 结束日期]
 */
export function getDateRange(days) {
  const end = new Date()
  const start = new Date()
  start.setDate(start.getDate() - days)
  return [start, end]
}

/**
 * 计算两个日期之间的天数差
 * @param {Date|string|number} date1 - 日期1
 * @param {Date|string|number} date2 - 日期2
 * @returns {number} 天数差
 */
export function getDaysDiff(date1, date2) {
  if (!date1 || !date2) return 0
  
  const d1 = new Date(date1)
  const d2 = new Date(date2)
  
  if (isNaN(d1.getTime()) || isNaN(d2.getTime())) return 0
  
  const diff = Math.abs(d2.getTime() - d1.getTime())
  return Math.ceil(diff / (1000 * 60 * 60 * 24))
}

/**
 * 获取月份的第一天
 * @param {Date|string|number} date - 日期
 * @returns {Date} 月份的第一天
 */
export function getFirstDayOfMonth(date = new Date()) {
  const d = new Date(date)
  return new Date(d.getFullYear(), d.getMonth(), 1)
}

/**
 * 获取月份的最后一天
 * @param {Date|string|number} date - 日期
 * @returns {Date} 月份的最后一天
 */
export function getLastDayOfMonth(date = new Date()) {
  const d = new Date(date)
  return new Date(d.getFullYear(), d.getMonth() + 1, 0)
}

/**
 * 添加天数
 * @param {Date|string|number} date - 日期
 * @param {number} days - 要添加的天数
 * @returns {Date} 新日期
 */
export function addDays(date, days) {
  const d = new Date(date)
  d.setDate(d.getDate() + days)
  return d
}

/**
 * 添加月份
 * @param {Date|string|number} date - 日期
 * @param {number} months - 要添加的月份数
 * @returns {Date} 新日期
 */
export function addMonths(date, months) {
  const d = new Date(date)
  d.setMonth(d.getMonth() + months)
  return d
}

/**
 * 验证日期字符串格式
 * @param {string} dateString - 日期字符串
 * @param {string} format - 期望的格式
 * @returns {boolean} 是否符合格式
 */
export function isValidDateFormat(dateString, format = 'YYYY-MM-DD') {
  if (!dateString) return false
  
  const regex = format
    .replace('YYYY', '\\d{4}')
    .replace('MM', '\\d{2}')
    .replace('DD', '\\d{2}')
    .replace('HH', '\\d{2}')
    .replace('mm', '\\d{2}')
    .replace('ss', '\\d{2}')
  
  return new RegExp(`^${regex}$`).test(dateString)
}

/**
 * 解析日期字符串
 * @param {string} dateString - 日期字符串
 * @returns {Date|null} 解析后的日期对象，失败返回null
 */
export function parseDate(dateString) {
  if (!dateString) return null
  
  const date = new Date(dateString)
  return isNaN(date.getTime()) ? null : date
}
