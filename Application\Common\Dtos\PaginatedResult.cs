// File: Application/Common/Dtos/PaginatedResult.cs
// Description: 分页结果类

using System.Collections.Generic;

namespace ItAssetsSystem.Application.Common.Dtos
{
    /// <summary>
    /// 分页结果类
    /// </summary>
    /// <typeparam name="T">数据项类型</typeparam>
    public class PaginatedResult<T>
    {
        /// <summary>
        /// 当前页码
        /// </summary>
        public int PageIndex { get; set; }
        
        /// <summary>
        /// 每页数量
        /// </summary>
        public int PageSize { get; set; }
        
        /// <summary>
        /// 总记录数
        /// </summary>
        public int TotalCount { get; set; }
        
        /// <summary>
        /// 总页数
        /// </summary>
        public int TotalPages { get; set; }
        
        /// <summary>
        /// 是否有上一页
        /// </summary>
        public bool HasPrevious => PageIndex > 1;
        
        /// <summary>
        /// 是否有下一页
        /// </summary>
        public bool HasNext => PageIndex < TotalPages;
        
        /// <summary>
        /// 数据列表
        /// </summary>
        public List<T> Items { get; set; } = new List<T>();
        
        /// <summary>
        /// 创建分页结果
        /// </summary>
        /// <param name="items">数据列表</param>
        /// <param name="totalCount">总记录数</param>
        /// <param name="pageIndex">当前页码</param>
        /// <param name="pageSize">每页数量</param>
        /// <returns>分页结果</returns>
        public static PaginatedResult<T> Create(List<T> items, int totalCount, int pageIndex, int pageSize)
        {
            var totalPages = (totalCount + pageSize - 1) / pageSize;
            
            return new PaginatedResult<T>
            {
                PageIndex = pageIndex,
                PageSize = pageSize,
                TotalCount = totalCount,
                TotalPages = totalPages,
                Items = items
            };
        }
    }
} 