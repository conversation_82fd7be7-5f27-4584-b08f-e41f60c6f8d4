-- =====================================================
-- 游戏化统计数据修复和完善脚本
-- 修复任务领取、完成统计、排行榜等功能
-- =====================================================

-- 1. 修复已完成任务的完成人信息
SELECT '=== 1. 修复已完成任务的完成人信息 ===' AS Info;

UPDATE tasks 
SET CompletedByUserId = COALESCE(AssigneeUserId, CreatorUserId),
    CompletedAt = COALESCE(ActualEndDate, LastUpdatedTimestamp),
    CompletionWatermarkColor = '#409EFF'
WHERE Status = 'Done' 
  AND (CompletedByUserId IS NULL OR CompletedAt IS NULL OR CompletionWatermarkColor IS NULL)
  AND IsDeleted = 0;

SELECT CONCAT('修复了 ', ROW_COUNT(), ' 个任务的完成人信息') AS 修复结果1;

-- 2. 初始化用户游戏化统计数据
SELECT '=== 2. 初始化用户游戏化统计数据 ===' AS Info;

INSERT INTO user_stats (
    UserId, 
    PointsBalance, 
    XpBalance, 
    CoinsBalance, 
    DiamondsBalance,
    TasksCompletedCount,
    TasksClaimedCount,
    TasksCreatedCount,
    FaultsReportedCount,
    AssetsUpdatedCount,
    LoginStreakCount,
    LastLoginDate,
    CreatedAt,
    UpdatedAt
)
SELECT 
    u.id,
    0, -- PointsBalance 将通过重新计算设置
    0, -- XpBalance 将通过重新计算设置
    0, -- CoinsBalance 将通过重新计算设置
    0, -- DiamondsBalance 将通过重新计算设置
    0, -- TasksCompletedCount 将通过重新计算设置
    0, -- TasksClaimedCount 将通过重新计算设置
    0, -- TasksCreatedCount 将通过重新计算设置
    0, -- FaultsReportedCount 将通过重新计算设置
    0, -- AssetsUpdatedCount 将通过重新计算设置
    0, -- LoginStreakCount
    CURDATE(), -- LastLoginDate
    NOW(), -- CreatedAt
    NOW()  -- UpdatedAt
FROM users u
WHERE u.IsDeleted = 0
  AND NOT EXISTS (
    SELECT 1 FROM user_stats us WHERE us.UserId = u.id
  );

SELECT CONCAT('初始化了 ', ROW_COUNT(), ' 个用户的游戏化统计数据') AS 初始化结果;

-- 3. 重新计算用户任务统计
SELECT '=== 3. 重新计算用户任务统计 ===' AS Info;

-- 更新任务完成数量
UPDATE user_stats us
SET TasksCompletedCount = (
    SELECT COUNT(*)
    FROM tasks t
    WHERE t.CompletedByUserId = us.UserId
      AND t.Status = 'Done'
      AND t.IsDeleted = 0
);

-- 更新任务创建数量
UPDATE user_stats us
SET TasksCreatedCount = (
    SELECT COUNT(*)
    FROM tasks t
    WHERE t.CreatorUserId = us.UserId
      AND t.IsDeleted = 0
);

-- 更新任务领取数量
UPDATE user_stats us
SET TasksClaimedCount = (
    SELECT COUNT(*)
    FROM task_claims tc
    WHERE tc.UserId = us.UserId
      AND tc.IsDeleted = 0
);

-- 更新故障报告数量
UPDATE user_stats us
SET FaultsReportedCount = (
    SELECT COUNT(*)
    FROM faults f
    WHERE f.ReporterUserId = us.UserId
      AND f.IsDeleted = 0
);

SELECT '任务统计数据重新计算完成' AS 计算结果;

-- 4. 重新计算积分和经验值
SELECT '=== 4. 重新计算积分和经验值 ===' AS Info;

UPDATE user_stats us
SET 
    PointsBalance = (
        -- 任务完成积分 (每个20分)
        COALESCE(us.TasksCompletedCount, 0) * 20 +
        -- 任务领取积分 (每个3分)
        COALESCE(us.TasksClaimedCount, 0) * 3 +
        -- 任务创建积分 (每个5分)
        COALESCE(us.TasksCreatedCount, 0) * 5 +
        -- 故障报告积分 (每个15分)
        COALESCE(us.FaultsReportedCount, 0) * 15
    ),
    XpBalance = (
        -- 任务完成经验 (每个10经验)
        COALESCE(us.TasksCompletedCount, 0) * 10 +
        -- 任务领取经验 (每个2经验)
        COALESCE(us.TasksClaimedCount, 0) * 2 +
        -- 任务创建经验 (每个3经验)
        COALESCE(us.TasksCreatedCount, 0) * 3 +
        -- 故障报告经验 (每个8经验)
        COALESCE(us.FaultsReportedCount, 0) * 8
    ),
    CoinsBalance = (
        -- 金币 = 积分 / 10
        (COALESCE(us.TasksCompletedCount, 0) * 20 +
         COALESCE(us.TasksClaimedCount, 0) * 3 +
         COALESCE(us.TasksCreatedCount, 0) * 5 +
         COALESCE(us.FaultsReportedCount, 0) * 15) / 10
    ),
    DiamondsBalance = (
        -- 钻石 = 完成任务数 / 5
        COALESCE(us.TasksCompletedCount, 0) / 5
    ),
    UpdatedAt = NOW();

SELECT '积分和经验值重新计算完成' AS 积分计算结果;

-- 5. 生成/更新排行榜数据
SELECT '=== 5. 生成排行榜数据 ===' AS Info;

-- 清理旧的排行榜数据
DELETE FROM leaderboards WHERE Period = 'current';

-- 生成当前积分排行榜
INSERT INTO leaderboards (
    UserId,
    LeaderboardType,
    Period,
    RankScore,
    Rank,
    LastUpdated,
    CreatedAt
)
SELECT 
    us.UserId,
    'points' AS LeaderboardType,
    'current' AS Period,
    us.PointsBalance AS RankScore,
    ROW_NUMBER() OVER (ORDER BY us.PointsBalance DESC) AS Rank,
    NOW() AS LastUpdated,
    NOW() AS CreatedAt
FROM user_stats us
JOIN users u ON u.id = us.UserId
WHERE u.IsDeleted = 0
  AND us.PointsBalance > 0
ORDER BY us.PointsBalance DESC;

-- 生成本周任务完成排行榜
INSERT INTO leaderboards (
    UserId,
    LeaderboardType,
    Period,
    RankScore,
    Rank,
    LastUpdated,
    CreatedAt
)
SELECT 
    t.CompletedByUserId AS UserId,
    'weekly_tasks' AS LeaderboardType,
    'current' AS Period,
    COUNT(*) AS RankScore,
    ROW_NUMBER() OVER (ORDER BY COUNT(*) DESC) AS Rank,
    NOW() AS LastUpdated,
    NOW() AS CreatedAt
FROM tasks t
JOIN users u ON u.id = t.CompletedByUserId
WHERE t.Status = 'Done'
  AND t.CompletedAt >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
  AND t.IsDeleted = 0
  AND u.IsDeleted = 0
GROUP BY t.CompletedByUserId
HAVING COUNT(*) > 0
ORDER BY COUNT(*) DESC;

SELECT CONCAT('生成了 ', ROW_COUNT(), ' 条排行榜记录') AS 排行榜生成结果;

-- 6. 创建每日统计视图（用于前端展示）
SELECT '=== 6. 创建统计视图 ===' AS Info;

-- 删除已存在的视图
DROP VIEW IF EXISTS v_daily_user_stats;

-- 创建每日用户统计视图
CREATE VIEW v_daily_user_stats AS
SELECT 
    u.id AS UserId,
    u.name AS UserName,
    u.avatar AS UserAvatar,
    DATE(NOW()) AS StatDate,
    
    -- 今日统计
    COUNT(CASE WHEN DATE(t.CompletedAt) = CURDATE() AND t.Status = 'Done' THEN 1 END) AS TodayCompletedTasks,
    COUNT(CASE WHEN DATE(tc.ClaimTimestamp) = CURDATE() THEN 1 END) AS TodayClaimedTasks,
    COUNT(CASE WHEN DATE(t.CreationTimestamp) = CURDATE() AND t.CreatorUserId = u.id THEN 1 END) AS TodayCreatedTasks,
    COUNT(CASE WHEN DATE(f.CreationTimestamp) = CURDATE() AND f.ReporterUserId = u.id THEN 1 END) AS TodayReportedFaults,
    
    -- 本周统计
    COUNT(CASE WHEN DATE(t.CompletedAt) >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) AND t.Status = 'Done' THEN 1 END) AS WeeklyCompletedTasks,
    COUNT(CASE WHEN DATE(tc.ClaimTimestamp) >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) THEN 1 END) AS WeeklyClaimedTasks,
    COUNT(CASE WHEN DATE(t.CreationTimestamp) >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) AND t.CreatorUserId = u.id THEN 1 END) AS WeeklyCreatedTasks,
    
    -- 总计统计
    us.TasksCompletedCount AS TotalCompletedTasks,
    us.TasksClaimedCount AS TotalClaimedTasks,
    us.TasksCreatedCount AS TotalCreatedTasks,
    us.FaultsReportedCount AS TotalReportedFaults,
    
    -- 游戏化数据
    us.PointsBalance,
    us.XpBalance,
    us.CoinsBalance,
    us.DiamondsBalance
    
FROM users u
LEFT JOIN user_stats us ON us.UserId = u.id
LEFT JOIN tasks t ON (t.CompletedByUserId = u.id OR t.CreatorUserId = u.id) AND t.IsDeleted = 0
LEFT JOIN task_claims tc ON tc.UserId = u.id AND tc.IsDeleted = 0
LEFT JOIN faults f ON f.ReporterUserId = u.id AND f.IsDeleted = 0
WHERE u.IsDeleted = 0
GROUP BY u.id, u.name, u.avatar, us.TasksCompletedCount, us.TasksClaimedCount, us.TasksCreatedCount, us.FaultsReportedCount, us.PointsBalance, us.XpBalance, us.CoinsBalance, us.DiamondsBalance;

SELECT '统计视图创建完成' AS 视图创建结果;

-- 7. 验证修复结果
SELECT '=== 7. 验证修复结果 ===' AS Info;

-- 验证任务完成人信息
SELECT 
    '已完成任务总数' AS 统计项,
    COUNT(*) AS 数量
FROM tasks 
WHERE Status = 'Done' AND IsDeleted = 0

UNION ALL

SELECT 
    '有完成人的已完成任务' AS 统计项,
    COUNT(*) AS 数量
FROM tasks 
WHERE Status = 'Done' AND CompletedByUserId IS NOT NULL AND IsDeleted = 0

UNION ALL

SELECT 
    '有完成时间的已完成任务' AS 统计项,
    COUNT(*) AS 数量
FROM tasks 
WHERE Status = 'Done' AND CompletedAt IS NOT NULL AND IsDeleted = 0

UNION ALL

SELECT 
    '有游戏化统计的用户数' AS 统计项,
    COUNT(*) AS 数量
FROM user_stats

UNION ALL

SELECT 
    '排行榜记录数' AS 统计项,
    COUNT(*) AS 数量
FROM leaderboards
WHERE Period = 'current';

-- 8. 显示Top 10用户统计
SELECT '=== 8. Top 10 用户统计 ===' AS Info;
SELECT 
    u.name AS 用户名,
    us.PointsBalance AS 积分,
    us.TasksCompletedCount AS 完成任务数,
    us.TasksClaimedCount AS 领取任务数,
    us.TasksCreatedCount AS 创建任务数,
    us.FaultsReportedCount AS 故障报告数
FROM user_stats us
JOIN users u ON u.id = us.UserId
WHERE u.IsDeleted = 0
ORDER BY us.PointsBalance DESC
LIMIT 10;
