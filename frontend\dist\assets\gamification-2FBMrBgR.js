import{aV as t}from"./index-CG5lHOPO.js";function e(){return t({url:"/v2/gamification-v2/stats/current",method:"get"})}function a(){return t({url:"/v2/gamification/rules",method:"get"})}function r(e){return t({url:"/v2/gamification/rules",method:"post",data:e})}function i(e){return t({url:"/v2/gamification/rules",method:"put",data:e})}function s(e){return t({url:`/v2/gamification/rules/${e}`,method:"delete"})}function n(e,a){return t({url:`/v2/gamification/rules/${e}/active`,method:"patch",params:{isActive:a}})}function o(){return t({url:"/v2/gamification/rules/reload",method:"post"})}function d(e="points",a="weekly",r=20){return t({url:"/v2/leaderboard",method:"get",params:{metric:e,period:a,limit:r}})}function u(e,a="测试任务",r="General",i=10){return t({url:"/v2/gamification-v2/test/task-created",method:"post",params:{taskId:e,taskName:a,taskType:r,points:i}})}function m(e,a="测试任务",r="General",i=!0,s=20){return t({url:"/v2/gamification-v2/test/task-completed",method:"post",params:{taskId:e,taskName:a,taskType:r,isOnTime:i,points:s}})}function l(e=null){return t({url:"/v2/gamification-v2/stats/update",method:"post",params:e?{userId:e}:{}})}function c(e="weekly",a="current"){return t({url:"/v2/gamification-v2/leaderboard/update",method:"post",params:{leaderboardType:e,period:a}})}const g={POINTS:"points",COINS:"coins",DIAMONDS:"diamonds",TASKS_CREATED:"tasks_created",TASKS_COMPLETED:"tasks_completed",TASKS_CLAIMED:"tasks_claimed",FAULTS_RECORDED:"faults_recorded",MAINTENANCE_CREATED:"maintenance_created",ASSETS_UPDATED:"assets_updated"},p={DAILY:"daily",WEEKLY:"weekly",MONTHLY:"monthly",QUARTERLY:"quarterly",YEARLY:"yearly",ALLTIME:"alltime"},f={getUserStats:e,getUserStatsById:function(e){return t({url:`/v2/gamification/stats/${e}`,method:"get"})},getDailyTaskStats:function(e=null){const a={};return e&&(a.date=e),t({url:"/v2/gamification/daily-stats",method:"get",params:a})},getLeaderboard:function(e=1,a=10){return t({url:"/v2/gamification/leaderboard",method:"get",params:{type:e,topN:a}})},getMultiDimensionLeaderboard:d,getLeaderboardMetrics:function(){return t({url:"/v2/leaderboard/metrics",method:"get"})},getLeaderboardPeriods:function(){return t({url:"/v2/leaderboard/periods",method:"get"})},getLeaderboardOverview:function(e="weekly",a=10){return t({url:"/v2/leaderboard/overview",method:"get",params:{period:e,limit:a}})},getMyRank:function(e=1){return t({url:"/v2/gamification/my-rank",method:"get",params:{type:e}})},initializeUserStats:function(){return t({url:"/v2/gamification/initialize",method:"post"})},triggerClaimReward:function(e){return t({url:"/v2/gamification-v2/trigger-claim-reward",method:"post",data:{taskId:e}})},triggerCompleteReward:function(e,a=!1){return t({url:"/v2/gamification-v2/trigger-complete-reward",method:"post",data:{taskId:e,isOnTime:a}})},claimTask:function(e,a=""){return t({url:`/v2/tasks/${e}/claim`,method:"post",data:{notes:a}})},getUserTodayClaims:function(){return t({url:"/v2/tasks/claims/today",method:"get"})},updateClaimStatus:function(e,a,r=""){return t({url:`/v2/tasks/claims/${e}/status`,method:"patch",data:{claimStatus:a,notes:r}})},getTodayShiftStatistics:function(e={}){return t({url:"/v2/tasks/claims/statistics",method:"get",params:e})},getWeeklyStats:function(e=0){return t({url:"/v2/gamification/weekly-stats",method:"get",params:{weekOffset:e}})},getUserWeeklyStats:function(e=null,a=0){const r={weekOffset:a};return e&&(r.userId=e),t({url:"/v2/gamification/weekly-stats/user",method:"get",params:r})},LeaderboardType:{WEEKLY:1,MONTHLY:2,ALL_TIME:3},LeaderboardMetrics:g,StatisticsPeriods:p,TaskClaimStatus:{CLAIMED:"Claimed",STARTED:"Started",COMPLETED:"Completed",CANCELLED:"Cancelled"},GamificationEventType:{TASK_COMPLETED:"TaskCompleted",TASK_CLAIMED:"TaskClaimed",TASK_CREATED:"TaskCreated",TASK_UPDATED:"TaskUpdated",BADGE_EARNED:"BadgeEarned",LEVEL_UP:"LevelUp",POINTS_SPENT:"PointsSpent",DAILY_LOGIN:"DailyLogin",COMMENT_ADDED:"CommentAdded",STREAK_MAINTAINED:"StreakMaintained",ON_TIME_COMPLETION:"OnTimeCompletion"}};export{g as L,p as S,d as a,a as b,e as c,l as d,u as e,s as f,f as g,i as h,r as i,o as r,n as s,m as t,c as u};
