// IT资产管理系统 - 恢复备份API
// 文件路径: /Api/Backup/RestoreBackupEndpoint.cs
// 功能: 从备份恢复数据的API端点

using System.Threading.Tasks;
using ItAssetsSystem.Core.Backup;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace ItAssetsSystem.Api.Backup
{
    [ApiController]
    [Route("api/backup/restore")]
    public class RestoreBackupEndpoint : ControllerBase
    {
        private readonly IBackupService _backupService;
        private readonly ILogger<RestoreBackupEndpoint> _logger;

        public RestoreBackupEndpoint(
            IBackupService backupService,
            ILogger<RestoreBackupEndpoint> logger)
        {
            _backupService = backupService;
            _logger = logger;
        }

        public class RestoreBackupRequest
        {
            public string BackupId { get; set; }
        }

        public class RestoreBackupResponse
        {
            public bool Success { get; set; }
            public string Message { get; set; }
        }

        [HttpPost]
        public async Task<ActionResult<RestoreBackupResponse>> HandleAsync([FromBody] RestoreBackupRequest request)
        {
            _logger.LogInformation("从备份恢复数据: {BackupId}", request.BackupId);
            
            try
            {
                bool success = await _backupService.RestoreFromBackupAsync(request.BackupId);
                
                if (success)
                {
                    return Ok(new RestoreBackupResponse
                    {
                        Success = true,
                        Message = "数据恢复成功"
                    });
                }
                else
                {
                    return BadRequest(new RestoreBackupResponse
                    {
                        Success = false,
                        Message = "数据恢复失败，可能是备份不存在或已损坏"
                    });
                }
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "从备份恢复数据失败");
                
                return StatusCode(500, new RestoreBackupResponse
                {
                    Success = false,
                    Message = $"数据恢复失败: {ex.Message}"
                });
            }
        }
    }
}

// 计划行数: 25
// 实际行数: 25 