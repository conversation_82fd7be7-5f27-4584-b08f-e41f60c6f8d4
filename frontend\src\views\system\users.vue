/**
 * 航空航天级IT资产管理系统 - 用户管理页面
 * 文件路径: src/views/system/users.vue
 * 功能描述: 系统用户的增删改查管理功能
 */

<template>
  <div class="user-management-container">
    <!-- 页面标题和操作按钮 -->
    <div class="page-header">
      <h2 class="page-title">用户管理</h2>
      <div class="page-actions">
        <el-button type="primary" @click="handleCreateUser" :icon="Plus">
          新建用户
        </el-button>
        <el-button type="primary" @click="handleExportData" :icon="Download">
          导出数据
        </el-button>
      </div>
    </div>

    <!-- 筛选条件 -->
    <el-card class="filter-card">
      <div class="filter-container">
        <el-form :inline="true" :model="filterForm" class="filter-form">
          <el-form-item label="用户名">
            <el-input v-model="filterForm.username" placeholder="用户名" clearable />
          </el-form-item>
          <el-form-item label="姓名">
            <el-input v-model="filterForm.name" placeholder="姓名" clearable />
          </el-form-item>
          <el-form-item label="部门">
            <el-select 
              v-model="filterForm.department" 
              placeholder="全部部门" 
              clearable
            >
              <el-option 
                v-for="item in departmentOptions" 
                :key="item.value" 
                :label="item.label" 
                :value="item.value" 
              />
            </el-select>
          </el-form-item>
          <el-form-item label="角色">
            <el-select 
              v-model="filterForm.role" 
              placeholder="全部角色" 
              clearable
            >
              <el-option 
                v-for="item in roleOptions" 
                :key="item.value" 
                :label="item.label" 
                :value="item.value" 
              />
            </el-select>
          </el-form-item>
          <el-form-item label="状态">
            <el-select 
              v-model="filterForm.status" 
              placeholder="全部状态" 
              clearable
            >
              <el-option 
                v-for="item in statusOptions" 
                :key="item.value" 
                :label="item.label" 
                :value="item.value" 
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch" :icon="Search">
              搜索
            </el-button>
            <el-button @click="resetFilter" :icon="RefreshRight">
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="data-card">
      <el-table
        ref="userTable"
        v-loading="loading"
        :data="userList"
        border
        style="width: 100%"
      >
        <el-table-column prop="username" label="用户名" width="120" sortable />
        <el-table-column prop="name" label="姓名" width="120" />
        <el-table-column prop="department" label="部门" width="150" />
        <el-table-column prop="role" label="角色" width="150">
          <template #default="scope">
            <el-tag :type="getRoleTagType(scope.row.role)" size="small">
              {{ getRoleLabel(scope.row.role) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="email" label="邮箱" min-width="180" show-overflow-tooltip />
        <el-table-column prop="phone" label="手机号" width="130" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusTag(scope.row.status)" size="small">
              {{ getStatusLabel(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="170" sortable />
        <el-table-column prop="lastLoginTime" label="最后登录" width="170" sortable />
        <el-table-column label="操作" width="230" fixed="right">
          <template #default="scope">
            <el-button 
              type="text" 
              size="small" 
              @click="handleViewDetail(scope.row)"
              :icon="View"
            >
              详情
            </el-button>
            <el-button 
              type="text" 
              size="small" 
              @click="handleEdit(scope.row)"
              :icon="Edit"
            >
              编辑
            </el-button>
            <el-button 
              v-if="scope.row.status === 'active'"
              type="text" 
              size="small" 
              @click="handleDisable(scope.row)"
              :icon="Lock"
            >
              禁用
            </el-button>
            <el-button 
              v-if="scope.row.status === 'disabled'"
              type="text" 
              size="small" 
              @click="handleEnable(scope.row)"
              :icon="Unlock"
            >
              启用
            </el-button>
            <el-button 
              type="text" 
              size="small" 
              @click="handleDelete(scope.row)"
              :icon="Delete"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :background="true"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Search, Plus, Download, View, Edit, Delete,
  RefreshRight, Lock, Unlock
} from '@element-plus/icons-vue'

// 数据加载状态
const loading = ref(false)

// 用户列表数据
const userList = ref([])
const userTable = ref(null)

// 分页配置
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 筛选表单
const filterForm = reactive({
  username: '',
  name: '',
  department: '',
  role: '',
  status: ''
})

// 角色选项
const roleOptions = [
  { label: '系统管理员', value: 'admin' },
  { label: '资产管理员', value: 'asset_manager' },
  { label: '采购员', value: 'purchaser' },
  { label: '维护人员', value: 'maintenance' },
  { label: '普通用户', value: 'user' }
]

// 部门选项
const departmentOptions = [
  { label: 'IT部门', value: 'IT' },
  { label: '人力资源部', value: 'HR' },
  { label: '财务部', value: 'Finance' },
  { label: '研发部', value: 'R&D' },
  { label: '运营部', value: 'Operations' }
]

// 状态选项
const statusOptions = [
  { label: '活跃', value: 'active' },
  { label: '已禁用', value: 'disabled' }
]

// 生命周期钩子
onMounted(() => {
  fetchUserList()
})

// 获取用户列表
const fetchUserList = () => {
  loading.value = true
  
  // 构建查询参数
  const params = {
    page: pagination.currentPage,
    pageSize: pagination.pageSize,
    username: filterForm.username,
    name: filterForm.name,
    department: filterForm.department,
    role: filterForm.role,
    status: filterForm.status
  }
  
  // 模拟API调用
  // 实际项目中应该调用API: const res = await getUserList(params)
  setTimeout(() => {
    // 模拟数据
    const mockData = []
    const total = 56
    
    for (let i = 0; i < Math.min(pagination.pageSize, total - (pagination.currentPage - 1) * pagination.pageSize); i++) {
      const index = (pagination.currentPage - 1) * pagination.pageSize + i
      
      // 随机状态
      const status = ['active', 'disabled'][Math.floor(Math.random() * 2)]
      
      // 随机角色
      const role = ['admin', 'asset_manager', 'purchaser', 'maintenance', 'user'][Math.floor(Math.random() * 5)]
      
      // 随机部门
      const department = ['IT', 'HR', 'Finance', 'R&D', 'Operations'][Math.floor(Math.random() * 5)]
      
      // 随机创建时间
      const createDate = new Date()
      createDate.setMonth(createDate.getMonth() - Math.floor(Math.random() * 24))
      const createTime = formatDate(createDate)
      
      // 随机最后登录时间
      const lastLoginDate = new Date()
      lastLoginDate.setDate(lastLoginDate.getDate() - Math.floor(Math.random() * 30))
      const lastLoginTime = formatDate(lastLoginDate)
      
      mockData.push({
        id: 1000 + index,
        username: `user${1000 + index}`,
        name: [
          '张三', '李四', '王五', '赵六', '陈七', '钱八', '孙九', '周十',
          '吴一', '郑二', '冯三', '蒋四', '沈五', '韩六', '杨七', '朱八'
        ][Math.floor(Math.random() * 16)],
        department: department,
        role: role,
        email: `user${1000 + index}@example.com`,
        phone: `1${Math.floor(Math.random() * 9) + 1}${Array(9).fill(0).map(() => Math.floor(Math.random() * 10)).join('')}`,
        status: status,
        createTime: createTime,
        lastLoginTime: lastLoginTime
      })
    }
    
    userList.value = mockData
    pagination.total = total
    loading.value = false
  }, 500)
}

// 格式化日期函数
const formatDate = (date) => {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hour = String(date.getHours()).padStart(2, '0')
  const minute = String(date.getMinutes()).padStart(2, '0')
  const second = String(date.getSeconds()).padStart(2, '0')
  
  return `${year}-${month}-${day} ${hour}:${minute}:${second}`
}

// 搜索
const handleSearch = () => {
  pagination.currentPage = 1
  fetchUserList()
}

// 重置筛选条件
const resetFilter = () => {
  filterForm.username = ''
  filterForm.name = ''
  filterForm.department = ''
  filterForm.role = ''
  filterForm.status = ''
  
  pagination.currentPage = 1
  fetchUserList()
}

// 分页事件
const handleSizeChange = (size) => {
  pagination.pageSize = size
  fetchUserList()
}

const handleCurrentChange = (page) => {
  pagination.currentPage = page
  fetchUserList()
}

// 查看详情
const handleViewDetail = (row) => {
  ElMessage.info(`查看用户详情：${row.username}`)
  // 实际项目中可以打开对话框或者跳转到详情页
}

// 编辑用户
const handleEdit = (row) => {
  ElMessage.info(`编辑用户：${row.username}`)
  // 实际项目中可以打开编辑对话框
}

// 禁用用户
const handleDisable = (row) => {
  ElMessageBox.confirm(`确定要禁用用户"${row.username}"吗？禁用后该用户将无法登录系统。`, '禁用用户', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // 模拟API调用
    // 实际项目中应该调用API: await disableUser(row.id)
    
    ElMessage.success('用户已禁用')
    
    // 刷新列表
    fetchUserList()
  }).catch(() => {
    // 取消操作
  })
}

// 启用用户
const handleEnable = (row) => {
  ElMessageBox.confirm(`确定要启用用户"${row.username}"吗？`, '启用用户', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // 模拟API调用
    // 实际项目中应该调用API: await enableUser(row.id)
    
    ElMessage.success('用户已启用')
    
    // 刷新列表
    fetchUserList()
  }).catch(() => {
    // 取消操作
  })
}

// 删除用户
const handleDelete = (row) => {
  ElMessageBox.confirm(`确定要删除用户"${row.username}"吗？此操作不可恢复！`, '删除用户', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'error'
  }).then(() => {
    // 模拟API调用
    // 实际项目中应该调用API: await deleteUser(row.id)
    
    ElMessage.success('用户已删除')
    
    // 刷新列表
    fetchUserList()
  }).catch(() => {
    // 取消操作
  })
}

// 创建用户
const handleCreateUser = () => {
  ElMessage.info('打开新建用户对话框')
  // 实际项目中可以打开对话框创建用户
}

// 导出数据
const handleExportData = () => {
  ElMessage.success('开始导出数据，请稍候...')
  // 实际项目中调用导出API
  // exportUserList(filterForm).then(() => {
  //   ElMessage.success('导出成功')
  // })
}

// 工具方法：获取角色标签样式
const getRoleTagType = (role) => {
  const map = {
    'admin': 'danger',
    'asset_manager': 'warning',
    'purchaser': 'success',
    'maintenance': 'primary',
    'user': 'info'
  }
  return map[role] || ''
}

// 工具方法：获取角色文本
const getRoleLabel = (role) => {
  const map = {
    'admin': '系统管理员',
    'asset_manager': '资产管理员',
    'purchaser': '采购员',
    'maintenance': '维护人员',
    'user': '普通用户'
  }
  return map[role] || '未知'
}

// 工具方法：获取状态标签样式
const getStatusTag = (status) => {
  const map = {
    'active': 'success',
    'disabled': 'info'
  }
  return map[status] || ''
}

// 工具方法：获取状态文本
const getStatusLabel = (status) => {
  const map = {
    'active': '活跃',
    'disabled': '已禁用'
  }
  return map[status] || '未知'
}
</script>

<style lang="scss" scoped>
.user-management-container {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    
    .page-title {
      font-size: 20px;
      font-weight: 600;
      color: var(--el-color-primary);
      margin: 0;
    }
    
    .page-actions {
      display: flex;
      gap: 8px;
    }
  }
  
  .filter-card {
    margin-bottom: 16px;
    
    .filter-container {
      .filter-form {
        display: flex;
        flex-wrap: wrap;
      }
    }
  }
  
  .data-card {
    .pagination-container {
      margin-top: 16px;
      display: flex;
      justify-content: flex-end;
    }
  }
}
</style> 