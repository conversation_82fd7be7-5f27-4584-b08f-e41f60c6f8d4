using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using ItAssetsSystem.Core.Resilience;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace ItAssetsSystem.Controllers
{
    /// <summary>
    /// 测试离线操作的API控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class OfflineOperationController : ControllerBase
    {
        private readonly ILogger<OfflineOperationController> _logger;
        private readonly IOfflineQueue _offlineQueue;
        private readonly INetworkMonitor _networkMonitor;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志记录器</param>
        /// <param name="offlineQueue">离线队列</param>
        /// <param name="networkMonitor">网络监控</param>
        public OfflineOperationController(
            ILogger<OfflineOperationController> logger,
            IOfflineQueue offlineQueue,
            INetworkMonitor networkMonitor)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _offlineQueue = offlineQueue ?? throw new ArgumentNullException(nameof(offlineQueue));
            _networkMonitor = networkMonitor ?? throw new ArgumentNullException(nameof(networkMonitor));
        }

        /// <summary>
        /// 获取当前网络状态
        /// </summary>
        /// <returns>网络状态</returns>
        [HttpGet("network-status")]
        public ActionResult<NetworkStatus> GetNetworkStatus()
        {
            return _networkMonitor.CurrentStatus;
        }

        /// <summary>
        /// 获取离线队列状态
        /// </summary>
        /// <returns>队列状态</returns>
        [HttpGet("queue-status")]
        public ActionResult<dynamic> GetQueueStatus()
        {
            return new
            {
                IsEnabled = _offlineQueue.IsEnabled,
                PendingCount = _offlineQueue.PendingCount
            };
        }

        /// <summary>
        /// 添加一个模拟操作到离线队列
        /// </summary>
        /// <param name="data">操作数据</param>
        /// <returns>操作ID</returns>
        [HttpPost("enqueue")]
        public async Task<ActionResult<string>> EnqueueOperation([FromBody] EnqueueOperationRequest data)
        {
            try
            {
                // 创建一个模拟操作
                var operation = new OfflineOperation(
                    data.OperationType ?? "SimulatedOperation",
                    data.OperationData ?? "{}",
                    async (cancellationToken) =>
                    {
                        _logger.LogInformation("执行模拟操作：{0}，数据：{1}", data.OperationType, data.OperationData);
                        
                        // 模拟操作执行时间
                        await Task.Delay(data.DelayMs > 0 ? data.DelayMs : 1000, cancellationToken);
                        
                        // 根据设置的成功率返回成功或失败
                        var random = new Random();
                        bool success = random.NextDouble() < (data.SuccessRate / 100.0);
                        
                        if (!success)
                        {
                            _logger.LogWarning("模拟操作执行失败");
                        }
                        
                        return success;
                    },
                    data.Priority,
                    data.TimeoutMs
                );
                
                // 添加到队列
                string operationId = await _offlineQueue.EnqueueAsync(operation);
                
                return Ok(new { OperationId = operationId });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "添加操作到队列时发生错误");
                return StatusCode(500, new { Error = ex.Message });
            }
        }

        /// <summary>
        /// 获取待处理操作列表
        /// </summary>
        /// <returns>待处理操作列表</returns>
        [HttpGet("pending")]
        public async Task<ActionResult<IEnumerable<dynamic>>> GetPendingOperations()
        {
            var operations = await _offlineQueue.GetPendingOperationsAsync();
            var result = new List<dynamic>();
            
            foreach (var op in operations)
            {
                result.Add(new
                {
                    Id = op.Id,
                    Type = op.OperationType,
                    Data = op.OperationData,
                    Status = op.Status.ToString(),
                    CreatedAt = op.CreatedAt,
                    UpdatedAt = op.UpdatedAt,
                    RetryCount = op.RetryCount,
                    Priority = op.Priority
                });
            }
            
            return Ok(result);
        }

        /// <summary>
        /// 获取失败操作列表
        /// </summary>
        /// <returns>失败操作列表</returns>
        [HttpGet("failed")]
        public async Task<ActionResult<IEnumerable<dynamic>>> GetFailedOperations()
        {
            var operations = await _offlineQueue.GetFailedOperationsAsync();
            var result = new List<dynamic>();
            
            foreach (var op in operations)
            {
                result.Add(new
                {
                    Id = op.Id,
                    Type = op.OperationType,
                    Data = op.OperationData,
                    Status = op.Status.ToString(),
                    CreatedAt = op.CreatedAt,
                    UpdatedAt = op.UpdatedAt,
                    ErrorMessage = op.ErrorMessage,
                    RetryCount = op.RetryCount,
                    Priority = op.Priority
                });
            }
            
            return Ok(result);
        }

        /// <summary>
        /// 取消操作
        /// </summary>
        /// <param name="id">操作ID</param>
        /// <returns>操作结果</returns>
        [HttpPost("{id}/cancel")]
        public async Task<ActionResult> CancelOperation(string id)
        {
            bool result = await _offlineQueue.CancelOperationAsync(id);
            
            if (result)
            {
                return Ok(new { Success = true, Message = "操作已取消" });
            }
            else
            {
                return BadRequest(new { Success = false, Message = "取消操作失败" });
            }
        }

        /// <summary>
        /// 执行操作
        /// </summary>
        /// <param name="id">操作ID</param>
        /// <returns>操作结果</returns>
        [HttpPost("{id}/execute")]
        public async Task<ActionResult> ExecuteOperation(string id)
        {
            bool result = await _offlineQueue.ExecuteOperationAsync(id);
            
            if (result)
            {
                return Ok(new { Success = true, Message = "操作执行成功" });
            }
            else
            {
                return BadRequest(new { Success = false, Message = "操作执行失败" });
            }
        }

        /// <summary>
        /// 重试失败操作
        /// </summary>
        /// <param name="id">操作ID，如果为null则重试所有失败操作</param>
        /// <returns>操作结果</returns>
        [HttpPost("retry")]
        public async Task<ActionResult> RetryFailedOperations(string id = null)
        {
            int count = await _offlineQueue.RetryFailedOperationsAsync(id);
            
            return Ok(new { Success = true, Count = count, Message = $"已重试{count}个操作" });
        }

        /// <summary>
        /// 清空队列
        /// </summary>
        /// <returns>操作结果</returns>
        [HttpPost("clear")]
        public async Task<ActionResult> ClearQueue()
        {
            bool result = await _offlineQueue.ClearQueueAsync();
            
            if (result)
            {
                return Ok(new { Success = true, Message = "队列已清空" });
            }
            else
            {
                return BadRequest(new { Success = false, Message = "清空队列失败" });
            }
        }
    }

    /// <summary>
    /// 入队操作请求
    /// </summary>
    public class EnqueueOperationRequest
    {
        /// <summary>
        /// 操作类型
        /// </summary>
        public string OperationType { get; set; }
        
        /// <summary>
        /// 操作数据
        /// </summary>
        public string OperationData { get; set; }
        
        /// <summary>
        /// 优先级
        /// </summary>
        public int Priority { get; set; } = 0;
        
        /// <summary>
        /// 超时（毫秒）
        /// </summary>
        public int TimeoutMs { get; set; } = 30000;
        
        /// <summary>
        /// 延迟时间（毫秒）
        /// </summary>
        public int DelayMs { get; set; } = 1000;
        
        /// <summary>
        /// 成功率（百分比）
        /// </summary>
        public int SuccessRate { get; set; } = 80;
    }
} 