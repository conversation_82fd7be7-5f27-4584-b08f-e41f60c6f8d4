import{aa as e,ab as s,ac as a,_ as l,c as r,x as o,r as n,ad as u,b as t,d as i,e as d,w as c,t as m,f as p,C as g,a as f,u as v,B as w,o as b,ae as y,af as _,p as h,E as k}from"./index-CG5lHOPO.js";e().use((({store:e})=>{e.router=s(a)}));const V={class:"login-container"},x={class:"login-banner"},U={class:"banner-content"},q={class:"login-logo"},C={class:"system-name"},F={class:"system-desc"},I={class:"login-form-container"},K={class:"login-form-wrapper"},L={class:"login-footer"},j={class:"copyright"},z=l({__name:"Login",setup(e){const s=r((()=>(new Date).getFullYear())),a=v(),l=w(),z=o(),A=n(null),B=n(!1),D=u({username:"",password:"",remember:!1}),E={username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:3,max:20,message:"用户名长度为3-20个字符",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,max:30,message:"密码长度为6-30个字符",trigger:"blur"}]},M=async()=>{var e,s;if(A.value)try{await A.value.validate((e=>{if(!e)return k.warning("请正确填写用户名和密码"),!1})),B.value=!0;try{const e={username:D.username,password:D.password},s=await z.login(e);s&&s.success?(k.success("登录成功，欢迎回来！"),R()):k.error((null==s?void 0:s.message)||"登录失败，请检查用户名和密码")}catch(a){if((null==(e=null==a?void 0:a.message)?void 0:e.includes("404"))||(null==(s=null==a?void 0:a.message)?void 0:s.includes("network error"))){k.warning("后端API不可用，尝试模拟登录..."),g.useMock=!0;try{const e=await z.login({username:D.username,password:D.password});if(e&&e.success)return k.success("模拟登录成功，欢迎回来！"),void R();k.error((null==e?void 0:e.message)||"模拟登录失败")}catch(l){k.error("登录失败: "+((null==l?void 0:l.message)||"未知错误"))}}else k.error("登录失败: "+((null==a?void 0:a.message)||"未知错误"))}}catch(r){k.error("登录失败: "+((null==r?void 0:r.message)||"未知错误"))}finally{B.value=!1}},P=()=>{a.push("/test")},R=()=>{const e=l.query.redirect||"/main/dashboard";setTimeout((()=>{z.token?a.push(e).catch((()=>{a.push("/")})):k.error("登录状态异常，请重新登录")}),100)};return(e,a)=>{const l=f("el-icon"),r=f("el-input"),o=f("el-form-item"),n=f("el-checkbox"),u=f("el-link"),v=f("el-button"),w=f("el-form");return b(),t("div",V,[i("div",x,[i("div",U,[i("div",q,[d(l,{size:80,color:"#ffffff"},{default:c((()=>[d(p(y))])),_:1})]),i("h1",C,m(p(g).name),1),i("p",F,m(p(g).description),1)])]),i("div",I,[i("div",K,[a[7]||(a[7]=i("h2",{class:"login-title"},"系统登录",-1)),d(w,{ref_key:"loginFormRef",ref:A,model:D,rules:E,class:"login-form"},{default:c((()=>[d(o,{prop:"username"},{default:c((()=>[d(r,{modelValue:D.username,"onUpdate:modelValue":a[0]||(a[0]=e=>D.username=e),placeholder:"请输入用户名","prefix-icon":"User",clearable:"",onKeyup:_(M,["enter"])},null,8,["modelValue"])])),_:1}),d(o,{prop:"password"},{default:c((()=>[d(r,{modelValue:D.password,"onUpdate:modelValue":a[1]||(a[1]=e=>D.password=e),type:"password",placeholder:"请输入密码","prefix-icon":"Lock","show-password":"",clearable:"",onKeyup:_(M,["enter"])},null,8,["modelValue"])])),_:1}),d(o,null,{default:c((()=>[d(n,{modelValue:D.remember,"onUpdate:modelValue":a[2]||(a[2]=e=>D.remember=e)},{default:c((()=>a[3]||(a[3]=[h("记住我")]))),_:1},8,["modelValue"]),d(u,{type:"primary",class:"forget-password",underline:!1},{default:c((()=>a[4]||(a[4]=[h("忘记密码？")]))),_:1})])),_:1}),d(o,null,{default:c((()=>[d(v,{type:"primary",class:"login-button",loading:B.value,onClick:M},{default:c((()=>a[5]||(a[5]=[h(" 登录 ")]))),_:1},8,["loading"])])),_:1}),d(o,null,{default:c((()=>[d(v,{type:"success",class:"test-button",onClick:P},{default:c((()=>a[6]||(a[6]=[h(" 测试页面跳转 ")]))),_:1})])),_:1})])),_:1},8,["model"]),i("div",L,[i("p",j,"© "+m(s.value)+" "+m(p(g).name),1)])])])])}}},[["__scopeId","data-v-4fd8c931"]]);export{z as default};
