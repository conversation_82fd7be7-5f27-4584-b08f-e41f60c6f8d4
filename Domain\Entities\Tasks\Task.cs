// File: Domain/Entities/Tasks/Task.cs
// Description: 任务核心实体
#nullable enable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using ItAssetsSystem.Models.Entities; // Corrected: Assuming User, Asset, Location are here
//using ItAssetsSystem.Domain.Interfaces; // For IAuditableEntity if used - Removed for now

namespace ItAssetsSystem.Domain.Entities.Tasks
{
    /// <summary>
    /// 任务主表 (V2 - BIGINT PK)
    /// </summary>
    public class Task
    {
        /// <summary>
        /// 任务主键ID (BIGINT)
        /// </summary>
        [Key]
        public long TaskId { get; set; }

        /// <summary>
        /// 所属项目ID (如果需要，也应为 BIGINT)
        /// </summary>
        public long? ProjectId { get; set; }

        /// <summary>
        /// 父任务ID (BIGINT, 指向自身 TaskId)
        /// </summary>
        public long? ParentTaskId { get; set; }
        [ForeignKey("ParentTaskId")]
        public virtual Task? ParentTask { get; set; }
        public virtual ICollection<Task> SubTasks { get; set; } = new List<Task>();

        /// <summary>
        /// 创建者用户ID (INT, 关联 users.Id)
        /// </summary>
        public int CreatorUserId { get; set; }
        [ForeignKey("CreatorUserId")]
        public virtual User? Creator { get; set; }

        /// <summary>
        /// 负责人ID (INT, 关联 users.Id)
        /// </summary>
        public int? AssigneeUserId { get; set; }
        [ForeignKey("AssigneeUserId")]
        public virtual User? Assignee { get; set; }
        
        /// <summary>
        /// 关联资产ID (INT, 关联 assets.Id)
        /// </summary>
        public int? AssetId { get; set; }
        
        /// <summary>
        /// 导航属性：关联资产
        /// </summary>
        [ForeignKey("AssetId")]
        public virtual Asset? Asset { get; set; }

        /// <summary>
        /// 关联位置ID (INT, 关联 locations.Id)
        /// </summary>
        public int? LocationId { get; set; }
        
        /// <summary>
        /// 导航属性：关联位置
        /// </summary>
        [ForeignKey("LocationId")]
        public virtual Location? Location { get; set; }

        /// <summary>
        /// 任务名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 任务描述 (支持Markdown或富文本)
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// 任务状态 (使用字符串: Todo, InProgress, Done, Canceled, Review等)
        /// </summary>
        public string Status { get; set; } = "Todo";
        
        /// <summary>
        /// 优先级 (使用字符串: Low, Medium, High, Urgent等)
        /// </summary>
        public string? Priority { get; set; } = "Medium";

        /// <summary>
        /// 任务类型 (Normal-普通, Periodic-周期, PDCA)
        /// </summary>
        public string TaskType { get; set; } = "Normal";

        /// <summary>
        /// 任务分类ID (关联 task_categories.category_id)
        /// </summary>
        public int? CategoryId { get; set; }
        [ForeignKey("CategoryId")]
        public virtual TaskCategory? Category { get; set; }
        
        /// <summary>
        /// 计划开始时间
        /// </summary>
        public DateTime? PlanStartDate { get; set; }

        /// <summary>
        /// 计划结束时间 (截止时间)
        /// </summary>
        public DateTime? PlanEndDate { get; set; }

        /// <summary>
        /// 实际开始时间
        /// </summary>
        public DateTime? ActualStartDate { get; set; }

        /// <summary>
        /// 实际完成时间
        /// </summary>
        public DateTime? ActualEndDate { get; set; }

        /// <summary>
        /// 任务创建时间
        /// </summary>
        public DateTime CreationTimestamp { get; set; }
        
        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime LastUpdatedTimestamp { get; set; }

        /// <summary>
        /// 逾期是否已知晓或已处理 (0:否, 1:是)
        /// </summary>
        public bool IsOverdueAcknowledged { get; set; }

        /// <summary>
        /// PDCA任务所处阶段 (Plan, Do, Check, Act)
        /// </summary>
        public string? PDCAStage { get; set; }

        /// <summary>
        /// 周期性任务的前一个实例ID (BIGINT, 指向自身 TaskId)
        /// </summary>
        public long? PreviousInstanceTaskId { get; set; }
        [System.ComponentModel.DataAnnotations.Schema.NotMapped]
        public virtual Task? PreviousInstanceTask { get; set; }

        /// <summary>
        /// 完成用户ID (用于水印显示)
        /// </summary>
        public int? CompletedByUserId { get; set; }

        /// <summary>
        /// 任务完成时间 (实际完成时间的补充字段)
        /// </summary>
        public DateTime? CompletedAt { get; set; }

        /// <summary>
        /// 完成用户水印颜色 (十六进制颜色代码)
        /// </summary>
        public string? CompletionWatermarkColor { get; set; }

        /// <summary>
        /// 关联的周期性任务计划ID (BIGINT)
        /// </summary>
        public long? PeriodicTaskScheduleId { get; set; }
        
        /// <summary>
        /// 导航属性：关联的周期性任务计划
        /// </summary>
        [ForeignKey("PeriodicTaskScheduleId")]
        [NotMapped] // 避免循环依赖
        public virtual PeriodicTaskSchedule? PeriodicTaskSchedule { get; set; }

        /// <summary>
        /// 任务进度百分比 (0-100)
        /// </summary>
        public int Progress { get; set; }

        /// <summary>
        /// 完成任务可获得的基础积分/经验值
        /// </summary>
        public int Points { get; set; }

        /// <summary>
        /// 是否已删除（软删除标记）
        /// </summary>
        public bool IsDeleted { get; set; } = false;

        // --- Navigation Properties ---

        /// <summary>
        /// 任务的评论列表
        /// </summary>
        public virtual ICollection<Comment> Comments { get; set; } = new List<Comment>();

        /// <summary>
        /// 任务的附件列表
        /// </summary>
        public virtual ICollection<Attachment> Attachments { get; set; } = new List<Attachment>();

        /// <summary>
        /// 任务的历史记录列表
        /// </summary>
        public virtual ICollection<TaskHistory> History { get; set; } = new List<TaskHistory>();

        /// <summary>
        /// 任务的负责人/参与者列表
        /// </summary>
        public virtual ICollection<TaskAssignee> Assignees { get; set; } = new List<TaskAssignee>();

        /// <summary>
        /// 如果此任务是模板任务，关联的周期计划
        /// </summary>
        [System.ComponentModel.DataAnnotations.Schema.NotMapped]
        public virtual ICollection<PeriodicTaskSchedule> TemplateForSchedules { get; set; } = new List<PeriodicTaskSchedule>();

        /// <summary>
        /// 是否逾期 (计算属性)
        /// </summary>
        [NotMapped]
        public bool IsOverdue
        {
            get
            {
                return PlanEndDate.HasValue &&
                       PlanEndDate.Value < DateTime.Now &&
                       !("Done".Equals(Status, StringComparison.OrdinalIgnoreCase) ||
                         "Completed".Equals(Status, StringComparison.OrdinalIgnoreCase) ||
                         "Cancelled".Equals(Status, StringComparison.OrdinalIgnoreCase));
            }
        }
    }
} 