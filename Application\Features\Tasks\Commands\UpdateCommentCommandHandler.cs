// File: Application/Features/Tasks/Commands/UpdateCommentCommandHandler.cs
// Description: 更新评论命令处理器

using System;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using MediatR;
using Microsoft.Extensions.Logging;
using ItAssetsSystem.Application.Common.Dtos;
using ItAssetsSystem.Application.Features.Tasks.Dtos;
using ItAssetsSystem.Core.Abstractions;
using ItAssetsSystem.Domain.Entities.Tasks;

namespace ItAssetsSystem.Application.Features.Tasks.Commands
{
    /// <summary>
    /// 更新评论命令处理器
    /// </summary>
    public class UpdateCommentCommandHandler : IRequestHandler<UpdateCommentCommand, ApiResponse<CommentDto>>
    {
        private readonly ITaskRepository _taskRepository;
        private readonly ICoreDataQueryService _coreDataQueryService;
        private readonly ILogger<UpdateCommentCommandHandler> _logger;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="taskRepository">任务仓储</param>
        /// <param name="coreDataQueryService">核心数据查询服务</param>
        /// <param name="logger">日志记录器</param>
        public UpdateCommentCommandHandler(
            ITaskRepository taskRepository,
            ICoreDataQueryService coreDataQueryService,
            ILogger<UpdateCommentCommandHandler> logger)
        {
            _taskRepository = taskRepository;
            _coreDataQueryService = coreDataQueryService;
            _logger = logger;
        }

        /// <summary>
        /// 处理更新评论命令
        /// </summary>
        /// <param name="request">更新评论命令</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>更新后的评论DTO</returns>
        public async Task<ApiResponse<CommentDto>> Handle(UpdateCommentCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("开始处理更新评论命令: CommentId={CommentId}, TaskId={TaskId}, UserId={UserId}", 
                    request.CommentId, request.TaskId, request.CurrentUserId);

                // 验证评论是否存在
                var existingComment = await _taskRepository.GetCommentByIdAsync(request.CommentId);
                if (existingComment == null)
                {
                    return ApiResponse<CommentDto>.CreateFail($"评论不存在: CommentId={request.CommentId}");
                }

                // 验证评论是否属于指定任务
                if (existingComment.TaskId != request.TaskId)
                {
                    return ApiResponse<CommentDto>.CreateFail("评论不属于指定任务");
                }

                // 验证权限：只有评论作者可以编辑评论内容，管理员可以置顶/取消置顶
                if (existingComment.UserId != request.CurrentUserId && request.IsPinned == null)
                {
                    return ApiResponse<CommentDto>.CreateFail("只有评论作者可以编辑评论内容");
                }

                // 验证任务是否存在
                if (!await _taskRepository.TaskExistsAsync(request.TaskId))
                {
                    return ApiResponse<CommentDto>.CreateFail($"任务不存在: TaskId={request.TaskId}");
                }

                // 准备更新的评论对象
                var updatedComment = new Comment
                {
                    CommentId = existingComment.CommentId,
                    TaskId = existingComment.TaskId,
                    UserId = existingComment.UserId,
                    ParentCommentId = existingComment.ParentCommentId,
                    Content = !string.IsNullOrWhiteSpace(request.Content) ? request.Content.Trim() : existingComment.Content,
                    IsPinned = request.IsPinned ?? existingComment.IsPinned,
                    IsEdited = !string.IsNullOrWhiteSpace(request.Content) && request.Content.Trim() != existingComment.Content,
                    CreationTimestamp = existingComment.CreationTimestamp,
                    LastUpdatedTimestamp = DateTime.Now
                };

                // 处理提及用户
                if (request.MentionedUserIds != null)
                {
                    updatedComment.MentionedUserIds = request.MentionedUserIds.Count > 0 
                        ? JsonSerializer.Serialize(request.MentionedUserIds) 
                        : null;
                }
                else
                {
                    updatedComment.MentionedUserIds = existingComment.MentionedUserIds;
                }

                // 执行更新
                var updateSuccess = await _taskRepository.UpdateCommentAsync(updatedComment);
                if (!updateSuccess)
                {
                    return ApiResponse<CommentDto>.CreateFail("更新评论失败");
                }

                // 记录任务历史
                await _taskRepository.AddTaskHistoryAsync(new TaskHistory
                {
                    TaskId = request.TaskId,
                    UserId = request.CurrentUserId,
                    Timestamp = DateTime.Now,
                    ActionType = "UpdateComment",
                    Description = $"更新了评论: {updatedComment.Content.Substring(0, Math.Min(updatedComment.Content.Length, 50))}...",
                    CommentId = updatedComment.CommentId
                });

                // 获取用户信息并构建返回DTO
                var user = await _coreDataQueryService.GetUserAsync(updatedComment.UserId);
                var commentDto = new CommentDto
                {
                    CommentId = updatedComment.CommentId,
                    TaskId = updatedComment.TaskId,
                    Content = updatedComment.Content,
                    UserId = updatedComment.UserId,
                    UserName = user?.Name ?? "未知用户", // 使用Name而不是Username
                    UserAvatarUrl = user?.AvatarUrl,
                    CreationTimestamp = updatedComment.CreationTimestamp,
                    LastUpdatedTimestamp = updatedComment.LastUpdatedTimestamp,
                    ParentCommentId = updatedComment.ParentCommentId,
                    IsPinned = updatedComment.IsPinned,
                    IsEdited = updatedComment.IsEdited,
                    MentionedUserIds = updatedComment.MentionedUserIds
                };

                _logger.LogInformation("评论更新成功: CommentId={CommentId}, IsEdited={IsEdited}, IsPinned={IsPinned}", 
                    updatedComment.CommentId, updatedComment.IsEdited, updatedComment.IsPinned);

                return ApiResponse<CommentDto>.CreateSuccess(commentDto, "评论更新成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新评论时发生错误: CommentId={CommentId}, TaskId={TaskId}", 
                    request.CommentId, request.TaskId);
                return ApiResponse<CommentDto>.CreateFail($"更新评论失败: {ex.Message}");
            }
        }
    }
}
