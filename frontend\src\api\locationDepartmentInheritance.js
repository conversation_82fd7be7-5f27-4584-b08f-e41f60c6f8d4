// File: frontend/src/api/locationDepartmentInheritance.js
// Description: 位置部门继承API接口

import request from '@/utils/request'

const API_BASE = '/v2/locationdepartmentinheritance'

export default {
  /**
   * 获取所有位置的部门继承信息
   */
  getLocationDepartmentInheritance() {
    return request({
      url: API_BASE,
      method: 'get'
    })
  },

  /**
   * 根据位置ID获取部门继承信息
   * @param {number} locationId 位置ID
   */
  getLocationDepartmentInheritanceById(locationId) {
    return request({
      url: `${API_BASE}/${locationId}`,
      method: 'get'
    })
  },

  /**
   * 获取部门位置统计信息
   */
  getDepartmentLocationStats() {
    return request({
      url: `${API_BASE}/department-stats`,
      method: 'get'
    })
  },

  /**
   * 根据部门ID获取其管理的所有位置
   * @param {number} departmentId 部门ID
   */
  getLocationsByDepartment(departmentId) {
    return request({
      url: `${API_BASE}/by-department/${departmentId}`,
      method: 'get'
    })
  },

  /**
   * 基于分类条件查询位置信息（POST方式）
   * @param {Object} query 查询条件
   */
  searchLocationsByClassification(query) {
    return request({
      url: `${API_BASE}/search`,
      method: 'post',
      data: query
    })
  },

  /**
   * 基于分类条件查询位置信息（GET方式）
   * @param {Object} params 查询参数
   */
  searchLocationsByClassificationGet(params) {
    return request({
      url: `${API_BASE}/search`,
      method: 'get',
      params
    })
  },

  /**
   * 获取位置分类统计信息
   */
  getLocationClassificationStats() {
    return request({
      url: `${API_BASE}/classification-stats`,
      method: 'get'
    })
  }
}