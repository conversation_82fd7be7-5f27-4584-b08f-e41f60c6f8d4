// File: Models/Enums/TaskStatus.cs
// Description: 任务状态枚举

using System.ComponentModel.DataAnnotations;

namespace ItAssetsSystem.Models.Enums
{
    /// <summary>
    /// 任务状态枚举
    /// </summary>
    public enum TaskStatus
    {
        /// <summary>
        /// 未开始
        /// </summary>
        [Display(Name = "未开始")]
        NotStarted = 0,

        /// <summary>
        /// 进行中
        /// </summary>
        [Display(Name = "进行中")]
        InProgress = 1,

        /// <summary>
        /// 已暂停
        /// </summary>
        [Display(Name = "已暂停")]
        OnHold = 2,

        /// <summary>
        /// 待复核
        /// </summary>
        [Display(Name = "待复核")]
        PendingReview = 3,

        /// <summary>
        /// 已完成
        /// </summary>
        [Display(Name = "已完成")]
        Completed = 4,

        /// <summary>
        /// 已取消
        /// </summary>
        [Display(Name = "已取消")]
        Cancelled = 5,

        /// <summary>
        /// 已逾期
        /// </summary>
        [Display(Name = "已逾期")]
        Overdue = 6
    }
} 