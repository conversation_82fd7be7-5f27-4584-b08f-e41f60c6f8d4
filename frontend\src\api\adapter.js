/**
 * API适配器 - 统一API版本管理
 * 文件路径: src/api/adapter.js
 * 功能描述: 根据版本配置动态选择API版本，支持无缝切换
 */

import versionManager from './version-manager'
import performanceMonitor from '@/services/performance-monitor'

// 导入V1版本API
import userApiV1 from './user'
import assetApiV1 from './asset'
// 注意：V1版本的task API需要从其他地方导入，因为当前task.js是V2版本

// 导入V1.1版本API
import userApiV1_1 from './v1.1/user'
import assetApiV1_1 from './v1.1/asset'
import taskApiV1_1 from './v1.1/task'

// 导入V2版本API
import { taskApi as taskApiV2 } from './task'  // 🔧 添加：当前task.js是V2版本

/**
 * API适配器类
 */
class ApiAdapter {
  constructor() {
    this.versionManager = versionManager
    this.apiVersions = {
      user: {
        'v1': userApiV1,
        'v1.1': userApiV1_1
      },
      asset: {
        'v1': assetApiV1,
        'v1.1': assetApiV1_1
      },
      task: {
        'v1': taskApiV1_1,  // 🔧 修改：使用v1.1作为v1的替代，因为当前task.js是v2
        'v1.1': taskApiV1_1,
        'v2': taskApiV2     // 🔧 添加：v2版本支持
      }
    }
  }

  /**
   * 获取指定服务的API实例
   * @param {string} serviceName 服务名称
   * @returns {Object} API实例
   */
  getApi(serviceName) {
    const version = this.versionManager.getServiceVersion(serviceName)
    const api = this.apiVersions[serviceName]?.[version]

    if (!api) {
      console.warn(`服务 ${serviceName} 的版本 ${version} 不存在，使用V1版本`)
      return this.apiVersions[serviceName]?.['v1'] || {}
    }

    console.log(`使用 ${serviceName} 服务的 ${version} 版本`)

    // 包装API方法以添加性能监控
    return this.wrapApiWithPerformanceMonitoring(api, serviceName, version)
  }

  /**
   * 包装API方法以添加性能监控
   * @param {Object} api API实例
   * @param {string} serviceName 服务名称
   * @param {string} version 版本号
   * @returns {Object} 包装后的API实例
   */
  wrapApiWithPerformanceMonitoring(api, serviceName, version) {
    const wrappedApi = {}

    for (const [methodName, method] of Object.entries(api)) {
      if (typeof method === 'function') {
        wrappedApi[methodName] = async (...args) => {
          const apiName = `${serviceName}.${methodName}`

          return await performanceMonitor.monitorApiCall(
            apiName,
            version,
            () => method.apply(api, args)
          )
        }
      } else {
        wrappedApi[methodName] = method
      }
    }

    return wrappedApi
  }

  /**
   * 获取用户API
   * @returns {Object} 用户API实例
   */
  get userApi() {
    return this.getApi('user')
  }

  /**
   * 获取资产API
   * @returns {Object} 资产API实例
   */
  get assetApi() {
    return this.getApi('asset')
  }

  /**
   * 获取任务API
   * @returns {Object} 任务API实例
   */
  get taskApi() {
    return this.getApi('task')
  }

  /**
   * 切换服务版本
   * @param {string} serviceName 服务名称
   * @param {string} targetVersion 目标版本
   * @returns {Promise<boolean>} 切换结果
   */
  async switchVersion(serviceName, targetVersion) {
    const success = await this.versionManager.switchServiceVersion(serviceName, targetVersion)
    if (success) {
      console.log(`服务 ${serviceName} 已切换到版本 ${targetVersion}`)
    }
    return success
  }

  /**
   * 批量切换版本
   * @param {Object} versionMap 版本映射 {serviceName: version}
   * @returns {Promise<Object>} 切换结果
   */
  async batchSwitchVersions(versionMap) {
    const results = {}
    
    for (const [serviceName, version] of Object.entries(versionMap)) {
      try {
        results[serviceName] = await this.switchVersion(serviceName, version)
      } catch (error) {
        console.error(`切换服务 ${serviceName} 到版本 ${version} 失败:`, error)
        results[serviceName] = false
      }
    }
    
    return results
  }

  /**
   * 获取所有服务的当前版本
   * @returns {Object} 版本映射
   */
  getCurrentVersions() {
    return this.versionManager.getCurrentVersionStatus().versions
  }

  /**
   * 获取支持的版本列表
   * @returns {Object} 支持的版本映射
   */
  getSupportedVersions() {
    return this.versionManager.getSupportedVersions()
  }

  /**
   * 检查版本兼容性
   * @param {string} serviceName 服务名称
   * @param {string} version 版本号
   * @returns {boolean} 是否兼容
   */
  isVersionCompatible(serviceName, version) {
    return this.versionManager.isVersionSupported(serviceName, version)
  }

  /**
   * 重置所有服务到默认版本
   */
  resetToDefaultVersions() {
    this.versionManager.resetToDefaultVersions()
    console.log('所有服务已重置到默认版本')
  }

  /**
   * 启用A/B测试
   * @param {Object} config A/B测试配置
   * @returns {Promise<boolean>} 启用结果
   */
  async enableABTest(config) {
    return await this.versionManager.enableABTest(config)
  }

  /**
   * 获取版本统计
   * @returns {Promise<Object>} 版本统计数据
   */
  async getVersionStatistics() {
    return await this.versionManager.getVersionStatistics()
  }

  /**
   * 获取性能对比数据
   * @param {string} apiName 可选，指定API名称
   * @returns {Object} 性能对比数据
   */
  getPerformanceComparison(apiName = null) {
    if (apiName) {
      return performanceMonitor.getPerformanceComparison(apiName)
    }
    return performanceMonitor.getAllPerformanceComparisons()
  }

  /**
   * 获取实时性能指标
   * @returns {Object} 实时性能数据
   */
  getRealTimeMetrics() {
    return performanceMonitor.getRealTimeMetrics()
  }

  /**
   * 清除性能数据
   * @param {string} apiName 可选，指定API名称
   */
  clearPerformanceData(apiName = null) {
    performanceMonitor.clearMetrics(apiName)
  }

  /**
   * 导出性能数据
   * @returns {Object} 性能数据
   */
  exportPerformanceData() {
    return performanceMonitor.exportData()
  }

  /**
   * 启用/禁用性能监控
   * @param {boolean} enabled 是否启用
   */
  setPerformanceMonitoringEnabled(enabled) {
    performanceMonitor.setEnabled(enabled)
  }

  /**
   * 监听版本切换事件
   * @param {Function} callback 回调函数
   */
  onVersionChange(callback) {
    this.versionManager.onVersionChange(callback)
  }

  /**
   * 移除版本切换事件监听
   * @param {Function} callback 回调函数
   */
  offVersionChange(callback) {
    this.versionManager.offVersionChange(callback)
  }

  /**
   * 初始化适配器
   * @param {number} userId 用户ID
   */
  async initialize(userId = null) {
    await this.versionManager.initialize(userId)
    console.log('API适配器初始化完成')
  }

  /**
   * 获取健康检查状态
   * @returns {Promise<Object>} 健康检查结果
   */
  async getHealthStatus() {
    const results = {}
    const services = ['user', 'asset', 'task']
    
    for (const serviceName of services) {
      try {
        const api = this.getApi(serviceName)
        if (api.healthCheck) {
          const response = await api.healthCheck()
          results[serviceName] = {
            version: this.versionManager.getServiceVersion(serviceName),
            status: response.status || 'healthy',
            timestamp: response.timestamp || new Date().toISOString()
          }
        } else {
          results[serviceName] = {
            version: this.versionManager.getServiceVersion(serviceName),
            status: 'unknown',
            message: '健康检查方法不存在'
          }
        }
      } catch (error) {
        results[serviceName] = {
          version: this.versionManager.getServiceVersion(serviceName),
          status: 'error',
          error: error.message
        }
      }
    }
    
    return results
  }
}

// 创建全局实例
const apiAdapter = new ApiAdapter()

// 导出实例和类
export { apiAdapter, ApiAdapter }
export default apiAdapter
