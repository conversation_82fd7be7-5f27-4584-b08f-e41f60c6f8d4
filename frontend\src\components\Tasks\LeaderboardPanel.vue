/**
 * 航空航天级IT资产管理系统 - 积分排行榜组件
 * 文件路径: src/components/Tasks/LeaderboardPanel.vue
 * 功能描述: 显示用户积分排名，支持不同时间维度切换
 */

<template>
  <div class="leaderboard-panel">
    <!-- 榜单切换 -->
    <div class="leaderboard-tabs">
      <el-radio-group v-model="currentType" size="medium">
        <el-radio-button label="daily">日榜</el-radio-button>
        <el-radio-button label="weekly">周榜</el-radio-button>
        <el-radio-button label="monthly">月榜</el-radio-button>
        <el-radio-button label="total">总榜</el-radio-button>
      </el-radio-group>
      
      <div class="board-date">
        {{ boardDate }}
      </div>
    </div>
    
    <!-- 榜单内容 -->
    <div class="leaderboard-content">
      <div class="top-winners" v-if="topWinners.length > 0">
        <!-- 第2名 -->
        <div class="winner-card second-place" v-if="topWinners.length > 1">
          <div class="rank-badge rank-2">2</div>
          <el-avatar :size="60" :src="topWinners[1].avatar">{{ topWinners[1].userName.substr(0, 1) }}</el-avatar>
          <div class="winner-name">{{ topWinners[1].userName }}</div>
          <div class="winner-score">
            <i class="el-icon-star-on"></i> {{ topWinners[1].points }}
          </div>
          <div class="winner-badges">
            <span v-for="(badge, index) in topWinners[1].badges" :key="index">{{ badge }}</span>
          </div>
        </div>
        
        <!-- 第1名 -->
        <div class="winner-card first-place">
          <div class="crown-icon">👑</div>
          <div class="rank-badge rank-1">1</div>
          <el-avatar :size="80" :src="topWinners[0].avatar">{{ topWinners[0].userName.substr(0, 1) }}</el-avatar>
          <div class="winner-name">{{ topWinners[0].userName }}</div>
          <div class="winner-score">
            <i class="el-icon-star-on"></i> {{ topWinners[0].points }}
          </div>
          <div class="winner-badges">
            <span v-for="(badge, index) in topWinners[0].badges" :key="index">{{ badge }}</span>
          </div>
        </div>
        
        <!-- 第3名 -->
        <div class="winner-card third-place" v-if="topWinners.length > 2">
          <div class="rank-badge rank-3">3</div>
          <el-avatar :size="60" :src="topWinners[2].avatar">{{ topWinners[2].userName.substr(0, 1) }}</el-avatar>
          <div class="winner-name">{{ topWinners[2].userName }}</div>
          <div class="winner-score">
            <i class="el-icon-star-on"></i> {{ topWinners[2].points }}
          </div>
          <div class="winner-badges">
            <span v-for="(badge, index) in topWinners[2].badges" :key="index">{{ badge }}</span>
          </div>
        </div>
      </div>
      
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-state">
        <i class="el-icon-loading"></i>
        <span>加载榜单数据中...</span>
      </div>
      
      <!-- 其他排名 -->
      <div class="rank-list" v-if="otherRanks.length > 0">
        <div 
          v-for="item in otherRanks" 
          :key="item.userId"
          class="rank-item"
          :class="{ 'is-current-user': item.isCurrentUser }"
        >
          <div class="rank-number">{{ item.ranking }}</div>
          <el-avatar :size="32" :src="item.avatar">{{ item.userName.substr(0, 1) }}</el-avatar>
          <div class="rank-user-name">{{ item.userName }}</div>
          <div class="rank-user-score">
            <i class="el-icon-star-on"></i> {{ item.points }}
          </div>
          <div class="rank-user-badges">
            <span v-for="(badge, index) in item.badges" :key="index">{{ badge }}</span>
          </div>
        </div>
      </div>
      
      <!-- 空数据提示 -->
      <div v-if="!loading && leaderboardData.length === 0" class="empty-state">
        <i class="el-icon-data-line"></i>
        <span>暂无排行数据</span>
      </div>
      
      <!-- 我的排名 -->
      <div class="my-rank" v-if="myRank">
        <div class="divider"></div>
        <div class="rank-item is-current-user">
          <div class="rank-number">{{ myRank.ranking }}</div>
          <el-avatar :size="32" :src="myRank.avatar">{{ myRank.userName.substr(0, 1) }}</el-avatar>
          <div class="rank-user-name">{{ myRank.userName }}</div>
          <div class="rank-user-score">
            <i class="el-icon-star-on"></i> {{ myRank.points }}
          </div>
          <div class="rank-user-badges">
            <span v-for="(badge, index) in myRank.badges" :key="index">{{ badge }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, watch, onMounted } from 'vue'
import { taskApi } from '@/api/task'
import { formatDate } from '@/utils/format'

export default {
  name: 'LeaderboardPanel',
  
  setup() {
    // 当前用户ID
    const currentUserId = 1 // 后续应从用户登录状态中获取
    
    // 排行榜类型
    const currentType = ref('daily')
    
    // 榜单数据
    const leaderboardData = ref([])
    const loading = ref(false)
    
    // 获取排行榜数据
    const fetchLeaderboard = async () => {
      loading.value = true
      try {
        const res = await taskApi.getLeaderboard(currentType.value)
        if (res.success) {
          leaderboardData.value = res.data.users || []
        }
      } catch (error) {
        console.error('获取排行榜数据失败', error)
      } finally {
        loading.value = false
      }
    }
    
    // 获取榜单日期
    const boardDate = computed(() => {
      const now = new Date()
      if (currentType.value === 'daily') {
        return formatDate(now, 'YYYY-MM-DD')
      } else if (currentType.value === 'weekly') {
        const startOfWeek = new Date(now)
        startOfWeek.setDate(now.getDate() - now.getDay())
        return `${formatDate(startOfWeek, 'MM-DD')} 至 ${formatDate(now, 'MM-DD')}`
      } else if (currentType.value === 'monthly') {
        return formatDate(now, 'YYYY-MM')
      } else {
        return '总排行'
      }
    })
    
    // 前三名
    const topWinners = computed(() => {
      return leaderboardData.value.slice(0, 3)
    })
    
    // 其他排名
    const otherRanks = computed(() => {
      const result = leaderboardData.value.slice(3, 10).map(item => ({
        ...item,
        isCurrentUser: item.userId === currentUserId
      }))
      return result
    })
    
    // 我的排名 - 如果不在前10名显示在底部
    const myRank = computed(() => {
      // 如果我在前10名，则不需要单独显示
      if (topWinners.value.some(w => w.userId === currentUserId) || 
          otherRanks.value.some(r => r.userId === currentUserId)) {
        return null
      }
      
      // 查找我的排名
      const myRankData = leaderboardData.value.find(item => item.userId === currentUserId)
      if (myRankData) {
        return {
          ...myRankData,
          isCurrentUser: true
        }
      }
      
      return null
    })
    
    // 监听类型变化，重新获取数据
    watch(currentType, () => {
      fetchLeaderboard()
    })
    
    // 组件挂载时获取数据
    onMounted(() => {
      fetchLeaderboard()
    })
    
    return {
      currentType,
      loading,
      leaderboardData,
      boardDate,
      topWinners,
      otherRanks,
      myRank
    }
  }
}
</script>

<style lang="scss" scoped>
.leaderboard-panel {
  padding: 0 10px;
  
  .leaderboard-tabs {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    
    .board-date {
      color: #909399;
      font-size: 14px;
    }
  }
  
  .leaderboard-content {
    .top-winners {
      display: flex;
      justify-content: center;
      align-items: flex-end;
      margin-bottom: 30px;
      position: relative;
      height: 240px;
      
      .winner-card {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 16px;
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        position: relative;
        transition: all 0.3s;
        z-index: 1;
        
        &:hover {
          transform: translateY(-5px);
          box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.15);
        }
        
        .rank-badge {
          position: absolute;
          top: -10px;
          left: -10px;
          width: 30px;
          height: 30px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 50%;
          font-weight: bold;
          color: white;
          
          &.rank-1 {
            background: linear-gradient(135deg, #FFD700, #FFA500);
          }
          
          &.rank-2 {
            background: linear-gradient(135deg, #E0E0E0, #B0B0B0);
          }
          
          &.rank-3 {
            background: linear-gradient(135deg, #CD7F32, #A05A2C);
          }
        }
        
        .crown-icon {
          position: absolute;
          top: -25px;
          font-size: 28px;
          animation: float 2s ease-in-out infinite;
        }
        
        .winner-name {
          margin-top: 12px;
          font-weight: 500;
          font-size: 16px;
        }
        
        .winner-score {
          margin-top: 8px;
          color: #F7BA2A;
          font-weight: bold;
          font-size: 18px;
          
          i {
            margin-right: 4px;
          }
        }
        
        .winner-badges {
          margin-top: 8px;
          display: flex;
          gap: 4px;
          font-size: 16px;
        }
        
        &.first-place {
          margin: 0 -10px;
          z-index: 3;
          height: 210px;
          background: linear-gradient(to bottom, rgba(255, 217, 0, 0.05), rgba(255, 217, 0, 0));
          border: 1px solid rgba(255, 217, 0, 0.2);
        }
        
        &.second-place {
          z-index: 2;
          height: 180px;
          background: linear-gradient(to bottom, rgba(224, 224, 224, 0.05), rgba(224, 224, 224, 0));
          border: 1px solid rgba(224, 224, 224, 0.2);
        }
        
        &.third-place {
          z-index: 1;
          height: 180px;
          background: linear-gradient(to bottom, rgba(205, 127, 50, 0.05), rgba(205, 127, 50, 0));
          border: 1px solid rgba(205, 127, 50, 0.2);
        }
      }
    }
    
    .loading-state,
    .empty-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: #909399;
      padding: 32px 0;
      
      i {
        font-size: 32px;
        margin-bottom: 8px;
      }
    }
    
    .rank-list {
      margin-bottom: 16px;
      
      .rank-item {
        display: flex;
        align-items: center;
        padding: 12px 16px;
        background: #fff;
        margin-bottom: 8px;
        border-radius: 4px;
        transition: all 0.3s;
        
        &:hover {
          background: #f8f9fb;
        }
        
        &.is-current-user {
          background: #ecf5ff;
          border: 1px solid #d9ecff;
        }
        
        .rank-number {
          width: 24px;
          font-weight: 500;
          color: #606266;
          margin-right: 16px;
        }
        
        .rank-user-name {
          flex: 1;
          margin-left: 12px;
          font-weight: 500;
        }
        
        .rank-user-score {
          color: #F7BA2A;
          font-weight: bold;
          margin-right: 12px;
          
          i {
            margin-right: 4px;
          }
        }
        
        .rank-user-badges {
          display: flex;
          gap: 4px;
          font-size: 14px;
        }
      }
    }
    
    .my-rank {
      .divider {
        height: 1px;
        background: #dcdfe6;
        margin: 16px 0;
      }
    }
  }
}

@keyframes float {
  0% { transform: translateY(0); }
  50% { transform: translateY(-5px); }
  100% { transform: translateY(0); }
}
</style> 