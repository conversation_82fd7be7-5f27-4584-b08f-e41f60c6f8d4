// IT资产管理系统 - 部门实体
// 文件路径: /Models/Entities/Department.cs
// 功能: 定义部门实体，对应departments表

using System;
using System.Collections.Generic;
using ItAssetsSystem.Models;

namespace ItAssetsSystem.Models.Entities
{
    /// <summary>
    /// 部门实体
    /// </summary>
    public class Department : IAuditableEntity
    {
        /// <summary>
        /// 部门ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 部门编码
        /// </summary>
        public string Code { get; set; }

        /// <summary>
        /// 部门名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 父部门ID
        /// </summary>
        public int? ParentId { get; set; }

        /// <summary>
        /// 层级路径（例如：1,2,3）
        /// </summary>
        public string Path { get; set; }

        /// <summary>
        /// 部门经理ID(一级负责人)
        /// </summary>
        public int? ManagerId { get; set; }

        /// <summary>
        /// 部门主任ID(二级负责人)
        /// </summary>
        public int? DeputyManagerId { get; set; }

        /// <summary>
        /// 是否激活
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; }

        /// <summary>
        /// 父部门
        /// </summary>
        public virtual Department Parent { get; set; }

        /// <summary>
        /// 子部门
        /// </summary>
        public virtual ICollection<Department> Children { get; set; }

        /// <summary>
        /// 关联的资产
        /// </summary>
        public virtual ICollection<Asset> Assets { get; set; }

        /// <summary>
        /// 部门用户
        /// </summary>
        public virtual ICollection<User> Users { get; set; }
    }
} 