<template>
  <div class="factory-dashboard">
    <!-- Header -->
    <header class="dashboard-header">
      <div class="max-w-7xl mx-auto flex flex-wrap items-center justify-between gap-2">
        <div class="flex items-center">
          <div class="header-icon">
            <el-icon size="32"><Cpu /></el-icon>
          </div>
          <div class="header-title">
            <h1>智能制造监控系统</h1>
            <p>实时工厂状态监控 • {{ stats.total }}个工位</p>
          </div>
          <div class="update-badge">
            <span class="text-gray-400">最后更新: </span>
            <span class="font-medium">{{ formattedTime }}</span>
          </div>
        </div>
        
        <div class="header-controls">
          <!-- 配置文件选择器 -->
          <el-select
            v-model="configType"
            @change="handleConfigChange"
            placeholder="选择布局配置"
            class="config-selector"
            size="small"
          >
            <el-option label="默认布局" value="default" />
            <el-option label="自定义布局" value="custom" />
            <el-option label="导入JSON文件" value="import" />
          </el-select>

          <!-- JSON文件导入 -->
          <div v-if="configType === 'import'" class="file-import-section">
            <el-upload
              ref="uploadRef"
              :auto-upload="false"
              :show-file-list="false"
              accept=".json"
              :on-change="handleFileChange"
              class="json-uploader"
            >
              <el-button type="info" size="small" :icon="Setting">选择JSON</el-button>
            </el-upload>
            <el-button
              type="success"
              size="small"
              @click="loadDefaultJson"
              title="加载默认JSON文件"
            >
              加载默认
            </el-button>
            <span v-if="importedFileName" class="imported-file-name">{{ importedFileName }}</span>
          </div>
          
          <!-- 搜索框 -->
          <el-input
            v-model="searchTerm"
            placeholder="搜索工位..."
            class="search-input"
            clearable
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          
          <!-- 刷新按钮 -->
          <el-button 
            @click="handleRefresh"
            :loading="refreshing"
            size="small"
            circle
            title="刷新数据"
          >
            <el-icon><Refresh /></el-icon>
          </el-button>
          
          <!-- 全屏按钮 -->
          <el-button 
            @click="toggleFullScreen"
            size="small"
            circle
            :title="isFullScreen ? '退出全屏' : '全屏'"
          >
            <el-icon v-if="isFullScreen"><Close /></el-icon>
            <el-icon v-else><FullScreen /></el-icon>
          </el-button>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto p-3 flex-grow">
      <!-- Loading State -->
      <div v-if="loading" class="loading-container">
        <el-loading-spinner text="正在加载工厂布局配置..." />
      </div>
      
      <!-- Main Content -->
      <div v-else class="dashboard-main">
        <!-- Left Stats Panel -->
        <div class="stats-panel">
          <el-card class="stats-card">
            <template #header>
              <div class="card-header">
                <span>实时概览</span>
                <span class="update-time">{{ formattedTime }}</span>
              </div>
            </template>
            
            <div class="stats-grid">
              <div class="stat-item operational">
                <div class="stat-icon">
                  <el-icon><Check /></el-icon>
                </div>
                <div class="stat-content">
                  <div class="stat-value">{{ stats.operational }}</div>
                  <div class="stat-label">运行正常</div>
                  <div class="stat-percent">{{ stats.operationalPercent }}%</div>
                </div>
              </div>
              
              <div class="stat-item warning">
                <div class="stat-icon">
                  <el-icon><Warning /></el-icon>
                </div>
                <div class="stat-content">
                  <div class="stat-value">{{ stats.warning }}</div>
                  <div class="stat-label">警告状态</div>
                </div>
              </div>
              
              <div class="stat-item error">
                <div class="stat-icon">
                  <el-icon><CloseIcon /></el-icon>
                </div>
                <div class="stat-content">
                  <div class="stat-value">{{ stats.error }}</div>
                  <div class="stat-label">错误状态</div>
                </div>
              </div>
              
              <div class="stat-item idle">
                <div class="stat-icon">
                  <el-icon><Setting /></el-icon>
                </div>
                <div class="stat-content">
                  <div class="stat-value">{{ stats.idle }}</div>
                  <div class="stat-label">空闲工位</div>
                </div>
              </div>
            </div>

            <div class="efficiency-summary">
              <div class="efficiency-item">
                <div class="efficiency-value">{{ avgEfficiency }}%</div>
                <div class="efficiency-label">平均效率</div>
              </div>
              <div class="efficiency-item">
                <div class="efficiency-value">{{ totalAssets }}</div>
                <div class="efficiency-label">总设备数</div>
              </div>
            </div>
          </el-card>
        </div>

        <!-- Factory Layout -->
        <div class="factory-layout">
          <div class="layout-header">
            <span>显示: {{ displayedLocations.length }} / {{ stats.total }}</span>
            <div class="header-controls">
              <el-select v-model="viewMode" class="view-selector">
                <el-option label="布局视图" value="layout" />
                <el-option label="列表视图" value="list" />
              </el-select>
            </div>
          </div>

          <!-- Layout View -->
          <div v-if="viewMode === 'layout'" class="factory-floor" ref="factoryFloor">
            <!-- Factory Grid Background -->
            <svg class="factory-grid" :viewBox="canvasViewBox">
              <defs>
                <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
                  <path d="M 20 0 L 0 0 0 20" fill="none" stroke="rgba(107, 148, 214, 0.08)" stroke-width="1"/>
                </pattern>
              </defs>
              <rect width="100%" height="100%" fill="url(#grid)" />
            </svg>

            <!-- 动态区域容器布局系统 -->
            <div class="zone-containers">
              <!-- 动态生成区域容器 -->
              <div 
                v-for="zone in layoutConfig?.zones || []" 
                :key="zone.id"
                class="zone-container"
                :class="`zone-${zone.id}`"
                :data-zone="zone.name"
                :style="getZoneContainerStyle(zone)"
              >
                <div class="zone-workstations" :style="getZoneGridStyle(zone)">
                  <div
                    v-for="location in getZoneWorkstations(zone.name)"
                    :key="location.locationId"
                    class="workstation-cell"
                    :class="getWorkstationClasses(location)"
                    @click="handleLocationClick(location.locationId)"
                    @mouseenter="handleLocationMouseEnter(location.locationId)"
                    @mouseleave="handleLocationMouseLeave"
                  >
                    <div class="cell-content">
                      <el-icon v-if="location.status === 'error'" class="status-icon error"><CloseIcon /></el-icon>
                      <el-icon v-else-if="location.status === 'warning'" class="status-icon warning"><Warning /></el-icon>
                      <el-icon v-else-if="location.status === 'operational'" class="status-icon operational"><Check /></el-icon>
                      <span v-else class="cell-number">{{ location.locationId }}</span>
                    </div>
                    <div v-if="location.faultCount > 0" class="fault-badge">{{ location.faultCount }}</div>
                    <div v-if="hoveredLocationId === location.locationId" class="hover-tooltip">
                      {{ location.locationName }} - {{ location.efficiency }}%
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- List View -->
          <div v-else class="factory-list">
            <el-table :data="displayedLocations" height="100%">
              <el-table-column prop="locationCode" label="工位编号" width="100" />
              <el-table-column prop="locationName" label="工位名称" />
              <el-table-column prop="departmentName" label="所属部门" />
              <el-table-column prop="efficiency" label="效率" width="80">
                <template #default="{ row }">
                  <span>{{ row.efficiency }}%</span>
                </template>
              </el-table-column>
              <el-table-column prop="status" label="状态" width="100">
                <template #default="{ row }">
                  <el-tag :type="getStatusTagType(row.status)">
                    {{ getStatusText(row.status) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="assetCount" label="设备数" width="80" />
              <el-table-column prop="taskCount" label="任务数" width="80" />
              <el-table-column label="操作" width="100">
                <template #default="{ row }">
                  <el-button size="small" @click="handleLocationClick(row.locationId)">
                    详情
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Search, Refresh, FullScreen, Close, Cpu,
  Check, Warning, Close as CloseIcon, Setting
} from '@element-plus/icons-vue'

// 响应式数据
const locations = ref([])
const selectedLocationId = ref(null)
const hoveredLocationId = ref(null)
const searchTerm = ref('')
const isFullScreen = ref(false)
const lastUpdate = ref(new Date())
const refreshing = ref(false)
const viewMode = ref('layout')

// 布局配置
const layoutConfig = ref(null)
const loading = ref(true)
const configType = ref('import')
const importedFileName = ref('')
const importedFileContent = ref(null)
const uploadRef = ref(null)

// 基础函数
const handleConfigChange = () => {
  console.log('配置变更')
}

const handleFileChange = () => {
  console.log('文件变更')
}

const loadDefaultJson = () => {
  console.log('加载默认JSON')
}

const handleRefresh = () => {
  console.log('刷新')
}

const toggleFullScreen = () => {
  console.log('全屏切换')
}

const handleLocationClick = () => {
  console.log('工位点击')
}

const handleLocationMouseEnter = () => {
  console.log('鼠标进入')
}

const handleLocationMouseLeave = () => {
  console.log('鼠标离开')
}

const getZoneContainerStyle = () => {
  return {}
}

const getZoneGridStyle = () => {
  return {}
}

const getZoneWorkstations = () => {
  return []
}

const getWorkstationClasses = () => {
  return []
}

const getStatusTagType = () => {
  return 'info'
}

const getStatusText = () => {
  return '正常'
}

// 计算属性
const displayedLocations = computed(() => {
  return locations.value
})

const stats = computed(() => {
  return {
    total: 0,
    operational: 0,
    warning: 0,
    error: 0,
    idle: 0,
    operationalPercent: 0
  }
})

const formattedTime = computed(() => {
  return lastUpdate.value.toLocaleTimeString('zh-CN')
})

const avgEfficiency = computed(() => {
  return 0
})

const totalAssets = computed(() => {
  return 0
})

const canvasViewBox = computed(() => {
  return '0 0 1200 500'
})

// 生命周期
onMounted(() => {
  loading.value = false
})
</script>

<style scoped>
.factory-dashboard {
  min-height: 100vh;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
  color: #f1f5f9;
}

.dashboard-header {
  background: rgba(15, 23, 42, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(59, 130, 246, 0.2);
  padding: 1rem 0;
}

.header-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(45deg, #3b82f6, #1d4ed8);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
}

.dashboard-main {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 1.5rem;
  height: calc(100vh - 140px);
}

.stats-panel {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.stats-card {
  background: rgba(30, 41, 59, 0.6);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 16px;
}

.factory-layout {
  background: rgba(30, 41, 59, 0.4);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 16px;
  padding: 1rem;
  overflow: hidden;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: calc(100vh - 140px);
}
</style>
