/**
 * 导出测试API
 * 文件路径: src/api/testExport.js
 * 功能描述: 提供简化的导出测试功能
 */

import request from '@/utils/request'
import systemConfig from '@/config/system'

/**
 * 测试导出XLSX格式 (Excel 2007+)
 * @returns {Promise} 返回导出的文件流
 */
export function exportTestXlsx() {
  console.log('测试导出XLSX格式 - API调用', {
    请求方法: 'GET',
    请求路径: '/ExportTest/xlsx',
    完整URL: `${systemConfig.apiBaseUrl}/ExportTest/xlsx`
  })
  return request.get('/ExportTest/xlsx', { 
    responseType: 'blob',
    headers: {
      'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    }
  })
}

/**
 * 测试导出XLS格式 (Excel 97-2003)
 * @returns {Promise} 返回导出的文件流
 */
export function exportTestXls() {
  console.log('测试导出XLS格式 - API调用', {
    请求方法: 'GET',
    请求路径: '/ExportTest/xls',
    完整URL: `${systemConfig.apiBaseUrl}/ExportTest/xls`
  })
  return request.get('/ExportTest/xls', { 
    responseType: 'blob',
    headers: {
      'Accept': 'application/vnd.ms-excel'
    }
  })
}

// 默认导出所有API函数
export default {
  exportTestXlsx,
  exportTestXls
} 