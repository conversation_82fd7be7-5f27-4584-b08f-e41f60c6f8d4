#nullable enable
// File: Application/Features/Tasks/Dtos/CreateTaskRequestDto.cs
// Description: 创建任务请求的数据传输对象

using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace ItAssetsSystem.Application.Features.Tasks.Dtos
{
    /// <summary>
    /// 创建任务请求 DTO
    /// </summary>
    public class CreateTaskRequestDto
    {
        [Required(ErrorMessage = "任务名称不能为空")]
        [StringLength(200, ErrorMessage = "任务名称长度不能超过200个字符")]
        public string Name { get; set; } = null!;

        [StringLength(2000, ErrorMessage = "任务描述长度不能超过2000个字符")]
        public string? Description { get; set; }

        // Status, Priority, TaskType 会在 TaskService 中设置默认值 (如果为空)
        [StringLength(50, ErrorMessage = "状态长度不能超过50个字符")]
        public string? Status { get; set; }

        [StringLength(50, ErrorMessage = "优先级长度不能超过50个字符")]
        public string? Priority { get; set; }
        
        [Required(ErrorMessage = "任务类型不能为空")]
        [StringLength(50, ErrorMessage = "任务类型长度不能超过50个字符")]
        public string TaskType { get; set; } = "Normal"; // 默认为 "Normal"

        public DateTime? PlanStartDate { get; set; }

        public DateTime? PlanEndDate { get; set; }

        public int? AssigneeUserId { get; set; }
        
        /// <summary>
        /// 协作者用户ID列表，包括所有除主负责人外的协作者
        /// </summary>
        public List<int>? CollaboratorUserIds { get; set; }

        public long? ParentTaskId { get; set; }

        [StringLength(50, ErrorMessage = "PDCA阶段长度不能超过50个字符")]
        public string? PDCAStage { get; set; }

        public int? Points { get; set; }

        public long? ProjectId { get; set; } // 假设项目ID是long类型

        public int? AssetId { get; set; } // 核心资产ID是int

        public int? LocationId { get; set; } // 核心位置ID是int

        /// <summary>
        /// 任务分类ID
        /// </summary>
        public int? CategoryId { get; set; }

        public List<int>? ParticipantUserIdsList { get; set; }
    }
} 