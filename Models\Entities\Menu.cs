// IT资产管理系统 - 菜单实体
// 文件路径: /Models/Entities/Menu.cs
// 功能: 定义菜单实体，对应menus表

using System;
using System.Collections.Generic;
using ItAssetsSystem.Models;

namespace ItAssetsSystem.Models.Entities
{
    /// <summary>
    /// 菜单实体
    /// </summary>
    public class Menu : IAuditableEntity
    {
        /// <summary>
        /// 菜单ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 菜单名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 菜单编码
        /// </summary>
        public string Code { get; set; }

        /// <summary>
        /// 上级菜单ID
        /// </summary>
        public int? ParentId { get; set; }

        /// <summary>
        /// 图标
        /// </summary>
        public string Icon { get; set; }

        /// <summary>
        /// 路由路径
        /// </summary>
        public string Path { get; set; }

        /// <summary>
        /// 组件路径
        /// </summary>
        public string Component { get; set; }

        /// <summary>
        /// 排序
        /// </summary>
        public int SortOrder { get; set; }

        /// <summary>
        /// 是否显示
        /// </summary>
        public bool IsVisible { get; set; }

        /// <summary>
        /// 是否激活
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; }

        /// <summary>
        /// 上级菜单
        /// </summary>
        public virtual Menu Parent { get; set; }

        /// <summary>
        /// 下级菜单
        /// </summary>
        public virtual ICollection<Menu> Children { get; set; }

        /// <summary>
        /// 菜单角色关联
        /// </summary>
        public virtual ICollection<RoleMenu> RoleMenus { get; set; }
    }
} 