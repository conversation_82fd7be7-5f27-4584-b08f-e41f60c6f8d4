// IT资产管理系统 - 插件接口
// 文件路径: /Core/Abstractions/IPlugin.cs
// 功能: 定义插件的基本接口，包括插件初始化、启动和停止的方法

using Microsoft.Extensions.DependencyInjection;
using System;

namespace ItAssetsSystem.Core.Abstractions
{
    /// <summary>
    /// 定义插件的基本接口
    /// </summary>
    public interface IPlugin
    {
        /// <summary>
        /// 插件名称
        /// </summary>
        string Name { get; }

        /// <summary>
        /// 插件描述
        /// </summary>
        string Description { get; }

        /// <summary>
        /// 插件版本
        /// </summary>
        Version Version { get; }

        /// <summary>
        /// 插件是否已启动
        /// </summary>
        bool IsRunning { get; }

        /// <summary>
        /// 注册插件服务
        /// </summary>
        /// <param name="services">服务集合</param>
        void RegisterServices(IServiceCollection services);

        /// <summary>
        /// 启动插件
        /// </summary>
        /// <param name="serviceProvider">服务提供程序</param>
        void Start(IServiceProvider serviceProvider);

        /// <summary>
        /// 停止插件
        /// </summary>
        void Stop();
    }
}

// 计划行数: 25
// 实际行数: 25 