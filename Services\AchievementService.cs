// File: Services/AchievementService.cs
// Description: 成就系统服务实现

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using ItAssetsSystem.Infrastructure.Data;
using ItAssetsSystem.Models.Entities.Gamification;
using ItAssetsSystem.Services.Interfaces;

namespace ItAssetsSystem.Services
{
    /// <summary>
    /// 成就系统服务实现
    /// </summary>
    public class AchievementService : IAchievementService
    {
        private readonly AppDbContext _context;
        private readonly ILogger<AchievementService> _logger;
        private readonly IGamificationService _gamificationService;

        public AchievementService(
            AppDbContext context,
            ILogger<AchievementService> logger,
            IGamificationService gamificationService)
        {
            _context = context;
            _logger = logger;
            _gamificationService = gamificationService;
        }

        /// <summary>
        /// 检查用户成就进度
        /// </summary>
        public async Task CheckUserAchievementsAsync(int userId)
        {
            try
            {
                var userStats = await _context.GamificationUserStats
                    .FirstOrDefaultAsync(s => s.CoreUserId == userId);

                if (userStats == null)
                {
                    _logger.LogWarning("用户 {UserId} 的游戏化统计不存在", userId);
                    return;
                }

                // 获取所有活跃的成就
                var achievements = await _context.Achievements
                    .Where(a => a.IsActive)
                    .OrderBy(a => a.SortOrder)
                    .ToListAsync();

                foreach (var achievement in achievements)
                {
                    await CheckSingleAchievementAsync(userId, achievement, userStats);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查用户 {UserId} 成就进度失败", userId);
            }
        }

        /// <summary>
        /// 检查单个成就
        /// </summary>
        private async Task CheckSingleAchievementAsync(int userId, Achievement achievement, GamificationUserStats userStats)
        {
            try
            {
                // 检查用户是否已经获得此成就
                var userAchievement = await _context.UserAchievements
                    .FirstOrDefaultAsync(ua => ua.UserId == userId && ua.AchievementId == achievement.Id);

                if (userAchievement?.IsCompleted == true)
                {
                    return; // 已经完成的成就跳过
                }

                // 获取当前进度值
                var currentValue = GetCurrentValueByMetric(achievement.Metric, userStats);

                // 如果用户成就记录不存在，创建一个
                if (userAchievement == null)
                {
                    userAchievement = new UserAchievement
                    {
                        UserId = userId,
                        AchievementId = achievement.Id,
                        CurrentValue = currentValue,
                        AchievedAt = DateTime.Now,
                        IsCompleted = false,
                        IsNotified = false
                    };
                    _context.UserAchievements.Add(userAchievement);
                }
                else
                {
                    userAchievement.CurrentValue = currentValue;
                }

                // 检查是否达成成就
                if (currentValue >= achievement.RequiredValue && !userAchievement.IsCompleted)
                {
                    userAchievement.IsCompleted = true;
                    userAchievement.AchievedAt = DateTime.Now;

                    // 发放成就奖励
                    if (achievement.PointsReward > 0 || achievement.CoinsReward > 0 || achievement.DiamondsReward > 0)
                    {
                        await _gamificationService.AddRewardAsync(userId, new Models.Dtos.Gamification.RewardDto
                        {
                            Points = achievement.PointsReward,
                            Coins = achievement.CoinsReward,
                            Diamonds = achievement.DiamondsReward,
                            ActionType = "ACHIEVEMENT_UNLOCKED",
                            Description = $"解锁成就: {achievement.Name}",
                            ReferenceId = achievement.Id.ToString()
                        });
                    }

                    _logger.LogInformation("用户 {UserId} 解锁成就: {AchievementName}", userId, achievement.Name);
                }

                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查成就 {AchievementCode} 失败", achievement.Code);
            }
        }

        /// <summary>
        /// 根据指标获取当前值
        /// </summary>
        private int GetCurrentValueByMetric(string metric, GamificationUserStats userStats)
        {
            return metric switch
            {
                AchievementMetrics.POINTS => userStats.PointsBalance,
                AchievementMetrics.LEVEL => userStats.CurrentLevel,
                AchievementMetrics.TASKS_CREATED => userStats.CreatedTasksCount,
                AchievementMetrics.TASKS_COMPLETED => userStats.CompletedTasksCount,
                AchievementMetrics.TASKS_CLAIMED => userStats.ClaimedTasksCount,
                // 注释掉不存在的字段，这些功能将在后续版本中实现
                // AchievementMetrics.FAULTS_RECORDED => userStats.FaultsRecordedCount,
                // AchievementMetrics.MAINTENANCE_CREATED => userStats.MaintenanceCreatedCount,
                // AchievementMetrics.ASSETS_UPDATED => userStats.AssetsUpdatedCount,
                // AchievementMetrics.RETURN_FACTORY => userStats.ReturnFactoryCount,
                _ => 0
            };
        }

        /// <summary>
        /// 获取用户成就列表
        /// </summary>
        public async Task<List<UserAchievementDto>> GetUserAchievementsAsync(int userId)
        {
            try
            {
                var userAchievements = await _context.UserAchievements
                    .Include(ua => ua.Achievement)
                    .Where(ua => ua.UserId == userId)
                    .OrderByDescending(ua => ua.IsCompleted)
                    .ThenBy(ua => ua.Achievement.SortOrder)
                    .Select(ua => new UserAchievementDto
                    {
                        Id = ua.Id,
                        AchievementId = ua.AchievementId,
                        Code = ua.Achievement.Code,
                        Name = ua.Achievement.Name,
                        Description = ua.Achievement.Description,
                        Icon = ua.Achievement.Icon,
                        Level = ua.Achievement.Level,
                        RequiredValue = ua.Achievement.RequiredValue,
                        CurrentValue = ua.CurrentValue,
                        IsCompleted = ua.IsCompleted,
                        AchievedAt = ua.AchievedAt,
                        Progress = ua.Achievement.RequiredValue > 0 
                            ? Math.Min(100, (ua.CurrentValue * 100) / ua.Achievement.RequiredValue) 
                            : 0,
                        RewardPoints = ua.Achievement.PointsReward,
                        RewardCoins = ua.Achievement.CoinsReward,
                        RewardDiamonds = ua.Achievement.DiamondsReward
                    })
                    .ToListAsync();

                return userAchievements;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户 {UserId} 成就列表失败", userId);
                return new List<UserAchievementDto>();
            }
        }

        /// <summary>
        /// 获取成就统计
        /// </summary>
        public async Task<AchievementStatsDto> GetAchievementStatsAsync(int userId)
        {
            try
            {
                var stats = await _context.UserAchievements
                    .Where(ua => ua.UserId == userId)
                    .GroupBy(ua => ua.UserId)
                    .Select(g => new AchievementStatsDto
                    {
                        TotalAchievements = g.Count(),
                        CompletedAchievements = g.Count(ua => ua.IsCompleted),
                        InProgressAchievements = g.Count(ua => !ua.IsCompleted && ua.CurrentValue > 0),
                        CompletionRate = g.Count() > 0 ? (g.Count(ua => ua.IsCompleted) * 100) / g.Count() : 0
                    })
                    .FirstOrDefaultAsync();

                return stats ?? new AchievementStatsDto();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户 {UserId} 成就统计失败", userId);
                return new AchievementStatsDto();
            }
        }

        /// <summary>
        /// 初始化用户成就进度
        /// </summary>
        public async Task InitializeUserAchievementsAsync(int userId)
        {
            try
            {
                var achievements = await _context.Achievements
                    .Where(a => a.IsActive)
                    .ToListAsync();

                var userStats = await _context.GamificationUserStats
                    .FirstOrDefaultAsync(s => s.CoreUserId == userId);

                if (userStats == null)
                {
                    _logger.LogWarning("用户 {UserId} 的游戏化统计不存在，无法初始化成就", userId);
                    return;
                }

                foreach (var achievement in achievements)
                {
                    var existingUserAchievement = await _context.UserAchievements
                        .FirstOrDefaultAsync(ua => ua.UserId == userId && ua.AchievementId == achievement.Id);

                    if (existingUserAchievement == null)
                    {
                        var currentValue = GetCurrentValueByMetric(achievement.Metric, userStats);
                        var userAchievement = new UserAchievement
                        {
                            UserId = userId,
                            AchievementId = achievement.Id,
                            CurrentValue = currentValue,
                            IsCompleted = currentValue >= achievement.RequiredValue,
                            AchievedAt = DateTime.Now
                        };

                        _context.UserAchievements.Add(userAchievement);
                    }
                }

                await _context.SaveChangesAsync();
                _logger.LogInformation("用户 {UserId} 成就进度初始化完成", userId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "初始化用户 {UserId} 成就进度失败", userId);
            }
        }
    }
}
