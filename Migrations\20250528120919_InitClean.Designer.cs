﻿// <auto-generated />
using System;
using ItAssetsSystem.Infrastructure.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace ItAssetsSystem.Migrations
{
    [DbContext(typeof(AppDbContext))]
    [Migration("20250528120919_InitClean")]
    partial class InitClean
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "6.0.7")
                .HasAnnotation("Relational:MaxIdentifierLength", 64);

            modelBuilder.Entity("ItAssetsSystem.Domain.Entities.Notes.QuickMemo", b =>
                {
                    b.Property<string>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(255)")
                        .HasColumnName("id");

                    b.Property<string>("CategoryId")
                        .HasColumnType("varchar(255)")
                        .HasColumnName("category_id");

                    b.Property<string>("Color")
                        .HasMaxLength(7)
                        .HasColumnType("varchar(7)")
                        .HasColumnName("color");

                    b.Property<string>("Content")
                        .HasColumnType("TEXT")
                        .HasColumnName("content");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("created_at");

                    b.Property<bool>("IsPinned")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("is_pinned");

                    b.Property<int>("PositionXDb")
                        .HasColumnType("int")
                        .HasColumnName("PositionX");

                    b.Property<int>("PositionYDb")
                        .HasColumnType("int")
                        .HasColumnName("PositionY");

                    b.Property<int>("SizeHeight")
                        .HasColumnType("int")
                        .HasColumnName("SizeHeight");

                    b.Property<int>("SizeWidth")
                        .HasColumnType("int")
                        .HasColumnName("SizeWidth");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("varchar(200)")
                        .HasColumnName("title");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("updated_at");

                    b.Property<int>("UserId")
                        .HasColumnType("int")
                        .HasColumnName("user_id");

                    b.Property<int>("ZIndex")
                        .HasColumnType("int")
                        .HasColumnName("ZIndex");

                    b.HasKey("Id");

                    b.HasIndex("CategoryId");

                    b.HasIndex("UserId")
                        .HasDatabaseName("ix_quick_memos_user_id");

                    b.HasIndex("UserId", "CategoryId")
                        .HasDatabaseName("ix_quick_memos_user_category");

                    b.HasIndex("UserId", "IsPinned", "UpdatedAt")
                        .HasDatabaseName("ix_quick_memos_user_pinned_updated");

                    b.ToTable("quick_memos", (string)null);
                });

            modelBuilder.Entity("ItAssetsSystem.Domain.Entities.Notes.QuickMemoCategory", b =>
                {
                    b.Property<string>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("varchar(255)")
                        .HasColumnName("id");

                    b.Property<string>("Color")
                        .HasMaxLength(7)
                        .HasColumnType("varchar(7)")
                        .HasColumnName("color");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("created_at");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)")
                        .HasColumnName("name");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("updated_at");

                    b.Property<int>("UserId")
                        .HasColumnType("int")
                        .HasColumnName("user_id");

                    b.HasKey("Id");

                    b.HasIndex("UserId", "Name")
                        .IsUnique()
                        .HasDatabaseName("ix_quick_memo_categories_user_name");

                    b.ToTable("quick_memo_categories", (string)null);
                });

            modelBuilder.Entity("ItAssetsSystem.Domain.Entities.SparePart", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    b.Property<string>("Brand")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)")
                        .HasColumnName("brand");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasColumnName("code");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("created_at");

                    b.Property<long?>("LocationId")
                        .HasColumnType("bigint")
                        .HasColumnName("location_id");

                    b.Property<int>("MinStock")
                        .HasColumnType("int")
                        .HasColumnName("min_threshold");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)")
                        .HasColumnName("name");

                    b.Property<decimal?>("Price")
                        .HasPrecision(10, 2)
                        .HasColumnType("decimal(10,2)")
                        .HasColumnName("purchase_price");

                    b.Property<string>("Remarks")
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)")
                        .HasColumnName("notes");

                    b.Property<string>("Specification")
                        .HasMaxLength(200)
                        .HasColumnType("varchar(200)")
                        .HasColumnName("spec");

                    b.Property<int>("StockQuantity")
                        .HasColumnType("int")
                        .HasColumnName("quantity");

                    b.Property<long>("TypeId")
                        .HasColumnType("bigint")
                        .HasColumnName("type_id");

                    b.Property<string>("Unit")
                        .HasMaxLength(20)
                        .HasColumnType("varchar(20)")
                        .HasColumnName("unit");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("updated_at");

                    b.Property<int>("WarningThreshold")
                        .HasColumnType("int")
                        .HasColumnName("warning_threshold");

                    b.HasKey("Id");

                    b.HasIndex("Code")
                        .IsUnique();

                    b.HasIndex("LocationId");

                    b.HasIndex("StockQuantity");

                    b.HasIndex("TypeId");

                    b.ToTable("spare_parts", (string)null);
                });

            modelBuilder.Entity("ItAssetsSystem.Domain.Entities.SparePartLocation", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    b.Property<string>("Area")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("varchar(20)")
                        .HasColumnName("area");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasColumnName("code");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("created_at");

                    b.Property<string>("Description")
                        .HasMaxLength(200)
                        .HasColumnType("varchar(200)")
                        .HasColumnName("description");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)")
                        .HasColumnName("is_active");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)")
                        .HasColumnName("name");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("updated_at");

                    b.HasKey("Id");

                    b.HasIndex("Area");

                    b.HasIndex("Code")
                        .IsUnique();

                    b.ToTable("spare_part_locations", (string)null);
                });

            modelBuilder.Entity("ItAssetsSystem.Domain.Entities.SparePartTransaction", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    b.Property<string>("BatchNumber")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasColumnName("batch_number");

                    b.Property<bool>("IsSystemGenerated")
                        .HasColumnType("tinyint(1)")
                        .HasColumnName("is_system_generated");

                    b.Property<long>("LocationId")
                        .HasColumnType("bigint")
                        .HasColumnName("location_id");

                    b.Property<DateTime>("OperationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("transaction_time");

                    b.Property<int>("OperatorUserId")
                        .HasColumnType("int")
                        .HasColumnName("user_id");

                    b.Property<long>("PartId")
                        .HasColumnType("bigint")
                        .HasColumnName("part_id");

                    b.Property<int>("Quantity")
                        .HasColumnType("int")
                        .HasColumnName("quantity");

                    b.Property<string>("Reason")
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)")
                        .HasColumnName("reason");

                    b.Property<byte>("ReasonType")
                        .HasColumnType("tinyint unsigned")
                        .HasColumnName("reason_type");

                    b.Property<string>("ReferenceNumber")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)")
                        .HasColumnName("reference");

                    b.Property<int?>("RelatedAssetId")
                        .HasColumnType("int")
                        .HasColumnName("related_asset_id");

                    b.Property<int?>("RelatedFaultId")
                        .HasColumnType("int")
                        .HasColumnName("related_fault_id");

                    b.Property<int>("StockAfter")
                        .HasColumnType("int")
                        .HasColumnName("stock_after");

                    b.Property<byte>("Type")
                        .HasColumnType("tinyint unsigned")
                        .HasColumnName("type");

                    b.HasKey("Id");

                    b.HasIndex("BatchNumber");

                    b.HasIndex("LocationId");

                    b.HasIndex("OperationTime");

                    b.HasIndex("PartId");

                    b.HasIndex("Type");

                    b.ToTable("spare_part_transactions", (string)null);
                });

            modelBuilder.Entity("ItAssetsSystem.Domain.Entities.SparePartType", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("varchar(20)")
                        .HasColumnName("code");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("created_at");

                    b.Property<string>("Description")
                        .HasMaxLength(200)
                        .HasColumnType("varchar(200)")
                        .HasColumnName("description");

                    b.Property<int>("Level")
                        .HasColumnType("int")
                        .HasColumnName("level");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasColumnName("name");

                    b.Property<long?>("ParentId")
                        .HasColumnType("bigint")
                        .HasColumnName("parent_id");

                    b.Property<string>("Path")
                        .HasMaxLength(255)
                        .HasColumnType("varchar(255)")
                        .HasColumnName("path");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("updated_at");

                    b.HasKey("Id");

                    b.HasIndex("Code")
                        .IsUnique();

                    b.HasIndex("ParentId");

                    b.HasIndex("Path");

                    b.ToTable("spare_part_types", (string)null);
                });

            modelBuilder.Entity("ItAssetsSystem.Domain.Entities.Tasks.Attachment", b =>
                {
                    b.Property<long>("AttachmentId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("AttachmentId");

                    b.Property<long?>("CommentId")
                        .HasColumnType("bigint")
                        .HasColumnName("CommentId");

                    b.Property<DateTime>("CreationTimestamp")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreationTimestamp");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("FileName");

                    b.Property<string>("FilePath")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("FilePath");

                    b.Property<long>("FileSize")
                        .HasColumnType("bigint")
                        .HasColumnName("FileSize");

                    b.Property<string>("FileType")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("FileType");

                    b.Property<bool>("IsPreviewable")
                        .HasColumnType("tinyint(1)")
                        .HasColumnName("IsPreviewable");

                    b.Property<string>("StorageType")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("StorageType");

                    b.Property<string>("StoredFileName")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("StoredFileName");

                    b.Property<long?>("TaskId")
                        .HasColumnType("bigint")
                        .HasColumnName("TaskId");

                    b.Property<int>("UploaderUserId")
                        .HasColumnType("int")
                        .HasColumnName("UploaderUserId");

                    b.HasKey("AttachmentId");

                    b.HasIndex("CommentId");

                    b.HasIndex("TaskId");

                    b.HasIndex("UploaderUserId");

                    b.ToTable("attachments", (string)null);
                });

            modelBuilder.Entity("ItAssetsSystem.Domain.Entities.Tasks.Comment", b =>
                {
                    b.Property<long>("CommentId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("CommentId");

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("Content");

                    b.Property<DateTime>("CreationTimestamp")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreationTimestamp");

                    b.Property<bool>("IsEdited")
                        .HasColumnType("tinyint(1)")
                        .HasColumnName("IsEdited");

                    b.Property<bool>("IsPinned")
                        .HasColumnType("tinyint(1)")
                        .HasColumnName("IsPinned");

                    b.Property<DateTime>("LastUpdatedTimestamp")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("LastUpdatedTimestamp");

                    b.Property<string>("MentionedUserIds")
                        .HasColumnType("longtext")
                        .HasColumnName("MentionedUserIds");

                    b.Property<long?>("ParentCommentId")
                        .HasColumnType("bigint")
                        .HasColumnName("ParentCommentId");

                    b.Property<long>("TaskId")
                        .HasColumnType("bigint")
                        .HasColumnName("TaskId");

                    b.Property<int>("UserId")
                        .HasColumnType("int")
                        .HasColumnName("UserId");

                    b.HasKey("CommentId");

                    b.HasIndex("ParentCommentId");

                    b.HasIndex("TaskId");

                    b.HasIndex("UserId");

                    b.ToTable("comments", (string)null);
                });

            modelBuilder.Entity("ItAssetsSystem.Domain.Entities.Tasks.PdcaPlan", b =>
                {
                    b.Property<long>("PdcaPlanId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("pdca_plan_id");

                    b.Property<string>("ActAction")
                        .HasColumnType("longtext")
                        .HasColumnName("act_action");

                    b.Property<string>("CheckResult")
                        .HasColumnType("longtext")
                        .HasColumnName("check_result");

                    b.Property<decimal>("CompletionRate")
                        .HasColumnType("decimal(65,30)")
                        .HasColumnName("completion_rate");

                    b.Property<DateTime>("CreationTimestamp")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("creation_timestamp");

                    b.Property<int>("CreatorUserId")
                        .HasColumnType("int")
                        .HasColumnName("creator_user_id");

                    b.Property<string>("DoRecord")
                        .HasColumnType("longtext")
                        .HasColumnName("do_record");

                    b.Property<string>("Goal")
                        .HasColumnType("longtext")
                        .HasColumnName("goal");

                    b.Property<DateTime>("LastUpdatedTimestamp")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("last_updated_timestamp");

                    b.Property<string>("Notes")
                        .HasColumnType("longtext")
                        .HasColumnName("notes");

                    b.Property<string>("PlanContent")
                        .HasColumnType("longtext")
                        .HasColumnName("plan_content");

                    b.Property<int>("ResponsiblePersonId")
                        .HasColumnType("int")
                        .HasColumnName("responsible_person_id");

                    b.Property<string>("Stage")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("stage");

                    b.Property<int>("Status")
                        .HasColumnType("int")
                        .HasColumnName("status");

                    b.Property<long>("TaskId")
                        .HasColumnType("bigint")
                        .HasColumnName("TaskId");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("title");

                    b.HasKey("PdcaPlanId");

                    b.HasIndex("CreatorUserId");

                    b.HasIndex("ResponsiblePersonId");

                    b.HasIndex("TaskId")
                        .IsUnique();

                    b.ToTable("pdcaplans", (string)null);
                });

            modelBuilder.Entity("ItAssetsSystem.Domain.Entities.Tasks.PeriodicTaskSchedule", b =>
                {
                    b.Property<long>("PeriodicTaskScheduleId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("periodic_task_schedule_id");

                    b.Property<DateTime>("CreationTimestamp")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("creation_timestamp");

                    b.Property<int>("CreatorUserId")
                        .HasColumnType("int")
                        .HasColumnName("creator_user_id");

                    b.Property<string>("CronExpression")
                        .HasColumnType("longtext")
                        .HasColumnName("cron_expression");

                    b.Property<int?>("DayOfMonth")
                        .HasColumnType("int")
                        .HasColumnName("day_of_month");

                    b.Property<string>("DayOfWeekForMonth")
                        .HasColumnType("longtext")
                        .HasColumnName("day_of_week_for_month");

                    b.Property<string>("DaysOfWeek")
                        .HasColumnType("longtext")
                        .HasColumnName("days_of_week");

                    b.Property<int>("DefaultPoints")
                        .HasColumnType("int")
                        .HasColumnName("default_points");

                    b.Property<string>("Description")
                        .HasColumnType("longtext")
                        .HasColumnName("description");

                    b.Property<string>("EndConditionType")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("end_condition_type");

                    b.Property<DateTime?>("EndDate")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("end_date");

                    b.Property<string>("LastError")
                        .HasColumnType("longtext")
                        .HasColumnName("last_error");

                    b.Property<DateTime?>("LastGeneratedTimestamp")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("last_generated_timestamp");

                    b.Property<DateTime>("LastUpdatedTimestamp")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("last_updated_timestamp");

                    b.Property<int?>("MonthOfYear")
                        .HasColumnType("int")
                        .HasColumnName("month_of_year");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("name");

                    b.Property<DateTime>("NextGenerationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("next_generation_time");

                    b.Property<int>("OccurrencesGenerated")
                        .HasColumnType("int")
                        .HasColumnName("occurrences_generated");

                    b.Property<int>("RecurrenceInterval")
                        .HasColumnType("int")
                        .HasColumnName("recurrence_interval");

                    b.Property<string>("RecurrenceType")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("recurrence_type");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("start_date");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("status");

                    b.Property<long>("TemplateTaskId")
                        .HasColumnType("bigint")
                        .HasColumnName("template_task_id");

                    b.Property<int?>("TotalOccurrences")
                        .HasColumnType("int")
                        .HasColumnName("total_occurrences");

                    b.Property<string>("WeekOfMonth")
                        .HasColumnType("longtext")
                        .HasColumnName("week_of_month");

                    b.HasKey("PeriodicTaskScheduleId");

                    b.HasIndex("CreatorUserId");

                    b.HasIndex("TemplateTaskId");

                    b.ToTable("periodictaskschedules", (string)null);
                });

            modelBuilder.Entity("ItAssetsSystem.Domain.Entities.Tasks.Task", b =>
                {
                    b.Property<long>("TaskId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("TaskId");

                    b.Property<DateTime?>("ActualEndDate")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("ActualEndDate");

                    b.Property<DateTime?>("ActualStartDate")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("ActualStartDate");

                    b.Property<int?>("AssetId")
                        .HasColumnType("int")
                        .HasColumnName("AssetId");

                    b.Property<int?>("AssigneeUserId")
                        .HasColumnType("int")
                        .HasColumnName("AssigneeUserId");

                    b.Property<DateTime>("CreationTimestamp")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreationTimestamp");

                    b.Property<int>("CreatorUserId")
                        .HasColumnType("int")
                        .HasColumnName("CreatorUserId");

                    b.Property<string>("Description")
                        .HasColumnType("longtext")
                        .HasColumnName("Description");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsOverdueAcknowledged")
                        .HasColumnType("tinyint(1)")
                        .HasColumnName("IsOverdueAcknowledged");

                    b.Property<DateTime>("LastUpdatedTimestamp")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("LastUpdatedTimestamp");

                    b.Property<int?>("LocationId")
                        .HasColumnType("int")
                        .HasColumnName("LocationId");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("Name");

                    b.Property<string>("PDCAStage")
                        .HasColumnType("longtext")
                        .HasColumnName("PDCAStage");

                    b.Property<long?>("ParentTaskId")
                        .HasColumnType("bigint")
                        .HasColumnName("ParentTaskId");

                    b.Property<long?>("PeriodicTaskScheduleId")
                        .HasColumnType("bigint")
                        .HasColumnName("PeriodicTaskScheduleId");

                    b.Property<DateTime?>("PlanEndDate")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("PlanEndDate");

                    b.Property<DateTime?>("PlanStartDate")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("PlanStartDate");

                    b.Property<int>("Points")
                        .HasColumnType("int")
                        .HasColumnName("Points");

                    b.Property<long?>("PreviousInstanceTaskId")
                        .HasColumnType("bigint")
                        .HasColumnName("PreviousInstanceTaskId");

                    b.Property<string>("Priority")
                        .HasColumnType("longtext")
                        .HasColumnName("Priority");

                    b.Property<int>("Progress")
                        .HasColumnType("int")
                        .HasColumnName("Progress");

                    b.Property<long?>("ProjectId")
                        .HasColumnType("bigint")
                        .HasColumnName("ProjectId");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("Status");

                    b.Property<string>("TaskType")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("TaskType");

                    b.HasKey("TaskId");

                    b.HasIndex("AssetId");

                    b.HasIndex("AssigneeUserId");

                    b.HasIndex("CreatorUserId");

                    b.HasIndex("LocationId");

                    b.HasIndex("ParentTaskId");

                    b.HasIndex("PreviousInstanceTaskId")
                        .IsUnique();

                    b.ToTable("tasks", (string)null);
                });

            modelBuilder.Entity("ItAssetsSystem.Domain.Entities.Tasks.TaskAssignee", b =>
                {
                    b.Property<long>("TaskAssigneeId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("TaskAssigneeId");

                    b.Property<int>("AssignedByUserId")
                        .HasColumnType("int")
                        .HasColumnName("AssignedByUserId");

                    b.Property<DateTime>("AssignmentTimestamp")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("AssignmentTimestamp");

                    b.Property<string>("AssignmentType")
                        .HasColumnType("longtext")
                        .HasColumnName("AssignmentType");

                    b.Property<long>("TaskId")
                        .HasColumnType("bigint")
                        .HasColumnName("TaskId");

                    b.Property<int>("UserId")
                        .HasColumnType("int")
                        .HasColumnName("UserId");

                    b.HasKey("TaskAssigneeId");

                    b.HasIndex("AssignedByUserId");

                    b.HasIndex("TaskId");

                    b.HasIndex("UserId");

                    b.ToTable("taskassignees", (string)null);
                });

            modelBuilder.Entity("ItAssetsSystem.Domain.Entities.Tasks.TaskHistory", b =>
                {
                    b.Property<long>("TaskHistoryId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasColumnName("TaskHistoryId");

                    b.Property<string>("ActionType")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("ActionType");

                    b.Property<long?>("AttachmentId")
                        .HasColumnType("bigint")
                        .HasColumnName("AttachmentId");

                    b.Property<long?>("CommentId")
                        .HasColumnType("bigint")
                        .HasColumnName("CommentId");

                    b.Property<string>("Description")
                        .HasColumnType("longtext")
                        .HasColumnName("Description");

                    b.Property<string>("FieldName")
                        .HasColumnType("longtext")
                        .HasColumnName("FieldName");

                    b.Property<string>("NewValue")
                        .HasColumnType("longtext")
                        .HasColumnName("NewValue");

                    b.Property<string>("OldValue")
                        .HasColumnType("longtext")
                        .HasColumnName("OldValue");

                    b.Property<long>("TaskId")
                        .HasColumnType("bigint")
                        .HasColumnName("TaskId");

                    b.Property<DateTime>("Timestamp")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("Timestamp");

                    b.Property<int?>("UserId")
                        .HasColumnType("int")
                        .HasColumnName("UserId");

                    b.HasKey("TaskHistoryId");

                    b.HasIndex("AttachmentId");

                    b.HasIndex("CommentId");

                    b.HasIndex("TaskId");

                    b.HasIndex("UserId");

                    b.ToTable("taskhistory", (string)null);
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.Asset", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    b.Property<string>("AssetCode")
                        .HasColumnType("longtext");

                    b.Property<int>("AssetTypeId")
                        .HasColumnType("int");

                    b.Property<string>("Brand")
                        .HasColumnType("longtext");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<int?>("DepartmentId")
                        .HasColumnType("int");

                    b.Property<string>("FinancialCode")
                        .HasColumnType("longtext");

                    b.Property<int?>("LocationId")
                        .HasColumnType("int");

                    b.Property<string>("Model")
                        .HasColumnType("longtext");

                    b.Property<string>("Name")
                        .HasColumnType("longtext");

                    b.Property<string>("Notes")
                        .HasColumnType("longtext");

                    b.Property<decimal?>("Price")
                        .HasColumnType("decimal(65,30)");

                    b.Property<DateTime?>("PurchaseDate")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("SerialNumber")
                        .HasColumnType("longtext");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("WarrantyExpireDate")
                        .HasColumnType("datetime(6)");

                    b.HasKey("Id");

                    b.HasIndex("AssetTypeId");

                    b.HasIndex("DepartmentId");

                    b.HasIndex("LocationId");

                    b.ToTable("assets", (string)null);
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.AssetHistory", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    b.Property<int>("AssetId")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Description")
                        .HasColumnType("longtext");

                    b.Property<DateTime>("OperationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<int>("OperationType")
                        .HasColumnType("int");

                    b.Property<int>("OperatorId")
                        .HasColumnType("int");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime(6)");

                    b.HasKey("Id");

                    b.HasIndex("AssetId");

                    b.HasIndex("OperatorId");

                    b.ToTable("AssetHistories");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.AssetReceive", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    b.Property<int>("AssetId")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<int?>("CreatedBy")
                        .HasColumnType("int");

                    b.Property<string>("InspectionResult")
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)");

                    b.Property<int>("LocationId")
                        .HasColumnType("int");

                    b.Property<int?>("PurchaseOrderId")
                        .HasColumnType("int");

                    b.Property<DateTime>("ReceiveDate")
                        .HasColumnType("datetime(6)");

                    b.Property<int>("ReceiveType")
                        .HasColumnType("int");

                    b.Property<int>("ReceiverId")
                        .HasColumnType("int");

                    b.Property<string>("Remarks")
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)");

                    b.Property<int?>("ReturnToFactoryId")
                        .HasColumnType("int");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<int?>("UpdatedBy")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("AssetId");

                    b.HasIndex("LocationId");

                    b.HasIndex("PurchaseOrderId");

                    b.HasIndex("ReceiverId");

                    b.HasIndex("ReturnToFactoryId");

                    b.ToTable("AssetReceives");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.AssetType", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    b.Property<string>("Code")
                        .HasColumnType("longtext");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Description")
                        .HasColumnType("longtext");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Name")
                        .HasColumnType("longtext");

                    b.Property<int?>("ParentId")
                        .HasColumnType("int");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime(6)");

                    b.HasKey("Id");

                    b.HasIndex("ParentId");

                    b.ToTable("assettypes", (string)null);
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.AuditLog", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    b.Property<int>("ActionType")
                        .HasColumnType("int");

                    b.Property<string>("Content")
                        .HasColumnType("longtext");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Function")
                        .HasColumnType("longtext");

                    b.Property<string>("IPAddress")
                        .HasColumnType("longtext");

                    b.Property<string>("Module")
                        .HasColumnType("longtext");

                    b.Property<int>("Result")
                        .HasColumnType("int");

                    b.Property<string>("Target")
                        .HasColumnType("longtext");

                    b.Property<string>("TargetId")
                        .HasColumnType("longtext");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<int?>("UserId")
                        .HasColumnType("int");

                    b.Property<string>("Username")
                        .HasColumnType("longtext");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("AuditLogs");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.Department", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    b.Property<string>("Code")
                        .HasColumnType("longtext");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<int?>("DeputyManagerId")
                        .HasColumnType("int");

                    b.Property<string>("Description")
                        .HasColumnType("longtext");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<int?>("ManagerId")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .HasColumnType("longtext");

                    b.Property<int?>("ParentId")
                        .HasColumnType("int");

                    b.Property<string>("Path")
                        .HasColumnType("longtext");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime(6)");

                    b.HasKey("Id");

                    b.HasIndex("ParentId");

                    b.ToTable("departments", (string)null);
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.FaultRecord", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    b.Property<int>("AssetId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("AssignTime")
                        .HasColumnType("datetime(6)");

                    b.Property<int?>("AssigneeId")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Description")
                        .HasColumnType("longtext");

                    b.Property<int>("FaultTypeId")
                        .HasColumnType("int");

                    b.Property<bool>("IsReturned")
                        .HasColumnType("tinyint(1)");

                    b.Property<int?>("LocationId")
                        .HasColumnType("int");

                    b.Property<string>("Notes")
                        .HasColumnType("longtext");

                    b.Property<DateTime>("ReportTime")
                        .HasColumnType("datetime(6)");

                    b.Property<int>("ReporterId")
                        .HasColumnType("int");

                    b.Property<string>("Resolution")
                        .HasColumnType("longtext");

                    b.Property<DateTime?>("ResolutionTime")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("ResponseTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("RootCause")
                        .HasColumnType("longtext");

                    b.Property<int>("Severity")
                        .HasColumnType("int");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<string>("Title")
                        .HasColumnType("longtext");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime(6)");

                    b.HasKey("Id");

                    b.HasIndex("AssetId");

                    b.HasIndex("AssigneeId");

                    b.HasIndex("FaultTypeId");

                    b.HasIndex("LocationId");

                    b.HasIndex("ReporterId");

                    b.ToTable("FaultRecords");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.FaultType", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    b.Property<string>("Code")
                        .HasColumnType("longtext");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Description")
                        .HasColumnType("longtext");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Name")
                        .HasColumnType("longtext");

                    b.Property<int?>("ParentId")
                        .HasColumnType("int");

                    b.Property<int>("SortOrder")
                        .HasColumnType("int");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime(6)");

                    b.HasKey("Id");

                    b.HasIndex("ParentId");

                    b.ToTable("FaultTypes");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.Location", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<int?>("DefaultDepartmentId")
                        .HasColumnType("int")
                        .HasColumnName("DefaultDepartmentId");

                    b.Property<string>("Description")
                        .HasMaxLength(200)
                        .HasColumnType("varchar(200)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<int?>("ManagerId")
                        .HasColumnType("int")
                        .HasColumnName("DefaultResponsiblePersonId");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<int?>("ParentId")
                        .HasColumnType("int");

                    b.Property<string>("Path")
                        .HasMaxLength(200)
                        .HasColumnType("varchar(200)");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime(6)");

                    b.HasKey("Id");

                    b.HasIndex("DefaultDepartmentId");

                    b.HasIndex("ManagerId");

                    b.HasIndex("ParentId");

                    b.ToTable("locations", (string)null);
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.LocationHistory", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("Id");

                    b.Property<int>("AssetId")
                        .HasColumnType("int")
                        .HasColumnName("AssetId");

                    b.Property<DateTime>("ChangeTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("ChangeTime");

                    b.Property<int>("ChangeType")
                        .HasColumnType("int")
                        .HasColumnName("ChangeType");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreatedAt");

                    b.Property<int>("NewLocationId")
                        .HasColumnType("int")
                        .HasColumnName("NewLocationId");

                    b.Property<string>("Notes")
                        .HasColumnType("longtext")
                        .HasColumnName("Notes");

                    b.Property<int?>("OldLocationId")
                        .HasColumnType("int")
                        .HasColumnName("OldLocationId");

                    b.Property<int>("OperatorId")
                        .HasColumnType("int")
                        .HasColumnName("OperatorId");

                    b.HasKey("Id");

                    b.HasIndex("AssetId");

                    b.HasIndex("NewLocationId");

                    b.HasIndex("OldLocationId");

                    b.HasIndex("OperatorId");

                    b.ToTable("locationhistories", (string)null);
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.LocationUser", b =>
                {
                    b.Property<int>("LocationId")
                        .HasColumnType("int")
                        .HasColumnName("location_id");

                    b.Property<int>("PersonnelId")
                        .HasColumnType("int")
                        .HasColumnName("personnel_id");

                    b.Property<int>("UserType")
                        .HasColumnType("int")
                        .HasColumnName("user_type");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("created_at");

                    b.Property<int?>("UserId")
                        .HasColumnType("int");

                    b.HasKey("LocationId", "PersonnelId", "UserType");

                    b.HasIndex("PersonnelId");

                    b.HasIndex("UserId");

                    b.ToTable("locationusers", (string)null);
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.MaintenanceOrder", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    b.Property<DateTime?>("ActualCompletionDate")
                        .HasColumnType("datetime(6)");

                    b.Property<int>("AssetId")
                        .HasColumnType("int");

                    b.Property<int>("AssigneeId")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<int?>("CreatedBy")
                        .HasColumnType("int");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)");

                    b.Property<int?>("FaultRecordId")
                        .HasColumnType("int");

                    b.Property<int>("LocationId")
                        .HasColumnType("int");

                    b.Property<decimal?>("MaintenanceCost")
                        .HasColumnType("decimal(65,30)");

                    b.Property<DateTime>("MaintenanceDate")
                        .HasColumnType("datetime(6)");

                    b.Property<int>("MaintenanceType")
                        .HasColumnType("int");

                    b.Property<string>("OrderNumber")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime?>("PlannedCompletionDate")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Remarks")
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)");

                    b.Property<string>("Result")
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<int?>("UpdatedBy")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("AssetId");

                    b.HasIndex("AssigneeId");

                    b.HasIndex("FaultRecordId");

                    b.HasIndex("LocationId");

                    b.ToTable("MaintenanceOrders");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.Menu", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    b.Property<string>("Code")
                        .HasColumnType("longtext");

                    b.Property<string>("Component")
                        .HasColumnType("longtext");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Icon")
                        .HasColumnType("longtext");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsVisible")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Name")
                        .HasColumnType("longtext");

                    b.Property<int?>("ParentId")
                        .HasColumnType("int");

                    b.Property<string>("Path")
                        .HasColumnType("longtext");

                    b.Property<int>("SortOrder")
                        .HasColumnType("int");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime(6)");

                    b.HasKey("Id");

                    b.HasIndex("ParentId");

                    b.ToTable("Menus");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.Permission", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("Description")
                        .HasColumnType("longtext");

                    b.Property<bool>("IsSystem")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.HasKey("Id");

                    b.HasIndex("Code")
                        .IsUnique();

                    b.ToTable("permissions", (string)null);
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.Personnel", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    b.Property<string>("Contact")
                        .HasColumnType("longtext")
                        .HasColumnName("contact");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("created_at");

                    b.Property<int?>("DepartmentId")
                        .HasColumnType("int")
                        .HasColumnName("department_id");

                    b.Property<string>("EmployeeCode")
                        .HasColumnType("longtext")
                        .HasColumnName("employee_code");

                    b.Property<string>("Name")
                        .HasColumnType("longtext")
                        .HasColumnName("name");

                    b.Property<string>("Position")
                        .HasColumnType("longtext")
                        .HasColumnName("position");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("updated_at");

                    b.HasKey("Id");

                    b.HasIndex("DepartmentId");

                    b.ToTable("personnel");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.PurchaseItem", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    b.Property<int>("AssetTypeId")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<bool>("IsAssetGenerated")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Name")
                        .HasColumnType("longtext");

                    b.Property<string>("Notes")
                        .HasColumnType("longtext");

                    b.Property<int>("PurchaseOrderId")
                        .HasColumnType("int");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<string>("Specification")
                        .HasColumnType("longtext");

                    b.Property<decimal>("TotalPrice")
                        .HasColumnType("decimal(65,30)");

                    b.Property<decimal>("UnitPrice")
                        .HasColumnType("decimal(65,30)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime(6)");

                    b.HasKey("Id");

                    b.HasIndex("AssetTypeId");

                    b.HasIndex("PurchaseOrderId");

                    b.ToTable("PurchaseItems");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.PurchaseOrder", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    b.Property<DateTime?>("ActualDeliveryDate")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime?>("ExpectedDeliveryDate")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Notes")
                        .HasColumnType("longtext");

                    b.Property<string>("OrderNumber")
                        .HasColumnType("longtext");

                    b.Property<int>("RequesterId")
                        .HasColumnType("int");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<int>("SupplierId")
                        .HasColumnType("int");

                    b.Property<decimal>("TotalAmount")
                        .HasColumnType("decimal(65,30)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime(6)");

                    b.HasKey("Id");

                    b.HasIndex("RequesterId");

                    b.HasIndex("SupplierId");

                    b.ToTable("PurchaseOrders");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.RefreshToken", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    b.Property<DateTime>("AddedDate")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime>("ExpiryDate")
                        .HasColumnType("datetime(6)");

                    b.Property<bool>("IsRevoked")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsUsed")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("JwtId")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("Token")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("RefreshTokens");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.ReturnToFactory", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    b.Property<DateTime?>("ActualReturnDate")
                        .HasColumnType("datetime(6)");

                    b.Property<int>("AssetId")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<int?>("CreatedBy")
                        .HasColumnType("int");

                    b.Property<DateTime?>("ExpectedReturnDate")
                        .HasColumnType("datetime(6)");

                    b.Property<int>("OperatorId")
                        .HasColumnType("int");

                    b.Property<string>("Reason")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)");

                    b.Property<string>("Remarks")
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)");

                    b.Property<string>("Result")
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)");

                    b.Property<DateTime>("ReturnDate")
                        .HasColumnType("datetime(6)");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<int>("SupplierId")
                        .HasColumnType("int");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<int?>("UpdatedBy")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("AssetId");

                    b.HasIndex("OperatorId");

                    b.HasIndex("SupplierId");

                    b.ToTable("ReturnToFactories");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.Role", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Description")
                        .HasColumnType("longtext");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime(6)");

                    b.HasKey("Id");

                    b.ToTable("roles", (string)null);
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.RoleMenu", b =>
                {
                    b.Property<int>("RoleId")
                        .HasColumnType("int");

                    b.Property<int>("MenuId")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime(6)");

                    b.HasKey("RoleId", "MenuId");

                    b.HasIndex("MenuId");

                    b.ToTable("rolemenus", (string)null);
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.RolePermission", b =>
                {
                    b.Property<int>("RoleId")
                        .HasColumnType("int");

                    b.Property<int>("PermissionId")
                        .HasColumnType("int");

                    b.HasKey("RoleId", "PermissionId");

                    b.HasIndex("PermissionId");

                    b.ToTable("rolepermissions", (string)null);
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.Supplier", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    b.Property<string>("Address")
                        .HasColumnType("longtext");

                    b.Property<string>("Code")
                        .HasColumnType("longtext");

                    b.Property<string>("ContactEmail")
                        .HasColumnType("longtext");

                    b.Property<string>("ContactPerson")
                        .HasColumnType("longtext");

                    b.Property<string>("ContactPhone")
                        .HasColumnType("longtext");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Name")
                        .HasColumnType("longtext");

                    b.Property<string>("Notes")
                        .HasColumnType("longtext");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime(6)");

                    b.HasKey("Id");

                    b.ToTable("Suppliers");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.User", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    b.Property<string>("Avatar")
                        .HasColumnType("longtext")
                        .HasColumnName("Avatar");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreatedAt");

                    b.Property<int?>("DefaultRoleId")
                        .HasColumnType("int")
                        .HasColumnName("DefaultRoleId");

                    b.Property<int?>("DepartmentId")
                        .HasColumnType("int")
                        .HasColumnName("DepartmentId");

                    b.Property<string>("Email")
                        .HasColumnType("varchar(255)")
                        .HasColumnName("Email");

                    b.Property<int>("Gender")
                        .HasColumnType("int")
                        .HasColumnName("Gender");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)")
                        .HasColumnName("IsActive");

                    b.Property<DateTime?>("LastLoginAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("LastLoginAt");

                    b.Property<string>("Mobile")
                        .HasColumnType("longtext")
                        .HasColumnName("Mobile");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasColumnName("Name");

                    b.Property<string>("PasswordHash")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("PasswordHash");

                    b.Property<string>("Position")
                        .HasColumnType("longtext")
                        .HasColumnName("Position");

                    b.Property<string>("SecurityStamp")
                        .HasColumnType("longtext")
                        .HasColumnName("SecurityStamp");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("UpdatedAt");

                    b.Property<string>("Username")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)")
                        .HasColumnName("Username");

                    b.HasKey("Id");

                    b.HasIndex("DefaultRoleId");

                    b.HasIndex("DepartmentId");

                    b.HasIndex("Email")
                        .IsUnique();

                    b.HasIndex("Username")
                        .IsUnique();

                    b.ToTable("users", (string)null);
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.UserRole", b =>
                {
                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.Property<int>("RoleId")
                        .HasColumnType("int");

                    b.HasKey("UserId", "RoleId");

                    b.HasIndex("RoleId");

                    b.ToTable("userroles", (string)null);
                });

            modelBuilder.Entity("ItAssetsSystem.Domain.Entities.Notes.QuickMemo", b =>
                {
                    b.HasOne("ItAssetsSystem.Domain.Entities.Notes.QuickMemoCategory", "Category")
                        .WithMany("QuickMemos")
                        .HasForeignKey("CategoryId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("ItAssetsSystem.Models.Entities.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Category");

                    b.Navigation("User");
                });

            modelBuilder.Entity("ItAssetsSystem.Domain.Entities.Notes.QuickMemoCategory", b =>
                {
                    b.HasOne("ItAssetsSystem.Models.Entities.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("ItAssetsSystem.Domain.Entities.SparePart", b =>
                {
                    b.HasOne("ItAssetsSystem.Domain.Entities.SparePartLocation", "Location")
                        .WithMany("SpareParts")
                        .HasForeignKey("LocationId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("ItAssetsSystem.Domain.Entities.SparePartType", "Type")
                        .WithMany("SpareParts")
                        .HasForeignKey("TypeId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Location");

                    b.Navigation("Type");
                });

            modelBuilder.Entity("ItAssetsSystem.Domain.Entities.SparePartTransaction", b =>
                {
                    b.HasOne("ItAssetsSystem.Domain.Entities.SparePartLocation", "Location")
                        .WithMany("Transactions")
                        .HasForeignKey("LocationId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ItAssetsSystem.Domain.Entities.SparePart", "Part")
                        .WithMany("Transactions")
                        .HasForeignKey("PartId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Location");

                    b.Navigation("Part");
                });

            modelBuilder.Entity("ItAssetsSystem.Domain.Entities.SparePartType", b =>
                {
                    b.HasOne("ItAssetsSystem.Domain.Entities.SparePartType", "Parent")
                        .WithMany("Children")
                        .HasForeignKey("ParentId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Parent");
                });

            modelBuilder.Entity("ItAssetsSystem.Domain.Entities.Tasks.Attachment", b =>
                {
                    b.HasOne("ItAssetsSystem.Domain.Entities.Tasks.Comment", "Comment")
                        .WithMany("Attachments")
                        .HasForeignKey("CommentId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("ItAssetsSystem.Domain.Entities.Tasks.Task", "Task")
                        .WithMany("Attachments")
                        .HasForeignKey("TaskId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("ItAssetsSystem.Models.Entities.User", "UploaderUser")
                        .WithMany()
                        .HasForeignKey("UploaderUserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Comment");

                    b.Navigation("Task");

                    b.Navigation("UploaderUser");
                });

            modelBuilder.Entity("ItAssetsSystem.Domain.Entities.Tasks.Comment", b =>
                {
                    b.HasOne("ItAssetsSystem.Domain.Entities.Tasks.Comment", "ParentComment")
                        .WithMany("Replies")
                        .HasForeignKey("ParentCommentId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("ItAssetsSystem.Domain.Entities.Tasks.Task", "Task")
                        .WithMany("Comments")
                        .HasForeignKey("TaskId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ItAssetsSystem.Models.Entities.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("ParentComment");

                    b.Navigation("Task");

                    b.Navigation("User");
                });

            modelBuilder.Entity("ItAssetsSystem.Domain.Entities.Tasks.PdcaPlan", b =>
                {
                    b.HasOne("ItAssetsSystem.Models.Entities.User", "CreatorUser")
                        .WithMany()
                        .HasForeignKey("CreatorUserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ItAssetsSystem.Models.Entities.User", "ResponsiblePerson")
                        .WithMany()
                        .HasForeignKey("ResponsiblePersonId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ItAssetsSystem.Domain.Entities.Tasks.Task", "Task")
                        .WithOne()
                        .HasForeignKey("ItAssetsSystem.Domain.Entities.Tasks.PdcaPlan", "TaskId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CreatorUser");

                    b.Navigation("ResponsiblePerson");

                    b.Navigation("Task");
                });

            modelBuilder.Entity("ItAssetsSystem.Domain.Entities.Tasks.PeriodicTaskSchedule", b =>
                {
                    b.HasOne("ItAssetsSystem.Models.Entities.User", "CreatorUser")
                        .WithMany()
                        .HasForeignKey("CreatorUserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ItAssetsSystem.Domain.Entities.Tasks.Task", "TemplateTask")
                        .WithMany()
                        .HasForeignKey("TemplateTaskId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("CreatorUser");

                    b.Navigation("TemplateTask");
                });

            modelBuilder.Entity("ItAssetsSystem.Domain.Entities.Tasks.Task", b =>
                {
                    b.HasOne("ItAssetsSystem.Models.Entities.Asset", "Asset")
                        .WithMany()
                        .HasForeignKey("AssetId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("ItAssetsSystem.Models.Entities.User", "Assignee")
                        .WithMany()
                        .HasForeignKey("AssigneeUserId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("ItAssetsSystem.Models.Entities.User", "Creator")
                        .WithMany()
                        .HasForeignKey("CreatorUserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ItAssetsSystem.Models.Entities.Location", "Location")
                        .WithMany()
                        .HasForeignKey("LocationId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("ItAssetsSystem.Domain.Entities.Tasks.Task", "ParentTask")
                        .WithMany("SubTasks")
                        .HasForeignKey("ParentTaskId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("ItAssetsSystem.Domain.Entities.Tasks.Task", "PreviousInstanceTask")
                        .WithOne()
                        .HasForeignKey("ItAssetsSystem.Domain.Entities.Tasks.Task", "PreviousInstanceTaskId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("Asset");

                    b.Navigation("Assignee");

                    b.Navigation("Creator");

                    b.Navigation("Location");

                    b.Navigation("ParentTask");

                    b.Navigation("PreviousInstanceTask");
                });

            modelBuilder.Entity("ItAssetsSystem.Domain.Entities.Tasks.TaskAssignee", b =>
                {
                    b.HasOne("ItAssetsSystem.Models.Entities.User", "AssignedByUser")
                        .WithMany()
                        .HasForeignKey("AssignedByUserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ItAssetsSystem.Domain.Entities.Tasks.Task", "Task")
                        .WithMany("Assignees")
                        .HasForeignKey("TaskId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ItAssetsSystem.Models.Entities.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("AssignedByUser");

                    b.Navigation("Task");

                    b.Navigation("User");
                });

            modelBuilder.Entity("ItAssetsSystem.Domain.Entities.Tasks.TaskHistory", b =>
                {
                    b.HasOne("ItAssetsSystem.Domain.Entities.Tasks.Attachment", "Attachment")
                        .WithMany()
                        .HasForeignKey("AttachmentId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("ItAssetsSystem.Domain.Entities.Tasks.Comment", "Comment")
                        .WithMany()
                        .HasForeignKey("CommentId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("ItAssetsSystem.Domain.Entities.Tasks.Task", "Task")
                        .WithMany("History")
                        .HasForeignKey("TaskId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ItAssetsSystem.Models.Entities.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("Attachment");

                    b.Navigation("Comment");

                    b.Navigation("Task");

                    b.Navigation("User");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.Asset", b =>
                {
                    b.HasOne("ItAssetsSystem.Models.Entities.AssetType", "AssetType")
                        .WithMany("Assets")
                        .HasForeignKey("AssetTypeId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ItAssetsSystem.Models.Entities.Department", "Department")
                        .WithMany("Assets")
                        .HasForeignKey("DepartmentId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("ItAssetsSystem.Models.Entities.Location", "Location")
                        .WithMany("Assets")
                        .HasForeignKey("LocationId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("AssetType");

                    b.Navigation("Department");

                    b.Navigation("Location");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.AssetHistory", b =>
                {
                    b.HasOne("ItAssetsSystem.Models.Entities.Asset", "Asset")
                        .WithMany("AssetHistories")
                        .HasForeignKey("AssetId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ItAssetsSystem.Models.Entities.User", "Operator")
                        .WithMany()
                        .HasForeignKey("OperatorId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Asset");

                    b.Navigation("Operator");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.AssetReceive", b =>
                {
                    b.HasOne("ItAssetsSystem.Models.Entities.Asset", "Asset")
                        .WithMany()
                        .HasForeignKey("AssetId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ItAssetsSystem.Models.Entities.Location", "Location")
                        .WithMany()
                        .HasForeignKey("LocationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ItAssetsSystem.Models.Entities.PurchaseOrder", "PurchaseOrder")
                        .WithMany()
                        .HasForeignKey("PurchaseOrderId");

                    b.HasOne("ItAssetsSystem.Models.Entities.User", "Receiver")
                        .WithMany()
                        .HasForeignKey("ReceiverId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ItAssetsSystem.Models.Entities.ReturnToFactory", "ReturnToFactory")
                        .WithMany()
                        .HasForeignKey("ReturnToFactoryId");

                    b.Navigation("Asset");

                    b.Navigation("Location");

                    b.Navigation("PurchaseOrder");

                    b.Navigation("Receiver");

                    b.Navigation("ReturnToFactory");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.AssetType", b =>
                {
                    b.HasOne("ItAssetsSystem.Models.Entities.AssetType", "Parent")
                        .WithMany("Children")
                        .HasForeignKey("ParentId");

                    b.Navigation("Parent");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.AuditLog", b =>
                {
                    b.HasOne("ItAssetsSystem.Models.Entities.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId");

                    b.Navigation("User");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.Department", b =>
                {
                    b.HasOne("ItAssetsSystem.Models.Entities.Department", "Parent")
                        .WithMany("Children")
                        .HasForeignKey("ParentId");

                    b.Navigation("Parent");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.FaultRecord", b =>
                {
                    b.HasOne("ItAssetsSystem.Models.Entities.Asset", "Asset")
                        .WithMany("FaultRecords")
                        .HasForeignKey("AssetId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ItAssetsSystem.Models.Entities.User", "Assignee")
                        .WithMany()
                        .HasForeignKey("AssigneeId");

                    b.HasOne("ItAssetsSystem.Models.Entities.FaultType", "FaultType")
                        .WithMany("FaultRecords")
                        .HasForeignKey("FaultTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ItAssetsSystem.Models.Entities.Location", "Location")
                        .WithMany()
                        .HasForeignKey("LocationId");

                    b.HasOne("ItAssetsSystem.Models.Entities.User", "Reporter")
                        .WithMany()
                        .HasForeignKey("ReporterId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Asset");

                    b.Navigation("Assignee");

                    b.Navigation("FaultType");

                    b.Navigation("Location");

                    b.Navigation("Reporter");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.FaultType", b =>
                {
                    b.HasOne("ItAssetsSystem.Models.Entities.FaultType", "Parent")
                        .WithMany("Children")
                        .HasForeignKey("ParentId");

                    b.Navigation("Parent");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.Location", b =>
                {
                    b.HasOne("ItAssetsSystem.Models.Entities.Department", "Department")
                        .WithMany()
                        .HasForeignKey("DefaultDepartmentId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("ItAssetsSystem.Models.Entities.User", "Manager")
                        .WithMany()
                        .HasForeignKey("ManagerId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("ItAssetsSystem.Models.Entities.Location", "Parent")
                        .WithMany("Children")
                        .HasForeignKey("ParentId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Department");

                    b.Navigation("Manager");

                    b.Navigation("Parent");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.LocationHistory", b =>
                {
                    b.HasOne("ItAssetsSystem.Models.Entities.Asset", "Asset")
                        .WithMany("LocationHistories")
                        .HasForeignKey("AssetId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ItAssetsSystem.Models.Entities.Location", "NewLocation")
                        .WithMany()
                        .HasForeignKey("NewLocationId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ItAssetsSystem.Models.Entities.Location", "OldLocation")
                        .WithMany()
                        .HasForeignKey("OldLocationId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("ItAssetsSystem.Models.Entities.User", "Operator")
                        .WithMany()
                        .HasForeignKey("OperatorId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Asset");

                    b.Navigation("NewLocation");

                    b.Navigation("OldLocation");

                    b.Navigation("Operator");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.LocationUser", b =>
                {
                    b.HasOne("ItAssetsSystem.Models.Entities.Location", "Location")
                        .WithMany("LocationUsers")
                        .HasForeignKey("LocationId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ItAssetsSystem.Models.Entities.Personnel", "Personnel")
                        .WithMany("LocationUsers")
                        .HasForeignKey("PersonnelId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("ItAssetsSystem.Models.Entities.User", null)
                        .WithMany("LocationUsers")
                        .HasForeignKey("UserId");

                    b.Navigation("Location");

                    b.Navigation("Personnel");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.MaintenanceOrder", b =>
                {
                    b.HasOne("ItAssetsSystem.Models.Entities.Asset", "Asset")
                        .WithMany()
                        .HasForeignKey("AssetId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ItAssetsSystem.Models.Entities.User", "Assignee")
                        .WithMany()
                        .HasForeignKey("AssigneeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ItAssetsSystem.Models.Entities.FaultRecord", "FaultRecord")
                        .WithMany()
                        .HasForeignKey("FaultRecordId");

                    b.HasOne("ItAssetsSystem.Models.Entities.Location", "Location")
                        .WithMany()
                        .HasForeignKey("LocationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Asset");

                    b.Navigation("Assignee");

                    b.Navigation("FaultRecord");

                    b.Navigation("Location");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.Menu", b =>
                {
                    b.HasOne("ItAssetsSystem.Models.Entities.Menu", "Parent")
                        .WithMany("Children")
                        .HasForeignKey("ParentId");

                    b.Navigation("Parent");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.Personnel", b =>
                {
                    b.HasOne("ItAssetsSystem.Models.Entities.Department", "Department")
                        .WithMany()
                        .HasForeignKey("DepartmentId");

                    b.Navigation("Department");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.PurchaseItem", b =>
                {
                    b.HasOne("ItAssetsSystem.Models.Entities.AssetType", "AssetType")
                        .WithMany()
                        .HasForeignKey("AssetTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ItAssetsSystem.Models.Entities.PurchaseOrder", "PurchaseOrder")
                        .WithMany("PurchaseItems")
                        .HasForeignKey("PurchaseOrderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("AssetType");

                    b.Navigation("PurchaseOrder");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.PurchaseOrder", b =>
                {
                    b.HasOne("ItAssetsSystem.Models.Entities.User", "Requester")
                        .WithMany()
                        .HasForeignKey("RequesterId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ItAssetsSystem.Models.Entities.Supplier", "Supplier")
                        .WithMany("PurchaseOrders")
                        .HasForeignKey("SupplierId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Requester");

                    b.Navigation("Supplier");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.RefreshToken", b =>
                {
                    b.HasOne("ItAssetsSystem.Models.Entities.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.ReturnToFactory", b =>
                {
                    b.HasOne("ItAssetsSystem.Models.Entities.Asset", "Asset")
                        .WithMany()
                        .HasForeignKey("AssetId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ItAssetsSystem.Models.Entities.User", "Operator")
                        .WithMany()
                        .HasForeignKey("OperatorId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ItAssetsSystem.Models.Entities.Supplier", "Supplier")
                        .WithMany()
                        .HasForeignKey("SupplierId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Asset");

                    b.Navigation("Operator");

                    b.Navigation("Supplier");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.RoleMenu", b =>
                {
                    b.HasOne("ItAssetsSystem.Models.Entities.Menu", "Menu")
                        .WithMany("RoleMenus")
                        .HasForeignKey("MenuId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ItAssetsSystem.Models.Entities.Role", "Role")
                        .WithMany("RoleMenus")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Menu");

                    b.Navigation("Role");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.RolePermission", b =>
                {
                    b.HasOne("ItAssetsSystem.Models.Entities.Permission", "Permission")
                        .WithMany("RolePermissions")
                        .HasForeignKey("PermissionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ItAssetsSystem.Models.Entities.Role", "Role")
                        .WithMany("RolePermissions")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Permission");

                    b.Navigation("Role");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.User", b =>
                {
                    b.HasOne("ItAssetsSystem.Models.Entities.Role", "DefaultRole")
                        .WithMany()
                        .HasForeignKey("DefaultRoleId");

                    b.HasOne("ItAssetsSystem.Models.Entities.Department", "Department")
                        .WithMany("Users")
                        .HasForeignKey("DepartmentId");

                    b.Navigation("DefaultRole");

                    b.Navigation("Department");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.UserRole", b =>
                {
                    b.HasOne("ItAssetsSystem.Models.Entities.Role", "Role")
                        .WithMany("UserRoles")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ItAssetsSystem.Models.Entities.User", "User")
                        .WithMany("UserRoles")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Role");

                    b.Navigation("User");
                });

            modelBuilder.Entity("ItAssetsSystem.Domain.Entities.Notes.QuickMemoCategory", b =>
                {
                    b.Navigation("QuickMemos");
                });

            modelBuilder.Entity("ItAssetsSystem.Domain.Entities.SparePart", b =>
                {
                    b.Navigation("Transactions");
                });

            modelBuilder.Entity("ItAssetsSystem.Domain.Entities.SparePartLocation", b =>
                {
                    b.Navigation("SpareParts");

                    b.Navigation("Transactions");
                });

            modelBuilder.Entity("ItAssetsSystem.Domain.Entities.SparePartType", b =>
                {
                    b.Navigation("Children");

                    b.Navigation("SpareParts");
                });

            modelBuilder.Entity("ItAssetsSystem.Domain.Entities.Tasks.Comment", b =>
                {
                    b.Navigation("Attachments");

                    b.Navigation("Replies");
                });

            modelBuilder.Entity("ItAssetsSystem.Domain.Entities.Tasks.Task", b =>
                {
                    b.Navigation("Assignees");

                    b.Navigation("Attachments");

                    b.Navigation("Comments");

                    b.Navigation("History");

                    b.Navigation("SubTasks");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.Asset", b =>
                {
                    b.Navigation("AssetHistories");

                    b.Navigation("FaultRecords");

                    b.Navigation("LocationHistories");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.AssetType", b =>
                {
                    b.Navigation("Assets");

                    b.Navigation("Children");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.Department", b =>
                {
                    b.Navigation("Assets");

                    b.Navigation("Children");

                    b.Navigation("Users");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.FaultType", b =>
                {
                    b.Navigation("Children");

                    b.Navigation("FaultRecords");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.Location", b =>
                {
                    b.Navigation("Assets");

                    b.Navigation("Children");

                    b.Navigation("LocationUsers");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.Menu", b =>
                {
                    b.Navigation("Children");

                    b.Navigation("RoleMenus");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.Permission", b =>
                {
                    b.Navigation("RolePermissions");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.Personnel", b =>
                {
                    b.Navigation("LocationUsers");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.PurchaseOrder", b =>
                {
                    b.Navigation("PurchaseItems");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.Role", b =>
                {
                    b.Navigation("RoleMenus");

                    b.Navigation("RolePermissions");

                    b.Navigation("UserRoles");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.Supplier", b =>
                {
                    b.Navigation("PurchaseOrders");
                });

            modelBuilder.Entity("ItAssetsSystem.Models.Entities.User", b =>
                {
                    b.Navigation("LocationUsers");

                    b.Navigation("UserRoles");
                });
#pragma warning restore 612, 618
        }
    }
}
