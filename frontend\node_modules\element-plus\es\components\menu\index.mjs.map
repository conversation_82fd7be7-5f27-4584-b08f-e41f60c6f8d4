{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/menu/index.ts"], "sourcesContent": ["import { withInstall, withNoopInstall } from '@element-plus/utils'\n\nimport Menu from './src/menu'\nimport MenuItem from './src/menu-item.vue'\nimport MenuItemGroup from './src/menu-item-group.vue'\nimport SubMenu from './src/sub-menu'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElMenu: SFCWithInstall<typeof Menu> & {\n  MenuItem: typeof MenuItem\n  MenuItemGroup: typeof MenuItemGroup\n  SubMenu: typeof SubMenu\n} = withInstall(Menu, {\n  MenuItem,\n  MenuItemGroup,\n  SubMenu,\n})\nexport default ElMenu\nexport const ElMenuItem: SFCWithInstall<typeof MenuItem> =\n  withNoopInstall(MenuItem)\nexport const ElMenuItemGroup: SFCWithInstall<typeof MenuItemGroup> =\n  withNoopInstall(MenuItemGroup)\nexport const ElSubMenu: SFCWithInstall<typeof SubMenu> =\n  withNoopInstall(SubMenu)\n\nexport * from './src/menu'\nexport * from './src/menu-item'\nexport * from './src/menu-item-group'\nexport * from './src/sub-menu'\nexport * from './src/types'\nexport * from './src/instance'\n"], "names": [], "mappings": ";;;;;;;;;;AAKY,MAAC,MAAM,GAAG,WAAW,CAAC,IAAI,EAAE;AACxC,EAAE,QAAQ;AACV,EAAE,aAAa;AACf,EAAE,OAAO;AACT,CAAC,EAAE;AAES,MAAC,UAAU,GAAG,eAAe,CAAC,QAAQ,EAAE;AACxC,MAAC,eAAe,GAAG,eAAe,CAAC,aAAa,EAAE;AAClD,MAAC,SAAS,GAAG,eAAe,CAAC,OAAO;;;;"}