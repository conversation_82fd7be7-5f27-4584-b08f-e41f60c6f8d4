// File: Application/Features/Avatars/Commands/UploadAvatarCommandHandler.cs
// Description: Handler for UploadAvatarCommand.

#nullable enable

using MediatR;
using Microsoft.Extensions.Logging;
using ItAssetsSystem.Application.Features.Avatars.Dtos;
using ItAssetsSystem.Core.Abstractions; // For IUserRepository AND Core IFileStorageService
using ItAssetsSystem.Models.Entities;
// Removed IOptions<FileStorageSettings> as FileStorageService now handles it.
using System;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;

namespace ItAssetsSystem.Application.Features.Avatars.Commands
{
    public class UploadAvatarCommandHandler : IRequestHandler<UploadAvatarCommand, UploadAvatarResponseDto>
    {
        private readonly IUserRepository _userRepository;
        private readonly IFileStorageService _fileStorageService; // This is now Core.Abstractions.IFileStorageService
        private readonly ILogger<UploadAvatarCommandHandler> _logger;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public UploadAvatarCommandHandler(
            IUserRepository userRepository,
            IFileStorageService fileStorageService, // Core.Abstractions.IFileStorageService
            ILogger<UploadAvatarCommandHandler> logger,
            IHttpContextAccessor httpContextAccessor)
        {
            _userRepository = userRepository ?? throw new ArgumentNullException(nameof(userRepository));
            _fileStorageService = fileStorageService ?? throw new ArgumentNullException(nameof(fileStorageService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _httpContextAccessor = httpContextAccessor ?? throw new ArgumentNullException(nameof(httpContextAccessor));
        }

        public async Task<UploadAvatarResponseDto> Handle(UploadAvatarCommand request, CancellationToken cancellationToken)
        {
            var response = new UploadAvatarResponseDto();

            try
            {
                // 1. Validate User
                if (!int.TryParse(request.UserId, out int userIdInt))
                {
                    response.Success = false;
                    response.Message = "Invalid user ID format.";
                    _logger.LogWarning("Invalid user ID format provided: {UserId}", request.UserId);
                    return response;
                }
                var user = await _userRepository.GetByIdAsync(userIdInt);
                if (user == null)
                {
                    response.Success = false;
                    response.Message = "User not found.";
                    _logger.LogWarning("User not found for ID: {UserId}", userIdInt);
                    return response;
                }

                // 2. Validate File (Basic - Service will do more)
                if (request.File == null || request.File.Length == 0)
                {
                    response.Success = false;
                    response.Message = "No file uploaded or file is empty.";
                    return response;
                }

                var allowedExtensions = new[] { ".jpg", ".jpeg", ".png" };
                var fileExtension = Path.GetExtension(request.File.FileName)?.ToLowerInvariant();
                var maxFileSize = 5 * 1024 * 1024; // 5MB

                if (string.IsNullOrEmpty(fileExtension) || !allowedExtensions.Contains(fileExtension))
                {
                    response.Success = false;
                    response.Message = "Invalid file type. Only JPG, JPEG, PNG are allowed.";
                    return response;
                }

                if (request.File.Length > maxFileSize)
                {
                    response.Success = false;
                    response.Message = $"File size exceeds the limit of {maxFileSize / 1024 / 1024}MB.";
                    return response;
                }

                // 3. Delete Old Avatar (if exists)
                if (!string.IsNullOrEmpty(user.Avatar))
                {
                    bool oldDeleted = await _fileStorageService.DeleteFileAsync(user.Avatar);
                    if (!oldDeleted)
                    {
                        _logger.LogWarning("Failed to delete old avatar: {OldAvatarPath} for user {UserId}. Proceeding with new upload.", user.Avatar, userIdInt);
                        // Optionally, decide if this is a hard fail or just a warning.
                    }
                    else
                    {
                        _logger.LogInformation("Successfully deleted old avatar {OldAvatarPath} for user {UserId}", user.Avatar, userIdInt);
                    }
                }

                // 4. Save New File (using Core.Abstractions.IFileStorageService)
                // The service method now takes userId directly and handles path/name generation.
                string? newRelativePath = await _fileStorageService.SaveAvatarAsync(request.File, request.UserId); 

                if (string.IsNullOrEmpty(newRelativePath))
                {
                    _logger.LogError("Failed to save new avatar for user {UserId}. SaveAvatarAsync returned null or empty.", userIdInt);
                    response.Success = false;
                    response.Message = "Could not save the avatar file. The storage service failed.";
                    return response;
                }

                // 5. Update User Entity with new relative path
                user.Avatar = newRelativePath;
                user.UpdatedAt = DateTime.UtcNow;
                bool updateSuccess = await _userRepository.UpdateAsync(user);

                if (!updateSuccess)
                {
                    _logger.LogError("Failed to update user avatar in database for user {UserId}.", userIdInt);
                    response.Success = false;
                    response.Message = "Failed to update user information with new avatar.";
                    // Attempt to delete the newly uploaded file if DB update fails
                    await _fileStorageService.DeleteFileAsync(newRelativePath);
                    return response;
                }

                // 6. Prepare Success Response with both absolute and relative paths
                string? baseUrl = GetBaseUrl();
                string accessUrl = baseUrl != null 
                    ? $"{baseUrl.TrimEnd('/')}/{newRelativePath.TrimStart('/')}" 
                    : $"/{newRelativePath.TrimStart('/')}";

                response.Success = true;
                response.AvatarUrl = newRelativePath; // 保持相对路径的返回
                response.AccessUrl = accessUrl; // 添加完整访问URL
                response.Message = "Avatar uploaded successfully.";
                _logger.LogInformation("Avatar uploaded successfully for user {UserId}. Path: {AvatarPath}, Access URL: {AccessUrl}", 
                    userIdInt, newRelativePath, accessUrl);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "An unexpected error occurred while uploading avatar for user {UserId}", request.UserId);
                response.Success = false;
                response.Message = ex.Message;
            }

            return response;
        }

        /// <summary>
        /// 获取当前请求的基础URL
        /// </summary>
        /// <returns>基础URL，例如 https://example.com </returns>
        private string? GetBaseUrl()
        {
            try
            {
                var request = _httpContextAccessor.HttpContext?.Request;
                if (request == null) return null;

                var host = request.Host.Value; // 包含主机名和可能的端口
                var pathBase = request.PathBase.Value?.TrimEnd('/');
                
                // 构建基础URL
                return $"{request.Scheme}://{host}{pathBase}";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error determining base URL");
                return null;
            }
        }
    }
} 