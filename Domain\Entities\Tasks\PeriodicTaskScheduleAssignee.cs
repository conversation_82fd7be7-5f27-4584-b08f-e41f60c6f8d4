#nullable enable
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ItAssetsSystem.Domain.Entities.Tasks
{
    /// <summary>
    /// 周期性任务计划负责人关联表
    /// </summary>
    [Table("periodic_task_schedule_assignees")]
    public class PeriodicTaskScheduleAssignee
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Key]
        [Column("id")]
        public long Id { get; set; }

        /// <summary>
        /// 周期性任务计划ID
        /// </summary>
        [Column("periodic_task_schedule_id")]
        public long PeriodicTaskScheduleId { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        [Column("user_id")]
        public int UserId { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [Column("created_at")]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 导航属性：周期性任务计划
        /// </summary>
        [ForeignKey("PeriodicTaskScheduleId")]
        public virtual PeriodicTaskSchedule? PeriodicTaskSchedule { get; set; }
    }
}
