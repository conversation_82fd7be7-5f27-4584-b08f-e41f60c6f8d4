{"name": "interactjs", "main": "index.js", "license": "SEE LICENSE AT https://interactjs.io/license", "description": "Drag and drop, resizing and multi-touch gestures with inertia and snapping for modern browsers (and also IE9+)", "homepage": "http://interactjs.io", "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://taye.me"}], "keywords": ["interact.js", "draggable", "droppable", "drag", "drop", "drag and drop", "resize", "touch", "multi-touch", "gesture", "snap", "inertia", "grid", "autoscroll", "SVG"], "moduleType": ["amd", "globals", "node"], "ignore": ["/*", "!src", "!dist", "!LICENSE"]}