import{_ as e,x as a,r as l,c as t,j as r,z as s,a as u,k as n,o as d,w as o,b as i,F as c,h as m,d as p,e as v,$ as f,p as y,t as b}from"./index-CG5lHOPO.js";const h={class:"user-option-content"},k={class:"user-info"},g={class:"user-name"},U={key:0,class:"user-department"},_={key:0,class:"user-workload"},w=e({__name:"UserSelect",props:{modelValue:{type:[Number,Array],default:null},placeholder:{type:String,default:"选择用户"},multiple:{type:Boolean,default:!1},filterable:{type:Boolean,default:!0},remote:{type:Boolean,default:!0},clearable:{type:Boolean,default:!0},size:{type:String,default:"default"},showWorkload:{type:Boolean,default:!1},userFilter:{type:Object,default:()=>({})}},emits:["update:modelValue","change"],setup(e,{emit:w}){const z=e,A=w,T=a(),B=l([]),V=l(!1),F=l(""),j=t((()=>T.users||[]));r((()=>z.userFilter),(()=>{x()}),{deep:!0}),s((()=>{x()}));const x=async(e="")=>{V.value=!0;try{let a=[];a=e?await T.searchUsers(e):await T.getUsers(z.userFilter),B.value=a.map((e=>{var a;return{id:e.id,name:e.name||e.username,avatarUrl:e.avatarUrl||e.avatar,department:(null==(a=e.department)?void 0:a.name)||e.departmentName,currentTasks:e.currentTasks||0,isActive:!1!==e.isActive}})).filter((e=>e.isActive))}catch(a){B.value=j.value.map((e=>({id:e.id,name:e.name||e.username,avatarUrl:e.avatarUrl,department:e.department,currentTasks:0,isActive:!0})))}finally{V.value=!1}},S=e=>{F.value=e,e?x(e):x()},N=e=>{A("update:modelValue",e),A("change",e)};return(a,l)=>{const t=u("el-avatar"),r=u("el-tag"),s=u("el-option"),w=u("el-select");return d(),n(w,{"model-value":e.modelValue,"onUpdate:modelValue":N,placeholder:e.placeholder,multiple:e.multiple,filterable:e.filterable,remote:e.remote,"remote-method":S,loading:V.value,clearable:e.clearable,size:e.size,class:"user-select"},{default:o((()=>[(d(!0),i(c,null,m(B.value,(a=>(d(),n(s,{key:a.id,label:a.name,value:a.id,class:"user-option"},{default:o((()=>{return[p("div",h,[v(t,{src:a.avatarUrl,size:20,class:"user-avatar"},{default:o((()=>{var e;return[y(b((null==(e=a.name)?void 0:e.charAt(0))||"?"),1)]})),_:2},1032,["src"]),p("div",k,[p("span",g,b(a.name),1),a.department?(d(),i("span",U,b(a.department),1)):f("",!0)]),e.showWorkload&&void 0!==a.currentTasks?(d(),i("div",_,[v(r,{size:"small",type:(l=a.currentTasks,0===l?"success":l<=3?"":l<=6?"warning":"danger")},{default:o((()=>[y(b(a.currentTasks)+"个任务 ",1)])),_:2},1032,["type"])])):f("",!0)])];var l})),_:2},1032,["label","value"])))),128))])),_:1},8,["model-value","placeholder","multiple","filterable","remote","loading","clearable","size"])}}},[["__scopeId","data-v-73c4f6e9"]]);export{w as U};
