import{_ as e,r as s,b as a,e as t,w as l,a as o,o as c,d as r,$ as n,p as i,i as d,t as u,E as p,bY as m,C as v}from"./index-CG5lHOPO.js";const h={class:"export-test-container"},g={class:"export-options"},y={key:0,class:"result-section"},f={key:0,class:"result-details"},L=e({__name:"export",setup(e){const L=s(!1),b=s(!1),x=s(null),X=async()=>{L.value=!0,x.value=null;try{const s=p({message:"正在准备XLSX导出，请稍候...",duration:0,type:"info"});try{const e=await m({method:"get",url:`${v.apiBaseUrl}/ExportTest/xlsx`,responseType:"blob",headers:{Accept:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}});s.close();const a=e.data;if(!(a&&a instanceof Blob))throw new Error("导出失败：服务器响应格式不正确");if(0===a.size)throw new Error("导出失败：收到的文件为空");const t=`测试导出_XLSX_${(new Date).toISOString().substring(0,10)}.xlsx`,l=URL.createObjectURL(a),o=document.createElement("a");o.href=l,o.setAttribute("download",t),document.body.appendChild(o),o.click(),setTimeout((()=>{document.body.removeChild(o),URL.revokeObjectURL(l)}),100),x.value={success:!0,message:"成功导出XLSX格式文件",details:`文件大小: ${(a.size/1024).toFixed(2)} KB\n文件类型: ${a.type}`},p.success("XLSX导出成功")}catch(e){throw s.close(),e}}catch(e){x.value={success:!1,message:"XLSX导出失败: "+(e.message||"未知错误"),details:e.stack},p.error("XLSX导出失败: "+(e.message||"未知错误"))}finally{L.value=!1}},S=async()=>{b.value=!0,x.value=null;try{const s=p({message:"正在准备XLS导出，请稍候...",duration:0,type:"info"});try{const e=await m({method:"get",url:`${v.apiBaseUrl}/ExportTest/xls`,responseType:"blob",headers:{Accept:"application/vnd.ms-excel"}});s.close();const a=e.data;if(!(a&&a instanceof Blob))throw new Error("导出失败：服务器响应格式不正确");if(0===a.size)throw new Error("导出失败：收到的文件为空");const t=`测试导出_XLS_${(new Date).toISOString().substring(0,10)}.xls`,l=URL.createObjectURL(a),o=document.createElement("a");o.href=l,o.setAttribute("download",t),document.body.appendChild(o),o.click(),setTimeout((()=>{document.body.removeChild(o),URL.revokeObjectURL(l)}),100),x.value={success:!0,message:"成功导出XLS格式文件",details:`文件大小: ${(a.size/1024).toFixed(2)} KB\n文件类型: ${a.type}`},p.success("XLS导出成功")}catch(e){throw s.close(),e}}catch(e){x.value={success:!1,message:"XLS导出失败: "+(e.message||"未知错误"),details:e.stack},p.error("XLS导出失败: "+(e.message||"未知错误"))}finally{b.value=!1}};return(e,s)=>{const p=o("el-button"),m=o("el-card");return c(),a("div",h,[t(m,{class:"export-card"},{header:l((()=>s[0]||(s[0]=[r("div",{class:"card-header"},[r("h2",null,"导出功能测试"),r("p",null,"用于测试不同格式的Excel导出功能")],-1)]))),default:l((()=>[r("div",g,[t(p,{type:"primary",onClick:X,loading:L.value,size:"large"},{default:l((()=>s[1]||(s[1]=[i(" 导出XLSX格式 (Excel 2007+) ")]))),_:1},8,["loading"]),t(p,{type:"success",onClick:S,loading:b.value,size:"large"},{default:l((()=>s[2]||(s[2]=[i(" 导出XLS格式 (Excel 97-2003) ")]))),_:1},8,["loading"])]),x.value?(c(),a("div",y,[s[3]||(s[3]=r("h3",null,"最近操作结果:",-1)),r("div",{class:d(["result-message",x.value.success?"success":"error"])},u(x.value.message),3),x.value.details?(c(),a("div",f,[r("pre",null,u(x.value.details),1)])):n("",!0)])):n("",!0)])),_:1})])}}},[["__scopeId","data-v-ff0817da"]]);export{L as default};
