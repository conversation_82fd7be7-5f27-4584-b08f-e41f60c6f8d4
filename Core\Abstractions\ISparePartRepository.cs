// File: Core/Abstractions/ISparePartRepository.cs
// Description: 备品备件仓储接口，定义数据访问方法

using System.Collections.Generic;
using System.Threading.Tasks;
using ItAssetsSystem.Application.Features.SpareParts.Dtos;
using ItAssetsSystem.Domain.Entities;

namespace ItAssetsSystem.Core.Abstractions
{
    /// <summary>
    /// 备品备件仓储接口
    /// </summary>
    public interface ISparePartRepository
    {
        /// <summary>
        /// 获取备品备件分页列表
        /// </summary>
        /// <param name="query">查询参数</param>
        /// <returns>备件列表和总数</returns>
        Task<(List<SparePart>, int)> GetSparePartsPagedAsync(SparePartQuery query);
        
        /// <summary>
        /// 根据ID获取备品备件
        /// </summary>
        /// <param name="id">备件ID</param>
        /// <returns>备件实体</returns>
        Task<SparePart> GetByIdAsync(long id);
        
        /// <summary>
        /// 根据备件编码检查备件是否存在
        /// </summary>
        /// <param name="code">备件编码</param>
        /// <returns>如果存在则返回true，否则返回false</returns>
        Task<bool> ExistsByCodeAsync(string code);
        
        /// <summary>
        /// 根据备件编码查找备件实体
        /// </summary>
        /// <param name="code">备件编码</param>
        /// <returns>备件实体，如果不存在则返回null</returns>
        Task<SparePart> FindByCodeAsync(string code);
        
        /// <summary>
        /// 创建备品备件
        /// </summary>
        /// <param name="entity">备件实体</param>
        /// <returns>创建的备件实体</returns>
        Task<SparePart> CreateAsync(SparePart entity);
        
        /// <summary>
        /// 更新备品备件
        /// </summary>
        /// <param name="entity">备件实体</param>
        /// <returns>更新后的备件实体</returns>
        Task<SparePart> UpdateAsync(SparePart entity);
        
        /// <summary>
        /// 删除备品备件
        /// </summary>
        /// <param name="id">备件ID</param>
        /// <returns>是否成功</returns>
        Task<bool> DeleteAsync(long id);
        
        /// <summary>
        /// 获取库存预警列表
        /// </summary>
        /// <param name="onlyMinStock">是否只获取低于最小安全库存的备件</param>
        /// <returns>备件列表</returns>
        Task<List<SparePart>> GetLowStockSparePartsAsync(bool onlyMinStock);
        
        /// <summary>
        /// 更新库存数量
        /// </summary>
        /// <param name="id">备件ID</param>
        /// <param name="changeQuantity">变更数量(正数增加,负数减少)</param>
        /// <param name="transaction">出入库记录</param>
        /// <returns>是否成功</returns>
        Task<bool> UpdateStockAsync(long id, int changeQuantity, SparePartTransaction transaction);
    }
} 