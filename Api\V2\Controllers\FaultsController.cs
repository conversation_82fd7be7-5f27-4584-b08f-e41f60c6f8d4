using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Logging;
using ItAssetsSystem.Application.Features.Faults.Services;
using ItAssetsSystem.Core.Abstractions;
using ItAssetsSystem.Application.Common.Dtos;
using ItAssetsSystem.Services.Interfaces;
using ItAssetsSystem.Models.Entities.Gamification;

namespace ItAssetsSystem.Api.V2.Controllers
{
    /// <summary>
    /// 故障管理V2控制器
    /// </summary>
    [ApiController]
    [Route("api/v2/faults")]
    [Authorize]
    public class FaultsController : ControllerBase
    {
        private readonly FaultService _faultService;
        private readonly ICurrentUserService _currentUserService;
        private readonly ILogger<FaultsController> _logger;
        private readonly IUniversalGamificationService _universalGamificationService;

        public FaultsController(
            FaultService faultService,
            ICurrentUserService currentUserService,
            ILogger<FaultsController> logger,
            IUniversalGamificationService universalGamificationService)
        {
            _faultService = faultService;
            _currentUserService = currentUserService;
            _logger = logger;
            _universalGamificationService = universalGamificationService;
        }

        /// <summary>
        /// 获取故障列表
        /// </summary>
        /// <param name="page">页码</param>
        /// <param name="pageSize">页大小</param>
        /// <param name="code">故障编号</param>
        /// <param name="assetKeyword">资产关键字</param>
        /// <param name="faultType">故障类型</param>
        /// <param name="status">状态</param>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <returns>故障列表</returns>
        [HttpGet]
        public async Task<IActionResult> GetFaultList(
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 10,
            [FromQuery] string code = null,
            [FromQuery] string assetKeyword = null,
            [FromQuery] int? faultType = null,
            [FromQuery] int? status = null,
            [FromQuery] DateTime? startTime = null,
            [FromQuery] DateTime? endTime = null)
        {
            try
            {
                var result = await _faultService.GetFaultListAsync(new GetFaultListRequest
                {
                    Page = page,
                    PageSize = pageSize,
                    Code = code,
                    AssetKeyword = assetKeyword,
                    FaultType = faultType,
                    Status = status,
                    StartTime = startTime,
                    EndTime = endTime
                });

                return Ok(ApiResponse<object>.CreateSuccess(result));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取故障列表失败");
                return Ok(ApiResponse<object>.CreateFail("获取故障列表失败"));
            }
        }

        /// <summary>
        /// 获取故障详情
        /// </summary>
        /// <param name="id">故障ID</param>
        /// <returns>故障详情</returns>
        [HttpGet("{id}")]
        public async Task<IActionResult> GetFaultById(int id)
        {
            try
            {
                var result = await _faultService.GetFaultByIdAsync(id);
                return Ok(ApiResponse<object>.CreateSuccess(result));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取故障详情失败，故障ID: {FaultId}", id);
                return Ok(ApiResponse<object>.CreateFail("获取故障详情失败"));
            }
        }

        /// <summary>
        /// 创建故障记录
        /// </summary>
        /// <param name="request">故障创建请求</param>
        /// <returns>创建结果</returns>
        [HttpPost]
        public async Task<IActionResult> CreateFault([FromBody] CreateFaultRequestDto request)
        {
            _logger.LogInformation($"用户 {_currentUserService.UserId} 创建故障记录: {request.Title}");

            try
            {
                // 验证必填字段
                if (string.IsNullOrWhiteSpace(request.Title))
                {
                    return Ok(ApiResponse<object>.CreateFail("故障标题不能为空"));
                }

                if (string.IsNullOrWhiteSpace(request.Description))
                {
                    return Ok(ApiResponse<object>.CreateFail("故障描述不能为空"));
                }

                if (request.FaultType <= 0)
                {
                    return Ok(ApiResponse<object>.CreateFail("请选择故障类型"));
                }

                // 构建服务请求
                var serviceRequest = new CreateFaultRequest
                {
                    FaultMode = request.FaultMode ?? "asset",
                    AssetId = request.AssetId,
                    AssetKeyword = request.AssetKeyword,
                    DeviceName = request.DeviceName,
                    FaultTypeId = request.FaultType,
                    Title = request.Title,
                    Description = request.Description,
                    Priority = request.Priority ?? "medium",
                    ReporterId = _currentUserService.UserId,
                    HappenTime = request.HappenTime,
                    AutoGenerateSparePartRecord = request.AutoGenerateSparePartRecord,
                    SparePartInfo = request.SparePartInfo != null ? new SparePartInfoRequest
                    {
                        Name = request.SparePartInfo.Name,
                        TypeId = request.SparePartInfo.TypeId,
                        Specification = request.SparePartInfo.Specification,
                        Brand = request.SparePartInfo.Brand,
                        Quantity = request.SparePartInfo.Quantity,
                        Price = request.SparePartInfo.Price,
                        LocationId = request.SparePartInfo.LocationId
                    } : null
                };

                var result = await _faultService.CreateFaultAsync(serviceRequest);

                // 触发故障登记奖励
                try
                {
                    await _universalGamificationService.TriggerBehaviorRewardAsync(
                        _currentUserService.UserId,
                        BehaviorCodes.FAULT_RECORDED,
                        (result as dynamic)?.id ?? 0,
                        context: new {
                            Title = request.Title,
                            Priority = request.Priority,
                            FaultType = request.FaultType
                        },
                        description: $"登记故障: {request.Title}"
                    );
                }
                catch (Exception ex)
                {
                    var faultId = (result as dynamic)?.id ?? 0;
                    Microsoft.Extensions.Logging.LoggerExtensions.LogWarning(_logger, ex, "触发故障登记奖励失败: FaultId={FaultId}", faultId);
                }

                return Ok(ApiResponse<object>.CreateSuccess(result));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"创建故障记录失败: {request.Title}");
                return Ok(ApiResponse<object>.CreateFail("创建故障记录失败"));
            }
        }
    }

    /// <summary>
    /// 创建故障请求DTO
    /// </summary>
    public class CreateFaultRequestDto
    {
        /// <summary>
        /// 故障模式：asset(有资产) / offline(线下设备)
        /// </summary>
        public string FaultMode { get; set; } = "asset";

        /// <summary>
        /// 资产ID（可选，对于电池等无固定资产编号的故障可为空）
        /// </summary>
        public int? AssetId { get; set; }

        /// <summary>
        /// 资产关键字（用于搜索）
        /// </summary>
        public string AssetKeyword { get; set; }

        /// <summary>
        /// 线下设备名称（当故障模式为offline时使用）
        /// </summary>
        public string DeviceName { get; set; }

        /// <summary>
        /// 故障类型
        /// </summary>
        public int FaultType { get; set; }

        /// <summary>
        /// 故障标题
        /// </summary>
        public string Title { get; set; }

        /// <summary>
        /// 故障描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 优先级
        /// </summary>
        public string Priority { get; set; }

        /// <summary>
        /// 故障发生时间
        /// </summary>
        public System.DateTime? HappenTime { get; set; }

        /// <summary>
        /// 是否自动生成备件入库记录
        /// </summary>
        public bool AutoGenerateSparePartRecord { get; set; } = false;

        /// <summary>
        /// 备件信息（如果需要自动生成备件记录）
        /// </summary>
        public SparePartInfoDto SparePartInfo { get; set; }
    }

    /// <summary>
    /// 备件信息DTO
    /// </summary>
    public class SparePartInfoDto
    {
        /// <summary>
        /// 备件名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 备件类型ID
        /// </summary>
        public int? TypeId { get; set; }

        /// <summary>
        /// 规格型号
        /// </summary>
        public string Specification { get; set; }

        /// <summary>
        /// 品牌
        /// </summary>
        public string Brand { get; set; }

        /// <summary>
        /// 入库数量
        /// </summary>
        public int Quantity { get; set; } = 1;

        /// <summary>
        /// 单价
        /// </summary>
        public decimal? Price { get; set; }

        /// <summary>
        /// 库位ID
        /// </summary>
        public int? LocationId { get; set; }
    }
}
