# IT资产管理系统 - 现代化游戏化统计系统实施方案

## 📋 方案概述

**技术栈**: Vue 3 + .NET 8 + MySQL + SignalR + Redis  
**适用规模**: 100人以内企业  
**实施周期**: 2-3周  
**方案特色**: 现代化架构 + 实时更新 + 高性能缓存 + 用户体验优良  

---

## 🏗️ 系统架构设计

### 整体架构图
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Vue 3 前端    │    │   .NET 8 API    │    │   MySQL 数据库   │
│                 │    │                 │    │                 │
│ • Pinia状态管理 │◄──►│ • EF Core ORM   │◄──►│ • 汇总统计表     │
│ • SignalR客户端 │    │ • SignalR Hub   │    │ • 排行榜视图     │
│ • Element Plus  │    │ • 后台服务      │    │ • 索引优化       │
│ • 实时图表      │    │ • 缓存策略      │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
        │                       │                       │
        │                       │                       │
        └───────────────────────┼───────────────────────┘
                                │
                    ┌─────────────────┐
                    │   Redis 缓存    │
                    │                 │
                    │ • 排行榜缓存     │
                    │ • 用户统计缓存   │
                    │ • 实时数据缓存   │
                    └─────────────────┘
```

### 核心设计原则
1. **性能优先**: 多层缓存 + 预聚合数据
2. **实时体验**: SignalR推送 + 前端状态同步
3. **扩展性强**: 配置驱动 + 模块化设计
4. **维护简单**: 现代化代码 + 清晰架构

---

## 🗄️ 数据库设计

### 1. 核心统计表

```sql
-- =====================================================
-- 用户工作统计汇总表 (核心表)
-- =====================================================
CREATE TABLE user_work_statistics (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    period_type ENUM('daily', 'weekly', 'monthly') NOT NULL,
    period_start_date DATE NOT NULL COMMENT '统计周期开始日期',
    
    -- 📋 任务模块统计
    tasks_created INT DEFAULT 0 COMMENT '新建任务数',
    tasks_claimed INT DEFAULT 0 COMMENT '领取任务数', 
    tasks_completed INT DEFAULT 0 COMMENT '完成任务数',
    tasks_commented INT DEFAULT 0 COMMENT '评论任务数',
    
    -- 🏢 资产模块统计
    assets_created INT DEFAULT 0 COMMENT '新建资产数',
    assets_updated INT DEFAULT 0 COMMENT '更新资产数',
    assets_deleted INT DEFAULT 0 COMMENT '删除资产数',
    
    -- 🔧 故障模块统计
    faults_reported INT DEFAULT 0 COMMENT '登记故障数',
    faults_repaired INT DEFAULT 0 COMMENT '维修故障数',
    
    -- 🛒 采购模块统计
    procurements_created INT DEFAULT 0 COMMENT '新建采购单数',
    procurements_updated INT DEFAULT 0 COMMENT '更新采购进度数',
    
    -- 📦 备件模块统计
    parts_in INT DEFAULT 0 COMMENT '备件入库数',
    parts_out INT DEFAULT 0 COMMENT '备件出库数',
    parts_added INT DEFAULT 0 COMMENT '新增备件数',
    
    -- 🎮 游戏化收益统计
    points_earned INT DEFAULT 0 COMMENT '获得积分',
    coins_earned INT DEFAULT 0 COMMENT '获得金币',
    diamonds_earned INT DEFAULT 0 COMMENT '获得钻石',
    xp_earned INT DEFAULT 0 COMMENT '获得经验',
    
    -- 📊 排名缓存 (提升查询性能)
    points_rank INT DEFAULT 0 COMMENT '积分排名',
    productivity_rank INT DEFAULT 0 COMMENT '生产力排名',
    
    -- 🔄 版本控制
    version INT DEFAULT 1 COMMENT '数据版本',
    calculated_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '计算时间',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- 索引优化
    UNIQUE KEY uk_user_period (user_id, period_type, period_start_date),
    INDEX idx_period_rank (period_type, period_start_date, points_rank),
    INDEX idx_user_period_type (user_id, period_type),
    INDEX idx_calculated_at (calculated_at)
) ENGINE=InnoDB COMMENT='用户工作统计汇总表';

-- =====================================================
-- 实时排行榜缓存表 (Redis备份)
-- =====================================================
CREATE TABLE leaderboard_cache (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    board_type VARCHAR(20) NOT NULL COMMENT '排行榜类型',
    period_type VARCHAR(10) NOT NULL COMMENT '统计周期',
    rank_position INT NOT NULL COMMENT '排名位置',
    user_id INT NOT NULL COMMENT '用户ID',
    score_value DECIMAL(10,2) NOT NULL COMMENT '得分值',
    metadata JSON COMMENT '扩展数据',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_board_period_rank (board_type, period_type, rank_position),
    INDEX idx_board_type_period (board_type, period_type),
    INDEX idx_user_score (user_id, score_value)
) ENGINE=InnoDB COMMENT='排行榜缓存表';

-- =====================================================
-- 游戏化规则配置表 (动态配置)
-- =====================================================
CREATE TABLE gamification_rule_config (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    behavior_code VARCHAR(50) NOT NULL UNIQUE,
    behavior_name VARCHAR(100) NOT NULL,
    module_name VARCHAR(50) NOT NULL,
    
    -- 奖励配置
    base_points INT DEFAULT 0 COMMENT '基础积分',
    base_coins INT DEFAULT 0 COMMENT '基础金币',
    base_xp INT DEFAULT 0 COMMENT '基础经验',
    base_diamonds INT DEFAULT 0 COMMENT '基础钻石',
    
    -- 高级规则
    bonus_rules JSON COMMENT '奖励规则配置',
    daily_limit INT DEFAULT -1 COMMENT '每日限制(-1无限制)',
    cooldown_minutes INT DEFAULT 0 COMMENT '冷却时间(分钟)',
    
    -- 权重配置
    productivity_weight DECIMAL(3,2) DEFAULT 1.00 COMMENT '生产力权重',
    
    -- 状态控制
    is_active BOOLEAN DEFAULT TRUE,
    effective_date DATE DEFAULT (CURDATE()) COMMENT '生效日期',
    
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_module_active (module_name, is_active),
    INDEX idx_behavior_active (behavior_code, is_active)
) ENGINE=InnoDB COMMENT='游戏化规则配置表';

-- =====================================================
-- 初始化游戏化规则数据
-- =====================================================
INSERT INTO gamification_rule_config (behavior_code, behavior_name, module_name, base_points, base_coins, base_xp, productivity_weight) VALUES
-- 任务模块
('TASK_CREATE', '新建任务', 'Task', 10, 5, 15, 1.0),
('TASK_CLAIM', '领取任务', 'Task', 5, 2, 8, 1.2),
('TASK_COMPLETE', '完成任务', 'Task', 25, 12, 35, 3.0),
('TASK_COMMENT', '评论任务', 'Task', 3, 1, 5, 0.5),

-- 资产模块  
('ASSET_CREATE', '新建资产', 'Asset', 15, 8, 20, 2.0),
('ASSET_UPDATE', '更新资产', 'Asset', 8, 4, 12, 1.5),
('ASSET_DELETE', '删除资产', 'Asset', 5, 2, 8, 1.0),

-- 故障模块
('FAULT_REPORT', '登记故障', 'Fault', 12, 6, 18, 2.0),
('FAULT_REPAIR', '故障维修', 'Fault', 20, 10, 25, 2.5),

-- 采购模块
('PROCUREMENT_CREATE', '新建采购单', 'Procurement', 18, 10, 25, 1.5),
('PROCUREMENT_UPDATE', '更新采购进度', 'Procurement', 8, 4, 12, 1.0),

-- 备件模块
('INVENTORY_IN', '备件入库', 'Inventory', 10, 5, 15, 1.0),
('INVENTORY_OUT', '备件出库', 'Inventory', 8, 4, 12, 1.0),
('INVENTORY_ADD', '新增备件', 'Inventory', 12, 6, 18, 1.0);
```

### 2. 高性能视图

```sql
-- =====================================================
-- 实时排行榜视图 (优化查询性能)
-- =====================================================

-- 积分排行榜视图
CREATE VIEW v_points_leaderboard AS
SELECT 
    ROW_NUMBER() OVER (ORDER BY us.PointsBalance DESC, us.XpBalance DESC) as rank_no,
    u.id as user_id,
    u.name as user_name,
    d.name as department_name,
    us.PointsBalance as total_points,
    us.current_level,
    ul.name as level_name,
    ul.color as level_color,
    
    -- 本周数据
    COALESCE(uws.points_earned, 0) as weekly_points,
    COALESCE(uws.tasks_completed, 0) as weekly_tasks,
    COALESCE(uws.assets_created + uws.assets_updated, 0) as weekly_assets,
    
    -- 趋势数据
    COALESCE(prev_week.points_earned, 0) as prev_weekly_points,
    ROUND(
        CASE 
            WHEN COALESCE(prev_week.points_earned, 0) > 0 
            THEN ((COALESCE(uws.points_earned, 0) - COALESCE(prev_week.points_earned, 0)) * 100.0 / COALESCE(prev_week.points_earned, 0))
            ELSE 0 
        END, 1
    ) as growth_rate,
    
    us.updated_at
FROM users u
JOIN user_stats us ON u.id = us.UserId
LEFT JOIN user_levels ul ON us.current_level = ul.level
LEFT JOIN departments d ON u.department_id = d.id
LEFT JOIN user_work_statistics uws ON u.id = uws.user_id 
    AND uws.period_type = 'weekly' 
    AND uws.period_start_date = DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY)
LEFT JOIN user_work_statistics prev_week ON u.id = prev_week.user_id
    AND prev_week.period_type = 'weekly'
    AND prev_week.period_start_date = DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) + 7 DAY)
WHERE u.IsDeleted = 0
ORDER BY us.PointsBalance DESC, us.XpBalance DESC;

-- 生产力排行榜视图
CREATE VIEW v_productivity_leaderboard AS
SELECT 
    ROW_NUMBER() OVER (ORDER BY productivity_score DESC) as rank_no,
    uws.user_id,
    u.name as user_name,
    d.name as department_name,
    
    -- 生产力得分计算
    (uws.tasks_completed * 3.0 + 
     (uws.assets_created + uws.assets_updated) * 2.0 + 
     uws.faults_reported * 2.0 + 
     uws.procurements_created * 1.5 + 
     (uws.parts_in + uws.parts_out + uws.parts_added) * 1.0
    ) as productivity_score,
    
    -- 详细统计
    uws.tasks_completed,
    uws.assets_created + uws.assets_updated as assets_managed,
    uws.faults_reported,
    uws.procurements_created,
    uws.parts_in + uws.parts_out + uws.parts_added as parts_managed,
    uws.points_earned,
    
    -- 专业特长标识
    CASE 
        WHEN uws.tasks_completed >= GREATEST(uws.assets_created, uws.faults_reported, uws.procurements_created) THEN '任务专家'
        WHEN uws.assets_created >= GREATEST(uws.tasks_completed, uws.faults_reported, uws.procurements_created) THEN '资产能手'
        WHEN uws.faults_reported >= GREATEST(uws.tasks_completed, uws.assets_created, uws.procurements_created) THEN '维修达人'
        WHEN uws.procurements_created >= GREATEST(uws.tasks_completed, uws.assets_created, uws.faults_reported) THEN '采购专家'
        ELSE '全能选手'
    END as specialty,
    
    uws.period_start_date
FROM user_work_statistics uws
JOIN users u ON uws.user_id = u.id
LEFT JOIN departments d ON u.department_id = d.id
WHERE u.IsDeleted = 0 
    AND uws.period_type = 'weekly'
    AND uws.period_start_date = DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY)
ORDER BY productivity_score DESC;
```

---

## 🚀 后端实现 (.NET 8)

### 1. 领域模型设计

```csharp
// Models/Statistics/UserWorkStatistics.cs
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ItAssetsSystem.Models.Statistics
{
    [Table("user_work_statistics")]
    public class UserWorkStatistics
    {
        [Key]
        public long Id { get; set; }
        
        [Column("user_id")]
        public int UserId { get; set; }
        
        [Column("period_type")]
        public PeriodType PeriodType { get; set; }
        
        [Column("period_start_date")]
        public DateTime PeriodStartDate { get; set; }
        
        // 任务统计
        [Column("tasks_created")]
        public int TasksCreated { get; set; }
        
        [Column("tasks_claimed")]
        public int TasksClaimed { get; set; }
        
        [Column("tasks_completed")]
        public int TasksCompleted { get; set; }
        
        [Column("tasks_commented")]
        public int TasksCommented { get; set; }
        
        // 资产统计
        [Column("assets_created")]
        public int AssetsCreated { get; set; }
        
        [Column("assets_updated")]
        public int AssetsUpdated { get; set; }
        
        [Column("assets_deleted")]
        public int AssetsDeleted { get; set; }
        
        // 故障统计
        [Column("faults_reported")]
        public int FaultsReported { get; set; }
        
        [Column("faults_repaired")]
        public int FaultsRepaired { get; set; }
        
        // 采购统计
        [Column("procurements_created")]
        public int ProcurementsCreated { get; set; }
        
        [Column("procurements_updated")]
        public int ProcurementsUpdated { get; set; }
        
        // 备件统计
        [Column("parts_in")]
        public int PartsIn { get; set; }
        
        [Column("parts_out")]
        public int PartsOut { get; set; }
        
        [Column("parts_added")]
        public int PartsAdded { get; set; }
        
        // 游戏化收益
        [Column("points_earned")]
        public int PointsEarned { get; set; }
        
        [Column("coins_earned")]
        public int CoinsEarned { get; set; }
        
        [Column("diamonds_earned")]
        public int DiamondsEarned { get; set; }
        
        [Column("xp_earned")]
        public int XpEarned { get; set; }
        
        // 排名缓存
        [Column("points_rank")]
        public int PointsRank { get; set; }
        
        [Column("productivity_rank")]
        public int ProductivityRank { get; set; }
        
        // 版本控制
        [Column("version")]
        public int Version { get; set; } = 1;
        
        [Column("calculated_at")]
        public DateTime CalculatedAt { get; set; } = DateTime.UtcNow;
        
        [Column("created_at")]
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        
        [Column("updated_at")]
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
        
        // 导航属性
        public virtual User User { get; set; }
        
        // 计算属性
        [NotMapped]
        public int TotalTasks => TasksCreated + TasksClaimed + TasksCompleted + TasksCommented;
        
        [NotMapped]
        public int TotalAssets => AssetsCreated + AssetsUpdated + AssetsDeleted;
        
        [NotMapped]
        public int TotalFaults => FaultsReported + FaultsRepaired;
        
        [NotMapped]
        public int TotalProcurements => ProcurementsCreated + ProcurementsUpdated;
        
        [NotMapped]
        public int TotalParts => PartsIn + PartsOut + PartsAdded;
        
        [NotMapped]
        public double ProductivityScore => 
            TasksCompleted * 3.0 + 
            (AssetsCreated + AssetsUpdated) * 2.0 + 
            FaultsReported * 2.0 + 
            ProcurementsCreated * 1.5 + 
            (PartsIn + PartsOut + PartsAdded) * 1.0;
    }

    public enum PeriodType
    {
        Daily,
        Weekly, 
        Monthly
    }
}

// DTOs/Statistics/UserWorkSummaryDto.cs
namespace ItAssetsSystem.DTOs.Statistics
{
    public class UserWorkSummaryDto
    {
        public int UserId { get; set; }
        public string UserName { get; set; }
        public string DepartmentName { get; set; }
        public string PeriodType { get; set; }
        public DateTime PeriodStartDate { get; set; }
        
        // 任务统计
        public int TasksCreated { get; set; }
        public int TasksClaimed { get; set; }
        public int TasksCompleted { get; set; }
        public int TasksCommented { get; set; }
        public int TasksTotal { get; set; }
        
        // 资产统计
        public int AssetsCreated { get; set; }
        public int AssetsUpdated { get; set; }
        public int AssetsDeleted { get; set; }
        public int AssetsTotal { get; set; }
        
        // 故障统计
        public int FaultsReported { get; set; }
        public int FaultsRepaired { get; set; }
        public int FaultsTotal { get; set; }
        
        // 采购统计
        public int ProcurementsCreated { get; set; }
        public int ProcurementsUpdated { get; set; }
        public int ProcurementsTotal { get; set; }
        
        // 备件统计
        public int PartsIn { get; set; }
        public int PartsOut { get; set; }
        public int PartsAdded { get; set; }
        public int PartsTotal { get; set; }
        
        // 游戏化收益
        public int PointsEarned { get; set; }
        public int CoinsEarned { get; set; }
        public int DiamondsEarned { get; set; }
        public int XpEarned { get; set; }
        
        // 排名信息
        public int PointsRank { get; set; }
        public int ProductivityRank { get; set; }
        
        // 扩展信息
        public double ProductivityScore { get; set; }
        public string Evaluation { get; set; }
        public string Specialty { get; set; }
        public double GrowthRate { get; set; }
    }

    public class LeaderboardItemDto
    {
        public int RankNo { get; set; }
        public int UserId { get; set; }
        public string UserName { get; set; }
        public string DepartmentName { get; set; }
        public double ScoreValue { get; set; }
        public string ScoreLabel { get; set; }
        public int CurrentLevel { get; set; }
        public string LevelName { get; set; }
        public string LevelColor { get; set; }
        public string Specialty { get; set; }
        public double GrowthRate { get; set; }
        public Dictionary<string, object> Metadata { get; set; } = new();
    }
}
```

### 2. 核心服务实现

```csharp
// Services/Statistics/IStatisticsService.cs
namespace ItAssetsSystem.Services.Statistics
{
    public interface IStatisticsService
    {
        Task<List<UserWorkSummaryDto>> GetWorkSummaryAsync(string periodType, DateTime? periodDate = null, int limit = 50);
        Task<List<LeaderboardItemDto>> GetLeaderboardAsync(string boardType, int limit = 20);
        Task<UserDetailedStatsDto> GetUserDetailedStatsAsync(int userId, string periodType = "weekly");
        Task UpdateStatisticsAsync(string periodType, DateTime? targetDate = null);
        Task<bool> TriggerRealTimeUpdateAsync(int userId, string behaviorCode);
    }
}

// Services/Statistics/StatisticsService.cs
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.AspNetCore.SignalR;
using System.Text.Json;

namespace ItAssetsSystem.Services.Statistics
{
    public class StatisticsService : IStatisticsService
    {
        private readonly ApplicationDbContext _context;
        private readonly IMemoryCache _memoryCache;
        private readonly IDistributedCache _distributedCache;
        private readonly IHubContext<StatisticsHub> _hubContext;
        private readonly ILogger<StatisticsService> _logger;

        public StatisticsService(
            ApplicationDbContext context,
            IMemoryCache memoryCache,
            IDistributedCache distributedCache,
            IHubContext<StatisticsHub> hubContext,
            ILogger<StatisticsService> logger)
        {
            _context = context;
            _memoryCache = memoryCache;
            _distributedCache = distributedCache;
            _hubContext = hubContext;
            _logger = logger;
        }

        public async Task<List<UserWorkSummaryDto>> GetWorkSummaryAsync(
            string periodType, DateTime? periodDate = null, int limit = 50)
        {
            var targetDate = periodDate ?? DateTime.Today;
            var periodStartDate = GetPeriodStartDate(periodType, targetDate);
            var cacheKey = $"work_summary_{periodType}_{periodStartDate:yyyyMMdd}_{limit}";

            // L1 缓存: 内存缓存 (1分钟)
            if (_memoryCache.TryGetValue(cacheKey, out List<UserWorkSummaryDto> cached))
            {
                _logger.LogDebug("工作汇总数据命中内存缓存: {CacheKey}", cacheKey);
                return cached;
            }

            // L2 缓存: Redis缓存 (5分钟)
            var redisData = await _distributedCache.GetStringAsync(cacheKey);
            if (!string.IsNullOrEmpty(redisData))
            {
                var data = JsonSerializer.Deserialize<List<UserWorkSummaryDto>>(redisData);
                _memoryCache.Set(cacheKey, data, TimeSpan.FromMinutes(1));
                _logger.LogDebug("工作汇总数据命中Redis缓存: {CacheKey}", cacheKey);
                return data;
            }

            // L3: 数据库查询
            _logger.LogInformation("从数据库查询工作汇总数据: {PeriodType}, {PeriodDate}", periodType, periodStartDate);

            var query = from uws in _context.UserWorkStatistics
                       join u in _context.Users on uws.UserId equals u.Id
                       join d in _context.Departments on u.DepartmentId equals d.Id into deptJoin
                       from dept in deptJoin.DefaultIfEmpty()
                       where uws.PeriodType == Enum.Parse<PeriodType>(periodType, true)
                             && uws.PeriodStartDate == periodStartDate
                             && !u.IsDeleted
                       orderby uws.ProductivityRank
                       select new UserWorkSummaryDto
                       {
                           UserId = uws.UserId,
                           UserName = u.Name,
                           DepartmentName = dept != null ? dept.Name : "未分配",
                           PeriodType = periodType,
                           PeriodStartDate = uws.PeriodStartDate,
                           
                           // 任务统计
                           TasksCreated = uws.TasksCreated,
                           TasksClaimed = uws.TasksClaimed,
                           TasksCompleted = uws.TasksCompleted,
                           TasksCommented = uws.TasksCommented,
                           TasksTotal = uws.TasksCreated + uws.TasksClaimed + uws.TasksCompleted + uws.TasksCommented,
                           
                           // 资产统计
                           AssetsCreated = uws.AssetsCreated,
                           AssetsUpdated = uws.AssetsUpdated,
                           AssetsDeleted = uws.AssetsDeleted,
                           AssetsTotal = uws.AssetsCreated + uws.AssetsUpdated + uws.AssetsDeleted,
                           
                           // 故障统计
                           FaultsReported = uws.FaultsReported,
                           FaultsRepaired = uws.FaultsRepaired,
                           FaultsTotal = uws.FaultsReported + uws.FaultsRepaired,
                           
                           // 采购统计
                           ProcurementsCreated = uws.ProcurementsCreated,
                           ProcurementsUpdated = uws.ProcurementsUpdated,
                           ProcurementsTotal = uws.ProcurementsCreated + uws.ProcurementsUpdated,
                           
                           // 备件统计
                           PartsIn = uws.PartsIn,
                           PartsOut = uws.PartsOut,
                           PartsAdded = uws.PartsAdded,
                           PartsTotal = uws.PartsIn + uws.PartsOut + uws.PartsAdded,
                           
                           // 游戏化收益
                           PointsEarned = uws.PointsEarned,
                           CoinsEarned = uws.CoinsEarned,
                           DiamondsEarned = uws.DiamondsEarned,
                           XpEarned = uws.XpEarned,
                           
                           // 排名
                           PointsRank = uws.PointsRank,
                           ProductivityRank = uws.ProductivityRank,
                           
                           // 扩展信息
                           ProductivityScore = uws.ProductivityScore,
                           Evaluation = GetEvaluation(uws.ProductivityRank),
                           Specialty = GetSpecialty(uws)
                       };

            var result = await query.Take(limit).ToListAsync();

            // 计算增长率
            await CalculateGrowthRatesAsync(result, periodType, periodStartDate);

            // 写入缓存
            var jsonOptions = new JsonSerializerOptions { PropertyNamingPolicy = JsonNamingPolicy.CamelCase };
            var serializedData = JsonSerializer.Serialize(result, jsonOptions);
            
            await _distributedCache.SetStringAsync(cacheKey, serializedData, new DistributedCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(5)
            });
            
            _memoryCache.Set(cacheKey, result, TimeSpan.FromMinutes(1));

            _logger.LogInformation("工作汇总查询完成，返回 {Count} 条记录", result.Count);
            return result;
        }

        public async Task<List<LeaderboardItemDto>> GetLeaderboardAsync(string boardType, int limit = 20)
        {
            var cacheKey = $"leaderboard_{boardType}_{limit}";

            // L1 缓存检查
            if (_memoryCache.TryGetValue(cacheKey, out List<LeaderboardItemDto> cached))
            {
                return cached;
            }

            // L2 Redis缓存检查
            var redisData = await _distributedCache.GetStringAsync(cacheKey);
            if (!string.IsNullOrEmpty(redisData))
            {
                var data = JsonSerializer.Deserialize<List<LeaderboardItemDto>>(redisData);
                _memoryCache.Set(cacheKey, data, TimeSpan.FromMinutes(2));
                return data;
            }

            // 数据库查询
            List<LeaderboardItemDto> result;

            switch (boardType.ToLower())
            {
                case "points":
                    result = await GetPointsLeaderboardAsync(limit);
                    break;
                case "coins":
                    result = await GetCoinsLeaderboardAsync(limit);
                    break;
                case "diamonds":
                    result = await GetDiamondsLeaderboardAsync(limit);
                    break;
                case "productivity":
                    result = await GetProductivityLeaderboardAsync(limit);
                    break;
                case "items":
                    result = await GetItemsLeaderboardAsync(limit);
                    break;
                default:
                    throw new ArgumentException($"不支持的排行榜类型: {boardType}");
            }

            // 写入缓存
            var jsonData = JsonSerializer.Serialize(result);
            await _distributedCache.SetStringAsync(cacheKey, jsonData, new DistributedCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(3)
            });
            _memoryCache.Set(cacheKey, result, TimeSpan.FromMinutes(2));

            return result;
        }

        private async Task<List<LeaderboardItemDto>> GetPointsLeaderboardAsync(int limit)
        {
            var query = _context.Database.SqlQuery<LeaderboardItemDto>($@"
                SELECT 
                    rank_no as RankNo,
                    user_id as UserId,
                    user_name as UserName,
                    department_name as DepartmentName,
                    total_points as ScoreValue,
                    CONCAT(total_points, ' 积分') as ScoreLabel,
                    current_level as CurrentLevel,
                    level_name as LevelName,
                    level_color as LevelColor,
                    CASE 
                        WHEN weekly_tasks >= GREATEST(weekly_assets, 0) THEN '任务专家'
                        WHEN weekly_assets >= GREATEST(weekly_tasks, 0) THEN '资产能手'
                        ELSE '全能选手'
                    END as Specialty,
                    growth_rate as GrowthRate
                FROM v_points_leaderboard 
                ORDER BY rank_no 
                LIMIT {limit}");

            return await query.ToListAsync();
        }

        private async Task<List<LeaderboardItemDto>> GetProductivityLeaderboardAsync(int limit)
        {
            var query = _context.Database.SqlQuery<LeaderboardItemDto>($@"
                SELECT 
                    rank_no as RankNo,
                    user_id as UserId,
                    user_name as UserName,
                    department_name as DepartmentName,
                    productivity_score as ScoreValue,
                    CONCAT(ROUND(productivity_score, 1), ' 生产力') as ScoreLabel,
                    0 as CurrentLevel,
                    specialty as LevelName,
                    '#FF6B35' as LevelColor,
                    specialty as Specialty,
                    0 as GrowthRate
                FROM v_productivity_leaderboard 
                ORDER BY rank_no 
                LIMIT {limit}");

            return await query.ToListAsync();
        }

        public async Task UpdateStatisticsAsync(string periodType, DateTime? targetDate = null)
        {
            var date = targetDate ?? DateTime.Today;
            var periodStartDate = GetPeriodStartDate(periodType, date);
            var periodEndDate = GetPeriodEndDate(periodType, date);

            _logger.LogInformation("开始更新统计数据: {PeriodType}, {StartDate} - {EndDate}", 
                periodType, periodStartDate, periodEndDate);

            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                // 1. 计算统计数据
                var userStats = await CalculateUserStatisticsAsync(periodStartDate, periodEndDate);

                // 2. 批量更新或插入统计记录
                await UpsertUserStatisticsAsync(userStats, periodType, periodStartDate);

                // 3. 更新排名
                await UpdateRankingsAsync(periodType, periodStartDate);

                // 4. 清理缓存
                await ClearStatisticsCacheAsync(periodType, periodStartDate);

                // 5. 推送实时更新
                await BroadcastStatisticsUpdateAsync(periodType);

                await transaction.CommitAsync();
                _logger.LogInformation("统计数据更新完成: {PeriodType}, 影响用户数: {UserCount}", 
                    periodType, userStats.Count);
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, "统计数据更新失败: {PeriodType}, {Error}", periodType, ex.Message);
                throw;
            }
        }

        private async Task<List<UserStatisticsCalculation>> CalculateUserStatisticsAsync(
            DateTime startDate, DateTime endDate)
        {
            var query = from u in _context.Users
                       where !u.IsDeleted
                       select new UserStatisticsCalculation
                       {
                           UserId = u.Id,
                           UserName = u.Name,
                           DepartmentName = u.Department != null ? u.Department.Name : "未分配",
                           
                           // 使用子查询统计各模块数据
                           TasksCreated = _context.GamificationLogs
                               .Count(gl => gl.UserId == u.Id && gl.BehaviorCode == "TASK_CREATE" &&
                                          gl.CreatedAt >= startDate && gl.CreatedAt < endDate),
                           
                           TasksClaimed = _context.GamificationLogs
                               .Count(gl => gl.UserId == u.Id && gl.BehaviorCode == "TASK_CLAIM" &&
                                          gl.CreatedAt >= startDate && gl.CreatedAt < endDate),
                                          
                           TasksCompleted = _context.GamificationLogs
                               .Count(gl => gl.UserId == u.Id && gl.BehaviorCode == "TASK_COMPLETE" &&
                                          gl.CreatedAt >= startDate && gl.CreatedAt < endDate),
                                          
                           TasksCommented = _context.GamificationLogs
                               .Count(gl => gl.UserId == u.Id && gl.BehaviorCode == "TASK_COMMENT" &&
                                          gl.CreatedAt >= startDate && gl.CreatedAt < endDate),
                           
                           // 资产统计
                           AssetsCreated = _context.GamificationLogs
                               .Count(gl => gl.UserId == u.Id && gl.BehaviorCode == "ASSET_CREATE" &&
                                          gl.CreatedAt >= startDate && gl.CreatedAt < endDate),
                                          
                           AssetsUpdated = _context.GamificationLogs
                               .Count(gl => gl.UserId == u.Id && gl.BehaviorCode == "ASSET_UPDATE" &&
                                          gl.CreatedAt >= startDate && gl.CreatedAt < endDate),
                           
                           // 故障统计
                           FaultsReported = _context.GamificationLogs
                               .Count(gl => gl.UserId == u.Id && gl.BehaviorCode == "FAULT_REPORT" &&
                                          gl.CreatedAt >= startDate && gl.CreatedAt < endDate),
                           
                           // 采购统计
                           ProcurementsCreated = _context.GamificationLogs
                               .Count(gl => gl.UserId == u.Id && gl.BehaviorCode == "PROCUREMENT_CREATE" &&
                                          gl.CreatedAt >= startDate && gl.CreatedAt < endDate),
                           
                           // 备件统计
                           PartsIn = _context.GamificationLogs
                               .Count(gl => gl.UserId == u.Id && gl.BehaviorCode == "INVENTORY_IN" &&
                                          gl.CreatedAt >= startDate && gl.CreatedAt < endDate),
                           
                           // 游戏化收益汇总
                           PointsEarned = _context.GamificationLogs
                               .Where(gl => gl.UserId == u.Id && gl.CreatedAt >= startDate && gl.CreatedAt < endDate)
                               .Sum(gl => gl.PointsGained ?? 0),
                               
                           CoinsEarned = _context.GamificationLogs
                               .Where(gl => gl.UserId == u.Id && gl.CreatedAt >= startDate && gl.CreatedAt < endDate)
                               .Sum(gl => gl.CoinsGained ?? 0),
                               
                           XpEarned = _context.GamificationLogs
                               .Where(gl => gl.UserId == u.Id && gl.CreatedAt >= startDate && gl.CreatedAt < endDate)
                               .Sum(gl => gl.XpGained ?? 0)
                       };

            return await query.ToListAsync();
        }

        private async Task UpsertUserStatisticsAsync(
            List<UserStatisticsCalculation> userStats, 
            string periodType, 
            DateTime periodStartDate)
        {
            var periodTypeEnum = Enum.Parse<PeriodType>(periodType, true);
            
            foreach (var stat in userStats)
            {
                var existing = await _context.UserWorkStatistics
                    .FirstOrDefaultAsync(uws => uws.UserId == stat.UserId &&
                                               uws.PeriodType == periodTypeEnum &&
                                               uws.PeriodStartDate == periodStartDate);

                if (existing != null)
                {
                    // 更新现有记录
                    existing.TasksCreated = stat.TasksCreated;
                    existing.TasksClaimed = stat.TasksClaimed;
                    existing.TasksCompleted = stat.TasksCompleted;
                    existing.TasksCommented = stat.TasksCommented;
                    existing.AssetsCreated = stat.AssetsCreated;
                    existing.AssetsUpdated = stat.AssetsUpdated;
                    existing.FaultsReported = stat.FaultsReported;
                    existing.ProcurementsCreated = stat.ProcurementsCreated;
                    existing.PartsIn = stat.PartsIn;
                    existing.PointsEarned = stat.PointsEarned;
                    existing.CoinsEarned = stat.CoinsEarned;
                    existing.XpEarned = stat.XpEarned;
                    existing.Version++;
                    existing.CalculatedAt = DateTime.UtcNow;
                    existing.UpdatedAt = DateTime.UtcNow;
                }
                else
                {
                    // 插入新记录
                    var newStat = new UserWorkStatistics
                    {
                        UserId = stat.UserId,
                        PeriodType = periodTypeEnum,
                        PeriodStartDate = periodStartDate,
                        TasksCreated = stat.TasksCreated,
                        TasksClaimed = stat.TasksClaimed,
                        TasksCompleted = stat.TasksCompleted,
                        TasksCommented = stat.TasksCommented,
                        AssetsCreated = stat.AssetsCreated,
                        AssetsUpdated = stat.AssetsUpdated,
                        FaultsReported = stat.FaultsReported,
                        ProcurementsCreated = stat.ProcurementsCreated,
                        PartsIn = stat.PartsIn,
                        PointsEarned = stat.PointsEarned,
                        CoinsEarned = stat.CoinsEarned,
                        XpEarned = stat.XpEarned
                    };
                    
                    _context.UserWorkStatistics.Add(newStat);
                }
            }

            await _context.SaveChangesAsync();
        }

        private async Task UpdateRankingsAsync(string periodType, DateTime periodStartDate)
        {
            var periodTypeEnum = Enum.Parse<PeriodType>(periodType, true);

            // 更新积分排名
            var pointsRankings = await _context.UserWorkStatistics
                .Where(uws => uws.PeriodType == periodTypeEnum && uws.PeriodStartDate == periodStartDate)
                .OrderByDescending(uws => uws.PointsEarned)
                .Select((uws, index) => new { uws.Id, Rank = index + 1 })
                .ToListAsync();

            foreach (var ranking in pointsRankings)
            {
                await _context.Database.ExecuteSqlRawAsync(
                    "UPDATE user_work_statistics SET points_rank = {0} WHERE id = {1}",
                    ranking.Rank, ranking.Id);
            }

            // 更新生产力排名
            var productivityRankings = await _context.UserWorkStatistics
                .Where(uws => uws.PeriodType == periodTypeEnum && uws.PeriodStartDate == periodStartDate)
                .OrderByDescending(uws => uws.ProductivityScore)
                .Select((uws, index) => new { uws.Id, Rank = index + 1 })
                .ToListAsync();

            foreach (var ranking in productivityRankings)
            {
                await _context.Database.ExecuteSqlRawAsync(
                    "UPDATE user_work_statistics SET productivity_rank = {0} WHERE id = {1}",
                    ranking.Rank, ranking.Id);
            }
        }

        public async Task<bool> TriggerRealTimeUpdateAsync(int userId, string behaviorCode)
        {
            try
            {
                // 1. 更新用户实时统计缓存
                await InvalidateUserCacheAsync(userId);

                // 2. 如果是重要行为，触发排行榜更新
                var importantBehaviors = new[] { "TASK_COMPLETE", "ASSET_CREATE", "FAULT_REPORT" };
                if (importantBehaviors.Contains(behaviorCode))
                {
                    await InvalidateLeaderboardCacheAsync();
                    
                    // 3. 推送实时更新到客户端
                    await _hubContext.Clients.All.SendAsync("UserStatsUpdated", new 
                    { 
                        UserId = userId, 
                        BehaviorCode = behaviorCode,
                        Timestamp = DateTime.UtcNow 
                    });
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "实时更新失败: UserId={UserId}, BehaviorCode={BehaviorCode}", userId, behaviorCode);
                return false;
            }
        }

        // 辅助方法
        private DateTime GetPeriodStartDate(string periodType, DateTime date)
        {
            return periodType.ToLower() switch
            {
                "daily" => date.Date,
                "weekly" => date.Date.AddDays(-(int)date.DayOfWeek + 1), // 周一
                "monthly" => new DateTime(date.Year, date.Month, 1),
                _ => throw new ArgumentException($"不支持的周期类型: {periodType}")
            };
        }

        private DateTime GetPeriodEndDate(string periodType, DateTime date)
        {
            return periodType.ToLower() switch
            {
                "daily" => date.Date.AddDays(1),
                "weekly" => GetPeriodStartDate("weekly", date).AddDays(7),
                "monthly" => new DateTime(date.Year, date.Month, 1).AddMonths(1),
                _ => throw new ArgumentException($"不支持的周期类型: {periodType}")
            };
        }

        private string GetEvaluation(int rank)
        {
            return rank switch
            {
                <= 3 => "🥇 超级明星",
                <= 10 => "🏆 优秀员工",
                <= 20 => "⭐ 努力奋斗",
                _ => "💪 稳步提升"
            };
        }

        private string GetSpecialty(UserWorkStatistics uws)
        {
            var scores = new Dictionary<string, int>
            {
                ["任务专家"] = uws.TasksCompleted,
                ["资产能手"] = uws.AssetsCreated + uws.AssetsUpdated,
                ["维修达人"] = uws.FaultsReported,
                ["采购专家"] = uws.ProcurementsCreated
            };

            return scores.OrderByDescending(x => x.Value).First().Key;
        }

        private async Task ClearStatisticsCacheAsync(string periodType, DateTime periodDate)
        {
            var patterns = new[]
            {
                $"work_summary_{periodType}_{periodDate:yyyyMMdd}_*",
                "leaderboard_*"
            };

            foreach (var pattern in patterns)
            {
                // 清理内存缓存 (简化实现，实际应该基于键模式清理)
                _memoryCache.Remove(pattern);
                
                // 清理Redis缓存 (实际应该使用SCAN命令匹配模式)
                await _distributedCache.RemoveAsync(pattern);
            }
        }

        private async Task BroadcastStatisticsUpdateAsync(string periodType)
        {
            await _hubContext.Clients.All.SendAsync("StatisticsUpdated", new 
            { 
                PeriodType = periodType,
                UpdateTime = DateTime.UtcNow,
                Message = $"{periodType}统计数据已更新"
            });
        }

        private async Task InvalidateUserCacheAsync(int userId)
        {
            var cacheKeys = new[]
            {
                $"user_stats_{userId}",
                $"user_detailed_{userId}_*"
            };

            foreach (var key in cacheKeys)
            {
                _memoryCache.Remove(key);
                await _distributedCache.RemoveAsync(key);
            }
        }

        private async Task InvalidateLeaderboardCacheAsync()
        {
            var boardTypes = new[] { "points", "coins", "diamonds", "productivity", "items" };
            
            foreach (var boardType in boardTypes)
            {
                var cacheKey = $"leaderboard_{boardType}_20";
                _memoryCache.Remove(cacheKey);
                await _distributedCache.RemoveAsync(cacheKey);
            }
        }
    }

    // 内部计算模型
    internal class UserStatisticsCalculation
    {
        public int UserId { get; set; }
        public string UserName { get; set; }
        public string DepartmentName { get; set; }
        
        public int TasksCreated { get; set; }
        public int TasksClaimed { get; set; }
        public int TasksCompleted { get; set; }
        public int TasksCommented { get; set; }
        
        public int AssetsCreated { get; set; }
        public int AssetsUpdated { get; set; }
        public int AssetsDeleted { get; set; }
        
        public int FaultsReported { get; set; }
        public int FaultsRepaired { get; set; }
        
        public int ProcurementsCreated { get; set; }
        public int ProcurementsUpdated { get; set; }
        
        public int PartsIn { get; set; }
        public int PartsOut { get; set; }
        public int PartsAdded { get; set; }
        
        public int PointsEarned { get; set; }
        public int CoinsEarned { get; set; }
        public int DiamondsEarned { get; set; }
        public int XpEarned { get; set; }
    }
}
```

### 3. SignalR实时通信

```csharp
// Hubs/StatisticsHub.cs
using Microsoft.AspNetCore.SignalR;
using Microsoft.AspNetCore.Authorization;

namespace ItAssetsSystem.Hubs
{
    [Authorize]
    public class StatisticsHub : Hub
    {
        private readonly ILogger<StatisticsHub> _logger;

        public StatisticsHub(ILogger<StatisticsHub> logger)
        {
            _logger = logger;
        }

        public async Task JoinStatisticsGroup()
        {
            await Groups.AddToGroupAsync(Context.ConnectionId, "Statistics");
            _logger.LogDebug("用户 {UserId} 加入统计数据推送组", Context.UserIdentifier);
        }

        public async Task LeaveStatisticsGroup()
        {
            await Groups.RemoveFromGroupAsync(Context.ConnectionId, "Statistics");
            _logger.LogDebug("用户 {UserId} 离开统计数据推送组", Context.UserIdentifier);
        }

        public async Task JoinPersonalStatsGroup(int userId)
        {
            var groupName = $"UserStats_{userId}";
            await Groups.AddToGroupAsync(Context.ConnectionId, groupName);
            _logger.LogDebug("用户 {UserId} 加入个人统计推送组", userId);
        }

        public override async Task OnConnectedAsync()
        {
            _logger.LogDebug("SignalR连接建立: {ConnectionId}, 用户: {UserId}", 
                Context.ConnectionId, Context.UserIdentifier);
            await base.OnConnectedAsync();
        }

        public override async Task OnDisconnectedAsync(Exception exception)
        {
            _logger.LogDebug("SignalR连接断开: {ConnectionId}, 用户: {UserId}, 异常: {Exception}", 
                Context.ConnectionId, Context.UserIdentifier, exception?.Message);
            await base.OnDisconnectedAsync(exception);
        }
    }
}
```

### 4. 后台统计更新服务

```csharp
// Services/BackgroundServices/StatisticsUpdateService.cs
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace ItAssetsSystem.Services.BackgroundServices
{
    public class StatisticsUpdateService : BackgroundService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<StatisticsUpdateService> _logger;

        public StatisticsUpdateService(
            IServiceProvider serviceProvider,
            ILogger<StatisticsUpdateService> logger)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("统计更新后台服务启动");

            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    var now = DateTime.Now;

                    // 每日凌晨2:00更新昨日统计
                    if (now.Hour == 2 && now.Minute < 5)
                    {
                        await UpdateDailyStatistics();
                    }

                    // 每周一凌晨3:00更新上周统计
                    if (now.DayOfWeek == DayOfWeek.Monday && now.Hour == 3 && now.Minute < 5)
                    {
                        await UpdateWeeklyStatistics();
                    }

                    // 每月1日凌晨4:00更新上月统计
                    if (now.Day == 1 && now.Hour == 4 && now.Minute < 5)
                    {
                        await UpdateMonthlyStatistics();
                    }

                    // 每小时更新一次当前周期的统计 (实时性)
                    if (now.Minute < 5)
                    {
                        await UpdateCurrentPeriodStatistics();
                    }

                    // 每5分钟检查一次
                    await Task.Delay(TimeSpan.FromMinutes(5), stoppingToken);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "统计更新后台服务执行出错");
                    // 出错后等待10分钟再重试
                    await Task.Delay(TimeSpan.FromMinutes(10), stoppingToken);
                }
            }
        }

        private async Task UpdateDailyStatistics()
        {
            using var scope = _serviceProvider.CreateScope();
            var statisticsService = scope.ServiceProvider.GetRequiredService<IStatisticsService>();

            var yesterday = DateTime.Today.AddDays(-1);
            await statisticsService.UpdateStatisticsAsync("daily", yesterday);

            _logger.LogInformation("昨日统计更新完成: {Date}", yesterday.ToString("yyyy-MM-dd"));
        }

        private async Task UpdateWeeklyStatistics()
        {
            using var scope = _serviceProvider.CreateScope();
            var statisticsService = scope.ServiceProvider.GetRequiredService<IStatisticsService>();

            var lastMonday = DateTime.Today.AddDays(-(int)DateTime.Today.DayOfWeek - 6);
            await statisticsService.UpdateStatisticsAsync("weekly", lastMonday);

            _logger.LogInformation("上周统计更新完成: {StartDate}", lastMonday.ToString("yyyy-MM-dd"));
        }

        private async Task UpdateMonthlyStatistics()
        {
            using var scope = _serviceProvider.CreateScope();
            var statisticsService = scope.ServiceProvider.GetRequiredService<IStatisticsService>();

            var lastMonth = DateTime.Today.AddMonths(-1);
            var firstDayOfLastMonth = new DateTime(lastMonth.Year, lastMonth.Month, 1);
            await statisticsService.UpdateStatisticsAsync("monthly", firstDayOfLastMonth);

            _logger.LogInformation("上月统计更新完成: {Month}", firstDayOfLastMonth.ToString("yyyy-MM"));
        }

        private async Task UpdateCurrentPeriodStatistics()
        {
            using var scope = _serviceProvider.CreateScope();
            var statisticsService = scope.ServiceProvider.GetRequiredService<IStatisticsService>();

            // 更新本周统计 (保持实时性)
            await statisticsService.UpdateStatisticsAsync("weekly", DateTime.Today);

            _logger.LogDebug("本周统计实时更新完成");
        }
    }
}
```

### 5. Web API控制器

```csharp
// Controllers/Api/V2/StatisticsController.cs
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using ItAssetsSystem.Services.Statistics;
using ItAssetsSystem.DTOs.Statistics;
using ItAssetsSystem.Core;

namespace ItAssetsSystem.Controllers.Api.V2
{
    [ApiController]
    [Route("api/v2/[controller]")]
    [Authorize]
    public class StatisticsController : ControllerBase
    {
        private readonly IStatisticsService _statisticsService;
        private readonly ILogger<StatisticsController> _logger;

        public StatisticsController(
            IStatisticsService statisticsService,
            ILogger<StatisticsController> logger)
        {
            _statisticsService = statisticsService;
            _logger = logger;
        }

        /// <summary>
        /// 获取人员工作汇总报告
        /// </summary>
        /// <param name="periodType">统计周期: daily/weekly/monthly</param>
        /// <param name="periodDate">统计日期 (可选)</param>
        /// <param name="limit">返回条数限制</param>
        [HttpGet("work-summary")]
        public async Task<ActionResult<ApiResponse<List<UserWorkSummaryDto>>>> GetWorkSummary(
            [FromQuery] string periodType = "weekly",
            [FromQuery] DateTime? periodDate = null,
            [FromQuery] int limit = 50)
        {
            try
            {
                if (!IsValidPeriodType(periodType))
                {
                    return BadRequest(ApiResponse<object>.CreateFail("无效的统计周期类型"));
                }

                if (limit <= 0 || limit > 100)
                {
                    return BadRequest(ApiResponse<object>.CreateFail("返回条数限制应在1-100之间"));
                }

                var result = await _statisticsService.GetWorkSummaryAsync(periodType, periodDate, limit);

                _logger.LogInformation("工作汇总查询成功: {PeriodType}, {Count}条记录", periodType, result.Count);

                return Ok(ApiResponse<List<UserWorkSummaryDto>>.CreateSuccess(result, "查询成功"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取工作汇总失败: {PeriodType}, {Error}", periodType, ex.Message);
                return StatusCode(500, ApiResponse<object>.CreateFail("获取工作汇总失败"));
            }
        }

        /// <summary>
        /// 获取排行榜数据
        /// </summary>
        /// <param name="boardType">排行榜类型: points/coins/diamonds/productivity/items</param>
        /// <param name="limit">返回条数限制</param>
        [HttpGet("leaderboard/{boardType}")]
        public async Task<ActionResult<ApiResponse<List<LeaderboardItemDto>>>> GetLeaderboard(
            string boardType,
            [FromQuery] int limit = 20)
        {
            try
            {
                if (!IsValidBoardType(boardType))
                {
                    return BadRequest(ApiResponse<object>.CreateFail("无效的排行榜类型"));
                }

                if (limit <= 0 || limit > 50)
                {
                    return BadRequest(ApiResponse<object>.CreateFail("返回条数限制应在1-50之间"));
                }

                var result = await _statisticsService.GetLeaderboardAsync(boardType, limit);

                _logger.LogInformation("排行榜查询成功: {BoardType}, {Count}条记录", boardType, result.Count);

                return Ok(ApiResponse<List<LeaderboardItemDto>>.CreateSuccess(result, "查询成功"));
            }
            catch (ArgumentException ex)
            {
                return BadRequest(ApiResponse<object>.CreateFail(ex.Message));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取排行榜失败: {BoardType}, {Error}", boardType, ex.Message);
                return StatusCode(500, ApiResponse<object>.CreateFail("获取排行榜失败"));
            }
        }

        /// <summary>
        /// 获取用户详细统计
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="periodType">统计周期</param>
        [HttpGet("user/{userId}/detailed")]
        public async Task<ActionResult<ApiResponse<UserDetailedStatsDto>>> GetUserDetailedStats(
            int userId,
            [FromQuery] string periodType = "weekly")
        {
            try
            {
                if (!IsValidPeriodType(periodType))
                {
                    return BadRequest(ApiResponse<object>.CreateFail("无效的统计周期类型"));
                }

                var result = await _statisticsService.GetUserDetailedStatsAsync(userId, periodType);

                return Ok(ApiResponse<UserDetailedStatsDto>.CreateSuccess(result, "查询成功"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取用户详细统计失败: UserId={UserId}, Error={Error}", userId, ex.Message);
                return StatusCode(500, ApiResponse<object>.CreateFail("获取用户详细统计失败"));
            }
        }

        /// <summary>
        /// 手动触发统计更新
        /// </summary>
        /// <param name="periodType">统计周期</param>
        /// <param name="targetDate">目标日期</param>
        [HttpPost("update")]
        [Authorize(Roles = "Admin,Manager")]
        public async Task<ActionResult<ApiResponse<object>>> UpdateStatistics(
            [FromQuery] string periodType = "weekly",
            [FromQuery] DateTime? targetDate = null)
        {
            try
            {
                if (!IsValidPeriodType(periodType))
                {
                    return BadRequest(ApiResponse<object>.CreateFail("无效的统计周期类型"));
                }

                await _statisticsService.UpdateStatisticsAsync(periodType, targetDate);

                _logger.LogInformation("手动统计更新完成: {PeriodType}, {TargetDate}", periodType, targetDate);

                return Ok(ApiResponse<object>.CreateSuccess(null, "统计更新完成"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "手动统计更新失败: {PeriodType}, {Error}", periodType, ex.Message);
                return StatusCode(500, ApiResponse<object>.CreateFail("统计更新失败"));
            }
        }

        /// <summary>
        /// 获取统计概览信息
        /// </summary>
        [HttpGet("overview")]
        public async Task<ActionResult<ApiResponse<StatisticsOverviewDto>>> GetStatisticsOverview()
        {
            try
            {
                // 并行获取多个统计数据
                var tasks = new[]
                {
                    _statisticsService.GetLeaderboardAsync("points", 5),
                    _statisticsService.GetLeaderboardAsync("productivity", 5),
                    _statisticsService.GetWorkSummaryAsync("weekly", null, 10)
                };

                await Task.WhenAll(tasks);

                var overview = new StatisticsOverviewDto
                {
                    TopPointsUsers = tasks[0].Result,
                    TopProductivityUsers = tasks[1].Result,
                    RecentActivities = tasks[2].Result.Take(5).ToList(),
                    UpdateTime = DateTime.UtcNow
                };

                return Ok(ApiResponse<StatisticsOverviewDto>.CreateSuccess(overview, "获取概览成功"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取统计概览失败: {Error}", ex.Message);
                return StatusCode(500, ApiResponse<object>.CreateFail("获取统计概览失败"));
            }
        }

        // 辅助方法
        private static bool IsValidPeriodType(string periodType)
        {
            var validTypes = new[] { "daily", "weekly", "monthly" };
            return validTypes.Contains(periodType?.ToLower());
        }

        private static bool IsValidBoardType(string boardType)
        {
            var validTypes = new[] { "points", "coins", "diamonds", "productivity", "items" };
            return validTypes.Contains(boardType?.ToLower());
        }
    }

    // 统计概览DTO
    public class StatisticsOverviewDto
    {
        public List<LeaderboardItemDto> TopPointsUsers { get; set; } = new();
        public List<LeaderboardItemDto> TopProductivityUsers { get; set; } = new();
        public List<UserWorkSummaryDto> RecentActivities { get; set; } = new();
        public DateTime UpdateTime { get; set; }
    }
}
```

---

## 🎨 前端实现 (Vue 3)

### 1. Pinia状态管理

```typescript
// src/stores/statistics.ts
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import * as signalR from '@microsoft/signalr'
import { statisticsApi } from '@/api/statistics'
import type { 
  UserWorkSummary, 
  LeaderboardItem, 
  UserDetailedStats,
  StatisticsOverview 
} from '@/types/statistics'

export const useStatisticsStore = defineStore('statistics', () => {
  // ===== 状态管理 =====
  const workSummary = ref<UserWorkSummary[]>([])
  const leaderboards = ref<{
    points: LeaderboardItem[]
    coins: LeaderboardItem[]
    diamonds: LeaderboardItem[]
    productivity: LeaderboardItem[]
    items: LeaderboardItem[]
  }>({
    points: [],
    coins: [],
    diamonds: [],
    productivity: [],
    items: []
  })
  
  const currentUserStats = ref<UserDetailedStats | null>(null)
  const statisticsOverview = ref<StatisticsOverview | null>(null)
  
  // 加载状态
  const loading = ref({
    workSummary: false,
    leaderboards: false,
    userStats: false,
    overview: false
  })
  
  // 错误状态
  const errors = ref<Record<string, string>>({})
  
  // 实时连接状态
  const connectionState = ref<'Disconnected' | 'Connecting' | 'Connected'>('Disconnected')
  const lastUpdateTime = ref<Date | null>(null)

  // ===== 计算属性 =====
  const myPointsRank = computed(() => {
    if (!currentUserStats.value) return null
    return leaderboards.value.points.findIndex(
      item => item.userId === currentUserStats.value?.userId
    ) + 1
  })

  const myProductivityRank = computed(() => {
    if (!currentUserStats.value) return null
    return leaderboards.value.productivity.findIndex(
      item => item.userId === currentUserStats.value?.userId
    ) + 1
  })

  const topPerformers = computed(() => {
    return workSummary.value
      .filter(user => user.productivityRank <= 5)
      .sort((a, b) => a.productivityRank - b.productivityRank)
  })

  const isLoading = computed(() => {
    return Object.values(loading.value).some(loading => loading)
  })

  // ===== SignalR 连接管理 =====
  let connection: signalR.HubConnection | null = null

  async function initializeSignalR() {
    try {
      connectionState.value = 'Connecting'
      
      connection = new signalR.HubConnectionBuilder()
        .withUrl('/hubs/statistics', {
          accessTokenFactory: () => {
            // 获取JWT Token
            const token = localStorage.getItem('access_token')
            return token || ''
          }
        })
        .withAutomaticReconnect([0, 2000, 10000, 30000])
        .configureLogging(signalR.LogLevel.Information)
        .build()

      // 连接事件监听
      connection.onclose(() => {
        connectionState.value = 'Disconnected'
        console.warn('SignalR连接已断开')
      })

      connection.onreconnecting(() => {
        connectionState.value = 'Connecting'
        console.info('SignalR正在重连...')
      })

      connection.onreconnected(() => {
        connectionState.value = 'Connected'
        console.info('SignalR重连成功')
      })

      // 数据更新事件监听
      connection.on('StatisticsUpdated', (data) => {
        console.log('收到统计数据更新推送:', data)
        lastUpdateTime.value = new Date(data.updateTime)
        
        // 根据更新类型刷新对应数据
        if (data.periodType === 'weekly') {
          refreshCurrentData()
        }
      })

      connection.on('UserStatsUpdated', (data) => {
        console.log('收到用户统计更新推送:', data)
        
        // 如果是当前用户，更新个人统计
        if (currentUserStats.value && data.userId === currentUserStats.value.userId) {
          refreshUserStats(data.userId)
        }
        
        // 刷新排行榜 (可能排名发生变化)
        refreshLeaderboards()
      })

      connection.on('LeaderboardUpdated', (data) => {
        console.log('收到排行榜更新推送:', data)
        updateLeaderboardFromPush(data)
      })

      // 启动连接
      await connection.start()
      connectionState.value = 'Connected'
      
      // 加入统计数据推送组
      await connection.invoke('JoinStatisticsGroup')
      
      console.log('SignalR连接建立成功')
      return true
    } catch (error) {
      connectionState.value = 'Disconnected'
      console.error('SignalR连接失败:', error)
      return false
    }
  }

  async function disconnectSignalR() {
    if (connection) {
      await connection.stop()
      connection = null
      connectionState.value = 'Disconnected'
    }
  }

  // ===== 数据获取方法 =====
  async function fetchWorkSummary(
    periodType: string = 'weekly', 
    periodDate?: string, 
    limit: number = 50
  ) {
    loading.value.workSummary = true
    errors.value.workSummary = ''
    
    try {
      const response = await statisticsApi.getWorkSummary({
        periodType,
        periodDate,
        limit
      })
      
      if (response.data.success) {
        workSummary.value = response.data.data.map(item => ({
          ...item,
          // 计算总计
          tasksTotal: item.tasksCreated + item.tasksClaimed + item.tasksCompleted + item.tasksCommented,
          assetsTotal: item.assetsCreated + item.assetsUpdated + item.assetsDeleted,
          faultsTotal: item.faultsReported + item.faultsRepaired,
          procurementsTotal: item.procurementsCreated + item.procurementsUpdated,
          partsTotal: item.partsIn + item.partsOut + item.partsAdded
        }))
      } else {
        throw new Error(response.data.message || '获取工作汇总失败')
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '获取工作汇总失败'
      errors.value.workSummary = errorMessage
      console.error('获取工作汇总失败:', error)
      throw error
    } finally {
      loading.value.workSummary = false
    }
  }

  async function fetchLeaderboard(boardType: string, limit: number = 20) {
    loading.value.leaderboards = true
    
    try {
      const response = await statisticsApi.getLeaderboard(boardType, { limit })
      
      if (response.data.success) {
        leaderboards.value[boardType as keyof typeof leaderboards.value] = response.data.data
      } else {
        throw new Error(response.data.message || '获取排行榜失败')
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '获取排行榜失败'
      errors.value.leaderboards = errorMessage
      console.error(`获取${boardType}排行榜失败:`, error)
      throw error
    } finally {
      loading.value.leaderboards = false
    }
  }

  async function fetchAllLeaderboards() {
    loading.value.leaderboards = true
    errors.value.leaderboards = ''
    
    try {
      const boardTypes = ['points', 'coins', 'diamonds', 'productivity', 'items']
      const promises = boardTypes.map(type => 
        statisticsApi.getLeaderboard(type, { limit: 20 })
      )
      
      const responses = await Promise.all(promises)
      
      responses.forEach((response, index) => {
        const boardType = boardTypes[index]
        if (response.data.success) {
          leaderboards.value[boardType as keyof typeof leaderboards.value] = response.data.data
        }
      })
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '获取排行榜失败'
      errors.value.leaderboards = errorMessage
      console.error('获取排行榜失败:', error)
      throw error
    } finally {
      loading.value.leaderboards = false
    }
  }

  async function fetchUserDetailedStats(userId: number, periodType: string = 'weekly') {
    loading.value.userStats = true
    errors.value.userStats = ''
    
    try {
      const response = await statisticsApi.getUserDetailedStats(userId, { periodType })
      
      if (response.data.success) {
        currentUserStats.value = response.data.data
      } else {
        throw new Error(response.data.message || '获取用户详细统计失败')
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '获取用户详细统计失败'
      errors.value.userStats = errorMessage
      console.error('获取用户详细统计失败:', error)
      throw error
    } finally {
      loading.value.userStats = false
    }
  }

  async function fetchOverview() {
    loading.value.overview = true
    errors.value.overview = ''
    
    try {
      const response = await statisticsApi.getOverview()
      
      if (response.data.success) {
        statisticsOverview.value = response.data.data
      } else {
        throw new Error(response.data.message || '获取统计概览失败')
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '获取统计概览失败'
      errors.value.overview = errorMessage
      console.error('获取统计概览失败:', error)
      throw error
    } finally {
      loading.value.overview = false
    }
  }

  // ===== 刷新方法 =====
  async function refreshCurrentData() {
    try {
      await Promise.all([
        fetchWorkSummary('weekly'),
        fetchAllLeaderboards(),
        fetchOverview()
      ])
    } catch (error) {
      console.error('刷新当前数据失败:', error)
    }
  }

  async function refreshLeaderboards() {
    try {
      await fetchAllLeaderboards()
    } catch (error) {
      console.error('刷新排行榜失败:', error)
    }
  }

  async function refreshUserStats(userId: number) {
    try {
      await fetchUserDetailedStats(userId)
    } catch (error) {
      console.error('刷新用户统计失败:', error)
    }
  }

  // ===== 实时更新处理 =====
  function updateLeaderboardFromPush(data: any) {
    // 处理实时推送的排行榜数据
    if (data.boardType && leaderboards.value[data.boardType as keyof typeof leaderboards.value]) {
      leaderboards.value[data.boardType as keyof typeof leaderboards.value] = data.leaderboard
      lastUpdateTime.value = new Date()
    }
  }

  // ===== 手动操作 =====
  async function triggerStatisticsUpdate(periodType: string = 'weekly', targetDate?: string) {
    try {
      const response = await statisticsApi.updateStatistics({ periodType, targetDate })
      
      if (response.data.success) {
        // 更新成功后刷新数据
        await refreshCurrentData()
        return true
      } else {
        throw new Error(response.data.message || '触发统计更新失败')
      }
    } catch (error) {
      console.error('触发统计更新失败:', error)
      throw error
    }
  }

  // ===== 清理方法 =====
  function clearErrors() {
    errors.value = {}
  }

  function clearCache() {
    workSummary.value = []
    leaderboards.value = {
      points: [],
      coins: [],
      diamonds: [],
      productivity: [],
      items: []
    }
    currentUserStats.value = null
    statisticsOverview.value = null
    lastUpdateTime.value = null
  }

  // ===== 生命周期 =====
  async function initialize(userId?: number) {
    try {
      // 1. 建立SignalR连接
      await initializeSignalR()
      
      // 2. 获取初始数据
      await Promise.all([
        fetchWorkSummary('weekly'),
        fetchAllLeaderboards(),
        fetchOverview()
      ])
      
      // 3. 如果提供了用户ID，获取用户详细统计
      if (userId) {
        await fetchUserDetailedStats(userId)
      }
      
      console.log('统计数据Store初始化完成')
      return true
    } catch (error) {
      console.error('统计数据Store初始化失败:', error)
      return false
    }
  }

  async function cleanup() {
    await disconnectSignalR()
    clearCache()
    clearErrors()
  }

  // ===== 返回接口 =====
  return {
    // 状态
    workSummary: readonly(workSummary),
    leaderboards: readonly(leaderboards),
    currentUserStats: readonly(currentUserStats),
    statisticsOverview: readonly(statisticsOverview),
    loading: readonly(loading),
    errors: readonly(errors),
    connectionState: readonly(connectionState),
    lastUpdateTime: readonly(lastUpdateTime),
    
    // 计算属性
    myPointsRank,
    myProductivityRank,
    topPerformers,
    isLoading,
    
    // 方法
    initialize,
    cleanup,
    fetchWorkSummary,
    fetchLeaderboard,
    fetchAllLeaderboards,
    fetchUserDetailedStats,
    fetchOverview,
    refreshCurrentData,
    refreshLeaderboards,
    refreshUserStats,
    triggerStatisticsUpdate,
    clearErrors,
    clearCache,
    
    // SignalR方法
    initializeSignalR,
    disconnectSignalR
  }
})
```

### 2. API接口定义

```typescript
// src/api/statistics.ts
import { request } from '@/utils/request'
import type { 
  UserWorkSummary, 
  LeaderboardItem, 
  UserDetailedStats,
  StatisticsOverview 
} from '@/types/statistics'

export interface WorkSummaryParams {
  periodType?: string
  periodDate?: string
  limit?: number
}

export interface LeaderboardParams {
  limit?: number
}

export interface UserStatsParams {
  periodType?: string
}

export interface UpdateStatisticsParams {
  periodType?: string
  targetDate?: string
}

export const statisticsApi = {
  // 获取工作汇总
  getWorkSummary(params: WorkSummaryParams = {}) {
    return request<UserWorkSummary[]>({
      url: '/api/v2/statistics/work-summary',
      method: 'GET',
      params: {
        periodType: 'weekly',
        limit: 50,
        ...params
      }
    })
  },

  // 获取排行榜
  getLeaderboard(boardType: string, params: LeaderboardParams = {}) {
    return request<LeaderboardItem[]>({
      url: `/api/v2/statistics/leaderboard/${boardType}`,
      method: 'GET',
      params: {
        limit: 20,
        ...params
      }
    })
  },

  // 获取用户详细统计
  getUserDetailedStats(userId: number, params: UserStatsParams = {}) {
    return request<UserDetailedStats>({
      url: `/api/v2/statistics/user/${userId}/detailed`,
      method: 'GET',
      params: {
        periodType: 'weekly',
        ...params
      }
    })
  },

  // 获取统计概览
  getOverview() {
    return request<StatisticsOverview>({
      url: '/api/v2/statistics/overview',
      method: 'GET'
    })
  },

  // 手动触发统计更新 (管理员功能)
  updateStatistics(params: UpdateStatisticsParams = {}) {
    return request({
      url: '/api/v2/statistics/update',
      method: 'POST',
      params: {
        periodType: 'weekly',
        ...params
      }
    })
  }
}
```

### 3. TypeScript类型定义

```typescript
// src/types/statistics.ts

export interface UserWorkSummary {
  userId: number
  userName: string
  departmentName: string
  periodType: string
  periodStartDate: string
  
  // 任务统计
  tasksCreated: number
  tasksClaimed: number
  tasksCompleted: number
  tasksCommented: number
  tasksTotal: number
  
  // 资产统计
  assetsCreated: number
  assetsUpdated: number
  assetsDeleted: number
  assetsTotal: number
  
  // 故障统计
  faultsReported: number
  faultsRepaired: number
  faultsTotal: number
  
  // 采购统计
  procurementsCreated: number
  procurementsUpdated: number
  procurementsTotal: number
  
  // 备件统计
  partsIn: number
  partsOut: number
  partsAdded: number
  partsTotal: number
  
  // 游戏化收益
  pointsEarned: number
  coinsEarned: number
  diamondsEarned: number
  xpEarned: number
  
  // 排名信息
  pointsRank: number
  productivityRank: number
  
  // 扩展信息
  productivityScore: number
  evaluation: string
  specialty: string
  growthRate: number
}

export interface LeaderboardItem {
  rankNo: number
  userId: number
  userName: string
  departmentName: string
  scoreValue: number
  scoreLabel: string
  currentLevel: number
  levelName: string
  levelColor: string
  specialty: string
  growthRate: number
  metadata?: Record<string, any>
}

export interface UserDetailedStats {
  userId: number
  userName: string
  currentStats: {
    totalPoints: number
    totalCoins: number
    totalDiamonds: number
    currentLevel: number
    levelName: string
    weeklyPoints: number
    weeklyTasks: number
    weeklyAssets: number
    pointsRank: number
    productivityRank: number
  }
  historyTrend: Array<{
    periodDate: string
    pointsEarned: number
    tasksCompleted: number
    assetsManaged: number
    productivityRank: number
    growthRate: number
  }>
  itemStats: Array<{
    rarity: string
    itemCount: number
    recentItems: string[]
  }>
}

export interface StatisticsOverview {
  topPointsUsers: LeaderboardItem[]
  topProductivityUsers: LeaderboardItem[]
  recentActivities: UserWorkSummary[]
  updateTime: string
}

// 枚举类型
export enum PeriodType {
  Daily = 'daily',
  Weekly = 'weekly',
  Monthly = 'monthly'
}

export enum BoardType {
  Points = 'points',
  Coins = 'coins',
  Diamonds = 'diamonds',
  Productivity = 'productivity',
  Items = 'items'
}

export enum EvaluationType {
  SuperStar = '🥇 超级明星',
  Excellent = '🏆 优秀员工',
  Hardworking = '⭐ 努力奋斗',
  Improving = '💪 稳步提升'
}
```

### 4. Vue组件实现

```vue
<!-- src/components/Statistics/StatisticsDashboard.vue -->
<template>
  <div class="statistics-dashboard">
    <!-- 顶部概览卡片 -->
    <div class="dashboard-header">
      <el-row :gutter="20" class="overview-cards">
        <el-col :span="6" v-for="(card, index) in overviewCards" :key="index">
          <el-card class="overview-card" :class="card.className">
            <div class="card-content">
              <div class="card-icon">
                <i :class="card.icon"></i>
              </div>
              <div class="card-info">
                <div class="card-value">{{ card.value }}</div>
                <div class="card-label">{{ card.label }}</div>
                <div class="card-trend" :class="card.trendClass">
                  <i :class="card.trendIcon"></i>
                  {{ card.trend }}
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 主要内容区域 -->
    <el-row :gutter="20">
      <!-- 左侧：工作汇总表格 -->
      <el-col :span="16">
        <el-card class="work-summary-card">
          <template #header>
            <div class="card-header">
              <div class="header-title">
                <i class="el-icon-data-analysis"></i>
                <span>📊 人员工作汇总</span>
                <el-badge 
                  v-if="connectionState === 'Connected'" 
                  value="实时" 
                  type="success" 
                  class="realtime-badge"
                />
              </div>
              <div class="header-controls">
                <el-select 
                  v-model="selectedPeriod" 
                  @change="handlePeriodChange"
                  size="small"
                  style="width: 120px"
                >
                  <el-option label="今日" value="daily" />
                  <el-option label="本周" value="weekly" />
                  <el-option label="本月" value="monthly" />
                </el-select>
                <el-button 
                  @click="handleRefresh" 
                  :loading="isRefreshing"
                  size="small"
                  type="primary"
                  icon="el-icon-refresh"
                >
                  刷新
                </el-button>
              </div>
            </div>
          </template>

          <WorkSummaryTable 
            :data="workSummary"
            :loading="loading.workSummary"
            :period-type="selectedPeriod"
            @user-click="handleUserClick"
          />
        </el-card>
      </el-col>

      <!-- 右侧：排行榜 -->
      <el-col :span="8">
        <el-card class="leaderboard-card">
          <template #header>
            <div class="card-header">
              <span>🏆 实时排行榜</span>
              <div class="update-time" v-if="lastUpdateTime">
                <el-tooltip content="最后更新时间">
                  <span>{{ formatUpdateTime(lastUpdateTime) }}</span>
                </el-tooltip>
              </div>
            </div>
          </template>

          <el-tabs v-model="activeLeaderboard" @tab-change="handleLeaderboardChange">
            <el-tab-pane label="💯 积分" name="points">
              <LeaderboardList 
                :data="leaderboards.points.slice(0, 10)" 
                :loading="loading.leaderboards"
                type="points"
                @user-click="handleUserClick"
              />
            </el-tab-pane>
            
            <el-tab-pane label="🚀 生产力" name="productivity">
              <LeaderboardList 
                :data="leaderboards.productivity.slice(0, 10)" 
                :loading="loading.leaderboards"
                type="productivity"
                @user-click="handleUserClick"
              />
            </el-tab-pane>
            
            <el-tab-pane label="💰 金币" name="coins">
              <LeaderboardList 
                :data="leaderboards.coins.slice(0, 10)" 
                :loading="loading.leaderboards"
                type="coins"
                @user-click="handleUserClick"
              />
            </el-tab-pane>
            
            <el-tab-pane label="💎 钻石" name="diamonds">
              <LeaderboardList 
                :data="leaderboards.diamonds.slice(0, 10)" 
                :loading="loading.leaderboards"
                type="diamonds"
                @user-click="handleUserClick"
              />
            </el-tab-pane>
          </el-tabs>
        </el-card>
      </el-col>
    </el-row>

    <!-- 底部：详细排行榜 -->
    <el-card class="detailed-leaderboard-card" style="margin-top: 20px">
      <template #header>
        <div class="card-header">
          <span>🎖️ 完整排行榜</span>
          <el-radio-group v-model="selectedBoardType" size="small">
            <el-radio-button label="points">积分榜</el-radio-button>
            <el-radio-button label="productivity">生产力榜</el-radio-button>
            <el-radio-button label="coins">金币榜</el-radio-button>
            <el-radio-button label="diamonds">钻石榜</el-radio-button>
            <el-radio-button label="items">道具榜</el-radio-button>
          </el-radio-group>
        </div>
      </template>

      <DetailedLeaderboard 
        :data="leaderboards[selectedBoardType]" 
        :loading="loading.leaderboards"
        :board-type="selectedBoardType"
        @user-click="handleUserClick"
      />
    </el-card>

    <!-- 用户详情弹窗 -->
    <UserStatsDialog 
      v-model="showUserDialog"
      :user-id="selectedUserId"
      :user-name="selectedUserName"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { useStatisticsStore } from '@/stores/statistics'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'
import { formatDistanceToNow } from 'date-fns'
import { zhCN } from 'date-fns/locale'

// 组件引入
import WorkSummaryTable from './WorkSummaryTable.vue'
import LeaderboardList from './LeaderboardList.vue'
import DetailedLeaderboard from './DetailedLeaderboard.vue'
import UserStatsDialog from './UserStatsDialog.vue'

// Store
const statisticsStore = useStatisticsStore()
const userStore = useUserStore()

// 响应式数据
const selectedPeriod = ref('weekly')
const activeLeaderboard = ref('points')
const selectedBoardType = ref<'points' | 'productivity' | 'coins' | 'diamonds' | 'items'>('points')
const isRefreshing = ref(false)
const showUserDialog = ref(false)
const selectedUserId = ref<number | null>(null)
const selectedUserName = ref('')

// 计算属性
const workSummary = computed(() => statisticsStore.workSummary)
const leaderboards = computed(() => statisticsStore.leaderboards)
const loading = computed(() => statisticsStore.loading)
const connectionState = computed(() => statisticsStore.connectionState)
const lastUpdateTime = computed(() => statisticsStore.lastUpdateTime)
const currentUser = computed(() => userStore.currentUser)

// 概览卡片数据
const overviewCards = computed(() => {
  const myStats = statisticsStore.currentUserStats
  return [
    {
      icon: 'el-icon-trophy',
      label: '我的积分',
      value: myStats?.currentStats.totalPoints || 0,
      trend: '+12%',
      trendIcon: 'el-icon-arrow-up',
      trendClass: 'trend-up',
      className: 'points-card'
    },
    {
      icon: 'el-icon-rank',
      label: '我的排名',
      value: `第${statisticsStore.myPointsRank || '-'}名`,
      trend: '↑2',
      trendIcon: 'el-icon-arrow-up',
      trendClass: 'trend-up',
      className: 'rank-card'
    },
    {
      icon: 'el-icon-document',
      label: '本周任务',
      value: myStats?.currentStats.weeklyTasks || 0,
      trend: '+3',
      trendIcon: 'el-icon-arrow-up',
      trendClass: 'trend-up',
      className: 'tasks-card'
    },
    {
      icon: 'el-icon-star-on',
      label: '当前等级',
      value: myStats?.currentStats.levelName || 'LV.1',
      trend: myStats?.currentStats.currentLevel || 1,
      trendIcon: 'el-icon-arrow-up',
      trendClass: 'trend-stable',
      className: 'level-card'
    }
  ]
})

// 方法
const handlePeriodChange = async (period: string) => {
  try {
    await statisticsStore.fetchWorkSummary(period)
    ElMessage.success(`已切换到${getPeriodLabel(period)}视图`)
  } catch (error) {
    ElMessage.error('切换统计周期失败')
  }
}

const handleLeaderboardChange = (boardType: string) => {
  activeLeaderboard.value = boardType
}

const handleRefresh = async () => {
  isRefreshing.value = true
  try {
    await statisticsStore.refreshCurrentData()
    ElMessage.success('数据刷新成功')
  } catch (error) {
    ElMessage.error('数据刷新失败')
  } finally {
    isRefreshing.value = false
  }
}

const handleUserClick = (userId: number, userName: string) => {
  selectedUserId.value = userId
  selectedUserName.value = userName
  showUserDialog.value = true
}

const formatUpdateTime = (time: Date) => {
  return formatDistanceToNow(time, { 
    addSuffix: true, 
    locale: zhCN 
  })
}

const getPeriodLabel = (period: string) => {
  const labels: Record<string, string> = {
    daily: '今日',
    weekly: '本周',
    monthly: '本月'
  }
  return labels[period] || period
}

// 监听器
watch(selectedBoardType, (newType) => {
  // 当切换排行榜类型时，确保数据已加载
  if (!leaderboards.value[newType].length) {
    statisticsStore.fetchLeaderboard(newType, 20)
  }
})

// 生命周期
onMounted(async () => {
  try {
    // 初始化统计数据Store
    const userId = currentUser.value?.id
    await statisticsStore.initialize(userId)
    
    // 如果有当前用户，获取用户详细统计
    if (userId) {
      await statisticsStore.fetchUserDetailedStats(userId)
    }
  } catch (error) {
    console.error('统计仪表板初始化失败:', error)
    ElMessage.error('加载统计数据失败')
  }
})

onUnmounted(() => {
  // 清理资源
  statisticsStore.cleanup()
})
</script>

<style scoped lang="scss">
.statistics-dashboard {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;

  .dashboard-header {
    margin-bottom: 20px;
  }

  .overview-cards {
    .overview-card {
      height: 120px;
      border-radius: 8px;
      overflow: hidden;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
      }

      &.points-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
      }

      &.rank-card {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
      }

      &.tasks-card {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        color: white;
      }

      &.level-card {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        color: white;
      }

      .card-content {
        display: flex;
        align-items: center;
        height: 100%;
        padding: 20px;

        .card-icon {
          font-size: 2.5rem;
          margin-right: 15px;
          opacity: 0.8;
        }

        .card-info {
          flex: 1;

          .card-value {
            font-size: 1.8rem;
            font-weight: bold;
            margin-bottom: 5px;
          }

          .card-label {
            font-size: 0.9rem;
            opacity: 0.8;
            margin-bottom: 5px;
          }

          .card-trend {
            font-size: 0.8rem;
            display: flex;
            align-items: center;
            gap: 4px;

            &.trend-up {
              color: #67c23a;
            }

            &.trend-down {
              color: #f56c6c;
            }

            &.trend-stable {
              color: #e6a23c;
            }
          }
        }
      }
    }
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .header-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 600;

      .realtime-badge {
        margin-left: 8px;
      }
    }

    .header-controls {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .update-time {
      font-size: 0.8rem;
      color: #909399;
    }
  }

  .work-summary-card {
    .el-card__body {
      padding: 0;
    }
  }

  .leaderboard-card {
    height: 600px;

    .el-tabs {
      height: 100%;

      ::v-deep(.el-tabs__content) {
        height: calc(100% - 40px);
        overflow-y: auto;
      }
    }
  }

  .detailed-leaderboard-card {
    .el-card__body {
      padding: 0;
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .statistics-dashboard {
    .overview-cards {
      .el-col {
        margin-bottom: 20px;
      }
    }
  }
}

@media (max-width: 768px) {
  .statistics-dashboard {
    padding: 10px;

    .el-row {
      margin: 0;

      .el-col {
        padding: 0 10px;
        margin-bottom: 20px;
      }
    }

    .card-header {
      flex-direction: column;
      gap: 10px;
      align-items: flex-start;

      .header-controls {
        width: 100%;
        justify-content: space-between;
      }
    }
  }
}
</style>
```

---

## 🚀 完整部署方案

### 1. 项目配置更新

```csharp
// Startup.cs 配置更新
public void ConfigureServices(IServiceCollection services)
{
    // 现有配置...

    // 添加Redis缓存
    services.AddStackExchangeRedisCache(options =>
    {
        options.Configuration = Configuration.GetConnectionString("Redis");
        options.InstanceName = "ItAssetsSystem";
    });

    // 添加内存缓存
    services.AddMemoryCache();

    // 注册统计服务
    services.AddScoped<IStatisticsService, StatisticsService>();

    // 注册后台服务
    services.AddHostedService<StatisticsUpdateService>();

    // 添加SignalR
    services.AddSignalR(options =>
    {
        options.EnableDetailedErrors = true;
        options.ClientTimeoutInterval = TimeSpan.FromMinutes(1);
        options.KeepAliveInterval = TimeSpan.FromSeconds(30);
    });

    // 配置CORS (如果需要)
    services.AddCors(options =>
    {
        options.AddPolicy("AllowStatistics", builder =>
        {
            builder.WithOrigins("http://localhost:5173") // Vue开发服务器
                   .AllowAnyMethod()
                   .AllowAnyHeader()
                   .AllowCredentials();
        });
    });
}

public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
{
    // 现有配置...

    app.UseCors("AllowStatistics");
    
    app.UseRouting();
    app.UseAuthentication();
    app.UseAuthorization();

    app.UseEndpoints(endpoints =>
    {
        endpoints.MapControllers();
        endpoints.MapHub<StatisticsHub>("/hubs/statistics");
        // 现有endpoints...
    });
}
```

### 2. 配置文件

```json
// appsettings.json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost;Database=itassets_db;User=root;Password=your_password;AllowUserVariables=true;",
    "Redis": "localhost:6379"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore.SignalR": "Debug",
      "ItAssetsSystem.Services.Statistics": "Debug"
    }
  },
  "Statistics": {
    "CacheExpirationMinutes": 5,
    "AutoUpdateEnabled": true,
    "RealTimeUpdateThrottleSeconds": 30
  }
}
```

### 3. 数据库迁移脚本

```sql
-- 执行顺序脚本
-- migration_001_create_statistics_tables.sql

-- 1. 创建统计相关表
-- (前面已定义的SQL脚本)

-- 2. 创建性能优化索引
CREATE INDEX idx_gamification_logs_user_behavior_date 
ON gamification_logs(UserId, BehaviorCode, CreatedAt);

CREATE INDEX idx_gamification_logs_created_at 
ON gamification_logs(CreatedAt);

-- 3. 创建统计视图
-- (前面已定义的视图脚本)

-- 4. 插入初始配置数据
-- (前面已定义的规则配置数据)

-- 5. 数据迁移验证
SELECT COUNT(*) as total_users FROM users WHERE IsDeleted = 0;
SELECT COUNT(*) as total_logs FROM gamification_logs;
SELECT COUNT(*) as total_rules FROM gamification_rule_config WHERE is_active = 1;

-- 验证视图是否正常工作
SELECT COUNT(*) FROM v_points_leaderboard LIMIT 5;
SELECT COUNT(*) FROM v_productivity_leaderboard LIMIT 5;
```

### 4. 前端项目配置

```typescript
// vite.config.ts
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  },
  server: {
    port: 5173,
    proxy: {
      '/api': {
        target: 'http://localhost:5001',
        changeOrigin: true
      },
      '/hubs': {
        target: 'http://localhost:5001',
        changeOrigin: true,
        ws: true // 支持WebSocket
      }
    }
  },
  build: {
    outDir: 'dist',
    sourcemap: false,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['vue', 'vue-router', 'pinia'],
          signalr: ['@microsoft/signalr'],
          elementplus: ['element-plus']
        }
      }
    }
  }
})
```

```json
// package.json dependencies
{
  "dependencies": {
    "vue": "^3.3.0",
    "vue-router": "^4.2.0",
    "pinia": "^2.1.0",
    "element-plus": "^2.3.0",
    "@microsoft/signalr": "^7.0.0",
    "axios": "^1.5.0",
    "date-fns": "^2.30.0",
    "echarts": "^5.4.0"
  },
  "devDependencies": {
    "@vitejs/plugin-vue": "^4.3.0",
    "typescript": "^5.0.0",
    "vite": "^4.4.0",
    "sass": "^1.60.0"
  }
}
```

### 5. 部署检查清单

#### 5.1 数据库部署
```bash
# 1. 执行数据库迁移脚本
mysql -u root -p itassets_db < migration_001_create_statistics_tables.sql

# 2. 验证表结构
mysql -u root -p -e "SHOW TABLES LIKE '%statistics%';" itassets_db
mysql -u root -p -e "SHOW TABLES LIKE '%leaderboard%';" itassets_db

# 3. 验证视图
mysql -u root -p -e "SELECT COUNT(*) FROM v_points_leaderboard;" itassets_db

# 4. 验证初始数据
mysql -u root -p -e "SELECT COUNT(*) FROM gamification_rule_config;" itassets_db
```

#### 5.2 Redis部署
```bash
# 安装Redis (Ubuntu)
sudo apt update
sudo apt install redis-server

# 配置Redis
sudo systemctl enable redis-server
sudo systemctl start redis-server

# 验证Redis连接
redis-cli ping
```

#### 5.3 后端部署
```bash
# 1. 发布应用
dotnet publish -c Release -o ./publish

# 2. 配置生产环境连接字符串
# 编辑 appsettings.Production.json

# 3. 运行应用
cd publish
dotnet ItAssetsSystem.dll --environment=Production

# 4. 验证统计API
curl -X GET "http://localhost:5001/api/v2/statistics/overview" \
     -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### 5.4 前端部署
```bash
# 1. 构建生产版本
npm run build

# 2. 部署到Web服务器 (Nginx配置示例)
# /etc/nginx/sites-available/itassets-frontend
server {
    listen 80;
    server_name your-domain.com;
    root /var/www/itassets-frontend/dist;
    index index.html;

    # 处理Vue Router的History模式
    location / {
        try_files $uri $uri/ /index.html;
    }

    # 代理API请求
    location /api/ {
        proxy_pass http://localhost:5001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    # 代理SignalR Hub
    location /hubs/ {
        proxy_pass http://localhost:5001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }
}

# 3. 启用站点并重载Nginx
sudo ln -s /etc/nginx/sites-available/itassets-frontend /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### 6. 性能监控配置

```csharp
// Program.cs 或 Startup.cs
public void ConfigureServices(IServiceCollection services)
{
    // 添加健康检查
    services.AddHealthChecks()
        .AddDbContext<ApplicationDbContext>()
        .AddRedis(Configuration.GetConnectionString("Redis"));

    // 添加应用洞察 (可选)
    services.AddApplicationInsightsTelemetry();
}

public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
{
    // 健康检查端点
    app.UseHealthChecks("/health", new HealthCheckOptions
    {
        ResponseWriter = UIResponseWriter.WriteHealthCheckUIResponse
    });
}
```

### 7. 验证测试脚本

```bash
#!/bin/bash
# deploy_verification.sh

echo "=== IT资产管理系统游戏化统计部署验证 ==="

# 1. 验证数据库连接
echo "1. 验证数据库连接..."
mysql -u root -p itassets_db -e "SELECT 'Database OK' as status;" || exit 1

# 2. 验证Redis连接
echo "2. 验证Redis连接..."
redis-cli ping | grep PONG || exit 1

# 3. 验证API端点
echo "3. 验证API端点..."
curl -f -s "http://localhost:5001/health" > /dev/null || exit 1

# 4. 验证统计API
echo "4. 验证统计API..."
curl -f -s "http://localhost:5001/api/v2/statistics/overview" \
     -H "Authorization: Bearer $JWT_TOKEN" > /dev/null || exit 1

# 5. 验证SignalR Hub
echo "5. 验证SignalR Hub..."
# (这里需要更复杂的WebSocket测试，简化处理)
curl -f -s "http://localhost:5001/hubs/statistics/negotiate" > /dev/null || exit 1

# 6. 验证前端
echo "6. 验证前端..."
curl -f -s "http://localhost" > /dev/null || exit 1

echo "✅ 所有验证通过！部署成功！"
```

---

## 📊 总结

### 本方案的核心优势

1. **🏗️ 现代化架构**: Vue 3 + .NET 8 + MySQL + Redis + SignalR
2. **⚡ 高性能**: 多层缓存 + 预聚合数据 + 数据库优化
3. **🔄 实时性**: SignalR实时推送 + 自动更新机制
4. **📈 可扩展**: 模块化设计 + 配置驱动 + 清晰架构
5. **🎮 用户体验**: 现代UI + 实时反馈 + 丰富交互

### 实施周期规划

**第1周**: 数据库设计 + 后端API开发  
**第2周**: 前端组件开发 + SignalR集成  
**第3周**: 测试优化 + 部署上线  

### 技术债务最小化

- 使用TypeScript确保类型安全
- 完整的错误处理和日志记录  
- 自动化测试和部署流程
- 清晰的代码结构和文档

这是一份**生产就绪**的现代化游戏化统计系统实施方案，完全适配你的技术栈！🚀