using System;
using System.Collections.Generic;

namespace ItAssetsSystem.Models.Import
{
    /// <summary>
    /// 导入处理响应
    /// </summary>
    public class ImportResponse
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; } = true;

        /// <summary>
        /// 消息
        /// </summary>
        public string Message { get; set; } = "导入成功";

        /// <summary>
        /// 总行数
        /// </summary>
        public int TotalRows { get; set; }

        /// <summary>
        /// 成功数量
        /// </summary>
        public int SuccessCount { get; set; }

        /// <summary>
        /// 错误数量
        /// </summary>
        public int ErrorCount { get; set; }

        /// <summary>
        /// 错误列表
        /// </summary>
        public List<string> Errors { get; set; } = new List<string>();

        /// <summary>
        /// 成功导入的记录ID列表
        /// </summary>
        public List<object> SuccessItems { get; set; } = new List<object>();
    }
} 