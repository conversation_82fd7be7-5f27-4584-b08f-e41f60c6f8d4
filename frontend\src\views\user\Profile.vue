/**
 * 航空航天级IT资产管理系统 - 个人信息页面
 * 文件路径: src/views/user/Profile.vue
 * 功能描述: 用户查看和编辑个人信息
 */

<template>
  <div class="user-profile">
    <page-header title="个人信息" description="查看和修改您的个人账户信息" />
    
    <el-card shadow="hover" class="profile-card">
      <template #header>
        <div class="card-header">
          <h3>个人信息</h3>
          <el-button type="primary" @click="handleEdit">编辑</el-button>
        </div>
      </template>
      
      <div class="profile-content">
        <div class="avatar-container">
          <div class="avatar-uploader">
            <el-upload
              class="avatar-uploader"
              :action="uploadUrl"
              :headers="uploadHeaders"
              :show-file-list="false"
              :on-success="handleAvatarSuccess"
              :before-upload="beforeAvatarUpload"
              :on-error="handleUploadError"
            >
              <!--  TEMPORARY HARDCODING FOR TESTING -->
              <el-avatar 
                :size="100" 
                src="http://localhost/files/uploads/avatars/user1_1747191994946.jpg" 
                class="profile-avatar"
              />
              <!-- <el-avatar :size="100" :src="userStore.computedAvatarUrl" class="profile-avatar" /> -->
              
              <div class="avatar-uploader-icon">
                <el-icon><Camera /></el-icon>
                <span>点击更换</span>
              </div>
            </el-upload>
          </div>
        </div>
        
        <div class="info-container">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="用户名">{{ userInfo.username }}</el-descriptions-item>
            <el-descriptions-item label="姓名">{{ userInfo.name }}</el-descriptions-item>
            <el-descriptions-item label="邮箱">{{ userInfo.email }}</el-descriptions-item>
            <el-descriptions-item label="电话">{{ userInfo.phone }}</el-descriptions-item>
            <el-descriptions-item label="部门">{{ userInfo.department }}</el-descriptions-item>
            <el-descriptions-item label="职位">{{ userInfo.position }}</el-descriptions-item>
            <el-descriptions-item label="角色">{{ userInfo.roles?.join(', ') || '无' }}</el-descriptions-item>
            <el-descriptions-item label="最后登录">{{ formatDate(userInfo.lastLogin) }}</el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </el-card>
    
    <!-- 编辑信息对话框 -->
    <el-dialog
      v-model="editDialogVisible"
      title="编辑个人信息"
      width="500px"
      destroy-on-close
    >
      <el-form
        ref="editFormRef"
        :model="editForm"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="姓名" prop="name">
          <el-input v-model="editForm.name" />
        </el-form-item>
        
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="editForm.email" />
        </el-form-item>
        
        <el-form-item label="电话" prop="phone">
          <el-input v-model="editForm.phone" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="editDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useUserStore } from '@/stores/modules/user'
import { Camera } from '@element-plus/icons-vue'
import PageHeader from '@/components/PageHeader.vue'
import systemConfig from '@/config/system'

// 状态管理
const userStore = useUserStore()
const editDialogVisible = ref(false)
const editFormRef = ref(null)
const defaultAvatar = systemConfig.defaultAvatar

// 上传相关配置
const uploadUrl = `${systemConfig.apiBaseUrl}/v2/profile/avatar`
const uploadHeaders = computed(() => ({
  Authorization: `Bearer ${userStore.token}`
}))

// 用户信息
const userInfo = computed(() => userStore.userInfo || {})

// 编辑表单
const editForm = reactive({
  name: '',
  email: '',
  phone: ''
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' },
    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ]
}

// 日期格式化
const formatDate = (date) => {
  if (!date) return '未登录'
  return new Date(date).toLocaleString()
}

// 处理编辑按钮点击
const handleEdit = () => {
  // 复制当前用户信息到编辑表单
  editForm.name = userInfo.value.name || ''
  editForm.email = userInfo.value.email || ''
  editForm.phone = userInfo.value.phone || ''
  
  editDialogVisible.value = true
}

// 处理头像上传成功
const handleAvatarSuccess = (res) => {
  console.log('头像上传响应:', res);
  if (res.success) {
    const avatarUrl = res.data.avatarUrl;
    if (avatarUrl) {
      userStore.setAvatar(avatarUrl);
      ElMessage.success('头像上传成功');
    } else {
      ElMessage.warning('头像上传成功，但未获取到URL');
    }
  } else {
    ElMessage.error(res.message || '头像上传失败');
  }
}

// 头像上传前校验
const beforeAvatarUpload = (file) => {
  const isImage = file.type.startsWith('image/')
  const isLt5M = file.size / 1024 / 1024 < 5

  if (!isImage) {
    ElMessage.error('头像必须是图片格式!')
    return false
  }
  if (!isLt5M) {
    ElMessage.error('头像大小不能超过 5MB!')
    return false
  }

  return true
}

// 处理上传错误
const handleUploadError = (err) => {
  console.error('头像上传失败:', err)
  ElMessage.error('头像上传失败，请重试')
}

// 提交编辑表单
const submitForm = async () => {
  if (!editFormRef.value) return
  
  await editFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        const response = await userStore.updateProfile({
          name: editForm.name,
          email: editForm.email,
          phone: editForm.phone
        })
        
        if (response.success) {
          ElMessage.success('个人信息更新成功')
          editDialogVisible.value = false
        } else {
          ElMessage.error(response.message || '更新个人信息失败')
        }
      } catch (error) {
        console.error('更新个人信息出错:', error)
        ElMessage.error('更新个人信息失败: ' + (error.message || '未知错误'))
      }
    }
  })
}

// 页面初始化
onMounted(() => {
  console.log('个人信息页面已加载')
})
</script>

<style lang="scss" scoped>
.user-profile {
  padding: 20px;
  
  .profile-card {
    margin-top: 20px;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      h3 {
        margin: 0;
        font-size: 18px;
        font-weight: 500;
      }
    }
    
    .profile-content {
      display: flex;
      align-items: flex-start;
      gap: 30px;
      
      .avatar-container {
        position: relative;
        
        .avatar-uploader {
          cursor: pointer;
          
          &:hover .avatar-upload-overlay {
            opacity: 1;
          }
        }
        
        .avatar-upload-overlay {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          background: rgba(0, 0, 0, 0.6);
          border-radius: 50%;
          opacity: 0;
          transition: opacity 0.3s;
          color: white;
          
          .el-icon {
            font-size: 24px;
            margin-bottom: 8px;
          }
          
          span {
            font-size: 12px;
          }
        }
      }
      
      .info-container {
        flex: 1;
      }
    }
  }
}

@media (max-width: 768px) {
  .profile-content {
    flex-direction: column;
    
    .avatar-container {
      margin: 0 auto 20px;
    }
    
    .info-container {
      width: 100%;
    }
  }
}
</style> 