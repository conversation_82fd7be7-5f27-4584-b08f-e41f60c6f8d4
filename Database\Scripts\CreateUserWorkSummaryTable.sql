-- 创建用户工作汇总表
-- 用于存储用户的工作统计数据，支持日/周/月统计

CREATE TABLE IF NOT EXISTS `user_work_summary` (
  `user_id` int NOT NULL COMMENT '用户ID',
  `period_type` varchar(20) NOT NULL COMMENT '统计周期类型(daily/weekly/monthly)',
  `period_date` date NOT NULL COMMENT '统计周期日期',
  `tasks_created` int NOT NULL DEFAULT '0' COMMENT '创建任务数',
  `tasks_completed` int NOT NULL DEFAULT '0' COMMENT '完成任务数',
  `tasks_claimed` int NOT NULL DEFAULT '0' COMMENT '领取任务数',
  `tasks_updated` int NOT NULL DEFAULT '0' COMMENT '更新任务数',
  `assets_created` int NOT NULL DEFAULT '0' COMMENT '创建资产数',
  `assets_updated` int NOT NULL DEFAULT '0' COMMENT '更新资产数',
  `assets_transferred` int NOT NULL DEFAULT '0' COMMENT '转移资产数',
  `faults_recorded` int NOT NULL DEFAULT '0' COMMENT '登记故障数',
  `faults_resolved` int NOT NULL DEFAULT '0' COMMENT '解决故障数',
  `procurement_created` int NOT NULL DEFAULT '0' COMMENT '创建采购数',
  `procurement_updated` int NOT NULL DEFAULT '0' COMMENT '更新采购数',
  `parts_inbound` int NOT NULL DEFAULT '0' COMMENT '备件入库数',
  `parts_outbound` int NOT NULL DEFAULT '0' COMMENT '备件出库数',
  `parts_updated` int NOT NULL DEFAULT '0' COMMENT '更新备件数',
  `total_points` int NOT NULL DEFAULT '0' COMMENT '总积分',
  `total_coins` int NOT NULL DEFAULT '0' COMMENT '总金币',
  `total_diamonds` int NOT NULL DEFAULT '0' COMMENT '总钻石',
  `productivity_score` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '生产力评分',
  `productivity_rank` int DEFAULT NULL COMMENT '生产力排名',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`user_id`, `period_type`, `period_date`),
  KEY `idx_user_work_summary_period` (`period_type`, `period_date`),
  KEY `idx_user_work_summary_productivity` (`productivity_rank`),
  KEY `idx_user_work_summary_points` (`total_points`),
  CONSTRAINT `fk_user_work_summary_user` FOREIGN KEY (`user_id`) REFERENCES `Users` (`Id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户工作汇总表';

-- 插入一些测试数据
INSERT IGNORE INTO `user_work_summary` 
(`user_id`, `period_type`, `period_date`, `tasks_created`, `tasks_completed`, `tasks_claimed`, 
 `assets_created`, `faults_recorded`, `total_points`, `total_coins`, `total_diamonds`, 
 `productivity_score`, `productivity_rank`) 
VALUES 
(6, 'weekly', CURDATE(), 5, 8, 10, 3, 2, 150, 75, 3, 85.50, 1),
(1, 'weekly', CURDATE(), 3, 6, 8, 2, 1, 120, 60, 2, 78.20, 2),
(2, 'weekly', CURDATE(), 4, 5, 7, 1, 3, 110, 55, 1, 72.30, 3),
(3, 'weekly', CURDATE(), 2, 4, 6, 2, 1, 95, 48, 1, 65.80, 4),
(4, 'weekly', CURDATE(), 1, 3, 5, 1, 2, 80, 40, 0, 58.90, 5);

SELECT '✅ user_work_summary 表创建完成并插入测试数据' as Status;
