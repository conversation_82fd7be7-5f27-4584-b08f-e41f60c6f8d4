{"version": 3, "file": "interact.min.js", "sources": ["../../@interactjs/utils/isWindow.ts", "../../@interactjs/utils/window.ts", "../../@interactjs/utils/is.ts", "../../@interactjs/actions/drag/plugin.ts", "../../@interactjs/utils/domObjects.ts", "../../@interactjs/utils/browser.ts", "../../@interactjs/utils/domUtils.ts", "../../@interactjs/utils/extend.ts", "../../@interactjs/utils/rect.ts", "../../@interactjs/utils/getOriginXY.ts", "../../@interactjs/utils/normalizeListeners.ts", "../../@interactjs/utils/hypot.ts", "../../@interactjs/utils/pointerExtend.ts", "../../@interactjs/utils/pointerUtils.ts", "../../@interactjs/core/BaseEvent.ts", "../../@interactjs/utils/arr.ts", "../../@interactjs/actions/drop/DropEvent.ts", "../../@interactjs/actions/drop/plugin.ts", "../../@interactjs/actions/gesture/plugin.ts", "../../@interactjs/actions/resize/plugin.ts", "../../@interactjs/utils/raf.ts", "../../@interactjs/actions/plugin.ts", "../../@interactjs/auto-scroll/plugin.ts", "../../@interactjs/utils/misc.ts", "../../@interactjs/auto-start/InteractableMethods.ts", "../../@interactjs/auto-start/base.ts", "../../@interactjs/auto-start/dragAxis.ts", "../../@interactjs/auto-start/hold.ts", "../../@interactjs/auto-start/plugin.ts", "../../@interactjs/core/interactablePreventDefault.ts", "../../@interactjs/utils/isNonNativeEvent.ts", "../../@interactjs/utils/clone.ts", "../../@interactjs/modifiers/Modification.ts", "../../@interactjs/modifiers/base.ts", "../../@interactjs/core/options.ts", "../../@interactjs/core/InteractEvent.ts", "../../@interactjs/core/PointerInfo.ts", "../../@interactjs/core/Interaction.ts", "../../@interactjs/offset/plugin.ts", "../../@interactjs/inertia/plugin.ts", "../../@interactjs/core/Eventable.ts", "../../@interactjs/core/events.ts", "../../@interactjs/core/interactionFinder.ts", "../../@interactjs/core/interactions.ts", "../../@interactjs/core/Interactable.ts", "../../@interactjs/core/InteractableSet.ts", "../../@interactjs/core/scope.ts", "../../@interactjs/core/InteractStatic.ts", "../../@interactjs/interact/index.ts", "../../@interactjs/snappers/edgeTarget.ts", "../../@interactjs/snappers/elements.ts", "../../@interactjs/snappers/grid.ts", "../../@interactjs/snappers/plugin.ts", "../../@interactjs/modifiers/aspectRatio.ts", "../../@interactjs/modifiers/noop.ts", "../../@interactjs/modifiers/restrict/pointer.ts", "../../@interactjs/modifiers/restrict/edges.ts", "../../@interactjs/modifiers/restrict/rect.ts", "../../@interactjs/modifiers/restrict/size.ts", "../../@interactjs/modifiers/snap/pointer.ts", "../../@interactjs/modifiers/snap/size.ts", "../../@interactjs/modifiers/snap/edges.ts", "../../@interactjs/modifiers/all.ts", "../../@interactjs/modifiers/plugin.ts", "../../@interactjs/pointer-events/PointerEvent.ts", "../../@interactjs/pointer-events/base.ts", "../../@interactjs/pointer-events/holdRepeat.ts", "../../@interactjs/pointer-events/interactableTargets.ts", "../../@interactjs/pointer-events/plugin.ts", "../../@interactjs/reflow/plugin.ts", "../index.ts", "../../@interactjs/interactjs/index.ts"], "sourcesContent": ["export default (thing: any) => !!(thing && thing.Window) && thing instanceof thing.Window\n", "import isWindow from './isWindow'\n\nexport let realWindow = undefined as Window\n\nlet win = undefined as Window\nexport { win as window }\n\nexport function init(window: Window & { wrap?: (...args: any[]) => any }) {\n  // get wrapped window if using Shadow DOM polyfill\n\n  realWindow = window\n\n  // create a TextNode\n  const el = window.document.createTextNode('')\n\n  // check if it's wrapped by a polyfill\n  if (el.ownerDocument !== window.document && typeof window.wrap === 'function' && window.wrap(el) === el) {\n    // use wrapped window\n    window = window.wrap(window)\n  }\n\n  win = window\n}\n\nif (typeof window !== 'undefined' && !!window) {\n  init(window)\n}\n\nexport function getWindow(node: any) {\n  if (isWindow(node)) {\n    return node\n  }\n\n  const rootNode = node.ownerDocument || node\n\n  return rootNode.defaultView || win.window\n}\n", "import isWindow from './isWindow'\nimport * as win from './window'\n\nconst window = (thing: any): thing is Window => thing === win.window || isWindow(thing)\n\nconst docFrag = (thing: any): thing is DocumentFragment => object(thing) && thing.nodeType === 11\n\nconst object = (thing: any): thing is { [index: string]: any } => !!thing && typeof thing === 'object'\n\nconst func = (thing: any): thing is (...args: any[]) => any => typeof thing === 'function'\n\nconst number = (thing: any): thing is number => typeof thing === 'number'\n\nconst bool = (thing: any): thing is boolean => typeof thing === 'boolean'\n\nconst string = (thing: any): thing is string => typeof thing === 'string'\n\nconst element = (thing: any): thing is HTMLElement | SVGElement => {\n  if (!thing || typeof thing !== 'object') {\n    return false\n  }\n\n  const _window = win.getWindow(thing) || win.window\n\n  return /object|function/.test(typeof Element)\n    ? thing instanceof Element || thing instanceof _window.Element\n    : thing.nodeType === 1 && typeof thing.nodeName === 'string'\n}\n\nconst plainObject: typeof object = (thing: any): thing is { [index: string]: any } =>\n  object(thing) && !!thing.constructor && /function Object\\b/.test(thing.constructor.toString())\n\nconst array = <T extends unknown>(thing: any): thing is T[] =>\n  object(thing) && typeof thing.length !== 'undefined' && func(thing.splice)\n\nexport default {\n  window,\n  docFrag,\n  object,\n  func,\n  number,\n  bool,\n  string,\n  element,\n  plainObject,\n  array,\n}\n", "import type { Interactable } from '@interactjs/core/Interactable'\nimport type { InteractEvent } from '@interactjs/core/InteractEvent'\nimport type { PerActionDefaults } from '@interactjs/core/options'\nimport type { Scope, Plugin } from '@interactjs/core/scope'\nimport type { ListenersArg, OrBoolean } from '@interactjs/core/types'\nimport is from '@interactjs/utils/is'\n\ndeclare module '@interactjs/core/Interactable' {\n  interface Interactable {\n    draggable(options: Partial<OrBoolean<DraggableOptions>> | boolean): this\n    draggable(): DraggableOptions\n    /**\n     * ```js\n     * interact(element).draggable({\n     *     onstart: function (event) {},\n     *     onmove : function (event) {},\n     *     onend  : function (event) {},\n     *\n     *     // the axis in which the first movement must be\n     *     // for the drag sequence to start\n     *     // 'xy' by default - any direction\n     *     startAxis: 'x' || 'y' || 'xy',\n     *\n     *     // 'xy' by default - don't restrict to one axis (move in any direction)\n     *     // 'x' or 'y' to restrict movement to either axis\n     *     // 'start' to restrict movement to the axis the drag started in\n     *     lockAxis: 'x' || 'y' || 'xy' || 'start',\n     *\n     *     // max number of drags that can happen concurrently\n     *     // with elements of this Interactable. Infinity by default\n     *     max: Infinity,\n     *\n     *     // max number of drags that can target the same element+Interactable\n     *     // 1 by default\n     *     maxPerElement: 2\n     * })\n     *\n     * var isDraggable = interact('element').draggable(); // true\n     * ```\n     *\n     * Get or set whether drag actions can be performed on the target\n     *\n     * @param options - true/false or An object with event\n     * listeners to be fired on drag events (object makes the Interactable\n     * draggable)\n     */\n    draggable(options?: Partial<OrBoolean<DraggableOptions>> | boolean): this | DraggableOptions\n  }\n}\n\ndeclare module '@interactjs/core/options' {\n  interface ActionDefaults {\n    drag: DraggableOptions\n  }\n}\n\ndeclare module '@interactjs/core/types' {\n  interface ActionMap {\n    drag?: typeof drag\n  }\n}\n\nexport type DragEvent = InteractEvent<'drag'>\n\nexport interface DraggableOptions extends PerActionDefaults {\n  startAxis?: 'x' | 'y' | 'xy'\n  lockAxis?: 'x' | 'y' | 'xy' | 'start'\n  oninertiastart?: ListenersArg\n  onstart?: ListenersArg\n  onmove?: ListenersArg\n  onend?: ListenersArg\n}\n\nfunction install(scope: Scope) {\n  const { actions, Interactable, defaults } = scope\n\n  Interactable.prototype.draggable = drag.draggable\n\n  actions.map.drag = drag\n  actions.methodDict.drag = 'draggable'\n\n  defaults.actions.drag = drag.defaults\n}\n\nfunction beforeMove({ interaction }) {\n  if (interaction.prepared.name !== 'drag') return\n\n  const axis = interaction.prepared.axis\n\n  if (axis === 'x') {\n    interaction.coords.cur.page.y = interaction.coords.start.page.y\n    interaction.coords.cur.client.y = interaction.coords.start.client.y\n\n    interaction.coords.velocity.client.y = 0\n    interaction.coords.velocity.page.y = 0\n  } else if (axis === 'y') {\n    interaction.coords.cur.page.x = interaction.coords.start.page.x\n    interaction.coords.cur.client.x = interaction.coords.start.client.x\n\n    interaction.coords.velocity.client.x = 0\n    interaction.coords.velocity.page.x = 0\n  }\n}\n\nfunction move({ iEvent, interaction }) {\n  if (interaction.prepared.name !== 'drag') return\n\n  const axis = interaction.prepared.axis\n\n  if (axis === 'x' || axis === 'y') {\n    const opposite = axis === 'x' ? 'y' : 'x'\n\n    iEvent.page[opposite] = interaction.coords.start.page[opposite]\n    iEvent.client[opposite] = interaction.coords.start.client[opposite]\n    iEvent.delta[opposite] = 0\n  }\n}\n\nconst draggable: Interactable['draggable'] = function draggable(\n  this: Interactable,\n  options?: DraggableOptions | boolean,\n): any {\n  if (is.object(options)) {\n    this.options.drag.enabled = options.enabled !== false\n    this.setPerAction('drag', options)\n    this.setOnEvents('drag', options)\n\n    if (/^(xy|x|y|start)$/.test(options.lockAxis)) {\n      this.options.drag.lockAxis = options.lockAxis\n    }\n    if (/^(xy|x|y)$/.test(options.startAxis)) {\n      this.options.drag.startAxis = options.startAxis\n    }\n\n    return this\n  }\n\n  if (is.bool(options)) {\n    this.options.drag.enabled = options\n\n    return this\n  }\n\n  return this.options.drag as DraggableOptions\n}\n\nconst drag: Plugin = {\n  id: 'actions/drag',\n  install,\n  listeners: {\n    'interactions:before-action-move': beforeMove,\n    'interactions:action-resume': beforeMove,\n\n    // dragmove\n    'interactions:action-move': move,\n    'auto-start:check': (arg) => {\n      const { interaction, interactable, buttons } = arg\n      const dragOptions = interactable.options.drag\n\n      if (\n        !(dragOptions && dragOptions.enabled) ||\n        // check mouseButton setting if the pointer is down\n        (interaction.pointerIsDown &&\n          /mouse|pointer/.test(interaction.pointerType) &&\n          (buttons & interactable.options.drag.mouseButtons) === 0)\n      ) {\n        return undefined\n      }\n\n      arg.action = {\n        name: 'drag',\n        axis: dragOptions.lockAxis === 'start' ? dragOptions.startAxis : dragOptions.lockAxis,\n      }\n\n      return false\n    },\n  },\n  draggable,\n  beforeMove,\n  move,\n  defaults: {\n    startAxis: 'xy',\n    lockAxis: 'xy',\n  } as DraggableOptions,\n\n  getCursor() {\n    return 'move'\n  },\n\n  filterEventType: (type: string) => type.search('drag') === 0,\n}\n\nexport default drag\n", "const domObjects: {\n  init: any\n  document: Document\n  DocumentFragment: typeof DocumentFragment\n  SVGElement: typeof SVGElement\n  SVGSVGElement: typeof SVGSVGElement\n  SVGElementInstance: any\n  Element: typeof Element\n  HTMLElement: typeof HTMLElement\n  Event: typeof Event\n  Touch: typeof Touch\n  PointerEvent: typeof PointerEvent\n} = {\n  init,\n  document: null,\n  DocumentFragment: null,\n  SVGElement: null,\n  SVGSVGElement: null,\n  SVGElementInstance: null,\n  Element: null,\n  HTMLElement: null,\n  Event: null,\n  Touch: null,\n  PointerEvent: null,\n}\n\nfunction blank() {}\n\nexport default domObjects\n\nfunction init(window: Window) {\n  const win = window as any\n\n  domObjects.document = win.document\n  domObjects.DocumentFragment = win.DocumentFragment || blank\n  domObjects.SVGElement = win.SVGElement || blank\n  domObjects.SVGSVGElement = win.SVGSVGElement || blank\n  domObjects.SVGElementInstance = win.SVGElementInstance || blank\n  domObjects.Element = win.Element || blank\n  domObjects.HTMLElement = win.HTMLElement || domObjects.Element\n\n  domObjects.Event = win.Event\n  domObjects.Touch = win.Touch || blank\n  domObjects.PointerEvent = win.PointerEvent || win.MSPointerEvent\n}\n", "import domObjects from './domObjects'\nimport is from './is'\n\nconst browser = {\n  init,\n  supportsTouch: null as boolean,\n  supportsPointerEvent: null as boolean,\n  isIOS7: null as boolean,\n  isIOS: null as boolean,\n  isIe9: null as boolean,\n  isOperaMobile: null as boolean,\n  prefixedMatchesSelector: null as 'matches',\n  pEventTypes: null as {\n    up: string\n    down: string\n    over: string\n    out: string\n    move: string\n    cancel: string\n  },\n  wheelEvent: null as string,\n}\n\nfunction init(window: any) {\n  const Element = domObjects.Element\n  const navigator: Partial<Navigator> = window.navigator || {}\n\n  // Does the browser support touch input?\n  browser.supportsTouch =\n    'ontouchstart' in window ||\n    (is.func(window.DocumentTouch) && domObjects.document instanceof window.DocumentTouch)\n\n  // Does the browser support PointerEvents\n  // https://github.com/taye/interact.js/issues/703#issuecomment-471570492\n  browser.supportsPointerEvent = (navigator as any).pointerEnabled !== false && !!domObjects.PointerEvent\n\n  browser.isIOS = /iP(hone|od|ad)/.test(navigator.platform)\n\n  // scrolling doesn't change the result of getClientRects on iOS 7\n  browser.isIOS7 = /iP(hone|od|ad)/.test(navigator.platform) && /OS 7[^\\d]/.test(navigator.appVersion)\n\n  browser.isIe9 = /MSIE 9/.test(navigator.userAgent)\n\n  // Opera Mobile must be handled differently\n  browser.isOperaMobile =\n    navigator.appName === 'Opera' && browser.supportsTouch && /Presto/.test(navigator.userAgent)\n\n  // prefix matchesSelector\n  browser.prefixedMatchesSelector = (\n    'matches' in Element.prototype\n      ? 'matches'\n      : 'webkitMatchesSelector' in Element.prototype\n        ? 'webkitMatchesSelector'\n        : 'mozMatchesSelector' in Element.prototype\n          ? 'mozMatchesSelector'\n          : 'oMatchesSelector' in Element.prototype\n            ? 'oMatchesSelector'\n            : 'msMatchesSelector'\n  ) as 'matches'\n\n  browser.pEventTypes = browser.supportsPointerEvent\n    ? domObjects.PointerEvent === window.MSPointerEvent\n      ? {\n          up: 'MSPointerUp',\n          down: 'MSPointerDown',\n          over: 'mouseover',\n          out: 'mouseout',\n          move: 'MSPointerMove',\n          cancel: 'MSPointerCancel',\n        }\n      : {\n          up: 'pointerup',\n          down: 'pointerdown',\n          over: 'pointerover',\n          out: 'pointerout',\n          move: 'pointermove',\n          cancel: 'pointercancel',\n        }\n    : null\n\n  // because Webkit and Opera still use 'mousewheel' event type\n  browser.wheelEvent = domObjects.document && 'onmousewheel' in domObjects.document ? 'mousewheel' : 'wheel'\n}\n\nexport default browser\n", "import type { Rect, Target, Element } from '@interactjs/core/types'\n\nimport browser from './browser'\nimport domObjects from './domObjects'\nimport is from './is'\nimport * as win from './window'\n\nexport function nodeContains(parent: Node, child: Node) {\n  if (parent.contains) {\n    return parent.contains(child as Node)\n  }\n\n  while (child) {\n    if (child === parent) {\n      return true\n    }\n\n    child = (child as Node).parentNode\n  }\n\n  return false\n}\n\nexport function closest(element: Node, selector: string) {\n  while (is.element(element)) {\n    if (matchesSelector(element, selector)) {\n      return element\n    }\n\n    element = parentNode(element)\n  }\n\n  return null\n}\n\nexport function parentNode(node: Node | Document) {\n  let parent = node.parentNode\n\n  if (is.docFrag(parent)) {\n    // skip past #shado-root fragments\n    // tslint:disable-next-line\n    while ((parent = (parent as any).host) && is.docFrag(parent)) {\n      continue\n    }\n\n    return parent\n  }\n\n  return parent\n}\n\nexport function matchesSelector(element: Element, selector: string) {\n  // remove /deep/ from selectors if shadowDOM polyfill is used\n  if (win.window !== win.realWindow) {\n    selector = selector.replace(/\\/deep\\//g, ' ')\n  }\n\n  return element[browser.prefixedMatchesSelector](selector)\n}\n\nconst getParent = (el: Node | Document | ShadowRoot) => el.parentNode || (el as ShadowRoot).host\n\n// Test for the element that's \"above\" all other qualifiers\nexport function indexOfDeepestElement(elements: Element[] | NodeListOf<globalThis.Element>) {\n  let deepestNodeParents: Node[] = []\n  let deepestNodeIndex: number\n\n  for (let i = 0; i < elements.length; i++) {\n    const currentNode = elements[i]\n    const deepestNode: Node = elements[deepestNodeIndex]\n\n    // node may appear in elements array multiple times\n    if (!currentNode || i === deepestNodeIndex) {\n      continue\n    }\n\n    if (!deepestNode) {\n      deepestNodeIndex = i\n      continue\n    }\n\n    const currentNodeParent = getParent(currentNode)\n    const deepestNodeParent = getParent(deepestNode)\n\n    // check if the deepest or current are document.documentElement/rootElement\n    // - if the current node is, do nothing and continue\n    if (currentNodeParent === currentNode.ownerDocument) {\n      continue\n    }\n    // - if deepest is, update with the current node and continue to next\n    else if (deepestNodeParent === currentNode.ownerDocument) {\n      deepestNodeIndex = i\n      continue\n    }\n\n    // compare zIndex of siblings\n    if (currentNodeParent === deepestNodeParent) {\n      if (zIndexIsHigherThan(currentNode, deepestNode)) {\n        deepestNodeIndex = i\n      }\n\n      continue\n    }\n\n    // populate the ancestry array for the latest deepest node\n    deepestNodeParents = deepestNodeParents.length ? deepestNodeParents : getNodeParents(deepestNode)\n\n    let ancestryStart: Node\n\n    // if the deepest node is an HTMLElement and the current node is a non root svg element\n    if (\n      deepestNode instanceof domObjects.HTMLElement &&\n      currentNode instanceof domObjects.SVGElement &&\n      !(currentNode instanceof domObjects.SVGSVGElement)\n    ) {\n      // TODO: is this check necessary? Was this for HTML elements embedded in SVG?\n      if (currentNode === deepestNodeParent) {\n        continue\n      }\n\n      ancestryStart = currentNode.ownerSVGElement\n    } else {\n      ancestryStart = currentNode\n    }\n\n    const currentNodeParents = getNodeParents(ancestryStart, deepestNode.ownerDocument)\n    let commonIndex = 0\n\n    // get (position of closest common ancestor) + 1\n    while (\n      currentNodeParents[commonIndex] &&\n      currentNodeParents[commonIndex] === deepestNodeParents[commonIndex]\n    ) {\n      commonIndex++\n    }\n\n    const parents = [\n      currentNodeParents[commonIndex - 1],\n      currentNodeParents[commonIndex],\n      deepestNodeParents[commonIndex],\n    ]\n\n    if (parents[0]) {\n      let child = parents[0].lastChild\n\n      while (child) {\n        if (child === parents[1]) {\n          deepestNodeIndex = i\n          deepestNodeParents = currentNodeParents\n\n          break\n        } else if (child === parents[2]) {\n          break\n        }\n\n        child = child.previousSibling\n      }\n    }\n  }\n\n  return deepestNodeIndex\n}\n\nfunction getNodeParents(node: Node, limit?: Node) {\n  const parents: Node[] = []\n  let parent: Node = node\n  let parentParent: Node\n\n  while ((parentParent = getParent(parent)) && parent !== limit && parentParent !== parent.ownerDocument) {\n    parents.unshift(parent)\n    parent = parentParent\n  }\n\n  return parents\n}\n\nfunction zIndexIsHigherThan(higherNode: Node, lowerNode: Node) {\n  const higherIndex = parseInt(win.getWindow(higherNode).getComputedStyle(higherNode).zIndex, 10) || 0\n  const lowerIndex = parseInt(win.getWindow(lowerNode).getComputedStyle(lowerNode).zIndex, 10) || 0\n\n  return higherIndex >= lowerIndex\n}\n\nexport function matchesUpTo(element: Element, selector: string, limit: Node) {\n  while (is.element(element)) {\n    if (matchesSelector(element, selector)) {\n      return true\n    }\n\n    element = parentNode(element) as Element\n\n    if (element === limit) {\n      return matchesSelector(element, selector)\n    }\n  }\n\n  return false\n}\n\nexport function getActualElement(element: Element) {\n  return (element as any).correspondingUseElement || element\n}\n\nexport function getScrollXY(relevantWindow?: Window) {\n  relevantWindow = relevantWindow || win.window\n  return {\n    x: relevantWindow.scrollX || relevantWindow.document.documentElement.scrollLeft,\n    y: relevantWindow.scrollY || relevantWindow.document.documentElement.scrollTop,\n  }\n}\n\nexport function getElementClientRect(element: Element): Required<Rect> {\n  const clientRect =\n    element instanceof domObjects.SVGElement ? element.getBoundingClientRect() : element.getClientRects()[0]\n\n  return (\n    clientRect && {\n      left: clientRect.left,\n      right: clientRect.right,\n      top: clientRect.top,\n      bottom: clientRect.bottom,\n      width: clientRect.width || clientRect.right - clientRect.left,\n      height: clientRect.height || clientRect.bottom - clientRect.top,\n    }\n  )\n}\n\nexport function getElementRect(element: Element) {\n  const clientRect = getElementClientRect(element)\n\n  if (!browser.isIOS7 && clientRect) {\n    const scroll = getScrollXY(win.getWindow(element))\n\n    clientRect.left += scroll.x\n    clientRect.right += scroll.x\n    clientRect.top += scroll.y\n    clientRect.bottom += scroll.y\n  }\n\n  return clientRect\n}\n\nexport function getPath(node: Node | Document) {\n  const path = []\n\n  while (node) {\n    path.push(node)\n    node = parentNode(node)\n  }\n\n  return path\n}\n\nexport function trySelector(value: Target) {\n  if (!is.string(value)) {\n    return false\n  }\n\n  // an exception will be raised if it is invalid\n  domObjects.document.querySelector(value)\n  return true\n}\n", "export default function extend<T, U extends object>(dest: U & Partial<T>, source: T): T & U {\n  for (const prop in source) {\n    ;(dest as unknown as T)[prop] = source[prop]\n  }\n\n  const ret = dest as T & U\n\n  return ret\n}\n", "import type {\n  HasGetRect,\n  RectResolvable,\n  Rect,\n  Element,\n  Point,\n  FullRect,\n  EdgeOptions,\n} from '@interactjs/core/types'\n\nimport { closest, getElementRect, parentNode } from './domUtils'\nimport extend from './extend'\nimport is from './is'\n\nexport function getStringOptionResult(value: any, target: HasGetRect, element: Node) {\n  if (value === 'parent') {\n    return parentNode(element)\n  }\n\n  if (value === 'self') {\n    return target.getRect(element as Element)\n  }\n\n  return closest(element, value)\n}\n\nexport function resolveRectLike<T extends any[]>(\n  value: RectResolvable<T>,\n  target?: HasGetRect,\n  element?: Node,\n  functionArgs?: T,\n) {\n  let returnValue: any = value\n  if (is.string(returnValue)) {\n    returnValue = getStringOptionResult(returnValue, target, element)\n  } else if (is.func(returnValue)) {\n    returnValue = returnValue(...functionArgs)\n  }\n\n  if (is.element(returnValue)) {\n    returnValue = getElementRect(returnValue)\n  }\n\n  return returnValue as Rect\n}\n\nexport function toFullRect(rect: Rect): FullRect {\n  const { top, left, bottom, right } = rect\n  const width = rect.width ?? rect.right - rect.left\n  const height = rect.height ?? rect.bottom - rect.top\n\n  return { top, left, bottom, right, width, height }\n}\n\nexport function rectToXY(rect: Rect | Point) {\n  return (\n    rect && {\n      x: 'x' in rect ? rect.x : rect.left,\n      y: 'y' in rect ? rect.y : rect.top,\n    }\n  )\n}\n\nexport function xywhToTlbr<T extends Partial<Rect & Point>>(rect: T) {\n  if (rect && !('left' in rect && 'top' in rect)) {\n    rect = extend({}, rect)\n\n    rect.left = rect.x || 0\n    rect.top = rect.y || 0\n    rect.right = rect.right || rect.left + rect.width\n    rect.bottom = rect.bottom || rect.top + rect.height\n  }\n\n  return rect as Rect & T\n}\n\nexport function tlbrToXywh(rect: Rect & Partial<Point>) {\n  if (rect && !('x' in rect && 'y' in rect)) {\n    rect = extend({}, rect)\n\n    rect.x = rect.left || 0\n    rect.y = rect.top || 0\n    rect.width = rect.width || (rect.right || 0) - rect.x\n    rect.height = rect.height || (rect.bottom || 0) - rect.y\n  }\n\n  return rect as FullRect & Point\n}\n\nexport function addEdges(edges: EdgeOptions, rect: Rect, delta: Point) {\n  if (edges.left) {\n    rect.left += delta.x\n  }\n  if (edges.right) {\n    rect.right += delta.x\n  }\n  if (edges.top) {\n    rect.top += delta.y\n  }\n  if (edges.bottom) {\n    rect.bottom += delta.y\n  }\n\n  rect.width = rect.right - rect.left\n  rect.height = rect.bottom - rect.top\n}\n", "import type { PerActionDefaults } from '@interactjs/core/options'\nimport type { ActionName, HasGetRect } from '@interactjs/core/types'\n\nimport { rectToXY, resolveRectLike } from './rect'\n\nexport default function getOriginXY(\n  target: HasGetRect & { options: PerActionDefaults },\n  element: Node,\n  actionName?: ActionName,\n) {\n  const actionOptions = actionName && (target.options as any)[actionName]\n  const actionOrigin = actionOptions && actionOptions.origin\n  const origin = actionOrigin || target.options.origin\n\n  const originRect = resolveRectLike(origin, target, element, [target && element])\n\n  return rectToXY(originRect) || { x: 0, y: 0 }\n}\n", "import type { EventTypes, Listener, ListenersArg } from '@interactjs/core/types'\n\nimport is from './is'\n\nexport interface NormalizedListeners {\n  [type: string]: Listener[]\n}\n\nexport default function normalize(\n  type: EventTypes,\n  listeners?: ListenersArg | ListenersArg[] | null,\n  filter = (_typeOrPrefix: string) => true,\n  result?: NormalizedListeners,\n): NormalizedListeners {\n  result = result || {}\n\n  if (is.string(type) && type.search(' ') !== -1) {\n    type = split(type)\n  }\n\n  if (is.array(type)) {\n    type.forEach((t) => normalize(t, listeners, filter, result))\n    return result\n  }\n\n  // before:  type = [{ drag: () => {} }], listeners = undefined\n  // after:   type = ''                  , listeners = [{ drag: () => {} }]\n  if (is.object(type)) {\n    listeners = type\n    type = ''\n  }\n\n  if (is.func(listeners) && filter(type)) {\n    result[type] = result[type] || []\n    result[type].push(listeners)\n  } else if (is.array(listeners)) {\n    for (const l of listeners) {\n      normalize(type, l, filter, result)\n    }\n  } else if (is.object(listeners)) {\n    for (const prefix in listeners) {\n      const combinedTypes = split(prefix).map((p) => `${type}${p}`)\n\n      normalize(combinedTypes, listeners[prefix], filter, result)\n    }\n  }\n\n  return result as NormalizedListeners\n}\n\nfunction split(type: string) {\n  return type.trim().split(/ +/)\n}\n", "export default (x: number, y: number) => Math.sqrt(x * x + y * y)\n", "const VENDOR_PREFIXES = ['webkit', 'moz']\n\nexport default function pointerExtend<T>(dest: Partial<T & { __set?: Partial<T> }>, source: T) {\n  dest.__set ||= {} as any\n\n  for (const prop in source) {\n    // skip deprecated prefixed properties\n    if (VENDOR_PREFIXES.some((prefix) => prop.indexOf(prefix) === 0)) continue\n\n    if (typeof dest[prop] !== 'function' && prop !== '__set') {\n      Object.defineProperty(dest, prop, {\n        get() {\n          if (prop in dest.__set) return dest.__set[prop]\n\n          return (dest.__set[prop] = source[prop] as any)\n        },\n        set(value: any) {\n          dest.__set[prop] = value\n        },\n        configurable: true,\n      })\n    }\n  }\n  return dest\n}\n", "import type { InteractEvent } from '@interactjs/core/InteractEvent'\nimport type { CoordsSetMember, PointerType, Point, PointerEventType, Element } from '@interactjs/core/types'\n\nimport browser from './browser'\nimport dom from './domObjects'\nimport * as domUtils from './domUtils'\nimport hypot from './hypot'\nimport is from './is'\nimport pointerExtend from './pointerExtend'\n\nexport function copyCoords(dest: CoordsSetMember, src: CoordsSetMember) {\n  dest.page = dest.page || ({} as any)\n  dest.page.x = src.page.x\n  dest.page.y = src.page.y\n\n  dest.client = dest.client || ({} as any)\n  dest.client.x = src.client.x\n  dest.client.y = src.client.y\n\n  dest.timeStamp = src.timeStamp\n}\n\nexport function setCoordDeltas(targetObj: CoordsSetMember, prev: CoordsSetMember, cur: CoordsSetMember) {\n  targetObj.page.x = cur.page.x - prev.page.x\n  targetObj.page.y = cur.page.y - prev.page.y\n  targetObj.client.x = cur.client.x - prev.client.x\n  targetObj.client.y = cur.client.y - prev.client.y\n  targetObj.timeStamp = cur.timeStamp - prev.timeStamp\n}\n\nexport function setCoordVelocity(targetObj: CoordsSetMember, delta: CoordsSetMember) {\n  const dt = Math.max(delta.timeStamp / 1000, 0.001)\n\n  targetObj.page.x = delta.page.x / dt\n  targetObj.page.y = delta.page.y / dt\n  targetObj.client.x = delta.client.x / dt\n  targetObj.client.y = delta.client.y / dt\n  targetObj.timeStamp = dt\n}\n\nexport function setZeroCoords(targetObj: CoordsSetMember) {\n  targetObj.page.x = 0\n  targetObj.page.y = 0\n  targetObj.client.x = 0\n  targetObj.client.y = 0\n}\n\nexport function isNativePointer(pointer: any) {\n  return pointer instanceof dom.Event || pointer instanceof dom.Touch\n}\n\n// Get specified X/Y coords for mouse or event.touches[0]\nexport function getXY(type: string, pointer: PointerType | InteractEvent, xy: Point) {\n  xy = xy || ({} as Point)\n  type = type || 'page'\n\n  xy.x = pointer[(type + 'X') as 'pageX']\n  xy.y = pointer[(type + 'Y') as 'pageY']\n\n  return xy\n}\n\nexport function getPageXY(pointer: PointerType | InteractEvent, page?: Point) {\n  page = page || { x: 0, y: 0 }\n\n  // Opera Mobile handles the viewport and scrolling oddly\n  if (browser.isOperaMobile && isNativePointer(pointer)) {\n    getXY('screen', pointer, page)\n\n    page.x += window.scrollX\n    page.y += window.scrollY\n  } else {\n    getXY('page', pointer, page)\n  }\n\n  return page\n}\n\nexport function getClientXY(pointer: PointerType, client: Point) {\n  client = client || ({} as any)\n\n  if (browser.isOperaMobile && isNativePointer(pointer)) {\n    // Opera Mobile handles the viewport and scrolling oddly\n    getXY('screen', pointer, client)\n  } else {\n    getXY('client', pointer, client)\n  }\n\n  return client\n}\n\nexport function getPointerId(pointer: { pointerId?: number; identifier?: number; type?: string }) {\n  return is.number(pointer.pointerId) ? pointer.pointerId! : pointer.identifier!\n}\n\nexport function setCoords(dest: CoordsSetMember, pointers: any[], timeStamp: number) {\n  const pointer = pointers.length > 1 ? pointerAverage(pointers) : pointers[0]\n\n  getPageXY(pointer, dest.page)\n  getClientXY(pointer, dest.client)\n\n  dest.timeStamp = timeStamp\n}\n\nexport function getTouchPair(event: TouchEvent | PointerType[]) {\n  const touches: PointerType[] = []\n\n  // array of touches is supplied\n  if (is.array(event)) {\n    touches[0] = event[0]\n    touches[1] = event[1]\n  }\n  // an event\n  else {\n    if (event.type === 'touchend') {\n      if (event.touches.length === 1) {\n        touches[0] = event.touches[0]\n        touches[1] = event.changedTouches[0]\n      } else if (event.touches.length === 0) {\n        touches[0] = event.changedTouches[0]\n        touches[1] = event.changedTouches[1]\n      }\n    } else {\n      touches[0] = event.touches[0]\n      touches[1] = event.touches[1]\n    }\n  }\n\n  return touches\n}\n\nexport function pointerAverage(pointers: PointerType[]) {\n  const average = {\n    pageX: 0,\n    pageY: 0,\n    clientX: 0,\n    clientY: 0,\n    screenX: 0,\n    screenY: 0,\n  }\n\n  type CoordKeys = keyof typeof average\n\n  for (const pointer of pointers) {\n    for (const prop in average) {\n      average[prop as CoordKeys] += pointer[prop as CoordKeys]\n    }\n  }\n  for (const prop in average) {\n    average[prop as CoordKeys] /= pointers.length\n  }\n\n  return average\n}\n\nexport function touchBBox(event: PointerType[]) {\n  if (!event.length) {\n    return null\n  }\n\n  const touches = getTouchPair(event)\n  const minX = Math.min(touches[0].pageX, touches[1].pageX)\n  const minY = Math.min(touches[0].pageY, touches[1].pageY)\n  const maxX = Math.max(touches[0].pageX, touches[1].pageX)\n  const maxY = Math.max(touches[0].pageY, touches[1].pageY)\n\n  return {\n    x: minX,\n    y: minY,\n    left: minX,\n    top: minY,\n    right: maxX,\n    bottom: maxY,\n    width: maxX - minX,\n    height: maxY - minY,\n  }\n}\n\nexport function touchDistance(event: PointerType[] | TouchEvent, deltaSource: string) {\n  const sourceX = (deltaSource + 'X') as 'pageX'\n  const sourceY = (deltaSource + 'Y') as 'pageY'\n  const touches = getTouchPair(event)\n\n  const dx = touches[0][sourceX] - touches[1][sourceX]\n  const dy = touches[0][sourceY] - touches[1][sourceY]\n\n  return hypot(dx, dy)\n}\n\nexport function touchAngle(event: PointerType[] | TouchEvent, deltaSource: string) {\n  const sourceX = (deltaSource + 'X') as 'pageX'\n  const sourceY = (deltaSource + 'Y') as 'pageY'\n  const touches = getTouchPair(event)\n  const dx = touches[1][sourceX] - touches[0][sourceX]\n  const dy = touches[1][sourceY] - touches[0][sourceY]\n  const angle = (180 * Math.atan2(dy, dx)) / Math.PI\n\n  return angle\n}\n\nexport function getPointerType(pointer: { pointerType?: string; identifier?: number; type?: string }) {\n  return is.string(pointer.pointerType)\n    ? pointer.pointerType\n    : is.number(pointer.pointerType)\n      ? [undefined, undefined, 'touch', 'pen', 'mouse'][pointer.pointerType]!\n      : // if the PointerEvent API isn't available, then the \"pointer\" must\n        // be either a MouseEvent, TouchEvent, or Touch object\n        /touch/.test(pointer.type || '') || pointer instanceof dom.Touch\n        ? 'touch'\n        : 'mouse'\n}\n\n// [ event.target, event.currentTarget ]\nexport function getEventTargets(event: Event) {\n  const path = is.func(event.composedPath)\n    ? (event.composedPath() as Element[])\n    : (event as unknown as { path: Element[] }).path\n\n  return [\n    domUtils.getActualElement(path ? path[0] : (event.target as Element)),\n    domUtils.getActualElement(event.currentTarget as Element),\n  ]\n}\n\nexport function newCoords(): CoordsSetMember {\n  return {\n    page: { x: 0, y: 0 },\n    client: { x: 0, y: 0 },\n    timeStamp: 0,\n  }\n}\n\nexport function coordsToEvent(coords: MockCoords) {\n  const event = {\n    coords,\n    get page() {\n      return this.coords.page\n    },\n    get client() {\n      return this.coords.client\n    },\n    get timeStamp() {\n      return this.coords.timeStamp\n    },\n    get pageX() {\n      return this.coords.page.x\n    },\n    get pageY() {\n      return this.coords.page.y\n    },\n    get clientX() {\n      return this.coords.client.x\n    },\n    get clientY() {\n      return this.coords.client.y\n    },\n    get pointerId() {\n      return this.coords.pointerId\n    },\n    get target() {\n      return this.coords.target\n    },\n    get type() {\n      return this.coords.type\n    },\n    get pointerType() {\n      return this.coords.pointerType\n    },\n    get buttons() {\n      return this.coords.buttons\n    },\n    preventDefault() {},\n  }\n\n  return event as typeof event & PointerType & PointerEventType\n}\n\nexport interface MockCoords {\n  page: Point\n  client: Point\n  timeStamp?: number\n  pointerId?: any\n  target?: any\n  type?: string\n  pointerType?: string\n  buttons?: number\n}\n\nexport { pointerExtend }\n", "import type { Interactable } from '@interactjs/core/Interactable'\nimport type { Interaction, InteractionProxy } from '@interactjs/core/Interaction'\nimport type { ActionName } from '@interactjs/core/types'\n\nexport class BaseEvent<T extends ActionName | null = never> {\n  declare type: string\n  declare target: EventTarget\n  declare currentTarget: Node\n  declare interactable: Interactable\n  /** @internal */\n  declare _interaction: Interaction<T>\n  declare timeStamp: number\n  immediatePropagationStopped = false\n  propagationStopped = false\n\n  constructor(interaction: Interaction<T>) {\n    this._interaction = interaction\n  }\n\n  preventDefault() {}\n\n  /**\n   * Don't call any other listeners (even on the current target)\n   */\n  stopPropagation() {\n    this.propagationStopped = true\n  }\n\n  /**\n   * Don't call listeners on the remaining targets\n   */\n  stopImmediatePropagation() {\n    this.immediatePropagationStopped = this.propagationStopped = true\n  }\n}\n\n// defined outside of class definition to avoid assignment of undefined during\n// construction\nexport interface BaseEvent<T extends ActionName | null = never> {\n  interaction: InteractionProxy<T>\n}\n\n// getters and setters defined here to support typescript 3.6 and below which\n// don't support getter and setters in .d.ts files\nObject.defineProperty(BaseEvent.prototype, 'interaction', {\n  get(this: BaseEvent) {\n    return this._interaction._proxy\n  },\n  set(this: BaseEvent) {},\n})\n", "type Filter<T> = (element: T, index: number, array: T[]) => boolean\n\nexport const contains = <T>(array: T[], target: T) => array.indexOf(target) !== -1\n\nexport const remove = <T>(array: T[], target: T) => array.splice(array.indexOf(target), 1)\n\nexport const merge = <T, U>(target: Array<T | U>, source: U[]) => {\n  for (const item of source) {\n    target.push(item)\n  }\n\n  return target\n}\n\nexport const from = <T = any>(source: ArrayLike<T>) => merge([] as T[], source as T[])\n\nexport const findIndex = <T>(array: T[], func: Filter<T>) => {\n  for (let i = 0; i < array.length; i++) {\n    if (func(array[i], i, array)) {\n      return i\n    }\n  }\n\n  return -1\n}\n\nexport const find = <T = any>(array: T[], func: Filter<T>): T | undefined => array[findIndex(array, func)]\n", "import { BaseEvent } from '@interactjs/core/BaseEvent'\nimport type { Interactable } from '@interactjs/core/Interactable'\nimport type { InteractEvent } from '@interactjs/core/InteractEvent'\nimport type { Element } from '@interactjs/core/types'\nimport * as arr from '@interactjs/utils/arr'\n\nimport type { DropState } from './plugin'\n\nexport class DropEvent extends BaseEvent<'drag'> {\n  declare target: Element\n  dropzone: Interactable\n  dragEvent: InteractEvent<'drag'>\n  relatedTarget: Element\n  draggable: Interactable\n  propagationStopped = false\n  immediatePropagationStopped = false\n\n  /**\n   * Class of events fired on dropzones during drags with acceptable targets.\n   */\n  constructor(dropState: DropState, dragEvent: InteractEvent<'drag'>, type: string) {\n    super(dragEvent._interaction)\n\n    const { element, dropzone } = type === 'dragleave' ? dropState.prev : dropState.cur\n\n    this.type = type\n    this.target = element\n    this.currentTarget = element\n    this.dropzone = dropzone\n    this.dragEvent = dragEvent\n    this.relatedTarget = dragEvent.target\n    this.draggable = dragEvent.interactable\n    this.timeStamp = dragEvent.timeStamp\n  }\n\n  /**\n   * If this is a `dropactivate` event, the dropzone element will be\n   * deactivated.\n   *\n   * If this is a `dragmove` or `dragenter`, a `dragleave` will be fired on the\n   * dropzone element and more.\n   */\n  reject() {\n    const { dropState } = this._interaction\n\n    if (\n      this.type !== 'dropactivate' &&\n      (!this.dropzone || dropState.cur.dropzone !== this.dropzone || dropState.cur.element !== this.target)\n    ) {\n      return\n    }\n\n    dropState.prev.dropzone = this.dropzone\n    dropState.prev.element = this.target\n\n    dropState.rejected = true\n    dropState.events.enter = null\n\n    this.stopImmediatePropagation()\n\n    if (this.type === 'dropactivate') {\n      const activeDrops = dropState.activeDrops\n      const index = arr.findIndex(\n        activeDrops,\n        ({ dropzone, element }) => dropzone === this.dropzone && element === this.target,\n      )\n\n      dropState.activeDrops.splice(index, 1)\n\n      const deactivateEvent = new DropEvent(dropState, this.dragEvent, 'dropdeactivate')\n\n      deactivateEvent.dropzone = this.dropzone\n      deactivateEvent.target = this.target\n\n      this.dropzone.fire(deactivateEvent)\n    } else {\n      this.dropzone.fire(new DropEvent(dropState, this.dragEvent, 'dragleave'))\n    }\n  }\n\n  preventDefault() {}\n\n  stopPropagation() {\n    this.propagationStopped = true\n  }\n\n  stopImmediatePropagation() {\n    this.immediatePropagationStopped = this.propagationStopped = true\n  }\n}\n", "import type { Interactable } from '@interactjs/core/Interactable'\nimport type { EventPhase, InteractEvent } from '@interactjs/core/InteractEvent'\nimport type { Interaction, DoPhaseArg } from '@interactjs/core/Interaction'\nimport type { PerActionDefaults } from '@interactjs/core/options'\nimport type { Scope, Plugin } from '@interactjs/core/scope'\nimport type { Element, PointerEventType, Rect, ListenersArg } from '@interactjs/core/types'\nimport * as domUtils from '@interactjs/utils/domUtils'\nimport extend from '@interactjs/utils/extend'\nimport getOriginXY from '@interactjs/utils/getOriginXY'\nimport is from '@interactjs/utils/is'\nimport normalizeListeners from '@interactjs/utils/normalizeListeners'\nimport * as pointerUtils from '@interactjs/utils/pointerUtils'\n\n/* eslint-disable import/no-duplicates -- for typescript module augmentations */\nimport '../drag/plugin'\n\nimport type { DragEvent } from '../drag/plugin'\nimport drag from '../drag/plugin'\n/* eslint-enable import/no-duplicates */\n\nimport { DropEvent } from './DropEvent'\n\nexport type DropFunctionChecker = (\n  dragEvent: any, // related drag operation\n  event: any, // touch or mouse EventEmitter\n  dropped: boolean, // default checker result\n  dropzone: Interactable, // dropzone interactable\n  dropElement: Element, // drop zone element\n  draggable: Interactable, // draggable's Interactable\n  draggableElement: Element, // dragged element\n) => boolean\n\nexport interface DropzoneOptions extends PerActionDefaults {\n  accept?:\n    | string\n    | Element\n    | (({ dropzone, draggableElement }: { dropzone: Interactable; draggableElement: Element }) => boolean)\n  // How the overlap is checked on the drop zone\n  overlap?: 'pointer' | 'center' | number\n  checker?: DropFunctionChecker\n\n  ondropactivate?: ListenersArg\n  ondropdeactivate?: ListenersArg\n  ondragenter?: ListenersArg\n  ondragleave?: ListenersArg\n  ondropmove?: ListenersArg\n  ondrop?: ListenersArg\n}\n\nexport interface DropzoneMethod {\n  (this: Interactable, options: DropzoneOptions | boolean): Interactable\n  (): DropzoneOptions\n}\n\ndeclare module '@interactjs/core/Interactable' {\n  interface Interactable {\n    /**\n     *\n     * ```js\n     * interact('.drop').dropzone({\n     *   accept: '.can-drop' || document.getElementById('single-drop'),\n     *   overlap: 'pointer' || 'center' || zeroToOne\n     * }\n     * ```\n     *\n     * Returns or sets whether draggables can be dropped onto this target to\n     * trigger drop events\n     *\n     * Dropzones can receive the following events:\n     *  - `dropactivate` and `dropdeactivate` when an acceptable drag starts and ends\n     *  - `dragenter` and `dragleave` when a draggable enters and leaves the dropzone\n     *  - `dragmove` when a draggable that has entered the dropzone is moved\n     *  - `drop` when a draggable is dropped into this dropzone\n     *\n     * Use the `accept` option to allow only elements that match the given CSS\n     * selector or element. The value can be:\n     *\n     *  - **an Element** - only that element can be dropped into this dropzone.\n     *  - **a string**, - the element being dragged must match it as a CSS selector.\n     *  - **`null`** - accept options is cleared - it accepts any element.\n     *\n     * Use the `overlap` option to set how drops are checked for. The allowed\n     * values are:\n     *\n     *   - `'pointer'`, the pointer must be over the dropzone (default)\n     *   - `'center'`, the draggable element's center must be over the dropzone\n     *   - a number from 0-1 which is the `(intersection area) / (draggable area)`.\n     *   e.g. `0.5` for drop to happen when half of the area of the draggable is\n     *   over the dropzone\n     *\n     * Use the `checker` option to specify a function to check if a dragged element\n     * is over this Interactable.\n     *\n     * @param options - The new options to be set\n     */\n    dropzone(options: DropzoneOptions | boolean): Interactable\n    /** @returns The current setting */\n    dropzone(): DropzoneOptions\n\n    /**\n     * ```js\n     * interact(target)\n     * .dropChecker(function(dragEvent,         // related dragmove or dragend event\n     *                       event,             // TouchEvent/PointerEvent/MouseEvent\n     *                       dropped,           // bool result of the default checker\n     *                       dropzone,          // dropzone Interactable\n     *                       dropElement,       // dropzone elemnt\n     *                       draggable,         // draggable Interactable\n     *                       draggableElement) {// draggable element\n     *\n     *   return dropped && event.target.hasAttribute('allow-drop')\n     * }\n     * ```\n     */\n    dropCheck(\n      dragEvent: InteractEvent,\n      event: PointerEventType,\n      draggable: Interactable,\n      draggableElement: Element,\n      dropElemen: Element,\n      rect: any,\n    ): boolean\n  }\n}\n\ndeclare module '@interactjs/core/Interaction' {\n  interface Interaction {\n    dropState?: DropState\n  }\n}\n\ndeclare module '@interactjs/core/InteractEvent' {\n  interface InteractEvent {\n    /** @internal */\n    prevDropzone?: Interactable\n    dropzone?: Interactable\n    dragEnter?: Element\n    dragLeave?: Element\n  }\n}\n\ndeclare module '@interactjs/core/options' {\n  interface ActionDefaults {\n    drop: DropzoneOptions\n  }\n}\n\ndeclare module '@interactjs/core/scope' {\n  interface Scope {\n    dynamicDrop?: boolean\n  }\n\n  interface SignalArgs {\n    'actions/drop:start': DropSignalArg\n    'actions/drop:move': DropSignalArg\n    'actions/drop:end': DropSignalArg\n  }\n}\n\ndeclare module '@interactjs/core/types' {\n  interface ActionMap {\n    drop?: typeof drop\n  }\n}\n\ndeclare module '@interactjs/core/InteractStatic' {\n  interface InteractStatic {\n    /**\n     * Returns or sets whether the dimensions of dropzone elements are calculated\n     * on every dragmove or only on dragstart for the default dropChecker\n     *\n     * @param {boolean} [newValue] True to check on each move. False to check only\n     * before start\n     * @return {boolean | interact} The current setting or interact\n     */\n    dynamicDrop: (newValue?: boolean) => boolean | this\n  }\n}\n\ninterface DropSignalArg {\n  interaction: Interaction<'drag'>\n  dragEvent: DragEvent\n}\n\nexport interface ActiveDrop {\n  dropzone: Interactable\n  element: Element\n  rect: Rect\n}\n\nexport interface DropState {\n  cur: {\n    // the dropzone a drag target might be dropped into\n    dropzone: Interactable\n    // the element at the time of checking\n    element: Element\n  }\n  prev: {\n    // the dropzone that was recently dragged away from\n    dropzone: Interactable\n    // the element at the time of checking\n    element: Element\n  }\n  // wheather the potential drop was rejected from a listener\n  rejected: boolean\n  // the drop events related to the current drag event\n  events: FiredDropEvents\n  activeDrops: ActiveDrop[]\n}\n\nfunction install(scope: Scope) {\n  const { actions, interactStatic: interact, Interactable, defaults } = scope\n\n  scope.usePlugin(drag)\n\n  Interactable.prototype.dropzone = function (this: Interactable, options) {\n    return dropzoneMethod(this, options)\n  } as Interactable['dropzone']\n\n  Interactable.prototype.dropCheck = function (\n    this: Interactable,\n    dragEvent,\n    event,\n    draggable,\n    draggableElement,\n    dropElement,\n    rect,\n  ) {\n    return dropCheckMethod(this, dragEvent, event, draggable, draggableElement, dropElement, rect)\n  }\n\n  interact.dynamicDrop = function (newValue?: boolean) {\n    if (is.bool(newValue)) {\n      // if (dragging && scope.dynamicDrop !== newValue && !newValue) {\n      //  calcRects(dropzones)\n      // }\n\n      scope.dynamicDrop = newValue\n\n      return interact\n    }\n    return scope.dynamicDrop!\n  }\n\n  extend(actions.phaselessTypes, {\n    dragenter: true,\n    dragleave: true,\n    dropactivate: true,\n    dropdeactivate: true,\n    dropmove: true,\n    drop: true,\n  })\n  actions.methodDict.drop = 'dropzone'\n\n  scope.dynamicDrop = false\n\n  defaults.actions.drop = drop.defaults\n}\n\nfunction collectDropzones({ interactables }: Scope, draggableElement: Element) {\n  const drops: ActiveDrop[] = []\n\n  // collect all dropzones and their elements which qualify for a drop\n  for (const dropzone of interactables.list) {\n    if (!dropzone.options.drop.enabled) {\n      continue\n    }\n\n    const accept = dropzone.options.drop.accept\n\n    // test the draggable draggableElement against the dropzone's accept setting\n    if (\n      (is.element(accept) && accept !== draggableElement) ||\n      (is.string(accept) && !domUtils.matchesSelector(draggableElement, accept)) ||\n      (is.func(accept) && !accept({ dropzone, draggableElement }))\n    ) {\n      continue\n    }\n\n    for (const dropzoneElement of dropzone.getAllElements()) {\n      if (dropzoneElement !== draggableElement) {\n        drops.push({\n          dropzone,\n          element: dropzoneElement,\n          rect: dropzone.getRect(dropzoneElement),\n        })\n      }\n    }\n  }\n\n  return drops\n}\n\nfunction fireActivationEvents(activeDrops: ActiveDrop[], event: DropEvent) {\n  // loop through all active dropzones and trigger event\n  for (const { dropzone, element } of activeDrops.slice()) {\n    event.dropzone = dropzone\n\n    // set current element as event target\n    event.target = element\n    dropzone.fire(event)\n    event.propagationStopped = event.immediatePropagationStopped = false\n  }\n}\n\n// return a new array of possible drops. getActiveDrops should always be\n// called when a drag has just started or a drag event happens while\n// dynamicDrop is true\nfunction getActiveDrops(scope: Scope, dragElement: Element) {\n  // get dropzones and their elements that could receive the draggable\n  const activeDrops = collectDropzones(scope, dragElement)\n\n  for (const activeDrop of activeDrops) {\n    activeDrop.rect = activeDrop.dropzone.getRect(activeDrop.element)\n  }\n\n  return activeDrops\n}\n\nfunction getDrop(\n  { dropState, interactable: draggable, element: dragElement }: Interaction,\n  dragEvent,\n  pointerEvent,\n) {\n  const validDrops: Element[] = []\n\n  // collect all dropzones and their elements which qualify for a drop\n  for (const { dropzone, element: dropzoneElement, rect } of dropState.activeDrops) {\n    const isValid = dropzone.dropCheck(\n      dragEvent,\n      pointerEvent,\n      draggable!,\n      dragElement!,\n      dropzoneElement,\n      rect,\n    )\n    validDrops.push(isValid ? dropzoneElement : null)\n  }\n\n  // get the most appropriate dropzone based on DOM depth and order\n  const dropIndex = domUtils.indexOfDeepestElement(validDrops)\n\n  return dropState!.activeDrops[dropIndex] || null\n}\n\nfunction getDropEvents(interaction: Interaction, _pointerEvent, dragEvent: DragEvent) {\n  const dropState = interaction.dropState!\n  const dropEvents: Record<string, DropEvent | null> = {\n    enter: null,\n    leave: null,\n    activate: null,\n    deactivate: null,\n    move: null,\n    drop: null,\n  }\n\n  if (dragEvent.type === 'dragstart') {\n    dropEvents.activate = new DropEvent(dropState, dragEvent, 'dropactivate')\n\n    dropEvents.activate.target = null as never\n    dropEvents.activate.dropzone = null as never\n  }\n  if (dragEvent.type === 'dragend') {\n    dropEvents.deactivate = new DropEvent(dropState, dragEvent, 'dropdeactivate')\n\n    dropEvents.deactivate.target = null as never\n    dropEvents.deactivate.dropzone = null as never\n  }\n\n  if (dropState.rejected) {\n    return dropEvents\n  }\n\n  if (dropState.cur.element !== dropState.prev.element) {\n    // if there was a previous dropzone, create a dragleave event\n    if (dropState.prev.dropzone) {\n      dropEvents.leave = new DropEvent(dropState, dragEvent, 'dragleave')\n\n      dragEvent.dragLeave = dropEvents.leave.target = dropState.prev.element\n      dragEvent.prevDropzone = dropEvents.leave.dropzone = dropState.prev.dropzone\n    }\n    // if dropzone is not null, create a dragenter event\n    if (dropState.cur.dropzone) {\n      dropEvents.enter = new DropEvent(dropState, dragEvent, 'dragenter')\n\n      dragEvent.dragEnter = dropState.cur.element\n      dragEvent.dropzone = dropState.cur.dropzone\n    }\n  }\n\n  if (dragEvent.type === 'dragend' && dropState.cur.dropzone) {\n    dropEvents.drop = new DropEvent(dropState, dragEvent, 'drop')\n\n    dragEvent.dropzone = dropState.cur.dropzone\n    dragEvent.relatedTarget = dropState.cur.element\n  }\n  if (dragEvent.type === 'dragmove' && dropState.cur.dropzone) {\n    dropEvents.move = new DropEvent(dropState, dragEvent, 'dropmove')\n\n    dragEvent.dropzone = dropState.cur.dropzone\n  }\n\n  return dropEvents\n}\n\ntype FiredDropEvents = Partial<\n  Record<'leave' | 'enter' | 'move' | 'drop' | 'activate' | 'deactivate', DropEvent>\n>\n\nfunction fireDropEvents(interaction: Interaction, events: FiredDropEvents) {\n  const dropState = interaction.dropState!\n  const { activeDrops, cur, prev } = dropState\n\n  if (events.leave) {\n    prev.dropzone.fire(events.leave)\n  }\n  if (events.enter) {\n    cur.dropzone.fire(events.enter)\n  }\n  if (events.move) {\n    cur.dropzone.fire(events.move)\n  }\n  if (events.drop) {\n    cur.dropzone.fire(events.drop)\n  }\n\n  if (events.deactivate) {\n    fireActivationEvents(activeDrops, events.deactivate)\n  }\n\n  dropState.prev.dropzone = cur.dropzone\n  dropState.prev.element = cur.element\n}\n\nfunction onEventCreated({ interaction, iEvent, event }: DoPhaseArg<'drag', EventPhase>, scope: Scope) {\n  if (iEvent.type !== 'dragmove' && iEvent.type !== 'dragend') {\n    return\n  }\n\n  const dropState = interaction.dropState!\n\n  if (scope.dynamicDrop) {\n    dropState.activeDrops = getActiveDrops(scope, interaction.element!)\n  }\n\n  const dragEvent = iEvent\n  const dropResult = getDrop(interaction, dragEvent, event)\n\n  // update rejected status\n  dropState.rejected =\n    dropState.rejected &&\n    !!dropResult &&\n    dropResult.dropzone === dropState.cur.dropzone &&\n    dropResult.element === dropState.cur.element\n\n  dropState.cur.dropzone = dropResult && dropResult.dropzone\n  dropState.cur.element = dropResult && dropResult.element\n\n  dropState.events = getDropEvents(interaction, event, dragEvent)\n}\n\nfunction dropzoneMethod(interactable: Interactable): DropzoneOptions\nfunction dropzoneMethod(interactable: Interactable, options: DropzoneOptions | boolean): Interactable\nfunction dropzoneMethod(interactable: Interactable, options?: DropzoneOptions | boolean) {\n  if (is.object(options)) {\n    interactable.options.drop.enabled = options.enabled !== false\n\n    if (options.listeners) {\n      const normalized = normalizeListeners(options.listeners)\n      // rename 'drop' to '' as it will be prefixed with 'drop'\n      const corrected = Object.keys(normalized).reduce((acc, type) => {\n        const correctedType = /^(enter|leave)/.test(type)\n          ? `drag${type}`\n          : /^(activate|deactivate|move)/.test(type)\n            ? `drop${type}`\n            : type\n\n        acc[correctedType] = normalized[type]\n\n        return acc\n      }, {})\n\n      const prevListeners = interactable.options.drop.listeners\n      prevListeners && interactable.off(prevListeners)\n\n      interactable.on(corrected)\n      interactable.options.drop.listeners = corrected\n    }\n\n    if (is.func(options.ondrop)) {\n      interactable.on('drop', options.ondrop)\n    }\n    if (is.func(options.ondropactivate)) {\n      interactable.on('dropactivate', options.ondropactivate)\n    }\n    if (is.func(options.ondropdeactivate)) {\n      interactable.on('dropdeactivate', options.ondropdeactivate)\n    }\n    if (is.func(options.ondragenter)) {\n      interactable.on('dragenter', options.ondragenter)\n    }\n    if (is.func(options.ondragleave)) {\n      interactable.on('dragleave', options.ondragleave)\n    }\n    if (is.func(options.ondropmove)) {\n      interactable.on('dropmove', options.ondropmove)\n    }\n\n    if (/^(pointer|center)$/.test(options.overlap as string)) {\n      interactable.options.drop.overlap = options.overlap\n    } else if (is.number(options.overlap)) {\n      interactable.options.drop.overlap = Math.max(Math.min(1, options.overlap), 0)\n    }\n    if ('accept' in options) {\n      interactable.options.drop.accept = options.accept\n    }\n    if ('checker' in options) {\n      interactable.options.drop.checker = options.checker\n    }\n\n    return interactable\n  }\n\n  if (is.bool(options)) {\n    interactable.options.drop.enabled = options\n\n    return interactable\n  }\n\n  return interactable.options.drop\n}\n\nfunction dropCheckMethod(\n  interactable: Interactable,\n  dragEvent: InteractEvent,\n  event: PointerEventType,\n  draggable: Interactable,\n  draggableElement: Element,\n  dropElement: Element,\n  rect: any,\n) {\n  let dropped = false\n\n  // if the dropzone has no rect (eg. display: none)\n  // call the custom dropChecker or just return false\n  if (!(rect = rect || interactable.getRect(dropElement))) {\n    return interactable.options.drop.checker\n      ? interactable.options.drop.checker(\n          dragEvent,\n          event,\n          dropped,\n          interactable,\n          dropElement,\n          draggable,\n          draggableElement,\n        )\n      : false\n  }\n\n  const dropOverlap = interactable.options.drop.overlap\n\n  if (dropOverlap === 'pointer') {\n    const origin = getOriginXY(draggable, draggableElement, 'drag')\n    const page = pointerUtils.getPageXY(dragEvent)\n\n    page.x += origin.x\n    page.y += origin.y\n\n    const horizontal = page.x > rect.left && page.x < rect.right\n    const vertical = page.y > rect.top && page.y < rect.bottom\n\n    dropped = horizontal && vertical\n  }\n\n  const dragRect = draggable.getRect(draggableElement)\n\n  if (dragRect && dropOverlap === 'center') {\n    const cx = dragRect.left + dragRect.width / 2\n    const cy = dragRect.top + dragRect.height / 2\n\n    dropped = cx >= rect.left && cx <= rect.right && cy >= rect.top && cy <= rect.bottom\n  }\n\n  if (dragRect && is.number(dropOverlap)) {\n    const overlapArea =\n      Math.max(0, Math.min(rect.right, dragRect.right) - Math.max(rect.left, dragRect.left)) *\n      Math.max(0, Math.min(rect.bottom, dragRect.bottom) - Math.max(rect.top, dragRect.top))\n\n    const overlapRatio = overlapArea / (dragRect.width * dragRect.height)\n\n    dropped = overlapRatio >= dropOverlap\n  }\n\n  if (interactable.options.drop.checker) {\n    dropped = interactable.options.drop.checker(\n      dragEvent,\n      event,\n      dropped,\n      interactable,\n      dropElement,\n      draggable,\n      draggableElement,\n    )\n  }\n\n  return dropped\n}\n\nconst drop: Plugin = {\n  id: 'actions/drop',\n  install,\n  listeners: {\n    'interactions:before-action-start': ({ interaction }) => {\n      if (interaction.prepared.name !== 'drag') {\n        return\n      }\n\n      interaction.dropState = {\n        cur: {\n          dropzone: null,\n          element: null,\n        },\n        prev: {\n          dropzone: null,\n          element: null,\n        },\n        rejected: null,\n        events: null,\n        activeDrops: [],\n      }\n    },\n\n    'interactions:after-action-start': (\n      { interaction, event, iEvent: dragEvent }: DoPhaseArg<'drag', EventPhase>,\n      scope,\n    ) => {\n      if (interaction.prepared.name !== 'drag') {\n        return\n      }\n\n      const dropState = interaction.dropState!\n\n      // reset active dropzones\n      dropState.activeDrops = []\n      dropState.events = {}\n      dropState.activeDrops = getActiveDrops(scope, interaction.element!)\n      dropState.events = getDropEvents(interaction, event, dragEvent)\n\n      if (dropState.events.activate) {\n        fireActivationEvents(dropState.activeDrops, dropState.events.activate)\n        scope.fire('actions/drop:start', { interaction, dragEvent })\n      }\n    },\n\n    'interactions:action-move': onEventCreated,\n\n    'interactions:after-action-move': (\n      { interaction, iEvent: dragEvent }: DoPhaseArg<'drag', EventPhase>,\n      scope,\n    ) => {\n      if (interaction.prepared.name !== 'drag') {\n        return\n      }\n\n      const dropState = interaction.dropState!\n      fireDropEvents(interaction, dropState.events)\n\n      scope.fire('actions/drop:move', { interaction, dragEvent })\n      dropState.events = {}\n    },\n\n    'interactions:action-end': (arg: DoPhaseArg<'drag', EventPhase>, scope) => {\n      if (arg.interaction.prepared.name !== 'drag') {\n        return\n      }\n\n      const { interaction, iEvent: dragEvent } = arg\n\n      onEventCreated(arg, scope)\n      fireDropEvents(interaction, interaction.dropState!.events)\n      scope.fire('actions/drop:end', { interaction, dragEvent })\n    },\n\n    'interactions:stop': ({ interaction }) => {\n      if (interaction.prepared.name !== 'drag') {\n        return\n      }\n\n      const { dropState } = interaction\n\n      if (dropState) {\n        dropState.activeDrops = null as never\n        dropState.events = null as never\n        dropState.cur.dropzone = null as never\n        dropState.cur.element = null as never\n        dropState.prev.dropzone = null as never\n        dropState.prev.element = null as never\n        dropState.rejected = false\n      }\n    },\n  },\n  getActiveDrops,\n  getDrop,\n  getDropEvents,\n  fireDropEvents,\n\n  filterEventType: (type: string) => type.search('drag') === 0 || type.search('drop') === 0,\n\n  defaults: {\n    enabled: false,\n    accept: null as never,\n    overlap: 'pointer',\n  } as DropzoneOptions,\n}\n\nexport default drop\n", "import type { Interactable } from '@interactjs/core/Interactable'\nimport type { InteractEvent, EventPhase } from '@interactjs/core/InteractEvent'\nimport type { Interaction, DoPhaseArg } from '@interactjs/core/Interaction'\nimport type { PerActionDefaults } from '@interactjs/core/options'\nimport type { Scope, Plugin } from '@interactjs/core/scope'\nimport type { Rect, PointerType, ListenersArg, OrBoolean } from '@interactjs/core/types'\nimport is from '@interactjs/utils/is'\nimport * as pointerUtils from '@interactjs/utils/pointerUtils'\n\ndeclare module '@interactjs/core/Interaction' {\n  interface Interaction {\n    gesture?: {\n      angle: number // angle from first to second touch\n      distance: number\n      scale: number // gesture.distance / gesture.startDistance\n      startAngle: number // angle of line joining two touches\n      startDistance: number // distance between two touches of touchStart\n    }\n  }\n}\n\ndeclare module '@interactjs/core/Interactable' {\n  interface Interactable {\n    gesturable(options: Partial<OrBoolean<GesturableOptions>> | boolean): this\n    gesturable(): GesturableOptions\n    /**\n     * ```js\n     * interact(element).gesturable({\n     *     onstart: function (event) {},\n     *     onmove : function (event) {},\n     *     onend  : function (event) {},\n     *\n     *     // limit multiple gestures.\n     *     // See the explanation in {@link Interactable.draggable} example\n     *     max: Infinity,\n     *     maxPerElement: 1,\n     * })\n     *\n     * var isGestureable = interact(element).gesturable()\n     * ```\n     *\n     * Gets or sets whether multitouch gestures can be performed on the target\n     *\n     * @param options - true/false or An object with event listeners to be fired on gesture events (makes the Interactable gesturable)\n     * @returns A boolean indicating if this can be the target of gesture events, or this Interactable\n     */\n    gesturable(options?: Partial<OrBoolean<GesturableOptions>> | boolean): this | GesturableOptions\n  }\n}\n\ndeclare module '@interactjs/core/options' {\n  interface ActionDefaults {\n    gesture: GesturableOptions\n  }\n}\n\ndeclare module '@interactjs/core/types' {\n  interface ActionMap {\n    gesture?: typeof gesture\n  }\n}\n\nexport interface GesturableOptions extends PerActionDefaults {\n  onstart?: ListenersArg\n  onmove?: ListenersArg\n  onend?: ListenersArg\n}\n\nexport interface GestureEvent extends InteractEvent<'gesture'> {\n  distance: number\n  angle: number\n  da: number // angle change\n  scale: number // ratio of distance start to current event\n  ds: number // scale change\n  box: Rect // enclosing box of all points\n  touches: PointerType[]\n}\n\nexport interface GestureSignalArg extends DoPhaseArg<'gesture', EventPhase> {\n  iEvent: GestureEvent\n  interaction: Interaction<'gesture'>\n}\n\nfunction install(scope: Scope) {\n  const { actions, Interactable, defaults } = scope\n\n  Interactable.prototype.gesturable = function (\n    this: InstanceType<typeof Interactable>,\n    options: GesturableOptions | boolean,\n  ) {\n    if (is.object(options)) {\n      this.options.gesture.enabled = options.enabled !== false\n      this.setPerAction('gesture', options)\n      this.setOnEvents('gesture', options)\n\n      return this\n    }\n\n    if (is.bool(options)) {\n      this.options.gesture.enabled = options\n\n      return this\n    }\n\n    return this.options.gesture as GesturableOptions\n  } as Interactable['gesturable']\n\n  actions.map.gesture = gesture\n  actions.methodDict.gesture = 'gesturable'\n\n  defaults.actions.gesture = gesture.defaults\n}\n\nfunction updateGestureProps({ interaction, iEvent, phase }: GestureSignalArg) {\n  if (interaction.prepared.name !== 'gesture') return\n\n  const pointers = interaction.pointers.map((p) => p.pointer)\n  const starting = phase === 'start'\n  const ending = phase === 'end'\n  const deltaSource = interaction.interactable.options.deltaSource\n\n  iEvent.touches = [pointers[0], pointers[1]]\n\n  if (starting) {\n    iEvent.distance = pointerUtils.touchDistance(pointers, deltaSource)\n    iEvent.box = pointerUtils.touchBBox(pointers)\n    iEvent.scale = 1\n    iEvent.ds = 0\n    iEvent.angle = pointerUtils.touchAngle(pointers, deltaSource)\n    iEvent.da = 0\n\n    interaction.gesture.startDistance = iEvent.distance\n    interaction.gesture.startAngle = iEvent.angle\n  } else if (ending || interaction.pointers.length < 2) {\n    const prevEvent = interaction.prevEvent as GestureEvent\n\n    iEvent.distance = prevEvent.distance\n    iEvent.box = prevEvent.box\n    iEvent.scale = prevEvent.scale\n    iEvent.ds = 0\n    iEvent.angle = prevEvent.angle\n    iEvent.da = 0\n  } else {\n    iEvent.distance = pointerUtils.touchDistance(pointers, deltaSource)\n    iEvent.box = pointerUtils.touchBBox(pointers)\n    iEvent.scale = iEvent.distance / interaction.gesture.startDistance\n    iEvent.angle = pointerUtils.touchAngle(pointers, deltaSource)\n\n    iEvent.ds = iEvent.scale - interaction.gesture.scale\n    iEvent.da = iEvent.angle - interaction.gesture.angle\n  }\n\n  interaction.gesture.distance = iEvent.distance\n  interaction.gesture.angle = iEvent.angle\n\n  if (is.number(iEvent.scale) && iEvent.scale !== Infinity && !isNaN(iEvent.scale)) {\n    interaction.gesture.scale = iEvent.scale\n  }\n}\n\nconst gesture: Plugin = {\n  id: 'actions/gesture',\n  before: ['actions/drag', 'actions/resize'],\n  install,\n  listeners: {\n    'interactions:action-start': updateGestureProps,\n    'interactions:action-move': updateGestureProps,\n    'interactions:action-end': updateGestureProps,\n\n    'interactions:new': ({ interaction }) => {\n      interaction.gesture = {\n        angle: 0,\n        distance: 0,\n        scale: 1,\n        startAngle: 0,\n        startDistance: 0,\n      }\n    },\n\n    'auto-start:check': (arg) => {\n      if (arg.interaction.pointers.length < 2) {\n        return undefined\n      }\n\n      const gestureOptions = arg.interactable.options.gesture\n\n      if (!(gestureOptions && gestureOptions.enabled)) {\n        return undefined\n      }\n\n      arg.action = { name: 'gesture' }\n\n      return false\n    },\n  },\n\n  defaults: {},\n\n  getCursor() {\n    return ''\n  },\n\n  filterEventType: (type: string) => type.search('gesture') === 0,\n}\n\nexport default gesture\n", "import type { Interactable } from '@interactjs/core/Interactable'\nimport type { EventPhase, InteractEvent } from '@interactjs/core/InteractEvent'\nimport type { Interaction } from '@interactjs/core/Interaction'\nimport type { PerActionDefaults } from '@interactjs/core/options'\nimport type { <PERSON><PERSON>, Plugin } from '@interactjs/core/scope'\nimport type {\n  ActionName,\n  ActionProps,\n  EdgeOptions,\n  FullRect,\n  ListenersArg,\n  OrBoolean,\n  Point,\n  Rect,\n} from '@interactjs/core/types'\nimport * as dom from '@interactjs/utils/domUtils'\nimport extend from '@interactjs/utils/extend'\nimport is from '@interactjs/utils/is'\n\nexport type EdgeName = 'top' | 'left' | 'bottom' | 'right'\n\ndeclare module '@interactjs/core/Interactable' {\n  interface Interactable {\n    resizable(): ResizableOptions\n    resizable(options: Partial<OrBoolean<ResizableOptions>> | boolean): this\n    /**\n     * ```js\n     * interact(element).resizable({\n     *   onstart: function (event) {},\n     *   onmove : function (event) {},\n     *   onend  : function (event) {},\n     *\n     *   edges: {\n     *     top   : true,       // Use pointer coords to check for resize.\n     *     left  : false,      // Disable resizing from left edge.\n     *     bottom: '.resize-s',// Resize if pointer target matches selector\n     *     right : handleEl    // Resize if pointer target is the given Element\n     *   },\n     *\n     *   // Width and height can be adjusted independently. When `true`, width and\n     *   // height are adjusted at a 1:1 ratio.\n     *   square: false,\n     *\n     *   // Width and height can be adjusted independently. When `true`, width and\n     *   // height maintain the aspect ratio they had when resizing started.\n     *   preserveAspectRatio: false,\n     *\n     *   // a value of 'none' will limit the resize rect to a minimum of 0x0\n     *   // 'negate' will allow the rect to have negative width/height\n     *   // 'reposition' will keep the width/height positive by swapping\n     *   // the top and bottom edges and/or swapping the left and right edges\n     *   invert: 'none' || 'negate' || 'reposition'\n     *\n     *   // limit multiple resizes.\n     *   // See the explanation in the {@link Interactable.draggable} example\n     *   max: Infinity,\n     *   maxPerElement: 1,\n     * })\n     *\n     * var isResizeable = interact(element).resizable()\n     * ```\n     *\n     * Gets or sets whether resize actions can be performed on the target\n     *\n     * @param options - true/false or An object with event\n     * listeners to be fired on resize events (object makes the Interactable\n     * resizable)\n     * @returns A boolean indicating if this can be the\n     * target of resize elements, or this Interactable\n     */\n    resizable(options?: Partial<OrBoolean<ResizableOptions>> | boolean): this | ResizableOptions\n  }\n}\n\ndeclare module '@interactjs/core/Interaction' {\n  interface Interaction<T extends ActionName | null = ActionName> {\n    resizeAxes: 'x' | 'y' | 'xy'\n    styleCursor(newValue: boolean): this\n    styleCursor(): boolean\n    resizeStartAspectRatio: number\n  }\n}\n\ndeclare module '@interactjs/core/options' {\n  interface ActionDefaults {\n    resize: ResizableOptions\n  }\n}\n\ndeclare module '@interactjs/core/types' {\n  interface ActionMap {\n    resize?: typeof resize\n  }\n}\n\nexport interface ResizableOptions extends PerActionDefaults {\n  square?: boolean\n  preserveAspectRatio?: boolean\n  edges?: EdgeOptions | null\n  axis?: 'x' | 'y' | 'xy' // deprecated\n  invert?: 'none' | 'negate' | 'reposition'\n  margin?: number\n  squareResize?: boolean\n  oninertiastart?: ListenersArg\n  onstart?: ListenersArg\n  onmove?: ListenersArg\n  onend?: ListenersArg\n}\n\nexport interface ResizeEvent<P extends EventPhase = EventPhase> extends InteractEvent<'resize', P> {\n  deltaRect?: FullRect\n  edges?: ActionProps['edges']\n}\n\nfunction install(scope: Scope) {\n  const {\n    actions,\n    browser,\n    Interactable, // tslint:disable-line no-shadowed-variable\n    defaults,\n  } = scope\n\n  // Less Precision with touch input\n\n  resize.cursors = initCursors(browser)\n  resize.defaultMargin = browser.supportsTouch || browser.supportsPointerEvent ? 20 : 10\n\n  Interactable.prototype.resizable = function (this: Interactable, options: ResizableOptions | boolean) {\n    return resizable(this, options, scope)\n  } as Interactable['resizable']\n\n  actions.map.resize = resize\n  actions.methodDict.resize = 'resizable'\n\n  defaults.actions.resize = resize.defaults\n}\n\nfunction resizeChecker(arg) {\n  const { interaction, interactable, element, rect, buttons } = arg\n\n  if (!rect) {\n    return undefined\n  }\n\n  const page = extend({}, interaction.coords.cur.page)\n  const resizeOptions = interactable.options.resize\n\n  if (\n    !(resizeOptions && resizeOptions.enabled) ||\n    // check mouseButton setting if the pointer is down\n    (interaction.pointerIsDown &&\n      /mouse|pointer/.test(interaction.pointerType) &&\n      (buttons & resizeOptions.mouseButtons) === 0)\n  ) {\n    return undefined\n  }\n\n  // if using resize.edges\n  if (is.object(resizeOptions.edges)) {\n    const resizeEdges = {\n      left: false,\n      right: false,\n      top: false,\n      bottom: false,\n    }\n\n    for (const edge in resizeEdges) {\n      resizeEdges[edge] = checkResizeEdge(\n        edge,\n        resizeOptions.edges[edge],\n        page,\n        interaction._latestPointer.eventTarget,\n        element,\n        rect,\n        resizeOptions.margin || resize.defaultMargin,\n      )\n    }\n\n    resizeEdges.left = resizeEdges.left && !resizeEdges.right\n    resizeEdges.top = resizeEdges.top && !resizeEdges.bottom\n\n    if (resizeEdges.left || resizeEdges.right || resizeEdges.top || resizeEdges.bottom) {\n      arg.action = {\n        name: 'resize',\n        edges: resizeEdges,\n      }\n    }\n  } else {\n    const right = resizeOptions.axis !== 'y' && page.x > rect.right - resize.defaultMargin\n    const bottom = resizeOptions.axis !== 'x' && page.y > rect.bottom - resize.defaultMargin\n\n    if (right || bottom) {\n      arg.action = {\n        name: 'resize',\n        axes: (right ? 'x' : '') + (bottom ? 'y' : ''),\n      }\n    }\n  }\n\n  return arg.action ? false : undefined\n}\n\nfunction resizable(interactable: Interactable, options: OrBoolean<ResizableOptions> | boolean, scope: Scope) {\n  if (is.object(options)) {\n    interactable.options.resize.enabled = options.enabled !== false\n    interactable.setPerAction('resize', options)\n    interactable.setOnEvents('resize', options)\n\n    if (is.string(options.axis) && /^x$|^y$|^xy$/.test(options.axis)) {\n      interactable.options.resize.axis = options.axis\n    } else if (options.axis === null) {\n      interactable.options.resize.axis = scope.defaults.actions.resize.axis\n    }\n\n    if (is.bool(options.preserveAspectRatio)) {\n      interactable.options.resize.preserveAspectRatio = options.preserveAspectRatio\n    } else if (is.bool(options.square)) {\n      interactable.options.resize.square = options.square\n    }\n\n    return interactable\n  }\n  if (is.bool(options)) {\n    interactable.options.resize.enabled = options\n\n    return interactable\n  }\n  return interactable.options.resize\n}\n\nfunction checkResizeEdge(\n  name: string,\n  value: any,\n  page: Point,\n  element: Node,\n  interactableElement: Element,\n  rect: Rect,\n  margin: number,\n) {\n  // false, '', undefined, null\n  if (!value) {\n    return false\n  }\n\n  // true value, use pointer coords and element rect\n  if (value === true) {\n    // if dimensions are negative, \"switch\" edges\n    const width = is.number(rect.width) ? rect.width : rect.right - rect.left\n    const height = is.number(rect.height) ? rect.height : rect.bottom - rect.top\n\n    // don't use margin greater than half the relevent dimension\n    margin = Math.min(margin, Math.abs((name === 'left' || name === 'right' ? width : height) / 2))\n\n    if (width < 0) {\n      if (name === 'left') {\n        name = 'right'\n      } else if (name === 'right') {\n        name = 'left'\n      }\n    }\n    if (height < 0) {\n      if (name === 'top') {\n        name = 'bottom'\n      } else if (name === 'bottom') {\n        name = 'top'\n      }\n    }\n\n    if (name === 'left') {\n      const edge = width >= 0 ? rect.left : rect.right\n      return page.x < edge + margin\n    }\n    if (name === 'top') {\n      const edge = height >= 0 ? rect.top : rect.bottom\n      return page.y < edge + margin\n    }\n\n    if (name === 'right') {\n      return page.x > (width >= 0 ? rect.right : rect.left) - margin\n    }\n    if (name === 'bottom') {\n      return page.y > (height >= 0 ? rect.bottom : rect.top) - margin\n    }\n  }\n\n  // the remaining checks require an element\n  if (!is.element(element)) {\n    return false\n  }\n\n  return is.element(value)\n    ? // the value is an element to use as a resize handle\n      value === element\n    : // otherwise check if element matches value as selector\n      dom.matchesUpTo(element, value, interactableElement)\n}\n\n/* eslint-disable multiline-ternary */\n// eslint-disable-next-line @typescript-eslint/consistent-type-imports\nfunction initCursors(browser: typeof import('@interactjs/utils/browser').default) {\n  return browser.isIe9\n    ? {\n        x: 'e-resize',\n        y: 's-resize',\n        xy: 'se-resize',\n\n        top: 'n-resize',\n        left: 'w-resize',\n        bottom: 's-resize',\n        right: 'e-resize',\n        topleft: 'se-resize',\n        bottomright: 'se-resize',\n        topright: 'ne-resize',\n        bottomleft: 'ne-resize',\n      }\n    : {\n        x: 'ew-resize',\n        y: 'ns-resize',\n        xy: 'nwse-resize',\n\n        top: 'ns-resize',\n        left: 'ew-resize',\n        bottom: 'ns-resize',\n        right: 'ew-resize',\n        topleft: 'nwse-resize',\n        bottomright: 'nwse-resize',\n        topright: 'nesw-resize',\n        bottomleft: 'nesw-resize',\n      }\n}\n/* eslint-enable multiline-ternary */\n\nfunction start({ iEvent, interaction }: { iEvent: InteractEvent<any, any>; interaction: Interaction }) {\n  if (interaction.prepared.name !== 'resize' || !interaction.prepared.edges) {\n    return\n  }\n\n  const resizeEvent = iEvent as ResizeEvent\n  const rect = interaction.rect\n\n  interaction._rects = {\n    start: extend({}, rect),\n    corrected: extend({}, rect),\n    previous: extend({}, rect),\n    delta: {\n      left: 0,\n      right: 0,\n      width: 0,\n      top: 0,\n      bottom: 0,\n      height: 0,\n    },\n  }\n\n  resizeEvent.edges = interaction.prepared.edges\n  resizeEvent.rect = interaction._rects.corrected\n  resizeEvent.deltaRect = interaction._rects.delta\n}\n\nfunction move({ iEvent, interaction }: { iEvent: InteractEvent<any, any>; interaction: Interaction }) {\n  if (interaction.prepared.name !== 'resize' || !interaction.prepared.edges) return\n\n  const resizeEvent = iEvent as ResizeEvent\n  const resizeOptions = interaction.interactable.options.resize\n  const invert = resizeOptions.invert\n  const invertible = invert === 'reposition' || invert === 'negate'\n\n  const current = interaction.rect\n  const { start: startRect, corrected, delta: deltaRect, previous } = interaction._rects\n\n  extend(previous, corrected)\n\n  if (invertible) {\n    // if invertible, copy the current rect\n    extend(corrected, current)\n\n    if (invert === 'reposition') {\n      // swap edge values if necessary to keep width/height positive\n      if (corrected.top > corrected.bottom) {\n        const swap = corrected.top\n\n        corrected.top = corrected.bottom\n        corrected.bottom = swap\n      }\n      if (corrected.left > corrected.right) {\n        const swap = corrected.left\n\n        corrected.left = corrected.right\n        corrected.right = swap\n      }\n    }\n  } else {\n    // if not invertible, restrict to minimum of 0x0 rect\n    corrected.top = Math.min(current.top, startRect.bottom)\n    corrected.bottom = Math.max(current.bottom, startRect.top)\n    corrected.left = Math.min(current.left, startRect.right)\n    corrected.right = Math.max(current.right, startRect.left)\n  }\n\n  corrected.width = corrected.right - corrected.left\n  corrected.height = corrected.bottom - corrected.top\n\n  for (const edge in corrected) {\n    deltaRect[edge] = corrected[edge] - previous[edge]\n  }\n\n  resizeEvent.edges = interaction.prepared.edges\n  resizeEvent.rect = corrected\n  resizeEvent.deltaRect = deltaRect\n}\n\nfunction end({ iEvent, interaction }: { iEvent: InteractEvent<any, any>; interaction: Interaction }) {\n  if (interaction.prepared.name !== 'resize' || !interaction.prepared.edges) return\n\n  const resizeEvent = iEvent as ResizeEvent\n\n  resizeEvent.edges = interaction.prepared.edges\n  resizeEvent.rect = interaction._rects.corrected\n  resizeEvent.deltaRect = interaction._rects.delta\n}\n\nfunction updateEventAxes({\n  iEvent,\n  interaction,\n}: {\n  iEvent: InteractEvent<any, any>\n  interaction: Interaction\n}) {\n  if (interaction.prepared.name !== 'resize' || !interaction.resizeAxes) return\n\n  const options = interaction.interactable.options\n  const resizeEvent = iEvent as ResizeEvent\n\n  if (options.resize.square) {\n    if (interaction.resizeAxes === 'y') {\n      resizeEvent.delta.x = resizeEvent.delta.y\n    } else {\n      resizeEvent.delta.y = resizeEvent.delta.x\n    }\n    resizeEvent.axes = 'xy'\n  } else {\n    resizeEvent.axes = interaction.resizeAxes\n\n    if (interaction.resizeAxes === 'x') {\n      resizeEvent.delta.y = 0\n    } else if (interaction.resizeAxes === 'y') {\n      resizeEvent.delta.x = 0\n    }\n  }\n}\n\nconst resize: Plugin = {\n  id: 'actions/resize',\n  before: ['actions/drag'],\n  install,\n  listeners: {\n    'interactions:new': ({ interaction }) => {\n      interaction.resizeAxes = 'xy'\n    },\n\n    'interactions:action-start': (arg) => {\n      start(arg)\n      updateEventAxes(arg)\n    },\n    'interactions:action-move': (arg) => {\n      move(arg)\n      updateEventAxes(arg)\n    },\n    'interactions:action-end': end,\n    'auto-start:check': resizeChecker,\n  },\n\n  defaults: {\n    square: false,\n    preserveAspectRatio: false,\n    axis: 'xy',\n\n    // use default margin\n    margin: NaN,\n\n    // object with props left, right, top, bottom which are\n    // true/false values to resize when the pointer is over that edge,\n    // CSS selectors to match the handles for each direction\n    // or the Elements for each handle\n    edges: null,\n\n    // a value of 'none' will limit the resize rect to a minimum of 0x0\n    // 'negate' will alow the rect to have negative width/height\n    // 'reposition' will keep the width/height positive by swapping\n    // the top and bottom edges and/or swapping the left and right edges\n    invert: 'none',\n  } as ResizableOptions,\n\n  cursors: null as ReturnType<typeof initCursors>,\n\n  getCursor({ edges, axis, name }: ActionProps) {\n    const cursors = resize.cursors\n    let result: string = null\n\n    if (axis) {\n      result = cursors[name + axis]\n    } else if (edges) {\n      let cursorKey = ''\n\n      for (const edge of ['top', 'bottom', 'left', 'right']) {\n        if (edges[edge]) {\n          cursorKey += edge\n        }\n      }\n\n      result = cursors[cursorKey]\n    }\n\n    return result\n  },\n\n  filterEventType: (type: string) => type.search('resize') === 0,\n\n  defaultMargin: null as number,\n}\n\nexport default resize\n", "let lastTime = 0\nlet request: typeof requestAnimationFrame\nlet cancel: typeof cancelAnimationFrame\n\nfunction init(global: Window | typeof globalThis) {\n  request = global.requestAnimationFrame\n  cancel = global.cancelAnimationFrame\n\n  if (!request) {\n    const vendors = ['ms', 'moz', 'webkit', 'o']\n\n    for (const vendor of vendors) {\n      request = global[`${vendor}RequestAnimationFrame` as 'requestAnimationFrame']\n      cancel =\n        global[`${vendor}CancelAnimationFrame` as 'cancelAnimationFrame'] ||\n        global[`${vendor}CancelRequestAnimationFrame` as 'cancelAnimationFrame']\n    }\n  }\n\n  request = request && request.bind(global)\n  cancel = cancel && cancel.bind(global)\n\n  if (!request) {\n    request = (callback) => {\n      const currTime = Date.now()\n      const timeToCall = Math.max(0, 16 - (currTime - lastTime))\n      const token = global.setTimeout(() => {\n        // eslint-disable-next-line n/no-callback-literal\n        callback(currTime + timeToCall)\n      }, timeToCall)\n\n      lastTime = currTime + timeToCall\n      return token as any\n    }\n\n    cancel = (token) => clearTimeout(token)\n  }\n}\n\nexport default {\n  request: (callback: FrameRequestCallback) => request(callback),\n  cancel: (token: number) => cancel(token),\n  init,\n}\n", "import type { Scope } from '@interactjs/core/scope'\n\n/* eslint-disable import/no-duplicates -- for typescript module augmentations */\nimport './drag/plugin'\nimport './drop/plugin'\nimport './gesture/plugin'\nimport './resize/plugin'\n\nimport drag from './drag/plugin'\nimport drop from './drop/plugin'\nimport gesture from './gesture/plugin'\nimport resize from './resize/plugin'\n/* eslint-enable import/no-duplicates */\n\nexport default {\n  id: 'actions',\n  install(scope: Scope) {\n    scope.usePlugin(gesture)\n    scope.usePlugin(resize)\n    scope.usePlugin(drag)\n    scope.usePlugin(drop)\n  },\n}\n", "import type { Interactable } from '@interactjs/core/Interactable'\nimport type Interaction from '@interactjs/core/Interaction'\nimport type { Scope, Plugin } from '@interactjs/core/scope'\nimport type { ActionName, PointerType } from '@interactjs/core/types'\nimport * as domUtils from '@interactjs/utils/domUtils'\nimport is from '@interactjs/utils/is'\nimport raf from '@interactjs/utils/raf'\nimport { getStringOptionResult } from '@interactjs/utils/rect'\nimport { getWindow } from '@interactjs/utils/window'\n\ndeclare module '@interactjs/core/scope' {\n  interface Scope {\n    autoScroll: typeof autoScroll\n  }\n}\n\ndeclare module '@interactjs/core/Interaction' {\n  interface Interaction {\n    autoScroll?: typeof autoScroll\n  }\n}\n\ndeclare module '@interactjs/core/options' {\n  interface PerActionDefaults {\n    autoScroll?: AutoScrollOptions\n  }\n}\n\nexport interface AutoScrollOptions {\n  container?: Window | HTMLElement | string\n  margin?: number\n  distance?: number\n  interval?: number\n  speed?: number\n  enabled?: boolean\n}\n\nfunction install(scope: Scope) {\n  const { defaults, actions } = scope\n\n  scope.autoScroll = autoScroll\n  autoScroll.now = () => scope.now()\n\n  actions.phaselessTypes.autoscroll = true\n  defaults.perAction.autoScroll = autoScroll.defaults\n}\n\nconst autoScroll = {\n  defaults: {\n    enabled: false,\n    margin: 60,\n\n    // the item that is scrolled (Window or HTMLElement)\n    container: null as AutoScrollOptions['container'],\n\n    // the scroll speed in pixels per second\n    speed: 300,\n  } as AutoScrollOptions,\n\n  now: Date.now,\n\n  interaction: null as Interaction<ActionName> | null,\n  i: 0, // the handle returned by window.setInterval\n\n  // Direction each pulse is to scroll in\n  x: 0,\n  y: 0,\n\n  isScrolling: false,\n  prevTime: 0,\n  margin: 0,\n  speed: 0,\n\n  start(interaction: Interaction) {\n    autoScroll.isScrolling = true\n    raf.cancel(autoScroll.i)\n\n    interaction.autoScroll = autoScroll\n    autoScroll.interaction = interaction\n    autoScroll.prevTime = autoScroll.now()\n    autoScroll.i = raf.request(autoScroll.scroll)\n  },\n\n  stop() {\n    autoScroll.isScrolling = false\n    if (autoScroll.interaction) {\n      autoScroll.interaction.autoScroll = null\n    }\n    raf.cancel(autoScroll.i)\n  },\n\n  // scroll the window by the values in scroll.x/y\n  scroll() {\n    const { interaction } = autoScroll\n    const { interactable, element } = interaction\n    const actionName = interaction.prepared.name\n    const options = interactable.options[actionName].autoScroll\n    const container = getContainer(options.container, interactable, element)\n    const now = autoScroll.now()\n    // change in time in seconds\n    const dt = (now - autoScroll.prevTime) / 1000\n    // displacement\n    const s = options.speed * dt\n\n    if (s >= 1) {\n      const scrollBy = {\n        x: autoScroll.x * s,\n        y: autoScroll.y * s,\n      }\n\n      if (scrollBy.x || scrollBy.y) {\n        const prevScroll = getScroll(container)\n\n        if (is.window(container)) {\n          container.scrollBy(scrollBy.x, scrollBy.y)\n        } else if (container) {\n          container.scrollLeft += scrollBy.x\n          container.scrollTop += scrollBy.y\n        }\n\n        const curScroll = getScroll(container)\n        const delta = {\n          x: curScroll.x - prevScroll.x,\n          y: curScroll.y - prevScroll.y,\n        }\n\n        if (delta.x || delta.y) {\n          interactable.fire({\n            type: 'autoscroll',\n            target: element,\n            interactable,\n            delta,\n            interaction,\n            container,\n          })\n        }\n      }\n\n      autoScroll.prevTime = now\n    }\n\n    if (autoScroll.isScrolling) {\n      raf.cancel(autoScroll.i)\n      autoScroll.i = raf.request(autoScroll.scroll)\n    }\n  },\n  check(interactable: Interactable, actionName: ActionName) {\n    const options = interactable.options\n\n    return options[actionName].autoScroll?.enabled\n  },\n  onInteractionMove<T extends ActionName>({\n    interaction,\n    pointer,\n  }: {\n    interaction: Interaction<T>\n    pointer: PointerType\n  }) {\n    if (\n      !(interaction.interacting() && autoScroll.check(interaction.interactable, interaction.prepared.name))\n    ) {\n      return\n    }\n\n    if (interaction.simulation) {\n      autoScroll.x = autoScroll.y = 0\n      return\n    }\n\n    let top: boolean\n    let right: boolean\n    let bottom: boolean\n    let left: boolean\n\n    const { interactable, element } = interaction\n    const actionName = interaction.prepared.name\n    const options = interactable.options[actionName].autoScroll\n    const container = getContainer(options.container, interactable, element)\n\n    if (is.window(container)) {\n      left = pointer.clientX < autoScroll.margin\n      top = pointer.clientY < autoScroll.margin\n      right = pointer.clientX > container.innerWidth - autoScroll.margin\n      bottom = pointer.clientY > container.innerHeight - autoScroll.margin\n    } else {\n      const rect = domUtils.getElementClientRect(container)\n\n      left = pointer.clientX < rect.left + autoScroll.margin\n      top = pointer.clientY < rect.top + autoScroll.margin\n      right = pointer.clientX > rect.right - autoScroll.margin\n      bottom = pointer.clientY > rect.bottom - autoScroll.margin\n    }\n\n    autoScroll.x = right ? 1 : left ? -1 : 0\n    autoScroll.y = bottom ? 1 : top ? -1 : 0\n\n    if (!autoScroll.isScrolling) {\n      // set the autoScroll properties to those of the target\n      autoScroll.margin = options.margin\n      autoScroll.speed = options.speed\n\n      autoScroll.start(interaction)\n    }\n  },\n}\n\nexport function getContainer(value: any, interactable: Interactable, element: Element) {\n  return (\n    (is.string(value) ? getStringOptionResult(value, interactable, element) : value) || getWindow(element)\n  )\n}\n\nexport function getScroll(container: any) {\n  if (is.window(container)) {\n    container = window.document.body\n  }\n\n  return { x: container.scrollLeft, y: container.scrollTop }\n}\n\nexport function getScrollSize(container: any) {\n  if (is.window(container)) {\n    container = window.document.body\n  }\n\n  return { x: container.scrollWidth, y: container.scrollHeight }\n}\n\nexport function getScrollSizeDelta<T extends ActionName>(\n  {\n    interaction,\n    element,\n  }: {\n    interaction: Partial<Interaction<T>>\n    element: Element\n  },\n  func: any,\n) {\n  const scrollOptions = interaction && interaction.interactable.options[interaction.prepared.name].autoScroll\n\n  if (!scrollOptions || !scrollOptions.enabled) {\n    func()\n    return { x: 0, y: 0 }\n  }\n\n  const scrollContainer = getContainer(scrollOptions.container, interaction.interactable, element)\n\n  const prevSize = getScroll(scrollContainer)\n  func()\n  const curSize = getScroll(scrollContainer)\n\n  return {\n    x: curSize.x - prevSize.x,\n    y: curSize.y - prevSize.y,\n  }\n}\n\nconst autoScrollPlugin: Plugin = {\n  id: 'auto-scroll',\n  install,\n  listeners: {\n    'interactions:new': ({ interaction }) => {\n      interaction.autoScroll = null\n    },\n\n    'interactions:destroy': ({ interaction }) => {\n      interaction.autoScroll = null\n      autoScroll.stop()\n      if (autoScroll.interaction) {\n        autoScroll.interaction = null\n      }\n    },\n\n    'interactions:stop': autoScroll.stop,\n\n    'interactions:action-move': (arg: any) => autoScroll.onInteractionMove(arg),\n  },\n}\n\nexport default autoScrollPlugin\n", "import type { ActionName, ActionProps } from '@interactjs/core/types'\n\nimport { window } from './window'\n\nexport function warnOnce<T>(this: T, method: (...args: any[]) => any, message: string) {\n  let warned = false\n\n  return function (this: T) {\n    if (!warned) {\n      ;(window as any).console.warn(message)\n      warned = true\n    }\n\n    return method.apply(this, arguments)\n  }\n}\n\nexport function copyAction<T extends ActionName>(dest: ActionProps<any>, src: ActionProps<T>) {\n  dest.name = src.name\n  dest.axis = src.axis\n  dest.edges = src.edges\n\n  return dest\n}\n\nexport const sign = (n: number) => (n >= 0 ? 1 : -1)\n", "import type { Interactable } from '@interactjs/core/Interactable'\nimport type { Interaction } from '@interactjs/core/Interaction'\nimport type { Scope } from '@interactjs/core/scope'\nimport type { ActionProps, PointerType, PointerEventType, Element } from '@interactjs/core/types'\nimport is from '@interactjs/utils/is'\nimport { warnOnce } from '@interactjs/utils/misc'\n\ndeclare module '@interactjs/core/Interactable' {\n  interface Interactable {\n    getAction: (\n      this: Interactable,\n      pointer: PointerType,\n      event: PointerEventType,\n      interaction: Interaction,\n      element: Element,\n    ) => ActionProps | null\n    styleCursor(newValue: boolean): this\n    styleCursor(): boolean\n    /**\n     * Returns or sets whether the the cursor should be changed depending on the\n     * action that would be performed if the mouse were pressed and dragged.\n     *\n     * @param {boolean} [newValue]\n     * @return {boolean | Interactable} The current setting or this Interactable\n     */\n    styleCursor(newValue?: boolean): boolean | this\n    actionChecker(checker: Function): Interactable\n    actionChecker(): Function\n    /**\n     * ```js\n     * interact('.resize-drag')\n     *   .resizable(true)\n     *   .draggable(true)\n     *   .actionChecker(function (pointer, event, action, interactable, element, interaction) {\n     *\n     *     if (interact.matchesSelector(event.target, '.drag-handle')) {\n     *       // force drag with handle target\n     *       action.name = drag\n     *     }\n     *     else {\n     *       // resize from the top and right edges\n     *       action.name  = 'resize'\n     *       action.edges = { top: true, right: true }\n     *     }\n     *\n     *     return action\n     * })\n     * ```\n     *\n     * Returns or sets the function used to check action to be performed on\n     * pointerDown\n     *\n     * @param checker - A function which takes a pointer event,\n     * defaultAction string, interactable, element and interaction as parameters\n     * and returns an object with name property 'drag' 'resize' or 'gesture' and\n     * optionally an `edges` object with boolean 'top', 'left', 'bottom' and right\n     * props.\n     * @returns The checker function or this Interactable\n     */\n    actionChecker(checker?: Function): Interactable | Function\n    /** @returns This interactable */\n    ignoreFrom(newValue: string | Element | null): Interactable\n    /** @returns The current ignoreFrom value */\n    ignoreFrom(): string | Element | null\n    /**\n     * If the target of the `mousedown`, `pointerdown` or `touchstart` event or any\n     * of it's parents match the given CSS selector or Element, no\n     * drag/resize/gesture is started.\n     *\n     * @deprecated\n     * Don't use this method. Instead set the `ignoreFrom` option for each action\n     * or for `pointerEvents`\n     *\n     * ```js\n     * interact(targett)\n     *   .draggable({\n     *     ignoreFrom: 'input, textarea, a[href]'',\n     *   })\n     *   .pointerEvents({\n     *     ignoreFrom: '[no-pointer]',\n     *   })\n     * ```\n     * Interactable\n     */\n    ignoreFrom(\n      /** a CSS selector string, an Element or `null` to not ignore any elements */\n      newValue?: string | Element | null,\n    ): Interactable | string | Element | null\n    allowFrom(): boolean\n    /**\n     *\n     * A drag/resize/gesture is started only If the target of the `mousedown`,\n     * `pointerdown` or `touchstart` event or any of it's parents match the given\n     * CSS selector or Element.\n     *\n     * @deprecated\n     * Don't use this method. Instead set the `allowFrom` option for each action\n     * or for `pointerEvents`\n     *\n     * ```js\n     * interact(targett)\n     *   .resizable({\n     *     allowFrom: '.resize-handle',\n     *   .pointerEvents({\n     *     allowFrom: '.handle',,\n     *   })\n     * ```\n     *\n     * @param {string | Element | null} [newValue]\n     * @return {string | Element | object} The current allowFrom value or this\n     * Interactable\n     */\n    allowFrom(\n      /** A CSS selector string, an Element or `null` to allow from any element */\n      newValue: string | Element | null,\n    ): Interactable\n  }\n}\n\nfunction install(scope: Scope) {\n  const {\n    Interactable, // tslint:disable-line no-shadowed-variable\n  } = scope\n\n  Interactable.prototype.getAction = function getAction(\n    this: Interactable,\n    pointer: PointerType,\n    event: PointerEventType,\n    interaction: Interaction,\n    element: Element,\n  ): ActionProps {\n    const action = defaultActionChecker(this, event, interaction, element, scope)\n\n    if (this.options.actionChecker) {\n      return this.options.actionChecker(pointer, event, action, this, element, interaction)\n    }\n\n    return action\n  }\n\n  Interactable.prototype.ignoreFrom = warnOnce(function (this: Interactable, newValue) {\n    return this._backCompatOption('ignoreFrom', newValue)\n  }, 'Interactable.ignoreFrom() has been deprecated. Use Interactble.draggable({ignoreFrom: newValue}).')\n\n  Interactable.prototype.allowFrom = warnOnce(function (this: Interactable, newValue) {\n    return this._backCompatOption('allowFrom', newValue)\n  }, 'Interactable.allowFrom() has been deprecated. Use Interactble.draggable({allowFrom: newValue}).')\n\n  Interactable.prototype.actionChecker = actionChecker\n\n  Interactable.prototype.styleCursor = styleCursor\n}\n\nfunction defaultActionChecker(\n  interactable: Interactable,\n  event: PointerEventType,\n  interaction: Interaction,\n  element: Element,\n  scope: Scope,\n) {\n  const rect = interactable.getRect(element)\n  const buttons =\n    (event as MouseEvent).buttons ||\n    {\n      0: 1,\n      1: 4,\n      3: 8,\n      4: 16,\n    }[(event as MouseEvent).button as 0 | 1 | 3 | 4]\n  const arg = {\n    action: null,\n    interactable,\n    interaction,\n    element,\n    rect,\n    buttons,\n  }\n\n  scope.fire('auto-start:check', arg)\n\n  return arg.action\n}\n\nfunction styleCursor(this: Interactable, newValue?: boolean) {\n  if (is.bool(newValue)) {\n    this.options.styleCursor = newValue\n\n    return this\n  }\n\n  if (newValue === null) {\n    delete this.options.styleCursor\n\n    return this\n  }\n\n  return this.options.styleCursor\n}\n\nfunction actionChecker(this: Interactable, checker?: any) {\n  if (is.func(checker)) {\n    this.options.actionChecker = checker\n\n    return this\n  }\n\n  if (checker === null) {\n    delete this.options.actionChecker\n\n    return this\n  }\n\n  return this.options.actionChecker\n}\n\nexport default {\n  id: 'auto-start/interactableMethods',\n  install,\n}\n", "import type { Interactable } from '@interactjs/core/Interactable'\nimport type { Interaction } from '@interactjs/core/Interaction'\nimport type { <PERSON><PERSON>, <PERSON><PERSON>rg<PERSON>, Plugin } from '@interactjs/core/scope'\nimport type {\n  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n  PointerType,\n  PointerEventType,\n  Element,\n  ActionName,\n  ActionProps,\n} from '@interactjs/core/types'\nimport * as domUtils from '@interactjs/utils/domUtils'\nimport extend from '@interactjs/utils/extend'\nimport is from '@interactjs/utils/is'\nimport { copyAction } from '@interactjs/utils/misc'\n\n/* eslint-disable import/no-duplicates -- for typescript module augmentations */\nimport './InteractableMethods'\nimport InteractableMethods from './InteractableMethods'\n/* eslint-enable import/no-duplicates */\n\ndeclare module '@interactjs/core/InteractStatic' {\n  export interface InteractStatic {\n    /**\n     * Returns or sets the maximum number of concurrent interactions allowed.  By\n     * default only 1 interaction is allowed at a time (for backwards\n     * compatibility). To allow multiple interactions on the same Interactables and\n     * elements, you need to enable it in the draggable, resizable and gesturable\n     * `'max'` and `'maxPerElement'` options.\n     *\n     * @param {number} [newValue] Any number. newValue <= 0 means no interactions.\n     */\n    maxInteractions: (newValue: any) => any\n  }\n}\n\ndeclare module '@interactjs/core/scope' {\n  interface Scope {\n    autoStart: AutoStart\n  }\n\n  interface SignalArgs {\n    'autoStart:before-start': Omit<SignalArgs['interactions:move'], 'interaction'> & {\n      interaction: Interaction<ActionName>\n    }\n    'autoStart:prepared': { interaction: Interaction }\n    'auto-start:check': CheckSignalArg\n  }\n}\n\ndeclare module '@interactjs/core/options' {\n  interface BaseDefaults {\n    actionChecker?: any\n    cursorChecker?: any\n    styleCursor?: any\n  }\n\n  interface PerActionDefaults {\n    manualStart?: boolean\n    max?: number\n    maxPerElement?: number\n    allowFrom?: string | Element\n    ignoreFrom?: string | Element\n    cursorChecker?: CursorChecker\n\n    // only allow left button by default\n    // see https://developer.mozilla.org/en-US/docs/Web/API/MouseEvent/buttons#Return_value\n    // TODO: docst\n    mouseButtons?: 0 | 1 | 2 | 4 | 8 | 16\n  }\n}\n\ninterface CheckSignalArg {\n  interactable: Interactable\n  interaction: Interaction\n  element: Element\n  action: ActionProps<ActionName>\n  buttons: number\n}\n\nexport interface AutoStart {\n  // Allow this many interactions to happen simultaneously\n  maxInteractions: number\n  withinInteractionLimit: typeof withinInteractionLimit\n  cursorElement: Element\n}\n\nfunction install(scope: Scope) {\n  const { interactStatic: interact, defaults } = scope\n\n  scope.usePlugin(InteractableMethods)\n\n  defaults.base.actionChecker = null\n  defaults.base.styleCursor = true\n\n  extend(defaults.perAction, {\n    manualStart: false,\n    max: Infinity,\n    maxPerElement: 1,\n    allowFrom: null,\n    ignoreFrom: null,\n\n    // only allow left button by default\n    // see https://developer.mozilla.org/en-US/docs/Web/API/MouseEvent/buttons#Return_value\n    mouseButtons: 1,\n  })\n\n  interact.maxInteractions = (newValue: number) => maxInteractions(newValue, scope)\n\n  scope.autoStart = {\n    // Allow this many interactions to happen simultaneously\n    maxInteractions: Infinity,\n    withinInteractionLimit,\n    cursorElement: null,\n  }\n}\n\nfunction prepareOnDown(\n  { interaction, pointer, event, eventTarget }: SignalArgs['interactions:down'],\n  scope: Scope,\n) {\n  if (interaction.interacting()) return\n\n  const actionInfo = getActionInfo(interaction, pointer, event, eventTarget, scope)\n  prepare(interaction, actionInfo, scope)\n}\n\nfunction prepareOnMove(\n  { interaction, pointer, event, eventTarget }: SignalArgs['interactions:move'],\n  scope: Scope,\n) {\n  if (interaction.pointerType !== 'mouse' || interaction.pointerIsDown || interaction.interacting()) return\n\n  const actionInfo = getActionInfo(interaction, pointer, event, eventTarget as Element, scope)\n  prepare(interaction, actionInfo, scope)\n}\n\nfunction startOnMove(arg: SignalArgs['interactions:move'], scope: Scope) {\n  const { interaction } = arg\n\n  if (\n    !interaction.pointerIsDown ||\n    interaction.interacting() ||\n    !interaction.pointerWasMoved ||\n    !interaction.prepared.name\n  ) {\n    return\n  }\n\n  scope.fire('autoStart:before-start', arg)\n\n  const { interactable } = interaction\n  const actionName = (interaction as Interaction<ActionName>).prepared.name\n\n  if (actionName && interactable) {\n    // check manualStart and interaction limit\n    if (\n      interactable.options[actionName].manualStart ||\n      !withinInteractionLimit(interactable, interaction.element, interaction.prepared, scope)\n    ) {\n      interaction.stop()\n    } else {\n      interaction.start(interaction.prepared, interactable, interaction.element)\n      setInteractionCursor(interaction, scope)\n    }\n  }\n}\n\nfunction clearCursorOnStop({ interaction }: { interaction: Interaction }, scope: Scope) {\n  const { interactable } = interaction\n\n  if (interactable && interactable.options.styleCursor) {\n    setCursor(interaction.element, '', scope)\n  }\n}\n\n// Check if the current interactable supports the action.\n// If so, return the validated action. Otherwise, return null\nfunction validateAction<T extends ActionName>(\n  action: ActionProps<T>,\n  interactable: Interactable,\n  element: Element,\n  eventTarget: Node,\n  scope: Scope,\n) {\n  if (\n    interactable.testIgnoreAllow(interactable.options[action.name], element, eventTarget) &&\n    interactable.options[action.name].enabled &&\n    withinInteractionLimit(interactable, element, action, scope)\n  ) {\n    return action\n  }\n\n  return null\n}\n\nfunction validateMatches(\n  interaction: Interaction,\n  pointer: PointerType,\n  event: PointerEventType,\n  matches: Interactable[],\n  matchElements: Element[],\n  eventTarget: Node,\n  scope: Scope,\n) {\n  for (let i = 0, len = matches.length; i < len; i++) {\n    const match = matches[i]\n    const matchElement = matchElements[i]\n    const matchAction = match.getAction(pointer, event, interaction, matchElement)\n\n    if (!matchAction) {\n      continue\n    }\n\n    const action = validateAction<ActionName>(matchAction, match, matchElement, eventTarget, scope)\n\n    if (action) {\n      return {\n        action,\n        interactable: match,\n        element: matchElement,\n      }\n    }\n  }\n\n  return { action: null, interactable: null, element: null }\n}\n\nfunction getActionInfo(\n  interaction: Interaction,\n  pointer: PointerType,\n  event: PointerEventType,\n  eventTarget: Node,\n  scope: Scope,\n) {\n  let matches: Interactable[] = []\n  let matchElements: Element[] = []\n\n  let element = eventTarget as Element\n\n  function pushMatches(interactable: Interactable) {\n    matches.push(interactable)\n    matchElements.push(element)\n  }\n\n  while (is.element(element)) {\n    matches = []\n    matchElements = []\n\n    scope.interactables.forEachMatch(element, pushMatches)\n\n    const actionInfo = validateMatches(\n      interaction,\n      pointer,\n      event,\n      matches,\n      matchElements,\n      eventTarget,\n      scope,\n    )\n\n    if (actionInfo.action && !actionInfo.interactable.options[actionInfo.action.name].manualStart) {\n      return actionInfo\n    }\n\n    element = domUtils.parentNode(element) as Element\n  }\n\n  return { action: null, interactable: null, element: null }\n}\n\nfunction prepare(\n  interaction: Interaction,\n  {\n    action,\n    interactable,\n    element,\n  }: {\n    action: ActionProps<any>\n    interactable: Interactable\n    element: Element\n  },\n  scope: Scope,\n) {\n  action = action || { name: null }\n\n  interaction.interactable = interactable\n  interaction.element = element\n  copyAction(interaction.prepared, action)\n\n  interaction.rect = interactable && action.name ? interactable.getRect(element) : null\n\n  setInteractionCursor(interaction, scope)\n\n  scope.fire('autoStart:prepared', { interaction })\n}\n\nfunction withinInteractionLimit<T extends ActionName>(\n  interactable: Interactable,\n  element: Element,\n  action: ActionProps<T>,\n  scope: Scope,\n) {\n  const options = interactable.options\n  const maxActions = options[action.name].max\n  const maxPerElement = options[action.name].maxPerElement\n  const autoStartMax = scope.autoStart.maxInteractions\n  let activeInteractions = 0\n  let interactableCount = 0\n  let elementCount = 0\n\n  // no actions if any of these values == 0\n  if (!(maxActions && maxPerElement && autoStartMax)) {\n    return false\n  }\n\n  for (const interaction of scope.interactions.list) {\n    const otherAction = interaction.prepared.name\n\n    if (!interaction.interacting()) {\n      continue\n    }\n\n    activeInteractions++\n\n    if (activeInteractions >= autoStartMax) {\n      return false\n    }\n\n    if (interaction.interactable !== interactable) {\n      continue\n    }\n\n    interactableCount += otherAction === action.name ? 1 : 0\n\n    if (interactableCount >= maxActions) {\n      return false\n    }\n\n    if (interaction.element === element) {\n      elementCount++\n\n      if (otherAction === action.name && elementCount >= maxPerElement) {\n        return false\n      }\n    }\n  }\n\n  return autoStartMax > 0\n}\n\nfunction maxInteractions(newValue: any, scope: Scope) {\n  if (is.number(newValue)) {\n    scope.autoStart.maxInteractions = newValue\n\n    return this\n  }\n\n  return scope.autoStart.maxInteractions\n}\n\nfunction setCursor(element: Element, cursor: string, scope: Scope) {\n  const { cursorElement: prevCursorElement } = scope.autoStart\n\n  if (prevCursorElement && prevCursorElement !== element) {\n    prevCursorElement.style.cursor = ''\n  }\n\n  element.ownerDocument.documentElement.style.cursor = cursor\n  element.style.cursor = cursor\n  scope.autoStart.cursorElement = cursor ? element : null\n}\n\nfunction setInteractionCursor<T extends ActionName>(interaction: Interaction<T>, scope: Scope) {\n  const { interactable, element, prepared } = interaction\n\n  if (!(interaction.pointerType === 'mouse' && interactable && interactable.options.styleCursor)) {\n    // clear previous target element cursor\n    if (scope.autoStart.cursorElement) {\n      setCursor(scope.autoStart.cursorElement, '', scope)\n    }\n\n    return\n  }\n\n  let cursor = ''\n\n  if (prepared.name) {\n    const cursorChecker = interactable.options[prepared.name].cursorChecker\n\n    if (is.func(cursorChecker)) {\n      cursor = cursorChecker(prepared, interactable, element, interaction._interacting)\n    } else {\n      cursor = scope.actions.map[prepared.name].getCursor(prepared)\n    }\n  }\n\n  setCursor(interaction.element, cursor || '', scope)\n}\n\nconst autoStart: Plugin = {\n  id: 'auto-start/base',\n  before: ['actions'],\n  install,\n  listeners: {\n    'interactions:down': prepareOnDown,\n    'interactions:move': (arg, scope) => {\n      prepareOnMove(arg, scope)\n      startOnMove(arg, scope)\n    },\n    'interactions:stop': clearCursorOnStop,\n  },\n  maxInteractions,\n  withinInteractionLimit,\n  validateAction,\n}\n\nexport default autoStart\n", "import type { Interactable } from '@interactjs/core/Interactable'\nimport type Interaction from '@interactjs/core/Interaction'\nimport type { SignalArgs, Scope } from '@interactjs/core/scope'\nimport type { ActionName, Element } from '@interactjs/core/types'\nimport { parentNode } from '@interactjs/utils/domUtils'\nimport is from '@interactjs/utils/is'\n\nimport autoStart from './base'\n\nfunction beforeStart({ interaction, eventTarget, dx, dy }: SignalArgs['interactions:move'], scope: Scope) {\n  if (interaction.prepared.name !== 'drag') return\n\n  // check if a drag is in the correct axis\n  const absX = Math.abs(dx)\n  const absY = Math.abs(dy)\n  const targetOptions = interaction.interactable.options.drag\n  const startAxis = targetOptions.startAxis\n  const currentAxis = absX > absY ? 'x' : absX < absY ? 'y' : 'xy'\n\n  interaction.prepared.axis =\n    targetOptions.lockAxis === 'start'\n      ? (currentAxis[0] as 'x' | 'y') // always lock to one axis even if currentAxis === 'xy'\n      : targetOptions.lockAxis\n\n  // if the movement isn't in the startAxis of the interactable\n  if (currentAxis !== 'xy' && startAxis !== 'xy' && startAxis !== currentAxis) {\n    // cancel the prepared action\n    ;(interaction as Interaction<ActionName>).prepared.name = null\n\n    // then try to get a drag from another ineractable\n    let element = eventTarget as Element\n\n    const getDraggable = function (interactable: Interactable): Interactable | void {\n      if (interactable === interaction.interactable) return\n\n      const options = interaction.interactable.options.drag\n\n      if (!options.manualStart && interactable.testIgnoreAllow(options, element, eventTarget)) {\n        const action = interactable.getAction(\n          interaction.downPointer,\n          interaction.downEvent,\n          interaction,\n          element,\n        )\n\n        if (\n          action &&\n          action.name === 'drag' &&\n          checkStartAxis(currentAxis, interactable) &&\n          autoStart.validateAction(action, interactable, element, eventTarget, scope)\n        ) {\n          return interactable\n        }\n      }\n    }\n\n    // check all interactables\n    while (is.element(element)) {\n      const interactable = scope.interactables.forEachMatch(element, getDraggable)\n\n      if (interactable) {\n        ;(interaction as Interaction<ActionName>).prepared.name = 'drag'\n        interaction.interactable = interactable\n        interaction.element = element\n        break\n      }\n\n      element = parentNode(element) as Element\n    }\n  }\n}\n\nfunction checkStartAxis(startAxis: string, interactable: Interactable) {\n  if (!interactable) {\n    return false\n  }\n\n  const thisAxis = interactable.options.drag.startAxis\n\n  return startAxis === 'xy' || thisAxis === 'xy' || thisAxis === startAxis\n}\n\nexport default {\n  id: 'auto-start/dragAxis',\n  listeners: { 'autoStart:before-start': beforeStart },\n}\n", "import type Interaction from '@interactjs/core/Interaction'\nimport type { Scope, Plugin } from '@interactjs/core/scope'\n\n/* eslint-disable import/no-duplicates -- for typescript module augmentations */\nimport './base'\nimport basePlugin from './base'\n/* eslint-enable */\n\ndeclare module '@interactjs/core/options' {\n  interface PerActionDefaults {\n    hold?: number\n    delay?: number\n  }\n}\n\ndeclare module '@interactjs/core/Interaction' {\n  interface Interaction {\n    autoStartHoldTimer?: any\n  }\n}\n\nfunction install(scope: Scope) {\n  const { defaults } = scope\n\n  scope.usePlugin(basePlugin)\n\n  defaults.perAction.hold = 0\n  defaults.perAction.delay = 0\n}\n\nfunction getHoldDuration(interaction: Interaction) {\n  const actionName = interaction.prepared && interaction.prepared.name\n\n  if (!actionName) {\n    return null\n  }\n\n  const options = interaction.interactable.options\n\n  return options[actionName].hold || options[actionName].delay\n}\n\nconst hold: Plugin = {\n  id: 'auto-start/hold',\n  install,\n  listeners: {\n    'interactions:new': ({ interaction }) => {\n      interaction.autoStartHoldTimer = null\n    },\n\n    'autoStart:prepared': ({ interaction }) => {\n      const hold = getHoldDuration(interaction)\n\n      if (hold > 0) {\n        interaction.autoStartHoldTimer = setTimeout(() => {\n          interaction.start(interaction.prepared, interaction.interactable, interaction.element)\n        }, hold)\n      }\n    },\n\n    'interactions:move': ({ interaction, duplicate }) => {\n      if (interaction.autoStartHoldTimer && interaction.pointerWasMoved && !duplicate) {\n        clearTimeout(interaction.autoStartHoldTimer)\n        interaction.autoStartHoldTimer = null\n      }\n    },\n\n    // prevent regular down->move autoStart\n    'autoStart:before-start': ({ interaction }) => {\n      const holdDuration = getHoldDuration(interaction)\n\n      if (holdDuration > 0) {\n        interaction.prepared.name = null\n      }\n    },\n  },\n  getHoldDuration,\n}\nexport default hold\n", "import type { Scope } from '@interactjs/core/scope'\n\n/* eslint-disable import/no-duplicates -- for typescript module augmentations */\nimport './base'\nimport './dragAxis'\nimport './hold'\n\nimport autoStart from './base'\nimport dragAxis from './dragAxis'\nimport hold from './hold'\n/* eslint-enable import/no-duplicates */\n\nexport default {\n  id: 'auto-start',\n  install(scope: Scope) {\n    scope.usePlugin(autoStart)\n    scope.usePlugin(hold)\n    scope.usePlugin(dragAxis)\n  },\n}\n", "import { matchesSelector, nodeContains } from '@interactjs/utils/domUtils'\nimport is from '@interactjs/utils/is'\nimport { getWindow } from '@interactjs/utils/window'\n\nimport type { Interactable } from '@interactjs/core/Interactable'\nimport type Interaction from '@interactjs/core/Interaction'\nimport type { Scope } from '@interactjs/core/scope'\nimport type { PointerEventType } from '@interactjs/core/types'\n\ntype PreventDefaultValue = 'always' | 'never' | 'auto'\n\ndeclare module '@interactjs/core/Interactable' {\n  interface Interactable {\n    preventDefault(newValue: PreventDefaultValue): this\n    preventDefault(): PreventDefaultValue\n    /**\n     * Returns or sets whether to prevent the browser's default behaviour in\n     * response to pointer events. Can be set to:\n     *  - `'always'` to always prevent\n     *  - `'never'` to never prevent\n     *  - `'auto'` to let interact.js try to determine what would be best\n     *\n     * @param newValue - `'always'`, `'never'` or `'auto'`\n     * @returns The current setting or this Interactable\n     */\n    preventDefault(newValue?: PreventDefaultValue): PreventDefaultValue | this\n    checkAndPreventDefault(event: Event): void\n  }\n}\n\nconst preventDefault = function preventDefault(this: Interactable, newValue?: PreventDefaultValue) {\n  if (/^(always|never|auto)$/.test(newValue)) {\n    this.options.preventDefault = newValue\n    return this\n  }\n\n  if (is.bool(newValue)) {\n    this.options.preventDefault = newValue ? 'always' : 'never'\n    return this\n  }\n\n  return this.options.preventDefault\n} as Interactable['preventDefault']\n\nfunction checkAndPreventDefault(interactable: Interactable, scope: Scope, event: Event) {\n  const setting = interactable.options.preventDefault\n\n  if (setting === 'never') return\n\n  if (setting === 'always') {\n    event.preventDefault()\n    return\n  }\n\n  // setting === 'auto'\n\n  // if the browser supports passive event listeners and isn't running on iOS,\n  // don't preventDefault of touch{start,move} events. CSS touch-action and\n  // user-select should be used instead of calling event.preventDefault().\n  if (scope.events.supportsPassive && /^touch(start|move)$/.test(event.type)) {\n    const doc = getWindow(event.target).document\n    const docOptions = scope.getDocOptions(doc)\n\n    if (!(docOptions && docOptions.events) || docOptions.events.passive !== false) {\n      return\n    }\n  }\n\n  // don't preventDefault of pointerdown events\n  if (/^(mouse|pointer|touch)*(down|start)/i.test(event.type)) {\n    return\n  }\n\n  // don't preventDefault on editable elements\n  if (\n    is.element(event.target) &&\n    matchesSelector(event.target, 'input,select,textarea,[contenteditable=true],[contenteditable=true] *')\n  ) {\n    return\n  }\n\n  event.preventDefault()\n}\n\nfunction onInteractionEvent({ interaction, event }: { interaction: Interaction; event: PointerEventType }) {\n  if (interaction.interactable) {\n    interaction.interactable.checkAndPreventDefault(event as Event)\n  }\n}\n\nexport function install(scope: Scope) {\n  const { Interactable } = scope\n\n  Interactable.prototype.preventDefault = preventDefault\n\n  Interactable.prototype.checkAndPreventDefault = function (event) {\n    return checkAndPreventDefault(this, scope, event)\n  }\n\n  // prevent native HTML5 drag on interact.js target elements\n  scope.interactions.docEvents.push({\n    type: 'dragstart',\n    listener(event) {\n      for (const interaction of scope.interactions.list) {\n        if (\n          interaction.element &&\n          (interaction.element === event.target || nodeContains(interaction.element, event.target))\n        ) {\n          interaction.interactable.checkAndPreventDefault(event)\n          return\n        }\n      }\n    },\n  })\n}\n\nexport default {\n  id: 'core/interactablePreventDefault',\n  install,\n  listeners: ['down', 'move', 'up', 'cancel'].reduce((acc, eventType) => {\n    acc[`interactions:${eventType}`] = onInteractionEvent\n    return acc\n  }, {} as any),\n}\n", "import type { Actions } from '@interactjs/core/types'\n\nexport default function isNonNativeEvent(type: string, actions: Actions) {\n  if (actions.phaselessTypes[type]) {\n    return true\n  }\n\n  for (const name in actions.map) {\n    if (type.indexOf(name) === 0 && type.substr(name.length) in actions.phases) {\n      return true\n    }\n  }\n\n  return false\n}\n", "import * as arr from './arr'\nimport is from './is'\n\n// tslint:disable-next-line ban-types\nexport default function clone<T extends Object>(source: T): Partial<T> {\n  const dest = {} as Partial<T>\n\n  for (const prop in source) {\n    const value = source[prop]\n\n    if (is.plainObject(value)) {\n      dest[prop] = clone(value) as any\n    } else if (is.array(value)) {\n      dest[prop] = arr.from(value) as typeof value\n    } else {\n      dest[prop] = value\n    }\n  }\n\n  return dest\n}\n", "import type { EventPhase } from '@interactjs/core/InteractEvent'\nimport type { Interaction, DoAnyPhaseArg } from '@interactjs/core/Interaction'\nimport type { EdgeOptions, FullRect, Point, Rect } from '@interactjs/core/types'\nimport clone from '@interactjs/utils/clone'\nimport extend from '@interactjs/utils/extend'\nimport * as rectUtils from '@interactjs/utils/rect'\n\nimport type { Modifier, ModifierArg, ModifierState } from './types'\n\nexport interface ModificationResult {\n  delta: Point\n  rectDelta: Rect\n  coords: Point\n  rect: FullRect\n  eventProps: any[]\n  changed: boolean\n}\n\ninterface MethodArg {\n  phase: EventPhase\n  pageCoords: Point\n  rect: FullRect\n  coords: Point\n  preEnd?: boolean\n  skipModifiers?: number\n}\n\nexport class Modification {\n  states: ModifierState[] = []\n  startOffset: Rect = { left: 0, right: 0, top: 0, bottom: 0 }\n  startDelta!: Point\n  result!: ModificationResult\n  endResult!: Point\n  startEdges!: EdgeOptions\n  edges: EdgeOptions\n  readonly interaction: Readonly<Interaction>\n\n  constructor(interaction: Interaction) {\n    this.interaction = interaction\n    this.result = createResult()\n    this.edges = {\n      left: false,\n      right: false,\n      top: false,\n      bottom: false,\n    }\n  }\n\n  start({ phase }: { phase: EventPhase }, pageCoords: Point) {\n    const { interaction } = this\n    const modifierList = getModifierList(interaction)\n    this.prepareStates(modifierList)\n\n    this.startEdges = extend({}, interaction.edges)\n    this.edges = extend({}, this.startEdges)\n    this.startOffset = getRectOffset(interaction.rect, pageCoords)\n    this.startDelta = { x: 0, y: 0 }\n\n    const arg = this.fillArg({\n      phase,\n      pageCoords,\n      preEnd: false,\n    })\n\n    this.result = createResult()\n    this.startAll(arg)\n\n    const result = (this.result = this.setAll(arg))\n\n    return result\n  }\n\n  fillArg(arg: Partial<ModifierArg>) {\n    const { interaction } = this\n\n    arg.interaction = interaction\n    arg.interactable = interaction.interactable\n    arg.element = interaction.element\n    arg.rect ||= interaction.rect\n    arg.edges ||= this.startEdges\n    arg.startOffset = this.startOffset\n\n    return arg as ModifierArg\n  }\n\n  startAll(arg: MethodArg & Partial<ModifierArg>) {\n    for (const state of this.states) {\n      if (state.methods.start) {\n        arg.state = state\n        state.methods.start(arg as ModifierArg)\n      }\n    }\n  }\n\n  setAll(arg: MethodArg & Partial<ModifierArg>): ModificationResult {\n    const { phase, preEnd, skipModifiers, rect: unmodifiedRect, edges: unmodifiedEdges } = arg\n\n    arg.coords = extend({}, arg.pageCoords)\n    arg.rect = extend({}, unmodifiedRect)\n    arg.edges = extend({}, unmodifiedEdges)\n\n    const states = skipModifiers ? this.states.slice(skipModifiers) : this.states\n\n    const newResult = createResult(arg.coords, arg.rect)\n\n    for (const state of states) {\n      const { options } = state\n      const lastModifierCoords = extend({}, arg.coords)\n      let returnValue = null\n\n      if (state.methods?.set && this.shouldDo(options, preEnd, phase)) {\n        arg.state = state\n        returnValue = state.methods.set(arg as ModifierArg<never>)\n\n        rectUtils.addEdges(arg.edges, arg.rect, {\n          x: arg.coords.x - lastModifierCoords.x,\n          y: arg.coords.y - lastModifierCoords.y,\n        })\n      }\n\n      newResult.eventProps.push(returnValue)\n    }\n\n    extend(this.edges, arg.edges)\n\n    newResult.delta.x = arg.coords.x - arg.pageCoords.x\n    newResult.delta.y = arg.coords.y - arg.pageCoords.y\n\n    newResult.rectDelta.left = arg.rect.left - unmodifiedRect.left\n    newResult.rectDelta.right = arg.rect.right - unmodifiedRect.right\n    newResult.rectDelta.top = arg.rect.top - unmodifiedRect.top\n    newResult.rectDelta.bottom = arg.rect.bottom - unmodifiedRect.bottom\n\n    const prevCoords = this.result.coords\n    const prevRect = this.result.rect\n\n    if (prevCoords && prevRect) {\n      const rectChanged =\n        newResult.rect.left !== prevRect.left ||\n        newResult.rect.right !== prevRect.right ||\n        newResult.rect.top !== prevRect.top ||\n        newResult.rect.bottom !== prevRect.bottom\n\n      newResult.changed =\n        rectChanged || prevCoords.x !== newResult.coords.x || prevCoords.y !== newResult.coords.y\n    }\n\n    return newResult\n  }\n\n  applyToInteraction(arg: { phase: EventPhase; rect?: Rect }) {\n    const { interaction } = this\n    const { phase } = arg\n    const curCoords = interaction.coords.cur\n    const startCoords = interaction.coords.start\n    const { result, startDelta } = this\n    const curDelta = result.delta\n\n    if (phase === 'start') {\n      extend(this.startDelta, result.delta)\n    }\n\n    for (const [coordsSet, delta] of [\n      [startCoords, startDelta],\n      [curCoords, curDelta],\n    ] as const) {\n      coordsSet.page.x += delta.x\n      coordsSet.page.y += delta.y\n      coordsSet.client.x += delta.x\n      coordsSet.client.y += delta.y\n    }\n\n    const { rectDelta } = this.result\n    const rect = arg.rect || interaction.rect\n\n    rect.left += rectDelta.left\n    rect.right += rectDelta.right\n    rect.top += rectDelta.top\n    rect.bottom += rectDelta.bottom\n\n    rect.width = rect.right - rect.left\n    rect.height = rect.bottom - rect.top\n  }\n\n  setAndApply(\n    arg: Partial<DoAnyPhaseArg> & {\n      phase: EventPhase\n      preEnd?: boolean\n      skipModifiers?: number\n      modifiedCoords?: Point\n    },\n  ): void | false {\n    const { interaction } = this\n    const { phase, preEnd, skipModifiers } = arg\n\n    const result = this.setAll(\n      this.fillArg({\n        preEnd,\n        phase,\n        pageCoords: arg.modifiedCoords || interaction.coords.cur.page,\n      }),\n    )\n\n    this.result = result\n\n    // don't fire an action move if a modifier would keep the event in the same\n    // cordinates as before\n    if (\n      !result.changed &&\n      (!skipModifiers || skipModifiers < this.states.length) &&\n      interaction.interacting()\n    ) {\n      return false\n    }\n\n    if (arg.modifiedCoords) {\n      const { page } = interaction.coords.cur\n      const adjustment = {\n        x: arg.modifiedCoords.x - page.x,\n        y: arg.modifiedCoords.y - page.y,\n      }\n\n      result.coords.x += adjustment.x\n      result.coords.y += adjustment.y\n      result.delta.x += adjustment.x\n      result.delta.y += adjustment.y\n    }\n\n    this.applyToInteraction(arg)\n  }\n\n  beforeEnd(arg: Omit<DoAnyPhaseArg, 'iEvent'> & { state?: ModifierState }): void | false {\n    const { interaction, event } = arg\n    const states = this.states\n\n    if (!states || !states.length) {\n      return\n    }\n\n    let doPreend = false\n\n    for (const state of states) {\n      arg.state = state\n      const { options, methods } = state\n\n      const endPosition = methods.beforeEnd && methods.beforeEnd(arg as unknown as ModifierArg)\n\n      if (endPosition) {\n        this.endResult = endPosition\n        return false\n      }\n\n      doPreend = doPreend || (!doPreend && this.shouldDo(options, true, arg.phase, true))\n    }\n\n    if (doPreend) {\n      // trigger a final modified move before ending\n      interaction.move({ event, preEnd: true })\n    }\n  }\n\n  stop(arg: { interaction: Interaction }) {\n    const { interaction } = arg\n\n    if (!this.states || !this.states.length) {\n      return\n    }\n\n    const modifierArg: Partial<ModifierArg> = extend(\n      {\n        states: this.states,\n        interactable: interaction.interactable,\n        element: interaction.element,\n        rect: null,\n      },\n      arg,\n    )\n\n    this.fillArg(modifierArg)\n\n    for (const state of this.states) {\n      modifierArg.state = state\n\n      if (state.methods.stop) {\n        state.methods.stop(modifierArg as ModifierArg)\n      }\n    }\n\n    this.states = null\n    this.endResult = null\n  }\n\n  prepareStates(modifierList: Modifier[]) {\n    this.states = []\n\n    for (let index = 0; index < modifierList.length; index++) {\n      const { options, methods, name } = modifierList[index]\n\n      this.states.push({\n        options,\n        methods,\n        index,\n        name,\n      })\n    }\n\n    return this.states\n  }\n\n  restoreInteractionCoords({ interaction: { coords, rect, modification } }: { interaction: Interaction }) {\n    if (!modification.result) return\n\n    const { startDelta } = modification\n    const { delta: curDelta, rectDelta } = modification.result\n\n    const coordsAndDeltas = [\n      [coords.start, startDelta],\n      [coords.cur, curDelta],\n    ]\n\n    for (const [coordsSet, delta] of coordsAndDeltas as any) {\n      coordsSet.page.x -= delta.x\n      coordsSet.page.y -= delta.y\n      coordsSet.client.x -= delta.x\n      coordsSet.client.y -= delta.y\n    }\n\n    rect.left -= rectDelta.left\n    rect.right -= rectDelta.right\n    rect.top -= rectDelta.top\n    rect.bottom -= rectDelta.bottom\n  }\n\n  shouldDo(options, preEnd?: boolean, phase?: string, requireEndOnly?: boolean) {\n    if (\n      // ignore disabled modifiers\n      !options ||\n      options.enabled === false ||\n      // check if we require endOnly option to fire move before end\n      (requireEndOnly && !options.endOnly) ||\n      // don't apply endOnly modifiers when not ending\n      (options.endOnly && !preEnd) ||\n      // check if modifier should run be applied on start\n      (phase === 'start' && !options.setStart)\n    ) {\n      return false\n    }\n\n    return true\n  }\n\n  copyFrom(other: Modification) {\n    this.startOffset = other.startOffset\n    this.startDelta = other.startDelta\n    this.startEdges = other.startEdges\n    this.edges = other.edges\n    this.states = other.states.map((s) => clone(s) as ModifierState)\n    this.result = createResult(extend({}, other.result.coords), extend({}, other.result.rect))\n  }\n\n  destroy() {\n    for (const prop in this) {\n      this[prop] = null\n    }\n  }\n}\n\nfunction createResult(coords?: Point, rect?: FullRect): ModificationResult {\n  return {\n    rect,\n    coords,\n    delta: { x: 0, y: 0 },\n    rectDelta: {\n      left: 0,\n      right: 0,\n      top: 0,\n      bottom: 0,\n    },\n    eventProps: [],\n    changed: true,\n  }\n}\n\nfunction getModifierList(interaction) {\n  const actionOptions = interaction.interactable.options[interaction.prepared.name]\n  const actionModifiers = actionOptions.modifiers\n\n  if (actionModifiers && actionModifiers.length) {\n    return actionModifiers\n  }\n\n  return ['snap', 'snapSize', 'snapEdges', 'restrict', 'restrictEdges', 'restrictSize']\n    .map((type) => {\n      const options = actionOptions[type]\n\n      return (\n        options &&\n        options.enabled && {\n          options,\n          methods: options._methods,\n        }\n      )\n    })\n    .filter((m) => !!m)\n}\n\nexport function getRectOffset(rect, coords) {\n  return rect\n    ? {\n        left: coords.x - rect.left,\n        top: coords.y - rect.top,\n        right: rect.right - coords.x,\n        bottom: rect.bottom - coords.y,\n      }\n    : {\n        left: 0,\n        top: 0,\n        right: 0,\n        bottom: 0,\n      }\n}\n", "import type { InteractEvent } from '@interactjs/core/InteractEvent'\nimport type Interaction from '@interactjs/core/Interaction'\nimport type { Plugin } from '@interactjs/core/scope'\n\nimport { Modification } from './Modification'\nimport type { Modifier, ModifierModule, ModifierState } from './types'\n\ndeclare module '@interactjs/core/Interaction' {\n  interface Interaction {\n    modification?: Modification\n  }\n}\n\ndeclare module '@interactjs/core/InteractEvent' {\n  interface InteractEvent {\n    modifiers?: Array<{\n      name: string\n      [key: string]: any\n    }>\n  }\n}\n\ndeclare module '@interactjs/core/options' {\n  interface PerActionDefaults {\n    modifiers?: Modifier[]\n  }\n}\n\nexport function makeModifier<\n  Defaults extends { enabled?: boolean },\n  State extends ModifierState,\n  Name extends string,\n  Result,\n>(module: ModifierModule<Defaults, State, Result>, name?: Name) {\n  const { defaults } = module\n  const methods = {\n    start: module.start,\n    set: module.set,\n    beforeEnd: module.beforeEnd,\n    stop: module.stop,\n  }\n\n  const modifier = (_options?: Partial<Defaults>) => {\n    const options = (_options || {}) as Defaults\n\n    options.enabled = options.enabled !== false\n\n    // add missing defaults to options\n    for (const prop in defaults) {\n      if (!(prop in options)) {\n        ;(options as any)[prop] = defaults[prop]\n      }\n    }\n\n    const m: Modifier<Defaults, State, Name, Result> = {\n      options,\n      methods,\n      name,\n      enable: () => {\n        options.enabled = true\n        return m\n      },\n      disable: () => {\n        options.enabled = false\n        return m\n      },\n    }\n\n    return m\n  }\n\n  if (name && typeof name === 'string') {\n    // for backwrads compatibility\n    modifier._defaults = defaults\n    modifier._methods = methods\n  }\n\n  return modifier\n}\n\nexport function addEventModifiers({\n  iEvent,\n  interaction,\n}: {\n  iEvent: InteractEvent<any>\n  interaction: Interaction<any>\n}) {\n  const result = interaction.modification!.result\n\n  if (result) {\n    iEvent.modifiers = result.eventProps\n  }\n}\n\nconst modifiersBase: Plugin = {\n  id: 'modifiers/base',\n  before: ['actions'],\n  install: (scope) => {\n    scope.defaults.perAction.modifiers = []\n  },\n  listeners: {\n    'interactions:new': ({ interaction }) => {\n      interaction.modification = new Modification(interaction)\n    },\n\n    'interactions:before-action-start': (arg) => {\n      const { interaction } = arg\n      const modification = arg.interaction.modification!\n\n      modification.start(arg, interaction.coords.start.page)\n      interaction.edges = modification.edges\n      modification.applyToInteraction(arg)\n    },\n\n    'interactions:before-action-move': (arg) => {\n      const { interaction } = arg\n      const { modification } = interaction\n      const ret = modification.setAndApply(arg)\n      interaction.edges = modification.edges\n\n      return ret\n    },\n\n    'interactions:before-action-end': (arg) => {\n      const { interaction } = arg\n      const { modification } = interaction\n      const ret = modification.beforeEnd(arg)\n      interaction.edges = modification.startEdges\n\n      return ret\n    },\n\n    'interactions:action-start': addEventModifiers,\n    'interactions:action-move': addEventModifiers,\n    'interactions:action-end': addEventModifiers,\n\n    'interactions:after-action-start': (arg) => arg.interaction.modification.restoreInteractionCoords(arg),\n    'interactions:after-action-move': (arg) => arg.interaction.modification.restoreInteractionCoords(arg),\n\n    'interactions:stop': (arg) => arg.interaction.modification.stop(arg),\n  },\n}\n\nexport default modifiersBase\n", "import type { Point, Listeners, OrBoolean, Element, Rect } from '@interactjs/core/types'\n\nexport interface Defaults {\n  base: BaseDefaults\n  perAction: PerActionDefaults\n  actions: ActionDefaults\n}\n\n// eslint-disable-next-line @typescript-eslint/no-empty-interface\nexport interface ActionDefaults {}\n\nexport interface BaseDefaults {\n  preventDefault?: 'always' | 'never' | 'auto'\n  deltaSource?: 'page' | 'client'\n  context?: Node\n  getRect?: (element: Element) => Rect\n}\n\nexport interface PerActionDefaults {\n  enabled?: boolean\n  origin?: Point | string | Element\n  listeners?: Listeners\n  allowFrom?: string | Element\n  ignoreFrom?: string | Element\n}\n\nexport type Options = Partial<BaseDefaults> &\n  Partial<PerActionDefaults> & {\n    [P in keyof ActionDefaults]?: Partial<ActionDefaults[P]>\n  }\n\nexport interface OptionsArg extends BaseDefaults, OrBoolean<Partial<ActionDefaults>> {}\n\nexport const defaults: Defaults = {\n  base: {\n    preventDefault: 'auto',\n    deltaSource: 'page',\n  },\n\n  perAction: {\n    enabled: false,\n    origin: { x: 0, y: 0 },\n  },\n\n  actions: {} as ActionDefaults,\n}\n", "import extend from '@interactjs/utils/extend'\nimport getOriginXY from '@interactjs/utils/getOriginXY'\nimport hypot from '@interactjs/utils/hypot'\n\nimport type { Point, FullRect, PointerEventType, Element, ActionName } from '@interactjs/core/types'\n\nimport { BaseEvent } from './BaseEvent'\nimport type { Interaction } from './Interaction'\nimport { defaults } from './options'\n\nexport type EventPhase = keyof PhaseMap\n\nexport interface PhaseMap {\n  start: true\n  move: true\n  end: true\n}\n\n// defined outside of class definition to avoid assignment of undefined during\n// construction\nexport interface InteractEvent {\n  pageX: number\n  pageY: number\n\n  clientX: number\n  clientY: number\n\n  dx: number\n  dy: number\n\n  velocityX: number\n  velocityY: number\n}\n\nexport class InteractEvent<\n  T extends ActionName = never,\n  P extends EventPhase = EventPhase,\n> extends BaseEvent<T> {\n  declare target: Element\n  declare currentTarget: Element\n  relatedTarget: Element | null = null\n  screenX?: number\n  screenY?: number\n  button: number\n  buttons: number\n  ctrlKey: boolean\n  shiftKey: boolean\n  altKey: boolean\n  metaKey: boolean\n  page: Point\n  client: Point\n  delta: Point\n  rect: FullRect\n  x0: number\n  y0: number\n  t0: number\n  dt: number\n  duration: number\n  clientX0: number\n  clientY0: number\n  velocity: Point\n  speed: number\n  swipe: ReturnType<InteractEvent<T>['getSwipe']>\n  // resize\n  axes?: 'x' | 'y' | 'xy'\n  /** @internal */\n  preEnd?: boolean\n\n  constructor(\n    interaction: Interaction<T>,\n    event: PointerEventType,\n    actionName: T,\n    phase: P,\n    element: Element,\n    preEnd?: boolean,\n    type?: string,\n  ) {\n    super(interaction)\n\n    element = element || interaction.element\n\n    const target = interaction.interactable\n    const deltaSource = (((target && target.options) || defaults) as any).deltaSource as 'page' | 'client'\n    const origin = getOriginXY(target, element, actionName)\n    const starting = phase === 'start'\n    const ending = phase === 'end'\n    const prevEvent = starting ? this : interaction.prevEvent\n    const coords = starting\n      ? interaction.coords.start\n      : ending\n        ? { page: prevEvent.page, client: prevEvent.client, timeStamp: interaction.coords.cur.timeStamp }\n        : interaction.coords.cur\n\n    this.page = extend({}, coords.page)\n    this.client = extend({}, coords.client)\n    this.rect = extend({}, interaction.rect)\n    this.timeStamp = coords.timeStamp\n\n    if (!ending) {\n      this.page.x -= origin.x\n      this.page.y -= origin.y\n\n      this.client.x -= origin.x\n      this.client.y -= origin.y\n    }\n\n    this.ctrlKey = event.ctrlKey\n    this.altKey = event.altKey\n    this.shiftKey = event.shiftKey\n    this.metaKey = event.metaKey\n    this.button = (event as MouseEvent).button\n    this.buttons = (event as MouseEvent).buttons\n    this.target = element\n    this.currentTarget = element\n    this.preEnd = preEnd\n    this.type = type || actionName + (phase || '')\n    this.interactable = target\n\n    this.t0 = starting ? interaction.pointers[interaction.pointers.length - 1].downTime : prevEvent.t0\n\n    this.x0 = interaction.coords.start.page.x - origin.x\n    this.y0 = interaction.coords.start.page.y - origin.y\n    this.clientX0 = interaction.coords.start.client.x - origin.x\n    this.clientY0 = interaction.coords.start.client.y - origin.y\n\n    if (starting || ending) {\n      this.delta = { x: 0, y: 0 }\n    } else {\n      this.delta = {\n        x: this[deltaSource].x - prevEvent[deltaSource].x,\n        y: this[deltaSource].y - prevEvent[deltaSource].y,\n      }\n    }\n\n    this.dt = interaction.coords.delta.timeStamp\n    this.duration = this.timeStamp - this.t0\n\n    // velocity and speed in pixels per second\n    this.velocity = extend({}, interaction.coords.velocity[deltaSource])\n    this.speed = hypot(this.velocity.x, this.velocity.y)\n\n    this.swipe = ending || phase === 'inertiastart' ? this.getSwipe() : null\n  }\n\n  getSwipe() {\n    const interaction = this._interaction\n\n    if (interaction.prevEvent.speed < 600 || this.timeStamp - interaction.prevEvent.timeStamp > 150) {\n      return null\n    }\n\n    let angle = (180 * Math.atan2(interaction.prevEvent.velocityY, interaction.prevEvent.velocityX)) / Math.PI\n    const overlap = 22.5\n\n    if (angle < 0) {\n      angle += 360\n    }\n\n    const left = 135 - overlap <= angle && angle < 225 + overlap\n    const up = 225 - overlap <= angle && angle < 315 + overlap\n\n    const right = !left && (315 - overlap <= angle || angle < 45 + overlap)\n    const down = !up && 45 - overlap <= angle && angle < 135 + overlap\n\n    return {\n      up,\n      down,\n      left,\n      right,\n      angle,\n      speed: interaction.prevEvent.speed,\n      velocity: {\n        x: interaction.prevEvent.velocityX,\n        y: interaction.prevEvent.velocityY,\n      },\n    }\n  }\n\n  preventDefault() {}\n\n  /**\n   * Don't call listeners on the remaining targets\n   */\n  stopImmediatePropagation() {\n    this.immediatePropagationStopped = this.propagationStopped = true\n  }\n\n  /**\n   * Don't call any other listeners (even on the current target)\n   */\n  stopPropagation() {\n    this.propagationStopped = true\n  }\n}\n\n// getters and setters defined here to support typescript 3.6 and below which\n// don't support getter and setters in .d.ts files\nObject.defineProperties(InteractEvent.prototype, {\n  pageX: {\n    get() {\n      return this.page.x\n    },\n    set(value) {\n      this.page.x = value\n    },\n  },\n  pageY: {\n    get() {\n      return this.page.y\n    },\n    set(value) {\n      this.page.y = value\n    },\n  },\n\n  clientX: {\n    get() {\n      return this.client.x\n    },\n    set(value) {\n      this.client.x = value\n    },\n  },\n  clientY: {\n    get() {\n      return this.client.y\n    },\n    set(value) {\n      this.client.y = value\n    },\n  },\n\n  dx: {\n    get() {\n      return this.delta.x\n    },\n    set(value) {\n      this.delta.x = value\n    },\n  },\n  dy: {\n    get() {\n      return this.delta.y\n    },\n    set(value) {\n      this.delta.y = value\n    },\n  },\n\n  velocityX: {\n    get() {\n      return this.velocity.x\n    },\n    set(value) {\n      this.velocity.x = value\n    },\n  },\n  velocityY: {\n    get() {\n      return this.velocity.y\n    },\n    set(value) {\n      this.velocity.y = value\n    },\n  },\n})\n", "import type { PointerEventType, PointerType } from '@interactjs/core/types'\n\nexport class PointerInfo {\n  id: number\n  pointer: PointerType\n  event: PointerEventType\n  downTime: number\n  downTarget: Node\n\n  constructor(id: number, pointer: PointerType, event: PointerEventType, downTime: number, downTarget: Node) {\n    this.id = id\n    this.pointer = pointer\n    this.event = event\n    this.downTime = downTime\n    this.downTarget = downTarget\n  }\n}\n", "import * as arr from '@interactjs/utils/arr'\nimport extend from '@interactjs/utils/extend'\nimport hypot from '@interactjs/utils/hypot'\nimport { warnOnce, copyAction } from '@interactjs/utils/misc'\nimport * as pointerUtils from '@interactjs/utils/pointerUtils'\nimport * as rectUtils from '@interactjs/utils/rect'\n\nimport type {\n  Element,\n  EdgeOptions,\n  PointerEventType,\n  PointerType,\n  FullRect,\n  CoordsSet,\n  ActionName,\n  ActionProps,\n} from '@interactjs/core/types'\n\nimport type { Interactable } from './Interactable'\nimport type { EventPhase } from './InteractEvent'\nimport { InteractEvent } from './InteractEvent'\nimport type { ActionDefaults } from './options'\nimport { PointerInfo } from './PointerInfo'\nimport type { Scope } from './scope'\n\nexport enum _ProxyValues {\n  interactable = '',\n  element = '',\n  prepared = '',\n  pointerIsDown = '',\n  pointerWasMoved = '',\n  _proxy = '',\n}\n\nexport enum _ProxyMethods {\n  start = '',\n  move = '',\n  end = '',\n  stop = '',\n  interacting = '',\n}\n\nexport type PointerArgProps<T extends {} = {}> = {\n  pointer: PointerType\n  event: PointerEventType\n  eventTarget: Node\n  pointerIndex: number\n  pointerInfo: PointerInfo\n  interaction: Interaction<never>\n} & T\n\nexport interface DoPhaseArg<T extends ActionName, P extends EventPhase> {\n  event: PointerEventType\n  phase: EventPhase\n  interaction: Interaction<T>\n  iEvent: InteractEvent<T, P>\n  preEnd?: boolean\n  type?: string\n}\n\nexport type DoAnyPhaseArg = DoPhaseArg<ActionName, EventPhase>\n\ndeclare module '@interactjs/core/scope' {\n  interface SignalArgs {\n    'interactions:new': { interaction: Interaction<ActionName> }\n    'interactions:down': PointerArgProps<{\n      type: 'down'\n    }>\n    'interactions:move': PointerArgProps<{\n      type: 'move'\n      dx: number\n      dy: number\n      duplicate: boolean\n    }>\n    'interactions:up': PointerArgProps<{\n      type: 'up'\n      curEventTarget: EventTarget\n    }>\n    'interactions:cancel': SignalArgs['interactions:up'] & {\n      type: 'cancel'\n      curEventTarget: EventTarget\n    }\n    'interactions:update-pointer': PointerArgProps<{\n      down: boolean\n    }>\n    'interactions:remove-pointer': PointerArgProps\n    'interactions:blur': { interaction: Interaction<never>; event: Event; type: 'blur' }\n    'interactions:before-action-start': Omit<DoAnyPhaseArg, 'iEvent'>\n    'interactions:action-start': DoAnyPhaseArg\n    'interactions:after-action-start': DoAnyPhaseArg\n    'interactions:before-action-move': Omit<DoAnyPhaseArg, 'iEvent'>\n    'interactions:action-move': DoAnyPhaseArg\n    'interactions:after-action-move': DoAnyPhaseArg\n    'interactions:before-action-end': Omit<DoAnyPhaseArg, 'iEvent'>\n    'interactions:action-end': DoAnyPhaseArg\n    'interactions:after-action-end': DoAnyPhaseArg\n    'interactions:stop': { interaction: Interaction }\n  }\n}\n\nexport type InteractionProxy<T extends ActionName | null = never> = Pick<\n  Interaction<T>,\n  Exclude<keyof typeof _ProxyValues | keyof typeof _ProxyMethods, '_proxy'>\n>\n\nlet idCounter = 0\n\nexport class Interaction<T extends ActionName | null = ActionName> {\n  /** current interactable being interacted with */\n  interactable: Interactable | null = null\n\n  /** the target element of the interactable */\n  element: Element | null = null\n  rect: FullRect | null = null\n  /** @internal */\n  _rects?: {\n    start: FullRect\n    corrected: FullRect\n    previous: FullRect\n    delta: FullRect\n  }\n  /** @internal */\n  edges: EdgeOptions | null = null\n\n  /** @internal */\n  _scopeFire: Scope['fire']\n\n  // action that's ready to be fired on next move event\n  prepared: ActionProps<T> = {\n    name: null,\n    axis: null,\n    edges: null,\n  }\n\n  pointerType: string\n\n  /** @internal keep track of added pointers */\n  pointers: PointerInfo[] = []\n\n  /** @internal pointerdown/mousedown/touchstart event */\n  downEvent: PointerEventType | null = null\n\n  /** @internal */ downPointer: PointerType = {} as PointerType\n\n  /** @internal */\n  _latestPointer: {\n    pointer: PointerType\n    event: PointerEventType\n    eventTarget: Node\n  } = {\n    pointer: null,\n    event: null,\n    eventTarget: null,\n  }\n\n  /** @internal */ prevEvent: InteractEvent<T, EventPhase> = null\n\n  pointerIsDown = false\n  pointerWasMoved = false\n  /** @internal */ _interacting = false\n  /** @internal */ _ending = false\n  /** @internal */ _stopped = true\n  /** @internal */ _proxy: InteractionProxy<T>\n\n  /** @internal */ simulation = null\n\n  /** @internal */ get pointerMoveTolerance() {\n    return 1\n  }\n\n  doMove = warnOnce(function (this: Interaction, signalArg: any) {\n    this.move(signalArg)\n  }, 'The interaction.doMove() method has been renamed to interaction.move()')\n\n  coords: CoordsSet = {\n    // Starting InteractEvent pointer coordinates\n    start: pointerUtils.newCoords(),\n    // Previous native pointer move event coordinates\n    prev: pointerUtils.newCoords(),\n    // current native pointer move event coordinates\n    cur: pointerUtils.newCoords(),\n    // Change in coordinates and time of the pointer\n    delta: pointerUtils.newCoords(),\n    // pointer velocity\n    velocity: pointerUtils.newCoords(),\n  }\n\n  /** @internal */ readonly _id: number = idCounter++\n\n  constructor({ pointerType, scopeFire }: { pointerType?: string; scopeFire: Scope['fire'] }) {\n    this._scopeFire = scopeFire\n    this.pointerType = pointerType\n\n    const that = this\n\n    this._proxy = {} as InteractionProxy<T>\n\n    for (const key in _ProxyValues) {\n      Object.defineProperty(this._proxy, key, {\n        get() {\n          return that[key]\n        },\n      })\n    }\n\n    for (const key in _ProxyMethods) {\n      Object.defineProperty(this._proxy, key, {\n        value: (...args: any[]) => that[key](...args),\n      })\n    }\n\n    this._scopeFire('interactions:new', { interaction: this })\n  }\n\n  pointerDown(pointer: PointerType, event: PointerEventType, eventTarget: Node) {\n    const pointerIndex = this.updatePointer(pointer, event, eventTarget, true)\n    const pointerInfo = this.pointers[pointerIndex]\n\n    this._scopeFire('interactions:down', {\n      pointer,\n      event,\n      eventTarget,\n      pointerIndex,\n      pointerInfo,\n      type: 'down',\n      interaction: this as unknown as Interaction<never>,\n    })\n  }\n\n  /**\n   * ```js\n   * interact(target)\n   *   .draggable({\n   *     // disable the default drag start by down->move\n   *     manualStart: true\n   *   })\n   *   // start dragging after the user holds the pointer down\n   *   .on('hold', function (event) {\n   *     var interaction = event.interaction\n   *\n   *     if (!interaction.interacting()) {\n   *       interaction.start({ name: 'drag' },\n   *                         event.interactable,\n   *                         event.currentTarget)\n   *     }\n   * })\n   * ```\n   *\n   * Start an action with the given Interactable and Element as tartgets. The\n   * action must be enabled for the target Interactable and an appropriate\n   * number of pointers must be held down - 1 for drag/resize, 2 for gesture.\n   *\n   * Use it with `interactable.<action>able({ manualStart: false })` to always\n   * [start actions manually](https://github.com/taye/interact.js/issues/114)\n   *\n   * @param action - The action to be performed - drag, resize, etc.\n   * @param target - The Interactable to target\n   * @param element - The DOM Element to target\n   * @returns Whether the interaction was successfully started\n   */\n  start<A extends ActionName>(action: ActionProps<A>, interactable: Interactable, element: Element): boolean {\n    if (\n      this.interacting() ||\n      !this.pointerIsDown ||\n      this.pointers.length < (action.name === 'gesture' ? 2 : 1) ||\n      !interactable.options[action.name as keyof ActionDefaults].enabled\n    ) {\n      return false\n    }\n\n    copyAction(this.prepared, action)\n\n    this.interactable = interactable\n    this.element = element\n    this.rect = interactable.getRect(element)\n    this.edges = this.prepared.edges\n      ? extend({}, this.prepared.edges)\n      : { left: true, right: true, top: true, bottom: true }\n    this._stopped = false\n    this._interacting =\n      this._doPhase({\n        interaction: this,\n        event: this.downEvent,\n        phase: 'start',\n      }) && !this._stopped\n\n    return this._interacting\n  }\n\n  pointerMove(pointer: PointerType, event: PointerEventType, eventTarget: Node) {\n    if (!this.simulation && !(this.modification && this.modification.endResult)) {\n      this.updatePointer(pointer, event, eventTarget, false)\n    }\n\n    const duplicateMove =\n      this.coords.cur.page.x === this.coords.prev.page.x &&\n      this.coords.cur.page.y === this.coords.prev.page.y &&\n      this.coords.cur.client.x === this.coords.prev.client.x &&\n      this.coords.cur.client.y === this.coords.prev.client.y\n\n    let dx: number\n    let dy: number\n\n    // register movement greater than pointerMoveTolerance\n    if (this.pointerIsDown && !this.pointerWasMoved) {\n      dx = this.coords.cur.client.x - this.coords.start.client.x\n      dy = this.coords.cur.client.y - this.coords.start.client.y\n\n      this.pointerWasMoved = hypot(dx, dy) > this.pointerMoveTolerance\n    }\n\n    const pointerIndex = this.getPointerIndex(pointer)\n    const signalArg = {\n      pointer,\n      pointerIndex,\n      pointerInfo: this.pointers[pointerIndex],\n      event,\n      type: 'move' as const,\n      eventTarget,\n      dx,\n      dy,\n      duplicate: duplicateMove,\n      interaction: this as unknown as Interaction<never>,\n    }\n\n    if (!duplicateMove) {\n      // set pointer coordinate, time changes and velocity\n      pointerUtils.setCoordVelocity(this.coords.velocity, this.coords.delta)\n    }\n\n    this._scopeFire('interactions:move', signalArg)\n\n    if (!duplicateMove && !this.simulation) {\n      // if interacting, fire an 'action-move' signal etc\n      if (this.interacting()) {\n        signalArg.type = null\n        this.move(signalArg)\n      }\n\n      if (this.pointerWasMoved) {\n        pointerUtils.copyCoords(this.coords.prev, this.coords.cur)\n      }\n    }\n  }\n\n  /**\n   * ```js\n   * interact(target)\n   *   .draggable(true)\n   *   .on('dragmove', function (event) {\n   *     if (someCondition) {\n   *       // change the snap settings\n   *       event.interactable.draggable({ snap: { targets: [] }})\n   *       // fire another move event with re-calculated snap\n   *       event.interaction.move()\n   *     }\n   *   })\n   * ```\n   *\n   * Force a move of the current action at the same coordinates. Useful if\n   * snap/restrict has been changed and you want a movement with the new\n   * settings.\n   */\n  move(signalArg?: any) {\n    if (!signalArg || !signalArg.event) {\n      pointerUtils.setZeroCoords(this.coords.delta)\n    }\n\n    signalArg = extend(\n      {\n        pointer: this._latestPointer.pointer,\n        event: this._latestPointer.event,\n        eventTarget: this._latestPointer.eventTarget,\n        interaction: this,\n      },\n      signalArg || {},\n    )\n\n    signalArg.phase = 'move'\n\n    this._doPhase(signalArg)\n  }\n\n  /**\n   * @internal\n   * End interact move events and stop auto-scroll unless simulation is running\n   */\n  pointerUp(pointer: PointerType, event: PointerEventType, eventTarget: Node, curEventTarget: EventTarget) {\n    let pointerIndex = this.getPointerIndex(pointer)\n\n    if (pointerIndex === -1) {\n      pointerIndex = this.updatePointer(pointer, event, eventTarget, false)\n    }\n\n    const type = /cancel$/i.test(event.type) ? 'cancel' : 'up'\n\n    this._scopeFire(`interactions:${type}` as 'interactions:up' | 'interactions:cancel', {\n      pointer,\n      pointerIndex,\n      pointerInfo: this.pointers[pointerIndex],\n      event,\n      eventTarget,\n      type: type as any,\n      curEventTarget,\n      interaction: this as unknown as Interaction<never>,\n    })\n\n    if (!this.simulation) {\n      this.end(event)\n    }\n\n    this.removePointer(pointer, event)\n  }\n\n  /** @internal */\n  documentBlur(event: Event) {\n    this.end(event as any)\n    this._scopeFire('interactions:blur', {\n      event,\n      type: 'blur',\n      interaction: this as unknown as Interaction<never>,\n    })\n  }\n\n  /**\n   * ```js\n   * interact(target)\n   *   .draggable(true)\n   *   .on('move', function (event) {\n   *     if (event.pageX > 1000) {\n   *       // end the current action\n   *       event.interaction.end()\n   *       // stop all further listeners from being called\n   *       event.stopImmediatePropagation()\n   *     }\n   *   })\n   * ```\n   */\n  end(event?: PointerEventType) {\n    this._ending = true\n    event = event || this._latestPointer.event\n    let endPhaseResult: boolean\n\n    if (this.interacting()) {\n      endPhaseResult = this._doPhase({\n        event,\n        interaction: this,\n        phase: 'end',\n      })\n    }\n\n    this._ending = false\n\n    if (endPhaseResult === true) {\n      this.stop()\n    }\n  }\n\n  currentAction() {\n    return this._interacting ? this.prepared.name : null\n  }\n\n  interacting() {\n    return this._interacting\n  }\n\n  stop() {\n    this._scopeFire('interactions:stop', { interaction: this })\n\n    this.interactable = this.element = null\n\n    this._interacting = false\n    this._stopped = true\n    this.prepared.name = this.prevEvent = null\n  }\n\n  /** @internal */\n  getPointerIndex(pointer: any) {\n    const pointerId = pointerUtils.getPointerId(pointer)\n\n    // mouse and pen interactions may have only one pointer\n    return this.pointerType === 'mouse' || this.pointerType === 'pen'\n      ? this.pointers.length - 1\n      : arr.findIndex(this.pointers, (curPointer) => curPointer.id === pointerId)\n  }\n\n  /** @internal */\n  getPointerInfo(pointer: any) {\n    return this.pointers[this.getPointerIndex(pointer)]\n  }\n\n  /** @internal */\n  updatePointer(pointer: PointerType, event: PointerEventType, eventTarget: Node, down?: boolean) {\n    const id = pointerUtils.getPointerId(pointer)\n    let pointerIndex = this.getPointerIndex(pointer)\n    let pointerInfo = this.pointers[pointerIndex]\n\n    down = down === false ? false : down || /(down|start)$/i.test(event.type)\n\n    if (!pointerInfo) {\n      pointerInfo = new PointerInfo(id, pointer, event, null, null)\n\n      pointerIndex = this.pointers.length\n      this.pointers.push(pointerInfo)\n    } else {\n      pointerInfo.pointer = pointer\n    }\n\n    pointerUtils.setCoords(\n      this.coords.cur,\n      this.pointers.map((p) => p.pointer),\n      this._now(),\n    )\n    pointerUtils.setCoordDeltas(this.coords.delta, this.coords.prev, this.coords.cur)\n\n    if (down) {\n      this.pointerIsDown = true\n\n      pointerInfo.downTime = this.coords.cur.timeStamp\n      pointerInfo.downTarget = eventTarget\n      pointerUtils.pointerExtend(this.downPointer, pointer)\n\n      if (!this.interacting()) {\n        pointerUtils.copyCoords(this.coords.start, this.coords.cur)\n        pointerUtils.copyCoords(this.coords.prev, this.coords.cur)\n\n        this.downEvent = event\n        this.pointerWasMoved = false\n      }\n    }\n\n    this._updateLatestPointer(pointer, event, eventTarget)\n\n    this._scopeFire('interactions:update-pointer', {\n      pointer,\n      event,\n      eventTarget,\n      down,\n      pointerInfo,\n      pointerIndex,\n      interaction: this as unknown as Interaction<never>,\n    })\n\n    return pointerIndex\n  }\n\n  /** @internal */\n  removePointer(pointer: PointerType, event: PointerEventType) {\n    const pointerIndex = this.getPointerIndex(pointer)\n\n    if (pointerIndex === -1) return\n\n    const pointerInfo = this.pointers[pointerIndex]\n\n    this._scopeFire('interactions:remove-pointer', {\n      pointer,\n      event,\n      eventTarget: null,\n      pointerIndex,\n      pointerInfo,\n      interaction: this as unknown as Interaction<never>,\n    })\n\n    this.pointers.splice(pointerIndex, 1)\n    this.pointerIsDown = false\n  }\n\n  /** @internal */\n  _updateLatestPointer(pointer: PointerType, event: PointerEventType, eventTarget: Node) {\n    this._latestPointer.pointer = pointer\n    this._latestPointer.event = event\n    this._latestPointer.eventTarget = eventTarget\n  }\n\n  destroy() {\n    this._latestPointer.pointer = null\n    this._latestPointer.event = null\n    this._latestPointer.eventTarget = null\n  }\n\n  /** @internal */\n  _createPreparedEvent<P extends EventPhase>(\n    event: PointerEventType,\n    phase: P,\n    preEnd?: boolean,\n    type?: string,\n  ) {\n    return new InteractEvent<T, P>(this, event, this.prepared.name, phase, this.element, preEnd, type)\n  }\n\n  /** @internal */\n  _fireEvent<P extends EventPhase>(iEvent: InteractEvent<T, P>) {\n    this.interactable?.fire(iEvent)\n\n    if (!this.prevEvent || iEvent.timeStamp >= this.prevEvent.timeStamp) {\n      this.prevEvent = iEvent\n    }\n  }\n\n  /** @internal */\n  _doPhase<P extends EventPhase>(\n    signalArg: Omit<DoPhaseArg<T, P>, 'iEvent'> & { iEvent?: InteractEvent<T, P> },\n  ) {\n    const { event, phase, preEnd, type } = signalArg\n    const { rect } = this\n\n    if (rect && phase === 'move') {\n      // update the rect changes due to pointer move\n      rectUtils.addEdges(this.edges, rect, this.coords.delta[this.interactable.options.deltaSource])\n\n      rect.width = rect.right - rect.left\n      rect.height = rect.bottom - rect.top\n    }\n\n    const beforeResult = this._scopeFire(`interactions:before-action-${phase}` as any, signalArg)\n\n    if (beforeResult === false) {\n      return false\n    }\n\n    const iEvent = (signalArg.iEvent = this._createPreparedEvent(event, phase, preEnd, type))\n\n    this._scopeFire(`interactions:action-${phase}` as any, signalArg)\n\n    if (phase === 'start') {\n      this.prevEvent = iEvent\n    }\n\n    this._fireEvent(iEvent)\n\n    this._scopeFire(`interactions:after-action-${phase}` as any, signalArg)\n\n    return true\n  }\n\n  /** @internal */\n  _now() {\n    return Date.now()\n  }\n}\n\nexport default Interaction\nexport { PointerInfo }\n", "import type Interaction from '@interactjs/core/Interaction'\nimport { _ProxyMethods } from '@interactjs/core/Interaction'\nimport type { Plugin } from '@interactjs/core/scope'\nimport type { Point } from '@interactjs/core/types'\nimport * as rectUtils from '@interactjs/utils/rect'\n\ndeclare module '@interactjs/core/Interaction' {\n  interface Interaction {\n    offsetBy?: typeof offsetBy\n    offset: {\n      total: Point\n      pending: Point\n    }\n  }\n\n  enum _ProxyMethods {\n    offsetBy = '',\n  }\n}\n\n;(_ProxyMethods as any).offsetBy = ''\n\nexport function addTotal(interaction: Interaction) {\n  if (!interaction.pointerIsDown) {\n    return\n  }\n\n  addToCoords(interaction.coords.cur, interaction.offset.total)\n\n  interaction.offset.pending.x = 0\n  interaction.offset.pending.y = 0\n}\n\nfunction beforeAction({ interaction }: { interaction: Interaction }) {\n  applyPending(interaction)\n}\n\nfunction beforeEnd({ interaction }: { interaction: Interaction }): boolean | void {\n  const hadPending = applyPending(interaction)\n\n  if (!hadPending) return\n\n  interaction.move({ offset: true })\n  interaction.end()\n\n  return false\n}\n\nfunction end({ interaction }: { interaction: Interaction }) {\n  interaction.offset.total.x = 0\n  interaction.offset.total.y = 0\n  interaction.offset.pending.x = 0\n  interaction.offset.pending.y = 0\n}\n\nexport function applyPending(interaction: Interaction) {\n  if (!hasPending(interaction)) {\n    return false\n  }\n\n  const { pending } = interaction.offset\n\n  addToCoords(interaction.coords.cur, pending)\n  addToCoords(interaction.coords.delta, pending)\n  rectUtils.addEdges(interaction.edges, interaction.rect, pending)\n\n  pending.x = 0\n  pending.y = 0\n\n  return true\n}\n\nfunction offsetBy(this: Interaction, { x, y }: Point) {\n  this.offset.pending.x += x\n  this.offset.pending.y += y\n\n  this.offset.total.x += x\n  this.offset.total.y += y\n}\n\nfunction addToCoords({ page, client }, { x, y }: Point) {\n  page.x += x\n  page.y += y\n  client.x += x\n  client.y += y\n}\n\nfunction hasPending(interaction: Interaction) {\n  return !!(interaction.offset.pending.x || interaction.offset.pending.y)\n}\n\nconst offset: Plugin = {\n  id: 'offset',\n  before: ['modifiers', 'pointer-events', 'actions', 'inertia'],\n  install(scope) {\n    scope.Interaction.prototype.offsetBy = offsetBy\n  },\n  listeners: {\n    'interactions:new': ({ interaction }) => {\n      interaction.offset = {\n        total: { x: 0, y: 0 },\n        pending: { x: 0, y: 0 },\n      }\n    },\n    'interactions:update-pointer': ({ interaction }) => addTotal(interaction),\n    'interactions:before-action-start': beforeAction,\n    'interactions:before-action-move': beforeAction,\n    'interactions:before-action-end': beforeEnd,\n    'interactions:stop': end,\n  },\n}\n\nexport default offset\n", "import type { Inter<PERSON>, DoPhaseArg } from '@interactjs/core/Interaction'\nimport type { <PERSON><PERSON>, <PERSON>Args, Plugin } from '@interactjs/core/scope'\nimport type { ActionName, Point, PointerEventType } from '@interactjs/core/types'\n/* eslint-disable import/no-duplicates -- for typescript module augmentations */\nimport '@interactjs/modifiers/base'\nimport '@interactjs/offset/plugin'\nimport * as modifiers from '@interactjs/modifiers/base'\nimport { Modification } from '@interactjs/modifiers/Modification'\nimport type { ModifierArg } from '@interactjs/modifiers/types'\nimport offset from '@interactjs/offset/plugin'\n/* eslint-enable import/no-duplicates */\nimport * as dom from '@interactjs/utils/domUtils'\nimport hypot from '@interactjs/utils/hypot'\nimport is from '@interactjs/utils/is'\nimport { copyCoords } from '@interactjs/utils/pointerUtils'\nimport raf from '@interactjs/utils/raf'\n\ndeclare module '@interactjs/core/InteractEvent' {\n  interface PhaseMap {\n    resume?: true\n    inertiastart?: true\n  }\n}\n\ndeclare module '@interactjs/core/Interaction' {\n  interface Interaction {\n    inertia?: InertiaState\n  }\n}\n\ndeclare module '@interactjs/core/options' {\n  interface PerActionDefaults {\n    inertia?: {\n      enabled?: boolean\n      resistance?: number // the lambda in exponential decay\n      minSpeed?: number // target speed must be above this for inertia to start\n      endSpeed?: number // the speed at which inertia is slow enough to stop\n      allowResume?: true // allow resuming an action in inertia phase\n      smoothEndDuration?: number // animate to snap/restrict endOnly if there's no inertia\n    }\n  }\n}\n\ndeclare module '@interactjs/core/scope' {\n  interface SignalArgs {\n    'interactions:before-action-inertiastart': Omit<DoPhaseArg<ActionName, 'inertiastart'>, 'iEvent'>\n    'interactions:action-inertiastart': DoPhaseArg<ActionName, 'inertiastart'>\n    'interactions:after-action-inertiastart': DoPhaseArg<ActionName, 'inertiastart'>\n    'interactions:before-action-resume': Omit<DoPhaseArg<ActionName, 'resume'>, 'iEvent'>\n    'interactions:action-resume': DoPhaseArg<ActionName, 'resume'>\n    'interactions:after-action-resume': DoPhaseArg<ActionName, 'resume'>\n  }\n}\n\nfunction install(scope: Scope) {\n  const { defaults } = scope\n\n  scope.usePlugin(offset)\n  scope.usePlugin(modifiers.default)\n  scope.actions.phases.inertiastart = true\n  scope.actions.phases.resume = true\n\n  defaults.perAction.inertia = {\n    enabled: false,\n    resistance: 10, // the lambda in exponential decay\n    minSpeed: 100, // target speed must be above this for inertia to start\n    endSpeed: 10, // the speed at which inertia is slow enough to stop\n    allowResume: true, // allow resuming an action in inertia phase\n    smoothEndDuration: 300, // animate to snap/restrict endOnly if there's no inertia\n  }\n}\n\nexport class InertiaState {\n  active = false\n  isModified = false\n  smoothEnd = false\n  allowResume = false\n\n  modification!: Modification\n  modifierCount = 0\n  modifierArg!: ModifierArg\n\n  startCoords!: Point\n  t0 = 0\n  v0 = 0\n\n  te = 0\n  targetOffset!: Point\n  modifiedOffset!: Point\n  currentOffset!: Point\n\n  lambda_v0? = 0 // eslint-disable-line camelcase\n  one_ve_v0? = 0 // eslint-disable-line camelcase\n  timeout!: number\n  readonly interaction: Interaction\n\n  constructor(interaction: Interaction) {\n    this.interaction = interaction\n  }\n\n  start(event: PointerEventType) {\n    const { interaction } = this\n    const options = getOptions(interaction)\n\n    if (!options || !options.enabled) {\n      return false\n    }\n\n    const { client: velocityClient } = interaction.coords.velocity\n    const pointerSpeed = hypot(velocityClient.x, velocityClient.y)\n    const modification = this.modification || (this.modification = new Modification(interaction))\n\n    modification.copyFrom(interaction.modification)\n\n    this.t0 = interaction._now()\n    this.allowResume = options.allowResume\n    this.v0 = pointerSpeed\n    this.currentOffset = { x: 0, y: 0 }\n    this.startCoords = interaction.coords.cur.page\n\n    this.modifierArg = modification.fillArg({\n      pageCoords: this.startCoords,\n      preEnd: true,\n      phase: 'inertiastart',\n    })\n\n    const thrown =\n      this.t0 - interaction.coords.cur.timeStamp < 50 &&\n      pointerSpeed > options.minSpeed &&\n      pointerSpeed > options.endSpeed\n\n    if (thrown) {\n      this.startInertia()\n    } else {\n      modification.result = modification.setAll(this.modifierArg)\n\n      if (!modification.result.changed) {\n        return false\n      }\n\n      this.startSmoothEnd()\n    }\n\n    // force modification change\n    interaction.modification.result.rect = null\n\n    // bring inertiastart event to the target coords\n    interaction.offsetBy(this.targetOffset)\n    interaction._doPhase({\n      interaction,\n      event,\n      phase: 'inertiastart',\n    })\n    interaction.offsetBy({ x: -this.targetOffset.x, y: -this.targetOffset.y })\n    // force modification change\n    interaction.modification.result.rect = null\n\n    this.active = true\n    interaction.simulation = this\n\n    return true\n  }\n\n  startInertia() {\n    const startVelocity = this.interaction.coords.velocity.client\n    const options = getOptions(this.interaction)\n    const lambda = options.resistance\n    const inertiaDur = -Math.log(options.endSpeed / this.v0) / lambda\n\n    this.targetOffset = {\n      x: (startVelocity.x - inertiaDur) / lambda,\n      y: (startVelocity.y - inertiaDur) / lambda,\n    }\n\n    this.te = inertiaDur\n    this.lambda_v0 = lambda / this.v0\n    this.one_ve_v0 = 1 - options.endSpeed / this.v0\n\n    const { modification, modifierArg } = this\n\n    modifierArg.pageCoords = {\n      x: this.startCoords.x + this.targetOffset.x,\n      y: this.startCoords.y + this.targetOffset.y,\n    }\n\n    modification.result = modification.setAll(modifierArg)\n\n    if (modification.result.changed) {\n      this.isModified = true\n      this.modifiedOffset = {\n        x: this.targetOffset.x + modification.result.delta.x,\n        y: this.targetOffset.y + modification.result.delta.y,\n      }\n    }\n\n    this.onNextFrame(() => this.inertiaTick())\n  }\n\n  startSmoothEnd() {\n    this.smoothEnd = true\n    this.isModified = true\n    this.targetOffset = {\n      x: this.modification.result.delta.x,\n      y: this.modification.result.delta.y,\n    }\n\n    this.onNextFrame(() => this.smoothEndTick())\n  }\n\n  onNextFrame(tickFn: () => void) {\n    this.timeout = raf.request(() => {\n      if (this.active) {\n        tickFn()\n      }\n    })\n  }\n\n  inertiaTick() {\n    const { interaction } = this\n    const options = getOptions(interaction)\n    const lambda = options.resistance\n    const t = (interaction._now() - this.t0) / 1000\n\n    if (t < this.te) {\n      const progress = 1 - (Math.exp(-lambda * t) - this.lambda_v0) / this.one_ve_v0\n      let newOffset: Point\n\n      if (this.isModified) {\n        newOffset = getQuadraticCurvePoint(\n          0,\n          0,\n          this.targetOffset.x,\n          this.targetOffset.y,\n          this.modifiedOffset.x,\n          this.modifiedOffset.y,\n          progress,\n        )\n      } else {\n        newOffset = {\n          x: this.targetOffset.x * progress,\n          y: this.targetOffset.y * progress,\n        }\n      }\n\n      const delta = { x: newOffset.x - this.currentOffset.x, y: newOffset.y - this.currentOffset.y }\n\n      this.currentOffset.x += delta.x\n      this.currentOffset.y += delta.y\n\n      interaction.offsetBy(delta)\n      interaction.move()\n\n      this.onNextFrame(() => this.inertiaTick())\n    } else {\n      interaction.offsetBy({\n        x: this.modifiedOffset.x - this.currentOffset.x,\n        y: this.modifiedOffset.y - this.currentOffset.y,\n      })\n\n      this.end()\n    }\n  }\n\n  smoothEndTick() {\n    const { interaction } = this\n    const t = interaction._now() - this.t0\n    const { smoothEndDuration: duration } = getOptions(interaction)\n\n    if (t < duration) {\n      const newOffset = {\n        x: easeOutQuad(t, 0, this.targetOffset.x, duration),\n        y: easeOutQuad(t, 0, this.targetOffset.y, duration),\n      }\n      const delta = {\n        x: newOffset.x - this.currentOffset.x,\n        y: newOffset.y - this.currentOffset.y,\n      }\n\n      this.currentOffset.x += delta.x\n      this.currentOffset.y += delta.y\n\n      interaction.offsetBy(delta)\n      interaction.move({ skipModifiers: this.modifierCount })\n\n      this.onNextFrame(() => this.smoothEndTick())\n    } else {\n      interaction.offsetBy({\n        x: this.targetOffset.x - this.currentOffset.x,\n        y: this.targetOffset.y - this.currentOffset.y,\n      })\n\n      this.end()\n    }\n  }\n\n  resume({ pointer, event, eventTarget }: SignalArgs['interactions:down']) {\n    const { interaction } = this\n\n    // undo inertia changes to interaction coords\n    interaction.offsetBy({\n      x: -this.currentOffset.x,\n      y: -this.currentOffset.y,\n    })\n\n    // update pointer at pointer down position\n    interaction.updatePointer(pointer, event, eventTarget, true)\n\n    // fire resume signals and event\n    interaction._doPhase({\n      interaction,\n      event,\n      phase: 'resume',\n    })\n    copyCoords(interaction.coords.prev, interaction.coords.cur)\n\n    this.stop()\n  }\n\n  end() {\n    this.interaction.move()\n    this.interaction.end()\n    this.stop()\n  }\n\n  stop() {\n    this.active = this.smoothEnd = false\n    this.interaction.simulation = null\n    raf.cancel(this.timeout)\n  }\n}\n\nfunction start({ interaction, event }: DoPhaseArg<ActionName, 'end'>) {\n  if (!interaction._interacting || interaction.simulation) {\n    return null\n  }\n\n  const started = interaction.inertia.start(event)\n\n  // prevent action end if inertia or smoothEnd\n  return started ? false : null\n}\n\n// Check if the down event hits the current inertia target\n// control should be return to the user\nfunction resume(arg: SignalArgs['interactions:down']) {\n  const { interaction, eventTarget } = arg\n  const state = interaction.inertia\n\n  if (!state.active) return\n\n  let element = eventTarget as Node\n\n  // climb up the DOM tree from the event target\n  while (is.element(element)) {\n    // if interaction element is the current inertia target element\n    if (element === interaction.element) {\n      state.resume(arg)\n      break\n    }\n\n    element = dom.parentNode(element)\n  }\n}\n\nfunction stop({ interaction }: { interaction: Interaction }) {\n  const state = interaction.inertia\n\n  if (state.active) {\n    state.stop()\n  }\n}\n\nfunction getOptions({ interactable, prepared }: Interaction) {\n  return interactable && interactable.options && prepared.name && interactable.options[prepared.name].inertia\n}\n\nconst inertia: Plugin = {\n  id: 'inertia',\n  before: ['modifiers', 'actions'],\n  install,\n  listeners: {\n    'interactions:new': ({ interaction }) => {\n      interaction.inertia = new InertiaState(interaction)\n    },\n\n    'interactions:before-action-end': start,\n    'interactions:down': resume,\n    'interactions:stop': stop,\n\n    'interactions:before-action-resume': (arg) => {\n      const { modification } = arg.interaction\n\n      modification.stop(arg)\n      modification.start(arg, arg.interaction.coords.cur.page)\n      modification.applyToInteraction(arg)\n    },\n\n    'interactions:before-action-inertiastart': (arg) => arg.interaction.modification.setAndApply(arg),\n    'interactions:action-resume': modifiers.addEventModifiers,\n    'interactions:action-inertiastart': modifiers.addEventModifiers,\n    'interactions:after-action-inertiastart': (arg) =>\n      arg.interaction.modification.restoreInteractionCoords(arg),\n    'interactions:after-action-resume': (arg) => arg.interaction.modification.restoreInteractionCoords(arg),\n  },\n}\n\n// http://stackoverflow.com/a/5634528/2280888\nfunction _getQBezierValue(t: number, p1: number, p2: number, p3: number) {\n  const iT = 1 - t\n  return iT * iT * p1 + 2 * iT * t * p2 + t * t * p3\n}\n\nfunction getQuadraticCurvePoint(\n  startX: number,\n  startY: number,\n  cpX: number,\n  cpY: number,\n  endX: number,\n  endY: number,\n  position: number,\n) {\n  return {\n    x: _getQBezierValue(position, startX, cpX, endX),\n    y: _getQBezierValue(position, startY, cpY, endY),\n  }\n}\n\n// http://gizma.com/easing/\nfunction easeOutQuad(t: number, b: number, c: number, d: number) {\n  t /= d\n  return -c * t * (t - 2) + b\n}\n\nexport default inertia\n", "import * as arr from '@interactjs/utils/arr'\nimport extend from '@interactjs/utils/extend'\nimport type { NormalizedListeners } from '@interactjs/utils/normalizeListeners'\nimport normalize from '@interactjs/utils/normalizeListeners'\n\nimport type { Listener, ListenersArg, Rect } from '@interactjs/core/types'\n\nfunction fireUntilImmediateStopped(event: any, listeners: Listener[]) {\n  for (const listener of listeners) {\n    if (event.immediatePropagationStopped) {\n      break\n    }\n\n    listener(event)\n  }\n}\n\nexport class Eventable {\n  options: any\n  types: NormalizedListeners = {}\n  propagationStopped = false\n  immediatePropagationStopped = false\n  global: any\n\n  constructor(options?: { [index: string]: any }) {\n    this.options = extend({}, options || {})\n  }\n\n  fire<T extends { type: string; propagationStopped?: boolean }>(event: T) {\n    let listeners: Listener[]\n    const global = this.global\n\n    // Interactable#on() listeners\n    // tslint:disable no-conditional-assignment\n    if ((listeners = this.types[event.type])) {\n      fireUntilImmediateStopped(event, listeners)\n    }\n\n    // interact.on() listeners\n    if (!event.propagationStopped && global && (listeners = global[event.type])) {\n      fireUntilImmediateStopped(event, listeners)\n    }\n  }\n\n  on(type: string, listener: ListenersArg) {\n    const listeners = normalize(type, listener)\n\n    for (type in listeners) {\n      this.types[type] = arr.merge(this.types[type] || [], listeners[type])\n    }\n  }\n\n  off(type: string, listener: ListenersArg) {\n    const listeners = normalize(type, listener)\n\n    for (type in listeners) {\n      const eventList = this.types[type]\n\n      if (!eventList || !eventList.length) {\n        continue\n      }\n\n      for (const subListener of listeners[type]) {\n        const index = eventList.indexOf(subListener)\n\n        if (index !== -1) {\n          eventList.splice(index, 1)\n        }\n      }\n    }\n  }\n\n  getRect(_element: Element): Rect {\n    return null\n  }\n}\n", "import * as arr from '@interactjs/utils/arr'\nimport * as domUtils from '@interactjs/utils/domUtils'\nimport is from '@interactjs/utils/is'\nimport pExtend from '@interactjs/utils/pointerExtend'\nimport * as pointerUtils from '@interactjs/utils/pointerUtils'\n\nimport type { Scope } from '@interactjs/core/scope'\nimport type { Element } from '@interactjs/core/types'\n\nimport type { NativeEventTarget } from './NativeTypes'\n\ndeclare module '@interactjs/core/scope' {\n  interface Scope {\n    events: ReturnType<typeof install>\n  }\n}\n\ninterface EventOptions {\n  capture: boolean\n  passive: boolean\n}\n\ntype PartialEventTarget = Partial<NativeEventTarget>\n\ntype ListenerEntry = { func: (event: Event | FakeEvent) => any; options: EventOptions }\n\nfunction install(scope: Scope) {\n  const targets: Array<{\n    eventTarget: PartialEventTarget\n    events: { [type: string]: ListenerEntry[] }\n  }> = []\n\n  const delegatedEvents: {\n    [type: string]: Array<{\n      selector: string\n      context: Node\n      listeners: ListenerEntry[]\n    }>\n  } = {}\n  const documents: Document[] = []\n\n  const eventsMethods = {\n    add,\n    remove,\n\n    addDelegate,\n    removeDelegate,\n\n    delegateListener,\n    delegateUseCapture,\n    delegatedEvents,\n    documents,\n\n    targets,\n\n    supportsOptions: false,\n    supportsPassive: false,\n  }\n\n  // check if browser supports passive events and options arg\n  scope.document?.createElement('div').addEventListener('test', null, {\n    get capture() {\n      return (eventsMethods.supportsOptions = true)\n    },\n    get passive() {\n      return (eventsMethods.supportsPassive = true)\n    },\n  })\n\n  scope.events = eventsMethods\n\n  function add(\n    eventTarget: PartialEventTarget,\n    type: string,\n    listener: ListenerEntry['func'],\n    optionalArg?: boolean | EventOptions,\n  ) {\n    if (!eventTarget.addEventListener) return\n\n    const options = getOptions(optionalArg)\n    let target = arr.find(targets, (t) => t.eventTarget === eventTarget)\n\n    if (!target) {\n      target = {\n        eventTarget,\n        events: {},\n      }\n\n      targets.push(target)\n    }\n\n    if (!target.events[type]) {\n      target.events[type] = []\n    }\n\n    if (!arr.find(target.events[type], (l) => l.func === listener && optionsMatch(l.options, options))) {\n      eventTarget.addEventListener(\n        type,\n        listener as any,\n        eventsMethods.supportsOptions ? options : options.capture,\n      )\n      target.events[type].push({ func: listener, options })\n    }\n  }\n\n  function remove(\n    eventTarget: PartialEventTarget,\n    type: string,\n    listener?: 'all' | ListenerEntry['func'],\n    optionalArg?: boolean | EventOptions,\n  ) {\n    if (!eventTarget.addEventListener || !eventTarget.removeEventListener) return\n\n    const targetIndex = arr.findIndex(targets, (t) => t.eventTarget === eventTarget)\n    const target = targets[targetIndex]\n\n    if (!target || !target.events) {\n      return\n    }\n\n    if (type === 'all') {\n      for (type in target.events) {\n        if (target.events.hasOwnProperty(type)) {\n          remove(eventTarget, type, 'all')\n        }\n      }\n      return\n    }\n\n    let typeIsEmpty = false\n    const typeListeners = target.events[type]\n\n    if (typeListeners) {\n      if (listener === 'all') {\n        for (let i = typeListeners.length - 1; i >= 0; i--) {\n          const entry = typeListeners[i]\n          remove(eventTarget, type, entry.func, entry.options)\n        }\n        return\n      } else {\n        const options = getOptions(optionalArg)\n\n        for (let i = 0; i < typeListeners.length; i++) {\n          const entry = typeListeners[i]\n          if (entry.func === listener && optionsMatch(entry.options, options)) {\n            eventTarget.removeEventListener(\n              type,\n              listener as any,\n              eventsMethods.supportsOptions ? options : options.capture,\n            )\n            typeListeners.splice(i, 1)\n\n            if (typeListeners.length === 0) {\n              delete target.events[type]\n              typeIsEmpty = true\n            }\n\n            break\n          }\n        }\n      }\n    }\n\n    if (typeIsEmpty && !Object.keys(target.events).length) {\n      targets.splice(targetIndex, 1)\n    }\n  }\n\n  function addDelegate(\n    selector: string,\n    context: Node,\n    type: string,\n    listener: ListenerEntry['func'],\n    optionalArg?: any,\n  ) {\n    const options = getOptions(optionalArg)\n    if (!delegatedEvents[type]) {\n      delegatedEvents[type] = []\n\n      // add delegate listener functions\n      for (const doc of documents) {\n        add(doc, type, delegateListener)\n        add(doc, type, delegateUseCapture, true)\n      }\n    }\n\n    const delegates = delegatedEvents[type]\n    let delegate = arr.find(delegates, (d) => d.selector === selector && d.context === context)\n\n    if (!delegate) {\n      delegate = { selector, context, listeners: [] }\n      delegates.push(delegate)\n    }\n\n    delegate.listeners.push({ func: listener, options })\n  }\n\n  function removeDelegate(\n    selector: string,\n    context: Document | Element,\n    type: string,\n    listener?: ListenerEntry['func'],\n    optionalArg?: any,\n  ) {\n    const options = getOptions(optionalArg)\n    const delegates = delegatedEvents[type]\n    let matchFound = false\n    let index: number\n\n    if (!delegates) return\n\n    // count from last index of delegated to 0\n    for (index = delegates.length - 1; index >= 0; index--) {\n      const cur = delegates[index]\n      // look for matching selector and context Node\n      if (cur.selector === selector && cur.context === context) {\n        const { listeners } = cur\n\n        // each item of the listeners array is an array: [function, capture, passive]\n        for (let i = listeners.length - 1; i >= 0; i--) {\n          const entry = listeners[i]\n\n          // check if the listener functions and capture and passive flags match\n          if (entry.func === listener && optionsMatch(entry.options, options)) {\n            // remove the listener from the array of listeners\n            listeners.splice(i, 1)\n\n            // if all listeners for this target have been removed\n            // remove the target from the delegates array\n            if (!listeners.length) {\n              delegates.splice(index, 1)\n\n              // remove delegate function from context\n              remove(context, type, delegateListener)\n              remove(context, type, delegateUseCapture, true)\n            }\n\n            // only remove one listener\n            matchFound = true\n            break\n          }\n        }\n\n        if (matchFound) {\n          break\n        }\n      }\n    }\n  }\n\n  // bound to the interactable context when a DOM event\n  // listener is added to a selector interactable\n  function delegateListener(event: Event | FakeEvent, optionalArg?: any) {\n    const options = getOptions(optionalArg)\n    const fakeEvent = new FakeEvent(event as Event)\n    const delegates = delegatedEvents[event.type]\n    const [eventTarget] = pointerUtils.getEventTargets(event as Event)\n    let element: Node = eventTarget\n\n    // climb up document tree looking for selector matches\n    while (is.element(element)) {\n      for (let i = 0; i < delegates.length; i++) {\n        const cur = delegates[i]\n        const { selector, context } = cur\n\n        if (\n          domUtils.matchesSelector(element, selector) &&\n          domUtils.nodeContains(context, eventTarget) &&\n          domUtils.nodeContains(context, element)\n        ) {\n          const { listeners } = cur\n\n          fakeEvent.currentTarget = element\n\n          for (const entry of listeners) {\n            if (optionsMatch(entry.options, options)) {\n              entry.func(fakeEvent)\n            }\n          }\n        }\n      }\n\n      element = domUtils.parentNode(element)\n    }\n  }\n\n  function delegateUseCapture(this: Element, event: Event | FakeEvent) {\n    return delegateListener.call(this, event, true)\n  }\n\n  // for type inferrence\n  return eventsMethods\n}\n\nclass FakeEvent implements Partial<Event> {\n  currentTarget: Node\n  originalEvent: Event\n  type: string\n\n  constructor(originalEvent: Event) {\n    this.originalEvent = originalEvent\n    // duplicate the event so that currentTarget can be changed\n    pExtend(this, originalEvent)\n  }\n\n  preventOriginalDefault() {\n    this.originalEvent.preventDefault()\n  }\n\n  stopPropagation() {\n    this.originalEvent.stopPropagation()\n  }\n\n  stopImmediatePropagation() {\n    this.originalEvent.stopImmediatePropagation()\n  }\n}\n\nfunction getOptions(param: { [index: string]: any } | boolean): { capture: boolean; passive: boolean } {\n  if (!is.object(param)) {\n    return { capture: !!param, passive: false }\n  }\n\n  return {\n    capture: !!param.capture,\n    passive: !!param.passive,\n  }\n}\n\nfunction optionsMatch(a: Partial<EventOptions> | boolean, b: Partial<EventOptions>) {\n  if (a === b) return true\n\n  if (typeof a === 'boolean') return !!b.capture === a && !!b.passive === false\n\n  return !!a.capture === !!b.capture && !!a.passive === !!b.passive\n}\n\nexport default {\n  id: 'events',\n  install,\n}\n", "import * as dom from '@interactjs/utils/domUtils'\n\nimport type Interaction from '@interactjs/core/Interaction'\nimport type { Scope } from '@interactjs/core/scope'\nimport type { PointerType } from '@interactjs/core/types'\n\nexport interface SearchDetails {\n  pointer: PointerType\n  pointerId: number\n  pointerType: string\n  eventType: string\n  eventTarget: EventTarget\n  curEventTarget: EventTarget\n  scope: Scope\n}\n\nconst finder = {\n  methodOrder: ['simulationResume', 'mouseOrPen', 'hasPointer', 'idle'] as const,\n\n  search(details: SearchDetails) {\n    for (const method of finder.methodOrder) {\n      const interaction = finder[method](details)\n\n      if (interaction) {\n        return interaction\n      }\n    }\n\n    return null\n  },\n\n  // try to resume simulation with a new pointer\n  simulationResume({ pointerType, eventType, eventTarget, scope }: SearchDetails) {\n    if (!/down|start/i.test(eventType)) {\n      return null\n    }\n\n    for (const interaction of scope.interactions.list) {\n      let element = eventTarget as Node\n\n      if (\n        interaction.simulation &&\n        interaction.simulation.allowResume &&\n        interaction.pointerType === pointerType\n      ) {\n        while (element) {\n          // if the element is the interaction element\n          if (element === interaction.element) {\n            return interaction\n          }\n          element = dom.parentNode(element)\n        }\n      }\n    }\n\n    return null\n  },\n\n  // if it's a mouse or pen interaction\n  mouseOrPen({ pointerId, pointerType, eventType, scope }: SearchDetails) {\n    if (pointerType !== 'mouse' && pointerType !== 'pen') {\n      return null\n    }\n\n    let firstNonActive\n\n    for (const interaction of scope.interactions.list) {\n      if (interaction.pointerType === pointerType) {\n        // if it's a down event, skip interactions with running simulations\n        if (interaction.simulation && !hasPointerId(interaction, pointerId)) {\n          continue\n        }\n\n        // if the interaction is active, return it immediately\n        if (interaction.interacting()) {\n          return interaction\n        }\n        // otherwise save it and look for another active interaction\n        else if (!firstNonActive) {\n          firstNonActive = interaction\n        }\n      }\n    }\n\n    // if no active mouse interaction was found use the first inactive mouse\n    // interaction\n    if (firstNonActive) {\n      return firstNonActive\n    }\n\n    // find any mouse or pen interaction.\n    // ignore the interaction if the eventType is a *down, and a simulation\n    // is active\n    for (const interaction of scope.interactions.list) {\n      if (interaction.pointerType === pointerType && !(/down/i.test(eventType) && interaction.simulation)) {\n        return interaction\n      }\n    }\n\n    return null\n  },\n\n  // get interaction that has this pointer\n  hasPointer({ pointerId, scope }: SearchDetails) {\n    for (const interaction of scope.interactions.list) {\n      if (hasPointerId(interaction, pointerId)) {\n        return interaction\n      }\n    }\n\n    return null\n  },\n\n  // get first idle interaction with a matching pointerType\n  idle({ pointerType, scope }: SearchDetails) {\n    for (const interaction of scope.interactions.list) {\n      // if there's already a pointer held down\n      if (interaction.pointers.length === 1) {\n        const target = interaction.interactable\n        // don't add this pointer if there is a target interactable and it\n        // isn't gesturable\n        if (target && !(target.options.gesture && target.options.gesture.enabled)) {\n          continue\n        }\n      }\n      // maximum of 2 pointers per interaction\n      else if (interaction.pointers.length >= 2) {\n        continue\n      }\n\n      if (!interaction.interacting() && pointerType === interaction.pointerType) {\n        return interaction\n      }\n    }\n\n    return null\n  },\n}\n\nfunction hasPointerId(interaction: Interaction, pointerId: number) {\n  return interaction.pointers.some(({ id }) => id === pointerId)\n}\n\nexport default finder\n", "import browser from '@interactjs/utils/browser'\nimport domObjects from '@interactjs/utils/domObjects'\nimport { nodeContains } from '@interactjs/utils/domUtils'\nimport * as pointerUtils from '@interactjs/utils/pointerUtils'\n\nimport type { Scope, SignalArgs, Plugin } from '@interactjs/core/scope'\nimport type { ActionName, Listener } from '@interactjs/core/types'\n\n/* eslint-disable import/no-duplicates -- for typescript module augmentations */\nimport './interactablePreventDefault'\nimport interactablePreventDefault from './interactablePreventDefault'\nimport InteractionBase from './Interaction'\n/* eslint-enable import/no-duplicates */\nimport type { SearchDetails } from './interactionFinder'\nimport finder from './interactionFinder'\n\ndeclare module '@interactjs/core/scope' {\n  interface Scope {\n    Interaction: typeof InteractionBase\n    interactions: {\n      new: <T extends ActionName>(options: any) => InteractionBase<T>\n      list: Array<InteractionBase<ActionName>>\n      listeners: { [type: string]: Listener }\n      docEvents: Array<{ type: string; listener: Listener }>\n      pointerMoveTolerance: number\n    }\n    prevTouchTime: number\n  }\n\n  interface SignalArgs {\n    'interactions:find': {\n      interaction: InteractionBase\n      searchDetails: SearchDetails\n    }\n  }\n}\n\nconst methodNames = [\n  'pointerDown',\n  'pointerMove',\n  'pointerUp',\n  'updatePointer',\n  'removePointer',\n  'windowBlur',\n]\n\nfunction install(scope: Scope) {\n  const listeners = {} as any\n\n  for (const method of methodNames) {\n    listeners[method] = doOnInteractions(method, scope)\n  }\n\n  const pEventTypes = browser.pEventTypes\n  let docEvents: typeof scope.interactions.docEvents\n\n  if (domObjects.PointerEvent) {\n    docEvents = [\n      { type: pEventTypes.down, listener: releasePointersOnRemovedEls },\n      { type: pEventTypes.down, listener: listeners.pointerDown },\n      { type: pEventTypes.move, listener: listeners.pointerMove },\n      { type: pEventTypes.up, listener: listeners.pointerUp },\n      { type: pEventTypes.cancel, listener: listeners.pointerUp },\n    ]\n  } else {\n    docEvents = [\n      { type: 'mousedown', listener: listeners.pointerDown },\n      { type: 'mousemove', listener: listeners.pointerMove },\n      { type: 'mouseup', listener: listeners.pointerUp },\n\n      { type: 'touchstart', listener: releasePointersOnRemovedEls },\n      { type: 'touchstart', listener: listeners.pointerDown },\n      { type: 'touchmove', listener: listeners.pointerMove },\n      { type: 'touchend', listener: listeners.pointerUp },\n      { type: 'touchcancel', listener: listeners.pointerUp },\n    ]\n  }\n\n  docEvents.push({\n    type: 'blur',\n    listener(event) {\n      for (const interaction of scope.interactions.list) {\n        interaction.documentBlur(event)\n      }\n    },\n  })\n\n  // for ignoring browser's simulated mouse events\n  scope.prevTouchTime = 0\n\n  scope.Interaction = class<T extends ActionName> extends InteractionBase<T> {\n    get pointerMoveTolerance() {\n      return scope.interactions.pointerMoveTolerance\n    }\n\n    set pointerMoveTolerance(value) {\n      scope.interactions.pointerMoveTolerance = value\n    }\n\n    _now() {\n      return scope.now()\n    }\n  }\n\n  scope.interactions = {\n    // all active and idle interactions\n    list: [],\n    new<T extends ActionName>(options: { pointerType?: string; scopeFire?: Scope['fire'] }) {\n      options.scopeFire = (name, arg) => scope.fire(name, arg)\n\n      const interaction = new scope.Interaction<T>(options as Required<typeof options>)\n\n      scope.interactions.list.push(interaction)\n      return interaction\n    },\n    listeners,\n    docEvents,\n    pointerMoveTolerance: 1,\n  }\n\n  function releasePointersOnRemovedEls() {\n    // for all inactive touch interactions with pointers down\n    for (const interaction of scope.interactions.list) {\n      if (!interaction.pointerIsDown || interaction.pointerType !== 'touch' || interaction._interacting) {\n        continue\n      }\n\n      // if a pointer is down on an element that is no longer in the DOM tree\n      for (const pointer of interaction.pointers) {\n        if (!scope.documents.some(({ doc }) => nodeContains(doc, pointer.downTarget))) {\n          // remove the pointer from the interaction\n          interaction.removePointer(pointer.pointer, pointer.event)\n        }\n      }\n    }\n  }\n\n  scope.usePlugin(interactablePreventDefault)\n}\n\nfunction doOnInteractions(method: string, scope: Scope) {\n  return function (event: Event) {\n    const interactions = scope.interactions.list\n\n    const pointerType = pointerUtils.getPointerType(event)\n    const [eventTarget, curEventTarget] = pointerUtils.getEventTargets(event)\n    const matches: any[] = [] // [ [pointer, interaction], ...]\n\n    if (/^touch/.test(event.type)) {\n      scope.prevTouchTime = scope.now()\n\n      // @ts-expect-error\n      for (const changedTouch of event.changedTouches) {\n        const pointer = changedTouch\n        const pointerId = pointerUtils.getPointerId(pointer)\n        const searchDetails: SearchDetails = {\n          pointer,\n          pointerId,\n          pointerType,\n          eventType: event.type,\n          eventTarget,\n          curEventTarget,\n          scope,\n        }\n        const interaction = getInteraction(searchDetails)\n\n        matches.push([\n          searchDetails.pointer,\n          searchDetails.eventTarget,\n          searchDetails.curEventTarget,\n          interaction,\n        ])\n      }\n    } else {\n      let invalidPointer = false\n\n      if (!browser.supportsPointerEvent && /mouse/.test(event.type)) {\n        // ignore mouse events while touch interactions are active\n        for (let i = 0; i < interactions.length && !invalidPointer; i++) {\n          invalidPointer = interactions[i].pointerType !== 'mouse' && interactions[i].pointerIsDown\n        }\n\n        // try to ignore mouse events that are simulated by the browser\n        // after a touch event\n        invalidPointer =\n          invalidPointer ||\n          scope.now() - scope.prevTouchTime < 500 ||\n          // on iOS and Firefox Mobile, MouseEvent.timeStamp is zero if simulated\n          event.timeStamp === 0\n      }\n\n      if (!invalidPointer) {\n        const searchDetails = {\n          pointer: event as PointerEvent,\n          pointerId: pointerUtils.getPointerId(event as PointerEvent),\n          pointerType,\n          eventType: event.type,\n          curEventTarget,\n          eventTarget,\n          scope,\n        }\n\n        const interaction = getInteraction(searchDetails)\n\n        matches.push([\n          searchDetails.pointer,\n          searchDetails.eventTarget,\n          searchDetails.curEventTarget,\n          interaction,\n        ])\n      }\n    }\n\n    // eslint-disable-next-line no-shadow\n    for (const [pointer, eventTarget, curEventTarget, interaction] of matches) {\n      interaction[method](pointer, event, eventTarget, curEventTarget)\n    }\n  }\n}\n\nfunction getInteraction(searchDetails: SearchDetails) {\n  const { pointerType, scope } = searchDetails\n\n  const foundInteraction = finder.search(searchDetails)\n  const signalArg = { interaction: foundInteraction, searchDetails }\n\n  scope.fire('interactions:find', signalArg)\n\n  return signalArg.interaction || scope.interactions.new({ pointerType })\n}\n\nfunction onDocSignal<T extends 'scope:add-document' | 'scope:remove-document'>(\n  { doc, scope, options }: SignalArgs[T],\n  eventMethodName: 'add' | 'remove',\n) {\n  const {\n    interactions: { docEvents },\n    events,\n  } = scope\n  const eventMethod = events[eventMethodName]\n\n  if (scope.browser.isIOS && !options.events) {\n    options.events = { passive: false }\n  }\n\n  // delegate event listener\n  for (const eventType in events.delegatedEvents) {\n    eventMethod(doc, eventType, events.delegateListener)\n    eventMethod(doc, eventType, events.delegateUseCapture, true)\n  }\n\n  const eventOptions = options && options.events\n\n  for (const { type, listener } of docEvents) {\n    eventMethod(doc, type, listener, eventOptions)\n  }\n}\n\nconst interactions: Plugin = {\n  id: 'core/interactions',\n  install,\n  listeners: {\n    'scope:add-document': (arg) => onDocSignal(arg, 'add'),\n    'scope:remove-document': (arg) => onDocSignal(arg, 'remove'),\n    'interactable:unset': ({ interactable }, scope) => {\n      // Stop and destroy related interactions when an Interactable is unset\n      for (let i = scope.interactions.list.length - 1; i >= 0; i--) {\n        const interaction = scope.interactions.list[i]\n\n        if (interaction.interactable !== interactable) {\n          continue\n        }\n\n        interaction.stop()\n        scope.fire('interactions:destroy', { interaction })\n        interaction.destroy()\n\n        if (scope.interactions.list.length > 2) {\n          scope.interactions.list.splice(i, 1)\n        }\n      }\n    },\n  },\n  onDocSignal,\n  doOnInteractions,\n  methodNames,\n}\n\nexport default interactions\n", "/* eslint-disable no-dupe-class-members */\nimport * as arr from '@interactjs/utils/arr'\nimport browser from '@interactjs/utils/browser'\nimport clone from '@interactjs/utils/clone'\nimport { getElementRect, matchesUpTo, nodeContains, trySelector } from '@interactjs/utils/domUtils'\nimport extend from '@interactjs/utils/extend'\nimport is from '@interactjs/utils/is'\nimport isNonNativeEvent from '@interactjs/utils/isNonNativeEvent'\nimport normalizeListeners from '@interactjs/utils/normalizeListeners'\nimport { getWindow } from '@interactjs/utils/window'\n\nimport type { Scope } from '@interactjs/core/scope'\nimport type {\n  ActionMap,\n  ActionMethod,\n  ActionName,\n  Actions,\n  Context,\n  Element,\n  EventTypes,\n  Listeners,\n  ListenersArg,\n  OrBoolean,\n  Target,\n} from '@interactjs/core/types'\n\nimport { Eventable } from './Eventable'\nimport type { ActionDefaults, Defaults, OptionsArg, PerActionDefaults, Options } from './options'\n\ntype IgnoreValue = string | Element | boolean\ntype DeltaSource = 'page' | 'client'\n\nconst enum OnOffMethod {\n  On,\n  Off,\n}\n\n/**\n * ```ts\n * const interactable = interact('.cards')\n *   .draggable({\n *     listeners: { move: event => console.log(event.type, event.pageX, event.pageY) }\n *   })\n *   .resizable({\n *     listeners: { move: event => console.log(event.rect) },\n *     modifiers: [interact.modifiers.restrictEdges({ outer: 'parent' })]\n *   })\n * ```\n */\nexport class Interactable implements Partial<Eventable> {\n  /** @internal */ get _defaults(): Defaults {\n    return {\n      base: {},\n      perAction: {},\n      actions: {} as ActionDefaults,\n    }\n  }\n\n  readonly target: Target\n  /** @internal */ readonly options!: Required<Options>\n  /** @internal */ readonly _actions: Actions\n  /** @internal */ readonly events = new Eventable()\n  /** @internal */ readonly _context: Context\n  /** @internal */ readonly _win: Window\n  /** @internal */ readonly _doc: Document\n  /** @internal */ readonly _scopeEvents: Scope['events']\n\n  constructor(\n    target: Target,\n    options: any,\n    defaultContext: Document | Element,\n    scopeEvents: Scope['events'],\n  ) {\n    this._actions = options.actions\n    this.target = target\n    this._context = options.context || defaultContext\n    this._win = getWindow(trySelector(target) ? this._context : target)\n    this._doc = this._win.document\n    this._scopeEvents = scopeEvents\n\n    this.set(options)\n  }\n\n  setOnEvents(actionName: ActionName, phases: NonNullable<any>) {\n    if (is.func(phases.onstart)) {\n      this.on(`${actionName}start`, phases.onstart)\n    }\n    if (is.func(phases.onmove)) {\n      this.on(`${actionName}move`, phases.onmove)\n    }\n    if (is.func(phases.onend)) {\n      this.on(`${actionName}end`, phases.onend)\n    }\n    if (is.func(phases.oninertiastart)) {\n      this.on(`${actionName}inertiastart`, phases.oninertiastart)\n    }\n\n    return this\n  }\n\n  updatePerActionListeners(actionName: ActionName, prev: Listeners | undefined, cur: Listeners | undefined) {\n    const actionFilter = (this._actions.map[actionName] as { filterEventType?: (type: string) => boolean })\n      ?.filterEventType\n    const filter = (type: string) =>\n      (actionFilter == null || actionFilter(type)) && isNonNativeEvent(type, this._actions)\n\n    if (is.array(prev) || is.object(prev)) {\n      this._onOff(OnOffMethod.Off, actionName, prev, undefined, filter)\n    }\n\n    if (is.array(cur) || is.object(cur)) {\n      this._onOff(OnOffMethod.On, actionName, cur, undefined, filter)\n    }\n  }\n\n  setPerAction(actionName: ActionName, options: OrBoolean<Options>) {\n    const defaults = this._defaults\n\n    // for all the default per-action options\n    for (const optionName_ in options) {\n      const optionName = optionName_ as keyof PerActionDefaults\n      const actionOptions = this.options[actionName]\n      const optionValue: any = options[optionName]\n\n      // remove old event listeners and add new ones\n      if (optionName === 'listeners') {\n        this.updatePerActionListeners(actionName, actionOptions.listeners, optionValue as Listeners)\n      }\n\n      // if the option value is an array\n      if (is.array(optionValue)) {\n        ;(actionOptions[optionName] as any) = arr.from(optionValue)\n      }\n      // if the option value is an object\n      else if (is.plainObject(optionValue)) {\n        // copy the object\n        ;(actionOptions[optionName] as any) = extend(\n          actionOptions[optionName] || ({} as any),\n          clone(optionValue),\n        )\n\n        // set anabled field to true if it exists in the defaults\n        if (\n          is.object(defaults.perAction[optionName]) &&\n          'enabled' in (defaults.perAction[optionName] as any)\n        ) {\n          ;(actionOptions[optionName] as any).enabled = optionValue.enabled !== false\n        }\n      }\n      // if the option value is a boolean and the default is an object\n      else if (is.bool(optionValue) && is.object(defaults.perAction[optionName])) {\n        ;(actionOptions[optionName] as any).enabled = optionValue\n      }\n      // if it's anything else, do a plain assignment\n      else {\n        ;(actionOptions[optionName] as any) = optionValue\n      }\n    }\n  }\n\n  /**\n   * The default function to get an Interactables bounding rect. Can be\n   * overridden using {@link Interactable.rectChecker}.\n   *\n   * @param {Element} [element] The element to measure.\n   * @return {Rect} The object's bounding rectangle.\n   */\n  getRect(element: Element) {\n    element = element || (is.element(this.target) ? this.target : null)\n\n    if (is.string(this.target)) {\n      element = element || this._context.querySelector(this.target)\n    }\n\n    return getElementRect(element)\n  }\n\n  /**\n   * Returns or sets the function used to calculate the interactable's\n   * element's rectangle\n   *\n   * @param {function} [checker] A function which returns this Interactable's\n   * bounding rectangle. See {@link Interactable.getRect}\n   * @return {function | object} The checker function or this Interactable\n   */\n  rectChecker(): (element: Element) => any | null\n  rectChecker(checker: (element: Element) => any): this\n  rectChecker(checker?: (element: Element) => any) {\n    if (is.func(checker)) {\n      this.getRect = (element) => {\n        const rect = extend({}, checker.apply(this, element))\n\n        if (!(('width' in rect) as unknown)) {\n          rect.width = rect.right - rect.left\n          rect.height = rect.bottom - rect.top\n        }\n\n        return rect\n      }\n\n      return this\n    }\n\n    if (checker === null) {\n      delete (this as Partial<typeof this>).getRect\n\n      return this\n    }\n\n    return this.getRect\n  }\n\n  /** @internal */\n  _backCompatOption(optionName: keyof Options, newValue: any) {\n    if (trySelector(newValue) || is.object(newValue)) {\n      ;(this.options[optionName] as any) = newValue\n\n      for (const action in this._actions.map) {\n        ;(this.options[action as keyof ActionMap] as any)[optionName] = newValue\n      }\n\n      return this\n    }\n\n    return this.options[optionName]\n  }\n\n  /**\n   * Gets or sets the origin of the Interactable's element.  The x and y\n   * of the origin will be subtracted from action event coordinates.\n   *\n   * @param {Element | object | string} [origin] An HTML or SVG Element whose\n   * rect will be used, an object eg. { x: 0, y: 0 } or string 'parent', 'self'\n   * or any CSS selector\n   *\n   * @return {object} The current origin or this Interactable\n   */\n  origin(newValue: any) {\n    return this._backCompatOption('origin', newValue)\n  }\n\n  /**\n   * Returns or sets the mouse coordinate types used to calculate the\n   * movement of the pointer.\n   *\n   * @param {string} [newValue] Use 'client' if you will be scrolling while\n   * interacting; Use 'page' if you want autoScroll to work\n   * @return {string | object} The current deltaSource or this Interactable\n   */\n  deltaSource(): DeltaSource\n  deltaSource(newValue: DeltaSource): this\n  deltaSource(newValue?: DeltaSource) {\n    if (newValue === 'page' || newValue === 'client') {\n      this.options.deltaSource = newValue\n\n      return this\n    }\n\n    return this.options.deltaSource\n  }\n\n  /** @internal */\n  getAllElements(): Element[] {\n    const { target } = this\n\n    if (is.string(target)) {\n      return Array.from(this._context.querySelectorAll(target))\n    }\n\n    if (is.func(target) && (target as any).getAllElements) {\n      return (target as any).getAllElements()\n    }\n\n    return is.element(target) ? [target] : []\n  }\n\n  /**\n   * Gets the selector context Node of the Interactable. The default is\n   * `window.document`.\n   *\n   * @return {Node} The context Node of this Interactable\n   */\n  context() {\n    return this._context\n  }\n\n  inContext(element: Document | Node) {\n    return this._context === element.ownerDocument || nodeContains(this._context, element)\n  }\n\n  /** @internal */\n  testIgnoreAllow(\n    this: Interactable,\n    options: { ignoreFrom?: IgnoreValue; allowFrom?: IgnoreValue },\n    targetNode: Node,\n    eventTarget: Node,\n  ) {\n    return (\n      !this.testIgnore(options.ignoreFrom, targetNode, eventTarget) &&\n      this.testAllow(options.allowFrom, targetNode, eventTarget)\n    )\n  }\n\n  /** @internal */\n  testAllow(this: Interactable, allowFrom: IgnoreValue | undefined, targetNode: Node, element: Node) {\n    if (!allowFrom) {\n      return true\n    }\n\n    if (!is.element(element)) {\n      return false\n    }\n\n    if (is.string(allowFrom)) {\n      return matchesUpTo(element, allowFrom, targetNode)\n    } else if (is.element(allowFrom)) {\n      return nodeContains(allowFrom, element)\n    }\n\n    return false\n  }\n\n  /** @internal */\n  testIgnore(this: Interactable, ignoreFrom: IgnoreValue | undefined, targetNode: Node, element: Node) {\n    if (!ignoreFrom || !is.element(element)) {\n      return false\n    }\n\n    if (is.string(ignoreFrom)) {\n      return matchesUpTo(element, ignoreFrom, targetNode)\n    } else if (is.element(ignoreFrom)) {\n      return nodeContains(ignoreFrom, element)\n    }\n\n    return false\n  }\n\n  /**\n   * Calls listeners for the given InteractEvent type bound globally\n   * and directly to this Interactable\n   *\n   * @param {InteractEvent} iEvent The InteractEvent object to be fired on this\n   * Interactable\n   * @return {Interactable} this Interactable\n   */\n  fire<E extends { type: string }>(iEvent: E) {\n    this.events.fire(iEvent)\n\n    return this\n  }\n\n  /** @internal */\n  _onOff(\n    method: OnOffMethod,\n    typeArg: EventTypes,\n    listenerArg?: ListenersArg | null,\n    options?: any,\n    filter?: (type: string) => boolean,\n  ) {\n    if (is.object(typeArg) && !is.array(typeArg)) {\n      options = listenerArg\n      listenerArg = null\n    }\n\n    const listeners = normalizeListeners(typeArg, listenerArg, filter)\n\n    for (let type in listeners) {\n      if (type === 'wheel') {\n        type = browser.wheelEvent\n      }\n\n      for (const listener of listeners[type]) {\n        // if it is an action event type\n        if (isNonNativeEvent(type, this._actions)) {\n          this.events[method === OnOffMethod.On ? 'on' : 'off'](type, listener)\n        }\n        // delegated event\n        else if (is.string(this.target)) {\n          this._scopeEvents[method === OnOffMethod.On ? 'addDelegate' : 'removeDelegate'](\n            this.target,\n            this._context,\n            type,\n            listener,\n            options,\n          )\n        }\n        // remove listener from this Interactable's element\n        else {\n          this._scopeEvents[method === OnOffMethod.On ? 'add' : 'remove'](\n            this.target,\n            type,\n            listener,\n            options,\n          )\n        }\n      }\n    }\n\n    return this\n  }\n\n  /**\n   * Binds a listener for an InteractEvent, pointerEvent or DOM event.\n   *\n   * @param {string | array | object} types The types of events to listen\n   * for\n   * @param {function | array | object} [listener] The event listener function(s)\n   * @param {object | boolean} [options] options object or useCapture flag for\n   * addEventListener\n   * @return {Interactable} This Interactable\n   */\n  on(types: EventTypes, listener?: ListenersArg, options?: any) {\n    return this._onOff(OnOffMethod.On, types, listener, options)\n  }\n\n  /**\n   * Removes an InteractEvent, pointerEvent or DOM event listener.\n   *\n   * @param {string | array | object} types The types of events that were\n   * listened for\n   * @param {function | array | object} [listener] The event listener function(s)\n   * @param {object | boolean} [options] options object or useCapture flag for\n   * removeEventListener\n   * @return {Interactable} This Interactable\n   */\n  off(types: string | string[] | EventTypes, listener?: ListenersArg, options?: any) {\n    return this._onOff(OnOffMethod.Off, types, listener, options)\n  }\n\n  /**\n   * Reset the options of this Interactable\n   *\n   * @param {object} options The new settings to apply\n   * @return {object} This Interactable\n   */\n  set(options: OptionsArg) {\n    const defaults = this._defaults\n\n    if (!is.object(options)) {\n      options = {}\n    }\n\n    ;(this.options as Required<Options>) = clone(defaults.base) as Required<Options>\n\n    for (const actionName_ in this._actions.methodDict) {\n      const actionName = actionName_ as ActionName\n      const methodName = this._actions.methodDict[actionName]\n\n      this.options[actionName] = {}\n      this.setPerAction(actionName, extend(extend({}, defaults.perAction), defaults.actions[actionName]))\n      ;(this[methodName] as ActionMethod<unknown>)(options[actionName])\n    }\n\n    for (const setting in options) {\n      if (setting === 'getRect') {\n        this.rectChecker(options.getRect)\n        continue\n      }\n\n      if (is.func((this as any)[setting])) {\n        ;(this as any)[setting](options[setting as keyof typeof options])\n      }\n    }\n\n    return this\n  }\n\n  /**\n   * Remove this interactable from the list of interactables and remove it's\n   * action capabilities and event listeners\n   */\n  unset() {\n    if (is.string(this.target)) {\n      // remove delegated events\n      for (const type in this._scopeEvents.delegatedEvents) {\n        const delegated = this._scopeEvents.delegatedEvents[type]\n\n        for (let i = delegated.length - 1; i >= 0; i--) {\n          const { selector, context, listeners } = delegated[i]\n\n          if (selector === this.target && context === this._context) {\n            delegated.splice(i, 1)\n          }\n\n          for (let l = listeners.length - 1; l >= 0; l--) {\n            this._scopeEvents.removeDelegate(\n              this.target,\n              this._context,\n              type,\n              listeners[l][0],\n              listeners[l][1],\n            )\n          }\n        }\n      }\n    } else {\n      this._scopeEvents.remove(this.target, 'all')\n    }\n  }\n}\n", "import * as arr from '@interactjs/utils/arr'\nimport * as domUtils from '@interactjs/utils/domUtils'\nimport extend from '@interactjs/utils/extend'\nimport is from '@interactjs/utils/is'\n\nimport type { Interactable } from '@interactjs/core/Interactable'\nimport type { OptionsArg, Options } from '@interactjs/core/options'\nimport type { Scope } from '@interactjs/core/scope'\nimport type { Target } from '@interactjs/core/types'\n\ndeclare module '@interactjs/core/scope' {\n  interface SignalArgs {\n    'interactable:new': {\n      interactable: Interactable\n      target: Target\n      options: OptionsArg\n      win: Window\n    }\n  }\n}\n\nexport class InteractableSet {\n  // all set interactables\n  list: Interactable[] = []\n\n  selectorMap: {\n    [selector: string]: Interactable[]\n  } = {}\n\n  scope: Scope\n\n  constructor(scope: Scope) {\n    this.scope = scope\n    scope.addListeners({\n      'interactable:unset': ({ interactable }) => {\n        const { target } = interactable\n        const interactablesOnTarget: Interactable[] = is.string(target)\n          ? this.selectorMap[target]\n          : (target as any)[this.scope.id]\n\n        const targetIndex = arr.findIndex(interactablesOnTarget, (i) => i === interactable)\n        interactablesOnTarget.splice(targetIndex, 1)\n      },\n    })\n  }\n\n  new(target: Target, options?: any): Interactable {\n    options = extend(options || {}, {\n      actions: this.scope.actions,\n    })\n    const interactable = new this.scope.Interactable(target, options, this.scope.document, this.scope.events)\n\n    this.scope.addDocument(interactable._doc)\n    this.list.push(interactable)\n\n    if (is.string(target)) {\n      if (!this.selectorMap[target]) {\n        this.selectorMap[target] = []\n      }\n      this.selectorMap[target].push(interactable)\n    } else {\n      if (!(interactable.target as any)[this.scope.id]) {\n        Object.defineProperty(target, this.scope.id, {\n          value: [],\n          configurable: true,\n        })\n      }\n\n      ;(target as any)[this.scope.id].push(interactable)\n    }\n\n    this.scope.fire('interactable:new', {\n      target,\n      options,\n      interactable,\n      win: this.scope._win,\n    })\n\n    return interactable\n  }\n\n  getExisting(target: Target, options?: Options) {\n    const context = (options && options.context) || this.scope.document\n    const isSelector = is.string(target)\n    const interactablesOnTarget: Interactable[] = isSelector\n      ? this.selectorMap[target as string]\n      : (target as any)[this.scope.id]\n\n    if (!interactablesOnTarget) return undefined\n\n    return arr.find(\n      interactablesOnTarget,\n      (interactable) =>\n        interactable._context === context && (isSelector || interactable.inContext(target as any)),\n    )\n  }\n\n  forEachMatch<T>(node: Node, callback: (interactable: Interactable) => T): T | void {\n    for (const interactable of this.list) {\n      let ret: T\n\n      if (\n        (is.string(interactable.target)\n          ? // target is a selector and the element matches\n            is.element(node) && domUtils.matchesSelector(node, interactable.target)\n          : // target is the element\n            node === interactable.target) &&\n        // the element is in context\n        interactable.inContext(node)\n      ) {\n        ret = callback(interactable)\n      }\n\n      if (ret !== undefined) {\n        return ret\n      }\n    }\n  }\n}\n", "import browser from '@interactjs/utils/browser'\nimport clone from '@interactjs/utils/clone'\nimport domObjects from '@interactjs/utils/domObjects'\nimport extend from '@interactjs/utils/extend'\nimport is from '@interactjs/utils/is'\nimport raf from '@interactjs/utils/raf'\nimport * as win from '@interactjs/utils/window'\n\nimport type Interaction from '@interactjs/core/Interaction'\n\nimport { Eventable } from './Eventable'\n/* eslint-disable import/no-duplicates -- for typescript module augmentations */\nimport './events'\nimport './interactions'\nimport events from './events'\nimport { Interactable as InteractableBase } from './Interactable'\nimport { InteractableSet } from './InteractableSet'\nimport { InteractEvent } from './InteractEvent'\nimport interactions from './interactions'\n/* eslint-enable import/no-duplicates */\nimport { createInteractStatic } from './InteractStatic'\nimport type { OptionsArg } from './options'\nimport { defaults } from './options'\nimport type { Actions } from './types'\n\nexport interface SignalArgs {\n  'scope:add-document': DocSignalArg\n  'scope:remove-document': DocSignalArg\n  'interactable:unset': { interactable: InteractableBase }\n  'interactable:set': { interactable: InteractableBase; options: OptionsArg }\n  'interactions:destroy': { interaction: Interaction }\n}\n\nexport type ListenerName = keyof SignalArgs\n\nexport type ListenerMap = {\n  [P in ListenerName]?: (arg: SignalArgs[P], scope: Scope, signalName: P) => void | boolean\n}\n\ninterface DocSignalArg {\n  doc: Document\n  window: Window\n  scope: Scope\n  options: Record<string, any>\n}\n\nexport interface Plugin {\n  [key: string]: any\n  id?: string\n  listeners?: ListenerMap\n  before?: string[]\n  install?(scope: Scope, options?: any): void\n}\n\n/** @internal */\nexport class Scope {\n  id = `__interact_scope_${Math.floor(Math.random() * 100)}`\n  isInitialized = false\n  listenerMaps: Array<{\n    map: ListenerMap\n    id?: string\n  }> = []\n\n  browser = browser\n  defaults = clone(defaults) as typeof defaults\n  Eventable = Eventable\n  actions: Actions = {\n    map: {},\n    phases: {\n      start: true,\n      move: true,\n      end: true,\n    },\n    methodDict: {} as any,\n    phaselessTypes: {},\n  }\n\n  interactStatic = createInteractStatic(this)\n  InteractEvent = InteractEvent\n  Interactable: typeof InteractableBase\n  interactables = new InteractableSet(this)\n\n  // main window\n  _win!: Window\n\n  // main document\n  document!: Document\n\n  // main window\n  window!: Window\n\n  // all documents being listened to\n  documents: Array<{ doc: Document; options: any }> = []\n\n  _plugins: {\n    list: Plugin[]\n    map: { [id: string]: Plugin }\n  } = {\n    list: [],\n    map: {},\n  }\n\n  constructor() {\n    const scope = this\n\n    this.Interactable = class extends InteractableBase {\n      get _defaults() {\n        return scope.defaults\n      }\n\n      set<T extends InteractableBase>(this: T, options: OptionsArg) {\n        super.set(options)\n\n        scope.fire('interactable:set', {\n          options,\n          interactable: this,\n        })\n\n        return this\n      }\n\n      unset(this: InteractableBase) {\n        super.unset()\n\n        const index = scope.interactables.list.indexOf(this)\n        if (index < 0) return\n\n        scope.interactables.list.splice(index, 1)\n        scope.fire('interactable:unset', { interactable: this })\n      }\n    }\n  }\n\n  addListeners(map: ListenerMap, id?: string) {\n    this.listenerMaps.push({ id, map })\n  }\n\n  fire<T extends ListenerName>(name: T, arg: SignalArgs[T]): void | false {\n    for (const {\n      map: { [name]: listener },\n    } of this.listenerMaps) {\n      if (!!listener && listener(arg as any, this, name as never) === false) {\n        return false\n      }\n    }\n  }\n\n  onWindowUnload = (event: BeforeUnloadEvent) => this.removeDocument(event.target as Document)\n\n  init(window: Window | typeof globalThis) {\n    return this.isInitialized ? this : initScope(this, window)\n  }\n\n  pluginIsInstalled(plugin: Plugin) {\n    const { id } = plugin\n    return id ? !!this._plugins.map[id] : this._plugins.list.indexOf(plugin) !== -1\n  }\n\n  usePlugin(plugin: Plugin, options?: { [key: string]: any }) {\n    if (!this.isInitialized) {\n      return this\n    }\n\n    if (this.pluginIsInstalled(plugin)) {\n      return this\n    }\n\n    if (plugin.id) {\n      this._plugins.map[plugin.id] = plugin\n    }\n    this._plugins.list.push(plugin)\n\n    if (plugin.install) {\n      plugin.install(this, options)\n    }\n\n    if (plugin.listeners && plugin.before) {\n      let index = 0\n      const len = this.listenerMaps.length\n      const before = plugin.before.reduce((acc, id) => {\n        acc[id] = true\n        acc[pluginIdRoot(id)] = true\n        return acc\n      }, {})\n\n      for (; index < len; index++) {\n        const otherId = this.listenerMaps[index].id\n\n        if (otherId && (before[otherId] || before[pluginIdRoot(otherId)])) {\n          break\n        }\n      }\n\n      this.listenerMaps.splice(index, 0, { id: plugin.id, map: plugin.listeners })\n    } else if (plugin.listeners) {\n      this.listenerMaps.push({ id: plugin.id, map: plugin.listeners })\n    }\n\n    return this\n  }\n\n  addDocument(doc: Document, options?: any): void | false {\n    // do nothing if document is already known\n    if (this.getDocIndex(doc) !== -1) {\n      return false\n    }\n\n    const window = win.getWindow(doc)\n\n    options = options ? extend({}, options) : {}\n\n    this.documents.push({ doc, options })\n    this.events.documents.push(doc)\n\n    // don't add an unload event for the main document\n    // so that the page may be cached in browser history\n    if (doc !== this.document) {\n      this.events.add(window, 'unload', this.onWindowUnload)\n    }\n\n    this.fire('scope:add-document', { doc, window, scope: this, options })\n  }\n\n  removeDocument(doc: Document) {\n    const index = this.getDocIndex(doc)\n\n    const window = win.getWindow(doc)\n    const options = this.documents[index].options\n\n    this.events.remove(window, 'unload', this.onWindowUnload)\n\n    this.documents.splice(index, 1)\n    this.events.documents.splice(index, 1)\n\n    this.fire('scope:remove-document', { doc, window, scope: this, options })\n  }\n\n  getDocIndex(doc: Document) {\n    for (let i = 0; i < this.documents.length; i++) {\n      if (this.documents[i].doc === doc) {\n        return i\n      }\n    }\n\n    return -1\n  }\n\n  getDocOptions(doc: Document) {\n    const docIndex = this.getDocIndex(doc)\n\n    return docIndex === -1 ? null : this.documents[docIndex].options\n  }\n\n  now() {\n    return (((this.window as any).Date as typeof Date) || Date).now()\n  }\n}\n\n// Keep Scope class internal, but expose minimal interface to avoid broken types when Scope is stripped out\nexport interface Scope {\n  fire<T extends ListenerName>(name: T, arg: SignalArgs[T]): void | false\n}\n\n/** @internal */\nexport function initScope(scope: Scope, window: Window | typeof globalThis) {\n  scope.isInitialized = true\n\n  if (is.window(window)) {\n    win.init(window)\n  }\n\n  domObjects.init(window)\n  browser.init(window)\n  raf.init(window)\n\n  // @ts-expect-error\n  scope.window = window\n  scope.document = window.document\n\n  scope.usePlugin(interactions)\n  scope.usePlugin(events)\n\n  return scope\n}\n\nfunction pluginIdRoot(id: string) {\n  return id && id.replace(/\\/.*$/, '')\n}\n", "import browser from '@interactjs/utils/browser'\nimport * as domUtils from '@interactjs/utils/domUtils'\nimport is from '@interactjs/utils/is'\nimport isNonNativeEvent from '@interactjs/utils/isNonNativeEvent'\nimport { warnOnce } from '@interactjs/utils/misc'\nimport * as pointerUtils from '@interactjs/utils/pointerUtils'\n\nimport type { Scope, Plugin } from '@interactjs/core/scope'\nimport type { Context, EventTypes, Listener, ListenersArg, Target } from '@interactjs/core/types'\n\nimport type { Interactable } from './Interactable'\nimport type { Options } from './options'\n\n/**\n * ```js\n * interact('#draggable').draggable(true)\n *\n * var rectables = interact('rect')\n * rectables\n *   .gesturable(true)\n *   .on('gesturemove', function (event) {\n *       // ...\n *   })\n * ```\n *\n * The methods of this variable can be used to set elements as interactables\n * and also to change various default settings.\n *\n * Calling it as a function and passing an element or a valid CSS selector\n * string returns an Interactable object which has various methods to configure\n * it.\n *\n * @param {Element | string} target The HTML or SVG Element to interact with\n * or CSS selector\n * @return {Interactable}\n */\nexport interface InteractStatic {\n  (target: Target, options?: Options): Interactable\n  getPointerAverage: typeof pointerUtils.pointerAverage\n  getTouchBBox: typeof pointerUtils.touchBBox\n  getTouchDistance: typeof pointerUtils.touchDistance\n  getTouchAngle: typeof pointerUtils.touchAngle\n  getElementRect: typeof domUtils.getElementRect\n  getElementClientRect: typeof domUtils.getElementClientRect\n  matchesSelector: typeof domUtils.matchesSelector\n  closest: typeof domUtils.closest\n  /** @internal */ globalEvents: any\n  version: string\n  /** @internal */ scope: Scope\n  /**\n   * Use a plugin\n   */\n  use(\n    plugin: Plugin,\n    options?: {\n      [key: string]: any\n    },\n  ): any\n  /**\n   * Check if an element or selector has been set with the `interact(target)`\n   * function\n   *\n   * @return {boolean} Indicates if the element or CSS selector was previously\n   * passed to interact\n   */\n  isSet(\n    /* The Element or string being searched for */\n    target: Target,\n    options?: any,\n  ): boolean\n  on(type: string | EventTypes, listener: ListenersArg, options?: object): any\n  off(type: EventTypes, listener: any, options?: object): any\n  debug(): any\n  /**\n   * Whether or not the browser supports touch input\n   */\n  supportsTouch(): boolean\n  /**\n   * Whether or not the browser supports PointerEvents\n   */\n  supportsPointerEvent(): boolean\n  /**\n   * Cancels all interactions (end events are not fired)\n   */\n  stop(): InteractStatic\n  /**\n   * Returns or sets the distance the pointer must be moved before an action\n   * sequence occurs. This also affects tolerance for tap events.\n   */\n  pointerMoveTolerance(\n    /** The movement from the start position must be greater than this value */\n    newValue?: number,\n  ): InteractStatic | number\n  addDocument(doc: Document, options?: object): void\n  removeDocument(doc: Document): void\n}\n\nexport function createInteractStatic(scope: Scope): InteractStatic {\n  const interact = ((target: Target, options: Options) => {\n    let interactable = scope.interactables.getExisting(target, options)\n\n    if (!interactable) {\n      interactable = scope.interactables.new(target, options)\n      interactable.events.global = interact.globalEvents\n    }\n\n    return interactable\n  }) as InteractStatic\n\n  // expose the functions used to calculate multi-touch properties\n  interact.getPointerAverage = pointerUtils.pointerAverage\n  interact.getTouchBBox = pointerUtils.touchBBox\n  interact.getTouchDistance = pointerUtils.touchDistance\n  interact.getTouchAngle = pointerUtils.touchAngle\n\n  interact.getElementRect = domUtils.getElementRect\n  interact.getElementClientRect = domUtils.getElementClientRect\n  interact.matchesSelector = domUtils.matchesSelector\n  interact.closest = domUtils.closest\n\n  interact.globalEvents = {} as any\n\n  // eslint-disable-next-line no-undef\n  interact.version = process.env.npm_package_version\n  interact.scope = scope\n  interact.use = function (plugin, options) {\n    this.scope.usePlugin(plugin, options)\n\n    return this\n  }\n\n  interact.isSet = function (target: Target, options?: { context?: Context }): boolean {\n    return !!this.scope.interactables.get(target, options && options.context)\n  }\n\n  interact.on = warnOnce(function on(type: string | EventTypes, listener: ListenersArg, options?: object) {\n    if (is.string(type) && type.search(' ') !== -1) {\n      type = type.trim().split(/ +/)\n    }\n\n    if (is.array(type)) {\n      for (const eventType of type as any[]) {\n        this.on(eventType, listener, options)\n      }\n\n      return this\n    }\n\n    if (is.object(type)) {\n      for (const prop in type) {\n        this.on(prop, (type as any)[prop], listener)\n      }\n\n      return this\n    }\n\n    // if it is an InteractEvent type, add listener to globalEvents\n    if (isNonNativeEvent(type, this.scope.actions)) {\n      // if this type of event was never bound\n      if (!this.globalEvents[type]) {\n        this.globalEvents[type] = [listener]\n      } else {\n        this.globalEvents[type].push(listener)\n      }\n    }\n    // If non InteractEvent type, addEventListener to document\n    else {\n      this.scope.events.add(this.scope.document, type, listener as Listener, { options })\n    }\n\n    return this\n  }, 'The interact.on() method is being deprecated')\n\n  interact.off = warnOnce(function off(type: EventTypes, listener: any, options?: object) {\n    if (is.string(type) && type.search(' ') !== -1) {\n      type = type.trim().split(/ +/)\n    }\n\n    if (is.array(type)) {\n      for (const eventType of type) {\n        this.off(eventType, listener, options)\n      }\n\n      return this\n    }\n\n    if (is.object(type)) {\n      for (const prop in type) {\n        this.off(prop, type[prop], listener)\n      }\n\n      return this\n    }\n\n    if (isNonNativeEvent(type, this.scope.actions)) {\n      let index: number\n\n      if (type in this.globalEvents && (index = this.globalEvents[type].indexOf(listener)) !== -1) {\n        this.globalEvents[type].splice(index, 1)\n      }\n    } else {\n      this.scope.events.remove(this.scope.document, type, listener, options)\n    }\n\n    return this\n  }, 'The interact.off() method is being deprecated')\n\n  interact.debug = function () {\n    return this.scope\n  }\n\n  interact.supportsTouch = function () {\n    return browser.supportsTouch\n  }\n\n  interact.supportsPointerEvent = function () {\n    return browser.supportsPointerEvent\n  }\n\n  interact.stop = function () {\n    for (const interaction of this.scope.interactions.list) {\n      interaction.stop()\n    }\n\n    return this\n  }\n\n  interact.pointerMoveTolerance = function (newValue?: number) {\n    if (is.number(newValue)) {\n      this.scope.interactions.pointerMoveTolerance = newValue\n\n      return this\n    }\n\n    return this.scope.interactions.pointerMoveTolerance\n  }\n\n  interact.addDocument = function (doc: Document, options?: object) {\n    this.scope.addDocument(doc, options)\n  }\n\n  interact.removeDocument = function (doc: Document) {\n    this.scope.removeDocument(doc)\n  }\n\n  return interact\n}\n", "import { Scope } from '@interactjs/core/scope'\n\nconst scope = new Scope()\n\nconst interact = scope.interactStatic\n\nexport default interact\n\nconst _global = typeof globalThis !== 'undefined' ? globalThis : typeof window !== 'undefined' ? window : this\nscope.init(_global)\n", "export default () => {}\n", "export default () => {}\n", "import type { Rect, Point } from '@interactjs/core/types'\nimport type { SnapFunction, SnapTarget } from '@interactjs/modifiers/snap/pointer'\n\nexport interface GridOptionsBase {\n  range?: number\n  limits?: Rect\n  offset?: Point\n}\nexport interface GridOptionsXY extends GridOptionsBase {\n  x: number\n  y: number\n}\nexport interface GridOptionsTopLeft extends GridOptionsBase {\n  top?: number\n  left?: number\n}\nexport interface GridOptionsBottomRight extends GridOptionsBase {\n  bottom?: number\n  right?: number\n}\nexport interface GridOptionsWidthHeight extends GridOptionsBase {\n  width?: number\n  height?: number\n}\n\nexport type GridOptions = GridOptionsXY | GridOptionsTopLeft | GridOptionsBottomRight | GridOptionsWidthHeight\n\nexport default (grid: GridOptions) => {\n  const coordFields = (\n    [\n      ['x', 'y'],\n      ['left', 'top'],\n      ['right', 'bottom'],\n      ['width', 'height'],\n    ] as const\n  ).filter(([xField, yField]) => xField in grid || yField in grid)\n\n  const gridFunc: SnapFunction & {\n    grid: typeof grid\n    coordFields: typeof coordFields\n  } = (x, y) => {\n    const {\n      range,\n      limits = {\n        left: -Infinity,\n        right: Infinity,\n        top: -Infinity,\n        bottom: Infinity,\n      },\n      offset = { x: 0, y: 0 },\n    } = grid\n\n    const result: SnapTarget & {\n      grid: typeof grid\n    } = { range, grid, x: null as number, y: null as number }\n\n    for (const [xField, yField] of coordFields) {\n      const gridx = Math.round((x - offset.x) / (grid as any)[xField])\n      const gridy = Math.round((y - offset.y) / (grid as any)[yField])\n\n      result[xField] = Math.max(limits.left, Math.min(limits.right, gridx * (grid as any)[xField] + offset.x))\n      result[yField] = Math.max(limits.top, Math.min(limits.bottom, gridy * (grid as any)[yField] + offset.y))\n    }\n\n    return result\n  }\n\n  gridFunc.grid = grid\n  gridFunc.coordFields = coordFields\n\n  return gridFunc\n}\n", "import type { Plugin } from '@interactjs/core/scope'\nimport extend from '@interactjs/utils/extend'\n\nimport * as allSnappers from './all'\n\ndeclare module '@interactjs/core/InteractStatic' {\n  export interface InteractStatic {\n    snappers: typeof allSnappers\n    createSnapGrid: typeof allSnappers.grid\n  }\n}\n\nconst snappersPlugin: Plugin = {\n  id: 'snappers',\n  install(scope) {\n    const { interactStatic: interact } = scope\n\n    interact.snappers = extend(interact.snappers || {}, allSnappers)\n    interact.createSnapGrid = interact.snappers.grid\n  },\n}\n\nexport default snappersPlugin\n", "/**\n * @module modifiers/aspectRatio\n *\n * @description\n * This modifier forces elements to be resized with a specified dx/dy ratio.\n *\n * ```js\n * interact(target).resizable({\n *   modifiers: [\n *     interact.modifiers.snapSize({\n *       targets: [ interact.snappers.grid({ x: 20, y: 20 }) ],\n *     }),\n *     interact.aspectRatio({ ratio: 'preserve' }),\n *   ],\n * });\n * ```\n */\n\nimport type { Point, Rect, EdgeOptions } from '@interactjs/core/types'\nimport extend from '@interactjs/utils/extend'\nimport { addEdges } from '@interactjs/utils/rect'\n\nimport { makeModifier } from './base'\nimport { Modification } from './Modification'\nimport type { Modifier, ModifierModule, ModifierState } from './types'\n\nexport interface AspectRatioOptions {\n  ratio?: number | 'preserve'\n  equalDelta?: boolean\n  modifiers?: Modifier[]\n  enabled?: boolean\n}\n\nexport type AspectRatioState = ModifierState<\n  AspectRatioOptions,\n  {\n    startCoords: Point\n    startRect: Rect\n    linkedEdges: EdgeOptions\n    ratio: number\n    equalDelta: boolean\n    xIsPrimaryAxis: boolean\n    edgeSign: {\n      x: number\n      y: number\n    }\n    subModification: Modification\n  }\n>\n\nconst aspectRatio: ModifierModule<AspectRatioOptions, AspectRatioState> = {\n  start(arg) {\n    const { state, rect, edges, pageCoords: coords } = arg\n    let { ratio, enabled } = state.options\n    const { equalDelta, modifiers } = state.options\n\n    if (ratio === 'preserve') {\n      ratio = rect.width / rect.height\n    }\n\n    state.startCoords = extend({}, coords)\n    state.startRect = extend({}, rect)\n    state.ratio = ratio\n    state.equalDelta = equalDelta\n\n    const linkedEdges = (state.linkedEdges = {\n      top: edges.top || (edges.left && !edges.bottom),\n      left: edges.left || (edges.top && !edges.right),\n      bottom: edges.bottom || (edges.right && !edges.top),\n      right: edges.right || (edges.bottom && !edges.left),\n    })\n\n    state.xIsPrimaryAxis = !!(edges.left || edges.right)\n\n    if (state.equalDelta) {\n      const sign = (linkedEdges.left ? 1 : -1) * (linkedEdges.top ? 1 : -1)\n      state.edgeSign = {\n        x: sign,\n        y: sign,\n      }\n    } else {\n      state.edgeSign = {\n        x: linkedEdges.left ? -1 : 1,\n        y: linkedEdges.top ? -1 : 1,\n      }\n    }\n\n    if (enabled !== false) {\n      extend(edges, linkedEdges)\n    }\n\n    if (!modifiers?.length) return\n\n    const subModification = new Modification(arg.interaction)\n\n    subModification.copyFrom(arg.interaction.modification)\n    subModification.prepareStates(modifiers)\n\n    state.subModification = subModification\n    subModification.startAll({ ...arg })\n  },\n\n  set(arg) {\n    const { state, rect, coords } = arg\n    const { linkedEdges } = state\n    const initialCoords = extend({}, coords)\n    const aspectMethod = state.equalDelta ? setEqualDelta : setRatio\n\n    extend(arg.edges, linkedEdges)\n    aspectMethod(state, state.xIsPrimaryAxis, coords, rect)\n\n    if (!state.subModification) {\n      return null\n    }\n\n    const correctedRect = extend({}, rect)\n\n    addEdges(linkedEdges, correctedRect, {\n      x: coords.x - initialCoords.x,\n      y: coords.y - initialCoords.y,\n    })\n\n    const result = state.subModification.setAll({\n      ...arg,\n      rect: correctedRect,\n      edges: linkedEdges,\n      pageCoords: coords,\n      prevCoords: coords,\n      prevRect: correctedRect,\n    })\n\n    const { delta } = result\n\n    if (result.changed) {\n      const xIsCriticalAxis = Math.abs(delta.x) > Math.abs(delta.y)\n\n      // do aspect modification again with critical edge axis as primary\n      aspectMethod(state, xIsCriticalAxis, result.coords, result.rect)\n      extend(coords, result.coords)\n    }\n\n    return result.eventProps\n  },\n\n  defaults: {\n    ratio: 'preserve',\n    equalDelta: false,\n    modifiers: [],\n    enabled: false,\n  },\n}\n\nfunction setEqualDelta({ startCoords, edgeSign }: AspectRatioState, xIsPrimaryAxis: boolean, coords: Point) {\n  if (xIsPrimaryAxis) {\n    coords.y = startCoords.y + (coords.x - startCoords.x) * edgeSign.y\n  } else {\n    coords.x = startCoords.x + (coords.y - startCoords.y) * edgeSign.x\n  }\n}\n\nfunction setRatio(\n  { startRect, startCoords, ratio, edgeSign }: AspectRatioState,\n  xIsPrimaryAxis: boolean,\n  coords: Point,\n  rect: Rect,\n) {\n  if (xIsPrimaryAxis) {\n    const newHeight = rect.width / ratio\n\n    coords.y = startCoords.y + (newHeight - startRect.height) * edgeSign.y\n  } else {\n    const newWidth = rect.height * ratio\n\n    coords.x = startCoords.x + (newWidth - startRect.width) * edgeSign.x\n  }\n}\n\nexport default makeModifier(aspectRatio, 'aspectRatio')\nexport { aspectRatio }\n", "import type { ModifierFunction } from './types'\n\nconst noop = (() => {}) as unknown as ModifierFunction<any, any, 'noop'>\n\nnoop._defaults = {}\n\nexport default noop\n", "import type Interaction from '@interactjs/core/Interaction'\nimport type { RectResolvable, Rect, Point } from '@interactjs/core/types'\nimport extend from '@interactjs/utils/extend'\nimport is from '@interactjs/utils/is'\nimport * as rectUtils from '@interactjs/utils/rect'\n\nimport { makeModifier } from '../base'\nimport type { ModifierArg, ModifierModule, ModifierState } from '../types'\n\nexport interface RestrictOptions {\n  // where to drag over\n  restriction: RectResolvable<[number, number, Interaction]>\n  // what part of self is allowed to drag over\n  elementRect: Rect\n  offset: Rect\n  // restrict just before the end drag\n  endOnly: boolean\n  enabled?: boolean\n}\n\nexport type RestrictState = ModifierState<\n  RestrictOptions,\n  {\n    offset: Rect\n  }\n>\n\nfunction start({ rect, startOffset, state, interaction, pageCoords }: ModifierArg<RestrictState>) {\n  const { options } = state\n  const { elementRect } = options\n  const offset: Rect = extend(\n    {\n      left: 0,\n      top: 0,\n      right: 0,\n      bottom: 0,\n    },\n    options.offset || {},\n  )\n\n  if (rect && elementRect) {\n    const restriction = getRestrictionRect(options.restriction, interaction, pageCoords)\n\n    if (restriction) {\n      const widthDiff = restriction.right - restriction.left - rect.width\n      const heightDiff = restriction.bottom - restriction.top - rect.height\n\n      if (widthDiff < 0) {\n        offset.left += widthDiff\n        offset.right += widthDiff\n      }\n      if (heightDiff < 0) {\n        offset.top += heightDiff\n        offset.bottom += heightDiff\n      }\n    }\n\n    offset.left += startOffset.left - rect.width * elementRect.left\n    offset.top += startOffset.top - rect.height * elementRect.top\n\n    offset.right += startOffset.right - rect.width * (1 - elementRect.right)\n    offset.bottom += startOffset.bottom - rect.height * (1 - elementRect.bottom)\n  }\n\n  state.offset = offset\n}\n\nfunction set({ coords, interaction, state }: ModifierArg<RestrictState>) {\n  const { options, offset } = state\n\n  const restriction = getRestrictionRect(options.restriction, interaction, coords)\n\n  if (!restriction) return\n\n  const rect = rectUtils.xywhToTlbr(restriction)\n\n  coords.x = Math.max(Math.min(rect.right - offset.right, coords.x), rect.left + offset.left)\n  coords.y = Math.max(Math.min(rect.bottom - offset.bottom, coords.y), rect.top + offset.top)\n}\n\nexport function getRestrictionRect(\n  value: RectResolvable<[number, number, Interaction]>,\n  interaction: Interaction,\n  coords?: Point,\n) {\n  if (is.func(value)) {\n    return rectUtils.resolveRectLike(value, interaction.interactable, interaction.element, [\n      coords.x,\n      coords.y,\n      interaction,\n    ])\n  } else {\n    return rectUtils.resolveRectLike(value, interaction.interactable, interaction.element)\n  }\n}\n\nconst defaults: RestrictOptions = {\n  restriction: null,\n  elementRect: null,\n  offset: null,\n  endOnly: false,\n  enabled: false,\n}\n\nconst restrict: ModifierModule<RestrictOptions, RestrictState> = {\n  start,\n  set,\n  defaults,\n}\n\nexport default makeModifier(restrict, 'restrict')\nexport { restrict }\n", "// This modifier adds the options.resize.restrictEdges setting which sets min and\n// max for the top, left, bottom and right edges of the target being resized.\n//\n// interact(target).resize({\n//   edges: { top: true, left: true },\n//   restrictEdges: {\n//     inner: { top: 200, left: 200, right: 400, bottom: 400 },\n//     outer: { top:   0, left:   0, right: 600, bottom: 600 },\n//   },\n// })\n\nimport type { Point, Rect } from '@interactjs/core/types'\nimport extend from '@interactjs/utils/extend'\nimport * as rectUtils from '@interactjs/utils/rect'\n\nimport { makeModifier } from '../base'\nimport type { ModifierArg, ModifierState } from '../types'\n\nimport type { RestrictOptions } from './pointer'\nimport { getRestrictionRect } from './pointer'\n\nexport interface RestrictEdgesOptions {\n  inner: RestrictOptions['restriction']\n  outer: RestrictOptions['restriction']\n  offset?: RestrictOptions['offset']\n  endOnly: boolean\n  enabled?: boolean\n}\n\nexport type RestrictEdgesState = ModifierState<\n  RestrictEdgesOptions,\n  {\n    inner: Rect\n    outer: Rect\n    offset: RestrictEdgesOptions['offset']\n  }\n>\n\nconst noInner = { top: +Infinity, left: +Infinity, bottom: -Infinity, right: -Infinity }\nconst noOuter = { top: -Infinity, left: -Infinity, bottom: +Infinity, right: +Infinity }\n\nfunction start({ interaction, startOffset, state }: ModifierArg<RestrictEdgesState>) {\n  const { options } = state\n  let offset: Point\n\n  if (options) {\n    const offsetRect = getRestrictionRect(options.offset, interaction, interaction.coords.start.page)\n\n    offset = rectUtils.rectToXY(offsetRect)\n  }\n\n  offset = offset || { x: 0, y: 0 }\n\n  state.offset = {\n    top: offset.y + startOffset.top,\n    left: offset.x + startOffset.left,\n    bottom: offset.y - startOffset.bottom,\n    right: offset.x - startOffset.right,\n  }\n}\n\nfunction set({ coords, edges, interaction, state }: ModifierArg<RestrictEdgesState>) {\n  const { offset, options } = state\n\n  if (!edges) {\n    return\n  }\n\n  const page = extend({}, coords)\n  const inner = getRestrictionRect(options.inner, interaction, page) || ({} as Rect)\n  const outer = getRestrictionRect(options.outer, interaction, page) || ({} as Rect)\n\n  fixRect(inner, noInner)\n  fixRect(outer, noOuter)\n\n  if (edges.top) {\n    coords.y = Math.min(Math.max(outer.top + offset.top, page.y), inner.top + offset.top)\n  } else if (edges.bottom) {\n    coords.y = Math.max(Math.min(outer.bottom + offset.bottom, page.y), inner.bottom + offset.bottom)\n  }\n  if (edges.left) {\n    coords.x = Math.min(Math.max(outer.left + offset.left, page.x), inner.left + offset.left)\n  } else if (edges.right) {\n    coords.x = Math.max(Math.min(outer.right + offset.right, page.x), inner.right + offset.right)\n  }\n}\n\nfunction fixRect(rect: Rect, defaults: Rect) {\n  for (const edge of ['top', 'left', 'bottom', 'right']) {\n    if (!(edge in rect)) {\n      rect[edge] = defaults[edge]\n    }\n  }\n\n  return rect\n}\n\nconst defaults: RestrictEdgesOptions = {\n  inner: null,\n  outer: null,\n  offset: null,\n  endOnly: false,\n  enabled: false,\n}\n\nconst restrictEdges = {\n  noInner,\n  noOuter,\n  start,\n  set,\n  defaults,\n}\n\nexport default makeModifier(restrictEdges, 'restrictEdges')\nexport { restrictEdges }\n", "import extend from '@interactjs/utils/extend'\n\nimport { makeModifier } from '../base'\n\nimport { restrict } from './pointer'\n\nconst defaults = extend(\n  {\n    get elementRect() {\n      return { top: 0, left: 0, bottom: 1, right: 1 }\n    },\n    set elementRect(_) {},\n  },\n  restrict.defaults,\n)\n\nconst restrictRect = {\n  start: restrict.start,\n  set: restrict.set,\n  defaults,\n}\n\nexport default makeModifier(restrictRect, 'restrictRect')\nexport { restrictRect }\n", "import type { Point, Rect, Size } from '@interactjs/core/types'\nimport extend from '@interactjs/utils/extend'\nimport * as rectUtils from '@interactjs/utils/rect'\n\nimport { makeModifier } from '../base'\nimport type { ModifierArg, ModifierState } from '../types'\n\nimport type { RestrictEdgesState } from './edges'\nimport { restrictEdges } from './edges'\nimport type { RestrictOptions } from './pointer'\nimport { getRestrictionRect } from './pointer'\n\nconst noMin = { width: -Infinity, height: -Infinity }\nconst noMax = { width: +Infinity, height: +Infinity }\n\nexport interface RestrictSizeOptions {\n  min?: Size | Point | RestrictOptions['restriction']\n  max?: Size | Point | RestrictOptions['restriction']\n  endOnly: boolean\n  enabled?: boolean\n}\n\nfunction start(arg: ModifierArg<RestrictEdgesState>) {\n  return restrictEdges.start(arg)\n}\n\nexport type RestrictSizeState = RestrictEdgesState &\n  ModifierState<\n    RestrictSizeOptions & { inner: Rect; outer: Rect },\n    {\n      min: Rect\n      max: Rect\n    }\n  >\n\nfunction set(arg: ModifierArg<RestrictSizeState>) {\n  const { interaction, state, rect, edges } = arg\n  const { options } = state\n\n  if (!edges) {\n    return\n  }\n\n  const minSize =\n    rectUtils.tlbrToXywh(getRestrictionRect(options.min as any, interaction, arg.coords)) || noMin\n  const maxSize =\n    rectUtils.tlbrToXywh(getRestrictionRect(options.max as any, interaction, arg.coords)) || noMax\n\n  state.options = {\n    endOnly: options.endOnly,\n    inner: extend({}, restrictEdges.noInner),\n    outer: extend({}, restrictEdges.noOuter),\n  }\n\n  if (edges.top) {\n    state.options.inner.top = rect.bottom - minSize.height\n    state.options.outer.top = rect.bottom - maxSize.height\n  } else if (edges.bottom) {\n    state.options.inner.bottom = rect.top + minSize.height\n    state.options.outer.bottom = rect.top + maxSize.height\n  }\n  if (edges.left) {\n    state.options.inner.left = rect.right - minSize.width\n    state.options.outer.left = rect.right - maxSize.width\n  } else if (edges.right) {\n    state.options.inner.right = rect.left + minSize.width\n    state.options.outer.right = rect.left + maxSize.width\n  }\n\n  restrictEdges.set(arg)\n\n  state.options = options\n}\n\nconst defaults: RestrictSizeOptions = {\n  min: null,\n  max: null,\n  endOnly: false,\n  enabled: false,\n}\n\nconst restrictSize = {\n  start,\n  set,\n  defaults,\n}\n\nexport default makeModifier(restrictSize, 'restrictSize')\nexport { restrictSize }\n", "import type { Interaction, InteractionProxy } from '@interactjs/core/Interaction'\nimport type { ActionName, Point, RectResolvable, Element } from '@interactjs/core/types'\nimport extend from '@interactjs/utils/extend'\nimport getOriginXY from '@interactjs/utils/getOriginXY'\nimport hypot from '@interactjs/utils/hypot'\nimport is from '@interactjs/utils/is'\nimport { resolveRectLike, rectToXY } from '@interactjs/utils/rect'\n\nimport { makeModifier } from '../base'\nimport type { ModifierArg, ModifierState } from '../types'\n\nexport interface Offset {\n  x: number\n  y: number\n  index: number\n  relativePoint?: Point | null\n}\n\nexport interface SnapPosition {\n  x?: number\n  y?: number\n  range?: number\n  offset?: Offset\n  [index: string]: any\n}\n\nexport type SnapFunction = (\n  x: number,\n  y: number,\n  interaction: InteractionProxy<ActionName>,\n  offset: Offset,\n  index: number,\n) => SnapPosition\nexport type SnapTarget = SnapPosition | SnapFunction\nexport interface SnapOptions {\n  targets?: SnapTarget[]\n  // target range\n  range?: number\n  // self points for snapping. [0,0] = top left, [1,1] = bottom right\n  relativePoints?: Point[]\n  // startCoords = offset snapping from drag start page position\n  offset?: Point | RectResolvable<[Interaction]> | 'startCoords'\n  offsetWithOrigin?: boolean\n  origin?: RectResolvable<[Element]> | Point\n  endOnly?: boolean\n  enabled?: boolean\n}\n\nexport type SnapState = ModifierState<\n  SnapOptions,\n  {\n    offsets?: Offset[]\n    closest?: any\n    targetFields?: string[][]\n  }\n>\n\nfunction start(arg: ModifierArg<SnapState>) {\n  const { interaction, interactable, element, rect, state, startOffset } = arg\n  const { options } = state\n  const origin = options.offsetWithOrigin ? getOrigin(arg) : { x: 0, y: 0 }\n\n  let snapOffset: Point\n\n  if (options.offset === 'startCoords') {\n    snapOffset = {\n      x: interaction.coords.start.page.x,\n      y: interaction.coords.start.page.y,\n    }\n  } else {\n    const offsetRect = resolveRectLike(options.offset as any, interactable, element, [interaction])\n\n    snapOffset = rectToXY(offsetRect) || { x: 0, y: 0 }\n    snapOffset.x += origin.x\n    snapOffset.y += origin.y\n  }\n\n  const { relativePoints } = options\n\n  state.offsets =\n    rect && relativePoints && relativePoints.length\n      ? relativePoints.map((relativePoint, index) => ({\n          index,\n          relativePoint,\n          x: startOffset.left - rect.width * relativePoint.x + snapOffset.x,\n          y: startOffset.top - rect.height * relativePoint.y + snapOffset.y,\n        }))\n      : [\n          {\n            index: 0,\n            relativePoint: null,\n            x: snapOffset.x,\n            y: snapOffset.y,\n          },\n        ]\n}\n\nfunction set(arg: ModifierArg<SnapState>) {\n  const { interaction, coords, state } = arg\n  const { options, offsets } = state\n\n  const origin = getOriginXY(interaction.interactable!, interaction.element!, interaction.prepared.name)\n  const page = extend({}, coords)\n  const targets: SnapPosition[] = []\n\n  if (!options.offsetWithOrigin) {\n    page.x -= origin.x\n    page.y -= origin.y\n  }\n\n  for (const offset of offsets!) {\n    const relativeX = page.x - offset.x\n    const relativeY = page.y - offset.y\n\n    for (let index = 0, len = options.targets!.length; index < len; index++) {\n      const snapTarget = options.targets![index]\n      let target: SnapPosition\n\n      if (is.func(snapTarget)) {\n        target = snapTarget(relativeX, relativeY, interaction._proxy, offset, index)\n      } else {\n        target = snapTarget\n      }\n\n      if (!target) {\n        continue\n      }\n\n      targets.push({\n        x: (is.number(target.x) ? target.x : relativeX) + offset.x,\n        y: (is.number(target.y) ? target.y : relativeY) + offset.y,\n\n        range: is.number(target.range) ? target.range : options.range,\n        source: snapTarget,\n        index,\n        offset,\n      })\n    }\n  }\n\n  const closest = {\n    target: null,\n    inRange: false,\n    distance: 0,\n    range: 0,\n    delta: { x: 0, y: 0 },\n  }\n\n  for (const target of targets) {\n    const range = target.range\n    const dx = target.x - page.x\n    const dy = target.y - page.y\n    const distance = hypot(dx, dy)\n    let inRange = distance <= range\n\n    // Infinite targets count as being out of range\n    // compared to non infinite ones that are in range\n    if (range === Infinity && closest.inRange && closest.range !== Infinity) {\n      inRange = false\n    }\n\n    if (\n      !closest.target ||\n      (inRange\n        ? // is the closest target in range?\n          closest.inRange && range !== Infinity\n          ? // the pointer is relatively deeper in this target\n            distance / range < closest.distance / closest.range\n          : // this target has Infinite range and the closest doesn't\n            (range === Infinity && closest.range !== Infinity) ||\n            // OR this target is closer that the previous closest\n            distance < closest.distance\n        : // The other is not in range and the pointer is closer to this target\n          !closest.inRange && distance < closest.distance)\n    ) {\n      closest.target = target\n      closest.distance = distance\n      closest.range = range\n      closest.inRange = inRange\n      closest.delta.x = dx\n      closest.delta.y = dy\n    }\n  }\n\n  if (closest.inRange) {\n    coords.x = closest.target.x\n    coords.y = closest.target.y\n  }\n\n  state.closest = closest\n  return closest\n}\n\nfunction getOrigin(arg: Partial<ModifierArg<SnapState>>) {\n  const { element } = arg.interaction\n  const optionsOrigin = rectToXY(resolveRectLike(arg.state.options.origin as any, null, null, [element]))\n  const origin = optionsOrigin || getOriginXY(arg.interactable, element, arg.interaction.prepared.name)\n\n  return origin\n}\n\nconst defaults: SnapOptions = {\n  range: Infinity,\n  targets: null,\n  offset: null,\n  offsetWithOrigin: true,\n  origin: null,\n  relativePoints: null,\n  endOnly: false,\n  enabled: false,\n}\nconst snap = {\n  start,\n  set,\n  defaults,\n}\n\nexport default makeModifier(snap, 'snap')\nexport { snap }\n", "// This modifier allows snapping of the size of targets during resize\n// interactions.\n\nimport extend from '@interactjs/utils/extend'\nimport is from '@interactjs/utils/is'\n\nimport { makeModifier } from '../base'\nimport type { ModifierArg } from '../types'\n\nimport type { SnapOptions, SnapState } from './pointer'\nimport { snap } from './pointer'\n\nexport type SnapSizeOptions = Pick<SnapOptions, 'targets' | 'offset' | 'endOnly' | 'range' | 'enabled'>\n\nfunction start(arg: ModifierArg<SnapState>) {\n  const { state, edges } = arg\n  const { options } = state\n\n  if (!edges) {\n    return null\n  }\n\n  arg.state = {\n    options: {\n      targets: null,\n      relativePoints: [\n        {\n          x: edges.left ? 0 : 1,\n          y: edges.top ? 0 : 1,\n        },\n      ],\n      offset: options.offset || 'self',\n      origin: { x: 0, y: 0 },\n      range: options.range,\n    },\n  }\n\n  state.targetFields = state.targetFields || [\n    ['width', 'height'],\n    ['x', 'y'],\n  ]\n\n  snap.start(arg)\n  state.offsets = arg.state.offsets\n\n  arg.state = state\n}\n\nfunction set(arg) {\n  const { interaction, state, coords } = arg\n  const { options, offsets } = state\n  const relative = {\n    x: coords.x - offsets[0].x,\n    y: coords.y - offsets[0].y,\n  }\n\n  state.options = extend({}, options)\n  state.options.targets = []\n\n  for (const snapTarget of options.targets || []) {\n    let target\n\n    if (is.func(snapTarget)) {\n      target = snapTarget(relative.x, relative.y, interaction)\n    } else {\n      target = snapTarget\n    }\n\n    if (!target) {\n      continue\n    }\n\n    for (const [xField, yField] of state.targetFields) {\n      if (xField in target || yField in target) {\n        target.x = target[xField]\n        target.y = target[yField]\n\n        break\n      }\n    }\n\n    state.options.targets.push(target)\n  }\n\n  const returnValue = snap.set(arg)\n\n  state.options = options\n\n  return returnValue\n}\n\nconst defaults: SnapSizeOptions = {\n  range: Infinity,\n  targets: null,\n  offset: null,\n  endOnly: false,\n  enabled: false,\n}\n\nconst snapSize = {\n  start,\n  set,\n  defaults,\n}\n\nexport default makeModifier(snapSize, 'snapSize')\nexport { snapSize }\n", "/**\n * @module modifiers/snapEdges\n *\n * @description\n * This modifier allows snapping of the edges of targets during resize\n * interactions.\n *\n * ```js\n * interact(target).resizable({\n *   snapEdges: {\n *     targets: [interact.snappers.grid({ x: 100, y: 50 })],\n *   },\n * })\n *\n * interact(target).resizable({\n *   snapEdges: {\n *     targets: [\n *       interact.snappers.grid({\n *        top: 50,\n *        left: 50,\n *        bottom: 100,\n *        right: 100,\n *       }),\n *     ],\n *   },\n * })\n * ```\n */\n\nimport clone from '@interactjs/utils/clone'\nimport extend from '@interactjs/utils/extend'\n\nimport { makeModifier } from '../base'\nimport type { ModifierArg, ModifierModule } from '../types'\n\nimport type { SnapOptions, SnapState } from './pointer'\nimport { snapSize } from './size'\n\nexport type SnapEdgesOptions = Pick<SnapOptions, 'targets' | 'range' | 'offset' | 'endOnly' | 'enabled'>\n\nfunction start(arg: ModifierArg<SnapState>) {\n  const { edges } = arg\n\n  if (!edges) {\n    return null\n  }\n\n  arg.state.targetFields = arg.state.targetFields || [\n    [edges.left ? 'left' : 'right', edges.top ? 'top' : 'bottom'],\n  ]\n\n  return snapSize.start(arg)\n}\n\nconst snapEdges: ModifierModule<SnapEdgesOptions, SnapState, ReturnType<typeof snapSize.set>> = {\n  start,\n  set: snapSize.set,\n  defaults: extend(clone(snapSize.defaults), {\n    targets: undefined,\n    range: undefined,\n    offset: { x: 0, y: 0 },\n  } as const),\n}\n\nexport default makeModifier(snapEdges, 'snapEdges')\nexport { snapEdges }\n", "/* eslint-disable n/no-extraneous-import, import/no-unresolved */\nimport aspectRatio from './aspectRatio'\nimport avoid from './avoid/avoid'\nimport restrictEdges from './restrict/edges'\nimport restrict from './restrict/pointer'\nimport restrictRect from './restrict/rect'\nimport restrictSize from './restrict/size'\nimport rubberband from './rubberband/rubberband'\nimport snapEdges from './snap/edges'\nimport snap from './snap/pointer'\nimport snapSize from './snap/size'\nimport spring from './spring/spring'\nimport transform from './transform/transform'\n\nexport default {\n  aspectRatio,\n  restrictEdges,\n  restrict,\n  restrictRect,\n  restrictSize,\n  snapEdges,\n  snap,\n  snapSize,\n\n  spring,\n  avoid,\n  transform,\n  rubberband,\n}\n", "import type { Plugin } from '@interactjs/core/scope'\nimport snappers from '@interactjs/snappers/plugin'\n\n/* eslint-disable import/no-duplicates -- for typescript module augmentations */\nimport './all'\nimport './base'\n\nimport all from './all'\nimport base from './base'\n/* eslint-enable import/no-duplicates */\n\ndeclare module '@interactjs/core/InteractStatic' {\n  export interface InteractStatic {\n    modifiers: typeof all\n  }\n}\n\nconst modifiers: Plugin = {\n  id: 'modifiers',\n  install(scope) {\n    const { interactStatic: interact } = scope\n\n    scope.usePlugin(base)\n    scope.usePlugin(snappers)\n\n    interact.modifiers = all\n\n    // for backwrads compatibility\n    for (const type in all) {\n      const { _defaults, _methods } = all[type as keyof typeof all]\n\n      ;(_defaults as any)._methods = _methods\n      ;(scope.defaults.perAction as any)[type] = _defaults\n    }\n  },\n}\n\nexport default modifiers\n", "import { BaseEvent } from '@interactjs/core/BaseEvent'\nimport type Interaction from '@interactjs/core/Interaction'\nimport type { PointerEventType, PointerType, Point } from '@interactjs/core/types'\nimport * as pointerUtils from '@interactjs/utils/pointerUtils'\n\nexport class PointerEvent<T extends string = any> extends BaseEvent<never> {\n  declare type: T\n  declare originalEvent: PointerEventType\n  declare pointerId: number\n  declare pointerType: string\n  declare double: boolean\n  declare pageX: number\n  declare pageY: number\n  declare clientX: number\n  declare clientY: number\n  declare dt: number\n  declare eventable: any;\n  [key: string]: any\n\n  constructor(\n    type: T,\n    pointer: PointerType | PointerEvent<any>,\n    event: PointerEventType,\n    eventTarget: Node,\n    interaction: Interaction<never>,\n    timeStamp: number,\n  ) {\n    super(interaction)\n    pointerUtils.pointerExtend(this, event)\n\n    if (event !== pointer) {\n      pointerUtils.pointerExtend(this, pointer)\n    }\n\n    this.timeStamp = timeStamp\n    this.originalEvent = event\n    this.type = type\n    this.pointerId = pointerUtils.getPointerId(pointer)\n    this.pointerType = pointerUtils.getPointerType(pointer)\n    this.target = eventTarget\n    this.currentTarget = null\n\n    if (type === 'tap') {\n      const pointerIndex = interaction.getPointerIndex(pointer)\n      this.dt = this.timeStamp - interaction.pointers[pointerIndex].downTime\n\n      const interval = this.timeStamp - interaction.tapTime\n\n      this.double =\n        !!interaction.prevTap &&\n        interaction.prevTap.type !== 'doubletap' &&\n        interaction.prevTap.target === this.target &&\n        interval < 500\n    } else if (type === 'doubletap') {\n      this.dt = (pointer as PointerEvent<'tap'>).timeStamp - interaction.tapTime\n      this.double = true\n    }\n  }\n\n  _subtractOrigin({ x: originX, y: originY }: Point) {\n    this.pageX -= originX\n    this.pageY -= originY\n    this.clientX -= originX\n    this.clientY -= originY\n\n    return this\n  }\n\n  _addOrigin({ x: originX, y: originY }: Point) {\n    this.pageX += originX\n    this.pageY += originY\n    this.clientX += originX\n    this.clientY += originY\n\n    return this\n  }\n\n  /**\n   * Prevent the default behaviour of the original Event\n   */\n  preventDefault() {\n    this.originalEvent.preventDefault()\n  }\n}\n", "import type { Eventable } from '@interactjs/core/Eventable'\nimport type { Interaction } from '@interactjs/core/Interaction'\nimport type { PerActionDefaults } from '@interactjs/core/options'\nimport type { Scope, SignalArgs, Plugin } from '@interactjs/core/scope'\nimport type { Point, PointerType, PointerEventType, Element } from '@interactjs/core/types'\nimport * as domUtils from '@interactjs/utils/domUtils'\nimport extend from '@interactjs/utils/extend'\nimport getOriginXY from '@interactjs/utils/getOriginXY'\n\nimport { PointerEvent } from './PointerEvent'\n\nexport type EventTargetList = Array<{\n  node: Node\n  eventable: Eventable\n  props: { [key: string]: any }\n}>\n\nexport interface PointerEventOptions extends PerActionDefaults {\n  enabled?: undefined // not used\n  holdDuration?: number\n  ignoreFrom?: any\n  allowFrom?: any\n  origin?: Point | string | Element\n}\n\ndeclare module '@interactjs/core/scope' {\n  interface Scope {\n    pointerEvents: typeof pointerEvents\n  }\n}\n\ndeclare module '@interactjs/core/Interaction' {\n  interface Interaction {\n    prevTap?: PointerEvent<string>\n    tapTime?: number\n  }\n}\n\ndeclare module '@interactjs/core/PointerInfo' {\n  interface PointerInfo {\n    hold?: {\n      duration: number\n      timeout: any\n    }\n  }\n}\n\ndeclare module '@interactjs/core/options' {\n  interface ActionDefaults {\n    pointerEvents: Options\n  }\n}\n\ndeclare module '@interactjs/core/scope' {\n  interface SignalArgs {\n    'pointerEvents:new': { pointerEvent: PointerEvent<any> }\n    'pointerEvents:fired': {\n      interaction: Interaction<null>\n      pointer: PointerType | PointerEvent<any>\n      event: PointerEventType | PointerEvent<any>\n      eventTarget: Node\n      pointerEvent: PointerEvent<any>\n      targets?: EventTargetList\n      type: string\n    }\n    'pointerEvents:collect-targets': {\n      interaction: Interaction<any>\n      pointer: PointerType | PointerEvent<any>\n      event: PointerEventType | PointerEvent<any>\n      eventTarget: Node\n      targets?: EventTargetList\n      type: string\n      path: Node[]\n      node: null\n    }\n  }\n}\n\nconst defaults: PointerEventOptions = {\n  holdDuration: 600,\n  ignoreFrom: null,\n  allowFrom: null,\n  origin: { x: 0, y: 0 },\n}\n\nconst pointerEvents: Plugin = {\n  id: 'pointer-events/base',\n  before: ['inertia', 'modifiers', 'auto-start', 'actions'],\n  install,\n  listeners: {\n    'interactions:new': addInteractionProps,\n    'interactions:update-pointer': addHoldInfo,\n    'interactions:move': moveAndClearHold,\n    'interactions:down': (arg, scope) => {\n      downAndStartHold(arg, scope)\n      fire(arg, scope)\n    },\n    'interactions:up': (arg, scope) => {\n      clearHold(arg)\n      fire(arg, scope)\n      tapAfterUp(arg, scope)\n    },\n    'interactions:cancel': (arg, scope) => {\n      clearHold(arg)\n      fire(arg, scope)\n    },\n  },\n  PointerEvent,\n  fire,\n  collectEventTargets,\n  defaults,\n  types: {\n    down: true,\n    move: true,\n    up: true,\n    cancel: true,\n    tap: true,\n    doubletap: true,\n    hold: true,\n  } as { [type: string]: true },\n}\n\nfunction fire<T extends string>(\n  arg: {\n    pointer: PointerType | PointerEvent<any>\n    event: PointerEventType | PointerEvent<any>\n    eventTarget: Node\n    interaction: Interaction<never>\n    type: T\n    targets?: EventTargetList\n  },\n  scope: Scope,\n) {\n  const { interaction, pointer, event, eventTarget, type, targets = collectEventTargets(arg, scope) } = arg\n\n  const pointerEvent = new PointerEvent(type, pointer, event, eventTarget, interaction, scope.now())\n\n  scope.fire('pointerEvents:new', { pointerEvent })\n\n  const signalArg = {\n    interaction,\n    pointer,\n    event,\n    eventTarget,\n    targets,\n    type,\n    pointerEvent,\n  }\n\n  for (let i = 0; i < targets.length; i++) {\n    const target = targets[i]\n\n    for (const prop in target.props || {}) {\n      ;(pointerEvent as any)[prop] = target.props[prop]\n    }\n\n    const origin = getOriginXY(target.eventable, target.node)\n\n    pointerEvent._subtractOrigin(origin)\n    pointerEvent.eventable = target.eventable\n    pointerEvent.currentTarget = target.node\n\n    target.eventable.fire(pointerEvent)\n\n    pointerEvent._addOrigin(origin)\n\n    if (\n      pointerEvent.immediatePropagationStopped ||\n      (pointerEvent.propagationStopped &&\n        i + 1 < targets.length &&\n        targets[i + 1].node !== pointerEvent.currentTarget)\n    ) {\n      break\n    }\n  }\n\n  scope.fire('pointerEvents:fired', signalArg)\n\n  if (type === 'tap') {\n    // if pointerEvent should make a double tap, create and fire a doubletap\n    // PointerEvent and use that as the prevTap\n    const prevTap = pointerEvent.double\n      ? fire(\n          {\n            interaction,\n            pointer,\n            event,\n            eventTarget,\n            type: 'doubletap',\n          },\n          scope,\n        )\n      : pointerEvent\n\n    interaction.prevTap = prevTap\n    interaction.tapTime = prevTap.timeStamp\n  }\n\n  return pointerEvent\n}\n\nfunction collectEventTargets<T extends string>(\n  {\n    interaction,\n    pointer,\n    event,\n    eventTarget,\n    type,\n  }: {\n    interaction: Interaction<any>\n    pointer: PointerType | PointerEvent<any>\n    event: PointerEventType | PointerEvent<any>\n    eventTarget: Node\n    type: T\n  },\n  scope: Scope,\n) {\n  const pointerIndex = interaction.getPointerIndex(pointer)\n  const pointerInfo = interaction.pointers[pointerIndex]\n\n  // do not fire a tap event if the pointer was moved before being lifted\n  if (\n    type === 'tap' &&\n    (interaction.pointerWasMoved ||\n      // or if the pointerup target is different to the pointerdown target\n      !(pointerInfo && pointerInfo.downTarget === eventTarget))\n  ) {\n    return []\n  }\n\n  const path = domUtils.getPath(eventTarget as Element | Document)\n  const signalArg = {\n    interaction,\n    pointer,\n    event,\n    eventTarget,\n    type,\n    path,\n    targets: [] as EventTargetList,\n    node: null,\n  }\n\n  for (const node of path) {\n    signalArg.node = node\n\n    scope.fire('pointerEvents:collect-targets', signalArg)\n  }\n\n  if (type === 'hold') {\n    signalArg.targets = signalArg.targets.filter(\n      (target) =>\n        target.eventable.options.holdDuration === interaction.pointers[pointerIndex]?.hold?.duration,\n    )\n  }\n\n  return signalArg.targets\n}\n\nfunction addInteractionProps({ interaction }) {\n  interaction.prevTap = null // the most recent tap event on this interaction\n  interaction.tapTime = 0 // time of the most recent tap event\n}\n\nfunction addHoldInfo({ down, pointerInfo }: SignalArgs['interactions:update-pointer']) {\n  if (!down && pointerInfo.hold) {\n    return\n  }\n\n  pointerInfo.hold = { duration: Infinity, timeout: null }\n}\n\nfunction clearHold({ interaction, pointerIndex }) {\n  const hold = interaction.pointers[pointerIndex].hold\n\n  if (hold && hold.timeout) {\n    clearTimeout(hold.timeout)\n    hold.timeout = null\n  }\n}\n\nfunction moveAndClearHold(arg: SignalArgs['interactions:move'], scope: Scope) {\n  const { interaction, pointer, event, eventTarget, duplicate } = arg\n\n  if (!duplicate && (!interaction.pointerIsDown || interaction.pointerWasMoved)) {\n    if (interaction.pointerIsDown) {\n      clearHold(arg)\n    }\n\n    fire(\n      {\n        interaction,\n        pointer,\n        event,\n        eventTarget: eventTarget as Element,\n        type: 'move',\n      },\n      scope,\n    )\n  }\n}\n\nfunction downAndStartHold(\n  { interaction, pointer, event, eventTarget, pointerIndex }: SignalArgs['interactions:down'],\n  scope: Scope,\n) {\n  const timer = interaction.pointers[pointerIndex].hold!\n  const path = domUtils.getPath(eventTarget as Element | Document)\n  const signalArg = {\n    interaction,\n    pointer,\n    event,\n    eventTarget,\n    type: 'hold',\n    targets: [] as EventTargetList,\n    path,\n    node: null,\n  }\n\n  for (const node of path) {\n    signalArg.node = node\n\n    scope.fire('pointerEvents:collect-targets', signalArg)\n  }\n\n  if (!signalArg.targets.length) return\n\n  let minDuration = Infinity\n\n  for (const target of signalArg.targets) {\n    const holdDuration = target.eventable.options.holdDuration\n\n    if (holdDuration < minDuration) {\n      minDuration = holdDuration\n    }\n  }\n\n  timer.duration = minDuration\n  timer.timeout = setTimeout(() => {\n    fire(\n      {\n        interaction,\n        eventTarget,\n        pointer,\n        event,\n        type: 'hold',\n      },\n      scope,\n    )\n  }, minDuration)\n}\n\nfunction tapAfterUp(\n  { interaction, pointer, event, eventTarget }: SignalArgs['interactions:up'],\n  scope: Scope,\n) {\n  if (!interaction.pointerWasMoved) {\n    fire({ interaction, eventTarget, pointer, event, type: 'tap' }, scope)\n  }\n}\n\nfunction install(scope: Scope) {\n  scope.pointerEvents = pointerEvents\n  scope.defaults.actions.pointerEvents = pointerEvents.defaults\n  extend(scope.actions.phaselessTypes, pointerEvents.types)\n}\n\nexport default pointerEvents\n", "import type Interaction from '@interactjs/core/Interaction'\nimport type { <PERSON>er<PERSON>ap, <PERSON>ope, SignalArgs, Plugin } from '@interactjs/core/scope'\n\n/* eslint-disable import/no-duplicates -- for typescript module augmentations */\nimport './base'\nimport basePlugin from './base'\n/* eslint-enable import/no-duplicates */\nimport { type PointerEvent } from './PointerEvent'\n\ndeclare module '@interactjs/core/Interaction' {\n  interface Interaction {\n    holdIntervalHandle?: any\n  }\n}\n\ndeclare module '@interactjs/pointer-events/PointerEvent' {\n  interface PointerEvent<T extends string = any> {\n    count?: number\n  }\n}\n\ndeclare module '@interactjs/pointer-events/base' {\n  interface PointerEventOptions {\n    holdRepeatInterval?: number\n  }\n}\n\nfunction install(scope: Scope) {\n  scope.usePlugin(basePlugin)\n\n  const { pointerEvents } = scope\n\n  // don't repeat by default\n  pointerEvents.defaults.holdRepeatInterval = 0\n  pointerEvents.types.holdrepeat = scope.actions.phaselessTypes.holdrepeat = true\n}\n\nfunction onNew({ pointerEvent }: { pointerEvent: PointerEvent<any> }) {\n  if (pointerEvent.type !== 'hold') return\n\n  pointerEvent.count = (pointerEvent.count || 0) + 1\n}\n\nfunction onFired(\n  { interaction, pointerEvent, eventTarget, targets }: SignalArgs['pointerEvents:fired'],\n  scope: Scope,\n) {\n  if (pointerEvent.type !== 'hold' || !targets.length) return\n\n  // get the repeat interval from the first eventable\n  const interval = targets[0].eventable.options.holdRepeatInterval\n\n  // don't repeat if the interval is 0 or less\n  if (interval <= 0) return\n\n  // set a timeout to fire the holdrepeat event\n  interaction.holdIntervalHandle = setTimeout(() => {\n    scope.pointerEvents.fire(\n      {\n        interaction,\n        eventTarget,\n        type: 'hold',\n        pointer: pointerEvent,\n        event: pointerEvent,\n      },\n      scope,\n    )\n  }, interval)\n}\n\nfunction endHoldRepeat({ interaction }: { interaction: Interaction }) {\n  // set the interaction's holdStopTime property\n  // to stop further holdRepeat events\n  if (interaction.holdIntervalHandle) {\n    clearInterval(interaction.holdIntervalHandle)\n    interaction.holdIntervalHandle = null\n  }\n}\n\nconst holdRepeat: Plugin = {\n  id: 'pointer-events/holdRepeat',\n  install,\n  listeners: ['move', 'up', 'cancel', 'endall'].reduce(\n    (acc, enderTypes) => {\n      ;(acc as any)[`pointerEvents:${enderTypes}`] = endHoldRepeat\n      return acc\n    },\n    {\n      'pointerEvents:new': onNew,\n      'pointerEvents:fired': onFired,\n    } as ListenerMap,\n  ),\n}\n\nexport default holdRepeat\n", "import type { Interactable } from '@interactjs/core/Interactable'\nimport type { Scope, Plugin } from '@interactjs/core/scope'\nimport type { Element } from '@interactjs/core/types'\nimport extend from '@interactjs/utils/extend'\n\nimport type { PointerEventOptions } from '@interactjs/pointer-events/base'\n\ndeclare module '@interactjs/core/Interactable' {\n  interface Interactable {\n    pointerEvents(options: Partial<PointerEventOptions>): this\n    /** @internal */\n    __backCompatOption: (optionName: string, newValue: any) => any\n  }\n}\n\nfunction install(scope: Scope) {\n  const { Interactable } = scope\n\n  Interactable.prototype.pointerEvents = function (\n    this: Interactable,\n    options: Partial<PointerEventOptions>,\n  ) {\n    extend(this.events.options, options)\n\n    return this\n  }\n\n  const __backCompatOption = Interactable.prototype._backCompatOption\n\n  Interactable.prototype._backCompatOption = function (optionName, newValue) {\n    const ret = __backCompatOption.call(this, optionName, newValue)\n\n    if (ret === this) {\n      this.events.options[optionName] = newValue\n    }\n\n    return ret\n  }\n}\n\nconst plugin: Plugin = {\n  id: 'pointer-events/interactableTargets',\n  install,\n  listeners: {\n    'pointerEvents:collect-targets': ({ targets, node, type, eventTarget }, scope) => {\n      scope.interactables.forEachMatch(node, (interactable: Interactable) => {\n        const eventable = interactable.events\n        const options = eventable.options\n\n        if (\n          eventable.types[type] &&\n          eventable.types[type].length &&\n          interactable.testIgnoreAllow(options, node, eventTarget)\n        ) {\n          targets.push({\n            node,\n            eventable,\n            props: { interactable },\n          })\n        }\n      })\n    },\n\n    'interactable:new': ({ interactable }) => {\n      interactable.events.getRect = function (element: Element) {\n        return interactable.getRect(element)\n      }\n    },\n\n    'interactable:set': ({ interactable, options }, scope) => {\n      extend(interactable.events.options, scope.pointerEvents.defaults)\n      extend(interactable.events.options, options.pointerEvents || {})\n    },\n  },\n}\n\nexport default plugin\n", "import type { Plugin } from '@interactjs/core/scope'\n\n/* eslint-disable import/no-duplicates -- for typescript module augmentations */\nimport './base'\nimport './holdRepeat'\nimport './interactableTargets'\n\nimport * as pointerEvents from './base'\nimport holdRepeat from './holdRepeat'\nimport interactableTargets from './interactableTargets'\n/* eslint-enable import/no-duplicates */\n\nconst plugin: Plugin = {\n  id: 'pointer-events',\n  install(scope) {\n    scope.usePlugin(pointerEvents)\n    scope.usePlugin(holdRepeat)\n    scope.usePlugin(interactableTargets)\n  },\n}\n\nexport default plugin\n", "import type { Interactable } from '@interactjs/core/Interactable'\nimport type { DoAnyPhaseArg, Interaction } from '@interactjs/core/Interaction'\nimport type { Scope, Plugin } from '@interactjs/core/scope'\nimport type { ActionName, ActionProps, Element } from '@interactjs/core/types'\nimport * as arr from '@interactjs/utils/arr'\nimport { copyAction } from '@interactjs/utils/misc'\nimport * as pointerUtils from '@interactjs/utils/pointerUtils'\nimport { tlbrToXywh } from '@interactjs/utils/rect'\n\ndeclare module '@interactjs/core/scope' {\n  interface SignalArgs {\n    'interactions:before-action-reflow': Omit<DoAnyPhaseArg, 'iEvent'>\n    'interactions:action-reflow': DoAnyPhaseArg\n    'interactions:after-action-reflow': DoAnyPhaseArg\n  }\n}\n\ndeclare module '@interactjs/core/Interactable' {\n  interface Interactable {\n    /**\n     * ```js\n     * const interactable = interact(target)\n     * const drag = { name: drag, axis: 'x' }\n     * const resize = { name: resize, edges: { left: true, bottom: true }\n     *\n     * interactable.reflow(drag)\n     * interactable.reflow(resize)\n     * ```\n     *\n     * Start an action sequence to re-apply modifiers, check drops, etc.\n     *\n     * @param { Object } action The action to begin\n     * @param { string } action.name The name of the action\n     * @returns { Promise } A promise that resolves to the `Interactable` when actions on all targets have ended\n     */\n    reflow<T extends ActionName>(action: ActionProps<T>): ReturnType<typeof doReflow>\n  }\n}\n\ndeclare module '@interactjs/core/Interaction' {\n  interface Interaction {\n    _reflowPromise: Promise<void>\n    _reflowResolve: (...args: unknown[]) => void\n  }\n}\n\ndeclare module '@interactjs/core/InteractEvent' {\n  interface PhaseMap {\n    reflow?: true\n  }\n}\n\nfunction install(scope: Scope) {\n  const { Interactable } = scope\n\n  scope.actions.phases.reflow = true\n\n  Interactable.prototype.reflow = function (action: ActionProps) {\n    return doReflow(this, action, scope)\n  }\n}\n\nfunction doReflow<T extends ActionName>(\n  interactable: Interactable,\n  action: ActionProps<T>,\n  scope: Scope,\n): Promise<Interactable> {\n  const elements = interactable.getAllElements()\n\n  // tslint:disable-next-line variable-name\n  const Promise = (scope.window as any).Promise\n  const promises: Array<Promise<null>> | null = Promise ? [] : null\n\n  for (const element of elements) {\n    const rect = interactable.getRect(element as HTMLElement | SVGElement)\n\n    if (!rect) {\n      break\n    }\n\n    const runningInteraction = arr.find(scope.interactions.list, (interaction: Interaction) => {\n      return (\n        interaction.interacting() &&\n        interaction.interactable === interactable &&\n        interaction.element === element &&\n        interaction.prepared.name === action.name\n      )\n    })\n    let reflowPromise: Promise<null>\n\n    if (runningInteraction) {\n      runningInteraction.move()\n\n      if (promises) {\n        reflowPromise =\n          runningInteraction._reflowPromise ||\n          new Promise((resolve: any) => {\n            runningInteraction._reflowResolve = resolve\n          })\n      }\n    } else {\n      const xywh = tlbrToXywh(rect)\n      const coords = {\n        page: { x: xywh.x, y: xywh.y },\n        client: { x: xywh.x, y: xywh.y },\n        timeStamp: scope.now(),\n      }\n\n      const event = pointerUtils.coordsToEvent(coords)\n      reflowPromise = startReflow<T>(scope, interactable, element, action, event)\n    }\n\n    if (promises) {\n      promises.push(reflowPromise)\n    }\n  }\n\n  return promises && Promise.all(promises).then(() => interactable)\n}\n\nfunction startReflow<T extends ActionName>(\n  scope: Scope,\n  interactable: Interactable,\n  element: Element,\n  action: ActionProps<T>,\n  event: any,\n) {\n  const interaction = scope.interactions.new({ pointerType: 'reflow' })\n  const signalArg = {\n    interaction,\n    event,\n    pointer: event,\n    eventTarget: element,\n    phase: 'reflow',\n  } as const\n\n  interaction.interactable = interactable\n  interaction.element = element\n  interaction.prevEvent = event\n  interaction.updatePointer(event, event, element, true)\n  pointerUtils.setZeroCoords(interaction.coords.delta)\n\n  copyAction(interaction.prepared, action)\n  interaction._doPhase(signalArg)\n\n  const { Promise } = scope.window as unknown as { Promise: PromiseConstructor }\n  const reflowPromise = Promise\n    ? new Promise<undefined>((resolve) => {\n        interaction._reflowResolve = resolve\n      })\n    : undefined\n\n  interaction._reflowPromise = reflowPromise\n  interaction.start(action, interactable, element)\n\n  if (interaction._interacting) {\n    interaction.move(signalArg)\n    interaction.end(event)\n  } else {\n    interaction.stop()\n    interaction._reflowResolve()\n  }\n\n  interaction.removePointer(event, event)\n\n  return reflowPromise\n}\n\nconst reflow: Plugin = {\n  id: 'reflow',\n  install,\n  listeners: {\n    // remove completed reflow interactions\n    'interactions:stop': ({ interaction }, scope) => {\n      if (interaction.pointerType === 'reflow') {\n        if (interaction._reflowResolve) {\n          interaction._reflowResolve()\n        }\n\n        arr.remove(scope.interactions.list, interaction)\n      }\n    },\n  },\n}\n\nexport default reflow\n", "// eslint-disable-next-line import/no-extraneous-dependencies\nimport interact from '@interactjs/interactjs'\n\nexport default interact\n\nif (typeof module === 'object' && !!module) {\n  try {\n    module.exports = interact\n  } catch {}\n}\n\n;(interact as any).default = interact\n", "/* eslint-disable import/no-duplicates -- for typescript module augmentations */\nimport '@interactjs/actions/plugin'\nimport '@interactjs/auto-scroll/plugin'\nimport '@interactjs/auto-start/plugin'\nimport '@interactjs/core/interactablePreventDefault'\nimport '@interactjs/dev-tools/plugin'\nimport '@interactjs/inertia/plugin'\nimport '@interactjs/interact'\nimport '@interactjs/modifiers/plugin'\nimport '@interactjs/offset/plugin'\nimport '@interactjs/pointer-events/plugin'\nimport '@interactjs/reflow/plugin'\n\nimport actions from '@interactjs/actions/plugin'\nimport autoScroll from '@interactjs/auto-scroll/plugin'\nimport autoStart from '@interactjs/auto-start/plugin'\nimport interactablePreventDefault from '@interactjs/core/interactablePreventDefault'\nimport devTools from '@interactjs/dev-tools/plugin'\nimport inertia from '@interactjs/inertia/plugin'\nimport interact from '@interactjs/interact'\nimport modifiers from '@interactjs/modifiers/plugin'\nimport offset from '@interactjs/offset/plugin'\nimport pointerEvents from '@interactjs/pointer-events/plugin'\nimport reflow from '@interactjs/reflow/plugin'\n/* eslint-enable import/no-duplicates */\n\ninteract.use(interactablePreventDefault)\n\ninteract.use(offset)\n\n// pointerEvents\ninteract.use(pointerEvents)\n\n// inertia\ninteract.use(inertia)\n\n// snap, resize, etc.\ninteract.use(modifiers)\n\n// autoStart, hold\ninteract.use(autoStart)\n\n// drag and drop, resize, gesture\ninteract.use(actions)\n\n// autoScroll\ninteract.use(autoScroll)\n\n// reflow\ninteract.use(reflow)\n\n// eslint-disable-next-line no-undef\nif (process.env.NODE_ENV !== 'production') {\n  interact.use(devTools)\n}\n\nexport default interact\n;(interact as any).default = interact\n"], "names": ["isWindow", "thing", "Window", "realWindow", "undefined", "win", "init", "window", "el", "document", "createTextNode", "ownerDocument", "wrap", "getWindow", "node", "defaultView", "object", "_typeof", "func", "is", "docFrag", "nodeType", "number", "bool", "string", "element", "_window", "test", "Element", "nodeName", "plainObject", "constructor", "toString", "array", "length", "splice", "beforeMove", "_ref", "interaction", "prepared", "name", "axis", "coords", "cur", "page", "y", "start", "client", "velocity", "x", "move", "_ref2", "iEvent", "opposite", "delta", "drag", "id", "install", "scope", "actions", "Interactable", "defaults", "prototype", "draggable", "map", "methodDict", "listeners", "arg", "interactable", "buttons", "dragOptions", "options", "enabled", "pointerIsDown", "pointerType", "mouseButtons", "action", "lockAxis", "startAxis", "this", "setPerAction", "setOnEvents", "getCursor", "filterEventType", "type", "search", "drag$1", "domObjects", "DocumentFragment", "blank", "SVGElement", "SVGSVGElement", "SVGElementInstance", "HTMLElement", "Event", "Touch", "PointerEvent", "MSPointerEvent", "domObjects$1", "browser", "navigator", "supportsTouch", "DocumentTouch", "supportsPointerEvent", "pointer<PERSON><PERSON>bled", "isIOS", "platform", "isIOS7", "appVersion", "isIe9", "userAgent", "isOperaMobile", "appName", "prefixedMatchesSelector", "pEventTypes", "up", "down", "over", "out", "cancel", "wheelEvent", "browser$1", "nodeContains", "parent", "child", "contains", "parentNode", "closest", "selector", "matchesSelector", "host", "replace", "getParent", "getNodeParents", "limit", "parentParent", "parents", "unshift", "matchesUpTo", "getActualElement", "correspondingUseElement", "getElementClientRect", "clientRect", "getBoundingClientRect", "getClientRects", "left", "right", "top", "bottom", "width", "height", "getElementRect", "relevantWindow", "scroll", "scrollX", "documentElement", "scrollLeft", "scrollY", "scrollTop", "<PERSON><PERSON><PERSON>", "path", "push", "trySelector", "value", "querySelector", "extend", "dest", "source", "prop", "getStringOptionResult", "target", "getRect", "resolveRectLike", "functionArgs", "returnValue", "apply", "rectToXY", "rect", "tlbrToXywh", "addEdges", "edges", "getOriginXY", "actionName", "actionOptions", "origin", "normalize", "filter", "arguments", "_typeOrPrefix", "result", "split", "for<PERSON>ach", "t", "_i2", "_listeners2", "l", "prefix", "p", "concat", "trim", "hypot", "Math", "sqrt", "VENDOR_PREFIXES", "pointerExtend", "__set", "_loop", "some", "indexOf", "Object", "defineProperty", "get", "set", "configurable", "copyCoords", "src", "timeStamp", "setZeroCoords", "targetObj", "isNativePointer", "pointer", "dom", "getXY", "xy", "getPageXY", "getPointerId", "pointerId", "identifier", "setCoords", "pointers", "pointerAverage", "getClientXY", "getTouchPair", "event", "touches", "changedTouches", "average", "pageX", "pageY", "clientX", "clientY", "screenX", "screenY", "touchBBox", "minX", "min", "minY", "maxX", "max", "maxY", "touchDistance", "deltaSource", "sourceX", "sourceY", "dx", "dy", "touchAngle", "atan2", "PI", "getPointerType", "getEventTargets", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "currentTarget", "BaseEvent", "_classCallCheck", "immediatePropagationStopped", "propagationStopped", "_interaction", "_createClass", "key", "_proxy", "merge", "item", "from", "findIndex", "i", "find", "DropEvent", "_BaseEvent", "_inherits", "_super", "_createSuper", "dropState", "dragEvent", "_this", "call", "dropzone", "relatedTarget", "prev", "_this2", "rejected", "events", "enter", "stopImmediatePropagation", "activeDrops", "index", "arr", "deactivateEvent", "fire", "fireActivationEvents", "_i6", "_activeDrops$slice2", "slice", "_activeDrops$slice2$_", "getActiveDrops", "dragElement", "draggableElement", "drops", "_interactables$list2", "interactables", "list", "drop", "accept", "_i4", "_dropzone$getAllEleme2", "getAllElements", "dropzoneElement", "collectDropzones", "_i8", "activeDrop", "getDrop", "pointerEvent", "validDrops", "_i10", "_dropState$activeDrop2", "_dropState$activeDrop3", "<PERSON><PERSON><PERSON><PERSON>", "dropCheck", "dropIndex", "elements", "deepestNodeIndex", "higherNode", "lowerNode", "deepestNodeParents", "currentNode", "deepestNode", "currentNodeParent", "deepestNodeParent", "ancestryStart", "ownerSVGElement", "currentNodeParents", "commonIndex", "<PERSON><PERSON><PERSON><PERSON>", "previousSibling", "higherIndex", "lowerIndex", "parseInt", "getComputedStyle", "zIndex", "getDropEvents", "_pointerEvent", "dropEvents", "leave", "activate", "deactivate", "dragLeave", "prevDropzone", "dragEnter", "fireDropEvents", "onEventCreated", "_ref3", "dynamicDrop", "dropResult", "interact", "interactStatic", "usePlugin", "normalized", "normalizeListeners", "corrected", "keys", "reduce", "acc", "prevListeners", "off", "on", "ondrop", "ondropactivate", "ondropdeactivate", "ondragenter", "ondragleave", "ondropmove", "overlap", "checker", "dropzoneMethod", "dropElement", "dropped", "dropOverlap", "pointerUtils", "horizontal", "vertical", "dragRect", "cx", "cy", "dropCheckMethod", "newValue", "phaselessTypes", "dragenter", "dragleave", "dropactivate", "dropdeactivate", "dropmove", "_ref4", "_ref5", "_ref6", "_ref7", "drop$1", "updateGestureProps", "phase", "starting", "ending", "distance", "box", "scale", "ds", "angle", "da", "gesture", "startDistance", "startAngle", "prevEvent", "Infinity", "isNaN", "before", "gesturable", "gestureOptions", "gesture$1", "checkResizeEdge", "interactableElement", "margin", "abs", "edge", "updateEventAxes", "resizeAxes", "resizeEvent", "resize", "square", "axes", "request", "cursors", "topleft", "bottomright", "topright", "bottomleft", "initCursors", "defaultMargin", "resizable", "preserveAspectRatio", "_rects", "previous", "deltaRect", "invert", "invertible", "current", "_interaction$_rects", "startRect", "swap", "resizeOptions", "resizeEdges", "_latestPointer", "eventTarget", "NaN", "cursor<PERSON><PERSON>", "_ref8", "resize$1", "lastTime", "raf", "callback", "token", "global", "requestAnimationFrame", "cancelAnimationFrame", "vendors", "vendor", "bind", "currTime", "Date", "now", "timeToCall", "setTimeout", "clearTimeout", "autoScroll", "container", "speed", "isScrolling", "prevTime", "stop", "getContainer", "dt", "s", "scrollBy", "prevScroll", "getScroll", "curScroll", "check", "_options$actionName$a", "onInteractionMove", "interacting", "simulation", "innerWidth", "innerHeight", "body", "autoScrollPlugin", "autoscroll", "perAction", "autoScroll$1", "warnOnce", "method", "message", "warned", "console", "warn", "copyAction", "styleCursor", "actionChecker", "InteractableMethods", "getAction", "button", "defaultActionChecker", "ignoreFrom", "_backCompatOption", "allowFrom", "validateAction", "testIgnoreAllow", "withinInteractionLimit", "validate<PERSON><PERSON><PERSON>", "matches", "matchElements", "len", "match", "matchElement", "matchAction", "getActionInfo", "pushMatches", "forEachMatch", "actionInfo", "manualStart", "prepare", "setInteractionCursor", "maxActions", "maxPer<PERSON><PERSON>", "autoStartMax", "autoStart", "maxInteractions", "activeInteractions", "interactableCount", "elementCount", "_scope$interactions$l2", "interactions", "otherAction", "setCursor", "cursor", "prevCursorElement", "cursorElement", "style", "cursor<PERSON><PERSON><PERSON>", "_interacting", "base", "prepareOnMove", "pointer<PERSON><PERSON><PERSON>oved", "startOnMove", "autoStart$2", "dragAxis", "absX", "absY", "targetOptions", "currentAxis", "getDraggable", "downPointer", "downEvent", "thisAxis", "checkStartAxis", "getHoldDuration", "hold", "delay", "basePlugin", "autoStartHoldTimer", "duplicate", "hold$1", "preventDefault", "onInteractionEvent", "checkAndPreventDefault", "interactablePreventDefault", "setting", "supportsPassive", "doc", "docOptions", "getDocOptions", "passive", "docEvents", "listener", "eventType", "isNonNativeEvent", "substr", "phases", "clone", "Modification", "states", "startOffset", "startDelta", "endResult", "startEdges", "createResult", "pageCoords", "modifierList", "actionModifiers", "modifiers", "methods", "_methods", "m", "getModifierList", "prepareStates", "fillArg", "preEnd", "startAll", "setAll", "_this$states2", "state", "skipModifiers", "unmodifiedRect", "unmodifiedEdges", "newResult", "_state$methods", "lastModifierCoords", "shouldDo", "rectUtils", "eventProps", "rectD<PERSON><PERSON>", "prevCoords", "prevRect", "rectChanged", "changed", "curCoords", "startCoords", "cur<PERSON><PERSON><PERSON>", "_ref3$_i", "coordsSet", "modifiedCoords", "adjustment", "applyToInteraction", "<PERSON><PERSON><PERSON><PERSON>", "endPosition", "beforeEnd", "modifierArg", "_this$states4", "_modifierList$index", "_ref4$interaction", "modification", "_modification$result", "_i12", "_ref6$_i", "requireEndOnly", "endOnly", "setStart", "other", "makeModifier", "module", "modifier", "_options", "enable", "disable", "_defaults", "addEventModifiers", "modifiersBase", "ret", "setAndApply", "restoreInteractionCoords", "InteractEvent", "ctrl<PERSON>ey", "shift<PERSON>ey", "altKey", "metaKey", "x0", "y0", "t0", "duration", "clientX0", "clientY0", "swipe", "_assertThisInitialized", "downTime", "getSwipe", "velocityY", "velocityX", "defineProperties", "PointerInfo", "downTarget", "_ProxyValues", "_ProxyMethods", "idCounter", "Interaction", "scopeFire", "_scopeFire", "_ending", "_stopped", "do<PERSON>ove", "signalArg", "_id", "that", "_loop2", "_key", "pointerIndex", "updatePointer", "pointerInfo", "_doPhase", "duplicateMove", "pointerMoveTolerance", "getPointerIndex", "curE<PERSON><PERSON><PERSON><PERSON>", "end", "removePointer", "endPhaseResult", "curPointer", "_now", "_updateLatestPointer", "_this$interactable", "_createPreparedEvent", "_fireEvent", "beforeAction", "applyPending", "offset", "pending", "hasPending", "addToCoords", "offsetBy", "total", "addTotal", "offset$1", "InertiaState", "active", "isModified", "smoothEnd", "allowResume", "modifierCount", "v0", "te", "targetOffset", "modifiedOffset", "currentOffset", "lambda_v0", "one_ve_v0", "timeout", "getOptions", "velocityClient", "pointerSpeed", "copyFrom", "minSpeed", "endSpeed", "startInertia", "startSmoothEnd", "startVelocity", "lambda", "resistance", "inertiaDur", "log", "onNextFrame", "inertiaTick", "smoothEndTick", "tickFn", "_this3", "startX", "startY", "cpX", "cpY", "endX", "endY", "position", "_this4", "newOffset", "progress", "exp", "_getQBezierValue", "_this5", "smoothEndDuration", "easeOutQuad", "inertia", "inertiastart", "resume", "p1", "p2", "p3", "iT", "b", "c", "d", "inertia$1", "fireUntilImmediateStopped", "Eventable", "types", "eventList", "_listeners$type2", "subListener", "_element", "FakeEvent", "originalEvent", "pExtend", "stopPropagation", "param", "capture", "optionsMatch", "a", "_scope$document", "targets", "delegatedEvents", "documents", "eventsMethods", "add", "remove", "addDelegate", "context", "optionalArg", "_i3", "<PERSON><PERSON><PERSON><PERSON>", "delegateUseCapture", "delegates", "delegate", "removeDelegate", "matchFound", "entry", "supportsOptions", "addEventListener", "removeEventListener", "targetIndex", "typeIsEmpty", "typeListeners", "hasOwnProperty", "fakeEvent", "_i5", "createElement", "finder", "methodOrder", "details", "_finder$methodOrder2", "simulationResume", "mouseOrPen", "firstNonActive", "_scope$interactions$l4", "hasPointerId", "_scope$interactions$l6", "hasPointer", "_scope$interactions$l8", "idle", "_scope$interactions$l10", "finder$1", "methodNames", "doOnInteractions", "_pointerUtils$getEven", "prevTouchTime", "_event$changedTouches2", "changedTouch", "searchDetails", "getInteraction", "invalidPointer", "_matches$_i", "new", "onDocSignal", "eventMethodName", "eventMethod", "eventOptions", "_i14", "_docEvents$_i", "releasePointersOnRemovedEls", "_interaction$pointers2", "pointerDown", "pointer<PERSON><PERSON>", "pointerUp", "documentBlur", "_InteractionBase", "_class", "InteractionBase", "destroy", "interactions$1", "OnOffMethod", "defaultContext", "scopeEvents", "_actions", "_context", "_win", "_doc", "_scopeEvents", "onstart", "onmove", "onend", "oninertiastart", "_this$_actions$map$ac", "actionFilter", "_onOff", "Off", "On", "optionName_", "optionName", "optionValue", "updatePerActionListeners", "Array", "querySelectorAll", "targetNode", "testIgnore", "testAllow", "typeArg", "listenerArg", "_listeners$_type2", "actionName_", "methodName", "rect<PERSON><PERSON><PERSON>", "delegated", "_delegated$i", "InteractableSet", "selectorMap", "addListeners", "interactablesOnTarget", "addDocument", "isSelector", "inContext", "_this$list2", "<PERSON><PERSON>", "floor", "random", "isInitialized", "listenerMaps", "getExisting", "globalEvents", "getPointerAverage", "getTouchBBox", "getTouchDistance", "getTouchAngle", "version", "process", "use", "plugin", "isSet", "_type2", "debug", "_this$scope$interacti2", "removeDocument", "createInteractStatic", "_plugins", "onWindowUnload", "_InteractableBase", "_class2", "_get", "_getPrototypeOf", "InteractableBase", "_this$listenerMaps2", "initScope", "pluginIsInstalled", "pluginIdRoot", "otherId", "getDocIndex", "docIndex", "interact$1", "_global", "globalThis", "grid", "coord<PERSON>ields", "xField", "yField", "gridFunc", "range", "_grid$limits", "limits", "_grid$offset", "_coordFields$_i", "gridx", "round", "gridy", "snappersPlugin", "snappers", "allSnappers", "createSnapGrid", "aspectRatio", "_state$options", "ratio", "_state$options2", "equalDel<PERSON>", "linkedEdges", "xIsPrimaryAxis", "sign", "edgeSign", "subModification", "_objectSpread", "initialCoords", "aspectMethod", "setEqualDelta", "setRatio", "correctedRect", "newHeight", "newWidth", "aspectRatio$1", "noop", "rubberband", "getRestrictionRect", "restrict", "elementRect", "restriction", "widthDiff", "heightDiff", "restrict$1", "noInner", "noOuter", "fixRect", "restrictEdges", "inner", "outer", "restrictEdges$1", "_", "restrictRect$1", "noMin", "noMax", "restrictSize$1", "minSize", "maxSize", "snap", "snapOffset", "offsetWithOrigin", "options<PERSON><PERSON>in", "<PERSON><PERSON><PERSON><PERSON>", "offsetRect", "relativePoints", "offsets", "relativePoint", "relativeX", "relativeY", "snap<PERSON>arget", "inRange", "snap$1", "snapSize", "targetFields", "relative", "_state$targetFields2", "_state$targetFields2$", "snapSize$1", "all", "restrictRect", "restrictSize", "<PERSON><PERSON><PERSON>", "spring", "avoid", "transform", "_all", "modifiers$1", "interval", "tapTime", "double", "prevTap", "originX", "originY", "pointerEvents", "clearHold", "timer", "minDuration", "_signalArg$targets2", "holdDuration", "eventable", "downAndStartHold", "tapAfterUp", "collectEventTargets", "tap", "doubletap", "_arg$targets", "props", "_subtract<PERSON><PERSON>in", "_addOrigin", "_interaction$pointers", "endHoldRepeat", "holdIntervalHandle", "clearInterval", "holdRepeat", "holdRepeatInterval", "holdrepeat", "enderTypes", "count", "holdRepeat$1", "__backCompatOption", "interactableTargets", "reflow", "Promise", "promises", "reflowPromise", "runningInteraction", "_reflowPromise", "resolve", "_reflowResolve", "xywh", "startReflow", "then", "doReflow", "reflow$1", "default", "exports", "_unused"], "mappings": ";;83GAAA,IAAAA,EAAA,SAAgBC,GAAU,SAAQA,IAASA,EAAMC,SAAWD,aAAiBA,EAAMC,MAAM,ECE9EC,OAAaC,EAEpBC,OAAMD,EAGH,SAASE,EAAKC,GAGnBJ,EAAaI,EAGb,IAAMC,EAAKD,EAAOE,SAASC,eAAe,IAGtCF,EAAGG,gBAAkBJ,EAAOE,UAAmC,mBAAhBF,EAAOK,MAAuBL,EAAOK,KAAKJ,KAAQA,IAEnGD,EAASA,EAAOK,KAAKL,IAGvBF,EAAME,CACR,CAMO,SAASM,EAAUC,GACxB,OAAId,EAASc,GACJA,GAGQA,EAAKH,eAAiBG,GAEvBC,aAAeV,EAAIE,MACrC,CAZsB,oBAAXA,QAA4BA,QACrCD,EAAKC,QCtBP,IAIMS,EAAS,SAACf,GAAU,QAA0CA,GAA0B,WAAjBgB,EAAOhB,EAAkB,EAEhGiB,EAAO,SAACjB,GAAU,MAAwD,mBAAVA,CAAoB,EA0B3EkB,EAAA,CACbZ,OAjCa,SAACN,GAAU,OAAsBA,IAAUI,GAAcL,EAASC,EAAM,EAkCrFmB,QAhCc,SAACnB,GAAU,OAAgCe,EAAOf,IAA6B,KAAnBA,EAAMoB,QAAe,EAiC/FL,OAAAA,EACAE,KAAAA,EACAI,OA7Ba,SAACrB,GAAU,MAAuC,iBAAVA,CAAkB,EA8BvEsB,KA5BW,SAACtB,GAAU,MAAwC,kBAAVA,CAAmB,EA6BvEuB,OA3Ba,SAACvB,GAAU,MAAuC,iBAAVA,CAAkB,EA4BvEwB,QA1Bc,SAACxB,GACf,IAAKA,GAA0B,WAAjBgB,EAAOhB,GACnB,OAAO,EAGT,IAAMyB,EAAUrB,EAAcJ,IAAUI,EAExC,MAAO,kBAAkBsB,KAAmBV,oBAAPW,QAAOX,YAAAA,EAAPW,UACjC3B,aAAiB2B,SAAW3B,aAAiByB,EAAQE,QAClC,IAAnB3B,EAAMoB,UAA4C,iBAAnBpB,EAAM4B,QAC3C,EAiBEC,YAfiC,SAAC7B,GAAU,OAC5Ce,EAAOf,MAAYA,EAAM8B,aAAe,oBAAoBJ,KAAK1B,EAAM8B,YAAYC,WAAW,EAe9FC,MAbY,SAAoBhC,GAAU,OAC1Ce,EAAOf,SAAkC,IAAjBA,EAAMiC,QAA0BhB,EAAKjB,EAAMkC,OAAO,GCmD5E,SAASC,EAAUC,GAAkB,IAAfC,EAAWD,EAAXC,YACpB,GAAkC,SAA9BA,EAAYC,SAASC,KAAzB,CAEA,IAAMC,EAAOH,EAAYC,SAASE,KAErB,MAATA,GACFH,EAAYI,OAAOC,IAAIC,KAAKC,EAAIP,EAAYI,OAAOI,MAAMF,KAAKC,EAC9DP,EAAYI,OAAOC,IAAII,OAAOF,EAAIP,EAAYI,OAAOI,MAAMC,OAAOF,EAElEP,EAAYI,OAAOM,SAASD,OAAOF,EAAI,EACvCP,EAAYI,OAAOM,SAASJ,KAAKC,EAAI,GACnB,MAATJ,IACTH,EAAYI,OAAOC,IAAIC,KAAKK,EAAIX,EAAYI,OAAOI,MAAMF,KAAKK,EAC9DX,EAAYI,OAAOC,IAAII,OAAOE,EAAIX,EAAYI,OAAOI,MAAMC,OAAOE,EAElEX,EAAYI,OAAOM,SAASD,OAAOE,EAAI,EACvCX,EAAYI,OAAOM,SAASJ,KAAKK,EAAI,EAfG,CAiB5C,CAEA,SAASC,EAAIC,GAA0B,IAAvBC,EAAMD,EAANC,OAAQd,EAAWa,EAAXb,YACtB,GAAkC,SAA9BA,EAAYC,SAASC,KAAzB,CAEA,IAAMC,EAAOH,EAAYC,SAASE,KAElC,GAAa,MAATA,GAAyB,MAATA,EAAc,CAChC,IAAMY,EAAoB,MAATZ,EAAe,IAAM,IAEtCW,EAAOR,KAAKS,GAAYf,EAAYI,OAAOI,MAAMF,KAAKS,GACtDD,EAAOL,OAAOM,GAAYf,EAAYI,OAAOI,MAAMC,OAAOM,GAC1DD,EAAOE,MAAMD,GAAY,CAC3B,CAV0C,CAW5C,CAEA,IA4BME,EAAe,CACnBC,GAAI,eACJC,QA3EF,SAAiBC,GACf,IAAQC,EAAoCD,EAApCC,QAASC,EAA2BF,EAA3BE,aAAcC,EAAaH,EAAbG,SAE/BD,EAAaE,UAAUC,UAAYR,EAAKQ,UAExCJ,EAAQK,IAAIT,KAAOA,EACnBI,EAAQM,WAAWV,KAAO,YAE1BM,EAASF,QAAQJ,KAAOA,EAAKM,QAC/B,EAmEEK,UAAW,CACT,kCAAmC9B,EACnC,6BAA8BA,EAG9B,2BAA4Bc,EAC5B,mBAAoB,SAACiB,GACnB,IAAQ7B,EAAuC6B,EAAvC7B,YAAa8B,EAA0BD,EAA1BC,aAAcC,EAAYF,EAAZE,QAC7BC,EAAcF,EAAaG,QAAQhB,KAEzC,GACIe,GAAeA,EAAYE,WAE5BlC,EAAYmC,gBACX,gBAAgB9C,KAAKW,EAAYoC,cACsB,IAAtDL,EAAUD,EAAaG,QAAQhB,KAAKoB,eAUzC,OALAR,EAAIS,OAAS,CACXpC,KAAM,OACNC,KAA+B,UAAzB6B,EAAYO,SAAuBP,EAAYQ,UAAYR,EAAYO,WAGxE,CACT,GAEFd,UA3D2C,SAE3CQ,GAEA,OAAIpD,EAAGH,OAAOuD,IACZQ,KAAKR,QAAQhB,KAAKiB,SAA8B,IAApBD,EAAQC,QACpCO,KAAKC,aAAa,OAAQT,GAC1BQ,KAAKE,YAAY,OAAQV,GAErB,mBAAmB5C,KAAK4C,EAAQM,YAClCE,KAAKR,QAAQhB,KAAKsB,SAAWN,EAAQM,UAEnC,aAAalD,KAAK4C,EAAQO,aAC5BC,KAAKR,QAAQhB,KAAKuB,UAAYP,EAAQO,WAGjCC,MAGL5D,EAAGI,KAAKgD,IACVQ,KAAKR,QAAQhB,KAAKiB,QAAUD,EAErBQ,MAGFA,KAAKR,QAAQhB,IACtB,EAkCEnB,WAAAA,EACAc,KAAAA,EACAW,SAAU,CACRiB,UAAW,KACXD,SAAU,MAGZK,UAAS,WACP,MAAO,MACR,EAEDC,gBAAiB,SAACC,GAAY,OAA6B,IAAxBA,EAAKC,OAAO,OAAa,GAG9DC,EAAe/B,EChMTgC,EAYF,CACFjF,KAiBF,SAAcC,GACZ,IAAMF,EAAME,EAEZgF,EAAW9E,SAAWJ,EAAII,SAC1B8E,EAAWC,iBAAmBnF,EAAImF,kBAAoBC,EACtDF,EAAWG,WAAarF,EAAIqF,YAAcD,EAC1CF,EAAWI,cAAgBtF,EAAIsF,eAAiBF,EAChDF,EAAWK,mBAAqBvF,EAAIuF,oBAAsBH,EAC1DF,EAAW3D,QAAUvB,EAAIuB,SAAW6D,EACpCF,EAAWM,YAAcxF,EAAIwF,aAAeN,EAAW3D,QAEvD2D,EAAWO,MAAQzF,EAAIyF,MACvBP,EAAWQ,MAAQ1F,EAAI0F,OAASN,EAChCF,EAAWS,aAAe3F,EAAI2F,cAAgB3F,EAAI4F,cACpD,EA9BExF,SAAU,KACV+E,iBAAkB,KAClBE,WAAY,KACZC,cAAe,KACfC,mBAAoB,KACpBhE,QAAS,KACTiE,YAAa,KACbC,MAAO,KACPC,MAAO,KACPC,aAAc,MAGhB,SAASP,IAAS,CAElB,IAAAS,EAAeX,ECzBf,IAAMY,EAAU,CACd7F,KAmBF,SAAcC,GACZ,IAAMqB,EAAU2D,EAAW3D,QACrBwE,EAAgC7F,EAAO6F,WAAa,GAG1DD,EAAQE,cACN,iBAAkB9F,GACjBY,EAAGD,KAAKX,EAAO+F,gBAAkBf,EAAW9E,oBAAoBF,EAAO+F,cAI1EH,EAAQI,sBAA6D,IAArCH,EAAkBI,kBAA8BjB,EAAWS,aAE3FG,EAAQM,MAAQ,iBAAiB9E,KAAKyE,EAAUM,UAGhDP,EAAQQ,OAAS,iBAAiBhF,KAAKyE,EAAUM,WAAa,YAAY/E,KAAKyE,EAAUQ,YAEzFT,EAAQU,MAAQ,SAASlF,KAAKyE,EAAUU,WAGxCX,EAAQY,cACgB,UAAtBX,EAAUY,SAAuBb,EAAQE,eAAiB,SAAS1E,KAAKyE,EAAUU,WAGpFX,EAAQc,wBACN,YAAarF,EAAQkC,UACjB,UACA,0BAA2BlC,EAAQkC,UACjC,wBACA,uBAAwBlC,EAAQkC,UAC9B,qBACA,qBAAsBlC,EAAQkC,UAC5B,mBACA,oBAGZqC,EAAQe,YAAcf,EAAQI,qBAC1BhB,EAAWS,eAAiBzF,EAAO0F,eACjC,CACEkB,GAAI,cACJC,KAAM,gBACNC,KAAM,YACNC,IAAK,WACLpE,KAAM,gBACNqE,OAAQ,mBAEV,CACEJ,GAAI,YACJC,KAAM,cACNC,KAAM,cACNC,IAAK,aACLpE,KAAM,cACNqE,OAAQ,iBAEZ,KAGJpB,EAAQqB,WAAajC,EAAW9E,UAAY,iBAAkB8E,EAAW9E,SAAW,aAAe,OACrG,EA7EE4F,cAAe,KACfE,qBAAsB,KACtBI,OAAQ,KACRF,MAAO,KACPI,MAAO,KACPE,cAAe,KACfE,wBAAyB,KACzBC,YAAa,KAQbM,WAAY,MAgEd,IAAAC,EAAetB,EC7ER,SAASuB,EAAaC,EAAcC,GACzC,GAAID,EAAOE,SACT,OAAOF,EAAOE,SAASD,GAGzB,KAAOA,GAAO,CACZ,GAAIA,IAAUD,EACZ,OAAO,EAGTC,EAASA,EAAeE,UAC1B,CAEA,OAAO,CACT,CAEO,SAASC,EAAQtG,EAAeuG,GACrC,KAAO7G,EAAGM,QAAQA,IAAU,CAC1B,GAAIwG,EAAgBxG,EAASuG,GAC3B,OAAOvG,EAGTA,EAAUqG,EAAWrG,EACvB,CAEA,OAAO,IACT,CAEO,SAASqG,EAAWhH,GACzB,IAAI6G,EAAS7G,EAAKgH,WAElB,GAAI3G,EAAGC,QAAQuG,GAAS,CAGtB,MAAQA,EAAUA,EAAeO,OAAS/G,EAAGC,QAAQuG,KAIrD,OAAOA,CACT,CAEA,OAAOA,CACT,CAEO,SAASM,EAAgBxG,EAAkBuG,GAMhD,OAJI3H,IAAeA,IACjB2H,EAAWA,EAASG,QAAQ,YAAa,MAGpC1G,EAAQ0E,EAAQc,yBAAyBe,EAClD,CAEA,IAAMI,EAAY,SAAC5H,GAAgC,OAAKA,EAAGsH,YAAetH,EAAkB0H,IAAI,EAuGhG,SAASG,EAAevH,EAAYwH,GAKlC,IAJA,IAEIC,EAFEC,EAAkB,GACpBb,EAAe7G,GAGXyH,EAAeH,EAAUT,KAAYA,IAAWW,GAASC,IAAiBZ,EAAOhH,eACvF6H,EAAQC,QAAQd,GAChBA,EAASY,EAGX,OAAOC,CACT,CASO,SAASE,EAAYjH,EAAkBuG,EAAkBM,GAC9D,KAAOnH,EAAGM,QAAQA,IAAU,CAC1B,GAAIwG,EAAgBxG,EAASuG,GAC3B,OAAO,EAKT,IAFAvG,EAAUqG,EAAWrG,MAEL6G,EACd,OAAOL,EAAgBxG,EAASuG,EAEpC,CAEA,OAAO,CACT,CAEO,SAASW,EAAiBlH,GAC/B,OAAQA,EAAgBmH,yBAA2BnH,CACrD,CAUO,SAASoH,EAAqBpH,GACnC,IAAMqH,EACJrH,aAAmB8D,EAAWG,WAAajE,EAAQsH,wBAA0BtH,EAAQuH,iBAAiB,GAExG,OACEF,GAAc,CACZG,KAAMH,EAAWG,KACjBC,MAAOJ,EAAWI,MAClBC,IAAKL,EAAWK,IAChBC,OAAQN,EAAWM,OACnBC,MAAOP,EAAWO,OAASP,EAAWI,MAAQJ,EAAWG,KACzDK,OAAQR,EAAWQ,QAAUR,EAAWM,OAASN,EAAWK,IAGlE,CAEO,SAASI,EAAe9H,GAC7B,IAzB0B+H,EAyBpBV,EAAaD,EAAqBpH,GAExC,IAAK0E,EAAQQ,QAAUmC,EAAY,CACjC,IAAMW,EA1BD,CACLxG,GAFFuG,GAD0BA,EA4BGnJ,EAAcoB,KA3BRpB,GAEfqJ,SAAWF,EAAe/I,SAASkJ,gBAAgBC,WACrE/G,EAAG2G,EAAeK,SAAWL,EAAe/I,SAASkJ,gBAAgBG,WA0BrEhB,EAAWG,MAAQQ,EAAOxG,EAC1B6F,EAAWI,OAASO,EAAOxG,EAC3B6F,EAAWK,KAAOM,EAAO5G,EACzBiG,EAAWM,QAAUK,EAAO5G,CAC9B,CAEA,OAAOiG,CACT,CAEO,SAASiB,EAAQjJ,GAGtB,IAFA,IAAMkJ,EAAO,GAENlJ,GACLkJ,EAAKC,KAAKnJ,GACVA,EAAOgH,EAAWhH,GAGpB,OAAOkJ,CACT,CAEO,SAASE,EAAYC,GAC1B,QAAKhJ,EAAGK,OAAO2I,KAKf5E,EAAW9E,SAAS2J,cAAcD,IAC3B,EACT,CCrQe,SAASE,EAA4BC,EAAsBC,GACxE,IAAK,IAAMC,KAAQD,EACfD,EAAsBE,GAAQD,EAAOC,GAKzC,OAFYF,CAGd,CCMO,SAASG,EAAsBN,EAAYO,EAAoBjJ,GACpE,MAAc,WAAV0I,EACKrC,EAAWrG,GAGN,SAAV0I,EACKO,EAAOC,QAAQlJ,GAGjBsG,EAAQtG,EAAS0I,EAC1B,CAEO,SAASS,EACdT,EACAO,EACAjJ,EACAoJ,GAEA,IAAIC,EAAmBX,EAWvB,OAVIhJ,EAAGK,OAAOsJ,GACZA,EAAcL,EAAsBK,EAAaJ,EAAQjJ,GAChDN,EAAGD,KAAK4J,KACjBA,EAAcA,EAAWC,WAAA,EAAIF,IAG3B1J,EAAGM,QAAQqJ,KACbA,EAAcvB,EAAeuB,IAGxBA,CACT,CAUO,SAASE,EAASC,GACvB,OACEA,GAAQ,CACNhI,EAAG,MAAOgI,EAAOA,EAAKhI,EAAIgI,EAAKhC,KAC/BpG,EAAG,MAAOoI,EAAOA,EAAKpI,EAAIoI,EAAK9B,IAGrC,CAeO,SAAS+B,EAAWD,GAUzB,OATIA,GAAU,MAAOA,GAAQ,MAAOA,KAClCA,EAAOZ,EAAO,GAAIY,IAEbhI,EAAIgI,EAAKhC,MAAQ,EACtBgC,EAAKpI,EAAIoI,EAAK9B,KAAO,EACrB8B,EAAK5B,MAAQ4B,EAAK5B,QAAU4B,EAAK/B,OAAS,GAAK+B,EAAKhI,EACpDgI,EAAK3B,OAAS2B,EAAK3B,SAAW2B,EAAK7B,QAAU,GAAK6B,EAAKpI,GAGlDoI,CACT,CAEO,SAASE,EAASC,EAAoBH,EAAY3H,GACnD8H,EAAMnC,OACRgC,EAAKhC,MAAQ3F,EAAML,GAEjBmI,EAAMlC,QACR+B,EAAK/B,OAAS5F,EAAML,GAElBmI,EAAMjC,MACR8B,EAAK9B,KAAO7F,EAAMT,GAEhBuI,EAAMhC,SACR6B,EAAK7B,QAAU9F,EAAMT,GAGvBoI,EAAK5B,MAAQ4B,EAAK/B,MAAQ+B,EAAKhC,KAC/BgC,EAAK3B,OAAS2B,EAAK7B,OAAS6B,EAAK9B,GACnC,CCpGe,SAASkC,EACtBX,EACAjJ,EACA6J,GAEA,IAAMC,EAAgBD,GAAeZ,EAAOnG,QAAgB+G,GAM5D,OAAON,EAFYJ,EAHEW,GAAiBA,EAAcC,QACrBd,EAAOnG,QAAQiH,OAEHd,EAAQjJ,EAAS,CAACiJ,GAAUjJ,MAExC,CAAEwB,EAAG,EAAGJ,EAAG,EAC5C,CCTe,SAAS4I,EACtBrG,EACAlB,GAGqB,IAFrBwH,EAAMC,UAAAzJ,OAAA,QAAA9B,IAAAuL,UAAA,GAAAA,UAAA,GAAG,SAACC,GAAqB,OAAK,CAAI,EACxCC,EAA4BF,UAAAzJ,OAAAyJ,EAAAA,kBAAAvL,EAQ5B,GANAyL,EAASA,GAAU,GAEf1K,EAAGK,OAAO4D,KAA+B,IAAtBA,EAAKC,OAAO,OACjCD,EAAO0G,EAAM1G,IAGXjE,EAAGc,MAAMmD,GAEX,OADAA,EAAK2G,SAAQ,SAACC,GAAC,OAAKP,EAAUO,EAAG9H,EAAWwH,EAAQG,MAC7CA,EAUT,GALI1K,EAAGH,OAAOoE,KACZlB,EAAYkB,EACZA,EAAO,IAGLjE,EAAGD,KAAKgD,IAAcwH,EAAOtG,GAC/ByG,EAAOzG,GAAQyG,EAAOzG,IAAS,GAC/ByG,EAAOzG,GAAM6E,KAAK/F,QACb,GAAI/C,EAAGc,MAAMiC,GAAY,IAAA,IAAA+H,EAAA,EAAAC,EACdhI,EAAS+H,EAAAC,EAAAhK,OAAA+J,IAAE,CAAtB,IAAME,EAACD,EAAAD,GACVR,EAAUrG,EAAM+G,EAAGT,EAAQG,EAC7B,MACK,GAAI1K,EAAGH,OAAOkD,GACnB,IAAK,IAAMkI,KAAUlI,EAAW,CAG9BuH,EAFsBK,EAAMM,GAAQpI,KAAI,SAACqI,GAAC,MAAA,GAAAC,OAAQlH,GAAIkH,OAAGD,EAAC,IAEjCnI,EAAUkI,GAASV,EAAQG,EACtD,CAGF,OAAOA,CACT,CAEA,SAASC,EAAM1G,GACb,OAAOA,EAAKmH,OAAOT,MAAM,KAC3B,CCpDA,IAAAU,EAAe,SAACvJ,EAAWJ,GAAS,OAAK4J,KAAKC,KAAKzJ,EAAIA,EAAIJ,EAAIA,EAAE,ECA3D8J,EAAkB,CAAC,SAAU,OAEpB,SAASC,GAAiBtC,EAA2CC,GAClFD,EAAKuC,QAALvC,EAAKuC,MAAU,CAAE,GAAO,IAAAC,EAAA,SAAAtC,GAItB,GAAImC,EAAgBI,MAAK,SAACX,GAAM,OAA8B,IAAzB5B,EAAKwC,QAAQZ,MAAc,OAAA,EAEtC,mBAAf9B,EAAKE,IAAiC,UAATA,GACtCyC,OAAOC,eAAe5C,EAAME,EAAM,CAChC2C,IAAG,WACD,OAAI3C,KAAQF,EAAKuC,MAAcvC,EAAKuC,MAAMrC,GAElCF,EAAKuC,MAAMrC,GAAQD,EAAOC,EACnC,EACD4C,IAAG,SAACjD,GACFG,EAAKuC,MAAMrC,GAAQL,CACpB,EACDkD,cAAc,KAdpB,IAAK,IAAM7C,KAAQD,EAAMuC,EAAAtC,GAkBzB,OAAOF,CACT,CCdO,SAASgD,GAAWhD,EAAuBiD,GAChDjD,EAAK1H,KAAO0H,EAAK1H,MAAS,CAAA,EAC1B0H,EAAK1H,KAAKK,EAAIsK,EAAI3K,KAAKK,EACvBqH,EAAK1H,KAAKC,EAAI0K,EAAI3K,KAAKC,EAEvByH,EAAKvH,OAASuH,EAAKvH,QAAW,CAAA,EAC9BuH,EAAKvH,OAAOE,EAAIsK,EAAIxK,OAAOE,EAC3BqH,EAAKvH,OAAOF,EAAI0K,EAAIxK,OAAOF,EAE3ByH,EAAKkD,UAAYD,EAAIC,SACvB,CAoBO,SAASC,GAAcC,GAC5BA,EAAU9K,KAAKK,EAAI,EACnByK,EAAU9K,KAAKC,EAAI,EACnB6K,EAAU3K,OAAOE,EAAI,EACrByK,EAAU3K,OAAOF,EAAI,CACvB,CAEO,SAAS8K,GAAgBC,GAC9B,OAAOA,aAAmBC,EAAI/H,OAAS8H,aAAmBC,EAAI9H,KAChE,CAGO,SAAS+H,GAAM1I,EAAcwI,EAAsCG,GAOxE,OALA3I,EAAOA,GAAQ,QADf2I,EAAKA,GAAO,IAGT9K,EAAI2K,EAASxI,EAAO,KACvB2I,EAAGlL,EAAI+K,EAASxI,EAAO,KAEhB2I,CACT,CAEO,SAASC,GAAUJ,EAAsChL,GAa9D,OAZAA,EAAOA,GAAQ,CAAEK,EAAG,EAAGJ,EAAG,GAGtBsD,EAAQY,eAAiB4G,GAAgBC,IAC3CE,GAAM,SAAUF,EAAShL,GAEzBA,EAAKK,GAAK1C,OAAOmJ,QACjB9G,EAAKC,GAAKtC,OAAOsJ,SAEjBiE,GAAM,OAAQF,EAAShL,GAGlBA,CACT,CAeO,SAASqL,GAAaL,GAC3B,OAAOzM,EAAGG,OAAOsM,EAAQM,WAAaN,EAAQM,UAAaN,EAAQO,UACrE,CAEO,SAASC,GAAU9D,EAAuB+D,EAAiBb,GAChE,IAAMI,EAAUS,EAASnM,OAAS,EAAIoM,GAAeD,GAAYA,EAAS,GAE1EL,GAAUJ,EAAStD,EAAK1H,MApBnB,SAAqBgL,EAAsB7K,GAChDA,EAASA,GAAW,GAEhBoD,EAAQY,eAAiB4G,GAAgBC,GAE3CE,GAAM,SAAUF,EAAS7K,GAEzB+K,GAAM,SAAUF,EAAS7K,EAI7B,CAUEwL,CAAYX,EAAStD,EAAKvH,QAE1BuH,EAAKkD,UAAYA,CACnB,CAEO,SAASgB,GAAaC,GAC3B,IAAMC,EAAyB,GAuB/B,OApBIvN,EAAGc,MAAMwM,IACXC,EAAQ,GAAKD,EAAM,GACnBC,EAAQ,GAAKD,EAAM,IAIA,aAAfA,EAAMrJ,KACqB,IAAzBqJ,EAAMC,QAAQxM,QAChBwM,EAAQ,GAAKD,EAAMC,QAAQ,GAC3BA,EAAQ,GAAKD,EAAME,eAAe,IACA,IAAzBF,EAAMC,QAAQxM,SACvBwM,EAAQ,GAAKD,EAAME,eAAe,GAClCD,EAAQ,GAAKD,EAAME,eAAe,KAGpCD,EAAQ,GAAKD,EAAMC,QAAQ,GAC3BA,EAAQ,GAAKD,EAAMC,QAAQ,IAIxBA,CACT,CAEO,SAASJ,GAAeD,GAQ5B,IAPD,IAAMO,EAAU,CACdC,MAAO,EACPC,MAAO,EACPC,QAAS,EACTC,QAAS,EACTC,QAAS,EACTC,QAAS,GACVjD,IAAAA,EAIqBoC,EAAQnM,OAAA+J,IAAE,CAA3B,IAAM2B,EAAWS,EAAQpC,GAC5B,IAAK,IAAMzB,KAAQoE,EACjBA,EAAQpE,IAAsBoD,EAAQpD,EAE1C,CACA,IAAK,IAAMA,KAAQoE,EACjBA,EAAQpE,IAAsB6D,EAASnM,OAGzC,OAAO0M,CACT,CAEO,SAASO,GAAUV,GACxB,IAAKA,EAAMvM,OACT,OAAO,KAGT,IAAMwM,EAAUF,GAAaC,GACvBW,EAAO3C,KAAK4C,IAAIX,EAAQ,GAAGG,MAAOH,EAAQ,GAAGG,OAC7CS,EAAO7C,KAAK4C,IAAIX,EAAQ,GAAGI,MAAOJ,EAAQ,GAAGI,OAC7CS,EAAO9C,KAAK+C,IAAId,EAAQ,GAAGG,MAAOH,EAAQ,GAAGG,OAC7CY,EAAOhD,KAAK+C,IAAId,EAAQ,GAAGI,MAAOJ,EAAQ,GAAGI,OAEnD,MAAO,CACL7L,EAAGmM,EACHvM,EAAGyM,EACHrG,KAAMmG,EACNjG,IAAKmG,EACLpG,MAAOqG,EACPnG,OAAQqG,EACRpG,MAAOkG,EAAOH,EACd9F,OAAQmG,EAAOH,EAEnB,CAEO,SAASI,GAAcjB,EAAmCkB,GAC/D,IAAMC,EAAWD,EAAc,IACzBE,EAAWF,EAAc,IACzBjB,EAAUF,GAAaC,GAEvBqB,EAAKpB,EAAQ,GAAGkB,GAAWlB,EAAQ,GAAGkB,GACtCG,EAAKrB,EAAQ,GAAGmB,GAAWnB,EAAQ,GAAGmB,GAE5C,OAAOrD,EAAMsD,EAAIC,EACnB,CAEO,SAASC,GAAWvB,EAAmCkB,GAC5D,IAAMC,EAAWD,EAAc,IACzBE,EAAWF,EAAc,IACzBjB,EAAUF,GAAaC,GACvBqB,EAAKpB,EAAQ,GAAGkB,GAAWlB,EAAQ,GAAGkB,GACtCG,EAAKrB,EAAQ,GAAGmB,GAAWnB,EAAQ,GAAGmB,GAG5C,OAFe,IAAMpD,KAAKwD,MAAMF,EAAID,GAAOrD,KAAKyD,EAGlD,CAEO,SAASC,GAAevC,GAC7B,OAAOzM,EAAGK,OAAOoM,EAAQlJ,aACrBkJ,EAAQlJ,YACRvD,EAAGG,OAAOsM,EAAQlJ,aAChB,MAACtE,OAAWA,EAAW,QAAS,MAAO,SAASwN,EAAQlJ,aAGxD,QAAQ/C,KAAKiM,EAAQxI,MAAQ,KAAOwI,aAAmBC,EAAI9H,MACzD,QACA,OACV,CAGO,SAASqK,GAAgB3B,GAC9B,IAAMzE,EAAO7I,EAAGD,KAAKuN,EAAM4B,cACtB5B,EAAM4B,eACN5B,EAAyCzE,KAE9C,MAAO,CACLsG,EAA0BtG,EAAOA,EAAK,GAAMyE,EAAM/D,QAClD4F,EAA0B7B,EAAM8B,eAEpC,CC1NA,IAAaC,GAAS,WAWpB,SAAAA,EAAYlO,GAA6BmO,OAAAD,GANzCzL,KAGA2L,6BAA8B,EAAK3L,KACnC4L,oBAAqB,EAGnB5L,KAAK6L,aAAetO,CACtB,CAgBC,OAhBAuO,EAAAL,EAAA,CAAA,CAAAM,IAAA,iBAAA3G,MAED,WAAkB,GAElB,CAAA2G,IAAA,kBAAA3G,MAGA,WACEpF,KAAK4L,oBAAqB,CAC5B,GAEA,CAAAG,IAAA,2BAAA3G,MAGA,WACEpF,KAAK2L,4BAA8B3L,KAAK4L,oBAAqB,CAC/D,KAACH,CAAA,CA7BmB,GAwCtBvD,OAAOC,eAAesD,GAAU1M,UAAW,cAAe,CACxDqJ,IAAG,WACD,OAAOpI,KAAK6L,aAAaG,MAC1B,EACD3D,IAAG,WAAmB,IC5CjB,IAEM4D,GAAQ,SAAOtG,EAAsBH,GAAgB,IAAA0B,IAAAA,IAAAA,EAC7C1B,EAAMrI,OAAA+J,IAAE,CAAtB,IAAMgF,EAAQ1G,EAAM0B,GACvBvB,EAAOT,KAAKgH,EACd,CAEA,OAAOvG,CACT,EAEawG,GAAO,SAAU3G,GAAoB,OAAKyG,GAAM,GAAWzG,EAAc,EAEzE4G,GAAY,SAAIlP,EAAYf,GACvC,IAAK,IAAIkQ,EAAI,EAAGA,EAAInP,EAAMC,OAAQkP,IAChC,GAAIlQ,EAAKe,EAAMmP,GAAIA,EAAGnP,GACpB,OAAOmP,EAIX,OAAQ,CACV,EAEaC,GAAO,SAAUpP,EAAYf,GAAe,OAAoBe,EAAMkP,GAAUlP,EAAOf,GAAM,EClB7FoQ,YAASC,GAAAC,EAAAF,EAAAC,GAAA,IAAAE,EAAAC,EAAAJ,GAYpB,SAAAA,EAAYK,EAAsBC,EAAkCxM,GAAc,IAAAyM,EAAApB,OAAAa,IAChFO,EAAAJ,EAAAK,KAAMF,KAAAA,EAAUhB,eAXlBmB,cAAQ,EAAAF,EACRD,eAAS,EAAAC,EACTG,mBAAa,EAAAH,EACb9N,eAAS,EAAA8N,EACTlB,oBAAqB,EAAKkB,EAC1BnB,6BAA8B,EAQ5B,IAAArO,EAAuC,cAAT+C,EAAuBuM,EAAUM,KAAON,EAAUhP,IAAxElB,EAAOY,EAAPZ,QAASsQ,EAAQ1P,EAAR0P,SASmB,OAPpCF,EAAKzM,KAAOA,EACZyM,EAAKnH,OAASjJ,EACdoQ,EAAKtB,cAAgB9O,EACrBoQ,EAAKE,SAAWA,EAChBF,EAAKD,UAAYA,EACjBC,EAAKG,cAAgBJ,EAAUlH,OAC/BmH,EAAK9N,UAAY6N,EAAUxN,aAC3ByN,EAAKrE,UAAYoE,EAAUpE,UAASqE,CACtC,CAuDC,OArDDhB,EAAAS,EAAA,CAAA,CAAAR,IAAA,SAAA3G,MAOA,WAAS,IAAA+H,EAAAnN,KACC4M,EAAc5M,KAAK6L,aAAnBe,UAER,GACgB,iBAAd5M,KAAKK,MACHL,KAAKgN,UAAYJ,EAAUhP,IAAIoP,WAAahN,KAAKgN,UAAYJ,EAAUhP,IAAIlB,UAAYsD,KAAK2F,OAahG,GARAiH,EAAUM,KAAKF,SAAWhN,KAAKgN,SAC/BJ,EAAUM,KAAKxQ,QAAUsD,KAAK2F,OAE9BiH,EAAUQ,UAAW,EACrBR,EAAUS,OAAOC,MAAQ,KAEzBtN,KAAKuN,2BAEa,iBAAdvN,KAAKK,KAAyB,CAChC,IAAMmN,EAAcZ,EAAUY,YACxBC,EAAQC,GACZF,GACA,SAAApP,GAAA,IAAG4O,EAAQ5O,EAAR4O,SAAUtQ,EAAO0B,EAAP1B,QAAO,OAAOsQ,IAAaG,EAAKH,UAAYtQ,IAAYyQ,EAAKxH,MAAM,IAGlFiH,EAAUY,YAAYpQ,OAAOqQ,EAAO,GAEpC,IAAME,EAAkB,IAAIpB,EAAUK,EAAW5M,KAAK6M,UAAW,kBAEjEc,EAAgBX,SAAWhN,KAAKgN,SAChCW,EAAgBhI,OAAS3F,KAAK2F,OAE9B3F,KAAKgN,SAASY,KAAKD,EACrB,MACE3N,KAAKgN,SAASY,KAAK,IAAIrB,EAAUK,EAAW5M,KAAK6M,UAAW,aAEhE,GAAC,CAAAd,IAAA,iBAAA3G,MAED,WAAkB,GAAC,CAAA2G,IAAA,kBAAA3G,MAEnB,WACEpF,KAAK4L,oBAAqB,CAC5B,GAAC,CAAAG,IAAA,2BAAA3G,MAED,WACEpF,KAAK2L,4BAA8B3L,KAAK4L,oBAAqB,CAC/D,KAACW,CAAA,EAhF4Bd,IC6R/B,SAASoC,GAAqBL,EAA2B9D,GACvD,IAAA,IAAAoE,EAAAC,EAAAA,EACoCP,EAAYQ,QAAOF,EAAAC,EAAA5Q,OAAA2Q,IAAE,CAApD,IAAAG,EAAAF,EAAAD,GAAQd,EAAQiB,EAARjB,SAAUtQ,EAAOuR,EAAPvR,QACrBgN,EAAMsD,SAAWA,EAGjBtD,EAAM/D,OAASjJ,EACfsQ,EAASY,KAAKlE,GACdA,EAAMkC,mBAAqBlC,EAAMiC,6BAA8B,CACjE,CACF,CAKA,SAASuC,GAAevP,EAAcwP,GAEoB,IAAxD,IAAMX,EAnDR,SAAyBlQ,EAA2B8Q,GAGlD,IAH6E,IACvEC,EAAsB,GAE5BnH,EAAA,EAAAoH,EAHuChR,EAAbiR,cAIWC,KAAItH,EAAAoH,EAAAnR,OAAA+J,IAAE,CAAtC,IAAM8F,EAAQsB,EAAApH,GACjB,GAAK8F,EAASxN,QAAQiP,KAAKhP,QAA3B,CAIA,IAAMiP,EAAS1B,EAASxN,QAAQiP,KAAKC,OAGrC,KACGtS,EAAGM,QAAQgS,IAAWA,IAAWN,GACjChS,EAAGK,OAAOiS,KAAYnD,EAAyB6C,EAAkBM,IACjEtS,EAAGD,KAAKuS,KAAYA,EAAO,CAAE1B,SAAAA,EAAUoB,iBAAAA,KAGzC,IAAA,IAAAO,EAAAC,EAAAA,EAE6B5B,EAAS6B,iBAAgBF,EAAAC,EAAAzR,OAAAwR,IAAE,CAApD,IAAMG,EAAeF,EAAAD,GACpBG,IAAoBV,GACtBC,EAAMnJ,KAAK,CACT8H,SAAAA,EACAtQ,QAASoS,EACT5I,KAAM8G,EAASpH,QAAQkJ,IAG7B,CArBA,CAsBF,CAEA,OAAOT,CACT,CAmBsBU,CAAiBpQ,EAAOwP,GAAYa,IAAAA,EAE/BxB,EAAWrQ,OAAA6R,IAAE,CAAjC,IAAMC,EAAczB,EAAWwB,GAClCC,EAAW/I,KAAO+I,EAAWjC,SAASpH,QAAQqJ,EAAWvS,QAC3D,CAEA,OAAO8Q,CACT,CAEA,SAAS0B,GAAO9Q,EAEdyO,EACAsC,GAIA,IAHA,IAHEvC,EAASxO,EAATwO,UAAyB5N,EAASZ,EAAvBiB,aAAkC8O,EAAW/P,EAApB1B,QAIhC0S,EAAwB,GAE9BC,EAAA,EAAAC,EAC2D1C,EAAUY,YAAW6B,EAAAC,EAAAnS,OAAAkS,IAAE,CAA7E,IAAAE,EAAAD,EAAAD,GAAQrC,EAAQuC,EAARvC,SAAmB8B,EAAeS,EAAxB7S,QAA0BwJ,EAAIqJ,EAAJrJ,KACzCsJ,EAAUxC,EAASyC,UACvB5C,EACAsC,EACAnQ,EACAmP,EACAW,EACA5I,GAEFkJ,EAAWlK,KAAKsK,EAAUV,EAAkB,KAC7C,CAGD,IAAMY,EXrRD,SAA+BC,GAIpC,IAHA,IACIC,EA+GsBC,EAAkBC,EAhHxCC,EAA6B,GAGxB1D,EAAI,EAAGA,EAAIsD,EAASxS,OAAQkP,IAAK,CACxC,IAAM2D,EAAcL,EAAStD,GACvB4D,EAAoBN,EAASC,GAGnC,GAAKI,GAAe3D,IAAMuD,EAI1B,GAAKK,EAAL,CAKA,IAAMC,EAAoB7M,EAAU2M,GAC9BG,EAAoB9M,EAAU4M,GAIpC,GAAIC,IAAsBF,EAAYpU,cAIjC,GAAIuU,IAAsBH,EAAYpU,cAM3C,GAAIsU,IAAsBC,EAA1B,CASAJ,EAAqBA,EAAmB5S,OAAS4S,EAAqBzM,EAAe2M,GAErF,IAAIG,OAAmB,EAGvB,GACEH,aAAuBzP,EAAWM,aAClCkP,aAAuBxP,EAAWG,cAChCqP,aAAuBxP,EAAWI,eACpC,CAEA,GAAIoP,IAAgBG,EAClB,SAGFC,EAAgBJ,EAAYK,eAC9B,MACED,EAAgBJ,EAOlB,IAJA,IAAMM,EAAqBhN,EAAe8M,EAAeH,EAAYrU,eACjE2U,EAAc,EAIhBD,EAAmBC,IACnBD,EAAmBC,KAAiBR,EAAmBQ,IAEvDA,IAGF,IAAM9M,EAAU,CACd6M,EAAmBC,EAAc,GACjCD,EAAmBC,GACnBR,EAAmBQ,IAGrB,GAAI9M,EAAQ,GAGV,IAFA,IAAIZ,EAAQY,EAAQ,GAAG+M,UAEhB3N,GAAO,CACZ,GAAIA,IAAUY,EAAQ,GAAI,CACxBmM,EAAmBvD,EACnB0D,EAAqBO,EAErB,KACD,CAAM,GAAIzN,IAAUY,EAAQ,GAC3B,MAGFZ,EAAQA,EAAM4N,eAChB,CAtDF,MA0E0CX,EA/EJG,OAgFlCS,OACAC,GADcC,SAAStV,EADHuU,EA/ECG,GAgF4Ba,iBAAiBhB,GAAYiB,OAAQ,KAAO,KAChFF,SAAStV,EAAcwU,GAAWe,iBAAiBf,GAAWgB,OAAQ,KAAO,KAhF1FlB,EAAmBvD,QAPrBuD,EAAmBvD,CAZrB,MAFEuD,EAAmBvD,CAiFvB,CAEA,OAAOuD,CACT,CWmLoBrE,CAA+B6D,GAEjD,OAAOxC,EAAWY,YAAYkC,IAAc,IAC9C,CAEA,SAASqB,GAAcxT,EAA0ByT,EAAenE,GAC9D,IAAMD,EAAYrP,EAAYqP,UACxBqE,EAA+C,CACnD3D,MAAO,KACP4D,MAAO,KACPC,SAAU,KACVC,WAAY,KACZjT,KAAM,KACNsQ,KAAM,MAgBR,MAbuB,cAAnB5B,EAAUxM,OACZ4Q,EAAWE,SAAW,IAAI5E,GAAUK,EAAWC,EAAW,gBAE1DoE,EAAWE,SAASxL,OAAS,KAC7BsL,EAAWE,SAASnE,SAAW,MAEV,YAAnBH,EAAUxM,OACZ4Q,EAAWG,WAAa,IAAI7E,GAAUK,EAAWC,EAAW,kBAE5DoE,EAAWG,WAAWzL,OAAS,KAC/BsL,EAAWG,WAAWpE,SAAW,MAG/BJ,EAAUQ,WAIVR,EAAUhP,IAAIlB,UAAYkQ,EAAUM,KAAKxQ,UAEvCkQ,EAAUM,KAAKF,WACjBiE,EAAWC,MAAQ,IAAI3E,GAAUK,EAAWC,EAAW,aAEvDA,EAAUwE,UAAYJ,EAAWC,MAAMvL,OAASiH,EAAUM,KAAKxQ,QAC/DmQ,EAAUyE,aAAeL,EAAWC,MAAMlE,SAAWJ,EAAUM,KAAKF,UAGlEJ,EAAUhP,IAAIoP,WAChBiE,EAAW3D,MAAQ,IAAIf,GAAUK,EAAWC,EAAW,aAEvDA,EAAU0E,UAAY3E,EAAUhP,IAAIlB,QACpCmQ,EAAUG,SAAWJ,EAAUhP,IAAIoP,WAIhB,YAAnBH,EAAUxM,MAAsBuM,EAAUhP,IAAIoP,WAChDiE,EAAWxC,KAAO,IAAIlC,GAAUK,EAAWC,EAAW,QAEtDA,EAAUG,SAAWJ,EAAUhP,IAAIoP,SACnCH,EAAUI,cAAgBL,EAAUhP,IAAIlB,SAEnB,aAAnBmQ,EAAUxM,MAAuBuM,EAAUhP,IAAIoP,WACjDiE,EAAW9S,KAAO,IAAIoO,GAAUK,EAAWC,EAAW,YAEtDA,EAAUG,SAAWJ,EAAUhP,IAAIoP,WA7B5BiE,CAiCX,CAMA,SAASO,GAAejU,EAA0B8P,GAChD,IAAMT,EAAYrP,EAAYqP,UACtBY,EAA2BZ,EAA3BY,YAAa5P,EAAcgP,EAAdhP,IAAKsP,EAASN,EAATM,KAEtBG,EAAO6D,OACThE,EAAKF,SAASY,KAAKP,EAAO6D,OAExB7D,EAAOC,OACT1P,EAAIoP,SAASY,KAAKP,EAAOC,OAEvBD,EAAOlP,MACTP,EAAIoP,SAASY,KAAKP,EAAOlP,MAEvBkP,EAAOoB,MACT7Q,EAAIoP,SAASY,KAAKP,EAAOoB,MAGvBpB,EAAO+D,YACTvD,GAAqBL,EAAaH,EAAO+D,YAG3CxE,EAAUM,KAAKF,SAAWpP,EAAIoP,SAC9BJ,EAAUM,KAAKxQ,QAAUkB,EAAIlB,OAC/B,CAEA,SAAS+U,GAAcC,EAAiE/S,GAAc,IAA5EpB,EAAWmU,EAAXnU,YAAac,EAAMqT,EAANrT,OAAQqL,EAAKgI,EAALhI,MAC7C,GAAoB,aAAhBrL,EAAOgC,MAAuC,YAAhBhC,EAAOgC,KAAzC,CAIA,IAAMuM,EAAYrP,EAAYqP,UAE1BjO,EAAMgT,cACR/E,EAAUY,YAAcU,GAAevP,EAAOpB,EAAYb,UAG5D,IAAMmQ,EAAYxO,EACZuT,EAAa1C,GAAQ3R,EAAasP,EAAWnD,GAGnDkD,EAAUQ,SACRR,EAAUQ,YACRwE,GACFA,EAAW5E,WAAaJ,EAAUhP,IAAIoP,UACtC4E,EAAWlV,UAAYkQ,EAAUhP,IAAIlB,QAEvCkQ,EAAUhP,IAAIoP,SAAW4E,GAAcA,EAAW5E,SAClDJ,EAAUhP,IAAIlB,QAAUkV,GAAcA,EAAWlV,QAEjDkQ,EAAUS,OAAS0D,GAAcxT,EAAamM,EAAOmD,EArBrD,CAsBF,CAqJA,IAAM4B,GAAe,CACnBhQ,GAAI,eACJC,QAhZF,SAAiBC,GACf,IAAQC,EAA8DD,EAA9DC,QAAyBiT,EAAqClT,EAArDmT,eAA0BjT,EAA2BF,EAA3BE,aAAcC,EAAaH,EAAbG,SAEzDH,EAAMoT,UAAUvT,GAEhBK,EAAaE,UAAUiO,SAAW,SAA8BxN,GAC9D,OAuPJ,SAAwBH,EAA4BG,GAClD,GAAIpD,EAAGH,OAAOuD,GAAU,CAGtB,GAFAH,EAAaG,QAAQiP,KAAKhP,SAA8B,IAApBD,EAAQC,QAExCD,EAAQL,UAAW,CACrB,IAAM6S,EAAaC,EAAmBzS,EAAQL,WAExC+S,EAAYhK,OAAOiK,KAAKH,GAAYI,QAAO,SAACC,EAAKhS,GASrD,OAFAgS,EANsB,iBAAiBzV,KAAKyD,GAAKkH,OAAAA,OACtClH,GACP,8BAA8BzD,KAAKyD,UAAKkH,OAC/BlH,GACPA,GAEe2R,EAAW3R,GAEzBgS,CACR,GAAE,CAAE,GAECC,EAAgBjT,EAAaG,QAAQiP,KAAKtP,UAChDmT,GAAiBjT,EAAakT,IAAID,GAElCjT,EAAamT,GAAGN,GAChB7S,EAAaG,QAAQiP,KAAKtP,UAAY+S,CACxC,CAiCA,OA/BI9V,EAAGD,KAAKqD,EAAQiT,SAClBpT,EAAamT,GAAG,OAAQhT,EAAQiT,QAE9BrW,EAAGD,KAAKqD,EAAQkT,iBAClBrT,EAAamT,GAAG,eAAgBhT,EAAQkT,gBAEtCtW,EAAGD,KAAKqD,EAAQmT,mBAClBtT,EAAamT,GAAG,iBAAkBhT,EAAQmT,kBAExCvW,EAAGD,KAAKqD,EAAQoT,cAClBvT,EAAamT,GAAG,YAAahT,EAAQoT,aAEnCxW,EAAGD,KAAKqD,EAAQqT,cAClBxT,EAAamT,GAAG,YAAahT,EAAQqT,aAEnCzW,EAAGD,KAAKqD,EAAQsT,aAClBzT,EAAamT,GAAG,WAAYhT,EAAQsT,YAGlC,qBAAqBlW,KAAK4C,EAAQuT,SACpC1T,EAAaG,QAAQiP,KAAKsE,QAAUvT,EAAQuT,QACnC3W,EAAGG,OAAOiD,EAAQuT,WAC3B1T,EAAaG,QAAQiP,KAAKsE,QAAUrL,KAAK+C,IAAI/C,KAAK4C,IAAI,EAAG9K,EAAQuT,SAAU,IAEzE,WAAYvT,IACdH,EAAaG,QAAQiP,KAAKC,OAASlP,EAAQkP,QAEzC,YAAalP,IACfH,EAAaG,QAAQiP,KAAKuE,QAAUxT,EAAQwT,SAGvC3T,CACT,CAEA,GAAIjD,EAAGI,KAAKgD,GAGV,OAFAH,EAAaG,QAAQiP,KAAKhP,QAAUD,EAE7BH,EAGT,OAAOA,EAAaG,QAAQiP,IAC9B,CA1TWwE,CAAejT,KAAMR,IAG9BX,EAAaE,UAAU0Q,UAAY,SAEjC5C,EACAnD,EACA1K,EACAoP,EACA8E,EACAhN,GAEA,OAgTJ,SACE7G,EACAwN,EACAnD,EACA1K,EACAoP,EACA8E,EACAhN,GAEA,IAAIiN,GAAU,EAId,KAAMjN,EAAOA,GAAQ7G,EAAauG,QAAQsN,IACxC,QAAO7T,EAAaG,QAAQiP,KAAKuE,SAC7B3T,EAAaG,QAAQiP,KAAKuE,QACxBnG,EACAnD,EACAyJ,EACA9T,EACA6T,EACAlU,EACAoP,GAKR,IAAMgF,EAAc/T,EAAaG,QAAQiP,KAAKsE,QAE9C,GAAoB,YAAhBK,EAA2B,CAC7B,IAAM3M,EAASH,EAAYtH,EAAWoP,EAAkB,QAClDvQ,EAAOwV,GAAuBxG,GAEpChP,EAAKK,GAAKuI,EAAOvI,EACjBL,EAAKC,GAAK2I,EAAO3I,EAEjB,IAAMwV,EAAazV,EAAKK,EAAIgI,EAAKhC,MAAQrG,EAAKK,EAAIgI,EAAK/B,MACjDoP,EAAW1V,EAAKC,EAAIoI,EAAK9B,KAAOvG,EAAKC,EAAIoI,EAAK7B,OAEpD8O,EAAUG,GAAcC,CAC1B,CAEA,IAAMC,EAAWxU,EAAU4G,QAAQwI,GAEnC,GAAIoF,GAA4B,WAAhBJ,EAA0B,CACxC,IAAMK,EAAKD,EAAStP,KAAOsP,EAASlP,MAAQ,EACtCoP,EAAKF,EAASpP,IAAMoP,EAASjP,OAAS,EAE5C4O,EAAUM,GAAMvN,EAAKhC,MAAQuP,GAAMvN,EAAK/B,OAASuP,GAAMxN,EAAK9B,KAAOsP,GAAMxN,EAAK7B,MAChF,CAEA,GAAImP,GAAYpX,EAAGG,OAAO6W,GAAc,CAOtCD,EALEzL,KAAK+C,IAAI,EAAG/C,KAAK4C,IAAIpE,EAAK/B,MAAOqP,EAASrP,OAASuD,KAAK+C,IAAIvE,EAAKhC,KAAMsP,EAAStP,OAChFwD,KAAK+C,IAAI,EAAG/C,KAAK4C,IAAIpE,EAAK7B,OAAQmP,EAASnP,QAAUqD,KAAK+C,IAAIvE,EAAK9B,IAAKoP,EAASpP,OAE/CoP,EAASlP,MAAQkP,EAASjP,SAEpC6O,CAC5B,CAEI/T,EAAaG,QAAQiP,KAAKuE,UAC5BG,EAAU9T,EAAaG,QAAQiP,KAAKuE,QAClCnG,EACAnD,EACAyJ,EACA9T,EACA6T,EACAlU,EACAoP,IAIJ,OAAO+E,CACT,CA1XWQ,CAAgB3T,KAAM6M,EAAWnD,EAAO1K,EAAWoP,EAAkB8E,EAAahN,IAG3F2L,EAASF,YAAc,SAAUiC,GAC/B,OAAIxX,EAAGI,KAAKoX,IAKVjV,EAAMgT,YAAciC,EAEb/B,GAEFlT,EAAMgT,aAGfrM,EAAO1G,EAAQiV,eAAgB,CAC7BC,WAAW,EACXC,WAAW,EACXC,cAAc,EACdC,gBAAgB,EAChBC,UAAU,EACVzF,MAAM,IAER7P,EAAQM,WAAWuP,KAAO,WAE1B9P,EAAMgT,aAAc,EAEpB7S,EAASF,QAAQ6P,KAAOA,GAAK3P,QAC/B,EAkWEK,UAAW,CACT,mCAAoC,SAAAgV,GAAqB,IAAlB5W,EAAW4W,EAAX5W,YACH,SAA9BA,EAAYC,SAASC,OAIzBF,EAAYqP,UAAY,CACtBhP,IAAK,CACHoP,SAAU,KACVtQ,QAAS,MAEXwQ,KAAM,CACJF,SAAU,KACVtQ,QAAS,MAEX0Q,SAAU,KACVC,OAAQ,KACRG,YAAa,IAEhB,EAED,kCAAmC,SAAA4G,EAEjCzV,GACG,IAFDpB,EAAW6W,EAAX7W,YAA4BsP,GAAVuH,EAAL1K,MAAwB0K,EAAjB/V,QAGtB,GAAkC,SAA9Bd,EAAYC,SAASC,KAAzB,CAIA,IAAMmP,EAAYrP,EAAYqP,UAG9BA,EAAUY,YAAc,GACxBZ,EAAUS,OAAS,GACnBT,EAAUY,YAAcU,GAAevP,EAAOpB,EAAYb,SAC1DkQ,EAAUS,OAAS0D,GAAcxT,EAAamM,EAAOmD,GAEjDD,EAAUS,OAAO8D,WACnBtD,GAAqBjB,EAAUY,YAAaZ,EAAUS,OAAO8D,UAC7DxS,EAAMiP,KAAK,qBAAsB,CAAErQ,YAAAA,EAAasP,UAAAA,IAZlD,CAcD,EAED,2BAA4B4E,GAE5B,iCAAkC,SAAA4C,EAEhC1V,GACG,IAFDpB,EAAW8W,EAAX9W,YAAqBsP,EAASwH,EAAjBhW,OAGf,GAAkC,SAA9Bd,EAAYC,SAASC,KAAzB,CAIA,IAAMmP,EAAYrP,EAAYqP,UAC9B4E,GAAejU,EAAaqP,EAAUS,QAEtC1O,EAAMiP,KAAK,oBAAqB,CAAErQ,YAAAA,EAAasP,UAAAA,IAC/CD,EAAUS,OAAS,EANnB,CAOD,EAED,0BAA2B,SAACjO,EAAqCT,GAC/D,GAAsC,SAAlCS,EAAI7B,YAAYC,SAASC,KAA7B,CAIA,IAAQF,EAAmC6B,EAAnC7B,YAAqBsP,EAAczN,EAAtBf,OAErBoT,GAAerS,EAAKT,GACpB6S,GAAejU,EAAaA,EAAYqP,UAAWS,QACnD1O,EAAMiP,KAAK,mBAAoB,CAAErQ,YAAAA,EAAasP,UAAAA,GAN9C,CAOD,EAED,oBAAqB,SAAAyH,GAAqB,IAAlB/W,EAAW+W,EAAX/W,YACtB,GAAkC,SAA9BA,EAAYC,SAASC,KAAzB,CAIA,IAAQmP,EAAcrP,EAAdqP,UAEJA,IACFA,EAAUY,YAAc,KACxBZ,EAAUS,OAAS,KACnBT,EAAUhP,IAAIoP,SAAW,KACzBJ,EAAUhP,IAAIlB,QAAU,KACxBkQ,EAAUM,KAAKF,SAAW,KAC1BJ,EAAUM,KAAKxQ,QAAU,KACzBkQ,EAAUQ,UAAW,EAXvB,CAaF,GAEFc,eAAAA,GACAgB,QAAAA,GACA6B,cAAAA,GACAS,eAAAA,GAEApR,gBAAiB,SAACC,GAAY,OAA6B,IAAxBA,EAAKC,OAAO,SAAyC,IAAxBD,EAAKC,OAAO,OAAa,EAEzFxB,SAAU,CACRW,SAAS,EACTiP,OAAQ,KACRqE,QAAS,YAIbwB,GAAe9F,GC1lBf,SAAS+F,GAAkBlX,GAAmD,IAAhDC,EAAWD,EAAXC,YAAac,EAAMf,EAANe,OAAQoW,EAAKnX,EAALmX,MACjD,GAAkC,YAA9BlX,EAAYC,SAASC,KAAzB,CAEA,IAAM6L,EAAW/L,EAAY+L,SAASrK,KAAI,SAACqI,GAAC,OAAKA,EAAEuB,WAC7C6L,EAAqB,UAAVD,EACXE,EAAmB,QAAVF,EACT7J,EAAcrN,EAAY8B,aAAaG,QAAQoL,YAIrD,GAFAvM,EAAOsL,QAAU,CAACL,EAAS,GAAIA,EAAS,IAEpCoL,EACFrW,EAAOuW,SAAWvB,GAA2B/J,EAAUsB,GACvDvM,EAAOwW,IAAMxB,GAAuB/J,GACpCjL,EAAOyW,MAAQ,EACfzW,EAAO0W,GAAK,EACZ1W,EAAO2W,MAAQ3B,GAAwB/J,EAAUsB,GACjDvM,EAAO4W,GAAK,EAEZ1X,EAAY2X,QAAQC,cAAgB9W,EAAOuW,SAC3CrX,EAAY2X,QAAQE,WAAa/W,EAAO2W,WACnC,GAAIL,GAAUpX,EAAY+L,SAASnM,OAAS,EAAG,CACpD,IAAMkY,EAAY9X,EAAY8X,UAE9BhX,EAAOuW,SAAWS,EAAUT,SAC5BvW,EAAOwW,IAAMQ,EAAUR,IACvBxW,EAAOyW,MAAQO,EAAUP,MACzBzW,EAAO0W,GAAK,EACZ1W,EAAO2W,MAAQK,EAAUL,MACzB3W,EAAO4W,GAAK,CACd,MACE5W,EAAOuW,SAAWvB,GAA2B/J,EAAUsB,GACvDvM,EAAOwW,IAAMxB,GAAuB/J,GACpCjL,EAAOyW,MAAQzW,EAAOuW,SAAWrX,EAAY2X,QAAQC,cACrD9W,EAAO2W,MAAQ3B,GAAwB/J,EAAUsB,GAEjDvM,EAAO0W,GAAK1W,EAAOyW,MAAQvX,EAAY2X,QAAQJ,MAC/CzW,EAAO4W,GAAK5W,EAAO2W,MAAQzX,EAAY2X,QAAQF,MAGjDzX,EAAY2X,QAAQN,SAAWvW,EAAOuW,SACtCrX,EAAY2X,QAAQF,MAAQ3W,EAAO2W,MAE/B5Y,EAAGG,OAAO8B,EAAOyW,QAAUzW,EAAOyW,QAAUQ,MAAaC,MAAMlX,EAAOyW,SACxEvX,EAAY2X,QAAQJ,MAAQzW,EAAOyW,MA1CQ,CA4C/C,CAEA,IAAMI,GAAkB,CACtBzW,GAAI,kBACJ+W,OAAQ,CAAC,eAAgB,kBACzB9W,QAhFF,SAAiBC,GACf,IAAQC,EAAoCD,EAApCC,QAASC,EAA2BF,EAA3BE,aAAcC,EAAaH,EAAbG,SAE/BD,EAAaE,UAAU0W,WAAa,SAElCjW,GAEA,OAAIpD,EAAGH,OAAOuD,IACZQ,KAAKR,QAAQ0V,QAAQzV,SAA8B,IAApBD,EAAQC,QACvCO,KAAKC,aAAa,UAAWT,GAC7BQ,KAAKE,YAAY,UAAWV,GAErBQ,MAGL5D,EAAGI,KAAKgD,IACVQ,KAAKR,QAAQ0V,QAAQzV,QAAUD,EAExBQ,MAGFA,KAAKR,QAAQ0V,SAGtBtW,EAAQK,IAAIiW,QAAUA,GACtBtW,EAAQM,WAAWgW,QAAU,aAE7BpW,EAASF,QAAQsW,QAAUA,GAAQpW,QACrC,EAqDEK,UAAW,CACT,4BAA6BqV,GAC7B,2BAA4BA,GAC5B,0BAA2BA,GAE3B,mBAAoB,SAAApW,GAAcA,EAAXb,YACT2X,QAAU,CACpBF,MAAO,EACPJ,SAAU,EACVE,MAAO,EACPM,WAAY,EACZD,cAAe,EAElB,EAED,mBAAoB,SAAC/V,GACnB,KAAIA,EAAI7B,YAAY+L,SAASnM,OAAS,GAAtC,CAIA,IAAMuY,EAAiBtW,EAAIC,aAAaG,QAAQ0V,QAEhD,GAAMQ,GAAkBA,EAAejW,QAMvC,OAFAL,EAAIS,OAAS,CAAEpC,KAAM,YAEd,CAVP,CAWF,GAGFqB,SAAU,CAAE,EAEZqB,UAAS,WACP,MAAO,EACR,EAEDC,gBAAiB,SAACC,GAAY,OAAgC,IAA3BA,EAAKC,OAAO,UAAgB,GAGjEqV,GAAeT,GCyBf,SAASU,GACPnY,EACA2H,EACAvH,EACAnB,EACAmZ,EACA3P,EACA4P,GAGA,IAAK1Q,EACH,OAAO,EAIT,IAAc,IAAVA,EAAgB,CAElB,IAAMd,EAAQlI,EAAGG,OAAO2J,EAAK5B,OAAS4B,EAAK5B,MAAQ4B,EAAK/B,MAAQ+B,EAAKhC,KAC/DK,EAASnI,EAAGG,OAAO2J,EAAK3B,QAAU2B,EAAK3B,OAAS2B,EAAK7B,OAAS6B,EAAK9B,IAoBzE,GAjBA0R,EAASpO,KAAK4C,IAAIwL,EAAQpO,KAAKqO,KAAc,SAATtY,GAA4B,UAATA,EAAmB6G,EAAQC,GAAU,IAExFD,EAAQ,IACG,SAAT7G,EACFA,EAAO,QACW,UAATA,IACTA,EAAO,SAGP8G,EAAS,IACE,QAAT9G,EACFA,EAAO,SACW,WAATA,IACTA,EAAO,QAIE,SAATA,EAAiB,CACnB,IAAMuY,EAAO1R,GAAS,EAAI4B,EAAKhC,KAAOgC,EAAK/B,MAC3C,OAAOtG,EAAKK,EAAI8X,EAAOF,CACzB,CACA,GAAa,QAATrY,EAAgB,CAClB,IAAMuY,EAAOzR,GAAU,EAAI2B,EAAK9B,IAAM8B,EAAK7B,OAC3C,OAAOxG,EAAKC,EAAIkY,EAAOF,CACzB,CAEA,GAAa,UAATrY,EACF,OAAOI,EAAKK,GAAKoG,GAAS,EAAI4B,EAAK/B,MAAQ+B,EAAKhC,MAAQ4R,EAE1D,GAAa,WAATrY,EACF,OAAOI,EAAKC,GAAKyG,GAAU,EAAI2B,EAAK7B,OAAS6B,EAAK9B,KAAO0R,CAE7D,CAGA,QAAK1Z,EAAGM,QAAQA,KAITN,EAAGM,QAAQ0I,GAEdA,IAAU1I,EAEVoM,EAAgBpM,EAAS0I,EAAOyQ,GACtC,CA8HA,SAASI,GAAe9B,GAMrB,IALD9V,EAAM8V,EAAN9V,OACAd,EAAW4W,EAAX5W,YAKA,GAAkC,WAA9BA,EAAYC,SAASC,MAAsBF,EAAY2Y,WAA3D,CAEA,IACMC,EAAc9X,EADJd,EAAY8B,aAAaG,QAG7B4W,OAAOC,QACc,MAA3B9Y,EAAY2Y,WACdC,EAAY5X,MAAML,EAAIiY,EAAY5X,MAAMT,EAExCqY,EAAY5X,MAAMT,EAAIqY,EAAY5X,MAAML,EAE1CiY,EAAYG,KAAO,OAEnBH,EAAYG,KAAO/Y,EAAY2Y,WAEA,MAA3B3Y,EAAY2Y,WACdC,EAAY5X,MAAMT,EAAI,EACc,MAA3BP,EAAY2Y,aACrBC,EAAY5X,MAAML,EAAI,GAlB6C,CAqBzE,CAEA,IClcIqY,GACA/T,GDicE4T,GAAiB,CACrB3X,GAAI,iBACJ+W,OAAQ,CAAC,gBACT9W,QApVF,SAAiBC,GACf,IACEC,EAIED,EAJFC,QACAwC,EAGEzC,EAHFyC,QACAvC,EAEEF,EAFFE,aACAC,EACEH,EADFG,SAKFsX,GAAOI,QA+KT,SAAqBpV,GACnB,OAAOA,EAAQU,MACX,CACE5D,EAAG,WACHJ,EAAG,WACHkL,GAAI,YAEJ5E,IAAK,WACLF,KAAM,WACNG,OAAQ,WACRF,MAAO,WACPsS,QAAS,YACTC,YAAa,YACbC,SAAU,YACVC,WAAY,aAEd,CACE1Y,EAAG,YACHJ,EAAG,YACHkL,GAAI,cAEJ5E,IAAK,YACLF,KAAM,YACNG,OAAQ,YACRF,MAAO,YACPsS,QAAS,cACTC,YAAa,cACbC,SAAU,cACVC,WAAY,cAEpB,CA7MmBC,CAAYzV,GAC7BgV,GAAOU,cAAgB1V,EAAQE,eAAiBF,EAAQI,qBAAuB,GAAK,GAEpF3C,EAAaE,UAAUgY,UAAY,SAA8BvX,GAC/D,OA0EJ,SAAmBH,EAA4BG,EAAgDb,GAC7F,GAAIvC,EAAGH,OAAOuD,GAiBZ,OAhBAH,EAAaG,QAAQ4W,OAAO3W,SAA8B,IAApBD,EAAQC,QAC9CJ,EAAaY,aAAa,SAAUT,GACpCH,EAAaa,YAAY,SAAUV,GAE/BpD,EAAGK,OAAO+C,EAAQ9B,OAAS,eAAed,KAAK4C,EAAQ9B,MACzD2B,EAAaG,QAAQ4W,OAAO1Y,KAAO8B,EAAQ9B,KACjB,OAAjB8B,EAAQ9B,OACjB2B,EAAaG,QAAQ4W,OAAO1Y,KAAOiB,EAAMG,SAASF,QAAQwX,OAAO1Y,MAG/DtB,EAAGI,KAAKgD,EAAQwX,qBAClB3X,EAAaG,QAAQ4W,OAAOY,oBAAsBxX,EAAQwX,oBACjD5a,EAAGI,KAAKgD,EAAQ6W,UACzBhX,EAAaG,QAAQ4W,OAAOC,OAAS7W,EAAQ6W,QAGxChX,EAET,GAAIjD,EAAGI,KAAKgD,GAGV,OAFAH,EAAaG,QAAQ4W,OAAO3W,QAAUD,EAE/BH,EAET,OAAOA,EAAaG,QAAQ4W,MAC9B,CApGWW,CAAU/W,KAAMR,EAASb,IAGlCC,EAAQK,IAAImX,OAASA,GACrBxX,EAAQM,WAAWkX,OAAS,YAE5BtX,EAASF,QAAQwX,OAASA,GAAOtX,QACnC,EAgUEK,UAAW,CACT,mBAAoB,SAAAiV,GAAcA,EAAX7W,YACT2Y,WAAa,IAC1B,EAED,4BAA6B,SAAC9W,IAhIlC,SAAc9B,GAAyF,IAAtFe,EAAMf,EAANe,OAAQd,EAAWD,EAAXC,YACvB,GAAkC,WAA9BA,EAAYC,SAASC,MAAsBF,EAAYC,SAAS6I,MAApE,CAIA,IAAM8P,EAAc9X,EACd6H,EAAO3I,EAAY2I,KAEzB3I,EAAY0Z,OAAS,CACnBlZ,MAAOuH,EAAO,CAAE,EAAEY,GAClBgM,UAAW5M,EAAO,CAAE,EAAEY,GACtBgR,SAAU5R,EAAO,CAAE,EAAEY,GACrB3H,MAAO,CACL2F,KAAM,EACNC,MAAO,EACPG,MAAO,EACPF,IAAK,EACLC,OAAQ,EACRE,OAAQ,IAIZ4R,EAAY9P,MAAQ9I,EAAYC,SAAS6I,MACzC8P,EAAYjQ,KAAO3I,EAAY0Z,OAAO/E,UACtCiE,EAAYgB,UAAY5Z,EAAY0Z,OAAO1Y,KArB3C,CAsBF,CAwGMR,CAAMqB,GACN6W,GAAgB7W,EACjB,EACD,2BAA4B,SAACA,IAzGjC,SAAahB,GAAyF,IAAtFC,EAAMD,EAANC,OAAQd,EAAWa,EAAXb,YACtB,GAAkC,WAA9BA,EAAYC,SAASC,MAAsBF,EAAYC,SAAS6I,MAApE,CAEA,IAAM8P,EAAc9X,EAEd+Y,EADgB7Z,EAAY8B,aAAaG,QAAQ4W,OAC1BgB,OACvBC,EAAwB,eAAXD,GAAsC,WAAXA,EAExCE,EAAU/Z,EAAY2I,KAC5BqR,EAAoEha,EAAY0Z,OAAjEO,EAASD,EAAhBxZ,MAAkBmU,EAASqF,EAATrF,UAAkBiF,EAASI,EAAhBhZ,MAAkB2Y,EAAQK,EAARL,SAIvD,GAFA5R,EAAO4R,EAAUhF,GAEbmF,GAIF,GAFA/R,EAAO4M,EAAWoF,GAEH,eAAXF,EAAyB,CAE3B,GAAIlF,EAAU9N,IAAM8N,EAAU7N,OAAQ,CACpC,IAAMoT,EAAOvF,EAAU9N,IAEvB8N,EAAU9N,IAAM8N,EAAU7N,OAC1B6N,EAAU7N,OAASoT,CACrB,CACA,GAAIvF,EAAUhO,KAAOgO,EAAU/N,MAAO,CACpC,IAAMsT,EAAOvF,EAAUhO,KAEvBgO,EAAUhO,KAAOgO,EAAU/N,MAC3B+N,EAAU/N,MAAQsT,CACpB,CACF,OAGAvF,EAAU9N,IAAMsD,KAAK4C,IAAIgN,EAAQlT,IAAKoT,EAAUnT,QAChD6N,EAAU7N,OAASqD,KAAK+C,IAAI6M,EAAQjT,OAAQmT,EAAUpT,KACtD8N,EAAUhO,KAAOwD,KAAK4C,IAAIgN,EAAQpT,KAAMsT,EAAUrT,OAClD+N,EAAU/N,MAAQuD,KAAK+C,IAAI6M,EAAQnT,MAAOqT,EAAUtT,MAMtD,IAAK,IAAM8R,KAHX9D,EAAU5N,MAAQ4N,EAAU/N,MAAQ+N,EAAUhO,KAC9CgO,EAAU3N,OAAS2N,EAAU7N,OAAS6N,EAAU9N,IAE7B8N,EACjBiF,EAAUnB,GAAQ9D,EAAU8D,GAAQkB,EAASlB,GAG/CG,EAAY9P,MAAQ9I,EAAYC,SAAS6I,MACzC8P,EAAYjQ,KAAOgM,EACnBiE,EAAYgB,UAAYA,CAhDmD,CAiD7E,CAwDMhZ,CAAKiB,GACL6W,GAAgB7W,EACjB,EACD,0BAzDJ,SAAYsS,GAAyF,IAAtFrT,EAAMqT,EAANrT,OAAQd,EAAWmU,EAAXnU,YACrB,GAAkC,WAA9BA,EAAYC,SAASC,MAAsBF,EAAYC,SAAS6I,MAApE,CAEA,IAAM8P,EAAc9X,EAEpB8X,EAAY9P,MAAQ9I,EAAYC,SAAS6I,MACzC8P,EAAYjQ,KAAO3I,EAAY0Z,OAAO/E,UACtCiE,EAAYgB,UAAY5Z,EAAY0Z,OAAO1Y,KANgC,CAO7E,EAkDI,mBA5UJ,SAAuBa,GACrB,IAAQ7B,EAAsD6B,EAAtD7B,YAAa8B,EAAyCD,EAAzCC,aAAc3C,EAA2B0C,EAA3B1C,QAASwJ,EAAkB9G,EAAlB8G,KAAM5G,EAAYF,EAAZE,QAElD,GAAK4G,EAAL,CAIA,IAAMrI,EAAOyH,EAAO,CAAE,EAAE/H,EAAYI,OAAOC,IAAIC,MACzC6Z,EAAgBrY,EAAaG,QAAQ4W,OAE3C,GACIsB,GAAiBA,EAAcjY,WAEhClC,EAAYmC,gBACX,gBAAgB9C,KAAKW,EAAYoC,cACU,IAA1CL,EAAUoY,EAAc9X,eAL7B,CAWA,GAAIxD,EAAGH,OAAOyb,EAAcrR,OAAQ,CAClC,IAAMsR,EAAc,CAClBzT,MAAM,EACNC,OAAO,EACPC,KAAK,EACLC,QAAQ,GAGV,IAAK,IAAM2R,KAAQ2B,EACjBA,EAAY3B,GAAQJ,GAClBI,EACA0B,EAAcrR,MAAM2P,GACpBnY,EACAN,EAAYqa,eAAeC,YAC3Bnb,EACAwJ,EACAwR,EAAc5B,QAAUM,GAAOU,eAInCa,EAAYzT,KAAOyT,EAAYzT,OAASyT,EAAYxT,MACpDwT,EAAYvT,IAAMuT,EAAYvT,MAAQuT,EAAYtT,QAE9CsT,EAAYzT,MAAQyT,EAAYxT,OAASwT,EAAYvT,KAAOuT,EAAYtT,UAC1EjF,EAAIS,OAAS,CACXpC,KAAM,SACN4I,MAAOsR,GAGb,KAAO,CACL,IAAMxT,EAA+B,MAAvBuT,EAAcha,MAAgBG,EAAKK,EAAIgI,EAAK/B,MAAQiS,GAAOU,cACnEzS,EAAgC,MAAvBqT,EAAcha,MAAgBG,EAAKC,EAAIoI,EAAK7B,OAAS+R,GAAOU,eAEvE3S,GAASE,KACXjF,EAAIS,OAAS,CACXpC,KAAM,SACN6Y,MAAOnS,EAAQ,IAAM,KAAOE,EAAS,IAAM,KAGjD,CAEA,OAAOjF,EAAIS,aAAiBxE,CA5C5B,CAbA,CA0DF,GAgREyD,SAAU,CACRuX,QAAQ,EACRW,qBAAqB,EACrBtZ,KAAM,KAGNoY,OAAQgC,IAMRzR,MAAO,KAMP+Q,OAAQ,QAGVZ,QAAS,KAETrW,UAAS,SAAAkU,GAAqC,IAAlChO,EAAKgO,EAALhO,MAAO3I,EAAI2W,EAAJ3W,KAAMD,EAAI4W,EAAJ5W,KACjB+Y,EAAUJ,GAAOI,QACnB1P,EAAiB,KAErB,GAAIpJ,EACFoJ,EAAS0P,EAAQ/Y,EAAOC,QACnB,GAAI2I,EAAO,CACE,IAAlB,IAAI0R,EAAY,GAAE7Q,IAAA8Q,EAEC,CAAC,MAAO,SAAU,OAAQ,SAAQ9Q,EAAA8Q,EAAA7a,OAAA+J,IAAE,CAAlD,IAAM8O,EAAIgC,EAAA9Q,GACTb,EAAM2P,KACR+B,GAAa/B,EAEjB,CAEAlP,EAAS0P,EAAQuB,EACnB,CAEA,OAAOjR,CACR,EAED1G,gBAAiB,SAACC,GAAY,OAA+B,IAA1BA,EAAKC,OAAO,SAAe,EAE9DwW,cAAe,MAGjBmB,GAAe7B,GE3fAxX,GAAA,CACbH,GAAI,UACJC,QAAO,SAACC,GACNA,EAAMoT,UAAUmD,IAChBvW,EAAMoT,UAAUqE,IAChBzX,EAAMoT,UAAUvT,GAChBG,EAAMoT,UAAUtD,GAClB,GDrBEyJ,GAAW,EAuCA,IAAAC,GAAA,CACb5B,QAAS,SAAC6B,GAA8B,OAAK7B,GAAQ6B,EAAS,EAC9D5V,OAAQ,SAAC6V,GAAa,OAAK7V,GAAO6V,EAAM,EACxC9c,KAtCF,SAAc+c,GAIZ,GAHA/B,GAAU+B,EAAOC,sBACjB/V,GAAS8V,EAAOE,sBAEXjC,GACyC,IAA5C,IAAMkC,EAAU,CAAC,KAAM,MAAO,SAAU,KAAIvR,IAAAA,EAEvBuR,EAAOtb,OAAA+J,IAAE,CAAzB,IAAMwR,EAAUD,EAAOvR,GAC1BqP,GAAU+B,EAAM,GAAA/Q,OAAImR,EAAyD,0BAC7ElW,GACE8V,EAAM/Q,GAAAA,OAAImR,EAAuD,0BACjEJ,EAAM,GAAA/Q,OAAImR,EAA8D,+BAC5E,CAGFnC,GAAUA,IAAWA,GAAQoC,KAAKL,GAClC9V,GAASA,IAAUA,GAAOmW,KAAKL,GAE1B/B,KACHA,GAAU,SAAC6B,GACT,IAAMQ,EAAWC,KAAKC,MAChBC,EAAarR,KAAK+C,IAAI,EAAG,IAAMmO,EAAWV,KAC1CG,EAAQC,EAAOU,YAAW,WAE9BZ,EAASQ,EAAWG,EACrB,GAAEA,GAGH,OADAb,GAAWU,EAAWG,EACfV,GAGT7V,GAAS,SAAC6V,GAAK,OAAKY,aAAaZ,EAAM,EAE3C,GEUA,IAAMa,GAAa,CACjBpa,SAAU,CACRW,SAAS,EACTqW,OAAQ,GAGRqD,UAAW,KAGXC,MAAO,KAGTN,IAAKD,KAAKC,IAEVvb,YAAa,KACb8O,EAAG,EAGHnO,EAAG,EACHJ,EAAG,EAEHub,aAAa,EACbC,SAAU,EACVxD,OAAQ,EACRsD,MAAO,EAEPrb,MAAK,SAACR,GACJ2b,GAAWG,aAAc,EACzBlB,GAAI3V,OAAO0W,GAAW7M,GAEtB9O,EAAY2b,WAAaA,GACzBA,GAAW3b,YAAcA,EACzB2b,GAAWI,SAAWJ,GAAWJ,MACjCI,GAAW7M,EAAI8L,GAAI5B,QAAQ2C,GAAWxU,OACvC,EAED6U,KAAI,WACFL,GAAWG,aAAc,EACrBH,GAAW3b,cACb2b,GAAW3b,YAAY2b,WAAa,MAEtCf,GAAI3V,OAAO0W,GAAW7M,EACvB,EAGD3H,OAAM,WACJ,IAAQnH,EAAgB2b,GAAhB3b,YACA8B,EAA0B9B,EAA1B8B,aAAc3C,EAAYa,EAAZb,QAChB6J,EAAahJ,EAAYC,SAASC,KAClC+B,EAAUH,EAAaG,QAAQ+G,GAAY2S,WAC3CC,EAAYK,GAAaha,EAAQ2Z,UAAW9Z,EAAc3C,GAC1Doc,EAAMI,GAAWJ,MAEjBW,GAAMX,EAAMI,GAAWI,UAAY,IAEnCI,EAAIla,EAAQ4Z,MAAQK,EAE1B,GAAIC,GAAK,EAAG,CACV,IAAMC,EAAW,CACfzb,EAAGgb,GAAWhb,EAAIwb,EAClB5b,EAAGob,GAAWpb,EAAI4b,GAGpB,GAAIC,EAASzb,GAAKyb,EAAS7b,EAAG,CAC5B,IAAM8b,EAAaC,GAAUV,GAEzB/c,EAAGZ,OAAO2d,GACZA,EAAUQ,SAASA,EAASzb,EAAGyb,EAAS7b,GAC/Bqb,IACTA,EAAUtU,YAAc8U,EAASzb,EACjCib,EAAUpU,WAAa4U,EAAS7b,GAGlC,IAAMgc,EAAYD,GAAUV,GACtB5a,EAAQ,CACZL,EAAG4b,EAAU5b,EAAI0b,EAAW1b,EAC5BJ,EAAGgc,EAAUhc,EAAI8b,EAAW9b,IAG1BS,EAAML,GAAKK,EAAMT,IACnBuB,EAAauO,KAAK,CAChBvN,KAAM,aACNsF,OAAQjJ,EACR2C,aAAAA,EACAd,MAAAA,EACAhB,YAAAA,EACA4b,UAAAA,GAGN,CAEAD,GAAWI,SAAWR,CACxB,CAEII,GAAWG,cACblB,GAAI3V,OAAO0W,GAAW7M,GACtB6M,GAAW7M,EAAI8L,GAAI5B,QAAQ2C,GAAWxU,QAEzC,EACDqV,MAAKA,SAAC1a,EAA4BkH,GAAwB,IAAAyT,EAGxD,OAAqC,OAArCA,EAFgB3a,EAAaG,QAEd+G,GAAY2S,iBAAU,EAA9Bc,EAAgCva,OACxC,EACDwa,kBAAiB,SAAA3c,GAMd,IALDC,EAAWD,EAAXC,YACAsL,EAAOvL,EAAPuL,QAKA,GACItL,EAAY2c,eAAiBhB,GAAWa,MAAMxc,EAAY8B,aAAc9B,EAAYC,SAASC,MAKjG,GAAIF,EAAY4c,WACdjB,GAAWhb,EAAIgb,GAAWpb,EAAI,MADhC,CAKA,IAAIsG,EACAD,EACAE,EACAH,EAEI7E,EAA0B9B,EAA1B8B,aAAc3C,EAAYa,EAAZb,QAChB6J,EAAahJ,EAAYC,SAASC,KAClC+B,EAAUH,EAAaG,QAAQ+G,GAAY2S,WAC3CC,EAAYK,GAAaha,EAAQ2Z,UAAW9Z,EAAc3C,GAEhE,GAAIN,EAAGZ,OAAO2d,GACZjV,EAAO2E,EAAQmB,QAAUkP,GAAWpD,OACpC1R,EAAMyE,EAAQoB,QAAUiP,GAAWpD,OACnC3R,EAAQ0E,EAAQmB,QAAUmP,EAAUiB,WAAalB,GAAWpD,OAC5DzR,EAASwE,EAAQoB,QAAUkP,EAAUkB,YAAcnB,GAAWpD,WACzD,CACL,IAAM5P,EAAOqF,EAA8B4N,GAE3CjV,EAAO2E,EAAQmB,QAAU9D,EAAKhC,KAAOgV,GAAWpD,OAChD1R,EAAMyE,EAAQoB,QAAU/D,EAAK9B,IAAM8U,GAAWpD,OAC9C3R,EAAQ0E,EAAQmB,QAAU9D,EAAK/B,MAAQ+U,GAAWpD,OAClDzR,EAASwE,EAAQoB,QAAU/D,EAAK7B,OAAS6U,GAAWpD,MACtD,CAEAoD,GAAWhb,EAAIiG,EAAQ,EAAID,GAAQ,EAAI,EACvCgV,GAAWpb,EAAIuG,EAAS,EAAID,GAAO,EAAI,EAElC8U,GAAWG,cAEdH,GAAWpD,OAAStW,EAAQsW,OAC5BoD,GAAWE,MAAQ5Z,EAAQ4Z,MAE3BF,GAAWnb,MAAMR,GAlCnB,CAoCF,GAGK,SAASic,GAAapU,EAAY/F,EAA4B3C,GACnE,OACGN,EAAGK,OAAO2I,GAASM,EAAsBN,EAAO/F,EAAc3C,GAAW0I,IAAUtJ,EAAUY,EAElG,CAEO,SAASmd,GAAUV,GAKxB,OAJI/c,EAAGZ,OAAO2d,KACZA,EAAY3d,OAAOE,SAAS4e,MAGvB,CAAEpc,EAAGib,EAAUtU,WAAY/G,EAAGqb,EAAUpU,UACjD,CAuCA,IAAMwV,GAA2B,CAC/B9b,GAAI,cACJC,QA9NF,SAAiBC,GACf,IAAQG,EAAsBH,EAAtBG,SAAUF,EAAYD,EAAZC,QAElBD,EAAMua,WAAaA,GACnBA,GAAWJ,IAAM,WAAA,OAAMna,EAAMma,KAAK,EAElCla,EAAQiV,eAAe2G,YAAa,EACpC1b,EAAS2b,UAAUvB,WAAaA,GAAWpa,QAC7C,EAuNEK,UAAW,CACT,mBAAoB,SAAAuS,GAAcA,EAAXnU,YACT2b,WAAa,IAC1B,EAED,uBAAwB,SAAA/E,GAAcA,EAAX5W,YACb2b,WAAa,KACzBA,GAAWK,OACPL,GAAW3b,cACb2b,GAAW3b,YAAc,KAE5B,EAED,oBAAqB2b,GAAWK,KAEhC,2BAA4B,SAACna,GAAQ,OAAK8Z,GAAWe,kBAAkB7a,EAAI,IAI/Esb,GAAeH,GCnRR,SAASI,GAAqBC,EAAiCC,GACpE,IAAIC,GAAS,EAEb,OAAO,WAML,OALKA,IACDtf,EAAeuf,QAAQC,KAAKH,GAC9BC,GAAS,GAGJF,EAAO5U,MAAMhG,KAAM4G,WAE9B,CAEO,SAASqU,GAAiC1V,EAAwBiD,GAKvE,OAJAjD,EAAK9H,KAAO+K,EAAI/K,KAChB8H,EAAK7H,KAAO8K,EAAI9K,KAChB6H,EAAKc,MAAQmC,EAAInC,MAEVd,CACT,CCgKA,SAAS2V,GAAgCtH,GACvC,OAAIxX,EAAGI,KAAKoX,IACV5T,KAAKR,QAAQ0b,YAActH,EAEpB5T,MAGQ,OAAb4T,UACK5T,KAAKR,QAAQ0b,YAEblb,MAGFA,KAAKR,QAAQ0b,WACtB,CAEA,SAASC,GAAkCnI,GACzC,OAAI5W,EAAGD,KAAK6W,IACVhT,KAAKR,QAAQ2b,cAAgBnI,EAEtBhT,MAGO,OAAZgT,UACKhT,KAAKR,QAAQ2b,cAEbnb,MAGFA,KAAKR,QAAQ2b,aACtB,CAEe,IAAAC,GAAA,CACb3c,GAAI,iCACJC,QAlGF,SAAiBC,GACf,IACEE,EACEF,EADFE,aAGFA,EAAaE,UAAUsc,UAAY,SAEjCxS,EACAa,EACAnM,EACAb,GAEA,IAAMmD,EAsBV,SACER,EACAqK,EACAnM,EACAb,EACAiC,GAEA,IAAMuH,EAAO7G,EAAauG,QAAQlJ,GAC5B4C,EACHoK,EAAqBpK,SACtB,CACE,EAAG,EACH,EAAG,EACH,EAAG,EACH,EAAG,IACFoK,EAAqB4R,QACpBlc,EAAM,CACVS,OAAQ,KACRR,aAAAA,EACA9B,YAAAA,EACAb,QAAAA,EACAwJ,KAAAA,EACA5G,QAAAA,GAKF,OAFAX,EAAMiP,KAAK,mBAAoBxO,GAExBA,EAAIS,MACb,CAlDmB0b,CAAqBvb,KAAM0J,EAAOnM,EAAab,EAASiC,GAEvE,OAAIqB,KAAKR,QAAQ2b,cACRnb,KAAKR,QAAQ2b,cAActS,EAASa,EAAO7J,EAAQG,KAAMtD,EAASa,GAGpEsC,GAGThB,EAAaE,UAAUyc,WAAab,IAAS,SAA8B/G,GACzE,OAAO5T,KAAKyb,kBAAkB,aAAc7H,EAC7C,GAAE,qGAEH/U,EAAaE,UAAU2c,UAAYf,IAAS,SAA8B/G,GACxE,OAAO5T,KAAKyb,kBAAkB,YAAa7H,EAC5C,GAAE,mGAEH/U,EAAaE,UAAUoc,cAAgBA,GAEvCtc,EAAaE,UAAUmc,YAAcA,EACvC,GC2BA,SAASS,GACP9b,EACAR,EACA3C,EACAmb,EACAlZ,GAEA,OACEU,EAAauc,gBAAgBvc,EAAaG,QAAQK,EAAOpC,MAAOf,EAASmb,IACzExY,EAAaG,QAAQK,EAAOpC,MAAMgC,SAClCoc,GAAuBxc,EAAc3C,EAASmD,EAAQlB,GAE/CkB,EAGF,IACT,CAEA,SAASic,GACPve,EACAsL,EACAa,EACAqS,EACAC,EACAnE,EACAlZ,GAEA,IAAK,IAAI0N,EAAI,EAAG4P,EAAMF,EAAQ5e,OAAQkP,EAAI4P,EAAK5P,IAAK,CAClD,IAAM6P,EAAQH,EAAQ1P,GAChB8P,EAAeH,EAAc3P,GAC7B+P,EAAcF,EAAMb,UAAUxS,EAASa,EAAOnM,EAAa4e,GAEjE,GAAKC,EAAL,CAIA,IAAMvc,EAAS8b,GAA2BS,EAAaF,EAAOC,EAActE,EAAalZ,GAEzF,GAAIkB,EACF,MAAO,CACLA,OAAAA,EACAR,aAAc6c,EACdxf,QAASyf,EARb,CAWF,CAEA,MAAO,CAAEtc,OAAQ,KAAMR,aAAc,KAAM3C,QAAS,KACtD,CAEA,SAAS2f,GACP9e,EACAsL,EACAa,EACAmO,EACAlZ,GAEA,IAAIod,EAA0B,GAC1BC,EAA2B,GAE3Btf,EAAUmb,EAEd,SAASyE,EAAYjd,GACnB0c,EAAQ7W,KAAK7F,GACb2c,EAAc9W,KAAKxI,EACrB,CAEA,KAAON,EAAGM,QAAQA,IAAU,CAC1Bqf,EAAU,GACVC,EAAgB,GAEhBrd,EAAM4P,cAAcgO,aAAa7f,EAAS4f,GAE1C,IAAME,EAAaV,GACjBve,EACAsL,EACAa,EACAqS,EACAC,EACAnE,EACAlZ,GAGF,GAAI6d,EAAW3c,SAAW2c,EAAWnd,aAAaG,QAAQgd,EAAW3c,OAAOpC,MAAMgf,YAChF,OAAOD,EAGT9f,EAAU6O,EAAoB7O,EAChC,CAEA,MAAO,CAAEmD,OAAQ,KAAMR,aAAc,KAAM3C,QAAS,KACtD,CAEA,SAASggB,GACPnf,EAAwB4W,EAUxBxV,GACA,IATEkB,EAAMsU,EAANtU,OACAR,EAAY8U,EAAZ9U,aACA3C,EAAOyX,EAAPzX,QAQFmD,EAASA,GAAU,CAAEpC,KAAM,MAE3BF,EAAY8B,aAAeA,EAC3B9B,EAAYb,QAAUA,EACtBue,GAAW1d,EAAYC,SAAUqC,GAEjCtC,EAAY2I,KAAO7G,GAAgBQ,EAAOpC,KAAO4B,EAAauG,QAAQlJ,GAAW,KAEjFigB,GAAqBpf,EAAaoB,GAElCA,EAAMiP,KAAK,qBAAsB,CAAErQ,YAAAA,GACrC,CAEA,SAASse,GACPxc,EACA3C,EACAmD,EACAlB,GAEA,IAAMa,EAAUH,EAAaG,QACvBod,EAAapd,EAAQK,EAAOpC,MAAMgN,IAClCoS,EAAgBrd,EAAQK,EAAOpC,MAAMof,cACrCC,EAAene,EAAMoe,UAAUC,gBACjCC,EAAqB,EACrBC,EAAoB,EACpBC,EAAe,EAGnB,KAAMP,GAAcC,GAAiBC,GACnC,OAAO,EACR,IAAA,IAAA5V,EAAAkW,EAAAA,EAEyBze,EAAM0e,aAAa7O,KAAItH,EAAAkW,EAAAjgB,OAAA+J,IAAE,CAA9C,IAAM3J,EAAW6f,EAAAlW,GACdoW,EAAc/f,EAAYC,SAASC,KAEzC,GAAKF,EAAY2c,cAAjB,CAMA,KAFA+C,GAE0BH,EACxB,OAAO,EAGT,GAAIvf,EAAY8B,eAAiBA,EAAjC,CAMA,IAFA6d,GAAqBI,IAAgBzd,EAAOpC,KAAO,EAAI,IAE9Bmf,EACvB,OAAO,EAGT,GAAIrf,EAAYb,UAAYA,IAC1BygB,IAEIG,IAAgBzd,EAAOpC,MAAQ0f,GAAgBN,GACjD,OAAO,CAZX,CAVA,CAyBF,CAEA,OAAOC,EAAe,CACxB,CAEA,SAASE,GAAgBpJ,EAAejV,GACtC,OAAIvC,EAAGG,OAAOqX,IACZjV,EAAMoe,UAAUC,gBAAkBpJ,EAE3B5T,MAGFrB,EAAMoe,UAAUC,eACzB,CAEA,SAASO,GAAU7gB,EAAkB8gB,EAAgB7e,GACnD,IAAuB8e,EAAsB9e,EAAMoe,UAA3CW,cAEJD,GAAqBA,IAAsB/gB,IAC7C+gB,EAAkBE,MAAMH,OAAS,IAGnC9gB,EAAQd,cAAcgJ,gBAAgB+Y,MAAMH,OAASA,EACrD9gB,EAAQihB,MAAMH,OAASA,EACvB7e,EAAMoe,UAAUW,cAAgBF,EAAS9gB,EAAU,IACrD,CAEA,SAASigB,GAA2Cpf,EAA6BoB,GAC/E,IAAQU,EAAoC9B,EAApC8B,aAAc3C,EAAsBa,EAAtBb,QAASc,EAAaD,EAAbC,SAE/B,GAAkC,UAA5BD,EAAYoC,aAA2BN,GAAgBA,EAAaG,QAAQ0b,YAAlF,CASA,IAAIsC,EAAS,GAEb,GAAIhgB,EAASC,KAAM,CACjB,IAAMmgB,EAAgBve,EAAaG,QAAQhC,EAASC,MAAMmgB,cAGxDJ,EADEphB,EAAGD,KAAKyhB,GACDA,EAAcpgB,EAAU6B,EAAc3C,EAASa,EAAYsgB,cAE3Dlf,EAAMC,QAAQK,IAAIzB,EAASC,MAAM0C,UAAU3C,EAExD,CAEA+f,GAAUhgB,EAAYb,QAAS8gB,GAAU,GAAI7e,EAd7C,MALMA,EAAMoe,UAAUW,eAClBH,GAAU5e,EAAMoe,UAAUW,cAAe,GAAI/e,EAmBnD,CAEA,IAAMoe,GAAoB,CACxBte,GAAI,kBACJ+W,OAAQ,CAAC,WACT9W,QA5TF,SAAiBC,GACf,IAAwBkT,EAAuBlT,EAAvCmT,eAA0BhT,EAAaH,EAAbG,SAElCH,EAAMoT,UAAUqJ,IAEhBtc,EAASgf,KAAK3C,cAAgB,KAC9Brc,EAASgf,KAAK5C,aAAc,EAE5B5V,EAAOxG,EAAS2b,UAAW,CACzBgC,aAAa,EACbhS,IAAK6K,IACLuH,cAAe,EACfnB,UAAW,KACXF,WAAY,KAIZ5b,aAAc,IAGhBiS,EAASmL,gBAAkB,SAACpJ,GAAgB,OAAKoJ,GAAgBpJ,EAAUjV,EAAM,EAEjFA,EAAMoe,UAAY,CAEhBC,gBAAiB1H,IACjBuG,uBAAAA,GACA6B,cAAe,KAEnB,EAiSEve,UAAW,CACT,oBAhSJ,SAAsB7B,EAEpBqB,GACA,IAFEpB,EAAWD,EAAXC,YAAasL,EAAOvL,EAAPuL,QAASa,EAAKpM,EAALoM,MAAOmO,EAAWva,EAAXua,YAG3Bta,EAAY2c,eAGhBwC,GAAQnf,EADW8e,GAAc9e,EAAasL,EAASa,EAAOmO,EAAalZ,GAC1CA,EACnC,EAyRI,oBAAqB,SAACS,EAAKT,IAvR/B,SAAsBP,EAEpBO,GACA,IAFEpB,EAAWa,EAAXb,YAAasL,EAAOzK,EAAPyK,QAASa,EAAKtL,EAALsL,MAAOmO,EAAWzZ,EAAXyZ,YAGC,UAA5Bta,EAAYoC,aAA2BpC,EAAYmC,eAAiBnC,EAAY2c,eAGpFwC,GAAQnf,EADW8e,GAAc9e,EAAasL,EAASa,EAAOmO,EAAwBlZ,GACrDA,EACnC,CAgRMof,CAAc3e,EAAKT,GA9QzB,SAAqBS,EAAsCT,GACzD,IAAQpB,EAAgB6B,EAAhB7B,YAER,GACGA,EAAYmC,gBACbnC,EAAY2c,eACX3c,EAAYygB,iBACZzgB,EAAYC,SAASC,KAJxB,CASAkB,EAAMiP,KAAK,yBAA0BxO,GAErC,IAAQC,EAAiB9B,EAAjB8B,aACFkH,EAAchJ,EAAwCC,SAASC,KAEjE8I,GAAclH,IAGdA,EAAaG,QAAQ+G,GAAYkW,cAChCZ,GAAuBxc,EAAc9B,EAAYb,QAASa,EAAYC,SAAUmB,GAEjFpB,EAAYgc,QAEZhc,EAAYQ,MAAMR,EAAYC,SAAU6B,EAAc9B,EAAYb,SAClEigB,GAAqBpf,EAAaoB,IAhBtC,CAmBF,CAkPMsf,CAAY7e,EAAKT,EAClB,EACD,oBAlPJ,SAA0B+S,EAAgD/S,GAAc,IAA3DpB,EAAWmU,EAAXnU,YACnB8B,EAAiB9B,EAAjB8B,aAEJA,GAAgBA,EAAaG,QAAQ0b,aACvCqC,GAAUhgB,EAAYb,QAAS,GAAIiC,EAEvC,GA8OEqe,gBAAAA,GACAnB,uBAAAA,GACAF,eAAAA,IAGFuC,GAAenB,GC/UA,IAAAoB,GAAA,CACb1f,GAAI,sBACJU,UAAW,CAAE,yBA3Ef,SAAoB7B,EAAwEqB,GAAc,IAAnFpB,EAAWD,EAAXC,YAAasa,EAAWva,EAAXua,YAAa9M,EAAEzN,EAAFyN,GAAIC,EAAE1N,EAAF0N,GACnD,GAAkC,SAA9BzN,EAAYC,SAASC,KAAzB,CAGA,IAAM2gB,EAAO1W,KAAKqO,IAAIhL,GAChBsT,EAAO3W,KAAKqO,IAAI/K,GAChBsT,EAAgB/gB,EAAY8B,aAAaG,QAAQhB,KACjDuB,EAAYue,EAAcve,UAC1Bwe,EAAcH,EAAOC,EAAO,IAAMD,EAAOC,EAAO,IAAM,KAQ5D,GANA9gB,EAAYC,SAASE,KACQ,UAA3B4gB,EAAcxe,SACTye,EAAY,GACbD,EAAcxe,SAGA,OAAhBye,GAAsC,OAAdxe,GAAsBA,IAAcwe,EAAa,CAEzEhhB,EAAwCC,SAASC,KAAO,KA8B1D,IA3BA,IAAIf,EAAUmb,EAER2G,EAAe,SAAUnf,GAC7B,GAAIA,IAAiB9B,EAAY8B,aAAjC,CAEA,IAAMG,EAAUjC,EAAY8B,aAAaG,QAAQhB,KAEjD,IAAKgB,EAAQid,aAAepd,EAAauc,gBAAgBpc,EAAS9C,EAASmb,GAAc,CACvF,IAAMhY,EAASR,EAAagc,UAC1B9d,EAAYkhB,YACZlhB,EAAYmhB,UACZnhB,EACAb,GAGF,GACEmD,GACgB,SAAhBA,EAAOpC,MAyBjB,SAAwBsC,EAAmBV,GACzC,IAAKA,EACH,OAAO,EAGT,IAAMsf,EAAWtf,EAAaG,QAAQhB,KAAKuB,UAE3C,MAAqB,OAAdA,GAAmC,OAAb4e,GAAqBA,IAAa5e,CACjE,CAhCU6e,CAAeL,EAAalf,IAC5B0d,GAAUpB,eAAe9b,EAAQR,EAAc3C,EAASmb,EAAalZ,GAErE,OAAOU,CAEX,CApB+C,GAwB1CjD,EAAGM,QAAQA,IAAU,CAC1B,IAAM2C,EAAeV,EAAM4P,cAAcgO,aAAa7f,EAAS8hB,GAE/D,GAAInf,EAAc,CACd9B,EAAwCC,SAASC,KAAO,OAC1DF,EAAY8B,aAAeA,EAC3B9B,EAAYb,QAAUA,EACtB,KACF,CAEAA,EAAUqG,EAAWrG,EACvB,CACF,CA3D0C,CA4D5C,ICxCA,SAASmiB,GAAgBthB,GACvB,IAAMgJ,EAAahJ,EAAYC,UAAYD,EAAYC,SAASC,KAEhE,IAAK8I,EACH,OAAO,KAGT,IAAM/G,EAAUjC,EAAY8B,aAAaG,QAEzC,OAAOA,EAAQ+G,GAAYuY,MAAQtf,EAAQ+G,GAAYwY,KACzD,CAEA,IAAMD,GAAe,CACnBrgB,GAAI,kBACJC,QAvBF,SAAiBC,GACf,IAAQG,EAAaH,EAAbG,SAERH,EAAMoT,UAAUiN,IAEhBlgB,EAAS2b,UAAUqE,KAAO,EAC1BhgB,EAAS2b,UAAUsE,MAAQ,CAC7B,EAiBE5f,UAAW,CACT,mBAAoB,SAAA7B,GAAcA,EAAXC,YACT0hB,mBAAqB,IAClC,EAED,qBAAsB,SAAA7gB,GAAqB,IAAlBb,EAAWa,EAAXb,YACjBuhB,EAAOD,GAAgBthB,GAEzBuhB,EAAO,IACTvhB,EAAY0hB,mBAAqBjG,YAAW,WAC1Czb,EAAYQ,MAAMR,EAAYC,SAAUD,EAAY8B,aAAc9B,EAAYb,QAC/E,GAAEoiB,GAEN,EAED,oBAAqB,SAAApN,GAAgC,IAA7BnU,EAAWmU,EAAXnU,YAAa2hB,EAASxN,EAATwN,UAC/B3hB,EAAY0hB,oBAAsB1hB,EAAYygB,kBAAoBkB,IACpEjG,aAAa1b,EAAY0hB,oBACzB1hB,EAAY0hB,mBAAqB,KAEpC,EAGD,yBAA0B,SAAA9K,GAAqB,IAAlB5W,EAAW4W,EAAX5W,YACNshB,GAAgBthB,GAElB,IACjBA,EAAYC,SAASC,KAAO,KAEhC,GAEFohB,gBAAAA,IAEFM,GAAeL,GClEA/B,GAAA,CACbte,GAAI,aACJC,QAAO,SAACC,GACNA,EAAMoT,UAAUgL,IAChBpe,EAAMoT,UAAU+M,IAChBngB,EAAMoT,UAAUoM,GAClB,GCYIiB,GAAiB,SAA4CxL,GACjE,MAAI,wBAAwBhX,KAAKgX,IAC/B5T,KAAKR,QAAQ4f,eAAiBxL,EACvB5T,MAGL5D,EAAGI,KAAKoX,IACV5T,KAAKR,QAAQ4f,eAAiBxL,EAAW,SAAW,QAC7C5T,MAGFA,KAAKR,QAAQ4f,cACtB,EA0CA,SAASC,GAAkB/hB,GAAgF,IAA7EC,EAAWD,EAAXC,YAAamM,EAAKpM,EAALoM,MACrCnM,EAAY8B,cACd9B,EAAY8B,aAAaigB,uBAAuB5V,EAEpD,CA4Be,IAAA6V,GAAA,CACb9gB,GAAI,kCACJC,QA5BK,SAAiBC,GACtB,IAAQE,EAAiBF,EAAjBE,aAERA,EAAaE,UAAUqgB,eAAiBA,GAExCvgB,EAAaE,UAAUugB,uBAAyB,SAAU5V,GACxD,OApDJ,SAAgCrK,EAA4BV,EAAc+K,GACxE,IAAM8V,EAAUngB,EAAaG,QAAQ4f,eAErC,GAAgB,UAAZI,EAEJ,GAAgB,WAAZA,EAAJ,CAUA,GAAI7gB,EAAM0O,OAAOoS,iBAAmB,sBAAsB7iB,KAAK8M,EAAMrJ,MAAO,CAC1E,IAAMqf,EAAM5jB,EAAU4N,EAAM/D,QAAQjK,SAC9BikB,EAAahhB,EAAMihB,cAAcF,GAEvC,IAAMC,IAAcA,EAAWtS,SAAyC,IAA9BsS,EAAWtS,OAAOwS,QAC1D,MAEJ,CAGI,uCAAuCjjB,KAAK8M,EAAMrJ,OAMpDjE,EAAGM,QAAQgN,EAAM/D,SACjBzC,EAAgBwG,EAAM/D,OAAQ,0EAKhC+D,EAAM0V,gBA7BN,MAFE1V,EAAM0V,gBAgCV,CAcWE,CAAuBtf,KAAMrB,EAAO+K,IAI7C/K,EAAM0e,aAAayC,UAAU5a,KAAK,CAChC7E,KAAM,YACN0f,SAAQ,SAACrW,GAAO,IAAA,IAAAxC,EAAAkW,EAAAA,EACYze,EAAM0e,aAAa7O,KAAItH,EAAAkW,EAAAjgB,OAAA+J,IAAE,CAA9C,IAAM3J,EAAW6f,EAAAlW,GACpB,GACE3J,EAAYb,UACXa,EAAYb,UAAYgN,EAAM/D,QAAUhD,EAAapF,EAAYb,QAASgN,EAAM/D,SAGjF,YADApI,EAAY8B,aAAaigB,uBAAuB5V,EAGpD,CACF,GAEJ,EAKEvK,UAAW,CAAC,OAAQ,OAAQ,KAAM,UAAUiT,QAAO,SAACC,EAAK2N,GAEvD,OADA3N,kBAAG9K,OAAiByY,IAAeX,GAC5BhN,CACR,GAAE,KCxHU,SAAS4N,GAAiB5f,EAAczB,GACrD,GAAIA,EAAQiV,eAAexT,GACzB,OAAO,EAGT,IAAK,IAAM5C,KAAQmB,EAAQK,IACzB,GAA2B,IAAvBoB,EAAK4H,QAAQxK,IAAe4C,EAAK6f,OAAOziB,EAAKN,UAAWyB,EAAQuhB,OAClE,OAAO,EAIX,OAAO,CACT,CCVe,SAASC,GAAwB5a,GAC9C,IAAMD,EAAO,CAAA,EAEb,IAAK,IAAME,KAAQD,EAAQ,CACzB,IAAMJ,EAAQI,EAAOC,GAEjBrJ,EAAGW,YAAYqI,GACjBG,EAAKE,GAAQ2a,GAAMhb,GACVhJ,EAAGc,MAAMkI,GAClBG,EAAKE,GAAQiI,GAAStI,GAEtBG,EAAKE,GAAQL,CAEjB,CAEA,OAAOG,CACT,CCOA,IAAa8a,GAAY,WAUvB,SAAAA,EAAY9iB,GAA0BmO,OAAA2U,GAAArgB,KATtCsgB,OAA0B,GAAEtgB,KAC5BugB,YAAoB,CAAErc,KAAM,EAAGC,MAAO,EAAGC,IAAK,EAAGC,OAAQ,GAAGrE,KAC5DwgB,gBAAU,EAAAxgB,KACV8G,YAAM,EAAA9G,KACNygB,eAAS,EAAAzgB,KACT0gB,gBAAU,EAAA1gB,KACVqG,WAAK,EAAArG,KACIzC,iBAAW,EAGlByC,KAAKzC,YAAcA,EACnByC,KAAK8G,OAAS6Z,KACd3gB,KAAKqG,MAAQ,CACXnC,MAAM,EACNC,OAAO,EACPC,KAAK,EACLC,QAAQ,EAEZ,CA8TC,OA9TAyH,EAAAuU,EAAA,CAAA,CAAAtU,IAAA,QAAA3G,MAED,SAAA9H,EAAwCsjB,GAAmB,IAsW/B1a,EAAMvI,EAtW1B8W,EAAKnX,EAALmX,MACElX,EAAgByC,KAAhBzC,YACFsjB,EA6UV,SAAyBtjB,GACvB,IAAMiJ,EAAgBjJ,EAAY8B,aAAaG,QAAQjC,EAAYC,SAASC,MACtEqjB,EAAkBta,EAAcua,UAEtC,GAAID,GAAmBA,EAAgB3jB,OACrC,OAAO2jB,EAGT,MAAO,CAAC,OAAQ,WAAY,YAAa,WAAY,gBAAiB,gBACnE7hB,KAAI,SAACoB,GACJ,IAAMb,EAAUgH,EAAcnG,GAE9B,OACEb,GACAA,EAAQC,SAAW,CACjBD,QAAAA,EACAwhB,QAASxhB,EAAQyhB,SAGvB,IACCta,QAAO,SAACua,GAAC,QAAOA,IACrB,CAlWyBC,CAAgB5jB,GACrCyC,KAAKohB,cAAcP,GAEnB7gB,KAAK0gB,WAAapb,EAAO,CAAE,EAAE/H,EAAY8I,OACzCrG,KAAKqG,MAAQf,EAAO,CAAE,EAAEtF,KAAK0gB,YAC7B1gB,KAAKugB,aA+VqBra,EA/VO3I,EAAY2I,KA+VbvI,EA/VmBijB,EAgW9C1a,EACH,CACEhC,KAAMvG,EAAOO,EAAIgI,EAAKhC,KACtBE,IAAKzG,EAAOG,EAAIoI,EAAK9B,IACrBD,MAAO+B,EAAK/B,MAAQxG,EAAOO,EAC3BmG,OAAQ6B,EAAK7B,OAAS1G,EAAOG,GAE/B,CACEoG,KAAM,EACNE,IAAK,EACLD,MAAO,EACPE,OAAQ,IA1WZrE,KAAKwgB,WAAa,CAAEtiB,EAAG,EAAGJ,EAAG,GAE7B,IAAMsB,EAAMY,KAAKqhB,QAAQ,CACvB5M,MAAAA,EACAmM,WAAAA,EACAU,QAAQ,IAQV,OALAthB,KAAK8G,OAAS6Z,KACd3gB,KAAKuhB,SAASniB,GAEEY,KAAK8G,OAAS9G,KAAKwhB,OAAOpiB,EAG5C,GAAC,CAAA2M,IAAA,UAAA3G,MAED,SAAQhG,GACN,IAAQ7B,EAAgByC,KAAhBzC,YASR,OAPA6B,EAAI7B,YAAcA,EAClB6B,EAAIC,aAAe9B,EAAY8B,aAC/BD,EAAI1C,QAAUa,EAAYb,QAC1B0C,EAAI8G,OAAJ9G,EAAI8G,KAAS3I,EAAY2I,MACzB9G,EAAIiH,QAAJjH,EAAIiH,MAAUrG,KAAK0gB,YACnBthB,EAAImhB,YAAcvgB,KAAKugB,YAEhBnhB,CACT,GAAC,CAAA2M,IAAA,WAAA3G,MAED,SAAShG,GAAuC,IAAA,IAAA8H,EAAA,EAAAua,EAC1BzhB,KAAKsgB,OAAMpZ,EAAAua,EAAAtkB,OAAA+J,IAAE,CAA5B,IAAMwa,EAAKD,EAAAva,GACVwa,EAAMV,QAAQjjB,QAChBqB,EAAIsiB,MAAQA,EACZA,EAAMV,QAAQjjB,MAAMqB,GAExB,CACF,GAAC,CAAA2M,IAAA,SAAA3G,MAED,SAAOhG,GACL,IAAQqV,EAA+ErV,EAA/EqV,MAAO6M,EAAwEliB,EAAxEkiB,OAAQK,EAAgEviB,EAAhEuiB,cAAqBC,EAA2CxiB,EAAjD8G,KAA6B2b,EAAoBziB,EAA3BiH,MAE5DjH,EAAIzB,OAAS2H,EAAO,CAAE,EAAElG,EAAIwhB,YAC5BxhB,EAAI8G,KAAOZ,EAAO,CAAE,EAAEsc,GACtBxiB,EAAIiH,MAAQf,EAAO,CAAE,EAAEuc,GAI6B,IAFpD,IAAMvB,EAASqB,EAAgB3hB,KAAKsgB,OAAOtS,MAAM2T,GAAiB3hB,KAAKsgB,OAEjEwB,EAAYnB,GAAavhB,EAAIzB,OAAQyB,EAAI8G,MAAKyI,IAAAA,EAEhC2R,EAAMnjB,OAAAwR,IAAE,CAAA,IAAAoT,EAAjBL,EAASpB,EAAM3R,GAChBnP,EAAYkiB,EAAZliB,QACFwiB,EAAqB1c,EAAO,CAAE,EAAElG,EAAIzB,QACtCoI,EAAc,KAED,OAAbgc,EAAAL,EAAMV,UAANe,EAAe1Z,KAAOrI,KAAKiiB,SAASziB,EAAS8hB,EAAQ7M,KACvDrV,EAAIsiB,MAAQA,EACZ3b,EAAc2b,EAAMV,QAAQ3Y,IAAIjJ,GAEhC8iB,EAAmB9iB,EAAIiH,MAAOjH,EAAI8G,KAAM,CACtChI,EAAGkB,EAAIzB,OAAOO,EAAI8jB,EAAmB9jB,EACrCJ,EAAGsB,EAAIzB,OAAOG,EAAIkkB,EAAmBlkB,KAIzCgkB,EAAUK,WAAWjd,KAAKa,EAC5B,CAEAT,EAAOtF,KAAKqG,MAAOjH,EAAIiH,OAEvByb,EAAUvjB,MAAML,EAAIkB,EAAIzB,OAAOO,EAAIkB,EAAIwhB,WAAW1iB,EAClD4jB,EAAUvjB,MAAMT,EAAIsB,EAAIzB,OAAOG,EAAIsB,EAAIwhB,WAAW9iB,EAElDgkB,EAAUM,UAAUle,KAAO9E,EAAI8G,KAAKhC,KAAO0d,EAAe1d,KAC1D4d,EAAUM,UAAUje,MAAQ/E,EAAI8G,KAAK/B,MAAQyd,EAAezd,MAC5D2d,EAAUM,UAAUhe,IAAMhF,EAAI8G,KAAK9B,IAAMwd,EAAexd,IACxD0d,EAAUM,UAAU/d,OAASjF,EAAI8G,KAAK7B,OAASud,EAAevd,OAE9D,IAAMge,EAAariB,KAAK8G,OAAOnJ,OACzB2kB,EAAWtiB,KAAK8G,OAAOZ,KAE7B,GAAImc,GAAcC,EAAU,CAC1B,IAAMC,EACJT,EAAU5b,KAAKhC,OAASoe,EAASpe,MACjC4d,EAAU5b,KAAK/B,QAAUme,EAASne,OAClC2d,EAAU5b,KAAK9B,MAAQke,EAASle,KAChC0d,EAAU5b,KAAK7B,SAAWie,EAASje,OAErCyd,EAAUU,QACRD,GAAeF,EAAWnkB,IAAM4jB,EAAUnkB,OAAOO,GAAKmkB,EAAWvkB,IAAMgkB,EAAUnkB,OAAOG,CAC5F,CAEA,OAAOgkB,CACT,GAAC,CAAA/V,IAAA,qBAAA3G,MAED,SAAmBhG,GACjB,IAAQ7B,EAAgByC,KAAhBzC,YACAkX,EAAUrV,EAAVqV,MACFgO,EAAYllB,EAAYI,OAAOC,IAC/B8kB,EAAcnlB,EAAYI,OAAOI,MAC/B+I,EAAuB9G,KAAvB8G,OAAQ0Z,EAAexgB,KAAfwgB,WACVmC,EAAW7b,EAAOvI,MAEV,UAAVkW,GACFnP,EAAOtF,KAAKwgB,WAAY1Z,EAAOvI,OAChC,IAAAuP,IAAAA,EAAA4D,EAAAA,EAEgC,CAC/B,CAACgR,EAAalC,GACd,CAACiC,EAAWE,IACb7U,EAAA4D,EAAAvU,OAAA2Q,IAAW,CAHP,IAAA8U,EAAAlR,EAAA5D,GAAO+U,EAASD,EAAA,GAAErkB,EAAKqkB,EAAA,GAI1BC,EAAUhlB,KAAKK,GAAKK,EAAML,EAC1B2kB,EAAUhlB,KAAKC,GAAKS,EAAMT,EAC1B+kB,EAAU7kB,OAAOE,GAAKK,EAAML,EAC5B2kB,EAAU7kB,OAAOF,GAAKS,EAAMT,CAC9B,CAEA,IAAQskB,EAAcpiB,KAAK8G,OAAnBsb,UACFlc,EAAO9G,EAAI8G,MAAQ3I,EAAY2I,KAErCA,EAAKhC,MAAQke,EAAUle,KACvBgC,EAAK/B,OAASie,EAAUje,MACxB+B,EAAK9B,KAAOge,EAAUhe,IACtB8B,EAAK7B,QAAU+d,EAAU/d,OAEzB6B,EAAK5B,MAAQ4B,EAAK/B,MAAQ+B,EAAKhC,KAC/BgC,EAAK3B,OAAS2B,EAAK7B,OAAS6B,EAAK9B,GACnC,GAAC,CAAA2H,IAAA,cAAA3G,MAED,SACEhG,GAOA,IAAQ7B,EAAgByC,KAAhBzC,YACAkX,EAAiCrV,EAAjCqV,MAAO6M,EAA0BliB,EAA1BkiB,OAAQK,EAAkBviB,EAAlBuiB,cAEjB7a,EAAS9G,KAAKwhB,OAClBxhB,KAAKqhB,QAAQ,CACXC,OAAAA,EACA7M,MAAAA,EACAmM,WAAYxhB,EAAI0jB,gBAAkBvlB,EAAYI,OAAOC,IAAIC,QAQ7D,GAJAmC,KAAK8G,OAASA,GAKXA,EAAO0b,WACNb,GAAiBA,EAAgB3hB,KAAKsgB,OAAOnjB,SAC/CI,EAAY2c,cAEZ,OAAO,EAGT,GAAI9a,EAAI0jB,eAAgB,CACtB,IAAQjlB,EAASN,EAAYI,OAAOC,IAA5BC,KACFklB,EAAa,CACjB7kB,EAAGkB,EAAI0jB,eAAe5kB,EAAIL,EAAKK,EAC/BJ,EAAGsB,EAAI0jB,eAAehlB,EAAID,EAAKC,GAGjCgJ,EAAOnJ,OAAOO,GAAK6kB,EAAW7kB,EAC9B4I,EAAOnJ,OAAOG,GAAKilB,EAAWjlB,EAC9BgJ,EAAOvI,MAAML,GAAK6kB,EAAW7kB,EAC7B4I,EAAOvI,MAAMT,GAAKilB,EAAWjlB,CAC/B,CAEAkC,KAAKgjB,mBAAmB5jB,EAC1B,GAAC,CAAA2M,IAAA,YAAA3G,MAED,SAAUhG,GACR,IAAQ7B,EAAuB6B,EAAvB7B,YAAamM,EAAUtK,EAAVsK,MACf4W,EAAStgB,KAAKsgB,OAEpB,GAAKA,GAAWA,EAAOnjB,OAAvB,CAIoB,IAApB,IAAI8lB,GAAW,EAAKjU,IAAAA,EAEAsR,EAAMnjB,OAAA6R,IAAE,CAAvB,IAAM0S,EAASpB,EAAMtR,GACxB5P,EAAIsiB,MAAQA,EACZ,IAAQliB,EAAqBkiB,EAArBliB,QAASwhB,EAAYU,EAAZV,QAEXkC,EAAclC,EAAQmC,WAAanC,EAAQmC,UAAU/jB,GAE3D,GAAI8jB,EAEF,OADAljB,KAAKygB,UAAYyC,GACV,EAGTD,EAAWA,IAAcA,GAAYjjB,KAAKiiB,SAASziB,GAAS,EAAMJ,EAAIqV,OAAO,EAC/E,CAEIwO,GAEF1lB,EAAYY,KAAK,CAAEuL,MAAAA,EAAO4X,QAAQ,GApBpC,CAsBF,GAAC,CAAAvV,IAAA,OAAA3G,MAED,SAAKhG,GACH,IAAQ7B,EAAgB6B,EAAhB7B,YAER,GAAKyC,KAAKsgB,QAAWtgB,KAAKsgB,OAAOnjB,OAAjC,CAIA,IAAMimB,EAAoC9d,EACxC,CACEgb,OAAQtgB,KAAKsgB,OACbjhB,aAAc9B,EAAY8B,aAC1B3C,QAASa,EAAYb,QACrBwJ,KAAM,MAER9G,GAGFY,KAAKqhB,QAAQ+B,GAAY,IAAA,IAAA/T,EAAA,EAAAgU,EAELrjB,KAAKsgB,OAAMjR,EAAAgU,EAAAlmB,OAAAkS,IAAE,CAA5B,IAAMqS,EAAK2B,EAAAhU,GACd+T,EAAY1B,MAAQA,EAEhBA,EAAMV,QAAQzH,MAChBmI,EAAMV,QAAQzH,KAAK6J,EAEvB,CAEApjB,KAAKsgB,OAAS,KACdtgB,KAAKygB,UAAY,IAvBjB,CAwBF,GAAC,CAAA1U,IAAA,gBAAA3G,MAED,SAAcyb,GACZ7gB,KAAKsgB,OAAS,GAEd,IAAK,IAAI7S,EAAQ,EAAGA,EAAQoT,EAAa1jB,OAAQsQ,IAAS,CACxD,IAAA6V,EAAmCzC,EAAapT,GAAxCjO,EAAO8jB,EAAP9jB,QAASwhB,EAAOsC,EAAPtC,QAASvjB,EAAI6lB,EAAJ7lB,KAE1BuC,KAAKsgB,OAAOpb,KAAK,CACf1F,QAAAA,EACAwhB,QAAAA,EACAvT,MAAAA,EACAhQ,KAAAA,GAEJ,CAEA,OAAOuC,KAAKsgB,MACd,GAAC,CAAAvU,IAAA,2BAAA3G,MAED,SAAA+O,GAAwG,IAAAoP,EAAApP,EAA7E5W,YAAeI,EAAM4lB,EAAN5lB,OAAQuI,EAAIqd,EAAJrd,KAAMsd,EAAYD,EAAZC,aACtD,GAAKA,EAAa1c,OAAlB,CAQC,IAND,IAAQ0Z,EAAegD,EAAfhD,WACRiD,EAAuCD,EAAa1c,OAArC6b,EAAQc,EAAfllB,MAAiB6jB,EAASqB,EAATrB,UAKxBsB,EAAA,EAAArP,EAHuB,CACtB,CAAC1W,EAAOI,MAAOyiB,GACf,CAAC7iB,EAAOC,IAAK+kB,IAGiCe,EAAArP,EAAAlX,OAAAumB,IAAS,CAApD,IAAAC,EAAAtP,EAAAqP,GAAOb,EAASc,EAAA,GAAEplB,EAAKolB,EAAA,GAC1Bd,EAAUhlB,KAAKK,GAAKK,EAAML,EAC1B2kB,EAAUhlB,KAAKC,GAAKS,EAAMT,EAC1B+kB,EAAU7kB,OAAOE,GAAKK,EAAML,EAC5B2kB,EAAU7kB,OAAOF,GAAKS,EAAMT,CAC9B,CAEAoI,EAAKhC,MAAQke,EAAUle,KACvBgC,EAAK/B,OAASie,EAAUje,MACxB+B,EAAK9B,KAAOge,EAAUhe,IACtB8B,EAAK7B,QAAU+d,EAAU/d,MApBC,CAqB5B,GAAC,CAAA0H,IAAA,WAAA3G,MAED,SAAS5F,EAAS8hB,EAAkB7M,EAAgBmP,GAClD,SAEGpkB,IACmB,IAApBA,EAAQC,SAEPmkB,IAAmBpkB,EAAQqkB,SAE3BrkB,EAAQqkB,UAAYvC,GAEV,UAAV7M,IAAsBjV,EAAQskB,SAMnC,GAAC,CAAA/X,IAAA,WAAA3G,MAED,SAAS2e,GACP/jB,KAAKugB,YAAcwD,EAAMxD,YACzBvgB,KAAKwgB,WAAauD,EAAMvD,WACxBxgB,KAAK0gB,WAAaqD,EAAMrD,WACxB1gB,KAAKqG,MAAQ0d,EAAM1d,MACnBrG,KAAKsgB,OAASyD,EAAMzD,OAAOrhB,KAAI,SAACya,GAAC,OAAK0G,GAAM1G,EAAE,IAC9C1Z,KAAK8G,OAAS6Z,GAAarb,EAAO,CAAE,EAAEye,EAAMjd,OAAOnJ,QAAS2H,EAAO,CAAA,EAAIye,EAAMjd,OAAOZ,MACtF,GAAC,CAAA6F,IAAA,UAAA3G,MAED,WACE,IAAK,IAAMK,KAAQzF,KACjBA,KAAKyF,GAAQ,IAEjB,KAAC4a,CAAA,CAjVsB,GAoVzB,SAASM,GAAahjB,EAAgBuI,GACpC,MAAO,CACLA,KAAAA,EACAvI,OAAAA,EACAY,MAAO,CAAEL,EAAG,EAAGJ,EAAG,GAClBskB,UAAW,CACTle,KAAM,EACNC,MAAO,EACPC,IAAK,EACLC,OAAQ,GAEV8d,WAAY,GACZK,SAAS,EAEb,CCjWO,SAASwB,GAKdC,EAAiDxmB,GACjD,IAAQqB,EAAamlB,EAAbnlB,SACFkiB,EAAU,CACdjjB,MAAOkmB,EAAOlmB,MACdsK,IAAK4b,EAAO5b,IACZ8a,UAAWc,EAAOd,UAClB5J,KAAM0K,EAAO1K,MAGT2K,EAAW,SAACC,GAChB,IAAM3kB,EAAW2kB,GAAY,GAK7B,IAAK,IAAM1e,KAHXjG,EAAQC,SAA8B,IAApBD,EAAQC,QAGPX,EACX2G,KAAQjG,IACVA,EAAgBiG,GAAQ3G,EAAS2G,IAIvC,IAAMyb,EAA6C,CACjD1hB,QAAAA,EACAwhB,QAAAA,EACAvjB,KAAAA,EACA2mB,OAAQ,WAEN,OADA5kB,EAAQC,SAAU,EACXyhB,CACR,EACDmD,QAAS,WAEP,OADA7kB,EAAQC,SAAU,EACXyhB,CACT,GAGF,OAAOA,GAST,OANIzjB,GAAwB,iBAATA,IAEjBymB,EAASI,UAAYxlB,EACrBolB,EAASjD,SAAWD,GAGfkD,CACT,CAEO,SAASK,GAAiBjnB,GAM9B,IALDe,EAAMf,EAANe,OAMMyI,EALKxJ,EAAXC,YAK2BimB,aAAc1c,OAErCA,IACFzI,EAAO0iB,UAAYja,EAAOqb,WAE9B,CAEA,IAAMqC,GAAwB,CAC5B/lB,GAAI,iBACJ+W,OAAQ,CAAC,WACT9W,QAAS,SAACC,GACRA,EAAMG,SAAS2b,UAAUsG,UAAY,EACtC,EACD5hB,UAAW,CACT,mBAAoB,SAAAf,GAAqB,IAAlBb,EAAWa,EAAXb,YACrBA,EAAYimB,aAAe,IAAInD,GAAa9iB,EAC7C,EAED,mCAAoC,SAAC6B,GACnC,IAAQ7B,EAAgB6B,EAAhB7B,YACFimB,EAAepkB,EAAI7B,YAAYimB,aAErCA,EAAazlB,MAAMqB,EAAK7B,EAAYI,OAAOI,MAAMF,MACjDN,EAAY8I,MAAQmd,EAAand,MACjCmd,EAAaR,mBAAmB5jB,EACjC,EAED,kCAAmC,SAACA,GAClC,IAAQ7B,EAAgB6B,EAAhB7B,YACAimB,EAAiBjmB,EAAjBimB,aACFiB,EAAMjB,EAAakB,YAAYtlB,GAGrC,OAFA7B,EAAY8I,MAAQmd,EAAand,MAE1Boe,CACR,EAED,iCAAkC,SAACrlB,GACjC,IAAQ7B,EAAgB6B,EAAhB7B,YACAimB,EAAiBjmB,EAAjBimB,aACFiB,EAAMjB,EAAaL,UAAU/jB,GAGnC,OAFA7B,EAAY8I,MAAQmd,EAAa9C,WAE1B+D,CACR,EAED,4BAA6BF,GAC7B,2BAA4BA,GAC5B,0BAA2BA,GAE3B,kCAAmC,SAACnlB,GAAG,OAAKA,EAAI7B,YAAYimB,aAAamB,yBAAyBvlB,EAAI,EACtG,iCAAkC,SAACA,GAAG,OAAKA,EAAI7B,YAAYimB,aAAamB,yBAAyBvlB,EAAI,EAErG,oBAAqB,SAACA,GAAG,OAAKA,EAAI7B,YAAYimB,aAAajK,KAAKna,EAAI,IAIxE0e,GAAe0G,GC9GF1lB,GAAqB,CAChCgf,KAAM,CACJsB,eAAgB,OAChBxU,YAAa,QAGf6P,UAAW,CACThb,SAAS,EACTgH,OAAQ,CAAEvI,EAAG,EAAGJ,EAAG,IAGrBc,QAAS,CAAC,GCVCgmB,YAAapY,GAAAC,EAAAmY,EAAApY,GAAA,IAAAE,EAAAC,EAAAiY,GAkCxB,SAAAA,EACErnB,EACAmM,EACAnD,EACAkO,EACA/X,EACA4kB,EACAjhB,GACA,IAAAyM,EAAApB,OAAAkZ,IACA9X,EAAAJ,EAAAK,UAAMxP,IArCR0P,cAAgC,KAAIH,EACpC5C,aAAO,EAAA4C,EACP3C,aAAO,EAAA2C,EACPwO,YAAM,EAAAxO,EACNxN,aAAO,EAAAwN,EACP+X,aAAO,EAAA/X,EACPgY,cAAQ,EAAAhY,EACRiY,YAAM,EAAAjY,EACNkY,aAAO,EAAAlY,EACPjP,UAAI,EAAAiP,EACJ9O,YAAM,EAAA8O,EACNvO,WAAK,EAAAuO,EACL5G,UAAI,EAAA4G,EACJmY,QAAE,EAAAnY,EACFoY,QAAE,EAAApY,EACFqY,QAAE,EAAArY,EACF2M,QAAE,EAAA3M,EACFsY,cAAQ,EAAAtY,EACRuY,cAAQ,EAAAvY,EACRwY,cAAQ,EAAAxY,EACR7O,cAAQ,EAAA6O,EACRsM,WAAK,EAAAtM,EACLyY,WAAK,EACLzY,EACAwJ,UAAI,EACJxJ,EACAwU,YAAM,EAaJ5kB,EAAUA,GAAWa,EAAYb,QAEjC,IAAMiJ,EAASpI,EAAY8B,aACrBuL,GAAiBjF,GAAUA,EAAOnG,SAAYV,IAAkB8L,YAChEnE,EAASH,EAAYX,EAAQjJ,EAAS6J,GACtCmO,EAAqB,UAAVD,EACXE,EAAmB,QAAVF,EACTY,EAAYX,EAAQ8Q,EAAA1Y,GAAUvP,EAAY8X,UAC1C1X,EAAS+W,EACXnX,EAAYI,OAAOI,MACnB4W,EACE,CAAE9W,KAAMwX,EAAUxX,KAAMG,OAAQqX,EAAUrX,OAAQyK,UAAWlL,EAAYI,OAAOC,IAAI6K,WACpFlL,EAAYI,OAAOC,IAkD+C,OAhDxEkP,EAAKjP,KAAOyH,EAAO,CAAE,EAAE3H,EAAOE,MAC9BiP,EAAK9O,OAASsH,EAAO,CAAE,EAAE3H,EAAOK,QAChC8O,EAAK5G,KAAOZ,EAAO,CAAE,EAAE/H,EAAY2I,MACnC4G,EAAKrE,UAAY9K,EAAO8K,UAEnBkM,IACH7H,EAAKjP,KAAKK,GAAKuI,EAAOvI,EACtB4O,EAAKjP,KAAKC,GAAK2I,EAAO3I,EAEtBgP,EAAK9O,OAAOE,GAAKuI,EAAOvI,EACxB4O,EAAK9O,OAAOF,GAAK2I,EAAO3I,GAG1BgP,EAAK+X,QAAUnb,EAAMmb,QACrB/X,EAAKiY,OAASrb,EAAMqb,OACpBjY,EAAKgY,SAAWpb,EAAMob,SACtBhY,EAAKkY,QAAUtb,EAAMsb,QACrBlY,EAAKwO,OAAU5R,EAAqB4R,OACpCxO,EAAKxN,QAAWoK,EAAqBpK,QACrCwN,EAAKnH,OAASjJ,EACdoQ,EAAKtB,cAAgB9O,EACrBoQ,EAAKwU,OAASA,EACdxU,EAAKzM,KAAOA,GAAQkG,GAAckO,GAAS,IAC3C3H,EAAKzN,aAAesG,EAEpBmH,EAAKqY,GAAKzQ,EAAWnX,EAAY+L,SAAS/L,EAAY+L,SAASnM,OAAS,GAAGsoB,SAAWpQ,EAAU8P,GAEhGrY,EAAKmY,GAAK1nB,EAAYI,OAAOI,MAAMF,KAAKK,EAAIuI,EAAOvI,EACnD4O,EAAKoY,GAAK3nB,EAAYI,OAAOI,MAAMF,KAAKC,EAAI2I,EAAO3I,EACnDgP,EAAKuY,SAAW9nB,EAAYI,OAAOI,MAAMC,OAAOE,EAAIuI,EAAOvI,EAC3D4O,EAAKwY,SAAW/nB,EAAYI,OAAOI,MAAMC,OAAOF,EAAI2I,EAAO3I,EAGzDgP,EAAKvO,MADHmW,GAAYC,EACD,CAAEzW,EAAG,EAAGJ,EAAG,GAEX,CACXI,EAAG4O,EAAKlC,GAAa1M,EAAImX,EAAUzK,GAAa1M,EAChDJ,EAAGgP,EAAKlC,GAAa9M,EAAIuX,EAAUzK,GAAa9M,GAIpDgP,EAAK2M,GAAKlc,EAAYI,OAAOY,MAAMkK,UACnCqE,EAAKsY,SAAWtY,EAAKrE,UAAYqE,EAAKqY,GAGtCrY,EAAK7O,SAAWqH,EAAO,CAAE,EAAE/H,EAAYI,OAAOM,SAAS2M,IACvDkC,EAAKsM,MAAQ3R,EAAMqF,EAAK7O,SAASC,EAAG4O,EAAK7O,SAASH,GAElDgP,EAAKyY,MAAQ5Q,GAAoB,iBAAVF,EAA2B3H,EAAK4Y,WAAa,KAAI5Y,CAC1E,CAkDC,OAlDAhB,EAAA8Y,EAAA,CAAA,CAAA7Y,IAAA,WAAA3G,MAED,WACE,IAAM7H,EAAcyC,KAAK6L,aAEzB,GAAItO,EAAY8X,UAAU+D,MAAQ,KAAOpZ,KAAKyI,UAAYlL,EAAY8X,UAAU5M,UAAY,IAC1F,OAAO,KAGT,IAAIuM,EAAS,IAAMtN,KAAKwD,MAAM3N,EAAY8X,UAAUsQ,UAAWpoB,EAAY8X,UAAUuQ,WAAcle,KAAKyD,GAGpG6J,EAAQ,IACVA,GAAS,KAGX,IAAM9Q,EAAO,OAAiB8Q,GAASA,EAAQ,MACzC5S,EAAK,OAAiB4S,GAASA,EAAQ,MAK7C,MAAO,CACL5S,GAAAA,EACAC,MAJYD,GAAM,MAAgB4S,GAASA,EAAQ,MAKnD9Q,KAAAA,EACAC,OAPaD,IAAS,OAAiB8Q,GAASA,EAAQ,MAQxDA,MAAAA,EACAoE,MAAO7b,EAAY8X,UAAU+D,MAC7Bnb,SAAU,CACRC,EAAGX,EAAY8X,UAAUuQ,UACzB9nB,EAAGP,EAAY8X,UAAUsQ,WAG/B,GAAC,CAAA5Z,IAAA,iBAAA3G,MAED,WAAkB,GAElB,CAAA2G,IAAA,2BAAA3G,MAGA,WACEpF,KAAK2L,4BAA8B3L,KAAK4L,oBAAqB,CAC/D,GAEA,CAAAG,IAAA,kBAAA3G,MAGA,WACEpF,KAAK4L,oBAAqB,CAC5B,KAACgZ,CAAA,EA3JOnZ,IAgKVvD,OAAO2d,iBAAiBjB,GAAc7lB,UAAW,CAC/C+K,MAAO,CACL1B,IAAG,WACD,OAAOpI,KAAKnC,KAAKK,CAClB,EACDmK,IAAG,SAACjD,GACFpF,KAAKnC,KAAKK,EAAIkH,CAChB,GAEF2E,MAAO,CACL3B,IAAG,WACD,OAAOpI,KAAKnC,KAAKC,CAClB,EACDuK,IAAG,SAACjD,GACFpF,KAAKnC,KAAKC,EAAIsH,CAChB,GAGF4E,QAAS,CACP5B,IAAG,WACD,OAAOpI,KAAKhC,OAAOE,CACpB,EACDmK,IAAG,SAACjD,GACFpF,KAAKhC,OAAOE,EAAIkH,CAClB,GAEF6E,QAAS,CACP7B,IAAG,WACD,OAAOpI,KAAKhC,OAAOF,CACpB,EACDuK,IAAG,SAACjD,GACFpF,KAAKhC,OAAOF,EAAIsH,CAClB,GAGF2F,GAAI,CACF3C,IAAG,WACD,OAAOpI,KAAKzB,MAAML,CACnB,EACDmK,IAAG,SAACjD,GACFpF,KAAKzB,MAAML,EAAIkH,CACjB,GAEF4F,GAAI,CACF5C,IAAG,WACD,OAAOpI,KAAKzB,MAAMT,CACnB,EACDuK,IAAG,SAACjD,GACFpF,KAAKzB,MAAMT,EAAIsH,CACjB,GAGFwgB,UAAW,CACTxd,IAAG,WACD,OAAOpI,KAAK/B,SAASC,CACtB,EACDmK,IAAG,SAACjD,GACFpF,KAAK/B,SAASC,EAAIkH,CACpB,GAEFugB,UAAW,CACTvd,IAAG,WACD,OAAOpI,KAAK/B,SAASH,CACtB,EACDuK,IAAG,SAACjD,GACFpF,KAAK/B,SAASH,EAAIsH,CACpB,KCrQJ,IAAa0gB,GAAWha,GAOtB,SAAAga,EAAYrnB,EAAYoK,EAAsBa,EAAyB+b,EAAkBM,GAAkBra,OAAAoa,GAAA9lB,KAN3GvB,QAAE,EAAAuB,KACF6I,aAAO,EAAA7I,KACP0J,WAAK,EAAA1J,KACLylB,cAAQ,EAAAzlB,KACR+lB,gBAAU,EAGR/lB,KAAKvB,GAAKA,EACVuB,KAAK6I,QAAUA,EACf7I,KAAK0J,MAAQA,EACb1J,KAAKylB,SAAWA,EAChBzlB,KAAK+lB,WAAaA,CACpB,ICUUC,YAAAA,GAAY,OAAZA,EAAY,aAAA,GAAZA,EAAY,QAAA,GAAZA,EAAY,SAAA,GAAZA,EAAY,cAAA,GAAZA,EAAY,gBAAA,GAAZA,EAAY,OAAA,GAAZA,CAAY,EAAA,CAAA,GASZC,YAAAA,GAAa,OAAbA,EAAa,MAAA,GAAbA,EAAa,KAAA,GAAbA,EAAa,IAAA,GAAbA,EAAa,KAAA,GAAbA,EAAa,YAAA,GAAbA,CAAa,EAAA,CAAA,GAuErBC,GAAY,EAEHC,GAAW,WAkFtB,SAAAA,EAAA7oB,GAA4F,IAAAwP,EAAA9M,KAA9EL,EAAWrC,EAAXqC,YAAaymB,EAAS9oB,EAAT8oB,UAAS1a,OAAAya,GAjFpCnmB,KACAX,aAAoC,KAEpCW,KACAtD,QAA0B,KAAIsD,KAC9BkG,KAAwB,KACxBlG,KACAiX,YAAM,EAMNjX,KACAqG,MAA4B,KAE5BrG,KACAqmB,gBAAU,EAEVrmB,KACAxC,SAA2B,CACzBC,KAAM,KACNC,KAAM,KACN2I,MAAO,MACRrG,KAEDL,iBAAW,EAEXK,KACAsJ,SAA0B,GAE1BtJ,KACA0e,UAAqC,KAErC1e,KAAiBye,YAA2B,GAE5Cze,KACA4X,eAII,CACF/O,QAAS,KACTa,MAAO,KACPmO,YAAa,MAGf7X,KAAiBqV,UAA0C,KAAIrV,KAE/DN,eAAgB,EAAKM,KACrBge,iBAAkB,EAClBhe,KAAiB6d,cAAe,EAChC7d,KAAiBsmB,SAAU,EAC3BtmB,KAAiBumB,UAAW,EAC5BvmB,KAAiBgM,YAAM,EAEvBhM,KAAiBma,WAAa,KAAIna,KAMlCwmB,OAAS7L,IAAS,SAA6B8L,GAC7CzmB,KAAK7B,KAAKsoB,EACX,GAAE,0EAAyEzmB,KAE5ErC,OAAoB,CAElBI,MxBiDK,CACLF,KAAM,CAAEK,EAAG,EAAGJ,EAAG,GACjBE,OAAQ,CAAEE,EAAG,EAAGJ,EAAG,GACnB2K,UAAW,GwBlDXyE,KxB+CK,CACLrP,KAAM,CAAEK,EAAG,EAAGJ,EAAG,GACjBE,OAAQ,CAAEE,EAAG,EAAGJ,EAAG,GACnB2K,UAAW,GwBhDX7K,IxB6CK,CACLC,KAAM,CAAEK,EAAG,EAAGJ,EAAG,GACjBE,OAAQ,CAAEE,EAAG,EAAGJ,EAAG,GACnB2K,UAAW,GwB9CXlK,MxB2CK,CACLV,KAAM,CAAEK,EAAG,EAAGJ,EAAG,GACjBE,OAAQ,CAAEE,EAAG,EAAGJ,EAAG,GACnB2K,UAAW,GwB5CXxK,SxByCK,CACLJ,KAAM,CAAEK,EAAG,EAAGJ,EAAG,GACjBE,OAAQ,CAAEE,EAAG,EAAGJ,EAAG,GACnB2K,UAAW,IwBzCbzI,KAA0B0mB,IAAcR,KAGtClmB,KAAKqmB,WAAaD,EAClBpmB,KAAKL,YAAcA,EAEnB,IAAMgnB,EAAO3mB,KAEbA,KAAKgM,OAAS,GAAyB,IAAAjE,EAAA,SAAAgE,GAGrC7D,OAAOC,eAAe2E,EAAKd,OAAQD,EAAK,CACtC3D,IAAG,WACD,OAAOue,EAAK5a,EACd,KAJJ,IAAK,IAAMA,KAAOia,GAAYje,EAAAgE,GAM7B,IAAA6a,EAAA,SAAAC,GAGC3e,OAAOC,eAAe2E,EAAKd,OAAQD,EAAK,CACtC3G,MAAO,WAAA,OAAoBuhB,EAAK5a,GAAI/F,MAAT2gB,EAAI/f,UAAc,KAFjD,IAAK,IAAMmF,KAAOka,GAAaW,EAAAC,GAM/B7mB,KAAKqmB,WAAW,mBAAoB,CAAE9oB,YAAayC,MACrD,CA0aC,OA1aA8L,EAAAqa,EAAA,CAAA,CAAApa,IAAA,uBAAA3D,IA9CgB,WACf,OAAO,CACT,GAAC,CAAA2D,IAAA,cAAA3G,MA8CD,SAAYyD,EAAsBa,EAAyBmO,GACzD,IAAMiP,EAAe9mB,KAAK+mB,cAAcle,EAASa,EAAOmO,GAAa,GAC/DmP,EAAchnB,KAAKsJ,SAASwd,GAElC9mB,KAAKqmB,WAAW,oBAAqB,CACnCxd,QAAAA,EACAa,MAAAA,EACAmO,YAAAA,EACAiP,aAAAA,EACAE,YAAAA,EACA3mB,KAAM,OACN9C,YAAayC,MAEjB,GAEA,CAAA+L,IAAA,QAAA3G,MA+BA,SAA4BvF,EAAwBR,EAA4B3C,GAC9E,QACEsD,KAAKka,gBACJla,KAAKN,eACNM,KAAKsJ,SAASnM,QAA0B,YAAhB0C,EAAOpC,KAAqB,EAAI,KACvD4B,EAAaG,QAAQK,EAAOpC,MAA8BgC,WAK7Dwb,GAAWjb,KAAKxC,SAAUqC,GAE1BG,KAAKX,aAAeA,EACpBW,KAAKtD,QAAUA,EACfsD,KAAKkG,KAAO7G,EAAauG,QAAQlJ,GACjCsD,KAAKqG,MAAQrG,KAAKxC,SAAS6I,MACvBf,EAAO,CAAA,EAAItF,KAAKxC,SAAS6I,OACzB,CAAEnC,MAAM,EAAMC,OAAO,EAAMC,KAAK,EAAMC,QAAQ,GAClDrE,KAAKumB,UAAW,EAChBvmB,KAAK6d,aACH7d,KAAKinB,SAAS,CACZ1pB,YAAayC,KACb0J,MAAO1J,KAAK0e,UACZjK,MAAO,YACFzU,KAAKumB,SAEPvmB,KAAK6d,aACd,GAAC,CAAA9R,IAAA,cAAA3G,MAED,SAAYyD,EAAsBa,EAAyBmO,GACpD7X,KAAKma,YAAgBna,KAAKwjB,cAAgBxjB,KAAKwjB,aAAa/C,WAC/DzgB,KAAK+mB,cAAcle,EAASa,EAAOmO,GAAa,GAGlD,IAMI9M,EACAC,EAPEkc,EACJlnB,KAAKrC,OAAOC,IAAIC,KAAKK,IAAM8B,KAAKrC,OAAOuP,KAAKrP,KAAKK,GACjD8B,KAAKrC,OAAOC,IAAIC,KAAKC,IAAMkC,KAAKrC,OAAOuP,KAAKrP,KAAKC,GACjDkC,KAAKrC,OAAOC,IAAII,OAAOE,IAAM8B,KAAKrC,OAAOuP,KAAKlP,OAAOE,GACrD8B,KAAKrC,OAAOC,IAAII,OAAOF,IAAMkC,KAAKrC,OAAOuP,KAAKlP,OAAOF,EAMnDkC,KAAKN,gBAAkBM,KAAKge,kBAC9BjT,EAAK/K,KAAKrC,OAAOC,IAAII,OAAOE,EAAI8B,KAAKrC,OAAOI,MAAMC,OAAOE,EACzD8M,EAAKhL,KAAKrC,OAAOC,IAAII,OAAOF,EAAIkC,KAAKrC,OAAOI,MAAMC,OAAOF,EAEzDkC,KAAKge,gBAAkBvW,EAAMsD,EAAIC,GAAMhL,KAAKmnB,sBAG9C,IxBzR6Bxe,EAA4BpK,EACrDkb,EwBwREqN,EAAe9mB,KAAKonB,gBAAgBve,GACpC4d,EAAY,CAChB5d,QAAAA,EACAie,aAAAA,EACAE,YAAahnB,KAAKsJ,SAASwd,GAC3Bpd,MAAAA,EACArJ,KAAM,OACNwX,YAAAA,EACA9M,GAAAA,EACAC,GAAAA,EACAkU,UAAWgI,EACX3pB,YAAayC,MAGVknB,IxBvSwBve,EwBySG3I,KAAKrC,OAAOM,SxBzSaM,EwBySHyB,KAAKrC,OAAOY,MxBxS9Dkb,EAAK/R,KAAK+C,IAAIlM,EAAMkK,UAAY,IAAM,MAE5CE,EAAU9K,KAAKK,EAAIK,EAAMV,KAAKK,EAAIub,EAClC9Q,EAAU9K,KAAKC,EAAIS,EAAMV,KAAKC,EAAI2b,EAClC9Q,EAAU3K,OAAOE,EAAIK,EAAMP,OAAOE,EAAIub,EACtC9Q,EAAU3K,OAAOF,EAAIS,EAAMP,OAAOF,EAAI2b,EACtC9Q,EAAUF,UAAYgR,GwBqSpBzZ,KAAKqmB,WAAW,oBAAqBI,GAEhCS,GAAkBlnB,KAAKma,aAEtBna,KAAKka,gBACPuM,EAAUpmB,KAAO,KACjBL,KAAK7B,KAAKsoB,IAGRzmB,KAAKge,iBACP3K,GAAwBrT,KAAKrC,OAAOuP,KAAMlN,KAAKrC,OAAOC,KAG5D,GAEA,CAAAmO,IAAA,OAAA3G,MAkBA,SAAKqhB,GACEA,GAAcA,EAAU/c,OAC3B2J,GAA2BrT,KAAKrC,OAAOY,QAGzCkoB,EAAYnhB,EACV,CACEuD,QAAS7I,KAAK4X,eAAe/O,QAC7Ba,MAAO1J,KAAK4X,eAAelO,MAC3BmO,YAAa7X,KAAK4X,eAAeC,YACjCta,YAAayC,MAEfymB,GAAa,CAAA,IAGLhS,MAAQ,OAElBzU,KAAKinB,SAASR,EAChB,GAEA,CAAA1a,IAAA,YAAA3G,MAIA,SAAUyD,EAAsBa,EAAyBmO,EAAmBwP,GAC1E,IAAIP,EAAe9mB,KAAKonB,gBAAgBve,IAElB,IAAlBie,IACFA,EAAe9mB,KAAK+mB,cAAcle,EAASa,EAAOmO,GAAa,IAGjE,IAAMxX,EAAO,WAAWzD,KAAK8M,EAAMrJ,MAAQ,SAAW,KAEtDL,KAAKqmB,WAAU,gBAAA9e,OAAiBlH,GAAqD,CACnFwI,QAAAA,EACAie,aAAAA,EACAE,YAAahnB,KAAKsJ,SAASwd,GAC3Bpd,MAAAA,EACAmO,YAAAA,EACAxX,KAAMA,EACNgnB,eAAAA,EACA9pB,YAAayC,OAGVA,KAAKma,YACRna,KAAKsnB,IAAI5d,GAGX1J,KAAKunB,cAAc1e,EAASa,EAC9B,GAEA,CAAAqC,IAAA,eAAA3G,MACA,SAAasE,GACX1J,KAAKsnB,IAAI5d,GACT1J,KAAKqmB,WAAW,oBAAqB,CACnC3c,MAAAA,EACArJ,KAAM,OACN9C,YAAayC,MAEjB,GAEA,CAAA+L,IAAA,MAAA3G,MAcA,SAAIsE,GAGF,IAAI8d,EAFJxnB,KAAKsmB,SAAU,EACf5c,EAAQA,GAAS1J,KAAK4X,eAAelO,MAGjC1J,KAAKka,gBACPsN,EAAiBxnB,KAAKinB,SAAS,CAC7Bvd,MAAAA,EACAnM,YAAayC,KACbyU,MAAO,SAIXzU,KAAKsmB,SAAU,GAEQ,IAAnBkB,GACFxnB,KAAKuZ,MAET,GAAC,CAAAxN,IAAA,gBAAA3G,MAED,WACE,OAAOpF,KAAK6d,aAAe7d,KAAKxC,SAASC,KAAO,IAClD,GAAC,CAAAsO,IAAA,cAAA3G,MAED,WACE,OAAOpF,KAAK6d,YACd,GAAC,CAAA9R,IAAA,OAAA3G,MAED,WACEpF,KAAKqmB,WAAW,oBAAqB,CAAE9oB,YAAayC,OAEpDA,KAAKX,aAAeW,KAAKtD,QAAU,KAEnCsD,KAAK6d,cAAe,EACpB7d,KAAKumB,UAAW,EAChBvmB,KAAKxC,SAASC,KAAOuC,KAAKqV,UAAY,IACxC,GAEA,CAAAtJ,IAAA,kBAAA3G,MACA,SAAgByD,GACd,IAAMM,EAAYkK,GAA0BxK,GAG5C,MAA4B,UAArB7I,KAAKL,aAAgD,QAArBK,KAAKL,YACxCK,KAAKsJ,SAASnM,OAAS,EACvBuQ,GAAc1N,KAAKsJ,UAAU,SAACme,GAAU,OAAKA,EAAWhpB,KAAO0K,IACrE,GAEA,CAAA4C,IAAA,iBAAA3G,MACA,SAAeyD,GACb,OAAO7I,KAAKsJ,SAAStJ,KAAKonB,gBAAgBve,GAC5C,GAEA,CAAAkD,IAAA,gBAAA3G,MACA,SAAcyD,EAAsBa,EAAyBmO,EAAmBxV,GAC9E,IxBvd2BsG,EAA4BuE,EAAuBtP,EwBudxEa,EAAK4U,GAA0BxK,GACjCie,EAAe9mB,KAAKonB,gBAAgBve,GACpCme,EAAchnB,KAAKsJ,SAASwd,GAgDhC,OA9CAzkB,GAAgB,IAATA,IAAyBA,GAAQ,iBAAiBzF,KAAK8M,EAAMrJ,OAE/D2mB,EAMHA,EAAYne,QAAUA,GALtBme,EAAc,IAAIlB,GAAYrnB,EAAIoK,EAASa,EAAO,KAAM,MAExDod,EAAe9mB,KAAKsJ,SAASnM,OAC7B6C,KAAKsJ,SAASpE,KAAK8hB,IAKrB3T,GACErT,KAAKrC,OAAOC,IACZoC,KAAKsJ,SAASrK,KAAI,SAACqI,GAAC,OAAKA,EAAEuB,OAAO,IAClC7I,KAAK0nB,QxBzeoB/e,EwB2eC3I,KAAKrC,OAAOY,MxB3ee2O,EwB2eRlN,KAAKrC,OAAOuP,KxB3emBtP,EwB2eboC,KAAKrC,OAAOC,IxB1e/E+K,EAAU9K,KAAKK,EAAIN,EAAIC,KAAKK,EAAIgP,EAAKrP,KAAKK,EAC1CyK,EAAU9K,KAAKC,EAAIF,EAAIC,KAAKC,EAAIoP,EAAKrP,KAAKC,EAC1C6K,EAAU3K,OAAOE,EAAIN,EAAII,OAAOE,EAAIgP,EAAKlP,OAAOE,EAChDyK,EAAU3K,OAAOF,EAAIF,EAAII,OAAOF,EAAIoP,EAAKlP,OAAOF,EAChD6K,EAAUF,UAAY7K,EAAI6K,UAAYyE,EAAKzE,UwBwerCpG,IACFrC,KAAKN,eAAgB,EAErBsnB,EAAYvB,SAAWzlB,KAAKrC,OAAOC,IAAI6K,UACvCue,EAAYjB,WAAalO,EACzBxE,GAA2BrT,KAAKye,YAAa5V,GAExC7I,KAAKka,gBACR7G,GAAwBrT,KAAKrC,OAAOI,MAAOiC,KAAKrC,OAAOC,KACvDyV,GAAwBrT,KAAKrC,OAAOuP,KAAMlN,KAAKrC,OAAOC,KAEtDoC,KAAK0e,UAAYhV,EACjB1J,KAAKge,iBAAkB,IAI3Bhe,KAAK2nB,qBAAqB9e,EAASa,EAAOmO,GAE1C7X,KAAKqmB,WAAW,8BAA+B,CAC7Cxd,QAAAA,EACAa,MAAAA,EACAmO,YAAAA,EACAxV,KAAAA,EACA2kB,YAAAA,EACAF,aAAAA,EACAvpB,YAAayC,OAGR8mB,CACT,GAEA,CAAA/a,IAAA,gBAAA3G,MACA,SAAcyD,EAAsBa,GAClC,IAAMod,EAAe9mB,KAAKonB,gBAAgBve,GAE1C,IAAsB,IAAlBie,EAAJ,CAEA,IAAME,EAAchnB,KAAKsJ,SAASwd,GAElC9mB,KAAKqmB,WAAW,8BAA+B,CAC7Cxd,QAAAA,EACAa,MAAAA,EACAmO,YAAa,KACbiP,aAAAA,EACAE,YAAAA,EACAzpB,YAAayC,OAGfA,KAAKsJ,SAASlM,OAAO0pB,EAAc,GACnC9mB,KAAKN,eAAgB,CAdI,CAe3B,GAEA,CAAAqM,IAAA,uBAAA3G,MACA,SAAqByD,EAAsBa,EAAyBmO,GAClE7X,KAAK4X,eAAe/O,QAAUA,EAC9B7I,KAAK4X,eAAelO,MAAQA,EAC5B1J,KAAK4X,eAAeC,YAAcA,CACpC,GAAC,CAAA9L,IAAA,UAAA3G,MAED,WACEpF,KAAK4X,eAAe/O,QAAU,KAC9B7I,KAAK4X,eAAelO,MAAQ,KAC5B1J,KAAK4X,eAAeC,YAAc,IACpC,GAEA,CAAA9L,IAAA,uBAAA3G,MACA,SACEsE,EACA+K,EACA6M,EACAjhB,GAEA,OAAO,IAAIukB,GAAoB5kB,KAAM0J,EAAO1J,KAAKxC,SAASC,KAAMgX,EAAOzU,KAAKtD,QAAS4kB,EAAQjhB,EAC/F,GAEA,CAAA0L,IAAA,aAAA3G,MACA,SAAiC/G,GAA6B,IAAAupB,EAC3C,OAAjBA,EAAI5nB,KAACX,eAALuoB,EAAmBha,KAAKvP,KAEnB2B,KAAKqV,WAAahX,EAAOoK,WAAazI,KAAKqV,UAAU5M,aACxDzI,KAAKqV,UAAYhX,EAErB,GAEA,CAAA0N,IAAA,WAAA3G,MACA,SACEqhB,GAEA,IAAQ/c,EAA+B+c,EAA/B/c,MAAO+K,EAAwBgS,EAAxBhS,MAAO6M,EAAiBmF,EAAjBnF,OAAQjhB,EAASomB,EAATpmB,KACtB6F,EAASlG,KAATkG,KAYR,GAVIA,GAAkB,SAAVuO,IAEVyN,EAAmBliB,KAAKqG,MAAOH,EAAMlG,KAAKrC,OAAOY,MAAMyB,KAAKX,aAAaG,QAAQoL,cAEjF1E,EAAK5B,MAAQ4B,EAAK/B,MAAQ+B,EAAKhC,KAC/BgC,EAAK3B,OAAS2B,EAAK7B,OAAS6B,EAAK9B,MAKd,IAFApE,KAAKqmB,WAAU9e,8BAAAA,OAA+BkN,GAAgBgS,GAGjF,OAAO,EAGT,IAAMpoB,EAAUooB,EAAUpoB,OAAS2B,KAAK6nB,qBAAqBne,EAAO+K,EAAO6M,EAAQjhB,GAYnF,OAVAL,KAAKqmB,WAAU9e,uBAAAA,OAAwBkN,GAAgBgS,GAEzC,UAAVhS,IACFzU,KAAKqV,UAAYhX,GAGnB2B,KAAK8nB,WAAWzpB,GAEhB2B,KAAKqmB,WAAU9e,6BAAAA,OAA8BkN,GAAgBgS,IAEtD,CACT,GAEA,CAAA1a,IAAA,OAAA3G,MACA,WACE,OAAOyT,KAAKC,KACd,KAACqN,CAAA,CAnhBqB,GC1ExB,SAAS4B,GAAYzqB,GACnB0qB,GADiC1qB,EAAXC,YAExB,CAoBO,SAASyqB,GAAazqB,GAC3B,IA+BF,SAAoBA,GAClB,SAAUA,EAAY0qB,OAAOC,QAAQhqB,IAAKX,EAAY0qB,OAAOC,QAAQpqB,EACvE,CAjCOqqB,CAAW5qB,GACd,OAAO,EAGT,IAAQ2qB,EAAY3qB,EAAY0qB,OAAxBC,QASR,OAPAE,GAAY7qB,EAAYI,OAAOC,IAAKsqB,GACpCE,GAAY7qB,EAAYI,OAAOY,MAAO2pB,GACtChG,EAAmB3kB,EAAY8I,MAAO9I,EAAY2I,KAAMgiB,GAExDA,EAAQhqB,EAAI,EACZgqB,EAAQpqB,EAAI,GAEL,CACT,CAEA,SAASuqB,GAAQlU,GAAqC,IAAfjW,EAACiW,EAADjW,EAAGJ,EAACqW,EAADrW,EACxCkC,KAAKioB,OAAOC,QAAQhqB,GAAKA,EACzB8B,KAAKioB,OAAOC,QAAQpqB,GAAKA,EAEzBkC,KAAKioB,OAAOK,MAAMpqB,GAAKA,EACvB8B,KAAKioB,OAAOK,MAAMxqB,GAAKA,CACzB,CAEA,SAASsqB,GAAWhU,EAAAC,GAAoC,IAAjCxW,EAAIuW,EAAJvW,KAAMG,EAAMoW,EAANpW,OAAYE,EAACmW,EAADnW,EAAGJ,EAACuW,EAADvW,EAC1CD,EAAKK,GAAKA,EACVL,EAAKC,GAAKA,EACVE,EAAOE,GAAKA,EACZF,EAAOF,GAAKA,CACd,CAjEEmoB,GAAsBoC,SAAW,GAuEnC,IAAMJ,GAAiB,CACrBxpB,GAAI,SACJ+W,OAAQ,CAAC,YAAa,iBAAkB,UAAW,WACnD9W,QAAO,SAACC,GACNA,EAAMwnB,YAAYpnB,UAAUspB,SAAWA,EACxC,EACDlpB,UAAW,CACT,mBAAoB,SAAAmV,GAAcA,EAAX/W,YACT0qB,OAAS,CACnBK,MAAO,CAAEpqB,EAAG,EAAGJ,EAAG,GAClBoqB,QAAS,CAAEhqB,EAAG,EAAGJ,EAAG,GAEvB,EACD,8BAA+B,SAAAka,GAAc,OAlF1C,SAAkBza,GAClBA,EAAYmC,gBAIjB0oB,GAAY7qB,EAAYI,OAAOC,IAAKL,EAAY0qB,OAAOK,OAEvD/qB,EAAY0qB,OAAOC,QAAQhqB,EAAI,EAC/BX,EAAY0qB,OAAOC,QAAQpqB,EAAI,EACjC,CAyEwDyqB,CAAPvQ,EAAXza,YAAuC,EACzE,mCAAoCwqB,GACpC,kCAAmCA,GACnC,iCAtEJ,SAAkB3pB,GAAgE,IAA7Db,EAAWa,EAAXb,YAGnB,GAFmByqB,GAAazqB,GAOhC,OAHAA,EAAYY,KAAK,CAAE8pB,QAAQ,IAC3B1qB,EAAY+pB,OAEL,CACT,EA8DI,oBA5DJ,SAAY5V,GAAgD,IAA7CnU,EAAWmU,EAAXnU,YACbA,EAAY0qB,OAAOK,MAAMpqB,EAAI,EAC7BX,EAAY0qB,OAAOK,MAAMxqB,EAAI,EAC7BP,EAAY0qB,OAAOC,QAAQhqB,EAAI,EAC/BX,EAAY0qB,OAAOC,QAAQpqB,EAAI,CACjC,IA2DA0qB,GAAeP,GCxCf,IAAaQ,GAAY,WAwBvB,SAAAA,EAAYlrB,GAA0BmO,OAAA+c,GAAAzoB,KAvBtC0oB,QAAS,EAAK1oB,KACd2oB,YAAa,EAAK3oB,KAClB4oB,WAAY,EAAK5oB,KACjB6oB,aAAc,EAAK7oB,KAEnBwjB,kBAAY,EAAAxjB,KACZ8oB,cAAgB,EAAC9oB,KACjBojB,iBAAW,EAAApjB,KAEX0iB,iBAAW,EAAA1iB,KACXmlB,GAAK,EAACnlB,KACN+oB,GAAK,EAAC/oB,KAENgpB,GAAK,EAAChpB,KACNipB,kBAAY,EAAAjpB,KACZkpB,oBAAc,EAAAlpB,KACdmpB,mBAAa,EAAAnpB,KAEbopB,UAAa,EAAEppB,KACfqpB,UAAa,EAAErpB,KACfspB,aAAO,EAAAtpB,KACEzC,iBAAW,EAGlByC,KAAKzC,YAAcA,CACrB,CAsOC,OAtOAuO,EAAA2c,EAAA,CAAA,CAAA1c,IAAA,QAAA3G,MAED,SAAMsE,GACJ,IAAQnM,EAAgByC,KAAhBzC,YACFiC,EAAU+pB,GAAWhsB,GAE3B,IAAKiC,IAAYA,EAAQC,QACvB,OAAO,EAGT,IAAgB+pB,EAAmBjsB,EAAYI,OAAOM,SAA9CD,OACFyrB,EAAehiB,EAAM+hB,EAAetrB,EAAGsrB,EAAe1rB,GACtD0lB,EAAexjB,KAAKwjB,eAAiBxjB,KAAKwjB,aAAe,IAAInD,GAAa9iB,IAqBhF,GAnBAimB,EAAakG,SAASnsB,EAAYimB,cAElCxjB,KAAKmlB,GAAK5nB,EAAYmqB,OACtB1nB,KAAK6oB,YAAcrpB,EAAQqpB,YAC3B7oB,KAAK+oB,GAAKU,EACVzpB,KAAKmpB,cAAgB,CAAEjrB,EAAG,EAAGJ,EAAG,GAChCkC,KAAK0iB,YAAcnlB,EAAYI,OAAOC,IAAIC,KAE1CmC,KAAKojB,YAAcI,EAAanC,QAAQ,CACtCT,WAAY5gB,KAAK0iB,YACjBpB,QAAQ,EACR7M,MAAO,iBAIPzU,KAAKmlB,GAAK5nB,EAAYI,OAAOC,IAAI6K,UAAY,IAC7CghB,EAAejqB,EAAQmqB,UACvBF,EAAejqB,EAAQoqB,SAGvB5pB,KAAK6pB,mBACA,CAGL,GAFArG,EAAa1c,OAAS0c,EAAahC,OAAOxhB,KAAKojB,cAE1CI,EAAa1c,OAAO0b,QACvB,OAAO,EAGTxiB,KAAK8pB,gBACP,CAmBA,OAhBAvsB,EAAYimB,aAAa1c,OAAOZ,KAAO,KAGvC3I,EAAY8qB,SAASroB,KAAKipB,cAC1B1rB,EAAY0pB,SAAS,CACnB1pB,YAAAA,EACAmM,MAAAA,EACA+K,MAAO,iBAETlX,EAAY8qB,SAAS,CAAEnqB,GAAI8B,KAAKipB,aAAa/qB,EAAGJ,GAAIkC,KAAKipB,aAAanrB,IAEtEP,EAAYimB,aAAa1c,OAAOZ,KAAO,KAEvClG,KAAK0oB,QAAS,EACdnrB,EAAY4c,WAAana,MAElB,CACT,GAAC,CAAA+L,IAAA,eAAA3G,MAED,WAAe,IAAA0H,EAAA9M,KACP+pB,EAAgB/pB,KAAKzC,YAAYI,OAAOM,SAASD,OACjDwB,EAAU+pB,GAAWvpB,KAAKzC,aAC1BysB,EAASxqB,EAAQyqB,WACjBC,GAAcxiB,KAAKyiB,IAAI3qB,EAAQoqB,SAAW5pB,KAAK+oB,IAAMiB,EAE3DhqB,KAAKipB,aAAe,CAClB/qB,GAAI6rB,EAAc7rB,EAAIgsB,GAAcF,EACpClsB,GAAIisB,EAAcjsB,EAAIosB,GAAcF,GAGtChqB,KAAKgpB,GAAKkB,EACVlqB,KAAKopB,UAAYY,EAAShqB,KAAK+oB,GAC/B/oB,KAAKqpB,UAAY,EAAI7pB,EAAQoqB,SAAW5pB,KAAK+oB,GAE7C,IAAQvF,EAA8BxjB,KAA9BwjB,aAAcJ,EAAgBpjB,KAAhBojB,YAEtBA,EAAYxC,WAAa,CACvB1iB,EAAG8B,KAAK0iB,YAAYxkB,EAAI8B,KAAKipB,aAAa/qB,EAC1CJ,EAAGkC,KAAK0iB,YAAY5kB,EAAIkC,KAAKipB,aAAanrB,GAG5C0lB,EAAa1c,OAAS0c,EAAahC,OAAO4B,GAEtCI,EAAa1c,OAAO0b,UACtBxiB,KAAK2oB,YAAa,EAClB3oB,KAAKkpB,eAAiB,CACpBhrB,EAAG8B,KAAKipB,aAAa/qB,EAAIslB,EAAa1c,OAAOvI,MAAML,EACnDJ,EAAGkC,KAAKipB,aAAanrB,EAAI0lB,EAAa1c,OAAOvI,MAAMT,IAIvDkC,KAAKoqB,aAAY,WAAA,OAAMtd,EAAKud,gBAC9B,GAAC,CAAAte,IAAA,iBAAA3G,MAED,WAAiB,IAAA+H,EAAAnN,KACfA,KAAK4oB,WAAY,EACjB5oB,KAAK2oB,YAAa,EAClB3oB,KAAKipB,aAAe,CAClB/qB,EAAG8B,KAAKwjB,aAAa1c,OAAOvI,MAAML,EAClCJ,EAAGkC,KAAKwjB,aAAa1c,OAAOvI,MAAMT,GAGpCkC,KAAKoqB,aAAY,WAAA,OAAMjd,EAAKmd,kBAC9B,GAAC,CAAAve,IAAA,cAAA3G,MAED,SAAYmlB,GAAoB,IAAAC,EAAAxqB,KAC9BA,KAAKspB,QAAUnR,GAAI5B,SAAQ,WACrBiU,EAAK9B,QACP6B,GAEJ,GACF,GAAC,CAAAxe,IAAA,cAAA3G,MAED,WAAc,IAoMdqlB,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EA1McC,EAAAhrB,KACJzC,EAAgByC,KAAhBzC,YAEFysB,EADUT,GAAWhsB,GACJ0sB,WACjBhjB,GAAK1J,EAAYmqB,OAAS1nB,KAAKmlB,IAAM,IAE3C,GAAIle,EAAIjH,KAAKgpB,GAAI,CACf,IACIiC,EADEC,EAAW,GAAKxjB,KAAKyjB,KAAKnB,EAAS/iB,GAAKjH,KAAKopB,WAAappB,KAAKqpB,UAGjErpB,KAAK2oB,YA0Lb8B,EAxLQ,EAyLRC,EAxLQ,EAyLRC,EAxLQ3qB,KAAKipB,aAAa/qB,EAyL1B0sB,EAxLQ5qB,KAAKipB,aAAanrB,EAyL1B+sB,EAxLQ7qB,KAAKkpB,eAAehrB,EAyL5B4sB,EAxLQ9qB,KAAKkpB,eAAeprB,EANtBmtB,EAiMC,CACL/sB,EAAGktB,GAHLL,EAxLQG,EA2LwBT,EAAQE,EAAKE,GAC3C/sB,EAAGstB,GAAiBL,EAAUL,EAAQE,EAAKE,KAzLvCG,EAAY,CACV/sB,EAAG8B,KAAKipB,aAAa/qB,EAAIgtB,EACzBptB,EAAGkC,KAAKipB,aAAanrB,EAAIotB,GAI7B,IAAM3sB,EAAQ,CAAEL,EAAG+sB,EAAU/sB,EAAI8B,KAAKmpB,cAAcjrB,EAAGJ,EAAGmtB,EAAUntB,EAAIkC,KAAKmpB,cAAcrrB,GAE3FkC,KAAKmpB,cAAcjrB,GAAKK,EAAML,EAC9B8B,KAAKmpB,cAAcrrB,GAAKS,EAAMT,EAE9BP,EAAY8qB,SAAS9pB,GACrBhB,EAAYY,OAEZ6B,KAAKoqB,aAAY,WAAA,OAAMY,EAAKX,gBAC9B,MACE9sB,EAAY8qB,SAAS,CACnBnqB,EAAG8B,KAAKkpB,eAAehrB,EAAI8B,KAAKmpB,cAAcjrB,EAC9CJ,EAAGkC,KAAKkpB,eAAeprB,EAAIkC,KAAKmpB,cAAcrrB,IAGhDkC,KAAKsnB,KAET,GAAC,CAAAvb,IAAA,gBAAA3G,MAED,WAAgB,IAAAimB,EAAArrB,KACNzC,EAAgByC,KAAhBzC,YACF0J,EAAI1J,EAAYmqB,OAAS1nB,KAAKmlB,GACTC,EAAamE,GAAWhsB,GAA3C+tB,kBAER,GAAIrkB,EAAIme,EAAU,CAChB,IAAM6F,EAAY,CAChB/sB,EAAGqtB,GAAYtkB,EAAG,EAAGjH,KAAKipB,aAAa/qB,EAAGknB,GAC1CtnB,EAAGytB,GAAYtkB,EAAG,EAAGjH,KAAKipB,aAAanrB,EAAGsnB,IAEtC7mB,EAAQ,CACZL,EAAG+sB,EAAU/sB,EAAI8B,KAAKmpB,cAAcjrB,EACpCJ,EAAGmtB,EAAUntB,EAAIkC,KAAKmpB,cAAcrrB,GAGtCkC,KAAKmpB,cAAcjrB,GAAKK,EAAML,EAC9B8B,KAAKmpB,cAAcrrB,GAAKS,EAAMT,EAE9BP,EAAY8qB,SAAS9pB,GACrBhB,EAAYY,KAAK,CAAEwjB,cAAe3hB,KAAK8oB,gBAEvC9oB,KAAKoqB,aAAY,WAAA,OAAMiB,EAAKf,kBAC9B,MACE/sB,EAAY8qB,SAAS,CACnBnqB,EAAG8B,KAAKipB,aAAa/qB,EAAI8B,KAAKmpB,cAAcjrB,EAC5CJ,EAAGkC,KAAKipB,aAAanrB,EAAIkC,KAAKmpB,cAAcrrB,IAG9CkC,KAAKsnB,KAET,GAAC,CAAAvb,IAAA,SAAA3G,MAED,SAAA9H,GAAyE,IAAhEuL,EAAOvL,EAAPuL,QAASa,EAAKpM,EAALoM,MAAOmO,EAAWva,EAAXua,YACfta,EAAgByC,KAAhBzC,YAGRA,EAAY8qB,SAAS,CACnBnqB,GAAI8B,KAAKmpB,cAAcjrB,EACvBJ,GAAIkC,KAAKmpB,cAAcrrB,IAIzBP,EAAYwpB,cAAcle,EAASa,EAAOmO,GAAa,GAGvDta,EAAY0pB,SAAS,CACnB1pB,YAAAA,EACAmM,MAAAA,EACA+K,MAAO,WAETlM,GAAWhL,EAAYI,OAAOuP,KAAM3P,EAAYI,OAAOC,KAEvDoC,KAAKuZ,MACP,GAAC,CAAAxN,IAAA,MAAA3G,MAED,WACEpF,KAAKzC,YAAYY,OACjB6B,KAAKzC,YAAY+pB,MACjBtnB,KAAKuZ,MACP,GAAC,CAAAxN,IAAA,OAAA3G,MAED,WACEpF,KAAK0oB,OAAS1oB,KAAK4oB,WAAY,EAC/B5oB,KAAKzC,YAAY4c,WAAa,KAC9BhC,GAAI3V,OAAOxC,KAAKspB,QAClB,KAACb,CAAA,CAhQsB,GA4SzB,SAASc,GAAUpV,GAA0C,IAAvC9U,EAAY8U,EAAZ9U,aAAc7B,EAAQ2W,EAAR3W,SAClC,OAAO6B,GAAgBA,EAAaG,SAAWhC,EAASC,MAAQ4B,EAAaG,QAAQhC,EAASC,MAAM+tB,OACtG,CAEA,IAAMA,GAAkB,CACtB/sB,GAAI,UACJ+W,OAAQ,CAAC,YAAa,WACtB9W,QArUF,SAAiBC,GACf,IAAQG,EAAaH,EAAbG,SAERH,EAAMoT,UAAUkW,IAChBtpB,EAAMoT,UAAUgP,IAChBpiB,EAAMC,QAAQuhB,OAAOsL,cAAe,EACpC9sB,EAAMC,QAAQuhB,OAAOuL,QAAS,EAE9B5sB,EAAS2b,UAAU+Q,QAAU,CAC3B/rB,SAAS,EACTwqB,WAAY,GACZN,SAAU,IACVC,SAAU,GACVf,aAAa,EACbyC,kBAAmB,IAEvB,EAsTEnsB,UAAW,CACT,mBAAoB,SAAAiV,GAAqB,IAAlB7W,EAAW6W,EAAX7W,YACrBA,EAAYiuB,QAAU,IAAI/C,GAAalrB,EACxC,EAED,iCAtDJ,SAAca,GAAwD,IAArDb,EAAWa,EAAXb,YAAamM,EAAKtL,EAALsL,MAC5B,QAAKnM,EAAYsgB,cAAgBtgB,EAAY4c,aAI7B5c,EAAYiuB,QAAQztB,MAAM2L,KAHjC,IAOX,EA8CI,oBA1CJ,SAAgBtK,GACd,IAAQ7B,EAA6B6B,EAA7B7B,YAAasa,EAAgBzY,EAAhByY,YACf6J,EAAQnkB,EAAYiuB,QAE1B,GAAK9J,EAAMgH,OAKX,IAHA,IAAIhsB,EAAUmb,EAGPzb,EAAGM,QAAQA,IAAU,CAE1B,GAAIA,IAAYa,EAAYb,QAAS,CACnCglB,EAAMgK,OAAOtsB,GACb,KACF,CAEA1C,EAAUoM,EAAepM,EAC3B,CACF,EAyBI,oBAvBJ,SAAagV,GAAgD,IACrDgQ,EADmBhQ,EAAXnU,YACYiuB,QAEtB9J,EAAMgH,QACRhH,EAAMnI,MAEV,EAmBI,oCAAqC,SAACna,GACpC,IAAQokB,EAAiBpkB,EAAI7B,YAArBimB,aAERA,EAAajK,KAAKna,GAClBokB,EAAazlB,MAAMqB,EAAKA,EAAI7B,YAAYI,OAAOC,IAAIC,MACnD2lB,EAAaR,mBAAmB5jB,EACjC,EAED,0CAA2C,SAACA,GAAG,OAAKA,EAAI7B,YAAYimB,aAAakB,YAAYtlB,EAAI,EACjG,6BAA8B2hB,GAC9B,mCAAoCA,GACpC,yCAA0C,SAAC3hB,GAAG,OAC5CA,EAAI7B,YAAYimB,aAAamB,yBAAyBvlB,EAAI,EAC5D,mCAAoC,SAACA,GAAG,OAAKA,EAAI7B,YAAYimB,aAAamB,yBAAyBvlB,EAAI,IAK3G,SAASgsB,GAAiBnkB,EAAW0kB,EAAYC,EAAYC,GAC3D,IAAMC,EAAK,EAAI7kB,EACf,OAAO6kB,EAAKA,EAAKH,EAAK,EAAIG,EAAK7kB,EAAI2kB,EAAK3kB,EAAIA,EAAI4kB,CAClD,CAkBA,SAASN,GAAYtkB,EAAW8kB,EAAWC,EAAWC,GAEpD,OAAQD,GADR/kB,GAAKglB,IACYhlB,EAAI,GAAK8kB,CAC5B,CAEA,IAAAG,GAAeV,GC1af,SAASW,GAA0BziB,EAAYvK,GAAuB,IAAA+H,IAAAA,IAAAA,EAC7C/H,EAAShC,OAAA+J,IAAE,CAA7B,IAAM6Y,EAAY5gB,EAAS+H,GAC9B,GAAIwC,EAAMiC,4BACR,MAGFoU,EAASrW,EACX,CACF,CAEA,IAAa0iB,GAAS,WAOpB,SAAAA,EAAY5sB,GAAoCkM,OAAA0gB,GAAApsB,KANhDR,aAAO,EAAAQ,KACPqsB,MAA6B,GAAErsB,KAC/B4L,oBAAqB,EAAK5L,KAC1B2L,6BAA8B,EAAK3L,KACnCsY,YAAM,EAGJtY,KAAKR,QAAU8F,EAAO,CAAE,EAAE9F,GAAW,CAAE,EACzC,CAgDC,OAhDAsM,EAAAsgB,EAAA,CAAA,CAAArgB,IAAA,OAAA3G,MAED,SAA+DsE,GAC7D,IAAIvK,EACEmZ,EAAStY,KAAKsY,QAIfnZ,EAAYa,KAAKqsB,MAAM3iB,EAAMrJ,QAChC8rB,GAA0BziB,EAAOvK,IAI9BuK,EAAMkC,oBAAsB0M,IAAWnZ,EAAYmZ,EAAO5O,EAAMrJ,QACnE8rB,GAA0BziB,EAAOvK,EAErC,GAAC,CAAA4M,IAAA,KAAA3G,MAED,SAAG/E,EAAc0f,GACf,IAAM5gB,EAAYuH,EAAUrG,EAAM0f,GAElC,IAAK1f,KAAQlB,EACXa,KAAKqsB,MAAMhsB,GAAQqN,GAAU1N,KAAKqsB,MAAMhsB,IAAS,GAAIlB,EAAUkB,GAEnE,GAAC,CAAA0L,IAAA,MAAA3G,MAED,SAAI/E,EAAc0f,GAChB,IAAM5gB,EAAYuH,EAAUrG,EAAM0f,GAElC,IAAK1f,KAAQlB,EAAW,CACtB,IAAMmtB,EAAYtsB,KAAKqsB,MAAMhsB,GAE7B,GAAKisB,GAAcA,EAAUnvB,OAE5B,IAAA,IAAAwR,EAAA4d,EAAAA,EAEyBptB,EAAUkB,GAAKsO,EAAA4d,EAAApvB,OAAAwR,IAAE,CAAtC,IAAM6d,EAAWD,EAAA5d,GACdlB,EAAQ6e,EAAUrkB,QAAQukB,IAEjB,IAAX/e,GACF6e,EAAUlvB,OAAOqQ,EAAO,EAE5B,CACF,CACF,GAAC,CAAA1B,IAAA,UAAA3G,MAED,SAAQqnB,GACN,OAAO,IACT,KAACL,CAAA,CAzDmB,GCmRrB,IAEKM,GAAS,WAKb,SAAAA,EAAYC,GAAsBjhB,OAAAghB,GAAA1sB,KAJlCwL,mBAAa,EAAAxL,KACb2sB,mBAAa,EAAA3sB,KACbK,UAAI,EAGFL,KAAK2sB,cAAgBA,EAErBC,GAAQ5sB,KAAM2sB,EAChB,CAYC,OAZA7gB,EAAA4gB,EAAA,CAAA,CAAA3gB,IAAA,yBAAA3G,MAED,WACEpF,KAAK2sB,cAAcvN,gBACrB,GAAC,CAAArT,IAAA,kBAAA3G,MAED,WACEpF,KAAK2sB,cAAcE,iBACrB,GAAC,CAAA9gB,IAAA,2BAAA3G,MAED,WACEpF,KAAK2sB,cAAcpf,0BACrB,KAACmf,CAAA,CArBY,GAwBf,SAASnD,GAAWuD,GAClB,OAAK1wB,EAAGH,OAAO6wB,GAIR,CACLC,UAAWD,EAAMC,QACjBlN,UAAWiN,EAAMjN,SALV,CAAEkN,UAAWD,EAAOjN,SAAS,EAOxC,CAEA,SAASmN,GAAaC,EAAoClB,GACxD,OAAIkB,IAAMlB,IAEO,kBAANkB,IAA0BlB,EAAEgB,UAAYE,IAAqB,KAAdlB,EAAElM,UAEnDoN,EAAEF,WAAchB,EAAEgB,WAAaE,EAAEpN,WAAckM,EAAElM,QAC5D,CAEe,IAAAxS,GAAA,CACb5O,GAAI,SACJC,QAzTF,SAAiBC,GAAc,IAAAuuB,EACvBC,EAGD,GAECC,EAMF,CAAA,EACEC,EAAwB,GAExBC,EAAgB,CACpBC,IAAAA,EACAC,OAAAA,EAEAC,YA2HF,SACExqB,EACAyqB,EACArtB,EACA0f,EACA4N,GAEA,IAAMnuB,EAAU+pB,GAAWoE,GAC3B,IAAKP,EAAgB/sB,GAAO,CAC1B+sB,EAAgB/sB,GAAQ,GAExB,IAAAutB,IAAAA,IAAAA,EACkBP,EAASlwB,OAAAywB,IAAE,CAAxB,IAAMlO,EAAO2N,EAASO,GACzBL,EAAI7N,EAAKrf,EAAMwtB,GACfN,EAAI7N,EAAKrf,EAAMytB,GAAoB,EACrC,CACF,CAEA,IAAMC,EAAYX,EAAgB/sB,GAC9B2tB,EAAWtgB,GAASqgB,GAAW,SAAC9B,GAAC,OAAKA,EAAEhpB,WAAaA,GAAYgpB,EAAEyB,UAAYA,KAE9EM,IACHA,EAAW,CAAE/qB,SAAAA,EAAUyqB,QAAAA,EAASvuB,UAAW,IAC3C4uB,EAAU7oB,KAAK8oB,IAGjBA,EAAS7uB,UAAU+F,KAAK,CAAE/I,KAAM4jB,EAAUvgB,QAAAA,GAC5C,EArJEyuB,eAuJF,SACEhrB,EACAyqB,EACArtB,EACA0f,EACA4N,GAEA,IAGIlgB,EAHEjO,EAAU+pB,GAAWoE,GACrBI,EAAYX,EAAgB/sB,GAC9B6tB,GAAa,EAGjB,IAAKH,EAAW,OAGhB,IAAKtgB,EAAQsgB,EAAU5wB,OAAS,EAAGsQ,GAAS,EAAGA,IAAS,CACtD,IAAM7P,EAAMmwB,EAAUtgB,GAEtB,GAAI7P,EAAIqF,WAAaA,GAAYrF,EAAI8vB,UAAYA,EAAS,CAIxD,IAHA,IAAQvuB,EAAcvB,EAAduB,UAGCkN,EAAIlN,EAAUhC,OAAS,EAAGkP,GAAK,EAAGA,IAAK,CAC9C,IAAM8hB,EAAQhvB,EAAUkN,GAGxB,GAAI8hB,EAAMhyB,OAAS4jB,GAAYiN,GAAamB,EAAM3uB,QAASA,GAAU,CAEnEL,EAAU/B,OAAOiP,EAAG,GAIflN,EAAUhC,SACb4wB,EAAU3wB,OAAOqQ,EAAO,GAGxB+f,EAAOE,EAASrtB,EAAMwtB,GACtBL,EAAOE,EAASrtB,EAAMytB,GAAoB,IAI5CI,GAAa,EACb,KACF,CACF,CAEA,GAAIA,EACF,KAEJ,CACF,CACF,EAxMEL,iBAAAA,EACAC,mBAAAA,EACAV,gBAAAA,EACAC,UAAAA,EAEAF,QAAAA,EAEAiB,iBAAiB,EACjB3O,iBAAiB,GAenB,SAAS8N,EACP1V,EACAxX,EACA0f,EACA4N,GAEA,GAAK9V,EAAYwW,iBAAjB,CAEA,IAAM7uB,EAAU+pB,GAAWoE,GACvBhoB,EAAS+H,GAASyf,GAAS,SAAClmB,GAAC,OAAKA,EAAE4Q,cAAgBA,KAEnDlS,IACHA,EAAS,CACPkS,YAAAA,EACAxK,OAAQ,CAAC,GAGX8f,EAAQjoB,KAAKS,IAGVA,EAAO0H,OAAOhN,KACjBsF,EAAO0H,OAAOhN,GAAQ,IAGnBqN,GAAS/H,EAAO0H,OAAOhN,IAAO,SAAC+G,GAAC,OAAKA,EAAEjL,OAAS4jB,GAAYiN,GAAa5lB,EAAE5H,QAASA,EAAQ,MAC/FqY,EAAYwW,iBACVhuB,EACA0f,EACAuN,EAAcc,gBAAkB5uB,EAAUA,EAAQutB,SAEpDpnB,EAAO0H,OAAOhN,GAAM6E,KAAK,CAAE/I,KAAM4jB,EAAUvgB,QAAAA,IAxBV,CA0BrC,CAEA,SAASguB,EACP3V,EACAxX,EACA0f,EACA4N,GAEA,GAAK9V,EAAYwW,kBAAqBxW,EAAYyW,oBAAlD,CAEA,IAAMC,EAAc7gB,GAAcyf,GAAS,SAAClmB,GAAC,OAAKA,EAAE4Q,cAAgBA,KAC9DlS,EAASwnB,EAAQoB,GAEvB,GAAK5oB,GAAWA,EAAO0H,OAIvB,GAAa,QAAThN,EAAJ,CASA,IAAImuB,GAAc,EACZC,EAAgB9oB,EAAO0H,OAAOhN,GAEpC,GAAIouB,EAAe,CACjB,GAAiB,QAAb1O,EAAoB,CACtB,IAAK,IAAI1T,EAAIoiB,EAActxB,OAAS,EAAGkP,GAAK,EAAGA,IAAK,CAClD,IAAM8hB,EAAQM,EAAcpiB,GAC5BmhB,EAAO3V,EAAaxX,EAAM8tB,EAAMhyB,KAAMgyB,EAAM3uB,QAC9C,CACA,MACF,CAGE,IAFA,IAAMA,EAAU+pB,GAAWoE,GAElBthB,EAAI,EAAGA,EAAIoiB,EAActxB,OAAQkP,IAAK,CAC7C,IAAM8hB,EAAQM,EAAcpiB,GAC5B,GAAI8hB,EAAMhyB,OAAS4jB,GAAYiN,GAAamB,EAAM3uB,QAASA,GAAU,CACnEqY,EAAYyW,oBACVjuB,EACA0f,EACAuN,EAAcc,gBAAkB5uB,EAAUA,EAAQutB,SAEpD0B,EAAcrxB,OAAOiP,EAAG,GAEK,IAAzBoiB,EAActxB,gBACTwI,EAAO0H,OAAOhN,GACrBmuB,GAAc,GAGhB,KACF,CACF,CAEJ,CAEIA,IAAgBtmB,OAAOiK,KAAKxM,EAAO0H,QAAQlQ,QAC7CgwB,EAAQ/vB,OAAOmxB,EAAa,EArC9B,MANE,IAAKluB,KAAQsF,EAAO0H,OACd1H,EAAO0H,OAAOqhB,eAAeruB,IAC/BmtB,EAAO3V,EAAaxX,EAAM,MAZuC,CAuDzE,CAsFA,SAASwtB,EAAiBnkB,EAA0BikB,GAQlD,IAPA,IAAMnuB,EAAU+pB,GAAWoE,GACrBgB,EAAY,IAAIjC,GAAUhjB,GAC1BqkB,EAAYX,EAAgB1jB,EAAMrJ,MACjCwX,EAAexE,GAA6B3J,GAAjC,GACdhN,EAAgBmb,EAGbzb,EAAGM,QAAQA,IAAU,CAC1B,IAAK,IAAI2P,EAAI,EAAGA,EAAI0hB,EAAU5wB,OAAQkP,IAAK,CACzC,IAAMzO,EAAMmwB,EAAU1hB,GACdpJ,EAAsBrF,EAAtBqF,SAAUyqB,EAAY9vB,EAAZ8vB,QAElB,GACEniB,EAAyB7O,EAASuG,IAClCsI,EAAsBmiB,EAAS7V,IAC/BtM,EAAsBmiB,EAAShxB,GAC/B,CACA,IAAQyC,EAAcvB,EAAduB,UAERwvB,EAAUnjB,cAAgB9O,EAAO,IAAAkyB,IAAAA,IAAAA,EAEbzvB,EAAShC,OAAAyxB,IAAE,CAA1B,IAAMT,EAAShvB,EAASyvB,GACvB5B,GAAamB,EAAM3uB,QAASA,IAC9B2uB,EAAMhyB,KAAKwyB,EAEf,CACF,CACF,CAEAjyB,EAAU6O,EAAoB7O,EAChC,CACF,CAEA,SAASoxB,EAAkCpkB,GACzC,OAAOmkB,EAA4BnkB,GAAO,EAC5C,CAGA,OAvOc,OAAdwjB,EAAAvuB,EAAMjD,WAANwxB,EAAgB2B,cAAc,OAAOR,iBAAiB,OAAQ,KAAM,CAClE,WAAItB,GACF,OAAQO,EAAcc,iBAAkB,CACzC,EACD,WAAIvO,GACF,OAAQyN,EAAc7N,iBAAkB,CAC1C,IAGF9gB,EAAM0O,OAASigB,EA8NRA,CACT,GCpRMwB,GAAS,CACbC,YAAa,CAAC,mBAAoB,aAAc,aAAc,QAE9DzuB,OAAM,SAAC0uB,GAAwB,IAAA,IAAA9nB,EAAA,EAAA+nB,EACRH,GAAOC,YAAW7nB,EAAA+nB,EAAA9xB,OAAA+J,IAAE,CAApC,IAAM0T,EAAMqU,EAAA/nB,GACT3J,EAAcuxB,GAAOlU,GAAQoU,GAEnC,GAAIzxB,EACF,OAAOA,CAEX,CAEA,OAAO,IACR,EAGD2xB,iBAAgB,SAAA5xB,GAAgE,IAA7DqC,EAAWrC,EAAXqC,YAAaqgB,EAAS1iB,EAAT0iB,UAAWnI,EAAWva,EAAXua,YAAalZ,EAAKrB,EAALqB,MACtD,IAAK,cAAc/B,KAAKojB,GACtB,OAAO,KACR,IAAA,IAAArR,EAAAyO,EAAAA,EAEyBze,EAAM0e,aAAa7O,KAAIG,EAAAyO,EAAAjgB,OAAAwR,IAAE,CAA9C,IAAMpR,EAAW6f,EAAAzO,GAChBjS,EAAUmb,EAEd,GACEta,EAAY4c,YACZ5c,EAAY4c,WAAW0O,aACvBtrB,EAAYoC,cAAgBA,EAE5B,KAAOjD,GAAS,CAEd,GAAIA,IAAYa,EAAYb,QAC1B,OAAOa,EAETb,EAAUoM,EAAepM,EAC3B,CAEJ,CAEA,OAAO,IACR,EAGDyyB,WAAU,SAAA/wB,GAA8D,IAKlEgxB,EALOjmB,EAAS/K,EAAT+K,UAAWxJ,EAAWvB,EAAXuB,YAAaqgB,EAAS5hB,EAAT4hB,UAAWrhB,EAAKP,EAALO,MAC9C,GAAoB,UAAhBgB,GAA2C,QAAhBA,EAC7B,OAAO,KAGS,IAAA,IAAAmO,EAAAuhB,EAAAA,EAEQ1wB,EAAM0e,aAAa7O,KAAIV,EAAAuhB,EAAAlyB,OAAA2Q,IAAE,CAA9C,IAAMvQ,EAAW8xB,EAAAvhB,GACpB,GAAIvQ,EAAYoC,cAAgBA,EAAa,CAE3C,GAAIpC,EAAY4c,aAAemV,GAAa/xB,EAAa4L,GACvD,SAIF,GAAI5L,EAAY2c,cACd,OAAO3c,EAGC6xB,IACRA,EAAiB7xB,EAErB,CACD,CAID,GAAI6xB,EACF,OAAOA,EAKT,IAAA,IAAApgB,EAAAugB,EAAAA,EAC0B5wB,EAAM0e,aAAa7O,KAAIQ,EAAAugB,EAAApyB,OAAA6R,IAAE,CAA9C,IAAMzR,EAAWgyB,EAAAvgB,GACpB,KAAIzR,EAAYoC,cAAgBA,GAAiB,QAAQ/C,KAAKojB,IAAcziB,EAAY4c,YACtF,OAAO5c,CAEX,CAEA,OAAO,IACR,EAGDiyB,WAAU,SAAA9d,GAAmB,IAAmB,IAAnCvI,EAASuI,EAATvI,UAAgBkG,EAAAogB,EAAAA,EAAA/d,EAAL/S,MACU0e,aAAa7O,KAAIa,EAAAogB,EAAAtyB,OAAAkS,IAAE,CAA9C,IAAM9R,EAAWkyB,EAAApgB,GACpB,GAAIigB,GAAa/xB,EAAa4L,GAC5B,OAAO5L,CAEX,CAEA,OAAO,IACR,EAGDmyB,KAAI,SAAAvb,GAAqB,IAAmB,IAArCxU,EAAWwU,EAAXxU,YAAkB+jB,EAAAiM,EAAAA,EAAAxb,EAALxV,MACc0e,aAAa7O,KAAIkV,EAAAiM,EAAAxyB,OAAAumB,IAAE,CAA9C,IAAMnmB,EAAWoyB,EAAAjM,GAEpB,GAAoC,IAAhCnmB,EAAY+L,SAASnM,OAAc,CACrC,IAAMwI,EAASpI,EAAY8B,aAG3B,GAAIsG,KAAYA,EAAOnG,QAAQ0V,UAAWvP,EAAOnG,QAAQ0V,QAAQzV,SAC/D,QAEJ,MAEK,GAAIlC,EAAY+L,SAASnM,QAAU,EACtC,SAGF,IAAKI,EAAY2c,eAAiBva,IAAgBpC,EAAYoC,YAC5D,OAAOpC,CAEX,CAEA,OAAO,IACT,GAGF,SAAS+xB,GAAa/xB,EAA0B4L,GAC9C,OAAO5L,EAAY+L,SAAStB,MAAK,SAAAoM,GAAK,OAAAA,EAAF3V,KAAgB0K,IACtD,CAEA,IAAAymB,GAAed,GC1GTe,GAAc,CAClB,cACA,cACA,YACA,gBACA,gBACA,cAiGF,SAASC,GAAiBlV,EAAgBjc,GACxC,OAAO,SAAU+K,GACf,IAAM2T,EAAe1e,EAAM0e,aAAa7O,KAElC7O,EAAc0T,GAA4B3J,GAChDqmB,EAAsC1c,GAA6B3J,GAA5DmO,EAAWkY,EAAA,GAAE1I,EAAc0I,EAAA,GAC5BhU,EAAiB,GAEvB,GAAI,SAASnf,KAAK8M,EAAMrJ,MAAO,CAC7B1B,EAAMqxB,cAAgBrxB,EAAMma,MAE5B,IAAA,IAAAzJ,EAAA,EAAA4gB,EAC2BvmB,EAAME,eAAcyF,EAAA4gB,EAAA9yB,OAAAkS,IAAE,CAA5C,IAAM6gB,EAAYD,EAAA5gB,GAGf8gB,EAA+B,CACnCtnB,QAHcqnB,EAId/mB,UAHgBkK,GADF6c,GAKdvwB,YAAAA,EACAqgB,UAAWtW,EAAMrJ,KACjBwX,YAAAA,EACAwP,eAAAA,EACA1oB,MAAAA,GAEIpB,EAAc6yB,GAAeD,GAEnCpU,EAAQ7W,KAAK,CACXirB,EAActnB,QACdsnB,EAActY,YACdsY,EAAc9I,eACd9pB,GAEJ,CACF,KAAO,CACL,IAAI8yB,GAAiB,EAErB,IAAKjvB,EAAQI,sBAAwB,QAAQ5E,KAAK8M,EAAMrJ,MAAO,CAE7D,IAAK,IAAIgM,EAAI,EAAGA,EAAIgR,EAAalgB,SAAWkzB,EAAgBhkB,IAC1DgkB,EAAiD,UAAhChT,EAAahR,GAAG1M,aAA2B0d,EAAahR,GAAG3M,cAK9E2wB,EACEA,GACA1xB,EAAMma,MAAQna,EAAMqxB,cAAgB,KAEhB,IAApBtmB,EAAMjB,SACV,CAEA,IAAK4nB,EAAgB,CACnB,IAAMF,EAAgB,CACpBtnB,QAASa,EACTP,UAAWkK,GAA0B3J,GACrC/J,YAAAA,EACAqgB,UAAWtW,EAAMrJ,KACjBgnB,eAAAA,EACAxP,YAAAA,EACAlZ,MAAAA,GAGIpB,EAAc6yB,GAAeD,GAEnCpU,EAAQ7W,KAAK,CACXirB,EAActnB,QACdsnB,EAActY,YACdsY,EAAc9I,eACd9pB,GAEJ,CACF,CAEA,IAAAmmB,IAAAA,IAAAA,EACkE3H,EAAO5e,OAAAumB,IAAE,CAAtE,IAAA4M,EAA6DvU,EAAO2H,GAA7D7a,EAAOynB,EAAA,GAAEzY,EAAWyY,EAAA,GAAEjJ,EAAciJ,EAAA,GAAaA,EAAA,GAC/C1V,GAAQ/R,EAASa,EAAOmO,EAAawP,EACnD,EAEJ,CAEA,SAAS+I,GAAeD,GACtB,IAAQxwB,EAAuBwwB,EAAvBxwB,YAAahB,EAAUwxB,EAAVxxB,MAGf8nB,EAAY,CAAElpB,YADKuxB,GAAOxuB,OAAO6vB,GACYA,cAAAA,GAInD,OAFAxxB,EAAMiP,KAAK,oBAAqB6Y,GAEzBA,EAAUlpB,aAAeoB,EAAM0e,aAAakT,IAAI,CAAE5wB,YAAAA,GAC3D,CAEA,SAAS6wB,GAAWpyB,EAElBqyB,GACA,IAFE/Q,EAAGthB,EAAHshB,IAAK/gB,EAAKP,EAALO,MAAOa,EAAOpB,EAAPoB,QAIIsgB,EAEdnhB,EAFF0e,aAAgByC,UAChBzS,EACE1O,EADF0O,OAEIqjB,EAAcrjB,EAAOojB,GAO3B,IAAK,IAAMzQ,KALPrhB,EAAMyC,QAAQM,QAAUlC,EAAQ6N,SAClC7N,EAAQ6N,OAAS,CAAEwS,SAAS,IAINxS,EAAO+f,gBAC7BsD,EAAYhR,EAAKM,EAAW3S,EAAOwgB,kBACnC6C,EAAYhR,EAAKM,EAAW3S,EAAOygB,oBAAoB,GAGX,IAA9C,IAAM6C,EAAenxB,GAAWA,EAAQ6N,OAAMujB,IAAAA,EAEb9Q,EAAS3iB,OAAAyzB,IAAE,CAAvC,IAAAC,EAA4B/Q,EAAS8Q,GACxCF,EAAYhR,EADGmR,EAAJxwB,KAAcwwB,EAAR9Q,SACgB4Q,EACnC,CACF,CAEA,IAAMtT,GAAuB,CAC3B5e,GAAI,oBACJC,QAtNF,SAAiBC,GACY,IAA3B,IAAMQ,EAAY,CAAA,EAAS+H,IAAAA,EAEN2oB,GAAW1yB,OAAA+J,IAAE,CAA7B,IAAM0T,EAAUiV,GAAW3oB,GAC9B/H,EAAUyb,GAAUkV,GAAiBlV,EAAQjc,EAC/C,CAEA,IACImhB,EADE3d,EAAcf,EAAQe,YAmE5B,SAAS2uB,IACP,IAAA,IAAAhjB,EAAAuhB,EAAAA,EAC0B1wB,EAAM0e,aAAa7O,KAAIV,EAAAuhB,EAAAlyB,OAAA2Q,IAAE,CAA9C,IAAMvQ,EAAW8xB,EAAAvhB,GACpB,GAAKvQ,EAAYmC,eAA6C,UAA5BnC,EAAYoC,cAA2BpC,EAAYsgB,aAUpF,IAND,IAAA9V,EAAAA,WACK,IAAMc,EAAOkoB,EAAA/hB,GACXrQ,EAAM0uB,UAAUrlB,MAAK,SAAA1K,GAAM,OAAOqF,EAAPrF,EAAHoiB,IAA4B7W,EAAQkd,WAAW,KAE1ExoB,EAAYgqB,cAAc1e,EAAQA,QAASA,EAAQa,QAEtDsF,EAAA,EAAA+hB,EALqBxzB,EAAY+L,SAAQ0F,EAAA+hB,EAAA5zB,OAAA6R,IAAAjH,GAM5C,CACF,EA9EE+X,EADEtf,EAAWS,aACD,CACV,CAAEZ,KAAM8B,EAAYE,KAAM0d,SAAU+Q,GACpC,CAAEzwB,KAAM8B,EAAYE,KAAM0d,SAAU5gB,EAAU6xB,aAC9C,CAAE3wB,KAAM8B,EAAYhE,KAAM4hB,SAAU5gB,EAAU8xB,aAC9C,CAAE5wB,KAAM8B,EAAYC,GAAI2d,SAAU5gB,EAAU+xB,WAC5C,CAAE7wB,KAAM8B,EAAYK,OAAQud,SAAU5gB,EAAU+xB,YAGtC,CACV,CAAE7wB,KAAM,YAAa0f,SAAU5gB,EAAU6xB,aACzC,CAAE3wB,KAAM,YAAa0f,SAAU5gB,EAAU8xB,aACzC,CAAE5wB,KAAM,UAAW0f,SAAU5gB,EAAU+xB,WAEvC,CAAE7wB,KAAM,aAAc0f,SAAU+Q,GAChC,CAAEzwB,KAAM,aAAc0f,SAAU5gB,EAAU6xB,aAC1C,CAAE3wB,KAAM,YAAa0f,SAAU5gB,EAAU8xB,aACzC,CAAE5wB,KAAM,WAAY0f,SAAU5gB,EAAU+xB,WACxC,CAAE7wB,KAAM,cAAe0f,SAAU5gB,EAAU+xB,aAIrChsB,KAAK,CACb7E,KAAM,OACN0f,SAAQ,SAACrW,GAAO,IAAA,IAAAiF,EAAAyO,EAAAA,EACYze,EAAM0e,aAAa7O,KAAIG,EAAAyO,EAAAjgB,OAAAwR,IAAE,CAA7ByO,EAAAzO,GACRwiB,aAAaznB,EAC3B,CACF,IAIF/K,EAAMqxB,cAAgB,EAEtBrxB,EAAMwnB,YAAW,SAAAiL,GAAA3kB,EAAA4kB,EAAAD,GAAA,IAAA1kB,EAAAC,EAAA0kB,GAAA,SAAAA,IAAA,OAAA3lB,OAAA2lB,GAAA3kB,EAAA1G,MAAAhG,KAAA4G,UAAA,CAWd,OAXckF,EAAAulB,EAAA,CAAA,CAAAtlB,IAAA,uBAAA3D,IACf,WACE,OAAOzJ,EAAM0e,aAAa8J,oBAC3B,EAAA9e,IAED,SAAyBjD,GACvBzG,EAAM0e,aAAa8J,qBAAuB/hB,CAC5C,GAAC,CAAA2G,IAAA,OAAA3G,MAED,WACE,OAAOzG,EAAMma,KACf,KAACuY,CAAA,CAXc,CAAuCC,IAcxD3yB,EAAM0e,aAAe,CAEnB7O,KAAM,GACN+hB,IAAG,SAAuB/wB,GACxBA,EAAQ4mB,UAAY,SAAC3oB,EAAM2B,GAAG,OAAKT,EAAMiP,KAAKnQ,EAAM2B,EAAI,EAExD,IAAM7B,EAAc,IAAIoB,EAAMwnB,YAAe3mB,GAG7C,OADAb,EAAM0e,aAAa7O,KAAKtJ,KAAK3H,GACtBA,CACR,EACD4B,UAAAA,EACA2gB,UAAAA,EACAqH,qBAAsB,GAoBxBxoB,EAAMoT,UAAUwN,GAClB,EA2HEpgB,UAAW,CACT,qBAAsB,SAACC,GAAG,OAAKoxB,GAAYpxB,EAAK,MAAM,EACtD,wBAAyB,SAACA,GAAG,OAAKoxB,GAAYpxB,EAAK,SAAS,EAC5D,qBAAsB,SAAAsS,EAAmB/S,GAEvC,IAFiD,IAA1BU,EAAYqS,EAAZrS,aAEdgN,EAAI1N,EAAM0e,aAAa7O,KAAKrR,OAAS,EAAGkP,GAAK,EAAGA,IAAK,CAC5D,IAAM9O,EAAcoB,EAAM0e,aAAa7O,KAAKnC,GAExC9O,EAAY8B,eAAiBA,IAIjC9B,EAAYgc,OACZ5a,EAAMiP,KAAK,uBAAwB,CAAErQ,YAAAA,IACrCA,EAAYg0B,UAER5yB,EAAM0e,aAAa7O,KAAKrR,OAAS,GACnCwB,EAAM0e,aAAa7O,KAAKpR,OAAOiP,EAAG,GAEtC,CACF,GAEFmkB,YAAAA,GACAV,iBAAAA,GACAD,YAAAA,IAGF2B,GAAenU,GChQJoU,YAAAA,GAAW,OAAXA,EAAAA,EAAW,GAAA,GAAA,KAAXA,EAAAA,EAAW,IAAA,GAAA,MAAXA,CAAW,EAAXA,IAAW,CAAA,GAiBT5yB,GAAY,WAkBvB,SAAAA,EACE8G,EACAnG,EACAkyB,EACAC,GACAjmB,OAAA7M,GAAAmB,KAdO2F,YAAM,EACf3F,KAA0BR,aAAO,EACjCQ,KAA0B4xB,cAAQ,EAClC5xB,KAA0BqN,OAAS,IAAI+e,GACvCpsB,KAA0B6xB,cAAQ,EAClC7xB,KAA0B8xB,UAAI,EAC9B9xB,KAA0B+xB,UAAI,EAC9B/xB,KAA0BgyB,kBAAY,EAQpChyB,KAAK4xB,SAAWpyB,EAAQZ,QACxBoB,KAAK2F,OAASA,EACd3F,KAAK6xB,SAAWryB,EAAQkuB,SAAWgE,EACnC1xB,KAAK8xB,KAAOh2B,EAAUqJ,EAAYQ,GAAU3F,KAAK6xB,SAAWlsB,GAC5D3F,KAAK+xB,KAAO/xB,KAAK8xB,KAAKp2B,SACtBsE,KAAKgyB,aAAeL,EAEpB3xB,KAAKqI,IAAI7I,EACX,CAiaC,OAjaAsM,EAAAjN,EAAA,CAAA,CAAAkN,IAAA,YAAA3D,IA/BgB,WACf,MAAO,CACL0V,KAAM,CAAE,EACRrD,UAAW,CAAE,EACb7b,QAAS,CAAC,EAEd,GAAC,CAAAmN,IAAA,cAAA3G,MA2BD,SAAYmB,EAAwB4Z,GAclC,OAbI/jB,EAAGD,KAAKgkB,EAAO8R,UACjBjyB,KAAKwS,GAAE,GAAAjL,OAAIhB,EAAmB4Z,SAAAA,EAAO8R,SAEnC71B,EAAGD,KAAKgkB,EAAO+R,SACjBlyB,KAAKwS,GAAE,GAAAjL,OAAIhB,EAAkB4Z,QAAAA,EAAO+R,QAElC91B,EAAGD,KAAKgkB,EAAOgS,QACjBnyB,KAAKwS,GAAE,GAAAjL,OAAIhB,EAAiB4Z,OAAAA,EAAOgS,OAEjC/1B,EAAGD,KAAKgkB,EAAOiS,iBACjBpyB,KAAKwS,GAAE,GAAAjL,OAAIhB,EAA0B4Z,gBAAAA,EAAOiS,gBAGvCpyB,IACT,GAAC,CAAA+L,IAAA,2BAAA3G,MAED,SAAyBmB,EAAwB2G,EAA6BtP,GAA4B,IAAAy0B,EAAAvlB,EAAA9M,KAClGsyB,EAAeD,OAAHA,EAAIryB,KAAK4xB,SAAS3yB,IAAIsH,SAAnB8rB,EAAAA,EACjBjyB,gBACEuG,EAAS,SAACtG,GAAY,OACT,MAAhBiyB,GAAwBA,EAAajyB,KAAU4f,GAAiB5f,EAAMyM,EAAK8kB,SAAS,GAEnFx1B,EAAGc,MAAMgQ,IAAS9Q,EAAGH,OAAOiR,KAC9BlN,KAAKuyB,OAAOd,GAAYe,IAAKjsB,EAAY2G,OAAM7R,EAAWsL,IAGxDvK,EAAGc,MAAMU,IAAQxB,EAAGH,OAAO2B,KAC7BoC,KAAKuyB,OAAOd,GAAYgB,GAAIlsB,EAAY3I,OAAKvC,EAAWsL,EAE5D,GAAC,CAAAoF,IAAA,eAAA3G,MAED,SAAamB,EAAwB/G,GACnC,IAAMV,EAAWkB,KAAKskB,UAGtB,IAAK,IAAMoO,KAAelzB,EAAS,CACjC,IAAMmzB,EAAaD,EACblsB,EAAgBxG,KAAKR,QAAQ+G,GAC7BqsB,EAAmBpzB,EAAQmzB,GAGd,cAAfA,GACF3yB,KAAK6yB,yBAAyBtsB,EAAYC,EAAcrH,UAAWyzB,GAIjEx2B,EAAGc,MAAM01B,GACTpsB,EAAcmsB,GAAsBjlB,GAASklB,GAGxCx2B,EAAGW,YAAY61B,IAEpBpsB,EAAcmsB,GAAsBrtB,EACpCkB,EAAcmsB,IAAgB,GAC9BvS,GAAMwS,IAKNx2B,EAAGH,OAAO6C,EAAS2b,UAAUkY,KAC7B,YAAc7zB,EAAS2b,UAAUkY,KAE/BnsB,EAAcmsB,GAAoBlzB,SAAkC,IAAxBmzB,EAAYnzB,UAIrDrD,EAAGI,KAAKo2B,IAAgBx2B,EAAGH,OAAO6C,EAAS2b,UAAUkY,IAC1DnsB,EAAcmsB,GAAoBlzB,QAAUmzB,EAI5CpsB,EAAcmsB,GAAsBC,CAE1C,CACF,GAEA,CAAA7mB,IAAA,UAAA3G,MAOA,SAAQ1I,GAON,OANAA,EAAUA,IAAYN,EAAGM,QAAQsD,KAAK2F,QAAU3F,KAAK2F,OAAS,MAE1DvJ,EAAGK,OAAOuD,KAAK2F,UACjBjJ,EAAUA,GAAWsD,KAAK6xB,SAASxsB,cAAcrF,KAAK2F,SAGjDnB,EAAe9H,EACxB,GAEA,CAAAqP,IAAA,cAAA3G,MAUA,SAAY4N,GAAqC,IAAA7F,EAAAnN,KAC/C,OAAI5D,EAAGD,KAAK6W,IACVhT,KAAK4F,QAAU,SAAClJ,GACd,IAAMwJ,EAAOZ,EAAO,GAAI0N,EAAQhN,MAAMmH,EAAMzQ,IAO5C,MALO,UAAWwJ,IAChBA,EAAK5B,MAAQ4B,EAAK/B,MAAQ+B,EAAKhC,KAC/BgC,EAAK3B,OAAS2B,EAAK7B,OAAS6B,EAAK9B,KAG5B8B,GAGFlG,MAGO,OAAZgT,UACMhT,KAA8B4F,QAE/B5F,MAGFA,KAAK4F,OACd,GAEA,CAAAmG,IAAA,oBAAA3G,MACA,SAAkButB,EAA2B/e,GAC3C,GAAIzO,EAAYyO,IAAaxX,EAAGH,OAAO2X,GAAW,CAGhD,IAAK,IAAM/T,KAFTG,KAAKR,QAAQmzB,GAAsB/e,EAEhB5T,KAAK4xB,SAAS3yB,IAC/Be,KAAKR,QAAQK,GAAmC8yB,GAAc/e,EAGlE,OAAO5T,IACT,CAEA,OAAOA,KAAKR,QAAQmzB,EACtB,GAEA,CAAA5mB,IAAA,SAAA3G,MAUA,SAAOwO,GACL,OAAO5T,KAAKyb,kBAAkB,SAAU7H,EAC1C,GAEA,CAAA7H,IAAA,cAAA3G,MAUA,SAAYwO,GACV,MAAiB,SAAbA,GAAoC,WAAbA,GACzB5T,KAAKR,QAAQoL,YAAcgJ,EAEpB5T,MAGFA,KAAKR,QAAQoL,WACtB,GAEA,CAAAmB,IAAA,iBAAA3G,MACA,WACE,IAAQO,EAAW3F,KAAX2F,OAER,OAAIvJ,EAAGK,OAAOkJ,GACLmtB,MAAM3mB,KAAKnM,KAAK6xB,SAASkB,iBAAiBptB,IAG/CvJ,EAAGD,KAAKwJ,IAAYA,EAAekJ,eAC7BlJ,EAAekJ,iBAGlBzS,EAAGM,QAAQiJ,GAAU,CAACA,GAAU,EACzC,GAEA,CAAAoG,IAAA,UAAA3G,MAMA,WACE,OAAOpF,KAAK6xB,QACd,GAAC,CAAA9lB,IAAA,YAAA3G,MAED,SAAU1I,GACR,OAAOsD,KAAK6xB,WAAan1B,EAAQd,eAAiB+G,EAAa3C,KAAK6xB,SAAUn1B,EAChF,GAEA,CAAAqP,IAAA,kBAAA3G,MACA,SAEE5F,EACAwzB,EACAnb,GAEA,OACG7X,KAAKizB,WAAWzzB,EAAQgc,WAAYwX,EAAYnb,IACjD7X,KAAKkzB,UAAU1zB,EAAQkc,UAAWsX,EAAYnb,EAElD,GAEA,CAAA9L,IAAA,YAAA3G,MACA,SAA8BsW,EAAoCsX,EAAkBt2B,GAClF,OAAKgf,KAIAtf,EAAGM,QAAQA,KAIZN,EAAGK,OAAOif,GACL/X,EAAYjH,EAASgf,EAAWsX,KAC9B52B,EAAGM,QAAQgf,IACb/Y,EAAa+Y,EAAWhf,GAInC,GAEA,CAAAqP,IAAA,aAAA3G,MACA,SAA+BoW,EAAqCwX,EAAkBt2B,GACpF,SAAK8e,IAAepf,EAAGM,QAAQA,MAI3BN,EAAGK,OAAO+e,GACL7X,EAAYjH,EAAS8e,EAAYwX,KAC/B52B,EAAGM,QAAQ8e,IACb7Y,EAAa6Y,EAAY9e,GAIpC,GAEA,CAAAqP,IAAA,OAAA3G,MAQA,SAAiC/G,GAG/B,OAFA2B,KAAKqN,OAAOO,KAAKvP,GAEV2B,IACT,GAEA,CAAA+L,IAAA,SAAA3G,MACA,SACEwV,EACAuY,EACAC,EACA5zB,EACAmH,GAEIvK,EAAGH,OAAOk3B,KAAa/2B,EAAGc,MAAMi2B,KAClC3zB,EAAU4zB,EACVA,EAAc,MAGhB,IAAMj0B,EAAY8S,EAAmBkhB,EAASC,EAAazsB,GAE3D,IAAK,IAAItG,KAAQlB,EAAW,CACb,UAATkB,IACFA,EAAOe,EAAQqB,YAChB,IAAA,IAAAyE,EAAAmsB,EAAAA,EAEsBl0B,EAAUkB,GAAK6G,EAAAmsB,EAAAl2B,OAAA+J,IAAE,CAAnC,IAAM6Y,EAAQsT,EAAAnsB,GAEb+Y,GAAiB5f,EAAML,KAAK4xB,UAC9B5xB,KAAKqN,OAAOuN,IAAW6W,GAAYgB,GAAK,KAAO,OAAOpyB,EAAM0f,GAGrD3jB,EAAGK,OAAOuD,KAAK2F,QACtB3F,KAAKgyB,aAAapX,IAAW6W,GAAYgB,GAAK,cAAgB,kBAC5DzyB,KAAK2F,OACL3F,KAAK6xB,SACLxxB,EACA0f,EACAvgB,GAKFQ,KAAKgyB,aAAapX,IAAW6W,GAAYgB,GAAK,MAAQ,UACpDzyB,KAAK2F,OACLtF,EACA0f,EACAvgB,EAGN,CACF,CAEA,OAAOQ,IACT,GAEA,CAAA+L,IAAA,KAAA3G,MAUA,SAAGinB,EAAmBtM,EAAyBvgB,GAC7C,OAAOQ,KAAKuyB,OAAOd,GAAYgB,GAAIpG,EAAOtM,EAAUvgB,EACtD,GAEA,CAAAuM,IAAA,MAAA3G,MAUA,SAAIinB,EAAuCtM,EAAyBvgB,GAClE,OAAOQ,KAAKuyB,OAAOd,GAAYe,IAAKnG,EAAOtM,EAAUvgB,EACvD,GAEA,CAAAuM,IAAA,MAAA3G,MAMA,SAAI5F,GACF,IAAMV,EAAWkB,KAAKskB,UAQtB,IAAK,IAAMgP,KANNl3B,EAAGH,OAAOuD,KACbA,EAAU,CAAA,GAGVQ,KAAKR,QAAgC4gB,GAAMthB,EAASgf,MAE5B9d,KAAK4xB,SAAS1yB,WAAY,CAClD,IAAMqH,EAAa+sB,EACbC,EAAavzB,KAAK4xB,SAAS1yB,WAAWqH,GAE5CvG,KAAKR,QAAQ+G,GAAc,GAC3BvG,KAAKC,aAAasG,EAAYjB,EAAOA,EAAO,CAAE,EAAExG,EAAS2b,WAAY3b,EAASF,QAAQ2H,KACpFvG,KAAKuzB,GAAsC/zB,EAAQ+G,GACvD,CAEA,IAAK,IAAMiZ,KAAWhgB,EACJ,YAAZggB,EAKApjB,EAAGD,KAAM6D,KAAawf,KACtBxf,KAAawf,GAAShgB,EAAQggB,IALhCxf,KAAKwzB,YAAYh0B,EAAQoG,SAS7B,OAAO5F,IACT,GAEA,CAAA+L,IAAA,QAAA3G,MAIA,WACE,GAAIhJ,EAAGK,OAAOuD,KAAK2F,QAEjB,IAAK,IAAMtF,KAAQL,KAAKgyB,aAAa5E,gBAGnC,IAFA,IAAMqG,EAAYzzB,KAAKgyB,aAAa5E,gBAAgB/sB,GAE3CgM,EAAIonB,EAAUt2B,OAAS,EAAGkP,GAAK,EAAGA,IAAK,CAC9C,IAAAqnB,EAAyCD,EAAUpnB,GAA3CpJ,EAAQywB,EAARzwB,SAAUyqB,EAAOgG,EAAPhG,QAASvuB,EAASu0B,EAATv0B,UAEvB8D,IAAajD,KAAK2F,QAAU+nB,IAAY1tB,KAAK6xB,UAC/C4B,EAAUr2B,OAAOiP,EAAG,GAGtB,IAAK,IAAIjF,EAAIjI,EAAUhC,OAAS,EAAGiK,GAAK,EAAGA,IACzCpH,KAAKgyB,aAAa/D,eAChBjuB,KAAK2F,OACL3F,KAAK6xB,SACLxxB,EACAlB,EAAUiI,GAAG,GACbjI,EAAUiI,GAAG,GAGnB,MAGFpH,KAAKgyB,aAAaxE,OAAOxtB,KAAK2F,OAAQ,MAE1C,KAAC9G,CAAA,CAjcsB,GC5BZ80B,GAAe,WAU1B,SAAAA,EAAYh1B,GAAc,IAAAmO,EAAA9M,KAAA0L,OAAAioB,GAT1B3zB,KACAwO,KAAuB,GAAExO,KAEzB4zB,YAEI,GAAE5zB,KAENrB,WAAK,EAGHqB,KAAKrB,MAAQA,EACbA,EAAMk1B,aAAa,CACjB,qBAAsB,SAAAv2B,GAAsB,IAAnB+B,EAAY/B,EAAZ+B,aACfsG,EAAWtG,EAAXsG,OACFmuB,EAAwC13B,EAAGK,OAAOkJ,GACpDmH,EAAK8mB,YAAYjuB,GAChBA,EAAemH,EAAKnO,MAAMF,IAEzB8vB,EAAc7gB,GAAcomB,GAAuB,SAACznB,GAAC,OAAKA,IAAMhN,KACtEy0B,EAAsB12B,OAAOmxB,EAAa,EAC5C,GAEJ,CAyEC,OAzEAziB,EAAA6nB,EAAA,CAAA,CAAA5nB,IAAA,MAAA3G,MAED,SAAIO,EAAgBnG,GAClBA,EAAU8F,EAAO9F,GAAW,GAAI,CAC9BZ,QAASoB,KAAKrB,MAAMC,UAEtB,IAAMS,EAAe,IAAIW,KAAKrB,MAAME,aAAa8G,EAAQnG,EAASQ,KAAKrB,MAAMjD,SAAUsE,KAAKrB,MAAM0O,QA4BlG,OA1BArN,KAAKrB,MAAMo1B,YAAY10B,EAAa0yB,MACpC/xB,KAAKwO,KAAKtJ,KAAK7F,GAEXjD,EAAGK,OAAOkJ,IACP3F,KAAK4zB,YAAYjuB,KACpB3F,KAAK4zB,YAAYjuB,GAAU,IAE7B3F,KAAK4zB,YAAYjuB,GAAQT,KAAK7F,KAExBA,EAAasG,OAAe3F,KAAKrB,MAAMF,KAC3CyJ,OAAOC,eAAexC,EAAQ3F,KAAKrB,MAAMF,GAAI,CAC3C2G,MAAO,GACPkD,cAAc,IAIhB3C,EAAe3F,KAAKrB,MAAMF,IAAIyG,KAAK7F,IAGvCW,KAAKrB,MAAMiP,KAAK,mBAAoB,CAClCjI,OAAAA,EACAnG,QAAAA,EACAH,aAAAA,EACA/D,IAAK0E,KAAKrB,MAAMmzB,OAGXzyB,CACT,GAAC,CAAA0M,IAAA,cAAA3G,MAED,SAAYO,EAAgBnG,GAC1B,IAAMkuB,EAAWluB,GAAWA,EAAQkuB,SAAY1tB,KAAKrB,MAAMjD,SACrDs4B,EAAa53B,EAAGK,OAAOkJ,GACvBmuB,EAAwCE,EAC1Ch0B,KAAK4zB,YAAYjuB,GAChBA,EAAe3F,KAAKrB,MAAMF,IAE/B,GAAKq1B,EAEL,OAAOpmB,GACLomB,GACA,SAACz0B,GAAY,OACXA,EAAawyB,WAAanE,IAAYsG,GAAc30B,EAAa40B,UAAUtuB,GAAe,GAEhG,GAAC,CAAAoG,IAAA,eAAA3G,MAED,SAAgBrJ,EAAYqc,GAAuD,IAAA,IAAAlR,EAAA,EAAAgtB,EACtDl0B,KAAKwO,KAAItH,EAAAgtB,EAAA/2B,OAAA+J,IAAE,CAAjC,IAAM7H,EAAY60B,EAAAhtB,GACjBud,OAAM,EAcV,IAXGroB,EAAGK,OAAO4C,EAAasG,QAEpBvJ,EAAGM,QAAQX,IAASwP,EAAyBxP,EAAMsD,EAAasG,QAEhE5J,IAASsD,EAAasG,SAE1BtG,EAAa40B,UAAUl4B,KAEvB0oB,EAAMrM,EAAS/Y,SAGLhE,IAARopB,EACF,OAAOA,CAEX,CACF,KAACkP,CAAA,CAhGyB,GCkC5B,IAAaQ,GAAK,WA+ChB,SAAAA,IAAc,IAAArnB,EAAA9M,KAAA0L,OAAAyoB,GAAAn0B,KA9CdvB,GAAE,oBAAA8I,OAAuBG,KAAK0sB,MAAsB,IAAhB1sB,KAAK2sB,WAAer0B,KACxDs0B,eAAgB,EAAKt0B,KACrBu0B,aAGK,GAAEv0B,KAEPoB,QAAUA,EAAOpB,KACjBlB,SAAWshB,GAAMthB,IAASkB,KAC1BosB,UAAYA,GAASpsB,KACrBpB,QAAmB,CACjBK,IAAK,CAAE,EACPkhB,OAAQ,CACNpiB,OAAO,EACPI,MAAM,EACNmpB,KAAK,GAEPpoB,WAAY,CAAS,EACrB2U,eAAgB,CAAC,GAClB7T,KAED8R,eCoBK,SAA8BnT,GACnC,IAAMkT,EAAY,SAAZA,EAAalM,EAAgBnG,GACjC,IAAIH,EAAeV,EAAM4P,cAAcimB,YAAY7uB,EAAQnG,GAO3D,OALKH,KACHA,EAAeV,EAAM4P,cAAcgiB,IAAI5qB,EAAQnG,IAClC6N,OAAOiL,OAASzG,EAAS4iB,cAGjCp1B,GA2IT,OAvIAwS,EAAS6iB,kBAAoBrhB,GAC7BxB,EAAS8iB,aAAethB,GACxBxB,EAAS+iB,iBAAmBvhB,GAC5BxB,EAASgjB,cAAgBxhB,GAEzBxB,EAASrN,eAAiB+G,EAC1BsG,EAAS/N,qBAAuByH,EAChCsG,EAAS3O,gBAAkBqI,EAC3BsG,EAAS7O,QAAUuI,EAEnBsG,EAAS4iB,aAAe,GAGxB5iB,EAASijB,QAAUC,UACnBljB,EAASlT,MAAQA,EACjBkT,EAASmjB,IAAM,SAAUC,EAAQz1B,GAG/B,OAFAQ,KAAKrB,MAAMoT,UAAUkjB,EAAQz1B,GAEtBQ,MAGT6R,EAASqjB,MAAQ,SAAUvvB,EAAgBnG,GACzC,QAASQ,KAAKrB,MAAM4P,cAAcnG,IAAIzC,EAAQnG,GAAWA,EAAQkuB,UAGnE7b,EAASW,GAAKmI,IAAS,SAAYta,EAA2B0f,EAAwBvgB,GAKpF,GAJIpD,EAAGK,OAAO4D,KAA+B,IAAtBA,EAAKC,OAAO,OACjCD,EAAOA,EAAKmH,OAAOT,MAAM,OAGvB3K,EAAGc,MAAMmD,GAAO,CAAA,IAAA,IAAA6G,EAAA,EAAA9I,EACMiC,EAAI6G,EAAA9I,EAAAjB,OAAA+J,IAAW,CAAlC,IAAM8Y,EAAS5hB,EAAA8I,GAClBlH,KAAKwS,GAAGwN,EAAWD,EAAUvgB,EAC/B,CAEA,OAAOQ,IACT,CAEA,GAAI5D,EAAGH,OAAOoE,GAAO,CACnB,IAAK,IAAMoF,KAAQpF,EACjBL,KAAKwS,GAAG/M,EAAOpF,EAAaoF,GAAOsa,GAGrC,OAAO/f,IACT,CAgBA,OAbIigB,GAAiB5f,EAAML,KAAKrB,MAAMC,SAE/BoB,KAAKy0B,aAAap0B,GAGrBL,KAAKy0B,aAAap0B,GAAM6E,KAAK6a,GAF7B/f,KAAKy0B,aAAap0B,GAAQ,CAAC0f,GAO7B/f,KAAKrB,MAAM0O,OAAOkgB,IAAIvtB,KAAKrB,MAAMjD,SAAU2E,EAAM0f,EAAsB,CAAEvgB,QAAAA,IAGpEQ,IACR,GAAE,gDAEH6R,EAASU,IAAMoI,IAAS,SAAata,EAAkB0f,EAAevgB,GAKpE,GAJIpD,EAAGK,OAAO4D,KAA+B,IAAtBA,EAAKC,OAAO,OACjCD,EAAOA,EAAKmH,OAAOT,MAAM,OAGvB3K,EAAGc,MAAMmD,GAAO,CAAA,IAAA,IAAAsO,EAAA,EAAAwmB,EACM90B,EAAIsO,EAAAwmB,EAAAh4B,OAAAwR,IAAE,CAAzB,IAAMqR,EAASmV,EAAAxmB,GAClB3O,KAAKuS,IAAIyN,EAAWD,EAAUvgB,EAChC,CAEA,OAAOQ,IACT,CAEA,GAAI5D,EAAGH,OAAOoE,GAAO,CACnB,IAAK,IAAMoF,KAAQpF,EACjBL,KAAKuS,IAAI9M,EAAMpF,EAAKoF,GAAOsa,GAG7B,OAAO/f,IACT,CAGE,IAAIyN,EASN,OAVIwS,GAAiB5f,EAAML,KAAKrB,MAAMC,SAGhCyB,KAAQL,KAAKy0B,eAAyE,KAAxDhnB,EAAQzN,KAAKy0B,aAAap0B,GAAM4H,QAAQ8X,KACxE/f,KAAKy0B,aAAap0B,GAAMjD,OAAOqQ,EAAO,GAGxCzN,KAAKrB,MAAM0O,OAAOmgB,OAAOxtB,KAAKrB,MAAMjD,SAAU2E,EAAM0f,EAAUvgB,GAGzDQ,IACR,GAAE,iDAEH6R,EAASujB,MAAQ,WACf,OAAOp1B,KAAKrB,OAGdkT,EAASvQ,cAAgB,WACvB,OAAOF,EAAQE,eAGjBuQ,EAASrQ,qBAAuB,WAC9B,OAAOJ,EAAQI,sBAGjBqQ,EAAS0H,KAAO,WAAY,IAAA,IAAAzL,EAAAunB,EAAAA,EACAr1B,KAAKrB,MAAM0e,aAAa7O,KAAIV,EAAAunB,EAAAl4B,OAAA2Q,IAAhCunB,EAAAvnB,GACRyL,OAGd,OAAOvZ,MAGT6R,EAASsV,qBAAuB,SAAUvT,GACxC,OAAIxX,EAAGG,OAAOqX,IACZ5T,KAAKrB,MAAM0e,aAAa8J,qBAAuBvT,EAExC5T,MAGFA,KAAKrB,MAAM0e,aAAa8J,sBAGjCtV,EAASkiB,YAAc,SAAUrU,EAAelgB,GAC9CQ,KAAKrB,MAAMo1B,YAAYrU,EAAKlgB,IAG9BqS,EAASyjB,eAAiB,SAAU5V,GAClC1f,KAAKrB,MAAM22B,eAAe5V,IAGrB7N,CACT,CDzKmB0jB,CAAqBv1B,MAAKA,KAC3C4kB,cAAgBA,GAAa5kB,KAC7BnB,kBAAY,EAAAmB,KACZuO,cAAgB,IAAIolB,GAAgB3zB,MAEpCA,KACA8xB,UAAI,EAEJ9xB,KACAtE,cAAQ,EAERsE,KACAxE,YAAM,EAENwE,KACAqtB,UAAoD,GAAErtB,KAEtDw1B,SAGI,CACFhnB,KAAM,GACNvP,IAAK,CAAC,GACPe,KA+CDy1B,eAAiB,SAAC/rB,GAAwB,OAAKoD,EAAKwoB,eAAe5rB,EAAM/D,OAAmB,EA5C1F,IAAMhH,EAAQqB,KAEdA,KAAKnB,aAAY,SAAA62B,GAAAjpB,EAAAkpB,EAAAD,GAAA,IAAAhpB,EAAAC,EAAAgpB,GAAA,SAAAA,IAAA,OAAAjqB,OAAAiqB,GAAAjpB,EAAA1G,MAAAhG,KAAA4G,UAAA,CAwBd,OAxBckF,EAAA6pB,EAAA,CAAA,CAAA5pB,IAAA,YAAA3D,IACf,WACE,OAAOzJ,EAAMG,QACf,GAAC,CAAAiN,IAAA,MAAA3G,MAED,SAAyC5F,GAQvC,OAPAo2B,EAAAC,EAAAF,EAAA52B,WAAA,MAAAiB,MAAA+M,KAAA/M,KAAUR,GAEVb,EAAMiP,KAAK,mBAAoB,CAC7BpO,QAAAA,EACAH,aAAcW,OAGTA,IACT,GAAC,CAAA+L,IAAA,QAAA3G,MAED,WACEwwB,EAAAC,EAAAF,EAAA52B,yBAAAgO,KAAA/M,MAEA,IAAMyN,EAAQ9O,EAAM4P,cAAcC,KAAKvG,QAAQjI,MAC3CyN,EAAQ,IAEZ9O,EAAM4P,cAAcC,KAAKpR,OAAOqQ,EAAO,GACvC9O,EAAMiP,KAAK,qBAAsB,CAAEvO,aAAcW,OACnD,KAAC21B,CAAA,CAxBc,CAAiBG,GA0BpC,CA4HC,OA5HAhqB,EAAAqoB,EAAA,CAAA,CAAApoB,IAAA,eAAA3G,MAED,SAAanG,EAAkBR,GAC7BuB,KAAKu0B,aAAarvB,KAAK,CAAEzG,GAAAA,EAAIQ,IAAAA,GAC/B,GAAC,CAAA8M,IAAA,OAAA3G,MAED,SAA6B3H,EAAS2B,GAAkC,IAAA,IAAA8H,EAAA,EAAA6uB,EAGjE/1B,KAAKu0B,aAAYrtB,EAAA6uB,EAAA54B,OAAA+J,IAAE,CAFnB,IACY6Y,EAAQgW,EAAA7uB,GAAvBjI,IAAQxB,GAER,GAAMsiB,IAA0D,IAA9CA,EAAS3gB,EAAYY,KAAMvC,GAC3C,OAAO,CAEX,CACF,GAAC,CAAAsO,IAAA,OAAA3G,MAID,SAAK5J,GACH,OAAOwE,KAAKs0B,cAAgBt0B,KAkHzB,SAAmBrB,EAAcnD,GACtCmD,EAAM21B,eAAgB,EAElBl4B,EAAGZ,OAAOA,IACZF,EAASE,GAcX,OAXAgF,EAAWjF,KAAKC,GAChB4F,EAAQ7F,KAAKC,GACb2c,GAAI5c,KAAKC,GAGTmD,EAAMnD,OAASA,EACfmD,EAAMjD,SAAWF,EAAOE,SAExBiD,EAAMoT,UAAUsL,IAChB1e,EAAMoT,UAAU1E,IAET1O,CACT,CArIuCq3B,CAAUh2B,KAAMxE,EACrD,GAAC,CAAAuQ,IAAA,oBAAA3G,MAED,SAAkB6vB,GAChB,IAAQx2B,EAAOw2B,EAAPx2B,GACR,OAAOA,IAAOuB,KAAKw1B,SAASv2B,IAAIR,IAA8C,IAAxCuB,KAAKw1B,SAAShnB,KAAKvG,QAAQgtB,EACnE,GAAC,CAAAlpB,IAAA,YAAA3G,MAED,SAAU6vB,EAAgBz1B,GACxB,IAAKQ,KAAKs0B,cACR,OAAOt0B,KAGT,GAAIA,KAAKi2B,kBAAkBhB,GACzB,OAAOj1B,KAYT,GATIi1B,EAAOx2B,KACTuB,KAAKw1B,SAASv2B,IAAIg2B,EAAOx2B,IAAMw2B,GAEjCj1B,KAAKw1B,SAAShnB,KAAKtJ,KAAK+vB,GAEpBA,EAAOv2B,SACTu2B,EAAOv2B,QAAQsB,KAAMR,GAGnBy1B,EAAO91B,WAAa81B,EAAOzf,OAAQ,CASrC,IARA,IAAI/H,EAAQ,EACNwO,EAAMjc,KAAKu0B,aAAap3B,OACxBqY,EAASyf,EAAOzf,OAAOpD,QAAO,SAACC,EAAK5T,GAGxC,OAFA4T,EAAI5T,IAAM,EACV4T,EAAI6jB,GAAaz3B,KAAO,EACjB4T,CACR,GAAE,CAAE,GAEE5E,EAAQwO,EAAKxO,IAAS,CAC3B,IAAM0oB,EAAUn2B,KAAKu0B,aAAa9mB,GAAOhP,GAEzC,GAAI03B,IAAY3gB,EAAO2gB,IAAY3gB,EAAO0gB,GAAaC,KACrD,KAEJ,CAEAn2B,KAAKu0B,aAAan3B,OAAOqQ,EAAO,EAAG,CAAEhP,GAAIw2B,EAAOx2B,GAAIQ,IAAKg2B,EAAO91B,WAClE,MAAW81B,EAAO91B,WAChBa,KAAKu0B,aAAarvB,KAAK,CAAEzG,GAAIw2B,EAAOx2B,GAAIQ,IAAKg2B,EAAO91B,YAGtD,OAAOa,IACT,GAAC,CAAA+L,IAAA,cAAA3G,MAED,SAAYsa,EAAelgB,GAEzB,IAA+B,IAA3BQ,KAAKo2B,YAAY1W,GACnB,OAAO,EAGT,IAAMlkB,EAASF,EAAcokB,GAE7BlgB,EAAUA,EAAU8F,EAAO,CAAE,EAAE9F,GAAW,CAAA,EAE1CQ,KAAKqtB,UAAUnoB,KAAK,CAAEwa,IAAAA,EAAKlgB,QAAAA,IAC3BQ,KAAKqN,OAAOggB,UAAUnoB,KAAKwa,GAIvBA,IAAQ1f,KAAKtE,UACfsE,KAAKqN,OAAOkgB,IAAI/xB,EAAQ,SAAUwE,KAAKy1B,gBAGzCz1B,KAAK4N,KAAK,qBAAsB,CAAE8R,IAAAA,EAAKlkB,OAAAA,EAAQmD,MAAOqB,KAAMR,QAAAA,GAC9D,GAAC,CAAAuM,IAAA,iBAAA3G,MAED,SAAesa,GACb,IAAMjS,EAAQzN,KAAKo2B,YAAY1W,GAEzBlkB,EAASF,EAAcokB,GACvBlgB,EAAUQ,KAAKqtB,UAAU5f,GAAOjO,QAEtCQ,KAAKqN,OAAOmgB,OAAOhyB,EAAQ,SAAUwE,KAAKy1B,gBAE1Cz1B,KAAKqtB,UAAUjwB,OAAOqQ,EAAO,GAC7BzN,KAAKqN,OAAOggB,UAAUjwB,OAAOqQ,EAAO,GAEpCzN,KAAK4N,KAAK,wBAAyB,CAAE8R,IAAAA,EAAKlkB,OAAAA,EAAQmD,MAAOqB,KAAMR,QAAAA,GACjE,GAAC,CAAAuM,IAAA,cAAA3G,MAED,SAAYsa,GACV,IAAK,IAAIrT,EAAI,EAAGA,EAAIrM,KAAKqtB,UAAUlwB,OAAQkP,IACzC,GAAIrM,KAAKqtB,UAAUhhB,GAAGqT,MAAQA,EAC5B,OAAOrT,EAIX,OAAQ,CACV,GAAC,CAAAN,IAAA,gBAAA3G,MAED,SAAcsa,GACZ,IAAM2W,EAAWr2B,KAAKo2B,YAAY1W,GAElC,OAAqB,IAAd2W,EAAkB,KAAOr2B,KAAKqtB,UAAUgJ,GAAU72B,OAC3D,GAAC,CAAAuM,IAAA,MAAA3G,MAED,WACE,OAAUpF,KAAKxE,OAAeqd,MAAwBA,MAAMC,KAC9D,KAACqb,CAAA,CAxMe,GAsOlB,SAAS+B,GAAaz3B,GACpB,OAAOA,GAAMA,EAAG2E,QAAQ,QAAS,GACnC,CE7RA,IAAMzE,GAAQ,IAAIw1B,GAIlBmC,GAFiB33B,GAAMmT,eAIjBykB,GAAgC,oBAAfC,WAA6BA,WAA6Ch7B,OACjGmD,GAAMpD,KAAKg7B,ICTI,gDAAA,WAAQ,WCAR,WAAQ,OC2BR,SAACE,GACd,IAAMC,EACJ,CACE,CAAC,IAAK,KACN,CAAC,OAAQ,OACT,CAAC,QAAS,UACV,CAAC,QAAS,WAEZ/vB,QAAO,SAAArJ,GAAA,IAAEq5B,EAAMr5B,EAAA,GAAEs5B,EAAMt5B,EAAA,GAAA,OAAMq5B,KAAUF,GAAQG,KAAUH,KAErDI,EAGF,SAAC34B,EAAGJ,GAcmD,IAbzD,IACEg5B,EAQEL,EARFK,MAAKC,EAQHN,EAPFO,OAAAA,OAAS,IAAHD,EAAG,CACP7yB,MAAOoR,IACPnR,MAAOmR,IACPlR,KAAMkR,IACNjR,OAAQiR,KACTyhB,EAAAE,EAECR,EADFxO,OAAAA,OAAS,IAAHgP,EAAG,CAAE/4B,EAAG,EAAGJ,EAAG,GAAGm5B,EAGnBnwB,EAEF,CAAEgwB,MAAAA,EAAOL,KAAAA,EAAMv4B,EAAG,KAAgBJ,EAAG,MAAgBoJ,IAAAA,EAE1BwvB,EAAWv5B,OAAA+J,IAAE,CAAvC,IAAAgwB,EAA0BR,EAAWxvB,GAA9ByvB,EAAMO,EAAA,GAAEN,EAAMM,EAAA,GAClBC,EAAQzvB,KAAK0vB,OAAOl5B,EAAI+pB,EAAO/pB,GAAMu4B,EAAaE,IAClDU,EAAQ3vB,KAAK0vB,OAAOt5B,EAAImqB,EAAOnqB,GAAM24B,EAAaG,IAExD9vB,EAAO6vB,GAAUjvB,KAAK+C,IAAIusB,EAAO9yB,KAAMwD,KAAK4C,IAAI0sB,EAAO7yB,MAAOgzB,EAASV,EAAaE,GAAU1O,EAAO/pB,IACrG4I,EAAO8vB,GAAUlvB,KAAK+C,IAAIusB,EAAO5yB,IAAKsD,KAAK4C,IAAI0sB,EAAO3yB,OAAQgzB,EAASZ,EAAaG,GAAU3O,EAAOnqB,GACvG,CAEA,OAAOgJ,GAMT,OAHA+vB,EAASJ,KAAOA,EAChBI,EAASH,YAAcA,EAEhBG,CACR,IC3DKS,GAAyB,CAC7B74B,GAAI,WACJC,QAAO,SAACC,GACN,IAAwBkT,EAAalT,EAA7BmT,eAERD,EAAS0lB,SAAWjyB,EAAOuM,EAAS0lB,UAAY,CAAA,EAAIC,IACpD3lB,EAAS4lB,eAAiB5lB,EAAS0lB,SAASd,IAC9C,GAGFc,GAAeD,GC4BTI,GAAoE,CACxE35B,MAAK,SAACqB,GACJ,IAAQsiB,EAA2CtiB,EAA3CsiB,MAAOxb,EAAoC9G,EAApC8G,KAAMG,EAA8BjH,EAA9BiH,MAAmB1I,EAAWyB,EAAvBwhB,WAC5B+W,EAAyBjW,EAAMliB,QAAzBo4B,EAAKD,EAALC,MAAOn4B,EAAOk4B,EAAPl4B,QACbo4B,EAAkCnW,EAAMliB,QAAhCs4B,EAAUD,EAAVC,WAAY/W,EAAS8W,EAAT9W,UAEN,aAAV6W,IACFA,EAAQ1xB,EAAK5B,MAAQ4B,EAAK3B,QAG5Bmd,EAAMgB,YAAcpd,EAAO,CAAE,EAAE3H,GAC/B+jB,EAAMlK,UAAYlS,EAAO,CAAE,EAAEY,GAC7Bwb,EAAMkW,MAAQA,EACdlW,EAAMoW,WAAaA,EAEnB,IAAMC,EAAerW,EAAMqW,YAAc,CACvC3zB,IAAKiC,EAAMjC,KAAQiC,EAAMnC,OAASmC,EAAMhC,OACxCH,KAAMmC,EAAMnC,MAASmC,EAAMjC,MAAQiC,EAAMlC,MACzCE,OAAQgC,EAAMhC,QAAWgC,EAAMlC,QAAUkC,EAAMjC,IAC/CD,MAAOkC,EAAMlC,OAAUkC,EAAMhC,SAAWgC,EAAMnC,MAKhD,GAFAwd,EAAMsW,kBAAoB3xB,EAAMnC,OAAQmC,EAAMlC,OAE1Cud,EAAMoW,WAAY,CACpB,IAAMG,GAAQF,EAAY7zB,KAAO,GAAK,IAAM6zB,EAAY3zB,IAAM,GAAK,GACnEsd,EAAMwW,SAAW,CACfh6B,EAAG+5B,EACHn6B,EAAGm6B,EAEP,MACEvW,EAAMwW,SAAW,CACfh6B,EAAG65B,EAAY7zB,MAAQ,EAAI,EAC3BpG,EAAGi6B,EAAY3zB,KAAO,EAAI,GAQ9B,IAJgB,IAAZ3E,GACF6F,EAAOe,EAAO0xB,GAGF,MAAThX,GAAAA,EAAW5jB,OAAhB,CAEA,IAAMg7B,EAAkB,IAAI9X,GAAajhB,EAAI7B,aAE7C46B,EAAgBzO,SAAStqB,EAAI7B,YAAYimB,cACzC2U,EAAgB/W,cAAcL,GAE9BW,EAAMyW,gBAAkBA,EACxBA,EAAgB5W,SAAQ6W,EAAMh5B,CAAAA,EAAAA,GARN,CASzB,EAEDiJ,IAAG,SAACjJ,GACF,IAAQsiB,EAAwBtiB,EAAxBsiB,MAAOxb,EAAiB9G,EAAjB8G,KAAMvI,EAAWyB,EAAXzB,OACbo6B,EAAgBrW,EAAhBqW,YACFM,EAAgB/yB,EAAO,CAAE,EAAE3H,GAC3B26B,EAAe5W,EAAMoW,WAAaS,GAAgBC,GAKxD,GAHAlzB,EAAOlG,EAAIiH,MAAO0xB,GAClBO,EAAa5W,EAAOA,EAAMsW,eAAgBr6B,EAAQuI,IAE7Cwb,EAAMyW,gBACT,OAAO,KAGT,IAAMM,EAAgBnzB,EAAO,CAAE,EAAEY,GAEjCE,EAAS2xB,EAAaU,EAAe,CACnCv6B,EAAGP,EAAOO,EAAIm6B,EAAcn6B,EAC5BJ,EAAGH,EAAOG,EAAIu6B,EAAcv6B,IAG9B,IAAMgJ,EAAS4a,EAAMyW,gBAAgB3W,OAAM4W,EAAAA,EAAA,CAAA,EACtCh5B,GAAG,GAAA,CACN8G,KAAMuyB,EACNpyB,MAAO0xB,EACPnX,WAAYjjB,EACZ0kB,WAAY1kB,EACZ2kB,SAAUmW,KAGJl6B,EAAUuI,EAAVvI,MAEJuI,EAAO0b,UAIT8V,EAAa5W,EAHWha,KAAKqO,IAAIxX,EAAML,GAAKwJ,KAAKqO,IAAIxX,EAAMT,GAGtBgJ,EAAOnJ,OAAQmJ,EAAOZ,MAC3DZ,EAAO3H,EAAQmJ,EAAOnJ,SAGxB,OAAOmJ,EAAOqb,UACf,EAEDrjB,SAAU,CACR84B,MAAO,WACPE,YAAY,EACZ/W,UAAW,GACXthB,SAAS,IAIb,SAAS84B,GAAaj7B,EAA8C06B,EAAyBr6B,GAAe,IAAnF+kB,EAAWplB,EAAXolB,YAAawV,EAAQ56B,EAAR46B,SAChCF,EACFr6B,EAAOG,EAAI4kB,EAAY5kB,GAAKH,EAAOO,EAAIwkB,EAAYxkB,GAAKg6B,EAASp6B,EAEjEH,EAAOO,EAAIwkB,EAAYxkB,GAAKP,EAAOG,EAAI4kB,EAAY5kB,GAAKo6B,EAASh6B,CAErE,CAEA,SAASs6B,GAAQp6B,EAEf45B,EACAr6B,EACAuI,GACA,IAJEsR,EAASpZ,EAAToZ,UAAWkL,EAAWtkB,EAAXskB,YAAakV,EAAKx5B,EAALw5B,MAAOM,EAAQ95B,EAAR85B,SAKjC,GAAIF,EAAgB,CAClB,IAAMU,EAAYxyB,EAAK5B,MAAQszB,EAE/Bj6B,EAAOG,EAAI4kB,EAAY5kB,GAAK46B,EAAYlhB,EAAUjT,QAAU2zB,EAASp6B,CACvE,KAAO,CACL,IAAM66B,EAAWzyB,EAAK3B,OAASqzB,EAE/Bj6B,EAAOO,EAAIwkB,EAAYxkB,GAAKy6B,EAAWnhB,EAAUlT,OAAS4zB,EAASh6B,CACrE,CACF,CAEA,IAAA06B,GAAe5U,GAAa0T,GAAa,eC/KnCmB,GAAQ,aAEdA,GAAKvU,UAAY,CAAA,EAEjB,IAAAwU,GAAeD,GC0ER,SAASE,GACd3zB,EACA7H,EACAI,GAEA,OAAIvB,EAAGD,KAAKiJ,GACH8c,EAA0B9c,EAAO7H,EAAY8B,aAAc9B,EAAYb,QAAS,CACrFiB,EAAOO,EACPP,EAAOG,EACPP,IAGK2kB,EAA0B9c,EAAO7H,EAAY8B,aAAc9B,EAAYb,QAElF,CAEA,IAQMs8B,GAA2D,CAC/Dj7B,MA9EF,SAAcT,GAAoF,IAAjF4I,EAAI5I,EAAJ4I,KAAMqa,EAAWjjB,EAAXijB,YAAamB,EAAKpkB,EAALokB,MAAOnkB,EAAWD,EAAXC,YAAaqjB,EAAUtjB,EAAVsjB,WAC9CphB,EAAYkiB,EAAZliB,QACAy5B,EAAgBz5B,EAAhBy5B,YACFhR,EAAe3iB,EACnB,CACEpB,KAAM,EACNE,IAAK,EACLD,MAAO,EACPE,OAAQ,GAEV7E,EAAQyoB,QAAU,CAAA,GAGpB,GAAI/hB,GAAQ+yB,EAAa,CACvB,IAAMC,EAAcH,GAAmBv5B,EAAQ05B,YAAa37B,EAAaqjB,GAEzE,GAAIsY,EAAa,CACf,IAAMC,EAAYD,EAAY/0B,MAAQ+0B,EAAYh1B,KAAOgC,EAAK5B,MACxD80B,EAAaF,EAAY70B,OAAS60B,EAAY90B,IAAM8B,EAAK3B,OAE3D40B,EAAY,IACdlR,EAAO/jB,MAAQi1B,EACflR,EAAO9jB,OAASg1B,GAEdC,EAAa,IACfnR,EAAO7jB,KAAOg1B,EACdnR,EAAO5jB,QAAU+0B,EAErB,CAEAnR,EAAO/jB,MAAQqc,EAAYrc,KAAOgC,EAAK5B,MAAQ20B,EAAY/0B,KAC3D+jB,EAAO7jB,KAAOmc,EAAYnc,IAAM8B,EAAK3B,OAAS00B,EAAY70B,IAE1D6jB,EAAO9jB,OAASoc,EAAYpc,MAAQ+B,EAAK5B,OAAS,EAAI20B,EAAY90B,OAClE8jB,EAAO5jB,QAAUkc,EAAYlc,OAAS6B,EAAK3B,QAAU,EAAI00B,EAAY50B,OACvE,CAEAqd,EAAMuG,OAASA,CACjB,EAyCE5f,IAvCF,SAAYjK,GAA6D,IAA1DT,EAAMS,EAANT,OAAQJ,EAAWa,EAAXb,YAAamkB,EAAKtjB,EAALsjB,MAC1BliB,EAAoBkiB,EAApBliB,QAASyoB,EAAWvG,EAAXuG,OAEXiR,EAAcH,GAAmBv5B,EAAQ05B,YAAa37B,EAAaI,GAEzE,GAAKu7B,EAAL,CAEA,IAAMhzB,E/CXD,SAAqDA,GAU1D,OATIA,GAAU,SAAUA,GAAQ,QAASA,KACvCA,EAAOZ,EAAO,GAAIY,IAEbhC,KAAOgC,EAAKhI,GAAK,EACtBgI,EAAK9B,IAAM8B,EAAKpI,GAAK,EACrBoI,EAAK/B,MAAQ+B,EAAK/B,OAAS+B,EAAKhC,KAAOgC,EAAK5B,MAC5C4B,EAAK7B,OAAS6B,EAAK7B,QAAU6B,EAAK9B,IAAM8B,EAAK3B,QAGxC2B,CACT,C+CAegc,CAAqBgX,GAElCv7B,EAAOO,EAAIwJ,KAAK+C,IAAI/C,KAAK4C,IAAIpE,EAAK/B,MAAQ8jB,EAAO9jB,MAAOxG,EAAOO,GAAIgI,EAAKhC,KAAO+jB,EAAO/jB,MACtFvG,EAAOG,EAAI4J,KAAK+C,IAAI/C,KAAK4C,IAAIpE,EAAK7B,OAAS4jB,EAAO5jB,OAAQ1G,EAAOG,GAAIoI,EAAK9B,IAAM6jB,EAAO7jB,IALrE,CAMpB,EA6BEtF,SAXgC,CAChCo6B,YAAa,KACbD,YAAa,KACbhR,OAAQ,KACRpE,SAAS,EACTpkB,SAAS,IASX45B,GAAerV,GAAagV,GAAU,YCxEhCM,GAAU,CAAEl1B,IAAMkR,IAAUpR,KAAOoR,IAAUjR,QAASiR,IAAUnR,OAAQmR,KACxEikB,GAAU,CAAEn1B,KAAMkR,IAAUpR,MAAOoR,IAAUjR,OAASiR,IAAUnR,MAAQmR,KAgD9E,SAASkkB,GAAQtzB,EAAYpH,GAAgB,IAAAoI,IAAAA,IAAAiN,EACxB,CAAC,MAAO,OAAQ,SAAU,SAAQjN,EAAAiN,EAAAhX,OAAA+J,IAAE,CAAlD,IAAM8O,EAAI7B,EAAAjN,GACP8O,KAAQ9P,IACZA,EAAK8P,GAAQlX,EAASkX,GAE1B,CAEA,OAAO9P,CACT,CAEA,IAQMuzB,GAAgB,CACpBH,QAAAA,GACAC,QAAAA,GACAx7B,MAnEF,SAAcT,GAAuE,IAE/E2qB,EAFW1qB,EAAWD,EAAXC,YAAagjB,EAAWjjB,EAAXijB,YAAamB,EAAKpkB,EAALokB,MACjCliB,EAAYkiB,EAAZliB,QAGJA,IAGFyoB,EAAS/F,EAFU6W,GAAmBv5B,EAAQyoB,OAAQ1qB,EAAaA,EAAYI,OAAOI,MAAMF,QAK9FoqB,EAASA,GAAU,CAAE/pB,EAAG,EAAGJ,EAAG,GAE9B4jB,EAAMuG,OAAS,CACb7jB,IAAK6jB,EAAOnqB,EAAIyiB,EAAYnc,IAC5BF,KAAM+jB,EAAO/pB,EAAIqiB,EAAYrc,KAC7BG,OAAQ4jB,EAAOnqB,EAAIyiB,EAAYlc,OAC/BF,MAAO8jB,EAAO/pB,EAAIqiB,EAAYpc,MAElC,EAkDEkE,IAhDF,SAAYjK,GAAyE,IAAtET,EAAMS,EAANT,OAAQ0I,EAAKjI,EAALiI,MAAO9I,EAAWa,EAAXb,YAAamkB,EAAKtjB,EAALsjB,MACjCuG,EAAoBvG,EAApBuG,OAAQzoB,EAAYkiB,EAAZliB,QAEhB,GAAK6G,EAAL,CAIA,IAAMxI,EAAOyH,EAAO,CAAE,EAAE3H,GAClB+7B,EAAQX,GAAmBv5B,EAAQk6B,MAAOn8B,EAAaM,IAAU,GACjE87B,EAAQZ,GAAmBv5B,EAAQm6B,MAAOp8B,EAAaM,IAAU,GAEvE27B,GAAQE,EAAOJ,IACfE,GAAQG,EAAOJ,IAEXlzB,EAAMjC,IACRzG,EAAOG,EAAI4J,KAAK4C,IAAI5C,KAAK+C,IAAIkvB,EAAMv1B,IAAM6jB,EAAO7jB,IAAKvG,EAAKC,GAAI47B,EAAMt1B,IAAM6jB,EAAO7jB,KACxEiC,EAAMhC,SACf1G,EAAOG,EAAI4J,KAAK+C,IAAI/C,KAAK4C,IAAIqvB,EAAMt1B,OAAS4jB,EAAO5jB,OAAQxG,EAAKC,GAAI47B,EAAMr1B,OAAS4jB,EAAO5jB,SAExFgC,EAAMnC,KACRvG,EAAOO,EAAIwJ,KAAK4C,IAAI5C,KAAK+C,IAAIkvB,EAAMz1B,KAAO+jB,EAAO/jB,KAAMrG,EAAKK,GAAIw7B,EAAMx1B,KAAO+jB,EAAO/jB,MAC3EmC,EAAMlC,QACfxG,EAAOO,EAAIwJ,KAAK+C,IAAI/C,KAAK4C,IAAIqvB,EAAMx1B,MAAQ8jB,EAAO9jB,MAAOtG,EAAKK,GAAIw7B,EAAMv1B,MAAQ8jB,EAAO9jB,OAjBzF,CAmBF,EAyBErF,SAbqC,CACrC46B,MAAO,KACPC,MAAO,KACP1R,OAAQ,KACRpE,SAAS,EACTpkB,SAAS,IAWXm6B,GAAe5V,GAAayV,GAAe,iBC3GrC36B,GAAWwG,EACf,CACE,eAAI2zB,GACF,MAAO,CAAE70B,IAAK,EAAGF,KAAM,EAAGG,OAAQ,EAAGF,MAAO,EAC7C,EACD,eAAI80B,CAAYY,GAAI,GAEtBb,GAASl6B,UASXg7B,GAAe9V,GANM,CACnBjmB,MAAOi7B,GAASj7B,MAChBsK,IAAK2wB,GAAS3wB,IACdvJ,SAAAA,IAGwC,gBCVpCi7B,GAAQ,CAAEz1B,OAAQgR,IAAU/Q,QAAS+Q,KACrC0kB,GAAQ,CAAE11B,MAAQgR,IAAU/Q,OAAS+Q,KA6D3C,IAaA2kB,GAAejW,GANM,CACnBjmB,MA5DF,SAAeqB,GACb,OAAOq6B,GAAc17B,MAAMqB,EAC7B,EA2DEiJ,IAhDF,SAAajJ,GACX,IAAQ7B,EAAoC6B,EAApC7B,YAAamkB,EAAuBtiB,EAAvBsiB,MAAOxb,EAAgB9G,EAAhB8G,KAAMG,EAAUjH,EAAViH,MAC1B7G,EAAYkiB,EAAZliB,QAER,GAAK6G,EAAL,CAIA,IAAM6zB,EACJhY,EAAqB6W,GAAmBv5B,EAAQ8K,IAAY/M,EAAa6B,EAAIzB,UAAYo8B,GACrFI,EACJjY,EAAqB6W,GAAmBv5B,EAAQiL,IAAYlN,EAAa6B,EAAIzB,UAAYq8B,GAE3FtY,EAAMliB,QAAU,CACdqkB,QAASrkB,EAAQqkB,QACjB6V,MAAOp0B,EAAO,GAAIm0B,GAAcH,SAChCK,MAAOr0B,EAAO,GAAIm0B,GAAcF,UAG9BlzB,EAAMjC,KACRsd,EAAMliB,QAAQk6B,MAAMt1B,IAAM8B,EAAK7B,OAAS61B,EAAQ31B,OAChDmd,EAAMliB,QAAQm6B,MAAMv1B,IAAM8B,EAAK7B,OAAS81B,EAAQ51B,QACvC8B,EAAMhC,SACfqd,EAAMliB,QAAQk6B,MAAMr1B,OAAS6B,EAAK9B,IAAM81B,EAAQ31B,OAChDmd,EAAMliB,QAAQm6B,MAAMt1B,OAAS6B,EAAK9B,IAAM+1B,EAAQ51B,QAE9C8B,EAAMnC,MACRwd,EAAMliB,QAAQk6B,MAAMx1B,KAAOgC,EAAK/B,MAAQ+1B,EAAQ51B,MAChDod,EAAMliB,QAAQm6B,MAAMz1B,KAAOgC,EAAK/B,MAAQg2B,EAAQ71B,OACvC+B,EAAMlC,QACfud,EAAMliB,QAAQk6B,MAAMv1B,MAAQ+B,EAAKhC,KAAOg2B,EAAQ51B,MAChDod,EAAMliB,QAAQm6B,MAAMx1B,MAAQ+B,EAAKhC,KAAOi2B,EAAQ71B,OAGlDm1B,GAAcpxB,IAAIjJ,GAElBsiB,EAAMliB,QAAUA,CA9BhB,CA+BF,EAYEV,SAVoC,CACpCwL,IAAK,KACLG,IAAK,KACLoZ,SAAS,EACTpkB,SAAS,IAS+B,gBCkH1C,IAUM26B,GAAO,CACXr8B,MA3JF,SAAeqB,GACb,IAIIi7B,EAJI98B,EAAiE6B,EAAjE7B,YAAa8B,EAAoDD,EAApDC,aAAc3C,EAAsC0C,EAAtC1C,QAASwJ,EAA6B9G,EAA7B8G,KAAMwb,EAAuBtiB,EAAvBsiB,MAAOnB,EAAgBnhB,EAAhBmhB,YACjD/gB,EAAYkiB,EAAZliB,QACFiH,EAASjH,EAAQ86B,iBAqIzB,SAAmBl7B,GACjB,IAAQ1C,EAAY0C,EAAI7B,YAAhBb,QACF69B,EAAgBt0B,EAASJ,EAAgBzG,EAAIsiB,MAAMliB,QAAQiH,OAAe,KAAM,KAAM,CAAC/J,KACvF+J,EAAS8zB,GAAiBj0B,EAAYlH,EAAIC,aAAc3C,EAAS0C,EAAI7B,YAAYC,SAASC,MAEhG,OAAOgJ,CACT,CA3I4C+zB,CAAUp7B,GAAO,CAAElB,EAAG,EAAGJ,EAAG,GAItE,GAAuB,gBAAnB0B,EAAQyoB,OACVoS,EAAa,CACXn8B,EAAGX,EAAYI,OAAOI,MAAMF,KAAKK,EACjCJ,EAAGP,EAAYI,OAAOI,MAAMF,KAAKC,OAE9B,CACL,IAAM28B,EAAa50B,EAAgBrG,EAAQyoB,OAAe5oB,EAAc3C,EAAS,CAACa,KAElF88B,EAAap0B,EAASw0B,IAAe,CAAEv8B,EAAG,EAAGJ,EAAG,IACrCI,GAAKuI,EAAOvI,EACvBm8B,EAAWv8B,GAAK2I,EAAO3I,CACzB,CAEA,IAAQ48B,EAAmBl7B,EAAnBk7B,eAERhZ,EAAMiZ,QACJz0B,GAAQw0B,GAAkBA,EAAev9B,OACrCu9B,EAAez7B,KAAI,SAAC27B,EAAentB,GAAK,MAAM,CAC5CA,MAAAA,EACAmtB,cAAAA,EACA18B,EAAGqiB,EAAYrc,KAAOgC,EAAK5B,MAAQs2B,EAAc18B,EAAIm8B,EAAWn8B,EAChEJ,EAAGyiB,EAAYnc,IAAM8B,EAAK3B,OAASq2B,EAAc98B,EAAIu8B,EAAWv8B,EAChE,IACF,CACE,CACE2P,MAAO,EACPmtB,cAAe,KACf18B,EAAGm8B,EAAWn8B,EACdJ,EAAGu8B,EAAWv8B,GAG1B,EAsHEuK,IApHF,SAAajJ,GACX,IAAQ7B,EAA+B6B,EAA/B7B,YAAaI,EAAkByB,EAAlBzB,OAAQ+jB,EAAUtiB,EAAVsiB,MACrBliB,EAAqBkiB,EAArBliB,QAASm7B,EAAYjZ,EAAZiZ,QAEXl0B,EAASH,EAAY/I,EAAY8B,aAAe9B,EAAYb,QAAUa,EAAYC,SAASC,MAC3FI,EAAOyH,EAAO,CAAE,EAAE3H,GAClBwvB,EAA0B,GAE3B3tB,EAAQ86B,mBACXz8B,EAAKK,GAAKuI,EAAOvI,EACjBL,EAAKC,GAAK2I,EAAO3I,GAClB,IAAA,IAAAoJ,EAAA,EAAA9I,EAEoBu8B,EAAOzzB,EAAA9I,EAAAjB,OAAA+J,IAI1B,IAJG,IAAM+gB,EAAM7pB,EAAA8I,GACT2zB,EAAYh9B,EAAKK,EAAI+pB,EAAO/pB,EAC5B48B,EAAYj9B,EAAKC,EAAImqB,EAAOnqB,EAEzB2P,EAAQ,EAAGwO,EAAMzc,EAAQ2tB,QAAShwB,OAAQsQ,EAAQwO,EAAKxO,IAAS,CACvE,IAAMstB,EAAav7B,EAAQ2tB,QAAS1f,GAChC9H,OAAoB,GAGtBA,EADEvJ,EAAGD,KAAK4+B,GACDA,EAAWF,EAAWC,EAAWv9B,EAAYyO,OAAQic,EAAQxa,GAE7DstB,IAOX5N,EAAQjoB,KAAK,CACXhH,GAAI9B,EAAGG,OAAOoJ,EAAOzH,GAAKyH,EAAOzH,EAAI28B,GAAa5S,EAAO/pB,EACzDJ,GAAI1B,EAAGG,OAAOoJ,EAAO7H,GAAK6H,EAAO7H,EAAIg9B,GAAa7S,EAAOnqB,EAEzDg5B,MAAO16B,EAAGG,OAAOoJ,EAAOmxB,OAASnxB,EAAOmxB,MAAQt3B,EAAQs3B,MACxDtxB,OAAQu1B,EACRttB,MAAAA,EACAwa,OAAAA,GAEJ,CASD,IAND,IAAMjlB,EAAU,CACd2C,OAAQ,KACRq1B,SAAS,EACTpmB,SAAU,EACVkiB,MAAO,EACPv4B,MAAO,CAAEL,EAAG,EAAGJ,EAAG,IACnB6Q,IAAAA,EAEoBwe,EAAOhwB,OAAAwR,IAAE,CAAzB,IAAMhJ,EAAUwnB,EAAOxe,GACpBmoB,EAAQnxB,EAAOmxB,MACf/rB,EAAKpF,EAAOzH,EAAIL,EAAKK,EACrB8M,EAAKrF,EAAO7H,EAAID,EAAKC,EACrB8W,EAAWnN,EAAMsD,EAAIC,GACvBgwB,EAAUpmB,GAAYkiB,EAItBA,IAAUxhB,KAAYtS,EAAQg4B,SAAWh4B,EAAQ8zB,QAAUxhB,MAC7D0lB,GAAU,GAITh4B,EAAQ2C,UACRq1B,EAEGh4B,EAAQg4B,SAAWlE,IAAUxhB,IAE3BV,EAAWkiB,EAAQ9zB,EAAQ4R,SAAW5R,EAAQ8zB,MAE7CA,IAAUxhB,KAAYtS,EAAQ8zB,QAAUxhB,KAEzCV,EAAW5R,EAAQ4R,UAEpB5R,EAAQg4B,SAAWpmB,EAAW5R,EAAQ4R,YAE3C5R,EAAQ2C,OAASA,EACjB3C,EAAQ4R,SAAWA,EACnB5R,EAAQ8zB,MAAQA,EAChB9zB,EAAQg4B,QAAUA,EAClBh4B,EAAQzE,MAAML,EAAI6M,EAClB/H,EAAQzE,MAAMT,EAAIkN,EAEtB,CAQA,OANIhI,EAAQg4B,UACVr9B,EAAOO,EAAI8E,EAAQ2C,OAAOzH,EAC1BP,EAAOG,EAAIkF,EAAQ2C,OAAO7H,GAG5B4jB,EAAM1e,QAAUA,EACTA,CACT,EAuBElE,SAb4B,CAC5Bg4B,MAAOxhB,IACP6X,QAAS,KACTlF,OAAQ,KACRqS,kBAAkB,EAClB7zB,OAAQ,KACRi0B,eAAgB,KAChB7W,SAAS,EACTpkB,SAAS,IAQXw7B,GAAejX,GAAaoW,GAAM,QC9HlC,IAQMc,GAAW,CACfn9B,MAtFF,SAAeqB,GACb,IAAQsiB,EAAiBtiB,EAAjBsiB,MAAOrb,EAAUjH,EAAViH,MACP7G,EAAYkiB,EAAZliB,QAER,IAAK6G,EACH,OAAO,KAGTjH,EAAIsiB,MAAQ,CACVliB,QAAS,CACP2tB,QAAS,KACTuN,eAAgB,CACd,CACEx8B,EAAGmI,EAAMnC,KAAO,EAAI,EACpBpG,EAAGuI,EAAMjC,IAAM,EAAI,IAGvB6jB,OAAQzoB,EAAQyoB,QAAU,OAC1BxhB,OAAQ,CAAEvI,EAAG,EAAGJ,EAAG,GACnBg5B,MAAOt3B,EAAQs3B,QAInBpV,EAAMyZ,aAAezZ,EAAMyZ,cAAgB,CACzC,CAAC,QAAS,UACV,CAAC,IAAK,MAGRf,GAAKr8B,MAAMqB,GACXsiB,EAAMiZ,QAAUv7B,EAAIsiB,MAAMiZ,QAE1Bv7B,EAAIsiB,MAAQA,CACd,EAuDErZ,IArDF,SAAajJ,GACX,IAAQ7B,EAA+B6B,EAA/B7B,YAAamkB,EAAkBtiB,EAAlBsiB,MAAO/jB,EAAWyB,EAAXzB,OACpB6B,EAAqBkiB,EAArBliB,QAASm7B,EAAYjZ,EAAZiZ,QACXS,EAAW,CACfl9B,EAAGP,EAAOO,EAAIy8B,EAAQ,GAAGz8B,EACzBJ,EAAGH,EAAOG,EAAI68B,EAAQ,GAAG78B,GAG3B4jB,EAAMliB,QAAU8F,EAAO,CAAE,EAAE9F,GAC3BkiB,EAAMliB,QAAQ2tB,QAAU,GAAE,IAAA,IAAAjmB,EAAA9I,EAAAA,EAEDoB,EAAQ2tB,SAAW,GAAEjmB,EAAA9I,EAAAjB,OAAA+J,IAAE,CAA3C,IAAM6zB,EAAU38B,EAAA8I,GACfvB,OAAM,EAQV,GALEA,EADEvJ,EAAGD,KAAK4+B,GACDA,EAAWK,EAASl9B,EAAGk9B,EAASt9B,EAAGP,GAEnCw9B,EAGX,CAEC,IAAA,IAAApsB,EAAA,EAAA0sB,EAE8B3Z,EAAMyZ,aAAYxsB,EAAA0sB,EAAAl+B,OAAAwR,IAAE,CAA9C,IAAA2sB,EAAAD,EAAA1sB,GAAOgoB,EAAM2E,EAAA,GAAE1E,EAAM0E,EAAA,GACxB,GAAI3E,KAAUhxB,GAAUixB,KAAUjxB,EAAQ,CACxCA,EAAOzH,EAAIyH,EAAOgxB,GAClBhxB,EAAO7H,EAAI6H,EAAOixB,GAElB,KACF,CACF,CAEAlV,EAAMliB,QAAQ2tB,QAAQjoB,KAAKS,EAX3B,CAYF,CAEA,IAAMI,EAAcq0B,GAAK/xB,IAAIjJ,GAI7B,OAFAsiB,EAAMliB,QAAUA,EAETuG,CACT,EAaEjH,SAXgC,CAChCg4B,MAAOxhB,IACP6X,QAAS,KACTlF,OAAQ,KACRpE,SAAS,EACTpkB,SAAS,IASX87B,GAAevX,GAAakX,GAAU,YCnDtC,ICxCeM,GAAA,CACb9D,YAAAA,GACA+B,cAAAA,GACAT,SAAAA,GACAyC,aAAAA,GACAC,aAAAA,GACAC,UD4Ca3X,GAViF,CAC9FjmB,MAfF,SAAeqB,GACb,IAAQiH,EAAUjH,EAAViH,MAER,OAAKA,GAILjH,EAAIsiB,MAAMyZ,aAAe/7B,EAAIsiB,MAAMyZ,cAAgB,CACjD,CAAC90B,EAAMnC,KAAO,OAAS,QAASmC,EAAMjC,IAAM,MAAQ,WAG/C82B,GAASn9B,MAAMqB,IAPb,IAQX,EAIEiJ,IAAK6yB,GAAS7yB,IACdvJ,SAAUwG,EAAO8a,GAAM8a,GAASp8B,UAAW,CACzCquB,aAAS9xB,EACTy7B,WAAOz7B,EACP4sB,OAAQ,CAAE/pB,EAAG,EAAGJ,EAAG,MAIgB,aC3CrCs8B,KAAAA,GACAc,SAAAA,GAEAU,OAAAA,GACAC,MAAAA,GACAC,UAAAA,GACAhD,WAAAA,ICVI/X,GAAoB,CACxBtiB,GAAI,YACJC,QAAO,SAACC,GACN,IAAwBkT,EAAalT,EAA7BmT,eAQR,IAAK,IAAMzR,KANX1B,EAAMoT,UAAU+L,IAChBnf,EAAMoT,UAAUwlB,IAEhB1lB,EAASkP,UAAYya,GAGFA,GAAK,CACtB,IAAAO,EAAgCP,GAAIn7B,GAA5BikB,EAASyX,EAATzX,UAAWrD,EAAQ8a,EAAR9a,SAEjBqD,EAAkBrD,SAAWA,EAC7BtiB,EAAMG,SAAS2b,UAAkBpa,GAAQikB,CAC7C,CACF,GAGF0X,GAAejb,GChCF9f,YAAYuL,GAAAC,EAAAxL,EAAAuL,GAAA,IAAAE,EAAAC,EAAA1L,GAcvB,SAAAA,EACEZ,EACAwI,EACAa,EACAmO,EACAta,EACAkL,GACA,IAAAqE,EAgBA,GAhBApB,OAAAzK,GAEAoS,GAA0BmS,EAD1B1Y,EAAAJ,EAAAK,UAAMxP,IAC2BmM,GAE7BA,IAAUb,GACZwK,GAA0BmS,EAAA1Y,GAAOjE,GAGnCiE,EAAKrE,UAAYA,EACjBqE,EAAK6f,cAAgBjjB,EACrBoD,EAAKzM,KAAOA,EACZyM,EAAK3D,UAAYkK,GAA0BxK,GAC3CiE,EAAKnN,YAAc0T,GAA4BxK,GAC/CiE,EAAKnH,OAASkS,EACd/K,EAAKtB,cAAgB,KAER,QAATnL,EAAgB,CAClB,IAAMymB,EAAevpB,EAAY6pB,gBAAgBve,GACjDiE,EAAK2M,GAAK3M,EAAKrE,UAAYlL,EAAY+L,SAASwd,GAAcrB,SAE9D,IAAMwW,EAAWnvB,EAAKrE,UAAYlL,EAAY2+B,QAE9CpvB,EAAKqvB,SACD5+B,EAAY6+B,SACe,cAA7B7+B,EAAY6+B,QAAQ/7B,MACpB9C,EAAY6+B,QAAQz2B,SAAWmH,EAAKnH,QACpCs2B,EAAW,GACf,KAAoB,cAAT57B,IACTyM,EAAK2M,GAAM5Q,EAAgCJ,UAAYlL,EAAY2+B,QACnEpvB,EAAKqvB,QAAS,GACf,OAAArvB,CACH,CAyBC,OAzBAhB,EAAA7K,EAAA,CAAA,CAAA8K,IAAA,kBAAA3G,MAED,SAAA9H,GAAmD,IAA9B++B,EAAO/+B,EAAVY,EAAeo+B,EAAOh/B,EAAVQ,EAM5B,OALAkC,KAAK8J,OAASuyB,EACdr8B,KAAK+J,OAASuyB,EACdt8B,KAAKgK,SAAWqyB,EAChBr8B,KAAKiK,SAAWqyB,EAETt8B,IACT,GAAC,CAAA+L,IAAA,aAAA3G,MAED,SAAAhH,GAA8C,IAA9Bi+B,EAAOj+B,EAAVF,EAAeo+B,EAAOl+B,EAAVN,EAMvB,OALAkC,KAAK8J,OAASuyB,EACdr8B,KAAK+J,OAASuyB,EACdt8B,KAAKgK,SAAWqyB,EAChBr8B,KAAKiK,SAAWqyB,EAETt8B,IACT,GAEA,CAAA+L,IAAA,iBAAA3G,MAGA,WACEpF,KAAK2sB,cAAcvN,gBACrB,KAACne,CAAA,EA7EuDwK,ICgFpD8wB,GAAwB,CAC5B99B,GAAI,sBACJ+W,OAAQ,CAAC,UAAW,YAAa,aAAc,WAC/C9W,QAgRF,SAAiBC,GACfA,EAAM49B,cAAgBA,GACtB59B,EAAMG,SAASF,QAAQ29B,cAAgBA,GAAcz9B,SACrDwG,EAAO3G,EAAMC,QAAQiV,eAAgB0oB,GAAclQ,MACrD,EAnREltB,UAAW,CACT,mBAwKJ,SAA4Bf,GAAkB,IAAfb,EAAWa,EAAXb,YAC7BA,EAAY6+B,QAAU,KACtB7+B,EAAY2+B,QAAU,CACxB,EA1KI,8BA4KJ,SAAoBxqB,GAAmE,IAAhErP,EAAIqP,EAAJrP,KAAM2kB,EAAWtV,EAAXsV,YAC3B,IAAK3kB,GAAQ2kB,EAAYlI,KACvB,OAGFkI,EAAYlI,KAAO,CAAEsG,SAAU9P,IAAUgU,QAAS,KACpD,EAjLI,oBA4LJ,SAA0BlqB,EAAsCT,GAC9D,IAAQpB,EAAwD6B,EAAxD7B,YAAasL,EAA2CzJ,EAA3CyJ,QAASa,EAAkCtK,EAAlCsK,MAAOmO,EAA2BzY,EAA3ByY,YAA2BzY,EAAd8f,WAE9B3hB,EAAYmC,gBAAiBnC,EAAYygB,kBACvDzgB,EAAYmC,eACd88B,GAAUp9B,GAGZwO,GACE,CACErQ,YAAAA,EACAsL,QAAAA,EACAa,MAAAA,EACAmO,YAAaA,EACbxX,KAAM,QAER1B,GAGN,EA9MI,oBAAqB,SAACS,EAAKT,IAgN/B,SAAyByV,EAEvBzV,GAaC,IAZD,IAFEpB,EAAW6W,EAAX7W,YAAasL,EAAOuL,EAAPvL,QAASa,EAAK0K,EAAL1K,MAAOmO,EAAWzD,EAAXyD,YAAaiP,EAAY1S,EAAZ0S,aAGtC2V,EAAQl/B,EAAY+L,SAASwd,GAAchI,KAC3C7Z,EAAOsG,EAAiBsM,GACxB4O,EAAY,CAChBlpB,YAAAA,EACAsL,QAAAA,EACAa,MAAAA,EACAmO,YAAAA,EACAxX,KAAM,OACN8sB,QAAS,GACTloB,KAAAA,EACAlJ,KAAM,MACP4S,IAAAA,EAEkB1J,EAAI9H,OAAAwR,IAAE,CAApB,IAAM5S,EAAQkJ,EAAI0J,GACrB8X,EAAU1qB,KAAOA,EAEjB4C,EAAMiP,KAAK,gCAAiC6Y,EAC9C,CAEA,IAAKA,EAAU0G,QAAQhwB,OAAQ,OAEL,IAA1B,IAAIu/B,EAAcpnB,IAAQxH,EAAA,EAAA6uB,EAELlW,EAAU0G,QAAOrf,EAAA6uB,EAAAx/B,OAAA2Q,IAAE,CAAnC,IACG8uB,EADSD,EAAA7uB,GACa+uB,UAAUr9B,QAAQo9B,aAE1CA,EAAeF,IACjBA,EAAcE,EAElB,CAEAH,EAAMrX,SAAWsX,EACjBD,EAAMnT,QAAUtQ,YAAW,WACzBpL,GACE,CACErQ,YAAAA,EACAsa,YAAAA,EACAhP,QAAAA,EACAa,MAAAA,EACArJ,KAAM,QAER1B,EAEH,GAAE+9B,EACL,CA/PMI,CAAiB19B,EAAKT,GACtBiP,GAAKxO,EAAKT,EACX,EACD,kBAAmB,SAACS,EAAKT,GACvB69B,GAAUp9B,GACVwO,GAAKxO,EAAKT,GA4PhB,SAAmB0V,EAEjB1V,GACA,IAFEpB,EAAW8W,EAAX9W,YAAasL,EAAOwL,EAAPxL,QAASa,EAAK2K,EAAL3K,MAAOmO,EAAWxD,EAAXwD,YAG1Bta,EAAYygB,iBACfpQ,GAAK,CAAErQ,YAAAA,EAAasa,YAAAA,EAAahP,QAAAA,EAASa,MAAAA,EAAOrJ,KAAM,OAAS1B,EAEpE,CAlQMo+B,CAAW39B,EAAKT,EACjB,EACD,sBAAuB,SAACS,EAAKT,GAC3B69B,GAAUp9B,GACVwO,GAAKxO,EAAKT,EACZ,GAEFsC,aAAAA,GACA2M,KAAAA,GACAovB,oBAAAA,GACAl+B,SAhCoC,CACpC89B,aAAc,IACdphB,WAAY,KACZE,UAAW,KACXjV,OAAQ,CAAEvI,EAAG,EAAGJ,EAAG,IA6BnBuuB,MAAO,CACLhqB,MAAM,EACNlE,MAAM,EACNiE,IAAI,EACJI,QAAQ,EACRy6B,KAAK,EACLC,WAAW,EACXpe,MAAM,IAIV,SAASlR,GACPxO,EAQAT,GAEA,IAAQpB,EAA8F6B,EAA9F7B,YAAasL,EAAiFzJ,EAAjFyJ,QAASa,EAAwEtK,EAAxEsK,MAAOmO,EAAiEzY,EAAjEyY,YAAaxX,EAAoDjB,EAApDiB,KAAI88B,EAAgD/9B,EAA9C+tB,QAAAA,OAAO,IAAAgQ,EAAGH,GAAoB59B,EAAKT,GAAMw+B,EAE3FhuB,EAAe,IAAIlO,GAAaZ,EAAMwI,EAASa,EAAOmO,EAAata,EAAaoB,EAAMma,OAE5Fna,EAAMiP,KAAK,oBAAqB,CAAEuB,aAAAA,IAYlC,IAVA,IAAMsX,EAAY,CAChBlpB,YAAAA,EACAsL,QAAAA,EACAa,MAAAA,EACAmO,YAAAA,EACAsV,QAAAA,EACA9sB,KAAAA,EACA8O,aAAAA,GAGO9C,EAAI,EAAGA,EAAI8gB,EAAQhwB,OAAQkP,IAAK,CACvC,IAAM1G,EAASwnB,EAAQ9gB,GAEvB,IAAK,IAAM5G,KAAQE,EAAOy3B,OAAS,CAAA,EAC/BjuB,EAAqB1J,GAAQE,EAAOy3B,MAAM33B,GAG9C,IAAMgB,EAASH,EAAYX,EAAOk3B,UAAWl3B,EAAO5J,MAUpD,GARAoT,EAAakuB,gBAAgB52B,GAC7B0I,EAAa0tB,UAAYl3B,EAAOk3B,UAChC1tB,EAAa3D,cAAgB7F,EAAO5J,KAEpC4J,EAAOk3B,UAAUjvB,KAAKuB,GAEtBA,EAAamuB,WAAW72B,GAGtB0I,EAAaxD,6BACZwD,EAAavD,oBACZS,EAAI,EAAI8gB,EAAQhwB,QAChBgwB,EAAQ9gB,EAAI,GAAGtQ,OAASoT,EAAa3D,cAEvC,KAEJ,CAIA,GAFA7M,EAAMiP,KAAK,sBAAuB6Y,GAErB,QAATpmB,EAAgB,CAGlB,IAAM+7B,EAAUjtB,EAAagtB,OACzBvuB,GACE,CACErQ,YAAAA,EACAsL,QAAAA,EACAa,MAAAA,EACAmO,YAAAA,EACAxX,KAAM,aAER1B,GAEFwQ,EAEJ5R,EAAY6+B,QAAUA,EACtB7+B,EAAY2+B,QAAUE,EAAQ3zB,SAChC,CAEA,OAAO0G,CACT,CAEA,SAAS6tB,GAAmB1/B,EAc1BqB,GACA,IAbEpB,EAAWD,EAAXC,YACAsL,EAAOvL,EAAPuL,QACAa,EAAKpM,EAALoM,MACAmO,EAAWva,EAAXua,YACAxX,EAAI/C,EAAJ+C,KAUIymB,EAAevpB,EAAY6pB,gBAAgBve,GAC3Cme,EAAczpB,EAAY+L,SAASwd,GAGzC,GACW,QAATzmB,IACC9C,EAAYygB,kBAETgJ,GAAeA,EAAYjB,aAAelO,GAE9C,MAAO,GAaR,IAVD,IAAM5S,EAAOsG,EAAiBsM,GACxB4O,EAAY,CAChBlpB,YAAAA,EACAsL,QAAAA,EACAa,MAAAA,EACAmO,YAAAA,EACAxX,KAAAA,EACA4E,KAAAA,EACAkoB,QAAS,GACTpxB,KAAM,MACPmL,IAAAA,EAEkBjC,EAAI9H,OAAA+J,IAAE,CAApB,IAAMnL,EAAQkJ,EAAIiC,GACrBuf,EAAU1qB,KAAOA,EAEjB4C,EAAMiP,KAAK,gCAAiC6Y,EAC9C,CASA,MAPa,SAATpmB,IACFomB,EAAU0G,QAAU1G,EAAU0G,QAAQxmB,QACpC,SAAChB,GAAM,IAAA43B,EAAAxM,EAAA,OACLprB,EAAOk3B,UAAUr9B,QAAQo9B,gBAAmD,OAAvCW,EAAKhgC,EAAY+L,SAASwd,KAAmB,OAANiK,EAAlCwM,EAAoCze,WAAF,EAAlCiS,EAA0C3L,SAAQ,KAI3FqB,EAAU0G,OACnB,CAeA,SAASqP,GAASroB,GAAgC,IAA7B5W,EAAW4W,EAAX5W,YAAaupB,EAAY3S,EAAZ2S,aAC1BhI,EAAOvhB,EAAY+L,SAASwd,GAAchI,KAE5CA,GAAQA,EAAKwK,UACfrQ,aAAa6F,EAAKwK,SAClBxK,EAAKwK,QAAU,KAEnB,mDChNA,SAASkU,GAAa9rB,GAAgD,IAA7CnU,EAAWmU,EAAXnU,YAGnBA,EAAYkgC,qBACdC,cAAcngC,EAAYkgC,oBAC1BlgC,EAAYkgC,mBAAqB,KAErC,CAEA,IAAME,GAAqB,CACzBl/B,GAAI,4BACJC,QAtDF,SAAiBC,GACfA,EAAMoT,UAAUiN,IAEhB,IAAQud,EAAkB59B,EAAlB49B,cAGRA,EAAcz9B,SAAS8+B,mBAAqB,EAC5CrB,EAAclQ,MAAMwR,WAAal/B,EAAMC,QAAQiV,eAAegqB,YAAa,CAC7E,EA+CE1+B,UAAW,CAAC,OAAQ,KAAM,SAAU,UAAUiT,QAC5C,SAACC,EAAKyrB,GAEJ,OADEzrB,mBAAG9K,OAA0Bu2B,IAAgBN,GACxCnrB,CACT,GACA,CACE,oBAnDN,SAAc/U,GAAwD,IAArD6R,EAAY7R,EAAZ6R,aACW,SAAtBA,EAAa9O,OAEjB8O,EAAa4uB,OAAS5uB,EAAa4uB,OAAS,GAAK,EACnD,EAgDM,sBA9CN,SAAgB3/B,EAEdO,GACA,IAFEpB,EAAWa,EAAXb,YAAa4R,EAAY/Q,EAAZ+Q,aAAc0I,EAAWzZ,EAAXyZ,YAAasV,EAAO/uB,EAAP+uB,QAG1C,GAA0B,SAAtBhe,EAAa9O,MAAoB8sB,EAAQhwB,OAA7C,CAGA,IAAM8+B,EAAW9O,EAAQ,GAAG0P,UAAUr9B,QAAQo+B,mBAG1C3B,GAAY,IAGhB1+B,EAAYkgC,mBAAqBzkB,YAAW,WAC1Cra,EAAM49B,cAAc3uB,KAClB,CACErQ,YAAAA,EACAsa,YAAAA,EACAxX,KAAM,OACNwI,QAASsG,EACTzF,MAAOyF,GAETxQ,EAEH,GAAEs9B,GApBkD,CAqBvD,KA0BA+B,GAAeL,GCtDf,IAAM1I,GAAiB,CACrBx2B,GAAI,qCACJC,QA3BF,SAAiBC,GACf,IAAQE,EAAiBF,EAAjBE,aAERA,EAAaE,UAAUw9B,cAAgB,SAErC/8B,GAIA,OAFA8F,EAAOtF,KAAKqN,OAAO7N,QAASA,GAErBQ,MAGT,IAAMi+B,EAAqBp/B,EAAaE,UAAU0c,kBAElD5c,EAAaE,UAAU0c,kBAAoB,SAAUkX,EAAY/e,GAC/D,IAAM6Q,EAAMwZ,EAAmBlxB,KAAK/M,KAAM2yB,EAAY/e,GAMtD,OAJI6Q,IAAQzkB,OACVA,KAAKqN,OAAO7N,QAAQmzB,GAAc/e,GAG7B6Q,EAEX,EAKEtlB,UAAW,CACT,gCAAiC,SAAA7B,EAAuCqB,GAAU,IAA9CwuB,EAAO7vB,EAAP6vB,QAASpxB,EAAIuB,EAAJvB,KAAMsE,EAAI/C,EAAJ+C,KAAMwX,EAAWva,EAAXua,YACvDlZ,EAAM4P,cAAcgO,aAAaxgB,GAAM,SAACsD,GACtC,IAAMw9B,EAAYx9B,EAAagO,OACzB7N,EAAUq9B,EAAUr9B,QAGxBq9B,EAAUxQ,MAAMhsB,IAChBw8B,EAAUxQ,MAAMhsB,GAAMlD,QACtBkC,EAAauc,gBAAgBpc,EAASzD,EAAM8b,IAE5CsV,EAAQjoB,KAAK,CACXnJ,KAAAA,EACA8gC,UAAAA,EACAO,MAAO,CAAE/9B,aAAAA,IAGf,GACD,EAED,mBAAoB,SAAAjB,GAAsB,IAAnBiB,EAAYjB,EAAZiB,aACrBA,EAAagO,OAAOzH,QAAU,SAAUlJ,GACtC,OAAO2C,EAAauG,QAAQlJ,GAE/B,EAED,mBAAoB,SAAAgV,EAA4B/S,GAAU,IAAnCU,EAAYqS,EAAZrS,aAAcG,EAAOkS,EAAPlS,QACnC8F,EAAOjG,EAAagO,OAAO7N,QAASb,EAAM49B,cAAcz9B,UACxDwG,EAAOjG,EAAagO,OAAO7N,QAASA,EAAQ+8B,eAAiB,CAAA,EAC/D,IAIJ2B,GAAejJ,GChETA,GAAiB,CACrBx2B,GAAI,iBACJC,QAAO,SAACC,GACNA,EAAMoT,UAAUwqB,IAChB59B,EAAMoT,UAAU4rB,IAChBh/B,EAAMoT,UAAUmsB,GAClB,GAGF3B,GAAetH,GCmJf,IAAMkJ,GAAiB,CACrB1/B,GAAI,SACJC,QAtHF,SAAiBC,GACf,IAAQE,EAAiBF,EAAjBE,aAERF,EAAMC,QAAQuhB,OAAOge,QAAS,EAE9Bt/B,EAAaE,UAAUo/B,OAAS,SAAUt+B,GACxC,OAIJ,SACER,EACAQ,EACAlB,GAkDC,IAhDD,IAAMgR,EAAWtQ,EAAawP,iBAGxBuvB,EAAWz/B,EAAMnD,OAAe4iC,QAChCC,EAAwCD,EAAU,GAAK,KAAIr2B,EAAAA,WAE5D,IAAMrL,EAAWiT,EAAQzI,GACtBhB,EAAO7G,EAAauG,QAAQlJ,GAElC,IAAKwJ,EAAM,OAAA,EAIX,IAQIo4B,EAREC,EAAqB7wB,GAAS/O,EAAM0e,aAAa7O,MAAM,SAACjR,GAC5D,OACEA,EAAY2c,eACZ3c,EAAY8B,eAAiBA,GAC7B9B,EAAYb,UAAYA,GACxBa,EAAYC,SAASC,OAASoC,EAAOpC,IAEzC,IAGA,GAAI8gC,EACFA,EAAmBpgC,OAEfkgC,IACFC,EACEC,EAAmBC,gBACnB,IAAIJ,GAAQ,SAACK,GACXF,EAAmBG,eAAiBD,CACtC,SAEC,CACL,IAAME,EAAOx4B,EAAWD,GAOlBwD,ExD4HL,SAAuB/L,GA0C5B,MAzCc,CACZA,OAAAA,EACA,QAAIE,GACF,OAAOmC,KAAKrC,OAAOE,IACpB,EACD,UAAIG,GACF,OAAOgC,KAAKrC,OAAOK,MACpB,EACD,aAAIyK,GACF,OAAOzI,KAAKrC,OAAO8K,SACpB,EACD,SAAIqB,GACF,OAAO9J,KAAKrC,OAAOE,KAAKK,CACzB,EACD,SAAI6L,GACF,OAAO/J,KAAKrC,OAAOE,KAAKC,CACzB,EACD,WAAIkM,GACF,OAAOhK,KAAKrC,OAAOK,OAAOE,CAC3B,EACD,WAAI+L,GACF,OAAOjK,KAAKrC,OAAOK,OAAOF,CAC3B,EACD,aAAIqL,GACF,OAAOnJ,KAAKrC,OAAOwL,SACpB,EACD,UAAIxD,GACF,OAAO3F,KAAKrC,OAAOgI,MACpB,EACD,QAAItF,GACF,OAAOL,KAAKrC,OAAO0C,IACpB,EACD,eAAIV,GACF,OAAOK,KAAKrC,OAAOgC,WACpB,EACD,WAAIL,GACF,OAAOU,KAAKrC,OAAO2B,OACpB,EACD8f,eAAc,WAAI,EAItB,CwDvKoB/L,CANC,CACbxV,KAAM,CAAEK,EAAGygC,EAAKzgC,EAAGJ,EAAG6gC,EAAK7gC,GAC3BE,OAAQ,CAAEE,EAAGygC,EAAKzgC,EAAGJ,EAAG6gC,EAAK7gC,GAC7B2K,UAAW9J,EAAMma,QAInBwlB,EAWN,SACE3/B,EACAU,EACA3C,EACAmD,EACA6J,GAEA,IAAMnM,EAAcoB,EAAM0e,aAAakT,IAAI,CAAE5wB,YAAa,WACpD8mB,EAAY,CAChBlpB,YAAAA,EACAmM,MAAAA,EACAb,QAASa,EACTmO,YAAanb,EACb+X,MAAO,UAGTlX,EAAY8B,aAAeA,EAC3B9B,EAAYb,QAAUA,EACtBa,EAAY8X,UAAY3L,EACxBnM,EAAYwpB,cAAcrd,EAAOA,EAAOhN,GAAS,GACjD2W,GAA2B9V,EAAYI,OAAOY,OAE9C0c,GAAW1d,EAAYC,SAAUqC,GACjCtC,EAAY0pB,SAASR,GAErB,IAAAnpB,EAAoBqB,EAAMnD,OAAlB4iC,EAAO9gC,EAAP8gC,QACFE,EAAgBF,EAClB,IAAIA,GAAmB,SAACK,GACtBlhC,EAAYmhC,eAAiBD,CAC9B,SACDpjC,EAEJkC,EAAYihC,eAAiBF,EAC7B/gC,EAAYQ,MAAM8B,EAAQR,EAAc3C,GAEpCa,EAAYsgB,cACdtgB,EAAYY,KAAKsoB,GACjBlpB,EAAY+pB,IAAI5d,KAEhBnM,EAAYgc,OACZhc,EAAYmhC,kBAKd,OAFAnhC,EAAYgqB,cAAc7d,EAAOA,GAE1B40B,CACT,CAzDsBM,CAAejgC,EAAOU,EAAc3C,EAASmD,EAAQ6J,EACvE,CAEI20B,GACFA,EAASn5B,KAAKo5B,IAEjBp3B,IAAAA,EA1CqByI,EAAQxS,SAAA4K,IAAAb,KA4C9B,OAAOm3B,GAAYD,EAAQ5C,IAAI6C,GAAUQ,MAAK,WAAA,OAAMx/B,IACtD,CA5DWy/B,CAAS9+B,KAAMH,EAAQlB,GAElC,EA+GEQ,UAAW,CAET,oBAAqB,SAAAf,EAAkBO,GAAU,IAAzBpB,EAAWa,EAAXb,YACU,WAA5BA,EAAYoC,cACVpC,EAAYmhC,gBACdnhC,EAAYmhC,iBtD5KA,SAAIxhC,EAAYyI,GAAczI,EAAME,OAAOF,EAAM+K,QAAQtC,GAAS,EAAE,CsD+KlF+H,CAAW/O,EAAM0e,aAAa7O,KAAMjR,GAExC,IAIJwhC,GAAeZ,GCpLf,GCqBAtsB,GAASmjB,IAAIzV,IAEb1N,GAASmjB,IAAI/M,IAGbpW,GAASmjB,IAAIuH,IAGb1qB,GAASmjB,IAAIxJ,IAGb3Z,GAASmjB,IAAIjU,IAGblP,GAASmjB,IAAIjY,IAGblL,GAASmjB,IAAIp2B,IAGbiT,GAASmjB,IAAI9b,IAGbrH,GAASmjB,IAAImJ,IAQH7H,GAAS0I,QAAUntB,GDpDP,YAAL3V,oBAAN+nB,OAAM/nB,YAAAA,EAAN+nB,UAAyBA,OAClC,IACEA,OAAOgb,QAAUptB,EACnB,CAAE,MAAAqtB,GAAO,QAGD5I,GAAS0I,QAAUntB"}