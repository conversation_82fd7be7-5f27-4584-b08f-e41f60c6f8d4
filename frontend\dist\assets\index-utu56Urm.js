import{_ as a,x as e,r as s,c as l,j as t,ad as i,z as d,B as c,E as n,C as o,b as u,d as r,t as v,F as p,e as m,$ as h,f,w as g,p as _,ag as k,a as y,u as w,o as C,a2 as x,ah as b,ai as z,aj as T,k as S,h as L,i as I}from"./index-CG5lHOPO.js";import{u as P}from"./gamification-Dm7mCEPf.js";import"./gamification-2FBMrBgR.js";const j={class:"dashboard-container"},$={class:"debug-info",style:{"margin-bottom":"10px",padding:"8px","background-color":"#f5f7fa","border-radius":"4px","font-size":"12px"}},F={class:"welcome-section"},N={class:"welcome-info"},B={class:"welcome-title"},D={class:"welcome-subtitle"},O={key:0,class:"user-game-info"},E={class:"level-badge"},H={class:"score-info"},V={class:"score-value"},q={class:"level-progress-text"},A={class:"card-header"},G={class:"card-content"},J={class:"card-value"},K={class:"card-header"},M={class:"card-content"},Q={class:"card-value"},R={class:"card-header"},U={class:"card-content"},W={class:"card-value"},X={class:"card-actions"},Y={class:"card-header"},Z={class:"card-content"},aa={class:"card-value"},ea={class:"card-header"},sa={class:"game-metrics-content"},la={class:"metric-item"},ta={class:"metric-icon task-completion"},ia={class:"metric-info"},da={class:"metric-value"},ca={class:"metric-item"},na={class:"metric-icon task-streak"},oa={class:"metric-info"},ua={class:"metric-value"},ra={class:"metric-item"},va={class:"metric-icon task-items"},pa={class:"metric-info"},ma={class:"metric-value"},ha={class:"card-header"},fa={class:"chart-placeholder"},ga={key:0,class:"chart-loading"},_a={key:1,class:"chart-empty"},ka={class:"card-header"},ya={class:"chart-placeholder"},wa={key:0,class:"chart-loading"},Ca={key:1,class:"chart-empty"},xa={class:"card-header"},ba={key:0,class:"task-list"},za={key:1,class:"loading-placeholder"},Ta={class:"card-header"},Sa={key:0,class:"notification-list"},La={class:"notification-content"},Ia={class:"notification-title"},Pa={class:"notification-time"},ja={key:1,class:"loading-placeholder"},$a={key:1,class:"simple-dashboard"},Fa={class:"simple-card-content"},Na={class:"simple-value"},Ba=a({__name:"index",setup(a){const Ba=w(),Da=c(),Oa=e(),Ea=P(),Ha=s(!0),Va=l((()=>Da.path)),qa=l((()=>Va.value.includes("/main/dashboard")||"/main"===Va.value));t(Da,(a=>{}),{immediate:!0});const Aa=i([{title:"资产总数",value:"123"},{title:"待处理故障",value:"5"},{title:"待办任务",value:"8"}]),Ga=()=>{Ba.push("/main/dashboard")},Ja=i({assetCount:0,pendingFaultCount:0,pendingTaskCount:0,pendingPurchaseCount:0}),Ka=s([]),Ma=s([]),Qa=l((()=>{const a=(new Date).getHours();return a<6?"凌晨好":a<9?"早上好":a<12?"上午好":a<14?"中午好":a<18?"下午好":"晚上好"})),Ra=(new Date).toISOString().split("T")[0],Ua=()=>{Ba.push("/task/list")},Wa=l((()=>({level:Ea.level,score:Ea.score,levelProgress:Ea.currentLevelProgress,nextLevelScore:Ea.pointsToNextLevel}))),Xa=s({completedCount:0,completionStreak:0,itemCount:0}),Ya=()=>{Ba.push("/main/tasks/board")},Za=()=>{Ba.push("/main/tasks/board")},ae=()=>{Ba.push("/main/tasks/board")};return d((()=>{var a;(async()=>{var a;Ha.value=!0;try{const e=null==(a=Oa.userInfo)?void 0:a.id,s=[];s.push(k.getTaskList({status:"in-progress",pageSize:1}).catch((a=>null))),s.push(k.getTaskList({status:"unstarted",pageSize:1}).catch((a=>null))),e?s.push(k.getTaskList({assigneeId:e,pageSize:5,sortBy:"creationTimestamp",sortOrder:"desc"}).catch((a=>null))):s.push(Promise.resolve(null)),s.push(k.getTaskList({status:"completed",pageSize:1}).catch((a=>null)));const[l,t,i,d]=await Promise.all(s);let c=0;(null==l?void 0:l.totalCount)&&(c+=l.totalCount),(null==l?void 0:l.total)&&(c+=l.total),(null==t?void 0:t.totalCount)&&(c+=t.totalCount),(null==t?void 0:t.total)&&(c+=t.total),Ja.pendingTaskCount=c,(null==i?void 0:i.items)?Ka.value=i.items:(null==i?void 0:i.list)?Ka.value=i.list:Ka.value=[];let o=0;(null==d?void 0:d.totalCount)&&(o+=d.totalCount),(null==d?void 0:d.total)&&(o+=d.total),Xa.value.completedCount=o,Xa.value.completionStreak=0,Xa.value.itemCount=Ea.inventory.length,qa.value&&n.success("仪表盘数据加载完成")}catch(e){n.error("仪表盘数据加载失败"),Ja.pendingTaskCount=0,Ka.value=[],Xa.value.completedCount=0}finally{Ha.value=!1}})(),n({message:`${Qa.value}，${(null==(a=Oa.userInfo)?void 0:a.name)||"用户"}！欢迎使用${o.appName||"IT资产管理系统"}`,type:"success",duration:3e3})})),(a,e)=>{var s;const l=y("el-tag"),t=y("el-tooltip"),i=y("el-icon"),d=y("el-progress"),c=y("el-card"),n=y("el-col"),k=y("el-button"),w=y("el-row"),P=y("el-skeleton"),Ba=y("el-empty"),Da=y("el-table-column"),Ea=y("el-table"),ee=y("el-badge"),se=y("el-result");return C(),u("div",j,[r("div",$,[r("p",null,"当前路由路径: "+v(Va.value),1),r("p",null,"匹配结果: "+v(qa.value?"主仪表盘":"简易仪表盘"),1)]),qa.value?(C(),u(p,{key:0},[r("div",F,[r("div",N,[r("h2",B,v(Qa.value)+"，"+v((null==(s=f(Oa).userInfo)?void 0:s.name)||"用户"),1),r("p",D,"今天是 "+v(f(Ra))+"，欢迎使用"+v(f(o).appName||"IT资产管理系统"),1)]),Wa.value?(C(),u("div",O,[r("div",E,[m(t,{content:"当前等级"},{default:g((()=>[m(l,{size:"large",effect:"dark",class:"level-tag"},{default:g((()=>[_("LV."+v(Wa.value.level),1)])),_:1})])),_:1})]),r("div",H,[r("div",V,[m(i,null,{default:g((()=>[m(f(x))])),_:1}),_(" "+v(Wa.value.score)+" 积分 ",1)]),m(d,{percentage:Wa.value.levelProgress,"stroke-width":8,"show-text":!1,class:"level-progress"},null,8,["percentage"]),r("div",q," 距离下一级还需 "+v(Wa.value.nextLevelScore)+" 积分 ",1)])])):h("",!0)]),m(w,{gutter:20,class:"data-overview"},{default:g((()=>[m(n,{xs:24,sm:12,md:6},{default:g((()=>[m(c,{shadow:"hover",class:"data-card"},{header:g((()=>[r("div",A,[e[1]||(e[1]=r("span",null,"资产总数",-1)),m(l,{size:"small"},{default:g((()=>e[0]||(e[0]=[_("总计")]))),_:1})])])),default:g((()=>[r("div",G,[r("div",J,v(Ja.assetCount),1)])])),_:1})])),_:1}),m(n,{xs:24,sm:12,md:6},{default:g((()=>[m(c,{shadow:"hover",class:"data-card"},{header:g((()=>[r("div",K,[e[3]||(e[3]=r("span",null,"待处理故障",-1)),m(l,{size:"small",type:"warning"},{default:g((()=>e[2]||(e[2]=[_("待处理")]))),_:1})])])),default:g((()=>[r("div",M,[r("div",Q,v(Ja.pendingFaultCount),1)])])),_:1})])),_:1}),m(n,{xs:24,sm:12,md:6},{default:g((()=>[m(c,{shadow:"hover",class:"data-card task-card-highlight"},{header:g((()=>[r("div",R,[e[5]||(e[5]=r("span",null,"待办任务",-1)),m(l,{size:"small",type:"info"},{default:g((()=>e[4]||(e[4]=[_("进行中")]))),_:1})])])),default:g((()=>[r("div",U,[r("div",W,v(Ja.pendingTaskCount),1),r("div",X,[m(k,{type:"text",onClick:Ya},{default:g((()=>e[6]||(e[6]=[_("查看任务看板")]))),_:1})])])])),_:1})])),_:1}),m(n,{xs:24,sm:12,md:6},{default:g((()=>[m(c,{shadow:"hover",class:"data-card"},{header:g((()=>[r("div",Y,[e[8]||(e[8]=r("span",null,"待审批采购",-1)),m(l,{size:"small",type:"success"},{default:g((()=>e[7]||(e[7]=[_("审批中")]))),_:1})])])),default:g((()=>[r("div",Z,[r("div",aa,v(Ja.pendingPurchaseCount),1)])])),_:1})])),_:1})])),_:1}),m(w,{gutter:20,class:"game-metrics-section"},{default:g((()=>[m(n,{xs:24},{default:g((()=>[m(c,{shadow:"hover",class:"game-metrics-card"},{header:g((()=>[r("div",ea,[e[10]||(e[10]=r("span",null,"任务成就指标",-1)),m(k,{type:"primary",link:"",onClick:Za},{default:g((()=>e[9]||(e[9]=[_("查看排行榜")]))),_:1})])])),default:g((()=>[r("div",sa,[m(w,{gutter:30},{default:g((()=>[m(n,{xs:24,sm:8},{default:g((()=>[r("div",la,[r("div",ta,[m(i,null,{default:g((()=>[m(f(b))])),_:1})]),r("div",ia,[r("div",da,v(Xa.value.completedCount||0),1),e[11]||(e[11]=r("div",{class:"metric-label"},"已完成任务",-1))])])])),_:1}),m(n,{xs:24,sm:8},{default:g((()=>[r("div",ca,[r("div",na,[m(i,null,{default:g((()=>[m(f(z))])),_:1})]),r("div",oa,[r("div",ua,v(Xa.value.completionStreak||0),1),e[12]||(e[12]=r("div",{class:"metric-label"},"连续完成天数",-1))])])])),_:1}),m(n,{xs:24,sm:8},{default:g((()=>[r("div",ra,[r("div",va,[m(i,null,{default:g((()=>[m(f(T))])),_:1})]),r("div",pa,[r("div",ma,v(Xa.value.itemCount||0),1),e[14]||(e[14]=r("div",{class:"metric-label"},"拥有道具数",-1)),Xa.value.itemCount>0?(C(),S(k,{key:0,type:"success",size:"small",onClick:ae},{default:g((()=>e[13]||(e[13]=[_("查看道具")]))),_:1})):h("",!0)])])])),_:1})])),_:1})])])),_:1})])),_:1})])),_:1}),m(w,{gutter:20,class:"chart-section"},{default:g((()=>[m(n,{xs:24,md:12},{default:g((()=>[m(c,{shadow:"hover",class:"chart-card"},{header:g((()=>[r("div",ha,[e[16]||(e[16]=r("span",null,"资产分类统计",-1)),m(k,{type:"link"},{default:g((()=>e[15]||(e[15]=[_("详情")]))),_:1})])])),default:g((()=>[r("div",fa,[Ha.value?(C(),u("div",ga,[m(P,{animated:"",rows:5})])):(C(),u("div",_a,[m(Ba,{description:"资产分类数据加载中"})]))])])),_:1})])),_:1}),m(n,{xs:24,md:12},{default:g((()=>[m(c,{shadow:"hover",class:"chart-card"},{header:g((()=>[r("div",ka,[e[18]||(e[18]=r("span",null,"故障趋势分析",-1)),m(k,{type:"link"},{default:g((()=>e[17]||(e[17]=[_("详情")]))),_:1})])])),default:g((()=>[r("div",ya,[Ha.value?(C(),u("div",wa,[m(P,{animated:"",rows:5})])):(C(),u("div",Ca,[m(Ba,{description:"故障趋势数据加载中"})]))])])),_:1})])),_:1})])),_:1}),m(w,{gutter:20,class:"task-notification-section"},{default:g((()=>[m(n,{xs:24,lg:16},{default:g((()=>[m(c,{shadow:"hover",class:"task-card"},{header:g((()=>[r("div",xa,[e[20]||(e[20]=r("span",null,"我的任务",-1)),m(k,{type:"link",onClick:Ua},{default:g((()=>e[19]||(e[19]=[_("查看更多")]))),_:1})])])),default:g((()=>[!Ha.value&&Ka.value.length>0?(C(),u("div",ba,[m(Ea,{data:Ka.value,style:{width:"100%"},"show-header":!1},{default:g((()=>[m(Da,{width:"40"},{default:g((a=>{return[m(l,{type:(e=a.row.status,{unstarted:"info","in-progress":"primary",completed:"success"}[e]||"info"),size:"small",effect:"plain"},null,8,["type"])];var e})),_:1}),m(Da,{prop:"title",label:"任务标题"}),m(Da,{prop:"deadline",label:"截止日期",width:"120"}),m(Da,{width:"80"},{default:g((()=>[m(k,{type:"text"},{default:g((()=>e[21]||(e[21]=[_("处理")]))),_:1})])),_:1})])),_:1},8,["data"])])):Ha.value?(C(),u("div",za,[m(P,{animated:"",rows:3})])):(C(),S(Ba,{key:2,description:"暂无任务"}))])),_:1})])),_:1}),m(n,{xs:24,lg:8},{default:g((()=>[m(c,{shadow:"hover",class:"notification-card"},{header:g((()=>[r("div",Ta,[e[23]||(e[23]=r("span",null,"系统通知",-1)),m(k,{type:"link"},{default:g((()=>e[22]||(e[22]=[_("全部已读")]))),_:1})])])),default:g((()=>[!Ha.value&&Ma.value.length>0?(C(),u("div",Sa,[(C(!0),u(p,null,L(Ma.value,((a,e)=>(C(),u("div",{key:e,class:I(["notification-item",{"is-unread":!a.read}])},[m(ee,{"is-dot":"",hidden:a.read,class:"notification-badge"},{default:g((()=>[r("div",La,[r("p",Ia,v(a.title),1),r("p",Pa,v(a.time),1)])])),_:2},1032,["hidden"])],2)))),128))])):Ha.value?(C(),u("div",ja,[m(P,{animated:"",rows:5})])):(C(),S(Ba,{key:2,description:"暂无通知"}))])),_:1})])),_:1})])),_:1})],64)):(C(),u("div",$a,[m(se,{icon:"success",title:"仪表盘加载成功","sub-title":"您正在查看简化版仪表盘，缺少完整菜单结构"},{extra:g((()=>[m(k,{type:"primary",onClick:Ga},{default:g((()=>e[24]||(e[24]=[_("进入完整仪表盘")]))),_:1})])),_:1}),m(w,{gutter:20,class:"simple-data-cards"},{default:g((()=>[(C(!0),u(p,null,L(Aa,((a,e)=>(C(),S(n,{span:8,key:e},{default:g((()=>[m(c,{shadow:"hover"},{default:g((()=>[r("div",Fa,[r("h3",null,v(a.title),1),r("p",Na,v(a.value),1)])])),_:2},1024)])),_:2},1024)))),128))])),_:1})]))])}}},[["__scopeId","data-v-5b1f5a1b"]]);export{Ba as default};
