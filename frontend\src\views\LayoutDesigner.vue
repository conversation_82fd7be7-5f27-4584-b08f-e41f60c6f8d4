<template>
  <div class="layout-designer-page">
    <FactoryLayoutDesigner @layout-saved="handleLayoutSaved" />
  </div>
</template>

<script setup>
import { ElMessage } from 'element-plus'
import FactoryLayoutDesigner from '@/components/FactoryLayoutDesigner.vue'

// 处理布局保存
const handleLayoutSaved = (layoutConfig) => {
  console.log('布局配置已保存:', layoutConfig)
  
  // 保存到localStorage
  localStorage.setItem('factoryLayoutConfig', JSON.stringify(layoutConfig))
  
  ElMessage.success(`布局配置已保存！总工位数: ${layoutConfig.totalWorkstations}`)
}
</script>

<style scoped>
.layout-designer-page {
  height: 100vh;
  overflow: hidden;
}
</style>
