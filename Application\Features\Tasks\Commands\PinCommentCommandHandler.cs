// File: Application/Features/Tasks/Commands/PinCommentCommandHandler.cs
// Description: 置顶评论命令处理器

using System;
using System.Threading;
using System.Threading.Tasks;
using MediatR;
using Microsoft.Extensions.Logging;
using ItAssetsSystem.Application.Common.Dtos;
using ItAssetsSystem.Application.Features.Tasks.Dtos;
using ItAssetsSystem.Core.Abstractions;
using ItAssetsSystem.Domain.Entities.Tasks;

namespace ItAssetsSystem.Application.Features.Tasks.Commands
{
    /// <summary>
    /// 置顶评论命令处理器
    /// </summary>
    public class PinCommentCommandHandler : IRequestHandler<PinCommentCommand, ApiResponse<CommentDto>>
    {
        private readonly ITaskRepository _taskRepository;
        private readonly ICoreDataQueryService _coreDataQueryService;
        private readonly ILogger<PinCommentCommandHandler> _logger;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="taskRepository">任务仓储</param>
        /// <param name="coreDataQueryService">核心数据查询服务</param>
        /// <param name="logger">日志记录器</param>
        public PinCommentCommandHandler(
            ITaskRepository taskRepository,
            ICoreDataQueryService coreDataQueryService,
            ILogger<PinCommentCommandHandler> logger)
        {
            _taskRepository = taskRepository;
            _coreDataQueryService = coreDataQueryService;
            _logger = logger;
        }

        /// <summary>
        /// 处理置顶评论命令
        /// </summary>
        /// <param name="request">置顶评论命令</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>评论DTO</returns>
        public async Task<ApiResponse<CommentDto>> Handle(PinCommentCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("开始处理置顶评论命令，评论ID: {CommentId}, 任务ID: {TaskId}, 置顶状态: {IsPinned}, 用户ID: {UserId}",
                    request.CommentId, request.TaskId, request.IsPinned, request.CurrentUserId);

                // 验证评论是否存在
                var existingComment = await _taskRepository.GetCommentByIdAsync(request.CommentId);
                if (existingComment == null)
                {
                    _logger.LogWarning("评论不存在，评论ID: {CommentId}", request.CommentId);
                    return ApiResponse<CommentDto>.CreateFail("评论不存在");
                }

                // 验证评论是否属于指定任务
                if (existingComment.TaskId != request.TaskId)
                {
                    _logger.LogWarning("评论不属于指定任务，评论ID: {CommentId}, 任务ID: {TaskId}", request.CommentId, request.TaskId);
                    return ApiResponse<CommentDto>.CreateFail("评论不属于指定任务");
                }

                // 验证任务是否存在
                var task = await _taskRepository.GetTaskByIdAsync(request.TaskId);
                if (task == null)
                {
                    _logger.LogWarning("任务不存在，任务ID: {TaskId}", request.TaskId);
                    return ApiResponse<CommentDto>.CreateFail("任务不存在");
                }

                // 验证用户权限（只有任务负责人、创建者或管理员可以置顶评论）
                if (task.AssigneeUserId != request.CurrentUserId &&
                    task.CreatorUserId != request.CurrentUserId)
                {
                    _logger.LogWarning("用户无权限置顶评论，用户ID: {UserId}, 任务ID: {TaskId}", request.CurrentUserId, request.TaskId);
                    return ApiResponse<CommentDto>.CreateFail("无权限置顶评论");
                }

                // 更新评论置顶状态
                var updatedComment = new Comment
                {
                    CommentId = existingComment.CommentId,
                    TaskId = existingComment.TaskId,
                    UserId = existingComment.UserId,
                    ParentCommentId = existingComment.ParentCommentId,
                    Content = existingComment.Content,
                    IsPinned = request.IsPinned,
                    IsEdited = existingComment.IsEdited,
                    CreationTimestamp = existingComment.CreationTimestamp,
                    LastUpdatedTimestamp = DateTime.Now
                };

                // 保存更新
                var result = await _taskRepository.UpdateCommentAsync(updatedComment);
                if (!result)
                {
                    _logger.LogError("更新评论置顶状态失败，评论ID: {CommentId}", request.CommentId);
                    return ApiResponse<CommentDto>.CreateFail("更新评论置顶状态失败");
                }

                // 获取用户信息
                var user = await _coreDataQueryService.GetUserAsync(existingComment.UserId);

                // 构建返回的评论DTO
                var commentDto = new CommentDto
                {
                    CommentId = updatedComment.CommentId,
                    TaskId = updatedComment.TaskId,
                    Content = updatedComment.Content,
                    UserId = updatedComment.UserId,
                    UserName = user?.Name ?? "未知用户",
                    UserAvatarUrl = user?.AvatarUrl ?? "",
                    CreationTimestamp = updatedComment.CreationTimestamp,
                    LastUpdatedTimestamp = updatedComment.LastUpdatedTimestamp,
                    ParentCommentId = updatedComment.ParentCommentId,
                    IsPinned = updatedComment.IsPinned,
                    IsEdited = updatedComment.IsEdited
                };

                _logger.LogInformation("成功处理置顶评论命令，评论ID: {CommentId}", request.CommentId);
                return ApiResponse<CommentDto>.CreateSuccess(commentDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理置顶评论命令时发生错误，评论ID: {CommentId}", request.CommentId);
                return ApiResponse<CommentDto>.CreateFail($"处理置顶评论时发生错误: {ex.Message}");
            }
        }
    }
}
