#!/usr/bin/env python3
"""
IT Assets Management System 前端分析工具
"""
import os
import sys
import json
from collections import defaultdict
from datetime import datetime
from typing import Optional, List, Dict, Any

# --- 项目元数据 ---
PROJECT_METADATA = {
    "project_name": "IT Assets Management System - Frontend",
    "repository_url": "https://github.com/yourorg/it-assets-frontend",
    "last_updated": "2025-04-15",
    "technology_stack": [
        "Vue 3", "Vite", "TypeScript", "Pinia",
        "Vue Router", "Tailwind CSS", "Axios"
    ]
}

# --- 配置 ---
FRONTEND_TARGET_DIR = 'frontend'
OUTPUT_FILENAME = 'FRONTEND_STRUCTURE_ANALYSIS.md'
WORKSPACE_ROOT = '.' 

# --- 忽略规则 ---
IGNORE_DIRS = [
    '.git', 'bin', 'obj', '.idea', '.vscode', 'node_modules',
    '__pycache__', '.pytest_cache', 'dist', '.pnpm', '.cursor',
    'docs', 'coverage', '.history', '.nuxt', '.next', 'build',
    '.svelte-kit', 'public','.astro'
]

IGNORE_FILE_PATTERNS = [
    '*.log', '*.map', '*.tmp', '*.bak', '*.swp', '.DS_Store',
    '*.min.*', '*.d.ts', 'package-lock.json', 'yarn.lock',
    'pnpm-lock.yaml', '*.ico', '*.png', '*.jpg', '*.jpeg',
    '*.gif', '*.svg', '*.woff', '*.woff2', '*.ttf', '*.otf'
]

# --- 关键文件识别 ---
KEY_FILES = {
    "package.json": "项目配置和依赖",
    "vite.config.js": "Vite构建配置",
    "vite.config.ts": "Vite构建配置(TypeScript)",
    "vue.config.js": "Vue CLI配置",
    "tsconfig.json": "TypeScript配置",
    "main.js": "应用入口(JavaScript)",
    "main.ts": "应用入口(TypeScript)",
    "App.vue": "根组件",
    "router/index.js": "路由配置",
    "router/index.ts": "路由配置(TypeScript)",
    "store/index.js": "状态管理(Pinia/Vuex)",
    "store/index.ts": "状态管理(TypeScript)",
    ".env": "环境变量",
    ".env.*": "环境变量配置",
    "Dockerfile": "容器化配置"
}

# --- 架构概览 ---
VUE_ARCHITECTURE_DIAGRAM = """## Vue项目架构概览

```plaintext
frontend/
├── public/               - 公共静态资源
├── src/                  - 源代码目录
│   ├── assets/           - 静态资源 (图片、字体等)
│   ├── components/       - 可复用UI组件
│   ├── composables/      - Composition API 逻辑
│   ├── layouts/          - 布局组件
│   ├── pages/            - 页面组件 (路由级)
│   ├── plugins/          - 插件 (如axios、Toast等)
│   ├── router/           - 路由配置
│   ├── store/            - 状态管理 (Pinia/Vuex)
│   ├── styles/           - 全局样式
│   ├── utils/            - 工具函数
│   ├── App.vue           - 根组件
│   └── main.ts           - 应用入口
├── tests/                - 测试文件
├── .eslintrc.js          - ESLint配置
├── .prettierrc           - Prettier配置
├── index.html            - 主HTML文件
├── package.json          - 项目配置和依赖
└── vite.config.ts        - Vite构建配置
```"""

# --- 初始化数据结构 ---
file_data = {}
empty_dirs = []
ignored_items = {"dirs": [], "files": []}
file_type_stats = defaultdict(lambda: {"count": 0, "size": 0})
project_dependencies = {}
key_files_analysis = {}
vue_component_stats = {"components": 0, "pages": 0, "composables": 0, "layouts": 0}

# --- 路径构建 ---
output_file_path = os.path.abspath(os.path.join(WORKSPACE_ROOT, OUTPUT_FILENAME))
workspace_abs_root = os.path.abspath(WORKSPACE_ROOT)
frontend_dir_abs = os.path.abspath(os.path.join(workspace_abs_root, FRONTEND_TARGET_DIR))

# --- 工具函数 ---
def should_ignore_file(filename: str) -> bool:
    """检查文件是否匹配忽略模式"""
    return any(filename.endswith(pattern.replace('*', '')) 
               for pattern in IGNORE_FILE_PATTERNS)

def is_key_file(filepath: str) -> Optional[str]:
    """识别关键文件并返回描述"""
    normalized_path = filepath.replace('\\', '/').lower()
    filename = os.path.basename(normalized_path)
    
    for pattern, description in KEY_FILES.items():
        # 处理通配符模式
        if pattern.startswith('*'):
            suffix = pattern[1:].lower()
            if normalized_path.endswith(suffix):
                return description
        # 处理精确文件名匹配
        elif filename == pattern.lower():
            return description
        # 处理路径模式
        elif normalized_path.endswith(pattern.lower()):
            return description
    return None

def analyze_package_json(file_path: str, content: str) -> Dict[str, Any]:
    """分析package.json文件内容"""
    try:
        package_data = json.loads(content)
        analysis = {}
        
        # 提取主要依赖
        if 'dependencies' in package_data:
            analysis['dependencies'] = package_data['dependencies']
        if 'devDependencies' in package_data:
            analysis['devDependencies'] = package_data['devDependencies']
        
        # 提取脚本命令
        if 'scripts' in package_data:
            analysis['scripts'] = package_data['scripts']
            
        # 提取Vue配置
        if 'vue' in package_data:
            analysis['vue_config'] = package_data['vue']
            
        return analysis
    except Exception as e:
        return {"error": f"分析错误: {str(e)}"}

def track_vue_components(filepath: str) -> None:
    """跟踪Vue组件统计"""
    normalized_path = filepath.replace('\\', '/').lower()
    
    if '/components/' in normalized_path:
        vue_component_stats["components"] += 1
    elif '/pages/' in normalized_path or '/views/' in normalized_path:
        vue_component_stats["pages"] += 1
    elif '/composables/' in normalized_path or '/hooks/' in normalized_path:
        vue_component_stats["composables"] += 1
    elif '/layouts/' in normalized_path:
        vue_component_stats["layouts"] += 1

def read_and_store_file(file_path_abs: str, root_dir: str) -> None:
    """读取文件内容并存储，同时收集元数据"""
    file_path_relative = os.path.relpath(file_path_abs, root_dir)
    
    # 文件类型统计
    ext = os.path.splitext(file_path_abs)[1].lower() or "no_extension"
    try:
        file_size = os.path.getsize(file_path_abs)
    except OSError as e:
        print(f"  错误：获取文件大小失败 {file_path_relative}: {e}")
        return
    
    file_type_stats[ext]["count"] += 1
    file_type_stats[ext]["size"] += file_size
    
    # Vue组件统计
    if ext == '.vue':
        track_vue_components(file_path_relative)
    
    # 关键文件分析
    if key_desc := is_key_file(file_path_relative):
        key_files_analysis[file_path_relative] = key_desc
        
        # 特殊文件处理
        if os.path.basename(file_path_abs) == 'package.json':
            try:
                with open(file_path_abs, 'r', encoding='utf-8') as f:
                    content = f.read()
                dependencies = analyze_package_json(file_path_abs, content)
                project_dependencies[file_path_relative] = dependencies
            except Exception as e:
                project_dependencies[file_path_relative] = {"error": f"读取失败: {str(e)}"}
    
    # 读取文件内容
    try:
        with open(file_path_abs, 'r', encoding='utf-8') as f:
            content = f.read()
        file_data[file_path_relative] = content
        print(f"  读取文件: {file_path_relative} (UTF-8)")
    except UnicodeDecodeError:
        try:
            with open(file_path_abs, 'r', encoding=sys.getdefaultencoding()) as f:
                content = f.read()
            file_data[file_path_relative] = content
            print(f"  读取文件: {file_path_relative} ({sys.getdefaultencoding()})")
        except Exception as e:
            print(f"  错误：无法读取文件 {file_path_relative}: {e}")
            file_data[file_path_relative] = f"Error reading file: {e}"
    except Exception as e:
        print(f"  错误：读取文件 {file_path_relative} 时发生未知错误: {e}")
        file_data[file_path_relative] = f"Error reading file: {e}"

def generate_report() -> None:
    """生成分析报告"""
    print(f"\n=== 生成前端分析报告: {OUTPUT_FILENAME} ===")

    try:
        with open(output_file_path, 'w', encoding='utf-8') as outfile:
            # 项目元数据
            outfile.write(f"# {PROJECT_METADATA['project_name']} 前端结构分析报告\n\n")
            outfile.write(f"**生成日期**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            outfile.write(f"**代码仓库**: [{PROJECT_METADATA['repository_url']}]({PROJECT_METADATA['repository_url']})\n")
            outfile.write(f"**技术栈**: {', '.join(PROJECT_METADATA['technology_stack'])}\n\n")
            
            # 架构概览
            outfile.write(VUE_ARCHITECTURE_DIAGRAM + "\n\n")
            
            # 分析统计
            total_files = sum(stats['count'] for stats in file_type_stats.values())
            outfile.write("## 分析统计\n")
            outfile.write(f"- 分析文件总数: {total_files}\n")
            outfile.write(f"- 发现空目录数: {len(empty_dirs)}\n")
            outfile.write(f"- 忽略项目总数: {len(ignored_items['dirs']) + len(ignored_items['files'])}\n")
            outfile.write(f"- Vue组件总数: {vue_component_stats['components']}\n")
            outfile.write(f"- 页面组件数: {vue_component_stats['pages']}\n")
            outfile.write(f"- 组合函数数: {vue_component_stats['composables']}\n")
            outfile.write(f"- 布局组件数: {vue_component_stats['layouts']}\n\n")
            
            # 文件类型统计
            outfile.write("### 文件类型统计\n")
            outfile.write("| 类型 | 文件数 | 总大小 |\n")
            outfile.write("|------|--------|--------|\n")
            for ext, stats in sorted(file_type_stats.items()):
                size_mb = stats['size'] / (1024 * 1024)
                ext_display = ext if ext else '无扩展名'
                outfile.write(f"| `{ext_display}` | {stats['count']} | {size_mb:.2f} MB |\n")
            outfile.write("\n")
            
            # 关键文件分析
            if key_files_analysis:
                outfile.write("## 关键文件分析\n")
                outfile.write("| 文件路径 | 描述 |\n")
                outfile.write("|----------|------|\n")
                for path, desc in key_files_analysis.items():
                    outfile.write(f"| `{path}` | {desc} |\n")
                outfile.write("\n")
            
            # 项目依赖分析
            if project_dependencies:
                outfile.write("## 项目依赖分析\n")
                for file_path, analysis in project_dependencies.items():
                    if file_path.endswith('package.json'):
                        outfile.write(f"### `{file_path}`\n")
                        
                        # 输出依赖信息
                        if 'dependencies' in analysis:
                            outfile.write("#### 生产依赖\n")
                            outfile.write("| 包名 | 版本 |\n")
                            outfile.write("|------|------|\n")
                            for pkg, version in analysis['dependencies'].items():
                                outfile.write(f"| `{pkg}` | `{version}` |\n")
                            outfile.write("\n")
                        
                        if 'devDependencies' in analysis:
                            outfile.write("#### 开发依赖\n")
                            outfile.write("| 包名 | 版本 |\n")
                            outfile.write("|------|------|\n")
                            for pkg, version in analysis['devDependencies'].items():
                                outfile.write(f"| `{pkg}` | `{version}` |\n")
                            outfile.write("\n")
                        
                        if 'scripts' in analysis:
                            outfile.write("#### 脚本命令\n")
                            outfile.write("```json\n")
                            outfile.write(json.dumps(analysis['scripts'], indent=2))
                            outfile.write("\n```\n\n")
            
            # 忽略项目列表
            outfile.write("## 忽略项目列表\n")
            if ignored_items["dirs"]:
                outfile.write("### 忽略目录\n")
                for dir_path in sorted(ignored_items["dirs"]):
                    outfile.write(f"- `{dir_path}`\n")
            
            if ignored_items["files"]:
                outfile.write("\n### 忽略文件\n")
                for file_path in sorted(ignored_items["files"]):
                    outfile.write(f"- `{file_path}`\n")
            outfile.write("\n")
            
            # 空目录列表
            if empty_dirs:
                outfile.write("## 空目录列表\n")
                outfile.write("> **注意**: 这些目录当前为空，请确认是否需要保留\n\n")
                for dir_path in sorted(empty_dirs):
                    outfile.write(f"- `{dir_path}`\n")
                outfile.write("\n")
            
            # 文件内容详情
            outfile.write("## 文件内容详情\n")
            for path, content in sorted(file_data.items()):
                normalized_path = path.replace('\\', '/')
                
                # 添加文件类型标签
                file_ext = os.path.splitext(path)[1]
                lang = {
                    '.vue': 'vue',
                    '.js': 'javascript',
                    '.ts': 'typescript',
                    '.json': 'json',
                    '.html': 'html',
                    '.css': 'css',
                    '.scss': 'scss'
                }.get(file_ext, '')
                
                outfile.write(f"### `{normalized_path}`\n\n")
                
                if lang:
                    outfile.write(f"```{lang}\n")
                else:
                    outfile.write("```\n")
                
                outfile.write(content.rstrip() + "\n")
                outfile.write("```\n\n")
        
        print(f"=== 前端报告生成成功: {os.path.relpath(output_file_path)} ===")
    except Exception as e:
        print(f"错误：无法写入报告文件: {e}")

def main() -> None:
    """主分析流程"""
    print("=== 开始前端项目结构分析 ===")
    print(f"项目名称: {PROJECT_METADATA['project_name']}")
    print(f"分析目标: {FRONTEND_TARGET_DIR}")
    print(f"忽略目录: {', '.join(IGNORE_DIRS)}")
    print(f"忽略文件模式: {', '.join(IGNORE_FILE_PATTERNS)}\n")

    if not os.path.isdir(frontend_dir_abs):
        print(f"错误: 前端目录 '{FRONTEND_TARGET_DIR}' 不存在")
        sys.exit(1)

    print(f"\n--- 遍历目录: {FRONTEND_TARGET_DIR} ---")
    for root, dirs, files in os.walk(frontend_dir_abs, topdown=True):
        # 处理忽略目录
        for dir_name in list(dirs):
            full_dir_path = os.path.join(root, dir_name)
            rel_dir_path = os.path.relpath(full_dir_path, workspace_abs_root)
            
            if dir_name in IGNORE_DIRS:
                dirs.remove(dir_name)
                ignored_items["dirs"].append(rel_dir_path)
                print(f"  忽略目录: {rel_dir_path}")
                continue
        
        # 处理文件
        for filename in files:
            file_path_abs = os.path.join(root, filename)
            rel_file_path = os.path.relpath(file_path_abs, workspace_abs_root)
            
            if should_ignore_file(filename):
                ignored_items["files"].append(rel_file_path)
                print(f"  忽略文件: {rel_file_path}")
                continue
                
            read_and_store_file(file_path_abs, workspace_abs_root)
            
        # 检查空目录
        if not files and not dirs:
            rel_dir_path = os.path.relpath(root, workspace_abs_root)
            if rel_dir_path not in empty_dirs:
                empty_dirs.append(rel_dir_path)
                print(f"  发现空目录: {rel_dir_path}")

    generate_report()

if __name__ == "__main__":
    main()