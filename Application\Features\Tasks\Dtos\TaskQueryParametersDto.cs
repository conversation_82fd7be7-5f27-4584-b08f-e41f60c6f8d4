// File: Application/Features/Tasks/Dtos/TaskQueryParametersDto.cs
// Description: DTO for task query parameters.
using System;

namespace ItAssetsSystem.Application.Features.Tasks.Dtos
{
    public class TaskQueryParametersDto
    {
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 10;
        public string SearchTerm { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty; // e.g., "Todo", "InProgress", "Done"
        public string Priority { get; set; } = string.Empty; // e.g., "Low", "Medium", "High"
        public string TaskType { get; set; } = string.Empty; // e.g., "Normal", "Periodic", "PDCA"
        public int? AssigneeUserId { get; set; }
        public int? CreatorUserId { get; set; }
        public long? ParentTaskId { get; set; }
        public long? ProjectId { get; set; } // Added ProjectId
        public int? AssetId { get; set; }
        public int? LocationId { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public DateTime? CreateStartDate { get; set; } // 创建开始日期
        public DateTime? CreateEndDate { get; set; } // 创建结束日期
        public string SortBy { get; set; } = string.Empty; // e.g., "Name", "CreationTimestamp", "PlanEndDate"
        public string SortDirection { get; set; } = string.Empty; // "asc" or "desc"
        public bool IncludeDeleted { get; set; } = false;
        public bool ForceRefresh { get; set; } = false; // 强制刷新，跳过缓存
    }
} 