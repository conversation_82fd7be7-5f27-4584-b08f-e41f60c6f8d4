using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using ItAssetsSystem.Infrastructure.Data;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;
using System.Linq;
using MySqlConnector;

namespace ItAssetsSystem.Controllers
{
    /// <summary>
    /// [已弃用] 资产统计控制器 V1 - 请使用 /api/v2/asset-statistics
    /// </summary>
    [ApiController]
    [Route("api/asset/statistics")]
    [Obsolete("此控制器已弃用，请使用 V2 API: /api/v2/asset-statistics")]
    public class AssetStatisticsController : ControllerBase
    {
        private readonly AppDbContext _context;
        private readonly ILogger<AssetStatisticsController> _logger;

        public AssetStatisticsController(AppDbContext context, ILogger<AssetStatisticsController> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// 获取资产总体统计
        /// </summary>
        [HttpGet("overall")]
        public async Task<IActionResult> GetOverallStatistics([FromQuery] StatisticsQueryDto query)
        {
            try
            {
                var endDate = query.EndDate ?? DateTime.Now;
                var startDate = query.StartDate ?? endDate.AddDays(-30);

                // 当前统计
                var currentStats = await _context.Assets
                    .Where(a => a.CreatedAt <= endDate)
                    .GroupBy(a => 1)
                    .Select(g => new
                    {
                        TotalAssets = g.Count(),
                        ActiveAssets = g.Count(a => a.Status == 1),
                        IdleAssets = g.Count(a => a.Status == 0),
                        MaintenanceAssets = g.Count(a => a.Status == 2),
                        ScrapAssets = g.Count(a => a.Status == 3),
                        TotalValue = g.Sum(a => a.Price ?? 0)
                    })
                    .FirstOrDefaultAsync();

                // 上期统计（用于计算趋势）
                var previousPeriodEnd = startDate.AddDays(-1);
                var previousPeriodStart = previousPeriodEnd.AddDays(-(endDate - startDate).Days);
                
                var previousStats = await _context.Assets
                    .Where(a => a.CreatedAt <= previousPeriodEnd)
                    .GroupBy(a => 1)
                    .Select(g => new
                    {
                        TotalAssets = g.Count(),
                        ActiveAssets = g.Count(a => a.Status == 1),
                        IdleAssets = g.Count(a => a.Status == 0),
                        TotalValue = g.Sum(a => a.Price ?? 0)
                    })
                    .FirstOrDefaultAsync();

                // 计算趋势
                var result = new
                {
                    TotalAssets = currentStats?.TotalAssets ?? 0,
                    ActiveAssets = currentStats?.ActiveAssets ?? 0,
                    IdleAssets = currentStats?.IdleAssets ?? 0,
                    MaintenanceAssets = currentStats?.MaintenanceAssets ?? 0,
                    ScrapAssets = currentStats?.ScrapAssets ?? 0,
                    TotalValue = currentStats?.TotalValue ?? 0,
                    TotalTrend = CalculateTrend(currentStats?.TotalAssets ?? 0, previousStats?.TotalAssets ?? 0),
                    ActiveTrend = CalculateTrend(currentStats?.ActiveAssets ?? 0, previousStats?.ActiveAssets ?? 0),
                    IdleTrend = CalculateTrend(currentStats?.IdleAssets ?? 0, previousStats?.IdleAssets ?? 0),
                    ValueTrend = CalculateTrend((double)(currentStats?.TotalValue ?? 0), (double)(previousStats?.TotalValue ?? 0))
                };

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取总体统计失败");
                return StatusCode(500, "获取统计数据失败");
            }
        }

        /// <summary>
        /// 按资产类型统计
        /// </summary>
        [HttpGet("by-type")]
        public async Task<IActionResult> GetStatisticsByType([FromQuery] StatisticsQueryDto query)
        {
            try
            {
                var endDate = query.EndDate ?? DateTime.Now;
                var startDate = query.StartDate ?? endDate.AddDays(-30);

                var result = await _context.Assets
                    .Where(a => a.CreatedAt >= startDate && a.CreatedAt <= endDate)
                    .Include(a => a.AssetType)
                    .GroupBy(a => new { a.AssetTypeId, a.AssetType.Name })
                    .Select(g => new
                    {
                        TypeId = g.Key.AssetTypeId,
                        TypeName = g.Key.Name,
                        Count = g.Count(),
                        ActiveCount = g.Count(a => a.Status == 1),
                        IdleCount = g.Count(a => a.Status == 0),
                        MaintenanceCount = g.Count(a => a.Status == 2),
                        ScrapCount = g.Count(a => a.Status == 3),
                        TotalValue = g.Sum(a => a.Price ?? 0),
                        AvgValue = g.Average(a => a.Price ?? 0)
                    })
                    .OrderByDescending(x => x.Count)
                    .ToListAsync();

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取类型统计失败");
                return StatusCode(500, "获取类型统计失败");
            }
        }

        /// <summary>
        /// 按区域统计（根据位置Path第四个值进行统计）
        /// </summary>
        [HttpGet("by-region")]
        public async Task<IActionResult> GetStatisticsByRegion([FromQuery] StatisticsQueryDto query)
        {
            try
            {
                var endDate = query.EndDate ?? DateTime.Now;
                var startDate = query.StartDate ?? endDate.AddDays(-30);

                _logger.LogInformation($"开始按区域统计，时间范围: {startDate:yyyy-MM-dd} 到 {endDate:yyyy-MM-dd}");

                // 获取所有资产及其位置Path
                var assetsWithPath = await _context.Assets
                    .Where(a => a.CreatedAt >= startDate && a.CreatedAt <= endDate &&
                               a.Location != null && !string.IsNullOrEmpty(a.Location.Path))
                    .Include(a => a.AssetType)
                    .Select(a => new
                    {
                        a.Id,
                        a.Status,
                        a.Price,
                        AssetTypeName = a.AssetType.Name,
                        LocationPath = a.Location.Path
                    })
                    .ToListAsync();

                _logger.LogInformation($"获取到 {assetsWithPath.Count} 个有位置路径的资产");

                // 按Path第四个值分组统计
                var regionGroups = assetsWithPath
                    .Where(a => !string.IsNullOrEmpty(a.LocationPath))
                    .Select(a => new
                    {
                        Asset = a,
                        PathParts = a.LocationPath.Split(','),
                    })
                    .Where(x => x.PathParts.Length >= 4 && int.TryParse(x.PathParts[3], out _))
                    .GroupBy(x => int.Parse(x.PathParts[3]))
                    .ToList();

                _logger.LogInformation($"按Path第四个值分组后得到 {regionGroups.Count} 个区域组");

                // 获取这些区域ID对应的位置信息
                var regionIds = regionGroups.Select(g => g.Key).ToList();
                var regionInfos = await _context.Locations
                    .Where(l => regionIds.Contains(l.Id))
                    .ToDictionaryAsync(l => l.Id, l => l.Name);

                // 构建统计结果
                var result = regionGroups.Select(g =>
                {
                    var regionId = g.Key;
                    var assets = g.Select(x => x.Asset).ToList();
                    var regionName = regionInfos.ContainsKey(regionId) ? regionInfos[regionId] : $"位置{regionId}";

                    return new RegionStatisticsDto
                    {
                        RegionId = regionId,
                        RegionName = regionName,
                        TotalAssets = assets.Count,
                        ActiveAssets = assets.Count(a => a.Status == 1),
                        IdleAssets = assets.Count(a => a.Status == 0),
                        Computers = assets.Count(a => a.AssetTypeName.Contains("电脑") || a.AssetTypeName.Contains("计算机")),
                        PDAs = assets.Count(a => a.AssetTypeName.Contains("PDA") || a.AssetTypeName.Contains("手持")),
                        Others = assets.Count(a => !a.AssetTypeName.Contains("电脑") && !a.AssetTypeName.Contains("计算机") &&
                                                  !a.AssetTypeName.Contains("PDA") && !a.AssetTypeName.Contains("手持")),
                        TotalValue = assets.Sum(a => a.Price ?? 0),
                        UtilizationRate = assets.Count > 0 ? Math.Round((double)assets.Count(a => a.Status == 1) * 100.0 / assets.Count, 2) : 0
                    };
                })
                .Where(r => r.TotalAssets > 0)
                .OrderByDescending(r => r.TotalAssets)
                .ToList();

                _logger.LogInformation($"最终返回 {result.Count} 个区域统计结果");

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取区域统计失败");
                return StatusCode(500, "获取区域统计失败");
            }
        }

        /// <summary>
        /// 按部门统计
        /// </summary>
        [HttpGet("by-department")]
        public async Task<IActionResult> GetStatisticsByDepartment([FromQuery] StatisticsQueryDto query)
        {
            try
            {
                var endDate = query.EndDate ?? DateTime.Now;
                var startDate = query.StartDate ?? endDate.AddDays(-30);

                // 使用LINQ查询进行部门统计
                var result = await _context.Departments
                    .Where(d => d.IsActive == true)
                    .Select(d => new DepartmentStatisticsDto
                    {
                        DepartmentId = d.Id,
                        DepartmentName = d.Name,
                        TotalAssets = d.Assets.Count(a => a.CreatedAt >= startDate && a.CreatedAt <= endDate),
                        ActiveAssets = d.Assets.Count(a => a.CreatedAt >= startDate && a.CreatedAt <= endDate && a.Status == 1),
                        IdleAssets = d.Assets.Count(a => a.CreatedAt >= startDate && a.CreatedAt <= endDate && a.Status == 0),
                        Computers = d.Assets.Count(a => a.CreatedAt >= startDate && a.CreatedAt <= endDate &&
                            (a.AssetType.Name.Contains("电脑") || a.AssetType.Name.Contains("计算机"))),
                        PDAs = d.Assets.Count(a => a.CreatedAt >= startDate && a.CreatedAt <= endDate &&
                            (a.AssetType.Name.Contains("PDA") || a.AssetType.Name.Contains("手持"))),
                        Others = d.Assets.Count(a => a.CreatedAt >= startDate && a.CreatedAt <= endDate &&
                            !a.AssetType.Name.Contains("电脑") && !a.AssetType.Name.Contains("计算机") &&
                            !a.AssetType.Name.Contains("PDA") && !a.AssetType.Name.Contains("手持")),
                        TotalValue = d.Assets.Where(a => a.CreatedAt >= startDate && a.CreatedAt <= endDate)
                            .Sum(a => a.Price ?? 0),
                        ActiveRate = d.Assets.Count(a => a.CreatedAt >= startDate && a.CreatedAt <= endDate) > 0 ?
                            Math.Round((double)d.Assets.Count(a => a.CreatedAt >= startDate && a.CreatedAt <= endDate && a.Status == 1) * 100.0 /
                            d.Assets.Count(a => a.CreatedAt >= startDate && a.CreatedAt <= endDate), 2) : 0
                    })
                    .Where(r => r.TotalAssets > 0)
                    .OrderByDescending(r => r.TotalAssets)
                    .ToListAsync();

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取部门统计失败");
                return StatusCode(500, "获取部门统计失败");
            }
        }

        /// <summary>
        /// 获取区域筛选选项（直接获取type=2的所有位置）
        /// </summary>
        [HttpGet("region-options")]
        public async Task<IActionResult> GetRegionOptions()
        {
            try
            {
                _logger.LogInformation("开始获取区域筛选选项（直接获取type=2的位置）");

                // 直接获取所有type=2的位置作为筛选选项
                var regionOptions = await _context.Locations
                    .Where(l => l.Type == 2 && l.IsActive == true)
                    .Select(l => new RegionOptionDto
                    {
                        RegionId = l.Id,
                        RegionName = l.Name
                    })
                    .OrderBy(r => r.RegionName)
                    .ToListAsync();

                _logger.LogInformation($"获取到 {regionOptions.Count} 个type=2的位置作为区域选项");

                return Ok(regionOptions);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取区域选项失败");
                return StatusCode(500, "获取区域选项失败");
            }
        }

        /// <summary>
        /// 计算趋势百分比
        /// </summary>
        private double CalculateTrend(double current, double previous)
        {
            if (previous == 0) return current > 0 ? 100 : 0;
            return Math.Round(((current - previous) / previous) * 100, 2);
        }
    }

    // DTO类
    public class StatisticsQueryDto
    {
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public string Period { get; set; } = "weekly"; // daily, weekly, monthly, custom
    }

    public class RegionStatisticsDto
    {
        public int RegionId { get; set; }
        public string RegionName { get; set; } = string.Empty;
        public int TotalAssets { get; set; }
        public int ActiveAssets { get; set; }
        public int IdleAssets { get; set; }
        public int Computers { get; set; }
        public int PDAs { get; set; }
        public int Others { get; set; }
        public decimal TotalValue { get; set; }
        public double UtilizationRate { get; set; }
    }

    public class DepartmentStatisticsDto
    {
        public int DepartmentId { get; set; }
        public string DepartmentName { get; set; } = string.Empty;
        public int TotalAssets { get; set; }
        public int ActiveAssets { get; set; }
        public int IdleAssets { get; set; }
        public int Computers { get; set; }
        public int PDAs { get; set; }
        public int Others { get; set; }
        public decimal TotalValue { get; set; }
        public double ActiveRate { get; set; }
    }

    public class RegionOptionDto
    {
        public int RegionId { get; set; }
        public string RegionName { get; set; } = string.Empty;
    }
}
