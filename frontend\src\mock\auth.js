/**
 * 认证API模拟数据
 * 文件路径: src/mock/auth.js
 * 功能描述: 提供用户认证相关的模拟数据
 */

import systemConfig from '@/config/system'

// 模拟延迟
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

/**
 * 模拟用户登录
 * @param {Object} data 登录数据
 * @returns {Promise<Object>} 登录结果
 */
async function login(data) {
  console.log('使用模拟登录', data);
  
  // 模拟网络延迟
  await delay(800);
  
  // 检查用户名和密码
  if (data.username === 'admin' && data.password === '123456') {
    return {
      success: true,
      data: {
        token: 'mock-token-12345',
        refreshToken: 'mock-refresh-token-67890',
        user: {
          id: 1,
          username: 'admin',
          name: '管理员',
          avatar: '',
          email: '<EMAIL>',
          roles: ['admin'],
          permissions: [
            'asset:view',
            'asset:list',
            'asset:type',
            'asset:create',
            'asset:edit',
            'asset:delete',
            'system:view',
            'user:manage'
          ]
        }
      },
      message: '登录成功'
    };
  } else if (data.username === 'user' && data.password === '123456') {
    return {
      success: true,
      data: {
        token: 'mock-token-user-12345',
        refreshToken: 'mock-refresh-token-user-67890',
        user: {
          id: 2,
          username: 'user',
          name: '普通用户',
          avatar: '',
          email: '<EMAIL>',
          roles: ['user'],
          permissions: [
            'asset:view',
            'asset:list'
          ]
        }
      },
      message: '登录成功'
    };
  }
  
  // 登录失败
  return {
    success: false,
    data: null,
    message: '用户名或密码错误'
  };
}

/**
 * 模拟退出登录
 * @returns {Promise<Object>} 退出结果
 */
async function logout() {
  await delay(300);
  
  return {
    success: true,
    data: null,
    message: '退出登录成功'
  };
}

/**
 * 模拟获取用户信息
 * @returns {Promise<Object>} 用户信息
 */
async function getUserInfo() {
  await delay(300);
  
  return {
    success: true,
    data: {
      id: 1,
      username: 'admin',
      name: '管理员',
      avatar: '',
      email: '<EMAIL>',
      roles: ['admin'],
      permissions: [
        'asset:view',
        'asset:list',
        'asset:type',
        'asset:create',
        'asset:edit',
        'asset:delete',
        'system:view',
        'user:manage'
      ]
    },
    message: '获取用户信息成功'
  };
}

/**
 * 模拟刷新令牌
 * @param {Object} data 包含refreshToken的对象
 * @returns {Promise<Object>} 新的令牌信息
 */
async function refreshToken(data) {
  await delay(300);
  
  if (data.refreshToken) {
    return {
      success: true,
      data: {
        token: 'mock-new-token-' + Date.now(),
        refreshToken: 'mock-new-refresh-token-' + Date.now()
      },
      message: '刷新令牌成功'
    };
  }
  
  return {
    success: false,
    data: null,
    message: '刷新令牌失败，请重新登录'
  };
}

// 导出模拟API
export default {
  login,
  logout,
  getUserInfo,
  refreshToken
}; 