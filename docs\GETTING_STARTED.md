# 🚀 新人入门指南

## 👋 欢迎加入团队！

欢迎来到航空航天级IT资产管理系统项目！这份指南将帮助你在**5分钟内**了解项目，**30分钟内**搭建开发环境，**1小时内**开始贡献代码。

## 📚 必读材料（10分钟）

### 1. 项目概览
- **定位**: 企业级IT资产管理平台，专为航空航天等高端制造业设计
- **技术栈**: Vue 3 + .NET 6 + MySQL + Clean Architecture
- **特色**: 游戏化设计、3D可视化、实时协作、智能预测

### 2. 核心业务理解
```
📦 资产采购 → 🏭 入库接收 → 📍 位置分配 → 🔧 使用维护 → ⚠️ 故障处理 → 🔄 返厂维修 → 🗑️ 报废处置
```

**5大核心模块**:
- 🏗️ **资产管理**: 全生命周期跟踪（核心业务）
- ⚡ **任务管理**: 游戏化协作系统（创新亮点）
- 🔧 **备件管理**: 智能库存控制（成本控制）
- 🌐 **3D可视化**: 工厂实时监控（技术展示）
- 👥 **用户权限**: 多角色管理（基础设施）

### 3. 技术架构速览
```
前端 Vue 3          后端 .NET 6         数据库 MySQL
├── Element Plus    ├── Clean Arch     ├── 69张表
├── Pinia状态       ├── EF Core ORM    ├── 双主键策略
├── ECharts图表     ├── CQRS模式       └── 完整外键
└── Three.js 3D     └── 事件驱动
```

## 🛠️ 环境搭建（15分钟）

### 步骤1: 安装必要工具
```bash
# 1. Node.js 16+ (前端)
node --version  # 检查版本

# 2. .NET 6 SDK (后端)  
dotnet --version  # 检查版本

# 3. MySQL 8.0+ (数据库)
mysql --version  # 检查版本

# 4. Git (版本控制)
git --version  # 检查版本
```

### 步骤2: 克隆项目
```bash
# 克隆项目
git clone <repository-url>
cd singleit20250406

# 查看项目结构
ls -la
```

### 步骤3: 后端启动（5分钟）
```bash
# 1. 还原包
dotnet restore

# 2. 配置数据库连接 (开发环境使用SQLite)
# appsettings.Development.json 已配置好SQLite，无需修改

# 3. 运行应用 (自动创建数据库)
dotnet run

# ✅ 看到这个输出说明成功
# info: Microsoft.Hosting.Lifetime[14]
#       Now listening on: https://localhost:5001
```

### 步骤4: 前端启动（5分钟）
```bash
# 新开终端窗口
cd frontend

# 1. 安装依赖
npm install

# 2. 启动开发服务器  
npm run dev

# ✅ 看到这个输出说明成功
# Local:   http://localhost:5173/
# Network: http://192.168.x.x:5173/
```

### 步骤5: 验证安装
```bash
# 1. 访问前端页面
浏览器打开: http://localhost:5173

# 2. 默认账号登录
用户名: admin
密码: 123456

# 3. 检查API连接
浏览器打开: https://localhost:5001/swagger
```

## 🎯 第一个任务（30分钟）

### 任务：修改首页欢迎文字

这是一个简单的热身任务，帮你熟悉开发流程。

#### 1. 理解现有代码
```vue
<!-- 文件位置: frontend/src/views/HomeView.vue -->
<template>
  <div class="home">
    <h1>欢迎使用IT资产管理系统</h1>  <!-- 要修改这里 -->
    <p>专业、智能、高效的企业级资产管理平台</p>
  </div>
</template>
```

#### 2. 修改代码
```vue
<!-- 修改为你的个人化欢迎词 -->
<template>
  <div class="home">
    <h1>欢迎 {{ userName }} 使用航空航天级资产管理系统！</h1>
    <p>让我们一起管理未来！🚀</p>
  </div>
</template>

<script setup>
import { ref } from 'vue'

// 添加动态用户名
const userName = ref('新同事')
</script>
```

#### 3. 查看效果
- 保存文件后浏览器自动刷新
- 应该看到修改后的欢迎文字

#### 4. 提交代码
```bash
git add frontend/src/views/HomeView.vue
git commit -m "feat: 个性化首页欢迎文字"
git push origin feature/welcome-message
```

🎉 **恭喜！你已经完成第一个代码贡献！**

## 📂 项目结构详解

### 后端结构 (.NET 6)
```
📁 后端代码组织 (Clean Architecture)
├── 📁 Api/                     # V2 API端点 (Clean Architecture)
│   ├── V2/Controllers/         # 新架构控制器  
│   ├── Backup/                 # 备份管理API
│   └── Location/               # 位置管理API
│
├── 📁 Application/             # 应用层 (用例、DTOs、服务)
│   ├── Common/                 # 通用DTOs和模型
│   └── Features/               # 按功能模块组织
│       ├── Tasks/              # 任务管理
│       ├── SpareParts/         # 备件管理
│       └── AssetStatistics/    # 资产统计
│
├── 📁 Domain/                  # 领域层 (实体、聚合根)
│   └── Entities/
│       ├── Tasks/              # 任务实体
│       ├── Notes/              # 备忘实体
│       └── Gamification/       # 游戏化实体
│
├── 📁 Infrastructure/          # 基础设施层 (数据访问、外部服务)
│   ├── Data/                   # EF Core DbContext
│   ├── Repositories/           # 仓储实现
│   └── Services/               # 外部服务
│
├── 📁 Core/                    # 核心层 (抽象、接口、横切关注点)
│   ├── Abstractions/           # 仓储接口
│   ├── Events/                 # 域事件
│   ├── Services/               # 核心服务
│   └── Plugins/                # 插件系统
│
└── 📁 Controllers/             # V1 传统控制器 (向后兼容)
    ├── AssetController.cs      # 资产管理
    ├── TaskController.cs       # 任务管理
    └── V1_1/                   # V1.1优化版本
```

### 前端结构 (Vue 3)
```
📁 前端代码组织 (Vue 3 + Element Plus)
├── 📁 src/
│   ├── 📁 api/                 # API接口封装
│   │   ├── asset.js            # 资产相关API
│   │   ├── task.js             # 任务相关API
│   │   └── v1.1/               # V1.1版本API
│   │
│   ├── 📁 components/          # 可复用组件
│   │   ├── QuickMemo/          # 快速备忘组件
│   │   ├── Tasks/              # 任务相关组件
│   │   └── UserAvatar.vue      # 用户头像
│   │
│   ├── 📁 views/               # 页面组件
│   │   ├── asset/              # 资产管理页面
│   │   ├── tasks/              # 任务管理页面
│   │   ├── dashboard/          # 仪表盘页面
│   │   └── spareparts/         # 备件管理页面
│   │
│   ├── 📁 stores/              # Pinia状态管理
│   │   └── modules/            # 按模块分组
│   │
│   ├── 📁 router/              # Vue Router配置
│   ├── 📁 utils/               # 工具函数
│   └── 📁 styles/              # 全局样式
│
└── 📄 package.json             # 依赖配置
```

## 🔍 如何找到你要修改的代码

### 场景1: 要修改页面UI
```bash
# 1. 根据URL找到对应页面
/assets → frontend/src/views/asset/
/tasks → frontend/src/views/tasks/
/dashboard → frontend/src/views/dashboard/

# 2. 主要文件名对应
资产列表页面 → views/asset/list.vue
任务管理页面 → views/tasks/EnhancedTaskListView.vue
工厂监控页面 → views/dashboard/FactoryLayoutDashboardV2.vue
```

### 场景2: 要修改API接口
```bash
# 1. 优先查看V2新架构 (推荐)
Api/V2/Controllers/TasksController.cs        # 任务API
Api/V2/Controllers/SparePartsController.cs   # 备件API

# 2. 如果V2没有，查看V1控制器
Controllers/AssetController.cs               # 资产API
Controllers/UserController.cs                # 用户API
```

### 场景3: 要修改业务逻辑
```bash
# 1. V2新模块 (Clean Architecture)
Application/Features/Tasks/Services/TaskService.cs          # 任务业务逻辑
Application/Features/SpareParts/Services/SparePartService.cs # 备件业务逻辑

# 2. V1传统模块  
Services/GamificationService.cs             # 游戏化逻辑
Services/TokenService.cs                    # 认证逻辑
```

### 场景4: 要修改数据库
```bash
# 1. 查看实体定义
Domain/Entities/Tasks/Task.cs               # V2任务实体
Models/Entities/Asset.cs                    # V1资产实体

# 2. 查看数据库配置
Infrastructure/Data/AppDbContext.cs         # EF Core配置
Infrastructure/Data/Configurations/         # 实体配置
```

## 🐛 常见问题与解决

### 问题1: 后端启动失败
```bash
# 错误: 数据库连接失败
解决: 检查appsettings.json中的连接字符串

# 错误: 端口被占用
解决: 修改launchSettings.json中的端口，或者杀掉占用进程
netstat -ano | findstr :5001
taskkill /PID <进程ID> /F
```

### 问题2: 前端启动失败
```bash
# 错误: npm install失败
解决: 清除缓存重新安装
rm -rf node_modules package-lock.json
npm install

# 错误: 代理错误，无法连接后端
解决: 确保后端已启动，检查vite.config.js中的proxy配置
```

### 问题3: 登录失败
```bash
# 错误: 401 Unauthorized
解决1: 检查用户名密码 (admin/123456)
解决2: 检查数据库是否有初始数据
解决3: 查看后端日志确认JWT配置
```

### 问题4: API调用404
```bash
# 错误: API路径不存在
解决: 检查API版本路径
✅ 正确: /api/v2/tasks
❌ 错误: /api/tasks

# 检查Swagger文档确认可用API
浏览器访问: https://localhost:5001/swagger
```

## 🎯 开发工作流

### 1. 接到新需求
```bash
# 1. 创建功能分支
git checkout -b feature/新功能名称

# 2. 分析需求
- 确定是前端还是后端修改
- 找到相关的文件位置
- 查看现有类似功能实现

# 3. 开发实现
- 编写代码
- 本地测试
- 修复问题

# 4. 提交代码
git add .
git commit -m "feat: 新功能描述"
git push origin feature/新功能名称

# 5. 创建Pull Request
- 在GitHub/GitLab创建PR
- 请求代码评审
- 根据反馈修改
```

### 2. 修复Bug
```bash
# 1. 创建修复分支
git checkout -b hotfix/bug描述

# 2. 定位问题
- 查看错误日志
- 重现问题场景
- 找到相关代码

# 3. 修复验证
- 修改代码
- 本地验证修复
- 添加测试用例（如有）

# 4. 提交修复
git commit -m "fix: 修复xxx问题"
git push origin hotfix/bug描述
```

## 📖 学习资源

### 技术文档
- **Vue 3**: https://vuejs.org/guide/
- **.NET 6**: https://docs.microsoft.com/aspnet/core
- **Element Plus**: https://element-plus.org/
- **Clean Architecture**: https://blog.cleancoder.com/uncle-bob/2012/08/13/the-clean-architecture.html

### 项目文档
- 📋 [技术架构详解](ARCHITECTURE.md) - 深入理解系统设计
- 🔍 [项目问题分析](PROJECT_ISSUES_ANALYSIS.md) - 了解技术债务
- 🛠️ [开发规范](DEVELOPMENT.md) - 编码标准和最佳实践
- 📊 [API文档](API.md) - 接口说明和示例

### 内部资源
- 🎥 **录屏教程**: docs/videos/ (如有)
- 💬 **团队群聊**: 微信群/钉钉群
- 📝 **知识库**: confluence/notion地址
- 🐛 **问题跟踪**: Jira/GitHub Issues

## 🤝 团队协作

### 代码评审标准
- ✅ 代码符合项目规范
- ✅ 功能完整且已测试
- ✅ 注释清晰，易于理解
- ✅ 性能无明显问题
- ✅ 安全无漏洞风险

### 沟通规范
- 🚀 **紧急问题**: 立即电话/微信
- 📝 **日常问题**: 群聊讨论
- 📋 **需求澄清**: 邮件确认
- 🐛 **Bug反馈**: 提Issue详细描述

### 学习建议
1. **第一周**: 熟悉项目结构和业务流程
2. **第二周**: 完成简单功能开发
3. **第三周**: 深入理解架构设计
4. **第四周**: 独立承担复杂需求

---

🎉 **欢迎加入我们的团队！有任何问题随时在群里问，我们都很乐意帮助你！**

📞 **联系方式**: 
- 技术负责人: @张三 (微信: zhangsan123)
- 产品负责人: @李四 (微信: lisi456)
- 项目经理: @王五 (微信: wangwu789)