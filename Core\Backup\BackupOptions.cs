// IT资产管理系统 - 备份选项
// 文件路径: /Core/Backup/BackupOptions.cs
// 功能: 定义系统备份配置选项

using System;

namespace ItAssetsSystem.Core.Backup
{
    /// <summary>
    /// 备份选项
    /// </summary>
    public class BackupOptions
    {
        /// <summary>
        /// 备份存储路径
        /// </summary>
        public string StoragePath { get; set; }
        
        /// <summary>
        /// 是否启用自动备份
        /// </summary>
        public bool EnableAutoBackup { get; set; } = true;
        
        /// <summary>
        /// 自动备份频率（小时）
        /// </summary>
        public int AutoBackupFrequencyHours { get; set; } = 24;
        
        /// <summary>
        /// 备份保留天数
        /// </summary>
        public int RetentionDays { get; set; } = 30;
        
        /// <summary>
        /// 最大备份数量
        /// </summary>
        public int MaxBackupCount { get; set; } = 10;
        
        /// <summary>
        /// 备份前检查存储空间
        /// </summary>
        public bool CheckStorageSpace { get; set; } = true;
        
        /// <summary>
        /// 最小可用空间（MB）
        /// </summary>
        public int MinimumFreeSpaceMB { get; set; } = 1000;
    }
} 