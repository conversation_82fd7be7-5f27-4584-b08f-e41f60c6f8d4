using Microsoft.AspNetCore.Mvc;
using ItAssetsSystem.Core.Abstractions;
using ItAssetsSystem.Application.Common.Dtos;
using System.Threading.Tasks;
using System;

namespace ItAssetsSystem.Api.V2.Controllers
{
    [ApiController]
    [Route("api/v2/[controller]")]
    public class NotificationTestController : ControllerBase
    {
        private readonly INotificationService _notificationService;
        private readonly ICurrentUserService _currentUserService;

        public NotificationTestController(
            INotificationService notificationService,
            ICurrentUserService currentUserService)
        {
            _notificationService = notificationService;
            _currentUserService = currentUserService;
        }

        /// <summary>
        /// 发送测试通知
        /// </summary>
        [HttpPost("send")]
        public async Task<ActionResult<ApiResponse<object>>> SendTestNotification()
        {
            try
            {
                var currentUserId = _currentUserService.UserId;
                Console.WriteLine($"=== 测试通知API调试 ===");
                Console.WriteLine($"原始用户ID: {currentUserId}");

                // 临时解决方案：如果用户ID为0，使用默认用户ID 1
                if (currentUserId == 0)
                {
                    Console.WriteLine("用户ID为0，使用默认用户ID 1 进行测试");
                    currentUserId = 1;
                    // return ApiResponse<object>.Error("用户未登录");
                }

                // 创建测试通知
                await _notificationService.CreateNotificationAsync(
                    currentUserId,
                    "测试通知",
                    $"这是一条测试通知，发送时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}",
                    "Test",
                    "Task",
                    123,
                    currentUserId,
                    "Normal"
                );

                return Ok(ApiResponse<object>.CreateSuccess(null, "测试通知发送成功"));
            }
            catch (Exception ex)
            {
                return Ok(ApiResponse<object>.CreateFail($"发送测试通知失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 创建任务相关测试通知
        /// </summary>
        [HttpPost("send-task")]
        public async Task<ActionResult<ApiResponse<object>>> SendTaskTestNotification()
        {
            try
            {
                var currentUserId = _currentUserService.UserId;
                Console.WriteLine($"=== 任务测试通知API调试 ===");
                Console.WriteLine($"原始用户ID: {currentUserId}");

                // 临时解决方案：如果用户ID为0，使用默认用户ID 1
                if (currentUserId == 0)
                {
                    Console.WriteLine("用户ID为0，使用默认用户ID 1 进行测试");
                    currentUserId = 1;
                    // return ApiResponse<object>.Error("用户未登录");
                }

                // 创建任务相关测试通知
                await _notificationService.CreateNotificationAsync(
                    currentUserId,
                    "任务测试通知",
                    $"您有一个新的任务分配，发送时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}",
                    "TaskAssigned",
                    "Task",
                    456,
                    currentUserId,
                    "High"
                );

                return Ok(ApiResponse<object>.CreateSuccess(null, "任务测试通知发送成功"));
            }
            catch (Exception ex)
            {
                return Ok(ApiResponse<object>.CreateFail($"发送任务测试通知失败: {ex.Message}"));
            }
        }
    }
}