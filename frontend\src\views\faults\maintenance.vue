/**
 * 航空航天级IT资产管理系统 - 返厂/维修页面
 * 文件路径: src/views/faults/maintenance.vue
 * 功能描述: 管理需要返厂或送外维修的IT设备
 */

<template>
  <div class="maintenance-container">
    <!-- 页面标题和操作按钮 -->
    <div class="page-header">
      <h2 class="page-title">返厂/维修管理</h2>
      <div class="page-actions">
        <el-button type="primary" @click="handleAddMaintenance" :icon="Plus">
          创建维修单
        </el-button>
        <el-button type="primary" @click="handleExportData" :icon="Download">
          导出数据
        </el-button>
      </div>
    </div>

    <!-- 筛选条件 -->
    <el-card class="filter-card">
      <div class="filter-container">
        <el-form :inline="true" :model="filterForm" class="filter-form">
          <el-form-item label="单号">
            <el-input v-model="filterForm.code" placeholder="维修单号" clearable />
          </el-form-item>
          <el-form-item label="资产信息">
            <el-input 
              v-model="filterForm.assetKeyword" 
              placeholder="资产名称/编号/SN" 
              clearable 
            />
          </el-form-item>
          <el-form-item label="类型">
            <el-select 
              v-model="filterForm.type" 
              placeholder="全部类型" 
              clearable
            >
              <el-option 
                v-for="item in typeOptions" 
                :key="item.value" 
                :label="item.label" 
                :value="item.value" 
              />
            </el-select>
          </el-form-item>
          <el-form-item label="状态">
            <el-select 
              v-model="filterForm.status" 
              placeholder="全部状态" 
              clearable
            >
              <el-option 
                v-for="item in statusOptions" 
                :key="item.value" 
                :label="item.label" 
                :value="item.value" 
              />
            </el-select>
          </el-form-item>
          <el-form-item label="申请时间">
            <el-date-picker
              v-model="filterForm.timeRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch" :icon="Search">
              搜索
            </el-button>
            <el-button @click="resetFilter" :icon="RefreshRight">
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="data-card">
      <el-tabs v-model="activeTab" @tab-click="handleTabChange">
        <el-tab-pane label="全部" name="all"></el-tab-pane>
        <el-tab-pane label="待发出" name="pending"></el-tab-pane>
        <el-tab-pane label="维修中" name="repairing"></el-tab-pane>
        <el-tab-pane label="已完成" name="completed"></el-tab-pane>
      </el-tabs>
      
      <el-table
        ref="maintenanceTable"
        v-loading="loading"
        :data="maintenanceList"
        border
        style="width: 100%"
      >
        <el-table-column prop="code" label="维修单号" width="150" sortable />
        <el-table-column prop="assetInfo" label="资产信息" width="220" show-overflow-tooltip>
          <template #default="scope">
            <div class="asset-info">
              <div class="asset-name">{{ scope.row.assetName }}</div>
              <div class="asset-code text-secondary">{{ scope.row.assetCode }}</div>
              <div class="asset-sn text-secondary">SN: {{ scope.row.assetSn }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="type" label="类型" width="100">
          <template #default="scope">
            <el-tag :type="getTypeTag(scope.row.type)" size="small">
              {{ getTypeLabel(scope.row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="120">
          <template #default="scope">
            <el-tag :type="getStatusTag(scope.row.status)" size="small">
              {{ getStatusLabel(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="vendor" label="维修厂商" width="150" />
        <el-table-column prop="applicant" label="申请人" width="100" />
        <el-table-column prop="applyTime" label="申请时间" width="180" sortable />
        <el-table-column prop="sendTime" label="发出时间" width="180" sortable />
        <el-table-column prop="estimatedReturnTime" label="预计返回时间" width="180" />
        <el-table-column prop="actualReturnTime" label="实际返回时间" width="180" />
        <el-table-column prop="cost" label="维修费用" width="120">
          <template #default="scope">
            {{ scope.row.cost ? `￥${scope.row.cost}` : '-' }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="240" fixed="right">
          <template #default="scope">
            <el-button 
              type="primary" 
              text 
              size="small" 
              @click="handleViewDetail(scope.row)"
              :icon="View"
            >
              详情
            </el-button>
            <el-button 
              type="success" 
              text 
              size="small" 
              @click="handleUpdateStatus(scope.row, 'send')"
              :icon="TopRight"
              v-if="scope.row.status === 'pending'"
            >
              发出
            </el-button>
            <el-button 
              type="warning" 
              text 
              size="small" 
              @click="handleUpdateStatus(scope.row, 'receive')"
              :icon="BottomLeft"
              v-if="scope.row.status === 'repairing'"
            >
              接收
            </el-button>
            <el-button 
              type="danger" 
              text 
              size="small" 
              @click="handleDelete(scope.row)"
              :icon="Delete"
              v-if="scope.row.status === 'pending'"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :background="true"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 创建维修单对话框 -->
    <RepairOrderDialog
      v-model:visible="repairOrderDialogVisible"
      :order-data="currentRepairOrder"
      :mode="dialogMode"
      @submit="handleRepairOrderSubmit"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search, Plus, Download, View, Delete,
  TopRight, BottomLeft, RefreshRight
} from '@element-plus/icons-vue'
import returnToFactoryApi from '@/api/returnToFactory'
import { getRepairOrders, createRepairOrder } from '@/api/spareparts'
import RepairOrderDialog from '@/views/spareparts/components/RepairOrderDialog.vue'

// 数据加载状态
const loading = ref(false)
const activeTab = ref('all')

// 维修单列表数据
const maintenanceList = ref([])
const maintenanceTable = ref(null)

// 对话框相关
const repairOrderDialogVisible = ref(false)
const currentRepairOrder = ref({})
const dialogMode = ref('create')

// 分页配置
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 筛选表单
const filterForm = reactive({
  code: '',
  assetKeyword: '',
  type: '',
  status: '',
  timeRange: []
})

// 类型选项
const typeOptions = [
  { label: '返厂维修', value: 'factory' },
  { label: '第三方维修', value: 'third_party' },
  { label: '保修服务', value: 'warranty' }
]

// 状态选项
const statusOptions = [
  { label: '待发出', value: 'pending' },
  { label: '维修中', value: 'repairing' },
  { label: '已完成', value: 'completed' },
  { label: '已取消', value: 'cancelled' }
]

// 生命周期钩子
onMounted(() => {
  fetchMaintenanceList()
})

// 获取维修单列表
const fetchMaintenanceList = async () => {
  loading.value = true

  // 构建查询参数
  const params = {
    pageNumber: pagination.currentPage,
    pageSize: pagination.pageSize,
    searchTerm: filterForm.code || filterForm.assetKeyword,
    type: filterForm.type,
    status: activeTab.value === 'all' ? filterForm.status : activeTab.value,
    startDate: filterForm.timeRange?.[0],
    endDate: filterForm.timeRange?.[1]
  }

  try {
    // 调用维修单API获取维修列表
    const response = await getRepairOrders(params)

    if (response.success) {
      // 转换数据格式以匹配前端显示
      const items = response.data.items || response.data || []
      maintenanceList.value = items.map(item => ({
        id: item.id,
        code: item.orderCode,
        assetName: item.repairItems?.[0]?.assetName || '未知资产',
        assetCode: item.repairItems?.[0]?.assetCode || '',
        assetSn: item.repairItems?.[0]?.assetSn || '',
        type: mapRepairType(item.type),
        status: mapRepairStatus(item.status),
        priority: item.priority,
        supplierName: item.supplierName,
        estimatedCost: item.estimatedCost,
        actualCost: item.actualCost,
        createdAt: item.createdAt,
        expectedReturnDate: item.expectedReturnDate,
        actualReturnDate: item.actualReturnDate,
        notes: item.notes
      }))
      pagination.total = response.data.total || items.length || 0
    } else {
      ElMessage.error(response.message || '获取维修列表失败')
      maintenanceList.value = []
      pagination.total = 0
    }
  } catch (error) {
    console.error('获取维修列表失败:', error)
    ElMessage.error('获取维修列表失败')
    maintenanceList.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

// Tab切换
const handleTabChange = () => {
  pagination.currentPage = 1
  fetchMaintenanceList()
}

// 搜索
const handleSearch = () => {
  pagination.currentPage = 1
  fetchMaintenanceList()
}

// 重置筛选条件
const resetFilter = () => {
  filterForm.code = ''
  filterForm.assetKeyword = ''
  filterForm.type = ''
  filterForm.status = ''
  filterForm.timeRange = []
  
  pagination.currentPage = 1
  fetchMaintenanceList()
}

// 分页事件
const handleSizeChange = (size) => {
  pagination.pageSize = size
  fetchMaintenanceList()
}

const handleCurrentChange = (page) => {
  pagination.currentPage = page
  fetchMaintenanceList()
}

// 详情
const handleViewDetail = (row) => {
  ElMessage.info(`查看维修单详情：${row.code}`)
  // 实际项目中可以跳转到详情页或打开详情对话框
  // router.push(`/faults/maintenance/detail/${row.id}`)
}

// 更新状态
const handleUpdateStatus = (row, action) => {
  let title = ''
  let content = ''
  let successMsg = ''
  
  if (action === 'send') {
    title = '确认发出'
    content = `确认将资产 ${row.assetName} 发出维修吗？`
    successMsg = '已更新为维修中状态'
  } else if (action === 'receive') {
    title = '确认接收'
    content = `确认已接收维修完成的资产 ${row.assetName} 吗？`
    successMsg = '已更新为维修完成状态'
  }
  
  ElMessageBox.confirm(content, title, {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      // 调用真实API更新维修状态
      const response = await returnToFactoryApi.updateReturnStatus(row.id, {
        status: action === 'send' ? 'sent' : 'returned',
        notes: `${action === 'send' ? '发出维修' : '接收维修完成'} - ${new Date().toLocaleString()}`
      })

      if (response.success) {
        ElMessage.success(successMsg)
        // 刷新列表
        fetchMaintenanceList()
      } else {
        ElMessage.error(response.message || '状态更新失败')
      }
    } catch (error) {
      console.error('状态更新失败:', error)
      ElMessage.error('状态更新失败')
    }
  }).catch(() => {
    // 取消操作
  })
}

// 删除维修单
const handleDelete = (row) => {
  ElMessageBox.confirm(`确认删除维修单 ${row.code} 吗？`, '删除确认', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      // 调用真实API删除维修记录
      const response = await returnToFactoryApi.deleteReturnToFactory(row.id)

      if (response.success) {
        ElMessage.success('删除成功')
        // 刷新列表
        fetchMaintenanceList()
      } else {
        ElMessage.error(response.message || '删除失败')
      }
    } catch (error) {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }).catch(() => {
    // 取消操作
  })
}

// 新建维修单
const handleAddMaintenance = () => {
  currentRepairOrder.value = {}
  dialogMode.value = 'create'
  repairOrderDialogVisible.value = true
}

// 处理维修单提交
const handleRepairOrderSubmit = async (orderData) => {
  try {
    const response = await createRepairOrder(orderData)
    if (response.success) {
      ElMessage.success('维修单创建成功')
      repairOrderDialogVisible.value = false
      // 刷新列表
      await fetchMaintenanceList()
    } else {
      ElMessage.error(response.message || '维修单创建失败')
    }
  } catch (error) {
    console.error('维修单创建失败:', error)
    ElMessage.error('维修单创建失败')
  }
}

// 导出数据
const handleExportData = () => {
  ElMessage.success('开始导出数据，请稍候...')
  // 实际项目中调用导出API
  // exportMaintenanceList(filterForm).then(() => {
  //   ElMessage.success('导出成功')
  // })
}

// 工具方法：获取类型标签样式
const getTypeTag = (type) => {
  const map = {
    'factory': 'primary',
    'third_party': 'warning',
    'warranty': 'success'
  }
  return map[type] || ''
}

// 工具方法：获取类型文本
const getTypeLabel = (type) => {
  const map = {
    'factory': '返厂维修',
    'third_party': '第三方维修',
    'warranty': '保修服务'
  }
  return map[type] || '未知'
}

// 工具方法：获取状态标签样式
const getStatusTag = (status) => {
  const map = {
    'pending': 'info',
    'repairing': 'warning',
    'completed': 'success',
    'cancelled': 'danger'
  }
  return map[status] || ''
}

// 工具方法：获取状态文本
const getStatusLabel = (status) => {
  const map = {
    'pending': '待发出',
    'repairing': '维修中',
    'completed': '已完成',
    'cancelled': '已取消'
  }
  return map[status] || '未知'
}

// 数据映射函数
const mapRepairType = (type) => {
  const typeMap = {
    1: 'factory',      // 返厂维修
    2: 'third_party',  // 第三方维修
    3: 'warranty'      // 保修服务
  }
  return typeMap[type] || 'factory'
}

const mapRepairStatus = (status) => {
  const statusMap = {
    0: 'pending',      // 待审批
    1: 'pending',      // 待发出
    2: 'repairing',    // 维修中
    3: 'completed',    // 已完成
    4: 'cancelled'     // 已取消
  }
  return statusMap[status] || 'pending'
}
</script>

<style lang="scss" scoped>
@use "@/styles/variables.scss" as *;

.maintenance-container {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    
    .page-title {
      font-size: 20px;
      font-weight: 600;
      color: #303133;
      margin: 0;
    }
    
    .page-actions {
      display: flex;
      gap: 8px;
    }
  }
  
  .filter-card {
    margin-bottom: 16px;
    
    .filter-container {
      .filter-form {
        display: flex;
        flex-wrap: wrap;
      }
    }
  }
  
  .data-card {
    .pagination-container {
      margin-top: 16px;
      display: flex;
      justify-content: flex-end;
    }
    
    .asset-info {
      .asset-name {
        font-weight: 500;
      }
      
      .asset-code, .asset-sn {
        font-size: 12px;
        color: #909399;
      }
    }
  }
  
  .text-secondary {
    color: #909399;
  }
}
</style>