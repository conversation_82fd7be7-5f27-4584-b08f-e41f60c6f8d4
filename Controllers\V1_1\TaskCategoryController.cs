using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Logging;
using ItAssetsSystem.Application.Features.Tasks.Services;
using ItAssetsSystem.Application.Features.Tasks.Dtos;
using ItAssetsSystem.Core.Extensions;
using System.Security.Claims;

namespace ItAssetsSystem.Controllers.V1_1
{
    /// <summary>
    /// 任务分类管理控制器 V1.1
    /// </summary>
    [ApiController]
    [Route("api/v1.1/task-categories")]
    [Authorize]
    public class TaskCategoryController : ControllerBase
    {
        private readonly ITaskCategoryService _taskCategoryService;
        private readonly ILogger<TaskCategoryController> _logger;

        public TaskCategoryController(
            ITaskCategoryService taskCategoryService,
            ILogger<TaskCategoryController> logger)
        {
            _taskCategoryService = taskCategoryService;
            _logger = logger;
        }

        /// <summary>
        /// 获取所有任务分类
        /// </summary>
        /// <returns>任务分类列表</returns>
        [HttpGet]
        public async Task<IActionResult> GetAllCategories(CancellationToken cancellationToken = default)
        {
            _logger.LogInformation("V1.1 获取所有任务分类");

            try
            {
                var result = await _taskCategoryService.GetAllAsync(cancellationToken);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "V1.1 获取所有任务分类时发生错误");
                return StatusCode(500, new
                {
                    success = false,
                    message = "获取任务分类失败",
                    version = "v1.1"
                });
            }
        }

        /// <summary>
        /// 获取启用的任务分类（简化版本，用于下拉选择）
        /// </summary>
        /// <returns>启用的任务分类列表</returns>
        [HttpGet("active")]
        [AllowAnonymous]
        public async Task<IActionResult> GetActiveCategories(CancellationToken cancellationToken = default)
        {
            _logger.LogInformation("V1.1 获取启用的任务分类");

            try
            {
                var result = await _taskCategoryService.GetActiveSimpleAsync(cancellationToken);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "V1.1 获取启用的任务分类时发生错误");
                return StatusCode(500, new
                {
                    success = false,
                    message = "获取任务分类失败",
                    version = "v1.1"
                });
            }
        }

        /// <summary>
        /// 根据ID获取任务分类详情
        /// </summary>
        /// <param name="id">分类ID</param>
        /// <returns>任务分类详情</returns>
        [HttpGet("{id}")]
        public async Task<IActionResult> GetCategoryById(int id, CancellationToken cancellationToken = default)
        {
            _logger.LogInformation("V1.1 获取任务分类详情: CategoryId={CategoryId}", id);

            try
            {
                var result = await _taskCategoryService.GetByIdAsync(id, cancellationToken);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "V1.1 获取任务分类详情时发生错误: CategoryId={CategoryId}", id);
                return StatusCode(500, new
                {
                    success = false,
                    message = "获取任务分类详情失败",
                    version = "v1.1"
                });
            }
        }

        /// <summary>
        /// 分页查询任务分类
        /// </summary>
        /// <param name="request">查询请求</param>
        /// <returns>分页结果</returns>
        [HttpPost("search")]
        public async Task<IActionResult> SearchCategories([FromBody] TaskCategoryQueryRequestDto request, CancellationToken cancellationToken = default)
        {
            _logger.LogInformation("V1.1 分页查询任务分类: Page={PageNumber}, Size={PageSize}", request.PageNumber, request.PageSize);

            try
            {
                var result = await _taskCategoryService.GetPagedAsync(request, cancellationToken);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "V1.1 分页查询任务分类时发生错误");
                return StatusCode(500, new
                {
                    success = false,
                    message = "查询任务分类失败",
                    version = "v1.1"
                });
            }
        }

        /// <summary>
        /// 创建任务分类
        /// </summary>
        /// <param name="request">创建请求</param>
        /// <returns>创建结果</returns>
        [HttpPost]
        public async Task<IActionResult> CreateCategory([FromBody] CreateTaskCategoryRequestDto request, CancellationToken cancellationToken = default)
        {
            _logger.LogInformation("V1.1 创建任务分类: {CategoryName}", request.Name);

            try
            {
                var currentUserId = GetCurrentUserId();
                var result = await _taskCategoryService.CreateAsync(request, currentUserId, cancellationToken);
                
                if (result.Success)
                {
                    return CreatedAtAction(nameof(GetCategoryById), new { id = result.Data!.CategoryId }, result);
                }
                
                return BadRequest(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "V1.1 创建任务分类时发生错误: {CategoryName}", request.Name);
                return StatusCode(500, new
                {
                    success = false,
                    message = "创建任务分类失败",
                    version = "v1.1"
                });
            }
        }

        /// <summary>
        /// 更新任务分类
        /// </summary>
        /// <param name="id">分类ID</param>
        /// <param name="request">更新请求</param>
        /// <returns>更新结果</returns>
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateCategory(int id, [FromBody] UpdateTaskCategoryRequestDto request, CancellationToken cancellationToken = default)
        {
            _logger.LogInformation("V1.1 更新任务分类: CategoryId={CategoryId}, Name={CategoryName}", id, request.Name);

            try
            {
                var result = await _taskCategoryService.UpdateAsync(id, request, cancellationToken);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "V1.1 更新任务分类时发生错误: CategoryId={CategoryId}", id);
                return StatusCode(500, new
                {
                    success = false,
                    message = "更新任务分类失败",
                    version = "v1.1"
                });
            }
        }

        /// <summary>
        /// 删除任务分类
        /// </summary>
        /// <param name="id">分类ID</param>
        /// <returns>删除结果</returns>
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteCategory(int id, CancellationToken cancellationToken = default)
        {
            _logger.LogInformation("V1.1 删除任务分类: CategoryId={CategoryId}", id);

            try
            {
                var result = await _taskCategoryService.DeleteAsync(id, cancellationToken);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "V1.1 删除任务分类时发生错误: CategoryId={CategoryId}", id);
                return StatusCode(500, new
                {
                    success = false,
                    message = "删除任务分类失败",
                    version = "v1.1"
                });
            }
        }

        /// <summary>
        /// 更新分类排序
        /// </summary>
        /// <param name="id">分类ID</param>
        /// <param name="request">排序更新请求</param>
        /// <returns>更新结果</returns>
        [HttpPatch("{id}/sort")]
        public async Task<IActionResult> UpdateSortOrder(int id, [FromBody] UpdateSortOrderRequest request, CancellationToken cancellationToken = default)
        {
            _logger.LogInformation("V1.1 更新分类排序: CategoryId={CategoryId}, Direction={Direction}", id, request.Direction);

            try
            {
                var result = await _taskCategoryService.UpdateSortOrderAsync(id, request.Direction, cancellationToken);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "V1.1 更新分类排序时发生错误: CategoryId={CategoryId}", id);
                return StatusCode(500, new
                {
                    success = false,
                    message = "更新分类排序失败",
                    version = "v1.1"
                });
            }
        }

        /// <summary>
        /// 获取当前用户ID
        /// </summary>
        /// <returns>当前用户ID</returns>
        private int GetCurrentUserId()
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? User.FindFirst("uid")?.Value;
            if (int.TryParse(userIdClaim, out var userId))
            {
                return userId;
            }
            _logger.LogWarning("Unable to parse UserID from token claims");
            return 1; // 默认返回1用于测试
        }
    }
}
