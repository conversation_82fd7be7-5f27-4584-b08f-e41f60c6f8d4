-- IT资产管理系统 - 简化优化脚本
-- 版本: v2.2 (最小化稳定版)
-- 目标: 确保所有视图能正确创建并运行

-- ============================================================================
-- 1. 创建简化的核心资产视图
-- ============================================================================

DROP VIEW IF EXISTS v_assets_enhanced;

CREATE VIEW v_assets_enhanced AS
SELECT 
    -- 基础信息
    a.Id AS AssetId,
    a.assetCode AS AssetCode,
    a.Name AS AssetName,
    a.Status,
    COALESCE(a.Price, 0) AS Price,
    a.CreatedAt,
    a.UpdatedAt,
    
    -- 资产类型
    a.AssetTypeId,
    COALESCE(at.Name, '未分类') AS AssetTypeName,
    COALESCE(at.Code, 'UNKNOWN') AS AssetTypeCode,
    
    -- 位置信息
    a.LocationId AS CurrentLocationId,
    COALESCE(l.Name, '未指定位置') AS CurrentLocationName,
    COALESCE(l.Code, '') AS CurrentLocationCode,
    COALESCE(l.Path, CAST(a.LocationId AS CHAR)) AS LocationPath,
    
    -- 部门继承
    COALESCE(l.DefaultDepartmentId, 0) AS InheritedDepartmentId,
    COALESCE(d.Name, '未分配部门') AS DepartmentName,
    COALESCE(d.Code, '') AS DepartmentCode,
    COALESCE(d.Id, 0) AS DepartmentId,
    
    -- 区域ID (简化版本)
    COALESCE(a.LocationId, 0) AS RegionId,
    COALESCE(l.Name, '未知区域') AS RegionName,
    
    -- 状态文本
    CASE a.Status
        WHEN 0 THEN '闲置'
        WHEN 1 THEN '在用'
        WHEN 2 THEN '维修中'
        WHEN 3 THEN '报废'
        WHEN 4 THEN '故障'
        ELSE '未知'
    END AS StatusText,
    
    CASE a.Status
        WHEN 0 THEN 'idle'
        WHEN 1 THEN 'in_use'
        WHEN 2 THEN 'maintenance'
        WHEN 3 THEN 'scrapped'
        WHEN 4 THEN 'faulty'
        ELSE 'unknown'
    END AS StatusCategory,
    
    -- 布尔字段
    CASE WHEN a.Status = 1 THEN 1 ELSE 0 END AS IsInUse,
    CASE WHEN a.Status = 0 THEN 1 ELSE 0 END AS IsIdle,
    CASE WHEN a.Status = 2 THEN 1 ELSE 0 END AS IsMaintenance,
    CASE WHEN a.Status = 4 THEN 1 ELSE 0 END AS IsFaulty,
    CASE WHEN a.Status = 3 THEN 1 ELSE 0 END AS IsScrapped,
    
    -- 时间字段
    TIMESTAMPDIFF(MONTH, a.CreatedAt, NOW()) AS AssetAgeMonths

FROM assets a
LEFT JOIN assettypes at ON a.AssetTypeId = at.Id
LEFT JOIN locations l ON a.LocationId = l.Id
LEFT JOIN departments d ON d.Id = l.DefaultDepartmentId;

-- ============================================================================
-- 2. 创建KPI视图
-- ============================================================================

DROP VIEW IF EXISTS v_asset_kpi_enhanced;

CREATE VIEW v_asset_kpi_enhanced AS
SELECT 
    COUNT(*) AS TotalAssets,
    SUM(Price) AS TotalValue,
    ROUND(SUM(Price) / 10000, 2) AS TotalValueWan,
    
    SUM(IsInUse) AS InUseAssets,
    SUM(IsIdle) AS IdleAssets,
    SUM(IsMaintenance) AS MaintenanceAssets,
    SUM(IsFaulty) AS FaultAssets,
    SUM(IsScrapped) AS ScrappedAssets,
    
    ROUND(SUM(IsInUse) * 100.0 / COUNT(*), 2) AS InUseRate,
    ROUND(SUM(IsIdle) * 100.0 / COUNT(*), 2) AS IdleRate,
    ROUND(SUM(IsMaintenance) * 100.0 / COUNT(*), 2) AS MaintenanceRate,
    ROUND(SUM(IsFaulty) * 100.0 / COUNT(*), 2) AS FaultRate,
    ROUND(SUM(IsScrapped) * 100.0 / COUNT(*), 2) AS ScrappedRate,
    
    ROUND(
        SUM(IsInUse) * 100.0 / COUNT(*) - 
        SUM(IsIdle) * 30.0 / COUNT(*) - 
        SUM(IsMaintenance) * 50.0 / COUNT(*), 
        2
    ) AS AverageUtilization,
    
    ROUND(
        100 - SUM(IsFaulty) * 100.0 / COUNT(*) - SUM(IsScrapped) * 100.0 / COUNT(*), 
        2
    ) AS HealthScore,
    
    COUNT(DISTINCT AssetTypeId) AS TypeCount,
    COUNT(DISTINCT CASE WHEN InheritedDepartmentId > 0 THEN InheritedDepartmentId END) AS DepartmentCount,
    COUNT(DISTINCT RegionId) AS RegionCount,
    
    MIN(CreatedAt) AS EarliestAsset,
    MAX(CreatedAt) AS LatestAsset,
    ROUND(AVG(AssetAgeMonths), 1) AS AverageAgeMonths,
    
    ROUND(AVG(Price), 2) AS AverageValue,
    MAX(Price) AS MaxValue,
    MIN(CASE WHEN Price > 0 THEN Price END) AS MinValue,
    
    (SELECT COUNT(*) FROM assets a_sub WHERE a_sub.CreatedAt >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)) AS NewAssetsLast30Days,
    (SELECT COUNT(*) FROM assets a_sub WHERE a_sub.CreatedAt >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)) AS NewAssetsLast7Days

FROM v_assets_enhanced;

-- ============================================================================
-- 3. 创建统计视图
-- ============================================================================

DROP VIEW IF EXISTS v_asset_statistics_fast;

CREATE VIEW v_asset_statistics_fast AS

-- 按资产类型统计
SELECT 
    'type' AS DimensionType,
    CAST(AssetTypeId AS CHAR) AS DimensionKey,
    AssetTypeName AS DimensionName,
    AssetTypeCode AS DimensionCode,
    NULL AS ParentKey,
    COUNT(*) AS TotalCount,
    SUM(IsInUse) AS InUseCount,
    SUM(IsIdle) AS IdleCount,
    SUM(IsMaintenance) AS MaintenanceCount,
    SUM(IsFaulty) AS FaultCount,
    SUM(IsScrapped) AS ScrappedCount,
    SUM(Price) AS TotalValue,
    ROUND(AVG(Price), 2) AS AverageValue,
    ROUND(SUM(IsInUse) * 100.0 / COUNT(*), 2) AS InUseRate,
    ROUND(SUM(IsFaulty) * 100.0 / COUNT(*), 2) AS FaultRate,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM assets), 2) AS Percentage,
    MIN(CreatedAt) AS EarliestCreated,
    MAX(CreatedAt) AS LatestCreated,
    COUNT(DISTINCT DepartmentId) AS DepartmentCount,
    COUNT(DISTINCT RegionId) AS RegionCount
FROM v_assets_enhanced
GROUP BY AssetTypeId, AssetTypeName, AssetTypeCode

UNION ALL

-- 按部门统计
SELECT 
    'department' AS DimensionType,
    CAST(InheritedDepartmentId AS CHAR) AS DimensionKey,
    DepartmentName AS DimensionName,
    DepartmentCode AS DimensionCode,
    NULL AS ParentKey,
    COUNT(*) AS TotalCount,
    SUM(IsInUse) AS InUseCount,
    SUM(IsIdle) AS IdleCount,
    SUM(IsMaintenance) AS MaintenanceCount,
    SUM(IsFaulty) AS FaultCount,
    SUM(IsScrapped) AS ScrappedCount,
    SUM(Price) AS TotalValue,
    ROUND(AVG(Price), 2) AS AverageValue,
    ROUND(SUM(IsInUse) * 100.0 / COUNT(*), 2) AS InUseRate,
    ROUND(SUM(IsFaulty) * 100.0 / COUNT(*), 2) AS FaultRate,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM assets), 2) AS Percentage,
    MIN(CreatedAt) AS EarliestCreated,
    MAX(CreatedAt) AS LatestCreated,
    COUNT(DISTINCT AssetTypeId) AS DepartmentCount,
    COUNT(DISTINCT RegionId) AS RegionCount
FROM v_assets_enhanced
WHERE InheritedDepartmentId IS NOT NULL AND InheritedDepartmentId > 0
GROUP BY InheritedDepartmentId, DepartmentName, DepartmentCode

UNION ALL

-- 按状态统计
SELECT 
    'status' AS DimensionType,
    CAST(Status AS CHAR) AS DimensionKey,
    StatusText AS DimensionName,
    StatusCategory AS DimensionCode,
    NULL AS ParentKey,
    COUNT(*) AS TotalCount,
    SUM(IsInUse) AS InUseCount,
    SUM(IsIdle) AS IdleCount,
    SUM(IsMaintenance) AS MaintenanceCount,
    SUM(IsFaulty) AS FaultCount,
    SUM(IsScrapped) AS ScrappedCount,
    SUM(Price) AS TotalValue,
    ROUND(AVG(Price), 2) AS AverageValue,
    ROUND(SUM(IsInUse) * 100.0 / COUNT(*), 2) AS InUseRate,
    ROUND(SUM(IsFaulty) * 100.0 / COUNT(*), 2) AS FaultRate,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM assets), 2) AS Percentage,
    MIN(CreatedAt) AS EarliestCreated,
    MAX(CreatedAt) AS LatestCreated,
    COUNT(DISTINCT AssetTypeId) AS DepartmentCount,
    COUNT(DISTINCT InheritedDepartmentId) AS RegionCount
FROM v_assets_enhanced
GROUP BY Status, StatusText, StatusCategory;

-- ============================================================================
-- 4. 验证视图创建
-- ============================================================================

SELECT 'Views Created Successfully' AS Status;
SELECT COUNT(*) AS enhanced_view_count FROM v_assets_enhanced;
SELECT COUNT(*) AS kpi_view_count FROM v_asset_kpi_enhanced;
SELECT COUNT(*) AS statistics_view_count FROM v_asset_statistics_fast;

-- 显示KPI示例数据
SELECT 'KPI Sample Data' AS Title;
SELECT * FROM v_asset_kpi_enhanced;

SELECT 'Script completed successfully!' AS Result;