// File: Application/Features/Notes/Dtos/QuickMemoCategoryDto.cs
// Description: DTO for QuickMemoCategory responses.
#nullable enable
namespace ItAssetsSystem.Application.Features.Notes.Dtos
{
    public class QuickMemoCategoryDto
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string? Color { get; set; }
        // public int UserId { get; set; } // Typically not exposed in DTO unless specifically needed
        // public DateTime CreatedAt { get; set; } // Typically not exposed unless needed
        // public DateTime UpdatedAt { get; set; } // Typically not exposed unless needed
    }
} 