using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ItAssetsSystem.Models.Entities.Gamification
{
    /// <summary>
    /// 行为类型配置实体
    /// 标准化行为配置表
    /// </summary>
    [Table("behavior_type_configs")]
    public class BehaviorTypeConfig
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Key]
        [Column("id")]
        public int Id { get; set; }

        /// <summary>
        /// 行为类型代码（唯一）
        /// </summary>
        [Column("action_type")]
        [MaxLength(50)]
        public string ActionType { get; set; } = string.Empty;

        /// <summary>
        /// 行为分类
        /// </summary>
        [Column("action_category")]
        [MaxLength(20)]
        public string ActionCategory { get; set; } = string.Empty;

        /// <summary>
        /// 显示名称
        /// </summary>
        [Column("display_name")]
        [MaxLength(100)]
        public string DisplayName { get; set; } = string.Empty;

        /// <summary>
        /// 行为描述
        /// </summary>
        [Column("description")]
        public string? Description { get; set; }

        /// <summary>
        /// 基础积分
        /// </summary>
        [Column("base_points")]
        public int BasePoints { get; set; } = 0;

        /// <summary>
        /// 基础金币
        /// </summary>
        [Column("base_coins")]
        public int BaseCoins { get; set; } = 0;

        /// <summary>
        /// 基础钻石
        /// </summary>
        [Column("base_diamonds")]
        public int BaseDiamonds { get; set; } = 0;

        /// <summary>
        /// 基础经验值
        /// </summary>
        [Column("base_xp")]
        public int BaseXp { get; set; } = 0;

        /// <summary>
        /// 奖励倍数
        /// </summary>
        [Column("multiplier")]
        public decimal Multiplier { get; set; } = 1.00m;

        /// <summary>
        /// 每日限制次数
        /// </summary>
        [Column("daily_limit")]
        public int? DailyLimit { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        [Column("is_active")]
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// 创建时间
        /// </summary>
        [Column("created_at")]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新时间
        /// </summary>
        [Column("updated_at")]
        public DateTime UpdatedAt { get; set; } = DateTime.Now;
    }
}
