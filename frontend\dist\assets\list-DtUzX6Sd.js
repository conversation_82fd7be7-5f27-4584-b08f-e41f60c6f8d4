import{_ as e,r as a,ad as l,aN as t,z as s,E as o,b as r,d as n,e as u,w as i,l as d,m as c,k as p,p as v,t as m,ax as f,aO as y,a as g,o as h,F as w,h as b,$ as _,f as C,aj as V,aM as N,q as T,aI as k,a9 as I,aP as x,aQ as A,aR as S,aS as $,C as z,aT as O,aU as U}from"./index-CG5lHOPO.js";import{g as j}from"./assetType-FfrNKE6C.js";import{l as M}from"./location-DxYD9YPx.js";import{f as E}from"./format-DfhXadVZ.js";const R={class:"asset-list"},q={class:"sticky-header"},L={class:"page-header"},D={class:"header-actions"},B={class:"content-area"},P={class:"pagination-container"},F={key:0,style:{"margin-top":"10px",padding:"10px",background:"#f5f5f5","border-radius":"4px","font-size":"12px"}},H={key:0,class:"asset-title"},J={class:"history-table"},W={key:0,class:"empty-history"},G={key:0,class:"changes-list"},Q={class:"field-name"},X={class:"change-values"},K={class:"change-value-old"},Y={class:"change-value-new"},Z={key:1,class:"unknown-change"},ee={key:1,class:"pagination-container"},ae={class:"import-dialog-content"},le={class:"import-steps"},te={class:"step"},se={class:"step"},oe={class:"step"},re={class:"template-download"},ne={key:0,class:"import-progress"},ue={key:1,class:"import-result"},ie={class:"result-info"},de={key:0,class:"error-messages"},ce={class:"message-list"},pe={key:1,class:"warning-messages"},ve={class:"message-list"},me={class:"dialog-footer"},fe=e({__name:"list",setup(e){const fe=a(!1),ye=a([]),ge=a([]),he=a([]),we=a(0),be=a(1),_e=a(20),Ce=l({assetCode:"",name:"",assetTypeName:"",statusName:""}),Ve=a(!1),Ne=a({}),Te=a(!1),ke=a(!1),Ie=a(null),xe=l({id:"",assetCode:"",financialCode:"",name:"",assetTypeId:void 0,model:"",brand:"",locationId:void 0,status:1,notes:""}),Ae={assetCode:[{required:!0,message:"请输入资产编号",trigger:"blur"}],name:[{required:!0,message:"请输入资产名称",trigger:"blur"}],assetTypeId:[{required:!0,message:"请选择资产类型",trigger:"change"}],model:[{required:!0,message:"请输入规格型号",trigger:"blur"}],brand:[{required:!0,message:"请输入品牌",trigger:"blur"}]},Se=a(!1),$e=a(!1),ze=a([]),Oe=a(1),Ue=a(10),je=a(0),Me=a(!1),Ee=a(null),Re=a(null),qe=a(!1),Le=a(0),De=a(null);t();const Be=a([]),Pe=async()=>{try{const e=await j();if(e&&e.success)if(Array.isArray(e.data))ge.value=e.data;else if(e.data&&"object"==typeof e.data){let a=!1;for(const l in e.data)if(Array.isArray(e.data[l])){ge.value=e.data[l],a=!0;break}a||(ge.value=[],o.warning("获取资产类型数据格式错误"))}else ge.value=[],o.warning("获取资产类型失败：数据格式错误");else ge.value=[],o.warning("获取资产类型列表失败")}catch(e){ge.value=[],o.warning("获取资产类型列表失败："+(e.message||"未知错误"))}return ge.value},Fe=async()=>{try{const e=await M.getLocationsForDropdown({includeAll:!0});e.success&&(he.value=e.data||[])}catch(e){}},He=async()=>{fe.value=!0,ye.value=[],we.value=0;try{const e=await y({pageIndex:be.value,pageSize:_e.value,...Ce});if(e&&e.success){if(e.data&&(e.data.items&&Array.isArray(e.data.items)?(ye.value=e.data.items,we.value=e.data.totalCount||e.data.total||e.data.items.length):Array.isArray(e.data)?(ye.value=e.data,we.value=e.data.length):e.items&&Array.isArray(e.items)?(ye.value=e.items,we.value=e.total||e.items.length):Object.keys(e).forEach((a=>{Array.isArray(e[a])&&e[a].length>0&&(ye.value=e[a],we.value=e[a].length),"object"==typeof e[a]&&null!==e[a]&&Object.keys(e[a]).forEach((l=>{Array.isArray(e[a][l])&&e[a][l].length>0&&(ye.value=e[a][l],e[a].totalCount||e[a].total?we.value=e[a].totalCount||e[a].total:we.value=e[a][l].length)}))}))),void 0!==e.total?we.value=e.total:void 0!==e.totalCount&&(we.value=e.totalCount),0===we.value&&e.data&&Array.isArray(e.data.items))for(const a in e)if("total"===a&&"number"==typeof e[a]){we.value=e[a];break}if(ye.value.length>0){Math.min(_e.value,Math.max(0,we.value-(be.value-1)*_e.value)),Math.ceil(we.value/_e.value);ye.value.length,f((()=>{document.querySelectorAll(".el-pager .number").forEach(((e,a)=>{}))}))}}else o.error((null==e?void 0:e.message)||"获取资产列表失败")}catch(e){o.error("获取资产列表失败: "+(e.message||"未知错误"))}finally{fe.value=!1,f((()=>{const e=document.querySelector(".el-table__body-wrapper");e&&(e.scrollTop=0);const a=document.querySelector(".el-table");a&&a.scrollIntoView({behavior:"smooth",block:"start"})}))}},Je=async e=>{if(!e)return o.error("获取资产详情失败: 资产ID无效"),null;try{const a=await O(e);return a&&a.success?a.data:(o.error((null==a?void 0:a.message)||"获取资产详情失败"),null)}catch(a){return o.error("获取资产详情失败: "+(a.message||"网络错误")),null}},We=async e=>{var a;$e.value=!0;try{const l={page:Oe.value,pageSize:Ue.value},t=await U(e,l);!0===t.success&&t.data?t.data.history?(ze.value=t.data.history.map((e=>wa(e)))||[],je.value=(null==(a=t.data.history)?void 0:a.length)||0,t.data.asset&&(Ne.value={...Ne.value,...t.data.asset})):t.data.items?(ze.value=t.data.items.map((e=>wa(e)))||[],je.value=t.data.total||0):(ze.value=Array.isArray(t.data)?t.data.map((e=>wa(e))):[],je.value=Array.isArray(t.data)?t.data.length:0):(o.error(t.message||"获取资产历史记录失败"),ze.value=[],je.value=0)}catch(l){o.error("获取资产历史记录失败: "+(l.message||"未知错误")),ze.value=[],je.value=0}finally{$e.value=!1}},Ge=e=>({0:"info",1:"success",2:"warning",3:"danger"}[e]||"info"),Qe=e=>({0:"闲置",1:"在用",2:"维修",3:"报废"}[e]||"未知"),Xe=e=>({1:"创建",2:"修改",3:"删除",4:"位置变更",5:"状态变更"}[e]||"未知操作"),Ke=()=>{be.value=1,He()},Ye=()=>{Ce.assetCode="",Ce.name="",Ce.assetTypeName="",Ce.statusName="",be.value=1,He()},Ze=async e=>{_e.value=Number(e),be.value=1;Math.ceil(we.value/e);await f(),await He()},ea=async e=>{_e.value=Number(e),be.value=1,await f(),await He()};window.forceSetPageSize=ea;const aa=async e=>{const a=Math.ceil(we.value/_e.value);e>a?o.warning(`页码${e}超出范围，总共只有${a}页`):e<1||(be.value=e,await He())},la=e=>(be.value-1)*_e.value+e+1,ta=e=>{Ue.value=e,We(Ne.value.id)},sa=e=>{Oe.value=e,We(Ne.value.id)},oa=()=>{ke.value=!0,Object.keys(xe).forEach((e=>{xe[e]=""})),xe.status=1,Te.value=!0},ra=async()=>{if(Ie.value)try{await Ie.value.validate();const a=na();let l={};if(!ke.value&&Ne.value){if(["assetCode","financialCode","name","assetTypeId","model","brand","locationId","status","notes"].forEach((e=>{if(void 0===a[e])return;let t=Ne.value[e],s=a[e];if("assetTypeId"!==e&&"locationId"!==e&&"status"!==e||(t=null!=t?Number(t):null,s=null!=s?Number(s):null),"string"==typeof t||"string"==typeof s){const a=(t||"").toString().trim(),o=(s||"").toString().trim();a!==o&&(l[e]={oldValue:a||"无",newValue:o||"无"})}else t!==s&&(null==t&&null==s||(l[e]={oldValue:null==t?"无":t,newValue:null==s?"无":s}))})),0===Object.keys(l).length)return void o.info("没有修改任何内容，无需保存");if(l.assetTypeId){const e=ge.value.find((e=>e.id===Number(Ne.value.assetTypeId))),t=ge.value.find((e=>e.id===Number(a.assetTypeId)));l["资产类型"]={oldValue:e?e.name:"未知",newValue:t?t.name:"未知"}}if(l.locationId){const e=he.value.find((e=>e.id===Number(Ne.value.locationId))),t=he.value.find((e=>e.id===Number(a.locationId)));l["位置"]={oldValue:e?e.fullName||e.name:"未分配",newValue:t?t.fullName||t.name:"未分配"}}l.status&&(l["状态"]={oldValue:Qe(Ne.value.status),newValue:Qe(a.status)})}fe.value=!0;try{if(ke.value){const e=await S(a);e.success?(o.success("创建资产成功"),Te.value=!1,He()):o.error(e.message||"创建资产失败")}else{const e={asset:a,changes:l},t=await $(xe.id,e);t.success?(o.success("更新资产成功"),Te.value=!1,He()):o.error(t.message||"更新资产失败")}}catch(e){o.error(ke.value?"创建资产失败":"更新资产失败")}finally{fe.value=!1}}catch(e){o.error("请完成必填项")}},na=()=>{const e={...xe};return void 0!==e.assetTypeId&&null!==e.assetTypeId&&(e.assetTypeId=Number(e.assetTypeId)),void 0!==e.locationId&&null!==e.locationId&&(e.locationId=Number(e.locationId)),void 0!==e.status&&(e.status=Number(e.status)),e},ua=()=>{Me.value=!0,Re.value=null,qe.value=!1,Le.value=0,De.value=null},ia=async()=>{try{const e=`${z.apiBaseUrl}/import/template?entityType=Assets&format=excel`,a=t(),l=await fetch(e,{method:"GET",headers:{Accept:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",Authorization:a?`Bearer ${a}`:""}});if(!l.ok){await l.text();throw new Error(`服务器返回错误: ${l.status} ${l.statusText}`)}const s=await l.blob(),r=`资产导入模板_${(new Date).toISOString().substring(0,10)}.xlsx`;if(window.navigator.msSaveOrOpenBlob)window.navigator.msSaveBlob(s,r);else{const e=URL.createObjectURL(s),a=document.createElement("a");a.href=e,a.download=r,a.style.display="none",document.body.appendChild(a),a.click(),URL.revokeObjectURL(e),document.body.removeChild(a)}o.success("模板下载成功")}catch(e){o.error(`模板下载失败: ${e.message}`)}},da=e=>{const a="application/vnd.ms-excel"===e.type||"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"===e.type||e.name.endsWith(".xlsx")||e.name.endsWith(".xls"),l="text/csv"===e.type||e.name.endsWith(".csv"),t=a||l,s=e.size/1024/1024<10;return t?s?(Re.value=e,!0):(o.error("文件大小不能超过10MB!"),!1):(o.error("请上传Excel或CSV格式的文件!"),!1)},ca=e=>{e&&(Re.value=e.raw||e)},pa=e=>{if(!e.file)return void o.warning("没有选择文件");const a=new FormData;a.append("file",e.file),a.append("entityType","Assets"),qe.value=!0,Le.value=10;const l=new XMLHttpRequest;l.open("POST",`${z.apiBaseUrl}/import/data`,!0);const s=t();s&&l.setRequestHeader("Authorization",`Bearer ${s}`),l.upload.addEventListener("progress",(a=>{if(a.lengthComputable){const l=Math.round(100*a.loaded/a.total);Le.value=l,e.onProgress&&e.onProgress({percent:l})}})),l.onload=()=>{if(l.status>=200&&l.status<300){let t;try{t=JSON.parse(l.responseText)}catch(a){t={success:!1,message:"无法解析服务器响应"}}e.onSuccess&&e.onSuccess(t),fa(t)}else{let t="上传失败",s=null;try{s=JSON.parse(l.responseText),t=s.message||`服务器返回错误: ${l.status}`}catch(a){t=`上传失败: ${l.status} ${l.statusText}`}e.onError&&e.onError(new Error(t)),ya({message:t,response:{data:s}})}},l.onerror=()=>{const a="网络错误，上传失败";e.onError&&e.onError(new Error(a)),ya({message:a})},l.send(a)},va=()=>{if(Re.value)if(qe.value=!0,Le.value=0,De.value=null,Ee.value&&Ee.value.uploadFiles&&Ee.value.uploadFiles.length>0)Ee.value.submit();else{const e=new FormData;e.append("file",Re.value),e.append("entityType","Assets"),pa({file:Re.value,onProgress:e=>ma(e),onSuccess:e=>fa(e),onError:e=>ya(e)})}else o.warning("请先选择要导入的文件")},ma=e=>{Le.value=Math.round(e.percent)},fa=e=>{if(qe.value=!1,Le.value=100,e.success){if(o.success("数据导入成功"),De.value={success:!0,message:e.message||"导入成功",data:{successCount:e.successCount||0,errorCount:e.errorCount||0,totalRows:e.totalRows||0,errorMessages:[]}},e.errors&&Array.isArray(e.errors))De.value.data.errorMessages=e.errors;else if(e.errors&&"object"==typeof e.errors){const a=[];for(const l in e.errors)a.push(`行 ${l}: ${e.errors[l]}`);De.value.data.errorMessages=a}if(e.warnings&&"object"==typeof e.warnings){const a=[];for(const l in e.warnings){const t=e.warnings[l];Array.isArray(t)?t.forEach((e=>{a.push(`行 ${l} 警告: ${e}`)})):a.push(`行 ${l} 警告: ${t}`)}De.value.data.warningMessages=a}}else if(o.error(e.message||"导入失败"),De.value={success:!1,message:e.message||"导入失败",data:{successCount:e.successCount||0,errorCount:e.errorCount||0,totalRows:e.totalRows||0,errorMessages:[]}},e.errors&&Array.isArray(e.errors))De.value.data.errorMessages=e.errors;else if(e.errors&&"object"==typeof e.errors){const a=[];for(const l in e.errors)a.push(`行 ${l}: ${e.errors[l]}`);De.value.data.errorMessages=a}},ya=e=>{qe.value=!1;let a=e.message||"导入失败",l=[];if(e.response&&e.response.data){const t=e.response.data;if(t.message&&(a=t.message),t.errors&&Array.isArray(t.errors))l=t.errors;else if(t.errors&&"object"==typeof t.errors)for(const e in t.errors)l.push(`${e}: ${t.errors[e]}`)}o.error(a),De.value={success:!1,message:a,data:{successCount:0,errorCount:0,errorMessages:l}}},ga=()=>{Me.value=!1,He()},ha=async()=>{try{fe.value=!0;const e={assetCode:Ce.assetCode||"",name:Ce.name||"",assetTypeName:Ce.assetTypeName||"",statusName:Ce.statusName||""};await A(e),o.success("导出成功")}catch(e){o.error("导出失败："+(e.message||"未知错误"))}finally{fe.value=!1}},wa=e=>{const a={...e};if(void 0!==e.operationType||e.description){if(a.isAssetHistory=!0,a.operationTypeName=Xe(e.operationType),e.description&&"string"==typeof e.description)try{const l=JSON.parse(e.description),t={};Object.keys(l).forEach((e=>{const a=l[e];t[e]="object"==typeof a?"oldValue"in a&&"newValue"in a?{oldValue:null===a.oldValue||void 0===a.oldValue?"无":String(a.oldValue),newValue:null===a.newValue||void 0===a.newValue?"无":String(a.newValue)}:"Old"in a&&"New"in a?{oldValue:null===a.Old||void 0===a.Old?"无":String(a.Old),newValue:null===a.New||void 0===a.New?"无":String(a.New)}:"old"in a&&"new"in a?{oldValue:null===a.old||void 0===a.old?"无":String(a.old),newValue:null===a.new||void 0===a.new?"无":String(a.new)}:a:a})),a.parsedChanges=t}catch(l){a.parsedChanges=null}}else void 0===e.changeType&&void 0===e.oldLocationId||(a.isLocationHistory=!0,a.operationType=4,a.operationTypeName="位置变更",a.operationTime=e.changeTime,a.parsedChanges={"位置":{oldValue:e.oldLocationName||"未知位置",newValue:e.newLocationName||"未知位置"}},e.notes&&(a.parsedChanges["备注"]=e.notes));return a},ba=e=>{if(!e)return"无";if("object"==typeof e){if("oldValue"in e)return null===e.oldValue||void 0===e.oldValue||""===e.oldValue?"无":String(e.oldValue);if("Old"in e)return null===e.Old||void 0===e.Old||""===e.Old?"无":String(e.Old);if("old"in e)return null===e.old||void 0===e.old||""===e.old?"无":String(e.old)}return"无"},_a=e=>{if(!e)return"无";if("object"==typeof e){if("newValue"in e)return null===e.newValue||void 0===e.newValue||""===e.newValue?"无":String(e.newValue);if("New"in e)return null===e.New||void 0===e.New||""===e.New?"无":String(e.New);if("new"in e)return null===e.new||void 0===e.new||""===e.new?"无":String(e.new)}return String(e)};s((async()=>{try{await Promise.all([Pe(),Fe()]),He()}catch(e){o.error("初始化数据失败，请刷新页面重试")}}));const Ca=e=>{Be.value=e},Va=()=>{0!==Be.value.length?(Be.value.map((e=>e.name||e.assetCode)).join(", "),I.confirm(`确定要删除选中的 ${Be.value.length} 个资产吗？删除后不可恢复！`,"警告",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"warning"}).then((async()=>{fe.value=!0;try{const e=Be.value.map((e=>x(e.id))),a=await Promise.allSettled(e),l=a.filter((e=>{var a;return"fulfilled"===e.status&&(null==(a=e.value)?void 0:a.success)})).length,t=a.length-l;0===t?o.success(`成功删除 ${l} 个资产`):0===l?o.error("删除失败，请重试"):o.warning(`部分资产删除成功，${l} 成功，${t} 失败`),He()}catch(e){o.error("批量删除资产失败:"+(e.message||"未知错误"))}finally{fe.value=!1,Be.value=[]}})).catch((()=>{}))):o.warning("请选择要删除的资产")},Na=e=>({assetCode:"资产编号",financialCode:"财务编号",name:"资产名称",assetTypeId:"资产类型ID",locationId:"位置ID",model:"型号",brand:"品牌",status:"状态",notes:"备注","资产编码":"资产编号","财务编码":"财务编号","名称":"资产名称","资产类型":"资产类型","序列号":"序列号","型号":"型号","品牌":"品牌","购买日期":"购买日期","保修到期日":"保修到期日","价格":"价格","位置":"位置","状态":"状态","备注":"备注",id:"ID"}[e]||e),Ta=e=>{if(!e)return{};const a={},l=["id","assetTypeId","locationId","ID"];return Object.keys(e).forEach((t=>{if(!l.includes(t)&&!t.endsWith("Id")&&!t.includes("ID")){const l=e[t];ba(l)!==_a(l)&&(a[t]=e[t])}})),a};return(e,a)=>{const l=g("el-button"),t=g("el-input"),s=g("el-form-item"),f=g("el-form"),y=g("el-table-column"),A=g("el-tooltip"),S=g("el-tag"),$=g("el-table"),z=g("el-pagination"),O=g("el-descriptions-item"),U=g("el-descriptions"),j=g("el-dialog"),Fe=g("el-option"),na=g("el-select"),wa=g("el-icon"),ka=g("el-empty"),Ia=g("el-divider"),xa=g("el-upload"),Aa=g("el-progress"),Sa=g("el-scrollbar"),$a=g("el-alert"),za=c("loading");return h(),r("div",R,[n("div",q,[n("div",L,[a[27]||(a[27]=n("h2",null,"资产列表",-1)),n("div",D,[u(l,{type:"primary",onClick:oa},{default:i((()=>a[23]||(a[23]=[v("新增资产")]))),_:1}),u(l,{type:"danger",onClick:Va,disabled:0===Be.value.length},{default:i((()=>a[24]||(a[24]=[v("批量删除")]))),_:1},8,["disabled"]),u(l,{onClick:ua},{default:i((()=>a[25]||(a[25]=[v("导入")]))),_:1}),u(l,{onClick:ha},{default:i((()=>a[26]||(a[26]=[v("导出")]))),_:1})])]),u(f,{inline:!0,model:Ce,class:"search-form"},{default:i((()=>[u(s,{label:"资产编号"},{default:i((()=>[u(t,{modelValue:Ce.assetCode,"onUpdate:modelValue":a[0]||(a[0]=e=>Ce.assetCode=e),placeholder:"请输入资产编号"},null,8,["modelValue"])])),_:1}),u(s,{label:"资产名称"},{default:i((()=>[u(t,{modelValue:Ce.name,"onUpdate:modelValue":a[1]||(a[1]=e=>Ce.name=e),placeholder:"请输入资产名称"},null,8,["modelValue"])])),_:1}),u(s,{label:"资产类型"},{default:i((()=>[u(t,{modelValue:Ce.assetTypeName,"onUpdate:modelValue":a[2]||(a[2]=e=>Ce.assetTypeName=e),placeholder:"资产类型",disabled:""},null,8,["modelValue"])])),_:1}),u(s,{label:"使用状态"},{default:i((()=>[u(t,{modelValue:Ce.statusName,"onUpdate:modelValue":a[3]||(a[3]=e=>Ce.statusName=e),placeholder:"使用状态",disabled:""},null,8,["modelValue"])])),_:1}),u(s,null,{default:i((()=>[u(l,{type:"primary",onClick:Ke},{default:i((()=>a[28]||(a[28]=[v("查询")]))),_:1}),u(l,{onClick:Ye},{default:i((()=>a[29]||(a[29]=[v("重置")]))),_:1})])),_:1})])),_:1},8,["model"])]),n("div",B,[d((h(),p($,{ref:"multipleTableRef","element-loading-text":"加载资产数据中...",data:ye.value,style:{width:"100%","margin-top":"15px","border-radius":"5px"},"row-key":"id","highlight-current-row":!0,onSelectionChange:Ca},{default:i((()=>[u(y,{type:"selection",width:"55"}),u(y,{type:"index",label:"序号",width:"60",align:"center",index:la}),u(y,{prop:"assetCode",label:"资产编号","min-width":"120","show-overflow-tooltip":""},{default:i((({row:e})=>[u(A,{content:e.assetCode,placement:"top","show-after":500},{default:i((()=>[n("span",null,m(e.assetCode),1)])),_:2},1032,["content"])])),_:1}),u(y,{prop:"financialCode",label:"财务编号",width:"120"},{default:i((({row:e})=>[v(m(e.financialCode||"-"),1)])),_:1}),u(y,{prop:"name",label:"资产名称",width:"150"},{default:i((({row:e})=>[u(A,{content:e.name,placement:"top","show-after":500},{default:i((()=>[n("span",null,m(e.name),1)])),_:2},1032,["content"])])),_:1}),u(y,{label:"资产类型",width:"120"},{default:i((({row:e})=>[v(m(e.assetTypeName||"-"),1)])),_:1}),u(y,{prop:"model",label:"规格型号",width:"120"},{default:i((({row:e})=>[v(m(e.model||"-"),1)])),_:1}),u(y,{prop:"brand",label:"品牌",width:"100"},{default:i((({row:e})=>[v(m(e.brand||"-"),1)])),_:1}),u(y,{prop:"locationName",label:"位置",width:"150","show-overflow-tooltip":""},{default:i((({row:e})=>[v(m(e.locationName||"-"),1)])),_:1}),u(y,{label:"状态",width:"80"},{default:i((({row:e})=>[u(S,{type:Ge(e.status)},{default:i((()=>[v(m(Qe(e.status)),1)])),_:2},1032,["type"])])),_:1}),u(y,{label:"操作",width:"200",fixed:"right"},{default:i((({row:e})=>[u(l,{link:"",onClick:a=>(async e=>{ke.value=!1,Object.keys(xe).forEach((e=>{xe[e]="status"===e?1:""}));try{if(0===ge.value.length)try{await Pe()}catch(a){o.warning("资产类型加载失败，可能影响编辑功能")}if(0===he.value.length){const e=await M.getLocationsForDropdown({includeAll:!0});e&&e.success?he.value=e.data||[]:o.warning("位置列表加载失败，可能影响编辑功能")}const l=await Je(e.id);if(!l)return void o.error("无法获取资产详情，编辑失败");if(xe.id=l.id||e.id,xe.assetCode=l.assetCode||e.assetCode||"",xe.financialCode=l.financialCode||e.financialCode||"",xe.name=l.name||e.name||"",xe.model=l.model||e.model||"",xe.brand=l.brand||e.brand||"",xe.notes=l.notes||e.notes||"",xe.status=void 0!==l.status?Number(l.status):void 0!==e.status?Number(e.status):1,l.assetTypeId)if(xe.assetTypeId=Number(l.assetTypeId),Array.isArray(ge.value)){if(ge.value.find((e=>e.id===xe.assetTypeId)));else if(l.assetTypeName&&Array.isArray(ge.value)){const e=ge.value.find((e=>e.name===l.assetTypeName));e&&(xe.assetTypeId=e.id)}}else o.warning("资产类型数据格式错误，可能影响显示");else if(l.assetTypeName&&Array.isArray(ge.value)){const e=ge.value.find((e=>e.name===l.assetTypeName));e&&(xe.assetTypeId=e.id)}if(l.locationId)if(xe.locationId=Number(l.locationId),Array.isArray(he.value)){if(he.value.find((e=>e.id===xe.locationId)));else if(l.locationName&&Array.isArray(he.value)){const e=he.value.find((e=>e.name===l.locationName||e.fullName===l.locationName));e&&(xe.locationId=e.id)}}else o.warning("位置数据格式错误，可能影响显示");else if(l.locationName&&Array.isArray(he.value)){const e=he.value.find((e=>e.name===l.locationName||e.fullName===l.locationName));e&&(xe.locationId=e.id)}Ne.value={...l},Te.value=!0}catch(l){o.error("准备编辑对话框失败: "+(l.message||"未知错误"))}})(e)},{default:i((()=>a[30]||(a[30]=[v("编辑")]))),_:2},1032,["onClick"]),u(l,{link:"",onClick:a=>(async e=>{try{const a=await Je(e.id);if(a){if(Ne.value={...e,...a},!Ne.value.assetTypeName&&Ne.value.assetTypeId){const e=ge.value.find((e=>e.id===Ne.value.assetTypeId));e&&(Ne.value.assetTypeName=e.name)}if(!Ne.value.locationName&&Ne.value.locationId){const e=he.value.find((e=>e.id===Ne.value.locationId));e&&(Ne.value.locationName=e.fullName||e.name)}Ve.value=!0}}catch(a){o.error("获取资产详情失败")}})(e)},{default:i((()=>a[31]||(a[31]=[v("查看")]))),_:2},1032,["onClick"]),u(l,{link:"",onClick:a=>(async e=>{Ne.value=e,Oe.value=1,await We(e.id),Se.value=!0})(e)},{default:i((()=>a[32]||(a[32]=[v("历史")]))),_:2},1032,["onClick"]),u(l,{link:"",class:"delete-btn",onClick:a=>(e=>{I.confirm("确认删除该资产吗?","提示",{type:"warning"}).then((async()=>{try{const a=await x(e.id);!0===a.success?(o.success("删除成功"),He()):o.error(a.message||"删除失败")}catch(a){o.error("删除失败")}}))})(e)},{default:i((()=>a[33]||(a[33]=[v("删除")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"])),[[za,fe.value]]),n("div",P,[(h(),p(z,{key:`pagination-${_e.value}-${we.value}`,"current-page":be.value,"onUpdate:currentPage":a[4]||(a[4]=e=>be.value=e),"page-size":_e.value,"onUpdate:pageSize":a[5]||(a[5]=e=>_e.value=e),"page-sizes":[10,20,50,100,200],total:we.value,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:Ze,onCurrentChange:aa},null,8,["current-page","page-size","total"])),(h(),r("div",F,[a[35]||(a[35]=n("strong",null,"🔍 分页调试信息:",-1)),v(" 当前页: "+m(be.value)+" | 每页大小: "+m(_e.value)+" | 总记录数: "+m(we.value)+" | 总页数: "+m(Math.ceil(we.value/_e.value))+" ",1),a[36]||(a[36]=n("br",null,null,-1)),u(l,{size:"small",onClick:a[6]||(a[6]=e=>ea(200)),style:{"margin-top":"5px"}},{default:i((()=>a[34]||(a[34]=[v(" 🔧 强制设置每页200条 ")]))),_:1})]))])]),u(j,{modelValue:Ve.value,"onUpdate:modelValue":a[7]||(a[7]=e=>Ve.value=e),title:"资产详情",width:"650px","destroy-on-close":""},{default:i((()=>[u(U,{column:2,border:""},{default:i((()=>[u(O,{label:"资产编号"},{default:i((()=>[v(m(Ne.value.assetCode),1)])),_:1}),u(O,{label:"财务编号"},{default:i((()=>[v(m(Ne.value.financialCode||"-"),1)])),_:1}),u(O,{label:"资产名称"},{default:i((()=>[v(m(Ne.value.name),1)])),_:1}),u(O,{label:"资产类型"},{default:i((()=>[v(m(Ne.value.assetTypeName),1)])),_:1}),u(O,{label:"规格型号"},{default:i((()=>[v(m(Ne.value.model),1)])),_:1}),u(O,{label:"品牌"},{default:i((()=>[v(m(Ne.value.brand),1)])),_:1}),u(O,{label:"位置"},{default:i((()=>[v(m(Ne.value.locationName),1)])),_:1}),u(O,{label:"状态"},{default:i((()=>[u(S,{type:Ge(Ne.value.status)},{default:i((()=>[v(m(Qe(Ne.value.status)),1)])),_:1},8,["type"])])),_:1})])),_:1})])),_:1},8,["modelValue"]),u(j,{modelValue:Te.value,"onUpdate:modelValue":a[17]||(a[17]=e=>Te.value=e),title:ke.value?"新增资产":"编辑资产",width:"500px","destroy-on-close":""},{footer:i((()=>[u(l,{onClick:a[16]||(a[16]=e=>Te.value=!1)},{default:i((()=>a[37]||(a[37]=[v("取消")]))),_:1}),u(l,{type:"primary",onClick:ra},{default:i((()=>a[38]||(a[38]=[v("确定")]))),_:1})])),default:i((()=>[u(f,{ref_key:"assetFormRef",ref:Ie,model:xe,rules:Ae,"label-width":"100px",style:{"max-height":"500px","overflow-y":"auto"}},{default:i((()=>[u(s,{label:"资产编号",prop:"assetCode"},{default:i((()=>[u(t,{modelValue:xe.assetCode,"onUpdate:modelValue":a[8]||(a[8]=e=>xe.assetCode=e),placeholder:"请输入资产编号"},null,8,["modelValue"])])),_:1}),u(s,{label:"财务编号",prop:"financialCode"},{default:i((()=>[u(t,{modelValue:xe.financialCode,"onUpdate:modelValue":a[9]||(a[9]=e=>xe.financialCode=e),placeholder:"请输入财务编号"},null,8,["modelValue"])])),_:1}),u(s,{label:"资产名称",prop:"name"},{default:i((()=>[u(t,{modelValue:xe.name,"onUpdate:modelValue":a[10]||(a[10]=e=>xe.name=e),placeholder:"请输入资产名称"},null,8,["modelValue"])])),_:1}),u(s,{label:"资产类型",prop:"assetTypeId"},{default:i((()=>[u(na,{modelValue:xe.assetTypeId,"onUpdate:modelValue":a[11]||(a[11]=e=>xe.assetTypeId=e),placeholder:"请选择资产类型",style:{width:"100%"}},{default:i((()=>[(h(!0),r(w,null,b(ge.value,(e=>(h(),p(Fe,{key:e.id,label:e.name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),u(s,{label:"规格型号",prop:"model"},{default:i((()=>[u(t,{modelValue:xe.model,"onUpdate:modelValue":a[12]||(a[12]=e=>xe.model=e),placeholder:"请输入规格型号"},null,8,["modelValue"])])),_:1}),u(s,{label:"品牌",prop:"brand"},{default:i((()=>[u(t,{modelValue:xe.brand,"onUpdate:modelValue":a[13]||(a[13]=e=>xe.brand=e),placeholder:"请输入品牌"},null,8,["modelValue"])])),_:1}),u(s,{label:"位置",prop:"locationId"},{default:i((()=>[u(na,{modelValue:xe.locationId,"onUpdate:modelValue":a[14]||(a[14]=e=>xe.locationId=e),filterable:"",placeholder:"请选择位置",style:{width:"100%"}},{default:i((()=>[(h(!0),r(w,null,b(he.value,(e=>(h(),p(Fe,{key:e.id,label:e.fullName||e.name,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),u(s,{label:"状态",prop:"status"},{default:i((()=>[u(na,{modelValue:xe.status,"onUpdate:modelValue":a[15]||(a[15]=e=>xe.status=e),placeholder:"请选择状态"},{default:i((()=>[u(Fe,{label:"闲置",value:0}),u(Fe,{label:"在用",value:1}),u(Fe,{label:"维修",value:2}),u(Fe,{label:"报废",value:3})])),_:1},8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue","title"]),u(j,{modelValue:Se.value,"onUpdate:modelValue":a[20]||(a[20]=e=>Se.value=e),title:"资产历史记录",width:"800px","destroy-on-close":"",class:"history-dialog"},{default:i((()=>[Ne.value.id?(h(),r("h3",H,[u(wa,null,{default:i((()=>[u(C(V))])),_:1}),v(" 资产: "+m(Ne.value.name)+" ",1),u(S,{type:"info",size:"small"},{default:i((()=>[v(m(Ne.value.assetCode),1)])),_:1})])):_("",!0),d((h(),r("div",J,[0!==ze.value.length||$e.value?(h(),p($,{key:1,ref:"historyTable",data:ze.value,style:{width:"100%"},border:"",stripe:"",size:"default"},{default:i((()=>[u(y,{prop:"operationTime",label:"变更时间",width:"170",sortable:""},{default:i((e=>[v(m(C(E)(e.row.changeTime||e.row.operationTime)),1)])),_:1}),u(y,{prop:"operationTypeName",label:"操作类型",width:"100"},{default:i((e=>{return[u(S,{type:(a=e.row.operationType,{1:"success",2:"primary",3:"danger",4:"warning",5:"info"}[a]||"info")},{default:i((()=>[v(m(e.row.operationTypeName||Xe(e.row.operationType)),1)])),_:2},1032,["type"])];var a})),_:1}),u(y,{label:"变更内容","min-width":"450"},{default:i((e=>[e.row.parsedChanges?(h(),r("div",G,[(h(!0),r(w,null,b(Ta(e.row.parsedChanges),((e,a)=>(h(),r("div",{key:a,class:"change-item"},[n("div",Q,m(Na(a)),1),n("div",X,[n("span",K,m(ba(e)),1),u(wa,{class:"change-arrow"},{default:i((()=>[u(C(N))])),_:1}),n("span",Y,m(_a(e)),1)])])))),128))])):(h(),r("div",Z,[u(l,{size:"small",type:"primary",onClick:a=>(e=>{if(e.description)try{const a=JSON.parse(e.description);e.parsedChanges=a}catch(a){o.error("无法解析变更详情")}})(e.row)},{default:i((()=>a[39]||(a[39]=[v("解析变更")]))),_:2},1032,["onClick"])]))])),_:1}),u(y,{prop:"operatorName",label:"操作人",width:"150"})])),_:1},8,["data"])):(h(),r("div",W,[u(ka,{description:"暂无历史记录"})]))])),[[za,$e.value]]),ze.value.length>0?(h(),r("div",ee,[u(z,{"current-page":Oe.value,"onUpdate:currentPage":a[18]||(a[18]=e=>Oe.value=e),"page-size":Ue.value,"onUpdate:pageSize":a[19]||(a[19]=e=>Ue.value=e),total:je.value,"page-sizes":[10,20,50],layout:"total, sizes, prev, pager, next",onSizeChange:ta,onCurrentChange:sa},null,8,["current-page","page-size","total"])])):_("",!0)])),_:1},8,["modelValue"]),u(j,{modelValue:Me.value,"onUpdate:modelValue":a[22]||(a[22]=e=>Me.value=e),title:"资产数据导入",width:"500px","destroy-on-close":""},{footer:i((()=>[n("span",me,[u(l,{onClick:a[21]||(a[21]=e=>Me.value=!1)},{default:i((()=>a[56]||(a[56]=[v("关闭")]))),_:1}),De.value&&De.value.success?(h(),p(l,{key:0,type:"primary",onClick:ga},{default:i((()=>a[57]||(a[57]=[v(" 完成并刷新 ")]))),_:1})):_("",!0)])])),default:i((()=>[n("div",ae,[n("div",le,[n("p",te,[u(S,{size:"small"},{default:i((()=>a[40]||(a[40]=[v("步骤 1")]))),_:1}),a[41]||(a[41]=v(" 下载导入模板"))]),n("p",se,[u(S,{size:"small"},{default:i((()=>a[42]||(a[42]=[v("步骤 2")]))),_:1}),a[43]||(a[43]=v(" 按照模板格式填写数据"))]),n("p",oe,[u(S,{size:"small"},{default:i((()=>a[44]||(a[44]=[v("步骤 3")]))),_:1}),a[45]||(a[45]=v(" 上传填写好的文件"))])]),n("div",re,[u(l,{type:"primary",plain:"",onClick:ia},{default:i((()=>[u(wa,null,{default:i((()=>[u(C(T))])),_:1}),a[46]||(a[46]=v(" 下载导入模板 "))])),_:1}),a[47]||(a[47]=n("p",{class:"template-tip"},"请确保按照模板格式填写数据，否则可能导致导入失败",-1))]),u(Ia),u(xa,{ref_key:"uploadRef",ref:Ee,class:"import-upload",action:"",multiple:!1,"auto-upload":!1,"http-request":pa,"on-change":ca,"before-upload":da,"on-success":fa,"on-error":ya,"on-progress":ma,accept:".csv,.xlsx,.xls",limit:1},{trigger:i((()=>[u(l,{type:"primary"},{default:i((()=>[u(wa,null,{default:i((()=>[u(C(k))])),_:1}),a[48]||(a[48]=v(" 选择文件 "))])),_:1})])),tip:i((()=>a[49]||(a[49]=[n("div",{class:"el-upload__tip"}," 仅支持 xlsx, xls 或 csv 格式文件，文件大小不超过10MB ",-1)]))),default:i((()=>[u(l,{class:"ml-3",type:"success",onClick:va,loading:qe.value,disabled:null===Re.value},{default:i((()=>a[50]||(a[50]=[v(" 开始导入 ")]))),_:1},8,["loading","disabled"])])),_:1},512),qe.value?(h(),r("div",ne,[u(Aa,{percentage:Le.value,status:"success"},null,8,["percentage"]),a[51]||(a[51]=n("p",{class:"progress-text"},"正在导入，请勿关闭窗口...",-1))])):_("",!0),De.value?(h(),r("div",ue,[u($a,{title:De.value.success?"导入成功":"导入失败",type:De.value.success?"success":"error","show-icon":""},{default:i((()=>{var e,l,t,s,o;return[n("div",ie,[n("p",null," 总计 "+m((null==(e=De.value.data)?void 0:e.totalRows)||0)+" 条记录， 成功导入 "+m((null==(l=De.value.data)?void 0:l.successCount)||0)+" 条， 失败 "+m((null==(t=De.value.data)?void 0:t.errorCount)||0)+" 条 ",1),(null==(s=De.value.data)?void 0:s.errorMessages)&&De.value.data.errorMessages.length>0?(h(),r("div",de,[a[53]||(a[53]=n("p",{class:"message-title"},"错误信息:",-1)),u(Sa,{height:"150px"},{default:i((()=>[n("ul",ce,[(h(!0),r(w,null,b(De.value.data.errorMessages,((e,l)=>(h(),r("li",{key:"error-"+l},[u(S,{type:"danger",size:"small"},{default:i((()=>a[52]||(a[52]=[v("错误")]))),_:1}),v(" "+m(e),1)])))),128))])])),_:1})])):_("",!0),(null==(o=De.value.data)?void 0:o.warningMessages)&&De.value.data.warningMessages.length>0?(h(),r("div",pe,[a[55]||(a[55]=n("p",{class:"message-title"},"警告信息:",-1)),u(Sa,{height:"150px"},{default:i((()=>[n("ul",ve,[(h(!0),r(w,null,b(De.value.data.warningMessages,((e,l)=>(h(),r("li",{key:"warning-"+l},[u(S,{type:"warning",size:"small"},{default:i((()=>a[54]||(a[54]=[v("警告")]))),_:1}),v(" "+m(e),1)])))),128))])])),_:1})])):_("",!0)])]})),_:1},8,["title","type"])])):_("",!0)])])),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-dfc8a52e"]]);export{fe as default};
