// File: Services/Interfaces/IUniversalGamificationService.cs
// Description: 通用游戏化服务接口

using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ItAssetsSystem.Services.Interfaces
{
    /// <summary>
    /// 通用游戏化服务接口
    /// </summary>
    public interface IUniversalGamificationService
    {
        /// <summary>
        /// 触发行为奖励
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="behaviorCode">行为代码</param>
        /// <param name="referenceId">关联对象ID</param>
        /// <param name="context">上下文数据</param>
        /// <param name="description">描述</param>
        /// <returns>是否成功</returns>
        Task<bool> TriggerBehaviorRewardAsync(
            int userId, 
            string behaviorCode, 
            long? referenceId = null, 
            object? context = null,
            string? description = null);

        /// <summary>
        /// 回退行为奖励（用于删除操作）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="behaviorCode">行为代码</param>
        /// <param name="referenceId">关联对象ID</param>
        /// <returns>是否成功</returns>
        Task<bool> RevertBehaviorRewardAsync(int userId, string behaviorCode, long? referenceId = null);
    }

    /// <summary>
    /// 排行榜服务接口
    /// </summary>
    public interface ILeaderboardService
    {
        /// <summary>
        /// 获取排行榜
        /// </summary>
        /// <param name="metric">排行指标</param>
        /// <param name="period">统计周期</param>
        /// <param name="limit">返回数量</param>
        /// <returns>排行榜数据</returns>
        Task<List<LeaderboardEntry>> GetLeaderboardAsync(string metric, string period = "alltime", int limit = 50);

        /// <summary>
        /// 获取用户排名
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="metric">排行指标</param>
        /// <param name="period">统计周期</param>
        /// <returns>用户排名信息</returns>
        Task<UserRankInfo?> GetUserRankAsync(int userId, string metric, string period = "alltime");

        /// <summary>
        /// 刷新排行榜缓存
        /// </summary>
        Task RefreshLeaderboardCacheAsync();
    }

    /// <summary>
    /// 排行榜条目
    /// </summary>
    public class LeaderboardEntry
    {
        public int Rank { get; set; }
        public int UserId { get; set; }
        public string UserName { get; set; } = string.Empty;
        public string? AvatarUrl { get; set; }
        public string? Department { get; set; }
        public int Points { get; set; }
        public int Coins { get; set; }
        public int Diamonds { get; set; }
        public int TasksCreated { get; set; }
        public int TasksCompleted { get; set; }
        public int TasksClaimed { get; set; }
        public int FaultsRecorded { get; set; }
        public int MaintenanceCreated { get; set; }
        public int AssetsUpdated { get; set; }
        public DateTime LastActiveTime { get; set; }
    }

    /// <summary>
    /// 用户排名信息
    /// </summary>
    public class UserRankInfo
    {
        public int UserId { get; set; }
        public string UserName { get; set; } = string.Empty;
        public int Rank { get; set; }
        public int Score { get; set; }
        public int TotalUsers { get; set; }
        public double Percentile { get; set; } // 百分位
    }

    /// <summary>
    /// 排行榜指标枚举
    /// </summary>
    public static class LeaderboardMetrics
    {
        public const string POINTS = "points";
        public const string COINS = "coins";
        public const string DIAMONDS = "diamonds";
        public const string TASKS_CREATED = "tasks_created";
        public const string TASKS_COMPLETED = "tasks_completed";
        public const string TASKS_CLAIMED = "tasks_claimed";
        public const string FAULTS_RECORDED = "faults_recorded";
        public const string MAINTENANCE_CREATED = "maintenance_created";
        public const string ASSETS_UPDATED = "assets_updated";
    }

    /// <summary>
    /// 统计周期枚举
    /// </summary>
    public static class StatisticsPeriods
    {
        public const string DAILY = "daily";
        public const string WEEKLY = "weekly";
        public const string MONTHLY = "monthly";
        public const string QUARTERLY = "quarterly";
        public const string YEARLY = "yearly";
        public const string ALLTIME = "alltime";
    }
}
