// UDP极致性能通知客户端实现
#include "udp_notification_client.h"
#include <iostream>
#include <cstring>

UDPNotificationClient::UDPNotificationClient() 
    : udp_socket(INVALID_SOCKET)
    , running(false)
    , sequence_counter(0) {
    
    // 初始化Winsock
    WSADATA wsaData;
    if (WSAStartup(MAKEWORD(2, 2), &wsaData) != 0) {
        throw std::runtime_error("WSAStartup failed");
    }
}

UDPNotificationClient::~UDPNotificationClient() {
    disconnect();
    WSACleanup();
}

bool UDPNotificationClient::connect(const std::string& server_ip, int port) {
    // 创建UDP套接字
    udp_socket = socket(AF_INET, SOCK_DGRAM, IPPROTO_UDP);
    if (udp_socket == INVALID_SOCKET) {
        std::cerr << "创建UDP套接字失败: " << WSAGetLastError() << std::endl;
        return false;
    }
    
    // 设置套接字选项（极致性能优化）
    int opt = 1;
    setsockopt(udp_socket, SOL_SOCKET, SO_REUSEADDR, (char*)&opt, sizeof(opt));
    
    // 设置发送缓冲区大小
    int send_buffer = 1024 * 1024; // 1MB
    setsockopt(udp_socket, SOL_SOCKET, SO_SNDBUF, (char*)&send_buffer, sizeof(send_buffer));
    
    // 设置接收缓冲区大小
    int recv_buffer = 1024 * 1024; // 1MB
    setsockopt(udp_socket, SOL_SOCKET, SO_RCVBUF, (char*)&recv_buffer, sizeof(recv_buffer));
    
    // 设置服务器地址
    memset(&server_addr, 0, sizeof(server_addr));
    server_addr.sin_family = AF_INET;
    server_addr.sin_port = htons(port);
    if (inet_pton(AF_INET, server_ip.c_str(), &server_addr.sin_addr) <= 0) {
        std::cerr << "无效的服务器IP地址: " << server_ip << std::endl;
        closesocket(udp_socket);
        return false;
    }
    
    running = true;
    
    // 启动工作线程
    send_thread = std::thread(&UDPNotificationClient::sendWorker, this);
    recv_thread = std::thread(&UDPNotificationClient::receiveWorker, this);
    
    // 设置线程优先级（实时优先级）
    SetThreadPriority(send_thread.native_handle(), THREAD_PRIORITY_TIME_CRITICAL);
    SetThreadPriority(recv_thread.native_handle(), THREAD_PRIORITY_ABOVE_NORMAL);
    
    std::cout << "UDP客户端已连接到 " << server_ip << ":" << port << std::endl;
    return true;
}

void UDPNotificationClient::disconnect() {
    if (!running) return;
    
    running = false;
    
    // 唤醒发送线程
    queue_cv.notify_all();
    
    // 关闭套接字
    if (udp_socket != INVALID_SOCKET) {
        closesocket(udp_socket);
        udp_socket = INVALID_SOCKET;
    }
    
    // 等待线程结束
    if (send_thread.joinable()) {
        send_thread.join();
    }
    if (recv_thread.joinable()) {
        recv_thread.join();
    }
    
    std::cout << "UDP客户端已断开连接" << std::endl;
}

void UDPNotificationClient::sendMessage(MessageType type, Priority priority, const void* data, size_t size) {
    if (!running || size > sizeof(UDPPacket::payload)) return;
    
    auto packet = createPacket(type, priority, data, size);
    
    // 根据优先级选择发送方式
    if (priority >= Priority::HIGH) {
        // 高优先级消息立即发送
        sendto(udp_socket, (char*)&packet, sizeof(UDPPacket), 0,
               (sockaddr*)&server_addr, sizeof(server_addr));
        packets_sent++;
        bytes_sent += sizeof(UDPPacket);
    } else {
        // 普通消息加入队列
        std::lock_guard<std::mutex> lock(queue_mutex);
        send_queue.push(packet);
        queue_cv.notify_one();
    }
}

bool UDPNotificationClient::sendReliable(MessageType type, const void* data, size_t size, int timeout_ms) {
    if (!running || size > sizeof(UDPPacket::payload)) return false;
    
    auto packet = createPacket(type, Priority::CRITICAL, data, size);
    uint16_t seq = packet.sequence;
    
    // 发送数据包
    int result = sendto(udp_socket, (char*)&packet, sizeof(UDPPacket), 0,
                       (sockaddr*)&server_addr, sizeof(server_addr));
    
    if (result == SOCKET_ERROR) {
        return false;
    }
    
    packets_sent++;
    bytes_sent += sizeof(UDPPacket);
    
    // TODO: 实现确认机制（根据需要）
    // 简化版本：直接返回成功
    return true;
}

void UDPNotificationClient::setMessageCallback(std::function<void(const UDPPacket&)> callback) {
    message_callback = callback;
}

UDPNotificationClient::Stats UDPNotificationClient::getStats() const {
    static auto start_time = std::chrono::steady_clock::now();
    auto now = std::chrono::steady_clock::now();
    auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(now - start_time).count();
    
    Stats stats;
    stats.packets_sent = packets_sent.load();
    stats.packets_received = packets_received.load();
    stats.bytes_sent = bytes_sent.load();
    stats.send_rate_pps = elapsed > 0 ? (double)stats.packets_sent / elapsed : 0;
    stats.send_rate_bps = elapsed > 0 ? (double)stats.bytes_sent / elapsed : 0;
    
    return stats;
}

void UDPNotificationClient::sendWorker() {
    while (running) {
        std::unique_lock<std::mutex> lock(queue_mutex);
        queue_cv.wait(lock, [this] { return !send_queue.empty() || !running; });
        
        while (!send_queue.empty() && running) {
            auto packet = send_queue.front();
            send_queue.pop();
            lock.unlock();
            
            // 发送数据包
            sendto(udp_socket, (char*)&packet, sizeof(UDPPacket), 0,
                   (sockaddr*)&server_addr, sizeof(server_addr));
            
            packets_sent++;
            bytes_sent += sizeof(UDPPacket);
            
            lock.lock();
        }
    }
}

void UDPNotificationClient::receiveWorker() {
    char buffer[2048];
    sockaddr_in from_addr;
    int addr_len = sizeof(from_addr);
    
    while (running) {
        // 设置接收超时
        fd_set readfds;
        FD_ZERO(&readfds);
        FD_SET(udp_socket, &readfds);
        
        timeval timeout = {1, 0}; // 1秒超时
        int result = select(0, &readfds, nullptr, nullptr, &timeout);
        
        if (result > 0 && FD_ISSET(udp_socket, &readfds)) {
            int bytes_received = recvfrom(udp_socket, buffer, sizeof(buffer), 0,
                                        (sockaddr*)&from_addr, &addr_len);
            
            if (bytes_received >= sizeof(UDPPacket)) {
                UDPPacket* packet = (UDPPacket*)buffer;
                
                if (validatePacket(*packet)) {
                    packets_received++;
                    
                    if (message_callback) {
                        message_callback(*packet);
                    }
                }
            }
        }
    }
}

uint32_t UDPNotificationClient::calculateChecksum(const void* data, size_t size) {
    // 简单的校验和算法（生产环境建议使用CRC32）
    uint32_t checksum = 0;
    const uint8_t* bytes = (const uint8_t*)data;
    for (size_t i = 0; i < size; i++) {
        checksum += bytes[i];
    }
    return checksum;
}

bool UDPNotificationClient::validatePacket(const UDPPacket& packet) {
    // 验证魔术字
    if (packet.magic != 0xDEADBEEF) {
        return false;
    }
    
    // 验证负载大小
    if (packet.payload_size > sizeof(packet.payload)) {
        return false;
    }
    
    // 验证校验和
    UDPPacket temp = packet;
    uint32_t original_checksum = temp.checksum;
    temp.checksum = 0;
    
    uint32_t calculated_checksum = calculateChecksum(&temp, sizeof(UDPPacket) - sizeof(uint32_t));
    
    return calculated_checksum == original_checksum;
}

UDPPacket UDPNotificationClient::createPacket(MessageType type, Priority priority, const void* data, size_t size) {
    UDPPacket packet = {};
    
    packet.magic = 0xDEADBEEF;
    packet.sequence = sequence_counter++;
    packet.type = (uint8_t)type;
    packet.priority = (uint8_t)priority;
    packet.timestamp = GetTickCount();
    packet.payload_size = (uint16_t)size;
    
    if (data && size > 0) {
        memcpy(packet.payload, data, std::min(size, sizeof(packet.payload)));
    }
    
    // 计算校验和（排除校验和字段本身）
    packet.checksum = calculateChecksum(&packet, sizeof(UDPPacket) - sizeof(uint32_t));
    
    return packet;
}