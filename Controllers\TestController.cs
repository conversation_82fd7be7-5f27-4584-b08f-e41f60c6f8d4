#nullable enable
// File: Controllers/TestController.cs
// Description: 用于测试数据库连接和API权限的测试控制器

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using ItAssetsSystem.Infrastructure.Data;
using Task = ItAssetsSystem.Domain.Entities.Tasks.Task;

namespace ItAssetsSystem.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class TestController : ControllerBase
    {
        private readonly AppDbContext _context;

        public TestController(AppDbContext context)
        {
            _context = context;
        }

        [HttpGet("tasks")]
        public async Task<IActionResult> GetTasks()
        {
            try
            {
                var tasks = await _context.Tasks
                    .Take(10)
                    .ToListAsync();

                return Ok(new { success = true, data = tasks, count = tasks.Count });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { success = false, error = ex.Message, stackTrace = ex.StackTrace });
            }
        }

        [HttpGet("users")]
        public async Task<IActionResult> GetUsers()
        {
            try
            {
                var users = await _context.Users
                    .Take(10)
                    .ToListAsync();

                return Ok(new { success = true, data = users, count = users.Count });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { success = false, error = ex.Message, stackTrace = ex.StackTrace });
            }
        }

        [HttpGet("periodic-schedules-test")]
        public async Task<IActionResult> TestPeriodicSchedules()
        {
            try
            {
                // 测试基本查询
                var count = await _context.PeriodicTaskSchedules.CountAsync();

                // 测试分页查询
                var schedules = await _context.PeriodicTaskSchedules
                    .Take(5)
                    .Select(p => new
                    {
                        p.PeriodicTaskScheduleId,
                        p.Name,
                        p.TemplateTaskId,
                        p.CreatorUserId,
                        p.Status
                    })
                    .ToListAsync();

                return Ok(new
                {
                    success = true,
                    count = count,
                    schedules = schedules,
                    message = "周期性任务查询测试成功"
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    error = ex.Message,
                    stackTrace = ex.StackTrace,
                    message = "周期性任务查询测试失败"
                });
            }
        }
    }
} 