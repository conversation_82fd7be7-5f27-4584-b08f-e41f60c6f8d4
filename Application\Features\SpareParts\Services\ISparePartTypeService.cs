// File: Application/Features/SpareParts/Services/ISparePartTypeService.cs
// Description: 备品备件类型服务接口

using System.Collections.Generic;
using System.Threading.Tasks;
using ItAssetsSystem.Application.Features.SpareParts.Dtos;

namespace ItAssetsSystem.Application.Features.SpareParts.Services
{
    /// <summary>
    /// 备品备件类型服务接口
    /// </summary>
    public interface ISparePartTypeService
    {
        /// <summary>
        /// 获取所有备品备件类型（平铺列表）
        /// </summary>
        /// <returns>类型列表</returns>
        Task<List<SparePartTypeDto>> GetAllTypesAsync();
        
        /// <summary>
        /// 获取类型树
        /// </summary>
        /// <returns>树形结构的类型列表</returns>
        Task<List<SparePartTypeDto>> GetTypeTreeAsync();
        
        /// <summary>
        /// 根据ID获取类型详情
        /// </summary>
        /// <param name="id">类型ID</param>
        /// <returns>类型DTO</returns>
        Task<SparePartTypeDto> GetTypeByIdAsync(long id);
        
        /// <summary>
        /// 创建类型
        /// </summary>
        /// <param name="request">创建请求</param>
        /// <returns>创建的类型DTO</returns>
        Task<SparePartTypeDto> CreateTypeAsync(CreateSparePartTypeRequest request);
        
        /// <summary>
        /// 更新类型
        /// </summary>
        /// <param name="id">类型ID</param>
        /// <param name="request">更新请求</param>
        /// <returns>更新后的类型DTO</returns>
        Task<SparePartTypeDto> UpdateTypeAsync(long id, CreateSparePartTypeRequest request);
        
        /// <summary>
        /// 删除类型
        /// </summary>
        /// <param name="id">类型ID</param>
        /// <returns>是否成功</returns>
        Task<bool> DeleteTypeAsync(long id);
        
        /// <summary>
        /// 获取类型的所有子类型ID
        /// </summary>
        /// <param name="parentId">父类型ID</param>
        /// <returns>子类型ID列表</returns>
        Task<List<long>> GetChildTypeIdsAsync(long parentId);
    }
} 