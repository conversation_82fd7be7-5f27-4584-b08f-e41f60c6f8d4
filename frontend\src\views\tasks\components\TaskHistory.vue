<template>
  <div class="task-history">
    <div class="history-header">
      <h4>操作历史 ({{ history.length }})</h4>
    </div>
    
    <!-- 历史记录列表 -->
    <div v-if="history.length" class="history-list">
      <div v-for="item in history" :key="item.taskHistoryId" class="history-item">
        <div class="history-user">
          <el-avatar 
            :src="item.userAvatarUrl" 
            :size="32"
            class="user-avatar"
          >
            {{ item.userName ? item.userName.charAt(0).toUpperCase() : '?' }}
          </el-avatar>
          <div class="history-meta">
            <div class="history-info">
              <span class="user-name">{{ item.userName || '系统' }}</span>
              <span class="action-type">{{ item.formattedActionType || item.actionType }}</span>
              <span class="history-time">{{ formatTime(item.timestamp) }}</span>
            </div>
            <div class="history-description">{{ item.description }}</div>
            <div v-if="item.fieldName" class="field-changes">
              <span class="field-name">{{ item.fieldName }}:</span>
              <span v-if="item.oldValue" class="old-value">{{ item.oldValue }}</span>
              <span v-if="item.oldValue && item.newValue" class="arrow"> → </span>
              <span v-if="item.newValue" class="new-value">{{ item.newValue }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-else class="empty-history">
      <div class="empty-icon">📋</div>
      <div class="empty-text">暂无历史记录</div>
    </div>
  </div>
</template>

<script setup>
import { computed, watch } from 'vue'
import { taskApi } from '@/api/task'

const props = defineProps({
  taskId: {
    type: [String, Number],
    required: true
  },
  history: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['load-history'])

// 格式化时间
const formatTime = (timestamp) => {
  if (!timestamp) return ''
  const date = new Date(timestamp)
  const now = new Date()
  const diff = now - date
  
  // 1分钟内
  if (diff < 60000) {
    return '刚刚'
  }
  // 1小时内
  if (diff < 3600000) {
    return `${Math.floor(diff / 60000)}分钟前`
  }
  // 24小时内
  if (diff < 86400000) {
    return `${Math.floor(diff / 3600000)}小时前`
  }
  // 超过24小时
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 使用计算属性确保正确的数据源
const history = computed(() => {
  return props.history || []
})
</script>

<style scoped>
.task-history {
  padding: 16px 0;
}

.history-header {
  margin-bottom: 16px;
}

.history-header h4 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 500;
}

.history-list {
  max-height: 400px;
  overflow-y: auto;
}

.history-item {
  padding: 12px 0;
  border-bottom: 1px solid #f5f7fa;
}

.history-item:last-child {
  border-bottom: none;
}

.history-user {
  display: flex;
  align-items: flex-start;
}

.user-avatar {
  margin-right: 12px;
  flex-shrink: 0;
}

.history-meta {
  flex: 1;
}

.history-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.user-name {
  font-weight: 500;
  color: #303133;
  font-size: 14px;
}

.action-type {
  background: #f0f9ff;
  color: #0ea5e9;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
}

.history-time {
  font-size: 12px;
  color: #909399;
}

.history-description {
  color: #606266;
  font-size: 14px;
  line-height: 1.4;
  margin-bottom: 4px;
}

.field-changes {
  font-size: 12px;
  color: #909399;
}

.field-name {
  font-weight: 500;
  margin-right: 4px;
}

.old-value {
  background: #fef2f2;
  color: #dc2626;
  padding: 1px 4px;
  border-radius: 2px;
}

.new-value {
  background: #f0fdf4;
  color: #16a34a;
  padding: 1px 4px;
  border-radius: 2px;
}

.arrow {
  color: #6b7280;
  margin: 0 4px;
}

.empty-history {
  text-align: center;
  padding: 40px 0;
  color: #c0c4cc;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 12px;
}

.empty-text {
  font-size: 14px;
}
</style> 