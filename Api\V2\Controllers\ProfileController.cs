// File: Api/V2/Controllers/ProfileController.cs
// Description: Controller for managing user profile related operations.

#nullable enable

using ItAssetsSystem.Core.Abstractions;
using ItAssetsSystem.Core.Services;
using ItAssetsSystem.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using System.Collections.Generic;
using ItAssetsSystem.Models.Entities;
using MediatR;
using ItAssetsSystem.Application.Features.Avatars.Commands;
using ItAssetsSystem.Application.Features.Avatars.Dtos;

namespace ItAssetsSystem.Api.V2.Controllers
{
    [Authorize] // All actions in this controller require authentication
    [Route("api/v2/[controller]")]
    [ApiController]
    public class ProfileController : ControllerBase
    {
        private readonly IFileStorageService _fileStorageService;
        private readonly IUserRepository _userRepository;
        private readonly IMediator _mediator;
        private readonly ILogger<ProfileController> _logger;

        public ProfileController(
            IFileStorageService fileStorageService,
            IUserRepository userRepository,
            IMediator mediator,
            ILogger<ProfileController> logger)
        {
            _fileStorageService = fileStorageService ?? throw new ArgumentNullException(nameof(fileStorageService));
            _userRepository = userRepository ?? throw new ArgumentNullException(nameof(userRepository));
            _mediator = mediator ?? throw new ArgumentNullException(nameof(mediator));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Gets the current user's profile information.
        /// </summary>
        /// <returns>The user's profile information including avatar URL.</returns>
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<UserProfileDto>))]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetProfile()
        {
            var userIdString = User.FindFirstValue(ClaimTypes.NameIdentifier);
            if (string.IsNullOrEmpty(userIdString) || !int.TryParse(userIdString, out var userId))
            {
                _logger.LogWarning("GetProfile: User ID not found in claims or invalid.");
                return Ok(ApiResponseFactory.CreateFail<object>("User not authenticated properly."));
            }

            try
            {
                var user = await _userRepository.GetByIdAsync(userId);
                if (user == null)
                {
                    return Ok(ApiResponseFactory.CreateFail<object>("User not found."));
                }

                // 获取用户角色和权限
                var roles = await _userRepository.GetUserRolesAsync(userId);
                var permissions = await _userRepository.GetUserPermissionsAsync(userId);

                var profile = new UserProfileDto
                {
                    Id = user.Id,
                    Username = user.Username,
                    Name = user.Name,
                    Email = user.Email,
                    Avatar = user.Avatar,
                    Department = user.Department?.Name,
                    Position = user.Position,
                    Roles = roles.Select(r => r.Name).ToList(),
                    Permissions = permissions.Select(p => p.Name).ToList()
                };

                return Ok(ApiResponseFactory.CreateSuccess(profile));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving profile for user {UserId}.", userId);
                return Ok(ApiResponseFactory.CreateFail<object>("An error occurred while retrieving the user profile."));
            }
        }

        /// <summary>
        /// Uploads or updates the current user's avatar.
        /// </summary>
        /// <param name="file">The avatar image file.</param>
        /// <returns>The new avatar URL or an error response.</returns>
        [HttpPost("avatar")]
        [Consumes("multipart/form-data")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ApiResponse<AvatarUploadResultDto>))]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(ApiResponse<object>))]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ApiResponse<object>))]
        public async Task<IActionResult> UploadAvatar(IFormFile file)
        {
            var userIdString = User.FindFirstValue(ClaimTypes.NameIdentifier);
            if (string.IsNullOrEmpty(userIdString))
            {
                return Ok(ApiResponseFactory.CreateFail<object>("User not authenticated properly."));
            }

            if (file == null || file.Length == 0)
            {
                return Ok(ApiResponseFactory.CreateFail<object>("No file uploaded."));
            }

            if (file.Length > 5 * 1024 * 1024)
            {
                return Ok(ApiResponseFactory.CreateFail<object>("File size exceeds the 5MB limit."));
            }

            try
            {
                var command = new UploadAvatarCommand { File = file, UserId = userIdString };
                var result = await _mediator.Send(command);

                if (result.Success)
                {
                    var avatarResult = new AvatarUploadResultDto
                    {
                        AvatarUrl = result.AvatarUrl,
                        AccessUrl = result.AccessUrl
                    };
                    
                    return Ok(ApiResponseFactory.CreateSuccess(avatarResult, "Avatar uploaded successfully."));
                }
                else
                {
                    return Ok(ApiResponseFactory.CreateFail<object>(result.Message));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception occurred while uploading avatar for user {UserId}.", userIdString);
                return Ok(ApiResponseFactory.CreateFail<object>("An unexpected error occurred while uploading the avatar."));
            }
        }
    }

    public class UserProfileDto
    {
        public int Id { get; set; }
        public string Username { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string? Email { get; set; }
        public string? Avatar { get; set; }
        public string? Department { get; set; }
        public string? Position { get; set; }
        public List<string>? Roles { get; set; }
        public List<string>? Permissions { get; set; }
    }

    public class AvatarUploadResultDto
    {
        /// <summary>
        /// 相对路径，用于存储在数据库中
        /// </summary>
        public string? AvatarUrl { get; set; }

        /// <summary>
        /// 完整访问URL，包含域名和路径
        /// </summary>
        public string? AccessUrl { get; set; }
    }
} 