<template>
  <div class="layout-designer">
    <!-- 设计器工具栏 -->
    <div class="designer-toolbar">
      <div class="toolbar-section">
        <h3>布局设计器</h3>
        <div class="layout-stats">
          <div class="stat-item">
            <span class="stat-label">总工位数:</span>
            <span class="stat-value">{{ totalWorkstations }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">区域数:</span>
            <span class="stat-value">{{ zones.length }}</span>
          </div>
        </div>
        <div class="toolbar-actions">
          <el-button type="primary" @click="addZone" :icon="Plus">添加区域</el-button>
          <el-button @click="resetLayout" :icon="Refresh">重置布局</el-button>
          <el-button type="success" @click="saveLayout" :icon="Check">保存布局</el-button>
          <el-button @click="previewLayout" :icon="View">预览</el-button>

          <!-- 文件操作 -->
          <div class="file-operations">
            <el-button type="info" @click="triggerImport" :icon="Upload">导入布局</el-button>
            <el-button @click="exportLayout" :icon="Download">导出布局</el-button>
            <input
              ref="fileInput"
              type="file"
              accept=".json"
              @change="handleFileImport"
              style="display: none"
            />
          </div>
        </div>

        <!-- 操作状态指示器 -->
        <div class="operation-status" v-if="selectedZones.length > 1 || dragState.isGroupDragging || isSelecting">
          <el-alert
            v-if="dragState.isGroupDragging"
            title="🔥 编组移动中"
            :description="`正在移动 ${selectedZones.length} 个区域`"
            type="warning"
            :closable="false"
            show-icon
          />
          <el-alert
            v-else-if="isSelecting"
            title="📦 框选模式"
            description="拖拽鼠标进行框选，按住Ctrl键可追加选择"
            type="info"
            :closable="false"
            show-icon
          />
          <el-alert
            v-else-if="selectedZones.length > 1"
            title="🎯 编组模式激活"
            :description="`已选择 ${selectedZones.length} 个区域，拖拽任意区域进行编组移动`"
            type="success"
            :closable="false"
            show-icon
          />
        </div>

        <!-- 当前选择状态详情 -->
        <div class="selection-details" v-if="selectedZones.length > 0">
          <h5>选择状态详情</h5>
          <div class="details-content">
            <div class="detail-item">
              <span class="label">选中区域:</span>
              <span class="value">{{ selectedZones.length }} 个</span>
            </div>
            <div class="detail-item">
              <span class="label">当前区域:</span>
              <span class="value">{{ selectedZone?.name || '无' }}</span>
            </div>
            <div class="detail-item">
              <span class="label">状态:</span>
              <span class="value status" :class="getSelectionStatusClass()">
                {{ getSelectionStatusText() }}
              </span>
            </div>
            <div class="selected-zones-list" v-if="selectedZones.length > 1">
              <span class="label">选中列表:</span>
              <div class="zones-tags">
                <span 
                  v-for="zoneId in selectedZones" 
                  :key="zoneId"
                  class="zone-tag"
                  :style="{ backgroundColor: getZoneById(zoneId)?.color }"
                >
                  {{ getZoneById(zoneId)?.name }}
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- 操作提示 -->
        <div class="operation-tips">
          <h5>操作提示</h5>
          <div class="tip-item">
            <span class="tip-key">Ctrl + 点击</span>
            <span class="tip-desc">多选区域</span>
          </div>
          <div class="tip-item">
            <span class="tip-key">拖拽空白</span>
            <span class="tip-desc">框选区域</span>
          </div>
          <div class="tip-item">
            <span class="tip-key">拖拽区域</span>
            <span class="tip-desc">移动/编组移动</span>
          </div>
          <div class="tip-item">
            <span class="tip-key">Esc</span>
            <span class="tip-desc">取消选择</span>
          </div>
        </div>
      </div>
      
      <!-- 区域列表 -->
      <div class="zones-list">
        <h4>区域列表</h4>
        <div class="zone-items">
          <div 
            v-for="zone in zones" 
            :key="zone.id"
            class="zone-item"
            :class="{ active: selectedZone?.id === zone.id }"
            @click="selectZone(zone)"
          >
            <div class="zone-color" :style="{ backgroundColor: zone.color }"></div>
            <span class="zone-name">{{ zone.name }}</span>
            <div class="zone-info">{{ zone.rows }}×{{ zone.cols }}</div>
            <el-button 
              size="small" 
              type="danger" 
              :icon="Delete" 
              @click.stop="deleteZone(zone.id)"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 动态尺寸设计画布 -->
    <div class="designer-canvas" ref="canvasRef">
      <div class="canvas-container">
        <div
          class="canvas-grid"
          :style="getCanvasStyle()"
          @mousedown="handleCanvasMouseDown"
          @click="handleCanvasClick"
        >
          <!-- 网格背景 - 使用CSS背景图案 -->
          <div class="grid-background-layer" :style="getGridBackgroundStyle()"></div>

        <!-- 区域标题 - 外部显示 -->
        <div
          v-for="zone in zones"
          :key="`header-${zone.id}`"
          class="zone-header-external"
          :style="{
            left: `${zone.x}px`,
            top: `${zone.y - 25}px`,
            color: zone.color,
            fontWeight: 'bold',
            fontSize: '12px',
            position: 'absolute',
            zIndex: 10,
            pointerEvents: 'none'
          }"
        >
          {{ zone.name }} ({{ zone.rows }}×{{ zone.cols }})
        </div>

        <!-- 框选框 -->
        <div
          v-if="selectionBox.visible"
          class="selection-box"
          :style="getSelectionBoxStyle()"
        >
          <div class="selection-info" v-if="isSelecting">
            框选中 {{ selectedZones.length }} 个区域
          </div>
        </div>

        <!-- 多选边框标识 -->
        <div
          v-if="selectedZones.length > 1"
          class="multi-selection-border"
          :style="getMultiSelectionBorderStyle()"
        ></div>

        <!-- 可拖拽的区域 -->
        <div
          v-for="zone in zones"
          :key="zone.id"
          class="draggable-zone"
          :class="{
            selected: selectedZone?.id === zone.id || selectedZones.includes(zone.id),
            'multi-selected': selectedZones.includes(zone.id) && selectedZones.length > 1,
            'single-selected': selectedZones.includes(zone.id) && selectedZones.length === 1,
            'being-resized': resizingZone?.id === zone.id,
            'group-dragging': dragState.isGroupDragging && selectedZones.includes(zone.id)
          }"
          :style="getZoneStyle(zone)"
          @mousedown="startDrag(zone, $event)"
          @click="selectZone(zone, $event)"
        >
          <!-- 工位网格预览 - 占满整个区域 -->
          <div class="workstation-grid" :style="getGridStyle(zone)">
            <div 
              v-for="n in (zone.rows * zone.cols)" 
              :key="n"
              class="workstation-preview"
              :style="{ backgroundColor: zone.color }"
            >
              {{ getWorkstationNumber(zone, n) }}
            </div>
          </div>

          <!-- 调整手柄 -->
          <div class="resize-handles" v-if="selectedZone?.id === zone.id">
            <div class="resize-handle nw" @mousedown.stop="startResize(zone, 'nw', $event)"></div>
            <div class="resize-handle ne" @mousedown.stop="startResize(zone, 'ne', $event)"></div>
            <div class="resize-handle sw" @mousedown.stop="startResize(zone, 'sw', $event)"></div>
            <div class="resize-handle se" @mousedown.stop="startResize(zone, 'se', $event)"></div>
            <div class="resize-handle n" @mousedown.stop="startResize(zone, 'n', $event)"></div>
            <div class="resize-handle s" @mousedown.stop="startResize(zone, 's', $event)"></div>
            <div class="resize-handle w" @mousedown.stop="startResize(zone, 'w', $event)"></div>
            <div class="resize-handle e" @mousedown.stop="startResize(zone, 'e', $event)"></div>
          </div>
        </div>
      </div>
      </div>
    </div>

    <!-- 属性面板 -->
    <div class="properties-panel" v-if="selectedZone">
      <h4>区域属性</h4>
      <el-form :model="selectedZone" label-width="80px" size="small">
        <el-form-item label="区域名称">
          <el-input v-model="selectedZone.name" />
        </el-form-item>
        
        <el-form-item label="颜色">
          <el-color-picker v-model="selectedZone.color" />
        </el-form-item>
        
        <el-form-item label="行数">
          <el-input-number 
            v-model="selectedZone.rows" 
            :min="1" 
            :max="20" 
            @change="updateZoneGrid"
          />
        </el-form-item>
        
        <el-form-item label="列数">
          <el-input-number 
            v-model="selectedZone.cols" 
            :min="1" 
            :max="20" 
            @change="updateZoneGrid"
          />
        </el-form-item>
        
        <el-form-item label="位置X">
          <el-input-number
            v-model="selectedZone.x"
            :min="0"
            :max="canvasConfig.width - selectedZone.width"
          />
        </el-form-item>

        <el-form-item label="位置Y">
          <el-input-number
            v-model="selectedZone.y"
            :min="0"
            :max="canvasConfig.height - selectedZone.height"
          />
        </el-form-item>

        <el-form-item label="宽度">
          <el-input-number
            v-model="selectedZone.width"
            :min="50"
            :max="canvasConfig.width - selectedZone.x"
          />
        </el-form-item>

        <el-form-item label="高度">
          <el-input-number
            v-model="selectedZone.height"
            :min="40"
            :max="canvasConfig.height - selectedZone.y"
          />
        </el-form-item>

        <el-form-item label="工位起始">
          <el-input-number
            v-model="selectedZone.startWorkstation"
            :min="1"
            :max="1000"
            @change="updateWorkstationNumbers"
          />
        </el-form-item>

        <el-form-item label="水平间距">
          <el-input-number
            v-model="selectedZone.gapX"
            :min="0"
            :max="20"
            :step="1"
            placeholder="px"
          />
        </el-form-item>

        <el-form-item label="垂直间距">
          <el-input-number
            v-model="selectedZone.gapY"
            :min="0"
            :max="20"
            :step="1"
            placeholder="px"
          />
        </el-form-item>

        <!-- 微调控制 -->
        <div class="fine-tune-controls">
          <h5>精确微调</h5>
          <div class="tune-grid">
            <el-button size="small" @click="fineTunePosition('up')" :icon="ArrowUp">上移</el-button>
            <div class="tune-row">
              <el-button size="small" @click="fineTunePosition('left')" :icon="ArrowLeft">左移</el-button>
              <el-button size="small" @click="fineTunePosition('right')" :icon="ArrowRight">右移</el-button>
            </div>
            <el-button size="small" @click="fineTunePosition('down')" :icon="ArrowDown">下移</el-button>
          </div>

          <div class="tune-step">
            <el-form-item label="微调步长">
              <el-select v-model="tuneStep" size="small">
                <el-option label="1px" :value="1" />
                <el-option label="5px" :value="5" />
                <el-option label="10px" :value="10" />
                <el-option label="20px" :value="20" />
              </el-select>
            </el-form-item>
          </div>
        </div>

        <el-button type="danger" @click="deleteZone(selectedZone.id)" :icon="Delete">删除区域</el-button>
      </el-form>
    </div>

    <!-- 画布配置面板 -->
    <div class="canvas-config-panel" v-if="!selectedZone">
      <h4>画布配置</h4>
      <el-form :model="canvasConfig" label-width="80px" size="small">
        <el-form-item label="画布宽度">
          <el-input-number
            v-model="canvasConfig.width"
            :min="canvasConfig.minWidth"
            :max="canvasConfig.maxWidth"
            :step="100"
          />
        </el-form-item>

        <el-form-item label="画布高度">
          <el-input-number
            v-model="canvasConfig.height"
            :min="canvasConfig.minHeight"
            :max="canvasConfig.maxHeight"
            :step="100"
          />
        </el-form-item>

        <el-form-item label="网格大小">
          <el-input-number
            v-model="canvasConfig.gridSize"
            :min="10"
            :max="50"
            :step="5"
          />
        </el-form-item>

        <el-form-item label="显示网格">
          <el-switch v-model="canvasConfig.showGrid" />
        </el-form-item>

        <el-form-item label="背景色">
          <el-color-picker v-model="canvasConfig.backgroundColor" />
        </el-form-item>

        <div class="canvas-info">
          <h5>画布信息</h5>
          <div class="info-item">
            <span>尺寸比例:</span>
            <span>{{ (canvasConfig.width / canvasConfig.height).toFixed(2) }}:1</span>
          </div>
          <div class="info-item">
            <span>总面积:</span>
            <span>{{ (canvasConfig.width * canvasConfig.height / 10000).toFixed(1) }}万px²</span>
          </div>
        </div>
      </el-form>
    </div>

    <!-- 预览对话框 -->
    <el-dialog v-model="showPreview" title="布局预览" width="98%" :close-on-click-modal="false" class="preview-dialog" :fullscreen="false" top="2vh">
      <div class="preview-container">
        <div class="preview-canvas" :style="{ width: `${canvasConfig.width}px`, height: `${canvasConfig.height}px`, position: 'relative', margin: '0 auto' }">
          <!-- 与编辑界面相同的网格背景 -->
          <div class="grid-background-layer" :style="getGridBackgroundStyle()"></div>

          <!-- 预览区域标题 - 外部显示 -->
          <div
            v-for="zone in zones"
            :key="`preview-header-${zone.id}`"
            class="preview-zone-header-external"
            :style="{
              left: `${zone.x}px`,
              top: `${zone.y - 20}px`,
              color: zone.color,
              fontWeight: 'bold',
              fontSize: '11px',
              position: 'absolute',
              zIndex: 10,
              pointerEvents: 'none'
            }"
          >
            {{ zone.name }} ({{ zone.rows }}×{{ zone.cols }})
          </div>

          <!-- 预览区域 - 使用与编辑界面相同的结构 -->
          <div
            v-for="zone in zones"
            :key="zone.id"
            class="preview-zone"
            :style="getZoneStyle(zone)"
          >
            <!-- 工位网格预览 - 与编辑界面完全一致 -->
            <div class="workstation-grid" :style="getGridStyle(zone)">
              <div
                v-for="n in (zone.rows * zone.cols)"
                :key="n"
                class="workstation-preview"
                :style="{ backgroundColor: zone.color }"
              >
                {{ getWorkstationNumber(zone, n) }}
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <template #footer>
        <el-button @click="showPreview = false">关闭</el-button>
        <el-button type="primary" @click="exportLayout">导出配置</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Refresh, Check, View, Delete, ArrowUp, ArrowDown, ArrowLeft, ArrowRight, Upload, Download } from '@element-plus/icons-vue'

// 响应式数据
const zones = ref([])
const selectedZone = ref(null)
const selectedZones = ref([]) // 多选区域
const draggingZone = ref(null)
const resizingZone = ref(null)
const showPreview = ref(false)
const canvasRef = ref(null)
const fileInput = ref(null)
const tuneStep = ref(5) // 微调步长，默认5px

// 画布配置 - 动态可调整
const canvasConfig = ref({
  width: 2000,  // 增大画布宽度
  height: 1200, // 增大画布高度
  minWidth: 1200,
  minHeight: 800,
  maxWidth: 4000,
  maxHeight: 2400,
  gridSize: 20,
  showGrid: true,
  backgroundColor: '#fafafa'
})

// 框选功能
const isSelecting = ref(false)
const selectionBox = ref({
  startX: 0,
  startY: 0,
  endX: 0,
  endY: 0,
  visible: false
})

// 拖拽状态
const dragState = reactive({
  isDragging: false,
  isResizing: false,
  isGroupDragging: false, // 编组拖拽状态
  startX: 0,
  startY: 0,
  startZoneX: 0,
  startZoneY: 0,
  startZoneWidth: 0,
  startZoneHeight: 0,
  resizeDirection: '',
  groupStartPositions: [] // 编组初始位置
})

// 大尺寸画布的默认区域配置 - 适配新的画布尺寸 (2000×1200画布)
const defaultZones = [
  { id: 1, name: '区域1', x: 1800, y: 100, width: 120, height: 800, rows: 25, cols: 1, color: '#4A90E2', startWorkstation: 1, gapX: 2, gapY: 2 },     // 25个工位
  { id: 2, name: '区域2', x: 600, y: 800, width: 1100, height: 300, rows: 5, cols: 22, color: '#F5A623', startWorkstation: 26, gapX: 2, gapY: 2 },   // 110个工位
  { id: 3, name: '区域3', x: 100, y: 800, width: 400, height: 300, rows: 5, cols: 8, color: '#4A90E2', startWorkstation: 136, gapX: 2, gapY: 2 },     // 40个工位
  { id: 4, name: '区域4', x: 100, y: 100, width: 450, height: 600, rows: 10, cols: 9, color: '#F8A5C2', startWorkstation: 176, gapX: 2, gapY: 2 },     // 90个工位
  { id: 5, name: '区域5', x: 600, y: 100, width: 550, height: 600, rows: 10, cols: 11, color: '#50E3C2', startWorkstation: 266, gapX: 2, gapY: 2 },   // 110个工位
  { id: 6, name: '区域6', x: 1200, y: 100, width: 550, height: 600, rows: 10, cols: 11, color: '#BD10E0', startWorkstation: 376, gapX: 2, gapY: 2 }   // 110个工位
]
// 总计: 25+110+40+90+110+110 = 485个工位

// 计算总工位数
const totalWorkstations = computed(() => {
  return zones.value.reduce((sum, zone) => sum + (zone.rows * zone.cols), 0)
})

// 计算区域样式
const getZoneStyle = (zone) => {
  return {
    left: `${zone.x}px`,
    top: `${zone.y}px`,
    width: `${zone.width}px`,
    height: `${zone.height}px`,
    borderColor: zone.color
  }
}

// 计算网格样式
const getGridStyle = (zone) => {
  const gapX = zone.gapX || 2
  const gapY = zone.gapY || 2
  return {
    gridTemplateColumns: `repeat(${zone.cols}, 1fr)`,
    gridTemplateRows: `repeat(${zone.rows}, 1fr)`,
    gap: `${gapY}px ${gapX}px`
  }
}

// 计算工位编号
const getWorkstationNumber = (zone, index) => {
  return zone.startWorkstation + index - 1
}

// 获取画布样式
const getCanvasStyle = () => {
  return {
    width: `${canvasConfig.value.width}px`,
    height: `${canvasConfig.value.height}px`,
    backgroundColor: canvasConfig.value.backgroundColor,
    border: '2px solid #e5e7eb',
    borderRadius: '8px',
    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
    position: 'relative'
  }
}

// 获取网格背景样式
const getGridBackgroundStyle = () => {
  if (!canvasConfig.value.showGrid) {
    return { display: 'none' }
  }

  const gridSize = canvasConfig.value.gridSize
  const majorGridSize = gridSize * 5 // 主网格线是小网格的5倍

  return {
    position: 'absolute',
    top: '0',
    left: '0',
    width: '100%',
    height: '100%',
    pointerEvents: 'none',
    zIndex: 1,
    backgroundImage: `
      linear-gradient(to right, #d1d5db 1px, transparent 1px),
      linear-gradient(to bottom, #d1d5db 1px, transparent 1px),
      linear-gradient(to right, #e5e7eb 1px, transparent 1px),
      linear-gradient(to bottom, #e5e7eb 1px, transparent 1px)
    `,
    backgroundSize: `
      ${majorGridSize}px ${majorGridSize}px,
      ${majorGridSize}px ${majorGridSize}px,
      ${gridSize}px ${gridSize}px,
      ${gridSize}px ${gridSize}px
    `,
    opacity: 0.6
  }
}

// 处理画布点击 - 修复：防止框选后自动取消选择
const handleCanvasClick = (event) => {
  // 如果正在框选或刚完成框选，不处理点击事件
  if (isSelecting.value) return

  // 如果刚完成拖拽，不要取消选择
  if (dragState.isDragging || dragState.isGroupDragging) return

  // 检查是否点击的是真正的空白区域
  if (event.target.classList.contains('canvas-grid') ||
      event.target.classList.contains('grid-background-layer')) {
    // 只有在没有按住Ctrl键且不是刚完成操作时才取消选择
    if (!event.ctrlKey) {
      console.log('🎯 点击空白区域，清空选择')
      selectedZone.value = null
      selectedZones.value = []
      selectionBox.value.visible = false
    }
  }
}





// 添加区域
const addZone = () => {
  const newId = Math.max(...zones.value.map(z => z.id), 0) + 1
  const newZone = {
    id: newId,
    name: `区域${newId}`,
    x: 100 + (newId - 1) * 20,
    y: 50 + (newId - 1) * 20,
    width: 120,
    height: 80,
    rows: 2,
    cols: 3,
    color: `#${Math.floor(Math.random()*16777215).toString(16)}`,
    startWorkstation: getNextWorkstationStart(),
    gapX: 2,
    gapY: 2
  }
  zones.value.push(newZone)
  selectedZone.value = newZone
  ElMessage.success('区域添加成功')
}

// 获取下一个工位起始编号
const getNextWorkstationStart = () => {
  let maxEnd = 0
  zones.value.forEach(zone => {
    const end = zone.startWorkstation + (zone.rows * zone.cols) - 1
    if (end > maxEnd) maxEnd = end
  })
  return maxEnd + 1
}

// 根据ID获取区域
const getZoneById = (zoneId) => {
  return zones.value.find(z => z.id === zoneId)
}

// 获取选择状态文本
const getSelectionStatusText = () => {
  if (dragState.isGroupDragging) return '编组拖拽中'
  if (isSelecting.value) return '框选中'
  if (selectedZones.value.length > 1) return '多选编组'
  if (selectedZones.value.length === 1) return '单选'
  return '未选择'
}

// 获取选择状态样式类
const getSelectionStatusClass = () => {
  if (dragState.isGroupDragging) return 'dragging'
  if (isSelecting.value) return 'selecting'
  if (selectedZones.value.length > 1) return 'multi'
  if (selectedZones.value.length === 1) return 'single'
  return 'none'
}

// 选择区域 - 支持Ctrl多选
const selectZone = (zone, event) => {
  // 阻止事件冒泡，避免触发画布点击
  if (event) {
    event.stopPropagation()
  }

  if (event && event.ctrlKey) {
    // Ctrl+点击多选/取消选择
    if (selectedZones.value.includes(zone.id)) {
      // 如果已选中，则取消选择
      selectedZones.value = selectedZones.value.filter(id => id !== zone.id)
      // 如果取消选择的是当前区域，选择其他区域或清空
      if (selectedZone.value?.id === zone.id) {
        selectedZone.value = selectedZones.value.length > 0
          ? zones.value.find(z => z.id === selectedZones.value[0])
          : null
      }
    } else {
      // 添加到选择列表
      selectedZones.value.push(zone.id)
      selectedZone.value = zone
    }
  } else {
    // 单选模式
    selectedZones.value = [zone.id]
    selectedZone.value = zone
  }
}

// 框选框样式计算
const getSelectionBoxStyle = () => {
  const left = Math.min(selectionBox.value.startX, selectionBox.value.endX)
  const top = Math.min(selectionBox.value.startY, selectionBox.value.endY)
  const width = Math.abs(selectionBox.value.endX - selectionBox.value.startX)
  const height = Math.abs(selectionBox.value.endY - selectionBox.value.startY)

  return {
    position: 'absolute',
    left: `${left}px`,
    top: `${top}px`,
    width: `${width}px`,
    height: `${height}px`,
    border: '2px dashed #3b82f6',
    background: 'rgba(59, 130, 246, 0.1)',
    pointerEvents: 'none',
    zIndex: 1000
  }
}

// 多选边框样式计算
const getMultiSelectionBorderStyle = () => {
  if (selectedZones.value.length <= 1) return {}

  // 计算所有选中区域的边界
  const selectedZoneObjects = zones.value.filter(z => selectedZones.value.includes(z.id))
  if (selectedZoneObjects.length === 0) return {}

  let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity

  selectedZoneObjects.forEach(zone => {
    minX = Math.min(minX, zone.x)
    minY = Math.min(minY, zone.y)
    maxX = Math.max(maxX, zone.x + zone.width)
    maxY = Math.max(maxY, zone.y + zone.height)
  })

  // 添加边距
  const padding = 10
  minX -= padding
  minY -= padding
  maxX += padding
  maxY += padding

  return {
    position: 'absolute',
    left: `${minX}px`,
    top: `${minY}px`,
    width: `${maxX - minX}px`,
    height: `${maxY - minY}px`,
    border: '3px dashed #10b981',
    background: 'rgba(16, 185, 129, 0.05)',
    borderRadius: '8px',
    pointerEvents: 'none',
    zIndex: 999
  }
}

// 统一的画布鼠标按下处理
const handleCanvasMouseDown = (event) => {
  // 如果点击的是区域或调整手柄，不启动框选
  if (event.target.classList.contains('draggable-zone') ||
      event.target.closest('.draggable-zone') ||
      event.target.classList.contains('resize-handle')) {
    return
  }

  // 启动框选
  startCanvasSelection(event)
}

// 开始画布框选
const startCanvasSelection = (event) => {
  console.log('🎯 开始框选')

  // 如果没有按住Ctrl键，清空之前的选择
  if (!event.ctrlKey) {
    selectedZones.value = []
    selectedZone.value = null
  }

  const rect = event.currentTarget.getBoundingClientRect()
  selectionBox.value.startX = event.clientX - rect.left
  selectionBox.value.startY = event.clientY - rect.top
  selectionBox.value.endX = selectionBox.value.startX
  selectionBox.value.endY = selectionBox.value.startY
  selectionBox.value.visible = true
  isSelecting.value = true

  event.preventDefault()
}

// 更新画布框选
const updateCanvasSelection = (event) => {
  if (!isSelecting.value) return

  // 获取画布元素的边界
  const canvasElement = canvasRef.value?.querySelector('.canvas-grid')
  if (!canvasElement) return

  const rect = canvasElement.getBoundingClientRect()
  
  // 限制框选范围在画布内
  const newEndX = Math.max(0, Math.min(canvasConfig.value.width, event.clientX - rect.left))
  const newEndY = Math.max(0, Math.min(canvasConfig.value.height, event.clientY - rect.top))
  
  selectionBox.value.endX = newEndX
  selectionBox.value.endY = newEndY

  // 检查哪些区域在框选范围内
  const newSelectedIds = []
  zones.value.forEach(zone => {
    if (isZoneInSelection(zone)) {
      newSelectedIds.push(zone.id)
    }
  })

  console.log(`📦 框选更新，发现 ${newSelectedIds.length} 个区域:`, newSelectedIds)

  // 如果按住Ctrl键，合并选择；否则替换选择
  if (event.ctrlKey) {
    // 合并新选择的区域到现有选择中
    const combinedIds = [...new Set([...selectedZones.value, ...newSelectedIds])]
    selectedZones.value = combinedIds
  } else {
    selectedZones.value = newSelectedIds
  }
}

// 结束画布框选 - 修复：不自动取消选择
const endCanvasSelection = () => {
  if (isSelecting.value) {
    console.log(`🎯 框选结束，选中了 ${selectedZones.value.length} 个区域:`, selectedZones.value)

    // 结束框选状态，但保持选择
    isSelecting.value = false
    selectionBox.value.visible = false

    // 如果有选中的区域，设置第一个为当前选中区域，并保持所有选择
    if (selectedZones.value.length > 0) {
      selectedZone.value = zones.value.find(z => z.id === selectedZones.value[0])
      console.log(`✅ 框选完成，当前选中区域: ${selectedZone.value?.name}`)
      console.log(`🔒 保持选择状态，选中区域列表: [${selectedZones.value}]`)

      // 如果选中了多个区域，提示用户可以进行编组拖拽
      if (selectedZones.value.length > 1) {
        console.log(`🔥 多选状态激活！已选中 ${selectedZones.value.length} 个区域，可以拖拽任意区域进行编组移动`)
        ElMessage.success(`已选中 ${selectedZones.value.length} 个区域，可拖拽进行编组移动`)
      } else {
        ElMessage.success(`已选中区域: ${selectedZone.value?.name}`)
      }
    } else {
      selectedZone.value = null
      console.log(`📭 框选完成，没有选中任何区域`)
    }

    // 重要：框选结束后不清空选择状态，保持用户的选择
    // 只有用户主动点击空白区域或按Esc键才会取消选择
  }
}

// 检查区域是否在框选范围内
const isZoneInSelection = (zone) => {
  const selLeft = Math.min(selectionBox.value.startX, selectionBox.value.endX)
  const selTop = Math.min(selectionBox.value.startY, selectionBox.value.endY)
  const selRight = Math.max(selectionBox.value.startX, selectionBox.value.endX)
  const selBottom = Math.max(selectionBox.value.startY, selectionBox.value.endY)

  const zoneLeft = zone.x
  const zoneTop = zone.y
  const zoneRight = zone.x + zone.width
  const zoneBottom = zone.y + zone.height

  // 检查是否有重叠
  return !(selRight < zoneLeft || selLeft > zoneRight ||
           selBottom < zoneTop || selTop > zoneBottom)
}

// 删除区域
const deleteZone = (zoneId) => {
  ElMessageBox.confirm('确定要删除这个区域吗？', '确认删除', {
    type: 'warning'
  }).then(() => {
    zones.value = zones.value.filter(z => z.id !== zoneId)
    if (selectedZone.value?.id === zoneId) {
      selectedZone.value = null
    }
    ElMessage.success('区域删除成功')
  }).catch(() => {})
}

// 重置布局
const resetLayout = () => {
  ElMessageBox.confirm('确定要重置为默认布局吗？', '确认重置', {
    type: 'warning'
  }).then(() => {
    zones.value = [...defaultZones]
    selectedZone.value = null
    ElMessage.success('布局重置成功')
  }).catch(() => {})
}

// 更新区域网格
const updateZoneGrid = () => {
  // 重新计算工位起始编号
  updateWorkstationNumbers()
}

// 更新工位编号
const updateWorkstationNumbers = () => {
  // 按区域顺序重新分配工位编号
  let currentStart = 1
  zones.value.sort((a, b) => a.id - b.id).forEach(zone => {
    zone.startWorkstation = currentStart
    currentStart += zone.rows * zone.cols
  })
}

// 精确微调位置 - 使用动态画布边界
const fineTunePosition = (direction) => {
  if (!selectedZone.value) {
    ElMessage.warning('请先选择一个区域')
    return
  }

  const zone = selectedZone.value
  const step = tuneStep.value
  const maxX = canvasConfig.value.width - zone.width
  const maxY = canvasConfig.value.height - zone.height

  switch (direction) {
    case 'up':
      zone.y = Math.max(0, zone.y - step)
      break
    case 'down':
      zone.y = Math.min(maxY, zone.y + step)
      break
    case 'left':
      zone.x = Math.max(0, zone.x - step)
      break
    case 'right':
      zone.x = Math.min(maxX, zone.x + step)
      break
  }
}

// 开始拖拽
const startDrag = (zone, event) => {
  if (event.target.classList.contains('resize-handle')) return

  // 阻止事件冒泡
  event.stopPropagation()

  console.log(`🚀 开始拖拽检查 - 区域: ${zone.name} (ID: ${zone.id})`)
  console.log(`📋 当前选中状态 - selectedZones: [${selectedZones.value}], 数量: ${selectedZones.value.length}`)

  // 检查是否是编组拖拽（当前区域已在多选列表中）
  const isAlreadySelected = selectedZones.value.includes(zone.id)
  const hasMultipleSelected = selectedZones.value.length > 1
  const isGroupDrag = isAlreadySelected && hasMultipleSelected

  console.log(`🔍 拖拽判断 - 已选中: ${isAlreadySelected}, 多选: ${hasMultipleSelected}, 编组拖拽: ${isGroupDrag}`)

  if (isGroupDrag) {
    // 编组拖拽：不改变选择状态，直接开始拖拽
    dragState.isGroupDragging = true
    dragState.isDragging = false
    dragState.startX = event.clientX
    dragState.startY = event.clientY

    // 记录所有选中区域的初始位置
    dragState.groupStartPositions = selectedZones.value.map(zoneId => {
      const targetZone = zones.value.find(z => z.id === zoneId)
      return {
        id: zoneId,
        startX: targetZone.x,
        startY: targetZone.y
      }
    })

    console.log(`🔥 开始编组拖拽，选中 ${selectedZones.value.length} 个区域:`, selectedZones.value)
    ElMessage.success(`开始编组拖拽 ${selectedZones.value.length} 个区域`)
    
    // 确保被拖拽的区域是当前选中区域
    selectedZone.value = zone
  } else {
    console.log(`🎯 单个区域拖拽模式`)
    // 单个区域拖拽：更新选择状态
    if (!selectedZones.value.includes(zone.id)) {
      if (event.ctrlKey) {
        // Ctrl+拖拽：添加到选择列表
        selectedZones.value.push(zone.id)
        console.log(`➕ Ctrl+拖拽：添加到选择 - 新状态: [${selectedZones.value}]`)
      } else {
        // 普通拖拽：替换选择
        selectedZones.value = [zone.id]
        console.log(`🔄 普通拖拽：替换选择 - 新状态: [${selectedZones.value}]`)
      }
    }
    selectedZone.value = zone

    dragState.isGroupDragging = false
    dragState.isDragging = true
    dragState.startX = event.clientX
    dragState.startY = event.clientY
    dragState.startZoneX = zone.x
    dragState.startZoneY = zone.y
    draggingZone.value = zone

    console.log(`🎯 开始单个区域拖拽: ${zone.name}`)
  }

  event.preventDefault()
}

// 开始调整大小
const startResize = (zone, direction, event) => {
  resizingZone.value = zone
  dragState.isResizing = true
  dragState.resizeDirection = direction
  dragState.startX = event.clientX
  dragState.startY = event.clientY
  dragState.startZoneX = zone.x
  dragState.startZoneY = zone.y
  dragState.startZoneWidth = zone.width
  dragState.startZoneHeight = zone.height

  event.preventDefault()
  event.stopPropagation()
}

// 鼠标移动处理 - 使用动态画布边界
const handleMouseMove = (event) => {
  // 框选处理 - 优先级最高
  if (isSelecting.value) {
    updateCanvasSelection(event)
    return
  }

  // 编组拖拽处理 - 修复跟随鼠标逻辑和边界处理
  if (dragState.isGroupDragging) {
    const deltaX = event.clientX - dragState.startX
    const deltaY = event.clientY - dragState.startY

    console.log(`🔥 编组拖拽 - 鼠标移动: (${deltaX}, ${deltaY})`)

    // 计算编组在新位置的整体边界
    let groupMinX = Infinity, groupMinY = Infinity
    let groupMaxX = -Infinity, groupMaxY = -Infinity

    // 先计算如果按照鼠标移动，编组的新边界
    dragState.groupStartPositions.forEach(pos => {
      const zone = zones.value.find(z => z.id === pos.id)
      if (zone) {
        const newX = pos.startX + deltaX
        const newY = pos.startY + deltaY
        groupMinX = Math.min(groupMinX, newX)
        groupMinY = Math.min(groupMinY, newY)
        groupMaxX = Math.max(groupMaxX, newX + zone.width)
        groupMaxY = Math.max(groupMaxY, newY + zone.height)
      }
    })

    // 计算边界限制 - 确保整个编组不超出画布
    let finalDeltaX = deltaX
    let finalDeltaY = deltaY

    // X轴边界检查
    if (groupMinX < 0) {
      finalDeltaX = deltaX - groupMinX  // 向右调整
    } else if (groupMaxX > canvasConfig.value.width) {
      finalDeltaX = deltaX - (groupMaxX - canvasConfig.value.width)  // 向左调整
    }

    // Y轴边界检查
    if (groupMinY < 0) {
      finalDeltaY = deltaY - groupMinY  // 向下调整
    } else if (groupMaxY > canvasConfig.value.height) {
      finalDeltaY = deltaY - (groupMaxY - canvasConfig.value.height)  // 向上调整
    }

    // 应用最终的移动到所有区域
    dragState.groupStartPositions.forEach(pos => {
      const zone = zones.value.find(z => z.id === pos.id)
      if (zone) {
        zone.x = pos.startX + finalDeltaX
        zone.y = pos.startY + finalDeltaY
      }
    })

    // 调试信息（减少频率）
    if (Math.abs(deltaX) % 20 === 0 || Math.abs(deltaY) % 20 === 0) {
      console.log(`🔥 编组拖拽 - 原始移动: (${deltaX}, ${deltaY}), 最终移动: (${finalDeltaX}, ${finalDeltaY})`)
      console.log(`📐 编组边界: (${groupMinX}, ${groupMinY}) 到 (${groupMaxX}, ${groupMaxY})`)
    }
  }

  // 单个区域拖拽处理
  if (dragState.isDragging && draggingZone.value) {
    const deltaX = event.clientX - dragState.startX
    const deltaY = event.clientY - dragState.startY
    const maxX = canvasConfig.value.width - draggingZone.value.width
    const maxY = canvasConfig.value.height - draggingZone.value.height

    // 严格边界限制，不允许超出
    draggingZone.value.x = Math.max(0, Math.min(maxX, dragState.startZoneX + deltaX))
    draggingZone.value.y = Math.max(0, Math.min(maxY, dragState.startZoneY + deltaY))
  }

  if (dragState.isResizing && resizingZone.value) {
    const deltaX = event.clientX - dragState.startX
    const deltaY = event.clientY - dragState.startY
    const zone = resizingZone.value
    const direction = dragState.resizeDirection
    const canvasWidth = canvasConfig.value.width
    const canvasHeight = canvasConfig.value.height

    // 单方向调整大小 - 使用动态画布边界
    if (direction === 'e') {
      // 只调整右边
      zone.width = Math.max(50, Math.min(canvasWidth - zone.x, dragState.startZoneWidth + deltaX))
    } else if (direction === 'w') {
      // 只调整左边
      const newWidth = Math.max(50, dragState.startZoneWidth - deltaX)
      const maxX = dragState.startZoneX + dragState.startZoneWidth - 50
      zone.x = Math.max(0, Math.min(maxX, dragState.startZoneX + deltaX))
      zone.width = dragState.startZoneWidth + (dragState.startZoneX - zone.x)
    } else if (direction === 's') {
      // 只调整下边
      zone.height = Math.max(40, Math.min(canvasHeight - zone.y, dragState.startZoneHeight + deltaY))
    } else if (direction === 'n') {
      // 只调整上边
      const newHeight = Math.max(40, dragState.startZoneHeight - deltaY)
      const maxY = dragState.startZoneY + dragState.startZoneHeight - 40
      zone.y = Math.max(0, Math.min(maxY, dragState.startZoneY + deltaY))
      zone.height = dragState.startZoneHeight + (dragState.startZoneY - zone.y)
    } else if (direction === 'ne') {
      // 右上角
      zone.width = Math.max(50, Math.min(canvasWidth - zone.x, dragState.startZoneWidth + deltaX))
      const maxY = dragState.startZoneY + dragState.startZoneHeight - 40
      zone.y = Math.max(0, Math.min(maxY, dragState.startZoneY + deltaY))
      zone.height = dragState.startZoneHeight + (dragState.startZoneY - zone.y)
    } else if (direction === 'nw') {
      // 左上角
      const maxX = dragState.startZoneX + dragState.startZoneWidth - 50
      zone.x = Math.max(0, Math.min(maxX, dragState.startZoneX + deltaX))
      zone.width = dragState.startZoneWidth + (dragState.startZoneX - zone.x)
      const maxY = dragState.startZoneY + dragState.startZoneHeight - 40
      zone.y = Math.max(0, Math.min(maxY, dragState.startZoneY + deltaY))
      zone.height = dragState.startZoneHeight + (dragState.startZoneY - zone.y)
    } else if (direction === 'se') {
      // 右下角
      zone.width = Math.max(50, Math.min(canvasWidth - zone.x, dragState.startZoneWidth + deltaX))
      zone.height = Math.max(40, Math.min(canvasHeight - zone.y, dragState.startZoneHeight + deltaY))
    } else if (direction === 'sw') {
      // 左下角
      const maxX = dragState.startZoneX + dragState.startZoneWidth - 50
      zone.x = Math.max(0, Math.min(maxX, dragState.startZoneX + deltaX))
      zone.width = dragState.startZoneWidth + (dragState.startZoneX - zone.x)
      zone.height = Math.max(40, Math.min(canvasHeight - zone.y, dragState.startZoneHeight + deltaY))
    }
  }
}

// 鼠标释放处理
const handleMouseUp = (event) => {
  // 处理框选结束 - 优先处理框选
  if (isSelecting.value) {
    console.log('🎯 检测到框选状态，结束框选')
    endCanvasSelection()
    return
  }

  if (dragState.isDragging || dragState.isGroupDragging) {
    console.log(`结束拖拽 - 编组: ${dragState.isGroupDragging}, 单个: ${dragState.isDragging}`)

    // 拖拽结束后，保持当前拖拽的区域为选中状态 - 核心逻辑
    if (dragState.isGroupDragging) {
      // 编组拖拽：保持所有选中的区域
      console.log(`✅ 编组拖拽结束，保持 ${selectedZones.value.length} 个区域选中`)
    } else if (dragState.isDragging && draggingZone.value) {
      // 单个拖拽：确保拖拽的区域被选中
      selectedZone.value = draggingZone.value
      if (!selectedZones.value.includes(draggingZone.value.id)) {
        selectedZones.value = [draggingZone.value.id]
      }
      console.log(`✅ 单个拖拽结束，选中区域: ${draggingZone.value.name}`)
    }
  }

  // 重置拖拽状态
  dragState.isDragging = false
  dragState.isResizing = false
  dragState.isGroupDragging = false
  dragState.groupStartPositions = []
  draggingZone.value = null
  resizingZone.value = null

  // 保持选择状态，不清空 selectedZone 和 selectedZones
}

// 键盘事件处理
const handleKeyDown = (event) => {
  if (event.key === 'Escape') {
    // Esc键取消所有选择
    selectedZones.value = []
    selectedZone.value = null
    selectionBox.value.visible = false
    isSelecting.value = false
  } else if (event.key === 'Delete' && (selectedZones.value.length > 0 || selectedZone.value)) {
    // Delete键删除选中的区域
    if (selectedZones.value.length > 0) {
      ElMessageBox.confirm(`确定要删除这 ${selectedZones.value.length} 个区域吗？`, '批量删除', {
        type: 'warning'
      }).then(() => {
        zones.value = zones.value.filter(z => !selectedZones.value.includes(z.id))
        selectedZones.value = []
        selectedZone.value = null
        ElMessage.success('区域删除成功')
      }).catch(() => {})
    } else if (selectedZone.value) {
      deleteZone(selectedZone.value.id)
    }
  }
}

// 设置事件监听器（使用 passive 选项优化性能）
const setupEventListeners = () => {
  document.addEventListener('mousemove', handleMouseMove, { passive: true })
  document.addEventListener('mouseup', handleMouseUp, { passive: true })
  document.addEventListener('keydown', handleKeyDown, { passive: true })
}

// 移除事件监听器
const removeEventListeners = () => {
  document.removeEventListener('mousemove', handleMouseMove, { passive: true })
  document.removeEventListener('mouseup', handleMouseUp, { passive: true })
  document.removeEventListener('keydown', handleKeyDown, { passive: true })
}

// 预览布局
const previewLayout = () => {
  showPreview.value = true
}

// 保存布局 - 包含完整的画布配置
const saveLayout = () => {
  const layoutConfig = {
    zones: zones.value.map(zone => ({
      id: zone.id,
      name: zone.name,
      x: zone.x,
      y: zone.y,
      width: zone.width,
      height: zone.height,
      rows: zone.rows,
      cols: zone.cols,
      color: zone.color,
      startWorkstation: zone.startWorkstation,
      gapX: zone.gapX || 2,
      gapY: zone.gapY || 2,
      totalWorkstations: zone.rows * zone.cols
    })),
    totalWorkstations: zones.value.reduce((sum, zone) => sum + (zone.rows * zone.cols), 0),
    canvas: {
      width: canvasConfig.value.width,
      height: canvasConfig.value.height,
      gridSize: canvasConfig.value.gridSize,
      showGrid: canvasConfig.value.showGrid,
      backgroundColor: canvasConfig.value.backgroundColor
    },
    metadata: {
      version: '2.0',
      timestamp: new Date().toISOString(),
      totalZones: zones.value.length,
      designerVersion: 'FactoryLayoutDesigner v2.0'
    }
  }

  // 触发保存事件
  emit('layout-saved', layoutConfig)
  ElMessage.success('布局保存成功')
}

// 触发文件导入
const triggerImport = () => {
  fileInput.value?.click()
}

// 处理文件导入
const handleFileImport = (event) => {
  const file = event.target.files[0]
  if (!file) return

  if (!file.name.endsWith('.json')) {
    ElMessage.error('请选择JSON格式的文件')
    return
  }

  const reader = new FileReader()
  reader.onload = (e) => {
    try {
      const config = JSON.parse(e.target.result)
      importLayout(config)
    } catch (error) {
      ElMessage.error('文件格式错误，请检查JSON文件')
      console.error('JSON解析错误:', error)
    }
  }
  reader.readAsText(file)

  // 清空input值，允许重复选择同一文件
  event.target.value = ''
}

// 导入布局配置 - 支持画布配置恢复
const importLayout = (config) => {
  try {
    // 验证配置格式
    if (!config.zones || !Array.isArray(config.zones)) {
      throw new Error('配置文件格式错误：缺少zones数组')
    }

    // 验证每个区域的必要字段
    const requiredFields = ['id', 'name', 'x', 'y', 'width', 'height', 'rows', 'cols', 'color', 'startWorkstation']
    for (const zone of config.zones) {
      for (const field of requiredFields) {
        if (zone[field] === undefined || zone[field] === null) {
          throw new Error(`区域 ${zone.name || zone.id} 缺少必要字段: ${field}`)
        }
      }

      // 确保间距字段存在，如果不存在则设置默认值
      if (zone.gapX === undefined) zone.gapX = 2
      if (zone.gapY === undefined) zone.gapY = 2
    }

    // 恢复画布配置
    if (config.canvas) {
      canvasConfig.value = {
        ...canvasConfig.value,
        ...config.canvas
      }
      console.log('画布配置已恢复:', canvasConfig.value)
    }

    // 应用区域配置
    zones.value = [...config.zones]
    selectedZone.value = null
    selectedZones.value = []

    ElMessage.success(`成功导入 ${config.zones.length} 个区域的布局配置`)

    // 显示导入信息
    if (config.metadata?.timestamp) {
      const importTime = new Date(config.metadata.timestamp).toLocaleString('zh-CN')
      ElMessage.info(`布局创建时间: ${importTime}`)
    }

    // 显示画布信息
    ElMessage.info(`画布尺寸: ${canvasConfig.value.width} × ${canvasConfig.value.height}`)

  } catch (error) {
    ElMessage.error(`导入失败: ${error.message}`)
    console.error('导入错误:', error)
  }
}

// 导出配置 - 包含完整的设计信息
const exportLayout = () => {
  const config = {
    zones: zones.value.map(zone => ({
      id: zone.id,
      name: zone.name,
      x: zone.x,
      y: zone.y,
      width: zone.width,
      height: zone.height,
      rows: zone.rows,
      cols: zone.cols,
      color: zone.color,
      startWorkstation: zone.startWorkstation,
      gapX: zone.gapX || 2,
      gapY: zone.gapY || 2,
      totalWorkstations: zone.rows * zone.cols
    })),
    canvas: {
      width: canvasConfig.value.width,
      height: canvasConfig.value.height,
      gridSize: canvasConfig.value.gridSize,
      showGrid: canvasConfig.value.showGrid,
      backgroundColor: canvasConfig.value.backgroundColor,
      // 边界信息
      boundaries: {
        minX: 0,
        minY: 0,
        maxX: canvasConfig.value.width,
        maxY: canvasConfig.value.height
      }
    },
    metadata: {
      version: '2.0',
      timestamp: new Date().toISOString(),
      totalZones: zones.value.length,
      totalWorkstations: zones.value.reduce((sum, zone) => sum + (zone.rows * zone.cols), 0),
      designerVersion: 'FactoryLayoutDesigner v2.0',
      exportedBy: 'FactoryLayoutDesigner',
      // 比例关系信息
      proportions: {
        aspectRatio: canvasConfig.value.width / canvasConfig.value.height,
        gridDensity: (canvasConfig.value.width * canvasConfig.value.height) / (canvasConfig.value.gridSize * canvasConfig.value.gridSize)
      }
    }
  }

  const blob = new Blob([JSON.stringify(config, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `factory-layout-${Date.now()}.json`
  a.click()
  URL.revokeObjectURL(url)

  ElMessage.success('配置导出成功')
  console.log('导出的配置:', config)
}

// 定义事件
const emit = defineEmits(['layout-saved', 'layout-updated'])

// 初始化
onMounted(() => {
  zones.value = [...defaultZones]
  setupEventListeners()
})

onUnmounted(() => {
  removeEventListeners()
})
</script>

<style scoped>
.layout-designer {
  display: grid;
  grid-template-columns: 300px 1fr 250px;
  height: 100vh;
  background: #f5f5f5;
}

.designer-toolbar {
  background: white;
  border-right: 1px solid #e5e7eb;
  padding: 1rem;
  overflow-y: auto;
}

.toolbar-section h3 {
  margin: 0 0 1rem 0;
  color: #374151;
}

.layout-stats {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
  padding: 0.75rem;
  background: #f8fafc;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-label {
  font-size: 0.875rem;
  color: #64748b;
  font-weight: 500;
}

.stat-value {
  font-size: 1rem;
  color: #1e293b;
  font-weight: 600;
  background: #3b82f6;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  min-width: 2rem;
  text-align: center;
}

.toolbar-actions {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 2rem;
}

.file-operations {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e5e7eb;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.operation-status {
  margin-top: 1rem;
  padding: 0.5rem;
  border-radius: 6px;
  background: #f9fafb;
}

.selection-details {
  margin-top: 1rem;
  padding: 1rem;
  background: #f1f5f9;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
}

.selection-details h5 {
  margin: 0 0 0.75rem 0;
  color: #374151;
  font-size: 0.875rem;
  font-weight: 600;
}

.details-content {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.75rem;
}

.detail-item .label {
  color: #64748b;
  font-weight: 500;
}

.detail-item .value {
  color: #1e293b;
  font-weight: 600;
}

.detail-item .status {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.7rem;
}

.status.single { background: #dbeafe; color: #1e40af; }
.status.multi { background: #d1fae5; color: #065f46; }
.status.selecting { background: #fef3c7; color: #92400e; }
.status.dragging { background: #fed7aa; color: #9a3412; }

.selected-zones-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.zones-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
}

.zone-tag {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  color: white;
  font-size: 0.7rem;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0,0,0,0.5);
  border: 1px solid rgba(255,255,255,0.3);
}

.operation-tips {
  margin-top: 1.5rem;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
}

.operation-tips h5 {
  margin: 0 0 0.75rem 0;
  color: #374151;
  font-size: 0.875rem;
  font-weight: 600;
}

.tip-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  font-size: 0.75rem;
}

.tip-key {
  background: #e5e7eb;
  color: #374151;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-weight: 600;
  font-family: monospace;
  min-width: 80px;
  text-align: center;
}

.tip-desc {
  color: #64748b;
  font-weight: 500;
}

.zones-list h4 {
  margin: 0 0 1rem 0;
  color: #6b7280;
  font-size: 0.875rem;
}

.zone-items {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.zone-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
}

.zone-item:hover {
  background: #f9fafb;
  border-color: #3b82f6;
}

.zone-item.active {
  background: #eff6ff;
  border-color: #3b82f6;
}

.zone-color {
  width: 16px;
  height: 16px;
  border-radius: 3px;
  border: 1px solid #d1d5db;
}

.zone-name {
  flex: 1;
  font-size: 0.875rem;
  font-weight: 500;
}

.zone-info {
  font-size: 0.75rem;
  color: #6b7280;
}

.designer-canvas {
  position: relative;
  background: #ffffff;
  overflow: auto;
  padding: 1rem;
}

.canvas-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100%;
}

.canvas-grid {
  position: relative;
  /* 动态尺寸通过内联样式设置 */
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  background: #fafafa;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  /* 确保画布边界清晰可见 */
  border-style: solid;
  border-width: 3px;
}

.grid-background-layer {
  /* 动态网格样式通过内联样式设置 */
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
  opacity: 0.6;
}

.draggable-zone {
  position: absolute;
  border: 2px dashed #6b7280;
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.9);
  cursor: move;
  transition: all 0.2s;
  min-width: 30px;
  min-height: 25px;
  z-index: 10;
}

.draggable-zone:hover {
  border-color: #3b82f6;
  background: rgba(59, 130, 246, 0.05);
}

.draggable-zone.selected {
  border-color: #3b82f6;
  border-style: solid;
  background: rgba(59, 130, 246, 0.1);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

.draggable-zone.single-selected {
  border-color: #3b82f6;
  border-style: solid;
  background: rgba(59, 130, 246, 0.1);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

.draggable-zone.multi-selected {
  border-color: #10b981;
  border-style: solid;
  border-width: 3px;
  background: rgba(16, 185, 129, 0.15);
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.4);
  animation: multi-select-pulse 2s infinite;
}

.draggable-zone.group-dragging {
  border-color: #f59e0b;
  border-style: solid;
  background: rgba(245, 158, 11, 0.15);
  box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.4);
  transform: scale(1.02);
  transition: none;
}

/* 框选框样式 */
.selection-box {
  border: 2px dashed #3b82f6;
  background: rgba(59, 130, 246, 0.1);
  pointer-events: none;
  z-index: 1000;
  position: relative;
}

.selection-info {
  position: absolute;
  top: -25px;
  left: 0;
  background: #3b82f6;
  color: white;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  white-space: nowrap;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

/* 多选边框样式 */
.multi-selection-border {
  border: 3px dashed #10b981;
  background: rgba(16, 185, 129, 0.05);
  border-radius: 8px;
  pointer-events: none;
  z-index: 999;
  animation: pulse-border 2s infinite;
}

@keyframes pulse-border {
  0%, 100% {
    border-color: #10b981;
    box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.4);
  }
  50% {
    border-color: #059669;
    box-shadow: 0 0 0 4px rgba(16, 185, 129, 0.2);
  }
}

@keyframes multi-select-pulse {
  0%, 100% {
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.4);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 0 5px rgba(16, 185, 129, 0.6);
    transform: scale(1.01);
  }
}

.zone-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2px 4px;
  background: rgba(0, 0, 0, 0.05);
  font-size: 10px;
  font-weight: 600;
  border-radius: 4px 4px 0 0;
}

.workstation-grid {
  display: grid;
  gap: 1px;
  padding: 2px;
  height: 100%;
  width: 100%;
}

.workstation-preview {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: 600;
  color: white;
  border-radius: 3px;
  min-height: 16px;
  min-width: 20px;
  text-shadow: 0 1px 2px rgba(0,0,0,0.5);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.2s ease;
}

.workstation-preview:hover {
  transform: scale(1.1);
  box-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.properties-panel {
  background: white;
  border-left: 1px solid #e5e7eb;
  padding: 1rem;
  overflow-y: auto;
}

.canvas-config-panel {
  background: white;
  border-left: 1px solid #e5e7eb;
  padding: 1rem;
  overflow-y: auto;
}

.canvas-config-panel h4 {
  margin: 0 0 1rem 0;
  color: #374151;
}

.canvas-info {
  margin-top: 1.5rem;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
}

.canvas-info h5 {
  margin: 0 0 0.75rem 0;
  color: #374151;
  font-size: 0.875rem;
  font-weight: 600;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
}

.info-item span:first-child {
  color: #64748b;
  font-weight: 500;
}

.info-item span:last-child {
  color: #1e293b;
  font-weight: 600;
}

/* 微调控制样式 */
.fine-tune-controls {
  margin: 1rem 0;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
}

.fine-tune-controls h5 {
  margin: 0 0 0.75rem 0;
  color: #374151;
  font-size: 0.875rem;
  font-weight: 600;
}

.tune-grid {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.tune-row {
  display: flex;
  gap: 0.5rem;
}

.tune-step {
  margin-top: 0.5rem;
}

.tune-grid .el-button {
  min-width: 60px;
}

.properties-panel h4 {
  margin: 0 0 1rem 0;
  color: #374151;
}

/* 调整手柄 */
.resize-handles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.resize-handle {
  position: absolute;
  background: #3b82f6;
  border: 1px solid white;
  pointer-events: all;
  z-index: 10;
}

.resize-handle.nw { top: -4px; left: -4px; width: 8px; height: 8px; cursor: nw-resize; }
.resize-handle.ne { top: -4px; right: -4px; width: 8px; height: 8px; cursor: ne-resize; }
.resize-handle.sw { bottom: -4px; left: -4px; width: 8px; height: 8px; cursor: sw-resize; }
.resize-handle.se { bottom: -4px; right: -4px; width: 8px; height: 8px; cursor: se-resize; }
.resize-handle.n { top: -4px; left: 50%; transform: translateX(-50%); width: 8px; height: 8px; cursor: n-resize; }
.resize-handle.s { bottom: -4px; left: 50%; transform: translateX(-50%); width: 8px; height: 8px; cursor: s-resize; }
.resize-handle.w { top: 50%; left: -4px; transform: translateY(-50%); width: 8px; height: 8px; cursor: w-resize; }
.resize-handle.e { top: 50%; right: -4px; transform: translateY(-50%); width: 8px; height: 8px; cursor: e-resize; }

/* 预览样式 */
.preview-container {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding: 2rem;
  background: #f9fafb;
  border-radius: 8px;
  overflow: auto;
  min-height: 70vh;
  max-height: none;
}

.preview-dialog .el-dialog__body {
  padding: 1rem;
  overflow: visible;
  max-height: none;
}

.preview-dialog .el-dialog {
  margin-top: 2vh !important;
  margin-bottom: 2vh !important;
  max-height: 96vh;
  overflow: visible;
}

.preview-dialog .el-dialog__wrapper {
  overflow: auto;
}

.preview-canvas {
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  overflow: visible;
}

.preview-zone {
  position: absolute;
  border: 2px solid;
  border-radius: 4px;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.9);
}

.preview-zone-header {
  background: rgba(0, 0, 0, 0.1);
  color: #374151;
  font-size: 10px;
  font-weight: 600;
  padding: 2px 4px;
  text-align: center;
}

.preview-workstations {
  display: grid;
  gap: 1px;
  padding: 2px;
  height: 100%;
  width: 100%;
}

.preview-zone .workstation-preview {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 8px;
  font-weight: 600;
  color: white;
  border-radius: 2px;
  min-height: 10px;
  text-shadow: 0 1px 2px rgba(0,0,0,0.5);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 响应式设计 */
@media (max-width: 1600px) {
  .canvas-grid {
    width: 1400px;
    height: 700px;
  }

  .layout-designer {
    grid-template-columns: 280px 1fr 220px;
  }
}

@media (max-width: 1200px) {
  .layout-designer {
    grid-template-columns: 250px 1fr 200px;
  }

  .canvas-grid {
    width: 1200px;
    height: 600px;
  }
}

@media (max-width: 768px) {
  .layout-designer {
    grid-template-columns: 1fr;
    grid-template-rows: auto 1fr auto;
  }

  .designer-toolbar {
    border-right: none;
    border-bottom: 1px solid #e5e7eb;
  }

  .properties-panel {
    border-left: none;
    border-top: 1px solid #e5e7eb;
  }

  .canvas-grid {
    width: 100%;
    max-width: 800px;
    height: 400px;
  }

  .toolbar-actions {
    flex-direction: row;
    flex-wrap: wrap;
  }
}

/* 大屏幕优化 */
@media (min-width: 1920px) {
  .canvas-grid {
    width: 2000px;
    height: 1000px;
  }

  .layout-designer {
    grid-template-columns: 320px 1fr 280px;
  }
}
</style>
