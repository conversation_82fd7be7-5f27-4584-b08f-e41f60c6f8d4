/**
 * IT资产管理系统 - Pinia状态管理主文件
 * 文件路径: src/stores/index.js
 * 功能描述: 配置并导出Pinia状态管理库
 */

import { createPinia } from 'pinia'
import { markRaw } from 'vue'
import router from '@/router'
import { useUserStore } from './modules/user'
import { useAppStore } from './modules/app'
import { useGamificationStore } from './modules/gamification'
import { useQuickMemoStore } from './modules/quickMemo'
import { useSparePartsStore } from './modules/spareparts'
import { useTaskEnhancedStore } from './modules/taskEnhanced'

// 创建 pinia 实例
const pinia = createPinia()

// 为 pinia 添加 router 实例
pinia.use(({ store }) => {
  store.router = markRaw(router)
})

export default pinia

// 导出各个模块的store，方便在组件中使用
export * from './modules/user'
export * from './modules/gamification'
export * from './modules/quickMemo'
export * from './modules/spareparts'
export * from './modules/taskEnhanced'
export { useAppStore } 