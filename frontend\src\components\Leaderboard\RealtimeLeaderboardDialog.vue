<template>
  <el-dialog
    v-model="dialogVisible"
    title="🏆 实时工作排行榜"
    width="900px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    center
    class="leaderboard-dialog"
    @close="handleClose"
  >
    <!-- 顶部统计卡片 -->
    <div class="stats-cards">
      <div class="stat-card">
        <div class="stat-number">{{ totalUsers }}</div>
        <div class="stat-label">参与人数</div>
      </div>
      <div class="stat-card">
        <div class="stat-number">{{ totalTasks }}</div>
        <div class="stat-label">总任务数</div>
      </div>
      <div class="stat-card">
        <div class="stat-number">{{ totalPoints }}</div>
        <div class="stat-label">总积分</div>
      </div>
      <div class="stat-card">
        <div class="stat-number">{{ averageScore }}</div>
        <div class="stat-label">平均得分</div>
      </div>
    </div>

    <!-- 排行榜类型切换 -->
    <div class="leaderboard-controls">
      <el-radio-group v-model="currentType" @change="handleTypeChange">
        <el-radio-button label="points">积分排行</el-radio-button>
        <el-radio-button label="tasks">任务排行</el-radio-button>
        <el-radio-button label="assets">资产排行</el-radio-button>
        <el-radio-button label="procurement">采购排行</el-radio-button>
      </el-radio-group>
      
      <div class="refresh-info">
        <el-tag type="success" size="small">
          <el-icon><Refresh /></el-icon>
          实时更新
        </el-tag>
        <span class="last-update">最后更新: {{ lastUpdateTime }}</span>
      </div>
    </div>

    <!-- 排行榜表格 -->
    <div class="leaderboard-table" v-loading="loading">
      <el-table 
        :data="leaderboardData" 
        stripe 
        style="width: 100%"
        :row-class-name="getRowClassName"
        max-height="400"
      >
        <el-table-column type="index" label="排名" width="80" align="center">
          <template #default="{ $index }">
            <div class="rank-cell">
              <el-icon v-if="$index === 0" class="rank-icon gold"><Trophy /></el-icon>
              <el-icon v-else-if="$index === 1" class="rank-icon silver"><Medal /></el-icon>
              <el-icon v-else-if="$index === 2" class="rank-icon bronze"><Medal /></el-icon>
              <span v-else class="rank-number">{{ $index + 1 }}</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="姓名" min-width="120">
          <template #default="{ row }">
            <div class="user-cell">
              <el-avatar :size="32" :src="row.avatar" class="user-avatar">
                {{ row.name?.charAt(0) || 'U' }}
              </el-avatar>
              <span class="user-name">{{ row.name || '未知用户' }}</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="department" label="部门" width="100" />
        
        <el-table-column :label="getScoreLabel()" width="100" align="right">
          <template #default="{ row }">
            <el-tag :type="getScoreTagType(row.score)" size="small">
              {{ row.score || 0 }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="新建" width="80" align="center">
          <template #default="{ row }">
            <span class="score-text">{{ row.created || 0 }}</span>
          </template>
        </el-table-column>
        
        <el-table-column label="更新" width="80" align="center">
          <template #default="{ row }">
            <span class="score-text">{{ row.updated || 0 }}</span>
          </template>
        </el-table-column>
        
        <el-table-column label="创建" width="80" align="center">
          <template #default="{ row }">
            <span class="score-text">{{ row.created || 0 }}</span>
          </template>
        </el-table-column>
        
        <el-table-column label="更新" width="80" align="center">
          <template #default="{ row }">
            <span class="score-text">{{ row.updated || 0 }}</span>
          </template>
        </el-table-column>
        
        <el-table-column label="积分" width="80" align="center">
          <template #default="{ row }">
            <span class="score-text points">{{ row.points || 0 }}</span>
          </template>
        </el-table-column>
        
        <el-table-column label="评价" width="100" align="center">
          <template #default="{ row }">
            <el-rate 
              v-model="row.rating" 
              :max="5" 
              size="small" 
              disabled 
              show-score
              text-color="#ff9900"
            />
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 底部操作 -->
    <template #footer>
      <div class="dialog-footer">
        <el-checkbox v-model="dontShowAgain" size="small">
          今日不再显示
        </el-checkbox>
        <div class="footer-buttons">
          <el-button @click="handleClose">关闭</el-button>
          <el-button type="primary" @click="goToLeaderboard">查看详细排行榜</el-button>
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElNotification } from 'element-plus'
import { Trophy, Medal, Refresh } from '@element-plus/icons-vue'
import { getMultiDimensionLeaderboard } from '@/api/gamification'
import request from '@/utils/request'
import { formatDate } from '@/utils/date'

// Props & Emits
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:visible', 'close'])

// Router
const router = useRouter()

// 响应式数据
const dialogVisible = ref(false)
const loading = ref(false)
const currentType = ref('points')
const leaderboardData = ref([])
const lastUpdateTime = ref('')
const dontShowAgain = ref(false)

// 实时更新定时器
let updateTimer = null

// 监听visible变化
watch(() => props.visible, (val) => {
  dialogVisible.value = val
  if (val) {
    loadLeaderboardData()
    startRealTimeUpdate()
  } else {
    stopRealTimeUpdate()
  }
})

watch(dialogVisible, (val) => {
  emit('update:visible', val)
})

// 计算属性
const totalUsers = computed(() => leaderboardData.value.length)
const totalTasks = computed(() => leaderboardData.value.reduce((sum, user) => sum + (user.created || 0) + (user.updated || 0), 0))
const totalPoints = computed(() => leaderboardData.value.reduce((sum, user) => sum + (user.points || 0), 0))
const averageScore = computed(() => {
  if (totalUsers.value === 0) return 0
  return Math.round(totalPoints.value / totalUsers.value * 10) / 10
})

// 方法
const loadLeaderboardData = async () => {
  try {
    loading.value = true

    // 使用工作汇总API获取实时排行榜数据
    const response = await request.get('/v2/work-summary', {
      params: {
        periodType: 'weekly',
        periodDate: new Date().toISOString(),
        limit: 50
      }
    })

    if (response.success && response.data && Array.isArray(response.data)) {
      // 处理数据并按积分排序（确保排行榜按排行顺序显示）
      leaderboardData.value = response.data
        .filter(user => user.userId && user.userName) // 过滤有效用户
        .map(user => ({
          id: user.userId,
          name: user.userName || '未知用户',
          avatar: user.avatar || '',
          department: user.department || '未分配',
          score: user.totalPointsEarned || user.productivityScore || 0,
          points: user.totalPointsEarned || 0,
          created: user.tasksCreated || 0,
          updated: user.tasksCompleted || 0,
          rating: Math.min(5, Math.max(1, Math.round((user.totalPointsEarned || 0) / 20)))
        }))
        .sort((a, b) => b.score - a.score) // 按分数降序排列，确保排名正确

      lastUpdateTime.value = formatDate(new Date())
    } else {
      // 使用模拟数据
      generateMockData()
    }
  } catch (error) {
    console.error('加载排行榜数据失败:', error)
    generateMockData()
  } finally {
    loading.value = false
  }
}

const generateMockData = () => {
  const mockUsers = [
    { name: '张三', department: '生产部', score: 95, created: 12, updated: 8, points: 1900 },
    { name: '李四', department: '技术部', score: 88, created: 10, updated: 6, points: 1760 },
    { name: '王五', department: '质量部', score: 82, created: 9, updated: 7, points: 1640 },
    { name: '赵六', department: '设备部', score: 76, created: 8, updated: 5, points: 1520 },
    { name: '钱七', department: '采购部', score: 71, created: 7, updated: 4, points: 1420 }
  ]
  
  leaderboardData.value = mockUsers.map((user, index) => ({
    id: index + 1,
    name: user.name,
    avatar: '',
    department: user.department,
    score: user.score,
    points: user.points,
    created: user.created,
    updated: user.updated,
    rating: Math.min(5, Math.max(1, Math.round(user.score / 20)))
  }))
  
  lastUpdateTime.value = formatDate(new Date())
}

const startRealTimeUpdate = () => {
  // 每30秒更新一次数据
  updateTimer = setInterval(() => {
    loadLeaderboardData()
  }, 30000)
}

const stopRealTimeUpdate = () => {
  if (updateTimer) {
    clearInterval(updateTimer)
    updateTimer = null
  }
}

const handleTypeChange = () => {
  loadLeaderboardData()
}

const getScoreLabel = () => {
  const labels = {
    points: '积分',
    tasks: '任务数',
    assets: '资产数',
    procurement: '采购数'
  }
  return labels[currentType.value] || '积分'
}

const getScoreTagType = (score) => {
  if (score >= 90) return 'danger'
  if (score >= 80) return 'warning'
  if (score >= 70) return 'primary'
  return 'info'
}

const getRowClassName = ({ rowIndex }) => {
  if (rowIndex === 0) return 'first-place'
  if (rowIndex === 1) return 'second-place'
  if (rowIndex === 2) return 'third-place'
  return ''
}

const handleClose = () => {
  if (dontShowAgain.value) {
    // 保存到localStorage，今日不再显示
    const today = new Date().toDateString()
    localStorage.setItem('leaderboard-dialog-hidden', today)
  }
  
  dialogVisible.value = false
  emit('close')
}

const goToLeaderboard = () => {
  router.push('/main/gamification/leaderboard')
  handleClose()
}

// 生命周期
onMounted(() => {
  if (props.visible) {
    loadLeaderboardData()
    startRealTimeUpdate()
  }
})

onUnmounted(() => {
  stopRealTimeUpdate()
})
</script>

<style scoped>
.leaderboard-dialog {
  .el-dialog__body {
    padding: 20px;
  }
}

/* 统计卡片 */
.stats-cards {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  margin-bottom: 20px;
}

.stat-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
  border-radius: 12px;
  text-align: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-number {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  opacity: 0.9;
}

/* 控制区域 */
.leaderboard-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.refresh-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.last-update {
  font-size: 12px;
  color: #666;
}

/* 排行榜表格 */
.leaderboard-table {
  margin-bottom: 20px;
}

.rank-cell {
  display: flex;
  align-items: center;
  justify-content: center;
}

.rank-icon {
  font-size: 20px;
}

.rank-icon.gold {
  color: #ffd700;
}

.rank-icon.silver {
  color: #c0c0c0;
}

.rank-icon.bronze {
  color: #cd7f32;
}

.rank-number {
  font-weight: bold;
  font-size: 16px;
}

.user-cell {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-avatar {
  flex-shrink: 0;
}

.user-name {
  font-weight: 500;
}

.score-text {
  font-weight: 500;
}

.score-text.points {
  color: #409eff;
  font-weight: bold;
}

/* 表格行样式 */
:deep(.first-place) {
  background-color: #fff7e6 !important;
}

:deep(.second-place) {
  background-color: #f6f6f6 !important;
}

:deep(.third-place) {
  background-color: #f0f9ff !important;
}

/* 底部操作区 */
.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.footer-buttons {
  display: flex;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stats-cards {
    grid-template-columns: repeat(2, 1fr);
  }

  .leaderboard-controls {
    flex-direction: column;
    gap: 12px;
  }
}
</style>
