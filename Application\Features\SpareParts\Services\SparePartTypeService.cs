// File: Application/Features/SpareParts/Services/SparePartTypeService.cs
// Description: 备品备件类型服务实现类

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ItAssetsSystem.Application.Features.SpareParts.Dtos;
using ItAssetsSystem.Core.Abstractions;
using ItAssetsSystem.Domain.Entities;
using Microsoft.Extensions.Logging;

namespace ItAssetsSystem.Application.Features.SpareParts.Services
{
    /// <summary>
    /// 备品备件类型服务实现类
    /// </summary>
    public class SparePartTypeService : ISparePartTypeService
    {
        private readonly ISparePartTypeRepository _typeRepository;
        private readonly ICurrentUserService _currentUserService;
        private readonly ILogger<SparePartTypeService> _logger;
        
        /// <summary>
        /// 构造函数
        /// </summary>
        public SparePartTypeService(
            ISparePartTypeRepository typeRepository,
            ICurrentUserService currentUserService,
            ILogger<SparePartTypeService> logger)
        {
            _typeRepository = typeRepository;
            _currentUserService = currentUserService;
            _logger = logger;
        }
        
        /// <summary>
        /// 获取所有备品备件类型
        /// </summary>
        /// <returns>类型列表</returns>
        public async Task<List<SparePartTypeDto>> GetAllTypesAsync()
        {
            try 
            {
                var types = await _typeRepository.GetAllAsync();
                return types.Select(MapToDto).ToList();
            }
            catch (Exception ex)
            {
                // 记录详细的异常信息
                _logger.LogError(ex, "获取备品备件类型失败: {Message}", ex.Message);
                
                // 在开发环境中，我们返回一个空列表而不是抛出异常
                // 这样前端至少能得到响应，而不是500错误
                return new List<SparePartTypeDto>();
            }
        }
        
        /// <summary>
        /// 获取类型树
        /// </summary>
        /// <returns>树形结构的类型列表</returns>
        public async Task<List<SparePartTypeDto>> GetTypeTreeAsync()
        {
            try
            {
                var allTypes = await _typeRepository.GetAllAsync();
                var typeDtos = allTypes.Select(MapToDto).ToList();
                
                // 获取根节点（无父节点的类型）
                var rootTypes = typeDtos.Where(t => !t.ParentId.HasValue).ToList();
                
                // 为每个根节点构建子树
                foreach (var root in rootTypes)
                {
                    BuildTypeTree(root, typeDtos);
                }
                
                return rootTypes;
            }
            catch (Exception ex)
            {
                // 记录详细的异常信息
                _logger.LogError(ex, "获取备品备件类型树失败: {Message}", ex.Message);
                
                // 返回空列表
                return new List<SparePartTypeDto>();
            }
        }
        
        /// <summary>
        /// 递归构建类型树
        /// </summary>
        /// <param name="parent">父节点</param>
        /// <param name="allTypes">所有类型列表</param>
        private void BuildTypeTree(SparePartTypeDto parent, List<SparePartTypeDto> allTypes)
        {
            // 查找所有以当前节点为父节点的类型
            var children = allTypes.Where(t => t.ParentId.HasValue && t.ParentId.Value == parent.Id).ToList();
            
            if (children.Any())
            {
                parent.Children = children;
                
                // 递归为每个子节点构建子树
                foreach (var child in children)
                {
                    BuildTypeTree(child, allTypes);
                }
            }
        }
        
        /// <summary>
        /// 获取备品备件类型详情
        /// </summary>
        /// <param name="id">类型ID</param>
        /// <returns>类型DTO</returns>
        public async Task<SparePartTypeDto> GetTypeByIdAsync(long id)
        {
            var type = await _typeRepository.GetByIdAsync(id);
            if (type == null)
            {
                return null;
            }
            
            return MapToDto(type);
        }
        
        /// <summary>
        /// 创建备品备件类型
        /// </summary>
        /// <param name="request">创建请求</param>
        /// <returns>创建的类型DTO</returns>
        public async Task<SparePartTypeDto> CreateTypeAsync(CreateSparePartTypeRequest request)
        {
            try 
            {
                // 验证编码是否已存在
                var codeExists = await _typeRepository.IsCodeExistsAsync(request.Code);
                if (codeExists)
                {
                    throw new ArgumentException($"编码 {request.Code} 已存在");
                }
                
                // 创建类型实体
                var type = new SparePartType
                {
                    Name = request.Name,
                    Code = request.Code,
                    ParentId = request.ParentId,
                    Description = request.Description,
                    CreatedAt = DateTime.Now,
                };
                
                // 设置层级和路径
                if (request.ParentId.HasValue)
                {
                    var parent = await _typeRepository.GetByIdAsync(request.ParentId.Value);
                    if (parent == null)
                    {
                        throw new ArgumentException($"父级ID {request.ParentId} 不存在");
                    }
                    
                    // 设置层级和路径
                    type.Level = parent.Level + 1;
                    type.Path = $"{parent.Path}{parent.Id},";
                }
                else
                {
                    // 根节点
                    type.Level = 0;
                    type.Path = ",";
                }
                
                // 保存类型
                var createdType = await _typeRepository.CreateAsync(type);
                
                return MapToDto(createdType);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建备品备件类型失败: {Message}", ex.Message);
                throw;
            }
        }
        
        /// <summary>
        /// 更新备品备件类型
        /// </summary>
        /// <param name="id">类型ID</param>
        /// <param name="request">更新请求</param>
        /// <returns>更新后的类型DTO</returns>
        public async Task<SparePartTypeDto> UpdateTypeAsync(long id, CreateSparePartTypeRequest request)
        {
            try
            {
                // 验证类型是否存在
                var existingType = await _typeRepository.GetByIdAsync(id);
                if (existingType == null)
                {
                    return null;
                }
                
                // 验证编码是否已存在
                var codeExists = await _typeRepository.IsCodeExistsAsync(request.Code, id);
                if (codeExists)
                {
                    throw new ArgumentException($"编码 {request.Code} 已存在");
                }
                
                // 验证父级是否存在
                if (request.ParentId.HasValue)
                {
                    var parent = await _typeRepository.GetByIdAsync(request.ParentId.Value);
                    if (parent == null)
                    {
                        throw new ArgumentException($"父级ID {request.ParentId} 不存在");
                    }
                    
                    // 验证父级不能是自己或自己的子级
                    if (request.ParentId == id)
                    {
                        throw new ArgumentException("父级不能是自己");
                    }
                    
                    var childIds = await _typeRepository.GetChildTypeIdsAsync(id);
                    if (childIds.Contains(request.ParentId.Value))
                    {
                        throw new ArgumentException("父级不能是自己的子级");
                    }
                    
                    // 如果父级发生变化，更新层级和路径
                    if (existingType.ParentId != request.ParentId)
                    {
                        existingType.Level = parent.Level + 1;
                        existingType.Path = $"{parent.Path}{parent.Id},";
                    }
                }
                else if (existingType.ParentId.HasValue) // 如果从有父级变为无父级
                {
                    // 变为根节点
                    existingType.Level = 0;
                    existingType.Path = ",";
                }
                
                // 更新类型属性
                existingType.Name = request.Name;
                existingType.Code = request.Code;
                existingType.ParentId = request.ParentId;
                existingType.Description = request.Description;
                existingType.UpdatedAt = DateTime.Now;
                
                // 保存类型
                var updatedType = await _typeRepository.UpdateAsync(existingType);
                
                return MapToDto(updatedType);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新备品备件类型失败: {Message}", ex.Message);
                throw;
            }
        }
        
        /// <summary>
        /// 删除备品备件类型
        /// </summary>
        /// <param name="id">类型ID</param>
        /// <returns>是否成功</returns>
        public async Task<bool> DeleteTypeAsync(long id)
        {
            return await _typeRepository.DeleteAsync(id);
        }
        
        /// <summary>
        /// 获取类型的所有子类型
        /// </summary>
        /// <param name="parentId">父类型ID</param>
        /// <returns>子类型ID列表</returns>
        public async Task<List<long>> GetChildTypeIdsAsync(long parentId)
        {
            return await _typeRepository.GetChildTypeIdsAsync(parentId);
        }
        
        /// <summary>
        /// 将实体映射为DTO
        /// </summary>
        /// <param name="entity">类型实体</param>
        /// <returns>类型DTO</returns>
        private SparePartTypeDto MapToDto(SparePartType entity)
        {
            if (entity == null)
            {
                return null;
            }
            
            return new SparePartTypeDto
            {
                Id = entity.Id,
                Name = entity.Name,
                Code = entity.Code,
                ParentId = entity.ParentId,
                ParentName = entity.Parent?.Name,
                Description = entity.Description,
                Level = entity.Level,
                Path = entity.Path,
                CreatedAt = entity.CreatedAt,
                UpdatedAt = entity.UpdatedAt ?? entity.CreatedAt
            };
        }
    }
} 