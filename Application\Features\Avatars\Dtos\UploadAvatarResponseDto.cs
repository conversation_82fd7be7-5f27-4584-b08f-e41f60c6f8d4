// File: Application/Features/Avatars/Dtos/UploadAvatarResponseDto.cs
// Description: DTO for avatar upload responses

#nullable enable

namespace ItAssetsSystem.Application.Features.Avatars.Dtos
{
    /// <summary>
    /// 上传头像响应DTO
    /// </summary>
    public class UploadAvatarResponseDto
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 头像相对路径URL
        /// </summary>
        public string? AvatarUrl { get; set; }

        /// <summary>
        /// 头像完整访问URL（包含域名和路径）
        /// </summary>
        public string? AccessUrl { get; set; }
    }
} 