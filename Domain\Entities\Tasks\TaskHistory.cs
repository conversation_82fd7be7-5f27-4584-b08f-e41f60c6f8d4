// File: Domain/Entities/Tasks/TaskHistory.cs
// Description: V2 任务历史记录实体 (BIGINT PK)

#nullable enable
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using ItAssetsSystem.Models.Entities; // For User entity reference
// Add using for Comment and Attachment if they are in a different namespace and not automatically resolved.
// Assuming Comment and Attachment are in ItAssetsSystem.Domain.Entities.Tasks
using ItAssetsSystem.Domain.Entities.Tasks;

namespace ItAssetsSystem.Domain.Entities.Tasks
{
    /// <summary>
    /// 任务变更历史记录表 (V2 - BIGINT PK)
    /// </summary>
    public class TaskHistory
    {
        /// <summary>
        /// 历史记录主键ID (BIGINT)
        /// </summary>
        [Key]
        public long TaskHistoryId { get; set; }

        /// <summary>
        /// 关联的任务ID (BIGINT)
        /// </summary>
        public long TaskId { get; set; }

        /// <summary>
        /// 执行操作的用户ID (INT, 关联 users.Id, 系统操作可为NULL)
        /// </summary>
        public int? UserId { get; set; }

        /// <summary>
        /// 操作发生时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// 操作类型 (如 Create, StatusChange, AssigneeChange, CommentAdded, FieldUpdate, AttachmentAdded)
        /// </summary>
        public string ActionType { get; set; } = "";

        /// <summary>
        /// 当 ActionType 为 FieldUpdate 时，变更的字段名
        /// </summary>
        public string? FieldName { get; set; }

        /// <summary>
        /// 字段变更前的旧值 (建议JSON存储复杂类型)
        /// </summary>
        public string? OldValue { get; set; }

        /// <summary>
        /// 字段变更后的新值 (建议JSON存储复杂类型)
        /// </summary>
        public string? NewValue { get; set; }

        /// <summary>
        /// 描述性信息，例如 "用户A将状态从'待处理'更改为'进行中'"
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// 关联的评论ID (BIGINT, 可为空)
        /// </summary>
        public long? CommentId { get; set; }

        /// <summary>
        /// 关联的附件ID (BIGINT, 可为空)
        /// </summary>
        public long? AttachmentId { get; set; }

        /// <summary>
        /// 导航属性：相关任务
        /// </summary>
        [ForeignKey("TaskId")]
        public virtual Task? Task { get; set; }

        /// <summary>
        /// 导航属性：操作用户
        /// </summary>
        [ForeignKey("UserId")]
        public virtual User? User { get; set; }

        /// <summary>
        /// 导航属性：关联评论
        /// </summary>
        [ForeignKey("CommentId")]
        public virtual Comment? Comment { get; set; }

        /// <summary>
        /// 导航属性：关联附件
        /// </summary>
        [ForeignKey("AttachmentId")]
        public virtual Attachment? Attachment { get; set; }
    }
} 