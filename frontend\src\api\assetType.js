/**
 * 航空航天级IT资产管理系统 - 资产类型API
 * 文件路径: src/api/assetType.js
 * 功能描述: 提供资产类型管理相关的API服务
 */

import request from '@/utils/request'
import systemConfig from '@/config/system'

/**
 * 获取资产类型列表
 * @param {Object} params 查询参数
 * @returns {Promise} 返回资产类型列表
 */
export function getAssetTypes(params = {}) {
  // 处理参数，确保格式正确
  const queryParams = {};
  
  // 确保分页参数正确传递（避免使用params[page]这种格式）
  queryParams.page = params.page || 1;
  // 默认获取大量数据，确保能获取所有资产类型
  queryParams.pageSize = params.pageSize || 1000;
  
  // 确保isActive参数正确传递
  if (params.isActive !== undefined) {
    queryParams.isActive = params.isActive;
  }
  
  // 关键词搜索
  if (params.keyword) {
    queryParams.keyword = params.keyword;
  }
  
  console.log('获取资产类型列表 - API调用', {
    请求方法: 'GET',
    请求路径: '/AssetType',
    完整URL: `${systemConfig.apiBaseUrl}/AssetType`,
    请求参数: queryParams
  });
  
  return request.get('/AssetType', { params: queryParams })
    .then(response => {
      if (response && response.data && Array.isArray(response.data.items)) {
        console.log(`成功获取${response.data.items.length}条资产类型数据，总数: ${response.data.total}`);
        
        // 检查是否有重复ID的问题
        const uniqueItems = [];
        const idSet = new Set();
        
        // 过滤掉重复的ID
        response.data.items.forEach(item => {
          if (!idSet.has(item.id)) {
            idSet.add(item.id);
            uniqueItems.push(item);
          }
        });
        
        // 替换原始列表
        response.data.items = uniqueItems;
        
        return response;
      } else {
        console.warn('资产类型数据结构异常:', response);
        return response;
      }
    })
    .catch(error => {
      console.error('获取资产类型列表出错:', error);
      throw error;
    });
}

/**
 * 获取资产类型详情
 * @param {number} id 资产类型ID
 * @returns {Promise} 返回资产类型详情
 */
export function getAssetTypeById(id) {
  console.log('获取资产类型详情 - API调用', {
    请求方法: 'GET',
    请求路径: `/AssetType/${id}`,
    完整URL: `${systemConfig.apiBaseUrl}/AssetType/${id}`,
    资产类型ID: id
  })
  return request.get(`/AssetType/${id}`)
}

/**
 * 创建资产类型
 * @param {Object} data 资产类型数据
 * @returns {Promise} 返回创建结果
 */
export function createAssetType(data) {
  // 确保数据类型正确
  const processedData = { ...data };
  
  // 如果有ParentId，确保是数字类型
  if (processedData.parentId !== undefined && processedData.parentId !== null) {
    if (typeof processedData.parentId === 'string') {
      const numericParentId = parseInt(processedData.parentId, 10);
      if (!isNaN(numericParentId)) {
        processedData.parentId = numericParentId;
      } else if (processedData.parentId.trim() === '') {
        // 如果是空字符串，设为null
        processedData.parentId = null;
      } else {
        console.warn('无效的父类型ID:', processedData.parentId);
      }
    }
  }
  
  console.log('创建资产类型 - API调用', {
    请求方法: 'POST',
    请求路径: '/AssetType',
    完整URL: `${systemConfig.apiBaseUrl}/AssetType`,
    请求数据: processedData
  })
  return request.post('/AssetType', processedData)
}

/**
 * 更新资产类型
 * @param {number} id 资产类型ID
 * @param {Object} data 资产类型数据
 * @returns {Promise} 返回更新结果
 */
export function updateAssetType(id, data) {
  // 确保ID是数字类型
  let numericId = id;
  if (typeof id === 'string') {
    numericId = parseInt(id, 10);
    if (isNaN(numericId)) {
      console.error('资产类型ID无效:', id);
      return Promise.reject(new Error('资产类型ID无效'));
    }
  }

  // 确保data.id也是数字类型
  if (data && data.id && typeof data.id === 'string') {
    data.id = parseInt(data.id, 10);
    if (isNaN(data.id)) {
      console.error('资产类型data.id无效:', data.id);
      return Promise.reject(new Error('资产类型data.id无效'));
    }
  }
  
  console.log('更新资产类型 - API调用', {
    请求方法: 'PUT',
    请求路径: `/AssetType/${numericId}`,
    完整URL: `${systemConfig.apiBaseUrl}/AssetType/${numericId}`,
    资产类型ID: numericId,
    请求数据: data
  })
  
  return request.put(`/AssetType/${numericId}`, data)
}

/**
 * 删除资产类型
 * @param {number} id 资产类型ID
 * @returns {Promise} 返回删除结果
 */
export function deleteAssetType(id) {
  console.log('删除资产类型 - API调用', {
    请求方法: 'DELETE',
    请求路径: `/AssetType/${id}`,
    完整URL: `${systemConfig.apiBaseUrl}/AssetType/${id}`,
    资产类型ID: id
  })
  return request.delete(`/AssetType/${id}`)
}

/**
 * 获取资产类型的规格型号列表
 * @param {number} assetTypeId 资产类型ID
 * @returns {Promise} 返回规格型号列表
 */
export function getAssetTypeSpecs(assetTypeId) {
  console.log('获取资产类型的规格型号列表 - API调用', {
    请求方法: 'GET',
    请求路径: `/AssetType/${assetTypeId}/specifications`,
    完整URL: `${systemConfig.apiBaseUrl}/AssetType/${assetTypeId}/specifications`,
    资产类型ID: assetTypeId
  })
  return request.get(`/AssetType/${assetTypeId}/specifications`)
}

/**
 * 更新资产类型的规格型号列表
 * @param {number} assetTypeId 资产类型ID
 * @param {Object} data 规格型号数据
 * @returns {Promise} 返回更新结果
 */
export function updateAssetTypeSpecs(assetTypeId, data) {
  console.log('更新资产类型的规格型号列表 - API调用', {
    请求方法: 'PUT',
    请求路径: `/AssetType/${assetTypeId}/specifications`,
    完整URL: `${systemConfig.apiBaseUrl}/AssetType/${assetTypeId}/specifications`,
    资产类型ID: assetTypeId,
    请求数据: data
  })
  return request.put(`/AssetType/${assetTypeId}/specifications`, data)
}

/**
 * 导出资产类型
 * @param {Object} params 查询参数
 * @returns {Promise} 返回导出的文件流
 */
export function exportAssetTypes(params) {
  console.log('导出资产类型 - API调用', {
    请求方法: 'GET',
    请求路径: '/AssetType/export',
    完整URL: `${systemConfig.apiBaseUrl}/AssetType/export`,
    请求参数: params
  })
  return request.get('/AssetType/export', { 
    params, 
    responseType: 'blob',
    headers: {
      'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel,text/csv',
    }
  })
  .then(response => {
    // 检查响应类型是否正确
    console.log('导出资产类型响应:', response);
    return response;
  })
  .catch(error => {
    console.error('导出资产类型失败:', error);
    throw error;
  });
}

/**
 * 获取资产类型导入模板
 * @returns {Promise} 返回导入模板文件
 */
export function getAssetTypeImportTemplate() {
  console.log('获取资产类型导入模板 - API调用', {
    请求方法: 'GET',
    请求路径: '/Import/template?entityType=AssetTypes&format=excel',
    完整URL: `${systemConfig.apiBaseUrl}/Import/template?entityType=AssetTypes&format=excel`
  })
  return request.get('/Import/template', { 
    params: { entityType: 'AssetTypes', format: 'excel' },
    responseType: 'blob'
  })
}

/**
 * 导入资产类型数据
 * @param {FormData} formData 包含导入文件的表单数据
 * @returns {Promise} 返回导入结果
 */
export function importAssetTypes(formData) {
  console.log('导入资产类型数据 - API调用', {
    请求方法: 'POST',
    请求路径: '/Import/data?entityType=AssetTypes',
    完整URL: `${systemConfig.apiBaseUrl}/Import/data?entityType=AssetTypes`
  })
  return request.post('/Import/data', formData, {
    params: { entityType: 'AssetTypes' },
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 切换资产类型激活状态
 * @param {number} id 资产类型ID
 * @returns {Promise} 返回操作结果
 */
export function toggleAssetTypeActive(id) {
  // 确保ID是数字类型
  let numericId = id;
  if (typeof id === 'string') {
    numericId = parseInt(id, 10);
    if (isNaN(numericId)) {
      console.error('资产类型ID无效:', id);
      return Promise.reject(new Error('资产类型ID无效'));
    }
  }
  
  console.log('切换资产类型激活状态 - API调用', {
    请求方法: 'PUT',
    请求路径: `/AssetType/${numericId}/toggle-active`,
    完整URL: `${systemConfig.apiBaseUrl}/AssetType/${numericId}/toggle-active`,
    资产类型ID: numericId
  })
  
  return request.put(`/AssetType/${numericId}/toggle-active`)
}

export default {
  getAssetTypes,
  getAssetTypeById,
  createAssetType,
  updateAssetType,
  deleteAssetType,
  getAssetTypeSpecs,
  updateAssetTypeSpecs,
  exportAssetTypes,
  getAssetTypeImportTemplate,
  importAssetTypes,
  toggleAssetTypeActive
} 