import{_ as e,r as a,c as l,z as s,b as t,d as r,e as n,w as i,E as d,a as u,u as c,m as o,o as v,l as m,k as p,$ as f,f as h,br as _,p as S,t as E,a3 as b,bw as C,F as T,h as A,bH as y,bI as g}from"./index-CG5lHOPO.js";import{u as w}from"./gamification-Dm7mCEPf.js";import{L as N,a as D,S as k}from"./gamification-2FBMrBgR.js";import{f as I,z as O}from"./zh-CN-B1csyosV.js";import"./en-US-BvtvdVHO.js";const U={class:"leaderboard-view page-container"},R={class:"card-header"},P={class:"header-controls"},M={key:0,class:"top-users"},L={class:"top-user second-place"},z={class:"user-info"},x={class:"user-name"},K={class:"user-score"},V={class:"user-department"},j={class:"top-user first-place"},F={class:"crown"},$={class:"user-info"},H={class:"user-name"},W={class:"user-score"},Y={class:"user-department"},q={class:"top-user third-place"},B={class:"user-info"},G={class:"user-name"},J={class:"user-score"},Q={class:"user-department"},X={class:"user-cell"},Z={style:{"font-weight":"600"}},ee={class:"score-with-icon"},ae={class:"score-with-icon"},le={class:"card-header"},se={key:0,class:"achievement-list thin-scrollbar"},te={class:"achievement-info"},re={class:"achievement-user"},ne={class:"achievement-title"},ie={class:"achievement-time"},de={key:2,class:"card-footer"},ue={class:"card-header"},ce={key:0,class:"star-of-month"},oe={class:"star-info"},ve={class:"star-stats"},me={class:"stat-item"},pe={class:"stat-value"},fe={class:"stat-item"},he={class:"stat-value"},_e={class:"stat-item"},Se={class:"stat-value"},Ee="https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png",be=e({__name:"LeaderboardView",setup(e){const be=c(),Ce=w(),Te=a(!1),Ae=a(!1),ye=a(!1),ge=a(N.POINTS),we=a(k.WEEKLY),Ne=a([]),De=a([]),ke=l((()=>Ce.achievements.slice(0,5))),Ie=l((()=>Ce.achievements)),Oe=a(null),Ue=a(!1),Re=l((()=>{const e=De.value.find((e=>e.code===ge.value));return e?e.name:"排行榜"})),Pe=l((()=>({[N.POINTS]:"积分",[N.COINS]:"金币",[N.DIAMONDS]:"钻石",[N.TASKS_CREATED]:"创建任务",[N.TASKS_COMPLETED]:"完成任务",[N.TASKS_CLAIMED]:"领取任务",[N.FAULTS_RECORDED]:"故障登记",[N.MAINTENANCE_CREATED]:"维修单",[N.ASSETS_UPDATED]:"资产更新"}[ge.value]||"分数"))),Me=l((()=>ge.value===N.POINTS)),Le=l((()=>ge.value===N.POINTS||ge.value===N.COINS)),ze=l((()=>ge.value===N.POINTS||ge.value===N.DIAMONDS)),xe=e=>{const a={[N.POINTS]:"🏆",[N.COINS]:"💰",[N.DIAMONDS]:"💎",[N.TASKS_CREATED]:"📝",[N.TASKS_COMPLETED]:"✅",[N.TASKS_CLAIMED]:"👋",[N.FAULTS_RECORDED]:"🔧",[N.MAINTENANCE_CREATED]:"🛠️",[N.ASSETS_UPDATED]:"📦"},l={[N.POINTS]:e.points||0,[N.COINS]:e.coins||0,[N.DIAMONDS]:e.diamonds||0,[N.TASKS_CREATED]:e.tasksCreated||0,[N.TASKS_COMPLETED]:e.tasksCompleted||0,[N.TASKS_CLAIMED]:e.tasksClaimed||0,[N.FAULTS_RECORDED]:e.faultsRecorded||0,[N.MAINTENANCE_CREATED]:e.maintenanceCreated||0,[N.ASSETS_UPDATED]:e.assetsUpdated||0};return`${a[ge.value]||"📊"} ${l[ge.value]||0}`};async function Ke(){Te.value=!0;try{const e=await D(ge.value,we.value,20);e.success&&e.data?Ne.value=e.data.map(((e,a)=>({userId:e.userId,userName:e.userName,avatarUrl:e.avatarUrl,department:e.department,points:e.points||0,coins:e.coins||0,diamonds:e.diamonds||0,tasksCreated:e.tasksCreated||0,tasksCompleted:e.tasksCompleted||0,tasksClaimed:e.tasksClaimed||0,faultsRecorded:e.faultsRecorded||0,maintenanceCreated:e.maintenanceCreated||0,assetsUpdated:e.assetsUpdated||0,rank:a+1}))):(Ne.value=[],d.warning("暂无排行榜数据"))}catch(e){d.error("获取排行榜数据失败"),Ne.value=[]}finally{Te.value=!1}}const Ve=()=>{Ke()};const je=e=>e+4,Fe=e=>{if(!e)return"";try{const a=new Date(e);return isNaN(a.getTime())?"无效日期":I(a,{addSuffix:!0,locale:O})}catch(a){return"无效日期"}},$e=e=>{if(!e||"string"!=typeof e)return"?";const a=e.split(" ");let l=a[0].substring(0,1).toUpperCase();return a.length>1&&(l+=a[a.length-1].substring(0,1).toUpperCase()),l},He=e=>{e.target.style.display="none"};return s((async()=>{await async function(){try{De.value=[{code:N.POINTS,name:"积分排行",icon:"🏆",description:"根据总积分排名"},{code:N.COINS,name:"金币排行",icon:"💰",description:"根据金币数量排名"},{code:N.DIAMONDS,name:"钻石排行",icon:"💎",description:"根据钻石数量排名"},{code:N.TASKS_COMPLETED,name:"任务完成排行",icon:"✅",description:"根据完成任务数量排名"},{code:N.TASKS_CREATED,name:"任务创建排行",icon:"📝",description:"根据创建任务数量排名"},{code:N.FAULTS_RECORDED,name:"故障登记排行",icon:"🔧",description:"根据登记故障数量排名"},{code:N.MAINTENANCE_CREATED,name:"维修单排行",icon:"🛠️",description:"根据创建维修单数量排名"},{code:N.ASSETS_UPDATED,name:"资产更新排行",icon:"📦",description:"根据更新资产数量排名"}]}catch(e){}}(),await Ke(),await async function(){Ae.value=!0;try{await Ce.fetchAchievements()}catch(e){d.error("获取成就数据失败")}finally{Ae.value=!1}}(),await async function(){ye.value=!0;try{await new Promise((e=>setTimeout(e,400))),Oe.value={id:"user1",name:"张三",avatar:"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png",department:"研发部",taskCount:15,monthlyRankScore:890,achievementCount:3}}catch(e){d.error("获取本月之星数据失败"),Oe.value=null}finally{ye.value=!1}}()})),(e,a)=>{const l=u("el-icon"),s=u("el-option"),d=u("el-select"),c=u("el-radio-button"),w=u("el-radio-group"),N=u("el-avatar"),D=u("el-empty"),k=u("el-table-column"),I=u("el-button"),O=u("el-table"),Ce=u("el-card"),We=u("el-col"),Ye=u("el-row"),qe=u("el-dialog"),Be=o("loading");return v(),t("div",U,[a[21]||(a[21]=r("h2",{class:"page-title"},"游戏化中心 - 多维度排行榜",-1)),n(Ye,{gutter:20},{default:i((()=>[n(We,{xs:24,md:16},{default:i((()=>[m((v(),p(Ce,{shadow:"never",class:"leaderboard-card"},{header:i((()=>[r("div",R,[r("span",null,[n(l,null,{default:i((()=>[n(h(C))])),_:1}),S(" "+E(Re.value),1)]),r("div",P,[n(d,{modelValue:ge.value,"onUpdate:modelValue":a[0]||(a[0]=e=>ge.value=e),size:"small",onChange:Ve,style:{width:"140px","margin-right":"10px"}},{default:i((()=>[(v(!0),t(T,null,A(De.value,(e=>(v(),p(s,{key:e.code,label:e.name,value:e.code},{default:i((()=>[r("span",null,E(e.icon)+" "+E(e.name),1)])),_:2},1032,["label","value"])))),128))])),_:1},8,["modelValue"]),n(w,{modelValue:we.value,"onUpdate:modelValue":a[1]||(a[1]=e=>we.value=e),size:"small",onChange:Ke},{default:i((()=>[n(c,{label:"weekly"},{default:i((()=>a[5]||(a[5]=[S("本周")]))),_:1}),n(c,{label:"monthly"},{default:i((()=>a[6]||(a[6]=[S("本月")]))),_:1}),n(c,{label:"yearly"},{default:i((()=>a[7]||(a[7]=[S("本年")]))),_:1}),n(c,{label:"alltime"},{default:i((()=>a[8]||(a[8]=[S("总榜")]))),_:1})])),_:1},8,["modelValue"])])])])),default:i((()=>[Ne.value.length>=3?(v(),t("div",M,[r("div",L,[a[9]||(a[9]=r("div",{class:"rank-badge"},"2",-1)),n(N,{size:70,src:h(_)(Ne.value[1].avatarUrl)||Ee,class:"user-avatar",onError:He},{default:i((()=>[S(E($e(Ne.value[1].userName)),1)])),_:1},8,["src"]),r("div",z,[r("div",x,E(Ne.value[1].userName),1),r("div",K,E(xe(Ne.value[1])),1),r("div",V,E(Ne.value[1].department||"未分配"),1)])]),r("div",j,[r("div",F,[n(l,null,{default:i((()=>[n(h(b))])),_:1})]),a[10]||(a[10]=r("div",{class:"rank-badge"},"1",-1)),n(N,{size:90,src:h(_)(Ne.value[0].avatarUrl)||Ee,class:"user-avatar",onError:He},{default:i((()=>[S(E($e(Ne.value[0].userName)),1)])),_:1},8,["src"]),r("div",$,[r("div",H,E(Ne.value[0].userName),1),r("div",W,E(xe(Ne.value[0])),1),r("div",Y,E(Ne.value[0].department||"未分配"),1)])]),r("div",q,[a[11]||(a[11]=r("div",{class:"rank-badge"},"3",-1)),n(N,{size:70,src:h(_)(Ne.value[2].avatarUrl)||Ee,class:"user-avatar",onError:He},{default:i((()=>[S(E($e(Ne.value[2].userName)),1)])),_:1},8,["src"]),r("div",B,[r("div",G,E(Ne.value[2].userName),1),r("div",J,E(xe(Ne.value[2])),1),r("div",Q,E(Ne.value[2].department||"未分配"),1)])])])):!Te.value&&Ne.value.length<3?(v(),p(D,{key:1,description:"暂无足够数据生成Top 3排行"})):f("",!0),Ne.value.length>3?(v(),p(O,{key:2,data:Ne.value.slice(3),stripe:"",style:{width:"100%"},class:"leaderboard-table"},{default:i((()=>[n(k,{type:"index",index:je,width:"70",label:"排名",align:"center"}),n(k,{label:"用户","min-width":"150"},{default:i((({row:e})=>[r("div",X,[n(N,{size:30,src:h(_)(e.avatarUrl)||Ee,class:"table-avatar",onError:He},{default:i((()=>[S(E($e(e.userName)),1)])),_:2},1032,["src"]),r("span",null,E(e.userName),1)])])),_:1}),n(k,{prop:"department",label:"部门","min-width":"100"}),n(k,{label:Pe.value,sortable:"","min-width":"120",align:"right"},{default:i((({row:e})=>[r("span",Z,E(xe(e)),1)])),_:1},8,["label"]),Me.value?(v(),p(k,{key:0,label:"完成任务",sortable:"","min-width":"100",align:"center"},{default:i((({row:e})=>[r("span",null,E(e.tasksCompleted||0),1)])),_:1})):f("",!0),Le.value?(v(),p(k,{key:1,label:"金币",sortable:"","min-width":"100",align:"center"},{default:i((({row:e})=>[r("div",ee,[r("span",null,"💰 "+E(e.coins||0),1)])])),_:1})):f("",!0),ze.value?(v(),p(k,{key:2,label:"钻石",sortable:"","min-width":"100",align:"center"},{default:i((({row:e})=>[r("div",ae,[r("span",null,"💎 "+E(e.diamonds||0),1)])])),_:1})):f("",!0),n(k,{label:"操作",width:"100",align:"center"},{default:i((({row:e})=>[n(I,{link:"",type:"primary",size:"small",onClick:a=>{var l;(l=e.userId)&&be.push({name:"UserProfile",params:{id:l}}).catch((e=>{}))}},{default:i((()=>a[12]||(a[12]=[S("查看")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"])):f("",!0),Te.value||0!==Ne.value.length?f("",!0):(v(),p(D,{key:3,description:"暂无排行数据"}))])),_:1})),[[Be,Te.value]])])),_:1}),n(We,{xs:24,md:8},{default:i((()=>[m((v(),p(Ce,{shadow:"never",class:"achievements-card mb-4"},{header:i((()=>[r("div",le,[r("span",null,[n(l,null,{default:i((()=>[n(h(y))])),_:1}),a[13]||(a[13]=S(" 最新成就"))])])])),default:i((()=>[ke.value.length>0?(v(),t("div",se,[(v(!0),t(T,null,A(ke.value,((e,l)=>{var s,d;return v(),t("div",{key:l,class:"achievement-item"},[n(N,{size:36,src:h(_)(null==(s=e.user)?void 0:s.avatar)||Ee,class:"achievement-avatar",onError:He},{default:i((()=>{var a;return[S(E($e(null==(a=e.user)?void 0:a.name)),1)]})),_:2},1032,["src"]),r("div",te,[r("span",re,E((null==(d=e.user)?void 0:d.name)||"未知用户"),1),r("span",ne,[a[14]||(a[14]=S("获得了成就: ")),r("strong",null,E(e.title),1)]),r("div",ie,E(Fe(e.time)),1)])])})),128))])):(v(),p(D,{key:1,description:"暂无最新成就记录"})),Ie.value.length>0?(v(),t("div",de,[n(I,{type:"primary",plain:"",size:"small",onClick:a[2]||(a[2]=e=>Ue.value=!0)},{default:i((()=>a[15]||(a[15]=[S(" 查看所有成就 ")]))),_:1})])):f("",!0)])),_:1})),[[Be,Ae.value]]),m((v(),p(Ce,{shadow:"never",class:"star-card"},{header:i((()=>[r("div",ue,[r("span",null,[n(l,null,{default:i((()=>[n(h(g))])),_:1}),a[16]||(a[16]=S(" 本月之星"))])])])),default:i((()=>[Oe.value?(v(),t("div",ce,[n(N,{size:80,src:h(_)(Oe.value.avatar)||Ee,class:"star-avatar",onError:He},{default:i((()=>[S(E($e(Oe.value.name)),1)])),_:1},8,["src"]),r("div",oe,[r("h3",null,E(Oe.value.name),1),r("p",null,E(Oe.value.department||"-"),1),r("div",ve,[r("div",me,[r("span",pe,E(Oe.value.taskCount||0),1),a[17]||(a[17]=r("span",{class:"stat-label"},"完成任务",-1))]),r("div",fe,[r("span",he,E(Oe.value.monthlyRankScore||0),1),a[18]||(a[18]=r("span",{class:"stat-label"},"本月积分",-1))]),r("div",_e,[r("span",Se,E(Oe.value.achievementCount||0),1),a[19]||(a[19]=r("span",{class:"stat-label"},"获得成就",-1))])])])])):(v(),p(D,{key:1,description:"暂无本月之星数据"}))])),_:1})),[[Be,ye.value]])])),_:1})])),_:1}),n(qe,{title:"所有成就记录",modelValue:Ue.value,"onUpdate:modelValue":a[4]||(a[4]=e=>Ue.value=e),width:"clamp(500px, 60%, 800px)",top:"8vh","append-to-body":"",draggable:""},{footer:i((()=>[n(I,{onClick:a[3]||(a[3]=e=>Ue.value=!1)},{default:i((()=>a[20]||(a[20]=[S("关闭")]))),_:1})])),default:i((()=>[m((v(),p(O,{data:Ie.value,style:{width:"100%"},height:"450px"},{default:i((()=>[n(k,{prop:"user.name",label:"用户",width:"120"}),n(k,{prop:"title",label:"成就名称","min-width":"150"}),n(k,{prop:"time",label:"获得时间",width:"160"},{default:i((({row:e})=>[S(E(Fe(e.time)),1)])),_:1})])),_:1},8,["data"])),[[Be,Ae.value]])])),_:1},8,["modelValue"])])}}},[["__scopeId","data-v-1524e067"]]);export{be as default};
