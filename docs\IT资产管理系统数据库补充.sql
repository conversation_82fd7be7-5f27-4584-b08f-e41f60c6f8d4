-- IT资产管理系统数据库补充脚本 - 第1部分：表结构创建和修改

-- ===================================================
-- 创建新表
-- ===================================================

-- 1. 任务历史表 (task_history)
CREATE TABLE IF NOT EXISTS `task_history` (
  `id` int NOT NULL AUTO_INCREMENT,
  `task_id` int NOT NULL,
  `user_id` int NOT NULL,
  `action` varchar(255) NOT NULL COMMENT '记录完整操作语义',
  `snapshot` json DEFAULT NULL COMMENT '操作时的任务数据快照',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  <PERSON>EY `idx_task_history_task_id` (`task_id`),
  KEY `idx_task_history_user_id` (`user_id`),
  CONSTRAINT `fk_task_history_task` FOREIGN KEY (`task_id`) REFERENCES `tasks` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_task_history_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- 2. 用户积分表 (user_points)
CREATE TABLE IF NOT EXISTS `user_points` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `points` int NOT NULL DEFAULT '0',
  `daily_points` int NOT NULL DEFAULT '0' COMMENT '当日获得积分',
  `weekly_points` int NOT NULL DEFAULT '0' COMMENT '本周获得积分',
  `monthly_points` int NOT NULL DEFAULT '0' COMMENT '本月获得积分',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_user_points_user_id` (`user_id`),
  CONSTRAINT `fk_user_points_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- 3. 积分历史表 (point_history)
CREATE TABLE IF NOT EXISTS `point_history` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `action_type` varchar(50) NOT NULL COMMENT '如：新建任务/更新进度/完成任务',
  `points` int NOT NULL COMMENT '获得或扣除的积分',
  `reference_id` int DEFAULT NULL COMMENT '关联ID(如任务ID)',
  `reference_type` varchar(50) DEFAULT NULL COMMENT '关联类型(如task)',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_point_history_user_id` (`user_id`),
  KEY `idx_point_history_created_at` (`created_at`),
  CONSTRAINT `fk_point_history_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- 4. 积分排行榜表 (point_leaderboard)
CREATE TABLE IF NOT EXISTS `point_leaderboard` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `points` int NOT NULL DEFAULT '0',
  `ranking` int NOT NULL,
  `leaderboard_date` date NOT NULL,
  `leaderboard_type` enum('daily','weekly','monthly','total') NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_leaderboard_unique` (`user_id`,`leaderboard_date`,`leaderboard_type`),
  KEY `idx_leaderboard_date_type` (`leaderboard_date`,`leaderboard_type`),
  CONSTRAINT `fk_point_leaderboard_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- 5. 系统通知表 (notifications)
CREATE TABLE IF NOT EXISTS `notifications` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `title` varchar(255) NOT NULL,
  `content` text NOT NULL,
  `type` varchar(50) NOT NULL COMMENT '通知类型：task/system/alert',
  `reference_id` int DEFAULT NULL COMMENT '关联ID',
  `reference_type` varchar(50) DEFAULT NULL COMMENT '关联类型',
  `is_read` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `read_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_notifications_user_id` (`user_id`),
  KEY `idx_notifications_created_at` (`created_at`),
  CONSTRAINT `fk_notifications_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- 6. 中控台配置表 (dashboard_config)
CREATE TABLE IF NOT EXISTS `dashboard_config` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `theme` enum('light','dark') NOT NULL DEFAULT 'dark',
  `layout` json DEFAULT NULL COMMENT '面板布局配置',
  `auto_refresh` int DEFAULT '60' COMMENT '刷新间隔(秒)',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_dashboard_user` (`user_id`),
  CONSTRAINT `fk_dashboard_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- 7. 晨间战报表 (daily_reports)
CREATE TABLE IF NOT EXISTS `daily_reports` (
  `id` int NOT NULL AUTO_INCREMENT,
  `report_date` date NOT NULL,
  `report_type` enum('daily','weekly','monthly') NOT NULL DEFAULT 'daily',
  `file_path` varchar(255) DEFAULT NULL COMMENT '报表文件路径',
  `snapshot_data` json DEFAULT NULL COMMENT '数据快照',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_report_date_type` (`report_date`,`report_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- 8. 用户行为日志表 (user_actions)
CREATE TABLE IF NOT EXISTS `user_actions` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `action` varchar(50) NOT NULL COMMENT '行为类型',
  `reference_id` int DEFAULT NULL COMMENT '关联ID',
  `reference_type` varchar(50) DEFAULT NULL COMMENT '关联类型',
  `points` int DEFAULT '0' COMMENT '获得积分',
  `action_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_actions_user` (`user_id`),
  KEY `idx_user_actions_time` (`action_time`),
  CONSTRAINT `fk_user_actions_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- 9. 库存安全线配置表 (inventory_thresholds)
CREATE TABLE IF NOT EXISTS `inventory_thresholds` (
  `id` int NOT NULL AUTO_INCREMENT,
  `asset_type_id` int NOT NULL,
  `min_threshold` int NOT NULL DEFAULT '5' COMMENT '最小安全库存',
  `warning_threshold` int NOT NULL DEFAULT '10' COMMENT '预警库存',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_inventory_asset_type` (`asset_type_id`),
  CONSTRAINT `fk_inventory_asset_type` FOREIGN KEY (`asset_type_id`) REFERENCES `assettypes` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ===================================================
-- 修改现有表结构
-- ===================================================

-- 1. 资产表 (assets) 增强
-- 检查asset_code_prefix列是否存在
SET @column_exists = (
  SELECT COUNT(1) FROM information_schema.columns 
  WHERE table_schema = DATABASE() 
  AND table_name = 'assets' 
  AND column_name = 'asset_code_prefix'
);

-- 如果列不存在，则添加它
SET @sql = IF(@column_exists = 0, 
    'ALTER TABLE `assets` ADD COLUMN `asset_code_prefix` varchar(20) DEFAULT "IT" COMMENT "资产编号前缀"', 
    'SELECT "Column already exists"');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 为其他列和表重复相同的模式
SET @column_exists = (
  SELECT COUNT(1) FROM information_schema.columns 
  WHERE table_schema = DATABASE() 
  AND table_name = 'assets' 
  AND column_name = 'inventory_id'
);

SET @sql = IF(@column_exists = 0, 
    'ALTER TABLE `assets` ADD COLUMN `inventory_id` int DEFAULT NULL COMMENT "来源库存ID"', 
    'SELECT "Column already exists"');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 修改assetCode列
ALTER TABLE `assets` MODIFY COLUMN `assetCode` varchar(30) NOT NULL COMMENT '资产编号(IT-类型-年月日-序号)';

-- 2. 位置表 (locations) 增强
-- 检查level列是否存在
SET @column_exists = (
  SELECT COUNT(1) FROM information_schema.columns 
  WHERE table_schema = DATABASE() 
  AND table_name = 'locations' 
  AND column_name = 'level'
);

SET @sql = IF(@column_exists = 0, 
    'ALTER TABLE `locations` ADD COLUMN `level` tinyint NOT NULL DEFAULT 3 COMMENT "位置级别(1-5)"', 
    'SELECT "Column already exists"');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查索引是否存在
SET @exist_idx = (
  SELECT COUNT(1) FROM information_schema.statistics 
  WHERE table_schema = DATABASE() 
  AND table_name = 'locations' 
  AND index_name = 'idx_locations_level'
);

SET @sql = IF(@exist_idx = 0, 
    'ALTER TABLE `locations` ADD INDEX `idx_locations_level` (`level`)', 
    'SELECT "Index already exists"');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 3. 采购订单表 (purchaseorders) 增强
ALTER TABLE `purchaseorders` MODIFY COLUMN `OrderCode` varchar(30) NOT NULL COMMENT '采购单号(PO-年月日-时分秒)';

-- 4. 故障记录表 (faultrecords) 增强 
-- 注意：确认列名是否是faultNumber或FaultNumber
-- 检查faultNumber列是否存在
SET @column_exists = (
  SELECT COUNT(1) FROM information_schema.columns 
  WHERE table_schema = DATABASE() 
  AND table_name = 'faultrecords' 
  AND column_name = 'faultNumber'
);

SET @sql = IF(@column_exists = 0, 
    'ALTER TABLE `faultrecords` ADD COLUMN `faultNumber` varchar(30) DEFAULT NULL COMMENT "故障单号(FIX-年月日-序号)"', 
    'SELECT "Column already exists"');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查expected_return_date列是否存在
SET @column_exists = (
  SELECT COUNT(1) FROM information_schema.columns 
  WHERE table_schema = DATABASE() 
  AND table_name = 'faultrecords' 
  AND column_name = 'expected_return_date'
);

SET @sql = IF(@column_exists = 0, 
    'ALTER TABLE `faultrecords` ADD COLUMN `expected_return_date` datetime DEFAULT NULL COMMENT "预期返厂日期"', 
    'SELECT "Column already exists"');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查warning_flag列是否存在
SET @column_exists = (
  SELECT COUNT(1) FROM information_schema.columns 
  WHERE table_schema = DATABASE() 
  AND table_name = 'faultrecords' 
  AND column_name = 'warning_flag'
);

SET @sql = IF(@column_exists = 0, 
    'ALTER TABLE `faultrecords` ADD COLUMN `warning_flag` tinyint(1) DEFAULT 0 COMMENT "超期预警标记"', 
    'SELECT "Column already exists"');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查索引是否存在
SET @exist_idx = (
  SELECT COUNT(1) FROM information_schema.statistics 
  WHERE table_schema = DATABASE() 
  AND table_name = 'faultrecords' 
  AND index_name = 'idx_faultrecords_faultNumber'
);

SET @sql = IF(@exist_idx = 0, 
    'ALTER TABLE `faultrecords` ADD INDEX `idx_faultrecords_faultNumber` (`faultNumber`)', 
    'SELECT "Index already exists"');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 5. 任务表 (tasks) 增强
-- 检查progress列是否存在
SET @column_exists = (
  SELECT COUNT(1) FROM information_schema.columns 
  WHERE table_schema = DATABASE() 
  AND table_name = 'tasks' 
  AND column_name = 'progress'
);

SET @sql = IF(@column_exists = 0, 
    'ALTER TABLE `tasks` ADD COLUMN `progress` int DEFAULT 0 COMMENT "进度百分比(0-100)"', 
    'SELECT "Column already exists"');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查points列是否存在
SET @column_exists = (
  SELECT COUNT(1) FROM information_schema.columns 
  WHERE table_schema = DATABASE() 
  AND table_name = 'tasks' 
  AND column_name = 'points'
);

SET @sql = IF(@column_exists = 0, 
    'ALTER TABLE `tasks` ADD COLUMN `points` int DEFAULT 5 COMMENT "任务积分值"', 
    'SELECT "Column already exists"');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查claimed_by_id列是否存在
SET @column_exists = (
  SELECT COUNT(1) FROM information_schema.columns 
  WHERE table_schema = DATABASE() 
  AND table_name = 'tasks' 
  AND column_name = 'claimed_by_id'
);

SET @sql = IF(@column_exists = 0, 
    'ALTER TABLE `tasks` ADD COLUMN `claimed_by_id` int DEFAULT NULL COMMENT "认领人ID"', 
    'SELECT "Column already exists"');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查外键约束是否存在
SET @exist_fk = (
  SELECT COUNT(1) FROM information_schema.TABLE_CONSTRAINTS
  WHERE table_schema = DATABASE()
  AND table_name = 'tasks'
  AND constraint_name = 'fk_task_claimed_by'
);

SET @sql = IF(@exist_fk = 0, 
    'ALTER TABLE `tasks` ADD CONSTRAINT `fk_task_claimed_by` FOREIGN KEY (`claimed_by_id`) REFERENCES `users` (`id`)', 
    'SELECT "Foreign key already exists"');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 6. 库存安全线配置表 (inventory_thresholds)
-- 不需要修改，因为库存安全线配置表的结构是正确的

-- ===================================================
-- 修改现有表结构
-- ===================================================

-- 1. 资产表 (assets) 增强
-- 检查asset_code_prefix列是否存在
SET @column_exists = (
  SELECT COUNT(1) FROM information_schema.columns 
  WHERE table_schema = DATABASE() 
  AND table_name = 'assets' 
  AND column_name = 'asset_code_prefix'
);

-- 如果列不存在，则添加它
SET @sql = IF(@column_exists = 0, 
    'ALTER TABLE `assets` ADD COLUMN `asset_code_prefix` varchar(20) DEFAULT "IT" COMMENT "资产编号前缀"', 
    'SELECT "Column already exists"');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 为其他列和表重复相同的模式
SET @column_exists = (
  SELECT COUNT(1) FROM information_schema.columns 
  WHERE table_schema = DATABASE() 
  AND table_name = 'assets' 
  AND column_name = 'inventory_id'
);

SET @sql = IF(@column_exists = 0, 
    'ALTER TABLE `assets` ADD COLUMN `inventory_id` int DEFAULT NULL COMMENT "来源库存ID"', 
    'SELECT "Column already exists"');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 修改assetCode列
ALTER TABLE `assets` MODIFY COLUMN `assetCode` varchar(30) NOT NULL COMMENT '资产编号(IT-类型-年月日-序号)';

-- 2. 位置表 (locations) 增强
-- 检查level列是否存在
SET @column_exists = (
  SELECT COUNT(1) FROM information_schema.columns 
  WHERE table_schema = DATABASE() 
  AND table_name = 'locations' 
  AND column_name = 'level'
);

SET @sql = IF(@column_exists = 0, 
    'ALTER TABLE `locations` ADD COLUMN `level` tinyint NOT NULL DEFAULT 3 COMMENT "位置级别(1-5)"', 
    'SELECT "Column already exists"');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查索引是否存在
SET @exist_idx = (
  SELECT COUNT(1) FROM information_schema.statistics 
  WHERE table_schema = DATABASE() 
  AND table_name = 'locations' 
  AND index_name = 'idx_locations_level'
);

SET @sql = IF(@exist_idx = 0, 
    'ALTER TABLE `locations` ADD INDEX `idx_locations_level` (`level`)', 
    'SELECT "Index already exists"');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 3. 采购订单表 (purchaseorders) 增强
ALTER TABLE `purchaseorders` MODIFY COLUMN `OrderCode` varchar(30) NOT NULL COMMENT '采购单号(PO-年月日-时分秒)';

-- 4. 故障记录表 (faultrecords) 增强 
-- 注意：确认列名是否是faultNumber或FaultNumber
-- 检查faultNumber列是否存在
SET @column_exists = (
  SELECT COUNT(1) FROM information_schema.columns 
  WHERE table_schema = DATABASE() 
  AND table_name = 'faultrecords' 
  AND column_name = 'faultNumber'
);

SET @sql = IF(@column_exists = 0, 
    'ALTER TABLE `faultrecords` ADD COLUMN `faultNumber` varchar(30) DEFAULT NULL COMMENT "故障单号(FIX-年月日-序号)"', 
    'SELECT "Column already exists"');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查expected_return_date列是否存在
SET @column_exists = (
  SELECT COUNT(1) FROM information_schema.columns 
  WHERE table_schema = DATABASE() 
  AND table_name = 'faultrecords' 
  AND column_name = 'expected_return_date'
);

SET @sql = IF(@column_exists = 0, 
    'ALTER TABLE `faultrecords` ADD COLUMN `expected_return_date` datetime DEFAULT NULL COMMENT "预期返厂日期"', 
    'SELECT "Column already exists"');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查warning_flag列是否存在
SET @column_exists = (
  SELECT COUNT(1) FROM information_schema.columns 
  WHERE table_schema = DATABASE() 
  AND table_name = 'faultrecords' 
  AND column_name = 'warning_flag'
);

SET @sql = IF(@column_exists = 0, 
    'ALTER TABLE `faultrecords` ADD COLUMN `warning_flag` tinyint(1) DEFAULT 0 COMMENT "超期预警标记"', 
    'SELECT "Column already exists"');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查索引是否存在
SET @exist_idx = (
  SELECT COUNT(1) FROM information_schema.statistics 
  WHERE table_schema = DATABASE() 
  AND table_name = 'faultrecords' 
  AND index_name = 'idx_faultrecords_faultNumber'
);

SET @sql = IF(@exist_idx = 0, 
    'ALTER TABLE `faultrecords` ADD INDEX `idx_faultrecords_faultNumber` (`faultNumber`)', 
    'SELECT "Index already exists"');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 5. 任务表 (tasks) 增强
-- 检查progress列是否存在
SET @column_exists = (
  SELECT COUNT(1) FROM information_schema.columns 
  WHERE table_schema = DATABASE() 
  AND table_name = 'tasks' 
  AND column_name = 'progress'
);

SET @sql = IF(@column_exists = 0, 
    'ALTER TABLE `tasks` ADD COLUMN `progress` int DEFAULT 0 COMMENT "进度百分比(0-100)"', 
    'SELECT "Column already exists"');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查points列是否存在
SET @column_exists = (
  SELECT COUNT(1) FROM information_schema.columns 
  WHERE table_schema = DATABASE() 
  AND table_name = 'tasks' 
  AND column_name = 'points'
);

SET @sql = IF(@column_exists = 0, 
    'ALTER TABLE `tasks` ADD COLUMN `points` int DEFAULT 5 COMMENT "任务积分值"', 
    'SELECT "Column already exists"');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查claimed_by_id列是否存在
SET @column_exists = (
  SELECT COUNT(1) FROM information_schema.columns 
  WHERE table_schema = DATABASE() 
  AND table_name = 'tasks' 
  AND column_name = 'claimed_by_id'
);

SET @sql = IF(@column_exists = 0, 
    'ALTER TABLE `tasks` ADD COLUMN `claimed_by_id` int DEFAULT NULL COMMENT "认领人ID"', 
    'SELECT "Column already exists"');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查外键约束是否存在
SET @exist_fk = (
  SELECT COUNT(1) FROM information_schema.TABLE_CONSTRAINTS
  WHERE table_schema = DATABASE()
  AND table_name = 'tasks'
  AND constraint_name = 'fk_task_claimed_by'
);

SET @sql = IF(@exist_fk = 0, 
    'ALTER TABLE `tasks` ADD CONSTRAINT `fk_task_claimed_by` FOREIGN KEY (`claimed_by_id`) REFERENCES `users` (`id`)', 
    'SELECT "Foreign key already exists"');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 6. 库存安全线配置表 (inventory_thresholds)
-- 不需要修改，因为库存安全线配置表的结构是正确的 

第二部分
-- 前面第一步已经创建好了所有表结构
-- 该脚本只创建存储过程、触发器和事件，不涉及表结构修改

-- ===================================================
-- 创建业务存储过程
-- ===================================================

-- 1. 资产编号生成器
DROP PROCEDURE IF EXISTS `generate_asset_code`;
DELIMITER //
CREATE PROCEDURE `generate_asset_code`(
    IN asset_type_code VARCHAR(10),
    OUT asset_code VARCHAR(30)
)
BEGIN
    DECLARE date_part VARCHAR(10);
    DECLARE seq_num INT;
    
    -- 当前日期部分 (年月日)
    SET date_part = DATE_FORMAT(NOW(), '%Y%m%d');
    
    -- 获取当天最大序号并+1
    SELECT IFNULL(MAX(SUBSTRING_INDEX(assetCode, '-', -1)), 0) + 1 
    INTO seq_num
    FROM assets 
    WHERE assetCode LIKE CONCAT('IT-', asset_type_code, '-', date_part, '-%');
    
    -- 格式化资产编号
    SET asset_code = CONCAT('IT-', asset_type_code, '-', date_part, '-', LPAD(seq_num, 3, '0'));
END //
DELIMITER ;

-- 2. 库存转资产过程
DROP PROCEDURE IF EXISTS `inventory_to_asset`;
DELIMITER //
CREATE PROCEDURE `inventory_to_asset`(
    IN inventory_id INT,
    IN location_id INT,
    IN user_id INT,
    OUT asset_id INT
)
BEGIN
    DECLARE asset_type_code VARCHAR(10);
    DECLARE asset_code VARCHAR(30);
    
    -- 事务开始
    START TRANSACTION;
    
    -- 获取物料类型代码
    SELECT code INTO asset_type_code 
    FROM assettypes at 
    JOIN purchaseitems pi ON at.id = pi.assetTypeId
    WHERE pi.id = inventory_id;
    
    -- 生成资产编号
    CALL generate_asset_code(asset_type_code, asset_code);
    
    -- 创建资产记录
    INSERT INTO assets(
        assetCode,
        name,
        assetTypeId,
        model,
        brand,
        purchaseDate,
        locationId,
        status,
        inventoryId,
        createdAt,
        updatedAt
    )
    SELECT 
        asset_code,
        pi.name,
        pi.assetTypeId,
        pi.model,
        pi.brand,
        po.orderDate,
        location_id,
        1, -- 使用中状态
        inventory_id,
        NOW(),
        NOW()
    FROM purchaseitems pi
    JOIN purchaseorders po ON pi.purchaseOrderId = po.id
    WHERE pi.id = inventory_id;
    
    -- 更新库存状态
    UPDATE purchaseitems 
    SET status = 2 -- 已出库
    WHERE id = inventory_id;
    
    -- 获取新创建的资产ID
    SELECT LAST_INSERT_ID() INTO asset_id;
    
    -- 提交事务
    COMMIT;
END //
DELIMITER ;

-- 3. 任务积分计算过程
DROP PROCEDURE IF EXISTS `calculate_points`;
DELIMITER //
CREATE PROCEDURE `calculate_points`(
    IN user_id INT,
    IN action_type VARCHAR(50),
    IN reference_id INT,
    IN reference_type VARCHAR(50)
)
BEGIN
    DECLARE points_awarded INT;
    
    -- 根据动作类型确定积分
    CASE action_type
        WHEN 'create_task' THEN SET points_awarded = 5;
        WHEN 'update_progress' THEN SET points_awarded = 3;
        WHEN 'complete_task' THEN SET points_awarded = 10;
        ELSE SET points_awarded = 1;
    END CASE;
    
    -- 记录积分历史
    INSERT INTO point_history(
        user_id,
        action_type,
        points,
        reference_id,
        reference_type,
        created_at
    ) VALUES (
        user_id,
        action_type,
        points_awarded,
        reference_id,
        reference_type,
        NOW()
    );
    
    -- 更新用户积分
    INSERT INTO user_points (user_id, points, daily_points, weekly_points, monthly_points)
    VALUES (user_id, points_awarded, points_awarded, points_awarded, points_awarded)
    ON DUPLICATE KEY UPDATE
        points = points + points_awarded,
        daily_points = daily_points + points_awarded,
        weekly_points = weekly_points + points_awarded,
        monthly_points = monthly_points + points_awarded;
END //
DELIMITER ;

-- 4. 生成每日积分榜过程
DROP PROCEDURE IF EXISTS `generate_daily_leaderboard`;
DELIMITER //
CREATE PROCEDURE `generate_daily_leaderboard`()
BEGIN
    -- 清除今日排行榜数据
    DELETE FROM point_leaderboard 
    WHERE leaderboard_date = CURDATE() AND leaderboard_type = 'daily';
    
    -- 插入最新排行榜，使用ranking而非rank关键字
    INSERT INTO point_leaderboard (
        user_id,
        points,
        ranking,
        leaderboard_date,
        leaderboard_type,
        created_at
    )
    SELECT 
        user_id,
        daily_points as points,
        RANK() OVER (ORDER BY daily_points DESC) as ranking,
        CURDATE() as leaderboard_date,
        'daily' as leaderboard_type,
        NOW() as created_at
    FROM user_points
    WHERE daily_points > 0
    ORDER BY daily_points DESC;
    
    -- 重置每日积分
    UPDATE user_points SET daily_points = 0;
END //
DELIMITER ;

-- ===================================================
-- 创建触发器
-- ===================================================

-- 1. 资产位置合法性触发器
DROP TRIGGER IF EXISTS `check_asset_location_level`;
DELIMITER //
CREATE TRIGGER `check_asset_location_level`
BEFORE INSERT ON `assets`
FOR EACH ROW
BEGIN
    DECLARE loc_level INT;
    
    -- 获取位置级别
    SELECT level INTO loc_level FROM locations WHERE id = NEW.locationId;
    
    -- 确保资产只能关联到第5级位置
    IF loc_level != 5 THEN
        SIGNAL SQLSTATE '45000' 
        SET MESSAGE_TEXT = 'Assets must be associated with level 5 locations only';
    END IF;
END //
DELIMITER ;

-- 2. 采购单号格式触发器 (注意使用正确的列名OrderCode)
DROP TRIGGER IF EXISTS `format_purchase_order_number`;
DELIMITER //
CREATE TRIGGER `format_purchase_order_number`
BEFORE INSERT ON `purchaseorders`
FOR EACH ROW
BEGIN
    -- 如果没有手动指定采购单号
    IF NEW.OrderCode IS NULL OR NEW.OrderCode = '' THEN
        -- 生成采购单号 (PO-年月日-时分秒)
        SET NEW.OrderCode = CONCAT('PO-', DATE_FORMAT(NOW(), '%Y%m%d-%H%i%s'));
    END IF;
END //
DELIMITER ;

-- 3. 故障单号生成触发器
DROP TRIGGER IF EXISTS `generate_fault_number`;
DELIMITER //
CREATE TRIGGER `generate_fault_number`
BEFORE INSERT ON `faultrecords`
FOR EACH ROW
BEGIN
    DECLARE date_part VARCHAR(10);
    DECLARE seq_num INT;
    
    -- 如果没有手动指定故障单号
    IF NEW.faultNumber IS NULL OR NEW.faultNumber = '' THEN
        -- 生成日期部分
        SET date_part = DATE_FORMAT(NOW(), '%Y%m%d');
        
        -- 获取当天最大序号
        SELECT IFNULL(MAX(SUBSTRING_INDEX(faultNumber, '-', -1)), 0) + 1 
        INTO seq_num
        FROM faultrecords 
        WHERE faultNumber LIKE CONCAT('FIX-', date_part, '-%');
        
        -- 设置故障单号
        SET NEW.faultNumber = CONCAT('FIX-', date_part, '-', LPAD(seq_num, 3, '0'));
    END IF;
END //
DELIMITER ;

-- 4. 任务历史记录触发器
DROP TRIGGER IF EXISTS `record_task_history`;
DELIMITER //
CREATE TRIGGER `record_task_history`
AFTER UPDATE ON `tasks`
FOR EACH ROW
BEGIN
    DECLARE action_desc VARCHAR(255);
    DECLARE changed BOOLEAN DEFAULT FALSE;
    
    -- 检查状态是否变更
    IF NEW.Status != OLD.Status THEN
        SET action_desc = CONCAT('状态从 ', 
            CASE OLD.Status
                WHEN 0 THEN '未开始'
                WHEN 1 THEN '进行中'
                WHEN 2 THEN '已完成'
                ELSE '未知'
            END, 
            ' 变更为 ', 
            CASE NEW.Status
                WHEN 0 THEN '未开始'
                WHEN 1 THEN '进行中'
                WHEN 2 THEN '已完成'
                ELSE '未知'
            END);
        SET changed = TRUE;
    -- 检查进度是否变更
    ELSEIF NEW.progress != OLD.progress THEN
        SET action_desc = CONCAT('进度从 ', OLD.progress, '% 更新为 ', NEW.progress, '%');
        SET changed = TRUE;
    -- 检查认领人是否变更
    ELSEIF (NEW.claimed_by_id IS NOT NULL AND OLD.claimed_by_id IS NULL) THEN
        SET action_desc = '认领了任务';
        SET changed = TRUE;
    -- 检查任务标题是否变更
    ELSEIF NEW.Title != OLD.Title THEN
        SET action_desc = CONCAT('更新了任务标题为 "', NEW.Title, '"');
        SET changed = TRUE;
    END IF;
    
    -- 如果发生了变更，记录历史
    IF changed THEN
        INSERT INTO task_history (
            task_id,
            user_id,
            action,
            snapshot,
            created_at
        ) VALUES (
            NEW.Id,
            NEW.AssignedToId, -- 假设是当前操作用户
            action_desc,
            JSON_OBJECT(
                'id', NEW.Id,
                'title', NEW.Title,
                'status', NEW.Status,
                'progress', NEW.progress,
                'assignedTo', NEW.AssignedToId,
                'dueDate', NEW.DueDate
            ),
            NOW()
        );
    END IF;
END //
DELIMITER ;

-- ===================================================
-- 创建事件
-- ===================================================

-- 开启事件调度器
SET GLOBAL event_scheduler = ON;

-- 创建库存和返厂预警事件
DROP EVENT IF EXISTS `check_inventory_warning`;
DELIMITER //
CREATE EVENT `check_inventory_warning`
ON SCHEDULE EVERY 1 DAY STARTS (CONCAT(CURDATE(), ' 08:00:00'))
DO
BEGIN
    -- 更新低库存预警
    UPDATE purchaseitems pi
    JOIN inventory_thresholds it ON pi.assetTypeId = it.asset_type_id
    SET pi.status = 3 -- 假设3=低库存预警
    WHERE pi.status = 1 -- 在库状态
    AND pi.quantity <= it.min_threshold;
    
    -- 更新超期返厂预警
    UPDATE faultrecords
    SET warning_flag = 1
    WHERE expected_return_date < NOW()
    AND status != 2; -- 未完成状态
    
    -- 生成系统通知
    INSERT INTO notifications (
        user_id,
        title,
        content,
        type,
        reference_id,
        reference_type,
        created_at
    )
    SELECT 
        u.id,
        '库存预警通知',
        CONCAT('资产类型 [', at.name, '] 当前库存量为 ', pi.quantity, '，低于安全阈值 ', it.min_threshold),
        'alert',
        pi.id,
        'inventory',
        NOW()
    FROM purchaseitems pi
    JOIN assettypes at ON pi.assetTypeId = at.id
    JOIN inventory_thresholds it ON at.id = it.asset_type_id
    JOIN users u ON u.roleId = 1 -- 假设1为管理员角色
    WHERE pi.status = 3 -- 低库存预警状态
    AND pi.quantity <= it.min_threshold;
    
    -- 生成每日积分排行
    CALL generate_daily_leaderboard();
END //
DELIMITER ;

-- 验证脚本执行完成
SELECT 'IT资产管理系统数据库补充脚本 - 第2部分执行完成' AS '执行状态';