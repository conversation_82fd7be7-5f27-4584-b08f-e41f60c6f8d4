using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using ItAssetsSystem.Infrastructure.Data;
using System;
using System.Threading.Tasks;

namespace ItAssetsSystem.Application.Features.Gamification.Services
{
    /// <summary>
    /// 业务操作追踪器
    /// 实现标准的双写模式：业务数据 + 行为追踪
    /// </summary>
    public interface IBusinessOperationTracker
    {
        /// <summary>
        /// 执行带行为追踪的业务操作
        /// </summary>
        Task<T> ExecuteWithTrackingAsync<T>(
            Func<Task<T>> businessOperation,
            string actionType,
            int userId,
            Func<T, long?> getReferenceId = null,
            string? referenceTable = null,
            Func<T, object?> getContext = null);

        /// <summary>
        /// 执行带行为追踪的业务操作（无返回值）
        /// </summary>
        Task ExecuteWithTrackingAsync(
            Func<Task> businessOperation,
            string actionType,
            int userId,
            long? referenceId = null,
            string? referenceTable = null,
            object? context = null);
    }

    public class BusinessOperationTracker : IBusinessOperationTracker
    {
        private readonly AppDbContext _context;
        private readonly IStandardBehaviorTracker _behaviorTracker;
        private readonly ILogger<BusinessOperationTracker> _logger;

        public BusinessOperationTracker(
            AppDbContext context,
            IStandardBehaviorTracker behaviorTracker,
            ILogger<BusinessOperationTracker> logger)
        {
            _context = context;
            _behaviorTracker = behaviorTracker;
            _logger = logger;
        }

        /// <summary>
        /// 执行带行为追踪的业务操作
        /// </summary>
        public async Task<T> ExecuteWithTrackingAsync<T>(
            Func<Task<T>> businessOperation,
            string actionType,
            int userId,
            Func<T, long?> getReferenceId = null,
            string? referenceTable = null,
            Func<T, object?> getContext = null)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                // 1. 执行业务操作
                var result = await businessOperation();

                // 2. 获取关联信息
                var referenceId = getReferenceId?.Invoke(result);
                var context = getContext?.Invoke(result);

                // 3. 记录行为追踪
                await _behaviorTracker.TrackBehaviorAsync(
                    actionType, 
                    userId, 
                    referenceId, 
                    referenceTable, 
                    context);

                // 4. 提交事务
                await transaction.CommitAsync();

                _logger.LogInformation("业务操作 {ActionType} 执行成功，用户 {UserId}，关联ID {ReferenceId}", 
                    actionType, userId, referenceId);

                return result;
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, "业务操作 {ActionType} 执行失败，用户 {UserId}", actionType, userId);
                throw;
            }
        }

        /// <summary>
        /// 执行带行为追踪的业务操作（无返回值）
        /// </summary>
        public async Task ExecuteWithTrackingAsync(
            Func<Task> businessOperation,
            string actionType,
            int userId,
            long? referenceId = null,
            string? referenceTable = null,
            object? context = null)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                // 1. 执行业务操作
                await businessOperation();

                // 2. 记录行为追踪
                await _behaviorTracker.TrackBehaviorAsync(
                    actionType, 
                    userId, 
                    referenceId, 
                    referenceTable, 
                    context);

                // 3. 提交事务
                await transaction.CommitAsync();

                _logger.LogInformation("业务操作 {ActionType} 执行成功，用户 {UserId}，关联ID {ReferenceId}", 
                    actionType, userId, referenceId);
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, "业务操作 {ActionType} 执行失败，用户 {UserId}", actionType, userId);
                throw;
            }
        }
    }

    /// <summary>
    /// 标准行为类型常量
    /// </summary>
    public static class StandardBehaviorTypes
    {
        // 任务相关
        public const string TASK_CREATED = "TASK_CREATED";
        public const string TASK_CLAIMED = "TASK_CLAIMED";
        public const string TASK_COMPLETED = "TASK_COMPLETED";
        public const string TASK_COMMENTED = "TASK_COMMENTED";

        // 资产相关
        public const string ASSET_CREATED = "ASSET_CREATED";
        public const string ASSET_UPDATED = "ASSET_UPDATED";
        public const string ASSET_TRANSFERRED = "ASSET_TRANSFERRED";

        // 故障相关
        public const string FAULT_RECORDED = "FAULT_RECORDED";
        public const string FAULT_RESOLVED = "FAULT_RESOLVED";

        // 采购相关
        public const string PURCHASE_CREATED = "PURCHASE_CREATED";
        public const string PURCHASE_UPDATED = "PURCHASE_UPDATED";

        // 备件相关
        public const string SPAREPART_INBOUND = "SPAREPART_INBOUND";
        public const string SPAREPART_OUTBOUND = "SPAREPART_OUTBOUND";
        public const string SPAREPART_UPDATED = "SPAREPART_UPDATED";
    }

    /// <summary>
    /// 标准业务表名常量
    /// </summary>
    public static class StandardTableNames
    {
        public const string TASKS = "tasks_v2";
        public const string ASSETS = "assets";
        public const string FAULTS = "faults";
        public const string PURCHASES = "purchases";
        public const string SPAREPARTS = "spare_parts";
        public const string TASK_CLAIMS = "task_claims";
    }
}
