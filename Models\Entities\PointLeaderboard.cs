// IT资产管理系统 - 积分排行榜实体
// 文件路径: /Models/Entities/PointLeaderboard.cs
// 功能: 定义积分排行榜数据结构

using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ItAssetsSystem.Models.Entities
{
    /// <summary>
    /// 积分排行榜实体类
    /// </summary>
    [Table("point_leaderboard")]
    public class PointLeaderboard
    {
        /// <summary>
        /// 记录ID
        /// </summary>
        [Key]
        [Column("id")]
        public int Id { get; set; }
        
        /// <summary>
        /// 用户ID
        /// </summary>
        [Required]
        [Column("user_id")]
        public int UserId { get; set; }
        
        /// <summary>
        /// 积分值
        /// </summary>
        [Column("points")]
        public int Points { get; set; }
        
        /// <summary>
        /// 排名
        /// </summary>
        [Column("ranking")]
        public int Ranking { get; set; }
        
        /// <summary>
        /// 榜单日期
        /// </summary>
        [Column("leaderboard_date")]
        public DateTime LeaderboardDate { get; set; }
        
        /// <summary>
        /// 榜单类型（daily,weekly,monthly,total）
        /// </summary>
        [Column("leaderboard_type")]
        [StringLength(20)]
        public string LeaderboardType { get; set; }
        
        /// <summary>
        /// 创建时间
        /// </summary>
        [Column("created_at")]
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        
        /// <summary>
        /// 导航属性 - 用户
        /// </summary>
        public virtual User User { get; set; }
    }
} 