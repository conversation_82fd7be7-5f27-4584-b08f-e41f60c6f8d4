<template>
  <div class="factory-dashboard">
    <!-- Header -->
    <header class="dashboard-header">
      <div class="max-w-7xl mx-auto flex flex-wrap items-center justify-between gap-2">
        <div class="flex items-center">
          <div class="header-icon">
            <el-icon size="32"><Cpu /></el-icon>
          </div>
          <div class="header-title">
            <h1>智能制造监控系统</h1>
            <p>实时工厂状态监控 • {{ stats.total }}个工位</p>
          </div>
          <div class="update-badge">
            <span class="text-gray-400">最后更新: </span>
            <span class="font-medium">{{ formattedTime }}</span>
          </div>
        </div>
        
        <div class="header-controls">
          <!-- 配置文件选择器 -->
          <el-select
            v-model="configType"
            @change="handleConfigChange"
            placeholder="选择布局配置"
            class="config-selector"
            size="small"
          >
            <el-option label="默认布局" value="default" />
            <el-option label="自定义布局" value="custom" />
            <el-option label="导入JSON文件" value="import" />
          </el-select>

          <!-- JSON文件导入 -->
          <div v-if="configType === 'import'" class="file-import-section">
            <el-upload
              ref="uploadRef"
              :auto-upload="false"
              :show-file-list="false"
              accept=".json"
              :on-change="handleFileChange"
              class="json-uploader"
            >
              <el-button type="info" size="small" :icon="Setting">选择JSON</el-button>
            </el-upload>
            <el-button
              type="success"
              size="small"
              @click="loadDefaultJson"
              title="加载默认JSON文件"
            >
              加载默认
            </el-button>
            <span v-if="importedFileName" class="imported-file-name">{{ importedFileName }}</span>
          </div>
          
          <!-- 刷新按钮 -->
          <el-button 
            @click="handleRefresh"
            :loading="refreshing"
            size="small"
            circle
            title="刷新数据"
          >
            <el-icon><Refresh /></el-icon>
          </el-button>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto p-3 flex-grow">
      <!-- Loading State -->
      <div v-if="loading" class="loading-container">
        <el-loading-spinner text="正在加载工厂布局配置..." />
      </div>
      
      <!-- Main Content -->
      <div v-else class="dashboard-main">
        <!-- Stats Panel -->
        <div class="stats-panel">
          <el-card class="stats-card">
            <template #header>
              <div class="card-header">
                <span>实时概览</span>
                <span class="update-time">{{ formattedTime }}</span>
              </div>
            </template>
            
            <div class="stats-grid">
              <div class="stat-item operational">
                <div class="stat-icon">
                  <el-icon><Check /></el-icon>
                </div>
                <div class="stat-content">
                  <div class="stat-value">{{ stats.operational }}</div>
                  <div class="stat-label">运行正常</div>
                  <div class="stat-percent">{{ stats.operationalPercent }}%</div>
                </div>
              </div>
              
              <div class="stat-item warning">
                <div class="stat-icon">
                  <el-icon><Warning /></el-icon>
                </div>
                <div class="stat-content">
                  <div class="stat-value">{{ stats.warning }}</div>
                  <div class="stat-label">警告状态</div>
                </div>
              </div>
              
              <div class="stat-item error">
                <div class="stat-icon">
                  <el-icon><Close /></el-icon>
                </div>
                <div class="stat-content">
                  <div class="stat-value">{{ stats.error }}</div>
                  <div class="stat-label">错误状态</div>
                </div>
              </div>
              
              <div class="stat-item idle">
                <div class="stat-icon">
                  <el-icon><Setting /></el-icon>
                </div>
                <div class="stat-content">
                  <div class="stat-value">{{ stats.idle }}</div>
                  <div class="stat-label">空闲工位</div>
                </div>
              </div>
            </div>
          </el-card>
        </div>

        <!-- Factory Layout -->
        <div class="factory-layout">
          <div class="layout-header">
            <span>显示: {{ locations.length }} 个工位</span>
            <span v-if="layoutConfig" class="layout-info">
              | {{ layoutConfig.zones?.length || 0 }} 个区域
              | {{ layoutConfig.name || '未命名布局' }}
            </span>
            <span v-if="importedFileName" class="current-file">
              | 当前文件: {{ importedFileName }}
            </span>
          </div>

          <!-- Layout View -->
          <div class="factory-floor">
            <!-- Factory Grid Background -->
            <svg class="factory-grid" :viewBox="canvasViewBox">
              <defs>
                <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
                  <path d="M 20 0 L 0 0 0 20" fill="none" stroke="rgba(107, 148, 214, 0.08)" stroke-width="1"/>
                </pattern>
              </defs>
              <rect width="100%" height="100%" fill="url(#grid)" />
            </svg>

            <!-- 动态区域容器布局系统 -->
            <div class="zone-containers" :style="containerStyle">
              <!-- 动态生成区域容器 -->
              <div
                v-for="zone in layoutConfig?.zones || []"
                :key="zone.id"
                class="zone-container"
                :class="`zone-${zone.id}`"
                :data-zone="zone.name"
                :style="getZoneContainerStyle(zone)"
              >
                <div class="zone-workstations" :style="getZoneGridStyle(zone)">
                  <div
                    v-for="location in getSortedZoneWorkstations(zone)"
                    :key="location.locationId"
                    class="workstation-cell"
                    :class="getWorkstationClasses(location)"
                    @click="handleLocationClick(location.locationId)"
                  >
                    <div class="cell-content">
                      <el-icon v-if="location.status === 'error'" class="status-icon error"><Close /></el-icon>
                      <el-icon v-else-if="location.status === 'warning'" class="status-icon warning"><Warning /></el-icon>
                      <el-icon v-else-if="location.status === 'operational'" class="status-icon operational"><Check /></el-icon>
                      <span v-else class="cell-number">{{ location.locationId }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Refresh, Cpu, Check, Warning, Close, Setting
} from '@element-plus/icons-vue'

// 响应式数据
const locations = ref([])
const loading = ref(true)
const refreshing = ref(false)
const configType = ref('import')
const customConfigFile = ref('factory-layout-1748945213262.json')
const layoutConfig = ref(null)
const lastUpdate = ref(new Date())
const importedFileName = ref('')
const importedFileContent = ref(null)
const uploadRef = ref(null)

// 加载工厂布局配置
const loadLayoutConfig = async () => {
  try {
    loading.value = true

    let config

    // 如果是导入模式且有导入的文件内容，直接使用
    if (configType.value === 'import' && importedFileContent.value) {
      console.log('使用导入的JSON文件内容')
      config = importedFileContent.value
    } else {
      // 从服务器加载配置文件
      let configPath
      if (configType.value === 'custom') {
        configPath = `/analyresport/${customConfigFile.value}`
      } else if (configType.value === 'default') {
        configPath = '/Configurations/factory-layout.json'
      } else {
        // import模式但没有文件内容，使用默认配置
        console.log('导入模式但没有文件，使用默认配置')
        layoutConfig.value = getDefaultLayoutConfig()
        return layoutConfig.value
      }

      console.log(`加载配置文件: ${configPath}`)

      const response = await fetch(configPath)
      console.log('响应状态:', response.status, response.statusText)
      console.log('响应头:', response.headers.get('content-type'))

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const responseText = await response.text()
      console.log('响应内容前100字符:', responseText.substring(0, 100))

      try {
        config = JSON.parse(responseText)
      } catch (parseError) {
        console.error('JSON解析错误:', parseError)
        console.error('响应内容:', responseText)
        throw new Error(`JSON解析失败: ${parseError.message}`)
      }
    }

    if (config.factoryLayout) {
      layoutConfig.value = config.factoryLayout
    } else if (config.zones && Array.isArray(config.zones)) {
      layoutConfig.value = adaptCustomConfig(config)
    } else {
      throw new Error('未识别的配置文件格式')
    }

    console.log('布局配置加载成功:', layoutConfig.value)
    return layoutConfig.value
  } catch (error) {
    console.error('加载布局配置失败:', error)
    ElMessage.error(`加载布局配置失败: ${error.message}，使用默认配置`)
    layoutConfig.value = getDefaultLayoutConfig()
    return layoutConfig.value
  } finally {
    loading.value = false
  }
}

// 适配自定义配置格式
const adaptCustomConfig = (customConfig) => {
  // 计算原始画布的实际尺寸
  let maxX = 0, maxY = 0
  customConfig.zones.forEach(zone => {
    maxX = Math.max(maxX, zone.x + zone.width)
    maxY = Math.max(maxY, zone.y + zone.height)
  })

  // 使用原始尺寸，添加一些边距
  const originalCanvasWidth = maxX + 50
  const originalCanvasHeight = maxY + 50

  console.log(`原始画布尺寸: ${originalCanvasWidth} x ${originalCanvasHeight}`)

  const adapted = {
    name: "自定义工厂布局",
    description: `包含${customConfig.zones.length}个区域的工厂布局`,
    version: customConfig.version || "1.0",
    canvas: {
      width: originalCanvasWidth,
      height: originalCanvasHeight,
      backgroundColor: "#0f172a",
      gridSize: 20
    },
    zones: [],
    statusDistribution: { operational: 0.7, warning: 0.15, error: 0.1, idle: 0.05 },
    defaultMetrics: {
      efficiency: { min: 70, max: 100 },
      uptime: { min: 80, max: 100 },
      assetCount: { min: 2, max: 7 },
      taskCount: { min: 1, max: 9 }
    }
  }

  customConfig.zones.forEach(zone => {
    const workstationCount = zone.rows * zone.cols
    const positions = []

    // 计算每个工位的实际尺寸（考虑间距）
    const cellWidth = zone.width / zone.cols
    const cellHeight = zone.height / zone.rows

    for (let row = 0; row < zone.rows; row++) {
      for (let col = 0; col < zone.cols; col++) {
        const workstationId = zone.startWorkstation + row * zone.cols + col
        // 计算工位在区域内的相对位置
        const x = col * cellWidth
        const y = row * cellHeight

        positions.push({
          id: workstationId,
          x: x,
          y: y,
          name: `${zone.name}-工位${workstationId.toString().padStart(3, '0')}`
        })
      }
    }

    adapted.zones.push({
      id: `zone${zone.id}`,
      name: zone.name,
      uniqueName: `${zone.name}-${zone.id}`, // 添加唯一标识符
      description: `${zone.name}生产区域`,
      color: zone.color,
      position: { x: zone.x, y: zone.y, width: zone.width, height: zone.height },
      layout: { type: "grid", rows: zone.rows, cols: zone.cols },
      workstations: { startId: zone.startWorkstation, count: workstationCount, pattern: "grid", positions: positions }
    })
  })

  return adapted
}

// 默认布局配置
const getDefaultLayoutConfig = () => {
  return {
    name: "默认工厂布局",
    description: "包含1个区域的默认工厂布局",
    version: "1.0",
    canvas: {
      width: 1000,
      height: 600,
      backgroundColor: "#0f172a",
      gridSize: 20
    },
    zones: [{
      id: "zone1",
      name: "默认区域",
      uniqueName: "默认区域-1",
      description: "默认区域生产区域",
      color: "#4A90E2",
      position: { x: 100, y: 100, width: 300, height: 270 },
      layout: { type: "grid", rows: 3, cols: 3 },
      workstations: {
        startId: 1,
        count: 9,
        pattern: "grid",
        positions: Array.from({length: 9}, (_, i) => ({
          id: i + 1,
          x: (i % 3) * 100,
          y: Math.floor(i / 3) * 90,
          name: `默认区域-工位${(i + 1).toString().padStart(3, '0')}`
        }))
      }
    }],
    statusDistribution: { operational: 0.7, warning: 0.15, error: 0.1, idle: 0.05 },
    defaultMetrics: {
      efficiency: { min: 70, max: 100 },
      uptime: { min: 80, max: 100 },
      assetCount: { min: 2, max: 7 },
      taskCount: { min: 1, max: 9 }
    }
  }
}

// 根据JSON配置生成工位数据
const generateWorkstationDataFromLayout = () => {
  if (!layoutConfig.value) return []

  const workstations = []
  const statuses = ['operational', 'warning', 'error', 'idle']
  const distribution = layoutConfig.value.statusDistribution
  const statusWeights = [distribution.operational, distribution.warning, distribution.error, distribution.idle]

  layoutConfig.value.zones.forEach(zone => {
    // 根据区域的行列数生成对应数量的工位
    const { rows, cols } = zone.layout
    const startId = zone.workstations.startId

    for (let row = 0; row < rows; row++) {
      for (let col = 0; col < cols; col++) {
        const workstationId = startId + row * cols + col

        const random = Math.random()
        let status = 'operational'
        let cumulative = 0

        for (let j = 0; j < statusWeights.length; j++) {
          cumulative += statusWeights[j]
          if (random < cumulative) {
            status = statuses[j]
            break
          }
        }

        const metrics = layoutConfig.value.defaultMetrics || {
          efficiency: { min: 70, max: 100 },
          uptime: { min: 80, max: 100 },
          assetCount: { min: 2, max: 7 },
          taskCount: { min: 1, max: 9 }
        }

        workstations.push({
          locationId: workstationId,
          locationName: `${zone.name}-工位${workstationId.toString().padStart(3, '0')}`,
          locationCode: `WS${workstationId.toString().padStart(3, '0')}`,
          departmentName: zone.uniqueName, // 使用唯一标识符
          originalDepartmentName: zone.name, // 保留原始名称用于显示
          status: status,
          efficiency: Math.floor(Math.random() * (metrics.efficiency.max - metrics.efficiency.min + 1)) + metrics.efficiency.min,
          uptime: Math.floor(Math.random() * (metrics.uptime.max - metrics.uptime.min + 1)) + metrics.uptime.min,
          assetCount: Math.floor(Math.random() * (metrics.assetCount.max - metrics.assetCount.min + 1)) + metrics.assetCount.min,
          taskCount: Math.floor(Math.random() * (metrics.taskCount.max - metrics.taskCount.min + 1)) + metrics.taskCount.min,
          faultCount: status === 'error' ? Math.floor(Math.random() * 3) + 1 : status === 'warning' ? Math.floor(Math.random() * 2) : 0,
          lastUpdate: new Date(),
          zoneColor: zone.color,
          zoneId: zone.id,
          isHighlighted: false,
          // 添加在区域内的相对位置信息
          relativeRow: row,
          relativeCol: col
        })
      }
    }
  })

  console.log(`生成了 ${workstations.length} 个工位数据`)
  console.log('工位分布详情:')
  layoutConfig.value.zones.forEach(zone => {
    const zoneWorkstations = workstations.filter(w => w.departmentName === zone.uniqueName)
    console.log(`  ${zone.name}(${zone.uniqueName}): ${zoneWorkstations.length}个工位 (${zone.layout.rows}x${zone.layout.cols})`)
  })
  return workstations
}

// 获取指定区域的工位列表
const getZoneWorkstations = (zoneName) => {
  return locations.value.filter(location => location.departmentName === zoneName)
}

// 获取排序后的区域工位列表（按照网格位置排序）
const getSortedZoneWorkstations = (zone) => {
  // 使用唯一标识符来获取工位
  const zoneWorkstations = locations.value.filter(location => location.departmentName === zone.uniqueName)

  console.log(`获取区域 ${zone.name}(${zone.uniqueName}) 的工位: ${zoneWorkstations.length}个`)

  // 按照行列位置排序，确保工位按照网格顺序显示
  return zoneWorkstations.sort((a, b) => {
    if (a.relativeRow !== b.relativeRow) {
      return a.relativeRow - b.relativeRow
    }
    return a.relativeCol - b.relativeCol
  })
}

// 工位样式类
const getWorkstationClasses = (location) => {
  return [`status-${location.status}`, { 'highlighted': location.isHighlighted }]
}

// 获取区域容器样式
const getZoneContainerStyle = (zone) => {
  if (!zone.position) return {}

  return {
    position: 'absolute',
    left: `${zone.position.x}px`,
    top: `${zone.position.y}px`,
    width: `${zone.position.width}px`,
    height: `${zone.position.height}px`,
    border: `2px dashed ${zone.color}40`,
    borderRadius: '8px',
    backgroundColor: `${zone.color}08`
  }
}

// 获取区域网格样式
const getZoneGridStyle = (zone) => {
  if (!zone.layout) return {}

  const { rows, cols } = zone.layout
  return {
    display: 'grid',
    gridTemplateColumns: `repeat(${cols}, 1fr)`,
    gridTemplateRows: `repeat(${rows}, 1fr)`,
    gap: '2px',
    height: '100%',
    width: '100%',
    padding: '5px'
  }
}

// 计算属性
const stats = computed(() => {
  const total = locations.value.length
  const operational = locations.value.filter(l => l.status === 'operational').length
  const warning = locations.value.filter(l => l.status === 'warning').length
  const error = locations.value.filter(l => l.status === 'error').length
  const idle = locations.value.filter(l => l.status === 'idle').length

  return {
    total,
    operational,
    warning,
    error,
    idle,
    operationalPercent: total > 0 ? Math.round(operational / total * 100) : 0
  }
})

const formattedTime = computed(() => {
  return lastUpdate.value.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
})

const canvasViewBox = computed(() => {
  if (layoutConfig.value && layoutConfig.value.canvas) {
    const { width, height } = layoutConfig.value.canvas
    return `0 0 ${width} ${height}`
  }
  return '0 0 1200 500'
})

// 计算缩放比例和容器样式
const containerStyle = computed(() => {
  if (!layoutConfig.value || !layoutConfig.value.canvas) {
    return {}
  }

  const { width: canvasWidth, height: canvasHeight } = layoutConfig.value.canvas

  // 假设显示区域大小（可以根据实际情况调整）
  const displayWidth = 900  // 右侧显示区域宽度
  const displayHeight = 400 // 右侧显示区域高度

  // 计算缩放比例，保持宽高比
  const scaleX = displayWidth / canvasWidth
  const scaleY = displayHeight / canvasHeight
  const scale = Math.min(scaleX, scaleY, 1) // 不放大，只缩小

  console.log(`画布尺寸: ${canvasWidth}x${canvasHeight}, 显示区域: ${displayWidth}x${displayHeight}, 缩放比例: ${scale}`)

  return {
    width: `${canvasWidth}px`,
    height: `${canvasHeight}px`,
    transform: `scale(${scale})`,
    transformOrigin: 'center center'
  }
})

// 文件处理
const handleFileChange = (file) => {
  console.log('选择文件:', file.name)

  if (!file.raw) {
    ElMessage.error('文件读取失败')
    return
  }

  const reader = new FileReader()
  reader.onload = (e) => {
    try {
      const content = e.target.result
      const jsonData = JSON.parse(content)

      importedFileName.value = file.name
      importedFileContent.value = jsonData

      console.log('JSON文件解析成功:', jsonData)
      ElMessage.success(`文件 ${file.name} 导入成功`)

      // 自动加载新配置
      loadLayoutConfig().then(() => {
        locations.value = generateWorkstationDataFromLayout()
        lastUpdate.value = new Date()
      })

    } catch (error) {
      console.error('JSON文件解析失败:', error)
      ElMessage.error(`JSON文件解析失败: ${error.message}`)
      importedFileName.value = ''
      importedFileContent.value = null
    }
  }

  reader.onerror = () => {
    ElMessage.error('文件读取失败')
    importedFileName.value = ''
    importedFileContent.value = null
  }

  reader.readAsText(file.raw)
}

// 加载默认JSON文件
const loadDefaultJson = async () => {
  try {
    console.log('手动加载默认JSON文件...')
    const success = await autoLoadExistingJson()

    if (success) {
      // 重新加载配置
      await loadLayoutConfig()
      locations.value = generateWorkstationDataFromLayout()
      lastUpdate.value = new Date()
    } else {
      ElMessage.warning('默认JSON文件不存在，请选择其他文件或使用默认布局')
    }
  } catch (error) {
    console.error('加载默认JSON文件失败:', error)
    ElMessage.error('加载默认JSON文件失败')
  }
}

// 事件处理
const handleLocationClick = (locationId) => {
  console.log('点击工位:', locationId)
  ElMessage.info(`点击了工位 ${locationId}`)
}

const handleRefresh = async () => {
  refreshing.value = true
  try {
    await loadLayoutConfig()
    locations.value = generateWorkstationDataFromLayout()
    lastUpdate.value = new Date()
    ElMessage.success('数据已刷新')
  } catch (error) {
    console.error('刷新失败:', error)
    ElMessage.error('刷新失败')
  } finally {
    refreshing.value = false
  }
}

const handleConfigChange = async () => {
  try {
    let configName = ''
    switch (configType.value) {
      case 'default':
        configName = '默认'
        break
      case 'custom':
        configName = '自定义'
        break
      case 'import':
        configName = '导入文件'
        // 清空之前的导入数据，等待用户选择新文件
        if (!importedFileContent.value) {
          importedFileName.value = ''
          ElMessage.info('请选择JSON文件进行导入')
          return
        }
        break
    }

    console.log(`切换到${configName}布局配置`)
    await loadLayoutConfig()
    locations.value = generateWorkstationDataFromLayout()
    lastUpdate.value = new Date()
    ElMessage.success(`已切换到${configName}布局: ${locations.value.length}个工位`)
  } catch (error) {
    console.error('切换配置失败:', error)
    ElMessage.error('切换配置失败')
  }
}

// 自动加载现有JSON文件
const autoLoadExistingJson = async () => {
  try {
    console.log('尝试自动加载现有JSON文件...')
    const response = await fetch(`/analyresport/${customConfigFile.value}`)

    if (response.ok) {
      const jsonData = await response.json()
      importedFileName.value = customConfigFile.value
      importedFileContent.value = jsonData
      console.log('自动加载JSON文件成功:', customConfigFile.value)
      ElMessage.success(`自动加载布局文件: ${customConfigFile.value}`)
      return true
    }
  } catch (error) {
    console.log('自动加载JSON文件失败，将使用默认配置:', error.message)
  }
  return false
}

// 初始化
const initDashboard = async () => {
  try {
    console.log('开始初始化工厂仪表板...')

    // 如果是导入模式，尝试自动加载现有JSON文件
    if (configType.value === 'import') {
      await autoLoadExistingJson()
    }

    await loadLayoutConfig()
    locations.value = generateWorkstationDataFromLayout()
    console.log(`工厂仪表板初始化完成: ${locations.value.length}个工位`)

    // 定时更新数据
    setInterval(() => {
      lastUpdate.value = new Date()
    }, 5000)
  } catch (error) {
    console.error('初始化工厂仪表板失败:', error)
    ElMessage.error('初始化失败，请刷新页面重试')
  }
}

// 生命周期
onMounted(async () => {
  await initDashboard()
})
</script>

<style scoped>
.factory-dashboard {
  min-height: 100vh;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
  color: #f1f5f9;
  font-family: 'SF Pro Display', 'Segoe UI', sans-serif;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: calc(100vh - 140px);
  background: rgba(30, 41, 59, 0.4);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 16px;
  padding: 2rem;
}

.dashboard-header {
  background: rgba(15, 23, 42, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(59, 130, 246, 0.2);
  padding: 1rem 0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(45deg, #3b82f6, #1d4ed8);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
  box-shadow: 0 8px 25px -8px rgba(59, 130, 246, 0.3);
}

.header-title h1 {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
  background: linear-gradient(45deg, #f1f5f9, #cbd5e1);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header-title p {
  font-size: 0.875rem;
  color: #94a3b8;
  margin: 0;
}

.update-badge {
  background: rgba(34, 197, 94, 0.1);
  border: 1px solid rgba(34, 197, 94, 0.2);
  border-radius: 8px;
  padding: 0.5rem 1rem;
  margin-left: 1rem;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.config-selector {
  width: 140px;
  margin-right: 0.75rem;
}

.file-import-section {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-left: 0.5rem;
}

.json-uploader {
  display: inline-block;
}

.imported-file-name {
  font-size: 0.75rem;
  color: #22c55e;
  background: rgba(34, 197, 94, 0.1);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  border: 1px solid rgba(34, 197, 94, 0.2);
  max-width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.layout-info {
  color: #94a3b8;
  font-size: 0.875rem;
}

.current-file {
  color: #22c55e;
  font-size: 0.875rem;
  font-weight: 500;
}

.dashboard-main {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 1.5rem;
  height: calc(100vh - 140px);
}

.stats-panel {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.stats-card {
  background: rgba(30, 41, 59, 0.6);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
}

.update-time {
  font-size: 0.75rem;
  color: #64748b;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  background: rgba(51, 65, 85, 0.3);
  border-radius: 12px;
  border-left: 4px solid;
  transition: all 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px -8px rgba(0, 0, 0, 0.3);
}

.stat-item.operational { border-left-color: #22c55e; }
.stat-item.warning { border-left-color: #eab308; }
.stat-item.error { border-left-color: #ef4444; }
.stat-item.idle { border-left-color: #6b7280; }

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
}

.operational .stat-icon { background: rgba(34, 197, 94, 0.2); color: #22c55e; }
.warning .stat-icon { background: rgba(234, 179, 8, 0.2); color: #eab308; }
.error .stat-icon { background: rgba(239, 68, 68, 0.2); color: #ef4444; }
.idle .stat-icon { background: rgba(107, 114, 128, 0.2); color: #6b7280; }

.stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  line-height: 1;
}

.stat-label {
  font-size: 0.75rem;
  color: #94a3b8;
  margin-top: 0.25rem;
}

.stat-percent {
  font-size: 0.875rem;
  font-weight: 600;
  color: #22c55e;
}

.factory-layout {
  background: rgba(30, 41, 59, 0.4);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 16px;
  padding: 1rem;
  overflow: hidden;
}

.layout-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid rgba(59, 130, 246, 0.2);
  font-weight: 600;
}

.factory-floor {
  position: relative;
  height: calc(100% - 60px);
  background: #0f172a;
  border-radius: 12px;
  overflow: auto;
  border: 2px solid rgba(59, 130, 246, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
}

.factory-grid {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.zone-containers {
  position: relative;
  z-index: 2;
  transform-origin: center center;
  /* 尺寸和缩放通过 containerStyle 计算属性动态设置 */
}

.zone-container {
  z-index: 3;
}

.zone-workstations {
  width: 100%;
  height: 100%;
}

.workstation-cell {
  background: rgba(59, 130, 246, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 20px;
  position: relative;
}

.workstation-cell:hover {
  transform: scale(1.1);
  z-index: 10;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.workstation-cell.status-operational {
  background: rgba(34, 197, 94, 0.8);
}

.workstation-cell.status-warning {
  background: rgba(234, 179, 8, 0.8);
}

.workstation-cell.status-error {
  background: rgba(239, 68, 68, 0.8);
}

.workstation-cell.status-idle {
  background: rgba(107, 114, 128, 0.8);
}

.cell-content {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  font-size: 10px;
  font-weight: 600;
  color: white;
  text-shadow: 0 1px 2px rgba(0,0,0,0.5);
}

.status-icon {
  font-size: 12px;
}

.cell-number {
  font-size: 8px;
}
</style>
