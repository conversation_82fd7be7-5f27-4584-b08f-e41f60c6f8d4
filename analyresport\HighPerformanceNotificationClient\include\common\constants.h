// 常量定义
#pragma once

#include <cstdint>

namespace notification {
namespace constants {

// 网络相关常量
constexpr uint16_t DEFAULT_UDP_PORT = 8081;
constexpr uint16_t DEFAULT_WEBSOCKET_PORT = 8080;
constexpr uint16_t DEFAULT_HTTP_PORT = 5000;

// 协议相关常量
constexpr uint32_t PROTOCOL_MAGIC = 0xDEADBEEF;
constexpr uint8_t PROTOCOL_VERSION = 1;
constexpr size_t MAX_PACKET_SIZE = 1400;  // 保证不超过MTU
constexpr size_t MAX_PAYLOAD_SIZE = MAX_PACKET_SIZE - 64;  // 预留协议头空间

// 缓冲区大小
constexpr size_t DEFAULT_BUFFER_SIZE = 65536;
constexpr size_t MAX_BUFFER_SIZE = 1048576;  // 1MB
constexpr size_t MIN_BUFFER_SIZE = 4096;

// 队列相关
constexpr size_t DEFAULT_QUEUE_SIZE = 1000;
constexpr size_t MAX_QUEUE_SIZE = 10000;
constexpr size_t BATCH_SEND_THRESHOLD = 10;

// 超时设置（毫秒）
constexpr int DEFAULT_UDP_TIMEOUT_MS = 1000;
constexpr int DEFAULT_WEBSOCKET_TIMEOUT_MS = 5000;
constexpr int DEFAULT_HTTP_TIMEOUT_MS = 10000;
constexpr int CONNECTION_TIMEOUT_MS = 30000;
constexpr int HEARTBEAT_INTERVAL_MS = 30000;
constexpr int TOKEN_REFRESH_THRESHOLD_MS = 300000;  // 5分钟

// 重试相关
constexpr int MAX_RETRY_COUNT = 3;
constexpr int RETRY_INTERVAL_MS = 1000;
constexpr int EXPONENTIAL_BACKOFF_BASE = 2;

// UI相关
constexpr int NOTIFICATION_DISPLAY_TIME_MS = 3000;
constexpr int ACHIEVEMENT_DISPLAY_TIME_MS = 5000;
constexpr int AUTO_HIDE_TIMEOUT_MS = 5000;
constexpr int NOTIFICATION_FADE_TIME_MS = 300;
constexpr size_t MAX_NOTIFICATIONS = 100;

// 性能相关
constexpr int THREAD_POOL_SIZE = 4;
constexpr size_t MEMORY_POOL_BLOCK_SIZE = 1024;
constexpr size_t MEMORY_POOL_BLOCKS = 1000;
constexpr int STATISTICS_INTERVAL_MS = 60000;  // 1分钟

// 文件路径
constexpr const char* CONFIG_FILE_PATH = "config/config.json";
constexpr const char* LOG_FILE_PATH = "logs/client.log";
constexpr const char* CRASH_DUMP_PATH = "dumps/";

// API端点
constexpr const char* API_AUTH_LOGIN = "/api/auth/login";
constexpr const char* API_AUTH_REFRESH = "/api/auth/refresh";
constexpr const char* API_V2_GAMIFICATION = "/api/v2/gamification";
constexpr const char* API_V2_WORK_SUMMARY = "/api/v2/work-summary";
constexpr const char* API_V2_NOTIFICATIONS = "/api/v2/notifications";
constexpr const char* SIGNALR_HUB_PATH = "/hubs/notification";

// 游戏化相关
constexpr const char* REWARD_POINTS_KEY = "points";
constexpr const char* REWARD_COINS_KEY = "coins";
constexpr const char* REWARD_DIAMONDS_KEY = "diamonds";
constexpr const char* REWARD_EXPERIENCE_KEY = "experience";

// 任务状态
constexpr const char* TASK_STATUS_PENDING = "pending";
constexpr const char* TASK_STATUS_IN_PROGRESS = "in_progress";
constexpr const char* TASK_STATUS_COMPLETED = "completed";
constexpr const char* TASK_STATUS_CANCELLED = "cancelled";

// 错误代码
enum class ErrorCode : int {
    SUCCESS = 0,
    GENERIC_ERROR = -1,
    NETWORK_ERROR = -1000,
    CONNECTION_FAILED = -1001,
    TIMEOUT = -1002,
    PROTOCOL_ERROR = -1003,
    AUTH_ERROR = -2000,
    INVALID_CREDENTIALS = -2001,
    TOKEN_EXPIRED = -2002,
    ACCESS_DENIED = -2003,
    CONFIG_ERROR = -3000,
    FILE_NOT_FOUND = -3001,
    INVALID_CONFIG = -3002,
    UI_ERROR = -4000,
    TRAY_INIT_FAILED = -4001,
    WINDOW_CREATE_FAILED = -4002
};

// 日志级别
enum class LogLevel : int {
    TRACE = 0,
    DEBUG = 1,
    INFO = 2,
    WARN = 3,
    ERROR = 4,
    FATAL = 5
};

// 字符串常量
namespace strings {
    constexpr const char* APP_NAME = "高性能实时通知客户端";
    constexpr const char* APP_VERSION = "1.0.0";
    constexpr const char* APP_DESCRIPTION = "IT资产管理系统实时通知客户端";
    
    constexpr const char* TRAY_TOOLTIP = "IT资产管理系统 - 实时通知";
    constexpr const char* TRAY_MENU_SHOW = "显示(&S)";
    constexpr const char* TRAY_MENU_HIDE = "隐藏(&H)";
    constexpr const char* TRAY_MENU_SETTINGS = "设置(&C)";
    constexpr const char* TRAY_MENU_ABOUT = "关于(&A)";
    constexpr const char* TRAY_MENU_EXIT = "退出(&X)";
    
    constexpr const char* STATUS_CONNECTING = "连接中...";
    constexpr const char* STATUS_CONNECTED = "已连接";
    constexpr const char* STATUS_DISCONNECTED = "未连接";
    constexpr const char* STATUS_RECONNECTING = "重新连接中...";
    constexpr const char* STATUS_FAILED = "连接失败";
    
    constexpr const char* MSG_CONNECTION_SUCCESS = "成功连接到服务器";
    constexpr const char* MSG_CONNECTION_FAILED = "连接服务器失败";
    constexpr const char* MSG_AUTH_SUCCESS = "用户认证成功";
    constexpr const char* MSG_AUTH_FAILED = "用户认证失败";
    constexpr const char* MSG_TOKEN_EXPIRED = "登录令牌已过期";
    constexpr const char* MSG_CONFIG_LOADED = "配置加载成功";
    constexpr const char* MSG_CONFIG_ERROR = "配置加载失败";
}

// 数值限制
namespace limits {
    constexpr size_t MAX_USERNAME_LENGTH = 50;
    constexpr size_t MAX_PASSWORD_LENGTH = 128;
    constexpr size_t MAX_MESSAGE_TITLE_LENGTH = 100;
    constexpr size_t MAX_MESSAGE_CONTENT_LENGTH = 1000;
    constexpr size_t MAX_LOG_FILE_SIZE = 10485760;  // 10MB
    constexpr int MAX_LOG_FILES = 5;
    constexpr int MAX_NOTIFICATION_HISTORY = 1000;
}

} // namespace constants
} // namespace notification