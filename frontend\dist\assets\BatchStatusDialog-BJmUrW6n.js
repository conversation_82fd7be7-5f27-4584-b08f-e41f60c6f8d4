import{bv as e,r as a,c as t,ag as l,E as s,_ as n,bw as r,bx as o,by as i,bz as u,ai as d,bA as c,bB as p,ah as m,aJ as v,bn as g,J as y,b1 as k,bo as f,a as h,b as w,o as b,$ as I,d as _,aB as V,e as U,t as D,n as T,w as C,p as S,i as E,k as x,F as A,h as N,j as P,l as z,bC as L,af as M,au as B,bD as $,aG as O,b3 as F,bp as j,x as H,ad as R,f as q,ax as K,a9 as Q,z as G,bq as J,br as W}from"./index-CG5lHOPO.js";import{U as X,a as Y}from"./UserAvatarStack-X4c9Liov.js";import{a as Z,b as ee}from"./format-DfhXadVZ.js";import{U as ae}from"./UserSelect-BwdFLAkx.js";const te=e("taskEnhanced",(()=>{const e=a([]),n=a([]),r=a({total:0,completed:0,inProgress:0,overdue:0,todo:0}),o=a(!1),i=a(null),u=a([]),d=a({status:"",priority:"",assigneeId:"",assetId:"",locationId:"",dateRange:[]}),c=a(""),p=a({pageNumber:1,pageSize:20,total:0}),m=t((()=>{let a=[...e.value||[]];if(c.value){const e=c.value.toLowerCase();a=a.filter((a=>{var t,l,s;return(null==(t=a.name)?void 0:t.toLowerCase().includes(e))||(null==(l=a.description)?void 0:l.toLowerCase().includes(e))||(null==(s=a.assigneeUserName)?void 0:s.toLowerCase().includes(e))}))}if(d.value.status&&(a=a.filter((e=>e.status===d.value.status))),d.value.priority&&(a=a.filter((e=>e.priority===d.value.priority))),d.value.assigneeId&&(a=a.filter((e=>e.assigneeUserId===d.value.assigneeId))),d.value.assetId&&(a=a.filter((e=>e.assetId===d.value.assetId))),d.value.locationId&&(a=a.filter((e=>e.locationId===d.value.locationId))),d.value.dateRange&&2===d.value.dateRange.length){const[e,t]=d.value.dateRange;a=a.filter((a=>{const l=new Date(a.planEndDate);return l>=e&&l<=t}))}return a})),v=t((()=>{const e={Todo:[],InProgress:[],Done:[],Cancelled:[],Overdue:[]};return m.value.forEach((a=>{var t;a.isOverdue&&"Done"!==a.status?e.Overdue.push(a):null==(t=e[a.status])||t.push(a)})),e})),g=t((()=>(e.value||[]).filter((e=>e&&e.isOverdue&&"Done"!==e.status)).length)),y=t((()=>{const a=new Date,t=new Date(a.getTime()+2592e5);return(e.value||[]).filter((e=>e&&e.planEndDate&&"Done"!==e.status&&new Date(e.planEndDate)<=t&&new Date(e.planEndDate)>=a)).sort(((e,a)=>new Date(e.planEndDate)-new Date(a.planEndDate)))})),k=async(a={})=>{o.value=!0;try{const t={pageNumber:p.value.pageNumber,pageSize:p.value.pageSize,searchTerm:c.value,...d.value,...a},s=await l.getTaskList(t);if(s.success)return e.value=s.data||[],b(),s;throw new Error(s.message||"获取任务列表失败")}catch(t){throw s.error(t.message||"获取任务列表失败"),t}finally{o.value=!1}},f=async a=>{var t,s,n,r;try{const s=await l.deleteTask(a);if(s.success){const l=(null==(t=e.value)?void 0:t.findIndex((e=>e&&e.taskId===a)))??-1;if(-1!==l&&e.value){const t=e.value[l];e.value.splice(l,1),b(),I({type:"warning",title:"任务删除成功",message:`任务 "${t.name}" 已删除`,taskId:a})}return!0}throw new Error(s.message||"删除任务失败")}catch(o){throw 404===(null==(s=null==o?void 0:o.response)?void 0:s.status)?("undefined"!=typeof window&&window.ElMessage&&window.ElMessage.error("任务已被删除或不存在"),await k()):"undefined"!=typeof window&&window.ElMessage&&window.ElMessage.error("删除任务失败: "+((null==(r=null==(n=o.response)?void 0:n.data)?void 0:r.message)||o.message)),o}},h=async(e,a,t="")=>{try{const s={status:a,remarks:t},n=await l.updateTaskStatus(e,s);if(n&&n.success)return I({type:"info",title:"任务状态更新",message:`任务状态已更新为 "${a}"`,taskId:e}),n.data;throw new Error((null==n?void 0:n.message)||"更新任务状态失败")}catch(s){throw s}},w=async(a,t,s="")=>{var n;try{const r={assigneeUserId:t,remarks:s},o=await l.assignTask(a,r);if(o.success){const t=(null==(n=e.value)?void 0:n.findIndex((e=>e&&e.taskId===a)))??-1;return-1!==t&&e.value&&Object.assign(e.value[t],o.data),I({type:"info",title:"任务分配成功",message:`任务已分配给 "${o.data.assigneeUserName}"`,taskId:a}),o.data}throw new Error(o.message||"任务分配失败")}catch(r){throw r}},b=()=>{const a=e.value||[],t={total:a.length,completed:0,inProgress:0,overdue:0,todo:0};a.forEach((e=>{if(e){switch(e.status){case"Done":t.completed++;break;case"InProgress":t.inProgress++;break;case"Todo":t.todo++}e.isOverdue&&"Done"!==e.status&&t.overdue++}})),r.value=t},I=e=>{const a={id:Date.now(),timestamp:(new Date).toISOString(),read:!1,...e};u.value.unshift(a),u.value.length>100&&(u.value=u.value.slice(0,50))},_=()=>{d.value={status:"",priority:"",assigneeId:"",assetId:"",locationId:"",dateRange:[]}};return{tasks:e,periodicSchedules:n,taskStats:r,loading:o,currentTask:i,notifications:u,filters:d,searchQuery:c,pagination:p,filteredTasks:m,tasksByStatus:v,overdueTasksCount:g,upcomingDeadlines:y,fetchTasks:k,getTaskById:async e=>{var a,t,s;try{const a=await l.getTaskById(e);if(a.success&&a.data)return i.value=a.data,a.data;throw new Error(a.message||"获取任务详情失败")}catch(n){throw 404===(null==(a=null==n?void 0:n.response)?void 0:a.status)?"undefined"!=typeof window&&window.ElMessage&&window.ElMessage.error("任务已被删除或不存在"):"undefined"!=typeof window&&window.ElMessage&&window.ElMessage.error("获取任务详情失败: "+((null==(s=null==(t=n.response)?void 0:t.data)?void 0:s.message)||n.message)),n}},createTask:async a=>{try{const t=await l.createTask(a);if(t.success)return e.value?e.value.unshift(t.data):e.value=[t.data],b(),I({type:"success",title:"任务创建成功",message:`任务 "${a.name}" 已创建`,taskId:t.data.taskId}),t.data;throw new Error(t.message||"创建任务失败")}catch(t){throw t}},updateTask:async(a,t)=>{var s,n,r;try{let o,u;if("object"==typeof a&&null!==a){const{taskId:e,...t}=a;o=e,u=t}else o=a,u=t||{};if(!o||0===o||"0"===o)throw new Error("任务ID不能为空或为0");const d=await l.updateTask(o,u);if(d.success){const a=(null==(s=e.value)?void 0:s.findIndex((e=>e&&e.taskId===o)))??-1;return-1!==a&&e.value&&Object.assign(e.value[a],d.data),(null==(n=i.value)?void 0:n.taskId)===o&&Object.assign(i.value,d.data),b(),I({type:"info",title:"任务更新成功",message:`任务 "${(null==(r=d.data)?void 0:r.name)||"未命名任务"}" 已更新`,taskId:o}),d.data}throw new Error(d.message||"更新任务失败")}catch(o){throw o}},deleteTask:f,updateTaskStatus:h,updateTaskProgress:async(a,t,s="")=>{var n;try{const r={progress:t,remarks:s},o=await l.updateTaskProgress(a,r);if(o.success){const t=(null==(n=e.value)?void 0:n.findIndex((e=>e&&e.taskId===a)))??-1;return-1!==t&&e.value&&Object.assign(e.value[t],o.data),b(),o.data}throw new Error(o.message||"更新任务进度失败")}catch(r){throw r}},assignTask:w,completeTask:async(a,t="")=>{var s;try{const n={remarks:t},r=await l.completeTask(a,n);if(r.success){const t=(null==(s=e.value)?void 0:s.findIndex((e=>e&&e.taskId===a)))??-1;return-1!==t&&e.value&&Object.assign(e.value[t],r.data),b(),I({type:"success",title:"任务完成",message:`任务 "${r.data.name}" 已完成`,taskId:a}),r.data}throw new Error(r.message||"完成任务失败")}catch(n){throw n}},batchUpdateStatus:async(e,a)=>{try{const t=e.map((e=>h(e,a)));return await Promise.all(t),I({type:"success",title:"批量状态更新成功",message:`${e.length} 个任务状态已更新为 "${a}"`}),!0}catch(t){throw t}},batchAssignTasks:async(e,a)=>{try{const t=e.map((e=>w(e,a)));return await Promise.all(t),I({type:"success",title:"批量分配成功",message:`${e.length} 个任务已成功分配`}),!0}catch(t){throw t}},batchDeleteTasks:async e=>{try{const a=e.map((e=>f(e)));return await Promise.all(a),I({type:"warning",title:"批量删除成功",message:`${e.length} 个任务已删除`}),!0}catch(a){throw a}},getTaskComments:async e=>{try{const a=await l.getTaskComments(e);if(a.success)return a.data||[];throw new Error(a.message||"获取评论失败")}catch(a){throw a}},addComment:async(a,t,s=[])=>{var n;try{const r=await l.addComment(a,{content:t,mentionedUserIds:s});if(r.success){const t=(null==(n=e.value)?void 0:n.findIndex((e=>e&&e.taskId===a)))??-1;return-1!==t&&e.value&&e.value[t]&&e.value[t].commentCount++,r.data}throw new Error(r.message||"添加评论失败")}catch(r){throw r}},getTaskAttachments:async e=>{try{const a=await l.getTaskAttachments(e);if(a.success)return a.data||[];throw new Error(a.message||"获取附件失败")}catch(a){throw a}},addAttachment:async(a,t,s="")=>{var n;try{const r=new FormData;r.append("file",t),s&&r.append("description",s);const o=await l.addAttachment(a,r);if(o.success){const t=(null==(n=e.value)?void 0:n.findIndex((e=>e&&e.taskId===a)))??-1;return-1!==t&&e.value&&e.value[t]&&e.value[t].attachmentCount++,o.data}throw new Error(o.message||"上传附件失败")}catch(r){throw r}},deleteAttachment:async e=>{try{const a=await l.deleteAttachment(e);if(a.success)return!0;throw new Error(a.message||"删除附件失败")}catch(a){throw a}},getTaskHistory:async e=>{try{const a=await l.getTaskHistory(e);if(a.success)return a.data||[];throw new Error(a.message||"获取历史记录失败")}catch(a){throw a}},getPeriodicSchedules:async(e={})=>{var a;try{const t=await l.getPeriodicSchedules(e);if(t.success)return n.value=(null==(a=t.data)?void 0:a.items)||[],t.data;throw new Error(t.message||"获取周期性任务计划失败")}catch(t){throw t}},createPeriodicSchedule:async e=>{try{const a=await l.createPeriodicSchedule(e);if(a.success)return n.value.unshift(a.data),a.data;throw new Error(a.message||"创建周期性任务计划失败")}catch(a){throw a}},addNotification:I,markNotificationAsRead:e=>{const a=u.value.find((a=>a.id===e));a&&(a.read=!0)},clearAllNotifications:()=>{u.value=[]},setFilters:e=>{d.value={...d.value,...e}},clearFilters:_,setSearchQuery:e=>{c.value=e},setPagination:e=>{p.value={...p.value,...e}},updateTaskInStore:a=>{var t;const l=(null==(t=e.value)?void 0:t.findIndex((e=>e&&e.taskId===a.taskId)))??-1;-1!==l&&e.value&&(e.value.splice(l,1,{...e.value[l],...a}),b())},forceReactiveUpdate:()=>{e.value=[...e.value],b()},$reset:()=>{e.value=[],n.value=[],r.value={total:0,completed:0,inProgress:0,overdue:0,todo:0},o.value=!1,i.value=null,u.value=[],_(),c.value="",p.value={pageNumber:1,pageSize:20,total:0}}}})),le={name:"EnhancedTaskCard",components:{More:f,Edit:k,User:y,DocumentCopy:g,Delete:v,Check:m,VideoPlay:p,VideoPause:c,Calendar:d,Flag:u,Paperclip:i,ChatLineRound:o,Medal:r,UserAvatar:Y,UserAvatarStack:X},props:{task:{type:Object,required:!0},selected:{type:Boolean,default:!1},selectable:{type:Boolean,default:!0},maxCollaboratorsVisible:{type:Number,default:3}},emits:["click","select","quickAction","statusChange"],setup(e,{emit:a}){const l=t((()=>e.task.collaborators?e.task.collaborators:e.task.assignees?e.task.assignees.filter((e=>"Primary"!==e.role)):[])),s=t((()=>l.value.slice(0,e.maxCollaboratorsVisible))),n=t((()=>Math.max(0,l.value.length-e.maxCollaboratorsVisible))),r=t((()=>!(!e.task.planEndDate||"Done"===e.task.status)&&new Date(e.task.planEndDate)<new Date)),o=t((()=>{if(!e.task.planEndDate||"Done"===e.task.status)return!1;const a=new Date(e.task.planEndDate),t=new Date,l=Math.ceil((a-t)/864e5);return l<=2&&l>=0}));return{collaborators:l,visibleCollaborators:s,hiddenCollaboratorsCount:n,isOverdue:r,isDueSoon:o,formatDueDate:e=>Z(e),truncateText:(e,a)=>!e||e.length<=a?e:e.substring(0,a)+"...",getPriorityTagType:e=>({Low:"info",Medium:"warning",High:"danger"}[e]||"info"),getPriorityText:e=>({Low:"低",Medium:"中",High:"高"}[e]||e),getTaskTypeText:e=>({Periodic:"周期任务",Emergency:"紧急任务",Maintenance:"维护任务"}[e]||e),getProgressColor:e=>e>=80?"#67c23a":e>=50?"#e6a23c":e>=20?"#409eff":"#f56c6c",handleQuickAction:t=>{a("quickAction",{action:t,task:e.task})},quickStatusChange:t=>{a("statusChange",{taskId:e.task.taskId,oldStatus:e.task.status,newStatus:t,task:e.task})},getAllAssignees:e=>{var a,t;if(!e)return[];const s=[],n=new Set;if(e.assigneeUserId){const l=e.assigneeAvatarUrl||e.assigneeAvatar||(null==(a=e.assignee)?void 0:a.avatarUrl)||(null==(t=e.assignee)?void 0:t.avatar)||"";s.push({id:e.assigneeUserId,userId:e.assigneeUserId,name:e.assigneeUserName||"未知用户",userName:e.assigneeUserName||"未知用户",avatarUrl:l,role:"Primary"}),n.add(e.assigneeUserId)}return e.assignees&&Array.isArray(e.assignees)&&e.assignees.length>0?e.assignees.forEach((a=>{var t,l;if(!a)return;const r=a.userId||a.id;if(!r||n.has(r))return;const o=a.avatarUrl||a.avatar||(null==(t=a.user)?void 0:t.avatarUrl)||(null==(l=a.user)?void 0:l.avatar)||"";s.push({id:r,userId:r,name:a.userName||a.name||"未知用户",userName:a.userName||a.name||"未知用户",avatarUrl:o,role:a.role||(r===e.assigneeUserId?"Primary":"Collaborator")}),n.add(r)})):l.value&&l.value.length>0?l.value.forEach((e=>{var a,t;if(!e)return;const l=e.userId||e.id;if(!l||n.has(l))return;const r=e.avatarUrl||e.avatar||e.userAvatarUrl||(null==(a=e.user)?void 0:a.avatarUrl)||(null==(t=e.user)?void 0:t.avatar)||"";s.push({id:l,userId:l,name:e.userName||e.name||"未知用户",userName:e.userName||e.name||"未知用户",avatarUrl:r,role:"Collaborator"}),n.add(l)})):e.participants&&Array.isArray(e.participants)&&e.participants.length>0&&e.participants.forEach((a=>{var t,l;if(!a)return;const r=a.id||a.userId;if(!r||n.has(r)||r===e.assigneeUserId)return;const o=a.avatarUrl||a.avatar||(null==(t=a.user)?void 0:t.avatarUrl)||(null==(l=a.user)?void 0:l.avatar)||"";s.push({id:r,userId:r,name:a.name||a.userName||"未知用户",userName:a.name||a.userName||"未知用户",avatarUrl:o,role:"Collaborator"}),n.add(r)})),s.length,s}}}},se={class:"task-header"},ne={class:"task-title-row"},re={class:"task-title-container"},oe=["title"],ie={class:"watermark-text-card"},ue={class:"task-actions"},de={key:0,class:"task-description"},ce={class:"assignee-section"},pe={key:1,class:"task-progress"},me={class:"progress-header"},ve={class:"progress-value"},ge={class:"task-meta"},ye={class:"meta-row"},ke={key:0,class:"due-date"},fe={key:1,class:"task-type"},he={class:"engagement-stats"},we={key:0,class:"stat-item"},be={key:1,class:"stat-item"},Ie={key:2,class:"stat-item points"},_e={class:"quick-status-actions"};const Ve=n(le,[["render",function(e,a,t,l,s,n){var r,o;const i=h("el-checkbox"),u=h("el-tag"),d=h("More"),c=h("el-icon"),p=h("el-button"),m=h("Edit"),v=h("el-dropdown-item"),g=h("User"),y=h("DocumentCopy"),k=h("Delete"),f=h("el-dropdown-menu"),A=h("el-dropdown"),N=h("UserAvatarStack"),P=h("el-progress"),z=h("Calendar"),L=h("Flag"),M=h("Paperclip"),B=h("ChatLineRound"),$=h("Medal"),O=h("Check"),F=h("VideoPlay"),j=h("VideoPause"),H=h("el-button-group");return b(),w("div",{class:E(["enhanced-task-card",[`priority-${null==(r=t.task.priority)?void 0:r.toLowerCase()}`,`status-${null==(o=t.task.status)?void 0:o.toLowerCase()}`,{selected:t.selected,overdue:l.isOverdue,"due-soon":l.isDueSoon}]]),onClick:a[5]||(a[5]=a=>e.$emit("click",t.task))},[t.selectable?(b(),w("div",{key:0,class:"task-select",onClick:a[1]||(a[1]=V((()=>{}),["stop"]))},[U(i,{"model-value":t.selected,onChange:a[0]||(a[0]=a=>e.$emit("select",t.task.taskId,a))},null,8,["model-value"])])):I("",!0),_("div",se,[_("div",ne,[_("div",re,[_("h4",{class:"task-title",title:t.task.name},D(t.task.name),9,oe),"Done"===t.task.status&&t.task.completedByUserName?(b(),w("div",{key:0,class:"completion-watermark-card",style:T({"--watermark-color":t.task.completionWatermarkColor||"#409EFF"})},[_("span",ie,D(t.task.completedByUserName),1)],4)):I("",!0)]),_("div",ue,[U(u,{type:l.getPriorityTagType(t.task.priority),size:"small",class:"priority-tag"},{default:C((()=>[S(D(l.getPriorityText(t.task.priority)),1)])),_:1},8,["type"]),U(A,{onCommand:l.handleQuickAction,trigger:"click"},{dropdown:C((()=>[U(f,null,{default:C((()=>[U(v,{command:"edit"},{default:C((()=>[U(c,null,{default:C((()=>[U(m)])),_:1}),a[6]||(a[6]=S(" 编辑 "))])),_:1}),U(v,{command:"assign"},{default:C((()=>[U(c,null,{default:C((()=>[U(g)])),_:1}),a[7]||(a[7]=S(" 重新分配 "))])),_:1}),U(v,{command:"clone"},{default:C((()=>[U(c,null,{default:C((()=>[U(y)])),_:1}),a[8]||(a[8]=S(" 克隆任务 "))])),_:1}),U(v,{command:"delete",divided:""},{default:C((()=>[U(c,null,{default:C((()=>[U(k)])),_:1}),a[9]||(a[9]=S(" 删除 "))])),_:1})])),_:1})])),default:C((()=>[U(p,{type:"text",size:"small",class:"action-btn"},{default:C((()=>[U(c,null,{default:C((()=>[U(d)])),_:1})])),_:1})])),_:1},8,["onCommand"])])]),t.task.description?(b(),w("p",de,D(l.truncateText(t.task.description,80)),1)):I("",!0)]),_("div",ce,[U(N,{users:l.getAllAssignees(t.task),"is-main-user-primary":!0,"max-users":4,"avatar-size":"8",overlap:-12,class:"small"},null,8,["users"])]),void 0!==t.task.progress?(b(),w("div",pe,[_("div",me,[a[10]||(a[10]=_("span",{class:"progress-label"},"进度",-1)),_("span",ve,D(t.task.progress)+"%",1)]),U(P,{percentage:t.task.progress,"stroke-width":4,"show-text":!1,color:l.getProgressColor(t.task.progress)},null,8,["percentage","color"])])):I("",!0),_("div",ge,[_("div",ye,[t.task.planEndDate?(b(),w("div",ke,[U(c,null,{default:C((()=>[U(z)])),_:1}),_("span",{class:E({"overdue-text":l.isOverdue,"due-soon-text":l.isDueSoon})},D(l.formatDueDate(t.task.planEndDate)),3)])):I("",!0),t.task.taskType&&"Normal"!==t.task.taskType?(b(),w("div",fe,[U(c,null,{default:C((()=>[U(L)])),_:1}),_("span",null,D(l.getTaskTypeText(t.task.taskType)),1)])):I("",!0)]),_("div",he,[t.task.attachmentCount>0?(b(),w("div",we,[U(c,null,{default:C((()=>[U(M)])),_:1}),_("span",null,D(t.task.attachmentCount),1)])):I("",!0),t.task.commentCount>0?(b(),w("div",be,[U(c,null,{default:C((()=>[U(B)])),_:1}),_("span",null,D(t.task.commentCount),1)])):I("",!0),t.task.points>0?(b(),w("div",Ie,[U(c,null,{default:C((()=>[U($)])),_:1}),_("span",null,D(t.task.points),1)])):I("",!0)])]),_("div",_e,[U(H,{size:"small"},{default:C((()=>["Done"!==t.task.status?(b(),x(p,{key:0,onClick:a[2]||(a[2]=V((e=>l.quickStatusChange("Done")),["stop"])),type:"success",size:"small"},{default:C((()=>[U(c,null,{default:C((()=>[U(O)])),_:1}),a[11]||(a[11]=S(" 完成 "))])),_:1})):I("",!0),"Todo"===t.task.status?(b(),x(p,{key:1,onClick:a[3]||(a[3]=V((e=>l.quickStatusChange("InProgress")),["stop"])),type:"primary",size:"small"},{default:C((()=>[U(c,null,{default:C((()=>[U(F)])),_:1}),a[12]||(a[12]=S(" 开始 "))])),_:1})):I("",!0),"InProgress"===t.task.status?(b(),x(p,{key:2,onClick:a[4]||(a[4]=V((e=>l.quickStatusChange("Todo")),["stop"])),type:"warning",size:"small"},{default:C((()=>[U(c,null,{default:C((()=>[U(j)])),_:1}),a[13]||(a[13]=S(" 暂停 "))])),_:1})):I("",!0)])),_:1})])],2)}],["__scopeId","data-v-a944f64c"]]),Ue={name:"TaskSelect",props:{modelValue:{type:[String,Number,Array],default:null},multiple:{type:Boolean,default:!1},placeholder:{type:String,default:"请选择任务"},excludeCompleted:{type:Boolean,default:!1}},emits:["update:modelValue","change"],setup(e,{emit:t}){const s=a(e.modelValue),n=a([]),r=a(!1);return P((()=>e.modelValue),(e=>{s.value=e})),{selectedValue:s,tasks:n,loading:r,searchTasks:async a=>{var t;if(a){r.value=!0;try{const s={search:a,pageSize:20};e.excludeCompleted&&(s.excludeStatus="Completed");const r=await l.getTasks(s);n.value=(null==(t=r.data)?void 0:t.items)||[]}catch(s){n.value=[]}finally{r.value=!1}}else n.value=[]},handleChange:e=>{t("update:modelValue",e),t("change",e)},getPriorityType:e=>({High:"danger",Medium:"warning",Low:"success"}[e]||"info"),getPriorityLabel:e=>({High:"高",Medium:"中",Low:"低"}[e]||e),getStatusType:e=>({Pending:"info",InProgress:"warning",Completed:"success",Cancelled:"danger"}[e]||"info"),getStatusLabel:e=>({Pending:"待处理",InProgress:"进行中",Completed:"已完成",Cancelled:"已取消"}[e]||e),formatDate:e=>e?new Date(e).toLocaleDateString("zh-CN"):""}}},De={class:"task-option"},Te={class:"task-info"},Ce={class:"task-title"},Se={class:"task-meta"},Ee={key:0,class:"task-assignee"},xe={key:1,class:"task-date"};const Ae=n(Ue,[["render",function(e,a,t,l,s,n){const r=h("el-tag"),o=h("el-option"),i=h("el-select");return b(),x(i,{modelValue:l.selectedValue,"onUpdate:modelValue":a[0]||(a[0]=e=>l.selectedValue=e),placeholder:t.placeholder,multiple:t.multiple,filterable:!0,remote:!0,"remote-method":l.searchTasks,loading:l.loading,clearable:!0,onChange:l.handleChange},{default:C((()=>[(b(!0),w(A,null,N(l.tasks,(e=>(b(),x(o,{key:e.id,label:e.title,value:e.id},{default:C((()=>[_("div",De,[_("div",Te,[_("span",Ce,D(e.title),1),U(r,{type:l.getPriorityType(e.priority),size:"small"},{default:C((()=>[S(D(l.getPriorityLabel(e.priority)),1)])),_:2},1032,["type"]),U(r,{type:l.getStatusType(e.status),size:"small"},{default:C((()=>[S(D(l.getStatusLabel(e.status)),1)])),_:2},1032,["type"])]),_("div",Se,[e.assigneeName?(b(),w("span",Ee,D(e.assigneeName),1)):I("",!0),e.dueDate?(b(),w("span",xe,D(l.formatDate(e.dueDate)),1)):I("",!0)])])])),_:2},1032,["label","value"])))),128))])),_:1},8,["modelValue","placeholder","multiple","remote-method","loading","onChange"])}],["__scopeId","data-v-d1e68401"]]),Ne={class:"tag-input"},Pe={class:"tags"},ze=["onClick"],Le=n({__name:"TagInput",props:{modelValue:{type:Array,default:()=>[]}},emits:["update:modelValue"],setup(e,{emit:t}){const l=e,s=t,n=a("");function r(){const e=n.value.trim();e&&!l.modelValue.includes(e)&&s("update:modelValue",[...l.modelValue,e]),n.value=""}return(a,t)=>(b(),w("div",Ne,[_("div",Pe,[(b(!0),w(A,null,N(e.modelValue,((e,a)=>(b(),w("span",{key:a,class:"tag"},[S(D(e)+" ",1),_("span",{class:"remove",onClick:e=>function(e){const a=[...l.modelValue];a.splice(e,1),s("update:modelValue",a)}(a)},"×",8,ze)])))),128)),z(_("input",{"onUpdate:modelValue":t[0]||(t[0]=e=>n.value=e),onKeyup:M(r,["enter"]),onBlur:r,placeholder:"添加标签",class:"tag-input-box"},null,544),[[L,n.value]])])]))}},[["__scopeId","data-v-6a2723e7"]]),Me={class:"task-comments"},Be={class:"comments-header"},$e={key:0,class:"comments-list"},Oe={class:"comment-user"},Fe={class:"comment-meta"},je={class:"comment-header"},He={class:"user-name"},Re={class:"comment-time"},qe={class:"comment-content"},Ke={key:0,class:"edit-indicator"},Qe={key:1,class:"empty-comments"},Ge={class:"add-comment"},Je={class:"comment-actions"},We=n({__name:"TaskComments",props:{taskId:{type:[String,Number],required:!0},comments:{type:Array,default:()=>[]}},emits:["add-comment","load-comments"],setup(e,{emit:n}){const r=e,o=n,i=a(""),u=a(!1),d=t((()=>r.comments||[])),c=e=>{if(!e)return"";const a=new Date(e),t=new Date-a;return t<6e4?"刚刚":t<36e5?`${Math.floor(t/6e4)}分钟前`:t<864e5?`${Math.floor(t/36e5)}小时前`:a.toLocaleDateString("zh-CN",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"})};async function p(){if(!i.value.trim())return void s.warning("请输入评论内容");if(u.value)return void s.warning("正在提交中，请稍候...");u.value=!0;const e=i.value.trim();try{await l.addComment(r.taskId,{content:e}),o("add-comment",{content:e}),i.value="",s.success("评论发表成功")}catch(a){s.error("评论发表失败: "+(a.message||"未知错误"))}finally{setTimeout((()=>{u.value=!1}),1e3)}}return P((()=>r.taskId),(async function(){if(r.taskId)try{o("load-comments")}catch(e){}}),{immediate:!0}),(e,a)=>{const t=h("el-avatar"),l=h("el-input"),s=h("el-button");return b(),w("div",Me,[_("div",Be,[_("h4",null,"评论 ("+D(d.value.length)+")",1)]),d.value.length?(b(),w("div",$e,[(b(!0),w(A,null,N(d.value,(e=>(b(),w("div",{key:e.commentId,class:"comment-item"},[_("div",Oe,[U(t,{src:e.userAvatarUrl,size:32,class:"user-avatar"},{default:C((()=>[S(D(e.userName?e.userName.charAt(0).toUpperCase():"?"),1)])),_:2},1032,["src"]),_("div",Fe,[_("div",je,[_("span",He,D(e.userName||"未知用户"),1),_("span",Re,D(c(e.creationTimestamp)),1)])])]),_("div",qe,D(e.content),1),e.isEdited?(b(),w("div",Ke,"已编辑")):I("",!0)])))),128))])):(b(),w("div",Qe,a[1]||(a[1]=[_("div",{class:"empty-icon"},"💬",-1),_("div",{class:"empty-text"},"暂无评论",-1)]))),_("div",Ge,[U(l,{modelValue:i.value,"onUpdate:modelValue":a[0]||(a[0]=e=>i.value=e),type:"textarea",rows:3,placeholder:"写下你的评论...",maxlength:"2000","show-word-limit":"",onKeyup:M(V(p,["ctrl"]),["enter"])},null,8,["modelValue","onKeyup"]),_("div",Je,[U(s,{type:"primary",size:"small",onClick:p,loading:u.value},{default:C((()=>a[2]||(a[2]=[S(" 发表评论 ")]))),_:1},8,["loading"]),a[3]||(a[3]=_("div",{class:"comment-tip"},"Ctrl + Enter 快速发送",-1))])])])}}},[["__scopeId","data-v-48657d21"]]),Xe={name:"FileUpload",components:{Plus:O,UploadFilled:$,Document:B},props:{modelValue:{type:Array,default:()=>[]},multiple:{type:Boolean,default:!0},accept:{type:String,default:"*"},limit:{type:Number,default:10},maxSize:{type:Number,default:10485760},autoUpload:{type:Boolean,default:!0},showFileList:{type:Boolean,default:!1},drag:{type:Boolean,default:!1},buttonText:{type:String,default:"选择文件"},tipText:{type:String,default:""},uploadPath:{type:String,default:"tasks"}},emits:["update:modelValue","change","upload-success","upload-error"],setup(e,{emit:l}){const n=H(),r=a(),o=a(!1),i=a([]),u=t((()=>"/api/files/upload")),d=t((()=>({Authorization:`Bearer ${n.token}`}))),c=t((()=>({path:e.uploadPath}))),p=t((()=>!!e.tipText)),m=t((()=>e.modelValue||[])),v=e=>{if(!e)return"0 B";const a=["B","KB","MB","GB"];let t=0;for(;e>=1024&&t<a.length-1;)e/=1024,t++;return`${e.toFixed(1)} ${a[t]}`};return P((()=>e.modelValue),(e=>{i.value=(e||[]).map((e=>({name:e.name,url:e.url,uid:e.uid||e.id})))}),{immediate:!0}),{uploadRef:r,uploading:o,fileList:i,uploadUrl:u,uploadHeaders:d,uploadData:c,showTip:p,previewFiles:m,beforeUpload:a=>a.size>e.maxSize?(s.error(`文件大小不能超过 ${v(e.maxSize)}`),!1):(o.value=!0,!0),onSuccess:(a,t)=>{var n,r;o.value=!1;const i={id:(null==(n=a.data)?void 0:n.id)||Date.now(),name:t.name,size:t.size,url:null==(r=a.data)?void 0:r.url,type:t.type,uid:t.uid},u=[...e.modelValue||[],i];l("update:modelValue",u),l("change",u),l("upload-success",i,a),s.success("文件上传成功")},onError:(e,a)=>{o.value=!1,l("upload-error",e,a),s.error("文件上传失败")},onRemove:a=>{const t=(e.modelValue||[]).filter((e=>e.uid!==a.uid));l("update:modelValue",t),l("change",t)},onExceed:()=>{s.warning(`最多只能上传 ${e.limit} 个文件`)},removeFile:a=>{const t=(e.modelValue||[]).filter((e=>e.id&&e.id!==a.id||e.uid&&e.uid!==a.uid));l("update:modelValue",t),l("change",t)},previewFile:e=>{e.url&&window.open(e.url,"_blank")},downloadFile:e=>{if(e.url){const a=document.createElement("a");a.href=e.url,a.download=e.name,a.click()}},formatFileSize:v}}},Ye={class:"file-upload"},Ze={class:"el-upload__tip"},ea={key:0,class:"file-preview"},aa={class:"file-info"},ta={class:"file-name"},la={class:"file-size"},sa={class:"file-actions"};const na=n(Xe,[["render",function(e,a,t,l,s,n){const r=h("upload-filled"),o=h("el-icon"),i=h("plus"),u=h("el-button"),d=h("el-upload"),c=h("document");return b(),w("div",Ye,[U(d,{ref:"uploadRef",action:l.uploadUrl,headers:l.uploadHeaders,data:l.uploadData,multiple:t.multiple,accept:t.accept,limit:t.limit,"file-list":l.fileList,"before-upload":l.beforeUpload,"on-success":l.onSuccess,"on-error":l.onError,"on-remove":l.onRemove,"on-exceed":l.onExceed,"auto-upload":t.autoUpload,"show-file-list":t.showFileList,drag:t.drag,class:"upload-component"},F({default:C((()=>[t.drag?(b(),w(A,{key:0},[U(o,{class:"el-icon--upload"},{default:C((()=>[U(r)])),_:1}),a[0]||(a[0]=_("div",{class:"el-upload__text"},[S(" 将文件拖拽到此处，或"),_("em",null,"点击上传")],-1))],64)):(b(),x(u,{key:1,type:"primary",loading:l.uploading},{default:C((()=>[U(o,null,{default:C((()=>[U(i)])),_:1}),S(" "+D(t.buttonText),1)])),_:1},8,["loading"]))])),_:2},[l.showTip?{name:"tip",fn:C((()=>[_("div",Ze,[j(e.$slots,"tip",{},(()=>[S(D(t.tipText),1)]),!0)])])),key:"0"}:void 0]),1032,["action","headers","data","multiple","accept","limit","file-list","before-upload","on-success","on-error","on-remove","on-exceed","auto-upload","show-file-list","drag"]),l.previewFiles.length>0?(b(),w("div",ea,[(b(!0),w(A,null,N(l.previewFiles,(e=>(b(),w("div",{key:e.uid||e.id,class:"file-item"},[_("div",aa,[U(o,{class:"file-icon"},{default:C((()=>[U(c)])),_:1}),_("span",ta,D(e.name),1),_("span",la,D(l.formatFileSize(e.size)),1)]),_("div",sa,[e.url?(b(),x(u,{key:0,type:"text",size:"small",onClick:a=>l.previewFile(e)},{default:C((()=>a[1]||(a[1]=[S(" 预览 ")]))),_:2},1032,["onClick"])):I("",!0),e.url?(b(),x(u,{key:1,type:"text",size:"small",onClick:a=>l.downloadFile(e)},{default:C((()=>a[2]||(a[2]=[S(" 下载 ")]))),_:2},1032,["onClick"])):I("",!0),U(u,{type:"text",size:"small",onClick:a=>l.removeFile(e)},{default:C((()=>a[3]||(a[3]=[S(" 删除 ")]))),_:2},1032,["onClick"])])])))),128))])):I("",!0)])}],["__scopeId","data-v-bc4ec572"]]),ra={class:"task-attachments"},oa={key:0,class:"empty"},ia=n({__name:"TaskAttachments",props:{taskId:{type:[String,Number],required:!0}},setup(e){const t=e,l=a([]);return P((()=>t.taskId),(e=>{l.value=[]})),(a,t)=>(b(),w("div",ra,[t[1]||(t[1]=_("h3",null,"任务附件",-1)),U(na,{modelValue:l.value,"onUpdate:modelValue":t[0]||(t[0]=e=>l.value=e),uploadPath:`tasks/${e.taskId}/attachments`,"show-file-list":!0,multiple:!0,"button-text":"上传附件","tip-text":"支持多文件上传，单个文件不超过10MB"},null,8,["modelValue","uploadPath"]),0===l.value.length?(b(),w("div",oa,"暂无附件")):I("",!0)]))}},[["__scopeId","data-v-0a3a4afd"]]),ua={class:"task-history"},da={class:"history-header"},ca={key:0,class:"history-list"},pa={class:"history-user"},ma={class:"history-meta"},va={class:"history-info"},ga={class:"user-name"},ya={class:"action-type"},ka={class:"history-time"},fa={class:"history-description"},ha={key:0,class:"field-changes"},wa={class:"field-name"},ba={key:0,class:"old-value"},Ia={key:1,class:"arrow"},_a={key:2,class:"new-value"},Va={key:1,class:"empty-history"},Ua=n({__name:"TaskHistory",props:{taskId:{type:[String,Number],required:!0},history:{type:Array,default:()=>[]}},emits:["load-history"],setup(e,{emit:a}){const l=e,s=e=>{if(!e)return"";const a=new Date(e),t=new Date-a;return t<6e4?"刚刚":t<36e5?`${Math.floor(t/6e4)}分钟前`:t<864e5?`${Math.floor(t/36e5)}小时前`:a.toLocaleDateString("zh-CN",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"})},n=t((()=>l.history||[]));return(e,a)=>{const t=h("el-avatar");return b(),w("div",ua,[_("div",da,[_("h4",null,"操作历史 ("+D(n.value.length)+")",1)]),n.value.length?(b(),w("div",ca,[(b(!0),w(A,null,N(n.value,(e=>(b(),w("div",{key:e.taskHistoryId,class:"history-item"},[_("div",pa,[U(t,{src:e.userAvatarUrl,size:32,class:"user-avatar"},{default:C((()=>[S(D(e.userName?e.userName.charAt(0).toUpperCase():"?"),1)])),_:2},1032,["src"]),_("div",ma,[_("div",va,[_("span",ga,D(e.userName||"系统"),1),_("span",ya,D(e.formattedActionType||e.actionType),1),_("span",ka,D(s(e.timestamp)),1)]),_("div",fa,D(e.description),1),e.fieldName?(b(),w("div",ha,[_("span",wa,D(e.fieldName)+":",1),e.oldValue?(b(),w("span",ba,D(e.oldValue),1)):I("",!0),e.oldValue&&e.newValue?(b(),w("span",Ia," → ")):I("",!0),e.newValue?(b(),w("span",_a,D(e.newValue),1)):I("",!0)])):I("",!0)])])])))),128))])):(b(),w("div",Va,a[0]||(a[0]=[_("div",{class:"empty-icon"},"📋",-1),_("div",{class:"empty-text"},"暂无历史记录",-1)])))])}}},[["__scopeId","data-v-1d006189"]]),Da={key:0,class:"loading-container"},Ta={key:1,class:"task-detail-content"},Ca={class:"task-header"},Sa={class:"task-title-section"},Ea={class:"task-meta-info"},xa={key:0,class:"pdca-badge"},Aa={key:1,class:"overdue-indicator"},Na={class:"quick-actions"},Pa={class:"task-main-content"},za={class:"task-details-panel"},La={class:"task-details"},Ma={class:"detail-section"},Ba={key:0,class:"description-display"},$a=["innerHTML"],Oa={class:"detail-section"},Fa={class:"relation-grid"},ja={class:"relation-item"},Ha={key:0},Ra={key:1,class:"empty-value"},qa={class:"relation-item"},Ka={class:"detail-section"},Qa={class:"time-grid"},Ga={class:"time-item"},Ja={key:0},Wa={class:"time-item"},Xa={class:"time-item"},Ya={class:"time-item"},Za={class:"detail-section"},et={class:"progress-section"},at={class:"progress-display"},tt={class:"progress-controls"},lt={class:"progress-text"},st={key:0,class:"points-display"},nt={class:"task-properties-panel"},rt={class:"properties-card"},ot={class:"property-item"},it={key:0,class:"property-value"},ut={key:1,class:"empty-value"},dt={class:"property-item"},ct={key:0,class:"property-value"},pt={class:"property-item"},mt={key:0,class:"property-value"},vt={class:"property-item"},gt={key:0,class:"property-value"},yt={class:"property-item"},kt={class:"tags-display"},ft={class:"creation-info"},ht={class:"info-item"},wt={class:"creator-name"},bt={class:"info-item"},It={class:"info-item"},_t={class:"dialog-footer"},Vt={class:"footer-left"},Ut={class:"footer-right"},Dt=n({__name:"TaskDetailDialog",props:{modelValue:{type:Boolean,default:!1},task:{type:Object,default:null}},emits:["update:modelValue","save","delete","close","updated"],setup(e,{emit:n}){const r=e,o=n,i=te(),u=t({get:()=>r.modelValue,set:e=>o("update:modelValue",e)}),d=a(!1),c=a(!1),p=a(!1),m=a(!1),v=a("details"),g=a(null),y=R({}),k=R({}),f=a(!1),V=a(""),T=a(null),z=a([]),L=a([]),B=a([]),$=a(["Todo","InProgress","Done","Cancelled"]),O=a(["Low","Medium","High","Urgent"]),F=a(["Normal","Periodic","PDCA"]),j=t((()=>g.value&&(g.value.assigneeUserId===De()||g.value.creatorUserId===De())));P((()=>r.task),(e=>{e&&H(e)}),{immediate:!0}),P((()=>r.modelValue),(e=>{e&&r.task&&(H(r.task),pe(),me(),ve())}));const H=async e=>{if(e){d.value=!0;try{if("object"==typeof e&&e.taskId)g.value={...e};else{const a="number"==typeof e?e:e.taskId,t=await i.getTaskById(a);if(!t)return void o("close");g.value=t}G()}catch(a){s.error("加载任务详情失败: "+a.message),g.value=null,o("close")}finally{d.value=!1}}},G=()=>{if(!g.value)return;const e=Te(g.value).map((e=>e.userId)),a={name:g.value.name,description:g.value.description,status:g.value.status,priority:g.value.priority,taskType:g.value.taskType,assigneeUserId:g.value.assigneeUserId,assigneeUserIds:e.length>0?e:[g.value.assigneeUserId].filter(Boolean),planStartDate:g.value.planStartDate?new Date(g.value.planStartDate):null,planEndDate:g.value.planEndDate?new Date(g.value.planEndDate):null,progress:g.value.progress,parentTaskId:g.value.parentTaskId,tagList:[...g.value.tagList||[]],pdcaStage:g.value.pdcaStage,points:g.value.points};Object.assign(y,a),Object.assign(k,a)},J=()=>{m.value&&Object.assign(y,k),m.value=!m.value},W=async()=>{if(g.value&&g.value.taskId){c.value=!0;try{const e=g.value.taskId;if(0===e||"0"===e)throw new Error("任务ID不能为0");let a=null,t=[];Array.isArray(y.assigneeUserIds)&&y.assigneeUserIds.length>0&&(a=y.assigneeUserIds[0],t=y.assigneeUserIds.slice(1));const n={name:y.name,description:y.description,status:y.status,priority:y.priority,taskType:y.taskType,assigneeUserId:a,collaboratorUserIds:t,planStartDate:y.planStartDate?new Date(y.planStartDate).toISOString():null,planEndDate:y.planEndDate?new Date(y.planEndDate).toISOString():null,progress:y.progress,parentTaskId:y.parentTaskId,pdcaStage:y.pdcaStage,points:y.points,tagList:y.tagList},r=await l.updateTask(e,n);if(!r.success)throw new Error(r.message||"保存失败");g.value=r.data,G(),m.value=!1,s.success("任务更新成功"),o("save",r.data),o("updated",r.data)}catch(e){s.error("保存失败: "+e.message)}finally{c.value=!1}}else s.error("保存失败: 任务ID不能为空")},Z=async()=>{if(g.value){p.value=!0;try{const e=await i.completeTask(g.value.taskId);g.value=e,s.success("任务已完成"),o("save",e),o("updated",e)}catch(e){s.error("完成任务失败: "+e.message)}finally{p.value=!1}}},le=async e=>{switch(e){case"duplicate":await se();break;case"convert":await ne();break;case"archive":await re();break;case"delete":await oe()}},se=async()=>{try{const e={...y,name:g.value.name+" (副本)",status:"Todo",progress:0,actualStartDate:null,actualEndDate:null};delete e.taskId,await i.createTask(e),s.success("任务复制成功"),o("updated")}catch(e){s.error("复制任务失败: "+e.message)}},ne=async()=>{s.info("转换为模板功能开发中...")},re=async()=>{try{await i.updateTaskStatus(g.value.taskId,"Archived"),s.success("任务已归档"),o("updated"),be()}catch(e){s.error("归档失败: "+e.message)}},oe=async()=>{try{await Q.confirm("确定要删除这个任务吗？此操作不可恢复。","确认删除",{type:"warning"}),await i.deleteTask(g.value.taskId),s.success("任务删除成功"),o("delete",g.value.taskId),o("updated"),be()}catch(e){"cancel"!==e&&s.error("删除失败: "+e.message)}},ie=()=>{f.value=!0,V.value=g.value.name,K((()=>{var e;null==(e=T.value)||e.focus()}))},ue=async()=>{if(V.value.trim()&&V.value!==g.value.name)try{const e=g.value.taskId;if(!e||0===e||"0"===e)throw new Error("任务ID不能为空或为0");const a=await i.updateTask({taskId:e,name:V.value.trim()});g.value.name=a.name,s.success("标题更新成功"),o("updated",a)}catch(e){s.error("更新标题失败: "+e.message)}f.value=!1},de=()=>{f.value=!1,V.value=g.value.name},ce=async e=>{if(j.value)try{await i.updateTaskProgress(g.value.taskId,e),g.value.progress=e}catch(a){s.error("更新进度失败: "+a.message),y.progress=g.value.progress}},pe=async()=>{if(g.value)try{z.value=await i.getTaskComments(g.value.taskId)}catch(e){}},me=async()=>{if(g.value)try{L.value=await i.getTaskAttachments(g.value.taskId)}catch(e){}},ve=async()=>{var e;if(!(null==(e=r.task)?void 0:e.taskId))return;const a=await l.getTaskHistory(r.task.taskId);B.value=a.data||[]},ge=async e=>{try{const a=await i.addComment(g.value.taskId,e.content,e.mentionedUserIds);z.value.unshift(a),g.value.commentCount++}catch(a){s.error("添加评论失败: "+a.message)}},ye=async(e,a)=>{try{const t=await i.addAttachment(g.value.taskId,e,a);L.value.unshift(t),g.value.attachmentCount++}catch(t){s.error("上传附件失败: "+t.message)}},ke=async e=>{try{await i.deleteAttachment(e),L.value=L.value.filter((a=>a.attachmentId!==e)),g.value.attachmentCount--,s.success("附件删除成功")}catch(a){s.error("删除附件失败: "+a.message)}},fe=()=>{const e=`${window.location.origin}/tasks/${g.value.taskId}`;navigator.clipboard.writeText(e).then((()=>{s.success("链接已复制到剪贴板")}))},he=()=>{g.value.parentTaskId&&o("open-task",g.value.parentTaskId)},we=()=>{o("show-subtasks",g.value.taskId)},be=()=>{m.value=!1,f.value=!1,v.value="details",o("close")},Ie=e=>({Todo:"",InProgress:"warning",Done:"success",Cancelled:"info"}[e]||""),_e=e=>({High:"danger",Medium:"warning",Low:"success",Urgent:"danger"}[e]||""),Ve=e=>({High:"高",Medium:"中",Low:"低",Urgent:"紧急"}[e]||e),Ue=e=>({Todo:"待处理",InProgress:"进行中",Done:"已完成",Cancelled:"已取消"}[e]||e),De=()=>1,Te=e=>{if(!e)return[];if(e.assignees&&e.assignees.length>0)return e.assignees.map((e=>({id:e.userId,userId:e.userId,name:e.userName||"未知用户",userName:e.userName||"未知用户",avatarUrl:e.avatarUrl||"",role:e.role||("Responsible"===e.assignmentType?"Primary":"Collaborator"),assignmentType:e.assignmentType})));const a=[];return e.assigneeUserId&&a.push({id:e.assigneeUserId,userId:e.assigneeUserId,name:e.assigneeUserName||"未知用户",userName:e.assigneeUserName||"未知用户",avatarUrl:e.assigneeAvatarUrl||"",role:"Primary"}),e.participants&&e.participants.length>0&&e.participants.forEach((t=>{t&&(t.id||t.userId)!==e.assigneeUserId&&a.push({id:t.id||t.userId,userId:t.id||t.userId,name:t.name||t.userName||"未知用户",userName:t.name||t.userName||"未知用户",avatarUrl:t.avatarUrl||"",role:"Collaborator"})})),a};return(e,a)=>{const t=h("el-skeleton"),l=h("el-tag"),s=h("WarningFilled"),n=h("el-icon"),r=h("Edit"),o=h("el-input"),i=h("Check"),k=h("el-button"),P=h("ArrowDown"),H=h("el-dropdown-item"),R=h("el-dropdown-menu"),K=h("el-dropdown"),Q=h("el-button-group"),G=h("el-link"),te=h("el-badge"),se=h("el-date-picker"),ne=h("el-slider"),re=h("el-progress"),oe=h("Star"),De=h("el-tab-pane"),Ce=h("el-tabs"),Se=h("el-option"),Ee=h("el-select"),xe=h("Link"),Ne=h("el-dialog");return b(),x(Ne,{modelValue:u.value,"onUpdate:modelValue":a[13]||(a[13]=e=>u.value=e),title:m.value?"编辑任务":"任务详情",width:"70%","close-on-click-modal":!1,"destroy-on-close":!0,class:"task-detail-dialog",onClose:be},{footer:C((()=>[_("div",_t,[_("div",Vt,[g.value&&g.value.taskId?(b(),x(k,{key:0,type:"info",text:"",onClick:fe},{default:C((()=>[U(n,null,{default:C((()=>[U(xe)])),_:1}),a[45]||(a[45]=S(" 复制链接 "))])),_:1})):I("",!0)]),_("div",Ut,[U(k,{onClick:be},{default:C((()=>a[46]||(a[46]=[S("取消")]))),_:1}),m.value?(b(),x(k,{key:0,type:"primary",onClick:W,loading:c.value},{default:C((()=>a[47]||(a[47]=[S(" 保存更改 ")]))),_:1},8,["loading"])):I("",!0)])])])),default:C((()=>[d.value?(b(),w("div",Da,[U(t,{rows:8,animated:""})])):g.value?(b(),w("div",Ta,[_("div",Ca,[_("div",Sa,[_("div",Ea,[U(l,{type:Ie(g.value.status),size:"large",class:"status-tag"},{default:C((()=>[S(D(Ue(g.value.status)),1)])),_:1},8,["type"]),U(l,{type:_e(g.value.priority),size:"small",round:"",class:"priority-tag"},{default:C((()=>[S(D(Ve(g.value.priority)),1)])),_:1},8,["type"]),"PDCA"===g.value.taskType?(b(),w("span",xa," PDCA-"+D(g.value.pdcaStage),1)):I("",!0),g.value.isOverdue?(b(),w("span",Aa,[U(n,null,{default:C((()=>[U(s)])),_:1}),a[14]||(a[14]=S(" 已逾期 "))])):I("",!0)]),f.value?(b(),x(o,{key:1,modelValue:V.value,"onUpdate:modelValue":a[0]||(a[0]=e=>V.value=e),size:"large",onBlur:ue,onKeyup:[M(ue,["enter"]),M(de,["esc"])],ref_key:"titleInput",ref:T,class:"title-input"},null,8,["modelValue"])):(b(),w("h2",{key:0,class:"task-title",onDblclick:ie},[S(D(g.value.name)+" ",1),U(n,{class:"edit-icon"},{default:C((()=>[U(r)])),_:1})],32))]),_("div",Na,[U(Q,null,{default:C((()=>["Done"!==g.value.status?(b(),x(k,{key:0,type:"success",onClick:Z,loading:p.value},{default:C((()=>[U(n,null,{default:C((()=>[U(i)])),_:1}),a[15]||(a[15]=S(" 完成任务 "))])),_:1},8,["loading"])):I("",!0),U(k,{onClick:J},{default:C((()=>[U(n,null,{default:C((()=>[U(r)])),_:1}),S(" "+D(m.value?"取消编辑":"编辑"),1)])),_:1}),U(K,{onCommand:le},{dropdown:C((()=>[U(R,null,{default:C((()=>[U(H,{command:"duplicate"},{default:C((()=>a[17]||(a[17]=[S("复制任务")]))),_:1}),U(H,{command:"convert"},{default:C((()=>a[18]||(a[18]=[S("转换为模板")]))),_:1}),U(H,{command:"archive"},{default:C((()=>a[19]||(a[19]=[S("归档")]))),_:1}),U(H,{command:"delete",divided:""},{default:C((()=>a[20]||(a[20]=[_("span",{style:{color:"var(--el-color-danger)"}},"删除任务",-1)]))),_:1})])),_:1})])),default:C((()=>[U(k,null,{default:C((()=>[a[16]||(a[16]=S(" 更多操作")),U(n,{class:"el-icon--right"},{default:C((()=>[U(P)])),_:1})])),_:1})])),_:1})])),_:1})])]),_("div",Pa,[_("div",za,[U(Ce,{modelValue:v.value,"onUpdate:modelValue":a[7]||(a[7]=e=>v.value=e),class:"task-tabs"},{default:C((()=>[U(De,{label:"详情",name:"details"},{default:C((()=>{return[_("div",La,[_("div",Ma,[a[21]||(a[21]=_("h4",{class:"section-title"},"任务描述",-1)),m.value?(b(),x(o,{key:1,modelValue:y.description,"onUpdate:modelValue":a[2]||(a[2]=e=>y.description=e),type:"textarea",rows:6,placeholder:"输入任务描述，支持Markdown格式...",class:"description-editor"},null,8,["modelValue"])):(b(),w("div",Ba,[g.value.description?(b(),w("div",{key:0,innerHTML:(t=g.value.description,(null==t?void 0:t.replace(/\n/g,"<br>"))||"")},null,8,$a)):(b(),w("div",{key:1,class:"empty-description",onClick:a[1]||(a[1]=e=>m.value=!0)}," 点击添加描述... "))]))]),_("div",Oa,[a[25]||(a[25]=_("h4",{class:"section-title"},"关联信息",-1)),_("div",Fa,[_("div",ja,[a[22]||(a[22]=_("label",null,"父任务:",-1)),m.value?(b(),x(Ae,{key:1,modelValue:y.parentTaskId,"onUpdate:modelValue":a[3]||(a[3]=e=>y.parentTaskId=e)},null,8,["modelValue"])):(b(),w("div",Ha,[g.value.parentTaskName?(b(),x(G,{key:0,type:"primary",onClick:he},{default:C((()=>[S(D(g.value.parentTaskName),1)])),_:1})):(b(),w("span",Ra,"无"))]))]),_("div",qa,[a[24]||(a[24]=_("label",null,"子任务:",-1)),_("div",null,[U(te,{value:g.value.subTaskCount,class:"item"},{default:C((()=>[U(k,{size:"small",text:"",onClick:we},{default:C((()=>a[23]||(a[23]=[S(" 查看子任务 ")]))),_:1})])),_:1},8,["value"])])])])]),_("div",Ka,[a[30]||(a[30]=_("h4",{class:"section-title"},"时间信息",-1)),_("div",Qa,[_("div",Ga,[a[26]||(a[26]=_("label",null,"计划开始:",-1)),m.value?(b(),x(se,{key:1,modelValue:y.planStartDate,"onUpdate:modelValue":a[4]||(a[4]=e=>y.planStartDate=e),type:"datetime",placeholder:"选择开始时间",size:"small"},null,8,["modelValue"])):(b(),w("div",Ja,D(q(ee)(g.value.planStartDate)||"未设置"),1))]),_("div",Wa,[a[27]||(a[27]=_("label",null,"计划结束:",-1)),m.value?(b(),x(se,{key:1,modelValue:y.planEndDate,"onUpdate:modelValue":a[5]||(a[5]=e=>y.planEndDate=e),type:"datetime",placeholder:"选择结束时间",size:"small"},null,8,["modelValue"])):(b(),w("div",{key:0,class:E({"overdue-date":g.value.isOverdue})},D(q(ee)(g.value.planEndDate)||"未设置"),3))]),_("div",Xa,[a[28]||(a[28]=_("label",null,"实际开始:",-1)),_("div",null,D(q(ee)(g.value.actualStartDate)||"未开始"),1)]),_("div",Ya,[a[29]||(a[29]=_("label",null,"实际结束:",-1)),_("div",null,D(q(ee)(g.value.actualEndDate)||"未完成"),1)])])]),_("div",Za,[a[32]||(a[32]=_("h4",{class:"section-title"},"进度跟踪",-1)),_("div",et,[_("div",at,[a[31]||(a[31]=_("span",{class:"progress-label"},"完成进度:",-1)),_("div",tt,[m.value||j.value?(b(),x(ne,{key:0,modelValue:y.progress,"onUpdate:modelValue":a[6]||(a[6]=e=>y.progress=e),disabled:!m.value&&!j.value,onChange:ce,class:"progress-slider"},null,8,["modelValue","disabled"])):(b(),x(re,{key:1,percentage:g.value.progress,"stroke-width":12,color:(e=g.value.progress,e>=80?"#67c23a":e>=50?"#e6a23c":"#f56c6c")},null,8,["percentage","color"])),_("span",lt,D(g.value.progress)+"%",1)])]),g.value.points>0?(b(),w("div",st,[U(n,null,{default:C((()=>[U(oe)])),_:1}),_("span",null,"完成可获得 "+D(g.value.points)+" 积分",1)])):I("",!0)])])])];var e,t})),_:1}),U(De,{name:"comments"},{label:C((()=>[a[33]||(a[33]=S(" 评论 ")),g.value.commentCount>0?(b(),x(te,{key:0,value:g.value.commentCount,class:"item"},null,8,["value"])):I("",!0)])),default:C((()=>[U(We,{"task-id":g.value.taskId,comments:z.value,onAddComment:ge,onLoadComments:pe},null,8,["task-id","comments"])])),_:1}),U(De,{name:"attachments"},{label:C((()=>[a[34]||(a[34]=S(" 附件 ")),g.value.attachmentCount>0?(b(),x(te,{key:0,value:g.value.attachmentCount,class:"item"},null,8,["value"])):I("",!0)])),default:C((()=>[U(ia,{"task-id":g.value.taskId,attachments:L.value,onUploadAttachment:ye,onDeleteAttachment:ke,onLoadAttachments:me},null,8,["task-id","attachments"])])),_:1}),U(De,{label:"历史",name:"history"},{default:C((()=>[U(Ua,{"task-id":g.value.taskId,history:B.value,onLoadHistory:ve},null,8,["task-id","history"])])),_:1})])),_:1},8,["modelValue"])]),_("div",nt,[_("div",rt,[a[44]||(a[44]=_("h4",{class:"panel-title"},"任务属性",-1)),_("div",ot,[a[35]||(a[35]=_("label",{class:"property-label"},"负责人",-1)),m.value?(b(),x(ae,{key:1,modelValue:y.assigneeUserIds,"onUpdate:modelValue":a[8]||(a[8]=e=>y.assigneeUserIds=e),multiple:!0},null,8,["modelValue"])):(b(),w("div",it,[Te(g.value).length>0?(b(),x(X,{key:0,users:Te(g.value),"is-main-user-primary":!0,"max-users":5,"show-details":!0,"avatar-size":"24",overlap:8},null,8,["users"])):(b(),w("span",ut,"未分配"))]))]),_("div",dt,[a[36]||(a[36]=_("label",{class:"property-label"},"状态",-1)),m.value?(b(),x(Ee,{key:1,modelValue:y.status,"onUpdate:modelValue":a[9]||(a[9]=e=>y.status=e),size:"small"},{default:C((()=>[(b(!0),w(A,null,N($.value,(e=>(b(),x(Se,{key:e,label:Ue(e),value:e},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])):(b(),w("div",ct,[U(l,{type:Ie(g.value.status)},{default:C((()=>[S(D(Ue(g.value.status)),1)])),_:1},8,["type"])]))]),_("div",pt,[a[37]||(a[37]=_("label",{class:"property-label"},"优先级",-1)),m.value?(b(),x(Ee,{key:1,modelValue:y.priority,"onUpdate:modelValue":a[10]||(a[10]=e=>y.priority=e),size:"small"},{default:C((()=>[(b(!0),w(A,null,N(O.value,(e=>(b(),x(Se,{key:e,label:Ve(e),value:e},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])):(b(),w("div",mt,[U(l,{type:_e(g.value.priority),size:"small"},{default:C((()=>[S(D(Ve(g.value.priority)),1)])),_:1},8,["type"])]))]),_("div",vt,[a[38]||(a[38]=_("label",{class:"property-label"},"类型",-1)),m.value?(b(),x(Ee,{key:1,modelValue:y.taskType,"onUpdate:modelValue":a[11]||(a[11]=e=>y.taskType=e),size:"small"},{default:C((()=>[(b(!0),w(A,null,N(F.value,(e=>(b(),x(Se,{key:e,label:e,value:e},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])):(b(),w("div",gt,[U(l,{type:"info",size:"small"},{default:C((()=>[S(D(g.value.taskType),1)])),_:1})]))]),_("div",yt,[a[39]||(a[39]=_("label",{class:"property-label"},"标签",-1)),_("div",kt,[m.value?(b(),x(Le,{key:1,modelValue:y.tagList,"onUpdate:modelValue":a[12]||(a[12]=e=>y.tagList=e)},null,8,["modelValue"])):(b(!0),w(A,{key:0},N(g.value.tagList,(e=>(b(),x(l,{key:e,size:"small",closable:"",onClose:a=>(async e=>{if(!m.value)return;const a=y.tagList.indexOf(e);a>-1&&y.tagList.splice(a,1)})(e)},{default:C((()=>[S(D(e),1)])),_:2},1032,["onClose"])))),128))])]),_("div",ft,[a[43]||(a[43]=_("h4",{class:"panel-title"},"创建信息",-1)),_("div",ht,[a[40]||(a[40]=_("span",{class:"info-label"},"创建者:",-1)),U(Y,{"user-id":g.value.creatorUserId,"user-name":g.value.creatorUserName,"avatar-url":g.value.creatorUserAvatarUrl,size:"small"},null,8,["user-id","user-name","avatar-url"]),_("span",wt,D(g.value.creatorUserName),1)]),_("div",bt,[a[41]||(a[41]=_("span",{class:"info-label"},"创建时间:",-1)),_("span",null,D(q(ee)(g.value.creationTimestamp)),1)]),_("div",It,[a[42]||(a[42]=_("span",{class:"info-label"},"最后更新:",-1)),_("span",null,D(q(ee)(g.value.lastUpdatedTimestamp)),1)])])])])])])):I("",!0)])),_:1},8,["modelValue","title"])}}},[["__scopeId","data-v-d5036ff5"]]),Tt={name:"TaskFormDialog",props:{visible:{type:Boolean,default:!1},isEdit:{type:Boolean,default:!1},formData:{type:Object,default:()=>({})}},setup(e,{emit:t}){const l=a(e.visible),n=a(null),r=H(),o=a(!1),i=a([]),u=R({name:"",description:"",priority:"Medium",assigneeUserIds:[],planStartDate:"",planEndDate:"",taskType:"Normal"});P((()=>e.visible),(e=>{l.value=e})),P(l,(e=>{e||t("update:visible",!1)})),P((()=>e.formData),(a=>{(a=>{if(!a){if(!e.isEdit){const e=new Date,a=new Date;a.setMonth(e.getMonth()+1),u.planStartDate=e,u.planEndDate=a}return}const t={...a},l=[];if(t.assigneeUserId&&l.push(t.assigneeUserId),t.collaboratorUserIds&&Array.isArray(t.collaboratorUserIds))l.push(...t.collaboratorUserIds);else if(t.assignees&&Array.isArray(t.assignees)){const e=t.assignees.filter((e=>"Participant"===e.assignmentType)).map((e=>e.userId||e.id));l.push(...e)}t.assigneeUserIds=[...new Set(l)],t.planStartDate&&(t.planStartDate=new Date(t.planStartDate)),t.planEndDate&&(t.planEndDate=new Date(t.planEndDate)),Object.assign(u,t)})(a)}),{immediate:!0,deep:!0});return G((()=>{if((async()=>{o.value=!0;try{const e=await J.getUserList();e&&e.data?i.value=e.data.map((e=>{if(!e)return null;const a={id:e.id||e.userId||e.ID||0,name:e.name||e.userName||e.username||e.displayName||"未知用户",department:e.department||e.departmentName||"",avatarUrl:e.avatarUrl||""};return a.id&&0!==a.id?a:null})).filter((e=>null!==e&&e.id>0)):i.value=[]}catch(e){s.error("获取用户列表失败，使用测试数据"),i.value=[{id:1,name:"张三",department:"部门A",avatarUrl:"https://example.com/avatar1.jpg"},{id:2,name:"李四",department:"部门B",avatarUrl:"https://example.com/avatar2.jpg"},{id:3,name:"王五",department:"部门C",avatarUrl:"https://example.com/avatar3.jpg"}]}finally{o.value=!1}})(),!e.isEdit){r.userInfo&&r.userInfo.id&&(u.assigneeUserIds=[r.userInfo.id]);const e=new Date,a=new Date;a.setMonth(e.getMonth()+1),u.planStartDate=e,u.planEndDate=a}})),{dialogVisible:l,formRef:n,form:u,rules:{name:[{required:!0,message:"请输入任务名称",trigger:"blur"}],priority:[{required:!0,message:"请选择优先级",trigger:"change"}]},users:i,loading:o,handleClose:()=>{l.value=!1,t("close")},handleSubmit:()=>{n.value&&n.value.validate((e=>{if(e){const e=u.assigneeUserIds||[],a=e.length>0?e[0]:null,l=e.length>1?e.slice(1):[],s={name:u.name,description:u.description,assigneeUserId:a,collaboratorUserIds:l,priority:u.priority,planStartDate:u.planStartDate?new Date(u.planStartDate).toISOString():null,planEndDate:u.planEndDate?new Date(u.planEndDate).toISOString():null,status:u.status||"Todo",taskType:u.taskType||"Normal",points:u.points||0};t("submit",s)}}))}}}},Ct={class:"user-option"},St={class:"user-dept"},Et={slot:"footer",class:"dialog-footer"};const xt=n(Tt,[["render",function(e,a,t,l,s,n){const r=h("el-input"),o=h("el-form-item"),i=h("el-option"),u=h("el-select"),d=h("el-avatar"),c=h("el-date-picker"),p=h("el-form"),m=h("el-button"),v=h("el-dialog");return b(),x(v,{modelValue:l.dialogVisible,"onUpdate:modelValue":a[7]||(a[7]=e=>l.dialogVisible=e),title:t.isEdit?"编辑任务":"新建任务",width:"600px",onClose:l.handleClose},{default:C((()=>[U(p,{model:l.form,rules:l.rules,ref:"formRef","label-width":"100px"},{default:C((()=>[U(o,{label:"任务名称",prop:"name"},{default:C((()=>[U(r,{modelValue:l.form.name,"onUpdate:modelValue":a[0]||(a[0]=e=>l.form.name=e),placeholder:"请输入任务名称"},null,8,["modelValue"])])),_:1}),U(o,{label:"描述",prop:"description"},{default:C((()=>[U(r,{type:"textarea",modelValue:l.form.description,"onUpdate:modelValue":a[1]||(a[1]=e=>l.form.description=e),placeholder:"请输入描述"},null,8,["modelValue"])])),_:1}),U(o,{label:"优先级",prop:"priority"},{default:C((()=>[U(u,{modelValue:l.form.priority,"onUpdate:modelValue":a[2]||(a[2]=e=>l.form.priority=e),placeholder:"请选择优先级"},{default:C((()=>[U(i,{label:"高",value:"High"}),U(i,{label:"中",value:"Medium"}),U(i,{label:"低",value:"Low"})])),_:1},8,["modelValue"])])),_:1}),U(o,{label:"负责人",prop:"assigneeUserIds"},{default:C((()=>[U(u,{modelValue:l.form.assigneeUserIds,"onUpdate:modelValue":a[3]||(a[3]=e=>l.form.assigneeUserIds=e),multiple:"","collapse-tags":"","collapse-tags-tooltip":"",filterable:"",placeholder:"请选择负责人(多选)",loading:l.loading,style:{width:"100%"}},{default:C((()=>[(b(!0),w(A,null,N(l.users,(e=>(b(),x(i,{key:e.id,label:e.name,value:e.id},{default:C((()=>[_("div",Ct,[e.avatarUrl?(b(),x(d,{key:0,size:24,src:e.avatarUrl},{default:C((()=>[S(D(e.name.substring(0,1)),1)])),_:2},1032,["src"])):I("",!0),_("span",null,D(e.name),1),_("span",St,D(e.department),1)])])),_:2},1032,["label","value"])))),128))])),_:1},8,["modelValue","loading"])])),_:1}),U(o,{label:"计划开始",prop:"planStartDate"},{default:C((()=>[U(c,{modelValue:l.form.planStartDate,"onUpdate:modelValue":a[4]||(a[4]=e=>l.form.planStartDate=e),type:"date",placeholder:"选择日期"},null,8,["modelValue"])])),_:1}),U(o,{label:"计划结束",prop:"planEndDate"},{default:C((()=>[U(c,{modelValue:l.form.planEndDate,"onUpdate:modelValue":a[5]||(a[5]=e=>l.form.planEndDate=e),type:"date",placeholder:"选择日期"},null,8,["modelValue"])])),_:1}),U(o,{label:"任务类型",prop:"taskType"},{default:C((()=>[U(u,{modelValue:l.form.taskType,"onUpdate:modelValue":a[6]||(a[6]=e=>l.form.taskType=e),placeholder:"请选择任务类型"},{default:C((()=>[U(i,{label:"普通",value:"Normal"}),U(i,{label:"周期",value:"Periodic"}),U(i,{label:"PDCA",value:"PDCA"})])),_:1},8,["modelValue"])])),_:1})])),_:1},8,["model","rules"]),_("div",Et,[U(m,{onClick:l.handleClose},{default:C((()=>a[8]||(a[8]=[S("取消")]))),_:1},8,["onClick"]),U(m,{type:"primary",onClick:l.handleSubmit},{default:C((()=>[S(D(t.isEdit?"保存":"创建"),1)])),_:1},8,["onClick"])])])),_:1},8,["modelValue","title","onClose"])}],["__scopeId","data-v-80012505"]]),At={class:"task-preview-section"},Nt={class:"section-title"},Pt={class:"task-preview-list"},zt={class:"task-info"},Lt={class:"task-name"},Mt={class:"current-assignee"},Bt={key:1,class:"no-assignee"},$t={key:0,class:"more-tasks"},Ot={class:"assign-section"},Ft={key:0,class:"single-assign"},jt={key:0,class:"user-preview"},Ht={class:"user-info"},Rt={class:"user-name"},qt={class:"user-workload"},Kt={key:1,class:"multiple-assign"},Qt={key:0,class:"distribution-preview"},Gt={class:"distribution-list"},Jt={class:"distribution-info"},Wt={class:"user-name"},Xt={class:"task-count"},Yt={class:"advanced-options"},Zt={class:"options-content"},el={class:"reason-section"},al={key:0,class:"operation-preview"},tl={class:"preview-content"},ll={class:"preview-summary"},sl={key:0,class:"notification-info"},nl={class:"dialog-footer"},rl={class:"footer-info"},ol={class:"task-count-info"},il={class:"footer-actions"},ul=n({__name:"BatchAssignDialog",props:{modelValue:{type:Boolean,default:!1},selectedTasks:{type:Array,default:()=>[]}},emits:["update:modelValue","assign","close"],setup(e,{emit:l}){const n=e,r=l,o=H(),i=t({get:()=>n.modelValue,set:e=>r("update:modelValue",e)}),u=a("single"),d=a(null),c=a([]),p=a(!0),m=a(!0),v=a(""),g=a(!1),y=a([]),k=a(null),f=t((()=>n.selectedTasks.slice(0,5))),V=t((()=>"single"===u.value?null!==d.value:c.value.length>0)),T=t((()=>V.value));P((()=>d.value),(e=>{k.value=e?y.value.find((a=>a.id===e)):null})),P((()=>n.modelValue),(e=>{e&&(z(),E())})),G((()=>{E()}));const E=async()=>{try{y.value=await o.getUsers()}catch(e){s.error("加载用户列表失败")}},z=()=>{u.value="single",d.value=null,c.value=[],v.value="",p.value=!0,m.value=!0,k.value=null},L=async()=>{var e;if(V.value)try{await Q.confirm(`确定要将 ${n.selectedTasks.length} 个任务${u.value,"分配给"}${"single"===u.value?null==(e=k.value)?void 0:e.name:c.value.length+"个用户"}吗？`,"确认批量分配",{type:"warning"}),g.value=!0;const a={taskIds:n.selectedTasks.map((e=>e.taskId)),assignMode:u.value,userIds:"single"===u.value?[d.value]:c.value,notifyAssignees:p.value,keepParticipants:m.value,reason:v.value};r("assign",a),M()}catch(a){"cancel"!==a&&s.error("批量分配失败: "+a.message)}finally{g.value=!1}else s.warning("请选择负责人")},M=()=>{r("close")},B=e=>{const a=n.selectedTasks.length,t=c.value.length,l=Math.floor(a/t);return e<a%t?l+1:l},$=e=>{const a=y.value.find((a=>a.id===e));return(null==a?void 0:a.name)||"未知用户"},O=e=>{const a=y.value.find((a=>a.id===e));return W(null==a?void 0:a.avatar)},F=e=>({High:"高",Medium:"中",Low:"低",Urgent:"紧急"}[e]||e);return(a,t)=>{const l=h("el-tag"),s=h("el-radio"),n=h("el-radio-group"),r=h("el-checkbox"),o=h("el-input"),y=h("el-collapse-item"),E=h("el-collapse"),P=h("User"),z=h("el-icon"),j=h("Bell"),H=h("el-button"),R=h("el-dialog");return b(),x(R,{modelValue:i.value,"onUpdate:modelValue":t[6]||(t[6]=e=>i.value=e),title:"批量分配任务",width:"500px","close-on-click-modal":!1,class:"batch-assign-dialog",onClose:M},{footer:C((()=>[_("div",nl,[_("div",rl,[_("span",ol," 共 "+D(e.selectedTasks.length)+" 个任务 ",1)]),_("div",il,[U(H,{onClick:M},{default:C((()=>t[17]||(t[17]=[S("取消")]))),_:1}),U(H,{type:"primary",onClick:L,disabled:!V.value,loading:g.value},{default:C((()=>t[18]||(t[18]=[S(" 确认分配 ")]))),_:1},8,["disabled","loading"])])])])),default:C((()=>{var a;return[_("div",At,[_("h4",Nt," 将要分配的任务 ("+D(e.selectedTasks.length)+"个) ",1),_("div",Pt,[(b(!0),w(A,null,N(f.value,(e=>{return b(),w("div",{key:e.taskId,class:"task-preview-item"},[_("div",zt,[_("span",Lt,D(e.name),1),U(l,{type:(a=e.priority,{High:"danger",Medium:"warning",Low:"success",Urgent:"danger"}[a]||""),size:"small",class:"priority-tag"},{default:C((()=>[S(D(F(e.priority)),1)])),_:2},1032,["type"])]),_("div",Mt,[t[7]||(t[7]=_("span",{class:"label"},"当前负责人:",-1)),e.assigneeUserId?(b(),x(Y,{key:0,"user-id":e.assigneeUserId,"user-name":e.assigneeUserName,"avatar-url":O(e.assigneeUserId),size:"mini"},null,8,["user-id","user-name","avatar-url"])):(b(),w("span",Bt,"未分配"))])]);var a})),128)),e.selectedTasks.length>5?(b(),w("div",$t," 还有 "+D(e.selectedTasks.length-5)+" 个任务... ",1)):I("",!0)])]),_("div",Ot,[t[11]||(t[11]=_("h4",{class:"section-title"},"选择新的负责人",-1)),U(n,{modelValue:u.value,"onUpdate:modelValue":t[0]||(t[0]=e=>u.value=e),class:"assign-mode"},{default:C((()=>[U(s,{value:"single"},{default:C((()=>t[8]||(t[8]=[S("分配给单个用户")]))),_:1}),U(s,{value:"distribute"},{default:C((()=>t[9]||(t[9]=[S("平均分配给多个用户")]))),_:1})])),_:1},8,["modelValue"]),"single"===u.value?(b(),w("div",Ft,[U(ae,{modelValue:d.value,"onUpdate:modelValue":t[1]||(t[1]=e=>d.value=e),placeholder:"选择负责人",filterable:!0,class:"user-select"},null,8,["modelValue"]),k.value?(b(),w("div",jt,[U(Y,{"user-id":k.value.id,"user-name":k.value.name,"avatar-url":O(k.value.id),size:"small"},null,8,["user-id","user-name","avatar-url"]),_("div",Ht,[_("div",Rt,D(k.value.name),1),_("div",qt," 当前任务: "+D(k.value.currentTasks||0)+"个 ",1)])])):I("",!0)])):(b(),w("div",Kt,[U(ae,{modelValue:c.value,"onUpdate:modelValue":t[2]||(t[2]=e=>c.value=e),placeholder:"选择多个负责人",multiple:!0,filterable:!0,class:"user-select"},null,8,["modelValue"]),c.value.length>0?(b(),w("div",Qt,[t[10]||(t[10]=_("h5",null,"分配预览:",-1)),_("div",Gt,[(b(!0),w(A,null,N(c.value,((e,a)=>(b(),w("div",{key:e,class:"distribution-item"},[U(Y,{"user-id":e,"user-name":$(e),"avatar-url":O(e),size:"small"},null,8,["user-id","user-name","avatar-url"]),_("div",Jt,[_("div",Wt,D($(e)),1),_("div",Xt," 将分配 "+D(B(a))+" 个任务 ",1)])])))),128))])])):I("",!0)]))]),_("div",Yt,[U(E,null,{default:C((()=>[U(y,{title:"高级选项",name:"advanced"},{default:C((()=>[_("div",Zt,[U(r,{modelValue:p.value,"onUpdate:modelValue":t[3]||(t[3]=e=>p.value=e)},{default:C((()=>t[12]||(t[12]=[S(" 通知新的负责人 ")]))),_:1},8,["modelValue"]),U(r,{modelValue:m.value,"onUpdate:modelValue":t[4]||(t[4]=e=>m.value=e)},{default:C((()=>t[13]||(t[13]=[S(" 保留原有参与者 ")]))),_:1},8,["modelValue"]),_("div",el,[t[14]||(t[14]=_("label",{class:"reason-label"},"分配原因:",-1)),U(o,{modelValue:v.value,"onUpdate:modelValue":t[5]||(t[5]=e=>v.value=e),type:"textarea",rows:2,placeholder:"输入分配原因（可选）",maxlength:"200","show-word-limit":""},null,8,["modelValue"])])])])),_:1})])),_:1})]),T.value?(b(),w("div",al,[t[16]||(t[16]=_("h4",{class:"section-title"},"操作预览",-1)),_("div",tl,[_("div",ll,[U(z,{class:"preview-icon"},{default:C((()=>[U(P)])),_:1}),_("span",null," 将 "+D(e.selectedTasks.length)+" 个任务 "+D((u.value,"分配给"))+" "+D("single"===u.value?null==(a=k.value)?void 0:a.name:c.value.length+"个用户"),1)]),p.value?(b(),w("div",sl,[U(z,{class:"info-icon"},{default:C((()=>[U(j)])),_:1}),t[15]||(t[15]=_("span",null,"将向新负责人发送通知",-1))])):I("",!0)])])):I("",!0)]})),_:1},8,["modelValue"])}}},[["__scopeId","data-v-9da0bc33"]]),dl={class:"batch-status-dialog"},cl={class:"dialog-info"},pl={class:"status-option"},ml={class:"affected-tasks"},vl={class:"task-list"},gl={class:"dialog-footer"},yl=n({__name:"BatchStatusDialog",props:{visible:{type:Boolean,default:!1},taskIds:{type:Array,default:()=>[]}},emits:["update:visible","success"],setup(e,{emit:t}){const n=e,r=t,o=a(!1),i=a(!1),u=te(),d=R({status:"",remarks:""}),c=[{value:"Todo",label:"待办",tagType:"info"},{value:"InProgress",label:"进行中",tagType:"warning"},{value:"Done",label:"已完成",tagType:"success"},{value:"Cancelled",label:"已取消",tagType:"danger"}];P((()=>n.visible),(e=>{o.value=e,e&&p()})),P(o,(e=>{r("update:visible",e)}));const p=()=>{d.status="",d.remarks=""},m=e=>{const a=u.getTaskById(e);return a?a.name||a.title:null},v=async()=>{if(d.status){i.value=!0;try{const e=await l.batchUpdateStatus(n.taskIds,d.status,d.remarks);e.success?(s.success("批量更新状态成功"),r("success"),o.value=!1,await u.fetchTasks()):s.error(e.message||"操作失败")}catch(e){s.error(e.message||"更新状态失败，请稍后重试")}finally{i.value=!1}}else s.warning("请选择要更新的状态")};return(a,t)=>{const l=h("el-tag"),s=h("el-option"),n=h("el-select"),r=h("el-form-item"),u=h("el-input"),p=h("el-form"),g=h("el-scrollbar"),y=h("el-button"),k=h("el-dialog");return b(),x(k,{modelValue:o.value,"onUpdate:modelValue":t[3]||(t[3]=e=>o.value=e),title:"批量更新任务状态",width:"500px","destroy-on-close":""},{footer:C((()=>[_("div",gl,[U(y,{onClick:t[2]||(t[2]=e=>o.value=!1)},{default:C((()=>t[7]||(t[7]=[S("取消")]))),_:1}),U(y,{type:"primary",onClick:v,loading:i.value},{default:C((()=>t[8]||(t[8]=[S(" 确认更新 ")]))),_:1},8,["loading"])])])),default:C((()=>[_("div",dl,[_("p",cl,[t[4]||(t[4]=S(" 您选择了 ")),_("strong",null,D(e.taskIds.length),1),t[5]||(t[5]=S(" 个任务，请选择要更新的状态： "))]),U(p,{model:d,"label-width":"80px"},{default:C((()=>[U(r,{label:"状态"},{default:C((()=>[U(n,{modelValue:d.status,"onUpdate:modelValue":t[0]||(t[0]=e=>d.status=e),placeholder:"选择任务状态",style:{width:"100%"}},{default:C((()=>[(b(),w(A,null,N(c,(e=>U(s,{key:e.value,label:e.label,value:e.value},{default:C((()=>[_("div",pl,[U(l,{type:e.tagType,size:"small",effect:"light"},{default:C((()=>[S(D(e.label),1)])),_:2},1032,["type"])])])),_:2},1032,["label","value"]))),64))])),_:1},8,["modelValue"])])),_:1}),U(r,{label:"备注"},{default:C((()=>[U(u,{modelValue:d.remarks,"onUpdate:modelValue":t[1]||(t[1]=e=>d.remarks=e),type:"textarea",rows:3,placeholder:"请输入更新备注（可选）"},null,8,["modelValue"])])),_:1})])),_:1},8,["model"]),_("div",ml,[t[6]||(t[6]=_("p",null,"受影响的任务：",-1)),U(g,{height:"120px"},{default:C((()=>[_("div",vl,[(b(!0),w(A,null,N(e.taskIds,(e=>(b(),w("div",{key:e,class:"task-item"},[U(l,{size:"small",effect:"plain"},{default:C((()=>[S(D(m(e)||`任务ID: ${e}`),1)])),_:2},1024)])))),128))])])),_:1})])])])),_:1},8,["modelValue"])}}},[["__scopeId","data-v-68621b98"]]);export{ul as B,Ve as E,Dt as T,xt as a,yl as b,te as u};
