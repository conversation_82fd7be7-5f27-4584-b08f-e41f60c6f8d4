// File: Application/Features/SpareParts/Services/SparePartTransactionService.cs
// Description: 备品备件出入库记录服务实现类

using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ItAssetsSystem.Application.Common.Dtos;
using ItAssetsSystem.Application.Features.SpareParts.Dtos;
using ItAssetsSystem.Core.Abstractions;
using ItAssetsSystem.Domain.Entities;
using Microsoft.Extensions.Logging;
using System;

namespace ItAssetsSystem.Application.Features.SpareParts.Services
{
    /// <summary>
    /// 备品备件出入库记录服务实现类
    /// </summary>
    public class SparePartTransactionService : ISparePartTransactionService
    {
        private readonly ISparePartTransactionRepository _transactionRepository;
        private readonly ISparePartRepository _sparePartRepository;
        private readonly ICurrentUserService _currentUserService;
        private readonly IUserRepository _userRepository;
        private readonly ILogger<SparePartTransactionService> _logger;
        
        /// <summary>
        /// 构造函数
        /// </summary>
        public SparePartTransactionService(
            ISparePartTransactionRepository transactionRepository,
            ISparePartRepository sparePartRepository,
            ICurrentUserService currentUserService,
            IUserRepository userRepository,
            ILogger<SparePartTransactionService> logger)
        {
            _transactionRepository = transactionRepository;
            _sparePartRepository = sparePartRepository;
            _currentUserService = currentUserService;
            _userRepository = userRepository;
            _logger = logger;
        }
        
        /// <summary>
        /// 获取出入库记录列表（分页）
        /// </summary>
        /// <param name="query">查询参数</param>
        /// <returns>分页结果</returns>
        public async Task<PaginatedResult<SparePartTransactionDto>> GetTransactionsAsync(SparePartTransactionQuery query)
        {
            var (transactions, totalCount) = await _transactionRepository.GetTransactionsPagedAsync(query);
            
            var transactionDtos = new List<SparePartTransactionDto>();
            foreach (var transaction in transactions)
            {
                transactionDtos.Add(await MapToDtoAsync(transaction));
            }
            
            return PaginatedResult<SparePartTransactionDto>.Create(
                transactionDtos,
                totalCount,
                query.PageIndex,
                query.PageSize);
        }
        
        /// <summary>
        /// 获取特定备件的出入库记录（分页）
        /// </summary>
        /// <param name="partId">备件ID</param>
        /// <param name="query">分页参数</param>
        /// <returns>分页结果</returns>
        public async Task<PaginatedResult<SparePartTransactionDto>> GetTransactionsByPartIdAsync(long partId, PaginationQuery query)
        {
            var (transactions, totalCount) = await _transactionRepository.GetTransactionsByPartIdAsync(partId, query);
            
            var transactionDtos = new List<SparePartTransactionDto>();
            foreach (var transaction in transactions)
            {
                transactionDtos.Add(await MapToDtoAsync(transaction));
            }
            
            return PaginatedResult<SparePartTransactionDto>.Create(
                transactionDtos,
                totalCount,
                query.PageIndex,
                query.PageSize);
        }
        
        /// <summary>
        /// 获取出入库记录详情
        /// </summary>
        /// <param name="id">记录ID</param>
        /// <returns>记录DTO</returns>
        public async Task<SparePartTransactionDto> GetTransactionByIdAsync(long id)
        {
            var transaction = await _transactionRepository.GetByIdAsync(id);
            if (transaction == null)
            {
                return null;
            }
            
            return await MapToDtoAsync(transaction);
        }
        
        /// <summary>
        /// 将实体映射为DTO（异步版本，包含用户信息查询）
        /// </summary>
        /// <param name="entity">交易记录实体</param>
        /// <returns>交易记录DTO</returns>
        private async Task<SparePartTransactionDto> MapToDtoAsync(SparePartTransaction entity)
        {
            if (entity == null)
            {
                return null;
            }
            
            string operatorName = "未知";
            try
            {
                var user = await _userRepository.GetByIdAsync(entity.OperatorUserId);
                if (user != null)
                {
                    operatorName = user.Name;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取操作人信息失败，用户ID: {UserId}", entity.OperatorUserId);
            }
            
            return new SparePartTransactionDto
            {
                Id = entity.Id,
                PartId = entity.PartId,
                PartCode = entity.Part?.Code,
                PartName = entity.Part?.Name,
                LocationId = entity.LocationId,
                LocationName = entity.Location?.Name,
                LocationArea = entity.Location?.Area,
                Type = entity.Type,
                TypeName = entity.Type == 1 ? "入库" : "出库",
                Quantity = entity.Quantity,
                StockAfter = entity.StockAfter,
                ReasonType = entity.ReasonType,
                ReasonTypeName = GetReasonTypeName(entity.ReasonType),
                ReferenceNumber = entity.ReferenceNumber,
                OperatorUserId = entity.OperatorUserId,
                OperatorName = operatorName,
                OperationTime = entity.OperationTime,
                Remarks = entity.Reason,
                BatchNumber = entity.BatchNumber,
                IsSystemGenerated = entity.IsSystemGenerated
            };
        }
        
        /// <summary>
        /// 获取原因类型名称
        /// </summary>
        /// <param name="reasonType">原因类型编码</param>
        /// <returns>原因类型名称</returns>
        private string GetReasonTypeName(byte reasonType)
        {
            switch (reasonType)
            {
                case 1:
                    return "采购入库";
                case 2:
                    return "退回入库";
                case 3:
                    return "领用出库";
                case 4:
                    return "报废出库";
                case 5:
                    return "盘点调整";
                default:
                    return "未知";
            }
        }
    }
} 