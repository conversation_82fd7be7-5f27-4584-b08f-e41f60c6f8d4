// File: Infrastructure/Data/Repositories/QuickMemoRepository.cs
// Description: EF Core implementation of IQuickMemoRepository.
#nullable enable
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Threading; // For CancellationToken
using ItAssetsSystem.Core.Abstractions;
using ItAssetsSystem.Domain.Entities.Notes;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace ItAssetsSystem.Infrastructure.Data.Repositories
{
    public class QuickMemoRepository : IQuickMemoRepository
    {
        private readonly AppDbContext _context;
        private readonly ILogger<QuickMemoRepository> _logger;

        public QuickMemoRepository(AppDbContext context, ILogger<QuickMemoRepository> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<IEnumerable<QuickMemo>> GetByUserIdAsync(int userId, CancellationToken cancellationToken = default)
        {
            try 
            {
                return await _context.QuickMemos
                    .Include(qm => qm.Category) // Include category for CategoryName/Color
                    .Include(qm => qm.User) // Include user for UserName
                    .Where(qm => qm.UserId == userId)
                    .OrderByDescending(qm => qm.IsPinned) // Pinned items first
                    .ThenByDescending(qm => qm.UpdatedAt) // Then by most recently updated
                    .ToListAsync(cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving quickmemos for user {UserId}", userId);
                return new List<QuickMemo>();
            }
        }

        public async Task<QuickMemo?> GetByIdAsync(string memoId, CancellationToken cancellationToken = default)
        {
            try
            {
                return await _context.QuickMemos
                    .Include(qm => qm.Category)
                    .Include(qm => qm.User) // Include user for UserName
                    .FirstOrDefaultAsync(qm => qm.Id == memoId, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving quickmemo with id {MemoId}", memoId);
                return null;
            }
        }

        public async Task AddAsync(QuickMemo memo, CancellationToken cancellationToken = default)
        {
            try
            {
                await _context.QuickMemos.AddAsync(memo, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding new quickmemo for user {UserId}", memo.UserId);
                throw;
            }
        }

        public void Update(QuickMemo memo)
        {
            try
            {
                _context.QuickMemos.Update(memo);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating quickmemo with id {MemoId}", memo.Id);
                throw;
            }
        }

        public void Delete(QuickMemo memo)
        {
            try
            {
                _context.QuickMemos.Remove(memo);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting quickmemo with id {MemoId}", memo.Id);
                throw;
            }
        }

        public async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                return await _context.SaveChangesAsync(cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving quickmemo changes");
                throw;
            }
        }

        public async Task<bool> UserOwnsMemoAsync(string memoId, int userId, CancellationToken cancellationToken = default)
        {
            try
            {
                return await _context.QuickMemos.AnyAsync(qm => qm.Id == memoId && qm.UserId == userId, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking quickmemo ownership for user {UserId} and memo {MemoId}", userId, memoId);
                return false;
            }
        }
    }
} 