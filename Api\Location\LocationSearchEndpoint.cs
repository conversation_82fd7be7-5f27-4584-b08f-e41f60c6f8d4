// IT资产管理系统 - 位置搜索API端点
// 文件路径: /Api/Location/LocationSearchEndpoint.cs
// 功能: 提供位置搜索的API端点

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ItAssetsSystem.Infrastructure.Data;
using ItAssetsSystem.Models.Entities;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace ItAssetsSystem.Api.Location
{
    /// <summary>
    /// 位置搜索请求
    /// </summary>
    public class LocationSearchRequest
    {
        /// <summary>
        /// 搜索关键词（位置名称、代码或地址）
        /// </summary>
        public string Keyword { get; set; }
        
        /// <summary>
        /// 是否仅返回激活的位置
        /// </summary>
        public bool ActiveOnly { get; set; } = true;
        
        /// <summary>
        /// 部门ID（可选）
        /// </summary>
        public int? DepartmentId { get; set; }
        
        /// <summary>
        /// 最大返回结果数
        /// </summary>
        public int Limit { get; set; } = 20;
    }
    
    /// <summary>
    /// 位置搜索结果
    /// </summary>
    public class LocationSearchResult
    {
        /// <summary>
        /// 位置ID
        /// </summary>
        public int Id { get; set; }
        
        /// <summary>
        /// 位置代码
        /// </summary>
        public string Code { get; set; }
        
        /// <summary>
        /// 位置名称
        /// </summary>
        public string Name { get; set; }
        
        /// <summary>
        /// 位置类型
        /// </summary>
        public int Type { get; set; }
        
        /// <summary>
        /// 上级位置ID
        /// </summary>
        public int? ParentId { get; set; }
        
        /// <summary>
        /// 位置路径
        /// </summary>
        public string Path { get; set; }
        
        /// <summary>
        /// 位置描述
        /// </summary>
        public string Description { get; set; }
        
        /// <summary>
        /// 是否激活
        /// </summary>
        public bool IsActive { get; set; }
    }
    
    /// <summary>
    /// 位置搜索API端点
    /// </summary>
    [ApiController]
    [Route("api/location-search")]
    public class LocationSearchEndpoint
    {
        private readonly AppDbContext _dbContext;
        private readonly ILogger<LocationSearchEndpoint> _logger;
        
        /// <summary>
        /// 构造函数
        /// </summary>
        public LocationSearchEndpoint(
            AppDbContext dbContext,
            ILogger<LocationSearchEndpoint> logger)
        {
            _dbContext = dbContext;
            _logger = logger;
        }
        
        /// <summary>
        /// 搜索位置
        /// </summary>
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<IActionResult> SearchLocationsAsync([FromQuery] LocationSearchRequest request)
        {
            _logger.LogInformation("搜索位置，关键词：{Keyword}", request.Keyword);
            
            try
            {
                // 构建查询
                var query = _dbContext.Locations.AsQueryable();
                
                // 关键词搜索
                if (!string.IsNullOrWhiteSpace(request.Keyword))
                {
                    string keyword = request.Keyword.Trim().ToLower();
                    query = query.Where(l => 
                        l.Name.ToLower().Contains(keyword) || 
                        l.Code.ToLower().Contains(keyword) || 
                        (l.Description != null && l.Description.ToLower().Contains(keyword))
                    );
                }
                
                // 筛选激活状态
                if (request.ActiveOnly)
                {
                    query = query.Where(l => l.IsActive);
                }
                
                // 筛选部门
                if (request.DepartmentId.HasValue)
                {
                    query = query.Where(l => l.DefaultDepartmentId == request.DepartmentId);
                }
                
                // 包含关联数据
                query = query
                    .Include(l => l.Parent)
                    .Include(l => l.Department);
                
                // 执行查询并转换结果
                var locations = await query
                    .OrderByDescending(l => request.Keyword != null && l.Name.ToLower().StartsWith(request.Keyword.ToLower()))
                    .ThenBy(l => l.Name)
                    .Take(request.Limit)
                    .ToListAsync();
                
                // 转换为搜索结果
                var results = locations.Select(l => new LocationSearchResult
                {
                    Id = l.Id,
                    Code = l.Code,
                    Name = l.Name,
                    Type = l.Type,
                    ParentId = l.ParentId,
                    Path = BuildLocationPath(l),
                    Description = l.Description,
                    IsActive = l.IsActive
                }).ToList();
                
                return new OkObjectResult(results);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "搜索位置失败");
                return new StatusCodeResult(StatusCodes.Status500InternalServerError);
            }
        }
        
        /// <summary>
        /// 按ID获取位置详情
        /// </summary>
        [HttpGet("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetLocationByIdAsync(int id)
        {
            _logger.LogInformation("获取位置详情，ID：{Id}", id);
            
            try
            {
                // 查询位置和关联数据
                var location = await _dbContext.Locations
                    .Include(l => l.Parent)
                    .Include(l => l.Department)
                    .FirstOrDefaultAsync(l => l.Id == id);
                
                if (location == null)
                {
                    return new NotFoundResult();
                }
                
                // 转换为搜索结果
                var result = new LocationSearchResult
                {
                    Id = location.Id,
                    Code = location.Code,
                    Name = location.Name,
                    Type = location.Type,
                    ParentId = location.ParentId,
                    Path = BuildLocationPath(location),
                    Description = location.Description,
                    IsActive = location.IsActive
                };
                
                return new OkObjectResult(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取位置详情失败");
                return new StatusCodeResult(StatusCodes.Status500InternalServerError);
            }
        }
        
        /// <summary>
        /// 构建位置路径
        /// </summary>
        private string BuildLocationPath(Models.Entities.Location location)
        {
            if (location == null)
            {
                return string.Empty;
            }
            
            var path = new List<string> { location.Name };
            var current = location;
            
            while (current.ParentId != null && current.Parent != null)
            {
                path.Insert(0, current.Parent.Name);
                current = current.Parent;
            }
            
            return string.Join(" / ", path);
        }
    }
} 