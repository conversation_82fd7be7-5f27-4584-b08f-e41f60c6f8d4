using System;
using MediatR;
using ItAssetsSystem.Application.Common.Dtos;
using ItAssetsSystem.Application.Features.Tasks.Dtos;

namespace ItAssetsSystem.Application.Features.Tasks.Queries
{
    /// <summary>
    /// 获取任务分页列表查询
    /// </summary>
    public class GetTasksPagedQuery : IRequest<PaginatedResult<TaskDto>>
    {
        /// <summary>
        /// 页码
        /// </summary>
        public int Page { get; set; } = 1;

        /// <summary>
        /// 每页大小
        /// </summary>
        public int Size { get; set; } = 20;

        /// <summary>
        /// 搜索关键词
        /// </summary>
        public string? Keyword { get; set; }

        /// <summary>
        /// 任务状态
        /// </summary>
        public string? Status { get; set; }

        /// <summary>
        /// 优先级
        /// </summary>
        public string? Priority { get; set; }

        /// <summary>
        /// 任务类型
        /// </summary>
        public string? TaskType { get; set; }

        /// <summary>
        /// 负责人ID
        /// </summary>
        public int? AssigneeId { get; set; }

        /// <summary>
        /// 创建人ID
        /// </summary>
        public int? CreatedBy { get; set; }

        /// <summary>
        /// 资产ID
        /// </summary>
        public int? AssetId { get; set; }

        /// <summary>
        /// 位置ID
        /// </summary>
        public int? LocationId { get; set; }

        /// <summary>
        /// 开始日期
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// 结束日期
        /// </summary>
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// 父任务ID
        /// </summary>
        public long? ParentTaskId { get; set; }

        /// <summary>
        /// 项目ID
        /// </summary>
        public long? ProjectId { get; set; }

        /// <summary>
        /// 排序字段
        /// </summary>
        public string? SortBy { get; set; } = "CreationTimestamp";

        /// <summary>
        /// 排序方向
        /// </summary>
        public string? SortDirection { get; set; } = "desc";

        /// <summary>
        /// 是否包含已删除
        /// </summary>
        public bool IncludeDeleted { get; set; } = false;
    }
}
