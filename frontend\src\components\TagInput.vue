<template>
  <div class="tag-input">
    <div class="tags">
      <span v-for="(tag, idx) in modelValue" :key="idx" class="tag">
        {{ tag }}
        <span class="remove" @click="removeTag(idx)">×</span>
      </span>
      <input
        v-model="input"
        @keyup.enter="addTag"
        @blur="addTag"
        placeholder="添加标签"
        class="tag-input-box"
      />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  }
})
const emit = defineEmits(['update:modelValue'])
const input = ref('')
function addTag() {
  const val = input.value.trim()
  if (val && !props.modelValue.includes(val)) {
    emit('update:modelValue', [...props.modelValue, val])
  }
  input.value = ''
}
function removeTag(idx) {
  const newTags = [...props.modelValue]
  newTags.splice(idx, 1)
  emit('update:modelValue', newTags)
}
</script>

<style scoped>
.tag-input {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}
.tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}
.tag {
  background: #f0f0f0;
  border-radius: 2px;
  padding: 2px 8px;
  font-size: 13px;
  display: flex;
  align-items: center;
}
.remove {
  margin-left: 4px;
  color: #888;
  cursor: pointer;
}
.tag-input-box {
  border: none;
  outline: none;
  min-width: 60px;
  font-size: 13px;
  margin-left: 4px;
}
</style> 