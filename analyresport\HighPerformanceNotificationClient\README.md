# 高性能实时通知客户端

## 项目概述

这是一个基于Win32 API和UDP协议的高性能实时通知客户端，专为IT资产管理系统设计，实现极致低延迟（<0.5ms）的实时通知推送。

## 功能特性

- ⚡ **极致性能**：UDP协议 + Win32原生API，延迟<0.5ms
- 🔄 **混合架构**：UDP主通道 + WebSocket备用通道
- 🎮 **游戏化支持**：实时游戏化奖励通知
- 📋 **任务管理**：任务状态变更实时推送
- 🔔 **系统托盘**：原生Windows系统托盘集成
- 🏆 **排行榜**：实时排行榜更新
- 🔒 **安全认证**：支持JWT令牌认证

## 系统要求

- Windows 10/11
- Visual Studio 2019+ 或 CMake 3.16+
- C++17 支持

## 快速开始

### 1. 构建项目

```bash
mkdir build
cd build
cmake ..
cmake --build . --config Release
```

### 2. 配置连接

编辑 `config.json`：

```json
{
  "server": {
    "ip": "127.0.0.1",
    "udp_port": 8081,
    "websocket_port": 8080,
    "api_base_url": "http://localhost:5000/api"
  },
  "auth": {
    "username": "your_username",
    "password": "your_password",
    "auto_login": true
  },
  "performance": {
    "enable_udp": true,
    "fallback_to_websocket": true,
    "max_retry_count": 3
  }
}
```

### 3. 运行客户端

```bash
./HighPerformanceNotificationClient.exe
```

## 架构设计

### 通信协议

1. **主通道：UDP + 自定义协议**
   - 延迟：0.1-0.5ms
   - 适用：游戏化奖励、实时状态
   - 特点：无连接开销，极低延迟

2. **备用通道：WebSocket**
   - 延迟：2-5ms
   - 适用：重要消息、防火墙环境
   - 特点：可靠传输，防火墙友好

### 消息类型

- `GAMIFICATION_REWARD`: 游戏化奖励通知
- `TASK_UPDATE`: 任务状态更新
- `SYSTEM_ALERT`: 系统重要提醒
- `LEADERBOARD_UPDATE`: 排行榜更新

## API对接

### 认证状态检测

客户端自动检测后端API认证要求：

- 自动获取JWT令牌
- 处理令牌过期和刷新
- 支持匿名API调用

### 支持的后端接口

- `/api/v2/gamification/*` - 游戏化相关API
- `/api/v2/work-summary/*` - 工作汇总API  
- `/hubs/notification` - SignalR通知中心

## 使用示例

### C++ API调用

```cpp
#include "notification_client.h"

int main() {
    // 创建客户端
    auto client = std::make_unique<HighPerformanceNotificationClient>();
    
    // 设置回调
    client->setNotificationCallback([](const Notification& notif) {
        std::cout << "收到通知: " << notif.message << std::endl;
    });
    
    // 连接服务器
    if (client->connect("127.0.0.1", 8081)) {
        client->run();
    }
    
    return 0;
}
```

### 系统托盘交互

- 右键菜单：显示/隐藏、设置、退出
- 气泡通知：重要消息弹出提醒
- 红点徽章：未读消息计数显示

## 性能优化

### 网络优化

- 零拷贝UDP发送
- 内存池避免动态分配
- 批量消息合并发送
- 智能重连机制

### 界面优化

- 硬件加速渲染
- 异步消息处理
- 最小化CPU占用
- 低内存占用设计

## 故障排除

### 常见问题

1. **连接失败**
   - 检查服务器IP和端口
   - 确认防火墙设置
   - 验证网络连通性

2. **认证失败**
   - 检查用户名密码
   - 验证API访问权限
   - 确认令牌有效性

3. **性能问题**
   - 检查网络延迟
   - 监控CPU和内存使用
   - 调整缓冲区大小

### 调试模式

启用详细日志：

```bash
./HighPerformanceNotificationClient.exe --debug --log-level=verbose
```

## 开发指南

### 添加新消息类型

1. 在 `message_types.h` 中定义新类型
2. 在 `message_handler.cpp` 中添加处理逻辑
3. 更新协议文档

### 性能测试

运行性能基准测试：

```bash
./performance_benchmark.exe
```

## 许可证

MIT License - 详见 LICENSE 文件

## 技术支持

- 项目地址：内部Git仓库
- 技术文档：docs/ 目录
- 问题反馈：技术支持邮箱