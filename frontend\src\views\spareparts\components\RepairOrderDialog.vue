<template>
  <el-dialog
    :model-value="props.visible"
    @update:model-value="emit('update:visible', $event)"
    :title="mode === 'create' ? '创建返厂维修单' : '编辑返厂维修单'"
    width="1200px"
    @close="handleClose"
    class="repair-order-dialog"
  >
    <!-- 紧凑的表单布局 -->
    <el-form ref="formRef" :model="form" :rules="rules" label-width="100px" size="small">
      <!-- 基本信息 - 紧凑布局 -->
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="维修标题" prop="title">
            <el-input v-model="form.title" placeholder="请输入维修标题" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="优先级" prop="priority">
            <el-select v-model="form.priority" placeholder="中" style="width: 100%">
              <el-option label="紧急" :value="1" />
              <el-option label="高" :value="2" />
              <el-option label="中" :value="3" />
              <el-option label="低" :value="4" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="维修描述" prop="description">
        <el-input v-model="form.description" type="textarea" :rows="2" placeholder="请输入维修描述" />
      </el-form-item>

      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="维修供应商" prop="supplierId">
            <el-select v-model="form.supplierId" placeholder="请选择维修供应商" style="width: 100%">
              <el-option v-for="supplier in suppliers" :key="supplier.id" :label="supplier.name" :value="supplier.id" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="关联故障">
            <el-select v-model="form.faultId" placeholder="选择关联故障（可选）" clearable style="width: 100%">
              <el-option v-for="fault in faults" :key="fault.id" :label="fault.title" :value="fault.id" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="16">
        <el-col :span="8">
          <el-form-item label="预估费用">
            <el-input-number v-model="form.estimatedCost" :min="0" :precision="2" controls-position="right" style="width: 100%" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="预估天数">
            <el-input-number v-model="form.estimatedDays" :min="1" controls-position="right" style="width: 100%" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="备注">
            <el-input v-model="form.notes" placeholder="请输入备注" />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 维修物品选择 - 紧凑表格 -->
      <el-divider content-position="left">
        <span style="font-weight: bold;">维修物品</span>
        <el-button type="primary" size="small" @click="showSparePartSelector" style="margin-left: 10px;">选择备件</el-button>
      </el-divider>

      <el-table :data="form.repairItems" border size="small" class="items-table" max-height="300">
        <el-table-column prop="partCode" label="备件编号" width="120" />
        <el-table-column prop="partName" label="备件名称" min-width="150" />
        <el-table-column label="数量" width="100">
          <template #default="{ row }">
            <el-input-number v-model="row.quantity" :min="1" :max="row.availableQuantity" size="small" controls-position="right" />
          </template>
        </el-table-column>
        <el-table-column label="故障描述" min-width="150">
          <template #default="{ row }">
            <el-input v-model="row.faultDescription" placeholder="故障描述" size="small" />
          </template>
        </el-table-column>
        <el-table-column label="序列号" width="120">
          <template #default="{ row }">
            <el-input v-model="row.serialNumber" placeholder="序列号" size="small" />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="80" fixed="right">
          <template #default="{ $index }">
            <el-button type="danger" size="small" link @click="removeRepairItem($index)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div v-if="form.repairItems.length === 0" class="empty-state">
        <el-empty description="暂无维修物品，请点击上方选择备件添加" :image-size="80" />
      </div>

      <!-- 快速汇总信息 -->
      <div v-if="form.repairItems.length > 0" class="quick-summary">
        <el-tag type="info">物品种类：{{ form.repairItems.length }}种</el-tag>
        <el-tag type="success">总数量：{{ totalItems }}件</el-tag>
        <el-tag v-if="form.estimatedCost" type="warning">预估费用：¥{{ form.estimatedCost.toFixed(2) }}</el-tag>
        <el-tag v-if="form.estimatedDays" type="primary">预估工期：{{ form.estimatedDays }}天</el-tag>
      </div>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :disabled="!canSubmit" :loading="submitting">
          {{ mode === 'create' ? '创建维修单' : '保存修改' }}
        </el-button>
      </div>
    </template>

    <!-- 备件选择器对话框 -->
    <SparePartSelector
      v-model:visible="sparePartSelectorVisible"
      :selected-parts="form.repairItems"
      @confirm="handleSparePartsSelected"
    />

    <!-- 维修单汇总单对话框 -->
    <RepairOrderSummary
      v-model:visible="summaryVisible"
      :order-data="createdOrderData"
    />
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Delete } from '@element-plus/icons-vue'
import SparePartSelector from './SparePartSelector.vue'
import RepairOrderSummary from './RepairOrderSummary.vue'
import {
  createRepairOrder,
  updateRepairOrder,
  getMaintenanceSuppliers
} from '@/api/spareparts'
import { getFaults } from '@/api/faults'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  orderData: {
    type: Object,
    default: () => ({})
  },
  mode: {
    type: String,
    default: 'create' // 'create' | 'edit'
  }
})

// Emits
const emit = defineEmits(['update:visible', 'submit'])

// 响应式数据
const formRef = ref()
const sparePartSelectorVisible = ref(false)
const summaryVisible = ref(false)
const submitting = ref(false)
const suppliers = ref([])
const faults = ref([])
const createdOrderData = ref({})

const form = reactive({
  id: null,
  title: '',
  description: '',
  priority: 3,
  supplierId: null,
  faultId: null,
  estimatedCost: null,
  estimatedDays: null,
  notes: '',
  repairItems: []
})

const rules = {
  title: [{ required: true, message: '请输入维修标题', trigger: 'blur' }],
  description: [{ required: true, message: '请输入维修描述', trigger: 'blur' }],
  priority: [{ required: true, message: '请选择优先级', trigger: 'change' }],
  supplierId: [{ required: true, message: '请选择维修供应商', trigger: 'change' }]
}

// 计算属性
const totalItems = computed(() => {
  return form.repairItems.reduce((total, item) => total + (item.quantity || 0), 0)
})

const canSubmit = computed(() => {
  return form.title && form.description && form.supplierId && form.repairItems.length > 0
})

// 方法
const loadSuppliers = async () => {
  try {
    const response = await getMaintenanceSuppliers()
    if (response.success) {
      suppliers.value = response.data || []
    }
  } catch (error) {
    console.error('加载维修供应商失败:', error)
  }
}

const loadFaults = async () => {
  try {
    // 使用数字状态值：0=待处理, 1=处理中
    const response = await getFaults({ status: 0 })
    if (response.success) {
      faults.value = response.data.items || []
    }
  } catch (error) {
    console.error('加载故障清单失败:', error)
  }
}

const showSparePartSelector = () => {
  sparePartSelectorVisible.value = true
}

const handleSparePartsSelected = (selectedParts) => {
  // 添加新选择的备件，避免重复
  selectedParts.forEach(part => {
    const exists = form.repairItems.find(item => item.partId === part.id)
    if (!exists) {
      form.repairItems.push({
        partId: part.id,
        partCode: part.code,
        partName: part.name,
        availableQuantity: part.stockQuantity,
        quantity: 1,
        faultDescription: '',
        serialNumber: ''
      })
    }
  })
  sparePartSelectorVisible.value = false
}

const removeRepairItem = (index) => {
  form.repairItems.splice(index, 1)
}

// 汇总相关方法
const generateOrderNumber = () => {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0')
  return `RO${year}${month}${day}${random}`
}

const getPriorityText = (priority) => {
  const priorityMap = {
    1: '紧急',
    2: '高',
    3: '中',
    4: '低'
  }
  return priorityMap[priority] || '中'
}

const getSupplierName = (supplierId) => {
  if (!supplierId) return '未选择'
  const supplier = suppliers.value.find(s => s.id === supplierId)
  return supplier ? supplier.name : '未知供应商'
}

const previewRepairOrder = () => {
  if (form.repairItems.length === 0) {
    ElMessage.warning('请先添加维修物品')
    return
  }
  previewVisible.value = true
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()

    if (form.repairItems.length === 0) {
      ElMessage.warning('请至少选择一个维修物品')
      return
    }

    submitting.value = true

    const submitData = {
      ...form,
      repairItems: form.repairItems.map(item => ({
        partId: item.partId,
        quantity: item.quantity,
        faultDescription: item.faultDescription,
        serialNumber: item.serialNumber
      }))
    }

    let response
    if (props.mode === 'create') {
      response = await createRepairOrder(submitData)
    } else {
      response = await updateRepairOrder(form.id, submitData)
    }

    if (response.success) {
      ElMessage.success(props.mode === 'create' ? '维修单创建成功' : '维修单更新成功')

      // 如果是创建模式，准备汇总单数据并显示
      if (props.mode === 'create') {
        createdOrderData.value = {
          ...response.data,
          supplierName: getSupplierName(form.supplierId),
          repairItems: form.repairItems,
          createdAt: new Date()
        }

        // 关闭当前对话框
        emit('update:visible', false)
        resetForm()

        // 显示汇总单
        setTimeout(() => {
          summaryVisible.value = true
        }, 300)
      }

      emit('submit', response.data)
    } else {
      ElMessage.error('操作失败: ' + response.message)
    }
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败')
  } finally {
    submitting.value = false
  }
}

const handleClose = () => {
  emit('update:visible', false)
  resetForm()
}

const resetForm = () => {
  Object.assign(form, {
    id: null,
    title: '',
    description: '',
    priority: 3,
    supplierId: null,
    faultId: null,
    estimatedCost: null,
    estimatedDays: null,
    notes: '',
    repairItems: []
  })
}

// 监听props变化
watch(() => props.visible, (newVisible) => {
  console.log('RepairOrderDialog visible 变化:', newVisible)
  if (newVisible) {
    console.log('对话框打开，模式:', props.mode)
  }
}, { immediate: true })

watch(() => props.orderData, (newData) => {
  if (newData && Object.keys(newData).length > 0) {
    Object.assign(form, {
      ...newData,
      repairItems: newData.repairItems || []
    })
  }
}, { deep: true, immediate: true })

// 生命周期
onMounted(() => {
  loadSuppliers()
  loadFaults()
})
</script>

<style scoped>
.repair-order-dialog :deep(.el-dialog__body) {
  padding: 20px;
}

.items-table {
  margin-top: 10px;
}

.empty-state {
  text-align: center;
  padding: 20px 0;
  color: #909399;
}

.quick-summary {
  margin-top: 15px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.dialog-footer {
  text-align: right;
}

.dialog-footer .el-button {
  margin-left: 10px;
}

/* 紧凑布局样式 */
:deep(.el-form-item) {
  margin-bottom: 16px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-divider__text) {
  font-weight: bold;
  color: #303133;
}

/* 表格样式优化 */
:deep(.el-table) {
  border-radius: 4px;
}

:deep(.el-table th) {
  background-color: #fafafa;
  font-weight: 600;
}

:deep(.el-table td) {
  padding: 8px 0;
}

/* 输入框紧凑样式 */
:deep(.el-input-number) {
  width: 100%;
}

:deep(.el-input-number .el-input__inner) {
  text-align: center;
}
</style>
