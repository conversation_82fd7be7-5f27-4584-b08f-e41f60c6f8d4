// File: Domain/Entities/SparePart.cs
// Description: 备品备件实体类

using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ItAssetsSystem.Domain.Entities
{
    /// <summary>
    /// 备品备件实体
    /// </summary>
    [Table("spare_parts")]
    public class SparePart
    {
        /// <summary>
        /// 备件ID
        /// </summary>
        [Key]
        [Column("id")]
        public long Id { get; set; }
        
        /// <summary>
        /// 备件编号
        /// </summary>
        [Required]
        [Column("code")]
        [StringLength(50)]
        public string Code { get; set; }

        /// <summary>
        /// 物料编号
        /// </summary>
        [Column("material_number")]
        [StringLength(50)]
        public string MaterialNumber { get; set; }

        /// <summary>
        /// 备件名称
        /// </summary>
        [Required]
        [Column("name")]
        [StringLength(100)]
        public string Name { get; set; }
        
        /// <summary>
        /// 备件类型ID
        /// </summary>
        [Column("type_id")]
        public long TypeId { get; set; }
        
        /// <summary>
        /// 备件类型导航属性
        /// </summary>
        [ForeignKey("TypeId")]
        public virtual SparePartType Type { get; set; }
        
        /// <summary>
        /// 规格型号
        /// </summary>
        [Column("spec")]
        [StringLength(200)]
        public string Specification { get; set; }
        
        /// <summary>
        /// 品牌
        /// </summary>
        [Column("brand")]
        [StringLength(100)]
        public string Brand { get; set; }
        
        /// <summary>
        /// 单位
        /// </summary>
        [Column("unit")]
        [StringLength(20)]
        public string Unit { get; set; }
        
        /// <summary>
        /// 库存数量
        /// </summary>
        [Column("quantity")]
        public int StockQuantity { get; set; }
        
        /// <summary>
        /// 安全库存预警阈值
        /// </summary>
        [Column("warning_threshold")]
        public int WarningThreshold { get; set; }
        
        /// <summary>
        /// 最小安全库存
        /// </summary>
        [Column("min_threshold")]
        public int MinStock { get; set; }
        
        /// <summary>
        /// 库位ID
        /// </summary>
        [Column("location_id")]
        public long? LocationId { get; set; }
        
        /// <summary>
        /// 库位导航属性
        /// </summary>
        [ForeignKey("LocationId")]
        public virtual SparePartLocation Location { get; set; }
        
        /// <summary>
        /// 价格(元)
        /// </summary>
        [Column("purchase_price")]
        public decimal? Price { get; set; }
        
        /// <summary>
        /// 备注
        /// </summary>
        [Column("notes")]
        [StringLength(500)]
        public string Remarks { get; set; }
        
        /// <summary>
        /// 创建时间
        /// </summary>
        [Column("created_at")]
        public DateTime CreatedAt { get; set; }
        
        /// <summary>
        /// 最后修改时间
        /// </summary>
        [Column("updated_at")]
        public DateTime? UpdatedAt { get; set; }
        
        /// <summary>
        /// 出入库记录导航属性
        /// </summary>
        public virtual ICollection<SparePartTransaction> Transactions { get; set; } = new List<SparePartTransaction>();

        /// <summary>
        /// 库存明细导航属性
        /// </summary>
        public virtual ICollection<SparePartInventory> Inventories { get; set; } = new List<SparePartInventory>();

        /// <summary>
        /// 返厂维修明细导航属性
        /// </summary>
        public virtual ICollection<RepairOrderItem> RepairOrderItems { get; set; } = new List<RepairOrderItem>();

        /// <summary>
        /// 更新库存数量
        /// </summary>
        /// <param name="changeQuantity">变更数量</param>
        public void UpdateStock(int changeQuantity)
        {
            StockQuantity += changeQuantity;
            UpdatedAt = DateTime.Now;
        }
        
        /// <summary>
        /// 检查是否低于库存预警阈值
        /// </summary>
        /// <returns>是否预警</returns>
        public bool IsLowWarningStock()
        {
            return StockQuantity <= WarningThreshold;
        }
        
        /// <summary>
        /// 检查是否低于最小安全库存
        /// </summary>
        /// <returns>是否危险</returns>
        public bool IsLowMinStock()
        {
            return StockQuantity <= MinStock;
        }
    }
} 