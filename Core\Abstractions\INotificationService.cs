using ItAssetsSystem.Domain.Entities;
using System.Threading.Tasks;
using System.Collections.Generic;

namespace ItAssetsSystem.Core.Abstractions
{
    public interface INotificationService
    {
        /// <summary>
        /// 创建通知
        /// </summary>
        Task<Notification> CreateNotificationAsync(
            int userId,
            string title,
            string content,
            string type,
            string resourceType = null,
            long? resourceId = null,
            int? triggeredByUserId = null,
            string priority = "Normal");

        /// <summary>
        /// 批量创建通知
        /// </summary>
        Task CreateNotificationsAsync(List<Notification> notifications);

        /// <summary>
        /// 获取用户通知列表
        /// </summary>
        Task<List<Notification>> GetUserNotificationsAsync(int userId, int page = 1, int pageSize = 20, bool? isRead = null);

        /// <summary>
        /// 获取用户未读通知数量
        /// </summary>
        Task<int> GetUnreadCountAsync(int userId);

        /// <summary>
        /// 获取用户通知总数量
        /// </summary>
        Task<int> GetTotalCountAsync(int userId);

        /// <summary>
        /// 标记通知为已读
        /// </summary>
        Task MarkAsReadAsync(long notificationId, int userId);

        /// <summary>
        /// 批量标记为已读
        /// </summary>
        Task MarkAllAsReadAsync(int userId);

        /// <summary>
        /// 删除通知
        /// </summary>
        Task DeleteNotificationAsync(long notificationId, int userId);

        /// <summary>
        /// 任务相关通知：评论
        /// </summary>
        Task NotifyTaskCommentAsync(long taskId, long commentId, int commentedByUserId, string commentContent);

        /// <summary>
        /// 任务相关通知：状态变更
        /// </summary>
        Task NotifyTaskStatusChangedAsync(long taskId, string oldStatus, string newStatus, int changedByUserId);

        /// <summary>
        /// 任务相关通知：负责人变更
        /// </summary>
        Task NotifyTaskAssigneeChangedAsync(long taskId, List<int> oldAssigneeIds, List<int> newAssigneeIds, int changedByUserId);

        /// <summary>
        /// 任务相关通知：任务逾期
        /// </summary>
        Task NotifyTaskOverdueAsync(long taskId);

        /// <summary>
        /// 任务相关通知：任务内容变更
        /// </summary>
        Task NotifyTaskContentChangedAsync(long taskId, string changeDescription, int changedByUserId);

        /// <summary>
        /// 任务相关通知：附件添加
        /// </summary>
        Task NotifyTaskAttachmentAddedAsync(long taskId, long attachmentId, int uploadedByUserId, string fileName);

        /// <summary>
        /// 任务相关通知：@提及用户
        /// </summary>
        Task NotifyTaskMentionAsync(long taskId, List<int> mentionedUserIds, int mentionedByUserId, string content);

        /// <summary>
        /// 任务相关通知：任务创建
        /// </summary>
        Task NotifyTaskCreatedAsync(long taskId, string taskName, int creatorUserId, int? assigneeUserId, string taskType);
    }
}