<!-- File: frontend/src/views/spareparts/MobileSparePartView.vue -->
<!-- Description: 备品备件移动端视图，包含出入库记录和快速操作入口 -->

<template>
  <div class="mobile-spare-part-view">
    <!-- 顶部导航 -->
    <div class="mobile-header">
      <div class="title">备件管理</div>
      <div class="actions">
        <el-button type="primary" size="small" circle icon="Plus" @click="showOperationPanel"></el-button>
        <el-button size="small" circle icon="Search" @click="showFilterDrawer"></el-button>
      </div>
    </div>
    
    <!-- 筛选器抽屉 -->
    <el-drawer v-model="filterDrawerVisible" title="筛选条件" direction="right" size="90%">
      <div class="mobile-filter-form">
        <el-form :model="queryParams" label-position="top">
          <el-form-item label="备件名称">
            <el-input v-model="queryParams.partName" placeholder="输入备件名称"></el-input>
          </el-form-item>
          <el-form-item label="备件编号">
            <el-input v-model="queryParams.partCode" placeholder="输入备件编号"></el-input>
          </el-form-item>
          <el-form-item label="操作类型">
            <el-select v-model="queryParams.type" placeholder="选择操作类型" style="width: 100%">
              <el-option label="全部" :value="null"></el-option>
              <el-option label="入库" :value="1"></el-option>
              <el-option label="出库" :value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="时间范围">
            <el-date-picker v-model="dateRange" type="daterange" style="width: 100%"
              start-placeholder="开始日期" end-placeholder="结束日期" value-format="YYYY-MM-DD"></el-date-picker>
          </el-form-item>
          <div class="form-actions">
            <el-button type="primary" @click="handleQuery">查询</el-button>
            <el-button @click="resetQuery">重置</el-button>
          </div>
        </el-form>
      </div>
    </el-drawer>
    
    <!-- 快速操作面板 -->
    <el-drawer v-model="operationPanelVisible" title="快速操作" direction="right" size="90%">
      <div class="quick-actions">
        <el-button type="primary" @click="goToQuickIn" block>快速入库</el-button>
        <div style="margin: 10px 0;"></div>
        <el-button type="warning" @click="goToQuickOut" block>快速出库</el-button>
      </div>
    </el-drawer>
    
    <!-- 记录卡片列表 -->
    <div class="record-list" v-loading="loading">
      <el-empty v-if="transactionList.length === 0" description="暂无数据"></el-empty>
      
      <div v-for="(item, index) in transactionList" :key="index" class="record-card">
        <div class="card-header">
          <el-tag :type="item.type === 1 ? 'success' : 'warning'">
            {{ item.typeName }}
          </el-tag>
          <span class="card-id">#{{ item.id }}</span>
        </div>
        <div class="card-body">
          <div class="info-row">
            <span class="label">备件</span>
            <span class="value">{{ item.partName }}</span>
          </div>
          <div class="info-row">
            <span class="label">编号</span>
            <span class="value">{{ item.partCode }}</span>
          </div>
          <div class="info-row">
            <span class="label">数量</span>
            <span class="value" :class="item.type === 1 ? 'quantity-in' : 'quantity-out'">
              {{ item.type === 1 ? '+' : '-' }}{{ Math.abs(item.quantity) }}
            </span>
          </div>
          <div class="info-row">
            <span class="label">库位</span>
            <span class="value">{{ item.locationName }}</span>
          </div>
          <div class="info-row">
            <span class="label">原因</span>
            <span class="value">{{ item.reasonTypeName }}</span>
          </div>
          <div class="info-row">
            <span class="label">操作人</span>
            <span class="value">{{ item.operatorName }}</span>
          </div>
          <div class="info-row">
            <span class="label">时间</span>
            <span class="value">{{ formatDateTime(item.operationTime) }}</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 加载更多 -->
    <div class="load-more" v-if="transactionList.length > 0 && !noMoreData">
      <el-button link @click="loadMore" :loading="loadingMore">加载更多</el-button>
    </div>
    <div class="no-more" v-if="noMoreData && transactionList.length > 0">
      <span>没有更多数据了</span>
    </div>
    
    <!-- 底部导航 -->
    <div class="bottom-nav">
      <div class="nav-item" @click="goToHome">
        <el-icon><HomeFilled /></el-icon>
        <span>首页</span>
      </div>
      <div class="nav-item active" @click="goToTransactions">
        <el-icon><Memo /></el-icon>
        <span>出入库</span>
      </div>
      <div class="nav-item" @click="goToSparePartList">
        <el-icon><Tickets /></el-icon>
        <span>备件</span>
      </div>
      <div class="nav-item" @click="goToUserCenter">
        <el-icon><User /></el-icon>
        <span>我的</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import * as sparePartsApi from '@/api/spareparts';
import { 
  HomeFilled, 
  Memo, 
  Tickets, 
  User, 
  Plus, 
  Search 
} from '@element-plus/icons-vue';

// 路由
const router = useRouter();

// 创建响应式状态
const loading = ref(false);
const loadingMore = ref(false);
const transactionList = ref([]);
const total = ref(0);
const dateRange = ref([]);
const filterDrawerVisible = ref(false);
const operationPanelVisible = ref(false);
const noMoreData = ref(false);

// 查询参数
const queryParams = reactive({
  pageIndex: 1,
  pageSize: 10,
  partName: '',
  partCode: '',
  type: '',
  reasonType: '',
  startDate: '',
  endDate: '',
  sortBy: 'operationTime',
  sortOrder: 'desc'
});

// 监听日期范围变化
const watchDateRange = computed(() => {
  if (dateRange.value && dateRange.value.length === 2) {
    queryParams.startDate = dateRange.value[0];
    queryParams.endDate = dateRange.value[1];
  } else {
    queryParams.startDate = '';
    queryParams.endDate = '';
  }
  return dateRange.value;
});

// 生命周期钩子
onMounted(() => {
  fetchTransactionList();
});

// 获取出入库记录数据
const fetchTransactionList = async () => {
  loading.value = true;
  try {
    // 构建查询参数
    const params = {
      pageIndex: queryParams.pageIndex,
      pageSize: queryParams.pageSize,
      partName: queryParams.partName || undefined,
      partCode: queryParams.partCode || undefined,
      type: queryParams.type || undefined,
      reasonType: queryParams.reasonType || undefined,
      startDate: queryParams.startDate || undefined,
      endDate: queryParams.endDate || undefined,
      sortBy: queryParams.sortBy || undefined,
      sortOrder: queryParams.sortOrder || undefined
    };

    const response = await sparePartsApi.getSparePartTransactions(params);
    if (response.success) {
      transactionList.value = response.data.items;
      total.value = response.data.totalCount;
      noMoreData.value = transactionList.value.length >= total.value;
    } else {
      ElMessage.error(response.message || '获取出入库记录失败');
    }
  } catch (error) {
    console.error('获取出入库记录出错:', error);
    ElMessage.error('获取出入库记录失败，请稍后重试');
  } finally {
    loading.value = false;
  }
};

// 加载更多数据
const loadMore = async () => {
  if (loadingMore.value || noMoreData.value) return;
  
  loadingMore.value = true;
  queryParams.pageIndex += 1;
  
  try {
    const params = {
      pageIndex: queryParams.pageIndex,
      pageSize: queryParams.pageSize,
      partName: queryParams.partName || undefined,
      partCode: queryParams.partCode || undefined,
      type: queryParams.type || undefined,
      reasonType: queryParams.reasonType || undefined,
      startDate: queryParams.startDate || undefined,
      endDate: queryParams.endDate || undefined,
      sortBy: queryParams.sortBy || undefined,
      sortOrder: queryParams.sortOrder || undefined
    };

    const response = await sparePartsApi.getSparePartTransactions(params);
    if (response.success) {
      transactionList.value = [...transactionList.value, ...response.data.items];
      noMoreData.value = transactionList.value.length >= total.value;
    } else {
      ElMessage.error(response.message || '加载更多数据失败');
    }
  } catch (error) {
    console.error('加载更多数据出错:', error);
    ElMessage.error('加载更多数据失败，请稍后重试');
  } finally {
    loadingMore.value = false;
  }
};

// 格式化日期时间
const formatDateTime = (dateTimeStr) => {
  if (!dateTimeStr) return '';
  const date = new Date(dateTimeStr);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    hour12: false
  });
};

// 处理查询
const handleQuery = () => {
  queryParams.pageIndex = 1;
  filterDrawerVisible.value = false;
  fetchTransactionList();
};

// 重置查询
const resetQuery = () => {
  queryParams.partName = '';
  queryParams.partCode = '';
  queryParams.type = '';
  queryParams.reasonType = '';
  dateRange.value = [];
  queryParams.startDate = '';
  queryParams.endDate = '';
  queryParams.pageIndex = 1;
};

// 显示筛选抽屉
const showFilterDrawer = () => {
  filterDrawerVisible.value = true;
};

// 显示操作面板
const showOperationPanel = () => {
  operationPanelVisible.value = true;
};

// 导航到快速入库页面
const goToQuickIn = () => {
  operationPanelVisible.value = false;
  router.push('/main/spareparts/mobile/quick-in');
};

// 导航到快速出库页面
const goToQuickOut = () => {
  operationPanelVisible.value = false;
  router.push('/main/spareparts/mobile/quick-out');
};

// 导航到首页
const goToHome = () => {
  router.push('/main/dashboard');
};

// 导航到出入库记录
const goToTransactions = () => {
  // 已在当前页面，不需要导航
};

// 导航到备件列表
const goToSparePartList = () => {
  router.push('/main/spareparts/list');
};

// 导航到用户中心
const goToUserCenter = () => {
  router.push('/main/user/profile');
};
</script>

<style scoped>
.mobile-spare-part-view {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f7fa;
  padding-bottom: 60px; /* 为底部导航预留空间 */
}

.mobile-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background-color: #fff;
  box-shadow: 0 1px 4px rgba(0,0,0,0.1);
  position: sticky;
  top: 0;
  z-index: 10;
}

.mobile-header .title {
  font-size: 18px;
  font-weight: bold;
}

.mobile-header .actions {
  display: flex;
  gap: 10px;
}

.record-list {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
}

.record-card {
  background-color: #fff;
  border-radius: 8px;
  margin-bottom: 12px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.card-header {
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
}

.card-id {
  color: #999;
  font-size: 14px;
}

.card-body {
  padding: 12px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.info-row .label {
  color: #666;
  font-size: 14px;
}

.info-row .value {
  font-weight: 500;
  text-align: right;
}

.quantity-in {
  color: #67c23a;
  font-weight: bold;
}

.quantity-out {
  color: #f56c6c;
  font-weight: bold;
}

.mobile-filter-form {
  padding: 15px;
}

.form-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
}

.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  display: flex;
  height: 60px;
  box-shadow: 0 -1px 4px rgba(0,0,0,0.1);
}

.nav-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #666;
  font-size: 12px;
}

.nav-item.active {
  color: var(--el-color-primary);
}

.load-more, .no-more {
  text-align: center;
  padding: 15px 0;
  color: #999;
}

.quick-actions {
  padding: 15px;
}
</style> 