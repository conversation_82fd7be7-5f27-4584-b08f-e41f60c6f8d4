import{_ as e,r as a,z as l,E as t,b as d,d as u,e as n,w as r,f as o,aG as s,aE as i,aD as p,a as c,m,o as v,p as g,l as f,k as h,t as y,$ as b,b1 as I,aJ as _,F as w,h as V,ax as k,a9 as C}from"./index-CG5lHOPO.js";import{d as M,p as x}from"./personnel-C5IStqeW.js";const U={class:"department-management-container"},D={class:"page-header"},N={class:"page-actions"},$={class:"personnel-option"},A={class:"personnel-code"},L={class:"personnel-option"},P={class:"personnel-code"},z=e({__name:"departments",setup(e){const z=a(!1),E=a(!1),R=a(null),T=a([]),j=a([]),q=a(!1),B=a(""),S=a(!1),F=a({id:void 0,name:"",code:"",parentId:null,managerId:null,deputyManagerId:null,phone:"",email:"",description:"",sort:0,status:"active"}),G=a({name:[{required:!0,message:"部门名称不能为空",trigger:"blur"}],code:[{required:!0,message:"部门编码不能为空",trigger:"blur"}]}),J=a(null);l((()=>{H(),K()}));const H=async()=>{z.value=!0;try{const e=await M.getDepartmentList();e.data&&e.data.success?T.value=O(e.data.data):t.error("获取部门数据失败")}catch(e){t.error("获取部门列表失败: "+(e.message||"服务器错误"))}finally{z.value=!1}},K=async()=>{E.value=!0;try{const e=await x.getPersonnelList({});e.data&&e.data.success?j.value=e.data.data:t.error("获取人员数据失败")}catch(e){t.error("获取人员数据失败: "+(e.message||"服务器错误"))}finally{E.value=!1}return Promise.resolve()},O=e=>{if(!e||!Array.isArray(e))return[];const a=[],l={};return e.forEach((e=>{l[e.id]={...e,id:Number(e.id),parentId:e.parentId?Number(e.parentId):null,managerId:e.managerId?Number(e.managerId):null,deputyManagerId:void 0!==e.deputyManagerId?Number(e.deputyManagerId):null,manager:e.manager||"",phone:e.phone||"",email:e.email||"",userCount:e.userCount||0,sort:e.sort||0,status:e.isActive?"active":"disabled",children:[]}})),e.forEach((e=>{if(e.parentId){const t=l[e.parentId];t?t.children.push(l[e.id]):a.push(l[e.id])}else a.push(l[e.id])})),a},Q=()=>{R.value.expandAllRows()},W=()=>{R.value.collapseAllRows()},X=()=>{B.value="新建部门",F.value={id:void 0,name:"",code:"",parentId:null,managerId:null,deputyManagerId:null,phone:"",email:"",description:"",sort:0,status:"active"},q.value=!0,E.value=!0,x.getPersonnelList().then((e=>{e.data&&e.data.success?j.value=e.data.data:t.error("获取人员数据失败"),E.value=!1})).catch((e=>{t.error("获取人员数据失败: "+(e.message||"服务器错误")),E.value=!1})),k((()=>{J.value&&J.value.clearValidate()}))},Y=async()=>{var e;if(J.value)try{await J.value.validate(),S.value=!0;const a={name:F.value.name,code:F.value.code,description:F.value.description||"",parentId:F.value.parentId,managerId:F.value.managerId,deputyManagerId:F.value.deputyManagerId,status:F.value.status};let l;l=F.value.id?await M.updateDepartment(F.value.id,a):await M.createDepartment(a),l.data&&l.data.success?(t.success(F.value.id?"部门更新成功":"部门创建成功"),q.value=!1,H()):t.error((null==(e=l.data)?void 0:e.message)||(F.value.id?"部门更新失败":"部门创建失败"))}catch(a){t.error("操作失败: "+(a.message||"服务器错误"))}finally{S.value=!1}},Z=()=>{q.value=!1},ee=e=>{if(!e)return"";const a=Number(e);if(!j.value||0===j.value.length)return`ID: ${a}`;let l=j.value.find((e=>e.id===a));return l||(l=j.value.find((e=>String(e.id)===String(a)))),l?l.name:`未知(ID:${a})`};return(e,a)=>{const l=c("el-button"),K=c("el-table-column"),O=c("el-tag"),ae=c("el-table"),le=c("el-card"),te=c("el-input"),de=c("el-form-item"),ue=c("el-option"),ne=c("el-select"),re=c("el-input-number"),oe=c("el-form"),se=c("el-dialog"),ie=m("loading");return v(),d("div",U,[u("div",D,[a[13]||(a[13]=u("h2",{class:"page-title"},"部门管理",-1)),u("div",N,[n(l,{type:"primary",onClick:X,icon:o(s)},{default:r((()=>a[10]||(a[10]=[g(" 新建部门 ")]))),_:1},8,["icon"]),n(l,{type:"success",onClick:Q,icon:o(i)},{default:r((()=>a[11]||(a[11]=[g(" 展开所有 ")]))),_:1},8,["icon"]),n(l,{type:"info",onClick:W,icon:o(p)},{default:r((()=>a[12]||(a[12]=[g(" 折叠所有 ")]))),_:1},8,["icon"])])]),n(le,{class:"data-card"},{default:r((()=>[f((v(),h(ae,{ref_key:"departmentTable",ref:R,data:T.value,"row-key":"id",border:"","default-expand-all":"","tree-props":{children:"children",hasChildren:"hasChildren"}},{default:r((()=>[n(K,{prop:"name",label:"部门名称","min-width":"180"}),n(K,{prop:"code",label:"部门编码",width:"150"}),n(K,{label:"部门经理",width:"120"},{default:r((e=>[u("span",null,y(ee(e.row.managerId)),1)])),_:1}),n(K,{label:"部门主任",width:"120"},{default:r((e=>[u("span",null,y(ee(e.row.deputyManagerId)),1)])),_:1}),n(K,{prop:"phone",label:"联系电话",width:"150"}),n(K,{prop:"email",label:"邮箱","min-width":"200","show-overflow-tooltip":""}),n(K,{prop:"userCount",label:"用户数",width:"100",align:"center"}),n(K,{prop:"sort",label:"排序",width:"80",align:"center"}),n(K,{prop:"status",label:"状态",width:"100",align:"center"},{default:r((e=>["active"===e.row.status?(v(),h(O,{key:0,type:"success"},{default:r((()=>a[14]||(a[14]=[g("正常")]))),_:1})):"disabled"===e.row.status?(v(),h(O,{key:1,type:"info"},{default:r((()=>a[15]||(a[15]=[g("禁用")]))),_:1})):b("",!0)])),_:1}),n(K,{label:"操作",width:"250",fixed:"right"},{default:r((e=>[n(l,{type:"text",size:"small",onClick:a=>{return l=e.row,B.value=`添加"${l.name}"的子部门`,F.value={id:void 0,name:"",code:"",parentId:l.id,managerId:null,deputyManagerId:null,phone:"",email:"",description:"",sort:0,status:"active"},q.value=!0,E.value=!0,x.getPersonnelList().then((e=>{e.data&&e.data.success?j.value=e.data.data:t.error("获取人员数据失败"),E.value=!1})).catch((e=>{t.error("获取人员数据失败: "+(e.message||"服务器错误")),E.value=!1})),void k((()=>{J.value&&J.value.clearValidate()}));var l},icon:o(s)},{default:r((()=>a[16]||(a[16]=[g(" 添加下级 ")]))),_:2},1032,["onClick","icon"]),n(l,{type:"text",size:"small",onClick:a=>(e=>{B.value=`编辑部门"${e.name}"`,q.value=!0;const a={...e,deputyManagerId:void 0!==e.deputyManagerId?e.deputyManagerId:null};F.value={...a,managerId:null,deputyManagerId:null},E.value=!0,x.getPersonnelList().then((e=>{e.data&&e.data.success?(j.value=e.data.data,k((()=>{F.value.managerId=a.managerId?Number(a.managerId):null,F.value.deputyManagerId=a.deputyManagerId?Number(a.deputyManagerId):null}))):t.error("获取人员数据失败"),E.value=!1})).catch((e=>{t.error("获取人员数据失败: "+(e.message||"服务器错误")),E.value=!1})),k((()=>{J.value&&J.value.clearValidate()}))})(e.row),icon:o(I)},{default:r((()=>a[17]||(a[17]=[g(" 编辑 ")]))),_:2},1032,["onClick","icon"]),n(l,{type:"text",size:"small",onClick:a=>{var l;(l=e.row).children&&l.children.length>0?t.warning("该部门下有子部门，请先删除子部门"):l.userCount>0?t.warning("该部门下有用户，不能直接删除"):C.confirm(`确定要删除部门"${l.name}"吗？`,"删除部门",{confirmButtonText:"确定",cancelButtonText:"取消",type:"error"}).then((async()=>{var e;try{z.value=!0;const a=await M.deleteDepartment(l.id);a.data&&a.data.success?(t.success("部门已删除"),H()):t.error((null==(e=a.data)?void 0:e.message)||"删除部门失败")}catch(a){t.error("删除部门失败: "+(a.message||"服务器错误"))}finally{z.value=!1}})).catch((()=>{}))},icon:o(_)},{default:r((()=>a[18]||(a[18]=[g(" 删除 ")]))),_:2},1032,["onClick","icon"])])),_:1})])),_:1},8,["data"])),[[ie,z.value]])])),_:1}),n(se,{modelValue:q.value,"onUpdate:modelValue":a[9]||(a[9]=e=>q.value=e),title:B.value,width:"50%","before-close":Z},{footer:r((()=>[n(l,{onClick:Z},{default:r((()=>a[19]||(a[19]=[g("取消")]))),_:1}),n(l,{type:"primary",loading:S.value,onClick:Y},{default:r((()=>a[20]||(a[20]=[g("确定")]))),_:1},8,["loading"])])),default:r((()=>[n(oe,{ref_key:"formRef",ref:J,model:F.value,rules:G.value,"label-width":"120px"},{default:r((()=>[n(de,{label:"部门名称",prop:"name"},{default:r((()=>[n(te,{modelValue:F.value.name,"onUpdate:modelValue":a[0]||(a[0]=e=>F.value.name=e)},null,8,["modelValue"])])),_:1}),n(de,{label:"部门编码",prop:"code"},{default:r((()=>[n(te,{modelValue:F.value.code,"onUpdate:modelValue":a[1]||(a[1]=e=>F.value.code=e)},null,8,["modelValue"])])),_:1}),n(de,{label:"部门经理",prop:"managerId"},{default:r((()=>[n(ne,{modelValue:F.value.managerId,"onUpdate:modelValue":a[2]||(a[2]=e=>F.value.managerId=e),filterable:"",clearable:"",placeholder:"请选择部门经理",loading:E.value},{default:r((()=>[(v(!0),d(w,null,V(j.value,(e=>(v(),h(ue,{key:e.id,label:e.name,value:e.id},{default:r((()=>[u("div",$,[u("span",null,y(e.name),1),u("span",A,"("+y(e.employeeCode)+")",1)])])),_:2},1032,["label","value"])))),128))])),_:1},8,["modelValue","loading"])])),_:1}),n(de,{label:"部门主任",prop:"deputyManagerId"},{default:r((()=>[n(ne,{modelValue:F.value.deputyManagerId,"onUpdate:modelValue":a[3]||(a[3]=e=>F.value.deputyManagerId=e),filterable:"",clearable:"",placeholder:"请选择部门主任",loading:E.value},{default:r((()=>[(v(!0),d(w,null,V(j.value,(e=>(v(),h(ue,{key:e.id,label:e.name,value:e.id},{default:r((()=>[u("div",L,[u("span",null,y(e.name),1),u("span",P,"("+y(e.employeeCode)+")",1)])])),_:2},1032,["label","value"])))),128))])),_:1},8,["modelValue","loading"])])),_:1}),n(de,{label:"联系电话",prop:"phone"},{default:r((()=>[n(te,{modelValue:F.value.phone,"onUpdate:modelValue":a[4]||(a[4]=e=>F.value.phone=e)},null,8,["modelValue"])])),_:1}),n(de,{label:"邮箱",prop:"email"},{default:r((()=>[n(te,{modelValue:F.value.email,"onUpdate:modelValue":a[5]||(a[5]=e=>F.value.email=e)},null,8,["modelValue"])])),_:1}),n(de,{label:"描述",prop:"description"},{default:r((()=>[n(te,{modelValue:F.value.description,"onUpdate:modelValue":a[6]||(a[6]=e=>F.value.description=e),type:"textarea"},null,8,["modelValue"])])),_:1}),n(de,{label:"排序",prop:"sort"},{default:r((()=>[n(re,{modelValue:F.value.sort,"onUpdate:modelValue":a[7]||(a[7]=e=>F.value.sort=e),min:0,max:100},null,8,["modelValue"])])),_:1}),n(de,{label:"状态",prop:"status"},{default:r((()=>[n(ne,{modelValue:F.value.status,"onUpdate:modelValue":a[8]||(a[8]=e=>F.value.status=e),placeholder:"请选择状态"},{default:r((()=>[n(ue,{label:"正常",value:"active"}),n(ue,{label:"禁用",value:"disabled"})])),_:1},8,["modelValue"])])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["modelValue","title"])])}}},[["__scopeId","data-v-1417501b"]]);export{z as default};
