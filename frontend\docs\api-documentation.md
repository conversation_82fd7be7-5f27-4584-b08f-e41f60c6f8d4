# IT资产管理系统API文档

## 目录
1. [通用信息](#1-通用信息)
2. [认证与授权](#2-认证与授权)
3. [资产管理API](#3-资产管理api)
4. [位置管理API](#4-位置管理api)
5. [采购管理API](#5-采购管理api)
6. [故障管理API](#6-故障管理api)
7. [任务管理API](#7-任务管理api)
8. [用户与权限管理API](#8-用户与权限管理api)
9. [系统配置API](#9-系统配置api)
10. [数据导入导出API](#10-数据导入导出api)
11. [备份与恢复API](#11-备份与恢复api)
12. [核心业务流程示例](#12-核心业务流程示例)
9. [PDCA改进计划API](#9-pdca改进计划api)

## 1. 通用信息

### 1.1 基础URL
所有API请求都使用以下基本URL:
```
http://localhost:5001/api
```

### 1.2 通用请求头
| 头部名称 | 描述 |
|---------|------|
| Authorization | Bearer {token} 格式的JWT令牌 |
| Content-Type | application/json |
| Accept | application/json |

### 1.3 通用响应格式
所有API响应都使用JSON格式，标准结构如下:

```json
{
  "success": true,
  "data": { ... },
  "message": "操作成功",
  "error": null,
  "timestamp": "2025-03-22T12:34:56.789Z"
}
```

错误响应格式:
```json
{
  "success": false,
  "data": null,
  "message": "操作失败",
  "error": {
    "code": "ERROR_CODE",
    "details": "详细错误信息"
  },
  "timestamp": "2025-03-22T12:34:56.789Z"
}
```

### 1.4 分页机制
对于返回多条记录的API，支持标准分页参数:

**请求参数:**
- `page`: 页码，从1开始（默认：1）
- `size`: 每页记录数（默认：20，最大：100）
- `sort`: 排序字段（格式：`field,direction`，例如：`name,asc`）

**分页响应格式:**
```json
{
  "success": true,
  "data": {
    "items": [ ... ],
    "pagination": {
      "page": 1,
      "size": 20,
      "totalItems": 135,
      "totalPages": 7
    }
  },
  "message": "操作成功",
  "error": null,
  "timestamp": "2025-03-22T12:34:56.789Z"
}
```

### 1.5 通用HTTP状态码
| 状态码 | 描述 |
|-------|------|
| 200 | 请求成功 |
| 201 | 资源创建成功 |
| 204 | 请求成功，无返回内容 |
| 400 | 请求参数错误 |
| 401 | 未授权或认证令牌无效 |
| 403 | 禁止访问，权限不足 |
| 404 | 资源不存在 |
| 409 | 资源冲突 |
| 422 | 请求参数验证失败 |
| 500 | 服务器内部错误 |

### 1.6 通用错误码
| 错误码 | 描述 |
|-------|------|
| UNAUTHORIZED | 未授权或认证令牌无效 |
| FORBIDDEN | 权限不足 |
| NOT_FOUND | 资源不存在 |
| VALIDATION_ERROR | 参数验证失败 |
| CONFLICT | 资源冲突 |
| INTERNAL_ERROR | 服务器内部错误 |
| NETWORK_ERROR | 网络连接错误 |

### 1.7 版本控制策略
API版本通过URL路径中的版本号指定，当前为V1：
```
/api/v1/assets
```

未来版本升级遵循以下兼容性原则：
- 不会删除现有字段
- 不会改变现有字段的数据类型
- 新字段将被标记为可选
- 弃用的字段将在新版本中标记，并在至少一个版本周期内保持向后兼容

## 2. 认证与授权

### 2.1 用户登录
**请求:**
- 方法: `POST`
- URL: `/api/user/login`
- 描述: 用户登录并获取令牌

**请求体:**
```json
{
  "username": "admin",
  "password": "password123"
}
```

**参数说明:**
| 参数名 | 类型 | 必需 | 描述 |
|-------|------|-----|------|
| username | string | 是 | 用户名 |
| password | string | 是 | 密码 |

**成功响应:**
- 状态码: `200`
```json
{
  "success": true,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": 1,
      "username": "admin",
      "name": "系统管理员",
      "roles": ["系统管理员"],
      "permissions": ["asset:view", "asset:create", "asset:update", "asset:delete"]
    },
    "expiresIn": 86400
  },
  "message": "登录成功",
  "error": null,
  "timestamp": "2025-03-22T12:34:56.789Z"
}
```

**错误响应:**
- 状态码: `401`
```json
{
  "success": false,
  "data": null,
  "message": "用户名或密码错误",
  "error": {
    "code": "UNAUTHORIZED",
    "details": "用户名或密码错误"
  },
  "timestamp": "2025-03-22T12:34:56.789Z"
}
```

### 2.2 刷新令牌
**请求:**
- 方法: `POST`
- URL: `/api/user/refresh-token`
- 描述: 使用刷新令牌获取新的访问令牌

**请求体:**
```json
{
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**参数说明:**
| 参数名 | 类型 | 必需 | 描述 |
|-------|------|-----|------|
| refreshToken | string | 是 | 刷新令牌 |

**成功响应:**
- 状态码: `200`
```json
{
  "success": true,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expiresIn": 86400
  },
  "message": "令牌刷新成功",
  "error": null,
  "timestamp": "2025-03-22T12:34:56.789Z"
}
```

**错误响应:**
- 状态码: `401`
```json
{
  "success": false,
  "data": null,
  "message": "无效的刷新令牌",
  "error": {
    "code": "UNAUTHORIZED",
    "details": "刷新令牌已过期或无效"
  },
  "timestamp": "2025-03-22T12:34:56.789Z"
}
```

### 2.3 登出
**请求:**
- 方法: `POST`
- URL: `/api/user/logout`
- 描述: 用户登出，使当前令牌失效
- 需要认证: 是

**成功响应:**
- 状态码: `200`
```json
{
  "success": true,
  "data": null,
  "message": "登出成功",
  "error": null,
  "timestamp": "2025-03-22T12:34:56.789Z"
}
```

## 3. 资产管理API

### 3.1 获取资产列表
**请求:**
- 方法: `GET`
- URL: `/api/assets`
- 描述: 获取资产列表，支持分页和筛选
- 需要认证: 是
- 所需权限: `asset:view`

**查询参数:**
| 参数名 | 类型 | 必需 | 描述 |
|-------|------|-----|------|
| page | integer | 否 | 页码，默认1 |
| size | integer | 否 | 每页记录数，默认20 |
| status | integer | 否 | 资产状态筛选 |
| type | integer | 否 | 资产类型ID筛选 |
| locationId | integer | 否 | 位置ID筛选 |
| departmentId | integer | 否 | 部门ID筛选 |
| keyword | string | 否 | 关键词搜索（名称、编码、序列号） |
| startDate | string | 否 | 购买日期起始（YYYY-MM-DD） |
| endDate | string | 否 | 购买日期截止（YYYY-MM-DD） |

**成功响应:**
- 状态码: `200`
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": 1,
        "assetCode": "A-2025-0001",
        "name": "ThinkPad X1 Carbon",
        "assetType": {
          "id": 1,
          "name": "笔记本电脑"
        },
        "serialNumber": "**********",
        "model": "X1 Carbon Gen 10",
        "brand": "Lenovo",
        "purchaseDate": "2025-01-15",
        "warrantyExpireDate": "2028-01-14",
        "price": 12999.99,
        "location": {
          "id": 5,
          "name": "研发部工位A",
          "fullPath": "总部/研发中心/研发部/工位A"
        },
        "status": 1,
        "statusName": "使用中",
        "createdAt": "2025-01-16T08:30:00Z",
        "updatedAt": "2025-01-16T08:30:00Z"
      },
      // 更多资产...
    ],
    "pagination": {
      "page": 1,
      "size": 20,
      "totalItems": 135,
      "totalPages": 7
    }
  },
  "message": "获取资产列表成功",
  "error": null,
  "timestamp": "2025-03-22T12:34:56.789Z"
}
```

### 3.2 获取资产详情
**请求:**
- 方法: `GET`
- URL: `/api/assets/{id}`
- 描述: 获取指定ID的资产详细信息
- 需要认证: 是
- 所需权限: `asset:view`

**路径参数:**
| 参数名 | 类型 | 必需 | 描述 |
|-------|------|-----|------|
| id | integer | 是 | 资产ID |

**成功响应:**
- 状态码: `200`
```json
{
  "success": true,
  "data": {
    "id": 1,
    "assetCode": "A-2025-0001",
    "name": "ThinkPad X1 Carbon",
    "assetTypeId": 1,
    "assetType": {
      "id": 1,
      "name": "笔记本电脑",
      "code": "LAPTOP",
      "description": "便携式计算机设备"
    },
    "serialNumber": "**********",
    "model": "X1 Carbon Gen 10",
    "brand": "Lenovo",
    "purchaseDate": "2025-01-15",
    "warrantyExpireDate": "2028-01-14",
    "price": 12999.99,
    "locationId": 5,
    "location": {
      "id": 5,
      "name": "研发部工位A",
      "code": "RD-WS-A",
      "fullPath": "总部/研发中心/研发部/工位A",
      "department": {
        "id": 3,
        "name": "研发部"
      }
    },
    "status": 1,
    "statusName": "使用中",
    "notes": "分配给首席工程师使用",
    "createdAt": "2025-01-16T08:30:00Z",
    "updatedAt": "2025-01-16T08:30:00Z",
    "faultRecords": [
      {
        "id": 1,
        "title": "屏幕显示异常",
        "reportTime": "2025-02-10T14:30:00Z",
        "status": 2,
        "statusName": "已修复"
      }
    ],
    "locationHistories": [
      {
        "id": 1,
        "oldLocationId": null,
        "newLocationId": 5,
        "changeTime": "2025-01-16T08:30:00Z",
        "operatorId": 1,
        "operatorName": "系统管理员",
        "reason": "初始分配"
      }
    ]
  },
  "message": "获取资产详情成功",
  "error": null,
  "timestamp": "2025-03-22T12:34:56.789Z"
}
```

**错误响应:**
- 状态码: `404`
```json
{
  "success": false,
  "data": null,
  "message": "资产不存在",
  "error": {
    "code": "NOT_FOUND",
    "details": "ID为1的资产不存在"
  },
  "timestamp": "2025-03-22T12:34:56.789Z"
}
```

### 3.3 创建资产
**请求:**
- 方法: `POST`
- URL: `/api/assets`
- 描述: 创建新资产
- 需要认证: 是
- 所需权限: `asset:create`

**请求体:**
```json
{
  "name": "ThinkPad X1 Carbon",
  "assetTypeId": 1,
  "serialNumber": "**********",
  "model": "X1 Carbon Gen 10",
  "brand": "Lenovo",
  "purchaseDate": "2025-01-15",
  "warrantyExpireDate": "2028-01-14",
  "price": 12999.99,
  "locationId": 5,
  "status": 1,
  "notes": "分配给首席工程师使用"
}
```

**参数说明:**
| 参数名 | 类型 | 必需 | 描述 |
|-------|------|-----|------|
| name | string | 是 | 资产名称 |
| assetTypeId | integer | 是 | 资产类型ID |
| serialNumber | string | 是 | 序列号 |
| model | string | 否 | 型号 |
| brand | string | 否 | 品牌 |
| purchaseDate | string | 否 | 购买日期（YYYY-MM-DD） |
| warrantyExpireDate | string | 否 | 保修到期日（YYYY-MM-DD） |
| price | decimal | 否 | 价格 |
| locationId | integer | 否 | 位置ID |
| status | integer | 是 | 状态（0:库存, 1:使用中, 2:维修中, 3:闲置, 4:报废） |
| notes | string | 否 | 备注 |

**成功响应:**
- 状态码: `201`
```json
{
  "success": true,
  "data": {
    "id": 1,
    "assetCode": "A-2025-0001",
    "name": "ThinkPad X1 Carbon",
    "assetTypeId": 1,
    "serialNumber": "**********",
    "model": "X1 Carbon Gen 10",
    "brand": "Lenovo",
    "purchaseDate": "2025-01-15",
    "warrantyExpireDate": "2028-01-14",
    "price": 12999.99,
    "locationId": 5,
    "status": 1,
    "statusName": "使用中",
    "notes": "分配给首席工程师使用",
    "createdAt": "2025-03-22T12:34:56.789Z",
    "updatedAt": "2025-03-22T12:34:56.789Z"
  },
  "message": "资产创建成功",
  "error": null,
  "timestamp": "2025-03-22T12:34:56.789Z"
}
```

**错误响应:**
- 状态码: `400`
```json
{
  "success": false,
  "data": null,
  "message": "参数验证失败",
  "error": {
    "code": "VALIDATION_ERROR",
    "details": {
      "name": "资产名称不能为空",
      "assetTypeId": "必须选择有效的资产类型",
      "serialNumber": "序列号不能为空"
    }
  },
  "timestamp": "2025-03-22T12:34:56.789Z"
}
```

### 3.4 更新资产
**请求:**
- 方法: `PUT`
- URL: `/api/assets/{id}`
- 描述: 更新指定ID的资产信息
- 需要认证: 是
- 所需权限: `asset:update`

**路径参数:**
| 参数名 | 类型 | 必需 | 描述 |
|-------|------|-----|------|
| id | integer | 是 | 资产ID |

**请求体:**
```json
{
  "name": "ThinkPad X1 Carbon",
  "assetTypeId": 1,
  "serialNumber": "**********",
  "model": "X1 Carbon Gen 10",
  "brand": "Lenovo",
  "purchaseDate": "2025-01-15",
  "warrantyExpireDate": "2028-01-14",
  "price": 12999.99,
  "locationId": 6,
  "status": 1,
  "notes": "更新：分配给研发主管使用"
}
```

**参数说明:**
| 参数名 | 类型 | 必需 | 描述 |
|-------|------|-----|------|
| name | string | 是 | 资产名称 |
| assetTypeId | integer | 是 | 资产类型ID |
| serialNumber | string | 是 | 序列号 |
| model | string | 否 | 型号 |
| brand | string | 否 | 品牌 |
| purchaseDate | string | 否 | 购买日期（YYYY-MM-DD） |
| warrantyExpireDate | string | 否 | 保修到期日（YYYY-MM-DD） |
| price | decimal | 否 | 价格 |
| locationId | integer | 否 | 位置ID |
| status | integer | 是 | 状态（0:库存, 1:使用中, 2:维修中, 3:闲置, 4:报废） |
| notes | string | 否 | 备注 |

**成功响应:**
- 状态码: `200`
```json
{
  "success": true,
  "data": {
    "id": 1,
    "assetCode": "A-2025-0001",
    "name": "ThinkPad X1 Carbon",
    "assetTypeId": 1,
    "serialNumber": "**********",
    "model": "X1 Carbon Gen 10",
    "brand": "Lenovo",
    "purchaseDate": "2025-01-15",
    "warrantyExpireDate": "2028-01-14",
    "price": 12999.99,
    "locationId": 6,
    "status": 1,
    "statusName": "使用中",
    "notes": "更新：分配给研发主管使用",
    "createdAt": "2025-01-16T08:30:00Z",
    "updatedAt": "2025-03-22T12:34:56.789Z"
  },
  "message": "资产更新成功",
  "error": null,
  "timestamp": "2025-03-22T12:34:56.789Z"
}
```

### 3.5 变更资产位置
**请求:**
- 方法: `POST`
- URL: `/api/Asset/{id}/change-location`
- 描述: 变更资产位置，并记录位置变更历史
- 需要认证: 是
- 所需权限: `asset:update`

**路径参数:**
| 参数名 | 类型 | 必需 | 描述 |
|-------|------|-----|------|
| id | integer | 是 | 资产ID |

**请求体:**
```json
{
  "newLocationId": 6,
  "reason": "部门内部调整",
  "notes": "从工位A调整到工位B"
}
```

**参数说明:**
| 参数名 | 类型 | 必需 | 描述 |
|-------|------|-----|------|
| newLocationId | integer | 是 | 新位置ID |
| reason | string | 是 | 变更原因 |
| notes | string | 否 | 备注说明 |

**成功响应:**
- 状态码: `200`
```json
{
  "success": true,
  "data": {
    "id": 1,
    "assetCode": "IT-PC-001",
    "name": "办公电脑",
    "oldLocationId": 5,
    "oldLocationName": "研发部工位A",
    "newLocationId": 6,
    "newLocationName": "研发部工位B",
    "changeTime": "2025-03-22T12:34:56.789Z",
    "reason": "部门内部调整"
  },
  "message": "资产位置变更成功",
  "error": null,
  "timestamp": "2025-03-22T12:34:56.789Z"
}
```

**错误响应:**
- 状态码: `400`, `404` 或 `500`
```json
{
  "success": false,
  "data": null,
  "message": "新位置与当前位置相同",
  "error": {
    "code": "VALIDATION_ERROR",
    "details": "新位置与当前位置相同"
  },
  "timestamp": "2025-03-22T12:34:56.789Z"
}
```

### 3.6 变更资产状态
**请求:**
- 方法: `POST`
- URL: `/api/assets/{id}/change-status`
- 描述: 变更资产状态
- 需要认证: 是
- 所需权限: `asset:update`

**路径参数:**
| 参数名 | 类型 | 必需 | 描述 |
|-------|------|-----|------|
| id | integer | 是 | 资产ID |

**请求体:**
```json
{
  "status": 2,
  "reason": "设备故障，送修",
  "notes": "屏幕显示异常，送到维修中心维修"
}
```

**参数说明:**
| 参数名 | 类型 | 必需 | 描述 |
|-------|------|-----|------|
| status | integer | 是 | 新状态（0:库存, 1:使用中, 2:维修中, 3:闲置, 4:报废） |
| reason | string | 是 | 变更原因 |
| notes | string | 否 | 备注说明 |

**成功响应:**
- 状态码: `200`
```json
{
  "success": true,
  "data": {
    "id": 1,
    "assetCode": "A-2025-0001",
    "name": "ThinkPad X1 Carbon",
    "status": 2,
    "statusName": "维修中",
    "updatedAt": "2025-03-22T12:34:56.789Z"
  },
  "message": "资产状态变更成功",
  "error": null,
  "timestamp": "2025-03-22T12:34:56.789Z"
}
```

### 3.7 获取资产类型列表
**请求:**
- 方法: `GET`
- URL: `/api/asset-types`
- 描述: 获取所有资产类型
- 需要认证: 是
- 所需权限: `asset:view`

**查询参数:**
| 参数名 | 类型 | 必需 | 描述 |
|-------|------|-----|------|
| parentId | integer | 否 | 父类型ID，获取指定父类型下的子类型 |
| isActive | boolean | 否 | 是否激活 |

**成功响应:**
- 状态码: `200`
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "电脑设备",
      "code": "COMPUTER",
      "parentId": null,
      "description": "各类计算机设备",
      "sortOrder": 1,
      "isActive": true,
      "children": [
        {
          "id": 2,
          "name": "笔记本电脑",
          "code": "LAPTOP",
          "parentId": 1,
          "description": "便携式计算机",
          "sortOrder": 1,
          "isActive": true,
          "children": []
        },
        {
          "id": 3,
          "name": "台式电脑",
          "code": "DESKTOP",
          "parentId": 1,
          "description": "固定式计算机",
          "sortOrder": 2,
          "isActive": true,
          "children": []
        }
      ]
    },
    // 更多资产类型...
  ],
  "message": "获取资产类型列表成功",
  "error": null,
  "timestamp": "2025-03-22T12:34:56.789Z"
}
```

### 3.8 获取资产位置历史
**请求:**
- 方法: `GET`
- URL: `/api/assets/{id}/location-history`
- 描述: 获取资产位置变更历史
- 需要认证: 是
- 所需权限: `asset:view`

**路径参数:**
| 参数名 | 类型 | 必需 | 描述 |
|-------|------|-----|------|
| id | integer | 是 | 资产ID |

**查询参数:**
| 参数名 | 类型 | 必需 | 描述 |
|-------|------|-----|------|
| page | integer | 否 | 页码，默认1 |
| size | integer | 否 | 每页记录数，默认20 |

**成功响应:**
- 状态码: `200`
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": 2,
        "assetId": 1,
        "assetCode": "A-2025-0001",
        "oldLocationId": 5,
        "oldLocationName": "研发部工位A",
        "newLocationId": 6,
        "newLocationName": "研发部工位B",
        "operatorId": 1,
        "operatorName": "系统管理员",
        "changeTime": "2025-03-22T12:34:56.789Z",
        "reason": "部门内部调整",
        "notes": "从工位A调整到工位B"
      },
      {
        "id": 1,
        "assetId": 1,
        "assetCode": "A-2025-0001",
        "oldLocationId": null,
        "oldLocationName": null,
        "newLocationId": 5,
        "newLocationName": "研发部工位A",
        "operatorId": 1,
        "operatorName": "系统管理员",
        "changeTime": "2025-01-16T08:30:00Z",
        "reason": "初始分配",
        "notes": null
      }
    ],
    "pagination": {
      "page": 1,
      "size": 20,
      "totalItems": 2,
      "totalPages": 1
    }
  },
  "message": "获取资产位置历史成功",
  "error": null,
  "timestamp": "2025-03-22T12:34:56.789Z"
}
```

## 4. 位置管理API

### 4.1 获取位置树
**请求:**
- 方法: `GET`
- URL: `/api/location-tree`
- 描述: 获取完整的位置层级树结构
- 需要认证: 是
- 所需权限: `location:view`

**成功响应:**
- 状态码: `200`
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "code": "HQ",
      "name": "总部",
      "type": 0,
      "typeName": "工厂",
      "parentId": null,
      "description": "公司总部",
      "address": "北京市海淀区",
      "departmentId": 1,
      "departmentName": "总部",
      "isActive": true,
      "children": [
        {
          "id": 2,
          "code": "RD-CENTER",
          "name": "研发中心",
          "type": 1,
          "typeName": "产线",
          "parentId": 1,
          "description": "研发中心",
          "departmentId": 2,
          "departmentName": "研发中心",
          "isActive": true,
          "children": [
            {
              "id": 3,
              "code": "RD-DEPT",
              "name": "研发部",
              "type": 2,
              "typeName": "工序",
              "parentId": 2,
              "description": "研发部",
              "departmentId": 3,
              "departmentName": "研发部",
              "isActive": true,
              "children": [
                {
                  "id": 5,
                  "code": "RD-WS-A",
                  "name": "研发部工位A",
                  "type": 3,
                  "typeName": "工位",
                  "parentId": 3,
                  "description": "研发部工位A",
                  "departmentId": 3,
                  "departmentName": "研发部",
                  "isActive": true,
                  "children": []
                },
                {
                  "id": 6,
                  "code": "RD-WS-B",
                  "name": "研发部工位B",
                  "type": 3,
                  "typeName": "工位",
                  "parentId": 3,
                  "description": "研发部工位B",
                  "departmentId": 3,
                  "departmentName": "研发部",
                  "isActive": true,
                  "children": []
                }
              ]
            }
          ]
        }
        // 更多位置...
      ]
    }
  ],
  "message": "获取位置树成功",
  "error": null,
  "timestamp": "2025-03-22T12:34:56.789Z"
}
```

### 4.2 获取位置详情
**请求:**
- 方法: `GET`
- URL: `/api/locations/{id}`
- 描述: 获取指定ID的位置详细信息
- 需要认证: 是
- 所需权限: `location:view`

**路径参数:**
| 参数名 | 类型 | 必需 | 描述 |
|-------|------|-----|------|
| id | integer | 是 | 位置ID |

**成功响应:**
- 状态码: `200`
```json
{
  "success": true,
  "data": {
    "id": 5,
    "code": "RD-WS-A",
    "name": "研发部工位A",
    "type": 3,
    "typeName": "工位",
    "parentId": 3,
    "parent": {
      "id": 3,
      "name": "研发部",
      "code": "RD-DEPT"
    },
    "path": "1/2/3/5",
    "fullPath": "总部/研发中心/研发部/研发部工位A",
    "description": "研发部工位A",
    "address": "北京市海淀区中关村软件园",
    "departmentId": 3,
    "department": {
      "id": 3,
      "name": "研发部",
      "code": "RD-DEPT"
    },
    "defaultDepartmentId": 3,
    "defaultDepartment": {
      "id": 3,
      "name": "研发部",
      "code": "RD-DEPT"
    },
    "defaultResponsiblePersonId": 2,
    "defaultResponsiblePerson": {
      "id": 2,
      "name": "张三",
      "username": "zhangsan"
    },
    "isActive": true,
    "sortOrder": 1,
    "children": [],
    "users": [
      {
        "userId": 3,
        "name": "李四",
        "username": "lisi",
        "userType": 0,
        "userTypeName": "使用人"
      },
      {
        "userId": 2,
        "name": "张三",
        "username": "zhangsan",
        "userType": 1,
        "userTypeName": "负责人"
      }
    ],
    "assets": [
      {
        "id": 1,
        "assetCode": "A-2025-0001",
        "name": "ThinkPad X1 Carbon",
        "status": 1,
        "statusName": "使用中"
      }
    ],
    "createdAt": "2025-01-01T00:00:00Z",
    "updatedAt": "2025-01-01T00:00:00Z"
  },
  "message": "获取位置详情成功",
  "error": null,
  "timestamp": "2025-03-22T12:34:56.789Z"
}
```

### 4.3 创建位置
**请求:**
- 方法: `POST`
- URL: `/api/locations`
- 描述: 创建新位置
- 需要认证: 是
- 所需权限: `location:create`

**请求体:**
```json
{
  "code": "RD-WS-C",
  "name": "研发部工位C",
  "type": 3,
  "parentId": 3,
  "description": "研发部工位C",
  "address": "北京市海淀区中关村软件园",
  "departmentId": 3,
  "defaultDepartmentId": 3,
  "defaultResponsiblePersonId": 2,
  "isActive": true,
  "sortOrder": 3
}
```

**参数说明:**
| 参数名 | 类型 | 必需 | 描述 |
|-------|------|-----|------|
| code | string | 是 | 位置代码 |
| name | string | 是 | 位置名称 |
| type | integer | 是 | 位置类型（0:工厂, 1:产线, 2:工序, 3:工位, 4:位置） |
| parentId | integer | 否 | 父位置ID，顶级位置可为null |
| description | string | 否 | 描述 |
| address | string | 否 | 地址 |
| departmentId | integer | 否 | 关联部门ID |
| defaultDepartmentId | integer | 否 | 默认部门ID |
| defaultResponsiblePersonId | integer | 否 | 默认负责人ID |
| isActive | boolean | 否 | 是否激活，默认true |
| sortOrder | integer | 否 | 排序顺序，默认0 |

**成功响应:**
- 状态码: `201`
```json
{
  "success": true,
  "data": {
    "id": 7,
    "code": "RD-WS-C",
    "name": "研发部工位C",
    "type": 3,
    "typeName": "工位",
    "parentId": 3,
    "path": "1/2/3/7",
    "fullPath": "总部/研发中心/研发部/研发部工位C",
    "description": "研发部工位C",
    "address": "北京市海淀区中关村软件园",
    "departmentId": 3,
    "defaultDepartmentId": 3,
    "defaultResponsiblePersonId": 2,
    "isActive": true,
    "sortOrder": 3,
    "createdAt": "2025-03-22T12:34:56.789Z",
    "updatedAt": "2025-03-22T12:34:56.789Z"
  },
  "message": "位置创建成功",
  "error": null,
  "timestamp": "2025-03-22T12:34:56.789Z"
}
```

### 4.4 更新位置
**请求:**
- 方法: `PUT`
- URL: `/api/locations/{id}`
- 描述: 更新指定ID的位置信息
- 需要认证: 是
- 所需权限: `location:update`

**路径参数:**
| 参数名 | 类型 | 必需 | 描述 |
|-------|------|-----|------|
| id | integer | 是 | 位置ID |

**请求体:**
```json
{
  "code": "RD-WS-C",
  "name": "研发部工位C（更新）",
  "description": "研发部工位C - 已更新",
  "address": "北京市海淀区中关村软件园",
  "departmentId": 3,
  "defaultDepartmentId": 3,
  "defaultResponsiblePersonId": 4,
  "isActive": true,
  "sortOrder": 3
}
```

**参数说明:**
| 参数名 | 类型 | 必需 | 描述 |
|-------|------|-----|------|
| code | string | 是 | 位置代码 |
| name | string | 是 | 位置名称 |
| description | string | 否 | 描述 |
| address | string | 否 | 地址 |
| departmentId | integer | 否 | 关联部门ID |
| defaultDepartmentId | integer | 否 | 默认部门ID |
| defaultResponsiblePersonId | integer | 否 | 默认负责人ID |
| isActive | boolean | 否 | 是否激活 |
| sortOrder | integer | 否 | 排序顺序 |

**成功响应:**
- 状态码: `200`
```json
{
  "success": true,
  "data": {
    "id": 7,
    "code": "RD-WS-C",
    "name": "研发部工位C（更新）",
    "type": 3,
    "typeName": "工位",
    "parentId": 3,
    "path": "1/2/3/7",
    "fullPath": "总部/研发中心/研发部/研发部工位C（更新）",
    "description": "研发部工位C - 已更新",
    "address": "北京市海淀区中关村软件园",
    "departmentId": 3,
    "defaultDepartmentId": 3,
    "defaultResponsiblePersonId": 4,
    "isActive": true,
    "sortOrder": 3,
    "createdAt": "2025-03-22T12:34:56.789Z",
    "updatedAt": "2025-03-22T13:45:12.456Z"
  },
  "message": "位置更新成功",
  "error": null,
  "timestamp": "2025-03-22T13:45:12.456Z"
}
```

### 4.5 删除位置
**请求:**
- 方法: `DELETE`
- URL: `/api/locations/{id}`
- 描述: 删除指定ID的位置
- 需要认证: 是
- 所需权限: `location:delete`

**路径参数:**
| 参数名 | 类型 | 必需 | 描述 |
|-------|------|-----|------|
| id | integer | 是 | 位置ID |

**成功响应:**
- 状态码: `204`

**错误响应:**
- 状态码: `400`
```json
{
  "success": false,
  "data": null,
  "message": "无法删除位置",
  "error": {
    "code": "VALIDATION_ERROR",
    "details": "此位置包含子位置或关联资产，无法删除"
  },
  "timestamp": "2025-03-22T12:34:56.789Z"
}
```

### 4.6 位置层级同步
**请求:**
- 方法: `POST`
- URL: `/api/location-hierarchy/sync`
- 描述: 同步位置层级结构
- 需要认证: 是
- 所需权限: `location:manage`

**请求体:**
```json
{
  "hierarchyDefinition": [
    {
      "code": "HQ",
      "name": "总部",
      "type": 0,
      "children": [
        {
          "code": "RD-CENTER",
          "name": "研发中心",
          "type": 1,
          "children": [
            {
              "code": "RD-DEPT",
              "name": "研发部",
              "type": 2,
              "children": [
                {
                  "code": "RD-WS-A",
                  "name": "研发部工位A",
                  "type": 3
                },
                {
                  "code": "RD-WS-B",
                  "name": "研发部工位B",
                  "type": 3
                },
                {
                  "code": "RD-WS-C",
                  "name": "研发部工位C",
                  "type": 3
                }
              ]
            }
          ]
        }
      ]
    }
  ],
  "deleteNonExisting": false
}
```

**参数说明:**
| 参数名 | 类型 | 必需 | 描述 |
|-------|------|-----|------|
| hierarchyDefinition | array | 是 | 位置层级结构定义 |
| deleteNonExisting | boolean | 否 | 是否删除定义中不存在的位置，默认false |

**成功响应:**
- 状态码: `200`
```json
{
  "success": true,
  "data": {
    "created": 1,
    "updated": 5,
    "deleted": 0,
    "skipped": 0
  },
  "message": "位置层级同步成功",
  "error": null,
  "timestamp": "2025-03-22T12:34:56.789Z"
}
```

### 4.7 获取位置用户列表
**请求:**
- 方法: `GET`
- URL: `/api/locations/{id}/users`
- 描述: 获取指定位置的用户列表
- 需要认证: 是
- 所需权限: `location:view`

**路径参数:**
| 参数名 | 类型 | 必需 | 描述 |
|-------|------|-----|------|
| id | integer | 是 | 位置ID |

**查询参数:**
| 参数名 | 类型 | 必需 | 描述 |
|-------|------|-----|------|
| userType | integer | 否 | 用户类型筛选（0:使用人, 1:负责人） |

**成功响应:**
- 状态码: `200`
```json
{
  "success": true,
  "data": [
    {
      "userId": 3,
      "name": "李四",
      "username": "lisi",
      "userType": 0,
      "userTypeName": "使用人",
      "departmentId": 3,
      "departmentName": "研发部",
      "createdAt": "2025-01-01T00:00:00Z"
    },
    {
      "userId": 2,
      "name": "张三",
      "username": "zhangsan",
      "userType": 1,
      "userTypeName": "负责人",
      "departmentId": 3,
      "departmentName": "研发部",
      "createdAt": "2025-01-01T00:00:00Z"
    }
  ],
  "message": "获取位置用户列表成功",
  "error": null,
  "timestamp": "2025-03-22T12:34:56.789Z"
}
```

### 4.8 添加位置用户
**请求:**
- 方法: `POST`
- URL: `/api/locations/{id}/users`
- 描述: 为指定位置添加用户
- 需要认证: 是
- 所需权限: `location:manage`

**路径参数:**
| 参数名 | 类型 | 必需 | 描述 |
|-------|------|-----|------|
| id | integer | 是 | 位置ID |

**请求体:**
```json
{
  "userId": 5,
  "userType": 0
}
```

**参数说明:**
| 参数名 | 类型 | 必需 | 描述 |
|-------|------|-----|------|
| userId | integer | 是 | 用户ID |
| userType | integer | 是 | 用户类型（0:使用人, 1:负责人） |

**成功响应:**
- 状态码: `200`
```json
{
  "success": true,
  "data": {
    "userId": 5,
    "name": "王五",
    "username": "wangwu",
    "userType": 0,
    "userTypeName": "使用人",
    "departmentId": 3,
    "departmentName": "研发部",
    "createdAt": "2025-03-22T12:34:56.789Z"
  },
  "message": "添加位置用户成功",
  "error": null,
  "timestamp": "2025-03-22T12:34:56.789Z"
}
```

### 4.9 删除位置用户
**请求:**
- 方法: `DELETE`
- URL: `/api/locations/{locationId}/users/{userId}`
- 描述: 删除指定位置的用户
- 需要认证: 是

**参数说明:**
| 参数名 | 类型 | 必需 | 描述 |
|-------|------|-----|------|
| locationId | integer | 是 | 位置ID |
| userId | integer | 是 | 用户ID |

**成功响应:**
- 状态码: `204`
```json
{
  "success": true,
  "data": null,
  "message": "用户删除成功",
  "error": null,
  "timestamp": "2025-03-22T12:34:56.789Z"
}
```

## 5. 采购管理API

### 5.1 获取采购单列表
**请求:**
- 方法: `GET`
- URL: `/api/purchase-orders`
- 描述: 获取采购单列表

**参数说明:**
| 参数名 | 类型 | 必需 | 描述 |
|-------|------|-----|------|
| page | integer | 否 | 页码，从1开始（默认：1） |
| size | integer | 否 | 每页记录数（默认：20，最大：100） |
| sort | string | 否 | 排序字段（格式：`field,direction`，例如：`name,asc`） |

**成功响应:**
- 状态码: `200`
```json
{
  "success": true,
  "data": {
    "items": [ ... ],
    "pagination": {
      "page": 1,
      "size": 20,
      "totalItems": 135,
      "totalPages": 7
    }
  },
  "message": "操作成功",
  "error": null,
  "timestamp": "2025-03-22T12:34:56.789Z"
}
```

### 5.2 获取单个采购单
**请求:**
- 方法: `GET`
- URL: `/api/purchase-orders/{id}`
- 描述: 获取单个采购单

**参数说明:**
| 参数名 | 类型 | 必需 | 描述 |
|-------|------|-----|------|
| id | integer | 是 | 采购单ID |

**成功响应:**
- 状态码: `200`
```json
{
  "success": true,
  "data": { ... },
  "message": "操作成功",
  "error": null,
  "timestamp": "2025-03-22T12:34:56.789Z"
}
```

### 5.3 创建采购单
**请求:**
- 方法: `POST`
- URL: `/api/purchase-orders`
- 描述: 创建新采购单
- 需要认证: 是
- 所需权限: `purchase:create`

**请求体:**
```json
{
  "title": "笔记本电脑采购",
  "supplierId": 1,
  "purchaseDate": "2025-01-10",
  "expectedDeliveryDate": "2025-01-20",
  "description": "研发部笔记本电脑采购",
  "items": [
    {
      "name": "ThinkPad X1 Carbon",
      "assetTypeId": 1,
      "specification": "X1 Carbon Gen 10, 16GB, 512GB SSD",
      "unitPrice": 12999.99,
      "quantity": 10,
      "notes": null
    }
  ]
}
```

**参数说明:**
| 参数名 | 类型 | 必需 | 描述 |
|-------|------|-----|------|
| title | string | 是 | 采购单标题 |
| supplierId | integer | 是 | 供应商ID |
| purchaseDate | string | 是 | 采购日期（YYYY-MM-DD） |
| expectedDeliveryDate | string | 否 | 预计交付日期（YYYY-MM-DD） |
| description | string | 否 | 描述 |
| items | array | 是 | 采购项目数组 |
| items[].name | string | 是 | 项目名称 |
| items[].assetTypeId | integer | 是 | 资产类型ID |
| items[].specification | string | 否 | 规格 |
| items[].unitPrice | decimal | 是 | 单价 |
| items[].quantity | integer | 是 | 数量 |
| items[].notes | string | 否 | 备注 |

**成功响应:**
- 状态码: `201`
```json
{
  "success": true,
  "data": {
    "id": 2,
    "code": "PO-2025-0002",
    "title": "笔记本电脑采购",
    "supplierId": 1,
    "supplierName": "联想中国",
    "totalAmount": 129999.90,
    "status": 0,
    "statusName": "已创建",
    "purchaseDate": "2025-01-10",
    "expectedDeliveryDate": "2025-01-20",
    "description": "研发部笔记本电脑采购",
    "createdBy": "admin",
    "createdAt": "2025-03-22T12:34:56.789Z",
    "items": [
      {
        "id": 2,
        "purchaseOrderId": 2,
        "name": "ThinkPad X1 Carbon",
        "assetTypeId": 1,
        "assetTypeName": "笔记本电脑",
        "specification": "X1 Carbon Gen 10, 16GB, 512GB SSD",
        "unitPrice": 12999.99,
        "quantity": 10,
        "amount": 129999.90,
        "notes": null
      }
    ]
  },
  "message": "采购单创建成功",
  "error": null,
  "timestamp": "2025-03-22T12:34:56.789Z"
}
```

### 5.4 更新采购单
**请求:**
- 方法: `PUT`
- URL: `/api/purchase-orders/{id}`
- 描述: 更新指定ID的采购单信息
- 需要认证: 是
- 所需权限: `purchase:update`

**路径参数:**
| 参数名 | 类型 | 必需 | 描述 |
|-------|------|-----|------|
| id | integer | 是 | 采购单ID |

**请求体:**
```json
{
  "title": "笔记本电脑采购（更新）",
  "supplierId": 1,
  "purchaseDate": "2025-01-10",
  "expectedDeliveryDate": "2025-01-25",
  "description": "研发部笔记本电脑采购 - 已更新",
  "items": [
    {
      "id": 2,
      "name": "ThinkPad X1 Carbon",
      "assetTypeId": 1,
      "specification": "X1 Carbon Gen 10, 16GB, 1TB SSD",
      "unitPrice": 13999.99,
      "quantity": 10,
      "notes": "升级配置"
    }
  ]
}
```

**参数说明:**
| 参数名 | 类型 | 必需 | 描述 |
|-------|------|-----|------|
| title | string | 是 | 采购单标题 |
| supplierId | integer | 是 | 供应商ID |
| purchaseDate | string | 是 | 采购日期（YYYY-MM-DD） |
| expectedDeliveryDate | string | 否 | 预计交付日期（YYYY-MM-DD） |
| description | string | 否 | 描述 |
| items | array | 是 | 采购项目数组 |
| items[].id | integer | 否 | 项目ID，更新时提供 |
| items[].name | string | 是 | 项目名称 |
| items[].assetTypeId | integer | 是 | 资产类型ID |
| items[].specification | string | 否 | 规格 |
| items[].unitPrice | decimal | 是 | 单价 |
| items[].quantity | integer | 是 | 数量 |
| items[].notes | string | 否 | 备注 |

**成功响应:**
- 状态码: `200`
```json
{
  "success": true,
  "data": {
    "id": 2,
    "code": "PO-2025-0002",
    "title": "笔记本电脑采购（更新）",
    "supplierId": 1,
    "supplierName": "联想中国",
    "totalAmount": 139999.90,
    "status": 0,
    "statusName": "已创建",
    "purchaseDate": "2025-01-10",
    "expectedDeliveryDate": "2025-01-25",
    "description": "研发部笔记本电脑采购 - 已更新",
    "updatedAt": "2025-03-22T13:45:12.456Z",
    "items": [
      {
        "id": 2,
        "purchaseOrderId": 2,
        "name": "ThinkPad X1 Carbon",
        "assetTypeId": 1,
        "assetTypeName": "笔记本电脑",
        "specification": "X1 Carbon Gen 10, 16GB, 1TB SSD",
        "unitPrice": 13999.99,
        "quantity": 10,
        "amount": 139999.90,
        "notes": "升级配置"
      }
    ]
  },
  "message": "采购单更新成功",
  "error": null,
  "timestamp": "2025-03-22T13:45:12.456Z"
}
```

### 5.5 变更采购单状态
**请求:**
- 方法: `POST`
- URL: `/api/purchase-orders/{id}/change-status`
- 描述: 变更采购单状态
- 需要认证: 是
- 所需权限: `purchase:update`

**路径参数:**
| 参数名 | 类型 | 必需 | 描述 |
|-------|------|-----|------|
| id | integer | 是 | 采购单ID |

**请求体:**
```json
{
  "status": 1,
  "notes": "采购单已下单"
}
```

**参数说明:**
| 参数名 | 类型 | 必需 | 描述 |
|-------|------|-----|------|
| status | integer | 是 | 新状态（0:已创建, 1:已下单, 2:已到货, 3:已完成, 4:已取消） |
| notes | string | 否 | 备注说明 |

**成功响应:**
- 状态码: `200`
```json
{
  "success": true,
  "data": {
    "id": 2,
    "code": "PO-2025-0002",
    "status": 1,
    "statusName": "已下单",
    "updatedAt": "2025-03-22T14:15:30.123Z"
  },
  "message": "采购单状态变更成功",
  "error": null,
  "timestamp": "2025-03-22T14:15:30.123Z"
}
```

### 5.6 资产接收
**请求:**
- 方法: `POST`
- URL: `/api/purchase-orders/{id}/receive`
- 描述: 接收采购单中的资产
- 需要认证: 是
- 所需权限: `purchase:manage`

**路径参数:**
| 参数名 | 类型 | 必需 | 描述 |
|-------|------|-----|------|
| id | integer | 是 | 采购单ID |

**请求体:**
```json
{
  "receiveDate": "2025-01-25",
  "notes": "正常到货",
  "items": [
    {
      "purchaseItemId": 2,
      "receivedQuantity": 10,
      "serialNumbers": [
        "XCG12345671",
        "XCG12345672",
        "XCG12345673",
        "XCG12345674",
        "XCG12345675",
        "XCG12345676",
        "XCG12345677",
        "XCG12345678",
        "XCG12345679",
        "XCG12345680"
      ],
      "assetStatus": 0,
      "locationId": 5
    }
  ]
}
```

**参数说明:**
| 参数名 | 类型 | 必需 | 描述 |
|-------|------|-----|------|
| receiveDate | string | 是 | 接收日期（YYYY-MM-DD） |
| notes | string | 否 | 备注 |
| items | array | 是 | 接收项目数组 |
| items[].purchaseItemId | integer | 是 | 采购项目ID |
| items[].receivedQuantity | integer | 是 | 接收数量 |
| items[].serialNumbers | array | 是 | 序列号数组，数量必须与receivedQuantity一致 |
| items[].assetStatus | integer | 是 | 资产状态（0:正常, 1:维修中, 2:已报废, 3:已借出） |
| items[].locationId | integer | 是 | 存放位置ID |

**成功响应:**
- 状态码: `200`
```json
{
  "success": true,
  "data": {
    "id": 3,
    "purchaseOrderId": 2,
    "items": [ ... ],
    "pagination": {
      "page": 1,
      "size": 20,
      "totalItems": 135,
      "totalPages": 7
    }
  },
  "message": "操作成功",
  "error": null,
  "timestamp": "2025-03-22T12:34:56.789Z"
}
```

### 7.2 获取单个任务
**请求:**
- 方法: `GET`
- URL: `/api/v1/tasks/{id}`
- 描述: 获取单个任务

**参数说明:**
| 参数名 | 类型 | 必需 | 描述 |
|-------|------|-----|------|
| id | integer | 是 | 任务ID |

**成功响应:**
- 状态码: `200`
```json
{
  "success": true,
  "data": { ... },
  "message": "操作成功",
  "error": null,
  "timestamp": "2025-03-22T12:34:56.789Z"
}
```

### 7.3 创建任务
**请求:**
- 方法: `POST`
- URL: `/api/v1/tasks`
- 描述: 创建任务

**请求体:**
```json
{
  "name": "任务名称",
  "description": "任务描述",
  "dueDate": "2025-03-22",
  "status": "未开始"
}
```

**参数说明:**
| 参数名 | 类型 | 必需 | 描述 |
|-------|------|-----|------|
| name | string | 是 | 任务名称 |
| description | string | 否 | 任务描述 |
| dueDate | string | 是 | 截止日期 |
| status | string | 是 | 任务状态 |

**成功响应:**
- 状态码: `201`
```json
{
  "success": true,
  "data": {
    "id": 1,
    "name": "任务名称",
    "description": "任务描述",
    "dueDate": "2025-03-22",
    "status": "未开始"
  },
  "message": "任务创建成功",
  "error": null,
  "timestamp": "2025-03-22T12:34:56.789Z"
}
```

### 7.4 更新任务
**请求:**
- 方法: `PUT`
- URL: `/api/v1/tasks/{id}`
- 描述: 更新任务

**请求体:**
```json
{
  "name": "新任务名称",
  "description": "新任务描述",
  "dueDate": "2025-03-22",
  "status": "进行中"
}
```

**参数说明:**
| 参数名 | 类型 | 必需 | 描述 |
|-------|------|-----|------|
| id | integer | 是 | 任务ID |
| name | string | 是 | 任务名称 |
| description | string | 否 | 任务描述 |
| dueDate | string | 是 | 截止日期 |
| status | string | 是 | 任务状态 |

**成功响应:**
- 状态码: `200`
```json
{
  "success": true,
  "data": {
    "id": 1,
    "name": "新任务名称",
    "description": "新任务描述",
    "dueDate": "2025-03-22",
    "status": "进行中"
  },
  "message": "任务更新成功",
  "error": null,
  "timestamp": "2025-03-22T12:34:56.789Z"
}
```

### 7.5 删除任务
**请求:**
- 方法: `DELETE`
- URL: `/api/v1/tasks/{id}`
- 描述: 删除任务

**参数说明:**
| 参数名 | 类型 | 必需 | 描述 |
|-------|------|-----|------|
| id | integer | 是 | 任务ID |

**成功响应:**
- 状态码: `204`
```json
{
  "success": true,
  "data": null,
  "message": "任务删除成功",
  "error": null,
  "timestamp": "2025-03-22T12:34:56.789Z"
}
```

### 7.5 更改维修订单状态
**请求:**
- 方法: `POST`
- URL: `/api/maintenance-orders/{id}/change-status`
- 描述: 更改维修订单状态
- 需要认证: 是
- 所需权限: `maintenance:update`

**路径参数:**
| 参数名 | 类型 | 必需 | 描述 |
|-------|------|-----|------|
| id | integer | 是 | 维修订单ID |

**请求体:**
```json
{
  "status": 2,
  "notes": "维修完成，已更换硬件"
}
```

**参数说明:**
| 参数名 | 类型 | 必需 | 描述 |
|-------|------|-----|------|
| status | integer | 是 | 新状态（0:待维修, 1:维修中, 2:已完成） |
| notes | string | 否 | 状态变更说明 |

**成功响应:**
- 状态码: `200`
```json
{
  "success": true,
  "data": {
    "id": 2,
    "code": "MO-2025-0002",
    "status": 2,
    "statusName": "已完成",
    "updatedAt": "2025-02-22T16:12:34.567Z"
  },
  "message": "维修订单状态更新成功",
  "error": null,
  "timestamp": "2025-02-22T16:12:34.567Z"
}
```

## 8. 周期性任务API

### 8.1 获取周期性任务列表
**请求:**
- 方法: `GET`
- URL: `/api/periodic-tasks`
- 描述: 获取周期性任务列表
- 需要认证: 是
- 所需权限: `task:read`

**参数说明:**
| 参数名 | 类型 | 必需 | 描述 |
|-------|------|-----|------|
| page | integer | 否 | 页码，从1开始（默认：1） |
| size | integer | 否 | 每页记录数（默认：20，最大：100） |
| status | integer | 否 | 状态筛选（0:未开始, 1:执行中, 2:已暂停, 3:已完成） |

**成功响应:**
- 状态码: `200`
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": 1,
        "title": "服务器月度巡检",
        "description": "对所有服务器进行月度例行巡检",
        "type": 1,
        "typeName": "资产巡检",
        "cycle": "monthly",
        "startDate": "2025-01-01",
        "endDate": "2025-12-31",
        "nextExecutionDate": "2025-04-01",
        "status": 1,
        "statusName": "执行中",
        "locationId": 3,
        "locationName": "数据中心A区",
        "departmentId": 2,
        "departmentName": "IT运维部",
        "assigneeId": 5,
        "assigneeName": "张三",
        "createdAt": "2024-12-25T10:12:34.567Z"
      },
      {
        "id": 2,
        "title": "网络设备季度检查",
        "description": "对所有网络设备进行季度检查和固件更新",
        "type": 1,
        "typeName": "资产巡检",
        "cycle": "quarterly",
        "startDate": "2025-01-01",
        "endDate": "2025-12-31",
        "nextExecutionDate": "2025-04-01",
        "status": 1,
        "statusName": "执行中",
        "locationId": 3,
        "locationName": "数据中心A区",
        "departmentId": 2,
        "departmentName": "IT运维部",
        "assigneeId": 6,
        "assigneeName": "李四",
        "createdAt": "2024-12-25T11:23:45.678Z"
      }
    ],
    "pagination": {
      "page": 1,
      "size": 20,
      "totalItems": 2,
      "totalPages": 1
    }
  },
  "message": "获取周期性任务列表成功",
  "error": null,
  "timestamp": "2025-03-22T12:34:56.789Z"
}
```

### 8.2 获取单个周期性任务
**请求:**
- 方法: `GET`
- URL: `/api/periodic-tasks/{id}`
- 描述: 获取单个周期性任务详情
- 需要认证: 是
- 所需权限: `task:read`

**路径参数:**
| 参数名 | 类型 | 必需 | 描述 |
|-------|------|-----|------|
| id | integer | 是 | 周期性任务ID |

**成功响应:**
- 状态码: `200`
```json
{
  "success": true,
  "data": {
    "id": 1,
    "title": "服务器月度巡检",
    "description": "对所有服务器进行月度例行巡检",
    "type": 1,
    "typeName": "资产巡检",
    "cycle": "monthly",
    "cycleConfig": {
      "dayOfMonth": 1
    },
    "startDate": "2025-01-01",
    "endDate": "2025-12-31",
    "lastExecutionDate": "2025-03-01",
    "nextExecutionDate": "2025-04-01",
    "status": 1,
    "statusName": "执行中",
    "locationId": 3,
    "locationName": "数据中心A区",
    "departmentId": 2,
    "departmentName": "IT运维部",
    "assigneeId": 5,
    "assigneeName": "张三",
    "checklist": [
      {
        "id": 1,
        "content": "检查服务器运行状态",
        "required": true
      },
      {
        "id": 2,
        "content": "检查服务器温度",
        "required": true
      },
      {
        "id": 3,
        "content": "检查硬盘状态",
        "required": true
      },
      {
        "id": 4,
        "content": "清理服务器内部灰尘",
        "required": false
      }
    ],
    "history": [
      {
        "id": 1,
        "executionDate": "2025-01-01",
        "status": 2,
        "statusName": "已完成",
        "executor": "张三",
        "notes": "所有服务器状态正常"
      },
      {
        "id": 2,
        "executionDate": "2025-02-01",
        "status": 2,
        "statusName": "已完成",
        "executor": "张三",
        "notes": "服务器2硬盘有预警，已更换"
      },
      {
        "id": 3,
        "executionDate": "2025-03-01",
        "status": 2,
        "statusName": "已完成",
        "executor": "张三",
        "notes": "所有服务器状态正常"
      }
    ],
    "createdAt": "2024-12-25T10:12:34.567Z",
    "updatedAt": "2025-03-01T15:23:45.678Z"
  },
  "message": "获取周期性任务详情成功",
  "error": null,
  "timestamp": "2025-03-22T12:34:56.789Z"
}
```

### 8.3 创建周期性任务
**请求:**
- 方法: `POST`
- URL: `/api/periodic-tasks`
- 描述: 创建新周期性任务
- 需要认证: 是
- 所需权限: `task:create`

**请求体:**
```json
{
  "title": "工作站季度检查",
  "description": "对研发部工作站进行季度检查",
  "type": 1,
  "cycle": "quarterly",
  "cycleConfig": {
    "startMonth": 1,
    "monthInterval": 3
  },
  "startDate": "2025-04-01",
  "endDate": "2025-12-31",
  "locationId": 6,
  "departmentId": 3,
  "assigneeId": 7,
  "checklist": [
    {
      "content": "检查工作站运行状态",
      "required": true
    },
    {
      "content": "检查内存使用情况",
      "required": true
    },
    {
      "content": "清理硬盘空间",
      "required": true
    },
    {
      "content": "更新系统补丁",
      "required": true
    }
  ]
}
```

**参数说明:**
| 参数名 | 类型 | 必需 | 描述 |
|-------|------|-----|------|
| title | string | 是 | 任务标题 |
| description | string | 否 | 任务描述 |
| type | integer | 是 | 任务类型（1:资产巡检, 2:维护保养, 3:数据备份, 4:其他） |
| cycle | string | 是 | 周期类型（daily、weekly、monthly、quarterly、yearly） |
| cycleConfig | object | 是 | 周期配置（根据cycle类型不同而不同） |
| startDate | string | 是 | 开始日期（YYYY-MM-DD） |
| endDate | string | 否 | 结束日期（YYYY-MM-DD） |
| locationId | integer | 否 | 位置ID |
| departmentId | integer | 否 | 部门ID |
| assigneeId | integer | 是 | 负责人ID |
| checklist | array | 否 | 检查项数组 |
| checklist[].content | string | 是 | 检查项内容 |
| checklist[].required | boolean | 是 | 是否必须 |

**成功响应:**
- 状态码: `201`
```json
{
  "success": true,
  "data": {
    "id": 3,
    "title": "工作站季度检查",
    "description": "对研发部工作站进行季度检查",
    "type": 1,
    "typeName": "资产巡检",
    "cycle": "quarterly",
    "startDate": "2025-04-01",
    "endDate": "2025-12-31",
    "nextExecutionDate": "2025-04-01",
    "status": 0,
    "statusName": "未开始",
    "createdBy": "admin",
    "createdAt": "2025-03-22T12:34:56.789Z"
  },
  "message": "周期性任务创建成功",
  "error": null,
  "timestamp": "2025-03-22T12:34:56.789Z"
}
```

### 8.4 更新周期性任务
**请求:**
- 方法: `PUT`
- URL: `/api/periodic-tasks/{id}`
- 描述: 更新周期性任务信息
- 需要认证: 是
- 所需权限: `task:update`

**路径参数:**
| 参数名 | 类型 | 必需 | 描述 |
|-------|------|-----|------|
| id | integer | 是 | 周期性任务ID |

**请求体:**
```json
{
  "title": "工作站季度检查（更新）",
  "description": "对研发部所有工作站进行季度检查",
  "cycle": "quarterly",
  "cycleConfig": {
    "startMonth": 1,
    "monthInterval": 3
  },
  "startDate": "2025-04-01",
  "endDate": "2026-12-31",
  "assigneeId": 8,
  "checklist": [
    {
      "id": 5,
      "content": "检查工作站运行状态",
      "required": true
    },
    {
      "id": 6,
      "content": "检查内存使用情况",
      "required": true
    },
    {
      "id": 7,
      "content": "清理硬盘空间",
      "required": true
    },
    {
      "id": 8,
      "content": "更新系统补丁",
      "required": true
    },
    {
      "content": "检查显卡驱动",
      "required": false
    }
  ]
}
```

**成功响应:**
- 状态码: `200`
```json
{
  "success": true,
  "data": {
    "id": 3,
    "title": "工作站季度检查（更新）",
    "updatedAt": "2025-03-23T10:12:34.567Z"
  },
  "message": "周期性任务更新成功",
  "error": null,
  "timestamp": "2025-03-23T10:12:34.567Z"
}
```

### 8.5 改变周期性任务状态
**请求:**
- 方法: `POST`
- URL: `/api/periodic-tasks/{id}/change-status`
- 描述: 改变周期性任务状态
- 需要认证: 是
- 所需权限: `task:update`

**路径参数:**
| 参数名 | 类型 | 必需 | 描述 |
|-------|------|-----|------|
| id | integer | 是 | 周期性任务ID |

**请求体:**
```json
{
  "status": 1,
  "notes": "开始执行周期性任务"
}
```

**参数说明:**
| 参数名 | 类型 | 必需 | 描述 |
|-------|------|-----|------|
| status | integer | 是 | 新状态（0:未开始, 1:执行中, 2:已暂停, 3:已完成） |
| notes | string | 否 | 状态变更说明 |

**成功响应:**
- 状态码: `200`
```json
{
  "success": true,
  "data": {
    "id": 3,
    "status": 1,
    "statusName": "执行中",
    "updatedAt": "2025-03-23T10:23:45.678Z"
  },
  "message": "周期性任务状态更新成功",
  "error": null,
  "timestamp": "2025-03-23T10:23:45.678Z"
}
```

### 8.6 记录周期性任务执行
**请求:**
- 方法: `POST`
- URL: `/api/periodic-tasks/{id}/executions`
- 描述: 记录周期性任务执行结果
- 需要认证: 是
- 所需权限: `task:update`

**路径参数:**
| 参数名 | 类型 | 必需 | 描述 |
|-------|------|-----|------|
| id | integer | 是 | 周期性任务ID |

**请求体:**
```json
{
  "executionDate": "2025-04-01",
  "status": 2,
  "notes": "所有工作站检查完成，3号工作站需要增加内存",
  "checkResults": [
    {
  "success": true,
  "data": null,
  "message": "备份恢复成功",
  "error": null,
  "timestamp": "2025-03-22T12:34:56.789Z"
}
```

## 9. PDCA改进计划API

### 9.1 获取PDCA改进计划列表
**请求:**
- 方法: `GET`
- URL: `/api/pdca-plans`
- 描述: 获取PDCA改进计划列表
- 需要认证: 是
- 所需权限: `pdca:read`

**参数说明:**
| 参数名 | 类型 | 必需 | 描述 |
|-------|------|-----|------|
| page | integer | 否 | 页码，从1开始（默认：1） |
| size | integer | 否 | 每页记录数（默认：20，最大：100） |
| status | integer | 否 | 状态筛选（0:计划, 1:执行, 2:检查, 3:处理, 4:完成） |
| departmentId | integer | 否 | 部门ID筛选 |

**成功响应:**
- 状态码: `200`
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": 1,
        "title": "提高服务器资源利用率",
        "description": "通过优化配置提高现有服务器资源利用率",
        "startDate": "2025-02-01",
        "endDate": "2025-05-31",
        "status": 1,
        "statusName": "执行",
        "progress": 45,
        "stage": "D",
        "departmentId": 2,
        "departmentName": "IT运维部",
        "responsiblePerson": "张三",
        "createdAt": "2025-01-15T10:12:34.567Z"
      },
      {
        "id": 2,
        "title": "降低网络故障率",
        "description": "通过预防性维护降低网络故障发生率",
        "startDate": "2025-03-01",
        "endDate": "2025-06-30",
        "status": 0,
        "statusName": "计划",
        "progress": 20,
        "stage": "P",
        "departmentId": 2,
        "departmentName": "IT运维部",
        "responsiblePerson": "李四",
        "createdAt": "2025-02-15T11:23:45.678Z"
      }
    ],
    "pagination": {
      "page": 1,
      "size": 20,
      "totalItems": 2,
      "totalPages": 1
    }
  },
  "message": "获取PDCA改进计划列表成功",
  "error": null,
  "timestamp": "2025-03-22T12:34:56.789Z"
}
## 12. 核心业务流程示例

### 12.1 资产采购流程
1. 用户登录系统
2. 创建资产采购任务
3. 选择供应商并提交采购申请
4. 审核采购申请
5. 签订采购合同
6. 接收资产并进行验收
7. 完成资产采购流程

### 12.2 资产维护流程
1. 用户登录系统
2. 创建资产维护任务
3. 选择维护方式并提交维护申请
4. 审核维护申请
5. 执行维护任务
6. 完成资产维护流程

### 12.3 资产报废流程
1. 用户登录系统
2. 创建资产报废申请
3. 审核报废申请
4. 执行报废流程
5. 完成资产报废流程 