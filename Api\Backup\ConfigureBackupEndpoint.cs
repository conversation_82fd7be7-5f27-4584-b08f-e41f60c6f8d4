// IT资产管理系统 - 配置自动备份API
// 文件路径: /Api/Backup/ConfigureBackupEndpoint.cs
// 功能: 配置自动备份的API端点

using System;
using System.Threading.Tasks;
using ItAssetsSystem.Core.Backup;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace ItAssetsSystem.Api.Backup
{
    /// <summary>
    /// 备份配置请求模型
    /// </summary>
    public class BackupConfigurationRequest
    {
        /// <summary>
        /// 是否启用自动备份
        /// </summary>
        public bool Enabled { get; set; }
        
        /// <summary>
        /// 备份频率（小时）
        /// </summary>
        public int Frequency { get; set; }
        
        /// <summary>
        /// 保留的最大备份数量
        /// </summary>
        public int MaxBackups { get; set; }
    }
    
    [Route("api/backup/configure")]
    [ApiController]
    public class ConfigureBackupEndpoint : ControllerBase
    {
        private readonly IBackupService _backupService;
        private readonly ILogger<ConfigureBackupEndpoint> _logger;
        
        public ConfigureBackupEndpoint(
            IBackupService backupService,
            ILogger<ConfigureBackupEndpoint> logger)
        {
            _backupService = backupService;
            _logger = logger;
        }
        
        [HttpGet]
        public async Task<IActionResult> GetBackupConfiguration()
        {
            try
            {
                _logger.LogInformation("获取备份配置");
                
                // 获取当前备份设置
                var settings = await _backupService.GetBackupSettingsAsync();
                
                // 返回备份配置
                return Ok(new
                {
                    Enabled = settings.EnableAutoBackup,
                    Frequency = settings.AutoBackupFrequencyHours,
                    MaxBackups = settings.MaxBackupCount,
                    LastAutoBackup = settings.LastAutoBackupTime,
                    LastManualBackup = settings.LastManualBackupTime
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取备份配置时发生错误");
                return StatusCode(500, "获取备份配置时发生错误");
            }
        }
        
        [HttpPost]
        public async Task<IActionResult> ConfigureBackup([FromBody] BackupConfigurationRequest request)
        {
            try
            {
                _logger.LogInformation("正在配置自动备份: Enabled={Enabled}, Frequency={Frequency}, MaxBackups={MaxBackups}", 
                    request.Enabled, request.Frequency, request.MaxBackups);
                
                // 配置备份设置
                var settings = await _backupService.GetBackupSettingsAsync();
                settings.EnableAutoBackup = request.Enabled;
                settings.AutoBackupFrequencyHours = request.Frequency;
                settings.MaxBackupCount = request.MaxBackups;
                
                // 保存更新后的设置
                await _backupService.UpdateBackupSettingsAsync(settings);
                
                return Ok(new
                {
                    Success = true,
                    Message = "备份配置已更新"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "配置备份时发生错误");
                return StatusCode(500, "配置备份时发生错误");
            }
        }
    }
}

// 计划行数: 25
// 实际行数: 25 